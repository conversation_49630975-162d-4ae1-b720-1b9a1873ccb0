package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.service.core.repository.BaseEntityRepository;
import com.ruckus.cloud.wifi.servicemodel.projection.ApModelFirmwaresProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.ApModelFirmwareLabelProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.ApModelVersionProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.TenantAvailableApFirmwareQueryProjection;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TenantAvailableApFirmwareRepository extends BaseEntityRepository<TenantAvailableApFirmware> {
  List<TenantAvailableApFirmware> findByTenant(Tenant tenant);

  List<TenantAvailableApFirmware> findByTenantAndApVersion(Tenant tenant, ApVersion apVersion);

  @Query(
      value = "SELECT id, name, releaseDate, onboardDate, category FROM ((SELECT taaf.ap_version AS id, taaf.ap_version AS name, MAX(taaf.updated_date) AS releaseDate, COALESCE(to_char(CAST(av.created_date AS timestamp), 'YYYY-MM-DDThh:MI:SS.MS+0000'), '') AS onboardDate, av.category AS category FROM tenant_available_ap_firmware taaf LEFT JOIN ap_version av ON taaf.ap_version = av.id WHERE taaf.tenant = :tenantId AND av.category IN ('RECOMMENDED', 'CRITICAL') GROUP BY taaf.ap_version, av.created_date, av.category ORDER BY releaseDate DESC LIMIT :max) UNION ALL (SELECT taaf.ap_version AS id, taaf.ap_version AS name, MAX(taaf.updated_date) AS releaseDate, COALESCE(to_char(CAST(av.created_date AS timestamp), 'YYYY-MM-DDThh:MI:SS.MS+0000'), '') AS onboardDate, av.category AS category FROM tenant_available_ap_firmware taaf LEFT JOIN ap_version av ON taaf.ap_version = av.id WHERE taaf.tenant = :tenantId AND av.category = 'BETA' GROUP BY taaf.ap_version, av.created_date, av.category ORDER BY releaseDate DESC LIMIT 1)) AS temp ORDER BY releaseDate DESC",
      nativeQuery = true
  )
  List<TenantAvailableApFirmwareQueryProjection> findMultipleReleaseByTenantId(@Param("tenantId") String tenantId, @Param("max") int max);

  @Query(
      value = "SELECT taaf.ap_version AS id, taaf.ap_version AS name, MAX(taaf.updated_date) AS releaseDate, COALESCE(to_char(CAST(av.created_date AS timestamp), 'YYYY-MM-DDThh:MI:SS.MS+0000'), '') AS onboardDate, av.category AS category FROM tenant_available_ap_firmware taaf LEFT JOIN ap_version av ON taaf.ap_version = av.id WHERE taaf.tenant = :tenantId AND av.category IN ('RECOMMENDED', 'CRITICAL') GROUP BY taaf.ap_version, av.created_date, av.category ORDER BY releaseDate DESC",
      nativeQuery = true
  )
  List<TenantAvailableApFirmwareQueryProjection> findRecommendedFirmwaresByTenantId(@Param("tenantId") String tenantId);

  @Query(
    nativeQuery = true,
    value = """
    SELECT
      taaf.ap_version AS id,
      taaf.ap_version AS name,
      MAX(taaf.updated_date) AS releaseDate,
      COALESCE(to_char(CAST (av.created_date AS timestamp), 'YYYY-MM-DDThh:MI:SS.MS+0000'), '') AS onboardDate,
      av.category AS category,
      av.supported_ap_models AS supportedApModels,
      av.labels AS labels
    FROM tenant_available_ap_firmware taaf
    LEFT JOIN ap_version av ON taaf.ap_version = av.id
    WHERE taaf.tenant = :tenantId AND av.category IN ('RECOMMENDED')
    GROUP BY taaf.ap_version, av.created_date, av.category, av.supported_ap_models, av.labels
    ORDER BY CAST(regexp_split_to_array(taaf.ap_version, '\\.') AS int[]) DESC
    """)
  List<ApModelFirmwareLabelProjection> findApModelFirmwaresByTenantIdV2(@Param("tenantId") String tenantId);

  @Deprecated(since = "Use findApModelFirmwaresByTenantIdV2 instead")
  @Query(
      nativeQuery = true,
      value =
          """
      SELECT
          taaf.ap_version AS id,
          taaf.ap_version AS name,
          MAX(taaf.updated_date) AS releaseDate,
          COALESCE(to_char(CAST (av.created_date AS timestamp), 'YYYY-MM-DDThh:MI:SS.MS+0000'), '') AS onboardDate,
          av.category AS category,
          av.supported_ap_models AS supportedApModels,
          CAST(REGEXP_REPLACE(taaf.ap_version, '\\.\\d+\\.\\d+\\.\\d+\\.\\d+$', '') AS INT) AS v1,
          CAST(REGEXP_REPLACE(REGEXP_REPLACE(taaf.ap_version, '\\.\\d+\\.\\d+\\.\\d+$', ''), '^\\d+\\.', '') AS INT) AS v2,
          CAST(REGEXP_REPLACE(REGEXP_REPLACE(taaf.ap_version, '\\.\\d+\\.\\d+$', ''), '^\\d+\\.\\d+\\.', '') AS INT) AS v3,
          CAST(REGEXP_REPLACE(REGEXP_REPLACE(taaf.ap_version, '\\.\\d+$', ''), '^\\d+\\.\\d+\\.\\d+\\.', '') AS INT) AS v4,
          CAST(REGEXP_REPLACE(taaf.ap_version, '^\\d+\\.\\d+\\.\\d+\\.\\d+\\.', '') AS INT) AS v5
      FROM tenant_available_ap_firmware taaf
      LEFT JOIN ap_version av ON taaf.ap_version = av.id
      WHERE taaf.tenant = :tenantId AND av.category IN ('RECOMMENDED', 'CRITICAL', 'BETA')
      GROUP BY taaf.ap_version, av.created_date, av.category, av.supported_ap_models
      ORDER BY v1 DESC, v2 DESC, v3 DESC, v4 DESC, v5 DESC
      """)
  List<ApModelFirmwaresProjection> findApModelFirmwaresByTenantId(
      @Param("tenantId") String tenantId);

  Optional<TenantAvailableApFirmware> findByTenantIdAndApVersionId(String tenantId, String apVersionId);

  @Query(
      nativeQuery = true,
      value = """
      SELECT av.id FROM ap_version av WHERE
        category IN ('RECOMMENDED', 'CRITICAL')
        AND id IN (SELECT ap_version FROM public.tenant_available_ap_firmware WHERE tenant = :tenantId)
        AND av.supported_ap_models LIKE CONCAT(CONCAT('%"', :supportedApModel), '"%')
        ORDER BY CAST(regexp_split_to_array(id, '\\.') AS int[]) DESC LIMIT 1;
      """
  )
  Optional<String> findModelLatestSupportedRecommendedVersionByTenantId(String tenantId, String supportedApModel);

  @Query(
      nativeQuery = true,
      value = """
      SELECT av.id
      FROM ap_version av
      WHERE category IN ('RECOMMENDED', 'CRITICAL')
      AND id IN (SELECT ap_version FROM public.tenant_available_ap_firmware WHERE tenant = :tenantId)
      ORDER BY CAST(regexp_split_to_array(id, '\\.') AS int[]) DESC LIMIT 1;
      """
  )
  Optional<String> findBiggestVersionByTenantId(String tenantId);

  @Query(
      nativeQuery = true,
      value = """
      SELECT DISTINCT ON (apModel) apModel, apVersion FROM (
        SELECT DISTINCT ON (supported_ap_models)
      	  id AS apVersion,
      	  TRIM(CAST(JSON_ARRAY_ELEMENTS(CAST(supported_ap_models AS JSON)) AS TEXT), '\"') AS apModel
        FROM
          ap_version
        WHERE
          ap_version.supported_ap_models IS NOT NULL
          AND category IN ('RECOMMENDED', 'CRITICAL')
          AND id IN (SELECT taaf.ap_version FROM tenant_available_ap_firmware taaf WHERE tenant = :tenantId)
        ORDER BY supported_ap_models DESC, CAST(regexp_split_to_array(id, '\\.') AS int[]) DESC) AS model_latest_supported_version_mapping
      ORDER BY apModel DESC, CAST(regexp_split_to_array(apVersion, '\\.') AS int[]) DESC;
      """
  )
  List<ApModelVersionProjection> getModelLatestSupportedRecommendedVersionByTenantId(String tenantId);

  boolean existsByTenantId(String tenantId);

  boolean existsByTenantIdAndApVersionId(String tenantId, String apVersionId);

  boolean existsByTenantIdAndApVersionIdStartingWith(String tenantId, String idPrefix);

  @Modifying(
      clearAutomatically = true,
      flushAutomatically = true
  )
  void deleteByTenantId(String tenantId);

  @Modifying(
      clearAutomatically = true,
      flushAutomatically = true
  )
  int deleteByApVersionId(String version);

  void deleteByTenantIdAndApVersionIdStartingWith(String tenantId, String idPrefix);
}
