package com.ruckus.cloud.wifi.service.impl;

import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;

import com.ruckus.cloud.wifi.aop.AllowTemplateOperationOnly;
import com.ruckus.cloud.wifi.core.utils.IdGenerator;
import com.ruckus.cloud.wifi.eda.service.InternalRecTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.service.NetworkTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.TemplateInstanceCreateResponse;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.ExtendedTemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.WifiNetworkCreateInstanceOperation;
import com.ruckus.cloud.wifi.handler.header.ConfigRequestHeader;
import com.ruckus.cloud.wifi.kafka.KafkaHeaderUtil;

import java.util.Optional;

import com.ruckus.cloud.wifi.utils.TemplateUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@AllArgsConstructor
public class InternalRecTemplateServiceCtrlImpl implements InternalRecTemplateServiceCtrl {

  private final InternalTemplateServiceCtrlImpl internalTemplateServiceCtrl;

  @Transactional
  @Override
  public TemplateInstanceCreateResponse addWifiNetworkByRecTemplate(String recTemplateId,
      String targetRecTenantId, TemplateInstanceCreateRequest request) throws Exception {
    log.debug("addWifiNetworkByRecTemplate, recTemplateId: {}, targetRecTenantId: {}", recTemplateId, targetRecTenantId);
    internalTemplateServiceCtrl.addWifiNetworkByTemplate(recTemplateId, targetRecTenantId, request);
    //NOTE: WifiNetworkTemplateInstancePostCreateBuilder will handle relation operations
    return null;
  }
}
