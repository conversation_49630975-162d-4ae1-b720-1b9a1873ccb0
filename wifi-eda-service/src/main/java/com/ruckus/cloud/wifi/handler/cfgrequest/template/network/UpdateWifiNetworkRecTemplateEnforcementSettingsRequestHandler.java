package com.ruckus.cloud.wifi.handler.cfgrequest.template.network;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ruckus.cloud.wifi.aop.AllowTemplateOperationOnly;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.WifiNetworkTemplateRestCtrl.WifiNetworkTemplateMapper;
import com.ruckus.cloud.wifi.eda.service.WifiNetworkTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.TemplateEnforcementSettings;
import com.ruckus.cloud.wifi.handler.cfgrequest.NoDdccmConfigRequestHandler;
import com.ruckus.cloud.wifi.handler.header.ConfigRequestHeader;
import com.ruckus.cloud.wifi.kafka.KafkaHeaderUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UpdateWifiNetworkRecTemplateEnforcementSettingsRequestHandler extends
    UpdateWifiNetworkTemplateEnforcementSettingsRequestHandler {

  @Autowired
  private WifiNetworkTemplateServiceCtrl wifiNetworkTemplateServiceCtrl;

  public UpdateWifiNetworkRecTemplateEnforcementSettingsRequestHandler(WifiNetworkTemplateServiceCtrl wifiNetworkTemplateServiceCtrl) {
    super(wifiNetworkTemplateServiceCtrl);
  }

  @Override
  public String apiAction() {
    return CfgAction.UPDATE_WIFI_NETWORK_REC_TEMPLATE_ENFORCEMENT_SETTINGS.key();
  }

  @Override
  public String stepId() {
    return ApiFlowNames.UPDATE_WIFI_NETWORK_REC_TEMPLATE_ENFORCEMENT_SETTINGS + "InWifi";
  }
}
