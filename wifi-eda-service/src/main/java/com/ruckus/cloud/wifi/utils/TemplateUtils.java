package com.ruckus.cloud.wifi.utils;

import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_FLOW;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_ID;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_VERSION;

import com.google.common.base.Preconditions;
import com.google.common.base.Supplier;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractTenantAwareTemplateBaseEntity;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.TemplateHandler;
import com.ruckus.cloud.wifi.handler.header.ConfigRequestHeader;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.exception.TemplateEntityValidationException;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import lombok.SneakyThrows;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.data.util.Pair;

public class TemplateUtils {

  private TemplateUtils() {
    // For Sonar squid:S1118
    throw new IllegalStateException("Utility class");
  }

  public static final String KEY_IS_TEMPLATE =  "isTemplate";
  public static final String KEY_TEMPLATE_CONTEXT_STR =  "templateContextStr";
  public static final String KEY_INSTANCE_TEMPLATE_CONTEXT_STR =  "instanceTemplateContextStr";
  public static final String KEY_DISABLE_FILTER =  "disableTemplateFilter";
  public static final String PARAM_TEMPLATE_STEP = "template-step";
  public static final String PARAM_TEMPLATE_REQUEST_STOP = "template-request-stop";
  public static final String PARAM_TEMPLATE_STEP_END = "template-step-end";
  private static final String SPLIT_CHAR = "$";
  public static final String ACTIVITY_SENDER = "WIFI";

  /**********************************************************/
  public static final String STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST = "AddVenueByTemplateInWifiPost";
  public static final String STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST = "AddWifiNetworkByTemplateInWifiPost";
  public static final String STEP_ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_INSTANCE_POST = "AddAccessControlProfileByTemplateInWifiPost";
  private static Set<String> templatePostSteps = Set.of(
      STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST,
      STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST,
      STEP_ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_INSTANCE_POST
  );
  /**********************************************************/
  public static final String FIELD_ACTIVITY_ERRORS = "activity_errors";
  public static final Duration TEMPLATE_INSTANCE_TTL = Duration.ofMinutes(30);

  public enum TemplateContext {
    REC("REC"),
    MSP("MSP"),
    NONE("NONE")
    ;
    TemplateContext(String name) {
    }
  }

  public static void validateEntityIsTemplate(AbstractTenantAwareTemplateBaseEntity entity) {
    validateEntityIsTemplate(entity, isTemplateApiFlow());
  }

  @SneakyThrows
  public static void validateEntityIsTemplate(AbstractTenantAwareTemplateBaseEntity entity, boolean isTemplate) {
    boolean entityIsTemplate = BooleanUtils.isTrue(entity.getIsTemplate());
    if (entityIsTemplate != isTemplate) {
      throw new TemplateEntityValidationException(entity, isTemplate);
    }
  }

  public static <T extends AbstractTenantAwareTemplateBaseEntity> void validateEntitiesIsTemplate(
      Iterable<T> entities, boolean isTemplate) {
    entities.forEach(o -> validateEntityIsTemplate(o, isTemplate));
  }

  public static <T extends AbstractTenantAwareTemplateBaseEntity> void validateEntitiesIsTemplate(
      Iterable<T> entities) {
    validateEntitiesIsTemplate(entities, isTemplateApiFlow());
  }

  @SneakyThrows
  public static void handleTemplateHeaders(AbstractTenantAwareTemplateBaseEntity request, ConfigRequestHeader header) {
    if (isTriggeredByTemplate(header)) {
      String templateId = getTemplateId(header);
      Long templateVersion = getTemplateVersion(header);
      request.setTemplateId(templateId);
      request.setTemplateVersion(templateVersion);
    }
  }

  public static boolean isTriggeredByTemplate(ConfigRequestHeader header) {
    return header.getRequestHeaders().containsKey(HEADER_TEMPLATE_FLOW);
  }

  public static String getTemplateId(ConfigRequestHeader header) {
    Map<String, Object> requestParams = header.getRequestParams().getRequestParam();
    return (String) requestParams.get(HEADER_TEMPLATE_ID);
  }

  public static Long getTemplateVersion(ConfigRequestHeader header) {
    Map<String, Object> requestParams = header.getRequestParams().getRequestParam();
    return Long.parseLong((String) requestParams.get(HEADER_TEMPLATE_VERSION));
  }

  public static void flagTemplateApiFlowInContext() {
    flagTemplateApiFlowInContext(true);
  }

  public static void flagTemplateApiFlowInContext(boolean isTemplateApiFlow) {
    var txCtx = TxCtxHolder.get();
    if (txCtx != null) {
      txCtx.getData().put(KEY_IS_TEMPLATE, isTemplateApiFlow);
    }
  }
  public static void flagRecTemplateApiFlowInContext() {
    flagRecTemplateApiFlowInContext(TemplateContext.REC);
  }
  public static void flagMspTemplateApiFlowInContext() {
    flagRecTemplateApiFlowInContext(TemplateContext.MSP);
  }
  public static void flagRecTemplateApiFlowInContext(TemplateContext templateContext) {
    var txCtx = TxCtxHolder.get();
    if (txCtx != null) {
      txCtx.getData().put(KEY_TEMPLATE_CONTEXT_STR, templateContext.name());
    }
  }
  public static boolean isRecTemplateApiFlow() {
    var txCtx = TxCtxHolder.get();
    if (txCtx == null) {
      return false;
    }
    return txCtx.getData().getOrDefault(KEY_TEMPLATE_CONTEXT_STR, TemplateContext.NONE.name()).equals(TemplateContext.REC.name());
  }

  public static boolean isMspTemplateApiFlow() {
    var txCtx = TxCtxHolder.get();
    if (txCtx == null) {
      return false;
    }
    return txCtx.getData().getOrDefault(KEY_TEMPLATE_CONTEXT_STR, TemplateContext.NONE.name()).equals(TemplateContext.MSP.name());
  }

  public static String getTemplateContext() {
    var txCtx = TxCtxHolder.get();
    if (txCtx == null) {
      return TemplateContext.NONE.name();
    }
    if (isTemplateApiFlow())  {
      String templateContextStr = (String) txCtx.getData().getOrDefault(KEY_TEMPLATE_CONTEXT_STR, TemplateContext.NONE.name());
      return templateContextStr.equals(TemplateContext.REC.name()) ? TemplateContext.REC.name() : TemplateContext.MSP.name();
    }
    return TemplateContext.NONE.name();
  }

  public static void setTemplateContext(String templateContext) {
    var txCtx = TxCtxHolder.get();
    if (txCtx != null) {
      txCtx.getData().put(KEY_TEMPLATE_CONTEXT_STR, templateContext);
    }
  }

  public static String getInstanceTemplateContext() {
    var txCtx = TxCtxHolder.get();
    if (txCtx == null) {
      return TemplateContext.NONE.name();
    }
    return (String) txCtx.getData().getOrDefault(KEY_INSTANCE_TEMPLATE_CONTEXT_STR, TemplateContext.NONE.name());
  }

  public static void setInstanceTemplateContext(String templateContext) {
    var txCtx = TxCtxHolder.get();
    if (txCtx != null) {
      txCtx.getData().put(KEY_INSTANCE_TEMPLATE_CONTEXT_STR, templateContext);
    }
  }

  public static void disableTemplateFilterInContext() {
    var txCtx = TxCtxHolder.get();
    if (txCtx != null) {
      txCtx.getData().put(KEY_DISABLE_FILTER, true);
    }
  }

  public static void enableTemplateFilterInContext() {
    var txCtx = TxCtxHolder.get();
    if (txCtx != null) {
      txCtx.getData().put(KEY_DISABLE_FILTER, false);
    }
  }

  public static boolean isTemplateFilterInContextDisabled() {
    var txCtx = TxCtxHolder.get();
    if (txCtx == null) {
      return false;
    }
    return (boolean) txCtx.getData().getOrDefault(KEY_DISABLE_FILTER, false);
  }

  public static boolean isTemplateApiFlow() {
    var txCtx = TxCtxHolder.get();
    if (txCtx == null) {
      return false;
    }
    return (boolean) txCtx.getData().getOrDefault(KEY_IS_TEMPLATE, false);
  }

  public static boolean isNotTemplateApiFlow() {
    return !isTemplateApiFlow();
  }

  public static boolean isTemplateHandler(Class<? extends ConfigRequestHandler> clazz) {
    return AnnotationUtils.findAnnotation(clazz, TemplateHandler.class) != null;
  }

  /**
   * Determine whether the given apiAction represents a REC template-triggered action.
   * <p>
   * Convention: any cfg-request action containing "REC_TEMPLATE" is considered REC template.
   * Examples:
   * - ADD_WIFI_NETWORK_REC_TEMPLATE
   * - Add Portal Service Profile by REC Template
   * - UpdateEthernetPortProfileRecTemplate
   *
   * @param apiAction the action string from header, may be null/blank
   * @return true if REC template action; false otherwise
   */
  public static boolean isTriggeredByRecTemplate(String apiAction) {
    if (StringUtils.isBlank(apiAction)) {
      return false;
    }
    return StringUtils.containsIgnoreCase(apiAction, "_REC_TEMPLATE")
        || StringUtils.containsIgnoreCase(apiAction, " REC Template")
        || StringUtils.containsIgnoreCase(apiAction, "RecTemplate");
  }

  public static String toEcRequestId(String requestId, String stop) {
    String[] strs = StringUtils.split(requestId, SPLIT_CHAR);
    String ecRequestId = "";
    if (strs != null) {
      for (int i = 0; i < strs.length; i++) {
        if (i == 0) {
          ecRequestId = strs[i];
        } else {
          ecRequestId += SPLIT_CHAR + strs[i];
        }
        if (strs[i].equals(stop)) break;
      }
    }
    Preconditions.checkArgument(ecRequestId.contains(SPLIT_CHAR), "EC requestId should contain at least one $");
    Preconditions.checkArgument(ecRequestId.contains(stop), "EC requestId should contain " + stop);
    return ecRequestId;
  }

  public static String buildTemplateCacheKey(String step, String requestId) {
    Preconditions.checkArgument(StringUtils.isNotBlank(step), "step should not be blank");
    Preconditions.checkArgument(StringUtils.isNotBlank(requestId), "requestId should not be blank");
    return step + ":" + requestId;
  }

  public static boolean isTemplateStepEnd(Map<String, Object> extraRequestParams) {
    return Boolean.parseBoolean((String) extraRequestParams.getOrDefault(PARAM_TEMPLATE_STEP_END, "false"));
  }

  public static boolean isTemplatePostStep(String step) {
    return templatePostSteps.contains(step);
  }

  /**
   * Get settings by using supplier and validate by isTemplate flag
   * @param supplier
   * @param isTemplate
   * @return
   * @param <T>
   */
  public static <T> T getSettings(Supplier<T> supplier, boolean isTemplate) {
    return getByFlagTemplateApiFlowInContext(supplier, isTemplate);
  }

  /**
   * Get template settings by using supplier
   * @param supplier
   * @return
   * @param <T>
   */
  public static <T> T getTemplateSettings(Supplier<T> supplier) {
    return getByFlagTemplateApiFlowInContext(supplier, true);
  }

  /**
   * Get non-template settings by using supplier
   * @param supplier
   * @return
   * @param <T>
   */
  public static <T> T getNonTemplateSettings(Supplier<T> supplier) {
    return getByFlagTemplateApiFlowInContext(supplier, false);
  }

  private static <T> T getByFlagTemplateApiFlowInContext(Supplier<T> supplier, boolean isTemplateApiFlow) {
    final var currentTxCtx = TxCtxHolder.get();
    Preconditions.checkNotNull(currentTxCtx, "no TxCtx");
    final var currentIsTemplateApiFlow = isTemplateApiFlow();
    try {
      flagTemplateApiFlowInContext(isTemplateApiFlow);
      return supplier.get();
    } finally {
      flagTemplateApiFlowInContext(currentIsTemplateApiFlow);
    }
  }

  public static String buildPathVariablesForDiffAndSync(List<Pair<String, String>> pathVariables) {
    final var stringJoiner = new StringJoiner(",");
    pathVariables.forEach(pair -> stringJoiner.add(String.format("%s=%s", pair.getFirst(), pair.getSecond())));
    return stringJoiner.toString();
  }
}
