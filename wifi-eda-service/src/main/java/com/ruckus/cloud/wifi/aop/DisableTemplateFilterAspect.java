package com.ruckus.cloud.wifi.aop;

import static com.ruckus.cloud.wifi.utils.TemplateUtils.disableTemplateFilterInContext;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.enableTemplateFilterInContext;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.isTemplateFilterInContextDisabled;

import com.ruckus.cloud.wifi.hibernate.SessionFilters;
import com.ruckus.cloud.wifi.utils.TemplateUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.hibernate.Session;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class DisableTemplateFilterAspect {

  @PersistenceContext
  private EntityManager entityManager;

  @Around("@annotation(com.ruckus.cloud.wifi.aop.DisableTemplateFilter) || @within(com.ruckus.cloud.wifi.aop.DisableTemplateFilter)")
  public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
    log.trace("Processing DisableTemplateFilter annotation, disable all template filters");
    final var before = isTemplateFilterInContextDisabled();
    try {
      disableTemplateFilterInContext();
      disableTemplateFilter();
      return joinPoint.proceed();
    } finally {
      if (!before) {
        final var isTemplateApiFlow = TemplateUtils.isTemplateApiFlow();
        enableTemplateFilterInContext();
        enableTemplateFilter(isTemplateApiFlow);
        log.trace("Resume template filter: [{}]", isTemplateApiFlow);
      }
    }
  }

  private void enableTemplateFilter(boolean isTemplate) {
    SessionFilters.enableTemplateFilter(entityManager.unwrap(Session.class), isTemplate);
  }

  private void disableTemplateFilter() {
    SessionFilters.disableTemplateFilter(entityManager.unwrap(Session.class));
  }
}
