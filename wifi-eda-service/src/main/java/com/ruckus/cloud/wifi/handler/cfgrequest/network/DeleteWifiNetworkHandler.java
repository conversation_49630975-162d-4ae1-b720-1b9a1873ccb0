package com.ruckus.cloud.wifi.handler.cfgrequest.network;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;

import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.service.WifiNetworkServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.handler.header.ConfigRequestHeader;
import com.ruckus.cloud.wifi.kafka.KafkaHeaderUtil;
import com.ruckus.cloud.wifi.service.ExtendedWifiNetworkServiceCtrl;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
public class DeleteWifiNetworkHandler extends ConfigRequestHandler<Void, Void> {

  private WifiNetworkServiceCtrl wifiNetworkServiceCtrl;

  private ExtendedWifiNetworkServiceCtrl extendedWifiNetworkServiceCtrl;

  private FeatureFlagService featureFlagService;

  @Override
  public String apiAction() {
    return CfgAction.DELETE_WIFI_NETWORK.key();
  }

  @Override
  public String stepId() {
    return ApiFlowNames.DELETE_WIFI_NETWORK;
  }

  @Override
  protected Object callService(Void request, ConfigRequestHeader header) throws Exception {
    var networkId = KafkaHeaderUtil.getString(header.getRequestParams().getPathVariable(),
        "wifiNetworkId");
    var network = wifiNetworkServiceCtrl.getWifiNetwork(networkId);
    if (network instanceof com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork dpskNetwork) {
      if (isDsaeNetwork(dpskNetwork)) {
        extendedWifiNetworkServiceCtrl.deleteDsaeNetwork(networkId,
            dpskNetwork.getDsaeNetworkPairId());
        return null;
      }
    }
    if (network instanceof OpenNetwork || (network instanceof GuestNetwork
        && featureFlagService.isFeatureEnable(WIFI_CAPTIVE_PORTAL_OWE_TRANSITION,
        TxCtxHolder.tenantId()))) {
      if (isOweTransitionMaster(network)) {
        extendedWifiNetworkServiceCtrl.deleteOweTransitionNetwork(networkId,
            getOwePairNetworkId(network));
        return null;
      }
    }

    wifiNetworkServiceCtrl.deleteWifiNetwork(networkId);
    return null;
  }
}
