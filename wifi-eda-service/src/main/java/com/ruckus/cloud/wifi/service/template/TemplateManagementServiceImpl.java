package com.ruckus.cloud.wifi.service.template;

import com.ruckus.cloud.wifi.aop.AllowCrossingTenantQuery;
import com.ruckus.cloud.wifi.aop.DisableTemplateFilter;
import com.ruckus.cloud.wifi.core.header.HttpHeaderContext;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractTenantAwareTemplateBaseEntity;

import com.ruckus.cloud.wifi.entitylistener.EntityListenerEndpoint;
import com.ruckus.cloud.wifi.entitylistener.hint.EntityListenerHint;
import java.util.List;
import java.util.Set;

import com.ruckus.cloud.wifi.eda.servicemodel.TemplateEnforcementSettings;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.impl.TemplateServiceCtrl;
import com.ruckus.cloud.wifi.repository.base.TenantAwareBaseEntityTemplateRepository;
import com.ruckus.cloud.wifi.utils.TemplateUtils;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@Service
@Slf4j
@NoArgsConstructor
public class TemplateManagementServiceImpl implements TemplateManagementService {

  @PersistenceContext
  private EntityManager entityManager;

  @Override
  @Transactional(readOnly = true)
  @AllowCrossingTenantQuery(AbstractTenantAwareTemplateBaseEntity.class)
  public boolean isManagedByTemplate(Class<? extends AbstractTenantAwareTemplateBaseEntity> entityClass,
                                     String entityId) {

    AbstractTenantAwareTemplateBaseEntity entity = entityManager.find(entityClass, entityId);
    if (entity == null) {
      return false;
    }

    String templateId = entity.getTemplateId();
      if (templateId != null) {
      AbstractTenantAwareTemplateBaseEntity template = entityManager.find(entityClass, templateId);
      TemplateUtils.setInstanceTemplateContext(template.getTemplateContext());
      return template != null;
    }

    return false;
  }

  @Override
  @Transactional(readOnly = true)
  public boolean shouldBlockRequest(AbstractTenantAwareTemplateBaseEntity entity, Set<RequestSource> sourceTypes) {
    if (isTemplateSync()) {
      log.info("shouldBlockRequest -> false. template sync.");
      return false;
    }

    String templateId = entity.getTemplateId();
    if (templateId == null) { // not managed by template
      log.info("shouldBlockRequest -> entity[{}] has no templateId", entity.getId());
      return false;
    }

    AbstractTenantAwareTemplateBaseEntity template = entityManager.find(entity.getClass(), templateId);
    if (template == null) { // template might be deleted, not managed by template anymore
      log.warn("shouldBlockRequest -> entity[{}] -> template[{}] not found", entity.getId(), templateId);
      return false;
    }

    String templateTenantId = template.getTenant().getId();
    log.info("shouldBlockRequest -> found template: {} in tenant: {}", template, templateTenantId);

    // managed by template and template still exists
    // need to check request source and see if it should be blocked
    log.info("shouldBlockRequest -> template.tenantId: {} vs header.tenantId: {}",
            templateTenantId, HttpHeaderContext.getHeader(HttpHeaderName.RKS_TENANT_ID));

    if (isCreatedByRecTemplate() && !TemplateUtils.isRecTemplateApiFlow() &&
        HttpHeaderContext.containsHeader(HttpHeaderName.RKS_TENANT_ID) &&
        HttpHeaderContext.getHeader(HttpHeaderName.RKS_TENANT_ID).equals(templateTenantId)) {
      log.info("shouldBlockRequest -> true, created by REC template but not REC template flow");
      return true;
    } else if (HttpHeaderContext.containsHeader(HttpHeaderName.RKS_TENANT_ID) &&
        HttpHeaderContext.getHeader(HttpHeaderName.RKS_TENANT_ID).equals(templateTenantId)) {
      log.info("shouldBlockRequest -> false, request from template tenant: {}", templateTenantId);
      return false;
    }

    // request from EC tenant
    if (!HttpHeaderContext.containsHeader(HttpHeaderName.RKS_DELEGATION_SOURCE_TENANT_ID)) {
      log.info("shouldBlockRequest -> {}, no RKS_DELEGATION_SOURCE_TENANT_ID, request from instance tenant", sourceTypes.contains(RequestSource.FROM_EC_DIRECTLY));
      return sourceTypes.contains(RequestSource.FROM_EC_DIRECTLY);
    }

    // request with RKS_DELEGATION_SOURCE_TENANT_ID
    String delegationSource = HttpHeaderContext.getHeader(HttpHeaderName.RKS_DELEGATION_SOURCE_TENANT_ID);
    log.info("shouldBlockRequest -> templateTenantId:{} , delegationSource:{}", templateTenantId, delegationSource);
    log.info("shouldBlockRequest -> sourceTypes {}", sourceTypes);
    log.info("shouldBlockRequest -> FROM_TEMPLATE_ORIGIN? {} -> shouldBlock? {}", templateTenantId.equals(delegationSource), sourceTypes.contains(RequestSource.FROM_TEMPLATE_ORIGIN));

    if (!templateTenantId.equals(delegationSource)) { // request from other tenant via delegation
      return sourceTypes.contains(RequestSource.FROM_OTHER_DELEGATION_SOURCE);
    } else { // else, request from template tenant (MSP tenant)
      return sourceTypes.contains(RequestSource.FROM_TEMPLATE_ORIGIN);
    }
  }

  private static boolean isTemplateSync() {
    return StringUtils.contains(HttpHeaderContext.getHeader(HttpHeaderName.REQUEST_ID), "$SYNC$");
  }

  private boolean isCreatedByRecTemplate(){
    return TemplateUtils.TemplateContext.REC.name().equals(TemplateUtils.getInstanceTemplateContext());
  }

  @Override
  @Transactional(readOnly = true)
  public boolean isEnforcedInstance(AbstractTenantAwareTemplateBaseEntity entity) {
    String templateId = entity.getTemplateId();
    if (templateId == null) { // not managed by template
      log.debug("isEnforcedInstance? -> entity[{}] has no templateId", entity.getId());
      return false;
    }

    AbstractTenantAwareTemplateBaseEntity template = entityManager.find(entity.getClass(), templateId);
    if (template == null) { // template might be deleted, not managed by template anymore
      log.warn("isEnforcedInstance? -> entity[{}] -> template[{}] not found", entity.getId(), templateId);
      return false;
    }

    log.debug("isEnforcedInstance? -> entity[{}].isTemplate:{}, template[{}].isEnforced:{}",
            entity.getId(), entity.getIsTemplate(),
            template.getId(), template.getIsEnforced());
    boolean isEnforcedInstance = !entity.getIsTemplate() && template.getIsEnforced();
    log.debug("isEnforcedInstance? -> resp:{}", isEnforcedInstance);
    return isEnforcedInstance;
  }

  @Override
  @Transactional
  @AllowCrossingTenantQuery(AbstractTenantAwareTemplateBaseEntity.class)
  @EntityListenerHint(disabled = EntityListenerEndpoint.DDCCM)
  public TemplateEnforcementSettings updateTemplateEnforcementSettings(
          TemplateServiceCtrl<? extends AbstractTenantAwareTemplateBaseEntity> templateServiceImpl,
          String templateId, TemplateEnforcementSettings object) throws Throwable {

    String tenantId = TxCtxHolder.tenantId();
    AbstractTenantAwareTemplateBaseEntity template = templateServiceImpl.getRepository().findByIdAndTenantId(templateId, tenantId)
            .orElseThrow(() -> new ObjectNotFoundException(
                    String.format("Template[%s] not found in Tenant[%s]", templateId, tenantId)));

    doUpdateEnforcementSettings(templateServiceImpl, template, object);
    log.debug("Updated Template[{}][{}] with TemplateEnforcementSettings.isEnforced: {}",
            template.getClass().getSimpleName(), templateId, object.getIsEnforced());

    findInstancesAndUpdate(templateServiceImpl, template.getClass(), templateId, object);

    return object;
  }

  @Override
  @Transactional
  @AllowCrossingTenantQuery(AbstractTenantAwareTemplateBaseEntity.class)
  public void resetInstanceEnforcementSettings(
      TemplateServiceCtrl<? extends AbstractTenantAwareTemplateBaseEntity> templateServiceImpl,
      Class<? extends AbstractTenantAwareTemplateBaseEntity> entityClass, String templateId) {
    TemplateEnforcementSettings settings = new TemplateEnforcementSettings();
    settings.setIsEnforced(false);
    findInstancesAndUpdate(templateServiceImpl, entityClass, templateId, settings);
  }

  @Override
  @Transactional
  @EntityListenerHint(disabled = {EntityListenerEndpoint.DDCCM, EntityListenerEndpoint.CMN_CFG_COLLECTOR})
  @SuppressWarnings("unchecked")
  public void decoupleTemplateInstances(
      TemplateServiceCtrl<? extends AbstractTenantAwareTemplateBaseEntity> templateServiceImpl,
      String tenantId) throws Throwable {
    log.info("Decoupling template instances for tenant: {}", tenantId);
    
    // Find all template instances for the given tenant directly from the repository
    List<? extends AbstractTenantAwareTemplateBaseEntity> instances =
        templateServiceImpl.getRepository().findByTenantIdAndIsTemplateFalseAndTemplateIdNotNull(tenantId);
    
    log.debug("Found {} template instances to decouple for tenant: {}", instances.size(), tenantId);
    
    // Decouple each instance
    for (AbstractTenantAwareTemplateBaseEntity instance : instances) {
      log.debug("Decoupling instance: {} (templateId: {})", instance.getId(), instance.getTemplateId());
      
      // Set templateId to null, templateVersion to null, isEnforced to false
      instance.setTemplateId(null);
      instance.setTemplateVersion(null);
      instance.setIsEnforced(false);
    }

    ((TenantAwareBaseEntityTemplateRepository) templateServiceImpl.getRepository()).saveAll(instances);
    
    log.info("Successfully decoupled {} template instances for tenant: {}", instances.size(), tenantId);
  }

  @DisableTemplateFilter
  private void findInstancesAndUpdate(
      TemplateServiceCtrl<? extends AbstractTenantAwareTemplateBaseEntity> templateServiceImpl,
      Class<? extends AbstractTenantAwareTemplateBaseEntity> entityClass,
      String templateId, TemplateEnforcementSettings settings) {
    // looking for instances to reset
    List<? extends AbstractTenantAwareTemplateBaseEntity> instances = templateServiceImpl.getRepository().findByTemplateId(templateId); // requires crossing-tenant query
    log.debug("Found {} instances of Template[{}][{}] to update", instances.size(), entityClass.getSimpleName(), templateId);
    instances.forEach(
        inst -> log.debug("Found Instance[{}][{}] in tenant[{}] to update", entityClass.getSimpleName(), inst.getId(),
            inst.getTenant().getId()));

    for (AbstractTenantAwareTemplateBaseEntity instance : instances) {
      doUpdateEnforcementSettings(templateServiceImpl, instance, settings);
    }

    log.debug("Updated {} instances of Template[{}][{}] -> isEnforced:{}", instances.size(), entityClass.getSimpleName(), templateId, settings.getIsEnforced());
  }

  private void doUpdateEnforcementSettings(TemplateServiceCtrl templateServiceImpl,
                                           AbstractTenantAwareTemplateBaseEntity entity,
                                           TemplateEnforcementSettings object) {
    entity.setIsEnforced(object.getIsEnforced());
    templateServiceImpl.getRepository().save(entity);
  }
}
