package com.ruckus.cloud.wifi.handler.cfgrequest.template.operation;

import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_ACTIVITY_PLAN_ENABLED;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_ID;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_VERSION;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.REQUEST_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_DELEGATION_SOURCE_TENANT_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_TENANT_ID;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.ruckus.acx.activity.protobuf.execution.common.ActivityCommon;
import com.ruckus.acx.activity.protobuf.execution.common.ActivityCommon.NameValuePair;
import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.wifi.activity.ActivityService;
import com.ruckus.cloud.wifi.activity.context.ActivityContext;
import com.ruckus.cloud.wifi.activity.context.ActivityContext.ActivityContextData;
import com.ruckus.cloud.wifi.activity.context.CustomActivityContextDelegate;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.header.HttpHeaderContext;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.Property;
import com.ruckus.cloud.wifi.handler.header.ConfigRequestHeader.RequestParams;
import com.ruckus.cloud.wifi.kafka.publisher.CfgRequestPublisher;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import java.lang.reflect.Type;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;

@Slf4j
public abstract class TemplateCreateInstanceOperation<S, T> {

  protected static final String SPACE = " ";
  protected static final String SPLITTER = ", ";
  private static final String APPENDER = "$";
  public static String genEcRequestId(String mspRequestId, String ecFlowName, Optional<Integer> seq) {
    if (seq.isPresent()) {
      return mspRequestId + APPENDER + ecFlowName + seq.get();
    }
    return mspRequestId + APPENDER + ecFlowName;
  }

  public static String genEcRequestId(String mspRequestId, String ecFlowName) {
    return genEcRequestId(mspRequestId, ecFlowName, Optional.empty());
  }

  private static final Validator validator = Validation.byDefaultProvider()
      .configure()
      .messageInterpolator(new FixedLocaleMessageInterpolator(Locale.US))
      .buildValidatorFactory()
      .getValidator();

  private static final Gson gson = new GsonBuilder().serializeNulls().create();

  @Autowired
  protected ExecutionPlanBuilder executionPlanBuilder;
  @Autowired
  protected ActivityService activityService;
  @Autowired
  protected CfgRequestPublisher cfgRequestPublisher;
  @Autowired
  protected ObjectMapper objectMapper;

  protected void validateTemplate(S template) throws Exception {
    Boolean isTemplate = (Boolean) template.getClass()
        .getMethod("getIsTemplate").invoke(template);
    String name = getName(template);
    if (!isTemplate) {
      throw new RuntimeException(String.format("%s[%s] is not a template",
          template.getClass().getSimpleName(), name));
    }
  }

  protected String getName(S template) throws Exception {
    return (String) template.getClass()
        .getMethod("getName").invoke(template);
  }

  protected abstract T toInstanceView(S template) throws Exception;

  private void preHandleOverrides(T instanceView, List<Property> overrides) throws Exception {
    Set<String> lackedOverrides = new LinkedHashSet<>();
    Set<String> incorrectOverrides = new LinkedHashSet<>();
    validateOverrides(instanceView, overrides, lackedOverrides, incorrectOverrides);

    if (!lackedOverrides.isEmpty()) {
      String temp = String.join(SPLITTER, lackedOverrides);
      throw new CommonException(String.format("Missing overrides: %s", temp))
          .setStatus(HttpStatus.BAD_REQUEST);
    }
    if (!incorrectOverrides.isEmpty()) {
      String temp = String.join(SPLITTER, incorrectOverrides);
      throw new CommonException(String.format("Incorrect overrides: %s", temp))
          .setStatus(HttpStatus.BAD_REQUEST);
    }
  }

  protected void validateOverrides(T instanceView, List<Property> overrides, Set<String> lackedOverrides, Set<String> incorrectOverrides) {
    Set<String> overrideKeys = overrides.stream().map(Property::getKey).collect(Collectors.toSet());
    // The default has no need extra overrides so it should be empty
    incorrectOverrides.addAll(overrideKeys);
  }

  protected void handleOverrides(T instanceView, List<Property> overrides) throws Exception {
    // The default has no need
  }

  protected RequestParams buildRequestParams() {
    return new RequestParams();
  }

  @VisibleForTesting
  final protected void doHandleOverrides(T instanceView, List<Property> overrides) throws Exception {
    preHandleOverrides(instanceView, overrides);
    handleOverrides(instanceView, overrides);
  }

  private T doHandleOverrides(S template, @NonNull String instanceId, List<Property> overrides) throws Exception {
    if (overrides == null) {
      overrides = List.of();
    }

    T instanceView = toInstanceView(template);
    if (instanceView == null) {
      // Some APIs don't have payload
      return null;
    }

    final var beforeJson = objectMapper.writeValueAsString(instanceView);

    setId(instanceView, instanceId);
    doHandleOverrides(instanceView, overrides);

    final var afterJson = objectMapper.writeValueAsString(instanceView);

    if (log.isDebugEnabled()) {
      Type mapType = new TypeToken<Map<String, Object>>() {}.getType();
      Map<String, Object> firstMap = gson.fromJson(beforeJson, mapType);
      Map<String, Object> secondMap = gson.fromJson(afterJson, mapType);
      if (firstMap == null || secondMap == null) {
        log.debug("firstMap is null = {}, secondMap is null = {}",
            firstMap == null, secondMap == null);
      } else {
        log.debug("template instance overrides diff = {}",
            Maps.difference(firstMap, secondMap).entriesDiffering());
      }
    }

    return instanceView;
  }

  protected void setId(T instanceView, String instanceId) throws Exception {
    instanceView.getClass().getMethod("setId", String.class)
        .invoke(instanceView, instanceId);
  }

  protected void validate(T instanceView) throws Exception {
    if (instanceView == null) return;

    Set<ConstraintViolation<T>> violations = validator.validate(instanceView);

    if (!violations.isEmpty()) {
      String errors = violations.stream()
          .map(v -> {
            String message = Optional.ofNullable(Errors.message(v.getMessage()))
                .map(s -> v.getMessage() + SPACE + s).orElse(v.getMessage());
            if (v.getPropertyPath() == null) {
              return message;
            } else {
              return v.getPropertyPath() + SPACE + message;
            }
          })
          .collect(Collectors.joining(SPLITTER));
      log.error("validation failed instanceView = {}",
          objectMapper.writeValueAsString(instanceView));
      throw new CommonException(errors).setStatus(HttpStatus.BAD_REQUEST);
    }
  }

  protected abstract CfgAction getCfgAction();
  public abstract String getFlowName();

  protected Long getTemplateVersion(S template) throws Exception {
    Date date = (Date) template.getClass().getMethod("getUpdatedDate").invoke(template);
    return date.getTime();
  }

  protected String getId(S template) throws Exception {
    return (String) template.getClass().getMethod("getId").invoke(template);
  }

  protected void handleEC(ExtendedTemplateInstanceCreateRequest request, S template, T instanceView) throws Exception {
    String ecTenantId = request.getTargetTenantId();
    String ecRequestId = genEcRequestId(TxCtxHolder.txId(), getFlowName(), request.getSequence());
    String templateId = getId(template);
    String templateVersion = getTemplateVersion(template).toString();
    Map<String, String> originalContext = HttpHeaderContext.getContext();

    try {

      // handle delegation first
      if (StringUtils.isEmpty(HttpHeaderContext.getHeader(RKS_DELEGATION_SOURCE_TENANT_ID))) {
        HttpHeaderContext.setHeader(RKS_DELEGATION_SOURCE_TENANT_ID, HttpHeaderContext.getHeader(RKS_TENANT_ID));
      }

      // change HttpHeaderContext to EC
      HttpHeaderContext.setHeader(RKS_TENANT_ID, ecTenantId);
      HttpHeaderContext.setHeader(REQUEST_ID, ecRequestId);

      // send cfg request to wifi-eda for EC tenant
      RequestParams requestParams = buildRequestParams();
      requestParams.getRequestParam().put(HEADER_TEMPLATE_ID, templateId);
      requestParams.getRequestParam().put(HEADER_TEMPLATE_VERSION, templateVersion);
      requestParams.getRequestParam().putAll(extraRequestParams(request));
      requestParams.getPathVariable().putAll(extraPathVariables(request));

      // prepare EC ActivityContext
      ActivityContextData context = new ActivityContextData();
      context.setDelegate(CustomActivityContextDelegate.builder()
          .tenantId(ecTenantId).requestId(ecRequestId) //EC
          .adminName(HttpHeaderContext.getHeader(HttpHeaderName.RKS_ADMIN_NAME)) //MSP
          .adminEmail(HttpHeaderContext.getHeader(HttpHeaderName.RKS_IDM_USER_ID)) //MSP
          .build());
      ActivityContext.setContext(context);

      // send activity-plan for EC tenant
      final var optionalActivityPlanBytes = request.getActivityPlanBytes();
      if (optionalActivityPlanBytes.isPresent()) {
        ActivityExecutionPlan activityExecutionPlan =
            ActivityExecutionPlan.newBuilder(
                    executionPlanBuilder.toActivityExecutionPlan(optionalActivityPlanBytes.get()))
                .addDescriptionVariables(NameValuePair.newBuilder()
                    .setName(HEADER_TEMPLATE_VERSION)
                    .setValue(templateVersion))
                .addAllDescriptionVariables(toNameValuePair(extraDescriptionVariables(request, requestParams)))
                .build();
        activityService.sendWifiActivity(activityExecutionPlan, ecTenantId, ecRequestId,
            getFlowName());
      } else {
        log.debug("Disable activity plan for ec cfg-request, requestId: [{}]", ecRequestId);
        HttpHeaderContext.replaceContext(Map.of(HEADER_ACTIVITY_PLAN_ENABLED, "false"));
      }

      // send cfg request to wifi-eda for EC tenant
      cfgRequestPublisher.publish(getCfgAction(), requestParams,
          objectMapper.writeValueAsString(instanceView), ecTenantId, ecRequestId);

    } finally {
      HttpHeaderContext.setContext(originalContext);
      ActivityContext.removeContext();
    }
  }

  private List<ActivityCommon.NameValuePair> toNameValuePair(Map<String, Object> map) {
    return map.entrySet().stream()
        .filter(e -> e.getValue() != null && e.getValue() instanceof String)
        .map(entry -> NameValuePair.newBuilder()
            .setName(entry.getKey())
            .setValue(entry.getValue().toString()).build())
        .toList();
  }

  protected Map<String, Object> extraDescriptionVariables(ExtendedTemplateInstanceCreateRequest request,
      RequestParams requestParams) {
    return Collections.emptyMap();
  }

  /**
   * Set extra request parameters to the cfg-request for EC tenant.
   * @param request
   * @return
   */
  protected Map<String, Object> extraRequestParams(ExtendedTemplateInstanceCreateRequest request) {
    return Optional.ofNullable(request.getExtraRequestParams()).orElse(Collections.emptyMap());
  }

  /**
   * Set extra path variables to the cfg-request for EC tenant.
   * @param request
   * @return
   */
  protected Map<String, Object> extraPathVariables(ExtendedTemplateInstanceCreateRequest request) {
    return Optional.ofNullable(request.getExtraPathVariables()).orElse(Collections.emptyMap());
  }

  @SneakyThrows
  public void execute(ExtendedTemplateInstanceCreateRequest request, String templateId, S template) {

    Preconditions.checkNotNull(request.getTargetTenantId(),
        "ExtendedTemplateInstanceCreateRequest.targetTenantId should not be null");
    Preconditions.checkNotNull(request.getInstanceId(),
        "ExtendedTemplateInstanceCreateRequest.instanceId should not be null");

    String instanceId = request.getInstanceId();

    log.info("template create check template {}[{}]",
        template.getClass().getSimpleName(), templateId);
    validateTemplate(template);

    // handle overrides
    log.info("template create instance[{}] from template {}[{}]",
        instanceId, template.getClass().getSimpleName(), templateId);
    List<Property> overrides = request.getOverrides();
    T instanceView = doHandleOverrides(template, instanceId, overrides);

    // validate instanceView
    log.info("template create instance validate instance before push to ec wifi-eda");
    validate(instanceView);

    // handle EC activity-plan and EC wifi-eda request
    log.info("template create instance handle ec activity-plan and ec wifi-eda request");
    handleEC(request, template, instanceView);
  }

  public static com.ruckus.cloud.wifi.eda.viewmodel.Property newViewProperty(String k, String v) {
    com.ruckus.cloud.wifi.eda.viewmodel.Property p = new com.ruckus.cloud.wifi.eda.viewmodel.Property();
    p.setKey(k);
    p.setValue(v);
    return p;
  }

  public static Property newServiceProperty(String k, String v) {
    Property p = new Property();
    p.setKey(k);
    p.setValue(v);
    return p;
  }

  protected void doValidateOverride(T instanceView, String methodName, String key,
      Set<String> overrides, Set<String> lackedOverrides, Set<String> incorrectOverrides) {
    boolean isBlank = true;
    boolean contained = overrides.contains(key);
    try {
      String value = (String) instanceView.getClass().getMethod(methodName).invoke(instanceView);
      isBlank = StringUtils.isBlank(value);
    } catch (Exception ignored) {} // some method might not exist for some network type
    if (!isBlank && !contained) {
      lackedOverrides.add(key);
    }
    if (isBlank && contained) {
      incorrectOverrides.add(key);
    }
  }

  private static class FixedLocaleMessageInterpolator extends ResourceBundleMessageInterpolator {

    private final Locale locale;

    private FixedLocaleMessageInterpolator(Locale locale) {
      this.locale = locale;
    }

    @Override
    public String interpolate(String message, Context context) {
      return super.interpolate(message, context, this.locale);
    }

    @Override
    public String interpolate(String message, Context context, Locale locale) {
      return super.interpolate(message, context, this.locale);
    }
  }
}
