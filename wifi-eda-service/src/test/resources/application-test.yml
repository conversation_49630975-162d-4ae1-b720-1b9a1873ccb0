KAFKA_SECURITY_PROTOCOL: PLAINTEXT

vault:
  enabled: false

redis.client.enabled: false

spring:
  autoconfigure:
    exclude: net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration,net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration

logging:
  config: classpath:logback-test.xml
---
spring:
  config:
    activate:
      on-profile: h2
  datasource:
    url: jdbc:h2:mem:db;DB_CLOSE_DELAY=-1
    username: sa
    password: sa
    driver-class-name: org.h2.Driver
---
spring:
  config:
    activate:
      on-profile: postgressql
  datasource:
    url: ***************************************************
    username: AP_OPS_RKS
    password: AP_OPS_RKS
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update