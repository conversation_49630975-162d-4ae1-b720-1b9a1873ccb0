spring:
  jpa:
    hibernate:
      ddl-auto: none
  autoconfigure:
    exclude: net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration,net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration
logging:
  level:
    root: WARN
    org.hibernate.engine.jdbc.spi.SqlExceptionHelper: ERROR
    com.ruckus.cloud.wifi: INFO #DEBUG
    com.ruckus.acx: INFO
    com.ruckus.cloud.wifi.entitylistener.observation: ERROR

allow-unexpected-crossing-tenant-query: false
---
# This configuration could be loaded by running test with environment variable `SPRING_PROFILES_INCLUDE=debug`
spring:
  config:
    activate:
      on-profile: debug
  jpa:
    show-sql: false

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type: TRACE
