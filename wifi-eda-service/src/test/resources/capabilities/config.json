[{"model": "M510", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "displayLabel": "WAN", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "displayLabel": "LAN", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": true, "simCardPrimaryEnabled": true, "simCardPrimaryApn": "defaultapn", "simCardPrimaryRoaming": true, "simCardPrimaryCellularNetworkSelection": "AUTO", "simCardSecondaryEnabled": true, "simCardSecondaryApn": "defaultapn", "simCardSecondaryRoaming": true, "simCardSecondaryCellularNetworkSelection": "AUTO", "wanConnection": "ETH_WITH_CELLULAR_FAILOVER", "primaryWanRecoveryTimer": 60}, {"model": "R720", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at", "802.3bt-Class_5"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 85, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false, "supportMesh": true}, {"model": "H320", "lanPorts": [{"id": "1", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "3", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "US", "SG", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 53, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false, "supportMesh": false}, {"model": "T610S", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "VV7321", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": [], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 12, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "E510", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "externalAntenna": {"externalAntenna24enable": false, "externalAntenna50enable": false, "externalAntenna24dbi": 3, "externalAntenna50dbi": 3, "isAllowDisableExtAnt": true, "isExtAntOnOffTogether": false}, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T300E", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "externalAntenna": {"externalAntenna50enable": false, "externalAntenna50dbi": 5, "isAllowDisableExtAnt": true}, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "requireOneEnabledTrunkPort": true}, {"model": "T305I", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T305E", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "externalAntenna": {"externalAntenna24enable": true, "externalAntenna50enable": true, "externalAntenna24dbi": 11, "externalAntenna50dbi": 14, "isAllowDisableExtAnt": true, "isExtAntOnOffTogether": false}, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R500", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "requireOneEnabledTrunkPort": true}, {"model": "R550", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 140, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false, "supportIoT": true}, {"model": "R600", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "requireOneEnabledTrunkPort": true}, {"model": "T750", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": true}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "3", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5", "802.3bt-Class_6", "802.3bt-Class_7"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 288, "isOutdoor": true, "has160MHzChannelBandwidth": true, "canSupportPoeOut": true, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R650", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 140, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R350", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 172, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T310S", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R710", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R730", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 288, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false, "usbPowerEnable": true}, {"model": "R510", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 75, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R610", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "US", "SG", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false, "supportChannel144": true}, {"model": "T710", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": true}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "3", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": true, "canSupportPoeMode": false, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T610", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T750SE", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": true}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "3", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5", "802.3bt-Class_6", "802.3bt-Class_7"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "externalAntenna": {"externalAntenna24enable": false, "externalAntenna50enable": false, "externalAntenna24dbi": 8, "externalAntenna50dbi": 8, "isAllowDisableExtAnt": true, "isExtAntOnOffTogether": true}, "capabilityScore": 288, "isOutdoor": true, "has160MHzChannelBandwidth": true, "canSupportPoeOut": true, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R310", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 32, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R750", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 288, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R850", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 288, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T310N", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "H350", "lanPorts": [{"id": "1", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "3", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 172, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T710S", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": true}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "3", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": true, "canSupportPoeMode": false, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "H550", "lanPorts": [{"id": "1", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": true}, {"id": "2", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "3", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "4", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "5", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at", "802.3bt-Class_5"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 172, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": true, "canSupportPoeMode": true, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T811CM", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": true, "isPoePort": false, "isPoeOutPort": true}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 85, "isOutdoor": true, "has160MHzChannelBandwidth": true, "canSupportPoeOut": true, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": false, "canSupportCellular": false}, {"model": "H510", "lanPorts": [{"id": "1", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "3", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "4", "defaultType": "ACCESS", "untagId": 1, "vlanMembers": "1", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "5", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 76, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T310D", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T350SE", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "externalAntenna": {"externalAntenna24enable": false, "externalAntenna50enable": false, "externalAntenna24dbi": 8, "externalAntenna50dbi": 8, "isAllowDisableExtAnt": true, "isExtAntOnOffTogether": true}, "capabilityScore": 172, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T310C", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "C110", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 75, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": false, "canSupportCellular": false}, {"model": "R320", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 79, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "V860", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": [], "allowDfsCountry": [], "allowCbandCountry": [], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 12, "isOutdoor": false, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": false, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false, "supportChannel144": false}, {"model": "T350D", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 172, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "T350C", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": true, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3af", "802.3at"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 172, "isOutdoor": true, "has160MHzChannelBandwidth": false, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false}, {"model": "R760", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5"], "canSupportCellular": false, "simCardPrimaryEnabled": true, "simCardPrimaryRoaming": true, "simCardSecondaryEnabled": true, "simCardSecondaryRoaming": true, "capabilityScore": 288, "supportTriRadio": true, "supportDual5gMode": true, "supportChannel144": true, "support11AX": true, "support11BE": false, "maxChannelization24G": 40, "maxChannelization5G": 160, "maxChannelization6G": 160, "supportMesh": true}, {"model": "R770", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 288, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "canSupportCellular": false, "supportTriRadio": true, "supportDual5gMode": false, "supportChannel144": true, "support11AX": true, "support11BE": true, "maxChannelization24G": 40, "maxChannelization5G": 160, "maxChannelization6G": 320, "supportApStickyClientSteering": true}, {"model": "R670", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5"], "canSupportCellular": false, "simCardPrimaryEnabled": true, "simCardPrimaryRoaming": true, "simCardSecondaryEnabled": true, "simCardSecondaryRoaming": true, "capabilityScore": 288, "supportTriRadio": true, "supportDual5gMode": true, "supportChannel144": true, "support11AX": true, "support11BE": false, "maxChannelization24G": 40, "maxChannelization5G": 160, "maxChannelization6G": 160, "supportMesh": true, "supportBandCombination": true, "bandCombinationCapabilities": ["DUAL", "TRIPLE"], "defaultBandCombination": "TRIPLE", "usbPowerEnable": true}, {"model": "T670", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5"], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["RL", "EG", "GB", "DE", "AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "GR", "HU", "IT", "LV", "LU", "MT", "NL", "NO", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "LT", "IE", "ME"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "capabilityScore": 260, "isOutdoor": true, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": false, "requireOneEnabledTrunkPort": true, "canSupportCellular": false, "supportTriRadio": true, "supportDual5gMode": false, "supportChannel144": true, "support11AX": true, "support11BE": true, "maxChannelization24G": 40, "maxChannelization5G": 160, "maxChannelization6G": 160, "supportMesh": true, "supportBandCombination": true, "defaultBandCombination": "DUAL", "bandCombinationCapabilities": ["DUAL", "TRIPLE"], "usbPowerEnable": true}, {"model": "T670SN", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5"], "canSupportCellular": false, "simCardPrimaryEnabled": true, "simCardPrimaryRoaming": true, "simCardSecondaryEnabled": true, "simCardSecondaryRoaming": true, "capabilityScore": 288, "supportTriRadio": true, "supportDual5gMode": true, "supportChannel144": true, "support11AX": true, "support11BE": false, "maxChannelization24G": 40, "maxChannelization5G": 160, "maxChannelization6G": 160, "supportMesh": true, "supportBandCombination": true, "bandCombinationCapabilities": ["DUAL", "TRIPLE"], "defaultBandCombination": "TRIPLE", "AntennaTypeCapabilities": ["SECTOR", "NARROW"], "supportAntennaType": true, "defaultAntennaType": "SECTOR", "usbPowerEnable": true}, {"model": "R350:R350E", "lanPorts": [{"id": "1", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": true, "isPoePort": false, "isPoeOutPort": false}, {"id": "2", "defaultType": "TRUNK", "untagId": 1, "vlanMembers": "1-4094", "trunkPortOnly": false, "supportDisable": false, "isPoePort": true, "isPoeOutPort": false}], "allowDfsCountry": ["US", "SG"], "allowCbandCountry": ["GB"], "lldpEnable": true, "lldpAdInterval": 30, "lldpHoldTime": 120, "lldpMgmtEnable": true, "ledOn": true, "isOutdoor": false, "has160MHzChannelBandwidth": true, "canSupportPoeOut": false, "canSupportPoeMode": true, "canSupportLacp": true, "requireOneEnabledTrunkPort": true, "poeModeCapabilities": ["Auto", "802.3at", "802.3bt-Class_5"], "canSupportCellular": false, "simCardPrimaryEnabled": true, "simCardPrimaryRoaming": true, "simCardSecondaryEnabled": true, "simCardSecondaryRoaming": true, "capabilityScore": 288, "supportTriRadio": true, "supportDual5gMode": true, "supportChannel144": true, "support11AX": true, "support11BE": false, "maxChannelization24G": 40, "maxChannelization5G": 160, "maxChannelization6G": 160, "supportMesh": true}]