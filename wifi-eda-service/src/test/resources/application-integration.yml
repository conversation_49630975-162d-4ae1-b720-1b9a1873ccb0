KAFKA_SECURITY_PROTOCOL: PLAINTEXT
SYSTEM_NOTIFICATION_TEAMS: https://commscope.webhook.office.com/webhookb2/fake
ENVIRONMENT: local
REGION: tw

vault:
  enabled: false

logging:
  level:
    root: WARN
    org.hibernate.engine.jdbc.spi.SqlExceptionHelper: ERROR
    com.ruckus.cloud.wifi: INFO #DEBUG
    com.ruckus.cloud.wifi.entitylistener.observation: ERROR
    com.ruckus.cloud.wifi.hibernate.TemplateFlagValidator: ERROR
    org.apache.kafka.clients.consumer.ConsumerConfig: WARN

afc:
  support:
    apModels: T670,R770

kafka:
  autoStartup: false
  group-migration: false

allow-unexpected-crossing-tenant-query: false

spring:
  autoconfigure:
    exclude: net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration,net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration
---
spring:
  jpa:
    show-sql: false
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
        ddl-auto: none
default:
  dpsk-service-profile:
    bind-max-count: 2
  rogue-ap-policy-profile:
    tenant-max-count: 3
    rule-max-count: 2
    bind-max-count: 2
  client-isolation-allowlist:
    tenant-max-count: 2
    bind-max-count: 3
---
# This configuration could be loaded by running test with environment variable `SPRING_PROFILES_INCLUDE=debug`
spring:
  config:
    activate:
      on-profile: debug

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type: TRACE
    com.ruckus.cloud.wifi.kafka.GroupMigrationServiceImpl: OFF