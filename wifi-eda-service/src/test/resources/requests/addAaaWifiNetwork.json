{"wlan": {"wlanSecurity": "WPA2Enterprise", "advancedCustomization": {"userUplinkRateLimiting": 0, "userDownlinkRateLimiting": 0, "totalUplinkRateLimiting": 0, "totalDownlinkRateLimiting": 0, "maxClientsOnWlanPerRadio": 100, "enableBandBalancing": true, "clientIsolation": false, "clientIsolationOptions": {"autoVrrp": false}, "hideSsid": false, "forceMobileDeviceDhcp": false, "clientLoadBalancingEnable": true, "enableAaaVlanOverride": true, "directedThreshold": 5, "enableNeighborReport": true, "enableFastRoaming": false, "enableAdditionalRegulatoryDomains": true, "mobilityDomainId": 1, "radioCustomization": {"rfBandUsage": "BOTH", "bssMinimumPhyRate": "default", "phyTypeConstraint": "OFDM", "managementFrameMinimumPhyRate": "6"}, "enableSyslog": false, "clientInactivityTimeout": 120, "respectiveAccessControl": true, "singleSessionIdAccounting": false, "proxyARP": false, "enableAirtimeDecongestion": false, "enableJoinRSSIThreshold": false, "joinRSSIThreshold": -85, "enableTransientClientManagement": false, "joinWaitTime": 30, "joinExpireTime": 300, "joinWaitThreshold": 10, "enableOptimizedConnectivityExperience": false, "broadcastProbeResponseDelay": 15, "rssiAssociationRejectionThreshold": -75, "enableAntiSpoofing": false, "enableArpRequestRateLimit": true, "arpRequestRateLimit": 15, "enableDhcpRequestRateLimit": true, "dhcpRequestRateLimit": 15, "dnsProxyEnabled": false}, "vlanId": 1, "ssid": "aaaNet003", "enabled": true, "bypassCPUsingMacAddressAuthentication": false, "bypassCNA": false, "managementFrameProtection": "Disabled"}, "name": "aaaNet003", "type": "aaa", "description": ""}