{"wlan": {"wlanSecurity": "WPA2Enterprise", "advancedCustomization": {"maxClientsOnWlanPerRadio": 100, "enableBandBalancing": true, "clientIsolation": false, "clientIsolationOptions": {"autoVrrp": false}, "hideSsid": false, "forceMobileDeviceDhcp": false, "clientLoadBalancingEnable": true, "enableAaaVlanOverride": true, "directedThreshold": 5, "enableNeighborReport": true, "enableFastRoaming": false, "enableAdditionalRegulatoryDomains": true, "mobilityDomainId": 1, "radioCustomization": {"rfBandUsage": "BOTH", "bssMinimumPhyRate": "default", "phyTypeConstraint": "OFDM", "managementFrameMinimumPhyRate": "6"}, "enableSyslog": false, "clientInactivityTimeout": 120, "accessControlEnable": false, "respectiveAccessControl": true, "vlanPool": null, "applicationPolicyEnable": false, "l2AclEnable": false, "l3AclEnable": false, "wifiCallingEnabled": false, "wifiCallingIds": [], "singleSessionIdAccounting": false, "proxyARP": false, "enableAirtimeDecongestion": false, "enableJoinRSSIThreshold": false, "joinRSSIThreshold": -85, "enableTransientClientManagement": false, "joinWaitTime": 30, "joinExpireTime": 300, "joinWaitThreshold": 10, "enableOptimizedConnectivityExperience": false, "broadcastProbeResponseDelay": 15, "rssiAssociationRejectionThreshold": -75, "enableAntiSpoofing": false, "enableArpRequestRateLimit": true, "arpRequestRateLimit": 15, "enableDhcpRequestRateLimit": true, "dhcpRequestRateLimit": 15, "dnsProxyEnabled": false}, "vlanId": 1, "ssid": "aaaNet003", "enabled": true, "bypassCPUsingMacAddressAuthentication": false, "bypassCNA": false, "managementFrameProtection": "Disabled"}, "venues": null, "name": "aaaNet003", "enableAuthProxy": false, "enableAccountingProxy": false, "type": "aaa", "description": "", "authRadius": {"primary": {"ip": "********", "port": 1812, "sharedSecret": "********"}}}