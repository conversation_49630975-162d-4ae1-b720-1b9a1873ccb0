{"providers": [{"name": "TestA", "regions": [{"name": "RegionA", "aaaName": "TestA-A", "authRadius": {"primary": {"ip": "*******", "port": "1001", "sharedSecret": "test"}, "tlsEnabled": false}, "accountingRadius": {"primary": {"ip": "*******", "port": "1002", "sharedSecret": "test"}, "tlsEnabled": false}}, {"name": "RegionB", "aaaName": "TestA-B", "authRadius": {"primary": {"ip": "*******", "port": "1001", "sharedSecret": "test"}, "tlsEnabled": false}, "accountingRadius": {"primary": {"ip": "*******", "port": "1002", "sharedSecret": "test"}, "tlsEnabled": false}}, {"name": "RegionC", "aaaName": "TestA-C", "authRadius": {"primary": {"ip": "*******", "port": "1801", "sharedSecret": "test"}, "tlsEnabled": false}, "accountingRadius": {"primary": {"ip": "*******", "port": "1002", "sharedSecret": "test"}, "tlsEnabled": false}}]}]}