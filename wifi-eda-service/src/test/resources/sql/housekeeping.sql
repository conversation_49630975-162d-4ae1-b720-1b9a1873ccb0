-- Two records for tenant 808682950bf04f7bbd9e9b0ae8f71bd8 but different updated_timestamp
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(1011000, false, '808682950bf04f7bbd9e9b0ae8f71bd8', 1688704077417, '266f84c6-95cd-4663-904d-54225e654d89$UpdateAp', 1688704077417);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(1011001, false, '808682950bf04f7bbd9e9b0ae8f71bd8', 1694142622589, '35b44c8a-fe14-4b45-9d2a-119b9b88d13e$UpdateNetworkVenue', 1694142622589);

INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(1011005, false, '68b108f739fa4887b34d43849a422f36', 1688704077417, '123e4567-e89b-12d3-a456-426655440000', 1688704077417);

-- fa2ac10a595f40b88b19e1a8673379c7 and 3f51e3dce36b4d0e892e5d3bead28c14 should be expired because the updated_timestamp are set to 0 and 1.
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(1011003, false, 'fa2ac10a595f40b88b19e1a8673379c7', 0, '550e8400-e29b-41d4-a716-446655440000', 1);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(1011004, false, '3f51e3dce36b4d0e892e5d3bead28c14', 1, 'a0a0a0a0-a0a0-00a0-a0a0-a000a000a000', 2);

-- b2164b9a0c8d490fbdb56da461e3c7d4 is not a dummy tenant because it exist on tenant table.
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(1011002, false, 'b2164b9a0c8d490fbdb56da461e3c7d4', 1670953769480, '8de17625-7dd6-49e3-be6e-d16ee48447c8', 1670953769480);
INSERT INTO tenant (id) VALUES('b2164b9a0c8d490fbdb56da461e3c7d4');


-- revision for tenant 5165b36526854e9694821f6f1a930bf7
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(2247130, false, '5165b36526854e9694821f6f1a930bf7', 1694142622589, '99999', 1694142622589);
INSERT INTO revision_type_info
    ("type", entity_id, entity_type, revision_type, tenant, revision)
    VALUES('WifiRevisionTypeInfo', '506423977777', 'com.ruckus.cloud.wifi.eda.servicemodel.Ap', 'DEL', '5165b36526854e9694821f6f1a930bf7', 2247130);
INSERT INTO revision_type_info
    ("type", entity_id, entity_type, revision_type, tenant, revision)
    VALUES('WifiRevisionTypeInfo', '506423977778', 'com.ruckus.cloud.wifi.eda.servicemodel.Ap', 'DEL', '5165b36526854e9694821f6f1a930bf7', 2247130);
INSERT INTO revision_type_info
    ("type", entity_id, entity_type, revision_type, tenant, revision)
    VALUES('WifiRevisionTypeInfo', 'f5ec7e2fbfbe4e628b3bf33eb27a51de', 'com.ruckus.cloud.wifi.eda.servicemodel.Venue', 'DEL', '5165b36526854e9694821f6f1a930bf7', 2247130);
INSERT INTO revision_type_info
    ("type", entity_id, entity_type, revision_type, tenant, revision)
    VALUES('WifiRevisionTypeInfo', '8cfc866d01b44bf0b88eacdd1811b85c', 'com.ruckus.cloud.wifi.eda.servicemodel.ApGroup', 'DEL', '5165b36526854e9694821f6f1a930bf7', 2247130);
INSERT INTO ap_aud (id, rev, revtype, tenant) VALUES('506423977777', 2247130, 2, '5165b36526854e9694821f6f1a930bf7');
INSERT INTO ap_aud (id, rev, revtype, tenant) VALUES('506423977778', 2247130, 2, '5165b36526854e9694821f6f1a930bf7');
INSERT INTO venue_aud (id, rev, revtype, tenant) VALUES('f5ec7e2fbfbe4e628b3bf33eb27a51de', 2247130, 2, '5165b36526854e9694821f6f1a930bf7');
INSERT INTO ap_group_aud (id, rev, revtype, tenant) VALUES('8cfc866d01b44bf0b88eacdd1811b85c', 2247130, 2, '5165b36526854e9694821f6f1a930bf7');

-- revision for tenant c98c811a76ec4ad8a4a3d54d08b7454e
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(2247131, false, 'c98c811a76ec4ad8a4a3d54d08b7454e', 1694142622589, '99998', 1694142622589);
INSERT INTO revision_type_info
    ("type", entity_id, entity_type, revision_type, tenant, revision)
    VALUES('WifiRevisionTypeInfo', '7e7878ef1e0b4f7e82e540b5a4b479eb', 'com.ruckus.cloud.wifi.eda.servicemodel.Network', 'MOD', 'c98c811a76ec4ad8a4a3d54d08b7454e', 2247131);
INSERT INTO network_aud (id, rev, revtype, tenant, type) VALUES('7e7878ef1e0b4f7e82e540b5a4b479eb', 2247131, 1, 'c98c811a76ec4ad8a4a3d54d08b7454e', 'PSK');

--
INSERT INTO tenant (id) VALUES ('6212eb6e036843bd97d1767b0e9ebea2');
INSERT INTO tenant (id) VALUES ('d4e5f8e7c3e84f769127c6c6d3b577fc');
INSERT INTO tenant (id) VALUES ('9a63e0d3642b4b64bf8a8e206e5f0cd5');

-- 6212eb6e036843bd97d1767b0e9ebea2: 1 Non-expired: updated_timestamp = now
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000010, false, '6212eb6e036843bd97d1767b0e9ebea2', EXTRACT(EPOCH FROM NOW())::BIGINT * 1000, '9951351', EXTRACT(EPOCH FROM NOW())::BIGINT * 1000);
-- 6212eb6e036843bd97d1767b0e9ebea2: 6 Expired: updated_timestamp = 0
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000001, false, '6212eb6e036843bd97d1767b0e9ebea2', 0, '9951352', 0);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000002, false, '6212eb6e036843bd97d1767b0e9ebea2', 0, '9951353', 0);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000003, false, '6212eb6e036843bd97d1767b0e9ebea2', 0, '9951354', 0);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000004, false, '6212eb6e036843bd97d1767b0e9ebea2', 0, '9951355', 0);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000005, false, '6212eb6e036843bd97d1767b0e9ebea2', 0, '9951356', 0);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000006, false, '6212eb6e036843bd97d1767b0e9ebea2', 0, '9951357', 0);

-- d4e5f8e7c3e84f769127c6c6d3b577fc: 1 Non-expired: updated_timestamp = now
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000020, false, 'd4e5f8e7c3e84f769127c6c6d3b577fc', EXTRACT(EPOCH FROM NOW())::BIGINT * 1000, '9951422', EXTRACT(EPOCH FROM NOW())::BIGINT * 1000);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000026, false, 'd4e5f8e7c3e84f769127c6c6d3b577fc', EXTRACT(EPOCH FROM NOW())::BIGINT * 1000, '9951452', EXTRACT(EPOCH FROM NOW())::BIGINT * 1000);

-- d4e5f8e7c3e84f769127c6c6d3b577fc: 5 Expired: updated_timestamp = 0
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000021, false, 'd4e5f8e7c3e84f769127c6c6d3b577fc', 0, '9952352', 0);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000022, false, 'd4e5f8e7c3e84f769127c6c6d3b577fc', 0, '9952353', 0);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000023, false, 'd4e5f8e7c3e84f769127c6c6d3b577fc', 0, '9952354', 0);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000024, false, 'd4e5f8e7c3e84f769127c6c6d3b577fc', 0, '9952355', 0);
INSERT INTO revision_info
    (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
    VALUES(3000025, false, 'd4e5f8e7c3e84f769127c6c6d3b577fc', 0, '9952356', 0);