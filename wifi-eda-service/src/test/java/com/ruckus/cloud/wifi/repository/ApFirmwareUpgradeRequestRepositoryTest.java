package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Slf4j
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant)
        VALUES ('e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap_firmware_upgrade_request (id, tenant,
            request_id, venue_id,
            source_version, target_version, deadline, model,
            backup_partion_upgrade, backup_partion_upgrade_count, serial_number)
        VALUES ('95f73c75dee4458eae615c65db86dee9', '4c8279f79307415fa9e4c88a1819f0fc',
            'fcaa4fc4-c5ba-48dc-ae76-ce658436f9a2', 'e465bac6afb747a4987d0d0945f77221',
            '6.2.1.103.100', '6.2.1.103.200', 0, 'R550',
            false, 0, '1234567890');
    """)
public class ApFirmwareUpgradeRequestRepositoryTest {
  private static final String SERIAL_NUMBER = "1234567890";
  private static final String REQUEST_ID = "fcaa4fc4-c5ba-48dc-ae76-ce658436f9a2";
  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String VENUE_ID = "e465bac6afb747a4987d0d0945f77221";

  @Autowired ApFirmwareUpgradeRequestRepository apFirmwareUpgradeRequestRepository;

  @Test
  public void findBySerialNumberTest() {
    assertEquals(
        SERIAL_NUMBER,
        apFirmwareUpgradeRequestRepository.findBySerialNumber(SERIAL_NUMBER).getSerialNumber());
  }

  @Test
  public void findByBackupPartionUpgradeTest() {
    assertEquals(0, apFirmwareUpgradeRequestRepository.findByBackupPartionUpgrade(true).size());
  }

  @Test
  public void findByRequestIdTest() {
    assertEquals(1, apFirmwareUpgradeRequestRepository.findByRequestId(REQUEST_ID).size());
  }

  @Test
  public void deleteBySerialNumberTest() {
    assertEquals(
        1,
        apFirmwareUpgradeRequestRepository.deleteByTenantIdAndSerialNumber(
            TENANT_ID, SERIAL_NUMBER));
  }

  @Test
  public void findByVenueIdTest() {
    assertEquals(
        1, apFirmwareUpgradeRequestRepository.findByTenantIdAndVenueId(TENANT_ID, VENUE_ID).size());
  }

  @Test
  public void findByVenueIdWithPageTest() {
    assertEquals(
      1, apFirmwareUpgradeRequestRepository.findByTenantIdAndVenueId(TENANT_ID, VENUE_ID,
        PageRequest.of(0, 10)).size());
  }

  @Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenant-id-1'), ('tenant-id-2');
    INSERT INTO venue (id, tenant)
        VALUES ('venue-id-1', 'tenant-id-1'), ('venue-id-2', 'tenant-id-2');
    INSERT INTO ap_firmware_upgrade_request (id, tenant,
            request_id, venue_id,
            source_version, target_version, deadline, model,
            backup_partion_upgrade, backup_partion_upgrade_count, serial_number)
    VALUES ('afur-id-1', 'tenant-id-1', 'req-id-1', 'venue-id-1',
            '6.2.1.103.100', '6.2.1.103.200', 0, 'R770', false, 0, '1234567891'),
            ('afur-id-2', 'tenant-id-1', 'req-id-1', 'venue-id-1', 
            '6.2.1.103.100', '6.2.1.103.200', 0, 'R650', false, 0, '1234567892'),
            ('afur-id-3', 'tenant-id-1', 'req-id-1', 'venue-id-1',
            '6.2.1.103.100', '6.2.1.103.200', 0, 'R770', false, 0, '1234567893');
    """)
  @Test
  void findDistinctModelByTenantIdAndRequestIdTest() {
    List<String> apModels =
        apFirmwareUpgradeRequestRepository
            .findDistinctModelByTenantIdAndRequestId("tenant-id-1", "req-id-1");
    assertEquals(2, apModels.size());
    assertTrue(apModels.containsAll(List.of("R770", "R650")));
  }

  @Sql(statements = """
      INSERT INTO ap_version (id, category) 
      VALUES ('7.0.0.103.200','RECOMMENDED'), 
             ('6.2.1.103.300','RECOMMENDED'), 
             ('7.1.0.200.100','RECOMMENDED');
      INSERT INTO tenant (id) VALUES ('tenant-id-A');
      INSERT INTO venue (id, tenant) VALUES ('venue-id-A', 'tenant-id-A');
      INSERT INTO ap_firmware_upgrade_request (id, tenant, request_id, venue_id, source_version, target_version, deadline, model, backup_partion_upgrade, backup_partion_upgrade_count, serial_number)
      VALUES ('afur-id-1', 'tenant-id-A', 'req-id-1', 'venue-id-A', '7.0.0.103.200', '7.0.0.103.200', 0, 'R770', false, 0, '0234567891'),
             ('afur-id-2', 'tenant-id-A', 'req-id-1', 'venue-id-A', '6.2.1.103.300', '6.2.1.103.300', 0, 'R650', false, 0, '0234567892'),
             ('afur-id-3', 'tenant-id-A', 'req-id-1', 'venue-id-A', '7.1.0.200.100', '7.1.0.200.100', 0, 'T670', false, 0, '0234567893');
      """)
  @Test
  void testFindMaximumAndMinimumTargetVersion() {
    List<String> maxMinFw = apFirmwareUpgradeRequestRepository.findMaximumAndMinimumTargetVersion("tenant-id-A", "venue-id-A");
    assertEquals("7.1.0.200.100", maxMinFw.get(0));
    assertEquals("6.2.1.103.300", maxMinFw.get(1));
  }

  @Sql(statements = """
      INSERT INTO ap_version (id, category) 
      VALUES ('7.0.0.103.200','RECOMMENDED'), 
             ('6.2.1.103.300','RECOMMENDED'), 
             ('7.1.0.200.100','RECOMMENDED');
      INSERT INTO tenant (id) VALUES ('tenant-id-B');
      INSERT INTO venue (id, tenant) VALUES ('venue-id-B', 'tenant-id-B');
      INSERT INTO ap_firmware_upgrade_request (id, tenant, request_id, venue_id, source_version, target_version, deadline, model, backup_partion_upgrade, backup_partion_upgrade_count, serial_number)
      VALUES ('afur-id-3', 'tenant-id-B', 'req-id-1', 'venue-id-B', '7.1.0.200.100', '7.0.0.103.200', 0, 'T670', false, 0, '0234567893');
      """)
  @Test
  void testFindMaximumAndMinimumTargetVersionOneRecord() {
    List<String> maxMinFw = apFirmwareUpgradeRequestRepository.findMaximumAndMinimumTargetVersion("tenant-id-B", "venue-id-B");
    assertEquals("7.0.0.103.200", maxMinFw.get(0));
    assertEquals("7.0.0.103.200", maxMinFw.get(1));
  }
}
