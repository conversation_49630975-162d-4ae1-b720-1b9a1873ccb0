package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApIotSettings;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.requirement.ApModelFamily;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModel;
import java.util.Set;
import jakarta.annotation.PostConstruct;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class ApIotFeatureTest {

  @SpyBean
  private ApIotFeature unit;

  @TestConfiguration
  static class TestApModelFamilyConfig {
    @PostConstruct
    public void init() {
      // UNSUPPORTED_MODELS_WIFI_6 = Set.of("T350C", "R350:R350E");
      // UNSUPPORTED_MODELS_WIFI_7 = Set.of("T670");
      ApModelFamily.WIFI_6.updateModels(Set.of("R550", "R750", "T350C", "R350:R350E"));
      ApModelFamily.WIFI_6E.updateModels(Set.of("R560", "R760"));
      ApModelFamily.WIFI_7.updateModels(Set.of("R370", "R770", "R670", "T670", "T670SN"));
    }
  }

  @Nested
  class TestIsDeviceSupport {
    @ParameterizedTest
    @CsvSource({
        //   apVersion,  apModel,    expectedResult(isDeviceSupport)
        "7.1.0.510.930,  R550,       false",
        "7.1.0.510.1005, T670,       false",
        "7.1.0.510.1005, R550,       true",
        "7.1.0.510.1005, T350C,      false",
        "7.1.0.510.1006, R350:R350E, false",
        "7.1.1.520.200,  T350C,      false",
        "7.1.1.520.287,  R550,       true",
        "7.1.1.520.287,  T350C,      false",
        "7.1.1.520.524,  R370,       false",
        "7.1.1.520.800,  T670,       true",
        "7.1.2.500.100,  R550,       true",
        "7.1.2.500.100,  T670SN,     true",
        "7.1.2.500.100,  T350C,      false",
        "             ,       ,      false"
    })
    void testIsDeviceSupport(String apVersion, String apModel, boolean expectedResult) {
      ApFirmwareModel apFwModel = ApFirmwareModel.builder()
          .firmware(apVersion).model(apModel).build();
      BDDAssertions.then(unit.isDeviceSupport(apFwModel)).isEqualTo(expectedResult);
    }
  }

  @Nested
  @FeatureFlag(disable = FlagNames.IOT_MQTT_BROKER_TOGGLE)
  class GivenFfIsDisabled {
    @Test
    void whenIotSettingsNotSet_shouldReturnFalse(Venue venue) {
      BDDAssertions.then(unit.test(venue)).isFalse();
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.IOT_MQTT_BROKER_TOGGLE)
  class GivenFfIsEnabled {
    @Test
    void whenIotSettingsNull_shouldReturnFalse(Venue venue) {
      venue.setIotSettings(null);
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    void whenIotSettingsDisabled_shouldReturnFalse(Venue venue) {
      var settings = new VenueApIotSettings();
      settings.setEnabled(false);
      venue.setIotSettings(settings);
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    void whenIotSettingsEnabled_shouldReturnTrue(Venue venue) {
      var settings = new VenueApIotSettings();
      settings.setEnabled(true);
      settings.setMqttBrokerAddress("venueiot.ruckus.com");
      venue.setIotSettings(settings);
      BDDAssertions.then(unit.test(venue)).isTrue();
    }
  }
}