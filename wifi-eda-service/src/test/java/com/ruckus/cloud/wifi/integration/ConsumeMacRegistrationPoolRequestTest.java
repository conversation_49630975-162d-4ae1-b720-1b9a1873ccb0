package com.ruckus.cloud.wifi.integration;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.*;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;

@Slf4j
@Tag("MacRegistrationPoolTest")
@WifiIntegrationTest
public class ConsumeMacRegistrationPoolRequestTest extends AbstractRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private ExtendedMessageUtil messageUtil;

  public static final String WIFI_NETWORK_ID = "wifiNetworkId";
  public static final String MAC_REGISTRATION_POOL_ID = "macRegistrationPoolId";

  @Test
  void testActivateMacRegistrationPoolOnWifiNetwork(final Tenant tenant, final Venue venue) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String macRegistrationPoolId = randomTxId();

    final var openNetwork = network(OpenNetwork.class).generate();
    openNetwork.getWlan().setMacAddressAuthentication(true);
    openNetwork.getWlan().setNetwork(openNetwork);
    repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());
    networkId = openNetwork.getId();

    RequestParams requestParams =
        new RequestParams()
            .addPathVariable(WIFI_NETWORK_ID, networkId)
            .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    messageUtil.sendWifiCfgRequest(
        tenantId, requestId,
        CfgAction.ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
        userName,
        requestParams,
        null);
    assertActivityStatusSuccess(ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);

    final OpenNetwork network =
        repositoryUtil.find(OpenNetwork.class, networkId);
    assertThat(network)
        .isNotNull()
        .matches(n -> Objects.equals(n.getId(), networkId))
        .matches(n -> BooleanUtils.isTrue(n.getWlan().getMacAddressAuthentication()) && Objects.equals(n.getWlan().getMacRegistrationListId(), macRegistrationPoolId));

  }

  @Test
  void testActivateMacRegistrationPoolOnWifiNetworkWithRadius(final Tenant tenant, final Radius radius) {
    String networkId;
    var tenantId = tenant.getId();
    var userName = randomName();
    var requestId = randomTxId();
    var macRegistrationPoolId = randomTxId();

    final var openNetwork = network(OpenNetwork.class).generate();
    openNetwork.setAuthRadius(radius);
    openNetwork.getWlan().setMacAddressAuthentication(true);
    openNetwork.getWlan().setNetwork(openNetwork);
    repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());
    networkId = openNetwork.getId();

    RequestParams requestParams =
        new RequestParams()
            .addPathVariable(WIFI_NETWORK_ID, networkId)
            .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    assertThatThrownBy(() ->
        messageUtil.sendWifiCfgRequest(
            tenantId,
            requestId,
            CfgAction.ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
            userName,
            requestParams,
            null))
        .isNotNull()
        .getRootCause()
        .isInstanceOf(InvalidPropertyValueException.class)
        .hasMessage(
            "MAC address authentication is already enabled and bind with RADIUS server profile: ["
                + radius.getId() + "]");
    assertActivityStatusFail(ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);
  }

  @Test
  void testActivateMacRegistrationPoolOnWifiNetworkFail(final Tenant tenant, final Radius radius) {
    String networkId;
    var tenantId = tenant.getId();
    var userName = randomName();
    var requestId = randomTxId();
    var macRegistrationPoolId = randomTxId();

    final var openNetwork = network(OpenNetwork.class).generate();
    openNetwork.setAuthRadius(radius);
    //mac auth is disable after network created
    openNetwork.getWlan().setMacAddressAuthentication(false);
    openNetwork.getWlan().setNetwork(openNetwork);
    repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());
    networkId = openNetwork.getId();

    RequestParams requestParams =
        new RequestParams()
            .addPathVariable(WIFI_NETWORK_ID, networkId)
            .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    assertThatThrownBy(() ->
        messageUtil.sendWifiCfgRequest(
            tenantId,
            requestId,
            CfgAction.ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
            userName,
            requestParams,
            null))
        .isNotNull()
        .getRootCause()
        .isInstanceOf(InvalidPropertyValueException.class)
        .hasMessage(Errors.WIFI_10522.message());
    assertActivityStatusFail(ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);
  }

  @Test
  void testDeactivateMacRegistrationPoolOnWifiNetwork(final Tenant tenant, final Venue venue) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String macRegistrationPoolId = randomTxId();

    final var openNetwork = network(OpenNetwork.class).generate();
    openNetwork.getWlan().setNetwork(openNetwork);
    openNetwork.getWlan().setMacRegistrationListId(macRegistrationPoolId);
    repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());
    networkId = openNetwork.getId();

    RequestParams requestParams =
        new RequestParams()
            .addPathVariable(WIFI_NETWORK_ID, networkId)
            .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    messageUtil.sendWifiCfgRequest(
        tenantId, requestId,
        CfgAction.DEACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
        userName,
        requestParams,
        null);
    assertActivityStatusSuccess(DEACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);

    final OpenNetwork network =
        repositoryUtil.find(OpenNetwork.class, networkId);
    assertThat(network)
        .isNotNull()
        .matches(n -> Objects.equals(n.getId(), networkId))
        .matches(n -> BooleanUtils.isFalse(n.getWlan().getMacAddressAuthentication()) && Objects.equals(n.getWlan().getMacRegistrationListId(), null));
  }

  @Test
  void testDeactivateMacRegistrationPoolOnWifiNetworkActivateOnVenue(final Tenant tenant, final Venue venue) {
    String networkId;
    var tenantId = tenant.getId();
    var userName = randomName();
    var requestId = randomTxId();
    var macRegistrationPoolId = randomTxId();

    final var openNetwork = network(OpenNetwork.class).generate();
    networkId = openNetwork.getId();
    openNetwork.getWlan().setNetwork(openNetwork);
    openNetwork.getWlan().setMacAddressAuthentication(true);
    openNetwork.getWlan().setMacRegistrationListId(macRegistrationPoolId);
    repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(
        NetworkVenueTestFixture.randomNetworkVenue(openNetwork, venue), tenant.getId(),
        randomTxId());

    RequestParams requestParams =
        new RequestParams()
            .addPathVariable(WIFI_NETWORK_ID, networkId)
            .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    assertThatThrownBy(() ->
        messageUtil.sendWifiCfgRequest(
            tenantId,
            requestId,
            CfgAction.DEACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
            userName,
            requestParams,
            null))
        .isNotNull()
        .getRootCause()
        .isInstanceOf(InvalidPropertyValueException.class)
        .hasMessage("Cannot deactivate MAC registration pool on network which has activated on venue");
    assertActivityStatusFail(DEACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);
  }

  @Test
  void testActivateMacRegistrationPoolOnAAANetwork_should_fail_without_dot1x_enabled(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String macRegistrationPoolId = randomTxId();

    final var aaaNetwork = network(AAANetwork.class).generate();
    aaaNetwork.getWlan().setNetwork(aaaNetwork);
    aaaNetwork.getWlan().setMacAddressAuthentication(true);
    aaaNetwork.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(false);
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
    networkId = aaaNetwork.getId();

    RequestParams requestParams =
            new RequestParams()
                    .addPathVariable(WIFI_NETWORK_ID, networkId)
                    .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    assertThatThrownBy(() ->
            messageUtil.sendWifiCfgRequest(
                    tenantId,
                    requestId,
                    CfgAction.ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
                    userName,
                    requestParams,
                    null))
            .isNotNull()
            .getRootCause()
            .isInstanceOf(InvalidPropertyValueException.class)
            .hasMessage("Invalid network type [AAA]");
    assertActivityStatusFail(ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);
  }

  @FeatureFlag(enable = FlagNames.WIFI_DOT1X_WITH_MAC_REGISTRATION_ENABLED)
  @Test
  void testActivateMacRegistrationPoolOnWifiNetwork_with_dot1x_enabled(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    var macRegistrationPoolId = randomTxId();

    final var aaaNetwork = network(AAANetwork.class).generate();
    final var radiusProfile = Generators.radiusProfile().generate();
    aaaNetwork.getWlan().setNetwork(aaaNetwork);
    aaaNetwork.getWlan().setMacAddressAuthentication(true);
    aaaNetwork.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(false);
    repositoryUtil.createOrUpdate(radiusProfile, tenant.getId());
    aaaNetwork.setAuthRadius(radiusProfile);
    aaaNetwork.setEnableAuthProxy(true);
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
    networkId = aaaNetwork.getId();

    RequestParams requestParams =
            new RequestParams()
                    .addPathVariable(WIFI_NETWORK_ID, networkId)
                    .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    messageUtil.sendWifiCfgRequest(
            tenantId, requestId,
            CfgAction.ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
            userName,
            requestParams,
            null);
    assertActivityStatusSuccess(ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);

    final AAANetwork network =
            repositoryUtil.find(AAANetwork.class, networkId);
    assertThat(network)
            .isNotNull()
            .matches(n -> Objects.equals(n.getId(), networkId))
            .matches(n -> BooleanUtils.isTrue(n.getWlan().getMacAddressAuthentication()) && Objects.equals(n.getWlan().getMacRegistrationListId(), macRegistrationPoolId));

  }

  @FeatureFlag(enable = FlagNames.WIFI_DOT1X_WITH_MAC_REGISTRATION_ENABLED)
  @Test
  void testActivateMacRegistrationPoolOnNonProxyWifiNetwork_should_fail_with_dot1x_enabled(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    var macRegistrationPoolId = randomTxId();

    final var aaaNetwork = network(AAANetwork.class).generate();
    final var radiusProfile = Generators.radiusProfile().generate();
    aaaNetwork.getWlan().setNetwork(aaaNetwork);
    aaaNetwork.getWlan().setMacAddressAuthentication(true);
    aaaNetwork.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(false);
    repositoryUtil.createOrUpdate(radiusProfile, tenant.getId());
    aaaNetwork.setAuthRadius(radiusProfile);
    aaaNetwork.setEnableAuthProxy(false);
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
    networkId = aaaNetwork.getId();

    RequestParams requestParams =
            new RequestParams()
                    .addPathVariable(WIFI_NETWORK_ID, networkId)
                    .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    assertThatThrownBy(() ->
            messageUtil.sendWifiCfgRequest(
                    tenantId,
                    requestId,
                    CfgAction.ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
                    userName,
                    requestParams,
                    null))
            .isNotNull()
            .getRootCause()
            .isInstanceOf(InvalidPropertyValueException.class)
            .hasMessage("Cannot activate MAC registration pool on non-proxy AAA network");
  }

  @FeatureFlag(enable = FlagNames.WIFI_DOT1X_WITH_MAC_REGISTRATION_ENABLED)
  @Test
  void testActivateMacRegistrationPoolOnDpskNetwork_should_fail_with_dot1x_enabled(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String macRegistrationPoolId = randomTxId();

    final var dpskNetwork = network(DpskNetwork.class).generate();
    dpskNetwork.setUseDpskService(true);
    dpskNetwork.getWlan().setNetwork(dpskNetwork);
    repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());
    networkId = dpskNetwork.getId();

    RequestParams requestParams =
            new RequestParams()
                    .addPathVariable(WIFI_NETWORK_ID, networkId)
                    .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    assertThatThrownBy(() ->
            messageUtil.sendWifiCfgRequest(
                    tenantId,
                    requestId,
                    CfgAction.ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
                    userName,
                    requestParams,
                    null))
            .isNotNull()
            .getRootCause()
            .isInstanceOf(InvalidPropertyValueException.class)
            .hasMessage("Invalid network type [DPSK]");
    assertActivityStatusFail(ACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);
  }

  @FeatureFlag(enable = FlagNames.WIFI_DOT1X_WITH_MAC_REGISTRATION_ENABLED)
  @Test
  void testDeactivateMacRegistrationPoolOnWifiNetwork_with_dot1x_enabled(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String macRegistrationPoolId = randomTxId();

    final var aaaNetwork = network(AAANetwork.class).generate();
    aaaNetwork.getWlan().setNetwork(aaaNetwork);
    aaaNetwork.getWlan().setMacAddressAuthentication(true);
    aaaNetwork.getWlan().setMacRegistrationListId(macRegistrationPoolId);
    aaaNetwork.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(false);
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
    networkId = aaaNetwork.getId();

    RequestParams requestParams =
            new RequestParams()
                    .addPathVariable(WIFI_NETWORK_ID, networkId)
                    .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    messageUtil.sendWifiCfgRequest(
            tenantId, requestId,
            CfgAction.DEACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
            userName,
            requestParams,
            null);
    assertActivityStatusSuccess(DEACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);

    final AAANetwork network =
            repositoryUtil.find(AAANetwork.class, networkId);
    assertThat(network)
            .isNotNull()
            .matches(n -> Objects.equals(n.getId(), networkId))
            .matches(n -> BooleanUtils.isFalse(n.getWlan().getMacAddressAuthentication()) && Objects.equals(n.getWlan().getMacRegistrationListId(), null));
  }

  @FeatureFlag(enable = FlagNames.WIFI_DOT1X_WITH_MAC_REGISTRATION_ENABLED)
  @Test
  void testDeactivateMacRegistrationPoolOnDpskNetwork_should_fail_with_dot1x_enabled(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String macRegistrationPoolId = randomTxId();

    final var dpskNetwork = network(DpskNetwork.class).generate();
    dpskNetwork.setUseDpskService(true);
    dpskNetwork.getWlan().setNetwork(dpskNetwork);
    repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());
    networkId = dpskNetwork.getId();

    RequestParams requestParams =
            new RequestParams()
                    .addPathVariable(WIFI_NETWORK_ID, networkId)
                    .addPathVariable(MAC_REGISTRATION_POOL_ID, macRegistrationPoolId);

    assertThatThrownBy(() ->
            messageUtil.sendWifiCfgRequest(
                    tenantId,
                    requestId,
                    CfgAction.DEACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK,
                    userName,
                    requestParams,
                    null))
            .isNotNull()
            .getRootCause()
            .isInstanceOf(InvalidPropertyValueException.class)
            .hasMessage("Invalid network type [DPSK]");
    assertActivityStatusFail(DEACTIVATE_MAC_REGISTRATION_POOL_ON_WIFI_NETWORK, tenantId);
  }
}