package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth6GEnum;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Arrays;
import org.apache.commons.lang3.RandomUtils;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class Wifi7With320MhzFeatureTest {

  @SpyBean
  private Wifi7With320MhzFeature unit;

  @Nested
  class WhenIsWifi7320MhzEnabled {

    @Test
    void givenChannelBandwidthNot320Mhz(Venue venue) {
      final var channels =
          Arrays.stream(ChannelBandwidth6GEnum.values())
              .filter(c -> c != ChannelBandwidth6GEnum._320MHz)
              .toList();
      final var radioParams6G = new VenueRadioParams6G();
      radioParams6G.setChannelBandwidth(channels.get(RandomUtils.nextInt(0, channels.size())));
      final var radioCustomization = new VenueRadioCustomization();
      radioCustomization.setRadioParams6G(radioParams6G);
      venue.setRadioCustomization(radioCustomization);

      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    void givenChannelBandwidthIs320Mhz(Venue venue) {
      final var radioParams6G = new VenueRadioParams6G();
      radioParams6G.setChannelBandwidth(ChannelBandwidth6GEnum._320MHz);
      final var radioCustomization = new VenueRadioCustomization();
      radioCustomization.setRadioParams6G(radioParams6G);
      venue.setRadioCustomization(radioCustomization);

      BDDAssertions.then(unit.test(venue)).isTrue();
    }
  }
}
