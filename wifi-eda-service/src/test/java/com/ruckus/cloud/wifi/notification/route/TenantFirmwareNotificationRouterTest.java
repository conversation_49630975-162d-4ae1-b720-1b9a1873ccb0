package com.ruckus.cloud.wifi.notification.route;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.notification.VenueUpgradeVersionsMapping;
import com.ruckus.cloud.wifi.notification.dto.VenueTemplateDto;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class TenantFirmwareNotificationRouterTest {

  @SpyBean
  private TenantFirmwareNotificationRouter tenantFirmwareNotificationRouter;

  @MockBean
  private FeatureFlagService featureFlagService;

  @MockBean
  private RedirectFirmwareUpdate redirectFirmwareUpdate;

  @MockBean
  private ProcessFirmwareUpdate processFirmwareUpdate;

  @Test
  public void testProcessCreateScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    List<Venue> venues = new LinkedList<>();
    VenueUpgradeVersionsMapping mapping = VenueUpgradeVersionsMapping.create();
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(false);

    tenantFirmwareNotificationRouter.sendCreateScheduleNotifications(tenant,
        venues, mapping, requestId);

    verify(processFirmwareUpdate).sendCreateScheduleNotifications(eq(tenant),
        any(), any(), anyString());
    verify(redirectFirmwareUpdate, never()).sendCreateScheduleNotifications(eq(tenant),
        any(), any(), anyString());
  }

  @Test
  public void testRedirectCreateScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    List<Venue> venues = new LinkedList<>();
    VenueUpgradeVersionsMapping mapping = VenueUpgradeVersionsMapping.create();
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(true);

    tenantFirmwareNotificationRouter.sendCreateScheduleNotifications(tenant,
        venues, mapping, requestId);

    verify(redirectFirmwareUpdate).sendCreateScheduleNotifications(eq(tenant),
        any(), any(), anyString());
    verify(processFirmwareUpdate, never()).sendCreateScheduleNotifications(eq(tenant),
        any(), any(), anyString());
  }

  @Test
  public void testProcessChangeScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    ApVersion apVersion = new ApVersion();
    Map<String, List<VenueTemplateDto>> venueTemplateMap = new HashMap<>();
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(false);

    tenantFirmwareNotificationRouter.sendChangeScheduleNotifications(tenant,
        apVersion, venueTemplateMap, requestId);

    verify(processFirmwareUpdate).sendChangeScheduleNotifications(eq(tenant),
        any(), any(), anyString());
    verify(redirectFirmwareUpdate, never()).sendChangeScheduleNotifications(eq(tenant),
        any(), any(), anyString());
  }

  @Test
  public void testProcessChangeScheduleNotificationByApVersions() {
    Tenant tenant = new Tenant("tenantId");
    ApVersion apVersion = new ApVersion();
    Map<String, List<VenueTemplateDto>> venueTemplateMap = new HashMap<>();
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(false);

    tenantFirmwareNotificationRouter.sendChangeScheduleNotifications(tenant,
        apVersion, venueTemplateMap, requestId);

    verify(processFirmwareUpdate).sendChangeScheduleNotifications(eq(tenant),
        any(), any(), anyString());
    verify(redirectFirmwareUpdate, never()).sendChangeScheduleNotifications(eq(tenant),
        any(), any(), anyString());
  }

  @Test
  public void testRedirectChangeScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    ApVersion apVersion = new ApVersion();
    Map<String, List<VenueTemplateDto>> venueTemplateMap = new HashMap<>();
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(true);

    tenantFirmwareNotificationRouter.sendChangeScheduleNotifications(tenant,
        apVersion, venueTemplateMap, requestId);

    verify(processFirmwareUpdate, never()).sendChangeScheduleNotifications(eq(tenant),
        any(), any(), anyString());
    verify(redirectFirmwareUpdate).sendChangeScheduleNotifications(eq(tenant),
        any(), any(), anyString());
  }

  @Test
  public void testRedirectChangeScheduleNotificationByApVersions() {
    Tenant tenant = new Tenant("tenantId");
    ApVersion apVersion = new ApVersion();
    Map<String, List<VenueTemplateDto>> venueTemplateMap = new HashMap<>();
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(true);

    tenantFirmwareNotificationRouter.sendChangeScheduleNotifications(tenant,
        apVersion, venueTemplateMap, requestId);

    verify(processFirmwareUpdate, never()).sendChangeScheduleNotifications(eq(tenant),
        any(), any(), anyString());
    verify(redirectFirmwareUpdate).sendChangeScheduleNotifications(eq(tenant),
        any(), any(), anyString());
  }

  @Test
  public void testProcessCancelScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    List<Venue> venues = new LinkedList<>();
    VenueUpgradeVersionsMapping mapping = VenueUpgradeVersionsMapping.create();
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(false);

    tenantFirmwareNotificationRouter.sendCancelScheduleNotifications(tenant,
        venues, mapping, requestId);

    verify(processFirmwareUpdate).sendCancelScheduleNotifications(eq(tenant),
        any(), any(), anyString());
    verify(redirectFirmwareUpdate, never()).sendCancelScheduleNotifications(eq(tenant),
        any(), any(), anyString());
  }

  @Test
  public void testRedirectCancelScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    List<Venue> venues = new LinkedList<>();
    VenueUpgradeVersionsMapping mapping = VenueUpgradeVersionsMapping.create();
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(true);

    tenantFirmwareNotificationRouter.sendCancelScheduleNotifications(tenant,
        venues, mapping, requestId);

    verify(processFirmwareUpdate, never()).sendCancelScheduleNotifications(eq(tenant),
        any(), any(), anyString());
    verify(redirectFirmwareUpdate).sendCancelScheduleNotifications(eq(tenant),
        any(), any(), anyString());
  }

  @Test
  public void testProcessUpdateFinishedScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    List<ApVersion> apVersions = new LinkedList<>();
    List<VenueTemplateDto> dtos = new LinkedList<>();
    boolean success = true;
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(false);

    tenantFirmwareNotificationRouter.sendUpdateFinishNotifications(tenant,
        apVersions, dtos, success, requestId);

    verify(processFirmwareUpdate).sendUpdateFinishNotifications(eq(tenant),
        any(), any(), anyBoolean(), anyString());
    verify(redirectFirmwareUpdate, never()).sendUpdateFinishNotifications(eq(tenant),
        any(), any(), anyBoolean(), anyString());
  }

  @Test
  public void testRedirectUpdateFinishedScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    List<ApVersion> apVersions = new LinkedList<>();
    List<VenueTemplateDto> dtos = new LinkedList<>();
    boolean success = true;
    String requestId = "requestId";
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(true);

    tenantFirmwareNotificationRouter.sendUpdateFinishNotifications(tenant,
        apVersions, dtos, success, requestId);

    verify(processFirmwareUpdate, never()).sendUpdateFinishNotifications(eq(tenant),
        any(), any(), anyBoolean(), anyString());
    verify(redirectFirmwareUpdate).sendUpdateFinishNotifications(eq(tenant),
        any(), any(), anyBoolean(), anyString());
  }

  @Test
  public void testProcessReminderScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    List<ApVersion> apVersions = new LinkedList<>();
    String slotDate = "time-slot";
    List<Venue> venues = new LinkedList<>();
    String requestId = "requestId";
    Set<String> timezones = new HashSet<>();

    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(false);

    tenantFirmwareNotificationRouter.sendReminderScheduleNotifications(tenant,
        apVersions, slotDate, venues, requestId, timezones);

    verify(processFirmwareUpdate).sendReminderScheduleNotifications(eq(tenant),
        any(), any(), any(), anyString(), any());
    verify(redirectFirmwareUpdate, never()).sendReminderScheduleNotifications(eq(tenant),
        any(), any(), any(), anyString(), any());
  }

  @Test
  public void testRedirectReminderScheduleNotification() {
    Tenant tenant = new Tenant("tenantId");
    List<ApVersion> apVersions = new LinkedList<>();
    String slotDate = "time-slot";
    List<Venue> venues = new LinkedList<>();
    String requestId = "requestId";
    Set<String> timezones = new HashSet<>();

    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER), eq(tenant.getId())))
        .thenReturn(true);

    tenantFirmwareNotificationRouter.sendReminderScheduleNotifications(tenant,
        apVersions, slotDate, venues, requestId, timezones);

    verify(processFirmwareUpdate, never()).sendReminderScheduleNotifications(eq(tenant),
        any(), any(), any(), anyString(), any());
    verify(redirectFirmwareUpdate).sendReminderScheduleNotifications(eq(tenant),
        any(), any(), any(), anyString(), any());
  }
}