package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.repository.NetworkVenueRepository.formattedNetworkIdAtVenueId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatRuntimeException;
import static org.assertj.core.groups.Tuple.tuple;

import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileNetworkMapping;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.utils.TxCtxUtils;
import java.util.List;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("VxLanTunnelFeatureTest")
@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        'venue1c6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        'venue2c6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        'venue3c6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        'venue4c6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO sd_lan_profile (id, tenant) VALUES (
        'sdlan10ac6afb747a4987d0d0945f772',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO sd_lan_profile (id, tenant) VALUES (
        'sdlan20ac6afb747a4987d0d0945f772',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO sd_lan_profile (id, tenant) VALUES (
        'sdlan30ac6afb747a4987d0d0945f772',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO sd_lan_profile (id, tenant) VALUES (
        'sdlan40ac6afb747a4987d0d0945f772',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tunnel_profile (id, name, tenant, mtu_type, mtu_size, force_fragmentation) VALUES (
        'afc284d992694d5c9d7a2fcf2289a0bd',
        'tunnel profile 1',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'MANUAL',
        1450,
        false);
    INSERT INTO tunnel_profile (id, name, tenant, mtu_type, mtu_size, force_fragmentation) VALUES (
        'qwert4d992694d5c9d7a2fcf2289a0bd',
        'tunnel profile 2',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'MANUAL',
        1450,
        false);
    INSERT INTO tunnel_profile (id, name, tenant, mtu_type, mtu_size, force_fragmentation) VALUES (
        'guest4d992694d5c9d7a2fcf2289a0bd',
        'tunnel profile 3',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'MANUAL',
        1450,
        false);
    INSERT INTO tunnel_profile (id, name, tenant, mtu_type, mtu_size, force_fragmentation) VALUES (
        'fwd4d992694d5c9d7a2fcf2289a0bd',
        'forwarding tunnel profile 4',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'MANUAL',
        1450,
        false);
    INSERT INTO sd_lan_profile_regular_setting (id, tenant, tunnel_profile, guest_traffic_tunnel_profile, sd_lan_profile, venue) VALUES (
        'rs10e13cc150444ca1956dd68c8999f2',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'afc284d992694d5c9d7a2fcf2289a0bd',
        'guest4d992694d5c9d7a2fcf2289a0bd', 
        'sdlan10ac6afb747a4987d0d0945f772', 
        'venue1c6afb747a4987d0d0945f77221');
    INSERT INTO sd_lan_profile_regular_setting (id, tenant, tunnel_profile, guest_traffic_tunnel_profile, sd_lan_profile, venue) VALUES (
        'rs20e13cc150444ca1956dd68c8999f2',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'guest4d992694d5c9d7a2fcf2289a0bd',
        'afc284d992694d5c9d7a2fcf2289a0bd',
        'sdlan20ac6afb747a4987d0d0945f772', 
        'venue2c6afb747a4987d0d0945f77221');
    INSERT INTO sd_lan_profile_regular_setting (id, tenant, tunnel_profile, sd_lan_profile, venue) VALUES (
        'rs30e13cc150444ca1956dd68c8999f2',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'guest4d992694d5c9d7a2fcf2289a0bd',
        'sdlan30ac6afb747a4987d0d0945f772', 
        'venue3c6afb747a4987d0d0945f77221');
    INSERT INTO sd_lan_profile_regular_setting (id, tenant, tunnel_profile, sd_lan_profile, venue) VALUES (
        'rs40e13cc150444ca1956dd68c8999f2',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'afc284d992694d5c9d7a2fcf2289a0bd',
        'sdlan40ac6afb747a4987d0d0945f772', 
        'venue4c6afb747a4987d0d0945f77221');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        'network1',
        'DPSK',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'efd0e13cc150444ca1956dd68c8999f2',
        'network2',
        'DPSK',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'c9845a491cbc43d596ffcf3b5fca5566',
        'network3',
        'PSK',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'efd0e13cc150444ca1956dd68c897788',
        'network4',
        'AAA',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO sd_lan_profile_network_mapping(id, tenant, sd_lan_profile_regular_setting, network, is_guest_traffic_tunnel, forwarding_tunnel_profile) VALUES (
        'nm10e13cc150444ca1956dd68c897788',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'rs10e13cc150444ca1956dd68c8999f2',
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        false,
        'fwd4d992694d5c9d7a2fcf2289a0bd');
    INSERT INTO sd_lan_profile_network_mapping(id, tenant, sd_lan_profile_regular_setting, network, is_guest_traffic_tunnel, forwarding_tunnel_profile) VALUES (
        'nm20e13cc150444ca1956dd68c897788',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'rs10e13cc150444ca1956dd68c8999f2',
        'efd0e13cc150444ca1956dd68c8999f2',
        false,
        'fwd4d992694d5c9d7a2fcf2289a0bd');
    INSERT INTO sd_lan_profile_network_mapping(id, tenant, sd_lan_profile_regular_setting, network, is_guest_traffic_tunnel) VALUES (
        'nm30e13cc150444ca1956dd68c897788',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'rs20e13cc150444ca1956dd68c8999f2',
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        false);
    INSERT INTO sd_lan_profile_network_mapping(id, tenant, sd_lan_profile_regular_setting, network, is_guest_traffic_tunnel) VALUES (
        'nm40e13cc150444ca1956dd68c897788',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'rs20e13cc150444ca1956dd68c8999f2',
        'c9845a491cbc43d596ffcf3b5fca5566',
        false);
    INSERT INTO sd_lan_profile_network_mapping(id, tenant, sd_lan_profile_regular_setting, network, is_guest_traffic_tunnel) VALUES (
        'nm50e13cc150444ca1956dd68c897788',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'rs20e13cc150444ca1956dd68c8999f2',
        'efd0e13cc150444ca1956dd68c897788',
        false);
    INSERT INTO sd_lan_profile_network_mapping(id, tenant, sd_lan_profile_regular_setting, network, is_guest_traffic_tunnel) VALUES (
        'nm60e13cc150444ca1956dd68c897788',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'rs30e13cc150444ca1956dd68c8999f2',
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        false);
    INSERT INTO sd_lan_profile_network_mapping(id, tenant, sd_lan_profile_regular_setting, network, is_guest_traffic_tunnel) VALUES (
        'nm70e13cc150444ca1956dd68c897788',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'rs40e13cc150444ca1956dd68c8999f2',
        'efd0e13cc150444ca1956dd68c8999f2',
        false);
    INSERT INTO sd_lan_profile_network_mapping(id, tenant, sd_lan_profile_regular_setting, network) VALUES (
        'nm80e13cc150444ca1956dd68c897788',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'rs40e13cc150444ca1956dd68c8999f2',
        'c9845a491cbc43d596ffcf3b5fca8c4f');
    INSERT INTO sd_lan_profile_network_mapping(id, tenant, sd_lan_profile_regular_setting, network, is_guest_traffic_tunnel) VALUES (
        'nm90e13cc150444ca1956dd68c897788',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'rs40e13cc150444ca1956dd68c8999f2',
        'efd0e13cc150444ca1956dd68c897788',
        true);
    """)
class SdLanProfileNetworkMappingRepositoryTest {

  private static final String SD_LAN_1_ID = "sdlan10ac6afb747a4987d0d0945f772";
  private static final String SD_LAN_2_ID = "sdlan20ac6afb747a4987d0d0945f772";
  private static final String SD_LAN_3_ID = "sdlan30ac6afb747a4987d0d0945f772";
  private static final String SD_LAN_4_ID = "sdlan40ac6afb747a4987d0d0945f772";
  private static final String REGULAR_SETTING_1_ID = "rs10e13cc150444ca1956dd68c8999f2";
  private static final String REGULAR_SETTING_2_ID = "rs20e13cc150444ca1956dd68c8999f2";
  private static final String REGULAR_SETTING_3_ID = "rs30e13cc150444ca1956dd68c8999f2";
  private static final String REGULAR_SETTING_4_ID = "rs40e13cc150444ca1956dd68c8999f2";
  private static final String VENUE_ID_1 = "venue1c6afb747a4987d0d0945f77221";
  private static final String VENUE_ID_2 = "venue2c6afb747a4987d0d0945f77221";
  private static final String VENUE_ID_3 = "venue3c6afb747a4987d0d0945f77221";
  private static final String VENUE_ID_4 = "venue4c6afb747a4987d0d0945f77221";
  private static final String NETWORK_ID_1 = "c9845a491cbc43d596ffcf3b5fca8c4f";
  private static final String NETWORK_ID_2 = "efd0e13cc150444ca1956dd68c8999f2";
  private static final String NETWORK_ID_3 = "c9845a491cbc43d596ffcf3b5fca5566";
  private static final String NETWORK_ID_4 = "efd0e13cc150444ca1956dd68c897788";
  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String TUNNEL_PROFILE_ID_1 = "afc284d992694d5c9d7a2fcf2289a0bd";
  private static final String TUNNEL_PROFILE_ID_2 = "qwert4d992694d5c9d7a2fcf2289a0bd";
  private static final String TUNNEL_PROFILE_ID_3 = "guest4d992694d5c9d7a2fcf2289a0bd";
  private static final String FWD_TUNNEL_PROFILE_ID_4 = "fwd4d992694d5c9d7a2fcf2289a0bd";

  @Autowired
  private SdLanProfileNetworkMappingRepository target;

  @Test
  void findByNetworkIdAndSdLanProfileRegularSettingVenueIdTest() {
    assertThatRuntimeException().isThrownBy(
            () -> target.findByNetworkIdAndSdLanProfileRegularSettingVenueId(NETWORK_ID_1, VENUE_ID_1))
        .withRootCauseExactlyInstanceOf(IllegalAccessException.class);

    TxCtxUtils.allowCrossingTenantQuery(SdLanProfileNetworkMapping.class);

    assertThat(target.findByNetworkIdAndSdLanProfileRegularSettingVenueId(NETWORK_ID_1, VENUE_ID_1))
        .isPresent().hasValueSatisfying(networkMapping -> {
          assertThat(networkMapping.getId()).isEqualTo("nm10e13cc150444ca1956dd68c897788");
          assertThat(networkMapping.getNetwork().getId()).isEqualTo(NETWORK_ID_1);
          assertThat(networkMapping.getSdLanProfileRegularSetting().getId()).isEqualTo(REGULAR_SETTING_1_ID);
          assertThat(networkMapping.getSdLanProfileRegularSetting().getVenue().getId()).isEqualTo(VENUE_ID_1);
        });
  }

  @Test
  void findByNetworkIdAndSdLanProfileRegularSettingVenueIdInTest() {
    assertThatRuntimeException().isThrownBy(
            () -> target.findByNetworkIdAndSdLanProfileRegularSettingVenueIdIn(NETWORK_ID_1, List.of(VENUE_ID_1, VENUE_ID_2)))
        .withRootCauseExactlyInstanceOf(IllegalAccessException.class);

    TxCtxUtils.allowCrossingTenantQuery(SdLanProfileNetworkMapping.class);

    assertThat(target.findByNetworkIdAndSdLanProfileRegularSettingVenueIdIn(NETWORK_ID_1, List.of(VENUE_ID_1, VENUE_ID_2)))
        .isNotEmpty()
        .hasSize(2)
        .extracting("id", "network.id", "sdLanProfileRegularSetting.id", "sdLanProfileRegularSetting.venue.id")
        .containsExactlyInAnyOrder(
            tuple("nm10e13cc150444ca1956dd68c897788", NETWORK_ID_1, REGULAR_SETTING_1_ID, VENUE_ID_1),
            tuple("nm30e13cc150444ca1956dd68c897788", NETWORK_ID_1, REGULAR_SETTING_2_ID, VENUE_ID_2));
  }

  @Test
  void findByTenantIdAndSdLanProfileRegularSettingIdInTest() {
    assertThat(target.findByTenantIdAndSdLanProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_1_ID)))
        .isNotEmpty()
        .hasSize(2);
    assertThat(target.findByTenantIdAndSdLanProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_2_ID)))
        .isNotEmpty()
        .hasSize(3);
    assertThat(target.findByTenantIdAndSdLanProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_3_ID)))
        .isNotEmpty()
        .hasSize(1);
    assertThat(target.findByTenantIdAndSdLanProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_4_ID)))
        .isNotEmpty()
        .hasSize(3);
    assertThat(target.findByTenantIdAndSdLanProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_1_ID, REGULAR_SETTING_2_ID, REGULAR_SETTING_3_ID, REGULAR_SETTING_4_ID)))
        .isNotEmpty()
        .hasSize(9);
    assertThat(target.findByTenantIdAndSdLanProfileRegularSettingIdIn(TENANT_ID, List.of("123456")))
        .isNullOrEmpty();
  }

  @Test
  void findByTenantIdAndSdLanProfileRegularSettingIdInAndIsGuestTrafficTunnelTest() {
    assertThat(target.findByTenantIdAndSdLanProfileRegularSettingIdInAndIsGuestTrafficTunnel(
        TENANT_ID, List.of(REGULAR_SETTING_4_ID), false))
        .isNotEmpty()
        .hasSize(1);
    assertThat(target.findByTenantIdAndSdLanProfileRegularSettingIdInAndIsGuestTrafficTunnel(
        TENANT_ID, List.of(REGULAR_SETTING_4_ID), true))
        .isNotEmpty()
        .hasSize(1);
    assertThat(target.findByTenantIdAndSdLanProfileRegularSettingIdInAndIsGuestTrafficTunnel(
        TENANT_ID, List.of(REGULAR_SETTING_4_ID), null))
        .isNotEmpty()
        .hasSize(1);
  }

  @Test
  void findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueIdTest() {
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_1, VENUE_ID_1))
        .isNotNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_2, VENUE_ID_1))
        .isNotNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_3, VENUE_ID_1))
        .isNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_4, VENUE_ID_1))
        .isNull();

    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_1, VENUE_ID_2))
        .isNotNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_2, VENUE_ID_2))
        .isNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_3, VENUE_ID_2))
        .isNotNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_4, VENUE_ID_2))
        .isNotNull();

    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_1, VENUE_ID_3))
        .isNotNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_2, VENUE_ID_3))
        .isNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_3, VENUE_ID_3))
        .isNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_4, VENUE_ID_3))
        .isNull();

    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_1, VENUE_ID_4))
        .isNotNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_2, VENUE_ID_4))
        .isNotNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_3, VENUE_ID_4))
        .isNull();
    assertThat(target.findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(TENANT_ID, NETWORK_ID_4, VENUE_ID_4))
        .isNotNull();
  }

  @Test
  void findByTenantIdAndNetworkIdInAndSdLanProfileRegularSettingVenueIdTest() {
    assertThat(target.findByTenantIdAndNetworkIdInAndSdLanProfileRegularSettingVenueId(TENANT_ID,
        List.of(NETWORK_ID_1, NETWORK_ID_2, NETWORK_ID_3, NETWORK_ID_4), VENUE_ID_1))
        .isNotEmpty()
        .hasSize(2)
        .extracting("network.id")
        .containsExactlyInAnyOrder(NETWORK_ID_1, NETWORK_ID_2);

    assertThat(target.findByTenantIdAndNetworkIdInAndSdLanProfileRegularSettingVenueId(TENANT_ID,
        List.of(NETWORK_ID_1, NETWORK_ID_2, NETWORK_ID_3, NETWORK_ID_4), VENUE_ID_2))
        .isNotEmpty()
        .hasSize(3)
        .extracting("network.id")
        .containsExactlyInAnyOrder(NETWORK_ID_1, NETWORK_ID_3, NETWORK_ID_4);

    assertThat(target.findByTenantIdAndNetworkIdInAndSdLanProfileRegularSettingVenueId(TENANT_ID,
        List.of(NETWORK_ID_1, NETWORK_ID_2, NETWORK_ID_3, NETWORK_ID_4), VENUE_ID_3))
        .isNotEmpty()
        .hasSize(1)
        .extracting("network.id")
        .containsExactly(NETWORK_ID_1);

    assertThat(target.findByTenantIdAndNetworkIdInAndSdLanProfileRegularSettingVenueId(TENANT_ID,
        List.of(NETWORK_ID_1, NETWORK_ID_2, NETWORK_ID_3, NETWORK_ID_4), VENUE_ID_4))
        .isNotEmpty()
        .hasSize(3)
        .extracting("network.id")
        .containsExactlyInAnyOrder(NETWORK_ID_1, NETWORK_ID_2, NETWORK_ID_4);
  }

  @Test
  void findByTenantIdAndNetworkIdAtVenueIdInTest() {
    assertThat(target.findByTenantIdAndSdLanProfileIdAndNetworkIdAtVenueIdIn(TENANT_ID, SD_LAN_1_ID,
        List.of(
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_1),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_2),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_4),
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_1),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_3),
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_3), // NOT EXIST
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_4),
            formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_2),
            formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_4), // NOT EXIST
            formattedNetworkIdAtVenueId(NETWORK_ID_4, VENUE_ID_2))))
        .hasSize(2)
        .extracting(nm -> nm.getNetwork().getId(),
            nm -> nm.getSdLanProfileRegularSetting().getVenue().getId())
        .containsExactlyInAnyOrder(
            tuple(NETWORK_ID_1, VENUE_ID_1),
            tuple(NETWORK_ID_2, VENUE_ID_1));

    assertThat(target.findByTenantIdAndSdLanProfileIdAndNetworkIdAtVenueIdIn(TENANT_ID, SD_LAN_2_ID,
        List.of(
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_1),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_2),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_4),
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_1),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_3),
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_3), // NOT EXIST
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_4),
            formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_2),
            formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_4), // NOT EXIST
            formattedNetworkIdAtVenueId(NETWORK_ID_4, VENUE_ID_2))))
        .hasSize(3)
        .extracting(nm -> nm.getNetwork().getId(),
            nm -> nm.getSdLanProfileRegularSetting().getVenue().getId())
        .containsExactlyInAnyOrder(
            tuple(NETWORK_ID_1, VENUE_ID_2),
            tuple(NETWORK_ID_3, VENUE_ID_2),
            tuple(NETWORK_ID_4, VENUE_ID_2));

    assertThat(target.findByTenantIdAndSdLanProfileIdAndNetworkIdAtVenueIdIn(TENANT_ID, SD_LAN_3_ID,
        List.of(
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_1),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_2),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_4),
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_1),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_3),
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_3), // NOT EXIST
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_4),
            formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_2),
            formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_4), // NOT EXIST
            formattedNetworkIdAtVenueId(NETWORK_ID_4, VENUE_ID_2))))
        .hasSize(1)
        .extracting(nm -> nm.getNetwork().getId(),
            nm -> nm.getSdLanProfileRegularSetting().getVenue().getId())
        .containsExactlyInAnyOrder(
            tuple(NETWORK_ID_1, VENUE_ID_3));

    assertThat(target.findByTenantIdAndSdLanProfileIdAndNetworkIdAtVenueIdIn(TENANT_ID, SD_LAN_4_ID,
        List.of(
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_1),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_2),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_4),
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_1),
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_3),
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_3), // NOT EXIST
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_4),
            formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_2),
            formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_4), // NOT EXIST
            formattedNetworkIdAtVenueId(NETWORK_ID_4, VENUE_ID_2))))
        .hasSize(2)
        .extracting(nm -> nm.getNetwork().getId(),
            nm -> nm.getSdLanProfileRegularSetting().getVenue().getId())
        .containsExactlyInAnyOrder(
            tuple(NETWORK_ID_1, VENUE_ID_4),
            tuple(NETWORK_ID_2, VENUE_ID_4));
  }

  @Test
  void existsByTenantIdAndSdLanProfileRegularSettingTunnelProfileIdTest() {
    assertThat(target.existsByTenantIdAndSdLanProfileRegularSettingTunnelProfileId(TENANT_ID,
        TUNNEL_PROFILE_ID_1)).isTrue();
    assertThat(target.existsByTenantIdAndSdLanProfileRegularSettingTunnelProfileId(TENANT_ID,
        TUNNEL_PROFILE_ID_2)).isFalse();
    assertThat(target.existsByTenantIdAndSdLanProfileRegularSettingTunnelProfileId(TENANT_ID,
        TUNNEL_PROFILE_ID_3)).isTrue();
  }

  @Test
  void findByTenantIdAndForwardingTunnelProfileIdTest() {
    assertThat(target.findByTenantIdAndForwardingTunnelProfileId(TENANT_ID, TUNNEL_PROFILE_ID_1))
            .isEmpty();
    assertThat(target.findByTenantIdAndForwardingTunnelProfileId(TENANT_ID, FWD_TUNNEL_PROFILE_ID_4))
        .isNotEmpty()
        .hasSize(2);
  }
}
