package com.ruckus.cloud.wifi.integration.devicepolicy;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeDeactivateDevicePolicyOnWifiNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeActivateDevicePolicyOnWifiNetworkMessage {

    private String networkId;
    private String devicePolicyId;

    @BeforeEach
    void beforeEach(Tenant tenant, Network network, DevicePolicy devicePolicy) {
      networkId = network.getId();
      devicePolicyId = devicePolicy.getId();
      network.getWlan().getAdvancedCustomization().setDevicePolicy(devicePolicy);
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkId", networkId)
          .addPathVariable("devicePolicyId", devicePolicyId);
    }

    @Test
    void givenAccessControlProfileExists(Tenant tenant) {
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.DEACTIVATE_DEVICE_POLICY_ON_WIFI_NETWORK,
          randomName(),
          requestParams(),
          "");

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.MOD)
          .matches(o -> o.getId().equals(devicePolicyId))
          .extracting(
              Operations::getDocMap,
              InstanceOfAssertFactories.map(String.class, Value.class))
          .extractingByKey(EsConstants.Key.NETWORK_IDS)
          .isNotNull()
          .extracting(Value::getListValue)
          .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
          .extracting(Value::getStringValue)
          .doesNotContain(networkId);

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(
              a -> a.getStep().equals(ApiFlowNames.DEACTIVATE_DEVICE_POLICY_ON_WIFI_NETWORK));
    }
  }
}
