package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomNetwork;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.mock;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.BaseEntityFieldNames;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.repository.AccessControlProfileRepository;
import com.ruckus.cloud.wifi.repository.WlanRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.api.BDDAssertions;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class L3AclPolicyCmnCfgOperationBuilderTest {
  @MockBean private BaseEntityFieldNames baseEntityFieldNames;
  @MockBean private WlanRepository wlanRepository;
  @MockBean private AccessControlProfileRepository accessControlProfileRepository;

  @SpyBean private L3AclPolicyCmnCfgOperationBuilder unit;

  @Test
  void whenEntityClass() {
    BDDAssertions.then(unit.entityClass()).isNotNull().isEqualTo(L3AclPolicy.class);
  }

  @Nested
  class WhenConfig {
    @Mock private EntityAction action;

    private List<Network> networks;
    private List<AccessControlProfile> accessControlProfiles;

    @BeforeEach
    void beforeEach(Tenant tenant) {
      networks = Stream.generate(() -> randomNetwork(tenant)).limit(5).toList();
      accessControlProfiles =
          Stream.generate(AccessControlProfileTestFixture::randomAccessControlProfile)
              .limit(5)
              .toList();
    }

    @Test
    void givenEmptyDescription(Tenant tenant, L3AclPolicy l3AclPolicy) {
      final var builder = Operations.newBuilder();
      l3AclPolicy.setDescription(null);
      willReturn(networks)
          .given(wlanRepository)
          .findNetworkByTenantIdAndL3AclPolicyIdIn(anyString(), anyList());
      willReturn(accessControlProfiles)
          .given(accessControlProfileRepository)
          .findByTenantIdAndL3AclPolicyIdIn(anyString(), anyList());

      unit.config(builder, l3AclPolicy, action);

      then(action).shouldHaveNoInteractions();
      then(wlanRepository)
          .should()
          .findNetworkByTenantIdAndL3AclPolicyIdIn(
              eq(tenant.getId()), eq(List.of(l3AclPolicy.getId())));
      then(accessControlProfileRepository)
          .should()
          .findByTenantIdAndL3AclPolicyIdIn(eq(tenant.getId()), eq(List.of(l3AclPolicy.getId())));

      validateConfig(builder, l3AclPolicy, "");
    }

    @Test
    void givenNonEmptyDescription(Tenant tenant, L3AclPolicy l3AclPolicy) {
      final var builder = Operations.newBuilder();
      final var description = RandomStringUtils.randomAlphabetic(20);
      l3AclPolicy.setDescription(description);
      willReturn(networks)
          .given(wlanRepository)
          .findNetworkByTenantIdAndL3AclPolicyIdIn(anyString(), anyList());
      willReturn(accessControlProfiles)
          .given(accessControlProfileRepository)
          .findByTenantIdAndL3AclPolicyIdIn(anyString(), anyList());

      unit.config(builder, l3AclPolicy, action);

      then(action).shouldHaveNoInteractions();
      then(wlanRepository)
          .should()
          .findNetworkByTenantIdAndL3AclPolicyIdIn(
              eq(tenant.getId()), eq(List.of(l3AclPolicy.getId())));
      then(accessControlProfileRepository)
          .should()
          .findByTenantIdAndL3AclPolicyIdIn(eq(tenant.getId()), eq(List.of(l3AclPolicy.getId())));

      validateConfig(builder, l3AclPolicy, description);
    }

    void validateConfig(Operations.Builder builder, L3AclPolicy l3AclPolicy, String description) {
      assertThat(builder)
          .extracting(
              Operations.Builder::getDocMap,
              InstanceOfAssertFactories.map(String.class, Value.class))
          .containsEntry(Key.ID, ValueUtils.stringValue(l3AclPolicy.getId()))
          .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(l3AclPolicy.getTenant().getId()))
          .containsEntry(Key.NAME, ValueUtils.stringValue(l3AclPolicy.getName()))
          .containsEntry(Key.TYPE, EsConstants.Value.TYPE_L3ACL_POLICY)
          .containsEntry(Key.DESCRIPTION, ValueUtils.stringValue(description))
          .containsEntry(Key.RULES, ValueUtils.numberValue(l3AclPolicy.getL3Rules().size()))
          .containsEntry(
              Key.ACCESS_CONTROL_PROFILE_IDS,
              ValueUtils.listValue(
                  accessControlProfiles.stream()
                      .map(AbstractBaseEntity::getId)
                      .map(ValueUtils::stringValue)
                      .toList()))
          .containsEntry(
              Key.WIFI_NETWORK_IDS,
              ValueUtils.listValue(
                  networks.stream()
                      .map(AbstractBaseEntity::getId)
                      .map(ValueUtils::stringValue)
                      .toList()))
          .matches(m -> m.get(Key.WIFI_NETWORK_IDS).equals(m.get(Key.NETWORK_IDS)));
    }
  }

  @Test
  void whenIndex() {
    BDDAssertions.then(unit.index())
        .isNotNull()
        .isEqualTo(EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME);
  }

  @Nested
  class WhenHasModification {
    @Test
    void givenTaggedDirty() {
      BDDAssertions.then(unit.hasModification(mock(EntityAction.class), Set.of(), true)).isTrue();
    }

    @Nested
    class GivenNotTaggedDirty {
      @Test
      void givenActionAdd() {
        BDDAssertions.then(unit.hasModification(EntityAction.ADD, Set.of(), false)).isTrue();
      }

      @Test
      void givenActionDelete() {
        BDDAssertions.then(unit.hasModification(EntityAction.DELETE, Set.of(), false)).isTrue();
      }

      @Test
      void givenActionSync() {
        BDDAssertions.then(unit.hasModification(EntityAction.SYNC, Set.of(), false)).isTrue();
      }

      @Nested
      class GivenActionModify {
        private final Set<String> modifiedProperties =
            Stream.generate(() -> RandomStringUtils.randomAlphanumeric(10))
                .limit(5)
                .collect(Collectors.toSet());

        @BeforeEach
        void beforeEach() {
          willReturn(AbstractBaseEntity.Fields.UPDATEDDATE)
              .given(baseEntityFieldNames)
              .getUpdatedDateFieldName();
        }

        @Test
        void givenNoPropertyMatch() {
          BDDAssertions.then(unit.hasModification(EntityAction.MODIFY, modifiedProperties, false))
              .isFalse();
        }

        @Test
        void givenNameModified() {
          modifiedProperties.add(L3AclPolicy.Fields.NAME);

          BDDAssertions.then(unit.hasModification(EntityAction.MODIFY, modifiedProperties, false))
              .isTrue();
        }

        @Test
        void givenDescriptionModified() {
          modifiedProperties.add(L3AclPolicy.Fields.DESCRIPTION);

          BDDAssertions.then(unit.hasModification(EntityAction.MODIFY, modifiedProperties, false))
              .isTrue();
        }

        @Test
        void givenL3RulesModified() {
          modifiedProperties.add(L3AclPolicy.Fields.L3RULES);

          BDDAssertions.then(unit.hasModification(EntityAction.MODIFY, modifiedProperties, false))
              .isTrue();
        }
      }
    }
  }
}
