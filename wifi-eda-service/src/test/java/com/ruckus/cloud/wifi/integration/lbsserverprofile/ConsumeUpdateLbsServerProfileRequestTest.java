package com.ruckus.cloud.wifi.integration.lbsserverprofile;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@WifiIntegrationTest
public class ConsumeUpdateLbsServerProfileRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeUpdateLbsServerProfileMessage {

    private LbsServerProfile savedLbsServerProfile;
    private LbsServerProfile savedLbsServerProfile2;

    @BeforeEach
    void givenLbsServerProfile(final Tenant tenant) {
      savedLbsServerProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.lbsServerProfile().generate();
      repositoryUtil.createOrUpdate(savedLbsServerProfile, tenant.getId(), randomTxId());
      savedLbsServerProfile2 = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.lbsServerProfile()
          .setLbsServerVenueName(always("vspot")).setServerAddress(always("***********")).generate();
      repositoryUtil.createOrUpdate(savedLbsServerProfile2, tenant.getId(), randomTxId());
    }

    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("lbsServerProfileId", savedLbsServerProfile.getId());
    }

    @Test
    void thenUpdateAndSendingMessages(Tenant tenant) {
      final var lbsServerProfile = Generators.lbsServerProfile().setId(always(savedLbsServerProfile.getId())).generate();

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.UPDATE_LBS_SERVER_PROFILE,
          randomName(),
          requestParams(),
          lbsServerProfile);

      assertThat(repositoryUtil.find(LbsServerProfile.class, lbsServerProfile.getId(), tenant.getId()))
          .isNotNull()
          .matches(p -> p.getName().equals(lbsServerProfile.getName()))
          .matches(p -> p.getLbsServerVenueName().equals(lbsServerProfile.getLbsServerVenueName()))
          .matches(p -> p.getServerAddress().equals(lbsServerProfile.getServerAddress()))
          .matches(p -> Objects.equals(p.getServerPort(), lbsServerProfile.getServerPort()))
          .matches(p -> p.getPassword().equals(lbsServerProfile.getPassword()));

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(1)
          .first()
          .matches(o -> o.getAction() == Action.MODIFY)
          .matches(o -> o.getId().equals(lbsServerProfile.getId()));

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.MOD)
          .matches(o -> o.getId().equals(lbsServerProfile.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_LBS_SERVER_PROFILE));
    }

    @Test
    void thenUpdateAndSendingMessages_throwsDuplicateException(Tenant tenant) {
      final var lbsServerProfile = Generators.lbsServerProfile().setId(always(savedLbsServerProfile.getId()))
          .setLbsServerVenueName(always("vspot")).setServerAddress(always("***********")).generate();

      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.UPDATE_LBS_SERVER_PROFILE,
                  randomName(),
                  requestParams(),
                  lbsServerProfile))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class)
          .extracting(e -> (InvalidPropertyValueException) e)
          .extracting(CommonException::getErrorCode)
          .isEqualTo(Errors.WIFI_10547);

      // Not changed
      assertThat(repositoryUtil.find(LbsServerProfile.class, lbsServerProfile.getId(), tenant.getId()))
          .isNotNull()
          .matches(p -> p.getName().equals(savedLbsServerProfile.getName()))
          .matches(p -> p.getLbsServerVenueName().equals(savedLbsServerProfile.getLbsServerVenueName()))
          .matches(p -> p.getServerAddress().equals(savedLbsServerProfile.getServerAddress()))
          .matches(p -> Objects.equals(p.getServerPort(), savedLbsServerProfile.getServerPort()))
          .matches(p -> p.getPassword().equals(savedLbsServerProfile.getPassword()));

      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_LBS_SERVER_PROFILE));
    }
  }

}
