package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('35sdg9f79307415fa9e4c88a1819f0fg');
       
    INSERT INTO venue (id, tenant) VALUES ('4a1df860e61c4c9ffffd5472a0576c4c', '35sdg9f79307415fa9e4c88a1819f0fg');
       
    INSERT INTO ap_group (id, is_default, name, tenant, venue) VALUES
       ('63415fa13e444d62ac64af9ab4ec5796', true, '', '35sdg9f79307415fa9e4c88a1819f0fg', '4a1df860e61c4c9ffffd5472a0576c4c');
         
    INSERT INTO network (type, id, name, tenant) VALUES
        ('OPEN', '39fc50a4bf0b43bcbf61e018f0ea264f', 'OPEN NETWORK', '35sdg9f79307415fa9e4c88a1819f0fg');
        
    INSERT INTO network_venue (id, tenant, network, venue, all_ap_groups_radio, is_all_ap_groups) VALUES
        ('4c71118d6ef549dbb1c3067d9c99a388', '35sdg9f79307415fa9e4c88a1819f0fg', '39fc50a4bf0b43bcbf61e018f0ea264f', '4a1df860e61c4c9ffffd5472a0576c4c', 'Both', true);
       
    INSERT INTO network_ap_group (id, tenant, ap_group, network_venue) VALUES
        ('5cae600415e9fffe8996b694856bfc91', '35sdg9f79307415fa9e4c88a1819f0fg', '63415fa13e444d62ac64af9ab4ec5796', '4c71118d6ef549dbb1c3067d9c99a388');
        
    INSERT INTO network_ap_group_radio (id, radio, network_ap_group, tenant, created_date) VALUES
        ('1a72c0e5e45f4b18kfff7f524a265eea', 'Upper_5_GHz', '5cae600415e9fffe8996b694856bfc91','35sdg9f79307415fa9e4c88a1819f0fg', '2024-02-27 08:59:29'),
        ('1a72c0e1111f4b18b6b67f524a265eeb', 'Lower_5_GHz', '5cae600415e9fffe8996b694856bfc91','35sdg9f79307415fa9e4c88a1819f0fg', '2024-02-27 08:59:29');
    """)
public class NetworkApGroupRadioRepositoryTest {

  @Autowired
  NetworkApGroupRadioRepository repository;

  @Test
  void testFindByNetworkIdAndVenueIdAndTenantId() {
    List<NetworkApGroupRadio> radios = repository.findByNetworkIdAndVenueIdAndTenantId("39fc50a4bf0b43bcbf61e018f0ea264f", "4a1df860e61c4c9ffffd5472a0576c4c", "35sdg9f79307415fa9e4c88a1819f0fg");

    assertThat(radios).hasSize(2)
        .extracting(AbstractBaseEntity::getId)
        .contains("1a72c0e5e45f4b18kfff7f524a265eea", "1a72c0e1111f4b18b6b67f524a265eeb");
  }

}
