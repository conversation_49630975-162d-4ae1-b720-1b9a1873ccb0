package com.ruckus.cloud.wifi.integration.apgroup;

import static com.ruckus.cloud.wifi.service.impl.ExtendedApGroupServiceCtrlImpl.DEFAULT_AP_GROUP_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Condition;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("ApGroupTest")
@WifiIntegrationTest
public class ConsumeAddVenueApGroupRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  protected MessageCaptors messageCaptors;


  @Nested
  class whenConsumeAddVenueApGroupRequest {

    @Test
    void thenSaveApGroup_withoutAps(Venue venue, @DefaultApGroup ApGroup defaultApGroup) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupName = randomName();
      final var tenantId = defaultApGroup.getTenant().getId();

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setName(apGroupName);

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", venue.getId());

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.ADD_VENUE_AP_GROUP, userName,
          requestParams, payload);

      validateResult(tenantId, requestId, venue.getId(), defaultApGroup.getId(), payload, CfgAction.ADD_VENUE_AP_GROUP);
    }

    @Test
    void thenSaveApGroup_withAp(Venue venue, @DefaultApGroup ApGroup defaultApGroup, Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupName = randomName();
      final var tenantId = defaultApGroup.getTenant().getId();

      defaultApGroup.setAps(List.of(ap));
      repositoryUtil.createOrUpdate(defaultApGroup, tenantId, randomTxId());

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setName(apGroupName);
      payload.setApSerialNumbers(List.of(ap.getId()));

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", venue.getId());

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.ADD_VENUE_AP_GROUP, userName,
          requestParams, payload);

      validateResult(tenantId, requestId, venue.getId(), defaultApGroup.getId(), payload, CfgAction.ADD_VENUE_AP_GROUP);
    }

    @Test
    void thenSaveApGroup_withNetworkApGroupAndRadios(Venue venue,
        @DefaultApGroup ApGroup defaultApGroup,
        Ap ap, NetworkVenue networkVenue, NetworkApGroup networkApGroup,
        NetworkApGroupRadio networkApGroupRadio) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupName = randomName();
      final var tenantId = defaultApGroup.getTenant().getId();

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setName(apGroupName);
      payload.setApSerialNumbers(List.of(ap.getId()));

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", venue.getId());

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.ADD_VENUE_AP_GROUP, userName,
          requestParams, payload);

      validateResult(tenantId, requestId, venue.getId(), networkVenue.getId(), networkVenue.getNetwork().getId(),
          defaultApGroup.getId(), payload, CfgAction.ADD_VENUE_AP_GROUP);
      validateNetworkApGroupAndRadios(tenantId, requestId, payload, CfgAction.ADD_VENUE_AP_GROUP);
    }
  }

  void validateResult(String tenantId, String requestId, String venueId,
      String defaultApGroupId, VenueApGroup payload, CfgAction action) {
    validateResult(tenantId, requestId, venueId, null, null, defaultApGroupId, payload, action);
  }

  void validateResult(String tenantId, String requestId, String venueId, String networkVenueId,
      String networkId, String defaultApGroupId, VenueApGroup payload, CfgAction action) {
    final var txChanges = revisionService.changes(requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(action.key()));
    assertThat(txChanges.getNewEntities())
        .isNotEmpty().hasSize(networkVenueId == null ? 1 : 6)
        .extracting(TxEntity::getEntity)
        .haveExactly(1, new Condition<>(ApGroup.class::isInstance,
            "should be instance of ApGroup"))
        .haveExactly(networkVenueId == null ? 0 : 1, new Condition<>(NetworkApGroup.class::isInstance,
            "should be instance of NetworkApGroup"))
        .haveExactly(networkVenueId == null ? 0 : 4, new Condition<>(NetworkApGroupRadio.class::isInstance,
            "should be instance of NetworkApGroupRadio"));
    if (CollectionUtils.isNotEmpty(payload.getApSerialNumbers())) {
      assertThat(txChanges.getModifiedEntities().stream().filter(m -> m.getEntity() instanceof Ap)
          .toList())
          .isNotEmpty().hasSize(payload.getApSerialNumbers().size());
    }
    if (networkVenueId != null) {
      assertThat(txChanges.getModifiedEntities().stream()
          .filter(m -> m.getEntity() instanceof NetworkVenue).toList())
          .isNotEmpty().singleElement()
          .extracting(TxEntity::getEntity)
          .extracting(NetworkVenue.class::cast)
          .extracting(NetworkVenue::getId)
          .isEqualTo(networkVenueId);
    }
    assertThat(txChanges.getDeletedEntities()).isEmpty();

    assertThat(repositoryUtil.find(ApGroup.class, payload.getId()))
        .isNotNull()
        .satisfies(apGroup -> {
          assertThat(apGroup.getId()).isEqualTo(payload.getId());
          assertThat(apGroup.getName()).isEqualTo(payload.getName());
          assertThat(apGroup.getVenue())
              .isNotNull()
              .extracting(Venue::getId)
              .isEqualTo(venueId);
        });

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    // validate AP Group cmnCfgCollectorMessage
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
        .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
        .isNotEmpty().hasSize(CollectionUtils.isEmpty(payload.getApSerialNumbers()) ? 1 : 2)
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> payload.getId().equals(op.getId()))
              .isNotEmpty().singleElement()
              .satisfies(op -> {
                assertThat(op.getOpType()).isEqualTo(OpType.ADD);
                assertThat(op.getDocMap())
                    .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                    .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                    .containsEntry(Key.ID, ValueUtils.stringValue(payload.getId()))
                    .containsEntry(Key.NAME, ValueUtils.stringValue(payload.getName()))
                    .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                    .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                    .satisfies(docMap -> {
                      if (StringUtils.isNotEmpty(payload.getDescription())) {
                        assertThat(docMap)
                            .containsEntry(Key.DESCRIPTION,
                                ValueUtils.stringValue(payload.getDescription()));
                      }
                      if (networkVenueId != null) {
                        final var networkVenue = repositoryUtil.find(NetworkVenue.class,
                            networkVenueId);
                        assertThat(networkVenue).isNotNull();
                        assertThat(docMap.get(Key.WIFI_NETWORK_IDS))
                            .isNotNull()
                            .extracting(value -> value.getListValue().getValuesList(),
                                InstanceOfAssertFactories.list(
                                    com.ruckus.cloud.events.gpb.Value.class))
                            .isNotEmpty().singleElement()
                            .extracting(com.ruckus.cloud.events.gpb.Value::getStringValue)
                            .isEqualTo(networkVenue.getNetwork().getId());
                      }
                    });
              });
          if (CollectionUtils.isNotEmpty(payload.getApSerialNumbers())) {
            assertThat(ops)
                .filteredOn(op -> defaultApGroupId.equals(op.getId()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                      .containsEntry(Key.ID, ValueUtils.stringValue(defaultApGroupId))
                      .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_AP_GROUP_NAME.trim()))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                      .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(true));
                });
          }
        });

    // validate AP cmnCfgCollectorMessage
    if (CollectionUtils.isNotEmpty(payload.getApSerialNumbers())) {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
          .isNotEmpty().hasSize(payload.getApSerialNumbers().size())
          .allSatisfy(op -> {
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocOrThrow(Key.DEVICE_GROUP_ID))
                .isEqualTo(ValueUtils.stringValue(payload.getId()));
            assertThat(op.getDocOrThrow(Key.DEVICE_GROUP_NAME))
                .isEqualTo(ValueUtils.stringValue(payload.getName()));
          })
          .extracting(Operations::getId)
          .containsExactlyInAnyOrderElementsOf(payload.getApSerialNumbers());
    } else {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
          .isEmpty();
    }

    // validate Network cmnCfgCollectorMessage
    if (networkId != null) {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
          .isNotEmpty().hasSize(payload.getApSerialNumbers().size())
          .allSatisfy(op -> {
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocOrThrow(Key.ID))
                .isEqualTo(ValueUtils.stringValue(networkId));
          });
    } else {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
          .isEmpty();
    }

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(activityCfgChangeRespMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getStatus()).isEqualTo(Status.OK);
          assertThat(msg.getStep()).isEqualTo(ApiFlowNames.ADD_AP_GROUP);
          assertThat(msg.getEventDate()).isNotNull();
        });

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(activityImpactedMessage.getPayload())
        .satisfies(msg -> {
          if (CollectionUtils.isEmpty(payload.getApSerialNumbers())) {
            assertThat(msg.getDeviceIdsList()).isEmpty();
          } else {
            assertThat(msg.getDeviceIdsList()).containsAll(payload.getApSerialNumbers());
          }
          assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
        });
  }

  void validateNetworkApGroupAndRadios(String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup payload, CfgAction action) {

    final var revision = revisionService.changes(requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(action.key()));
    final var networkApGroupId = revision.getNewEntities().stream()
        .filter(e -> e.getEntity() instanceof NetworkApGroup).findFirst().get().getId();
    final var networkApGroup = repositoryUtil.find(NetworkApGroup.class, networkApGroupId);

    assertThat(networkApGroup)
        .isNotNull()
        .satisfies(nag -> assertThat(nag.getApGroup().getId()).isEqualTo(payload.getId()))
        .extracting(NetworkApGroup::getNetworkApGroupRadios,
            InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
        .isNotEmpty().hasSize(4)
        .extracting(NetworkApGroupRadio::getRadio)
        .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
            StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);
  }
}
