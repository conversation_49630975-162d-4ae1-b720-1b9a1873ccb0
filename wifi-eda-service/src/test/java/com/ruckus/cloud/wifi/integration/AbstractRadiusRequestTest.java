package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.RADIUS_SERVER_PROFILE_INDEX_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.eda.service.RadiusTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusNetworkDataQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SortOrderEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.ExtendedRadiusServiceCtrl;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import lombok.SneakyThrows;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractRadiusRequestTest extends AbstractRequestTest {

  protected static final String RADIUS_ID = "radiusId";
  protected static final String RADIUS_SERVER_PROFILE_TEMPLATE_ID = "radiusServerProfileTemplateId";
  protected static final String NETWORK_ID = "networkId";
  protected static final String NETWORK_TYPE = "networkType";
  protected static final String WIFI_NETWORK_ID = "wifiNetworkId";
  protected static final String WIFI_NETWORK_TEMPLATE_ID = "wifiNetworkTemplateId";
  protected static final String VENUE_ID = "venueId";

  @RegisterExtension
  protected final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  protected ExtendedRadiusServiceCtrl radiusServiceCtrl;

  @Autowired
  protected RadiusTemplateServiceCtrl radiusTemplateServiceCtrl;

  protected static void assertRadiusCommon(Radius expectedRadius, Radius actualRadius) {
    assertNotNull(actualRadius.getId());
    assertEquals(expectedRadius.getName(), actualRadius.getName());
    assertEquals(expectedRadius.getType(), actualRadius.getType());
    // Validate radius primary
    assertEquals(expectedRadius.getPrimary().getIp(), actualRadius.getPrimary().getIp());
    assertEquals(expectedRadius.getPrimary().getPort(), actualRadius.getPrimary().getPort());
    assertEquals(expectedRadius.getPrimary().getSharedSecret(),
        actualRadius.getPrimary().getSharedSecret());
    // Validate radius secondary
    if (expectedRadius.getSecondary() != null) {
      assertEquals(expectedRadius.getSecondary().getIp(), actualRadius.getSecondary().getIp());
      assertEquals(expectedRadius.getSecondary().getPort(), actualRadius.getSecondary().getPort());
      assertEquals(expectedRadius.getSecondary().getSharedSecret(),
          actualRadius.getSecondary().getSharedSecret());
    }
  }

  @SneakyThrows
  protected void assertRadiusNetworks(Radius radius, int radiusNetworksCount) {
    QueryRequest queryParams = new QueryRequest();
    queryParams.setPage(1);
    queryParams.setPageSize(100);
    queryParams.setSortField(NETWORK_ID);
    queryParams.setSortOrder(SortOrderEnum.ASC);
    RadiusNetworkDataQueryResponse result = radiusServiceCtrl.getRadiusNetworks(
        radius.getId(), queryParams);

    assertEquals(radiusNetworksCount, result.getTotalCount().intValue());
    assertEquals(radiusNetworksCount, result.getData().size());
  }

  protected void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  protected void assertRadiusIndexViewmodel(List<Operations> operations, OpType opType, Radius radius, int scope) {
    if (opType.equals(OpType.DEL)) {
      assertTrue(operations.stream()
          .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .filter(o -> radius.getId().equals(o.getId()))
          .filter(o -> o.getOpType() == opType)
          .findAny().isPresent());
    } else {
      assertTrue(operations.stream()
          .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .filter(o -> radius.getId().equals(o.getId()))
          .filter(o -> o.getOpType() == opType)
          .filter(o -> radius.getName()
              .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
          .filter(o -> radius.getIsTemplate()
              .equals(o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue()))
          .filter(
              o -> o.getDocMap().get(EsConstants.Key.NETWORK_IDS).getListValue().getValuesCount()
                  == scope)
          .findAny().isPresent());
    }
  }
}
