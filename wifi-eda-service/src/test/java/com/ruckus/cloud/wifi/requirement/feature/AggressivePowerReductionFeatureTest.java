package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.AP_TX_POWER_TOGGLE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.service.validator.ApCapabilityValidator;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class AggressivePowerReductionFeatureTest {

  @MockBean
  private ApCapabilityValidator apCapabilityValidator;

  @SpyBean
  private AggressivePowerReductionFeature unit;

  @Test
  @FeatureFlag(disable = {WIFI_R370_TOGGLE, AP_TX_POWER_TOGGLE})
  void givenR370Disable(Venue venue) {
    BDDAssertions.then(unit.test(venue)).isFalse();
  }

  @Test
  @FeatureFlag(enable = {WIFI_R370_TOGGLE, AP_TX_POWER_TOGGLE})
  void givenAggressiveTxPowerCompatibleForVenue(Venue venue) {
    when(apCapabilityValidator.isAggressiveTxPowerIncompatibleByVenue(
        any(Venue.class))).thenReturn(false);

    BDDAssertions.then(unit.test(venue)).isFalse();
  }

  @Test
  @FeatureFlag(enable = {WIFI_R370_TOGGLE, AP_TX_POWER_TOGGLE})
  void givenAggressiveTxPowerIncompatibleForVenue(Venue venue) {
    when(apCapabilityValidator.isAggressiveTxPowerIncompatibleByVenue(
        any(Venue.class))).thenReturn(true);

    BDDAssertions.then(unit.test(venue)).isTrue();
  }
}
