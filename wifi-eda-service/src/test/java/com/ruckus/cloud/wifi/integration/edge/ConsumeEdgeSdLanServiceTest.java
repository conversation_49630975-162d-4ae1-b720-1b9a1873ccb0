package com.ruckus.cloud.wifi.integration.edge;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.EDGE_DELEGATION_POC_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.EDGE_L2GRE_TOGGLE;
import static com.ruckus.cloud.wifi.integration.edge.ConsumeEdgeSdLanServiceTest.EdgeSdLanServiceContext.contextOf;
import static com.ruckus.cloud.wifi.integration.edge.ConsumeEdgeSdLanServiceTest.EdgeSdLanServiceContext.contextOfActivateGuestNetwork;
import static com.ruckus.cloud.wifi.integration.edge.ConsumeEdgeSdLanServiceTest.EdgeSdLanServiceContext.contextOfActivateGuestTunnel;
import static com.ruckus.cloud.wifi.integration.edge.ConsumeEdgeSdLanServiceTest.EdgeSdLanServiceContext.contextOfActivateNetwork;
import static com.ruckus.cloud.wifi.integration.edge.ConsumeEdgeSdLanServiceTest.EdgeSdLanServiceContext.contextOfDelete;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomNetworkTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.TenantTestFixture.randomTenant;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplate;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplateInstance;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static java.util.Objects.requireNonNull;
import static java.util.Optional.ofNullable;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.edge.protobuf.ChangedConnectResource;
import com.ruckus.cloud.edge.protobuf.EdgeSdLanService;
import com.ruckus.cloud.edge.protobuf.EdgeSdLanService.Action;
import com.ruckus.cloud.edge.protobuf.TunneledWlan;
import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.EdgeConnectResource;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.EdgeCfgChangeJob;
import com.ruckus.cloud.wifi.proto.EdgeDelegationEcTenantOperationJob.OperationCase;
import com.ruckus.cloud.wifi.proto.EdgeDelegationEcTenantOperationJob.SdLanOperation.PayloadCase;
import com.ruckus.cloud.wifi.proto.SignaturePackageJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TunnelProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("VxLanTunnelFeatureTest")
@WifiIntegrationTest
class ConsumeEdgeSdLanServiceTest {
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class CreateUpdateDeleteSdLanProfileWithNetworkTest {

    private Venue venue1;
    private Venue venue2;
    private Network network1;
    private Network network2;
    private Network network3;
    private Network network4;
    private Network network5;
    private Network network6;
    private Network network7;
    private TunnelProfile tunnelProfile1;
    private TunnelProfile tunnelProfile2;
    private TunnelProfile tunnelProfile3;

    @BeforeEach
    void givenVenuesAndNetworksAndTunnelProfilesPersistedInDb(
        Tenant tenant, Venue venue1, TunnelProfile tunnelProfile1) {

      this.venue1 = venue1;
      this.venue2 = repositoryUtil.createOrUpdate(VenueTestFixture.randomVenue(tenant));

      final var networks = createAndSaveOpenNetworks(tenant, 7);
      this.network1 = networks.get(0);
      this.network2 = networks.get(1);
      this.network3 = networks.get(2);
      this.network4 = networks.get(3);
      this.network5 = networks.get(4);
      this.network6 = networks.get(5);
      this.network7 = networks.get(6);
      createAndSaveNetworkVenues(tenant, venue1, List.of(network1, network2, network3, network5, network7));
      createAndSaveNetworkVenues(tenant, venue2, List.of(network3, network4, network6, network7));

      this.tunnelProfile1 = tunnelProfile1;
      this.tunnelProfile2 = repositoryUtil.createOrUpdate(
          TunnelProfileTestFixture.randomTunnelProfile(tenant,
              tp -> tp.setName(randomString().generate())));
      this.tunnelProfile3 = repositoryUtil.createOrUpdate(
          TunnelProfileTestFixture.randomTunnelProfile(tenant,
              tp -> tp.setName(randomString().generate())));
    }

    @FeatureFlag(disable = EDGE_L2GRE_TOGGLE)
    @Nested
    class GivenEdgeL2GreFeatureFlagDisabled {

      @Test
      void testCreateUpdateDeleteSdLanProfileWithNetwork(Tenant tenant) {
        TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue1).isGuestTunnelUtilized(true).build();
        TunneledWlan tunneledWlan2 = TunneledWlanContext.of(network2, venue1).isGuestTunnelUtilized(false).withFwdTunnelProfile(tunnelProfile3).build();
        TunneledWlan tunneledWlan3 = TunneledWlanContext.of(network3, venue1).isGuestTunnelUtilized(true).withFwdTunnelProfileId(randomId()).build();
        TunneledWlan tunneledWlan4 = TunneledWlanContext.of(network5, venue1).isGuestTunnelUtilized(false).build();
        TunneledWlan tunneledWlan5 = TunneledWlanContext.of(network7, venue1).isGuestTunnelUtilized(true).build();
        TunneledWlan tunneledWlan6 = TunneledWlanContext.of(network3, venue2).build();
        TunneledWlan tunneledWlan7 = TunneledWlanContext.of(network4, venue2).build();
        TunneledWlan tunneledWlan8 = TunneledWlanContext.of(network6, venue2).build();
        TunneledWlan tunneledWlan9 = TunneledWlanContext.of(network7, venue2).build();

        String sdLanProfileId = txCtxExtension.newRandomId();
        String connectResourceId = txCtxExtension.newRandomId();
        String createSdLanRequestId = randomTxId();

        ChangedConnectResource connectResource = ChangedConnectResource.newBuilder()
            .setConnectResourceId(connectResourceId)
            .setSdLanId(sdLanProfileId)
            .build();
        messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId,
            contextOf(tenant, sdLanProfileId, tunnelProfile1).with(connectResource).build(Action.CREATE_SD_LAN));
        assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
            .isNotNull()
            .extracting(SdLanProfile::getSdLanProfileRegularSettings,
                InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
            .isNotEmpty()
            .singleElement()
            .extracting(SdLanProfileRegularSetting::getVenue)
            .isNull();

        assertThat(repositoryUtil.find(EdgeConnectResource.class, connectResourceId))
            .isNotNull()
            .satisfies(cr -> {
              assertThat(cr.getId()).isEqualTo(connectResourceId);
              assertThat(cr.getSdLanProfile().getId()).isEqualTo(sdLanProfileId);
            });

        String bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan4).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan5).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan6).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan7).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan8).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan9).build(Action.ACTIVATE_NETWORK));

        // Verify the isGuestTrafficTunnel value of SdLanProfileNetworkMapping
        assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId)).isNotNull()
            .extracting(SdLanProfile::getSdLanProfileRegularSettings,
                InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
            .hasSize(2)
            .filteredOn(regularSetting -> venue1.getId().equals(regularSetting.getVenue().getId()))
            .singleElement()
            .extracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings,
                InstanceOfAssertFactories.list(SdLanProfileNetworkMapping.class))
            .hasSize(5)
            .satisfiesExactlyInAnyOrder(
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network1.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isTrue();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNull();
                },
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network2.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isFalse();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNotNull().extracting(TunnelProfile::getId)
                      .isEqualTo(tunnelProfile3.getId());
                },
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network3.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isTrue();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNull();
                },
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network5.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isFalse();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNull();
                },
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network7.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isTrue();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNull();
                });

        final String updateSdLanRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), updateSdLanRequestId,
            contextOf(tenant, sdLanProfileId, tunnelProfile2).build(Action.UPDATE_SD_LAN));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), updateSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(2)
            .allSatisfy(op -> {
              if (StringUtils.equals(tunnelProfile1.getId(),
                  op.getDocMap().get(Key.ID).getStringValue())) {
                assertThat(op.getDocMap())
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                    .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
              } else if (StringUtils.equals(tunnelProfile2.getId(),
                  op.getDocMap().get(Key.ID).getStringValue())) {
                assertThat(op.getDocMap()).containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                        ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                    .hasEntrySatisfying(Key.NETWORK_IDS,
                        networkIdListValue -> assertThat(networkIdListValue.getListValue())
                            .extracting(
                                ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                            .hasSize(7)
                            .extracting(Value::getStringValue)
                            .containsExactlyInAnyOrder(network1.getId(), network2.getId(),
                                network3.getId(), network4.getId(), network5.getId(), network6.getId(),
                                network7.getId()));
              }
            });

        assertThat(messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), updateSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(WifiConfigRequest::getOperationsList,
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .as("The total operation count should be 9").hasSize(9)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
            .as("The modify WlanVenue operation count should be 9").hasSize(9)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
            .allSatisfy(wlanVenue -> {
              assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile2.getId());
              assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
            });


        final String deleteSdLanRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), deleteSdLanRequestId,
            contextOfDelete(tenant, sdLanProfileId).build(Action.DELETE_SD_LAN));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), deleteSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .singleElement()
            .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
            .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile2.getId()))
            .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
            .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));

        assertThat(messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), deleteSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(WifiConfigRequest::getOperationsList,
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .as("The total operation count should be 9").hasSize(9)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
            .as("The modify WlanVenue operation count should be 9").hasSize(9)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
            .allSatisfy(wlanVenue -> {
              assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(StringUtils.EMPTY);
              assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
              assertThat(wlanVenue.getIsNotifiedEdge()).isTrue();
            });
      }
    }

    @FeatureFlag(enable = EDGE_L2GRE_TOGGLE)
    @Nested
    class GivenEdgeL2GreFeatureFlagEnabled {

      @Test
      void testCreateUpdateDeleteSdLanProfileWithNetwork(Tenant tenant) {
        TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue1).isGuestTunnelUtilized(true).build();
        TunneledWlan tunneledWlan2 = TunneledWlanContext.of(network2, venue1).isGuestTunnelUtilized(false).withFwdTunnelProfile(tunnelProfile3).build();
        TunneledWlan tunneledWlan3 = TunneledWlanContext.of(network3, venue1).isGuestTunnelUtilized(true).withFwdTunnelProfileId(randomId()).build();
        TunneledWlan tunneledWlan4 = TunneledWlanContext.of(network5, venue1).isGuestTunnelUtilized(false).build();
        TunneledWlan tunneledWlan5 = TunneledWlanContext.of(network7, venue1).isGuestTunnelUtilized(true).withFwdTunnelProfile(tunnelProfile3).build();
        TunneledWlan tunneledWlan6 = TunneledWlanContext.of(network3, venue2).build();
        TunneledWlan tunneledWlan7 = TunneledWlanContext.of(network4, venue2).build();
        TunneledWlan tunneledWlan8 = TunneledWlanContext.of(network6, venue2).build();
        TunneledWlan tunneledWlan9 = TunneledWlanContext.of(network7, venue2).build();

        String sdLanProfileId = txCtxExtension.newRandomId();
        String connectResourceId = txCtxExtension.newRandomId();
        String createSdLanRequestId = randomTxId();

        ChangedConnectResource connectResource = ChangedConnectResource.newBuilder()
            .setConnectResourceId(connectResourceId)
            .setSdLanId(sdLanProfileId)
            .build();
        messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId,
            contextOf(tenant, sdLanProfileId, tunnelProfile1).with(connectResource).build(Action.CREATE_SD_LAN));
        assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
            .isNotNull()
            .extracting(SdLanProfile::getSdLanProfileRegularSettings,
                InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
            .isNotEmpty()
            .singleElement()
            .extracting(SdLanProfileRegularSetting::getVenue)
            .isNull();

        assertThat(repositoryUtil.find(EdgeConnectResource.class, connectResourceId))
            .isNotNull()
            .satisfies(cr -> {
              assertThat(cr.getId()).isEqualTo(connectResourceId);
              assertThat(cr.getSdLanProfile().getId()).isEqualTo(sdLanProfileId);
            });

        String bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan4).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan5).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan6).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan7).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan8).build(Action.ACTIVATE_NETWORK));

        bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan9).build(Action.ACTIVATE_NETWORK));

        // Verify the isGuestTrafficTunnel value of SdLanProfileNetworkMapping
        assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId)).isNotNull()
            .extracting(SdLanProfile::getSdLanProfileRegularSettings,
                InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
            .hasSize(2)
            .filteredOn(regularSetting -> venue1.getId().equals(regularSetting.getVenue().getId()))
            .singleElement()
            .extracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings,
                InstanceOfAssertFactories.list(SdLanProfileNetworkMapping.class))
            .hasSize(5)
            .satisfiesExactlyInAnyOrder(
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network1.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isTrue();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNull();
                },
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network2.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isFalse();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNotNull().extracting(TunnelProfile::getId)
                      .isEqualTo(tunnelProfile3.getId());
                },
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network3.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isTrue();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNull();
                },
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network5.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isFalse();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNull();
                },
                networkMapping -> {
                  assertThat(networkMapping.getNetwork().getId()).isEqualTo(network7.getId());
                  assertThat(networkMapping.getIsGuestTrafficTunnel()).isTrue();
                  assertThat(networkMapping.getForwardingTunnelProfile()).isNotNull().extracting(TunnelProfile::getId)
                      .isEqualTo(tunnelProfile3.getId());
                });

        final String updateSdLanRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), updateSdLanRequestId,
            contextOf(tenant, sdLanProfileId, tunnelProfile2).build(Action.UPDATE_SD_LAN));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), updateSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(3)
            .map(Operations::getDocMap)
            .satisfiesExactlyInAnyOrder(
                doc -> assertThat(doc)
                    .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile1.getId()))
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                    .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of())),
                doc -> assertThat(doc)
                    .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile2.getId()))
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                    .hasEntrySatisfying(Key.NETWORK_IDS,
                        networkIdListValue -> assertThat(networkIdListValue.getListValue())
                            .extracting(
                                ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                            .hasSize(7)
                            .extracting(Value::getStringValue)
                            .containsExactlyInAnyOrder(network1.getId(), network2.getId(),
                                network3.getId(), network4.getId(), network5.getId(), network6.getId(),
                                network7.getId())),
                doc -> assertThat(doc)
                    .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile3.getId()))
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                    .hasEntrySatisfying(Key.NETWORK_IDS,
                        networkIdListValue -> assertThat(networkIdListValue.getListValue())
                            .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                            .hasSize(2)
                            .extracting(Value::getStringValue)
                            .containsExactlyInAnyOrder(network2.getId(), network7.getId()))
            );

        assertThat(messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), updateSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(WifiConfigRequest::getOperationsList,
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .as("The total operation count should be 9").hasSize(9)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
            .as("The modify WlanVenue operation count should be 9").hasSize(9)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
            .allSatisfy(wlanVenue -> {
              assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile2.getId());
              assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
            });


        final String deleteSdLanRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), deleteSdLanRequestId,
            contextOfDelete(tenant, sdLanProfileId).build(Action.DELETE_SD_LAN));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), deleteSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(2)
            .map(Operations::getDocMap)
            .as("Should contain tunnelProfile2 (updated DC tunnel profile), and tunnelProfile3 (forwarding tunnelProfile)")
            .satisfiesExactlyInAnyOrder(
                doc -> assertThat(doc)
                    .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile2.getId()))
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                    .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of())),
                doc -> assertThat(doc)
                    .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile3.getId()))
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                    .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()))
            );

        assertThat(messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), deleteSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(WifiConfigRequest::getOperationsList,
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .as("The total operation count should be 9").hasSize(9)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
            .as("The modify WlanVenue operation count should be 9").hasSize(9)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
            .allSatisfy(wlanVenue -> {
              assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(StringUtils.EMPTY);
              assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
              assertThat(wlanVenue.getIsNotifiedEdge()).isTrue();
            });
      }

      @FeatureFlag(enable = EDGE_DELEGATION_POC_TOGGLE)
      @Test
      void testCreateDeleteSdLanProfileWithNetworkAndEdgeDelegation(Tenant mspTenant, TunnelProfile tunnelProfile) {
        final Venue venueTemplate = repositoryUtil.createOrUpdate(randomVenueTemplate(mspTenant));
        final var networkTemplates = createAndSaveOpenNetworkTemplates(mspTenant, 1);
        final Network networkTemplate1 = networkTemplates.get(0);
        createAndSaveNetworkVenues(mspTenant, venueTemplate, networkTemplates);

        final String tenantIdEC1 = randomId();
        final Tenant tenantEC1 = repositoryUtil.createOrUpdate(
                randomTenant(t -> {
                  t.setId(tenantIdEC1);
                  t.setName("Tenant EC 1");
                }), tenantIdEC1);

        final Venue venueInstEC1 = repositoryUtil.createOrUpdate(randomVenueTemplateInstance(venueTemplate.getId(), tenantEC1), tenantIdEC1);
        final Network network1InstEC1 = repositoryUtil.createOrUpdate(randomNetworkTemplateInstance(networkTemplate1.getId(), tenantEC1), tenantIdEC1);
        createAndSaveNetworkVenues(tenantEC1, venueInstEC1, List.of(network1InstEC1));

        final TunneledWlan tunneledWlan1 = TunneledWlanContext.of(networkTemplate1, venueTemplate).isTemplate(true).build();

        final String sdLanProfileId = txCtxExtension.newRandomId();

        final String createSdLanRequestId = randomTxId();

        messageUtil.sendEdgeSdLanService(mspTenant.getId(), createSdLanRequestId,
                contextOf(mspTenant, sdLanProfileId, tunnelProfile).build(Action.CREATE_SD_LAN));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
                .getValue(mspTenant.getId(), createSdLanRequestId)).isNotNull()
                .extracting(KafkaProtoMessage::getPayload).isNotNull()
                .extracting(ViewmodelCollector::getOperationsList,
                        InstanceOfAssertFactories.list(Operations.class))
                .isNotEmpty()
                .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
                .singleElement()
                .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
                .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                        ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))));
        messageCaptors.getDdccmMessageCaptor()
                .assertNotSent(mspTenant.getId(), createSdLanRequestId);

        messageUtil.clearMessage();

        final String bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(mspTenant.getId(), bindNetworkRequestId,
                contextOfActivateNetwork(mspTenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
                .getValue(mspTenant.getId(), bindNetworkRequestId)).isNotNull()
                .extracting(KafkaProtoMessage::getPayload).isNotNull()
                .extracting(ViewmodelCollector::getOperationsList,
                        InstanceOfAssertFactories.list(Operations.class))
                .isNotEmpty()
                .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
                .singleElement()
                .extracting(Operations::getDocMap)
                .satisfies(doc -> assertThat(doc)
                        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                                ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                        .hasEntrySatisfying(Key.NETWORK_IDS,
                                networkIdListValue -> assertThat(networkIdListValue.getListValue())
                                        .extracting(
                                                ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                                        .singleElement()
                                        .extracting(Value::getStringValue)
                                        .isEqualTo(networkTemplate1.getId())));

        assertThat(messageCaptors.getDdccmMessageCaptor()
                .getValue(mspTenant.getId(), bindNetworkRequestId)).isNotNull()
                .extracting(KafkaProtoMessage::getPayload).isNotNull()
                .extracting(WifiConfigRequest::getOperationsList,
                        InstanceOfAssertFactories.list(Operation.class))
                .isNotEmpty()
                .as("The total operation count should be 1").hasSize(1)
                .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
                .filteredOn(op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY)
                .as("The MODIFY WlanVenue operation count should be 1").hasSize(1)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                .allSatisfy(wlanVenue -> {
                  assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
                  assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
                });

        // networkTemplate1 is the instance in EC1, so no messages should be sent from MspTenant
        messageCaptors.getWifiAsyncJobMessageCaptor()
                .assertNotSentByTenants(mspTenant.getId());

        final String deleteSdLanRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(mspTenant.getId(), deleteSdLanRequestId,
                contextOfDelete(mspTenant, sdLanProfileId).build(Action.DELETE_SD_LAN));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
                .getValue(txCtxExtension.getTenantId(), deleteSdLanRequestId)).isNotNull()
                .extracting(KafkaProtoMessage::getPayload).isNotNull()
                .extracting(ViewmodelCollector::getOperationsList,
                        InstanceOfAssertFactories.list(Operations.class))
                .isNotEmpty()
                .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
                .singleElement()
                .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
                .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                .containsEntry(Key.NETWORK_SEGMENTATION_IDS, ValueUtils.listValue(List.of()))
                .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));

        assertThat(messageCaptors.getDdccmMessageCaptor()
                .getValue(txCtxExtension.getTenantId(), deleteSdLanRequestId)).isNotNull()
                .extracting(KafkaProtoMessage::getPayload).isNotNull()
                .extracting(WifiConfigRequest::getOperationsList,
                        InstanceOfAssertFactories.list(Operation.class))
                .isNotEmpty()
                .as("The total operation count should be 1").hasSize(1)
                .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
                .as("The modify WlanVenue operation count should be 0").hasSize(0)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                .isEmpty();

        assertThat(messageCaptors.getWifiAsyncJobMessageCaptor()
                .getValue(tenantIdEC1, deleteSdLanRequestId)).isNotNull()
                .extracting(KafkaProtoMessage::getPayload)
                .isInstanceOf(WifiAsyncJob.class)
                .satisfies(payload -> {
                  assertThat(payload.getJobCase()).isEqualTo(WifiAsyncJob.JobCase.EDGE_CFG_CHANGE_JOB);
                  assertThat(payload.getEdgeCfgChangeJob()).isNotNull()
                          .satisfies(edgeCfgChangeJob -> {
                            assertThat(edgeCfgChangeJob.getJobExecution()).isNotNull()
                                    .satisfies(jobExecution -> {
                                      assertThat(jobExecution.getJobEnum())
                                              .isEqualTo(EdgeCfgChangeJob.EdgeCfgChangeJobEnum.EdgeCfgChangeJobEnum_DELETE_SDLAN);
                                      assertThat(jobExecution.getNetworkIdAtVenueId())
                                              .isEqualTo(network1InstEC1.getId()+"@"+venueInstEC1.getId());
                                    });
                          });
                });
      }

    }
  }

  record EdgeSdLanServiceContext(String tenantId, String sdLanProfileId,
                                 String tunnelProfileId, String guestTunnelProfileId,
                                 TunneledWlan tunneledWlan, TunneledWlan guestTunneledWlan,
                                 ChangedConnectResource connectResource) {

    public EdgeSdLanService build(Action action) {
      return buildEdgeSdLanService(action, this);
    }

    public EdgeSdLanServiceContext with(ChangedConnectResource connectResource) {
      return newContext(tenantId, sdLanProfileId, tunnelProfileId,
          guestTunnelProfileId, tunneledWlan, guestTunneledWlan, connectResource);
    }

    static EdgeSdLanServiceContext contextOfDelete(Tenant tenant, String sdLanProfileId) {
      return contextOf(tenant, sdLanProfileId, null);
    }

    static EdgeSdLanServiceContext contextOfActivateGuestTunnel(Tenant tenant,
        String sdLanProfileId, TunnelProfile guestTunnelProfile) {
      return contextOf(tenant, sdLanProfileId, null, guestTunnelProfile);
    }

    static EdgeSdLanServiceContext contextOfActivateNetwork(Tenant tenant, String sdLanProfileId,
        TunneledWlan tunneledWlan) {
      return contextOf(tenant, sdLanProfileId, tunneledWlan, null);
    }

    static EdgeSdLanServiceContext contextOfActivateGuestNetwork(Tenant tenant,
        String sdLanProfileId, TunneledWlan guestTunneledWlan) {
      return contextOf(tenant, sdLanProfileId, null, guestTunneledWlan);
    }

    static EdgeSdLanServiceContext contextOf(Tenant tenant, String sdLanProfileId,
        TunnelProfile tunnelProfile) {
      return contextOf(tenant, sdLanProfileId, tunnelProfile, null);
    }

    private static EdgeSdLanServiceContext contextOf(Tenant tenant, String sdLanProfileId,
        TunnelProfile tunnelProfile, TunnelProfile guestTunnelProfile) {
      return newContext(tenant.getId(), sdLanProfileId,
          ofNullable(tunnelProfile).map(TunnelProfile::getId).orElse(null),
          ofNullable(guestTunnelProfile).map(TunnelProfile::getId).orElse(null),
          null, null, null);
    }

    private static EdgeSdLanServiceContext contextOf(Tenant tenant, String sdLanProfileId,
        TunneledWlan tunneledWlan, TunneledWlan guestTunneledWlan) {
      return newContext(tenant.getId(), sdLanProfileId, null, null,
          tunneledWlan, guestTunneledWlan, null);
    }

    private static EdgeSdLanServiceContext newContext(String tenantId, String sdLanProfileId,
        String tunnelProfileId, String guestTunnelProfileId,
        TunneledWlan tunneledWlan, TunneledWlan guestTunneledWlan,
        ChangedConnectResource connectResource) {
      return new EdgeSdLanServiceContext(tenantId, sdLanProfileId, tunnelProfileId,
          guestTunnelProfileId, tunneledWlan, guestTunneledWlan, connectResource);
    }
  }

  private static EdgeSdLanService buildEdgeSdLanService(Action action, EdgeSdLanServiceContext context) {
    EdgeSdLanService.Builder edgeSdLanService = EdgeSdLanService.newBuilder()
        .setId(context.sdLanProfileId)
        .setTenantId(context.tenantId)
        .setAction(action);
    if (null != context.tunnelProfileId) {
      edgeSdLanService.setTunnelProfileId(context.tunnelProfileId);
    }
    if (null != context.guestTunnelProfileId) {
      edgeSdLanService.setGuestTunnelProfileId(context.guestTunnelProfileId);
    }
    if (null != context.tunneledWlan) {
      edgeSdLanService.addTunneledWlans(context.tunneledWlan);
    }
    if (null != context.guestTunneledWlan) {
      edgeSdLanService.addTunneledGuestWlans(context.guestTunneledWlan);
    }
    if (null != context.connectResource) {
      edgeSdLanService.setConnectResource(context.connectResource);
    }
    return edgeSdLanService.build();
  }

  record TunneledWlanContext(String networkId, String venueId, String fwdTunnelProfileId,
                             Boolean isGuestTunnelUtilized, Boolean isTemplate) {

    public TunneledWlan.Builder toBuilder() {
      final var builder = TunneledWlan.newBuilder()
          .setNetworkId(networkId)
          .setVenueId(venueId);

      ofNullable(fwdTunnelProfileId).ifPresent(builder::setFwdTunnelProfileId);
      ofNullable(isGuestTunnelUtilized).ifPresent(builder::setIsGuestTunnelUtilized);
      ofNullable(isTemplate).ifPresent(builder::setIsTemplate);

      return builder;
    }

    public TunneledWlan build() {
      return toBuilder().build();
    }

    public TunneledWlanContext withFwdTunnelProfileId(String fwdTunnelProfileId) {
      return new TunneledWlanContext(networkId, venueId, fwdTunnelProfileId, isGuestTunnelUtilized, isTemplate);
    }

    public TunneledWlanContext withFwdTunnelProfile(TunnelProfile fwdTunnelProfile) {
      return new TunneledWlanContext(networkId, venueId,
          ofNullable(fwdTunnelProfile).map(TunnelProfile::getId).orElse(null),
          isGuestTunnelUtilized, isTemplate);
    }

    public TunneledWlanContext isGuestTunnelUtilized(Boolean isGuestTunnelUtilized) {
      return new TunneledWlanContext(networkId, venueId, fwdTunnelProfileId, isGuestTunnelUtilized, isTemplate);
    }

    public TunneledWlanContext isTemplate(Boolean isTemplate) {
      return new TunneledWlanContext(networkId, venueId, fwdTunnelProfileId, isGuestTunnelUtilized, isTemplate);
    }

    static TunneledWlanContext of(Network network, Venue venue) {
      return of(requireNonNull(network).getId(), requireNonNull(venue).getId());
    }

    static TunneledWlanContext of(String networkId, String venueId) {
      return new TunneledWlanContext(requireNonNull(networkId), requireNonNull(venueId),
          null, null, null);
    }
  }

  @Nested
  class BindNetworkAndUnbindNetworkTest {

    private Network network1;
    private Network network2;
    private Network network3;
    private Network network4;

    @BeforeEach
    void givenNetworksPersistedInDb(Tenant tenant, Venue venue) {
      final var networks = createAndSaveOpenNetworks(tenant, 4);
      this.network1 = networks.get(0);
      this.network2 = networks.get(1);
      this.network3 = networks.get(2);
      this.network4 = networks.get(3);
      createAndSaveNetworkVenues(tenant, venue, networks);
    }

    @FeatureFlag(disable = EDGE_L2GRE_TOGGLE)
    @Nested
    class GivenEdgeL2GreFeatureFlagDisabled {

      @Test
      void bindNetworkAndUnbindNetwork(Tenant tenant, Venue venue, TunnelProfile tunnelProfile) {
        final TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue).build();
        final TunneledWlan tunneledWlan2 = TunneledWlanContext.of(network2, venue).build();
        final TunneledWlan tunneledWlan3 = TunneledWlanContext.of(network3, venue).build();
        final TunneledWlan tunneledWlan4 = TunneledWlanContext.of(network4, venue).build();

        final String sdLanProfileId = txCtxExtension.newRandomId();

        final String createSdLanRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId,
            contextOf(tenant, sdLanProfileId, tunnelProfile).build(Action.CREATE_SD_LAN));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), createSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .singleElement()
            .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
            .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
            .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))));
        messageCaptors.getDdccmMessageCaptor()
            .assertNotSent(txCtxExtension.getTenantId(), createSdLanRequestId);

        messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

        messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

        messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.ACTIVATE_NETWORK));

        messageUtil.clearMessage();

        final String bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan4).build(Action.ACTIVATE_NETWORK));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), bindNetworkRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .singleElement()
            .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
            .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
            .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
            .hasEntrySatisfying(Key.NETWORK_IDS,
                networkIdListValue -> assertThat(networkIdListValue.getListValue())
                    .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                    .hasSize(4)
                    .extracting(Value::getStringValue)
                    .containsExactlyInAnyOrder(network1.getId(), network2.getId(),
                        network3.getId(), network4.getId()));

        assertThat(messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), bindNetworkRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(WifiConfigRequest::getOperationsList,
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .as("The total operation count should be 1").hasSize(1)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
            .as("The modify WlanVenue operation count should be 4").hasSize(1)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
            .allSatisfy(wlanVenue -> {
              assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
              assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
            });


        final String unbindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), unbindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.DEACTIVATE_NETWORK));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), unbindNetworkRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .singleElement()
            .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
            .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
            .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
            .hasEntrySatisfying(Key.NETWORK_IDS,
                networkIdListValue -> assertThat(networkIdListValue.getListValue())
                    .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                    .hasSize(3)
                    .extracting(Value::getStringValue)
                    .containsExactlyInAnyOrder(network1.getId(), network2.getId(), network4.getId()));

        assertThat(messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), unbindNetworkRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(WifiConfigRequest::getOperationsList,
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .as("The total operation count should be 4").hasSize(1)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
            .as("The modify WlanVenue operation count should be 4").hasSize(1)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
            .allSatisfy(wlanVenue -> {
              assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(StringUtils.EMPTY);
              assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
            });
      }
    }

    @FeatureFlag(enable = EDGE_L2GRE_TOGGLE)
    @Nested
    class GivenEdgeL2GreFeatureFlagEnabled extends AbstractBaseTest {

      @FeatureFlag(enable = EDGE_DELEGATION_POC_TOGGLE)
      @Nested
      class GivenEdgeDelegationPoCFeatureFlagEnabled extends AbstractBaseTest {

        @Test
        void bindNetworkTemplateAndUnbindNetworkTemplate(Tenant mspTenant, TunnelProfile tunnelProfile) {
          final Venue venueTemplate = repositoryUtil.createOrUpdate(randomVenueTemplate(mspTenant));

          final var networkTemplates = createAndSaveOpenNetworkTemplates(mspTenant, 4);
          final Network networkTemplate1 = networkTemplates.get(0);
          final Network networkTemplate2 = networkTemplates.get(1);
          final Network networkTemplate3 = networkTemplates.get(2);
          final Network networkTemplate4 = networkTemplates.get(3);
          createAndSaveNetworkVenues(mspTenant, venueTemplate, networkTemplates);

          final String tenantIdEC1 = randomId();
          final Tenant tenantEC1 = repositoryUtil.createOrUpdate(
              randomTenant(t -> {
                t.setId(tenantIdEC1);
                t.setName("Tenant EC 1");
              }), tenantIdEC1);
          final Venue venueInstEC1 = repositoryUtil.createOrUpdate(randomVenueTemplateInstance(venueTemplate.getId(), tenantEC1), tenantIdEC1);
          final Network network2InstEC1 = repositoryUtil.createOrUpdate(randomNetworkTemplateInstance(networkTemplate2.getId(), tenantEC1), tenantIdEC1);
          final Network network4InstEC1 = repositoryUtil.createOrUpdate(randomNetworkTemplateInstance(networkTemplate4.getId(), tenantEC1), tenantIdEC1);
          createAndSaveNetworkVenues(tenantEC1, venueInstEC1, List.of(network2InstEC1, network4InstEC1));

          final String tenantIdEC2 = randomId();
          final Tenant tenantEC2 = repositoryUtil.createOrUpdate(
              randomTenant(t -> {
                t.setId(tenantIdEC2);
                t.setName("Tenant EC 2");
              }), tenantIdEC2);
          final Venue venueInstEC2 = repositoryUtil.createOrUpdate(randomVenueTemplateInstance(venueTemplate.getId(), tenantEC2), tenantIdEC2);
          final Network network3InstEC2 = repositoryUtil.createOrUpdate(randomNetworkTemplateInstance(networkTemplate3.getId(), tenantEC2), tenantIdEC2);
          final Network network4InstEC2 = repositoryUtil.createOrUpdate(randomNetworkTemplateInstance(networkTemplate4.getId(), tenantEC2), tenantIdEC2);
          createAndSaveNetworkVenues(tenantEC2, venueInstEC2, List.of(network3InstEC2, network4InstEC2));

          final var serialNameGenerator = serialName("fwdTunnelProfile");
          final TunnelProfile fwdTunnelProfile1 = repositoryUtil.createOrUpdate(
              TunnelProfileTestFixture.randomTunnelProfile(mspTenant,
                  (t) -> t.setName(serialNameGenerator.generate())));
          final TunnelProfile fwdTunnelProfile2 = repositoryUtil.createOrUpdate(
              TunnelProfileTestFixture.randomTunnelProfile(mspTenant,
                  (t) -> t.setName(serialNameGenerator.generate())));

          final TunneledWlan tunneledWlan1 = TunneledWlanContext.of(networkTemplate1, venueTemplate).isTemplate(true).build();
          final TunneledWlan tunneledWlan2 = TunneledWlanContext.of(networkTemplate2, venueTemplate).isTemplate(true).build();
          final TunneledWlan tunneledWlan3 = TunneledWlanContext.of(networkTemplate3, venueTemplate).isTemplate(true).withFwdTunnelProfile(fwdTunnelProfile1).build();
          final TunneledWlan tunneledWlan4 = TunneledWlanContext.of(networkTemplate4, venueTemplate).isTemplate(true).withFwdTunnelProfile(fwdTunnelProfile2).build();

          final String sdLanProfileId = txCtxExtension.newRandomId();

          final String createSdLanRequestId = randomTxId();
          messageUtil.sendEdgeSdLanService(mspTenant.getId(), createSdLanRequestId,
              contextOf(mspTenant, sdLanProfileId, tunnelProfile).build(Action.CREATE_SD_LAN));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(mspTenant.getId(), createSdLanRequestId)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .isNotEmpty()
              .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
              .singleElement()
              .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
              .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
              .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                  ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))));
          messageCaptors.getDdccmMessageCaptor()
              .assertNotSent(mspTenant.getId(), createSdLanRequestId);

          messageUtil.clearMessage();

          final String bindNetworkRequestId1 = randomTxId();
          messageUtil.sendEdgeSdLanService(mspTenant.getId(), bindNetworkRequestId1,
              contextOfActivateNetwork(mspTenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(mspTenant.getId(), bindNetworkRequestId1)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .isNotEmpty()
              .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
              .singleElement()
              .extracting(Operations::getDocMap)
              .satisfies(doc -> assertThat(doc)
                  .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                  .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                      ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                  .hasEntrySatisfying(Key.NETWORK_IDS,
                      networkIdListValue -> assertThat(networkIdListValue.getListValue())
                          .extracting(
                              ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                          .singleElement()
                          .extracting(Value::getStringValue)
                          .isEqualTo(networkTemplate1.getId())));

          assertThat(messageCaptors.getDdccmMessageCaptor()
              .getValue(mspTenant.getId(), bindNetworkRequestId1)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(WifiConfigRequest::getOperationsList,
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .as("The total operation count should be 1").hasSize(1)
              .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
              .filteredOn(op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY)
              .as("The MODIFY WlanVenue operation count should be 1").hasSize(1)
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
              .allSatisfy(wlanVenue -> {
                assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
                assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
              });

          // networkTemplate1 has no instances in EC1 and EC2, so no messages should be sent
          messageCaptors.getWifiAsyncJobMessageCaptor()
              .assertNotSentByTenants(tenantIdEC1, tenantIdEC2);
          messageCaptors.getDdccmMessageCaptor()
              .assertNotSentByTenants(tenantIdEC1, tenantIdEC2);

          final String bindNetworkRequestId2 = randomTxId();
          messageUtil.sendEdgeSdLanService(mspTenant.getId(), bindNetworkRequestId2,
              contextOfActivateNetwork(mspTenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(mspTenant.getId(), bindNetworkRequestId2)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .isNotEmpty()
              .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
              .singleElement()
              .extracting(Operations::getDocMap)
              .satisfies(doc -> assertThat(doc)
                  .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                  .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                      ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                  .hasEntrySatisfying(Key.NETWORK_IDS,
                      networkIdListValue -> assertThat(networkIdListValue.getListValue())
                          .extracting(
                              ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                          .hasSize(2)
                          .extracting(Value::getStringValue)
                          .containsExactlyInAnyOrder(networkTemplate1.getId(), networkTemplate2.getId())));

          assertThat(messageCaptors.getDdccmMessageCaptor()
              .getValue(mspTenant.getId(), bindNetworkRequestId2)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(WifiConfigRequest::getOperationsList,
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .as("The total operation count should be 1").hasSize(1)
              .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
              .filteredOn(op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY)
              .as("The MODIFY WlanVenue operation count should be 1").hasSize(1)
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
              .allSatisfy(wlanVenue -> {
                assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
                assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
              });

          // networkTemplate2 has an instance in EC1, so messages should be sent
          assertThat(messageCaptors.getWifiAsyncJobMessageCaptor()
              .getValue(tenantIdEC1, bindNetworkRequestId2)).isNotNull()
              .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, bindNetworkRequestId2))
              .satisfies(assertHeader(WifiCommonHeader.WIFI_TENANT_ID, tenantIdEC1))
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .satisfies(wifiAsyncJob -> {
                assertThat(wifiAsyncJob.hasEdgeDelegationEcTenantOperationJob()).isTrue();
                assertThat(wifiAsyncJob.getEdgeDelegationEcTenantOperationJob()).isNotNull()
                    .satisfies(job -> {
                      assertThat(job.getMspTenantId()).isEqualTo(mspTenant.getId());
                      assertThat(job.getOperationCase()).isEqualTo(OperationCase.SD_LAN_OPERATION);
                      assertThat(job.hasSdLanOperation()).isTrue();
                      assertThat(job.getSdLanOperation()).isNotNull()
                          .satisfies(sdLanOperation -> {
                            assertThat(sdLanOperation.getPayloadCase()).isEqualTo(PayloadCase.ACTIVATE_NETWORK);
                            assertThat(sdLanOperation.hasActivateNetwork()).isTrue();
                            assertThat(sdLanOperation.getActivateNetwork()).isNotNull()
                                .satisfies(activateNetwork -> {
                                  assertThat(activateNetwork.getSdLanServiceId()).isEqualTo(sdLanProfileId);
                                  assertThat(activateNetwork.getNetworkVenuesCount()).isEqualTo(1);
                                  assertThat(activateNetwork.getNetworkVenuesList()).isNotNull()
                                      .singleElement()
                                      .satisfies(networkVenue -> {
                                        assertThat(networkVenue.getNetworkId()).isEqualTo(network2InstEC1.getId());
                                        assertThat(networkVenue.getVenueId()).isEqualTo(venueInstEC1.getId());
                                      });
                                });
                          });
                    });
              });

          assertThat(messageCaptors.getDdccmMessageCaptor()
              .getValue(tenantIdEC1, bindNetworkRequestId2)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(WifiConfigRequest::getOperationsList,
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .as("The total operation count should be 2").hasSize(2)
              .satisfiesExactlyInAnyOrder(op -> {
                assertThat(op.hasWlanVenue()).isTrue();
                assertThat(op.getAction()).isEqualTo(com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY);
                assertThat(op.getCommonInfo().getRequestId()).startsWith(bindNetworkRequestId2);
                assertThat(op.getCommonInfo().getTenantId()).isEqualTo(tenantIdEC1);
                assertThat(op.getWlanVenue().getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
                assertThat(op.getWlanVenue().getCentralizedForwardingEnabled()).isTrue();
              }, op -> {
                assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
                assertThat(op.getAction()).isEqualTo(com.ruckus.acx.ddccm.protobuf.common.Action.ADD);
                assertThat(op.getId()).isEqualTo(tunnelProfile.getId());
                assertThat(op.getCommonInfo().getRequestId()).startsWith(bindNetworkRequestId2);
                assertThat(op.getCommonInfo().getTenantId()).isEqualTo(tenantIdEC1);
                assertThat(op.getCcmVxLANTunnelProfile().getId()).isEqualTo(tunnelProfile.getId());
              });

          // networkTemplate2 has no instances in EC2, so no messages should be sent
          messageCaptors.getWifiAsyncJobMessageCaptor()
              .assertNotSent(tenantIdEC2, bindNetworkRequestId2);
          messageCaptors.getDdccmMessageCaptor()
              .assertNotSent(tenantIdEC2, bindNetworkRequestId2);

          final String bindNetworkRequestId3 = randomTxId();
          messageUtil.sendEdgeSdLanService(mspTenant.getId(), bindNetworkRequestId3,
              contextOfActivateNetwork(mspTenant, sdLanProfileId, tunneledWlan3).build(Action.ACTIVATE_NETWORK));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(mspTenant.getId(), bindNetworkRequestId3)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .isNotEmpty()
              .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
              .hasSize(2)
              .map(Operations::getDocMap)
              .satisfiesExactlyInAnyOrder(
                  doc -> assertThat(doc)
                      .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                      .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                      .hasEntrySatisfying(Key.NETWORK_IDS,
                          networkIdListValue -> assertThat(networkIdListValue.getListValue())
                              .extracting(
                                  ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                              .hasSize(3)
                              .extracting(Value::getStringValue)
                              .containsExactlyInAnyOrder(networkTemplate1.getId(), networkTemplate2.getId(), networkTemplate3.getId())),
                  doc -> assertThat(doc)
                      .containsEntry(Key.ID, ValueUtils.stringValue(fwdTunnelProfile1.getId()))
                      .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                      .hasEntrySatisfying(Key.NETWORK_IDS,
                          networkIdListValue -> assertThat(networkIdListValue.getListValue())
                              .extracting(
                                  ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                              .hasSize(1)
                              .extracting(Value::getStringValue)
                              .containsExactly(networkTemplate3.getId()))
              );

          assertThat(messageCaptors.getDdccmMessageCaptor()
              .getValue(mspTenant.getId(), bindNetworkRequestId3)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(WifiConfigRequest::getOperationsList,
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .as("The total operation count should be 1").hasSize(1)
              .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
              .filteredOn(op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY)
              .as("The MODIFY WlanVenue operation count should be 1").hasSize(1)
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
              .allSatisfy(wlanVenue -> {
                assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
                assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
              });

          // networkTemplate3 has an instance in EC2, so messages should be sent
          assertThat(messageCaptors.getWifiAsyncJobMessageCaptor()
              .getValue(tenantIdEC2, bindNetworkRequestId3)).isNotNull()
              .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, bindNetworkRequestId3))
              .satisfies(assertHeader(WifiCommonHeader.WIFI_TENANT_ID, tenantIdEC2))
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .satisfies(wifiAsyncJob -> {
                assertThat(wifiAsyncJob.hasEdgeDelegationEcTenantOperationJob()).isTrue();
                assertThat(wifiAsyncJob.getEdgeDelegationEcTenantOperationJob()).isNotNull()
                    .satisfies(job -> {
                      assertThat(job.getMspTenantId()).isEqualTo(mspTenant.getId());
                      assertThat(job.getOperationCase()).isEqualTo(OperationCase.SD_LAN_OPERATION);
                      assertThat(job.hasSdLanOperation()).isTrue();
                      assertThat(job.getSdLanOperation()).isNotNull()
                          .satisfies(sdLanOperation -> {
                            assertThat(sdLanOperation.getPayloadCase()).isEqualTo(PayloadCase.ACTIVATE_NETWORK);
                            assertThat(sdLanOperation.hasActivateNetwork()).isTrue();
                            assertThat(sdLanOperation.getActivateNetwork()).isNotNull()
                                .satisfies(activateNetwork -> {
                                  assertThat(activateNetwork.getSdLanServiceId()).isEqualTo(sdLanProfileId);
                                  assertThat(activateNetwork.getNetworkVenuesCount()).isEqualTo(1);
                                  assertThat(activateNetwork.getNetworkVenuesList()).isNotNull()
                                      .singleElement()
                                      .satisfies(networkVenue -> {
                                        assertThat(networkVenue.getNetworkId()).isEqualTo(network3InstEC2.getId());
                                        assertThat(networkVenue.getVenueId()).isEqualTo(venueInstEC2.getId());
                                      });
                                });
                          });
                    });
              });

          assertThat(messageCaptors.getDdccmMessageCaptor()
              .getValue(tenantIdEC2, bindNetworkRequestId3)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(WifiConfigRequest::getOperationsList,
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .as("The total operation count should be 2").hasSize(2)
              .satisfiesExactlyInAnyOrder(op -> {
                assertThat(op.hasWlanVenue()).isTrue();
                assertThat(op.getAction()).isEqualTo(com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY);
                assertThat(op.getCommonInfo().getRequestId()).startsWith(bindNetworkRequestId3);
                assertThat(op.getCommonInfo().getTenantId()).isEqualTo(tenantIdEC2);
                assertThat(op.getWlanVenue().getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
                assertThat(op.getWlanVenue().getCentralizedForwardingEnabled()).isTrue();
              }, op -> {
                assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
                assertThat(op.getAction()).isEqualTo(com.ruckus.acx.ddccm.protobuf.common.Action.ADD);
                assertThat(op.getId()).isEqualTo(tunnelProfile.getId());
                assertThat(op.getCommonInfo().getRequestId()).startsWith(bindNetworkRequestId3);
                assertThat(op.getCommonInfo().getTenantId()).isEqualTo(tenantIdEC2);
                assertThat(op.getCcmVxLANTunnelProfile().getId()).isEqualTo(tunnelProfile.getId());
                // EC don't care the forwarding tunnel profile, so it should not be sent
              });

          // networkTemplate3 has no instances in EC1, so no messages should be sent
          messageCaptors.getWifiAsyncJobMessageCaptor()
              .assertNotSent(tenantIdEC1, bindNetworkRequestId3);
          messageCaptors.getDdccmMessageCaptor()
              .assertNotSent(tenantIdEC1, bindNetworkRequestId3);

          final String bindNetworkRequestId4 = randomTxId();
          messageUtil.sendEdgeSdLanService(mspTenant.getId(), bindNetworkRequestId4,
              contextOfActivateNetwork(mspTenant, sdLanProfileId, tunneledWlan4).build(Action.ACTIVATE_NETWORK));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(mspTenant.getId(), bindNetworkRequestId4)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .isNotEmpty()
              .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
              .hasSize(2)
              .map(Operations::getDocMap)
              .satisfiesExactlyInAnyOrder(
                  doc -> assertThat(doc)
                      .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                      .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                      .hasEntrySatisfying(Key.NETWORK_IDS,
                          networkIdListValue -> assertThat(networkIdListValue.getListValue())
                              .extracting(
                                  ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                              .hasSize(4)
                              .extracting(Value::getStringValue)
                              .containsExactlyInAnyOrder(networkTemplate1.getId(), networkTemplate2.getId(), networkTemplate3.getId(), networkTemplate4.getId())),
                  doc -> assertThat(doc)
                      .containsEntry(Key.ID, ValueUtils.stringValue(fwdTunnelProfile2.getId()))
                      .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                      .hasEntrySatisfying(Key.NETWORK_IDS,
                          networkIdListValue -> assertThat(networkIdListValue.getListValue())
                              .extracting(
                                  ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                              .hasSize(1)
                              .extracting(Value::getStringValue)
                              .containsExactly(networkTemplate4.getId()))
              );

          assertThat(messageCaptors.getDdccmMessageCaptor()
              .getValue(mspTenant.getId(), bindNetworkRequestId4)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(WifiConfigRequest::getOperationsList,
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .as("The total operation count should be 1").hasSize(1)
              .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
              .filteredOn(op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY)
              .as("The MODIFY WlanVenue operation count should be 1").hasSize(1)
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
              .allSatisfy(wlanVenue -> {
                assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
                assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
              });

          // networkTemplate4 has instances in EC1 and EC2, so messages should be sent
          assertThat(messageCaptors.getWifiAsyncJobMessageCaptor()
              .getValues(2, Set.of(tenantIdEC1, tenantIdEC2), bindNetworkRequestId4)).isNotNull()
              .hasSize(2)
              .allSatisfy(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, bindNetworkRequestId4))
              .satisfiesExactlyInAnyOrder(
                  assertHeader(WifiCommonHeader.WIFI_TENANT_ID, tenantIdEC1),
                  assertHeader(WifiCommonHeader.WIFI_TENANT_ID, tenantIdEC2)
              )
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .allSatisfy(wifiAsyncJob -> {
                assertThat(wifiAsyncJob.hasEdgeDelegationEcTenantOperationJob()).isTrue();
                assertThat(wifiAsyncJob.getEdgeDelegationEcTenantOperationJob()).isNotNull()
                    .satisfies(job -> {
                      assertThat(job.getMspTenantId()).isEqualTo(mspTenant.getId());
                      assertThat(job.getOperationCase()).isEqualTo(OperationCase.SD_LAN_OPERATION);
                      assertThat(job.hasSdLanOperation()).isTrue();
                      assertThat(job.getSdLanOperation()).isNotNull()
                          .satisfies(sdLanOperation -> {
                            assertThat(sdLanOperation.getPayloadCase()).isEqualTo(PayloadCase.ACTIVATE_NETWORK);
                            assertThat(sdLanOperation.hasActivateNetwork()).isTrue();
                            assertThat(sdLanOperation.getActivateNetwork()).isNotNull()
                                .satisfies(activateNetwork -> {
                                  assertThat(activateNetwork.getSdLanServiceId()).isEqualTo(sdLanProfileId);
                                  assertThat(activateNetwork.getNetworkVenuesCount()).isEqualTo(1);
                                  assertThat(activateNetwork.getNetworkVenuesList()).isNotNull()
                                      .singleElement()
                                      .satisfiesAnyOf(networkVenue -> {
                                        assertThat(networkVenue.getNetworkId()).isEqualTo(network4InstEC1.getId());
                                        assertThat(networkVenue.getVenueId()).isEqualTo(venueInstEC1.getId());
                                      }, networkVenue -> {
                                        assertThat(networkVenue.getNetworkId()).isEqualTo(network4InstEC2.getId());
                                        assertThat(networkVenue.getVenueId()).isEqualTo(venueInstEC2.getId());
                                      });
                                });
                          });
                    });
              });

          assertThat(messageCaptors.getDdccmMessageCaptor()
              .getValues(2, Set.of(tenantIdEC1, tenantIdEC2), bindNetworkRequestId4)).isNotNull()
              .hasSize(2)
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .satisfiesExactlyInAnyOrder(wifiConfigRequest -> {
                assertThat(wifiConfigRequest.getOperationsCount()).isEqualTo(1);
                assertThat(wifiConfigRequest.getOperationsList()).isNotNull()
                    .singleElement()
                    .satisfies(op -> {
                      assertThat(op.hasWlanVenue()).isTrue();
                      assertThat(op.getAction()).isEqualTo(com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY);
                      assertThat(op.getCommonInfo().getRequestId()).startsWith(bindNetworkRequestId4);
                      assertThat(op.getCommonInfo().getTenantId()).isEqualTo(tenantIdEC1);
                      assertThat(op.getWlanVenue().getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
                      assertThat(op.getWlanVenue().getCentralizedForwardingEnabled()).isTrue();
                      // EC don't care the forwarding tunnel profile, so it should not be sent
                    });
              }, wifiConfigRequest -> {
                assertThat(wifiConfigRequest.getOperationsCount()).isEqualTo(1);
                assertThat(wifiConfigRequest.getOperationsList()).isNotNull()
                    .singleElement()
                    .satisfies(op -> {
                      assertThat(op.hasWlanVenue()).isTrue();
                      assertThat(op.getAction()).isEqualTo(com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY);
                      assertThat(op.getCommonInfo().getRequestId()).startsWith(bindNetworkRequestId4);
                      assertThat(op.getCommonInfo().getTenantId()).isEqualTo(tenantIdEC2);
                      assertThat(op.getWlanVenue().getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
                      assertThat(op.getWlanVenue().getCentralizedForwardingEnabled()).isTrue();
                      // EC don't care the forwarding tunnel profile, so it should not be sent
                    });
              });


          final String unbindNetworkRequestId2 = randomTxId();
          messageUtil.sendEdgeSdLanService(mspTenant.getId(), unbindNetworkRequestId2,
                  contextOfActivateNetwork(mspTenant, sdLanProfileId, tunneledWlan2).build(Action.DEACTIVATE_NETWORK));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
                  .getValue(mspTenant.getId(), unbindNetworkRequestId2)).isNotNull()
                  .extracting(KafkaProtoMessage::getPayload).isNotNull()
                  .extracting(ViewmodelCollector::getOperationsList,
                          InstanceOfAssertFactories.list(Operations.class))
                  .isNotEmpty()
                  .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
                  .singleElement()
                  .extracting(Operations::getDocMap)
                  .satisfies(doc -> assertThat(doc)
                          .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                          .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                                  ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                          .hasEntrySatisfying(Key.NETWORK_IDS,
                                  networkIdListValue -> assertThat(networkIdListValue.getListValue())
                                          .extracting(
                                                  ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                                          .hasSize(3)
                                          .extracting(Value::getStringValue)
                                          .containsExactlyInAnyOrder(networkTemplate1.getId(), networkTemplate3.getId(), networkTemplate4.getId())));

          assertThat(messageCaptors.getDdccmMessageCaptor()
                  .getValue(mspTenant.getId(), unbindNetworkRequestId2)).isNotNull()
                  .extracting(KafkaProtoMessage::getPayload).isNotNull()
                  .extracting(WifiConfigRequest::getOperationsList,
                          InstanceOfAssertFactories.list(Operation.class))
                  .isNotEmpty()
                  .as("The total operation count should be 1").hasSize(1)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
                  .filteredOn(op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY)
                  .as("The MODIFY WlanVenue operation count should be 1").hasSize(1)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                  .allSatisfy(wlanVenue -> {
                    assertThat(wlanVenue.getVxlanTunnelProfileId()).isEmpty();
                    assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
                  });

          // networkTemplate2 has an instance in EC1, so messages should be sent
          assertThat(messageCaptors.getWifiAsyncJobMessageCaptor()
                  .getValue(tenantIdEC1, unbindNetworkRequestId2)).isNotNull()
                  .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, unbindNetworkRequestId2))
                  .satisfies(assertHeader(WifiCommonHeader.WIFI_TENANT_ID, tenantIdEC1))
                  .extracting(KafkaProtoMessage::getPayload).isNotNull()
                  .satisfies(wifiAsyncJob -> {
                    assertThat(wifiAsyncJob.hasEdgeDelegationEcTenantOperationJob()).isTrue();
                    assertThat(wifiAsyncJob.getEdgeDelegationEcTenantOperationJob()).isNotNull()
                            .satisfies(job -> {
                              assertThat(job.getMspTenantId()).isEqualTo(mspTenant.getId());
                              assertThat(job.getOperationCase()).isEqualTo(OperationCase.SD_LAN_OPERATION);
                              assertThat(job.hasSdLanOperation()).isTrue();
                              assertThat(job.getSdLanOperation()).isNotNull()
                                      .satisfies(sdLanOperation -> {
                                        assertThat(sdLanOperation.getPayloadCase()).isEqualTo(PayloadCase.DEACTIVATE_NETWORK);
                                        assertThat(sdLanOperation.hasDeactivateNetwork()).isTrue();
                                        assertThat(sdLanOperation.getDeactivateNetwork()).isNotNull()
                                                .satisfies(deactivateNetwork -> {
                                                  assertThat(deactivateNetwork.getSdLanServiceId()).isEqualTo(sdLanProfileId);
                                                  assertThat(deactivateNetwork.getNetworkVenuesCount()).isEqualTo(1);
                                                  assertThat(deactivateNetwork.getNetworkVenuesList()).isNotNull()
                                                          .singleElement()
                                                          .satisfies(networkVenue -> {
                                                            assertThat(networkVenue.getNetworkId()).isEqualTo(network2InstEC1.getId());
                                                            assertThat(networkVenue.getVenueId()).isEqualTo(venueInstEC1.getId());
                                                          });
                                                });
                                      });
                            });
                  });

          assertThat(messageCaptors.getDdccmMessageCaptor()
                  .getValue(tenantIdEC1, unbindNetworkRequestId2)).isNotNull()
                  .extracting(KafkaProtoMessage::getPayload).isNotNull()
                  .extracting(WifiConfigRequest::getOperationsList,
                          InstanceOfAssertFactories.list(Operation.class))
                  .isNotEmpty()
                  .as("The total operation count should be 1").hasSize(1)
                  .satisfiesExactlyInAnyOrder(op -> {
                    assertThat(op.hasWlanVenue()).isTrue();
                    assertThat(op.getAction()).isEqualTo(com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY);
                    assertThat(op.getCommonInfo().getRequestId()).startsWith(unbindNetworkRequestId2);
                    assertThat(op.getCommonInfo().getTenantId()).isEqualTo(tenantIdEC1);
                    assertThat(op.getWlanVenue().getVxlanTunnelProfileId()).isEmpty();
                    assertThat(op.getWlanVenue().getCentralizedForwardingEnabled()).isFalse();
                  });

          // networkTemplate2 has no instances in EC2, so no messages should be sent
          messageCaptors.getWifiAsyncJobMessageCaptor()
                  .assertNotSent(tenantIdEC2, unbindNetworkRequestId2);
          messageCaptors.getDdccmMessageCaptor()
                  .assertNotSent(tenantIdEC2, unbindNetworkRequestId2);

          final String unbindNetworkRequestId3 = randomTxId();
          messageUtil.sendEdgeSdLanService(mspTenant.getId(), unbindNetworkRequestId3,
              contextOfActivateNetwork(mspTenant, sdLanProfileId, tunneledWlan3).build(Action.DEACTIVATE_NETWORK));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(mspTenant.getId(), unbindNetworkRequestId3)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .isNotEmpty()
              .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
              .hasSize(2)
              .map(Operations::getDocMap)
              .satisfiesExactlyInAnyOrder(
                  doc -> assertThat(doc)
                      .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                      .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                      .hasEntrySatisfying(Key.NETWORK_IDS,
                          networkIdListValue -> assertThat(networkIdListValue.getListValue())
                              .extracting(
                                  ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                              .hasSize(2)
                              .extracting(Value::getStringValue)
                              .containsExactlyInAnyOrder(networkTemplate1.getId(), networkTemplate4.getId())),
                  doc -> assertThat(doc)
                      .containsEntry(Key.ID, ValueUtils.stringValue(fwdTunnelProfile1.getId()))
                      .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                      .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()))
              );

          assertThat(messageCaptors.getDdccmMessageCaptor()
              .getValue(mspTenant.getId(), unbindNetworkRequestId3)).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(WifiConfigRequest::getOperationsList,
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .as("The total operation count should be 4").hasSize(1)
              .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
              .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
              .as("The modify WlanVenue operation count should be 4").hasSize(1)
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
              .allSatisfy(wlanVenue -> {
                assertThat(wlanVenue.getVxlanTunnelProfileId()).isEmpty();
                assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
              });

          // networkTemplate3 has an instance in EC2, so messages should be sent
          assertThat(messageCaptors.getWifiAsyncJobMessageCaptor()
              .getValue(tenantIdEC2, unbindNetworkRequestId3)).isNotNull()
              .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, unbindNetworkRequestId3))
              .satisfies(assertHeader(WifiCommonHeader.WIFI_TENANT_ID, tenantIdEC2))
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .satisfies(wifiAsyncJob -> {
                assertThat(wifiAsyncJob.hasEdgeDelegationEcTenantOperationJob()).isTrue();
                assertThat(wifiAsyncJob.getEdgeDelegationEcTenantOperationJob()).isNotNull()
                        .satisfies(job -> {
                          assertThat(job.getMspTenantId()).isEqualTo(mspTenant.getId());
                          assertThat(job.getOperationCase()).isEqualTo(OperationCase.SD_LAN_OPERATION);
                          assertThat(job.hasSdLanOperation()).isTrue();
                          assertThat(job.getSdLanOperation()).isNotNull()
                                  .satisfies(sdLanOperation -> {
                                    assertThat(sdLanOperation.getPayloadCase()).isEqualTo(PayloadCase.DEACTIVATE_NETWORK);
                                    assertThat(sdLanOperation.hasDeactivateNetwork()).isTrue();
                                    assertThat(sdLanOperation.getDeactivateNetwork()).isNotNull()
                                            .satisfies(deactivateNetwork -> {
                                              assertThat(deactivateNetwork.getSdLanServiceId()).isEqualTo(sdLanProfileId);
                                              assertThat(deactivateNetwork.getNetworkVenuesCount()).isEqualTo(1);
                                              assertThat(deactivateNetwork.getNetworkVenuesList()).isNotNull()
                                                      .singleElement()
                                                      .satisfies(networkVenue -> {
                                                        assertThat(networkVenue.getNetworkId()).isEqualTo(network3InstEC2.getId());
                                                        assertThat(networkVenue.getVenueId()).isEqualTo(venueInstEC2.getId());
                                                      });
                                            });
                                  });
                        });
              });

          assertThat(messageCaptors.getDdccmMessageCaptor()
                  .getValue(tenantIdEC2, unbindNetworkRequestId3)).isNotNull()
                  .extracting(KafkaProtoMessage::getPayload).isNotNull()
                  .extracting(WifiConfigRequest::getOperationsList,
                          InstanceOfAssertFactories.list(Operation.class))
                  .isNotEmpty()
                  .as("The total operation count should be 1").hasSize(1)
                  .satisfiesExactlyInAnyOrder(op -> {
                    assertThat(op.hasWlanVenue()).isTrue();
                    assertThat(op.getAction()).isEqualTo(com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY);
                    assertThat(op.getCommonInfo().getRequestId()).startsWith(unbindNetworkRequestId3);
                    assertThat(op.getCommonInfo().getTenantId()).isEqualTo(tenantIdEC2);
                    assertThat(op.getWlanVenue().getVxlanTunnelProfileId()).isEmpty();
                    assertThat(op.getWlanVenue().getCentralizedForwardingEnabled()).isFalse();
                  });

          // networkTemplate3 has no instances in EC1, so no messages should be sent
          messageCaptors.getWifiAsyncJobMessageCaptor()
                  .assertNotSent(tenantIdEC1, unbindNetworkRequestId3);
          messageCaptors.getDdccmMessageCaptor()
                  .assertNotSent(tenantIdEC1, unbindNetworkRequestId3);
        }
      }
    }

    private abstract class AbstractBaseTest {

      @Test
      void bindNetworkAndUnbindNetwork(Tenant tenant, Venue venue, TunnelProfile tunnelProfile) {
        final TunnelProfile fwdTunnelProfile1 = repositoryUtil.createOrUpdate(
            TunnelProfileTestFixture.randomTunnelProfile(tenant,
                (t) -> t.setName(randomString().generate())));
        final TunnelProfile fwdTunnelProfile2 = repositoryUtil.createOrUpdate(
            TunnelProfileTestFixture.randomTunnelProfile(tenant,
                (t) -> t.setName(randomString().generate())));

        final TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue).build();
        final TunneledWlan tunneledWlan2 = TunneledWlanContext.of(network2, venue).withFwdTunnelProfile(fwdTunnelProfile1).build();
        final TunneledWlan tunneledWlan3 = TunneledWlanContext.of(network3, venue).withFwdTunnelProfile(fwdTunnelProfile2).build();
        final TunneledWlan tunneledWlan4 = TunneledWlanContext.of(network4, venue).withFwdTunnelProfile(fwdTunnelProfile2).build();

        final String sdLanProfileId = txCtxExtension.newRandomId();

        final String createSdLanRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId,
            contextOf(tenant, sdLanProfileId, tunnelProfile).build(Action.CREATE_SD_LAN));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), createSdLanRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .singleElement()
            .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
            .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
            .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))));
        messageCaptors.getDdccmMessageCaptor()
            .assertNotSent(txCtxExtension.getTenantId(), createSdLanRequestId);

        messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

        messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

        messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.ACTIVATE_NETWORK));

        messageUtil.clearMessage();

        final String bindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan4).build(Action.ACTIVATE_NETWORK));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), bindNetworkRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(2)
            .map(Operations::getDocMap)
            .satisfiesExactlyInAnyOrder(
                doc -> assertThat(doc)
                    .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                    .hasEntrySatisfying(Key.NETWORK_IDS,
                        networkIdListValue -> assertThat(networkIdListValue.getListValue())
                            .extracting(
                                ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                            .hasSize(4)
                            .extracting(Value::getStringValue)
                            .containsExactlyInAnyOrder(network1.getId(), network2.getId(), network3.getId(), network4.getId())),
                doc -> assertThat(doc)
                    .containsEntry(Key.ID, ValueUtils.stringValue(fwdTunnelProfile2.getId()))
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                    .hasEntrySatisfying(Key.NETWORK_IDS,
                        networkIdListValue -> assertThat(networkIdListValue.getListValue())
                            .extracting(
                                ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                            .hasSize(2)
                            .extracting(Value::getStringValue)
                            .containsExactlyInAnyOrder(network3.getId(), network4.getId()))
            );

        assertThat(messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), bindNetworkRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(WifiConfigRequest::getOperationsList,
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .as("The total operation count should be 1").hasSize(1)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
            .as("The modify WlanVenue operation count should be 4").hasSize(1)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
            .allSatisfy(wlanVenue -> {
              assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
              assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
            });


        final String unbindNetworkRequestId = randomTxId();
        messageUtil.sendEdgeSdLanService(tenant.getId(), unbindNetworkRequestId,
            contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.DEACTIVATE_NETWORK));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), unbindNetworkRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(2)
            .map(Operations::getDocMap)
            .satisfiesExactlyInAnyOrder(
                doc -> assertThat(doc)
                    .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                    .hasEntrySatisfying(Key.NETWORK_IDS,
                        networkIdListValue -> assertThat(networkIdListValue.getListValue())
                            .extracting(
                                ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                            .hasSize(3)
                            .extracting(Value::getStringValue)
                            .containsExactlyInAnyOrder(network1.getId(), network2.getId(), network4.getId())),
                doc -> assertThat(doc)
                    .containsEntry(Key.ID, ValueUtils.stringValue(fwdTunnelProfile2.getId()))
                    .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                    .hasEntrySatisfying(Key.NETWORK_IDS,
                        networkIdListValue -> assertThat(networkIdListValue.getListValue())
                            .extracting(
                                ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                            .hasSize(1)
                            .extracting(Value::getStringValue)
                            .containsExactlyInAnyOrder(network4.getId()))
            );

        assertThat(messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), unbindNetworkRequestId)).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(WifiConfigRequest::getOperationsList,
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .as("The total operation count should be 4").hasSize(1)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
            .as("The modify WlanVenue operation count should be 4").hasSize(1)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
            .allSatisfy(wlanVenue -> {
              assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(StringUtils.EMPTY);
              assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
            });
      }
    }
  }

  @Test
  void modifyGuestNetworkAndGuestTunnelWithMultipleVenues(Tenant tenant, Venue venue1, TunnelProfile tunnelProfile1) {
    //prepare test data
    final var networks = createAndSaveOpenNetworks(tenant, 4);
    Network network1 = networks.get(0);
    Network network2 = networks.get(1);
    Network network3 = networks.get(2);
    Network network4 = networks.get(3);

    Venue venue2 = VenueTestFixture.randomVenue(tenant);
    repositoryUtil.createOrUpdate(venue2, tenant.getId(), randomTxId());
    createAndSaveNetworkVenues(tenant, venue1, List.of(network1, network2, network3, network4));
    createAndSaveNetworkVenues(tenant, venue2, List.of(network1, network2));

    TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue1).build();
    TunneledWlan tunneledWlan2 = TunneledWlanContext.of(network2, venue1).build();
    TunneledWlan tunneledWlan3 = TunneledWlanContext.of(network3, venue1).build();
    TunneledWlan tunneledWlan4 = TunneledWlanContext.of(network4, venue1).build();
    TunneledWlan tunneledWlan5 = TunneledWlanContext.of(network1, venue2).build();
    TunneledWlan tunneledWlan6 = TunneledWlanContext.of(network2, venue2).build();

    String sdLanProfileId = txCtxExtension.newRandomId();
    String createSdLanRequestId = randomTxId();

    messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId,
        contextOf(tenant, sdLanProfileId, tunnelProfile1).build(Action.CREATE_SD_LAN));

    String bindNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

    bindNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

    bindNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.ACTIVATE_NETWORK));

    bindNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan4).build(Action.ACTIVATE_NETWORK));

    bindNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan5).build(Action.ACTIVATE_NETWORK));

    bindNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan6).build(Action.ACTIVATE_NETWORK));

    TunnelProfile guestTunnelProfile = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> t.setName(randomString().generate()));
    repositoryUtil.createOrUpdate(guestTunnelProfile, tenant.getId(), randomTxId());


    final String addGuestTunnelRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), addGuestTunnelRequestId,
        contextOfActivateGuestTunnel(tenant, sdLanProfileId, guestTunnelProfile).build(Action.ACTIVATE_GUEST_TUNNEL));
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .isNotEmpty()
        .allSatisfy(rs -> assertThat(rs.getGuestTrafficTunnelProfile().getId())
            .isEqualTo(guestTunnelProfile.getId()));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), addGuestTunnelRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(guestTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));

    TunnelProfile newGuestTunnelProfile = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> t.setName(randomString().generate()));
    repositoryUtil.createOrUpdate(newGuestTunnelProfile, tenant.getId(), randomTxId());

    String newGuestTunnelRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), newGuestTunnelRequestId,
        contextOfActivateGuestTunnel(tenant, sdLanProfileId, newGuestTunnelProfile).build(Action.ACTIVATE_GUEST_TUNNEL));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), newGuestTunnelRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .hasSize(2)
        .allSatisfy(op -> {
          if (StringUtils.equals(guestTunnelProfile.getId(),
              op.getDocMap().get(Key.ID).getStringValue())) {
            assertThat(op.getDocMap())
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
          } else if (StringUtils.equals(newGuestTunnelProfile.getId(),
              op.getDocMap().get(Key.ID).getStringValue())) {
            assertThat(op.getDocMap())
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                    ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
          }
        });


    String bindGuestNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindGuestNetworkRequestId,
        contextOfActivateGuestNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_GUEST_NETWORK));
    bindGuestNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindGuestNetworkRequestId,
        contextOfActivateGuestNetwork(tenant, sdLanProfileId, tunneledWlan4).build(Action.ACTIVATE_GUEST_NETWORK));
    bindGuestNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindGuestNetworkRequestId,
        contextOfActivateGuestNetwork(tenant, sdLanProfileId, tunneledWlan5).build(Action.ACTIVATE_GUEST_NETWORK));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), bindGuestNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(newGuestTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(2)
                .extracting(Value::getStringValue)
                // same networkId (of network1) in tunneledWlan1 and tunneledWlan5
                .containsExactlyInAnyOrder(network1.getId(), network4.getId()));


    final String unbindGuestNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), unbindGuestNetworkRequestId,
        contextOfActivateGuestNetwork(tenant, sdLanProfileId, tunneledWlan4).build(Action.DEACTIVATE_GUEST_NETWORK));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), unbindGuestNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(newGuestTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .containsEntry(Key.NETWORK_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(network1.getId()))));

    final String deleteGuestTunnelRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), deleteGuestTunnelRequestId,
        contextOfDelete(tenant, sdLanProfileId).build(Action.DEACTIVATE_GUEST_TUNNEL));
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .isNotEmpty()
        .allSatisfy(rs -> assertThat(rs.getGuestTrafficTunnelProfile()).isNull());

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), deleteGuestTunnelRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(newGuestTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
        .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));


    final String enableGuestSettingRequestId = randomTxId();
    EdgeSdLanService edgeSdLanService = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .setGuestTunnelProfileId(newGuestTunnelProfile.getId())
        .setAction(Action.ENABLE_GUEST_FEATURE)
        .addAllTunneledGuestWlans(List.of(tunneledWlan3, tunneledWlan4, tunneledWlan5))
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), enableGuestSettingRequestId, edgeSdLanService);
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .allSatisfy(rs -> assertThat(rs.getGuestTrafficTunnelProfile().getId())
            .isEqualTo(newGuestTunnelProfile.getId()));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), enableGuestSettingRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(newGuestTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(3)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(network3.getId(), network4.getId(), network1.getId()));

    final String disableGuestSettingRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), disableGuestSettingRequestId,
        contextOfDelete(tenant, sdLanProfileId).build(Action.DISABLE_GUEST_FEATURE));
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .allSatisfy(rs -> assertThat(rs.getGuestTrafficTunnelProfile()).isNull());

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), disableGuestSettingRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(newGuestTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
        .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
  }

  @Test
  void testCreateUpdateDeleteSdLanProfileWithoutNetworks(Tenant tenant, Venue venue, TunnelProfile tunnelProfile) {
    String sdLanProfileId = txCtxExtension.newRandomId();
    String requestId = randomTxId();
    EdgeSdLanService createSdLan = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .setVenueId(venue.getId())
        .setTunnelProfileId(tunnelProfile.getId())
        .setAction(Action.CREATE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), requestId, createSdLan);

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .containsEntry(Key.NETWORK_SEGMENTATION_IDS, ValueUtils.listValue(List.of()))
        .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));


    TunnelProfile newTunnelProfile = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> t.setName(randomString().generate()));
    repositoryUtil.createOrUpdate(newTunnelProfile, tenant.getId(), randomTxId());

    String requestId2 = randomTxId();
    EdgeSdLanService edgeSdLanService = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .setVenueId(venue.getId())
        .setTunnelProfileId(newTunnelProfile.getId())
        .setAction(Action.UPDATE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), requestId2, edgeSdLanService);

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId2)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .hasSize(2)
        .allSatisfy(op -> {
          if (StringUtils.equals(tunnelProfile.getId(),
              op.getDocMap().get(Key.ID).getStringValue())) {
            assertThat(op.getDocMap())
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
          } else if (StringUtils.equals(newTunnelProfile.getId(),
              op.getDocMap().get(Key.ID).getStringValue())) {
            assertThat(op.getDocMap())
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                    ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
          }
        });


    String requestId3 = randomTxId();
    EdgeSdLanService deleteSdLan = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .setAction(Action.DELETE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), requestId3, deleteSdLan);

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId3)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(newTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
        .containsEntry(Key.NETWORK_SEGMENTATION_IDS, ValueUtils.listValue(List.of()))
        .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
  }



  @Test
  void bindNetworkAndChangeTunnelProfileInSdLanProfile(Tenant tenant, Venue venue, TunnelProfile tunnelProfile) {
    String sdLanProfileId = txCtxExtension.newRandomId();
    String requestId1 = randomTxId();
    EdgeSdLanService createSdLan = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .setTunnelProfileId(tunnelProfile.getId())
        .setAction(Action.CREATE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), requestId1, createSdLan);

    List<String> networkIds = createDpskNetwork(tenant, venue, 3);

    networkIds.forEach(networkId -> {
      String requestId2 = randomTxId();
      EdgeSdLanService bindNetwork = EdgeSdLanService.newBuilder()
          .setId(sdLanProfileId)
          .setTenantId(tenant.getId())
          .addTunneledWlans(TunneledWlan.newBuilder()
              .setNetworkId(networkId).setVenueId(venue.getId())
              .build())
          .setAction(Action.ACTIVATE_NETWORK)
          .build();
      messageUtil.sendEdgeSdLanService(tenant.getId(), requestId2, bindNetwork);
    });


    TunnelProfile newTunnelProfile = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> t.setName(randomString().generate()));
    repositoryUtil.createOrUpdate(newTunnelProfile, tenant.getId(), randomTxId());

    String requestId3 = randomTxId();
    EdgeSdLanService updateSdLanService = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .setTunnelProfileId(newTunnelProfile.getId())
        .setAction(Action.UPDATE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), requestId3, updateSdLanService);

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId3)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .as("The total operation count should be 3").hasSize(3)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .as("The modify WlanVenue operation count should be 2").hasSize(3)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .allSatisfy(wlanVenue -> {
          assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(newTunnelProfile.getId());
          assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
        });

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId3)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .hasSize(2)
        .allSatisfy(op -> {
          if (StringUtils.equals(tunnelProfile.getId(), op.getId())) {
            assertThat(op.getDocMap())
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                .containsEntry(Key.NETWORK_SEGMENTATION_IDS, ValueUtils.listValue(List.of()))
                .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
          } else if (StringUtils.equals(newTunnelProfile.getId(), op.getId())) {
            assertThat(op.getDocMap())
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                    ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                .containsEntry(Key.NETWORK_SEGMENTATION_IDS, ValueUtils.listValue(List.of()))
                .hasEntrySatisfying(Key.NETWORK_IDS,
                    networkIdListValue -> assertThat(networkIdListValue.getListValue())
                        .extracting(ListValue::getValuesList,
                            InstanceOfAssertFactories.list(Value.class))
                        .hasSize(networkIds.size())
                        .extracting(Value::getStringValue)
                        .containsExactlyInAnyOrderElementsOf(networkIds));
          }
        });
  }

  @Test
  void bindNetworkAndUnbindNetworkInSdLanProfile(Tenant tenant, Venue venue, TunnelProfile tunnelProfile) {
    String sdLanProfileId = txCtxExtension.newRandomId();
    String requestId1 = randomTxId();
    EdgeSdLanService createSdLan = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .setTunnelProfileId(tunnelProfile.getId())
        .setAction(Action.CREATE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), requestId1, createSdLan);

    List<String> networkIds = createDpskNetwork(tenant, venue, 2);

    String requestId2 = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), requestId2, EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(networkIds.get(0)).setVenueId(venue.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());
    requestId2 = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), requestId2, EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(networkIds.get(1)).setVenueId(venue.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId2)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .as("The total operation count should be 1").hasSize(1)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .as("The modify WlanVenue operation count should be 1").hasSize(1)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .allSatisfy(wlanVenue -> {
          assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(
              tunnelProfile.getId());
          assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
        });

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId2)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(networkIds.size())
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrderElementsOf(networkIds));


    String removeNetwork = networkIds.get(0);
    String requestId3 = randomTxId();
    EdgeSdLanService unbindNetwork = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(removeNetwork).setVenueId(venue.getId())
            .build())
        .setAction(Action.DEACTIVATE_NETWORK)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), requestId3, unbindNetwork);

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId3)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .as("The total operation count should be 1").hasSize(1)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .as("The modify WlanVenue operation count should be 1").hasSize(1)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .allSatisfy(wlanVenue -> {
          assertThat(wlanVenue.getWlanId()).isEqualTo(removeNetwork);
          assertThat(wlanVenue.getVxlanTunnelProfileId()).isEmpty();
          assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
        });

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId3)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .containsEntry(Key.NETWORK_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(networkIds.get(1)))));
  }

  /*
  sd1 -> rs1 , v1, t1, g(t1)
            \-> n1 , isGuest:true
            \-> n2 , isGuest:false
  sd2 -> rs2 , v2, t1, g(t2)
            \-> n2 , isGuest:true
            \-> n3 , isGuest:false
            \-> n4 , isGuest:true
  sd3 -> rs3 , v3, t2, g(t1)
           \-> n1 , isGuest:true
           \-> n5 , isGuest:true
  sd4 -> rs4 , v4, t2, g(t2)
           \-> n3 , isGuest:true
           \-> n4 , isGuest:true
           \-> n6 , isGuest:false
           \-> n7 , isGuest:false
   */
  @Test
  void enableGuestFeatureAndDisableGuestFeatureInSdLanProfile(Tenant tenant, Venue venue, TunnelProfile tunnelProfile) {
    //prepare test data
    Venue venue2 = VenueTestFixture.randomVenue(tenant);
    Venue venue3 = VenueTestFixture.randomVenue(tenant);
    Venue venue4 = VenueTestFixture.randomVenue(tenant);
    repositoryUtil.createOrUpdate(venue2, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(venue3, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(venue4, tenant.getId(), randomTxId());
    TunnelProfile tunnelProfile2 = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> t.setName(randomString().generate()));
    repositoryUtil.createOrUpdate(tunnelProfile2, tenant.getId(), randomTxId());

    final var networks = createAndSaveOpenNetworks(tenant, 7);
    Network network1 = networks.get(0);
    Network network2 = networks.get(1);
    Network network3 = networks.get(2);
    Network network4 = networks.get(3);
    Network network5 = networks.get(4);
    Network network6 = networks.get(5);
    Network network7 = networks.get(6);
    createAndSaveNetworkVenues(tenant, venue, List.of(network1, network2));
    createAndSaveNetworkVenues(tenant, venue2, List.of(network2, network3, network4));
    createAndSaveNetworkVenues(tenant, venue3, List.of(network1, network5));
    createAndSaveNetworkVenues(tenant, venue4, List.of(network3, network4, network6, network7));

    String sdLanProfileId1 = txCtxExtension.newRandomId();
    String sdLanProfileId2 = txCtxExtension.newRandomId();
    String sdLanProfileId3 = txCtxExtension.newRandomId();
    String sdLanProfileId4 = txCtxExtension.newRandomId();
    String createSdLanRequestId = randomTxId();
    EdgeSdLanService createSdLan = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId1)
        .setTenantId(tenant.getId())
        .setTunnelProfileId(tunnelProfile.getId())
        .setAction(Action.CREATE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId, createSdLan);

    createSdLanRequestId = randomTxId();
    createSdLan = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId2)
        .setTenantId(tenant.getId())
        .setTunnelProfileId(tunnelProfile.getId())
        .setAction(Action.CREATE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId, createSdLan);

    createSdLanRequestId = randomTxId();
    createSdLan = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId3)
        .setTenantId(tenant.getId())
        .setTunnelProfileId(tunnelProfile2.getId())
        .setAction(Action.CREATE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId, createSdLan);

    createSdLanRequestId = randomTxId();
    createSdLan = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId4)
        .setTenantId(tenant.getId())
        .setTunnelProfileId(tunnelProfile2.getId())
        .setAction(Action.CREATE_SD_LAN)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId, createSdLan);

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId1)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network1.getId()).setVenueId(venue.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId1)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network2.getId()).setVenueId(venue.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId2)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network2.getId()).setVenueId(venue2.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId2)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network3.getId()).setVenueId(venue2.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId2)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network4.getId()).setVenueId(venue2.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId3)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network1.getId()).setVenueId(venue3.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId3)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network5.getId()).setVenueId(venue3.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId4)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network3.getId()).setVenueId(venue4.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId4)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network4.getId()).setVenueId(venue4.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId4)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network6.getId()).setVenueId(venue4.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(), EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId4)
        .setTenantId(tenant.getId())
        .addTunneledWlans(TunneledWlan.newBuilder()
            .setNetworkId(network7.getId()).setVenueId(venue4.getId())
            .build())
        .setAction(Action.ACTIVATE_NETWORK)
        .build());

    String bindGuestNetworkRequestId1 = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindGuestNetworkRequestId1, EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId1)
        .setTenantId(tenant.getId())
        .setGuestTunnelProfileId(tunnelProfile.getId())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network1.getId()).setVenueId(venue.getId())
            .build())
        .setAction(Action.ENABLE_GUEST_FEATURE)
        .build());

    String bindGuestNetworkRequestId2 = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindGuestNetworkRequestId2, EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId2)
        .setTenantId(tenant.getId())
        .setGuestTunnelProfileId(tunnelProfile2.getId())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network2.getId()).setVenueId(venue2.getId())
            .build())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network4.getId()).setVenueId(venue2.getId())
            .build())
        .setAction(Action.ENABLE_GUEST_FEATURE)
        .build());

    String bindGuestNetworkRequestId3 = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindGuestNetworkRequestId3, EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId3)
        .setTenantId(tenant.getId())
        .setGuestTunnelProfileId(tunnelProfile.getId())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network1.getId()).setVenueId(venue3.getId())
            .build())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network5.getId()).setVenueId(venue3.getId())
            .build())
        .setAction(Action.ENABLE_GUEST_FEATURE)
        .build());

    String bindGuestNetworkRequestId4 = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), bindGuestNetworkRequestId4, EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId4)
        .setTenantId(tenant.getId())
        .setGuestTunnelProfileId(tunnelProfile2.getId())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network3.getId()).setVenueId(venue4.getId())
            .build())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network4.getId()).setVenueId(venue4.getId())
            .build())
        .setAction(Action.ENABLE_GUEST_FEATURE)
        .build());

    /*
    tunnel result:
      t1 : 5 network -> n1, n2, n3, n4, n5(guest),
           3 sdLan   -> sd1, sd2, sd3
      t2 : 7 network -> n1, n2(guest), n3(guest), n4(guest), n5, n6, n7
           3 sdLan   -> sd2, sd3, sd4
     */
    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), bindGuestNetworkRequestId4)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile2.getId()))
        .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS, centralizedForwardingIdListValue ->
            assertThat(centralizedForwardingIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(3)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(sdLanProfileId2, sdLanProfileId3, sdLanProfileId4))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(networks.size())
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrderElementsOf(networks.stream().map(Network::getId).toList()));


    String unbindGuestNetworkRequestId = randomTxId();
    EdgeSdLanService unbindGuestNetwork = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId1)
        .setTenantId(tenant.getId())
        .setGuestTunnelProfileId(tunnelProfile.getId())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network1.getId()).setVenueId(venue.getId())
            .build())
        .setAction(Action.DISABLE_GUEST_FEATURE)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), unbindGuestNetworkRequestId, unbindGuestNetwork);

    /*
    tunnel result:
      t1 : 5 network -> n1, n2, n3, n4, n5 (only remove guest tunnel profile),
           3 sdLan   -> sd1, sd2, sd3
     */
    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), unbindGuestNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
        .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS, centralizedForwardingIdListValue ->
            assertThat(centralizedForwardingIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(3)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(sdLanProfileId1, sdLanProfileId2, sdLanProfileId3))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(5)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(network1.getId(), network2.getId(), network3.getId(),
                    network4.getId(), network5.getId()));


    unbindGuestNetworkRequestId = randomTxId();
    unbindGuestNetwork = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId3)
        .setTenantId(tenant.getId())
        .setGuestTunnelProfileId(tunnelProfile.getId())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network1.getId()).setVenueId(venue3.getId())
            .build())
        .addTunneledGuestWlans(TunneledWlan.newBuilder()
            .setNetworkId(network5.getId()).setVenueId(venue3.getId())
            .build())
        .setAction(Action.DISABLE_GUEST_FEATURE)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), unbindGuestNetworkRequestId, unbindGuestNetwork);

    /*
    tunnel result:
      t1 : 5 network -> n1, n2, n3, n4
           3 sdLan   -> sd1, sd2
     */
    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), unbindGuestNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
        .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS, centralizedForwardingIdListValue ->
            assertThat(centralizedForwardingIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(2)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(sdLanProfileId1, sdLanProfileId2))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(4)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(network1.getId(), network2.getId(), network3.getId(),
                    network4.getId()));
  }

  private List<OpenNetwork> createAndSaveOpenNetworks(Tenant tenant, int count) {
    final var generator = network(OpenNetwork.class).setTenant(always(tenant));
    return IntStream.range(0, count).mapToObj(i -> {
      final var openNetwork = generator.generate();
      openNetwork.getWlan().setNetwork(openNetwork);
      openNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
      return repositoryUtil.createOrUpdate(openNetwork);
    }).toList();
  }

  private void createAndSaveNetworkVenues(Tenant tenant, Venue venue, List<? extends Network> networks) {
    networks.forEach(n -> {
      final var networkVenue = Generators.networkVenue()
          .setNetwork(always(n)).setVenue(always(venue))
          .setIsTemplate(always(n.getIsTemplate())).generate();
      n.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId());
    });
  }


  private List<String> createDpskNetwork(Tenant tenant, Venue venue, int count) {
    List<Network> networks = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      final var authRadius = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());

      final var dpskNetwork = network(DpskNetwork.class).setName(randomString()).generate();
      dpskNetwork.getWlan().setNetwork(dpskNetwork);
      dpskNetwork.setAuthRadius(authRadius);
      dpskNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
      authRadius.setAuthNetworks(List.of(dpskNetwork));
      repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());

      final var authRadiusService = new AuthRadiusService();
      authRadiusService.setRadius(authRadius);
      repositoryUtil.createOrUpdate(authRadiusService, tenant.getId(), randomTxId());

      final var networkVenue = Generators.networkVenue()
          .setNetwork(always(dpskNetwork)).setVenue(always(venue)).generate();
      dpskNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      networks.add(dpskNetwork);
    }

    return networks.stream().map(Network::getId).collect(Collectors.toList());
  }

  private List<String> createOweOpenNetwork(Tenant tenant, Venue venue, int count) {
    List<Network> networks = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      var authRadius = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());
      var owePairNetworkId = randomTxId();
      var oweNetwork = network(OpenNetwork.class).setName(randomString()).generate();
      oweNetwork.getWlan().setNetwork(oweNetwork);
      oweNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.OWE);
      oweNetwork.getWlan().setBypassCPUsingMacAddressAuthentication(true);
      oweNetwork.getWlan().setBypassCNA(true);
      oweNetwork.setIsOweMaster(true);
      oweNetwork.setOwePairNetworkId(owePairNetworkId);
      oweNetwork.setAuthRadius(authRadius);
      authRadius.setAuthNetworks(List.of(oweNetwork));
      repositoryUtil.createOrUpdate(oweNetwork, tenant.getId(), randomTxId());

      var owePairNetwork = NetworkTestFixture.randomOpenNetwork(tenant, n -> {
        n.setId(owePairNetworkId);
      });
      repositoryUtil.createOrUpdate(owePairNetwork, tenant.getId(), randomTxId());


      final var networkVenue = Generators.networkVenue()
          .setNetwork(always(oweNetwork)).setVenue(always(venue)).generate();
      oweNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      networks.add(oweNetwork);
    }

    return networks.stream().map(Network::getId).collect(Collectors.toList());
  }

  @Test
  @FeatureFlag(disable = EDGE_L2GRE_TOGGLE)
  void whenChangingGuestTunnelProfileTheForwardingProfileShouldAlsoBeChanged(Tenant tenant, Venue venue1,
      TunnelProfile tunnelProfile1) {
    //prepare test data
    Venue venue2 = VenueTestFixture.randomVenue(tenant);
    repositoryUtil.createOrUpdate(venue2, tenant.getId(), randomTxId());

    final var networks = createAndSaveOpenNetworks(tenant, 2);
    Network network1 = networks.get(0);
    Network network2 = networks.get(1);
    createAndSaveNetworkVenues(tenant, venue1, List.of(network1, network2));
    createAndSaveNetworkVenues(tenant, venue2, List.of(network1));

    TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue1).isGuestTunnelUtilized(true).build();
    TunneledWlan tunneledWlan2 = TunneledWlanContext.of(network2, venue1).isGuestTunnelUtilized(false).build();
    TunneledWlan tunneledWlan3 = TunneledWlanContext.of(network1, venue2).isGuestTunnelUtilized(true).build();

    String sdLanProfileId = txCtxExtension.newRandomId();
    String connectResourceId = txCtxExtension.newRandomId();

    ChangedConnectResource connectResource = ChangedConnectResource.newBuilder()
        .setConnectResourceId(connectResourceId)
        .setSdLanId(sdLanProfileId)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOf(tenant, sdLanProfileId, tunnelProfile1).with(connectResource).build(Action.CREATE_SD_LAN));

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.ACTIVATE_NETWORK));

    TunnelProfile guestTunnelProfile1 = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> t.setName(randomString().generate()));
    repositoryUtil.createOrUpdate(guestTunnelProfile1, tenant.getId(), randomTxId());

    String addGuestTunnelRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), addGuestTunnelRequestId,
        contextOfActivateGuestTunnel(tenant, sdLanProfileId, guestTunnelProfile1).build(Action.ACTIVATE_GUEST_TUNNEL));
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .hasSize(2)
        .flatExtracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings)
        .hasSize(3)
        .filteredOn(nm -> null != nm.getForwardingTunnelProfile())
        .allSatisfy(mapping ->
            assertThat(mapping.getForwardingTunnelProfile().getId()).isEqualTo(guestTunnelProfile1.getId()));

    TunnelProfile guestTunnelProfile2 = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> t.setName(randomString().generate()));
    repositoryUtil.createOrUpdate(guestTunnelProfile2, tenant.getId(), randomTxId());

    addGuestTunnelRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), addGuestTunnelRequestId,
        contextOfActivateGuestTunnel(tenant, sdLanProfileId, guestTunnelProfile2).build(Action.ACTIVATE_GUEST_TUNNEL));
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .hasSize(2)
        .flatExtracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings)
        .hasSize(3)
        .filteredOn(nm -> null != nm.getForwardingTunnelProfile())
        .allSatisfy(mapping ->
            assertThat(mapping.getForwardingTunnelProfile().getId()).isEqualTo(guestTunnelProfile2.getId()));

    String deleteGuestTunnelRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), deleteGuestTunnelRequestId,
        contextOfDelete(tenant, sdLanProfileId).build(Action.DEACTIVATE_GUEST_TUNNEL));
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .hasSize(2)
        .flatExtracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings)
        .hasSize(3)
        .allSatisfy(mapping ->
            assertThat(mapping.getForwardingTunnelProfile()).isNull());

    String enableGuestSettingRequestId = randomTxId();
    EdgeSdLanService edgeSdLanService = EdgeSdLanService.newBuilder()
        .setId(sdLanProfileId)
        .setTenantId(tenant.getId())
        .setGuestTunnelProfileId(guestTunnelProfile1.getId())
        .setAction(Action.ENABLE_GUEST_FEATURE)
        .addAllTunneledGuestWlans(List.of(tunneledWlan1, tunneledWlan2, tunneledWlan3))
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), enableGuestSettingRequestId, edgeSdLanService);

    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .hasSize(2)
        .flatExtracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings)
        .hasSize(3)
        .filteredOn(nm -> null != nm.getForwardingTunnelProfile())
        .allSatisfy(mapping ->
            assertThat(mapping.getForwardingTunnelProfile().getId()).isEqualTo(guestTunnelProfile1.getId()));

    String disableGuestSettingRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), disableGuestSettingRequestId,
        contextOfDelete(tenant, sdLanProfileId).build(Action.DISABLE_GUEST_FEATURE));
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .hasSize(2)
        .flatExtracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings)
        .hasSize(3)
        .allSatisfy(mapping ->
            assertThat(mapping.getForwardingTunnelProfile()).isNull());
  }


  @Test
  @FeatureFlag(enable = EDGE_L2GRE_TOGGLE)
  void updateForwardingProfileInSdLanNetwork_shouldChangeCentralizedForwardingIdsToES(Tenant tenant, Venue venue,
      TunnelProfile tunnelProfile) {
    //prepare test data
    Venue venue2 = VenueTestFixture.randomVenue(tenant);
    repositoryUtil.createOrUpdate(venue2, tenant.getId(), randomTxId());

    final var networks = createAndSaveOpenNetworks(tenant, 2);
    Network network1 = networks.get(0);
    Network network2 = networks.get(1);
    createAndSaveNetworkVenues(tenant, venue, List.of(network1, network2));
    createAndSaveNetworkVenues(tenant, venue2, List.of(network1));

    TunnelProfile l2GreTunnel1 = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> {
          t.setName(randomString().generate());
          t.setTunnelType(TunnelTypeEnum.L2GRE);
        });
    repositoryUtil.createOrUpdate(l2GreTunnel1, tenant.getId(), randomTxId());

    TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue).withFwdTunnelProfile(l2GreTunnel1).build();
    TunneledWlan tunneledWlan2 = TunneledWlanContext.of(network1, venue2).withFwdTunnelProfile(l2GreTunnel1).build();
    TunneledWlan tunneledWlan3 = TunneledWlanContext.of(network2, venue).withFwdTunnelProfile(l2GreTunnel1).build();

    String sdLanProfileId = txCtxExtension.newRandomId();
    String connectResourceId = txCtxExtension.newRandomId();

    ChangedConnectResource connectResource = ChangedConnectResource.newBuilder()
        .setConnectResourceId(connectResourceId)
        .setSdLanId(sdLanProfileId)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOf(tenant, sdLanProfileId, tunnelProfile).with(connectResource).build(Action.CREATE_SD_LAN));
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

    String activateNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), activateNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.ACTIVATE_NETWORK));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .filteredOn(op -> StringUtils.equals(op.getId(), l2GreTunnel1.getId()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(l2GreTunnel1.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .hasEntrySatisfying(Key.NETWORK_IDS, networkIdListValue ->
            assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .flatExtracting(Value::getStringValue)
                .containsExactlyInAnyOrder(
                    network1.getId(), network2.getId()
                )
        );
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.DEACTIVATE_NETWORK));
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.DEACTIVATE_NETWORK));

    String deactivateNetworkRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), deactivateNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan3).build(Action.DEACTIVATE_NETWORK));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), deactivateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .filteredOn(op -> StringUtils.equals(op.getId(), l2GreTunnel1.getId()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(l2GreTunnel1.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(Collections.emptyList()))
        .containsEntry(Key.NETWORK_IDS,
            ValueUtils.listValue(Collections.emptyList()));
  }

  @Test
  @FeatureFlag(enable = EDGE_L2GRE_TOGGLE)
  void whenNetworkAndVenueAlreadyExisted_shouldSkipBindNetworkProcess_L2GreFFIsEnabled(Tenant tenant, Venue venue, TunnelProfile tunnelProfile) {
    final var networks = createAndSaveOpenNetworks(tenant, 1);
    Network network1 = networks.get(0);
    createAndSaveNetworkVenues(tenant, venue, List.of(network1));

    TunnelProfile l2GreTunnel1 = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> {
          t.setName(randomString().generate());
          t.setTunnelType(TunnelTypeEnum.L2GRE);
        });
    repositoryUtil.createOrUpdate(l2GreTunnel1, tenant.getId(), randomTxId());

    String sdLanProfileId = txCtxExtension.newRandomId();
    String connectResourceId = txCtxExtension.newRandomId();
    ChangedConnectResource connectResource = ChangedConnectResource.newBuilder()
        .setConnectResourceId(connectResourceId)
        .setSdLanId(sdLanProfileId)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOf(tenant, sdLanProfileId, tunnelProfile).with(connectResource).build(Action.CREATE_SD_LAN));

    String activateNetworkRequestId = randomTxId();
    TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue).withFwdTunnelProfile(l2GreTunnel1).build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), activateNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty();
    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty();

    String sameNetworkVenueAndFwdProfileRequestId = randomTxId();
    TunneledWlan tunneledWlan2 = TunneledWlanContext.of(network1, venue).withFwdTunnelProfile(l2GreTunnel1).build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), sameNetworkVenueAndFwdProfileRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), sameNetworkVenueAndFwdProfileRequestId)).isNull();

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), sameNetworkVenueAndFwdProfileRequestId)).isNull();
  }

  @Test
  @FeatureFlag(disable = EDGE_L2GRE_TOGGLE)
  void whenNetworkAndVenueAlreadyExisted_shouldSkipBindNetworkProcess_L2GreFFIsDisabled(Tenant tenant, Venue venue, TunnelProfile tunnelProfile) {
    final var networks = createAndSaveOpenNetworks(tenant, 1);
    Network network1 = networks.get(0);
    createAndSaveNetworkVenues(tenant, venue, List.of(network1));

    String sdLanProfileId = txCtxExtension.newRandomId();
    String connectResourceId = txCtxExtension.newRandomId();
    ChangedConnectResource connectResource = ChangedConnectResource.newBuilder()
        .setConnectResourceId(connectResourceId)
        .setSdLanId(sdLanProfileId)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOf(tenant, sdLanProfileId, tunnelProfile).with(connectResource).build(Action.CREATE_SD_LAN));

    String activateNetworkRequestId = randomTxId();
    TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue).build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), activateNetworkRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty();
    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty();

    String sameNetworkVenueRequestId = randomTxId();
    TunneledWlan tunneledWlan2 = TunneledWlanContext.of(network1, venue).build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), sameNetworkVenueRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan2).build(Action.ACTIVATE_NETWORK));

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), sameNetworkVenueRequestId)).isNull();

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), sameNetworkVenueRequestId)).isNull();
  }

  @Test
  @FeatureFlag(enable = EDGE_L2GRE_TOGGLE)
  void changeExistingForwardingProfileInSdLanNetwork_shouldCompleteBindNetworkProcess(Tenant tenant, Venue venue,
      TunnelProfile tunnelProfile) {
    final var networks = createAndSaveOpenNetworks(tenant, 1);
    Network network1 = networks.get(0);
    createAndSaveNetworkVenues(tenant, venue, List.of(network1));

    TunnelProfile l2GreTunnel = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> {
          t.setName(randomString().generate());
          t.setTunnelType(TunnelTypeEnum.L2GRE);
        });
    repositoryUtil.createOrUpdate(l2GreTunnel, tenant.getId(), randomTxId());

    TunneledWlan tunneledWlan1 = TunneledWlanContext.of(network1, venue).withFwdTunnelProfile(l2GreTunnel).build();

    String sdLanProfileId = txCtxExtension.newRandomId();
    String connectResourceId = txCtxExtension.newRandomId();

    ChangedConnectResource connectResource = ChangedConnectResource.newBuilder()
        .setConnectResourceId(connectResourceId)
        .setSdLanId(sdLanProfileId)
        .build();
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOf(tenant, sdLanProfileId, tunnelProfile).with(connectResource).build(Action.CREATE_SD_LAN));
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlan1).build(Action.ACTIVATE_NETWORK));


    TunnelProfile newL2GreTunnel = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> {
          t.setName(randomString().generate());
          t.setTunnelType(TunnelTypeEnum.L2GRE);
        });
    repositoryUtil.createOrUpdate(newL2GreTunnel, tenant.getId(), randomTxId());
    TunneledWlan newTunneledWlan = TunneledWlanContext.of(network1, venue).withFwdTunnelProfile(newL2GreTunnel).build();

    String activateNewFwdProfileRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), activateNewFwdProfileRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, newTunneledWlan).build(Action.ACTIVATE_NETWORK));

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNewFwdProfileRequestId))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty();

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNewFwdProfileRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> StringUtils.equals(op.getId(), newL2GreTunnel.getId())
                  && op.getOpType() == OpType.MOD)
              .singleElement()
              .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
              .containsEntry(Key.ID, ValueUtils.stringValue(newL2GreTunnel.getId()))
              .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                  ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
              .hasEntrySatisfying(Key.NETWORK_IDS, networkIdListValue ->
                  assertThat(networkIdListValue.getListValue())
                      .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                      .flatExtracting(Value::getStringValue)
                      .containsExactly(network1.getId())
              );
          assertThat(ops)
              .filteredOn(op -> StringUtils.equals(op.getId(), l2GreTunnel.getId())
                  && op.getOpType() == OpType.MOD)
              .singleElement()
              .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
              .containsEntry(Key.ID, ValueUtils.stringValue(l2GreTunnel.getId()))
              .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                  ValueUtils.listValue(Collections.emptyList()))
              .containsEntry(Key.NETWORK_IDS,
                  ValueUtils.listValue(Collections.emptyList()));
        });

    // Core-port as forwarding destination
    TunneledWlan tunneledWlanWithEmptyFwdTunnelProfileId = TunneledWlanContext.of(network1, venue).withFwdTunnelProfile(null).build();

    String activateWithEmptyFwdTunnelProfileIdRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(),
        activateWithEmptyFwdTunnelProfileIdRequestId,
        contextOfActivateNetwork(tenant, sdLanProfileId, tunneledWlanWithEmptyFwdTunnelProfileId).build(Action.ACTIVATE_NETWORK));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateWithEmptyFwdTunnelProfileIdRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList, InstanceOfAssertFactories.list(Operations.class)).isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .filteredOn(op -> op.getId().equals(newL2GreTunnel.getId()))
        .singleElement()
        .satisfies(op -> {
          assertThat(op.getOpType()).isEqualTo(OpType.MOD);
          assertThat(op.getDocMap())
              .containsEntry(Key.ID, ValueUtils.stringValue(newL2GreTunnel.getId()))
              .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
              .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
        });
  }

  private List<OpenNetwork> createAndSaveOpenNetworkTemplates(Tenant tenant, int count) {
    final var generator = network(OpenNetwork.class)
            .setId(nullValue(String.class))
            .setName(serialName("OpenNetworkTemplate"))
            .setTenant(always(tenant)).setIsTemplate(alwaysTrue());
    return IntStream.range(0, count).mapToObj(i -> {
      final var openNetwork = generator.generate();
      openNetwork.getWlan().setNetwork(openNetwork);
      openNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
      return repositoryUtil.createOrUpdate(openNetwork);
    }).toList();
  }
}
