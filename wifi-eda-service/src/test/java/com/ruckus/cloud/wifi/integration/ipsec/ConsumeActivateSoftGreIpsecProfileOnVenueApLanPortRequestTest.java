package com.ruckus.cloud.wifi.integration.ipsec;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_IPSEC_PSK_OVER_NETWORK;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.IpsecProfileTestFixture.randomIpsecProfile;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.viewmodel.LanPortSoftGreProfileSettings;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("IpsecProfileTest")
@WifiIntegrationTest
@FeatureFlag(
    enable = {
        WIFI_ETHERNET_SOFTGRE_TOGGLE,
        ACX_UI_ETHERNET_TOGGLE,
        WIFI_IPSEC_PSK_OVER_NETWORK
    })
public class ConsumeActivateSoftGreIpsecProfileOnVenueApLanPortRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private LanPortAdoptionDataHelper dataHelper;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  private final String portId = "1";

  @Test
  public void givenValidActivation(NetworkVenue networkVenue, Tenant tenant, Ap ap) {
    var apLanPortData = dataHelper.createApLanPortDataWithAdoption(networkVenue.getVenue(), ap,
        portId, 3);
    var softGreProfile =
        repositoryUtil.createOrUpdate(
            randomSoftGreProfile(tenant, p -> {
            }), tenant.getId());
    var ipsecProfile =
        repositoryUtil.createOrUpdate(
            randomIpsecProfile(tenant, p -> {
            }), tenant.getId());

    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        txCtxExtension.getRequestId(),
        CfgAction.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_AP_LAN_PORT,
        randomName(),
        new RequestParams()
            .addPathVariable("venueId", networkVenue.getVenue().getId())
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("lanPortId", portId)
            .addPathVariable("softGreProfileId", softGreProfile.getId())
            .addPathVariable("ipsecProfileId", ipsecProfile.getId()),
        new LanPortSoftGreProfileSettings());

    var operationList = assertThat(
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .hasSize(2);
    operationList
        .filteredOn(o -> Index.SOFT_GRE_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .hasSize(1)
        .first()
        .matches(o -> o.getOpType() == OpType.MOD)
        .matches(o -> o.getId().equals(softGreProfile.getId()))
        .matches(
            o ->
                o.getDocMap().get(Key.AP_ACTIVATIONS).getListValue().getValuesList().size()
                    == 1);
    operationList
        .filteredOn(o -> Index.IPSEC_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .hasSize(1)
        .first()
        .matches(o -> o.getOpType() == OpType.MOD)
        .matches(o -> o.getId().equals(ipsecProfile.getId()))
        .matches(
            o ->
                o.getDocMap().get(EsConstants.Key.AP_ACTIVATIONS).getListValue().getValuesList()
                    .size()
                    == 1);

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
        .matches(
            a -> a.getStep()
                .equals(ApiFlowNames.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_AP_LAN_PORT));

    validateDdccmCfgRequestMessages(softGreProfile.getId(), apLanPortData.port().getId());
  }

  @Test
  public void givenApLanPortNotExists(NetworkVenue networkVenue, Tenant tenant, Ap ap) {
    var softGreProfile =
        repositoryUtil.createOrUpdate(
            randomSoftGreProfile(tenant, p -> {
            }), tenant.getId());
    var ipsecProfile =
        repositoryUtil.createOrUpdate(
            randomIpsecProfile(tenant, p -> {
            }), tenant.getId());

    assertThatThrownBy(
        () ->
            messageUtil.sendWifiCfgRequest(
                tenant.getId(),
                txCtxExtension.getRequestId(),
                CfgAction.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_AP_LAN_PORT,
                randomName(),
                new RequestParams()
                    .addPathVariable("venueId", networkVenue.getVenue().getId())
                    .addPathVariable("serialNumber", ap.getId())
                    .addPathVariable("lanPortId", portId)
                    .addPathVariable("softGreProfileId", softGreProfile.getId())
                    .addPathVariable("ipsecProfileId", ipsecProfile.getId()),
                new LanPortSoftGreProfileSettings()))
        .isNotNull()
        .rootCause()
        .isInstanceOf(ObjectNotFoundException.class);

    messageCaptors
        .assertThat(
            messageCaptors.getDdccmMessageCaptor(),
            messageCaptors.getCmnCfgCollectorMessageCaptor())
        .doesNotSendByTenant(tenant.getId());

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .matches(a -> a.getStatus() == Status.FAIL)
        .matches(
            a -> a.getStep()
                .equals(ApiFlowNames.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_AP_LAN_PORT));
  }

  private void validateDdccmCfgRequestMessages(String softGreProfileId, String apLanPortId) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();
    final ApLanPort apLanPort = repositoryUtil.find(ApLanPort.class, apLanPortId);

    var operations =
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList)
            .asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast);

    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .isNotEmpty()
        .allSatisfy(
            op -> {
              assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                  .matches(
                      venue ->
                          venue.getCcmMultipleTunnel().getSoftGreSettingList().stream()
                              .anyMatch(
                                  softgre -> softgre.getProfileId().equals(softGreProfileId)));
            });

    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp)
        .isNotEmpty()
        .allSatisfy(
            op -> {
              assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
                  .isNotNull()
                  .matches(
                      model ->
                          model.getLanPortList().stream()
                              .anyMatch(
                                  l ->
                                      apLanPort.getLanPortAdoption().getEthernetPortProfileId()
                                          == l.getApLanPortProfileId()));
            });

    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApLanPortProfile)
        .filteredOn(operation -> operation.getAction() == Action.ADD)
        .isNotNull()
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .allSatisfy(op -> verifyAddAction(op, apLanPort, softGreProfileId));
  }

  private void verifyAddAction(
      com.ruckus.acx.ddccm.protobuf.wifi.Operation operation,
      ApLanPort apLanPort,
      String softGreProfileId) {
    assertThat(operation)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
        .matches(
            apLanPortProfile ->
                apLanPort.getLanPortAdoption().getId().equals(apLanPortProfile.getName()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getLanPortAdoption()
                    .getEthernetPortProfileId()
                    .equals(apLanPortProfile.getId()))
        .matches(
            apLanPortProfile ->
                (apLanPort.getApLanPortProfile().getType().name() + "PORT")
                    .equals(apLanPortProfile.getLanPortType().name()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagId()))
        .matches(
            apLanPortProfile ->
                softGreProfileId.equals(apLanPortProfile.getTunnelProfile().getProfileId()));
  }
}
