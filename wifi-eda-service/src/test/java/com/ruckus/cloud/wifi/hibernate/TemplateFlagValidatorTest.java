package com.ruckus.cloud.wifi.hibernate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import jakarta.persistence.EntityManagerFactory;
import org.hibernate.event.service.spi.EventListenerGroup;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.event.spi.PreInsertEvent;
import org.hibernate.event.spi.PreUpdateEvent;
import org.hibernate.internal.SessionFactoryImpl;
import org.hibernate.service.spi.ServiceRegistryImplementor;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(TxCtxExtension.class)
public class TemplateFlagValidatorTest {

  @Test
  void testInit() {
    var entityManagerFactory = mock(EntityManagerFactory.class);
    var sessionFactory = mock(SessionFactoryImpl.class);
    var unit = new TemplateFlagValidator(entityManagerFactory);

    var eventListenerRegistry = mock(EventListenerRegistry.class);
    var serviceRegistry = mock(ServiceRegistryImplementor.class);
    var eventListenerGroup = mock(EventListenerGroup.class);

    doReturn(sessionFactory).when(entityManagerFactory).unwrap(any());
    doReturn(serviceRegistry).when(sessionFactory).getServiceRegistry();
    doReturn(eventListenerRegistry).when(serviceRegistry).getService(any());
    doReturn(eventListenerGroup).when(eventListenerRegistry).getEventListenerGroup(any());

    unit.init();

    verify(eventListenerRegistry, times(1)).getEventListenerGroup(eq(EventType.PRE_INSERT));
    verify(eventListenerRegistry, times(1)).getEventListenerGroup(eq(EventType.PRE_UPDATE));
    verify(eventListenerGroup, times(2)).appendListener(eq(unit));
  }

  @Test
  void testPreInsert() {
    var event = mock(PreInsertEvent.class);
    var entity = new Venue();
    var unit = new TemplateFlagValidator(null);

    doReturn(entity).when(event).getEntity();
    entity.setIsTemplate(true);

    unit.onPreInsert(event);

    assertThat(entity.getIsTemplate()).isTrue();
  }

  @Test
  void testPreInsert_ResetTemplateFlag() {
    var event = mock(PreInsertEvent.class);
    var entity = new Venue();
    var unit = new TemplateFlagValidator(null);

    doReturn(entity).when(event).getEntity();
    unit.setResetTemplateFlag(true);
    entity.setIsTemplate(true);

    unit.onPreInsert(event);

    assertThat(entity.getIsTemplate()).isFalse();
  }

  @Test
  void testPreInsert_ResetTemplateFlag_NoTxCtx() {
    TxCtxHolder.clear();

    var event = mock(PreInsertEvent.class);
    var entity = new Venue();
    var unit = new TemplateFlagValidator(null);

    doReturn(entity).when(event).getEntity();
    unit.setResetTemplateFlag(true);
    entity.setIsTemplate(true);

    unit.onPreInsert(event);

    assertThat(entity.getIsTemplate()).isTrue();
  }

  @Test
  void testPreInsert_ResetTemplateFlag_NotTemplateEntity() {
    var event = mock(PreInsertEvent.class);
    var entity = new Ap();
    var unit = new TemplateFlagValidator(null);

    doReturn(entity).when(event).getEntity();
    unit.setResetTemplateFlag(true);

    assertThatNoException().isThrownBy(() -> unit.onPreInsert(event));
  }

  @Test
  void testPreUpdate() {
    var event = mock(PreUpdateEvent.class);
    var entity = new Venue();
    var unit = new TemplateFlagValidator(null);

    doReturn(entity).when(event).getEntity();
    entity.setIsTemplate(true);

    unit.onPreUpdate(event);

    assertThat(entity.getIsTemplate()).isTrue();
  }

  @Test
  void testPreUpdate_ResetTemplateFlag() {
    var event = mock(PreUpdateEvent.class);
    var entity = new Venue();
    var unit = new TemplateFlagValidator(null);

    doReturn(entity).when(event).getEntity();
    unit.setResetTemplateFlag(true);
    entity.setIsTemplate(true);

    unit.onPreUpdate(event);

    assertThat(entity.getIsTemplate()).isFalse();
  }

}
