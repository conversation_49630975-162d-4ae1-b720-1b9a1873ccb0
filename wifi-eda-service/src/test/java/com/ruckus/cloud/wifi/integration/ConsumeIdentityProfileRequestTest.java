package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.reflect.TypeToken;
import com.google.protobuf.Message;
import com.google.protobuf.ProtocolStringList;
import com.google.protobuf.StringValue;
import com.ruckus.cloud.entity.association.protobuf.AssociatedProfile;
import com.ruckus.cloud.entity.association.protobuf.EntityAssociation;
import com.ruckus.cloud.entity.association.protobuf.EntityAssociationList;
import com.ruckus.cloud.entity.association.protobuf.EntityType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.Operation;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeIdentityProfileRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  private void validateWifiCfgChangeMessageInternal(
      TxCtx txCtx,
      Predicate<Operation> operationFilter,
      Function<Operation, ? extends Message> networkExtractor,
      Function<Message, StringValue> identityGroupIdGetter,
      Function<Message, StringValue> identityIdGetter,
      String identityGroupId,
      String identityId) {
    final var record = messageCaptors.getWifiCfgChangeMessageCaptor().getValue(txCtx);
    assertThat(record)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .isNotNull()
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(txCtx.getTxId());

    var wifiConfigChange = record.getPayload();
    assertThat(wifiConfigChange).isNotNull();
    assertThat(wifiConfigChange.getOperationList())
        .filteredOn(operationFilter)
        .anyMatch(
            operation -> {
              var network = networkExtractor.apply(operation);
              return identityGroupIdGetter.apply(network).getValue().equals(identityGroupId)
                  && (identityId == null
                      || identityIdGetter.apply(network).getValue().equals(identityId));
            });
  }

  private void validateCfgChangeMessageForPsk(
      TxCtx txCtx, String identityGroupId, String identityId) {
    validateWifiCfgChangeMessageInternal(
        txCtx,
        Operation::hasPskNetwork,
        Operation::getPskNetwork,
        message -> (((com.ruckus.cloud.wifi.proto.PskNetwork) message).getIdentityGroupId()),
        message -> (((com.ruckus.cloud.wifi.proto.PskNetwork) message).getIdentityId()),
        identityGroupId,
        identityId);
  }

  private void validateCfgChangeMessageForGuest(
      TxCtx txCtx, String identityGroupId, String identityId) {
    validateWifiCfgChangeMessageInternal(
        txCtx,
        Operation::hasGuestNetwork,
        Operation::getGuestNetwork,
        message -> (((com.ruckus.cloud.wifi.proto.GuestNetwork) message).getIdentityGroupId()),
        message -> (((com.ruckus.cloud.wifi.proto.GuestNetwork) message).getIdentityId()),
        identityGroupId,
        identityId);
  }

  private void validateEntityAssociationListMessage(
      TxCtx txCtx,
      Set<String> networkIds,
      String identityGroupId,
      String identityId,
      Set<String> entityTypes) {
    final var entityAssociationListMessage =
        messageCaptors.getExternalAssociationChangeMessageCaptor().getValue(txCtx);
    assertThat(entityAssociationListMessage)
        .isNotNull()
        .satisfies(
            msg -> {
              var headers = msg.getHeaders();
              assertThat(headers).isNotNull();
              assertThat(headers.lastHeader(WifiCommonHeader.WIFI_ENTITY_TYPES))
                  .isNotNull()
                  .extracting(Header::value)
                  .extracting(b -> new String(b, StandardCharsets.UTF_8))
                  .satisfies(
                      json -> {
                        JsonArray jsonArray = new Gson().fromJson(json, JsonArray.class);
                        Set<String> parsedSet =
                            new Gson()
                                .fromJson(jsonArray, new TypeToken<Set<String>>() {}.getType());
                        assertThat(parsedSet).containsAll(entityTypes);
                      });
              assertThat(headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
                  .isNotNull()
                  .extracting(Header::value)
                  .extracting(String::new)
                  .isEqualTo(txCtx.getTxId());
              EntityAssociationList entityAssociationList = msg.getPayload();
              assertThat(entityAssociationList.getAssociationsCount()).isEqualTo(networkIds.size());

              Set<String> foundNetworkIds = new HashSet<>();

              for (EntityAssociation association : entityAssociationList.getAssociationsList()) {
                assertThat(association.getEntityType().name()).isEqualTo("NETWORK");

                String nid = association.getEntityId();
                foundNetworkIds.add(nid);

                var profileMap =
                    association.getProfilesList().stream()
                        .collect(
                            Collectors.toMap(
                                AssociatedProfile::getEntityType,
                                AssociatedProfile::getEntityIdsList));

                assertEntityProfile(profileMap, EntityType.IDENTITY_GROUP, identityGroupId);
                assertEntityProfile(profileMap, EntityType.IDENTITY, identityId);
              }
              assertThat(foundNetworkIds).containsAll(networkIds);
            });
  }

  private void assertEntityProfile(
      Map<EntityType, ProtocolStringList> profileMap, EntityType entityType, String expectedId) {
    if (expectedId != null && !expectedId.isBlank()) {
      assertThat(profileMap).containsKey(entityType);
      assertThat(profileMap.get(entityType)).contains(expectedId);
    } else {
      assertThat(profileMap).doesNotContainKey(entityType);
    }
  }

  @Nested
  class ConsumeActivateIdentityGroupOnWifiNetworkRequestTest {

    String identityGroupId;
    String identityId;
    String networkId;

    @BeforeEach
    void setup(final Tenant tenant) {
      identityGroupId = "identityGroupId01";
      identityId = "identity01";
      final var pskNetwork = network(PskNetwork.class).generate();
      pskNetwork.getWlan().setNetwork(pskNetwork);
      repositoryUtil.createOrUpdate(pskNetwork, tenant.getId(), randomTxId());
      networkId = pskNetwork.getId();
    }

    @ApiAction.RequestParams("activateIdentityGroup")
    private RequestParams activateIdentityGroupRequestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkId", networkId)
          .addPathVariable("identityGroupId", identityGroupId);
    }

    @ApiAction.RequestParams("activateIdentity")
    private RequestParams activateIdentityRequestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkId", networkId)
          .addPathVariable("identityGroupId", identityGroupId)
          .addPathVariable("identityId", identityId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_IDENTITY_GROUP_ON_WIFI_NETWORK,
        requestParams = @ApiAction.RequestParams("activateIdentityGroup"))
    void thenShouldActivateIdentityGroupOnWifiNetworkSuccessfully(
        TxCtx txCtx,
        @ApiAction.RequestParams("activateIdentityGroup") RequestParams requestParams) {
      assertThat(networkId).isEqualTo(requestParams.getPathVariables().get("wifiNetworkId"));
      assertThat(identityGroupId)
          .isEqualTo(requestParams.getPathVariables().get("identityGroupId"));
      validateCfgChangeMessageForPsk(txCtx, identityGroupId, null);

      assertThat(
              messageCaptors
                  .getExternalAssociationChangeMessageCaptor()
                  .getValue(txCtx.getTenant()))
          .isNull();
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_IDENTITY_ON_WIFI_NETWORK,
        requestParams = @ApiAction.RequestParams("activateIdentity"))
    void thenShouldActivateIdentityOnWifiNetworkSuccessfully(
        TxCtx txCtx, @ApiAction.RequestParams("activateIdentity") RequestParams requestParams) {
      final var network = repositoryUtil.find(Network.class, networkId);
      validateCfgChangeMessageForPsk(txCtx, identityGroupId, identityId);
      assertThat(
              messageCaptors
                  .getExternalAssociationChangeMessageCaptor()
                  .getValue(txCtx.getTenant()))
          .isNull();
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
  class ConsumeActivateIdentityGroupOnGuestNetworkRequestTest {

    String identityGroupId;
    String identityId;
    String networkId1;
    String networkId2;

    @BeforeEach
    void setup(Tenant tenant) {
      identityGroupId = "identityGroupId01";
      identityId = "identity01";
      String workflowProfileId = "workflowProfile01";
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      var primary =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(GuestNetwork.class)
              .generate();
      var secondary =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(GuestNetwork.class)
              .generate();

      primary.setTenant(tenant);
      primary.setName(masterSsid);
      primary.getWlan().setNetwork(primary);
      primary.getWlan().setSsid(masterSsid);
      primary.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      primary.setIsOweMaster(true);
      primary.setOwePairNetworkId(secondary.getId());
      primary.setWorkflowProfileId(workflowProfileId);
      primary.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Workflow)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      primary.getGuestPortal().setNetwork(primary);
      primary = repositoryUtil.createOrUpdate(primary, primary.getTenant().getId(), randomTxId());

      secondary.setTenant(tenant);
      secondary.setName(masterSsid + "-owe-tr");
      secondary.getWlan().setNetwork(secondary);
      secondary.getWlan().setSsid(masterSsid + "-owe-tr");
      secondary.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      secondary.setOwePairNetworkId(primary.getId());
      secondary.setWorkflowProfileId(workflowProfileId);
      secondary.setIsOweMaster(false);
      secondary.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Workflow)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      secondary.getGuestPortal().setNetwork(secondary);
      repositoryUtil.createOrUpdate(secondary, secondary.getTenant().getId(), randomTxId());
      networkId1 = primary.getId();
      networkId2 = secondary.getId();
    }

    @ApiAction.RequestParams("activateIdentityGroup")
    private RequestParams activateIdentityGroupRequestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkId", networkId1)
          .addPathVariable("identityGroupId", identityGroupId);
    }

    @ApiAction.RequestParams("activateIdentity")
    private RequestParams activateIdentityRequestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkId", networkId1)
          .addPathVariable("identityGroupId", identityGroupId)
          .addPathVariable("identityId", identityId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_IDENTITY_GROUP_ON_WIFI_NETWORK,
        requestParams = @ApiAction.RequestParams("activateIdentityGroup"))
    void thenShouldActivateIdentityGroupOnWifiNetworkSuccessfully(
        TxCtx txCtx,
        @ApiAction.RequestParams("activateIdentityGroup") RequestParams requestParams) {
      assertThat(networkId1).isEqualTo(requestParams.getPathVariables().get("wifiNetworkId"));
      assertThat(identityGroupId)
          .isEqualTo(requestParams.getPathVariables().get("identityGroupId"));
      validateCfgChangeMessageForGuest(txCtx, identityGroupId, null);
      validateEntityAssociationListMessage(
          txCtx,
          Set.of(networkId1, networkId2),
          identityGroupId,
          null,
          Set.of(EntityType.IDENTITY_GROUP.name(), EntityType.IDENTITY.name()));
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_IDENTITY_ON_WIFI_NETWORK,
        requestParams = @ApiAction.RequestParams("activateIdentity"))
    void thenShouldActivateIdentityOnWifiNetworkSuccessfully(
        TxCtx txCtx, @ApiAction.RequestParams("activateIdentity") RequestParams requestParams) {
      final var network = repositoryUtil.find(Network.class, networkId1);
      validateCfgChangeMessageForGuest(txCtx, identityGroupId, identityId);
      validateEntityAssociationListMessage(
          txCtx,
          Set.of(networkId1, networkId2),
          identityGroupId,
          identityId,
          Set.of(EntityType.IDENTITY_GROUP.name(), EntityType.IDENTITY.name()));
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
  class ConsumeDeleteWifiNetworkWithIdentityGroupRequestTest {

    String networkId1;
    String networkId2;

    @BeforeEach
    void setup(Tenant tenant) {
      String identityGroupId = "identityGroupId01";
      String workflowProfileId = "workflowProfile01";
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      var primary =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(GuestNetwork.class)
              .generate();
      var secondary =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(GuestNetwork.class)
              .generate();

      primary.setTenant(tenant);
      primary.setName(masterSsid);
      primary.getWlan().setNetwork(primary);
      primary.getWlan().setSsid(masterSsid);
      primary.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      primary.setIsOweMaster(true);
      primary.setOwePairNetworkId(secondary.getId());
      primary.setWorkflowProfileId(workflowProfileId);
      primary.setIdentityGroupId(identityGroupId);
      primary.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Workflow)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      primary.getGuestPortal().setNetwork(primary);
      primary = repositoryUtil.createOrUpdate(primary, primary.getTenant().getId(), randomTxId());

      secondary.setTenant(tenant);
      secondary.setName(masterSsid + "-owe-tr");
      secondary.getWlan().setNetwork(secondary);
      secondary.getWlan().setSsid(masterSsid + "-owe-tr");
      secondary.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      secondary.setOwePairNetworkId(primary.getId());
      secondary.setWorkflowProfileId(workflowProfileId);
      secondary.setIdentityGroupId(identityGroupId);
      secondary.setIsOweMaster(false);
      secondary.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Workflow)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      secondary.getGuestPortal().setNetwork(secondary);
      repositoryUtil.createOrUpdate(secondary, secondary.getTenant().getId(), randomTxId());
      networkId1 = primary.getId();
      networkId2 = secondary.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkId1);
    }

    @Test
    @ApiAction(value = CfgAction.DELETE_WIFI_NETWORK)
    void thenShouldDeleteWifiNetworkSuccessfully(TxCtx txCtx) {

      validateEntityAssociationListMessage(
          txCtx,
          Set.of(networkId1, networkId2),
          null,
          null,
          Set.of(
              EntityType.IDENTITY_GROUP.name(),
              EntityType.IDENTITY.name(),
              EntityType.WORKFLOW.name()));
    }
  }
}
