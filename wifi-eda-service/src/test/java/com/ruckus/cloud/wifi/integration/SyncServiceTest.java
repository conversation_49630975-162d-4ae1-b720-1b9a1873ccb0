package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.dhcpPool;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.openNetwork;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.service.impl.SyncServiceCtrlImpl.ruckusOneViewmodelIndices;
import static com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture.randomAccessControlProfile;
import static com.ruckus.cloud.wifi.test.fixture.ApplicationPolicyTestFixture.applicationPolicy;
import static com.ruckus.cloud.wifi.test.fixture.ClientIsolationTestFixture.clientIsolationAllowlist;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.DevicePolicyTestFixture.randomDevicePolicy;
import static com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.getGuestNetwork;
import static com.ruckus.cloud.wifi.test.fixture.L2AclPolicyTestFixture.randomL2AclPolicy;
import static com.ruckus.cloud.wifi.test.fixture.L3AclPolicyTestFixture.randomL3AclPolicy;
import static com.ruckus.cloud.wifi.test.fixture.MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile;
import static com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture.rogueApPolicy;
import static com.ruckus.cloud.wifi.test.fixture.WifiCallingServiceProfileTestFixture.randomWifiCallingServiceProfile;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.cloud.entitlement.ProtoDeviceType;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.admin.Role;
import com.ruckus.cloud.wifi.core.header.HttpHeaderContext;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.eda.api.rest.WifiCallingRestCtrl.WifiCallingMapper;
import com.ruckus.cloud.wifi.eda.service.DhcpServiceProfileServiceCtrl;
import com.ruckus.cloud.wifi.eda.service.SyncServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackage;
import com.ruckus.cloud.wifi.eda.servicemodel.SyncRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.QosPriorityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DhcpConfigServiceProfileDeepGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.handler.cfgrequest.softgreprofile.AddSoftGreProfileRequestHandler;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.DhcpConfigServiceProfileRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.SignaturePackageRepository;
import com.ruckus.cloud.wifi.repository.TenantRepository;
import com.ruckus.cloud.wifi.repository.WifiCallingProfileRepository;
import com.ruckus.cloud.wifi.repository.WifiCallingServiceProfileRepository;
import com.ruckus.cloud.wifi.service.SignaturePackageService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
class SyncServiceTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private SyncServiceCtrl syncServiceCtrl;
  @Autowired
  private SignaturePackageRepository signaturePackageRepository;
  @Autowired
  private TenantRepository tenantRepository;
  @Autowired
  private DhcpServiceProfileServiceCtrl dhcpServiceProfileServiceCtrl;
  @Autowired
  private DhcpConfigServiceProfileRepository dhcpConfigServiceProfileRepository;
  @Autowired
  private ApRepository apRepository;
  @Autowired
  private WifiCallingProfileRepository wifiCallingProfileRepository;
  @Autowired
  private NetworkRepository networkRepository;
  @Autowired
  private WifiCallingServiceProfileRepository wifiCallingServiceProfileRepository;
  private AddSoftGreProfileRequestHandler addSoftGreProfileRequestHandler;

  @BeforeEach
  public void setup() {
    HttpHeaderContext.setHeader(HttpHeaderName.RKS_USER_ROLE, Role.DEVOPS.name());
  }

  @Test
  void triggerEntitlementDataSync(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apgroup");
    createAp(apGroup, "apSerial_1");
    createAp(apGroup, "apSerial_2");

    clearMessage();
    entitlementDataSync(tenant.getId());

    var entitlementEdaRequests = receiveEntitlementEdaOperations(1, tenantId);
    assertEquals(1, entitlementEdaRequests.size());
    assertTrue(entitlementEdaRequests.get(0).getSyncTo());

    var entitlementEdaOperations = entitlementEdaRequests.get(0).getOperationList();
    assertEquals(2, entitlementEdaOperations.size());
    assertSoftly(softly -> {
      softly.assertThat(entitlementEdaOperations)
        .allSatisfy(op -> {
          assertEquals(op.getAction(), com.ruckus.cloud.entitlement.Action.CONSUME);
          assertEquals(op.getDevice().getDeviceType(), ProtoDeviceType.WIFI);
          assertTrue(op.getDevice().getSerialNumber().contains("apSerial_"));
        });
    });
  }

  @Test
  void triggerEntitlementDataSyncWithoutAP(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();

    clearMessage();
    entitlementDataSync(tenant.getId());

    var entitlementEdaRequests = receiveEntitlementEdaOperations(1, tenantId);
    assertEquals(1, entitlementEdaRequests.size());
    assertTrue(entitlementEdaRequests.get(0).getSyncTo());

    var entitlementEdaOperations = entitlementEdaRequests.get(0).getOperationList();
    assertEquals(0, entitlementEdaOperations.size());
  }

  @Test
  void triggerViewmodelDataSyncForRuckusOne(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apgroup");

    // Services
    addWifiCallingServiceProfile(randomWifiCallingServiceProfile());
    addMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile("mdns"));
    addDhcpConfigServiceProfile(randomDhcpConfigServiceProfile());

    // Policies
    addL2AclPolicy(randomL2AclPolicy());
    addL3AclPolicy(randomL3AclPolicy());
    addApplicationPolicy(applicationPolicy());
    addDevicePolicy(randomDevicePolicy());
    addAccessControlProfile(randomAccessControlProfile());
    addVlanPool(Generators.vlanPool().generate());
    addRogueApPolicy(rogueApPolicy("rogue"));
    addClientIsolationAllowlist(clientIsolationAllowlist("allowlist"));
    addSyslogServerProfile(Generators.syslogServerProfile().generate());
    addRadius(Generators.radiusProfile().generate());
    addHotspot20Operator(Generators.hotspot20Operator().generate());
    addHotspot20IdentityProvider(Generators.hotspot20IdentityProvider().generate());
    addSoftGreProfile(Generators.softGreProfile().generate());

    // Add network and bind venue
    PskNetwork psk = addPskNetwork(map(pskNetwork("psk").generate()));
    edaAddNetworkVenue(tenantId, userName, psk.getId(), v.getId(), null);

    // For testing ApplicationPolicy Snapshot
    var snapshotApplicationPolicy = createSnapShotApplicationPolicy();

    clearMessage();

    log.info("====================");

    viewmodelDataSync(tenantId);

    // ddccm, ent
    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getEntitlementDeviceOperationRequest());

    // cmnCfgCollector
    var viewmodelOperations = receiveViewmodelCollectorOperations(2, tenantId);

    // Should be the same with the added data amount
    assertEquals(18, viewmodelOperations.stream()
        .filter(o -> o.getOpType() == OpType.ADD)
        .count());
    assertEquals(0, viewmodelOperations.stream()
        .filter(o -> o.getOpType() != OpType.ADD)
        .filter(o -> o.getOpType() != OpType.DEL)
        .count());

    var dhcpProfileIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME);
    var accessControlProfileIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME);
    var accessControlSubProfileIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME);
    var syslogProfileIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.SYSLOG_SERVER_PROFILE_INDEX_NAME);
    var rogueApPolicyProfileIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.ROGUE_AP_POLICY_PROFILE_INDEX_NAME);
    var clientIsolationAllowlistIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME);
    var wifiCallingServiceProfileIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME);
    var mDnsProxyServiceProfileIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME);
    var vlanPoolProfileIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.VLAN_POOL_POLICY_PROFILE_INDEX_NAME);
    var radiusServerProfileIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.RADIUS_SERVER_PROFILE_INDEX_NAME);
    var networkIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.NETWORK);
    var networkDeviceGroupIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.NETWORK_DEVICE_GROUP_MAPPING);
    var hotspot20OperatorIndexOperations = getSpecificIndexOperations(
      viewmodelOperations, Index.HOTSPOT20_OPERATOR_INDEX_NAME);
    var hotspot20IdentityProviderIndexOperations = getSpecificIndexOperations(
        viewmodelOperations, Index.HOTSPOT20_IDENTITY_PROVIDER_INDEX_NAME);
    var softGreProfileIndexOperations =
        getSpecificIndexOperations(viewmodelOperations, Index.SOFT_GRE_PROFILE_INDEX_NAME);

    assertEquals(1, dhcpProfileIndexOperations.size());
    assertEquals(1, accessControlProfileIndexOperations.size());
    assertEquals(4, accessControlSubProfileIndexOperations.size());
    assertTrue(accessControlSubProfileIndexOperations.stream()
        .noneMatch(op -> op.getId().equals(snapshotApplicationPolicy.getId())));
    assertEquals(1, syslogProfileIndexOperations.size());
    assertEquals(1, rogueApPolicyProfileIndexOperations.size());
    assertEquals(1, clientIsolationAllowlistIndexOperations.size());
    assertEquals(1, wifiCallingServiceProfileIndexOperations.size());
    assertEquals(1, mDnsProxyServiceProfileIndexOperations.size());
    assertEquals(1, vlanPoolProfileIndexOperations.size());
    assertEquals(1, radiusServerProfileIndexOperations.size());
    assertEquals(1, networkIndexOperations.size());
    assertEquals(1, networkDeviceGroupIndexOperations.size());
    assertEquals(1, hotspot20OperatorIndexOperations.size());
    assertEquals(1, hotspot20IdentityProviderIndexOperations.size());
    assertEquals(1, softGreProfileIndexOperations.size());

    /**
     * Make sure DownstreamPostMigrationService have
     * correct ruckusOneViewmodelIndices and ruckusOneViewmodelClasses.
     */
    Set<String> addIndices = viewmodelOperations.stream()
        .filter(o -> o.getOpType() == OpType.ADD)
        .map(Operations::getIndex)
        .collect(Collectors.toSet());
    assertEquals(ruckusOneViewmodelIndices.size(), addIndices.size());

    Set<String> deleteIndices = viewmodelOperations.stream()
        .filter(o -> o.getOpType() == OpType.DEL)
        .map(Operations::getIndex)
        .collect(Collectors.toSet());
    assertEquals(1, deleteIndices.size());

    Set<String> realDeleteIndices = Set.of(deleteIndices.iterator().next().split(","));
    assertEquals(ruckusOneViewmodelIndices.size(), realDeleteIndices.size());
    assertTrue(realDeleteIndices.containsAll(ruckusOneViewmodelIndices));
  }

  @Test
  void testDdccmConfigSyncWithValidCase(Ap ap) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // For testing ApplicationPolicy Snapshot
    var snapshotApplicationPolicy = createSnapShotApplicationPolicy();
    // For Normal ApplicationPolicy
    addApplicationPolicy(applicationPolicy());

    // For testing Predefined AAA
    var networkDeepReq = getGuestNetwork(GuestNetworkTypeEnum.ClickThrough, n -> {
    });
    networkDeepReq.setName("GuestNetwork");
    networkDeepReq.setEnableDhcp(true);
    var n1 = addGuestNetwork(map(networkDeepReq));
    edaAddNetworkVenue(tenantId, userName, n1.getId(), ap.getApGroup().getVenue().getId(), null);

    clearMessage();
    log.info("====================");
    ddccmConfigSync(tenantId);

    // NO CmnCig, ent
    assertNoMessages(tenantId,
        kafkaTopicProvider.getEntitlementDeviceOperationRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    // Do delete first then add
    var deleteOperations = receiveDdccmOperations(1, tenantId);
    assertEquals(1, deleteOperations.size());
    assertEquals(Action.DELETE, deleteOperations.get(0).getAction());
    assertTrue(deleteOperations.get(0).getConfigCase() == ConfigCase.TENANT);

    var addOperations = receiveDdccmOperations(1, tenantId);
    // Tenant + SigPack
    assertEquals(1, addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.TENANT)
        .count());
    assertEquals(1, addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.CCMQMSIGNATUREPACKAGEACX)
        .count());
    // The Tenant should be in the first one
    assertTrue(addOperations.get(0).getConfigCase() == ConfigCase.TENANT);
    // The AP should be in the last one
    assertTrue(addOperations.get(addOperations.size() - 1).getConfigCase() == ConfigCase.AP);

    // Application Policy & Rule verify
    var applicationPolicyOperations = addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.APPLICATIONPOLICY)
        .collect(Collectors.toList());
    assertEquals(1, applicationPolicyOperations.size());
    assertNotEquals(snapshotApplicationPolicy.getId(),
        applicationPolicyOperations.get(0).getApplicationPolicy().getId());
    var userDefinedRuleOperations = addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.USERDEFINEDAPPLICATIONRULE)
        .toList();
    assertEquals(1, userDefinedRuleOperations.size());

    // Venue DHCP Service Setting verify
    var dhcpServiceSettingOperations = addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.VENUEDHCPSERVICESETTING)
        .collect(Collectors.toList());
    assertEquals(1, dhcpServiceSettingOperations.size());

    // Predefined AAA verify
    var predefinedAaaServiceOperations = addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.RADIUSAUTHENTICATIONSERVICE)
        .collect(Collectors.toList());
    assertEquals(1, predefinedAaaServiceOperations.size());
    assertEquals(tenantId + "-Auth-ruckSM-AAA", predefinedAaaServiceOperations.get(0).getId());

    // One AP => send one message to franz
    var franzMessages = messageCaptors.getFranzDeviceStatusMessageCaptor()
        .getValue(tenantId);
    assertNotNull(franzMessages);
    assertEquals(ap.getId(), franzMessages.getPayload().getSerialNumber());
  }

  @Test
  void testDdccmConfigSyncByAddingDhcpConfigServiceProfileAfterMigration(Tenant tenant)
      throws Exception {
    // The purpose of this test case is to simulate a account migrate from acx to r1 and then do the ddccm config sync
    // 1. Prepare DhcpServiceProfile (acx) to simulate the profile before migration
    // 2. Prepare DhcpConfigServiceProfile (ruckus-one) to simulate the profile after migration
    // 3. Do ddccm sync => the result should only contain the operation of DhcpConfigServiceProfile.

    // STEP 1
    createDhcpServiceProfiles(tenant, 2);
    // STEP 2
    createDhcpConfigServiceProfile(tenant, 3);

    // STEP 3
    clearMessage();
    ddccmConfigSync(tenant.getId());

    // Do delete first then add
    var deleteOperations = receiveDdccmOperations(1, tenant.getId());
    assertEquals(1, deleteOperations.size());
    assertEquals(Action.DELETE, deleteOperations.get(0).getAction());
    assertTrue(deleteOperations.get(0).getConfigCase() == ConfigCase.TENANT);

    var addOperations = receiveDdccmOperations(1, tenant.getId());
    assertTrue(addOperations.stream().allMatch(o -> o.getAction() == Action.ADD));

    var dhcpConfigServiceProfiles = dhcpConfigServiceProfileRepository.findByTenantId(
        tenant.getId());
    assertEquals(1, dhcpConfigServiceProfiles.size());
    var profileIds = dhcpConfigServiceProfiles.get(0).getDhcpPools().stream()
        .map(DhcpServiceProfile::getId)
        .collect(Collectors.toSet());
    var operationIds = addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.DHCPSERVICEPROFILE)
        .map(Operation::getId)
        .collect(Collectors.toSet());
    assertEquals(profileIds, operationIds);
  }

  @Test
  void testDdccmConfigSyncByAddingMulticastDnsProxyServiceProfileAfterMigration(
      Tenant tenant, Ap ap) throws Exception {
    // The purpose of this test case is to simulate a account migrate from acx to r1 and then do the ddccm config sync
    // (deprecated) 1. Prepare Tenant, Venue, AP and BonjourGateway (acx) to simulate the profile before migration
    // 2. Prepare MulticastDnsProxyServiceProfile (ruckus-one) to simulate the profile after migration
    // 3. Do ddccm sync => the result should only contain the operation of MulticastDnsProxyServiceProfile.

    // STEP 2
    var serviceName = "TEST-MDNS";
    addMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile(serviceName));

    // STEP 3
    clearMessage();
    ddccmConfigSync(tenant.getId());

    // Do delete first then add
    var deleteOperations = receiveDdccmOperations(1, tenant.getId());
    assertEquals(1, deleteOperations.size());
    assertEquals(Action.DELETE, deleteOperations.get(0).getAction());
    assertTrue(deleteOperations.get(0).getConfigCase() == ConfigCase.TENANT);

    var addOperations = receiveDdccmOperations(1, tenant.getId());
    assertTrue(addOperations.stream().allMatch(o -> o.getAction() == Action.ADD));

    var mdnsOperations = addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
        .collect(Collectors.toList());
    assertEquals(1, mdnsOperations.size());
    assertEquals(serviceName, mdnsOperations.get(0).getVenueBonjourGateway().getName());
  }

  @Test
  void testDdccmConfigSyncByAddingWifiCallingServiceProfileAfterMigration(
      Tenant tenant, Venue venue) throws Exception {
    // The purpose of this test case is to simulate a account migrate from acx to r1 and then do the ddccm config sync
    // 1. Prepare OpenNetwork and WiFiCallingProfile (acx) to simulate the profile before migration
    // 2. Prepare WifiCallingServiceProfile (ruckus-one) to simulate the profile after migration
    // 3. Do ddccm sync => the result should only contain the operation of WifiCallingServiceProfile.

    // STEP 1
    createOpenNetwork(tenant, venue);
    createWifiCallingProfile(tenant);

    // STEP 2
    var networks = networkRepository.findByTenantId(tenant.getId());
    assertEquals(1, networks.size());
    var networkId = networks.get(0).getId();
    createWifiCallingServiceProfile(tenant, networkId);

    // STEP 3
    clearMessage();
    ddccmConfigSync(tenant.getId());

    // Do delete first then add
    var deleteOperations = receiveDdccmOperations(1, tenant.getId());
    assertEquals(1, deleteOperations.size());
    assertEquals(Action.DELETE, deleteOperations.get(0).getAction());
    assertTrue(deleteOperations.get(0).getConfigCase() == ConfigCase.TENANT);

    var addOperations = receiveDdccmOperations(1, tenant.getId());
    assertTrue(addOperations.stream().allMatch(o -> o.getAction() == Action.ADD));

    var wifiCallingServiceProfiles = wifiCallingServiceProfileRepository.findByTenantId(
        tenant.getId());
    assertEquals(1, wifiCallingServiceProfiles.size());
    var serviceName = wifiCallingServiceProfiles.get(0).getServiceName();
    var wifiCallingOperations = addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.WIFICALLING)
        .collect(Collectors.toList());
    assertEquals(1, wifiCallingOperations.size());
    assertEquals(serviceName, wifiCallingOperations.get(0).getWifiCalling().getName());
    var serviceId = wifiCallingServiceProfiles.get(0).getId();
    var wlanVenueOperations = addOperations.stream()
        .filter(o -> o.getConfigCase() == ConfigCase.WLANVENUE)
        .collect(Collectors.toList());
    assertEquals(1, wlanVenueOperations.size());
    assertEquals(serviceId, wlanVenueOperations.get(0)
        .getWlanVenue().getAdvancedCustomization().getWifiCallingPolicyIds(0));
  }

  @SneakyThrows
  private void ddccmConfigSync(String tenantId) {
    SyncRequest request = new SyncRequest();
    request.setTenantId(tenantId);
    assertEquals("ok", syncServiceCtrl.triggerDdccmConfigSync(request).getResponse());
  }

  @SneakyThrows
  private void viewmodelDataSync(String tenantId) {
    SyncRequest request = new SyncRequest();
    request.setTenantId(tenantId);
    assertEquals("ok", syncServiceCtrl.triggerViewmodelDataSync(request).getResponse());
  }

  @SneakyThrows
  private void entitlementDataSync(String tenantId) {
    SyncRequest request = new SyncRequest();
    request.setTenantId(tenantId);
    assertEquals("ok", syncServiceCtrl.triggerEntitlementDataSync(request).getResponse());
  }

  private List<Operations> getSpecificIndexOperations(List<Operations> viewmodelOperations,
      String syslogServerProfileIndexName) {
    return viewmodelOperations.stream()
        .filter(op -> op.getIndex().equals(syslogServerProfileIndexName))
        .collect(Collectors.toList());
  }

  @SneakyThrows
  private ApplicationPolicy createSnapShotApplicationPolicy() {
    Tenant tenant = tenantRepository.getReferenceById(TxCtxHolder.tenantId());

    // add application policy with old sp to test sync can skip

    changeTxId("addOldSp");
    var spId = SignaturePackageService.FIRST_VERSION + "-old";
    var sp = new SignaturePackage(spId);
    sp.setVersion(sp.getId());
    sp = signaturePackageRepository.save(sp);
    changeTxId("updateSpToTenant");
    tenant.setSignaturePackage(sp);
    tenantRepository.save(tenant);
    tenant = tenantRepository.getReferenceById(TxCtxHolder.tenantId());

    var snapshotApplicationPolicy = applicationPolicy();
    snapshotApplicationPolicy.setName(snapshotApplicationPolicy.getName() + "-snapshot");
    /**
     * add() return value is trimmed by viewmodel mapping
     * so we need to get it from db to have signaturePackage value.
     */
    snapshotApplicationPolicy = addApplicationPolicy(snapshotApplicationPolicy);
    snapshotApplicationPolicy = applicationPolicyServiceCtrl.getApplicationPolicy(
        snapshotApplicationPolicy.getId());

    assertEquals(spId, sp.getId());
    assertNotNull(snapshotApplicationPolicy.getSignaturePackage());
    assertEquals(sp.getId(), snapshotApplicationPolicy.getSignaturePackage().getId());

    // in order to keep original test case, set default version back

    var newSpId = SignaturePackageService.FIRST_VERSION;
    var newSp = new SignaturePackage(newSpId);
    changeTxId("addDefaultSp");
    newSp.setVersion(sp.getId());
    newSp = signaturePackageRepository.save(newSp);
    changeTxId("updateDefaultSpToTenant");
    tenant.setSignaturePackage(newSp);
    tenantRepository.save(tenant);

    assertEquals(newSpId, newSp.getId());

    return snapshotApplicationPolicy;
  }

  @SneakyThrows
  private void createDhcpServiceProfiles(Tenant tenant, int count) {
    var dhcpServiceProfiles = Generators.dhcpServiceProfile().generate(count);
    for (var p : dhcpServiceProfiles) {
      p.setTenant(tenant);
      TxCtxHolder.get().setTxId(randomTxId());
      dhcpServiceProfileServiceCtrl.addDhcpServiceProfile(p);
    }
  }

  private void createDhcpConfigServiceProfile(Tenant tenant, int poolSize) {
    var request = new DhcpConfigServiceProfileDeepGenerator()
        .setServiceName(serialName(DhcpConfigServiceProfileDeep.class.getSimpleName()))
        .setDhcpMode(always(DhcpModeEnum.EnableOnEachAPs))
        .setDhcpPools(dhcpPool().toListGenerator(poolSize)).generate();
    messageUtil.sendWifiCfgRequest(tenant.getId(), randomTxId(),
        CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE,
        randomName(), request);
  }

  private void createOpenNetwork(Tenant tenant, Venue venue) {
    var openNetwork = addOpenNetwork(map(openNetwork("open").generate()));
    edaAddNetworkVenue(tenant.getId(), randomName(), openNetwork.getId(), venue.getId(), null);
  }

  @SneakyThrows
  private void createWifiCallingProfile(Tenant tenant) {
    var wifiCallingProfile = genWifiCallingProfile();
    wifiCallingProfile.setTenant(tenant);
    TxCtxHolder.get().setTxId(randomTxId());
    wifiCallingProfileRepository.save(wifiCallingProfile);
  }

  private WifiCallingProfile genWifiCallingProfile() {
    var result = new com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingProfile();
    result.setProfileName("wifiCallingProfile1");
    result.setQosPriority(QosPriorityEnum.WIFICALLING_PRI_BG);
    result.setDescription("test-1");
    var epdg = new com.ruckus.cloud.wifi.eda.viewmodel.Epdg();
    epdg.setDomain("epdg.att.com");
    result.setEPDGs(Arrays.asList(epdg));
    return WifiCallingMapper.INSTANCE.WifiCallingProfile2ServiceWifiCallingProfile(result);
  }

  @SneakyThrows
  private void createWifiCallingServiceProfile(Tenant tenant, String networkId) {
    var profile = com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.wifiCallingServiceProfile()
        .setId(nullValue(String.class)).generate();
    profile.setNetworkIds(Arrays.asList(networkId));
    profile.setEPDGs(com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.epdg().generate(1));
    messageUtil.sendWifiCfgRequest(tenant.getId(), randomTxId(),
        CfgAction.ADD_WIFI_CALLING_SERVICE_PROFILE,
        randomName(), profile);
  }
}
