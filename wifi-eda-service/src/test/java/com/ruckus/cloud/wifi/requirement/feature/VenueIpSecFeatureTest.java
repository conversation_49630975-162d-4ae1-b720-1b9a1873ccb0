package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.IpsecProfileRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.List;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_IPSEC_PSK_OVER_NETWORK;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@WifiUnitTest
public class VenueIpSecFeatureTest {

  @MockBean
  private IpsecProfileRepository repository;

  @SpyBean
  private VenueIpSecFeature unit;

  @Nested
  class WhenConfigIpSec {

    @Test
    @FeatureFlag(disable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenR370Disable(Venue venue) {
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenIpSecDisable(Venue venue) {
      when(repository.findAllLanPortsActivatedIpsecProfiles(
          anyString(), anyString())).thenReturn(List.of());

      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenIpSecEnable(Venue venue) {
      when(repository.findAllLanPortsActivatedIpsecProfiles(
          anyString(), anyString())).thenReturn(
          List.of(new IpsecProfile()));

      BDDAssertions.then(unit.test(venue)).isTrue();
    }
  }
}
