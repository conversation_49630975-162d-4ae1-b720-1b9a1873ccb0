package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE;
import static com.ruckus.cloud.wifi.proto.DeviceRealtimeDataTypeEnum.ApRealtimeDataTypeEnum_DEVICE_CSR;
import static com.ruckus.cloud.wifi.proto.DeviceRealtimeDataTypeEnum.ApRealtimeDataTypeEnum_DEVICE_REGISTRATION;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.tuple;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceCsrProto.DeviceCsr;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceRegistrationPolicyProto.DeviceRegistrationPolicy.Policy;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceRegistrationProto.DeviceRegistration;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelMinimumFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.ApTlsEnhancedCertInfo;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApVersionLabelEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.proto.DeviceRealtimeDataJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.servicemodel.TenantEarlyAccessInfo;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = {WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
public class ConsumeDeviceRegistTest {

  public static final String TEST_CSR_PEM = "test csr";
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private TenantClient tenantClient;

  @Nested
  class whenConsumeDeviceRegist {

    @Test
    void thenSaveApMacAndModelAndApprove(ApGroup apGroup) throws InvalidProtocolBufferException {
      Ap ap = new Ap(randomSerialNumber());
      ap.setName(randomName());
      ap.setApGroup(apGroup);
      ap.setTenant(apGroup.getTenant());
      repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), randomTxId());

      var requestId = randomTxId();
      messageUtil.sendDeviceRegistration(apGroup.getTenant().getId(),
          requestId,
          DeviceRegistration.newBuilder()
              .setSerial(ap.getId())
              .setModel("R710")
              .setMac("11:22:33:44:55:66")
              .build());

      final var deviceRegistrationPolicyMessageRecord = messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
              .getValue(ap.getTenant().getId(), requestId);
      assertThat(deviceRegistrationPolicyMessageRecord)
          .isNotNull();
      assertThat(deviceRegistrationPolicyMessageRecord.getPayload())
          .isNotNull();
      assertThat(deviceRegistrationPolicyMessageRecord.getPayload())
          .matches(p -> p.getTenantId().equals(ap.getTenant().getId()))
          .matches(p -> p.getId().equals(ap.getId()))
          .matches(p -> p.getPolicy().equals(Policy.APPROVED));
    }

    @Nested
    @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
    class whenApFwMgmtUpgradeByModelFfTurnOn {
      @Test
      void thenCreateApFirmwareUpgradeRequestByVenueCurrentFirmware(ApGroup apGroup) {
        String tenantId = apGroup.getTenant().getId();
        Ap ap = ApTestFixture.randomAp(apGroup, a -> a.setName(randomName()));
        repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
        ApVersion version = ApVersionTestFixture.recommendedApVersion("*******.1",
            v -> v.setSupportedApModels(List.of("R710")));
        ApVersion biggestVersion = ApVersionTestFixture.recommendedApVersion("*******.2",
            v -> v.setSupportedApModels(List.of("R710")));
        repositoryUtil.createOrUpdate(version, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(biggestVersion, tenantId, randomTxId());

        createTenantAvailableApFirmwares(apGroup.getTenant(), List.of(version, biggestVersion));

        var requestId = randomTxId();
        messageUtil.sendDeviceRegistration(tenantId, requestId,
            DeviceRegistration.newBuilder()
                .setSerial(ap.getId())
                .setModel("R710")
                .setMac("11:22:33:44:55:66")
                .setTenantId(tenantId)
                .build());

        final var deviceRegistrationPolicyMessageRecord
            = messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
            .getValue(ap.getTenant().getId(), requestId);
        assertThat(deviceRegistrationPolicyMessageRecord.getPayload())
            .matches(p -> p.getTenantId().equals(ap.getTenant().getId()))
            .matches(p -> p.getId().equals(ap.getId()))
            .matches(p -> p.getPolicy().equals(Policy.APPROVED));

        final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(ap.getTenant().getId(), requestId);
        verifyVenueCmnCollectorMessage(cmnCfgCollectorMessage, apGroup.getVenue().getId(),
            tuple("R710", "*******.2"));
        verifyVenueCurrentFirmwareAndApFirmwareUpgradeRequest(ap, biggestVersion, "R710");
      }

      @Test
      void thenRejectWhenApVersionLowerThanMinAndVenueWithoutTheSameApModel(ApGroup apGroup) {
        String tenantId = apGroup.getTenant().getId();
        Ap ap = ApTestFixture.randomAp(apGroup, a -> a.setName(randomName()));
        repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
        ApVersion version = ApVersionTestFixture.recommendedApVersion("*******.1",
            v -> v.setSupportedApModels(List.of("R710")));
        ApVersion minimumRequiredVersion = ApVersionTestFixture.recommendedApVersion("*******.2",
            v -> v.setSupportedApModels(List.of("R710")));
        repositoryUtil.createOrUpdate(version, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(minimumRequiredVersion, tenantId, randomTxId());
        ApModelMinimumFirmware ammf = new ApModelMinimumFirmware();
        ammf.setMinimumFirmware(minimumRequiredVersion);
        ammf.setId("R710");
        repositoryUtil.createOrUpdate(ammf, tenantId, randomTxId());

        createTenantAvailableApFirmwares(apGroup.getTenant(), List.of(version));

        var requestId = randomTxId();
        messageUtil.sendDeviceRegistration(tenantId, requestId,
            DeviceRegistration.newBuilder()
                .setSerial(ap.getId())
                .setModel("R710")
                .setMac("11:22:33:44:55:66")
                .setTenantId(tenantId)
                .build());

        assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId))
            .describedAs("Shouldn't create model version mapping when AP is rejected")
            .isEmpty();
        final var deviceRegistrationPolicyMessageRecord
            = messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
            .getValue(ap.getTenant().getId(), requestId);
        assertThat(deviceRegistrationPolicyMessageRecord.getPayload())
            .matches(p -> p.getTenantId().equals(ap.getTenant().getId()))
            .matches(p -> p.getId().equals(ap.getId()))
            .matches(p -> p.getPolicy().equals(Policy.REJECTED));
      }

      @Test
      void thenSkipRejectWhenApVersionLowerThanMinButApIsNotFirstJoin(ApGroup apGroup) {
        String tenantId = apGroup.getTenant().getId();
        Ap ap = ApTestFixture.randomAp(apGroup, a -> a.setName(randomName()));
        ap.setModel("R710");
        ap.setMac(randomMacAddress());
        repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
        ApVersion version = ApVersionTestFixture.recommendedApVersion("*******.1",
            v -> v.setSupportedApModels(List.of("R710")));
        ApVersion minimumRequiredVersion = ApVersionTestFixture.recommendedApVersion("*******.2",
            v -> v.setSupportedApModels(List.of("R710")));
        ApVersion biggestVersion = ApVersionTestFixture.recommendedApVersion("*******.3",
            v -> v.setSupportedApModels(List.of("R710")));
        repositoryUtil.createOrUpdate(version, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(minimumRequiredVersion, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(biggestVersion, tenantId, randomTxId());
        ApModelMinimumFirmware ammf = new ApModelMinimumFirmware();
        ammf.setMinimumFirmware(minimumRequiredVersion);
        ammf.setId("R710");
        repositoryUtil.createOrUpdate(ammf, tenantId, randomTxId());

        createTenantAvailableApFirmwares(apGroup.getTenant(), List.of(version, biggestVersion));

        var requestId = randomTxId();
        messageUtil.sendDeviceRegistration(tenantId, requestId,
            DeviceRegistration.newBuilder()
                .setSerial(ap.getId())
                .setModel(ap.getModel())
                .setMac(ap.getMac())
                .setTenantId(tenantId)
                .build());

        final var deviceRegistrationPolicyMessageRecord
            = messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
            .getValue(ap.getTenant().getId(), requestId);
        assertThat(deviceRegistrationPolicyMessageRecord.getPayload())
            .matches(p -> p.getTenantId().equals(ap.getTenant().getId()))
            .matches(p -> p.getId().equals(ap.getId()))
            .matches(p -> p.getPolicy().equals(Policy.APPROVED));

        final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(ap.getTenant().getId(), requestId);
        verifyVenueCmnCollectorMessage(cmnCfgCollectorMessage, apGroup.getVenue().getId(),
            tuple("R710", biggestVersion.getId()));
        verifyVenueCurrentFirmwareAndApFirmwareUpgradeRequest(ap, biggestVersion, "R710");
      }

      @Nested
      @FeatureFlag(enable = FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE)
      class whenGreenfieldByApModelToggleTurnOn {

        @Test
        void thenCreateApFirmwareUpgradeRequestByVenueCurrentFirmware(ApGroup apGroup) {
          String tenantId = apGroup.getTenant().getId();
          ApVersion version = ApVersionTestFixture.recommendedApVersion("*******.1",
              v -> v.setSupportedApModels(List.of("R710")));
          ApVersion biggestVersion = ApVersionTestFixture.recommendedApVersion("*******.2",
              v -> v.setSupportedApModels(List.of("R710")));
          repositoryUtil.createOrUpdate(version, tenantId, randomTxId());
          repositoryUtil.createOrUpdate(biggestVersion, tenantId, randomTxId());
          Ap ap = ApTestFixture.randomAp(apGroup, a -> a.setName(randomName()));
          repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
          repositoryUtil.createOrUpdate(
              TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(ap.getTenant(),
                  biggestVersion, "R710"),
              tenantId,
              randomTxId());

          createTenantAvailableApFirmwares(apGroup.getTenant(), List.of(version, biggestVersion));

          var requestId = randomTxId();
          messageUtil.sendDeviceRegistration(tenantId, requestId,
              DeviceRegistration.newBuilder()
                  .setSerial(ap.getId())
                  .setModel("R710")
                  .setMac("11:22:33:44:55:66")
                  .setTenantId(tenantId)
                  .build());

          final var deviceRegistrationPolicyMessageRecord
              = messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
              .getValue(ap.getTenant().getId(), requestId);
          assertThat(deviceRegistrationPolicyMessageRecord.getPayload())
              .matches(p -> p.getTenantId().equals(ap.getTenant().getId()))
              .matches(p -> p.getId().equals(ap.getId()))
              .matches(p -> p.getPolicy().equals(Policy.APPROVED));
          repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId);

          final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(ap.getTenant().getId(), requestId);
          verifyVenueCmnCollectorMessage(cmnCfgCollectorMessage, apGroup.getVenue().getId(),
              tuple("R710", "*******.2"));
          verifyVenueCurrentFirmwareAndApFirmwareUpgradeRequest(ap, biggestVersion, "R710");
        }
      }

      @Nested
      @FeatureFlag(enable = FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE)
      class whenApFwMgmtEarlyAccessToggleOn {

        @Test
        void thenSetApFwToEarlyAccessVersion(ApGroup apGroup) {
          doReturn(new TenantEarlyAccessInfo(true, false))
              .when(tenantClient).getEarlyAccessInfo(anyString(), anyString());

          String tenantId = apGroup.getTenant().getId();
          ApVersion version = ApVersionTestFixture.recommendedApVersion("*******.1", v -> {
            v.setSupportedApModels(List.of("R710"));
            v.setLabels(List.of(ApVersionLabelEnum.GA));
          });
          ApVersion biggestGaVersion = ApVersionTestFixture.recommendedApVersion("*******.2", v -> {
            v.setSupportedApModels(List.of("R710"));
            v.setLabels(List.of(ApVersionLabelEnum.GA));
          });
          ApVersion betaVersion = ApVersionTestFixture.recommendedApVersion("*******.3", v -> {
            v.setSupportedApModels(List.of("R710"));
            v.setLabels(List.of(ApVersionLabelEnum.BETA));
          });
          ApVersion alphaVersion = ApVersionTestFixture.recommendedApVersion("*******.4", v -> {
            v.setSupportedApModels(List.of("R710"));
            v.setLabels(List.of(ApVersionLabelEnum.ALPHA));
          });
          repositoryUtil.createOrUpdate(version, tenantId, randomTxId());
          repositoryUtil.createOrUpdate(biggestGaVersion, tenantId, randomTxId());
          repositoryUtil.createOrUpdate(betaVersion, tenantId, randomTxId());
          repositoryUtil.createOrUpdate(alphaVersion, tenantId, randomTxId());

          Ap ap = ApTestFixture.randomAp(apGroup, a -> a.setName(randomName()));
          repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
          repositoryUtil.createOrUpdate(
              TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(ap.getTenant(),
                  biggestGaVersion, "R710"),
              tenantId,
              randomTxId());

          repositoryUtil.createOrUpdate(ap, tenantId);
          createTenantAvailableApFirmwares(apGroup.getTenant(), List.of(version, biggestGaVersion));

          var requestId = randomTxId();
          messageUtil.sendDeviceRegistration(tenantId, requestId,
              DeviceRegistration.newBuilder()
                  .setSerial(ap.getId())
                  .setModel("R710")
                  .setMac("11:22:33:44:55:66")
                  .setTenantId(tenantId)
                  .build());

          final var deviceRegistrationPolicyMessageRecord
              = messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
              .getValue(ap.getTenant().getId(), requestId);
          assertThat(deviceRegistrationPolicyMessageRecord.getPayload())
              .matches(p -> p.getTenantId().equals(ap.getTenant().getId()))
              .matches(p -> p.getId().equals(ap.getId()))
              .matches(p -> p.getPolicy().equals(Policy.APPROVED));
          repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId);

          final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(ap.getTenant().getId(), requestId);
          verifyVenueCmnCollectorMessage(cmnCfgCollectorMessage, apGroup.getVenue().getId(),
              tuple("R710", "*******.3"));

          // The ddccm message from async task
          var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(ap.getTenant().getId(), 2);
          assertThat(ddccmMessage).isNotNull();

          verifyVenueCurrentFirmwareAndApFirmwareUpgradeRequest(ap, betaVersion, "R710");
        }
      }
    }

    @Nested
    @FeatureFlag(enable = FlagNames.WIFI_SUPPORT_SECOND_ZONE_GPB_FOR_CBAND_TOGGLE)
    class whenSupportecondZoneGpbForCband {

      @Test
      void thenTriggerDdccmZoneSync(ApGroup apGroup) {
        String tenantId = apGroup.getTenant().getId();
        Ap ap = ApTestFixture.randomAp(apGroup, a -> a.setName(randomName()));
        repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
        ApVersion version = ApVersionTestFixture.recommendedApVersion("6.0.0.0.1",
            v -> v.setSupportedApModels(List.of("R500")));
        ApVersion biggestVersion = ApVersionTestFixture.recommendedApVersion("*******.2",
            v -> v.setSupportedApModels(List.of("R770")));
        repositoryUtil.createOrUpdate(version, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(biggestVersion, tenantId, randomTxId());

        createTenantAvailableApFirmwares(apGroup.getTenant(), List.of(version, biggestVersion));

        var requestId = randomTxId();
        messageUtil.sendDeviceRegistration(tenantId, requestId,
            DeviceRegistration.newBuilder()
                .setSerial(ap.getId())
                .setModel("R500")
                .setMac("11:22:33:44:55:66")
                .setTenantId(tenantId)
                .build());

        Venue venue = repositoryUtil.find(Venue.class, apGroup.getVenue().getId());
        venue.setWifiFirmwareVersion(biggestVersion);
        repositoryUtil.createOrUpdate(venue, tenantId, randomTxId());

        Ap ap2 = ApTestFixture.randomAp(apGroup, a -> a.setName(randomName()));
        repositoryUtil.createOrUpdate(ap2, tenantId, randomTxId());

        var requestId2 = randomTxId();
        messageUtil.sendDeviceRegistration(tenantId, requestId2,
            DeviceRegistration.newBuilder()
                .setSerial(ap2.getId())
                .setModel("R770")
                .setMac("aa:bb:cc:dd:ee:ff")
                .setTenantId(tenantId)
                .build());

        final var deviceRegistrationPolicyMessageRecord
            = messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
            .getValue(ap2.getTenant().getId(), requestId2);

        assertThat(deviceRegistrationPolicyMessageRecord.getPayload())
            .matches(p -> p.getTenantId().equals(ap2.getTenant().getId()))
            .matches(p -> p.getId().equals(ap2.getId()))
            .matches(p -> p.getPolicy().equals(Policy.APPROVED));
      }
    }

    private void createTenantAvailableApFirmwares(Tenant tenant, List<ApVersion> apVersions) {
      apVersions.forEach(v -> {
        TenantAvailableApFirmware taaf = new TenantAvailableApFirmware();
        taaf.setId(randomTxId());
        taaf.setTenant(tenant);
        taaf.setApVersion(v);
        repositoryUtil.createOrUpdate(taaf, tenant.getId(), randomTxId());
      });
    }

    private void verifyVenueCmnCollectorMessage(
        KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage,
        String venueId, Tuple... modelAndCurrentApFirmwares) {
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(ViewmodelCollector::getOperationsList).asList()
          .isNotEmpty()
          .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
          .filteredOn(op -> Index.VENUE.equals(op.getIndex()) && op.getOpType() == OpType.MOD)
          .filteredOn(
              op -> venueId.equals(op.getId()) && op.getDocMap()
                  .containsKey(EsConstants.Key.CURRENT_AP_FIRMWARES))
          .isNotEmpty().singleElement()
          .extracting(Operations::getDocMap)
          .satisfies(docMap -> {
            assertThat(
                docMap.get(EsConstants.Key.CURRENT_AP_FIRMWARES).getListValue().getValuesList())
                .extracting(c -> c.getStructValue().getFieldsMap())
                .extracting(map -> map.get(Key.AP_MODEL).getStringValue(),
                    map -> map.get(Key.FIRMWARE).getStringValue())
                .containsExactlyInAnyOrder(modelAndCurrentApFirmwares);
            assertThat(docMap.get(Key.LAST_AP_FIRMWARE_UPDATE).getStringValue()).isEmpty();
            assertThat(docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE).getBoolValue()).isTrue();
          });
    }

    private void verifyVenueCurrentFirmwareAndApFirmwareUpgradeRequest(Ap ap,
        ApVersion biggestVersion, String model) {
      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, ap.getTenant().getId()))
          .isNotEmpty()
          .filteredOn(vcf -> vcf.getApModel().equals(model))
          .hasSize(1)
          .singleElement()
          .extracting(VenueCurrentFirmware::getFirmware)
          .extracting(ApVersion::getId)
          .isEqualTo(biggestVersion.getId());

      // Waiting for the async job to save ApFirmwareUpgradeRequest into the database
      await().atMost(5000, MILLISECONDS)
          .pollInterval(100, MILLISECONDS)
          .untilAsserted(() -> {
            assertThat(repositoryUtil.findAll(ApFirmwareUpgradeRequest.class, ap.getTenant().getId()))
                .isNotEmpty()
                .filteredOn(afur -> afur.getSerialNumber().equals(ap.getId()))
                .hasSize(1)
                .singleElement()
                .extracting(ApFirmwareUpgradeRequest::getTargetVersion)
                .isEqualTo(biggestVersion.getId());
          });
    }
  }

  @Nested
  class whenConsumeDeviceCsr {

    @BeforeEach
    void setupApCert(Ap ap) {
      ApTlsEnhancedCertInfo enhancedCertInfo = new ApTlsEnhancedCertInfo();
      enhancedCertInfo.setCert("Default Cert");
      enhancedCertInfo.setCaChain("Default CA Chain");
      ap.setTlsEnhancedCertInfo(enhancedCertInfo);
      repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), randomTxId());
    }

    @Test
    @FeatureFlag(enable = WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE)
    void thenSaveDeviceCsrAndPublishCsr(Ap ap) {

      var requestId = randomTxId();
      messageUtil.sendDeviceCsr(ap.getId(),
          requestId,
          DeviceCsr.newBuilder()
              .setSerial(ap.getId())
              .setCsr(TEST_CSR_PEM)
              .setTenantId(ap.getTenant().getId())
              .build());

      final var enhancedMessageRecord = messageCaptors.getEnhancedCsrMessageCaptor().getValue(ap.getTenant().getId());

      assertThat(enhancedMessageRecord)
          .isNotNull();
      assertThat(enhancedMessageRecord.getPayload())
          .isNotNull();
      assertThat(enhancedMessageRecord.getPayload())
          .matches(p -> p.getId().equals(ap.getId()))
          .matches(p -> p.getCsrPem().equals(TEST_CSR_PEM));

      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(ap.getTenant().getId());
    }

    @Test
    @FeatureFlag(enable = {WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE})
    void thenHandledByAsync(Ap ap) {
      var requestId = randomTxId();
      messageUtil.sendDeviceCsr(ap.getId(),
          requestId,
          DeviceCsr.newBuilder()
              .setSerial(ap.getId())
              .setCsr(TEST_CSR_PEM)
              .setTenantId(ap.getTenant().getId())
              .build());

      final var asyncMessage = messageCaptors.getWifiDeviceRegistAsyncJobMessageCaptor()
          .getValue(ap.getTenant().getId());
      assertThat(asyncMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .isNotNull();
      assertThat(asyncMessage.getPayload())
          .extracting(WifiAsyncJob::getDeviceRealtimeDataJob)
          .extracting(DeviceRealtimeDataJob::getType)
          .isEqualTo(ApRealtimeDataTypeEnum_DEVICE_CSR);

      final var enhancedMessageRecord = messageCaptors.getEnhancedCsrMessageCaptor().getValue(ap.getTenant().getId());

      assertThat(enhancedMessageRecord)
          .isNotNull();
      assertThat(enhancedMessageRecord.getPayload())
          .isNotNull();
      assertThat(enhancedMessageRecord.getPayload())
          .matches(p -> p.getId().equals(ap.getId()))
          .matches(p -> p.getCsrPem().equals(TEST_CSR_PEM));

      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(ap.getTenant().getId());
    }
  }

  @Nested
  class whenAsyncDeviceRegistEnabled {

    @Test
    void thenShouldGetEarlyApprovedAndHandledByAsync(ApGroup apGroup) {
      Ap ap = new Ap(randomSerialNumber());
      ap.setName(randomName());
      ap.setApGroup(apGroup);
      ap.setTenant(apGroup.getTenant());
      repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), randomTxId());

      var requestId = randomTxId();
      messageUtil.sendDeviceRegistration(apGroup.getTenant().getId(),
          requestId,
          DeviceRegistration.newBuilder()
              .setSerial(ap.getId())
              .setModel("R710")
              .setMac("11:22:33:44:55:66")
              .build());

      final var deviceRegistrationPolicyMessageRecord = messageCaptors
          .getDeviceRegistrationPolicyMessageCaptor()
          .getValue(ap.getTenant().getId(), requestId);
      assertThat(deviceRegistrationPolicyMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .isNotNull();
      assertThat(deviceRegistrationPolicyMessageRecord.getPayload())
          .matches(p -> p.getTenantId().equals(ap.getTenant().getId()))
          .matches(p -> p.getId().equals(ap.getId()))
          .matches(p -> p.getPolicy().equals(Policy.APPROVED));

      final var asyncMessage = messageCaptors.getWifiDeviceRegistAsyncJobMessageCaptor()
          .getValue(ap.getTenant().getId());
      assertThat(asyncMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .isNotNull();
      assertThat(asyncMessage.getPayload())
          .extracting(WifiAsyncJob::getDeviceRealtimeDataJob)
          .extracting(DeviceRealtimeDataJob::getType)
          .isEqualTo(ApRealtimeDataTypeEnum_DEVICE_REGISTRATION);

      final var ddccmMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(ap.getTenant().getId());
      assertThat(ddccmMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .isNotNull();
      assertThat(ddccmMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList).asList()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
          .filteredOn(
              com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp)
          .extracting(Operation::getAp)
          .hasSize(1)
          .singleElement()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getMac)
          .extracting(StringValue::getValue)
          .isEqualTo("11:22:33:44:55:66");
    }
  }
}
