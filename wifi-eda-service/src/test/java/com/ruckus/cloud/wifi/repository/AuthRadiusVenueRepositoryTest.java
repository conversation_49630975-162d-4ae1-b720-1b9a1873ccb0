package com.ruckus.cloud.wifi.repository;


import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.specification.RadiusVenueSpecification;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('e8c4a93fa9f649e188171a2ed611ba46');
    INSERT INTO venue (id, name, tenant) VALUES
      ('c2fc215256b849c3a7dddfcf5f670ed8','venue-1', 'e8c4a93fa9f649e188171a2ed611ba46');
    INSERT INTO network (id, name, type, tenant) VALUES (
      'b2a0a6ad9b91493b98a8889b226f48ae','network','DPSK', 'e8c4a93fa9f649e188171a2ed611ba46');
    INSERT INTO radius (id, tenant) VALUES 
    ('61d16400d85f4e6d99d8694f7317f134', 'e8c4a93fa9f649e188171a2ed611ba46');
    INSERT INTO auth_radius_venue (id, venue, radius, tenant) VALUES 
    ('7f96030ff17b48f6b1f399b6fbb3f3d8', 'c2fc215256b849c3a7dddfcf5f670ed8', '61d16400d85f4e6d99d8694f7317f134', 'e8c4a93fa9f649e188171a2ed611ba46');
        
    INSERT INTO tenant (id) VALUES ('3a7dddfcf5f670ed8e8c4a93fa9f649e');
    INSERT INTO venue (id, name, tenant) VALUES
        ('3b98a8889b226f48a0a6ad9b91493b98','venue-2', '3a7dddfcf5f670ed8e8c4a93fa9f649e');
    INSERT INTO network (id, name, type, tenant) VALUES (
        '5d85f4e6d99d8694f7317f13461d1640','network-2','PSK', '3a7dddfcf5f670ed8e8c4a93fa9f649e');
    INSERT INTO radius (id, tenant) VALUES
        ('7f17b48f6b1f399b6fbb3f3d87f96030', '3a7dddfcf5f670ed8e8c4a93fa9f649e');
    INSERT INTO auth_radius_venue (id, venue, radius, tenant) VALUES
        ('8c4a93fa9f649e188171a2ed611ba467f96030', '3b98a8889b226f48a0a6ad9b91493b98', '7f17b48f6b1f399b6fbb3f3d87f96030', '3a7dddfcf5f670ed8e8c4a93fa9f649e');
          
    INSERT INTO venue (id, name, tenant) VALUES
        ('89b226f48ae61d16400d85f4e6d99d8','venue-3', '3a7dddfcf5f670ed8e8c4a93fa9f649e');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'a9f649e188171a2ed611ba463b98a888','network-3','WPA', '3a7dddfcf5f670ed8e8c4a93fa9f649e');
    INSERT INTO radius (id, tenant) VALUES
        ('b48f6b1f399b6fbb3f3d87f96030ff17', '3a7dddfcf5f670ed8e8c4a93fa9f649e');
    INSERT INTO auth_radius_venue (id, venue, radius, tenant) VALUES
        ('c4a93fa9f649e188171a2ed611ba467f96030', '89b226f48ae61d16400d85f4e6d99d8', 'b48f6b1f399b6fbb3f3d87f96030ff17', '3a7dddfcf5f670ed8e8c4a93fa9f649e');
    """)
@WifiJpaDataTest
public class AuthRadiusVenueRepositoryTest {

  @Autowired
  private AuthRadiusVenueRepository unit;
  @Test
  void queryAuthRadiusVenues() {
    assertThat(unit.findAll(RadiusVenueSpecification.findAuthRadiusVenueByVenueIdAndRadiusId(
        List.of(toAuthRadiusVenue("c2fc215256b849c3a7dddfcf5f670ed8",
            "61d16400d85f4e6d99d8694f7317f134"))
        , "e8c4a93fa9f649e188171a2ed611ba46")))
        .hasSize(1)
        .extracting("id")
        .contains("7f96030ff17b48f6b1f399b6fbb3f3d8");

    assertThat(unit.findAll(RadiusVenueSpecification.findAuthRadiusVenueByVenueIdAndRadiusId(
        List.of(toAuthRadiusVenue("3b98a8889b226f48a0a6ad9b91493b98",
                "7f17b48f6b1f399b6fbb3f3d87f96030"),
            toAuthRadiusVenue("89b226f48ae61d16400d85f4e6d99d8",
                "b48f6b1f399b6fbb3f3d87f96030ff17"))
        , "3a7dddfcf5f670ed8e8c4a93fa9f649e")))
        .hasSize(2)
        .extracting("id")
        .contains("8c4a93fa9f649e188171a2ed611ba467f96030",
            "c4a93fa9f649e188171a2ed611ba467f96030");
  }

  private AuthRadiusVenue toAuthRadiusVenue(String venueId, String radiusId) {
    var authRadiusVenue = new AuthRadiusVenue();
    authRadiusVenue.setVenue(new Venue(venueId));
    authRadiusVenue.setRadius(new Radius(radiusId));
    return authRadiusVenue;
  }
}
