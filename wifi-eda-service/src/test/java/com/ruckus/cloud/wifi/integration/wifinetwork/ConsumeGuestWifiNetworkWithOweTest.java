package com.ruckus.cloud.wifi.integration.wifinetwork;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.templateString;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.DirectoryServerProfileTestFixture.randomDirectoryServerProfile;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.reflect.TypeToken;
import com.google.protobuf.ProtocolStringList;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.entity.association.protobuf.AssociatedProfile;
import com.ruckus.cloud.entity.association.protobuf.EntityAssociation;
import com.ruckus.cloud.entity.association.protobuf.EntityAssociationList;
import com.ruckus.cloud.entity.association.protobuf.EntityType;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractWlan;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.GuestWifiNetworkGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.GuestWlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetwork;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenuePortalTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
public class ConsumeGuestWifiNetworkWithOweTest extends AbstractRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired private RevisionService revisionService;

  @Test
  void createGuestNetworkWithOweTransitionThenDelete(Tenant tenant) {
    // Given
    String ssid = "aa1234567890123456789012345678";
    var networkRequest = getOweTransNetwork(randomId(), ssid);

    // When
    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.ADD_WIFI_NETWORK,
        randomName(),
        new RequestParams(),
        networkRequest);

    // Then
    var master = repositoryUtil.find(GuestNetwork.class, networkRequest.getId());
    assertNotNull(master);
    var slave = repositoryUtil.find(GuestNetwork.class, master.getOwePairNetworkId());

    String masterId = master.getId();
    assertNotNull(slave);
    String slaveId = slave.getId();

    assertThat(master)
        .isNotNull()
        .matches(n -> n.getId().equals(networkRequest.getId()))
        .matches(n -> n.getName().equals(networkRequest.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.GUEST))
        .matches(n -> n.getOwePairNetworkId().equals(slave.getId()))
        .matches(GuestNetwork::getIsOweMaster)
        .extracting(GuestNetwork::getWlan)
        .isNotNull()
        .matches(AbstractWlan::getBypassCPUsingMacAddressAuthentication)
        .matches(
            wlan ->
                wlan.getWlanSecurity().name().equals(GuestWlanSecurityEnum.OWETransition.name()));

    assertThat(slave)
        .isNotNull()
        .matches(n -> n.getName().equals(ssid + "-owe-tr"))
        .matches(n -> n.getType().equals(NetworkTypeEnum.GUEST))
        .matches(n -> n.getOwePairNetworkId().equals(master.getId()))
        .matches(n -> !n.getIsOweMaster())
        .extracting(GuestNetwork::getWlan)
        .isNotNull()
        .matches(AbstractWlan::getBypassCPUsingMacAddressAuthentication)
        .matches(wlan -> wlan.getSsid().equals(ssid + "-o"))
        .matches(
            wlan ->
                wlan.getWlanSecurity().name().equals(GuestWlanSecurityEnum.OWETransition.name()));

    var deleteRequestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        deleteRequestId,
        CfgAction.DELETE_WIFI_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("wifiNetworkId", master.getId()),
        StringUtils.EMPTY);

    var masterAfterDelete = repositoryUtil.find(GuestNetwork.class, masterId);
    var slaveAfterMasterDelete = repositoryUtil.find(GuestNetwork.class, slaveId);

    assertThat(masterAfterDelete).isNull();
    assertThat(slaveAfterMasterDelete).isNull();
  }

  com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork getOweTransNetwork(
      String networkId, String ssid) {
    return GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
        GuestNetworkTypeEnum.WISPr,
        n -> {
          n.setId(networkId);
          n.setName("masterGuestNetwork");
          n.setDescription(StringUtils.EMPTY);
          n.setWlan(new com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiWlan());
          n.getWlan().setEnabled(true);
          n.getWlan().setSsid(ssid);
          n.getWlan().setVlanId((short) 1);
          n.getWlan().setWlanSecurity(GuestWlanSecurityEnum.OWETransition);
          n.getWlan()
              .setAdvancedCustomization(
                  new com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiWlanAdvancedCustomization());
          n.getGuestPortal()
              .setWisprPage(GuestNetworkViewModelTestFixture.wifiWisprPagePredefinedAAA());
        });
  }

  @Nested
  class GivenDirectoryNetworkPersistedInDb {

    private String networkId;
    private String directoryProfileId;
    private String identityGroupId;
    private String portalServiceProfileId;

    @BeforeEach
    void givenGuestNetworkAndDefaultApGroupAndNetworkVenuePersistedInDb(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork(
                guestNetworkType = GuestNetworkTypeEnum.Directory)
            GuestNetwork guestNetwork) {
      var profile1 =
          repositoryUtil.createOrUpdate(
              randomDirectoryServerProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      guestNetwork.setPortalServiceProfileId(randomId());
      guestNetwork.setDirectoryServerProfile(profile1);
      guestNetwork.setDirectoryProfileId(profile1.getId());
      guestNetwork.setIdentityGroupId(randomId());

      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      networkId = guestNetwork.getId();
      directoryProfileId = profile1.getId();
      identityGroupId = guestNetwork.getIdentityGroupId();
      portalServiceProfileId = guestNetwork.getPortalServiceProfileId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkId);
    }

    @Payload("oweTransitionRequest")
    private GuestWifiNetworkGenerator oweTransitionRequest() {

      var p = Generators.guestWifiPortal();
      p.setGuestNetworkType(always(GuestNetworkTypeEnum.Directory));
      return Generators.guestWifiNetwork()
          .setId(templateString(networkId))
          .setWlan(Generators.oweTransitionGuestWifiWlan())
          .setName(serialName("oweTransitionRequest"))
          .setDescription(randomString(64))
          .setGuestPortal(always(p.generate()));
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("oweTransitionRequest"))
    void testNormalGuestToOweTransitionSuccessfully(
        TxCtx txCtx,
        @Payload("oweTransitionRequest")
            com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      payload.setId(networkId);
      var primary = repositoryUtil.find(GuestNetwork.class, payload.getId());
      assertNotNull(primary);
      var secondary = repositoryUtil.find(GuestNetwork.class, primary.getOwePairNetworkId());
      assertTrue(primary.getIsOweMaster());
      assertNotNull(secondary);
      assertFalse(secondary.getIsOweMaster());
      assertEquals(directoryProfileId, secondary.getDirectoryProfileId());
      assertEquals(directoryProfileId, secondary.getDirectoryServerProfile().getId());
      assertEquals(identityGroupId, secondary.getIdentityGroupId());
      validateEntityAssociationListMessage(
          txCtx,
          Set.of(secondary.getId()),
          Map.of(
              EntityType.IDENTITY_GROUP, identityGroupId,
              EntityType.DIRECTORY, directoryProfileId,
              EntityType.PORTAL_SERVICE, portalServiceProfileId));
      validateCmnCfgCollector(txCtx.getTenant(), txCtx.getTxId());
    }
  }

  @Nested
  class GivenDirectoryOweTransitionNetworkPersistedInDb {

    private String networkId;
    private String secondaryNetworkId;

    @BeforeEach
    void givenOweNetworkVenuesPersistedInDb(Tenant tenant) {
      var profile1 =
          repositoryUtil.createOrUpdate(
              randomDirectoryServerProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      var primarySsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      var primary =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(GuestNetwork.class)
              .generate();
      var secondary =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(GuestNetwork.class)
              .generate();

      primary.setTenant(tenant);
      primary.setDirectoryProfileId(profile1.getId());
      primary.setDirectoryServerProfile(profile1);
      primary.setPortalServiceProfileId(randomId());
      primary.setIdentityGroupId(randomId());
      primary.setName(primarySsid);
      primary.getWlan().setNetwork(primary);
      primary.getWlan().setSsid(primarySsid);
      primary.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      primary.setIsOweMaster(true);
      primary.setOwePairNetworkId(secondary.getId());
      primary.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Directory)
              .generate());
      primary.getGuestPortal().setNetwork(primary);
      primary = repositoryUtil.createOrUpdate(primary, primary.getTenant().getId(), randomTxId());

      secondary.setTenant(tenant);
      secondary.setDirectoryProfileId(profile1.getId());
      secondary.setDirectoryServerProfile(profile1);
      secondary.setPortalServiceProfileId(randomId());
      secondary.setIdentityGroupId(randomId());
      secondary.setName(primarySsid + "-owe-tr");
      secondary.getWlan().setNetwork(secondary);
      secondary.getWlan().setSsid(primarySsid + "-owe-tr");
      secondary.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      secondary.setOwePairNetworkId(primary.getId());
      secondary.setIsOweMaster(false);
      secondary.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Directory)
              .generate());
      secondary.getGuestPortal().setNetwork(secondary);
      secondary =
          repositoryUtil.createOrUpdate(secondary, secondary.getTenant().getId(), randomTxId());

      secondaryNetworkId = secondary.getId();
      networkId = primary.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkId);
    }

    @Payload("normalGuestRequest")
    private GuestWifiNetworkGenerator normalGuestRequest() {
      return Generators.guestWifiNetwork()
          .setId(templateString(networkId))
          .setName(serialName("normalGuestRequest"))
          .setDescription(randomString(64))
          .setGuestPortal(
              always(
                  Generators.guestWifiPortal()
                      .setGuestNetworkType(always(GuestNetworkTypeEnum.Directory))
                      .generate()));
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("normalGuestRequest"))
    void testOweTransitionToNormalGuestSuccessfully(
        TxCtx txCtx,
        @Payload("normalGuestRequest")
            com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      payload.setId(networkId);
      validateEntityAssociationListMessage(
          txCtx,
          Set.of(secondaryNetworkId),
          Map.of(
              EntityType.IDENTITY_GROUP, StringUtils.EMPTY,
              EntityType.DIRECTORY, StringUtils.EMPTY,
              EntityType.PORTAL_SERVICE, StringUtils.EMPTY));
      validateNormalGuestResult(payload);
    }

    @Payload("oweTransactionBackToOweRequest")
    private GuestWifiNetworkGenerator oweTransactionBackToOweRequest() {
      return Generators.guestWifiNetwork()
          .setId(templateString(networkId))
          .setName(serialName("oweTransactionBackToOweRequest"))
          .setDescription(randomString(64))
          .setOwePairNetworkId(randomString(28))
          .setIsOweMaster(alwaysTrue())
          .setWlan(Generators.oweGuestWifiWlan())
          .setGuestPortal(
              always(
                  Generators.guestWifiPortal()
                      .setGuestNetworkType(always(GuestNetworkTypeEnum.Directory))
                      .generate()));
    }

    @Test
    @ApiAction(
        value = CfgAction.UPDATE_WIFI_NETWORK,
        payload = @Payload("oweTransactionBackToOweRequest"))
    void testOweTransitionToOweGuestSuccessfully(
        TxCtx txCtx,
        @Payload("oweTransactionBackToOweRequest")
            com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      payload.setId(networkId);
      validateEntityAssociationListMessage(
          txCtx,
          Set.of(secondaryNetworkId),
          Map.of(
              EntityType.IDENTITY_GROUP, StringUtils.EMPTY,
              EntityType.DIRECTORY, StringUtils.EMPTY,
              EntityType.PORTAL_SERVICE, StringUtils.EMPTY));
      validateNormalGuestResult(payload);
    }
  }

  @Nested
  class GivenSamlNetworkPersistedInDb {

    private String networkId;
    private String samlIdpProfileId;
    private String identityGroupId;
    private String portalServiceProfileId;

    @BeforeEach
    void givenGuestNetworkAndDefaultApGroupAndNetworkVenuePersistedInDb(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork(
                guestNetworkType = GuestNetworkTypeEnum.SAML)
            GuestNetwork guestNetwork) {
      guestNetwork.setPortalServiceProfileId(randomId());
      guestNetwork.setSamlIdpProfileId(randomId());
      guestNetwork.setIdentityGroupId(randomId());

      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      networkId = guestNetwork.getId();
      samlIdpProfileId = guestNetwork.getSamlIdpProfileId();
      identityGroupId = guestNetwork.getIdentityGroupId();
      portalServiceProfileId = guestNetwork.getPortalServiceProfileId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkId);
    }

    @Payload("oweTransitionRequest")
    private GuestWifiNetworkGenerator oweTransitionRequest() {

      var p = Generators.guestWifiPortal();
      p.setGuestNetworkType(always(GuestNetworkTypeEnum.SAML));
      return Generators.guestWifiNetwork()
          .setId(templateString(networkId))
          .setWlan(Generators.oweTransitionGuestWifiWlan())
          .setName(serialName("oweTransitionRequest"))
          .setDescription(randomString(64))
          .setGuestPortal(always(p.generate()));
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("oweTransitionRequest"))
    void testNormalGuestToOweTransitionSuccessfully(
        TxCtx txCtx,
        @Payload("oweTransitionRequest")
            com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      payload.setId(networkId);
      payload.setId(networkId);
      var primary = repositoryUtil.find(GuestNetwork.class, payload.getId());
      assertNotNull(primary);
      var secondary = repositoryUtil.find(GuestNetwork.class, primary.getOwePairNetworkId());
      assertTrue(primary.getIsOweMaster());
      assertNotNull(secondary);
      assertFalse(secondary.getIsOweMaster());
      assertEquals(samlIdpProfileId, secondary.getSamlIdpProfileId());
      assertEquals(identityGroupId, secondary.getIdentityGroupId());
      validateEntityAssociationListMessage(
          txCtx,
          Set.of(secondary.getId()),
          Map.of(
              EntityType.IDENTITY_GROUP, identityGroupId,
              EntityType.SAML, samlIdpProfileId,
              EntityType.PORTAL_SERVICE, portalServiceProfileId));
      validateCmnCfgCollector(txCtx.getTenant(), txCtx.getTxId());
    }
  }

  @Nested
  class GivenWorkflowNetworkPersistedInDb {

    private String networkId;
    private String workflowProfileId;

    @BeforeEach
    void givenGuestNetworkAndDefaultApGroupAndNetworkVenuePersistedInDb(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork(
                guestNetworkType = GuestNetworkTypeEnum.Workflow)
            GuestNetwork guestNetwork) {
      guestNetwork.setWorkflowProfileId(randomId());

      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      networkId = guestNetwork.getId();
      workflowProfileId = guestNetwork.getWorkflowProfileId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkId);
    }

    @Payload("oweTransitionRequest")
    private GuestWifiNetworkGenerator oweTransitionRequest() {

      var p = Generators.guestWifiPortal();
      p.setGuestNetworkType(always(GuestNetworkTypeEnum.Workflow));
      return Generators.guestWifiNetwork()
          .setId(templateString(networkId))
          .setWlan(Generators.oweTransitionGuestWifiWlan())
          .setName(serialName("oweTransitionRequest"))
          .setDescription(randomString(64))
          .setGuestPortal(always(p.generate()));
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("oweTransitionRequest"))
    void testNormalGuestToOweTransitionSuccessfully(
        TxCtx txCtx,
        @Payload("oweTransitionRequest")
            com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      payload.setId(networkId);
      payload.setId(networkId);
      var primary = repositoryUtil.find(GuestNetwork.class, payload.getId());
      assertNotNull(primary);
      var secondary = repositoryUtil.find(GuestNetwork.class, primary.getOwePairNetworkId());
      assertTrue(primary.getIsOweMaster());
      assertNotNull(secondary);
      assertFalse(secondary.getIsOweMaster());
      assertEquals(workflowProfileId, secondary.getWorkflowProfileId());
      validateEntityAssociationListMessage(
          txCtx, Set.of(secondary.getId()), Map.of(EntityType.WORKFLOW, workflowProfileId));
      validateCmnCfgCollector(txCtx.getTenant(), txCtx.getTxId());
    }
  }

  @Nested
  class GivenGuestNetworkPersistedInDb {

    private String networkId;

    @BeforeEach
    void givenGuestNetworkAndDefaultApGroupAndNetworkVenuePersistedInDb(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork(
                guestNetworkType = GuestNetworkTypeEnum.WISPr)
            GuestNetwork guestNetwork,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue
                .ScheduledNetworkVenue
            NetworkVenue networkVenue) {
      var wisprPage =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage().generate();
      var portal = guestNetwork.getGuestPortal();
      portal.setWisprPage(wisprPage);
      wisprPage.setCustomExternalProvider(false);
      wisprPage.setCaptivePortalUrl("https://guestPortal.com");
      wisprPage.setExternalProviderRegion("RegionA");
      wisprPage.setExternalProviderName("TestA");
      networkVenue.setApWlanId(1);
      networkVenue.setVenuePortal(
          VenuePortalTestFixture.randomVenuePortal(
              tenant,
              venuePortal -> {
                venuePortal.setNetworkVenue(networkVenue);
                venuePortal.setNetworkPortal(portal);
              }));
      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      networkId = guestNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkId);
    }

    @Payload("oweTransitionRequest")
    private GuestWifiNetworkGenerator oweTransitionRequest() {
      return Generators.guestWifiNetwork()
          .setId(templateString(networkId))
          .setName(serialName("oweTransitionRequest"))
          .setDescription(randomString(64))
          .setWlan(Generators.oweTransitionGuestWifiWlan())
          .setGuestPortal(
              always(
                  Generators.guestWifiPortal()
                      .setGuestNetworkType(always(GuestNetworkTypeEnum.WISPr))
                      .setWisprPage(
                          always(GuestNetworkViewModelTestFixture.wifiWisprPagePredefinedAAA()))
                      .generate()));
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("oweTransitionRequest"))
    void testNormalGuestToOweTransitionSuccessfully(
        TxCtx txCtx,
        @Payload("oweTransitionRequest")
            com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      payload.setId(networkId);
      validateOweTransitionResult(
          txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.UPDATE_WIFI_NETWORK.key(), true);
    }
  }

  @Nested
  class GivenOweTransitionNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String venue2Id;
    private String networkVenueId;
    private String slaveNetworkId;

    @BeforeEach
    void givenOweNetworkVenuesPersistedInDb(Tenant tenant) {
      var masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      var master =
          (GuestNetwork)
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
                      GuestNetwork.class)
                  .generate();
      var slave =
          (GuestNetwork)
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
                      GuestNetwork.class)
                  .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      master.setIsOweMaster(true);
      master.setOwePairNetworkId(slave.getId());
      master.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.WISPr)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      master.getGuestPortal().setNetwork(master);
      master = repositoryUtil.createOrUpdate(master, master.getTenant().getId(), randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      slave.setOwePairNetworkId(master.getId());
      slave.setIsOweMaster(false);
      slave.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.WISPr)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      slave.getGuestPortal().setNetwork(slave);
      slave = repositoryUtil.createOrUpdate(slave, slave.getTenant().getId(), randomTxId());

      var venue =
          repositoryUtil.createOrUpdate(
              VenueTestFixture.randomVenue(tenant), tenant.getId(), randomTxId());
      var apGroup =
          repositoryUtil.createOrUpdate(
              ApGroupTestFixture.randomApGroup(venue, (e) -> e.setIsDefault(true)),
              tenant.getId(),
              randomTxId());
      venue.setApGroups(List.of(apGroup));
      var venue2 =
          repositoryUtil.createOrUpdate(
              VenueTestFixture.randomVenue(tenant), tenant.getId(), randomTxId());
      var apGroup2 =
          repositoryUtil.createOrUpdate(
              ApGroupTestFixture.randomApGroup(venue2, (e) -> e.setIsDefault(true)),
              tenant.getId(),
              randomTxId());
      venue2.setApGroups(List.of(apGroup2));

      var masterNetworkVenue = NetworkVenueTestFixture.randomNetworkVenue(master, venue);
      var slaveNetworkVenue = NetworkVenueTestFixture.randomNetworkVenue(slave, venue);
      masterNetworkVenue.setApWlanId(1);
      masterNetworkVenue.setOweTransWlanId(2);
      final var finalMaster = master;
      masterNetworkVenue.setVenuePortal(
          VenuePortalTestFixture.randomVenuePortal(
              tenant,
              vportal -> {
                vportal.setNetworkVenue(masterNetworkVenue);
                vportal.setNetworkPortal(finalMaster.getGuestPortal());
              }));
      slaveNetworkVenue.setApWlanId(2);
      final var finalSlave = slave;
      slaveNetworkVenue.setVenuePortal(
          VenuePortalTestFixture.randomVenuePortal(
              tenant,
              vportal -> {
                vportal.setNetworkVenue(slaveNetworkVenue);
                vportal.setNetworkPortal(finalSlave.getGuestPortal());
              }));
      masterNetworkVenue.setOweTransWlanId(1);
      repositoryUtil.createOrUpdate(masterNetworkVenue, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(slaveNetworkVenue, tenant.getId(), randomTxId());

      var masterNetworkApGroup =
          NetworkApGroupTestFixture.randomNetworkApGroup(masterNetworkVenue, apGroup);
      masterNetworkApGroup =
          repositoryUtil.createOrUpdate(masterNetworkApGroup, tenant.getId(), randomTxId());

      var slaveNetworkApGroup =
          NetworkApGroupTestFixture.randomNetworkApGroup(slaveNetworkVenue, apGroup);
      slaveNetworkApGroup =
          repositoryUtil.createOrUpdate(slaveNetworkApGroup, tenant.getId(), randomTxId());

      var masterNetworkApGroupRadio =
          NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(masterNetworkApGroup);
      repositoryUtil.createOrUpdate(masterNetworkApGroupRadio, tenant.getId(), randomTxId());

      var slaveNetworkApGroupRadio =
          NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(slaveNetworkApGroup);
      repositoryUtil.createOrUpdate(slaveNetworkApGroupRadio, tenant.getId(), randomTxId());

      networkVenueId = masterNetworkVenue.getId();
      slaveNetworkId = slaveNetworkVenue.getId();
      networkId = master.getId();
      venueId = venue.getId();
      venue2Id = venue2.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkId);
    }

    @ApiAction.RequestParams("addNetworkVenueParams")
    private RequestParams addNetworkVenueRequestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venue2Id)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @ApiAction.RequestParams("deleteNetworkVenueParams")
    private RequestParams deleteNetworkVenueRequestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Payload("oweTransitionRequest")
    private GuestWifiNetworkGenerator oweTransitionRequest() {
      return Generators.guestWifiNetwork()
          .setId(templateString(networkId))
          .setName(serialName("oweTransitionRequest"))
          .setDescription(randomString(64))
          .setWlan(Generators.oweTransitionGuestWifiWlan())
          .setGuestPortal(
              always(
                  Generators.guestWifiPortal()
                      .setGuestNetworkType(always(GuestNetworkTypeEnum.WISPr))
                      .setWisprPage(
                          always(GuestNetworkViewModelTestFixture.wifiWisprPagePredefinedAAA()))
                      .generate()));
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("oweTransitionRequest"))
    void testOweTransitionToOweTransitionSuccessfully(
        TxCtx txCtx,
        @Payload("oweTransitionRequest")
            com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      payload.setId(networkId);
      validateOweTransitionResult(
          txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.UPDATE_WIFI_NETWORK.key(), false);
    }

    @Payload("normalGuestRequest")
    private GuestWifiNetworkGenerator normalGuestRequest() {
      return Generators.guestWifiNetwork()
          .setId(templateString(networkId))
          .setName(serialName("normalGuestRequest"))
          .setDescription(randomString(64))
          .setGuestPortal(
              always(
                  Generators.guestWifiPortal()
                      .setGuestNetworkType(always(GuestNetworkTypeEnum.WISPr))
                      .setWisprPage(
                          always(GuestNetworkViewModelTestFixture.wifiWisprPagePredefinedAAA()))
                      .generate()));
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("normalGuestRequest"))
    void testOweTransitionToNormalGuestSuccessfully(
        @Payload("normalGuestRequest")
            com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      payload.setId(networkId);
      validateNormalGuestResult(payload);
    }

    @Payload("oweTransactionBackToOweRequest")
    private GuestWifiNetworkGenerator oweTransactionBackToOweRequest() {
      return Generators.guestWifiNetwork()
          .setId(templateString(networkId))
          .setName(serialName("oweTransactionBackToOweRequest"))
          .setDescription(randomString(64))
          .setOwePairNetworkId(randomString(28))
          .setIsOweMaster(alwaysTrue())
          .setWlan(Generators.oweGuestWifiWlan())
          .setGuestPortal(
              always(
                  Generators.guestWifiPortal()
                      .setGuestNetworkType(always(GuestNetworkTypeEnum.WISPr))
                      .setWisprPage(
                          always(GuestNetworkViewModelTestFixture.wifiWisprPagePredefinedAAA()))
                      .generate()));
    }

    @Test
    @ApiAction(
        value = CfgAction.UPDATE_WIFI_NETWORK,
        payload = @Payload("oweTransactionBackToOweRequest"))
    void testOweTransitionToOweGuestSuccessfully(
        @Payload("oweTransactionBackToOweRequest")
            com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      payload.setId(networkId);
      validateNormalGuestResult(payload);
    }

    @Payload
    private VenueWifiNetwork payload() {
      return Generators.venueWifiNetwork().generate();
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE,
        requestParams = @ApiAction.RequestParams("addNetworkVenueParams"))
    void testUpdateNetworkVenue(TxCtx txCtx) {
      validateNetworkVenueResult(
          txCtx.getTenant(), txCtx.getTxId(), CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE.key());
    }

    @Test
    @ApiAction(
        value = CfgAction.DEACTIVATE_WIFI_NETWORK_ON_VENUE,
        requestParams = @ApiAction.RequestParams("deleteNetworkVenueParams"))
    void testDeleteNetworkVenues() {
      var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
      assertNull(networkVenue);
      var slaveNetwork = repositoryUtil.find(NetworkVenue.class, slaveNetworkId);
      assertNull(slaveNetwork);
    }
  }

  private void validateOweTransitionResult(
      String tenantId,
      String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload,
      String apiAction,
      boolean isNetworkVenueUpdated) {

    if (isNetworkVenueUpdated) {
      final var revision =
          revisionService.changes(
              requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueIds =
          revision.getAll().stream()
              .filter(entity -> entity instanceof NetworkVenue)
              .map(BaseEntity::getId)
              .toList();
      assertFalse(networkVenueIds.isEmpty());

      for (var networkVenueId : networkVenueIds) {
        final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
        assertNotNull(networkVenue);
        assertNotEquals(networkVenue.getOweTransWlanId(), networkVenue.getApWlanId());
        assertNotEquals(0, networkVenue.getApWlanId());
        assertNotNull(networkVenue.getScheduler());
      }
    }

    validateCmnCfgCollector(tenantId, requestId);

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(ddccmCfgRequestMessage.getPayload())
                    .extracting(WifiConfigRequest::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(Operation.class::cast)
                    .satisfies(
                        op ->
                            assertSoftly(
                                softly -> {
                                  softly.assertThat(payload).isNotNull();
                                  softly
                                      .assertThat(op)
                                      .filteredOn(Operation::hasWlanVenue)
                                      .describedAs("The count of WlanVenue operations should be 2")
                                      .hasSize(2);
                                })));
  }

  private void validateCmnCfgCollector(String tenantId, String requestId) {

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(cmnCfgCollectorMessage.getPayload())
                    .matches(msg -> msg.getTenantId().equals(tenantId))
                    .matches(msg -> msg.getRequestId().equals(requestId))
                    .extracting(ViewmodelCollector::getOperationsList)
                    .asList()
                    .isNotEmpty());
  }

  private void validateNormalGuestResult(
      com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
    var guestNetwork = repositoryUtil.find(GuestNetwork.class, payload.getId());
    assertNotNull(guestNetwork);
    assertNull(guestNetwork.getOwePairNetworkId());
    assertFalse(guestNetwork.getIsOweMaster());
    assertNotSame(guestNetwork.getWlan().getWlanSecurity(), WlanSecurityEnum.OWETransition);
  }

  private void validateNetworkVenueResult(String tenantId, String requestId, String apiAction) {

    final var revision =
        revisionService.changes(
            requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(apiAction));
    final var networkVenueIds =
        revision.getAll().stream()
            .filter(entity -> entity instanceof NetworkVenue)
            .map(BaseEntity::getId)
            .toList();
    assertFalse(networkVenueIds.isEmpty());

    for (var id : networkVenueIds) {
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, id);
      assertNotNull(networkVenue);
      assertNotEquals(networkVenue.getOweTransWlanId(), networkVenue.getApWlanId());
      assertNotEquals(0, networkVenue.getApWlanId());
      assertEquals(4, networkVenue.getNetworkApGroups().get(0).getNetworkApGroupRadios().size());
    }

    final var wifiCfgChangeMessage =
        messageCaptors.getWifiCfgChangeMessageCaptor().getValue(tenantId, requestId);

    assertThat(wifiCfgChangeMessage)
        .isNotNull()
        .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .satisfies(
            assertHeader(
                WifiCommonHeader.API_ACTION, CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE.key()))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(wifiCfgChangeMessage.getPayload())
                    .satisfies(
                        msg ->
                            assertSoftly(
                                softly -> {
                                  softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                                  softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
                                }))
                    .extracting(WifiConfigChange::getOperationList)
                    .asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
                    .satisfies(
                        ops ->
                            assertThat(ops)
                                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                                .filteredOn(
                                    op ->
                                        op.getAction()
                                            == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                                .as("The MODIFY NetworkVenue operation count should be 2")
                                .hasSize(2)));
  }

  private void validateEntityAssociationListMessage(
      TxCtx txCtx, Set<String> networkIds, Map<EntityType, String> expectedProfiles) {
    final var entityAssociationListMessage =
        messageCaptors.getExternalAssociationChangeMessageCaptor().getValue(txCtx);
    assertThat(entityAssociationListMessage)
        .isNotNull()
        .satisfies(
            msg -> {
              var headers = msg.getHeaders();
              assertThat(headers).isNotNull();
              assertThat(headers.lastHeader(WifiCommonHeader.WIFI_ENTITY_TYPES))
                  .isNotNull()
                  .extracting(Header::value)
                  .extracting(b -> new String(b, StandardCharsets.UTF_8))
                  .satisfies(
                      json -> {
                        JsonArray jsonArray = new Gson().fromJson(json, JsonArray.class);
                        Set<EntityType> parsedSet =
                            new Gson()
                                .fromJson(jsonArray, new TypeToken<Set<EntityType>>() {}.getType());
                        assertThat(parsedSet).containsAll(expectedProfiles.keySet());
                      });
              assertThat(headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
                  .isNotNull()
                  .extracting(Header::value)
                  .extracting(String::new)
                  .isEqualTo(txCtx.getTxId());
              EntityAssociationList entityAssociationList = msg.getPayload();
              assertThat(entityAssociationList.getAssociationsCount()).isEqualTo(networkIds.size());

              Set<String> foundNetworkIds = new HashSet<>();

              for (EntityAssociation association : entityAssociationList.getAssociationsList()) {
                assertThat(association.getEntityType().name()).isEqualTo("NETWORK");

                String nid = association.getEntityId();
                foundNetworkIds.add(nid);

                var profileMap =
                    association.getProfilesList().stream()
                        .collect(
                            Collectors.toMap(
                                AssociatedProfile::getEntityType,
                                AssociatedProfile::getEntityIdsList));
                for (Map.Entry<EntityType, String> entry : expectedProfiles.entrySet()) {
                  assertEntityProfile(profileMap, entry.getKey(), entry.getValue());
                }
              }
              assertThat(foundNetworkIds).containsAll(networkIds);
            });
  }

  private void assertEntityProfile(
      Map<EntityType, ProtocolStringList> profileMap, EntityType entityType, String expectedId) {
    if (expectedId != null && !expectedId.isBlank()) {
      assertThat(profileMap).containsKey(entityType);
      assertThat(profileMap.get(entityType)).contains(expectedId);
    } else {
      assertThat(profileMap).doesNotContainKey(entityType);
    }
  }
}
