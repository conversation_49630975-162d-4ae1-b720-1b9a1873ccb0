package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class NetworkMulticastRateLimitFeatureTest {

  @SpyBean
  private NetworkMulticastRateLimitFeature unit;

  @Nested
  class WhenMulticastRateLimitConfigInNetwork {

    @Test
    @FeatureFlag(disable = WIFI_R370_TOGGLE)
    void givenR370FFDisable_network(Network network) {
      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = WIFI_R370_TOGGLE)
    void givenMulticastRateLimitDisableForNetwork(Network network) {
      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = WIFI_R370_TOGGLE)
    void givenMulticastRateLimitEnableForNetwork(Network network) {
      var wlan = new Wlan();
      var advanced = new WlanAdvancedCustomization();
      advanced.setEnableMulticastUplinkRateLimiting(true);
      wlan.setAdvancedCustomization(advanced);
      network.setWlan(wlan);

      BDDAssertions.then(unit.test(network)).isTrue();
    }
  }
}
