package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob.JobCase;
import com.ruckus.cloud.wifi.repository.ApGroupRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.MessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class V0_1200__ApGroupPostMigrationConsumerTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageUtil messageUtil;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  private ApGroupRepository apGroupRepository;

  @Autowired
  private V0_1200__ApGroupPostMigrationConsumer postMigrationConsumer;

  @Nested
  class WhenRunPostMigration {

    private List<String> apGroupTenantIds;

    @BeforeEach
    void beforeEach() {
      final var tenants =
          Stream.generate(() -> TenantTestFixture.randomTenant(t -> {
              }))
              .limit(5)
              .peek(tenant -> repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId()))
              .collect(Collectors.toList());
      Collections.shuffle(tenants);
      apGroupTenantIds =
          tenants.stream()
              .limit(3)
              .map(VenueTestFixture::randomVenue)
              .peek(venue -> repositoryUtil.createOrUpdate(
                  venue, venue.getTenant().getId(), randomTxId()))
              .map(ApGroupTestFixture::randomApGroup)
              .peek(apGroup -> repositoryUtil.createOrUpdate(
                  apGroup, apGroup.getTenant().getId(), randomTxId()))
              .map(apGroup -> apGroup.getTenant().getId())
              .toList();
    }

    @Test
    void thenSendAsyncJobForEachTenant() {
      final var expectedTenantIds = new HashSet<>(
          apGroupRepository.findAllDistinctTenantIds());

      postMigrationConsumer.run(null);

      final var receivedTenantIds = new HashSet<String>();
      final var receivedRequestIds = new HashSet<String>();

      for (int i = 0; i < expectedTenantIds.size(); i++) {
        final var message = receive();
        assertThat(message)
            .isNotNull()
            .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID) != null)
            .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID) != null)
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(WifiAsyncJob::getJobCase)
            .isEqualTo(JobCase.AP_GROUP_POST_MIGRATION_JOB);

        receivedTenantIds.add(
            new String(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID).value()));

        receivedRequestIds.add(
            new String(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID).value()));
      }

      assertThat(receivedTenantIds)
          .isNotEmpty()
          .hasSameElementsAs(expectedTenantIds)
          .containsAll(apGroupTenantIds);

      assertThat(receivedRequestIds).isNotEmpty().hasSize(1);
    }

    private KafkaProtoMessage<WifiAsyncJob> receive() {
      return messageUtil.receive(
          kafkaTopicProvider.getWifiAsyncJob(),
          data -> {
            try {
              return WifiAsyncJob.parseFrom(data);
            } catch (InvalidProtocolBufferException e) {
              throw new RuntimeException(e);
            }
          });
    }
  }
}
