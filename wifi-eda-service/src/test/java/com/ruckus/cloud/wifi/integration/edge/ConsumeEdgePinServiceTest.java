package com.ruckus.cloud.wifi.integration.edge;

import static com.ruckus.acx.ddccm.protobuf.common.Action.ADD;
import static com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.edge.protobuf.ChangedConnectResource;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.pin.protobuf.EdgePinService;
import com.ruckus.cloud.pin.protobuf.EdgePinService.Action;
import com.ruckus.cloud.pin.protobuf.EdgePinService.TunneledWlan;
import com.ruckus.cloud.pin.protobuf.EdgePinServiceOrBuilder;
import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.core.model.Identifiable;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelNetworkSegmentTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Collection;
import java.util.Optional;
import java.util.function.Predicate;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("VxLanTunnelFeatureTest")
@WifiIntegrationTest
class ConsumeEdgePinServiceTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void givenTenantPersistedInDb(Tenant tenant) {
    assertThat(tenant.getId()).isEqualTo(txCtxExtension.getTenantId());
  }

  private EdgePinService.Builder newEdgePinServiceBuilder(Action action) {
    return EdgePinService.newBuilder()
        .setRequestId(txCtxExtension.getRequestId())
        .setTenantId(txCtxExtension.getTenantId())
        .setAction(action);
  }

  private void sendEdgePinService(EdgePinService.Builder payloadBuilder) {
    sendEdgePinService(payloadBuilder.build());
  }

  private void sendEdgePinService(EdgePinService payload) {
    messageUtil.sendEdgePinService(
        txCtxExtension.getTenantId(),
        txCtxExtension.getRequestId(),
        payload);
  }

  private record VerificationContext(
      String pinProfileId, String tunnelProfileId,
      boolean isCreateDefaultTunnelProfile,
      String venueId, String networkId,
      String edgeConnectResourceId) {
    public static VerificationContext fromPayload(EdgePinServiceOrBuilder edgePinService) {
      final var optTunneledWlan = Optional.of(edgePinService.getTunneledWlansList())
              .filter(Predicate.not(Collection::isEmpty)).map(list -> list.get(0));
      return new VerificationContext(edgePinService.getId(), edgePinService.getTunnelProfileId(),
          edgePinService.getIsCreateDefaultTunnelProfile(),
          optTunneledWlan.map(TunneledWlan::getVenueId).orElse(null),
          optTunneledWlan.map(TunneledWlan::getNetworkId).orElse(null),
          edgePinService.getConnectResource().getConnectResourceId());
    }
  }

  private static final String DEFAULT_VXLAN_TUNNEL_PROFILE_NAME = "Default";

  @Nested
  class ConsumeCreatePinActionTest {

    private static final String ACTIVITY_STEP_ID = "Add PIN in Wi-Fi service";

    @Test
    void withIsCreateDefaultTunnelProfileBeingTrue() {
      final var expectedPinProfileId = randomId();
      final var expectedTunnelProfileId = randomId();
      final var expectedEdgeConnectResourceId = randomId();

      final var createPinRequestPayload = newEdgePinServiceBuilder(Action.CREATE_PIN)
          .setId(expectedPinProfileId)
          .setTunnelProfileId(expectedTunnelProfileId)
          .setIsCreateDefaultTunnelProfile(true)
          .setConnectResource(ChangedConnectResource.newBuilder()
              .setConnectResourceId(expectedEdgeConnectResourceId)
              .setPinId(expectedPinProfileId));

      sendEdgePinService(createPinRequestPayload);

      verifyActivityMessages(ACTIVITY_STEP_ID);

      final var verificationContext = VerificationContext.fromPayload(createPinRequestPayload);

      verifyRepositoryData(verificationContext);
      verifyDdccmCfgRequestMessages(verificationContext);
      verifyCmnCfgCollectorMessages(verificationContext);
    }

    @Test
    void withIsCreateDefaultTunnelProfileBeingFalse() {
      final var expectedPinProfileId = randomId();
      final var expectedTunnelProfileId = randomId();
      final var expectedEdgeConnectResourceId = randomId();

      repositoryUtil.createOrUpdate(Generators.tunnelProfile()
          .setId(always(expectedTunnelProfileId))
          .setName(always(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
          .setType(always(TunnelNetworkSegmentTypeEnum.VXLAN))
          .generate(), txCtxExtension.getTenantId(), randomTxId());

      final var createPinRequestPayload = newEdgePinServiceBuilder(Action.CREATE_PIN)
          .setId(expectedPinProfileId)
          .setTunnelProfileId(expectedTunnelProfileId)
          .setIsCreateDefaultTunnelProfile(false)
          .setConnectResource(ChangedConnectResource.newBuilder()
              .setConnectResourceId(expectedEdgeConnectResourceId)
              .setPinId(expectedPinProfileId));

      sendEdgePinService(createPinRequestPayload);

      verifyActivityMessages(ACTIVITY_STEP_ID);

      final var verificationContext = VerificationContext.fromPayload(createPinRequestPayload);

      verifyRepositoryData(verificationContext);
      verifyDdccmCfgRequestMessages(verificationContext);
      verifyCmnCfgCollectorMessages(verificationContext);
    }

    private void verifyRepositoryData(VerificationContext context) {
      assertThat(repositoryUtil.find(PinProfile.class, context.pinProfileId())).isNotNull()
          .satisfies(pinProfile -> {
            assertThat(pinProfile.getId()).isEqualTo(context.pinProfileId());
            assertThat(pinProfile.getEdgeConnectResource()).isNotNull()
                .satisfies(edgeConnectResource -> {
                  assertThat(edgeConnectResource.getId())
                      .isEqualTo(context.edgeConnectResourceId());
                  assertThat(edgeConnectResource.getPinProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.pinProfileId());
                  assertThat(edgeConnectResource.getSdLanProfile()).isNull();
                });
            assertThat(pinProfile.getPinProfileRegularSettings()).isNotEmpty()
                .singleElement()
                .satisfies(pinProfileRegularSetting -> {
                  assertThat(pinProfileRegularSetting.getPinProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.pinProfileId());
                  assertThat(pinProfileRegularSetting.getTunnelProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.tunnelProfileId());
                  assertThat(pinProfileRegularSetting.getVenue()).isNull();
                  assertThat(pinProfileRegularSetting.getPinProfileNetworkMappings()).isEmpty();
                });
          });
    }

    private void verifyDdccmCfgRequestMessages(VerificationContext context) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      if (!context.isCreateDefaultTunnelProfile) {
        messageCaptors.getDdccmMessageCaptor().assertNotSent(tenantId, requestId);
        return;
      }

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class))
          .isNotEmpty()
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(ADD);
            assertThat(op.getId()).isEqualTo(context.tunnelProfileId());
            assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
            assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
            assertThat(op.getCcmVxLANTunnelProfile())
                .satisfies(ccmVxLANTunnelProfile -> {
                  assertThat(ccmVxLANTunnelProfile.getId()).isEqualTo(context.tunnelProfileId());
                  assertThat(ccmVxLANTunnelProfile.getName())
                      .isEqualTo(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME);
                });
          });
    }

    private void verifyCmnCfgCollectorMessages(VerificationContext context) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty()
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getIndex()).isEqualTo(Index.TUNNEL_PROFILE_INDEX_NAME);
            assertThat(op.getOpType()).isEqualTo(context.isCreateDefaultTunnelProfile ? OpType.ADD : OpType.MOD);
            assertThat(op.getId()).isEqualTo(context.tunnelProfileId());
            assertThat(op.getDocMap())
                .containsEntry(Key.ID, ValueUtils.stringValue(context.tunnelProfileId()))
                .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
                .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(TunnelNetworkSegmentTypeEnum.VXLAN.name()))
                .hasEntrySatisfying(Key.NETWORK_SEGMENTATION_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isNotEmpty()
                        .singleElement()
                        .extracting(Value::getStringValue).isEqualTo(context.pinProfileId))
                .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isEmpty())
                .hasEntrySatisfying(Key.NETWORK_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isEmpty());
          });
    }
  }

  @Nested
  class ConsumeUpdatePinActionTest {

    private static final String ACTIVITY_STEP_ID = "Update PIN in Wi-Fi service";

    @Nested
    class NotGivenPinProfilePersistedInDb {

      @Test
      void withIsCreateDefaultTunnelProfileBeingTrue() {
        final var expectedPinProfileId = randomId();
        final var expectedTunnelProfileId = randomId();
        final var expectedEdgeConnectResourceId = randomId();

        final var updatePinRequestPayload = newEdgePinServiceBuilder(Action.UPDATE_PIN)
            .setId(expectedPinProfileId)
            .setTunnelProfileId(expectedTunnelProfileId)
            .setIsCreateDefaultTunnelProfile(true)
            .setConnectResource(ChangedConnectResource.newBuilder()
                .setConnectResourceId(expectedEdgeConnectResourceId)
                .setPinId(expectedPinProfileId));

        sendEdgePinService(updatePinRequestPayload);

        verifyActivityMessages(ACTIVITY_STEP_ID);

        final var verificationContext = VerificationContext.fromPayload(updatePinRequestPayload);

        verifyRepositoryData(verificationContext);
        verifyDdccmCfgRequestMessages(verificationContext);
        verifyCmnCfgCollectorMessages(verificationContext);
      }

      @Test
      void withIsCreateDefaultTunnelProfileBeingFalse() {
        final var expectedPinProfileId = randomId();
        final var expectedTunnelProfileId = randomId();
        final var expectedEdgeConnectResourceId = randomId();

        repositoryUtil.createOrUpdate(Generators.tunnelProfile()
            .setId(always(expectedTunnelProfileId))
            .setName(always(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
            .setType(always(TunnelNetworkSegmentTypeEnum.VXLAN))
            .generate(), txCtxExtension.getTenantId(), randomTxId());

        final var updatePinRequestPayload = newEdgePinServiceBuilder(Action.UPDATE_PIN)
            .setId(expectedPinProfileId)
            .setTunnelProfileId(expectedTunnelProfileId)
            .setIsCreateDefaultTunnelProfile(false)
            .setConnectResource(ChangedConnectResource.newBuilder()
                .setConnectResourceId(expectedEdgeConnectResourceId)
                .setPinId(expectedPinProfileId));

        sendEdgePinService(updatePinRequestPayload);

        verifyActivityMessages(ACTIVITY_STEP_ID);

        final var verificationContext = VerificationContext.fromPayload(updatePinRequestPayload);

        verifyRepositoryData(verificationContext);
        verifyDdccmCfgRequestMessages(verificationContext);
        verifyCmnCfgCollectorMessages(verificationContext);
      }

      private void verifyCmnCfgCollectorMessages(VerificationContext context) {
        final String requestId = txCtxExtension.getRequestId();
        final String tenantId = txCtxExtension.getTenantId();

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId)).isNotNull()
            .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
            .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .satisfies(msg -> {
              assertThat(msg.getTenantId()).isEqualTo(tenantId);
              assertThat(msg.getRequestId()).isEqualTo(requestId);
            })
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .singleElement()
            .satisfies(op -> {
              assertThat(op.getIndex()).isEqualTo(Index.TUNNEL_PROFILE_INDEX_NAME);
              assertThat(op.getOpType()).isEqualTo(context.isCreateDefaultTunnelProfile ? OpType.ADD : OpType.MOD);
              assertThat(op.getId()).isEqualTo(context.tunnelProfileId());
              assertThat(op.getDocMap())
                  .containsEntry(Key.ID, ValueUtils.stringValue(context.tunnelProfileId()))
                  .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
                  .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(TunnelNetworkSegmentTypeEnum.VXLAN.name()))
                  .hasEntrySatisfying(Key.NETWORK_SEGMENTATION_IDS,
                      value -> assertThat(value.getListValue().getValuesList()).isNotEmpty()
                          .singleElement()
                          .extracting(Value::getStringValue).isEqualTo(context.pinProfileId))
                  .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS,
                      value -> assertThat(value.getListValue().getValuesList()).isEmpty())
                  .hasEntrySatisfying(Key.NETWORK_IDS,
                      value -> assertThat(value.getListValue().getValuesList()).isEmpty());
            });
      }
    }

    @Nested
    class GivenPinProfilePersistedInDb {

      private final String expectedPinProfileId = randomId();
      private final String originalTunnelProfileId = randomId();
      private final String expectedTunnelProfileId = randomId();
      private final String expectedEdgeConnectResourceId = randomId();

      @BeforeEach
      void givenPinProfilePersistedInDb() {
        final var originalTunnelProfile = repositoryUtil.createOrUpdate(
            Generators.tunnelProfile().setId(always(originalTunnelProfileId)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        final var pinProfile = repositoryUtil.createOrUpdate(
            Generators.pinProfile().setId(always(expectedPinProfileId))
                .setPinProfileRegularSettings(
                    Generators.pinProfileRegularSetting().toListGenerator(0)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        repositoryUtil.createOrUpdate(
            Generators.pinProfileRegularSetting().setId(nullValue(String.class))
                .setTunnelProfile(always(originalTunnelProfile))
                .setPinProfile(always(pinProfile)).setPinProfileNetworkMappings(
                    Generators.pinProfileNetworkMapping().toListGenerator(0)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        repositoryUtil.createOrUpdate(
            Generators.edgeConnectResource().setId(always(expectedEdgeConnectResourceId))
                .setPinProfile(always(pinProfile)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
      }

      @Test
      void withIsCreateDefaultTunnelProfileBeingTrue() {
        final var updatePinRequestPayload = newEdgePinServiceBuilder(Action.UPDATE_PIN)
            .setId(expectedPinProfileId)
            .setTunnelProfileId(expectedTunnelProfileId)
            .setIsCreateDefaultTunnelProfile(true)
            .setConnectResource(ChangedConnectResource.newBuilder()
                .setConnectResourceId(expectedEdgeConnectResourceId)
                .setPinId(expectedPinProfileId));

        sendEdgePinService(updatePinRequestPayload);

        verifyActivityMessages(ACTIVITY_STEP_ID);

        final var verificationContext = VerificationContext.fromPayload(updatePinRequestPayload);

        verifyRepositoryData(verificationContext);
        verifyDdccmCfgRequestMessages(verificationContext);
        verifyCmnCfgCollectorMessages(verificationContext);
      }

      @Test
      void withIsCreateDefaultTunnelProfileBeingFalse() {
        repositoryUtil.createOrUpdate(Generators.tunnelProfile()
            .setId(always(expectedTunnelProfileId))
            .setName(always(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
            .setType(always(TunnelNetworkSegmentTypeEnum.VXLAN))
            .generate(), txCtxExtension.getTenantId(), randomTxId());

        final var updatePinRequestPayload = newEdgePinServiceBuilder(Action.UPDATE_PIN)
            .setId(expectedPinProfileId)
            .setTunnelProfileId(expectedTunnelProfileId)
            .setIsCreateDefaultTunnelProfile(false)
            .setConnectResource(ChangedConnectResource.newBuilder()
                .setConnectResourceId(expectedEdgeConnectResourceId)
                .setPinId(expectedPinProfileId));

        sendEdgePinService(updatePinRequestPayload);

        verifyActivityMessages(ACTIVITY_STEP_ID);

        final var verificationContext = VerificationContext.fromPayload(updatePinRequestPayload);

        verifyRepositoryData(verificationContext);
        verifyDdccmCfgRequestMessages(verificationContext);
        verifyCmnCfgCollectorMessages(verificationContext);
      }

      private void verifyCmnCfgCollectorMessages(VerificationContext context) {
        final String requestId = txCtxExtension.getRequestId();
        final String tenantId = txCtxExtension.getTenantId();

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId)).isNotNull()
            .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
            .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .satisfies(msg -> {
              assertThat(msg.getTenantId()).isEqualTo(tenantId);
              assertThat(msg.getRequestId()).isEqualTo(requestId);
            })
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty()
            .hasSize(2)
            .satisfies(ops -> {
              assertThat(ops)
                  .filteredOn(op -> originalTunnelProfileId.equals(op.getId()))
                  .singleElement()
                  .satisfies(op -> {
                    assertThat(op.getIndex()).isEqualTo(Index.TUNNEL_PROFILE_INDEX_NAME);
                    assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                    assertThat(op.getDocMap())
                        .containsEntry(Key.ID, ValueUtils.stringValue(originalTunnelProfileId))
                        .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(TunnelNetworkSegmentTypeEnum.VXLAN.name()))
                        .hasEntrySatisfying(Key.NETWORK_SEGMENTATION_IDS,
                            value -> assertThat(value.getListValue().getValuesList()).isEmpty())
                        .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS,
                            value -> assertThat(value.getListValue().getValuesList()).isEmpty())
                        .hasEntrySatisfying(Key.NETWORK_IDS,
                            value -> assertThat(value.getListValue().getValuesList()).isEmpty());
                  });
              assertThat(ops)
                  .filteredOn(op -> context.tunnelProfileId().equals(op.getId()))
                  .singleElement()
                  .satisfies(op -> {
                    assertThat(op.getIndex()).isEqualTo(Index.TUNNEL_PROFILE_INDEX_NAME);
                    assertThat(op.getOpType()).isEqualTo(context.isCreateDefaultTunnelProfile ? OpType.ADD : OpType.MOD);
                    assertThat(op.getDocMap())
                        .containsEntry(Key.ID, ValueUtils.stringValue(context.tunnelProfileId()))
                        .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
                        .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(TunnelNetworkSegmentTypeEnum.VXLAN.name()))
                        .hasEntrySatisfying(Key.NETWORK_SEGMENTATION_IDS,
                            value -> assertThat(value.getListValue().getValuesList()).isNotEmpty()
                                .singleElement()
                                .extracting(Value::getStringValue).isEqualTo(context.pinProfileId))
                        .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS,
                            value -> assertThat(value.getListValue().getValuesList()).isEmpty())
                        .hasEntrySatisfying(Key.NETWORK_IDS,
                            value -> assertThat(value.getListValue().getValuesList()).isEmpty());
                  });
            });
      }
    }

    private void verifyRepositoryData(VerificationContext context) {
      assertThat(repositoryUtil.find(PinProfile.class, context.pinProfileId())).isNotNull()
          .satisfies(pinProfile -> {
            assertThat(pinProfile.getId()).isEqualTo(context.pinProfileId());
            assertThat(pinProfile.getEdgeConnectResource()).isNotNull()
                .satisfies(edgeConnectResource -> {
                  assertThat(edgeConnectResource.getId())
                      .isEqualTo(context.edgeConnectResourceId());
                  assertThat(edgeConnectResource.getPinProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.pinProfileId());
                  assertThat(edgeConnectResource.getSdLanProfile()).isNull();
                });
            assertThat(pinProfile.getPinProfileRegularSettings()).isNotEmpty()
                .singleElement()
                .satisfies(pinProfileRegularSetting -> {
                  assertThat(pinProfileRegularSetting.getPinProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.pinProfileId());
                  assertThat(pinProfileRegularSetting.getTunnelProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.tunnelProfileId());
                  assertThat(pinProfileRegularSetting.getVenue()).isNull();
                  assertThat(pinProfileRegularSetting.getPinProfileNetworkMappings()).isEmpty();
                });
          });
    }

    private void verifyDdccmCfgRequestMessages(VerificationContext context) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      if (!context.isCreateDefaultTunnelProfile) {
        messageCaptors.getDdccmMessageCaptor().assertNotSent(tenantId, requestId);
        return;
      }

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class))
          .isNotEmpty()
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(ADD);
            assertThat(op.getId()).isEqualTo(context.tunnelProfileId());
            assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
            assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
            assertThat(op.getCcmVxLANTunnelProfile())
                .satisfies(ccmVxLANTunnelProfile -> {
                  assertThat(ccmVxLANTunnelProfile.getId()).isEqualTo(context.tunnelProfileId());
                  assertThat(ccmVxLANTunnelProfile.getName())
                      .isEqualTo(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME);
                });
          });
    }
  }

  @Nested
  class ConsumeDeletePinActionTest {

    private static final String ACTIVITY_STEP_ID = "Delete PIN in Wi-Fi service";

    @Nested
    class GivenPinProfilePersistedInDb {

      private final String expectedPinProfileId = randomId();
      private final String expectedTunnelProfileId = randomId();
      private final String expectedEdgeConnectResourceId = randomId();

      @BeforeEach
      void givenPinProfilePersistedInDb() {
        final var tunnelProfile = repositoryUtil.createOrUpdate(
            Generators.tunnelProfile().setId(always(expectedTunnelProfileId))
                .setName(always(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
                .setType(always(TunnelNetworkSegmentTypeEnum.VXLAN)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        final var pinProfile = repositoryUtil.createOrUpdate(
            Generators.pinProfile().setId(always(expectedPinProfileId))
                .setPinProfileRegularSettings(
                    Generators.pinProfileRegularSetting().toListGenerator(0)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        repositoryUtil.createOrUpdate(
            Generators.pinProfileRegularSetting().setId(nullValue(String.class))
                .setTunnelProfile(always(tunnelProfile))
                .setPinProfile(always(pinProfile)).setPinProfileNetworkMappings(
                    Generators.pinProfileNetworkMapping().toListGenerator(0)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        repositoryUtil.createOrUpdate(
            Generators.edgeConnectResource().setId(always(expectedEdgeConnectResourceId))
                .setPinProfile(always(pinProfile)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
      }

      @Test
      void whenConsumeDeletePinActionRequest() {
        final var deletePinRequestPayload = newEdgePinServiceBuilder(Action.DELETE_PIN)
            .setId(expectedPinProfileId);

        sendEdgePinService(deletePinRequestPayload);

        verifyActivityMessages(ACTIVITY_STEP_ID);

        final var verificationContext = VerificationContext.fromPayload(
            deletePinRequestPayload.setTunnelProfileId(expectedTunnelProfileId)
                .setConnectResource(ChangedConnectResource.newBuilder()
                    .setConnectResourceId(expectedEdgeConnectResourceId)));

        verifyRepositoryData(verificationContext);
        verifyDdccmCfgRequestMessages(verificationContext);
        verifyCmnCfgCollectorMessages(verificationContext);
      }
    }

    private void verifyRepositoryData(VerificationContext context) {
      assertThat(repositoryUtil.find(PinProfile.class, context.pinProfileId())).isNull();
    }

    private void verifyDdccmCfgRequestMessages(VerificationContext context) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      messageCaptors.getDdccmMessageCaptor().assertNotSent(tenantId, requestId);
    }

    private void verifyCmnCfgCollectorMessages(VerificationContext context) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty()
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getIndex()).isEqualTo(Index.TUNNEL_PROFILE_INDEX_NAME);
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getId()).isEqualTo(context.tunnelProfileId());
            assertThat(op.getDocMap())
                .containsEntry(Key.ID, ValueUtils.stringValue(context.tunnelProfileId()))
                .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
                .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(TunnelNetworkSegmentTypeEnum.VXLAN.name()))
                .hasEntrySatisfying(Key.NETWORK_SEGMENTATION_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isEmpty())
                .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isEmpty())
                .hasEntrySatisfying(Key.NETWORK_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isEmpty());
          });
    }
  }

  @Nested
  class ConsumeActivateNetworkActionTest {

    private static final String ACTIVITY_STEP_ID = "Activate network in Wi-Fi service";

    @Nested
    class GivenNetworkAndPinProfilePersistedInDb {

      private String expectedVenueId;
      private String expectedNetworkId;

      private final String expectedPinProfileId = randomId();
      private final String expectedTunnelProfileId = randomId();
      private final String expectedEdgeConnectResourceId = randomId();

      @BeforeEach
      void givenVenueAndNetworkPersistedInDb(Venue savedVenue, Network savedNetwork,
          NetworkVenue savedNetworkVenue) {
        expectedVenueId = savedVenue.getId();
        expectedNetworkId = savedNetwork.getId();
      }

      @BeforeEach
      void givenPinProfilePersistedInDb() {
        final var tunnelProfile = repositoryUtil.createOrUpdate(
            Generators.tunnelProfile().setId(always(expectedTunnelProfileId))
                .setName(always(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
                .setType(always(TunnelNetworkSegmentTypeEnum.VXLAN)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        final var pinProfile = repositoryUtil.createOrUpdate(
            Generators.pinProfile().setId(always(expectedPinProfileId))
                .setPinProfileRegularSettings(
                    Generators.pinProfileRegularSetting().toListGenerator(0)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        repositoryUtil.createOrUpdate(
            Generators.pinProfileRegularSetting().setId(nullValue(String.class))
                .setTunnelProfile(always(tunnelProfile))
                .setPinProfile(always(pinProfile)).setPinProfileNetworkMappings(
                    Generators.pinProfileNetworkMapping().toListGenerator(0)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        repositoryUtil.createOrUpdate(
            Generators.edgeConnectResource().setId(always(expectedEdgeConnectResourceId))
                .setPinProfile(always(pinProfile)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
      }

      @Test
      void whenConsumeActivateNetworkActionRequest() {
        final var activateNetworkRequestPayload = newEdgePinServiceBuilder(Action.ACTIVATE_NETWORK)
            .setId(expectedPinProfileId)
            .addTunneledWlans(TunneledWlan.newBuilder()
                .setVenueId(expectedVenueId).setNetworkId(expectedNetworkId));

        sendEdgePinService(activateNetworkRequestPayload);

        verifyActivityMessages(ACTIVITY_STEP_ID);

        final var verificationContext = VerificationContext.fromPayload(
            activateNetworkRequestPayload.setTunnelProfileId(expectedTunnelProfileId)
                .setConnectResource(ChangedConnectResource.newBuilder()
                    .setConnectResourceId(expectedEdgeConnectResourceId)));

        verifyRepositoryData(verificationContext);
        verifyDdccmCfgRequestMessages(verificationContext);
        verifyCmnCfgCollectorMessages(verificationContext);
      }
    }

    private void verifyRepositoryData(VerificationContext context) {
      assertThat(repositoryUtil.find(PinProfile.class, context.pinProfileId())).isNotNull()
          .satisfies(pinProfile -> {
            assertThat(pinProfile.getId()).isEqualTo(context.pinProfileId());
            assertThat(pinProfile.getEdgeConnectResource()).isNotNull()
                .satisfies(edgeConnectResource -> {
                  assertThat(edgeConnectResource.getId())
                      .isEqualTo(context.edgeConnectResourceId());
                  assertThat(edgeConnectResource.getPinProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.pinProfileId());
                  assertThat(edgeConnectResource.getSdLanProfile()).isNull();
                });
            assertThat(pinProfile.getPinProfileRegularSettings()).isNotEmpty()
                .singleElement()
                .satisfies(pinProfileRegularSetting -> {
                  assertThat(pinProfileRegularSetting.getPinProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.pinProfileId());
                  assertThat(pinProfileRegularSetting.getTunnelProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.tunnelProfileId());
                  assertThat(pinProfileRegularSetting.getVenue()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.venueId());
                  assertThat(pinProfileRegularSetting.getPinProfileNetworkMappings()).isNotEmpty()
                      .singleElement()
                      .extracting(PinProfileNetworkMapping::getNetwork).isNotNull()
                      .extracting(BaseEntity::getId).isEqualTo(context.networkId());
                });
          });
    }

    private void verifyDdccmCfgRequestMessages(VerificationContext context) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class))
          .isNotEmpty()
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(MODIFY);
            assertThat(op.getConfigCase()).isEqualTo(ConfigCase.WLANVENUE);
            assertThat(op.hasWlanVenue()).isTrue();
            assertThat(op.getWlanVenue())
                .satisfies(wlanVenue -> {
                  assertThat(wlanVenue.getVenueId()).isEqualTo(context.venueId());
                  assertThat(wlanVenue.getWlanId()).isEqualTo(context.networkId());
                  assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(context.tunnelProfileId());
                  assertThat(wlanVenue.getDpTunnelDhcpEnabled()).isTrue();
                  assertThat(wlanVenue.getDpTunnelNatEnabled()).isTrue();
                  assertThat(wlanVenue.getIpForward()).isEqualTo(StringValue.of("LBO"));
                  assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
                  assertThat(wlanVenue.getIsNotifiedEdge()).isTrue();
                  assertThat(wlanVenue.getEdgeConnectResourceId())
                      .isEqualTo(StringValue.of(context.edgeConnectResourceId));
                });
          });
    }

    private void verifyCmnCfgCollectorMessages(VerificationContext context) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty()
          .satisfies(ops -> {
            assertThat(ops).hasSize(1);
          })
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getIndex()).isEqualTo(Index.TUNNEL_PROFILE_INDEX_NAME);
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getId()).isEqualTo(context.tunnelProfileId());
            assertThat(op.getDocMap())
                .containsEntry(Key.ID, ValueUtils.stringValue(context.tunnelProfileId()))
                .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
                .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(TunnelNetworkSegmentTypeEnum.VXLAN.name()))
                .hasEntrySatisfying(Key.NETWORK_SEGMENTATION_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isNotEmpty()
                        .singleElement()
                        .extracting(Value::getStringValue).isEqualTo(context.pinProfileId))
                .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isEmpty())
                .hasEntrySatisfying(Key.NETWORK_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isNotEmpty()
                        .singleElement()
                        .extracting(Value::getStringValue).isEqualTo(context.networkId));
          });
    }
  }

  @Nested
  class ConsumeDeactivateNetworkActionTest {

    private static final String ACTIVITY_STEP_ID = "Deactivate network in Wi-Fi service";

    @Nested
    class GivenNetworkAndPinProfileNetworkMappingPersistedInDb {

      private String expectedVenueId;
      private String expectedNetworkId;

      private final String expectedPinProfileId = randomId();
      private final String expectedTunnelProfileId = randomId();
      private final String expectedEdgeConnectResourceId = randomId();

      @BeforeEach
      void givenPinProfileNetworkMappingPersistedInDb(Venue savedVenue, Network savedNetwork,
          NetworkVenue savedNetworkVenue) {
        expectedVenueId = savedVenue.getId();
        expectedNetworkId = savedNetwork.getId();

        final var tunnelProfile = repositoryUtil.createOrUpdate(
            Generators.tunnelProfile().setId(always(expectedTunnelProfileId))
                .setName(always(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
                .setType(always(TunnelNetworkSegmentTypeEnum.VXLAN)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        final var pinProfile = repositoryUtil.createOrUpdate(
            Generators.pinProfile().setId(always(expectedPinProfileId))
                .setPinProfileRegularSettings(
                    Generators.pinProfileRegularSetting().toListGenerator(0)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        final var pinProfileRegularSetting = repositoryUtil.createOrUpdate(
            Generators.pinProfileRegularSetting().setId(nullValue(String.class))
                .setVenue(always(savedVenue))
                .setTunnelProfile(always(tunnelProfile))
                .setPinProfile(always(pinProfile)).setPinProfileNetworkMappings(
                    Generators.pinProfileNetworkMapping().toListGenerator(0)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        repositoryUtil.createOrUpdate(
            Generators.pinProfileNetworkMapping().setId(nullValue(String.class))
                .setPinProfileRegularSetting(always(pinProfileRegularSetting))
                .setNetwork(always(savedNetwork)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
        repositoryUtil.createOrUpdate(
            Generators.edgeConnectResource().setId(always(expectedEdgeConnectResourceId))
                .setPinProfile(always(pinProfile)).generate(),
            txCtxExtension.getTenantId(), randomTxId());
      }

      @Test
      void whenConsumeDeactivateNetworkActionRequest() {
        final var deactivateNetworkRequestPayload = newEdgePinServiceBuilder(Action.DEACTIVATE_NETWORK)
            .setId(expectedPinProfileId)
            .addTunneledWlans(TunneledWlan.newBuilder()
                .setVenueId(expectedVenueId).setNetworkId(expectedNetworkId));

        sendEdgePinService(deactivateNetworkRequestPayload);

        verifyActivityMessages(ACTIVITY_STEP_ID);

        final var verificationContext = VerificationContext.fromPayload(
            deactivateNetworkRequestPayload.setTunnelProfileId(expectedTunnelProfileId)
                .setConnectResource(ChangedConnectResource.newBuilder()
                    .setConnectResourceId(expectedEdgeConnectResourceId)));

        verifyRepositoryData(verificationContext);
        verifyDdccmCfgRequestMessages(verificationContext);
        verifyCmnCfgCollectorMessages(verificationContext);
      }
    }

    private void verifyRepositoryData(VerificationContext context) {
      assertThat(repositoryUtil.find(PinProfile.class, context.pinProfileId())).isNotNull()
          .satisfies(pinProfile -> {
            assertThat(pinProfile.getId()).isEqualTo(context.pinProfileId());
            assertThat(pinProfile.getEdgeConnectResource()).isNotNull()
                .satisfies(edgeConnectResource -> {
                  assertThat(edgeConnectResource.getId())
                      .isEqualTo(context.edgeConnectResourceId());
                  assertThat(edgeConnectResource.getPinProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.pinProfileId());
                  assertThat(edgeConnectResource.getSdLanProfile()).isNull();
                });
            assertThat(pinProfile.getPinProfileRegularSettings()).isNotEmpty()
                .singleElement()
                .satisfies(pinProfileRegularSetting -> {
                  assertThat(pinProfileRegularSetting.getPinProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.pinProfileId());
                  assertThat(pinProfileRegularSetting.getTunnelProfile()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.tunnelProfileId());
                  assertThat(pinProfileRegularSetting.getVenue()).isNotNull()
                      .extracting(Identifiable::getId).isEqualTo(context.venueId());
                  assertThat(pinProfileRegularSetting.getPinProfileNetworkMappings()).isEmpty();
                });
          });
    }

    private void verifyDdccmCfgRequestMessages(VerificationContext context) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class))
          .isNotEmpty()
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(MODIFY);
            assertThat(op.getConfigCase()).isEqualTo(ConfigCase.WLANVENUE);
            assertThat(op.hasWlanVenue()).isTrue();
            assertThat(op.getWlanVenue())
                .satisfies(wlanVenue -> {
                  assertThat(wlanVenue.getVenueId()).isEqualTo(context.venueId());
                  assertThat(wlanVenue.getWlanId()).isEqualTo(context.networkId());
                  assertThat(wlanVenue.getVxlanTunnelProfileId()).isNullOrEmpty();
                  assertThat(wlanVenue.getDpTunnelDhcpEnabled()).isFalse();
                  assertThat(wlanVenue.getDpTunnelNatEnabled()).isFalse();
                  assertThat(wlanVenue.getIpForward()).isEqualTo(StringValue.of("LBOAP"));
                  assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
                  assertThat(wlanVenue.getIsNotifiedEdge()).isTrue();
                  assertThat(wlanVenue.hasEdgeConnectResourceId()).isFalse();
                });
          });
    }

    private void verifyCmnCfgCollectorMessages(VerificationContext context) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty()
          .satisfies(ops -> {
            assertThat(ops).hasSize(1);
          })
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getIndex()).isEqualTo(Index.TUNNEL_PROFILE_INDEX_NAME);
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getId()).isEqualTo(context.tunnelProfileId());
            assertThat(op.getDocMap())
                .containsEntry(Key.ID, ValueUtils.stringValue(context.tunnelProfileId()))
                .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_VXLAN_TUNNEL_PROFILE_NAME))
                .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(TunnelNetworkSegmentTypeEnum.VXLAN.name()))
                .hasEntrySatisfying(Key.NETWORK_SEGMENTATION_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isNotEmpty()
                        .singleElement()
                        .extracting(Value::getStringValue).isEqualTo(context.pinProfileId))
                .hasEntrySatisfying(Key.CENTRALIZED_FORWARDING_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isEmpty())
                .hasEntrySatisfying(Key.NETWORK_IDS,
                    value -> assertThat(value.getListValue().getValuesList()).isEmpty());
          });
    }
  }

  private void verifyActivityMessages(String expectedStepId) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId)).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> {
          assertThat(msg.getStatus()).isEqualTo(Status.OK);
          assertThat(msg.getStep()).isEqualTo(expectedStepId);
          assertThat(msg.getEventDate()).isNotNull();
        });

    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(tenantId);
  }
}
