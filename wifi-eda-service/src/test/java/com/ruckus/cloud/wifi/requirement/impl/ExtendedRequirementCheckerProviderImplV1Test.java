package com.ruckus.cloud.wifi.requirement.impl;

import static org.apache.commons.lang3.RandomStringUtils.randomAlphanumeric;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.google.common.collect.Maps;
import com.ruckus.cloud.compatibility.contract.requirement.RequirementCheckerGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.requirement.feature.ApFeature;
import com.ruckus.cloud.wifi.requirement.feature.BssColoringFeature;
import com.ruckus.cloud.wifi.requirement.feature.QosMirroringFeature;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedWifiNetworkServiceCtrl;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.viewmodel.FeatureLevel;
import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.util.ReflectionUtils;

@WifiUnitTest
class ExtendedRequirementCheckerProviderImplV1Test {

  private static final ApFeature bssColoringFeature = new BssColoringFeature();
  private static final ApFeature qosMirroringFeature = new QosMirroringFeature();

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @MockBean
  private ExtendedVenueServiceCtrl venueService;
  @MockBean
  private ExtendedWifiNetworkServiceCtrl networkService;
  @SpyBean
  private ExtendedRequirementCheckerProviderImplV1 unit;

  @BeforeEach
  void beforeEach() throws NoSuchFieldException {
    Field field = ExtendedRequirementCheckerProviderImplV1.class.getDeclaredField(
        "requirementCheckerMap");
    field.setAccessible(true);
    Map<String, ApFeature> featureMap = Maps.newHashMap();
    featureMap.put(bssColoringFeature.getFeatureName(), bssColoringFeature);
    featureMap.put(qosMirroringFeature.getFeatureName(), qosMirroringFeature);
    ReflectionUtils.setField(field, unit, featureMap);
  }

  @Nested
  class WhenFindRequirementChecker {

    @Test
    void givenRequirementCheckerNotPresent() {
      BDDAssertions.then(unit.findRequirementChecker(randomAlphanumeric(10))).isNotNull().isEmpty();
    }

    @Test
    void givenRequirementCheckerPresent() {
      BDDAssertions.then(unit.findRequirementChecker(qosMirroringFeature.getFeatureName()))
          .isNotNull();
    }
  }

  @Nested
  class WhenGetRequirementCheckerGroupsByVenueIds {

    @Test
    void thenReturnRequirementCheckerGroupsForEachVenue(Tenant tenant) {
      // only first 3 venues have Bss coloring enabled
      final var venues =
          Stream.generate(() -> VenueTestFixture.randomVenue(tenant)).limit(5).toList();
      final var venueIds = venues.stream().map(AbstractBaseEntity::getId).toList();
      IntStream.range(0, 2).forEach(i -> venues.get(i).getBssColoring().setBssColoringEnabled(true));
      when(venueService.findVenuesByVenueIds(venueIds)).thenReturn(venues);

      // only first 2 networks have Qos mirroring enabled
      final var networks =
          Stream.generate(() -> NetworkTestFixture.randomNetwork(tenant)).limit(3).toList();

      var venueNetworkMapping = Map.of(
          venues.get(2).getId(), List.of(networks.get(0).getId(), networks.get(1).getId()),
          venues.get(3).getId(), List.of(networks.get(1).getId()),
          venues.get(4).getId(), List.of(networks.get(1).getId(), networks.get(2).getId())
      );
      var venueNetworksMap = venues.stream()
          .collect(Collectors.toMap(
              Venue::getId,
              venue -> venueNetworkMapping.getOrDefault(venue.getId(), List.of())
          ));

      var result = unit.getRequirementCheckerGroupsByVenues(venueIds, venueNetworksMap,
          List.of(qosMirroringFeature.getFeatureName(), bssColoringFeature.getFeatureName()),
          List.of(FeatureLevel.WIFI_NETWORK, FeatureLevel.VENUE));
      BDDAssertions.then(result)
          .isNotNull()
          .hasSize(venues.size())
          .map(RequirementCheckerGroup::getId)
          .containsExactlyElementsOf(venueIds);
    }
  }

  @Nested
  class WhenGetRequirementCheckerGroupsByNetworkIds {

    @Test
    void thenReturnRequirementCheckerGroupsForEachNetwork(Tenant tenant) {
      // only first 2 venues have Bss coloring enabled
      final var venues =
          Stream.generate(() -> VenueTestFixture.randomVenue(tenant)).limit(3).toList();
      IntStream.range(0, 1).forEach(i -> venues.get(i).getBssColoring().setBssColoringEnabled(true));
      when(venueService.findVenuesByVenueIds(anyList())).thenReturn(venues);

      // only first 3 networks have Qos mirroring enabled
      final var networks =
          Stream.generate(() -> NetworkTestFixture.randomNetwork(tenant)).limit(5).toList();
      final var networkIds = networks.stream().map(AbstractBaseEntity::getId).toList();
      IntStream.range(0, 2).forEach(i -> networks.get(i).getWlan().getAdvancedCustomization().setQosMirroringEnabled(true));
      when(networkService.findNetworksByNetworkIds(anyList())).thenReturn(networks);

      var networkVenueMapping = Map.of(
          networks.get(2).getId(), List.of(venues.get(0).getId(), venues.get(1).getId()),
          networks.get(3).getId(), List.of(venues.get(1).getId()),
          networks.get(4).getId(), List.of(venues.get(1).getId(), venues.get(2).getId())
      );
      var networkVenuesMap = networks.stream()
          .collect(Collectors.toMap(
              Network::getId,
              network -> networkVenueMapping.getOrDefault(network.getId(), List.of())
          ));

      var result = unit.getRequirementCheckerGroupsByNetworks(networkIds, networkVenuesMap,
          List.of(qosMirroringFeature.getFeatureName(), bssColoringFeature.getFeatureName()),
          List.of(FeatureLevel.WIFI_NETWORK, FeatureLevel.VENUE));
      BDDAssertions.then(result)
          .isNotNull()
          .hasSize(networks.size())
          .map(RequirementCheckerGroup::getId)
          .containsExactlyElementsOf(networkIds);
    }
  }

  @Nested
  class WhenGetRequirementCheckerGroupsByApIds {

    @Test
    void thenReturnRequirementCheckersForEachAps(Tenant tenant) throws Exception {
      // Mock or create a random venue for testing
      final var venue = VenueTestFixture.randomVenue(tenant);
      final var venueId = venue.getId();
      when(venueService.getVenue(venueId)).thenReturn(venue);
      venue.getBssColoring().setBssColoringEnabled(true);
      final var networks = Stream.generate(() -> NetworkTestFixture.randomNetwork(tenant))
          .limit(3) // Assuming we need at least 3 networks for the test
          .toList();
      when(networkService.findNetworksByNetworkIds(anyList())).thenReturn(networks);

      // only first 2 networks have Qos mirroring enabled
      IntStream.range(0, 2).forEach(
          i -> networks.get(i).getWlan().getAdvancedCustomization().setQosMirroringEnabled(true));
      var venueNetworkMapping = Map.of(
          venue.getId(), List.of(networks.get(0).getId(), networks.get(1).getId())
      );
      var venueNetworksMap = Map.of(
          venueId, venueNetworkMapping.getOrDefault(venueId, List.of())
      );
      var featureNames = List.of(
          qosMirroringFeature.getFeatureName(), bssColoringFeature.getFeatureName()
      );
      var result = unit.getRequirementCheckersForAps(venueId, venueNetworksMap.get(venueId),
          featureNames, List.of(FeatureLevel.VENUE, FeatureLevel.WIFI_NETWORK));
      // Validate the result using assertions
      BDDAssertions.then(result)
          .isNotNull()
          .hasSize(2);
      // Assert that the feature names are correct
      BDDAssertions.then(result)
          .extracting(checker -> checker.getFeatureName()) // Extract feature names
          .containsExactlyInAnyOrder(bssColoringFeature.getFeatureName(), qosMirroringFeature.getFeatureName());
      // Flatten and validate wifiNetworkIds
      // Get the expected wifi network IDs from the venue network mapping
      var expectedWifiNetworkIds = venueNetworkMapping.get(venueId);
      var actualWifiNetworkIds = result.stream()
          .flatMap(checker -> checker.getRequirement().getWifiNetworkIds().stream())
          .collect(Collectors.toSet()); // Collect to a Set to eliminate duplicates
      // Assert that the size matches the expected size
      BDDAssertions.then(actualWifiNetworkIds)
          .hasSize(expectedWifiNetworkIds.size()); // Check if the counts match
      // Convert expected to a Set as well to ensure unique comparison
      var expectedSet = new HashSet<>(expectedWifiNetworkIds);
      // Validate that both sets contain the same unique elements
      BDDAssertions.then(actualWifiNetworkIds)
          .containsExactlyInAnyOrderElementsOf(expectedSet);
    }
  }
}
