package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.servicemodel.projection.AccessPolicyQueryProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import com.ruckus.cloud.wifi.test.fixture.DevicePolicyTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.test.context.jdbc.Sql;

@Tag("DevicePolicyTest")
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO device_policy (id, name, tenant, default_access)
        VALUES ('dp_1', 'devicePolicy1', '4c8279f79307415fa9e4c88a1819f0fc', 'ALLOW');
    INSERT INTO device_policy (id, name, tenant, default_access, is_template)
        VALUES ('dp_1_template', 'policyTemplate1', '4c8279f79307415fa9e4c88a1819f0fc', 'ALLOW', true);
    INSERT INTO device_policy (id, name, tenant, default_access, is_template)
        VALUES ('dp_1_not_template', 'policy2', '4c8279f79307415fa9e4c88a1819f0fc', 'ALLOW', false);
    """)
class DevicePolicyRepositoryTest {

  private static final String TENANT_ID ="4c8279f79307415fa9e4c88a1819f0fc";

  @Autowired
  private DevicePolicyRepository repository;
  @Autowired
  private RepositoryUtil repositoryUtil;

  DevicePolicy dp1;
  DevicePolicy dp2;
  DevicePolicy dp3;


  @BeforeEach
  void setUp(final Tenant tenant) {
    DevicePolicy dp1 = DevicePolicyTestFixture.randomDevicePolicy();
    DevicePolicy dp2 = DevicePolicyTestFixture.randomDevicePolicy();
    DevicePolicy dp3 = DevicePolicyTestFixture.randomDevicePolicy();

    dp1.setName("name1");
    dp1.setTenant(tenant);
    dp1.setRules(Generators.devicePolicyRule(dp1)
        .setOsVendor(always(OsVendorEnum.Xbox360))
        .generate(1));
    dp2.setName("name2");
    dp2.setTenant(tenant);
    dp2.setRules(Generators.devicePolicyRule(dp2)
        .setOsVendor(always(OsVendorEnum.PlayStation2))
        .generate(1));
    dp3.setName("name3");
    Tenant tenant2 = new Tenant(randomId());
    dp3.setTenant(tenant2);
    dp3.setRules(Generators.devicePolicyRule(dp3)
        .setOsVendor(always(OsVendorEnum.PlayStation3))
        .generate(1));

    dp1 = repositoryUtil.createOrUpdate(dp1, tenant.getId(), randomTxId());
    dp2 = repositoryUtil.createOrUpdate(dp2, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(tenant2, tenant2.getId(), randomTxId());
    dp3 = repositoryUtil.createOrUpdate(dp3, tenant2.getId(), randomTxId());

    this.dp1 = dp1;
    this.dp2 = dp2;
    this.dp3 = dp3;
  }

  @AfterEach
  void teardown(final Tenant tenant) {
    repositoryUtil.remove(dp1, tenant.getId(),true);
    repositoryUtil.remove(dp2, tenant.getId(),true);
    repositoryUtil.remove(dp3, dp3.getTenant().getId(), true);
  }

  @Test
  void testFindDevicePoliciesData_findByName(Tenant tenant) {
    Page<AccessPolicyQueryProjection> result = repository.findDevicePoliciesData(Pageable.unpaged(),
        "%name1%", tenant.getId());
    assertEquals(1, result.getTotalElements());
    assertThat(result.getContent().get(0)).satisfies(actual -> {
          assertEquals(dp1.getId(), actual.getId());
          assertEquals(dp1.getName(), actual.getName());
          assertEquals(dp1.getDescription(), actual.getDescription());
          assertEquals(dp1.getRules().size(), actual.getRulesCount());
        }
    );
  }

  @Test
  void testFindDevicePoliciesData_nameNotFound(Tenant tenant) {
    Page<AccessPolicyQueryProjection> result = repository.findDevicePoliciesData(Pageable.unpaged(),
        "%name3%", tenant.getId());
    assertThat(result.getContent()).isEmpty();
  }

  @Test
  void testFindDevicePoliciesData_sortByName(Tenant tenant) {
    Page<AccessPolicyQueryProjection> result = repository.findDevicePoliciesData(
        PageRequest.of(0, 10, Sort.by(Direction.DESC, "name")), "%%", tenant.getId());
    assertThat(result.getContent()).hasSize(2).extracting("name")
        .containsSequence(List.of("name2", "name1"));
  }

  @Test
  void testFindByRuleContainsAnyOfOsVendor() {
    final var resultXbox360 = repository.findByRuleContainsAnyOfOsVendor(
        Pageable.unpaged(), OsVendorEnum.Xbox360);
    assertThat(resultXbox360.getContent()).isNotEmpty()
        .singleElement().satisfies(dp -> assertThat(dp.getId()).isEqualTo(dp1.getId()));
    final var resultPlayStation2 = repository.findByRuleContainsAnyOfOsVendor(
        Pageable.unpaged(), OsVendorEnum.PlayStation2);
    assertThat(resultPlayStation2.getContent()).isNotEmpty()
        .singleElement().satisfies(dp -> assertThat(dp.getId()).isEqualTo(dp2.getId()));
    final var resultPlayStation3 = repository.findByRuleContainsAnyOfOsVendor(
        Pageable.unpaged(), OsVendorEnum.PlayStation3);
    assertThat(resultPlayStation3.getContent()).isNotEmpty()
        .singleElement().satisfies(dp -> assertThat(dp.getId()).isEqualTo(dp3.getId()));

    final var resultAll = repository.findByRuleContainsAnyOfOsVendor(
        Pageable.unpaged(), OsVendorEnum.Xbox360, OsVendorEnum.PlayStation2, OsVendorEnum.PlayStation3);
    assertThat(resultAll.getContent()).isNotEmpty().hasSize(3)
        .extracting(DevicePolicy::getId)
        .containsExactlyInAnyOrder(dp1.getId(), dp2.getId(), dp3.getId());
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantId() {
    var result = repository.findByIdAndTenantId("dp_1_template", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("dp_1_template", result.get().getId());
    assertEquals(true, result.get().getIsTemplate());
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantIdWithIsNotTemplateId() {
    var result = repository.findByIdAndTenantId("dp_1_not_template", TENANT_ID);

    assertTrue(result.isEmpty());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplate() {
    var result = repository.findByIdAndTenantId("dp_1_not_template", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("dp_1_not_template", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplateWithLegacyData() {
    var result = repository.findByIdAndTenantId("dp_1", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("dp_1", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }
}
