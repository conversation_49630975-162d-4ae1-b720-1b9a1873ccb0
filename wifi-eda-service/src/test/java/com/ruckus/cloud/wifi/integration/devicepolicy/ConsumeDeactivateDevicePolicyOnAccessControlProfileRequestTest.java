package com.ruckus.cloud.wifi.integration.devicepolicy;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeDeactivateDevicePolicyOnAccessControlProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeDeactivateDevicePolicyOnAccessControlProfileMessage {

    private String accessControlProfileId;
    private String devicePolicyId;

    @BeforeEach
    void beforeEach(AccessControlProfile accessControlProfile, DevicePolicy devicePolicy) {
      accessControlProfileId = accessControlProfile.getId();
      devicePolicyId = devicePolicy.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("accessControlProfileId", accessControlProfileId)
          .addPathVariable("devicePolicyId", devicePolicyId);
    }

    @Nested
    class GivenDevicePolicyExists {

      @Test
      void givenAccessControlProfileExists(Tenant tenant) {
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.DEACTIVATE_DEVICE_POLICY_ON_ACCESS_CONTROL_PROFILE,
            randomName(),
            requestParams(),
            "");

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(1)
            .first()
            .matches(o -> o.getOpType() == OpType.MOD)
            .matches(o -> o.getId().equals(accessControlProfileId))
            .extracting(
                Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
            .extractingByKey(Key.DEVICE_POLICY_ID)
            .isNotNull()
            .extracting(Value::getStringValue)
            .isEqualTo("");

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a ->
                    a.getStep()
                        .equals(ApiFlowNames.DEACTIVATE_DEVICE_POLICY_ON_ACCESS_CONTROL_PROFILE));
      }
    }
  }
}
