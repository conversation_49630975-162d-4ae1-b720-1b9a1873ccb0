package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.handler.asyncjob.UpgradeByModelDataMigrationAsyncJobHandler.UPGRADE_BY_MODEL_DATA_MIGRATION_JOB_NAME;
import static com.ruckus.cloud.wifi.proto.UpgradeByModelDataMigrationJob.PayloadCase.MIGRATE_BY_VENUE_PAYLOAD;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.client.kairos.KairosApiClient;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.kafka.context.WifiAsyncJobContext;
import com.ruckus.cloud.wifi.kafka.publisher.WifiAsyncJobPublisher;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.repository.DefaultApBranchFamilyRepository;
import com.ruckus.cloud.wifi.repository.ScheduleTimeSlotRepository;
import com.ruckus.cloud.wifi.repository.TenantFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.TenantRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.repository.VenueCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.repository.VenueFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.ApVersionService;
import com.ruckus.cloud.wifi.service.UpgradeByModelDataMigrationService;
import com.ruckus.cloud.wifi.service.VenueFirmwareVersionService;
import com.ruckus.cloud.wifi.service.impl.ApBranchFamilyServiceRouter;
import com.ruckus.cloud.wifi.service.impl.ApVersionServiceImpl;
import com.ruckus.cloud.wifi.service.impl.UpgradeByModelDataMigrationServiceImpl;
import com.ruckus.cloud.wifi.service.impl.VenueFirmwareVersionServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.ApBranchFamilyServiceRouterTestConfig;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.ScheduleTimeSlotTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleFirmwareVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.viewmodel.UpgradeByApModelDataMigrationType;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
public class UpgradeByModelDataMigrationServiceTest extends AbstractServiceTest {

  @Autowired
  private UpgradeByModelDataMigrationService upgradeByModelDataMigrationService;

  @SpyBean
  private VenueCurrentFirmwareRepository venueCurrentFirmwareRepository;

  @Autowired
  private ScheduleTimeSlotRepository scheduleTimeSlotRepository;

  @SpyBean
  private UpgradeScheduleRepository upgradeScheduleRepository;

  @Autowired
  private UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository;

  @MockBean
  private WifiAsyncJobPublisher wifiAsyncJobPublisher;

  @MockBean
  private KairosApiClient kairosApiClient;

  @SpyBean
  private TenantRepository tenantRepository;

  @SpyBean
  private VenueRepository venueRepository;

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  public void testMigrate(Tenant tenant) {
    upgradeByModelDataMigrationService.migrate(UpgradeByApModelDataMigrationType.UPLOAD, List.of(tenant.getId()));
    verify(wifiAsyncJobPublisher, times(1))
        .publish(any(WifiAsyncJobContext.class), any(WifiAsyncJob.class));
  }

  @Test
  public void testMigrate_whenMigrationTypeIsAll_migrateAllTenants(Tenant tenant) {
    Tenant tenantA = TenantTestFixture.randomTenant(c -> {});
    repositoryUtil.createOrUpdate(tenantA, tenant.getId(), UUID.randomUUID().toString());
    Tenant tenantB = TenantTestFixture.randomTenant(c -> {});
    repositoryUtil.createOrUpdate(tenantB, tenant.getId(), UUID.randomUUID().toString());
    doReturn(List.of(tenantA.getId(), tenantB.getId())).when(tenantRepository).findAllIdsOrderByCreatedDate();

    upgradeByModelDataMigrationService.migrate(UpgradeByApModelDataMigrationType.ALL, List.of());
    verify(wifiAsyncJobPublisher, times(2))
        .publish(any(WifiAsyncJobContext.class), any(WifiAsyncJob.class));
  }

  @Test
  public void testMigrate_whenMigrationTypeIsNonSync(Tenant tenant) {
    doReturn(List.of("tenantA", "tenantB")).when(venueCurrentFirmwareRepository)
        .getTenantIdsWithNonSyncedVenueCurrentVersion();
    doReturn(List.of("tenantB", "tenantC")).when(upgradeScheduleRepository)
        .findTenantIdsByStatusAndTargetApModelsIsNull(any());
    upgradeByModelDataMigrationService.migrate(UpgradeByApModelDataMigrationType.NON_SYNC, List.of(tenant.getId()));
    verify(wifiAsyncJobPublisher, times(3))
        .publish(any(WifiAsyncJobContext.class), any(WifiAsyncJob.class));
  }

  @Test
  public void testMigrateSingleTenant(Tenant tenant) {
    doReturn(List.of("venueA", "venueB", "venueC")).when(venueRepository)
        .findIdsByTenantId(tenant.getId());

    upgradeByModelDataMigrationService.migrateSingleTenant(tenant.getId());

    ArgumentCaptor<WifiAsyncJobContext> jobCtxCaptor = ArgumentCaptor.forClass(WifiAsyncJobContext.class);
    ArgumentCaptor<WifiAsyncJob> msgCaptor = ArgumentCaptor.forClass(WifiAsyncJob.class);
    verify(wifiAsyncJobPublisher, times(1)).publish(jobCtxCaptor.capture(), msgCaptor.capture());
    assertThat(jobCtxCaptor.getValue())
        .isNotNull()
        .satisfies(jobCtx -> {
          assertEquals(tenant.getId(), jobCtx.getTenantId());
          assertEquals(UPGRADE_BY_MODEL_DATA_MIGRATION_JOB_NAME, jobCtx.getJobName());
        });

    assertThat(msgCaptor.getValue())
        .isNotNull()
        .extracting(WifiAsyncJob::getUpgradeByModelDataMigrationJob)
        .isNotNull()
        .satisfies(job -> {
          assertEquals(MIGRATE_BY_VENUE_PAYLOAD, job.getPayloadCase());
          assertThat(job)
              .extracting(j -> j.getMigrateByVenuePayload().getVenueIdList()).asList()
              .containsExactlyInAnyOrder("venueA", "venueB", "venueC");
        });
  }

  @Test
  public void testMigrateVenues(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version623,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.234") ApVersion version700) {
    updateVersionSupportedModels(version620, List.of("R500", "R550"));
    updateVersionSupportedModels(version623, List.of("R550"));
    updateVersionSupportedModels(version700, List.of("R550", "R770"));
    Venue venueA = this.createVenue(tenant, "venueA", version700);
    Venue venueB = this.createVenue(tenant, "venueB", version700);
    createVenueFirmwareVersion(venueA, "eol-ap-2022-12", version620);
    createVenueFirmwareVersion(venueA, "ABF2-3R", version623);
    createVenueFirmwareVersion(venueB, "eol-ap-2022-12", version620);
    createVenueFirmwareVersion(venueB, "ABF2-3R", version623);
    ApGroup apGroupA = this.createApGroup(venueA, "apGroupA");
    ApGroup apGroupB = this.createApGroup(venueB, "apGroupB");
    this.createAp(apGroupA, randomSerialNumber(), "R500", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R550", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R550", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R770", randomMacAddress());
    this.createAp(apGroupB, randomSerialNumber(), "R500", randomMacAddress());
    UpgradeSchedule scheduleForA = createSchedule(venueA, List.of(version623, version700));
    UpgradeSchedule scheduleForB = createSchedule(venueB, List.of(version700));

    upgradeByModelDataMigrationService.migrateVenues(tenant.getId(), List.of(venueA.getId(), venueB.getId()));

    validateVenueCurrentFirmware(venueA, "R500", version620);
    validateVenueCurrentFirmware(venueA, "R550", version700);
    validateVenueCurrentFirmware(venueA, "R770", version700);
    validateVenueCurrentFirmware(venueB, "R500", version620);
    assertThat(upgradeScheduleRepository.findAllByVenueId(venueA.getId()))
        .hasSize(1)
        .singleElement()
        .extracting(UpgradeSchedule::getUpgradeScheduleFirmwareVersions)
        .asList()
        .extracting(UpgradeScheduleFirmwareVersion.class::cast)
        .hasSize(1)
        .singleElement()
        .satisfies(usfv -> {
          assertThat(usfv.getTargetApModels())
              .containsExactlyInAnyOrder("R550", "R770");
          assertThat(usfv.getApFirmwareVersion())
              .isEqualTo(version700);
        });
    assertThat(upgradeScheduleRepository.findAllByVenueId(venueB.getId())).isEmpty();
    verify(kairosApiClient, times(1)).deleteScheduleJobs(
        argThat(list -> list.equals(List.of(scheduleForB.getId()))));
  }

  @Test
  public void testMigrateVenues_whenApWithoutMac(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version623,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.234") ApVersion version700) {
    updateVersionSupportedModels(version620, List.of("R500", "R550"));
    updateVersionSupportedModels(version623, List.of("R550"));
    updateVersionSupportedModels(version700, List.of("R550", "R770"));
    Venue venueA = this.createVenue(tenant, "venueA", version623);
    Venue venueB = this.createVenue(tenant, "venueB", version623);
    createVenueFirmwareVersion(venueA, "eol-ap-2022-12", version620);
    createVenueFirmwareVersion(venueA, "ABF2-3R", version623);
    createVenueFirmwareVersion(venueB, "eol-ap-2022-12", version620);
    createVenueFirmwareVersion(venueB, "ABF2-3R", version623);
    ApGroup apGroupA = this.createApGroup(venueA, "apGroupA");
    ApGroup apGroupB = this.createApGroup(venueB, "apGroupB");
    this.createAp(apGroupA, randomSerialNumber(), "R500", null);
    this.createAp(apGroupA, randomSerialNumber(), "R550", null);
    this.createAp(apGroupA, randomSerialNumber(), "R770", null);
    this.createAp(apGroupB, randomSerialNumber(), "R500", null);
    UpgradeSchedule scheduleForA = createSchedule(venueA, List.of(version623, version700));
    UpgradeSchedule scheduleForB = createSchedule(venueB, List.of(version700));

    upgradeByModelDataMigrationService.migrateVenues(tenant.getId(), List.of(venueA.getId(), venueB.getId()));

    assertThat(venueCurrentFirmwareRepository.findByTenantId(tenant.getId())).isEmpty();
    assertThat(upgradeScheduleRepository.findAllByVenueId(venueA.getId())).isEmpty();
    assertThat(upgradeScheduleRepository.findAllByVenueId(venueB.getId())).isEmpty();
    verify(kairosApiClient, times(1)).deleteScheduleJobs(
        argThat(list -> list.containsAll(List.of(scheduleForA.getId(), scheduleForB.getId()))));
  }

  @Test
  public void testMigrateVenues_whenVenueHasVenueCurrentFirmwares(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version621,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version623,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.234") ApVersion version700) {
    updateVersionSupportedModels(version620, List.of("R500", "R550"));
    updateVersionSupportedModels(version621, List.of("R550"));
    updateVersionSupportedModels(version623, List.of("R550"));
    updateVersionSupportedModels(version700, List.of("R550", "R770"));
    Venue venueA = this.createVenue(tenant, "venueA", version700);
    createVenueFirmwareVersion(venueA, "eol-ap-2022-12", version620);
    createVenueFirmwareVersion(venueA, "ABF2-3R", version623);
    ApGroup apGroupA = this.createApGroup(venueA, "apGroupA");
    this.createAp(apGroupA, randomSerialNumber(), "R500", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R500", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R550", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R770", randomMacAddress());
    createVenueCurrentFirmware(venueA, "R500", version620);
    createVenueCurrentFirmware(venueA, "R550", version621);
    createVenueCurrentFirmware(venueA, "R770", version621);

    upgradeByModelDataMigrationService.migrateVenues(tenant.getId(), List.of(venueA.getId()));

    validateVenueCurrentFirmware(venueA, "R500", version620);
    validateVenueCurrentFirmware(venueA, "R550", version700);
    validateVenueCurrentFirmware(venueA, "R770", version700);
  }

  @Test
  public void testMigrateVenues_whenVenueHasTwoSchedules(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version623,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.234") ApVersion version700) {
    updateVersionSupportedModels(version620, List.of("R500", "R550"));
    updateVersionSupportedModels(version623, List.of("R550"));
    updateVersionSupportedModels(version700, List.of("R550", "R770"));
    Venue venueA = this.createVenue(tenant, "venueA", version700);
    createVenueFirmwareVersion(venueA, "eol-ap-2022-12", version620);
    createVenueFirmwareVersion(venueA, "ABF2-3R", version623);
    ApGroup apGroupA = this.createApGroup(venueA, "apGroupA");
    this.createAp(apGroupA, randomSerialNumber(), "R500", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R550", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R550", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R770", randomMacAddress());
    createSchedule(venueA, List.of(version623, version700), UpgradeScheduleStatus.PENDING);
    createSchedule(venueA, List.of(version623, version700), UpgradeScheduleStatus.RUNNING);

    upgradeByModelDataMigrationService.migrateVenues(tenant.getId(), List.of(venueA.getId()));

    validateVenueCurrentFirmware(venueA, "R500", version620);
    validateVenueCurrentFirmware(venueA, "R550", version700);
    validateVenueCurrentFirmware(venueA, "R770", version700);
    assertThat(upgradeScheduleRepository.findAllByVenueId(venueA.getId())).hasSize(2);
    verify(kairosApiClient, times(0)).deleteScheduleJobs(anyList());
  }

  @Test
  public void testMigrateVenues_whenVenueIn620AndHasOneScheduleWith620And700TargetVersions(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version623,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.234") ApVersion version700) {
    updateVersionSupportedModels(version620, List.of("R500", "R550", "T310"));
    updateVersionSupportedModels(version623, List.of("T310", "R550", "R560"));
    updateVersionSupportedModels(version700, List.of("R550", "R770"));
    Venue venueA = this.createVenue(tenant, "venueA", version620);
    createVenueFirmwareVersion(venueA, "eol-ap-2022-12", version620);
    createVenueFirmwareVersion(venueA, "ABF2-3R", version623);
    ApGroup apGroupA = this.createApGroup(venueA, "apGroupA");
    this.createAp(apGroupA, randomSerialNumber(), "T310", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R500", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R550", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R560", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R770", randomMacAddress());
    createSchedule(venueA, List.of(version620, version700));

    upgradeByModelDataMigrationService.migrateVenues(tenant.getId(), List.of(venueA.getId()));

    assertThat(upgradeScheduleRepository.findAllByVenueId(venueA.getId()))
        .hasSize(1)
        .singleElement()
        .extracting(UpgradeSchedule::getUpgradeScheduleFirmwareVersions)
        .asList()
        .extracting(UpgradeScheduleFirmwareVersion.class::cast)
        .hasSize(2)
        .satisfies(upgradeScheduleFirmwareVersions -> {
          assertThat(upgradeScheduleFirmwareVersions)
              .filteredOn(usfv -> usfv.getApFirmwareVersion().equals(version620))
              .hasSize(1)
              .singleElement()
              .extracting(UpgradeScheduleFirmwareVersion::getTargetApModels)
              .asList().containsExactlyInAnyOrder("R500", "T310");
          assertThat(upgradeScheduleFirmwareVersions)
              .filteredOn(usfv -> usfv.getApFirmwareVersion().equals(version623))
              .isEmpty();
          assertThat(upgradeScheduleFirmwareVersions)
              .filteredOn(usfv -> usfv.getApFirmwareVersion().equals(version700))
              .hasSize(1)
              .singleElement()
              .extracting(UpgradeScheduleFirmwareVersion::getTargetApModels)
              .asList().containsExactlyInAnyOrder("R770", "R550");
        });
    verify(kairosApiClient, times(0)).deleteScheduleJobs(anyList());
  }

  @Test
  public void testMigrateVenues_whenVenueIn623AndHasOneScheduleWith620And700TargetVersions(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version623,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.234") ApVersion version700) {
    updateVersionSupportedModels(version620, List.of("R500", "R550", "T310"));
    updateVersionSupportedModels(version623, List.of("T310", "R550", "R560"));
    updateVersionSupportedModels(version700, List.of("R550", "R770"));
    Venue venueA = this.createVenue(tenant, "venueA", version623);
    createVenueFirmwareVersion(venueA, "eol-ap-2022-12", version620);
    createVenueFirmwareVersion(venueA, "ABF2-3R", version623);
    ApGroup apGroupA = this.createApGroup(venueA, "apGroupA");
    this.createAp(apGroupA, randomSerialNumber(), "T310", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R500", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R550", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R560", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R770", randomMacAddress());
    createSchedule(venueA, List.of(version620, version700));

    upgradeByModelDataMigrationService.migrateVenues(tenant.getId(), List.of(venueA.getId()));

    assertThat(upgradeScheduleRepository.findAllByVenueId(venueA.getId()))
        .hasSize(1)
        .singleElement()
        .extracting(UpgradeSchedule::getUpgradeScheduleFirmwareVersions)
        .asList()
        .extracting(UpgradeScheduleFirmwareVersion.class::cast)
        .hasSize(2)
        .satisfies(upgradeScheduleFirmwareVersions -> {
          assertThat(upgradeScheduleFirmwareVersions)
              .filteredOn(usfv -> usfv.getApFirmwareVersion().equals(version620))
              .hasSize(1)
              .singleElement()
              .extracting(UpgradeScheduleFirmwareVersion::getTargetApModels)
              .asList().containsExactlyInAnyOrder("R500");
          assertThat(upgradeScheduleFirmwareVersions)
              .filteredOn(usfv -> usfv.getApFirmwareVersion().equals(version623))
              .isEmpty();
          assertThat(upgradeScheduleFirmwareVersions)
              .filteredOn(usfv -> usfv.getApFirmwareVersion().equals(version700))
              .hasSize(1)
              .singleElement()
              .extracting(UpgradeScheduleFirmwareVersion::getTargetApModels)
              .asList().containsExactlyInAnyOrder("R770", "R550");
        });
    verify(kairosApiClient, times(0)).deleteScheduleJobs(anyList());
  }

  @Test
  public void testMigrateVenues_whenScheduleTargetModelListIsNotEmpty_updateTargetModelList(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.203") ApVersion version623,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.234") ApVersion version700) {
    updateVersionSupportedModels(version620, List.of("R550", "R560"));
    updateVersionSupportedModels(version623, List.of("R550", "R560"));
    updateVersionSupportedModels(version700, List.of("R550", "R560", "R770"));
    Venue venueA = this.createVenue(tenant, "venueA", version700);
    createVenueFirmwareVersion(venueA, "eol-ap-2022-12", version620);
    createVenueFirmwareVersion(venueA, "ABF2-3R", version623);
    ApGroup apGroupA = this.createApGroup(venueA, "apGroupA");
    this.createAp(apGroupA, randomSerialNumber(), "R550", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R550", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R560", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R770", randomMacAddress());
    createSchedule(venueA, List.of(version623, version700));
    updateScheduleTargetModels(venueA, version623, List.of("R770", "R650"));

    upgradeByModelDataMigrationService.migrateVenues(tenant.getId(), List.of(venueA.getId()));

    assertThat(upgradeScheduleRepository.findAllByVenueId(venueA.getId()))
        .hasSize(1)
        .singleElement()
        .extracting(UpgradeSchedule::getUpgradeScheduleFirmwareVersions)
        .asList()
        .extracting(UpgradeScheduleFirmwareVersion.class::cast)
        .hasSize(1)
        .satisfies(upgradeScheduleFirmwareVersions -> {
          assertThat(upgradeScheduleFirmwareVersions)
              .filteredOn(usfv -> usfv.getApFirmwareVersion().equals(version623))
              .isEmpty();
          assertThat(upgradeScheduleFirmwareVersions)
              .filteredOn(usfv -> usfv.getApFirmwareVersion().equals(version700))
              .hasSize(1)
              .singleElement()
              .extracting(UpgradeScheduleFirmwareVersion::getTargetApModels)
              .asList().containsExactlyInAnyOrder("R770", "R560", "R550");
        });
    verify(kairosApiClient, times(0)).deleteScheduleJobs(anyList());
  }

  private void validateVenueCurrentFirmware(Venue venue, String model, ApVersion version) {
    assertThat(venueCurrentFirmwareRepository.findByVenueIdAndApModel(venue.getId(), model))
        .isPresent()
        .get()
        .extracting(VenueCurrentFirmware::getFirmware)
        .isEqualTo(version);
  }

  private UpgradeSchedule createSchedule(Venue venue, List<ApVersion> versions) {
    return this.createSchedule(venue, versions, UpgradeScheduleStatus.PENDING);
  }

  private void updateScheduleTargetModels(Venue venue, ApVersion scheduleVersion, List<String> models) {
    upgradeScheduleRepository.findAllByVenueId(venue.getId())
        .stream()
        .flatMap(us -> us.getUpgradeScheduleFirmwareVersions().stream())
        .filter(usfv -> usfv.getApFirmwareVersion().equals(scheduleVersion))
        .forEach(usfv -> {
          usfv.setTargetApModels(models);
          upgradeScheduleFirmwareVersionRepository.save(usfv);
        });
  }

  private UpgradeSchedule createSchedule(Venue venue, List<ApVersion> versions, UpgradeScheduleStatus scheduleStatus) {
    ScheduleTimeSlot sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
    scheduleTimeSlotRepository.save(sts);
    UpgradeSchedule us = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, versions.get(0), sts,
        c -> c.setStatus(scheduleStatus));
    upgradeScheduleRepository.save(us);
    versions.forEach(v -> {
      UpgradeScheduleFirmwareVersion usfv =
          UpgradeScheduleFirmwareVersionTestFixture.randomUpgradeScheduleFirmwareVersion(us, v, c -> {});
      upgradeScheduleFirmwareVersionRepository.save(usfv);
      us.getUpgradeScheduleFirmwareVersions().add(usfv);
    });
    return upgradeScheduleRepository.save(us);
  }

  private VenueCurrentFirmware createVenueCurrentFirmware(Venue venue, String model, ApVersion version) {
    return venueCurrentFirmwareRepository.save(
        VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, version, model, c -> {}));
  }

  private void createVenueFirmwareVersion(Venue venue, String branchType, ApVersion version) {
    VenueFirmwareVersion vfv = new VenueFirmwareVersion();
    vfv.setTenant(venue.getTenant());
    vfv.setVenue(venue);
    vfv.setBranchType(branchType);
    vfv.setCurrentFirmwareVersion(version);
    repositoryUtil.createOrUpdate(vfv, venue.getTenant().getId(), randomTxId());
  }

  private void updateVersionSupportedModels(ApVersion apVersion, List<String> supportedApModels) {
    apVersion.setSupportedApModels(supportedApModels);
    repositoryUtil.createOrUpdate(apVersion, txCtxExtension.getTenantId(), randomTxId());
  }

  @TestConfiguration
  @Import({ApBranchFamilyServiceRouterTestConfig.class})
  static class TestConfig {

    @Bean
    @ConditionalOnMissingBean
    public UpgradeByModelDataMigrationService firmwareManagementMigrationService(
        VenueCurrentFirmwareRepository venueCurrentFirmwareRepository,
        VenueFirmwareVersionRepository venueFirmwareVersionRepository,
        UpgradeScheduleRepository upgradeScheduleRepository,
        UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository,
        ApRepository apRepository,
        TenantRepository tenantRepository,
        VenueRepository venueRepository,
        FeatureFlagService featureFlagService,
        ApVersionService apVersionService,
        WifiAsyncJobPublisher wifiAsyncJobPublisher,
        KairosApiClient kairosApiClient) {
      return new UpgradeByModelDataMigrationServiceImpl(venueCurrentFirmwareRepository, venueFirmwareVersionRepository,
          upgradeScheduleRepository, upgradeScheduleFirmwareVersionRepository, apRepository,
          tenantRepository, venueRepository, featureFlagService,
          apVersionService, wifiAsyncJobPublisher, kairosApiClient);
    }

    @Bean
    @ConditionalOnMissingBean
    public ApVersionService apVersionService(
        TenantFirmwareVersionRepository tenantFirmwareVersionRepository,
        VenueFirmwareVersionRepository venueFirmwareVersionRepository,
        VenueFirmwareVersionService venueFirmwareVersionService,
        ApVersionRepository apVersionRepository,
        DefaultApBranchFamilyRepository defaultApBranchFamilyRepository,
        ApBranchFamilyServiceRouter apBranchFamilyService) {
      return new ApVersionServiceImpl(
          tenantFirmwareVersionRepository,
          venueFirmwareVersionRepository,
          venueFirmwareVersionService,
          apVersionRepository,
          defaultApBranchFamilyRepository,
          apBranchFamilyService);
    }

    @Bean
    @ConditionalOnMissingBean
    public VenueFirmwareVersionService venueFirmwareVersionService(
        VenueFirmwareVersionRepository venueFirmwareVersionRepository) {
      return new VenueFirmwareVersionServiceImpl(venueFirmwareVersionRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public KairosApiClient kairosApiClient() {
      return mock(KairosApiClient.class);
    }
  }
}
