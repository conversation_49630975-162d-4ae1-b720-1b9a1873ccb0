package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.ap;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apGroup;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkApGroup;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkApGroupRadio;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.proto.ImportApResultJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.service.ImportApsCacheService;
import com.ruckus.cloud.wifi.service.core.exception.ImportException;
import com.ruckus.cloud.wifi.servicemodel.ImportApsCsv;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("ImportApsResultJobTest")
@WifiIntegrationTest
public class ConsumeImportApsResultJobTest {

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  @Autowired
  private ImportApsCacheService importApsCacheService;

  private Ap ap;

  private ApGroup apGroup1;

  private Venue venue1;

  private Network network1;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeImportApResultJobRequest {

    @BeforeEach
    void givenApGroupsNetworksPersistedInDb(final Tenant tenant, final Venue venue,
        final Network network)
        throws IOException, ImportException {
      venue.setTenant(tenant);
      venue1 = repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
      apGroup1 = repositoryUtil.createOrUpdate(
          apGroup().setTenant(always(tenant)).setVenue(always(venue)).generate(),
          tenant.getId(), randomTxId());
      ap = repositoryUtil.createOrUpdate(ap().setTenant(always(tenant))
          .setApGroup(always(apGroup1)).generate(), tenant.getId(), randomTxId());
      network1 = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      NetworkVenue networkVenue = repositoryUtil.createOrUpdate(
          networkVenue().setTenant(always(tenant)).setVenue(always(venue))
              .setNetwork(always(network)).setIsAllApGroups(alwaysTrue())
              .generate(), tenant.getId(), randomTxId());
      NetworkApGroup networkApGroup = repositoryUtil.createOrUpdate(
          networkApGroup()
              .setTenant(always(tenant))
              .setNetworkVenue(always(networkVenue))
              .setApGroup(always(apGroup1)).generate(), tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkApGroupRadio()
          .setTenant(always(tenant)).setNetworkApGroup(always(networkApGroup))
          .generate(), tenant.getId(), randomTxId());

      ImportApsCsv csv = new ImportApsCsv(
          txCtxExtension.getTenantId(),
          randomName(),
          txCtxExtension.getRequestId(),
          randomId(),
          randomName(),
          "http://wwww.download.url",
          null);
      csv.getApGroupIs().add(apGroup1.getId());

      importApsCacheService.updateImportApsCsv(txCtxExtension.getRequestId(), csv);
      importApsCacheService.updateDrsImportErrorsByRequestId(
          txCtxExtension.getRequestId(), Collections.emptyMap());
      importApsCacheService.updateLicenseImportErrorsByRequestId(
          txCtxExtension.getRequestId(), Collections.emptyMap());
      importApsCacheService.updateDrsBrokerDone(txCtxExtension.getRequestId());
      importApsCacheService.updateEntitlementDone(txCtxExtension.getRequestId());
    }

    @Test
    void whenConsumeImportApsResultJob(final Tenant tenant) {
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
          .setGenerateImportApResultJob(ImportApResultJob.newBuilder()
              .setRequestId(txCtxExtension.getRequestId())).build());

      var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId());

      // validate AP Group cmnCfgCollectorMessage
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtxExtension.getTenantId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
          .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
          .isNotEmpty().hasSize(1)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> apGroup1.getId().equals(op.getId()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(txCtxExtension.getTenantId()))
                      .containsEntry(Key.ID, ValueUtils.stringValue(apGroup1.getId()))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venue1.getId()))
                      .satisfies(docMap -> {
                        assertThat(docMap.get(Key.WIFI_NETWORK_IDS))
                            .isNotNull()
                            .extracting(value -> value.getListValue().getValuesList(),
                                InstanceOfAssertFactories.list(
                                    com.ruckus.cloud.events.gpb.Value.class))
                            .isNotEmpty().singleElement()
                            .extracting(com.ruckus.cloud.events.gpb.Value::getStringValue)
                            .isEqualTo(network1.getId());
                      });
                });
          });

      // validate Network cmnCfgCollectorMessage
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtxExtension.getTenantId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
          .isNotEmpty().hasSize(1)
          .allSatisfy(op -> {
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocOrThrow(Key.ID))
                .isEqualTo(ValueUtils.stringValue(network1.getId()));
          });
    }
  }
}
