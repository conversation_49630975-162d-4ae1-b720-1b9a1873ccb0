package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.SoftGreProfileRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.List;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@WifiUnitTest
public class VenueSoftGreGatewayFallbackFeatureTest {

  @MockBean
  private SoftGreProfileRepository repository;

  @SpyBean
  private VenueSoftGreGatewayFallbackFeature unit;

  @Nested
  class WhenConfigVenueSoftGre {

    @Test
    @FeatureFlag(disable = {WIFI_R370_TOGGLE, WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE})
    void givenFeatureFlagDisabled(Venue venue) {
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE})
    void givenSoftGreGatewayFallbackDisabled(Venue venue) {
      SoftGreProfile disabledProfile = new SoftGreProfile();
      disabledProfile.setGatewayFailbackEnabled(false);
      when(repository.findByIdInAndTenantId(
          anyList(), anyString())).thenReturn(List.of(disabledProfile));

      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE})
    void givenSoftGreGatewayFallbackEnabled(Venue venue) {
      SoftGreProfile enabledProfile = new SoftGreProfile();
      enabledProfile.setGatewayFailbackEnabled(true);

      when(repository.findByIdInAndTenantId(
          anyList(), anyString())).thenReturn(List.of(enabledProfile));

      BDDAssertions.then(unit.test(venue)).isTrue();
    }
  }
}
