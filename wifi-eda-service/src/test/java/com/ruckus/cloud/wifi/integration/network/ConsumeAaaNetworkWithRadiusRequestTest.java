package com.ruckus.cloud.wifi.integration.network;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_RADIUS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_RADIUS;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.RADIUS_SERVER_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomPort;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTimeSlot;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.service.NetworkServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusNetworkDataQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SortOrderEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.RadiusRepository;
import com.ruckus.cloud.wifi.service.ExtendedRadiusServiceCtrl;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@Slf4j
@Tag("RadiusServerProfileTest")
@WifiIntegrationTest
public class ConsumeAaaNetworkWithRadiusRequestTest extends AbstractRequestTest {

  private static final String RADIUS_ID = "radiusId";

  private static final String NETWORK_ID = "networkId";

  @Autowired
  private ExtendedRadiusServiceCtrl radiusServiceCtrl;

  @Autowired
  private NetworkServiceCtrl networkServiceCtrl;

  @Autowired
  private RadiusRepository radiusRepository;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  private void assertRadiusServers(Radius expectedRadius, Radius actualRadius) {
    assertNotNull(actualRadius.getId());
    assertEquals(expectedRadius.getName(), actualRadius.getName());
    assertEquals(expectedRadius.getType(), actualRadius.getType());
    // Validate radius primary
    assertEquals(expectedRadius.getPrimary().getIp(), actualRadius.getPrimary().getIp());
    assertEquals(expectedRadius.getPrimary().getPort(), actualRadius.getPrimary().getPort());
    assertEquals(expectedRadius.getPrimary().getSharedSecret(),
        actualRadius.getPrimary().getSharedSecret());
    // Validate radius secondary
    if (expectedRadius.getSecondary() != null) {
      assertEquals(expectedRadius.getSecondary().getIp(), actualRadius.getSecondary().getIp());
      assertEquals(expectedRadius.getSecondary().getPort(), actualRadius.getSecondary().getPort());
      assertEquals(expectedRadius.getSecondary().getSharedSecret(),
          actualRadius.getSecondary().getSharedSecret());
    }
  }

  @SneakyThrows
  private void assertRadiusNetworks(Radius radius, int radiusNetworksCount) {
    QueryRequest queryParams = new QueryRequest();
    queryParams.setPage(1);
    queryParams.setPageSize(100);
    queryParams.setSortField(NETWORK_ID);
    queryParams.setSortOrder(SortOrderEnum.ASC);
    RadiusNetworkDataQueryResponse result = radiusServiceCtrl.getRadiusNetworks(
        radius.getId(), queryParams);

    assertEquals(radiusNetworksCount, result.getTotalCount().intValue());
    assertEquals(radiusNetworksCount, result.getData().size());
  }

  private void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private static void assertDdccmOps(List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertRadiusIndexViewmodel(List<Operations> operations, OpType opType, Radius radius, int scope) {
    if (opType.equals(OpType.DEL)) {
      assertTrue(operations.stream()
          .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .filter(o -> radius.getId().equals(o.getId()))
          .filter(o -> o.getOpType() == opType)
          .findAny().isPresent());
    } else {
      assertTrue(operations.stream()
          .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .filter(o -> radius.getId().equals(o.getId()))
          .filter(o -> o.getOpType() == opType)
          .filter(o -> radius.getName()
              .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
          .filter(
              o -> o.getDocMap().get(EsConstants.Key.NETWORK_IDS).getListValue().getValuesCount()
                  == scope)
          .findAny().isPresent());
    }
  }

  private static void assertDdccmOpCount(List<Operation> operations, Action action, int expectCount,
      Predicate<? super Operation> predicate) {
    assertEquals(expectCount, operations.stream().filter(o -> o.getAction().equals(action)).filter(predicate).count());
  }

  private static com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler dummyNetworkVenueScheduler() {
    final var scheduler = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler();
    scheduler.setType(SchedulerTypeEnum.CUSTOM);
    scheduler.setMon(randomTimeSlot());
    scheduler.setTue(randomTimeSlot());
    scheduler.setWed(randomTimeSlot());
    scheduler.setThu(randomTimeSlot());
    scheduler.setFri(randomTimeSlot());
    scheduler.setSat(randomTimeSlot());
    scheduler.setSun(randomTimeSlot());
    return scheduler;
  }

  private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue networkVenue(String networkId, String venueId) {
    final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
    payload.setId(randomId()); // autoGenerated is true in wifi-api
    payload.setNetworkId(networkId);
    payload.setVenueId(venueId);
    payload.setIsAllApGroups(true);
    payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
    payload.setScheduler(dummyNetworkVenueScheduler());
    return payload;
  }

  @Test
  public void testAddAaaNetworkWithRadius(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    //////////
    // add AAANetwork

    AAANetwork aaaNetwork = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork.setAuthRadiusId(radiusAdded1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, aaaNetwork);
    assertActivityStatusSuccess(ADD_NETWORK);

    assertRadiusNetworks(radiusAdded1, 1);

    var viewOps1 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps1, 2),
        () -> assertTrue(viewOps1.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, radiusAdded1, 1)
    );

    //////////
    // activate Network to Venue
    Venue v1 = createVenue(tenant, "v1");
    NetworkVenue nv1 = networkVenue(aaaNetwork.getId(), v1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE, userName, nv1);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE);

    var viewOps2 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps2 {}", viewOps2);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps2, 2) // networkvenuemapping
    );

    // venueRadius, venueSchedule, wlanVenue
    var ddccmOps1 = receiveDdccmOperations(1);
    log.info("ddccmOps1 {}", ddccmOps1);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps1, 3),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 1, Operation::hasVenueRadius)
    );
  }

  @Test
  public void testAddAaaNetworkWithAcctRadiusAsAuthShouldFail(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.ACCOUNTING);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    //////////
    // add AAANetwork

    AAANetwork aaaNetwork = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork.setAuthRadiusId(radiusAdded1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, aaaNetwork);
    assertActivityStatusFail(ADD_NETWORK);
  }

  @Test
  public void testAddAaaNetworkWithAuthRadiusAsAcctShouldFail(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-2");
    radius2.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius2));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    assertEquals(2, radiusServiceCtrl.getRadiuses(Optional.empty()).size());

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    Radius radiusAdded2 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort());
    assertRadiusServers(radius2, radiusAdded2);
    assertRadiusNetworks(radiusAdded2, 0);

    //////////
    // add AAANetwork
    AAANetwork aaaNetwork = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork.setAuthRadiusId(radiusAdded1.getId());
    aaaNetwork.setAccountingRadiusId(radiusAdded2.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, aaaNetwork);
    assertActivityStatusFail(ADD_NETWORK);
  }

  @Test
  public void testAaaNetworkAddAndUpdateWithRadiusNonProxy(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-2");
    radius2.setType(RadiusProfileTypeEnum.ACCOUNTING);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius2));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    assertEquals(2, radiusServiceCtrl.getRadiuses(Optional.empty()).size());

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    Radius radiusAdded2 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort());
    assertRadiusServers(radius2, radiusAdded2);
    assertRadiusNetworks(radiusAdded2, 0);

    //////////
    // add AAANetwork
    AAANetwork aaaNetwork = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork.setAuthRadiusId(radiusAdded1.getId());
    aaaNetwork.setAccountingRadiusId(radiusAdded2.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, aaaNetwork);
    assertActivityStatusSuccess(ADD_NETWORK);

    assertRadiusNetworks(radiusAdded1, 1);
    assertRadiusNetworks(radiusAdded2, 1);

    var viewOps1 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps1, 3),
        () -> assertTrue(viewOps1.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, radiusAdded1, 1),
        () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, radiusAdded2, 1)
    );

    //////////
    // activate Network to Venue
    Venue v1 = createVenue(tenant, "v1");
    NetworkVenue nv1 = networkVenue(aaaNetwork.getId(), v1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE, userName, nv1);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE);

    receiveViewmodelCollectorOperations(1);

    // venueRadius, venueRadiusForAccounting, venueSchedule, wlanVenue
    var ddccmOps1 = receiveDdccmOperations(1);
    log.info("ddccmOps1 {}", ddccmOps1);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps1, 4),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 1, Operation::hasVenueRadius),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 1, Operation::hasVenueRadiusForAccounting)
    );

    //////////
    // prepare new RADIUS profile for update
    Radius radius3 = RadiusTestFixture.authRadius();
    radius3.setName("test-radius-3");
    radius3.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    Radius radius4 = RadiusTestFixture.authRadius();
    radius4.setName("test-radius-4");
    radius4.setType(RadiusProfileTypeEnum.ACCOUNTING);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius3));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius4));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    assertEquals(4, radiusServiceCtrl.getRadiuses(Optional.empty()).size());

    Radius radiusAdded3 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius3.getPrimary().getIp(), radius3.getPrimary().getPort());
    assertRadiusServers(radius3, radiusAdded3);
    assertRadiusNetworks(radiusAdded3, 0);

    Radius radiusAdded4 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius4.getPrimary().getIp(), radius4.getPrimary().getPort());
    assertRadiusServers(radius4, radiusAdded4);
    assertRadiusNetworks(radiusAdded4, 0);

    //////////
    // updateNetwork with new RADIUS
    AAANetwork aaaNetworkAdded = map(
        (com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork) networkServiceCtrl.getNetwork(
            aaaNetwork.getId(), Optional.empty()));

    aaaNetworkAdded.setAuthRadiusId(radiusAdded3.getId());
    aaaNetworkAdded.setAccountingRadiusId(radiusAdded4.getId());
    aaaNetworkAdded.setAuthRadius(null);
    aaaNetworkAdded.setAccountingRadius(null);

    RequestParams rps = new RequestParams().addPathVariable(NETWORK_ID, aaaNetworkAdded.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_NETWORK, userName, rps, aaaNetworkAdded);
    assertActivityStatusSuccess(UPDATE_NETWORK);

    AAANetwork aaaNetworkUpdated = map(
        (com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork) networkServiceCtrl.getNetwork(
            aaaNetwork.getId(), Optional.empty()));

    assertEquals(radiusAdded3.getId(), aaaNetworkUpdated.getAuthRadiusId());
    assertEquals(radiusAdded3.getId(), aaaNetworkUpdated.getAuthRadius().getId());
    assertEquals(radiusAdded4.getId(), aaaNetworkUpdated.getAccountingRadiusId());
    assertEquals(radiusAdded4.getId(), aaaNetworkUpdated.getAccountingRadius().getId());

    assertRadiusNetworks(radiusAdded1, 0);
    assertRadiusNetworks(radiusAdded2, 0);
    assertRadiusNetworks(radiusAdded3, 1);
    assertRadiusNetworks(radiusAdded4, 1);

    var viewOps2 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps2 {}", viewOps2);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps2, 6), // network, networkvenuemapping, radiusserverprofile
        () -> assertTrue(viewOps2.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.MOD)
            .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded1, 0),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded2, 0),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded3, 1),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded4, 1)
    );

    var ddccmOps2 = receiveDdccmOperations(1);
    log.info("ddccmOps2 {}", ddccmOps2);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps2, 5),
        () -> assertDdccmOpCount(ddccmOps2, Action.MODIFY, 1, Operation::hasWlanVenue),
        () -> assertDdccmOpCount(ddccmOps2, Action.ADD, 1, Operation::hasVenueRadius),
        () -> assertDdccmOpCount(ddccmOps2, Action.ADD, 1, Operation::hasVenueRadiusForAccounting),
        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasVenueRadius),
        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasVenueRadiusForAccounting)
    );

    //////////
    // Update Radius
    int newPort = randomPort();
    radiusAdded3.getPrimary().setPort(newPort);

    RequestParams rps2 = new RequestParams().addPathVariable(RADIUS_ID, radiusAdded3.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS, userName, rps2, map(radiusAdded3));
    assertActivityStatusSuccess(UPDATE_RADIUS);

    Radius radiusUpdated3 = radiusServiceCtrl.getRadiuses(Optional.empty()).stream()
        .filter(r -> r.getId().equals(radiusAdded3.getId())).findAny().get();

    assertEquals(newPort, radiusUpdated3.getPrimary().getPort());

    assertRadiusNetworks(radiusAdded1, 0);
    assertRadiusNetworks(radiusAdded2, 0);
    assertRadiusNetworks(radiusAdded3, 1);
    assertRadiusNetworks(radiusAdded4, 1);

    var viewOps3 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps3 {}", viewOps3);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps3, 1),
        () -> assertRadiusIndexViewmodel(viewOps3, OpType.MOD, radiusAdded3, 1)
    );

    var ddccmOps3 = receiveDdccmOperations(1);
    log.info("ddccmOps3 {}", ddccmOps3);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps3, 1),
        () -> assertDdccmOpCount(ddccmOps3, Action.MODIFY, 1, Operation::hasVenueRadius)
    );

    assertEquals(newPort,
        ddccmOps3.stream().map(Operation::getVenueRadius).findFirst().get().getServer(0).getPort());

  }

  @Test
  public void testAaaNetworkAddAndUpdateWithRadiusProxy(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // add Radius as profile
    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-2");
    radius2.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius2));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    assertEquals(2, radiusServiceCtrl.getRadiuses(Optional.empty()).size());

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    Radius radiusAdded2 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort());
    assertRadiusServers(radius2, radiusAdded2);
    assertRadiusNetworks(radiusAdded2, 0);

    //////////
    // add AAANetwork
    AAANetwork aaaNetwork = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork.setAuthRadiusId(radiusAdded1.getId());
    aaaNetwork.setEnableAuthProxy(true);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, aaaNetwork);
    assertActivityStatusSuccess(ADD_NETWORK);

    assertRadiusNetworks(radiusAdded1, 1);
    assertRadiusNetworks(radiusAdded2, 0);

    var viewOps1 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps1, 2),
        () -> assertTrue(viewOps1.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, radiusAdded1, 1)
    );

    //////////
    // activate Network to Venue
    Venue v1 = createVenue(tenant, "v1");
    NetworkVenue nv1 = networkVenue(aaaNetwork.getId(), v1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE, userName, nv1);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE);

    receiveViewmodelCollectorOperations(1); // networkvenuemapping

    // venueSchedule, wlanVenue, radiusAuthenticationService, authRealmProfile
    var ddccmOps1 = receiveDdccmOperations(2);
    log.info("ddccmOps1 {}", ddccmOps1);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps1, 4),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 0, Operation::hasVenueRadius),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 0, Operation::hasVenueRadiusForAccounting),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 1, Operation::hasRadiusAuthenticationService),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 1, Operation::hasAuthRealmProfile),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 1, Operation::hasWlanVenue)
    );

    String expectedAuthRealmProfileId =
        ddccmOps1.stream().filter(Operation::hasAuthRealmProfile)
            .filter(op -> op.getAction().equals(Action.ADD))
            .map(Operation::getAuthRealmProfile).findFirst().get().getId();

    WlanVenue wlanVenue = ddccmOps1.stream().filter(Operation::hasWlanVenue).findFirst().get()
        .getWlanVenue();

    assertTrue(wlanVenue.hasProxyAuthAaa());
    assertFalse(wlanVenue.hasProxyAcctAaa());
    assertFalse(wlanVenue.hasAuthAaa());
    assertFalse(wlanVenue.hasAcctAaa());
    assertEquals(expectedAuthRealmProfileId, wlanVenue.getProxyAuthAaa().getId());

    //////////
    // update Network with another Radius
    AAANetwork aaaNetworkAdded = map(
        (com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork) networkServiceCtrl.getNetwork(
            aaaNetwork.getId(), Optional.empty()));

    aaaNetworkAdded.setAuthRadiusId(radiusAdded2.getId());
    aaaNetworkAdded.setAuthRadius(null);

    RequestParams rps = new RequestParams().addPathVariable(NETWORK_ID, aaaNetworkAdded.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_NETWORK, userName, rps, aaaNetworkAdded);
    assertActivityStatusSuccess(UPDATE_NETWORK);

    AAANetwork aaaNetworkUpdated = map(
        (com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork) networkServiceCtrl.getNetwork(
            aaaNetwork.getId(), Optional.empty()));

    assertEquals(radiusAdded2.getId(), aaaNetworkUpdated.getAuthRadiusId());
    assertEquals(radiusAdded2.getId(), aaaNetworkUpdated.getAuthRadius().getId());

    assertRadiusNetworks(radiusAdded1, 0);
    assertRadiusNetworks(radiusAdded2, 1);

    // network, networkvenuemapping, 2 radiusserverprofile
    var viewOps2 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps2 {}", viewOps2);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps2, 4),
        () -> assertTrue(viewOps2.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.MOD)
            .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded1, 0),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded2, 1)
    );

    // ADD 1 radiusAuthenticationService, 1 authRealmProfile
    // MODIFY 1 wlanVenue
    // DELETE 1 radiusAuthenticationService, 1 authRealmProfile
    var ddccmOps2 = receiveDdccmOperations(1);
    log.info("ddccmOps2 {}", ddccmOps2);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps2, 5),
        () -> assertDdccmOpCount(ddccmOps2, Action.ADD, 1, Operation::hasRadiusAuthenticationService),
        () -> assertDdccmOpCount(ddccmOps2, Action.ADD, 1, Operation::hasAuthRealmProfile),
        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasRadiusAuthenticationService),
        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasAuthRealmProfile),
        () -> assertDdccmOpCount(ddccmOps2, Action.MODIFY, 1, Operation::hasWlanVenue)
    );

    expectedAuthRealmProfileId =
        ddccmOps1.stream().filter(Operation::hasAuthRealmProfile)
            .filter(op -> op.getAction().equals(Action.ADD))
            .map(Operation::getAuthRealmProfile).findFirst().get().getId();

    wlanVenue = ddccmOps1.stream().filter(Operation::hasWlanVenue).findFirst().get()
        .getWlanVenue();

    assertTrue(wlanVenue.hasProxyAuthAaa());
    assertFalse(wlanVenue.hasProxyAcctAaa());
    assertFalse(wlanVenue.hasAuthAaa());
    assertFalse(wlanVenue.hasAcctAaa());
    assertEquals(expectedAuthRealmProfileId, wlanVenue.getProxyAuthAaa().getId());

    //////////
    // Update Radius
    int newPort = randomPort();
    radiusAdded2.getPrimary().setPort(newPort);

    RequestParams rps2 = new RequestParams().addPathVariable(RADIUS_ID, radiusAdded2.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS, userName, rps2, map(radiusAdded2));
    assertActivityStatusSuccess(UPDATE_RADIUS);

    Radius radiusUpdated3 = radiusServiceCtrl.getRadiuses(Optional.empty()).stream()
        .filter(r -> r.getId().equals(radiusAdded2.getId())).findAny().get();

    assertEquals(newPort, radiusUpdated3.getPrimary().getPort());

    assertRadiusNetworks(radiusAdded1, 0);
    assertRadiusNetworks(radiusAdded2, 1);

    var viewOps3 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps3 {}", viewOps3);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps3, 1),
        () -> assertRadiusIndexViewmodel(viewOps3, OpType.MOD, radiusAdded2, 1)
    );

    var ddccmOps3 = receiveDdccmOperations(1);
    log.info("ddccmOps3 {}", ddccmOps3);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps3, 1),
        () -> assertDdccmOpCount(ddccmOps3, Action.MODIFY, 1, Operation::hasRadiusAuthenticationService)
    );

    assertEquals(newPort,
        ddccmOps3.stream().map(Operation::getRadiusAuthenticationService).findFirst().get()
            .getPrimary().getPort());
  }

}
