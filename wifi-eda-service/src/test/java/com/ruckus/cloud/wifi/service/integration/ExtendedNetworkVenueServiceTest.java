package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkActivationsQueryFilters;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkActivationsQueryRequest;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkActivationsQueryRequestV2;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.CloudpathServer;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskPassphraseGeneration;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenueScheduler;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CloudpathDeploymentTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DpskPolicyDefaultAccessEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MacAuthMacFormatEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PassphraseExpirationEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PassphraseFormatEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupGenerator;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmVenueApModelOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmVenueBonjourFencingOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmVenueDhcpServiceSettingOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmVenueOperationBuilder;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.publisher.DdccmPublisher;
import com.ruckus.cloud.wifi.repository.NetworkApGroupRadioRepository;
import com.ruckus.cloud.wifi.repository.NetworkApGroupRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.service.ApUpgradeService;
import com.ruckus.cloud.wifi.service.EntityIdGenerationLockService;
import com.ruckus.cloud.wifi.service.ExtendedApGroupServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedNetworkVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.FirmwareCountryCodeService;
import com.ruckus.cloud.wifi.service.InitVenueService;
import com.ruckus.cloud.wifi.service.NetworkApGroupService;
import com.ruckus.cloud.wifi.service.VenueCurrentFirmwareService;
import com.ruckus.cloud.wifi.service.VenueRadioService;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.util.RandomPassword;
import com.ruckus.cloud.wifi.service.impl.config.DdccmListenerTestKits;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedNetworkVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.utils.Hotspot20Helper;
import com.ruckus.cloud.wifi.service.validator.DhcpServiceValidator;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.AuthRadiusServiceTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueSchedulerTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VlanPoolTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.assertj.core.api.Condition;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Tag("NetworkVenueTest")
@WifiJpaDataTest
@FeatureFlag(enable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
class ExtendedNetworkVenueServiceTest {

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private ExtendedNetworkVenueServiceCtrl networkVenueService;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @MockBean
  private ExtendedVenueServiceCtrl venueService;
  @MockBean
  private InitVenueService initVenueService;
  @MockBean
  private ExtendedApGroupServiceCtrl apGroupService;
  @MockBean
  private NetworkApGroupService networkApGroupService;
  @MockBean
  private EntityIdGenerationLockService entityIdGenerationLockService;
  @MockBean
  private DhcpServiceValidator dhcpServiceValidator;
  @MockBean
  private FirmwareCountryCodeService firmwareCountryCodeService;
  @MockBean
  private ApUpgradeService apUpgradeService;
  @MockBean
  private VenueCurrentFirmwareService venueCurrentFirmwareService;

  @MockBean
  private VenueRadioService venueRadioService;

  @Autowired
  private NetworkVenueRepository networkVenueRepository;
  @Autowired
  private NetworkApGroupRepository networkApGroupRepository;
  @Autowired
  private NetworkApGroupRadioRepository networkApGroupRadioRepository;

  @MockBean
  private DdccmPublisher ddccmPublisher;

  @Value("${default.network.ap-group-activation-limit:15}")
  private long networkApGroupActivationLimit;

  @AfterEach
  void clearInvocations() {
    Mockito.clearInvocations(venueService, initVenueService, apGroupService,
        networkApGroupService, entityIdGenerationLockService);
  }

  @Deprecated
  @Tag("ApGroupTest")
  @Test
  void testAddNetworkVenue(Venue venue, @DefaultApGroup ApGroup apGroup,
      @OpenNetwork Network network) throws Exception {
    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(network);

    var networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue, apGroup,
        v -> v.setId(null));
    networkApGroup.setNetworkApGroupRadios(List.of(
        NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(networkApGroup,
            v -> v.setId(null))));
    networkVenue.setNetworkApGroups(List.of(networkApGroup));
    networkVenue.setScheduler(
        NetworkVenueSchedulerTestFixture.randomNetworkVenueScheduler(networkVenue));

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(apGroup)).when(apGroupService)
        .getApGroupsByVenue(eq(venue.getId()), any(), any());
    doReturn(apGroup).when(apGroupService).getApGroup(eq(apGroup.getId()));
    doReturn(List.of(1)).when(entityIdGenerationLockService).generateAvailabilities(any(), anyInt());
    mockNetworkGroupService();

    networkVenue = networkVenueService.addNetworkVenue(networkVenue);
    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId()))
        .isNotNull()
        .matches(nv -> venue.getId().equals(nv.getVenue().getId()))
        .matches(nv -> network.getId().equals(nv.getNetwork().getId()));

    assertThat(
        repositoryUtil.find(NetworkApGroup.class, networkVenue.getNetworkApGroups().get(0).getId()))
        .isNotNull()
        .extracting(NetworkApGroup::getApGroup)
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(apGroup.getId());

    assertThat(repositoryUtil.find(NetworkApGroupRadio.class,
        networkVenue.getNetworkApGroups().get(0).getNetworkApGroupRadios().get(0).getId()))
        .isNotNull()
        .extracting(NetworkApGroupRadio::getRadio)
        .isEqualTo(StrictRadioTypeEnum._2_4_GHz);

    assertThat(
        repositoryUtil.find(NetworkVenueScheduler.class, networkVenue.getScheduler().getId()))
        .isNotNull()
        .extracting(NetworkVenueScheduler::getType)
        .isEqualTo(SchedulerTypeEnum.CUSTOM);
  }

  @Deprecated
  @Tag("ApGroupTest")
  @Tag("VlanPoolTest")
  @Test
  void testUpdateNetworkVenue(Venue venue, @DefaultApGroup ApGroup apGroup,
      @OpenNetwork Network network,
      @ScheduledNetworkVenue NetworkVenue networkVenue, NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio)
      throws Exception {
    var vlanPool = VlanPoolTestFixture.randomVlanPool(networkVenue.getTenant());
    repositoryUtil.createOrUpdate(vlanPool, vlanPool.getTenant().getId(), randomTxId());

    var apGroup2 = ApGroupTestFixture.randomApGroup(venue, v -> v.setIsDefault(false));
    repositoryUtil.createOrUpdate(apGroup2, apGroup2.getTenant().getId(), randomTxId());
    var networkApGroup2 = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue, apGroup2,
        v -> v.setId(null));
    networkApGroup2.setNetworkApGroupRadios(List.of(
        NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(networkApGroup2,
            v -> v.setId(null)),
        NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(networkApGroup2, v -> {
          v.setId(null);
          v.setRadio(StrictRadioTypeEnum._5_GHz);
        }))
    );
    var networkVenueScheduler2 = new NetworkVenueScheduler();
    networkVenueScheduler2.setType(SchedulerTypeEnum.ALWAYS_OFF);

    var payload = new NetworkVenue();
    payload.setId(networkVenue.getId());
    payload.setNetwork(network);
    payload.setVenue(venue);
    payload.setNetworkApGroups(List.of(networkApGroup2));
    payload.setScheduler(networkVenueScheduler2);
    payload.setVlanPoolId(vlanPool.getId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(venue).when(initVenueService).initVenueIfNotExists(venue.getId());
    doReturn(List.of(apGroup2)).when(apGroupService)
        .getApGroupsByVenue(eq(venue.getId()), any(), any());
    doReturn(apGroup2).when(apGroupService).getVenueApGroup(venue.getId(), apGroup2.getId());

    networkVenue = networkVenueService.updateNetworkVenue(payload.getId(), payload);

    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId()))
        .isNotNull()
        .matches(nv -> venue.getId().equals(nv.getVenue().getId()))
        .matches(nv -> network.getId().equals(nv.getNetwork().getId()));

    assertThat(
        repositoryUtil.find(NetworkApGroup.class, networkVenue.getNetworkApGroups().get(0).getId()))
        .isNotNull()
        .extracting(NetworkApGroup::getApGroup)
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(apGroup2.getId());

    assertThat(repositoryUtil.find(NetworkApGroupRadio.class,
        networkVenue.getNetworkApGroups().get(0).getNetworkApGroupRadios().get(1).getId()))
        .isNotNull()
        .extracting(NetworkApGroupRadio::getRadio)
        .isEqualTo(StrictRadioTypeEnum._5_GHz);

    assertThat(
        repositoryUtil.find(NetworkVenueScheduler.class, networkVenue.getScheduler().getId()))
        .isNotNull()
        .extracting(NetworkVenueScheduler::getType)
        .isEqualTo(SchedulerTypeEnum.ALWAYS_OFF);
  }

  @Deprecated
  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_DEFAULT_6G_ENABLEMENT_TOGGLE)
  void testUpdateNetworkVenueWihBothRadio(
      Tenant tenant,
      Venue venue,
      @DefaultApGroup ApGroup apGroup,
      @OpenNetwork Network network,
      @ScheduledNetworkVenue NetworkVenue networkVenue,
      NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio)
      throws Exception {
    var vlanPool = VlanPoolTestFixture.randomVlanPool(networkVenue.getTenant());
    repositoryUtil.createOrUpdate(vlanPool, vlanPool.getTenant().getId(), randomTxId());

    var apGroup2 = ApGroupTestFixture.randomApGroup(venue, v -> v.setIsDefault(false));
    repositoryUtil.createOrUpdate(apGroup2, apGroup2.getTenant().getId(), randomTxId());
    var networkApGroup2 =
        NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue, apGroup2, v -> v.setId(null));
    networkApGroup2.setNetworkApGroupRadios(
        List.of(
            NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
                networkApGroup2,
                v -> {
                  v.setId(null);
                })));
    var networkVenueScheduler2 = new NetworkVenueScheduler();
    networkVenueScheduler2.setType(SchedulerTypeEnum.ALWAYS_OFF);

    var payload = new NetworkVenue();
    payload.setId(networkVenue.getId());
    payload.setNetwork(new Network(network.getId()));
    payload.setVenue(venue);
    payload.setNetworkApGroups(List.of(networkApGroup2));
    payload.setScheduler(networkVenueScheduler2);
    payload.setVlanPoolId(vlanPool.getId());
    payload.setAllApGroupsRadio(RadioEnum.Both);

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(venue).when(initVenueService).initVenueIfNotExists(venue.getId());
    doReturn(List.of(apGroup2))
        .when(apGroupService)
        .getApGroupsByVenue(eq(venue.getId()), any(), any());
    doReturn(apGroup2).when(apGroupService).getVenueApGroup(venue.getId(), apGroup2.getId());

    networkVenueService.updateNetworkVenue(payload.getId(), payload);

    Optional<NetworkVenue> result =
        networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(
            tenant.getId(), network.getId(), venue.getId());
    assertThat(result)
        .isNotNull()
        .hasValueSatisfying(
            nv -> {
              assertThat(nv.getAllApGroupsRadioTypes().size()).isEqualTo(2);
              assertThat(nv.getAllApGroupsRadioTypes())
                  .containsExactlyInAnyOrder(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz);
            });
  }

  @Deprecated
  @Tag("ApGroupTest")
  @Test
  void testUpdateNetworkVenueMappings(Venue venue, @DefaultApGroup ApGroup apGroup,
      @OpenNetwork Network network, @ScheduledNetworkVenue NetworkVenue networkVenue,
      NetworkApGroup networkApGroup, NetworkApGroupRadio networkApGroupRadio) throws Exception {

    // Gen NetworkVenue 1
    Tenant tenant = network.getTenant();
    NetworkVenue networkVenue1 = genNetworkVenue(tenant, venue, apGroup);
    String network1Id = networkVenue1.getNetwork().getId();

    // Gen NetworkVenue 2
    NetworkVenue networkVenue2 = genNetworkVenue(tenant, venue, apGroup);
    String network2Id = networkVenue2.getNetwork().getId();

    // Mock
    doReturn(Map.of(venue.getId(), venue)).when(initVenueService).addAndGetListByIds(anyList());
    doReturn(List.of(apGroup)).when(apGroupService)
        .getApGroupsByVenue(eq(venue.getId()), any(), any());
    doReturn(apGroup).when(apGroupService).getApGroup(eq(apGroup.getId()));
    doReturn(List.of(1, 2)).when(entityIdGenerationLockService).generateAvailabilities(any(), anyInt());

    // Execution add more networkVenue
    List<NetworkVenue> networkVenueList = new ArrayList<>();
    networkVenueList.add(networkVenue1);
    networkVenueList.add(networkVenue2);
    networkVenueService.addNetworkVenueMappings(networkVenueList);

    // Retrieve
    List<NetworkVenue> networkVenuesInDB =
        repositoryUtil.findAll(NetworkVenue.class, venue.getTenant().getId());
    assertThat(networkVenuesInDB).matches(r -> r.size() == 3);

    // Prepare Update
    var vlanPool = VlanPoolTestFixture.randomVlanPool(networkVenue.getTenant());
    repositoryUtil.createOrUpdate(vlanPool, vlanPool.getTenant().getId(), randomTxId());

    var apGroup2 = ApGroupTestFixture.randomApGroup(venue, v -> v.setIsDefault(false));
    repositoryUtil.createOrUpdate(apGroup2, apGroup2.getTenant().getId(), randomTxId());

    var networkApGroup2 = NetworkApGroupTestFixture.
                          randomNetworkApGroup(networkVenue, apGroup2, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkApGroup2, tenant.getId(), randomTxId());

    var networkApGroupRadio1 =
        NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(networkApGroup2, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkApGroupRadio1, tenant.getId(), randomTxId());

    var networkApGroupRadio2 =
        NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(networkApGroup2,
            v -> { v.setId(randomId());
                   v.setRadio(StrictRadioTypeEnum._5_GHz);
                  });
    repositoryUtil.createOrUpdate(networkApGroupRadio2, tenant.getId(), randomTxId());

    networkApGroup2.setNetworkApGroupRadios(List.of(networkApGroupRadio1, networkApGroupRadio2));
    repositoryUtil.createOrUpdate(networkApGroup2, tenant.getId(), randomTxId());

    var networkVenueScheduler2 = new NetworkVenueScheduler();
    networkVenueScheduler2.setType(SchedulerTypeEnum.ALWAYS_OFF);
    repositoryUtil.createOrUpdate(networkVenueScheduler2, tenant.getId(), randomTxId());

    var toUpdateNV = new NetworkVenue();
    toUpdateNV.setId(networkVenue.getId());
    toUpdateNV.setNetwork(network);
    toUpdateNV.setVenue(venue);
    toUpdateNV.setNetworkApGroups(List.of(networkApGroup2));
    toUpdateNV.setScheduler(networkVenueScheduler2);
    toUpdateNV.setVlanPoolId(vlanPool.getId());
    toUpdateNV.setTenant(tenant);

    Optional<NetworkVenue> targetNetworkVenue2 =
        networkVenuesInDB.stream().filter(nv -> networkVenue2.getId().equals(nv.getId())).findFirst();

    // Mock
    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(venue).when(initVenueService).initVenueIfNotExists(venue.getId());
    doReturn(List.of(apGroup2)).when(apGroupService)
        .getApGroupsByVenue(eq(venue.getId()), any(), any());
    doReturn(apGroup2).when(apGroupService).getVenueApGroup(venue.getId(), apGroup2.getId());

    // Update networkVenue mappings
    List<NetworkVenue> toUpdateNVList = new ArrayList<>();
    toUpdateNVList.add(toUpdateNV);
    toUpdateNVList.add(targetNetworkVenue2.get());
    networkVenueService.updateNetworkVenueMappings(toUpdateNVList);

    // Verify
    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId()))
        .isNotNull()
        .matches(nv -> venue.getId().equals(nv.getVenue().getId()))
        .matches(nv -> network.getId().equals(nv.getNetwork().getId()));

    assertThat(
        repositoryUtil.find(NetworkApGroup.class, networkVenue.getNetworkApGroups().get(0).getId()))
        .isNotNull()
        .extracting(NetworkApGroup::getApGroup)
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(apGroup2.getId());

    assertThat(repositoryUtil.find(NetworkApGroupRadio.class,
        networkVenue.getNetworkApGroups().get(0).getNetworkApGroupRadios().get(1).getId()))
        .isNotNull()
        .extracting(NetworkApGroupRadio::getRadio)
        .isEqualTo(StrictRadioTypeEnum._5_GHz);

    assertThat(
        repositoryUtil.find(NetworkVenueScheduler.class, networkVenue.getScheduler().getId()))
        .isNotNull()
        .extracting(NetworkVenueScheduler::getType)
        .isEqualTo(SchedulerTypeEnum.ALWAYS_OFF);
  }

  @Deprecated
  @Test
  void testAddNetworkVenueMapping(Venue venue, @DefaultApGroup ApGroup apGroup,
      @OpenNetwork Network network) throws Exception {

    // Gen NetworkVenue 1
    Tenant tenant = network.getTenant();
    NetworkVenue networkVenue = genNetworkVenue(tenant, venue, apGroup);
    String network1Id = networkVenue.getNetwork().getId();

    // Gen NetworkVenue 2
    NetworkVenue networkVenue2 = genNetworkVenue(tenant, venue, apGroup);
    String network2Id = networkVenue2.getNetwork().getId();

    // Mock
    doReturn(Map.of(venue.getId(), venue)).when(initVenueService).addAndGetListByIds(anyList());
    doReturn(List.of(1, 2)).when(entityIdGenerationLockService).generateAvailabilities(any(), anyInt());
    mockNetworkGroupService();

    // Execution
    List<NetworkVenue> networkVenueList = new ArrayList<>();
    networkVenueList.add(networkVenue);
    networkVenueList.add(networkVenue2);
    networkVenueService.addNetworkVenueMappings(networkVenueList);

    // Retrieve
    List<NetworkVenue> resultList =
        repositoryUtil.findAll(NetworkVenue.class, venue.getTenant().getId());
    NetworkVenue result0 = resultList.get(0);
    NetworkVenue networkVenueInDB1;
    NetworkVenue networkVenueInDB2;
    if (result0.getNetwork().getId().equals(network1Id)) {
      networkVenueInDB1 = resultList.get(0);
      networkVenueInDB2 = resultList.get(1);
    } else {
      networkVenueInDB1 = resultList.get(1);
      networkVenueInDB2 = resultList.get(0);
    }

    // verify networkVenueInDB1
    assertThat(networkVenueInDB1)
        .isNotNull()
        .matches(nv -> venue.getId().equals(nv.getVenue().getId()))
        .matches(nv -> network1Id.equals(nv.getNetwork().getId()));

    assertThat(
        repositoryUtil.find(NetworkApGroup.class, networkVenueInDB1.getNetworkApGroups().get(0).getId()))
        .isNotNull()
        .extracting(NetworkApGroup::getApGroup)
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(apGroup.getId());

    assertThat(repositoryUtil.find(NetworkApGroupRadio.class,
        networkVenueInDB1.getNetworkApGroups().get(0).getNetworkApGroupRadios().get(0).getId()))
        .isNotNull()
        .extracting(NetworkApGroupRadio::getRadio)
        .isEqualTo(StrictRadioTypeEnum._2_4_GHz);

    assertThat(
        repositoryUtil.find(NetworkVenueScheduler.class, networkVenueInDB1.getScheduler().getId()))
        .isNotNull()
        .extracting(NetworkVenueScheduler::getType)
        .isEqualTo(SchedulerTypeEnum.CUSTOM);

    // verify networkVenueInDB2
    assertThat(networkVenueInDB2)
        .isNotNull()
        .matches(nv -> venue.getId().equals(networkVenue2.getVenue().getId()))
        .matches(nv -> network2Id.equals(networkVenue2.getNetwork().getId()));

    assertThat(
        repositoryUtil.find(NetworkApGroup.class, networkVenueInDB2.getNetworkApGroups().get(0).getId()))
        .isNotNull()
        .extracting(NetworkApGroup::getApGroup)
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(apGroup.getId());

    assertThat(repositoryUtil.find(NetworkApGroupRadio.class,
        networkVenueInDB2.getNetworkApGroups().get(0).getNetworkApGroupRadios().get(0).getId()))
        .isNotNull()
        .extracting(NetworkApGroupRadio::getRadio)
        .isEqualTo(StrictRadioTypeEnum._2_4_GHz);

    assertThat(
        repositoryUtil.find(NetworkVenueScheduler.class, networkVenueInDB2.getScheduler().getId()))
        .isNotNull()
        .extracting(NetworkVenueScheduler::getType)
        .isEqualTo(SchedulerTypeEnum.CUSTOM);

    // Execution 2nd time, should not throw Exception with existing networkVenue
    NetworkVenue networkVenue3 = genNetworkVenue(tenant, venue, apGroup);
    List<NetworkVenue> networkVenueList2 = new ArrayList<>();
    networkVenueList2.add(networkVenue2);
    networkVenueList2.add(networkVenue3);
    networkVenueService.addNetworkVenueMappings(networkVenueList2);
  }

  @Deprecated
  @Test
  void testAddNetworkVenueMappingsExceeds(Venue venue, @DefaultApGroup ApGroup apGroup,
    Tenant tenant) throws Exception {

    List<NetworkVenue> networkVenueList = new ArrayList<>();

    doReturn(Map.of(venue.getId(), venue)).when(initVenueService).addAndGetListByIds(anyList());
    doReturn(IntStream.rangeClosed(0, (int)networkApGroupActivationLimit).boxed().collect(Collectors.toList()))
            .when(entityIdGenerationLockService).generateAvailabilities(any(), anyInt());
    mockNetworkGroupService();

    //Add 15 network venues
    for (int i = 0; i < networkApGroupActivationLimit; i++) {
      NetworkVenue networkVenue = genNetworkVenue(tenant, venue, apGroup);
      networkVenue.setIsAllApGroups(false);
      networkVenueList.add(networkVenue);
    }
    //Add 1 network venue with another apGroup
    ApGroup apg1 = repositoryUtil.createOrUpdate(ApGroupTestFixture.randomApGroup(venue), tenant.getId(), randomTxId());
    NetworkVenue networkVenue = genNetworkVenue(tenant, venue, apg1);
    networkVenue.setIsAllApGroups(false);
    networkVenueList.add(networkVenue);
    networkVenueService.addNetworkVenueMappings(networkVenueList);

    NetworkVenue exceedingNetworkVenue = genNetworkVenue(tenant, venue, apGroup);
    assertThatExceptionOfType(InvalidPropertyValueException.class)
      .describedAs("Should hit the validation, can't activate more than [%s] networks on one Ap Group."
          .formatted(networkApGroupActivationLimit))
      .isThrownBy(() -> networkVenueService.addNetworkVenueMappings(Collections.singletonList(exceedingNetworkVenue)));
  }

  private NetworkVenue genNetworkVenue(Tenant tenant, Venue venue, ApGroup apGroup) {

    Network network = Generators.network(
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class).generate();
    network.setTenant(tenant);
    network.setName("OpenNetwork"+randomId().substring(1,6));
    network.getWlan().setNetwork(network);
    network.getWlan().setSsid(network.getName());
    repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());

    NetworkVenue networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(network);
    networkVenue.setTenant(tenant);

    var networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue, apGroup,
        v -> v.setId(null));
    networkApGroup.setNetworkApGroupRadios(List.of(
        NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
            networkApGroup, v -> v.setId(null))));

    networkVenue.setNetworkApGroups(List.of(networkApGroup));
    networkVenue.setScheduler(
        NetworkVenueSchedulerTestFixture.randomNetworkVenueScheduler(networkVenue));

    return networkVenue;
  }

  private List<NetworkApGroup> createMockNetworkApGroups(List<NetworkVenue> networkVenues) {
    List<NetworkApGroup> networkApGroups = new ArrayList<>();
    for (var networkVenue : networkVenues) {
      for (var apGroup : getApGroups(networkVenue)) {
        networkApGroups.add(new NetworkApGroupGenerator()
            .setNetworkVenue(always(networkVenue))
            .setApGroup(always(apGroup))
            .generate());
      }
    }
    return networkApGroups;
  }

  private List<ApGroup> getApGroups(NetworkVenue networkVenue) {
    if (networkVenue.getIsAllApGroups()) {
      return networkVenue.getVenue().getApGroups();
    } else {
      if (networkVenue.getNetworkApGroups().isEmpty()) {
        return List.of(getDefaultApGroup(networkVenue.getVenue()));
      }
      return networkVenue.getNetworkApGroups().stream().map(NetworkApGroup::getApGroup)
          .collect(Collectors.toList());
    }
  }

  private ApGroup getDefaultApGroup(Venue venue) {
    return venue.getApGroups().stream().filter(ApGroup::getIsDefault)
        .findAny().orElseThrow(() -> new RuntimeException("No default ApGroup found"));
  }

  private List<ApGroup> getAllApGroups(Venue venue) {
    return venue.getApGroups();
  }

  @Deprecated
  @Test
  void testGetNetworkVenue(Venue venue, @OpenNetwork Network network,
      NetworkVenue networkVenue) throws Exception {
    networkVenue = networkVenueService.getNetworkVenue(networkVenue.getId());

    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId()))
        .isNotNull()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(networkVenue.getId());
  }

  @Deprecated
  @Test
  void testGetNetworkVenueByNetworkAndVenue() throws Exception {
    assertThatExceptionOfType(UnsupportedOperationException.class)
        .isThrownBy(
            () -> networkVenueService.getNetworkVenueByNetworkAndVenue(randomId(), randomId()));
  }

  @Deprecated
  @Test
  void testGetNetworkVenues() throws Exception {
    assertThatExceptionOfType(UnsupportedOperationException.class)
        .isThrownBy(
            () -> networkVenueService.getNetworkVenues(randomId(), Optional.empty(),
                Optional.empty()));
  }

  @Deprecated
  @Test
  void testGetVenueNetworks() throws Exception {
    assertThatExceptionOfType(UnsupportedOperationException.class)
        .isThrownBy(
            () -> networkVenueService.getVenueNetworks(randomId(), Optional.empty(),
                Optional.empty()));
  }

  @Deprecated
  @Test
  void testDeleteNetworkVenue(Venue venue, @OpenNetwork Network network,
      NetworkVenue networkVenue) throws Exception {
    networkVenueService.deleteNetworkVenue(networkVenue.getId());
    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId()))
        .isNull();
  }

  @Deprecated
  @Test
  void testDeleteNetworkVenues(Venue venue, @OpenNetwork Network network,
      NetworkVenue networkVenue) throws Exception {
    final var venue2 = VenueTestFixture.randomVenue(networkVenue.getTenant());
    repositoryUtil.createOrUpdate(venue2, venue2.getTenant().getId(), randomTxId());
    final var networkVenue2 = NetworkVenueTestFixture.randomNetworkVenue(network, venue2,
        v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue2, networkVenue2.getTenant().getId(), randomTxId());

    networkVenueService.deleteNetworkVenues(List.of(networkVenue.getId(), networkVenue2.getId()));
    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId())).isNull();
    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue2.getId())).isNull();
  }

  @Deprecated
  @Test
  void testGetNetworkApGroupsByVenue() throws Exception {
    assertThatExceptionOfType(UnsupportedOperationException.class)
        .isThrownBy(
            () -> networkVenueService.getNetworkApGroupsByVenue(List.of()));
  }

  @Deprecated
  @Tag("NetworkActivationTest")
  @Test
  void testGetNetworkActivationsByQuery_byNetworkId(Venue venue,
      @DefaultApGroup ApGroup apGroup, @OpenNetwork Network network,
      NetworkVenue networkVenue, NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio) throws Exception {
    final var venue2 = VenueTestFixture.randomVenue(networkVenue.getTenant());
    repositoryUtil.createOrUpdate(venue2, venue2.getTenant().getId(), randomTxId());
    final var networkVenue2 = NetworkVenueTestFixture.randomNetworkVenue(network, venue2,
        v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue2, networkVenue2.getTenant().getId(), randomTxId());

    final var query = networkActivationsQueryRequest()
        .setNetworkId(always(network.getId())).generate();

    final var response = networkVenueService.getNetworkActivationsByQuery(query);
    assertThat(response).isNotNull();
    assertThat(response.getData())
        .hasSize(2)
        .allMatch(nv -> network.getId().equals(nv.getNetwork().getId()));
  }

  @Deprecated
  @Tag("NetworkActivationTest")
  @Test
  void testGetNetworkActivationsByQuery_byVenueId(Venue venue,
      @DefaultApGroup ApGroup apGroup, @OpenNetwork Network network,
      NetworkVenue networkVenue, NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio) throws Exception {
    final var network2 = NetworkTestFixture.randomNetwork(networkVenue.getTenant());
    repositoryUtil.createOrUpdate(network2, network2.getTenant().getId(), randomTxId());
    final var networkVenue2 = NetworkVenueTestFixture.randomNetworkVenue(network2, venue,
        v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue2, networkVenue2.getTenant().getId(), randomTxId());

    final var query = networkActivationsQueryRequest()
        .setVenueId(always(venue.getId())).generate();

    final var response = networkVenueService.getNetworkActivationsByQuery(query);
    assertThat(response).isNotNull();
    assertThat(response.getData())
        .hasSize(2)
        .allMatch(nv -> venue.getId().equals(nv.getVenue().getId()));
  }

  @Deprecated
  @Tag("NetworkActivationTest")
  @Test
  void testGetNetworkActivationsByQuery_byNetworkIdAndVenueId(Venue venue,
      @DefaultApGroup ApGroup apGroup, @OpenNetwork Network network,
      NetworkVenue networkVenue, NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio) throws Exception {

    final var query = networkActivationsQueryRequest()
        .setNetworkId(always(network.getId()))
        .setVenueId(always(venue.getId())).generate();

    final var response = networkVenueService.getNetworkActivationsByQuery(query);
    assertThat(response).isNotNull();
    assertThat(response.getData())
        .hasSize(1)
        .allMatch(nv -> networkVenue.getId().equals(nv.getId()));
  }

  @Deprecated
  @Tag("NetworkActivationTest")
  @Test
  void testGetNetworkActivationsByQueryV1_1_byNetworkId(Venue venue,
      @DefaultApGroup ApGroup apGroup, @OpenNetwork Network network,
      NetworkVenue networkVenue, NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio) throws Exception {
    final var venue2 = VenueTestFixture.randomVenue(networkVenue.getTenant());
    repositoryUtil.createOrUpdate(venue2, venue2.getTenant().getId(), randomTxId());
    final var networkVenue2 = NetworkVenueTestFixture.randomNetworkVenue(network, venue2,
        v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue2, networkVenue2.getTenant().getId(), randomTxId());

    final var query = networkActivationsQueryRequestV2(
        networkActivationsQueryFilters().setNetworkId(always(network.getId()))).generate();

    final var response = networkVenueService.getNetworkActivationsByQueryV1_1(query);
    assertThat(response).isNotNull();
    assertThat(response.getData())
        .hasSize(2)
        .allMatch(nv -> network.getId().equals(nv.getNetwork().getId()));
  }

  @Deprecated
  @Tag("NetworkActivationTest")
  @Test
  void testGetNetworkActivationsByQueryV1_1_byVenueId(Venue venue,
      @DefaultApGroup ApGroup apGroup, @OpenNetwork Network network,
      NetworkVenue networkVenue, NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio) throws Exception {
    final var network2 = NetworkTestFixture.randomNetwork(networkVenue.getTenant());
    repositoryUtil.createOrUpdate(network2, network2.getTenant().getId(), randomTxId());
    final var networkVenue2 = NetworkVenueTestFixture.randomNetworkVenue(network2, venue,
        v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue2, networkVenue2.getTenant().getId(), randomTxId());

    final var query = networkActivationsQueryRequestV2(
        networkActivationsQueryFilters().setVenueId(always(venue.getId()))).generate();

    final var response = networkVenueService.getNetworkActivationsByQueryV1_1(query);
    assertThat(response).isNotNull();
    assertThat(response.getData())
        .hasSize(2)
        .allMatch(nv -> venue.getId().equals(nv.getVenue().getId()));
  }

  @Deprecated
  @Tag("NetworkActivationTest")
  @Test
  void testGetNetworkActivationsByQueryV1_1_byNetworkIdAndVenueId(Venue venue,
      @DefaultApGroup ApGroup apGroup, @OpenNetwork Network network,
      NetworkVenue networkVenue, NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio) throws Exception {

    final var query = networkActivationsQueryRequestV2(
        networkActivationsQueryFilters()
            .setNetworkId(always(network.getId()))
            .setVenueId(always(venue.getId()))).generate();

    final var response = networkVenueService.getNetworkActivationsByQueryV1_1(query);
    assertThat(response).isNotNull();
    assertThat(response.getData())
        .hasSize(1)
        .allMatch(nv -> networkVenue.getId().equals(nv.getId()));
  }

  @Deprecated
  @Tag("NetworkActivationTest")
  @Test
  void testGetNetworkActivationsByQueryV1_1_byNetworkIdOrVenueId(Venue venue1,
      @DefaultApGroup ApGroup apGroup, @OpenNetwork Network network1,
      NetworkVenue network1Venue1, NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio) throws Exception {
    final var venue2 = VenueTestFixture.randomVenue(venue1.getTenant());
    repositoryUtil.createOrUpdate(venue2, venue2.getTenant().getId(), randomTxId());
    final var network1Venue2 = NetworkVenueTestFixture.randomNetworkVenue(network1, venue2,
        v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(network1Venue2, network1Venue2.getTenant().getId(), randomTxId());
    final var network2 = NetworkTestFixture.randomNetwork(venue1.getTenant());
    repositoryUtil.createOrUpdate(network2, network2.getTenant().getId(), randomTxId());
    final var network2Venue2 = NetworkVenueTestFixture.randomNetworkVenue(network2, venue2,
        v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(network2Venue2, network2Venue2.getTenant().getId(), randomTxId());

    final var query = networkActivationsQueryRequestV2()
        .setFilters(networkActivationsQueryFilters()
            .setNetworkId(options(network1.getId(), null))
            .setVenueId(options(null, venue2.getId()))
            .toListGenerator(2)).generate();

    final var response = networkVenueService.getNetworkActivationsByQueryV1_1(query);
    assertThat(response).isNotNull();
    assertThat(response.getData())
        // 3 of network1Venue1 & network1Venue2 & network2Venue2
        .hasSize(3)
        // 2 of network1Venue1 & network1Venue2
        .areExactly(2, new Condition<>((NetworkVenue nv) ->
            network1.getId().equals(nv.getNetwork().getId()),
            "networkId should be " + network1.getId()))
        // 2 of network1Venue2 & network2Venue2
        .areExactly(2, new Condition<>((NetworkVenue nv) ->
            venue2.getId().equals(nv.getVenue().getId()),
            "venueId should be " + venue2.getId()))
        // 1 of network1Venue1
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            network1.getId().equals(nv.getNetwork().getId())
                && venue1.getId().equals(nv.getVenue().getId()),
            String.format("networkId should be %s && venueId should be %s",
                network1.getId(), venue1.getId())))
        // 1 of network2Venue2
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            network2.getId().equals(nv.getNetwork().getId())
                && venue2.getId().equals(nv.getVenue().getId()),
            String.format("networkId should be %s && venueId should be %s",
                network2.getId(), venue2.getId())))
        // no network2Venue1
        .areNot(new Condition<>((NetworkVenue nv) ->
            network2.getId().equals(nv.getNetwork().getId())
                && venue1.getId().equals(nv.getVenue().getId()),
            String.format("networkId should be %s && venueId should be %s",
                network2.getId(), venue1.getId())));
  }

  @Deprecated
  @Tag("NetworkActivationTest")
  @Test
  void testGetNetworkActivationsByQueryViaGrpc_byNetworkIdAndVenueId(Venue venue,
      @DefaultApGroup ApGroup apGroup, @OpenNetwork Network network,
      NetworkVenue networkVenue, NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio) throws Exception {

    final var query = networkActivationsQueryRequestV2(
        networkActivationsQueryFilters()
            .setNetworkId(always(network.getId()))
            .setVenueId(always(venue.getId()))).generate();

    final var response = networkVenueService.getNetworkActivationsByQueryViaGrpc(
        network.getTenant().getId(), query);
    assertThat(response).isNotNull();
    assertThat(response)
        .hasSize(1)
        .allMatch(nv -> networkVenue.getId().equals(nv.getId()));
  }

  @Test
  public void testActivateWifiNetworkOnVenue(Venue venue, @OpenNetwork Network network) throws Exception {
    var vlanPool = VlanPoolTestFixture.randomVlanPool(network.getTenant());
    repositoryUtil.createOrUpdate(vlanPool, vlanPool.getTenant().getId(), randomTxId());
    network.getWlan().getAdvancedCustomization().setVlanPool(vlanPool);
    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(network);
    networkVenue.setIsAllApGroups(true);
    var tenantId = TxCtxHolder.tenantId();
    var venueId = venue.getId();
    var networkId = network.getId();

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1)).when(entityIdGenerationLockService).generateAvailabilities(any(), anyInt());

   networkVenueService.activateWifiNetworkOnVenue(venue.getId(), network.getId(), networkVenue);
    // Exercise
    Optional<NetworkVenue> result = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(tenantId, networkId, venueId);

    // Verify
    assertThat(result)
        .isNotNull()
        .hasValueSatisfying(nv -> {
          assertThat(nv.getVenue().getId()).isEqualTo(venueId);
          assertThat(nv.getNetwork().getId()).isEqualTo(networkId);
          assertThat(nv.getVlanPoolId()).isNull();
        });
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_AP_DEFAULT_6G_ENABLEMENT_TOGGLE})
  public void testActivateWifiNetworkOnVenueWithDefaultRadiosOnWPA3(Tenant tenant, Venue venue)
      throws Exception {
    var pskNetworkWithWpa3 =
        NetworkTestFixture.randomPskNetwork(
            tenant,
            n -> {
              n.getWlan().setWlanSecurity(WlanSecurityEnum.WPA3);
            });
    repositoryUtil.createOrUpdate(
        pskNetworkWithWpa3, pskNetworkWithWpa3.getTenant().getId(), randomTxId());
    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(pskNetworkWithWpa3);
    networkVenue.setIsAllApGroups(true);
    var tenantId = tenant.getId();
    var venueId = venue.getId();
    var networkId = pskNetworkWithWpa3.getId();

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    networkVenueService.activateWifiNetworkOnVenue(venue.getId(), networkId, networkVenue);

    Optional<NetworkVenue> result =
        networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(tenantId, networkId, venueId);
    assertThat(result)
        .isNotNull()
        .hasValueSatisfying(
            nv -> {
              assertThat(nv.getAllApGroupsRadioTypes().size()).isEqualTo(3);
              assertThat(nv.getAllApGroupsRadioTypes())
                  .containsExactlyInAnyOrder(
                      RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz, RadioTypeEnum._6_GHz);
            });
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_AP_DEFAULT_6G_ENABLEMENT_TOGGLE})
  public void testActivateWifiNetworkOnVenueWithDefaultRadiosOnOwe(Tenant tenant, Venue venue)
      throws Exception {
    var openNetworkWithOwe =
        NetworkTestFixture.randomOpenNetwork(
            tenant,
            n -> {
              n.getWlan().setWlanSecurity(WlanSecurityEnum.OWE);
            });
    repositoryUtil.createOrUpdate(
        openNetworkWithOwe, openNetworkWithOwe.getTenant().getId(), randomTxId());
    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(openNetworkWithOwe);
    networkVenue.setIsAllApGroups(true);
    var tenantId = tenant.getId();
    var venueId = venue.getId();
    var networkId = openNetworkWithOwe.getId();

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    networkVenueService.activateWifiNetworkOnVenue(venue.getId(), networkId, networkVenue);

    Optional<NetworkVenue> result =
        networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(tenantId, networkId, venueId);
    assertThat(result)
        .isNotNull()
        .hasValueSatisfying(
            nv -> {
              assertThat(nv.getAllApGroupsRadioTypes().size()).isEqualTo(3);
              assertThat(nv.getAllApGroupsRadioTypes())
                  .containsExactlyInAnyOrder(
                      RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz, RadioTypeEnum._6_GHz);
            });
  }

  @Test
  @FeatureFlag(disable = {FlagNames.WIFI_AP_DEFAULT_6G_ENABLEMENT_TOGGLE})
  public void testActivateWifiNetworkOnVenueWithDefaultRadiosOnWPA3WhenFfDisabled(
      Tenant tenant, Venue venue) throws Exception {
    var pskNetworkWithWpa3 =
        NetworkTestFixture.randomPskNetwork(
            tenant,
            n -> {
              n.getWlan().setWlanSecurity(WlanSecurityEnum.WPA3);
            });
    repositoryUtil.createOrUpdate(
        pskNetworkWithWpa3, pskNetworkWithWpa3.getTenant().getId(), randomTxId());
    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(pskNetworkWithWpa3);
    networkVenue.setIsAllApGroups(true);
    var tenantId = tenant.getId();
    var venueId = venue.getId();
    var networkId = pskNetworkWithWpa3.getId();

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    networkVenueService.activateWifiNetworkOnVenue(venue.getId(), networkId, networkVenue);

    Optional<NetworkVenue> result =
        networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(tenantId, networkId, venueId);
    assertThat(result)
        .isNotNull()
        .hasValueSatisfying(
            nv -> {
              assertThat(nv.getAllApGroupsRadioTypes().size()).isEqualTo(2);
              assertThat(nv.getAllApGroupsRadioTypes())
                  .containsExactlyInAnyOrder(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz);
            });
  }

  @Test
  void testDeactivateWifiNetworkOnVenue(Venue venue, @OpenNetwork Network network,
      NetworkVenue networkVenue) throws Exception {
    networkVenueService.deactivateWifiNetworkOnVenue(venue.getId(), network.getId());
    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId()))
        .isNull();
  }

  @Test
  void testDeactivateWifiNetworkOnVenueTwice(Venue venue, @OpenNetwork Network network, NetworkVenue networkVenue) throws Exception {
    networkVenueService.deactivateWifiNetworkOnVenue(venue.getId(), network.getId());
    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId())).isNull();
    assertDoesNotThrow(() -> networkVenueService.deactivateWifiNetworkOnVenue(venue.getId(), network.getId()));
  }

  @Test
  void testGetVenueWifiNetworkSettings(Venue venue, @OpenNetwork Network network,
      NetworkVenue networkVenue) throws Exception {
    networkVenue = networkVenueService.getVenueWifiNetworkSettings(venue.getId(), network.getId());

    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId()))
        .isNotNull()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(networkVenue.getId());
  }

  @Test
  void testUpdateVenueWifiNetworkSettings(Venue venue, @DefaultApGroup ApGroup apGroup,
      @OpenNetwork Network network)
      throws Exception {
    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(apGroup)).when(apGroupService)
        .getApGroupsByVenue(eq(venue.getId()), any(), any());
    mockNetworkGroupService();

    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(network);
    networkVenue.setIsAllApGroups(false);
    var networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue, apGroup,
        v -> v.setId(null));
    networkApGroup.setNetworkApGroupRadios(List.of(
        NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
            networkApGroup, v -> v.setId(null))));
    networkVenue.setNetworkApGroups(List.of(networkApGroup));

    networkVenueService.addNetworkVenue(networkVenue);

    var networkVenueScheduler = new NetworkVenueScheduler();
    networkVenueScheduler.setType(SchedulerTypeEnum.ALWAYS_OFF);

    var payload = new NetworkVenue();
    var vlanPool = VlanPoolTestFixture.randomVlanPool(network.getTenant());
    repositoryUtil.createOrUpdate(vlanPool, vlanPool.getTenant().getId(), randomTxId());
    network.getWlan().getAdvancedCustomization().setVlanPool(vlanPool);
    payload.setId(networkVenue.getId());
    payload.setNetwork(network);
    payload.setVenue(venue);
    payload.setScheduler(networkVenueScheduler);
    payload.setIsAllApGroups(false);

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(venue).when(initVenueService).initVenueIfNotExists(venue.getId());

    networkVenue = networkVenueService.updateVenueWifiNetworkSettings(venue.getId(), network.getId(), payload);

    assertThat(repositoryUtil.find(NetworkVenue.class, networkVenue.getId()))
        .isNotNull()
        .matches(nv -> venue.getId().equals(nv.getVenue().getId()))
        .matches(nv -> network.getId().equals(nv.getNetwork().getId()));
    assertThat(
        repositoryUtil.find(NetworkVenueScheduler.class, networkVenue.getScheduler().getId()))
        .isNotNull()
        .extracting(NetworkVenueScheduler::getType)
        .isEqualTo(SchedulerTypeEnum.ALWAYS_OFF);

    assertThat(
        repositoryUtil.find(NetworkApGroup.class, networkVenue.getNetworkApGroups().get(0).getId()))
        .isNotNull()
        .extracting(NetworkApGroup::getApGroup)
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(apGroup.getId());
  }

  private void mockNetworkGroupService() throws Exception {
    doAnswer(invocation -> {
      List<NetworkApGroup> networkApGroups = invocation.getArgument(0);
      networkApGroups = networkApGroups.stream()
          .map(e -> networkApGroupRepository.save(e))
          .collect(Collectors.toList());
      networkApGroups.forEach(e -> {
        List<NetworkApGroupRadio> radios = e.getNetworkApGroupRadios().stream()
                .map(radio -> networkApGroupRadioRepository.save(radio))
                .collect(Collectors.toList());
        e.setNetworkApGroupRadios(radios);
      });
      return networkApGroups;
    }).when(networkApGroupService)
        .createNetworkApGroups(anyList(), any(), any());
  }

  @Test
  void testActivateWifiNetworkOnVenue_openNetworkWithMacRegistration_withoutMacRegistrationId_fail(
      Venue venue, @OpenNetwork Network network) throws Exception {
    network.getWlan().setMacAddressAuthentication(true);
    repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());
    final var networkVenue =
        NetworkVenueTestFixture.randomNetworkVenue(network, venue, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(), network.getId(), networkVenue))
        .withMessage(Errors.WIFI_10458.message())
        .extracting(InvalidPropertyValueException::getErrorCode)
        .isEqualTo(Errors.WIFI_10458);
  }

  @Test
  void
      testActivateWifiNetworkOnVenue_openNetworkWithMacRegistration_withBothMacRegistrationIdAndRadius_fail(
          Tenant tenant, Venue venue, @OpenNetwork Network network) throws Exception {
    var authRadius = RadiusTestFixture.authRadius();
    authRadius.setTenant(tenant);
    repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());

    network.getWlan().setMacAddressAuthentication(true);
    network.getWlan().setMacRegistrationListId(randomId());
    network.setAuthRadius(authRadius);

    repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());
    final var networkVenue =
        NetworkVenueTestFixture.randomNetworkVenue(network, venue, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(), network.getId(), networkVenue))
        .withMessage(Errors.WIFI_10458.message())
        .extracting(InvalidPropertyValueException::getErrorCode)
        .isEqualTo(Errors.WIFI_10458);
  }

  @Test
  void testActivateWifiNetworkOnVenue_aaaNetwork_withoutRadius_fail(Tenant tenant, Venue venue)
      throws Exception {
    var aaaNetwork =
        NetworkTestFixture.randomAAANetwork(
            tenant,
            n -> {
              n.getWlan().setMacAddressAuthentication(true);
              n.getWlan().setMacAuthMacFormat(MacAuthMacFormatEnum.Lower);
            });
    repositoryUtil.createOrUpdate(aaaNetwork, aaaNetwork.getTenant().getId(), randomTxId());
    final var networkVenue =
        NetworkVenueTestFixture.randomNetworkVenue(aaaNetwork, venue, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(), aaaNetwork.getId(), networkVenue))
        .withMessage(
            "Either Authentication Service or Cloudpath Server is required to be configured for an AAA network.");
  }

  @Deprecated(forRemoval = true)
  @Test
  @FeatureFlag(disable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
  void testActivateWifiNetworkOnVenue_aaaNetwork_withBothCloudpathIdAndRadius_fail(
      Tenant tenant, Venue venue) throws Exception {
    var authRadius = RadiusTestFixture.authRadius();
    authRadius.setTenant(tenant);
    repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());

    var authRadiusService =
        AuthRadiusServiceTestFixture.randomAuthRadiusService(tenant, authRadius);
    repositoryUtil.createOrUpdate(authRadiusService, tenant.getId(), randomTxId());

    var cloudpathServer = new CloudpathServer();
    cloudpathServer.setId(randomId());
    cloudpathServer.setTenant(tenant);
    cloudpathServer.setAuthRadius(authRadiusService.getRadius());
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.Cloud);
    repositoryUtil.createOrUpdate(cloudpathServer, tenant.getId(), randomTxId());

    var aaaNetwork =
        NetworkTestFixture.randomAAANetwork(
            tenant,
            n -> {
              n.setAuthRadius(authRadius);
              n.setCloudpathServer(cloudpathServer);
              n.getWlan().setMacAddressAuthentication(true);
              n.getWlan().setMacAuthMacFormat(MacAuthMacFormatEnum.Lower);
            });

    repositoryUtil.createOrUpdate(aaaNetwork, aaaNetwork.getTenant().getId(), randomTxId());
    final var networkVenue =
        NetworkVenueTestFixture.randomNetworkVenue(aaaNetwork, venue, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(), aaaNetwork.getId(), networkVenue))
        .withMessage(Errors.WIFI_10240.message());
  }

  @Test
  void testActivateWifiNetworkOnVenue_dpskNetwork_withoutRadiusAndDpskServiceProfileId_fail(
      Tenant tenant,
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.DpskNetwork(
              wlanSecurity = WlanSecurityEnum.WPA23Mixed,
              isDsaeServiceNetwork = true)
          com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork network)
      throws Exception {
    network.setAuthRadius(null);
    network.setDsaeNetworkPairId(null);
    repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    final var networkVenue =
        NetworkVenueTestFixture.randomNetworkVenue(network, venue, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(), network.getId(), networkVenue))
        .withMessage(Errors.WIFI_10512.message())
        .extracting(InvalidPropertyValueException::getErrorCode)
        .isEqualTo(Errors.WIFI_10512);

    repositoryUtil.remove(networkVenue, tenant.getId(),false);
    repositoryUtil.remove(network, tenant.getId(),false);
  }

  @Test
  void testActivateWifiNetworkOnVenue_dpskNetwork_withBothRadiusAndDpskServiceProfileId_fail(
      Tenant tenant,
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.DpskNetwork(
              wlanSecurity = WlanSecurityEnum.WPA23Mixed,
              isDsaeServiceNetwork = true)
          com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork network)
      throws Exception {
    var authRadius = RadiusTestFixture.authRadius();
    authRadius.setTenant(tenant);
    repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());

    network.setAuthRadius(authRadius);
    network.setDpskServiceProfileId(randomId());
    repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    final var networkVenue =
        NetworkVenueTestFixture.randomNetworkVenue(network, venue, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(), network.getId(), networkVenue))
        .withMessage(Errors.WIFI_10512.message())
        .extracting(InvalidPropertyValueException::getErrorCode)
        .isEqualTo(Errors.WIFI_10512);

    repositoryUtil.remove(networkVenue, tenant.getId(), false);
    repositoryUtil.remove(network, tenant.getId(), false);
  }

  @Test
  void testActivateWifiNetworkOnVenue_dpskNetworkWithPassphraseGeneration_withoutRadius_fail(
      Tenant tenant,
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.DpskNetwork(
              wlanSecurity = WlanSecurityEnum.WPA23Mixed,
              isDsaeServiceNetwork = true)
          com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork network)
      throws Exception {
    DpskPassphraseGeneration dpskPassphraseGeneration = new DpskPassphraseGeneration();
    dpskPassphraseGeneration.setLength((short) ThreadLocalRandom.current().nextInt(8, 63));
    dpskPassphraseGeneration.setExpiration(PassphraseExpirationEnum.ONE_MONTH);
    dpskPassphraseGeneration.setFormat(PassphraseFormatEnum.MOST_SECURED);
    dpskPassphraseGeneration.setPolicyDefaultAccess(DpskPolicyDefaultAccessEnum.ACCEPT);

    network.setAuthRadius(null);
    network.setDpskServiceProfileId(randomId());
    network.setDpskPassphraseGeneration(dpskPassphraseGeneration);
    repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    final var networkVenue =
        NetworkVenueTestFixture.randomNetworkVenue(network, venue, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    assertThatExceptionOfType(CommonException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(), network.getId(), networkVenue))
        .withMessage("dpskPassphraseGeneration cannot be set");

    repositoryUtil.remove(networkVenue, tenant.getId(), false);
    repositoryUtil.remove(network, tenant.getId(), false);
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testActivateWifiNetworkOnVenue_hotspot20Network_withoutOperatorAndIdentityProviders_fail(
      Tenant tenant, Venue venue) throws Exception {

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    var networkVenue = new NetworkVenue(randomId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(), hotspot20Network.getId(), networkVenue))
        .withMessage(Errors.WIFI_10525.message())
        .extracting(InvalidPropertyValueException::getErrorCode)
        .isEqualTo(Errors.WIFI_10525);
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testAddNetworkOnVenues_hotspot20Network_withoutOperatorAndIdentityProviders_fail(
      Tenant tenant, Venue venue, @DefaultApGroup ApGroup apGroup) throws Exception {

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    NetworkVenue networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(hotspot20Network);
    networkVenue.setTenant(tenant);

    var networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue, apGroup,
        v -> v.setId(null));
    networkApGroup.setNetworkApGroupRadios(List.of(
        NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
            networkApGroup, v -> v.setId(null))));

    networkVenue.setNetworkApGroups(List.of(networkApGroup));
    networkVenue.setScheduler(
        NetworkVenueSchedulerTestFixture.randomNetworkVenueScheduler(networkVenue));

    List<NetworkVenue> networkVenueList = new ArrayList<>();
    networkVenueList.add(networkVenue);

    // Mock
    doReturn(Map.of(venue.getId(), venue)).when(initVenueService).addAndGetListByIds(anyList());
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());
    mockNetworkGroupService();

    // Execution
    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.addNetworkVenueMappings(networkVenueList))
        .withMessage(Errors.WIFI_10525.message())
        .extracting(InvalidPropertyValueException::getErrorCode)
        .isEqualTo(Errors.WIFI_10525);
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testActivateWifiNetworkOnVenue_hotspot20Network_shouldTriggerDdccmVenueOperation(
      Tenant tenant, Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.103") ApVersion currentVersion) throws Exception {
    var hotspot20Helper = mockStatic(Hotspot20Helper.class);

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    hotspot20Network = repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());


    venue.setWifiFirmwareVersion(currentVersion);
    venue.setApPassword(RandomPassword.generate());
    repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    var networkVenue = new NetworkVenue(randomId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    when(Hotspot20Helper.isHotspot20Network(any())).thenReturn(true);
    when(Hotspot20Helper.isHotspot20SettingsActivated(any())).thenReturn(true);

    // when
    networkVenueService.activateWifiNetworkOnVenue(venue.getId(), hotspot20Network.getId(), networkVenue);

    // Then
    ArgumentCaptor<WifiConfigRequest> captor = ArgumentCaptor.forClass(WifiConfigRequest.class);
    verify(ddccmPublisher, times(1)).publish(captor.capture());

    List<WifiConfigRequest> ddccmRequests = captor.getAllValues();
    assertThat(ddccmRequests)
        .isNotNull()
        .flatExtracting(WifiConfigRequest::getOperationsList)
        .filteredOn(Operation::hasVenue)
        .extracting(Operation::getVenue)
        .hasSize(1);

    hotspot20Helper.close();
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testDeactivateWifiNetworkOnVenue_hotspot20Network_shouldTriggerDdccmVenueOperation(
      Tenant tenant, Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.103") ApVersion currentVersion) throws Exception {
    var hotspot20Helper = mockStatic(Hotspot20Helper.class);

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    hotspot20Network = repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    var networkVenue = Generators.networkVenue().generate();
    networkVenue.setNetwork(hotspot20Network);
    networkVenue.setVenue(venue);
    repositoryUtil.createOrUpdate(networkVenue, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    venue.setWifiFirmwareVersion(currentVersion);
    venue.setApPassword(RandomPassword.generate());
    repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    when(Hotspot20Helper.isHotspot20Network(any())).thenReturn(true);

    // when
    networkVenueService.deactivateWifiNetworkOnVenue(venue.getId(), hotspot20Network.getId());

    // Then
    ArgumentCaptor<WifiConfigRequest> captor = ArgumentCaptor.forClass(WifiConfigRequest.class);
    verify(ddccmPublisher, times(1)).publish(captor.capture());

    List<WifiConfigRequest> ddccmRequests = captor.getAllValues();
    assertThat(ddccmRequests)
        .isNotNull()
        .flatExtracting(WifiConfigRequest::getOperationsList)
        .filteredOn(Operation::hasVenue)
        .extracting(Operation::getVenue)
        .hasSize(1);

    hotspot20Helper.close();
  }

  @Test
  void testAddNetworkVenue_aaaNetworkWithCertTemplate_success(Tenant tenant,
      Venue venue, AAANetwork aaaNetwork)
      throws Exception {
    aaaNetwork.setUseCertificateTemplate(true);
    aaaNetwork.getWlan().setCertificateTemplateId("test");
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId(), txCtxExtension.newRandomId());
    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));

    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(aaaNetwork);

    networkVenueService.addNetworkVenue(networkVenue);

    Optional<NetworkVenue> result = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(
        tenant.getId(), aaaNetwork.getId(), venue.getId());
    assertThat(result)
        .isNotNull()
        .hasValueSatisfying(nv -> {
          assertThat(nv.getVenue().getId()).isEqualTo(venue.getId());
          assertThat(nv.getNetwork().getId()).isEqualTo(aaaNetwork.getId());
        });
  }

  @Test
  void testAddNetworkVenue_aaaNetworkWithCertTemplate_withoutCertTemplateId_fail(Tenant tenant,
      Venue venue, AAANetwork aaaNetwork)
      throws Exception {
    aaaNetwork.setUseCertificateTemplate(true);
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId(), txCtxExtension.newRandomId());
    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));

    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(aaaNetwork);
    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.addNetworkVenue(networkVenue))
        .withMessageContaining("Certificate template is required for network");
  }

  @Test
  void testActivateWifiNetworkOnVenue_aaaNetworkWithCertTemplate_success(Tenant tenant,
      Venue venue, AAANetwork aaaNetwork)
      throws Exception {
    aaaNetwork.setUseCertificateTemplate(true);
    aaaNetwork.getWlan().setCertificateTemplateId("test");
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId(), txCtxExtension.newRandomId());
    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));

    var networkVenue = new NetworkVenue(randomId());

    networkVenueService.activateWifiNetworkOnVenue(venue.getId(), aaaNetwork.getId(), networkVenue);

    Optional<NetworkVenue> result = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(
        tenant.getId(), aaaNetwork.getId(), venue.getId());
    assertThat(result)
        .isNotNull()
        .hasValueSatisfying(nv -> {
          assertThat(nv.getVenue().getId()).isEqualTo(venue.getId());
          assertThat(nv.getNetwork().getId()).isEqualTo(aaaNetwork.getId());
        });
  }

  @Test
  void testActivateWifiNetworkOnVenue_aaaNetworkWithCertTemplate_withoutCertTemplateId_fail(
      Tenant tenant,
      Venue venue, AAANetwork aaaNetwork)
      throws Exception {
    aaaNetwork.setUseCertificateTemplate(true);
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId(), txCtxExtension.newRandomId());
    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));

    var networkVenue = new NetworkVenue();

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(), aaaNetwork.getId(), networkVenue))
        .withMessageContaining("Certificate template is required for network");
  }

  @Test
  void testActivateWifiNetworkOnVenue_guestCloudPathNetwork_withoutRadius_fail(Venue venue,
      GuestNetwork guestNetwork)
      throws Exception {
    guestNetwork.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
    repositoryUtil.createOrUpdate(guestNetwork, guestNetwork.getTenant().getId(), randomTxId());
    final var networkVenue =
        NetworkVenueTestFixture.randomNetworkVenue(guestNetwork, venue, v -> v.setId(randomId()));
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    doReturn(venue).when(initVenueService).initVenueIfNotExists(any(Venue.class));
    doReturn(List.of(1))
        .when(entityIdGenerationLockService)
        .generateAvailabilities(any(), anyInt());

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () -> networkVenueService.activateWifiNetworkOnVenue(venue.getId(),
                guestNetwork.getId(), networkVenue))
        .withMessage(
            "Cannot activate Guest Cloudpath network on venue without Auth Radius");
  }

  @Deprecated
  @Test
  void testAddNetworkVenueMappings_withoutCertTemplateId_fail(Venue venue, @DefaultApGroup ApGroup apGroup,
      Tenant tenant) throws Exception {
    // given
    List<NetworkVenue> networkVenueList = new ArrayList<>();
    doReturn(Map.of(venue.getId(), venue)).when(initVenueService).addAndGetListByIds(anyList());
    doReturn(IntStream.rangeClosed(0, (int)networkApGroupActivationLimit).boxed().collect(Collectors.toList()))
        .when(entityIdGenerationLockService).generateAvailabilities(any(), anyInt());
    mockNetworkGroupService();

    // gen network
    AAANetwork network = (AAANetwork) Generators.network(
        AAANetwork.class).generate();
    network.setTenant(tenant);
    network.setName("AAANetwork"+randomId().substring(1,6));
    network.setUseCertificateTemplate(true);
    network.getWlan().setNetwork(network);
    network = repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());

    // set network venue
    NetworkVenue networkVenue = new NetworkVenue(randomId());
    networkVenue.setVenue(venue);
    networkVenue.setNetwork(network);
    networkVenue.setTenant(tenant);
    networkVenueList.add(networkVenue);

    // when, then
    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .describedAs("Certificate template is required for network %s"
            .formatted(network.getId()))
        .isThrownBy(() -> networkVenueService.addNetworkVenueMappings(networkVenueList));
  }

  @TestConfiguration
  @Import({ExtendedNetworkVenueServiceCtrlImplTestConfig.class,
      DdccmListenerTestKits.class,
      DdccmVenueApModelOperationBuilder.class,
      DdccmVenueDhcpServiceSettingOperationBuilder.class,
      DdccmVenueBonjourFencingOperationBuilder.class,
      DdccmVenueOperationBuilder.class})
  static class TestConfig {

  }
}
