package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.common.collect.Lists;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel50Enum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RegistrationStateEnum;
import com.ruckus.cloud.wifi.servicemodel.projection.ApCreatedDateProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.ApDhcpEventProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.ApRegistrationProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.TenantApCountProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApCountProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApModelCountProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApModelProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApSerialNumberProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.jdbc.Sql;

/**
 * Created by Hank Hao on 2022-11-01
 */
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant)
        VALUES ('e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap_group (id, venue, tenant)
        VALUES ('818552afabf544878057e510b9bb88b5', 'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap (id, name, ap_group, floorplan_id, tenant, soft_deleted)
        VALUES ('900000005015', 'My AP 15', '818552afabf544878057e510b9bb88b5', '5e180d7fd19d4dc1a6a768aa2255a23d', '4c8279f79307415fa9e4c88a1819f0fc', false);
    INSERT INTO ap (id, name, ap_group, tenant, model, enable50g, soft_deleted)
        VALUES ('900000008880', 'My AP 80', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', 'R710', true, false);
    INSERT INTO ap (id, name, ap_group, tenant, model, enable50g, arp_allowed_channels_50g, soft_deleted)
        VALUES ('900000008881', 'My AP 81', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', 'R730', true, '36,40,44', false);
    INSERT INTO ap (id, name, ap_group, tenant, model, soft_deleted)
        VALUES ('900000008882', 'My AP 82', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', '', false);
    INSERT INTO ap (id, name, ap_group, tenant, model, soft_deleted)
        VALUES ('900000008883', 'My AP 83', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', null, false);
    INSERT INTO ap (id, name, ap_group, tenant, model, mac, soft_deleted, registration_state)
        VALUES ('900000008884', 'My AP 84', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', 'R730', '00:00:00:00:00:00', false, 'APPROVED');
    INSERT INTO ap (id, name, ap_group, tenant, soft_deleted, registration_state, created_date)
        VALUES ('900000008885', 'My AP 85', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', false, 'REJECTED', CURRENT_TIMESTAMP);
    """)
public class ApRepositoryTest {

  private static final String AP_GROUP_ID = "818552afabf544878057e510b9bb88b5";
  private static final String VENUE_ID = "e465bac6afb747a4987d0d0945f77221";
  private static final String FLOORPLAN_ID = "5e180d7fd19d4dc1a6a768aa2255a23d";
  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";

  @Autowired
  private ApRepository apRepository;

  @Test
  void findByMacTest() {
    assertThat(apRepository.findByMac("00:00:00:00:00:00")).isPresent();
    assertThat(apRepository.findByMac("00:00:00:00:00:01")).isEmpty();
    assertThat(apRepository.findByMacAndTenantId("00:00:00:00:00:00", "4c8279f79307415fa9e4c88a1819f0fc")).isPresent();
    assertThat(apRepository.findByMacAndTenantId("00:00:00:00:00:00", "xxxx")).isEmpty();
  }

  @Test
  public void findByIdInAndTenantIdTest() {
    var apIds = List.of("900000005015", "900000008880");
    assertEquals(2, apRepository.findByIdInAndTenantId(apIds, TENANT_ID).size());
  }

  @Test
  public void findByApGroupIdAndTenantIdTest() {
    assertEquals(7, apRepository.findByApGroupIdAndTenantId(AP_GROUP_ID, TENANT_ID).size());
  }

  @Test
  public void findByApGroupIdAndTenantIdWithPageTest() {
    assertEquals(7, apRepository.findByApGroupIdAndTenantId(AP_GROUP_ID, TENANT_ID, null).size());
  }

  @Test
  public void findByApGroupVenueIdAndTenantIdTest() {
    assertEquals(7, apRepository.findByApGroupVenueIdAndTenantId(VENUE_ID, TENANT_ID).size());
  }

  @Test
  public void findByApGroupVenueIdAndTenantIdAndModelInTest() {
    assertEquals(2, apRepository.findByApGroupVenueIdAndTenantIdAndModelIn(VENUE_ID, TENANT_ID, List.of("R730")).size());
  }

  @Test
  public void findByApGroupVenueIdAndTenantIdWithPageTest() {
    assertEquals(7, apRepository.findByApGroupVenueIdAndTenantId(VENUE_ID, TENANT_ID, null).size());
  }

  @Test
  public void findByPositionFloorplanIdAndTenantIdTest() {
    assertEquals(1,
        apRepository.findByPositionFloorplanIdAndTenantId(FLOORPLAN_ID, TENANT_ID).size());
  }

  @Test
  public void findModelsByTenantIdAndApGroupIdTest() {
    assertThat(apRepository.findModels(TENANT_ID, AP_GROUP_ID))
        .hasSize(2)
        .contains("R710", "R730");
  }

  @Test
  public void findByTenantIdAndApGroupIdAndModelAndRadioParams50GAllowedChannelsIsNotNullTest() {
    assertThat(
        apRepository.findByTenantIdAndApGroupIdAndModelInAndRadioParams50GAllowedChannelsIsNotNull(
            TENANT_ID, AP_GROUP_ID, Set.of("R710"))).isEmpty();
    assertThat(
        apRepository.findByTenantIdAndApGroupIdAndModelInAndRadioParams50GAllowedChannelsIsNotNull(
            TENANT_ID, AP_GROUP_ID, Set.of("R710", "R730")))
        .hasSize(1)
        .singleElement()
        .satisfies(ap -> {
          assertThat(ap.getId()).isEqualTo("900000008881");
          assertThat(ap.getModel()).isEqualTo("R730");
          assertThat(ap.getRadioCustomization().getEnable50G()).isTrue();
          assertThat(ap.getRadioCustomization().getApRadioParams50G()).isNotNull();
          assertThat(ap.getRadioCustomization().getApRadioParams50G().getAllowedChannels())
              .isNotNull()
              .containsExactlyInAnyOrder(Channel50Enum._36, Channel50Enum._40, Channel50Enum._44);
        });
  }

  @Test
  public void countApByTenantIdAndModelsGroupByModelTest() {
    var result = apRepository.countApByTenantIdAndModelsGroupByModel(
        TENANT_ID, Set.of("R710", "R610"));
    assertEquals(1, result.size());
    var target = result.get(0);
    assertEquals("R710", target.getModel());
    assertEquals(1, target.getCount());
  }

  @Test
  public void findVenueApCountByVenueIdsTest() {
    List<VenueApCountProjection> vApCountProjectionList = apRepository.findVenueApCountByVenueIds(List.of(VENUE_ID));
    assertEquals(1, vApCountProjectionList.size());
    assertEquals(7, vApCountProjectionList.get(0).apCount());
  }

  @Test
  public void findVenueApCountByVenueIdsAndApModels() {
    List<VenueApCountProjection> vApCountProjectionList =
        apRepository.findVenueApCountByVenueIdsAndApModels(List.of(VENUE_ID), List.of("R730"));
    assertEquals(1, vApCountProjectionList.size());
    assertEquals(2, vApCountProjectionList.get(0).apCount());

    vApCountProjectionList =
        apRepository.findVenueApCountByVenueIdsAndApModels(List.of(VENUE_ID), List.of("R730", "R710"));
    assertEquals(3, vApCountProjectionList.get(0).apCount());
  }

  @Test
  public void findEmptyVenueApCountByVenueIdsAndApModels() {
    List<VenueApCountProjection> vApCountProjectionList =
        apRepository.findVenueApCountByVenueIdsAndApModels(List.of("1234"), List.of("R730"));
    assertEquals(0, vApCountProjectionList.size());
  }

  @Test
  public void findVenueApModelCountByVenueIdsTest() {
    List<VenueApModelCountProjection> vApModelCountProjectionList = apRepository.findVenueApModelCountByVenueIds(List.of(VENUE_ID));
    assertEquals(4, vApModelCountProjectionList.size());
    assertEquals(2, vApModelCountProjectionList.stream().filter(v -> "R730".equals(v.getApModel())).findFirst().get().getApCount());
    assertEquals(1, vApModelCountProjectionList.stream().filter(v -> "R710".equals(v.getApModel())).findFirst().get().getApCount());
    assertEquals(2, vApModelCountProjectionList.stream().filter(v -> StringUtils.isEmpty(
      v.getApModel())).count());
  }

  @Test
  @Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant)
        VALUES ('e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap_group (id, venue, tenant)
        VALUES ('818552afabf544878057e510b9bb88b5', 'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap (id, ap_group, floorplan_id, tenant, soft_deleted)
        VALUES ('900000005015', '818552afabf544878057e510b9bb88b5', '5e180d7fd19d4dc1a6a768aa2255a23d', '4c8279f79307415fa9e4c88a1819f0fc', false);
    INSERT INTO ap (id, ap_group, tenant, model, mac, enable50g, soft_deleted)
        VALUES ('900000008880', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', 'R710', '00:00:00:00:00:00', true, false);
    INSERT INTO ap (id, ap_group, tenant, model, mac, enable50g, soft_deleted)
        VALUES ('900000005333', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', 'R730', '00:00:00:00:00:01', true, false);
    INSERT INTO ap (id, ap_group, tenant, model, enable50g, arp_allowed_channels_50g, soft_deleted)
        VALUES ('900000008881', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', 'R730', true, '36,40,44', false);
    INSERT INTO ap (id, ap_group, tenant, model, soft_deleted)
        VALUES ('900000008882', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', '', false);
    INSERT INTO ap (id, ap_group, tenant, model, soft_deleted)
        VALUES ('900000008883', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', null, false);
    INSERT INTO ap (id, ap_group, tenant, model, mac, soft_deleted, registration_state)
        VALUES ('900000008884', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', 'R730', '00:00:00:00:00:04', false, 'APPROVED');
    INSERT INTO ap (id, ap_group, tenant, model, mac, soft_deleted, registration_state)
        VALUES ('900000001113', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', 'R730', '00:00:00:00:00:05', false, 'APPROVED');
    INSERT INTO ap (id,  ap_group, tenant, soft_deleted, registration_state, created_date)
        VALUES ('900000003332', '818552afabf544878057e510b9bb88b5', '4c8279f79307415fa9e4c88a1819f0fc', false, 'REJECTED', CURRENT_TIMESTAMP);
    """)
  public void findVenueApModelCountByTenantIdAndMacNotNullAndModelNotNull() {
    List<VenueApModelCountProjection> vApModelCountProjectionList =
        apRepository.findVenueApModelCountByTenantIdAndMacNotNullAndModelNotNullAndVenueIdIn(TENANT_ID,
            List.of("e465bac6afb747a4987d0d0945f77221"));
    assertThat(vApModelCountProjectionList)
        .hasSize(2)
        .extracting(VenueApModelCountProjection::getApModel, VenueApModelCountProjection::getApCount)
        .containsExactlyInAnyOrder(tuple("R730", 3L), tuple("R710", 1L));

    vApModelCountProjectionList =
        apRepository.findVenueApModelCountByTenantIdAndMacNotNullAndModelNotNullAndVenueIdIn(TENANT_ID,
        List.of("4c8279f79307415fa9e4c88a1819f0fc"));
    assertThat(vApModelCountProjectionList).isEmpty();
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
      INSERT INTO venue (id, tenant)
          VALUES ('e619cccb6bd74075ba0b220611969aab', '4c8279f79307415fa9e4c88a1819f0fc');
      INSERT INTO ap_group (id, tenant, venue)
          VALUES ('d888fd374a974e548af80a7ecbc74230', '4c8279f79307415fa9e4c88a1819f0fc','e619cccb6bd74075ba0b220611969aab');
      INSERT INTO ap (id, tenant, ap_group, soft_deleted)
          VALUES ('900000009527','4c8279f79307415fa9e4c88a1819f0fc','d888fd374a974e548af80a7ecbc74230', false);
      """)
  void findVenueId() {
    String serial = "900000009527";
    String expectVenueId = "e619cccb6bd74075ba0b220611969aab";
    assertThat(apRepository.findVenueIdBySerial(serial))
        .isNotEmpty()
        .isEqualTo(Optional.of(expectVenueId));
  }

  @Test
  void findEmptyVenueId() {
    String serial = "900000009527";
    assertThat(apRepository.findVenueIdBySerial(serial))
        .isNotNull()
        .isEqualTo(Optional.empty());
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('b338eaa6796443829192a61093e143f9');
      INSERT INTO venue (id, tenant)
          VALUES ('0e2f68ab79154ffea64aa52c5cc48826', 'b338eaa6796443829192a61093e143f9');
      INSERT INTO venue (id, tenant)
          VALUES ('0e2f68ab79154ffea64aa52c5cc48827', 'b338eaa6796443829192a61093e143f9');    
      INSERT INTO ap_group (id, tenant, venue)
          VALUES ('d888fd374a974e548af80a7ecbc74230', 'b338eaa6796443829192a61093e143f9','0e2f68ab79154ffea64aa52c5cc48826');
      INSERT INTO ap_group (id, tenant, venue)
          VALUES ('d888fd374a974e548af80a7ecbc74231', 'b338eaa6796443829192a61093e143f9','0e2f68ab79154ffea64aa52c5cc48827');    
      INSERT INTO ap (id, tenant, ap_group, soft_deleted)
          VALUES ('900000006688','b338eaa6796443829192a61093e143f9','d888fd374a974e548af80a7ecbc74230', false);
      INSERT INTO ap (id, tenant, ap_group, soft_deleted)
          VALUES ('900000006689','b338eaa6796443829192a61093e143f9','d888fd374a974e548af80a7ecbc74231', false);
      """)
  void findVenueIdsAndApSerialNumbers() {
    var serialNumbers = List.of("900000006688", "900000006689");
    var expectedResult = Map.of(
        "0e2f68ab79154ffea64aa52c5cc48826", Set.of("900000006688"),
        "0e2f68ab79154ffea64aa52c5cc48827", Set.of("900000006689")
    );
    var results = apRepository.findVenueIdsByApSerialNumbers(serialNumbers,
        "b338eaa6796443829192a61093e143f9");
    var resultMap = results.stream()
        .collect(Collectors.groupingBy(
            VenueApSerialNumberProjection::venueId,
            Collectors.mapping(VenueApSerialNumberProjection::apSerialNumber, Collectors.toSet())
        ));
    assertThat(resultMap)
        .isNotEmpty()
        .hasSize(2)
        .containsExactlyInAnyOrderEntriesOf(expectedResult);
  }

  @Test
  void findApRegistrationData() {
    String serial = "900000008884";
    assertThat(apRepository.findApRegistrationData(serial, TENANT_ID))
        .isNotNull()
        .isEqualTo(new ApRegistrationProjection("900000008884", "My AP 84", "00:00:00:00:00:00",
            "R730", AP_GROUP_ID, VENUE_ID, RegistrationStateEnum.APPROVED));
  }

  @Test
  void findByRegistrationStateAndApGroupVenueIdAndTenantId() {
    assertEquals(1, apRepository.findByRegistrationStateAndApGroupVenueIdAndTenantId(
        RegistrationStateEnum.REJECTED, VENUE_ID, TENANT_ID).size());
  }

  @Test
  void findCreatedDateByTenantId() {
    List<ApCreatedDateProjection> apCreatedDateProjectionList = apRepository.findCreatedDateByTenantId(TENANT_ID);
    assertEquals(7, apCreatedDateProjectionList.size());
    assertEquals(1, apCreatedDateProjectionList.stream()
      .map(ApCreatedDateProjection::createdDate).filter(Objects::nonNull).count());
    assertEquals("900000008885", apCreatedDateProjectionList.stream()
      .filter(projection -> Objects.nonNull(projection.createdDate())).findAny().get().id());
  }

  @Test
  void findByIdAndApGroupVenueIdAndTenantIdTest() {
    assertTrue(apRepository
        .findByIdAndApGroupVenueIdAndTenantId("900000005015", VENUE_ID, TENANT_ID).isPresent());
  }

  @Test
  void findModels() {
    assertThat(apRepository.findModels(TENANT_ID))
      .hasSize(2)
      .contains("R710", "R730");
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
      INSERT INTO venue (id, tenant)
          VALUES ('e619cccb6bd74075ba0b220611969aab', '4c8279f79307415fa9e4c88a1819f0fc');
      INSERT INTO ap_group (id, tenant, venue)
          VALUES ('d888fd374a974e548af80a7ecbc74230', '4c8279f79307415fa9e4c88a1819f0fc','e619cccb6bd74075ba0b220611969aab');
      INSERT INTO ap (id, model, tenant, ap_group, soft_deleted)
          VALUES ('900000102341', 'R720', '4c8279f79307415fa9e4c88a1819f0fc','d888fd374a974e548af80a7ecbc74230', false);
      INSERT INTO ap (id, model, tenant, ap_group, soft_deleted)
          VALUES ('900000103423', 'R730', '4c8279f79307415fa9e4c88a1819f0fc','d888fd374a974e548af80a7ecbc74230', false);
      INSERT INTO venue (id, tenant)
          VALUES ('3dercccb6bd74075ba0b22061erfw34f', '4c8279f79307415fa9e4c88a1819f0fc');
      INSERT INTO ap_group (id, tenant, venue)
          VALUES ('548af80a7ecbc74230d888fd374a974e', '4c8279f79307415fa9e4c88a1819f0fc','3dercccb6bd74075ba0b22061erfw34f');
      INSERT INTO ap (id, model, tenant, ap_group, soft_deleted)
          VALUES ('900000323033', 'R710', '4c8279f79307415fa9e4c88a1819f0fc','548af80a7ecbc74230d888fd374a974e', false);
      INSERT INTO ap (id, model, tenant, ap_group, soft_deleted)
          VALUES ('900000323034', 'R710', '4c8279f79307415fa9e4c88a1819f0fc','548af80a7ecbc74230d888fd374a974e', false);
      """)
  void findModelsByTenantIdAndVenueIdAndModelsIn() {
    String tenantId = "4c8279f79307415fa9e4c88a1819f0fc";
    String venueWithR730AndR720 = "e619cccb6bd74075ba0b220611969aab";
    String venueWithTwoR710 = "3dercccb6bd74075ba0b22061erfw34f";
    assertThat(apRepository.findModelsByTenantIdAndVenueIdAndModelsIn(tenantId, venueWithR730AndR720,
        List.of("R730", "R720", "R710")))
        .hasSize(2)
        .containsExactlyInAnyOrder("R730", "R720");
    assertThat(apRepository.findModelsByTenantIdAndVenueIdAndModelsIn(tenantId, venueWithTwoR710,
        List.of("R730", "R720", "R710")))
        .hasSize(1)
        .containsExactlyInAnyOrder("R710");
  }

  @Test
  void findVenueApModelsByTenantIdAndSerialNumbersIn() {
    List<String> serialNumbers = List.of("900000008880", "900000008881", "900000008884", "900000008883");

    assertThat(apRepository.findVenueApModelsByTenantIdAndSerialNumbersIn(TENANT_ID, serialNumbers))
        .hasSize(3)
        .containsExactlyInAnyOrder(
            new VenueApModelProjection(VENUE_ID, "R730"),
            new VenueApModelProjection(VENUE_ID, "R710"),
            new VenueApModelProjection(VENUE_ID, null)
        );
  }

  @Test
  @Sql("classpath:sql/ap-compatibilities-projection.sql")
  void findByTenantIdAndApGroupIdInTest() {
    Pageable pageable = Pageable.ofSize(1);
    var apCompatibilityProjections = apRepository.findByTenantIdAndApGroupIdIn(
      TENANT_ID,
      Lists.newArrayList(AP_GROUP_ID), pageable);

    int index = 0;
    List<String> apSerials = List.of("900000005015", "900000005016");
    List<String> apFwVersions = List.of("6.2.1.103.100", "7.0.0.103.100");
    List<String> apModes = List.of("R550", "R770");
    while(!apCompatibilityProjections.isEmpty()) {
      int finalIndex = index++;
      assertThat(apCompatibilityProjections.get(0))
        .isNotNull()
        .satisfies(ap -> {
          assertThat(ap.id()).isEqualTo(apSerials.get(finalIndex));
          assertThat(ap.firmware()).isEqualTo(apFwVersions.get(finalIndex));
          assertThat(ap.model()).isEqualTo(apModes.get(finalIndex));
        });
      pageable = pageable.next();
      apCompatibilityProjections = apRepository.findByTenantIdAndApGroupIdIn(
        TENANT_ID,
        Lists.newArrayList(AP_GROUP_ID), pageable);
    }
    assertEquals(2, index);
  }

  @Sql(statements = """
    INSERT INTO tenant (id) VALUES ('cb50756b-b0be-4827-af61-ef9ac66fb698');
    INSERT INTO venue (id, tenant)
        VALUES ('800120c6-e6c4-4d64-9298-81f7b6864b95', 'cb50756b-b0be-4827-af61-ef9ac66fb698');
    INSERT INTO ap_group (id, venue, tenant)
        VALUES ('4dbe434e-c410-4d41-b1b2-1aa6ee36ca83', '800120c6-e6c4-4d64-9298-81f7b6864b95', 'cb50756b-b0be-4827-af61-ef9ac66fb698');
    INSERT INTO ap (id, name, ap_group, tenant, soft_deleted)
        VALUES ('1234', 'My AP 15', '4dbe434e-c410-4d41-b1b2-1aa6ee36ca83', 'cb50756b-b0be-4827-af61-ef9ac66fb698', false);
    INSERT INTO tenant (id) VALUES ('0261549c-db98-44a7-aada-347bb02c82a0');
    INSERT INTO venue (id, tenant)
        VALUES ('5f9a035b-903c-4f84-af53-089eb2911683', '0261549c-db98-44a7-aada-347bb02c82a0');
    INSERT INTO ap_group (id, venue, tenant)
        VALUES ('d0259999-4fb2-42c6-b807-8b609670c545', '5f9a035b-903c-4f84-af53-089eb2911683', '0261549c-db98-44a7-aada-347bb02c82a0');
    INSERT INTO ap (id, name, ap_group, tenant, soft_deleted)
        VALUES ('5678', 'My AP 16', 'd0259999-4fb2-42c6-b807-8b609670c545', '0261549c-db98-44a7-aada-347bb02c82a0', false);
    """)
  @Test
  public void testFindApCountByTenantIds() {
    List<String> tenantIds = List.of("cb50756b-b0be-4827-af61-ef9ac66fb698", "0261549c-db98-44a7-aada-347bb02c82a0");

    List<TenantApCountProjection> tenantApCounts = apRepository.findApCountByTenantIds(tenantIds);

    Assertions.assertThat(tenantApCounts)
        .hasSize(2)
        .extracting(TenantApCountProjection::apCount)
        .allSatisfy(apCount -> Assertions.assertThat(apCount)
            .isEqualTo(1)
        );
  }

  @Test
  public void existsByApGroupVenueIdAndTenantIdAndModelAndIdIsNot() {
    assertThat(apRepository.existsByApGroupVenueIdAndTenantIdAndModelAndIdIsNot(VENUE_ID, TENANT_ID, "R730",
        "900000008881")).isTrue();
    assertThat(apRepository.existsByApGroupVenueIdAndTenantIdAndModelAndIdIsNot(VENUE_ID, TENANT_ID, "R730",
        "900000008884")).isTrue();
    assertThat(apRepository.existsByApGroupVenueIdAndTenantIdAndModelAndIdIsNot(VENUE_ID, TENANT_ID, "R710",
        "900000008884")).isTrue();
    assertThat(apRepository.existsByApGroupVenueIdAndTenantIdAndModelAndIdIsNot(VENUE_ID, TENANT_ID, "R710",
        "900000008880")).isFalse();
    assertThat(apRepository.existsByApGroupVenueIdAndTenantIdAndModelAndIdIsNot(VENUE_ID, TENANT_ID, null,
        "900000008883")).isTrue();
  }

  @Sql(statements =
    """
    INSERT INTO tenant (id) VALUES ('f96dc52ec98843a1917ba254fb0ac013'), ('f96dc52ec98843a1917ba254fb0ac014'), ('f96dc52ec98843a1917ba254fb0ac015');
    INSERT INTO ap_version (id) VALUES ('6.2.2.103.123'), ('7.0.0.104.123');
    INSERT INTO venue (id, tenant, ap_version, country_code)
        VALUES ('b444254ef3b947c7b66c9a83c607396d', 'f96dc52ec98843a1917ba254fb0ac013', '6.2.2.103.123', 'DE'),
               ('b444254ef3b947c7b66c9a83c607396e', 'f96dc52ec98843a1917ba254fb0ac014', '7.0.0.104.123', 'DE'),
               ('b444254ef3b947c7b66c9a83c607396f', 'f96dc52ec98843a1917ba254fb0ac014', '7.0.0.104.123', 'US'),
               ('b444254ef3b947c7b66c9a83c607396g', 'f96dc52ec98843a1917ba254fb0ac015', '7.0.0.104.123', 'GB');
    INSERT INTO ap_group (id, venue, tenant)
        VALUES ('5ec25531259c43bc8e4f04b25bcc7258', 'b444254ef3b947c7b66c9a83c607396d', 'f96dc52ec98843a1917ba254fb0ac013'),
               ('5ec25531259c43bc8e4f04b25bcc7259', 'b444254ef3b947c7b66c9a83c607396e', 'f96dc52ec98843a1917ba254fb0ac014'),
               ('5ec25531259c43bc8e4f04b25bcc7250', 'b444254ef3b947c7b66c9a83c607396f', 'f96dc52ec98843a1917ba254fb0ac014'),
               ('5ec25531259c43bc8e4f04b25bcc7251', 'b444254ef3b947c7b66c9a83c607396g', 'f96dc52ec98843a1917ba254fb0ac015');
    INSERT INTO ap (id, name, ap_group, tenant, registration_state, soft_deleted)
        VALUES ('491803002781', 'AP-Test-1', '5ec25531259c43bc8e4f04b25bcc7258', 'f96dc52ec98843a1917ba254fb0ac013', 'APPROVED', false),
               ('491803002782', 'AP-Test-2', '5ec25531259c43bc8e4f04b25bcc7259', 'f96dc52ec98843a1917ba254fb0ac014', 'APPROVED', false),
               ('491803002783', 'AP-Test-3', '5ec25531259c43bc8e4f04b25bcc7250', 'f96dc52ec98843a1917ba254fb0ac014', 'APPROVED', false),
               ('491803002784', 'AP-Test-4', '5ec25531259c43bc8e4f04b25bcc7251', 'f96dc52ec98843a1917ba254fb0ac015', 'PENDING', false);
    """)
  @Test
  void findAllDistinctTenantIdsTest() {
    var result = apRepository.findAllDistinctTenantIdsByProblematicVenueWifiFirmwareVersionAndCountryCodes();
    assertEquals(1, result.size());
    assertEquals("f96dc52ec98843a1917ba254fb0ac013", result.get(0));
  }

  @Test
  @Sql(statements =
      """
      INSERT INTO tenant (id) VALUES ('3e973c0822ad4b598000f05add008277');
      INSERT INTO venue (id, tenant, dhcp_service_enabled, dhcp_service_mode)
          VALUES ('ad0a6cbf0a754211bee23fdc03557ced', '3e973c0822ad4b598000f05add008277', true, 'EnableOnEachAPs'),
                 ('0a9dd9fed50d4c31a3d0a3c1842c3a53', '3e973c0822ad4b598000f05add008277', true, 'EnableOnHierarchicalAPs'),
                 ('ab78816a21434ff2821cc94c656ba39f', '3e973c0822ad4b598000f05add008277', false, null);
      INSERT INTO ap_group (id, venue, tenant)
          VALUES ('b5990d4b58fe45098bb8fd369a947028', 'ad0a6cbf0a754211bee23fdc03557ced', '3e973c0822ad4b598000f05add008277'),
                 ('0691aaa09b194450b29e46ada9a47a4a', '0a9dd9fed50d4c31a3d0a3c1842c3a53', '3e973c0822ad4b598000f05add008277'),
                 ('9f5563add9cf49409ab85992e534d70f', 'ab78816a21434ff2821cc94c656ba39f', '3e973c0822ad4b598000f05add008277');
      INSERT INTO ap (id, name, mac, ap_group, tenant, soft_deleted)
          VALUES ('891803002781', 'AP-Test-1', 'F0:DA:E1:BF:56:73', 'b5990d4b58fe45098bb8fd369a947028', '3e973c0822ad4b598000f05add008277', false),
                 ('891803002782', 'AP-Test-2', '8B:0C:42:FF:7D:BD', '0691aaa09b194450b29e46ada9a47a4a', '3e973c0822ad4b598000f05add008277', false),
                 ('891803002783', 'AP-Test-3', 'B3:B3:EC:EE:DA:CA', '9f5563add9cf49409ab85992e534d70f', '3e973c0822ad4b598000f05add008277', false);
      """)
  void findByTenantIdAndSerialNumberTest11111() {
    var expectedResult =
        apRepository.findApDhcpEventProjectionByTenantIdAndSerialNumber( "3e973c0822ad4b598000f05add008277", "891803002781");
    assertThat(expectedResult.orElseThrow())
        .extracting(ApDhcpEventProjection::serialNumber)
        .isEqualTo("891803002781");

    var notFoundByUnexpectedDhcpMode =
        apRepository.findApDhcpEventProjectionByTenantIdAndSerialNumber( "3e973c0822ad4b598000f05add008277", "891803002782");
    assertFalse(notFoundByUnexpectedDhcpMode.isPresent());

    var notFoundByDisabledDhcp =
        apRepository.findApDhcpEventProjectionByTenantIdAndSerialNumber( "3e973c0822ad4b598000f05add008277", "891803002783");
    assertFalse(notFoundByDisabledDhcp.isPresent());

    var notFoundByWrongSerialNumber =
        apRepository.findApDhcpEventProjectionByTenantIdAndSerialNumber( "3e973c0822ad4b598000f05add008277", "891803002784");
    assertFalse(notFoundByWrongSerialNumber.isPresent());

    var notFoundByWrongTenant =
        apRepository.findApDhcpEventProjectionByTenantIdAndSerialNumber( "4e973c0822ad4b598000f05add008277", "891803002781");
    assertFalse(notFoundByWrongTenant.isPresent());
  }

  @Test
  void testFindByApGroupVenueIdAndTenantIdAndNameIn() {
    assertThat(apRepository.findByApGroupVenueIdAndTenantIdAndNameIn("e465bac6afb747a4987d0d0945f77221",
        "4c8279f79307415fa9e4c88a1819f0fc", List.of("My AP 15", "My AP 83")))
        .hasSize(2)
        .extracting(AbstractBaseEntity::getId)
        .contains("900000005015", "900000008883");
  }

  @Test
  @Sql(statements =
      """
      INSERT INTO tenant (id) VALUES ('8c4857502ea8436c9c3d1f52f2be90d9');
      INSERT INTO venue (id, tenant)
          VALUES ('c2a978ca231d48dbac97f4fd32836787', '8c4857502ea8436c9c3d1f52f2be90d9');
      INSERT INTO ap_group (id, venue, tenant)
          VALUES ('1de59b67f57a4684a2a1a490a1cdc8f3', 'c2a978ca231d48dbac97f4fd32836787', '8c4857502ea8436c9c3d1f52f2be90d9');
      INSERT INTO ap_snmp_agent_profile (id, tenant)
          VALUES ('818552afabf544878057e510b9bb88b5', '8c4857502ea8436c9c3d1f52f2be90d9');
      INSERT INTO ap (id, ap_group, tenant, ap_snmp_agent_profile, enable_ap_snmp)
          VALUES ('901890089028', '1de59b67f57a4684a2a1a490a1cdc8f3', '8c4857502ea8436c9c3d1f52f2be90d9', '818552afabf544878057e510b9bb88b5', true);
      INSERT INTO ap (id, ap_group, tenant, ap_snmp_agent_profile, enable_ap_snmp)
          VALUES ('901890089029', '1de59b67f57a4684a2a1a490a1cdc8f3', '8c4857502ea8436c9c3d1f52f2be90d9', '818552afabf544878057e510b9bb88b5', true);    
      """)
  void testFindVenueApSerialNumberByTenantIdAndProfileId() {
    assertThat(apRepository.findVenueApSerialNumberByTenantIdAndProfileId(
        "8c4857502ea8436c9c3d1f52f2be90d9", "818552afabf544878057e510b9bb88b5"))
        .hasSize(2)
        .extracting(
            VenueApSerialNumberProjection::venueId,
            VenueApSerialNumberProjection::apSerialNumber
        )
        .containsExactlyInAnyOrder(
            tuple("c2a978ca231d48dbac97f4fd32836787", "901890089028"),
            tuple("c2a978ca231d48dbac97f4fd32836787", "901890089029")
        );
  }
}
