package com.ruckus.cloud.wifi.requirement.feature;

import static org.apache.commons.lang3.RandomUtils.nextBoolean;

import com.ruckus.cloud.wifi.eda.servicemodel.BssColoring;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class BssColoringFeatureTest {

  @SpyBean
  private BssColoringFeature unit;

  @Nested
  class WhenIsBssColoringEnabled {
    @Test
    void givenBssColoringIsNull() {
      final var venue = new Venue();
      venue.setBssColoring(null);

      BDDAssertions.then(unit.test(venue)).isTrue();
    }

    @Nested
    class GivenBssColoringIsNotNull {

      @Test
      void thenReturnBssColorEnabled() {
        final var enabled = nextBoolean();
        final var bssColoring = new BssColoring();
        bssColoring.setBssColoringEnabled(enabled);
        final var venue = new Venue();
        venue.setBssColoring(bssColoring);

        BDDAssertions.then(unit.test(venue)).isEqualTo(enabled);
      }
    }
  }
}
