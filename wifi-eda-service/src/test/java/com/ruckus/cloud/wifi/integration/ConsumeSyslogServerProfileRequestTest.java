package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.SyslogServerProfileRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslog;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.IdAndName;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

;

@Deprecated
@Tag("SyslogServerProfileTest")
@WifiIntegrationTest
public class ConsumeSyslogServerProfileRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired private RevisionService revisionService;

  @Autowired private KafkaTopicProvider kafkaTopicProvider;

  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_SYSLOG_SERVER_PROFILE)
  class ConsumeAddSyslogServerProfileRequestTest {

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile syslogServerProfile;

    private Venue venue;

    @BeforeEach
    void givenOneVenuePersistedInDb(final Venue venue) {
      this.venue = venue;
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenUpdateProfile() {
      syslogServerProfile = Generators.syslogServerProfile().generate();
      syslogServerProfile.setId(CommonTestFixture.randomId());
      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(this.venue.getId());
      syslogServerProfile.setVenues(List.of(newVenueIdAndName));
      return syslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile syslogServerProfile) {
      // Then
      validateResult(CfgAction.ADD_SYSLOG_SERVER_PROFILE, syslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.ADD_SYSLOG_SERVER_PROFILE)
  class ConsumeAddSyslogServerProfileAndRebindVenueRequestTest {

    private SyslogServerProfile syslogServerProfile;

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile addSyslogServerProfile;

    private Venue venue;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(final Tenant tenant, final Venue venue) {
      this.venue = venue;
      this.venue.setApPassword("1qaz@WSX");
      this.venue.setCountryCode("US");

      syslogServerProfile =
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile()
                      .generate();
      syslogServerProfile.setVenues(List.of(venue));
      syslogServerProfile.setTenant(tenant);

      repositoryUtil.createOrUpdate(
              syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      venue.setSyslogServerProfile(syslogServerProfile);
      venue.setSyslog(venueSyslog);
      repositoryUtil.createOrUpdate(
              venue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenAddProfile() {
      addSyslogServerProfile = Generators.syslogServerProfile().generate();
      addSyslogServerProfile.setId(CommonTestFixture.randomId());
      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(this.venue.getId());
      addSyslogServerProfile.setVenues(List.of(newVenueIdAndName));
      return addSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
            @Payload com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile syslogServerProfile) {
      // Then
      validateResult(CfgAction.ADD_SYSLOG_SERVER_PROFILE, syslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_SYSLOG_SERVER_PROFILE)
  class ConsumeUpdateSyslogServerProfileRequestTest {

    private SyslogServerProfile syslogServerProfile;

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile updateSyslogServerProfile;

    private Venue oldVenue;
    private Venue newBindVenue;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(final Tenant tenant, final Venue oldVenue) {
      this.oldVenue = oldVenue;
      this.oldVenue.setApPassword("1qaz@WSX");
      this.oldVenue.setCountryCode("US");
      this.newBindVenue =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venue()
              .setApPassword(always("1qaz@WSX"))
              .setAddressLine(always("Somewhere"))
              .setCountryCode(always("US"))
              .setTimezone(always("America/Los_Angeles"))
              .generate();

      syslogServerProfile =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile()
              .generate();
      syslogServerProfile.setVenues(List.of(oldVenue));
      syslogServerProfile.setTenant(tenant);

      repositoryUtil.createOrUpdate(
          syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      oldVenue.setSyslogServerProfile(syslogServerProfile);
      oldVenue.setSyslog(venueSyslog);
      repositoryUtil.createOrUpdate(
          oldVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      this.newBindVenue.setTenant(tenant);
      repositoryUtil.createOrUpdate(
          this.newBindVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("syslogServerProfileId", syslogServerProfile.getId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenUpdateProfile() {
      updateSyslogServerProfile = Generators.syslogServerProfile().generate();

      updateSyslogServerProfile.setId(syslogServerProfile.getId());

      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(newBindVenue.getId());
      updateSyslogServerProfile.setVenues(List.of(newVenueIdAndName));

      return updateSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(
          CfgAction.UPDATE_SYSLOG_SERVER_PROFILE,
          syslogServerProfile.getId(),
          updateSyslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_SYSLOG_SERVER_PROFILE)
  class ConsumeUpdateSyslogServerProfileChangeVenueRequestTest {

    private SyslogServerProfile syslogServerProfile;

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile updateSyslogServerProfile;

    private Venue oldVenue;
    private Venue newBindVenue;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(final Tenant tenant, final Venue oldVenue) {
      this.oldVenue = oldVenue;
      this.oldVenue.setApPassword("1qaz@WSX");
      this.oldVenue.setCountryCode("US");
      this.newBindVenue =
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venue()
                      .setApPassword(always("1qaz@WSX"))
                      .setAddressLine(always("Somewhere"))
                      .setCountryCode(always("US"))
                      .setTimezone(always("America/Los_Angeles"))
                      .generate();

      syslogServerProfile =
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile()
                      .generate();
      syslogServerProfile.setVenues(List.of(oldVenue));
      syslogServerProfile.setTenant(tenant);

      repositoryUtil.createOrUpdate(
              syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      oldVenue.setSyslogServerProfile(syslogServerProfile);
      oldVenue.setSyslog(venueSyslog);
      repositoryUtil.createOrUpdate(
              oldVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      this.newBindVenue.setTenant(tenant);
      repositoryUtil.createOrUpdate(
              this.newBindVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
              .addPathVariable("syslogServerProfileId", syslogServerProfile.getId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenUpdateProfile() {
      updateSyslogServerProfile = SyslogServerProfileRestCtrl.SyslogServerProfileMapper.INSTANCE.ServiceSyslogServerProfile2SyslogServerProfile(syslogServerProfile);

      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(newBindVenue.getId());
      updateSyslogServerProfile.setVenues(List.of(newVenueIdAndName));

      return updateSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(
              CfgAction.UPDATE_SYSLOG_SERVER_PROFILE,
              syslogServerProfile.getId(),
              updateSyslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_SYSLOG_SERVER_PROFILE)
  class ConsumeUpdateSyslogServerProfileChangeVenueWithBoundSyslogRequestTest {

    private SyslogServerProfile syslogServerProfile1;

    private SyslogServerProfile syslogServerProfile2;

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile updateSyslogServerProfile;

    private Venue venue;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(final Tenant tenant, final Venue venue) {
      this.venue = venue;
      this.venue.setApPassword("1qaz@WSX");
      this.venue.setCountryCode("US");

      syslogServerProfile1 =
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile()
                      .generate();
      syslogServerProfile1.setVenues(List.of(venue));
      syslogServerProfile1.setTenant(tenant);

      repositoryUtil.createOrUpdate(
              syslogServerProfile1, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      syslogServerProfile2 =
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile().setName(always("SyslogServerProfile00002"))
                      .generate();
      syslogServerProfile2.setTenant(tenant);

      repositoryUtil.createOrUpdate(
              syslogServerProfile2, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      venue.setSyslogServerProfile(syslogServerProfile1);
      venue.setSyslog(venueSyslog);
      repositoryUtil.createOrUpdate(
              venue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
              .addPathVariable("syslogServerProfileId", syslogServerProfile2.getId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenUpdateProfile() {
      updateSyslogServerProfile = SyslogServerProfileRestCtrl.SyslogServerProfileMapper.INSTANCE.ServiceSyslogServerProfile2SyslogServerProfile(syslogServerProfile2);

      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(venue.getId());
      updateSyslogServerProfile.setVenues(List.of(newVenueIdAndName));

      return updateSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(
              CfgAction.UPDATE_SYSLOG_SERVER_PROFILE,
              updateSyslogServerProfile.getId(),
              updateSyslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_SYSLOG_SERVER_PROFILE)
  class ConsumeDeleteSyslogServerProfileRequestTest {

    private String syslogServerProfileId;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(final Venue venue) {
      SyslogServerProfile profile =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile()
              .generate();
      profile.setVenues(List.of(venue));
      repositoryUtil.createOrUpdate(
          profile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());
      syslogServerProfileId = profile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("syslogServerProfileId", syslogServerProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(CfgAction.DELETE_SYSLOG_SERVER_PROFILE);
    }

    private void validateResult(CfgAction apiAction) {
      assertThat(repositoryUtil.find(SyslogServerProfile.class, syslogServerProfileId)).isNull();
      validateDdccmCfgRequestMessages(null, null, null);
      validateCmnCfgCollectorMessages(apiAction, List.of(syslogServerProfileId));
      validateActivityMessages(apiAction);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_SYSLOG_SERVER_PROFILES)
  class ConsumeDeleteSyslogServerProfilesRequestTest {

    @Payload private List<String> payload;

    @BeforeEach
    void whenSendingDeleteSyslogServerProfilesCfgRequest(final Tenant tenant) {
      // Given
      final var syslogServerProfileList = syslogServerProfile().generate(10);
      syslogServerProfileList.forEach(
          syslogServerProfile ->
              repositoryUtil.createOrUpdate(syslogServerProfile, tenant.getId(), randomTxId()));

      payload =
          syslogServerProfileList.stream()
              .map(SyslogServerProfile::getId)
              .collect(Collectors.toList());
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload List<String> payload) {
      // Then
      validateResult(CfgAction.DELETE_SYSLOG_SERVER_PROFILES, payload);
    }

    private void validateResult(CfgAction apiAction, List<String> syslogServerProfileIdList) {
      assertThat(syslogServerProfileIdList)
          .extracting(
              syslogServerProfileId ->
                  repositoryUtil.find(SyslogServerProfile.class, syslogServerProfileId))
          .isNotEmpty()
          .allMatch(Objects::isNull);
      validateDdccmCfgRequestMessages(null, null, null);
      validateMultipleDeleteCmnCfgCollectorMessages(apiAction, syslogServerProfileIdList);
      validateActivityMessages(apiAction);
    }
  }

  private void validateNothingHappened(CfgAction apiAction) {
    validateRepositoryData(null, null, apiAction);
    validateDdccmCfgRequestMessages(null, null, null);
    validateCmnCfgCollectorMessages(null, null);
    validateActivityMessages(null);
  }

  private void validateResult(
      CfgAction apiAction, com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges = revisionService.changes(requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    final String syslogServerProfileId =
        txChanges.getNewEntities().stream()
            .filter(txEntity -> txEntity.getEntity() instanceof SyslogServerProfile)
            .map(TxEntity::getId)
            .filter(id -> !id.isEmpty())
            .findAny()
            .orElseGet(Assertions::fail);
    validateResult(apiAction, syslogServerProfileId, payload);
  }

  private void validateResult(
      CfgAction apiAction,
      String syslogServerProfileId,
      com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile payload) {
    validateRepositoryData(syslogServerProfileId, payload, apiAction);
    validateDdccmCfgRequestMessages(
        apiAction,
        payload.getVenues().stream().map(IdAndName::getId).collect(Collectors.toList()),
        payload);
    validateCmnCfgCollectorMessages(apiAction, List.of(syslogServerProfileId));
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(
      String syslogServerProfileId,
      com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile payload,
      CfgAction apiAction) {
    if (syslogServerProfileId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final SyslogServerProfile syslogServerProfile =
        repositoryUtil.find(SyslogServerProfile.class, syslogServerProfileId);

    if (payload == null) {
      assertThat(syslogServerProfile).isNull();
      return;
    }

    assertThat(syslogServerProfile)
        .isNotNull()
        .matches(profile -> Objects.equals(profile.getId(), syslogServerProfileId))
        .matches(profile -> Objects.equals(profile.getName(), payload.getName()))
        .matches(
            profile ->
                Objects.equals(profile.getPrimary().getServer(), payload.getPrimary().getServer()));
  }

  private void validateDdccmCfgRequestMessages(
      CfgAction apiAction,
      List<String> venueIds,
      com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile payload) {
    if (apiAction == null || venueIds == null) {
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(ddccmCfgRequestMessage.getPayload())
                    .extracting(WifiConfigRequest::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
                    .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                    .filteredOn(op -> venueIds.contains(op.getId()))
                    .hasSize(venueIds.size())
                    .allMatch(op -> op.getAction() == Action.MODIFY)
                    .allSatisfy(
                        op ->
                            assertThat(op)
                                .extracting(
                                    com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                                .matches(
                                    commonInfo ->
                                        commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                    .allSatisfy(
                        op -> {
                          assertThat(payload).isNotNull();
                          assertThat(op)
                              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                              .matches(v -> venueIds.contains(v.getId()))
                              .matches(
                                  v ->
                                      payload
                                          .getPrimary()
                                          .getServer()
                                          .equals(v.getSyslog().getAddress()));
                        }));
  }

  private void validateCmnCfgCollectorMessages(
      CfgAction apiAction, List<String> syslogServerProfileIdList) {
    if (apiAction == null || syslogServerProfileIdList == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(cmnCfgCollectorMessage.getPayload())
                    .matches(msg -> msg.getTenantId().equals(tenantId))
                    .matches(msg -> msg.getRequestId().equals(requestId))
                    .extracting(ViewmodelCollector::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
                    .filteredOn(
                        op ->
                            EsConstants.Index.SYSLOG_SERVER_PROFILE_INDEX_NAME.equals(
                                op.getIndex()))
                    .filteredOn(op -> syslogServerProfileIdList.contains(op.getId()))
                    .filteredOn(op -> op.getOpType() == opType(apiAction))
                    .hasSize(syslogServerProfileIdList.size())
                    .singleElement()
                    .satisfies(
                        op -> {
                          if (op.getOpType() != OpType.DEL) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(
                                    doc ->
                                        syslogServerProfileIdList.contains(
                                            doc.get(EsConstants.Key.ID).getStringValue()))
                                .matches(
                                    doc ->
                                        tenantId.equals(
                                            doc.get(EsConstants.Key.TENANT_ID).getStringValue()));
                          }
                        }));
  }

  private void validateMultipleDeleteCmnCfgCollectorMessages(
      CfgAction apiAction, List<String> syslogServerProfileIdList) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(cmnCfgCollectorMessage.getPayload())
                    .matches(msg -> msg.getTenantId().equals(tenantId))
                    .matches(msg -> msg.getRequestId().equals(requestId))
                    .extracting(ViewmodelCollector::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
                    .filteredOn(
                        op ->
                            EsConstants.Index.SYSLOG_SERVER_PROFILE_INDEX_NAME.equals(
                                op.getIndex()))
                    .filteredOn(op -> syslogServerProfileIdList.contains(op.getId()))
                    .filteredOn(op -> op.getOpType() == opType(apiAction))
                    .hasSize(syslogServerProfileIdList.size())
                    .allSatisfy(
                        op -> {
                          if (op.getOpType() != OpType.DEL) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(
                                    doc ->
                                        syslogServerProfileIdList.contains(
                                            doc.get(EsConstants.Key.ID).getStringValue()));
                          }
                        }));
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);;
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(
                        activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(CfgStatus.ConfigurationStatus.Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(CfgStatus.ConfigurationStatus::getEventDate)
                    .isNotNull());

    final var activityImpactedMessage =
        messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId);
    assertThat(activityImpactedMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(activityImpactedMessage.getPayload())
                    .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
                    .matches(msg -> msg.getDeviceIdsCount() == 0)
                    .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList)
                    .asList()
                    .isEmpty());
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_SYSLOG_SERVER_PROFILE -> OpType.ADD;
      case UPDATE_SYSLOG_SERVER_PROFILE -> OpType.MOD;
      case DELETE_SYSLOG_SERVER_PROFILE, DELETE_SYSLOG_SERVER_PROFILES -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_SYSLOG_SERVER_PROFILE -> ApiFlowNames.ADD_SYSLOG_SERVER_PROFILE;
      case UPDATE_SYSLOG_SERVER_PROFILE -> ApiFlowNames.UPDATE_SYSLOG_SERVER_PROFILE;
      case DELETE_SYSLOG_SERVER_PROFILE -> ApiFlowNames.DELETE_SYSLOG_SERVER_PROFILE;
      case DELETE_SYSLOG_SERVER_PROFILES -> ApiFlowNames.DELETE_SYSLOG_SERVER_PROFILES;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
