package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.handler.asyncjob.FirmwareBindingAsyncJobHelper.FIRMWARE_BINDING_JOB_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.context.WifiAsyncJobContext;
import com.ruckus.cloud.wifi.kafka.publisher.WifiAsyncJobPublisher;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantAvailableApFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@WifiIntegrationTest
@FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
    FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
public class ConsumeReleaseFirmwareRequestTest extends AbstractRequestTest {
  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  private MessageCaptors messageCaptors;

  @Value("${topic.nuvo.notificationRequests}")
  private String notificationRequests;

  @Test
  public void asyncReleaseFirmwareWithoutScheduleGreenfieldOff(Tenant adminTenant) {
    //Given
    ApVersion defaultVersion = ApVersionTestFixture.recommendedApVersion("6.2.0.106.500",
        a -> a.setSupportedApModels(List.of("R500", "R550")));
    ApVersion targetVersion = ApVersionTestFixture.recommendedApVersion("6.2.1.106.500",
        a -> a.setSupportedApModels(List.of("R550", "R560")));
    repositoryUtil.createOrUpdate(defaultVersion, adminTenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(targetVersion, adminTenant.getId(), randomTxId());
    String requestId = randomTxId();
    String userName = randomName();
    List<Tenant> tenantList = List.of(
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));
    tenantList.forEach(tenant -> repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId()));
    List<String> tenantIdList = tenantList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());

    var params = new RequestParams();
    params.addPathVariable("firmwareVersion", targetVersion.getId());
    params.addRequestParam("schedule", false);

    // When
    messageUtil.sendWifiCfgRequest(
        adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, userName, params, tenantIdList);

    // Waiting for async jobs finished
    tenantList.forEach(tenant -> {
      assertThat(messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId()))
          .isNotNull();
      messageUtil.awaitMessageProceed(kafkaTopicProvider.getWifiAsyncJob(),
          WifiAsyncJobPublisher.getPartitionKey(WifiAsyncJobContext.builder()
              .jobName(FIRMWARE_BINDING_JOB_NAME)
              .tenantId(tenant.getId())
              .build()));
    });

    // Then
    validateResult(tenantList, targetVersion);
    Predicate<ConsumerRecord<String, byte[]>> predict = msg -> msg.value() != null;
    assertThat(getKafkaMsgs(notificationRequests, predict, Duration.ofSeconds(3), tenantList.size())).hasSize(tenantList.size());
  }

  @Test
  public void asyncReleaseFirmwareWithSchedule(Tenant adminTenant) {
    //Given
    ApVersion oldVersion = ApVersionTestFixture.recommendedApVersion("6.2.0.103.495",
        a -> a.setSupportedApModels(List.of("R500", "R510", "R550")));
    ApVersion defaultVersion = ApVersionTestFixture.recommendedApVersion("6.2.3.103.500",
        a -> a.setSupportedApModels(List.of("R550", "R650")));
    ApVersion targetVersion = ApVersionTestFixture.recommendedApVersion("6.2.3.103.800",
        a -> a.setSupportedApModels(List.of("R550", "R650")));
    repositoryUtil.createOrUpdate(oldVersion, adminTenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(defaultVersion, adminTenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(targetVersion, adminTenant.getId(), randomTxId());
    String requestId = randomTxId();
    String userName = randomName();
    List<Tenant> tenantList = List.of(
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));
    tenantList.forEach(tenant -> {
      repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(
          TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant, oldVersion),
          tenant.getId(),
          randomTxId());
      repositoryUtil.createOrUpdate(
          TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant, defaultVersion),
          tenant.getId(),
          randomTxId());
    });
    List<String> tenantIdList = tenantList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());

    var params = new RequestParams();
    params.addPathVariable("firmwareVersion", targetVersion.getId());
    params.addRequestParam("schedule", true);

    // When
    messageUtil.sendWifiCfgRequest(
        adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, userName, params, tenantIdList);

    // Waiting for async jobs finished
    waitingForAsyncJobsFinished(tenantList);

    // Then
    validateResult(tenantList, targetVersion);
    validateTenantCurrentFirmwares(tenantList,
        tuple("R500", oldVersion.getId()),
        tuple("R510", oldVersion.getId()),
        tuple("R550", targetVersion.getId()),
        tuple("R650", targetVersion.getId()));
    // TODO Failed randomly.
    assertThat(getScheduleCreateKafkaMsgs(requestId, tenantList.size())).hasSize(tenantList.size());
    messageCaptors.assertThat(
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getWifiScheduleCreate()
    ).doesNotSendByTenants(tenantList.stream().map(AbstractBaseEntity::getId)
        .collect(Collectors.toSet()));
  }

  @Nested
  @FeatureFlag(enable = FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE)
  class GivenGreenfieldByApModelToggleEnabled {

    @Test
    public void asyncReleaseFirmwareWithoutSchedule(Tenant adminTenant) {
      //Given
      ApVersion defaultVersion = ApVersionTestFixture.recommendedApVersion("6.2.0.105.500",
          a -> a.setSupportedApModels(List.of("R500", "R550")));
      ApVersion targetVersion = ApVersionTestFixture.recommendedApVersion("6.2.1.105.500",
          a -> a.setSupportedApModels(List.of("R550", "R560")));
      repositoryUtil.createOrUpdate(defaultVersion, adminTenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(targetVersion, adminTenant.getId(), randomTxId());
      String requestId = randomTxId();
      String userName = randomName();
      List<Tenant> tenantList = List.of(
          TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
          TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));
      tenantList.forEach(tenant -> repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId()));
      List<String> tenantIdList = tenantList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());

      var params = new RequestParams();
      params.addPathVariable("firmwareVersion", targetVersion.getId());
      params.addRequestParam("schedule", false);

      // When
      messageUtil.sendWifiCfgRequest(
          adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, userName, params, tenantIdList);

      // Waiting for async jobs finished
      waitingForAsyncJobsFinished(tenantList);

      // Then
      validateResult(tenantList, targetVersion);
      validateTenantCurrentFirmwares(tenantList,
          tuple("R550", targetVersion.getId()),
          tuple("R560", targetVersion.getId()));
      Predicate<ConsumerRecord<String, byte[]>> predict = msg -> msg.value() != null;
      assertThat(getKafkaMsgs(notificationRequests, predict, Duration.ofSeconds(3), tenantList.size()))
          .hasSize(tenantList.size());
    }

    @Test
    public void asyncReleaseFirmwareWithSchedule(Tenant adminTenant) {
      //Given
      ApVersion defaultVersion = ApVersionTestFixture.recommendedApVersion("6.2.0.103.500",
          a -> a.setSupportedApModels(List.of("R500", "R550")));
      ApVersion targetVersion = ApVersionTestFixture.recommendedApVersion("6.2.1.103.500",
          a -> a.setSupportedApModels(List.of("R550", "R560")));
      repositoryUtil.createOrUpdate(defaultVersion, adminTenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(targetVersion, adminTenant.getId(), randomTxId());
      String requestId = randomTxId();
      String userName = randomName();
      List<Tenant> tenantList = List.of(
          TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
          TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));
      tenantList.forEach(tenant -> repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId()));
      List<String> tenantIdList = tenantList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());

      var params = new RequestParams();
      params.addPathVariable("firmwareVersion", targetVersion.getId());
      params.addRequestParam("schedule", true);

      // When
      messageUtil.sendWifiCfgRequest(
          adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, userName, params, tenantIdList);

      // Waiting for async jobs finished
      waitingForAsyncJobsFinished(tenantList);

      // Then
      validateResult(tenantList, targetVersion);
      validateTenantCurrentFirmwares(tenantList,
          tuple("R550", targetVersion.getId()),
          tuple("R560", targetVersion.getId()));
      assertThat(getScheduleCreateKafkaMsgs(requestId, tenantList.size())).hasSize(tenantList.size());
      messageCaptors.assertThat(
          kafkaTopicProvider.getDdccmCfgRequest(),
          kafkaTopicProvider.getWifiScheduleCreate()
      ).doesNotSendByTenants(tenantList.stream().map(AbstractBaseEntity::getId)
          .collect(Collectors.toSet()));
    }

    @Test
    public void asyncReleaseFirmwareWithSchedule_whenTenantHasVenue(Tenant adminTenant) {
      //Given
      ApVersion defaultVersion = ApVersionTestFixture.recommendedApVersion("6.2.0.103.500",
          a -> a.setSupportedApModels(List.of("R500", "R550")));
      ApVersion targetVersion = ApVersionTestFixture.recommendedApVersion("6.2.1.103.500",
          a -> a.setSupportedApModels(List.of("R550", "R560")));
      repositoryUtil.createOrUpdate(defaultVersion, adminTenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(targetVersion, adminTenant.getId(), randomTxId());
      String requestId = randomTxId();
      String userName = randomName();
      Tenant tenant = TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion));
      repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId());
      Venue venue = createVenue(tenant, "venueA", defaultVersion);
      ApGroup apGroup = createApGroup(venue, "default");
      Ap ap = createAp(apGroup, randomSerialNumber(), "R550", randomMacAddress());

      VenueCurrentFirmware vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(ap.getApGroup().getVenue(),
          defaultVersion, ap.getModel());
      repositoryUtil.createOrUpdate(vcf, tenant.getId(), randomTxId());

      var params = new RequestParams();
      params.addPathVariable("firmwareVersion", targetVersion.getId());
      params.addRequestParam("schedule", true);

      // When
      messageUtil.sendWifiCfgRequest(
          adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, userName, params,
          List.of(tenant.getId()));

      // Waiting for async jobs finished
      waitingForAsyncJobsFinished(List.of(tenant));

      // Then
      validateResult(List.of(tenant), targetVersion);
      validateTenantCurrentFirmwares(List.of(tenant),
          tuple("R550", targetVersion.getId()),
          tuple("R560", targetVersion.getId()));
      assertThat(getScheduleCreateKafkaMsgs(requestId, 1)).hasSize(1);
      messageCaptors.assertThat(
          kafkaTopicProvider.getDdccmCfgRequest(),
          kafkaTopicProvider.getWifiScheduleCreate()
      ).doesNotSendByTenants(Set.of(tenant.getId()));
      final var cmnCfgCollectorMsg = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId());
      validateCmnCfgCollectorMsg(cmnCfgCollectorMsg, venue.getId(), List.of(vcf), false);
    }
  }

  private void waitingForAsyncJobsFinished(List<Tenant> tenantList) {
    // Waiting for async jobs finished
    tenantList.forEach(tenant -> {
      assertThat(messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId()))
          .isNotNull();
      messageUtil.awaitMessageProceed(kafkaTopicProvider.getWifiAsyncJob(),
          WifiAsyncJobPublisher.getPartitionKey(WifiAsyncJobContext.builder()
              .jobName(FIRMWARE_BINDING_JOB_NAME)
              .tenantId(tenant.getId())
              .build()));
    });
  }

  private List<ConsumerRecord<String, byte[]>> getScheduleCreateKafkaMsgs(String requestId, int count) {
    return this.getScheduleCreateKafkaMsgs(requestId, Duration.ofSeconds(3), count);
  }

  private List<ConsumerRecord<String, byte[]>> getScheduleCreateKafkaMsgs(String requestId, Duration timeout,
      int count) {
    Predicate<ConsumerRecord<String, byte[]>> validateRequest = request -> {
      if (request.value() != null) {
        String requestIdInMsg = request.key().split("@@")[0];
        return requestId.equals(requestIdInMsg);
      }
      return false;
    };
    return this.getKafkaMsgs(kafkaTopicProvider.getWifiScheduleCreate(), validateRequest, timeout, count);
  }

  private List<ConsumerRecord<String, byte[]>> getKafkaMsgs(String topic,
      Predicate<ConsumerRecord<String, byte[]>> predicate,
      Duration timeout,
      int count) {
    List<ConsumerRecord<String, byte[]>> res = new ArrayList<>();
    var request = messageUtil.receive(topic, timeout);
    while (request != null) {
      if (predicate.test(request)) {
        res.add(request);
        count--;
      }
      if (count == 0) {
        break;
      }
      request = messageUtil.receive(topic, timeout);
    }
    return res;
  }

  void validateResult(List<Tenant> tenants, ApVersion targetVersion) {
    tenants.forEach((tenant) -> {
      Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
      assertThat(updatedTenant)
          .isNotNull()
          .matches(t -> Objects.equals(tenant.getId(), t.getId()))
          .matches(t -> Objects.equals(targetVersion.getId(), t.getLatestReleaseVersion().getId()));
      TenantAvailableApFirmware updatedTaaf = repositoryUtil.find(TenantAvailableApFirmware.class,tenant.getId() + targetVersion.getId());
      assertThat(updatedTaaf)
          .isNotNull()
          .matches(taaf -> Objects.equals(targetVersion.getId(), taaf.getApVersion().getId()));
    });
  }

  void validateTenantCurrentFirmwares(List<Tenant> tenants, Tuple ...modelVersionTuples) {
    tenants.forEach((tenant) -> {
      List<TenantCurrentFirmware> tcfs = repositoryUtil.findAll(TenantCurrentFirmware.class, tenant.getId());
      assertThat(tcfs)
          .hasSize(modelVersionTuples.length)
          .extracting(TenantCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
          .containsExactlyInAnyOrder(modelVersionTuples);
    });
  }

  private void validateCmnCfgCollectorMsg(KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage,
      String venueId, List<VenueCurrentFirmware> vcfList, boolean isApUpToUpdate) {
    Tuple[] vcfTuples = vcfList.stream().map(vcf -> new Tuple(vcf.getApModel(), vcf.getFirmware().getId()))
        .toArray(Tuple[]::new);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> Index.VENUE.equals(op.getIndex()) && op.getOpType() == OpType.MOD)
        .filteredOn(
            op -> venueId.equals(op.getId()) && op.getDocMap().containsKey(EsConstants.Key.CURRENT_AP_FIRMWARES))
        .isNotEmpty().singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(docMap -> {
          assertThat(docMap.get(EsConstants.Key.CURRENT_AP_FIRMWARES).getListValue().getValuesList())
              .extracting(c -> c.getStructValue().getFieldsMap())
              .extracting(map -> map.get(Key.AP_MODEL).getStringValue(), map -> map.get(Key.FIRMWARE).getStringValue())
              .containsExactlyInAnyOrder(vcfTuples);
          assertThat(docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE).getBoolValue()).isEqualTo(isApUpToUpdate);
          assertThat(docMap.get(Key.LAST_AP_FIRMWARE_UPDATE).getStringValue()).isEmpty();
        });
  }
}