package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_RADIUS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_RADIUS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_RADIUS;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK_VENUE_MAPPING;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTimeSlot;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.guestWISPrNetwork;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.wisprPageOtherProvider;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusNetworkDataQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusServer;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MacAuthMacFormatEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SortOrderEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.repository.RadiusRepository;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.function.Predicate;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Tag("RadiusServerProfileTest")
@WifiIntegrationTest
public class ConsumeRadiusServerProfileV2RequestTest extends AbstractRadiusRequestTest {

  @Autowired
  private RadiusRepository radiusRepository;

  private void assertRadiusServers(Radius expectedRadius, Radius actualRadius) {
    assertRadiusCommon(expectedRadius, actualRadius);
    assertFalse(actualRadius.getIsTemplate());
  }

  @SneakyThrows
  private RadiusNetworkDataQueryResponse assertRadiusNetworksWithSort(Radius radius,
      int radiusNetworksCount, String sortField, SortOrderEnum order) {
    QueryRequest queryParams = new QueryRequest();
    queryParams.setPage(1);
    queryParams.setPageSize(100);
    queryParams.setSortField(sortField);
    queryParams.setSortOrder(order);
    RadiusNetworkDataQueryResponse result = radiusServiceCtrl.getRadiusNetworks(
        radius.getId(), queryParams);

    assertEquals(radiusNetworksCount, result.getTotalCount().intValue());
    assertEquals(radiusNetworksCount, result.getData().size());

    return result;
  }

  private static void assertDdccmOps(List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private static void assertDdccmOpCount(List<Operation> operations, Action action, int expectCount,
      Predicate<? super Operation> predicate) {
    assertEquals(expectCount, operations.stream().filter(o -> o.getAction().equals(action)).filter(predicate).count());
  }

  @Test
  public void testAddRadiusServerProfile(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // normal case
    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-auth");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    String radius1Id = radius1.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);

    Radius authRadiusCreated = radiusServiceCtrl.getRadiusServerProfile(radius1Id);
    assertNotNull(authRadiusCreated);

    assertRadiusServers(radius1, authRadiusCreated);
    assertRadiusNetworks(authRadiusCreated, 0);

    //////////
    // no type -> fail
    Radius radius2 = RadiusTestFixture.acctRadius();
    radius2.setName("test-radius-acct");
    radius2.setType(null);
    String radius2Id = radius2.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius2)); // expect to fail
    assertActivityStatusFail(ADD_RADIUS, Errors.WIFI_10464, tenantId);

    assertThrows(ObjectNotFoundException.class, () -> {
      radiusServiceCtrl.getRadiusServerProfile(radius2Id);
    });

    var viewmodelOperations1 = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewmodelOperations1, 1),
        () -> assertRadiusIndexViewmodel(viewmodelOperations1, OpType.ADD, radius1, 0)
    );

    //////////
    // name conflict
    Radius radius3 = RadiusTestFixture.authRadius();
    radius3.setName("test-radius-auth"); // name conflict
    radius3.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    String radius3Id = radius3.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius3)); // expect to fail
    assertActivityStatusFail(ADD_RADIUS, Errors.WIFI_10465, tenantId);

    assertThrows(ObjectNotFoundException.class, () -> {
      radiusServiceCtrl.getRadiusServerProfile(radius3Id);
    });

    //////////
    // new name
    radius3.setName("a-new-name");
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius3));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);
    Radius authRadius3Created = radiusServiceCtrl.getRadiusServerProfile(radius3Id);
    assertNotNull(authRadius3Created);

    assertRadiusNetworks(authRadius3Created, 0);

    var viewmodelOperations2 = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewmodelOperations2, 1),
        () -> assertRadiusIndexViewmodel(viewmodelOperations2, OpType.ADD, authRadius3Created, 0)
    );

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());
  }


  @Test
  public void testRadiusNetworks(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-2");
    radius2.setType(RadiusProfileTypeEnum.ACCOUNTING);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius1));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius2));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());

    Radius radiusAdded2 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort());

    assertRadiusNetworks(radiusAdded1, 0);
    assertRadiusNetworks(radiusAdded2, 0);

    //////////
    // add AAANetwork with auth + acct
    AAANetwork aaaNetwork = NetworkTestFixture.randomAAANetwork(tenant, n -> {
      n.setId(randomId());
      n.setAuthRadius(radiusAdded1);
      n.setAccountingRadius(radiusAdded2);
    });

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, map(aaaNetwork));
    assertActivityStatusSuccess(ADD_NETWORK, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    assertRadiusNetworks(radiusAdded1, 1);
    assertRadiusNetworks(radiusAdded2, 1);

    //////////
    // add GuestNetwork-WISPr
    GuestNetwork guestNetwork = guestWISPrNetwork(n -> n.getGuestPortal()
        .setWisprPage(wisprPageOtherProvider(map(radiusAdded1), map(radiusAdded2))));

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, guestNetwork);
    assertActivityStatusSuccess(ADD_NETWORK, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);
    receiveDdccmOperations(1, tenantId);

    assertRadiusNetworks(radiusAdded1, 2);
    assertRadiusNetworks(radiusAdded2, 2);

    RadiusNetworkDataQueryResponse radiusNetworkData = assertRadiusNetworksWithSort(radiusAdded1, 2,
        NETWORK_ID, SortOrderEnum.ASC);

    radiusNetworkData.getData().stream().forEach(d -> {
      log.info("radiusNetworkData, NETWORK_ID+ASC: {}/{}/{}", d.getNetworkId(), d.getNetworkName(), d.getNetworkType());
    });

    radiusNetworkData = assertRadiusNetworksWithSort(radiusAdded1, 2, NETWORK_TYPE, SortOrderEnum.ASC);
    radiusNetworkData.getData().stream().forEach(d -> {
      log.info("radiusNetworkData, NETWORK_TYPE+ASC: {}/{}/{}", d.getNetworkId(), d.getNetworkName(), d.getNetworkType());
    });
    assertEquals(aaaNetwork.getId(), radiusNetworkData.getData().get(0).getNetworkId());

    radiusNetworkData = assertRadiusNetworksWithSort(radiusAdded1, 2, NETWORK_TYPE, SortOrderEnum.DESC);
    radiusNetworkData.getData().stream().forEach(d -> {
      log.info("radiusNetworkData, NETWORK_TYPE+DESC: {}/{}/{}", d.getNetworkId(), d.getNetworkName(), d.getNetworkType());
    });
    assertEquals(guestNetwork.getId(), radiusNetworkData.getData().get(0).getNetworkId());
  }

  private static com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler dummyNetworkVenueScheduler() {
    final var scheduler = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler();
    scheduler.setType(SchedulerTypeEnum.CUSTOM);
    scheduler.setMon(randomTimeSlot());
    scheduler.setTue(randomTimeSlot());
    scheduler.setWed(randomTimeSlot());
    scheduler.setThu(randomTimeSlot());
    scheduler.setFri(randomTimeSlot());
    scheduler.setSat(randomTimeSlot());
    scheduler.setSun(randomTimeSlot());
    return scheduler;
  }

  private NetworkVenue networkVenue(String networkId, String venueId) {
    final var payload = new NetworkVenue();
    payload.setId(randomId()); // autoGenerated is true in wifi-api
    payload.setNetworkId(networkId);
    payload.setVenueId(venueId);
    payload.setIsAllApGroups(true);
    payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
    payload.setScheduler(dummyNetworkVenueScheduler());
    return payload;
  }

  @Test
  public void testUpdateRadiusServerProfile(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-2");
    radius2.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius1));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, mapToRadiusServerProfile(radius2));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    Radius radiusAdded2 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort());
    assertRadiusServers(radius2, radiusAdded2);
    assertRadiusNetworks(radiusAdded2, 0);

    //////////
    // update type -> success
    radiusAdded2.setType(RadiusProfileTypeEnum.ACCOUNTING);
    RequestParams rps = new RequestParams().addPathVariable(RADIUS_ID, radiusAdded2.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName, rps, mapToRadiusServerProfile(radiusAdded2));
    assertActivityStatusSuccess(UPDATE_RADIUS, tenantId);

    var viewOps1 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps1, 1),
        () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, radiusAdded2, 0)
    );

    //////////
    // update name -> name conflict
    radiusAdded2.setName(radius1.getName());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName, rps, mapToRadiusServerProfile(radiusAdded2));
    assertActivityStatusFail(UPDATE_RADIUS, Errors.WIFI_10465, tenantId);

    //////////
    // update primary -> primary conflict
    radiusAdded2.setName(radius2.getName());
    radiusAdded2.setType(radius1.getType());
    radiusAdded2.setPrimary(radius1.getPrimary());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName, rps, mapToRadiusServerProfile(radiusAdded2));
    assertActivityStatusFail(UPDATE_RADIUS, Errors.WIFI_10466, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getCmnCfgCollectorCfgRequest(), kafkaTopicProvider.getDdccmCfgRequest());

    // reset
    radiusAdded2.setPrimary(radius2.getPrimary());

    //////////
    // add AAANetwork with auth + acct
    AAANetwork aaaNetwork = NetworkTestFixture.randomAAANetwork(tenant, n -> {
      n.setId(randomId());
      n.setAuthRadius(radiusAdded1);
      n.setAccountingRadius(radiusAdded2);
    });

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, map(aaaNetwork));
    assertActivityStatusSuccess(ADD_NETWORK, tenantId);

    assertRadiusNetworks(radiusAdded1, 1);
    assertRadiusNetworks(radiusAdded2, 1);

    var viewOps2 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps2 {}", viewOps2);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps2, 3),
        () -> assertTrue(viewOps2.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded1, 1),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded2, 1)
    );

    // activate Network to Venue
    Venue v1 = createVenue(tenant, "v1");
    NetworkVenue nv1 = networkVenue(aaaNetwork.getId(), v1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE, userName, nv1);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    var viewOps3 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps3 {}", viewOps3);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps3, 2) // networkvenuemapping
    );

    // venueRadius, venueRadiusForAccounting, venueSchedule, wlanVenue
    var ddccmOps1 = receiveDdccmOperations(1, tenantId);
    log.info("ddccmOps1 {}", ddccmOps1);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps1, 4),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 1, Operation::hasVenueRadius),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 1, Operation::hasVenueRadiusForAccounting)
    );

    //////////
    // update type when it's in use
    radiusAdded2.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName, rps, mapToRadiusServerProfile(radiusAdded2));
    assertActivityStatusFail(UPDATE_RADIUS, Errors.WIFI_10467, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getCmnCfgCollectorCfgRequest(), kafkaTopicProvider.getDdccmCfgRequest());

    // reset
    radiusAdded2.setType(RadiusProfileTypeEnum.ACCOUNTING);

    //////////
    // add GuestNetwork-WISPr
    GuestNetwork guestNetwork = guestWISPrNetwork(n -> n.getGuestPortal()
        .setWisprPage(wisprPageOtherProvider(map(radiusAdded1), map(radiusAdded2))));

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, guestNetwork);
    assertActivityStatusSuccess(ADD_NETWORK, tenantId);

    assertRadiusNetworks(radiusAdded1, 2);
    assertRadiusNetworks(radiusAdded2, 2);

    var viewOps4 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps4 {}", viewOps4);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps4, 3), // 1 network, 2 radius
        () -> assertRadiusIndexViewmodel(viewOps4, OpType.MOD, radiusAdded1, 2),
        () -> assertRadiusIndexViewmodel(viewOps4, OpType.MOD, radiusAdded2, 2)
    );

    // WISPr Network is proxy-only
    // 2 ADD: radiusAuthenticationService, radiusAccountingService
    // 2 MOD: venueRadius, venueRadiusForAccounting
    var ddccmOps2 = receiveDdccmOperations(1, tenantId);
    log.info("ddccmOps2 {}", ddccmOps2);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps2, 4),
        () -> assertDdccmOpCount(ddccmOps2, Action.MODIFY, 1, Operation::hasVenueRadius),
        () -> assertDdccmOpCount(ddccmOps2, Action.MODIFY, 1, Operation::hasVenueRadiusForAccounting),
        () -> assertDdccmOpCount(ddccmOps2, Action.ADD, 1, Operation::hasRadiusAuthenticationService),
        () -> assertDdccmOpCount(ddccmOps2, Action.ADD, 1, Operation::hasRadiusAccountingService)
    );

    // activate Network
    NetworkVenue nv2 = networkVenue(guestNetwork.getId(), v1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE, userName, nv2);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    var viewOps5 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps5 {}", viewOps5);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps5, 2) // networkvenuemapping
    );

    // venueSchedule, venuePortal, wlanVenue
    // TODO: in wifi-consumer, there are also venueDnsProxy and wlanApGroup, but not in wifi-eda?
    var ddccmOps3 = receiveDdccmOperations(1, tenantId);
    log.info("ddccmOps3 {}", ddccmOps3);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps3, 3),
        () -> assertDdccmOpCount(ddccmOps3, Action.ADD, 1, Operation::hasVenueSchedule),
        () -> assertDdccmOpCount(ddccmOps3, Action.ADD, 1, Operation::hasVenuePortal),
        () -> assertDdccmOpCount(ddccmOps3, Action.ADD, 1, Operation::hasWlanVenue)
    );

    //////////
    // update radiusAdded1 -> impact both networks
    radiusAdded2.getPrimary().setIp("*******");
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName, rps, mapToRadiusServerProfile(radiusAdded2));
    assertActivityStatusSuccess(UPDATE_RADIUS, tenantId);

    var viewOps6 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps6 {}", viewOps6);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps6, 1),
        () -> assertRadiusIndexViewmodel(viewOps4, OpType.MOD, radiusAdded2, 2)
    );

    // 2 MOD radiusAccountingService, venueRadiusForAccounting
    var ddccmOps4 = receiveDdccmOperations(1, tenantId);
    log.info("ddccmOps4 {}", ddccmOps4);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps4, 2),
        () -> assertDdccmOpCount(ddccmOps4, Action.MODIFY, 1, Operation::hasRadiusAccountingService),
        () -> assertDdccmOpCount(ddccmOps4, Action.MODIFY, 1, Operation::hasVenueRadiusForAccounting)
    );
  }

  @Test
  public void testUpdateRadiusServerProfileWithSecondaryAndRemove(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius = RadiusTestFixture.authRadius();
    radius.setName("test-radius-1");
    radius.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    RadiusServer secondaryServer = radius.getSecondary();
    radius.setSecondary(null);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius));
    assertActivityStatusSuccess(ADD_RADIUS);

    Radius addedRadius = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(),  radius.getPrimary().getIp(), radius.getPrimary().getPort());
    assertRadiusServers(radius, addedRadius);
    assertRadiusNetworks(addedRadius, 0);

    var viewOps1 = receiveViewmodelCollectorOperations(1);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps1, 1),
        () -> assertRadiusIndexViewmodel(viewOps1, OpType.ADD, radius, 0)
    );

    // update with secondary server
    addedRadius.setSecondary(secondaryServer);

    RequestParams rps = new RequestParams().addPathVariable(RADIUS_ID, addedRadius.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName, rps, mapToRadiusServerProfile(addedRadius));
    assertActivityStatusSuccess(UPDATE_RADIUS);

    Radius updatedRadius1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), addedRadius.getPrimary().getIp(), addedRadius.getPrimary().getPort());
    assertRadiusServers(addedRadius, updatedRadius1);
    assertRadiusNetworks(updatedRadius1, 0);

    var viewOps2 = receiveViewmodelCollectorOperations(1);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps2, 1),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radius, 0),
        () -> assertEquals(
            updatedRadius1.getPrimary().getIp() + ":" + updatedRadius1.getPrimary().getPort(),
            viewOps2.get(0).getDocMap().get(Key.PRIMARY).getStringValue()),
        () -> assertEquals(
            updatedRadius1.getSecondary().getIp() + ":" + updatedRadius1.getSecondary().getPort(),
            viewOps2.get(0).getDocMap().get(Key.SECONDARY).getStringValue())
    );

    // remove secondary
    addedRadius.setSecondary(null);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName, rps, mapToRadiusServerProfile(addedRadius));
    assertActivityStatusSuccess(UPDATE_RADIUS);

    Radius updatedRadius2 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), addedRadius.getPrimary().getIp(), addedRadius.getPrimary().getPort());
    assertRadiusServers(addedRadius, updatedRadius2);
    assertRadiusNetworks(updatedRadius2, 0);
    assertNull(updatedRadius2.getSecondary());

    var viewOps3 = receiveViewmodelCollectorOperations(1);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps3, 1),
        () -> assertRadiusIndexViewmodel(viewOps3, OpType.MOD, radius, 0),
        () -> assertEquals(
            updatedRadius2.getPrimary().getIp() + ":" + updatedRadius2.getPrimary().getPort(),
            viewOps3.get(0).getDocMap().get(Key.PRIMARY).getStringValue()),
        () -> assertEquals("", viewOps3.get(0).getDocMap().get(Key.SECONDARY).getStringValue())
    );
  }

  @Test
  public void testNegativeUpdateRadiusServerProfileAsTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius1));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    //////////
    // update type -> success
    radiusAdded1.setType(RadiusProfileTypeEnum.ACCOUNTING);
    radiusAdded1.setIsTemplate(true); // isTemplate should not be changed
    RequestParams rps = new RequestParams().addPathVariable(RADIUS_ID, radiusAdded1.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName, rps,
        mapToRadiusServerProfile(radiusAdded1));
    assertActivityStatusSuccess(UPDATE_RADIUS, tenantId);


    Radius authRadiusUpdated = radiusServiceCtrl.getRadiusServerProfile(radiusAdded1.getId());
    assertRadiusServers(radiusAdded1, authRadiusUpdated);
    assertRadiusNetworks(authRadiusUpdated, 0);
    assertFalse(authRadiusUpdated.getIsTemplate());

    var viewOps1 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps1, 1),
        () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, authRadiusUpdated, 0)
    );

  }

  @Test
  public void testDeleteRadiusServerProfile(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    //////////
    // 1 setup
    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-auth");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-acct");
    radius2.setType(RadiusProfileTypeEnum.ACCOUNTING);

    Radius radius3 = RadiusTestFixture.authRadius();
    radius3.setName("test-radius-auth3");
    radius3.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius1));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius2));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius3));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    Radius radiusAdded2 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort());
    assertRadiusServers(radius2, radiusAdded2);
    assertRadiusNetworks(radiusAdded2, 0);

    Radius radiusAdded3 = radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius3.getPrimary().getIp(), radius3.getPrimary().getPort());
    assertRadiusServers(radius3, radiusAdded3);
    assertRadiusNetworks(radiusAdded3, 0);

    //////////
    // Add AAANetwork with authRadius
    AAANetwork aaaNetwork = NetworkTestFixture.randomAAANetwork(tenant, n -> {
      n.setId(randomId());
      n.setAuthRadius(radiusAdded1);
    });

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, map(aaaNetwork));
    assertActivityStatusSuccess(ADD_NETWORK, tenantId);

    assertRadiusNetworks(radiusAdded1, 1);

    var viewOps2 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps2 {}", viewOps2);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps2, 2),
        () -> assertTrue(viewOps2.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded1, 1)
    );

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());

    // activate Network to Venue
    Venue v1 = createVenue(tenant, "v1");
    NetworkVenue nv1 = networkVenue(aaaNetwork.getId(), v1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE, userName, nv1);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    var viewOps3 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps3 {}", viewOps3);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps3, 2) // networkvenuemapping
    );

    // venueRadius, venueSchedule, wlanVenue
    var ddccmOps1 = receiveDdccmOperations(1, tenantId);
    log.info("ddccmOps1 {}", ddccmOps1);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps1, 3),
        () -> assertDdccmOpCount(ddccmOps1, Action.ADD, 1, Operation::hasVenueRadius)
    );

    //////////
    // 2 delete Radius should fail since it's in-use
    RequestParams rps = new RequestParams().addPathVariable(RADIUS_ID, radiusAdded1.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_RADIUS_SERVER_PROFILE, userName, rps, "");

    assertActivityStatusFail(DELETE_RADIUS, Errors.WIFI_10470, tenantId);
    assertNoMessages(tenantId, kafkaTopicProvider.getCmnCfgCollectorCfgRequest(),
        kafkaTopicProvider.getDdccmCfgRequest());

    //////////
    // 3 delete Network before deleting Radius
    RequestParams rps2 = new RequestParams().addPathVariable("networkId", aaaNetwork.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_NETWORK, userName, rps2, "");
    assertActivityStatusSuccess(DELETE_NETWORK, tenantId);

    var viewOps5 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps5 {}", viewOps5);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps5, 3),
        () -> assertTrue(viewOps5.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.DEL)
            .findAny().isPresent()),
        () -> assertTrue(viewOps5.stream()
            .filter(o -> NETWORK_VENUE_MAPPING.equals(o.getIndex()))
            .filter(o -> o.getOpType() == OpType.DEL)
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps5, OpType.MOD, radiusAdded1, 0)
    );

    // venueRadius, venueSchedule, wlanVenue
    var ddccmOps2 = receiveDdccmOperations(1, tenantId);
    log.info("ddccmOps2 {}", ddccmOps2);
    assertAll("assert ddccm ops",
        () -> assertDdccmOps(ddccmOps2, 3),
        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasVenueRadius),
        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasVenueSchedule),
        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasWlanVenue)
    );

    //////////
    // 4 delete Radius
    RequestParams rps3 = new RequestParams().addPathVariable(RADIUS_ID, radiusAdded1.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_RADIUS_SERVER_PROFILE, userName, rps3, "");
    assertActivityStatusSuccess(DELETE_RADIUS, tenantId);

    var viewOps6 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps6 {}", viewOps6);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps6, 1),
        () -> assertRadiusIndexViewmodel(viewOps6, OpType.DEL, radiusAdded1, 0)
    );

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());
  }

  @Test
  public void testUpdateWifiNetworkRadiusServerProfileSettings(Tenant tenant, Venue venue) throws Exception {

    var tenantId = tenant.getId();

    Radius authRadius = RadiusTestFixture.authRadius();
    authRadius.setId("radiusId01");
    authRadius = repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(venue, tenantId, randomTxId());

    AuthRadiusService authRadiusService = RadiusTestFixture.authRadiusService(authRadius);
    authRadiusService.setId("radiusServiceId01");
    repositoryUtil.createOrUpdate(authRadiusService, tenantId, randomTxId());

    AuthRadiusVenue authRadiusVenue = RadiusTestFixture.authRadiusVenue(authRadius, venue);
    authRadiusVenue.setId("radiusVenueId01");
    repositoryUtil.createOrUpdate(authRadiusVenue, tenantId, randomTxId());

    AuthRadiusProfile authRadiusProfile = RadiusTestFixture.authRadiusProfile(authRadiusService);
    authRadiusProfile.setId("radiusProfileId01");
    repositoryUtil.createOrUpdate(authRadiusProfile, tenantId, randomTxId());

    // add network
    Radius finalAuthRadius = authRadius;
    AAANetwork aaaNetwork = NetworkTestFixture.randomAAANetwork(tenant, n -> {
      n.setId(randomId());
      n.setAuthRadius(finalAuthRadius);
    });
    repositoryUtil.createOrUpdate(aaaNetwork, tenantId, randomTxId());

    // add NetworkVenue
    final var networkVenue = Generators.networkVenue()
        .setNetwork(always(aaaNetwork)).setVenue(always(venue)).generate();
    aaaNetwork.setNetworkVenues(List.of(networkVenue));
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    // test enableAuthProxy = true
    updateNetworkRadiusServerProfileSettings(tenantId, aaaNetwork.getId(), true);

    // verify DDCCM message for networkVenue
    WlanVenue ddccmWlanVenue = getDdccmWlanVenueFromOperations(tenantId);
    assertEquals("radiusProfileId01", ddccmWlanVenue.getProxyAuthAaa().getId());
    assertEquals("", ddccmWlanVenue.getAuthAaa().getId());

    // test enableAuthProxy = false
    updateNetworkRadiusServerProfileSettings(tenantId, aaaNetwork.getId(), false);

    // verify DDCCM message for networkVenue
    ddccmWlanVenue = getDdccmWlanVenueFromOperations(tenantId);
    assertEquals("", ddccmWlanVenue.getProxyAuthAaa().getId());
    assertEquals("radiusVenueId01", ddccmWlanVenue.getAuthAaa().getId());
  }

  private WlanVenue getDdccmWlanVenueFromOperations(String tenantId) {

    List<Operation> ddccmOps = receiveDdccmOperations(1, tenantId);
    log.info("ddccmOps {}", ddccmOps);
    assertDdccmOpCount(ddccmOps, Action.MODIFY, 1, Operation::hasWlanVenue);

    com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue ddccmWlanVenue =
        ddccmOps.stream().filter(o -> o.getConfigCase().equals(ConfigCase.WLANVENUE))
            .map(Operation::getWlanVenue).toList().get(0);

    return ddccmWlanVenue;
  }


  private void updateNetworkRadiusServerProfileSettings(String tenantId, String networkId, boolean enableAuthProxy) {

    RequestParams rps = new RequestParams().addPathVariable(WIFI_NETWORK_ID, networkId);
    var payload = new com.ruckus.cloud.wifi.eda.viewmodel.WifiNetworkRadiusServerProfileSettings();
    payload.setMacAuthMacFormat(MacAuthMacFormatEnum.Upper);
    payload.setEnableAuthProxy(enableAuthProxy);

    sendWifiCfgRequest(tenantId, randomTxId(),
        CfgAction.UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS,
        txCtxExtension.getUserName(), rps, payload);
  }

}
