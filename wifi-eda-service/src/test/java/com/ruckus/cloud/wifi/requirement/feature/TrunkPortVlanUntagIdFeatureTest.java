package com.ruckus.cloud.wifi.requirement.feature;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.EthernetPortProfileRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Tag("EthernetPortProfileTest")
@WifiUnitTest
public class TrunkPortVlanUntagIdFeatureTest {

  @MockBean
  private EthernetPortProfileRepository ethernetPortProfileRepository;
  @SpyBean
  private TrunkPortVlanUntagIdFeature unit;

  @Nested
  class WhenIsTrunkPortVlanUntagIdEnabled {

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_TRUNK_PORT_UNTAGGED_VLAN_TOGGLE)
    void givenExistTrunkPortUntagIdNotEqualOne() {
      final var venue = new Venue();
      when(ethernetPortProfileRepository.existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueId(eq(
          TxCtxHolder.tenantId()), eq(venue.getId()))).thenReturn(true);

      BDDAssertions.then(unit.test(venue)).isTrue();
    }

    @Test
    void givenExistTrunkPortUntagIdNotEqualOneWithoutFFEnabled() {
      final var venue = new Venue();
      when(ethernetPortProfileRepository.existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueId(eq(
          TxCtxHolder.tenantId()), eq(venue.getId()))).thenReturn(true);

      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    void givenExistTrunkPortUntagIdEqualOne() {
      final var venue = new Venue();
      when(ethernetPortProfileRepository.existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueId(eq(
          TxCtxHolder.tenantId()), eq(venue.getId()))).thenReturn(false);
      BDDAssertions.then(unit.test(venue)).isFalse();
    }
  }
}
