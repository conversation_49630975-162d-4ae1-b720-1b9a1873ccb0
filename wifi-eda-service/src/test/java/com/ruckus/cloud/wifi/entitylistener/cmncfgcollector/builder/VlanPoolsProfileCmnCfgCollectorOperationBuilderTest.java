package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomNetwork;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.repository.NetworkApGroupRepository;
import com.ruckus.cloud.wifi.repository.VlanPoolRepository;
import com.ruckus.cloud.wifi.repository.WlanRepository;
import com.ruckus.cloud.wifi.servicemodel.projection.NetworkVenueApGroupQueryProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VlanPoolVenueQueryProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.assertj.core.api.BDDAssertions;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@WifiUnitTest
class VlanPoolsProfileCmnCfgCollectorOperationBuilderTest {

  @Mock
  private VlanPoolRepository repository;

  @Mock
  private WlanRepository wlanRepository;

  @Mock
  private NetworkApGroupRepository networkApGroupRepository;

  @InjectMocks
  @Spy
  private VlanPoolsProfileCmnCfgCollectorOperationBuilder unit;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void whenEntityClass() {
    BDDAssertions.then(unit.entityClass()).isNotNull().isEqualTo(VlanPool.class);
  }

  @Test
  void whenIndex() {
    BDDAssertions.then(unit.index())
        .isNotNull()
        .isEqualTo(EsConstants.Index.VLAN_POOL_POLICY_PROFILE_INDEX_NAME);
  }

  @Nested
  class WhenConfig {

    private final Operations.Builder builder = Operations.newBuilder();
    private final String venueId = randomId();
    private VlanPool vlanPool;
    private Map<String, Value> docMap;
    private List<String> wifiNetworkIds;

    @BeforeEach
    void beforeEach() {
      vlanPool = new VlanPool(randomId());
      vlanPool.setName(randomName());
      vlanPool.setTenant(new Tenant(txCtxExtension.getTenantId()));
      vlanPool.setVlanMembers(List.of("11", "12"));

      List<VlanPoolVenueQueryProjection> vlanPoolVenueQueryProjections = new ArrayList<>();
      vlanPoolVenueQueryProjections.add(
          new VlanPoolVenueQueryProjectionIml(
              venueId, randomName(), "xx1<!0!>groupA<!-!>xx2<!0!>groupB", 1));
      List<Network> networks = List.of(randomNetwork(new Tenant(txCtxExtension.getTenantId())),
          randomNetwork(new Tenant(txCtxExtension.getTenantId())));
      wifiNetworkIds = networks.stream().map(Network::getId).toList();
      List<NetworkVenueApGroupQueryProjection> networkVenueApGroupQueryProjections = new ArrayList<>();
      wifiNetworkIds.forEach(wifiNetworkId -> {
        networkVenueApGroupQueryProjections.add(
            new NetworkVenueApGroupQueryProjection(
                wifiNetworkId, venueId, false, "xx1", new Date()));
        networkVenueApGroupQueryProjections.add(
            new NetworkVenueApGroupQueryProjection(
                wifiNetworkId, venueId, false, "xx2", new Date()));
      });


      doReturn(new PageImpl<>(vlanPoolVenueQueryProjections), Page.empty()).when(repository)
          .findVenueApGroupActivationsByVlanPoolIdAndQueryVenueName(
              eq(txCtxExtension.getTenantId()), eq(vlanPool.getId()), eq("%%"), any());
      doReturn(networks).when(wlanRepository).findNetworkByTenantIdAndVlanPoolId(
          eq(txCtxExtension.getTenantId()), eq(vlanPool.getId()));
      doReturn(new PageImpl<>(networkVenueApGroupQueryProjections), Page.empty())
          .when(networkApGroupRepository)
          .findNetworkApGroupsByTenantIdAndVlanPoolId(any(), eq(txCtxExtension.getTenantId()),
              eq(vlanPool.getId()));
    }

    @Test
    void testVenueIdInAddVlanPoolProfile() {
      unit.config(builder, vlanPool, EntityAction.ADD);
      docMap = builder.build().getDocMap();
      validateVenueIdInVlanPoolProfileCfgMsg();
    }

    @Test
    void testVenueIdInUpdateVlanPoolProfile() {
      unit.config(builder, vlanPool, EntityAction.MODIFY);
      docMap = builder.build().getDocMap();
      validateVenueIdInVlanPoolProfileCfgMsg();
    }

    private void validateVenueIdInVlanPoolProfileCfgMsg() {
      assertThat(docMap.get(Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(vlanPool.getId());
      assertThat(docMap.get(Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(vlanPool.getName());
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(vlanPool.getTenant().getId());
      assertThat(docMap.get(Key.VENUE_APGROUPS))
          .extracting(Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(Value.class))
          .extracting(Value::getStructValue)
          .allSatisfy(vap -> {
            assertThat(vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue()).isEqualTo(venueId);
          });
      assertThat(docMap.get(Key.WIFI_NETWORK_IDS))
          .extracting(Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(Value.class))
          .extracting(Value::getStringValue)
          .isEqualTo(wifiNetworkIds);
      assertThat(docMap.get(Key.WIFI_NETWORK_VENUE_AP_GROUPS))
          .extracting(Value::getListValue)
          .extracting(ListValue::getValuesList,
              InstanceOfAssertFactories.list(Value.class))
          .extracting(Value::getStructValue)
          .allSatisfy(nvap -> {
            assertThat(nvap.getFieldsOrThrow(Key.VENUE_ID).getStringValue()).isEqualTo(venueId);
            assertThat(nvap.getFieldsOrThrow(Key.WIFI_NETWORK_ID).getStringValue())
                .isNotNull()
                .isIn(wifiNetworkIds);
            assertFalse(nvap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertThat(nvap.getFieldsOrThrow(Key.APGROUP_IDS))
                .extracting(Value::getListValue)
                .extracting(ListValue::getValuesList,
                    InstanceOfAssertFactories.list(Value.class))
                .extracting(Value::getStringValue)
                .isNotNull()
                .isEqualTo(List.of("xx1", "xx2"));
          });
    }
  }

  @Data
  @AllArgsConstructor
  static class VlanPoolVenueQueryProjectionIml implements VlanPoolVenueQueryProjection {

    String venueId;
    String venueName;
    String apGroups;
    Integer venueApCount;
  }
}
