package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.eda.servicemodel.PinProfile;
import com.ruckus.cloud.wifi.servicemodel.projection.PinProfileTenantIdProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import static org.assertj.core.api.Assertions.assertThat;

@Tag("VxLanTunnelFeatureTest")
@WifiJpaDataTest
class PinProfileRepositoryTest {

  @Autowired
  private PinProfileRepository repository;

  @Nested
  @Tag("L2GreTunnelProfileMigrationTest")
  @DisplayName("Test findAllWithSharedTunnels()")
  class FindAllProjectionsWithSharedTunnelProfilesTests {

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tunnel1', 'tenant1', 'tunnel1');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin1', 'tenant1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs1', 'tenant1', 'pin1', 'tunnel1');
        """)
    void shouldNotReturnSelfWhenNoSharing() {
      assertThat(repository.findAllProjectionsWithSharedTunnelProfiles()).isEmpty();
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tunnel1', 'tenant1', 'tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tunnel2', 'tenant1', 'tunnel2');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin1', 'tenant1');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin2', 'tenant1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs1', 'tenant1', 'pin1', 'tunnel1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs2', 'tenant1', 'pin2', 'tunnel1');
        """)
    void shouldReturnProfilesSharingTunnel() {
      assertThat(repository.findAllProjectionsWithSharedTunnelProfiles())
          .hasSize(2)
          .extracting(PinProfileTenantIdProjection::pinId)
          .containsExactlyInAnyOrder("pin1", "pin2");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tunnel1', 'tenant1', 'tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tunnel2', 'tenant1', 'tunnel2');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin1', 'tenant1');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin2', 'tenant1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs1', 'tenant1', 'pin1', 'tunnel1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs2', 'tenant1', 'pin2', 'tunnel2');
      """)
    void shouldReturnEmptyWhenNoSharing() {
      assertThat(repository.findAllProjectionsWithSharedTunnelProfiles())
          .hasSize(0);
    }
  }

  @Nested
  @Tag("L2GreTunnelProfileMigrationTest")
  @DisplayName("Test findAllProjectionsWithDefaultTunnelProfiles()")
  class FindAllProjectionsWithDefaultTunnelProfilesTests {

    @Test
    @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant1');
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tunnel1', 'tenant1', 'tunnel1', false);
      INSERT INTO pin_profile (id, tenant) VALUES ('pin1', 'tenant1');
      INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile)
        VALUES ('prs1', 'tenant1', 'pin1', 'tunnel1');
      """)
    void shouldReturnEmptyWhenTunnelIsNotDefault() {
      assertThat(repository.findAllProjectionsWithDefaultTunnelProfiles()).isEmpty();
    }

    @Test
    @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant1');
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tunnel1', 'tenant1', 'tunnel1', true);
      INSERT INTO pin_profile (id, tenant) VALUES ('pin1', 'tenant1');
      INSERT INTO pin_profile (id, tenant) VALUES ('pin2', 'tenant1');
      INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile)
        VALUES ('prs1', 'tenant1', 'pin1', 'tunnel1');
      INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile)
        VALUES ('prs2', 'tenant1', 'pin2', 'tunnel1');
      """)
    void shouldReturnMultiplePinsUsingSameDefaultTunnel() {
      assertThat(repository.findAllProjectionsWithDefaultTunnelProfiles())
          .hasSize(2)
          .extracting(PinProfileTenantIdProjection::pinId)
          .containsExactlyInAnyOrder("pin1", "pin2");
    }

    @Test
    @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant1');
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tunnel1', 'tenant1', 'tunnel1', true);
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tunnel2', 'tenant1', 'tunnel2', false);
      INSERT INTO pin_profile (id, tenant) VALUES ('pin1', 'tenant1');
      INSERT INTO pin_profile (id, tenant) VALUES ('pin2', 'tenant1');
      INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile)
        VALUES ('prs1', 'tenant1', 'pin1', 'tunnel1');
      INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile)
        VALUES ('prs2', 'tenant1', 'pin2', 'tunnel2');
      """)
    void shouldReturnPinUsingDefaultTunnel() {
      assertThat(repository.findAllProjectionsWithDefaultTunnelProfiles())
          .hasSize(1)
          .extracting(PinProfileTenantIdProjection::pinId)
          .containsExactly("pin1");
    }
  }

  @Nested
  @Tag("L2GreTunnelProfileMigrationTest")
  @DisplayName("Test findPinAndSharedPins()")
  class FindPinProfileAndOthersWithSameTunnelProfileTests {

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tunnel1', 'tenant1', 'tunnel1');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin1', 'tenant1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs1', 'tenant1', 'pin1', 'tunnel1');
        """)
    void shouldReturnSelfOnly() {
      assertThat(repository.findPinProfileAndOthersWithSameTunnelProfile("pin1"))
          .singleElement()
          .extracting(PinProfile::getId)
          .isEqualTo("pin1");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tunnel1', 'tenant1', 'tunnel1');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin1', 'tenant1');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin2', 'tenant1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs1', 'tenant1', 'pin1', 'tunnel1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs2', 'tenant1', 'pin2', 'tunnel1');
        """)
    void shouldReturnBothWhenSharingTunnel() {
      assertThat(repository.findPinProfileAndOthersWithSameTunnelProfile("pin1"))
          .hasSize(2)
          .extracting(PinProfile::getId)
          .containsExactlyInAnyOrder("pin1", "pin2");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tunnel1', 'tenant1', 'tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tunnel2', 'tenant1', 'tunnel2');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin1', 'tenant1');
        INSERT INTO pin_profile (id, tenant) VALUES ('pin2', 'tenant1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs1', 'tenant1', 'pin1', 'tunnel1');
        INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) VALUES ('prs2', 'tenant1', 'pin2', 'tunnel2');
        """)
    void shouldReturnSelfOnlyWhenNoSharing() {
      assertThat(repository.findPinProfileAndOthersWithSameTunnelProfile("pin1"))
          .singleElement()
          .extracting(PinProfile::getId)
          .isEqualTo("pin1");
    }
  }
}