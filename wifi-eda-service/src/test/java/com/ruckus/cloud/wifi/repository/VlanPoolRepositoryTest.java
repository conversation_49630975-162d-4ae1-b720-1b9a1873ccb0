package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wlanAdvancedCustomization;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity.Fields;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.ApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupRadioGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkVenueGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.VenueGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.VlanPoolGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.WlanGenerator;
import com.ruckus.cloud.wifi.servicemodel.projection.VlanPoolVenueQueryProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.test.context.jdbc.Sql;

@Tag("VlanPoolTest")
@WifiJpaDataTest(showSql = false)
@Sql(statements = """
    INSERT INTO tenant (id) VALUES
        ('869576a010e24d23bbd3d030306d3690'),
        ('8e449dec034941d4a39b04684562d67b'),
        ('3b92b8c837ee4b05b345bd4dcedcd645');
    INSERT INTO vlan_pool (id, tenant) VALUES
        ('9506fbc6aad1483b8ac09718c745b3c6', '869576a010e24d23bbd3d030306d3690'),
        ('7506fbc6aad1483b8ac09718c745b3c7', '869576a010e24d23bbd3d030306d3690'),
        ('3506fbc6aad1483b8ac09218c742b3cc', '3b92b8c837ee4b05b345bd4dcedcd645');
    INSERT INTO network (id, type, tenant, name) VALUES
        ('e4ea1c9e226940bab4a0590486c6edb7', 'OPEN', '869576a010e24d23bbd3d030306d3690', 'test123'),
        ('f1c66aa6279142cb8f23bc6d2df9ce26', 'OPEN', '869576a010e24d23bbd3d030306d3690', 'dpskWlan1'),
        ('db6d7f287d9a4b51892a9b202cfadf31', 'OPEN', '869576a010e24d23bbd3d030306d3690', 'n1'),
        ('eb6d7f287d9a4b51892a9b202cfadf32', 'OPEN', '869576a010e24d23bbd3d030306d3690', 'openWlan');
    INSERT INTO wlan (id, vlan_pool, network, tenant) VALUES
        ('aa29f84f63f74b1898289bf1c4a2b156', null, 'f1c66aa6279142cb8f23bc6d2df9ce26', '869576a010e24d23bbd3d030306d3690'),
        ('74bc576d645a4bd3800806bde9d338b9', '9506fbc6aad1483b8ac09718c745b3c6', 'e4ea1c9e226940bab4a0590486c6edb7', '869576a010e24d23bbd3d030306d3690'),
        ('0db4926751c9410387418d6d5f0a2e0d', '9506fbc6aad1483b8ac09718c745b3c6', 'db6d7f287d9a4b51892a9b202cfadf31', '869576a010e24d23bbd3d030306d3690'),
        ('1db4926751c9410387418d6d5f0a2e0e', '7506fbc6aad1483b8ac09718c745b3c7', 'eb6d7f287d9a4b51892a9b202cfadf32', '869576a010e24d23bbd3d030306d3690');
    INSERT INTO venue (id, name, tenant) VALUES
        ('a263782672754f339ad34cb5f22c5c40','dVenue', '869576a010e24d23bbd3d030306d3690'),
        ('144c424d8ef74d09b5435fdb354712d6','My-Venue', '869576a010e24d23bbd3d030306d3690');
    INSERT INTO network_venue (id, is_all_ap_groups, venue, network, tenant) VALUES
        ('839b5743185747b6ae442be3b98f331a', true, '144c424d8ef74d09b5435fdb354712d6', 'f1c66aa6279142cb8f23bc6d2df9ce26', '869576a010e24d23bbd3d030306d3690'),
        ('64166dfc4f5849619bff2d1e2f385254', true, 'a263782672754f339ad34cb5f22c5c40', 'e4ea1c9e226940bab4a0590486c6edb7', '869576a010e24d23bbd3d030306d3690'),
        ('3aed3ac03da445fb99b70e9e6d32c7d8', false, '144c424d8ef74d09b5435fdb354712d6', 'db6d7f287d9a4b51892a9b202cfadf31', '869576a010e24d23bbd3d030306d3690');
    INSERT INTO ap_group (id, name, is_default, tenant, venue) VALUES
        ('bd703743f85b4887b75826cb654a8eb7', 'ap1', false, '869576a010e24d23bbd3d030306d3690', '144c424d8ef74d09b5435fdb354712d6'),
        ('dad50fd9d56b4c4bba634f596647504a', 'ap2', false, '869576a010e24d23bbd3d030306d3690', '144c424d8ef74d09b5435fdb354712d6'),
        ('c2b0fd39957e4314ba27f90a468c5e8d', '', true, '869576a010e24d23bbd3d030306d3690', '144c424d8ef74d09b5435fdb354712d6'),
        ('e60a8e3f013c47cc97f12eb7a9a6812f', '', true, '869576a010e24d23bbd3d030306d3690', 'a263782672754f339ad34cb5f22c5c40');
    INSERT INTO network_ap_group (id, network_venue, ap_group, tenant) VALUES
        ('a9c2872816c1455d856f43c4107db739','64166dfc4f5849619bff2d1e2f385254','e60a8e3f013c47cc97f12eb7a9a6812f', '869576a010e24d23bbd3d030306d3690'),
        ('8a6d34b5dff9421592f32630cc95a6a1','839b5743185747b6ae442be3b98f331a','bd703743f85b4887b75826cb654a8eb7', '869576a010e24d23bbd3d030306d3690'),
        ('5fa7568f015b4aa5993912a1419eb1f0','839b5743185747b6ae442be3b98f331a','c2b0fd39957e4314ba27f90a468c5e8d', '869576a010e24d23bbd3d030306d3690'),
        ('2976309148194955beb256798ce17077','3aed3ac03da445fb99b70e9e6d32c7d8','bd703743f85b4887b75826cb654a8eb7', '869576a010e24d23bbd3d030306d3690'),
        ('23c6d74b7f934c9aaf755e4995181bf2','3aed3ac03da445fb99b70e9e6d32c7d8','dad50fd9d56b4c4bba634f596647504a', '869576a010e24d23bbd3d030306d3690'),
        ('03ab6d21c3d0415fa6a2532c95fca231','839b5743185747b6ae442be3b98f331a','dad50fd9d56b4c4bba634f596647504a', '869576a010e24d23bbd3d030306d3690');
    INSERT INTO ap (id, ap_group, tenant) VALUES
        ('961211111151', 'e60a8e3f013c47cc97f12eb7a9a6812f', '869576a010e24d23bbd3d030306d3690');
    INSERT INTO network_ap_group_radio (id, network_ap_group, vlan_pool, tenant) VALUES
        ('aa72c0e5e45f4b18b6b67f524a265eea','23c6d74b7f934c9aaf755e4995181bf2','9506fbc6aad1483b8ac09718c745b3c6','869576a010e24d23bbd3d030306d3690'),
        ('5a7cb87d9a604d9882c991f52da75fc6','2976309148194955beb256798ce17077','9506fbc6aad1483b8ac09718c745b3c6','869576a010e24d23bbd3d030306d3690'),
        ('f0631d23e6924aed82d3c2d8385830a9','a9c2872816c1455d856f43c4107db739',null,'869576a010e24d23bbd3d030306d3690'),
        ('9cf461a59dae4d29bfc0e875e5a7b5ae','03ab6d21c3d0415fa6a2532c95fca231',null,'869576a010e24d23bbd3d030306d3690'),
        ('ae3012fb48af410eabdc346f76a47aff','8a6d34b5dff9421592f32630cc95a6a1',null,'869576a010e24d23bbd3d030306d3690'),
        ('73cfb8ea68a9431fb6391c17da9d5805','5fa7568f015b4aa5993912a1419eb1f0',null,'869576a010e24d23bbd3d030306d3690');
    UPDATE vlan_pool SET is_template = false;
    UPDATE network SET is_template = false;
    UPDATE venue SET is_template = false;
    INSERT INTO tenant (id) VALUES ('tenet');
    INSERT INTO vlan_pool (id, tenant, name, is_template) VALUES
        ('vp1', 'tenet', 'M VP', false),
        ('vp2', 'tenet', 'VIP', false),
        ('vp3', 'tenet', 'M VP', true);
    """)
class VlanPoolRepositoryTest {

  @Autowired
  private VlanPoolRepository repository;

  @Autowired
  private RepositoryUtil repositoryUtil;

  private final VenueGenerator venueGenerator = Generators.venue();
  private final VlanPoolGenerator vlanPoolGenerator = Generators.vlanPool();

  @Nested
  class GivenTenantVenuesAndVlanPools {

    private Venue venue1;
    private Venue venue2;

    private VlanPool vlanPool1;
    private VlanPool vlanPool2;

    @BeforeEach
    void beforeEach(Tenant tenant) {
      venue1 = repositoryUtil.createOrUpdate(
          venueGenerator.generate(), tenant.getId(), randomTxId());
      venue2 = repositoryUtil.createOrUpdate(
          venueGenerator.generate(), tenant.getId(), randomTxId());

      vlanPool1 = repositoryUtil.createOrUpdate(
          vlanPoolGenerator.generate(), tenant.getId(), randomTxId());
      vlanPool2 = repositoryUtil.createOrUpdate(
          vlanPoolGenerator.generate(), tenant.getId(), randomTxId());
    }

    @Nested
    class GivenVlanPoolUsedByNetworks {

      private final WlanGenerator wlanGenerator = Generators.wlan();
      private final NetworkGenerator<OpenNetwork, ?> networkGenerator = Generators.network(OpenNetwork.class).setWlan(wlanGenerator);
      private final NetworkVenueGenerator networkVenueGenerator = Generators.networkVenue();

      private Network network1;
      private Network network2;
      private Network network3;

      private NetworkVenue networkVenue1_1;
      private NetworkVenue networkVenue2_1;
      private NetworkVenue networkVenue3_2;

      @BeforeEach
      void beforeEach(Tenant tenant) {
        // Network #1 with VlanPool #1
        network1 = repositoryUtil.createOrUpdate(
            generateNetworkForVlanPool(vlanPool1), tenant.getId(), randomTxId());
        // Network #2 with VlanPool #1
        network2 = repositoryUtil.createOrUpdate(
            generateNetworkForVlanPool(vlanPool1), tenant.getId(), randomTxId());
        // Network #3 with VlanPool #2
        network3 = repositoryUtil.createOrUpdate(
            generateNetworkForVlanPool(vlanPool2), tenant.getId(), randomTxId());

        // NetworkVenue of network1 + venue1
        networkVenue1_1 = repositoryUtil.createOrUpdate(
            generateNetworkVenue(network1, venue1), tenant.getId(), randomTxId());
        // NetworkVenue of network2 + venue1
        networkVenue2_1 = repositoryUtil.createOrUpdate(
            generateNetworkVenue(network2, venue1), tenant.getId(), randomTxId());
        // NetworkVenue of network3 + venue2
        networkVenue3_2 = repositoryUtil.createOrUpdate(
            generateNetworkVenue(network3, venue2), tenant.getId(), randomTxId());
      }

      @Test
      void findByTenantIdAndVenueId1(Tenant tenant) {
        // When, Then
        assertThat(repository.findByTenantIdAndVenueId(tenant.getId(), venue1.getId()))
            .isNotNull()
            .hasSize(1)
            .singleElement()
            .extracting(VlanPool::getId)
            .isEqualTo(vlanPool1.getId());
      }

      @Test
      void findByTenantIdAndVenueId2(Tenant tenant) {
        // When, Then
        assertThat(repository.findByTenantIdAndVenueId(tenant.getId(), venue2.getId()))
            .isNotNull()
            .hasSize(1)
            .singleElement()
            .extracting(VlanPool::getId)
            .isEqualTo(vlanPool2.getId());
      }

      @Nested
      class GivenVlanPoolOverrideByApGroups {

        private final ApGroupGenerator apGroupGenerator = Generators.apGroup();
        private final NetworkApGroupGenerator networkApGroupGenerator = Generators.networkApGroup();
        private final NetworkApGroupRadioGenerator networkApGroupRadioGenerator = Generators.networkApGroupRadio();

        private ApGroup apGroup1_1;
        private NetworkApGroup networkApGroup1_1_1_1;

        @BeforeEach
        void beforeEach(Tenant tenant) {
          // ApGroup #1 in venue1
          apGroup1_1 = repositoryUtil.createOrUpdate(
              apGroupGenerator.setVenue(always(venue1)).generate(), tenant.getId(), randomTxId());
          // NetworkApGroup of apGroup1_1 + networkVenue1_1 with VlanPool #2
          networkApGroup1_1_1_1 = repositoryUtil.createOrUpdate(
              networkApGroupGenerator.setApGroup(always(apGroup1_1))
                  .setNetworkVenue(always(networkVenue1_1))
                  .generate(), tenant.getId(), randomTxId());
          networkApGroupRadioGenerator
              .setNetworkApGroup(always(networkApGroup1_1_1_1))
              .setVlanPool(always(vlanPool2)).generate(StrictRadioTypeEnum.values().length)
              .forEach(radio -> repositoryUtil.createOrUpdate(radio, tenant.getId(), randomTxId()));
        }

        @Test
        void findByTenantIdAndVenueId1(Tenant tenant) {
          // When
          assertThat(repository.findByTenantIdAndVenueId(tenant.getId(), venue1.getId()))
              // Then
              .isNotNull()
              .satisfies(vlanPools -> assertSoftly(softly -> {
                softly.assertThat(vlanPools)
                    .as("The total count of VlanPool in the result should be 2")
                    .hasSize(2);
                softly.assertThat(vlanPools)
                    .filteredOn(vp -> vp.getId().equals(vlanPool1.getId()))
                    .as("The count of VlanPool with id = [%s] in the result should be 1", vlanPool1.getId())
                    .hasSize(1);
                softly.assertThat(vlanPools)
                    .filteredOn(vp -> vp.getId().equals(vlanPool2.getId()))
                    .as("The count of VlanPool with id = [%s] in the result should be 1", vlanPool2.getId())
                    .hasSize(1);
              }));
        }

        @Test
        void findByTenantIdAndVenueId2(Tenant tenant) {
          // When, Then
          assertThat(repository.findByTenantIdAndVenueId(tenant.getId(), venue2.getId()))
              .isNotNull()
              .hasSize(1)
              .singleElement()
              .extracting(VlanPool::getId)
              .isEqualTo(vlanPool2.getId());
        }
      }

      private Network generateNetworkForVlanPool(VlanPool vlanPool) {
        wlanGenerator.setAdvancedCustomization(
            wlanAdvancedCustomization().setVlanPool(always(vlanPool)));
        final var network = networkGenerator.generate();
        network.getWlan().setNetwork(network);
        return network;
      }

      private NetworkVenue generateNetworkVenue(Network network, Venue venue) {
        final var networkVenue = networkVenueGenerator
            .setNetwork(always(network)).setVenue(always(venue)).generate();
        if (network.getNetworkVenues() == null) {
          network.setNetworkVenues(new ArrayList<>());
        }
        network.getNetworkVenues().add(networkVenue);
        return networkVenue;
      }
    }
  }

  @Test
  public void findVenueApGroupActivationsByVlanPoolIdAndQueryVenueNameTest() {
    String tenantId = "869576a010e24d23bbd3d030306d3690";
    String vlanPoolId = "9506fbc6aad1483b8ac09718c745b3c6";
    String queryVenueName = "%%";
    Pageable pageable = PageRequest.of(0, 10, Direction.DESC, "apGroupData");
    Page<VlanPoolVenueQueryProjection> page = repository.findVenueApGroupActivationsByVlanPoolIdAndQueryVenueName(
        tenantId, vlanPoolId, queryVenueName, pageable);
    assertThat(page)
        .isNotNull()
        .hasSize(2)
        .first()
        .satisfies( vlanPoolVenueQueryProjection -> {
          assertThat(vlanPoolVenueQueryProjection.getVenueName())
              .isEqualTo("My-Venue");
          assertThat(vlanPoolVenueQueryProjection.getVenueApCount())
              .isEqualTo(0);
        });

    pageable = PageRequest.of(0, 10, Direction.DESC, "venueApCount");
    page = repository.findVenueApGroupActivationsByVlanPoolIdAndQueryVenueName(
        tenantId, vlanPoolId, queryVenueName, pageable);

    assertThat(page)
        .isNotNull()
        .hasSize(2)
        .first()
        .satisfies( vlanPoolVenueQueryProjection -> {
          assertThat(vlanPoolVenueQueryProjection.getVenueName())
              .isEqualTo("dVenue");
          assertThat(vlanPoolVenueQueryProjection.getVenueApCount())
              .isEqualTo(1);
        });
  }

  @Test
  public void findVenueApGroupActivationsByVlanPoolIdAndQueryVenueNamePageTest() {
    String tenantId = "869576a010e24d23bbd3d030306d3690";
    String vlanPoolId = "9506fbc6aad1483b8ac09718c745b3c6";
    String queryVenueName = "%%";
    Pageable pageable = PageRequest.of(0, 1, Direction.DESC, "apGroupData");
    Page<VlanPoolVenueQueryProjection> page = repository.findVenueApGroupActivationsByVlanPoolIdAndQueryVenueName(
        tenantId, vlanPoolId, queryVenueName, pageable);
    assertThat(page)
        .isNotNull()
        .hasSize(1)
        .first()
        .satisfies( vlanPoolVenueQueryProjection -> {
          assertThat(vlanPoolVenueQueryProjection.getVenueName())
              .isEqualTo("My-Venue");
          assertThat(vlanPoolVenueQueryProjection.getVenueApCount())
              .isEqualTo(0);
        });

    pageable = PageRequest.of(1, 1, Direction.DESC, "apGroupData");
    page = repository.findVenueApGroupActivationsByVlanPoolIdAndQueryVenueName(
        tenantId, vlanPoolId, queryVenueName, pageable);

    assertThat(page)
        .isNotNull()
        .hasSize(1)
        .first()
        .satisfies( vlanPoolVenueQueryProjection -> {
          assertThat(vlanPoolVenueQueryProjection.getVenueName())
              .isEqualTo("dVenue");
          assertThat(vlanPoolVenueQueryProjection.getVenueApCount())
              .isEqualTo(1);
        });
  }

  @Test
  void testFindByNonExistingTenantIdAndNetworkActivations(Tenant tenant) {
    var result = repository.findByTenantIdAndNetworkActivations(Pageable.unpaged(), tenant.getId());
    assertThat(result)
        .isNotNull()
        .isEmpty();
  }

  @Test
  void testFindByExistingTenantIdAndNetworkActivations() {
    var tenantId = "869576a010e24d23bbd3d030306d3690";
    var result = repository.findByTenantIdAndNetworkActivations(Pageable.unpaged(), tenantId);
    assertThat(result)
        .isNotNull()
        .isNotEmpty()
        .hasSize(2)
        .extracting(VlanPool::getId)
        .allSatisfy(vlanPoolId -> {
          assertThat(vlanPoolId).isIn(
              List.of("9506fbc6aad1483b8ac09718c745b3c6", "7506fbc6aad1483b8ac09718c745b3c7"));
        });
  }

  @Test
  void testFindByTenantIdAndNetworkActivationsSortByCreatedDate() {
    var tenantId = "869576a010e24d23bbd3d030306d3690";
    Pageable pageRequest = PageRequest.of(0, 1, Sort.by(Fields.CREATEDDATE));
    Page<VlanPool> result = repository.findByTenantIdAndNetworkActivations(pageRequest, tenantId);
    var totalCnt = 0;
    while(result.hasContent()) {
      assertThat(result)
          .isNotNull()
          .isNotEmpty()
          .allSatisfy(vp -> {
            assertThat(vp.getId()).isIn(
                List.of("9506fbc6aad1483b8ac09718c745b3c6", "7506fbc6aad1483b8ac09718c745b3c7"));
          });
      totalCnt += result.getSize();
      pageRequest = pageRequest.next();
      result = repository.findByTenantIdAndNetworkActivations(pageRequest, tenantId);
    }
    assertThat(totalCnt).isEqualTo(2);
  }

  @Test
  void testFindAllDistinctTenantIds() {
    var result = repository.findAllDistinctTenantIds();
    assertThat(result)
        .isNotEmpty()
        .hasSizeGreaterThanOrEqualTo(2)
        .contains(
            "869576a010e24d23bbd3d030306d3690",
            "3b92b8c837ee4b05b345bd4dcedcd645");
  }

  @Test
  void testFindByTenantIdAndQueryName() {
    PageRequest pageable = PageRequest.of(0, 2, Sort.by(Direction.ASC, Fields.ID));
    assertThat(repository.findByTenantIdAndIsNotTemplateAndQueryName("tenet", "M VP", pageable))
        .hasSize(1)
        .singleElement()
        .extracting(vp -> vp.getId())
        .isEqualTo("vp1");
    assertThat(repository.findByTenantIdAndIsNotTemplateAndQueryName("tenet", "VIP", pageable))
        .hasSize(1)
        .extracting(vp -> vp.getId())
        .containsOnly("vp2");
  }

  @ApplyTemplateFilter
  @Nested
  class InTemplateFlow {

    @Test
    void testexistsByTenantIdAndName() {
      assertThat(repository.existsByTenantIdAndName("tenet", "M VP")).isTrue();
      assertThat(repository.existsByTenantIdAndName("tenet", "VIP")).isFalse();
    }

    @Test
    void testexistsByTenantIdAndNameAndIdNot() {
      assertThat(repository.existsByTenantIdAndNameAndIdNot("tenet", "M VP", "vp1")).isTrue();
      assertThat(repository.existsByTenantIdAndNameAndIdNot("tenet", "VIP", "vp2")).isFalse();
      assertThat(repository.existsByTenantIdAndNameAndIdNot("tenet", "M VP", "vp3")).isFalse();
    }

    @Test
    void testcountByTenantId() {
      assertThat(repository.countByTenantId("tenet")).isEqualTo(1);
    }

    @Test
    void testfindByTenantId() {
      assertThat(repository.findByTenantId("tenet"))
          .hasSize(1)
          .singleElement()
          .extracting(VlanPool::getId)
          .isEqualTo("vp3");
    }

    @Test
    void testfindByIdAndTenantId() {
      assertThat(repository.findByIdAndTenantId("vp1", "tenet"))
          .isNotPresent();

      assertThat(repository.findByIdAndTenantId("vp3", "tenet"))
          .isPresent()
          .map(VlanPool::getId)
          .hasValue("vp3");

      assertThat(repository.findByIdAndTenantId("vp4", "tenet"))
          .isNotPresent();
    }
  }

  @Nested
  class OutOfTemplateFlow {

    @Test
    void testexistsByTenantIdAndName() {
      assertThat(repository.existsByTenantIdAndName("tenet", "M VP")).isTrue();
      assertThat(repository.existsByTenantIdAndName("tenet", "VIP")).isTrue();
    }

    @Test
    void testexistsByTenantIdAndNameAndIdNot() {
      assertThat(repository.existsByTenantIdAndNameAndIdNot("tenet", "M VP", "vp1")).isFalse();
      assertThat(repository.existsByTenantIdAndNameAndIdNot("tenet", "VIP", "vp2")).isFalse();
      assertThat(repository.existsByTenantIdAndNameAndIdNot("tenet", "M VP", "vp3")).isTrue();
    }

    @Test
    void testcountByTenantId() {
      assertThat(repository.countByTenantId("tenet")).isEqualTo(2);
    }

    @Test
    void testfindByTenantId() {
      assertThat(repository.findByTenantId("tenet"))
          .hasSize(2)
          .extracting(VlanPool::getId)
          .containsOnly("vp1", "vp2");
    }

    @Test
    void testfindByTenantIdPageable() {
      assertThat(repository.findByTenantId(PageRequest.of(0, 1,
          Sort.by(Sort.Direction.ASC, AbstractBaseEntity.Fields.ID)), "tenet"))
          .isNotNull()
          .hasSize(1)
          .singleElement()
          .extracting(VlanPool::getId)
          .isEqualTo("vp1");

      assertThat(repository.findByTenantId(PageRequest.of(1, 1,
          Sort.by(Sort.Direction.ASC, AbstractBaseEntity.Fields.ID)), "tenet"))
          .isNotNull()
          .hasSize(1)
          .singleElement()
          .extracting(VlanPool::getId)
          .isEqualTo("vp2");

      assertThat(repository.findByTenantId(PageRequest.of(2, 1,
          Sort.by(Sort.Direction.ASC, AbstractBaseEntity.Fields.ID)), "tenet"))
          .isNotNull()
          .hasSize(0);
    }

    @Test
    void testfindByIdAndTenantId() {
      assertThat(repository.findByIdAndTenantId("vp1", "tenet"))
          .isPresent()
          .map(VlanPool::getId)
          .hasValue("vp1");

      assertThat(repository.findByIdAndTenantId("vp2", "tenet"))
          .isPresent()
          .map(VlanPool::getId)
          .hasValue("vp2");
    }
  }
}
