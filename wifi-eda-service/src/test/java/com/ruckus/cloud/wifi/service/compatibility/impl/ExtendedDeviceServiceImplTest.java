package com.ruckus.cloud.wifi.service.compatibility.impl;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.apache.commons.lang3.RandomStringUtils.randomAlphanumeric;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.mock;

import com.ruckus.cloud.compatibility.contract.model.DeviceAggregationGroup;
import com.ruckus.cloud.wifi.client.viewmodel.ViewmodelClientGrpc;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApFirmwareModelCountAggregationDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApFirmwareModelCountDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApFirmwareModelDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApSubStateEnum;
import com.ruckus.cloud.wifi.service.NetworkApGroupService;
import com.ruckus.cloud.wifi.servicemodel.projection.NetworkApGroupProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class ExtendedDeviceServiceImplTest {
  @RegisterExtension
  public final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @MockBean private ViewmodelClientGrpc viewmodelClient;
  @MockBean private NetworkApGroupService networkApGroupService;

  @SpyBean private ExtendedDeviceServiceImpl unit;

  @Test
  void whenGetDeviceAggregationGroupsByNetworks() {
    final var networkIds = Stream.generate(CommonTestFixture::randomId).limit(3).toList();
    final var projections =
        networkIds.stream()
            .flatMap(
                networkId ->
                    Stream.generate(CommonTestFixture::randomId)
                        .limit(3)
                        .map(apGroupId -> new NetworkApGroupProjection(networkId, apGroupId)))
            .toList();
    final var apGroupIds = projections.stream().map(NetworkApGroupProjection::apGroupId).toList();
    final var apFirmwareModelCountAggregations =
        apGroupIds.stream().map(this::randomAggregationDTO).toList();

    willReturn(projections).given(networkApGroupService).getNetworkApGroupProjections(anyList());
    willReturn(apFirmwareModelCountAggregations)
        .given(viewmodelClient)
        .getApFirmwareModelCountAggregationsByApGroupIds(anyString(), anyString(), anyList());

    BDDAssertions.then(unit.getDeviceAggregationGroupsByNetworks(networkIds))
        .isNotNull()
        .hasSize(networkIds.size())
        .allSatisfy(
            group -> {
              final var networkId = group.getId();
              final var expectedApGroupIds =
                  projections.stream()
                      .filter(p -> p.networkId().equals(networkId))
                      .map(NetworkApGroupProjection::apGroupId)
                      .toList();
              final var apFirmwareModelCounts =
                  apFirmwareModelCountAggregations.stream()
                      .filter(agg -> expectedApGroupIds.contains(agg.getId()))
                      .flatMap(agg -> agg.getApFirmwareModelCounts().stream())
                      .toList();
              assertThat(group.getDeviceAggregations())
                  .hasSize(apFirmwareModelCounts.size())
                  .allMatch(
                      agg ->
                          apFirmwareModelCounts.stream()
                              .anyMatch(
                                  count ->
                                      agg.getCount() == count.getCount()
                                          && Objects.equals(
                                              agg.getDevice().getModel(),
                                              count.getApFirmwareModel().getModel())
                                          && Objects.equals(
                                              agg.getDevice().getFirmware(),
                                              count.getApFirmwareModel().getFirmware())));
            })
        .extracting(DeviceAggregationGroup::getId)
        .containsExactlyInAnyOrderElementsOf(networkIds);

    then(networkApGroupService).should().getNetworkApGroupProjections(eq(networkIds));
    then(viewmodelClient)
        .should()
        .getApFirmwareModelCountAggregationsByApGroupIds(
            eq(txCtxExtension.getRequestId()),
            eq(txCtxExtension.getTenantId()),
            argThat(ids -> CollectionUtils.isEqualCollection(ids, apGroupIds)));
  }

  @Test
  void whenGetDeviceAggregationsByNetwork() {
    final var networkId = randomId();
    final var projections =
        Stream.generate(() -> new NetworkApGroupProjection(networkId, randomId()))
            .limit(10)
            .toList();
    final var apGroupIds = projections.stream().map(NetworkApGroupProjection::apGroupId).toList();
    final var apFirmwareModelCountAggregations =
        apGroupIds.stream().map(this::randomAggregationDTO).toList();

    final var apFirmwareModelCounts =
        apFirmwareModelCountAggregations.stream()
            .flatMap(agg -> agg.getApFirmwareModelCounts().stream())
            .toList();

    willReturn(projections).given(networkApGroupService).getNetworkApGroupProjections(anyList());
    willReturn(apFirmwareModelCountAggregations)
        .given(viewmodelClient)
        .getApFirmwareModelCountAggregationsByApGroupIds(anyString(), anyString(), anyList());

    BDDAssertions.then(unit.getDeviceAggregationsByNetwork(networkId))
        .isNotNull()
        .hasSize(
            apFirmwareModelCountAggregations.stream()
                .mapToInt(agg -> agg.getApFirmwareModelCounts().size())
                .sum())
        .allMatch(
            d ->
                apFirmwareModelCounts.stream()
                    .anyMatch(
                        count ->
                            Objects.equals(
                                    d.getDevice().getModel(), count.getApFirmwareModel().getModel())
                                && Objects.equals(
                                    d.getDevice().getFirmware(),
                                    count.getApFirmwareModel().getFirmware())
                                && d.getCount() == count.getCount()));

    then(networkApGroupService).should().getNetworkApGroupProjections(eq(List.of(networkId)));
    then(viewmodelClient)
        .should()
        .getApFirmwareModelCountAggregationsByApGroupIds(
            eq(txCtxExtension.getRequestId()),
            eq(txCtxExtension.getTenantId()),
            argThat(ids -> CollectionUtils.isEqualCollection(ids, apGroupIds)));
  }

  @Test
  void whenGetDeviceAggregations() {
    final var venueId = randomId();
    final var apFirmwareModelCountAggregation = randomAggregationDTO(venueId);

    willReturn(List.of(apFirmwareModelCountAggregation))
        .given(viewmodelClient)
        .getApFirmwareModelCountAggregationsByVenueIds(anyString(), anyString(), anyList());

    BDDAssertions.then(unit.getDeviceAggregations(venueId))
        .isNotNull()
        .hasSize(apFirmwareModelCountAggregation.getApFirmwareModelCounts().size())
        .allMatch(
            d ->
                apFirmwareModelCountAggregation.getApFirmwareModelCounts().stream()
                    .anyMatch(
                        agg ->
                            d.getCount() == agg.getCount()
                                && Objects.equals(
                                    d.getDevice().getModel(), agg.getApFirmwareModel().getModel())
                                && Objects.equals(
                                    d.getDevice().getFirmware(),
                                    agg.getApFirmwareModel().getFirmware())));

    then(viewmodelClient)
        .should()
        .getApFirmwareModelCountAggregationsByVenueIds(
            eq(txCtxExtension.getRequestId()),
            eq(txCtxExtension.getTenantId()),
            eq(List.of(venueId)));
  }

  @Test
  void whenGetDeviceAggregationGroups() {
    final var venueIds = Stream.generate(CommonTestFixture::randomId).limit(3).toList();
    final var apFirmwareModelCountAggregations =
        venueIds.stream().map(this::randomAggregationDTO).toList();

    willReturn(apFirmwareModelCountAggregations)
        .given(viewmodelClient)
        .getApFirmwareModelCountAggregationsByVenueIds(anyString(), anyString(), anyList());

    BDDAssertions.then(unit.getDeviceAggregationGroups(venueIds))
        .isNotNull()
        .hasSize(venueIds.size())
        .allSatisfy(
            group -> {
              final var venueId = group.getId();
              final var apFirmwareModelCounts =
                  apFirmwareModelCountAggregations.stream()
                      .filter(agg -> agg.getId().equals(venueId))
                      .findFirst()
                      .map(ApFirmwareModelCountAggregationDTO::getApFirmwareModelCounts)
                      .orElseThrow();
              assertThat(group.getDeviceAggregations())
                  .hasSize(apFirmwareModelCounts.size())
                  .allMatch(
                      agg ->
                          apFirmwareModelCounts.stream()
                              .anyMatch(
                                  count ->
                                      agg.getCount() == count.getCount()
                                          && Objects.equals(
                                              agg.getDevice().getModel(),
                                              count.getApFirmwareModel().getModel())
                                          && Objects.equals(
                                              agg.getDevice().getFirmware(),
                                              count.getApFirmwareModel().getFirmware())));
            });

    then(viewmodelClient)
        .should()
        .getApFirmwareModelCountAggregationsByVenueIds(
            eq(txCtxExtension.getRequestId()), eq(txCtxExtension.getTenantId()), eq(venueIds));
  }

  @Test
  void whenGetDevicesWithIdentity() {
    final var deviceIds =
        Stream.generate(CommonTestFixture::randomSerialNumber).limit(100).toList();
    final var aps =
        deviceIds.stream()
            .map(id -> ApDTO.builder().serialNumber(id).build())
            .peek(ap -> ap.setFirmware(randomAlphanumeric(10)))
            .peek(ap -> ap.setModel(randomAlphanumeric(10)))
            .peek(
                ap ->
                    ap.setSubState(
                        ApSubStateEnum.values()[
                            RandomUtils.nextInt(0, ApSubStateEnum.values().length)]))
            .toList();

    willReturn(aps).given(viewmodelClient).getApsBySerialNumber(anyString(), anyString(), anySet());

    BDDAssertions.then(unit.getDevicesWithIdentity(deviceIds))
        .isNotNull()
        .matches(devices -> devices.size() <= aps.size())
        .allMatch(
            device ->
                aps.stream()
                    .anyMatch(
                        ap ->
                            ap.getSerialNumber().equals(device.getId())
                                && ap.getSubState() == ApSubStateEnum.Operational
                                && ap.getFirmware().equals(device.getDevice().getFirmware())
                                && ap.getModel().equals(device.getDevice().getModel())));

    then(viewmodelClient)
        .should()
        .getApsBySerialNumber(
            eq(txCtxExtension.getRequestId()),
            eq(txCtxExtension.getTenantId()),
            eq(Set.copyOf(deviceIds)));
  }

  private ApFirmwareModelCountAggregationDTO randomAggregationDTO(String id) {
    return ApFirmwareModelCountAggregationDTO.builder()
        .id(id)
        .apFirmwareModelCounts(
            Stream.generate(
                    () ->
                        ApFirmwareModelCountDTO.builder()
                            .apFirmwareModel(mock(ApFirmwareModelDTO.class))
                            .count(RandomUtils.nextInt(0, 100))
                            .build())
                .limit(10)
                .toList())
        .build();
  }
}
