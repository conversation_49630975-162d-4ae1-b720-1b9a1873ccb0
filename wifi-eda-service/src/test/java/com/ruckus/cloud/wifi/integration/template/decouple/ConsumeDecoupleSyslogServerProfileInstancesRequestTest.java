package com.ruckus.cloud.wifi.integration.template.decouple;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.SyslogServerProfileTestFixture;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.*;

import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.events.gpb.OpType;

@Slf4j
@WifiIntegrationTest
public class ConsumeDecoupleSyslogServerProfileInstancesRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void init() {
  clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void decouple_syslogServerProfileInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();
    // create msp tenant & template
    String mspTenantId = mspTenant.getId();
    repositoryUtil.createOrUpdate(mspTenant, mspTenantId);
    SyslogServerProfile template = SyslogServerProfileTestFixture.randomSyslogServerProfile();
    template.setIsTemplate(true);
    repositoryUtil.createOrUpdate(template, mspTenantId);

    // create ec tenant & instance
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, ecTenantId);
    SyslogServerProfile instance = SyslogServerProfileTestFixture.randomSyslogServerProfile();
    instance.setIsTemplate(false);
    instance.setIsEnforced(true);
    instance.setTemplateId(template.getId());
    repositoryUtil.createOrUpdate(instance, ecTenantId);

    // Verify before decouple
    SyslogServerProfile before = repositoryUtil.find(SyslogServerProfile.class, instance.getId(), ecTenantId, false);
    assertAll(
    () -> assertEquals(template.getId(), before.getTemplateId()),
    () -> assertFalse(before.getIsTemplate()),
    () -> assertTrue(before.getIsEnforced())
    );

    // Perform decouple
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_SYSLOG_SERVER_PROFILE_INSTANCES, userName, template.getId());
    assertDdccmCfgRequestNotSent(ecTenantId);
    // Verify after decouple
    SyslogServerProfile after = repositoryUtil.find(SyslogServerProfile.class, instance.getId(), ecTenantId, false);
    assertAll(
    () -> assertNull(after.getTemplateId()),
    () -> assertFalse(after.getIsEnforced()),
    () -> assertFalse(after.getIsTemplate())
    );
    // Verify template is not affected
    changeTxCtxTenant(mspTenantId);
    SyslogServerProfile templateAfter = repositoryUtil.find(SyslogServerProfile.class, template.getId(), mspTenantId, true);
    assertAll(
    () -> assertTrue(templateAfter.getIsTemplate()),
    () -> assertEquals(template.getName(), templateAfter.getName())
    );

    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instance.getId());
  }

  @Test
  public void decouple_noInstances_shouldSucceed() {
  var userName = txCtxExtension.getUserName();
  Tenant mspTenant = TenantTestFixture.randomTenant((t) -> {});
  String mspTenantId = mspTenant.getId();
  repositoryUtil.createOrUpdate(mspTenant, mspTenantId);
  SyslogServerProfile template = SyslogServerProfileTestFixture.randomSyslogServerProfile();
  template.setIsTemplate(true);
  repositoryUtil.createOrUpdate(template, mspTenantId);
  Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
  String ecTenantId = ecTenant.getId();
  repositoryUtil.createOrUpdate(ecTenant, ecTenantId);
  // Do not create any instance
  changeTxCtxTenant(ecTenantId);
  sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_SYSLOG_SERVER_PROFILE_INSTANCES, userName, template.getId());
  assertDdccmCfgRequestNotSent(ecTenantId);
  // Should not throw exception and no instance is decoupled
  assertTrue(true);
  assertViewmodelOpsNotSent(ecTenantId);
  }

  private void validateCmnCfgCollectorMessage(String tenantId, String requestId, String instanceId) {
  final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
  assertNotNull(cmnCfgCollectorMessage.getPayload());
  assertEquals(tenantId, cmnCfgCollectorMessage.getPayload().getTenantId());
  assertEquals(requestId, cmnCfgCollectorMessage.getPayload().getRequestId());
  assertEquals(1, cmnCfgCollectorMessage.getPayload().getOperationsList().size());
  var op = cmnCfgCollectorMessage.getPayload().getOperationsList().get(0);
  assertEquals(instanceId, op.getId());
  assertEquals(EsConstants.Index.SYSLOG_SERVER_PROFILE_INDEX_NAME, op.getIndex());
  assertEquals(OpType.MOD, op.getOpType());
  assertEquals(tenantId, op.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue());
  assertFalse(op.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue());
//  assertFalse(op.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue());
  }

  private void assertViewmodelOpsNotSent(String tenantId) {
  messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }
} 