package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelMinimumFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.proto.Operation.Action;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
public class ConsumeDeleteApModelMinimumFirmwareHandler {
  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private MessageCaptors messageCaptors;

  @Test
  public void testDeleteApModelMinimumFirmware(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.500") ApVersion version620) {
    String requestId = randomTxId();
    ApModelMinimumFirmware existedApModelMinimumFw = new ApModelMinimumFirmware();
    existedApModelMinimumFw.setMinimumFirmware(version620);
    existedApModelMinimumFw.setId("R550");
    repositoryUtil.createOrUpdate(existedApModelMinimumFw, tenant.getId(), randomTxId());
    RequestParams requestParams = new RequestParams();
    requestParams.addPathVariable("apModel", existedApModelMinimumFw.getId());

    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgExtendedAction.DELETE_AP_MODEL_MINIMUM_FIRMWARE,
        randomName(),
        requestParams,
        StringUtils.EMPTY);

    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenant.getId(), requestId);

    assertThat(wifiCfgChangeMessage.getPayload())
        .isNotNull()
        .extracting(WifiConfigChange::getOperationList)
        .asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
        .satisfies(ops -> assertThat(ops)
            .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasApModelMinimumFirmware)
            .filteredOn(op -> op.getAction() == Action.DELETE)
            .hasSize(1)
            .singleElement()
            .extracting(op -> op.getApModelMinimumFirmware().getId().getValue())
            .isEqualTo(existedApModelMinimumFw.getId()));
  }
}
