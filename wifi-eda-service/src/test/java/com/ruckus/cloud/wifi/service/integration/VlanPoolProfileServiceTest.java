package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.VlanPoolTestFixture.copyOf;
import static java.util.Collections.emptyList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.cloud.wifi.eda.service.VlanPoolProfileServiceCtrl;
import com.ruckus.cloud.wifi.eda.service.WifiNetworkServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.VlanPoolGenerator;
import com.ruckus.cloud.wifi.mapper.VlanPoolMerger;
import com.ruckus.cloud.wifi.mapper.VlanPoolMergerImpl;
import com.ruckus.cloud.wifi.repository.NetworkApGroupRadioRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.VlanPoolRepository;
import com.ruckus.cloud.wifi.repository.WlanRepository;
import com.ruckus.cloud.wifi.service.ExtendedApGroupServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedNetworkServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedNetworkVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.impl.VlanPoolProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedNetworkVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.function.Consumer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;

@Tag("VlanPoolProfileTest")
@WifiJpaDataTest
public class VlanPoolProfileServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private VlanPoolProfileServiceCtrl vlanPoolProfileService;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Test
  void nonNullTest() {
    Assertions.assertNotNull(vlanPoolProfileService);
  }

  @Nested
  class TestAddVlanPool {

    private final VlanPoolGenerator generator = Generators.vlanPool();

    @Test
    void sunnyDay(final Tenant tenant) throws Exception {
      // Given
      final VlanPool vlanPool = generator.generate();

      // When
      final VlanPool savedVlanPool = vlanPoolProfileService.addVlanPoolProfile(vlanPool);

      // Then
      assertThat(savedVlanPool).isNotNull()
          .isNotEqualTo(vlanPool).satisfies(assertVlanPoolSoftly(vlanPool));

      // Then
      assertThat(repositoryUtil.find(VlanPool.class, vlanPool.getId(), tenant.getId()))
          .isNotNull().satisfies(assertVlanPoolSoftly(vlanPool));
    }

    @Test
    void sunnyDayWithoutAutoGeneratedId(final Tenant tenant) throws Exception {
      // Given
      final VlanPool vlanPool = Generators.vlanPool().setId(always(null)).generate();

      // When
      final VlanPool savedVlanPool = vlanPoolProfileService.addVlanPoolProfile(vlanPool);

      // Then
      assertThat(savedVlanPool).isNotNull()
          .isEqualTo(
              vlanPool); // different behavior in org.springframework.data.jpa.repository.support.SimpleJpaRepository.save

      // Then
      assertThat(repositoryUtil.find(VlanPool.class, vlanPool.getId(), tenant.getId()))
          .isNotNull().satisfies(assertVlanPoolSoftly(vlanPool));
    }

    @Test
    void sunnyDayWithNullVlanMembers(final Tenant tenant) throws Exception {
      // Given
      final VlanPool vlanPool = generator.generate();
      vlanPool.setVlanMembers(null);

      // When
      final VlanPool savedVlanPool = vlanPoolProfileService.addVlanPoolProfile(vlanPool);

      // Then
      assertThat(savedVlanPool).isNotNull()
          .isNotEqualTo(vlanPool).satisfies(assertVlanPoolSoftly(vlanPool));

      // Then
      final VlanPool expectedVlanPool = copyOf(vlanPool, vp -> vp.setVlanMembers(emptyList()));
      assertThat(repositoryUtil.find(VlanPool.class, vlanPool.getId(), tenant.getId()))
          .isNotNull().satisfies(assertVlanPoolSoftly(expectedVlanPool));
    }

    @Test
    void sunnyDayWithEmptyVlanMembers(final Tenant tenant) throws Exception {
      // Given
      final VlanPool vlanPool = generator.generate();
      vlanPool.setVlanMembers(emptyList());

      // When
      final VlanPool savedVlanPool = vlanPoolProfileService.addVlanPoolProfile(vlanPool);

      // Then
      assertThat(savedVlanPool).isNotNull()
          .isNotEqualTo(vlanPool).satisfies(assertVlanPoolSoftly(vlanPool));

      // Then
      assertThat(repositoryUtil.find(VlanPool.class, vlanPool.getId(), tenant.getId()))
          .isNotNull().satisfies(assertVlanPoolSoftly(vlanPool));
    }
  }

  private Consumer<VlanPool> assertVlanPoolSoftly(final VlanPool expected) {
    return (final VlanPool actual) -> assertSoftly(softly -> {
      softly.assertThat(actual.getId()).isEqualTo(expected.getId());
      softly.assertThat(actual.getName()).isEqualTo(expected.getName());
      softly.assertThat(actual.getDescription()).isEqualTo(expected.getDescription());
      softly.assertThat(actual.getVlanMembers()).isEqualTo(expected.getVlanMembers());
    });
  }

  @TestConfiguration
  @Import({
      ExtendedNetworkVenueServiceCtrlImplTestConfig.class
  })
  static class TestConfig {

    @Bean
    VlanPoolProfileServiceCtrl vlanPoolProfileService(
        VlanPoolMerger vlanPoolMerger,
        NetworkRepository networkRepository,
        VlanPoolRepository vlanPoolRepository,
        ExtendedApGroupServiceCtrl apGroupService,
        NetworkApGroupRadioRepository networkApGroupRadioRepository,
        WlanRepository wlanRepository,
        NetworkVenueRepository networkVenueRepository,
        @Lazy WifiNetworkServiceCtrl wifiNetworkServiceCtrl,
        @Lazy ExtendedNetworkVenueServiceCtrl extendedNetworkVenueService,
        @Lazy ExtendedNetworkServiceCtrl extendedNetworkServiceCtrl,
        @Value("${default.vlan-pool.tenant-max-count}") long vlanPoolTenantMaxCount,
        @Value("${default.vlan-pool.max-vlan-members}") long vlanPoolMaxVlanMembers) {
      return new VlanPoolProfileServiceCtrlImpl(vlanPoolMerger, networkRepository,
          vlanPoolRepository,
          apGroupService, networkApGroupRadioRepository,
          wlanRepository, networkVenueRepository, wifiNetworkServiceCtrl,
          extendedNetworkVenueService, extendedNetworkServiceCtrl,
          vlanPoolTenantMaxCount, vlanPoolMaxVlanMembers);
    }

    @Bean
    VlanPoolMerger vlanPoolMerger() {
      return new VlanPoolMergerImpl();
    }
  }
}
