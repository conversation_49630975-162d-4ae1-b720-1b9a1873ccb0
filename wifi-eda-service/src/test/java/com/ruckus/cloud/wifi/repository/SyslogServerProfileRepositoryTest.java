package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
  INSERT INTO tenant (id, name) VALUES
    ('ecTenant', 'ecTenant'), ('mspTenant', 'mspTenant');
  INSERT INTO syslog_server_profile (id, name, is_template, tenant) VALUES
    ('syslogProfile', 'syslogProfile', false, 'ecTenant'),
    ('syslogProfile2', 'syslogProfile2', false, 'ecTenant'),
    ('ecProfile', 'ecProfile', true, 'ecTenant'),
    ('mspProfile', 'mspProfile', true, 'mspTenant');
""")
class SyslogServerProfileRepositoryTest {
  final String EC_TENANT_ID = "ecTenant";
  final String MSP_TENANT_ID = "mspTenant";

  @Autowired
  SyslogServerProfileRepository unit;

  @ApplyTemplateFilter
  @Nested
  class InTemplateFlow {

    @Test
    void findByIdAndTenantId() {
      var ecProfile = unit.findByIdAndTenantId("ecProfile", EC_TENANT_ID);
      assertTrue(ecProfile.isPresent());
      assertEquals("ecProfile", ecProfile.get().getId());

      var mspProfile = unit.findByIdAndTenantId("mspProfile", MSP_TENANT_ID);
      assertTrue(mspProfile.isPresent());
      assertEquals("mspProfile", mspProfile.get().getId());
    }

    @Test
    void countByTenantId() {
      assertEquals(1, unit.countByTenantId(EC_TENANT_ID));
      assertEquals(1, unit.countByTenantId(MSP_TENANT_ID));
    }

    @Test
    void findByTenantIdAndName() {
      var ecProfile = unit.findByTenantIdAndName(EC_TENANT_ID, "ecProfile");
      assertEquals("ecProfile", ecProfile.getId());

      var mspProfile = unit.findByTenantIdAndName(MSP_TENANT_ID, "mspProfile");
      assertEquals("mspProfile", mspProfile.getId());
    }
  }

  @Nested
  class OutOfTemplateFlow {

    @Test
    void findByIdAndTenantId() {
      var syslogProfile = unit.findByIdAndTenantId("syslogProfile", EC_TENANT_ID);
      assertTrue(syslogProfile.isPresent());
      assertEquals("syslogProfile", syslogProfile.get().getId());
    }

    @Test
    void countByTenantId() {
      assertEquals(2, unit.countByTenantId(EC_TENANT_ID));
    }

    @Test
    void findByTenantIdAndName() {
      var syslogProfile = unit.findByTenantIdAndName(EC_TENANT_ID, "syslogProfile");
      assertEquals("syslogProfile", syslogProfile.getId());
    }
  }
}