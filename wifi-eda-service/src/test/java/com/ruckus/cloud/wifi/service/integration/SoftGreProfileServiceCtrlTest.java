package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.core.autoconfig.HibernateAutoConfiguration;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.service.SoftGreProfileServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.mapper.ClientIsolationLanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.mapper.DhcpOption82LanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.mapper.SoftGreProfileLanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.mapper.SoftGreProfileMerger;
import com.ruckus.cloud.wifi.mapper.SoftGreProfileMergerImpl;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.ApModelSpecificRepository;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.EthernetPortProfileRepository;
import com.ruckus.cloud.wifi.repository.IpsecProfileRepository;
import com.ruckus.cloud.wifi.repository.LanPortAdoptionRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.PinProfileNetworkMappingRepository;
import com.ruckus.cloud.wifi.repository.SdLanProfileNetworkMappingRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileLanPortActivationRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileNetworkVenueActivationRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileRepository;
import com.ruckus.cloud.wifi.repository.VenueApModelSpecificAttributesRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.service.FirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.LanPortAdoptionService;
import com.ruckus.cloud.wifi.service.TunnelCheckService;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.ClientIsolationLanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.DhcpOption82LanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.EntityIdGenerationLockServiceImpl;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.service.impl.SoftGreProfileLanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.SoftGreProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.TunnelCheckServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.validator.ApCapabilityValidator;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@Tag("SoftGreProfileTest")
@WifiJpaDataTest
class SoftGreProfileServiceCtrlTest {

  @Autowired private LanPortAdoptionDataHelper dataHelper;
  @Autowired private RepositoryUtil repositoryUtil;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private NetworkRepository networkRepository;
  @Autowired
  private NetworkVenueRepository networkVenueRepository;
  @Autowired
  private SoftGreProfileRepository softGreProfileRepository;

  @Autowired
  private SoftGreProfileNetworkVenueActivationRepository
      softGreProfileNetworkVenueActivationRepository;

  @Autowired
  private SoftGreProfileServiceCtrl softGreProfileService;

  @Autowired
  private VenueApModelSpecificAttributesRepository venueApModelSpecificAttributesRepository;
  @Autowired
  private LanPortAdoptionRepository lanPortAdoptionRepository;
  @Autowired
  private VenueLanPortRepository venueLanPortRepository;
  @Autowired
  private EthernetPortProfileRepository ethernetPortProfileRepository;
  @Autowired
  private LanPortAdoptionService lanPortAdoptionService;
  @Autowired
  private SoftGreProfileLanPortActivationRepository softGreProfileLanPortActivationRepository;
  @Autowired
  private ApLanPortRepository apLanPortRepository;
  @Autowired
  private ApModelSpecificRepository apModelSpecificRepository;
  @Autowired
  private ApCapabilityValidator apCapabilityValidator;

  @Test
  void addSoftGreProfile(Tenant tenant) throws Exception {
    var profile = new SoftGreProfile();
    profile.setName("name");
    profile.setPrimaryGateway("*******");

    profile = softGreProfileService.addSoftGreProfile(profile);
    assertNotNull(profile);
    assertNotNull(profile.getId());
    var result = softGreProfileService.getSoftGreProfile(profile.getId());
    assertEquals(profile.getId(), result.getId());

    var profile2 = new SoftGreProfile();
    profile2.setName("name");
    profile2.setPrimaryGateway("*******");
    assertThrows(
        InvalidPropertyValueException.class,
        () -> softGreProfileService.addSoftGreProfile(profile2));
  }

  @Test
  void updateSoftGreProfile(Tenant tenant) throws Exception {
    var existedProfile = new SoftGreProfile();
    existedProfile.setName("My Profile");
    existedProfile.setPrimaryGateway("*******");
    softGreProfileService.addSoftGreProfile(existedProfile);

    var profileToUpdate = new SoftGreProfile();
    profileToUpdate.setName("name");
    profileToUpdate.setPrimaryGateway("*******");
    profileToUpdate = softGreProfileService.addSoftGreProfile(profileToUpdate);
    var profileToUpdateId = profileToUpdate.getId();

    profileToUpdate = new SoftGreProfile();
    profileToUpdate.setName("name - updated");
    profileToUpdate.setPrimaryGateway("*******");
    profileToUpdate =
        softGreProfileService.updateSoftGreProfile(profileToUpdateId, profileToUpdate);
    assertEquals(profileToUpdateId, profileToUpdate.getId());
    assertEquals("*******", profileToUpdate.getPrimaryGateway());

    var profileToUpdate2 = new SoftGreProfile();
    profileToUpdate2.setName("My Profile");
    profileToUpdate2.setPrimaryGateway("3.3.3.3");
    assertThrows(
        InvalidPropertyValueException.class,
        () -> softGreProfileService.updateSoftGreProfile(profileToUpdateId, profileToUpdate2));
  }

  @Test
  void updateSoftGreProfileHasOtherNetworkVenueActivations(Tenant tenant, NetworkVenue networkVenue)
      throws Exception {
    var profile = new SoftGreProfile();
    profile.setName("My Profile");
    profile.setPrimaryGateway("*******");
    profile = softGreProfileService.addSoftGreProfile(profile);
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        networkVenue.getVenue().getId(), networkVenue.getNetwork().getId(), profile.getId());

    var network = new Network();
    network.setName("2nd Network");
    network = networkRepository.save(network);
    var networkVenue2 = new NetworkVenue();
    networkVenue2.setNetwork(network);
    networkVenue2.setVenue(networkVenue.getVenue());
    networkVenue2 = networkVenueRepository.save(networkVenue2);
    var profile2 = new SoftGreProfile();
    profile2.setName("My Profile 2");
    profile2.setPrimaryGateway("*******");
    profile2 = softGreProfileService.addSoftGreProfile(profile2);
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        networkVenue2.getVenue().getId(), networkVenue2.getNetwork().getId(), profile2.getId());

    var profileToUpdate = new SoftGreProfile();
    profileToUpdate.setName("My Profile");
    profileToUpdate.setPrimaryGateway("*******");
    profileToUpdate.setSecondaryGateway("*******");
    var profileId = profile.getId();
    var exception =
        assertThrows(
            InvalidPropertyValueException.class,
            () -> softGreProfileService.updateSoftGreProfile(profileId, profileToUpdate));
    assertTrue(
        exception
            .getMessage()
            .contains(
                "SoftGRE profiles are not allowed to have duplicate gateway addresses with other activated SoftGRE profiles with the same venue. Duplicated SoftGRE profiles:"));
  }

  @Test
  void updateSoftGreProfileHasOtherVenueLanPortActivations(Venue venue) {
    var profile1 =
        dataHelper.createSoftGreProfile(venue.getTenant(), p -> p.setPrimaryGateway("*******"));
    var portData1 =
        dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-01", "1", 1, of(profile1));
    var profile2 =
        dataHelper.createSoftGreProfile(venue.getTenant(), p -> p.setPrimaryGateway("*******"));
    var portData2 =
        dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-02", "1", 2, of(profile2));

    var profileToUpdate = new SoftGreProfile();
    profileToUpdate.setName("My Profile");
    profileToUpdate.setPrimaryGateway("*******");
    profileToUpdate.setSecondaryGateway("*******");
    var profileId = profile1.getId();
    var exception =
        assertThrows(
            InvalidPropertyValueException.class,
            () -> softGreProfileService.updateSoftGreProfile(profileId, profileToUpdate));
    assertTrue(
        exception
            .getMessage()
            .contains(
                "SoftGRE profiles are not allowed to have duplicate gateway addresses with other activated SoftGRE profiles with the same venue. Duplicated SoftGRE profiles:"));
  }

  @Test
  void updateSoftGreProfileHasOtherApLanPortActivations(Venue venue, Ap ap) {
    var profile1 =
        dataHelper.createSoftGreProfile(venue.getTenant(), p -> p.setPrimaryGateway("*******"));
    var portData1 = dataHelper.createApLanPortDataWithAdoption(venue, ap, "1", 1, of(profile1));
    var profile2 =
        dataHelper.createSoftGreProfile(venue.getTenant(), p -> p.setPrimaryGateway("*******"));
    var portData2 = dataHelper.createApLanPortDataWithAdoption(venue, ap, "2", 2, of(profile2));

    var profileToUpdate = new SoftGreProfile();
    profileToUpdate.setName("My Profile");
    profileToUpdate.setPrimaryGateway("*******");
    profileToUpdate.setSecondaryGateway("*******");
    var profileId = profile1.getId();
    var exception =
        assertThrows(
            InvalidPropertyValueException.class,
            () -> softGreProfileService.updateSoftGreProfile(profileId, profileToUpdate));
    assertTrue(
        exception
            .getMessage()
            .contains(
                "SoftGRE profiles are not allowed to have duplicate gateway addresses with other activated SoftGRE profiles with the same venue. Duplicated SoftGRE profiles:"));
  }

  @Test
  void deleteSoftGreProfile(Tenant tenant) throws Exception {
    var profile = dataHelper.createSoftGreProfile(tenant, p -> p.setPrimaryGateway("*******"));

    softGreProfileService.deleteSoftGreProfile(profile.getId());
    assertThrows(
        ObjectNotFoundException.class,
        () -> softGreProfileService.getSoftGreProfile(profile.getId()));
  }

  @Test
  void deleteSoftGreProfileWithUnusedLanPortAdoptions(Tenant tenant) throws Exception {
    var profile = dataHelper.createSoftGreProfile(tenant, p -> p.setPrimaryGateway("*******"));
    dataHelper.createLanPortAdoption(tenant, dataHelper.createEthernetPortProfile(tenant, 777));
    dataHelper.createLanPortAdoption(
        tenant,
        dataHelper.createEthernetPortProfile(tenant, 888),
        of(dataHelper.createSoftGreProfile(tenant)));
    dataHelper.createLanPortAdoption(
        tenant,
        dataHelper.createEthernetPortProfile(tenant, 999),
        of(new ClientIsolationLanPortActivation()));

    softGreProfileService.deleteSoftGreProfile(profile.getId());

    assertTrue(repositoryUtil.findAll(LanPortAdoption.class, tenant.getId()).isEmpty());
    assertTrue(
        repositoryUtil.findAll(SoftGreProfileLanPortActivation.class, tenant.getId()).isEmpty());
    assertTrue(
        repositoryUtil.findAll(ClientIsolationLanPortActivation.class, tenant.getId()).isEmpty());
    assertThrows(
        ObjectNotFoundException.class,
        () -> softGreProfileService.getSoftGreProfile(profile.getId()));
  }

  @Test
  void deleteSoftGreProfileButActivatedOnNetworkVenue(Tenant tenant, NetworkVenue networkVenue)
      throws Exception {
    var profile = dataHelper.createSoftGreProfile(tenant, p -> p.setPrimaryGateway("*******"));
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        networkVenue.getVenue().getId(), networkVenue.getNetwork().getId(), profile.getId());

    assertThrows(
        ObjectInUseException.class,
        () -> softGreProfileService.deleteSoftGreProfile(profile.getId()));
  }

  @Test
  void deleteSoftGreProfileButActivatedOnVenueLanPort(Tenant tenant, Venue venue) throws Exception {
    var profile = dataHelper.createSoftGreProfile(tenant, p -> p.setPrimaryGateway("*******"));
    var portData =
        dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-01", "1", 1, of(profile));

    assertThrows(
        ObjectInUseException.class,
        () -> softGreProfileService.deleteSoftGreProfile(profile.getId()));
  }

  @Test
  void deleteSoftGreProfileButActivatedOnApLanPort(Venue venue, Ap ap) {
    var profile =
        dataHelper.createSoftGreProfile(venue.getTenant(), p -> p.setPrimaryGateway("*******"));
    var portData = dataHelper.createApLanPortDataWithAdoption(venue, ap, "1", 1, of(profile));

    assertThrows(
        ObjectInUseException.class,
        () -> softGreProfileService.deleteSoftGreProfile(profile.getId()));
  }

  @Test
  void activateSoftGreProfileOnNetworkVenue(Tenant tenant, NetworkVenue networkVenue)
      throws Exception {
    var profile = new SoftGreProfile();
    profile.setName("name");
    profile.setPrimaryGateway("*******");
    profile = softGreProfileService.addSoftGreProfile(profile);
    var networkId = networkVenue.getNetwork().getId();
    var venueId = networkVenue.getVenue().getId();
    var profileId = profile.getId();

    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(venueId, networkId, profileId);
    var activation =
        softGreProfileNetworkVenueActivationRepository
            .findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId(
                tenant.getId(), networkId, venueId, profileId);
    assertTrue(activation.isPresent());
    assertNotNull(activation.get().getId());
  }

  @Test
  void activateSoftGreProfileOnNetworkVenueActivatedAlready(
      Tenant tenant, NetworkVenue networkVenue) throws Exception {
    var profile1 = new SoftGreProfile();
    profile1.setName("name");
    profile1.setPrimaryGateway("*******");
    profile1 = softGreProfileService.addSoftGreProfile(profile1);
    var networkId = networkVenue.getNetwork().getId();
    var venueId = networkVenue.getVenue().getId();
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        venueId, networkId, profile1.getId());
    var activations =
        softGreProfileNetworkVenueActivationRepository.findByTenantIdAndSoftGreProfileId(
            tenant.getId(), profile1.getId());
    assertEquals(1, activations.size());
    assertEquals(networkVenue, activations.get(0).getNetworkVenue());
    assertEquals(profile1, activations.get(0).getSoftGreProfile());
    var activation =
        softGreProfileNetworkVenueActivationRepository
            .findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId(
                tenant.getId(), networkId, venueId, profile1.getId());
    assertTrue(activation.isPresent());
    var profiles =
        softGreProfileRepository.findAllNetworkVenueActivatedSoftGreProfiles(
            tenant.getId(), venueId);
    assertEquals(1, profiles.size());
    assertEquals(profile1.getId(), profiles.get(0).getId());

    var profile2 = new SoftGreProfile();
    profile2.setName("name2");
    profile2.setPrimaryGateway("1.1.1.2");
    profile2 = softGreProfileService.addSoftGreProfile(profile2);
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        venueId, networkId, profile2.getId());
    activations =
        softGreProfileNetworkVenueActivationRepository.findByTenantIdAndSoftGreProfileId(
            tenant.getId(), profile2.getId());
    assertEquals(1, activations.size());
    assertEquals(networkVenue, activations.get(0).getNetworkVenue());
    assertEquals(profile2, activations.get(0).getSoftGreProfile());
    activation =
        softGreProfileNetworkVenueActivationRepository
            .findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId(
                tenant.getId(), networkId, venueId, profile2.getId());
    assertTrue(activation.isPresent());
    profiles =
        softGreProfileRepository.findAllNetworkVenueActivatedSoftGreProfiles(
            tenant.getId(), venueId);
    assertEquals(1, profiles.size());
    assertEquals(profile2.getId(), profiles.get(0).getId());

    var profile3 = new SoftGreProfile();
    profile3.setName("name3");
    profile3.setPrimaryGateway("1.1.1.2");
    profile3 = softGreProfileService.addSoftGreProfile(profile3);
    var profile3Id = profile3.getId();
    assertDoesNotThrow(() -> softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
                venueId, networkId, profile3Id));
  }

  @Test
  void activateSoftGreProfileOnNetworkVenueButExceedMaxSoftGreProfileOnVenueCount(
      Tenant tenant, Venue venue) throws Exception {
    // N1 - V - SoftGreProfile 1
    var network1 = new Network();
    network1.setName("Network1");
    network1 = networkRepository.save(network1);
    var networkVenue1 = new NetworkVenue();
    networkVenue1.setNetwork(network1);
    networkVenue1.setVenue(venue);
    networkVenueRepository.save(networkVenue1);
    var profile1 = new SoftGreProfile();
    profile1.setName("Profile1");
    profile1.setPrimaryGateway("*******");
    profile1 = softGreProfileService.addSoftGreProfile(profile1);
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        venue.getId(), network1.getId(), profile1.getId());

    // N2 - V - SoftGreProfile 2
    var network2 = new Network();
    network2.setName("Network2");
    network2 = networkRepository.save(network2);
    var networkVenue2 = new NetworkVenue();
    networkVenue2.setNetwork(network2);
    networkVenue2.setVenue(venue);
    networkVenueRepository.save(networkVenue2);
    var profile2 = new SoftGreProfile();
    profile2.setName("Profile2");
    profile2.setPrimaryGateway("1.1.1.2");
    profile2 = softGreProfileService.addSoftGreProfile(profile2);
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        venue.getId(), network2.getId(), profile2.getId());

    // V - ApModelLanPort - SoftGreProfile 3
    var profile3 = new SoftGreProfile();
    profile3.setName("Profile3");
    profile3.setPrimaryGateway("1.1.1.3");
    profile3 = softGreProfileService.addSoftGreProfile(profile3);
    var portData = dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-1", "1", 6);
    var softGreProfileLanPortActivation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
            .softGreProfileLanPortActivation()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(
                always(
                    com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                        .dhcpOption82Settings()
                        .generate()))
            .generate();
    softGreProfileService.activateSoftGreProfileOnVenueApModelLanPort(
        venue.getId(),
        portData.modelSpecific().getModel(),
        portData.port().getPortId(),
        profile3.getId(),
        softGreProfileLanPortActivation);

    // N5 - V - SoftGreProfile 2
    var network5 = new Network();
    network5.setName("Network5");
    network5 = networkRepository.save(network5);
    var networkVenue5 = new NetworkVenue();
    networkVenue5.setNetwork(network5);
    networkVenue5.setVenue(venue);
    networkVenueRepository.save(networkVenue5);
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        venue.getId(), network5.getId(), profile2.getId());

    // N4 - V - SoftGreProfile 4
    var network4 = new Network();
    network4.setName("Network4");
    network4 = networkRepository.save(network4);
    var networkVenue4 = new NetworkVenue();
    networkVenue4.setNetwork(network4);
    networkVenue4.setVenue(venue);
    networkVenueRepository.save(networkVenue4);
    var profile4 = new SoftGreProfile();
    profile4.setName("Profile4");
    profile4.setPrimaryGateway("1.1.1.4");
    profile4 = softGreProfileService.addSoftGreProfile(profile4);
    var network4Id = network4.getId();
    var profile4Id = profile4.getId();
    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () ->
                softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
                    venue.getId(), network4Id, profile4Id))
        .withMessage(
            "The Venue[%s] activated %s SoftGreProfiles already.".formatted(venue.getId(), 3));

    // N1 - V - SoftGreProfile 4 | Replace SoftGreProfile 1
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        venue.getId(), network1.getId(), profile4Id);
  }

  @Test
  void activateSoftGreProfileOnNetworkVenueButDuplicateGatewayAddresses(
      Venue venue) throws Exception {
    // N1 - V - SoftGreProfile 1
    var network1 = new Network();
    network1.setName("Network1");
    network1 = networkRepository.save(network1);
    var networkVenue1 = new NetworkVenue();
    networkVenue1.setNetwork(network1);
    networkVenue1.setVenue(venue);
    networkVenueRepository.save(networkVenue1);
    var profile1 = new SoftGreProfile();
    profile1.setName("Profile1");
    profile1.setPrimaryGateway("*******");
    profile1 = softGreProfileService.addSoftGreProfile(profile1);
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
        venue.getId(), network1.getId(), profile1.getId());

    // N2 - V - SoftGreProfile 2
    var network2 = new Network();
    network2.setName("Network2");
    network2 = networkRepository.save(network2);
    var network2Id = network2.getId();
    var networkVenue2 = new NetworkVenue();
    networkVenue2.setNetwork(network2);
    networkVenue2.setVenue(venue);
    networkVenueRepository.save(networkVenue2);
    var profile2 = new SoftGreProfile();
    profile2.setName("Profile2");
    profile2.setPrimaryGateway("*******");
    profile2 = softGreProfileService.addSoftGreProfile(profile2);
    var profile2Id = profile2.getId();

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(() -> softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
              venue.getId(), network2Id, profile2Id))
        .withMessage("The SoftGreProfile[%s] to be bound is not allowed to have duplicate gateway addresses with other activated SoftGRE profiles with the same Venue[%s]."
            .formatted(profile2Id, venue.getId()));
  }

  @Test
  void activateSoftGreProfileOnCaptivePortalNetwork(Tenant tenant, Venue venue) throws Exception {
    // N1 - V - SoftGreProfile 1
    var network = new Network();
    network.setType(NetworkTypeEnum.GUEST);
    network.setName("Network");
    network = networkRepository.save(network);
    var networkId = network.getId();
    var networkVenue = new NetworkVenue();
    networkVenue.setNetwork(network);
    networkVenue.setVenue(venue);
    networkVenueRepository.save(networkVenue);
    var profile = new SoftGreProfile();
    profile.setName("Profile1");
    profile.setPrimaryGateway("*******");
    profile = softGreProfileService.addSoftGreProfile(profile);
    var profileId = profile.getId();
    assertThrows(
        InvalidPropertyValueException.class,
        () ->
            softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(
                venue.getId(), networkId, profileId));
  }

  @Test
  void deactivateSoftGreProfileOnNetworkVenue(Tenant tenant, NetworkVenue networkVenue)
      throws Exception {
    var profile = new SoftGreProfile();
    profile.setName("name");
    profile.setPrimaryGateway("*******");
    profile = softGreProfileService.addSoftGreProfile(profile);
    var networkId = networkVenue.getNetwork().getId();
    var venueId = networkVenue.getVenue().getId();
    var profileId = profile.getId();
    softGreProfileService.activateSoftGreProfileOnVenueWifiNetwork(venueId, networkId, profileId);

    softGreProfileService.deactivateSoftGreProfileOnVenueWifiNetwork(venueId, networkId, profileId);
    assertTrue(
        softGreProfileNetworkVenueActivationRepository
            .findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId(
                tenant.getId(), networkId, venueId, profileId)
            .isEmpty());
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void activateSoftGreProfileOnVenueApModelLanPort(Venue venue) throws Exception {
    var profile = new SoftGreProfile();
    profile.setName("name");
    profile.setPrimaryGateway("*******");
    profile = softGreProfileService.addSoftGreProfile(profile);
    var softGreProfileLanPortActivation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
            .softGreProfileLanPortActivation()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(
                always(
                    com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                        .dhcpOption82Settings()
                        .generate()))
            .generate();

    var portData1 = dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-1", "2", 3);
    var venueId = venue.getId();
    var apModel = portData1.modelSpecific().getModel();
    var portId = portData1.port().getPortId();
    var venueLanPortId = portData1.port().getId();
    var softGreProfileId = profile.getId();

    softGreProfileService.activateSoftGreProfileOnVenueApModelLanPort(
        venueId, apModel, portId, profile.getId(), softGreProfileLanPortActivation);

    final var venueLanPort =
        venueLanPortRepository
            .findByIdAndTenantId(venueLanPortId, txCtxExtension.getTenantId())
            .get();
    assertThat(venueLanPort)
        .isNotNull()
        .extracting(VenueLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getSoftGreActivation)
        .isNotNull()
        .matches(activation -> activation.getSoftGreProfile().getId().equals(softGreProfileId))
        .matches(
            activation ->
                activation
                    .getDhcpOption82Enabled()
                    .equals(softGreProfileLanPortActivation.getDhcpOption82Enabled()))
        .matches(
            activation -> {
              var settings = activation.getDhcpOption82Settings();
              var expectedSettings = softGreProfileLanPortActivation.getDhcpOption82Settings();
              return settings.getSubOption1Enabled().equals(expectedSettings.getSubOption1Enabled())
                  && settings.getSubOption2Enabled().equals(expectedSettings.getSubOption2Enabled())
                  && settings
                      .getSubOption150Enabled()
                      .equals(expectedSettings.getSubOption150Enabled())
                  && settings
                      .getSubOption151Enabled()
                      .equals(expectedSettings.getSubOption151Enabled())
                  && settings.getMacFormat() == expectedSettings.getMacFormat()
                  && settings.getSubOption2Format() == expectedSettings.getSubOption2Format()
                  && settings.getSubOption151Format() == expectedSettings.getSubOption151Format()
                  && settings
                      .getSubOption151Input()
                      .equals(expectedSettings.getSubOption151Input());
            });
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void activateSoftGreProfileOnVenueApModelLanPortButExceedMaxSoftGreProfileOnVenueCount(
      Venue venue) throws Exception {
    var profile1 = dataHelper.createSoftGreProfile(venue.getTenant());
    var profile2 = dataHelper.createSoftGreProfile(venue.getTenant());
    var profile3 = dataHelper.createSoftGreProfile(venue.getTenant());
    var profile4 = dataHelper.createSoftGreProfile(venue.getTenant());
    var portData1 =
        dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-1", "1", 3, of(profile1));
    var portData2 =
        dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-2", "2", 4, of(profile2));
    var portData3 =
        dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-3", "3", 5, of(profile3));
    var portData4 = dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-4", "4", 6);
    var softGreProfileLanPortActivation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
            .softGreProfileLanPortActivation()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(
                always(
                    com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                        .dhcpOption82Settings()
                        .generate()))
            .generate();

    var venueId = venue.getId();
    var apModel = portData4.modelSpecific().getModel();
    var portId = portData4.port().getPortId();
    var softGreProfileId = profile4.getId();

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () ->
                softGreProfileService.activateSoftGreProfileOnVenueApModelLanPort(
                    venueId, apModel, portId, softGreProfileId, softGreProfileLanPortActivation))
        .withMessage("The Venue[%s] activated %s SoftGreProfiles already.".formatted(venueId, 3));
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void deactivateSoftGreProfileOnVenueApModelLanPort(Venue venue) throws Exception {
    var softGreProfile = dataHelper.createSoftGreProfile(venue.getTenant());
    var portData1 =
        dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-1", "2", 3, of(softGreProfile));
    var venueId = venue.getId();
    var apModel = portData1.modelSpecific().getModel();
    var portId = portData1.port().getPortId();
    var venueLanPortId = portData1.port().getId();

    softGreProfileService.deactivateSoftGreProfileOnVenueApModelLanPort(venueId, apModel, portId,
        softGreProfile.getId());

    final var venueLanPort = venueLanPortRepository.findByIdAndTenantId(venueLanPortId,
        txCtxExtension.getTenantId()).get();
    assertThat(venueLanPort)
        .isNotNull()
        .extracting(VenueLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getSoftGreActivation)
        .isNull();
    var lanPortAdoptions = lanPortAdoptionRepository.findByTenantId(txCtxExtension.getTenantId());
    assertEquals(1, lanPortAdoptions.size());

    var softGreProfileLanPortActivations = softGreProfileLanPortActivationRepository.findByTenantId(
        txCtxExtension.getTenantId());
    assertEquals(0, softGreProfileLanPortActivations.size());
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void deactivateSoftGreProfileOnVenueApModelLanPortButNotActivateSoftGre(Venue venue) throws Exception {
    var softGreProfile = dataHelper.createSoftGreProfile(venue.getTenant());
    var portData1 = dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-1", "2", 3);
    var venueId = venue.getId();
    var apModel = portData1.modelSpecific().getModel();
    var portId = portData1.port().getPortId();
    var venueLanPortId = portData1.port().getId();

    softGreProfileService.deactivateSoftGreProfileOnVenueApModelLanPort(venueId, apModel, portId,
        softGreProfile.getId());

    final var venueLanPort = venueLanPortRepository.findByIdAndTenantId(venueLanPortId,
        txCtxExtension.getTenantId()).get();
    assertThat(venueLanPort)
        .isNotNull()
        .extracting(VenueLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getSoftGreActivation)
        .isNull();
    var lanPortAdoptions = lanPortAdoptionRepository.findByTenantId(txCtxExtension.getTenantId());
    assertEquals(1, lanPortAdoptions.size());

    var softGreProfileLanPortActivations = softGreProfileLanPortActivationRepository.findByTenantId(
        txCtxExtension.getTenantId());
    assertEquals(0, softGreProfileLanPortActivations.size());
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void deactivateSoftGreProfileOnVenueApModelLanPortButNotActivateEthernetPortProfile(Venue venue) throws Exception {
    var softGreProfile = dataHelper.createSoftGreProfile(venue.getTenant());
    var portData1 =
        dataHelper.createVenueLanPortDataWithAdoption(
            venue,
            "TEST-1",
            "2",
            3,
            of(
                com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                    .softGreProfileLanPortActivation()
                    .setDhcpOption82Enabled(always(true))
                    .setDhcpOption82Settings(
                        always(
                            com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                                .dhcpOption82Settings()
                                .generate()))
                    .setSoftGreProfile(always(softGreProfile))
                    .generate()));
    var venueId = venue.getId();
    var apModel = portData1.modelSpecific().getModel();
    var venueLanPortId = portData1.port().getId();
    var softGreProfileId = softGreProfile.getId();

    softGreProfileService.deactivateSoftGreProfileOnVenueApModelLanPort(venueId, apModel, "3",
        softGreProfile.getId());

    final var venueLanPort = venueLanPortRepository.findByIdAndTenantId(venueLanPortId,
        txCtxExtension.getTenantId()).get();
    assertThat(venueLanPort)
        .isNotNull()
        .extracting(VenueLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getSoftGreActivation)
        .isNotNull()
        .matches(activation -> activation.getSoftGreProfile().getId().equals(softGreProfileId))
        .matches(activation -> activation.getDhcpOption82Enabled()
            .equals(portData1.port().getLanPortAdoption().getSoftGreActivation().getDhcpOption82Enabled()))
        .matches(activation -> {
          var settings = activation.getDhcpOption82Settings();
          var expectedSettings = portData1.port().getLanPortAdoption().getSoftGreActivation().getDhcpOption82Settings();
          return settings.getSubOption1Enabled().equals(expectedSettings.getSubOption1Enabled())
              &&
              settings.getSubOption2Enabled().equals(expectedSettings.getSubOption2Enabled()) &&
              settings.getSubOption150Enabled()
                  .equals(expectedSettings.getSubOption150Enabled()) &&
              settings.getSubOption151Enabled()
                  .equals(expectedSettings.getSubOption151Enabled()) &&
              settings.getMacFormat() == expectedSettings.getMacFormat() &&
              settings.getSubOption2Format() == expectedSettings.getSubOption2Format() &&
              settings.getSubOption151Format() == expectedSettings.getSubOption151Format() &&
              settings.getSubOption151Input().equals(expectedSettings.getSubOption151Input())
              ;
        });
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void activateSoftGreProfileOnVenueApLanPort(Venue venue, Ap ap) throws Exception {
    var profile = new SoftGreProfile();
    profile.setName("name");
    profile.setPrimaryGateway("*******");
    profile = softGreProfileService.addSoftGreProfile(profile);
    var softGreProfileLanPortActivation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.softGreProfileLanPortActivation()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(always(
                com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.dhcpOption82Settings().generate()))
            .generate();

    var portData1 = dataHelper.createApLanPortDataWithAdoption(venue, ap, "2", 3);
    var venueId = venue.getId();
    var portId = portData1.port().getPortId();
    var venueLanPortId = portData1.port().getId();
    var softGreProfileId = profile.getId();

    softGreProfileService.activateSoftGreProfileOnVenueApLanPort(venueId, ap.getId(), portId,
        profile.getId(), softGreProfileLanPortActivation);

    final var apLanPort = apLanPortRepository.findByIdAndTenantId(venueLanPortId,
        txCtxExtension.getTenantId()).get();
    assertThat(apLanPort)
        .isNotNull()
        .extracting(ApLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getSoftGreActivation)
        .isNotNull()
        .matches(activation -> activation.getSoftGreProfile().getId().equals(softGreProfileId))
        .matches(activation -> activation.getDhcpOption82Enabled()
            .equals(softGreProfileLanPortActivation.getDhcpOption82Enabled()))
        .matches(activation -> {
          var settings = activation.getDhcpOption82Settings();
          var expectedSettings = softGreProfileLanPortActivation.getDhcpOption82Settings();
          return settings.getSubOption1Enabled().equals(expectedSettings.getSubOption1Enabled())
              &&
              settings.getSubOption2Enabled().equals(expectedSettings.getSubOption2Enabled()) &&
              settings.getSubOption150Enabled()
                  .equals(expectedSettings.getSubOption150Enabled()) &&
              settings.getSubOption151Enabled()
                  .equals(expectedSettings.getSubOption151Enabled()) &&
              settings.getMacFormat() == expectedSettings.getMacFormat() &&
              settings.getSubOption2Format() == expectedSettings.getSubOption2Format() &&
              settings.getSubOption151Format() == expectedSettings.getSubOption151Format() &&
              settings.getSubOption151Input().equals(expectedSettings.getSubOption151Input())
              ;
        });
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void activateSoftGreProfileOnVenueApLanPortButExceedMaxSoftGreProfileOnVenueCount(
      Venue venue, Ap ap) throws Exception {
    var profile1 = dataHelper.createSoftGreProfile(venue.getTenant());
    var profile2 = dataHelper.createSoftGreProfile(venue.getTenant());
    var profile3 = dataHelper.createSoftGreProfile(venue.getTenant());;
    var profile4 = dataHelper.createSoftGreProfile(venue.getTenant());;
    var portData1 = dataHelper.createApLanPortDataWithAdoption(venue, ap, "1", 3, of(profile1));
    var portData2 = dataHelper.createApLanPortDataWithAdoption(venue, ap, "2", 4, of(profile2));
    var portData3 = dataHelper.createApLanPortDataWithAdoption(venue, ap, "3", 5, of(profile3));
    var portData4 = dataHelper.createApLanPortDataWithAdoption(venue, ap, "4", 6);
    var softGreProfileLanPortActivation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
            .softGreProfileLanPortActivation()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(
                always(
                    com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                        .dhcpOption82Settings()
                        .generate()))
            .generate();

    var venueId = venue.getId();
    var portId = portData4.port().getPortId();
    var softGreProfileId = profile4.getId();

    assertThatExceptionOfType(InvalidPropertyValueException.class)
        .isThrownBy(
            () ->
                softGreProfileService.activateSoftGreProfileOnVenueApLanPort(
                    venueId, ap.getId(), portId, softGreProfileId, softGreProfileLanPortActivation))
        .withMessage("The Venue[%s] activated %s SoftGreProfiles already.".formatted(venueId, 3));
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void deactivateSoftGreProfileOnVenueApLanPort(Venue venue, Ap ap) throws Exception {
    var softGreProfile = dataHelper.createSoftGreProfile(venue.getTenant());
    var portData1 =
        dataHelper.createApLanPortDataWithAdoption(venue, ap, "2", 3, of(softGreProfile));
    var venueId = venue.getId();
    var venueLanPortId = portData1.port().getId();
    var portId = portData1.port().getPortId();

    softGreProfileService.deactivateSoftGreProfileOnVenueApLanPort(
        venueId, ap.getId(), portId, softGreProfile.getId());

    final var apLanPort =
        apLanPortRepository.findByIdAndTenantId(venueLanPortId, txCtxExtension.getTenantId()).get();
    assertThat(apLanPort)
        .isNotNull()
        .extracting(ApLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getSoftGreActivation)
        .isNull();
    var lanPortAdoptions = lanPortAdoptionRepository.findByTenantId(txCtxExtension.getTenantId());
    assertEquals(1, lanPortAdoptions.size());

    var softGreProfileLanPortActivations =
        softGreProfileLanPortActivationRepository.findByTenantId(txCtxExtension.getTenantId());
    assertEquals(0, softGreProfileLanPortActivations.size());
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void deactivateSoftGreProfileOnVenueApLanPortButNotActivateSoftGre(Venue venue, Ap ap)
      throws Exception {
    var softGreProfile = dataHelper.createSoftGreProfile(venue.getTenant());
    var portData1 =
        dataHelper.createApLanPortDataWithAdoption(venue, ap, "2", 3, of(softGreProfile));
    var venueId = venue.getId();
    var venueLanPortId = portData1.port().getId();
    var portId = portData1.port().getPortId();

    softGreProfileService.deactivateSoftGreProfileOnVenueApLanPort(
        venueId, ap.getId(), portId, softGreProfile.getId());

    final var apLanPort =
        apLanPortRepository.findByIdAndTenantId(venueLanPortId, txCtxExtension.getTenantId()).get();
    assertThat(apLanPort)
        .isNotNull()
        .extracting(ApLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getSoftGreActivation)
        .isNull();
    var lanPortAdoptions = lanPortAdoptionRepository.findByTenantId(txCtxExtension.getTenantId());
    assertEquals(1, lanPortAdoptions.size());

    var softGreProfileLanPortActivations =
        softGreProfileLanPortActivationRepository.findByTenantId(txCtxExtension.getTenantId());
    assertEquals(0, softGreProfileLanPortActivations.size());
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void deactivateSoftGreProfileOnVenueApLanPortButNotActivateEthernetPortProfile(Venue venue, Ap ap)
      throws Exception {
    var softGreProfile = dataHelper.createSoftGreProfile(venue.getTenant());
    var portData1 =
        dataHelper.createApLanPortDataWithAdoption(
            venue,
            ap,
            "2",
            3,
            of(
                com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                    .softGreProfileLanPortActivation()
                    .setDhcpOption82Enabled(always(true))
                    .setDhcpOption82Settings(
                        always(
                            com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                                .dhcpOption82Settings()
                                .generate()))
                    .setSoftGreProfile(always(softGreProfile))
                    .generate()));
    var venueId = venue.getId();
    var venueLanPortId = portData1.port().getId();
    var softGreProfileId = softGreProfile.getId();

    softGreProfileService.deactivateSoftGreProfileOnVenueApLanPort(
        venueId, ap.getId(), "3", softGreProfile.getId());

    final var apLanPort =
        apLanPortRepository.findByIdAndTenantId(venueLanPortId, txCtxExtension.getTenantId()).get();
    assertThat(apLanPort)
        .isNotNull()
        .extracting(ApLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getSoftGreActivation)
        .isNotNull()
        .matches(activation -> activation.getSoftGreProfile().getId().equals(softGreProfileId))
        .matches(
            activation ->
                activation
                    .getDhcpOption82Enabled()
                    .equals(
                        portData1
                            .port()
                            .getLanPortAdoption()
                            .getSoftGreActivation()
                            .getDhcpOption82Enabled()))
        .matches(
            activation -> {
              var settings = activation.getDhcpOption82Settings();
              var expectedSettings =
                  portData1
                      .port()
                      .getLanPortAdoption()
                      .getSoftGreActivation()
                      .getDhcpOption82Settings();
              return settings.getSubOption1Enabled().equals(expectedSettings.getSubOption1Enabled())
                  && settings.getSubOption2Enabled().equals(expectedSettings.getSubOption2Enabled())
                  && settings
                      .getSubOption150Enabled()
                      .equals(expectedSettings.getSubOption150Enabled())
                  && settings
                      .getSubOption151Enabled()
                      .equals(expectedSettings.getSubOption151Enabled())
                  && settings.getMacFormat() == expectedSettings.getMacFormat()
                  && settings.getSubOption2Format() == expectedSettings.getSubOption2Format()
                  && settings.getSubOption151Format() == expectedSettings.getSubOption151Format()
                  && settings
                      .getSubOption151Input()
                      .equals(expectedSettings.getSubOption151Input());
            });
  }

  @TestConfiguration
  @Import({
    HibernateAutoConfiguration.class,
    EntityIdGenerationLockServiceImpl.class,
    SoftGreProfileLanPortActivationMapperImpl.class,
    ClientIsolationLanPortActivationMapperImpl.class,
    DhcpOption82LanPortActivationMapperImpl.class,
    SoftGreProfileLanPortActivationHandler.class,
    ClientIsolationLanPortActivationHandler.class,
    DhcpOption82LanPortActivationHandler.class,
    LanPortAdoptionServiceImpl.class,
    ExtendedVenueServiceCtrlImplTestConfig.class
  })
  static class TestConfig {

    @Bean
    TunnelCheckService tunnelCheckService(
        SdLanProfileNetworkMappingRepository sdLanProfileNetworkMappingRepository,
        PinProfileNetworkMappingRepository pinProfileNetworkMappingRepository,
        SoftGreProfileNetworkVenueActivationRepository
            softGreProfileNetworkVenueActivationRepository) {
      return new TunnelCheckServiceImpl(
          sdLanProfileNetworkMappingRepository,
          pinProfileNetworkMappingRepository,
          softGreProfileNetworkVenueActivationRepository);
    }

    @Bean
    SoftGreProfileServiceCtrl softGreProfileService(
        SoftGreProfileRepository softGreProfileRepository,
        SoftGreProfileNetworkVenueActivationRepository
            softGreProfileNetworkVenueActivationRepository,
        TunnelCheckService tunnelCheckService,
        NetworkVenueRepository networkVenueRepository,
        NetworkRepository networkRepository,
        SoftGreProfileMerger softGreProfileMerger,
        VenueLanPortRepository venueLanPortRepository,
        LanPortAdoptionService lanPortAdoptionService,
        ApLanPortRepository apLanPortRepository,
        IpsecProfileRepository ipsecProfileRepository,
        ApCapabilityValidator apCapabilityValidator) {
      return new SoftGreProfileServiceCtrlImpl(
          softGreProfileRepository,
          softGreProfileNetworkVenueActivationRepository,
          networkVenueRepository,
          networkRepository,
          tunnelCheckService,
          softGreProfileMerger,
          venueLanPortRepository,
          lanPortAdoptionService,
          apLanPortRepository,
          ipsecProfileRepository,
          apCapabilityValidator,
          64);
    }

    @Bean
    @ConditionalOnMissingBean
    public SoftGreProfileMerger softGreProfileMerger() {
      return new SoftGreProfileMergerImpl();
    }

    @Bean
    @ConditionalOnMissingBean
    LanPortAdoptionDataHelper dataHelper(
        RepositoryUtil repositoryUtil, LanPortAdoptionServiceImpl lanPortAdoptionService) {
      return new LanPortAdoptionDataHelper(repositoryUtil, lanPortAdoptionService);
    }

    @Bean
    @ConditionalOnMissingBean
    public ApCapabilityValidator apCapabilityValidator(
        FeatureFlagService featureFlagService,
        FirmwareCapabilityService firmwareCapabilityService,
        ApRepository apRepository) {
      return new ApCapabilityValidator(featureFlagService, firmwareCapabilityService, apRepository);
    }
  }
}
