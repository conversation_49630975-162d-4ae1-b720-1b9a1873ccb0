package com.ruckus.cloud.wifi.entitylistener.templateinstance.builder;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_FLOW;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Answers.RETURNS_SELF;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.core.header.HttpHeaderContext;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.TemplateInstancePostCreatePackage;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateApplicationPolicyOnAccessControlProfileRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateDevicePolicyOnAccessControlProfileRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateL2AclPolicyOnAccessControlProfileRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateL3AclPolicyOnAccessControlProfileRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.CreateApplicationPolicyRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.CreateDevicePolicyRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.CreateL2AclPolicyRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.CreateL3AclPolicyRequestBuilder;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.ExtendedTemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.repository.AccessControlProfileRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.HashMap;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class AccessControlProfileTemplateInstancePostCreateBuilderTest {
  
  @SpyBean
  private AccessControlProfileTemplateInstancePostCreateBuilder unit;

  @MockBean
  private AccessControlProfileRepository accessControlProfileRepository;
  @MockBean(answer = RETURNS_SELF)
  private CreateApplicationPolicyRequestBuilder createApplicationPolicyRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private CreateDevicePolicyRequestBuilder createDevicePolicyRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private CreateL2AclPolicyRequestBuilder createL2AclPolicyRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private CreateL3AclPolicyRequestBuilder createL3AclPolicyRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateApplicationPolicyOnAccessControlProfileRequestBuilder activateApplicationPolicyOnAccessControlProfileRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateDevicePolicyOnAccessControlProfileRequestBuilder activateDevicePolicyOnAccessControlProfileRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateL2AclPolicyOnAccessControlProfileRequestBuilder activateL2AclPolicyOnAccessControlProfileRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateL3AclPolicyOnAccessControlProfileRequestBuilder activateL3AclPolicyOnAccessControlProfileRequestBuilder;

  @Test
  void testEntityClass() {
    assertThat(unit.entityClass()).isEqualTo(AccessControlProfile.class);
  }

  @Test
  void testIsTemplateInstance() {
    var accessControlProfile = new AccessControlProfile(randomId());
    assertThat(unit.isTemplateInstance(accessControlProfile))
        .isFalse();

    accessControlProfile.setTemplateId(randomId());
    assertThat(unit.isTemplateInstance(accessControlProfile))
        .isTrue();
  }

  @Test
  void testBuild() {
    var profileTemplate = new AccessControlProfile(randomId());
    var tenantId = randomId();
    profileTemplate.setIsTemplate(true);
    profileTemplate.setTenant(new Tenant(tenantId));
    profileTemplate.setApplicationPolicy(new ApplicationPolicy(randomId()));
    profileTemplate.setDevicePolicy(new DevicePolicy(randomId()));
    profileTemplate.setL2AclPolicy(new L2AclPolicy(randomId()));
    profileTemplate.setL3AclPolicy(new L3AclPolicy(randomId()));

    var profile = new AccessControlProfile(randomId());
    profile.setTemplateId(profileTemplate.getId());
    var context = new HashMap<String, String>();
    context.put(HEADER_TEMPLATE_FLOW, ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE);
    HttpHeaderContext.setContext(context);

    doReturn(Optional.of(profileTemplate)).when(accessControlProfileRepository).findTemplateById(profileTemplate.getId());

    TemplateInstancePostCreatePackage mockTemplateInstancePostCreatePackage = mockPackage();
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage))
        .when(createApplicationPolicyRequestBuilder)
        .build(eq(profileTemplate.getApplicationPolicy()));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage))
        .when(createDevicePolicyRequestBuilder)
        .build(eq(profileTemplate.getDevicePolicy()));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage))
        .when(createL2AclPolicyRequestBuilder)
        .build(eq(profileTemplate.getL2AclPolicy()));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage))
        .when(createL3AclPolicyRequestBuilder)
        .build(eq(profileTemplate.getL3AclPolicy()));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage))
        .when(activateApplicationPolicyOnAccessControlProfileRequestBuilder)
        .build(eq(profileTemplate));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage))
        .when(activateDevicePolicyOnAccessControlProfileRequestBuilder)
        .build(eq(profileTemplate));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage))
        .when(activateL2AclPolicyOnAccessControlProfileRequestBuilder)
        .build(eq(profileTemplate));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage))
        .when(activateL3AclPolicyOnAccessControlProfileRequestBuilder)
        .build(eq(profileTemplate));

    var txEntity = new TxEntity<>(profile, EntityAction.ADD);
    var result = unit.build(txEntity);

    assertThat(result).hasSize(8);
  }

  private TemplateInstancePostCreatePackage mockPackage() {
    return new TemplateInstancePostCreatePackage(null, new ExtendedTemplateInstanceCreateRequest(), null);
  }
}