package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture.randomApGroup;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture.randomNetworkApGroup;
import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomNetwork;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenue;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenue;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.servicemodel.projection.NetworkApGroupProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fg');
    INSERT INTO tenant (id) VALUES ('a782b402d95c46b4aabce87b77e68613');
       
    INSERT INTO venue (id, tenant) VALUES ('4a1df860e61c4c93a65d5472a0576c4c', '4c8279f79307415fa9e4c88a1819f0fg');
    INSERT INTO venue (id, tenant) VALUES ('71ad469e254e4bf9a98e5fbfbbcae8f0', 'a782b402d95c46b4aabce87b77e68613');
       
    INSERT INTO ap_group (id, is_default, name, tenant, venue) VALUES
       ('33335fa13e444d62ac64af9ab4ec5796', true, '', '4c8279f79307415fa9e4c88a1819f0fg', '4a1df860e61c4c93a65d5472a0576c4c'),
       ('fdd94f9fd66641b9b893ae8f7c5848dg', false, 'AP-Group-1', '4c8279f79307415fa9e4c88a1819f0fg', '4a1df860e61c4c93a65d5472a0576c4c'),
       ('58bc286013e1468e9867061e8dbed380', false, 'AP-Group-2', '4c8279f79307415fa9e4c88a1819f0fg', '4a1df860e61c4c93a65d5472a0576c4c'),
       ('1b42a14ada374d6dbf563ef756ef70f2', true, '', 'a782b402d95c46b4aabce87b77e68613', '71ad469e254e4bf9a98e5fbfbbcae8f0');
         
    INSERT INTO network (type, id, name, tenant) VALUES
        ('GUEST', '239cb0716d164a559b6f79a84a74de11', 'GUEST NETWORK', '4c8279f79307415fa9e4c88a1819f0fg'),
        ('AAA', '14fafa7aa9df4ce49f25b65a70badf17', 'AAA NETWORK', '4c8279f79307415fa9e4c88a1819f0fg'),
        ('OPEN', '39fc80a4bf0b43bcbf61e018f0ea264f', 'OPEN NETWORK', 'a782b402d95c46b4aabce87b77e68613');
        
    INSERT INTO vlan_pool (id, tenant) VALUES ('2054c90fc91e435db77a2980ab00adbc', '4c8279f79307415fa9e4c88a1819f0fg'); 
           
    INSERT INTO network_venue (id, tenant, network, venue, all_ap_groups_radio, is_all_ap_groups, vlan_pool_id) VALUES
        ('4c77388d6ef549dbb1c3067d9c99a388', '4c8279f79307415fa9e4c88a1819f0fg', '239cb0716d164a559b6f79a84a74de11', '4a1df860e61c4c93a65d5472a0576c4c', 'Both', true, '2054c90fc91e435db77a2980ab00adbc'),
        ('260416791f7348cdbc2f1212036a3d9e', '4c8279f79307415fa9e4c88a1819f0fg', '14fafa7aa9df4ce49f25b65a70badf17', '4a1df860e61c4c93a65d5472a0576c4c', 'Both', false, '2054c90fc91e435db77a2980ab00adbc'),
        ('edbfc4df1faf4517b099dc8bf17e376h', 'a782b402d95c46b4aabce87b77e68613', '39fc80a4bf0b43bcbf61e018f0ea264f', '71ad469e254e4bf9a98e5fbfbbcae8f0', 'Both', false, '2054c90fc91e435db77a2980ab00adbc');
       
    INSERT INTO network_ap_group (id, tenant, ap_group, network_venue) VALUES
        ('5cae600415e945ee8996b694856bfc91', '4c8279f79307415fa9e4c88a1819f0fg', '33335fa13e444d62ac64af9ab4ec5796', '4c77388d6ef549dbb1c3067d9c99a388'),
        ('22e436840cde4812844175a6cc4035d5', '4c8279f79307415fa9e4c88a1819f0fg', 'fdd94f9fd66641b9b893ae8f7c5848dg', '4c77388d6ef549dbb1c3067d9c99a388'),
        ('917d8ba0c4584cbd8e91cc08cc8152a0', '4c8279f79307415fa9e4c88a1819f0fg', '58bc286013e1468e9867061e8dbed380', '4c77388d6ef549dbb1c3067d9c99a388'),
        ('38dfe64090744fec948ec3f803e78far', '4c8279f79307415fa9e4c88a1819f0fg', 'fdd94f9fd66641b9b893ae8f7c5848dg', '260416791f7348cdbc2f1212036a3d9e'),
        ('fb3f510532a44b369855986524f940b6', 'a782b402d95c46b4aabce87b77e68613', '1b42a14ada374d6dbf563ef756ef70f2', 'edbfc4df1faf4517b099dc8bf17e376h');
        
    INSERT INTO network_ap_group_radio (id, radio, network_ap_group, vlan_pool, tenant, created_date) VALUES
        ('1a72c0e5e45f4b18b6b67f524a265eea', 'Upper_5_GHz', '5cae600415e945ee8996b694856bfc91','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:59:29'),
        ('1a72c0e5e45f4b18b6b67f524a265eeb', 'Lower_5_GHz', '5cae600415e945ee8996b694856bfc91','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:59:29'),
        ('1a72c0e5e45f4b18b6b67f524a265eec', '_5_GHz', '5cae600415e945ee8996b694856bfc91','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:59:29'),
        ('1a72c0e5e45f4b18b6b67f524a265eed', '_2_4_GHz', '5cae600415e945ee8996b694856bfc91','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:59:29'),
        ('2a7cb87d9a604d9882c991f52da75fca', 'Upper_5_GHz', '22e436840cde4812844175a6cc4035d5','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('2a7cb87d9a604d9882c991f52da75fcb', 'Lower_5_GHz', '22e436840cde4812844175a6cc4035d5','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('2a7cb87d9a604d9882c991f52da75fcc', '_5_GHz', '22e436840cde4812844175a6cc4035d5','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('2a7cb87d9a604d9882c991f52da75fcd', '_2_4_GHz', '22e436840cde4812844175a6cc4035d5','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('3a7cb87d9a604d9882c991f52da75fca', 'Upper_5_GHz', '917d8ba0c4584cbd8e91cc08cc8152a0','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('3a7cb87d9a604d9882c991f52da75fcb', 'Lower_5_GHz', '917d8ba0c4584cbd8e91cc08cc8152a0','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('3a7cb87d9a604d9882c991f52da75fcc', '_5_GHz', '917d8ba0c4584cbd8e91cc08cc8152a0','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('3a7cb87d9a604d9882c991f52da75fcd', '_2_4_GHz', '917d8ba0c4584cbd8e91cc08cc8152a0','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('4a7cb87d9a604d9882c991f52da75fca', 'Upper_5_GHz', '38dfe64090744fec948ec3f803e78far','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('4a7cb87d9a604d9882c991f52da75fcb', 'Lower_5_GHz', '38dfe64090744fec948ec3f803e78far','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('4a7cb87d9a604d9882c991f52da75fcc', '_5_GHz', '38dfe64090744fec948ec3f803e78far','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49'),
        ('4a7cb87d9a604d9882c991f52da75fcd', '_2_4_GHz', '38dfe64090744fec948ec3f803e78far','2054c90fc91e435db77a2980ab00adbc','4c8279f79307415fa9e4c88a1819f0fg', '2024-02-27 08:33:49');
    """)
class NetworkApGroupRepositoryTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private NetworkApGroupRepository repository;

  private static final String TENANT_ID_1 = "4c8279f79307415fa9e4c88a1819f0fg";
  private static final String TENANT_ID_2 = "a782b402d95c46b4aabce87b77e68613";
  private static final String AP_GROUP_ID_1 = "33335fa13e444d62ac64af9ab4ec5796"; // default
  private static final String AP_GROUP_ID_1_1 = "fdd94f9fd66641b9b893ae8f7c5848dg"; // AP-Group-1
  private static final String AP_GROUP_ID_1_2 = "58bc286013e1468e9867061e8dbed380"; // AP-Group-2
  private static final String AP_GROUP_ID_2 = "1b42a14ada374d6dbf563ef756ef70f2"; // default
  private static final String NETWORK_ID_1_1 = "239cb0716d164a559b6f79a84a74de11"; // GUEST NETWORK
  private static final String NETWORK_ID_1_2 = "14fafa7aa9df4ce49f25b65a70badf17"; // AAA NETWORK
  private static final String NETWORK_ID_2 = "39fc80a4bf0b43bcbf61e018f0ea264f"; // OPEN NETWORK
  private static final String VENUE_ID_1 = "4a1df860e61c4c93a65d5472a0576c4c";
  private static final String VENUE_ID_2 = "71ad469e254e4bf9a98e5fbfbbcae8f0";
  private static final String NETWORK_VENUE_ID_1_1 = "4c77388d6ef549dbb1c3067d9c99a388";
  private static final String NETWORK_VENUE_ID_1_2 = "260416791f7348cdbc2f1212036a3d9e";

  @Test
  void testExistsByTenantIdAndApGroupIdAndNetworkVenueNetworkId() {
    assertThat(repository.existsByTenantIdAndApGroupIdAndNetworkVenueNetworkId(
        TENANT_ID_1, AP_GROUP_ID_1_1, NETWORK_ID_1_1)).isTrue();
    assertThat(repository.existsByTenantIdAndApGroupIdAndNetworkVenueNetworkId(
        TENANT_ID_1, AP_GROUP_ID_2, NETWORK_ID_2)).isFalse();
    assertThat(repository.existsByTenantIdAndApGroupIdAndNetworkVenueNetworkId(
        TENANT_ID_2, AP_GROUP_ID_2, NETWORK_ID_2)).isTrue();
  }

  @Test
  void testExistsByTenantIdAndApGroupIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId() {
    assertThat(repository.existsByTenantIdAndApGroupIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
        TENANT_ID_1, AP_GROUP_ID_2, NETWORK_ID_2, VENUE_ID_2)).isFalse();
    assertThat(repository.existsByTenantIdAndApGroupIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
        TENANT_ID_2, AP_GROUP_ID_2, NETWORK_ID_2, VENUE_ID_2)).isTrue();
  }

  @Test
  void testFindByTenantIdAndApGroupId() {
    assertThat(repository.findByTenantIdAndApGroupId(TENANT_ID_1, AP_GROUP_ID_1_1))
        .isNotEmpty().hasSize(2).allSatisfy(networkApGroup -> {
          assertThat(networkApGroup.getTenant().getId()).isEqualTo(TENANT_ID_1);
          assertThat(networkApGroup.getApGroup().getId()).isEqualTo(AP_GROUP_ID_1_1);
          assertThat(networkApGroup.getNetworkVenue().getVenue().getId()).isEqualTo(VENUE_ID_1);
        })
        .extracting(networkApGroup -> networkApGroup.getNetworkVenue().getNetwork().getId())
        .containsExactlyInAnyOrder(NETWORK_ID_1_1, NETWORK_ID_1_2);
    assertThat(repository.findByTenantIdAndApGroupId(TENANT_ID_2, AP_GROUP_ID_1_2)).isEmpty();
    assertThat(repository.findByTenantIdAndApGroupId(TENANT_ID_2, AP_GROUP_ID_1_1)).isEmpty();
  }

  @Test
  void testFindByTenantIdAndApGroupIdIn() {
    assertThat(repository.findByTenantIdAndApGroupIdIn(TENANT_ID_1,
        List.of(AP_GROUP_ID_1_1, AP_GROUP_ID_1_2)))
        .extracting(AbstractBaseEntity::getId)
        .containsExactlyInAnyOrder("22e436840cde4812844175a6cc4035d5", "917d8ba0c4584cbd8e91cc08cc8152a0", "38dfe64090744fec948ec3f803e78far");
    assertThat(repository.findByTenantIdAndApGroupIdIn(TENANT_ID_2,
        List.of(AP_GROUP_ID_1_1, AP_GROUP_ID_1_2))).isEmpty();
  }

  @Test
  void testFindByTenantIdAndVenueIdIn() {
    assertThat(repository.findByTenantIdAndNetworkVenueVenueIdIn(TENANT_ID_1,
        List.of(VENUE_ID_1)))
        .extracting(AbstractBaseEntity::getId)
        .containsExactlyInAnyOrder("22e436840cde4812844175a6cc4035d5", "917d8ba0c4584cbd8e91cc08cc8152a0"
            , "38dfe64090744fec948ec3f803e78far", "5cae600415e945ee8996b694856bfc91");
    assertThat(repository.findByTenantIdAndNetworkVenueVenueIdIn(TENANT_ID_2,
        List.of(VENUE_ID_2)))
        .extracting(AbstractBaseEntity::getId)
        .containsExactlyInAnyOrder("fb3f510532a44b369855986524f940b6");
    assertThat(repository.findByTenantIdAndNetworkVenueVenueIdIn(TENANT_ID_2,
        List.of(AP_GROUP_ID_1_1, AP_GROUP_ID_1_2))).isEmpty();
  }

  @Test
  void testFindByTenantIdAndNetworkVenueId() {
    assertThat(repository.findByTenantIdAndNetworkVenueId(TENANT_ID_1, NETWORK_VENUE_ID_1_1))
        .isNotEmpty().hasSize(3).allSatisfy(networkApGroup -> {
          assertThat(networkApGroup.getTenant().getId()).isEqualTo(TENANT_ID_1);
          assertThat(networkApGroup.getNetworkVenue().getNetwork().getId()).isEqualTo(NETWORK_ID_1_1);
          assertThat(networkApGroup.getNetworkVenue().getVenue().getId()).isEqualTo(VENUE_ID_1);
        })
        .extracting(networkApGroup -> networkApGroup.getApGroup().getId())
        .containsExactlyInAnyOrder(AP_GROUP_ID_1, AP_GROUP_ID_1_1, AP_GROUP_ID_1_2);
    assertThat(repository.findByTenantIdAndNetworkVenueId(TENANT_ID_1, NETWORK_VENUE_ID_1_2)).isNotEmpty();
    assertThat(repository.findByTenantIdAndNetworkVenueId(TENANT_ID_2, NETWORK_VENUE_ID_1_1)).isEmpty();
    assertThat(repository.findByTenantIdAndNetworkVenueId(TENANT_ID_2, NETWORK_VENUE_ID_1_2)).isEmpty();
  }

  @Test
  void testFindByTenantIdAndApGroupIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId() {
    assertThat(repository.findByTenantIdAndApGroupIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
        TENANT_ID_1, AP_GROUP_ID_2, NETWORK_ID_2, VENUE_ID_2)).isEmpty();
    assertThat(repository.findByTenantIdAndApGroupIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
        TENANT_ID_2, AP_GROUP_ID_2, NETWORK_ID_2, VENUE_ID_2)).isPresent()
        .get().satisfies(networkApGroup -> {
          assertThat(networkApGroup.getTenant().getId()).isEqualTo(TENANT_ID_2);
          assertThat(networkApGroup.getApGroup().getId()).isEqualTo(AP_GROUP_ID_2);
          assertThat(networkApGroup.getNetworkVenue().getNetwork().getId()).isEqualTo(NETWORK_ID_2);
          assertThat(networkApGroup.getNetworkVenue().getVenue().getId()).isEqualTo(VENUE_ID_2);
        });
  }

  @Test
  void testFindTenantExistNetworkApGroup() {
    assertThat(repository.findAllDistinctTenantIds())
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(2)
        .contains(TENANT_ID_1, TENANT_ID_2);
  }

  @Test
  void testFindNetworksByApGroupIds() {
    assertThat(repository.findNetworksByTenantIdAndApGroupIdIn(TENANT_ID_1,
        List.of(AP_GROUP_ID_1, AP_GROUP_ID_1_2)))
        .isNotEmpty().hasSize(1).allSatisfy(network -> {
          assertThat(network.getId()).isEqualTo(NETWORK_ID_1_1);
        });
  }

  @Nested
  class WhenFindNetworkApGroupProjections {
    private final String networkId1 = randomId();
    private final String networkId2 = randomId();
    private final List<String> apGroupIdsOfVenue1 = List.of(randomId(), randomId(), randomId());
    private final List<String> apGroupIdsOfVenue2 = List.of(randomId(), randomId());

    @Nested
    class GivenNetworkApGroupsPresent {
      @BeforeEach
      void prepareData(Tenant tenant) {
        // prepare venue1 and apGroups
        final var venue1 =
            repositoryUtil.createOrUpdate(
                randomVenue(tenant, v -> {}), tenant.getId(), randomTxId());
        final var apGroupsOfVenue1 =
            apGroupIdsOfVenue1.stream()
                .map(
                    apGroupId ->
                        repositoryUtil.createOrUpdate(
                            randomApGroup(venue1, apGroup -> apGroup.setId(apGroupId)),
                            tenant.getId(),
                            randomTxId()))
                .toList();

        // prepare venue2 and apGroups
        final var venue2 =
            repositoryUtil.createOrUpdate(
                randomVenue(tenant, v -> {}), tenant.getId(), randomTxId());
        final var apGroupsOfVenue2 =
            apGroupIdsOfVenue2.stream()
                .map(
                    apGroupId ->
                        repositoryUtil.createOrUpdate(
                            randomApGroup(venue2, apGroup -> apGroup.setId(apGroupId)),
                            tenant.getId(),
                            randomTxId()))
                .toList();

        // prepare network1
        final var network1 =
            repositoryUtil.createOrUpdate(
                randomNetwork(tenant, n -> n.setId(networkId1)), tenant.getId(), randomTxId());
        final var networkVenue1 =
            repositoryUtil.createOrUpdate(
                randomNetworkVenue(network1, venue1), tenant.getId(), randomTxId());
        apGroupsOfVenue1.stream()
            .limit(2)
            .forEach(
                apGroup ->
                    repositoryUtil.createOrUpdate(
                        randomNetworkApGroup(networkVenue1, apGroup),
                        tenant.getId(),
                        randomTxId()));

        final var networkVenue2 =
            repositoryUtil.createOrUpdate(
                randomNetworkVenue(network1, venue2), tenant.getId(), randomTxId());
        apGroupsOfVenue2.stream()
            .limit(2)
            .forEach(
                apGroup ->
                    repositoryUtil.createOrUpdate(
                        randomNetworkApGroup(networkVenue2, apGroup),
                        tenant.getId(),
                        randomTxId()));

        // prepare network2
        final var network2 =
            repositoryUtil.createOrUpdate(
                randomNetwork(tenant, n -> n.setId(networkId2)), tenant.getId(), randomTxId());
        final var networkVenue =
            repositoryUtil.createOrUpdate(
                randomNetworkVenue(network2, venue2), tenant.getId(), randomTxId());
        apGroupsOfVenue2.stream()
            .limit(2)
            .forEach(
                apGroup ->
                    repositoryUtil.createOrUpdate(
                        randomNetworkApGroup(networkVenue, apGroup), tenant.getId(), randomTxId()));
      }

      @Test
      void thenFindProjections(Tenant tenant) {
        assertThat(
                repository.findByTenantIdAndNetworkIdIn(tenant.getId(), List.of(networkId1, networkId2)))
            .hasSize(6)
            .containsExactlyInAnyOrder(
                new NetworkApGroupProjection(networkId1, apGroupIdsOfVenue1.get(0)),
                new NetworkApGroupProjection(networkId1, apGroupIdsOfVenue1.get(1)),
                new NetworkApGroupProjection(networkId1, apGroupIdsOfVenue2.get(0)),
                new NetworkApGroupProjection(networkId1, apGroupIdsOfVenue2.get(1)),
                new NetworkApGroupProjection(networkId2, apGroupIdsOfVenue2.get(0)),
                new NetworkApGroupProjection(networkId2, apGroupIdsOfVenue2.get(1)));
      }
    }

    @Test
    void givenNetworkApGroupsNotPresent(Tenant tenant) {
      final var networkIds = Stream.generate(CommonTestFixture::randomId).limit(3).toList();

      assertThat(repository.findByTenantIdAndNetworkIdIn(tenant.getId(), networkIds))
          .isNotNull()
          .isEmpty();
    }
  }

  @Test
  void givenVlanPoolNotPresent(Tenant tenant) {
    final var vlanPoolId = randomId();

    assertThat(repository.findNetworkApGroupsByTenantIdAndVlanPoolId(
        Pageable.unpaged(), tenant.getId(), vlanPoolId))
        .isNotNull()
        .isEmpty();
  }

  @Test
  void testFindNetworkApGroupsByTenantIdAndVlanPoolId() {
    final var vlanPoolId = "2054c90fc91e435db77a2980ab00adbc";

    var pageable = PageRequest.of(0, 2, Sort.by(AbstractBaseEntity.Fields.CREATEDDATE));
    var result = repository.findNetworkApGroupsByTenantIdAndVlanPoolId(
        pageable, TENANT_ID_1, vlanPoolId);
    var totalDone = 0;
    while (result.hasContent()) {
      assertThat(result)
          .isNotNull()
          .isNotEmpty()
          .allSatisfy(nap -> {
            assertThat(nap.venueId()).isEqualTo(VENUE_ID_1);
            assertThat(nap.apGroupId()).isIn(
                List.of(
                    AP_GROUP_ID_1,
                    AP_GROUP_ID_1_1,
                    AP_GROUP_ID_1_2));
            assertThat(nap.wifiNetworkId()).isIn(
                List.of(
                    NETWORK_ID_1_1, NETWORK_ID_1_2));
            assertThat(nap.isAllApGroups()).satisfies(isAllApGroup -> {
              if (nap.wifiNetworkId().equals(NETWORK_ID_1_1)) {
                assertThat(isAllApGroup).isTrue();
              } else {
                assertThat(isAllApGroup).isFalse();
              }
            });
          });
      totalDone += result.getSize();
      pageable = pageable.next();
      result = repository.findNetworkApGroupsByTenantIdAndVlanPoolId(
          pageable, TENANT_ID_1, vlanPoolId);
    }
    assertThat(totalDone).isEqualTo(4);
  }
}
