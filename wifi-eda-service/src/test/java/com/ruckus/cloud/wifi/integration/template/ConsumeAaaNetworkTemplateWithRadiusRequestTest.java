package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_RADIUS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_RADIUS_SERVER_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.RADIUS_SERVER_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTimeSlot;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.util.TemplateRetriever.retrieveTemplate;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusNetworkDataQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SortOrderEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.RadiusRepository;
import com.ruckus.cloud.wifi.service.ExtendedNetworkTemplateServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedRadiusServiceCtrl;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Tag("RadiusServerProfileTest")
@WifiIntegrationTest
public class ConsumeAaaNetworkTemplateWithRadiusRequestTest extends AbstractRequestTest {

  private static final String NETWORK_ID = "networkId";

  @Autowired
  private ExtendedRadiusServiceCtrl radiusServiceCtrl;

  @Autowired
  private ExtendedNetworkTemplateServiceCtrl networkTemplateServiceCtrl;

  @Autowired
  private RadiusRepository radiusRepository;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  private void assertRadiusServers(Radius expectedRadius, Radius actualRadius) {
    assertNotNull(actualRadius.getId());
    assertEquals(expectedRadius.getName(), actualRadius.getName());
    assertEquals(expectedRadius.getType(), actualRadius.getType());
    // Validate radius primary
    assertEquals(expectedRadius.getPrimary().getIp(), actualRadius.getPrimary().getIp());
    assertEquals(expectedRadius.getPrimary().getPort(), actualRadius.getPrimary().getPort());
    assertEquals(expectedRadius.getPrimary().getSharedSecret(),
        actualRadius.getPrimary().getSharedSecret());
    // Validate radius secondary
    if (expectedRadius.getSecondary() != null) {
      assertEquals(expectedRadius.getSecondary().getIp(), actualRadius.getSecondary().getIp());
      assertEquals(expectedRadius.getSecondary().getPort(), actualRadius.getSecondary().getPort());
      assertEquals(expectedRadius.getSecondary().getSharedSecret(),
          actualRadius.getSecondary().getSharedSecret());
    }
  }

  @SneakyThrows
  private void assertRadiusNetworks(Radius radius, int radiusNetworksCount) {
    QueryRequest queryParams = new QueryRequest();

    queryParams.setPage(1);
    queryParams.setPageSize(100);
    queryParams.setSortField(NETWORK_ID);
    queryParams.setSortOrder(SortOrderEnum.ASC);
    RadiusNetworkDataQueryResponse result = radiusServiceCtrl.getRadiusNetworks(
        radius.getId(), queryParams);

    assertEquals(radiusNetworksCount, result.getTotalCount().intValue());
    assertEquals(radiusNetworksCount, result.getData().size());
  }

  private void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertRadiusIndexViewmodel(List<Operations> operations, OpType opType, Radius radius, int scope) {
    if (opType.equals(OpType.DEL)) {
      assertTrue(operations.stream()
          .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .filter(o -> radius.getId().equals(o.getId()))
          .filter(o -> o.getOpType() == opType)
          .findAny().isPresent());
    } else {
      assertTrue(operations.stream()
          .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .filter(o -> radius.getId().equals(o.getId()))
          .filter(o -> o.getOpType() == opType)
          .filter(o -> radius.getName()
              .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
          .filter(
              o -> o.getDocMap().get(EsConstants.Key.NETWORK_IDS).getListValue().getValuesCount()
                  == scope)
          .findAny().isPresent());
    }
  }

  private static com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler dummyNetworkVenueScheduler() {
    final var scheduler = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler();
    scheduler.setType(SchedulerTypeEnum.CUSTOM);
    scheduler.setMon(randomTimeSlot());
    scheduler.setTue(randomTimeSlot());
    scheduler.setWed(randomTimeSlot());
    scheduler.setThu(randomTimeSlot());
    scheduler.setFri(randomTimeSlot());
    scheduler.setSat(randomTimeSlot());
    scheduler.setSun(randomTimeSlot());
    return scheduler;
  }

  private NetworkVenue networkVenue(String networkId, String venueId) {
    final var payload = new NetworkVenue();
    payload.setId(randomId()); // autoGenerated is true in wifi-api
    payload.setNetworkId(networkId);
    payload.setVenueId(venueId);
    payload.setIsAllApGroups(true);
    payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
    payload.setScheduler(dummyNetworkVenueScheduler());
    return payload;
  }

  @Test
  public void testAddAaaNetworkTemplateWithRadiusTemplate(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE);
    receiveViewmodelCollectorOperations(1);

    List<Radius> radiusAddedList = repositoryUtil.findAll(Radius.class, TxCtxHolder.tenantId(), true);
    Radius radiusAdded1 = radiusAddedList.get(0);
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    //////////
    // add AAANetwork
    AAANetwork aaaNetwork = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork.setAuthRadiusId(radiusAdded1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, aaaNetwork);
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE);

    assertRadiusNetworks(radiusAdded1, 1);

    var viewOps1 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps1, 2),
        () -> assertTrue(viewOps1.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())),
        () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, radiusAdded1, 1)
    );
  }

  @Test
  public void testAddAaaNetworkWithRadiusShouldFail(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    Radius radius2 = RadiusTestFixture.acctRadius();
    radius2.setName("test-radius-2");
    radius2.setType(RadiusProfileTypeEnum.ACCOUNTING);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius2));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    Radius radiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
            TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort());
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);
    Radius radiusAdded2 = radiusRepository.findRadiusByTenantIdByPrimary(
            TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort());
    assertRadiusServers(radius2, radiusAdded2);
    assertRadiusNetworks(radiusAdded2, 0);

    //////////
    // add AAANetwork Template

    AAANetwork aaaNetwork1 = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork1.setAuthRadiusId(radiusAdded1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, aaaNetwork1);
    assertActivityStatusFail(ADD_NETWORK_TEMPLATE);

    AAANetwork aaaNetwork2 = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork2.setAccountingRadiusId(radiusAdded2.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, aaaNetwork2);
    assertActivityStatusFail(ADD_NETWORK_TEMPLATE);


    assertRadiusNetworks(radiusAdded1, 0);
    assertRadiusNetworks(radiusAdded2, 0);
    assertCmnCfgCollectorCfgRequestNotSent(tenantId);
  }

  @Test
  public void testUpdateAaaNetworkTemplateWithRadiusTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE);
    receiveViewmodelCollectorOperations(1);

    List<Radius> radiusAddedList = repositoryUtil.findAll(Radius.class, TxCtxHolder.tenantId(), true);
    Radius radiusAdded1 = radiusAddedList.get(0);
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    //////////
    // add AAANetwork
    AAANetwork aaaNetwork = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork.setAuthRadiusId(radiusAdded1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, aaaNetwork);
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE);

    assertRadiusNetworks(radiusAdded1, 1);

    var viewOps1 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
            () -> assertViewmodelOps(viewOps1, 2),
            () -> assertTrue(viewOps1.stream()
                    .filter(o -> NETWORK.equals(o.getIndex()))
                    .filter(o -> aaaNetwork.getId().equals(o.getId()))
                    .filter(o -> o.getOpType() == OpType.ADD)
                    .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
                    .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())),
            () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, radiusAdded1, 1)
    );

    //////////
    // update AAANetwork
    //////////
    // prepare new RADIUS profile for update
    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-2");
    radius2.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius2.setIsTemplate(true);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius2));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE);
    receiveViewmodelCollectorOperations(1);

    Radius radiusAdded2 = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
            TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort()));
    assertRadiusServers(radius2, radiusAdded2);
    assertRadiusNetworks(radiusAdded2, 0);

    //////////
    // updateNetwork with new RADIUS
    AAANetwork aaaNetworkAdded = map(
            (com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork) networkTemplateServiceCtrl.getNetworkTemplate(
                    aaaNetwork.getId()));

    aaaNetworkAdded.setAuthRadiusId(radiusAdded2.getId());
    aaaNetworkAdded.setAuthRadius(null);
    aaaNetworkAdded.setAccountingRadius(null);

    RequestParams rps = new RequestParams().addPathVariable("networkTemplateId", aaaNetworkAdded.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_NETWORK_TEMPLATE, userName, rps, aaaNetworkAdded);
    assertActivityStatusSuccess(UPDATE_NETWORK_TEMPLATE);

    AAANetwork aaaNetworkUpdated = map(
            (com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork) networkTemplateServiceCtrl.getNetworkTemplate(
                    aaaNetwork.getId()));

    assertEquals(radiusAdded2.getId(), aaaNetworkUpdated.getAuthRadiusId());
    assertEquals(radiusAdded2.getId(), aaaNetworkUpdated.getAuthRadius().getId());

    assertRadiusNetworks(radiusAdded1, 0);
    assertRadiusNetworks(radiusAdded2, 1);

    var viewOps2 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps2 {}", viewOps2);
    assertAll("assert viewmodel ops",
            () -> assertViewmodelOps(viewOps2, 3),
            () -> assertTrue(viewOps2.stream()
                    .filter(o -> NETWORK.equals(o.getIndex()))
                    .filter(o -> aaaNetwork.getId().equals(o.getId()))
                    .filter(o -> o.getOpType() == OpType.MOD)
                    .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
                    .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())),
            () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded1, 0),
            () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded2, 1)
    );
  }

  @Test
  public void testUpdateAaaNetworkTemplateWithRadiusShouldFail(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE);
    receiveViewmodelCollectorOperations(1);

    List<Radius> radiusAddedList = repositoryUtil.findAll(Radius.class, TxCtxHolder.tenantId(), true);
    Radius radiusAdded1 = radiusAddedList.get(0);
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    //////////
    // add AAANetwork
    AAANetwork aaaNetwork = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork.setAuthRadiusId(radiusAdded1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, aaaNetwork);
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE);

    assertRadiusNetworks(radiusAdded1, 1);

    var viewOps1 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
            () -> assertViewmodelOps(viewOps1, 2),
            () -> assertTrue(viewOps1.stream()
                    .filter(o -> NETWORK.equals(o.getIndex()))
                    .filter(o -> aaaNetwork.getId().equals(o.getId()))
                    .filter(o -> o.getOpType() == OpType.ADD)
                    .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
                    .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())),
            () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, radiusAdded1, 1)
    );

    //////////
    // update AAANetwork
    //////////
    // prepare new RADIUS profile for update
    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-2");
    radius2.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS, userName, map(radius2));
    assertActivityStatusSuccess(ADD_RADIUS);
    receiveViewmodelCollectorOperations(1);

    Radius radiusAdded2 = radiusRepository.findRadiusByTenantIdByPrimary(
            TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort());
    assertRadiusServers(radius2, radiusAdded2);
    assertRadiusNetworks(radiusAdded2, 0);

    //////////
    // updateNetwork with new RADIUS
    AAANetwork aaaNetworkAdded = map(
            (com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork) networkTemplateServiceCtrl.getNetworkTemplate(
                    aaaNetwork.getId()));

    aaaNetworkAdded.setAuthRadiusId(radiusAdded2.getId());
    aaaNetworkAdded.setAuthRadius(null);
    aaaNetworkAdded.setAccountingRadius(null);

    RequestParams rps = new RequestParams().addPathVariable("networkTemplateId", aaaNetworkAdded.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_NETWORK_TEMPLATE, userName, rps, aaaNetworkAdded);
    assertActivityStatusFail(UPDATE_NETWORK_TEMPLATE);

    AAANetwork aaaNetworkUpdated = map(
            (com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork) networkTemplateServiceCtrl.getNetworkTemplate(
                    aaaNetwork.getId()));

    assertEquals(radiusAdded1.getId(), aaaNetworkUpdated.getAuthRadiusId());
    assertEquals(radiusAdded1.getId(), aaaNetworkUpdated.getAuthRadius().getId());

    assertRadiusNetworks(radiusAdded1, 1);
    assertRadiusNetworks(radiusAdded2, 0);
    assertCmnCfgCollectorCfgRequestNotSent(tenantId);
  }

  @Test
  public void testDeleteAaaNetworkTemplateWithRadiusTemplate(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE);
    receiveViewmodelCollectorOperations(1);

    List<Radius> radiusAddedList = repositoryUtil.findAll(Radius.class, TxCtxHolder.tenantId(), true);
    Radius radiusAdded1 = radiusAddedList.get(0);
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    //////////
    // add AAANetwork
    AAANetwork aaaNetwork = map(NetworkTestFixture.randomAAANetwork(tenant, n -> n.setId(randomId())));
    aaaNetwork.setAuthRadiusId(radiusAdded1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, aaaNetwork);
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE);

    assertRadiusNetworks(radiusAdded1, 1);

    var viewOps1 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
            () -> assertViewmodelOps(viewOps1, 2),
            () -> assertTrue(viewOps1.stream()
                    .filter(o -> NETWORK.equals(o.getIndex()))
                    .filter(o -> aaaNetwork.getId().equals(o.getId()))
                    .filter(o -> o.getOpType() == OpType.ADD)
                    .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
                    .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())),
            () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, radiusAdded1, 1)
    );

    //////////
    // delete AAANetwork

    RequestParams rps = new RequestParams().addPathVariable("networkTemplateId", aaaNetwork.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_NETWORK_TEMPLATE, userName, rps, aaaNetwork);
    assertActivityStatusSuccess(DELETE_NETWORK_TEMPLATE);

    assertRadiusNetworks(radiusAdded1, 0);

    var viewOps2 = receiveViewmodelCollectorOperations(1);
    log.info("viewOps1 {}", viewOps2);
    assertAll(
        "assert viewmodel ops",
        () -> assertViewmodelOps(viewOps2, 2),
        () -> assertTrue(viewOps2.stream()
                    .filter(o -> NETWORK.equals(o.getIndex()))
                    .filter(o -> aaaNetwork.getId().equals(o.getId()))
                    .filter(o -> o.getOpType() == OpType.DEL)
                    .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded1, 0));
  }
}
