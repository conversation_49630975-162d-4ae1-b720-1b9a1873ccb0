package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_CALLING_SERVICE_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_CALLING_SERVICE_PROFILE_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.TemplateCreateInstanceOperation.newViewProperty;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfile;
import com.ruckus.cloud.wifi.eda.viewmodel.Property;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.WifiCallingServiceProfileTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddWifiCallingServiceProfileByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private NetworkRepository networkRepository;

  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void add_wifiCallingServiceProfile_by_template(@Template WifiCallingServiceProfile profile)
    throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, ecTenantId, randomTxId());

    // trigger add wifiCallingServiceProfile by template
    String instanceId = randomId();
    addWifiCallingByTemplate(instanceId, profile.getId(), ecTenantId, mspTenantId,
        userName, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_WIFI_CALLING_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_CALLING_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_CALLING_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void v1_1_apply_pskNetworkTemplate_with_wifiCallingTemplate(@Template WifiCallingServiceProfile profile) throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // add PskNetwork template
    PskNetwork pskNetwork = addPskNetwork(map(pskNetwork("psk").generate()), true);

    // activate wificalling on network template
    edaActivateWifiCallingServiceProfileOnWifiNetworkTemplate(mspTenantId, userName,
        pskNetwork.getId(), profile.getId());
    assertActivityStatusSuccess(ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE, mspTenantId);

    PskNetwork mspPskNetwork =
        repositoryUtil.find(PskNetwork.class, pskNetwork.getId(), mspTenantId, true);
    assertTrue(mspPskNetwork.getWlan().getAdvancedCustomization().getWifiCallingEnabled());
    assertEquals(1, mspPskNetwork.getWifiCallingServiceProfileNetworks().size());

    // apply template to create instance
    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, ecTenantId, randomTxId());

    // apply WifiCalling template
    String wifiCallingInstanceId = randomId();
    addWifiCallingByTemplate(wifiCallingInstanceId, profile.getId(), ecTenantId, mspTenantId,
        userName, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_WIFI_CALLING_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_CALLING_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_CALLING_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);

    // apply Network template
    String instanceId = randomId();
    addWifiNetworkByTemplate(instanceId, pskNetwork.getId(), ecTenantId, mspTenantId, userName);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    PskNetwork ecPskNetwork =
        (PskNetwork) networkRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecPskNetwork.getId());
    assertEquals(mspPskNetwork.getId(), ecPskNetwork.getTemplateId());
    assertEquals(mspPskNetwork.getUpdatedDate().getTime(), ecPskNetwork.getTemplateVersion());
    assertEquals(1, ecPskNetwork.getWifiCallingServiceProfileNetworks().size());
  }

  private void addWifiCallingByTemplate(String instanceId, String templateId, String ecTenantId,
      String mspTenantId, String userName, TemplateInstanceCreateRequest request) {
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ADD_WIFI_CALLING_SERVICE_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.WIFI_CALLING_SERVICE_PROFILE,
        instanceId);
    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, templateId)
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES,
            executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, randomTxId(),
        CfgAction.ADD_WIFI_CALLING_SERVICE_PROFILE_BY_TEMPLATE,
        ADD_WIFI_CALLING_SERVICE_PROFILE_BY_TEMPLATE, userName, requestParams, request);
  }

  private void addWifiNetworkByTemplate(String instanceId, String templateId, String ecTenantId,
      String mspTenantId, String userName) {
    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.NETWORK, instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);
    TemplateInstanceCreateRequest networkInstanceCreateRequest = new TemplateInstanceCreateRequest();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, templateId)
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, randomTxId(),
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, networkInstanceCreateRequest);
  }

  @Test
  public void ec_add_wifiCallingServiceProfile_fail_then_msp_activity_should_fail(@Template WifiCallingServiceProfile profile)
    throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // msp tenant add wifiCallingServiceProfile Template
    profile.setServiceName("conflict-name");
    repositoryUtil.createOrUpdate(profile, mspTenantId, randomTxId());

    // check inserted into DB
    WifiCallingServiceProfile profileTemplateFromDB = wifiCallingServiceProfileTemplateServiceCtrl.getWifiCallingServiceProfileTemplate(
      profile.getId());
    assertWifiCallingServiceProfile(profile, profileTemplateFromDB);

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, ecTenantId, randomTxId());

    // profile with the same name already in ec tenant before apply template
    WifiCallingServiceProfile existedEcProfile = WifiCallingServiceProfileTestFixture.randomWifiCallingServiceProfile();
    existedEcProfile.setServiceName("conflict-name");
    repositoryUtil.createOrUpdate(existedEcProfile, ecTenantId, randomTxId());

    // trigger add wifiCallingServiceProfile by template
    String instanceId = randomId();
    addWifiCallingByTemplate(instanceId, profileTemplateFromDB.getId(), ecTenantId, mspTenantId,
        userName, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_WIFI_CALLING_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusFail(ADD_WIFI_CALLING_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusFail(ADD_WIFI_CALLING_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void ec_add_wifiCallingServiceProfile_fail_then_msp_activity_should_fail_because_incorrect_overrides(
    @Template WifiCallingServiceProfile profile)
    throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, ecTenantId, randomTxId());

    // trigger add wifiCallingServiceProfile by template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    instanceCreateRequest.setOverrides(List.of(newViewProperty("x", "x"))); // incorrect

    String instanceId = randomId();
    addWifiCallingByTemplate(instanceId, profile.getId(), ecTenantId, mspTenantId,
        userName, instanceCreateRequest);

    assertActivityPlanNotSent(ecTenantId);
    assertActivityStatusFail(ADD_WIFI_CALLING_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  private void assertWifiCallingServiceProfile(WifiCallingServiceProfile profile, WifiCallingServiceProfile profileTemplateFromDB) {
    // compare the two profiles by their fields
    assertEquals(profile.getId(), profileTemplateFromDB.getId());
    assertEquals(profile.getServiceName(), profileTemplateFromDB.getServiceName());
    assertEquals(profile.getIsTemplate(), profileTemplateFromDB.getIsTemplate());
    assertEquals(profile.getTenant().getId(), profileTemplateFromDB.getTenant().getId());
    assertEquals(profile.getQosPriority(), profileTemplateFromDB.getQosPriority());
    assertEquals(profile.getEPDGs(), profileTemplateFromDB.getEPDGs());
  }

}
