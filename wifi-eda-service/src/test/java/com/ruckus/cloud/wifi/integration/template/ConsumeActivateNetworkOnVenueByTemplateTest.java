package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_WIFI_NETWORK_ON_VENUE_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.NetworkVenueCreateInstanceOperation.OVERRIDE_KEY_NETWORK_ID;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.NetworkVenueCreateInstanceOperation.OVERRIDE_KEY_VENUE_ID;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.TemplateCreateInstanceOperation.newViewProperty;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.VENUE_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.WIFI_NETWORK_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenueScheduler;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.Radio;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeActivateNetworkOnVenueByTemplateTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @Test
  void testActivateNetworkOnVenue(Tenant tenant, @Template Venue venue,
      @DefaultApGroup ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);
    var networkInstance = createInstanceFromTemplate(network, ecTenant);
    var venueInstance = createInstanceFromTemplate(venue, ecTenant);

    sendRequest(mspTenantId, userName, ecTenantId, venue, network, networkVenue, networkInstance,
        venueInstance, instanceId);

    assertActivityPlan(ADD_NETWORK_VENUE, ecTenantId);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, ecTenantId);
    assertActivityStatusSuccess(ACTIVATE_WIFI_NETWORK_ON_VENUE_BY_TEMPLATE, mspTenantId);

    assertNetworkVenueInstanceCreate(ecTenantId, networkVenue, networkInstance,
        venueInstance.getId(), instanceId);
  }

  @Test
  void testActivateNetworkOnVenue_CustomizeScheduleAndRadio(Tenant tenant, @Template Venue venue,
      @DefaultApGroup ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Radio(RadioEnum._5_GHz) @ScheduledNetworkVenue @Template NetworkVenue networkVenue) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);
    var networkInstance = createInstanceFromTemplate(network, ecTenant);
    var venueInstance = createInstanceFromTemplate(venue, ecTenant);

    sendRequest(mspTenantId, userName, ecTenantId, venue, network, networkVenue, networkInstance,
        venueInstance, instanceId);

    assertActivityPlan(ADD_NETWORK_VENUE, ecTenantId);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, ecTenantId);
    assertActivityStatusSuccess(ACTIVATE_WIFI_NETWORK_ON_VENUE_BY_TEMPLATE, mspTenantId);

    assertNetworkVenueInstanceCreate(ecTenantId, networkVenue, networkInstance,
        venueInstance.getId(), instanceId);
  }

  private void sendRequest(String mspTenantId, String userName, String ecTenantId, Venue venueTemplate,
      Network networkTemplate, NetworkVenue networkVenueTemplate,
      Network networkInstance, Venue venueInstance, String networkVenueInstanceId) {
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    instanceCreateRequest.setTargetTenantId(ecTenantId);
    instanceCreateRequest.setOverrides(List.of(
        newViewProperty(OVERRIDE_KEY_NETWORK_ID, networkInstance.getId()),
        newViewProperty(OVERRIDE_KEY_VENUE_ID, venueInstance.getId())
    ));

    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ADD_NETWORK_VENUE, WifiActivityPlan.Action.UPDATE,
        ExecutionPlanEntityTypes.NETWORK, networkInstance.getId());
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);

    String mspRequestId = randomTxId();

    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, networkVenueTemplate.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addPathVariable(VENUE_ID, venueTemplate.getId())
        .addPathVariable(WIFI_NETWORK_ID, networkTemplate.getId());
    rps.addRequestParam(INSTANCE_ID, networkVenueInstanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE_BY_TEMPLATE,
        ACTIVATE_WIFI_NETWORK_ON_VENUE_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);
  }

  private Network createInstanceFromTemplate(OpenNetwork template, Tenant ecTenant) {
    var instance = new OpenNetwork();
    instance.setId(randomId());
    instance.setName(template.getName());
    instance.setTemplateId(template.getId());
    instance.setTenant(ecTenant);
    instance.setWlan(new Wlan());
    instance.getWlan().setNetwork(instance);
    instance.getWlan().setSsid(template.getWlan().getSsid());
    instance.getWlan().setWlanSecurity(WlanSecurityEnum.Open);

    return repositoryUtil.createOrUpdate(instance, ecTenant.getId(), randomTxId());
  }

  private Venue createInstanceFromTemplate(Venue template, Tenant ecTenant) {
    var instance = new Venue();
    instance.setId(randomId());
    instance.setName(template.getName());
    instance.setTemplateId(template.getId());
    instance.setTenant(ecTenant);

    instance = repositoryUtil.createOrUpdate(instance, ecTenant.getId(), randomTxId());
    var apGroup = new ApGroup(randomId());
    apGroup.setVenue(instance);
    apGroup.setName("");
    apGroup.setTenant(ecTenant);
    apGroup = repositoryUtil.createOrUpdate(apGroup, ecTenant.getId(), randomTxId());
    instance.setApGroups(List.of(apGroup));
    return instance;
  }

  private void assertNetworkVenueInstanceCreate(String ecTenantId,
      NetworkVenue networkVenueTemplate, Network networkInstance, String venueInstanceId,
      String networkVenueInstanceId) {
    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(ecTenantId, Duration.ofSeconds(100));
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .hasSize(1)
        .first()
        .satisfies(e -> assertThat(e.getAction()).isEqualTo(
            com.ruckus.acx.ddccm.protobuf.common.Action.ADD))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .satisfies(e -> assertThat(e.getTenantId()).isEqualTo(ecTenantId))
        .satisfies(e -> assertThat(e.getVenueId()).isEqualTo(venueInstanceId))
        .satisfies(e -> assertThat(e.getWlanId()).isEqualTo(networkInstance.getId()));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(ecTenantId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.ADD)
                  .as("The ADD venue operation count should be 1")
                  .hasSize(1);
            }));
    assertNetworkVenue(networkVenueTemplate,
        repositoryUtil.find(NetworkVenue.class, networkVenueInstanceId));
  }

  private void assertNetworkVenue(NetworkVenue template, NetworkVenue instance) {
    assertThat(instance).isNotNull();
    assertNetworkVenueScheduler(template.getScheduler(), instance.getScheduler());
    assertThat(template.getIsAllApGroups()).isEqualTo(instance.getIsAllApGroups());
    assertRadio(template, instance);
  }

  private void assertNetworkVenueScheduler(NetworkVenueScheduler template, NetworkVenueScheduler instance) {
    if (template == null) {
      return;
    }
    assertThat(template.getType()).isEqualTo(instance.getType());

    if(template.getType() == SchedulerTypeEnum.CUSTOM) {
      assertThat(template.getSun()).isEqualTo(instance.getSun());
      assertThat(template.getMon()).isEqualTo(instance.getMon());
      assertThat(template.getTue()).isEqualTo(instance.getTue());
      assertThat(template.getWed()).isEqualTo(instance.getWed());
      assertThat(template.getThu()).isEqualTo(instance.getThu());
      assertThat(template.getFri()).isEqualTo(instance.getFri());
      assertThat(template.getSat()).isEqualTo(instance.getSat());
    }
  }

  private void assertRadio(NetworkVenue template, NetworkVenue instance) {
    if (template.getIsAllApGroups()) {
      assertThat(template.getAllApGroupsRadio()).isEqualTo(instance.getAllApGroupsRadio());
    }
  }

  private Tenant createEcTenant(Tenant tenant, String ecTenantId) {
    var ecTenant = new Tenant(ecTenantId);
    ecTenant.setRecoveryPsk("recover");
    ecTenant.setLatestReleaseVersion(tenant.getLatestReleaseVersion());
    return repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());
  }
}
