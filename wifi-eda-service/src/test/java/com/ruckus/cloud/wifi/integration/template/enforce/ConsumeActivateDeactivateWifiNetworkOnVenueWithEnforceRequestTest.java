package com.ruckus.cloud.wifi.integration.template.enforce;

import com.google.common.collect.Maps;
import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.venue.proto.VenueEvent;
import com.ruckus.cloud.venue.proto.VenueTemplateEnforcementSettings;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.REQUEST_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_ADMIN_NAME;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_DELEGATION_SOURCE_TENANT_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_TENANT_ID;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.venueWifiNetwork;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertNetworkVenueSoftly;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplate;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import com.ruckus.cloud.wifi.test.kafka.captor.ActivityCfgChangeMessageCaptor;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest.WifiCfgRequestBuilder;
import java.util.List;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.header.Header;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("NetworkActivationTest")
@WifiIntegrationTest
@Slf4j
class ConsumeActivateDeactivateWifiNetworkOnVenueWithEnforceRequestTest {

  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private ExtendedMessageUtil messageUtil;

  String mspTenantId;
  String ecTenantId;
  String venueTemplateId;

  String networkId;
  String venueId;
  String apGroupId;
  String apId;
  String networkVenueId;

  void validateActivityMessages(String tenantId, String requestId, String expectedStep, Status expectedStatus) {
    ActivityCfgChangeMessageCaptor captor = messageCaptors.getActivityCfgChangeMessageCaptor();
    final var activityCfgChangeRespMessage = captor
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(expectedStatus))
        .matches(p -> p.getStep().equals(expectedStep))
        .extracting(ConfigurationStatus::getEventDate)
        .isNotNull();
  }

  VenueEvent buildVenueTemplateEnforcementSettingsEvent(Venue venueTemplate, boolean isEnforced) {
    return VenueEvent.newBuilder()
        .setTenantId(venueTemplate.getTenant().getId())
        .addOperation(com.ruckus.cloud.venue.proto.Operation.newBuilder().setAction(
                com.ruckus.cloud.venue.proto.Action.MODIFY)
            .setVenueTemplateEnforcementSettings(
                VenueTemplateEnforcementSettings.newBuilder()
                    .setVenueId(venueTemplate.getId())
                    .setVenueName(venueTemplate.getName())
                    .setIsEnforced(isEnforced)
            )).build();
  }

  @SneakyThrows
  void toggleEnforce(boolean toggle) {
    Venue savedVenueTemplate = repositoryUtil.find(Venue.class, venueTemplateId, mspTenantId);
    Venue savedVenueInstance;

    var eventEnforce = buildVenueTemplateEnforcementSettingsEvent(savedVenueTemplate, toggle);
    var requestIdEnforce = randomTxId() + "$Enforce";
    messageUtil.sendVenueCfgChange(mspTenantId, requestIdEnforce, eventEnforce);

    validateActivityMessages(mspTenantId, requestIdEnforce,
        "UpdateVenueTemplateEnforcementSettingsInWifi", Status.OK);

    savedVenueTemplate = repositoryUtil.find(Venue.class, venueTemplateId, mspTenantId);
    assertThat(savedVenueTemplate).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(toggle);

    savedVenueInstance = repositoryUtil.find(Venue.class, venueId, ecTenantId);
    assertThat(savedVenueInstance).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(toggle);

    messageUtil.clearMessage();
  }

  WifiCfgRequest makeWifiCfgRequest(String requestId, CfgAction action, Object payload, boolean isDelegation) {
    var userName = randomName();
    WifiCfgRequestBuilder builder = WifiCfgRequest.builder()
        .tenantId(ecTenantId)
        .requestId(requestId)
        .apiAction(action)
        .requestHeaders(Maps.newHashMap())
        .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
        .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), isDelegation ? mspTenantId : "")
        .addHeader(REQUEST_ID.getName(), requestId)
        .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
        .addHeader(RKS_IDM_USER_ID.getName(), userName)
        .requestParams(new RequestParams()
            .addPathVariable("venueId", venueId)
            .addPathVariable("wifiNetworkId", networkId))
        .payload(payload);

    return builder.build();
  }

  @SneakyThrows
  private String doEcDirectRequestAndThenDelegation(CfgAction action, String flowStep, Object payload, String errorMsg, EntityAction entityAction) {
    var requestIdDirect = randomTxId();
    var wifiCfgRequestDirect = makeWifiCfgRequest(requestIdDirect, action, payload, false);
    // should fail to update Venue when it's enforced
    assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequestDirect)).isNotNull()
        .getRootCause().isInstanceOf(CommonException.class).hasMessage(String.format(errorMsg, entityAction));
    validateActivityMessages(ecTenantId, requestIdDirect, flowStep, Status.FAIL);

    messageUtil.clearMessage();

    var requestIdDelegate = randomTxId();
    var wifiCfgRequestDelegate = makeWifiCfgRequest(requestIdDelegate, action, payload, true);
    messageUtil.sendWifiCfgRequest(wifiCfgRequestDelegate);
    validateActivityMessages(ecTenantId, requestIdDelegate, flowStep, Status.OK);

    return requestIdDelegate;
  }

  @Nested
  class GivenOpenNetworkPersistedInDb {

    void validateResult(String tenantId, String requestId, String venueId, String networkId,
        String apiAction,
        List<String> impactList) {
      final var revision = revisionService.changes(requestId, tenantId,
          ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueId = revision.getNewEntities().stream()
          .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
          .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);

      assertThat(networkVenue)
          .isNotNull()
          .matches(nv -> Objects.equals(nv.getNetwork().getId(), networkId))
          .matches(nv -> Objects.equals(nv.getVenue().getId(), venueId));

      if (NetworkTypeEnum.GUEST.equals(networkVenue.getNetwork().getType())) {
        assertThat(networkVenue.getVenuePortal())
            .isNotNull();
      }

      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                  .as("The ADD NetworkVenue operation count should be 1")
                  .hasSize(1)
                  .singleElement()
                  .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                  .satisfies(assertNetworkVenueSoftly(networkId, venueId))));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(1)
              .singleElement()
              .matches(op -> op.getOpType() == OpType.ADD)
              .matches(op -> networkVenueId.equals(op.getId()))
              .extracting(Operations::getDocMap)
              .matches(doc -> networkVenueId.equals(doc.get(Key.ID).getStringValue()))
              .matches(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
              .matches(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
              .matches(doc -> networkId.equals(doc.get(Key.NETWORK_ID).getStringValue()))
              .matches(doc -> venueId.equals(doc.get(Key.VENUE_ID).getStringValue()))
              .matches(doc -> doc.get(Key.VENUE_NAME).getStringValue() != null));

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityImpactedMessage.getPayload())
              .matches(msg -> msg.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID))
              .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
              .hasSameElementsAs(impactList));

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .satisfies(op -> assertSoftly(softly -> {
                softly.assertThat(op)
                    .filteredOn(Operation::hasWlanVenue)
                    .describedAs("The count of WlanVenue operations should be 1")
                    .hasSize(1)
                    .singleElement()
                    .matches(wlanVenueOp -> wlanVenueOp.getAction() == Action.ADD,
                        String.format(
                            "The value of `action` field in WlanVenue operation should be %s",
                            Action.ADD))
                    .matches(wlanVenueOp -> networkVenueId.equals(wlanVenueOp.getId()),
                        String.format("The value of `id` field in WlanVenue operation should be %s",
                            networkVenueId))
                    .extracting(Operation::getWlanVenue)
                    .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                      alsoSoftly.assertThat(wlanVenue.getWlanId())
                          .isEqualTo(networkId);
                      alsoSoftly.assertThat(wlanVenue.getVenueId())
                          .isEqualTo(venueId);
                    }));
              })));
    }

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupPersistedInDb(Tenant tenant, @OpenNetwork Network network,
        Venue venue, @DefaultApGroup ApGroup apGroup, Ap ap) {
      ecTenantId = tenant.getId();

      Tenant mspTenant = TenantTestFixture.randomTenant();
      mspTenantId = mspTenant.getId();
      repositoryUtil.createOrUpdate(mspTenant, mspTenantId, randomTxId());

      Venue venueTemplate = randomVenueTemplate(mspTenant);
      venueTemplate.setIsEnforced(false);
      repositoryUtil.createOrUpdate(venueTemplate, mspTenantId, randomTxId());
      venueTemplateId = venueTemplate.getId();

      venue.setTemplateId(venueTemplateId);
      repositoryUtil.createOrUpdate(venue, ecTenantId, randomTxId());

      networkId = network.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
      apId = ap.getId();
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      toggleEnforce(true);

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE,
          ApiFlowNames.ACTIVATE_WIFI_NETWORK_ON_VENUE,
          venueWifiNetwork().generate(),
          "This relation of Venue is enforced by its Template, and this action [%s] is not allowed",
          EntityAction.ADD);

      validateResult(ecTenantId, requestId, venueId, networkId,
          CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE.key(), List.of(apId));
    }
  }

  @Nested
  class GivenOpenNetworkAndRelationPersistedInDb {

    void validateResult(String tenantId, String requestId, String networkVenueId,
        String networkId, String venueId) {
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);

      assertThat(networkVenue).isNull();

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(1)
              .singleElement()
              .matches(op -> op.getOpType() == OpType.DEL)
              .matches(op -> networkVenueId.equals(op.getId())));

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityImpactedMessage.getPayload())
              .matches(msg -> msg.getDeviceIdsList().size() == 0)
              .matches(msg -> msg.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID))
              .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
              .isEmpty());

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allMatch(op -> op.getAction() == Action.DELETE)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasWlanApGroup).count() == 1,
                  "The count of WlanApGroup operations should be 1") // 2.4G
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 1,
                  "The count of VenueSchedule operations should be 1")
              .filteredOn(Operation::hasWlanVenue)
              .hasSize(1)
              .singleElement()
              .matches(op -> op.getAction() == Action.DELETE,
                  String.format("The value of `action` field in WlanVenue operation should be %s",
                      Action.DELETE))
              .matches(op -> networkVenueId.equals(op.getId()),
                  String.format("The value of `id` field in WlanVenue operation should be %s",
                      networkVenueId))
              .satisfies(op -> {
                assertSoftly(softly -> {
                  softly.assertThat(op.getWlanVenue().getWlanId())
                      .isEqualTo(networkId);
                  softly.assertThat(op.getWlanVenue().getVenueId())
                      .isEqualTo(venueId);
                });
              }));
    }

    @BeforeEach
    void givenOpenNetworkAndRelatedEntitiesPersistedInDb(Tenant tenant, @OpenNetwork Network network,
        Venue venue, @DefaultApGroup ApGroup apGroup,
        @ScheduledNetworkVenue NetworkVenue networkVenue, NetworkApGroup networkApGroup,
        NetworkApGroupRadio networkApGroupRadio) {

      ecTenantId = tenant.getId();

      Tenant mspTenant = TenantTestFixture.randomTenant();
      mspTenantId = mspTenant.getId();
      repositoryUtil.createOrUpdate(mspTenant, mspTenantId, randomTxId());

      Venue venueTemplate = randomVenueTemplate(mspTenant);
      venueTemplate.setIsEnforced(false);
      repositoryUtil.createOrUpdate(venueTemplate, mspTenantId, randomTxId());
      venueTemplateId = venueTemplate.getId();

      venue.setTemplateId(venueTemplateId);
      repositoryUtil.createOrUpdate(venue, ecTenantId, randomTxId());

      networkId = network.getId();
      venueId = venue.getId();
      networkVenueId = networkVenue.getId();
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      toggleEnforce(true);

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.DEACTIVATE_WIFI_NETWORK_ON_VENUE,
          ApiFlowNames.DEACTIVATE_WIFI_NETWORK_ON_VENUE,
          null,
          "This relation of Venue is enforced by its Template, and this action [%s] is not allowed",
          EntityAction.DELETE);

      validateResult(ecTenantId, requestId, networkVenueId, networkId, venueId);
    }
  }
}
