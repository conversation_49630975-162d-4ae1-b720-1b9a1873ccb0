package com.ruckus.cloud.wifi.integration.apgroup;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.wifi.ApGroupApModel;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApGroupApModelBandModeSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LoadBalancingMethodEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SteeringModeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroupApClientAdmissionControlSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroupApLoadBalancingSettings;
import com.ruckus.cloud.wifi.entitylistener.ddccm.DdccmConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("ApGroupTest")
@Slf4j
@WifiIntegrationTest
public class ConsumeUpdateApGroupCfgRequestTest {
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private ObjectMapper objectMapper;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired private MessageCaptors messageCaptors;

  void validateUpdateVenueApLoadBalancingSettingsResult(
      String apGroupId,
      VenueApGroupApLoadBalancingSettings venueApGroupApLoadBalancingSettings,
      String tenantId,
      String requestId) {
    final var apGroupFromDb = repositoryUtil.find(ApGroup.class, apGroupId);
    assertThat(apGroupFromDb)
        .isNotNull()
        .extracting(ApGroup::getLoadBalancing)
        .isNotNull()
        .matches(v -> v.getEnabled().equals(venueApGroupApLoadBalancingSettings.getEnabled()))
        .matches(
            v ->
                v.getLoadBalancingMethod()
                    .equals(venueApGroupApLoadBalancingSettings.getLoadBalancingMethod()),
            "LoadBalancingMethod is equal")
        .matches(
            v ->
                v.getBandBalancingEnabled()
                    .equals(venueApGroupApLoadBalancingSettings.getBandBalancingEnabled()),
            "BandBalancingEnabled is equal")
        .matches(
            v -> v.getSteeringMode().equals(venueApGroupApLoadBalancingSettings.getSteeringMode()),
            "SteeringMode is equal")
        .matches(
            v ->
                v.getBandBalancingClientPercent24G()
                    .equals(venueApGroupApLoadBalancingSettings.getBandBalancingClientPercent24G()),
            "BandBalancingClientPercent24G is equal")
        .matches(
            v ->
                v.getStickyClientSteeringEnabled()
                    .equals(venueApGroupApLoadBalancingSettings.getStickyClientSteeringEnabled()),
            "getStickyClientSteeringEnabled is equal")
        .matches(
            v ->
                v.getStickyClientSnrThreshold()
                    .equals(venueApGroupApLoadBalancingSettings.getStickyClientSnrThreshold()),
            "getStickyClientSnrThreshold is equal")
        .matches(
            v ->
                v.getStickyClientNbrApPercentageThreshold()
                    .equals(
                        venueApGroupApLoadBalancingSettings
                            .getStickyClientNbrApPercentageThreshold()),
            "getStickyClientNbrApPercentageThreshold is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(request -> request.stream().anyMatch(Operation::hasApGroup))
        .hasSize(1)
        .first()
        .extracting(Operation::getApGroup)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::hasLoadBalancing)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::getLoadBalancing)
        .extracting(
            com.ruckus.acx.ddccm.protobuf.wifi.ApGroupLoadBalancing::getStickyClientSteering)
        .matches(
            v ->
                v.getStickyClientSteeringEnabled()
                    .equals(
                        BoolValue.of(
                            venueApGroupApLoadBalancingSettings.getStickyClientSteeringEnabled())),
            "getStickyClientSteeringEnabled is equal")
        .matches(
            v ->
                v.getStickyClientSnrThreshold()
                    .equals(
                        Int32Value.of(
                            venueApGroupApLoadBalancingSettings.getStickyClientSnrThreshold())),
            "getStickyClientSnrThreshold is equal")
        .matches(
            v ->
                v.getStickyClientNbrApPercentageThreshold()
                    .equals(
                        Int32Value.of(
                            venueApGroupApLoadBalancingSettings
                                .getStickyClientNbrApPercentageThreshold())),
            "getStickyClientNbrApPercentageThreshold is equal");
  }

  void validateUpdateVenueApClientAdmissionControlSettingsResult(
      String apGroupId,
      VenueApGroupApClientAdmissionControlSettings venueApGroupApClientAdmissionControlSettings,
      String tenantId,
      String requestId) {
    final var apGroupFromDb = repositoryUtil.find(ApGroup.class, apGroupId);
    assertThat(apGroupFromDb)
        .isNotNull()
        .extracting(ApGroup::getClientAdmissionControl)
        .isNotNull()
        .matches(
            v ->
                v.getEnable24G()
                    .equals(venueApGroupApClientAdmissionControlSettings.getEnable24G()),
            "DB ClientAdmissionControl Enable24G is equal")
        .matches(
            v ->
                v.getEnable50G()
                    .equals(venueApGroupApClientAdmissionControlSettings.getEnable50G()),
            "DB ClientAdmissionControl Enable50G is equal")
        .matches(
            v ->
                v.getMinClientCount24G()
                    .equals(venueApGroupApClientAdmissionControlSettings.getMinClientCount24G()),
            "DB ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            v ->
                v.getMinClientCount50G()
                    .equals(venueApGroupApClientAdmissionControlSettings.getMinClientCount50G()),
            "DB ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            v ->
                v.getMaxRadioLoad24G()
                    .equals(venueApGroupApClientAdmissionControlSettings.getMaxRadioLoad24G()),
            "DB ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            v ->
                v.getMaxRadioLoad50G()
                    .equals(venueApGroupApClientAdmissionControlSettings.getMaxRadioLoad50G()),
            "DB ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            v ->
                v.getMinClientThroughput24G()
                    .equals(
                        venueApGroupApClientAdmissionControlSettings.getMinClientThroughput24G()),
            "DB ClientAdmissionControl MinClientThroughput24G is equal")
        .matches(
            v ->
                v.getMinClientThroughput50G()
                    .equals(
                        venueApGroupApClientAdmissionControlSettings.getMinClientThroughput50G()),
            "DB ClientAdmissionControl MinClientThroughput50G is equal");

    var record =
        messageCaptors
            .getDdccmMessageCaptor()
            .getValue(apGroupFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();

    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApGroup))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApGroup)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApGroupRadioCustomization::hasRadio24)
        .extracting(
            radioCustomization -> radioCustomization.getRadio24().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled()
                    == venueApGroupApClientAdmissionControlSettings.getEnable24G(),
            "DDCCM ClientAdmissionControl Enable24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == venueApGroupApClientAdmissionControlSettings.getMinClientCount24G(),
            "DDCCM ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == venueApGroupApClientAdmissionControlSettings.getMaxRadioLoad24G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == venueApGroupApClientAdmissionControlSettings.getMinClientThroughput24G(),
            "DDCCM ClientAdmissionControl MinClientThroughput24G is equal");

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApGroup))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApGroup)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApGroupRadioCustomization::hasRadio50)
        .extracting(
            radioCustomization -> radioCustomization.getRadio50().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled()
                    == venueApGroupApClientAdmissionControlSettings.getEnable50G(),
            "DDCCM ClientAdmissionControl Enable50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == venueApGroupApClientAdmissionControlSettings.getMinClientCount50G(),
            "DDCCM ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == venueApGroupApClientAdmissionControlSettings.getMaxRadioLoad50G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == venueApGroupApClientAdmissionControlSettings.getMinClientThroughput50G(),
            "DDCCM ClientAdmissionControl MinClientThroughput50G is equal");
  }

  void assertActivitySuccess(String requestId, String stepId) {
    final String tenantId = txCtxExtension.getTenantId();
    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull()
        .satisfies(
            msg -> {
              assertThat(msg.getStatus()).isEqualTo(CfgStatus.ConfigurationStatus.Status.OK);
              assertThat(msg.getStep()).isEqualTo(stepId);
            })
        .extracting(CfgStatus.ConfigurationStatus::getEventDate)
        .isNotNull();
  }

  private void sendRequest(
      CfgAction cfgAction, String requestId, String venueId, String apGroupId, Object payload) {
    messageUtil.sendWifiCfgRequest(
        txCtxExtension.getTenantId(),
        requestId,
        cfgAction,
        randomName(),
        new RequestParams()
            .addPathVariable("venueId", venueId)
            .addPathVariable("apGroupId", apGroupId),
        payload);
  }

  @Nested
  class WhenConsumeUpdateApGroupX670Request {
    @Test
    @FeatureFlag(
        enable = {
          FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE, FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE3_TOGGLE,
          FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL
        })
    void consumeUpdateApGroupX670Request(Venue venue, ApGroup apGroup)
        throws JsonProcessingException {
      upgradeVenueToWifi7Version(venue, "R670");
      final String tenantId = txCtxExtension.getTenantId();
      // default
      final VenueApGroupApModelBandModeSettings request =
          objectMapper.readValue(
              """
          {
            "useVenueSettings": false,
            "apModelBandModeSettings": [
              {
                "model": "R670",
                "bandMode": "DUAL"
              }
            ]
          }
          """,
              new TypeReference<>() {});
      final String requestId1 = randomTxId();
      sendRequest(
          CfgAction.UPDATE_VENUE_AP_GROUP_BAND_MODE,
          requestId1,
          venue.getId(),
          apGroup.getId(),
          request);

      assertActivitySuccess(requestId1, ApiFlowNames.UPDATE_VENUE_AP_GROUP_BAND_MODE);

      final var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId1);
      assertThat(record).isNotNull();
      WifiConfigRequest ddccmRequest = record.getPayload();
      log.debug("ddccmRequest={}", ddccmRequest);

      assertThat(ddccmRequest.getOperationsList())
          .isNotNull()
          .matches(req -> req.stream().anyMatch(Operation::hasApGroup))
          .isNotEmpty()
          .first()
          .extracting(Operation::getApGroup)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::getApGroupApModelsList)
          .isNotNull()
          .asList()
          .first()
          .extracting(ApGroupApModel.class::cast)
          .matches(m -> m.getBandCombinationMode().getValue().equals(DdccmConstants.BAND_MODE_DUAL))
          .matches(m -> m.getModelName().equals("R670"));

      // Reset request
      final String requestId2 = randomTxId();
      final VenueApGroupApModelBandModeSettings request2 =
          objectMapper.readValue(
              """
                  {
                    "useVenueSettings": true
                  }
                  """,
              new TypeReference<>() {});
      sendRequest(
          CfgAction.UPDATE_VENUE_AP_GROUP_BAND_MODE,
          requestId2,
          venue.getId(),
          apGroup.getId(),
          request2);

      assertActivitySuccess(requestId2, ApiFlowNames.UPDATE_VENUE_AP_GROUP_BAND_MODE);

      final var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
      assertThat(record2).isNotNull();
      WifiConfigRequest ddccmRequest2 = record2.getPayload();
      log.debug("ddccmRequest2={}", ddccmRequest2);
      assertThat(ddccmRequest2.getOperationsList())
          .isNotNull()
          .matches(req -> req.stream().anyMatch(Operation::hasApGroup))
          .isNotEmpty()
          .first()
          .extracting(Operation::getApGroup)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::getApGroupApModelsList)
          .isNotNull()
          .asList()
          .first()
          .extracting(ApGroupApModel.class::cast)
          .matches(m -> m.getModelName().equals("R670"))
          .extracting(ApGroupApModel::getBandCombinationMode)
          .matches(b -> StringUtils.isEmpty(b.getValue())); // getBandCombinationMode is empty
    }

    void upgradeVenueToWifi7Version(Venue venue, String model) {
      ApVersion apVersion = ApVersionTestFixture.recommendedApVersion("7.0.0.105.212", v -> {});
      repositoryUtil.createOrUpdate(apVersion);

      VenueCurrentFirmware venueCurrentFw =
          VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, model);
      repositoryUtil.createOrUpdate(venueCurrentFw);
    }
  }

  @Nested
  class whenConsumeVenueApGroupApLoadBalancingSettingsRequest {
    @NotNull
    private static VenueApGroupApLoadBalancingSettings getVenueApGroupApLoadBalancingSettings() {
      final VenueApGroupApLoadBalancingSettings venueApGroupApLoadBalancingSettings =
          new VenueApGroupApLoadBalancingSettings();
      venueApGroupApLoadBalancingSettings.setUseVenueSettings(false);
      venueApGroupApLoadBalancingSettings.setEnabled(true);
      venueApGroupApLoadBalancingSettings.setLoadBalancingMethod(
          LoadBalancingMethodEnum.BASED_ON_CAPACITY);
      venueApGroupApLoadBalancingSettings.setSteeringMode(SteeringModeEnum.STRICT);
      venueApGroupApLoadBalancingSettings.setBandBalancingEnabled(false);
      venueApGroupApLoadBalancingSettings.setBandBalancingClientPercent24G((short) 25);
      venueApGroupApLoadBalancingSettings.setStickyClientSteeringEnabled(true);
      venueApGroupApLoadBalancingSettings.setStickyClientSnrThreshold((short) 16);
      venueApGroupApLoadBalancingSettings.setStickyClientNbrApPercentageThreshold((short) 22);
      return venueApGroupApLoadBalancingSettings;
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE})
    void thenUpdateVenueApGroupApLoadBalancingSettings(Venue venue, ApGroup apGroup) {
      final var requestId = randomTxId();
      final var tenantId = txCtxExtension.getTenantId();

      final VenueApGroupApLoadBalancingSettings venueApGroupApLoadBalancingSettings =
          getVenueApGroupApLoadBalancingSettings();

      sendRequest(
          CfgAction.UPDATE_VENUE_AP_GROUP_AP_LOAD_BALANCING_SETTINGS,
          requestId,
          venue.getId(),
          apGroup.getId(),
          venueApGroupApLoadBalancingSettings);

      assertActivitySuccess(
          requestId, ApiFlowNames.UPDATE_VENUE_AP_GROUP_AP_LOAD_BALANCING_SETTINGS);

      validateUpdateVenueApLoadBalancingSettingsResult(
          apGroup.getId(), venueApGroupApLoadBalancingSettings, tenantId, requestId);
    }
  }

  @Nested
  class whenConsumeVenueApGroupApClientAdmissionControlSettingsRequest {
    @NotNull
    private static VenueApGroupApClientAdmissionControlSettings
        getVenueApGroupApClientAdmissionControlSettings() {
      final VenueApGroupApClientAdmissionControlSettings
          venueApGroupApClientAdmissionControlSettings =
              new VenueApGroupApClientAdmissionControlSettings();
      venueApGroupApClientAdmissionControlSettings.setUseVenueSettings(false);
      venueApGroupApClientAdmissionControlSettings.setEnable24G(true);
      venueApGroupApClientAdmissionControlSettings.setMinClientCount24G((short) 11);
      venueApGroupApClientAdmissionControlSettings.setMaxRadioLoad24G((short) 50);
      venueApGroupApClientAdmissionControlSettings.setMinClientThroughput24G((short) 10);
      venueApGroupApClientAdmissionControlSettings.setEnable50G(true);
      venueApGroupApClientAdmissionControlSettings.setMinClientCount50G((short) 21);
      venueApGroupApClientAdmissionControlSettings.setMaxRadioLoad50G((short) 50);
      venueApGroupApClientAdmissionControlSettings.setMinClientThroughput50G((short) 20);
      return venueApGroupApClientAdmissionControlSettings;
    }

    @Test
    @FeatureFlag(
        enable = {
          FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE,
          FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE3_TOGGLE
        })
    void thenUpdateVenueApGroupApClientAdmissionControlSettings(Venue venue, ApGroup apGroup) {
      final var requestId = randomTxId();
      final var tenantId = txCtxExtension.getTenantId();

      final VenueApGroupApClientAdmissionControlSettings
          venueApGroupApClientAdmissionControlSettings =
              getVenueApGroupApClientAdmissionControlSettings();

      sendRequest(
          CfgAction.UPDATE_VENUE_AP_GROUP_AP_CLIENT_ADMISSION_CONTROL_SETTINGS,
          requestId,
          venue.getId(),
          apGroup.getId(),
          venueApGroupApClientAdmissionControlSettings);

      assertActivitySuccess(
          requestId, ApiFlowNames.UPDATE_VENUE_AP_GROUP_AP_CLIENT_ADMISSION_CONTROL_SETTINGS);

      validateUpdateVenueApClientAdmissionControlSettingsResult(
          apGroup.getId(), venueApGroupApClientAdmissionControlSettings, tenantId, requestId);
    }
  }
}
