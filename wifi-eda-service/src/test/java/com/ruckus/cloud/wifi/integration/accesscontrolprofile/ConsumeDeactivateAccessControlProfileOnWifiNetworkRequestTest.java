package com.ruckus.cloud.wifi.integration.accesscontrolprofile;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeDeactivateAccessControlProfileOnWifiNetworkRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeDeactivateAccessControlProfileOnWifiNetworkMessage {

    private String accessControlProfileId;
    private String networkId;

    @BeforeEach
    void beforeEach(AccessControlProfile accessControlProfile, Network network) {
      accessControlProfileId = accessControlProfile.getId();
      networkId = network.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("accessControlProfileId", accessControlProfileId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Nested
    class GivenNetworkExists {

      @BeforeEach
      void beforeEach(Tenant tenant, Network network, AccessControlProfile accessControlProfile) {
        repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());
        network.getWlan().getAdvancedCustomization().setAccessControlProfile(accessControlProfile);
        network.getWlan().getAdvancedCustomization().setAccessControlEnable(true);
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      }

      @Test
      void givenAccessControlProfileActivatedOnNetwork(Tenant tenant) {
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.DEACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        assertThat(
                messageCaptors
                    .getCmnCfgCollectorMessageCaptor()
                    .getValue(tenant.getId(), Duration.ofSeconds(1)))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(1)
            .first()
            .matches(o -> o.getOpType() == OpType.MOD)
            .matches(o -> o.getId().equals(accessControlProfileId));
        ;

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a ->
                    a.getStep()
                        .equals(ApiFlowNames.DEACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK));
      }

      @Test
      void givenAccessControlProfileActivatedOnNetworkButDeactivateAnotherAccessControlProfile(
          Tenant tenant) {
        AccessControlProfile accessControlProfile =
            AccessControlProfileTestFixture.randomAccessControlProfile();
        accessControlProfile = repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId());
        accessControlProfileId = accessControlProfile.getId();
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.DEACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        assertThat(
                messageCaptors
                    .getCmnCfgCollectorMessageCaptor()
                    .getValue(tenant.getId(), Duration.ofSeconds(1)))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(1)
            .first()
            .matches(o -> o.getOpType() == OpType.MOD)
            .matches(o -> o.getId().equals(accessControlProfileId));
        ;

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a ->
                    a.getStep()
                        .equals(ApiFlowNames.DEACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK));
      }
    }
  }
}
