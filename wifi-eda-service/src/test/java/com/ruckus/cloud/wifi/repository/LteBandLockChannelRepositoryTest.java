package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        'e465bac6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO lte_band_lock_channel (id, venue, tenant) VALUES (
        '79e563afd05647bab1abd48e20f5eb7b',
        'e465bac6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc');
    """)
public class LteBandLockChannelRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String VENUE_ID = "e465bac6afb747a4987d0d0945f77221";

  @Autowired
  private LteBandLockChannelRepository target;

  @Test
  void findByVenueIdAndModelTest() {
    var result = target.findByVenueIdAndTenantId(VENUE_ID, TENANT_ID);
    assertEquals(1, result.size());
  }
}
