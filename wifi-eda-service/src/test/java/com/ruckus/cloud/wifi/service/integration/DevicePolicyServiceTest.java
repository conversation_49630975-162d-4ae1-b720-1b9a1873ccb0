package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.servicemodel.enums.SortOrderEnum.DESC;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.DevicePolicyTestFixture.copyOf;
import static com.ruckus.cloud.wifi.test.fixture.DevicePolicyTestFixture.randomDevicePolicy;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractWlan;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessPolicyQueryData;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessPolicyQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AccessEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.mapper.DevicePolicyMerger;
import com.ruckus.cloud.wifi.mapper.DevicePolicyMergerImpl;
import com.ruckus.cloud.wifi.repository.AccessControlProfileRepository;
import com.ruckus.cloud.wifi.repository.DevicePolicyRepository;
import com.ruckus.cloud.wifi.repository.DevicePolicyRuleRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.WlanRepository;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.impl.DevicePolicyServiceCtrlImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.DevicePolicyTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.TestPropertySource;

@Tag("DevicePolicyTest")
@WifiJpaDataTest
@TestPropertySource(properties = {"default.device-policy.tenant-max-count: 10"})
class DevicePolicyServiceTest {

  @Autowired
  private DevicePolicyRepository devicePolicyRepository;
  @Autowired
  private AccessControlProfileRepository accessControlProfileRepository;
  @Autowired
  private DevicePolicyMerger devicePolicyMerger;
  @Autowired
  private DevicePolicyRuleRepository devicePolicyRuleRepository;
  @Autowired
  private WlanRepository wlanRepository;
  @Autowired
  private NetworkRepository networkRepository;
  @SpyBean
  private DevicePolicyServiceCtrlImpl devicePolicyService;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void testGetAllDevicePolicies(DevicePolicy devicePolicy) throws Exception {
    assertEquals(devicePolicy, devicePolicyService.getAllDevicePolicies().get(0));
  }

  @Test
  void testGetDevicePolicy(DevicePolicy devicePolicy) throws Exception {
    assertEquals(devicePolicy, devicePolicyService.getDevicePolicy(devicePolicy.getId()));
  }

  @Test
  void testGetDevicePolicyByQuery_sortNameDesc(DevicePolicy devicePolicy) throws Exception {
    // Given
    devicePolicy.setName("name1");
    repositoryUtil.createOrUpdate(devicePolicy, devicePolicy.getTenant().getId(), randomTxId());
    DevicePolicy policy2 = DevicePolicyTestFixture.randomDevicePolicy(devicePolicy.getTenant(),
        (dp) -> {
        });
    policy2.setName("name2");
    repositoryUtil.createOrUpdate(policy2, devicePolicy.getTenant().getId(), randomTxId());

    // When
    QueryRequest queryRequest = new QueryRequest();
    queryRequest.setSortOrder(DESC);
    queryRequest.setSortField("name");
    AccessPolicyQueryResponse queryResponse = devicePolicyService.getDevicePolicyByQuery(
        queryRequest);

    // Then
    assertEquals(2, queryResponse.getTotalCount());
    assertEquals(1, queryResponse.getTotalPages());
    assertEquals(1, queryResponse.getPage());
    assertEquals(2, queryResponse.getData().size());
    assertEquals(List.of("name2", "name1"),
        queryResponse.getData().stream().map(AccessPolicyQueryData::getName)
            .collect(Collectors.toList()));
  }

  @Test
  void testAddDevicePolicy(Tenant tenant) throws Exception {
    // Given
    DevicePolicy devicePolicy = DevicePolicyTestFixture.randomDevicePolicy();
    // When
    devicePolicyService.addDevicePolicy(devicePolicy);
    // Then
    assertDevicePolicy(devicePolicy, devicePolicyService.getDevicePolicy(devicePolicy.getId()));
  }

  @Test
  void testAddDevicePolicy_existName(DevicePolicy devicePolicy) throws Exception {
    // When
    assertThrows(InvalidPropertyValueException.class,
        () -> devicePolicyService.addDevicePolicy(devicePolicy));
  }

  @Test
  void testAddDevicePolicy_reachMaxCount(Tenant tenant) throws Exception {
    // Given
    for (int i = 0; i < 10; i++) {
      DevicePolicy devicePolicy = DevicePolicyTestFixture.randomDevicePolicy();
      repositoryUtil.createOrUpdate(devicePolicy, tenant.getId(), randomTxId());
    }
    assertThrows(InvalidPropertyValueException.class,
        () -> devicePolicyService.addDevicePolicy(DevicePolicyTestFixture.randomDevicePolicy()));
  }

  @Test
  void testUpdateDevicePolicy_addExtraRule(DevicePolicy devicePolicy) throws Exception {
    // Given
    DevicePolicy savedDevicePolicy = copyOf(devicePolicy);
    savedDevicePolicy.getRules().add(Generators.devicePolicyRule(savedDevicePolicy).generate());
    savedDevicePolicy.setName("updated device policy");
    savedDevicePolicy.setDefaultAccess(AccessEnum.BLOCK);
    savedDevicePolicy.setDescription("updated device policy description");

    // When
    devicePolicyService.updateDevicePolicy(devicePolicy.getId(), savedDevicePolicy);

    // Then
    assertDevicePolicy(savedDevicePolicy,
        devicePolicyService.getDevicePolicy(devicePolicy.getId()));
  }

  @Test
  void testUpdateDevicePolicy_removeRule(DevicePolicy devicePolicy) throws Exception {
    // Given
    DevicePolicy savedDevicePolicy = copyOf(devicePolicy);
    savedDevicePolicy.getRules().remove(0);
    savedDevicePolicy.getRules().add(Generators.devicePolicyRule(savedDevicePolicy).generate());
    savedDevicePolicy.setName("updated device policy");
    savedDevicePolicy.setDefaultAccess(AccessEnum.BLOCK);
    savedDevicePolicy.setDescription("updated device policy description");

    // When
    devicePolicyService.updateDevicePolicy(devicePolicy.getId(), savedDevicePolicy);

    // Then
    assertDevicePolicy(savedDevicePolicy,
        devicePolicyService.getDevicePolicy(devicePolicy.getId()));
  }

  @Test
  void testUpdateDevicePolicy_existName(DevicePolicy devicePolicy) throws Exception {
    // Given
    DevicePolicy devicePolicy1 = DevicePolicyTestFixture.randomDevicePolicy();
    repositoryUtil.createOrUpdate(devicePolicy1, devicePolicy.getTenant().getId(), randomTxId());
    DevicePolicy savedDevicePolicy = copyOf(devicePolicy);
    savedDevicePolicy.setName(devicePolicy1.getName());

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> devicePolicyService.updateDevicePolicy(devicePolicy.getId(), savedDevicePolicy));
  }

  @Test
  void testDeleteDevicePolicies_ruckusOne(DevicePolicy devicePolicy) throws Exception {
    // Given
    AccessControlProfile accessControlProfile = accessControlProfile().generate();
    accessControlProfile.setDevicePolicy(devicePolicy);
    repositoryUtil.createOrUpdate(accessControlProfile, devicePolicy.getTenant().getId(),
        randomTxId());

    // When
    devicePolicyService.deleteBulkDevicePolicies(List.of(devicePolicy.getId()));

    // Then
    assertThrows(ObjectNotFoundException.class,
        () -> devicePolicyService.getDevicePolicy(devicePolicy.getId()));
    AccessControlProfile savedAccessControlProfile = repositoryUtil.find(AccessControlProfile.class,
        accessControlProfile.getId());
    assertNull(savedAccessControlProfile.getDevicePolicy());
    assertFalse(savedAccessControlProfile.getDevicePolicyEnable());
  }

  @Test
  void testDeleteDevicePolicies_ruckusOne_networkInUse(Network network, DevicePolicy devicePolicy)
      throws Exception {
    // Given
    network.getWlan().getAdvancedCustomization().setDevicePolicy(devicePolicy);
    repositoryUtil.createOrUpdate(network, devicePolicy.getTenant().getId(), randomTxId());

    // Then
    assertThrows(ObjectInUseException.class, () -> devicePolicyService.deleteBulkDevicePolicies(
        List.of(devicePolicy.getId(), randomId())));
    assertEquals(devicePolicy, devicePolicyService.getDevicePolicy(devicePolicy.getId()));
  }

  @Test
  void testAddDevicePolicyV1_1(final Tenant tenant) throws Exception {
    // Given
    final DevicePolicy devicePolicy = randomDevicePolicy();

    // When
    final DevicePolicy addedDevicePolicy =
        devicePolicyService.addDevicePolicyV1_1(devicePolicy);

    // Then
    assertDevicePolicy(addedDevicePolicy,
        devicePolicyService.getDevicePolicyV1_1(devicePolicy.getId()));

    // Then
    assertThat(
        repositoryUtil.find(
            DevicePolicy.class,
            addedDevicePolicy.getId(),
            tenant.getId()))
        .isNotNull();
  }

  @Test
  void testUpdateDevicePolicyV1_1(final Tenant tenant) throws Exception {
    // Given
    DevicePolicy devicePolicy = randomDevicePolicy();
    final DevicePolicy addedDevicePolicy =
        devicePolicyService.addDevicePolicyV1_1(
            devicePolicy);

    // When
    resetTxId();
    devicePolicy.setName("newServiceName");
    devicePolicy.getRules().add(Generators.devicePolicyRule(devicePolicy).generate());

    final DevicePolicy updatedDevicePolicy =
        devicePolicyService.updateDevicePolicyV1_1(
            addedDevicePolicy.getId(), devicePolicy);

    // Then
    assertDevicePolicy(updatedDevicePolicy,
        devicePolicyService.getDevicePolicyV1_1(devicePolicy.getId()));

    assertThat(
        repositoryUtil.find(
            DevicePolicy.class,
            updatedDevicePolicy.getId(),
            tenant.getId()))
        .isNotNull();
  }

  @Test
  void testDeleteDevicePolicyV1_1(final Tenant tenant) throws Exception {
    // Given
    DevicePolicy devicePolicy = randomDevicePolicy();
    final DevicePolicy addedDevicePolicy =
        devicePolicyService.addDevicePolicyV1_1(devicePolicy);

    // When
    resetTxId();
    devicePolicyService.deleteDevicePolicyV1_1(addedDevicePolicy.getId());

    // Then
    assertThat(repositoryUtil.find(DevicePolicy.class,
        addedDevicePolicy.getId(), tenant.getId()))
        .isNull();
  }

  @Test
  void testDeleteDevicePolicyV1_1_withAccessControlProfile(final Tenant tenant,
      final AccessControlProfile accessControlProfile) throws Exception {
    // Given
    DevicePolicy devicePolicy = randomDevicePolicy();
    final DevicePolicy addedDevicePolicy = devicePolicyService.addDevicePolicyV1_1(devicePolicy);

    // When active on accessControlProfile
    resetTxId();
    devicePolicyService.activateDevicePolicyOnAccessControlProfile(accessControlProfile.getId(),
        addedDevicePolicy.getId());

    // Then
    resetTxId();
    assertThatNoException().isThrownBy(
        () -> devicePolicyService.deleteDevicePolicyV1_1(addedDevicePolicy.getId()));

    // Then
    assertThat(repositoryUtil.find(AccessControlProfile.class, accessControlProfile.getId(),
        tenant.getId()))
        .isNotNull()
        .matches(a ->!a.getDevicePolicyEnable())
        .extracting(AccessControlProfile::getDevicePolicy)
        .isNull();;
  }

  @Test
  void testDeleteDevicePolicyV1_1_withNetwork(final Tenant tenant, final Network network)
      throws Exception {
    // Given
    DevicePolicy devicePolicy = randomDevicePolicy();
    final DevicePolicy addedDevicePolicy = devicePolicyService.addDevicePolicyV1_1(devicePolicy);

    // When active on network
    resetTxId();
    devicePolicyService.activateDevicePolicyOnWifiNetwork(network.getId(),
        addedDevicePolicy.getId());

    // Then
    assertThatExceptionOfType(ObjectInUseException.class)
        .isThrownBy(() -> devicePolicyService.deleteDevicePolicyV1_1(addedDevicePolicy.getId()))
        .withMessage(Errors.WIFI_10433.message())
        .extracting(ObjectInUseException::getErrorCode)
        .isEqualTo(Errors.WIFI_10433);

    // When deactivate from network
    resetTxId();
    devicePolicyService.deactivateDevicePolicyOnWifiNetwork(network.getId(),
        addedDevicePolicy.getId());

    // Then
    resetTxId();
    assertThatNoException().isThrownBy(
        () -> devicePolicyService.deleteDevicePolicyV1_1(addedDevicePolicy.getId()));
  }

  @Test
  void testActivateDevicePolicyOnAccessControlProfile(Tenant tenant,
      AccessControlProfile accessControlProfile) throws Exception {
    // Given
    DevicePolicy devicePolicy = randomDevicePolicy();
    final DevicePolicy addedDevicePolicy = devicePolicyService.addDevicePolicyV1_1(devicePolicy);

    // When
    resetTxId();
    devicePolicyService.activateDevicePolicyOnAccessControlProfile(accessControlProfile.getId(),
        addedDevicePolicy.getId());

    // Then
    assertThat(repositoryUtil.find(AccessControlProfile.class, accessControlProfile.getId(),
        tenant.getId()))
        .isNotNull()
        .matches(AccessControlProfile::getDevicePolicyEnable)
        .extracting(AccessControlProfile::getDevicePolicy)
        .isNotNull()
        .matches(policy -> policy.getId().equals(addedDevicePolicy.getId()));
  }

  @Test
  void testDeactivateDevicePolicyOnAccessControlProfile(Tenant tenant,
      AccessControlProfile accessControlProfile) throws Exception {
    // Given
    DevicePolicy devicePolicy = randomDevicePolicy();
    final DevicePolicy addedDevicePolicy = devicePolicyService.addDevicePolicyV1_1(devicePolicy);

    // When
    resetTxId();
    devicePolicyService.activateDevicePolicyOnAccessControlProfile(accessControlProfile.getId(),
        addedDevicePolicy.getId());
    resetTxId();
    devicePolicyService.deactivateDevicePolicyOnAccessControlProfile(accessControlProfile.getId(),
        addedDevicePolicy.getId());

    // Then
    assertThat(repositoryUtil.find(AccessControlProfile.class, accessControlProfile.getId(),
        tenant.getId()))
        .isNotNull()
        .matches(a ->!a.getDevicePolicyEnable())
        .extracting(AccessControlProfile::getDevicePolicy)
        .isNull();;
  }

  @Test
  void testActivateDevicePolicyOnWifiNetwork(Tenant tenant, Network network)
      throws Exception {
    // Given
    DevicePolicy devicePolicy = randomDevicePolicy();
    final DevicePolicy addedDevicePolicy = devicePolicyService.addDevicePolicyV1_1(devicePolicy);

    // When
    resetTxId();
    devicePolicyService.activateDevicePolicyOnWifiNetwork(network.getId(),
        addedDevicePolicy.getId());

    // Then
    assertThat(repositoryUtil.find(Network.class, network.getId(), tenant.getId()))
        .isNotNull()
        .extracting(Network::getWlan)
        .extracting(AbstractWlan::getAdvancedCustomization)
        .matches(adc -> adc.getDevicePolicy().getId().equals(addedDevicePolicy.getId()));
  }

  @Test
  void testDeactivateDevicePolicyOnWifiNetwork(Tenant tenant, Network network)
      throws Exception {
    // Given
    DevicePolicy devicePolicy = randomDevicePolicy();
    final DevicePolicy addedDevicePolicy = devicePolicyService.addDevicePolicyV1_1(devicePolicy);

    // When
    resetTxId();
    devicePolicyService.activateDevicePolicyOnWifiNetwork(network.getId(),
        addedDevicePolicy.getId());
    resetTxId();
    devicePolicyService.deactivateDevicePolicyOnWifiNetwork(network.getId(),
        addedDevicePolicy.getId());

    // Then
    assertThat(repositoryUtil.find(Network.class, network.getId(), tenant.getId()))
        .isNotNull()
        .extracting(Network::getWlan)
        .extracting(AbstractWlan::getAdvancedCustomization)
        .extracting(WlanAdvancedCustomization::getDevicePolicy)
        .isNull();;
  }

  private void resetTxId() {
    TxCtxHolder.set(new TxCtx(TxCtxHolder.tenantId(), randomTxId(), randomName(), randomName()));
  }

  private void assertDevicePolicy(DevicePolicy expected, DevicePolicy actual) {
    assertNotNull(expected);
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getDescription(), actual.getDescription());
    assertEquals(expected.getDefaultAccess(), actual.getDefaultAccess());
    assertEquals(expected.getRules().size(), actual.getRules().size());

    for (DevicePolicyRule expectedRule : expected.getRules()) {
      DevicePolicyRule actualRule = actual.getRules().stream()
          .filter(rule -> expectedRule.getName().equals(rule.getName())).findFirst().orElse(null);
      if (expectedRule.getId() != null) {
        assertEquals(expectedRule.getId(), actualRule.getId());
      }
      assertEquals(expectedRule.getName(), actualRule.getName());
      assertEquals(expectedRule.getAction(), actualRule.getAction());
      assertEquals(expectedRule.getDeviceType(), actualRule.getDeviceType());
      assertEquals(expectedRule.getOsVendor(), actualRule.getOsVendor());
      assertEquals(expectedRule.getDownloadRateLimit(), actualRule.getDownloadRateLimit());
      assertEquals(expectedRule.getUploadRateLimit(), actualRule.getUploadRateLimit());
      assertEquals(expectedRule.getVlan(), actualRule.getVlan());
    }
  }


  @TestConfiguration
  public static class TestConfig {

    @Bean
    public DevicePolicyMerger merger() {
      return new DevicePolicyMergerImpl();
    }
  }
}
