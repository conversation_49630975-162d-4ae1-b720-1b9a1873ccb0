package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.CompatibilityActivityInfo;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.time.Instant;
import java.util.Date;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

// @formatter:off
@Sql(
    statements =
        """
INSERT INTO tenant (id) VALUES ('ccb1fd500809407bb29a59838bb7f003');
INSERT INTO compatibility_activity_info(id, activity_id, impacted_count, tenant) VALUES
('31acbd54-0a5e-4608-9a94-82132c10df1e', 'cff4a0bf-9d1b-4fc6-9089-8fb8da83385e', 100, 'ccb1fd500809407bb29a59838bb7f003');
""")
// @formatter:on
@WifiJpaDataTest
class CompatibilityActivityInfoRepositoryTest {
  private static final String TENANT_ID = "ccb1fd500809407bb29a59838bb7f003";
  private static final String ACTIVITY_ID = "cff4a0bf-9d1b-4fc6-9089-8fb8da83385e";

  @Autowired private CompatibilityActivityInfoRepository unit;

  @Nested
  class WhenFindByActivityIdAndTenantId {
    @Test
    void givenInfoNotExists() {
      final var activityId = randomTxId();

      assertThat(unit.findByActivityIdAndTenantId(activityId, TENANT_ID)).isNotNull().isEmpty();
    }

    @Test
    void givenInfoExists() {
      assertThat(unit.findByActivityIdAndTenantId(ACTIVITY_ID, TENANT_ID))
          .isNotNull()
          .get()
          .extracting(CompatibilityActivityInfo::getImpactedCount)
          .isEqualTo(100);
    }
  }

  // @formatter:off
  @Sql(
      statements =
          """
      INSERT INTO tenant (id) VALUES ('1fd0a84a5c03414aa4e18110d9001735');
      INSERT INTO compatibility_activity_info(id, tenant, created_date) VALUES
      ('da3bcff9f27848e183715fdb77983f39', '1fd0a84a5c03414aa4e18110d9001735', '2024-05-30T00:00:00Z');
      INSERT INTO compatibility_activity_info(id, tenant, created_date) VALUES
      ('4ba412eed59448b3b4a0f228756d0f05', '1fd0a84a5c03414aa4e18110d9001735', '2024-05-31T00:00:00Z');
      INSERT INTO compatibility_activity_info(id, tenant, created_date) VALUES
      ('c9adef18b12b44cfbf67f8f46f8a31be', '1fd0a84a5c03414aa4e18110d9001735', '2024-06-01T00:00:00Z');
      INSERT INTO compatibility_activity_info(id, tenant, created_date) VALUES
      ('fd474e74970f4d1d8e3d74769176a178', '1fd0a84a5c03414aa4e18110d9001735', '2024-06-02T00:00:00Z');
      INSERT INTO compatibility_activity_info(id, tenant, created_date) VALUES
      ('ef61822440c444dcbec6830364bb4288', '1fd0a84a5c03414aa4e18110d9001735', '2024-06-03T00:00:00Z');
      INSERT INTO compatibility_activity_info(id, tenant, created_date) VALUES
      ('507ca6ee86c24b32abb611ea9f39bb33', '1fd0a84a5c03414aa4e18110d9001735', '2024-06-05T00:00:00Z');
      """)
  // @formatter:on
  @Nested
  class WhenDeleteByCreatedDateBefore {
    private final Date before = Date.from(Instant.parse("2024-06-04T00:00:00Z"));

    @Test
    void thenReturnDeletedCount() {
      BDDAssertions.then(unit.deleteByCreatedDateBefore(before)).isEqualTo(5);
    }
  }
}
