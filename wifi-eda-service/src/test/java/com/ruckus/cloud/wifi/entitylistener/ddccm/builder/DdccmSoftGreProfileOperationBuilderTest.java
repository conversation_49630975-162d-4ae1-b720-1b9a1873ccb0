package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.ruckus.acx.ddccm.protobuf.wifi.CcmTunnelProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Optional;
import java.util.Set;

import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class DdccmSoftGreProfileOperationBuilderTest {

  @SpyBean private DdccmSoftGreProfileOperationBuilder unit;

  @RegisterExtension public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  void testAddSoftGreProfile() {
    var profile = Generators.softGreProfile().generate();
    var operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasCcmTunnelProfile)
        .allSatisfy(operation -> {
          validate(profile, operation.getCcmTunnelProfile());
          validateIpv4GatewayAddress(profile, operation.getCcmTunnelProfile());
        });
  }

  @Test
  void testAddSoftGreProfileWithIPv6GatewayAddress() {
    var profile = Generators.softGreProfileIpv6GatewayAddress().generate();
    var operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
      .hasSize(1)
      .allSatisfy(Operation::hasCcmTunnelProfile)
      .allSatisfy(operation -> {
        validate(profile, operation.getCcmTunnelProfile());
        validateIpv6GatewayAddress(profile, operation.getCcmTunnelProfile());
      });
  }

  @Test
  void testModifySoftGreProfile() {
    var profile = Generators.softGreProfile().generate();
    var operations =
        unit.build(new ModifiedTxEntity<>(profile, Set.of("description")), emptyTxChanges());
    assertThat(operations).isEmpty();
    operations =
        unit.build(
            new ModifiedTxEntity<>(profile, Set.of("description", "updatedDate")),
            emptyTxChanges());
    assertThat(operations).isEmpty();

    operations = unit.build(new ModifiedTxEntity<>(profile, Set.of("name")), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasCcmTunnelProfile)
        .allSatisfy(operation -> validate(profile, operation.getCcmTunnelProfile()));
  }

  @Test
  void testDeleteSoftGreProfile() {
    var profile = Generators.softGreProfile().generate();
    var operations = unit.build(new DeletedTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(
            operation ->
                operation.hasCcmTunnelProfile()
                    && profile.getId().equals(operation.getCcmTunnelProfile().getId()));
  }

  private void validate(SoftGreProfile source, CcmTunnelProfile target) {
    assertEquals(source.getId(), target.getId());
    assertEquals(CcmTunnelProfile.TunnelType.SoftGRE, target.getTunnelType());
    assertEquals(source.getName(), target.getName());
    assertEquals(source.getMtuType().name(), target.getTunnelMtuAutoEnabled().name());
    assertEquals(unit.getMtuSize(source).intValue(), target.getTunnelMtuSize());
    assertEquals(source.getKeepAliveInterval().intValue(), target.getTunnelKeepAliveInterval());
    assertEquals(source.getKeepAliveRetryTimes().intValue(), target.getTunnelKeepAliveRetryLimit());
    assertEquals(source.getDisassociateClientEnabled(), target.getForceDisassociateClient());
    assertFalse(target.getEnableDualTunnels());
  }

  private void validateIpv4GatewayAddress(SoftGreProfile source, CcmTunnelProfile target) {
    assertEquals(source.getPrimaryGateway(), target.getPrimaryGateway().getValue());
    assertEquals(
      Optional.ofNullable(source.getSecondaryGateway()).orElse(""),
      target.getSecondaryGateway().getValue());
  }

  private void validateIpv6GatewayAddress(SoftGreProfile source, CcmTunnelProfile target) {
    assertEquals(source.getPrimaryGateway(), target.getPrimaryGatewayV6().getValue());
    assertEquals(
      Optional.ofNullable(source.getSecondaryGateway()).orElse(""),
      target.getSecondaryGatewayV6().getValue());
  }


  @Test
  @FeatureFlag(enable = {WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE})
  void testAddSoftGreProfileWithGatewayFailback() {
    var profile = Generators.softGreProfileGatewayFailback().generate();
    var operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasCcmTunnelProfile)
        .allSatisfy(operation -> validateGatewayFailback(profile, operation.getCcmTunnelProfile()));
  }

  @Test
  @FeatureFlag(enable = {WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE})
  void testModifySoftGreProfileWithGatewayFailback() {
    var profile = Generators.softGreProfileGatewayFailback().generate();
    var operations =
        unit.build(new ModifiedTxEntity<>(profile, Set.of("description")), emptyTxChanges());
    assertThat(operations).isEmpty();
    operations =
        unit.build(
            new ModifiedTxEntity<>(profile, Set.of("description", "updatedDate")),
            emptyTxChanges());
    assertThat(operations).isEmpty();

    operations = unit.build(new ModifiedTxEntity<>(profile, Set.of("name")), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasCcmTunnelProfile)
        .allSatisfy(operation -> validateGatewayFailback(profile, operation.getCcmTunnelProfile()));
  }

  @Test
  @FeatureFlag(enable = {WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE})
  void testAddSoftGreProfileWithGatewayFailbackNull() {
    var profile = Generators.softGreProfile().generate();
    var operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasCcmTunnelProfile)
        .allSatisfy(operation -> validate(profile, operation.getCcmTunnelProfile()))
        .allMatch(operation -> !operation.getCcmTunnelProfile().getGatewayFailBack().getEnabled());
  }

  private void validateGatewayFailback(SoftGreProfile source, CcmTunnelProfile target) {
    assertEquals(source.getId(), target.getId());
    assertEquals(CcmTunnelProfile.TunnelType.SoftGRE, target.getTunnelType());
    assertEquals(source.getName(), target.getName());
    assertEquals(source.getPrimaryGateway(), target.getPrimaryGateway().getValue());
    assertEquals(source.getMtuType().name(), target.getTunnelMtuAutoEnabled().name());
    assertEquals(unit.getMtuSize(source).intValue(), target.getTunnelMtuSize());
    assertEquals(source.getKeepAliveInterval().intValue(), target.getTunnelKeepAliveInterval());
    assertEquals(source.getKeepAliveRetryTimes().intValue(), target.getTunnelKeepAliveRetryLimit());
    assertEquals(source.getDisassociateClientEnabled(), target.getForceDisassociateClient());
    assertFalse(target.getEnableDualTunnels());
    assertEquals(
        Optional.ofNullable(source.getSecondaryGateway()).orElse(""),
        target.getSecondaryGateway().getValue());

    assertEquals(target.getGatewayFailBack().getEnabled(), true);
    assertEquals(source.getGatewaySecondaryToPrimaryTimer(), (short) target.getGatewayFailBack().getS2PTimer());
  }
}
