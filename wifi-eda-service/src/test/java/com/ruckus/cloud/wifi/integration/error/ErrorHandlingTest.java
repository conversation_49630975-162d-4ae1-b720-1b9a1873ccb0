package com.ruckus.cloud.wifi.integration.error;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

import com.ruckus.cloud.wifi.error.WifiCommonException;
import com.ruckus.cloud.wifi.error.WifiEdaError;
import com.ruckus.cloud.wifi.service.core.api.rest.ErrorResponse;
import com.ruckus.cloud.wifi.service.core.exception.WifiCommonExceptionHandler;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import java.util.Locale;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.WebRequest;

@WifiIntegrationTest
public class ErrorHandlingTest {

  @Autowired
  WifiCommonExceptionHandler exceptionHandler;

  @Autowired
  MessageSource messageSource;

  @Test
  void testWifiCommonException() {
    WifiCommonException exception = new WifiCommonException(WifiEdaError.INVALID_SORT_FIELDS,
        "foo");
    ResponseEntity<Object> response = exceptionHandler.handleGeneralException(exception,
        mock(WebRequest.class));

    ErrorResponse errors = (ErrorResponse) response.getBody();
    assertThat(errors.getErrors()).hasSize(1);
    assertThat(errors.getErrors().get(0).getCode()).isEqualTo(
        WifiEdaError.INVALID_SORT_FIELDS.code());

    assertThat(errors.getErrors().get(0).getMessage()).isEqualTo(
        messageSource.getMessage(WifiEdaError.INVALID_SORT_FIELDS.code() + ".message", new Object[]{"foo"},
            Locale.getDefault()));
  }
}
