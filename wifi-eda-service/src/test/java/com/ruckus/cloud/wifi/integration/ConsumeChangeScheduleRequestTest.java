package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.acx.aggregate.notification.protobuf.message.NotificationRecord;
import com.ruckus.cloud.tenantservice.api.GetTenantResponse;
import com.ruckus.cloud.tenantservice.api.TenantType;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.servicemodel.enums.ApVersionCategory;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.viewmodel.ChangeScheduleRequestView;
import com.ruckus.cloud.wifi.viewmodel.ChangeScheduleVenueRequestView;
import java.sql.Date;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
class ConsumeChangeScheduleRequestTest {
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private UpgradeScheduleRepository upgradeScheduleRepository;

  @Autowired
  private TenantClient tenantClient;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Value("${topic.kairos.jobRegister}")
  private String kairosJobRegister;

  @Value("${topic.nuvo.notificationRequests}")
  private String nuvoNotificationRequests;

  @Value("${topic.aggregateNotification.records}")
  private String aggregateNotificationRecords;

  private List<String> AP_LIST = List.of("R560", "R550", "R500", "R560", "R550");

  @Test
  void changeSchedule(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55555") ApVersion version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55556") ApVersion targetVersion) {
    changeScheduleWithAggregateMessage(tenant, version, targetVersion, List.of("560"));
  }

  @Test
  void thenChangeScheduleWithImpactModelEmail(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
          ApVersion activeVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.95")
          ApVersion legacyVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100")
          ApVersion targetVersion) {
    changeScheduleWithMultiSchedulesWithAggregateMsg(tenant, activeVersion, legacyVersion, targetVersion, AP_LIST);
  }

  @Test
  void thenChangeScheduleWithMultipleSchedules(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
          ApVersion activeVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.95")
          ApVersion legacyVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100")
          ApVersion targetVersion) {
    changeScheduleWithMultiSchedulesWithAggregateMsg(tenant, activeVersion, legacyVersion, targetVersion, AP_LIST);
  }

  @Nested
  class ChangeScheduleMultipleTimezonesTest {

    List<Venue> venueList;

    @BeforeEach
    void setup(Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
        ApVersion activeVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.95")
        ApVersion legacyVersion) {
      venueList =
          List.of(
              VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("Asia/Taipei")),
              VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("America/Los_Angeles")),
              VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("America/Los_Angeles")),
              VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("America/New_York")));

      tenant.setLatestReleaseVersion(activeVersion);
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId());
      ScheduleTimeSlot sts = newScheduleTimeSlot();
      repositoryUtil.createOrUpdate(sts, tenant.getId(), randomTxId());
      venueList.forEach(
          venue -> {
            repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
            UpgradeSchedule schedule = newUpgradeSchedule(sts, activeVersion, tenant);
            schedule.setVenue(venue);
            repositoryUtil.createOrUpdate(schedule, tenant.getId(), randomTxId());
            UpgradeScheduleFirmwareVersion scheduleFirmwareVersion1 =
                newUpgradeScheduleFirmwareVersion(schedule, activeVersion, tenant);
            repositoryUtil.createOrUpdate(scheduleFirmwareVersion1, tenant.getId(), randomTxId());

            UpgradeScheduleFirmwareVersion scheduleFirmwareVersion2 =
                newUpgradeScheduleFirmwareVersion(schedule, legacyVersion, tenant);
            repositoryUtil.createOrUpdate(scheduleFirmwareVersion2, tenant.getId(), randomTxId());

            ApGroup apGroup =
                ApGroupTestFixture.randomApGroup(venue, (group) -> group.setIsDefault(true));
            repositoryUtil.createOrUpdate(apGroup, tenant.getId(), randomTxId());
            venue.setApGroups(List.of(apGroup));
            venue.setWifiFirmwareVersion(activeVersion);
            repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

            Ap ap = new Ap(randomSerialNumber());
            ap.setTenant(tenant);
            ap.setApGroup(apGroup);
            repositoryUtil.createOrUpdate(ap, tenant.getId(), randomTxId());

            ApFirmwareUpgradeRequest afur = new ApFirmwareUpgradeRequest(randomId());
            afur.setModel("R560");
            afur.setSerialNumber(ap.getId());
            afur.setTenant(tenant);
            repositoryUtil.createOrUpdate(afur, tenant.getId(), randomTxId());

            Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
            Assertions.assertNotNull(updatedVenue);
            assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull()
                .hasSize(1)
                .singleElement()
                .matches(us -> us.getVersion().getId().equals(activeVersion.getId()));
          });
    }

    @Test
    void thenChangeScheduleWithMultipleTimezones(Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100")
        ApVersion targetVersion) {
      changeSchedule(tenant, targetVersion);
    }

    @Test
    @FeatureFlag(
        enable = {
            FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM
        })
    void thenChangeScheduleWithMultipleTimezonesAndLongTermFF(Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100")
        ApVersion targetVersion) {
      changeSchedule(tenant, targetVersion);
    }

    private void changeSchedule(Tenant tenant, ApVersion targetVersion) {
      Set<String> usIds = upgradeScheduleRepository.findAll().stream().map(UpgradeSchedule::getId)
          .collect(Collectors.toSet());

      ChangeScheduleRequestView csrv = newChangeScheduleRequestView(venueList,
          targetVersion.getId());
      // When
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgExtendedAction.CHANGE_UPGRADE_SCHEDULE,
          randomName(),
          new RequestParams(),
          csrv);

      assertThat(Objects.requireNonNull(messageUtil.receive(kairosJobRegister)).headers())
          .filteredOn(h -> h.key().equals("jobName")).map(Header::value)
          .satisfies(jobName -> {
            assertThat(usIds).noneMatch(id -> id.equals(new String(jobName.get(0))));
          });
      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
    }
  }

  @Nested
  class ChangeScheduleWithAggregateMessageTest {

    @BeforeEach
    void givenMSPEC(final Tenant tenant) {
      when(tenantClient.getTenantData(eq(tenant.getId()), anyString())).thenReturn(
        GetTenantResponse.newBuilder()
          .setTenantType(TenantType.MSP_EC)
          .build());
    }
    @Test
    void thenChangeScheduleWithMultipleSchedulesWithAggregateMessageAndMultipleUpgradeScheduleEnabled(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
      ApVersion activeVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.95")
      ApVersion legacyVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100")
      ApVersion targetVersion) {
      changeScheduleWithMultiSchedulesWithAggregateMsg(tenant, activeVersion, legacyVersion, targetVersion, AP_LIST);
      validateAggregateNotificationMessage(List.of(targetVersion, legacyVersion), List.of(activeVersion), AP_LIST.size());
    }

    @Test
    void thenChangeScheduleWithMultipleSchedulesWithAggregateMessageAndNoFF(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
      ApVersion activeVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.95")
      ApVersion legacyVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100")
      ApVersion targetVersion) {
      changeScheduleWithMultiSchedulesWithAggregateMsg(tenant, activeVersion, legacyVersion, targetVersion, AP_LIST);
      validateAggregateNotificationMessage(List.of(targetVersion, legacyVersion), List.of(activeVersion), AP_LIST.size());
    }

    @Test
    void thenChangeScheduleWithAggregateMessageAndMultipleUpgradeScheduleEnabled(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55555") ApVersion version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55556") ApVersion targetVersion) {
      changeScheduleWithAggregateMessage(tenant, version, targetVersion, List.of("560"));
      validateAggregateNotificationMessage(List.of(targetVersion), List.of(version), 1);
    }

    @Test
    void thenChangeScheduleWithAggregateMessageAndNoFF(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55555") ApVersion version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55556") ApVersion targetVersion) {
      changeScheduleWithAggregateMessage(tenant, version, targetVersion, List.of("560"));
      validateAggregateNotificationMessage(List.of(targetVersion), List.of(version), 1);
    }

    @Nested
    class whenScheduleTargetVersonHasAbfCustomModelRules {

      @Test
      void thenChangeScheduleFor700103TenantWithMultiSchedule(Tenant tenant,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.103.99")
              ApVersion activeVersion,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.95")
              ApVersion legacyVersion,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.103.100")
              ApVersion targetVersion) {
        changeScheduleWithMultiSchedulesWithAggregateMsg(tenant, activeVersion, legacyVersion, targetVersion, AP_LIST);
        validateAggregateNotificationMessage(List.of(targetVersion, legacyVersion), List.of(activeVersion), AP_LIST.size());
      }

      @Test
      void thenChangeScheduleFor700104TenantMultiSchedule(Tenant tenant,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.104.99")
              ApVersion activeVersion,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.95")
              ApVersion legacyVersion,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.104.100")
              ApVersion targetVersion) {
        changeScheduleWithMultiSchedulesWithAggregateMsg(tenant, activeVersion, legacyVersion, targetVersion, AP_LIST);
        validateAggregateNotificationMessage(List.of(targetVersion, legacyVersion), List.of(activeVersion), AP_LIST.size());
      }

      @Test
      void thenChangeScheduleFor700105TenantMultiSchedule(Tenant tenant,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.99")
              ApVersion activeVersion,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.95")
              ApVersion legacyVersion,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.100")
              ApVersion targetVersion) {
        List<String> apList = new ArrayList<>(AP_LIST);
        apList.add("R350:R350E");
        changeScheduleWithMultiSchedulesWithAggregateMsg(tenant, activeVersion, legacyVersion, targetVersion, AP_LIST);
        validateAggregateNotificationMessage(List.of(targetVersion, legacyVersion), List.of(activeVersion), AP_LIST.size());
      }
    }
  }

  void changeScheduleWithMultiSchedulesWithAggregateMsg(
      Tenant tenant,
      ApVersion activeVersion,
      ApVersion legacyVersion,
      ApVersion targetVersion,
      List<String> apList) {
    List<Venue> venueList =
      List.of(
        VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("Asia/Taipei")),
        VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("Asia/Taipei")));

    tenant.setLatestReleaseVersion(activeVersion);
    repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId());
    ScheduleTimeSlot sts = newScheduleTimeSlot();
    repositoryUtil.createOrUpdate(sts, tenant.getId(), randomTxId());
    venueList.forEach(
      venue -> {
        createVenueAndAddAps(venue, activeVersion, apList);
        createOldSchedule(sts, venue, activeVersion, List.of(legacyVersion));
      });

    Set<String> usIds = upgradeScheduleRepository.findAll().stream().map(UpgradeSchedule::getId)
      .collect(Collectors.toSet());

    ChangeScheduleRequestView csrv = newChangeScheduleRequestView(venueList, targetVersion.getId());

    // When
    messageUtil.sendWifiCfgRequest(
      tenant.getId(),
      randomTxId(),
      CfgExtendedAction.CHANGE_UPGRADE_SCHEDULE,
      randomName(),
      new RequestParams(),
      csrv);

    // Then
    assertThat(Objects.requireNonNull(messageUtil.receive(kairosJobRegister)).headers())
      .filteredOn(h -> h.key().equals("jobName")).map(Header::value)
      .satisfies(jobName -> {
        assertThat(usIds).noneMatch(id -> id.equals(new String(jobName.get(0))));
      });
    assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();

    Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
    assertThat(updatedTenant.getVenues()).isNotNull().hasSize(2);
    updatedTenant
      .getVenues()
      .forEach(
        venue -> assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(1)
          .singleElement().matches(us -> us.getVersion().getId().equals(targetVersion.getId())));
  }

  public void changeScheduleWithAggregateMessage(Tenant tenant, ApVersion version, ApVersion targetVersion,
    List<String> apList) {
    List<Venue> venueList = List.of(
      VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("Asia/Taipei")),
      VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("Asia/Taipei")));

    ScheduleTimeSlot sts = newScheduleTimeSlot();
    repositoryUtil.createOrUpdate(sts, tenant.getId(), randomTxId());
    // Create test venues and upgrade schedules
    venueList.forEach(venue -> {
      createVenueAndAddAps(venue, version, apList);
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(0);
    });

    ChangeScheduleRequestView csrv = newChangeScheduleRequestView(venueList, targetVersion.getId());

    // When
    messageUtil.sendWifiCfgRequest(
      tenant.getId(),
      randomTxId(),
      CfgExtendedAction.CHANGE_UPGRADE_SCHEDULE,
      randomName(),
      new RequestParams(),
      csrv);

    // Then
    Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
    assertThat(updatedTenant.getVenues()).isNotNull().hasSize(venueList.size());
    updatedTenant.getVenues().forEach(venue -> {
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(1)
          .singleElement().matches(us -> us.getVersion().getId().equals(targetVersion.getId()));
    });
  }

  void validateAggregateNotificationMessage(
      List<ApVersion> targetVersions,
      List<ApVersion> currentVersions,
      int apCount) {
    assertThat(Objects.requireNonNull(messageUtil.receive(aggregateNotificationRecords)).value())
        .isNotEmpty()
        .satisfies(bytes -> assertThat(NotificationRecord.parseFrom(bytes))
            .isNotNull()
            .extracting(NotificationRecord::getVenueList).asList()
            .extracting(com.ruckus.acx.aggregate.notification.protobuf.message.Venue.class::cast)
            .satisfies(venues -> {
              assertSoftly(softly -> softly.assertThat(venues)
                  .hasSize(venues.size())
                  .allMatch(venue -> venue.getDeviceCount() == apCount)
                  .allMatch(venue -> targetVersions.stream().allMatch(version ->
                      venue.getNewVersion().contains(version.getId())))
                  .allMatch(venue -> currentVersions.stream().allMatch(version ->
                      venue.getLegacyVersion().contains(version.getId()))));
            }));
  }

  private void createVenueAndAddAps(Venue venue, ApVersion currentVersion, List<String> apList) {
    Tenant tenant = venue.getTenant();
    venue.setWifiFirmwareVersion(currentVersion);
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

    ApGroup apGroup = ApGroupTestFixture.randomApGroup(venue, (group) -> group.setIsDefault(true));
    repositoryUtil.createOrUpdate(apGroup, tenant.getId(), randomTxId());
    venue.setApGroups(List.of(apGroup));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

    apList.forEach(ap1 -> {
      Ap ap = new Ap(randomSerialNumber());
      ap.setTenant(tenant);
      ap.setApGroup(apGroup);
      ap.setModel(ap1);
      repositoryUtil.createOrUpdate(ap, tenant.getId(), randomTxId());

      ApFirmwareUpgradeRequest afur = new ApFirmwareUpgradeRequest(randomId());
      afur.setModel(ap1);
      afur.setSerialNumber(ap.getId());
      afur.setTenant(tenant);
      repositoryUtil.createOrUpdate(afur, tenant.getId(), randomTxId());
    });

    Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
    Assertions.assertNotNull(updatedVenue);
  }

  private void createOldSchedule(ScheduleTimeSlot sts, Venue venue, ApVersion activeVersion,
    List<ApVersion> legacyVersions) {
    Tenant tenant = venue.getTenant();

    UpgradeSchedule schedule = newUpgradeSchedule(sts, activeVersion, tenant);
    schedule.setVenue(venue);
    repositoryUtil.createOrUpdate(schedule, tenant.getId(), randomTxId());
    UpgradeScheduleFirmwareVersion scheduleFirmwareVersion1 =
      newUpgradeScheduleFirmwareVersion(schedule, activeVersion, tenant);
    repositoryUtil.createOrUpdate(scheduleFirmwareVersion1, tenant.getId(), randomTxId());

    legacyVersions.forEach(version -> {
      UpgradeScheduleFirmwareVersion scheduleFirmwareVersion2 =
        newUpgradeScheduleFirmwareVersion(schedule, version, tenant);
      repositoryUtil.createOrUpdate(scheduleFirmwareVersion2, tenant.getId(), randomTxId());
    });

    assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(1)
      .singleElement().matches(us -> us.getVersion().getId().equals(activeVersion.getId()));
  }

  private ScheduleTimeSlot newScheduleTimeSlot() {
    ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
    Instant startTime = Instant.now();
    sts.setStartDateTime(Date.from(startTime));
    sts.setEndDateTime(Date.from(startTime.plusSeconds(60 * 60 * 2)));
    sts.setTotalCapacityVenue(2500);
    return sts;
  }

  private UpgradeSchedule newUpgradeSchedule(ScheduleTimeSlot timeSlot, ApVersion version, Tenant tenant) {
    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(timeSlot);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(version);
    us.setTenant(tenant);
    return us;
  }

  private UpgradeScheduleFirmwareVersion newUpgradeScheduleFirmwareVersion(UpgradeSchedule us, ApVersion version, Tenant tenant) {
    UpgradeScheduleFirmwareVersion usfv = new UpgradeScheduleFirmwareVersion(randomId());
    usfv.setApFirmwareVersion(version);
    usfv.setUpgradeSchedule(us);
    us.setTenant(tenant);
    return usfv;
  }

  private ChangeScheduleRequestView newChangeScheduleRequestView(List<Venue> venueList, String version){
    List<ChangeScheduleVenueRequestView> csvrs = venueList.stream().map((venue) -> {
      ChangeScheduleVenueRequestView csvr = new ChangeScheduleVenueRequestView();
      csvr.setId(venue.getId());
      csvr.setType(ApVersionCategory.RECOMMENDED.toString());
      csvr.setVersion(version);
      return csvr;
    }).collect(Collectors.toList());

    ChangeScheduleRequestView csqv = new ChangeScheduleRequestView();
    csqv.setDate("2023-03-10");
    csqv.setTime("00:00-02:00");
    csqv.setVenues(csvrs);
    return csqv;
  }
}
