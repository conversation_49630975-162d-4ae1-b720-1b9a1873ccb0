package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.mapper.ClientIsolationLanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.mapper.DhcpOption82LanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.mapper.SoftGreProfileLanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.ApModelSpecificRepository;
import com.ruckus.cloud.wifi.repository.EthernetPortProfileRepository;
import com.ruckus.cloud.wifi.repository.LanPortAdoptionRepository;
import com.ruckus.cloud.wifi.repository.VenueApModelSpecificAttributesRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.service.impl.ClientIsolationLanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.DhcpOption82LanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.EntityIdGenerationLockServiceImpl;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.service.impl.SoftGreProfileLanPortActivationHandler;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@Tag("LanPortAdoptionTest")
@WifiJpaDataTest
class LanPortAdoptionServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtx = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private VenueApModelSpecificAttributesRepository venueApModelSpecificAttributesRepository;

  @Autowired private LanPortAdoptionRepository lanPortAdoptionRepository;
  @Autowired private VenueLanPortRepository venueLanPortRepository;
  @Autowired private EthernetPortProfileRepository ethernetPortProfileRepository;
  @Autowired private ApLanPortRepository apLanPortRepository;
  @Autowired private ApModelSpecificRepository apModelSpecificRepository;
  @Autowired private LanPortAdoptionDataHelper dataHelper;

  @Autowired private LanPortAdoptionServiceImpl lanPortAdoptionService;

  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  @Test
  void migrateApLanPortProfilesByVenue(Venue venue, Ap ap) {
    var profile1 = dataHelper.createEthernetPortProfile(venue.getTenant(), 100);
    var profile2 = dataHelper.createEthernetPortProfile(venue.getTenant(), 101);
    var venuePortData1 = dataHelper.createVenueLanPortData(venue, "A-T-001", "1", profile1);
    var venuePortData2 = dataHelper.createVenueLanPortData(venue, "A-T-002", "1", profile2);
    var apPortData1 = dataHelper.createApLanPortData(venue, ap, "1", profile1);
    var apPortData2 = dataHelper.createApLanPortData(venue, ap, "2", profile2);

    var profiles = ethernetPortProfileRepository.findByTenantId(txCtx.getTenantId());
    var venuePorts = venueLanPortRepository.findByTenantId(txCtx.getTenantId());
    var apPorts = apLanPortRepository.findByTenantId(txCtx.getTenantId());
    // ensure the prepared data
    assertEquals(2, profiles.size());
    assertEquals(2, venuePorts.size());
    assertEquals(2, apPorts.size());

    lanPortAdoptionService.migrateApLanPortProfiles(venue);

    // ports should be migrated
    venuePorts.forEach(
        venuePort -> {
          assertNotNull(venuePort.getLanPortAdoption());
          assertEquals(
              venuePort.getApLanPortProfile().getId(),
              venuePort.getLanPortAdoption().getApLanPortProfile().getId());
          assertEquals(
              venuePort.getLanPortAdoption().getId(),
              lanPortAdoptionRepository
                  .findByTenantIdAndChecksum(
                      txCtx.getTenantId(),
                      lanPortAdoptionService.calculateChecksum(venuePort.getLanPortAdoption()))
                  .get(0)
                  .getId());
        });
    apPorts.forEach(
        apPort -> {
          assertNotNull(apPort.getLanPortAdoption());
          assertEquals(
              apPort.getApLanPortProfile().getId(),
              apPort.getLanPortAdoption().getApLanPortProfile().getId());
          assertEquals(
              apPort.getLanPortAdoption().getId(),
              lanPortAdoptionRepository
                  .findByTenantIdAndChecksum(
                      txCtx.getTenantId(),
                      lanPortAdoptionService.calculateChecksum(apPort.getLanPortAdoption()))
                  .get(0)
                  .getId());
        });
    // verify adoptions
    var adoptions = lanPortAdoptionRepository.findByTenantId(txCtx.getTenantId());
    assertEquals(2, adoptions.size());
    assertNotEquals(
        Set.of(100, 101),
        adoptions.stream()
            .map(LanPortAdoption::getEthernetPortProfileId)
            .collect(Collectors.toSet()));
    adoptions.forEach(
        adoption -> {
          assertTrue(CollectionUtils.isNotEmpty(adoption.getApLanPorts()));
          assertTrue(CollectionUtils.isNotEmpty(adoption.getVenueLanPorts()));
        });
    var venueAdoption1 =
        adoptions.stream()
            .filter(
                adoption ->
                    adoption
                        .getVenueLanPorts()
                        .get(0)
                        .getId()
                        .equals(venuePortData1.port().getId()))
            .findAny()
            .get();
    var venueAdoption2 =
        adoptions.stream()
            .filter(
                adoption ->
                    adoption
                        .getVenueLanPorts()
                        .get(0)
                        .getId()
                        .equals(venuePortData2.port().getId()))
            .findAny()
            .get();
    assertEquals(venuePortData1.port().getId(), venueAdoption1.getVenueLanPorts().get(0).getId());
    validate(venuePortData1.profile(), venueAdoption1);
    assertEquals(venuePortData2.port().getId(), venueAdoption2.getVenueLanPorts().get(0).getId());
    validate(venuePortData2.profile(), venueAdoption2);
    assertNotEquals(venueAdoption1.getChecksum(), venueAdoption2.getChecksum());
    var apAdoption1 =
        adoptions.stream()
            .filter(
                adoption ->
                    adoption.getApLanPorts().get(0).getId().equals(apPortData1.port().getId()))
            .findAny()
            .get();
    var apAdoption2 =
        adoptions.stream()
            .filter(
                adoption ->
                    adoption.getApLanPorts().get(0).getId().equals(apPortData2.port().getId()))
            .findAny()
            .get();
    assertEquals(apPortData1.port().getId(), apAdoption1.getApLanPorts().get(0).getId());
    validate(apPortData1.profile(), apAdoption1);
    assertEquals(apPortData2.port().getId(), apAdoption2.getApLanPorts().get(0).getId());
    validate(apPortData2.profile(), apAdoption2);
    assertNotEquals(apAdoption1.getChecksum(), apAdoption2.getChecksum());
    // verify model attributes
    var venueAdoptionIds =
        venueApModelSpecificAttributesRepository
            .findByModelInAndVenueIdAndTenantId(
                List.of(
                    venuePortData1.modelSpecific().getModel(),
                    venuePortData2.modelSpecific().getModel()),
                venue.getId(),
                txCtx.getTenantId())
            .stream()
            .flatMap(attributes -> attributes.getLanPorts().stream())
            .map(VenueLanPort::getLanPortAdoption)
            .map(LanPortAdoption::getId)
            .collect(Collectors.toSet());
    assertEquals(2, venueAdoptionIds.size());
    assertEquals(Set.of(venueAdoption1.getId(), venueAdoption2.getId()), venueAdoptionIds);
    var apAdoptionIds =
        apModelSpecificRepository
            .findByApIdAndTenantId(ap.getId(), txCtx.getTenantId())
            .getLanPorts()
            .stream()
            .map(ApLanPort::getLanPortAdoption)
            .map(LanPortAdoption::getId)
            .collect(Collectors.toSet());
    assertEquals(2, apAdoptionIds.size());
    assertEquals(Set.of(apAdoption1.getId(), apAdoption2.getId()), apAdoptionIds);
  }

  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  @Test
  void updateLanPortAdoptionByVenueLanPort(Venue venue) {
    var portData1 = dataHelper.createVenueLanPortData(venue, "A-T-001", "1", 777);
    var portData2 = dataHelper.createVenueLanPortData(venue, "A-T-002", "1", 888);
    var activation = createSoftGreActivation(dataHelper.createSoftGreProfile(venue.getTenant()));

    var profiles = ethernetPortProfileRepository.findByTenantId(txCtx.getTenantId());
    var ports = venueLanPortRepository.findByTenantId(txCtx.getTenantId());
    // ensure the prepared data
    assertEquals(2, profiles.size());
    assertEquals(2, ports.size());

    var targetPort =
        ports.stream()
            .filter(port -> portData1.port().getId().equals(port.getId()))
            .findFirst()
            .get();
    var targetProfile =
        profiles.stream()
            .filter(profile -> portData1.profile().getId().equals(profile.getId()))
            .findFirst()
            .get();
    var updated =
        lanPortAdoptionService.updateLanPortAdoption(targetPort, targetProfile, activation);

    // portData1 should be migrated
    assertTrue(updated);
    assertNotNull(targetPort.getLanPortAdoption());
    assertNotNull(targetPort.getLanPortAdoption().getId());
    assertEquals(
        portData1.profile().getId(), targetPort.getLanPortAdoption().getApLanPortProfile().getId());
    assertEquals(
        targetPort.getLanPortAdoption().getId(),
        lanPortAdoptionRepository
            .findByTenantIdAndChecksum(
                txCtx.getTenantId(),
                lanPortAdoptionService.calculateChecksum(targetPort.getLanPortAdoption()))
            .get(0)
            .getId());
    // verify adoptions
    var adoptions = lanPortAdoptionRepository.findByTenantId(txCtx.getTenantId());
    assertEquals(2, adoptions.size());
    var adoption1 =
        adoptions.stream()
            .filter(
                adoption ->
                    adoption.getVenueLanPorts().get(0).getId().equals(portData1.port().getId()))
            .findAny()
            .get();
    assertTrue(
        adoption1.getVenueLanPorts().stream()
            .map(VenueLanPort::getId)
            .collect(Collectors.toSet())
            .contains(portData1.port().getId()));
    assertEquals(targetPort.getLanPortAdoption(), adoption1);
    assertEquals(
        activation.getSoftGreProfile(), adoption1.getSoftGreActivation().getSoftGreProfile());
    assertNotNull(adoption1.getSoftGreActivation().getId());
  }

  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  @Test
  void updateLanPortAdoptionByApLanPort(Venue venue, Ap ap) {
    var portData1 = dataHelper.createApLanPortData(venue, ap, "1", 777);
    var portData2 = dataHelper.createApLanPortData(venue, ap, "2", 888);
    var activation = createSoftGreActivation(dataHelper.createSoftGreProfile(venue.getTenant()));

    var profiles = ethernetPortProfileRepository.findByTenantId(txCtx.getTenantId());
    var ports = apLanPortRepository.findByTenantId(txCtx.getTenantId());
    // ensure the prepared data
    assertEquals(2, profiles.size());
    assertEquals(2, ports.size());

    var targetPort =
        ports.stream()
            .filter(port -> portData1.port().getId().equals(port.getId()))
            .findFirst()
            .get();
    var targetProfile =
        profiles.stream()
            .filter(profile -> portData1.profile().getId().equals(profile.getId()))
            .findFirst()
            .get();
    var updated =
        lanPortAdoptionService.updateLanPortAdoption(targetPort, targetProfile, activation);

    // portData1 should be migrated
    assertTrue(updated);
    assertNotNull(targetPort.getLanPortAdoption());
    assertNotNull(targetPort.getLanPortAdoption().getId());
    assertEquals(
        portData1.profile().getId(), targetPort.getLanPortAdoption().getApLanPortProfile().getId());
    assertEquals(
        targetPort.getLanPortAdoption().getId(),
        lanPortAdoptionRepository
            .findByTenantIdAndChecksum(
                txCtx.getTenantId(),
                lanPortAdoptionService.calculateChecksum(targetPort.getLanPortAdoption()))
            .get(0)
            .getId());
    // verify adoptions
    var adoptions = lanPortAdoptionRepository.findByTenantId(txCtx.getTenantId());
    assertEquals(2, adoptions.size());
    var adoption1 =
        adoptions.stream()
            .filter(
                adoption ->
                    adoption.getApLanPorts().get(0).getId().equals(portData1.port().getId()))
            .findAny()
            .get();
    assertTrue(
        adoption1.getApLanPorts().stream()
            .map(ApLanPort::getId)
            .collect(Collectors.toSet())
            .contains(portData1.port().getId()));
    assertEquals(targetPort.getLanPortAdoption(), adoption1);
    assertEquals(
        activation.getSoftGreProfile(), adoption1.getSoftGreActivation().getSoftGreProfile());
    assertNotNull(adoption1.getSoftGreActivation().getId());
  }

  private void validate(EthernetPortProfile source, LanPortAdoption target) {
    assertEquals(source.getId(), target.getApLanPortProfile().getId());
    assertNotEquals(source.getApLanPortId(), target.getEthernetPortProfileId());
    assertNotNull(target.getChecksum());
  }

  private SoftGreProfileLanPortActivation createSoftGreActivation(SoftGreProfile profile) {
    var activation = new SoftGreProfileLanPortActivation();
    activation.setSoftGreProfile(profile);
    return activation;
  }

  @TestConfiguration
  @Import({
    EntityIdGenerationLockServiceImpl.class,
    SoftGreProfileLanPortActivationMapperImpl.class,
    ClientIsolationLanPortActivationMapperImpl.class,
    DhcpOption82LanPortActivationMapperImpl.class,
    SoftGreProfileLanPortActivationHandler.class,
    ClientIsolationLanPortActivationHandler.class,
    DhcpOption82LanPortActivationHandler.class,
    LanPortAdoptionServiceImpl.class
  })
  static class TestConfig {

    @Bean
    @ConditionalOnMissingBean
    LanPortAdoptionDataHelper dataHelper(
        RepositoryUtil repositoryUtil, LanPortAdoptionServiceImpl lanPortAdoptionService) {
      return new LanPortAdoptionDataHelper(repositoryUtil, lanPortAdoptionService);
    }
  }
}
