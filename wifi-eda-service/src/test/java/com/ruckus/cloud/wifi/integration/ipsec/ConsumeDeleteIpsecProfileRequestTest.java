package com.ruckus.cloud.wifi.integration.ipsec;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.IpsecProfileTestFixture.randomIpsecProfile;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("IpsecProfileTest")
@WifiIntegrationTest
public class ConsumeDeleteIpsecProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeDeleteIpsecProfileMessage {

    @Test
    void givenValidProfile(Tenant tenant) {
      var profile =
          repositoryUtil.createOrUpdate(
              randomIpsecProfile(tenant, p -> {}), tenant.getId());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.DELETE_IPSEC_PROFILE,
          randomName(),
          new RequestParams().addPathVariable("ipsecProfileId", profile.getId()),
          "");

      assertThat(repositoryUtil.find(IpsecProfile.class, profile.getId(), tenant.getId()))
          .isNull();

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.DEL)
          .matches(o -> o.getId().equals(profile.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_IPSEC_PROFILE));

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getAction() == Action.DELETE);
    }

    @Test
    void givenProfileActivated(Tenant tenant, NetworkVenue networkVenue) {
      var softGreProfile =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {
              }), tenant.getId());
      var ipsecProfile =
          repositoryUtil.createOrUpdate(
              randomIpsecProfile(tenant, p -> {
              }), tenant.getId());
      var activation = new SoftGreProfileNetworkVenueActivation();
      activation.setTenant(tenant);
      activation.setNetworkVenue(networkVenue);
      activation.setSoftGreProfile(softGreProfile);
      activation.setIpsecProfile(ipsecProfile);
      repositoryUtil.createOrUpdate(activation, tenant.getId());

      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.DELETE_IPSEC_PROFILE,
                  randomName(),
                  new RequestParams().addPathVariable("ipsecProfileId", ipsecProfile.getId()),
                  ""))
          .isNotNull()
          .rootCause()
          .isNotNull()
          .isInstanceOf(ObjectInUseException.class);

      messageCaptors
          .assertThat(
              messageCaptors.getDdccmMessageCaptor(),
              messageCaptors.getCmnCfgCollectorMessageCaptor())
          .doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_IPSEC_PROFILE));
    }

    @Test
    void givenProfileNotExists(Tenant tenant) {
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.DELETE_IPSEC_PROFILE,
                  randomName(),
                  new RequestParams().addPathVariable("ipsecProfileId", randomId()),
                  ""))
          .isNotNull()
          .rootCause()
          .isNotNull()
          .isInstanceOf(ObjectNotFoundException.class);

      messageCaptors
          .assertThat(
              messageCaptors.getDdccmMessageCaptor(),
              messageCaptors.getCmnCfgCollectorMessageCaptor())
          .doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_IPSEC_PROFILE));
    }
  }
}
