package com.ruckus.cloud.wifi.integration.network;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.vlanPool;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.PskNetworkGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.VlanPool;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_BSS_PRIORITY_TOGGLE)
class ConsumePskNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Nested
  @ApiAction(CfgAction.ADD_NETWORK)
  class ConsumeAddPskNetworkRequestTest {

    @Payload
    private final PskNetworkGenerator generator = Generators.pskNetwork();

    @Test
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_NETWORK)
  class ConsumeUpdatePskNetworkRequestTest {

    @Payload
    private final PskNetworkGenerator generator = Generators.pskNetwork()
        .setId(nullValue(String.class))
        .setName(serialName("UpdatedPskNetwork"))
        .setDescription(randomString(64));

    @Payload("WithVlanPool")
    private com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork payloadWithVlanPool() {
      final var pskNetwork = generator.generate();
      Optional.ofNullable(pskNetwork.getWlan().getAdvancedCustomization().getVlanPool())
          .orElse(new VlanPool()).setId(vlanPoolId);
      return pskNetwork;
    }

    private String networkId;
    private String vlanPoolId;

    @BeforeEach
    void givenOnePskNetworkPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant, final Venue venue) {
      final var pskNetwork = network(PskNetwork.class).generate();
      pskNetwork.getWlan().setNetwork(pskNetwork);
      repositoryUtil.createOrUpdate(pskNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
          .setNetwork(always(pskNetwork)).setVenue(always(venue)).generate();
      pskNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      networkId = pskNetwork.getId();
    }

    @BeforeEach
    void givenOneVlanPoolPersistedInDb(final Tenant tenant) {
      final var vlanPool = vlanPool().generate();
      repositoryUtil.createOrUpdate(vlanPool, tenant.getId(), randomTxId());
      vlanPoolId = vlanPool.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    @FeatureRole("AP-70")
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork payload) {
      validateResult(txCtx, apiAction, networkId, payload);
    }

    @Tag("VlanPoolTest")
    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("WithVlanPool"))
    @FeatureRole("AP-70")
    void thenShouldHandleTheRequestWithVlanPoolSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("WithVlanPool") com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork payload) {
      validateResult(txCtx, apiAction, networkId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_NETWORK)
  class ConsumeDeletePskNetworkRequestTest {

    private String networkId;

    @BeforeEach
    void givenOnePskNetworkPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant, final Venue venue) {
      final var pskNetwork = network(PskNetwork.class).generate();
      pskNetwork.getWlan().setNetwork(pskNetwork);
      repositoryUtil.createOrUpdate(pskNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
          .setNetwork(always(pskNetwork)).setVenue(always(venue)).generate();
      pskNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      networkId = pskNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction) {
      validateResult(txCtx, apiAction, networkId, null);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_NETWORKS)
  class ConsumeDeletePskNetworksRequestTest {

    @Payload
    private List<String> networkIdList;

    @BeforeEach
    void givenTenPskNetworksPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant, final Venue venue) {
      final var pskNetworkList = network(PskNetwork.class).generate(10);
      pskNetworkList.forEach(pskNetwork -> {
        pskNetwork.getWlan().setNetwork(pskNetwork);
        repositoryUtil.createOrUpdate(pskNetwork, tenant.getId(), randomTxId());

        final var networkVenue = networkVenue()
            .setNetwork(always(pskNetwork)).setVenue(always(venue)).generate();
        pskNetwork.setNetworkVenues(List.of(networkVenue));
        repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      });

      networkIdList = pskNetworkList.stream().map(Network::getId).collect(Collectors.toList());
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload List<String> payload) {
      validateResult(txCtx, apiAction, payload);
    }

    private void validateResult(TxCtx txCtx, CfgAction apiAction, List<String> networkIdList) {
      networkIdList.forEach(networkId ->
          validateRepositoryData(txCtx, apiAction, networkId, null));
      validateDdccmCfgRequestMessages(txCtx, apiAction, networkIdList, null);
      validateCmnCfgCollectorMessages(txCtx, apiAction, networkIdList);
      validateActivityMessages(txCtx, apiAction);
    }
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String networkId,
      com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork payload) {
    validateRepositoryData(txCtx, apiAction, networkId, payload);
    validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
    validateCmnCfgCollectorMessages(txCtx, apiAction, List.of(networkId));
    validateActivityMessages(txCtx, apiAction);
  }

  private void validateRepositoryData(TxCtx txCtx, CfgAction apiAction, String networkId,
      com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork payload) {
    if (networkId == null) {
      final String requestId = txCtx.getTxId();
      final String tenantId = txCtx.getTenant();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty, "should be empty");
      return;
    }

    final PskNetwork network = repositoryUtil.find(PskNetwork.class, networkId);

    if (apiAction == null || apiAction == CfgAction.DELETE_NETWORK
        || apiAction == CfgAction.DELETE_NETWORKS || payload == null) {
      assertThat(network).isNull();
      return;
    }

    assertThat(network)
        .isNotNull()
        .matches(n -> Objects.equals(n.getId(), networkId))
        .matches(n -> Objects.equals(n.getName(), payload.getName()))
        .matches(n -> Objects.equals(n.getDescription(), payload.getDescription()))
        .matches(n -> Objects.equals(n.getWlan().getAdvancedCustomization().getBssPriority(),
            payload.getWlan().getAdvancedCustomization().getBssPriority()));
  }

  private void validateDdccmCfgRequestMessages(TxCtx txCtx, CfgAction apiAction,
      List<String> networkIdList, com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork payload) {
    if (apiAction == null || apiAction == CfgAction.ADD_NETWORK || networkIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final String requestId = txCtx.getTxId();
    final String tenantId = txCtx.getTenant();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
            .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(KafkaProtoMessage::getHeaders).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .describedAs("Should contains %s WlanVenue operations", networkIdList.size())
            .hasSize(networkIdList.size())
            .allMatch(op -> StringUtils.isNotEmpty(op.getId()),
                "All the ids of the WlanVenue operations should not be empty")
            .allMatch(op -> op.getAction() == action(apiAction),
                String.format("All the actions of the WlanVenue operations should be [%s]", apiAction))
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                    String.format("The value of `requestId` field in CommonInfo should be [%s]", requestId))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                    String.format("The value of `tenantId` field in CommonInfo should be [%s]", tenantId))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                    String.format("The value of `sender` field in CommonInfo should be [%s]", ServiceType.WIFI_SERVICE)))
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                    .matches(n -> networkIdList.contains(n.getWlanId()));
              } else {
                assertThat(payload).isNotNull();
                assertSoftly(softly -> {
                  softly.assertThat(op.getWlanVenue().getWlanId())
                      .matches(networkIdList::contains);
                  softly.assertThat(op.getWlanVenue().getSsid())
                      .isEqualTo(payload.getWlan().getSsid());
                  softly.assertThat(op.getWlanVenue().getPassphrase())
                      .isEqualTo(payload.getWlan().getPassphrase());
                  softly.assertThat(op.getWlanVenue().getPriority())
                      .isEqualTo(StringValue.of(
                          "PRIORITY_" + payload.getWlan().getAdvancedCustomization()
                              .getBssPriority().toString()));
                  Optional.ofNullable(payload.getWlan().getSaePassphrase())
                      .ifPresentOrElse(
                          // payload has wlan.saePassphrase
                          saePassphrase -> softly.assertThat(op.getWlanVenue())
                              .matches(WlanVenue::hasSaePassphrase)
                              .extracting(WlanVenue::getSaePassphrase)
                              .extracting(StringValue::getValue)
                              .isEqualTo(saePassphrase),
                          // payload doesn't have wlan.saePassphrase
                          () -> softly.assertThat(op.getWlanVenue().hasSaePassphrase()).isFalse());
                  Optional.ofNullable(payload.getWlan().getAdvancedCustomization())
                      .map(com.ruckus.cloud.wifi.eda.viewmodel.PskWlanAdvancedCustomization::getVlanPool)
                      .map(com.ruckus.cloud.wifi.eda.viewmodel.VlanPool::getId).ifPresentOrElse(
                          // payload has wlan.advancedCustomization.vlanPool.id
                          vlanPoolId -> softly.assertThat(op.getWlanVenue())
                              .matches(WlanVenue::hasVlanPoolId)
                              .extracting(WlanVenue::getVlanPoolId)
                              .extracting(StringValue::getValue)
                              .isEqualTo(vlanPoolId),
                          // payload doesn't have wlan.advancedCustomization.vlanPool.id
                          () -> softly.assertThat(op.getWlanVenue())
                              .matches(WlanVenue::hasVlanId)
                              .extracting(WlanVenue::getVlanId)
                              .extracting(Int32Value::getValue)
                              .isEqualTo(payload.getWlan().getVlanId().intValue()));
                });
              }
            }));
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK -> Action.ADD;
      case UPDATE_NETWORK -> Action.MODIFY;
      case DELETE_NETWORK, DELETE_NETWORKS -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void validateCmnCfgCollectorMessages(TxCtx txCtx, CfgAction apiAction, List<String> networkIdList) {
    if (apiAction == null || networkIdList == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final String requestId = txCtx.getTxId();
    final String tenantId = txCtx.getTenant();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
            .hasSize(networkIdList.size())
            .allMatch(op -> networkIdList.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction))
            .allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> networkIdList.contains(doc.get(Key.ID).getStringValue()))
                    .matches(doc -> Value.TYPE_NETWORK.equals(doc.get(Key.TYPE)))
                    .matches(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()));
              }
            }));
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK -> OpType.ADD;
      case UPDATE_NETWORK -> OpType.MOD;
      case DELETE_NETWORK, DELETE_NETWORKS -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void validateActivityMessages(TxCtx txCtx, CfgAction apiAction) {
    if (apiAction == null) {
 messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtx.getTenant());
      return;
    }

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(txCtx.getTenant(), txCtx.getTxId());
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
            .getValue(txCtx.getTenant(), txCtx.getTxId());
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 0)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
            .isEmpty());
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK -> ApiFlowNames.ADD_NETWORK;
      case UPDATE_NETWORK -> ApiFlowNames.UPDATE_NETWORK;
      case DELETE_NETWORK -> ApiFlowNames.DELETE_NETWORK;
      case DELETE_NETWORKS -> ApiFlowNames.DELETE_NETWORKS;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
