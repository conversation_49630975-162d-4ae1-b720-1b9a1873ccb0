package com.ruckus.cloud.wifi.integration.network;

import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.OpenNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.repository.AuthRadiusProfileRepository;
import com.ruckus.cloud.wifi.repository.AuthRadiusServiceRepository;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.openWlan;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.openWlanAdvancedCustomization;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertWlanSoftly;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.ConsumerRecordAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
public class ConsumeOpenNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  FeatureFlagService featureFlagService;

  @Autowired
  private AuthRadiusServiceRepository authRadiusServiceRepository;

  @Autowired
  private AuthRadiusProfileRepository authRadiusProfileRepository;

  private final String macRegistrationListId = "mac_registration_list_id";

  private final String macRegistrationListIdAuthId = "%s-Auth-Radius-AAA";
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  class ConsumeAddOpenNetworkRequestTest {

    private String authRadiusId;

    @Payload
    private OpenNetworkGenerator openNetwork() {
      return Generators.openNetwork();
    }

    @Payload("macAuthWithRadius")
    private OpenNetworkGenerator macAuthWithRadius() {
      return Generators.openNetwork()
              .setEnableAuthProxy(always(true))
              .setAuthRadiusId(always(authRadiusId))
              .setWlan(openWlan().setMacAddressAuthentication(always(true)));
    }

    @Payload("macAuthWithMacRegistrationListId")
    private OpenNetworkGenerator macAuthWithMacRegistrationListId() {
      return Generators.openNetwork()
              .setWlan(openWlan().setMacAddressAuthentication(always(true))
                      .setMacRegistrationListId(always(macRegistrationListId)));
    }

    @BeforeEach
    void giveAuthRadiusPersistedInDb(final Tenant tenant) {
      final var radius = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
      authRadiusId = radius.getId();
      System.out.println("saved authRadiusId:" + authRadiusId);
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK)
    void thenShouldHandleOpenNetworkSuccessfully(TxCtxHolder.TxCtx txCtx,
                                                 CfgAction apiAction, @Payload com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("macAuthWithRadius"))
    void thenShouldHandleMacAuthWithRadiusSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                     @Payload("macAuthWithRadius") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("macAuthWithMacRegistrationListId"))
    void thenShouldHandleMacAuthWithMacRegistrationListIdSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                           @Payload("macAuthWithMacRegistrationListId") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
    }
  }

  @Nested
  class ConsumeUpdateOpenNetworkRequestTest {

    private String authRadiusId;

    private String networkId;

    @BeforeEach
    void givenOneOpenNetworkPersistedInDbAndActivatedOnOneVenue(
            final Tenant tenant, final Venue venue) {
      final var authRadius = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());
      authRadiusId = authRadius.getId();

      final var openNetwork = network(OpenNetwork.class).generate();
      openNetwork.getWlan().setNetwork(openNetwork);
      openNetwork.getWlan().setMacAddressAuthentication(true);
      openNetwork.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(false);
      openNetwork.setAuthRadius(authRadius);
      repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
              .setNetwork(always(openNetwork)).setVenue(always(venue)).generate();
      openNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      networkId = openNetwork.getId();
    }

    @Payload
    private OpenNetworkGenerator openNetworkWithMacAuthAndRadius() {
      return Generators.openNetwork()
              .setId(nullValue(String.class))
              .setName(serialName("UpdatedOpenNetwork"))
              .setDescription(randomString(64))
              .setAuthRadiusId(always(authRadiusId))
              .setWlan(openWlan().setMacAddressAuthentication(always(true))
                      .setAdvancedCustomization(openWlanAdvancedCustomization().setEnableAaaVlanOverride(always(true))));
    }

    @Payload("openNetworkWithMacAuthAndProxy")
    private OpenNetworkGenerator openNetworkWithMacAuthAndProxy() {
      return Generators.openNetwork()
              .setId(nullValue(String.class))
              .setName(serialName("UpdatedOpenNetwork"))
              .setDescription(randomString(64))
              .setAuthRadiusId(always(authRadiusId))
              .setEnableAuthProxy(always(true))
              .setWlan(openWlan().setMacAddressAuthentication(always(true))
                      .setAdvancedCustomization(openWlanAdvancedCustomization().setEnableAaaVlanOverride(always(true))));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK)
    void thenShouldHandleDisableAuthProxyWithoutAaaVlanOverrideFFSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                     @Payload com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      validateResult(txCtx, apiAction, networkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK)
    void thenShouldHandleDisableAuthProxySuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                     @Payload com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      validateResult(txCtx, apiAction, networkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("openNetworkWithMacAuthAndProxy"))
    void thenShouldHandleEnableProxySuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                     @Payload("openNetworkWithMacAuthAndProxy") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      validateResult(txCtx, apiAction, networkId, payload);
    }
  }

  @Nested
  class ConsumeUpdateOpenNetworkWithMacRegistrationListRequestTest {

    private String networkId;

    private String tmpMacRegistrationListId = "tmp_mac_registration_list_id";

    @BeforeEach
    void givenOneOpenNetworkPersistedInDbAndActivatedOnOneVenue(
            final Tenant tenant, final Venue venue) {

      final var openNetwork = network(OpenNetwork.class).generate();
      openNetwork.getWlan().setNetwork(openNetwork);
      openNetwork.getWlan().setMacAddressAuthentication(true);
      openNetwork.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(false);
      openNetwork.getWlan().setMacRegistrationListId(tmpMacRegistrationListId);
      repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
              .setNetwork(always(openNetwork)).setVenue(always(venue)).generate();
      openNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      networkId = openNetwork.getId();
    }

    @Payload("openNetworkWithMacAuthAndMacRegistrationList")
    private OpenNetworkGenerator openNetworkWithMacAuthAndMacRegistrationList() {
      return Generators.openNetwork()
              .setId(nullValue(String.class))
              .setName(serialName("UpdatedOpenNetwork"))
              .setDescription(randomString(64))
              .setWlan(openWlan().setMacAddressAuthentication(always(true))
                      .setMacRegistrationListId(always(macRegistrationListId))
                      .setAdvancedCustomization(openWlanAdvancedCustomization().setEnableAaaVlanOverride(always(true))));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("openNetworkWithMacAuthAndMacRegistrationList"))
    void thenShouldHandleMacRegistrationListIdSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                           @Payload("openNetworkWithMacAuthAndMacRegistrationList") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      validateResult(txCtx, apiAction, networkId, payload);
    }
  }


  private void validateResult(TxCtxHolder.TxCtx txCtx, CfgAction apiAction, String networkId,
                              com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
    validateRepositoryData(txCtx, apiAction, networkId, payload);
    validateWifiCfgChangeMessages(txCtx, apiAction, List.of(networkId), payload);
    validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
  }

  private void validateRepositoryData(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                      String networkId, com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
    if (networkId == null) {
      final String requestId = txCtx.getTxId();
      final String tenantId = txCtx.getTenant();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty, "should be empty");
      return;
    }

    final OpenNetwork network = repositoryUtil.find(OpenNetwork.class, networkId);

    if (apiAction == null || apiAction == CfgAction.DELETE_NETWORK
            || apiAction == CfgAction.DELETE_NETWORKS || payload == null) {
      assertThat(network).isNull();
      return;
    }

    assertThat(network)
            .isNotNull()
            .matches(n -> Objects.equals(n.getId(), networkId))
            .matches(n -> Objects.equals(n.getName(), payload.getName()))
            .matches(n -> Objects.equals(n.getDescription(), payload.getDescription()));

    if (BooleanUtils.isFalse(payload.getWlan().getMacAddressAuthentication())) {
      assertThat(network.getAuthRadius()).isNull();
      assertThat(network.getWlan())
              .isNotNull()
              .extracting(Wlan::getMacRegistrationListId)
              .isNull();
      return;
    }

    if (payload.getAuthRadiusId() != null) {
      assertThat(network.getAuthRadius())
              .isNotNull()
              .extracting(BaseEntity::getId)
              .isEqualTo(payload.getAuthRadiusId());

      final List<AuthRadiusService> authRadiusServiceList = authRadiusServiceRepository
              .findByTenantIdAndRadiusId(txCtx.getTenant(), network.getAuthRadius().getId());

      if (!network.getEnableAuthProxy()) {
        assertThat(authRadiusServiceList)
                .isEmpty();
      } else {
        assertThat(authRadiusServiceList)
                .isNotEmpty();
      }
    } else {
      assertThat(network.getWlan())
              .isNotNull()
              .matches(wlan -> wlan.getMacRegistrationListId().equals(macRegistrationListId));
    }

    assertThat(network.getWlan())
            .isNotNull()
            .extracting(Wlan::getAdvancedCustomization)
            .matches(wlanAdvancedCustomization -> wlanAdvancedCustomization.getEnableAaaVlanOverride().equals(payload.getWlan().getAdvancedCustomization().getEnableAaaVlanOverride()));
  }

  private void validateWifiCfgChangeMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                             List<String> networkIdList, com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
    if (apiAction == null || networkIdList == null) {
      messageCaptors.getWifiCfgChangeMessageCaptor().assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final var wifiCfgChangeMessage = messageUtil.receive(kafkaTopicProvider.getWifiCfgChange());

    assertThat(wifiCfgChangeMessage).isNotNull()
            .satisfies(msg -> assertThat(msg.key()).isEqualTo(txCtx.getTenant()))
            .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, txCtx.getTxId()))
            .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction.key()))
            .extracting(ConsumerRecord::value).isNotNull();

    assertThatNoException().isThrownBy(() ->
            assertThat(WifiConfigChange.parseFrom(wifiCfgChangeMessage.value()))
                    .satisfies(msg -> assertSoftly(softly -> {
                      softly.assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
                      softly.assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
                    }))
                    .extracting(WifiConfigChange::getOperationList).asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
                    .satisfies(ops -> {
                      assertThat(ops)
                              .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasOpenNetwork)
                              .filteredOn(op -> op.getAction() == wifiProtoAction(apiAction))
                              .as("The %s OpenNetwork operation count should be 1", wifiProtoAction(apiAction).name())
                              .hasSize(1)
                              .singleElement()
                              .extracting(com.ruckus.cloud.wifi.proto.Operation::getOpenNetwork)
                              .isNotNull();

                      assertThat(ops)
                              .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasWlan)
                              .filteredOn(op -> op.getAction() == wifiProtoAction(apiAction))
                              .as("The %s Wlan operation count should be 1", wifiProtoAction(apiAction).name())
                              .hasSize(1)
                              .singleElement()
                              .extracting(com.ruckus.cloud.wifi.proto.Operation::getWlan)
                              .satisfies(assertWlanSoftly(payload.getWlan()));
                      if (payload.getAuthRadius() != null) {
                        assertThat(ops)
                                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasRadius)
                                .filteredOn(
                                        op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY)
                                .as("The MODIFY Radius operation count should be 1").hasSize(1)
                                .singleElement()
                                .extracting(com.ruckus.cloud.wifi.proto.Operation::getRadius)
                                .satisfies(radiusOp -> {
                                  assertThat(radiusOp.getId())
                                          .extracting(StringValue::getValue)
                                          .isEqualTo(payload.getAuthRadius().getId());
                                });
                        if (BooleanUtils.isTrue(payload.getEnableAuthProxy())) {
                          assertThat(ops)
                                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasAuthRadiusService)
                                  .filteredOn(op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                                  .as("The ADD AuthRadiusService operation count should be 1").hasSize(1)
                                  .singleElement()
                                  .extracting(com.ruckus.cloud.wifi.proto.Operation::getAuthRadiusService)
                                  .satisfies(authRadiusServiceOp -> {
                                    assertThat(authRadiusServiceOp.getRadiusId())
                                            .extracting(StringValue::getValue)
                                            .isEqualTo(payload.getAuthRadius().getId());
                                    assertThat(ops)
                                            .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasAuthRadiusProfile)
                                            .filteredOn(op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                                            .as("The ADD AuthRadiusProfile operation count should be 1")
                                            .hasSize(1)
                                            .singleElement()
                                            .extracting(com.ruckus.cloud.wifi.proto.Operation::getAuthRadiusProfile)
                                            .satisfies(authRadiusProfileOp -> {
                                              assertThat(authRadiusProfileOp.getAuthRadiusServiceId())
                                                      .isEqualTo(authRadiusServiceOp.getId());
                                            });
                                  });
                        }
                      }
                    }));
  }

  private void validateDdccmCfgRequestMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                               List<String> networkIdList, com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
    final boolean hasMacRegistrationListId = StringUtils.isNotEmpty(payload.getWlan().getMacRegistrationListId());
    final boolean isNonProxy = BooleanUtils.isFalse(payload.getEnableAuthProxy());
    final boolean isAaaVlanOverrideAvailable = BooleanUtils.isTrue(payload.getWlan().getMacAddressAuthentication());

    if (apiAction == null
            || (apiAction == CfgAction.ADD_NETWORK && isNonProxy)
            || networkIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final var ddccmCfgRequestMessage = messageUtil.receive(kafkaTopicProvider.getDdccmCfgRequest());

    assertThat(ddccmCfgRequestMessage).isNotNull()
            .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
            .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
            .extracting(ConsumerRecord::value).isNotNull();

    final int radiusAuthenticationServiceSize = isNonProxy ? 0 : 1;
    final int authRadiusProfileSize = isNonProxy ? 0 : 1;
    final int authRadiusVenueSize = isNonProxy && !hasMacRegistrationListId ? 1 : 0;
    AtomicReference<String> radiusAuthenticationServiceId = new AtomicReference<>();
    AtomicReference<String> authRadiusVenueId = new AtomicReference<>();
    AtomicReference<String> authRadiusProfileId = new AtomicReference<>();

    assertThatNoException().isThrownBy(() ->
            assertThat(WifiConfigRequest.parseFrom(ddccmCfgRequestMessage.value()))
                    .extracting(WifiConfigRequest::getOperationsList).asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
                    .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasRadiusAuthenticationService)
                    .describedAs("Should contains %s radiusAuthenticationService operations", radiusAuthenticationServiceSize)
                    .hasSize(radiusAuthenticationServiceSize)
                    .extracting(Operation::getRadiusAuthenticationService)
                    .map(radiusAuthenticationService -> {
                      radiusAuthenticationServiceId.set(radiusAuthenticationService.getId());
                      return radiusAuthenticationServiceId;
                    })
    );

    if (StringUtils.isEmpty(radiusAuthenticationServiceId.get())) {
      radiusAuthenticationServiceId.set(String.format(macRegistrationListIdAuthId, txCtx.getTenant()));
    }

    assertThatNoException().isThrownBy(() ->
            assertThat(WifiConfigRequest.parseFrom(ddccmCfgRequestMessage.value()))
                    .extracting(WifiConfigRequest::getOperationsList).asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
                    .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenueRadius)
                    .describedAs("Should contains %s venueRadius operations", authRadiusVenueSize)
                    .hasSize(authRadiusVenueSize)
                    .extracting(Operation::getVenueRadius)
                    .map(venueRadius -> {
                      authRadiusVenueId.set(venueRadius.getId());
                      return authRadiusVenueId;
                    })
    );

    assertThatNoException().isThrownBy(() ->
            assertThat(WifiConfigRequest.parseFrom(ddccmCfgRequestMessage.value()))
                    .extracting(WifiConfigRequest::getOperationsList).asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
                    .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAuthRealmProfile)
                    .describedAs("Should contains %s authRealmRrofile operations", authRadiusProfileSize)
                    .hasSize(authRadiusProfileSize)
                    .extracting(Operation::getAuthRealmProfile)
                    .map(authRealmProfile -> {
                      authRadiusProfileId.set(authRealmProfile.getId());
                      return authRadiusProfileId;
                    })
    );

    if (!apiAction.equals(CfgAction.UPDATE_NETWORK)) {
      return;
    }

    assertThatNoException().isThrownBy(() ->
            assertThat(WifiConfigRequest.parseFrom(ddccmCfgRequestMessage.value()))
                    .extracting(WifiConfigRequest::getOperationsList).asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
                    .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
                    .describedAs("Should contains %s WlanVenue operations", networkIdList.size())
                    .hasSize(networkIdList.size())
                    .allMatch(op -> StringUtils.isNotEmpty(op.getId()),
                            "All the ids of the WlanVenue operations should not be empty")
                    .allMatch(op -> op.getAction() == action(apiAction),
                            String.format("All the actions of the WlanVenue operations should be [%s]", apiAction))
                    .allSatisfy(op -> {
                      if (op.getAction() == Action.DELETE) {
                        assertThat(op)
                                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                                .matches(n -> networkIdList.contains(n.getWlanId()));
                      } else {
                        assertThat(payload).isNotNull();
                        if (isNonProxy) {
                          if (hasMacRegistrationListId) {
                            assertThat(op)
                                    .extracting(Operation::getWlanVenue)
                                    .extracting(WlanVenue::getAuthAaa)
                                    .matches(wlanAuth -> Objects.equals(wlanAuth.getThroughController(), true))
                                    .matches(wlanAuth -> Objects.equals(wlanAuth.getId(), radiusAuthenticationServiceId.get()));
                          } else {
                            assertThat(op)
                                    .extracting(Operation::getWlanVenue)
                                    .extracting(WlanVenue::getAuthAaa)
                                    .matches(wlanAuth -> Objects.equals(wlanAuth.getThroughController(), false))
                                    .matches(wlanAuth -> Objects.equals(wlanAuth.getId(), authRadiusVenueId.get()));
                          }
                        } else {
                          assertThat(op)
                                  .extracting(Operation::getWlanVenue)
                                  .extracting(WlanVenue::getProxyAuthAaa)
                                  .matches(wlanAuthProxy -> Objects.equals(wlanAuthProxy.getThroughController(),true))
                                  .matches(wlanAuthProxy -> Objects.equals(wlanAuthProxy.getId(), authRadiusProfileId.get()));
                        }

                        assertThat(op)
                                .extracting(Operation::getWlanVenue)
                                .extracting(WlanVenue::getAdvancedCustomization)
                                .isNotNull()
                                .matches(wlanAdvancedCustomization -> isAaaVlanOverrideAvailable
                                        ? Objects.equals(wlanAdvancedCustomization.getEnableAaaVlanOverride().getValue(), payload.getWlan().getAdvancedCustomization().getEnableAaaVlanOverride())
                                        : BooleanUtils.isFalse(wlanAdvancedCustomization.hasEnableAaaVlanOverride()));
                      }
                    }));
  }

  private com.ruckus.cloud.wifi.proto.Operation.Action wifiProtoAction(CfgAction apiAction) {
    switch (apiAction) {
      case ADD_NETWORK:
        return com.ruckus.cloud.wifi.proto.Operation.Action.ADD;
      case UPDATE_NETWORK:
        return com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY;
      case DELETE_NETWORK:
      case DELETE_NETWORKS:
        return com.ruckus.cloud.wifi.proto.Operation.Action.DELETE;
    }
    throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
  }

  private Action action(CfgAction apiAction) {
    switch (apiAction) {
      case ADD_NETWORK:
        return Action.ADD;
      case UPDATE_NETWORK:
        return Action.MODIFY;
      case DELETE_NETWORK:
      case DELETE_NETWORKS:
        return Action.DELETE;
    }
    throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
  }
}
