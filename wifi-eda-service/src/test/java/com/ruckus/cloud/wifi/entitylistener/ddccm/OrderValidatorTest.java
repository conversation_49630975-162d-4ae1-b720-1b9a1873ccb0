package com.ruckus.cloud.wifi.entitylistener.ddccm;

import static org.assertj.core.api.Assertions.assertThatNoException;

import org.junit.jupiter.api.Test;

public class OrderValidatorTest {

  @Test
  void testOrderValidator() {
    final var ddccmOrder = new DdccmOrder();
    ddccmOrder.init();

    assertThatNoException().isThrownBy(() -> {
      new OrderValidator(ddccmOrder).doValidation();
    });
  }
}
