package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.redis.service.RedisClientService;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.repository.TenantAvailableApFirmwareRepository;
import com.ruckus.cloud.wifi.repository.TenantCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.repository.VenueCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.service.impl.ApBranchFamilyServiceRouter;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Capabilities;
import com.ruckus.cloud.wifi.eda.servicemodel.CapabilitiesApModel;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.TenantFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.VenueFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.ApVersionService;
import com.ruckus.cloud.wifi.service.FirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.InternalFirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.UpgradeService;
import com.ruckus.cloud.wifi.service.exception.CapabilityNotFoundException;
import com.ruckus.cloud.wifi.service.impl.FirmwareCapabilityServiceImpl;
import com.ruckus.cloud.wifi.servicemodel.TenantEarlyAccessInfo;
import com.ruckus.cloud.wifi.servicemodel.enums.ApVersionCategory;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.TenantCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO ap_version (id, category, created_date, updated_date) VALUES
        ('*********.100', 'RECOMMENDED', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
        ('*********.110', 'RECOMMENDED', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
        ('*********.112', 'RECOMMENDED', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
        ('*********.100', 'RECOMMENDED', '2022-03-20 14:23:02.495', '2022-03-20 14:23:02.495'),
        ('*********.99', 'RECOMMENDED', '2022-03-20 14:23:02.495', '2022-03-20 14:23:02.495'),
        ('7.0.0.103.100', 'RECOMMENDED', '2022-03-20 14:23:02.495', '2022-03-20 14:23:02.495')
        ON CONFLICT (id) DO UPDATE SET 
          category = EXCLUDED.category,
          created_date = EXCLUDED.created_date,
          updated_date = EXCLUDED.updated_date;
    INSERT INTO tenant (id) VALUES ('rkg8y5147b3d45d892bd53f2761032b5'),
        ('68d34959cdd3433da4e29cb0f0640ae8'),
        ('9520ee78bb1111ec84220242ac120002');
    INSERT INTO venue (id, tenant, ap_version, timezone) VALUES
        ('2dec899ad64e47649981797a26ab5668', 'rkg8y5147b3d45d892bd53f2761032b5', '*********.100', 'Asia/Taipei'),
        ('8r4we2g4y8f2w18f5g1r2rr2r5e8w2d5', '68d34959cdd3433da4e29cb0f0640ae8', '*********.100', 'Asia/Taipei'),
        ('w4e5eg5we56we2esf21v5d4fe8e2fe1f', '9520ee78bb1111ec84220242ac120002', '*********.99', 'Asia/Taipei');
    """)
public class FirmwareCapabilityServiceJpaTest {

  @Autowired
  private FirmwareCapabilityService firmwareCapabilityService;

  @Autowired
  private TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository;

  @Autowired
  ApVersionRepository apVersionRepository;

  @Autowired
  TenantCurrentFirmwareRepository tenantCurrentFirmwareRepository;

  @Autowired
  VenueCurrentFirmwareRepository venueCurrentFirmwareRepository;

  @MockBean
  private InternalFirmwareCapabilityService internalFirmwareCapabilityService;

  @MockBean
  private TenantClient tenantClient;

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withTenant(TENANT_ID);

  private static final String TENANT_ID = "rkg8y5147b3d45d892bd53f2761032b5";
  private static final String VENUE_ID = "2dec899ad64e47649981797a26ab5668";

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  public void testGetTenantLatestCapabilities_whenApFwMgmtUpgradeByModelFfOn() throws CapabilityNotFoundException {
    // Given
    Map<String, String[]> apVersionMap = new HashMap<>();
    apVersionMap.put("*********.142", new String[]{"R500", "R550"});
    apVersionMap.put("6.2.1.103.1342", new String[]{"R310", "R550"});
    apVersionMap.put("*********.2321", new String[]{"R310", "R550"});
    apVersionMap.put("7.0.0.104.132", new String[]{"R550", "R770"});
    apVersionMap.put("7.0.0.104.133", new String[]{"R550", "R770"});

    for (Map.Entry<String, String[]> entry : apVersionMap.entrySet()) {
      String version = entry.getKey();
      String[] supported_ap_models = entry.getValue();
      apVersionRepository.save(newApVersion(version, List.of(supported_ap_models)));
      doReturn(newCapabilities(version, supported_ap_models)).when(internalFirmwareCapabilityService)
          .getCapabilities(version);
    }
    createTenantAvailableApFirmwares(TENANT_ID,
        List.of("*********.142", "6.2.1.103.1342", "*********.2321", "7.0.0.104.132", "7.0.0.104.133"));

    // When
    Capabilities capabilities = firmwareCapabilityService.getTenantLatestCapabilities();

    // Then
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "*********.142")).hasSize(1);
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "*********.2321")).hasSize(1);
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "7.0.0.104.133")).hasSize(2);
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  public void testGetVenueCapabilities_whenApFwMgmtUpgradeByModelFfOn() throws CapabilityNotFoundException {
    // Given
    Venue venue = VenueTestFixture.randomVenue(new Tenant(TENANT_ID), v -> v.setId(VENUE_ID));
    Map<String, String[]> apVersionMap = new HashMap<>();
    apVersionMap.put("6.2.1.103.1342", new String[]{"R310", "T310", "R550"});
    apVersionMap.put("7.0.0.104.132", new String[]{"R550", "R770"});
    apVersionMap.put("7.0.0.104.133", new String[]{"R550", "R670", "R770"});

    for (Map.Entry<String, String[]> entry : apVersionMap.entrySet()) {
      String version = entry.getKey();
      String[] supported_ap_models = entry.getValue();
      apVersionRepository.save(newApVersion(version, List.of(supported_ap_models)));
      doReturn(newCapabilities(version, supported_ap_models)).when(internalFirmwareCapabilityService)
          .getCapabilities(version);
    }

    createTenantAvailableApFirmwares(TENANT_ID, apVersionMap.keySet().stream().toList());
    createVenueCurrentFirmware(TENANT_ID, VENUE_ID, "7.0.0.104.132", "R770");
    createVenueCurrentFirmware(TENANT_ID, VENUE_ID, "7.0.0.104.132", "R550");
    // When
    Capabilities capabilities = firmwareCapabilityService.getVenueCapabilities(venue);

    // Then
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "6.2.1.103.1342")).hasSize(2);
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "7.0.0.104.132")).hasSize(2);
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "7.0.0.104.133")).hasSize(1);
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  public void testGetTenantLatestCapabilitiesApModel_whenApFwMgmtUpgradeByModelFfOn()
      throws CapabilityNotFoundException {
    // Given
    Map<String, String[]> apVersionMap = new HashMap<>();
    apVersionMap.put("*********.142", new String[]{"R500", "R550"});
    apVersionMap.put("6.2.1.103.1342", new String[]{"R310", "R550"});
    apVersionMap.put("7.0.0.104.132", new String[]{"R550", "R770", "R670"});
    apVersionMap.put("7.0.0.104.133", new String[]{"R550", "R770"});

    for (Map.Entry<String, String[]> entry : apVersionMap.entrySet()) {
      String version = entry.getKey();
      String[] supportedApModels = entry.getValue();
      apVersionRepository.save(newApVersion(version, List.of(supportedApModels)));
      for (String supported_ap_model : supportedApModels) {
        doReturn(newCapabilitiesApModel(version, supported_ap_model)).when(internalFirmwareCapabilityService)
            .getCapabilitiesApModel(version, supported_ap_model);
      }
    }
    createTenantAvailableApFirmwares(TENANT_ID, apVersionMap.keySet().stream().toList());

    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R500"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("*********.142");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R310"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("6.2.1.103.1342");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R670"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.104.132");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R770"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.104.133");
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  public void testGetCapabilitiesApModelByVenue_whenApFwMgmtUpgradeByModelFfOn()
      throws CapabilityNotFoundException {
    // Given
    Venue venue = VenueTestFixture.randomVenue(new Tenant(TENANT_ID), v -> v.setId(VENUE_ID));
    Map<String, String[]> apVersionMap = new HashMap<>();
    apVersionMap.put("6.2.1.103.1342", new String[]{"R310", "R550"});
    apVersionMap.put("7.0.0.104.132", new String[]{"R550", "R770", "R670"});
    apVersionMap.put("7.0.0.104.133", new String[]{"R550", "R770", "R777"});

    for (Map.Entry<String, String[]> entry : apVersionMap.entrySet()) {
      String version = entry.getKey();
      String[] supportedApModels = entry.getValue();
      apVersionRepository.save(newApVersion(version, List.of(supportedApModels)));
      for (String supported_ap_model : supportedApModels) {
        doReturn(newCapabilitiesApModel(version, supported_ap_model)).when(internalFirmwareCapabilityService)
            .getCapabilitiesApModel(version, supported_ap_model);
      }
    }
    createTenantAvailableApFirmwares(TENANT_ID, apVersionMap.keySet().stream().toList());
    createVenueCurrentFirmware(TENANT_ID, VENUE_ID, "7.0.0.104.132", "R550");
    createVenueCurrentFirmware(TENANT_ID, VENUE_ID, "7.0.0.104.132", "R770");

    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R310"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("6.2.1.103.1342");
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R770"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.104.132");
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R670"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.104.132");
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R777"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.104.133");
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE })
  public void testGetTenantLatestCapabilities_whenGreenfieldByApModelFfOn(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.142") ApVersion ver620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.2321") ApVersion ver624,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.104.133") ApVersion ver700
  ) throws CapabilityNotFoundException {
    Tenant tenant = new Tenant(TENANT_ID);
    String[] models = new String[]{"R500", "R650", "R750", "R770"};
    doReturn(newCapabilities(ver620.getId(), models)).when(internalFirmwareCapabilityService)
        .getCapabilities(ver620.getId());
    doReturn(newCapabilities(ver624.getId(), models)).when(internalFirmwareCapabilityService)
        .getCapabilities(ver624.getId());
    doReturn(newCapabilities(ver700.getId(), models)).when(internalFirmwareCapabilityService)
        .getCapabilities(ver700.getId());
    // Given
    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver620, "R500"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver624, "R650"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver700, "R770"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver700, "R750")
    ));

    // When
    Capabilities capabilities = firmwareCapabilityService.getTenantLatestCapabilities();

    // Then
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "*********.142")).hasSize(1)
        .extracting(CapabilitiesApModel::getModel).containsExactlyInAnyOrder("R500");
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "*********.2321")).hasSize(1)
        .extracting(CapabilitiesApModel::getModel).containsExactlyInAnyOrder("R650");
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "7.0.0.104.133")).hasSize(2)
        .extracting(CapabilitiesApModel::getModel).containsExactlyInAnyOrder("R750", "R770");
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE })
  public void testGetVenueCapabilities_whenGreenfieldByApModelFfOn(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.142") ApVersion ver620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.2321") ApVersion ver624,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.104.133") ApVersion ver700
  ) throws CapabilityNotFoundException {
    // Given
    Tenant tenant = new Tenant(TENANT_ID);
    Venue venue = VenueTestFixture.randomVenue(new Tenant(TENANT_ID), v -> v.setId(VENUE_ID));
    String[] models = new String[]{"R500", "R650", "R750", "R770"};
    doReturn(newCapabilities(ver620.getId(), models)).when(internalFirmwareCapabilityService)
        .getCapabilities(ver620.getId());
    doReturn(newCapabilities(ver624.getId(), models)).when(internalFirmwareCapabilityService)
        .getCapabilities(ver624.getId());
    doReturn(newCapabilities(ver700.getId(), models)).when(internalFirmwareCapabilityService)
        .getCapabilities(ver700.getId());
    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver620, "R500"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver624, "R650"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver700, "R770"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver700, "R750")
    ));
    createVenueCurrentFirmware(TENANT_ID, VENUE_ID, ver700.getId(), "R770");
    createVenueCurrentFirmware(TENANT_ID, VENUE_ID, ver700.getId(), "R650");
    // When
    Capabilities capabilities = firmwareCapabilityService.getVenueCapabilities(venue);

    // Then
    assertThat(getCapabilitiesApModelsByVersion(capabilities, ver620.getId())).hasSize(1)
        .extracting(CapabilitiesApModel::getModel).containsExactlyInAnyOrder("R500");
    assertThat(getCapabilitiesApModelsByVersion(capabilities, ver624.getId())).isEmpty();
    assertThat(getCapabilitiesApModelsByVersion(capabilities, ver700.getId())).hasSize(3)
        .extracting(CapabilitiesApModel::getModel).containsExactlyInAnyOrder("R650", "R750", "R770");
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE })
  public void testGetTenantLatestCapabilitiesApModel_whenGreenfieldByApModelFfOn(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.142") ApVersion ver620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.2321") ApVersion ver624,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.104.133") ApVersion ver700
  ) throws CapabilityNotFoundException {
    // Given
    Tenant tenant = new Tenant(TENANT_ID);
    doAnswer(invocation -> newCapabilitiesApModel(invocation.getArgument(0), invocation.getArgument(1)))
        .when(internalFirmwareCapabilityService)
        .getCapabilitiesApModel(anyString(), anyString());
    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver620, "R500"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver624, "R650"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver700, "R770"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver700, "R750")
    ));

    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R500"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo(ver620.getId());
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R650"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo(ver624.getId());
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R750"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo(ver700.getId());
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R770"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo(ver700.getId());
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE })
  public void testGetCapabilitiesApModelByVenue_whenGreenfieldByApModelFfOn(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.142") ApVersion ver620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.2321") ApVersion ver624,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.104.133") ApVersion ver700
  ) throws CapabilityNotFoundException {
    // Given
    Tenant tenant = new Tenant(TENANT_ID);
    Venue venue = VenueTestFixture.randomVenue(new Tenant(TENANT_ID), v -> v.setId(VENUE_ID));
    doAnswer(invocation -> newCapabilitiesApModel(invocation.getArgument(0), invocation.getArgument(1)))
        .when(internalFirmwareCapabilityService)
        .getCapabilitiesApModel(anyString(), anyString());
    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver620, "R500"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver624, "R650"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver700, "R770"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver700, "R750")
    ));
    createVenueCurrentFirmware(TENANT_ID, VENUE_ID, ver700.getId(), "R650");
    createVenueCurrentFirmware(TENANT_ID, VENUE_ID, ver700.getId(), "R770");

    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R500"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo(ver620.getId());
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R650"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo(ver700.getId());
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R770"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo(ver700.getId());
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R750"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo(ver700.getId());
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE,
      FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE })
  @Sql(statements = """
      INSERT INTO ap_version (id, category, supported_ap_models, labels) VALUES
          ('*********.100', 'RECOMMENDED', '["R500", "R550"]', 'ga'),
          ('*********.101', 'RECOMMENDED', '["R550"]', 'ga'),
          ('7.0.0.300.102', 'RECOMMENDED', '["R550", "R770"]', 'ga'),
          ('7.0.0.400.103', 'RECOMMENDED', '["R550", "R770", "beteNewModel"]', 'beta'),
          ('7.0.0.500.104', 'RECOMMENDED', '["R550", "R770", "alphaNewModel"]', 'alpha')
          ON CONFLICT (id) DO UPDATE SET category = EXCLUDED.category;
      INSERT INTO tenant (id) VALUES ('rkg8y5147b3d45d892bd53f2761032b5'), ('4rt53e147b3d45d892bdrf56wd34rft5');
      INSERT INTO venue (id, tenant, ap_version, timezone) VALUES
          ('2dec899ad64e47649981797a26ab5668', 'rkg8y5147b3d45d892bd53f2761032b5', '*********.100', 'Asia/Taipei'),
          ('3r4tgf9rfgtye7649981797r56f3wrtt', 'rkg8y5147b3d45d892bd53f2761032b5', '7.0.0.300.102', 'Asia/Taipei');
      INSERT INTO tenant_current_firmware(id, ap_model, firmware, tenant) VALUES 
          ('tcf1', 'R500', '*********.100', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf2', 'R550', '*********.101', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf3', 'R770', '7.0.0.300.102', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf4', 'R500', '*********.100', '4rt53e147b3d45d892bdrf56wd34rft5');
      INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant) VALUES
          ('vcf1', 'R770', '7.0.0.300.102', '2dec899ad64e47649981797a26ab5668', 'rkg8y5147b3d45d892bd53f2761032b5');      
      """)
  public void testGetTenantLatestCapabilities_whenApFwMgmtEarlyAccessFfOn() throws CapabilityNotFoundException {
    doReturn(new TenantEarlyAccessInfo(true, true)).when(tenantClient)
        .getEarlyAccessInfo(anyString(), anyString());
    doAnswer(invocation -> newCapabilitiesApModel(invocation.getArgument(0), invocation.getArgument(1)))
        .when(internalFirmwareCapabilityService)
        .getCapabilitiesApModel(anyString(), anyString());

    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R500"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("*********.100");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R550"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.500.104");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R770"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.500.104");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("beteNewModel"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.400.103");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("alphaNewModel"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.500.104");
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE,
      FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE
  })
  @Sql(statements = """
      INSERT INTO ap_version (id, category, supported_ap_models, labels) VALUES
          ('*********.100', 'RECOMMENDED', '["R500", "R550"]', 'ga'),
          ('*********.101', 'RECOMMENDED', '["R550"]', 'ga'),
          ('7.0.0.300.102', 'RECOMMENDED', '["R550", "R770"]', 'ga'),
          ('7.0.0.400.103', 'RECOMMENDED', '["R550", "R770", "beteNewModel"]', 'beta'),
          ('7.0.0.500.104', 'RECOMMENDED', '["R550", "R770", "alphaNewModel"]', 'alpha')
          ON CONFLICT (id) DO UPDATE SET category = EXCLUDED.category;
      INSERT INTO tenant (id) VALUES ('rkg8y5147b3d45d892bd53f2761032b5'), ('4rt53e147b3d45d892bdrf56wd34rft5');
      INSERT INTO venue (id, tenant, ap_version, timezone) VALUES
          ('2dec899ad64e47649981797a26ab5668', 'rkg8y5147b3d45d892bd53f2761032b5', '*********.100', 'Asia/Taipei'),
          ('3r4tgf9rfgtye7649981797r56f3wrtt', 'rkg8y5147b3d45d892bd53f2761032b5', '7.0.0.300.102', 'Asia/Taipei');
      INSERT INTO tenant_current_firmware(id, ap_model, firmware, tenant) VALUES 
          ('tcf1', 'R500', '*********.100', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf2', 'R550', '*********.101', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf3', 'R770', '7.0.0.300.102', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf4', 'R500', '*********.100', '4rt53e147b3d45d892bdrf56wd34rft5');
      INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant) VALUES
          ('vcf1', 'R770', '7.0.0.300.102', '2dec899ad64e47649981797a26ab5668', 'rkg8y5147b3d45d892bd53f2761032b5');      
      """)
  public void testGetVenueCapabilities_whenGreenfieldByApModelFfOn() throws CapabilityNotFoundException {
    // Given
    doReturn(new TenantEarlyAccessInfo(true, false)).when(tenantClient)
        .getEarlyAccessInfo(anyString(), anyString());
    String[] models = new String[]{"R500", "R550", "R770", "beteNewModel", "alphaNewModel"};
    doAnswer(invocation -> newCapabilities(invocation.getArgument(0), models))
        .when(internalFirmwareCapabilityService)
        .getCapabilities(anyString());
    Venue venue = VenueTestFixture.randomVenue(new Tenant(TENANT_ID), v -> v.setId(VENUE_ID));

    // When
    Capabilities capabilities = firmwareCapabilityService.getVenueCapabilities(venue);

    // Then
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "*********.100")).hasSize(1)
        .extracting(CapabilitiesApModel::getModel).containsExactlyInAnyOrder("R500");
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "*********.101")).isEmpty();
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "7.0.0.300.102")).hasSize(1)
        .extracting(CapabilitiesApModel::getModel).containsExactlyInAnyOrder("R770");
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "7.0.0.400.103")).hasSize(2)
        .extracting(CapabilitiesApModel::getModel).containsExactlyInAnyOrder("R550", "beteNewModel");
    assertThat(getCapabilitiesApModelsByVersion(capabilities, "7.0.0.500.104")).isEmpty();
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE,
      FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE
  })
  @Sql(statements = """
      INSERT INTO ap_version (id, category, supported_ap_models, labels) VALUES
          ('*********.100', 'RECOMMENDED', '["R500", "R550"]', 'ga'),
          ('*********.101', 'RECOMMENDED', '["R550"]', 'ga'),
          ('7.0.0.300.102', 'RECOMMENDED', '["R550", "R770"]', 'ga'),
          ('7.0.0.400.103', 'RECOMMENDED', '["R550", "R770", "beteNewModel"]', 'beta'),
          ('7.0.0.500.104', 'RECOMMENDED', '["R550", "R770", "alphaNewModel"]', 'alpha')
          ON CONFLICT (id) DO UPDATE SET category = EXCLUDED.category;
      INSERT INTO tenant (id) VALUES ('rkg8y5147b3d45d892bd53f2761032b5'), ('4rt53e147b3d45d892bdrf56wd34rft5');
      INSERT INTO venue (id, tenant, ap_version, timezone) VALUES
          ('2dec899ad64e47649981797a26ab5668', 'rkg8y5147b3d45d892bd53f2761032b5', '*********.100', 'Asia/Taipei'),
          ('3r4tgf9rfgtye7649981797r56f3wrtt', 'rkg8y5147b3d45d892bd53f2761032b5', '7.0.0.300.102', 'Asia/Taipei');
      INSERT INTO tenant_current_firmware(id, ap_model, firmware, tenant) VALUES 
          ('tcf1', 'R500', '*********.100', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf2', 'R550', '*********.101', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf3', 'R770', '7.0.0.300.102', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf4', 'R500', '*********.100', '4rt53e147b3d45d892bdrf56wd34rft5');
      INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant) VALUES
          ('vcf1', 'R770', '7.0.0.300.102', '2dec899ad64e47649981797a26ab5668', 'rkg8y5147b3d45d892bd53f2761032b5');      
      """)
  public void testGetTenantLatestCapabilitiesApModel_whenApFwMgmtEarlyAccessFfOn() throws CapabilityNotFoundException {
    // Given
    doReturn(new TenantEarlyAccessInfo(true, false)).when(tenantClient)
        .getEarlyAccessInfo(anyString(), anyString());
    doAnswer(invocation -> newCapabilitiesApModel(invocation.getArgument(0), invocation.getArgument(1)))
        .when(internalFirmwareCapabilityService)
        .getCapabilitiesApModel(anyString(), anyString());

    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R500"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("*********.100");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R550"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.400.103");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("R770"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.400.103");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("beteNewModel"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.400.103");
    assertThat(firmwareCapabilityService.getTenantLatestCapabilitiesApModel("alphaNewModel"))
        .isNull();
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE,
      FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE
  })
  @Sql(statements = """
      INSERT INTO ap_version (id, category, supported_ap_models, labels) VALUES
          ('*********.100', 'RECOMMENDED', '["R500", "R550"]', 'ga'),
          ('*********.101', 'RECOMMENDED', '["R550"]', 'ga'),
          ('7.0.0.300.102', 'RECOMMENDED', '["R550", "R770"]', 'ga'),
          ('7.0.0.400.103', 'RECOMMENDED', '["R550", "R770", "beteNewModel"]', 'beta'),
          ('7.0.0.500.104', 'RECOMMENDED', '["R550", "R770", "alphaNewModel"]', 'alpha')
          ON CONFLICT (id) DO UPDATE SET category = EXCLUDED.category;
      INSERT INTO tenant (id) VALUES ('rkg8y5147b3d45d892bd53f2761032b5'), ('4rt53e147b3d45d892bdrf56wd34rft5');
      INSERT INTO venue (id, tenant, ap_version, timezone) VALUES
          ('2dec899ad64e47649981797a26ab5668', 'rkg8y5147b3d45d892bd53f2761032b5', '*********.100', 'Asia/Taipei'),
          ('3r4tgf9rfgtye7649981797r56f3wrtt', 'rkg8y5147b3d45d892bd53f2761032b5', '7.0.0.300.102', 'Asia/Taipei');
      INSERT INTO tenant_current_firmware(id, ap_model, firmware, tenant) VALUES 
          ('tcf1', 'R500', '*********.100', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf2', 'R550', '*********.101', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf3', 'R770', '7.0.0.300.102', 'rkg8y5147b3d45d892bd53f2761032b5'),
          ('tcf4', 'R500', '*********.100', '4rt53e147b3d45d892bdrf56wd34rft5');
      INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant) VALUES
          ('vcf1', 'R770', '7.0.0.300.102', '2dec899ad64e47649981797a26ab5668', 'rkg8y5147b3d45d892bd53f2761032b5');      
      """)
  public void testGetCapabilitiesApModelByVenue_whenApFwMgmtEarlyAccessFfOn() throws CapabilityNotFoundException {
    doReturn(new TenantEarlyAccessInfo(true, true)).when(tenantClient)
        .getEarlyAccessInfo(anyString(), anyString());
    // Given
    Venue venue = VenueTestFixture.randomVenue(new Tenant(TENANT_ID), v -> v.setId(VENUE_ID));
    doAnswer(invocation -> newCapabilitiesApModel(invocation.getArgument(0), invocation.getArgument(1)))
        .when(internalFirmwareCapabilityService)
        .getCapabilitiesApModel(anyString(), anyString());

    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R500"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("*********.100");
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R550"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.500.104");
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "R770"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.300.102");
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "beteNewModel"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.400.103");
    assertThat(firmwareCapabilityService.getVenueCapabilitiesApModel(venue, "alphaNewModel"))
        .extracting(CapabilitiesApModel::getVersion).isEqualTo("7.0.0.500.104");
  }

  private List<CapabilitiesApModel> getCapabilitiesApModelsByVersion(Capabilities capabilities, String version) {
    return capabilities.getApModels().stream()
        .filter(capabilitiesApModel -> capabilitiesApModel.getVersion().equals(version))
        .collect(Collectors.toList());
  }

  private ApVersion newApVersion(String version) {
    return newApVersion(version, null);
  }

  private ApVersion newApVersion(String version, List<String> supported_ap_models) {
    ApVersion apVersion = new ApVersion();
    apVersion.setId(version);
    apVersion.setName(version);
    apVersion.setCategory(ApVersionCategory.RECOMMENDED);
    apVersion.setSupportedApModels(supported_ap_models);
    return apVersion;
  }

  private Capabilities newCapabilities(String version, String[] models) {
    Capabilities capabilities = new Capabilities();
    capabilities.setApModels(
        Arrays.stream(models)
            .map(m -> newCapabilitiesApModel(version, m))
            .collect(Collectors.toList()));
    return capabilities;
  }

  private CapabilitiesApModel newCapabilitiesApModel(String version, String model) {
    CapabilitiesApModel capabilitiesApModel = new CapabilitiesApModel();
    capabilitiesApModel.setModel(model);
    capabilitiesApModel.setVersion(version);
    return capabilitiesApModel;
  }

  private void createTenantAvailableApFirmwares(String tenantId, List<String> apVersions) {
    apVersions.forEach(v -> {
      TenantAvailableApFirmware taaf = new TenantAvailableApFirmware();
      taaf.setId(randomTxId());
      taaf.setTenant(new Tenant(tenantId));
      taaf.setApVersion(new ApVersion(v));
      tenantAvailableApFirmwareRepository.save(taaf);
    });
  }

  private void createVenueCurrentFirmware(String tenantId, String venueId, String apVersion, String apModel) {
    VenueCurrentFirmware vcf = new VenueCurrentFirmware();
    vcf.setApModel(apModel);
    vcf.setFirmware(new ApVersion(apVersion));
    vcf.setTenant(new Tenant(tenantId));
    vcf.setVenue(new Venue(venueId));
    venueCurrentFirmwareRepository.save(vcf);
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    @ConditionalOnMissingBean
    public FirmwareCapabilityService FirmwareCapabilityService(
        InternalFirmwareCapabilityService internalFirmwareCapabilityService,
        TenantClient tenantClient,
        TenantFirmwareVersionRepository tenantFirmwareVersionRepository,
        VenueFirmwareVersionRepository venueFirmwareVersionRepository,
        ApBranchFamilyServiceRouter apBranchFamilyService,
        ApVersionService apVersionService,
        UpgradeService upgradeService,
        VenueRepository venueRepository,
        FeatureFlagService featureFlagService,
        TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository,
        TenantCurrentFirmwareRepository tenantCurrentFirmwareRepository,
        VenueCurrentFirmwareRepository venueCurrentFirmwareRepository,
        ApVersionRepository apVersionRepository,
        RedisClientService redisClientService
    ) {
      return new FirmwareCapabilityServiceImpl(internalFirmwareCapabilityService,
          tenantClient,
          tenantFirmwareVersionRepository,
          venueFirmwareVersionRepository,
          apBranchFamilyService,
          apVersionService,
          upgradeService,
          venueRepository,
          featureFlagService,
          tenantAvailableApFirmwareRepository,
          tenantCurrentFirmwareRepository,
          venueCurrentFirmwareRepository,
          apVersionRepository,
          redisClientService);
    }

    @Bean
    @ConditionalOnMissingBean
    public ApBranchFamilyServiceRouter mockApBranchFamilyService() {
      return Mockito.mock(ApBranchFamilyServiceRouter.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantClient tenantClient() {
      return Mockito.mock(TenantClient.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public ApVersionService apVersionService() {
      return Mockito.mock(ApVersionService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public UpgradeService upgradeService() {
      return Mockito.mock(UpgradeService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisClientService redisClientService() { return  Mockito.mock(RedisClientService.class); }

  }
}
