package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.SoftGreProfileRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import java.util.Collections;
import java.util.List;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class VenueSoftGreFeatureTest {

  @MockBean
  private SoftGreProfileRepository repository;

  @SpyBean
  private VenueSoftGreFeature unit;

  @Nested
  class WhenConfigVenueSoftGre {

    @Test
    @FeatureFlag(disable = {WIFI_R370_TOGGLE})
    void givenFeatureFlagDisabled(Venue venue) {
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE})
    void givenSoftGreDisabled(Venue venue) {
      when(repository.findAllActivatedSoftGreProfileIdsByTenantIdAndVenueId(
          anyString(), anyString())).thenReturn(Collections.emptyList());

      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE})
    void givenSoftGreEnabled(Venue venue) {
      when(repository.findAllActivatedSoftGreProfileIdsByTenantIdAndVenueId(
          anyString(), anyString())).thenReturn(List.of("profile-id"));

      BDDAssertions.then(unit.test(venue)).isTrue();
    }
  }
}
