package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.GuestNetworkGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddNetworkTemplateRequestTest extends AbstractRequestTest {

  private static final String EXTERNAL_PORTAL_URL = "https://guestPortal.com";

  @Nested
  class AddGuestNetworkTemplateTest {

    private Radius radiusAuthentication;
    private Radius radiusAccounting;

    @BeforeEach
    void givenRadiusProfileForGuestNetworkTest(Tenant tenant) {
      radiusAuthentication = RadiusTestFixture.authRadius();
      radiusAuthentication.setName("test-radius-1");
      radiusAuthentication.setType(RadiusProfileTypeEnum.AUTHENTICATION);
      radiusAuthentication.setIsTemplate(true);
      radiusAuthentication.setTenant(tenant);
      radiusAccounting = RadiusTestFixture.authRadius();
      radiusAccounting.setName("test-radius-2");
      radiusAccounting.setType(RadiusProfileTypeEnum.ACCOUNTING);
      radiusAccounting.setIsTemplate(true);
      radiusAccounting.setTenant(tenant);

      repositoryUtil.createOrUpdate(radiusAuthentication, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(radiusAccounting, tenant.getId(), randomTxId());
    }

    @ApiAction.Payload("guestNetworkWithRadius")
    private GuestNetworkGenerator guestNetworkWithRadius() {
      return Generators.guestNetwork()
          .setName(serialName("GpenNetwork"))
          .setGuestPortal(always(Generators.guestPortal()
              .setGuestNetworkType(always(GuestNetworkTypeEnum.WISPr))
              .setWisprPage(always(GuestNetworkViewModelTestFixture
                  .wisprPageOtherProvider(map(radiusAuthentication), map(radiusAccounting))))
              .generate()))
          .setAuthRadiusId(always(radiusAuthentication.getId()))
          .setAuthRadius(always(map(radiusAuthentication)))
          .setAccountingRadiusId(always(radiusAccounting.getId()))
          .setAccountingRadius(always(map(radiusAccounting)));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_TEMPLATE, payload = @ApiAction.Payload("guestNetworkWithRadius"))
    void thenShouldHaveRadiusProfileForGuestNetwork(
        @ApiAction.Payload("guestNetworkWithRadius") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE);
      assertGuestNetwork(networkRequest, networkResult);
    }

    private void assertGuestNetwork(GuestNetwork networkRequest,
        com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork networkResult) {
      assertThat(networkResult)
          .matches(n -> Objects.nonNull(networkResult.getAuthRadius()))
          .matches(n -> Objects.nonNull(networkResult.getAccountingRadius()))
          .matches(n -> n.getAuthRadius().getId().equals(radiusAuthentication.getId())
              && n.getAccountingRadius().getId().equals(radiusAccounting.getId()))
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(g -> g.getGuestNetworkType().equals(GuestNetworkTypeEnum.WISPr),
              "GuestPortal.guestNetworkType")
          .matches(g -> g.getExternalPortalUrl().equals(EXTERNAL_PORTAL_URL),
              "GuestPortal.externalPortalUrl")
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal::getWisprPage)
          .isNotNull()
          .matches(w -> w.getCustomExternalProvider()
              .equals(networkRequest.getGuestPortal().getWisprPage().getCustomExternalProvider()))
          .matches(w -> w.getAuthType().equals(AuthTypeEnum.RADIUS));
    }
  }
}
