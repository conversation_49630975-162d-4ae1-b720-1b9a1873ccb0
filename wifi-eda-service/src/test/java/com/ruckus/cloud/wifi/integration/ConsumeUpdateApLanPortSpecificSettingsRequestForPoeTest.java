package com.ruckus.cloud.wifi.integration;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortOverwriteSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PoeModeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.ApLanPortSpecificSettingsGenerator;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApLanPortTestFixture;
import com.ruckus.cloud.wifi.test.fixture.EthernetPortProfileTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.Assertions;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_AP_POE_OPERATING_MODE_SETTING_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;

@Tag("ApPoeTest")
@WifiIntegrationTest
@FeatureFlag(enable = {WIFI_AP_POE_OPERATING_MODE_SETTING_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
class ConsumeUpdateApLanPortSpecificSettingsRequestForPoeTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeUpdateApLanPortSpecificSettingsRequestTest {

    private String serialNumber;
    private String venueId;

    @Payload("apLanPortSpecificSettings")
    private final ApLanPortSpecificSettingsGenerator generator =
        new ApLanPortSpecificSettingsGenerator()
            .setUseVenueSettings(always(Boolean.FALSE))
            .setPoeMode(always(PoeModeEnum._802_3af));

    @BeforeEach
    void givenOnePersistedInDb(
        final Tenant tenant,
        final ApGroup apGroup,
        final Venue venue,
        final @ApModel("R650") Ap ap) {
      initAp(ap, apGroup);
      createApLanPortData(venue, ap, 3, "1");
      createApLanPortData(venue, ap, 4, "2");
      serialNumber = ap.getId();
      venueId = venue.getId();
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      RequestParams requestParams =
          new RequestParams()
              .addPathVariable("venueId", venueId)
              .addPathVariable("serialNumber", serialNumber);
      var payload = generator.generate();

      messageUtil.sendWifiCfgRequest(
          txCtxExtension.getTenantId(),
          txCtxExtension.getRequestId(),
          CfgAction.UPDATE_AP_LAN_PORT_SPECIFIC_SETTINGS,
          "",
          requestParams,
          payload);

      Assertions.assertThat(messageCaptors.getDdccmMessageCaptor().getValue(txCtxExtension.getTenantId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(2)
          .filteredOn(Operation::hasAp)
          .singleElement()
          .matches(o -> o.getAction() == Action.MODIFY)
          .matches(o -> o.getAp().getModel().getPoeModeSetting() == com.ruckus.acx.ddccm.protobuf.wifi.PoeModeEnum._802_3af);
    }
  }

  private void initAp(Ap ap, ApGroup apGroup) {
    var apModelSpecific = new ApModelSpecific();
    apModelSpecific.setLanPorts(new ArrayList<>());
    apModelSpecific.setPoeMode(PoeModeEnum._802_3at);
    apModelSpecific = repositoryUtil.createOrUpdate(apModelSpecific, txCtxExtension.getTenantId());

    ap.setModelSpecific(apModelSpecific);
    ap.setApGroup(apGroup);
    repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId());
  }

  private EthernetPortProfile createEthernetPortProfile(Venue venue, int ethernetPortProfileId) {
    return repositoryUtil.createOrUpdate(
        EthernetPortProfileTestFixture.randomEthernetPortProfile(
            venue,
            e -> {
              e.setApLanPortId(ethernetPortProfileId);
              e.setName(randomName());
              e.setType(ApLanPortTypeEnum.TRUNK);
              e.setUntagId((short) 1);
              e.setVlanMembers("1-4094");
              e.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
            }),
        txCtxExtension.getTenantId());
  }

  private void createApLanPortData(
      Venue venue, Ap ap, int ethernetPortProfileId, String portId) {
    var apLanPortProfile = createEthernetPortProfile(venue, ethernetPortProfileId);
    var apLanPort =
        repositoryUtil.createOrUpdate(
            ApLanPortTestFixture.randomApLanPort(
                venue,
                ap.getModelSpecific(),
                port -> {
                  port.setApLanPortProfile(apLanPortProfile);
                  port.setPortId(portId);
                  port.setApLanPortOverwriteSettings(new LanPortOverwriteSettings());
                  port.getApLanPortOverwriteSettings().setUntagId(apLanPortProfile.getUntagId());
                  port.getApLanPortOverwriteSettings()
                      .setVlanMembers(apLanPortProfile.getVlanMembers());
                }),
            txCtxExtension.getTenantId());
    repositoryUtil.createOrUpdate(apLanPort, txCtxExtension.getTenantId());
  }
}
