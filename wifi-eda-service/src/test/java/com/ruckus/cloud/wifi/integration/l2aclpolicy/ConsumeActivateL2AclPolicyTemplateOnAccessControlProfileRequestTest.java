package com.ruckus.cloud.wifi.integration.l2aclpolicy;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmFirewallL2AccessControlPolicy;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmFirewallProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeActivateL2AclPolicyTemplateOnAccessControlProfileRequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeActivateL2AclPolicyTemplateOnAccessControlProfileMessage {
    private String accessControlProfileTemplateId;
    private String l2AclPolicyTemplateId;

    @BeforeEach
    void beforeEach(@Template AccessControlProfile accessControlProfile, @Template L2AclPolicy l2AclPolicy) {
      accessControlProfileTemplateId = accessControlProfile.getId();
      l2AclPolicyTemplateId = l2AclPolicy.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("accessControlProfileTemplateId", accessControlProfileTemplateId)
          .addPathVariable("l2AclPolicyTemplateId", l2AclPolicyTemplateId);
    }

    @Test
    void givenL2AclPolicyNotExists(Tenant tenant) {
      final var notExistL2AclPolicyId = randomId();
      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.ACTIVATE_L2ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE,
                      randomName(),
                      requestParams().addPathVariable("l2AclPolicyTemplateId", notExistL2AclPolicyId),
                      ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(ObjectNotFoundException.class);

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a ->
                  a.getStep().equals(ApiFlowNames.ACTIVATE_L2ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE));
    }

    @Nested
    class GivenL2AclPolicyExists {
      @Test
      void givenAccessControlProfileNotExists(Tenant tenant) {
        final var notExistAccessControlProfileId = randomId();
        assertThatThrownBy(
                () ->
                    messageUtil.sendWifiCfgRequest(
                        tenant.getId(),
                        txCtxExtension.getRequestId(),
                        CfgAction.ACTIVATE_L2ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE,
                        randomName(),
                        requestParams()
                            .addPathVariable(
                                "accessControlProfileTemplateId", notExistAccessControlProfileId),
                        ""))
            .isNotNull()
            .getRootCause()
            .isInstanceOf(ObjectNotFoundException.class);

        messageCaptors.assertThat(
            messageCaptors.getDdccmMessageCaptor(),
            messageCaptors.getCmnCfgCollectorMessageCaptor()
        ).doesNotSendByTenant(tenant.getId());

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
            .matches(
                a ->
                    a.getStep()
                        .equals(ApiFlowNames.ACTIVATE_L2ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE));
      }

      @Test
      void givenAccessControlProfileExists(Tenant tenant) {
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.ACTIVATE_L2ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE,
            randomName(),
            requestParams(),
            "");

        assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(
                m -> m.getPayload().getOperationsList(),
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .hasSize(1)
            .first()
            .matches(o -> o.getAction() == Action.MODIFY)
            .extracting(Operation::getCcmFirewallProfile)
            .extracting(CcmFirewallProfile::getFirewallL2AccessControlPolicy)
            .extracting(CcmFirewallL2AccessControlPolicy::getL2AccessControlPolicyId)
            .extracting(StringValue::getValue)
            .isEqualTo(l2AclPolicyTemplateId);

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(1)
            .first()
            .matches(o -> o.getOpType() == OpType.MOD)
            .matches(o -> o.getId().equals(accessControlProfileTemplateId))
            .extracting(
                Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
            .extractingByKey(EsConstants.Key.L2_ACL_POLICY_ID)
            .isNotNull()
            .extracting(Value::getStringValue)
            .isEqualTo(l2AclPolicyTemplateId);

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a ->
                    a.getStep()
                        .equals(ApiFlowNames.ACTIVATE_L2ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE));
      }
    }
  }
}
