package com.ruckus.cloud.wifi.integration.networkactivation;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeShort;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.vlanPool;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.networkApGroup;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertNetworkVenueSoftly;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DpskNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.repository.DpskNetworkRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

@Tag("NetworkActivationTest")
@WifiIntegrationTest
class ConsumeUpdateNetworkActivationRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private DpskNetworkRepository dpskNetworkRepository;

  @Nested
  class GivenOpenNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String apGroupId;
    private String networkVenueId;

    @BeforeEach
    void givenOpenNetworkAndRelatedEntitiesPersistedInDb(@OpenNetwork Network network, Venue venue,
        @DefaultApGroup ApGroup apGroup,
        @ScheduledNetworkVenue NetworkVenue networkVenue, NetworkApGroup networkApGroup,
        NetworkApGroupRadio networkApGroupRadio) {
      networkId = network.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
      networkVenueId = networkVenue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkVenueId", networkVenueId);
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz));
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(CfgAction.UPDATE_NETWORK_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload,
          CfgAction.UPDATE_NETWORK_VENUE.key(), true);
    }

    @Payload("WithSpecificApGroup")
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithSpecificApGroup() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(false);
      final var networkApGroup = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkApGroup();
      networkApGroup.setApGroupId(apGroupId);
      networkApGroup.setVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      networkApGroup.setRadioTypes(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz));
      payload.setApGroups(List.of(networkApGroup));
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK_VENUE, payload = @Payload("WithSpecificApGroup"))
    void thenShouldHandleTheRequestWithSpecificApGroupSuccessfully(TxCtx txCtx,
        @Payload("WithSpecificApGroup") com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload,
          CfgAction.UPDATE_NETWORK_VENUE.key(), true);
    }

    @Tag("VlanPoolTest")
    @Nested
    class WithVlanPoolAssociation {

      private String vlanPoolId;

      @BeforeEach
      void givenOneVlanPoolPersistedInDb(final Tenant tenant) {
        final var vlanPool = vlanPool().generate();
        repositoryUtil.createOrUpdate(vlanPool, tenant.getId(), randomTxId());
        vlanPoolId = vlanPool.getId();
      }

      @ApiAction.RequestParams
      private RequestParams requestParams() {
        return new RequestParams().addPathVariable("networkVenueId", networkVenueId);
      }

      @Payload
      private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithVlanPool() {
        final var generator = Generators.networkVenue()
            .setId(nullValue(String.class))
            .setNetworkId(always(networkId))
            .setVenueId(always(venueId))
            .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM))
            .setAllApGroupsRadioTypes(
                list(options(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz), 2))
            .setVlanPoolId(always(vlanPoolId));
        return generator.generate();
      }

      @Test
      @ApiAction(CfgAction.UPDATE_NETWORK_VENUE)
      void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
          @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload,
            CfgAction.UPDATE_NETWORK_VENUE.key(), true);
      }

      @Payload("WithVlanPoolInApGroup")
      private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithVlanPoolInApGroup() {
        final var generator = Generators.networkVenue()
            .setId(nullValue(String.class))
            .setNetworkId(always(networkId))
            .setVenueId(always(venueId))
            .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM))
            .setIsAllApGroups(alwaysFalse())
            .setApGroups(list(networkApGroup()
                .setId(nullValue(String.class))
                .setRadioTypes(list(options(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz), 2))
                .setApGroupId(always(apGroupId))
                .setVlanPoolId(always(vlanPoolId)), 1));
        return generator.generate();
      }

      @Test
      @ApiAction(value = CfgAction.UPDATE_NETWORK_VENUE, payload = @Payload("WithVlanPoolInApGroup"))
      void thenShouldHandleTheRequestWithVlanPoolInApGroupSuccessfully(TxCtx txCtx,
          @Payload("WithVlanPoolInApGroup") com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload,
            CfgAction.UPDATE_NETWORK_VENUE.key(), true);
      }

      @Test
      @ApiAction(CfgAction.UPDATE_NETWORK_VENUE)
      void thenShouldHandleTheRequestSuccessfully(TestInfo testInfo, TxCtx txCtx,
          @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(testInfo, txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload,
            CfgAction.UPDATE_NETWORK_VENUE.key());
      }
    }

    @Tag("MultipleNetworkApGroupTest")
    @Nested
    class WithMultipleNetworkApGroupAssociation {

      private String apGroup2Id;

      @BeforeEach
      void givenMultipleNetworkApGroupPersistedInDb(final Venue venue,
          final NetworkVenue networkVenue) {
        final var apGroup2 = ApGroupTestFixture.randomApGroup(venue);
        repositoryUtil.createOrUpdate(apGroup2, venue.getTenant().getId(), randomTxId());
        final var networkApGroup2 = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
            apGroup2);
        repositoryUtil.createOrUpdate(networkApGroup2, venue.getTenant().getId(), randomTxId());
        final var networkApGroupRadio2 = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
            networkApGroup2);
        repositoryUtil.createOrUpdate(networkApGroupRadio2, venue.getTenant().getId(),
            randomTxId());

        final var apGroup3 = ApGroupTestFixture.randomApGroup(venue);
        repositoryUtil.createOrUpdate(apGroup3, venue.getTenant().getId(), randomTxId());
        final var networkApGroup3 = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
            apGroup3);
        repositoryUtil.createOrUpdate(networkApGroup3, venue.getTenant().getId(), randomTxId());
        final var networkApGroupRadio3 = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
            networkApGroup3);
        repositoryUtil.createOrUpdate(networkApGroupRadio3, venue.getTenant().getId(),
            randomTxId());

        apGroup2Id = apGroup2.getId();
      }

      @ApiAction.RequestParams
      private RequestParams requestParams() {
        return new RequestParams().addPathVariable("networkVenueId", networkVenueId);
      }

      @Payload
      private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithApGroupRadioChangedInIsAllApGroupsFalse() {
        final var generator = Generators.networkVenue()
            .setId(nullValue(String.class))
            .setNetworkId(always(networkId))
            .setVenueId(always(venueId))
            .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM))
            .setIsAllApGroups(alwaysFalse())
            .setAllApGroupsRadio(always(RadioEnum.Both))
            .setAllApGroupsRadioTypes(list(options(RadioTypeEnum._5_GHz), 1))
            .setApGroups(list(networkApGroup()
                    .setId(nullValue(String.class)).setVlanId(nullValue(Short.class))
                    .setRadioTypes(list(options(RadioTypeEnum._5_GHz), 1))
                    .setApGroupId(always(apGroup2Id))
                    .setRadio(always(RadioEnum._5_GHz))
                , 1));
        return generator.generate();
      }

      @Test
      @ApiAction(CfgAction.UPDATE_NETWORK_VENUE)
      void thenShouldHandleTheRequestSuccessfully(TestInfo testInfo, TxCtx txCtx,
          @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(testInfo, txCtx.getTenant(), txCtx.getTxId(), networkVenueId,
            payload, CfgAction.UPDATE_NETWORK_VENUE.key());
      }
    }
  }

  @Nested
  class GivenDsaeNetworkPersistedInDb {

    @Payload("addDsaeNetwork")
    private DpskNetworkGenerator addDsaeNetwork() {
      return Generators.dpskNetwork()
          .setName(serialName("AddDsaeNetwork"))
          .setDescription(randomString(64))
          .setDpskServiceProfileId(defaultIdGenerator())
          .setWlan(Generators.dpskWpa23MixedModeWlan());
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("addDsaeNetwork"))
    @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
    @FeatureRole("BETA-DPSK3")
    void thenShouldHandleUpdateDsaeNetworkVenueSuccessfully(TxCtxHolder.TxCtx txCtx,
        CfgAction apiAction,
        @Payload("addDsaeNetwork") com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload,
        Venue venue) {

      var dsaeNetwork = (DpskNetwork) dpskNetworkRepository.findByIdAndTenantId(payload.getId(),
          txCtx.getTenant()).get();
      var dsaeOnboardNetwork = dpskNetworkRepository.findByDsaeNetworkPairIdAndTenantIdAndIsDsaeServiceNetwork(
          dsaeNetwork.getDsaeNetworkPairId(), TxCtxHolder.tenantId(), false);

      final var dsaeServiceNetworkVenue = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
      dsaeServiceNetworkVenue.setId(randomId()); // autoGenerated is true in wifi-api
      dsaeServiceNetworkVenue.setNetworkId(dsaeNetwork.getId());
      dsaeServiceNetworkVenue.setVenueId(venue.getId());
      dsaeServiceNetworkVenue.setIsAllApGroups(true);
      dsaeServiceNetworkVenue.setAllApGroupsVlanId(
          rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dsaeServiceNetworkVenue.setAllApGroupsRadioTypes(
          List.of(RadioTypeEnum.values())); // All radio types
      sendCreateNetworkActivation(txCtx.getTenant(), List.of(dsaeServiceNetworkVenue));

      final var dsaeOnboardNetworkVenue = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
      dsaeOnboardNetworkVenue.setId(randomId()); // autoGenerated is true in wifi-api
      dsaeOnboardNetworkVenue.setNetworkId(dsaeOnboardNetwork.getId());
      dsaeOnboardNetworkVenue.setVenueId(venue.getId());
      dsaeOnboardNetworkVenue.setIsAllApGroups(true);
      dsaeOnboardNetworkVenue.setAllApGroupsVlanId(
          rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dsaeOnboardNetworkVenue.setAllApGroupsRadioTypes(
          List.of(RadioTypeEnum.values())); // All radio types
      sendCreateNetworkActivation(txCtx.getTenant(), List.of(dsaeOnboardNetworkVenue));

      final var dsaeServiceNetworkVenueMod = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      dsaeServiceNetworkVenueMod.setId(
          dsaeServiceNetworkVenue.getId()); // autoGenerated is true in wifi-api
      dsaeServiceNetworkVenueMod.setNetworkId(dsaeNetwork.getId());
      dsaeServiceNetworkVenueMod.setVenueId(venue.getId());
      dsaeServiceNetworkVenueMod.setIsAllApGroups(true);
      dsaeServiceNetworkVenueMod.setAllApGroupsVlanId(
          rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dsaeServiceNetworkVenueMod.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._5_GHz));
      var requestId = randomTxId();
      sendUpdateNetworkActivation(txCtx.getTenant(), requestId, dsaeServiceNetworkVenueMod);

      validateDsaeResult(txCtx.getTenant(), requestId, dsaeServiceNetworkVenueMod.getId(),
          dsaeServiceNetworkVenueMod);
    }

    void validateDsaeResult(String tenantId, String requestId,
        String networkVenueId, com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);

      assertThat(networkVenue)
          .isNotNull()
          .matches(nv -> Objects.equals(nv.getNetwork().getId(), payload.getNetworkId()))
          .matches(nv -> Objects.equals(nv.getVenue().getId(), payload.getVenueId()));

      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(
              assertHeader(WifiCommonHeader.API_ACTION, CfgAction.UPDATE_NETWORK_VENUE.key()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY)
                  .as("The MODIFY NetworkVenue operation count should be 2")
                  .hasSize(2)
                  .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                  .anySatisfy(
                      assertNetworkVenueSoftly(payload.getNetworkId(), payload.getVenueId()))));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2)
              .allMatch(op -> op.getOpType() == OpType.MOD)
              .extracting(Operations::getDocMap)
              .allMatch(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
              .allMatch(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
              .allMatch(doc -> payload.getVenueId().equals(doc.get(Key.VENUE_ID).getStringValue()))
              .anyMatch(doc -> networkVenueId.equals(doc.get(Key.ID).getStringValue()))
              .anyMatch(
                  doc -> payload.getNetworkId().equals(doc.get(Key.NETWORK_ID).getStringValue())));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.UPDATE_NETWORK_VENUE))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor()
          .assertNotSent(tenantId, requestId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .satisfies(op -> assertSoftly(softly -> {
                softly.assertThat(payload).isNotNull();
                softly.assertThat(op)
                    .filteredOn(Operation::hasWlanVenue)
                    .hasSize(2)
                    .allMatch(wlanVenueOp -> wlanVenueOp.getAction() == Action.MODIFY,
                        String.format(
                            "The value of `action` field in WlanVenue operation should be %s",
                            Action.MODIFY))
                    .allMatch(wlanVenueOp -> networkVenueId.equals(wlanVenueOp.getId()),
                        String.format("The value of `id` field in WlanVenue operation should be %s",
                            networkVenueId))
                    .extracting(Operation::getWlanVenue)
                    .anySatisfy(wlanVenue -> assertSoftly(alsoSoftly -> {
                      alsoSoftly.assertThat(wlanVenue.getWlanId())
                          .isEqualTo(payload.getNetworkId());
                      alsoSoftly.assertThat(wlanVenue.getVenueId())
                          .isEqualTo(payload.getVenueId());
                    }));
              })));
    }

  }

  @Nested
  class GivenOpenNetworkTemplatePersistedInDb {

    private String networkId;
    private String venueId;
    private String apGroupId;
    private String networkVenueId;

    @BeforeEach
    void givenOpenNetworkAndRelatedEntitiesPersistedInDb(
        @OpenNetwork(isTemplate = true) Network network, @Template Venue venue,
        @DefaultApGroup ApGroup apGroup, @ScheduledNetworkVenue @Template NetworkVenue networkVenue,
        NetworkApGroup networkApGroup,
        NetworkApGroupRadio networkApGroupRadio) {
      networkId = network.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
      networkVenueId = networkVenue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkVenueId", networkVenueId);
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz));
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Payload("NetworkActivationMapping")
    private List<NetworkActivationMapping> networkActivationMappings() {
      var payload = new NetworkActivationMapping();
      payload.setId(networkVenueId);
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz));
      payload.setScheduler(dummyNetworkVenueScheduler());
      return List.of(payload);
    }

    @Test
    @ApiAction(CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE)
    void thenShouldUpdateNetworkVenueTemplateSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload,
          CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE.key(), false);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE_MAPPINGS, payload = @Payload("NetworkActivationMapping"))
    void thenShouldUpdateMappingSuccessfully(TxCtx txCtx,
        @Payload("NetworkActivationMapping") List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload.get(0),
          CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE_MAPPINGS.key());
    }

    // added for VlanPool Template
    @Tag("VlanPoolTemplateTest")
    @Nested
    class WithVlanPoolTemplateAssociation {

      private String vlanPoolTemplateId;

      @BeforeEach
      void givenOneVlanPoolPersistedInDb(final Tenant tenant) {
        final var vlanPool = vlanPool().setIsTemplate(always(true)).generate();
        repositoryUtil.createOrUpdate(vlanPool, tenant.getId(), randomTxId());
        vlanPoolTemplateId = vlanPool.getId();
      }

      @ApiAction.RequestParams
      private RequestParams requestParams() {
        return new RequestParams().addPathVariable("networkVenueId", networkVenueId);
      }

      @Payload
      private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithVlanPoolTemplate() {
        final var generator = Generators.networkVenue()
          .setId(nullValue(String.class))
          .setNetworkId(always(networkId))
          .setVenueId(always(venueId))
          .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM))
          .setAllApGroupsRadioTypes(
            list(options(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz), 2))
          .setVlanPoolId(always(vlanPoolTemplateId));
        return generator.generate();
      }

      @Payload("WithVlanPoolTemplateInApGroup")
      private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithVlanPoolInApGroup() {
        final var generator = Generators.networkVenue()
          .setId(nullValue(String.class))
          .setNetworkId(always(networkId))
          .setVenueId(always(venueId))
          .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM))
          .setIsAllApGroups(alwaysFalse())
          .setApGroups(list(networkApGroup()
            .setId(nullValue(String.class))
            .setRadioTypes(list(options(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz), 2))
            .setApGroupId(always(apGroupId))
            .setVlanPoolId(always(vlanPoolTemplateId)), 1));
        return generator.generate();
      }

      @Test
      @ApiAction(CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE)
      void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload,
          CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE.key(), false);
      }

      @Test
      @ApiAction(value = CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE, payload = @Payload("WithVlanPoolTemplateInApGroup"))
      void thenShouldHandleTheRequestWithVlanPoolInApGroupSuccessfully(TxCtx txCtx,
        @Payload("WithVlanPoolTemplateInApGroup") com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload,
          CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE.key(), false);
      }

      @Test
      @ApiAction(CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE)
      void thenShouldHandleTheRequestSuccessfully(TestInfo testInfo, TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(testInfo, txCtx.getTenant(), txCtx.getTxId(), networkVenueId, payload,
          CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE.key());
      }
    }
  }

  private void sendCreateNetworkActivation(String tenantId,
      List<com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping> networkVenues) {
    WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
        .tenantId(tenantId)
        .requestId(randomTxId())
        .addHeader(RKS_IDM_USER_ID.getName(), randomName())
        .apiAction(CfgAction.ADD_NETWORK_VENUE_MAPPINGS)
        .payload(networkVenues).build();

    messageUtil.sendWifiCfgRequest(wifiCfgRequest);
  }

  private void sendUpdateNetworkActivation(String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue networkVenue) {
    WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
        .tenantId(tenantId)
        .requestId(requestId)
        .addHeader(RKS_IDM_USER_ID.getName(), randomName())
        .apiAction(CfgAction.UPDATE_NETWORK_VENUE)
        .requestParams(new RequestParams().addPathVariable("networkVenueId", networkVenue.getId()))
        .payload(networkVenue).build();

    messageUtil.sendWifiCfgRequest(wifiCfgRequest);
  }

  void validateResult(String tenantId, String requestId, String networkVenueId,
      com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload, String apiAction,
      boolean hasImpact) {
    validateResult(null, tenantId, requestId, networkVenueId, payload, apiAction);
  }

  void validateEntity(String networkVenueId, String networkId, String venueId) {
    final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);

    assertThat(networkVenue)
        .isNotNull()
        .matches(nv -> Objects.equals(nv.getNetwork().getId(), networkId))
        .matches(nv -> Objects.equals(nv.getVenue().getId(), venueId));
  }

  void validateWifiCfgChangeMessage(String tenantId, String requestId, String apiAction,
      String networkId, String venueId) {
    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(wifiCfgChangeMessage).isNotNull()
        .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(wifiCfgChangeMessage.getPayload())
            .satisfies(msg -> assertSoftly(softly -> {
              softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
              softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
            }))
            .extracting(WifiConfigChange::getOperationList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
            .satisfies(ops -> assertThat(ops)
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .filteredOn(
                    op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY)
                .as("The MODIFY NetworkVenue operation count should be 1")
                .hasSize(1)
                .singleElement()
                .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                .satisfies(
                    assertNetworkVenueSoftly(networkId, venueId))));
  }

  void validateCmnCfgCollectorMessage(String tenantId, String requestId, String networkVenueId,
      String networkId, String venueId) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
            .hasSize(1)
            .singleElement()
            .matches(op -> op.getOpType() == OpType.MOD)
            .matches(op -> networkVenueId.equals(op.getId()))
            .extracting(Operations::getDocMap)
            .matches(doc -> networkVenueId.equals(doc.get(Key.ID).getStringValue()))
            .matches(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
            .matches(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
            .matches(doc -> networkId.equals(doc.get(Key.NETWORK_ID).getStringValue()))
            .matches(doc -> venueId.equals(doc.get(Key.VENUE_ID).getStringValue())));
  }

  void validateActivityMessages(String tenantId, String requestId, String apiAction) {
    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiAction))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);
  }

  void validateDdccmCfgRequestMessage(TestInfo testInfo, String tenantId, String requestId,
      String networkVenueId,
      com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                    String.format("The value of `requestId` field in CommonInfo should be [%s]",
                        requestId))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                    String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                        tenantId))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                    String.format("The value of `sender` field in CommonInfo should be [%s]",
                        ServiceType.WIFI_SERVICE)))
            .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 1,
                "The count of VenueSchedule operations should be 1")
            .satisfies(op -> assertSoftly(softly -> {
              softly.assertThat(payload).isNotNull();
              softly.assertThat(op)
                  .filteredOn(Operation::hasWlanVenue)
                  .hasSize(1)
                  .singleElement()
                  .matches(wlanVenueOp -> wlanVenueOp.getAction() == Action.MODIFY,
                      String.format(
                          "The value of `action` field in WlanVenue operation should be %s",
                          Action.MODIFY))
                  .matches(wlanVenueOp -> networkVenueId.equals(wlanVenueOp.getId()),
                      String.format("The value of `id` field in WlanVenue operation should be %s",
                          networkVenueId))
                  .extracting(Operation::getWlanVenue)
                  .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                    alsoSoftly.assertThat(wlanVenue.getWlanId())
                        .isEqualTo(payload.getNetworkId());
                    alsoSoftly.assertThat(wlanVenue.getVenueId())
                        .isEqualTo(payload.getVenueId());
                  }));
              softly.assertThat(op)
                  .filteredOn(Operation::hasWlanApGroup)
                  .satisfies(ops -> assertSoftly(alsoSoftly -> {
                    if (testInfo != null && testInfo.getTags() != null && testInfo.getTags()
                        .contains("MultipleNetworkApGroupTest")) {
                      alsoSoftly.assertThat(ops)
                          .matches(wlanApGroupOps -> wlanApGroupOps.stream()
                                  .filter(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.ADD)
                                  .count() == 3,
                              "The count of ADD WlanApGroup operations should be 3") // ADD 5G, lower 5G and upper 5G on wlanApGroup 2
                          .matches(wlanApGroupOps -> (Objects.isNull(payload.getAllApGroupsVlanId())
                                  && StringUtils.isEmpty(payload.getVlanPoolId()))
                                  || wlanApGroupOps.stream()
                                  .filter(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.DELETE)
                                  .count() == 3,
                              "The count of MODIFY WlanApGroup operations should be 3"); // DELETE 2.4G on 3 wlanApGroups
                    } else {
                      alsoSoftly.assertThat(ops)
                          .matches(wlanApGroupOps -> wlanApGroupOps.stream()
                                  .filter(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.ADD)
                                  .count() == 3,
                              "The count of ADD WlanApGroup operations should be 3") // ADD 5G, lower 5G and upper 5G
                          .matches(wlanApGroupOps -> (Objects.isNull(payload.getAllApGroupsVlanId())
                                  && StringUtils.isEmpty(payload.getVlanPoolId()))
                                  || wlanApGroupOps.stream()
                                  .filter(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.MODIFY)
                                  .count() == 1,
                              "The count of MODIFY WlanApGroup operations should be 1"); // MODIFY 2.4G
                    }
                  }))
                  .extracting(Operation::getWlanApGroup)
                  .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                    if (BooleanUtils.isFalse(payload.getIsAllApGroups())) {
                      return;
                    }
                    // isAllApGroups != false
                    if (StringUtils.isEmpty(payload.getVlanPoolId())) {
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                      if (payload.getAllApGroupsVlanId() != null) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanId())
                            .extracting(Int32Value::getValue)
                            .isEqualTo(payload.getAllApGroupsVlanId().intValue());
                      } else {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      }
                      return;
                    }
                    alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                    alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                    alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                        .extracting(StringValue::getValue)
                        .isEqualTo(payload.getVlanPoolId());
                  }))
                  .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                    if (BooleanUtils.isNotFalse(payload.getIsAllApGroups())
                        || CollectionUtils.isEmpty(payload.getApGroups())) {
                      return;
                    }
                    // isAllApGroups == false
                    final var networkApGroupReq = payload.getApGroups().get(0);
                    if (StringUtils.isEmpty(networkApGroupReq.getVlanPoolId())) {
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                      if (networkApGroupReq.getVlanId() != null) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanId())
                            .extracting(Int32Value::getValue)
                            .isEqualTo(networkApGroupReq.getVlanId().intValue());
                      } else {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      }
                      return;
                    }
                    alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                    alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                    alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                        .extracting(StringValue::getValue)
                        .isEqualTo(networkApGroupReq.getVlanPoolId());
                  }));
            })));
  }

  void validateDdccmCfgRequestMessage(TestInfo testInfo, String tenantId, String requestId,
      String networkVenueId,
      NetworkActivationMapping payload) {
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                    String.format("The value of `requestId` field in CommonInfo should be [%s]",
                        requestId))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                    String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                        tenantId))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                    String.format("The value of `sender` field in CommonInfo should be [%s]",
                        ServiceType.WIFI_SERVICE)))
            .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 1,
                "The count of VenueSchedule operations should be 1")
            .satisfies(op -> assertSoftly(softly -> {
              softly.assertThat(payload).isNotNull();
              softly.assertThat(op)
                  .filteredOn(Operation::hasWlanVenue)
                  .hasSize(1)
                  .singleElement()
                  .matches(wlanVenueOp -> wlanVenueOp.getAction() == Action.MODIFY,
                      String.format(
                          "The value of `action` field in WlanVenue operation should be %s",
                          Action.MODIFY))
                  .matches(wlanVenueOp -> networkVenueId.equals(wlanVenueOp.getId()),
                      String.format("The value of `id` field in WlanVenue operation should be %s",
                          networkVenueId))
                  .extracting(Operation::getWlanVenue)
                  .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                    alsoSoftly.assertThat(wlanVenue.getWlanId())
                        .isEqualTo(payload.getNetworkId());
                    alsoSoftly.assertThat(wlanVenue.getVenueId())
                        .isEqualTo(payload.getVenueId());
                  }));
              softly.assertThat(op)
                  .filteredOn(Operation::hasWlanApGroup)
                  .satisfies(ops -> assertSoftly(alsoSoftly -> {
                    if (testInfo != null && testInfo.getTags() != null && testInfo.getTags()
                        .contains("MultipleNetworkApGroupTest")) {
                      alsoSoftly.assertThat(ops)
                          .matches(wlanApGroupOps -> wlanApGroupOps.stream()
                                  .filter(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.ADD)
                                  .count() == 3,
                              "The count of ADD WlanApGroup operations should be 3") // ADD 5G, lower 5G and upper 5G on wlanApGroup 2
                          .matches(wlanApGroupOps -> (Objects.isNull(payload.getAllApGroupsVlanId())
                                  && StringUtils.isEmpty(payload.getVlanPoolId()))
                                  || wlanApGroupOps.stream()
                                  .filter(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.DELETE)
                                  .count() == 3,
                              "The count of MODIFY WlanApGroup operations should be 3"); // DELETE 2.4G on 3 wlanApGroups
                    } else {
                      alsoSoftly.assertThat(ops)
                          .matches(wlanApGroupOps -> wlanApGroupOps.stream()
                                  .filter(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.ADD)
                                  .count() == 3,
                              "The count of ADD WlanApGroup operations should be 3") // ADD 5G, lower 5G and upper 5G
                          .matches(wlanApGroupOps -> (Objects.isNull(payload.getAllApGroupsVlanId())
                                  && StringUtils.isEmpty(payload.getVlanPoolId()))
                                  || wlanApGroupOps.stream()
                                  .filter(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.MODIFY)
                                  .count() == 1,
                              "The count of MODIFY WlanApGroup operations should be 1"); // MODIFY 2.4G
                    }
                  }))
                  .extracting(Operation::getWlanApGroup)
                  .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                    if (BooleanUtils.isFalse(payload.getIsAllApGroups())) {
                      return;
                    }
                    // isAllApGroups != false
                    if (StringUtils.isEmpty(payload.getVlanPoolId())) {
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                      if (payload.getAllApGroupsVlanId() != null) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanId())
                            .extracting(Int32Value::getValue)
                            .isEqualTo(payload.getAllApGroupsVlanId().intValue());
                      } else {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      }
                      return;
                    }
                    alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                    alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                    alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                        .extracting(StringValue::getValue)
                        .isEqualTo(payload.getVlanPoolId());
                  }))
                  .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                    if (BooleanUtils.isNotFalse(payload.getIsAllApGroups())
                        || CollectionUtils.isEmpty(payload.getApGroups())) {
                      return;
                    }
                    // isAllApGroups == false
                    final var networkApGroupReq = payload.getApGroups().get(0);
                    if (StringUtils.isEmpty(networkApGroupReq.getVlanPoolId())) {
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                      if (networkApGroupReq.getVlanId() != null) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanId())
                            .extracting(Int32Value::getValue)
                            .isEqualTo(networkApGroupReq.getVlanId().intValue());
                      } else {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      }
                      return;
                    }
                    alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                    alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                    alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                        .extracting(StringValue::getValue)
                        .isEqualTo(networkApGroupReq.getVlanPoolId());
                  }));
            })));
  }

  void validateResult(String tenantId, String requestId, String networkVenueId,
      NetworkActivationMapping payload, String apiAction) {
    validateEntity(networkVenueId, payload.getNetworkId(), payload.getVenueId());
    validateWifiCfgChangeMessage(tenantId, requestId, apiAction, payload.getNetworkId(),
        payload.getVenueId());
    validateCmnCfgCollectorMessage(tenantId, requestId, networkVenueId, payload.getNetworkId(),
        payload.getVenueId());
    validateActivityMessages(tenantId, requestId, apiAction);
    validateDdccmCfgRequestMessage(null, tenantId, requestId, networkVenueId, payload);
  }

  void validateResult(TestInfo testInfo, String tenantId, String requestId, String networkVenueId,
      com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload, String apiAction) {
    validateEntity(networkVenueId, payload.getNetworkId(), payload.getVenueId());
    validateWifiCfgChangeMessage(tenantId, requestId, apiAction, payload.getNetworkId(),
        payload.getVenueId());
    validateCmnCfgCollectorMessage(tenantId, requestId, networkVenueId, payload.getNetworkId(),
        payload.getVenueId());
    validateActivityMessages(tenantId, requestId, apiAction);
    validateDdccmCfgRequestMessage(testInfo, tenantId, requestId, networkVenueId, payload);
  }

  private static com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler dummyNetworkVenueScheduler() {
    final var scheduler = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler();
    scheduler.setType(SchedulerTypeEnum.ALWAYS_ON);
    return scheduler;
  }
}
