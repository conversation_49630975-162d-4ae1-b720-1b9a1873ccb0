package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.servicemodel.enums.SortOrderEnum.DESC;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.service.ApplicationPolicyServiceCtrl;
import com.ruckus.cloud.wifi.eda.service.DevicePolicyServiceCtrl;
import com.ruckus.cloud.wifi.eda.service.L2AclPolicyServiceCtrl;
import com.ruckus.cloud.wifi.eda.service.L3AclPolicyServiceCtrl;
import com.ruckus.cloud.wifi.eda.service.NetworkServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfileQueryData;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfileQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.RateLimiting;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.mapper.AccessControlProfileMerge;
import com.ruckus.cloud.wifi.mapper.AccessControlProfileMergeImpl;
import com.ruckus.cloud.wifi.repository.AccessControlProfileRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.WlanRepository;
import com.ruckus.cloud.wifi.service.EntityIdGenerationLockService;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.AccessControlProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.integration.config.NetworkServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.config.MockGrpcBeanConfiguration;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

@Tag("AccessControlProfileTest")
@WifiJpaDataTest
@TestPropertySource(properties = {"default.access-control-profile.tenant-max-count: 10"})
class AccessControlProfileServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @SpyBean
  private AccessControlProfileServiceCtrlImpl accessControlProfileService;
  @Autowired
  private NetworkServiceCtrl networkServiceCtrl;
  @Autowired
  private AccessControlProfileRepository accessControlProfileRepository;
  @Autowired
  private WlanRepository wlanRepository;
  @Autowired
  private NetworkRepository networkRepository;
  @MockBean
  private L3AclPolicyServiceCtrl l3AclPolicyService;
  @MockBean
  private L2AclPolicyServiceCtrl l2AclPolicyService;
  @MockBean
  private ApplicationPolicyServiceCtrl applicationPolicyService;
  @MockBean
  private DevicePolicyServiceCtrl devicePolicyService;
  @MockBean
  private EntityIdGenerationLockService entityIdGenerationLockService;
  @Autowired
  private AccessControlProfileMerge accessControlProfileMerge;
  @Autowired
  private RepositoryUtil repositoryUtil;

  @Test
  void testGetAllAccessControlProfiles(AccessControlProfile accessControlProfile) throws Exception {
    // Then
    assertAccessControlProfile(accessControlProfile,
        accessControlProfileService.getAllAccessControlProfiles().get(0));
  }

  @Test
  void testGetAccessControlProfile(Network network, AccessControlProfile accessControlProfile)
      throws Exception {
    // Given
    AccessControlProfile expected = repositoryUtil.createOrUpdate(accessControlProfile,
        txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    network.getWlan().getAdvancedCustomization().setAccessControlProfile(accessControlProfile);
    network = repositoryUtil.createOrUpdate(network, txCtxExtension.getTenantId(),
        txCtxExtension.newRandomId());
    assertEquals(1, accessControlProfileService.getAllAccessControlProfiles().size());

    // Then
    expected.setNetworks(List.of(network.getId()));
    assertAccessControlProfile(expected,
        accessControlProfileService.getAccessControlProfile(accessControlProfile.getId()));
  }

  @Test
  void testGetAccessControlProfileByQuery_sortNameDesc(AccessControlProfile accessControlProfile) throws Exception {
    // Given
    accessControlProfile.setName("name1");
    repositoryUtil.createOrUpdate(accessControlProfile, accessControlProfile.getTenant().getId(), randomTxId());
    AccessControlProfile profile2 = AccessControlProfileTestFixture.randomAccessControlProfile();
    profile2.setName("name2");
    repositoryUtil.createOrUpdate(profile2, accessControlProfile.getTenant().getId(), randomTxId());

    // When
    QueryRequest queryRequest = new QueryRequest();
    queryRequest.setSortOrder(DESC);
    queryRequest.setSortField("name");
    AccessControlProfileQueryResponse queryResponse = accessControlProfileService.getAccessControlProfileByQuery(
        queryRequest);

    // Then
    assertEquals(2, queryResponse.getTotalCount());
    assertEquals(1, queryResponse.getTotalPages());
    assertEquals(1, queryResponse.getPage());
    assertEquals(2, queryResponse.getData().size());
    assertEquals(List.of("name2", "name1"),
        queryResponse.getData().stream().map(AccessControlProfileQueryData::getName)
            .collect(Collectors.toList()));
  }

  @Test
  void testAddAccessControlProfile(ApplicationPolicy applicationPolicy, DevicePolicy devicePolicy)
      throws Exception {
    // Given
    AccessControlProfile accessControlProfile = AccessControlProfileTestFixture.randomAccessControlProfile();
    accessControlProfile.setApplicationPolicy(applicationPolicy);
    accessControlProfile.setApplicationPolicyEnable(true);
    accessControlProfile.setDevicePolicy(devicePolicy);
    accessControlProfile.setDevicePolicyEnable(false);
    when(applicationPolicyService.getApplicationPolicy(any())).thenReturn(applicationPolicy);
    when(devicePolicyService.getDevicePolicy(any())).thenReturn(devicePolicy);

    // When
    accessControlProfileService.addAccessControlProfile(copyOf(accessControlProfile));

    // Then
    accessControlProfile.setApCfgIndex((short) 0);
    assertAccessControlProfile(accessControlProfile,
        accessControlProfileService.getAccessControlProfile(accessControlProfile.getId()));
  }

  @Test
  void testAddAccessControlProfile_existName(AccessControlProfile accessControlProfile) {
    // When
    assertThrows(InvalidPropertyValueException.class,
        () -> accessControlProfileService.addAccessControlProfile(copyOf(accessControlProfile)));
  }

  @Test
  void testAddAccessControlProfile_reachMaxCount(Tenant tenant) {
    // Given
    for (int i = 0; i < 10; i++) {
      AccessControlProfile accessControlProfile = AccessControlProfileTestFixture.randomAccessControlProfile();
      repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());
    }

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> accessControlProfileService.addAccessControlProfile(
            copyOf(AccessControlProfileTestFixture.randomAccessControlProfile())));
  }

  @Test
  void testUpdateAccessControlProfile(ApplicationPolicy applicationPolicy,
      DevicePolicy devicePolicy) throws Exception {
    // Given
    AccessControlProfile accessControlProfile = AccessControlProfileTestFixture.randomAccessControlProfile();
    accessControlProfile.setApplicationPolicy(applicationPolicy);
    accessControlProfile.setApplicationPolicyEnable(true);
    accessControlProfile = repositoryUtil.createOrUpdate(accessControlProfile,
        txCtxExtension.getTenantId(), randomTxId());
    when(devicePolicyService.getDevicePolicy(devicePolicy.getId())).thenReturn(devicePolicy);

    // When
    accessControlProfile.setApplicationPolicy(null);
    accessControlProfile.setApplicationPolicyEnable(false);
    accessControlProfile.setDevicePolicy(devicePolicy);
    accessControlProfile.setDevicePolicyEnable(true);
    accessControlProfileService.updateAccessControlProfile(accessControlProfile.getId(),
        copyOf(accessControlProfile));

    // Then
    assertAccessControlProfile(accessControlProfile,
        accessControlProfileService.getAccessControlProfile(accessControlProfile.getId()));
  }

  @Test
  void testUpdateAccessControlProfile_existName(AccessControlProfile accessControlProfile) {
    // Given
    AccessControlProfile accessControlProfile1 = AccessControlProfileTestFixture.randomAccessControlProfile();
    accessControlProfile1 = repositoryUtil.createOrUpdate(accessControlProfile1,
        txCtxExtension.getTenantId(), randomTxId());
    AccessControlProfile copied = copyOf(accessControlProfile);
    copied.setName(accessControlProfile1.getName());

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> accessControlProfileService.updateAccessControlProfile(copied.getId(),
            copied));
  }

  @Test
  void testDeleteAccessControlProfile_ruckusOne(AccessControlProfile accessControlProfile)
      throws Exception {
    // When
    accessControlProfileService.deleteAccessControlProfile(accessControlProfile.getId());

    // Then
    assertThrows(ObjectNotFoundException.class,
        () -> accessControlProfileService.getAccessControlProfile(accessControlProfile.getId()));
  }

  @Test
  void testDeleteAccessControlProfile_ruckusOne_networkInUse(Network network,
      AccessControlProfile accessControlProfile) {
    // When
    network.getWlan().getAdvancedCustomization().setAccessControlProfile(accessControlProfile);
    repositoryUtil.createOrUpdate(network, txCtxExtension.getTenantId(), randomTxId());

    // Then
    assertThrows(ObjectInUseException.class,
        () -> accessControlProfileService.deleteAccessControlProfile(accessControlProfile.getId()));
  }

  @Test
  void testActivateAccessControlProfileOnWifiNetwork(Network network,
      AccessControlProfile accessControlProfile) throws Exception {
    // When
    accessControlProfileService.activateAccessControlProfileOnWifiNetwork(network.getId(),
        accessControlProfile.getId());

    // Then
    Network savedNetwork = repositoryUtil.find(Network.class, network.getId());
    assertNotNull(savedNetwork);
    assertNotNull(savedNetwork.getWlan().getAdvancedCustomization().getAccessControlProfile());
    assertTrue(savedNetwork.getWlan().getAdvancedCustomization().getAccessControlEnable());
  }

  @Test
  void testDeactivateAccessControlProfileOnWifiNetwork(Network network,
      AccessControlProfile accessControlProfile) throws Exception {
    // When
    network.getWlan().getAdvancedCustomization().setAccessControlEnable(true);
    network.getWlan().getAdvancedCustomization().setAccessControlProfile(accessControlProfile);
    repositoryUtil.createOrUpdate(network, txCtxExtension.getTenantId(), randomTxId());

    // When
    accessControlProfileService.deactivateAccessControlProfileOnWifiNetwork(network.getId(),
        accessControlProfile.getId());

    // Then
    Network savedNetwork = repositoryUtil.find(Network.class, network.getId());
    assertNotNull(savedNetwork);
    assertNull(savedNetwork.getWlan().getAdvancedCustomization().getAccessControlProfile());
    assertFalse(savedNetwork.getWlan().getAdvancedCustomization().getAccessControlEnable());
  }

  private void assertAccessControlProfile(AccessControlProfile expected,
      AccessControlProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getDescription(), actual.getDescription());
    assertEquals(expected.getApCfgIndex(), actual.getApCfgIndex());
    if (expected.getL2AclPolicy() != null) {
      assertEquals(expected.getL2AclPolicy().getId(), actual.getL2AclPolicy().getId());
    } else {
      assertNull(actual.getL2AclPolicy());
    }
    if (expected.getL3AclPolicy() != null) {
      assertEquals(expected.getL3AclPolicy().getId(), actual.getL3AclPolicy().getId());
    } else {
      assertNull(actual.getL3AclPolicy());
    }
    if (expected.getDevicePolicy() != null) {
      assertEquals(expected.getDevicePolicy().getId(), actual.getDevicePolicy().getId());
    } else {
      assertNull(actual.getDevicePolicy());
    }
    if (expected.getApplicationPolicy() != null) {
      assertEquals(expected.getApplicationPolicy().getId(), actual.getApplicationPolicy().getId());
    } else {
      assertNull(actual.getApplicationPolicy());
    }
    if (expected.getRateLimiting() != null) {
      assertEquals(expected.getRateLimiting().getDownlinkLimit(),
          actual.getRateLimiting().getDownlinkLimit());
      assertEquals(expected.getRateLimiting().getUplinkLimit(),
          actual.getRateLimiting().getUplinkLimit());
      assertEquals(expected.getRateLimiting().getEnabled(), actual.getRateLimiting().getEnabled());
    } else {
      assertNull(actual.getRateLimiting());
    }
    assertEquals(expected.getL2AclEnable(), actual.getL2AclEnable());
    assertEquals(expected.getL3AclEnable(), actual.getL3AclEnable());
    assertEquals(expected.getDevicePolicyEnable(), actual.getDevicePolicyEnable());
    assertEquals(expected.getApplicationPolicyEnable(), actual.getApplicationPolicyEnable());

    if (expected.getNetworks() != null) {
      assertThat(actual.getNetworks()).hasSize(expected.getNetworks().size())
          .containsAll(expected.getNetworks());
    } else {
      actual.getNetworks();
    }
  }

  private AccessControlProfile copyOf(AccessControlProfile accessControlProfile) {
    AccessControlProfile copied = new AccessControlProfile();
    copied.setId(accessControlProfile.getId());
    copied.setName(accessControlProfile.getName());
    copied.setApCfgIndex(accessControlProfile.getApCfgIndex());
    copied.setDescription(accessControlProfile.getDescription());
    if (accessControlProfile.getL2AclPolicy() != null) {
      copied.setL2AclPolicy(new L2AclPolicy(accessControlProfile.getL2AclPolicy().getId()));
    }
    if (accessControlProfile.getL3AclPolicy() != null) {
      copied.setL3AclPolicy(new L3AclPolicy(accessControlProfile.getL3AclPolicy().getId()));
    }
    if (accessControlProfile.getDevicePolicy() != null) {
      copied.setDevicePolicy(new DevicePolicy(accessControlProfile.getDevicePolicy().getId()));
    }
    if (accessControlProfile.getApplicationPolicy() != null) {
      copied.setApplicationPolicy(
          new ApplicationPolicy(accessControlProfile.getApplicationPolicy().getId()));
    }
    copied.setL2AclEnable(accessControlProfile.getL2AclEnable());
    copied.setL3AclEnable(accessControlProfile.getL3AclEnable());
    copied.setDevicePolicyEnable(accessControlProfile.getDevicePolicyEnable());
    copied.setApplicationPolicyEnable(accessControlProfile.getApplicationPolicyEnable());
    if (accessControlProfile.getRateLimiting() != null) {
      RateLimiting rateLimiting = new RateLimiting();
      rateLimiting.setDownlinkLimit(accessControlProfile.getRateLimiting().getDownlinkLimit());
      rateLimiting.setUplinkLimit(accessControlProfile.getRateLimiting().getUplinkLimit());
      rateLimiting.setEnabled(accessControlProfile.getRateLimiting().getEnabled());
      copied.setRateLimiting(rateLimiting);
    }
    return copied;
  }

  @TestConfiguration
  @Import({
      MockGrpcBeanConfiguration.class,
      NetworkServiceCtrlImplTestConfig.class
  })
  static class Config {

    @Bean
    public AccessControlProfileMerge accessControlProfileMerge() {
      return new AccessControlProfileMergeImpl();
    }
  }

}
