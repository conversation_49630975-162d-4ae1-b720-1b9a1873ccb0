package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.groups.Tuple.tuple;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPortProfile;
import com.ruckus.cloud.wifi.hibernate.dto.ApActivationDto;
import com.ruckus.cloud.wifi.servicemodel.projection.ApLanPortProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("EthernetPortProfileTest")
@WifiJpaDataTest
@Sql(statements = """    
    INSERT INTO tenant (id) VALUES ('tenant00');

    INSERT INTO ap_lan_port_profile (category, id, tenant) VALUES
      ('ETHERNET', 'apLanPortProfile01', 'tenant00'),
      ('ETHERNET', 'apLanPortProfile02', 'tenant00'),
      ('ETHERNET', 'apLanPortProfile03', 'tenant00');
      
    INSERT INTO lan_port_adoption (id, checksum, ap_lan_port_profile, tenant) VALUES
      ('lanPortAdoptionId01', 'checksum1', 'apLanPortProfile01', 'tenant00'),
      ('lanPortAdoptionId02', 'checksum2', 'apLanPortProfile02', 'tenant00');   

   INSERT INTO venue (id, tenant)
      VALUES ('venue01', 'tenant00');
    INSERT INTO ap_group (id, venue, tenant)
      VALUES ('apg01', 'venue01', 'tenant00');
    INSERT INTO ap_model_specific (id, tenant)
      VALUES ('modelSpecific01', 'tenant00');
    INSERT INTO ap (id, name, ap_group, tenant, model, enable50g, soft_deleted, model_specific)
        VALUES ('900000008880', 'My AP 80', 'apg01', 'tenant00', 'R710', true, false, 'modelSpecific01');
    INSERT INTO ap_lan_port (id, port_id, model_specific, ap_lan_port_profile, lan_port_adoption, tenant)
      VALUES ('apLanPort01', '1', 'modelSpecific01', 'apLanPortProfile01', 'lanPortAdoptionId01', 'tenant00'),
             ('apLanPort01-1', '2', 'modelSpecific01', 'apLanPortProfile01', null, 'tenant00');

   INSERT INTO venue (id, tenant)
      VALUES ('venue02', 'tenant00');
    INSERT INTO ap_group (id, venue, tenant)
      VALUES ('apg02', 'venue02', 'tenant00');
    INSERT INTO ap_model_specific (id, tenant)
      VALUES ('modelSpecific02', 'tenant00');
    INSERT INTO ap (id, name, ap_group, floorplan_id, tenant, soft_deleted, model_specific)
        VALUES ('900000005015', 'My AP 15', 'apg02', '5e180d7fd19d4dc1a6a768aa2255a23d', 'tenant00', false, 'modelSpecific02');
    INSERT INTO ap_lan_port (id, port_id, model_specific, ap_lan_port_profile, lan_port_adoption, tenant)
      VALUES ('apLanPort02', '2', 'modelSpecific02', 'apLanPortProfile01', 'lanPortAdoptionId01', 'tenant00');

   INSERT INTO venue (id, tenant)
      VALUES ('venue03', 'tenant00');
    INSERT INTO ap_group (id, venue, tenant)
      VALUES ('apg03', 'venue03', 'tenant00');
    INSERT INTO ap_model_specific (id, tenant)
      VALUES ('modelSpecific03', 'tenant00');
    INSERT INTO ap (id, name, ap_group, floorplan_id, tenant, soft_deleted, model_specific)
        VALUES ('900000005018', 'My AP 15', 'apg03', '5e180d7fd19d4dc1a6a768aa2255a23d', 'tenant00', false, 'modelSpecific03');
    INSERT INTO ap_lan_port (id, port_id, model_specific, ap_lan_port_profile, lan_port_adoption, tenant)
      VALUES
       ('apLanPort03', '3', 'modelSpecific03', 'apLanPortProfile02', 'lanPortAdoptionId02', 'tenant00'),
       ('apLanPort04', '4', 'modelSpecific03', 'apLanPortProfile01', 'lanPortAdoptionId01', 'tenant00');

   INSERT INTO venue (id, tenant)
      VALUES ('venue04', 'tenant00');
    INSERT INTO ap_group (id, venue, tenant)
      VALUES ('apg04', 'venue04', 'tenant00');
    INSERT INTO ap_model_specific (id, tenant)
      VALUES ('modelSpecific04', 'tenant00');
    INSERT INTO ap (id, name, ap_group, floorplan_id, tenant, soft_deleted, model_specific)
        VALUES ('900000005418', 'My AP 16', 'apg04', '5e180d7fd19d4dc1a6a768aa2255a23d', 'tenant00', false, 'modelSpecific04');
    INSERT INTO ap_lan_port (id, port_id, model_specific, ap_lan_port_profile, lan_port_adoption, tenant)
      VALUES
       ('apLanPort05', '5', 'modelSpecific04', 'apLanPortProfile03', 'lanPortAdoptionId02', 'tenant00'),
       ('apLanPort06', '6', 'modelSpecific04', 'apLanPortProfile01', 'lanPortAdoptionId01', 'tenant00');

    INSERT INTO client_isolation_allowlist (id, tenant) VALUES ('clientIsolationAllowlist01', 'tenant00');
    INSERT INTO client_isolation_lan_port_activation (id, client_isolation_allowlist, lan_port_adoption, tenant)
      VALUES ('clientIsolationLanPortActivation01', 'clientIsolationAllowlist01', 'lanPortAdoptionId01', 'tenant00'),
             ('clientIsolationLanPortActivation02', 'clientIsolationAllowlist01', 'lanPortAdoptionId02', 'tenant00');
    
    INSERT INTO soft_gre_profile (id, tenant)
       VALUES ('softGreProfileId01', 'tenant00'),
              ('softGreProfileId02', 'tenant00');
    INSERT INTO ipsec_profile (id, tenant)
       VALUES ('ipsecProfileId01', 'tenant00'),
              ('ipsecProfileId02', 'tenant00');
    INSERT INTO soft_gre_profile_lan_port_activation (id, soft_gre_profile, ipsec_profile, lan_port_adoption, tenant)
       VALUES ('softGreProfileLanPortActivation01', 'softGreProfileId01', 'ipsecProfileId01', 'lanPortAdoptionId01', 'tenant00'),
              ('softGreProfileLanPortActivation02', 'softGreProfileId02', 'ipsecProfileId02', 'lanPortAdoptionId02', 'tenant00');
    """)
class ApLanPortRepositoryTest {

  @Autowired
  private ApLanPortRepository apLanPortRepository;

  @Test
  void findByTenantIdAndApLanPortProfileIdIn() {
    assertThat(apLanPortRepository.findByTenantIdAndApLanPortProfileIdIn("tenant00",
            List.of("apLanPortProfile01", "apLanPortProfile02")).stream()
        .map(ApLanPort::getId)
        .collect(Collectors.toList()))
        .contains("apLanPort01", "apLanPort02", "apLanPort03");

    assertThat(apLanPortRepository.findByTenantIdAndApLanPortProfileIdIn("tenant00",
            List.of("apLanPortProfile01")).stream()
        .map(ApLanPort::getId)
        .collect(Collectors.toList()))
        .contains("apLanPort01", "apLanPort02");
  }

  @Test
  void findByApLanPortProfileId() {
    assertThat(apLanPortRepository.findByTenantIdAndApLanPortProfileId("tenant00", "apLanPortProfile01").stream()
        .map(ApLanPort::getId)
        .collect(Collectors.toList()))
        .contains("apLanPort01", "apLanPort02");
  }

  @Test
  void findVenueIdByApLanPortProfileId() {

    assertThat(
        apLanPortRepository.findVenueIdsByTenantIdAndApLanPortProfileId("tenant00", "apLanPortProfile01"))
        .contains("venue01", "venue02");

    assertEquals(List.of("venue03"),
        apLanPortRepository.findVenueIdsByTenantIdAndApLanPortProfileId("tenant00", "apLanPortProfile02")
    );
  }

  @Test
  void findAllPortsInVenueWhichHasNoLanPortAdoptionTest() {
    assertThat(
            apLanPortRepository
                .findAllPortsInVenueWhichHasNoLanPortAdoption("tenant00", "venue01")
                .stream()
                .map(ApLanPort::getId)
                .toList())
        .hasSize(1)
        .contains("apLanPort01-1");
  }

  @Test
  void findAllPortsInVenueWhichIdNotAndHasNoLanPortAdoptionTest() {
    assertThat(
            apLanPortRepository
                .findAllPortsInVenueWhichIdNotAndHasNoLanPortAdoption(
                    "tenant00", "venue01", "apLanPort01")
                .stream()
                .map(ApLanPort::getId)
                .toList())
        .hasSize(1)
        .contains("apLanPort01-1");

    assertThat(
            apLanPortRepository
                .findAllPortsInVenueWhichIdNotAndHasNoLanPortAdoption(
                    "tenant00", "venue01", "apLanPort01-1")
                .stream()
                .map(ApLanPort::getId)
                .toList())
        .hasSize(0);
  }

  @Test
  void test_findApLanPortProfileIdsByTenantIdAndModelSpecificApId() {
    assertThat(
        apLanPortRepository.findApLanPortProfileIdsByTenantIdAndModelSpecificApId("tenant00", "900000008880")
            .stream())
        .hasSize(1)
        .contains("apLanPortProfile01");

    assertThat(
        apLanPortRepository.findApLanPortProfileIdsByTenantIdAndModelSpecificApId("tenant00", "900000005015")
            .stream())
        .hasSize(1)
        .contains("apLanPortProfile01");

    assertThat(
        apLanPortRepository.findApLanPortProfileIdsByTenantIdAndModelSpecificApId("tenant00", "900000005018")
            .stream())
        .hasSize(2)
        .contains("apLanPortProfile01", "apLanPortProfile02");
  }

  @Test
  void test_findApLanPortProfilesByTenantIdAndModelSpecificApId() {
    assertThat(
        apLanPortRepository.findApLanPortProfilesByTenantIdAndModelSpecificApId("tenant00", "900000008880")
            .stream())
        .hasSize(1)
        .map(ApLanPortProfile::getId)
        .contains("apLanPortProfile01");

    assertThat(
        apLanPortRepository.findApLanPortProfilesByTenantIdAndModelSpecificApId("tenant00", "900000005015")
            .stream())
        .hasSize(1)
        .map(ApLanPortProfile::getId)
        .contains("apLanPortProfile01");

    assertThat(
        apLanPortRepository.findApLanPortProfilesByTenantIdAndModelSpecificApId("tenant00", "900000005018")
            .stream())
        .hasSize(2)
        .map(ApLanPortProfile::getId)
        .contains("apLanPortProfile01", "apLanPortProfile02");
  }

  @Test
  void test_findApLanPortProfilesByTenantIdAndModelSpecificApIdIn() {
    assertThat(
        apLanPortRepository.findApLanPortProfilesByTenantIdAndModelSpecificApIdIn("tenant00",
                List.of("900000008880", "900000005015"))
            .stream())
        .hasSize(1)
        .map(ApLanPortProfile::getId)
        .contains("apLanPortProfile01");

    assertThat(
        apLanPortRepository.findApLanPortProfilesByTenantIdAndModelSpecificApIdIn("tenant00",
                List.of("900000008880", "900000005418"))
            .stream())
        .hasSize(2)
        .map(ApLanPortProfile::getId)
        .contains("apLanPortProfile01", "apLanPortProfile03");

    assertThat(
        apLanPortRepository.findApLanPortProfilesByTenantIdAndModelSpecificApIdIn("tenant00",
                List.of("900000005018"))
            .stream())
        .hasSize(2)
        .map(ApLanPortProfile::getId)
        .contains("apLanPortProfile01", "apLanPortProfile02");
  }

  @Test
  void findByLanPortAdoptionId() {
    assertThat(
        apLanPortRepository.findByTenantIdAndLanPortAdoptionIdIn("tenant00", List.of("lanPortAdoptionId01"))
            .stream()
            .map(ApLanPort::getId)
            .collect(Collectors.toList()))
        .contains("apLanPort01", "apLanPort02");
  }

  @Test
  void findVenueIdApSerialPortIdByApLanPorts() {
    var apLanPortProjections = apLanPortRepository.findVenueIdApSerialPortIdByApLanPorts(
        List.of("apLanPort01", "apLanPort02"));
    assertThat(apLanPortProjections)
        .isNotNull()
        .hasSize(2)
        .extracting(ApLanPortProjection::venueId, ApLanPortProjection::apSerialNumber,
            ApLanPortProjection::portId)
        .containsExactlyInAnyOrder(
            tuple("venue01", "900000008880", "1"),
            tuple("venue02", "900000005015", "2")
        );
  }

  @Test
  void findVenueIdModelPortIdApGroupsByClientIsolationAllowlist() {
    var venueLanPortProjections =
        apLanPortRepository.findApLanPortProjectionsWhichActivatedClientIsolationAllowlist(
            "tenant00", "clientIsolationAllowlist01");
    assertThat(venueLanPortProjections)
        .isNotNull()
        .hasSize(6)
        .extracting(ApLanPortProjection::venueId, ApLanPortProjection::apSerialNumber,
            ApLanPortProjection::portId)
        .containsExactlyInAnyOrder(
            tuple("venue01", "900000008880", "1"),
            tuple("venue02", "900000005015", "2"),
            tuple("venue03", "900000005018", "3"),
            tuple("venue03", "900000005018", "4"),
            tuple("venue04", "900000005418", "5"),
            tuple("venue04", "900000005418", "6")
        );
  }

  @Test
  void findBySerialNumbersIn() {
    var apSerialNumbers = apLanPortRepository.findBySerialNumbersIn(
        List.of("900000005015", "900000005018", "123412341234"));
    // AssertJ assertions for list content
    assertThat(apSerialNumbers)
        .isNotNull()
        .contains("900000005015", "900000005018");
  }

  @Test
  void findApActivationsTest() {
    var apActivationDtoList = apLanPortRepository.findApActivations("tenant00", "apLanPortProfile01");
    assertThat(apActivationDtoList)
        .isNotNull()
        .hasSize(5)
        .extracting(ApActivationDto::venueId, ApActivationDto::apSerialNumber,
            ApActivationDto::portId)
        .containsExactlyInAnyOrder(
            tuple("venue01", "900000008880", "1"),
            tuple("venue01", "900000008880", "2"),
            tuple("venue02", "900000005015", "2"),
            tuple("venue03", "900000005018", "4"),
            tuple("venue04", "900000005418", "6"));
  }

  @Test
  void findAllPortsInAllVenuesWhichActivatedTargetSoftGreProfileTest() {
    var ports =
        apLanPortRepository.findAllPortsInAllVenuesWhichActivatedTargetSoftGreProfile(
            "tenant00", "softGreProfileId01");
    // softGreProfileId01 is activated in lanPortAdoptionId01
    // lanPortAdoptionId01 is used by apLanPort01[venue01], apLanPort02[venue2], and
    // apLanPort04[venue3]
    assertEquals(4, ports.size());
    assertThat(ports).filteredOn(p -> "apLanPort01".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "apLanPort02".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "apLanPort04".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "apLanPort06".equals(p.getId())).hasSize(1);
  }

  @Test
  void findAllPortsInAllVenuesWhichActivatedTargetIpsecProfileTest() {
    var ports =
        apLanPortRepository.findAllPortsInAllVenuesWhichActivatedTargetIpsecProfile(
            "tenant00", "ipsecProfileId01");
    assertEquals(4, ports.size());
    assertThat(ports).filteredOn(p -> "apLanPort01".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "apLanPort02".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "apLanPort04".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "apLanPort06".equals(p.getId())).hasSize(1);

    ports =
        apLanPortRepository.findAllPortsInAllVenuesWhichActivatedTargetIpsecProfile(
            "tenant00", "non-exist-id");
    assertEquals(0, ports.size());
  }

  @Test
  void findOtherActivatedPortsInAllVenuesWhichActivatedTargetSoftGreProfileTest() {
    var ports =
        apLanPortRepository.findOtherActivatedPortsInAllVenuesWhichActivatedTargetSoftGreProfile(
            "tenant00", "softGreProfileId01");
    // softGreProfileId01 is activated in lanPortAdoptionId01
    // lanPortAdoptionId01 is used by apLanPort01[venue01], apLanPort02[venue02], and
    // apLanPort04[venue03] these should not be found
    // softGreProfileId02 is activated in lanPortAdoptionId02
    // lanPortAdoptionId02 is used by apLanPort03[venue03]
    // venue3 is included in the activated ports' venue, should be finding out.
    assertEquals(2, ports.size());
    assertThat(ports).filteredOn(p -> "apLanPort03".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "apLanPort05".equals(p.getId())).hasSize(1);
  }
}