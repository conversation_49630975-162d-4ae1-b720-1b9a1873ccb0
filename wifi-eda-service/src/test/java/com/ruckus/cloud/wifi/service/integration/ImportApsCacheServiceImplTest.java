package com.ruckus.cloud.wifi.service.integration;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.redis.service.RedisClientService;
import com.ruckus.cloud.wifi.redis.service.RedisLockTemplate;
import com.ruckus.cloud.wifi.service.ImportApsCacheService;
import com.ruckus.cloud.wifi.service.core.model.AccountVertical;
import com.ruckus.cloud.wifi.service.core.model.ImportDetails;
import com.ruckus.cloud.wifi.service.core.model.ImportError;
import com.ruckus.cloud.wifi.service.core.util.ImportConfig;
import com.ruckus.cloud.wifi.service.impl.ImportApsCacheServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.TestContainerRedisConfig;
import com.ruckus.cloud.wifi.servicemodel.ImportApsCsv;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.RedisTestContainer;
import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisServerCommands;
import org.springframework.data.redis.core.RedisTemplate;

@RedisTestContainer
@WifiUnitTest
public class ImportApsCacheServiceImplTest {

  private static final String TENANT_ID = "abc";

  private static final String REQUEST_ID = "123";

  @Autowired
  private ImportApsCacheService importApsCacheService;

  @Autowired
  private RedisTemplate<?, ?> redisTemplate;

  @BeforeEach
  private void redisFlushAll() {
    Optional.ofNullable(redisTemplate.getConnectionFactory())
        .map(RedisConnectionFactory::getConnection)
        .ifPresent(RedisServerCommands::flushAll);
  }

  @Test
  public void testIsImportApsTask_defaultValue() {
    assertFalse(importApsCacheService.isImportApsTask(REQUEST_ID));
  }

  @Test
  public void testGetImportApsCsv_notSet() {
    assertNull(importApsCacheService.getImportApsCsv(REQUEST_ID));
  }

  @Test
  public void testGetImportDetails_notSet() {
    assertThatNoException().isThrownBy(() ->
        assertNull(importApsCacheService.getImportDetailsByRequestId(REQUEST_ID)));
  }

  @Test
  public void testGetEntitlementDone_notSet() {
    assertFalse(importApsCacheService.getEntitlementDone(REQUEST_ID));
  }

  @Test
  public void testGetDrsBrokerDone_notSet() {
    assertFalse(importApsCacheService.getDrsBrokerDone(REQUEST_ID));
  }

  @Test
  public void testGetImportApsDone_notSet() {
    assertFalse(importApsCacheService.getImportApsDone(REQUEST_ID));
  }

  @Test
  public void testUpdateAndGetImportApsCsv() {
    ImportApsCsv expectedImportApsCsv = generateImportApsCsv();

    assertThatNoException().isThrownBy(() ->
        importApsCacheService.updateImportApsCsv(REQUEST_ID, expectedImportApsCsv));

    ImportApsCsv actualImportApCsv = importApsCacheService.getImportApsCsv(REQUEST_ID);

    assertEquals(expectedImportApsCsv.getFileName(), actualImportApCsv.getFileName());
    assertEquals(expectedImportApsCsv.getFileDownloadUri(), actualImportApCsv.getFileDownloadUri());
    assertEquals(expectedImportApsCsv.getHeaders(), actualImportApCsv.getHeaders());
    assertEquals(expectedImportApsCsv.getImportRowStatus().size(),
        actualImportApCsv.getImportRowStatus().size());
    assertEquals(expectedImportApsCsv.getImportErrors().size(),
        actualImportApCsv.getImportErrors().size());
    assertEquals(actualImportApCsv.getImportRowStatus().size(),
        actualImportApCsv.getImportErrors().size());
  }

  @Test
  public void testUpdateDrsImportErrorsByRequestId() {
    Map<Integer, StringBuilder> expectedRowErrors = generateDrsImportRowErrors();

    assertThatNoException().isThrownBy(() ->
        importApsCacheService.updateDrsImportErrorsByRequestId(REQUEST_ID, expectedRowErrors));

    AtomicReference<Map<Integer, StringBuilder>> actualRowErrors = new AtomicReference<>();

    assertThatNoException().isThrownBy(() ->
        actualRowErrors.set(importApsCacheService.getDrsImportRowErrorsByRequestId(REQUEST_ID)));

    assertEquals(2, actualRowErrors.get().size());
    assertTrue(actualRowErrors.get().containsKey(2));
    assertTrue(actualRowErrors.get().containsKey(10));
  }

  @Test
  public void testUpdateLicenseImportErrorsByRequestId() {
    Map<Integer, StringBuilder> expectedRowErrors = generateLicenseImportRowErrors();

    assertThatNoException().isThrownBy(() ->
        importApsCacheService.updateLicenseImportErrorsByRequestId(REQUEST_ID, expectedRowErrors));

    AtomicReference<Map<Integer, StringBuilder>> actualRowErrors = new AtomicReference<>();
    assertThatNoException().isThrownBy(() ->
        actualRowErrors.set(
            importApsCacheService.getLicenseImportRowErrorsByRequestId(REQUEST_ID)));

    assertEquals(1, actualRowErrors.get().size());
    assertTrue(actualRowErrors.get().containsKey(1));
    assertEquals(expectedRowErrors.get(1).toString(), actualRowErrors.get().get(1).toString());
  }

  @Test
  public void testUpdateDrsBrokerDone() {
    importApsCacheService.updateDrsBrokerDone(REQUEST_ID);

    assertTrue(importApsCacheService.getDrsBrokerDone(REQUEST_ID));
  }

  @Test
  public void testUpdateEntitlementDone() {
    importApsCacheService.updateEntitlementDone(REQUEST_ID);

    assertTrue(importApsCacheService.getEntitlementDone(REQUEST_ID));
  }

  @Test
  public void testUpdateImportApsDone() {
    importApsCacheService.updateImportApsDone(REQUEST_ID);

    assertTrue(importApsCacheService.getImportApsDone(REQUEST_ID));
  }

  @Test
  public void testUpdateImportDetails() {
    ImportDetails expectedDetails = generateImportDetails(REQUEST_ID);

    importApsCacheService.updateImportDetails(REQUEST_ID, expectedDetails);
    AtomicReference<ImportDetails> actualDetails = new AtomicReference<>();
    assertThatNoException().isThrownBy(() ->
        actualDetails.set(importApsCacheService.getImportDetailsByRequestId(REQUEST_ID)));

    assertEquals(expectedDetails.getTxId(), actualDetails.get().getTxId());
    assertEquals(expectedDetails.getDownloadUrl(), actualDetails.get().getDownloadUrl());
    assertEquals(expectedDetails.getErrors().get(0).getCode(),
        actualDetails.get().getErrors().get(0).getCode());
    assertEquals(expectedDetails.getErrorStatusCode(), actualDetails.get().getErrorStatusCode());
    assertEquals(expectedDetails.getFileErrorsCount(), actualDetails.get().getFileErrorsCount());
  }

  @Test
  public void testUpdateImportDetailsFailedByImportApsCsv() {
    ImportApsCsv importApsCsv = generateImportApsCsvWithImportExceptions();
    importApsCacheService.updateImportApsCsv(REQUEST_ID, importApsCsv);
    importApsCacheService.updateImportDetailsFailByImportApsCsv(REQUEST_ID, importApsCsv);

    ImportApsCsv actualImportApsCsv = importApsCacheService.getImportApsCsv(REQUEST_ID);
    AtomicReference<ImportDetails> actualDetails = new AtomicReference<>();
    assertThatNoException().isThrownBy(() ->
        actualDetails.set(importApsCacheService.getImportDetailsByRequestId(REQUEST_ID)));

    assertNotNull(actualImportApsCsv);
    assertEquals(importApsCsv.getErrorCount(),
        actualImportApsCsv.getImportExceptionMessages().size());
    assertEquals(importApsCsv.getErrorCount(), actualDetails.get().getFileErrorsCount());
    assertEquals(importApsCsv.getErrorCount(), actualDetails.get().getErrors().size());
  }

  private ImportDetails generateImportDetails(String requestId) {
    ImportDetails importDetails = new ImportDetails(
        requestId,
        "http://www.download.url",
        Arrays.asList(new ImportError(
            ImportConfig.LICENSES_NUMBER_ERROR,
            String.format(ImportConfig.TOTAL_REMAINING_DEVICES_MESSAGE_FORMAT, 1))),
        1);

    return importDetails;
  }

  private Map<Integer, StringBuilder> generateLicenseImportRowErrors() {
    String message = String.format(ImportConfig.TOTAL_REMAINING_DEVICES_MESSAGE_FORMAT,
        10);
    Map<Integer, StringBuilder> errors = new HashMap<>();
    errors.put(1, new StringBuilder(message));

    return errors;
  }

  private Map<Integer, StringBuilder> generateDrsImportRowErrors() {
    Map<Integer, StringBuilder> errors = new HashMap<>();
    errors.put(2, new StringBuilder(ImportConfig.SERIAL_NUMBER_ALREADY_EXIST));
    errors.put(10,
        new StringBuilder(ImportConfig.SERIAL_NUMBER_ALREADY_EXIST_IN_DIFFERENT_ACCOUNT));

    return errors;
  }

  private ImportApsCsv generateImportApsCsv() {
    File csvFile = new File("./src/test/resources/csv/aps_import.csv");
    String fileId = "fileId";
    String fileName = "aps_import.csv";
    String downloadUrl = "http://import_csv.download.url";
    AtomicReference<ImportApsCsv> importApsCsv = new AtomicReference<>();
    assertThatNoException().isThrownBy(() ->
        importApsCsv.set(new ImportApsCsv(
            TENANT_ID,
            REQUEST_ID,
            fileId,
            fileName,
            downloadUrl,
            csvFile,
            AccountVertical.DEFAULT)));

    importApsCsv.get().addImportRowErrorByApAndErrorMessage(new Ap("************"),
        ImportConfig.NONE_UNIQUE_AP_NAME_ERROR);
    importApsCsv.get().addImportRowErrorByApAndErrorMessage(new Ap("************"),
        ImportConfig.NONE_UNIQUE_AP_NAME_ERROR);
    importApsCsv.get().addImportRowErrorByApAndErrorMessage(new Ap("************"),
        ImportConfig.NONE_UNIQUE_AP_NAME_ERROR);
    importApsCsv.get().setVenueIdByNameMap(null);
    importApsCsv.get().setApGroupsByVenueIdMap(null);
    return importApsCsv.get();
  }

  private ImportApsCsv generateImportApsCsvWithImportExceptions() {
    File csvFile = new File("./src/test/resources/csv/aps_import.csv");
    String fileId = "fileId";
    String fileName = "aps_import.csv";
    String downloadUrl = "http://import_csv.download.url";
    AtomicReference<ImportApsCsv> importApsCsv = new AtomicReference<>();
    assertThatNoException().isThrownBy(() ->
        importApsCsv.set(new ImportApsCsv(
            TENANT_ID,
            REQUEST_ID,
            fileId,
            fileName,
            downloadUrl,
            csvFile,
            AccountVertical.DEFAULT)));

    List<Integer> rowNumbers = Arrays.asList(11, 12, 13, 14, 15, 16);
    for (Integer rowNumber : rowNumbers) {
      importApsCsv.get()
          .updateImportRowStatus(rowNumber, ImportConfig.AP_NAME_ALREADY_EXISTS_TENANT);
      importApsCsv.get().addImportException(ImportConfig.VALIDATION_ERROR_CODE,
          String.format(ImportConfig.ERROR_WITH_ROW, ImportConfig.AP_NAME_ALREADY_EXISTS_TENANT,
              rowNumber));
    }
    return importApsCsv.get();
  }

  @TestConfiguration
  @Import(TestContainerRedisConfig.class)
  static class TestConfig {

    @Bean
    public ImportApsCacheService importApsCacheService(RedisClientService redisClientService,
        RedisLockTemplate redisLockTemplate,
        @Qualifier("serviceRedissonClient") RedissonClient redissonClient) {
      return new ImportApsCacheServiceImpl(redisClientService, redisLockTemplate, redissonClient);
    }
  }
}
