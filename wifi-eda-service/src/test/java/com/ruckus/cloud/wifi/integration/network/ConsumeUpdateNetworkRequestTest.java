package com.ruckus.cloud.wifi.integration.network;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.HOST_APPROVAL_EMAIL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.google.protobuf.BoolValue;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.wifi.AaaType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanSecurity;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.RadiusRestCtrl;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractWlan;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MacAuthMacFormatEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.AAAWlan;
import com.ruckus.cloud.wifi.eda.viewmodel.MacAddressAuthenticationConfiguration;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.viewmodel.PskWlanSecurityEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenuePortalTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.kafka.common.header.Header;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
class ConsumeUpdateNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  
  @Nested
  class whenConsumeUpdateNetworkRequest {

    private final static String networkName = "networkName_update";
    private final static String ssid = "ssid_update";

    private String networkId;
    private Short vlanId;

    @Payload("HostApproval")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadClickthrough() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
            n.setName("networkName_update");
            n.getWlan().setSsid("ssid_update");
          });
      return networkRequest;
    }

    @Payload("HostApprovalWithEmail")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadHostApprovalWithEmail() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
            n.setName("networkName_update");
            n.getWlan().setSsid("ssid_update");
            n.getGuestPortal().getHostGuestConfig().setHostEmails(List.of(HOST_APPROVAL_EMAIL));
          });
      return networkRequest;
    }

    @BeforeEach
    void givenOneGuestNetworkPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant, final Venue venue) {
      final GuestNetwork guestNetwork = (GuestNetwork) network(GuestNetwork.class).generate();
      final GuestPortal guestPortal = guestPortal(GuestNetworkTypeEnum.HostApproval).generate();
      guestNetwork.setPortalServiceProfileId(randomId());
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
          .setNetwork(always(guestNetwork)).setVenue(always(venue)).generate();
      guestNetwork.setNetworkVenues(List.of(networkVenue));
      final var venuePortal = VenuePortalTestFixture.randomVenuePortal(tenant, vportal -> {
        vportal.setNetworkVenue(networkVenue);
        vportal.setNetworkPortal(guestPortal);
      });
      networkVenue.setVenuePortal(venuePortal);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      networkId = guestNetwork.getId();
      vlanId = guestNetwork.getWlan().getVlanId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("HostApproval"))
    void thenUpdatedGuestNetworkClickThroughWithPortalServiceProfile(TxCtx txCtx)
        throws InvalidProtocolBufferException {
      validateWifiCfgChangeMessage(txCtx);
      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);
      validGuestNetwork();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("HostApprovalWithEmail"))
    void thenUpdatedGuestNetworkHostApprovalWithEmail(TxCtx txCtx)
        throws InvalidProtocolBufferException {
      validateWifiCfgChangeMessage(txCtx);
      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);
      validGuestNetwork();
      validHostGuestConfig();
    }

    private void validateWifiCfgChangeMessage(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
              .getValue(txCtx);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();

      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestNetwork()).findFirst().get()
                  .getGuestNetwork())
          .matches(n -> n.getId().equals(StringValue.of(networkId)));

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> Objects.nonNull(gp.getId()));
    }

    private void validateWifiConfigChangeAndNetwork(WifiConfigChange wifiConfigChange,
        TxCtx txCtx) {
      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestNetwork()).findFirst().get()
                  .getGuestNetwork())
          .matches(n -> n.getId().equals(StringValue.of(networkId)))
          .matches(n -> n.getName().equals(StringValue.of(networkName)));
    }

    private void validateCmnCfgCollectorMessage(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(txCtx);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.MOD)
          .matches(p -> p.getId().equals(networkId));
    }

    private void validateActivityImpactedMessage(TxCtx txCtx) {
      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
              .getValue(txCtx);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());

      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
    }

    private void validateActivityMessages(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
              .getValue(txCtx);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.UPDATE_NETWORK))
          .extracting(ConfigurationStatus::getEventDate)
          .isNotNull();
    }

    private void validGuestNetwork() {
      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, networkId);
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkId))
          .matches(n -> n.getName().equals(networkName))
          .matches(n -> n.getType().equals(NetworkTypeEnum.GUEST))
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(ssid))
          .matches(wlan -> wlan.getVlanId().equals(vlanId));
    }

    private void validHostGuestConfig() {
      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, networkId);
      assertThat(network)
          .isNotNull()
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
          .isNotNull()
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal::getHostGuestConfig)
          .isNotNull()
          .matches(hostGuestConfig -> hostGuestConfig.getHostEmails().equals(List.of(HOST_APPROVAL_EMAIL)));
    }
  }

  @Test
  void updateOpenNetworkWithMacRegistrationId(Tenant tenant, Venue venue) throws IOException {
    var network = NetworkTestFixture.randomOpenNetwork(tenant, n -> {
      n.getWlan().setMacRegistrationListId(randomId());
      n.getWlan().setMacAddressAuthentication(true);
    });
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    NetworkVenue networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    String macRegistrationId = randomId();
    OpenNetwork openNetwork = Generators.openNetwork().generate();
    openNetwork.getWlan().setMacRegistrationListId(macRegistrationId);
    openNetwork.getWlan().setMacAddressAuthentication(true);
    openNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("networkId", network.getId()),
        openNetwork);

    // assert db
    final var savedNetwork = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork::getWlan)
        .isNotNull()
        .matches(Wlan::getMacAddressAuthentication)
        .matches(wlan -> wlan.getMacRegistrationListId().equals(macRegistrationId));

    // assert ddccm
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
            WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue).first().matches(operation ->
            operation.getWlanVenue().getAuthenticationServerId()
                .equals(tenant.getId() + "-Auth-Radius-AAA") && operation.getWlanVenue()
                .getAuthAaa().getThroughController() && operation.getWlanVenue().getAuthAaa()
                .getType().equals(AaaType.RADIUS));

    // assert wifiCfgChange
    final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenant.getId(), requestId);
    WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
    assertThat(wifiConfigChange.getOperationList()).filteredOn(
            com.ruckus.cloud.wifi.proto.Operation::hasWlan)
        .first().matches(operation -> operation.getWlan().getMacRegistrationListId().getValue()
            .equals(macRegistrationId));
  }

  @Test
  void updateOpenNetwork_whenTurnOnMulticastFilter(Tenant tenant, Venue venue) {
    Network network = NetworkTestFixture.randomOpenNetwork(tenant, (v)->{});
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    NetworkVenue networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    OpenNetwork openNetwork = Generators.openNetwork().generate();
    openNetwork.getWlan().getAdvancedCustomization().setMulticastFilterEnabled(true);
    openNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("networkId", network.getId()),
        openNetwork);

    final var savedNetwork = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork::getWlan)
        .isNotNull()
        .extracting(wlan -> wlan.getAdvancedCustomization().getMulticastFilterEnabled())
        .isEqualTo(true);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
            WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue).first().extracting(operation ->
            operation.getWlanVenue().getMulticastFilterDrop())
        .isEqualTo(true);
  }

  @Test
  void updateOpenNetworkWithQosMapSet(Tenant tenant, Venue venue) {
    Network network = NetworkTestFixture.randomOpenNetwork(tenant, (v)->{});
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

    NetworkVenue networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    OpenNetwork openNetwork = Generators.openNetwork().generate();
    openNetwork.getWlan().getAdvancedCustomization().setQosMapSetEnabled(true);
    openNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("networkId", network.getId()),
        openNetwork);

    final var savedNetwork = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork::getWlan)
        .isNotNull()
        .matches((wlan -> wlan.getAdvancedCustomization().getQosMapSetEnabled()))
        .matches(wlan -> wlan.getAdvancedCustomization().getQosMapSetOptions().getRules().size() == 8);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload())
        .extracting(WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue)
        .first()
        .extracting(o -> o.getWlanVenue().getAdvancedCustomization().getQosMap())
        .matches(qosMap -> qosMap.getEnabled().equals(BoolValue.of(true)))
        .matches(qosMap -> qosMap.getDscpValuesCount() == 8);

    final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenant.getId(), requestId);
    WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
    assertThat(wifiConfigChange.getOperationList())
        .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasQosMapRule)
        .hasSize(8);
    assertThat(wifiConfigChange.getOperationList())
        .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasWlan)
        .first()
        .matches(o -> o.getWlan().getAdvancedCustomization().getQosMapSetEnabled().equals(BoolValue.of(true)));
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_8021X_MAC_TOGGLE)
  void updateAAANetworkWithMacAuthMacFormat(Tenant tenant, Venue venue) {
    Network network = NetworkTestFixture.randomAAANetwork(tenant, (v) -> {});
    network.getWlan().setMacAddressAuthentication(true);
    network.getWlan().setMacAuthMacFormat(MacAuthMacFormatEnum.Lower);
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

    NetworkVenue networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork updatedNetwork =
        new com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork();
    updatedNetwork.setId(network.getId());
    updatedNetwork.setName(network.getName());
    updatedNetwork.setDescription(network.getDescription());
    updatedNetwork.setWlan(new AAAWlan());
    updatedNetwork.getWlan().setSsid(network.getWlan().getSsid());
    updatedNetwork
        .getWlan()
        .setMacAddressAuthenticationConfiguration(new MacAddressAuthenticationConfiguration());
    updatedNetwork
        .getWlan()
        .getMacAddressAuthenticationConfiguration()
        .setMacAddressAuthentication(true);
    updatedNetwork
        .getWlan()
        .getMacAddressAuthenticationConfiguration()
        .setMacAuthMacFormat(MacAuthMacFormatEnum.Upper);
    var authRadius = RadiusTestFixture.authRadius();
    repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());
    updatedNetwork.setAuthRadius(
        RadiusRestCtrl.RadiusMapper.INSTANCE.ServiceRadius2Radius(authRadius));

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("networkId", network.getId()),
        updatedNetwork);

    final var savedNetwork = repositoryUtil.find(AAANetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.AAA))
        .extracting(AAANetwork::getWlan)
        .isNotNull()
        .extracting(wlan -> wlan.getMacAuthMacFormat().name())
        .isEqualTo(MacAuthMacFormatEnum.Upper.name());

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload())
        .extracting(WifiConfigRequest::getOperationsList)
        .asList()
        .isNotEmpty()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue)
        .first()
        .matches(operation -> operation.getWlanVenue().getMacAddressAuthentication().getMacAuthEnabled().getValue())
        .extracting(
            operation ->
                operation.getWlanVenue().getMacAddressAuthentication().getMacAuthMacFormat().name())
        .isEqualTo(MacAuthMacFormatEnum.Upper.name());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_WLAN_DEPRECATE_WEP)
  void updatePskNetwork_withWep_success(Tenant tenant, Venue venue) {
    var network = NetworkTestFixture.randomPskNetwork(tenant, n -> {
      n.getWlan().setWlanSecurity(WlanSecurityEnum.WEP);
    });
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    var networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    var pskNetwork = Generators.pskNetwork().generate();
    pskNetwork.getWlan().setWlanSecurity(PskWlanSecurityEnum.WEP);
    pskNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("networkId", network.getId()),
        pskNetwork);

    final var savedNetwork = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.PSK))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork::getWlan)
        .isNotNull()
        .extracting(AbstractWlan::getWlanSecurity)
        .isEqualTo(WlanSecurityEnum.WEP);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
            WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue).first().extracting(operation ->
            operation.getWlanVenue().getWlanSecurity())
        .isEqualTo(WlanSecurity.WEP);
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_WLAN_DEPRECATE_WEP)
  void updatePskNetwork_withWep_fail(Tenant tenant, Venue venue) {
    var network = NetworkTestFixture.randomPskNetwork(tenant, n -> {
      n.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
    });
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    var networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    var pskNetwork = Generators.pskNetwork().generate();
    pskNetwork.getWlan().setWlanSecurity(PskWlanSecurityEnum.WEP);
    pskNetwork.setId(network.getId());

    var requestId = randomTxId();
    try {
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgAction.UPDATE_NETWORK,
          randomName(),
          new RequestParams().addPathVariable("networkId", network.getId()),
          pskNetwork);
    } catch (Exception ignored) {
    }

    var status = Optional.ofNullable(messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenant.getId(), Duration.ofSeconds(1L)))
        .map(KafkaProtoMessage::getPayload)
        .orElseThrow(() -> new RuntimeException("No ActivityStatus"));

    assertAll("assert activity status fail",
        () -> assertEquals(Status.FAIL, status.getStatus()),
        () -> assertEquals(ApiFlowNames.UPDATE_NETWORK, status.getStep())
    );
    JSONObject error = new JSONObject(status.getError());

    assertAll("assert activity status fail error",
        () -> assertEquals(Errors.WIFI_10552.code(), error.get("code")),
        () -> assertEquals(Errors.WIFI_10552.message(), error.get("message"))
    );

    messageCaptors.assertThat(kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest()).doesNotSendByTenant(tenant.getId());
  }
}
