package com.ruckus.cloud.wifi.integration.network;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.HOST_APPROVAL_EMAIL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.BoolValue;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.RadiusRestCtrl.RadiusMapper;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CalledStationIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MacAuthMacFormatEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdDelimiterEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.AAAWlan;
import com.ruckus.cloud.wifi.eda.viewmodel.AAAWlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.viewmodel.DnsProxy;
import com.ruckus.cloud.wifi.eda.viewmodel.DnsProxyRule;
import com.ruckus.cloud.wifi.eda.viewmodel.GuestWlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.MacAddressAuthenticationConfiguration;
import com.ruckus.cloud.wifi.eda.viewmodel.Network;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenWlan;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenWlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenWlanSecurityEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.GuestPortal;
import com.ruckus.cloud.wifi.proto.Operation;
import com.ruckus.cloud.wifi.proto.SocialIdentities;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_BSS_PRIORITY_TOGGLE)
class ConsumeAddNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  protected MessageCaptors messageCaptors;

  String getRequestJson(String file) throws IOException {
    return IOUtils.toString(
        Objects.requireNonNull(this.getClass().getResourceAsStream(file)), StandardCharsets.UTF_8);
  }

  @Nested
  class whenConsumeAddNetworkRequest {

    @Test
    void thenSaveNetwork(Tenant tenant) {
      final var requestId = randomTxId();
      final var userName = randomName();
      // wifi-api prepares id in ADD NETWORK case
      final var id = randomId();
      final var network = getOpenNetwork(id);

      messageUtil.sendWifiCfgRequest(tenant.getId(), requestId, CfgAction.ADD_NETWORK, userName,
          network);

      validateResult(network);
    }

    // FIXME: should refactor with the request payload for ruckus-one
    @Disabled("not using the request payload for ruckus-one")
    @Test
    void thenCreatePskNetworkWithMacAuthEnabled(Tenant tenant) throws IOException {

      var userName = "userName01";

      String json = getRequestJson("/requests/addPskNetworkMacAuth.json");

      com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork networkRequest;
      networkRequest = (com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork)
          objectMapper.readValue(json, new TypeReference<Network>() {
          });

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ADD_NETWORK,
          userName,
          new RequestParams(),
          networkRequest);

      final var network = repositoryUtil.find(
          PskNetwork.class, networkRequest.getId());

      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.PSK))
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(networkRequest.getWlan().getSsid()))
          .matches(wlan -> wlan.getVlanId().equals(networkRequest.getWlan().getVlanId()))
          .matches(wlan -> wlan.getAdvancedCustomization().getEnableAdditionalRegulatoryDomains().equals(
                  networkRequest.getWlan().getAdvancedCustomization().getEnableAdditionalRegulatoryDomains()));

      assertThat(network)
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork::getAuthRadius)
          .extracting(Radius::getPrimary)
          .matches(radiusServer -> radiusServer.getIp().equals(
              networkRequest.getAuthRadius().getPrimary().getIp()))
          .matches(radiusServer -> radiusServer.getPort().equals(
              networkRequest.getAuthRadius().getPrimary().getPort()))
          .matches(radiusServer -> radiusServer.getSharedSecret().equals(
              networkRequest.getAuthRadius().getPrimary().getSharedSecret()));

      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(tenant.getId());
    }

    // FIXME: should refactor with the request payload for ruckus-one
    @Disabled("not using the request payload for ruckus-one")
    @Test
    void thenCreateAaaNetwork(Tenant tenant) throws IOException {

      var userName = "userName01";

      String json = IOUtils.toString(
          Objects.requireNonNull(this.getClass().getResourceAsStream(
              "/requests/addAaaNetwork.json")),
          StandardCharsets.UTF_8);

      com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork networkRequest;
      networkRequest = (com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork)
          objectMapper.readValue(json, new TypeReference<Network>() {
          });
      networkRequest.setId(randomId());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ADD_NETWORK,
          userName,
          new RequestParams(),
          networkRequest);

      final var network = repositoryUtil.find(
          AAANetwork.class, networkRequest.getId());

      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.AAA))
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(networkRequest.getWlan().getSsid()))
          .matches(wlan -> wlan.getVlanId().equals(networkRequest.getWlan().getVlanId()))
          .matches(wlan -> wlan.getAdvancedCustomization().getEnableAdditionalRegulatoryDomains().equals(
                  networkRequest.getWlan().getAdvancedCustomization().getEnableAdditionalRegulatoryDomains()));

      assertThat(network)
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork::getAuthRadius)
          .extracting(Radius::getPrimary)
          .matches(radiusServer -> radiusServer.getIp().equals(
              networkRequest.getAuthRadius().getPrimary().getIp()))
          .matches(radiusServer -> radiusServer.getPort().equals(
              networkRequest.getAuthRadius().getPrimary().getPort()))
          .matches(radiusServer -> radiusServer.getSharedSecret().equals(
              networkRequest.getAuthRadius().getPrimary().getSharedSecret()));
    }

    @Test
    void thenCreateOpenNetworkWithMacRegistrationId(Tenant tenant) throws IOException {
      final var id = randomId();
      final var networkRequest = getOpenNetwork(id);
      final var macRegistrationId = randomId();
      networkRequest.getWlan().setMacRegistrationListId(macRegistrationId);
      networkRequest.getWlan().setMacAddressAuthentication(true);

      var requestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgAction.ADD_NETWORK,
          randomName(),
          new RequestParams(),
          networkRequest);

      // assert db
      final var network = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, networkRequest.getId());
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork::getWlan)
          .isNotNull()
          .matches(Wlan::getMacAddressAuthentication)
          .matches(wlan -> wlan.getMacRegistrationListId().equals(macRegistrationId))
          .matches(wlan -> wlan.getWlanSecurity().name().equals(OpenWlanSecurityEnum.OWE.name()));

      // assert ddccm
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(tenant.getId());

      // assert wifiCfgChange
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenant.getId(), requestId);
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
      assertThat(wifiConfigChange.getOperationList()).filteredOn(Operation::hasWlan)
          .first().matches(operation -> operation.getWlan().getMacRegistrationListId().getValue()
              .equals(macRegistrationId));
    }

    @Test
    void thenCreateOpenNetworkWithQosMapSet(Tenant tenant) throws IOException {
      final var id = randomId();
      final var networkRequest = getOpenNetwork(id);
      networkRequest.getWlan().getAdvancedCustomization().setQosMapSetEnabled(true);

      var requestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgAction.ADD_NETWORK,
          randomName(),
          new RequestParams(),
          networkRequest);

      // assert db
      final var network = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, networkRequest.getId());
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
          .extracting(n -> n.getWlan().getAdvancedCustomization().getQosMapSetOptions())
          .isNotNull()
          .matches(options -> options.getRules().size() == 8);

      // assert ddccm
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(tenant.getId());

      // assert wifiCfgChange
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenant.getId(), requestId);
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
      assertThat(wifiConfigChange.getOperationList())
          .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasQosMapRule)
          .hasSize(8);
      assertThat(wifiConfigChange.getOperationList())
          .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasWlan)
          .first()
          .matches(o -> o.getWlan().getAdvancedCustomization().getQosMapSetEnabled().equals(BoolValue.of(true)));
    }

    @Test
    void thenCreateAaaNetworkWithMacAddressAuthentication(Tenant tenant) {
      com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork networkRequest =
          getAAANetwork(tenant, randomId());
      networkRequest
          .getWlan()
          .setMacAddressAuthenticationConfiguration(new MacAddressAuthenticationConfiguration());
      networkRequest
          .getWlan()
          .getMacAddressAuthenticationConfiguration()
          .setMacAddressAuthentication(true);
      networkRequest
          .getWlan()
          .getMacAddressAuthenticationConfiguration()
          .setMacAuthMacFormat(MacAuthMacFormatEnum.Lower);

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ADD_NETWORK,
          randomName(),
          new RequestParams(),
          networkRequest);

      final var network = repositoryUtil.find(AAANetwork.class, networkRequest.getId());

      // assert db
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.AAA))
          .matches(n -> n.getWlan().getMacAuthMacFormat().equals(MacAuthMacFormatEnum.Lower))
          .extracting(n -> n.getWlan().getMacAddressAuthentication())
          .isNotNull();

      // assert ddccm
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(tenant.getId());
    }
  }

  com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork getAddDpskNetworkRequest(
      String id, String name, String dpskProfileId) throws IOException {

    String requestJson = getRequestJson("/requests/addDpskNetwork.json");

    com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork addDpskRequest;
    addDpskRequest = (com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork)
        objectMapper.readValue(requestJson, new TypeReference<Network>() {
        });
    addDpskRequest.setId(id);
    addDpskRequest.setName(name);
    addDpskRequest.getWlan().setSsid(name);
    addDpskRequest.setDpskServiceProfileId(dpskProfileId);
    addDpskRequest.setDpskPassphraseGeneration(null);

    return addDpskRequest;
  }

  @Nested
  class whenConsumeDPSKNetworkRequest {

    final String userName = randomName();

    @Test
    void thenDpskNetworkRuckusOnePver(Tenant tenant) throws IOException {

      final String tenantId = tenant.getId();
      String dpskProfileId = randomId();
      com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork addDpskRequest =
          getAddDpskNetworkRequest(randomId(), "network00", dpskProfileId);

      final String addRequestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          addRequestId,
          CfgAction.ADD_NETWORK,
          userName,
          new RequestParams(),
          addDpskRequest);

      final DpskNetwork addedDpsk = repositoryUtil.find(
          DpskNetwork.class, addDpskRequest.getId());

      assertThat(addedDpsk)
          .isNotNull()
          .matches(n -> n.getId().equals(addDpskRequest.getId()), "match id")
          .matches(n -> n.getName().equals(addDpskRequest.getName()), "match name")
          .matches(n -> n.getType().equals(NetworkTypeEnum.DPSK), "match DPSK")
          .matches(n -> n.getDpskServiceProfileId().equals(dpskProfileId), "match DPSK profile Id")
          .matches(n -> n.getUseDpskService().equals(true), "match useDpskService")
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(addDpskRequest.getWlan().getSsid()))
          .matches(wlan -> wlan.getVlanId().equals(addDpskRequest.getWlan().getVlanId()));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, addRequestId);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(addRequestId))
          .matches(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK.equals(o.getIndex())).count() == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.ADD)
          .matches(p -> p.getId().equals(addedDpsk.getId()));

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(tenantId, addRequestId);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(addRequestId);

      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(tenantId);

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, addRequestId);
      assertThat(activityCfgChangeRespMessage).isNotNull();
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(tenantId);

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.ADD_NETWORK))
          .matches(p -> p.getEventDate() != null);

      assertCfgChangeDpskNetworkIsTemplate(tenantId, addRequestId, false);

      //Update
      String updateReqJson = getRequestJson("/requests/updateDpskNetwork.json");

      com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork updateDpskRequest;
      updateDpskRequest = (com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork)
          objectMapper.readValue(updateReqJson, new TypeReference<Network>() {
          });

      updateDpskRequest.setId(addedDpsk.getId());
      updateDpskRequest.setTenantId(addedDpsk.getTenant().getId());
      updateDpskRequest.setDescription("updated DPSK description!");
      updateDpskRequest.setDpskPassphraseGeneration(null);
      String profileId2 = randomId();
      updateDpskRequest.setDpskServiceProfileId(profileId2);
      final String updateRequestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          updateRequestId,
          CfgAction.UPDATE_NETWORK,
          userName,
          new RequestParams().addPathVariable("networkId", updateDpskRequest.getId()),
          updateDpskRequest);

      final DpskNetwork updatedDpsk = repositoryUtil.find(
          DpskNetwork.class, addDpskRequest.getId());

      assertThat(updatedDpsk)
          .isNotNull()
          .matches(n -> n.getId().equals(updateDpskRequest.getId()), "match id")
          .matches(n -> n.getName().equals(updateDpskRequest.getName()), "match name")
          .matches(n -> n.getType().equals(NetworkTypeEnum.DPSK), "match DPSK")
          .matches(n -> n.getDescription().equals(updateDpskRequest.getDescription()),
              "match Description")
          .matches(n -> n.getDpskServiceProfileId().equals(profileId2), "match DPSK profile Id")
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(updateDpskRequest.getWlan().getSsid()))
          .matches(wlan -> wlan.getVlanId().equals(updateDpskRequest.getWlan().getVlanId()));

      assertCfgChangeDpskNetworkIsTemplate(tenantId, updateRequestId, false);

      //Delete
      final String deleteRequestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          deleteRequestId,
          CfgAction.DELETE_NETWORK,
          userName,
          new RequestParams().addPathVariable("networkId", updateDpskRequest.getId()),
          StringUtils.EMPTY);

      final DpskNetwork deletedDpsk = repositoryUtil.find(
          DpskNetwork.class, addDpskRequest.getId());

      assertThat(deletedDpsk).isNull();

      assertCfgChangeDpskNetworkIsTemplate(tenantId, deleteRequestId, false);
    }

    @Test
    void thenDpskNetworkTemplate(Tenant tenant) throws Exception {

      final String tenantId = tenant.getId();
      String dpskProfileId = randomId();
      com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork addDpskRequest =
          getAddDpskNetworkRequest(randomId(), "network00", dpskProfileId);

      final String addRequestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          addRequestId,
          CfgAction.ADD_NETWORK_TEMPLATE,
          userName,
          new RequestParams(),
          addDpskRequest);

      final DpskNetwork addedDpsk = repositoryUtil.find(
          DpskNetwork.class, addDpskRequest.getId());

      assertThat(addedDpsk)
          .isNotNull()
          .matches(n -> n.getId().equals(addDpskRequest.getId()), "match id")
          .matches(n -> n.getName().equals(addDpskRequest.getName()), "match name")
          .matches(n -> n.getType().equals(NetworkTypeEnum.DPSK), "match DPSK")
          .matches(n -> n.getDpskServiceProfileId().equals(dpskProfileId), "match DPSK profile Id")
          .matches(n -> n.getIsTemplate(), "match isTemplate")
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(addDpskRequest.getWlan().getSsid()))
          .matches(wlan -> wlan.getVlanId().equals(addDpskRequest.getWlan().getVlanId()));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, addRequestId);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(addRequestId))
          .matches(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK.equals(o.getIndex())).count() == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.ADD)
          .matches(p -> p.getId().equals(addedDpsk.getId()));

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(tenantId, addRequestId);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(addRequestId);

      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(tenantId);

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, addRequestId);
      assertThat(activityCfgChangeRespMessage).isNotNull();
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(tenantId);

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.ADD_NETWORK_TEMPLATE))
          .matches(p -> p.getEventDate() != null);

      assertCfgChangeDpskNetworkIsTemplate(tenantId, addRequestId, true);

      //Delete
      String deleteRequestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          deleteRequestId,
          CfgAction.DELETE_NETWORK_TEMPLATE,
          userName,
          new RequestParams().addPathVariable("networkTemplateId", addedDpsk.getId()),
          StringUtils.EMPTY);

      final DpskNetwork deletedDpsk = repositoryUtil.find(
          DpskNetwork.class, addDpskRequest.getId());

      assertThat(deletedDpsk).isNull();

      assertCfgChangeDpskNetworkIsTemplate(tenantId, deleteRequestId, true);
    }

    @Test
    void dpskNetworkBindServiceMaxValidate(Tenant tenant) throws IOException {

      final String tenantId = tenant.getId();
      final String dpskProfileId = randomId();

      messageUtil.sendWifiCfgRequest(
          tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, new RequestParams(),
          getAddDpskNetworkRequest(randomId(), "network01", dpskProfileId));

      messageUtil.sendWifiCfgRequest(
          tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, new RequestParams(),
          getAddDpskNetworkRequest(randomId(), "network02", dpskProfileId));

      //While set default.dpsk-service-profile.bind-max-count: 2
      assertThatExceptionOfType(RuntimeException.class)
          .isThrownBy(() ->
              messageUtil.sendWifiCfgRequest(
                  tenantId, randomTxId(), CfgAction.ADD_NETWORK, userName, new RequestParams(),
                  getAddDpskNetworkRequest(randomId(), "network03", dpskProfileId)));
    }
  }

  private void assertCfgChangeDpskNetworkIsTemplate(String tenantId, String addRequestId, boolean isTemplate)
      throws InvalidProtocolBufferException {
    final var wifiCfgChangeMessage =
        messageCaptors.getWifiCfgChangeMessageCaptor()
            .getValue(tenantId, addRequestId);
    WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
        wifiCfgChangeMessage.getPayload().toByteArray());
    assertThat(wifiConfigChange)
        .extracting(p -> p.getOperationList().stream()
            .filter(Operation::hasDpskNetwork)
            .findFirst().orElseThrow().getDpskNetwork())
        .matches(dpsk -> dpsk.hasIsTemplate())
        .matches(dpsk -> dpsk.getIsTemplate().getValue() == isTemplate);
  }

  @Nested
  class ConsumeAddGuestNetworkRequestTest {

    @Payload("Clickthrough")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadClickthrough() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.ClickThrough,
          n -> {
          });
      return networkRequest;
    }

    @Payload("SelfSignIn")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadSelfSignIn() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.SelfSignIn,
          n -> {
          });
      return networkRequest;
    }

    @Payload("HostApproval")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadHostApproval() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
          });
      return networkRequest;
    }

    @Payload("HostApprovalWithEmail")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadHostApprovalWithEmail() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
            n.getGuestPortal().getHostGuestConfig().setHostEmails(List.of(HOST_APPROVAL_EMAIL));
          });
      return networkRequest;
    }

    @Payload("GuestPass")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadGuestPass() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.GuestPass,
          n -> {
            n.getWlan().setWlanSecurity(GuestWlanSecurityEnum.OWE);
          });
      return networkRequest;
    }

    private String networkId;
    private String networkName;
    private String ssid;
    private Short vlanId;

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("Clickthrough"))
    void thenCreateGuestNetworkClickthrough(TxCtx txCtx,
        @Payload("Clickthrough") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest)
        throws InvalidProtocolBufferException {
      networkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));

      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> gp.getGuestNetworkType().equals(
              com.ruckus.cloud.wifi.proto.GuestNetworkTypeEnum.GuestNetworkTypeEnum_ClickThrough))
          .matches(gp -> gp.getExternalPortalUrl()
              .equals(StringValue.of(networkRequest.getGuestPortal().getExternalPortalUrl())))
          .matches(gp -> gp.getRedirectUrl()
              .equals(StringValue.of(networkRequest.getGuestPortal().getRedirectUrl())));

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasWlan()).findFirst().get()
                  .getWlan())
          .isNotNull()
          .matches(
              w -> w.getSsid().equals(StringValue.of(networkRequest.getWlan().getSsid())));

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);

      assertThat(validGuestNetworkAndGet())
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(portal -> portal.getGuestNetworkType().equals(GuestNetworkTypeEnum.ClickThrough))
          .matches(portal -> portal.getGuestPage().getLangCode().equals("en"))
          .matches(portal -> portal.getEnableSmsLogin().equals(Boolean.FALSE));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("GuestPass"))
    void thenCreateGuestNetworkGuestPassOwe(TxCtx txCtx,
        @Payload("GuestPass") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest)
        throws InvalidProtocolBufferException {
      networkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));

      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(p -> p.getOperationList().stream().filter(o -> o.hasWlan()).findFirst().get().getWlan())
          .isNotNull()
          .matches(w -> w.getSsid().equals(StringValue.of(networkRequest.getWlan().getSsid())))
          .matches(w -> w.getWlanSecurity().name().contains(GuestWlanSecurityEnum.OWE.name()));

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("GuestPass"))
    void thenCreateGuestNetworkGuestPass(TxCtx txCtx,
        @Payload("GuestPass") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest)
        throws InvalidProtocolBufferException {
      networkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));

      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> gp.getGuestNetworkType().equals(
              com.ruckus.cloud.wifi.proto.GuestNetworkTypeEnum.GuestNetworkTypeEnum_GuestPass))
          .matches(gp -> gp.getExternalPortalUrl()
              .equals(StringValue.of(networkRequest.getGuestPortal().getExternalPortalUrl())))
          .matches(gp -> gp.getRedirectUrl()
              .equals(StringValue.of(networkRequest.getGuestPortal().getRedirectUrl())))
          .extracting(GuestPortal::getSmsPasswordDuration)
          .isNotNull();

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);

      assertThat(validGuestNetworkAndGet())
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(portal -> portal.getGuestNetworkType().equals(GuestNetworkTypeEnum.GuestPass));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("HostApproval"))
    void thenCreateGuestNetworkHostApproval(TxCtx txCtx,
        @Payload("HostApproval") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest)
        throws InvalidProtocolBufferException {
      networkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));
      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> gp.getGuestNetworkType().equals(
              com.ruckus.cloud.wifi.proto.GuestNetworkTypeEnum.GuestNetworkTypeEnum_HostApproval))
          .extracting(GuestPortal::getHostGuestConfig)
          .isNotNull()
          .matches(config -> config.getHostDurationChoicesCount() > 0)
          .matches(config -> config.getHostDomainsCount() > 0);

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);

      assertThat(validGuestNetworkAndGet())
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(portal -> portal.getGuestNetworkType().equals(GuestNetworkTypeEnum.HostApproval))
          .matches(portal -> portal.getHostGuestConfig().getHostDomains().get(0)
              .equals(
                  networkRequest.getGuestPortal().getHostGuestConfig().getHostDomains().get(0)));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("HostApprovalWithEmail"))
    void thenCreateGuestNetworkHostApprovalWithEmail(TxCtx txCtx,
        @Payload("HostApprovalWithEmail") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      networkId = networkRequest.getId();

      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, networkId);
      assertThat(network)
          .isNotNull()
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
          .isNotNull()
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal::getHostGuestConfig)
          .isNotNull()
          .matches(hostGuestConfig -> hostGuestConfig.getHostEmails()
              .equals(List.of(HOST_APPROVAL_EMAIL)));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("SelfSignIn"))
    void thenCreateGuestNetworkSelfSignIn(TxCtx txCtx,
        @Payload("SelfSignIn") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest)
        throws InvalidProtocolBufferException {
      networkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));

      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> gp.getGuestNetworkType().equals(
              com.ruckus.cloud.wifi.proto.GuestNetworkTypeEnum.GuestNetworkTypeEnum_SelfSignIn))
          .extracting(GuestPortal::getSocialIdentities)
          .isNotNull()
          .extracting(SocialIdentities::getFacebook)
          .isNotNull();

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);

      assertThat(validGuestNetworkAndGet())
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(portal -> portal.getGuestNetworkType().equals(GuestNetworkTypeEnum.SelfSignIn))
          .matches(portal -> portal.getSocialIdentities().getFacebook().getSource().equals(
              networkRequest.getGuestPortal().getSocialIdentities().getFacebook().getSource()));
    }

    private byte[] validateWifiCfgChangeMessageAndGetValue(TxCtx txCtx) {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
              .getValue(txCtx);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());
      assertThat(wifiCfgChangeMessageRecord.getPayload()).isNotNull();
      return wifiCfgChangeMessageRecord.getPayload().toByteArray();
    }

    private void validateWifiConfigChangeAndNetwork(WifiConfigChange wifiConfigChange,
        TxCtx txCtx) {
      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestNetwork()).findFirst().get()
                  .getGuestNetwork())
          .matches(n -> n.getId().equals(StringValue.of(networkId)))
          .matches(n -> n.getName().equals(StringValue.of(networkName)));
    }

    private void validateCmnCfgCollectorMessage(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(txCtx);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.ADD)
          .matches(p -> p.getId().equals(networkId));
    }

    private void validateActivityImpactedMessage(TxCtx txCtx) {
      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());

      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
    }

    private void validateActivityMessages(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.ADD_NETWORK))
          .extracting(ConfigurationStatus::getEventDate)
          .isNotNull();
    }

    private GuestNetwork validGuestNetworkAndGet() {
      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, networkId);
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkId))
          .matches(n -> n.getName().equals(networkName))
          .matches(n -> n.getType().equals(NetworkTypeEnum.GUEST))
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(ssid))
          .matches(wlan -> wlan.getVlanId().equals(vlanId));
      return network;
    }
  }

  @Nested
  class ConsumeAddNetworkWithRadiusOptionsTest {

    @Test
    void thenCreateAaaNetworkWithOldPayload(Tenant tenant) throws IOException {

      com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork networkRequest = getAAANetwork(tenant,
          randomId());
      var advancedCustomization = networkRequest.getWlan().getAdvancedCustomization();
      advancedCustomization.setSingleSessionIdAccounting(true);
      assertThat(advancedCustomization.getRadiusOptions())
          .isNotNull()
          .matches(r -> !r.getSingleSessionIdAccounting())
          .extracting(com.ruckus.cloud.wifi.eda.viewmodel.RadiusOptions::getCalledStationIdType)
          .isNull();

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ADD_NETWORK,
          randomName(),
          new RequestParams(),
          networkRequest);

      final var network = repositoryUtil.find(
          AAANetwork.class, networkRequest.getId());

      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.AAA))
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork::getWlan)
          .isNotNull()
          .extracting(Wlan::getAdvancedCustomization)
          .extracting(WlanAdvancedCustomization::getRadiusOptions)
          .isNotNull()
          .matches(RadiusOptions::getSingleSessionIdAccounting,
              "RadiusOptions.SingleSessionIdAccounting")
          .matches(r -> Objects.equals(r.getCalledStationIdType(), CalledStationIdTypeEnum.BSSID),
              "RadiusOptions.calledStationIdType")
          .matches(r -> Objects.equals(r.getNasIdType(), NasIdTypeEnum.BSSID),
          "RadiusOptions.nasIdType")
          .matches(r -> Objects.equals(r.getNasMaxRetry(), 2),
          "RadiusOptions.nasMaxRetry")
          .matches(r -> Objects.equals(r.getNasReconnectPrimaryMin(), 5),
          "RadiusOptions.nasReconnectPrimaryMin")
          .matches(r -> Objects.equals(r.getNasRequestTimeoutSec(), 3),
          "RadiusOptions.nasRequestTimeoutSec")
          .matches(r -> Objects.equals(r.getNasIdDelimiter(), NasIdDelimiterEnum.DASH),
          "RadiusOptions.nasIdDelimiter")
          .matches(r -> Objects.isNull(r.getUserDefinedNasId()),
          "RadiusOptions.userDefinedNasId");
    }

    @Test
    void thenCreateAaaNetworkWithNewPayload(Tenant tenant) throws IOException {

      com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork networkRequest = getAAANetwork(tenant,
          randomId());
      com.ruckus.cloud.wifi.eda.viewmodel.RadiusOptions radiusOptions = new com.ruckus.cloud.wifi.eda.viewmodel.RadiusOptions();
      radiusOptions.setSingleSessionIdAccounting(true);
      radiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_GROUP);
      networkRequest.getWlan().getAdvancedCustomization().setRadiusOptions(radiusOptions);

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ADD_NETWORK,
          randomName(),
          new RequestParams(),
          networkRequest);

      final var network = repositoryUtil.find(
          AAANetwork.class, networkRequest.getId());

      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.AAA))
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork::getWlan)
          .isNotNull()
          .extracting(Wlan::getAdvancedCustomization)
          .extracting(WlanAdvancedCustomization::getRadiusOptions)
          .isNotNull()
          .matches(RadiusOptions::getSingleSessionIdAccounting,
              "RadiusOptions.SingleSessionIdAccounting")
          .matches(r -> Objects.equals(r.getCalledStationIdType(), CalledStationIdTypeEnum.AP_GROUP),
              "RadiusOptions.calledStationIdType")
          .matches(r -> Objects.equals(r.getNasIdType(), NasIdTypeEnum.BSSID),
          "RadiusOptions.nasIdType")
          .matches(r -> Objects.equals(r.getNasMaxRetry(), 2),
              "RadiusOptions.nasMaxRetry")
          .matches(r -> Objects.equals(r.getNasReconnectPrimaryMin(), 5),
              "RadiusOptions.nasReconnectPrimaryMin")
          .matches(r -> Objects.equals(r.getNasRequestTimeoutSec(), 3),
              "RadiusOptions.nasRequestTimeoutSec")
          .matches(r -> Objects.equals(r.getNasIdDelimiter(), NasIdDelimiterEnum.DASH),
              "RadiusOptions.nasIdDelimiter")
          .matches(r -> Objects.isNull(r.getUserDefinedNasId()),
              "RadiusOptions.userDefinedNasId");
    }
  }

  OpenNetwork getOpenNetwork(String id) {
    OpenNetwork network = new OpenNetwork();
    network.setId(id);
    network.setName("open3");
    network.setDescription(StringUtils.EMPTY);
    network.setWlan(new OpenWlan());
    network.getWlan().setEnabled(true);
    network.getWlan().setSsid("open3");
    network.getWlan().setVlanId((short) 1);

    network.getWlan().setAdvancedCustomization(new OpenWlanAdvancedCustomization());
    network.getWlan().getAdvancedCustomization().setWifiCallingEnabled(false);
    network.getWlan().getAdvancedCustomization().setWifiCallingIds(new ArrayList<>());
    network.getWlan().getAdvancedCustomization().setDnsProxyEnabled(true);
    network.getWlan().getAdvancedCustomization().setDnsProxy(getDnsProxy());
    network.getWlan().setWlanSecurity(OpenWlanSecurityEnum.OWE);

    return network;
  }

  com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork getAAANetwork(Tenant tenant, String id) {
    com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork network = new com.ruckus.cloud.wifi.eda.viewmodel.AAANetwork();
    network.setId(id);
    network.setName(randomName());
    network.setDescription("");
    network.setWlan(new AAAWlan());
    network.getWlan().setSsid(randomName());
    network.getWlan().setAdvancedCustomization(new AAAWlanAdvancedCustomization());

    var authRadius = RadiusTestFixture.authRadius();
    repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());
    var acctRadius = RadiusTestFixture.acctRadius();
    repositoryUtil.createOrUpdate(acctRadius, tenant.getId(), randomTxId());
    network.setAuthRadius(RadiusMapper.INSTANCE.ServiceRadius2Radius(authRadius));
    network.setAccountingRadius(RadiusMapper.INSTANCE.ServiceRadius2Radius(acctRadius));
    return network;
  }

  DnsProxy getDnsProxy() {
    DnsProxy dnsProxy = new DnsProxy();
    DnsProxyRule dnsProxyRule1 = new DnsProxyRule();
    dnsProxyRule1.setDomainName("rks.com");
    dnsProxyRule1.setIpList(List.of("**********"));
    dnsProxy.setDnsProxyRules(List.of(dnsProxyRule1));
    return dnsProxy;
  }

  void validateResult(OpenNetwork networkRequest) {
    final var network = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, networkRequest.getId());

    assertThat(network)
        .isNotNull()
        .matches(n -> n.getId().equals(networkRequest.getId()))
        .matches(n -> n.getName().equals(networkRequest.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork::getWlan)
        .isNotNull()
        .matches(wlan -> wlan.getSsid().equals(networkRequest.getWlan().getSsid()))
        .matches(wlan -> wlan.getVlanId().equals(networkRequest.getWlan().getVlanId()))
        .extracting(Wlan::getAdvancedCustomization)
        .isNotNull()
        .matches(wlanAdvancedCustomization -> wlanAdvancedCustomization.getDnsProxyEnabled()
            .equals(networkRequest.getWlan().getAdvancedCustomization().getDnsProxyEnabled()))
        .matches(
            wlanAdvancedCustomization -> wlanAdvancedCustomization.getDnsProxy().getDnsProxyRules()
                .get(0).getDomainName()
                .equals(networkRequest.getWlan().getAdvancedCustomization().getDnsProxy()
                    .getDnsProxyRules().get(0).getDomainName()))
        .matches(
            wlanAdvancedCustomization -> wlanAdvancedCustomization.getBssPriority()
                .equals(networkRequest.getWlan().getAdvancedCustomization().getBssPriority()));
  }
}
