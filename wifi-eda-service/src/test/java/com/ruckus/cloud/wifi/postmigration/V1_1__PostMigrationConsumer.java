package com.ruckus.cloud.wifi.postmigration;

import com.ruckus.acx.service.postmigration.entity.PostMigration;
import com.ruckus.acx.service.postmigration.kafka.PostMigrationConsumer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class V1_1__PostMigrationConsumer extends PostMigrationConsumer {
    @Override
    public void run(PostMigration message) throws Exception {
        log.info("!!!!!! [{}] {} !!!!!!", message.getVersion(), message.getDescription());
    }
}
