package com.ruckus.cloud.wifi.integration.clientIsolationonlanport;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.newLanPortAdoption;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.AP_ACTIVATIONS;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.VENUE_ACTIVATIONS;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice.ImpactDevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.CcmUserSidePort;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.*;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("EthernetPortProfileTest")
@FeatureFlag(
    enable = {
      FlagNames.ACX_UI_ETHERNET_TOGGLE,
      FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
      FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE
    })
@WifiIntegrationTest
class ConsumeDeactivateClientIsolationProfileOnVenueApModelLanPortRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private RevisionService revisionService;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private LanPortAdoptionServiceImpl lanPortAdoptionService;

  private void validateResult(
      CfgAction apiAction,
      String clientIsolationAllowlistId,
      String venueLanPortId,
      String venueId,
      String apModel,
      String portId) {
    validateRepositoryData(venueLanPortId, clientIsolationAllowlistId);
    validateDdccmCfgRequestMessages(venueLanPortId, clientIsolationAllowlistId);
    validateCmnCfgCollectorMessages(clientIsolationAllowlistId, venueId, apModel, portId);
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(String venueLanPortId, String clientIsolationAllowlistId) {

    assertThat(venueLanPortId).isNotNull();

    final var allLanPortAdoption =
        repositoryUtil.findAll(LanPortAdoption.class, txCtxExtension.getTenantId());
    assertThat(allLanPortAdoption).isNotNull().matches(l -> l.size() == 2);
    final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
    assertThat(venueLanPort)
        .extracting(VenueLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getClientIsolationActivation)
        .extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
        .isNull();
  }

  private void validateDdccmCfgRequestMessages(
      String venueLanPortId, String clientIsolationAllowlistId) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();
    final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
    var inUseEthernetPortProfileId = venueLanPort.getLanPortAdoption().getEthernetPortProfileId();

    var operations =
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList)
            .asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast);

    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .isNotEmpty()
        .allSatisfy(
            op ->
                assertThat(op.getVenue().getVenueApModelsList())
                    .anySatisfy(
                        model ->
                            assertThat(model.getLanPortList())
                                .anySatisfy(
                                    lanPort ->
                                        assertThat(lanPort.getApLanPortProfileId())
                                            .isEqualTo(inUseEthernetPortProfileId))));

    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApLanPortProfile)
        .filteredOn(operation -> operation.getAction() == Action.ADD)
        .isNotEmpty()
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .allSatisfy(op -> verifyAddAction(op, venueLanPort));
    operations
        .filteredOn(operation -> operation.getAction() == Action.DELETE)
        .allSatisfy(this::verifyDeleteAction);
  }

  private void verifyAddAction(
      com.ruckus.acx.ddccm.protobuf.wifi.Operation operation, VenueLanPort venueLanPort) {
    var apLanPortProfileAssert =
        assertThat(operation)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile);

    apLanPortProfileAssert
        .matches(
            apLanPortProfile ->
                venueLanPort.getLanPortAdoption().getId().equals(apLanPortProfile.getName()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getLanPortAdoption()
                    .getEthernetPortProfileId()
                    .equals(apLanPortProfile.getId()))
        .matches(
            apLanPortProfile ->
                (venueLanPort.getApLanPortProfile().getType().name() + "PORT")
                    .equals(apLanPortProfile.getLanPortType().name()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagId()));

    apLanPortProfileAssert.extracting(ApLanPortProfile::getClientIsolation).isNotNull();

    apLanPortProfileAssert
        .extracting(ApLanPortProfile::getClientIsolation)
        .matches(
            ccmWiredClientIsolation ->
                ccmWiredClientIsolation.getClientIsolationWhitelistId().isEmpty());

    apLanPortProfileAssert
        .extracting(ApLanPortProfile::getUserSidePort)
        .isNotNull()
        .matches(CcmUserSidePort::getUserSidePortEnabled)
        .matches(ccmUserSidePort -> ccmUserSidePort.getUserSidePortMaxClient().getValue() == 32);
  }

  private void verifyDeleteAction(com.ruckus.acx.ddccm.protobuf.wifi.Operation operation) {
    assertThat(operation)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
        .isNotNull();
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors
          .assertThat(
              kafkaTopicProvider.getActivityCfgChangeResp(),
              kafkaTopicProvider.getActivityImpacted())
          .doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(ConfigurationStatus::getEventDate)
                    .isNotNull());

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ImpactDevice::getDeviceIdsCount).isEqualTo(0);
  }

  private void validateCmnCfgCollectorMessages(
      String clientIsolationProfileId, String venueId, String apModel, String portId) {

    if (clientIsolationProfileId == null) {
      messageCaptors
          .getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    var operations =
        cmnCfgCollectorMessage.getPayload().getOperationsList().stream()
            .filter(
                op ->
                    EsConstants.Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME.equals(op.getIndex())
                        && op.getOpType() == OpType.MOD)
            .collect(Collectors.partitioningBy(op -> clientIsolationProfileId.equals(op.getId())));

    assertThat(operations.get(true))
        .singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(
            docMap -> {
              assertThat(docMap.get(AP_ACTIVATIONS).getListValue().getValuesList()).isEmpty();
              assertThat(docMap.get(VENUE_ACTIVATIONS).getListValue().getValuesList()).isEmpty();
            });
    assertThat(operations.get(false)).isEmpty();
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT ->
          ApiFlowNames.DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private EthernetPortProfile createEthernetPortProfile(Venue venue, int ethernetPortProfileId) {
    return repositoryUtil.createOrUpdate(
        EthernetPortProfileTestFixture.randomEthernetPortProfile(
            venue,
            e -> {
              e.setApLanPortId(ethernetPortProfileId);
              e.setName(randomName());
              e.setType(ApLanPortTypeEnum.TRUNK);
              e.setUntagId((short) 1);
              e.setVlanMembers("1-4094");
              e.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
            }),
        txCtxExtension.getTenantId(),
        randomTxId());
  }

  private VenueLanPortData createVenueLanPortData(
      Venue venue,
      int ethernetPortProfileId,
      String apModel,
      String portId,
      ClientIsolationLanPortActivation clientIsolationLanPortActivation) {

    var apLanPortProfile = createEthernetPortProfile(venue, ethernetPortProfileId);

    VenueApModelSpecificAttributes modelAttributes;
    var findVenueApModel =
        venue.getModelSpecificAttributes().stream()
            .filter(m -> apModel.equals(m.getModel()))
            .findAny();

    if (findVenueApModel.isPresent()) {
      modelAttributes = findVenueApModel.get();
    } else {
      modelAttributes =
          repositoryUtil.createOrUpdate(
              VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(
                  venue,
                  v -> {
                    v.setModel(apModel);
                    v.setLanPorts(new ArrayList<>());
                  }),
              txCtxExtension.getTenantId(),
              randomTxId());
      venue.getModelSpecificAttributes().add(modelAttributes);
    }

    var port =
        repositoryUtil.createOrUpdate(
            VenueLanPortTestFixture.randomVenueLanPort(
                venue,
                modelAttributes,
                v -> {
                  v.setApLanPortProfile(apLanPortProfile);
                  v.setPortId(portId);
                  v.setVenueApModelSpecificAttributes(modelAttributes);
                }),
            txCtxExtension.getTenantId(),
            randomTxId());

    final var finalClientIsolationLanPortActivation = clientIsolationLanPortActivation;

    var adoption =
        repositoryUtil.createOrUpdate(
            LanPortAdoptionTestFixture.randomLanPortAdoption(
                apLanPortProfile,
                customizer -> {
                  customizer.setChecksum(
                      lanPortAdoptionService.calculateChecksum(
                          newLanPortAdoption(
                              apLanPortProfile, finalClientIsolationLanPortActivation)));
                }),
            txCtxExtension.getTenantId(),
            randomTxId());
    if (clientIsolationLanPortActivation != null) {
      clientIsolationLanPortActivation.setLanPortAdoption(adoption);
      repositoryUtil.createOrUpdate(
          clientIsolationLanPortActivation, txCtxExtension.getTenantId(), randomTxId());
    }
    port.setLanPortAdoption(adoption);
    port = repositoryUtil.createOrUpdate(port, txCtxExtension.getTenantId(), port.getId());

    return new VenueLanPortData(modelAttributes, port, apLanPortProfile);
  }

  private record VenueLanPortData(
      VenueApModelSpecificAttributes modelAttributes,
      VenueLanPort port,
      EthernetPortProfile profile) {}

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT)
  class ConsumeActivateClientIsolationProfileOnVenueApModelLanPortAndChangeClientIsolationProfileTest {

    private final String apModel = "R550";
    private final String portId = "1";
    private String ethernetPortProfileId;
    private String venueId;
    private String venueLanPortId;
    private String clientIsolationProfileId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue, ClientIsolationAllowlist clientIsolationAllowlist) {
      venueId = venue.getId();
      clientIsolationProfileId = clientIsolationAllowlist.getId();

      ClientIsolationLanPortActivation clientIsolationLanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .clientIsolationLanPortActivation()
              .generate();
      clientIsolationLanPortActivation.setClientIsolationAllowlist(clientIsolationAllowlist);

      var portData1 =
          createVenueLanPortData(venue, 3, apModel, portId, clientIsolationLanPortActivation);
      var portData2 = createVenueLanPortData(venue, 4, apModel, "2", null);
      venueLanPortId = portData1.port.getId();
      ethernetPortProfileId =
          portData1.port.getLanPortAdoption().getEthernetPortProfileId().toString();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("clientIsolationProfileId", clientIsolationProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {

      validateResult(
          CfgAction.DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
          clientIsolationProfileId,
          venueLanPortId,
          venueId,
          apModel,
          portId);
    }
  }
}
