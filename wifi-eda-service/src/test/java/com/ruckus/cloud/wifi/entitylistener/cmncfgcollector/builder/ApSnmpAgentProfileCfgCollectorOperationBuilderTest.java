package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV2Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV3Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSnmpAgent;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApSerialNumberProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class ApSnmpAgentProfileCfgCollectorOperationBuilderTest {

  @MockBean
  private ApRepository apRepository;

  @MockBean
  private VenueRepository venueRepository;

  @SpyBean
  private ApSnmpAgentProfileCfgCollectorOperationBuilder apSnmpAgentProfileCfgCollectorOperationBuilder;

  @Test
  public void testGetEntityClass() {
    assertThat(apSnmpAgentProfileCfgCollectorOperationBuilder.entityClass())
        .isEqualTo(ApSnmpAgentProfile.class);
  }

  @Nested
  class testBuildConfig {

    @Test
    public void givenEntityActionIsDelete() {
      var operations = apSnmpAgentProfileCfgCollectorOperationBuilder
          .build(new TxEntity<>(new ApSnmpAgentProfile(randomId()), EntityAction.DELETE),
              emptyTxChanges()).get(0);
      assertThat(operations.getDocCount())
          .isZero();
    }

    @Test
    public void givenAddApSnmpAgentProfile() {
      var apSnmpAgentProfile = new ApSnmpAgentProfile(randomId());

      String tenantId = randomId();

      apSnmpAgentProfile.setTenant(new Tenant(tenantId));
      apSnmpAgentProfile.setPolicyName(randomName());

      var apSnmpV2Agent1 = new ApSnmpV2Agent(randomId());
      apSnmpV2Agent1.setCommunityName(randomName());
      var apSnmpV2Agent2 = new ApSnmpV2Agent(randomId());
      apSnmpV2Agent2.setCommunityName(randomName());
      apSnmpAgentProfile.setSnmpV2Agents(List.of(apSnmpV2Agent1, apSnmpV2Agent2));
      var apSnmpV3Agent = new ApSnmpV3Agent(randomId());
      apSnmpV3Agent.setUserName(randomName());
      apSnmpAgentProfile.setSnmpV3Agents(List.of(apSnmpV3Agent));

      var apSnmpAgent = new ApSnmpAgent();
      apSnmpAgent.setEnableApSnmp(true);
      apSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);

      var ap1 = new Ap(randomSerialNumber());
      ap1.setName(randomName());
      ap1.setApSnmpAgent(apSnmpAgent);

      doReturn(List.of(ap1)).when(apRepository)
          .findByTenantIdAndApSnmpAgentApSnmpAgentProfileIdIn(tenantId,
              List.of(apSnmpAgentProfile.getId()));

      var venueSnmpAgent = new VenueSnmpAgent();
      venueSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);
      venueSnmpAgent.setEnableApSnmp(true);

      var venue = new Venue(randomId());
      venue.setName(randomName());
      venue.setApSnmpAgent(venueSnmpAgent);

      var apGroup = new ApGroup(randomId());
      apGroup.setName(randomName());

      venue.setApGroups(List.of(apGroup));

      var ap2 = new Ap(randomSerialNumber());
      ap2.setName(randomName());
      apGroup.setAps(List.of(ap2));

      doReturn(List.of(venue)).when(venueRepository)
          .findByTenantIdAndApSnmpAgentApSnmpAgentProfileIdIn(tenantId,
              List.of(apSnmpAgentProfile.getId()));

      var builder = Operations.newBuilder();

      apSnmpAgentProfileCfgCollectorOperationBuilder
          .config(builder, apSnmpAgentProfile, EntityAction.ADD);

      var docMap = builder.build().getDocMap();

      assertThat(docMap.get("id"))
          .extracting(Value::getStringValue)
          .isEqualTo(apSnmpAgentProfile.getId());
      assertThat(docMap.get("name"))
          .extracting(Value::getStringValue)
          .isEqualTo(apSnmpAgentProfile.getPolicyName());
      assertThat(docMap.get("tenantId"))
          .extracting(Value::getStringValue)
          .isEqualTo(apSnmpAgentProfile.getTenant().getId());
      this.compareListStructValue(docMap, "v2Agents",
          tuple(apSnmpV2Agent1.getId(), apSnmpV2Agent1.getCommunityName()),
          tuple(apSnmpV2Agent2.getId(), apSnmpV2Agent2.getCommunityName()));
      this.compareListStructValue(docMap, "v3Agents",
          tuple(apSnmpV3Agent.getId(), apSnmpV3Agent.getUserName()));
      this.compareListStructValue(docMap, "aps", tuple(ap1.getId(), ap1.getName()),
          tuple(ap2.getId(), ap2.getName()));
      this.compareListStructValue(docMap, "venues", tuple(venue.getId(), venue.getName()));
      this.compareListStringValue(docMap, "communityNames", apSnmpV2Agent1.getCommunityName(),
          apSnmpV2Agent2.getCommunityName());
      this.compareListStringValue(docMap, "userNames", apSnmpV3Agent.getUserName());
      this.compareListStringValue(docMap, "apSerialNumbers", ap1.getId(), ap2.getId());
      this.compareListStringValue(docMap, "apNames", ap1.getName(), ap2.getName());
      this.compareListStringValue(docMap, "venueIds", venue.getId());
      this.compareListStringValue(docMap, "venueNames", venue.getName());
    }

    private void compareListStructValue(Map<String, Value> docMap, String key, Tuple... values) {
      var valuesList = docMap.get(key).getListValue().getValuesList();
      assertThat(valuesList)
          .hasSameSizeAs(values)
          .extracting(v -> v.getStructValue().getFieldsOrThrow("id").getStringValue(),
              v -> v.getStructValue().getFieldsOrThrow("name").getStringValue())
          .containsExactly(values);
    }

    private void compareListStringValue(Map<String, Value> docMap, String key, String... values) {
      var valuesList = docMap.get(key).getListValue().getValuesList();
      assertThat(valuesList)
          .hasSameSizeAs(values)
          .extracting(Value::getStringValue)
          .containsExactly(values);
    }

    @Test
    public void givenAddApSnmpAgentProfileWithoutRelations() {
      var apSnmpAgentProfile = new ApSnmpAgentProfile(randomId());

      apSnmpAgentProfile.setTenant(new Tenant(randomId()));
      apSnmpAgentProfile.setPolicyName(randomName());

      var builder = Operations.newBuilder();

      apSnmpAgentProfileCfgCollectorOperationBuilder
          .config(builder, apSnmpAgentProfile, EntityAction.ADD);

      var docMap = builder.build().getDocMap();

      assertThat(docMap.get("id"))
          .extracting(Value::getStringValue)
          .isEqualTo(apSnmpAgentProfile.getId());
      assertThat(docMap.get("name"))
          .extracting(Value::getStringValue)
          .isEqualTo(apSnmpAgentProfile.getPolicyName());
      assertThat(docMap.get("tenantId"))
          .extracting(Value::getStringValue)
          .isEqualTo(apSnmpAgentProfile.getTenant().getId());
      this.compareListStructValue(docMap, "v2Agents");
      this.compareListStructValue(docMap, "v3Agents");
      this.compareListStructValue(docMap, "aps");
      this.compareListStructValue(docMap, "venues");
      this.compareListStringValue(docMap, "communityNames");
      this.compareListStringValue(docMap, "userNames");
      this.compareListStringValue(docMap, "apSerialNumbers");
      this.compareListStringValue(docMap, "apNames");
      this.compareListStringValue(docMap, "venueIds");
      this.compareListStringValue(docMap, "venueNames");
    }

    private static Consumer<Value> assertApActivationsValue(Collection<Ap> aps) {
      return actualApActivationsValue -> {
        assertThat(actualApActivationsValue.getKindCase()).isEqualTo(KindCase.LIST_VALUE);
        assertThat(actualApActivationsValue.hasListValue()).isTrue();
        assertThat(actualApActivationsValue.getListValue()).isNotNull()
            .satisfies(listValue -> {
              assertThat(listValue.getValuesCount()).isEqualTo(aps.size());
              assertThat(listValue.getValuesList()).hasSize(aps.size())
                  .allSatisfy(value -> {
                    assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                    assertThat(value.hasStructValue()).isTrue();
                    assertThat(value.getStructValue()).isNotNull();
                  })
                  .extracting(Value::getStructValue)
                  .containsExactlyInAnyOrderElementsOf(() -> aps.stream()
                      .map(apLanPort -> Struct.newBuilder()
                          .putFields(Key.VENUE_ID,
                              ValueUtils.stringValue(apLanPort.getApGroup().getVenue().getId()))
                          .putFields(Key.AP_SERIAL_NUMBER,
                              ValueUtils.stringValue(apLanPort.getId())).build()).iterator());
            });
      };
    }

    private List<VenueApSerialNumberProjection> mockVenueApSerialNumberProjections(
        List<Ap> apList) {
      return apList.stream()
          .map(v -> new VenueApSerialNumberProjection(
              v.getApGroup().getVenue().getId(),
              v.getId()))
          .toList();
    }

    @Test
    void testApSnmpAgentProfileWithActivations() {
      var tenantId = randomId();
      var apSnmpAgentProfile = new ApSnmpAgentProfile(randomId());
      apSnmpAgentProfile.setTenant(new Tenant(tenantId));
      apSnmpAgentProfile.setPolicyName(randomName());

      var apSnmpAgent = new ApSnmpAgent();
      apSnmpAgent.setEnableApSnmp(true);
      apSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);

      var ap1 = new Ap(randomSerialNumber());
      ap1.setName(randomName());
      ap1.setApSnmpAgent(apSnmpAgent);

      doReturn(List.of(ap1)).when(apRepository)
          .findByTenantIdAndApSnmpAgentApSnmpAgentProfileIdIn(tenantId,
              List.of(apSnmpAgentProfile.getId()));

      var venueSnmpAgent = new VenueSnmpAgent();
      venueSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);
      venueSnmpAgent.setEnableApSnmp(true);

      var venue = new Venue(randomId());
      venue.setName(randomName());
      venue.setApSnmpAgent(venueSnmpAgent);

      var ap2 = new Ap(randomSerialNumber());
      ap2.setName(randomName());

      var apGroup = new ApGroup(randomId());
      apGroup.setName(randomName());
      apGroup.setVenue(venue);
      apGroup.setAps(List.of(ap1, ap2));

      venue.setApGroups(List.of(apGroup));
      ap1.setApGroup(apGroup);
      ap2.setApGroup(apGroup);

      doReturn(List.of(venue)).when(venueRepository)
          .findByTenantIdAndApSnmpAgentApSnmpAgentProfileIdIn(tenantId,
              List.of(apSnmpAgentProfile.getId()));

      var builder = Operations.newBuilder();

      doReturn(mockVenueApSerialNumberProjections(List.of(ap1)))
          .when(apRepository)
          .findVenueApSerialNumberByTenantIdAndProfileId(anyString(), anyString());

      doReturn(List.of(ap2))
          .when(apRepository)
          .findByApGroupVenueIdAndTenantId(anyString(), anyString());

      apSnmpAgentProfileCfgCollectorOperationBuilder
          .config(builder, apSnmpAgentProfile, EntityAction.ADD);

      var docMap = builder.build().getDocMap();

      assertThat(docMap.get(EsConstants.Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(apSnmpAgentProfile.getId());
      assertThat(docMap.get(EsConstants.Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(apSnmpAgentProfile.getPolicyName());
      assertThat(docMap.get(EsConstants.Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(apSnmpAgentProfile.getTenant().getId());

      assertThat(docMap)
          .hasEntrySatisfying(Key.AP_ACTIVATIONS, assertApActivationsValue(List.of(ap1)));
    }

    @Test
    public void testGetIndex() {
      assertThat(apSnmpAgentProfileCfgCollectorOperationBuilder.index())
          .isEqualTo(EsConstants.Index.AP_SNMP_AGENT_PROFILE);
    }

    @Nested
    class testHasModification {

      @Test
      public void givenEntityActionIsNotModify() {
        assertThat(apSnmpAgentProfileCfgCollectorOperationBuilder
            .hasModification(EntityAction.ADD, Collections.emptySet(), false)).isTrue();
      }

      @Test
      public void givenAnyFields() {
        assertThat(apSnmpAgentProfileCfgCollectorOperationBuilder
            .hasModification(EntityAction.MODIFY, Set.of("name"), false)).isTrue();
        assertThat(apSnmpAgentProfileCfgCollectorOperationBuilder
            .hasModification(EntityAction.MODIFY, Set.of("updatedDate"), false)).isTrue();
      }
    }
  }
}
