package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.service.template.TemplateManagementService;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApGroupsProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

@WifiUnitTest
class AbstractNetworkCfgCollectorOperationBuilderTest {

  @MockBean
  private RepositoryUtil repositoryUtil;
  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private TemplateManagementService templateManagementService;
  @SpyBean
  private MockNetworkCfgCollectorOperationBuilder mockNetworkCfgCollectorOperationBuilder;
  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("Tenant");

  @Nested
  class testBuildConfig {

    final String venueId = randomId();
    final String apGroupId = randomId();
    final String apSerialNumber = "123456789012";

    @Test
    void givenAddNetworkTemplate() {
      Operations.Builder builder = Operations.newBuilder();
      final var network = getNetwork(true);
      mockNetworkCfgCollectorOperationBuilder.config(builder, network, EntityAction.ADD);

      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(EsConstants.Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(network.getId());
      assertEquals(EsConstants.Value.TYPE_NETWORK, docMap.get(EsConstants.Key.TYPE));
      assertThat(docMap.get(EsConstants.Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(network.getTenant().getId());
      assertThat(docMap.get(EsConstants.Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(network.getName());
      assertThat(docMap.get(EsConstants.Key.IS_TEMPLATE))
          .extracting(Value::getBoolValue)
          .isEqualTo(true);
      assertThat(docMap.get(EsConstants.Key.VENUE_APGROUPS))
          .extracting(Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(Value.class))
          .extracting(Value::getStructValue)
          .allSatisfy(vap -> {
            assertEquals(venueId, vap.getFieldsOrThrow(EsConstants.Key.VENUE_ID).getStringValue());
            assertTrue(vap.getFieldsOrThrow(EsConstants.Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertEquals(1,
                vap.getFieldsOrThrow(EsConstants.Key.APGROUP_IDS).getListValue().getValuesCount());
            assertTrue(
                vap.getFieldsOrThrow(EsConstants.Key.APGROUP_IDS).getListValue().getValuesList()
                    .contains(ValueUtils.stringValue(apGroupId)));
          });
    }

    @Test
    void givenAddHotspot20Network() {
      Operations.Builder builder = Operations.newBuilder();
      final var network = getNetwork(false);
      mockNetworkCfgCollectorOperationBuilder.config(builder, network, EntityAction.ADD);

      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(EsConstants.Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(network.getId());
      assertEquals(EsConstants.Value.TYPE_NETWORK, docMap.get(EsConstants.Key.TYPE));
      assertThat(docMap.get(EsConstants.Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(network.getTenant().getId());
      assertThat(docMap.get(EsConstants.Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(network.getName());
      assertThat(docMap.get(EsConstants.Key.IS_TEMPLATE))
          .extracting(Value::getBoolValue)
          .isEqualTo(false);
      assertThat(docMap.get(EsConstants.Key.VENUE_APGROUPS))
          .extracting(Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(Value.class))
          .extracting(Value::getStructValue)
          .allSatisfy(vap -> {
            assertEquals(venueId, vap.getFieldsOrThrow(EsConstants.Key.VENUE_ID).getStringValue());
            assertTrue(vap.getFieldsOrThrow(EsConstants.Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertEquals(1,
                vap.getFieldsOrThrow(EsConstants.Key.APGROUP_IDS).getListValue().getValuesCount());
            assertTrue(
                vap.getFieldsOrThrow(EsConstants.Key.APGROUP_IDS).getListValue().getValuesList()
                    .contains(ValueUtils.stringValue(apGroupId)));
          });
    }

    @NotNull
    private Network getNetwork(boolean isTemplate) {
      var tenant = new Tenant(txCtxExtension.getTenantId());
      final var network = (Hotspot20Network) Generators.hotspot20Network().generate();
      network.setTenant(tenant);
      network.setCreatedDate(new Date());
      network.setUpdatedDate(new Date());
      network.setIsTemplate(isTemplate);
      network.getWlan().setNetwork(network);

      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

      var venueApGroupsQueryProjections = new ArrayList<>();
      venueApGroupsQueryProjections.add(
          new MockVenueApGroupsProjection(
              venueId, apGroupId, true));

      doReturn(new PageImpl<>(venueApGroupsQueryProjections), Page.empty()).when(networkRepository)
          .findVenueApGroupsByNetworkId(
              eq(txCtxExtension.getTenantId()), eq(network.getId()), any());
      return network;
    }
  }

  @Data
  @AllArgsConstructor
  static class MockVenueApGroupsProjection implements VenueApGroupsProjection {
    String venueId;
    String apGroups;
    boolean isAllApGroups;

    @Override
    public boolean getIsAllApGroups() {
      return true;
    }
  }

  static class MockNetworkCfgCollectorOperationBuilder extends AbstractNetworkCfgCollectorOperationBuilder<Network> {

    @Override
    public Class<Network> entityClass() {
      return Network.class;
    }
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public MockNetworkCfgCollectorOperationBuilder mockNetworkCfgCollectorOperationBuilder() {
      return new MockNetworkCfgCollectorOperationBuilder();
    }

  }

}
