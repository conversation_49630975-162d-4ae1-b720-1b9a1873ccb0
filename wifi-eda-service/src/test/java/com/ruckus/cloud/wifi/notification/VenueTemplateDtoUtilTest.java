package com.ruckus.cloud.wifi.notification;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.notification.VenueApCountExecutorFactory.VenueImpactApCountByModelsExecutor;
import com.ruckus.cloud.wifi.notification.VenueApCountExecutorFactory.VenueImpactApCountExecutor;
import com.ruckus.cloud.wifi.notification.dto.VenueTemplateDto;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
class VenueTemplateDtoUtilTest {

  @SpyBean
  private VenueTemplateDtoUtil venueTemplateDtoUtil;

  @MockBean
  private ExtendedVenueServiceCtrl venueServiceCtrl;

  @MockBean
  private VenueApCountExecutorFactory venueApCountExecutorFactory;

  @MockBean
  private VenueImpactApCountExecutor venueImpactApCountExecutor;

  @MockBean
  private VenueImpactApCountByModelsExecutor venueImpactApCountByModelsExecutor;

  @Test
  public void testUpdateImpactedApNumbers() {
    String tenantId = "tenantId";
    List<ApVersion> apVersions = List.of(new ApVersion());
    List<Venue> venues = mockVenues(2);
    List<VenueTemplateDto> dtos = mockVenueTemplateDtosByVenues(venues);
    Map<String, Integer> venueApCountMap = mockVenueApCount(venues, 3);
    when(venueApCountExecutorFactory.createImpactApCountExecutor(eq(tenantId), any(), any()))
        .thenReturn(venueImpactApCountExecutor);
    when(venueImpactApCountExecutor.execute()).thenReturn(venueApCountMap);

    venueTemplateDtoUtil.updateImpactedApNumbers(dtos, tenantId, apVersions);

    Assertions.assertThat(dtos)
        .hasSize(2)
        .allSatisfy(e ->
            Assertions.assertThat(e)
                .extracting(VenueTemplateDto::getImpactedNumberOfAps)
                .isEqualTo(3));
  }

  @Test
  public void testGetImpactedApNumbers() {
    String tenantId = "tenantId";
    List<ApVersion> apVersions = List.of(new ApVersion());
    List<Venue> venues = mockVenues(2);
    List<VenueTemplateDto> dtos = mockVenueTemplateDtosByVenues(venues);
    Map<String, Integer> venueApCountMap = mockVenueApCount(venues, 3);
    when(venueApCountExecutorFactory.createImpactApCountExecutor(eq(tenantId), any(), any()))
        .thenReturn(venueImpactApCountExecutor);
    when(venueImpactApCountExecutor.execute()).thenReturn(venueApCountMap);

    Map<String, Integer> impactedApNumbers =
        venueTemplateDtoUtil.getImpactedApNumbers(dtos, tenantId, apVersions);

    assertNotNull(impactedApNumbers);
    assertEquals(2, impactedApNumbers.size());
    assertEquals(3, impactedApNumbers.get(venues.get(0).getId()));
  }

  @Test
  void testGetImpactedApNumbersByModels() {
    List<Venue> venues = mockVenues(2);
    List<VenueTemplateDto> dtos = mockVenueTemplateDtosByVenues(venues);
    Map<String, Integer> venueApCountMap = mockVenueApCount(venues, 3);
    when(venueApCountExecutorFactory.createImpactApCountByModelsExecutor(anyList()))
        .thenReturn(venueImpactApCountByModelsExecutor);
    when(venueImpactApCountExecutor.execute()).thenReturn(venueApCountMap);

    venueTemplateDtoUtil.updateImpactedApNumbersByApModel(dtos, venueApCountMap);

    Assertions.assertThat(dtos)
        .hasSize(2)
        .allSatisfy(e ->
            Assertions.assertThat(e)
                .extracting(VenueTemplateDto::getImpactedNumberOfAps)
                .isEqualTo(3));
  }

  private List<Venue> mockVenues(int number) {
    List<Venue> venues = new LinkedList<>();
    for (int i = 0; i < number; i++) {
      Venue venue = new Venue();
      venue.setId(UUID.randomUUID().toString());
      venues.add(venue);
    }
    return venues;
  }

  private List<VenueTemplateDto> mockVenueTemplateDtosByVenues(List<Venue> venues) {
    List<VenueTemplateDto> dtos = new LinkedList<>();
    for (Venue venue : venues) {
      VenueTemplateDto dto = new VenueTemplateDto();
      dto.setId(venue.getId());
      dtos.add(dto);
    }
    return dtos;
  }

  private Map<String, Integer> mockVenueApCount(List<Venue> venues, int apCount) {
    return venues.stream()
        .collect(Collectors.toMap(v -> v.getId(), v -> apCount));
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public VenueTemplateDtoUtil venueTemplateDtoUtil(
        ExtendedVenueServiceCtrl venueServiceCtrl,
        VenueApCountExecutorFactory venueApCountExecutorFactory) {
      return new VenueTemplateDtoUtil(
          venueServiceCtrl,
          venueApCountExecutorFactory);
    }
  }
}