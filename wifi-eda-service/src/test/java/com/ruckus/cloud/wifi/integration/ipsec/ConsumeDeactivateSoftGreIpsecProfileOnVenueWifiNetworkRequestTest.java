package com.ruckus.cloud.wifi.integration.ipsec;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.IpsecProfileTestFixture.randomIpsecProfile;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("IpsecProfileTest")
@WifiIntegrationTest
@FeatureFlag(enable = {
    FlagNames.WIFI_IPSEC_PSK_OVER_NETWORK
})
public class ConsumeDeactivateSoftGreIpsecProfileOnVenueWifiNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void givenValidDeactivation(Tenant tenant, NetworkVenue networkVenue) {
    var ipsecProfile =
        repositoryUtil.createOrUpdate(
            randomIpsecProfile(tenant, p -> {
            }), tenant.getId());
    var softGreProfile =
        repositoryUtil.createOrUpdate(
            randomSoftGreProfile(tenant, p -> {
            }), tenant.getId());
    var activation = new SoftGreProfileNetworkVenueActivation();
    activation.setTenant(tenant);
    activation.setNetworkVenue(networkVenue);
    activation.setSoftGreProfile(softGreProfile);
    activation.setIpsecProfile(ipsecProfile);
    repositoryUtil.createOrUpdate(activation, tenant.getId());

    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        txCtxExtension.getRequestId(),
        CfgAction.DEACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK,
        randomName(),
        new RequestParams()
            .addPathVariable("venueId", networkVenue.getVenue().getId())
            .addPathVariable("wifiNetworkId", networkVenue.getNetwork().getId())
            .addPathVariable("softGreProfileId", softGreProfile.getId())
            .addPathVariable("ipsecProfileId", ipsecProfile.getId()),
        "");

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(
            ViewmodelCollector::getOperationsList, InstanceOfAssertFactories.list(Operations.class))
        .hasSize(2)
        .filteredOn(o -> o.getOpType() == OpType.MOD && o.getId().equals(ipsecProfile.getId()))
        .first()
        .matches(
            o ->
                o.getDocMap()
                    .get(EsConstants.Key.ACTIVATIONS)
                    .getListValue()
                    .getValuesList()
                    .isEmpty());

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
        .matches(
            a ->
                a.getStep()
                    .equals(ApiFlowNames.DEACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK));

    var operations =
        assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(
                m -> m.getPayload().getOperationsList(),
                InstanceOfAssertFactories.list(Operation.class));
    operations
        .filteredOn(
            o ->
                o.getAction() == Action.MODIFY
                    && o.getId().equals(networkVenue.getVenue().getId())
                    && o.hasVenue())
        .hasSize(1)
        .extracting(o -> o.getVenue().getCcmMultipleTunnel())
        .first()
        .matches(tunnels -> tunnels.getSoftGreSettingCount() == 0)
        .matches(tunnels -> tunnels.getIpsecSettingCount() == 0);

    operations
        .filteredOn(
            o ->
                o.getAction() == Action.MODIFY
                    && o.getId().equals(networkVenue.getId())
                    && o.hasWlanVenue())
        .hasSize(1)
        .extracting(Operation::getWlanVenue)
        .first()
        .matches(wlanVenue -> !wlanVenue.getTunnelEnabled());
  }

  @Test
  void givenLanPortNotExists(Tenant tenant, NetworkVenue networkVenue) {
    var ipsecProfile =
        repositoryUtil.createOrUpdate(
            randomIpsecProfile(tenant, p -> {
            }), tenant.getId());
    var softGreProfile =
        repositoryUtil.createOrUpdate(
            randomSoftGreProfile(tenant, p -> {
            }), tenant.getId());

    assertThatThrownBy(
        () ->
            messageUtil.sendWifiCfgRequest(
                tenant.getId(),
                txCtxExtension.getRequestId(),
                CfgAction.DEACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK,
                randomName(),
                new RequestParams()
                    .addPathVariable("venueId", networkVenue.getVenue().getId())
                    .addPathVariable("wifiNetworkId", networkVenue.getNetwork().getId())
                    .addPathVariable("softGreProfileId", softGreProfile.getId())
                    .addPathVariable("ipsecProfileId", ipsecProfile.getId()),
                ""))
        .isNotNull()
        .rootCause()
        .isNotNull()
        .isInstanceOf(ObjectNotFoundException.class);

    messageCaptors
        .assertThat(
            messageCaptors.getDdccmMessageCaptor(),
            messageCaptors.getCmnCfgCollectorMessageCaptor())
        .doesNotSendByTenant(tenant.getId());

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
        .matches(a -> a.getStep()
            .equals(ApiFlowNames.DEACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK));
  }
}
