package com.ruckus.cloud.wifi.requirement.feature;

import static org.mockito.ArgumentMatchers.eq;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class LbsFeatureTest {

  @SpyBean
  private LbsFeature unit;

  @Nested
  class WhenIsLbsEnabled {

    @Test
    void givenLbsServerProfileWithoutFF(Venue venue) {
      venue.setLbsServerProfile(Generators.lbsServerProfile().generate());
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_LBS_TOGGLE)
    void givenWithoutLbsServerProfileWithFF(Venue venue) {
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_LBS_TOGGLE)
    void givenLbsServerProfileWithFF(Venue venue) {
      venue.setLbsServerProfile(Generators.lbsServerProfile().generate());
      BDDAssertions.then(unit.test(venue)).isTrue();
    }

  }
}
