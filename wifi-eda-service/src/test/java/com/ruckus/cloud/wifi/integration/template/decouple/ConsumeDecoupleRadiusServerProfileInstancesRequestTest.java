package com.ruckus.cloud.wifi.integration.template.decouple;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;


@Slf4j
@WifiIntegrationTest
public class ConsumeDecoupleRadiusServerProfileInstancesRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void init() {
  clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void decouple_radiusServerProfileInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();
    // create msp tenant & template
    String mspTenantId = mspTenant.getId();
    repositoryUtil.createOrUpdate(mspTenant, mspTenantId);
    Radius template = RadiusTestFixture.randomRadius(mspTenant);
    template.setIsTemplate(true);
    repositoryUtil.createOrUpdate(template, mspTenantId);

    // create ec tenant & instance
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, ecTenantId);
    Radius instance = RadiusTestFixture.randomRadius(ecTenant);
    instance.setIsTemplate(false);
    instance.setIsEnforced(true);
    instance.setTemplateId(template.getId());
    repositoryUtil.createOrUpdate(instance, ecTenantId);

    // Verify before decouple
    Radius before = repositoryUtil.find(Radius.class, instance.getId(), ecTenantId, false);
    assertAll(
    () -> assertEquals(template.getId(), before.getTemplateId()),
    () -> assertFalse(before.getIsTemplate()),
    () -> assertTrue(before.getIsEnforced())
    );

    // Perform decouple
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_RADIUS_SERVER_PROFILE_INSTANCES, userName, template.getId());
    assertDdccmCfgRequestNotSent(ecTenantId);
    // Verify after decouple
    Radius after = repositoryUtil.find(Radius.class, instance.getId(), ecTenantId, false);
    assertAll(
    () -> assertNull(after.getTemplateId()),
    () -> assertFalse(after.getIsEnforced()),
    () -> assertFalse(after.getIsTemplate())
    );
    // Verify template is not affected
    changeTxCtxTenant(mspTenantId);
    Radius templateAfter = repositoryUtil.find(Radius.class, template.getId(), mspTenantId, true);
    assertAll(
    () -> assertTrue(templateAfter.getIsTemplate()),
    () -> assertEquals(template.getName(), templateAfter.getName())
    );

    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instance.getId());
  }

  @Test
  public void decouple_noInstances_shouldSucceed() {
  var userName = txCtxExtension.getUserName();
  Tenant mspTenant = TenantTestFixture.randomTenant((t) -> {});
  String mspTenantId = mspTenant.getId();
  repositoryUtil.createOrUpdate(mspTenant, mspTenantId);
  Radius template = RadiusTestFixture.randomRadius(mspTenant);
  template.setIsTemplate(true);
  repositoryUtil.createOrUpdate(template, mspTenantId);
  Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
  String ecTenantId = ecTenant.getId();
  repositoryUtil.createOrUpdate(ecTenant, ecTenantId);
  // Do not create any instance
  changeTxCtxTenant(ecTenantId);
  sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_RADIUS_SERVER_PROFILE_INSTANCES, userName, template.getId());
  assertDdccmCfgRequestNotSent(ecTenantId);
  // Should not throw exception and no instance is decoupled
  assertTrue(true);
  assertViewmodelOpsNotSent(ecTenantId);
  }

  private void validateCmnCfgCollectorMessage(String tenantId, String requestId, String instanceId) {
  final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
  assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();
  assertThat(cmnCfgCollectorMessage.getPayload())
  .matches(p -> p.getTenantId().equals(tenantId))
  .matches(p -> p.getRequestId().equals(requestId));
  assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
  .hasSize(1)
  .filteredOn(o -> o.getId().equals(instanceId)).first()
  .matches(o -> EsConstants.Index.RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
  .matches(o -> o.getOpType().equals(OpType.MOD))
  .matches(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(tenantId))
  .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue());
//  .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue());
  }

  private void assertViewmodelOpsNotSent(String tenantId) {
  messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }
} 