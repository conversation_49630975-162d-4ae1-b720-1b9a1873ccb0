package com.ruckus.cloud.wifi.service.compatibility.impl;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willAnswer;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;

import com.ruckus.cloud.compatibility.contract.model.Compatibility;
import com.ruckus.cloud.compatibility.contract.model.DeviceAggregation;
import com.ruckus.cloud.compatibility.contract.model.DeviceAggregationGroup;
import com.ruckus.cloud.compatibility.contract.model.DeviceIdentityWrapper;
import com.ruckus.cloud.compatibility.contract.requirement.RequirementChecker;
import com.ruckus.cloud.compatibility.contract.requirement.RequirementCheckerGroup;
import com.ruckus.cloud.wifi.requirement.ExtendedRequirementCheckerProvider;
import com.ruckus.cloud.wifi.service.compatibility.ExtendedDeviceService;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModel;
import com.ruckus.cloud.wifi.viewmodel.ApRequirement;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@SuppressWarnings("unchecked")
@WifiUnitTest
class ExtendedCompatibilityServiceImplTest {
  @MockBean private ExtendedDeviceService deviceService;
  @MockBean private ExtendedRequirementCheckerProvider requirementCheckerProvider;

  @SpyBean ExtendedCompatibilityServiceImpl unit;

  @Nested
  class WhenCheckCompatibilitiesByNetworks {
    private final List<String> networkIds =
        Stream.generate(CommonTestFixture::randomId).limit(10).toList();

    private final List<DeviceAggregationGroup<ApFirmwareModel>> deviceAggregationGroups =
        networkIds.stream()
            .limit(8)
            .map(
                networkId ->
                    DeviceAggregationGroup.<ApFirmwareModel>builder()
                        .id(networkId)
                        .deviceAggregations(
                            (List<DeviceAggregation<ApFirmwareModel>>) mock(List.class))
                        .build())
            .toList();
    private final List<RequirementCheckerGroup<ApRequirement, ApFirmwareModel>>
        requirementCheckerGroups =
            networkIds.stream()
                .skip(2)
                .map(
                    networkId ->
                        RequirementCheckerGroup.<ApRequirement, ApFirmwareModel>builder()
                            .id(networkId)
                            .requirementCheckers(
                                (List<RequirementChecker<ApRequirement, ApFirmwareModel>>)
                                    mock(List.class))
                            .build())
                .toList();

    @Test
    void thenCalculateCompatibilities() {
      willReturn(deviceAggregationGroups)
          .given(deviceService)
          .getDeviceAggregationGroupsByNetworks(anyList());
      willReturn(requirementCheckerGroups)
          .given(requirementCheckerProvider)
          .getRequirementCheckerGroupsByNetworks(anyList());
      willAnswer(
              invocation ->
                  Compatibility.builder().id(invocation.getArgument(0, String.class)).build())
          .given(unit)
          .calculateCompatibility(anyString(), anyList(), anyList());

      BDDAssertions.then(unit.checkCompatibilitiesByNetworks(networkIds))
          .isNotNull()
          .hasSize(networkIds.size())
          .extracting(Compatibility::getId)
          .containsExactlyElementsOf(networkIds);

      then(deviceService).should().getDeviceAggregationGroupsByNetworks(eq(networkIds));
      then(requirementCheckerProvider)
          .should()
          .getRequirementCheckerGroupsByNetworks(eq(networkIds));

      final var idCaptor = ArgumentCaptor.forClass(String.class);
      final var checkerCaptor = ArgumentCaptor.forClass(List.class);
      final var deviceAggregationCaptor = ArgumentCaptor.forClass(List.class);

      then(unit)
          .should(times(networkIds.size()))
          .calculateCompatibility(
              idCaptor.capture(), checkerCaptor.capture(), deviceAggregationCaptor.capture());
      final var ids = idCaptor.getAllValues();
      final var checkers = checkerCaptor.getAllValues();
      final var deviceAggregations = deviceAggregationCaptor.getAllValues();
      for (int i = 0; i < networkIds.size(); i++) {
        final var networkId = networkIds.get(i);
        final var checker =
            requirementCheckerGroups.stream()
                .filter(c -> networkId.equals(c.getId()))
                .findFirst()
                .map(RequirementCheckerGroup::getRequirementCheckers)
                .orElse(List.of());
        final var deviceAggregation =
            deviceAggregationGroups.stream()
                .filter(d -> networkId.equals(d.getId()))
                .findFirst()
                .map(DeviceAggregationGroup::getDeviceAggregations)
                .orElse(List.of());
        assertThat(ids.get(i)).isEqualTo(networkId);
        assertThat(checkers.get(i)).isEqualTo(checker);
        assertThat(deviceAggregations.get(i)).isEqualTo(deviceAggregation);
      }
    }
  }

  @Nested
  class WhenCheckDeviceCompatibilities {
    @Test
    void thenCalculateWithFeatureName() {
      final var venueId = randomId();
      final var featureName = randomName();
      final RequirementChecker<ApRequirement, ApFirmwareModel> checker =
          mock(RequirementChecker.class);
      final var deviceIds =
          Stream.generate(CommonTestFixture::randomSerialNumber).limit(10).toList();
      final var devices =
          deviceIds.stream()
              .map(
                  id ->
                      DeviceIdentityWrapper.builder()
                          .id(id)
                          .device(mock(ApFirmwareModel.class))
                          .build())
              .toList();

      willReturn(Optional.of(checker))
          .given(requirementCheckerProvider)
          .findRequirementChecker(anyString());
      willReturn(devices).given(deviceService).getDevicesWithIdentity(anyList());
      willAnswer(
              invocation ->
                  Compatibility.builder()
                      .id(invocation.getArgument(1, DeviceIdentityWrapper.class).getId())
                      .build())
          .given(unit)
          .calculateDeviceCompatibility(anyList(), any());

      BDDAssertions.then(unit.checkDeviceCompatibilities(venueId, deviceIds, featureName))
          .isNotNull()
          .hasSize(devices.size())
          .extracting(Compatibility::getId)
          .containsExactlyElementsOf(deviceIds);

      then(requirementCheckerProvider).should().findRequirementChecker(eq(featureName));
      then(deviceService).should().getDevicesWithIdentity(eq(deviceIds));
      final var deviceCaptor = ArgumentCaptor.forClass(DeviceIdentityWrapper.class);
      then(unit)
          .should(times(deviceIds.size()))
          .calculateDeviceCompatibility(eq(List.of(checker)), deviceCaptor.capture());
      assertThat(deviceCaptor.getAllValues()).containsExactlyInAnyOrderElementsOf(devices);
    }
  }

  @Nested
  class WhenCheckDeviceCompatibilitiesByNetwork {
    @Test
    void thenCalculateDeviceCompatibilities() {
      final var networkId = randomId();
      final var deviceIds =
          Stream.generate(CommonTestFixture::randomSerialNumber).limit(10).toList();
      final var devices =
          deviceIds.stream()
              .map(
                  id ->
                      DeviceIdentityWrapper.builder()
                          .id(id)
                          .device(mock(ApFirmwareModel.class))
                          .build())
              .toList();
      final var checkers =
          (List<RequirementChecker<ApRequirement, ApFirmwareModel>>) mock(List.class);

      willReturn(checkers)
          .given(requirementCheckerProvider)
          .getRequirementCheckersByNetwork(anyString());
      willReturn(devices).given(deviceService).getDevicesWithIdentity(anyList());
      willAnswer(
              invocation ->
                  Compatibility.builder()
                      .id(invocation.getArgument(1, DeviceIdentityWrapper.class).getId())
                      .build())
          .given(unit)
          .calculateDeviceCompatibility(anyList(), any());

      BDDAssertions.then(unit.checkDeviceCompatibilitiesByNetwork(networkId, deviceIds))
          .isNotNull()
          .hasSize(devices.size())
          .extracting(Compatibility::getId)
          .containsExactlyElementsOf(deviceIds);

      then(requirementCheckerProvider).should().getRequirementCheckersByNetwork(eq(networkId));
      then(deviceService).should().getDevicesWithIdentity(eq(deviceIds));
      final var deviceCaptor = ArgumentCaptor.forClass(DeviceIdentityWrapper.class);
      then(unit)
          .should(times(deviceIds.size()))
          .calculateDeviceCompatibility(eq(checkers), deviceCaptor.capture());
      assertThat(deviceCaptor.getAllValues()).containsExactlyInAnyOrderElementsOf(devices);
    }
  }

  @Nested
  class WhenCheckCompatibilitiesByNetworksOfVenue {
    private final String venueId = randomId();
    private final List<String> networkIds =
        Stream.generate(CommonTestFixture::randomId).limit(10).toList();
    ;

    @Test
    void thenCalculateWithFeatureName() {
      final var featureName = randomName();
      final RequirementChecker<ApRequirement, ApFirmwareModel> checker =
          mock(RequirementChecker.class);
      final var deviceAggregationGroups =
          networkIds.stream()
              .map(
                  networkId ->
                      DeviceAggregationGroup.<ApFirmwareModel>builder()
                          .id(networkId)
                          .deviceAggregations(mock(List.class))
                          .build())
              .toList();

      willReturn(Optional.of(checker))
          .given(requirementCheckerProvider)
          .findRequirementChecker(anyString());
      willReturn(deviceAggregationGroups)
          .given(deviceService)
          .getDeviceAggregationGroupsByNetworks(anyList());
      willAnswer(
              invocation ->
                  Compatibility.builder().id(invocation.getArgument(0, String.class)).build())
          .given(unit)
          .calculateCompatibility(anyString(), anyList(), anyList());

      BDDAssertions.then(
              unit.checkCompatibilitiesByNetworksOfVenue(venueId, networkIds, featureName))
          .isNotNull()
          .hasSize(networkIds.size())
          .extracting(Compatibility::getId)
          .containsExactlyElementsOf(networkIds);

      then(requirementCheckerProvider).should().findRequirementChecker(eq(featureName));
      then(deviceService).should().getDeviceAggregationGroupsByNetworks(eq(networkIds));

      final var idCaptor = ArgumentCaptor.forClass(String.class);
      final var checkerCaptor = ArgumentCaptor.forClass(List.class);
      final var deviceAggregationCaptor = ArgumentCaptor.forClass(List.class);

      then(unit)
          .should(times(networkIds.size()))
          .calculateCompatibility(
              idCaptor.capture(), checkerCaptor.capture(), deviceAggregationCaptor.capture());
      final var ids = idCaptor.getAllValues();
      final var checkers = checkerCaptor.getAllValues();
      final var deviceAggregations = deviceAggregationCaptor.getAllValues();
      for (int i = 0; i < networkIds.size(); i++) {
        final var networkId = networkIds.get(i);
        final var deviceAggregation =
            deviceAggregationGroups.stream()
                .filter(d -> networkId.equals(d.getId()))
                .findFirst()
                .map(DeviceAggregationGroup::getDeviceAggregations)
                .orElse(List.of());
        assertThat(ids.get(i)).isEqualTo(networkId);
        assertThat(checkers.get(i)).singleElement().isEqualTo(checker);
        assertThat(deviceAggregations.get(i)).isEqualTo(deviceAggregation);
      }
    }

    @Test
    void thenCalculateWithoutFeatureName() {
      final var venueCheckers =
          Stream.<RequirementChecker<ApRequirement, ApFirmwareModel>>generate(
                  () -> mock(RequirementChecker.class))
              .limit(5)
              .toList();
      final var networkCheckerGroups =
          networkIds.stream()
              .map(
                  networkId ->
                      RequirementCheckerGroup.<ApRequirement, ApFirmwareModel>builder()
                          .id(networkId)
                          .requirementCheckers(
                              Stream.<RequirementChecker<ApRequirement, ApFirmwareModel>>generate(
                                      () -> mock(RequirementChecker.class))
                                  .limit(5)
                                  .toList())
                          .build())
              .toList();
      final var deviceAggregationGroups =
          networkIds.stream()
              .map(
                  networkId ->
                      DeviceAggregationGroup.<ApFirmwareModel>builder()
                          .id(networkId)
                          .deviceAggregations(mock(List.class))
                          .build())
              .toList();

      willReturn(venueCheckers)
          .given(requirementCheckerProvider)
          .getRequirementCheckers(anyString());
      willReturn(networkCheckerGroups)
          .given(requirementCheckerProvider)
          .getRequirementCheckerGroupsByNetworks(anyList());
      willReturn(deviceAggregationGroups)
          .given(deviceService)
          .getDeviceAggregationGroupsByNetworks(anyList());
      willAnswer(
              invocation ->
                  Compatibility.builder().id(invocation.getArgument(0, String.class)).build())
          .given(unit)
          .calculateCompatibility(anyString(), anyList(), anyList());

      BDDAssertions.then(unit.checkCompatibilitiesByNetworksOfVenue(venueId, networkIds))
          .isNotNull()
          .hasSize(networkIds.size())
          .extracting(Compatibility::getId)
          .containsExactlyElementsOf(networkIds);

      then(requirementCheckerProvider).should().getRequirementCheckers(eq(venueId));
      then(requirementCheckerProvider)
          .should()
          .getRequirementCheckerGroupsByNetworks(eq(networkIds));
      then(deviceService).should().getDeviceAggregationGroupsByNetworks(eq(networkIds));

      final var idCaptor = ArgumentCaptor.forClass(String.class);
      final var checkerCaptor = ArgumentCaptor.forClass(List.class);
      final var deviceAggregationCaptor = ArgumentCaptor.forClass(List.class);

      then(unit)
          .should(times(networkIds.size()))
          .calculateCompatibility(
              idCaptor.capture(), checkerCaptor.capture(), deviceAggregationCaptor.capture());
      final var ids = idCaptor.getAllValues();
      final var checkers = checkerCaptor.getAllValues();
      final var deviceAggregations = deviceAggregationCaptor.getAllValues();
      for (int i = 0; i < networkIds.size(); i++) {
        final var networkId = networkIds.get(i);
        final var checker =
            Stream.concat(
                    venueCheckers.stream(),
                    networkCheckerGroups.stream()
                        .filter(c -> networkId.equals(c.getId()))
                        .findFirst()
                        .map(RequirementCheckerGroup::getRequirementCheckers)
                        .orElseThrow()
                        .stream())
                .toList();
        final var deviceAggregation =
            deviceAggregationGroups.stream()
                .filter(d -> networkId.equals(d.getId()))
                .findFirst()
                .map(DeviceAggregationGroup::getDeviceAggregations)
                .orElse(List.of());
        assertThat(ids.get(i)).isEqualTo(networkId);
        assertThat(checkers.get(i)).containsExactlyInAnyOrderElementsOf(checker);
        assertThat(deviceAggregations.get(i)).isEqualTo(deviceAggregation);
      }
    }
  }

  @Nested
  class WhenCheckCompatibilitiesByVenuesOfNetwork {
    @Test
    void thenCalculateCompatibilitiesWithNetworkCheckersAndEachVenueCheckers() {
      final var networkId = randomId();
      final var venueIds = Stream.generate(CommonTestFixture::randomId).limit(10).toList();
      final var venueCheckers =
          Stream.<RequirementChecker<ApRequirement, ApFirmwareModel>>generate(
                  () -> mock(RequirementChecker.class))
              .limit(5)
              .toList();
      final var venueCheckerGroups =
          venueIds.stream()
              .map(
                  venueId ->
                      RequirementCheckerGroup.<ApRequirement, ApFirmwareModel>builder()
                          .id(venueId)
                          .requirementCheckers(
                              Stream.<RequirementChecker<ApRequirement, ApFirmwareModel>>generate(
                                      () -> mock(RequirementChecker.class))
                                  .limit(5)
                                  .toList())
                          .build())
              .toList();
      final var deviceAggregationGroups =
          venueIds.stream()
              .map(
                  venueId ->
                      DeviceAggregationGroup.<ApFirmwareModel>builder()
                          .id(venueId)
                          .deviceAggregations(mock(List.class))
                          .build())
              .toList();

      willReturn(venueCheckers)
          .given(requirementCheckerProvider)
          .getRequirementCheckersByNetwork(anyString());
      willReturn(venueCheckerGroups)
          .given(requirementCheckerProvider)
          .getRequirementCheckerGroups(anyList());
      willReturn(deviceAggregationGroups)
          .given(deviceService)
          .getDeviceAggregationGroups(anyList());
      willAnswer(
              invocation ->
                  Compatibility.builder().id(invocation.getArgument(0, String.class)).build())
          .given(unit)
          .calculateCompatibility(anyString(), anyList(), anyList());

      BDDAssertions.then(unit.checkCompatibilitiesByVenuesOfNetwork(networkId, venueIds))
          .isNotNull()
          .hasSize(venueIds.size())
          .extracting(Compatibility::getId)
          .containsExactlyElementsOf(venueIds);

      then(requirementCheckerProvider).should().getRequirementCheckersByNetwork(eq(networkId));
      then(requirementCheckerProvider).should().getRequirementCheckerGroups(eq(venueIds));
      then(deviceService).should().getDeviceAggregationGroups(eq(venueIds));

      final var idCaptor = ArgumentCaptor.forClass(String.class);
      final var checkerCaptor = ArgumentCaptor.forClass(List.class);
      final var deviceAggregationCaptor = ArgumentCaptor.forClass(List.class);

      then(unit)
          .should(times(venueIds.size()))
          .calculateCompatibility(
              idCaptor.capture(), checkerCaptor.capture(), deviceAggregationCaptor.capture());
      final var ids = idCaptor.getAllValues();
      final var checkers = checkerCaptor.getAllValues();
      final var deviceAggregations = deviceAggregationCaptor.getAllValues();
      for (int i = 0; i < venueIds.size(); i++) {
        final var venueId = venueIds.get(i);
        final var checker =
            Stream.concat(
                    venueCheckers.stream(),
                    venueCheckerGroups.stream()
                        .filter(c -> venueId.equals(c.getId()))
                        .findFirst()
                        .map(RequirementCheckerGroup::getRequirementCheckers)
                        .orElseThrow()
                        .stream())
                .toList();
        final var deviceAggregation =
            deviceAggregationGroups.stream()
                .filter(d -> venueId.equals(d.getId()))
                .findFirst()
                .map(DeviceAggregationGroup::getDeviceAggregations)
                .orElse(List.of());
        assertThat(ids.get(i)).isEqualTo(venueId);
        assertThat(checkers.get(i)).containsExactlyInAnyOrderElementsOf(checker);
        assertThat(deviceAggregations.get(i)).isEqualTo(deviceAggregation);
      }
    }
  }

  @Test
  void whenGetRequirementCheckProvider() {
    BDDAssertions.then(unit.getRequirementCheckerProvider())
        .isNotNull()
        .isEqualTo(requirementCheckerProvider);
  }

  @Test
  void whenGetDeviceService() {
    BDDAssertions.then(unit.getDeviceService()).isNotNull().isEqualTo(deviceService);
  }
}
