package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.*;

import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusProfile;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenantId001');
    INSERT INTO radius (id, tenant, is_template)
        VALUES ('radiusId001', 'tenantId001', 'false');
    INSERT INTO auth_radius_service(id, tenant, radius)
        VALUES ('radiusServiceId001', 'tenantId001', 'radiusId001');
    INSERT INTO auth_radius_profile (id, tenant, auth_radius_service, hotspot20support_enabled)
        VALUES ('radiusProfileId001', 'tenantId001', 'radiusServiceId001', 'true');
    INSERT INTO auth_radius_service(id, tenant, radius)
        VALUES ('radiusServiceId002', 'tenantId001', 'radiusId001');    
    INSERT INTO auth_radius_profile (id, tenant, auth_radius_service, hotspot20support_enabled)
        VALUES ('radiusProfileId002', 'tenantId001', 'radiusServiceId002', 'false');     
    """)
class AuthRadiusProfileRepositoryTest {

  private static final String TENANT_ID = "tenantId001";

  @Autowired
  AuthRadiusProfileRepository repository;

  @Test
  void findByTenantIdAndAuthRadiusServiceId() {

    AuthRadiusProfile profile = repository
        .findByTenantIdAndAuthRadiusServiceId(TENANT_ID, "radiusServiceId001");
    assertEquals("radiusProfileId001", profile.getId());

    AuthRadiusProfile profile1 = repository
        .findByTenantIdAndAuthRadiusServiceIdAndHotspot20SupportEnabled(TENANT_ID, "radiusServiceId001", true);
    assertEquals("radiusProfileId001", profile1.getId());

    AuthRadiusProfile profile2 = repository
        .findByTenantIdAndAuthRadiusServiceIdAndHotspot20SupportEnabled(TENANT_ID, "radiusServiceId002", false);
    assertEquals("radiusProfileId002", profile2.getId());

    AuthRadiusProfile profile3 = repository
        .findByTenantIdAndAuthRadiusServiceIdAndHotspot20SupportEnabled(TENANT_ID, "radiusServiceId002", true);
    assertNull(profile3);
  }
}