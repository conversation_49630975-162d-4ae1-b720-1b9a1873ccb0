package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.REMOTE_TRIGGER_METHOD;
import static com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.REMOTE_TRIGGER_PARAMETERS;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueApModel;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.RPC;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.sql.Date;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = {FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
public class ConsumeUpgradeVenueRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private MessageCaptors messageCaptors;

  private final String REQUEST_ID = randomTxId();

  @Test
  public void testUpgradeVenue(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.500") ApVersion defaultAbf1Version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.1") ApVersion defaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.2") ApVersion targetVersion) {
    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    this.createVenueFirmwareVersion(venue, defaultAbf1Version, "eol-ap-2022-12");
    UpgradeSchedule schedule = this.createUpgradeSchedule(venue, List.of(targetVersion));
    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");
    ApFirmwareUpgradeRequest r310ApFwUpgradeReq = createAndJoinApToVenue(venue, "R310");

    messageUtil.sendWifiTrigger(
        venue.getTenant().getId(),
        REQUEST_ID,
        prepareUpgradeVenuePayload(schedule.getId(), venue.getId()));

    // Then
    validateVenueCurrentVersion(venue, targetVersion.getId());
    assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), schedule.getId()));
    assertFalse(isApFirmwareUpgradeRequestUpdated(r310ApFwUpgradeReq.getId(), schedule.getId()));
    validateDdccmCfgMessageResult(tenant, targetVersion.getId());
  }

  @Test
  public void testUpgradeVenue_withMultiTargetVersions(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.500") ApVersion abf1DefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.550") ApVersion abf2DefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.103.600") ApVersion abf3DefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.551") ApVersion abf2TargetVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.103.601") ApVersion abf3TargetVersion) {
    Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(abf1DefaultVersion));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    this.createVenueFirmwareVersion(venue, abf1DefaultVersion, "eol-ap-2022-12");
    this.createVenueFirmwareVersion(venue, abf2DefaultVersion, "ABF2-3R");
    UpgradeSchedule schedule =
        this.createUpgradeSchedule(venue, List.of(abf2TargetVersion, abf3TargetVersion));
    ApFirmwareUpgradeRequest r770ApFwUpgradeReq = createAndJoinApToVenue(venue, "R770");
    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");
    ApFirmwareUpgradeRequest r310ApFwUpgradeReq = createAndJoinApToVenue(venue, "R310");

    messageUtil.sendWifiTrigger(
        venue.getTenant().getId(),
        REQUEST_ID,
        prepareUpgradeVenuePayload(schedule.getId(), venue.getId()));

    // Then
    validateVenueCurrentVersion(venue, abf3TargetVersion.getId());
    assertTrue(isApFirmwareUpgradeRequestUpdated(r770ApFwUpgradeReq.getId(), schedule.getId()));
    assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), schedule.getId()));
    assertFalse(isApFirmwareUpgradeRequestUpdated(r310ApFwUpgradeReq.getId(), schedule.getId()));
    validateDdccmCfgMessageResult(tenant, abf3TargetVersion.getId());
  }

  @Tag("EthernetPortProfileTest")
  @Test
  public void testUpgradeVenue_withMultiTargetVersionsAndExistUnsupportedApModelSpecificAttributesInVenue(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.500") ApVersion abf1DefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.550") ApVersion abf2DefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.600") ApVersion abf3DefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.551") ApVersion abf2TargetVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.103.599") ApVersion abf3TargetVersion) {
    Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(abf1DefaultVersion));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    this.createVenueFirmwareVersion(venue, abf1DefaultVersion, "eol-ap-2022-12");
    this.createVenueFirmwareVersion(venue, abf2DefaultVersion, "ABF2-3R");
    createVenueApModelSpecificAttributes(tenant, venue, List.of("R750", "H550", "R350:R350E"));
    UpgradeSchedule schedule =
        this.createUpgradeSchedule(venue, List.of(abf2TargetVersion, abf3TargetVersion));
    ApFirmwareUpgradeRequest r770ApFwUpgradeReq = createAndJoinApToVenue(venue, "R770");
    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");
    ApFirmwareUpgradeRequest r310ApFwUpgradeReq = createAndJoinApToVenue(venue, "R310");

    messageUtil.sendWifiTrigger(
        venue.getTenant().getId(),
        REQUEST_ID,
        prepareUpgradeVenuePayload(schedule.getId(), venue.getId()));

    // Then
    validateVenueCurrentVersion(venue, abf3TargetVersion.getId());
    assertTrue(isApFirmwareUpgradeRequestUpdated(r770ApFwUpgradeReq.getId(), schedule.getId()));
    assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), schedule.getId()));
    assertFalse(isApFirmwareUpgradeRequestUpdated(r310ApFwUpgradeReq.getId(), schedule.getId()));
    validateDdccmCfgMessageResult(tenant, abf3TargetVersion.getId(), List.of("R750", "H550"));
    Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
    validateVenuesAndApModelSpecificAttributes(List.of(updatedVenue), abf3TargetVersion, List.of("R750", "H550"),
        List.of("R350:R350E"));
  }

  private void validateVenueCurrentVersion(Venue venue, String version) {
    Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
    assertThat(updatedVenue)
        .isNotNull()
        .matches(v -> v.getWifiFirmwareVersion().getId().equals(version));
  }

  void validateVenuesAndApModelSpecificAttributes(List<Venue> updatedVenues, ApVersion targetVersion,
      List<String> modelsWithSpecificAttrs, List<String> unsupportedModels) {
    List<String> modelsWithSpecificAttrsAfterUpdate = new ArrayList<>(modelsWithSpecificAttrs);
    modelsWithSpecificAttrsAfterUpdate.removeIf(unsupportedModels::contains);

    assertThat(updatedVenues)
        .hasSize(updatedVenues.size())
        .allMatch(venue -> targetVersion.getId().equals(venue.getWifiFirmwareVersion().getId()))
        .flatExtracting(Venue::getModelSpecificAttributes)
        .extracting(VenueApModelSpecificAttributes::getModel)
        .hasSize(modelsWithSpecificAttrsAfterUpdate.size())
        .doesNotContainAnyElementsOf(unsupportedModels)
        .containsAll(modelsWithSpecificAttrsAfterUpdate);
  }

  private void validateDdccmCfgMessageResult(Tenant tenant, String targetActiveVersion) {
    validateDdccmCfgMessageResult(tenant, targetActiveVersion, null);
  }

  private void validateDdccmCfgMessageResult(Tenant tenant, String targetActiveVersion, List<String> venueApModels) {
    boolean isOnlyUpdateNonActiveVersion = targetActiveVersion == null;
    if (isOnlyUpdateNonActiveVersion) {
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());
    } else {
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenant.getId(), REQUEST_ID);

      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(REQUEST_ID);

      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(tenant.getId());

      assertThatNoException()
          .isThrownBy(
              () ->
                  assertThat(ddccmCfgRequestMessage.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList)
                      .asList()
                      .isNotEmpty()
                      .extracting(Operation.class::cast)
                      .allMatch(op -> op.getAction() == Action.MODIFY)
                      .allSatisfy(
                          op ->
                              assertThat(op)
                                  .extracting(Operation::getCommonInfo)
                                  .matches(commonInfo -> REQUEST_ID.equals(commonInfo.getRequestId()))
                                  .matches(commonInfo -> tenant.getId().equals(commonInfo.getTenantId()))
                                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                      .allSatisfy(
                          op -> {
                            assertThat(op)
                                .extracting(Operation::getVenue)
                                .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasVersion)
                                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getVersion)
                                .matches(v -> targetActiveVersion.equals(v.getValue()));

                            if (!CollectionUtils.isEmpty(venueApModels)) {
                              assertThat(op)
                                  .extracting(Operation::getVenue)
                                  .extracting(venue -> venue.getVenueApModelsList().stream().map(
                                      VenueApModel::getModelName).toList())
                                  .matches(models -> models.size() == venueApModels.size() & models.containsAll(
                                      venueApModels));
                            }
                          })
          );
    }
  }

  private boolean isApFirmwareUpgradeRequestUpdated(String apFirmwareUpgradeRequestId, String requestId) {
    return isApFirmwareUpgradeRequestUpdated(apFirmwareUpgradeRequestId, requestId, null);
  }

  private boolean isApFirmwareUpgradeRequestUpdated(
      String apFirmwareUpgradeRequestId, String requestId, String targetVersion) {
    ApFirmwareUpgradeRequest upgradeRequest =
        repositoryUtil.find(ApFirmwareUpgradeRequest.class, apFirmwareUpgradeRequestId);
    assertNotNull(upgradeRequest);

    return upgradeRequest.getRequestId().equals(requestId)
        && (Objects.isNull(targetVersion) || upgradeRequest.getTargetVersion().equals(targetVersion));
  }

  private ApFirmwareUpgradeRequest createAndJoinApToVenue(Venue venue, String apModel) {
    Ap ap = ApTestFixture.randomAp(venue);
    ap.setModel(apModel);

    return repositoryUtil.createOrUpdate(
        newApFirmwareUpgradeRequest(randomId(), venue.getTenant(), venue, ap),
        venue.getTenant().getId(),
        randomTxId());
  }

  private VenueFirmwareVersion createVenueFirmwareVersion(Venue venue, ApVersion apVersion, String branchType) {
    return repositoryUtil.createOrUpdate(
        newVenueFirmwareVersion(venue, apVersion, branchType),
        venue.getTenant().getId(),
        randomTxId());
  }

  private UpgradeSchedule createUpgradeSchedule(Venue venue, List<ApVersion> apVersions) {
    ScheduleTimeSlot sts = repositoryUtil.createOrUpdate(this.newScheduleTimeSlot(), venue.getTenant().getId(),
        randomTxId());
    UpgradeSchedule schedule = repositoryUtil.createOrUpdate(
        this.newUpgradeSchedule(sts, apVersions.get(0), venue), venue.getTenant().getId(), randomTxId());

    apVersions.forEach(apVersion -> {
      UpgradeScheduleFirmwareVersion usfv1 = new UpgradeScheduleFirmwareVersion(randomId());
      usfv1.setApFirmwareVersion(apVersion);
      usfv1.setUpgradeSchedule(schedule);
      usfv1.setTenant(venue.getTenant());
      repositoryUtil.createOrUpdate(usfv1, venue.getTenant().getId(), randomTxId());
    });

    return schedule;
  }

  private void createVenueApModelSpecificAttributes(Tenant tenant, Venue venue, List<String> models) {
    for (String model : models) {
      VenueApModelSpecificAttributes venueApModelSpecificAttributes = new VenueApModelSpecificAttributes();
      venueApModelSpecificAttributes.setVenue(venue);
      venueApModelSpecificAttributes.setModel(model);
      venueApModelSpecificAttributes.setTenant(tenant);
      venueApModelSpecificAttributes = repositoryUtil.createOrUpdate(venueApModelSpecificAttributes,
          tenant.getId(), randomTxId());

      EthernetPortProfile ethernetPortProfile = new EthernetPortProfile();
      ethernetPortProfile.setTenant(tenant);
      ethernetPortProfile.setApLanPortId(1);
      ethernetPortProfile = repositoryUtil.createOrUpdate(ethernetPortProfile, tenant.getId(),
          randomTxId());

      VenueLanPort venueLanPort = new VenueLanPort();
      venueLanPort.setVenueApModelSpecificAttributes(venueApModelSpecificAttributes);
      venueLanPort.setTenant(tenant);
      venueLanPort.setApLanPortProfile(ethernetPortProfile);
      venueLanPort.setPortId("1");
      repositoryUtil.createOrUpdate(venueLanPort, tenant.getId(), randomTxId());

      venueApModelSpecificAttributes.setLanPorts(List.of(venueLanPort));
      repositoryUtil.createOrUpdate(venueApModelSpecificAttributes, tenant.getId(), randomTxId());
    }
  }

  private ApFirmwareUpgradeRequest newApFirmwareUpgradeRequest(
      String id, Tenant tenant, Venue venue, Ap ap) {
    ApFirmwareUpgradeRequest apFirmwareUpgradeRequest = new ApFirmwareUpgradeRequest();
    apFirmwareUpgradeRequest.setId(id);
    apFirmwareUpgradeRequest.setRequestId(randomId());
    apFirmwareUpgradeRequest.setTenant(tenant);
    apFirmwareUpgradeRequest.setVenueId(venue.getId());
    apFirmwareUpgradeRequest.setSerialNumber(ap.getId());
    apFirmwareUpgradeRequest.setModel(ap.getModel());
    apFirmwareUpgradeRequest.setSourceVersion(venue.getWifiFirmwareVersion().getId());
    apFirmwareUpgradeRequest.setTargetVersion(venue.getWifiFirmwareVersion().getId());
    return apFirmwareUpgradeRequest;
  }

  private Map<String, Object> prepareUpgradeVenuePayload(String scheduleId, String venueId) {
    Map<String, Object> data = new HashMap<>();
    data.put(REMOTE_TRIGGER_METHOD, RPC.UPGRADE_VENUE);
    // schedule id, venue id, version
    data.put(REMOTE_TRIGGER_PARAMETERS, new Object[]{scheduleId, venueId, null});
    return data;
  }

  private ScheduleTimeSlot newScheduleTimeSlot() {
    ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
    Instant startTime = Instant.now();
    sts.setStartDateTime(Date.from(startTime));
    sts.setEndDateTime(Date.from(startTime.plusSeconds(60 * 60 * 2)));
    sts.setTotalCapacityVenue(2500);
    return sts;
  }

  private UpgradeSchedule newUpgradeSchedule(ScheduleTimeSlot timeSlot, ApVersion version, Venue venue) {
    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(timeSlot);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(version);
    us.setTenant(venue.getTenant());
    us.setVenue(venue);
    return us;
  }

  private VenueFirmwareVersion newVenueFirmwareVersion(Venue venue, ApVersion version, String branchType) {
    VenueFirmwareVersion vfv = new VenueFirmwareVersion();
    vfv.setId(venue.getId() + branchType);
    vfv.setVenue(venue);
    vfv.setTenant(venue.getTenant());
    vfv.setBranchType(branchType);
    vfv.setCurrentFirmwareVersion(version);
    return vfv;
  }
}
