package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.google.protobuf.BoolValue;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenuePortal;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.redis.YamlPropertySourceFactory;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenuePortalTestFixture;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.PropertySource;

@WifiUnitTest
@PropertySource(value = "classpath:application.yml", factory = YamlPropertySourceFactory.class)
public class DdccmVenuePortalOperationBuilderTest {

  @SpyBean
  private DdccmVenuePortalOperationBuilder ddccmVenuePortalOperationBuilder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyMspTenantId");

  @Test
  public void testCloudpathDefaultValueEncryptMacIpEnabled() {
    // Given
    var portal = mockVenuePortalForCloudpathGuestPortal();

    // When
    List<Operation> operations = ddccmVenuePortalOperationBuilder.build(new NewTxEntity<>(portal),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenuePortal());
    assertNotNull(operations.get(0).getVenuePortal().getExternalPortalURL());
    assertEquals(BoolValue.of(false), operations.get(0).getVenuePortal().getEncryptMacIpEnabled());
  }

  @Test
  public void testClickthroughRucksOne() {
    // Given
    VenuePortal portal = mockVenuePortalForClickthroughGuestPortal();
    // When
    List<Operation> operations = ddccmVenuePortalOperationBuilder.build(new NewTxEntity<>(portal),
        emptyTxChanges());

    assertThat(operations)
        .isNotNull()
        .hasSize(1)
        .first()
        .extracting(Operation::getVenuePortal)
        .isNotNull()
        .extracting(venuePortal -> venuePortal.getExternalPortalURL().endsWith("portal"))
        .isNotNull();

  }

  @Test
  public void tesGuestWorkflow() {
    // Given
    var portal = mockVenuePortalForGuestWorkflow();
    // When
    var operations = ddccmVenuePortalOperationBuilder.build(new NewTxEntity<>(portal),
        emptyTxChanges());

    assertThat(operations)
        .isNotNull()
        .hasSize(1)
        .first()
        .extracting(Operation::getVenuePortal)
        .isNotNull()
        .extracting(venuePortal -> venuePortal.getExternalPortalURL().endsWith("portal"))
        .isNotNull();

  }

  private VenuePortal mockVenuePortalForCloudpathGuestPortal() {
    var result = new VenuePortal(uuid());

    var tenant = new Tenant("9b9920ec0f9643a2b2399741c82923cd");
    tenant.setRecoveryPsk("1111222233334444");
    result.setTenant(tenant);
    var networkVenue = new NetworkVenue();
    var venue = new Venue(uuid());
    networkVenue.setVenue(venue);
    result.setNetworkVenue(networkVenue);

    var guestPortal = new GuestPortal();
    guestPortal.setExternalPortalUrl("http://1.2.3.4");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
    result.setNetworkPortal(guestPortal);

    return result;
  }

  private VenuePortal mockVenuePortalForClickthroughGuestPortal() {
    var tenant = new Tenant(txCtxExtension.newRandomId());
    var result = VenuePortalTestFixture.randomVenuePortal(tenant, venuePortal -> {
      var networkVenue = new NetworkVenue();
      var venue = new Venue(uuid());
      networkVenue.setVenue(venue);
      venuePortal.setNetworkVenue(networkVenue);
      var guestPortal = GuestNetworkTestFixture.guestPortal();
      guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.ClickThrough);
      guestPortal.setTenant(tenant);
      venuePortal.setNetworkPortal(guestPortal);
    });
    return result;
  }

  private VenuePortal mockVenuePortalForGuestWorkflow() {
    var tenant = new Tenant(txCtxExtension.newRandomId());
    var result = VenuePortalTestFixture.randomVenuePortal(tenant, venuePortal -> {
      var networkVenue = new NetworkVenue();
      var venue = new Venue(uuid());
      networkVenue.setVenue(venue);
      venuePortal.setNetworkVenue(networkVenue);
      var guestPortal = GuestNetworkTestFixture.guestPortal();
      guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Workflow);
      guestPortal.setTenant(tenant);
      venuePortal.setNetworkPortal(guestPortal);
    });
    return result;
  }

  private String uuid() {
    return UUID.randomUUID().toString().replace("-", "");
  }
}
