package com.ruckus.cloud.wifi.service.integration;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.repository.VenueFirmwareVersionRepository;
import com.ruckus.cloud.wifi.service.VenueFirmwareVersionService;
import com.ruckus.cloud.wifi.service.impl.VenueFirmwareVersionServiceImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@WifiJpaDataTest
public class VenueFirmwareVersionServiceTest {

  @Autowired private VenueFirmwareVersionService venueFirmwareVersionService;

  @Autowired private VenueFirmwareVersionRepository venueFirmwareVersionRepository;

  @Autowired private RepositoryUtil repositoryUtil;

  @Test
  public void testSave_createAndGenerateCustomIdWhenIdNull(
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version) {
    VenueFirmwareVersion vfv = newVenueFirmwareVersion(version, venue);
    venueFirmwareVersionService.save(vfv);

    Optional<VenueFirmwareVersion> createdRes =
        venueFirmwareVersionRepository.findByVenueIdAndBranchType(
            venue.getId(), vfv.getBranchType());
    Assertions.assertTrue(createdRes.isPresent());
    Assertions.assertEquals(vfv.getVenue().getId() + vfv.getBranchType(), createdRes.get().getId());
  }

  @Test
  public void testSave_createNonCustomIdVfv(
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version) {
    String nonCustomId = UUID.randomUUID().toString();
    VenueFirmwareVersion vfv = newVenueFirmwareVersion(version, venue);
    vfv.setId(nonCustomId);
    venueFirmwareVersionService.save(vfv);

    Optional<VenueFirmwareVersion> createdRes =
        venueFirmwareVersionRepository.findByVenueIdAndBranchType(
            venue.getId(), vfv.getBranchType());
    Assertions.assertTrue(createdRes.isPresent());
    Assertions.assertNotEquals(
        vfv.getVenue().getId() + vfv.getBranchType(), createdRes.get().getId());
    Assertions.assertEquals(nonCustomId, createdRes.get().getId());
  }

  @Test
  public void testSave_updateNonCustomIdVfv(
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.2")
          ApVersion newVersion) {
    String nonCustomId = UUID.randomUUID().toString();
    VenueFirmwareVersion vfv = newVenueFirmwareVersion(version, venue);
    vfv.setId(nonCustomId);
    venueFirmwareVersionRepository.save(vfv);

    vfv.setCurrentFirmwareVersion(newVersion);
    venueFirmwareVersionService.save(vfv);

    List<VenueFirmwareVersion> updatedRes =
        venueFirmwareVersionRepository.findByVenueId(venue.getId());
    Assertions.assertEquals(1, updatedRes.size(), "check duplicate data");
    Assertions.assertNotEquals(
        vfv.getVenue().getId() + vfv.getBranchType(), updatedRes.get(0).getId());
    Assertions.assertEquals(nonCustomId, updatedRes.get(0).getId());
  }

  @Test
  public void testSaveAll_createAndGenerateCustomIdWhenIdNull(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version) {
    List<Venue> venues =
        List.of(
            VenueTestFixture.randomVenue(tenant, c -> {}),
            VenueTestFixture.randomVenue(tenant, c -> {}));
    List<VenueFirmwareVersion> venueFirmwareVersions =
        venues.stream()
            .map(
                venue -> {
                  repositoryUtil.createOrUpdate(
                      venue, tenant.getId(), UUID.randomUUID().toString());
                  return newVenueFirmwareVersion(version, venue);
                })
            .collect(Collectors.toList());

    venueFirmwareVersionService.saveAll(venueFirmwareVersions);

    venueFirmwareVersions.forEach(
        vfv -> {
          Optional<VenueFirmwareVersion> createdRes =
              venueFirmwareVersionRepository.findByVenueIdAndBranchType(
                  vfv.getVenue().getId(), vfv.getBranchType());

          Assertions.assertTrue(createdRes.isPresent());
          Assertions.assertEquals(
              vfv.getVenue().getId() + vfv.getBranchType(), createdRes.get().getId());
        });
  }

  @Test
  public void testSaveAll_updateNonCustomIdVenueFWVersions(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version) {
    List<Venue> venues =
        List.of(
            VenueTestFixture.randomVenue(tenant, c -> {}),
            VenueTestFixture.randomVenue(tenant, c -> {}));
    List<VenueFirmwareVersion> venueFirmwareVersions =
        venues.stream()
            .map(
                venue -> {
                  String nonCustomId = UUID.randomUUID().toString();
                  repositoryUtil.createOrUpdate(
                      venue, tenant.getId(), UUID.randomUUID().toString());
                  VenueFirmwareVersion vfv = newVenueFirmwareVersion(version, venue);
                  vfv.setId(nonCustomId);
                  return venueFirmwareVersionRepository.save(vfv);
                })
            .collect(Collectors.toList());

    venueFirmwareVersionRepository.saveAll(venueFirmwareVersions);

    venueFirmwareVersions.forEach(
        vfv -> {
          List<VenueFirmwareVersion> updatedRes =
              venueFirmwareVersionRepository.findByVenueId(vfv.getVenue().getId());
          Assertions.assertEquals(1, updatedRes.size(), "check duplicate data");
          Assertions.assertNotEquals(
              vfv.getVenue().getId() + vfv.getBranchType(), updatedRes.get(0).getId());
          Assertions.assertEquals(vfv.getId(), updatedRes.get(0).getId());
        });
  }

  private VenueFirmwareVersion newVenueFirmwareVersion(ApVersion version, Venue venue) {
    return newVenueFirmwareVersion(version, venue, "test-branch");
  }

  private VenueFirmwareVersion newVenueFirmwareVersion(ApVersion version, Venue venue, String branchType) {
    VenueFirmwareVersion vfv = new VenueFirmwareVersion();
    vfv.setCurrentFirmwareVersion(version);
    vfv.setTenant(venue.getTenant());
    vfv.setVenue(venue);
    vfv.setBranchType(branchType);
    return vfv;
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    @ConditionalOnMissingBean
    public VenueFirmwareVersionService venueFirmwareVersionService(
        VenueFirmwareVersionRepository venueFirmwareVersionRepository) {
      return new VenueFirmwareVersionServiceImpl(venueFirmwareVersionRepository);
    }
  }
}
