package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.handler.asyncjob.FirmwareBindingAsyncJobHelper.FIRMWARE_BINDING_JOB_NAME;
import static com.ruckus.cloud.wifi.kafka.WifiCommonHeader.REQUEST_ID;
import static com.ruckus.cloud.wifi.kafka.WifiCommonHeader.TENANT_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.context.WifiAsyncJobContext;
import com.ruckus.cloud.wifi.kafka.publisher.WifiAsyncJobPublisher;
import com.ruckus.cloud.wifi.test.TestConstants;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.utils.Time;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
public class ConsumeFirmwareBindingRequestTest {
  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  private MessageCaptors messageCaptors;

  private final String DEFAULT_VERSION = "6.2.0.103.500";

  private final String TARGET_VERSION = "6.2.1.103.500";

  @Test
  public void asyncRelease621To620Tenant_whenDisableAutoSchedule(Tenant adminTenant,
      ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = DEFAULT_VERSION) ApVersion defaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = TARGET_VERSION) ApVersion targetVersion) {
    String requestId = randomTxId();
    String userName = randomName();
    List<Tenant> tenantList = List.of(
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));
    tenantList.forEach(tenant -> repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId()));
    List<String> tenantIdList = tenantList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());

    var params = new RequestParams();
    params.addPathVariable("firmwareVersion", targetVersion.getId());
    params.addRequestParam("schedule", false);

    tenantList.forEach(t -> {
      Ap ap1 = ApTestFixture.randomAp(apGroup, ap -> ap.setModel("R560"));
      repositoryUtil.createOrUpdate(ap1, t.getId(), randomTxId());
    });

    // When
    messageUtil.sendWifiCfgRequest(
        adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, userName, params, tenantIdList);

    // Waiting for async jobs finished
    tenantList.forEach(tenant -> {
      assertThat(messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId()))
          .isNotNull();
      messageUtil.awaitMessageProceed(kafkaTopicProvider.getWifiAsyncJob(),
          WifiAsyncJobPublisher.getPartitionKey(WifiAsyncJobContext.builder()
              .jobName(FIRMWARE_BINDING_JOB_NAME)
              .tenantId(tenant.getId())
              .build()));
    });

    //Do not sync to ddccm
    messageCaptors.assertThat(
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getWifiScheduleCreate()
    ).doesNotSendByTenants(tenantList.stream().map(AbstractBaseEntity::getId)
        .collect(Collectors.toSet()));

    // Then
    validateResult(tenantList, targetVersion);
  }

  /*
   * beta fw to whatever Tenant
   * Expected: update Tenant.LatestBetaFW only
   */
  @Test
  public void asyncReleaseBetaFirmware(Tenant adminTenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = DEFAULT_VERSION) ApVersion defaultVersion) {
    String requestId = randomTxId();
    String userName = randomName();
    ApVersion betaFirmware = ApVersionTestFixture.betaApVersion("9.5.2.7.9527", a -> {});
    repositoryUtil.createOrUpdate(betaFirmware, adminTenant.getId(), randomTxId());
    List<Tenant> tenantList = List.of(
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));
    tenantList.forEach(tenant -> repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId()));
    List<String> tenantIdList = tenantList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());
    var params = new RequestParams();
    params.addPathVariable("firmwareVersion", betaFirmware.getId());
    params.addRequestParam("schedule", true);

    // When
    messageUtil.sendWifiCfgRequest(adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, userName, params,
        tenantIdList);

    // Waiting for async jobs finished
    tenantList.forEach(tenant -> {
          assertThat(messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId()))
              .isNotNull();
          messageUtil.awaitMessageProceed(kafkaTopicProvider.getWifiAsyncJob(),
              WifiAsyncJobPublisher.getPartitionKey(WifiAsyncJobContext.builder()
                      .jobName(FIRMWARE_BINDING_JOB_NAME)
                      .tenantId(tenant.getId())
                  .build()));
    });

    // Then
    tenantList.forEach(tenant -> {
      Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
      assertEquals(betaFirmware.getId(), updatedTenant.getLatestBetaVersion().getId());
    });

    //Do not sync to ddccm
    messageCaptors.assertThat(
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getWifiScheduleCreate()
    ).doesNotSendByTenants(tenantList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet()));
  }

  /*
   * 6.2.1 fw to 6.2.2 Tenant (same ABF)
   * Expected: new TAAF, not update TFV, update Tenant.LatatestRelaseFW, do FW schedule
   */
  @Test
  public void asyncRelease621FirmwareTo622Tenant(Tenant adminTenant,
      ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.301") ApVersion targetVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.300") ApVersion defaultVersion) {
    String requestId = randomTxId();
    String userName = randomName();
    List<Tenant> tenantList = List.of(
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));
    tenantList.forEach(tenant -> repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId()));
    List<String> tenantIdList = tenantList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());
    var params = new RequestParams();
    params.addPathVariable("firmwareVersion", targetVersion.getId());
    params.addRequestParam("schedule", true);

    tenantList.forEach(t -> {
      Ap ap1 = ApTestFixture.randomAp(apGroup, ap -> ap.setModel("R560"));
      repositoryUtil.createOrUpdate(ap1, t.getId(), randomTxId());
    });

    // When
    messageUtil.sendWifiCfgRequest(adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, userName, params,
        tenantIdList);
    assertThat(getScheduleCreateKafkaMsgs(requestId, tenantList.size())).hasSize(tenantList.size());

    // Then
    tenantList.forEach(tenant -> {
      Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
      assertEquals(targetVersion.getId(), updatedTenant.getLatestReleaseVersion().getId());
      TenantAvailableApFirmware taaf = repositoryUtil.find(TenantAvailableApFirmware.class,
          tenant.getId() + targetVersion.getId());
      assertEquals(targetVersion.getId(), taaf.getApVersion().getId());
    });
  }

  /*
   * Case A. 7.0 fw to 6.2.0 Tenant
   * Expected: ABF migration, two new TAAF, two new TFV, update Tenant.LatatestRelaseFW, do FW schedule, two new VFV
   * for each venue
   * Case B. security patch for 6.2.0
   * Expected: 6.2.0 TFV is updated
   */
  @Test
  public void asyncRelease70FirmwareTo620Tenant(Tenant adminTenant,
      ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.103.501") ApVersion targetVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.500") ApVersion defaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.502") ApVersion securityPatchVersion) {

    List<Tenant> tenantList = List.of(
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
        TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));
    tenantList.forEach(tenant -> repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId()));

    Venue venue = VenueTestFixture.randomVenue(tenantList.get(0));
    venue.setWifiFirmwareVersion(defaultVersion);
    repositoryUtil.createOrUpdate(venue, tenantList.get(0).getId(), randomTxId());

    List<String> tenantIdList = tenantList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());
    var params = new RequestParams();
    params.addPathVariable("firmwareVersion", targetVersion.getId());
    params.addRequestParam("schedule", true);

    tenantList.forEach(t -> {
      Ap ap1 = ApTestFixture.randomAp(apGroup, ap -> ap.setModel("R560"));
      repositoryUtil.createOrUpdate(ap1, t.getId(), randomTxId());
    });

    // When A
    String requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, randomName(),
        params, tenantIdList);
    assertThat(getScheduleCreateKafkaMsgs(requestId, tenantList.size())).hasSize(tenantList.size());

    // Result A
    tenantList.forEach(tenant -> {
      Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
      assertEquals(targetVersion.getId(), updatedTenant.getLatestReleaseVersion().getId());

      assertEquals(TestConstants.DABF2_VERSION, repositoryUtil.find(TenantAvailableApFirmware.class, tenant.getId() + TestConstants.DABF2_VERSION).getApVersion().getId());
      assertEquals(targetVersion.getId(), repositoryUtil.find(TenantAvailableApFirmware.class, tenant.getId() + targetVersion.getId()).getApVersion().getId());

      assertEquals(defaultVersion.getId(), repositoryUtil.find(TenantFirmwareVersion.class, tenant.getId() + TestConstants.ABF1).getLatestFirmwareVersion().getId());
      assertEquals(TestConstants.DABF2_VERSION, repositoryUtil.find(TenantFirmwareVersion.class, tenant.getId() + TestConstants.ABF2).getLatestFirmwareVersion().getId());
    });

    VenueFirmwareVersion vfv1 = repositoryUtil.find(VenueFirmwareVersion.class, venue.getId() + TestConstants.ABF1);
    assertEquals(defaultVersion.getId(), vfv1.getCurrentFirmwareVersion().getId());
    VenueFirmwareVersion vfv2 = repositoryUtil.find(VenueFirmwareVersion.class, venue.getId() + TestConstants.ABF2);
    assertEquals(TestConstants.DABF2_VERSION, vfv2.getCurrentFirmwareVersion().getId());

    // When B
    requestId = randomTxId();
    var paramsB = new RequestParams();
    paramsB.addPathVariable("firmwareVersion", securityPatchVersion.getId());
    paramsB.addRequestParam("schedule", true);
    messageUtil.sendWifiCfgRequest(adminTenant.getId(), requestId, CfgExtendedAction.FIRMWARE_BINDING, randomName(),
        paramsB, tenantIdList);
    assertThat(getScheduleCreateKafkaMsgs(requestId, tenantList.size())).hasSize(tenantList.size());

    // Result B
    TenantFirmwareVersion tfv = repositoryUtil.find(TenantFirmwareVersion.class, tenantList.get(0).getId() + TestConstants.ABF1);
    assertEquals(securityPatchVersion.getId(), tfv.getLatestFirmwareVersion().getId());
  }

  private List<ConsumerRecord<String, byte[]>> getScheduleCreateKafkaMsgs(String requestId, int count) {
    return this.getScheduleCreateKafkaMsgs(requestId, Duration.ofSeconds(3), count);
  }

  private List<ConsumerRecord<String, byte[]>> getScheduleCreateKafkaMsgs(String requestId, Duration timeout,
      int count) {
    Predicate<ConsumerRecord<String, byte[]>> validateRequest = request -> {
      if (request.value() != null) {
        String requestIdInMsg = request.key().split("@@")[0];
        return requestId.equals(requestIdInMsg);
      }
      return false;
    };
    return this.getKafkaMsgs(kafkaTopicProvider.getWifiScheduleCreate(), validateRequest, timeout, count);
  }

  private List<ConsumerRecord<String, byte[]>> getDdccmCfgKafkaMsgs(String requestId, List<String> tenantIds,
      int count) {
    Predicate<ConsumerRecord<String, byte[]>> validateRequest = request -> {
      if (request.headers() != null) {
        String requestIdInMsg = new String(request.headers().lastHeader(REQUEST_ID).value());
        String tenantIdInMsg = new String(request.headers().lastHeader(TENANT_ID).value());
        return requestId.equals(requestIdInMsg) && tenantIds.contains(tenantIdInMsg);
      }
      return false;
    };
    return this.getKafkaMsgs(kafkaTopicProvider.getDdccmCfgRequest(), validateRequest, Duration.ofSeconds(3), count);
  }

  private List<ConsumerRecord<String, byte[]>> getKafkaMsgs(String topic,
      Predicate<ConsumerRecord<String, byte[]>> predicate,
      Duration timeout,
      int count) {
    List<ConsumerRecord<String, byte[]>> res = new ArrayList<>();
    var request = messageUtil.receive(topic, timeout);
    final var timer = Time.SYSTEM.timer(timeout);
    while (timer.notExpired()) {
      if (request != null && predicate.test(request)) {
        res.add(request);
        count--;
      }
      if (count == 0) {
        break;
      }
      request = messageUtil.receive(topic, timeout);
      timer.update();
    }
    return res;
  }

  void validateResult(List<Tenant> tenants, ApVersion targetVersion) {
    tenants.forEach((tenant) -> {
      Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
      assertThat(updatedTenant)
          .isNotNull()
          .matches(t -> Objects.equals(tenant.getId(), t.getId()))
          .matches(t -> Objects.equals(targetVersion.getId(), t.getLatestReleaseVersion().getId()));
    });
  }
}
