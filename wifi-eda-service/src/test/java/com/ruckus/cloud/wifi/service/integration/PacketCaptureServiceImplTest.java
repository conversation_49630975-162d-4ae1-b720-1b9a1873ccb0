package com.ruckus.cloud.wifi.service.integration;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruckus.cloud.notification.dnb.gpb.Dnb;
import com.ruckus.cloud.wifi.kafka.publisher.NotificationRequestPublisher;
import com.ruckus.cloud.wifi.kafka.publisher.WebSocketPublisher;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.service.PacketCaptureDaoService;
import com.ruckus.cloud.wifi.service.PacketCaptureHelper;
import com.ruckus.cloud.wifi.service.entity.PacketCapture;
import com.ruckus.cloud.wifi.service.entity.PacketCaptureField;
import com.ruckus.cloud.wifi.service.entity.PacketCaptureOpStatus;
import com.ruckus.cloud.wifi.service.entity.PacketCaptureState;
import com.ruckus.cloud.wifi.service.entity.PcapOperationMeta;
import com.ruckus.cloud.wifi.service.exception.PcapInvalidStateException;
import com.ruckus.cloud.wifi.service.exception.PcapOperationFailureException;
import com.ruckus.cloud.wifi.service.impl.PacketCaptureDaoServiceImpl;
import com.ruckus.cloud.wifi.service.impl.PacketCaptureServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.TestContainerRedisConfig;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.RedisTestContainer;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RTransaction;
import org.redisson.api.RedissonClient;
import org.redisson.api.TransactionOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@RedisTestContainer
@WifiUnitTest
public class PacketCaptureServiceImplTest {

  protected static final String TENANT_ID = CommonTestFixture.randomId();
  protected static final String SERIAL_NUMBER = CommonTestFixture.randomSerialNumber();
  protected static final String REQUEST_ID = CommonTestFixture.randomTxId();
  @Autowired
  private PacketCaptureHelper helper;
  @Autowired
  private PacketCaptureServiceImpl packetCaptureServiceImpl;
  @Autowired
  private RedissonClient redissonClient;
  @Autowired
  private WebSocketPublisher webSocketPublisher;
  @Autowired
  private PacketCaptureDaoServiceImpl packetCaptureDao;

  @BeforeEach
  void initEach() {
    doReturn("downloadurl").when(helper).getDownloadSignedUrl(anyString(), any());
    doReturn("gcsfile").when(helper).getGcsFilePath(anyString(), anyString());
    doReturn("callback-url").when(helper).getCallbackUrl(anyString());
    doReturn("upload").when(helper).getUploadSignedUrl(anyString(), any());
    doNothing().when(webSocketPublisher)
        .publish(any(), anyString(), anyString(), anyString());
  }

  @AfterEach
  void cleanUpEach() {
    redissonClient.getMap("pcap:" + SERIAL_NUMBER).delete();
  }

  @Test
  public void startOK() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;
    List<String> testFrameTypeFilter = List.of("DATA", "CONTROL");

    packetCaptureServiceImpl.doStart(testSessionId, TENANT_ID, testSerial,
        Dnb.CaptureInterface.RADIO_24.name(), testFrameTypeFilter, "test-mac");

    PacketCapture pcap = packetCaptureServiceImpl.get(testSerial);
    assertEquals(testSessionId, pcap.getSessionId());
    assertEquals(PacketCaptureState.STARTING, pcap.getState());
    assertEquals(PacketCaptureOpStatus.STARTING, pcap.getOpStatus());

  }

  @Test
  public void reportStartedOK() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    becomeStartingStatus(packetCaptureServiceImpl, testSessionId, testSerial);

    // Do
    packetCaptureServiceImpl.doReportStarted(REQUEST_ID, testSerial, testSessionId);

    // Validation
    PacketCapture pcap = packetCaptureServiceImpl.get(testSerial);
    assertEquals(testSessionId, pcap.getSessionId());
    assertEquals(PacketCaptureState.STARTED, pcap.getState());
    assertEquals(PacketCaptureOpStatus.CAPTURING, pcap.getOpStatus());

  }

  //
  @Test
  public void stopOK() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    becomeCapturingStatus(packetCaptureServiceImpl, testSessionId, testSerial);

    // Do
    packetCaptureServiceImpl.doStop(REQUEST_ID, testSessionId, TENANT_ID, testSerial);

    // Validation
    PacketCapture pcap = packetCaptureServiceImpl.get(testSerial);
    assertEquals(testSessionId, pcap.getSessionId());
    assertEquals(PacketCaptureState.STOPPING, pcap.getState());
    assertEquals(PacketCaptureOpStatus.STOPPING, pcap.getOpStatus());

  }


  @Test
  public void reportReadyInStoppingOK() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    becomeStoppingStatus(packetCaptureServiceImpl, testSessionId, testSerial);

    // Do
    packetCaptureServiceImpl.doReportReady(REQUEST_ID, testSerial, testSessionId);

    // Validation
    PacketCapture pcap = packetCaptureServiceImpl.get(testSerial);
    assertEquals(testSessionId, pcap.getSessionId());
    assertEquals(PacketCaptureState.READY, pcap.getState());
    assertEquals(PacketCaptureOpStatus.READY, pcap.getOpStatus());
  }

  @Test
  public void reportReadyInCapturingOK() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    becomeCapturingStatus(packetCaptureServiceImpl, testSessionId, testSerial);

    // Do
    packetCaptureServiceImpl.doReportReady(REQUEST_ID, testSerial, testSessionId);

    // Validation
    PacketCapture pcap = packetCaptureServiceImpl.get(testSerial);
    assertEquals(testSessionId, pcap.getSessionId());
    assertEquals(PacketCaptureState.READY, pcap.getState());
    assertEquals(PacketCaptureOpStatus.READY, pcap.getOpStatus());
  }

  @Test
  public void reportStartingErrorOK() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;
    String errMsg = "error-msg";

    becomeStartingStatus(packetCaptureServiceImpl, testSessionId, testSerial);

    // Do
    packetCaptureServiceImpl.doReportError(REQUEST_ID, testSerial, testSessionId, errMsg);

    // Validation
    PacketCapture pcap = packetCaptureServiceImpl.get(testSerial);
    assertEquals(testSessionId, pcap.getSessionId());
    assertEquals(PacketCaptureState.STARTING_ERROR, pcap.getState());
    assertEquals(PacketCaptureOpStatus.STARTING_ERROR, pcap.getOpStatus());
    assertEquals(errMsg, pcap.getErrMsg());
  }

  @Test
  public void reportStoppingErrorOK() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;
    String errMsg = "error-msg";

    becomeStoppingStatus(packetCaptureServiceImpl, testSessionId, testSerial);

    // Do
    packetCaptureServiceImpl.doReportError(REQUEST_ID, testSerial, testSessionId, errMsg);

    // Validation
    PacketCapture pcap = packetCaptureServiceImpl.get(testSerial);
    assertEquals(testSessionId, pcap.getSessionId());
    assertEquals(PacketCaptureState.STOPPING_ERROR, pcap.getState());
    assertEquals(PacketCaptureOpStatus.STOPPING_ERROR, pcap.getOpStatus());
    assertEquals(errMsg, pcap.getErrMsg());
  }

  private void becomeStartingStatus(PacketCaptureServiceImpl packetCaptureService, String sessionId,
      String serial)
      throws PcapInvalidStateException {
    List<String> testFrameTypeFilter = List.of("DATA", "CONTROL");

    packetCaptureService.doStart(sessionId, TENANT_ID, serial,
        Dnb.CaptureInterface.RADIO_24.name(), testFrameTypeFilter, "test-mac");
  }

  //
  private void becomeCapturingStatus(PacketCaptureServiceImpl packetCaptureService,
      String sessionId, String serial)
      throws PcapInvalidStateException {
    List<String> testFrameTypeFilter = List.of("DATA", "CONTROL");
    packetCaptureService.doStart(sessionId, TENANT_ID, serial,
        Dnb.CaptureInterface.RADIO_24.name(), testFrameTypeFilter, "test-mac");
    packetCaptureService.doReportStarted(REQUEST_ID, serial, sessionId);

  }

  private void becomeStoppingStatus(PacketCaptureServiceImpl packetCaptureService, String sessionId,
      String serial)
      throws PcapInvalidStateException {
    List<String> testFrameTypeFilter = List.of("DATA", "CONTROL");
    packetCaptureService.doStart(sessionId, TENANT_ID, serial,
        Dnb.CaptureInterface.RADIO_24.name(), testFrameTypeFilter, "test-mac");
    packetCaptureService.doReportStarted(REQUEST_ID, serial, sessionId);
    packetCaptureService.doStop(REQUEST_ID, sessionId, TENANT_ID, serial);
  }

  @Test
  public void startAndDuplicateSessionError() {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    List<String> testFrameTypeFilter = List.of("DATA", "CONTROL");

    RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());
    Map<PacketCaptureField, String> data = new HashMap<>();

    packetCaptureDao.create(transaction, testSerial,
        packetCaptureServiceImpl.startDataSet(testSessionId, "gcsfile", "uploadurl",
            Dnb.CaptureInterface.RADIO_24.name(), testFrameTypeFilter),
        Duration.ofSeconds(30));
    transaction.commit();

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doStart(testSessionId, TENANT_ID, testSerial,
          Dnb.CaptureInterface.RADIO_24.name(), testFrameTypeFilter, "test-mac");
    });
  }

  @Test
  public void startErrorDataRollback() throws PcapInvalidStateException {
    doReturn(null).when(helper).getDownloadSignedUrl(anyString(), any());
    doReturn("upload").when(helper).getUploadSignedUrl(anyString(), any());
    doThrow(RuntimeException.class).when(webSocketPublisher)
        .publish(any(), anyString(), anyString(), anyString());

    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    List<String> testFrameTypeFilter = List.of("DATA", "CONTROL");
    assertThrows(PcapOperationFailureException.class, () -> {
      packetCaptureServiceImpl.start(PcapOperationMeta.builder().requestId(testSessionId).
          serial(testSerial)
          .tenantId(TENANT_ID)
          .build(), Dnb.CaptureInterface.RADIO_24.name(), testFrameTypeFilter, "test-mac");
    });

    PacketCapture pcap = packetCaptureServiceImpl.get(testSerial);
    assertEquals(PacketCaptureOpStatus.IDLE, pcap.getOpStatus());

  }

  @Test
  public void reportStartedMismatchedStatusError() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    becomeCapturingStatus(packetCaptureServiceImpl, testSessionId, testSerial);
    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportStarted(REQUEST_ID, testSerial, testSessionId);
    });
  }

  @Test
  public void stopMismatchedStatusError() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    becomeStartingStatus(packetCaptureServiceImpl, testSessionId, testSerial);

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doStop(REQUEST_ID, testSessionId, TENANT_ID, testSerial);
    });
  }

  @Test
  public void reportReadyMismatchedStatusError() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    becomeStartingStatus(packetCaptureServiceImpl, testSessionId, testSerial);

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportReady(REQUEST_ID, testSerial, testSessionId);
    });
  }

  @Test
  public void reportErrorMismatchedStatusError() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    becomeCapturingStatus(packetCaptureServiceImpl, testSessionId, testSerial);

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportError(REQUEST_ID, testSerial, testSessionId, "error");
    });
  }

  @Test
  public void noExistingRecordError() {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportStarted(REQUEST_ID, testSerial, testSessionId);
    });

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doStop(REQUEST_ID, testSessionId, TENANT_ID, testSerial);
    });

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportReady(REQUEST_ID, testSerial, testSessionId);
    });

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportError(REQUEST_ID, testSerial, testSessionId, "err");
    });
  }

  @Test
  public void sessionMismatchedError() {
    String testSerial = SERIAL_NUMBER;

    String existingSessionId = "existing-session-id";
    String operatingSessionId = "operating-session-id";
    List<String> testFrameTypeFilter = List.of("DATA", "CONTROL");

    // Create a record
    RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());

    Map<PacketCaptureField, String> data = new HashMap<>();
    packetCaptureDao.create(transaction, testSerial,
        packetCaptureServiceImpl.startDataSet(existingSessionId, "gcsfile", "uploadurl",
            Dnb.CaptureInterface.RADIO_24.name(), testFrameTypeFilter),
        Duration.ofSeconds(30));
    transaction.commit();

    // Expect errors by different session id
    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportStarted(REQUEST_ID, testSerial, operatingSessionId);
    });

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doStop(REQUEST_ID, operatingSessionId, TENANT_ID, testSerial);
    });

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportReady(REQUEST_ID, testSerial, operatingSessionId);
    });

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportError(REQUEST_ID, testSerial, operatingSessionId,
          "err");
    });

    assertThrows(PcapInvalidStateException.class, () -> {
      packetCaptureServiceImpl.doReportStarted(REQUEST_ID, testSerial, operatingSessionId);
    });


  }

  @Test
  public void unrecognizedInterface() throws PcapInvalidStateException {
    String testSerial = SERIAL_NUMBER;
    String testSessionId = CommonTestFixture.randomTxId();
    ;
    List<String> testFrameTypeFilter = List.of("DATA", "CONTROL");

    assertThrows(IllegalArgumentException.class, () -> {
      packetCaptureServiceImpl.start(PcapOperationMeta.builder()
          .requestId(testSessionId)
          .serial(testSerial)
          .tenantId(TENANT_ID)
          .build(), "POW5566", testFrameTypeFilter, "test-mac");
    });
  }

  @TestConfiguration
  @Import(TestContainerRedisConfig.class)
  static class TestConfig {

    @Bean
    @ConditionalOnMissingBean
    public PacketCaptureHelper mockPacketCaptureHelper() {
      return mock(PacketCaptureHelper.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public ApRepository mockApRepository() {
      return mock(ApRepository.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public PacketCaptureDaoService packetCaptureDaoService(RedissonClient redissonClient) {
      return new PacketCaptureDaoServiceImpl(redissonClient);
    }

    @Bean
    public PacketCaptureServiceImpl packetCaptureService(PacketCaptureDaoService dao,
        PacketCaptureHelper helper, NotificationRequestPublisher notificationRequestPublisher,
        WebSocketPublisher webSocketPublisher, RedissonClient redissonClient,
        ApRepository apRepository) {
      return new PacketCaptureServiceImpl(dao, helper,
          new ObjectMapper(), notificationRequestPublisher, webSocketPublisher,
          redissonClient, apRepository);
    }
  }
}
