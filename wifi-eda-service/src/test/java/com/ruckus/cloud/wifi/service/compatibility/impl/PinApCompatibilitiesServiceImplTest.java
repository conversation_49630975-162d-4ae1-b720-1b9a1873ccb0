package com.ruckus.cloud.wifi.service.compatibility.impl;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.PinProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.service.ScopeDataService;
import com.ruckus.cloud.wifi.service.core.rbac.Scope;
import com.ruckus.cloud.wifi.service.core.rbac.ScopeData;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@WifiUnitTest
public class PinApCompatibilitiesServiceImplTest {
  @RegisterExtension
  public final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @MockBean
  private PinProfileRegularSettingRepository pinSettingRepository;
  @MockBean
  private ScopeDataService scopeDataService;
  @SpyBean
  private PinApCompatibilitiesServiceImpl unit;

  @Nested
  class WhenGetPinVenuesNetworksList {
    Map<String, List<String>> pinVenueMap;
    Map<String, List<String>> pinNetwrokMap;
    List<String> serviceIds = List.of(randomId(), randomId());
    List<String> p1VenueIds = List.of(randomId(), randomId(), randomId());
    List<String> p2VenueIds = List.of(randomId());
    List<String> p1NetworkIds = List.of(randomId(), randomId(), randomId());
    List<String> p2NetworkIds = List.of(randomId(), randomId());
    List<PinProfileRegularSetting> settings;

    @BeforeEach
    void beforeEach() {
      String pinId1 = serviceIds.get(0);
      String pinId2 = serviceIds.get(1);
      pinVenueMap = Map.of(pinId1, p1VenueIds, pinId2, p2VenueIds);
      pinNetwrokMap = Map.of(pinId1, p1NetworkIds, pinId2, p2NetworkIds);

      PinProfile pinProfile1 = buildPinProfile(pinId1);
      PinProfileRegularSetting setting1 = buildPinProfileRegularSetting(pinProfile1,
          buildVenue(p1VenueIds.get(0)),
          List.of(buildPinProfileNetworkMapping(p1NetworkIds.get(0))));

      PinProfileRegularSetting setting2 = buildPinProfileRegularSetting(pinProfile1,
          buildVenue(p1VenueIds.get(1)),
          List.of(buildPinProfileNetworkMapping(p1NetworkIds.get(1)), buildPinProfileNetworkMapping(p1NetworkIds.get(2))));

      PinProfile pinProfile2 = buildPinProfile(pinId2);
      PinProfileRegularSetting setting3 = buildPinProfileRegularSetting(pinProfile2,
          buildVenue(p2VenueIds.get(0)),
          List.of(buildPinProfileNetworkMapping(p2NetworkIds.get(0)), buildPinProfileNetworkMapping(p2NetworkIds.get(1))));

      settings = List.of(setting1, setting2, setting3);
    }

    @Test
    void GivenNullServiceIdList() {
      when(pinSettingRepository.findByTenantIdAndVenueIsNotNull(anyString())).thenReturn(settings);

      BDDAssertions.then(unit.getPinVenuesNetworksList(txCtxExtension.getTenantId(), null))
          .isNotNull()
          .hasSize(2);

      then(pinSettingRepository)
          .should(times(1))
          .findByTenantIdAndVenueIsNotNull(eq(txCtxExtension.getTenantId()));
      then(pinSettingRepository)
          .should(times(0))
          .findByTenantIdAndPinProfileIdInAndVenueIsNotNull(anyString(), anyList());
    }

    @Test
    void GivenEmptyServiceIdList() {
      when(pinSettingRepository.findByTenantIdAndVenueIsNotNull(anyString())).thenReturn(settings);

      BDDAssertions.then(unit.getPinVenuesNetworksList(txCtxExtension.getTenantId(), List.of()))
          .isNotNull()
          .hasSize(2);

      then(pinSettingRepository)
          .should(times(1))
          .findByTenantIdAndVenueIsNotNull(eq(txCtxExtension.getTenantId()));
      then(pinSettingRepository)
          .should(times(0))
          .findByTenantIdAndPinProfileIdInAndVenueIsNotNull(anyString(), anyList());
    }

    @Test
    void GivenEmptyServiceIdListWithScopeDataNotEmpty() {
      ScopeData scopeData = new ScopeData();
      Scope scope = new Scope();
      scope.setVenues(List.of(p1VenueIds.get(0)));
      scopeData.setScopes(scope);
      willReturn(Optional.of(scopeData)).given(scopeDataService).getScopeData();

      when(pinSettingRepository.findByTenantIdAndVenueIsNotNull(anyString())).thenReturn(settings);

      BDDAssertions.then(unit.getPinVenuesNetworksList(txCtxExtension.getTenantId(), List.of()))
          .isNotNull()
          .hasSize(1);

      then(pinSettingRepository)
          .should(times(1))
          .findByTenantIdAndVenueIsNotNull(eq(txCtxExtension.getTenantId()));
      then(pinSettingRepository)
          .should(times(0))
          .findByTenantIdAndPinProfileIdInAndVenueIsNotNull(anyString(), anyList());
    }

    @Test
    void GivenServiceIdList() {
      when(pinSettingRepository.findByTenantIdAndPinProfileIdInAndVenueIsNotNull(anyString(), anyList())).thenReturn(settings);

      BDDAssertions.then(unit.getPinVenuesNetworksList(txCtxExtension.getTenantId(), serviceIds))
          .isNotNull()
          .hasSize(2)
          .allSatisfy(p -> {
              var venueIds = pinVenueMap.get(p.getServiceId());
              var networkIds = pinNetwrokMap.get(p.getServiceId());
              assertTrue(venueIds.containsAll(p.getVenueIds()));
              assertTrue(networkIds.containsAll(p.getNetworkIds()));
          });

      then(pinSettingRepository)
          .should(times(0))
          .findByTenantIdAndVenueIsNotNull(eq(txCtxExtension.getTenantId()));
      then(pinSettingRepository)
          .should(times(1))
          .findByTenantIdAndPinProfileIdInAndVenueIsNotNull(anyString(), anyList());
    }
  }

  private PinProfileRegularSetting buildPinProfileRegularSetting(PinProfile pinProfile, Venue venue, List<PinProfileNetworkMapping> mappings) {
    PinProfileRegularSetting setting = new PinProfileRegularSetting();
    setting.setId(txCtxExtension.newRandomId());
    setting.setPinProfile(pinProfile);
    setting.setVenue(venue);
    setting.setPinProfileNetworkMappings(mappings);
    return setting;
  }

  private PinProfile buildPinProfile(String id) {
    return new PinProfile(id);
  }

  private Venue buildVenue(String id) {
    return new Venue(id);
  }

  private PinProfileNetworkMapping buildPinProfileNetworkMapping(String networkId) {
    PinProfileNetworkMapping pinProfileNetworkMapping = new PinProfileNetworkMapping();
    pinProfileNetworkMapping.setId(txCtxExtension.newRandomId());
    pinProfileNetworkMapping.setNetwork(new Network(networkId));
    return pinProfileNetworkMapping;
  }
}
