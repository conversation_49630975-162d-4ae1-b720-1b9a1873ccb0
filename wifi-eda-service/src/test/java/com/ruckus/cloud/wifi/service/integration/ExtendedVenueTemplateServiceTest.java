package com.ruckus.cloud.wifi.service.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.common.collect.Lists;
import com.ruckus.cloud.wifi.aop.AllowTemplateOperationOnlyAspect;
import com.ruckus.cloud.wifi.client.kairos.KairosApiClient;
import com.ruckus.cloud.wifi.eda.api.rest.DhcpConfigServiceProfileRestCtrl.DhcpConfigServiceProfileMapper;
import com.ruckus.cloud.wifi.eda.api.rest.VenueRestCtrl.VenueMapper;
import com.ruckus.cloud.wifi.eda.service.DhcpConfigServiceProfileTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.service.RogueApPolicyProfileTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.service.VenueTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueDhcpServiceSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RogueClassificationEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RogueRuleTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSetting;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSettings;
import com.ruckus.cloud.wifi.mapper.DhcpConfigServiceProfileMergeImpl;
import com.ruckus.cloud.wifi.service.DhcpCapabilitiesHelper;
import com.ruckus.cloud.wifi.service.ExtendedDhcpConfigServiceProfileServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedTenantServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.InternalFirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.VenueRadioService;
import com.ruckus.cloud.wifi.service.impl.ExtendedDhcpConfigServiceProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.VenueTemplateServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedApGroupServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedNetworkVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.FirmwareCapabilityServiceTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.InitVenueServiceImplTestConfig;
import com.ruckus.cloud.wifi.service.validator.DhcpServiceValidator;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.VenueDecorator;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import java.util.List;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockReset;
import org.springframework.context.annotation.Import;

@WifiJpaDataTest
class ExtendedVenueTemplateServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private ExtendedVenueServiceCtrl venueServiceCtrl;
  @Autowired
  private VenueTemplateServiceCtrl venueTemplateServiceCtrl;
  @Autowired
  private ExtendedDhcpConfigServiceProfileServiceCtrl dhcpConfigServiceProfileServiceCtrl;
  @Autowired
  private DhcpConfigServiceProfileTemplateServiceCtrl dhcpConfigServiceProfileTemplateServiceCtrl;
  @Autowired
  private RogueApPolicyProfileTemplateServiceCtrl rogueApPolicyProfileTemplateServiceCtrl;

  @Test
  void testVenueDhcpConfigServiceProfileSetting(
      Venue venue, DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    testVenueDhcpConfigServiceProfileSetting(venue, dhcpConfigServiceProfile, false);
  }

  @ApplyTemplateFilter
  @Test
  void template_testVenueDhcpConfigServiceProfileSetting(
      @Template Venue venue, @Template DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    testVenueDhcpConfigServiceProfileSetting(venue, dhcpConfigServiceProfile, true);
  }

  @SneakyThrows
  void testVenueDhcpConfigServiceProfileSetting(
      Venue venue, DhcpConfigServiceProfile dhcpConfigServiceProfile, boolean isTemplate) {
    String venueId = venue.getId();
    String dhcpConfigServiceProfileId = dhcpConfigServiceProfile.getId();
    //
    assertEquals(isTemplate,
        dhcpConfigServiceProfileServiceCtrl.getDhcpConfigServiceProfile(
            dhcpConfigServiceProfileId, true).getIsTemplate());
    //
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        getVenueDhcpConfigServiceProfileSetting(venueId, isTemplate);
    assertFalse(venueDhcpConfigServiceProfileSetting.getEnabled());
    // update DHCP enabled
    VenueDhcpServiceSetting _venueDhcpServiceSetting = new VenueDhcpServiceSetting();
    _venueDhcpServiceSetting.setEnabled(true);
    DhcpConfigServiceProfile _dhcpConfigServiceProfile = cmap(dhcpConfigServiceProfile);
    Venue _venue = new Venue();
    _venue.setId(venueId);
    _venue.setDhcpConfigServiceProfile(_dhcpConfigServiceProfile);
    _venue.setDhcpServiceSetting(_venueDhcpServiceSetting);
    updateVenueDhcpConfigServiceProfileSetting(venueId, _venue, isTemplate);
    venueDhcpConfigServiceProfileSetting =
        getVenueDhcpConfigServiceProfileSetting(venueId, isTemplate);
    assertTrue(venueDhcpConfigServiceProfileSetting.getEnabled());
    assertEquals(dhcpConfigServiceProfileId, venueDhcpConfigServiceProfileSetting.getServiceProfileId());
    // deactivate dhcp pool
    List<DhcpServiceProfile> dhcpPools = getVenueDhcpPoolUsage(venueId, isTemplate)
        .stream().filter(DhcpServiceProfile::getActive).toList();
    assertTrue(!dhcpPools.isEmpty());
    String dhcpPoolId0 = dhcpPools.get(0).getId();
    String dhcpPoolId1 = dhcpPools.get(1).getId();
    String dhcpPoolId2 = dhcpPools.get(2).getId();
    deactivateVenueDhcpPool(venueId, dhcpPoolId1, isTemplate);
    deactivateVenueDhcpPool(venueId, dhcpPoolId2, isTemplate);
    List<String> dhcpPoolIds = getVenueDhcpPoolUsage(venueId, isTemplate)
        .stream().filter(DhcpServiceProfile::getActive)
        .map(DhcpServiceProfile::getId).toList();
    assertEquals(1, dhcpPoolIds.size());
    assertTrue(dhcpPoolIds.contains(dhcpPoolId0));
    // activate dhcp pool
    activateVenueDhcpPool(venueId, dhcpPoolId2, isTemplate);
    dhcpPoolIds = getVenueDhcpPoolUsage(venueId, isTemplate)
        .stream().filter(DhcpServiceProfile::getActive)
        .map(DhcpServiceProfile::getId).toList();;
    assertEquals(2, dhcpPoolIds.size());
    assertTrue(dhcpPoolIds.contains(dhcpPoolId0));
    assertTrue(dhcpPoolIds.contains(dhcpPoolId2));
  }

  @Test
  void testVenueDhcpConfigServiceProfileSettingV1_1(
      Venue venue, DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    testVenueDhcpConfigServiceProfileSettingV1_1(venue, dhcpConfigServiceProfile, false);
  }

  @ApplyTemplateFilter
  @Test
  void template_testVenueDhcpConfigServiceProfileSettingV1_1(
      @Template Venue venue, @Template DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    testVenueDhcpConfigServiceProfileSettingV1_1(venue, dhcpConfigServiceProfile, true);
  }

  @SneakyThrows
  void testVenueDhcpConfigServiceProfileSettingV1_1(
      Venue venue, DhcpConfigServiceProfile dhcpConfigServiceProfile, boolean isTemplate) {
    String venueId = venue.getId();
    String dhcpConfigServiceProfileId = dhcpConfigServiceProfile.getId();
    //
    assertEquals(isTemplate,
        dhcpConfigServiceProfileServiceCtrl.getDhcpConfigServiceProfile(
            dhcpConfigServiceProfileId, true).getIsTemplate());
    //
    {
      VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings =
          getVenueDhcpConfigServiceProfileSettings(venueId, dhcpConfigServiceProfileId, isTemplate);
      assertTrue(CollectionUtils.isEmpty(venueDhcpConfigServiceProfileSettings.getActiveDhcpPoolNames()));
    }
    // update DHCP enabled
    {
      VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings =
          new VenueDhcpConfigServiceProfileSettings();
      venueDhcpConfigServiceProfileSettings.setActiveDhcpPoolNames(
          dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getName)
              .toList());
      activateDhcpConfigServiceProfileTemplateAndUpdateSettings(
          venueId, dhcpConfigServiceProfileId, map(venueDhcpConfigServiceProfileSettings),
          isTemplate);
      venueDhcpConfigServiceProfileSettings =
          getVenueDhcpConfigServiceProfileSettings(venueId, dhcpConfigServiceProfileId, isTemplate);
      assertEquals(3, venueDhcpConfigServiceProfileSettings.getActiveDhcpPoolNames().size());
    }
    // deactivate dhcp pool => v1.1 activate 1 dhcp pool
    List<DhcpServiceProfile> dhcpPools = getVenueWifiDhcpPoolUsage(venueId, isTemplate)
        .stream().filter(DhcpServiceProfile::getActive).toList();
    assertEquals(3, dhcpPools.size());
    String dhcpPoolName0 = dhcpPools.get(0).getName();
    String dhcpPoolName1 = dhcpPools.get(1).getName();
    String dhcpPoolName2 = dhcpPools.get(2).getName();
    {
      VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings =
          new VenueDhcpConfigServiceProfileSettings();
      venueDhcpConfigServiceProfileSettings.setActiveDhcpPoolNames(
          List.of(dhcpPoolName0));
      activateDhcpConfigServiceProfileTemplateAndUpdateSettings(
          venueId, dhcpConfigServiceProfileId, map(venueDhcpConfigServiceProfileSettings),
          isTemplate);
      List<String> dhcpPoolNames = getVenueWifiDhcpPoolUsage(venueId, isTemplate)
          .stream().map(DhcpServiceProfile::getName).toList();
      assertEquals(1, dhcpPoolNames.size());
      assertTrue(dhcpPoolNames.contains(dhcpPoolName0));
    }
    // activate dhcp pool
    {
      VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings =
          new VenueDhcpConfigServiceProfileSettings();
      venueDhcpConfigServiceProfileSettings.setActiveDhcpPoolNames(
          List.of(dhcpPoolName0, dhcpPoolName2));
      activateDhcpConfigServiceProfileTemplateAndUpdateSettings(
          venueId, dhcpConfigServiceProfileId, map(venueDhcpConfigServiceProfileSettings),
          isTemplate);
      List<String> dhcpPoolNames = getVenueWifiDhcpPoolUsage(venueId, isTemplate)
          .stream().map(DhcpServiceProfile::getName).toList();
      assertEquals(2, dhcpPoolNames.size());
      assertTrue(dhcpPoolNames.contains(dhcpPoolName0));
      assertTrue(dhcpPoolNames.contains(dhcpPoolName2));
    }
  }

  private DhcpConfigServiceProfile cmap(DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    return DhcpConfigServiceProfileMapper.INSTANCE
        .DhcpConfigServiceProfileDeep2ServiceDhcpConfigServiceProfile(
            DhcpConfigServiceProfileMapper.INSTANCE
                .ServiceDhcpConfigServiceProfile2DhcpConfigServiceProfileDeep(dhcpConfigServiceProfile));
  }

  private Venue map(VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings) {
    return DhcpConfigServiceProfileMapper.INSTANCE
        .VenueDhcpConfigServiceProfileSettings2ServiceVenue(venueDhcpConfigServiceProfileSettings);
  }

  private VenueDhcpConfigServiceProfileSetting getVenueDhcpConfigServiceProfileSetting(
      String venueId, boolean isTemplate) throws Exception {
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        VenueMapper.INSTANCE.ServiceVenue2VenueDhcpConfigServiceProfileSetting(
            isTemplate ?
                venueTemplateServiceCtrl.getVenueTemplateDhcpConfigServiceProfileSetting(venueId) :
                venueServiceCtrl.getVenueDhcpConfigServiceProfileSetting(venueId));
    return venueDhcpConfigServiceProfileSetting;
  }

  private VenueDhcpConfigServiceProfileSettings getVenueDhcpConfigServiceProfileSettings(
      String venueId, String dhcpConfigServiceProfileId, boolean isTemplate) throws Exception {
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings =
        DhcpConfigServiceProfileMapper.INSTANCE.ServiceVenue2VenueDhcpConfigServiceProfileSettings(
            isTemplate ?
                dhcpConfigServiceProfileTemplateServiceCtrl.getVenueTemplateDhcpConfigServiceProfileSettings(venueId, dhcpConfigServiceProfileId) :
                dhcpConfigServiceProfileServiceCtrl.getVenueDhcpConfigServiceProfileSettings(venueId, dhcpConfigServiceProfileId));
    return venueDhcpConfigServiceProfileSettings;
  }

  private List<DhcpServiceProfile> getVenueDhcpPoolUsage(
      String venueId, boolean isTemplate) throws Exception {
    return isTemplate ?
        venueTemplateServiceCtrl.getVenueTemplateDhcpPoolUsage(venueId) :
        venueServiceCtrl.getVenueDhcpPoolUsage(venueId);
  }

  private List<DhcpServiceProfile> getVenueWifiDhcpPoolUsage(
      String venueId, boolean isTemplate) throws Exception {
    return isTemplate ?
        dhcpConfigServiceProfileTemplateServiceCtrl.getVenueTemplateWifiDhcpPoolUsages(venueId).getWifiDhcpPoolUsages() :
        dhcpConfigServiceProfileServiceCtrl.getVenueWifiDhcpPoolUsages(venueId).getWifiDhcpPoolUsages();
  }

  private void updateVenueDhcpConfigServiceProfileSetting(
      String venueId, Venue venue, boolean isTemplate) throws Exception {
    if (isTemplate) {
      venueTemplateServiceCtrl.updateVenueTemplateDhcpConfigServiceProfileSetting(venueId, venue);
    } else {
      venueServiceCtrl.updateVenueDhcpConfigServiceProfileSetting(venueId, venue);
    }
  }

  private void activateDhcpConfigServiceProfileTemplateAndUpdateSettings(
      String venueId, String dhcpConfigServiceProfileId, Venue venue, boolean isTemplate) throws Exception {
    if (isTemplate) {
      dhcpConfigServiceProfileTemplateServiceCtrl
          .activateDhcpConfigServiceProfileTemplateAndUpdateSettings(venueId, dhcpConfigServiceProfileId, venue);
    } else {
      dhcpConfigServiceProfileServiceCtrl
          .activateDhcpConfigServiceProfileAndUpdateSettings(venueId, dhcpConfigServiceProfileId, venue);
    }
  }

  private void deactivateVenueDhcpPool(
      String venueId, String dhcpPoolId, boolean isTemplate) throws Exception {
    if (isTemplate) {
      venueTemplateServiceCtrl.deactivateVenueTemplateDhcpPool(venueId, dhcpPoolId);
    } else {
      venueServiceCtrl.deactivateVenueDhcpPool(venueId, dhcpPoolId);
    }
  }

  private void activateVenueDhcpPool(
      String venueId, String dhcpPoolId, boolean isTemplate) throws Exception {
    if (isTemplate) {
      venueTemplateServiceCtrl.activateVenueTemplateDhcpPool(venueId, dhcpPoolId);
    } else {
      venueServiceCtrl.activateVenueDhcpPool(venueId, dhcpPoolId);
    }
  }

  @Test
  public void testUpdateVenueRogueAp_updateVenueRogueAp_updateVenueRogueApSuccessfully(
      @VenueDecorator(isTemplate = true) Venue venue)
      throws Exception {
    var venueId = venue.getId();
    var expectedVenueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();

    String defaultPolicyId = venueTemplateServiceCtrl.updateVenueTemplateRogueAp(
        venueId, expectedVenueRogueAp).getRoguePolicyId();
    expectedVenueRogueAp.setRoguePolicyId(defaultPolicyId);
    var actualVenueRogueAp = venueTemplateServiceCtrl.getVenueTemplateRogueAp(venue.getId());

    //Then
    assertNotNull(actualVenueRogueAp);
    verifyVenueRogueAp(expectedVenueRogueAp, actualVenueRogueAp);
    assertNotNull(actualVenueRogueAp.getRoguePolicyId());
  }

  @Test
  public void testUpdateVenueRogueAp_updateVenueRogueAp_setPolicyIdSuccessfully(
      @VenueDecorator(isTemplate = true) Venue venue)
      throws Exception {
    //Given
    var venueId = venue.getId();
    var expectedVenueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();
    RogueClassificationPolicy policy = new RogueClassificationPolicy();
    RogueClassificationPolicyRule rule = new RogueClassificationPolicyRule();
    rule.setType(RogueRuleTypeEnum.AdhocRule);
    rule.setClassification(RogueClassificationEnum.Ignored);
    rule.setName("rule");
    rule.setPriority(1);
    policy.setName("policy");
    policy.setRules(Lists.newArrayList(rule));

    //When
    //Set to default policy
    expectedVenueRogueAp = venueTemplateServiceCtrl.updateVenueTemplateRogueAp(venueId,
        expectedVenueRogueAp);

    policy = rogueApPolicyProfileTemplateServiceCtrl.addRogueApPolicyProfileTemplate(policy);
    expectedVenueRogueAp.setRoguePolicyId(policy.getId());
    venueTemplateServiceCtrl.updateVenueTemplateRogueAp(venueId, expectedVenueRogueAp);

    VenueRogueAp actualVenueRogueAp = venueTemplateServiceCtrl.getVenueTemplateRogueAp(venueId);

    //Then
    assertNotNull(actualVenueRogueAp);
    verifyVenueRogueAp(expectedVenueRogueAp, actualVenueRogueAp);
    assertEquals(policy.getId(), actualVenueRogueAp.getRoguePolicyId());
  }

  private void verifyVenueRogueAp(VenueRogueAp expectedVenueRogueAp,
      VenueRogueAp originalVenueRogueAp) {
    assertEquals(expectedVenueRogueAp.getEnabled(),
        originalVenueRogueAp.getEnabled());

    assertEquals(expectedVenueRogueAp.getReportThreshold(),
        originalVenueRogueAp.getReportThreshold());

    assertEquals(expectedVenueRogueAp.getRoguePolicyId(),
        originalVenueRogueAp.getRoguePolicyId());
  }

  @Test
  void testVenueSyslogServerProfileSetting(@Template Venue venue, @Template SyslogServerProfile syslogServerProfile) throws Exception {
    var expectedVenueSyslogServerProfile = new VenueSyslogServerProfile();
    expectedVenueSyslogServerProfile.setServiceProfileId(syslogServerProfile.getId());
    expectedVenueSyslogServerProfile.setEnabled(true);
    venueTemplateServiceCtrl.updateVenueTemplateSyslogServerProfileSettings(venue.getId(), expectedVenueSyslogServerProfile);

    var actualVenueSyslogServerProfile = venueTemplateServiceCtrl.getVenueTemplateSyslogServerProfileSettings(venue.getId());

    assertNotNull(actualVenueSyslogServerProfile);
    assertNotNull(actualVenueSyslogServerProfile.getServiceProfileId());
    assertEquals(expectedVenueSyslogServerProfile.getServiceProfileId(), actualVenueSyslogServerProfile.getServiceProfileId());
    assertEquals(expectedVenueSyslogServerProfile.getEnabled(), actualVenueSyslogServerProfile.getEnabled());
  }

  @TestConfiguration
  @Import({
      DhcpServiceValidator.class,
      DhcpConfigServiceProfileMergeImpl.class,
      ExtendedDhcpConfigServiceProfileServiceCtrlImpl.class,
      FirmwareCapabilityServiceTestConfig.class,
      ExtendedVenueServiceCtrlImplTestConfig.class,
      ExtendedNetworkVenueServiceCtrlImplTestConfig.class,
      ExtendedApGroupServiceCtrlImplTestConfig.class,
      InitVenueServiceImplTestConfig.class,
      VenueTemplateServiceCtrlImpl.class,
      AllowTemplateOperationOnlyAspect.class
  })
  static class TestConfig {

    @MockBean
    private ExtendedTenantServiceCtrl tenantService;
    @MockBean
    private VenueRadioService venueRadioService;
    @MockBean(reset = MockReset.BEFORE)
    private KairosApiClient kairosApiClient;
    @MockBean
    private DhcpCapabilitiesHelper dhcpCapabilitiesHelper;
    @MockBean
    private InternalFirmwareCapabilityService internalFirmwareCapabilityService;
  }
}
