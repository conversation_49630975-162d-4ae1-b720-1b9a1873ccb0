package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.servicemodel.projection.IdAndName;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO signature_package (id, version, default_version, next_version, releasable)
        VALUES ('v1', 'v1', true, 'v2', true);
    INSERT INTO signature_package (id, version, default_version, next_version, releasable)
        VALUES ('v2', 'v2', false, null, true);
    INSERT INTO application_policy (id, name, tenant, signature_package)
        VALUES ('ap_1', 'policy1', '4c8279f79307415fa9e4c88a1819f0fc', 'v1');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
        application_id, application_name, category_id, category)
        VALUES ('apr_1_1', 'rule1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_1', 'SIGNATURE',
        1496, 'COMM', 3, 'Audio/Video');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
        application_id, application_name, category_id, category)
        VALUES ('apr_1_2', 'rule2', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_1', 'SIGNATURE',
        3442, 'Realvnc', 13, 'Game');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
        application_id, application_name, category_id, category)
        VALUES ('apr_1_3', 'rule3', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_1', 'SIGNATURE',
        1138, 'Stafaband.info', 30, 'Web');
    INSERT INTO application_policy (id, name, tenant, signature_package)
        VALUES ('ap_2', 'policy1', '4c8279f79307415fa9e4c88a1819f0fc', 'v2');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
        application_id, application_name, category_id, category)
        VALUES ('apr_2_1', 'rule1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'SIGNATURE',
        1496, 'COMM', 3, 'Audio/Video');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
        application_id, application_name, category_id, category)
        VALUES ('apr_2_2', 'rule2', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'SIGNATURE',
        3442, 'Realvnc', 13, 'Game');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
        application_id, application_name, category_id, category)
        VALUES ('apr_2_3', 'rule3', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'SIGNATURE',
        1138, 'Stafaband.info', 30, 'Web');
    INSERT INTO l2acl_policy (id, name, tenant, mac_addresses, access)
        VALUES ('l2acl_1', 'l2acl-policy-1', '4c8279f79307415fa9e4c88a1819f0fc', '[11:22:33:44:55:66]', 'ALLOW');
    INSERT INTO l3acl_policy (id, name, tenant, default_access) VALUES
        ('l3acl_1', 'l3acl-policy-1', '4c8279f79307415fa9e4c88a1819f0fc', 'ALLOW');
    INSERT INTO l3rule (id, description, tenant, priority, source_enable_ip_subnet, access, l3acl_policy) VALUES
        ('l3rule_1', 'l3rule-1', '4c8279f79307415fa9e4c88a1819f0fc', 1, true, 'ALLOW', 'l3acl_1');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'network_1',
        'be3cb5cdef1d4fedbb7b778774157e41',
        'OPEN',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'network_2',
        'be3cb5cdef1d4fedbb7b778774157e42',
        'OPEN',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'network_3',
        'be3cb5cdef1d4fedbb7b778774157e43',
        'OPEN',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO wlan (id, network, tenant, application_policy, previous_application_policy)
        VALUES ('wlan_1', 'network_1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'ap_1');
    INSERT INTO wlan (id, network, tenant, application_policy, previous_application_policy)
        VALUES ('wlan_2', 'network_1', '4c8279f79307415fa9e4c88a1819f0fc', null, 'ap_1');
    INSERT INTO wlan (id, network, tenant, l2acl_policy, l2acl_enable)
        VALUES ('wlan_3', 'network_2', '4c8279f79307415fa9e4c88a1819f0fc', 'l2acl_1', true);
    INSERT INTO wlan (id, network, tenant, l3acl_policy, l3acl_enable)
        VALUES ('wlan_4', 'network_3', '4c8279f79307415fa9e4c88a1819f0fc', 'l3acl_1', true);
    INSERT INTO wlan (id, network, tenant, application_policy, previous_application_policy)
        VALUES ('wlan_5', 'network_1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', null);
    INSERT INTO revision_info
        (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
        VALUES(12312312, false, '4c8279f79307415fa9e4c88a1819f0fc', 1694142622590, '11111', 1694142622590);
    INSERT INTO revision_info
        (id, "abort", tenant, "timestamp", tx_id, updated_timestamp)
        VALUES(12312313, false, '4c8279f79307415fa9e4c88a1819f0fc', 1694142622591, '22222', 1694142622591);            
    INSERT INTO wlan_aud (id, rev, network, vlan_id, tenant, updated_date)
        VALUES ('wlan_1', 12312312, 'network_1', '1', '4c8279f79307415fa9e4c88a1819f0fc', '2024-11-11 00:12:53.427');
    INSERT INTO wlan_aud (id, rev, network, vlan_id, tenant, updated_date)
        VALUES ('wlan_1', 12312313, 'network_1', '2', '4c8279f79307415fa9e4c88a1819f0fc', '2024-11-12 14:23:02.49');    
    """)
public class WlanRepositoryTest {

  @Autowired
  private WlanRepository wlanRepository;

  @Test
  void findByTenantIdAndAdvancedCustomizationApplicationPolicyInTest() {
    var result = wlanRepository.findByTenantIdAndAdvancedCustomizationApplicationPolicyIn(
        "4c8279f79307415fa9e4c88a1819f0fc", List.of(new ApplicationPolicy("ap_2")));
    assertEquals(2, result.size());
    assertEquals(Set.of("wlan_1", "wlan_5"),
        result.stream().map(Wlan::getId).collect(Collectors.toSet()));
    assertEquals(Set.of("ap_1"),
        result.stream()
            .filter(w -> w.getPreviousApplicationPolicy() != null)
            .map(Wlan::getPreviousApplicationPolicy)
            .map(ApplicationPolicy::getId)
            .collect(Collectors.toSet()));
  }

  @Test
  void findByTenantIdAndPreviousApplicationPolicyIsNotNullAndAdvancedCustomizationApplicationPolicyIsNullTest() {
    var result = wlanRepository.findByTenantIdAndPreviousApplicationPolicyIsNotNullAndAdvancedCustomizationApplicationPolicyIsNull(
        "4c8279f79307415fa9e4c88a1819f0fc");
    assertEquals(1, result.size());
    assertEquals("wlan_2", result.get(0).getId());
  }

  @Test
  void findByTenantIdAndPreviousApplicationPolicyIsNullAndAdvancedCustomizationApplicationPolicyIsNotNullTest() {
    var result = wlanRepository.findByTenantIdAndPreviousApplicationPolicyIsNullAndAdvancedCustomizationApplicationPolicyIsNotNull(
        "4c8279f79307415fa9e4c88a1819f0fc");
    assertEquals(1, result.size());
    assertEquals("wlan_5", result.get(0).getId());
  }

  @Test
  void findByTenantIdAndPreviousApplicationPolicyIsNotNullTest() {
    var result = wlanRepository.findByTenantIdAndPreviousApplicationPolicyIsNotNull(
        "4c8279f79307415fa9e4c88a1819f0fc");
    assertEquals(2, result.size());
    assertEquals(2, result.stream()
        .filter(wlan -> wlan.getPreviousApplicationPolicy().getId().equals("ap_1")).count());
    assertEquals(Set.of("wlan_1", "wlan_2"),
        result.stream().map(Wlan::getId).collect(Collectors.toSet()));
  }

  @Test
  void findNetworkByTenantIdAndL2AclPolicyIdInTest() {
    assertThat(wlanRepository.findNetworkByTenantIdAndL2AclPolicyIdIn("4c8279f79307415fa9e4c88a1819f0fc", List.of("l2acl_1")))
        .isNotNull()
        .hasSize(1)
        .extracting(Network::getId)
        .singleElement()
        .isEqualTo("network_2");
  }

  @Test
  void findNetworkNamesByTenantIdAndL2AclPolicyIdInTest() {
    assertThat(wlanRepository.findNetworkNamesByTenantIdAndL2AclPolicyIdIn("4c8279f79307415fa9e4c88a1819f0fc", List.of("l2acl_1")))
            .isNotNull()
            .hasSize(1)
            .singleElement()
            .extracting(IdAndName::getName)
            .isEqualTo("be3cb5cdef1d4fedbb7b778774157e42");
  }

  @Test
  void findNetworkByTenantIdAndL3AclPolicyIdInTest() {
    assertThat(wlanRepository.findNetworkByTenantIdAndL3AclPolicyIdIn("4c8279f79307415fa9e4c88a1819f0fc", List.of("l3acl_1")))
            .isNotNull()
            .hasSize(1)
            .extracting(Network::getId)
            .singleElement()
            .isEqualTo("network_3");
  }

  @Test
  void findNetworkNamesByTenantIdAndL3AclPolicyIdInTest() {
    assertThat(wlanRepository.findNetworkNamesByTenantIdAndL3AclPolicyIdIn("4c8279f79307415fa9e4c88a1819f0fc", List.of("l3acl_1")))
            .isNotNull()
            .hasSize(1)
            .singleElement()
            .extracting(IdAndName::getName)
            .isEqualTo("be3cb5cdef1d4fedbb7b778774157e43");
  }

  @Test
  void findTenantListByDistinctTenantIdsWithVlanIdChangedTest() {
    assertThat(wlanRepository.findDistinctTenantIdsWithVlanIdChanged())
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .isEqualTo("4c8279f79307415fa9e4c88a1819f0fc");
  }
}
