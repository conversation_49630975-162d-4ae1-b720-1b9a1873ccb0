package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.IOT_MQTT_BROKER_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_SMART_MONITOR_DISABLE_WLAN_TOGGLE;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Ap.GpsMode;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLocation.CcmHeightType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLocation.CcmSource;
import com.ruckus.acx.ddccm.protobuf.wifi.ApRadio24;
import com.ruckus.acx.ddccm.protobuf.wifi.ApRadio50;
import com.ruckus.acx.ddccm.protobuf.wifi.ApRadio60;
import com.ruckus.acx.ddccm.protobuf.wifi.AutoChannelSelectionMethod;
import com.ruckus.acx.ddccm.protobuf.wifi.ManagementVlanModeApLevelEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Syslog;
import com.ruckus.acx.ddccm.protobuf.wifi.SyslogFacilityEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.SyslogFlowLevelEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.SyslogPriorityEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.SyslogProtocolEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.TxPowerOptionEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.UplinkModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap.Fields;
import com.ruckus.cloud.wifi.eda.servicemodel.ApClientAdmissionControl;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGeoLocation;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApIotSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApManagementTrafficVlanSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.ApMesh;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.ApRadioCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.ApRadioParams24G;
import com.ruckus.cloud.wifi.eda.servicemodel.ApRadioParams50G;
import com.ruckus.cloud.wifi.eda.servicemodel.ApRadioParams6G;
import com.ruckus.cloud.wifi.eda.servicemodel.ApRadioParamsDual5G;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSmartMonitor;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSyslog;
import com.ruckus.cloud.wifi.eda.servicemodel.ApTlsEnhancedCertInfo;
import com.ruckus.cloud.wifi.eda.servicemodel.Capabilities;
import com.ruckus.cloud.wifi.eda.servicemodel.CapabilitiesApModel;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortOverwriteSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.Mesh;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PoeOutModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AntennaTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BandModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BssMinRate6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel24Enum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel50Enum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth24GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth320MhzGroup6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth5GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LocationHeightTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LocationSourceEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MeshModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MeshUplinkModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MgmtTxRate6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ScanMethodEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TxPower6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TxPowerEnum;
import com.ruckus.cloud.wifi.entitylistener.ddccm.DdccmConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.FirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.exception.CapabilityNotFoundException;
import com.ruckus.cloud.wifi.service.impl.config.MockFirmwareCapabilityServiceTestConfig;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.context.ConfigDataApplicationContextInitializer;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.ContextConfiguration;

@WifiUnitTest
@ContextConfiguration(initializers = ConfigDataApplicationContextInitializer.class)
public class DdccmApOperationBuilderTest {

  @SpyBean
  private DdccmApOperationBuilder ddccmApOperationBuilder;

  @SpyBean
  private FirmwareCapabilityService firmwareCapabilityService;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyMspTenantId");

  @BeforeEach
  public void before() throws Exception {
    doReturn(true).when(ddccmApOperationBuilder).hasChanged(any(), any());
    var capabilitiesApModel = new CapabilitiesApModel();
    capabilitiesApModel.setVersion("6.2.3.103.244");
    capabilitiesApModel.setLldpEnable(true);
    capabilitiesApModel.setLldpHoldTime((short) 120);
    capabilitiesApModel.setLldpAdInterval((short) 30);
    capabilitiesApModel.setLldpMgmtEnable(true);
    when(firmwareCapabilityService.getVenueCapabilitiesApModel(nullable(Venue.class), anyString()))
        .thenReturn(capabilitiesApModel);
    var capabilities = new Capabilities();
    capabilities.setApModels(List.of(capabilitiesApModel));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());
  }

  private static final String MDNS_PROXY_ID = "36395589-1491-472a-84cd-9457ba7a96b3";

  @Test
  public void testAddApWithValidMac() {
    Ap ap = mockAp();
    ap.setMac("AA:BB:CC:DD:EE:FF");

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    Assertions.assertThat(ddccmAp.getMac().getValue()).isEqualTo(ap.getMac());
  }

  @Test
  public void testAddApWithInvalidMac() {
    Ap ap = mockAp();

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    Assertions.assertThat(ddccmAp.getMac().getValue()).isEmpty();
  }

  @Test
  public void testUpdateAp_without_radioCustomization() {
    Ap ap = mockAp();

    ApRadioCustomization apRadioCustomization = ap.getRadioCustomization();

    assertTrue(apRadioCustomization.getUseVenueSettings());
    assertNull(apRadioCustomization.getEnable24G());
    assertNull(apRadioCustomization.getEnable50G());
    assertNull(apRadioCustomization.getEnable6G());
    assertNull(apRadioCustomization.getApRadioParams24G().getAllowedChannels());
    assertNull(apRadioCustomization.getApRadioParams50G().getAllowedChannels());
    assertNull(apRadioCustomization.getApRadioParams6G().getAllowedChannels());
    assertFalse(apRadioCustomization.getApRadioParams6G().getEnableAfc());

    ApRadioParamsDual5G apRadioParamsDual5G = apRadioCustomization.getApRadioParamsDual5G();

    assertNull(apRadioParamsDual5G.getLower5gEnabled());
    assertNull(apRadioParamsDual5G.getUpper5gEnabled());
    assertNull(apRadioParamsDual5G.getRadioParamsLower5G().getAllowedChannels());
    assertNull(apRadioParamsDual5G.getRadioParamsUpper5G().getAllowedChannels());

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    assertFalse(ddccmAp.hasRadioCustomization());
  }

  @Test
  public void testUpdateAp_with_geoLocation() {
    var ap = mockAp();
    ap.setModel("R560");
    ap.setGeoLocation(new ApGeoLocation());
    var geoLocation = ap.getGeoLocation();

    geoLocation.setSource(LocationSourceEnum.MOBILE_APP);
    geoLocation.setHeightType(LocationHeightTypeEnum.AGL);
    geoLocation.setLongitude(3.1f);
    geoLocation.setLatitude(8.8f);
    geoLocation.setHeight(10f);
    geoLocation.setLateralUncertainty(0.02f);
    geoLocation.setVerticalUncertainty(0.1f);
    geoLocation.setUpdatedTimestamp(new Date().getTime());

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    var aplocation = ddccmAp.getApLocation();

    assertEquals(CcmSource.MOBILE_APP, aplocation.getSource());
    assertEquals(CcmHeightType.AGL, aplocation.getHeightType());
    assertEquals(8.8f, aplocation.getLatitude().getValue());
    assertEquals(3.1f, aplocation.getLongitude().getValue());
    assertEquals(10f, aplocation.getHeight().getValue());
    assertEquals(0.02f, aplocation.getLateralUncertainty().getValue());
    assertEquals(0.1f, aplocation.getVerticalUncertainty().getValue());
    assertEquals(geoLocation.getUpdatedTimestamp(), aplocation.getUpdatedTimestamp().getValue());
    assertEquals(GpsMode.MANUAL, ddccmAp.getGpsMode());
  }

  @Test
  public void testUpdateAp_without_geoLocation() {
    var ap = mockAp();
    ap.setModel("R770");

    var geoLocation = ap.getGeoLocation();

    assertEquals(LocationSourceEnum.NO_LOCATION, geoLocation.getSource());
    assertEquals(LocationHeightTypeEnum.UNDEFINED, geoLocation.getHeightType());
    assertEquals(0, geoLocation.getLatitude());
    assertEquals(0, geoLocation.getLongitude());
    assertEquals(0, geoLocation.getHeight());
    assertEquals(0, geoLocation.getLateralUncertainty());
    assertEquals(0, geoLocation.getVerticalUncertainty());

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    var aplocation = ddccmAp.getApLocation();

    assertEquals(CcmSource.NO_LOCATION, aplocation.getSource());
    assertEquals(CcmHeightType.UNDEFINED, aplocation.getHeightType());
    assertEquals(0, aplocation.getLatitude().getValue());
    assertEquals(0, aplocation.getLongitude().getValue());
    assertEquals(0, aplocation.getHeight().getValue());
    assertEquals(0, aplocation.getLateralUncertainty().getValue());
    assertEquals(0, aplocation.getVerticalUncertainty().getValue());
    assertEquals(0, aplocation.getUpdatedTimestamp().getValue());
    assertEquals(GpsMode.GPS, ddccmAp.getGpsMode());
  }

  @Test
  public void testUpdateAp_with_default_radioCustomization() {
    Ap ap = mockAp();

    resetApRadioCustomization(ap);

    ApRadioCustomization apRadioCustomization = ap.getRadioCustomization();

    assertTrue(apRadioCustomization.getUseVenueSettings());
    assertNull(apRadioCustomization.getEnable24G());
    assertNull(apRadioCustomization.getEnable50G());
    assertNull(apRadioCustomization.getEnable6G());
    assertNull(apRadioCustomization.getApRadioParams24G().getAllowedChannels());
    assertNull(apRadioCustomization.getApRadioParams50G().getAllowedChannels());
    assertNull(apRadioCustomization.getApRadioParams6G().getAllowedChannels());
    assertFalse(apRadioCustomization.getApRadioParams6G().getEnableAfc());

    ApRadioParamsDual5G apRadioParamsDual5G = apRadioCustomization.getApRadioParamsDual5G();

    assertNull(apRadioParamsDual5G.getLower5gEnabled());
    assertNull(apRadioParamsDual5G.getUpper5gEnabled());
    assertNull(apRadioParamsDual5G.getRadioParamsLower5G().getAllowedChannels());
    assertNull(apRadioParamsDual5G.getRadioParamsUpper5G().getAllowedChannels());

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    assertFalse(ddccmAp.hasRadioCustomization());
  }

  @Test
  public void testUpdateAp_multicast_rate_limit() {
    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = this
        .testUpdateAp_with_custom_radioCustomization();

    assertEquals(100,
        ddccmApRadioCustomization.getRadio60().getMulticastRateLimitUplinkRl().getValue());
    assertEquals(6,
        ddccmApRadioCustomization.getRadio60().getMulticastRateLimitDownlinkRl().getValue());
  }

  @Test
  public void testUpdateAp_multicast_rate_limit_not_enable() {
    var ap = mockAp();
    resetApRadioCustomization(ap);
    ap.getRadioCustomization().setEnable24G(true);
    ap.getRadioCustomization().setEnable50G(true);
    ap.getRadioCustomization().setEnable6G(true);
    ap.getRadioCustomization().getApRadioParams6G()
        .setAllowedChannels(Arrays.asList(Channel6GEnum._1, Channel6GEnum._5));
    ap.getRadioCustomization().getApRadioParams6G().setEnableMulticastDownlinkRateLimiting(false);
    ap.getRadioCustomization().getApRadioParams6G().setEnableMulticastUplinkRateLimiting(false);

    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = this
        .testUpdateAp_with_custom_radioCustomization(ap);

    assertTrue(
        ddccmApRadioCustomization.getRadio60().hasMulticastRateLimitUplinkRl());
    assertTrue(
        ddccmApRadioCustomization.getRadio60().hasMulticastRateLimitDownlinkRl());
    assertEquals(0,
        ddccmApRadioCustomization.getRadio60().getMulticastRateLimitUplinkRl().getValue());
    assertEquals(0,
        ddccmApRadioCustomization.getRadio60().getMulticastRateLimitDownlinkRl().getValue());
  }

  private com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization testUpdateAp_with_custom_radioCustomization() {
    return testUpdateAp_with_custom_radioCustomization(true);
  }

  private com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization testUpdateAp_with_custom_radioCustomization(
      boolean enable6G) {
    var ap = mockAp();
    resetApRadioCustomization(ap);
    var apRadioCustomization = ap.getRadioCustomization();

    apRadioCustomization.setEnable24G(true);
    apRadioCustomization.setEnable50G(true);
    apRadioCustomization.setEnable6G(enable6G);

    apRadioCustomization.getApRadioParams6G().setEnableMulticastUplinkRateLimiting(true);
    apRadioCustomization.getApRadioParams6G().setEnableMulticastDownlinkRateLimiting(true);
    apRadioCustomization.getApRadioParams6G().setMulticastUplinkRateLimiting(100);
    apRadioCustomization.getApRadioParams6G().setMulticastDownlinkRateLimiting(6);
    apRadioCustomization.getApRadioParams6G()
        .setAllowedChannels(Arrays.asList(Channel6GEnum._1, Channel6GEnum._5));
    return testUpdateAp_with_custom_radioCustomization(ap);
  }

  private com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization testUpdateAp_with_custom_radioCustomization(
      Ap ap) {
    var apRadioCustomization = ap.getRadioCustomization();

    assertTrue(apRadioCustomization.getUseVenueSettings());
    assertNull(apRadioCustomization.getApRadioParams24G().getAllowedChannels());
    assertNull(apRadioCustomization.getApRadioParams50G().getAllowedChannels());
    assertNotNull(apRadioCustomization.getApRadioParams6G().getAllowedChannels());

    ApRadioParamsDual5G apRadioParamsDual5G = apRadioCustomization.getApRadioParamsDual5G();

    apRadioParamsDual5G.setEnabled(false);
    apRadioCustomization.getApRadioParams6G().setEnableAfc(true);

    assertNull(apRadioParamsDual5G.getLower5gEnabled());
    assertNull(apRadioParamsDual5G.getUpper5gEnabled());
    assertNull(apRadioParamsDual5G.getRadioParamsLower5G().getAllowedChannels());
    assertNull(apRadioParamsDual5G.getRadioParamsUpper5G().getAllowedChannels());

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = ddccmAp
        .getRadioCustomization();

    assertFalse(ddccmApRadioCustomization.getOldDual5GEnabled());
    assertFalse(ddccmApRadioCustomization.hasDual5GEnabled());
    assertFalse(ddccmApRadioCustomization.hasRadio24());
    assertFalse(ddccmApRadioCustomization.hasRadio50());
    assertFalse(ddccmApRadioCustomization.hasRadio50Lower());
    assertFalse(ddccmApRadioCustomization.hasRadio50Upper());
    assertTrue(ddccmApRadioCustomization.hasRadio60());
    assertEquals(apRadioCustomization.getEnable6G(),
        ddccmApRadioCustomization.getRadio60().getWlanServiceEnabled().getValue());

    if (ddccmApRadioCustomization.getRadio60().getWlanServiceEnabled().getValue()) {
      assertApRadioParams6G(apRadioCustomization.getApRadioParams6G(),
          ddccmApRadioCustomization.getRadio60());
    }

    return ddccmApRadioCustomization;
  }

  @Test
  public void testUpdateAp_with_custom_radioCustomization_and_6g_data_control() {
    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = this
        .testUpdateAp_with_custom_radioCustomization();

    assertEquals(BssMinRate6GEnum.HE_MCS_3.toString(),
        ddccmApRadioCustomization.getRadio60().getBssMinRate6G());
    assertEquals(MgmtTxRate6GEnum._24.toString(),
        ddccmApRadioCustomization.getRadio60().getMgmtTxRate6G());
  }

  @Test
  public void testUpdateAp_with_custom_radioCustomization_and_afc_disabled() {
    doReturn(false).when(ddccmApOperationBuilder).isAfcFeatureEnabled();

    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = this
        .testUpdateAp_with_custom_radioCustomization();

    assertTrue(ddccmApRadioCustomization.getRadio60().getLowPowerIndoorModeEnable());
  }

  @Test
  public void testUpdateAp_with_custom_radioCustomization_and_afc_enabled() {
    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = this
        .testUpdateAp_with_custom_radioCustomization();

    assertFalse(ddccmApRadioCustomization.getRadio60().getLowPowerIndoorModeEnable());
  }

  @Test
  public void testUpdateAp_with_custom_radioCustomization_and_6G_disabled() {
    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = this
        .testUpdateAp_with_custom_radioCustomization(false);

    assertTrue(ddccmApRadioCustomization.getRadio60().getLowPowerIndoorModeEnable());
  }

  @Test
  public void testUpdateAp_with_custom_radioCustomization_dual5G() {
    Ap ap = mockAp();

    resetApRadioCustomization(ap);

    ApRadioCustomization apRadioCustomization = ap.getRadioCustomization();

    apRadioCustomization.setEnable24G(true);
    apRadioCustomization.getApRadioParams24G()
        .setAllowedChannels(Arrays.asList(Channel24Enum._1, Channel24Enum._2));

    assertTrue(apRadioCustomization.getUseVenueSettings());
    ApRadioParamsDual5G apRadioParamsDual5G = apRadioCustomization.getApRadioParamsDual5G();

    apRadioParamsDual5G.setEnabled(true);
    apRadioParamsDual5G.setLower5gEnabled(true);
    apRadioParamsDual5G.setUpper5gEnabled(true);
    apRadioParamsDual5G.getRadioParamsUpper5G()
        .setAllowedChannels(Arrays.asList(Channel50Enum._116, Channel50Enum._120));
    apRadioParamsDual5G.getRadioParamsLower5G()
        .setAllowedChannels(Arrays.asList(Channel50Enum._36, Channel50Enum._40));


    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = ddccmAp
        .getRadioCustomization();

    assertTrue(ddccmApRadioCustomization.getOldDual5GEnabled());
    assertEquals(BoolValue.of(true), ddccmApRadioCustomization.getDual5GEnabled());
    assertTrue(ddccmApRadioCustomization.getRadio24().getWlanServiceEnabled().getValue());
    assertTrue(ddccmApRadioCustomization.getRadio50Lower().getWlanServiceEnabled().getValue());
    assertTrue(ddccmApRadioCustomization.getRadio50Upper().getWlanServiceEnabled().getValue());
    assertFalse(ddccmApRadioCustomization.hasRadio50());
    assertFalse(ddccmApRadioCustomization.hasRadio60());

    assertApRadioParams24G(apRadioCustomization.getApRadioParams24G(),
        ddccmApRadioCustomization.getRadio24());
    assertApRadioParams5G(apRadioParamsDual5G.getRadioParamsLower5G(),
        ddccmApRadioCustomization.getRadio50Lower());
    assertApRadioParams5G(apRadioParamsDual5G.getRadioParamsUpper5G(),
        ddccmApRadioCustomization.getRadio50Upper());
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_TX_POWER_TOGGLE)
  public void testUpdateAp_with_custom_radioCustomization_txPowerOldFirmware() {
    // Given
    Ap ap = mockAp();
    ap.setModel("R550");
    resetApRadioCustomization(ap);
    ApRadioCustomization apRadioCustomization = ap.getRadioCustomization();
    apRadioCustomization.setUseVenueSettings(false);
    apRadioCustomization.setEnable24G(true);
    apRadioCustomization.setEnable50G(true);
    apRadioCustomization.setEnable6G(true);
    apRadioCustomization.getApRadioParams24G().setAllowedChannels(List.of(Channel24Enum._13));
    apRadioCustomization.getApRadioParams24G().setTxPower(TxPowerEnum.MIN);
    apRadioCustomization.getApRadioParams50G().setAllowedChannels(List.of(Channel50Enum._36));
    apRadioCustomization.getApRadioParams50G().setTxPower(TxPowerEnum._10);
    apRadioCustomization.getApRadioParams6G().setAllowedChannels(List.of(Channel6GEnum._21));
    apRadioCustomization.getApRadioParams6G().setTxPower(TxPower6GEnum._17);

    // When
    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    // Then
    validataApBasicInfo(ap, ddccmAp);
    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = ddccmAp.getRadioCustomization();
    assertEquals(TxPowerEnum.MIN.toString().toLowerCase(), ddccmApRadioCustomization.getRadio24().getTxPower());
    assertEquals(TxPowerOptionEnum.MIN, ddccmApRadioCustomization.getRadio24().getTxPowerOption());
    assertEquals(TxPowerEnum._10.toString(), ddccmApRadioCustomization.getRadio50().getTxPower());
    assertEquals(TxPowerOptionEnum._10, ddccmApRadioCustomization.getRadio50().getTxPowerOption());
    assertEquals(TxPower6GEnum._10.toString(), ddccmApRadioCustomization.getRadio60().getTxPower());
    assertEquals(TxPowerOptionEnum._17, ddccmApRadioCustomization.getRadio60().getTxPowerOption());
  }

  @Test
  public void testUpdateAp_with_custom_radioCustomization_and_per_radio_customize_enabled() {
    var ap = mockAp();
    resetApRadioCustomization(ap);
    var apRadioCustomization = ap.getRadioCustomization();

    apRadioCustomization.getApRadioParams24G().setUseVenueSettings(true);
    apRadioCustomization.getApRadioParams50G().setUseVenueSettings(false);
    apRadioCustomization.setEnable50G(false);

    apRadioCustomization.getApRadioParams6G().setUseVenueSettings(false);
    apRadioCustomization.setEnable6G(true);
    apRadioCustomization.getApRadioParams6G()
        .setAllowedChannels(Arrays.asList(Channel6GEnum._1, Channel6GEnum._5));

    apRadioCustomization.getApRadioParamsDual5G().setUseVenueEnabled(false);
    apRadioCustomization.getApRadioParamsDual5G().setEnabled(false);
    apRadioCustomization.getApRadioParamsDual5G().getRadioParamsUpper5G().setUseVenueSettings(true);
    apRadioCustomization.getApRadioParamsDual5G().getRadioParamsLower5G().setUseVenueSettings(true);
    apRadioCustomization.getApRadioParams6G().setEnableAfc(true);

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = ddccmAp
        .getRadioCustomization();

    assertFalse(ddccmApRadioCustomization.getOldDual5GEnabled());
    assertEquals(BoolValue.of(false), ddccmApRadioCustomization.getDual5GEnabled());
    assertFalse(ddccmApRadioCustomization.hasRadio24());
    assertTrue(ddccmApRadioCustomization.hasRadio50());
    assertFalse(ddccmApRadioCustomization.getRadio50().getWlanServiceEnabled().getValue());
    assertFalse(ddccmApRadioCustomization.hasRadio50Lower());
    assertFalse(ddccmApRadioCustomization.hasRadio50Upper());
    assertTrue(ddccmApRadioCustomization.hasRadio60());
    assertTrue(ddccmApRadioCustomization.getRadio60().getWlanServiceEnabled().getValue());

    assertApRadioParams6G(apRadioCustomization.getApRadioParams6G(),
        ddccmApRadioCustomization.getRadio60());
  }

  @Test
  public void testUpdateAp_with_apSyslog() {
    Ap ap = mockAp();

    ApSyslog apSyslog = new ApSyslog();
    apSyslog.setEnabled(true);
    apSyslog.setServer("***********");
    ap.setApSyslog(apSyslog);

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    Syslog ddccmApSyslog = ddccmAp.getSyslog();

    assertEquals(apSyslog.getServer(), ddccmApSyslog.getAddress());
    assertEquals(Int32Value.of(443), ddccmApSyslog.getPort());
    assertEquals(SyslogFacilityEnum.KEEP_ORIGINAL, ddccmApSyslog.getFacility());
    assertEquals(SyslogFlowLevelEnum.SyslogFlowLevelEnum_ALL, ddccmApSyslog.getFlowLevel());
    assertEquals(SyslogPriorityEnum.SyslogPriorityEnum_EMERGENCY, ddccmApSyslog.getPriority());
    assertEquals(SyslogProtocolEnum.RSYSPROTO_TLS, ddccmApSyslog.getProtocol());
  }

  @Test
  public void testUpdateAp_with_multicastDnsServiceProfile() {
    Ap ap = mockAp();
    ap.setMulticastDnsProxyServiceProfileId(MDNS_PROXY_ID);
    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();

    assertEquals(MDNS_PROXY_ID, ddccmAp.getBonjourGateway().getId());
  }

  @Test
  public void testCapabilitiesApModel_lldpDefaultApModel() throws CapabilityNotFoundException {
    CapabilitiesApModel capabilitiesApModel = ddccmApOperationBuilder.getFirstLldpEnableApModel(new Venue());

    // Then
    assertNotNull(capabilitiesApModel);
    assertTrue(capabilitiesApModel.getLldpEnable());
    assertTrue(capabilitiesApModel.getLldpMgmtEnable());
    assertEquals(120,
        Integer.valueOf(capabilitiesApModel.getLldpHoldTime()));
    assertEquals(30,
        Integer.valueOf(capabilitiesApModel.getLldpAdInterval()));
  }

  @Test
  public void testUpdateAp_with_apSnmp_enable() {
    var ap = mockAp();
    var apSnmpAgentProfile = new ApSnmpAgentProfile();
    apSnmpAgentProfile.setId(UUID.randomUUID().toString().replace("-", ""));

    var apSnmpAgent = new ApSnmpAgent();
    apSnmpAgent.setEnableApSnmp(true);
    apSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);

    ap.setApSnmpAgent(apSnmpAgent);

    var ddccmAp = getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    assertTrue(ddccmAp.getSnmpAgent().getEnabled());
    assertEquals(apSnmpAgentProfile.getId(),
        ddccmAp.getSnmpAgent().getApSnmpAgentProfileId().getValue());
  }

  @Test
  public void testUpdateAp_with_apSnmp_disable() {
    var ap = mockAp();

    var ddccmAp = getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    assertFalse(ddccmAp.getSnmpAgent().getEnabled());
    assertFalse(ddccmAp.getSnmpAgent().hasApSnmpAgentProfileId());
  }

  @Test
  public void testUpdateAp_with_default_lldp() {
    var ap = mockAp();
    ap.getApGroup().setVenue(new Venue());
    ap.setModel("R770");
    ApModelSpecific apModelSpecific = new ApModelSpecific();
    var apLanPort = new ApLanPort();
    apLanPort.setPortId("1");
    var profile1 = new ApLanPortProfile(CommonTestFixture.randomId());
    profile1.setTenant(ap.getApGroup().getTenant());
    profile1.setApLanPortId(1);
    profile1.setType(ApLanPortTypeEnum.TRUNK);
    profile1.setUntagId((short) 1);
    profile1.setVlanMembers("1-4094");
    apLanPort.setApLanPortProfile(profile1);
    apModelSpecific.setLanPorts(List.of(apLanPort));
    ap.setModelSpecific(apModelSpecific);

    var ddccmAp = getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();
    assertEquals(30, ddccmAp.getModel().getLldp().getAdvertiseIntervalInSec());
    assertEquals(120, ddccmAp.getModel().getLldp().getHoldTimeInSec());
  }


  @Test
  public void testApMesh_enableFF() {
    Ap ap = mockAp();
    ApMesh apMesh = ap.getMesh();
    ap.getApGroup().setVenue(new Venue());
    assertEquals(MeshModeEnum.AUTO, apMesh.getMeshMode());
    assertEquals(MeshUplinkModeEnum.SMART, apMesh.getUplinkMode());
    assertNull(apMesh.getUplinkMacAddresses());
    assertFalse(ap.getApGroup().getVenue().getMesh().getEnabled());

    // when venue mesh is disable
    List<Operation> operations = ddccmApOperationBuilder.build(new NewTxEntity<>(ap),
        emptyTxChanges());
    assertEquals(2, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    assertFalse(operations.get(0).getAp().hasApMesh());

    // when venue mesh is enable
    ap.getApGroup().getVenue().setMesh(new Mesh());
    ap.getApGroup().getVenue().getMesh().setEnabled(true);
    operations = ddccmApOperationBuilder.build(new NewTxEntity<>(ap), emptyTxChanges());
    com.ruckus.acx.ddccm.protobuf.wifi.ApMesh result = operations.get(0).getAp().getApMesh();
    assertEquals(
        com.ruckus.acx.ddccm.protobuf.wifi.MeshModeEnum.valueOf(MeshModeEnum.AUTO.name()),
        result.getMeshMode());
    assertEquals(UplinkModeEnum.valueOf(MeshUplinkModeEnum.SMART.name()), result.getUplinkMode());
    assertEquals(StringUtils.EMPTY, result.getUplinkEntryList());
    assertTrue(result.getMeshDownlinkEnabled());

    apMesh.setMeshMode(MeshModeEnum.MESH);
    apMesh.setUplinkMode(MeshUplinkModeEnum.MANUAL);
    apMesh.setUplinkMacAddresses(List.of("AA:BB:CC:DD:EE:FF", "11:22:33:44:55:66"));
    ap.setMesh(apMesh);
    operations = ddccmApOperationBuilder.build(new NewTxEntity<>(ap), emptyTxChanges());
    result = operations.get(0).getAp().getApMesh();
    assertEquals(
        com.ruckus.acx.ddccm.protobuf.wifi.MeshModeEnum.valueOf(MeshModeEnum.MESH.name()),
        result.getMeshMode());
    assertEquals(UplinkModeEnum.valueOf(MeshUplinkModeEnum.MANUAL.name()),
        result.getUplinkMode());
    assertEquals(StringUtils.join(apMesh.getUplinkMacAddresses(), ','),
        result.getUplinkEntryList());
    assertTrue(result.getMeshDownlinkEnabled());
  }

  @Test
  public void testUpdateAp_with_custom_radioCustomization_channel_bandwidth_group1() {
    Ap ap = mockAp();

    resetApRadioCustomization(ap);
    ApRadioCustomization apRadioCustomization = ap.getRadioCustomization();
    apRadioCustomization.setEnable6G(true);
    apRadioCustomization.getApRadioParams6G()
        .setAllowedChannels(Arrays.asList(Channel6GEnum._1, Channel6GEnum._5));
    apRadioCustomization.getApRadioParams6G()
        .setChannelBandwidth320MhzGroup(ChannelBandwidth320MhzGroup6GEnum._320MHz_1);

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();
    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = ddccmAp
        .getRadioCustomization();

    assertTrue(ddccmApRadioCustomization.getRadio60().getChannelBandwidth320MhzGroup().equals(
        com.ruckus.acx.ddccm.protobuf.wifi.ChannelBandwidth320MhzGroup6GEnum._320MHZ_1));
  }

  @Test
  public void testUpdateAp_with_custom_radioCustomization_channel_bandwidth_group_auto() {
    Ap ap = mockAp();

    resetApRadioCustomization(ap);
    ApRadioCustomization apRadioCustomization = ap.getRadioCustomization();
    apRadioCustomization.setEnable6G(true);
    apRadioCustomization.getApRadioParams6G()
            .setAllowedChannels(Arrays.asList(Channel6GEnum._1, Channel6GEnum._5));
    apRadioCustomization.getApRadioParams6G()
        .setChannelBandwidth320MhzGroup(ChannelBandwidth320MhzGroup6GEnum.AUTO);

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();
    com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization ddccmApRadioCustomization = ddccmAp
        .getRadioCustomization();

    assertTrue(ddccmApRadioCustomization.getRadio60().getChannelBandwidth320MhzGroup().equals(
        com.ruckus.acx.ddccm.protobuf.wifi.ChannelBandwidth320MhzGroup6GEnum._320MHZ_AUTO));
  }

  @Test
  public void testApModelApLed() {
    // case 1
    var ap = mockAp();
    ap.setMac("AA:BB:CC:DD:EE:FF");

    var ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    assertNotNull(ddccmAp);
    assertFalse(ddccmAp.hasModel());

    // case 2
    var apModelSpecific = new ApModelSpecific();
    ap.setModel("R610");
    ap.getApGroup().setVenue(new Venue());
    ap.setModelSpecific(apModelSpecific);

    ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    assertNotNull(ddccmAp);
    assertTrue(ddccmAp.hasModel());
    assertFalse(ddccmAp.getModel().hasLedStatusEnabled());
    assertEquals(0, ddccmAp.getModel().getLanPortCount());

    // case 3
    apModelSpecific.setLedOn(false);
    ap.setModelSpecific(apModelSpecific);

    ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    assertNotNull(ddccmAp);
    assertTrue(ddccmAp.hasModel());
    assertTrue(ddccmAp.getModel().hasLedStatusEnabled());
    assertFalse(ddccmAp.getModel().getLedStatusEnabled().getValue());
  }

  @Test
  void testApModelApUsbPort() {

    var ap = mockAp();
    ap.setMac("AA:BB:CC:DD:EE:FF");
    var ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();
    assertNotNull(ddccmAp);
    assertFalse(ddccmAp.hasModel());

    // first get
    var apModelSpecific = new ApModelSpecific();
    ap.setModel("R670");
    ap.getApGroup().setVenue(new Venue());
    ap.setModelSpecific(apModelSpecific);

    ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    assertNotNull(ddccmAp);
    assertTrue(ddccmAp.hasModel());
    assertFalse(ddccmAp.getModel().hasUsbPortEnabled());
    assertEquals(0, ddccmAp.getModel().getLanPortCount());

    // set usb port enable
    apModelSpecific.setUsbPortEnable(true);
    ap.setModelSpecific(apModelSpecific);

    ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    assertNotNull(ddccmAp);
    assertTrue(ddccmAp.hasModel());
    assertTrue(ddccmAp.getModel().hasUsbPortEnabled());
    assertTrue(ddccmAp.getModel().getUsbPortEnabled().getValue());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_SWITCHABLE_RF_TOGGLE)
  public void testApBandMode() {

    var ap = mockAp();
    ap.setMac("AA:BB:CC:DD:EE:FF");
    var ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();
    assertNotNull(ddccmAp);
    assertFalse(ddccmAp.hasModel());

    // first get
    var apModelSpecific = new ApModelSpecific();
    ap.setModel("R670");
    ap.getApGroup().setVenue(new Venue());
    ap.setModelSpecific(apModelSpecific);

    ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    assertNotNull(ddccmAp);
    assertTrue(ddccmAp.hasModel());
    assertFalse(ddccmAp.getModel().hasBandCombinationMode());

    // set band mode
    apModelSpecific.setBandMode(BandModeEnum.DUAL);
    ap.setModelSpecific(apModelSpecific);

    ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    assertNotNull(ddccmAp);
    assertTrue(ddccmAp.hasModel());
    assertTrue(ddccmAp.getModel().hasBandCombinationMode());
    assertEquals(DdccmConstants.BAND_MODE_DUAL, ddccmAp.getModel().getBandCombinationMode().getValue());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE)
  public void testApAntennaType() {

    var ap = mockAp();
    ap.setMac("AA:BB:CC:DD:EE:FF");
    var ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();
    assertNotNull(ddccmAp);
    assertFalse(ddccmAp.hasModel());

    // first get
    var apModelSpecific = new ApModelSpecific();
    ap.setModel("T670SN");
    ap.getApGroup().setVenue(new Venue());
    ap.setModelSpecific(apModelSpecific);

    ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    assertNotNull(ddccmAp);
    assertTrue(ddccmAp.hasModel());
    assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.AntennaTypeEnum.AntennaTypeEnum_UNSET,
        ddccmAp.getModel().getAntennaType());

    // set antenna type
    apModelSpecific.setAntennaType(AntennaTypeEnum.NARROW);
    ap.setModelSpecific(apModelSpecific);

    ddccmAp = getApOperation(new NewTxEntity<>(ap)).getAp();

    assertNotNull(ddccmAp);
    assertTrue(ddccmAp.hasModel());
    assertNotNull(ddccmAp.getModel().getAntennaType());
    assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.AntennaTypeEnum.Narrow,
        ddccmAp.getModel().getAntennaType());
  }

  @Test
  public void testGetApFwVersion() {
    var ap = mockAp();
    ap.setModel("R550");
    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
        Collections.emptySet())).getAp();
    assertEquals("6.2.3.103.244", ddccmAp.getVersion().getValue());
  }

  private void validataApBasicInfo(Ap expected, com.ruckus.acx.ddccm.protobuf.wifi.Ap actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getApGroup().getId(), actual.getApGroupId());
    assertEquals(expected.getName(), actual.getName());
  }

  private void assertApRadioParams24G(ApRadioParams24G expect24, ApRadio24 radio24) {
    if (expect24.getTxPower() == TxPowerEnum.Auto) {
      assertTrue(radio24.getAutoCellSizing());
    } else {
      assertEquals(expect24.getTxPower().toString().toLowerCase(), radio24.getTxPower());
    }

    if (expect24.getChannelBandwidth() == ChannelBandwidth24GEnum.AUTO) {
      assertEquals("Auto", radio24.getChannelBandWidth());
    } else {
      assertEquals(expect24.getChannelBandwidth().toString(), radio24.getChannelBandWidth());
    }

    assertEquals(expect24.getAllowedChannels().stream()
            .map(e -> Integer.parseInt(e.toString())).collect(Collectors.toList()),
        radio24.getAllowedChannelListList());

    assertEquals(scanMethod(expect24.getMethod()).name(),
        radio24.getChannelSelectionMethod().name());
    if (ScanMethodEnum.CHANNELFLY.equals(expect24.getMethod())) {
      assertEquals((int) expect24.getChangeInterval(), radio24.getChannelChangeFrequency());
    }
  }

  private void assertApRadioParams5G(ApRadioParams50G expect50, ApRadio50 radio50) {
    if (expect50.getTxPower() == TxPowerEnum.Auto) {
      assertTrue(radio50.getAutoCellSizing());
    } else {
      assertEquals(expect50.getTxPower().toString().toLowerCase(), radio50.getTxPower());
    }

    if (expect50.getChannelBandwidth() == ChannelBandwidth5GEnum.AUTO) {
      assertEquals("Auto", radio50.getChannelBandWidth());
    } else {
      assertEquals(expect50.getChannelBandwidth().toString(), radio50.getChannelBandWidth());
    }

    assertEquals(expect50.getAllowedChannels().stream()
            .map(e -> Integer.parseInt(e.toString())).collect(Collectors.toList()),
        radio50.getAllowedChannelListList());

    assertEquals(scanMethod(expect50.getMethod()).name(),
        radio50.getChannelSelectionMethod().name());
    if (ScanMethodEnum.CHANNELFLY.equals(expect50.getMethod())) {
      assertEquals((int) expect50.getChangeInterval(), radio50.getChannelChangeFrequency());
    }
  }

  private void assertApRadioParams6G(ApRadioParams6G expect60, ApRadio60 radio60) {
    assertEquals(expect60.getTxPower().toString().toLowerCase(), radio60.getTxPower());

    if (expect60.getChannelBandwidth() == ChannelBandwidth6GEnum.AUTO) {
      assertEquals("Auto", radio60.getChannelBandWidth());
    } else {
      assertEquals(expect60.getChannelBandwidth().toString(), radio60.getChannelBandWidth());
    }

    assertEquals(expect60.getAllowedChannels().stream()
            .map(e -> Integer.parseInt(e.toString())).collect(Collectors.toList()),
        radio60.getAllowedChannelListList());

    assertEquals(scanMethod(expect60.getMethod()).name(),
        radio60.getChannelSelectionMethod().name());
    if (ScanMethodEnum.CHANNELFLY.equals(expect60.getMethod())) {
      assertEquals((int) expect60.getChangeInterval(), radio60.getChannelChangeFrequency());
    }
  }

  private AutoChannelSelectionMethod scanMethod(ScanMethodEnum method) {
    return switch (method) {
      case CHANNELFLY -> AutoChannelSelectionMethod.ChannelFly;
      case MANUAL -> AutoChannelSelectionMethod.Manual;
      default -> AutoChannelSelectionMethod.BackgroundScanning;
    };
  }

  private void resetApRadioCustomization(Ap ap) {
    ApRadioCustomization apRadioCustomization = new ApRadioCustomization();
    apRadioCustomization.setEnable24G(null);
    apRadioCustomization.setEnable50G(null);
    apRadioCustomization.setEnable6G(null);
    apRadioCustomization.setApRadioParams24G(new ApRadioParams24G());
    apRadioCustomization.setApRadioParams50G(new ApRadioParams50G());
    apRadioCustomization.setApRadioParams6G(new ApRadioParams6G());
    apRadioCustomization.getApRadioParams6G().setBssMinRate6G(BssMinRate6GEnum.HE_MCS_3);
    apRadioCustomization.getApRadioParams6G().setMgmtTxRate6G(MgmtTxRate6GEnum._24);

    ApRadioParamsDual5G apRadioParamsDual5G = new ApRadioParamsDual5G();
    apRadioParamsDual5G.setRadioParamsLower5G(new ApRadioParams50G());
    apRadioParamsDual5G.setRadioParamsUpper5G(new ApRadioParams50G());
    apRadioParamsDual5G.setLower5gEnabled(null);
    apRadioParamsDual5G.setUpper5gEnabled(null);
    apRadioCustomization.setApRadioParamsDual5G(apRadioParamsDual5G);

    ap.setRadioCustomization(apRadioCustomization);
  }

  private Operation getApOperation(TxEntity<Ap> txEntity) {
    List<Operation> operations = ddccmApOperationBuilder
        .build(txEntity, emptyTxChanges());

    assertNotNull(operations);
    assertEquals(2, operations.size()); // Ap + ApTunnelCertInfo

    operations = operations.stream().filter(Operation::hasAp).collect(Collectors.toList());

    assertEquals(1, operations.size()); // Ap

    Operation operation = operations.get(0);

    assertEquals(txEntity.getAction().toString(), operation.getAction().toString());

    return operation;
  }

  private Ap mockAp() {
    ApGroup apGroup = new ApGroup();
    apGroup.setId(UUID.randomUUID().toString());

    Venue venue = new Venue();
    venue.setId(UUID.randomUUID().toString());
    apGroup.setVenue(venue);

    Ap ap = new Ap();
    ap.setId("000000000000");
    ap.setName("test_ap");
    ap.setApGroup(apGroup);

    return ap;
  }
  private ApLanPort mockApLanPort(String portId) {
    var apLanPortProfile = new ApLanPortProfile();
    apLanPortProfile.setApLanPortId(Integer.valueOf(portId));
    apLanPortProfile.setUntagId(Short.valueOf("2"));
    apLanPortProfile.setVlanMembers("2-4094");

    var apLanPort = new ApLanPort();
    apLanPort.setEnabled(true);
    apLanPort.setPortId(portId);
    apLanPort.setApLanPortProfile(apLanPortProfile);
    return apLanPort;
  }

  @Test
  public void testApClientAdmissionControlWhenFFEnable() {
    // Given
    Ap ap = mockAp();
    resetApRadioCustomization(ap);

    ApClientAdmissionControl apClientAdmissionControl = new ApClientAdmissionControl();
    apClientAdmissionControl.setEnable24G(true);
    apClientAdmissionControl.setEnable50G(true);
    apClientAdmissionControl.setMinClientCount24G((short) 10);
    apClientAdmissionControl.setMinClientCount50G((short) 20);
    apClientAdmissionControl.setMaxRadioLoad24G((short) 75);
    apClientAdmissionControl.setMaxRadioLoad50G((short) 75);
    apClientAdmissionControl.setMinClientThroughput24G((short) 0);
    apClientAdmissionControl.setMinClientThroughput50G((short) 0);
    ap.setClientAdmissionControl(apClientAdmissionControl);

    // When
    com.ruckus.acx.ddccm.protobuf.wifi.Ap operations =
        getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    // Then
    assertEquals(
        ap.getClientAdmissionControl().getEnable24G(),
        operations.getRadioCustomization().getRadio24().getClientAdmissionControl().getEnabled());
    assertEquals(
        ap.getClientAdmissionControl().getEnable50G(),
        operations.getRadioCustomization().getRadio50().getClientAdmissionControl().getEnabled());
    assertEquals(
        10,
        operations
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getMinimumClientCount());
    assertEquals(
        20,
        operations
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getMinimumClientCount());
    assertEquals(
        75,
        operations
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getMaxiumumRadioLoad());
    assertEquals(
        75,
        operations
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getMaxiumumRadioLoad());
    assertEquals(
        0,
        operations
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getMinimumClientThroughput(),
        0);
    assertEquals(
        0,
        operations
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getMinimumClientThroughput(),
        0);
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_MGMT_VLAN_AP_LEVEL_TOGGLE)
  public void testApManagementVlanWhenFFEnable() {
    // When Vlan override enabled
    Ap ap = mockAp();
    resetApRadioCustomization(ap);

    ApManagementTrafficVlanSettings apManagementTrafficVlanSettings = new ApManagementTrafficVlanSettings();
    apManagementTrafficVlanSettings.setVlanOverrideEnabled(true);
    apManagementTrafficVlanSettings.setVlanId((short) 1234);
    ap.setApManagementVlan(apManagementTrafficVlanSettings);

    // When
    com.ruckus.acx.ddccm.protobuf.wifi.Ap operations =
        getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    // Then
    assertNotNull(operations);
    assertEquals(
        ManagementVlanModeApLevelEnum.ManagementVlanModeApLevelEnum_USER_DEFINED,
        operations.getApManagementVlanMode());
    assertEquals(Int32Value.of(1234), operations.getApManagementVlanId());

    // When Vlan override disabled
    apManagementTrafficVlanSettings = new ApManagementTrafficVlanSettings();
    apManagementTrafficVlanSettings.setVlanOverrideEnabled(false);
    apManagementTrafficVlanSettings.setVlanId((short) 1234);
    ap.setApManagementVlan(apManagementTrafficVlanSettings);

    // When
    operations = getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    // Then
    assertNotNull(operations);
    assertEquals(
        ManagementVlanModeApLevelEnum.ManagementVlanModeApLevelEnum_KEEP,
        operations.getApManagementVlanMode());
    assertFalse(operations.hasApManagementVlanId());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_AP_MGMT_VLAN_AP_LEVEL_TOGGLE)
  public void testApManagementVlanWhenFFDisable() {
    // Given
    Ap ap = mockAp();
    resetApRadioCustomization(ap);

    ApManagementTrafficVlanSettings apManagementTrafficVlanSettings = new ApManagementTrafficVlanSettings();
    apManagementTrafficVlanSettings.setVlanOverrideEnabled(true);
    apManagementTrafficVlanSettings.setVlanId((short) 1234);
    ap.setApManagementVlan(apManagementTrafficVlanSettings);

    // When
    com.ruckus.acx.ddccm.protobuf.wifi.Ap operations =
        getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    // Then
    assertNotNull(operations);
    assertEquals(
        ManagementVlanModeApLevelEnum.ManagementVlanModeApLevelEnum_UNSET,
        operations.getApManagementVlanMode());
    assertFalse(operations.hasApManagementVlanId());
  }

  @Test
  @FeatureFlag(enable = WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE)
  void testSkipBuildWhenOnlySetTlsEnhancedCsr() {
    reset(ddccmApOperationBuilder);
    var ap = mockAp();
    ap.setMac("AA:BB:CC:DD:EE:FF");

    ApTlsEnhancedCertInfo apTlsEnhancedCertInfo = new ApTlsEnhancedCertInfo();
    apTlsEnhancedCertInfo.setCsr("TEST Csr");
    ap.setTlsEnhancedCertInfo(apTlsEnhancedCertInfo);

    List<Operation> operations = ddccmApOperationBuilder
        .build(new ModifiedTxEntity<>(ap,
                Set.of(Fields.TLSENHANCEDCERTINFO, AbstractBaseEntity.Fields.UPDATEDDATE)),
            emptyTxChanges());
    assertNotNull(operations);
    assertEquals(0, operations.size());
  }

  @Test
  @FeatureFlag(enable = WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE)
  void testTlsEnhancedCert() {
    var ap = mockAp();
    ap.setMac("AA:BB:CC:DD:EE:FF");
    Operation operation = ddccmApOperationBuilder
        .build(new NewTxEntity<>(ap), emptyTxChanges()).get(2);
    assertTrue(operation.hasApEnhancedCertInfo());
    assertEquals(ap.getId(), operation.getApEnhancedCertInfo().getId());

    ApTlsEnhancedCertInfo apTlsEnhancedCertInfo = new ApTlsEnhancedCertInfo();
    apTlsEnhancedCertInfo.setCert("TEST Cert");
    apTlsEnhancedCertInfo.setCaChain("TEST CA Chain");
    ap.setTlsEnhancedCertInfo(apTlsEnhancedCertInfo);

    operation = ddccmApOperationBuilder
        .build(new ModifiedTxEntity<>(ap, Collections.emptySet()), emptyTxChanges()).get(2);
    assertEquals("TEST Cert", operation.getApEnhancedCertInfo().getCert());
    assertEquals("TEST CA Chain", operation.getApEnhancedCertInfo().getCaChain());
  }

  @Test
  @FeatureFlag(disable = WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE)
  void testTlsEnhancedCertWithFFDisabled() {
    var ap = mockAp();
    ap.setMac("AA:BB:CC:DD:EE:FF");

    ApTlsEnhancedCertInfo apTlsEnhancedCertInfo = new ApTlsEnhancedCertInfo();
    apTlsEnhancedCertInfo.setCert("TEST Cert");
    apTlsEnhancedCertInfo.setCaChain("TEST CA Chain");
    ap.setTlsEnhancedCertInfo(apTlsEnhancedCertInfo);

    List<Operation> operations = ddccmApOperationBuilder
        .build(new NewTxEntity<>(ap), emptyTxChanges());
    assertNotNull(operations);
    assertEquals(2, operations.size());
    operations.forEach(op -> assertFalse(op.hasApEnhancedCertInfo()));
  }

  @Test
  @FeatureFlag(enable = WIFI_SMART_MONITOR_DISABLE_WLAN_TOGGLE)
  void testUpdateSmartMonitorWithFFEnabled() {
    var ap = mockAp();
    var apSmartMonitor = new ApSmartMonitor();
    apSmartMonitor.setEnabled(true);
    apSmartMonitor.setThreshold((short) 8);
    apSmartMonitor.setInterval((short) 15);
    ap.setSmartMonitor(apSmartMonitor);

    var ddccmAp = getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    assertTrue(ddccmAp.hasSmartMonitor());
    assertTrue(ddccmAp.getSmartMonitor().getEnabled());
    assertEquals(8, ddccmAp.getSmartMonitor().getThresholdTimes());
    assertEquals(15, ddccmAp.getSmartMonitor().getIntervalSeconds());
  }

  @Test
  @FeatureFlag(disable = WIFI_SMART_MONITOR_DISABLE_WLAN_TOGGLE)
  void testUpdateSmartMonitorWithFFDisabled() {
    var ap = mockAp();

    var ddccmAp = getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    assertFalse(ddccmAp.hasSmartMonitor());
  }

  @Test
  @FeatureFlag(enable = IOT_MQTT_BROKER_TOGGLE)
  void testUpdateIotSettingsWithFFEnabled() {
    var ap = mockAp();
    var apIotSettings = new ApIotSettings();
    apIotSettings.setEnabled(true);
    apIotSettings.setMqttBrokerAddress("iot.ruckus.com");
    ap.setIotSettings(apIotSettings);

    var ddccmAp = getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    assertTrue(ddccmAp.hasIotSettings());
    assertTrue(ddccmAp.getIotSettings().getEnabled());
    assertEquals("iot.ruckus.com", ddccmAp.getIotSettings().getMqttBrokerAddress());
  }

  @Test
  @FeatureFlag(disable = IOT_MQTT_BROKER_TOGGLE)
  void testUpdateIotSettingsWithFFDisabled() {
    var ap = mockAp();

    var ddccmAp = getApOperation(new ModifiedTxEntity<>(ap, Collections.emptySet())).getAp();

    assertFalse(ddccmAp.hasIotSettings());
  }

  @Test
  @FeatureFlag(disable = ACX_UI_ETHERNET_TOGGLE)
  public void test_ApLanOverwriteSettings_FF_Disabled() {
    ApLanPort src = new ApLanPort();
    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.Builder apLanPortBuilder_empty = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort
        .newBuilder();

    ddccmApOperationBuilder.apLanOverwriteSettings(src, apLanPortBuilder_empty);

    assertFalse(apLanPortBuilder_empty.getOverrideVlanEnabled().getValue());
  }

  @Test
  @FeatureFlag(enable = ACX_UI_ETHERNET_TOGGLE)
  public void test_ApLanOverwriteSettings_OverrideVlan_False() {
    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.Builder apLanPortBuilder_null = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort
        .newBuilder();

    ddccmApOperationBuilder.apLanOverwriteSettings(null, apLanPortBuilder_null);

    assertFalse(apLanPortBuilder_null.getOverrideVlanEnabled().getValue());

    ApLanPort src = new ApLanPort();
    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.Builder apLanPortBuilder_empty = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort
        .newBuilder();

    ddccmApOperationBuilder.apLanOverwriteSettings(src, apLanPortBuilder_empty);

    assertFalse(apLanPortBuilder_empty.getOverrideVlanEnabled().getValue());

    LanPortOverwriteSettings overwriteSettings = new LanPortOverwriteSettings();
    src.setApLanPortOverwriteSettings(overwriteSettings);
    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.Builder apLanPortBuilder_empty2 = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort
        .newBuilder();

    ddccmApOperationBuilder.apLanOverwriteSettings(src, apLanPortBuilder_empty2);

    assertFalse(apLanPortBuilder_empty2.getOverrideVlanEnabled().getValue());
  }

  @Test
  @FeatureFlag(enable = ACX_UI_ETHERNET_TOGGLE)
  public void testApLanOverwriteSettings_UntagIdNull() {
    // Arrange
    ApLanPort src = new ApLanPort();
    LanPortOverwriteSettings overwriteSettings = new LanPortOverwriteSettings();
    overwriteSettings.setVlanMembers("1-4094");
    src.setApLanPortOverwriteSettings(overwriteSettings);
    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.Builder apLanPortBuilder = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort
        .newBuilder();

    // Act
    ddccmApOperationBuilder.apLanOverwriteSettings(src, apLanPortBuilder);

    // Assert
    assertTrue(apLanPortBuilder.getOverrideVlanEnabled().getValue());
    assertEquals(0, apLanPortBuilder.getOverrideUntagVlanId());
    assertEquals("1-4094", apLanPortBuilder.getOverrideVlanMembers());
  }

  @Test
  @FeatureFlag(enable = ACX_UI_ETHERNET_TOGGLE)
  public void testApLanOverwriteSettings_VlanMembersNull() {
    // Arrange
    ApLanPort src = new ApLanPort();
    LanPortOverwriteSettings overwriteSettings = new LanPortOverwriteSettings();
    overwriteSettings.setUntagId((short) 1);
    src.setApLanPortOverwriteSettings(overwriteSettings);
    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.Builder apLanPortBuilder = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort
        .newBuilder();

    // Act
    ddccmApOperationBuilder.apLanOverwriteSettings(src, apLanPortBuilder);

    // Assert
    assertTrue(apLanPortBuilder.getOverrideVlanEnabled().getValue());
    assertEquals(1, apLanPortBuilder.getOverrideUntagVlanId());
    assertEquals("", apLanPortBuilder.getOverrideVlanMembers());
  }

  @Test
  @FeatureFlag(enable = ACX_UI_ETHERNET_TOGGLE)
  public void testApLanOverwriteSettings_OverrideVlan() {
    // Arrange
    ApLanPort src = new ApLanPort();
    LanPortOverwriteSettings overwriteSettings = new LanPortOverwriteSettings();
    overwriteSettings.setUntagId((short) 1);
    overwriteSettings.setVlanMembers("1-4094");
    src.setApLanPortOverwriteSettings(overwriteSettings);
    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.Builder apLanPortBuilder = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort
        .newBuilder();

    // Act
    ddccmApOperationBuilder.apLanOverwriteSettings(src, apLanPortBuilder);

    // Assert
    assertTrue(apLanPortBuilder.getOverrideVlanEnabled().getValue());
    assertEquals(1, apLanPortBuilder.getOverrideUntagVlanId());
    assertEquals("1-4094", apLanPortBuilder.getOverrideVlanMembers());
  }

  @Test
  @FeatureFlag(enable = ACX_UI_ETHERNET_TOGGLE)
  public void testApLanOverwriteSettings_TrunkType_AlwaysUntagIsOne() {
    // Arrange
    ApLanPort src = new ApLanPort();
    LanPortOverwriteSettings overwriteSettings = new LanPortOverwriteSettings();
    overwriteSettings.setUntagId((short) 2);
    overwriteSettings.setVlanMembers("1-4094");
    src.setApLanPortOverwriteSettings(overwriteSettings);

    ApLanPortProfile apLanPortProfile = new ApLanPortProfile();
    apLanPortProfile.setApLanPortId(1);
    apLanPortProfile.setType(ApLanPortTypeEnum.TRUNK);
    src.setApLanPortProfile(apLanPortProfile);

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.Builder apLanPortBuilder = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort
        .newBuilder();

    // Act
    ddccmApOperationBuilder.apLanOverwriteSettings(src, apLanPortBuilder);

    // Assert
    assertTrue(apLanPortBuilder.getOverrideVlanEnabled().getValue());
    assertEquals(1, apLanPortBuilder.getOverrideUntagId());
    assertEquals(2, apLanPortBuilder.getOverrideUntagVlanId());
    assertEquals("1-4094", apLanPortBuilder.getOverrideVlanMembers());
  }

  @Test
  @FeatureFlag(enable = ACX_UI_ETHERNET_TOGGLE)
  public void testApLanOverwriteSettings_OtherType_AlwaysUntagTheSame() {
    // Arrange
    ApLanPort src = new ApLanPort();
    LanPortOverwriteSettings overwriteSettings = new LanPortOverwriteSettings();
    overwriteSettings.setUntagId((short) 25);
    overwriteSettings.setVlanMembers("1-4094");
    src.setApLanPortOverwriteSettings(overwriteSettings);

    ApLanPortProfile apLanPortProfile = new ApLanPortProfile();
    apLanPortProfile.setApLanPortId(1);
    apLanPortProfile.setType(ApLanPortTypeEnum.ACCESS);
    src.setApLanPortProfile(apLanPortProfile);

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.Builder apLanPortBuilder = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort
        .newBuilder();

    // Act
    ddccmApOperationBuilder.apLanOverwriteSettings(src, apLanPortBuilder);

    // Assert
    assertTrue(apLanPortBuilder.getOverrideVlanEnabled().getValue());
    assertEquals(25, apLanPortBuilder.getOverrideUntagId());
    assertEquals(25, apLanPortBuilder.getOverrideUntagVlanId());
    assertEquals("1-4094", apLanPortBuilder.getOverrideVlanMembers());
  }

  @Test
  void getEthernetProfileIdTest() {
    var port = new ApLanPort();
    port.setApLanPortProfile(new EthernetPortProfile(txCtxExtension.newRandomId()));
    port.getApLanPortProfile().setApLanPortId(1);

    var id = ddccmApOperationBuilder.getEthernetProfileId(port, false);
    assertEquals(port.getApLanPortProfile().getApLanPortId(), id);

    id = ddccmApOperationBuilder.getEthernetProfileId(port, true);
    assertEquals(port.getApLanPortProfile().getApLanPortId(), id);

    port.setLanPortAdoption(new LanPortAdoption(txCtxExtension.newRandomId()));
    port.getLanPortAdoption().setEthernetPortProfileId(2);
    id = ddccmApOperationBuilder.getEthernetProfileId(port, true);
    assertEquals(port.getLanPortAdoption().getEthernetPortProfileId(), id);
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_PASSWORD_PER_AP_TOGGLE)
  void testApPasswordCustomization() {
    // Given
    String apPassword = "test-password";
    Ap ap = new Ap();
    ap.setApPassword(apPassword);
    com.ruckus.acx.ddccm.protobuf.wifi.Ap.Builder config = com.ruckus.acx.ddccm.protobuf.wifi.Ap.newBuilder();
    
    // When
    ddccmApOperationBuilder.apPasswordCustomization(ap, config);

    // Then
    assertEquals(apPassword, config.getApPassword().getValue());
  }

  @Test
  void testApPasswordCustomization_NullPassword() {
    // Given
    String apPassword = "test-password";
    Ap ap = new Ap();
    ap.setApPassword(apPassword);
    com.ruckus.acx.ddccm.protobuf.wifi.Ap.Builder config = com.ruckus.acx.ddccm.protobuf.wifi.Ap.newBuilder();

    // When
    ddccmApOperationBuilder.apPasswordCustomization(ap, config);

    // Then
    assertFalse(config.hasApPassword());
  }

  @Profile("unit")
  @TestConfiguration
  @Import(MockFirmwareCapabilityServiceTestConfig.class)
  static class TestConfig {

  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_POE_OUT_MODE_SETTING_TOGGLE)
  public void testUpdateAp_with_poe_out_mode() {
    Ap ap = mockAp();
    ap.setModel("h670");
    ApModelSpecific model = new ApModelSpecific();
    model.setPoeOutMode(PoeOutModeEnum._802_3at);
    ap.setModelSpecific(model);

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
            Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    assertEquals("PoeOutModeEnum_802_3at", ddccmAp.getModel().getPoeOutMode().toString());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_POE_OUT_MODE_SETTING_TOGGLE)
  public void testUpdateAp_without_poe_out_mode() {
    Ap ap = mockAp();
    ap.setModel("h670");
    ApModelSpecific model = new ApModelSpecific();
    model.setPoeOutMode(PoeOutModeEnum._802_3af);
    ap.setModelSpecific(model);

    com.ruckus.acx.ddccm.protobuf.wifi.Ap ddccmAp = getApOperation(new ModifiedTxEntity<>(ap,
            Collections.emptySet())).getAp();

    validataApBasicInfo(ap, ddccmAp);

    assertEquals("PoeOutModeEnum_802_3af", ddccmAp.getModel().getPoeOutMode().toString());
  }
}
