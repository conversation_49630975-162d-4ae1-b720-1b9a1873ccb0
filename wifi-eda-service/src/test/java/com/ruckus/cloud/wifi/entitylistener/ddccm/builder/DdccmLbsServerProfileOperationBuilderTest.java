package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import com.ruckus.acx.ddccm.protobuf.wifi.LbsProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.redis.YamlPropertySourceFactory;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.PropertySource;

import java.util.List;
import java.util.Set;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@WifiUnitTest
@PropertySource(value = "classpath:application-unit.yml", factory = YamlPropertySourceFactory.class)
public class DdccmLbsServerProfileOperationBuilderTest {

  @SpyBean
  private DdccmLbsServerProfileOperationBuilder ddccmLbsServerProfileOperationBuilder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  public void testAddLbsServerProfile() {

    com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile lbsServerProfile = Generators.lbsServerProfile().generate();

    List<Operation> operations = ddccmLbsServerProfileOperationBuilder.build(new NewTxEntity<>(lbsServerProfile), emptyTxChanges());

    assertNotNull(operations);
    assertEquals(1, operations.size());

    LbsProfile lbsProfileOperation = operations.get(0).getLbsProfile();
    assertNotNull(lbsProfileOperation);
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());

    validateProperties(lbsServerProfile, lbsProfileOperation);
  }

  @Test
  public void testUpdateLbsServerProfile() {

    com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile lbsServerProfile = Generators.lbsServerProfile().generate();

    List<Operation> operations = ddccmLbsServerProfileOperationBuilder.
        build(new ModifiedTxEntity<>(lbsServerProfile, Set.of("password")), emptyTxChanges());

    assertNotNull(operations);
    assertEquals(1, operations.size());

    LbsProfile lbsProfileOperation = operations.get(0).getLbsProfile();
    assertNotNull(lbsProfileOperation);
    assertEquals(EntityAction.MODIFY.toString(), operations.get(0).getAction().toString());

    validateProperties(lbsServerProfile, lbsProfileOperation);
  }

  @Test
  public void testDeleteLbsServerProfile() {

    com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile lbsServerProfile = Generators.lbsServerProfile().generate();

    List<Operation> operations = ddccmLbsServerProfileOperationBuilder
        .build(new DeletedTxEntity<>(lbsServerProfile), emptyTxChanges());

    assertNotNull(operations);
    assertEquals(1, operations.size());

    LbsProfile lbsProfileOperation = operations.get(0).getLbsProfile();
    assertNotNull(lbsProfileOperation);
    assertEquals(EntityAction.DELETE.toString(), operations.get(0).getAction().toString());
    assertEquals(lbsServerProfile.getId(), lbsProfileOperation.getId());
  }

  private void validateProperties(com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile lbsServerProfile,
                                  LbsProfile lbsProfileOperation) {

    assertEquals(lbsServerProfile.getId(), lbsProfileOperation.getId());
    assertEquals(lbsServerProfile.getName(), lbsProfileOperation.getName());
    assertEquals(lbsServerProfile.getServerAddress(), lbsProfileOperation.getUrl());
    assertEquals(lbsServerProfile.getServerPort(), lbsProfileOperation.getPort());
    assertEquals(lbsServerProfile.getPassword(), lbsProfileOperation.getPassword());
    assertEquals(lbsServerProfile.getLbsServerVenueName(), lbsProfileOperation.getVenue());
  }

}
