package com.ruckus.cloud.wifi.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.MulticastDnsProxyServiceProfileRestCtrl;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.service.MulticastDnsProxyServiceProfileServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceRule;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.MulticastDnsProxyServiceProfileRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.MulticaseDnsProxyTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeMulticastDnsProxyServiceProfileRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private MulticastDnsProxyServiceProfileServiceCtrl mDnsProxyServiceProfileServiceCtrl;

  @Test
  public void testAddMulticastDnsProxyServiceProfile(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();

    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getRules().size(), multicastDnsProxyServiceProfile.getRules().size());
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().get(0).getAp().getId(), ap.getId());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId, List.of(multicastDnsProxyServiceProfileViaGet.get(0).getId()));

    String profileId = multicastDnsProxyServiceProfileViaGet.get(0).getId();
    Operations viewmodelOperation = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.ADD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());

    List<Operation> ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());
    // 1 Ap will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .count());

    //When add with serviceName null
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile2 = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile2");
    multicastDnsProxyServiceProfile2.setServiceName(null);
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile2);

    //Then
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertEquals(multicastDnsProxyServiceProfileViaGet.size(), 1);

    assertActivityStatusFail(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);
  }

  @Test
  public void testMulticastDnsProxyServiceProfileViewmodelWithDeleteAp(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getRules().size(), multicastDnsProxyServiceProfile.getRules().size());
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().get(0).getAp().getId(), ap.getId());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    String profileId = multicastDnsProxyServiceProfileViaGet.get(0).getId();
    Operations viewmodelOperation = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.ADD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());

    //delete AP
    deleteAp(ap, tenant);
    Operations viewmodelOperation2 = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> OpType.MOD == o.getOpType())
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(0, viewmodelOperation2.getDocMap().get("venueIds").getListValue().getValuesCount());
  }

  @Test
  public void testUpdateMulticastDnsProxyServiceProfile(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));
    MulticastDnsProxyServiceRule multicastDnsProxyServiceRule = new MulticastDnsProxyServiceRule();
    multicastDnsProxyServiceRule.setService(BridgeServiceEnum.AIRPLAY);
    multicastDnsProxyServiceRule.setEnabled(true);
    multicastDnsProxyServiceRule.setFromVlan(4);
    multicastDnsProxyServiceRule.setToVlan(5);
    multicastDnsProxyServiceProfile.getRules().add(multicastDnsProxyServiceRule);

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getRules().size(), multicastDnsProxyServiceProfile.getRules().size());
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().get(0).getAp().getId(), ap.getId());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    String profileId = multicastDnsProxyServiceProfileViaGet.get(0).getId();
    Operations viewmodelOperation = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.ADD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());

    List<Operation> ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());
    // 1 Ap will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .count());

    //When update one more Ap profile
    multicastDnsProxyServiceProfile.getRules().remove(0);
    multicastDnsProxyServiceRule = new MulticastDnsProxyServiceRule();
    multicastDnsProxyServiceRule.setService(BridgeServiceEnum.AIRPLAY);
    multicastDnsProxyServiceRule.setEnabled(true);
    multicastDnsProxyServiceRule.setFromVlan(6);
    multicastDnsProxyServiceRule.setToVlan(7);
    multicastDnsProxyServiceProfile.getRules().add(multicastDnsProxyServiceRule);

    Ap ap2 = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp2 = new MulticastDnsProxyServiceProfileAp();
    mDnsAp2.setAp(ap2);

    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(List.of(mDnsAp, mDnsAp2));
    updateMulticastDnsProxyServiceProfile_eda(tenantId, profileId, multicastDnsProxyServiceProfile);

    //Then
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), multicastDnsProxyServiceProfileViaGet.get(0).getRules().size());
    assertEquals(1, multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().stream()
            .filter(e -> e.getAp().getId().equals(ap2.getId())).count());
    assertActivityStatusSuccess(ApiFlowNames.UPDATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());
    // 1 Ap will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .count());

    //When update rule to empty
    multicastDnsProxyServiceProfile.setRules(null);
    updateMulticastDnsProxyServiceProfile_eda(tenantId, profileId, multicastDnsProxyServiceProfile);

    //Then
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertEquals(multicastDnsProxyServiceProfileViaGet.size(), 1);
    assertActivityStatusFail(ApiFlowNames.UPDATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);
  }

  @Test
  public void testUpdateMDNSProfileWhitSelectAp(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");

    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getRules().size(), multicastDnsProxyServiceProfile.getRules().size());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    String profileId = multicastDnsProxyServiceProfileViaGet.get(0).getId();
    Operations viewmodelOperation = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.ADD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());
    assertEquals(0, viewmodelOperation.getDocMap().get(EsConstants.Key.VENUE_IDS).getListValue().getValuesCount());

    List<Operation> ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());

    //When update Ap profile
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));
    updateMulticastDnsProxyServiceProfile_eda(tenantId, profileId, multicastDnsProxyServiceProfile);

    //Then
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), multicastDnsProxyServiceProfileViaGet.get(0).getRules().size());
    assertEquals(1, multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().stream()
            .filter(e -> e.getAp().getId().equals(ap.getId())).count());
    assertActivityStatusSuccess(ApiFlowNames.UPDATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    viewmodelOperation = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.MOD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());
    assertEquals(1, viewmodelOperation.getDocMap().get(EsConstants.Key.VENUE_IDS).getListValue().getValuesCount());

    ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());
    // 1 Ap will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .count());
  }

  @Test
  public void testUpdateRuleInMulticastDnsProxyServiceProfile(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getRules().size(), multicastDnsProxyServiceProfile.getRules().size());
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().get(0).getAp().getId(), ap.getId());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    String profileId = multicastDnsProxyServiceProfileViaGet.get(0).getId();
    Operations viewmodelOperation = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.ADD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());

    List<Operation> ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());
    // 1 Ap will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .count());

    //When update one more Ap profile
    multicastDnsProxyServiceProfile.getRules().clear();
    MulticastDnsProxyServiceRule multicastDnsProxyServiceRule = new MulticastDnsProxyServiceRule();
    multicastDnsProxyServiceRule.setService(BridgeServiceEnum.WWW_HTTP);
    multicastDnsProxyServiceRule.setEnabled(true);
    multicastDnsProxyServiceRule.setFromVlan(4);
    multicastDnsProxyServiceRule.setToVlan(5);
    multicastDnsProxyServiceRule.setRuleIndex(1);
    multicastDnsProxyServiceProfile.getRules().add(multicastDnsProxyServiceRule);

    updateMulticastDnsProxyServiceProfile_eda(tenantId, profileId, multicastDnsProxyServiceProfile);

    //Then
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(1, multicastDnsProxyServiceProfileViaGet.get(0).getRules().size());
    assertEquals(BridgeServiceEnum.WWW_HTTP, multicastDnsProxyServiceProfileViaGet.get(0).getRules().get(0).getService());
    assertActivityStatusSuccess(ApiFlowNames.UPDATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());
  }

  @Test
  public void testDeleteMulticastDnsProxyServiceProfile(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getRules().size(), multicastDnsProxyServiceProfile.getRules().size());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);
    String profileId = multicastDnsProxyServiceProfileViaGet.get(0).getId();
    Operations viewmodelOperation = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.ADD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());

    List<Operation> ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());

    //When delete
    multicastDnsProxyServiceProfile.setId(multicastDnsProxyServiceProfileViaGet.get(0).getId());
    deleteMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);

    //Then
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(0, multicastDnsProxyServiceProfileViaGet.size());
    assertActivityStatusSuccess(ApiFlowNames.DELETE_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    Operations deleteOperations = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> multicastDnsProxyServiceProfile.getId().equals(o.getId()))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.DEL, deleteOperations.getOpType());

    ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == Operation.ConfigCase.VENUEBONJOURGATEWAY)
            .filter(o -> o.getAction() == Action.DELETE)
            .count());

    //when delete AP activated profile
    Ap ap = createAp(tenant, venue);

    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);

    multicastDnsProxyServiceProfile.setId(null);
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.size(), 1);
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    //Then fail due to activation validation
    multicastDnsProxyServiceProfile.setId(multicastDnsProxyServiceProfileViaGet.get(0).getId());
    deleteMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.size(), 1);

    assertActivityStatusFail(ApiFlowNames.DELETE_MULTICAST_DNS_PROXY_SERVICE_PROFILE, Errors.WIFI_10426, tenantId);
  }

  @Test
  public void testDeleteMulticastDnsProxyServiceProfiles(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    createVenue(tenant, "venue-test");

    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile1 = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxySP1");
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile2 = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxySP2");

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile1);
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile2);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfilesViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfilesViaGet);
    assertEquals(2, multicastDnsProxyServiceProfilesViaGet.size());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    long addOpViewmodelCount = receiveViewmodelCollectorOperations(2, tenantId).stream()
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .filter(o -> o.getOpType().equals(OpType.ADD))
            .count();
    assertEquals(2, addOpViewmodelCount);

    List<Operation> ddccmOperations = receiveDdccmOperations(2, tenantId);
    // 2 VenueBonjourGateway will be push
    assertEquals(2, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .filter(b -> b.getAction() == Action.ADD)
            .count());

    //When delete
    List<String> ids = multicastDnsProxyServiceProfilesViaGet.stream().map(AbstractBaseEntity::getId)
            .collect(Collectors.toList());
    deleteMulticastDnsProxyServiceProfiles_eda(tenantId, ids);

    //Then
    multicastDnsProxyServiceProfilesViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfilesViaGet);
    assertEquals(multicastDnsProxyServiceProfilesViaGet.size(), 0);

    assertActivityStatusSuccess(ApiFlowNames.DELETE_MULTICAST_DNS_PROXY_SERVICE_PROFILES, tenantId);

    List<String> viewmodelIds = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .filter(o -> OpType.DEL == o.getOpType())
            .map(Operations::getId)
            .collect(Collectors.toList());
    assertTrue(CollectionUtils.isEqualCollection(ids, viewmodelIds));

    ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 2 VenueBonjourGateway will be push
    assertEquals(2, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .filter(b -> b.getAction() == Action.DELETE)
            .count());

  }

  @Test
  public void testActivateMulticastDnsProxyServiceProfileAps(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();

    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getRules().size(), multicastDnsProxyServiceProfile.getRules().size());
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().get(0).getAp().getId(), ap.getId());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    String profileId = multicastDnsProxyServiceProfileViaGet.get(0).getId();
    Operations viewmodelOperation = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.ADD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());

    List<Operation> ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());
    // 1 Ap will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .count());

    //When activate aps
    Ap ap2 = createAp(tenant, venue);
    Ap ap3 = createAp(tenant, venue);
    activateMulticastDnsProxyServiceProfileAps_eda(tenantId, profileId, Arrays.asList(ap2.getId(), ap3.getId()));

    //Then
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), multicastDnsProxyServiceProfileViaGet.get(0).getRules().size());
    assertEquals(3, multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().size());
    assertActivityStatusSuccess(ApiFlowNames.ACTIVATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE_APS, tenantId);

    ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 2 AP will be push
    assertEquals(2, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .filter(o -> o.getAction() == Action.MODIFY)
            .count());
  }

  @Test
  public void testMoveMulticastDnsProxyServiceProfileAps(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile1 = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    multicastDnsProxyServiceProfile1.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile2 = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile2");

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile1);
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile2);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();

    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(2, multicastDnsProxyServiceProfileViaGet.size());
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().get(0).getAp().getId(), ap.getId());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    String profileId = multicastDnsProxyServiceProfileViaGet.get(0).getId();
    Operations viewmodelOperation = receiveViewmodelCollectorOperations(2, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.ADD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile1.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile1.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());

    List<Operation> ddccmOperations = receiveDdccmOperations(2, tenantId);
    // 2 VenueBonjourGateway will be push
    assertEquals(2, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());
    // 1 Ap will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .count());

    //When activate aps
    String profileId2 = multicastDnsProxyServiceProfileViaGet.get(1).getId();
    activateMulticastDnsProxyServiceProfileAps_eda(tenantId, profileId2, Collections.singletonList(ap.getId()));

    //Then
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(1, multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().size()
            + multicastDnsProxyServiceProfileViaGet.get(1).getMulticastDnsProxyServiceProfileAps().size());
    assertActivityStatusSuccess(ApiFlowNames.ACTIVATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE_APS, tenantId);

    ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 AP will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .filter(o -> o.getAp().getId().equals(ap.getId()))
            .filter(obj -> obj.getAction() == Action.MODIFY)
            .count());
  }

  @Test
  public void testDeactivateMulticastDnsProxyServiceProfileAps(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();

    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getRules().size(), multicastDnsProxyServiceProfile.getRules().size());
    assertEquals(multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().get(0).getAp().getId(), ap.getId());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    String profileId = multicastDnsProxyServiceProfileViaGet.get(0).getId();
    Operations viewmodelOperation = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> o.getId().equals(profileId))
            .filter(o -> o.getIndex().equals(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME))
            .findAny()
            .get();
    assertEquals(OpType.ADD, viewmodelOperation.getOpType());
    assertEquals(multicastDnsProxyServiceProfile.getServiceName(), viewmodelOperation.getDocMap().get(EsConstants.Key.NAME).getStringValue());
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), viewmodelOperation.getDocMap().get(EsConstants.Key.RULES).getListValue().getValuesCount());

    List<Operation> ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 VenueBonjourGateway will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.VENUEBONJOURGATEWAY)
            .count());
    // 1 Ap will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .count());

    //When deactivate aps
    deactivateMulticastDnsProxyServiceProfileAps_eda(tenantId, profileId, Collections.singletonList(ap.getId()));

    //Then
    multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(multicastDnsProxyServiceProfile.getRules().size(), multicastDnsProxyServiceProfileViaGet.get(0).getRules().size());
    assertEquals(0, multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().size());
    assertActivityStatusSuccess(ApiFlowNames.DEACTIVATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE_APS, tenantId);

    ddccmOperations = receiveDdccmOperations(1, tenantId);
    // 1 AP will be push
    assertEquals(1, ddccmOperations.stream()
            .filter(o -> o.getConfigCase() == ConfigCase.AP)
            .filter(obj -> obj.getAction() == Action.MODIFY)
            .count());
  }

  @Test
  public void testActivateMulticastDnsProxyServiceProfileApWithOnlyService(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile1 = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    multicastDnsProxyServiceProfile1.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile2 = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile2");
    multicastDnsProxyServiceProfile2.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile1);
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile2);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfilesViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfilesViaGet);
    assertEquals(2, multicastDnsProxyServiceProfilesViaGet.size());
    assertEquals(multicastDnsProxyServiceProfile1.getRules().size(), multicastDnsProxyServiceProfilesViaGet.get(0).getRules().size());
    assertEquals(multicastDnsProxyServiceProfile1.getRules().size(), multicastDnsProxyServiceProfilesViaGet.get(1).getRules().size());
    assertEquals(1, multicastDnsProxyServiceProfilesViaGet.get(0).getMulticastDnsProxyServiceProfileAps().size()
            + multicastDnsProxyServiceProfilesViaGet.get(1).getMulticastDnsProxyServiceProfileAps().size());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);
  }

  @Test
  public void testAddMulticastDnsProxyServcieProfileWithUsedAp(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile1 = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    multicastDnsProxyServiceProfile1.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile2 = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile2");
    multicastDnsProxyServiceProfile2.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));

    //When add
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile1);
    addMulticastDnsProxyServiceProfile_eda(tenantId, multicastDnsProxyServiceProfile2);

    //Then
    List<MulticastDnsProxyServiceProfile> multicastDnsProxyServiceProfileViaGet = getMulticastDnsProxyServiceProfiles();
    assertNotNull(multicastDnsProxyServiceProfileViaGet);
    assertEquals(2, multicastDnsProxyServiceProfileViaGet.size());
    assertEquals(1, multicastDnsProxyServiceProfileViaGet.get(0).getMulticastDnsProxyServiceProfileAps().size()
            + multicastDnsProxyServiceProfileViaGet.get(1).getMulticastDnsProxyServiceProfileAps().size());
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);
    assertActivityStatusSuccess(ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);
  }

  @SneakyThrows
  public MulticastDnsProxyServiceProfile getMulticastDnsProxyServiceProfile(String mDnsProxyId) {
    return map(mDnsProxyServiceProfileServiceCtrl.getMulticastDnsProxyServiceProfile(mDnsProxyId));
  }

  @SneakyThrows
  public List<MulticastDnsProxyServiceProfile> getMulticastDnsProxyServiceProfiles() {
    return mDnsProxyServiceProfileServiceCtrl.getMulticastDnsProxyServiceProfiles(Optional.empty())
            .stream().map(this::map).collect(Collectors.toList());
  }

  public MulticastDnsProxyServiceProfile map(MulticastDnsProxyServiceProfile mDnsProxyProfile) {
    return MulticastDnsProxyServiceProfileRestCtrl.MulticastDnsProxyServiceProfileMapper.INSTANCE.MulticastDnsProxyServiceProfileRequest2ServiceMulticastDnsProxyServiceProfile(
            MulticastDnsProxyServiceProfileRestCtrl.MulticastDnsProxyServiceProfileMapper.INSTANCE.ServiceMulticastDnsProxyServiceProfile2MulticastDnsProxyServiceProfileRequest(mDnsProxyProfile));
  }

  public MulticastDnsProxyServiceProfileRequest mapViewModel(MulticastDnsProxyServiceProfile mDnsProxyProfile) {
    return MulticastDnsProxyServiceProfileRestCtrl.MulticastDnsProxyServiceProfileMapper.INSTANCE.ServiceMulticastDnsProxyServiceProfile2MulticastDnsProxyServiceProfileRequest(mDnsProxyProfile);
  }

  //// mDns Proxy service profile
  private String addMulticastDnsProxyServiceProfile_eda(String tenantId
          , MulticastDnsProxyServiceProfile mDnsProxyServiceProfile) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE;

    sendWifiCfgRequest(tenantId, requestId, action, userName, mapViewModel(mDnsProxyServiceProfile));

    return requestId;
  }

  private String updateMulticastDnsProxyServiceProfile_eda(String tenantId, String mDnsProxyProfileId
          , MulticastDnsProxyServiceProfile mDnsProxyServiceProfile) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.UPDATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE;
    RequestParams params = new RequestParams().addPathVariable("mDnsProxyProfileId", mDnsProxyProfileId);

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, mapViewModel(mDnsProxyServiceProfile));

    return requestId;
  }

  private String deleteMulticastDnsProxyServiceProfile_eda(String tenantId
          , MulticastDnsProxyServiceProfile mDnsProxyServiceProfile) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.DELETE_MULTICAST_DNS_PROXY_SERVICE_PROFILE;
    RequestParams params = new RequestParams().addPathVariable("mDnsProxyProfileId", mDnsProxyServiceProfile.getId());

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, mapViewModel(mDnsProxyServiceProfile));
    return requestId;
  }

  private String deleteMulticastDnsProxyServiceProfiles_eda(String tenantId, List<String> mDnsProxyProfileIds) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.DELETE_MULTICAST_DNS_PROXY_SERVICE_PROFILES;

    sendWifiCfgRequest(tenantId, requestId, action, userName, mDnsProxyProfileIds);
    return requestId;
  }

  private String activateMulticastDnsProxyServiceProfileAps_eda(String tenantId, String mDnsProxyProfileId, List<String> serialNumbers) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.ACTIVATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE_APS;
    RequestParams params = new RequestParams().addPathVariable("mDnsProxyProfileId", mDnsProxyProfileId);

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, serialNumbers);

    return requestId;
  }

  private String deactivateMulticastDnsProxyServiceProfileAps_eda(String tenantId, String mDnsProxyProfileId, List<String> serialNumbers) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.DEACTIVATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE_APS;
    RequestParams params = new RequestParams().addPathVariable("mDnsProxyProfileId", mDnsProxyProfileId);

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, serialNumbers);

    return requestId;
  }
}
