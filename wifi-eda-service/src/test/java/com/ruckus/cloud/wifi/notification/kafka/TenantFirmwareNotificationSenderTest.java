package com.ruckus.cloud.wifi.notification.kafka;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.kafka.context.TenantFirmwareNotificationContext;
import com.ruckus.cloud.wifi.kafka.publisher.TenantFirmwareNotificationPublisher;
import com.ruckus.cloud.wifi.notification.NotificationUpdateFirmwareHandler;
import com.ruckus.cloud.wifi.notification.TenantFirmwareNotificationUtilTest;
import com.ruckus.cloud.wifi.notification.VenueUpgradeVersionsMapping;
import com.ruckus.cloud.wifi.notification.dto.VenueTemplateDto;
import com.ruckus.cloud.wifi.notification.template.VenueTemplateStatusEnum;
import com.ruckus.cloud.wifi.proto.TenantFirmwareNotification;
import com.ruckus.cloud.wifi.proto.TenantFirmwareNotification.CancelSchedule;
import com.ruckus.cloud.wifi.proto.TenantFirmwareNotification.ChangeSchedule;
import com.ruckus.cloud.wifi.proto.TenantFirmwareNotification.CreateSchedule;
import com.ruckus.cloud.wifi.proto.TenantFirmwareNotification.RemindRelease;
import com.ruckus.cloud.wifi.proto.TenantFirmwareNotification.RemindSchedule;
import com.ruckus.cloud.wifi.proto.TenantFirmwareNotification.UpdateFinished;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.TenantService;
import com.ruckus.cloud.wifi.service.entity.TenantFirmwareReleaseType;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class TenantFirmwareNotificationSenderTest {

  @SpyBean
  private TenantFirmwareNotificationSender notificationSender;

  @MockBean
  private TenantFirmwareNotificationPublisher notificationPublisher;

  @SpyBean
  private TenantFirmwareNotificationReceiver notificationReceiver;

  @MockBean
  private NotificationUpdateFirmwareHandler notificationUpdateFirmwareHandler;

  @MockBean
  private TenantService tenantService;

  @MockBean
  private ExtendedVenueServiceCtrl venueServiceCtrl;

  private TenantFirmwareNotificationUtilTest notificationUtil;

  private ArgumentCaptor<TenantFirmwareNotification> messageCaptor =
      ArgumentCaptor.forClass(TenantFirmwareNotification.class);
  private ArgumentCaptor<Tenant> tenantCaptor = ArgumentCaptor.forClass(Tenant.class);
  private ArgumentCaptor<List<Venue>> venuesCaptor = ArgumentCaptor.forClass(List.class);
  private ArgumentCaptor<VenueUpgradeVersionsMapping> versionMappingCaptor =
      ArgumentCaptor.forClass(VenueUpgradeVersionsMapping.class);
  private ArgumentCaptor<Map<String, Integer>> apCountMappingCaptor =
      ArgumentCaptor.forClass(Map.class);
  private ArgumentCaptor<String> requestIdCaptor = ArgumentCaptor.forClass(String.class);
  private ArgumentCaptor<ApVersion> apVersionCaptor = ArgumentCaptor.forClass(ApVersion.class);
  private ArgumentCaptor<List<ApVersion>> apVersionsCaptor = ArgumentCaptor.forClass(List.class);
  private ArgumentCaptor<Map<String, List<VenueTemplateDto>>> dtosMappingCaptor =
      ArgumentCaptor.forClass(Map.class);
  private ArgumentCaptor<List<VenueTemplateDto>> dtosCaptor = ArgumentCaptor.forClass(List.class);
  private ArgumentCaptor<Boolean> successCaptor = ArgumentCaptor.forClass(Boolean.class);
  private ArgumentCaptor<TenantFirmwareReleaseType> releaseTypeCaptor =
      ArgumentCaptor.forClass(TenantFirmwareReleaseType.class);
  private ArgumentCaptor<String> slotDateCaptor = ArgumentCaptor.forClass(String.class);
  private ArgumentCaptor<Set<String>> timezonesCaptor = ArgumentCaptor.forClass(Set.class);

  @Test
  public void testSendCreateScheduleNotification() {
    Tenant tenant = new Tenant("tenant-id");
    List<Venue> venues = notificationUtil.mockVenues(5);
    ApVersion apVersion = notificationUtil.mockApVersion("*********.1");
    VenueUpgradeVersionsMapping mapping = notificationUtil.mockVenueUpgradeVersionMappingByVenueIds(
        List.of("venue-id1", "venue-id2"), List.of(apVersion));
    String requestId = "request-id";
    when(tenantService.getById(any())).thenReturn(tenant);
    when(venueServiceCtrl.findVenuesByVenueIds(any())).thenReturn(venues);

    // use sender to convert the notification message
    notificationSender.sendCreateScheduleNotifications(tenant,
        venues, mapping, requestId);

    verify(notificationPublisher).publish(messageCaptor.capture(), any(TenantFirmwareNotificationContext.class));
    CreateSchedule notification = messageCaptor.getValue().getCreateSchedule();

    // use receiver to verify the notification message
    notificationReceiver.processCreateScheduleNotification(notification, tenant.getId(), requestId);
    verify(notificationUpdateFirmwareHandler).sendCreateScheduleNotifications(
        tenantCaptor.capture(), venuesCaptor.capture(), versionMappingCaptor.capture(),
        requestIdCaptor.capture());

    Assertions.assertThat(tenantCaptor.getValue()).isEqualTo(tenant);
    Assertions.assertThat(venuesCaptor.getValue()).isEqualTo(venues);
    Assertions.assertThat(versionMappingCaptor.getValue().getMapping().get("venue-id1"))
        .hasSize(1)
        .extracting(ap -> ap.getName())
            .containsExactly("*********.1");
    Assertions.assertThat(versionMappingCaptor.getValue().getUnionVersions())
        .hasSize(1)
        .extracting(ap -> ap.getName())
        .containsExactly("*********.1");
    Assertions.assertThat(requestIdCaptor.getValue()).isEqualTo(requestId);
  }

  @Test
  public void testSendChangeScheduleNotification() {
    Tenant tenant = new Tenant("tenant-id");
    ApVersion apVersion = notificationUtil.mockApVersion("*********.1");
    Map<String, List<VenueTemplateDto>> dtos = notificationUtil.mockVenueTemplateMap(5);
    String requestId = "request-id";
    when(tenantService.getById(any())).thenReturn(tenant);

    // use sender to convert the notification message
    notificationSender.sendChangeScheduleNotifications(
        tenant, apVersion, dtos, requestId);

    verify(notificationPublisher).publish(messageCaptor.capture(), any(TenantFirmwareNotificationContext.class));
    ChangeSchedule notification = messageCaptor.getValue().getChangeSchedule();

    // use receiver to verify the notification message
    notificationReceiver.processChangeScheduleNotification(notification, tenant.getId(), requestId);
    verify(notificationUpdateFirmwareHandler).sendChangeScheduleNotifications(
        tenantCaptor.capture(), apVersionCaptor.capture(), dtosMappingCaptor.capture(),
        requestIdCaptor.capture());

    Assertions.assertThat(tenantCaptor.getValue()).isEqualTo(tenant);
    Assertions.assertThat(apVersionCaptor.getValue().getId()).isEqualTo(apVersion.getId());
    Assertions.assertThat(dtosMappingCaptor.getValue()).hasSize(5)
        .isEqualTo(dtos);
    Assertions.assertThat(requestIdCaptor.getValue()).isEqualTo(requestId);
  }

  @Test
  public void testSendCancelScheduleNotification() {
    Tenant tenant = new Tenant("tenant-id");
    List<Venue> venues = notificationUtil.mockVenues(5);
    ApVersion apVersion = notificationUtil.mockApVersion("*********.1");
    VenueUpgradeVersionsMapping mapping = notificationUtil.mockVenueUpgradeVersionMapping(5,
        apVersion);
    String requestId = "request-id";
    when(tenantService.getById(any())).thenReturn(tenant);
    when(venueServiceCtrl.findVenuesByVenueIds(any())).thenReturn(venues);

    // use sender to convert the notification message
    notificationSender.sendCancelScheduleNotifications(tenant,
        venues, mapping, requestId);

    verify(notificationPublisher).publish(messageCaptor.capture(), any(TenantFirmwareNotificationContext.class));
    CancelSchedule notification = messageCaptor.getValue().getCancelSchedule();

    // use receiver to verify the notification message
    notificationReceiver.processCancelScheduleNotification(notification, tenant.getId(), requestId);
    verify(notificationUpdateFirmwareHandler).sendCancelScheduleNotifications(
        tenantCaptor.capture(), venuesCaptor.capture(), versionMappingCaptor.capture(),
        apCountMappingCaptor.capture(), requestIdCaptor.capture());

    Assertions.assertThat(tenantCaptor.getValue()).isEqualTo(tenant);
    Assertions.assertThat(venuesCaptor.getValue()).isEqualTo(venues);
    Assertions.assertThat(versionMappingCaptor.getValue().getMapping()).hasSize(5);
    Assertions.assertThat(requestIdCaptor.getValue()).isEqualTo(requestId);
  }

  @Test
  public void testSendUpdateFinishedNotification() {
    Tenant tenant = new Tenant("tenant-id");
    List<ApVersion> apVersions = notificationUtil.mockApVersions("*********.2");
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(8);
    dtos.forEach(dto -> dto.setFailedNumberOfAps(3));
    boolean success = true;
    String requestId = "request-id";
    when(tenantService.getById(any())).thenReturn(tenant);

    // use sender to convert the notification message
    notificationSender.sendUpdateFinishNotifications(tenant, apVersions,
        dtos, success, requestId);

    verify(notificationPublisher).publish(messageCaptor.capture(), any(TenantFirmwareNotificationContext.class));
    UpdateFinished notification = messageCaptor.getValue().getUpdateFinished();

    // use receiver to verify the notification message
    notificationReceiver.processUpdateFinishedNotification(notification, tenant.getId(), requestId);
    verify(notificationUpdateFirmwareHandler).sendUpdateFinishNotifications(
        tenantCaptor.capture(), apVersionsCaptor.capture(), dtosCaptor.capture(),
        successCaptor.capture(), requestIdCaptor.capture());

    Assertions.assertThat(tenantCaptor.getValue()).isEqualTo(tenant);
    Assertions.assertThat(apVersionsCaptor.getValue()).hasSize(1);
    Assertions.assertThat(dtosCaptor.getValue()).hasSize(8);
    Assertions.assertThat(dtosCaptor.getValue()).allMatch(dto -> dto.getFailedNumberOfAps() == 3);
    Assertions.assertThat(successCaptor.getValue()).isTrue();
    Assertions.assertThat(requestIdCaptor.getValue()).isEqualTo(requestId);
  }

  @Test
  public void testReminderScheduleNotification() {
    Tenant tenant = new Tenant("tenant-id");
    List<ApVersion> apVersions = notificationUtil.mockApVersions("*********.3");
    String slotDate = "slot-date";
    List<Venue> venues = notificationUtil.mockVenues(3);
    String requestId = "request-id";
    Set<String> timezones = notificationUtil.mockTimezones(2);
    when(tenantService.getById(any())).thenReturn(tenant);
    when(venueServiceCtrl.findVenuesByVenueIds(any())).thenReturn(venues);

    // use sender to convert the notification message
    notificationSender.sendReminderScheduleNotifications(tenant, apVersions,
        slotDate, venues, requestId, timezones);

    verify(notificationPublisher).publish(messageCaptor.capture(), any(TenantFirmwareNotificationContext.class));
    RemindSchedule notification = messageCaptor.getValue().getRemindSchedule();

    // use receiver to verify the notification message
    notificationReceiver.processReminderScheduleNotification(notification, tenant.getId(), requestId);
    verify(notificationUpdateFirmwareHandler).sendReminderScheduleNotifications(
        tenantCaptor.capture(), apVersionsCaptor.capture(), slotDateCaptor.capture(),
        venuesCaptor.capture(), requestIdCaptor.capture(), timezonesCaptor.capture());

    Assertions.assertThat(tenantCaptor.getValue()).isEqualTo(tenant);
    Assertions.assertThat(apVersionsCaptor.getValue()).hasSize(1);
    Assertions.assertThat(slotDateCaptor.getValue()).isEqualTo(slotDate);
    Assertions.assertThat(venuesCaptor.getValue()).isEqualTo(venues);
    Assertions.assertThat(requestIdCaptor.getValue()).isEqualTo(requestId);
    Assertions.assertThat(timezonesCaptor.getValue()).hasSize(2);
  }

  @Test
  public void testSendRemindReleaseNotification() throws Exception {
    Tenant tenant = new Tenant("tenant-id");
    ApVersion apVersion = notificationUtil.mockApVersion("*********.4");
    TenantFirmwareReleaseType releaseType = TenantFirmwareReleaseType.GA;
    String requestId = "request-id";
    when(tenantService.getById(any())).thenReturn(tenant);

    // use sender to convert the notification message
    notificationSender.sendRemindRelease(tenant.getId(), apVersion, releaseType,
        requestId);

    verify(notificationPublisher).publish(messageCaptor.capture(), any(TenantFirmwareNotificationContext.class));
    RemindRelease notification = messageCaptor.getValue().getRemindRelease();

    // use receiver to verify the notification message
    notificationReceiver.processReminderReleaseNotification(notification, tenant.getId(), requestId);
    verify(notificationUpdateFirmwareHandler).sendRemindReleaseNotification(
        tenantCaptor.capture(), apVersionCaptor.capture(), releaseTypeCaptor.capture(),
        requestIdCaptor.capture());

    Assertions.assertThat(tenantCaptor.getValue()).isEqualTo(tenant);
    Assertions.assertThat(apVersionCaptor.getValue().getId()).isEqualTo(apVersion.getId());
    Assertions.assertThat(releaseTypeCaptor.getValue()).isEqualTo(releaseType);
    Assertions.assertThat(requestIdCaptor.getValue()).isEqualTo(requestId);
  }

  @Test
  public void testSendChangeScheduleNotificationWithPartialInfo() {
    Tenant tenant = new Tenant("tenant-id");
    ApVersion apVersion = notificationUtil.mockApVersion("*********.1");
    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setId("id");
    dto.setName("venue-name");
    dto.setAddress("address");
    dto.setNumberOfAps(1);
    dto.setImpactedNumberOfAps(1);
    dto.setTimeZone("timezone");
    dto.setStatus(VenueTemplateStatusEnum.SUCCESS);
    Map<String, List<VenueTemplateDto>> dtos = new HashMap<>();
    dtos.put("venue-id", List.of(dto));
    String requestId = "request-id";
    when(tenantService.getById(any())).thenReturn(tenant);

    // use sender to convert the notification message
    notificationSender.sendChangeScheduleNotifications(
        tenant, apVersion, dtos, requestId);

    verify(notificationPublisher).publish(messageCaptor.capture(), any(TenantFirmwareNotificationContext.class));
    ChangeSchedule notification = messageCaptor.getValue().getChangeSchedule();

    // use receiver to verify the notification message
    notificationReceiver.processChangeScheduleNotification(notification, tenant.getId(), requestId);
    verify(notificationUpdateFirmwareHandler).sendChangeScheduleNotifications(
        any(Tenant.class), any(ApVersion.class), dtosMappingCaptor.capture(),
        any(String.class));

    Assertions.assertThat(dtosMappingCaptor.getValue()).hasSize(1);
    Assertions.assertThat(dtosMappingCaptor.getValue().get("venue-id"))
        .allMatch(e ->
                e.getId().equals("id") &&
                e.getName().equals("venue-name") &&
                e.getAddress().equals("address") &&
                e.getNumberOfAps() == 1 &&
                e.getImpactedNumberOfAps() == 1 &&
                e.getTimeZone() == "timezone" &&
                e.getStatus() == VenueTemplateStatusEnum.SUCCESS
            );
  }
}