package com.ruckus.cloud.wifi.service.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.ruckus.cloud.wifi.core.autoconfig.HibernateAutoConfiguration;
import com.ruckus.cloud.wifi.repository.EntityIdGenerationLockIndexRepository;
import com.ruckus.cloud.wifi.repository.EntityIdGenerationLockRepository;
import com.ruckus.cloud.wifi.service.EntityIdGenerationLockService;
import com.ruckus.cloud.wifi.service.EntityIdGenerationLockService.BatchEntityLockEnum;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.impl.EntityIdGenerationLockServiceImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.retry.annotation.EnableRetry;

/**
 * Created by Hank Hao on 2022-01-03
 */
@WifiJpaDataTest
public class EntityIdGenerationLockServiceTest {

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @MockBean
  private EntityIdGenerationLockRepository repository;

  @MockBean
  private EntityIdGenerationLockIndexRepository indexRepository;

  @Autowired
  private EntityIdGenerationLockService entityIdGenerationLockService;

  @Test
  public void testGenerateAvailabilities() throws CommonException {
    when(repository.getAvailableApWlanIdInNetworkVenue(anyString(), anyInt(), anyInt(), anyInt()))
        .thenReturn(Lists.newArrayList(1));
    when(indexRepository.getAvailableByIndex(anyString(), anyString(), anyString()))
        .thenReturn(Optional.empty());
    List<Integer> values = entityIdGenerationLockService.generateAvailabilities(
        BatchEntityLockEnum.NETWORK_VENUE_AP_WLAN_ID, 1);
    assertEquals(1, values.size());
    assertEquals(1, values.get(0));
  }

  @Test
  public void testGenerateAvailabilitiesWithNoIdAvailable() {
    when(repository.getAvailableApWlanIdInNetworkVenue(anyString(), anyInt(), anyInt(), anyInt()))
        .thenReturn(Lists.newArrayList());
    assertThrows(CommonException.class,
        () -> entityIdGenerationLockService.generateAvailabilities(BatchEntityLockEnum.NETWORK_VENUE_AP_WLAN_ID, 1));
  }

  @TestConfiguration
  @EnableRetry
  @Import(HibernateAutoConfiguration.class)
  static class TestConfig {

    @Bean
    public EntityIdGenerationLockService entityIdGenerationLockService(
        EntityIdGenerationLockRepository entityLockRepository,
        EntityIdGenerationLockIndexRepository entityLockIndexRepository) {
      return new EntityIdGenerationLockServiceImpl(entityLockRepository, entityLockIndexRepository);
    }
  }
}
