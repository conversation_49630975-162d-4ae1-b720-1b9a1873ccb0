package com.ruckus.cloud.wifi.integration.template;

import com.google.common.collect.Maps;
import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfile;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateEnforcementSettings;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.ActivityCfgChangeMessageCaptor;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.ExecutionException;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.REQUEST_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_ADMIN_NAME;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_DELEGATION_SOURCE_TENANT_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_TENANT_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wifiCallingServiceProfile;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@WifiIntegrationTest
@Slf4j
public class ConsumeUpdateWifiCallingServiceProfileTemplateEnforcementSettingsRequestTest extends
    AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  private final String ERROR_MSG = "This WifiCallingServiceProfile is enforced by its Template, and this action [%s] is not allowed";

  private final String INSTANCE_PATH_ID = "wifiCallingServiceProfileId";
  private final String TEMPLATE_PATH_ID = "wifiCallingServiceProfileId";

  private final CfgAction enforcementSettingsUpdateAction = CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ENFORCEMENT_SETTINGS;
  private final String enforcementSettingsUpdateApiFlowName = ApiFlowNames.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ENFORCEMENT_SETTINGS;

  private final CfgAction instanceUpdateAction = CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE;
  private final String instanceUpdateApiFlowName = ApiFlowNames.UPDATE_WIFI_CALLING_SERVICE_PROFILE;

  private final CfgAction templateUpdateAction = CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE;
  private final String templateUpdateApiFlowName = ApiFlowNames.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE;

  private final CfgAction instanceDeleteAction = CfgAction.DELETE_WIFI_CALLING_SERVICE_PROFILE;
  private final String instanceDeleteApiFlowName = ApiFlowNames.DELETE_WIFI_CALLING_SERVICE_PROFILE;

  private final CfgAction templateDeleteAction = CfgAction.DELETE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE;
  private final String templateDeleteApiFlowName = ApiFlowNames.DELETE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE;


  @Nested
  class whenConsumeUpdateTemplateEnforcementSettingsRequest {
    private String mspTenantId;
    private String ecTenantId;
    private final String templateId = "template-1";
    private final String instanceId = "instance-1";

    @BeforeEach
    void givenTemplateAndNetworkInstanceInDb(final Tenant tenant) {
      mspTenantId = tenant.getId();

      Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
      ecTenantId = ecTenant.getId();
      repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());

      // Given
      WifiCallingServiceProfile template = wifiCallingServiceProfile()
          .generate();
      template.setId(templateId);
      template.setIsTemplate(true);
      template.setEPDGs(List.of(Generators.epdg().generate()));
      repositoryUtil.createOrUpdate(template, mspTenantId, randomTxId());

      WifiCallingServiceProfile instance = wifiCallingServiceProfile()
          .generate();
      instance.setId(instanceId);
      instance.setIsTemplate(false);
      instance.setTemplateId(templateId);
      instance.setEPDGs(List.of(Generators.epdg().generate()));
      repositoryUtil.createOrUpdate(instance, ecTenantId, randomTxId());
    }

    private RequestParams requestParams() {
      return new RequestParams().addPathVariable(TEMPLATE_PATH_ID, templateId);
    }

    private TemplateEnforcementSettings templateEnforcementSettings() {
      TemplateEnforcementSettings templateEnforcementSettings = new TemplateEnforcementSettings();
      templateEnforcementSettings.setIsEnforced(true);
      return templateEnforcementSettings;
    }

    @Test
    public void shouldConsumeUpdateTemplateEnforcementSettingsRequest(Tenant tenant)
            throws InvalidProtocolBufferException {
      var tenantId = tenant.getId();
      var requestId = randomTxId();
      var userName = randomName();
      var wifiCfgRequest = WifiCfgRequest.builder()
              .tenantId(tenantId)
              .requestId(requestId)
              .apiAction(enforcementSettingsUpdateAction)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), tenantId)
              .addHeader(REQUEST_ID.getName(), requestId)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable(TEMPLATE_PATH_ID, templateId))
              .tenantId(mspTenantId)
              .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), mspTenantId)
              .payload(templateEnforcementSettings()).build();
      messageUtil.sendWifiCfgRequest(wifiCfgRequest);

      validateActivityMessages(mspTenantId, requestId,
              enforcementSettingsUpdateApiFlowName + "InWifi", Status.OK);
      validateWifiCfgChangeMessage(mspTenantId, requestId);
      validateCmnCfgCollectorMessage(mspTenantId, requestId);
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenantId);
      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      WifiCallingServiceProfile foundTemplate = repositoryUtil.find(WifiCallingServiceProfile.class, templateId);
      assertTrue(foundTemplate.getIsEnforced());
      WifiCallingServiceProfile foundInstance = repositoryUtil.find(WifiCallingServiceProfile.class, instanceId);
      assertEquals(foundTemplate.getId(), foundInstance.getTemplateId());
      assertTrue(foundInstance.getIsEnforced());
    }

    private void validateWifiCfgChangeMessage(String tenantId, String requestId) {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(requestId);
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();

      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(requestId));

      assertThat(wifiConfigChange.getOperationList())
          .hasSize(2)
          .filteredOn(o -> o.hasWifiCallingServiceProfile() )
          .hasSize(2)
          .extracting(o -> o.getWifiCallingServiceProfile().getId().getValue())
          .containsExactlyInAnyOrder(templateId, instanceId);
    }

    private void validateCmnCfgCollectorMessage(String tenantId, String requestId) {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(requestId));

      assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
          .hasSize(2)
          .filteredOn(o -> o.getId().equals(templateId)).first()
          .matches(o -> EsConstants.Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .matches(o -> o.getOpType().equals(OpType.MOD))
          .matches(o -> o.getDocMap().get(Key.TENANT_ID).getStringValue().equals(mspTenantId))
          .matches(o -> Boolean.TRUE.equals(o.getDocMap().get(Key.IS_TEMPLATE).getBoolValue()))
          .matches(o -> Boolean.TRUE.equals(o.getDocMap().get(Key.IS_ENFORCED).getBoolValue()));

      assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
          .hasSize(2)
          .filteredOn(o -> o.getId().equals(instanceId)).first()
          .matches(o -> o.getIndex().equals(EsConstants.Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME))
          .matches(o -> o.getOpType().equals(OpType.MOD))
          .matches(o -> o.getDocMap().get(Key.TENANT_ID).getStringValue().equals(ecTenantId))
          .matches(o -> Boolean.FALSE.equals(o.getDocMap().get(Key.IS_TEMPLATE).getBoolValue()))
          .matches(o -> Boolean.TRUE.equals(o.getDocMap().get(Key.IS_ENFORCED).getBoolValue()));
    }
  }

  public void validateActivityMessages(String tenantId, String requestId, String expectedStep, Status expectedStatus) throws InvalidProtocolBufferException {
    ActivityCfgChangeMessageCaptor captor = messageCaptors.getActivityCfgChangeMessageCaptor();
    final var activityCfgChangeRespMessage = captor
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
            .isNotNull()
            .extracting(KafkaProtoMessage::getHeaders)
            .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
            .isNotNull()
            .extracting(Header::value)
            .extracting(String::new)
            .isEqualTo(tenantId);
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(p -> p.getStatus().equals(expectedStatus))
            .matches(p -> p.getStep().equals(expectedStep))
            .extracting(ConfigurationStatus::getEventDate)
            .isNotNull();
  }

  @Nested
  class ConsumeUpdateRequestWhichIsEnforced {
    private String mspTenantId;
    private String ecTenantId;
    private final String templateId = "template-1";
    private final String instanceId = "instance-1";

    @BeforeEach
    void givenTemplateAndInstanceInDb(final Tenant tenant) {
      mspTenantId = tenant.getId();

      Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
      ecTenantId = ecTenant.getId();
      repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());

      // Given
      WifiCallingServiceProfile template = Generators.wifiCallingServiceProfile()
              .generate();
      template.setId(templateId);
      template.setIsTemplate(true);
      template.setIsEnforced(true);
      template.setEPDGs(List.of(Generators.epdg().generate()));
      repositoryUtil.createOrUpdate(template, mspTenantId, randomTxId());

      WifiCallingServiceProfile instance = Generators.wifiCallingServiceProfile()
              .generate();
      instance.setId(instanceId);
      instance.setIsTemplate(false);
      instance.setTemplateId(templateId);
      instance.setIsEnforced(true);
      instance.setEPDGs(List.of(Generators.epdg().generate()));
      repositoryUtil.createOrUpdate(instance, ecTenantId, randomTxId());
    }

    @Test
    void updateCase() throws InvalidProtocolBufferException, ExecutionException, InterruptedException {
      var savedInstance = repositoryUtil.find(WifiCallingServiceProfile.class, instanceId);
      assertNotNull(savedInstance);
      assertEquals(templateId, savedInstance.getTemplateId());

      messageUtil.clearMessage();

      // FROM_EC_DIRECTLY
      // update Instance
      var profile = Generators.wifiCallingServiceProfile()
              .generate();
      profile.setId(instanceId);
      profile.setIsTemplate(false);
      profile.setEPDGs(List.of(Generators.epdg().generate()));
      var requestId = randomTxId();
      log.warn("[1] [{}] ----------", requestId);
      var userName = randomName();
      var cfgRequest1 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId)
              .apiAction(instanceUpdateAction)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(REQUEST_ID.getName(), requestId)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable(INSTANCE_PATH_ID, instanceId))
              .payload(profile).build();
      // should fail to update instance when it's enforced
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(cfgRequest1)).isNotNull()
              .getRootCause().isInstanceOf(CommonException.class);
      validateActivityMessages(ecTenantId, requestId, instanceUpdateApiFlowName, Status.FAIL);
      messageUtil.clearMessage();

      // FROM_TEMPLATE_ORIGIN
      // update Template
      var requestId2 = randomTxId();
      profile.setIsTemplate(true);
      profile.setEPDGs(List.of(Generators.epdg().generate()));
      log.warn("[2] [{}] ----------", requestId2);
      var cfgRequest2 = WifiCfgRequest.builder()
              .tenantId(mspTenantId)
              .requestId(requestId2)
              .apiAction(templateUpdateAction)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), mspTenantId)
              .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), mspTenantId)
              .addHeader(REQUEST_ID.getName(), requestId2)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable(TEMPLATE_PATH_ID, templateId))
              .payload(profile).build();
      // should be able to update template since it's from MSP tenant
      messageUtil.sendWifiCfgRequest(cfgRequest2);
      validateActivityMessages(mspTenantId, requestId2, templateUpdateApiFlowName, Status.OK);
      messageUtil.clearMessage();
    }
  }

  @Nested
  class ConsumeDeleteRequestWhichIsEnforced {

    private String mspTenantId;
    private String ecTenantId;
    private final String templateId = "template-1";
    private final String instanceId = "instance-1";

    @BeforeEach
    void givenTemplateAndInstanceInDb(final Tenant tenant) {
      mspTenantId = tenant.getId();

      Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
      ecTenantId = ecTenant.getId();
      repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());

      // Given
      WifiCallingServiceProfile template = Generators.wifiCallingServiceProfile()
              .generate();

      template.setId(templateId);
      template.setIsTemplate(true);
      template.setIsEnforced(true);
      repositoryUtil.createOrUpdate(template, mspTenantId, randomTxId());

      WifiCallingServiceProfile instance = Generators.wifiCallingServiceProfile()
              .generate();
      instance.setId(instanceId);
      instance.setIsTemplate(false);
      instance.setTemplateId(templateId);
      instance.setIsEnforced(true);
      repositoryUtil.createOrUpdate(instance, ecTenantId, randomTxId());
    }

    @Test
    void deleteCase() throws InvalidProtocolBufferException, ExecutionException, InterruptedException {
      //////////
      // check instance isEnforced
      var savedInstance = repositoryUtil.find(WifiCallingServiceProfile.class, instanceId);
      assertNotNull(savedInstance);
      assertTrue(savedInstance.getIsEnforced());

      // FROM_EC_DIRECTLY
      // Delete Instance
      var requestId = randomTxId();
      var userName = randomName();
      var cfgRequest1 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId)
              .apiAction(instanceDeleteAction)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(REQUEST_ID.getName(), requestId)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable(INSTANCE_PATH_ID, instanceId))
              .payload("").build();
      // should fail to delete instance when it's enforced
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(cfgRequest1)).isNotNull()
              .getRootCause().isInstanceOf(CommonException.class)
              .hasMessage(String.format(ERROR_MSG, EntityAction.DELETE));
      validateActivityMessages(ecTenantId, requestId, instanceDeleteApiFlowName, Status.FAIL);
      messageUtil.clearMessage();

      // FROM_TEMPLATE_ORIGIN
      // Delete Template
      var requestId2 = randomTxId();
      log.warn("[2] [{}] ----------", requestId2);
      var cfgRequest2 = WifiCfgRequest.builder()
              .tenantId(mspTenantId)
              .requestId(requestId2)
              .apiAction(templateDeleteAction)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), mspTenantId)
              .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), mspTenantId)
              .addHeader(REQUEST_ID.getName(), requestId2)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable(TEMPLATE_PATH_ID, templateId))
              .payload("").build();
      // should be able to delete template since it's from MSP tenant
      messageUtil.sendWifiCfgRequest(cfgRequest2);
      validateActivityMessages(mspTenantId, requestId2, templateDeleteApiFlowName, Status.OK);
      messageUtil.clearMessage();
    }
  }
}
