package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.CompatibilityActivity;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.time.Instant;
import java.util.Date;
import java.util.Objects;
import org.assertj.core.api.BDDAssertions;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.test.context.jdbc.Sql;

// @formatter:off
@Sql(
    statements =
        """
INSERT INTO tenant (id) VALUES ('dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('46847f8f65aa4c1ba09c0517c73e2dfe', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '980672708831', 'AP_1', '["f1", "f2", "f3"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('88b1fb8699424dab82577d8b0d902a80', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '699619858250', 'AP_2', '["f1", "f3"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('0240f266ddc0466d8d77f4ba81a1d4d2', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '477528330090', 'AP_3', '["f2", "f3"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('64230112519c4d228f444cf155a45740', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '674728788347', 'AP_4', '["f1", "f2"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('17665ad2e77149338fb56c4d471cdac6', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '008167261851', 'AP_5', '["f1", "f2", "f3"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('261dbf00abf04eef802b6d246852eaaa', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '449792225684', 'AP_6', '["f1", "f3"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('6835a544607f4c83bfe64d6ecf163ae3', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '710669112651', 'AP_7', '["f1", "f2"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('0b69c3540d014d208be4874f5bb9a279', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '670784973702', 'AP_8', '["f2", "f3"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('49570fab62994e36a441371cced7db1e', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '323929765099', 'AP_9', '["f3"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('7a900622bd2b4be7a2cca40dbb92cbb1', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '274577305513', 'AP_10', '["f5"]', 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('fc0382f98d8b4c8f9907321c074f4484', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '916405170398', 'AP_11', null, 'dc5d93693d06447bba7e3607dcb532ff');
INSERT INTO compatibility_activity(id, activity_id, ap_id, ap_name, incompatible_features, tenant) VALUES
('288164b3f3934b938deca4b47a0087c7', '8d9070a9-981f-4959-821f-2cd6bcf93ef8', '771544994338', 'AP_12', '["f8"]', 'dc5d93693d06447bba7e3607dcb532ff');
""")
// @formatter:on
@WifiJpaDataTest
class CompatibilityActivityRepositoryTest {
  private static final String TENANT_ID = "dc5d93693d06447bba7e3607dcb532ff";
  private static final String ACTIVITY_ID = "8d9070a9-981f-4959-821f-2cd6bcf93ef8";

  @Autowired private CompatibilityActivityRepository unit;

  @Nested
  class WhenFindByActivityIdAndTenantId {
    @Test
    void givenNoRecordExists(Tenant tenant) {
      final var pageable = Pageable.ofSize(10);

      BDDAssertions.then(unit.findByActivityIdAndTenantId(ACTIVITY_ID, tenant.getId(), pageable))
          .asInstanceOf(InstanceOfAssertFactories.type(Page.class))
          .isNotNull()
          .matches(p -> p.getTotalPages() == 0)
          .matches(p -> p.getTotalElements() == 0)
          .extracting(Slice::getContent)
          .asList()
          .isEmpty();
      ;
    }

    @Nested
    class GivenSomeRecordsExist {
      @Test
      void findFirstPage() {
        final var pageable = Pageable.ofSize(10).first();

        BDDAssertions.then(unit.findByActivityIdAndTenantId(ACTIVITY_ID, TENANT_ID, pageable))
            .asInstanceOf(InstanceOfAssertFactories.type(Page.class))
            .isNotNull()
            .matches(p -> p.getTotalPages() == 2)
            .matches(p -> p.getTotalElements() == 12)
            .extracting(
                Slice::getContent, InstanceOfAssertFactories.list(CompatibilityActivity.class))
            .hasSize(10)
            .allMatch(c -> Objects.equals(c.getActivityId(), ACTIVITY_ID))
            .allMatch(c -> Objects.equals(c.getTenant().getId(), TENANT_ID))
            .extracting(AbstractBaseEntity::getId)
            .doesNotContain("fc0382f98d8b4c8f9907321c074f4484", "288164b3f3934b938deca4b47a0087c7");
      }

      @Test
      void findSecondPage() {
        final var pageable = Pageable.ofSize(10).next();

        BDDAssertions.then(unit.findByActivityIdAndTenantId(ACTIVITY_ID, TENANT_ID, pageable))
            .asInstanceOf(InstanceOfAssertFactories.type(Page.class))
            .isNotNull()
            .matches(p -> p.getTotalPages() == 2)
            .matches(p -> p.getTotalElements() == 12)
            .extracting(
                Slice::getContent, InstanceOfAssertFactories.list(CompatibilityActivity.class))
            .hasSize(2)
            .allMatch(c -> Objects.equals(c.getActivityId(), ACTIVITY_ID))
            .allMatch(c -> Objects.equals(c.getTenant().getId(), TENANT_ID))
            .extracting(AbstractBaseEntity::getId)
            .containsExactly(
                "fc0382f98d8b4c8f9907321c074f4484", "288164b3f3934b938deca4b47a0087c7");
      }
    }
  }

  // @formatter:off
  @Sql(
      statements =
          """
      INSERT INTO tenant (id) VALUES ('dc5d93693d06447bba7e3607dcb532ff');
      INSERT INTO compatibility_activity(id, tenant, created_date) VALUES
      ('bd52809cfbf74ab288179d7bcab898e9', 'dc5d93693d06447bba7e3607dcb532ff', '2024-05-30T00:00:00Z');
      INSERT INTO compatibility_activity(id, tenant, created_date) VALUES
      ('0205d084361b49f3b1730ef18ebbda42', 'dc5d93693d06447bba7e3607dcb532ff', '2024-05-31T00:00:00Z');
      INSERT INTO compatibility_activity(id, tenant, created_date) VALUES
      ('8f021ee098e84ba1bc17ff70e3105bad', 'dc5d93693d06447bba7e3607dcb532ff', '2024-06-01T00:00:00Z');
      INSERT INTO compatibility_activity(id, tenant, created_date) VALUES
      ('10ee7d97100748d7aec0c8f7295d32c9', 'dc5d93693d06447bba7e3607dcb532ff', '2024-06-02T00:00:00Z');
      INSERT INTO compatibility_activity(id, tenant, created_date) VALUES
      ('8131cd2d46454be0a7b1a2e6dfb8e129', 'dc5d93693d06447bba7e3607dcb532ff', '2024-06-03T00:00:00Z');
      INSERT INTO compatibility_activity(id, tenant, created_date) VALUES
      ('88572e6ada244842a2323646e52fd2a4', 'dc5d93693d06447bba7e3607dcb532ff', '2024-06-05T00:00:00Z');
      """)
  // @formatter:on
  @Nested
  class WhenDeleteByCreatedDateBefore {
    private final Date before = Date.from(Instant.parse("2024-06-04T00:00:00Z"));

    @Test
    void thenReturnDeletedCount() {
      BDDAssertions.then(unit.deleteByCreatedDateBefore(before)).isEqualTo(5);
    }
  }
}
