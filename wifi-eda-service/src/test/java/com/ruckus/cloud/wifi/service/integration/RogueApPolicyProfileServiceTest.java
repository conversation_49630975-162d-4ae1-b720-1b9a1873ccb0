package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.service.impl.RogueApPolicyProfileServiceCtrlImpl.DEFAULT_ROGUE_AP_POLICY_NAME;
import static com.ruckus.cloud.wifi.service.impl.RogueApPolicyProfileServiceCtrlImpl.NONPERSISTED_DEFAULT_POLICY_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture.createRule;
import static com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture.extractVenues;
import static com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture.rogueApPolicy;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.venueRogueApWithDefaultProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicyVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RogueClassificationEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RogueRuleTypeEnum;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.RogueApPolicyProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.VenueTemplateServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.integration.config.DhcpServiceMockTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedTenantServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.FirmwareCapabilityServiceTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.InitVenueServiceImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.VenueDecorator;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;

@Slf4j
@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
public class RogueApPolicyProfileServiceTest extends AbstractServiceTest {

  @Test
  public void testDataParameterAndRepositoryUtil(Tenant tenant) {
    // !! when using data parameter Venue v1, this fails at the final v1 assert ??
    Venue v1 = createVenue(tenant, "v1");
    Venue v2 = createVenue(tenant, "v2");
    RogueClassificationPolicy policy = rogueApPolicy("rogue-policy");
    policy.getRules().forEach(rule -> rule.setRogueClassificationPolicy(policy));
    policy.setTenant(tenant);
    RogueClassificationPolicy p = repositoryUtil.createOrUpdate(policy, tenant.getId(), randomTxId());
    RogueClassificationPolicyVenue pv1 = new RogueClassificationPolicyVenue();
    pv1.setVenue(v1);
    pv1.setRogueClassificationPolicy(p);
    RogueClassificationPolicyVenue pv2 = new RogueClassificationPolicyVenue();
    pv2.setVenue(v2);
    pv2.setRogueClassificationPolicy(p);
    repositoryUtil.createOrUpdate(pv1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(pv2, tenant.getId(), randomTxId());
    p = repositoryUtil.find(RogueClassificationPolicy.class, p.getId());
    assertEquals(2, p.getRogueClassificationPolicyVenues().size());
    v1 = repositoryUtil.find(Venue.class, v1.getId());
    assertThat(v1.getRogueClassificationPolicyVenue())
        .isNotEmpty()
        .hasSize(1);
    v2 = repositoryUtil.find(Venue.class, v2.getId());
    assertThat(v2.getRogueClassificationPolicyVenue())
        .isNotEmpty()
        .hasSize(1);
  }

  @Test
  public void testRoguePolicy_getNonPersistedDefaultPolicy_Successfully() {
    testRoguePolicy_getNonPersistedDefaultPolicy_Successfully(false);
  }

  @Test
  public void template_testRoguePolicy_getNonPersistedDefaultPolicy_Successfully() {
    testRoguePolicy_getNonPersistedDefaultPolicy_Successfully(true);
  }

  void testRoguePolicy_getNonPersistedDefaultPolicy_Successfully(boolean isTemplate) {
    //When
    RogueClassificationPolicy policy = getRogueApPolicy(NONPERSISTED_DEFAULT_POLICY_ID, isTemplate);

    //Then
    assertEquals(NONPERSISTED_DEFAULT_POLICY_ID, policy.getId());
  }

  @Test
  public void testRoguePolicy_createPolicy_successfully(Venue venue) {
    testRoguePolicy_createPolicy_successfully(venue, false);
  }

  @Test
  public void template_testRoguePolicy_createPolicy_successfully(
      @VenueDecorator(isTemplate = true) Venue venue) {
    testRoguePolicy_createPolicy_successfully(venue, true);
  }

  void testRoguePolicy_createPolicy_successfully(Venue venue, boolean isTemplate) {
    //Given
    String myPolicyId = randomId();

    RogueClassificationPolicy policy = new RogueClassificationPolicy();
    policy.setId(myPolicyId); // should work in r1
    policy.setName("policy name");
    policy.setDescription("policy description");
    List<RogueClassificationPolicyRule> rules = new ArrayList<>();
    rules.add(createRule(1, "SameNetworkRule", RogueRuleTypeEnum.SameNetworkRule,
        RogueClassificationEnum.Malicious));
    rules.add(createRule(2, "NullSSIDRule", RogueRuleTypeEnum.NullSSIDRule,
        RogueClassificationEnum.Unclassified));
    policy.setRules(rules);

    applyVenue(policy, venue);

    policy = addRogueApPolicy(policy, isTemplate);

    //When
    RogueClassificationPolicy policyFromDb = getRogueApPolicy(policy.getId(), isTemplate);

    //Then
    assertEquals(myPolicyId, policyFromDb.getId());
    assertEquals("policy name", policyFromDb.getName());
    assertEquals("policy description", policyFromDb.getDescription());
    assertEquals(2, policyFromDb.getRules().size());
    assertEquals("SameNetworkRule", policyFromDb.getRules().get(0).getName());
    assertEquals(1, policyFromDb.getRules().get(0).getPriority().intValue());
    assertEquals(RogueRuleTypeEnum.SameNetworkRule, policyFromDb.getRules().get(0).getType());
    assertEquals(RogueClassificationEnum.Malicious, policyFromDb.getRules().get(0).getClassification());
    assertEquals("NullSSIDRule", policyFromDb.getRules().get(1).getName());
    assertEquals(2, policyFromDb.getRules().get(1).getPriority().intValue());
    assertEquals(RogueRuleTypeEnum.NullSSIDRule, policyFromDb.getRules().get(1).getType());
    assertEquals(RogueClassificationEnum.Unclassified, policyFromDb.getRules().get(1).getClassification());

  }

  @Test
  public void testRoguePolicy_createPolicy_duplicatedPolicyName_failedWithException(Tenant tenant) {
    // Given
    RogueClassificationPolicy policy = rogueApPolicy("policy name");
    RogueClassificationPolicy policy2 = rogueApPolicy("policy name");

    // When
    CommonException exception = assertThrows(CommonException.class, () -> {
      addRogueApPolicy(policy, false);
      addRogueApPolicy(policy2, false);
    });

    assertEquals(HttpStatus.CONFLICT, exception.getStatus());
    assertEquals(Errors.WIFI_10176, exception.getErrorCode());
  }

  @Test
  public void testRoguePolicy_createPolicy_withVenue_successfully(Venue venue) {
    testRoguePolicy_createPolicy_withVenue_successfully(venue, false);
  }

  @Test
  public void template_testRoguePolicy_createPolicy_withVenue_successfully(
      @VenueDecorator(isTemplate = true) Venue venue) {
    testRoguePolicy_createPolicy_withVenue_successfully(venue, true);
  }

  void testRoguePolicy_createPolicy_withVenue_successfully(Venue venue, boolean isTemplate) {
    // Given
    RogueClassificationPolicy policy = new RogueClassificationPolicy();
    policy.setName("policy name");
    policy.setDescription("policy description");
    List<RogueClassificationPolicyRule> rules = new ArrayList<>();
    rules.add(createRule(1, "SameNetworkRule", RogueRuleTypeEnum.SameNetworkRule,
        RogueClassificationEnum.Malicious));
    rules.add(createRule(2, "NullSSIDRule", RogueRuleTypeEnum.NullSSIDRule,
        RogueClassificationEnum.Unclassified));
    policy.setRules(rules);

    applyVenue(policy, venue);

    // When
    policy = addRogueApPolicy(policy, isTemplate);
    RogueClassificationPolicy policyFromDb = getRogueApPolicy(policy.getId(), isTemplate);

    // Then
    assertNotNull(policyFromDb);
    List<Venue> venues = extractVenues(policyFromDb);
    assertEquals(1, venues.size());
    assertEquals(venue.getId(), venues.get(0).getId());
  }

  @Test
  public void testRoguePolicy_createPolicy_withVenueAlreadyBound_successfully(Venue venue) {
    testRoguePolicy_createPolicy_withVenueAlreadyBound_successfully(venue, false);
  }

  @Test
  public void template_testRoguePolicy_createPolicy_withVenueAlreadyBound_successfully(
      @VenueDecorator(isTemplate = true) Venue venue) {
    testRoguePolicy_createPolicy_withVenueAlreadyBound_successfully(venue, true);
  }

  void testRoguePolicy_createPolicy_withVenueAlreadyBound_successfully(Venue venue, boolean isTemplate) {
    //Given
    //Set to default policy
    String venueId = venue.getId();
    VenueRogueAp venueRogueAp = venueRogueApWithDefaultProfile();
    updateVenueRogueAp(venueId, venueRogueAp, isTemplate);
    assertNotNull(getVenueRogueAp(venueId, isTemplate).getRoguePolicyId());

    //When set to a policy
    RogueClassificationPolicy policy = rogueApPolicy("policy");
    applyVenue(policy, venue);
    policy = addRogueApPolicy(policy, isTemplate);
    RogueClassificationPolicy policyFromDb = getRogueApPolicy(policy.getId(), isTemplate);

    // Then
    assertNotNull(policyFromDb);
    List<Venue> venues = extractVenues(policyFromDb);
    assertEquals(1, venues.size());
    assertEquals(venueId, venues.get(0).getId());
    assertEquals(policy.getId(), getVenueRogueAp(venueId, isTemplate).getRoguePolicyId());

  }

  @Test
  public void testRoguePolicy_createPolicy_venueNotExisted_failedWithException(Tenant tenant) {
    //Given
    String venueId = "non-existed venue";
    final Venue venue = VenueTestFixture.randomVenue(tenant);
    venue.setId(venueId);
    venue.setName("non-existed venue name");

    RogueClassificationPolicy policy = rogueApPolicy("policy name");
    applyVenue(policy, venue);

    assertThrows(ObjectNotFoundException.class, () -> {
      addRogueApPolicy(policy, false);
    });
  }

  @Test
  public void testRoguePolicy_createPolicy_namedDefaultPolicy_failedWithException(Tenant tenant) {
    //Given
    RogueClassificationPolicy policy = rogueApPolicy(DEFAULT_ROGUE_AP_POLICY_NAME);

    CommonException exception = assertThrows(CommonException.class, () -> {
      addRogueApPolicy(policy, false);
    });
    assertEquals(Errors.WIFI_10192, exception.getErrorCode());
  }

  @Test
  public void testRoguePolicy_getPolicies_successfully(Tenant tenant) {
    /**
     * This is R1 case. It will return real entities from db.
     * No default profile until rogue is enabled at the first time.
     */

    //Given
    RogueClassificationPolicy policy = rogueApPolicy("policy");
    addRogueApPolicy(policy, false);

    //When
    List<RogueClassificationPolicy> policyListFromDb = getRogueApPolicies();

    //Then
    assertEquals(1, policyListFromDb.size()); // no default profile until rogue is enabled
    assertEquals("policy", policyListFromDb.get(0).getName());
    assertEquals("description", policyListFromDb.get(0).getDescription());
    assertEquals(2, policyListFromDb.get(0).getRules().size());
    assertEquals("Same Network Rule", policyListFromDb.get(0).getRules().get(0).getName());
    assertEquals(1, policyListFromDb.get(0).getRules().get(0).getPriority().intValue());
    assertEquals(RogueRuleTypeEnum.SameNetworkRule, policyListFromDb.get(0).getRules().get(0).getType());
    assertEquals(RogueClassificationEnum.Malicious, policyListFromDb.get(0).getRules().get(0).getClassification());
  }

  @Test
  public void testRoguePolicy_getPolicies_shouldIncludeVenueIdAndName_successfully(Venue venue) {
    testRoguePolicy_getPolicies_shouldIncludeVenueIdAndName_successfully(venue, false);
  }

  @Test
  public void template_testRoguePolicy_getPolicies_shouldIncludeVenueIdAndName_successfully(
      @VenueDecorator(isTemplate = true) Venue venue) {
    testRoguePolicy_getPolicies_shouldIncludeVenueIdAndName_successfully(venue, true);
  }

  void testRoguePolicy_getPolicies_shouldIncludeVenueIdAndName_successfully(Venue venue, boolean isTemplate) {
    //Given
    String venueId = venue.getId();
    assertNull(venue.getRogueAp().getRoguePolicyId());

    //When
    updateVenueRogueAp(venueId, venueRogueApWithDefaultProfile(), isTemplate);
    VenueRogueAp actualVenueRogueAp = getVenueRogueAp(venueId, isTemplate);
    List<RogueClassificationPolicy> policyListFromDb = getRogueApPolicies(isTemplate);

    //Then
    assertNotNull(actualVenueRogueAp.getRoguePolicyId());
    assertEquals(1, policyListFromDb.size());
    List<Venue> venues = extractVenues(policyListFromDb.get(0));
    assertEquals(1, venues.size());
    assertNotNull(venues.get(0).getId());
    assertNotNull(venues.get(0).getName());
  }

  @Test
  public void testRoguePolicy_updateDefaultPolicy_failedWithException() {
    //Given
    RogueClassificationPolicy policy = rogueApPolicy(DEFAULT_ROGUE_AP_POLICY_NAME);
    policy.setId(CommonTestFixture.randomId());

    CommonException exception = assertThrows(CommonException.class, () -> {
      updateRogueApPolicy(policy.getId(), policy, false);
    });
    assertEquals(Errors.WIFI_10175, exception.getErrorCode());
  }

  @Test
  public void testRoguePolicy_updateNonPersistedDefaultPolicy_failedWithException() {
    RogueClassificationPolicy policy = rogueApPolicy(DEFAULT_ROGUE_AP_POLICY_NAME);
    policy.setId(NONPERSISTED_DEFAULT_POLICY_ID);

    CommonException exception = assertThrows(CommonException.class, () -> {
      updateRogueApPolicy(policy.getId(), policy, false);
    });
    assertEquals(Errors.WIFI_10175, exception.getErrorCode());
  }

  @Test
  public void testRoguePolicy_updatePolicy_moreRules_successfully(Venue venue) {
    // Given
    RogueClassificationPolicy p1 = rogueApPolicy("Policy-test");
    applyVenue(p1, venue);
    p1 = addRogueApPolicy(p1, false);

    RogueClassificationPolicy p2 = new RogueClassificationPolicy();
    p2.setName("Policy-test 2");
    p2.setDescription("Policy-description 2");
    var rules = new ArrayList<RogueClassificationPolicyRule>();
    rules.add(createRule(1, "AdhocRule", RogueRuleTypeEnum.AdhocRule,
        RogueClassificationEnum.Ignored));
    rules.add(createRule(2, "CTSAbuseRule", RogueRuleTypeEnum.CTSAbuseRule,
        RogueClassificationEnum.Known));

    /**
     * ACX-22956: SSID, SNR, MAC-OUI need multiple rules
     */
    rules.add(createRule(3, "CustomSsidRule1", RogueRuleTypeEnum.CustomSsidRule,
        RogueClassificationEnum.Known, "SSID1"));
    rules.add(createRule(4, "CustomSsidRule2", RogueRuleTypeEnum.CustomSsidRule,
        RogueClassificationEnum.Known, "SSID2"));
    rules.add(createRule(5, "CustomSnrRule1", RogueRuleTypeEnum.CustomSnrRule,
        RogueClassificationEnum.Known, "1"));
    rules.add(createRule(6, "CustomSnrRule2", RogueRuleTypeEnum.CustomSnrRule,
        RogueClassificationEnum.Known, "2"));
    rules.add(createRule(7, "CustomMacOuiRule1", RogueRuleTypeEnum.CustomMacOuiRule,
        RogueClassificationEnum.Known, "AA:AA:AA"));
    rules.add(createRule(8, "CustomMacOuiRule2", RogueRuleTypeEnum.CustomMacOuiRule,
        RogueClassificationEnum.Known, "BB:BB:BB"));
    p2.setRules(rules);

    // When
    updateRogueApPolicy(p1.getId(), p2, false);
    RogueClassificationPolicy policyFromDb = getRogueApPolicy(p1.getId(), false);

    // Then
    assertEquals("Policy-test 2", policyFromDb.getName());
    assertEquals("Policy-description 2", policyFromDb.getDescription());
    assertEquals(8, policyFromDb.getRules().size());

    assertEquals("AdhocRule", policyFromDb.getRules().get(0).getName());
    assertEquals(1, policyFromDb.getRules().get(0).getPriority().intValue());
    assertEquals(RogueClassificationEnum.Ignored, policyFromDb.getRules().get(0).getClassification());
    assertEquals(RogueRuleTypeEnum.AdhocRule, policyFromDb.getRules().get(0).getType());

    assertEquals("CTSAbuseRule", policyFromDb.getRules().get(1).getName());
    assertEquals(2, policyFromDb.getRules().get(1).getPriority().intValue());
    assertEquals(RogueClassificationEnum.Known, policyFromDb.getRules().get(1).getClassification());
    assertEquals(RogueRuleTypeEnum.CTSAbuseRule, policyFromDb.getRules().get(1).getType());

    assertEquals("CustomSsidRule1", policyFromDb.getRules().get(2).getName());
    assertEquals(3, policyFromDb.getRules().get(2).getPriority().intValue());
    assertEquals(RogueClassificationEnum.Known, policyFromDb.getRules().get(2).getClassification());
    assertEquals(RogueRuleTypeEnum.CustomSsidRule, policyFromDb.getRules().get(2).getType());
    assertEquals("SSID1", policyFromDb.getRules().get(2).getValue());

    assertEquals("CustomSsidRule2", policyFromDb.getRules().get(3).getName());
    assertEquals(4, policyFromDb.getRules().get(3).getPriority().intValue());
    assertEquals(RogueClassificationEnum.Known, policyFromDb.getRules().get(3).getClassification());
    assertEquals(RogueRuleTypeEnum.CustomSsidRule, policyFromDb.getRules().get(3).getType());
    assertEquals("SSID2", policyFromDb.getRules().get(3).getValue());
  }

  @Test
  public void testRoguePolicy_updatePolicy_lessRules_successfully(Venue venue) {
    // Given
    RogueClassificationPolicy policy = rogueApPolicy("Policy-test");
    applyVenue(policy, venue);
    String policyId = addRogueApPolicy(policy, false).getId();

    policy.setName("Policy-test 2");
    policy.setDescription("Policy-description 2");
    var rules = new ArrayList<RogueClassificationPolicyRule>();
    rules.add(createRule(1, "NullSSIDRule", RogueRuleTypeEnum.NullSSIDRule,
        RogueClassificationEnum.Unclassified));
    policy.setRules(rules);

    // When
    updateRogueApPolicy(policyId, policy, false);
    RogueClassificationPolicy policyFromDb = getRogueApPolicy(policyId, false);

    // Then
    assertEquals("Policy-test 2", policyFromDb.getName());
    assertEquals("Policy-description 2", policyFromDb.getDescription());
    assertEquals(1, policyFromDb.getRules().size());

    assertEquals("NullSSIDRule", policyFromDb.getRules().get(0).getName());
    assertEquals(1, policyFromDb.getRules().get(0).getPriority().intValue());
    assertEquals(RogueClassificationEnum.Unclassified, policyFromDb.getRules().get(0).getClassification());
    assertEquals(RogueRuleTypeEnum.NullSSIDRule, policyFromDb.getRules().get(0).getType());

  }

  @Test
  public void testRoguePolicy_updatePolicy_updateBinding_successfully(Tenant tenant) {
    testRoguePolicy_updatePolicy_updateBinding_successfully(tenant, false);
  }

  @Test
  public void template_testRoguePolicy_updatePolicy_updateBinding_successfully(Tenant tenant) {
    testRoguePolicy_updatePolicy_updateBinding_successfully(tenant, true);
  }

  void testRoguePolicy_updatePolicy_updateBinding_successfully(Tenant tenant, boolean isTemplate) {
    // Given
    Venue v1 = createVenue(tenant, "v1", isTemplate);
    Venue v2 = createVenue(tenant, "v2", isTemplate);
    Venue v3 = createVenue(tenant, "v3", isTemplate);
    String venueId1 = v1.getId();
    String venueId2 = v2.getId();
    String venueId3 = v3.getId();

    RogueClassificationPolicy policy = rogueApPolicy("Policy-test");
    applyVenue(policy, v1);
    applyVenue(policy, v2);

    // When
    String policyId = addRogueApPolicy(policy, isTemplate).getId();
    policy.setRogueClassificationPolicyVenues(null);
    applyVenue(policy, v2);
    applyVenue(policy, v3);

    updateRogueApPolicy(policyId, policy, isTemplate);
    RogueClassificationPolicy actualPolicy = getRogueApPolicy(policyId, isTemplate);
    Set<String> venues = extractVenues(actualPolicy)
        .stream().map(Venue::getId).collect(Collectors.toSet());

    // Then
    assertEquals(2, venues.size());
    assertTrue(venues.contains(venueId2));
    assertTrue(venues.contains(venueId3));
  }

  @Test
  public void testRoguePolicy_updatePolicy_bindToNewVenue_shouldTurnOnRogueDetecting_successfully(Tenant tenant) {
    testRoguePolicy_updatePolicy_bindToNewVenue_shouldTurnOnRogueDetecting_successfully(tenant, false);
  }

  @Test
  public void template_testRoguePolicy_updatePolicy_bindToNewVenue_shouldTurnOnRogueDetecting_successfully(Tenant tenant) {
    testRoguePolicy_updatePolicy_bindToNewVenue_shouldTurnOnRogueDetecting_successfully(tenant, true);
  }

  void testRoguePolicy_updatePolicy_bindToNewVenue_shouldTurnOnRogueDetecting_successfully(Tenant tenant, boolean isTemplate) {
    // Given
    Venue v1 = createVenue(tenant, "v1", isTemplate);
    Venue v2 = createVenue(tenant, "v2", isTemplate);

    RogueClassificationPolicy policy = rogueApPolicy("Policy-test");
    applyVenue(policy, v1);

    policy = addRogueApPolicy(policy, isTemplate);
    VenueRogueAp venueRogueAp1 = getVenueRogueAp(v1.getId(), isTemplate);

    assertEquals(policy.getId(), venueRogueAp1.getRoguePolicyId());
    assertTrue(venueRogueAp1.getEnabled());

    // When
    applyVenue(policy, v2);

    updateRogueApPolicy(policy.getId(), policy, isTemplate);
    VenueRogueAp venueRogueAp2 = getVenueRogueAp(v2.getId(), isTemplate);

    // Then
    assertEquals(policy.getId(), venueRogueAp2.getRoguePolicyId());
    assertTrue(venueRogueAp2.getEnabled());
  }

  @Test
  public void testRoguePolicy_updatePolicy_unbindShouldResetToDefaultPolicy_successfully(Tenant tenant) {
    testRoguePolicy_updatePolicy_unbindShouldResetToDefaultPolicy_successfully(tenant, false);
  }

  @Test
  public void template_testRoguePolicy_updatePolicy_unbindShouldResetToDefaultPolicy_successfully(Tenant tenant) {
    testRoguePolicy_updatePolicy_unbindShouldResetToDefaultPolicy_successfully(tenant, true);
  }

  void testRoguePolicy_updatePolicy_unbindShouldResetToDefaultPolicy_successfully(Tenant tenant, boolean isTemplate) {

    Venue v1 = createVenue(tenant, "v1", isTemplate);
    Venue v2 = createVenue(tenant, "v2", isTemplate);

    RogueClassificationPolicy policy = rogueApPolicy("Policy-test");
    applyVenue(policy, v1);
    applyVenue(policy, v2);

    // When
    policy = addRogueApPolicy(policy, isTemplate);
    policy.setRogueClassificationPolicyVenues(null);
    applyVenue(policy, v1);

    updateRogueApPolicy(policy.getId(), policy, isTemplate);
    VenueRogueAp venueRogueApForVenueId1 = getVenueRogueAp(v1.getId(), isTemplate);
    VenueRogueAp venueRogueApForVenueId2 = getVenueRogueAp(v2.getId(), isTemplate);
    RogueClassificationPolicy v2Policy =
        getRogueApPolicy(venueRogueApForVenueId2.getRoguePolicyId(), isTemplate);

    // Then
    assertTrue(venueRogueApForVenueId1.getEnabled());
    assertTrue(venueRogueApForVenueId2.getEnabled());
    assertEquals(policy.getId(), venueRogueApForVenueId1.getRoguePolicyId());
    assertEquals(DEFAULT_ROGUE_AP_POLICY_NAME, v2Policy.getName());
  }

  @Test
  public void testRoguePolicy_updatePolicy_bindFromActiveVenue_successfully(Tenant tenant) {
    testRoguePolicy_updatePolicy_bindFromActiveVenue_successfully(tenant, false);
  }

  @Test
  public void template_testRoguePolicy_updatePolicy_bindFromActiveVenue_successfully(Tenant tenant) {
    testRoguePolicy_updatePolicy_bindFromActiveVenue_successfully(tenant, true);
  }

  void testRoguePolicy_updatePolicy_bindFromActiveVenue_successfully(Tenant tenant, boolean isTemplate) {
    // Given
    Venue v1 = createVenue(tenant, "v1", isTemplate);
    Venue v2 = createVenue(tenant, "v2", isTemplate);

    RogueClassificationPolicy policy_1 = rogueApPolicy("p1");
    applyVenue(policy_1, v1);

    RogueClassificationPolicy policy_2 = rogueApPolicy("p2");
    applyVenue(policy_2, v2);

    policy_1 = addRogueApPolicy(policy_1, isTemplate);
    policy_2 = addRogueApPolicy(policy_2, isTemplate);

    // When
    log.info("====== updatePolicy =====");
    applyVenue(policy_1, v2);
    updateRogueApPolicy(policy_1.getId(), policy_1, isTemplate);
    RogueClassificationPolicy policy_1_result = getRogueApPolicy(policy_1.getId(), isTemplate);
    RogueClassificationPolicy policy_2_result = getRogueApPolicy(policy_2.getId(), isTemplate);

    // Then
    assertEquals(2, extractVenues(policy_1_result).size());
    assertEquals(0, extractVenues(policy_2_result).size());
  }

  @Test
  public void testRoguePolicy_updateDefaultPolicy_failedWithException2() {
    RogueClassificationPolicy policy = rogueApPolicy(DEFAULT_ROGUE_AP_POLICY_NAME);
    policy.setId(NONPERSISTED_DEFAULT_POLICY_ID);

    CommonException exception = assertThrows(CommonException.class, () -> {
      updateRogueApPolicy(policy.getId(), policy, false);
    });
    assertEquals(Errors.WIFI_10175, exception.getErrorCode());
  }

  @Test
  public void testRoguePolicy_deleteNonPersistedDefaultPolicy_failedWithException() {
    CommonException exception = assertThrows(CommonException.class, () -> {
      deleteRogueApPolicy(NONPERSISTED_DEFAULT_POLICY_ID, false);
    });
    assertEquals(Errors.WIFI_10175, exception.getErrorCode());
  }

  @Test
  public void testRoguePolicy_deleteNonexistentPolicy_failedWithException() {
    assertThrows(ObjectNotFoundException.class, () -> {
      deleteRogueApPolicy("Nonexistent policy id", false);
    });
  }

  @Test
  public void testRoguePolicy_deletePoliciesIncludingDefaultPolicy_failedWithException(Venue v) {
    String defaultPolicyId =
        updateVenueRogueAp(v.getId(), venueRogueApWithDefaultProfile(), false).getRoguePolicyId();

    CommonException exception = assertThrows(CommonException.class, () -> {
      deleteRogueApPolicies(List.of(defaultPolicyId));
    });
    assertEquals(Errors.WIFI_10175, exception.getErrorCode());
  }

  @Test
  public void testRoguePolicy_deletePoliciesIncludingNonPersistedDefaultPolicy_failedWithException() {
    CommonException exception = assertThrows(CommonException.class, () -> {
      deleteRogueApPolicies(List.of(NONPERSISTED_DEFAULT_POLICY_ID));
    });
    assertEquals(Errors.WIFI_10175, exception.getErrorCode());
  }

  @Test
  public void testRoguePolicy_deleteInUsedPolicy_failedWithException(Venue v) {
    //Given
    RogueClassificationPolicy policy = rogueApPolicy("test");

    //When
    policy = addRogueApPolicy(policy, false);
    String policyId = policy.getId();
    VenueRogueAp venueRogueAp = venueRogueApWithDefaultProfile();
    venueRogueAp.setRoguePolicyId(policyId);
    updateVenueRogueAp(v.getId(), venueRogueAp, false);

    CommonException exception = assertThrows(CommonException.class, () -> {
      deleteRogueApPolicy(policyId, false);
    });
    assertEquals(Errors.WIFI_10174, exception.getErrorCode());
  }

  @Test
  public void testRoguePolicy_deleteInUsedPolicies_failedWithException(Venue v) {
    //Given
    RogueClassificationPolicy policy = rogueApPolicy("test");

    //When
    policy = addRogueApPolicy(policy, false);
    String policyId = policy.getId();
    VenueRogueAp venueRogueAp = venueRogueApWithDefaultProfile();
    venueRogueAp.setRoguePolicyId(policyId);
    updateVenueRogueAp(v.getId(), venueRogueAp, false).getRoguePolicyId();

    CommonException exception = assertThrows(CommonException.class, () -> {
      deleteRogueApPolicies(List.of(policyId));
    });
    assertEquals(Errors.WIFI_10174, exception.getErrorCode());
  }

  @TestConfiguration
  @Import({
      FirmwareCapabilityServiceTestConfig.class,
      ExtendedTenantServiceCtrlImplTestConfig.class,
      DhcpServiceMockTestConfig.class,
      ExtendedVenueServiceCtrlImplTestConfig.class,
      VenueTemplateServiceCtrlImpl.class,
      RogueApPolicyProfileServiceCtrlImpl.class,
      InitVenueServiceImplTestConfig.class
  })
  static class TestConfig {
  }
}
