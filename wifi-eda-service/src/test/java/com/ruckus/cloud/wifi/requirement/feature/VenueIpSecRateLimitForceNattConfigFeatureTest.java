package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.IpsecProfileRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.viewmodel.FeatureGroup;
import com.ruckus.cloud.wifi.viewmodel.FeatureLevel;
import com.ruckus.cloud.wifi.viewmodel.FeatureType;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_IPSEC_PSK_OVER_NETWORK;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;
import static com.ruckus.cloud.wifi.requirement.ApModelFamily.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@WifiUnitTest
public class VenueIpSecRateLimitForceNattConfigFeatureTest {

  @MockBean
  private IpsecProfileRepository repository;

  @SpyBean
  private VenueIpSecRateLimitForceNattConfigFeature unit;

  @Test
  void getFeatureNameTest() {
    assertThat(unit.getFeatureName())
        .isEqualTo(VenueIpSecRateLimitForceNattConfigFeature.NAME);
  }

  @Test
  void getFeatureGroupTest() {
    assertThat(unit.getFeatureGroup())
        .isEqualTo(FeatureGroup.IPSEC_PROFILE.getDisplayName());
  }

  @Test
  void getFeatureLevelTest() {
    assertThat(unit.getFeatureLevel())
        .isEqualTo(FeatureLevel.VENUE);
  }

  @Test
  void getFeatureTypeTest() {
    assertThat(unit.getFeatureType())
        .isEqualTo(FeatureType.WIFI);
  }

  @Test
  void getRequirementsTest() {
    assertThat(unit.getRequirements())
        .isNotEmpty().singleElement()
        .satisfies(apFirmwareModels -> {
          assertThat(apFirmwareModels.getFirmware()).isEqualTo("7.1.0.511.1070");
          assertThat(apFirmwareModels.getModels()).isEqualTo(
              Stream.of(AC_WAVE1.getModels(), AC_WAVE2.getModels(),
                  WIFI_6.getModels(), WIFI_6E.getModels(),
                  WIFI_7.getModels().stream().filter(m -> !m.equals("R370"))
                      .collect(Collectors.toSet())).flatMap(Set::stream).collect(
                  Collectors.toSet()));
        });
  }

  @Nested
  class WhenConfigIpSec {

    @Test
    @FeatureFlag(disable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenR370Disable(Venue venue) {
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenIpSecDisable(Venue venue) {
      when(repository.findAllLanPortsActivatedIpsecProfiles(
          anyString(), anyString())).thenReturn(List.of());

      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenIpSecEnable(Venue venue) {
      when(repository.findAllLanPortsActivatedIpsecProfiles(
          anyString(), anyString())).thenReturn(
          List.of(new IpsecProfile()));

      BDDAssertions.then(unit.test(venue)).isTrue();
    }
  }
}
