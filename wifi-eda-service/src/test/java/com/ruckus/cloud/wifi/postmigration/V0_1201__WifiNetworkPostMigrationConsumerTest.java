package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob.JobCase;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.MessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class V0_1201__WifiNetworkPostMigrationConsumerTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageUtil messageUtil;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private NetworkRepository networkRepository;
  @Autowired
  private V0_1201__WifiNetworkPostMigrationConsumer postMigrationConsumer;

  @Nested
  class WhenRunPostMigration {

    private List<String> wifiNetworkTenantIds;

    @BeforeEach
    void beforeEach() {
      final var tenants =
          Stream.generate(() -> TenantTestFixture.randomTenant(t -> {
              }))
              .limit(5)
              .peek(tenant -> repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId()))
              .collect(Collectors.toList());
      Collections.shuffle(tenants);
      wifiNetworkTenantIds =
          tenants.stream()
              .limit(3)
              .map(
                  tenant -> NetworkTestFixture.randomNetwork(tenant, d -> {
                  }))
              .peek(
                  policy ->
                      repositoryUtil.createOrUpdate(
                          policy, policy.getTenant().getId(), randomTxId()))
              .map(policy -> policy.getTenant().getId())
              .toList();
    }

    @Test
    void thenSendAsyncJobForEachTenant() throws Exception {
      final var expectedTenantIds = new HashSet<>(
          networkRepository.findAllDistinctTenantIds());

      postMigrationConsumer.run(null);

      final var receivedTenantIds = new HashSet<String>();
      final var receivedRequestIds = new HashSet<String>();

      for (int i = 0; i < expectedTenantIds.size(); i++) {
        final var message = receive();
        assertThat(message)
            .isNotNull()
            .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID) != null)
            .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID) != null)
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(WifiAsyncJob::getJobCase)
            .isEqualTo(JobCase.WIFI_NETWORK_POST_MIGRATION_JOB);

        receivedTenantIds.add(
            new String(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID).value()));

        receivedRequestIds.add(
            new String(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID).value()));
      }

      assertThat(receivedTenantIds)
          .isNotEmpty()
          .hasSameElementsAs(expectedTenantIds)
          .containsAll(wifiNetworkTenantIds);

      assertThat(receivedRequestIds).isNotEmpty().hasSize(1);
    }

    private KafkaProtoMessage<WifiAsyncJob> receive() {
      return messageUtil.receive(
          kafkaTopicProvider.getWifiAsyncJob(),
          data -> {
            try {
              return WifiAsyncJob.parseFrom(data);
            } catch (InvalidProtocolBufferException e) {
              throw new RuntimeException(e);
            }
          });
    }
  }
}