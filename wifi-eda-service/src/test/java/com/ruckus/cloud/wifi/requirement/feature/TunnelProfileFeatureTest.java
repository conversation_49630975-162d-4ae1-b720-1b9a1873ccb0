package com.ruckus.cloud.wifi.requirement.feature;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.PinProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.repository.SdLanProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class TunnelProfileFeatureTest {

  @MockBean
  private SdLanProfileRegularSettingRepository sdLanProfileRegularSettingRepository;
  @MockBean
  private PinProfileRegularSettingRepository pinProfileRegularSettingRepository;

  @SpyBean
  private TunnelProfileFeature unit;

  @Nested
  class WhenIsTunnelProfileEnabled {

    @Test
    void givenSdLanNotExistAndPinNotExist(Venue venue) {
      when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
          eq(TxCtxHolder.tenantId()), eq(venue.getId())))
          .thenReturn(false);
      when(pinProfileRegularSettingRepository.existsByTenantIdAndVenueId(
          eq(TxCtxHolder.tenantId()), eq(venue.getId())))
          .thenReturn(false);
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    void givenSdLanExistAndPinNotExist(Venue venue) {
      when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
          eq(TxCtxHolder.tenantId()), eq(venue.getId())))
          .thenReturn(true);
      when(pinProfileRegularSettingRepository.existsByTenantIdAndVenueId(
          eq(TxCtxHolder.tenantId()), eq(venue.getId())))
          .thenReturn(false);
      BDDAssertions.then(unit.test(venue)).isTrue();
    }

    @Test
    void givenSdLanExistAndPinExist(Venue venue) {
      when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
          eq(TxCtxHolder.tenantId()), eq(venue.getId())))
          .thenReturn(true);
      when(pinProfileRegularSettingRepository.existsByTenantIdAndVenueId(
          eq(TxCtxHolder.tenantId()), eq(venue.getId())))
          .thenReturn(true);
      BDDAssertions.then(unit.test(venue)).isTrue();
    }

    @Test
    void givenSdLanNotExistAndPinExist(Venue venue) {
      when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
          eq(TxCtxHolder.tenantId()), eq(venue.getId())))
          .thenReturn(false);
      when(pinProfileRegularSettingRepository.existsByTenantIdAndVenueId(
          eq(TxCtxHolder.tenantId()), eq(venue.getId())))
          .thenReturn(true);
      BDDAssertions.then(unit.test(venue)).isTrue();
    }
  }
}
