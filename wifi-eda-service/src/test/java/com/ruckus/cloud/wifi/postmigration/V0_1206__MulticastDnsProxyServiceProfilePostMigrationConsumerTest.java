package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.MulticastDnsProxyServiceProfileApGenerator;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob.JobCase;
import com.ruckus.cloud.wifi.repository.MulticastDnsProxyServiceProfileApRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApTestFixture;
import com.ruckus.cloud.wifi.test.fixture.MulticaseDnsProxyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.MessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class V0_1206__MulticastDnsProxyServiceProfilePostMigrationConsumerTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageUtil messageUtil;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  private MulticastDnsProxyServiceProfileApRepository proxyServiceProfileApRepository;

  @Autowired
  private V0_1206__MulticastDnsProxyServiceProfilePostMigrationConsumer postMigrationConsumer;

  @Nested
  class WhenRunPostMigration {

    private List<String> multicastDnsProxyServiceProfileApTenantIds;
    private MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile;

    @BeforeEach
    void beforeEach() {
      final var tenants =
          Stream.generate(() -> TenantTestFixture.randomTenant(t -> {
              }))
              .limit(5)
              .peek(tenant -> repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId()))
              .collect(Collectors.toList());
      Collections.shuffle(tenants);
      multicastDnsProxyServiceProfileApTenantIds =
          tenants.stream()
              .limit(3)
              .map(VenueTestFixture::randomVenue)
              .peek(venue -> repositoryUtil.createOrUpdate(
                  venue, venue.getTenant().getId(), randomTxId()))
              .map(ApGroupTestFixture::randomApGroup)
              .peek(apGroup -> repositoryUtil.createOrUpdate(
                  apGroup, apGroup.getTenant().getId(), randomTxId()))
              .map(ApTestFixture::randomAp)
              .peek(ap -> repositoryUtil.createOrUpdate(
                  ap, ap.getTenant().getId(), randomTxId()))
              .map(
                  ap -> {
                    multicastDnsProxyServiceProfile =
                        MulticaseDnsProxyTestFixture.randomMulticastDnsProxyServiceProfile(
                            ap.getTenant(), p -> {});
                    return new MulticastDnsProxyServiceProfileApGenerator()
                        .setTenant(always(ap.getTenant()))
                        .setAp(always(ap))
                        .setMulticastDnsProxyServiceProfile(always(multicastDnsProxyServiceProfile))
                        .generate();
                  }
              )
              .peek(profile -> {
                repositoryUtil.createOrUpdate(multicastDnsProxyServiceProfile, profile.getTenant().getId(), randomTxId());
                repositoryUtil.createOrUpdate(profile, profile.getTenant().getId(), randomTxId());
              })
              .map(profile -> profile.getTenant().getId())
              .toList();
    }

    @Test
    void thenSendAsyncJobForEachTenant() {
      final var expectedTenantIds = new HashSet<>(
          proxyServiceProfileApRepository.findAllDistinctTenantIds());

      postMigrationConsumer.run(null);

      final var receivedTenantIds = new HashSet<String>();
      final var receivedRequestIds = new HashSet<String>();

      for (int i = 0; i < expectedTenantIds.size(); i++) {
        final var message = receive();
        assertThat(message)
            .isNotNull()
            .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID) != null)
            .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID) != null)
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(WifiAsyncJob::getJobCase)
            .isEqualTo(JobCase.MULTICAST_DNS_PROXY_SERVICE_PROFILE_POST_MIGRATION_JOB);

        receivedTenantIds.add(
            new String(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID).value()));

        receivedRequestIds.add(
            new String(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID).value()));
      }

      assertThat(receivedTenantIds)
          .isNotEmpty()
          .hasSameElementsAs(expectedTenantIds)
          .containsAll(multicastDnsProxyServiceProfileApTenantIds);

      assertThat(receivedRequestIds).isNotEmpty().hasSize(1);
    }

    private KafkaProtoMessage<WifiAsyncJob> receive() {
      return messageUtil.receive(
          kafkaTopicProvider.getWifiAsyncJob(),
          data -> {
            try {
              return WifiAsyncJob.parseFrom(data);
            } catch (InvalidProtocolBufferException e) {
              throw new RuntimeException(e);
            }
          });
    }
  }
}
