package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.BIND_ROGUE_AP_POLICY_PROFILE_TEMPLATE_TO_VENUES;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_ROGUE_AP_POLICY_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UNBIND_ROGUE_AP_POLICY_PROFILE_TEMPLATE_FROM_VENUES;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_ROGUE_AP_POLICY_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_VENUE_TEMPLATE_ROGUE_AP;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.ROGUE_AP_POLICY_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.service.impl.RogueApPolicyProfileServiceCtrlImpl.DEFAULT_ROGUE_AP_POLICY_NAME;
import static com.ruckus.cloud.wifi.service.impl.RogueApPolicyProfileServiceCtrlImpl.NONPERSISTED_DEFAULT_POLICY_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture.extractVenues;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RogueApPolicy;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.venue.proto.Action;
import com.ruckus.cloud.venue.proto.VenueEvent;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

@Slf4j
@WifiIntegrationTest
public class ConsumeRogueApPolicyProfileTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  void addRogueClassificationPolicy(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var myPolicyId = randomId();

    Venue v = createVenue(tenant, "v", true);
    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    p.setId(myPolicyId);
    applyVenue(p, v);

    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p));

    List<RogueClassificationPolicy> policies = getRogueApPolicyProfileTemplates();
    assertEquals(2, policies.size());
    RogueClassificationPolicy defaultRogueClassificationPolicy = policies.stream()
        .filter(o -> DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .orElseThrow();
    assertNotNull(defaultRogueClassificationPolicy);
    assertNotEquals(NONPERSISTED_DEFAULT_POLICY_ID,
        defaultRogueClassificationPolicy.getId());
    RogueClassificationPolicy rogueClassificationPolicy = policies.stream()
        .filter(o -> !DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .orElseThrow();
    assertEquals(myPolicyId, rogueClassificationPolicy.getId());
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());

    VenueRogueAp venueRogueAp = getVenueRogueAp(v.getId(), true);
    assertTrue(venueRogueAp.getEnabled());
    assertEquals(myPolicyId, venueRogueAp.getRoguePolicyId());

    var rogueOperations = receiveRoguePolicyOperations(1, tenantId);
    assertAll("assert rogue publisher",
        () -> assertRoguePolicyPublisher(rogueOperations, 1),
        () -> assertRoguePolicyPublisher(rogueOperations, v.getId(), rogueClassificationPolicy.getId())
    );
    assertRoguePolicyNotSent(tenantId); // make sure no more

    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccmPublisher(ddccmOperations, 1),
        () -> assertDdccmPublisherVenue(ddccmOperations, v.getId(), true, 0)
    );

    // 2 RoguePolicy, 1 Venue
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 3),
        () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
            OpType.ADD, defaultRogueClassificationPolicy.getId(), DEFAULT_ROGUE_AP_POLICY_NAME, 0.0),
        () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
            OpType.ADD, rogueClassificationPolicy.getId(), "Rogue Policy", 1.0),
        () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
            OpType.MOD, v.getId(), rogueClassificationPolicy.getId())
    );

    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, tenantId);
  }

  @Test
  public void addDefaultRogueClassificationPolicy(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // test a non-pure default rogue policy
    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy(DEFAULT_ROGUE_AP_POLICY_NAME);
    Venue v = createVenue(tenant, "v", true);
    applyVenue(p, v);

    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p));

    assertEquals(0, getRogueApPolicyProfileTemplates().size());

    assertActivityStatusFail(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, Errors.WIFI_10192, tenantId);

    // test a pure default rogue policy
    p = new RogueClassificationPolicy();
    p.setName(DEFAULT_ROGUE_AP_POLICY_NAME);

    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p));

    assertEquals(1, getRogueApPolicyProfileTemplates().size());

    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, tenantId);
  }

  @Test
  public void updateRogueClassificationPolicy(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v1 = createVenue(tenant, "v1", true);
    Venue v2 = createVenue(tenant, "v2", true);
    String venueId1 = v1.getId();
    String venueId2 = v2.getId();

    // set p1 to v1
    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    applyVenue(p, v1);

    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p));

    List<RogueClassificationPolicy> policies = getRogueApPolicyProfileTemplates();
    assertEquals(2, policies.size());
    RogueClassificationPolicy rogueClassificationPolicy = policies.stream()
        .filter(o -> !DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .orElseThrow();
    String roguePolicyId = rogueClassificationPolicy.getId();
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());
    assertEquals(venueId1, extractVenues(rogueClassificationPolicy).get(0).getId());

    // update policy p1
    {
      // set id
      p.setId(roguePolicyId);
      // remove a rule
      p.getRules().remove(0);

      RequestParams rps = new RequestParams().addPathVariable("roguePolicyTemplateId", roguePolicyId);
      sendWifiCfgRequest(
          tenantId, randomTxId(), CfgAction.UPDATE_ROGUE_AP_POLICY_PROFILE_TEMPLATE, userName,
          rps, map(p));

      List<RogueClassificationPolicy> policies2 = getRogueApPolicyProfileTemplates();
      assertEquals(2, policies2.size());
      RogueClassificationPolicy rogueClassificationPolicy2 = policies2.stream()
          .filter(o -> roguePolicyId.equals(o.getId()))
          .findAny()
          .orElseThrow();
      assertEquals("Rogue Policy", rogueClassificationPolicy2.getName());
      assertEquals(1, rogueClassificationPolicy2.getRules().size());
      assertEquals(venueId1, extractVenues(rogueClassificationPolicy2).get(0).getId());

      var rogueOperations = receiveRoguePolicyOperations(1, tenantId);
      assertAll("assert rogue publisher",
          () -> assertRoguePolicyPublisher(rogueOperations, 1),
          () -> assertRoguePolicyPublisher(rogueOperations, venueId1, roguePolicyId)
      );
      assertRoguePolicyNotSent(tenantId); // make sure no more

      // 1 RoguePolicy 1 venue
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 2),
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.MOD, roguePolicyId, "Rogue Policy", 1.0)
      );

      assertActivityStatusSuccess(UPDATE_ROGUE_AP_POLICY_PROFILE_TEMPLATE, tenantId);
      clearMessage();
    }

    // change p1 to v2
    {
      // set id
      p.setId(roguePolicyId);
      // change applied venues
      p.setRogueClassificationPolicyVenues(null);
      applyVenue(p, v2);

      String requestId2 = randomTxId();
      RequestParams rps = new RequestParams().addPathVariable("roguePolicyTemplateId", roguePolicyId);
      sendWifiCfgRequest(
          tenantId, requestId2, CfgAction.UPDATE_ROGUE_AP_POLICY_PROFILE_TEMPLATE, userName,
          rps, map(p));

      List<RogueClassificationPolicy> policies2 = getRogueApPolicyProfileTemplates();
      assertEquals(2, policies2.size());
      RogueClassificationPolicy defaultRogueClassificationPolicy = policies2.stream()
          .filter(o -> DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
          .findAny()
          .orElseThrow();
      String defaultRoguePolicyId = defaultRogueClassificationPolicy.getId();
      RogueClassificationPolicy rogueClassificationPolicy2 = policies2.stream()
          .filter(o -> roguePolicyId.equals(o.getId()))
          .findAny()
          .orElseThrow();
      assertEquals("Rogue Policy", rogueClassificationPolicy2.getName());
      assertEquals(1, rogueClassificationPolicy2.getRules().size());
      assertEquals(venueId2, extractVenues(rogueClassificationPolicy2).get(0).getId());

      var rogueOperations = receiveRoguePolicyOperations(2, tenantId);
      assertAll("assert rogue publisher",
          () -> assertRoguePolicyPublisher(rogueOperations, 2),
          () -> assertRoguePolicyPublisher(rogueOperations, venueId1, defaultRoguePolicyId),
          () -> assertRoguePolicyPublisher(rogueOperations, venueId2, roguePolicyId)
      );
      assertRoguePolicyNotSent(tenantId); // make sure no more

      // v1 go back to default but ccm zone.gpb doesn't need change for rogueEnabled and rogueApReportThreshold
      // v2 update to the policy
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 1),
          () -> assertDdccmPublisherVenue(ddccmOperations, venueId2, true, 0)
      );

      // 2 RoguePolicy, 2 Venue
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 4),
          // v1 go back to default
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.MOD, defaultRogueClassificationPolicy.getId(), DEFAULT_ROGUE_AP_POLICY_NAME,
              1.0),
          // v2 update to the policy
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.MOD, rogueClassificationPolicy.getId(), "Rogue Policy", 1.0),
          // check v1
          () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
              OpType.MOD, venueId1, defaultRogueClassificationPolicy.getId()),
          // check v2
          () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
              OpType.MOD, venueId2, roguePolicyId)
      );

      assertActivityStatusSuccess(UPDATE_ROGUE_AP_POLICY_PROFILE_TEMPLATE, tenantId);
      clearMessage();
    }

    // change v2 from p1 to p2
    {
      RogueClassificationPolicy p2 = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy2");
      applyVenue(p2, v2);

      edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p2));

      List<RogueClassificationPolicy> policies2 = getRogueApPolicyProfileTemplates();
      assertEquals(3, policies2.size());
      RogueClassificationPolicy rogueClassificationPolicy2 = policies2.stream()
          .filter(o -> !DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
          .filter(o -> !roguePolicyId.equals(o.getId()))
          .findAny()
          .orElseThrow();
      assertEquals("Rogue Policy2", rogueClassificationPolicy2.getName());
      assertEquals(2, rogueClassificationPolicy2.getRules().size());
      assertEquals(venueId2, extractVenues(rogueClassificationPolicy2).get(0).getId());

      var rogueOperations = receiveRoguePolicyOperations(1, tenantId);
      assertAll("assert rogue publisher",
          () -> assertRoguePolicyPublisher(rogueOperations, 1),
          () -> assertRoguePolicyPublisher(rogueOperations, venueId2, rogueClassificationPolicy2.getId())
      );
      assertRoguePolicyNotSent(tenantId); // make sure no more

      assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, tenantId);
      clearMessage();
    }
  }

  private void assertRoguePolicyPublisher(
      List<RogueApPolicy> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertRoguePolicyPublisher(List<RogueApPolicy> operations,
      String venueId, String roguePolicyId) {
    RogueApPolicy rogueApPolicy = operations.stream()
        .filter(o -> venueId.equals(o.getVenueId()))
        .findAny()
        .orElseThrow();
    assertEquals(roguePolicyId, rogueApPolicy.getId());
  }

  @Test
  public void deleteRogueClassificationPolicy(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p));

    List<RogueClassificationPolicy> policies = getRogueApPolicyProfileTemplates();
    assertEquals(1, policies.size());
    RogueClassificationPolicy rogueClassificationPolicy = policies.get(0);
    String roguePolicyId = rogueClassificationPolicy.getId();
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());

    // update

    String requestId2 = randomTxId();
    RequestParams rps = new RequestParams().addPathVariable("roguePolicyTemplateId", roguePolicyId);
    sendWifiCfgRequest(
        tenantId, requestId2, CfgAction.DELETE_ROGUE_AP_POLICY_PROFILE_TEMPLATE, userName,
        rps, "");

    List<RogueClassificationPolicy> policies2 = getRogueApPolicyProfileTemplates();
    assertEquals(0, policies2.size());

    assertRoguePolicyNotSent(tenantId);

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 1),
        () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
            OpType.DEL, roguePolicyId, null, null)
    );

    assertActivityStatusSuccess(DELETE_ROGUE_AP_POLICY_PROFILE_TEMPLATE, tenantId);
  }

  @Test
  public void bindRogueApPolicyProfileToVenues(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v1 = createVenue(tenant, "v1", true);
    Venue v2 = createVenue(tenant, "v2", true);
    String venueId1 = v1.getId();
    String venueId2 = v2.getId();

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    applyVenue(p, v1);
    applyVenue(p, v2);
    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p));

    List<RogueClassificationPolicy> policies = getRogueApPolicyProfileTemplates();
    assertEquals(2, policies.size());
    RogueClassificationPolicy defaultRogueClassificationPolicy = policies.stream()
        .filter(o -> DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .get();
    String defaultRoguePolicyId = defaultRogueClassificationPolicy.getId();
    RogueClassificationPolicy rogueClassificationPolicy = policies.stream()
        .filter(o -> !DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .get();
    String roguePolicyId = rogueClassificationPolicy.getId();
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());
    assertEquals(2, extractVenues(rogueClassificationPolicy).size());

    // update

    String requestId2 = randomTxId();
    RequestParams rps = new RequestParams().addPathVariable("roguePolicyTemplateId", defaultRoguePolicyId);
    sendWifiCfgRequest(
        tenant.getId(), requestId2, CfgAction.BIND_ROGUE_AP_POLICY_PROFILE_TEMPLATE_TO_VENUES, userName,
        rps, List.of(venueId1, venueId2));

    List<RogueClassificationPolicy> policies2 = getRogueApPolicyProfileTemplates();
    assertEquals(2, policies2.size());
    RogueClassificationPolicy defaultRogueClassificationPolicy2 = policies2.stream()
        .filter(o -> DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .orElseThrow();
    assertEquals(2, extractVenues(defaultRogueClassificationPolicy2).size());
    RogueClassificationPolicy rogueClassificationPolicy2 = policies2.stream()
        .filter(o -> roguePolicyId.equals(o.getId()))
        .findAny()
        .orElseThrow();
    assertEquals("Rogue Policy", rogueClassificationPolicy2.getName());
    assertEquals(2, rogueClassificationPolicy2.getRules().size());
    assertTrue(CollectionUtils.isEmpty(extractVenues(rogueClassificationPolicy2)));

    var rogueOperations = receiveRoguePolicyOperations(2, tenantId);
    assertAll("assert rogue publisher",
        () -> assertRoguePolicyPublisher(rogueOperations, 2),
        () -> assertRoguePolicyPublisher(rogueOperations, venueId1, defaultRoguePolicyId),
        () -> assertRoguePolicyPublisher(rogueOperations, venueId2, defaultRoguePolicyId)
    );
    // make sure no more
    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getRoguePolicy());

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 4),
        // check v1
        () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
            OpType.MOD, defaultRoguePolicyId, DEFAULT_ROGUE_AP_POLICY_NAME, 2.0),
        () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
            OpType.MOD, roguePolicyId, "Rogue Policy", 0.0),
        () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
            OpType.MOD, venueId1, defaultRoguePolicyId)
    );

    assertActivityStatusSuccess(BIND_ROGUE_AP_POLICY_PROFILE_TEMPLATE_TO_VENUES, tenantId);
  }

  @Test
  public void unbindRogueApPolicyProfileFromVenues(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v1 = createVenue(tenant, "v1", true);
    Venue v2 = createVenue(tenant, "v2", true);
    String venueId1 = v1.getId();
    String venueId2 = v2.getId();

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    applyVenue(p, v1);
    applyVenue(p, v2);
    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p));

    List<RogueClassificationPolicy> policies = getRogueApPolicyProfileTemplates();
    assertEquals(2, policies.size());
    RogueClassificationPolicy rogueClassificationPolicy = policies.stream()
        .filter(o -> !DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .orElseThrow();
    String roguePolicyId = rogueClassificationPolicy.getId();
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());
    assertEquals(2, extractVenues(rogueClassificationPolicy).size());

    // update

    String requestId2 = randomTxId();
    RequestParams rps = new RequestParams().addPathVariable("roguePolicyTemplateId", roguePolicyId);
    sendWifiCfgRequest(
        tenant.getId(), requestId2, CfgAction.UNBIND_ROGUE_AP_POLICY_PROFILE_TEMPLATE_FROM_VENUES, userName,
        rps, List.of(venueId1, venueId2));

    List<RogueClassificationPolicy> policies2 = getRogueApPolicyProfileTemplates();
    assertEquals(2, policies2.size());
    RogueClassificationPolicy defaultRogueClassificationPolicy = policies2.stream()
        .filter(o -> DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .orElseThrow();
    String defaultRoguePolicyId = defaultRogueClassificationPolicy.getId();
    RogueClassificationPolicy rogueClassificationPolicy2 = policies2.stream()
        .filter(o -> roguePolicyId.equals(o.getId()))
        .findAny()
        .orElseThrow();
    assertEquals("Rogue Policy", rogueClassificationPolicy2.getName());
    assertEquals(2, rogueClassificationPolicy2.getRules().size());
    assertTrue(CollectionUtils.isEmpty(extractVenues(rogueClassificationPolicy2)));

    var rogueOperations = receiveRoguePolicyOperations(2, tenantId);
    assertAll("assert rogue publisher",
        () -> assertRoguePolicyPublisher(rogueOperations, 2),
        // v1 go back to default
        () -> assertRoguePolicyPublisher(rogueOperations, venueId1, defaultRoguePolicyId),
        // v2 go back to default
        () -> assertRoguePolicyPublisher(rogueOperations, venueId2, defaultRoguePolicyId)
    );
    // make sure no more
    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getRoguePolicy());

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 4),
        () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
            OpType.MOD, defaultRoguePolicyId, DEFAULT_ROGUE_AP_POLICY_NAME, 2.0),
        () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
            OpType.MOD, roguePolicyId, "Rogue Policy", 0.0),
        () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
            OpType.MOD, venueId1, defaultRoguePolicyId),
        () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
            OpType.MOD, venueId2, defaultRoguePolicyId)
    );

    assertActivityStatusSuccess(UNBIND_ROGUE_AP_POLICY_PROFILE_TEMPLATE_FROM_VENUES, tenantId);
  }

  @Test
  public void eda_venueRogueAp(Tenant tenant) {
    clearMessageBeforeEachEdaOperation = false;

    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v1 = createVenue(tenant, "v1", true);
    Venue v2 = createVenue(tenant, "v2", true);
    String venueId1 = v1.getId();
    String venueId2 = v2.getId();

    // create a real default policy and set it to v1
    VenueRogueAp defaultVenueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();
    edaUpdateVenueTemplateRogueAp(tenantId, userName, venueId1, map(defaultVenueRogueAp));

    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_ROGUE_AP, tenantId);

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p));

    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, tenantId);

    List<RogueClassificationPolicy> policies = getRogueApPolicyProfileTemplates();
    assertEquals(2, policies.size());
    RogueClassificationPolicy defaultRogueClassificationPolicy = policies.stream()
        .filter(o -> DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .orElseThrow();
    String defaultRoguePolicyId = defaultRogueClassificationPolicy.getId();
    RogueClassificationPolicy rogueClassificationPolicy = policies.stream()
        .filter(o -> !DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .orElseThrow();
    String roguePolicyId = rogueClassificationPolicy.getId();
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());
    assertTrue(extractVenues(rogueClassificationPolicy).isEmpty());

    {
      var viewmodelOperations = receiveViewmodelCollectorOperations(2, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 3),
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.ADD, defaultRoguePolicyId, DEFAULT_ROGUE_AP_POLICY_NAME, 1.0),
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.ADD, roguePolicyId, "Rogue Policy", 0.0),
          // wifi-c workflow: no trigger because TriggeredFromProfilePredicate. but workflow will update it.
          // wifi-eda should trigger to update it.
          () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
              OpType.MOD, venueId1, defaultRoguePolicyId)
      );
    }

    // reset
    clearMessage();

    // set default to v2
    defaultVenueRogueAp.setRoguePolicyId(defaultRoguePolicyId);
    edaUpdateVenueTemplateRogueAp(tenantId, userName, venueId2, map(defaultVenueRogueAp));

    {
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 2),
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.MOD, defaultRoguePolicyId, DEFAULT_ROGUE_AP_POLICY_NAME, 2.0),
          () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
              OpType.MOD, venueId2, defaultRoguePolicyId)
      );
    }

    // reset
    clearMessage();

    // disable v2 rogueAp
    VenueRogueAp disableVenueRogueAp = new VenueRogueAp();
    disableVenueRogueAp.setEnabled(false);
    edaUpdateVenueTemplateRogueAp(tenantId, userName, venueId2, map(disableVenueRogueAp));

    {
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 2),
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.MOD, defaultRoguePolicyId, DEFAULT_ROGUE_AP_POLICY_NAME, 1.0),
          () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
              OpType.MOD, venueId2, "")
      );
    }

    // reset for update
    clearMessage();

    // apply rogue-policy to v1 and v2
    log.info("====== apply rogue-policy[{}] to v1 and v2 =====", roguePolicyId);
    VenueRogueAp venueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();
    venueRogueAp.setRoguePolicyId(roguePolicyId);
    edaUpdateVenueTemplateRogueAp(tenantId, userName, venueId1, map(venueRogueAp));
    edaUpdateVenueTemplateRogueAp(tenantId, userName, venueId2, map(venueRogueAp));

    List<RogueClassificationPolicy> policies2 = getRogueApPolicyProfileTemplates();
    assertEquals(2, policies2.size());
    RogueClassificationPolicy defaultRogueClassificationPolicy2 = policies2.stream()
        .filter(o -> DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
        .findAny()
        .orElseThrow();
    assertTrue(extractVenues(defaultRogueClassificationPolicy2).isEmpty());
    RogueClassificationPolicy rogueClassificationPolicy2 = policies2.stream()
        .filter(o -> roguePolicyId.equals(o.getId()))
        .findAny()
        .orElseThrow();
    assertEquals("Rogue Policy", rogueClassificationPolicy2.getName());
    assertEquals(2, rogueClassificationPolicy2.getRules().size());
    assertEquals(2, extractVenues(rogueClassificationPolicy2).size());

    {
      var rogueOperations = receiveRoguePolicyOperations(2, tenantId);
      assertAll("assert rogue publisher",
          () -> assertRoguePolicyPublisher(rogueOperations, 2),
          () -> assertRoguePolicyPublisher(rogueOperations, venueId1, roguePolicyId),
          () -> assertRoguePolicyPublisher(rogueOperations, venueId2, roguePolicyId)
      );
      assertRoguePolicyNotSent(tenantId); // make sure no more

      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      // v2 from disable to enable
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 1),
          () -> assertDdccmPublisherVenue(ddccmOperations, venueId2, true, 80)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(2, tenantId);
      // roguePolicyId: 0 -> 1 -> 2
      // defaultRoguePolicyId: 2 -> 1 -> 0
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 5),
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.MOD, roguePolicyId, "Rogue Policy", 2.0),
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.MOD, defaultRoguePolicyId, DEFAULT_ROGUE_AP_POLICY_NAME, 0.0),
          () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
              OpType.MOD, venueId1, roguePolicyId),
          () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
              OpType.MOD, venueId2, roguePolicyId)
      );
    }

    clearMessage();

    // delete v1,v2

    VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(tenantId)
        .addOperation(com.ruckus.cloud.venue.proto.Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(venueId1)
                    .setIsTemplate(true)
                    .build()))
        .addOperation(com.ruckus.cloud.venue.proto.Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(venueId2)
                    .setIsTemplate(true)
                    .build()))
        .build();

    sendVenueCfgChange(tenantId, randomTxId(), event);

    {
      var rogueOperations = receiveRoguePolicyOperations(2, tenantId);
      assertAll("assert rogue publisher",
          () -> assertRoguePolicyPublisher(rogueOperations, 2),
          () -> assertRoguePolicyPublisher(rogueOperations, venueId1, roguePolicyId),
          () -> assertRoguePolicyPublisher(rogueOperations, venueId2, roguePolicyId)
      );
      assertRoguePolicyNotSent(tenantId); // make sure no more

      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 4)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 1),
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.MOD, roguePolicyId, "Rogue Policy", 0.0)
      );
    }
  }

  @Test
  public void validateRogueClassificationPolicyMaxCounts(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    /**
     * default:
     *   rogue-ap-policy-profile:
     *     tenant-max-count: 3
     *     rule-max-count: 2
     *     bind-max-count: 2
     */

    Venue v1 = createVenue(tenant, "v1", true);
    Venue v2 = createVenue(tenant, "v2", true);
    Venue v3 = createVenue(tenant, "v3", true);

    // test rule-max-count
    RogueClassificationPolicy p1 = RogueApPolicyTestFixture.rogueApPolicy("p1", 3);
    // test bind-max-count
    RogueClassificationPolicy p2 = RogueApPolicyTestFixture.rogueApPolicy("p2", 1);
    applyVenue(p2, v1);
    applyVenue(p2, v2);
    applyVenue(p2, v3);
    // tenant-max-count
    RogueClassificationPolicy p3 = RogueApPolicyTestFixture.rogueApPolicy("p3", 2);
    RogueClassificationPolicy p4 = RogueApPolicyTestFixture.rogueApPolicy("p4", 1);
    RogueClassificationPolicy p5 = RogueApPolicyTestFixture.rogueApPolicy("p5", 1);
    RogueClassificationPolicy p6 = RogueApPolicyTestFixture.rogueApPolicy("p6", 1);

    // test rule-max-count

    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p1));

    assertEquals(0, getRogueApPolicyProfileTemplates().size());

    assertNoMessages(tenantId,
        kafkaTopicProvider.getRoguePolicy(),
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    assertActivityStatusFail(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, Errors.WIFI_10194, tenantId);

    // test bind-max-count

    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p2));

    assertEquals(0, getRogueApPolicyProfileTemplates().size());

    assertNoMessages(tenantId,
        kafkaTopicProvider.getRoguePolicy(),
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    assertActivityStatusFail(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, Errors.WIFI_10195, tenantId);

    // test tenant-max-count

    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p3));
    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p4));
    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p5));
    edaAddRogueApPolicyProfileTemplate(tenantId, userName, map(p6));

    assertEquals(3, getRogueApPolicyProfileTemplates().size());

    assertNoMessages(tenantId,
        kafkaTopicProvider.getRoguePolicy(),
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    assertActivityStatusFail(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, Errors.WIFI_10193, tenantId);
  }

  private static void assertViewmodelCollector(
      List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private static void assertViewmodelCollectorRogueApPolicy(
      List<Operations> operations, OpType opType, String roguePolicyId, String roguePolicyName, Double scope) {
    assertTrue(operations.stream()
        .filter(o -> ROGUE_AP_POLICY_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .filter(o -> roguePolicyId.equals(o.getId()))
        .filter(o -> o.getOpType() == opType)
        .filter(o -> o.getOpType() == OpType.DEL || true == o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())
        .filter(o -> roguePolicyName == null || roguePolicyName.equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
        .filter(o -> scope == null || o.getDocMap().get(EsConstants.Key.VENUE_IDS).getListValue().getValuesCount() == scope)
        .findAny().isPresent());
  }

  private static void assertViewmodelCollectorVenueRogueAp(
      List<Operations> operations, OpType opType, String venueId, String roguePolicyId) {
    Operations venue = operations.stream()
        .filter(o -> venueId.equals(o.getId()))
        .findAny()
        .orElseThrow();
    assertEquals(EsConstants.Index.VENUE, venue.getIndex());
    assertEquals(opType, venue.getOpType());
    assertEquals(roguePolicyId, venue.getDocMap().get("rogueDetection").getStructValue()
        .getFieldsOrThrow("policyId").getStringValue());
  }

  private static void assertDdccmPublisher(
      List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private static void assertDdccmPublisherVenue(
      List<Operation> operations, String venueId, boolean rogueEnabled, int threshold) {
    assertTrue(operations.stream()
        .filter(Operation::hasVenue)
        .map(Operation::getVenue)
        .anyMatch(v -> venueId.equals(v.getId()) &&
            rogueEnabled == v.getRogueEnabled() &&
            threshold == v.getRogueApReportThreshold().getValue()));
  }

}
