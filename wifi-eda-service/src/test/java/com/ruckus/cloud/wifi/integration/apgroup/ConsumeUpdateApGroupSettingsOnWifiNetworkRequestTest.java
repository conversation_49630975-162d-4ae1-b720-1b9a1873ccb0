package com.ruckus.cloud.wifi.integration.apgroup;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.venueWifiNetworkApGroupSettings;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RadioType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanApGroup;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.VenueWifiNetworkApGroupSettingsGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkApGroupSettings;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.DpskNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.fixture.DPSKNetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.Condition;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("ApGroupTest")
@WifiIntegrationTest
class ConsumeUpdateApGroupSettingsOnWifiNetworkRequestTest {

  private final VenueWifiNetworkApGroupSettingsGenerator generator = venueWifiNetworkApGroupSettings()
      .setRadioTypes(list(options(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz), 2));

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class ConsumeUpdateApGroupSettingsOnOpenNetwork {

    private String venueId;
    private String networkId;
    private String networkVenueId;
    private String apGroupId;

    @BeforeEach
    void givenOpenNetworkActivatedOnAloneApGroupPersistedInDb(Tenant tenant, Venue venue,
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork network, NetworkVenue networkVenue) {
      venueId = venue.getId();
      networkId = network.getId();
      networkVenueId = networkVenue.getId();

      final var apGroup = repositoryUtil.createOrUpdate(Generators.apGroup()
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
      apGroupId = apGroup.getId();

      final var networkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(networkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(options(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._6_GHz))
          .generate(2)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId)
          .addPathVariable("apGroupId", apGroupId);
    }

    @Payload
    private VenueWifiNetworkApGroupSettings payload() {
      return generator.generate();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload VenueWifiNetworkApGroupSettings payload) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities())
          .isNotEmpty().hasSize(3).extracting(TxEntity::getEntity)
          .haveExactly(3, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getModifiedEntities())
          .isNotEmpty()
          .filteredOn(e -> !(e.getModifiedProperties().size() == 1 &&
              e.getModifiedProperties().contains("updatedDate")))
          .hasSize(1).extracting(TxEntity::getEntity)
          .haveExactly(1, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(1).extracting(TxEntity::getEntity)
          .haveExactly(1, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, networkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().hasSize(1)
          .singleElement()
          .satisfies(networkApGroup ->
              assertThat(networkApGroup.getApGroup().getId()).isEqualTo(apGroupId))
          .extracting(NetworkApGroup::getNetworkApGroupRadios,
              InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
          .isNotNull().hasSize(4)
          .allSatisfy(nagr -> assertThat(nagr.getVlanId()).isEqualTo(payload.getVlanId()))
          .extracting(NetworkApGroupRadio::getRadio)
          .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(5)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.ADD)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of ADD WlanApGroup operations should be [%d]", 3)
                .hasSize(3)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getWlanVenueId()).isEqualTo(networkVenueId);
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                })
                .extracting(WlanApGroup::getRadio)
                .containsExactlyInAnyOrder(RadioType.RADIO50, RadioType.RADIO50_LOWER,
                    RadioType.RADIO50_UPPER);
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.MODIFY)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of MODIFY WlanApGroup operations should be [%d]", 1)
                .hasSize(1)
                .singleElement()
                .extracting(Operation::getWlanApGroup)
                .satisfies(wlanApGroup -> {
                  assertThat(wlanApGroup.getWlanVenueId()).isEqualTo(networkVenueId);
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                })
                .extracting(WlanApGroup::getRadio)
                .isEqualTo(RadioType.RADIO24);
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.DELETE)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of DELETE WlanApGroup operations should be [%d]", 1)
                .hasSize(1)
                .singleElement()
                .extracting(Operation::getWlanApGroup)
                .satisfies(wlanApGroup -> {
                  assertThat(wlanApGroup.getWlanVenueId()).isEqualTo(networkVenueId);
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isFalse();
                })
                .extracting(WlanApGroup::getRadio)
                .isEqualTo(RadioType.RADIO60);
          });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(1)
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getId()).isNotEmpty();
            assertThat(op.getIndex()).isEqualTo(Index.NETWORK_DEVICE_GROUP_MAPPING);
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocMap())
                .containsEntry(Key.TENANT_ID,
                    ValueUtils.stringValue(txCtx.getTenant()))
                .containsEntry(Key.NETWORK_ID, ValueUtils.stringValue(networkId))
                .containsEntry(Key.DEVICE_GROUP_ID, ValueUtils.stringValue(apGroupId))
                .containsEntry(Key.IS_ALL_AP_GROUPS, ValueUtils.boolValue(true));
          });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(
                ApiFlowNames.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }
  }

  @Nested
  class ConsumeUpdateApGroupSettingsOnOpenNetworkWithVlanPool {

    private String venueId;
    private String networkId;
    private String networkVenueId;
    private String apGroupId;
    private String vlanPoolId;

    @BeforeEach
    void givenOpenNetworkActivatedOnAloneApGroupPersistedInDb(Tenant tenant, Venue venue,
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork network, NetworkVenue networkVenue,
        VlanPool vlanPool) {
      venueId = venue.getId();
      networkId = network.getId();
      networkVenueId = networkVenue.getId();
      vlanPoolId = vlanPool.getId();

      final var apGroup = repositoryUtil.createOrUpdate(Generators.apGroup()
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
      apGroupId = apGroup.getId();

      final var networkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(networkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(options(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._6_GHz))
          .setVlanPool(always(vlanPool))
          .generate(2)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId)
          .addPathVariable("apGroupId", apGroupId);
    }

    @Payload("networkApGroupWithoutVlanId")
    private VenueWifiNetworkApGroupSettings payloadWithoutVlanId() {
      return venueWifiNetworkApGroupSettings()
          .setRadioTypes(list(options(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz), 2))
          .setVlanId(always(null))
          .generate();
    }

    @Payload("networkApGroupWithVlanId")
    private VenueWifiNetworkApGroupSettings payloadWithVlanId() {
      return generator.generate();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK,
        payload = @Payload("networkApGroupWithoutVlanId"))
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("networkApGroupWithoutVlanId") VenueWifiNetworkApGroupSettings payload) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities())
          .isNotEmpty().hasSize(3).extracting(TxEntity::getEntity)
          .haveExactly(3, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getModifiedEntities())
          .isNotEmpty()
          .filteredOn(e -> !(e.getModifiedProperties().size() == 1 &&
              e.getModifiedProperties().contains("updatedDate")))
          .isEmpty();
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(1).extracting(TxEntity::getEntity)
          .haveExactly(1, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, networkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().hasSize(1)
          .singleElement()
          .satisfies(networkApGroup ->
              assertThat(networkApGroup.getApGroup().getId()).isEqualTo(apGroupId))
          .extracting(NetworkApGroup::getNetworkApGroupRadios,
              InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
          .isNotNull().hasSize(4)
          .allSatisfy(nagr -> assertThat(nagr.getVlanId()).isNull())
          .allSatisfy(nagr -> assertThat(nagr.getVlanPool().getId()).isEqualTo(vlanPoolId))
          .extracting(NetworkApGroupRadio::getRadio)
          .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(4)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.ADD)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of ADD WlanApGroup operations should be [%d]", 3)
                .hasSize(3)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getWlanVenueId()).isEqualTo(networkVenueId);
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isFalse();
                  assertThat(wlanApGroup.getVlanPoolId()).isNotNull()
                      .extracting(StringValue::getValue).isEqualTo(vlanPoolId);
                })
                .extracting(WlanApGroup::getRadio)
                .containsExactlyInAnyOrder(RadioType.RADIO50, RadioType.RADIO50_LOWER,
                    RadioType.RADIO50_UPPER);
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.DELETE)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of DELETE WlanApGroup operations should be [%d]", 1)
                .hasSize(1)
                .singleElement()
                .extracting(Operation::getWlanApGroup)
                .satisfies(wlanApGroup -> {
                  assertThat(wlanApGroup.getWlanVenueId()).isEqualTo(networkVenueId);
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isFalse();
                })
                .extracting(WlanApGroup::getRadio)
                .isEqualTo(RadioType.RADIO60);
          });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(1)
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getId()).isNotEmpty();
            assertThat(op.getIndex()).isEqualTo(Index.NETWORK_DEVICE_GROUP_MAPPING);
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocMap())
                .containsEntry(Key.TENANT_ID,
                    ValueUtils.stringValue(txCtx.getTenant()))
                .containsEntry(Key.NETWORK_ID, ValueUtils.stringValue(networkId))
                .containsEntry(Key.DEVICE_GROUP_ID, ValueUtils.stringValue(apGroupId))
                .containsEntry(Key.IS_ALL_AP_GROUPS, ValueUtils.boolValue(true));
          });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(
                ApiFlowNames.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK,
        payload = @Payload("networkApGroupWithVlanId"))
    void thenShouldHandleTheRequestAndDisableVlanPoolSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("networkApGroupWithVlanId") VenueWifiNetworkApGroupSettings payload) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities())
          .isNotEmpty().hasSize(3).extracting(TxEntity::getEntity)
          .haveExactly(3, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getModifiedEntities())
          .isNotEmpty()
          .filteredOn(e -> !(e.getModifiedProperties().size() == 1 &&
              e.getModifiedProperties().contains("updatedDate")))
          .hasSize(1).extracting(TxEntity::getEntity)
          .haveExactly(1, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(1).extracting(TxEntity::getEntity)
          .haveExactly(1, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, networkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().hasSize(1)
          .singleElement()
          .satisfies(networkApGroup ->
              assertThat(networkApGroup.getApGroup().getId()).isEqualTo(apGroupId))
          .extracting(NetworkApGroup::getNetworkApGroupRadios,
              InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
          .isNotNull().hasSize(4)
          .allSatisfy(nagr -> assertThat(nagr.getVlanId()).isEqualTo(payload.getVlanId()))
          .extracting(NetworkApGroupRadio::getRadio)
          .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(5)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.ADD)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of ADD WlanApGroup operations should be [%d]", 3)
                .hasSize(3)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getWlanVenueId()).isEqualTo(networkVenueId);
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                  assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                })
                .extracting(WlanApGroup::getRadio)
                .containsExactlyInAnyOrder(RadioType.RADIO50, RadioType.RADIO50_LOWER,
                    RadioType.RADIO50_UPPER);
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.MODIFY)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of MODIFY WlanApGroup operations should be [%d]", 1)
                .hasSize(1)
                .singleElement()
                .extracting(Operation::getWlanApGroup)
                .satisfies(wlanApGroup -> {
                  assertThat(wlanApGroup.getWlanVenueId()).isEqualTo(networkVenueId);
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                  assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                })
                .extracting(WlanApGroup::getRadio)
                .isEqualTo(RadioType.RADIO24);
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.DELETE)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of DELETE WlanApGroup operations should be [%d]", 1)
                .hasSize(1)
                .singleElement()
                .extracting(Operation::getWlanApGroup)
                .satisfies(wlanApGroup -> {
                  assertThat(wlanApGroup.getWlanVenueId()).isEqualTo(networkVenueId);
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isFalse();
                  assertThat(wlanApGroup.getVlanPoolId().getValue()).isEqualTo(vlanPoolId);
                })
                .extracting(WlanApGroup::getRadio)
                .isEqualTo(RadioType.RADIO60);
          });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(1)
          .singleElement()
          .satisfies(op -> {
            assertThat(op.getId()).isNotEmpty();
            assertThat(op.getIndex()).isEqualTo(Index.NETWORK_DEVICE_GROUP_MAPPING);
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocMap())
                .containsEntry(Key.TENANT_ID,
                    ValueUtils.stringValue(txCtx.getTenant()))
                .containsEntry(Key.NETWORK_ID, ValueUtils.stringValue(networkId))
                .containsEntry(Key.DEVICE_GROUP_ID, ValueUtils.stringValue(apGroupId))
                .containsEntry(Key.IS_ALL_AP_GROUPS, ValueUtils.boolValue(true));
          });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(
                ApiFlowNames.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }
  }

  @Nested
  class ConsumeUpdateApGroupSettingsOnOweTransitionNetwork {

    private String venueId;
    private String oweMasterNetworkId;
    private String oweSlaveNetworkId;
    private String oweMasterNetworkVenueId;
    private String oweSlaveNetworkVenueId;
    private String apGroupId;

    @BeforeEach
    void givenOweTransitionMasterAndSlaveNetworksActivatedOnAloneApGroupPersistedInDb(Tenant tenant, Venue venue,
        @OpenNetwork(wlanSecurity = WlanSecurityEnum.OWETransition, isOweMaster = true)
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork oweMasterNetwork,
        NetworkVenue oweMasterNetworkVenue) {
      venueId = venue.getId();
      oweMasterNetworkId = oweMasterNetwork.getId();
      oweMasterNetworkVenueId = oweMasterNetworkVenue.getId();

      final var apGroup = repositoryUtil.createOrUpdate(Generators.apGroup()
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
      apGroupId = apGroup.getId();

      final var oweMasterNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(oweMasterNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(oweMasterNetworkApGroup))
          .setRadio(options(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._6_GHz))
          .generate(2)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));

      final var oweSlaveNetwork = repositoryUtil.createOrUpdate(
          NetworkTestFixture.randomOpenNetwork(tenant,
              network -> {
                network.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
                network.setOwePairNetworkId(oweMasterNetworkId);
              }), tenant.getId(), randomTxId());
      oweSlaveNetworkId = oweSlaveNetwork.getId();
      oweMasterNetwork.setOwePairNetworkId(oweSlaveNetworkId);
      repositoryUtil.createOrUpdate(oweMasterNetwork, tenant.getId(), randomTxId());
      final var oweSlaveNetworkVenue = repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(oweSlaveNetwork, venue), tenant.getId(),
          randomTxId());
      oweSlaveNetworkVenueId = oweSlaveNetworkVenue.getId();

      final var oweSlaveNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(oweSlaveNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(oweSlaveNetworkApGroup))
          .setRadio(options(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._6_GHz))
          .generate(2)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", oweMasterNetworkId)
          .addPathVariable("apGroupId", apGroupId);
    }

    @Payload
    private VenueWifiNetworkApGroupSettings payload() {
      return generator.generate();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload VenueWifiNetworkApGroupSettings payload) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities())
          .isNotEmpty().hasSize(6).extracting(TxEntity::getEntity)
          .haveExactly(6, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getModifiedEntities())
          .isNotEmpty().filteredOn(e -> !(e.getModifiedProperties().size() == 1 &&
              e.getModifiedProperties().contains("updatedDate")))
          .hasSize(2).extracting(TxEntity::getEntity)
          .haveExactly(2, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(2).extracting(TxEntity::getEntity)
          .haveExactly(2, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, oweMasterNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().hasSize(1)
          .singleElement()
          .satisfies(networkApGroup ->
              assertThat(networkApGroup.getApGroup().getId()).isEqualTo(apGroupId))
          .extracting(NetworkApGroup::getNetworkApGroupRadios,
              InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
          .isNotNull().hasSize(4)
          .allSatisfy(nagr -> assertThat(nagr.getVlanId()).isEqualTo(payload.getVlanId()))
          .extracting(NetworkApGroupRadio::getRadio)
          .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);

      assertThat(repositoryUtil.find(NetworkVenue.class, oweSlaveNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().hasSize(1)
          .singleElement()
          .satisfies(networkApGroup ->
              assertThat(networkApGroup.getApGroup().getId()).isEqualTo(apGroupId))
          .extracting(NetworkApGroup::getNetworkApGroupRadios,
              InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
          .isNotNull().hasSize(4)
          .allSatisfy(nagr -> assertThat(nagr.getVlanId()).isEqualTo(payload.getVlanId()))
          .extracting(NetworkApGroupRadio::getRadio)
          .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(10)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.ADD)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of ADD WlanApGroup operations should be [%d]", 6)
                .hasSize(6)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                })
                .satisfies(wlanApGroups -> {
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweMasterNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .hasSize(3)
                      .extracting(WlanApGroup::getRadio)
                      .containsExactlyInAnyOrder(RadioType.RADIO50, RadioType.RADIO50_LOWER,
                          RadioType.RADIO50_UPPER);
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweSlaveNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .hasSize(3)
                      .extracting(WlanApGroup::getRadio)
                      .containsExactlyInAnyOrder(RadioType.RADIO50, RadioType.RADIO50_LOWER,
                          RadioType.RADIO50_UPPER);
                });
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.MODIFY)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of MODIFY WlanApGroup operations should be [%d]", 2)
                .hasSize(2)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                })
                .satisfies(wlanApGroups -> {
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweMasterNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO24);
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweSlaveNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO24);
                });
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.DELETE)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of DELETE WlanApGroup operations should be [%d]", 1)
                .hasSize(2)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isFalse();
                })
                .satisfies(wlanApGroups -> {
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweMasterNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO60);
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweSlaveNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO60);
                });
          });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(2)
          .allSatisfy(op -> {
            assertThat(op.getId()).isNotEmpty();
            assertThat(op.getIndex()).isEqualTo(Index.NETWORK_DEVICE_GROUP_MAPPING);
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocMap())
                .containsEntry(Key.TENANT_ID,
                    ValueUtils.stringValue(txCtx.getTenant()))
                .containsEntry(Key.DEVICE_GROUP_ID, ValueUtils.stringValue(apGroupId))
                .containsEntry(Key.IS_ALL_AP_GROUPS, ValueUtils.boolValue(true));
          })
          .extracting(ops -> ops.getDocOrThrow(Key.NETWORK_ID))
          .containsExactlyInAnyOrder(
              ValueUtils.stringValue(oweMasterNetworkId),
              ValueUtils.stringValue(oweSlaveNetworkId));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(
                ApiFlowNames.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }
  }

  @Nested
  class ConsumeUpdateApGroupSettingsOnOweTransitionGuestNetwork {

    private String venueId;
    private String oweMasterNetworkId;
    private String oweSlaveNetworkId;
    private String oweMasterNetworkVenueId;
    private String oweSlaveNetworkVenueId;
    private String apGroupId;

    @BeforeEach
    void givenOweTransitionMasterAndSlaveNetworksActivatedOnAloneApGroupPersistedInDb(Tenant tenant, Venue venue,
        @GuestNetwork(wlanSecurity = WlanSecurityEnum.OWETransition, isOweMaster = true)
        com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork oweMasterNetwork,
        NetworkVenue oweMasterNetworkVenue) {
      venueId = venue.getId();
      oweMasterNetworkId = oweMasterNetwork.getId();
      oweMasterNetworkVenueId = oweMasterNetworkVenue.getId();

      final var apGroup = repositoryUtil.createOrUpdate(Generators.apGroup()
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
      apGroupId = apGroup.getId();

      final var oweMasterNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(oweMasterNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(oweMasterNetworkApGroup))
          .setRadio(options(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._6_GHz))
          .generate(2)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));

      var oweSlaveNetwork = NetworkTestFixture.randomGuestOweNetwork(tenant,
          network -> {
            network.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
            network.setOwePairNetworkId(oweMasterNetworkId);
          });
      oweSlaveNetwork.getGuestPortal().setNetwork(oweSlaveNetwork);

      oweSlaveNetwork = repositoryUtil.createOrUpdate(oweSlaveNetwork, tenant.getId(),
          randomTxId());
      oweSlaveNetworkId = oweSlaveNetwork.getId();
      oweMasterNetwork.setOwePairNetworkId(oweSlaveNetworkId);
      repositoryUtil.createOrUpdate(oweMasterNetwork, tenant.getId(), randomTxId());
      final var oweSlaveNetworkVenue = repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(oweSlaveNetwork, venue), tenant.getId(),
          randomTxId());
      oweSlaveNetworkVenueId = oweSlaveNetworkVenue.getId();

      final var oweSlaveNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(oweSlaveNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(oweSlaveNetworkApGroup))
          .setRadio(options(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._6_GHz))
          .generate(2)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", oweMasterNetworkId)
          .addPathVariable("apGroupId", apGroupId);
    }

    @Payload
    private VenueWifiNetworkApGroupSettings payload() {
      return generator.generate();
    }

    @Test
    @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
    @ApiAction(value = CfgAction.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload VenueWifiNetworkApGroupSettings payload) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities())
          .isNotEmpty().hasSize(6).extracting(TxEntity::getEntity)
          .haveExactly(6, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getModifiedEntities())
          .isNotEmpty().filteredOn(e -> !(e.getModifiedProperties().size() == 1 &&
              e.getModifiedProperties().contains("updatedDate")))
          .hasSize(2).extracting(TxEntity::getEntity)
          .haveExactly(2, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(2).extracting(TxEntity::getEntity)
          .haveExactly(2, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, oweMasterNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().hasSize(1)
          .singleElement()
          .satisfies(networkApGroup ->
              assertThat(networkApGroup.getApGroup().getId()).isEqualTo(apGroupId))
          .extracting(NetworkApGroup::getNetworkApGroupRadios,
              InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
          .isNotNull().hasSize(4)
          .allSatisfy(nagr -> assertThat(nagr.getVlanId()).isEqualTo(payload.getVlanId()))
          .extracting(NetworkApGroupRadio::getRadio)
          .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);

      assertThat(repositoryUtil.find(NetworkVenue.class, oweSlaveNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().hasSize(1)
          .singleElement()
          .satisfies(networkApGroup ->
              assertThat(networkApGroup.getApGroup().getId()).isEqualTo(apGroupId))
          .extracting(NetworkApGroup::getNetworkApGroupRadios,
              InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
          .isNotNull().hasSize(4)
          .allSatisfy(nagr -> assertThat(nagr.getVlanId()).isEqualTo(payload.getVlanId()))
          .extracting(NetworkApGroupRadio::getRadio)
          .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(10)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.ADD)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of ADD WlanApGroup operations should be [%d]", 6)
                .hasSize(6)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                })
                .satisfies(wlanApGroups -> {
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweMasterNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .hasSize(3)
                      .extracting(WlanApGroup::getRadio)
                      .containsExactlyInAnyOrder(RadioType.RADIO50, RadioType.RADIO50_LOWER,
                          RadioType.RADIO50_UPPER);
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweSlaveNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .hasSize(3)
                      .extracting(WlanApGroup::getRadio)
                      .containsExactlyInAnyOrder(RadioType.RADIO50, RadioType.RADIO50_LOWER,
                          RadioType.RADIO50_UPPER);
                });
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.MODIFY)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of MODIFY WlanApGroup operations should be [%d]", 2)
                .hasSize(2)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                })
                .satisfies(wlanApGroups -> {
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweMasterNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO24);
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweSlaveNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO24);
                });
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.DELETE)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of DELETE WlanApGroup operations should be [%d]", 1)
                .hasSize(2)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isFalse();
                })
                .satisfies(wlanApGroups -> {
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweMasterNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO60);
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> oweSlaveNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO60);
                });
          });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(2)
          .allSatisfy(op -> {
            assertThat(op.getId()).isNotEmpty();
            assertThat(op.getIndex()).isEqualTo(Index.NETWORK_DEVICE_GROUP_MAPPING);
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocMap())
                .containsEntry(Key.TENANT_ID,
                    ValueUtils.stringValue(txCtx.getTenant()))
                .containsEntry(Key.DEVICE_GROUP_ID, ValueUtils.stringValue(apGroupId))
                .containsEntry(Key.IS_ALL_AP_GROUPS, ValueUtils.boolValue(true));
          })
          .extracting(ops -> ops.getDocOrThrow(Key.NETWORK_ID))
          .containsExactlyInAnyOrder(
              ValueUtils.stringValue(oweMasterNetworkId),
              ValueUtils.stringValue(oweSlaveNetworkId));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(
                ApiFlowNames.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }
  }

  @Nested
  class ConsumeUpdateApGroupSettingsOnDsaeNetwork {

    private String venueId;
    private String dsaeServiceNetworkId;
    private String dsaeOnboardNetworkId;
    private String dsaeServiceNetworkVenueId;
    private String dsaeOnboardNetworkVenueId;
    private String apGroupId;

    @BeforeEach
    void givenOweTransitionMasterAndSlaveNetworksActivatedOnAloneApGroupPersistedInDb(Tenant tenant, Venue venue,
        @DpskNetwork(wlanSecurity = WlanSecurityEnum.WPA23Mixed, isDsaeServiceNetwork = true)
        com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork dsaeServiceNetwork,
        NetworkVenue dsaeServiceNetworkVenue, AuthRadiusService authRadiusService) {
      venueId = venue.getId();
      dsaeServiceNetworkId = dsaeServiceNetwork.getId();
      dsaeServiceNetworkVenueId = dsaeServiceNetworkVenue.getId();

      final var apGroup = repositoryUtil.createOrUpdate(Generators.apGroup()
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
      apGroupId = apGroup.getId();

      final var dsaeServiceNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(dsaeServiceNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(dsaeServiceNetworkApGroup))
          .setRadio(options(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._6_GHz))
          .generate(2)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));

      final var dsaeOnboardNetwork = repositoryUtil.createOrUpdate(
          DPSKNetworkTestFixture.randomDpskNetwork(tenant, authRadiusService,
              network -> {
                network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
                network.setDsaeNetworkPairId(dsaeServiceNetworkId);
              }), tenant.getId(), randomTxId());
      dsaeOnboardNetworkId = dsaeOnboardNetwork.getId();
      dsaeServiceNetwork.setDsaeNetworkPairId(dsaeServiceNetworkId);
      repositoryUtil.createOrUpdate(dsaeServiceNetwork, tenant.getId(), randomTxId());
      final var dsaeOnboardNetworkVenue = repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(dsaeOnboardNetwork, venue), tenant.getId(),
          randomTxId());
      dsaeOnboardNetworkVenueId = dsaeOnboardNetworkVenue.getId();

      final var dsaeOnboardNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(dsaeOnboardNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(dsaeOnboardNetworkApGroup))
          .setRadio(options(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._6_GHz))
          .generate(2)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", dsaeServiceNetworkId)
          .addPathVariable("apGroupId", apGroupId);
    }

    @Payload
    private VenueWifiNetworkApGroupSettings payload() {
      return generator.generate();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload VenueWifiNetworkApGroupSettings payload) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities())
          .isNotEmpty().hasSize(6).extracting(TxEntity::getEntity)
          .haveExactly(6, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getModifiedEntities())
          .isNotEmpty().filteredOn(e -> !(e.getModifiedProperties().size() == 1 &&
              e.getModifiedProperties().contains("updatedDate")))
          .hasSize(2).extracting(TxEntity::getEntity)
          .haveExactly(2, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(2).extracting(TxEntity::getEntity)
          .haveExactly(2, new Condition<>(NetworkApGroupRadio.class::isInstance,
              "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, dsaeServiceNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().hasSize(1)
          .singleElement()
          .satisfies(networkApGroup ->
              assertThat(networkApGroup.getApGroup().getId()).isEqualTo(apGroupId))
          .extracting(NetworkApGroup::getNetworkApGroupRadios,
              InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
          .isNotNull().hasSize(4)
          .allSatisfy(nagr -> assertThat(nagr.getVlanId()).isEqualTo(payload.getVlanId()))
          .extracting(NetworkApGroupRadio::getRadio)
          .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);

      assertThat(repositoryUtil.find(NetworkVenue.class, dsaeOnboardNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().hasSize(1)
          .singleElement()
          .satisfies(networkApGroup ->
              assertThat(networkApGroup.getApGroup().getId()).isEqualTo(apGroupId))
          .extracting(NetworkApGroup::getNetworkApGroupRadios,
              InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
          .isNotNull().hasSize(4)
          .allSatisfy(nagr -> assertThat(nagr.getVlanId()).isEqualTo(payload.getVlanId()))
          .extracting(NetworkApGroupRadio::getRadio)
          .containsExactlyInAnyOrder(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(10)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.ADD)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of ADD WlanApGroup operations should be [%d]", 6)
                .hasSize(6)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                })
                .satisfies(wlanApGroups -> {
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> dsaeServiceNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .hasSize(3)
                      .extracting(WlanApGroup::getRadio)
                      .containsExactlyInAnyOrder(RadioType.RADIO50, RadioType.RADIO50_LOWER,
                          RadioType.RADIO50_UPPER);
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> dsaeOnboardNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .hasSize(3)
                      .extracting(WlanApGroup::getRadio)
                      .containsExactlyInAnyOrder(RadioType.RADIO50, RadioType.RADIO50_LOWER,
                          RadioType.RADIO50_UPPER);
                });
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.MODIFY)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of MODIFY WlanApGroup operations should be [%d]", 2)
                .hasSize(2)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isTrue();
                  assertThat(wlanApGroup.getVlanId()).isNotNull()
                      .extracting(Int32Value::getValue).isEqualTo(payload.getVlanId().intValue());
                })
                .satisfies(wlanApGroups -> {
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> dsaeServiceNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO24);
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> dsaeOnboardNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO24);
                });
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.DELETE)
                .filteredOn(Operation::hasWlanApGroup)
                .describedAs("The count of DELETE WlanApGroup operations should be [%d]", 1)
                .hasSize(2)
                .extracting(Operation::getWlanApGroup)
                .allSatisfy(wlanApGroup -> {
                  assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
                  assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
                  assertThat(wlanApGroup.hasVlanId()).isFalse();
                })
                .satisfies(wlanApGroups -> {
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> dsaeServiceNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO60);
                  assertThat(wlanApGroups)
                      .filteredOn(
                          wlanApGroup -> dsaeOnboardNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                      .singleElement()
                      .extracting(WlanApGroup::getRadio)
                      .isEqualTo(RadioType.RADIO60);
                });
          });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(2)
          .allSatisfy(op -> {
            assertThat(op.getId()).isNotEmpty();
            assertThat(op.getIndex()).isEqualTo(Index.NETWORK_DEVICE_GROUP_MAPPING);
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocMap())
                .containsEntry(Key.TENANT_ID,
                    ValueUtils.stringValue(txCtx.getTenant()))
                .containsEntry(Key.DEVICE_GROUP_ID, ValueUtils.stringValue(apGroupId))
                .containsEntry(Key.IS_ALL_AP_GROUPS, ValueUtils.boolValue(true));
          })
          .extracting(ops -> ops.getDocOrThrow(Key.NETWORK_ID))
          .containsExactlyInAnyOrder(
              ValueUtils.stringValue(dsaeServiceNetworkId),
              ValueUtils.stringValue(dsaeOnboardNetworkId));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(
                ApiFlowNames.UPDATE_AP_GROUP_SETTINGS_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }
  }
}
