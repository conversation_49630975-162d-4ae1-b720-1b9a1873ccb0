package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.events.gpb.Value.KindCase;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.ipv4;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeShort;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelMtuTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.LanPortAdoptionRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileNetworkVenueActivationRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.servicemodel.projection.ApLanPortProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueLanPortProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class SoftGreProfileCmnCfgCollectorOperationBuilderTest {

  @MockBean
  private SoftGreProfileNetworkVenueActivationRepository
      softGreProfileNetworkVenueActivationRepository;

  @MockBean
  private VenueLanPortRepository venueLanPortRepository;

  @MockBean
  private ApLanPortRepository apLanPortRepository;

  @MockBean
  private LanPortAdoptionRepository lanPortAdoptionRepository;

  @MockBean
  private ApRepository apRepository;

  @SpyBean private SoftGreProfileCmnCfgCollectorOperationBuilder unit;

  @Test
  void testGetEntityClass() {
    assertThat(unit.entityClass()).isEqualTo(SoftGreProfile.class);
  }

  @Test
  void testGetIndex() {
    assertThat(unit.index()).isEqualTo(EsConstants.Index.SOFT_GRE_PROFILE_INDEX_NAME);
  }

  @Nested
  class TestBuildConfig {

    private final List<VenueLanPort> givenVenueLanPorts = Generators.venueLanPort()
        .setVenueApModelSpecificAttributes(
            Generators.venueApModelSpecificAttributes().setVenue(Generators.venue())).generate(3);
    private final List<ApLanPort> givenApLanPorts = Generators.apLanPort().generate(3);

    @BeforeEach
    void mockRepositories() {
      var lanPortAdoption1 = new LanPortAdoption();
      lanPortAdoption1.setId("lanPortAdoptionId1");
      var lanPortAdoption2 = new LanPortAdoption();
      lanPortAdoption2.setId("lanPortAdoptionId2");
      doReturn(List.of(lanPortAdoption1, lanPortAdoption2)).when(lanPortAdoptionRepository)
          .findLanPortAdoptionIdsByTenantAndSoftGreProfile(anyString(), anyString());
      doReturn(givenVenueLanPorts).when(venueLanPortRepository)
          .findByTenantIdAndLanPortAdoptionIdIn(anyString(), anyList());
      doReturn(givenApLanPorts).when(apLanPortRepository)
          .findByTenantIdAndLanPortAdoptionIdIn(anyString(), anyList());
      var apLanPortProjections = mockApLanPortProjections(givenApLanPorts);
      doReturn(apLanPortProjections).when(apLanPortRepository)
          .findVenueIdApSerialPortIdByApLanPorts(anyList());
      var venueLanPortProjections = mockVenueLanPortProjections(givenVenueLanPorts);
      doReturn(venueLanPortProjections).when(venueLanPortRepository)
          .findVenueIdModelPortIdApGroupsByVenueLanPorts(anyList());
    }

    @Test
    void testDeleteSoftGreProfile() {
      var operationsList =
          unit.build(
              new DeletedTxEntity<>(Generators.softGreProfile().generate()), emptyTxChanges());
      assertThat(operationsList).hasSize(1).allMatch(operations -> operations.getDocCount() == 0);
    }

    @Test
    void testAddSoftGreProfile(Tenant tenant) {
      var profile = Generators.softGreProfile().setTenant(always(tenant)).generate();

      var operationsList = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(operations -> validate(profile, operations.getDocMap()));
    }

    @Test
    void testModifySoftGreProfile(Tenant tenant) {
      var profile =
          Generators.softGreProfile()
              .setTenant(always(tenant))
              .setDescription(randomString())
              .setSecondaryGateway(ipv4().setRandom(true))
              .setMtuType(always(TunnelMtuTypeEnum.MANUAL))
              .setMtuSize(rangeShort((short) 850, (short) 9018).setRandom(true))
              .generate();

      var operationsList =
          unit.build(new ModifiedTxEntity<>(profile, Set.of("description")), emptyTxChanges());
      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(operations -> validate(profile, operations.getDocMap()));
    }

    @Test
    void testModifySoftGreProfileWithActivations(Tenant tenant) {
      var profile =
          Generators.softGreProfile()
              .setTenant(always(tenant))
              .setDescription(randomString())
              .setSecondaryGateway(ipv4().setRandom(true))
              .setMtuType(always(TunnelMtuTypeEnum.MANUAL))
              .setMtuSize(rangeShort((short) 850, (short) 9018).setRandom(true))
              .generate();
      var network1 = Generators.network(OpenNetwork.class).generate();
      var network2 = Generators.network(OpenNetwork.class).generate();
      var network3 = Generators.network(OpenNetwork.class).generate();
      var venue1 = Generators.venue().generate();
      var venue2 = Generators.venue().generate();
      // N1 - V1
      var activation1 =
          Generators.softGreProfileNetworkVenueActivation(network1, venue1, profile).generate();
      // N2 - V1
      var activation2 =
          Generators.softGreProfileNetworkVenueActivation(network2, venue1, profile).generate();
      // N1 - V2
      var activation3 =
          Generators.softGreProfileNetworkVenueActivation(network1, venue2, profile).generate();
      // N3 - V2
      var activation4 =
          Generators.softGreProfileNetworkVenueActivation(network3, venue2, profile).generate();
      when(softGreProfileNetworkVenueActivationRepository.findByTenantIdAndSoftGreProfileId(
              profile.getTenant().getId(), profile.getId()))
          .thenReturn(List.of(activation1, activation2, activation3, activation4));
      IntStream.range(0, Math.min(givenVenueLanPorts.size(), givenApLanPorts.size()))
          .forEach(i -> givenApLanPorts.get(i)
              .getModelSpecific()
              .getAp()
              .setModel(givenVenueLanPorts.get(i)
                  .getVenueApModelSpecificAttributes()
                  .getModel()));
      var mockAps = givenApLanPorts.stream()
          .map(port -> port.getModelSpecific().getAp())
          .toList();
      when(apRepository.findByApGroupVenueIdAndTenantId(anyString(), anyString()))
          .thenReturn(mockAps);
      when(apLanPortRepository.findBySerialNumbersIn(anyList()))
          .thenReturn(mockAps.stream()
              .map(Ap::getId)
              .toList()
          );

      var operationsList =
          unit.build(new ModifiedTxEntity<>(profile, Set.of("description")), emptyTxChanges());
      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(
              operations ->
                  validate(
                      profile,
                      operations.getDocMap(),
                      activation1,
                      activation2,
                      activation3,
                      activation4));

      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(operations ->
              assertThat(operations.getDocMap())
                  .hasEntrySatisfying(Key.VENUE_ACTIVATIONS, assertVenueActivationsValue(givenVenueLanPorts))
                  .hasEntrySatisfying(Key.AP_ACTIVATIONS, assertApActivationsValue(givenApLanPorts))
          );
    }
  }

  private static Consumer<Value> assertVenueActivationsValue(Collection<VenueLanPort> givenVenueLanPorts) {
    return actualVenueActivationsValue -> {
      assertThat(actualVenueActivationsValue.getKindCase()).isEqualTo(KindCase.LIST_VALUE);
      assertThat(actualVenueActivationsValue.hasListValue()).isTrue();
      assertThat(actualVenueActivationsValue.getListValue()).isNotNull()
          .satisfies(listValue -> {
            assertThat(listValue.getValuesCount()).isEqualTo(givenVenueLanPorts.size());
            assertThat(listValue.getValuesList()).hasSize(givenVenueLanPorts.size())
                .allSatisfy(value -> {
                  assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                  assertThat(value.hasStructValue()).isTrue();
                  assertThat(value.getStructValue()).isNotNull();
                })
                .extracting(Value::getStructValue)
                .containsExactlyInAnyOrderElementsOf(() -> givenVenueLanPorts.stream()
                    .map(venueLanPort -> Struct.newBuilder()
                        .putFields(Key.VENUE_ID, ValueUtils.stringValue(venueLanPort.getVenueApModelSpecificAttributes().getVenue().getId()))
                        .putFields(Key.AP_MODEL, ValueUtils.stringValue(venueLanPort.getVenueApModelSpecificAttributes().getModel()))
                        .putFields(Key.PORT_ID, ValueUtils.stringValue(venueLanPort.getPortId()))
                        .putFields(Key.AP_SERIAL_NUMBERS, ValueUtils.listValue(List.of())).build()).iterator());
          });
    };
  }

  private static Consumer<Value> assertApActivationsValue(Collection<ApLanPort> givenApLanPorts) {
    return actualVenueActivationsValue -> {
      assertThat(actualVenueActivationsValue.getKindCase()).isEqualTo(KindCase.LIST_VALUE);
      assertThat(actualVenueActivationsValue.hasListValue()).isTrue();
      assertThat(actualVenueActivationsValue.getListValue()).isNotNull()
          .satisfies(listValue -> {
            assertThat(listValue.getValuesCount()).isEqualTo(givenApLanPorts.size());
            assertThat(listValue.getValuesList()).hasSize(givenApLanPorts.size())
                .allSatisfy(value -> {
                  assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                  assertThat(value.hasStructValue()).isTrue();
                  assertThat(value.getStructValue()).isNotNull();
                })
                .extracting(Value::getStructValue)
                .containsExactlyInAnyOrderElementsOf(() -> givenApLanPorts.stream()
                    .map(apLanPort -> Struct.newBuilder()
                        .putFields(Key.VENUE_ID, ValueUtils.stringValue(apLanPort.getModelSpecific().getAp().getApGroup().getVenue().getId()))
                        .putFields(Key.AP_SERIAL_NUMBER, ValueUtils.stringValue(apLanPort.getModelSpecific().getAp().getId()))
                        .putFields(Key.PORT_ID, ValueUtils.stringValue(apLanPort.getPortId())).build()).iterator());
          });
    };
  }

  private void validate(
      SoftGreProfile source,
      Map<String, Value> target,
      SoftGreProfileNetworkVenueActivation... activations) {
    assertThat(target.get(Key.TENANT_ID))
        .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(source.getTenant().getId());
    assertThat(target.get(Key.ID))
        .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(source.getId());
    assertThat(target.get(Key.NAME))
        .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(source.getName());
    assertThat(target.get(Key.PRIMARY_GATEWAY_ADDRESS))
        .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(source.getPrimaryGateway());
    assertThat(target.get(Key.MTU_TYPE))
        .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(source.getMtuType().name());
    assertThat(target.get(Key.KEEP_ALIVE_INTERVAL))
        .matches(value -> value.getKindCase() == KindCase.NUMBER_VALUE)
        .extracting(value -> (short) value.getNumberValue())
        .isEqualTo(source.getKeepAliveInterval());
    assertThat(target.get(Key.KEEP_ALIVE_RETRY_TIMES))
        .matches(value -> value.getKindCase() == KindCase.NUMBER_VALUE)
        .extracting(value -> (short) value.getNumberValue())
        .isEqualTo(source.getKeepAliveRetryTimes());
    assertThat(target.get(Key.DISASSOCIATE_CLIENT_ENABLED))
        .matches(value -> value.getKindCase() == KindCase.BOOL_VALUE)
        .extracting(Value::getBoolValue)
        .isEqualTo(source.getDisassociateClientEnabled());

    Optional.ofNullable(source.getDescription())
        .ifPresent(
            description ->
                assertThat(target.get(Key.DESCRIPTION))
                    .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
                    .extracting(Value::getStringValue)
                    .isEqualTo(description));
    Optional.ofNullable(source.getSecondaryGateway())
        .ifPresent(
            secondaryGateway ->
                assertThat(target.get(Key.SECONDARY_GATEWAY_ADDRESS))
                    .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
                    .extracting(Value::getStringValue)
                    .isEqualTo(secondaryGateway));
    Optional.ofNullable(source.getMtuSize())
        .ifPresent(
            mtuSize ->
                assertThat(target.get(Key.MTU_SIZE))
                    .matches(value -> value.getKindCase() == KindCase.NUMBER_VALUE)
                    .extracting(value -> (short) value.getNumberValue())
                    .isEqualTo(mtuSize));

    // K is venueId, V is list of networkId
    var venueNetworksMap =
        List.of(activations).stream()
            .collect(
                Collectors.toMap(
                    a -> a.getNetworkVenue().getVenue().getId(),
                    a -> List.of(a.getNetworkVenue().getNetwork().getId()),
                    (l1, l2) -> {
                      var result = new ArrayList<>(l1);
                      result.addAll(l2);
                      return result;
                    }));
    assertThat(target.get(Key.ACTIVATIONS))
        .matches(value -> value.getKindCase() == KindCase.LIST_VALUE)
        .extracting(value -> value.getListValue().getValuesList())
        .matches(values -> values.size() == venueNetworksMap.size())
        .satisfies(
            values -> {
              for (var value : values) {
                assertThat(value).matches(v -> v.getKindCase() == KindCase.STRUCT_VALUE);
                var struct = value.getStructValue();
                assertThat(struct.getFieldsMap())
                    .matches(fields -> fields.containsKey(Key.VENUE_ID))
                    .matches(fields -> fields.containsKey(Key.WIFI_NETWORK_IDS))
                    .matches(fields -> fields.size() == 2);
                var venueId = struct.getFieldsMap().get(Key.VENUE_ID).getStringValue();
                var wifiNetworkIds =
                    struct
                        .getFieldsMap()
                        .get(Key.WIFI_NETWORK_IDS)
                        .getListValue()
                        .getValuesList()
                        .stream()
                        .map(Value::getStringValue)
                        .toList();
                assertThat(venueId).matches(venueNetworksMap::containsKey);
                assertThat(Set.copyOf(wifiNetworkIds))
                    .matches(ids -> ids.equals(Set.copyOf(venueNetworksMap.get(venueId))));
              }
            });
  }

  private List<ApLanPortProjection> mockApLanPortProjections(List<ApLanPort> apLanPorts) {
    return apLanPorts.stream()
        .map(a -> new ApLanPortProjection(
            a.getModelSpecific().getAp().getApGroup().getVenue().getId(),
            a.getModelSpecific().getAp().getId(),
            a.getPortId()))
        .toList();
  }

  private List<VenueLanPortProjection> mockVenueLanPortProjections(
      List<VenueLanPort> venueLanPorts) {
    return venueLanPorts.stream()
        .map(v -> new VenueLanPortProjection(
            v.getVenueApModelSpecificAttributes().getVenue().getId(),
            v.getVenueApModelSpecificAttributes().getModel(),
            v.getPortId()))
        .toList();
  }
}
