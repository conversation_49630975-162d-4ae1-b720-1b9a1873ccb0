package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class MacAuthFeatureTest {

  @SpyBean
  private MacAuthFeature unit;

  @Test
  @FeatureFlag(disable = WIFI_R370_TOGGLE)
  void givenR370Disable(Network network) {
    BDDAssertions.then(unit.test(network)).isFalse();
  }

  @Test
  @FeatureFlag(enable = WIFI_R370_TOGGLE)
  void givenDisableMacAuth(Network network) {
    BDDAssertions.then(unit.test(network)).isFalse();
  }

  @Test
  @FeatureFlag(enable = WIFI_R370_TOGGLE)
  void givenEnableMacAuth(Network network) {
    var wlan = new Wlan();
    wlan.setMacAddressAuthentication(true);
    network.setWlan(wlan);

    BDDAssertions.then(unit.test(network)).isTrue();
  }
}
