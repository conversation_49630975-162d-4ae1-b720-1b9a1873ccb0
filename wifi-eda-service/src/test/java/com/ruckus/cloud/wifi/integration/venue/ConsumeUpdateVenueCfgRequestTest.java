package com.ruckus.cloud.wifi.integration.venue;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_FEATURE_ROLES;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.BonjourFencingDeviceTypeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.BonjourFencingRangeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.CalledStaIdType;
import com.ruckus.acx.ddccm.protobuf.wifi.ManagementVlanModeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RadioType;
import com.ruckus.acx.ddccm.protobuf.wifi.UplinkSelectionEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueMesh;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.venue.proto.Address;
import com.ruckus.cloud.venue.proto.VenueEvent;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Mesh;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApManagementTrafficVlanSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadiusOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSnmpAgent;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CalledStationIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.FencingRangeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.IpModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LoadBalancingMethodEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MeshRadioTypeEnumV1_1;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdDelimiterEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PoeModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ScanMethodEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SteeringModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.ApModelLanPort;
import com.ruckus.cloud.wifi.eda.viewmodel.BssColoring;
import com.ruckus.cloud.wifi.eda.viewmodel.DenialOfServiceProtection;
import com.ruckus.cloud.wifi.eda.viewmodel.MdnsFencing;
import com.ruckus.cloud.wifi.eda.viewmodel.MdnsFencingWirelessRule;
import com.ruckus.cloud.wifi.eda.viewmodel.RadioParams6G;
import com.ruckus.cloud.wifi.eda.viewmodel.RadioParamsDual5G;
import com.ruckus.cloud.wifi.eda.viewmodel.TripleBand;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApIotSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApIpModeSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApSmartMonitorSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApTlsKeyEnhancedModeSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueClientAdmissionControl;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDirectedMulticast;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueHeight;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueLoadBalancing;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueMdnsFencing;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueRadioCustomization;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.listener.ListenerExecutionFailedException;

@WifiIntegrationTest
public class ConsumeUpdateVenueCfgRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @Deprecated
  void validateUpdateVenueLoadBalancingResult(String venueId,
      VenueLoadBalancing venueLoadBalancing, String tenantId, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getLoadBalancing)
        .isNotNull()
        .matches(v -> v.getEnabled().equals(venueLoadBalancing.getEnabled()))
        .matches(
            v -> v.getLoadBalancingMethod().equals(venueLoadBalancing.getLoadBalancingMethod()),
            "LoadBalancingMethod is equal")
        .matches(
            v -> v.getBandBalancingEnabled().equals(venueLoadBalancing.getBandBalancingEnabled()),
            "BandBalancingEnabled is equal")
        .matches(v -> v.getSteeringMode().equals(venueLoadBalancing.getSteeringMode()),
            "SteeringMode is equal")
        .matches(v -> v.getBandBalancingClientPercent24G()
                .equals(venueLoadBalancing.getBandBalancingClientPercent24G()),
            "BandBalancingClientPercent24G is equal")
        .matches(v -> v.getStickyClientSteeringEnabled()
                .equals(venueLoadBalancing.getStickyClientSteeringEnabled()),
            "getStickyClientSteeringEnabled is equal")
        .matches(v -> v.getStickyClientSnrThreshold()
                .equals(venueLoadBalancing.getStickyClientSnrThreshold()),
            "getStickyClientSnrThreshold is equal")
        .matches(v -> v.getStickyClientNbrApPercentageThreshold()
                .equals(venueLoadBalancing.getStickyClientNbrApPercentageThreshold()),
            "getStickyClientNbrApPercentageThreshold is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasLoadBalancing)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getLoadBalancing)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.VenueLoadBalancing::getStickyClientSteering)
        .matches(v -> v.getStickyClientSteeringEnabled()
                .equals(BoolValue.of(venueLoadBalancing.getStickyClientSteeringEnabled())),
            "getStickyClientSteeringEnabled is equal")
        .matches(v -> v.getStickyClientSnrThreshold()
                .equals(Int32Value.of(venueLoadBalancing.getStickyClientSnrThreshold())),
            "getStickyClientSnrThreshold is equal")
        .matches(v -> v.getStickyClientNbrApPercentageThreshold()
                .equals(Int32Value.of(venueLoadBalancing.getStickyClientNbrApPercentageThreshold())),
            "getStickyClientNbrApPercentageThreshold is equal");

  }

  @Deprecated
  void validateUpdateVenueDirectedMulticastResult(String venueId,
      VenueDirectedMulticast venueDirectedMulticast, String tenantId, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getDirectedMulticast)
        .isNotNull()
        .matches(v -> v.getWirelessEnabled().equals(venueDirectedMulticast.getWirelessEnabled()),
            "Wireless enabled status is equal")
        .matches(
            v -> v.getWiredEnabled().equals(venueDirectedMulticast.getWiredEnabled()),
            "Wired enabled status is equal")
        .matches(
            v -> v.getNetworkEnabled().equals(venueDirectedMulticast.getNetworkEnabled()),
            "Network enabled status is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasDirectedMulticast);
  }

  void validateUpdateVenueBonjourFencingResult(String venueId,
      VenueMdnsFencing venueBonjourFencingRequest, String tenantId, String requestId) {
    final var bonjourFencingRequest = venueBonjourFencingRequest.getServices().get(0);
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .matches(venue -> venue.getBonjourFencingEnabled()
            .equals(venueBonjourFencingRequest.getEnabled()))
        .extracting(venue -> venue.getBonjourFencing().get(0))
        .isNotNull()
        .matches(
            v -> v.getWirelessEnabled().equals(bonjourFencingRequest.getWirelessEnabled()),
            "Wireless rule enabled status is equal")
        .matches(v -> v.getWirelessRule().getFencingRange()
                .equals(bonjourFencingRequest.getWirelessRule().getFencingRange()),
            "Wireless rule fencing range is equal")
        .matches(
            v -> v.getWiredEnabled().equals(bonjourFencingRequest.getWiredEnabled()),
            "Wired rule enabled status is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasBonjourFencing))
        .hasSize(2)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getBonjourFencing)
        .matches(bf -> bf.get(1).getFencingRuleCount() == 1)
        .flatExtracting(com.ruckus.acx.ddccm.protobuf.wifi.BonjourFencing::getFencingRuleList)
        .matches(
            rule -> rule.get(0).getServiceType().name().equals((BridgeServiceEnum.AIRDISK).name()))
        .matches(rule -> rule.get(0).getDeviceType().name()
            .equals((BonjourFencingDeviceTypeEnum.WIRELESS).name()))
        .matches(rule -> rule.get(0).getFencingRange().name()
            .equals((BonjourFencingRangeEnum.SAME_AP).name()));
  }

  void validateDisableCloudReachabilityTest(String tenantId, String requestId) {
    final var tenantFromDb = repositoryUtil.find(Tenant.class, tenantId);
    assertThat(tenantFromDb)
        .isNotNull()
        .extracting(Tenant::getCSRCEnabled)
        .isEqualTo(false);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .hasSize(2)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .hasSize(1)
        .singleElement()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasReachabilityTest)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getReachabilityTest)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ReachabilityTest::getEnableReachability)
        .isEqualTo(false);
  }

  void validateUpdateVenueSnmpAgentResult(String requestId, String venueId,
      com.ruckus.cloud.wifi.eda.viewmodel.VenueSnmpAgent viewVenueSnmpAgent)
      throws InvalidProtocolBufferException {
    final var venue = repositoryUtil.find(Venue.class, venueId);

    var tenantId = venue.getTenant().getId();

    assertThat(venue.getApSnmpAgent()).isNotNull()
        .matches(
            v -> v.getEnableApSnmp().equals(viewVenueSnmpAgent.getEnableApSnmp()),
            "AP SNMP enabled status is equal")
        .extracting(VenueSnmpAgent::getApSnmpAgentProfile)
        .matches(p -> p.getId().equals(viewVenueSnmpAgent.getApSnmpAgentProfileId()),
            "AP SNMP agent profile ID is equal");

    var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThat(ddccmCfgRequestMessage.getPayload().getOperationsList())
        .isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1).first().matches(op -> op.getAction() == Action.MODIFY)
        .satisfies(op -> assertThat(op)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
            .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
            .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
            .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasSnmpAgent)
        .matches(v -> v.getSnmpAgent().getEnabled())
        .matches(v -> v.getSnmpAgent().hasApSnmpAgentProfileId())
        .matches(v -> viewVenueSnmpAgent.getApSnmpAgentProfileId()
            .equals(v.getSnmpAgent().getApSnmpAgentProfileId().getValue()));

    var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(msg -> msg.getTenantId().equals(tenantId))
        .matches(msg -> msg.getRequestId().equals(requestId))
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> EsConstants.Index.AP_SNMP_AGENT_PROFILE.equals(op.getIndex()))
        .hasSize(1).first()
        .matches(op -> op.getOpType() == OpType.MOD)
        .matches(op -> viewVenueSnmpAgent.getApSnmpAgentProfileId().equals(op.getId()))
        .satisfies(op -> {
          assertThat(op)
              .extracting(Operations::getDocMap)
              .matches(doc -> viewVenueSnmpAgent.getApSnmpAgentProfileId()
                  .equals(doc.get(EsConstants.Key.ID).getStringValue()))
              .matches(doc -> venue.getApSnmpAgent().getApSnmpAgentProfile().getPolicyName()
                  .equals(doc.get(EsConstants.Key.NAME).getStringValue()))
              .matches(doc -> doc.get("v2Agents").getListValue().getValuesCount() == 1)
              .matches(doc -> doc.get("v3Agents").getListValue().getValuesCount() == 1)
              .matches(doc -> doc.get("venues").getListValue().getValuesCount() == 1)
              .matches(doc -> doc.get("aps").getListValue().getValuesCount() == 0);
        });

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    ;

    assertThat(activityCfgChangeRespMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(msg -> msg.getStatus().equals(Status.OK))
        .matches(msg -> msg.getStep().equals(ApiFlowNames.UPDATE_VENUE_AP_SNMP_AGENT))
        .extracting(ConfigurationStatus::getEventDate).isNotNull();

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(activityImpactedMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThat(activityImpactedMessage.getPayload())
        .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
        .matches(msg -> msg.getDeviceIdsCount() == 0)
        .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
        .isEmpty();
  }

  @Deprecated
  void validateUpdateDenialOfServiceProtection(String venueId,
      DenialOfServiceProtection denialOfServiceProtection, String tenantId, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getDenialOfServiceProtection)
        .isNotNull()
        .matches(v -> v.getEnabled().equals(denialOfServiceProtection.getEnabled()))
        .matches(
            v -> v.getBlockingPeriod().equals(denialOfServiceProtection.getBlockingPeriod()),
            "BlockingPeriod is equal")
        .matches(
            v -> v.getFailThreshold().equals(denialOfServiceProtection.getFailThreshold()),
            "FailThreshold is equal")
        .matches(v -> v.getCheckPeriod().equals(denialOfServiceProtection.getCheckPeriod()),
            "CheckPeriod is equal");
    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasDenialOfServiceProtection)
        .matches(v -> v.getDenialOfServiceProtection().getEnabled() == true);
  }

  @Deprecated
  void validateUpdateVenueRadiusOptionsEnableTest(String venueId, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getRadiusOptions)
        .extracting(VenueRadiusOptions::getOverrideEnabled)
        .isEqualTo(true);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(venueFromDb.getTenant().getId(),
        requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .hasSize(2)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .hasSize(1)
        .singleElement()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue::hasRadiusOptions)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue::getRadiusOptions)
        .matches(r -> r.getNasIdType().equals(StringValue.of(NasIdTypeEnum.USER.toString())))
        .matches(r -> r.getCalledStaIdType().equals(CalledStaIdType.CalledStaIdType_AP_MAC))
        .matches(r -> r.getUserDefinedNasId().equals(StringValue.of("User_Defined")))
        .matches(r -> r.getAuthenticationMaxRetry().equals(Int32Value.of(3)))
        .matches(r -> r.getAccountingMaxRetry().equals(Int32Value.of(3)))
        .matches(r -> r.getSingleSessionIdAcctEnabled().equals(BoolValue.of(true)))
        .matches(r -> !r.hasNasIdDelimiter());
  }

  @Deprecated
  void validateUpdateVenueRadiusOptionsDisableTest(String venueId, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getRadiusOptions)
        .extracting(VenueRadiusOptions::getOverrideEnabled)
        .isEqualTo(false);

    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .hasSize(2)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .hasSize(1)
        .singleElement()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue::hasRadiusOptions)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue::getRadiusOptions)
        .matches(r -> r.getNasIdType().equals(StringValue.of(NasIdTypeEnum.BSSID.toString())))
        .matches(r -> r.getCalledStaIdType().equals(CalledStaIdType.CalledStaIdType_WLAN_BSSID))
        .matches(r -> r.getUserDefinedNasId().equals(StringValue.of("")))
        .matches(r -> r.getAuthenticationMaxRetry().equals(Int32Value.of(2)))
        .matches(r -> r.getAccountingMaxRetry().equals(Int32Value.of(2)))
        .matches(r -> r.getSingleSessionIdAcctEnabled().equals(BoolValue.of(false)))
        .matches(
            r -> r.getNasIdDelimiter().equals(StringValue.of(NasIdDelimiterEnum.COLON.toString())));
  }

  @Deprecated
  void validateUpdateMesh(String venueId, Mesh mesh, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getMesh)
        .isNotNull()
        .matches(v -> v.getEnabled().equals(mesh.getEnabled()),
            "Enabled is equal")
        .matches(v -> v.getZeroTouchEnabled().equals(mesh.getZeroTouchEnabled()),
            "ZeroTouchEnabled is equal")
        .matches(v -> v.getPassphrase().equals(mesh.getPassphrase()),
            "Passphrase is equal")
        .matches(v -> v.getSsid().equals(mesh.getSsid()),
            "Ssid is equal")
        .matches(v -> v.getRadioType().equals(mesh.getRadioType()),
            "RadioType is equal");
    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasMesh)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getMesh)
        .matches(VenueMesh::getEnabled)
        .matches(VenueMesh::getZeroTouchEnabled)
        .matches(m -> m.getPassphrase().equals(mesh.getPassphrase()))
        .matches(m -> m.getSsid().equals(mesh.getSsid()))
        .matches(m -> m.getUplinkSelection().equals(UplinkSelectionEnum.RSSI))
        .matches(m -> m.getRadio().equals(RadioType.RADIO24));
  }

  void validateUpdateUpdateVenueTripleBandRadio(Venue venueFromDb, String requestId,
      TripleBand tripleBand)
      throws InvalidProtocolBufferException {

    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getRadioCustomization)
        .isNotNull()
        .matches(v -> v.getTripleBandEnabled()
                .equals(tripleBand.getEnabled()),
            "DB tripleBandEnabled is equal");

    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
        .matches(r -> !tripleBand.getEnabled()
                .equals(r.getDual5GEnabled()),
            "DDCCM Dual5GEnabled is not equal");
  }

  @Deprecated
  void validateUpdateVenueRadioCustomization6G(Venue venueFromDb, String requestId,
      VenueRadioCustomization venueRadioCustomization)
      throws InvalidProtocolBufferException {

    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getRadioCustomization)
        .isNotNull()
        .matches(v -> v.getRadioParams6G().getChannelBandwidth()
                .equals(venueRadioCustomization.getRadioParams6G().getChannelBandwidth()),
            "DB ChannelBandwidth is equal");

    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
        .matches(r -> venueRadioCustomization.getRadioParams6G().getChannelBandwidth().toString()
                .equals(r.getRadio60().getChannelBandWidth()),
            "DDCCM ChannelBandwidth is equal");
  }

  @Deprecated
  void validateUpdateVenueBssColoring(String venueId, String requestId, BssColoring bssColoring) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getBssColoring)
        .isNotNull()
        .matches(v -> v.getBssColoringEnabled()
                .equals(bssColoring.getBssColoringEnabled()),
            "DB bssColoringEnabled is equal");

    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasBssColoring)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getBssColoring)
        .matches(venueBssColoring -> bssColoring.getBssColoringEnabled()
                .equals(venueBssColoring.getBssColoringEnabled().getValue()),
            "DDCCM bssColoringEnabled is equal");
  }

  @Deprecated
  void validateUpdateVenueClientAdmissionControl(
      String venueId, String requestId, VenueClientAdmissionControl venueClientAdmissionControl) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getClientAdmissionControl)
        .isNotNull()
        .matches(
            v ->
                v.getEnable24G()
                    .equals(venueClientAdmissionControl.getEnable24G()),
            "DB ClientAdmissionControl Enable24G is equal")
        .matches(
            v ->
                v.getEnable50G()
                    .equals(venueClientAdmissionControl.getEnable50G()),
            "DB ClientAdmissionControl Enable50G is equal")
        .matches(
            v ->
                v.getMinClientCount24G()
                    .equals(
                        venueClientAdmissionControl.getMinClientCount24G()),
            "DB ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            v ->
                v.getMinClientCount50G()
                    .equals(
                        venueClientAdmissionControl.getMinClientCount50G()),
            "DB ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            v ->
                v.getMaxRadioLoad24G()
                    .equals(venueClientAdmissionControl.getMaxRadioLoad24G()),
            "DB ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            v ->
                v.getMaxRadioLoad50G()
                    .equals(venueClientAdmissionControl.getMaxRadioLoad50G()),
            "DB ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            v ->
                v.getMinClientThroughput24G()
                    .equals(
                        venueClientAdmissionControl
                            .getMinClientThroughput24G()),
            "DB ClientAdmissionControl MinClientThroughput24G is equal")
        .matches(
            v ->
                v.getMinClientThroughput50G()
                    .equals(
                        venueClientAdmissionControl
                            .getMinClientThroughput50G()),
            "DB ClientAdmissionControl MinClientThroughput50G is equal");

    var record =
        messageCaptors.getDdccmMessageCaptor().getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();

    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization::hasRadio24)
        .extracting(
            radioCustomization -> radioCustomization.getRadio24().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled()
                    == venueClientAdmissionControl.getEnable24G(),
            "DDCCM ClientAdmissionControl Enable24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == venueClientAdmissionControl.getMinClientCount24G(),
            "DDCCM ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == venueClientAdmissionControl.getMaxRadioLoad24G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == venueClientAdmissionControl
                    .getMinClientThroughput24G(),
            "DDCCM ClientAdmissionControl MinClientThroughput24G is equal");

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization::hasRadio50)
        .extracting(
            radioCustomization -> radioCustomization.getRadio50().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled()
                    == venueClientAdmissionControl.getEnable50G(),
            "DDCCM ClientAdmissionControl Enable50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == venueClientAdmissionControl.getMinClientCount50G(),
            "DDCCM ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == venueClientAdmissionControl.getMaxRadioLoad50G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == venueClientAdmissionControl
                    .getMinClientThroughput50G(),
            "DDCCM ClientAdmissionControl MinClientThroughput50G is equal");
  }

  void validateUpdateVenueApManagementVlan(String venueId, String requestId, VenueApManagementTrafficVlanSettings mgmtVlan) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getApManagementVlan)
        .isNotNull()
        .matches(v -> v.getVlanId()
                .equals(mgmtVlan.getVlanId()),
            "DB Venue ApManagementVlan vlanId is equal");

    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .isNotNull()
        .matches(v -> mgmtVlan.getVlanId() != null ?
                v.getApManagementVlanMode().equals(ManagementVlanModeEnum.USER_DEFINED) :
                v.getApManagementVlanMode().equals(ManagementVlanModeEnum.KEEP),
            "DDCCM Venue ApManagementVlan vlanOverrideEnable is equal")
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasApManagementVlanId)
        .matches(v -> v.getApManagementVlanId().equals(Int32Value.of(mgmtVlan.getVlanId())),
            "DDCCM Venue ApManagementVlan vlanId is equal");
  }

  @Deprecated
  private void testUpdateVenueRadioParams6G(Venue venue, boolean enableAfc) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var tenantId = venue.getTenant().getId();

    var radioCustomization = new VenueRadioCustomization();
    var radioParamsDual5G = new RadioParamsDual5G();
    radioParamsDual5G.setEnabled(false);
    radioCustomization.setRadioParamsDual5G(radioParamsDual5G);
    var radioParams6G = new RadioParams6G();
    radioParams6G.setEnableAfc(enableAfc);
    radioParams6G.setAllowedChannels(List.of(Channel6GEnum._1, Channel6GEnum._5, Channel6GEnum._9));
    if (enableAfc) {
      var venueHeight = new VenueHeight();
      venueHeight.setMinFloor(3);
      venueHeight.setMaxFloor(5);
      radioParams6G.setVenueHeight(venueHeight);
    }
    radioCustomization.setRadioParams6G(radioParams6G);

    var requestParams = new RequestParams().addPathVariable("venueId", venue.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_VENUE_RADIO_CUSTOMIZATION,
        userName, requestParams, radioCustomization);

    var venueFromDb = repositoryUtil.find(Venue.class, venue.getId());

    assertNotNull(venueFromDb);

    var radioCustomizationFromDb = venueFromDb.getRadioCustomization();

    assertNotNull(radioCustomizationFromDb);
    assertFalse(radioCustomizationFromDb.getRadioParamsDual5G().getEnabled());

    var radioParams6GFromDb = radioCustomizationFromDb.getRadioParams6G();

    assertEquals(enableAfc, radioParams6GFromDb.getEnableAfc());

    var allowedChannels6G = radioParams6G.getAllowedChannels();
    var allowedChannels6GFromDb = radioParams6GFromDb.getAllowedIndoorChannels();

    assertTrue(
        allowedChannels6G.size() == allowedChannels6GFromDb.size() && allowedChannels6G.containsAll(
            allowedChannels6GFromDb) && allowedChannels6GFromDb.containsAll(allowedChannels6G));

    final var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();
    assertThat(ddccmMessage.getPayload().getOperationsList())
        .filteredOn(Operation::hasVenue)
        .filteredOn(e -> e.getAction() == Action.MODIFY)
        .extracting(Operation::getVenue)
        .last()
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasRadioCustomization)
        .matches(v -> enableAfc ? v.hasAfcVenueHeightAgl()
            && v.getAfcVenueHeightAgl().getMinHeight().getValue() == 3 * 3.5f - 3.5f
            && v.getAfcVenueHeightAgl().getMaxHeight().getValue() == 5 * 3.5f + 3.5f
            : v.hasAfcVenueHeightAgl() && v.getAfcVenueHeightAgl().hasMinHeight()
                && v.getAfcVenueHeightAgl().hasMaxHeight()
                && v.getAfcVenueHeightAgl().getMinHeight().getValue() == 0 &&
                v.getAfcVenueHeightAgl().getMaxHeight().getValue() == 0)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization::hasRadio60)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization::getRadio60)
        .matches(radio60 -> enableAfc == !radio60.getLowPowerIndoorModeEnable())
        .matches(radio60 -> allowedChannels6G.size() == radio60.getAllowedIndoorChannelListList().size());

    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(wifiCfgChangeMessage).isNotNull()
        .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.API_ACTION, CfgAction.UPDATE_VENUE_RADIO_CUSTOMIZATION.key()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(wifiCfgChangeMessage.getPayload().getOperationList())
        .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasVenue)
        .filteredOn(e -> e.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY)
        .extracting(com.ruckus.cloud.wifi.proto.Operation::getVenue)
        .last()
        .matches(v -> enableAfc ? v.hasHeight() && v.getHeight().getMinFloor().getValue() == 3
            && v.getHeight().getMaxFloor().getValue() == 5
            : v.hasHeight() && !v.getHeight().hasMinFloor() && !v.getHeight().hasMaxFloor());
  }

  @Nested
  class whenConsumeUpdateVenueCfgRequestTest {

    @Deprecated
    @Test
    void thenUpdateVenueLoadBalancing(Venue venue) throws JsonProcessingException {

      final var requestId = randomTxId();
      final var userName = randomName();
      final var venueLoadBalancing = new VenueLoadBalancing();
      venueLoadBalancing.setEnabled(true);
      venueLoadBalancing.setLoadBalancingMethod(LoadBalancingMethodEnum.BASED_ON_CAPACITY);
      venueLoadBalancing.setSteeringMode(SteeringModeEnum.STRICT);
      venueLoadBalancing.setBandBalancingEnabled(false);
      venueLoadBalancing.setBandBalancingClientPercent24G((short) 25);
      venueLoadBalancing.setStickyClientSteeringEnabled(true);
      venueLoadBalancing.setStickyClientSnrThreshold((short) 16);
      venueLoadBalancing.setStickyClientNbrApPercentageThreshold((short) 22);

      ObjectMapper objectMapper = new ObjectMapper();
      messageUtil.sendWifiCfgRequest(
          venue.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_VENUE_LOAD_BALANCING,
          objectMapper.writeValueAsString(
              Map.of(
                  RKS_IDM_USER_ID.getName(), userName
              )),
          new RequestParams().addPathVariable("venueId", venue.getId()),
          venueLoadBalancing);

      validateUpdateVenueLoadBalancingResult(venue.getId(), venueLoadBalancing,
          venue.getTenant().getId(), requestId);
    }

    @Deprecated
    @Test
    void thenUpdateVenueDirectedMulticast(Venue venue) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var venueDirectedMulticast = new VenueDirectedMulticast();
      venueDirectedMulticast.setWirelessEnabled(true);
      venueDirectedMulticast.setWiredEnabled(true);
      venueDirectedMulticast.setNetworkEnabled(false);

      messageUtil.sendWifiCfgRequest(
          venue.getTenant().getId(), requestId, CfgAction.UPDATE_VENUE_DIRECTED_MULTICAST,
          userName, new RequestParams().addPathVariable("venueId", venue.getId()),
          venueDirectedMulticast);
      validateUpdateVenueDirectedMulticastResult(venue.getId(), venueDirectedMulticast,
          venue.getTenant().getId(), requestId);
    }

    @Deprecated
    @Test
    void thenUpdateVenueBonjourFencing(Venue venue) {
      final var requestId = randomTxId();
      final var userName = randomName();
      var venueMdnsFencing = new VenueMdnsFencing();
      var mdnsFencing = new MdnsFencing();

      //wireless rule
      var wirelessRule = new MdnsFencingWirelessRule();
      wirelessRule.setFencingRange(FencingRangeEnum.SAME_AP);
      mdnsFencing.setService(BridgeServiceEnum.AIRDISK);
      mdnsFencing.setWirelessEnabled(true);
      mdnsFencing.setWirelessRule(wirelessRule);

      //wired rules
      mdnsFencing.setWiredEnabled(false);

      venueMdnsFencing.setEnabled(true);
      venueMdnsFencing.setServices(List.of(mdnsFencing));

      messageUtil.sendWifiCfgRequest(
          venue.getTenant().getId(), requestId, CfgAction.UPDATE_VENUE_BONJOUR_FENCING,
          userName, new RequestParams().addPathVariable("venueId", venue.getId()),
          venueMdnsFencing);
      validateUpdateVenueBonjourFencingResult(venue.getId(), venueMdnsFencing,
          venue.getTenant().getId(), requestId);
    }

    @Test
    void thenDisableCloudReachabilityTest(Venue venue) {
      final var requestId = randomTxId();
      final var userName = randomName();

      assertTrue(venue.getTenant().getCSRCEnabled());
      messageUtil.sendWifiCfgRequest(
          venue.getTenant().getId(), requestId, CfgAction.DISABLE_CLOUD_SERVICE_REACHABILITY_CHECK,
          userName, new RequestParams().addPathVariable("tenantId", venue.getTenant().getId()),
          StringUtils.EMPTY);
      validateDisableCloudReachabilityTest(venue.getTenant().getId(), requestId);
    }

    @Test
    void thenUpdateVenueSnmpAgent(Venue venue, ApSnmpAgentProfile apSnmpAgentProfile)
        throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();

      var viewVenueSnmpAgent = new com.ruckus.cloud.wifi.eda.viewmodel.VenueSnmpAgent();
      viewVenueSnmpAgent.setEnableApSnmp(true);
      viewVenueSnmpAgent.setApSnmpAgentProfileId(apSnmpAgentProfile.getId());

      messageUtil.sendWifiCfgRequest(
          venue.getTenant().getId(), requestId, CfgAction.UPDATE_VENUE_AP_SNMP_AGENT,
          userName, new RequestParams().addPathVariable("venueId", venue.getId()),
          viewVenueSnmpAgent);

      validateUpdateVenueSnmpAgentResult(requestId, venue.getId(), viewVenueSnmpAgent);
    }

    @Test
    void thenUpdateVenueRadioParams6G(Venue venue) {
      var tenantId = venue.getTenant().getId();
      // US afcEnabled: true, AFC can be enabled or disabled
      testUpdateVenueRadioParams6G(venue, false);
      testUpdateVenueRadioParams6G(venue, true);

      var newVenueId = randomId();
      var event = VenueEvent.newBuilder()
          .setTenantId(tenantId)
          .addOperation(com.ruckus.cloud.venue.proto.Operation.newBuilder().setAction(
                  com.ruckus.cloud.venue.proto.Action.ADD)
              .setVenue(com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(newVenueId)
                  .setAddress(Address.newBuilder()
                      .setCountryCode("GB").build()).build()))
          .build();

      messageUtil.sendVenueCfgChange(tenantId, randomTxId(), event);

      var newVenue = repositoryUtil.find(Venue.class, newVenueId);

      // GB afcEnabled: false, AFC must be disabled
      testUpdateVenueRadioParams6G(newVenue, false);

      Throwable ex = assertThrows(RuntimeException.class,
          () -> testUpdateVenueRadioParams6G(newVenue, true));

      ex = ex.getCause();
      assertInstanceOf(ListenerExecutionFailedException.class, ex);
      ex = ex.getCause();
      assertInstanceOf(InvalidPropertyValueException.class, ex);

      assertEquals(Errors.WIFI_10502.code(), ((InvalidPropertyValueException) ex).getErrorCode().code());
      assertEquals(Errors.WIFI_10502.message(), ex.getMessage());
    }

    @Deprecated
    @Test
    void thenUpdateDenialOfServiceProtection(Venue venue) throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();
      DenialOfServiceProtection denialOfServiceProtection = new DenialOfServiceProtection();
      denialOfServiceProtection.setEnabled(true);

      messageUtil.sendWifiCfgRequest(
          venue.getTenant().getId(), requestId, CfgAction.UPDATE_DENIAL_OF_SERVICE_PROTECTION,
          userName, new RequestParams().addPathVariable("venueId", venue.getId()),
          denialOfServiceProtection);
      validateUpdateDenialOfServiceProtection(venue.getId(), denialOfServiceProtection,
          venue.getTenant().getId(), requestId);
    }

    @Deprecated
    @Test
    void thenUpdateVenueLanPorts(Venue venue) {
      final var userName = randomName();
      var venueLanPort1 = new VenueLanPort();
      venueLanPort1.setPortId("1");
      venueLanPort1.setEnabled(true);
      venueLanPort1.setType(ApLanPortTypeEnum.TRUNK);
      venueLanPort1.setUntagId((short) 1);
      venueLanPort1.setVlanMembers("1-4094");

      var venueLanPort2 = new VenueLanPort();
      venueLanPort2.setPortId("2");
      venueLanPort2.setEnabled(true);
      venueLanPort2.setType(ApLanPortTypeEnum.TRUNK);
      venueLanPort2.setUntagId((short) 1);
      venueLanPort2.setVlanMembers("1-4094");

      var venueLanPort3 = new VenueLanPort();
      venueLanPort3.setPortId("3");
      venueLanPort3.setEnabled(true);
      venueLanPort3.setType(ApLanPortTypeEnum.TRUNK);
      venueLanPort3.setUntagId((short) 1);
      venueLanPort3.setVlanMembers("1-4094");

      var apModelLanPort = new ApModelLanPort();
      apModelLanPort.setModel("T750SE");
      apModelLanPort.setPoeOut(true);
      apModelLanPort.setPoeMode(PoeModeEnum._802_3at);
      apModelLanPort.setLanPorts(List.of(venueLanPort1, venueLanPort2, venueLanPort3));

      messageUtil.sendWifiCfgRequest(
          venue.getTenant().getId(), randomTxId(), CfgAction.UPDATE_VENUE_LAN_PORTS,
          userName, new RequestParams().addPathVariable("venueId", venue.getId()),
          List.of(apModelLanPort));

      apModelLanPort.setPoeMode(PoeModeEnum.Auto);
      apModelLanPort.setPoeOut(false);

      messageUtil.sendWifiCfgRequest(
          venue.getTenant().getId(), randomTxId(), CfgAction.UPDATE_VENUE_LAN_PORTS,
          userName, new RequestParams().addPathVariable("venueId", venue.getId()),
          List.of(apModelLanPort));

      var venueFromDb = repositoryUtil.find(Venue.class, venue.getId());

      assertThat(venueFromDb).isNotNull().extracting(Venue::getModelSpecificAttributes).asList()
          .extracting(VenueApModelSpecificAttributes.class::cast).hasSize(1).first()
          .matches(attr -> "T750SE".equals(attr.getModel()))
          .matches(attr -> PoeModeEnum.Auto.equals(attr.getPoeMode()))
          .matches(attr -> Boolean.FALSE.equals(attr.getPoeOut()));
    }

    @Deprecated
    @Test
    void thenUpdateVenueRadiusOptionsTest(Tenant tenant, Venue venue) {
      Radius authRadius = new Radius("authRadius-id");
      repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());
      AuthRadiusVenue authRadiusVenue = new AuthRadiusVenue("authRadiusVenue-id");
      authRadiusVenue.setVenue(venue);
      authRadiusVenue.setRadius(authRadius);
      repositoryUtil.createOrUpdate(authRadiusVenue, tenant.getId(), randomTxId());

      OpenNetwork openNetwork = (OpenNetwork) network(OpenNetwork.class).generate();
      openNetwork.getWlan().setNetwork(openNetwork);
      openNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
      RadiusOptions initRadiusOptions = new RadiusOptions();
      initRadiusOptions.setNasIdDelimiter(NasIdDelimiterEnum.COLON);
      initRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.BSSID);
      openNetwork.getWlan().getAdvancedCustomization().setRadiusOptions(initRadiusOptions);
      // RADIUS options works on OpenNetwork only when:
      // 1. has authRadius
      // 2. macAddressAuthentication is enabled
      openNetwork.setAuthRadius(authRadius);
      openNetwork.getWlan().setMacAddressAuthentication(true);
      repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());

      NetworkVenue networkVenue = networkVenue()
          .setNetwork(always(openNetwork)).setVenue(always(venue)).generate();
      openNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      // Assertion before config override
      RadiusOptions radiusOptions = openNetwork.getWlan().getAdvancedCustomization()
          .getRadiusOptions();
      assertEquals(NasIdTypeEnum.BSSID, radiusOptions.getNasIdType());
      assertEquals(CalledStationIdTypeEnum.BSSID, radiusOptions.getCalledStationIdType());
      assertEquals(NasIdDelimiterEnum.COLON, radiusOptions.getNasIdDelimiter());
      assertEquals(2, radiusOptions.getNasMaxRetry());
      assertNull(radiusOptions.getUserDefinedNasId());
      assertFalse(radiusOptions.getSingleSessionIdAccounting());

      VenueRadiusOptions venueRadiusOptions = new VenueRadiusOptions();
      venueRadiusOptions.setOverrideEnabled(true);
      venueRadiusOptions.setNasIdType(NasIdTypeEnum.USER);
      venueRadiusOptions.setUserDefinedNasId("User_Defined");
      venueRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_MAC);
      venueRadiusOptions.setNasMaxRetry(3);
      venueRadiusOptions.setSingleSessionIdAccounting(true);
      venueRadiusOptions.setNasIdDelimiter(null);

      // Enable override
      var requestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenant.getId(), requestId, CfgAction.UPDATE_VENUE_RADIUS_OPTIONS,
          randomName(), new RequestParams().addPathVariable("venueId", venue.getId()),
          venueRadiusOptions);
      validateUpdateVenueRadiusOptionsEnableTest(venue.getId(), requestId);

      // Disable override
      requestId = randomTxId();
      venueRadiusOptions.setOverrideEnabled(false);
      messageUtil.sendWifiCfgRequest(
          tenant.getId(), requestId, CfgAction.UPDATE_VENUE_RADIUS_OPTIONS,
          randomName(), new RequestParams().addPathVariable("venueId", venue.getId()),
          venueRadiusOptions);
      validateUpdateVenueRadiusOptionsDisableTest(venue.getId(), requestId);
    }

    @Deprecated
    @Test
    void thenUpdateMesh(Venue venue) throws JsonProcessingException {
      final var requestId = randomTxId();
      final var userName = randomName();
      Mesh mesh = new Mesh();
      mesh.setEnabled(true);
      mesh.setZeroTouchEnabled(true);
      mesh.setRadioType(MeshRadioTypeEnumV1_1._2_4_GHz);
      mesh.setPassphrase("test_passphrase");
      mesh.setSsid("test_ssid");

      ObjectMapper objectMapper = new ObjectMapper();
      messageUtil.sendWifiCfgRequest(
          venue.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_MESH_OPTIONS,
          objectMapper.writeValueAsString(
              Map.of(
                  RKS_IDM_USER_ID.getName(), userName,
                  RKS_FEATURE_ROLES.getName(), "BETA-MESH"
              )),
          new RequestParams().addPathVariable("venueId", venue.getId()),
          mesh);
      validateUpdateMesh(venue.getId(), mesh, requestId);
    }
  }

  @Deprecated
  @Test
  void thenUpdateVenueBssColoring(Venue venue) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var venueBssColoring = new BssColoring();
    venueBssColoring.setBssColoringEnabled(false);

    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(), requestId, CfgAction.UPDATE_VENUE_BSS_COLORING_SETTINGS,
        userName, new RequestParams().addPathVariable("venueId", venue.getId()),
        venueBssColoring);
    validateUpdateVenueBssColoring(venue.getId(), requestId, venueBssColoring);
  }

  @Deprecated
  @Test
  void thenUpdateVenueClientAdmissionControl(Venue venue) throws JsonProcessingException {
    final var requestId = randomTxId();
    final var userName = randomName();

    // Disable load balancing and band balancing
    final var venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(false);
    venueLoadBalancing.setLoadBalancingMethod(LoadBalancingMethodEnum.BASED_ON_CAPACITY);
    venueLoadBalancing.setSteeringMode(SteeringModeEnum.STRICT);
    venueLoadBalancing.setBandBalancingEnabled(false);
    venueLoadBalancing.setBandBalancingClientPercent24G((short) 25);
    venueLoadBalancing.setStickyClientSteeringEnabled(true);
    venueLoadBalancing.setStickyClientSnrThreshold((short) 16);
    venueLoadBalancing.setStickyClientNbrApPercentageThreshold((short) 22);

    ObjectMapper objectMapper = new ObjectMapper();
    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(),
        randomTxId(),
        CfgAction.UPDATE_VENUE_LOAD_BALANCING,
        objectMapper.writeValueAsString(
            Map.of(
                RKS_IDM_USER_ID.getName(), userName)),
        new RequestParams().addPathVariable("venueId", venue.getId()),
        venueLoadBalancing);

    final var venueClientAdmissionControl = new VenueClientAdmissionControl();
    venueClientAdmissionControl.setEnable24G(true);
    venueClientAdmissionControl.setMinClientCount24G((short) 11);
    venueClientAdmissionControl.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl.setEnable50G(true);
    venueClientAdmissionControl.setMinClientCount50G((short) 21);
    venueClientAdmissionControl.setMaxRadioLoad50G((short) 50);
    venueClientAdmissionControl.setMinClientThroughput50G((short) 20);

    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(),
        requestId,
        CfgAction.UPDATE_VENUE_CLIENT_ADMISSION_CONTROL_SETTINGS,
        userName,
        new RequestParams().addPathVariable("venueId", venue.getId()),
        venueClientAdmissionControl);
    validateUpdateVenueClientAdmissionControl(
        venue.getId(), requestId, venueClientAdmissionControl);
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_VENUE_AP_MGMT_VLAN_TOGGLE)
  void thenUpdateVenueApManagementVlan(Venue venue) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var venueMgmtVlan = new VenueApManagementTrafficVlanSettings();
    venueMgmtVlan.setVlanId((short) 7);

    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(), requestId, CfgAction.UPDATE_VENUE_AP_MANAGEMENT_VLAN_SETTINGS,
        userName, new RequestParams().addPathVariable("venueId", venue.getId()),
        venueMgmtVlan);
    validateUpdateVenueApManagementVlan(venue.getId(), requestId, venueMgmtVlan);
  }

  @Nested
  class whenUpdateUpdateVenueTripleBandRadioRequest {

    private String venueId;

    @BeforeEach
    void persistedInDb(final Venue venue) {
      venueId = venue.getId();
    }

    @Payload
    private TripleBand tripleBand() {
      final var tripleBand = new TripleBand();
      tripleBand.setEnabled(true);
      return tripleBand;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_VENUE_TRIPLE_BAND_RADIO_SETTINGS)
    void thenUpdateUpdateVenueTripleBandRadio(TxCtxHolder.TxCtx txCtx,
        @Payload TripleBand payload)
        throws InvalidProtocolBufferException {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateUpdateUpdateVenueTripleBandRadio(venue, txCtx.getTxId(), payload);
    }
  }

  @Deprecated
  @Nested
  class whenConsumeUpdateVenueRadioCustomizationCfgRequestTest {

    private String venueId;

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId);
    }

    @Payload("ChannelFly_320")
    private VenueRadioCustomization channelFly320() {
      final var radioParams6GGenerator = Generators.radioParams6GGenerator(
          ScanMethodEnum.CHANNELFLY, ChannelBandwidth6GEnum._320MHz);
      final var venueRadioCustomization = Generators.venueRadioCustomizationGenerator(
          radioParams6GGenerator);
      return venueRadioCustomization.generate();
    }

    @Payload("ChannelFly_160")
    private VenueRadioCustomization channelFly160() {
      final var radioParams6GGenerator = Generators.radioParams6GGenerator(
          ScanMethodEnum.CHANNELFLY, ChannelBandwidth6GEnum._160MHz);
      final var venueRadioCustomization = Generators.venueRadioCustomizationGenerator(
          radioParams6GGenerator);
      return venueRadioCustomization.generate();
    }

    @BeforeEach
    void persistedInDb(final Venue venue) {
      venueId = venue.getId();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_VENUE_RADIO_CUSTOMIZATION, payload = @Payload("ChannelFly_320"))
    @FeatureRole("AP-70")
    void thenUpdateVenueRadioCustomizationChannelFly320(TxCtxHolder.TxCtx txCtx,
        @Payload("ChannelFly_320") VenueRadioCustomization payload)
        throws InvalidProtocolBufferException {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateUpdateVenueRadioCustomization6G(venue, txCtx.getTxId(), payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_VENUE_RADIO_CUSTOMIZATION, payload = @Payload("ChannelFly_160"))
    @FeatureRole("AP-70")
    void thenUpdateVenueRadioCustomizationChannelFly160(TxCtxHolder.TxCtx txCtx,
        @Payload("ChannelFly_160") VenueRadioCustomization payload)
        throws InvalidProtocolBufferException {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateUpdateVenueRadioCustomization6G(venue, txCtx.getTxId(), payload);
    }

  }

  @Nested
  class whenUpdateVenueApIpModeSettingsRequest {

    private String venueId;

    @BeforeEach
    void persistedInDb(final Venue venue) {
      venueId = venue.getId();
    }

    @Payload
    private VenueApIpModeSettings venueApIpModeSettings() {
      final var venueApIpModeSettings = new VenueApIpModeSettings();
      venueApIpModeSettings.setMode(IpModeEnum.IPV4_IPV6);
      return venueApIpModeSettings;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_IP_MODE_CONFIG_TOGGLE)
    @ApiAction(value = CfgAction.UPDATE_VENUE_AP_IP_MODE_SETTINGS)
    void thenUpdateVenueDeviceIpMode(TxCtxHolder.TxCtx txCtx,
        @Payload VenueApIpModeSettings payload)
        throws InvalidProtocolBufferException {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateUpdateVenueDeviceIpMode(venue, txCtx.getTxId(), payload);
    }

    private void validateUpdateVenueDeviceIpMode(Venue venueFromDb, String requestId,
        VenueApIpModeSettings venueApIpModeSettings)
        throws InvalidProtocolBufferException {

      assertThat(venueFromDb)
          .isNotNull()
          .extracting(Venue::getDeviceIpMode)
          .isNotNull()
          .matches(mode -> mode.equals(venueApIpModeSettings.getMode()),
              "DB deviceIpMode is equal");

      var record = messageCaptors.getDdccmMessageCaptor()
          .getValue(venueFromDb.getTenant().getId(), requestId);
      assertThat(record).isNotNull();
      WifiConfigRequest ddccmRequest = record.getPayload();
      assertThat(ddccmRequest.getOperationsList()).isNotNull()
          .matches(request -> request.stream()
              .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
          .hasSize(1)
          .singleElement()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getDeviceIpMode)
          .matches(r -> r.equals(com.ruckus.acx.ddccm.protobuf.wifi.IpModeEnum.IPV4_IPV6),
              "DDCCM deviceIpMode is equal");
    }
  }

  @Nested
  class whenUpdateVenueApTlsKeyEnhancedModeSettingsRequest {

    private String venueId;

    @BeforeEach
    void persistedInDb(final Venue venue) {
      venueId = venue.getId();
    }

    @Payload
    private VenueApTlsKeyEnhancedModeSettings venueApTlsKeyEnhanceModeSettings() {
      final var venueApTlsKeyEnhancedModeSettings = new VenueApTlsKeyEnhancedModeSettings();
      venueApTlsKeyEnhancedModeSettings.setTlsKeyEnhancedModeEnabled(true);
      return venueApTlsKeyEnhancedModeSettings;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE)
    @ApiAction(value = CfgAction.UPDATE_VENUE_AP_TLS_KEY_ENHANCED_MODE_SETTINGS)
    void thenUpdateVenueTlsKeyEnhancedMode(TxCtxHolder.TxCtx txCtx,
        @Payload VenueApTlsKeyEnhancedModeSettings payload)
        throws InvalidProtocolBufferException {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateUpdateVenueTlsKeyEnhancedMode(venue, txCtx.getTxId(), payload);
    }

    private void validateUpdateVenueTlsKeyEnhancedMode(Venue venueFromDb, String requestId,
        VenueApTlsKeyEnhancedModeSettings venueApTlsKeyEnhancedModeSettings)
        throws InvalidProtocolBufferException {

      assertThat(venueFromDb)
          .isNotNull()
          .extracting(Venue::getTlsKeyEnhancedModeEnabled)
          .isNotNull()
          .matches(mode -> mode.equals(venueApTlsKeyEnhancedModeSettings.getTlsKeyEnhancedModeEnabled()),
              "DB tlsKeyEnhancedModeEnabled is equal");

      var record = messageCaptors.getDdccmMessageCaptor()
          .getValue(venueFromDb.getTenant().getId(), requestId);
      assertThat(record).isNotNull();
      WifiConfigRequest ddccmRequest = record.getPayload();
      assertThat(ddccmRequest.getOperationsList()).isNotNull()
          .matches(request -> request.stream()
              .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
          .hasSize(1)
          .singleElement()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getTlsKeyEnhancedModeEnabled)
          .matches(r -> r.equals(venueApTlsKeyEnhancedModeSettings.getTlsKeyEnhancedModeEnabled()),
              "DDCCM tlsKeyEnhancedModeEnabled is equal");
    }
  }

  @Nested
  class whenUpdateVenueApIotSettingsRequest {
    private String venueId;

    @BeforeEach
    void persistedInDb(final Venue venue) {
      venueId = venue.getId();
    }

    @Payload
    private VenueApIotSettings venueApIotSettings() {
      final var venueApIotSettings = new VenueApIotSettings();
      venueApIotSettings.setEnabled(Boolean.TRUE);
      venueApIotSettings.setMqttBrokerAddress("*************");
      return venueApIotSettings;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId);
    }

    @Test
    @FeatureFlag(enable = FlagNames.IOT_MQTT_BROKER_TOGGLE)
    @ApiAction(value = CfgAction.UPDATE_VENUE_AP_IOT_SETTINGS)
    void thenUpdateVenueApIotSettings(TxCtxHolder.TxCtx txCtx,
        @Payload VenueApIotSettings payload) {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateUpdateVenueApIotSettings(venue, txCtx.getTxId(), payload);
    }

    private void validateUpdateVenueApIotSettings(Venue venueFromDb, String requestId,
        VenueApIotSettings venueApIotSettings) {
      assertThat(venueFromDb)
          .isNotNull()
          .extracting(Venue::getIotSettings)
          .isNotNull()
          .matches(s -> s.getEnabled().equals(venueApIotSettings.getEnabled())
                  && s.getMqttBrokerAddress().equals(venueApIotSettings.getMqttBrokerAddress()),
              "DB iot settings are equal.");

      var record = messageCaptors.getDdccmMessageCaptor()
          .getValue(venueFromDb.getTenant().getId(), requestId);
      assertThat(record).isNotNull();

      WifiConfigRequest ddccmRequest = record.getPayload();
      assertThat(ddccmRequest.getOperationsList()).isNotNull()
          .matches(request -> request.stream()
              .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
          .hasSize(1)
          .singleElement()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getIotSettings)
          .matches(s ->
                  s.getEnabled() == BooleanUtils.toBoolean(venueApIotSettings.getEnabled())
                      && s.getMqttBrokerAddress().equals(venueApIotSettings.getMqttBrokerAddress()),
              "DDCCM iot settings are equal.");
    }
  }

  @Nested
  class whenUpdateVenueApSmartMonitorSettingsRequest {

    private String venueId;

    @BeforeEach
    void persistedInDb(final Venue venue) {
      venueId = venue.getId();
    }

    @Payload
    private VenueApSmartMonitorSettings venueApSmartMonitorSettings() {
      final var venueApSmartMonitorSettings = new VenueApSmartMonitorSettings();
      venueApSmartMonitorSettings.setEnabled(true);
      venueApSmartMonitorSettings.setInterval((short) 10);
      venueApSmartMonitorSettings.setThreshold((short) 5);
      return venueApSmartMonitorSettings;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_SMART_MONITOR_DISABLE_WLAN_TOGGLE)
    @ApiAction(value = CfgAction.UPDATE_VENUE_AP_SMART_MONITOR_SETTINGS)
    void thenUpdateVenueApSmartMonitor(TxCtxHolder.TxCtx txCtx,
        @Payload VenueApSmartMonitorSettings payload)
        throws InvalidProtocolBufferException {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateUpdateVenueApSmartMonitor(venue, txCtx.getTxId(), payload);
    }

    private void validateUpdateVenueApSmartMonitor(Venue venueFromDb, String requestId,
        VenueApSmartMonitorSettings venueApSmartMonitorSettings) {

      assertThat(venueFromDb)
          .isNotNull()
          .extracting(Venue::getSmartMonitor)
          .isNotNull()
          .matches(s -> s.getEnabled() == venueApSmartMonitorSettings.getEnabled()
                  && s.getInterval().shortValue()
                  == venueApSmartMonitorSettings.getInterval()
                  && s.getThreshold().shortValue()
                  == venueApSmartMonitorSettings.getThreshold(),
              "DB smartMonitor is equal");

      var record = messageCaptors.getDdccmMessageCaptor()
          .getValue(venueFromDb.getTenant().getId(), requestId);
      assertThat(record).isNotNull();
      WifiConfigRequest ddccmRequest = record.getPayload();
      assertThat(ddccmRequest.getOperationsList()).isNotNull()
          .matches(request -> request.stream()
              .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
          .hasSize(1)
          .singleElement()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getSmartMonitor)
          .matches(s ->
                  s.getEnabled() == venueApSmartMonitorSettings.getEnabled()
                      && s.getIntervalSeconds()
                      == venueApSmartMonitorSettings.getInterval()
                      && s.getThresholdTimes()
                      == venueApSmartMonitorSettings.getThreshold(),
              "DDCCM smartMonitor is equal");
    }
  }
}
