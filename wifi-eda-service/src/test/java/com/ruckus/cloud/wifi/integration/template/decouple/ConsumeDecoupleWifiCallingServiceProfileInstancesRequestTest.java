package com.ruckus.cloud.wifi.integration.template.decouple;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfile;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.WifiCallingServiceProfileTestFixture;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@WifiIntegrationTest
public class ConsumeDecoupleWifiCallingServiceProfileInstancesRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void init() {
  clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void decouple_wifiCallingServiceProfileInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();
    String mspTenantId = mspTenant.getId();
    repositoryUtil.createOrUpdate(mspTenant, mspTenantId);
    WifiCallingServiceProfile template = WifiCallingServiceProfileTestFixture.randomWifiCallingServiceProfile();
    template.setIsTemplate(true);
    repositoryUtil.createOrUpdate(template, mspTenantId);

    // create ec tenant & instance
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, ecTenantId);
    WifiCallingServiceProfile instance = WifiCallingServiceProfileTestFixture.randomWifiCallingServiceProfile();
    instance.setIsTemplate(false);
    instance.setIsEnforced(true);
    instance.setTemplateId(template.getId());
    repositoryUtil.createOrUpdate(instance, ecTenantId);

    // Verify before decouple
    WifiCallingServiceProfile before = repositoryUtil.find(WifiCallingServiceProfile.class, instance.getId(), ecTenantId, false);
    assertAll(
    () -> assertEquals(template.getId(), before.getTemplateId()),
    () -> assertFalse(before.getIsTemplate()),
    () -> assertTrue(before.getIsEnforced())
    );

    // Perform decouple
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_WIFI_CALLING_SERVICE_PROFILE_INSTANCES, userName, template.getId());
    assertDdccmCfgRequestNotSent(ecTenantId);
    // Verify after decouple
    WifiCallingServiceProfile after = repositoryUtil.find(WifiCallingServiceProfile.class, instance.getId(), ecTenantId, false);
    assertAll(
    () -> assertNull(after.getTemplateId()),
    () -> assertFalse(after.getIsEnforced()),
    () -> assertFalse(after.getIsTemplate())
    );
    // Verify template is not affected
    changeTxCtxTenant(mspTenantId);
    WifiCallingServiceProfile templateAfter = repositoryUtil.find(WifiCallingServiceProfile.class, template.getId(), mspTenantId, true);
    assertAll(
    () -> assertTrue(templateAfter.getIsTemplate()),
    () -> assertEquals(template.getServiceName(), templateAfter.getServiceName())
    );

    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instance.getId());
  }

  @Test
  public void decouple_noInstances_shouldSucceed() {
  var userName = txCtxExtension.getUserName();
  Tenant mspTenant = TenantTestFixture.randomTenant((t) -> {});
  String mspTenantId = mspTenant.getId();
  repositoryUtil.createOrUpdate(mspTenant, mspTenantId);
  WifiCallingServiceProfile template = WifiCallingServiceProfileTestFixture.randomWifiCallingServiceProfile();
  template.setIsTemplate(true);
  repositoryUtil.createOrUpdate(template, mspTenantId);
  Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
  String ecTenantId = ecTenant.getId();
  repositoryUtil.createOrUpdate(ecTenant, ecTenantId);
  // Do not create any instance
  changeTxCtxTenant(ecTenantId);
  sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_WIFI_CALLING_SERVICE_PROFILE_INSTANCES, userName, template.getId());
  assertDdccmCfgRequestNotSent(ecTenantId);
  // Should not throw exception and no instance is decoupled
  // Can verify activity status or no exception
  assertTrue(true);
  assertViewmodelOpsNotSent(ecTenantId);
  }

  private void validateCmnCfgCollectorMessage(String tenantId, String requestId, String instanceId) {
  final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
  assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();
  assertThat(cmnCfgCollectorMessage.getPayload())
  .matches(p -> p.getTenantId().equals(tenantId))
  .matches(p -> p.getRequestId().equals(requestId));
  assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
  .hasSize(1)
  .filteredOn(o -> o.getId().equals(instanceId)).first()
  .matches(o -> EsConstants.Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex()))
  .matches(o -> o.getOpType().equals(OpType.MOD))
  .matches(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(tenantId))
  .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())
  .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue());
  }

  private void assertViewmodelOpsNotSent(String tenantId) {
  messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }
} 