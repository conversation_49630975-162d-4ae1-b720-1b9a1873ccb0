package com.ruckus.cloud.wifi.integration.wifinetwork;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Eap;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Plmn;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20RoamConsortium;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.AAAWifiNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DpskWifiNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.GuestWifiNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Hotspot20WifiNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.OpenWifiNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.PskWifiNetworkGenerator;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.RadiusDecorator;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator.IndicativeSentences;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_FR_ALLOW_NETWORK_TYPE_MODIFICATION_TOGGLE)
class ConsumeUpdateWifiNetworkRequestWithTypeModificationTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  private String wifiNetworkId;

  @ApiAction.RequestParams
  private RequestParams requestParams() {
    return new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId);
  }

  @Payload("ToOpen")
  private final OpenWifiNetworkGenerator openWifiNetworkGenerator = Generators.openWifiNetwork()
      .setId(nullValue(String.class));

  @Payload("ToPsk")
  private final PskWifiNetworkGenerator pskWifiNetworkGenerator = Generators.pskWifiNetwork()
      .setId(nullValue(String.class));

  @Payload("ToAAA")
  private final AAAWifiNetworkGenerator aaaWifiNetworkGenerator = Generators.aaaWifiNetwork()
      .setId(nullValue(String.class));

  @Payload("ToGuest")
  private final GuestWifiNetworkGenerator guestWifiNetworkGenerator = Generators.guestWifiNetwork()
      .setId(nullValue(String.class));

  @Payload("ToDpsk")
  private final DpskWifiNetworkGenerator dpskWifiNetworkGenerator = Generators.dpskWifiNetwork()
      .setId(nullValue(String.class));

  @Payload("ToHotspot20")
  private final Hotspot20WifiNetworkGenerator hotspotWifiNetworkGenerator = Generators.hotspot20WifiNetwork()
      .setId(nullValue(String.class));

  @Nested
  @DisplayNameGeneration(IndicativeSentences.class)
  class GivenOpenNetwork {

    @BeforeEach
    void givenOneOpenNetworkPersistedInDb(final Tenant tenant) {
      final var openNetwork = network(OpenNetwork.class).generate();
      openNetwork.getWlan().setNetwork(openNetwork);
      wifiNetworkId = repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId()).getId();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToPsk") com.ruckus.cloud.wifi.eda.viewmodel.PskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAASuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToAAA") com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAAAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToGuest") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToDpsk") com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20Successfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToHotspot20") com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20WifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20AndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }
  }

  @Nested
  @DisplayNameGeneration(IndicativeSentences.class)
  class GivenPskNetwork {

    @BeforeEach
    void givenOnePskNetworkPersistedInDb(final Tenant tenant) {
      final var pskNetwork = network(PskNetwork.class).generate();
      pskNetwork.getWlan().setNetwork(pskNetwork);
      repositoryUtil.createOrUpdate(pskNetwork, tenant.getId(), randomTxId());
      wifiNetworkId = repositoryUtil.createOrUpdate(pskNetwork, tenant.getId(), randomTxId()).getId();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToOpen") com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAASuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToAAA") com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAAAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToGuest") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToDpsk") com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20Successfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToHotspot20") com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20WifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20AndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }
  }

  @Nested
  @DisplayNameGeneration(IndicativeSentences.class)
  class GivenAAANetwork {

    @BeforeEach
    void givenOneAAANetworkPersistedInDb(final Tenant tenant,
        final @RadiusDecorator(type = RadiusProfileTypeEnum.AUTHENTICATION) Radius authRadius,
        final @RadiusDecorator(type = RadiusProfileTypeEnum.ACCOUNTING) Radius accountingRadius) {
      final var aaaNetwork = network(AAANetwork.class).generate();
      aaaNetwork.setAuthRadius(authRadius);
      aaaNetwork.setAccountingRadius(accountingRadius);
      aaaNetwork.getWlan().setNetwork(aaaNetwork);
      wifiNetworkId = repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId(), randomTxId()).getId();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToOpen") com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToPsk") com.ruckus.cloud.wifi.eda.viewmodel.PskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToGuest") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToDpsk") com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20Successfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToHotspot20") com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20WifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20AndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }
  }

  @Nested
  @DisplayNameGeneration(IndicativeSentences.class)
  class GivenGuestNetwork {

    @BeforeEach
    void givenOneGuestNetworkPersistedInDb(final Tenant tenant) {
      final GuestNetwork guestNetwork = (GuestNetwork) network(GuestNetwork.class).generate();
      final GuestPortal guestPortal = guestPortal(GuestNetworkTypeEnum.HostApproval).generate();
      guestNetwork.setPortalServiceProfileId(randomId());
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      wifiNetworkId = repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId()).getId();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToOpen") com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur PSQLException: ERROR: update or delete on table "network" violates foreign key constraint "fk_b6ogltx8lympobm8jfx6594ty" on table "guest_portal"
    @Disabled("Will occur PSQLException: ERROR: update or delete on table \"network\" violates foreign key constraint \"fk_b6ogltx8lympobm8jfx6594ty\" on table \"guest_portal\"")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToPsk") com.ruckus.cloud.wifi.eda.viewmodel.PskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur PSQLException: ERROR: update or delete on table "network" violates foreign key constraint "fk_b6ogltx8lympobm8jfx6594ty" on table "guest_portal"
    @Disabled("Will occur PSQLException: ERROR: update or delete on table \"network\" violates foreign key constraint \"fk_b6ogltx8lympobm8jfx6594ty\" on table \"guest_portal\"")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAASuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToAAA") com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur PSQLException: ERROR: update or delete on table "network" violates foreign key constraint "fk_b6ogltx8lympobm8jfx6594ty" on table "guest_portal"
    @Disabled("Will occur PSQLException: ERROR: update or delete on table \"network\" violates foreign key constraint \"fk_b6ogltx8lympobm8jfx6594ty\" on table \"guest_portal\"")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAAAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToDpsk") com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur PSQLException: ERROR: update or delete on table "network" violates foreign key constraint "fk_b6ogltx8lympobm8jfx6594ty" on table "guest_portal"
    @Disabled("Will occur PSQLException: ERROR: update or delete on table \"network\" violates foreign key constraint \"fk_b6ogltx8lympobm8jfx6594ty\" on table \"guest_portal\"")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20Successfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToHotspot20") com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20WifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur PSQLException: ERROR: update or delete on table "network" violates foreign key constraint "fk_b6ogltx8lympobm8jfx6594ty" on table "guest_portal"
    @Disabled("Will occur PSQLException: ERROR: update or delete on table \"network\" violates foreign key constraint \"fk_b6ogltx8lympobm8jfx6594ty\" on table \"guest_portal\"")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20AndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }
  }

  @Nested
  @DisplayNameGeneration(IndicativeSentences.class)
  class GivenDpskNetwork {

    @BeforeEach
    void givenOneDpskNetworkPersistedInDb(final Tenant tenant) {
      final var authRadius = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());

      final var dpskNetwork = network(DpskNetwork.class).generate();
      dpskNetwork.getWlan().setNetwork(dpskNetwork);
      dpskNetwork.setAuthRadius(authRadius);
      authRadius.setAuthNetworks(List.of(dpskNetwork));
      wifiNetworkId = repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId()).getId();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToOpen") com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToPsk") com.ruckus.cloud.wifi.eda.viewmodel.PskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAASuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToAAA") com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAAAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToGuest") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20Successfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToHotspot20") com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20WifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToHotspot20"))
    void updateTypeToHotspot20AndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }
  }

  @Nested
  @DisplayNameGeneration(IndicativeSentences.class)
  class GivenHotspot20Network {

    @BeforeEach
    void givenOneHotspot20NetworkPersistedInDb(final Tenant tenant) {
      final var hotspot20Operator = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20Operator().generate();
      final var hotspot20FriendlyName = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20FriendlyName().generate();
      hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));
      hotspot20FriendlyName.setOperator(hotspot20Operator);

      final var hotspot20IdentityProvider = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20IdentityProvider()
          .setRoamConsortiumOis(nullValue(Hotspot20RoamConsortium.class).toListGenerator(0))
          .setPlmns(nullValue(Hotspot20Plmn.class).toListGenerator(0)).generate();
      final var hotspot20NaiRealm = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20NaiRealm()
          .setEaps(nullValue(Hotspot20Eap.class).toListGenerator(0)).generate();
      hotspot20IdentityProvider.setNaiRealms(List.of(hotspot20NaiRealm));
      hotspot20NaiRealm.setIdentityProvider(hotspot20IdentityProvider);

      final var connectionCapability = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20ConnectionCapability().generate();
      final var networkHotspot20Settings = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkHotspot20Settings().generate();
      connectionCapability.setNetworkHotspot20Settings(networkHotspot20Settings);
      networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability));
      networkHotspot20Settings.setOperator(hotspot20Operator);

      final var hotspot20Network = (Hotspot20Network) network(Hotspot20Network.class).generate();
      hotspot20Network.getWlan().setNetwork(hotspot20Network);
      hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

      hotspot20IdentityProvider.setNetworkHotspot20SettingsList(List.of(networkHotspot20Settings));

      repositoryUtil.createOrUpdate(hotspot20Operator, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20FriendlyName, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20IdentityProvider, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20NaiRealm, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(connectionCapability, tenant.getId(), randomTxId());
      wifiNetworkId = repositoryUtil.createOrUpdate(hotspot20Network, tenant.getId(), randomTxId()).getId();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToOpen") com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToOpen"))
    void updateTypeToOpenAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToPsk") com.ruckus.cloud.wifi.eda.viewmodel.PskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToPsk"))
    void updateTypeToPskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAASuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToAAA") com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToAAA"))
    void updateTypeToAAAAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToGuest") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    // FIXME: Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate
    @Disabled("Will occur NPE in WifiNetworkServiceCtrlImpl.handleRadiusScopeUpdate")
    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToGuest"))
    void updateTypeToGuestAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("ToDpsk") com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork payload) {
      validateResult(txCtx, apiAction, wifiNetworkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("ToDpsk"))
    void updateTypeToDpskAndDeleteSuccessfully(TxCtx txCtx) {
      final String deleteTxId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(), deleteTxId,
          CfgAction.DELETE_WIFI_NETWORK, randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId),
          StringUtils.EMPTY);

      validateActivityMessages(txCtx.getTenant(), deleteTxId, ApiFlowNames.DELETE_WIFI_NETWORK);
    }
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiNetwork payload) {
    validateRepositoryData(wifiNetworkId, payload);
// TODO: validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(wifiNetworkId), payload);
// TODO: validateCmnCfgCollectorMessages(txCtx, apiAction, List.of(wifiNetworkId));
    validateActivityMessages(txCtx, ApiFlowNames.UPDATE_WIFI_NETWORK);
  }

  private void validateRepositoryData(String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiNetwork payload) {

    assertThat(repositoryUtil.find(OpenNetwork.class, wifiNetworkId))
        .isNotNull()
        .satisfies(network -> assertSoftly(softly -> {
          softly.assertThat(network.getId()).isEqualTo(wifiNetworkId);
          softly.assertThat(network.getType()).isEqualTo(NetworkTypeEnum.OPEN);
          softly.assertThat(network.getName()).isEqualTo(payload.getName());
          softly.assertThat(network.getDescription()).isEqualTo(payload.getDescription());
          softly.assertThat(network.getWlan().getAdvancedCustomization().getBssPriority())
              .isEqualTo(payload.getWlan().getAdvancedCustomization().getBssPriority());
        }));
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.PskWifiNetwork payload) {
    validateRepositoryData(wifiNetworkId, payload);
// TODO: validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(wifiNetworkId), payload);
// TODO: validateCmnCfgCollectorMessages(txCtx, apiAction, List.of(wifiNetworkId));
    validateActivityMessages(txCtx, ApiFlowNames.UPDATE_WIFI_NETWORK);
  }

  private void validateRepositoryData(String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.PskWifiNetwork payload) {

    assertThat(repositoryUtil.find(PskNetwork.class, wifiNetworkId))
        .isNotNull()
        .satisfies(network -> assertSoftly(softly -> {
          softly.assertThat(network.getId()).isEqualTo(wifiNetworkId);
          softly.assertThat(network.getType()).isEqualTo(NetworkTypeEnum.PSK);
          softly.assertThat(network.getName()).isEqualTo(payload.getName());
          softly.assertThat(network.getDescription()).isEqualTo(payload.getDescription());
          softly.assertThat(network.getWlan().getAdvancedCustomization().getBssPriority())
              .isEqualTo(payload.getWlan().getAdvancedCustomization().getBssPriority());
        }));
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork payload) {
    validateRepositoryData(wifiNetworkId, payload);
// TODO: validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(wifiNetworkId), payload);
// TODO: validateCmnCfgCollectorMessages(txCtx, apiAction, List.of(wifiNetworkId));
    validateActivityMessages(txCtx, ApiFlowNames.UPDATE_WIFI_NETWORK);
  }

  private void validateRepositoryData(String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork payload) {

    assertThat(repositoryUtil.find(AAANetwork.class, wifiNetworkId))
        .isNotNull()
        .satisfies(network -> assertSoftly(softly -> {
          softly.assertThat(network.getId()).isEqualTo(wifiNetworkId);
          softly.assertThat(network.getType()).isEqualTo(NetworkTypeEnum.AAA);
          softly.assertThat(network.getName()).isEqualTo(payload.getName());
          softly.assertThat(network.getDescription()).isEqualTo(payload.getDescription());
          softly.assertThat(network.getWlan().getAdvancedCustomization().getBssPriority())
              .isEqualTo(payload.getWlan().getAdvancedCustomization().getBssPriority());
        }));
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {
    validateRepositoryData(wifiNetworkId, payload);
// TODO: validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(wifiNetworkId), payload);
// TODO: validateCmnCfgCollectorMessages(txCtx, apiAction, List.of(wifiNetworkId));
    validateActivityMessages(txCtx, ApiFlowNames.UPDATE_WIFI_NETWORK);
  }

  private void validateRepositoryData(String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payload) {

    assertThat(repositoryUtil.find(GuestNetwork.class, wifiNetworkId))
        .isNotNull()
        .satisfies(network -> assertSoftly(softly -> {
          softly.assertThat(network.getId()).isEqualTo(wifiNetworkId);
          softly.assertThat(network.getType()).isEqualTo(NetworkTypeEnum.GUEST);
          softly.assertThat(network.getName()).isEqualTo(payload.getName());
          softly.assertThat(network.getDescription()).isEqualTo(payload.getDescription());
          softly.assertThat(network.getWlan().getAdvancedCustomization().getBssPriority())
              .isEqualTo(payload.getWlan().getAdvancedCustomization().getBssPriority());
        }));
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork payload) {
    validateRepositoryData(wifiNetworkId, payload);
// TODO: validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(wifiNetworkId), payload);
// TODO: validateCmnCfgCollectorMessages(txCtx, apiAction, List.of(wifiNetworkId));
    validateActivityMessages(txCtx, ApiFlowNames.UPDATE_WIFI_NETWORK);
  }

  private void validateRepositoryData(String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork payload) {

    assertThat(repositoryUtil.find(DpskNetwork.class, wifiNetworkId))
        .isNotNull()
        .satisfies(network -> assertSoftly(softly -> {
          softly.assertThat(network.getId()).isEqualTo(wifiNetworkId);
          softly.assertThat(network.getType()).isEqualTo(NetworkTypeEnum.DPSK);
          softly.assertThat(network.getName()).isEqualTo(payload.getName());
          softly.assertThat(network.getDescription()).isEqualTo(payload.getDescription());
          softly.assertThat(network.getWlan().getAdvancedCustomization().getBssPriority())
              .isEqualTo(payload.getWlan().getAdvancedCustomization().getBssPriority());
        }));
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20WifiNetwork payload) {
    validateRepositoryData(wifiNetworkId, payload);
// TODO: validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(wifiNetworkId), payload);
// TODO: validateCmnCfgCollectorMessages(txCtx, apiAction, List.of(wifiNetworkId));
    validateActivityMessages(txCtx, ApiFlowNames.UPDATE_WIFI_NETWORK);
  }

  private void validateRepositoryData(String wifiNetworkId,
      com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20WifiNetwork payload) {

    assertThat(repositoryUtil.find(Hotspot20Network.class, wifiNetworkId))
        .isNotNull()
        .satisfies(network -> assertSoftly(softly -> {
          softly.assertThat(network.getId()).isEqualTo(wifiNetworkId);
          softly.assertThat(network.getType()).isEqualTo(NetworkTypeEnum.HOTSPOT20);
          softly.assertThat(network.getName()).isEqualTo(payload.getName());
          softly.assertThat(network.getDescription()).isEqualTo(payload.getDescription());
          softly.assertThat(network.getWlan().getAdvancedCustomization().getBssPriority())
              .isEqualTo(payload.getWlan().getAdvancedCustomization().getBssPriority());
        }));
  }

  private void validateActivityMessages(TxCtx txCtx, String apiFlowName) {
    validateActivityMessages(txCtx.getTenant(), txCtx.getTxId(), apiFlowName);
  }

  private void validateActivityMessages(String tenantId, String requestId, String apiFlowName) {
    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 0)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
            .isEmpty());
  }
}
