package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.ap;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.multicastDnsProxyServiceProfile;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.MulticastDnsProxyServiceProfileApGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.proto.MulticastDnsProxyServiceProfilePostMigrationJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("MulticastDnsProxyServiceProfileTest")
@WifiIntegrationTest
public class ConsumeMulticastDnsProxyServiceProfilePostMigrationAsyncJobTest {

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class GivenMulticastDnsProxyServiceProfilePersistedInDb {

    private MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile;

    @BeforeEach
    void givenMulticastDnsProxyServiceProfileApPersistedInDb(final Tenant tenant) {
      final MulticastDnsProxyServiceProfile dnsProxyServiceProfile = multicastDnsProxyServiceProfile()
          .setTenant(always(tenant))
          .setId(always(randomId()))
          .generate();
      dnsProxyServiceProfile.getRules()
          .forEach(rule -> rule.setMulticastDnsProxyServiceProfile(dnsProxyServiceProfile));

      multicastDnsProxyServiceProfile = repositoryUtil.createOrUpdate(dnsProxyServiceProfile, tenant.getId(), randomTxId());
    }

    @Test
    void whenConsumeMulticastDnsProxyServiceProfilePostMigrationJob(final Tenant tenant) {
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
          .setMulticastDnsProxyServiceProfilePostMigrationJob(
              MulticastDnsProxyServiceProfilePostMigrationJob.newBuilder()).build());

      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());
    }

    @Nested
    class GivenMulticastDnsProxyServiceProfileApPersistedInDb {

      private String venueId;
      private List<String> apSerialNumbers = new ArrayList<>();
      private List<Ap> aps;
      private List<MulticastDnsProxyServiceProfileAp> multicastDnsProxyServiceProfileAps = new ArrayList<>();

      @BeforeEach
      void given10ApsPersistedInDb(final Tenant tenant, final ApGroup apGroup) {
        venueId = apGroup.getVenue().getId();

        aps = ap().setTenant(always(tenant)).setApGroup(always(apGroup))
            .generate(10, ap -> repositoryUtil.createOrUpdate(ap, tenant.getId(), randomTxId()));

        aps.forEach(ap -> {
          apSerialNumbers.add(ap.getId());
          multicastDnsProxyServiceProfileAps.add(repositoryUtil.createOrUpdate(
              new MulticastDnsProxyServiceProfileApGenerator()
                  .setAp(always(ap))
                  .setMulticastDnsProxyServiceProfile(always(multicastDnsProxyServiceProfile))
                  .generate(), tenant.getId(), randomTxId()));
        });
        apSerialNumbers = apSerialNumbers.stream().sorted().toList();
      }

      @AfterEach
      void teardown(final Tenant tenant) {
        aps.forEach(ap -> {
          repositoryUtil.remove(ap, tenant.getId(), false);
        });

        multicastDnsProxyServiceProfileAps.forEach(m -> {
          repositoryUtil.remove(m, tenant.getId(), false);
        });
      }

      @Test
      void whenConsumeMulticastDnsProxyServiceProfilePostMigrationJob(final Tenant tenant) {
        final var requestId = randomTxId();

        messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
            .setMulticastDnsProxyServiceProfilePostMigrationJob(
                MulticastDnsProxyServiceProfilePostMigrationJob.newBuilder()).build());

        final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenant.getId());
        assertThat(cmnCfgCollectorMessage).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull();

        assertThat(cmnCfgCollectorMessage.getPayload())
            .satisfies(msg -> {
              assertThat(msg.getTenantId()).isEqualTo(tenant.getId());
              assertThat(msg.getRequestId()).startsWith(requestId);
            })
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
            .filteredOn(op -> Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getId()).isEqualTo(multicastDnsProxyServiceProfile.getId());
              assertThat(op.getOpType()).isEqualTo(OpType.MOD);
              assertThat(op.getDocMap())
                  .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenant.getId()))
                  .containsEntry(Key.ID, ValueUtils.stringValue(multicastDnsProxyServiceProfile.getId()))
                  .containsEntry(Key.NAME, ValueUtils.stringValue(multicastDnsProxyServiceProfile.getServiceName()))
                  .satisfies(docMap -> {
                    assertThat(docMap.get(Key.RULES))
                        .isNotNull()
                        .extracting(value -> value.getListValue().getValuesList(),
                            InstanceOfAssertFactories.list(
                                com.ruckus.cloud.events.gpb.Value.class
                            ))
                        .isNotEmpty().hasSize(multicastDnsProxyServiceProfile.getRules().size());
                    assertThat(docMap.get(Key.ACTIVATIONS))
                        .isNotNull()
                        .extracting(value -> value.getListValue().getValuesList(),
                            InstanceOfAssertFactories.list(
                                com.ruckus.cloud.events.gpb.Value.class
                            ))
                        .isNotEmpty().hasSize(1)
                        .extracting(
                            v -> v.getStructValue().getFieldsOrThrow(Key.AP_SERIAL_NUMBERS)
                                .getListValue().getValuesList()
                                .stream().map(Value::getStringValue).sorted().toList(),
                            v -> v.getStructValue().getFieldsOrThrow(Key.VENUE_ID)
                                .getStringValue())
                        .containsExactly(tuple(apSerialNumbers, venueId));
                  });
            });
      }
    }
  }
}
