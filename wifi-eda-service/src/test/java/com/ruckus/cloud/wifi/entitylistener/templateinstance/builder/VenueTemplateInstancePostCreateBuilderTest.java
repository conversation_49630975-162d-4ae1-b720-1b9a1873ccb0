package com.ruckus.cloud.wifi.entitylistener.templateinstance.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_PORT_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.EthernetPortProfileTestFixture.randomEthernetPortProfileTemplate;
import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomOpenOweNetwork;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplate;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenue;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplate;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Answers.RETURNS_SELF;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslog;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.ApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupGenerator;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.TemplateInstancePostCreatePackage;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateApGroupOnWifiNetworkRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateDhcpConfigServiceProfileAndUpdateSettingsRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateRadiusServerProfileOnVenueRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateRoguePolicyOnVenueRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateSyslogServerProfileOnVenueRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateVlanPoolProfileOnApGroupRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.ActivateWifiNetworkOnVenueRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.UpdateApGroupSettingsOnWifiNetworkRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.UpdateVenueWifiNetworkSettingsRequestBuilder;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.VenueRoguePolicySettingsRequestBuilder;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.ExtendedTemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.validator.DhcpServiceValidator;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class VenueTemplateInstancePostCreateBuilderTest {

  @SpyBean
  private VenueTemplateInstancePostCreateBuilder unit;
  @MockBean
  private NetworkVenueRepository networkVenueRepository;
  @MockBean
  private VenueRepository venueRepository;
  @MockBean(answer = RETURNS_SELF)
  private ActivateDhcpConfigServiceProfileAndUpdateSettingsRequestBuilder activateDhcpConfigServiceProfileAndUpdateSettingsRequestBuilder;
  @MockBean
  private DhcpServiceValidator dhcpServiceValidator;
  @MockBean(answer = RETURNS_SELF)
  private VenueRoguePolicySettingsRequestBuilder venueRoguePolicySettingsRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateRoguePolicyOnVenueRequestBuilder activateRoguePolicyOnVenueRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateSyslogServerProfileOnVenueRequestBuilder activateSyslogServerProfileOnVenueRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateWifiNetworkOnVenueRequestBuilder activateWifiNetworkOnVenueRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private UpdateVenueWifiNetworkSettingsRequestBuilder updateVenueWifiNetworkSettingsRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateApGroupOnWifiNetworkRequestBuilder activateApGroupOnWifiNetworkRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private UpdateApGroupSettingsOnWifiNetworkRequestBuilder updateApGroupSettingsOnWifiNetworkRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateVlanPoolProfileOnApGroupRequestBuilder activateVlanPoolProfileOnApGroupRequestBuilder;
  @MockBean(answer = RETURNS_SELF)
  private ActivateRadiusServerProfileOnVenueRequestBuilder activateRadiusServerProfileOnVenueRequestBuilder;

  @Test
  void testEntityClass() {
    assertThat(unit.entityClass())
        .isEqualTo(Venue.class);
  }

  @Test
  void testIsTemplateInstance() {
    var venue = new Venue(randomId());
    assertThat(unit.isTemplateInstance(venue))
        .isFalse();
    venue.setTemplateId(randomId());
    assertThat(unit.isTemplateInstance(venue))
        .isTrue();
  }

  @Test
  void testBuild_getEmptyList_whenActionIsNotAdd() {
    var txEntity = new TxEntity<>(new Venue(randomId()), EntityAction.MODIFY);

    assertThat(unit.build(txEntity))
        .isEmpty();
  }

  @Test
  void testBuild_getEmptyList_whenVenueIsNotTemplate() {
    var venue = new Venue(randomId());
    var txEntity = new TxEntity<>(venue, EntityAction.ADD);

    assertThat(unit.build(txEntity))
        .isEmpty();
  }

  @Test
  void testDoBuild_getEmptyList_whenVenueTemplateNotFound() {
    var venue = new Venue(randomId());
    venue.setTemplateId(randomId());
    doReturn(Optional.empty()).when(venueRepository).findTemplateById(anyString());

    assertThat(unit.doBuild(venue))
        .isEmpty();
  }

  @Test
  @FeatureFlag(enable = WIFI_ETHERNET_PORT_PROFILE_TEMPLATE)
  void testBuild() {
    final var tenantId = randomId();
    var authRadius = new Radius(randomId());
    var venueTemplate = new Venue(randomId());
    venueTemplate.setTenant(new Tenant(tenantId));
    venueTemplate.setIsTemplate(true);
    venueTemplate.setDhcpConfigServiceProfile(new DhcpConfigServiceProfile(randomId()));
    VenueSyslog venueSyslog = new VenueSyslog();
    venueSyslog.setEnabled(true);
    venueTemplate.setSyslog(venueSyslog);
    venueTemplate.setSyslogServerProfile(new SyslogServerProfile(randomId()));
    VenueRogueAp venueRogueAp = new VenueRogueAp();
    venueRogueAp.setEnabled(true);
    venueTemplate.setRogueAp(venueRogueAp);
    venueTemplate.setAuthRadius(authRadius);
    var venue = new Venue(randomId());
    venue.setTemplateId(venueTemplate.getId());
    var ethernetPortProfileTemplate = randomEthernetPortProfileTemplate(new Tenant(tenantId));
    VenueApModelSpecificAttributes modelSpecificAttribute = new VenueApModelSpecificAttributes();
    VenueLanPort lanPort = new VenueLanPort();
    lanPort.setEnabled(true);
    lanPort.setApLanPortProfile(ethernetPortProfileTemplate);
    lanPort.setPortId("1");
    modelSpecificAttribute.setModel("R310");
    modelSpecificAttribute.setLanPorts(List.of(lanPort));
    venueTemplate.setModelSpecificAttributes(List.of(modelSpecificAttribute));

    var txEntity = new TxEntity<>(venue, EntityAction.ADD);

    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setTenant(new Tenant(tenantId));
    networkVenue.setVenue(venue);
    doReturn(Optional.of(venueTemplate)).when(venueRepository)
        .findTemplateById(eq(venue.getTemplateId()));
    doReturn(List.of(networkVenue)).when(networkVenueRepository)
        .findByVenueIdAndTenantIdAndIsTemplate(eq(venue.getTemplateId()),
            eq(venueTemplate.getTenant().getId()));
    TemplateInstancePostCreatePackage mockTemplateInstancePostCreatePackage = mockPackage();
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage)).when(
            activateDhcpConfigServiceProfileAndUpdateSettingsRequestBuilder)
        .build(eq(venueTemplate));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage)).when(
            venueRoguePolicySettingsRequestBuilder)
        .build(eq(venueTemplate));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage)).when(
            activateRoguePolicyOnVenueRequestBuilder)
        .build(eq(venueTemplate));
    doReturn(Optional.of(mockTemplateInstancePostCreatePackage)).when(
            activateSyslogServerProfileOnVenueRequestBuilder)
        .build(eq(venueTemplate));
    doReturn(List.of(mockTemplateInstancePostCreatePackage)).when(
            activateRadiusServerProfileOnVenueRequestBuilder)
        .buildList(eq(venueTemplate));

    TxCtxHolder.TxCtx txCtx =
        new TxCtxHolder.TxCtx(TxCtxHolder.tenantId(), TxCtxHolder.txId() + "$AddVenueByTemplate$AddVenueByTemplateInWifi",
            TxCtxHolder.get().getUserName(), TxCtxHolder.get().getFlowName());
    TxCtxHolder.set(txCtx);

    var result = unit.build(txEntity);
    assertThat(result).hasSize(5);
  }

  @Test
  void testBuild_NetworkVenuePostCreatePackages_WhenActivateOnAllApGroups() {
    final var venueTemplate = randomVenueTemplate(new Tenant(TxCtxHolder.tenantId()));
    final var venue = randomVenue(new Tenant(TxCtxHolder.tenantId()),
        (e) -> e.setTemplateId(venueTemplate.getId()));

    var txEntity = new TxEntity<>(venue, EntityAction.ADD);

    final var networkVenueTemplate = randomNetworkVenueTemplate(venue);
    networkVenueTemplate.setNetworkApGroups(List.of(
        new NetworkApGroupGenerator()
            .setTenant(always(venueTemplate.getTenant()))
            .setApGroup(new ApGroupGenerator())
            .setNetworkVenue(always(networkVenueTemplate)).generate(),
        new NetworkApGroupGenerator()
            .setTenant(always(venueTemplate.getTenant()))
            .setApGroup(new ApGroupGenerator())
            .setNetworkVenue(always(networkVenueTemplate)).generate()));

    doReturn(Optional.of(venueTemplate)).when(venueRepository)
        .findTemplateById(eq(venue.getTemplateId()));
    doReturn(List.of(networkVenueTemplate)).when(networkVenueRepository)
        .findByVenueIdAndTenantIdAndIsTemplate(eq(venue.getTemplateId()),
            eq(venueTemplate.getTenant().getId()));
    final var activateWifiNetworkOnVenue = mockPackage();
    final var updateVenueWifiNetworkSetting = mockPackage();
    final var updateApGroupSetting = mockPackage();
    doReturn(Optional.of(updateVenueWifiNetworkSetting)).when(
            updateVenueWifiNetworkSettingsRequestBuilder)
        .build(eq(networkVenueTemplate));
    doReturn(Optional.of(activateWifiNetworkOnVenue)).when(
            activateWifiNetworkOnVenueRequestBuilder)
        .build(eq(networkVenueTemplate));
    doReturn(Optional.of(updateApGroupSetting))
        .when(updateApGroupSettingsOnWifiNetworkRequestBuilder)
        .build(any());

    var result = unit.build(txEntity);
    assertThat(result).containsOnly(activateWifiNetworkOnVenue, updateVenueWifiNetworkSetting,
        updateApGroupSetting);
    assertThat(result).filteredOn( e -> e == activateWifiNetworkOnVenue).hasSize(1);
    assertThat(result).filteredOn( e -> e == updateVenueWifiNetworkSetting).hasSize(1);
    assertThat(result).filteredOn( e -> e == updateApGroupSetting).hasSize(2);
  }

  @Test
  void testBuild_NetworkVenuePostCreatePackages_WhenActivateOnSpecificApGroups() {
    final var venueTemplate = randomVenueTemplate(new Tenant(TxCtxHolder.tenantId()));
    final var venue = randomVenue(new Tenant(TxCtxHolder.tenantId()),
        (e) -> e.setTemplateId(venueTemplate.getId()));

    var txEntity = new TxEntity<>(venue, EntityAction.ADD);

    final var networkVenueTemplate = randomNetworkVenueTemplate(venue);
    networkVenueTemplate.setIsAllApGroups(false);
    networkVenueTemplate.setNetworkApGroups(List.of(
        new NetworkApGroupGenerator()
            .setTenant(always(venueTemplate.getTenant()))
            .setApGroup(new ApGroupGenerator())
            .setNetworkVenue(always(networkVenueTemplate)).generate(),
        new NetworkApGroupGenerator()
            .setTenant(always(venueTemplate.getTenant()))
            .setApGroup(new ApGroupGenerator())
            .setNetworkVenue(always(networkVenueTemplate)).generate()));

    doReturn(Optional.of(venueTemplate)).when(venueRepository)
        .findTemplateById(eq(venue.getTemplateId()));
    doReturn(List.of(networkVenueTemplate)).when(networkVenueRepository)
        .findByVenueIdAndTenantIdAndIsTemplate(eq(venue.getTemplateId()),
            eq(venueTemplate.getTenant().getId()));
    final var activateWifiNetworkOnVenue = mockPackage();
    final var updateVenueWifiNetworkSetting = mockPackage();
    final var activateApGroupOnWifiNetwork = mockPackage();
    final var updateApGroupSetting = mockPackage();
    doReturn(Optional.of(activateApGroupOnWifiNetwork)).when(
            activateApGroupOnWifiNetworkRequestBuilder)
        .build(any());
    doReturn(Optional.of(updateVenueWifiNetworkSetting)).when(
            updateVenueWifiNetworkSettingsRequestBuilder)
        .build(eq(networkVenueTemplate));
    doReturn(Optional.of(activateWifiNetworkOnVenue)).when(
            activateWifiNetworkOnVenueRequestBuilder)
        .build(eq(networkVenueTemplate));
    doReturn(Optional.of(updateApGroupSetting))
        .when(updateApGroupSettingsOnWifiNetworkRequestBuilder)
        .build(any());

    var result = unit.build(txEntity);
    assertThat(result).containsOnly(activateWifiNetworkOnVenue, activateApGroupOnWifiNetwork,
        updateApGroupSetting);
    assertThat(result).filteredOn( e -> e == activateWifiNetworkOnVenue).hasSize(1);
    assertThat(result).filteredOn( e -> e == updateVenueWifiNetworkSetting).hasSize(1);
    assertThat(result).filteredOn( e -> e == activateApGroupOnWifiNetwork).hasSize(2);
    assertThat(result).filteredOn( e -> e == updateApGroupSetting).hasSize(2);
  }

  @Test
  void testBuild_NetworkVenuePostCreatePackages_WhenActivateVlanPoolsOnSpecificApGroups() {
    final var venueTemplate = randomVenueTemplate(new Tenant(TxCtxHolder.tenantId()));
    final var venue = randomVenue(new Tenant(TxCtxHolder.tenantId()),
        (e) -> e.setTemplateId(venueTemplate.getId()));
    final var oweNetworkTemplate = randomOpenOweNetwork(venue.getTenant(),
        network -> {
          network.setIsOweMaster(true);
          network.setIsTemplate(true);
        });
    final var oweSlaveNetworkTemplate = randomOpenOweNetwork(venue.getTenant(),
        network -> {
          network.setIsOweMaster(false);
          network.setOwePairNetworkId(oweNetworkTemplate.getId());
          network.setIsTemplate(true);
        });

    var txEntity = new TxEntity<>(venue, EntityAction.ADD);

    final var networkVenueMasterTemplate = randomNetworkVenueTemplate(venue, oweNetworkTemplate);
    networkVenueMasterTemplate.setIsAllApGroups(true);
    networkVenueMasterTemplate.setNetworkApGroups(List.of());
    final var networkVenueSlaveTemplate = randomNetworkVenueTemplate(venue, oweSlaveNetworkTemplate);

    doReturn(Optional.of(venueTemplate)).when(venueRepository)
        .findTemplateById(eq(venue.getTemplateId()));
    doReturn(List.of(networkVenueMasterTemplate, networkVenueSlaveTemplate)).when(networkVenueRepository)
        .findByVenueIdAndTenantIdAndIsTemplate(eq(venue.getTemplateId()),
            eq(venueTemplate.getTenant().getId()));
    final var activateWifiNetworkOnVenue = mockPackage();

    doReturn(Optional.of(activateWifiNetworkOnVenue)).when(
            activateWifiNetworkOnVenueRequestBuilder)
        .build(eq(networkVenueMasterTemplate));

    var result = unit.build(txEntity);
    assertThat(result).containsOnly(activateWifiNetworkOnVenue);
    assertThat(result).filteredOn( e -> e == activateWifiNetworkOnVenue).hasSize(1);
  }

  @Test
  void testBuild_NetworkVenuePostCreatePackages_ShouldSkipSlaveNetworks() {
    final var venueTemplate = randomVenueTemplate(new Tenant(TxCtxHolder.tenantId()));
    final var venue = randomVenue(new Tenant(TxCtxHolder.tenantId()),
        (e) -> e.setTemplateId(venueTemplate.getId()));

    var txEntity = new TxEntity<>(venue, EntityAction.ADD);

    final var networkVenueTemplate = randomNetworkVenueTemplate(venue);
    networkVenueTemplate.setIsAllApGroups(false);
    networkVenueTemplate.setNetworkApGroups(List.of(
        new NetworkApGroupGenerator()
            .setTenant(always(venueTemplate.getTenant()))
            .setApGroup(new ApGroupGenerator())
            .setNetworkVenue(always(networkVenueTemplate)).generate(),
        new NetworkApGroupGenerator()
            .setTenant(always(venueTemplate.getTenant()))
            .setApGroup(new ApGroupGenerator())
            .setNetworkVenue(always(networkVenueTemplate)).generate()));

    doReturn(Optional.of(venueTemplate)).when(venueRepository)
        .findTemplateById(eq(venue.getTemplateId()));
    doReturn(List.of(networkVenueTemplate)).when(networkVenueRepository)
        .findByVenueIdAndTenantIdAndIsTemplate(eq(venue.getTemplateId()),
            eq(venueTemplate.getTenant().getId()));
    final var activateWifiNetworkOnVenue = mockPackage();
    final var updateVenueWifiNetworkSetting = mockPackage();
    final var activateApGroupOnWifiNetwork = mockPackage();
    final var updateApGroupSetting = mockPackage();
    doReturn(Optional.of(activateApGroupOnWifiNetwork)).when(
            activateApGroupOnWifiNetworkRequestBuilder)
        .build(any());
    doReturn(Optional.of(updateVenueWifiNetworkSetting)).when(
            updateVenueWifiNetworkSettingsRequestBuilder)
        .build(eq(networkVenueTemplate));
    doReturn(Optional.of(activateWifiNetworkOnVenue)).when(
            activateWifiNetworkOnVenueRequestBuilder)
        .build(eq(networkVenueTemplate));
    doReturn(Optional.of(updateApGroupSetting))
        .when(updateApGroupSettingsOnWifiNetworkRequestBuilder)
        .build(any());

    var result = unit.build(txEntity);
    assertThat(result).containsOnly(activateWifiNetworkOnVenue, activateApGroupOnWifiNetwork,
        updateApGroupSetting);
    assertThat(result).filteredOn( e -> e == activateWifiNetworkOnVenue).hasSize(1);
    assertThat(result).filteredOn( e -> e == updateVenueWifiNetworkSetting).hasSize(1);
    assertThat(result).filteredOn( e -> e == activateApGroupOnWifiNetwork).hasSize(2);
    assertThat(result).filteredOn( e -> e == updateApGroupSetting).hasSize(2);
  }

  private TemplateInstancePostCreatePackage mockPackage() {
    return new TemplateInstancePostCreatePackage(null, new ExtendedTemplateInstanceCreateRequest(), null);
  }
}
