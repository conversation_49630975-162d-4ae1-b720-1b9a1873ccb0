package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.nuketenant.protobuf.message.Nuketenant.DeleteTenant;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.venue.proto.Address;
import com.ruckus.cloud.venue.proto.Operation;
import com.ruckus.cloud.venue.proto.VenueEvent;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.ApSnmpAgentProfileRestCtrl.ApSnmpAgentProfileMapper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV2Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV3Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SnmpNotificationTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.impl.ApSnmpAgentProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApSnmpAgentProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiIntegrationTest
@Sql("classpath:sql/vspot.sql")
public class ConsumeApSnmpAgentProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private ApSnmpAgentProfileServiceCtrlImpl agentProfileService;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Test
  void testAddApSnmpAgentProfileAndThenGetProfiles(ApSnmpAgentProfile apSnmpAgentProfile) {
    final var requestId = randomTxId();
    final var userName = randomName();

    var viewApSnmpAgentProfile = ApSnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2ApSnmpAgentProfile(apSnmpAgentProfile);

    viewApSnmpAgentProfile.setId(randomId());
    viewApSnmpAgentProfile.setPolicyName("name");
    viewApSnmpAgentProfile.setSnmpV2Agents(null);
    viewApSnmpAgentProfile.setSnmpV3Agents(null);

    messageUtil.sendWifiCfgRequest(
        apSnmpAgentProfile.getTenant().getId(), requestId,
        CfgAction.ADD_AP_SNMP_AGENT_PROFILE,
        userName, viewApSnmpAgentProfile);

    validateApSnmpAgentProfileResult(Action.ADD, apSnmpAgentProfile.getTenant().getId(), requestId,
        viewApSnmpAgentProfile);

    TxCtxHolder.set(new TxCtx(apSnmpAgentProfile.getTenant().getId(), randomTxId(), userName,
        "GetApSnmpAgentProfiles"));

    assertEquals(2, agentProfileService.getApSnmpAgentProfiles(Optional.empty()).size());
  }

  @Test
  void testApplySnmpAgentAndThenDeleteApVenue(Venue venue, Ap ap,
      ApSnmpAgentProfile apSnmpAgentProfile) {
    final var userName = randomName();

    var requestId = randomTxId();
    var tenantId = venue.getTenant().getId();

    var viewVenueSnmpAgent = new com.ruckus.cloud.wifi.eda.viewmodel.VenueSnmpAgent();
    viewVenueSnmpAgent.setEnableApSnmp(true);
    viewVenueSnmpAgent.setApSnmpAgentProfileId(apSnmpAgentProfile.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.UPDATE_VENUE_AP_SNMP_AGENT, userName,
        new RequestParams().addPathVariable("venueId", venue.getId()), viewVenueSnmpAgent);

    var viewApSnmpAgentProfile = ApSnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2ApSnmpAgentProfile(apSnmpAgentProfile);

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId, viewApSnmpAgentProfile,
        1, 1); // venues should be 1, aps should be 1

    requestId = randomTxId();

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.DELETE_AP, userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()), "");

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    validateApSnmpAgentProfileCmnCfgCollectorRequest(cmnCfgCollectorMessage, requestId, tenantId);

    requestId = randomTxId();

    VenueEvent event = VenueEvent.newBuilder().setTenantId(tenantId).addOperation(
        Operation.newBuilder().setAction(com.ruckus.cloud.venue.proto.Action.DELETE).setVenue(
            com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    // venues should be 0, aps should be 0
    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, venue.getTenant().getId(),
        requestId, viewApSnmpAgentProfile);
  }

  @Test
  void testApplySnmpAgentAndThenDeleteTenant(Tenant tenant, Venue venue, Ap ap,
      ApSnmpAgentProfile apSnmpAgentProfile) {
    final var userName = randomName();

    var requestId = randomTxId();
    var tenantId = tenant.getId();

    var viewVenueSnmpAgent = new com.ruckus.cloud.wifi.eda.viewmodel.VenueSnmpAgent();
    viewVenueSnmpAgent.setEnableApSnmp(true);
    viewVenueSnmpAgent.setApSnmpAgentProfileId(apSnmpAgentProfile.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.UPDATE_VENUE_AP_SNMP_AGENT, userName,
        new RequestParams().addPathVariable("venueId", venue.getId()), viewVenueSnmpAgent);

    var viewApSnmpAgent = new com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgent();
    viewApSnmpAgent.setEnableApSnmp(true);
    viewApSnmpAgent.setApSnmpAgentProfileId(apSnmpAgentProfile.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.UPDATE_AP_SNMP_AGENT, userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()), viewApSnmpAgent);

    var viewApSnmpAgentProfile = ApSnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2ApSnmpAgentProfile(apSnmpAgentProfile);

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId,
        viewApSnmpAgentProfile,
        1, 1); // venues should be 1, aps should be 1

    assertEquals(1, repositoryUtil.findAll(ApSnmpAgentProfile.class, tenantId).size());

    requestId = randomTxId();

    final var deleteTenantMessage = DeleteTenant.newBuilder().setTenantId(tenantId).build();

    messageUtil.sendDeleteTenant(requestId, deleteTenantMessage);

    assertEquals(0, repositoryUtil.findAll(ApSnmpAgentProfile.class, tenantId).size());
  }

  @Test
  void testApplySnmpAgentAndThenUpdateApVenueName(Venue venue, Ap ap,
      ApSnmpAgentProfile apSnmpAgentProfile) {
    final var userName = randomName();

    var requestId = randomTxId();
    var tenantId = venue.getTenant().getId();

    var viewVenueSnmpAgent = new com.ruckus.cloud.wifi.eda.viewmodel.VenueSnmpAgent();
    viewVenueSnmpAgent.setEnableApSnmp(true);
    viewVenueSnmpAgent.setApSnmpAgentProfileId(apSnmpAgentProfile.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.UPDATE_VENUE_AP_SNMP_AGENT, userName,
        new RequestParams().addPathVariable("venueId", venue.getId()), viewVenueSnmpAgent);

    var viewApSnmpAgentProfile = ApSnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2ApSnmpAgentProfile(apSnmpAgentProfile);

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId, viewApSnmpAgentProfile,
        1, 1); // venues should be 1, aps should be 1

    requestId = randomTxId();

    VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(com.ruckus.cloud.venue.proto.Action.MODIFY)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(venue.getId())
                    .setVenueName("venueNewName")
                    .setAddress(Address.newBuilder()
                        .setCountryCode(venue.getCountryCode())
                        .setTimezone(venue.getTimezone())
                        .setAddressLine(venue.getAddressLine())
                        .setLongitude(Double.parseDouble(venue.getDeviceGps().getLongitude()))
                        .setLatitude(Double.parseDouble(venue.getDeviceGps().getLatitude()))
                        .build())))
        .build();

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId, viewApSnmpAgentProfile,
        1, 1, Optional.of("venueNewName"), Optional.empty()); // venues should be 1, aps should be 1

    requestId = randomTxId();

    final var apRequest = new ApRequest();
    apRequest.setSerialNumber(ap.getId());
    apRequest.setName("apNewName");
    apRequest.setApGroupId(ap.getApGroup().getId());
    apRequest.setVenueId(ap.getApGroup().getVenue().getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId, viewApSnmpAgentProfile,
        1, 1, Optional.of("venueNewName"),
        Optional.of(apRequest.getName())); // venues should be 1, aps should be 1
  }

  @Tag("ApGroupTest")
  @Test
  void testApplyToApAndThenMoveVenue(Venue venue, ApGroup apGroup, Ap ap,
      ApSnmpAgentProfile apSnmpAgentProfile) {
    final var userName = randomName();

    var requestId = randomTxId();
    var tenantId = venue.getTenant().getId();

    var newVenue = VenueTestFixture.randomVenue(venue.getTenant());
    var newApGroup = ApGroupTestFixture.randomApGroup(newVenue);

    repositoryUtil.createOrUpdate(newVenue, tenantId, randomTxId());
    repositoryUtil.createOrUpdate(newApGroup, tenantId, randomTxId());

    var viewVenueSnmpAgent = new com.ruckus.cloud.wifi.eda.viewmodel.VenueSnmpAgent();
    viewVenueSnmpAgent.setEnableApSnmp(true);
    viewVenueSnmpAgent.setApSnmpAgentProfileId(apSnmpAgentProfile.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.UPDATE_VENUE_AP_SNMP_AGENT, userName,
        new RequestParams().addPathVariable("venueId", venue.getId()), viewVenueSnmpAgent);

    var viewApSnmpAgentProfile = ApSnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2ApSnmpAgentProfile(apSnmpAgentProfile);

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId, viewApSnmpAgentProfile,
        1, 1); // venues should be 1, aps should be 1

    requestId = randomTxId();

    final var apRequest = new ApRequest();
    apRequest.setSerialNumber(ap.getId());
    apRequest.setName(ap.getName());
    apRequest.setApGroupId(newApGroup.getId());
    apRequest.setVenueId(newApGroup.getVenue().getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId, viewApSnmpAgentProfile,
        1, 0); // venues should be 1, aps should be 0

    var apSnmpAgentProfile2 = ApSnmpAgentProfileTestFixture
        .randomApSnmpAgentProfile(venue.getTenant());

    repositoryUtil.createOrUpdate(apSnmpAgentProfile2, tenantId, randomTxId());

    var viewApSnmpAgentProfile2 = ApSnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2ApSnmpAgentProfile(apSnmpAgentProfile2);

    viewVenueSnmpAgent.setEnableApSnmp(true);
    viewVenueSnmpAgent.setApSnmpAgentProfileId(apSnmpAgentProfile2.getId());

    requestId = randomTxId();

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.UPDATE_VENUE_AP_SNMP_AGENT, userName,
        new RequestParams().addPathVariable("venueId", newVenue.getId()), viewVenueSnmpAgent);

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId, viewApSnmpAgentProfile2,
        1, 1); // venues should be 1, aps should be 1

    requestId = randomTxId();

    apRequest.setSerialNumber(ap.getId());
    apRequest.setName(ap.getName());
    apRequest.setApGroupId(apGroup.getId());
    apRequest.setVenueId(venue.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);

    final var cmnCfgCollectorMessage = getCmnCfgCollectorMessage(tenantId, requestId);

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, requestId, viewApSnmpAgentProfile2,
        cmnCfgCollectorMessage, 2, 1, 0); // venues should be 1, aps should be 0

    validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, requestId, viewApSnmpAgentProfile,
        cmnCfgCollectorMessage, 2, 1, 1); // venues should be 1, aps should be 1
  }

  @Test
  void testUpdateApSnmpAgentProfile(ApSnmpAgentProfile apSnmpAgentProfile) {
    var requestId = randomTxId();
    final var userName = randomName();

    var tenant = apSnmpAgentProfile.getTenant();

    var viewApSnmpAgentProfile = ApSnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2ApSnmpAgentProfile(apSnmpAgentProfile);

    viewApSnmpAgentProfile.setPolicyName("newname");
    viewApSnmpAgentProfile.setSnmpV2Agents(null);
    viewApSnmpAgentProfile.setSnmpV3Agents(null);

    messageUtil.sendWifiCfgRequest(
        tenant.getId(), requestId,
        CfgAction.UPDATE_AP_SNMP_AGENT_PROFILE, userName,
        new RequestParams().addPathVariable("apSnmpProfileId", apSnmpAgentProfile.getId()),
        viewApSnmpAgentProfile);

    validateApSnmpAgentProfileResult(Action.MODIFY, apSnmpAgentProfile.getTenant().getId(),
        requestId, viewApSnmpAgentProfile);

    requestId = randomTxId();

    var apSnmpAgentProfile2 = ApSnmpAgentProfileTestFixture.randomApSnmpAgentProfile(tenant);

    var apSnmpV2Agent1 = apSnmpAgentProfile2.getSnmpV2Agents().get(0);
    apSnmpV2Agent1.setCommunityName("public");
    apSnmpV2Agent1.setReadPrivilege(false);

    var apSnmpV2Agent2 = new ApSnmpV2Agent();
    apSnmpV2Agent2.setCommunityName("snmp-1");
    apSnmpV2Agent2.setReadPrivilege(true);
    apSnmpV2Agent2.setApSnmpAgentProfile(apSnmpAgentProfile2);

    apSnmpAgentProfile2.setSnmpV2Agents(List.of(apSnmpV2Agent1, apSnmpV2Agent2));

    var apSnmpV3Agent1 = apSnmpAgentProfile2.getSnmpV3Agents().get(0);
    apSnmpV3Agent1.setUserName("public");

    var apSnmpV3Agent2 = new ApSnmpV3Agent();
    apSnmpV3Agent2.setUserName("snmp-1");
    apSnmpV3Agent2.setTrapPrivilege(true);
    apSnmpV3Agent2.setNotificationType(SnmpNotificationTypeEnum.Inform);
    apSnmpV3Agent2.setTargetAddr("1.1.1.1");
    apSnmpV3Agent2.setAuthPassword(randomName());
    apSnmpV3Agent2.setApSnmpAgentProfile(apSnmpAgentProfile2);

    apSnmpAgentProfile2.setSnmpV3Agents(List.of(apSnmpV3Agent1, apSnmpV3Agent2));

    repositoryUtil.createOrUpdate(apSnmpAgentProfile2, tenant.getId(), randomTxId());

    viewApSnmpAgentProfile = ApSnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2ApSnmpAgentProfile(apSnmpAgentProfile2);

    viewApSnmpAgentProfile.getSnmpV2Agents().remove(0);
    viewApSnmpAgentProfile.getSnmpV3Agents().remove(0);

    messageUtil.sendWifiCfgRequest(
        tenant.getId(), requestId,
        CfgAction.UPDATE_AP_SNMP_AGENT_PROFILE, userName,
        new RequestParams().addPathVariable("apSnmpProfileId", apSnmpAgentProfile2.getId()),
        viewApSnmpAgentProfile);

    validateApSnmpAgentProfileResult(Action.MODIFY, apSnmpAgentProfile.getTenant().getId(), requestId,
        viewApSnmpAgentProfile);
  }

  @Test
  void testDeleteApSnmpAgentProfile(ApSnmpAgentProfile apSnmpAgentProfile)
      throws InvalidProtocolBufferException {
    final var requestId = randomTxId();
    final var userName = randomName();

    var viewApSnmpAgentProfile = new com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgentProfile();

    viewApSnmpAgentProfile.setId(apSnmpAgentProfile.getId());
    viewApSnmpAgentProfile.setTenantId(apSnmpAgentProfile.getTenant().getId());

    messageUtil.sendWifiCfgRequest(
        apSnmpAgentProfile.getTenant().getId(), requestId,
        CfgAction.DELETE_AP_SNMP_AGENT_PROFILE, userName,
        new RequestParams().addPathVariable("apSnmpProfileId", apSnmpAgentProfile.getId()),
        "");

    validateApSnmpAgentProfileResult(Action.DELETE, apSnmpAgentProfile.getTenant().getId(), requestId, viewApSnmpAgentProfile);
  }

  @Test
  void testDeleteApSnmpAgentProfiles(ApSnmpAgentProfile apSnmpAgentProfile)
      throws InvalidProtocolBufferException {
    final var requestId = randomTxId();
    final var userName = randomName();

    var tenant = apSnmpAgentProfile.getTenant();

    var tenantId = tenant.getId();

    var apSnmpAgentProfile2 = ApSnmpAgentProfileTestFixture.randomApSnmpAgentProfile(tenant);

    repositoryUtil.createOrUpdate(apSnmpAgentProfile2, tenantId, randomTxId());

    var profileIds = List.of(apSnmpAgentProfile.getId(), apSnmpAgentProfile2.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.DELETE_AP_SNMP_AGENT_PROFILES, userName, profileIds);

    var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).extracting(KafkaProtoMessage::getPayload).isNotNull().isNotNull();

    var ddccmRequest = ddccmCfgRequestMessage.getPayload();

    for (String id : profileIds) {
      assertThat(repositoryUtil.find(ApSnmpAgentProfile.class, id)).isNull();
      assertThat(ddccmRequest.getOperationsList()).isNotNull()
          .matches(request -> request.stream()
              .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApSnmpAgentProfile))
          .hasSize(2).filteredOn(o -> id.equals(o.getId()))
          .first()
          .matches(o -> o.getConfigCase() == ConfigCase.APSNMPAGENTPROFILE)
          .matches(o -> o.getAction() == Action.DELETE)
          .satisfies(op -> assertThat(op)
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
              .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
              .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
              .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));

      var viewApSnmpAgentProfile = new com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgentProfile();

      viewApSnmpAgentProfile.setId(id);
      viewApSnmpAgentProfile.setTenantId(tenantId);
    }

    var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException()
        .isThrownBy(() -> assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> EsConstants.Index.AP_SNMP_AGENT_PROFILE.equals(op.getIndex()))
            .hasSize(profileIds.size())
            .allMatch(op -> profileIds.contains(op.getId()))
            .allMatch(op -> op.getOpType() == OpType.DEL));

    validateApSnmpAgentProfileActivityMessages(ApiFlowNames.DELETE_AP_SNMP_AGENT_PROFILES,
        apSnmpAgentProfile.getTenant().getId(), requestId);
  }

  void validateApSnmpAgentProfileResult(Action action, String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgentProfile viewApSnmpAgentProfile) {
    final var apSnmpAgentProfile = repositoryUtil
        .find(ApSnmpAgentProfile.class, viewApSnmpAgentProfile.getId());

    var isDelete = action == Action.DELETE;

    if (isDelete) {
      assertThat(apSnmpAgentProfile).isNull();
    } else {
      assertThat(apSnmpAgentProfile).isNotNull()
          .matches(a -> viewApSnmpAgentProfile.getPolicyName().equals(a.getPolicyName()))
          .matches(a -> CollectionUtils.size(a.getSnmpV2Agents()) == CollectionUtils
              .size(viewApSnmpAgentProfile.getSnmpV2Agents()))
          .matches(a -> CollectionUtils.size(a.getSnmpV3Agents()) == CollectionUtils
              .size(viewApSnmpAgentProfile.getSnmpV3Agents()));
    }

    OpType opType;

    String apiFlowName;

    if (action == Action.ADD) {
      opType = OpType.ADD;
      apiFlowName = ApiFlowNames.ADD_AP_SNMP_AGENT_PROFILE;
    } else if (action == Action.MODIFY) {
      opType = OpType.MOD;
      apiFlowName = ApiFlowNames.UPDATE_AP_SNMP_AGENT_PROFILE;
    } else if (isDelete) {
      opType = OpType.DEL;
      apiFlowName = ApiFlowNames.DELETE_AP_SNMP_AGENT_PROFILE;
    } else {
      throw new UnsupportedOperationException("Unsupported action: " + action);
    }

    validateApSnmpAgentProfileDdccmCfgRequest(action, tenantId, requestId, viewApSnmpAgentProfile);
    validateApSnmpAgentProfileCmnCfgCollectorRequest(opType, tenantId, requestId, viewApSnmpAgentProfile);
    validateApSnmpAgentProfileActivityMessages(apiFlowName, tenantId, requestId);
  }

  void validateApSnmpAgentProfileDdccmCfgRequest(Action action, String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgentProfile viewApSnmpAgentProfile) {
    var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).extracting(KafkaProtoMessage::getPayload).isNotNull().isNotNull();

    assertThatNoException()
        .isThrownBy(() -> assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApSnmpAgentProfile)
            .hasSize(1).filteredOn(o -> viewApSnmpAgentProfile.getId().equals(o.getId()))
            .first()
            .matches(o -> o.getConfigCase() == ConfigCase.APSNMPAGENTPROFILE)
            .matches(o -> o.getAction() == action)
            .satisfies(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                .matches(commonInfo -> viewApSnmpAgentProfile.getTenantId()
                    .equals(commonInfo.getTenantId()))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApSnmpAgentProfile)
            .satisfies(ap -> {
              assertThat(ap).matches(a -> ap.getId().equals(viewApSnmpAgentProfile.getId()));
              if (action != Action.DELETE) {
                assertThat(ap)
                    .matches(a -> viewApSnmpAgentProfile.getPolicyName().equals(a.getName()))
                    .matches(a -> viewApSnmpAgentProfile.getTenantId().equals(a.getTenantId()))
                    .matches(a -> a.getSnmpV2AgentCount() == CollectionUtils
                        .size(viewApSnmpAgentProfile.getSnmpV2Agents()))
                    .matches(a -> a.getSnmpV3AgentCount() == CollectionUtils
                        .size(viewApSnmpAgentProfile.getSnmpV3Agents()));
              }
            }));
  }

  private KafkaProtoMessage<ViewmodelCollector> getCmnCfgCollectorMessage(String tenantId, String requestId) {
    var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    return cmnCfgCollectorMessage;
  }

  private void validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType opType, String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgentProfile viewApSnmpAgentProfile) {
    validateApSnmpAgentProfileCmnCfgCollectorRequest(opType, requestId, viewApSnmpAgentProfile,
        getCmnCfgCollectorMessage(tenantId, requestId), 1, 0, 0, Optional.empty(), Optional.empty());
  }

  private void validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType opType, String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgentProfile viewApSnmpAgentProfile, int venues,
      int aps) {
    validateApSnmpAgentProfileCmnCfgCollectorRequest(opType, requestId, viewApSnmpAgentProfile,
        getCmnCfgCollectorMessage(tenantId, requestId), 1, venues, aps, Optional.empty(), Optional.empty());
  }

  private void validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType opType, String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgentProfile viewApSnmpAgentProfile, int venues,
      int aps, Optional<String> venueName, Optional<String> apName) {
    validateApSnmpAgentProfileCmnCfgCollectorRequest(opType, requestId, viewApSnmpAgentProfile,
        getCmnCfgCollectorMessage(tenantId, requestId), 1, venues, aps, venueName, apName);
  }

  private void validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType opType, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgentProfile viewApSnmpAgentProfile,
      KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage, int opSzie, int venues, int aps) {
    validateApSnmpAgentProfileCmnCfgCollectorRequest(opType, requestId, viewApSnmpAgentProfile,
        cmnCfgCollectorMessage, opSzie, venues, aps, Optional.empty(), Optional.empty());
  }

  private void validateApSnmpAgentProfileCmnCfgCollectorRequest(OpType opType, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgentProfile viewApSnmpAgentProfile,
      KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage, int opSzie, int venues, int aps,
      Optional<String> venueName, Optional<String> apName) {
    assertThatNoException()
        .isThrownBy(() -> assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(viewApSnmpAgentProfile.getTenantId()))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> EsConstants.Index.AP_SNMP_AGENT_PROFILE.equals(op.getIndex()))
            .hasSize(opSzie).filteredOn(op -> viewApSnmpAgentProfile.getId().equals(op.getId()))
            .first()
            .matches(op -> op.getOpType() == opType)
            .satisfies(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> viewApSnmpAgentProfile.getId()
                        .equals(doc.get(EsConstants.Key.ID).getStringValue()))
                    .matches(doc -> viewApSnmpAgentProfile.getPolicyName()
                        .equals(doc.get(EsConstants.Key.NAME).getStringValue()))
                    .matches(doc -> doc.get("v2Agents").getListValue().getValuesCount()
                        == CollectionUtils.emptyIfNull(viewApSnmpAgentProfile.getSnmpV2Agents())
                        .size())
                    .matches(doc -> doc.get("v3Agents").getListValue().getValuesCount()
                        == CollectionUtils.emptyIfNull(viewApSnmpAgentProfile.getSnmpV3Agents())
                        .size())
                    .matches(doc -> doc.get("venues").getListValue().getValuesCount() == venues)
                    .matches(doc -> !venueName.isPresent() || doc.get("venues").getListValue()
                        .getValuesList().stream().map(Value::getStructValue)
                        .map(Struct::getFieldsMap).map(map -> map.get("name").getStringValue())
                        .filter(name -> name.equals(venueName.get())).findAny().isPresent())
                    .matches(doc -> doc.get("aps").getListValue().getValuesCount() == aps)
                    .matches(doc -> !apName.isPresent() || doc.get("aps").getListValue()
                        .getValuesList().stream().map(Value::getStructValue)
                        .map(Struct::getFieldsMap).map(map -> map.get("name").getStringValue())
                        .filter(name -> name.equals(apName.get())).findAny().isPresent());

              }
            }));
  }

  private void validateApSnmpAgentProfileCmnCfgCollectorRequest(
      KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage, String requestId,
      String tenantId) {
    assertThatNoException()
        .isThrownBy(() -> {
          ViewmodelCollector payload = cmnCfgCollectorMessage.getPayload();
          assertThat(payload.getTenantId()).isEqualTo(tenantId);
          assertThat(payload.getRequestId()).isEqualTo(requestId);

          List<Operations> ops = payload.getOperationsList();
          assertThat(ops).isNotEmpty();
          long apSnmpOpCount = ops.stream()
              .filter(op -> EsConstants.Index.AP_SNMP_AGENT_PROFILE.equals(op.getIndex()))
              .count();
          assertThat(apSnmpOpCount).isEqualTo(0);
        });
  }

  private void validateApSnmpAgentProfileActivityMessages(String apiFlowName, String tenantId, String requestId) {
    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 0)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
            .isEmpty());
  }
}
