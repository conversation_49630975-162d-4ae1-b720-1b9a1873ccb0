package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.api.rest.viewmodel.ApContactDeviceRegistrarRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeUpdateApContactDeviceRegistrarRequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired private MessageCaptors messageCaptors;

  @Test
  void testUpdateApContactDeviceRegistrarByTenantRequest(Venue venue) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var apContactDeviceRegistrarRequest = new ApContactDeviceRegistrarRequest();
    apContactDeviceRegistrarRequest.setEnabled(true);

    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(),
        requestId,
        CfgExtendedAction.CONTACT_DEVICE_REGISTRAR_BY_TENANT,
        userName,
        new RequestParams(),
        apContactDeviceRegistrarRequest);

    validateUpdateContractDeviceRegistrar(venue.getId(), requestId);
  }

  @Test
  void testUpdateApContactDeviceRegistrarByVenueRequest(Venue venue) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var apContactDeviceRegistrarRequest = new ApContactDeviceRegistrarRequest();
    apContactDeviceRegistrarRequest.setEnabled(true);

    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(),
        requestId,
        CfgExtendedAction.CONTACT_DEVICE_REGISTRAR_BY_VENUE,
        userName,
        new RequestParams().addPathVariable("venueId", venue.getId()),
        apContactDeviceRegistrarRequest);

    validateUpdateContractDeviceRegistrar(venue.getId(), requestId);
  }

  void validateUpdateContractDeviceRegistrar(String venueId, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .matches(
            v -> BooleanUtils.isTrue(v.getContactDeviceRegistrarEnabled()),
            "DB ContactDeviceRegistrar is equal");

    var record =
        messageCaptors.getDdccmMessageCaptor().getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();

    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1)
        .singleElement()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(venue -> BooleanUtils.isTrue(venue.getContactDeviceRegistrar()));
  }
}
