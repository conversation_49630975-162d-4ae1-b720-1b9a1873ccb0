package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_DHCP_OPTION_82_TOGGLE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.viewmodel.FeatureLevel;
import com.ruckus.cloud.wifi.viewmodel.FeatureType;
import java.util.Collections;
import java.util.List;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class VenueDhcpOption82FeatureTest {

  @MockBean
  private VenueLanPortRepository repository;

  @SpyBean
  private VenueDhcpOption82Feature unit;

  @Test
  void getFeatureNameTest() {
    assertThat(unit.getFeatureName())
        .isEqualTo(VenueDhcpOption82Feature.NAME);
  }

  @Test
  void getFeatureLevelTest() {
    assertThat(unit.getFeatureLevel())
        .isEqualTo(FeatureLevel.VENUE);
  }

  @Test
  void getFeatureTypeTest() {
    assertThat(unit.getFeatureType())
        .isEqualTo(FeatureType.WIFI);
  }

  @Test
  void getRequirementsTest() {
    assertThat(unit.getRequirements())
        .isNotEmpty().singleElement()
        .satisfies(apFirmwareModels -> {
          assertThat(apFirmwareModels.getFirmware()).isEqualTo(VenueDhcpOption82Feature.REQUIRED_MINIMUM_VERSION);
          assertThat(apFirmwareModels.getModels()).isEqualTo(VenueDhcpOption82Feature.SUPPORTED_MODELS);
        });
  }

  @Test
  @FeatureFlag(disable = WIFI_ETHERNET_DHCP_OPTION_82_TOGGLE)
  void GivenNatTraversalFFIsDisabled(Venue venue) {
    BDDAssertions.then(unit.test(venue)).isFalse();
  }
  @Nested
  @FeatureFlag(enable = {WIFI_ETHERNET_DHCP_OPTION_82_TOGGLE})
  class GivenDhcpOption82FFEnabled {

    @Test
    void givenDhcpOption82ActivationNotExist(Venue venue) {
      when(repository.findAllPortsInVenueWhichHasDhcpOption82Activation(
          anyString(), anyString())).thenReturn(Collections.emptyList());

      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    void givenDhcpOption82Activation(Venue venue) {
      when(repository.findAllPortsInVenueWhichHasDhcpOption82Activation(
          anyString(), anyString())).thenReturn(List.of(new VenueLanPort()));

      BDDAssertions.then(unit.test(venue)).isTrue();
    }
  }
}
