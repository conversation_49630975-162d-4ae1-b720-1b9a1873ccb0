package com.ruckus.cloud.wifi.integration.apgroup;

import static com.ruckus.cloud.wifi.service.impl.ExtendedApGroupServiceCtrlImpl.DEFAULT_AP_GROUP_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApBssColoring;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("ApGroupTest")
@WifiIntegrationTest
public class ConsumeUpdateVenueApGroupRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class whenConsumeUpdateVenueApGroupRequest {

    @Test
    void thenUpdateApGroup_withoutAps(Venue venue, @DefaultApGroup ApGroup defaultApGroup) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var tenantId = defaultApGroup.getTenant().getId();

      repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(defaultApGroup.getVenue(), c -> c.setId(apGroupId)),
          tenantId, randomTxId());

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup();
      payload.setName(randomName());

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", venue.getId());
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.UPDATE_VENUE_AP_GROUP, userName,
          requestParams, payload);

      validateResult(tenantId, requestId, venue.getId(), defaultApGroup.getId(), apGroupId, payload);
    }

    @Test
    void thenUpdateApGroup_withApFromDefaultApGroupInSameVenue(Venue venue, @DefaultApGroup ApGroup defaultApGroup, Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var tenantId = defaultApGroup.getTenant().getId();

      defaultApGroup.setAps(List.of(ap));
      repositoryUtil.createOrUpdate(defaultApGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(defaultApGroup.getVenue(), c -> c.setId(apGroupId)),
          tenantId, randomTxId());

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup();
      payload.setName(randomName());
      payload.setApSerialNumbers(List.of(ap.getId()));

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", venue.getId());
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.UPDATE_VENUE_AP_GROUP, userName,
          requestParams, payload);

      validateResult(tenantId, requestId, venue.getId(), defaultApGroup.getId(), apGroupId, payload);
    }

    @Test
    void thenUpdateApGroup_withApFromDefaultApGroupAcrossVenues(Venue oldVenue, @DefaultApGroup ApGroup defaultApGroup, Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var tenantId = oldVenue.getTenant().getId();
      final var newVenueId = randomId();
      final var newVenue = repositoryUtil.createOrUpdate(
          VenueTestFixture.randomVenue(oldVenue.getTenant(), v -> v.setId(newVenueId)),
          tenantId, randomTxId());

      defaultApGroup.setAps(List.of(ap));
      repositoryUtil.createOrUpdate(defaultApGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(newVenue, c -> c.setId(apGroupId)),
          tenantId, randomTxId());

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup();
      payload.setName(randomName());
      payload.setApSerialNumbers(List.of(ap.getId()));

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", newVenueId);
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.UPDATE_VENUE_AP_GROUP, userName,
          requestParams, payload);

      validateResult(tenantId, requestId, oldVenue.getId(), newVenueId, null, null,
          defaultApGroup.getId(), apGroupId, payload);
    }

    @Test
    void thenChangeApVenue_bssColoringSettingsReset(Venue oldVenue, @DefaultApGroup ApGroup defaultApGroup, Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var tenantId = oldVenue.getTenant().getId();
      final var newVenueId = randomId();
      final var newVenue = repositoryUtil.createOrUpdate(
          VenueTestFixture.randomVenue(oldVenue.getTenant(), v -> v.setId(newVenueId)),
          tenantId, randomTxId());

      defaultApGroup.setAps(List.of(ap));
      repositoryUtil.createOrUpdate(defaultApGroup, tenantId, randomTxId());

      ApBssColoring apBssColoring = new ApBssColoring();
      apBssColoring.setUseVenueSettings(false);
      apBssColoring.setBssColoringEnabled(false);
      ap.setBssColoring(apBssColoring);
      repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());

      assertThat(repositoryUtil.find(Ap.class, ap.getId()))
          .isNotNull()
          .extracting(Ap::getBssColoring)
          .satisfies(bssColoring -> {
            assertThat(bssColoring.getBssColoringEnabled()).isFalse();
          });

      repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(newVenue, c -> c.setId(apGroupId)),
          tenantId, randomTxId());

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup();
      payload.setName(randomName());
      payload.setApSerialNumbers(List.of(ap.getId()));

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", newVenueId);
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.UPDATE_VENUE_AP_GROUP, userName,
          requestParams, payload);

      assertThat(repositoryUtil.find(Ap.class, ap.getId()))
          .isNotNull()
          .extracting(Ap::getBssColoring)
          .satisfies(bssColoring -> {
            assertThat(bssColoring.getBssColoringEnabled()).isNull();
          });

      final var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
      assertThat(ddccmMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .filteredOn(Operation::hasAp)
          .filteredOn(e -> e.getAction() == Action.MODIFY)
          .extracting(Operation::getAp)
          .last()
          .matches(a -> !a.hasBssColoring());
    }

    @Test
    void thenUpdateApGroup_withNetworkApGroupAndRadios(Venue venue,
        @DefaultApGroup ApGroup defaultApGroup, Ap ap,
        NetworkVenue networkVenue) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var networkApGroupId = randomId();
      final var tenantId = defaultApGroup.getTenant().getId();

      ApGroup apGroup = ApGroupTestFixture.randomApGroup(defaultApGroup.getVenue(),
          c -> c.setId(apGroupId));
      NetworkApGroup networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
          apGroup, c -> c.setId(networkApGroupId));
      NetworkApGroupRadio networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
          networkApGroup);
      repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(networkApGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(networkApGroupRadio, tenantId, randomTxId());

      defaultApGroup.setAps(List.of(ap));
      repositoryUtil.createOrUpdate(defaultApGroup, tenantId, randomTxId());

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup();
      payload.setName(randomName());
      payload.setApSerialNumbers(List.of(ap.getId()));

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", venue.getId());
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.UPDATE_VENUE_AP_GROUP, userName,
          requestParams, payload);

      validateResult(tenantId, requestId, venue.getId(), venue.getId(), networkVenue.getId(),
          networkVenue.getNetwork().getId(), defaultApGroup.getId(), apGroupId, payload);
      validateNetworkApGroupAndRadios(networkApGroupId, apGroupId);
    }
  }

  void validateResult(String tenantId, String requestId, String venueId, String oldApGroupId,
      String apGroupId, VenueApGroup payload) {
    validateResult(tenantId, requestId, venueId, venueId, null, null, oldApGroupId, apGroupId,
        payload);
  }

  void validateResult(String tenantId, String requestId, String oldVenueId, String venueId,
      String networkVenueId, String networkId, String oldApGroupId, String apGroupId,
      VenueApGroup payload) {
    assertThat(repositoryUtil.find(ApGroup.class, apGroupId))
        .isNotNull()
        .satisfies(apGroup -> {
          assertThat(apGroup.getId()).isEqualTo(apGroupId);
          assertThat(apGroup.getName()).isEqualTo(payload.getName());
          assertThat(apGroup.getVenue())
              .isNotNull()
              .extracting(Venue::getId)
              .isEqualTo(venueId);
        });

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    // validate AP Group cmnCfgCollectorMessage
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
        .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
        .isNotEmpty().hasSize(CollectionUtils.isEmpty(payload.getApSerialNumbers()) ? 1 : 2)
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> apGroupId.equals(op.getId()))
              .isNotEmpty().singleElement()
              .satisfies(op -> {
                assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                assertThat(op.getDocMap())
                    .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                    .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                    .containsEntry(Key.ID, ValueUtils.stringValue(apGroupId))
                    .containsEntry(Key.NAME, ValueUtils.stringValue(payload.getName()))
                    .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                    .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                    .satisfies(docMap -> {
                      if (StringUtils.isNotEmpty(payload.getDescription())) {
                        assertThat(docMap)
                            .containsEntry(Key.DESCRIPTION,
                                ValueUtils.stringValue(payload.getDescription()));
                      }
                      if (networkVenueId != null) {
                        final var networkVenue = repositoryUtil.find(NetworkVenue.class,
                            networkVenueId);
                        assertThat(networkVenue).isNotNull();
                        assertThat(docMap.get(Key.WIFI_NETWORK_IDS))
                            .isNotNull()
                            .extracting(value -> value.getListValue().getValuesList(),
                                InstanceOfAssertFactories.list(
                                    com.ruckus.cloud.events.gpb.Value.class))
                            .isNotEmpty().singleElement()
                            .extracting(com.ruckus.cloud.events.gpb.Value::getStringValue)
                            .isEqualTo(networkVenue.getNetwork().getId());
                      }
                    });
              });
          if (CollectionUtils.isNotEmpty(payload.getApSerialNumbers())) {
            assertThat(ops)
                .filteredOn(op -> oldApGroupId.equals(op.getId()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                      .containsEntry(Key.ID, ValueUtils.stringValue(oldApGroupId))
                      .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_AP_GROUP_NAME.trim()))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(oldVenueId))
                      .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(true));
                });
          }
        });

    // validate AP cmnCfgCollectorMessage
    if (CollectionUtils.isNotEmpty(payload.getApSerialNumbers())) {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
          .isNotEmpty().hasSize(payload.getApSerialNumbers().size())
          .allSatisfy(op -> {
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocOrThrow(Key.DEVICE_GROUP_ID))
                .isEqualTo(ValueUtils.stringValue(apGroupId));
            assertThat(op.getDocOrThrow(Key.DEVICE_GROUP_NAME))
                .isEqualTo(ValueUtils.stringValue(payload.getName()));
          })
          .extracting(Operations::getId)
          .containsExactlyInAnyOrderElementsOf(payload.getApSerialNumbers());
    } else {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
          .isEmpty();
    }

    // validate Network cmnCfgCollectorMessage
    if (networkId != null) {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
          .isNotEmpty().hasSize(payload.getApSerialNumbers().size())
          .allSatisfy(op -> {
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocOrThrow(Key.ID))
                .isEqualTo(ValueUtils.stringValue(networkId));
          });
    } else {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
          .isEmpty();
    }

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(activityCfgChangeRespMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getStatus()).isEqualTo(Status.OK);
          assertThat(msg.getStep()).isEqualTo(ApiFlowNames.UPDATE_AP_GROUP);
          assertThat(msg.getEventDate()).isNotNull();
        });

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(activityImpactedMessage.getPayload())
        .satisfies(msg -> {
          if (CollectionUtils.isEmpty(payload.getApSerialNumbers())) {
            assertThat(msg.getDeviceIdsList()).isEmpty();
          } else {
            assertThat(msg.getDeviceIdsList()).containsAll(payload.getApSerialNumbers());
          }
          assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
        });
  }

  void validateNetworkApGroupAndRadios(String networkApGroupId, String apGroupId) {

    final var networkApGroup = repositoryUtil.find(NetworkApGroup.class, networkApGroupId);

    assertThat(networkApGroup)
        .isNotNull()
        .satisfies(nag -> assertThat(nag.getApGroup().getId()).isEqualTo(apGroupId))
        .extracting(NetworkApGroup::getNetworkApGroupRadios,
            InstanceOfAssertFactories.list(NetworkApGroupRadio.class))
        .isNotEmpty().singleElement()
        .extracting(NetworkApGroupRadio::getRadio)
        .isEqualTo(StrictRadioTypeEnum._2_4_GHz);
  }
}
