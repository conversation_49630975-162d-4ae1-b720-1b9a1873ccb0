package com.ruckus.cloud.wifi.integration.edge;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.edge.protobuf.ChangedConnectResource;
import com.ruckus.cloud.edge.protobuf.ConnectResourceChanged;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("VxLanTunnelFeatureTest")
@WifiIntegrationTest
class ConsumeConnectResourceChangedTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void configSyncConnectResourceFromEdgeService(
      Tenant tenant, Venue venue, TunnelProfile tunnelProfile) {
    //prepare test data
    Venue venue2 = VenueTestFixture.randomVenue(tenant);
    Venue venue3 = VenueTestFixture.randomVenue(tenant);
    repositoryUtil.createOrUpdate(venue2, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(venue3, tenant.getId(), randomTxId());

    List<Network> networks = createAndSaveOpenNetworks(tenant, 3);
    Network network1 = networks.get(0);
    Network network2 = networks.get(1);
    Network network3 = networks.get(2);

    createAndSaveNetworkVenues(tenant, venue, List.of(network1, network2));
    createAndSaveNetworkVenues(tenant, venue2, List.of(network2, network3));
    createAndSaveNetworkVenues(tenant, venue3, List.of(network1, network2, network3));

    SdLanProfile sdLanProfile1 = new SdLanProfile(randomTxId());
    SdLanProfile sdLanProfile2 = new SdLanProfile(randomTxId());
    repositoryUtil.createOrUpdate(sdLanProfile1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(sdLanProfile2, tenant.getId(), randomTxId());

    SdLanProfileRegularSetting regularSetting1 = genSdLanProfileRegularSetting(tenant, venue, sdLanProfile1, tunnelProfile);
    SdLanProfileRegularSetting regularSetting2 = genSdLanProfileRegularSetting(tenant, venue2, sdLanProfile1, tunnelProfile);
    SdLanProfileRegularSetting regularSetting3 = genSdLanProfileRegularSetting(tenant, venue3, sdLanProfile2, tunnelProfile);
    repositoryUtil.createOrUpdate(regularSetting1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(regularSetting2, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(regularSetting3, tenant.getId(), randomTxId());

    SdLanProfileNetworkMapping networkMapping1 = genSdLanProfileNetworkMapping(tenant, network1, regularSetting1);
    SdLanProfileNetworkMapping networkMapping2 = genSdLanProfileNetworkMapping(tenant, network2, regularSetting1);
    SdLanProfileNetworkMapping networkMapping3 = genSdLanProfileNetworkMapping(tenant, network2, regularSetting2);
    SdLanProfileNetworkMapping networkMapping4 = genSdLanProfileNetworkMapping(tenant, network3, regularSetting2);
    SdLanProfileNetworkMapping networkMapping5 = genSdLanProfileNetworkMapping(tenant, network1, regularSetting3);
    SdLanProfileNetworkMapping networkMapping6 = genSdLanProfileNetworkMapping(tenant, network2, regularSetting3);
    SdLanProfileNetworkMapping networkMapping7 = genSdLanProfileNetworkMapping(tenant, network3, regularSetting3);
    repositoryUtil.createOrUpdate(networkMapping1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(networkMapping2, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(networkMapping3, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(networkMapping4, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(networkMapping5, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(networkMapping6, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(networkMapping7, tenant.getId(), randomTxId());

    String crId1 = randomTxId();
    String crId2 = randomTxId();
    ChangedConnectResource changedConnectResource1 = ChangedConnectResource.newBuilder()
        .setConnectResourceId(crId1).setSdLanId(sdLanProfile1.getId()).build();
    ChangedConnectResource changedConnectResource2 = ChangedConnectResource.newBuilder()
        .setConnectResourceId(crId2).setSdLanId(sdLanProfile2.getId()).build();

    String requestId = randomTxId();
    ConnectResourceChanged connectResourceChanged = ConnectResourceChanged.newBuilder()
        .setAction(ConnectResourceChanged.Action.CONFIG_SYNC)
        .setTenantId(tenant.getId())
        .setRequestId(requestId)
        .addAllConnectResources(List.of(changedConnectResource1, changedConnectResource2))
        .build();
    messageUtil.sendConnectResource(tenant.getId(), requestId, connectResourceChanged);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(
        txCtxExtension.getTenantId(), requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 7").hasSize(7)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> assertSoftly(softly -> {
              softly.assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
                  .filteredOn(
                      op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The modify WlanVenue operation count should be 7").hasSize(7)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                  .satisfies(wlanVenues -> {
                    for (WlanVenue wlanVenue : wlanVenues) {
                      assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(
                          tunnelProfile.getId());
                      assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
                      assertThat(StringUtils.isNotEmpty(wlanVenue.getEdgeConnectResourceId().getValue())).isTrue();
                    }
                  });
            }))
    );
  }

  private List<Network> createAndSaveOpenNetworks(Tenant tenant, int size) {
    List<Network> networks = new ArrayList<>();
    for (int i = 0; i < size; ++i) {
      final var openNetwork = network(OpenNetwork.class).setName(randomString()).generate();
      openNetwork.getWlan().setNetwork(openNetwork);
      openNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
      repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());
      networks.add(openNetwork);
    }
    return networks;
  }

  private void createAndSaveNetworkVenues(Tenant tenant, Venue venue, List<Network> networks) {
    networks.forEach(n -> {
      final var networkVenue = Generators.networkVenue().setNetwork(always(n))
          .setVenue(always(venue)).generate();
      n.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    });
  }

  private static SdLanProfileRegularSetting genSdLanProfileRegularSetting(Tenant tenant,
      Venue venue, SdLanProfile sdLanProfile, TunnelProfile tunnelProfile) {
    SdLanProfileRegularSetting regularSetting = new SdLanProfileRegularSetting();
    regularSetting.setId(randomTxId());
    regularSetting.setSdLanProfile(sdLanProfile);
    regularSetting.setTunnelProfile(tunnelProfile);
    regularSetting.setVenue(venue);
    regularSetting.setTenant(tenant);
    return regularSetting;
  }

  private static SdLanProfileNetworkMapping genSdLanProfileNetworkMapping(Tenant tenant,
      Network network, SdLanProfileRegularSetting regularSetting) {
    SdLanProfileNetworkMapping networkMapping = new SdLanProfileNetworkMapping();
    networkMapping.setId(randomTxId());
    networkMapping.setNetwork(network);
    networkMapping.setSdLanProfileRegularSetting(regularSetting);
    networkMapping.setTenant(tenant);
    return networkMapping;
  }
}
