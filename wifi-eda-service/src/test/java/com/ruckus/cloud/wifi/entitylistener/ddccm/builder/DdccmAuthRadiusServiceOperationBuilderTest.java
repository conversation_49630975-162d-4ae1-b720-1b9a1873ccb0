package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.authRadiusService;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuth;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.tenant;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuthWithIpv6;
import static com.ruckus.cloud.wifi.utils.FeatureRolesUtils.PROXY_RADSEC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import com.google.protobuf.BoolValue;
import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RadiusSecondaryHost;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusServer;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
public class DdccmAuthRadiusServiceOperationBuilderTest {

  @Autowired
  private DdccmAuthRadiusServiceOperationBuilder builder;

  @Test
  void testCreateAuthRadiusService() {
    Radius radius = radiusAuth().generate();
    AuthRadiusService authRadiusService = authRadiusService(radius).setTenant(tenant()).generate();
    Operation.Builder opBuilder = Operation.newBuilder().setAction(Action.ADD);
    builder.config(opBuilder, authRadiusService, EntityAction.ADD, null);
    Operation result = opBuilder.build();

    assertThat(result)
        .hasFieldOrPropertyWithValue("action", Action.ADD)
        .hasFieldOrPropertyWithValue("id", authRadiusService.getId());
    assertThat(result.getRadiusAuthenticationService())
        .hasFieldOrPropertyWithValue("id", authRadiusService.getId())
        .hasFieldOrPropertyWithValue("name", authRadiusService.getId());
    assertThat(result.getRadiusAuthenticationService().getPrimary()).isNotNull();
    assertThat(result.getRadiusAuthenticationService().getPrimary())
        .hasFieldOrPropertyWithValue("ip", authRadiusService.getRadius().getPrimary().getIp())
        .hasFieldOrPropertyWithValue("port", authRadiusService.getRadius().getPrimary().getPort())
        .hasFieldOrPropertyWithValue("sharedSecret", authRadiusService.getRadius().getPrimary().getSharedSecret());

    assertThat(result.getRadiusAuthenticationService().getSecondary()).isNotNull();
    assertThat(result.getRadiusAuthenticationService().getSecondary())
        .hasFieldOrPropertyWithValue("ip", authRadiusService.getRadius().getSecondary().getIp())
        .hasFieldOrPropertyWithValue("port", authRadiusService.getRadius().getSecondary().getPort())
        .hasFieldOrPropertyWithValue("sharedSecret", authRadiusService.getRadius().getSecondary().getSharedSecret());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_RADSEC_TOGGLE)
  @FeatureRole(PROXY_RADSEC)
  void testCreateTlsEnabledAuthRadiusService() {
    Radius radius = radiusAuth()
        .setRadSecOptions(Generators.radSecOptions())
        .setSecondary(nullValue(RadiusServer.class))
        .generate();
    AuthRadiusService authRadiusService = authRadiusService(radius).setTenant(tenant()).generate();
    Operation.Builder opBuilder = Operation.newBuilder().setAction(Action.ADD);
    builder.config(opBuilder, authRadiusService, EntityAction.ADD, null);
    Operation result = opBuilder.build();

    assertThat(result)
        .hasFieldOrPropertyWithValue("action", Action.ADD)
        .hasFieldOrPropertyWithValue("id", authRadiusService.getId());
    assertThat(result.getRadiusAuthenticationService())
        .hasFieldOrPropertyWithValue("id", authRadiusService.getId())
        .hasFieldOrPropertyWithValue("name", authRadiusService.getId())
        .hasFieldOrPropertyWithValue("tlsEnabled", BoolValue.of(authRadiusService.getRadius().getRadSecOptions().getTlsEnabled()))
        .hasFieldOrPropertyWithValue("cnSanIdentity", authRadiusService.getRadius().getRadSecOptions().getCnSanIdentity())
        .hasFieldOrPropertyWithValue("ocspUrl", authRadiusService.getRadius().getRadSecOptions().getOcspUrl())
        .hasFieldOrPropertyWithValue("certificateAuthorityId", StringValue.of(
            authRadiusService.getRadius().getRadSecOptions().getCertificateAuthorityId()))
        .hasFieldOrPropertyWithValue("clientCertificateId", StringValue.of(
            authRadiusService.getRadius().getRadSecOptions().getClientCertificateId()))
        .hasFieldOrPropertyWithValue("serverCertificateId", StringValue.of(
            authRadiusService.getRadius().getRadSecOptions().getServerCertificateId()))
        .hasFieldOrPropertyWithValue("secondary", RadiusSecondaryHost.getDefaultInstance());

    assertThat(result.getRadiusAuthenticationService().getPrimary()).isNotNull();
    assertThat(result.getRadiusAuthenticationService().getPrimary())
        .hasFieldOrPropertyWithValue("ip", authRadiusService.getRadius().getPrimary().getIp())
        .hasFieldOrPropertyWithValue("port", authRadiusService.getRadius().getPrimary().getPort());
  }

  @Test
  void testUpdateTlsEnabledAuthRadiusServiceWithoutFF() {
    Radius radius = radiusAuth()
        .setRadSecOptions(Generators.radSecOptions())
        .setSecondary(nullValue(RadiusServer.class))
        .generate();
    AuthRadiusService authRadiusService = authRadiusService(radius).setTenant(tenant()).generate();
    Operation.Builder opBuilder = Operation.newBuilder().setAction(Action.MODIFY);
    builder.config(opBuilder, authRadiusService, EntityAction.MODIFY, null);
    Operation result = opBuilder.build();

    assertThat(result)
        .hasFieldOrPropertyWithValue("action", Action.MODIFY)
        .hasFieldOrPropertyWithValue("id", authRadiusService.getId());
    assertThat(result.getRadiusAuthenticationService())
        .hasFieldOrPropertyWithValue("id", authRadiusService.getId())
        .hasFieldOrPropertyWithValue("name", authRadiusService.getId())
        .hasFieldOrPropertyWithValue("tlsEnabled", BoolValue.of(false))
        .hasFieldOrPropertyWithValue("cnSanIdentity", "")
        .hasFieldOrPropertyWithValue("ocspUrl", "")
        .hasFieldOrPropertyWithValue("certificateAuthorityId", StringValue.of(""))
        .hasFieldOrPropertyWithValue("clientCertificateId", StringValue.of(""))
        .hasFieldOrPropertyWithValue("serverCertificateId", StringValue.of(""))
        .hasFieldOrPropertyWithValue("secondary", RadiusSecondaryHost.getDefaultInstance());

    assertThat(result.getRadiusAuthenticationService().getPrimary()).isNotNull();
    assertThat(result.getRadiusAuthenticationService().getPrimary())
        .hasFieldOrPropertyWithValue("ip", authRadiusService.getRadius().getPrimary().getIp())
        .hasFieldOrPropertyWithValue("port", authRadiusService.getRadius().getPrimary().getPort())
        .hasFieldOrPropertyWithValue("sharedSecret", authRadiusService.getRadius().getPrimary().getSharedSecret());
  }

  @Test
  void testCreateAuthRadiusServiceWithIpv6() {
    Radius radius = radiusAuthWithIpv6().generate();
    AuthRadiusService authRadiusService = authRadiusService(radius).setTenant(tenant()).generate();
    Operation.Builder opBuilder = Operation.newBuilder().setAction(Action.ADD);
    builder.config(opBuilder, authRadiusService, EntityAction.ADD, null);
    Operation result = opBuilder.build();

    assertThat(result)
      .hasFieldOrPropertyWithValue("action", Action.ADD)
      .hasFieldOrPropertyWithValue("id", authRadiusService.getId());
    assertThat(result.getRadiusAuthenticationService())
      .hasFieldOrPropertyWithValue("id", authRadiusService.getId())
      .hasFieldOrPropertyWithValue("name", authRadiusService.getId());
    assertThat(result.getRadiusAuthenticationService().getPrimary()).isNotNull();
    assertThat(result.getRadiusAuthenticationService().getPrimary())
      .hasFieldOrPropertyWithValue("ipv6", authRadiusService.getRadius().getPrimary().getIp())
      .hasFieldOrPropertyWithValue("port", authRadiusService.getRadius().getPrimary().getPort())
      .hasFieldOrPropertyWithValue("sharedSecret", authRadiusService.getRadius().getPrimary().getSharedSecret());

    assertThat(result.getRadiusAuthenticationService().getSecondary()).isNotNull();
    assertThat(result.getRadiusAuthenticationService().getSecondary())
      .hasFieldOrPropertyWithValue("ipv6", authRadiusService.getRadius().getSecondary().getIp())
      .hasFieldOrPropertyWithValue("port", authRadiusService.getRadius().getSecondary().getPort())
      .hasFieldOrPropertyWithValue("sharedSecret", authRadiusService.getRadius().getSecondary().getSharedSecret());
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public DdccmAuthRadiusServiceOperationBuilder ddccmAuthRadiusServiceOperationBuilder() {
      DdccmAuthRadiusServiceOperationBuilder builder = Mockito.spy(DdccmAuthRadiusServiceOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }
  }
}