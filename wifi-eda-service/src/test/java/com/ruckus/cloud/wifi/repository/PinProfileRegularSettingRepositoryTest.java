package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;
import java.util.UUID;

@Tag("VxLanTunnelFeatureTest")
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES
        ('venue1c6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('venue2c6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('venue3c6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('venue4c6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO pin_profile (id, tenant) VALUES
        ('pin1da0ac6afb747a4987d0d0945f772', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('pin2da0ac6afb747a4987d0d0945f772', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('pin3da0ac6afb747a4987d0d0945f772', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('pin4da0ac6afb747a4987d0d0945f772', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tunnel_profile (id, name, tenant, mtu_type, mtu_size, force_fragmentation) VALUES
        ('afc284d992694d5c9d7a2fcf2289a0bd', 'tunnel profile 1', '4c8279f79307415fa9e4c88a1819f0fc',
         'MANUAL', 1450, false),
        ('qwert4d992694d5c9d7a2fcf2289a0bd', 'tunnel profile 2', '4c8279f79307415fa9e4c88a1819f0fc',
         'MANUAL', 1450, false),
        ('guest4d992694d5c9d7a2fcf2289a0bd', 'tunnel profile 3', '4c8279f79307415fa9e4c88a1819f0fc',
         'MANUAL', 1450, false);
    INSERT INTO pin_profile_regular_setting (id, tenant, tunnel_profile, pin_profile, venue) VALUES
        ('rs10e13cc150444ca1956dd68c8999f2', '4c8279f79307415fa9e4c88a1819f0fc',
         'afc284d992694d5c9d7a2fcf2289a0bd', 'pin1da0ac6afb747a4987d0d0945f772',
         'venue1c6afb747a4987d0d0945f77221'),
        ('rs20e13cc150444ca1956dd68c8999f2', '4c8279f79307415fa9e4c88a1819f0fc',
         'afc284d992694d5c9d7a2fcf2289a0bd', 'pin2da0ac6afb747a4987d0d0945f772',
         'venue2c6afb747a4987d0d0945f77221'),
        ('rs30e13cc150444ca1956dd68c8999f2', '4c8279f79307415fa9e4c88a1819f0fc',
         'qwert4d992694d5c9d7a2fcf2289a0bd', 'pin3da0ac6afb747a4987d0d0945f772',
         'venue3c6afb747a4987d0d0945f77221'),
        ('rs40e13cc150444ca1956dd68c8999f2', '4c8279f79307415fa9e4c88a1819f0fc',
         'afc284d992694d5c9d7a2fcf2289a0bd', 'pin4da0ac6afb747a4987d0d0945f772',
         'venue4c6afb747a4987d0d0945f77221'),
        ('rs40e13cc150444ca1956dd68c8999f3', '4c8279f79307415fa9e4c88a1819f0fc',
         'qwert4d992694d5c9d7a2fcf2289a0bd', 'pin1da0ac6afb747a4987d0d0945f772', null);
    """)
class PinProfileRegularSettingRepositoryTest {
  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String TUNNEL_PROFILE_ID_1 = "afc284d992694d5c9d7a2fcf2289a0bd";
  private static final String TUNNEL_PROFILE_ID_2 = "qwert4d992694d5c9d7a2fcf2289a0bd";
  private static final String TUNNEL_PROFILE_ID_3 = "guest4d992694d5c9d7a2fcf2289a0bd";
  private static final String VENUE_ID_1 = "venue1c6afb747a4987d0d0945f77221";
  private static final String VENUE_ID_2 = "venue2c6afb747a4987d0d0945f77221";
  private static final String VENUE_ID_3 = "venue3c6afb747a4987d0d0945f77221";
  private static final String VENUE_ID_4 = "venue4c6afb747a4987d0d0945f77221";
  private static final String PIN_ID_1 = "pin1da0ac6afb747a4987d0d0945f772";
  private static final String PIN_ID_2 = "pin2da0ac6afb747a4987d0d0945f772";
  private static final String PIN_ID_3 = "pin3da0ac6afb747a4987d0d0945f772";
  private static final String PIN_ID_4 = "pin4da0ac6afb747a4987d0d0945f772";


  @Autowired
  private PinProfileRegularSettingRepository repository;

  @Test
  void findByTenantIdAndTunnelProfileIdTest() {
    assertThat(repository.findByTenantIdAndTunnelProfileId(TENANT_ID, TUNNEL_PROFILE_ID_1))
        .isNotEmpty().hasSize(3)
        .extracting(regularSetting -> regularSetting.getTunnelProfile().getId())
        .containsOnly(TUNNEL_PROFILE_ID_1);
    assertThat(repository.findByTenantIdAndTunnelProfileId(TENANT_ID, TUNNEL_PROFILE_ID_2))
        .isNotEmpty().hasSize(2)
        .extracting(regularSetting -> regularSetting.getTunnelProfile().getId())
        .containsOnly(TUNNEL_PROFILE_ID_2);
    assertThat(repository.findByTenantIdAndTunnelProfileId(TENANT_ID, TUNNEL_PROFILE_ID_3))
        .isEmpty();
  }

  @Test
  void existsByTenantIdAndVenueIdTest() {
    assertThat(repository.existsByTenantIdAndVenueId(TENANT_ID, VENUE_ID_1)).isTrue();
    assertThat(repository.existsByTenantIdAndVenueId(TENANT_ID, VENUE_ID_2)).isTrue();
    assertThat(repository.existsByTenantIdAndVenueId(TENANT_ID, VENUE_ID_3)).isTrue();
    assertThat(repository.existsByTenantIdAndVenueId(TENANT_ID, VENUE_ID_4)).isTrue();
    assertThat(repository.existsByTenantIdAndVenueId(TENANT_ID, UUID.randomUUID().toString())).isFalse();
  }

  @Test
  void findByTenantIdAndVenueIsNotNullTest() {
    assertThat(repository.findByTenantIdAndVenueIsNotNull(TENANT_ID))
        .isNotEmpty().hasSize(4);
  }

  @Test
  void findByTenantIdAndPinProfileIdInAndVenueIsNotNullTest() {
    assertThat(repository.findByTenantIdAndPinProfileIdInAndVenueIsNotNull(TENANT_ID,
        List.of(PIN_ID_1, PIN_ID_2, PIN_ID_3)))
            .isNotEmpty().hasSize(3);
  }
}
