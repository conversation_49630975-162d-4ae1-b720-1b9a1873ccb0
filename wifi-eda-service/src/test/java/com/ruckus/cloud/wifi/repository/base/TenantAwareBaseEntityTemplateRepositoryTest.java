package com.ruckus.cloud.wifi.repository.base;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractTenantAwareTemplateBaseEntity;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.utils.TemplateUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.FluentQuery;

@ExtendWith(TxCtxExtension.class)
class TenantAwareBaseEntityTemplateRepositoryTest {

  private TestTenantAwareBaseEntityTemplateRepository repository;
  private MockEntity mockEntity;

  @BeforeEach
  void setUp() {
    repository = new TestTenantAwareBaseEntityTemplateRepository();
    mockEntity = new MockEntity();
  }

  @Test
  void testTemplatePredicate_WithTemplateEntity_ShouldReturnTrue() {
    // Given
    mockEntity.setIsTemplate(true);
    mockEntity.setTemplateContext(TemplateUtils.TemplateContext.REC.name());
    
    // When
    Predicate<MockEntity> predicate = repository.templatePredicate(true, TemplateUtils.TemplateContext.REC.name());
    boolean result = predicate.test(mockEntity);
    
    // Then
    assertThat(result).isTrue();
  }

  @Test
  void testTemplatePredicate_WithNonTemplateEntity_ShouldReturnTrue() {
    // Given
    mockEntity.setIsTemplate(false);
    mockEntity.setTemplateContext(TemplateUtils.TemplateContext.MSP.name());
    
    // When
    Predicate<MockEntity> predicate = repository.templatePredicate(false, TemplateUtils.TemplateContext.MSP.name());
    boolean result = predicate.test(mockEntity);
    
    // Then
    assertThat(result).isTrue();
  }

  @Test
  void testTemplatePredicate_WithNullEntity_ShouldReturnFalse() {
    // Given
    MockEntity nullEntity = null;
    
    // When
    Predicate<MockEntity> predicate = repository.templatePredicate(true, TemplateUtils.TemplateContext.REC.name());
    boolean result = predicate.test(nullEntity);
    
    // Then
    assertThat(result).isFalse();
  }

  @Test
  void testTemplatePredicate_WithMismatchedTemplateFlag_ShouldReturnFalse() {
    // Given
    mockEntity.setIsTemplate(false);
    mockEntity.setTemplateContext(TemplateUtils.TemplateContext.REC.name());
    
    // When
    Predicate<MockEntity> predicate = repository.templatePredicate(true, TemplateUtils.TemplateContext.REC.name());
    boolean result = predicate.test(mockEntity);
    
    // Then
    assertThat(result).isFalse();
  }

  @Test
  void testValidateTemplateContextMatch_WithRecContext_ShouldMatch() {
    // Given
    mockEntity.setTemplateContext(TemplateUtils.TemplateContext.REC.name());
    
    // When
    boolean result = repository.validateTemplateContextMatch(mockEntity, TemplateUtils.TemplateContext.REC.name());
    
    // Then
    assertThat(result).isTrue();
  }

  @Test
  void testValidateTemplateContextMatch_WithMspContext_ShouldNotMatchRec() {
    // Given
    mockEntity.setTemplateContext(TemplateUtils.TemplateContext.MSP.name());
    
    // When
    boolean result = repository.validateTemplateContextMatch(mockEntity, TemplateUtils.TemplateContext.REC.name());
    
    // Then
    assertThat(result).isFalse();
  }

  @Test
  void testValidateTemplateContextMatch_WithRecEntity_ShouldNotMatchMsp() {
    // Given
    mockEntity.setTemplateContext(TemplateUtils.TemplateContext.REC.name());
    
    // When
    boolean result = repository.validateTemplateContextMatch(mockEntity, TemplateUtils.TemplateContext.MSP.name());
    
    // Then
    assertThat(result).isFalse();
  }

  @Test
  void testValidateTemplateContextMatch_WithMspContext_ShouldMatchMsp() {
    // Given
    mockEntity.setTemplateContext(TemplateUtils.TemplateContext.MSP.name());
    
    // When
    boolean result = repository.validateTemplateContextMatch(mockEntity, TemplateUtils.TemplateContext.MSP.name());
    
    // Then
    assertThat(result).isTrue();
  }

  @Test
  void testValidateTemplateContextMatch_WithNullContext_ShouldHandleGracefully() {
    // Given
    mockEntity.setTemplateContext(null);
    
    // When
    boolean result = repository.validateTemplateContextMatch(mockEntity, TemplateUtils.TemplateContext.REC.name());
    
    // Then
    assertThat(result).isFalse();
  }

  @Test
  void testValidateTemplateContextMatch_WithEmptyContext_ShouldHandleGracefully() {
    // Given
    mockEntity.setTemplateContext("");
    
    // When
    boolean result = repository.validateTemplateContextMatch(mockEntity, TemplateUtils.TemplateContext.REC.name());
    
    // Then
    assertThat(result).isFalse();
  }

  @Test
  void testPredicates_WithTemplateFilterEnabled_ShouldReturnTwoPredicates() {
    // Given
    String tenantId = "test-tenant";
    boolean isTemplate = true;
    String templateContext = TemplateUtils.TemplateContext.REC.name();
    
    // When
    Predicate<MockEntity>[] predicates = repository.predicates(tenantId, isTemplate, templateContext);
    
    // Then
    assertThat(predicates).hasSize(2);
  }

  @Test
  void testPredicates_WithTemplateFilterDisabled_ShouldReturnTwoPredicates() {
    // Given
    String tenantId = "test-tenant";
    boolean isTemplate = true;
    String templateContext = TemplateUtils.TemplateContext.REC.name();
    
    // Mock disabled template filter
    TestTenantAwareBaseEntityTemplateRepository disabledRepository = new TestTenantAwareBaseEntityTemplateRepository();
    
    // When
    Predicate<MockEntity>[] predicates = disabledRepository.predicates(tenantId, isTemplate, templateContext);
    
    // Then
    assertThat(predicates).hasSize(2);
  }

  // Test implementation classes
  private static class TestTenantAwareBaseEntityTemplateRepository implements TenantAwareBaseEntityTemplateRepository<MockEntity> {
    // Other required methods from parent interfaces
    @Override
    public Predicate<MockEntity> tenantPredicate(String tenantId) {
      return entity -> entity != null && tenantId.equals(entity.getTenantId());
    }

    @Override
    public List<MockEntity> findAllByPredicates(Predicate<MockEntity>... predicates) {
      return TenantAwareBaseEntityTemplateRepository.super.findAllByPredicates(predicates);
    }

    @Override
    public List<MockEntity> findAllByPredicates(Sort sort, Predicate<MockEntity>... predicates) {
      return TenantAwareBaseEntityTemplateRepository.super.findAllByPredicates(sort, predicates);
    }

    @Override
    public List<MockEntity> findAllByIdAndPredicates(Iterable<String> strings, Predicate<MockEntity>... predicates) {
      return TenantAwareBaseEntityTemplateRepository.super.findAllByIdAndPredicates(strings, predicates);
    }

    @Override
    public MockEntity getOneByPredicates(String s, Predicate<MockEntity>... predicates) {
      return TenantAwareBaseEntityTemplateRepository.super.getOneByPredicates(s, predicates);
    }

    @Override
    public MockEntity getByIdAndPredicates(String s, Predicate<MockEntity>... predicates) {
      return TenantAwareBaseEntityTemplateRepository.super.getByIdAndPredicates(s, predicates);
    }

    @Override
    public <S extends MockEntity> List<S> findAllByPredicates(Example<S> example, Predicate<MockEntity>... predicates) {
      return TenantAwareBaseEntityTemplateRepository.super.findAllByPredicates(example, predicates);
    }

    @Override
    public <S extends MockEntity> List<S> findAllByPredicates(Example<S> example, Sort sort, Predicate<MockEntity>... predicates) {
      return TenantAwareBaseEntityTemplateRepository.super.findAllByPredicates(example, sort, predicates);
    }

    @Override
    public Optional<MockEntity> findByIdAndPredicates(String s, Predicate<MockEntity>... predicates) {
      return TenantAwareBaseEntityTemplateRepository.super.findByIdAndPredicates(s, predicates);
    }

    @Override
    public void deleteByIdAndPredicates(String s, Predicate<MockEntity>... predicates) {
      TenantAwareBaseEntityTemplateRepository.super.deleteByIdAndPredicates(s, predicates);
    }

    @Override
    public void deleteAllByIdAndPredicates(Iterable<String> strings, Predicate<MockEntity>... predicates) {
      TenantAwareBaseEntityTemplateRepository.super.deleteAllByIdAndPredicates(strings, predicates);
    }

    @Override
    public void deleteAllByPredicates(Predicate<MockEntity>... predicates) {
      TenantAwareBaseEntityTemplateRepository.super.deleteAllByPredicates(predicates);
    }

    @Override
    public boolean existsByIdAndPredicates(String s, Predicate<MockEntity>... predicates) {
      return TenantAwareBaseEntityTemplateRepository.super.existsByIdAndPredicates(s, predicates);
    }

    // Required abstract methods from JpaRepository/CrudRepository
    @Override
    public List<MockEntity> findAll() {
      return new ArrayList<>();
    }
    
    @Override
    public List<MockEntity> findAll(Sort sort) {
      return new ArrayList<>();
    }
    
    @Override
    public List<MockEntity> findAllById(Iterable<String> ids) {
      return new ArrayList<>();
    }
    
    @Override
    public MockEntity getReferenceById(String id) {
      return null;
    }
    
    @Override
    public MockEntity getById(String id) {
      return null;
    }
    
    // Add missing getOne method for Spring Data JPA compatibility
    @Override
    public MockEntity getOne(String id) {
      return getReferenceById(id);
    }
    
    @Override
    public <S extends MockEntity> List<S> findAll(Example<S> example) {
      return new ArrayList<>();
    }
    
    @Override
    public <S extends MockEntity> List<S> findAll(Example<S> example, Sort sort) {
      return new ArrayList<>();
    }
    
    @Override
    public Optional<MockEntity> findById(String id) {
      return Optional.empty();
    }
    
    @Override
    public void deleteById(String id) {
      // No-op for testing
    }
    
    @Override
    public void delete(MockEntity entity) {
      // No-op for testing
    }
    
    @Override
    public void deleteAllById(Iterable<? extends String> ids) {
      // No-op for testing
    }
    
    @Override
    public void deleteAll() {
      // No-op for testing
    }
    
    @Override
    public void deleteAll(Iterable<? extends MockEntity> entities) {
      // No-op for testing
    }
    
    @Override
    public boolean existsById(String id) {
      return false;
    }
    
    // Add missing save methods
    @Override
    public <S extends MockEntity> S save(S entity) {
      return entity;
    }
    
    @Override
    public <S extends MockEntity> List<S> saveAll(Iterable<S> entities) {
      List<S> result = new ArrayList<>();
      entities.forEach(result::add);
      return result;
    }
    
    @Override
    public void flush() {
      // No-op for testing
    }
    
    @Override
    public <S extends MockEntity> S saveAndFlush(S entity) {
      return entity;
    }
    
    @Override
    public <S extends MockEntity> List<S> saveAllAndFlush(Iterable<S> entities) {
      List<S> result = new ArrayList<>();
      entities.forEach(result::add);
      return result;
    }
    
    @Override
    public void deleteAllInBatch(Iterable<MockEntity> entities) {
      // No-op for testing
    }
    
    @Override
    public void deleteAllByIdInBatch(Iterable<String> ids) {
      // No-op for testing
    }
    
    @Override
    public void deleteAllInBatch() {
      // No-op for testing
    }
    
    @Override
    public <S extends MockEntity> Optional<S> findOne(Example<S> example) {
      return Optional.empty();
    }

    @Override
    public <S extends MockEntity> Page<S> findAll(Example<S> example, Pageable pageable) {
      return Page.empty();
    }
    
    @Override
    public <S extends MockEntity> long count(Example<S> example) {
      return 0;
    }
    
    @Override
    public <S extends MockEntity> boolean exists(Example<S> example) {
      return false;
    }

    @Override
    public <S extends MockEntity, R> R findBy(Example<S> example, Function<FluentQuery.FetchableFluentQuery<S>, R> queryFunction) {
      return null;
    }

    @Override
    public Page<MockEntity> findAll(Pageable pageable) {
      return Page.empty();
    }
    
    @Override
    public long count() {
      return 0;
    }
    
    // Required abstract methods from TenantAwareBaseEntityRepository
    @Override
    public long countByTenantId(String tenantId) {
      return 0;
    }
    
    @Override
    public List<MockEntity> findByTenantId(String tenantId) {
      return new ArrayList<>();
    }
    
    @Override
    public List<MockEntity> findByTenantId(Sort sort, String tenantId) {
      return new ArrayList<>();
    }
    
    @Override
    public Page<MockEntity> findByTenantId(Pageable pageable, String tenantId) {
      return Page.empty();
    }

    @Override
    public Predicate<MockEntity> templatePredicate(boolean isTemplate, String templateContext) {
      return TenantAwareBaseEntityTemplateRepository.super.templatePredicate(isTemplate, templateContext);
    }

    @Override
    public boolean validateTemplateContextMatch(MockEntity entity, String expectedContext) {
      return TenantAwareBaseEntityTemplateRepository.super.validateTemplateContextMatch(entity, expectedContext);
    }

    @Override
    public Predicate<MockEntity>[] predicates(Object... objects) {
      return TenantAwareBaseEntityTemplateRepository.super.predicates(objects);
    }

    @Override
    public Optional<MockEntity> findByIdAndTenantId(String id, String tenantId) {
      return TenantAwareBaseEntityTemplateRepository.super.findByIdAndTenantId(id, tenantId);
    }

    @Override
    public MockEntity getByIdAndTenantId(String id, String tenantId) {
      return TenantAwareBaseEntityTemplateRepository.super.getByIdAndTenantId(id, tenantId);
    }

    @Override
    public List<MockEntity> findByIdInAndTenantId(Iterable<String> ids, String tenantId) {
      return TenantAwareBaseEntityTemplateRepository.super.findByIdInAndTenantId(ids, tenantId);
    }

    @Override
    public void deleteByIdAndTenantId(String id, String tenantId) {
      TenantAwareBaseEntityTemplateRepository.super.deleteByIdAndTenantId(id, tenantId);
    }

    @Override
    public void deleteByIdInAndTenantId(Iterable<String> ids, String tenantId) {
      TenantAwareBaseEntityTemplateRepository.super.deleteByIdInAndTenantId(ids, tenantId);
    }

    @Override
    public boolean existsByIdAndTenantId(String id, String tenantId) {
      return false;
    }

    @Override
    public boolean existsByIdAndTenantIdAndIsTemplate(String id, String tenantId, boolean isTemplate) {
      return TenantAwareBaseEntityTemplateRepository.super.existsByIdAndTenantIdAndIsTemplate(id, tenantId, isTemplate);
    }

    @Override
    public Optional<MockEntity> findTemplateById(String id) {
      return TenantAwareBaseEntityTemplateRepository.super.findTemplateById(id);
    }

    @Override
    public Optional<MockEntity> findTemplateByIdAndTenantId(String id, String tenantId) {
      return TenantAwareBaseEntityTemplateRepository.super.findTemplateByIdAndTenantId(id, tenantId);
    }

    @Override
    public Optional<MockEntity> findByTemplateIdAndTenantId(String templateId, String tenantId) {
      return Optional.empty();
    }
    
    @Override
    public List<MockEntity> findByTemplateIdInAndTenantId(Iterable<String> ids, String tenantId) {
      return new ArrayList<>();
    }
    
    @Override
    public List<MockEntity> findByTemplateId(String templateId) {
      return new ArrayList<>();
    }
    
    @Override
    public List<MockEntity> findByTenantIdAndIsTemplateFalse(String tenantId) {
      return new ArrayList<>();
    }
    
    @Override
    public List<MockEntity> findByTenantIdAndIsTemplateFalseAndTemplateIdNotNull(String tenantId) {
      return new ArrayList<>();
    }
  }

  private static class MockEntity extends AbstractTenantAwareTemplateBaseEntity {
    private String tenantId;
    private Boolean isTemplate;
    private String templateContext;

    public String getTenantId() {
      return tenantId;
    }

    public void setTenantId(String tenantId) {
      this.tenantId = tenantId;
    }

    @Override
    public Boolean getIsTemplate() {
      return isTemplate;
    }

    public void setIsTemplate(Boolean isTemplate) {
      this.isTemplate = isTemplate;
    }

    @Override
    public String getTemplateContext() {
      return templateContext;
    }

    public void setTemplateContext(String templateContext) {
      this.templateContext = templateContext;
    }
  }
} 