package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.servicemodel.Ap.Fields.MESH;
import static com.ruckus.cloud.wifi.eda.servicemodel.Venue.Fields.LASTWIFIFIRMWAREVERSIONUPDATE;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.VenueCmnCfgCollectorOperationBuilder.KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.VenueCmnCfgCollectorOperationBuilder.KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_CMN_CFG_OPERATION_BUILDER_BULK_PRE_LOAD_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.tuple;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.repository.RogueClassificationPolicyVenueRepository;
import com.ruckus.cloud.wifi.repository.TenantAvailableApFirmwareRepository;
import com.ruckus.cloud.wifi.repository.VenueCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesImpl;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.utils.DateUtils;
import com.ruckus.cloud.wifi.servicemodel.projection.ApModelVersionProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
@FeatureFlag(enable = {
    AP_FW_MGMT_UPGRADE_BY_MODEL,
    WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE,
    WIFI_EDA_CMN_CFG_OPERATION_BUILDER_BULK_PRE_LOAD_TOGGLE
})
public class VenueCmnCfgCollectorOperationBuilderTest {

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @MockBean
  VenueCurrentFirmwareRepository venueCurrentFirmwareRepository;

  @MockBean
  TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository;

  @MockBean
  RogueClassificationPolicyVenueRepository rogueClassificationPolicyVenueRepository;

  @SpyBean
  VenueCmnCfgCollectorOperationBuilder unit;

  @Test
  void testGetEntityClass() {
    assertThat(unit.entityClass()).isEqualTo(Venue.class);
  }

  @Test
  void testGetIndex() {
    assertThat(unit.index()).isEqualTo(EsConstants.Index.VENUE);
  }

  @Nested
  class testBuildConfig {

    @Test
    @FeatureFlag(disable = WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
    public void givenFFIsDisabled(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      var venue = VenueTestFixture.randomVenue(tenant);
      unit.config(builder, venue, EntityAction.MODIFY);

      assertThat(builder.build()).isNotNull()
          .extracting(Operations::getDocMap)
          .extracting(docMap -> docMap.get(Key.CURRENT_AP_FIRMWARES))
          .isNull();
    }

    @Test
    public void givenUpdateVenue(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      var apVersion = ApVersionTestFixture.recommendedApVersion("*********.400", c -> {
      });
      var venue = VenueTestFixture.randomVenue(tenant);

      venue.setLastWifiFirmwareVersionUpdate(DateUtils.getSysDate());
      var r550Vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, "R550");
      var r750Vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, "R750");
      doReturn(List.of(r550Vcf, r750Vcf)).when(venueCurrentFirmwareRepository)
          .findByTenantIdAndVenueId(tenant.getId(), venue.getId());
      doReturn(
          List.of(newApModelVersionProjection("R550", apVersion.getId()),
              newApModelVersionProjection("R750", apVersion.getId())))
          .when(tenantAvailableApFirmwareRepository).getModelLatestSupportedRecommendedVersionByTenantId(any());
      // when
      unit.config(builder, venue, EntityAction.MODIFY);
      // then
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.LAST_AP_FIRMWARE_UPDATE))
          .extracting(Value::getStringValue)
          .isNotNull();
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE))
          .extracting(Value::getBoolValue)
          .isEqualTo(true);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.CURRENT_AP_FIRMWARES))
          .extracting(value -> value.getListValue().getValuesList())
          .satisfies(values -> {
            assertThat(values)
                .extracting(
                    v -> v.getStructValue().getFieldsMap().get(Key.AP_MODEL).getStringValue(),
                    v -> v.getStructValue().getFieldsMap().get(Key.FIRMWARE).getStringValue())
                .containsExactlyInAnyOrder(
                    tuple("R550", apVersion.getId()),
                    tuple("R750", apVersion.getId()));
          });
    }

    @Test
    public void givenUpdateVenueWithEmptyModelLatestSupportedVersions(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      var apVersion = ApVersionTestFixture.recommendedApVersion("*********.400", c -> {
      });
      var venue = VenueTestFixture.randomVenue(tenant);
      venue.setLastWifiFirmwareVersionUpdate(DateUtils.getSysDate());
      var r550Vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, "R550");
      var r750Vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, "R750");
      doReturn(List.of(r550Vcf, r750Vcf)).when(venueCurrentFirmwareRepository)
          .findByTenantIdAndVenueId(tenant.getId(), venue.getId());
      doReturn(List.of()).when(tenantAvailableApFirmwareRepository)
          .getModelLatestSupportedRecommendedVersionByTenantId(any());
      // when
      unit.config(builder, venue, EntityAction.MODIFY);
      // then
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.LAST_AP_FIRMWARE_UPDATE))
          .extracting(Value::getStringValue)
          .isNotNull();
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE))
          .extracting(Value::getBoolValue)
          .isEqualTo(false);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.CURRENT_AP_FIRMWARES))
          .extracting(value -> value.getListValue().getValuesList())
          .satisfies(values -> {
            assertThat(values)
                .extracting(
                    v -> v.getStructValue().getFieldsMap().get(Key.AP_MODEL).getStringValue(),
                    v -> v.getStructValue().getFieldsMap().get(Key.FIRMWARE).getStringValue())
                .containsExactlyInAnyOrder(
                    tuple("R550", apVersion.getId()),
                    tuple("R750", apVersion.getId()));
          });
    }

    @Test
    public void givenUpdateVenueWithEmptyVenueCurrentFirmwares(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      var venue = VenueTestFixture.randomVenue(tenant);
      venue.setLastWifiFirmwareVersionUpdate(DateUtils.getSysDate());
      doReturn(List.of()).when(venueCurrentFirmwareRepository)
          .findByTenantIdAndVenueId(tenant.getId(), venue.getId());
      // when
      unit.config(builder, venue, EntityAction.MODIFY);
      // then
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.LAST_AP_FIRMWARE_UPDATE))
          .extracting(Value::getNullValue)
          .isEqualTo(NullValue.NULL_VALUE);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE))
          .extracting(Value::getNullValue)
          .isEqualTo(NullValue.NULL_VALUE);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.CURRENT_AP_FIRMWARES))
          .extracting(Value::getListValue)
          .extracting(ListValue::getValuesList)
          .asList().isEmpty();
    }

    @Test
    public void givenUpdateVenue_whenLastWifiFirmwareVersionUpdatedDateIsNull(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      var venue = VenueTestFixture.randomVenue(tenant);
      venue.setLastWifiFirmwareVersionUpdate(null);
      doReturn(List.of()).when(venueCurrentFirmwareRepository)
          .findByTenantIdAndVenueId(tenant.getId(), venue.getId());
      // when
      unit.config(builder, venue, EntityAction.MODIFY);
      // then
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.LAST_AP_FIRMWARE_UPDATE))
          .extracting(Value::getNullValue)
          .isEqualTo(NullValue.NULL_VALUE);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE))
          .extracting(Value::getNullValue)
          .isEqualTo(NullValue.NULL_VALUE);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.CURRENT_AP_FIRMWARES))
          .extracting(Value::getListValue)
          .extracting(ListValue::getValuesList)
          .asList().isEmpty();
    }

    @Nested
    @FeatureFlag(enable = WIFI_EDA_CMN_CFG_OPERATION_BUILDER_BULK_PRE_LOAD_TOGGLE)
    class withPreloadData {
      @Test
      public void givenUpdateVenue(Tenant tenant) {
        Operations.Builder builder = Operations.newBuilder();
        var apVersion = ApVersionTestFixture.recommendedApVersion("*********.400", c -> {});
        var venue = VenueTestFixture.randomVenue(tenant);
        venue.setLastWifiFirmwareVersionUpdate(DateUtils.getSysDate());
        var r550Vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, "R550");
        var r750Vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, "R750");
        var r550VersionProj = newApModelVersionProjection("R550", apVersion.getId());
        var r750VersionProj = newApModelVersionProjection("R750", apVersion.getId());
        Map<Object, Object> preloadData = new HashMap<>();
        preloadData.put(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES, List.of(r550Vcf, r750Vcf));
        preloadData.put(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES, List.of(r550VersionProj, r750VersionProj));
        // when
        unit.config(builder, venue, EntityAction.MODIFY, preloadData);
        // then
        assertThat(builder.build().getDocMap())
            .extracting(docMap -> docMap.get(Key.LAST_AP_FIRMWARE_UPDATE))
            .extracting(Value::getStringValue)
            .isNotNull();
        assertThat(builder.build().getDocMap())
            .extracting(docMap -> docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE))
            .extracting(Value::getBoolValue)
            .isEqualTo(true);
        assertThat(builder.build().getDocMap())
            .extracting(docMap -> docMap.get(Key.CURRENT_AP_FIRMWARES))
            .extracting(value -> value.getListValue().getValuesList())
            .satisfies(values -> {
              assertThat(values)
                  .extracting(
                      v -> v.getStructValue().getFieldsMap().get(Key.AP_MODEL).getStringValue(),
                      v -> v.getStructValue().getFieldsMap().get(Key.FIRMWARE).getStringValue())
                  .containsExactlyInAnyOrder(
                      tuple("R550", apVersion.getId()),
                      tuple("R750", apVersion.getId()));
            });
        verify(venueCurrentFirmwareRepository, never()).findByTenantIdAndVenueId(any(), any());
        verify(tenantAvailableApFirmwareRepository, never()).getModelLatestSupportedRecommendedVersionByTenantId(any());
      }
    }
  }

  @Nested
  class testPreload {
    @Test
    public void givenEntityActionIsDelete_skipPreload() {
      var venue = new Venue(randomId());
      var venueTxEntity = new TxEntity<>(venue, EntityAction.DELETE);
      // When
      unit.preload(List.of(venueTxEntity), emptyTxChanges());
      // Then
      assertThat(venueTxEntity.getPreloadedData().get(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES)).isNull();
      assertThat(venueTxEntity.getPreloadedData().get(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES)).isNull();
      verify(venueCurrentFirmwareRepository, never()).findByTenantIdAndVenueIdIn(any(), any());
      verify(tenantAvailableApFirmwareRepository, never()).getModelLatestSupportedRecommendedVersionByTenantId(any());
    }

    @Test
    public void givenEntityActionIsNotDelete_preload() {
      var apVersion = ApVersionTestFixture.recommendedApVersion("*********.400", c -> {});
      var venue = new Venue(randomId());
      var venueTxEntity = new TxEntity<>(venue, EntityAction.MODIFY);
      var r550Vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, "R550");
      var r750Vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, "R750");
      doReturn(List.of(r550Vcf, r750Vcf)).when(venueCurrentFirmwareRepository).findByTenantIdAndVenueIdIn(any(), any());
      doReturn(List.of(
          newApModelVersionProjection("R550", apVersion.getId()),
          newApModelVersionProjection("R750", apVersion.getId())))
          .when(tenantAvailableApFirmwareRepository).getModelLatestSupportedRecommendedVersionByTenantId(any());
      TxChanges txChanges = new TxChangesImpl(null);
      txChanges.add(venue, Set.of("name"), EntityAction.MODIFY);
      venueTxEntity.setTaggedDirty(true);
      // When
      unit.preload(List.of(venueTxEntity), txChanges);
      // Then
      assertThat(venueTxEntity.getPreloadedData().get(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES)).asList()
          .extracting(c -> (VenueCurrentFirmware) c)
          .extracting(VenueCurrentFirmware::getApModel)
          .containsExactlyInAnyOrder("R550", "R750");
      assertThat(venueTxEntity.getPreloadedData().get(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES)).asList()
          .extracting(c -> (ApModelVersionProjection) c)
          .extracting(ApModelVersionProjection::getApModel)
          .containsExactlyInAnyOrder("R550", "R750");
      verify(venueCurrentFirmwareRepository, times(1)).findByTenantIdAndVenueIdIn(any(), any());
      verify(tenantAvailableApFirmwareRepository, times(1)).getModelLatestSupportedRecommendedVersionByTenantId(any());
    }

    @Test
    @FeatureFlag(disable = WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
    public void givenFirmwareManagementRbacFfIsOff_skipPreload() {
      var venue = new Venue(randomId());
      TxChanges txChanges = new TxChangesImpl(null);
      txChanges.add(venue, Set.of("name"), EntityAction.MODIFY);
      var venueTxEntity = new TxEntity<>(venue, EntityAction.MODIFY);
      venueTxEntity.setTaggedDirty(true);
      // When
      unit.preload(List.of(venueTxEntity), txChanges);
      // Then
      assertThat(venueTxEntity.getPreloadedData().get(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES)).isNull();
      assertThat(venueTxEntity.getPreloadedData().get(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES)).isNull();
      verify(venueCurrentFirmwareRepository, never()).findByTenantIdAndVenueIdIn(any(), any());
      verify(tenantAvailableApFirmwareRepository, never()).getModelLatestSupportedRecommendedVersionByTenantId(any());
    }
  }

  @Nested
  class testClearPreload {
    @Test
    public void givenEntityPreloadedDataIsNotEmpty() {
      var venueTxEntityA = new TxEntity<>(new Venue(randomId()), EntityAction.MODIFY);
      var venueTxEntityB = new TxEntity<>(new Venue(randomId()), EntityAction.MODIFY);
      venueTxEntityA.getPreloadedData().put(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES, List.of("test"));
      venueTxEntityA.getPreloadedData().put(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES, List.of("test"));
      venueTxEntityA.getPreloadedData().put("OTHER_KEY", List.of("test"));
      venueTxEntityB.getPreloadedData().put(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES, List.of("test"));
      venueTxEntityB.getPreloadedData().put(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES, List.of("test"));
      venueTxEntityA.setTaggedDirty(true);
      venueTxEntityB.setTaggedDirty(true);
      // When
      unit.clearPreload(List.of(venueTxEntityA, venueTxEntityB), emptyTxChanges());
      // Then
      assertThat(venueTxEntityA.getPreloadedData().get(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES)).isNull();
      assertThat(venueTxEntityA.getPreloadedData().get(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES)).isNull();
      assertThat(venueTxEntityA.getPreloadedData().get("OTHER_KEY")).isNotNull();
      assertThat(venueTxEntityB.getPreloadedData().get(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES)).isNull();
      assertThat(venueTxEntityB.getPreloadedData().get(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES)).isNull();
    }

    @Test
    public void givenEntityPreloadedDataIsEmpty() {
      var venueTxEntityA = new TxEntity<>(new Venue(randomId()), EntityAction.MODIFY);
      var venueTxEntityB = new TxEntity<>(new Venue(randomId()), EntityAction.MODIFY);
      venueTxEntityA.setTaggedDirty(true);
      venueTxEntityB.setTaggedDirty(true);
      // When
      unit.clearPreload(List.of(venueTxEntityA, venueTxEntityB), emptyTxChanges());
      // Then
      assertThat(venueTxEntityA.getPreloadedData().get(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES)).isNull();
      assertThat(venueTxEntityA.getPreloadedData().get(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES)).isNull();
      assertThat(venueTxEntityB.getPreloadedData().get(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES)).isNull();
      assertThat(venueTxEntityB.getPreloadedData().get(KEY_PRE_LOAD_TENANT_AVAILABLE_AP_FIRMWARES)).isNull();
    }

    @Test
    @FeatureFlag(disable = WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
    public void givenFirmwareManagementRbacFfIsOff_skipClear() {
      var venueTxEntityA = new TxEntity<>(new Venue(randomId()), EntityAction.MODIFY);
      venueTxEntityA.getPreloadedData().put(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES, List.of("test"));
      venueTxEntityA.setTaggedDirty(true);
      // When
      unit.clearPreload(List.of(venueTxEntityA), emptyTxChanges());
      // Then
      assertThat(venueTxEntityA.getPreloadedData().get(KEY_PRE_LOAD_VENUE_CURRENT_FIRMWARES)).isNotNull();

    }
  }

  @Nested
  class testHasModification {

    @Test
    public void givenEntityActionIsNotModify() {
      assertThat(unit.hasModification(EntityAction.ADD, Collections.emptySet(), false)).isTrue();
    }

    @Test
    public void givenAnyFields() {
      assertThat(unit.hasModification(EntityAction.MODIFY, Set.of(LASTWIFIFIRMWAREVERSIONUPDATE), false))
          .isTrue();
      assertThat(unit.hasModification(EntityAction.MODIFY, Set.of(MESH), false))
          .isTrue();
    }
  }

  public ApModelVersionProjection newApModelVersionProjection(String apModel, String apVersion) {
    return new ApModelVersionProjection() {
      @Override
      public String getApModel() {
        return apModel;
      }

      @Override
      public String getApVersion() {
        return apVersion;
      }
    };
  }
}
