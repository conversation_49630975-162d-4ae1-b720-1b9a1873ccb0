package com.ruckus.cloud.wifi.integration.clientIsolationonlanport;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.newLanPortAdoption;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice.ImpactDevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.CcmUserSidePort;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.CcmWiredClientIsolation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.IsolatePacketsTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.VenueApModelLanPortSettingsV1Generator;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelLanPortSettingsV1;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.EthernetPortProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.LanPortAdoptionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueApModelSpecificAttributesTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueLanPortTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("EthernetPortProfileTest")
@FeatureFlag(enable = {
    FlagNames.ACX_UI_ETHERNET_TOGGLE,
    FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
    FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
@WifiIntegrationTest
class ConsumeUpdateVenueApModelLanPortOverwriteSettingsForClientIsolationRequestTest {

  private record VenueLanPortData(
      VenueApModelSpecificAttributes modelAttributes,
      VenueLanPort port,
      EthernetPortProfile profile) {

  }

  private final Map<IsolatePacketsTypeEnum, Boolean> MULTICAST_ENABLED = Map.of(
      IsolatePacketsTypeEnum.MULTICAST, true,
      IsolatePacketsTypeEnum.UNICAST_MULTICAST, true,
      IsolatePacketsTypeEnum.UNICAST, false
  );

  private final Map<IsolatePacketsTypeEnum, Boolean> UNICAST_ENABLED = Map.of(
      IsolatePacketsTypeEnum.UNICAST, true,
      IsolatePacketsTypeEnum.UNICAST_MULTICAST, true,
      IsolatePacketsTypeEnum.MULTICAST, false
  );
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  @Autowired private LanPortAdoptionServiceImpl lanPortAdoptionService;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeEnableClientIsolationOnVenueApModelLanPortRequestTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator = Generators.venueApModelLanPortSettingsV1()
        .setClientIsolationEnabled(always(true))
        .setClientIsolationSettings(Generators.clientIsolationSettings())
        .setEnabled(always(true));

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R320";
    private String portId = "1";
    private String venueLanPortId;
    private String clientIsolationAllowlistId;

    @BeforeEach
    void givenOnePersistedInDb(final Venue venue) {
      venueId = venue.getId();
      var portData1 = createVenueLanPortData(venue, 3, apModel, portId, null);
      venueLanPortId = portData1.port.getId();
      ethernetPortProfileId = portData1.port.getLanPortAdoption().getEthernetPortProfileId().toString();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS,
          clientIsolationAllowlistId, venueLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeUpdateClientIsolationOnVenueApModelLanPortAndExistClientIsolationAllowlistRequestTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator = Generators.venueApModelLanPortSettingsV1()
        .setClientIsolationEnabled(always(true))
        .setClientIsolationSettings(Generators.clientIsolationSettings())
        .setEnabled(always(true));

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R320";
    private String portId = "1";
    private String venueLanPortId;
    private String clientIsolationAllowlistId;

    @BeforeEach
    void givenOnePersistedInDb(final Venue venue, ClientIsolationAllowlist clientIsolationAllowlist) {

      ClientIsolationLanPortActivation clientIsolationLanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.clientIsolationLanPortActivation()
              .generate();
      clientIsolationLanPortActivation.setClientIsolationAllowlist(clientIsolationAllowlist);
      clientIsolationAllowlistId = clientIsolationAllowlist.getId();

      venueId = venue.getId();
      var portData1 = createVenueLanPortData(venue, 3, apModel, portId, clientIsolationLanPortActivation);
      venueLanPortId = portData1.port.getId();
      ethernetPortProfileId = portData1.port.getLanPortAdoption().getEthernetPortProfileId().toString();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS,
          clientIsolationAllowlistId, venueLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeDisableClientIsolationOnVenueApModelLanPortRequestTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator = Generators.venueApModelLanPortSettingsV1()
        .setClientIsolationEnabled(always(false))
        .setEnabled(always(true));

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R320";
    private String portId = "1";
    private String venueLanPortId;
    private String clientIsolationAllowlistId;

    @BeforeEach
    void givenOnePersistedInDb(final Venue venue) {
      venueId = venue.getId();

      ClientIsolationLanPortActivation clientIsolationLanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.clientIsolationLanPortActivation()
              .generate();

      var portData1 = createVenueLanPortData(venue, 3, apModel, portId, clientIsolationLanPortActivation);
      venueLanPortId = portData1.port.getId();
      ethernetPortProfileId = portData1.port.getLanPortAdoption().getEthernetPortProfileId().toString();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS,
          clientIsolationAllowlistId, venueLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeDisablePortOnVenueApModelLanPortRequestTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator = Generators.venueApModelLanPortSettingsV1()
        .setClientIsolationEnabled(always(true))
        .setClientIsolationSettings(Generators.clientIsolationSettings())
        .setEnabled(always(false));

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R550";
    private String portId = "1";
    private String venueLanPortId;
    private String clientIsolationAllowlistId;

    @BeforeEach
    void givenOnePersistedInDb(final Venue venue) {
      venueId = venue.getId();

      ClientIsolationLanPortActivation clientIsolationLanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.clientIsolationLanPortActivation()
              .generate();

      var portData1 = createVenueLanPortData(venue, 3, apModel, portId, clientIsolationLanPortActivation);
      var portData2 = createVenueLanPortData(venue, 4, apModel, "2", null);
      venueLanPortId = portData1.port.getId();
      ethernetPortProfileId = portData1.port.getLanPortAdoption().getEthernetPortProfileId().toString();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {

      VenueApModelLanPortSettingsV1 expected = new VenueApModelLanPortSettingsV1();
      expected.setClientIsolationEnabled(false);
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS,
          clientIsolationAllowlistId, venueLanPortId, expected);
    }
  }


  private void validateEthernetPortProfileToVenueApModelLanPortActivationResult(
      CfgAction apiAction,
      String clientIsolationAllowlistId,
      String venueLanPortId,
      VenueApModelLanPortSettingsV1 expectedVenueApModelLanPortSettings) {
    validateRepositoryData(venueLanPortId,
        expectedVenueApModelLanPortSettings, clientIsolationAllowlistId);
    validateDdccmCfgRequestMessages(
        venueLanPortId, expectedVenueApModelLanPortSettings, clientIsolationAllowlistId);
    validateCmnCfgCollectorMessages(expectedVenueApModelLanPortSettings);
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(String venueLanPortId,
      VenueApModelLanPortSettingsV1 expectedVenueApModelLanPortSettings, String clientIsolationAllowlistId) {

    assertThat(venueLanPortId).isNotNull();

    final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
    if (expectedVenueApModelLanPortSettings.getClientIsolationEnabled()) {
      var clientIsolationActivation = assertThat(venueLanPort)
          .extracting(VenueLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNotNull();
      clientIsolationActivation.matches(activation -> activation.getAutoVrrp()
              .equals(expectedVenueApModelLanPortSettings.getClientIsolationSettings().getAutoVrrp()))
          .matches(activation -> activation.getPacketsType()
              == expectedVenueApModelLanPortSettings.getClientIsolationSettings()
              .getPacketsType());
      if (clientIsolationAllowlistId != null) {
        clientIsolationActivation.extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
            .isNotNull()
            .matches(c -> c.getId().equals(clientIsolationAllowlistId));
      }

    } else {
      assertThat(venueLanPort)
          .extracting(VenueLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNull();
    }


  }

  private void validateDdccmCfgRequestMessages(String venueLanPortId,
      VenueApModelLanPortSettingsV1 expectedVenueApModelLanPortSettings, String clientIsolationAllowlistId) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();
    final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
    var inUseEthernetPortProfileId = venueLanPort.getLanPortAdoption().getEthernetPortProfileId();
    var operations = assertThat(ddccmCfgRequestMessage.getPayload())
        .extracting(WifiConfigRequest::getOperationsList)
        .asList()
        .isNotEmpty()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast);
    operations.filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .allSatisfy(op -> {
          assertThat(op)
              .extracting(
                  com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
              .matches(venue -> venue.getVenueApModelsList().stream()
                  .anyMatch(model -> model.getLanPortList().stream()
                      .anyMatch(lanPort -> inUseEthernetPortProfileId.equals(lanPort.getApLanPortProfileId()))));
        });
    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApLanPortProfile)
        .filteredOn(operation -> operation.getAction() == Action.ADD)
        .isNotEmpty()
        .allSatisfy(op -> assertThat(op)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
            .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
            .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
            .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .allSatisfy(
            op -> verifyAddAction(op, venueLanPort, expectedVenueApModelLanPortSettings, clientIsolationAllowlistId));
    operations
        .filteredOn(operation -> operation.getAction() == Action.DELETE)
        .isNotEmpty()
        .allSatisfy(this::verifyDeleteAction);

  }

  private void verifyAddAction(com.ruckus.acx.ddccm.protobuf.wifi.Operation operation, VenueLanPort venueLanPort,
      VenueApModelLanPortSettingsV1 venueApModelLanPortSettings, String clientIsolationAllowlistId) {
    var apLanPortProfileAssert = assertThat(operation)
        .extracting(
            com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile);

    apLanPortProfileAssert.matches(apLanPortProfile -> venueLanPort.getLanPortAdoption().getId()
            .equals(apLanPortProfile.getName()))
        .matches(apLanPortProfile -> venueLanPort.getLanPortAdoption().getEthernetPortProfileId()
            .equals(apLanPortProfile.getId()))
        .matches(apLanPortProfile -> (venueLanPort.getApLanPortProfile().getType().name() + "PORT")
            .equals(apLanPortProfile.getLanPortType().name()))
        .matches(apLanPortProfile -> venueLanPort.getApLanPortProfile().getUntagId()
            .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(apLanPortProfile -> venueLanPort.getApLanPortProfile().getUntagId()
            .equals((short) apLanPortProfile.getUntagId()));

    if (venueApModelLanPortSettings.getClientIsolationEnabled()) {
      var clientIsolationSettings = venueApModelLanPortSettings.getClientIsolationSettings();
      apLanPortProfileAssert
          .extracting(ApLanPortProfile::getClientIsolation)
          .isNotNull()
          .matches(CcmWiredClientIsolation::getClientIsolationEnabled)
          .matches(ccmWiredClientIsolation -> clientIsolationSettings.getAutoVrrp()
              .equals(ccmWiredClientIsolation.getClientIsolationAutoVrrpEnabled()))
          .matches(ccmWiredClientIsolation -> MULTICAST_ENABLED.get(
                  clientIsolationSettings.getPacketsType())
              .equals(ccmWiredClientIsolation.getClientIsolationMulticastEnabled()))
          .matches(ccmWiredClientIsolation -> UNICAST_ENABLED.get(
                  clientIsolationSettings.getPacketsType())
              .equals(ccmWiredClientIsolation.getClientIsolationUnicastEnabled()));
      if (clientIsolationAllowlistId != null) {
        apLanPortProfileAssert
            .extracting(ApLanPortProfile::getClientIsolation)
            .matches(ccmWiredClientIsolation -> clientIsolationAllowlistId
                .equals(ccmWiredClientIsolation.getClientIsolationWhitelistId()));
      }

      apLanPortProfileAssert
          .extracting(ApLanPortProfile::getUserSidePort)
          .isNotNull()
          .matches(CcmUserSidePort::getUserSidePortEnabled)
          .matches(ccmUserSidePort -> ccmUserSidePort.getUserSidePortMaxClient().getValue() == 32);

    } else {
      apLanPortProfileAssert
          .extracting(ApLanPortProfile::getClientIsolation)
          .matches(ccmWiredClientIsolation -> ApLanPortProfile.CcmWiredClientIsolation.getDefaultInstance()
              .equals(ccmWiredClientIsolation));
      apLanPortProfileAssert
          .extracting(ApLanPortProfile::getUserSidePort)
          .matches(ccmUserSidePort -> ApLanPortProfile.CcmUserSidePort.getDefaultInstance().equals(ccmUserSidePort));
    }

  }

  private void verifyDeleteAction(com.ruckus.acx.ddccm.protobuf.wifi.Operation operation) {
    assertThat(operation)
        .extracting(
            com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
        .isNotNull();
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ImpactDevice::getDeviceIdsCount).isEqualTo(0);
  }

  private void validateCmnCfgCollectorMessages(VenueApModelLanPortSettingsV1 venueApModelLanPortSettings) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ETHERNET_PORT_PROFILE -> ApiFlowNames.ADD_ETHERNET_PORT_PROFILE;
      case UPDATE_ETHERNET_PORT_PROFILE -> ApiFlowNames.UPDATE_ETHERNET_PORT_PROFILE;
      case DELETE_ETHERNET_PORT_PROFILE -> ApiFlowNames.DELETE_ETHERNET_PORT_PROFILE;
      case ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> ApiFlowNames.ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE;
      case DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> ApiFlowNames.DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE;
      case ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT ->
          ApiFlowNames.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT;
      case DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT ->
          ApiFlowNames.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT;
      case ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT -> ApiFlowNames.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT;
      case DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT ->
          ApiFlowNames.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT;
      case UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS ->
          ApiFlowNames.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS;
      case UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS -> ApiFlowNames.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS;

      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private EthernetPortProfile createEthernetPortProfile(Venue venue, int ethernetPortProfileId) {
    return repositoryUtil.createOrUpdate(EthernetPortProfileTestFixture.randomEthernetPortProfile(venue, e ->
        {
          e.setApLanPortId(ethernetPortProfileId);
          e.setName(randomName());
          e.setType(ApLanPortTypeEnum.TRUNK);
          e.setUntagId((short) 1);
          e.setVlanMembers("1-4094");
          e.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
        }), txCtxExtension.getTenantId(),
        randomTxId());
  }

  private VenueLanPortData createVenueLanPortData(Venue venue, int ethernetPortProfileId, String apModel,
      String portId, ClientIsolationLanPortActivation clientIsolationLanPortActivation) {

    var apLanPortProfile = createEthernetPortProfile(venue, ethernetPortProfileId);

    VenueApModelSpecificAttributes modelAttributes;
    var findVenueApModel=venue.getModelSpecificAttributes().stream().filter(m->apModel.equals(m.getModel())).findAny();

    if (findVenueApModel.isPresent()) {
      modelAttributes = findVenueApModel.get();
    } else {
      modelAttributes = repositoryUtil.createOrUpdate(
          VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(
              venue, v -> {
                v.setModel(apModel);
                v.setLanPorts(new ArrayList<>());
              }), txCtxExtension.getTenantId(),
          randomTxId());
      venue.getModelSpecificAttributes().add(modelAttributes);
    }

    var port = repositoryUtil.createOrUpdate(
        VenueLanPortTestFixture.randomVenueLanPort(venue, modelAttributes, v -> {
          v.setApLanPortProfile(apLanPortProfile);
          v.setPortId(portId);
          v.setVenueApModelSpecificAttributes(modelAttributes);
        }), txCtxExtension.getTenantId(), randomTxId());

    final var finalClientIsolationLanPortActivation = clientIsolationLanPortActivation;

    var adoption =
        repositoryUtil.createOrUpdate(
            LanPortAdoptionTestFixture.randomLanPortAdoption(
                apLanPortProfile,
                customizer -> {
                  customizer.setChecksum(
                      lanPortAdoptionService.calculateChecksum(
                          newLanPortAdoption(
                              apLanPortProfile, finalClientIsolationLanPortActivation)));
                }),
            txCtxExtension.getTenantId(),
            randomTxId());
    if (clientIsolationLanPortActivation != null) {
      clientIsolationLanPortActivation.setLanPortAdoption(adoption);
      repositoryUtil.createOrUpdate(clientIsolationLanPortActivation,
          txCtxExtension.getTenantId(), randomTxId());
    }
    port.setLanPortAdoption(adoption);
    port = repositoryUtil.createOrUpdate(port, txCtxExtension.getTenantId(),
        port.getId());

    return new VenueLanPortData(modelAttributes, port, apLanPortProfile);
  }

}
