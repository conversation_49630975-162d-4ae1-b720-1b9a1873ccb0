package com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request;

import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.VENUE_ID;
import static com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture.randomRadiusTemplate;
import static com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture.randomRadiusTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.TenantTestFixture.randomTenant;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenue;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplateInstance;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.ActivateRadiusServerProfileOnVenueInstanceOperation;
import com.ruckus.cloud.wifi.repository.RadiusRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;

@ExtendWith(TxCtxExtension.class)
@WifiUnitTest
public class ActivateRadiusServerProfileOnVenueRequestBuilderTest {

  @Autowired
  private ActivateRadiusServerProfileOnVenueRequestBuilder unit;
  @MockBean
  private VenueRepository venueRepository;
  @MockBean
  private RadiusRepository radiusRepository;

  private Tenant mspTenant;
  private Tenant ecTenant;
  private Venue venueInstance;
  private Venue venueTemplate;
  private Radius radiusTemplate;
  private Radius radiusInstance;

  @BeforeEach
  public void setUp() {
    ecTenant = randomTenant((e) -> {
    });
    mspTenant = randomTenant(e -> e.setId(TxCtxHolder.tenantId()));
    unit.setTemplateTenantId(mspTenant.getId());
    unit.setTargetTenantId(ecTenant.getId());
    radiusTemplate = randomRadiusTemplate(mspTenant);
    radiusInstance = randomRadiusTemplateInstance(radiusTemplate.getId(), ecTenant);
    venueTemplate = randomVenue(
        mspTenant, v -> {
          v.setIsTemplate(true);
          v.setAuthRadius(radiusTemplate);
        });
    venueInstance = randomVenueTemplateInstance(venueTemplate.getId(),
        ecTenant);

  }

  @Test
  void testBuildList() {
    doReturn(List.of(radiusInstance))
        .when(radiusRepository)
        .findByTemplateIdInAndTenantId(anyList(),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));

    var result = unit.buildList(venueTemplate);

    assertThat(result).isNotEmpty();

    var request = result.get(0).request();
    var templateEntity = result.get(0).templateEntity();

    assertThat(request).satisfies(e -> {
      assertThat(e.getTargetTenantId()).isEqualTo(ecTenant.getId());
      assertThat(e.getInstanceId()).isNotEmpty();
      assertThat(e.getOverrides()).isNull();
    });
    assertThat(request.getExtraPathVariables())
        .containsOnly(entry(VENUE_ID, venueInstance.getId()),
            entry("radiusId", radiusInstance.getId()));

    assertThat(templateEntity)
        .isEqualTo(venueTemplate);
  }

  @Test
  void testBuildList_EntityIsNotTemplate() {
    final var nonTemplateVenue = randomVenue(
        mspTenant, v -> v.setAuthRadius(radiusTemplate));
    doReturn(List.of(radiusInstance))
        .when(radiusRepository)
        .findByTemplateIdInAndTenantId(anyList(),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(anyString(), eq(ecTenant.getId()));

    assertThrows(CommonException.class, () -> unit.buildList(nonTemplateVenue));
  }

  @Test
  void testBuildList_VenueInstanceNotFound() {
    doReturn(Optional.empty())
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(NoSuchElementException.class, () -> unit.buildList(venueTemplate));
  }

  @Test
  void testBuildList_NetworkInstanceNotFound() {
    doReturn(Collections.EMPTY_LIST)
        .when(radiusRepository)
        .findByTemplateIdInAndTenantId(anyList(),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(CommonException.class, () -> unit.buildList(venueTemplate));
  }

  @TestConfiguration
  static class TestConfig {

    @MockBean
    private ActivateRadiusServerProfileOnVenueInstanceOperation operation;

    @Bean
    ActivateRadiusServerProfileOnVenueRequestBuilder activateRadiusServerProfileOnVenueRequestBuilder(
        VenueRepository venueRepository, RadiusRepository radiusRepository,
        ActivateRadiusServerProfileOnVenueInstanceOperation operation) {
      return new ActivateRadiusServerProfileOnVenueRequestBuilder(venueRepository,
          radiusRepository, operation);
    }
  }
}
