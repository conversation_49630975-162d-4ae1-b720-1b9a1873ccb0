package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.AbstractNetworkCfgCollectorOperationBuilderTest.MockVenueApGroupsProjection;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.service.template.TemplateManagementService;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApGroupsProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@WifiUnitTest
public class GuestNetworkCfgCollectorOperationBuilderTest {

  @MockBean
  private RepositoryUtil repositoryUtil;
  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private TemplateManagementService templateManagementService;
  @SpyBean
  private GuestNetworkCfgCollectorOperationBuilder guestNetworkCfgCollectorOperationBuilder;
  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  public void testGetEntityClass() {
    assertThat(new GuestNetworkCfgCollectorOperationBuilder().entityClass())
        .isEqualTo(GuestNetwork.class);
  }

  @Nested
  class testBuildConfig {

    final String apSerialNumber = "123456789012";

    @Test
    @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
    public void givenAddOweTransitionNetwork() {
      Operations.Builder builder = Operations.newBuilder();
      var tenant = new Tenant(txCtxExtension.getTenantId());
      final var venueId = randomId();
      final var apGroupId = randomId();
      final var guestNetwork1 = (GuestNetwork) network(GuestNetwork.class).generate();
      final var portal = Generators.guestPortal(GuestNetworkTypeEnum.WISPr).generate();
      final var page = Generators.wisprPage().generate();
      portal.setWisprPage(page);
      guestNetwork1.setGuestPortal(portal);
      guestNetwork1.setName("guestNetwork1");
      guestNetwork1.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      guestNetwork1.setIsOweMaster(true);
      guestNetwork1.setOwePairNetworkId("childId");
      guestNetwork1.setTenant(tenant);
      guestNetwork1.setCreatedDate(new Date());
      guestNetwork1.setUpdatedDate(new Date());
      guestNetwork1.getWlan().setNetwork(guestNetwork1);
      repositoryUtil.createOrUpdate(guestNetwork1, tenant.getId(), randomTxId());
      var venueApGroupsProjections = new ArrayList<>();
      venueApGroupsProjections.add(
          new MockVenueApGroupsProjection(
              venueId, apGroupId, true));

      doReturn(new PageImpl<>(venueApGroupsProjections), Page.empty()).when(
              networkRepository)
          .findVenueApGroupsByNetworkId(
              eq(txCtxExtension.getTenantId()), eq(guestNetwork1.getId()), any());
      guestNetworkCfgCollectorOperationBuilder.config(builder, guestNetwork1, EntityAction.ADD);

      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getId());
      assertThat(docMap.get(Key.TYPE))
          .isEqualTo(EsConstants.Value.TYPE_NETWORK);
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getTenant().getId());
      assertThat(docMap.get(Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getName());
      assertThat(docMap.get(Key.NW_SUB_TYPE))
          .extracting(Value::getStringValue)
          .isEqualTo("guest");
      assertThat(docMap.get(Key.SECURITY_PROTOCOL))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getWlan().getWlanSecurity().name());
      assertThat(docMap.get(Key.OWE_PAIR_NETWORK_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getOwePairNetworkId());
      assertThat(docMap.get(Key.OWE_PAIR_NETWORK_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getOwePairNetworkId());
      assertThat(docMap.get(Key.IS_OWE_MASTER))
          .extracting(Value::getBoolValue)
          .isEqualTo(true);
      assertThat(docMap.get(Key.VENUE_APGROUPS))
          .extracting(Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(Value.class))
          .extracting(Value::getStructValue)
          .allSatisfy(vap -> {
            assertEquals(venueId, vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue());
            assertTrue(vap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertEquals(1,
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesCount());
            assertTrue(
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesList()
                    .contains(ValueUtils.stringValue(apGroupId)));
          });
    }

    @Test
    @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
    public void givenPureOweFromOweTransition() {
      Operations.Builder builder = Operations.newBuilder();
      var tenant = new Tenant(txCtxExtension.getTenantId());
      final var venueId = randomId();
      final var apGroupId = randomId();
      final var guestNetwork1 = (GuestNetwork) network(GuestNetwork.class).generate();
      final var portal = Generators.guestPortal(GuestNetworkTypeEnum.WISPr).generate();
      final var page = Generators.wisprPage().generate();
      portal.setWisprPage(page);
      guestNetwork1.setGuestPortal(portal);
      guestNetwork1.setName("guestNetwork2");
      guestNetwork1.getWlan().setWlanSecurity(WlanSecurityEnum.OWE);
      guestNetwork1.setIsOweMaster(null);
      guestNetwork1.setOwePairNetworkId(null);
      guestNetwork1.setTenant(tenant);
      guestNetwork1.setCreatedDate(new Date());
      guestNetwork1.setUpdatedDate(new Date());
      guestNetwork1.getWlan().setNetwork(guestNetwork1);
      repositoryUtil.createOrUpdate(guestNetwork1, tenant.getId(), randomTxId());
      var venueApGroupsQueryProjections = new ArrayList<>();
      venueApGroupsQueryProjections.add(
          new MockVenueApGroupsProjection(
              venueId, apGroupId, true));

      doReturn(new PageImpl<>(venueApGroupsQueryProjections), Page.empty()).when(
              networkRepository)
          .findVenueApGroupsByNetworkId(
              eq(txCtxExtension.getTenantId()), eq(guestNetwork1.getId()), any());
      guestNetworkCfgCollectorOperationBuilder.config(builder, guestNetwork1, EntityAction.MODIFY);

      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getId());
      assertThat(docMap.get(Key.TYPE))
          .isEqualTo(EsConstants.Value.TYPE_NETWORK);
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getTenant().getId());
      assertThat(docMap.get(Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getName());
      assertThat(docMap.get(Key.NW_SUB_TYPE))
          .extracting(Value::getStringValue)
          .isEqualTo("guest");
      assertThat(docMap.get(Key.SECURITY_PROTOCOL))
          .extracting(Value::getStringValue)
          .isEqualTo(guestNetwork1.getWlan().getWlanSecurity().name());
      assertThat(docMap.get(Key.OWE_PAIR_NETWORK_ID))
          .extracting(Value::getNullValue)
          .isEqualTo(NullValue.NULL_VALUE);
      assertThat(docMap.get(Key.IS_OWE_MASTER))
          .extracting(Value::getNullValue)
          .isEqualTo(NullValue.NULL_VALUE);
      assertThat(docMap.get(Key.VENUE_APGROUPS))
          .extracting(Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(Value.class))
          .extracting(Value::getStructValue)
          .allSatisfy(vap -> {
            assertEquals(venueId, vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue());
            assertTrue(vap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertEquals(1,
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesCount());
            assertTrue(
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesList()
                    .contains(ValueUtils.stringValue(apGroupId)));
          });
    }

    @Data
    @AllArgsConstructor
    static class VenueApGroupsApSerialNumbersProjectionIml implements
        VenueApGroupsProjection {

      String venueId;
      String apGroups;
      boolean isAllApGroups;
      String apSerialNumbers;

      @Override
      public boolean getIsAllApGroups() {
        return true;
      }
    }
  }
}
