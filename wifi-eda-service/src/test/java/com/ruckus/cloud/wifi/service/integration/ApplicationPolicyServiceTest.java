package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.servicemodel.enums.SortOrderEnum.DESC;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.ruckus.cloud.wifi.eda.service.AccessControlProfileServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicyQueryData;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicyQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackage;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApplicationPolicyProtocolEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApplicationTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AvcUserDefinedTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.mapper.ApplicationPolicyMerge;
import com.ruckus.cloud.wifi.mapper.ApplicationPolicyMergeImpl;
import com.ruckus.cloud.wifi.repository.AccessControlProfileRepository;
import com.ruckus.cloud.wifi.repository.ApplicationPolicyRepository;
import com.ruckus.cloud.wifi.repository.ApplicationPolicyRuleRepository;
import com.ruckus.cloud.wifi.repository.WlanRepository;
import com.ruckus.cloud.wifi.service.EntityIdGenerationLockService;
import com.ruckus.cloud.wifi.service.EntityIdGenerationLockService.BatchEntityLockEnum;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.ApplicationPolicyServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.NetworkServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.SignaturePackageServiceImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.ApplicationPolicyTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.TestPropertySource;

@Tag("ApplicationPolicyTest")
@WifiJpaDataTest
@TestPropertySource(properties = {"default.application-policy.tenant-max-count: 10"})
class ApplicationPolicyServiceTest {

  @SpyBean
  private ApplicationPolicyServiceCtrlImpl applicationPolicyService;
  @Autowired
  private ApplicationPolicyRepository applicationPolicyRepository;
  @Autowired
  private ApplicationPolicyRuleRepository applicationPolicyRuleRepository;
  @Autowired
  private WlanRepository wlanRepository;
  @Autowired
  private AccessControlProfileRepository accessControlProfileRepository;
  @MockBean
  private SignaturePackageServiceImpl signaturePackageService;
  @MockBean
  private EntityIdGenerationLockService entityIdGenerationLockService;
  @MockBean
  private AccessControlProfileServiceCtrl accessControlProfileService;
  @MockBean
  private NetworkServiceCtrlImpl networkServiceCtrlImpl;
  @Autowired
  private ApplicationPolicyMerge applicationPolicyMerger;
  @Autowired
  private RepositoryUtil repositoryUtil;

  @RegisterExtension
  TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void testGetApplicationPolicies(ApplicationPolicy applicationPolicy) {
    // When
    List<ApplicationPolicy> savedApplicationPolicies = applicationPolicyService.getApplicationPolicies(
        Optional.empty());

    // Then
    assertEquals(1, savedApplicationPolicies.size());
    assertEquals(applicationPolicy, savedApplicationPolicies.get(0));
  }

  @Test
  void testGetApplicationPolicies_withPage(ApplicationPolicy applicationPolicy) {
    // When
    List<ApplicationPolicy> applicationPolicies = applicationPolicyService.getApplicationPolicies(
        Optional.of(PageRequest.of(0, 10)));

    // Then
    assertEquals(1, applicationPolicies.size());
    assertEquals(applicationPolicy, applicationPolicies.get(0));
  }

  @Test
  void testGetApplicationPolicies_withSigPack(Tenant tenant, ApplicationPolicy applicationPolicy) {
    // Given
    SignaturePackage signaturePackage = new SignaturePackage();
    signaturePackage.setId(randomId());
    repositoryUtil.createOrUpdate(signaturePackage, tenant.getId(), randomTxId());
    when(signaturePackageService.getTenantCurrentSignaturePackage()).thenReturn(
        Optional.of(signaturePackage));

    applicationPolicy.setSignaturePackage(signaturePackage);
    ApplicationPolicy expected = repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(),
        randomTxId());

    // When
    List<ApplicationPolicy> actual = applicationPolicyService.getApplicationPolicies(
        Optional.empty());

    // Then
    assertEquals(1, actual.size());
    assertApplicationPolicy(expected, actual.get(0));
  }

  @Test
  void testGetApplicationPolicies_withSigPack_withPage(Tenant tenant,
      ApplicationPolicy applicationPolicy) {
    // Given
    SignaturePackage signaturePackage = new SignaturePackage();
    signaturePackage.setId(randomId());
    repositoryUtil.createOrUpdate(signaturePackage, tenant.getId(), randomTxId());
    tenant.setSignaturePackage(signaturePackage);
    when(signaturePackageService.getTenantCurrentSignaturePackage()).thenReturn(
        Optional.of(signaturePackage));

    applicationPolicy.setSignaturePackage(signaturePackage);
    ApplicationPolicy expected = repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(),
        randomTxId());

    // When
    List<ApplicationPolicy> actual = applicationPolicyService.getApplicationPolicies(
        Optional.of(PageRequest.of(0, 10)));

    // Then
    assertEquals(1, actual.size());
    assertApplicationPolicy(expected, actual.get(0));
  }

  @Test
  void testGetApplicationPolicy(ApplicationPolicy applicationPolicy) throws Exception {
    // Then
    assertEquals(applicationPolicy,
        applicationPolicyService.getApplicationPolicy(applicationPolicy.getId()));
  }

  @Test
  void testGetApplicationPolicy_withSigPack(Tenant tenant, ApplicationPolicy applicationPolicy)
      throws Exception {
    // Given
    SignaturePackage signaturePackage = new SignaturePackage();
    signaturePackage.setId(randomId());
    repositoryUtil.createOrUpdate(signaturePackage, tenant.getId(), randomTxId());
    when(signaturePackageService.getTenantCurrentSignaturePackage()).thenReturn(
        Optional.of(signaturePackage));

    applicationPolicy.setSignaturePackage(signaturePackage);
    ApplicationPolicy expected = repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(),
        randomTxId());

    // When
    assertApplicationPolicy(expected,
        applicationPolicyService.getApplicationPolicy(applicationPolicy.getId()));
  }

  @Test
  void testGetApplicationPolicy_idNotFound() {
    assertThrows(ObjectNotFoundException.class,
        () -> applicationPolicyService.getApplicationPolicy(randomId()));
  }

  @Test
  void testGetApplicationPolicyByQuery_sortNameDesc(Tenant tenant,
      ApplicationPolicy applicationPolicy) throws Exception {
    // Given
    applicationPolicy.setName("name1");
    repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(), randomTxId());
    ApplicationPolicy applicationPolicy2 = ApplicationPolicyTestFixture.applicationPolicy();
    applicationPolicy2.setName("name2");
    repositoryUtil.createOrUpdate(applicationPolicy2, tenant.getId(), randomTxId());

    // When
    QueryRequest queryRequest = new QueryRequest();
    queryRequest.setSortOrder(DESC);
    queryRequest.setSortField("name");
    ApplicationPolicyQueryResponse queryResponse = applicationPolicyService.getApplicationPolicyByQuery(
        queryRequest);

    // Then
    assertEquals(2, queryResponse.getTotalCount());
    assertEquals(1, queryResponse.getTotalPages());
    assertEquals(1, queryResponse.getPage());
    assertEquals(2, queryResponse.getData().size());
    assertEquals(List.of("name2", "name1"),
        queryResponse.getData().stream().map(ApplicationPolicyQueryData::getName)
            .collect(Collectors.toList()));
  }

  @Test
  void testGetApplicationPolicyByQuery_searchName_withSigPack(Tenant tenant,
      ApplicationPolicy applicationPolicy, Network network, Wlan wlan) throws Exception {
    // Given
    SignaturePackage signaturePackage = new SignaturePackage();
    signaturePackage.setId(randomId());
    repositoryUtil.createOrUpdate(signaturePackage, tenant.getId(), randomTxId());
    when(signaturePackageService.getTenantCurrentSignaturePackage()).thenReturn(
        Optional.of(signaturePackage));

    applicationPolicy.setSignaturePackage(signaturePackage);
    ApplicationPolicy savedApplicationPolicy = repositoryUtil.createOrUpdate(applicationPolicy,
        tenant.getId(), randomTxId());
    ApplicationPolicy applicationPolicy2 = ApplicationPolicyTestFixture.applicationPolicy();
    applicationPolicy2.setSignaturePackage(signaturePackage);
    repositoryUtil.createOrUpdate(applicationPolicy2, tenant.getId(), randomTxId());

    wlan.getAdvancedCustomization().setApplicationPolicy(savedApplicationPolicy);
    repositoryUtil.createOrUpdate(wlan, tenant.getId(), randomTxId());

    // When
    QueryRequest queryRequest = new QueryRequest();
    queryRequest.setSearchString(applicationPolicy.getName());
    ApplicationPolicyQueryResponse queryResponse = applicationPolicyService.getApplicationPolicyByQuery(
        queryRequest);

    // Then
    assertEquals(1, queryResponse.getData().size());
    assertEquals(1, queryResponse.getTotalCount());
    assertEquals(1, queryResponse.getTotalPages());
    assertEquals(1, queryResponse.getPage());
    assertThat(queryResponse.getData().get(0)).satisfies(ap -> {
      assertEquals(savedApplicationPolicy.getId(), ap.getId());
      assertEquals(savedApplicationPolicy.getName(), ap.getName());
      assertEquals(savedApplicationPolicy.getDescription(), ap.getDescription());
      assertEquals(savedApplicationPolicy.getRules().size(), ap.getRulesCount());
      assertEquals(1, ap.getNetworksCount());
      assertEquals(network.getName(), ap.getNetworksNames().get(0));
    });
  }

  @Test
  void testGetApplicationPolicyByQuery_sortNameDesc_withSigPack(Tenant tenant,
      ApplicationPolicy applicationPolicy) throws Exception {
    // Given
    SignaturePackage signaturePackage = new SignaturePackage();
    signaturePackage.setId(randomId());
    repositoryUtil.createOrUpdate(signaturePackage, tenant.getId(), randomTxId());
    when(signaturePackageService.getTenantCurrentSignaturePackage()).thenReturn(
        Optional.of(signaturePackage));

    applicationPolicy.setName("name1");
    applicationPolicy.setSignaturePackage(signaturePackage);
    repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(), randomTxId());
    ApplicationPolicy applicationPolicy2 = ApplicationPolicyTestFixture.applicationPolicy();
    applicationPolicy2.setName("name2");
    applicationPolicy2.setSignaturePackage(signaturePackage);
    repositoryUtil.createOrUpdate(applicationPolicy2, tenant.getId(), randomTxId());

    // When
    QueryRequest queryRequest = new QueryRequest();
    queryRequest.setSortOrder(DESC);
    queryRequest.setSortField("name");
    ApplicationPolicyQueryResponse queryResponse = applicationPolicyService.getApplicationPolicyByQuery(
        queryRequest);

    // Then
    assertEquals(2, queryResponse.getTotalCount());
    assertEquals(1, queryResponse.getTotalPages());
    assertEquals(1, queryResponse.getPage());
    assertEquals(2, queryResponse.getData().size());
    assertEquals(List.of("name2", "name1"),
        queryResponse.getData().stream().map(ApplicationPolicyQueryData::getName)
            .collect(Collectors.toList()));
  }

  @Test
  void testAddApplicationPolicy(Tenant tenant) throws Exception {
    // Given
    ApplicationPolicy applicationPolicy = ApplicationPolicyTestFixture.applicationPolicy();

    // When
    applicationPolicyService.addApplicationPolicy(applicationPolicy);

    // Then
    assertApplicationPolicy(applicationPolicy,
        applicationPolicyService.getApplicationPolicy(applicationPolicy.getId()));
  }

  @Test
  void testAddApplicationPolicy_withSigPack(Tenant tenant) throws Exception {
    // Given
    ApplicationPolicy applicationPolicy = ApplicationPolicyTestFixture.applicationPolicy();
    SignaturePackage signaturePackage = new SignaturePackage();
    signaturePackage.setId(randomId());
    signaturePackage.setVersion("1.0.0");
    repositoryUtil.createOrUpdate(signaturePackage, tenant.getId(), randomTxId());
    when(signaturePackageService.getTenantCurrentSignaturePackage()).thenReturn(
        Optional.of(signaturePackage));

    // When
    applicationPolicyService.addApplicationPolicy(applicationPolicy);

    // Then
    applicationPolicy.setSignaturePackage(signaturePackage);
    assertApplicationPolicy(applicationPolicy,
        applicationPolicyService.getApplicationPolicy(applicationPolicy.getId()));
  }

  @Test
  void testAddApplicationPolicy_userDefinedApplicationRule_sameAppName(Tenant tenant)
      throws Exception {
    // Given
    ApplicationPolicy applicationPolicy = ApplicationPolicyTestFixture.applicationPolicyUserDefinedRule(
        tenant);
    when(entityIdGenerationLockService.generateAvailabilities(any(BatchEntityLockEnum.class), anyInt()))
        .thenReturn(Lists.newArrayList(1, 2));
    applicationPolicy.getRules().get(0).setApplicationId(null);
    applicationPolicy.getRules().get(1).setApplicationId(null);

    // When
    applicationPolicyService.addApplicationPolicy(applicationPolicy);

    // Then
    applicationPolicy.getRules().forEach(rule -> rule.setApplicationId(1));
    assertApplicationPolicy(applicationPolicy,
        applicationPolicyService.getApplicationPolicy(applicationPolicy.getId()));
  }

  @Test
  void testAddApplicationPolicy_userDefinedApplicationRule_differentAppName(Tenant tenant)
      throws Exception {
    // Given
    when(entityIdGenerationLockService.generateAvailabilities(any(BatchEntityLockEnum.class), anyInt()))
        .thenReturn(Lists.newArrayList(1, 2));
    ApplicationPolicy applicationPolicy = ApplicationPolicyTestFixture.applicationPolicyUserDefinedRule(
        tenant);
    applicationPolicy.getRules().get(0).setApplicationName("name1");
    applicationPolicy.getRules().get(0).setApplicationId(null);
    applicationPolicy.getRules().get(1).setApplicationName("name2");
    applicationPolicy.getRules().get(1).setApplicationId(null);

    // When
    applicationPolicyService.addApplicationPolicy(applicationPolicy);

    // Then
    applicationPolicy.getRules().get(0).setApplicationId(1);
    applicationPolicy.getRules().get(1).setApplicationId(2);
    assertApplicationPolicy(applicationPolicy,
        applicationPolicyService.getApplicationPolicy(applicationPolicy.getId()));
  }

  @Test
  void testAddApplicationPolicy_reachMaxCount(Tenant tenant) {
    // Given
    for (int i = 0; i < 10; i++) {
      ApplicationPolicy applicationPolicy = ApplicationPolicyTestFixture.applicationPolicy();
      repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(), randomTxId());
    }

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> applicationPolicyService.addApplicationPolicy(
            ApplicationPolicyTestFixture.applicationPolicy()),
        "Application Policy number exceeds the limit");
  }

  @Test
  void testAddApplicationPolicy_existName(ApplicationPolicy applicationPolicy) {
    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> applicationPolicyService.addApplicationPolicy(applicationPolicy),
        "Application Policy name must be unique");
  }

  @Test
  void testUpdateApplicationPolicy(Tenant tenant, ApplicationPolicy applicationPolicy)
      throws Exception {
    // Given
    AccessControlProfile accessControlProfile = Generators.accessControlProfile().generate();
    accessControlProfile.setApplicationPolicy(applicationPolicy);
    accessControlProfile.setApplicationPolicyEnable(true);
    repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());

    // When
    applicationPolicy = copyOf(applicationPolicy);
    applicationPolicy.getRules().get(0).setRuleType(ApplicationTypeEnum.USER_DEFINED);
    applicationPolicy.getRules().get(0).setProtocol(ApplicationPolicyProtocolEnum.TCP);
    applicationPolicy.getRules().get(0).setDestinationPort(80);
    applicationPolicy.getRules().get(0).setPortMapping(AvcUserDefinedTypeEnum.PORT_ONLY);
    ApplicationPolicyRule newRule = ApplicationPolicyTestFixture.applicationPolicyRule(tenant, applicationPolicy);
    applicationPolicy.getRules().add(newRule);
    applicationPolicyService.updateApplicationPolicy(applicationPolicy.getId(), applicationPolicy);

    // Then
    ApplicationPolicy updatedApplicationPolicy = applicationPolicyService.getApplicationPolicy(
        applicationPolicy.getId());
    assertApplicationPolicy(applicationPolicy, updatedApplicationPolicy);
  }

  @Test
  void testUpdateApplicationPolicy_withSigPack(Tenant tenant, ApplicationPolicy applicationPolicy)
      throws Exception {
    // Given
    AccessControlProfile accessControlProfile = Generators.accessControlProfile().generate();
    accessControlProfile.setApplicationPolicy(applicationPolicy);
    accessControlProfile.setApplicationPolicyEnable(true);
    SignaturePackage signaturePackage = new SignaturePackage();
    signaturePackage.setId(randomId());
    signaturePackage.setVersion("1.0.0");
    repositoryUtil.createOrUpdate(signaturePackage, tenant.getId(), randomTxId());
    tenant.setSignaturePackage(signaturePackage);
    repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());

    // When
    applicationPolicy = copyOf(applicationPolicy);
    applicationPolicy.getRules().get(0).setRuleType(ApplicationTypeEnum.USER_DEFINED);
    applicationPolicy.getRules().get(0).setProtocol(ApplicationPolicyProtocolEnum.TCP);
    applicationPolicy.getRules().get(0).setDestinationPort(80);
    applicationPolicy.getRules().get(0).setPortMapping(AvcUserDefinedTypeEnum.PORT_ONLY);
    applicationPolicyService.updateApplicationPolicy(applicationPolicy.getId(), applicationPolicy);

    // Then
    applicationPolicy.setSignaturePackage(signaturePackage);
    ApplicationPolicy updatedApplicationPolicy = applicationPolicyService.getApplicationPolicy(
        applicationPolicy.getId());
    assertApplicationPolicy(applicationPolicy, updatedApplicationPolicy);
  }

  @Test
  void testUpdateApplicationPolicy_withLessRules(Tenant tenant, ApplicationPolicy applicationPolicy)
      throws Exception {
    // Given
    AccessControlProfile accessControlProfile = Generators.accessControlProfile().generate();
    accessControlProfile.setApplicationPolicy(applicationPolicy);
    accessControlProfile.setApplicationPolicyEnable(true);
    repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());

    // When
    applicationPolicy = copyOf(applicationPolicy);
    applicationPolicy.setName("new name");
    applicationPolicy.getRules().clear();
    applicationPolicy.getRules().add(
        ApplicationPolicyTestFixture.userDefinedApplicationPolicyRule(tenant, applicationPolicy));
    applicationPolicyService.updateApplicationPolicy(applicationPolicy.getId(), applicationPolicy);

    // Then
    ApplicationPolicy updatedApplicationPolicy = applicationPolicyService.getApplicationPolicy(
        applicationPolicy.getId());
    assertApplicationPolicy(applicationPolicy, updatedApplicationPolicy);
  }

  @Test
  void testDeleteApplicationPolicies_ruckusOne(Tenant tenant, ApplicationPolicy applicationPolicy)
      throws Exception {
    // Given
    AccessControlProfile accessControlProfile = Generators.accessControlProfile().generate();
    accessControlProfile.setApplicationPolicy(applicationPolicy);
    accessControlProfile.setApplicationPolicyEnable(true);
    repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());

    // When
    applicationPolicyService.deleteApplicationPolicies(List.of(applicationPolicy.getId()));

    // Then
    assertThrows(ObjectNotFoundException.class,
        () -> applicationPolicyService.getApplicationPolicy(applicationPolicy.getId()));
    AccessControlProfile savedAccessControlProfile = repositoryUtil.find(AccessControlProfile.class,
        accessControlProfile.getId());
    assertNotNull(savedAccessControlProfile);
    assertNull(savedAccessControlProfile.getApplicationPolicy());
    assertFalse(savedAccessControlProfile.getApplicationPolicyEnable());
  }


  @Test
  void testDeleteApplicationPolicies_ruckusOne_networkInUse(Tenant tenant, Network network,
      ApplicationPolicy applicationPolicy) throws Exception {
    // Given
    network.getWlan().getAdvancedCustomization().setApplicationPolicy(applicationPolicy);
    repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

    // Then
    assertThrows(ObjectInUseException.class,
        () -> applicationPolicyService.deleteApplicationPolicies(
            List.of(applicationPolicy.getId(), randomId())));
    assertApplicationPolicy(applicationPolicy,
        applicationPolicyService.getApplicationPolicy(applicationPolicy.getId()));
  }

  @Test
  void testActivateApplicationPolicyOnWifiNetwork(Tenant tenant, Network wifiNetwork,
                                                  ApplicationPolicy applicationPolicy) throws Exception {
    // Given
    applicationPolicy = repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(), randomTxId());
    wifiNetwork = repositoryUtil.createOrUpdate(wifiNetwork, tenant.getId(), randomTxId());

    // When
    applicationPolicyService.activateApplicationPolicyOnWifiNetwork(wifiNetwork.getId(), applicationPolicy.getId());

    // Then
    Network savedWifiNetwork = repositoryUtil.find(Network.class, wifiNetwork.getId());
    assertNotNull(savedWifiNetwork);
    assertNotNull(savedWifiNetwork.getWlan().getAdvancedCustomization().getApplicationPolicy());
    assertTrue(savedWifiNetwork.getWlan().getAdvancedCustomization().getApplicationPolicyEnable());
  }

  @Test
  void testDeactivateApplicationPolicyOnWifiNetwork(Tenant tenant, Network wifiNetwork,
                                                    ApplicationPolicy applicationPolicy) throws Exception {
    // Given
    applicationPolicy = repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(), randomTxId());
    wifiNetwork.getWlan().getAdvancedCustomization().setApplicationPolicy(applicationPolicy);
    wifiNetwork.getWlan().getAdvancedCustomization().setApplicationPolicyEnable(true);
    wifiNetwork = repositoryUtil.createOrUpdate(wifiNetwork, tenant.getId(), randomTxId());

    // When
    applicationPolicyService.deactivateApplicationPolicyOnWifiNetwork(wifiNetwork.getId(), applicationPolicy.getId());

    // Then
    Network savedWifiNetwork = repositoryUtil.find(Network.class, wifiNetwork.getId());
    assertNotNull(savedWifiNetwork);
    assertNull(savedWifiNetwork.getWlan().getAdvancedCustomization().getApplicationPolicy());
    assertFalse(savedWifiNetwork.getWlan().getAdvancedCustomization().getApplicationPolicyEnable());
  }

  @Test
  void testActivateApplicationPolicyOnAccessControlProfile(Tenant tenant, AccessControlProfile accessControlProfile,
                                                           ApplicationPolicy applicationPolicy) throws Exception {
    // Given
    accessControlProfile = repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());
    applicationPolicy = repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(), randomTxId());

    // When
    applicationPolicyService.activateApplicationPolicyOnAccessControlProfile(
            accessControlProfile.getId(), applicationPolicy.getId());

    // Then
    AccessControlProfile savedAccessControlProfile = repositoryUtil.find(AccessControlProfile.class,
            accessControlProfile.getId());
    assertNotNull(savedAccessControlProfile);
    assertNotNull(savedAccessControlProfile.getApplicationPolicy());
    assertTrue(savedAccessControlProfile.getApplicationPolicyEnable());
  }

  @Test
  void testDeactivateApplicationPolicyOnAccessControlProfile(Tenant tenant, AccessControlProfile accessControlProfile,
                                                             ApplicationPolicy applicationPolicy) throws Exception {
    // Given
    accessControlProfile.setApplicationPolicy(applicationPolicy);
    accessControlProfile.setApplicationPolicyEnable(true);
    accessControlProfile = repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());
    applicationPolicy = repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(), randomTxId());

    // When
    applicationPolicyService.deactivateApplicationPolicyOnAccessControlProfile(
            accessControlProfile.getId(), applicationPolicy.getId());

    // Then
    AccessControlProfile savedAccessControlProfile = repositoryUtil.find(AccessControlProfile.class,
            accessControlProfile.getId());
    assertNotNull(savedAccessControlProfile);
    assertNull(savedAccessControlProfile.getApplicationPolicy());
    assertFalse(savedAccessControlProfile.getApplicationPolicyEnable());
  }

  boolean assertApplicationPolicy(ApplicationPolicy expected, ApplicationPolicy actual) {
    assertEquals(expected.getId(), actual.getId());
    if (expected.getSignaturePackage() == null) {
      assertNull(actual.getSignaturePackage());
    } else {
      assertEquals(expected.getSignaturePackage().getId(), actual.getSignaturePackage().getId());
    }
    assertEquals(expected.getRules().size(), actual.getRules().size());
    for (ApplicationPolicyRule expectedRule : expected.getRules()) {
      ApplicationPolicyRule applicationPolicyRule = actual.getRules().stream()
          .filter(rule -> expectedRule.getName().equals(rule.getName())).findFirst().orElse(null);
      assertApplicationPolicyRule(expectedRule, applicationPolicyRule);
    }
    return true;
  }

  private void assertApplicationPolicyRule(ApplicationPolicyRule expected,
      ApplicationPolicyRule actual) {
    if (expected.getId() != null) {
      assertEquals(expected.getId(), actual.getId());
    }
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getRuleType(), actual.getRuleType());
    assertEquals(expected.getAccessControl(), actual.getAccessControl());
    assertEquals(expected.getPriority(), actual.getPriority());
    assertEquals(expected.getCategory(), actual.getCategory());
    assertEquals(expected.getCategoryId(), actual.getCategoryId());
    assertEquals(expected.getApplicationName(), actual.getApplicationName());
    assertEquals(expected.getApplicationId(), actual.getApplicationId());
    assertEquals(expected.getDownlink(), actual.getDownlink());
    assertEquals(expected.getUplink(), actual.getUplink());
    assertEquals(expected.getMarkingPriority(), actual.getMarkingPriority());
    assertEquals(expected.getUpLinkMarkingType(), actual.getUpLinkMarkingType());
    assertEquals(expected.getDownLinkMarkingType(), actual.getDownLinkMarkingType());
    assertEquals(expected.getPortMapping(), actual.getPortMapping());
    assertEquals(expected.getDestinationIp(), actual.getDestinationIp());
    assertEquals(expected.getNetmask(), actual.getNetmask());
    assertEquals(expected.getDestinationPort(), actual.getDestinationPort());
    assertEquals(expected.getProtocol(), actual.getProtocol());
    assertEquals(expected.getApplicationPolicy().getId(), actual.getApplicationPolicy().getId());
  }

  private ApplicationPolicy copyOf(ApplicationPolicy applicationPolicy) {
    ApplicationPolicy copied = new ApplicationPolicy();
    copied.setId(applicationPolicy.getId());
    copied.setSignaturePackage(applicationPolicy.getSignaturePackage());
    List<ApplicationPolicyRule> rules = new ArrayList<>();
    for (ApplicationPolicyRule applicationPolicyRule : applicationPolicy.getRules()) {
      rules.add(copyOf(applicationPolicyRule));
    }
    copied.setRules(rules);

    return copied;
  }

  private ApplicationPolicyRule copyOf(ApplicationPolicyRule applicationPolicyRule) {
    ApplicationPolicyRule copied = new ApplicationPolicyRule();
    copied.setId(applicationPolicyRule.getId());
    copied.setName(applicationPolicyRule.getName());
    copied.setRuleType(applicationPolicyRule.getRuleType());
    copied.setAccessControl(applicationPolicyRule.getAccessControl());
    copied.setPriority(applicationPolicyRule.getPriority());
    copied.setCategory(applicationPolicyRule.getCategory());
    copied.setCategoryId(applicationPolicyRule.getCategoryId());
    copied.setApplicationName(applicationPolicyRule.getApplicationName());
    copied.setApplicationId(applicationPolicyRule.getApplicationId());
    copied.setDownlink(applicationPolicyRule.getDownlink());
    copied.setUplink(applicationPolicyRule.getUplink());
    copied.setMarkingPriority(applicationPolicyRule.getMarkingPriority());
    copied.setUpLinkMarkingType(applicationPolicyRule.getUpLinkMarkingType());
    copied.setDownLinkMarkingType(applicationPolicyRule.getDownLinkMarkingType());
    copied.setPortMapping(applicationPolicyRule.getPortMapping());
    copied.setDestinationIp(applicationPolicyRule.getDestinationIp());
    copied.setNetmask(applicationPolicyRule.getNetmask());
    copied.setDestinationPort(applicationPolicyRule.getDestinationPort());
    copied.setProtocol(applicationPolicyRule.getProtocol());
    copied.setApplicationPolicy(applicationPolicyRule.getApplicationPolicy());

    return copied;
  }


  @TestConfiguration
  static class ApplicationPolicyServiceTestConfig {

    @Bean
    public ApplicationPolicyMerge applicationPolicyMerge() {
      return new ApplicationPolicyMergeImpl();
    }
  }
}
