package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.OpenNetworkGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class V0_1161__SecurityProtocolPostMigrationConsumerTest {

  @Autowired
  protected MessageCaptors messageCaptors;

  @Autowired
  private V0_1161__SecurityProtocolPostMigrationConsumer postMigrationConsumer;

  @Nested
  class PostMigrationNetworkData {

    @Payload
    private OpenNetworkGenerator openNetwork() {
      return Generators.openNetwork();
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK)
    void testPostMigrationOpenNetworkData(TxCtxHolder.TxCtx txCtx,
        CfgAction apiAction, @Payload com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload)
        throws Exception {

      txCtx.setTxId(randomTxId());
      postMigrationConsumer.run(null);

      final var cmnCfgCollectorMessage1 = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant());
      final var cmnCfgCollectorMessage2 = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant());
      final var cmnCfgCollectorMessage3 = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant());

      assertThat(cmnCfgCollectorMessage1.getPayload()).isNotNull();
      assertThat(cmnCfgCollectorMessage2.getPayload()).isNotNull();
      assertThat(cmnCfgCollectorMessage3.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage1.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).count() == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.ADD)
          .matches(p -> p.getDocMap().get("securityProtocol").getStringValue().equals(WlanSecurityEnum.Open.name()))
          .matches(p -> p.getId().equals(payload.getId()));

      assertThat(cmnCfgCollectorMessage2.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).count() == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.DEL);

      assertThat(cmnCfgCollectorMessage3.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).count() == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getDocMap().get("securityProtocol").getStringValue().equals(WlanSecurityEnum.Open.name()))
          .matches(p -> p.getOpType() == OpType.ADD)
          .matches(p -> p.getId().equals(payload.getId()));
    }
  }
}
