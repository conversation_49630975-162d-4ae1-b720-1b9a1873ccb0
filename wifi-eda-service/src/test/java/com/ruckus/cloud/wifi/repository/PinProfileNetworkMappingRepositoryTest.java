package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.repository.NetworkVenueRepository.formattedNetworkIdAtVenueId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.groups.Tuple.tuple;

import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("VxLanTunnelFeatureTest")
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES
      ('venue1', '4c8279f79307415fa9e4c88a1819f0fc'),
      ('venue2', '4c8279f79307415fa9e4c88a1819f0fc'),
      ('venue3', '4c8279f79307415fa9e4c88a1819f0fc'),
      ('venue4', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO pin_profile (id, tenant) VALUES
      ('pin1', '4c8279f79307415fa9e4c88a1819f0fc'),
      ('pin2', '4c8279f79307415fa9e4c88a1819f0fc'),
      ('pin3', '4c8279f79307415fa9e4c88a1819f0fc'),
      ('pin4', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tunnel_profile (id, name, tenant, mtu_type, mtu_size, force_fragmentation) VALUES
      ('tunnel1', 'tunnel profile 1', '4c8279f79307415fa9e4c88a1819f0fc', 'MANUAL', 1450, false),
      ('tunnel2', 'tunnel profile 2', '4c8279f79307415fa9e4c88a1819f0fc', 'MANUAL', 1450, false),
      ('tunnel3', 'tunnel profile 3', '4c8279f79307415fa9e4c88a1819f0fc', 'MANUAL', 1450, false);
    INSERT INTO pin_profile_regular_setting (id, tenant, tunnel_profile, pin_profile, venue) VALUES
      ('rs1 -> pin1 + venue1', '4c8279f79307415fa9e4c88a1819f0fc', 'tunnel1', 'pin1', 'venue1'),
      ('rs2 -> pin2 + venue2', '4c8279f79307415fa9e4c88a1819f0fc', 'tunnel3', 'pin2', 'venue2'),
      ('rs3 -> pin3 + venue3', '4c8279f79307415fa9e4c88a1819f0fc', 'tunnel3', 'pin3', 'venue3'),
      ('rs4 -> pin4 + venue4', '4c8279f79307415fa9e4c88a1819f0fc', 'tunnel1', 'pin4', 'venue4');
    INSERT INTO network (id, name, type, tenant) VALUES
      ('network1', 'network1', 'DPSK', '4c8279f79307415fa9e4c88a1819f0fc'),
      ('network2', 'network2', 'DPSK', '4c8279f79307415fa9e4c88a1819f0fc'),
      ('network3', 'network3', 'PSK', '4c8279f79307415fa9e4c88a1819f0fc'),
      ('network4', 'network4', 'AAA', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO pin_profile_network_mapping(id, tenant, network, pin_profile_regular_setting) VALUES
      ('nm1-rs1 -> network1 + venue1', '4c8279f79307415fa9e4c88a1819f0fc', 'network1', 'rs1 -> pin1 + venue1'),
      ('nm1-rs2 -> network1 + venue2', '4c8279f79307415fa9e4c88a1819f0fc', 'network1', 'rs2 -> pin2 + venue2'),
      ('nm1-rs3 -> network1 + venue3', '4c8279f79307415fa9e4c88a1819f0fc', 'network1', 'rs3 -> pin3 + venue3'),
      ('nm1-rs4 -> network1 + venue4', '4c8279f79307415fa9e4c88a1819f0fc', 'network1', 'rs4 -> pin4 + venue4'),
      ('nm2-rs1 -> network2 + venue1', '4c8279f79307415fa9e4c88a1819f0fc', 'network2', 'rs1 -> pin1 + venue1'),
      ('nm2-rs4 -> network2 + venue4', '4c8279f79307415fa9e4c88a1819f0fc', 'network2', 'rs4 -> pin4 + venue4'),
      ('nm3-rs2 -> network3 + venue2', '4c8279f79307415fa9e4c88a1819f0fc', 'network3', 'rs2 -> pin2 + venue2'),
      ('nm4-rs2 -> network4 + venue2', '4c8279f79307415fa9e4c88a1819f0fc', 'network4', 'rs2 -> pin2 + venue2'),
      ('nm4-rs4 -> network4 + venue4', '4c8279f79307415fa9e4c88a1819f0fc', 'network4', 'rs4 -> pin4 + venue4');
    """)
class PinProfileNetworkMappingRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String VENUE_ID_1 = "venue1";
  private static final String VENUE_ID_2 = "venue2";
  private static final String VENUE_ID_3 = "venue3";
  private static final String VENUE_ID_4 = "venue4";
  private static final String NETWORK_ID_1 = "network1";
  private static final String NETWORK_ID_2 = "network2";
  private static final String NETWORK_ID_3 = "network3";
  private static final String NETWORK_ID_4 = "network4";
  private static final String REGULAR_SETTING_ID_1 = "rs1 -> pin1 + venue1";
  private static final String REGULAR_SETTING_ID_2 = "rs2 -> pin2 + venue2";
  private static final String REGULAR_SETTING_ID_3 = "rs3 -> pin3 + venue3";
  private static final String REGULAR_SETTING_ID_4 = "rs4 -> pin4 + venue4";
  private static final String TUNNEL_PROFILE_ID_1 = "tunnel1";
  private static final String TUNNEL_PROFILE_ID_2 = "tunnel2";
  private static final String TUNNEL_PROFILE_ID_3 = "tunnel3";

  @Autowired
  private PinProfileNetworkMappingRepository repository;

  @Test
  void findByTenantIdAndPinProfileRegularSettingIdInTest() {
    assertThat(repository.findByTenantIdAndPinProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_ID_1)))
        .isNotEmpty().hasSize(2)
        .extracting(networkMapping -> networkMapping.getPinProfileRegularSetting().getId())
        .containsOnly(REGULAR_SETTING_ID_1);
    assertThat(repository.findByTenantIdAndPinProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_ID_2)))
        .isNotEmpty().hasSize(3)
        .extracting(networkMapping -> networkMapping.getPinProfileRegularSetting().getId())
        .containsOnly(REGULAR_SETTING_ID_2);
    assertThat(repository.findByTenantIdAndPinProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_ID_3)))
        .isNotEmpty().hasSize(1)
        .extracting(networkMapping -> networkMapping.getPinProfileRegularSetting().getId())
        .containsOnly(REGULAR_SETTING_ID_3);
    assertThat(repository.findByTenantIdAndPinProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_ID_4)))
        .isNotEmpty().hasSize(3)
        .extracting(networkMapping -> networkMapping.getPinProfileRegularSetting().getId())
        .containsOnly(REGULAR_SETTING_ID_4);
    assertThat(repository.findByTenantIdAndPinProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_ID_1, REGULAR_SETTING_ID_3)))
        .isNotEmpty().hasSize(3)
        .extracting(networkMapping -> networkMapping.getPinProfileRegularSetting().getId())
        .containsOnly(REGULAR_SETTING_ID_1, REGULAR_SETTING_ID_3);
    assertThat(repository.findByTenantIdAndPinProfileRegularSettingIdIn(TENANT_ID,
        List.of(REGULAR_SETTING_ID_2, REGULAR_SETTING_ID_4)))
        .isNotEmpty().hasSize(6)
        .extracting(networkMapping -> networkMapping.getPinProfileRegularSetting().getId())
        .containsOnly(REGULAR_SETTING_ID_2, REGULAR_SETTING_ID_4);
  }

  @Test
  void findByTenantIdAndNetworkIdAndPinProfileRegularSettingVenueIdTest() {
    assertThat(repository.findByTenantIdAndNetworkIdAndPinProfileRegularSettingVenueId(TENANT_ID,
        NETWORK_ID_1, VENUE_ID_1)).isNotNull().satisfies(networkMapping -> {
          assertThat(networkMapping.getNetwork().getId()).isEqualTo(NETWORK_ID_1);
          assertThat(networkMapping.getPinProfileRegularSetting().getVenue().getId())
              .isEqualTo(VENUE_ID_1);
        });
    assertThat(repository.findByTenantIdAndNetworkIdAndPinProfileRegularSettingVenueId(TENANT_ID,
        NETWORK_ID_2, VENUE_ID_1)).isNotNull().satisfies(networkMapping -> {
          assertThat(networkMapping.getNetwork().getId()).isEqualTo(NETWORK_ID_2);
          assertThat(networkMapping.getPinProfileRegularSetting().getVenue().getId())
              .isEqualTo(VENUE_ID_1);
        });
    assertThat(repository.findByTenantIdAndNetworkIdAndPinProfileRegularSettingVenueId(TENANT_ID,
        NETWORK_ID_3, VENUE_ID_1)).isNull();
    assertThat(repository.findByTenantIdAndNetworkIdAndPinProfileRegularSettingVenueId(TENANT_ID,
        NETWORK_ID_4, VENUE_ID_1)).isNull();
  }

  @Test
  void existsByTenantIdAndPinProfileRegularSettingTunnelProfileId() {
    assertThat(repository.existsByTenantIdAndPinProfileRegularSettingTunnelProfileId(TENANT_ID,
        TUNNEL_PROFILE_ID_1)).isTrue();
    assertThat(repository.existsByTenantIdAndPinProfileRegularSettingTunnelProfileId(TENANT_ID,
        TUNNEL_PROFILE_ID_2)).isFalse();
    assertThat(repository.existsByTenantIdAndPinProfileRegularSettingTunnelProfileId(TENANT_ID,
        TUNNEL_PROFILE_ID_3)).isTrue();
  }

  @Test
  void findByTenantIdAndPinProfileIdAndNetworkIdAtVenueIdInTest() {
    final List<String> networkIdAtVenueIdList = List.of(
        formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_1), // pin1
        formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_2), // pin2
        formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_4), // pin4
        formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_1), // pin1
        formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_3), // NOT EXIST
        formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_4), // pin4
        formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_2), // pin2
        formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_4), // NOT EXIST
        formattedNetworkIdAtVenueId(NETWORK_ID_4, VENUE_ID_2)  // pin2
    );
    assertThat(repository.findByTenantIdAndPinProfileIdAndNetworkIdAtVenueIdIn(TENANT_ID, "pin1",
        networkIdAtVenueIdList))
        .hasSize(2)
        .extracting(BaseEntity::getId, nm -> nm.getNetwork().getId(),
            nm -> nm.getPinProfileRegularSetting().getVenue().getId())
        .containsExactlyInAnyOrder(
            tuple("nm1-rs1 -> network1 + venue1", NETWORK_ID_1, VENUE_ID_1),
            tuple("nm2-rs1 -> network2 + venue1", NETWORK_ID_2, VENUE_ID_1));
    assertThat(repository.findByTenantIdAndPinProfileIdAndNetworkIdAtVenueIdIn(TENANT_ID, "pin2",
        networkIdAtVenueIdList))
        .hasSize(3)
        .extracting(BaseEntity::getId, nm -> nm.getNetwork().getId(),
            nm -> nm.getPinProfileRegularSetting().getVenue().getId())
        .containsExactlyInAnyOrder(
            tuple("nm1-rs2 -> network1 + venue2", NETWORK_ID_1, VENUE_ID_2),
            tuple("nm3-rs2 -> network3 + venue2", NETWORK_ID_3, VENUE_ID_2),
            tuple("nm4-rs2 -> network4 + venue2", NETWORK_ID_4, VENUE_ID_2));
    assertThat(repository.findByTenantIdAndPinProfileIdAndNetworkIdAtVenueIdIn(TENANT_ID, "pin3",
        networkIdAtVenueIdList)).isEmpty();
    assertThat(repository.findByTenantIdAndPinProfileIdAndNetworkIdAtVenueIdIn(TENANT_ID, "pin4",
        networkIdAtVenueIdList))
        .hasSize(2)
        .extracting(BaseEntity::getId, nm -> nm.getNetwork().getId(),
            nm -> nm.getPinProfileRegularSetting().getVenue().getId())
        .containsExactlyInAnyOrder(
            tuple("nm1-rs4 -> network1 + venue4", NETWORK_ID_1, VENUE_ID_4),
            tuple("nm2-rs4 -> network2 + venue4", NETWORK_ID_2, VENUE_ID_4));
  }
}
