package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.REMOTE_TRIGGER_METHOD;
import static com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.REMOTE_TRIGGER_PARAMETERS;
import static com.ruckus.cloud.wifi.servicemodel.enums.ScheduleTimeSlotStatus.IN_QUEUE;
import static com.ruckus.cloud.wifi.servicemodel.enums.ScheduleTimeSlotStatus.WAITING_REMINDER;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static java.lang.Math.toIntExact;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApFirmwareBatchOperation;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.RPC;
import com.ruckus.cloud.wifi.repository.VenueApFirmwareBatchOperationRepository;
import com.ruckus.cloud.wifi.repository.ScheduleTimeSlotRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.utils.DateUtils;
import com.ruckus.cloud.wifi.servicemodel.enums.ScheduleTimeSlotStatus;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.viewmodel.BatchOperationType;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@WifiIntegrationTest
@FeatureFlag(enable = {FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
public class ConsumeWifiTriggerTest {

  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private MessageCaptors messageCaptors;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired private VenueRepository venueRepository;

  @Autowired private ScheduleTimeSlotRepository scheduleTimeSlotRepository;

  @Autowired private VenueApFirmwareBatchOperationRepository venueApFirmwareBatchOperationRepository;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Value("${topic.nuvo.notificationRequests}")
  private String nuvoNotificationRequests;

  @Value("${default.fw-upgrade.schedule-finish-after-minutes}")
  private Duration finishAfterMinutes;

  @Nested
  class whenConsumeWifiTriggerTest {

    @Test
    void thenDisableApSyslog(Ap ap) throws InvalidProtocolBufferException {
      final var requestId = randomTxId();

      Map<String, Object> kafkaPayload = prepareApSyslogKafkaPayload(ap.getId());
      messageUtil.sendWifiTrigger(ap.getTenant().getId(), requestId, kafkaPayload);
      var record =
          messageCaptors.getDdccmMessageCaptor().getValue(ap.getTenant().getId(), requestId);
      assertThat(record).isNotNull();
      WifiConfigRequest ddccmRequest = record.getPayload();
      assertThat(ddccmRequest.getOperationsList())
          .isNotNull()
          .matches(
              request ->
                  request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
          .matches(
              request ->
                  request.stream()
                      .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApTunnelCertInfo));
    }

    private Map<String, Object> prepareApSyslogKafkaPayload(String serialNumber) {
      Map<String, Object> data = new HashMap<>();
      data.put(REMOTE_TRIGGER_METHOD, RPC.DISABLE_AP_SYSLOG);
      data.put(REMOTE_TRIGGER_PARAMETERS, serialNumber);
      return data;
    }
  }

  @Nested
  class NotifyScheduleReminderHandlerWithMultipleSchedulesTest {

    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion622,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.50")
            ApVersion apVersion62250,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion620,
        Venue venue) {
      tenant.setLatestReleaseVersion(apVersion622);
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.getRequestId());

      ScheduleTimeSlot sts = createNewScheduleTimeSlot(tenant.getId());

      UpgradeSchedule us = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue);
      createNewUpgradeScheduleFirmwareVersion(tenant, us, apVersion620);

      UpgradeSchedule usRunning = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue);
      setRunningStatusToSchedule(tenant, usRunning);

      venue.setApPassword("1qaz@WSX");
      venue.setCountryCode("US");
      venue.setWifiFirmwareVersion(apVersion62250);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRequestId());

      ApGroup apGroup = createApGroup(tenant, venue, true);
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R560");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R500");

      Venue venue2 = createNewVenue(tenant, apVersion62250);
      createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue2);
      ApGroup apGroup2 = createApGroup(tenant, venue2, true);
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R550");
    }

    @AfterEach
    void teardown(Tenant tenant, @Autowired VenueRepository venueRepository,
        @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      clearScheduleTimeslotTable(tenant, venueRepository, upgradeScheduleRepository);
    }

    @Test
    void thenSendReminderWithImpactModelEmail(Venue venue, @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      final var requestId = RPC.NOTIFY_SCHEDULE_REMINDER.name();

      messageUtil.sendWifiTrigger(
          RPC.NOTIFY_SCHEDULE_REMINDER.name(),
          requestId,
          prepareRoutineKafkaPayload(RPC.NOTIFY_SCHEDULE_REMINDER, requestId));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId()))
          .isNotNull().filteredOn(s -> s.getStatus().equals(UpgradeScheduleStatus.PENDING))
          .hasSize(1).singleElement()
          .matches(s -> s.getTimeSlot().getStatus().equals(IN_QUEUE));
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE)
    void thenSendReminderWithImpactModelEmailAsync(Venue venue, @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      final var requestId = RPC.NOTIFY_SCHEDULE_REMINDER.name();

      messageUtil.sendWifiTrigger(
          RPC.NOTIFY_SCHEDULE_REMINDER.name(),
          requestId,
          prepareRoutineKafkaPayload(RPC.NOTIFY_SCHEDULE_REMINDER, requestId));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId()))
          .isNotNull().filteredOn(s -> s.getStatus().equals(UpgradeScheduleStatus.PENDING))
          .hasSize(1).singleElement()
          .matches(s -> s.getTimeSlot().getStatus().equals(IN_QUEUE));
    }
  }

  @Nested
  class NotifyScheduleReminderByModelEmailTest {

    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
        ApVersion apVersion622,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.50")
        ApVersion apVersion62250,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
        ApVersion apVersion620,
        Venue venue) {
      tenant.setLatestReleaseVersion(apVersion622);
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.getRequestId());

      ScheduleTimeSlot sts = createNewScheduleTimeSlot(tenant.getId());

      UpgradeSchedule us = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue, List.of("R560", "R770"));
      createNewUpgradeScheduleFirmwareVersion(tenant, us, apVersion620);

      UpgradeSchedule usRunning = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue, List.of("R550"));
      setRunningStatusToSchedule(tenant, usRunning);

      venue.setApPassword("1qaz@WSX");
      venue.setCountryCode("US");
      venue.setWifiFirmwareVersion(apVersion62250);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRequestId());

      ApGroup apGroup = createApGroup(tenant, venue, true);
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R560");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R560");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R770");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R500");

      Venue venue2 = createNewVenue(tenant, apVersion62250);
      UpgradeSchedule us2 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue2, List.of("R550"));
      createNewUpgradeScheduleFirmwareVersion(tenant, us2, apVersion620, List.of("R560"));
      ApGroup apGroup2 = createApGroup(tenant, venue2, true);
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R550");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R560");

      Venue venue3 = createNewVenue(tenant, apVersion62250);
      UpgradeSchedule us3 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue3, List.of("R550"));
      ApGroup apGroup3 = createApGroup(tenant, venue3, true);
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup3, randomSerialNumber(), "R550");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup3, randomSerialNumber(), "R560");
    }

    @AfterEach
    void teardown(Tenant tenant, @Autowired VenueRepository venueRepository,
        @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      clearScheduleTimeslotTable(tenant, venueRepository, upgradeScheduleRepository);
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM, FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
    void thenSendReminderByModelEmail(Venue venue, @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      final var requestId = RPC.NOTIFY_SCHEDULE_REMINDER.name();

      messageUtil.sendWifiTrigger(
          RPC.NOTIFY_SCHEDULE_REMINDER.name(),
          requestId,
          prepareRoutineKafkaPayload(RPC.NOTIFY_SCHEDULE_REMINDER, requestId));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId()))
          .isNotNull().filteredOn(s -> s.getStatus().equals(UpgradeScheduleStatus.PENDING))
          .hasSize(1).singleElement()
          .matches(s -> s.getTimeSlot().getStatus().equals(IN_QUEUE));
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM})
    void thenSendReminderEmail(Venue venue, @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      final var requestId = RPC.NOTIFY_SCHEDULE_REMINDER.name();

      messageUtil.sendWifiTrigger(
          RPC.NOTIFY_SCHEDULE_REMINDER.name(),
          requestId,
          prepareRoutineKafkaPayload(RPC.NOTIFY_SCHEDULE_REMINDER, requestId));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId()))
          .isNotNull().filteredOn(s -> s.getStatus().equals(UpgradeScheduleStatus.PENDING))
          .hasSize(1).singleElement()
          .matches(s -> s.getTimeSlot().getStatus().equals(IN_QUEUE));
    }
  }

  @Nested
  class NotifyScheduleUpgradeFinishedNotificationServiceWithMultipleSchedulesTest {

    private static String scheduleTimeSlotId;

    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion622,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.50")
            ApVersion apVersion62250,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion620,
        Venue venue) {
      tenant.setLatestReleaseVersion(apVersion622);
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.getRequestId());

      ScheduleTimeSlot sts = createUpdateFinishedScheduleTimeSlot(tenant.getId());
      scheduleTimeSlotId = sts.getId();
      UpgradeSchedule runningUS1 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue);
      setRunningStatusToSchedule(tenant, runningUS1);

      ScheduleTimeSlot unfinishedTimeSlot = createUnfinishedScheduleTimeSlot(tenant.getId());
      UpgradeSchedule pendingSchedule = createNewUpgradeScheduleAndFirmwareVersion(tenant, unfinishedTimeSlot, apVersion620, venue);

      venue.setApPassword("1qaz@WSX");
      venue.setCountryCode("US");
      venue.setWifiFirmwareVersion(apVersion62250);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRequestId());

      ApGroup apGroup = createApGroup(tenant, venue, true);
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R560");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R500");

      Venue venue2 = createNewVenue(tenant, apVersion62250);
      UpgradeSchedule runningUS2 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue2);
      setRunningStatusToSchedule(tenant, runningUS2);
      ApGroup apGroup2 = createApGroup(tenant, venue2, true);
      Ap ap = createAp(tenant, apGroup2, randomSerialNumber(), "R500");
      createApFirmwareUpgradeRequest(tenant, ap.getId(), "R500", false, runningUS2.getId());

      Venue venue3 = createNewVenue(tenant, apVersion62250);
      UpgradeSchedule runningUS3 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion622, venue3);
      runningUS3 = setRunningStatusToSchedule(tenant, runningUS3);

      ApGroup apGroup3 = createApGroup(tenant, venue3, true);
      Ap ap2 = createAp(tenant, apGroup3, randomSerialNumber(), "R500");
      createApFirmwareUpgradeRequest(tenant, ap2.getId(), "R500", true, null);
    }

    @Test
    void thenSendUpgradeFinishedEmail(Venue venue, @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      final var requestId = RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.name();

      messageUtil.sendWifiTrigger(
          RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.name(),
          requestId,
          prepareRoutineKafkaPayload(RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH, requestId));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull()
          .hasSize(1);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE)
    void thenSendUpgradeFinishedEmailAsync(Venue venue, @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      final var requestId = RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.name();

      messageUtil.sendWifiTrigger(
          RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.name(),
          requestId,
          prepareRoutineKafkaPayload(RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH, requestId));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull()
          .hasSize(1);
    }
  }

  @Nested
  class NotifyUpgradeFinishedNotificationTest {

    private static String scheduleTimeSlotId;

    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.200")
        ApVersion apVersion200,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.250")
        ApVersion apVersion250,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.250")
        ApVersion apVersion6250,
        Venue venue) {
      tenant.setLatestReleaseVersion(apVersion250);
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.getRequestId());

      ScheduleTimeSlot sts = createUpdateFinishedScheduleTimeSlot(tenant.getId());
      scheduleTimeSlotId = sts.getId();
      UpgradeSchedule runningUS1 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion250, venue, List.of("R650", "R750"));
      setRunningStatusToSchedule(tenant, runningUS1);

      venue.setApPassword("1qaz@WSX");
      venue.setCountryCode("US");
      venue.setWifiFirmwareVersion(apVersion250);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRequestId());

      ApGroup apGroup = createApGroup(tenant, venue, true);
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R750");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R770");

      Venue venue2 = createNewVenue(tenant, apVersion250);
      UpgradeSchedule runningUS2 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion250, venue2, List.of("R750"));
      setRunningStatusToSchedule(tenant, runningUS2);
      ApGroup apGroup2 = createApGroup(tenant, venue2, true);
      Ap ap = createAp(tenant, apGroup2, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R750");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R770");

      Venue venue3 = createNewVenue(tenant, apVersion250);
      UpgradeSchedule runningUS3 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion250, venue3, List.of("R750"));
      setRunningStatusToSchedule(tenant, runningUS3);
      createNewUpgradeScheduleFirmwareVersion(tenant, runningUS3, apVersion6250, List.of("R500"));
      ApGroup apGroup3 = createApGroup(tenant, venue3, true);
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup3, randomSerialNumber(), "R750");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup3, randomSerialNumber(), "R500");

      // all failed
      Venue venue4 = createNewVenue(tenant, apVersion250);
      UpgradeSchedule runningUS4 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion250, venue4, List.of("R500"));
      setRunningStatusToSchedule(tenant, runningUS4);
      ApGroup apGroup4 = createApGroup(tenant, venue4, true);
      String serial = randomSerialNumber();
      String model = "R500";
      createAp(tenant, apGroup4, serial, model);
      createApFirmwareUpgradeRequest(tenant, venue4.getId(), serial, model, false, runningUS4.getId());

      String serial2 = randomSerialNumber();
      createAp(tenant, apGroup4, serial2, model);
      createApFirmwareUpgradeRequest(tenant, venue4.getId(), serial2, model, false, runningUS4.getId());
      
      // partial success
      Venue venue5 = createNewVenue(tenant, apVersion250);
      UpgradeSchedule runningUS5 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion250, venue5, List.of("R500"));
      setRunningStatusToSchedule(tenant, runningUS5);
      ApGroup apGroup5 = createApGroup(tenant, venue5, true);
      String serial3 = randomSerialNumber();
      createAp(tenant, apGroup5, serial3, model);
      createApFirmwareUpgradeRequest(tenant, venue5.getId(), serial3, model, false, runningUS5.getId());

      String serial4 = randomSerialNumber();
      createAp(tenant, apGroup5, serial4, model);
      createApFirmwareUpgradeRequest(tenant, serial4, model, true, null);
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER,
        FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
    void thenSendUpgradeFinishedEmailByModel(Tenant tenant, Venue venue, @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      final var requestId = RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.name();

      messageUtil.sendWifiTrigger(
          RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.name(),
          requestId,
          prepareRoutineKafkaPayload(RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH, requestId));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull()
          .hasSize(0);
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM})
    void thenSendUpgradeFinishedEmail(Tenant tenant, Venue venue, @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
      final var requestId = RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.name();

      messageUtil.sendWifiTrigger(
          RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.name(),
          requestId,
          prepareRoutineKafkaPayload(RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH, requestId));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull()
          .hasSize(0);
    }
  }

  @Nested
  class NotifyUpdateNowByApModelTest {
    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.200")
        ApVersion apVersion200,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.250")
        ApVersion apVersion250) {
      generateVenuesAndAps(tenant, apVersion200, apVersion250);
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM,
        FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
    void thenSendUpdateNowFinishedEmail(Tenant tenant, @Autowired VenueApFirmwareBatchOperationRepository venueApFirmwareBatchOperationRepository) {
      final var requestId = RPC.EDA_UPGRADE_RETRY.name();

      messageUtil.sendWifiTrigger(
          tenant.getId(),
          requestId,
          prepareUpgradeRetryKafkaPayload(RPC.EDA_UPGRADE_RETRY, tenant.getId(), "batchId"));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(venueApFirmwareBatchOperationRepository.findAllByTenantIdAndBatchId(tenant.getId(),
          "batchId")).hasSize(0);
    }
  }

  @Nested
  class NotifyUpdateNowFailedByApModelTest {
    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.200")
        ApVersion apVersion200,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.250")
        ApVersion apVersion250) {

      //case 1: partial success
      Venue venue1 = createNewVenue(tenant, apVersion250);
      ApGroup apGroup = createApGroup(tenant, venue1, true);
      String serial1 = randomSerialNumber();
      createAp(tenant, apGroup, serial1, "R750");
      createApFirmwareUpgradeRequest(tenant, venue1.getId(), serial1, "R750", false, "batchId");
      String serial2 = randomSerialNumber();
      createAp(tenant, apGroup, serial2, "R650");
      createApFirmwareUpgradeRequest(tenant, venue1.getId(), serial2, "R650", false, "batchId");
      String serial3 = randomSerialNumber();
      createAp(tenant, apGroup, serial3, "R770");
      createApFirmwareUpgradeRequest(tenant, venue1.getId(), serial3, "R770", true, null);

      createApFirmwareBatchOperation("batchId", tenant, venue1, List.of(apVersion250), List.of("R650", "R750", "R770"));

      //case 2: all failed
      Venue venue2 = createNewVenue(tenant, apVersion250);
      ApGroup apGroup2 = createApGroup(tenant, venue2, true);
      String serial4 = randomSerialNumber();
      createAp(tenant, apGroup2, serial4, "R770");
      createApFirmwareUpgradeRequest(tenant, venue2.getId(), serial4, "R770", false, "batchId");
      String serial5 = randomSerialNumber();
      createAp(tenant, apGroup2, serial5, "R770");
      createApFirmwareUpgradeRequest(tenant, venue2.getId(), serial5, "R770", false, "batchId");

      createApFirmwareBatchOperation("batchId", tenant, venue2, List.of(apVersion250, apVersion200), List.of("R770"));
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER,
        FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
    void thenSendUpdateNowFinishedEmail(Tenant tenant, @Autowired VenueApFirmwareBatchOperationRepository venueApFirmwareBatchOperationRepository) {
      final var requestId = RPC.EDA_UPGRADE_RETRY.name();

      messageUtil.sendWifiTrigger(
          tenant.getId(),
          requestId,
          prepareUpgradeRetryKafkaPayload(RPC.EDA_UPGRADE_RETRY, tenant.getId(), "batchId"));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(venueApFirmwareBatchOperationRepository.findAllByTenantIdAndBatchId(tenant.getId(),
          "batchId")).hasSize(0);
    }
  }

  @Nested
  class NotifySkipScheduleByApModelTest {
    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.200")
        ApVersion apVersion200,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.250")
        ApVersion apVersion250) {
      generateVenuesAndAps(tenant, apVersion200, apVersion250);
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM})
    void thenSendSkipScheduleEmailWithByModelFFDisabled(Tenant tenant,
        @Autowired VenueApFirmwareBatchOperationRepository venueApFirmwareBatchOperationRepository) {
      final var requestId = RPC.POST_BATCH_SCHEDULE_OPERATION.name();

      messageUtil.sendWifiTrigger(
          tenant.getId(),
          requestId,
          prepareBatchScheduleOperationKafkaPayload(RPC.POST_BATCH_SCHEDULE_OPERATION,
              tenant.getId(), "batchId", BatchOperationType.SKIP_SCHEDULE));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(venueApFirmwareBatchOperationRepository.findAllByTenantIdAndBatchId(tenant.getId(),
          "batchId")).hasSize(0);
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM,
        FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
    void thenSendSkipScheduleEmailWithByModelFFEnabled(Tenant tenant,
        @Autowired VenueApFirmwareBatchOperationRepository venueApFirmwareBatchOperationRepository) {
      final var requestId = RPC.POST_BATCH_SCHEDULE_OPERATION.name();

      messageUtil.sendWifiTrigger(
          tenant.getId(),
          requestId,
          prepareBatchScheduleOperationKafkaPayload(RPC.POST_BATCH_SCHEDULE_OPERATION,
              tenant.getId(), "batchId", BatchOperationType.SKIP_SCHEDULE));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(venueApFirmwareBatchOperationRepository.findAllByTenantIdAndBatchId(tenant.getId(),
          "batchId")).hasSize(0);
    }
  }

  @Nested
  class NotifyChangeScheduleByApModelTest {
    private static String scheduleTimeSlotId;
    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.200")
        ApVersion apVersion200,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.250")
        ApVersion apVersion250,
        Venue venue) {
      ScheduleTimeSlot sts = createUpdateFinishedScheduleTimeSlot(tenant.getId());
      scheduleTimeSlotId = sts.getId();

      venue.setApPassword("1qaz@WSX");
      venue.setCountryCode("US");
      venue.setWifiFirmwareVersion(apVersion250);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRequestId());

      UpgradeSchedule us1 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion250, venue, List.of("R650", "R750"));

      ApGroup apGroup = createApGroup(tenant, venue, true);
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R750");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R770");

      createApFirmwareBatchOperation("batchId", tenant, venue, List.of(apVersion250), null);

      Venue venue2 = createNewVenue(tenant, apVersion250);
      UpgradeSchedule us2 = createNewUpgradeScheduleAndFirmwareVersion(tenant, sts, apVersion250, venue2, List.of("R750"));
      ApGroup apGroup2 = createApGroup(tenant, venue2, true);
      Ap ap = createAp(tenant, apGroup2, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R650");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R750");
      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R770");

      createApFirmwareBatchOperation("batchId", tenant, venue2, List.of(apVersion250, apVersion200), null);
    }

    @Test
    @FeatureFlag(enable = {FlagNames.WIFI_EDA_EMAIL_TASK_SPLITTER_WITH_EVENT_STREAM_TOGGLE,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM,
        FlagNames.WIFI_EDA_EMAIL_TEMPLATE_NEW_REMINDER,
        FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
    void thenSendChangeScheduleEmail(Tenant tenant,
        @Autowired VenueApFirmwareBatchOperationRepository venueApFirmwareBatchOperationRepository) {
      final var requestId = RPC.POST_BATCH_SCHEDULE_OPERATION.name();

      messageUtil.sendWifiTrigger(
          tenant.getId(),
          requestId,
          prepareBatchScheduleOperationKafkaPayload(RPC.POST_BATCH_SCHEDULE_OPERATION,
              tenant.getId(), "batchId", BatchOperationType.CHANGE_SCHEDULE));

      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(venueApFirmwareBatchOperationRepository.findAllByTenantIdAndBatchId(tenant.getId(),
          "batchId")).hasSize(0);
    }
  }

  private UpgradeSchedule setRunningStatusToSchedule(Tenant tenant, UpgradeSchedule upgradeSchedule) {
    upgradeSchedule.setStatus(UpgradeScheduleStatus.RUNNING);
    return repositoryUtil.createOrUpdate(upgradeSchedule, tenant.getId(), randomTxId());
  }

  private Venue createNewVenue(Tenant tenant, ApVersion apVersion) {
    Venue v =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venue()
            .setApPassword(always("1qaz@WSX"))
            .setAddressLine(always("Somewhere"))
            .setCountryCode(always("US"))
            .setTimezone(always("America/New_York"))
            .setTenant(always(tenant))
            .setWifiFirmwareVersion(always(apVersion))
            .setName(always(randomName()))
            .generate();
    v = repositoryUtil.createOrUpdate(v, txCtxExtension.getTenantId(),
        txCtxExtension.newRequestId());
    return v;
  }

  private ApGroup createApGroup(Tenant tenant, Venue venue, boolean isDefault) {
    ApGroup apg =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apGroup().generate();
    apg.setVenue(venue);
    apg.setTenant(tenant);
    apg.setIsDefault(isDefault);
    return repositoryUtil.createOrUpdate(
        apg, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
  }

  private ScheduleTimeSlot createUpdateFinishedScheduleTimeSlot(String tenantId) {
    Date dateRoundToWholeHalfHour = DateUtils.roundToWholeHalfHour(Date.from(Instant.now()));
    dateRoundToWholeHalfHour =
        DateUtils.addMinutes(dateRoundToWholeHalfHour, toIntExact(-finishAfterMinutes.toMinutes()));
    return createScheduleTimeSlot(tenantId, dateRoundToWholeHalfHour, IN_QUEUE);
  }

  private ScheduleTimeSlot createUnfinishedScheduleTimeSlot(String tenantId) {
    return createScheduleTimeSlot(tenantId,
        DateUtils.roundToWholeHalfHour(Date.from(Instant.now())), IN_QUEUE);
  }

  private ScheduleTimeSlot createNewScheduleTimeSlot(String tenantId) {
    Date dateRoundToWholeHalfHour = DateUtils.roundToWholeHalfHour(Date.from(Instant.now()));
    dateRoundToWholeHalfHour =
        DateUtils.addHours(dateRoundToWholeHalfHour, toIntExact(Duration.ofHours(48).toHours()));

    return createScheduleTimeSlot(tenantId, dateRoundToWholeHalfHour, WAITING_REMINDER);
  }

  private ScheduleTimeSlot createScheduleTimeSlot(String tenantId, Date startDateTime,
      ScheduleTimeSlotStatus status) {
    List<ScheduleTimeSlot> scheduleTimeSlots = scheduleTimeSlotRepository.findByStartDateTime(startDateTime);
    if(scheduleTimeSlots.isEmpty()) {
      ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
      sts.setStatus(status);
      sts.setStartDateTime(startDateTime);
      sts.setEndDateTime(
          DateUtils.addHours(startDateTime, toIntExact(Duration.ofHours(2).toHours())));
      return repositoryUtil.createOrUpdate(sts, tenantId, txCtxExtension.newRequestId());
    } else {
      return scheduleTimeSlots.get(0);
    }
  }

  private UpgradeScheduleFirmwareVersion createNewUpgradeScheduleFirmwareVersion(
      Tenant tenant, UpgradeSchedule upgradeSchedule, ApVersion version) {
    UpgradeScheduleFirmwareVersion usfv = new UpgradeScheduleFirmwareVersion(randomId());
    usfv.setApFirmwareVersion(version);
    usfv.setUpgradeSchedule(upgradeSchedule);
    usfv.setTenant(tenant);
    return repositoryUtil.createOrUpdate(usfv, tenant.getId(), txCtxExtension.newRequestId());
  }

  private UpgradeScheduleFirmwareVersion createNewUpgradeScheduleFirmwareVersion(
      Tenant tenant, UpgradeSchedule upgradeSchedule, ApVersion version, List<String> targetApModels) {
    UpgradeScheduleFirmwareVersion usfv = new UpgradeScheduleFirmwareVersion(randomId());
    usfv.setApFirmwareVersion(version);
    usfv.setUpgradeSchedule(upgradeSchedule);
    usfv.setTenant(tenant);
    usfv.setTargetApModels(targetApModels);
    return repositoryUtil.createOrUpdate(usfv, tenant.getId(), txCtxExtension.newRequestId());
  }

  private UpgradeSchedule createNewUpgradeScheduleAndFirmwareVersion(
      Tenant tenant, ScheduleTimeSlot scheduleTimeSlot, ApVersion version) {
    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(scheduleTimeSlot);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(version);
    us = repositoryUtil.createOrUpdate(us, tenant.getId(), txCtxExtension.newRequestId());

    createNewUpgradeScheduleFirmwareVersion(tenant, us, version);
    return us;
  }

  private UpgradeSchedule createNewUpgradeScheduleAndFirmwareVersion(
      Tenant tenant, ScheduleTimeSlot scheduleTimeSlot, ApVersion version, Venue venue) {
    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(scheduleTimeSlot);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(version);
    us.setVenue(venue);
    us = repositoryUtil.createOrUpdate(us, tenant.getId(), txCtxExtension.newRequestId());

    createNewUpgradeScheduleFirmwareVersion(tenant, us, version);
    return us;
  }

  private UpgradeSchedule createNewUpgradeScheduleAndFirmwareVersion(
      Tenant tenant, ScheduleTimeSlot scheduleTimeSlot, ApVersion version, Venue venue, List<String> targetApModels) {
    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(scheduleTimeSlot);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(version);
    us.setVenue(venue);
    us = repositoryUtil.createOrUpdate(us, tenant.getId(), txCtxExtension.newRequestId());

    createNewUpgradeScheduleFirmwareVersion(tenant, us, version, targetApModels);
    return us;
  }

  private void createNewApAndApFirmwareUpgradeRequest(
      Tenant tenant, ApGroup apGroup, String serial, String model) {
    createAp(tenant, apGroup, serial, model);
    createApFirmwareUpgradeRequest(tenant, serial, model, true, null);
  }

  private Ap createAp(Tenant tenant, ApGroup apGroup, String serial, String model) {
    Ap ap = new Ap(serial);
    ap.setApGroup(apGroup);
    ap.setTenant(tenant);
    ap.setModel(model);
    return repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(),
        txCtxExtension.newRequestId());
  }

  private ApFirmwareUpgradeRequest createApFirmwareUpgradeRequest(Tenant tenant, String serial,
      String model, boolean isUpgradeSuccess, String usId) {
    ApFirmwareUpgradeRequest apFirmwareUpgradeRequest = generateApFirmwareUpgradeRequest(
        tenant, serial, model, isUpgradeSuccess, usId);
    return repositoryUtil.createOrUpdate(
        apFirmwareUpgradeRequest, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
  }

  private ApFirmwareUpgradeRequest createApFirmwareUpgradeRequest(Tenant tenant, String venueId, String serial,
      String model, boolean isUpgradeSuccess, String usId) {
    ApFirmwareUpgradeRequest apFirmwareUpgradeRequest = generateApFirmwareUpgradeRequest(
        tenant, serial, model, isUpgradeSuccess, usId);
    apFirmwareUpgradeRequest.setVenueId(venueId);
    return repositoryUtil.createOrUpdate(
        apFirmwareUpgradeRequest, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
  }

  private static ApFirmwareUpgradeRequest generateApFirmwareUpgradeRequest(Tenant tenant, String serial,
      String model, boolean isUpgradeSuccess, String usId) {
    ApFirmwareUpgradeRequest apFirmwareUpgradeRequest = new ApFirmwareUpgradeRequest(randomId());
    apFirmwareUpgradeRequest.setTenant(tenant);
    apFirmwareUpgradeRequest.setModel(model);
    apFirmwareUpgradeRequest.setSerialNumber(serial);
    apFirmwareUpgradeRequest.setRequestId(isUpgradeSuccess ? null : usId);
    apFirmwareUpgradeRequest.setDeadline(isUpgradeSuccess ? null : 10000000L);
    return apFirmwareUpgradeRequest;
  }

  private void createApFirmwareBatchOperation(String batchId, Tenant tenant, Venue venue, List<ApVersion> apVersions, List<String> targetApModels) {
    VenueApFirmwareBatchOperation operation = new VenueApFirmwareBatchOperation();
    operation.setVenue(venue);
    operation.setTenant(tenant);
    operation.setBatchId(batchId);
    operation.setTargetApModels(targetApModels);
    operation.setTargetApVersionList(apVersions);
    repositoryUtil.createOrUpdate(operation, tenant.getId(), randomTxId());
  }

  private void generateVenuesAndAps(Tenant tenant, ApVersion apVersion1, ApVersion apVersion2) {
    Venue venue1 = createNewVenue(tenant, apVersion2);

    ApGroup apGroup = createApGroup(tenant, venue1, true);
    createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R650");
    createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R650");
    createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R750");
    createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R770");

    createApFirmwareBatchOperation("batchId", tenant, venue1, List.of(apVersion2), List.of("R650", "R750", "R770"));

    Venue venue2 = createNewVenue(tenant, apVersion2);
    ApGroup apGroup2 = createApGroup(tenant, venue2, true);
    Ap ap = createAp(tenant, apGroup2, randomSerialNumber(), "R650");
    createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R650");
    createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R650");
    createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R750");
    createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R770");

    createApFirmwareBatchOperation("batchId", tenant, venue2, List.of(apVersion2, apVersion1), List.of("R770"));

    Venue venue3 = createNewVenue(tenant, apVersion2);
    ApGroup apGroup3 = createApGroup(tenant, venue3, true);
    createNewApAndApFirmwareUpgradeRequest(tenant, apGroup3, randomSerialNumber(), "R770");

    createApFirmwareBatchOperation("batchId", tenant, venue3, List.of(apVersion2, apVersion1), List.of("R750"));
  }

  private void clearScheduleTimeslotTable(Tenant tenant, String scheduleTimeslotId) {
    ScheduleTimeSlot scheduleTimeSlot =
        repositoryUtil.find(ScheduleTimeSlot.class, scheduleTimeslotId);
    repositoryUtil.remove(scheduleTimeSlot, tenant.getId(), true);
  }

  private void clearScheduleTimeslotTable(
      Tenant tenant,
      @Autowired VenueRepository venueRepository,
      @Autowired UpgradeScheduleRepository upgradeScheduleRepository) {
    List<Venue> venueList = venueRepository.findScheduledVenues(tenant.getId()).stream().toList();
    List<String> usIds =
        venueList.stream().map(v->
          upgradeScheduleRepository.findAllByVenueId(v.getId())
        ).flatMap(List::stream).map(UpgradeSchedule::getId).toList();
    venueList.stream()
        .forEach(
            v -> {
              repositoryUtil.createOrUpdate(v, tenant.getId(), txCtxExtension.newRequestId());
            });
    List<UpgradeSchedule> upgradeScheduleList = upgradeScheduleRepository.findAllById(usIds);
    List<ScheduleTimeSlot> scheduleTimeSlotList =
        upgradeScheduleList.stream().map(UpgradeSchedule::getTimeSlot).distinct().toList();
    upgradeScheduleList.stream()
        .forEach(
            us -> {
              repositoryUtil.remove(us, tenant.getId(), true);
            });
    scheduleTimeSlotList.stream()
        .forEach(
            sts -> {
              repositoryUtil.remove(sts, tenant.getId(), true);
            });
  }

  private Map<String, Object> prepareRoutineKafkaPayload(RPC rpc, String requestId) {
    Map<String, Object> data = new HashMap<>();
    data.put(REMOTE_TRIGGER_METHOD, rpc.name());
    data.put(REMOTE_TRIGGER_PARAMETERS, new Object[] {requestId, rpc.toString()});
    return data;
  }

  private Map<String, Object> prepareUpgradeRetryKafkaPayload(RPC rpc, String tenantId, String batchId) {
    Map<String, Object> data = new HashMap<>();
    data.put(REMOTE_TRIGGER_METHOD, rpc.name());
    data.put(REMOTE_TRIGGER_PARAMETERS, new Object[] {batchId, tenantId, "", List.of(), true, false, ""});
    return data;
  }

  private Map<String, Object> prepareBatchScheduleOperationKafkaPayload(RPC rpc,
      String tenantId, String batchId, BatchOperationType operationType) {
    Map<String, Object> data = new HashMap<>();
    data.put(REMOTE_TRIGGER_METHOD, rpc.name());
    data.put(REMOTE_TRIGGER_PARAMETERS, new Object[]{batchId, tenantId, operationType.name()});
    return data;
  }
}
