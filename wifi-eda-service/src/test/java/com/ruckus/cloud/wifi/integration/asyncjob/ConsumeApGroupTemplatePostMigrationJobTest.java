package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apGroup;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.proto.ApGroupTemplatePostMigrationJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeApGroupTemplatePostMigrationJobTest {

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class givenVenueTemplatePersistedInDb {

    private ApGroup templateApGroup;
    private ApGroup nonTemplateApGroup;

    @BeforeEach
    void givenVenueTemplatePersistedInDb(final Tenant tenant, @Template final Venue venue) {
      this.templateApGroup = repositoryUtil.createOrUpdate(apGroup()
          .setName(randomString())
          .setTenant(always(tenant))
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .setIsTemplate(alwaysTrue())
          .generate(), tenant.getId(), randomTxId());
      this.nonTemplateApGroup = repositoryUtil.createOrUpdate(apGroup()
          .setName(randomString())
          .setTenant(always(tenant))
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .setIsTemplate(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
    }

    @Test
    void whenConsumeApGroupPostMigrationJob(final Tenant tenant) {
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
          .setApGroupTemplatePostMigrationJob(ApGroupTemplatePostMigrationJob.newBuilder())
          .build());

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenant.getId());
            assertThat(msg.getRequestId()).startsWith(requestId);
          })
          // Only the non-template apGroup should be updated
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(1)
          .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
          .isNotEmpty().singleElement()
          .satisfies(op -> {
            assertThat(op.getId()).isEqualTo(nonTemplateApGroup.getId());
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocMap())
                .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenant.getId()))
                .containsEntry(Key.ID, ValueUtils.stringValue(nonTemplateApGroup.getId()))
                .containsEntry(Key.IS_TEMPLATE, ValueUtils.boolValue(true));
          });
    }
  }
}
