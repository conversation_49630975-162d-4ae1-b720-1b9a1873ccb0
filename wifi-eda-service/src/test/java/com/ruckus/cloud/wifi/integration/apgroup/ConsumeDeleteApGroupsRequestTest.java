package com.ruckus.cloud.wifi.integration.apgroup;

import static com.ruckus.cloud.wifi.service.impl.ExtendedApGroupServiceCtrlImpl.DEFAULT_AP_GROUP_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.kafka.common.header.Header;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@Tag("ApGroupTest")
@WifiIntegrationTest
public class ConsumeDeleteApGroupsRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class whenConsumeDeleteApGroupsRequest {

    @Test
    void thenDeleteApGroups_withoutAps(Venue venue, @DefaultApGroup ApGroup defaultApGroup)
        throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = defaultApGroup.getTenant().getId();

      final var apGroups = Stream.generate(ApGroup::new)
          .peek(a -> a.setId(randomId()))
          .peek(a -> a.setVenue(venue))
          .peek(a -> a.setName(randomName()))
          .limit(2)
          .collect(Collectors.toList());

      apGroups.forEach(apGroup -> {
        repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
      });

      final var payload = apGroups.stream()
          .map(ApGroup::getId).collect(Collectors.toList());

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.DELETE_AP_GROUPS, userName,
          payload);

      validateResult(tenantId, requestId, venue.getId(), defaultApGroup.getId(),
          payload, Collections.emptyList());
    }

    @Test
    void thenDeleteApGroups_withAp(Venue venue, @DefaultApGroup ApGroup defaultApGroup)
        throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = defaultApGroup.getTenant().getId();
      final var apIds = new ArrayList<String>();

      final var apGroups = Stream.generate(ApGroup::new)
          .peek(a -> a.setId(randomId()))
          .peek(a -> a.setVenue(venue))
          .peek(a -> a.setName(randomName()))
          .limit(2)
          .collect(Collectors.toList());

      apGroups.forEach(apGroup -> {
        Ap ap = new Ap(randomSerialNumber());
        ap.setApGroup(apGroup);
        ap.setName(randomName());
        ap.setMac(randomMacAddress());
        repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
        apIds.add(ap.getId());
      });

      final var payload = apGroups.stream().map(ApGroup::getId).collect(Collectors.toList());

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.DELETE_AP_GROUPS, userName,
          payload);

      validateResult(tenantId, requestId, venue.getId(), defaultApGroup.getId(), payload, apIds);
    }
  }

  void validateResult(String tenantId, String requestId, String venueId, String defaultApGroupId,
      List<String> apGroupIds, List<String> apIds) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    // validate AP Group cmnCfgCollectorMessage
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(apGroupIds.size())
        .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
        .isNotEmpty().hasSize(apGroupIds.size() + (apIds.isEmpty() ? 0 : 1))
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> apGroupIds.contains(op.getId()))
              .isNotEmpty().hasSize(apGroupIds.size())
              .extracting(Operations::getOpType).allMatch(OpType.DEL::equals);
          if (!apIds.isEmpty()) {
            assertThat(ops)
                .filteredOn(op -> defaultApGroupId.equals(op.getId()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                      .containsEntry(Key.ID, ValueUtils.stringValue(defaultApGroupId))
                      .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_AP_GROUP_NAME.trim()))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                      .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(true));
                });
          }
        });

    // validate AP cmnCfgCollectorMessage, should send empty deviceGroupName when we delete AP Group in EDA
    if (apIds.size() > 0) {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(requestId))
          .matches(p ->
              p.getOperationsList().stream().filter(o -> EsConstants.Index.DEVICE.equals(o.getIndex())).count()
                  == apIds.size())
          .extracting(p -> p.getOperationsList().stream().filter(o -> EsConstants.Index.DEVICE.equals(o.getIndex()))
              .findFirst().get())
          .matches(p -> p.getOpType() == OpType.MOD)
          .matches(p -> p.getDocMap().get("deviceGroupName").getStringValue().equals(""));
    }

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull();
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(Status.OK))
        .matches(p -> p.getStep().equals(ApiFlowNames.DELETE_AP_GROUPS))
        .matches(p -> p.getEventDate() != null);

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(requestId);

    assertThat(activityImpactedMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);

    assertThat(activityImpactedMessage.getPayload())
        .matches(p -> p.getDeviceIdsList().size() == apIds.size())
        .matches(p -> p.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID));
  }
}
