package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Capabilities;
import com.ruckus.cloud.wifi.eda.servicemodel.CapabilitiesApModel;
import com.ruckus.cloud.wifi.eda.servicemodel.CapabilitiesLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.redis.YamlPropertySourceFactory;
import com.ruckus.cloud.wifi.repository.ApFirmwareUpgradeRequestRepository;
import com.ruckus.cloud.wifi.repository.IpsecProfileRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.ApUpgradeService;
import com.ruckus.cloud.wifi.service.FirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.FirmwareCountryCodeService;
import com.ruckus.cloud.wifi.service.VenueCurrentFirmwareService;
import com.ruckus.cloud.wifi.service.VenueRadioService;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.exception.CapabilityNotFoundException;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;

@WifiUnitTest
@PropertySource(value = "classpath:application-unit.yml", factory = YamlPropertySourceFactory.class)
class DdccmVenueApModelOperationBuilderTest {

  @Autowired
  private DdccmVenueApModelOperationBuilder ddccmVenueApModelOperationBuilder;

  @MockBean
  private DdccmVenueDhcpServiceSettingOperationBuilder ddccmVenueDhcpServiceSettingOperationBuilder;

  @MockBean
  private DdccmVenueBonjourFencingOperationBuilder ddccmVenueBonjourFencingOperationBuilder;

  @MockBean
  private FirmwareCapabilityService firmwareCapabilityService;

  @MockBean
  private FirmwareCountryCodeService firmwareCountryCodeService;

  @MockBean
  private ApUpgradeService apUpgradeService;

  @MockBean
  private VenueCurrentFirmwareService venueCurrentFirmwareService;

  @MockBean
  private VenueRadioService venueRadioService;

  @MockBean
  private IpsecProfileRepository ipsecProfileRepository;

  @Autowired
  private VenueRepository venueRepository;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  private final static ApVersion AP_VERSION = new ApVersion("*********.317");

  @BeforeEach
  void beforeEach() throws CapabilityNotFoundException {
    var capabilities = new Capabilities();
    capabilities.setApModels(List.of(dummyR610Capabilities()));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());
  }

  private static CapabilitiesApModel dummyR610Capabilities() {
    return Generators.capabilitiesApModel("R610")
        .setLedOn(always(true))
        .setLanPorts(list(Generators.capabilitiesLanPort(), 2)).generate();
  }

  @Test
  void testCreateVenueApModelSpecificAttributes(final Venue venue) {
    // Given
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);
    venueApModelSpecificAttributes.setModel("R610");
    venue.setModelSpecificAttributes(List.of(venueApModelSpecificAttributes));
    venue.setWifiFirmwareVersion(AP_VERSION);

    // When
    List<Operation> operations = ddccmVenueApModelOperationBuilder.build(
        new NewTxEntity<>(venueApModelSpecificAttributes), emptyTxChanges());

    // Then
    assertThat(operations)
        .isNotNull()
        .hasSize(1).singleElement()
        .satisfies(op -> {
          assertThat(op.getId()).isEqualTo(venue.getId());
          assertThat(op.getAction()).isEqualTo(Action.MODIFY);
          assertThat(op.hasVenue()).isTrue();
        })
        .extracting(Operation::getVenue)
        .isNotNull()
        .satisfies(venueProto -> {
          assertThat(venueProto.getVenueApModelsCount()).isEqualTo(1);
          assertThat(venueProto.getVenueApModels(0).getModelName()).isEqualTo("R610");
        });
  }

  @Test
  void testCreateVenueApModelSpecificAttributes_whenVenueIsNewlyCreated(final Venue venue) {
    // Given
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);
    venueApModelSpecificAttributes.setModel("R610");
    venue.setModelSpecificAttributes(List.of(venueApModelSpecificAttributes));
    venue.setWifiFirmwareVersion(AP_VERSION);

    final Venue otherVenue = new Venue(txCtxExtension.newRandomId());

    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new NewTxEntity<>(otherVenue))).when(txChanges).getNewEntities();
    // When
    List<Operation> operations = ddccmVenueApModelOperationBuilder.build(
        new NewTxEntity<>(venueApModelSpecificAttributes), txChanges);
    // Then
    assertThat(operations).isNotEmpty();

    // When
    doReturn(List.of(new NewTxEntity<>(venue), new NewTxEntity<>(otherVenue))).when(txChanges).getNewEntities();
    operations = ddccmVenueApModelOperationBuilder.build(
        new NewTxEntity<>(venueApModelSpecificAttributes), txChanges);
    // Then
    assertThat(operations).isEmpty();
  }

  @Test
  void testUpdateVenueApModelSpecificAttributes(final Venue venue) {
    // Given
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);
    venueApModelSpecificAttributes.setModel("R610");
    venue.setModelSpecificAttributes(List.of(venueApModelSpecificAttributes));
    venue.setWifiFirmwareVersion(AP_VERSION);

    Set<String> modifiedProperties = Set.of("updatedDate");
    TxChangesReader txChanges = spy(TxChangesReader.class);
    doReturn(modifiedProperties).when(txChanges).getModifiedProperties(eq(venueApModelSpecificAttributes));
    doReturn(List.of(new ModifiedTxEntity<>(venueApModelSpecificAttributes, Collections.emptySet())))
        .when(txChanges)
        .getModifiedEntities();

    // When
    List<Operation> operations = ddccmVenueApModelOperationBuilder.build(
        new ModifiedTxEntity<>(venueApModelSpecificAttributes, Collections.emptySet()), txChanges);

    // Then
    assertThat(operations)
        .isNotNull()
        .hasSize(1)
        .first()
        .matches(operation -> Action.MODIFY.equals(operation.getAction()))
        .extracting(Operation::getVenue)
        .isNotNull();
  }

  @Test
  void testDeleteVenueApModelSpecificAttributes(final Venue venue) {
    // Given
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);
    venueApModelSpecificAttributes.setModel("R610");
    venue.setModelSpecificAttributes(List.of(venueApModelSpecificAttributes));
    venue.setWifiFirmwareVersion(AP_VERSION);

    // When
    List<Operation> operations = ddccmVenueApModelOperationBuilder.build(
        new DeletedTxEntity<>(venueApModelSpecificAttributes), emptyTxChanges());

    // Then
    assertThat(operations)
        .isNotNull()
        .hasSize(1)
        .first()
        .matches(operation -> Action.MODIFY.equals(operation.getAction()))
        .extracting(Operation::getVenue)
        .isNotNull();
  }

  @Test
  void testDeleteVenueApModelSpecificAttributes_whenVenueIsDeleted(final Venue venue) {
    // Given
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);
    venueApModelSpecificAttributes.setModel("R610");
    venue.setModelSpecificAttributes(List.of(venueApModelSpecificAttributes));
    venue.setWifiFirmwareVersion(AP_VERSION);

    final Venue otherVenue = new Venue(randomId());

    TxChangesReader txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new DeletedTxEntity<>(otherVenue))).when(txChanges).getDeletedEntities();
    // When
    List<Operation> operations = ddccmVenueApModelOperationBuilder.build(
        new DeletedTxEntity<>(venueApModelSpecificAttributes), txChanges);
    // Then
    assertThat(operations).isNotEmpty();

    // When
    doReturn(List.of(new DeletedTxEntity<>(venue), new DeletedTxEntity<>(otherVenue)))
        .when(txChanges)
        .getDeletedEntities();

    operations = ddccmVenueApModelOperationBuilder.build(
        new DeletedTxEntity<>(venueApModelSpecificAttributes), txChanges);
    // Then
    assertThat(operations).isEmpty();
  }

  @Test
  void getEthernetProfileNameTest() {
    var port = new VenueLanPort();
    port.setApLanPortProfile(new EthernetPortProfile(txCtxExtension.newRandomId()));

    var name = ddccmVenueApModelOperationBuilder.getEthernetProfileName(port, false);
    assertEquals(port.getApLanPortProfile().getId(), name);

    name = ddccmVenueApModelOperationBuilder.getEthernetProfileName(port, true);
    assertEquals(port.getApLanPortProfile().getId(), name);

    port.setLanPortAdoption(new LanPortAdoption(txCtxExtension.newRandomId()));
    name = ddccmVenueApModelOperationBuilder.getEthernetProfileName(port, true);
    assertEquals(port.getLanPortAdoption().getId(), name);
  }

  @Test
  void getEthernetProfileIdTest() {
    var port = new VenueLanPort();
    port.setApLanPortProfile(new EthernetPortProfile(txCtxExtension.newRandomId()));
    port.getApLanPortProfile().setApLanPortId(1);

    var id = ddccmVenueApModelOperationBuilder.getEthernetProfileId(port, false);
    assertEquals(port.getApLanPortProfile().getApLanPortId(), id);

    id = ddccmVenueApModelOperationBuilder.getEthernetProfileId(port, true);
    assertEquals(port.getApLanPortProfile().getApLanPortId(), id);

    port.setLanPortAdoption(new LanPortAdoption(txCtxExtension.newRandomId()));
    port.getLanPortAdoption().setEthernetPortProfileId(2);
    id = ddccmVenueApModelOperationBuilder.getEthernetProfileId(port, true);
    assertEquals(port.getLanPortAdoption().getEthernetPortProfileId(), id);
  }

  @Test
  void testMergeLanPorts_whenSrcIsEmpty() {
    final var venue = new Venue(randomId());
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);
    venueApModelSpecificAttributes.setLanPorts(Collections.emptyList());

    CapabilitiesApModel capabilities = new CapabilitiesApModel();
    capabilities.setLanPorts(List.of(
        createCapabilitiesLanPort("1", ApLanPortTypeEnum.TRUNK.name()),
        createCapabilitiesLanPort("2", ApLanPortTypeEnum.ACCESS.name())
    ));

    List<com.ruckus.acx.ddccm.protobuf.wifi.VenueApLanPort> result =
        ddccmVenueApModelOperationBuilder.mergeLanPorts(venueApModelSpecificAttributes, capabilities, false);

    assertThat(result).hasSize(2);
    assertThat(result.get(0).getPortNameValue()).isEqualTo(1);
    assertThat(result.get(0).getEthProfileName()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_TRUNK_PORT_WAN_NAME);
    assertThat(result.get(0).getApLanPortProfileId()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_TRUNK_PORT_WAN_ID);

    assertThat(result.get(1).getPortNameValue()).isEqualTo(2);
    assertThat(result.get(1).getEthProfileName()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_ACCESS_PORT_NAME);
    assertThat(result.get(1).getApLanPortProfileId()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_ACCESS_PORT_ID);
  }

  @Test
  void testMergeLanPorts_withMatchingAndNonMatchingPorts() {
    final var venue = new Venue(randomId());
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);

    VenueLanPort venueLanPort = createVenueLanPort("1", false);
    venueApModelSpecificAttributes.setLanPorts(List.of(venueLanPort));

    CapabilitiesApModel capabilities = new CapabilitiesApModel();
    capabilities.setLanPorts(List.of(
        createCapabilitiesLanPort("1", ApLanPortTypeEnum.TRUNK.name()),
        createCapabilitiesLanPort("2", ApLanPortTypeEnum.ACCESS.name())
    ));

    List<com.ruckus.acx.ddccm.protobuf.wifi.VenueApLanPort> result =
        ddccmVenueApModelOperationBuilder.mergeLanPorts(venueApModelSpecificAttributes, capabilities, false);

    assertThat(result).hasSize(2);
    // Matching port should have values from src
    assertThat(result.get(0).getPortNameValue()).isEqualTo(1);
    assertThat(result.get(0).getEthProfileName()).isEqualTo(venueLanPort.getApLanPortProfile().getId());
    assertThat(result.get(0).getApLanPortProfileId()).isEqualTo(venueLanPort.getApLanPortProfile().getApLanPortId());
    assertThat(result.get(0).getEnabled().getValue()).isFalse();

    // Non-matching port should have default values from capabilities
    assertThat(result.get(1).getPortNameValue()).isEqualTo(2);
    assertThat(result.get(1).getEthProfileName()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_ACCESS_PORT_NAME);
    assertThat(result.get(1).getApLanPortProfileId()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_ACCESS_PORT_ID);
  }

  @Test
  void testMergeLanPorts_withMigrationFalse() {
    final var venue = new Venue(randomId());
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);

    VenueLanPort venueLanPort = createVenueLanPort("1", true);
    venueLanPort.setLanPortAdoption(new LanPortAdoption("adoptedId"));
    venueLanPort.getLanPortAdoption().setEthernetPortProfileId(99);
    venueApModelSpecificAttributes.setLanPorts(List.of(venueLanPort));

    CapabilitiesApModel capabilities = new CapabilitiesApModel();
    capabilities.setLanPorts(List.of(createCapabilitiesLanPort("1", ApLanPortTypeEnum.TRUNK.name())));

    List<com.ruckus.acx.ddccm.protobuf.wifi.VenueApLanPort> result =
        ddccmVenueApModelOperationBuilder.mergeLanPorts(venueApModelSpecificAttributes, capabilities, false);

    assertThat(result).hasSize(1);
    // Should use ApLanPortProfile because migration is false
    assertThat(result.get(0).getEthProfileName()).isEqualTo(venueLanPort.getApLanPortProfile().getId());
    assertThat(result.get(0).getApLanPortProfileId()).isEqualTo(venueLanPort.getApLanPortProfile().getApLanPortId());
  }

  @Test
  void testMergeLanPorts_withMigrationTrue_withAdoption() {
    final var venue = new Venue(randomId());
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);

    VenueLanPort venueLanPort = createVenueLanPort("1", true);
    venueLanPort.setLanPortAdoption(new LanPortAdoption("adoptedId"));
    venueLanPort.getLanPortAdoption().setEthernetPortProfileId(99);
    venueApModelSpecificAttributes.setLanPorts(List.of(venueLanPort));

    CapabilitiesApModel capabilities = new CapabilitiesApModel();
    capabilities.setLanPorts(List.of(createCapabilitiesLanPort("1", ApLanPortTypeEnum.TRUNK.name())));

    List<com.ruckus.acx.ddccm.protobuf.wifi.VenueApLanPort> result =
        ddccmVenueApModelOperationBuilder.mergeLanPorts(venueApModelSpecificAttributes, capabilities, true);

    assertThat(result).hasSize(1);
    // Should use LanPortAdoption because migration is true
    assertThat(result.get(0).getEthProfileName()).isEqualTo("adoptedId");
    assertThat(result.get(0).getApLanPortProfileId()).isEqualTo(99);
  }

  @Test
  void testMergeLanPorts_withMigrationTrue_withoutAdoption() {
    final var venue = new Venue(randomId());
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);

    VenueLanPort venueLanPort = createVenueLanPort("1", true);
    venueApModelSpecificAttributes.setLanPorts(List.of(venueLanPort));

    CapabilitiesApModel capabilities = new CapabilitiesApModel();
    capabilities.setLanPorts(List.of(createCapabilitiesLanPort("1", ApLanPortTypeEnum.TRUNK.name())));

    List<com.ruckus.acx.ddccm.protobuf.wifi.VenueApLanPort> result =
        ddccmVenueApModelOperationBuilder.mergeLanPorts(venueApModelSpecificAttributes, capabilities, true);

    assertThat(result).hasSize(1);
    // Should use ApLanPortProfile because LanPortAdoption is null
    assertThat(result.get(0).getEthProfileName()).isEqualTo(venueLanPort.getApLanPortProfile().getId());
    assertThat(result.get(0).getApLanPortProfileId()).isEqualTo(venueLanPort.getApLanPortProfile().getApLanPortId());
  }

  @Test
  void testMergeLanPorts_withNullPortIds() {
    final var venue = new Venue(randomId());
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);

    // Create VenueLanPort with null portId
    VenueLanPort venueLanPortWithNullPortId = createVenueLanPort("1", true);
    venueLanPortWithNullPortId.setPortId(null);

    // Create another VenueLanPort with null portId (this would cause duplicate key exception)
    VenueLanPort venueLanPortWithNullPortId2 = createVenueLanPort("2", false);
    venueLanPortWithNullPortId2.setPortId(null);

    // Create a valid VenueLanPort
    VenueLanPort validVenueLanPort = createVenueLanPort("3", true);

    venueApModelSpecificAttributes.setLanPorts(List.of(
        venueLanPortWithNullPortId,
        venueLanPortWithNullPortId2,
        validVenueLanPort
    ));

    CapabilitiesApModel capabilities = new CapabilitiesApModel();
    capabilities.setLanPorts(List.of(
        createCapabilitiesLanPort("1", ApLanPortTypeEnum.TRUNK.name()),
        createCapabilitiesLanPort("2", ApLanPortTypeEnum.ACCESS.name()),
        createCapabilitiesLanPort("3", ApLanPortTypeEnum.TRUNK.name())
    ));

    // This should not throw an exception and should filter out null portIds
    List<com.ruckus.acx.ddccm.protobuf.wifi.VenueApLanPort> result =
        ddccmVenueApModelOperationBuilder.mergeLanPorts(venueApModelSpecificAttributes, capabilities, false);

    assertThat(result).hasSize(3);

    // Port 1 should use default values since it was filtered out due to null portId
    assertThat(result.get(0).getPortNameValue()).isEqualTo(1);
    assertThat(result.get(0).getEthProfileName()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_TRUNK_PORT_WAN_NAME);
    assertThat(result.get(0).getApLanPortProfileId()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_TRUNK_PORT_WAN_ID);

    // Port 2 should use default values since it was filtered out due to null portId
    assertThat(result.get(1).getPortNameValue()).isEqualTo(2);
    assertThat(result.get(1).getEthProfileName()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_ACCESS_PORT_NAME);
    assertThat(result.get(1).getApLanPortProfileId()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_ACCESS_PORT_ID);

    // Port 3 should use values from the valid VenueLanPort
    assertThat(result.get(2).getPortNameValue()).isEqualTo(3);
    assertThat(result.get(2).getEthProfileName()).isEqualTo(validVenueLanPort.getApLanPortProfile().getId());
    assertThat(result.get(2).getApLanPortProfileId()).isEqualTo(validVenueLanPort.getApLanPortProfile().getApLanPortId());
    assertThat(result.get(2).getEnabled().getValue()).isTrue();
  }

  @Test
  void testMergeLanPorts_withAllNullPortIds() {
    final var venue = new Venue(randomId());
    final var venueApModelSpecificAttributes = randomVenueApModelSpecificAttributes(venue);

    // Create VenueLanPorts with null portIds
    VenueLanPort venueLanPort1 = createVenueLanPort("1", true);
    venueLanPort1.setPortId(null);

    VenueLanPort venueLanPort2 = createVenueLanPort("2", false);
    venueLanPort2.setPortId(null);

    venueApModelSpecificAttributes.setLanPorts(List.of(venueLanPort1, venueLanPort2));

    CapabilitiesApModel capabilities = new CapabilitiesApModel();
    capabilities.setLanPorts(List.of(
        createCapabilitiesLanPort("1", ApLanPortTypeEnum.TRUNK.name()),
        createCapabilitiesLanPort("2", ApLanPortTypeEnum.ACCESS.name())
    ));

    // This should not throw an exception and should return capabilities defaults
    List<com.ruckus.acx.ddccm.protobuf.wifi.VenueApLanPort> result =
        ddccmVenueApModelOperationBuilder.mergeLanPorts(venueApModelSpecificAttributes, capabilities, false);

    assertThat(result).hasSize(2);

    // Both ports should use default values since all source ports had null portIds
    assertThat(result.get(0).getPortNameValue()).isEqualTo(1);
    assertThat(result.get(0).getEthProfileName()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_TRUNK_PORT_WAN_NAME);
    assertThat(result.get(0).getApLanPortProfileId()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_TRUNK_PORT_WAN_ID);

    assertThat(result.get(1).getPortNameValue()).isEqualTo(2);
    assertThat(result.get(1).getEthProfileName()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_ACCESS_PORT_NAME);
    assertThat(result.get(1).getApLanPortProfileId()).isEqualTo(DdccmVenueApModelOperationBuilder.DEFAULT_ACCESS_PORT_ID);
  }

  private CapabilitiesLanPort createCapabilitiesLanPort(String id, String defaultType) {
    CapabilitiesLanPort port = new CapabilitiesLanPort();
    port.setId(id);
    port.setDefaultType(defaultType);
    return port;
  }

  private VenueLanPort createVenueLanPort(String portId, boolean enabled) {
    var port = new VenueLanPort();
    port.setPortId(portId);
    port.setEnabled(enabled);
    port.setApLanPortProfile(new EthernetPortProfile("profile-" + portId));
    port.getApLanPortProfile().setApLanPortId(Integer.parseInt(portId) + 10);
    return port;
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    public DdccmVenueApModelOperationBuilder ddccmVenueApModelOperationBuilder() {
      return new DdccmVenueApModelOperationBuilder();
    }

    @Bean
    public DdccmVenueOperationBuilder ddccmVenueOperationBuilder() {
      return new DdccmVenueOperationBuilder();
    }

    @Bean
    public VenueRepository mockVenueRepository() {
      return Mockito.mock(VenueRepository.class);
    }

    @Bean
    public SoftGreProfileRepository mockSoftGreProfileRepository() {
      return Mockito.mock(SoftGreProfileRepository.class);
    }

    @Bean
    public ApFirmwareUpgradeRequestRepository
        mockApFirmwareUpgradeRequestRepository() {
      return Mockito.mock(ApFirmwareUpgradeRequestRepository.class);
    }
  }
}
