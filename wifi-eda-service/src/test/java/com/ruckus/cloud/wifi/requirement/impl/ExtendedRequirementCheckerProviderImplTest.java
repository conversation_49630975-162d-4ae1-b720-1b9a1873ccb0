package com.ruckus.cloud.wifi.requirement.impl;

import static org.apache.commons.lang3.RandomStringUtils.randomAlphanumeric;
import static org.apache.commons.lang3.RandomUtils.nextBoolean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willAnswer;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.compatibility.contract.requirement.RequirementCheckerGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.BssColoring;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.FastRoamingOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.MultiLinkOperationOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.EthernetPortProfileRepository;
import com.ruckus.cloud.wifi.repository.SdLanProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.repository.VenueApModelSpecificAttributesRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.requirement.ApFeatureRequirement;
import com.ruckus.cloud.wifi.service.ExtendedNetworkServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import javassist.tools.rmi.ObjectNotFoundException;
import org.apache.commons.lang3.RandomUtils;
import org.assertj.core.api.BDDAssertions;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.mockito.invocation.InvocationOnMock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class ExtendedRequirementCheckerProviderImplTest {
  @MockBean private ExtendedVenueServiceCtrl venueService;
  @MockBean private ExtendedNetworkServiceCtrl networkService;
  @MockBean private VenueApModelSpecificAttributesRepository apModelSpecificAttributesRepository;

  @MockBean private EthernetPortProfileRepository ethernetPortProfileRepository;
  @MockBean private VenueLanPortRepository venueLanPortRepository;
  @MockBean private SdLanProfileRegularSettingRepository sdLanProfileRegularSettingRepository;

  @SpyBean private ExtendedRequirementCheckerProviderImpl unit;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void beforeEach() {
    unit.init();
  }

  @Nested
  class WhenGetRequirementCheckersByVenueId {
    @Test
    void givenGetVenueFailed(Venue venue) throws Exception {
      willThrow(new ObjectNotFoundException(venue.getId()))
          .given(venueService)
          .getVenue(anyString());

      BDDAssertions.then(unit.getRequirementCheckers(venue.getId())).isNotNull().isEmpty();

      then(venueService).should().getVenue(eq(venue.getId()));
      then(unit).should(never()).getRequirementCheckers(any(Venue.class));
    }

    @Test
    void givenGetVenueSucceed(Venue venue) throws Exception {
      final var result = mock(List.class);
      willReturn(venue).given(venueService).getVenue(anyString());
      willReturn(result).given(unit).getRequirementCheckers(any(Venue.class));

      BDDAssertions.then(unit.getRequirementCheckers(venue.getId())).isNotNull().isEqualTo(result);

      then(venueService).should().getVenue(eq(venue.getId()));
      then(unit).should().getRequirementCheckers(eq(venue));
    }
  }

  @Nested
  class WhenGetRequirementCheckersByVenue {

    @Test
    void givenWifi7320MhzNotEnabled(Venue venue) {
      willReturn(false).given(unit).isWifi7320MhzEnabled(any(Venue.class));

      BDDAssertions.then(unit.getRequirementCheckers(venue))
          .isNotNull()
          .doesNotContain(ApFeatureRequirement.WIFI7_320MHZ);

      then(unit).should().isWifi7320MhzEnabled(eq(venue));
    }

    @Test
    void givenWifi7320MhzEnabled(Venue venue) {
      willReturn(true).given(unit).isWifi7320MhzEnabled(any(Venue.class));

      BDDAssertions.then(unit.getRequirementCheckers(venue))
          .isNotNull()
          .contains(ApFeatureRequirement.WIFI7_320MHZ);

      then(unit).should().isWifi7320MhzEnabled(eq(venue));
    }

    @Test
    void givenBandModeExisting(Venue venue) {
      willReturn(true).given(unit).isBandModeExisting(any(Venue.class));

      BDDAssertions.then(unit.getRequirementCheckers(venue))
          .isNotNull()
          .contains(ApFeatureRequirement.SWITCHABLE_RF);

      then(unit).should().isBandModeExisting(eq(venue));
    }

    @Test
    void givenBandModeExistingWithoutFF(Venue venue) {
      doReturn(true).when(apModelSpecificAttributesRepository)
          .existsByTenantIdAndVenueIdAndBandModeNotNull(anyString(), anyString());

      BDDAssertions.then(unit.getRequirementCheckers(venue))
          .isNotNull()
          .doesNotContain(ApFeatureRequirement.SWITCHABLE_RF);

      then(unit).should().isBandModeExisting(eq(venue));
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_SWITCHABLE_RF_TOGGLE)
    void givenBandModeExistingWithFF(Venue venue) {
      doReturn(true).when(apModelSpecificAttributesRepository)
          .existsByTenantIdAndVenueIdAndBandModeNotNull(anyString(), anyString());

      BDDAssertions.then(unit.getRequirementCheckers(venue))
          .isNotNull()
          .contains(ApFeatureRequirement.SWITCHABLE_RF);

      then(unit).should().isBandModeExisting(eq(venue));
    }
  }

  @Nested
  class WhenGetRequirementCheckerGroupsByVenueIds {
    @Test
    void thenReturnRequirementCheckerGroupsForEachVenue(Tenant tenant) {
      final var venues =
          Stream.generate(() -> VenueTestFixture.randomVenue(tenant)).limit(10).toList();
      final var venueIds = venues.stream().map(AbstractBaseEntity::getId).toList();

      willReturn(venues).given(venueService).findVenuesByVenueIds(anyList());
      willReturn(List.of()).given(unit).getRequirementCheckers(any(Venue.class));

      BDDAssertions.then(unit.getRequirementCheckerGroups(venueIds))
          .isNotNull()
          .hasSize(venues.size())
          .map(RequirementCheckerGroup::getId)
          .containsExactlyElementsOf(venueIds);

      then(venueService).should().findVenuesByVenueIds(eq(venueIds));
      final var venueCaptor = ArgumentCaptor.forClass(Venue.class);
      then(unit).should(times(venues.size())).getRequirementCheckers(venueCaptor.capture());
      assertThat(venueCaptor.getAllValues()).containsExactlyElementsOf(venues);
    }
  }

  @Nested
  class WhenGetRequirementCheckerGroupsByNetworkIds {
    @Test
    void thenReturnRequirementCheckerGroupsForEachNetwork(Tenant tenant) {
      final var networks =
          Stream.generate(() -> NetworkTestFixture.randomNetwork(tenant)).limit(10).toList();
      final var networkIds = networks.stream().map(AbstractBaseEntity::getId).toList();

      willReturn(networks).given(networkService).findNetworksByNetworkIds(anyList());
      willReturn(List.of()).given(unit).getRequirementCheckersByNetwork(anyString());

      BDDAssertions.then(unit.getRequirementCheckerGroupsByNetworks(networkIds))
          .isNotNull()
          .hasSize(networks.size())
          .map(RequirementCheckerGroup::getId)
          .containsExactlyElementsOf(networkIds);

      then(networkService).should().findNetworksByNetworkIds(eq(networkIds));
      final var networkCaptor = ArgumentCaptor.forClass(Network.class);
      then(unit)
          .should(times(networks.size()))
          .getRequirementCheckersByNetwork(networkCaptor.capture());
      assertThat(networkCaptor.getAllValues()).containsExactlyElementsOf(networks);
    }
  }

  @Nested
  class WhenGetRequirementCheckersByNetworkId {
    @Test
    void givenGetNetworkFailed(Network network) throws Exception {
      willThrow(new ObjectNotFoundException(network.getId()))
          .given(networkService)
          .getNetwork(anyString(), any());

      BDDAssertions.then(unit.getRequirementCheckersByNetwork(network.getId()))
          .isNotNull()
          .isEmpty();

      then(networkService).should().getNetwork(eq(network.getId()), eq(Optional.empty()));
      then(unit).should(never()).getRequirementCheckersByNetwork(any(Network.class));
    }

    @Test
    void givenGetNetworkSucceed(Network network) throws Exception {
      final var result = mock(List.class);
      willReturn(network).given(networkService).getNetwork(anyString(), any());
      willReturn(result).given(unit).getRequirementCheckersByNetwork(any(Network.class));

      BDDAssertions.then(unit.getRequirementCheckersByNetwork(network.getId()))
          .isNotNull()
          .isEqualTo(result);

      then(networkService).should().getNetwork(eq(network.getId()), eq(Optional.empty()));
      then(unit).should().getRequirementCheckersByNetwork(eq(network));
    }
  }

  @Nested
  class WhenGetRequirementCheckersByNetwork {
    @MockBean private ExtendedRequirementCheckerProviderImpl unit;

    @BeforeEach
    void beforeEach() {
      willAnswer(InvocationOnMock::callRealMethod)
          .given(unit)
          .getRequirementCheckersByNetwork(any(Network.class));
    }

    @Test
    void givenDSAENotEnabled(Network network) {
      willReturn(false).given(unit).isDSAEEnabled(any(Network.class));

      BDDAssertions.then(unit.getRequirementCheckersByNetwork(network))
          .isNotNull()
          .doesNotContain(ApFeatureRequirement.DSAE);

      then(unit).should().isDSAEEnabled(eq(network));
    }

    @Test
    void givenDSAEEnabled(Network network) {
      willReturn(true).given(unit).isDSAEEnabled(any(Network.class));

      BDDAssertions.then(unit.getRequirementCheckersByNetwork(network))
          .isNotNull()
          .contains(ApFeatureRequirement.DSAE);

      then(unit).should().isDSAEEnabled(eq(network));
    }

    @Test
    void givenQosMirroringNotEnabled(Network network) {
      willReturn(false).given(unit).isQosMirroringEnabled(any(Network.class));

      BDDAssertions.then(unit.getRequirementCheckersByNetwork(network))
          .isNotNull()
          .doesNotContain(ApFeatureRequirement.QOS_MIRRORING);

      then(unit).should().isQosMirroringEnabled(eq(network));
    }

    @Test
    void givenQosMirroringEnabled(Network network) {
      willReturn(true).given(unit).isQosMirroringEnabled(any(Network.class));

      BDDAssertions.then(unit.getRequirementCheckersByNetwork(network))
          .isNotNull()
          .contains(ApFeatureRequirement.QOS_MIRRORING);

      then(unit).should().isQosMirroringEnabled(eq(network));
    }

    @Test
    void givenOverTheDsNotEnabled(Network network) {
      willReturn(false).given(unit).isOverTheDsEnabled(any(Network.class));

      BDDAssertions.then(unit.getRequirementCheckersByNetwork(network))
          .isNotNull()
          .doesNotContain(ApFeatureRequirement.OVER_THE_DS);

      then(unit).should().isOverTheDsEnabled(eq(network));
    }

    @Test
    void givenOverTheDsEnabled(Network network) {
      willReturn(true).given(unit).isOverTheDsEnabled(any(Network.class));

      BDDAssertions.then(unit.getRequirementCheckersByNetwork(network))
          .isNotNull()
          .contains(ApFeatureRequirement.OVER_THE_DS);

      then(unit).should().isOverTheDsEnabled(eq(network));
    }
  }

  @Nested
  class WhenFindRequirementChecker {
    @Test
    void givenRequirementCheckerNotPresent() {
      BDDAssertions.then(unit.findRequirementChecker(randomAlphanumeric(10))).isNotNull().isEmpty();
    }

    @Test
    void givenRequirementCheckerPresent() {
      BDDAssertions.then(unit.findRequirementChecker(ApFeatureRequirement.DSAE.getFeatureName()))
          .isNotNull()
          .get()
          .isEqualTo(ApFeatureRequirement.DSAE);
    }
  }

  @Nested
  class WhenIsWifi7320MhzEnabled {
    @Test
    void givenChannelBandwidthNot320Mhz(Venue venue) {
      final var channels =
          Arrays.stream(ChannelBandwidth6GEnum.values())
              .filter(c -> c != ChannelBandwidth6GEnum._320MHz)
              .toList();
      final var radioParams6G = new VenueRadioParams6G();
      radioParams6G.setChannelBandwidth(channels.get(RandomUtils.nextInt(0, channels.size())));
      final var radioCustomization = new VenueRadioCustomization();
      radioCustomization.setRadioParams6G(radioParams6G);
      venue.setRadioCustomization(radioCustomization);

      BDDAssertions.then(unit.isWifi7320MhzEnabled(venue)).isFalse();
    }

    @Test
    void givenChannelBandwidthIs320Mhz(Venue venue) {
      final var radioParams6G = new VenueRadioParams6G();
      radioParams6G.setChannelBandwidth(ChannelBandwidth6GEnum._320MHz);
      final var radioCustomization = new VenueRadioCustomization();
      radioCustomization.setRadioParams6G(radioParams6G);
      venue.setRadioCustomization(radioCustomization);

      BDDAssertions.then(unit.isWifi7320MhzEnabled(venue)).isTrue();
    }
  }

  @Nested
  class WhenIsBssColoringEnabled {
    @Test
    void givenBssColoringIsNull() {
      final var venue = new Venue();
      venue.setBssColoring(null);

      BDDAssertions.then(unit.isBssColoringEnabled(venue)).isTrue();
    }

    @Nested
    class GivenBssColoringIsNotNull {
      @Test
      void thenReturnBssColorEnabled() {
        final var enabled = nextBoolean();
        final var bssColoring = new BssColoring();
        bssColoring.setBssColoringEnabled(enabled);
        final var venue = new Venue();
        venue.setBssColoring(bssColoring);

        BDDAssertions.then(unit.isBssColoringEnabled(venue)).isEqualTo(enabled);
      }
    }
  }

  @Nested
  class WhenIsDSAEEnabled {
    @Test
    void givenNetworkTypeNotDpskNetwork() {
      final var network = mock(OpenNetwork.class);

      BDDAssertions.then(unit.isDSAEEnabled(network)).isFalse();

      then(network).shouldHaveNoInteractions();
    }

    @Nested
    class GivenNetworkTypIsDpskNetwork {
      @Test
      void givenWlanIsNull() {
        final var network = new DpskNetwork();
        network.setWlan(null);

        BDDAssertions.then(unit.isDSAEEnabled(network)).isFalse();
      }

      @Nested
      class GivenWlanIsNotNull {
        @Test
        void givenWlanSecurityNotWPA32Mixed() {
          final var wlanSecurities =
              Arrays.stream(WlanSecurityEnum.values())
                  .filter(s -> s != WlanSecurityEnum.WPA23Mixed)
                  .toList();
          final var wlan = new Wlan();
          wlan.setWlanSecurity(wlanSecurities.get(RandomUtils.nextInt(0, wlanSecurities.size())));
          final var network = new DpskNetwork();
          network.setWlan(wlan);

          BDDAssertions.then(unit.isDSAEEnabled(network)).isFalse();
        }

        @Test
        void givenWlanSecurityIsWPA32Mixed() {
          final var wlan = new Wlan();
          wlan.setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
          final var network = new DpskNetwork();
          network.setWlan(wlan);

          BDDAssertions.then(unit.isDSAEEnabled(network)).isTrue();
        }
      }
    }
  }

  @Nested
  class WhenIsQosMirroringEnabled {
    @Test
    void givenWlanIsNull() {
      final var network = new Network();
      network.setWlan(null);

      BDDAssertions.then(unit.isQosMirroringEnabled(network)).isFalse();
    }

    @Nested
    class GivenWlanIsNotNull {
      @Test
      void givenAdvancedCustomizationIsNull(){
        final var wlan = new Wlan();
        wlan.setAdvancedCustomization(null);
        final var network = new Network();
        network.setWlan(wlan);

        BDDAssertions.then(unit.isQosMirroringEnabled(network)).isFalse();
      }

      @Nested
      class GivenAdvancedCustomizationIsNotNull{
        @Test
        void thenReturnEnabled() {
          final var enabled = nextBoolean();
          final var customization = new WlanAdvancedCustomization();
          customization.setQosMirroringEnabled(enabled);
          final var wlan = new Wlan();
          wlan.setAdvancedCustomization(customization);
          final var network = new Network();
          network.setWlan(wlan);

          BDDAssertions.then(unit.isQosMirroringEnabled(network)).isEqualTo(enabled);
        }
      }
    }
  }

  @Nested
  class WhenIsTlsKeyEnhancedModeEnabled {
    @Test
    void givenTlsKeyEnhancedModeIsNull() {
      final var venue = new Venue();
      venue.setTlsKeyEnhancedModeEnabled(null);

      BDDAssertions.then(unit.isTlsKeyEnhancedModeEnabled(venue)).isFalse();
    }

    @Nested
    class GivenTlsKeyEnhancedModeIsNotNull {
      @Test
      void thenReturnTlsKeyEnhancedModeEnabled() {
        final var enabled = nextBoolean();
        final var venue = new Venue();
        venue.setTlsKeyEnhancedModeEnabled(enabled);

        BDDAssertions.then(unit.isTlsKeyEnhancedModeEnabled(venue)).isEqualTo(enabled);
      }
    }
  }

  @Tag("EthernetPortProfileTest")
  @Nested
  class WhenIsTrunkPortVlanUntagIdEnabled {
    @Test
    @FeatureFlag(enable = FlagNames.WIFI_TRUNK_PORT_UNTAGGED_VLAN_TOGGLE)
    void givenExistTrunkPortUntagIdNotEqualOne() {
      final var venue = new Venue();
      when(ethernetPortProfileRepository.existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueId(eq(
          TxCtxHolder.tenantId()), eq(venue.getId()))).thenReturn(true);

      BDDAssertions.then(unit.isTrunkPortVlanUntagIdEnabled(venue)).isTrue();
    }

    @Test
    void givenExistTrunkPortUntagIdNotEqualOneWithoutFFEnabled() {
      final var venue = new Venue();
      when(ethernetPortProfileRepository.existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueId(eq(
          TxCtxHolder.tenantId()), eq(venue.getId()))).thenReturn(true);

      BDDAssertions.then(unit.isTrunkPortVlanUntagIdEnabled(venue)).isFalse();
    }

    @Test
    void givenExistTrunkPortUntagIdEqualOne() {
      final var venue = new Venue();
      when(ethernetPortProfileRepository.existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueId(eq(
          TxCtxHolder.tenantId()), eq(venue.getId()))).thenReturn(false);
      BDDAssertions.then(unit.isTrunkPortVlanUntagIdEnabled(venue)).isFalse();
    }
  }

  @Nested
  class WhenIsLbsEnabled {

    @Test
    void givenLbsServerProfileWithoutFF(Venue venue) {
      venue.setLbsServerProfile(Generators.lbsServerProfile().generate());

      BDDAssertions.then(unit.getRequirementCheckers(venue))
          .isNotNull()
          .doesNotContain(ApFeatureRequirement.LBS);

      then(unit).should().isLbsEnabled(eq(venue));
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_LBS_TOGGLE)
    void givenWithoutLbsServerProfileWithFF(Venue venue) {
      BDDAssertions.then(unit.getRequirementCheckers(venue))
          .isNotNull()
          .doesNotContain(ApFeatureRequirement.LBS);

      then(unit).should().isLbsEnabled(eq(venue));
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_LBS_TOGGLE)
    void givenLbsServerProfileWithFF(Venue venue) {
      venue.setLbsServerProfile(Generators.lbsServerProfile().generate());

      BDDAssertions.then(unit.getRequirementCheckers(venue))
          .isNotNull()
          .doesNotContain(ApFeatureRequirement.LBS);

      then(unit).should().isLbsEnabled(eq(venue));
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_LBS_TOGGLE)
    @FeatureRole("LOCATION-BASED-SERVICES")
    void givenLbsServerProfileWithFF_andBeta(Venue venue) {
      venue.setLbsServerProfile(Generators.lbsServerProfile().generate());

      BDDAssertions.then(unit.getRequirementCheckers(venue))
          .isNotNull()
          .contains(ApFeatureRequirement.LBS);

      then(unit).should().isLbsEnabled(eq(venue));
    }
  }

  @Nested
  class WhenIsOverTheDsEnabled {
    @Test
    @FeatureFlag(disable = FlagNames.WIFI_OVER_THE_DS_FT_SUPPORT_TOGGLE)
    void givenFFDisabled() {
      final var network = new Network();
      BDDAssertions.then(unit.isOverTheDsEnabled(network)).isFalse();
    }

    @Nested
    @FeatureFlag(enable = FlagNames.WIFI_OVER_THE_DS_FT_SUPPORT_TOGGLE)
    class GivenFFEnabled {
      @Test
      void givenWlanIsNull() {
        final var network = new Network();
        network.setWlan(null);

        BDDAssertions.then(unit.isOverTheDsEnabled(network)).isFalse();
      }

      @Test
      void givenAdvancedCustomizationIsNull() {
        final var wlan = new Wlan();
        wlan.setAdvancedCustomization(null);
        final var network = new Network();
        network.setWlan(wlan);

        BDDAssertions.then(unit.isOverTheDsEnabled(network)).isFalse();
      }

      @Nested
      class GivenAdvancedCustomizationIsNotNull {
        @Test
        void givenEnableFastRoamingIsFalse() {
          final var customization = new WlanAdvancedCustomization();
          customization.setEnableFastRoaming(false);
          final var wlan = new Wlan();
          wlan.setAdvancedCustomization(customization);
          final var network = new Network();
          network.setWlan(wlan);

          BDDAssertions.then(unit.isOverTheDsEnabled(network)).isFalse();
        }

        @Test
        void thenReturnEnabled() {
          final var enabled = nextBoolean();
          final var customization = new WlanAdvancedCustomization();
          customization.setEnableFastRoaming(true);
          final var fastRoamingOptions = new FastRoamingOptions();
          fastRoamingOptions.setStatisticsOverDistributedSystemEnabled(enabled);
          customization.setFastRoamingOptions(fastRoamingOptions);
          final var wlan = new Wlan();
          wlan.setAdvancedCustomization(customization);
          final var network = new Network();
          network.setWlan(wlan);

          BDDAssertions.then(unit.isOverTheDsEnabled(network)).isEqualTo(enabled);
        }
      }
    }
  }

  @Nested
  class WhenIsSdLanEnabled {
    @Test
    @FeatureFlag(disable = FlagNames.EDGE_COMPATIBILITY_CHECK_TOGGLE)
    void givenFFDisabled(Venue venue)  {
      BDDAssertions.then(unit.isSdLanEnabled(venue)).isFalse();
    }

    @Nested
    @FeatureFlag(enable = FlagNames.EDGE_COMPATIBILITY_CHECK_TOGGLE)
    class GivenFFEnabled {
      @Test
      void givenSdLanProfileRegularSettingNotExist(Venue venue)  {
        when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
            eq(TxCtxHolder.tenantId()), eq(venue.getId())))
            .thenReturn(false);

        BDDAssertions.then(unit.isSdLanEnabled(venue)).isFalse();
      }

      @Test
      void givenSdLanProfileRegularSettingExist(Venue venue)  {
        when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
            eq(TxCtxHolder.tenantId()), eq(venue.getId())))
            .thenReturn(true);

        BDDAssertions.then(unit.isSdLanEnabled(venue)).isTrue();
      }
    }
  }

  @Nested
  class WhenIsTunnelProfileEnabled {
    @Test
    @FeatureFlag(disable = {FlagNames.EDGE_COMPATIBILITY_CHECK_TOGGLE})
    void givenBothFFDisabled(Venue venue)  {
      BDDAssertions.then(unit.isTunnelProfileEnabled(venue)).isFalse();
    }

    @Test
    @FeatureFlag(disable = FlagNames.EDGE_COMPATIBILITY_CHECK_TOGGLE)
    void givenOneOfFFDisabled(Venue venue)  {
      BDDAssertions.then(unit.isSdLanEnabled(venue)).isFalse();
    }

    @Nested
    @FeatureFlag(enable = {FlagNames.EDGE_COMPATIBILITY_CHECK_TOGGLE})
    class GivenFFEnabled {
      @Test
      void givenSdLanProfileRegularSettingNotExist(Venue venue)  {
        when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
            eq(TxCtxHolder.tenantId()), eq(venue.getId())))
            .thenReturn(false);

        BDDAssertions.then(unit.isTunnelProfileEnabled(venue)).isFalse();
      }

      @Test
      void givenSdLanProfileRegularSettingExist(Venue venue)  {
        when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
            eq(TxCtxHolder.tenantId()), eq(venue.getId())))
            .thenReturn(true);

        BDDAssertions.then(unit.isTunnelProfileEnabled(venue)).isTrue();
      }
    }
  }

  @Nested
  class WhenIsMlo3LinksEnabled {

    @Test
    void givenWlanIsNull() {
      final var network = new Network();
      network.setWlan(null);

      BDDAssertions.then(unit.isMlo3LinksEnabled(network)).isFalse();
    }

    @Nested
    class GivenWlanIsNotNull {

      @Test
      void givenAdvancedCustomizationIsNull() {
        final var wlan = new Wlan();
        wlan.setAdvancedCustomization(null);
        final var network = new Network();
        network.setWlan(wlan);

        BDDAssertions.then(unit.isMlo3LinksEnabled(network)).isFalse();
      }

      @Nested
      class GivenAdvancedCustomizationIsNotNull {

        @Nested
        class Given3LinksEnabled {

          @Test
          void thenReturnEnabled() {
            var network = getMultiLinkOperationNetwork(true, true);

            BDDAssertions.then(unit.isMlo3LinksEnabled(network)).isEqualTo(false);
          }

          @Test
          void thenReturnDisabled() {
            var network = getMultiLinkOperationNetwork(false, true);

            BDDAssertions.then(unit.isMlo3LinksEnabled(network)).isEqualTo(false);
          }
        }

        @Nested
        @FeatureFlag(enable = FlagNames.WIFI_EDA_WIFI7_MLO_3LINK_TOGGLE)
        class Given3LinksAndFeatureFlagEnabled {

          @Test
          void thenReturnEnabled() {
            var network = getMultiLinkOperationNetwork(true, true);

            BDDAssertions.then(unit.isMlo3LinksEnabled(network)).isEqualTo(true);
          }

          @Test
          void thenReturnDisabled() {
            var network = getMultiLinkOperationNetwork(false, true);

            BDDAssertions.then(unit.isMlo3LinksEnabled(network)).isEqualTo(false);
          }
        }

        @Nested
        @FeatureFlag(enable = FlagNames.WIFI_EDA_WIFI7_MLO_3LINK_TOGGLE)
        class Given2LinksEnabled {

          @Test
          void thenReturnDisabled() {
            var network = getMultiLinkOperationNetwork(true, false);

            BDDAssertions.then(unit.isMlo3LinksEnabled(network)).isEqualTo(false);
          }
        }

        @NotNull
        private Network getMultiLinkOperationNetwork(boolean multiLinkOperationEnabled,
            boolean enable6G) {
          final var customization = new WlanAdvancedCustomization();
          customization.setMultiLinkOperationEnabled(multiLinkOperationEnabled);
          MultiLinkOperationOptions multiLinkOperationOptions = getMultiLinkOperationOptions(
              enable6G);
          customization.setMultiLinkOperationOptions(multiLinkOperationOptions);
          final var wlan = new Wlan();
          wlan.setAdvancedCustomization(customization);
          final var network = new Network();
          network.setWlan(wlan);
          return network;
        }

        @NotNull
        private MultiLinkOperationOptions getMultiLinkOperationOptions(boolean enable6G) {
          MultiLinkOperationOptions multiLinkOperationOptions = new MultiLinkOperationOptions();
          multiLinkOperationOptions.setEnable24G(true);
          multiLinkOperationOptions.setEnable50G(true);
          multiLinkOperationOptions.setEnable6G(enable6G);
          return multiLinkOperationOptions;
        }
      }
    }

  }

  @Nested
  class WhenIsStickyClientSteeringEnabledEnabled {

    @Test
    void givenVenueLoadBalancingNull(Venue venue) {
      BDDAssertions.then(unit.isStickyClientSteeringEnabled(venue)).isFalse();
    }

    @Test
    void givenVenueLoadBalancingDisabled(Venue venue) {
      var venueLoadBalancing = Generators.venueLoadBalancing().generate();
      venueLoadBalancing.setEnabled(false);
      BDDAssertions.then(unit.isStickyClientSteeringEnabled(venue)).isFalse();
    }

    @Test
    void givenStickyClientSteeringDisabled(Venue venue) {
      var venueLoadBalancing = Generators.venueLoadBalancing().generate();
      venueLoadBalancing.setEnabled(true);
      venueLoadBalancing.setStickyClientSteeringEnabled(false);
      venue.setLoadBalancing(venueLoadBalancing);
      BDDAssertions.then(unit.isStickyClientSteeringEnabled(venue)).isFalse();
    }

    @Test
    void givenStickyClientSteeringEnabled(Venue venue) {
      var venueLoadBalancing = Generators.venueLoadBalancing().generate();
      venueLoadBalancing.setEnabled(true);
      venueLoadBalancing.setStickyClientSteeringEnabled(true);
      venue.setLoadBalancing(venueLoadBalancing);
      BDDAssertions.then(unit.isStickyClientSteeringEnabled(venue)).isTrue();
    }
  }
}
