package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.cloud.wifi.servicemodel.projection.VenueScheduleTimeSlotProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

@WifiJpaDataTest
class ScheduleTimeSlotRepositoryTest {

  @Autowired
  private ScheduleTimeSlotRepository repository;

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenantId1');
      INSERT INTO ap_version (id) VALUES ('6.2.2.103.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'WAITING_REMINDER', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version)
        VALUES ('tenantId1', 'venueId1', '6.2.2.103.124'),
               ('tenantId1', 'venueId2', '6.2.2.103.124'),
               ('tenantId1', 'venueId3', '6.2.2.103.124');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '6.2.2.103.124', 'venueId1'), 
               ('tenantId1', 'scheduleId2', 'PENDING', 'timeSlotId', '6.2.2.103.124', 'venueId2'),
               ('tenantId1', 'scheduleId3', 'PENDING', 'timeSlotId', '6.2.2.103.124', 'venueId3');
      """)
  void testFindVenueScheduleTimeSlotByVenueIdIn() {
    List<VenueScheduleTimeSlotProjection> res =
        repository.findVenueScheduleTimeSlotByVenueIdIn(List.of("venueId1", "venueId2"));
    assertThat(res)
        .isNotNull()
        .hasSize(2)
        .satisfies(projections -> {
          assertEquals("timeSlotId", projections.get(0).getScheduleTimeSlotId());
          assertEquals("timeSlotId", projections.get(1).getScheduleTimeSlotId());
        });

    res = repository.findVenueScheduleTimeSlotByVenueIdIn(List.of("venueId3"));
    assertThat(res).isNotNull().first().matches(projection -> projection.getScheduleTimeSlotId().equals("timeSlotId"));
  }
}
