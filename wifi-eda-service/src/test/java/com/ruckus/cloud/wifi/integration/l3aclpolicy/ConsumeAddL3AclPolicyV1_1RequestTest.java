package com.ruckus.cloud.wifi.integration.l3aclpolicy;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.L3AclPolicyTestFixture.randomL3AclPolicy;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.L3AclPolicyRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3Rule;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeAddL3AclPolicyV1_1RequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeAddL3AclPolicyV1_1Message {
    @Test
    void givenDuplicatePolicyName(Tenant tenant) {
      final var duplicateName = randomName();
      repositoryUtil.createOrUpdate(
          randomL3AclPolicy(tenant, p -> p.setName(duplicateName)), tenant.getId(), randomTxId());

      final var l3AclPolicy =
          L3AclPolicyRestCtrl.L3AclPolicyMapper.INSTANCE.ServiceL3AclPolicy2L3AclPolicy(
              randomL3AclPolicy(tenant, p -> p.setName(duplicateName)));

      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.ADD_L3ACL_POLICY_V1_1,
                      randomName(),
                      new RequestParams(),
                      l3AclPolicy))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ADD_L3ACL_POLICY));
    }

    @Test
    void thenSaveAndSendingMessages(Tenant tenant) {
      final var l3AclPolicy =
          L3AclPolicyRestCtrl.L3AclPolicyMapper.INSTANCE.ServiceL3AclPolicy2L3AclPolicy(
              randomL3AclPolicy(tenant, p -> {}));

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.ADD_L3ACL_POLICY_V1_1,
          randomName(),
          new RequestParams(),
          l3AclPolicy);

      assertThat(repositoryUtil.find(L3AclPolicy.class, l3AclPolicy.getId(), tenant.getId()))
          .isNotNull()
          .matches(p -> p.getName().equals(l3AclPolicy.getName()))
          .matches(p -> p.getDefaultAccess() == l3AclPolicy.getDefaultAccess())
          .matches(p -> p.getDescription().equals(l3AclPolicy.getDescription()))
          .extracting(L3AclPolicy::getL3Rules, InstanceOfAssertFactories.list(L3Rule.class))
          .hasSize(l3AclPolicy.getL3Rules().size())
          .allMatch(
              rule ->
                  l3AclPolicy.getL3Rules().stream().anyMatch(r -> r.getId().equals(rule.getId())));

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(1)
          .first()
          .matches(o -> o.getAction() == Action.ADD)
          .matches(o -> o.getId().equals(l3AclPolicy.getId()));

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.ADD)
          .matches(o -> o.getId().equals(l3AclPolicy.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.ADD_L3ACL_POLICY));
    }
  }
}
