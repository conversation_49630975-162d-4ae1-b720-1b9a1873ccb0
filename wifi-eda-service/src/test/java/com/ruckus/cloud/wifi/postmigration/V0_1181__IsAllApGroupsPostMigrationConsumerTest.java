package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.networkApGroup;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class V0_1181__IsAllApGroupsPostMigrationConsumerTest {

  @Autowired
  protected MessageCaptors messageCaptors;

  @Autowired
  private V0_1181__IsAllApGroupsPostMigrationConsumer postMigrationConsumer;

  @Nested
  class PostMigrationNetworkData {

    private String networkId;
    private String venueId;
    private String apGroupId;
    private String networkVenueId;

    @BeforeEach
    void givenOpenNetworkAndRelatedEntitiesPersistedInDb(@OpenNetwork Network network, Venue venue,
        @DefaultApGroup ApGroup apGroup,
        @ScheduledNetworkVenue NetworkVenue networkVenue) {
      networkId = network.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
      networkVenueId = networkVenue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkVenueId", networkVenueId);
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithIsAllApGroupsFalse() {
      final var generator = Generators.networkVenue()
          .setId(nullValue(String.class))
          .setNetworkId(always(networkId))
          .setVenueId(always(venueId))
          .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM))
          .setIsAllApGroups(alwaysFalse())
          .setAllApGroupsRadio(always(RadioEnum.Both))
          .setAllApGroupsRadioTypes(list(options(RadioTypeEnum._5_GHz), 1))
          .setApGroups(list(networkApGroup()
                  .setId(nullValue(String.class)).setVlanId(nullValue(Short.class))
                  .setRadioTypes(list(options(RadioTypeEnum._5_GHz), 1))
                  .setApGroupId(always(apGroupId))
                  .setRadio(always(RadioEnum._5_GHz))
              , 1));
      return generator.generate();
    }

    @Test
    @ApiAction(CfgAction.UPDATE_NETWORK_VENUE)
    void testPostMigrationWithNetworkApGroupData(TxCtxHolder.TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload)
        throws Exception {

      txCtx.setTxId(randomTxId());
      postMigrationConsumer.run(null);

      final var cmnCfgCollectorMessage1 = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant());
      final var cmnCfgCollectorMessage2 = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant());
      final var cmnCfgCollectorMessage3 = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant());

      assertThat(cmnCfgCollectorMessage1.getPayload()).isNotNull();
      assertThat(cmnCfgCollectorMessage2.getPayload()).isNotNull();
      assertThat(cmnCfgCollectorMessage3.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage1.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK_DEVICE_GROUP_MAPPING.equals(o.getIndex())).count() == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK_DEVICE_GROUP_MAPPING.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.ADD)
          .matches(p -> Boolean.FALSE.equals(p.getDocMap().get("isAllApGroups").getBoolValue()));

      assertThat(cmnCfgCollectorMessage2.getPayload())
          .matches(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK_DEVICE_GROUP_MAPPING.equals(o.getIndex())).count() == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK_DEVICE_GROUP_MAPPING.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.DEL);

      assertThat(cmnCfgCollectorMessage3.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK_DEVICE_GROUP_MAPPING.equals(o.getIndex())).count() == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK_DEVICE_GROUP_MAPPING.equals(o.getIndex())).findFirst().get())
          .matches(p -> Boolean.FALSE.equals(p.getDocMap().get("isAllApGroups").getBoolValue()))
          .matches(p -> p.getOpType() == OpType.ADD);
    }
  }
}
