package com.ruckus.cloud.wifi.integration.apgroup;

import static com.ruckus.cloud.wifi.service.impl.ExtendedApGroupServiceCtrlImpl.DEFAULT_AP_GROUP_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.viewmodel.ApSerialNumber;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.header.Header;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@Tag("ApGroupTest")
@WifiIntegrationTest
public class ConsumeUpdateApGroupRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class whenConsumeUpdateApGroupRequest {

    @Test
    void thenUpdateApGroup_withoutAps(Venue venue, @DefaultApGroup ApGroup defaultApGroup) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var apGroupName = randomName();
      final var tenantId = defaultApGroup.getTenant().getId();

      repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(defaultApGroup.getVenue(), c -> c.setId(apGroupId)),
          tenantId, randomTxId());

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.ApGroup();
      payload.setName(apGroupName);

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.UPDATE_AP_GROUP, userName,
          requestParams, payload);

      validateResult(tenantId, requestId, venue.getId(), defaultApGroup.getId(), apGroupId, payload);
    }

    @Test
    void thenUpdateApGroup_withAp(Venue venue, @DefaultApGroup ApGroup defaultApGroup, Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var apGroupName = randomName();
      final var tenantId = defaultApGroup.getTenant().getId();

      defaultApGroup.setAps(List.of(ap));
      repositoryUtil.createOrUpdate(defaultApGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(defaultApGroup.getVenue(), c -> c.setId(apGroupId)),
          tenantId, randomTxId());

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.ApGroup();
      payload.setName(apGroupName);
      payload.setApSerialNumbers(List.of(toApSerialNumber(ap.getId())));

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.UPDATE_AP_GROUP, userName,
          requestParams, payload);

      validateResult(tenantId, requestId, venue.getId(), defaultApGroup.getId(), apGroupId, payload);
    }
  }

  void validateResult(String tenantId, String requestId, String venueId, String defaultApGroupId,
      String apGroupId, com.ruckus.cloud.wifi.eda.viewmodel.ApGroup payload) {
    final var apGroup = repositoryUtil.find(ApGroup.class, apGroupId);

    assertThat(apGroup)
        .isNotNull()
        .matches(a -> Objects.equals(a.getId(), apGroupId))
        .matches(a -> Objects.equals(a.getName(), payload.getName()));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    // validate AP Group cmnCfgCollectorMessage
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
        .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
        .isNotEmpty().hasSize(CollectionUtils.isEmpty(payload.getApSerialNumbers()) ? 1 : 2)
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> apGroupId.equals(op.getId()))
              .isNotEmpty().singleElement()
              .satisfies(op -> {
                assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                assertThat(op.getDocMap())
                    .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                    .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                    .containsEntry(Key.ID, ValueUtils.stringValue(apGroupId))
                    .containsEntry(Key.NAME, ValueUtils.stringValue(payload.getName()))
                    .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                    .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                    .satisfies(docMap -> {
                      if (StringUtils.isNotEmpty(payload.getDescription())) {
                        assertThat(docMap)
                            .containsEntry(Key.DESCRIPTION,
                                ValueUtils.stringValue(payload.getDescription()));
                      }
                    });
              });
          if (CollectionUtils.isNotEmpty(payload.getApSerialNumbers())) {
            assertThat(ops)
                .filteredOn(op -> defaultApGroupId.equals(op.getId()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                      .containsEntry(Key.ID, ValueUtils.stringValue(defaultApGroupId))
                      .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_AP_GROUP_NAME.trim()))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                      .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(true));
                });
          }
        });

    // validate AP cmnCfgCollectorMessage
    if (payload.getApSerialNumbers() != null) {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(requestId))
          .matches(p ->
              p.getOperationsList().stream().filter(o -> EsConstants.Index.DEVICE.equals(o.getIndex())).count()
                  == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o -> EsConstants.Index.DEVICE.equals(o.getIndex()))
              .findFirst().get())
          .matches(p -> p.getOpType() == OpType.MOD)
          .matches(p -> p.getId().equals(payload.getApSerialNumbers().get(0).getSerialNumber()));
    }

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull();
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(Status.OK))
        .matches(p -> p.getStep().equals(ApiFlowNames.UPDATE_AP_GROUP))
        .matches(p -> p.getEventDate() != null);

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(requestId);

    assertThat(activityImpactedMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);

    final var apIds =
        apGroup.getAps().stream().map(Ap::getId).collect(Collectors.toSet());
    assertThat(activityImpactedMessage.getPayload())
        .matches(p -> p.getDeviceIdsList().containsAll(apIds))
        .matches(p -> p.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID));
  }

  private ApSerialNumber toApSerialNumber(String id) {
    ApSerialNumber apSerialNumber = new ApSerialNumber();
    apSerialNumber.setSerialNumber(id);
    apSerialNumber.setSoftDeleted(false);
    return apSerialNumber;
  }
}
