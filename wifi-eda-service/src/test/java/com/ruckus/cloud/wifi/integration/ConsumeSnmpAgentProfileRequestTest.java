package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.acx.ddccm.protobuf.wifi.SnmpAgent;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.nuketenant.protobuf.message.Nuketenant.DeleteTenant;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.venue.proto.Address;
import com.ruckus.cloud.venue.proto.Operation;
import com.ruckus.cloud.venue.proto.VenueEvent;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.SnmpAgentProfileRestCtrl.SnmpAgentProfileMapper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV2Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV3Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApSnmpAgentProfileSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSnmpAgent;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SnmpNotificationTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRequest;
import com.ruckus.cloud.wifi.eda.viewmodel.SnmpAgentProfileV1_1;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.error.WifiCommonException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApSnmpAgentProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiIntegrationTest
@Sql("classpath:sql/vspot.sql")
public class ConsumeSnmpAgentProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Test
  void testAddSnmpAgentProfileAndThenGetProfiles(ApSnmpAgentProfile snmpAgentProfile) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var tenantId = snmpAgentProfile.getTenant().getId();

    var viewSnmpAgentProfile = SnmpAgentProfileMapper.INSTANCE.ServiceApSnmpAgentProfile2SnmpAgentProfileV1_1(
        snmpAgentProfile);

    viewSnmpAgentProfile.setId(randomId());
    viewSnmpAgentProfile.setName("name");
    viewSnmpAgentProfile.setSnmpV2Agents(null);
    viewSnmpAgentProfile.setSnmpV3Agents(null);

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.ADD_SNMP_AGENT_PROFILE_V1_1, userName,
        viewSnmpAgentProfile);

    validateSnmpAgentProfileResult(Action.ADD, tenantId, requestId, viewSnmpAgentProfile);

    assertEquals(2,
        Objects.requireNonNull(repositoryUtil.findAll(ApSnmpAgentProfile.class, tenantId)).size());
  }

  @Test
  void testApplySnmpAgentAndThenDeleteApVenue(Venue venue, Ap ap,
      ApSnmpAgentProfile snmpAgentProfile) {
    final var userName = randomName();

    var requestId = randomTxId();
    var tenantId = venue.getTenant().getId();

    messageUtil.sendWifiCfgRequest(venue.getTenant().getId(), requestId,
        CfgAction.ACTIVATE_SNMP_AGENT_PROFILE_ON_VENUE, userName,
        new RequestParams().addPathVariable("venueId", venue.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfile.getId()), StringUtils.EMPTY);

    var viewSnmpAgentProfile = SnmpAgentProfileMapper.INSTANCE.ServiceApSnmpAgentProfile2SnmpAgentProfileV1_1(
        snmpAgentProfile);

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile, 1,
        1); // venues should be 1, aps should be 1

    requestId = randomTxId();

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.DELETE_AP, userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()), "");

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(cmnCfgCollectorMessage, requestId, tenantId);

    requestId = randomTxId();

    VenueEvent event = VenueEvent.newBuilder().setTenantId(tenantId).addOperation(
            Operation.newBuilder().setAction(com.ruckus.cloud.venue.proto.Action.DELETE).setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    // venues should be 0, aps should be 0
    validateSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, venue.getTenant().getId(), requestId,
        viewSnmpAgentProfile);
  }

  @Test
  void testApplySnmpAgentAndThenDeleteTenant(Tenant tenant, Venue venue, Ap ap,
      ApSnmpAgentProfile snmpAgentProfile) {
    final var userName = randomName();

    var requestId = randomTxId();
    var tenantId = tenant.getId();
    var snmpAgentProfileId = snmpAgentProfile.getId();

    messageUtil.sendWifiCfgRequest(venue.getTenant().getId(), requestId,
        CfgAction.ACTIVATE_SNMP_AGENT_PROFILE_ON_VENUE, userName,
        new RequestParams().addPathVariable("venueId", venue.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfile.getId()), StringUtils.EMPTY);

    messageUtil.sendWifiCfgRequest(ap.getTenant().getId(), requestId,
        CfgAction.ACTIVATE_SNMP_AGENT_PROFILE_ON_AP, userName,
        new RequestParams().addPathVariable("venueId", ap.getApGroup().getVenue().getId())
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfileId), StringUtils.EMPTY);

    var viewSnmpAgentProfile = SnmpAgentProfileMapper.INSTANCE.ServiceApSnmpAgentProfile2SnmpAgentProfileV1_1(
        snmpAgentProfile);

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile, 1,
        1); // venues should be 1, aps should be 1

    assertEquals(1, Objects.requireNonNull(
        repositoryUtil.findAll(ApSnmpAgentProfile.class, tenantId)).size());

    requestId = randomTxId();

    final var deleteTenantMessage = DeleteTenant.newBuilder().setTenantId(tenantId).build();

    messageUtil.sendDeleteTenant(requestId, deleteTenantMessage);

    assertEquals(0, Objects.requireNonNull(
        repositoryUtil.findAll(ApSnmpAgentProfile.class, tenantId)).size());
  }

  @Test
  void testApplySnmpAgentAndThenUpdateApVenueName(Venue venue, Ap ap,
      ApSnmpAgentProfile snmpAgentProfile) {
    final var userName = randomName();

    var requestId = randomTxId();
    var tenantId = venue.getTenant().getId();

    messageUtil.sendWifiCfgRequest(venue.getTenant().getId(), requestId,
        CfgAction.ACTIVATE_SNMP_AGENT_PROFILE_ON_VENUE, userName,
        new RequestParams().addPathVariable("venueId", venue.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfile.getId()), StringUtils.EMPTY);

    var viewSnmpAgentProfile = SnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2SnmpAgentProfileV1_1(snmpAgentProfile);

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile, 1,
        1); // venues should be 1, aps should be 1

    requestId = randomTxId();

    VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(com.ruckus.cloud.venue.proto.Action.MODIFY)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(venue.getId())
                    .setVenueName("venueNewName")
                    .setAddress(Address.newBuilder()
                        .setCountryCode(venue.getCountryCode())
                        .setTimezone(venue.getTimezone())
                        .setAddressLine(venue.getAddressLine())
                        .setLongitude(Double.parseDouble(venue.getDeviceGps().getLongitude()))
                        .setLatitude(Double.parseDouble(venue.getDeviceGps().getLatitude()))
                        .build())))
        .build();

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile, 1,
        1, "venueNewName", null); // venues should be 1, aps should be 1

    requestId = randomTxId();

    final var apRequest = new ApRequest();
    apRequest.setSerialNumber(ap.getId());
    apRequest.setName("apNewName");
    apRequest.setApGroupId(ap.getApGroup().getId());
    apRequest.setVenueId(ap.getApGroup().getVenue().getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile, 1,
        1, "venueNewName", apRequest.getName()); // venues should be 1, aps should be 1
  }

  @Tag("ApGroupTest")
  @Test
  void testApplyToApAndThenMoveVenue(Venue venue, ApGroup apGroup, Ap ap,
      ApSnmpAgentProfile snmpAgentProfile) {
    final var userName = randomName();

    var requestId = randomTxId();
    var tenantId = venue.getTenant().getId();

    var newVenue = VenueTestFixture.randomVenue(venue.getTenant());
    var newApGroup = ApGroupTestFixture.randomApGroup(newVenue);

    repositoryUtil.createOrUpdate(newVenue, tenantId, randomTxId());
    repositoryUtil.createOrUpdate(newApGroup, tenantId, randomTxId());

    messageUtil.sendWifiCfgRequest(venue.getTenant().getId(), requestId,
        CfgAction.ACTIVATE_SNMP_AGENT_PROFILE_ON_VENUE, userName,
        new RequestParams().addPathVariable("venueId", venue.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfile.getId()), StringUtils.EMPTY);

    var viewSnmpAgentProfile = SnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2SnmpAgentProfileV1_1(snmpAgentProfile);

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile, 1,
        1); // venues should be 1, aps should be 1

    requestId = randomTxId();

    final var apRequest = new ApRequest();
    apRequest.setSerialNumber(ap.getId());
    apRequest.setName(ap.getName());
    apRequest.setApGroupId(newApGroup.getId());
    apRequest.setVenueId(newApGroup.getVenue().getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile, 1,
        0); // venues should be 1, aps should be 0

    var snmpAgentProfile2 = ApSnmpAgentProfileTestFixture
        .randomApSnmpAgentProfile(venue.getTenant());

    repositoryUtil.createOrUpdate(snmpAgentProfile2, tenantId, randomTxId());

    var viewSnmpAgentProfile2 = SnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2SnmpAgentProfileV1_1(snmpAgentProfile2);

    messageUtil.sendWifiCfgRequest(venue.getTenant().getId(), requestId,
        CfgAction.ACTIVATE_SNMP_AGENT_PROFILE_ON_VENUE, userName,
        new RequestParams().addPathVariable("venueId", newVenue.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfile2.getId()), StringUtils.EMPTY);

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile2, 1,
        1); // venues should be 1, aps should be 1

    requestId = randomTxId();

    apRequest.setSerialNumber(ap.getId());
    apRequest.setName(ap.getName());
    apRequest.setApGroupId(apGroup.getId());
    apRequest.setVenueId(venue.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);

    final var cmnCfgCollectorMessage = getCmnCfgCollectorMessage(tenantId, requestId);

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile2,
        cmnCfgCollectorMessage, 2, 1, 0); // venues should be 1, aps should be 0

    validateUpdSnmpAgentProfileCmnCfgCollectorRequest(tenantId, requestId, viewSnmpAgentProfile,
        cmnCfgCollectorMessage, 2, 1, 1); // venues should be 1, aps should be 1
  }

  @Test
  void testUpdateSnmpAgentProfileV1_1(ApSnmpAgentProfile snmpAgentProfile) {
    var requestId = randomTxId();
    final var userName = randomName();

    var tenant = snmpAgentProfile.getTenant();

    var viewSnmpAgentProfile = SnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2SnmpAgentProfileV1_1(snmpAgentProfile);

    viewSnmpAgentProfile.setName("newname");
    viewSnmpAgentProfile.setSnmpV2Agents(null);
    viewSnmpAgentProfile.setSnmpV3Agents(null);

    messageUtil.sendWifiCfgRequest(
        tenant.getId(), requestId,
        CfgAction.UPDATE_SNMP_AGENT_PROFILE_V1_1, userName,
        new RequestParams().addPathVariable("snmpAgentProfileId", snmpAgentProfile.getId()),
        viewSnmpAgentProfile);

    validateSnmpAgentProfileResult(Action.MODIFY, snmpAgentProfile.getTenant().getId(),
        requestId, viewSnmpAgentProfile);

    requestId = randomTxId();

    var snmpAgentProfile2 = ApSnmpAgentProfileTestFixture.randomApSnmpAgentProfile(tenant);

    var apSnmpV2Agent1 = snmpAgentProfile2.getSnmpV2Agents().get(0);
    apSnmpV2Agent1.setCommunityName("public");
    apSnmpV2Agent1.setReadPrivilege(false);

    var apSnmpV2Agent2 = new ApSnmpV2Agent();
    apSnmpV2Agent2.setCommunityName("snmp-1");
    apSnmpV2Agent2.setReadPrivilege(true);
    apSnmpV2Agent2.setApSnmpAgentProfile(snmpAgentProfile2);

    snmpAgentProfile2.setSnmpV2Agents(List.of(apSnmpV2Agent1, apSnmpV2Agent2));

    var apSnmpV3Agent1 = snmpAgentProfile2.getSnmpV3Agents().get(0);
    apSnmpV3Agent1.setUserName("public");

    var apSnmpV3Agent2 = new ApSnmpV3Agent();
    apSnmpV3Agent2.setUserName("snmp-1");
    apSnmpV3Agent2.setTrapPrivilege(true);
    apSnmpV3Agent2.setNotificationType(SnmpNotificationTypeEnum.Inform);
    apSnmpV3Agent2.setTargetAddr("1.1.1.1");
    apSnmpV3Agent2.setAuthPassword(randomName());
    apSnmpV3Agent2.setApSnmpAgentProfile(snmpAgentProfile2);

    snmpAgentProfile2.setSnmpV3Agents(List.of(apSnmpV3Agent1, apSnmpV3Agent2));

    repositoryUtil.createOrUpdate(snmpAgentProfile2, tenant.getId(), randomTxId());

    viewSnmpAgentProfile = SnmpAgentProfileMapper.INSTANCE
        .ServiceApSnmpAgentProfile2SnmpAgentProfileV1_1(snmpAgentProfile2);

    viewSnmpAgentProfile.getSnmpV2Agents().remove(0);
    viewSnmpAgentProfile.getSnmpV3Agents().remove(0);

    messageUtil.sendWifiCfgRequest(
        tenant.getId(), requestId,
        CfgAction.UPDATE_SNMP_AGENT_PROFILE_V1_1, userName,
        new RequestParams().addPathVariable("snmpAgentProfileId", snmpAgentProfile2.getId()),
        viewSnmpAgentProfile);

    validateSnmpAgentProfileResult(Action.MODIFY, snmpAgentProfile.getTenant().getId(), requestId,
        viewSnmpAgentProfile);
  }

  @Test
  void testDeleteSnmpAgentProfile(ApSnmpAgentProfile snmpAgentProfile) {
    final var requestId = randomTxId();
    final var userName = randomName();

    var viewSnmpAgentProfile = new SnmpAgentProfileV1_1();

    viewSnmpAgentProfile.setId(snmpAgentProfile.getId());

    messageUtil.sendWifiCfgRequest(
        snmpAgentProfile.getTenant().getId(), requestId,
        CfgAction.DELETE_SNMP_AGENT_PROFILE, userName,
        new RequestParams().addPathVariable("snmpAgentProfileId", snmpAgentProfile.getId()),
        "");

    validateSnmpAgentProfileResult(Action.DELETE, snmpAgentProfile.getTenant().getId(), requestId,
        viewSnmpAgentProfile);
  }

  @Test
  void thenUpdateVenueSnmpAgent(Venue venue, ApSnmpAgentProfile apSnmpAgentProfile) {
    var requestId = randomTxId();
    final var userName = randomName();
    final var snmpAgentProfileId = apSnmpAgentProfile.getId();
    final var snmpAgentProfileName = apSnmpAgentProfile.getPolicyName();

    messageUtil.sendWifiCfgRequest(venue.getTenant().getId(), requestId,
        CfgAction.ACTIVATE_SNMP_AGENT_PROFILE_ON_VENUE, userName,
        new RequestParams().addPathVariable("venueId", venue.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfileId),
        StringUtils.EMPTY);

    validateUpdateVenueSnmpAgentResult(requestId, venue.getId(), snmpAgentProfileId,
        snmpAgentProfileName, true);

    requestId = randomTxId();

    messageUtil.sendWifiCfgRequest(venue.getTenant().getId(), requestId,
        CfgAction.DEACTIVATE_SNMP_AGENT_PROFILE_ON_VENUE, userName,
        new RequestParams().addPathVariable("venueId", venue.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfileId),
        StringUtils.EMPTY);

    validateUpdateVenueSnmpAgentResult(requestId, venue.getId(), snmpAgentProfileId,
        snmpAgentProfileName, false);
  }

  @Test
  void testActivateSnmpAgentProfileOnAp(Ap ap, ApSnmpAgentProfile apSnmpAgentProfile) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var snmpAgentProfileId = apSnmpAgentProfile.getId();

    messageUtil.sendWifiCfgRequest(ap.getTenant().getId(), requestId,
        CfgAction.ACTIVATE_SNMP_AGENT_PROFILE_ON_AP, userName,
        new RequestParams().addPathVariable("venueId", ap.getApGroup().getVenue().getId())
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfileId), StringUtils.EMPTY);

    validateActivateSnmpAgentProfileOnApResult(requestId, ap.getId(), snmpAgentProfileId,
        apSnmpAgentProfile.getPolicyName());
  }

  @Test
  void testDeactivateSnmpAgentProfileOnAp(Ap ap, ApSnmpAgentProfile apSnmpAgentProfile) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var snmpAgentProfileId = apSnmpAgentProfile.getId();

    var apSnmpAgent = new ApSnmpAgent();
    apSnmpAgent.setUseVenueSettings(false);
    apSnmpAgent.setEnableApSnmp(true);
    apSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);

    ap.setApSnmpAgent(apSnmpAgent);

    repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), randomTxId());

    messageUtil.sendWifiCfgRequest(ap.getTenant().getId(), requestId,
        CfgAction.DEACTIVATE_SNMP_AGENT_PROFILE_ON_AP, userName,
        new RequestParams().addPathVariable("venueId", ap.getApGroup().getVenue().getId())
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfileId), StringUtils.EMPTY);

    validateDeactivateSnmpAgentProfileOnApResult(requestId, ap.getId(), snmpAgentProfileId,
        apSnmpAgentProfile.getPolicyName());
  }

  @Test
  void testUpdateVenueApSnmpAgentProfileSettings(Ap ap, ApSnmpAgentProfile apSnmpAgentProfile) {
    var requestId = randomTxId();
    final var userName = randomName();
    final var tenantId = ap.getTenant().getId();

    var apSnmpAgent = new ApSnmpAgent();
    apSnmpAgent.setUseVenueSettings(false);
    apSnmpAgent.setEnableApSnmp(true);
    apSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);

    ap.setApSnmpAgent(apSnmpAgent);
    repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());

    var settings = new VenueApSnmpAgentProfileSettings();

    settings.setUseVenueSettings(false);

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.UPDATE_VENUE_AP_SNMP_AGENT_PROFILE_SETTINGS, userName,
        new RequestParams().addPathVariable("venueId", ap.getApGroup().getVenue().getId())
            .addPathVariable("serialNumber", ap.getId()), settings);

    messageCaptors.assertThat(
        messageCaptors.getDdccmMessageCaptor(),
        messageCaptors.getCmnCfgCollectorMessageCaptor()
    ).doesNotSendByTenant(tenantId);
    validateApSnmpAgentActivityMessages(ApiFlowNames.UPDATE_VENUE_AP_SNMP_AGENT_PROFILE_SETTINGS,
        ap.getId(), tenantId, requestId, 1);

    requestId = randomTxId();

    settings.setUseVenueSettings(true);

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.UPDATE_VENUE_AP_SNMP_AGENT_PROFILE_SETTINGS, userName,
        new RequestParams().addPathVariable("venueId", ap.getApGroup().getVenue().getId())
            .addPathVariable("serialNumber", ap.getId()), settings);

    validateUpdateVenueApSnmpAgentProfileSettingsResult(requestId, ap.getId(),
        apSnmpAgentProfile.getId(), apSnmpAgentProfile.getPolicyName());
  }

  @Test
  void testUpdateVenueApSnmpAgentProfileSettingsThrowException(Ap ap,
      ApSnmpAgentProfile apSnmpAgentProfile) {
    final var tenantId = ap.getTenant().getId();
    final var requestId1 = randomTxId();
    final var requestId2 = randomTxId();
    var userName = randomName();
    var snmpAgentProfileId = apSnmpAgentProfile.getId();

    messageUtil.sendWifiCfgRequest(ap.getTenant().getId(), requestId1,
        CfgAction.ACTIVATE_SNMP_AGENT_PROFILE_ON_AP, userName,
        new RequestParams().addPathVariable("venueId", ap.getApGroup().getVenue().getId())
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("snmpAgentProfileId", snmpAgentProfileId), StringUtils.EMPTY);

    var settings = new VenueApSnmpAgentProfileSettings();
    settings.setUseVenueSettings(true);

    assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(tenantId, requestId2,
        CfgAction.UPDATE_VENUE_AP_SNMP_AGENT_PROFILE_SETTINGS, userName,
        new RequestParams().addPathVariable("venueId", ap.getApGroup().getVenue().getId())
            .addPathVariable("serialNumber", ap.getId()), settings)).isNotNull()
        .getRootCause().isInstanceOf(WifiCommonException.class);

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId2);

    assertThat(activityCfgChangeRespMessage).isNotNull();
    assertThat(activityCfgChangeRespMessage.getPayload().getStatus()).isEqualTo(Status.FAIL);
    assertThat(activityCfgChangeRespMessage.getPayload().getError())
        .contains("\"code\":\"WIFI-20002\"")
        .contains(
            "\"message\":\"SNMP Agent profile [" + snmpAgentProfileId + "] is still bound to AP ["
                + ap.getId() + "]\"");
  }

  void validateSnmpAgentProfileResult(Action action, String tenantId, String requestId,
      SnmpAgentProfileV1_1 viewSnmpAgentProfile) {
    final var snmpAgentProfile = repositoryUtil
        .find(ApSnmpAgentProfile.class, viewSnmpAgentProfile.getId());

    var isDelete = action == Action.DELETE;

    if (isDelete) {
      assertThat(snmpAgentProfile).isNull();
    } else {
      assertThat(snmpAgentProfile).isNotNull()
          .matches(a -> viewSnmpAgentProfile.getName().equals(a.getPolicyName()))
          .matches(a -> CollectionUtils.size(a.getSnmpV2Agents()) == CollectionUtils
              .size(viewSnmpAgentProfile.getSnmpV2Agents()))
          .matches(a -> CollectionUtils.size(a.getSnmpV3Agents()) == CollectionUtils
              .size(viewSnmpAgentProfile.getSnmpV3Agents()));
    }

    OpType opType;

    String apiFlowName;

    if (action == Action.ADD) {
      opType = OpType.ADD;
      apiFlowName = ApiFlowNames.ADD_SNMP_AGENT_PROFILE_V1_1;
    } else if (action == Action.MODIFY) {
      opType = OpType.MOD;
      apiFlowName = ApiFlowNames.UPDATE_SNMP_AGENT_PROFILE_V1_1;
    } else if (isDelete) {
      opType = OpType.DEL;
      apiFlowName = ApiFlowNames.DELETE_SNMP_AGENT_PROFILE;
    } else {
      throw new UnsupportedOperationException("Unsupported action: " + action);
    }

    validateSnmpAgentProfileDdccmCfgRequest(action, tenantId, requestId, viewSnmpAgentProfile);
    validateSnmpAgentProfileCmnCfgCollectorRequest(opType, tenantId, requestId,
        viewSnmpAgentProfile);
    validateSnmpAgentProfileActivityMessages(apiFlowName, tenantId, requestId);
  }

  void validateSnmpAgentProfileDdccmCfgRequest(Action action, String tenantId, String requestId,
      SnmpAgentProfileV1_1 viewSnmpAgentProfile) {
    var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).extracting(KafkaProtoMessage::getPayload).isNotNull()
        .isNotNull();

    assertThatNoException()
        .isThrownBy(() -> assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApSnmpAgentProfile)
            .hasSize(1).filteredOn(o -> viewSnmpAgentProfile.getId().equals(o.getId()))
            .first()
            .matches(o -> o.getConfigCase() == ConfigCase.APSNMPAGENTPROFILE)
            .matches(o -> o.getAction() == action)
            .satisfies(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApSnmpAgentProfile)
            .satisfies(ap -> {
              assertThat(ap).matches(a -> ap.getId().equals(viewSnmpAgentProfile.getId()));
              if (action != Action.DELETE) {
                assertThat(ap)
                    .matches(a -> viewSnmpAgentProfile.getName().equals(a.getName()))
                    .matches(a -> tenantId.equals(a.getTenantId()))
                    .matches(a -> a.getSnmpV2AgentCount() == CollectionUtils
                        .size(viewSnmpAgentProfile.getSnmpV2Agents()))
                    .matches(a -> a.getSnmpV3AgentCount() == CollectionUtils
                        .size(viewSnmpAgentProfile.getSnmpV3Agents()));
              }
            }));
  }

  private KafkaProtoMessage<ViewmodelCollector> getCmnCfgCollectorMessage(String tenantId,
      String requestId) {
    var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();
    return cmnCfgCollectorMessage;
  }

  private void validateSnmpAgentProfileCmnCfgCollectorRequest(OpType opType, String tenantId,
      String requestId, SnmpAgentProfileV1_1 viewSnmpAgentProfile) {
    validateSnmpAgentProfileCmnCfgCollectorRequest(opType, tenantId, requestId,
        viewSnmpAgentProfile, getCmnCfgCollectorMessage(tenantId, requestId), 1, 0, 0, null, null);
  }

  @SuppressWarnings("SameParameterValue")
  private void validateUpdSnmpAgentProfileCmnCfgCollectorRequest(String tenantId, String requestId,
      SnmpAgentProfileV1_1 viewSnmpAgentProfile, int venues, int aps) {
    validateSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId,
        viewSnmpAgentProfile, getCmnCfgCollectorMessage(tenantId, requestId), 1, venues, aps, null,
        null);
  }

  @SuppressWarnings("SameParameterValue")
  private void validateUpdSnmpAgentProfileCmnCfgCollectorRequest(String tenantId, String requestId,
      SnmpAgentProfileV1_1 viewSnmpAgentProfile, int venues, int aps, String venueName, String apName) {
    validateSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId,
        viewSnmpAgentProfile, getCmnCfgCollectorMessage(tenantId, requestId), 1, venues, aps,
        venueName, apName);
  }

  @SuppressWarnings("SameParameterValue")
  private void validateUpdSnmpAgentProfileCmnCfgCollectorRequest(String tenantId, String requestId,
      SnmpAgentProfileV1_1 viewSnmpAgentProfile,
      KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage, int opSize, int venues,
      int aps) {
    validateSnmpAgentProfileCmnCfgCollectorRequest(OpType.MOD, tenantId, requestId,
        viewSnmpAgentProfile, cmnCfgCollectorMessage, opSize, venues, aps, null, null);
  }

  private void validateSnmpAgentProfileCmnCfgCollectorRequest(OpType opType, String tenantId,
      String requestId, SnmpAgentProfileV1_1 viewSnmpAgentProfile,
      KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage, int opSize, int venues, int aps,
      String venueName, String apName) {
    assertThatNoException()
        .isThrownBy(() -> assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> EsConstants.Index.AP_SNMP_AGENT_PROFILE.equals(op.getIndex()))
            .hasSize(opSize).filteredOn(op -> viewSnmpAgentProfile.getId().equals(op.getId()))
            .first()
            .matches(op -> op.getOpType() == opType)
            .satisfies(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> viewSnmpAgentProfile.getId()
                        .equals(doc.get(EsConstants.Key.ID).getStringValue()))
                    .matches(doc -> viewSnmpAgentProfile.getName()
                        .equals(doc.get(EsConstants.Key.NAME).getStringValue()))
                    .matches(doc -> doc.get("v2Agents").getListValue().getValuesCount()
                        == CollectionUtils.emptyIfNull(viewSnmpAgentProfile.getSnmpV2Agents())
                        .size())
                    .matches(doc -> doc.get("v3Agents").getListValue().getValuesCount()
                        == CollectionUtils.emptyIfNull(viewSnmpAgentProfile.getSnmpV3Agents())
                        .size())
                    .matches(doc -> doc.get("communityNames").getListValue().getValuesCount()
                        == CollectionUtils.emptyIfNull(viewSnmpAgentProfile.getSnmpV2Agents())
                        .size())
                    .matches(doc -> doc.get("userNames").getListValue().getValuesCount()
                        == CollectionUtils.emptyIfNull(viewSnmpAgentProfile.getSnmpV3Agents())
                        .size())
                    .matches(doc -> doc.get("venues").getListValue().getValuesCount() == venues)
                    .matches(doc -> doc.get("venueNames").getListValue().getValuesCount() == venues)
                    .matches(doc -> venueName == null || doc.get("venues").getListValue()
                        .getValuesList().stream().map(Value::getStructValue)
                        .map(Struct::getFieldsMap).map(map -> map.get("name").getStringValue())
                        .anyMatch(name -> name.equals(venueName)))
                    .matches(doc -> venueName == null || doc.get("venueNames").getListValue()
                        .getValuesList().stream().map(Value::getStringValue)
                        .anyMatch(name -> name.equals(venueName)))
                    .matches(doc -> doc.get("aps").getListValue().getValuesCount() == aps)
                    .matches(doc -> doc.get("apNames").getListValue().getValuesCount() == aps)
                    .matches(doc -> apName == null || doc.get("aps").getListValue()
                        .getValuesList().stream().map(Value::getStructValue)
                        .map(Struct::getFieldsMap).map(map -> map.get("name").getStringValue())
                        .anyMatch(name -> name.equals(apName)))
                    .matches(doc -> apName == null || doc.get("apNames").getListValue()
                        .getValuesList().stream().map(Value::getStringValue)
                        .anyMatch(name -> name.equals(apName)));
              }
            }));
  }

  private void validateUpdSnmpAgentProfileCmnCfgCollectorRequest(
      KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage, String requestId,
      String tenantId) {
    assertThatNoException()
        .isThrownBy(() -> {
          ViewmodelCollector payload = cmnCfgCollectorMessage.getPayload();
          assertThat(payload.getTenantId()).isEqualTo(tenantId);
          assertThat(payload.getRequestId()).isEqualTo(requestId);

          List<Operations> ops = payload.getOperationsList();
          assertThat(ops).isNotEmpty();
          long apSnmpOpCount = ops.stream()
              .filter(op -> EsConstants.Index.AP_SNMP_AGENT_PROFILE.equals(op.getIndex()))
              .count();
          assertThat(apSnmpOpCount).isEqualTo(0);
        });
  }

  private void validateSnmpAgentProfileActivityMessages(String apiFlowName, String tenantId,
      String requestId) {
    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 0)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
            .isEmpty());
  }

  private void validateActivateSnmpAgentProfileOnApResult(String requestId, String apId,
      String profileId, String profileName) {
    final var ap = repositoryUtil.find(Ap.class, apId);

    var tenantId = Objects.requireNonNull(ap).getTenant().getId();

    assertThat(ap.getApSnmpAgent()).isNotNull()
        .matches(a -> a.getEnableApSnmp().equals(true), "AP SNMP enabled status is equal")
        .extracting(ApSnmpAgent::getApSnmpAgentProfile)
        .matches(p -> p.getId().equals(profileId), "AP SNMP agent profile ID is equal");

    validateApSnmpAgentDdccmCfgRequest(requestId, tenantId, profileId, false);
    validateApSnmpAgentCmnCfgCollectorRequest(requestId, tenantId, profileId, profileName, 1);
    validateApSnmpAgentActivityMessages(ApiFlowNames.ACTIVATE_SNMP_AGENT_PROFILE_ON_AP, apId,
        tenantId, requestId);
  }

  private void validateDeactivateSnmpAgentProfileOnApResult(String requestId, String apId,
      String profileId, String profileName) {
    final var ap = repositoryUtil.find(Ap.class, apId);

    var tenantId = Objects.requireNonNull(ap).getTenant().getId();

    assertThat(ap.getApSnmpAgent()).isNotNull().matches(a -> !a.getEnableApSnmp())
        .matches(a -> a.getApSnmpAgentProfile() == null);

    validateApSnmpAgentDdccmCfgRequest(requestId, tenantId, null, false);
    validateApSnmpAgentCmnCfgCollectorRequest(requestId, tenantId, profileId, profileName, 0);
    validateApSnmpAgentActivityMessages(ApiFlowNames.DEACTIVATE_SNMP_AGENT_PROFILE_ON_AP, apId,
        tenantId, requestId);
  }

  private void validateUpdateVenueApSnmpAgentProfileSettingsResult(String requestId, String apId,
      String profileId,
      String profileName) {
    final var ap = repositoryUtil.find(Ap.class, apId);

    var tenantId = Objects.requireNonNull(ap).getTenant().getId();

    assertThat(ap.getApSnmpAgent()).isNotNull().matches(a -> a.getEnableApSnmp() == null)
        .matches(a -> a.getApSnmpAgentProfile() == null);

    validateApSnmpAgentDdccmCfgRequest(requestId, tenantId, null, true);
    validateApSnmpAgentActivityMessages(ApiFlowNames.UPDATE_VENUE_AP_SNMP_AGENT_PROFILE_SETTINGS, apId,
        tenantId, requestId);
    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId)).isNull();
  }

  void validateApSnmpAgentDdccmCfgRequest(String requestId, String tenantId, String profileId,
      boolean reset) {
    var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage).extracting(KafkaProtoMessage::getPayload).isNotNull()
        .isNotNull();
    var ddccmRequest = ddccmCfgRequestMessage.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .hasSize(2).filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp).first()
        .matches(op -> op.getAction() == Action.MODIFY)
        .satisfies(op -> assertThat(op)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
            .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
            .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
            .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .satisfies(ap -> {
          if (reset) {
            assertThat(ap).matches(a -> !a.hasSnmpAgent());
          } else {
            if (profileId == null) { // use venue settings
              assertThat(ap).matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasSnmpAgent)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getSnmpAgent)
                  .matches(a -> !a.getEnabled())
                  .matches(a -> !a.hasApSnmpAgentProfileId());
            } else {
              assertThat(ap).matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasSnmpAgent)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getSnmpAgent)
                  .matches(SnmpAgent::getEnabled)
                  .matches(SnmpAgent::hasApSnmpAgentProfileId)
                  .matches(a -> profileId.equals(a.getApSnmpAgentProfileId().getValue()));
            }
          }
        });
  }

  void validateApSnmpAgentCmnCfgCollectorRequest(String requestId, String tenantId,
      String profileId, String profileName, int expectedAps) {
    var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(msg -> msg.getTenantId().equals(tenantId))
        .matches(msg -> msg.getRequestId().equals(requestId))
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> EsConstants.Index.AP_SNMP_AGENT_PROFILE.equals(op.getIndex()))
        .hasSize(1).first()
        .matches(op -> op.getOpType() == OpType.MOD)
        .matches(op -> profileId.equals(op.getId()))
        .satisfies(op -> assertThat(op)
            .extracting(Operations::getDocMap)
            .matches(doc -> profileId
                .equals(doc.get(EsConstants.Key.ID).getStringValue()))
            .matches(doc -> profileName
                .equals(doc.get(EsConstants.Key.NAME).getStringValue()))
            .matches(doc -> doc.get("v2Agents").getListValue().getValuesCount() == 1)
            .matches(doc -> doc.get("v3Agents").getListValue().getValuesCount() == 1)
            .matches(doc -> doc.get("communityNames").getListValue().getValuesCount() == 1)
            .matches(doc -> doc.get("userNames").getListValue().getValuesCount() == 1)
            .matches(doc -> doc.get("venues").getListValue().getValuesCount() == 0)
            .matches(doc -> doc.get("venueNames").getListValue().getValuesCount() == 0)
            .matches(doc -> doc.get("apNames").getListValue().getValuesCount() == expectedAps)
            .matches(doc -> doc.get("aps").getListValue().getValuesCount() == expectedAps));
  }

  private void validateApSnmpAgentActivityMessages(String apiFlowName, String apId, String tenantId,
      String requestId) {
    validateApSnmpAgentActivityMessages(apiFlowName, apId, tenantId, requestId, 1);
  }

  private void validateApSnmpAgentActivityMessages(String apiFlowName, String apId, String tenantId,
      String requestId, int impactedDeviceCnt) {
    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == impactedDeviceCnt)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
            .satisfies(ids -> {
              if (impactedDeviceCnt == 0) {
                assertThat(ids).isEmpty();
              } else {
                assertThat(ids).first().matches(id -> id.equals(apId));
              }
            })
    );
  }

  private void validateUpdateVenueSnmpAgentResult(String requestId, String venueId,
      String snmpAgentProfileId, String snmpAgentProfileName, boolean enableApSnmp) {
    final var venue = repositoryUtil.find(Venue.class, venueId);

    var tenantId = Objects.requireNonNull(venue).getTenant().getId();

    assertThat(venue.getApSnmpAgent()).isNotNull()
        .matches(
            v -> v.getEnableApSnmp().equals(enableApSnmp),
            "AP SNMP enabled status is equal")
        .extracting(VenueSnmpAgent::getApSnmpAgentProfile)
        .matches(p -> enableApSnmp ? p.getId().equals(snmpAgentProfileId) : p == null,
            "AP SNMP agent profile ID is equal");

    var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThat(ddccmCfgRequestMessage.getPayload().getOperationsList())
        .isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .hasSize(1).first().matches(op -> op.getAction() == Action.MODIFY)
        .satisfies(op -> assertThat(op)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
            .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
            .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
            .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasSnmpAgent)
        .matches(v -> v.getSnmpAgent().getEnabled() == enableApSnmp)
        .matches(v -> v.getSnmpAgent().hasApSnmpAgentProfileId() == enableApSnmp)
        .matches(v -> !enableApSnmp || snmpAgentProfileId
            .equals(v.getSnmpAgent().getApSnmpAgentProfileId().getValue()));

    var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(msg -> msg.getTenantId().equals(tenantId))
        .matches(msg -> msg.getRequestId().equals(requestId))
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> EsConstants.Index.AP_SNMP_AGENT_PROFILE.equals(op.getIndex()))
        .hasSize(1).first()
        .matches(op -> op.getOpType() == OpType.MOD)
        .matches(op -> snmpAgentProfileId.equals(op.getId()))
        .satisfies(op -> assertThat(op)
            .extracting(Operations::getDocMap)
            .matches(doc -> snmpAgentProfileId
                .equals(doc.get(EsConstants.Key.ID).getStringValue()))
            .matches(doc -> snmpAgentProfileName
                .equals(doc.get(EsConstants.Key.NAME).getStringValue()))
            .matches(doc -> doc.get("v2Agents").getListValue().getValuesCount() == 1)
            .matches(doc -> doc.get("v3Agents").getListValue().getValuesCount() == 1)
            .matches(
                doc -> doc.get("venues").getListValue().getValuesCount() == (enableApSnmp ? 1 : 0))
            .matches(doc -> doc.get("aps").getListValue().getValuesCount() == 0)
            .matches(doc -> doc.get("communityNames").getListValue().getValuesCount() == 1)
            .matches(doc -> doc.get("userNames").getListValue().getValuesCount() == 1)
            .matches(
                doc -> doc.get("venueNames").getListValue().getValuesCount() == (enableApSnmp ? 1
                    : 0))
            .matches(doc -> doc.get("apNames").getListValue().getValuesCount() == 0));

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(msg -> msg.getStatus().equals(Status.OK))
        .matches(msg -> msg.getStep().equals(
            enableApSnmp ? ApiFlowNames.ACTIVATE_SNMP_AGENT_PROFILE_ON_VENUE
                : ApiFlowNames.DEACTIVATE_SNMP_AGENT_PROFILE_ON_VENUE))
        .extracting(ConfigurationStatus::getEventDate).isNotNull();

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(activityImpactedMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThat(activityImpactedMessage.getPayload())
        .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
        .matches(msg -> msg.getDeviceIdsCount() == 0)
        .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
        .isEmpty();
  }
}
