package com.ruckus.cloud.wifi.datahelper;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;

import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpOption82LanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.test.fixture.ApLanPortTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApModelSpecificTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ClientIsolationTestFixture;
import com.ruckus.cloud.wifi.test.fixture.EthernetPortProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.IpsecProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.LanPortAdoptionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueApModelSpecificAttributesTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueLanPortTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Optional;
import java.util.function.Consumer;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class LanPortAdoptionDataHelper {

  public record VenueLanPortData(
      VenueApModelSpecificAttributes modelSpecific,
      VenueLanPort port,
      EthernetPortProfile profile) {}

  public record ApLanPortData(
      ApModelSpecific apSpecific, ApLanPort port, EthernetPortProfile profile) {}

  public record ActivationConfig(
      SoftGreProfileLanPortActivation softGreProfileActivation,
      ClientIsolationLanPortActivation clientIsolationActivation,
      DhcpOption82LanPortActivation dhcpOption82LanPortActivation) {

    public static ActivationConfig of(SoftGreProfileLanPortActivation softGreProfileActivation) {
      return new ActivationConfig(softGreProfileActivation, null, null);
    }

    public static ActivationConfig of(SoftGreProfile profile) {
      var activation = new SoftGreProfileLanPortActivation();
      activation.setSoftGreProfile(profile);
      return new ActivationConfig(activation, null, null);
    }

    public static ActivationConfig of(SoftGreProfile softGrepProfile, IpsecProfile ipsecProfile) {
      var activation = new SoftGreProfileLanPortActivation();
      activation.setSoftGreProfile(softGrepProfile);
      activation.setIpsecProfile(ipsecProfile);
      return new ActivationConfig(activation, null, null);
    }

    public static ActivationConfig of(ClientIsolationLanPortActivation clientIsolationActivation) {
      return new ActivationConfig(null, clientIsolationActivation, null);
    }

    public static ActivationConfig of(DhcpOption82LanPortActivation dhcpOption82LanPortActivation) {
      return new ActivationConfig(null, null, dhcpOption82LanPortActivation);
    }

    public static ActivationConfig of(
        SoftGreProfileLanPortActivation softGreProfileActivation,
        ClientIsolationLanPortActivation clientIsolationActivation) {
      return new ActivationConfig(softGreProfileActivation, clientIsolationActivation, null);
    }
  }

  public static LanPortAdoption newLanPortAdoption(
      SoftGreProfileLanPortActivation softGreActivation) {
    return newLanPortAdoption(null, softGreActivation);
  }

  public static LanPortAdoption newLanPortAdoption(
      ClientIsolationLanPortActivation clientIsolationActivation) {
    return newLanPortAdoption(null, null, clientIsolationActivation, null);
  }

  public static LanPortAdoption newLanPortAdoption(
      DhcpOption82LanPortActivation dhcpOption82LanPortActivation) {
    return newLanPortAdoption(null, null, null, dhcpOption82LanPortActivation);
  }

  public static LanPortAdoption newLanPortAdoption(
      ApLanPortProfile profile, SoftGreProfileLanPortActivation softGreActivation) {
    return newLanPortAdoption(profile, softGreActivation, null, null);
  }

  public static LanPortAdoption newLanPortAdoption(
      ApLanPortProfile profile, ClientIsolationLanPortActivation clientIsolationActivation) {
    return newLanPortAdoption(profile, null, clientIsolationActivation, null);
  }

  public static LanPortAdoption newLanPortAdoption(
      ApLanPortProfile profile, DhcpOption82LanPortActivation dhcpOption82LanPortActivation) {
    return newLanPortAdoption(profile, null, null, dhcpOption82LanPortActivation);
  }

  public static LanPortAdoption newLanPortAdoption(
      ApLanPortProfile profile,
      SoftGreProfileLanPortActivation softGreActivation,
      ClientIsolationLanPortActivation clientIsolationLanPortActivation,
      DhcpOption82LanPortActivation dhcpOption82LanPortActivation) {
    var result = new LanPortAdoption();
    result.setApLanPortProfile(profile);
    result.setSoftGreActivation(softGreActivation);
    result.setClientIsolationActivation(clientIsolationLanPortActivation);
    result.setDhcpOption82Activation(dhcpOption82LanPortActivation);
    return result;
  }

  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private LanPortAdoptionServiceImpl lanPortAdoptionService;

  public Ap createAp(Venue venue) {
    var result = ApTestFixture.randomAp(venue);
    var apGroup =
        repositoryUtil.createOrUpdate(result.getApGroup(), venue.getTenant().getId(), randomTxId());
    result.setApGroup(apGroup);
    return repositoryUtil.createOrUpdate(result, venue.getTenant().getId(), randomTxId());
  }

  public Venue createVenue(Tenant tenant) {
    return repositoryUtil.createOrUpdate(
        VenueTestFixture.randomVenue(tenant), tenant.getId(), randomTxId());
  }

  public EthernetPortProfile createEthernetPortProfile(Tenant tenant, int ethernetPortProfileId) {
    return repositoryUtil.createOrUpdate(
        EthernetPortProfileTestFixture.randomEthernetPortProfile(
            tenant,
            e -> {
              e.setApLanPortId(ethernetPortProfileId);
              e.setName(randomName());
              e.setType(ApLanPortTypeEnum.TRUNK);
              e.setUntagId((short) 1);
              e.setVlanMembers("1-4094");
              e.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
            }),
        tenant.getId(),
        randomTxId());
  }

  public VenueApModelSpecificAttributes createVenueApModelSpecificAttributes(
      Venue venue, String apModel) {
    return repositoryUtil.createOrUpdate(
        VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(
            venue,
            modelSpecific -> {
              modelSpecific.setModel(apModel);
              modelSpecific.setLanPorts(new ArrayList<>());
            }),
        venue.getTenant().getId(),
        modelSpecific -> venue.getModelSpecificAttributes().add(modelSpecific));
  }

  public VenueLanPort createVenueLanPort(
      Venue venue,
      VenueApModelSpecificAttributes modelSpecific,
      String portId,
      EthernetPortProfile ethernetPortProfile) {
    return repositoryUtil.createOrUpdate(
        VenueLanPortTestFixture.randomVenueLanPort(
            venue,
            modelSpecific,
            port -> {
              port.setApLanPortProfile(ethernetPortProfile);
              port.setPortId(portId);
            }),
        venue.getTenant().getId(),
        port -> {
          modelSpecific.getLanPorts().add(port);
        });
  }

  public LanPortAdoption createLanPortAdoption(Tenant tenant, ApLanPortProfile profile) {
    return createLanPortAdoption(tenant, profile, null);
  }

  public LanPortAdoption createLanPortAdoption(
      Tenant tenant, ApLanPortProfile profile, ActivationConfig activationConfig) {
    var result =
        repositoryUtil.createOrUpdate(
            LanPortAdoptionTestFixture.randomLanPortAdoption(
                profile,
                adoption ->
                    adoption.setChecksum(lanPortAdoptionService.calculateChecksum(adoption))),
            tenant.getId());
    var config = Optional.ofNullable(activationConfig);
    config
        .map(ActivationConfig::softGreProfileActivation)
        .ifPresent(
            activation -> {
              activation.setLanPortAdoption(result);
              repositoryUtil.createOrUpdate(
                  activation, tenant.getId(), result::setSoftGreActivation);
            });
    config
        .map(ActivationConfig::clientIsolationActivation)
        .ifPresent(
            activation -> {
              activation.setLanPortAdoption(result);
              repositoryUtil.createOrUpdate(
                  activation, tenant.getId(), result::setClientIsolationActivation);
            });
    config
        .map(ActivationConfig::dhcpOption82LanPortActivation)
        .ifPresent(
            activation -> {
              activation.setLanPortAdoption(result);
              repositoryUtil.createOrUpdate(
                  activation, tenant.getId(), result::setDhcpOption82Activation);
            });
    return result;
  }

  public ClientIsolationAllowlist createClientIsolationAllowlist(Tenant tenant) {
    return repositoryUtil.createOrUpdate(
        ClientIsolationTestFixture.randomClientIsolationAllowlist(), tenant.getId(), randomTxId());
  }

  public VenueLanPortData createVenueLanPortData(
      Venue venue, String apModel, String portId, int ethernetPortProfileId) {
    var profile = createEthernetPortProfile(venue.getTenant(), ethernetPortProfileId);
    return createVenueLanPortData(venue, apModel, portId, profile);
  }

  public VenueLanPortData createVenueLanPortData(
      Venue venue, String apModel, String portId, EthernetPortProfile profile) {
    var modelSpecific =
        venue.getModelSpecificAttributes().stream()
            .filter(m -> apModel.equals(m.getModel()))
            .findAny()
            .orElseGet(() -> createVenueApModelSpecificAttributes(venue, apModel));
    var port = createVenueLanPort(venue, modelSpecific, portId, profile);
    return new VenueLanPortData(modelSpecific, port, profile);
  }

  public VenueLanPortData createVenueLanPortDataWithAdoption(
      Venue venue, String apModel, String portId, int ethernetPortProfileId) {
    return createVenueLanPortDataWithAdoption(venue, apModel, portId, ethernetPortProfileId, null);
  }

  public VenueLanPortData createVenueLanPortDataWithAdoption(
      Venue venue,
      String apModel,
      String portId,
      int ethernetPortProfileId,
      ActivationConfig activationConfig) {
    var profile = createEthernetPortProfile(venue.getTenant(), ethernetPortProfileId);
    var modelSpecific =
        venue.getModelSpecificAttributes().stream()
            .filter(m -> apModel.equals(m.getModel()))
            .findAny()
            .orElseGet(() -> createVenueApModelSpecificAttributes(venue, apModel));
    var port = createVenueLanPort(venue, modelSpecific, portId, profile);
    var adoption = createLanPortAdoption(venue.getTenant(), profile);
    var config = Optional.ofNullable(activationConfig);
    config
        .map(ActivationConfig::softGreProfileActivation)
        .ifPresent(
            activation -> {
              activation.setLanPortAdoption(adoption);
              repositoryUtil.createOrUpdate(
                  activation, venue.getTenant().getId(), adoption::setSoftGreActivation);
            });
    config
        .map(ActivationConfig::clientIsolationActivation)
        .ifPresent(
            activation -> {
              activation.setLanPortAdoption(adoption);
              repositoryUtil.createOrUpdate(
                  activation, venue.getTenant().getId(), adoption::setClientIsolationActivation);
            });
    config
        .map(ActivationConfig::dhcpOption82LanPortActivation)
        .ifPresent(
            activation -> {
              activation.setLanPortAdoption(adoption);
              repositoryUtil.createOrUpdate(
                  activation, venue.getTenant().getId(), adoption::setDhcpOption82Activation);
            });
    port.setLanPortAdoption(updateCheckSum(adoption));
    repositoryUtil.createOrUpdate(port, venue.getTenant().getId(), randomTxId());
    return new VenueLanPortData(modelSpecific, port, profile);
  }

  public SoftGreProfile createSoftGreProfile(Tenant tenant) {
    return createSoftGreProfile(tenant, t -> {});
  }

  public SoftGreProfile createSoftGreProfile(Tenant tenant, Consumer<SoftGreProfile> customizer) {
    return repositoryUtil.createOrUpdate(
        SoftGreProfileTestFixture.randomSoftGreProfile(tenant, customizer), tenant.getId());
  }

  public IpsecProfile createIpsecProfile(Tenant tenant) {
    return createIpsecProfile(tenant, t -> {});
  }

  public IpsecProfile createIpsecProfile(Tenant tenant, Consumer<IpsecProfile> customizer) {
    return repositoryUtil.createOrUpdate(
        IpsecProfileTestFixture.randomIpsecProfile(tenant, customizer), tenant.getId());
  }

  public ApLanPort createApLanPort(
      Venue venue,
      ApModelSpecific apSpecific,
      String portId,
      EthernetPortProfile ethernetPortProfile) {
    return repositoryUtil.createOrUpdate(
        ApLanPortTestFixture.randomApLanPort(
            venue,
            apSpecific,
            port -> {
              port.setApLanPortProfile(ethernetPortProfile);
              port.setPortId(portId);
            }),
        venue.getTenant().getId(),
        port -> {
          apSpecific.getLanPorts().add(port);
        });
  }

  public ApModelSpecific createApModelSpecific(Venue venue, Ap ap) {
    return repositoryUtil.createOrUpdate(
        ApModelSpecificTestFixture.randomApModelSpecific(
            venue,
            null,
            apSpecific -> {
              apSpecific.setLanPorts(new ArrayList<>());
            }),
        venue.getTenant().getId(),
        apSpecific -> {
          apSpecific.setAp(ap);
          ap.setModelSpecific(apSpecific);
          repositoryUtil.createOrUpdate(ap, venue.getTenant().getId(), randomTxId());
        });
  }

  public ApLanPortData createApLanPortData(
      Venue venue, Ap ap, String portId, int ethernetPortProfileId) {
    var profile = createEthernetPortProfile(venue.getTenant(), ethernetPortProfileId);
    return createApLanPortData(venue, ap, portId, profile);
  }

  public ApLanPortData createApLanPortData(
      Venue venue, Ap ap, String portId, EthernetPortProfile profile) {
    var apSpecific =
        ap.getModelSpecific() == null ? createApModelSpecific(venue, ap) : ap.getModelSpecific();
    var port = createApLanPort(venue, apSpecific, portId, profile);
    return new ApLanPortData(apSpecific, port, profile);
  }

  public ApLanPortData createApLanPortDataWithAdoption(
      Venue venue, Ap ap, String portId, int ethernetPortProfileId) {
    return createApLanPortDataWithAdoption(venue, ap, portId, ethernetPortProfileId, null);
  }

  public ApLanPortData createApLanPortDataWithAdoption(
      Venue venue,
      Ap ap,
      String portId,
      int ethernetPortProfileId,
      ActivationConfig activationConfig) {
    var profile = createEthernetPortProfile(venue.getTenant(), ethernetPortProfileId);
    var apSpecific =
        ap.getModelSpecific() == null ? createApModelSpecific(venue, ap) : ap.getModelSpecific();
    var port = createApLanPort(venue, apSpecific, portId, profile);
    var adoption = createLanPortAdoption(venue.getTenant(), profile, activationConfig);
    port.setLanPortAdoption(updateCheckSum(adoption));
    repositoryUtil.createOrUpdate(port, venue.getTenant().getId(), randomTxId());
    return new ApLanPortData(apSpecific, port, profile);
  }

  private LanPortAdoption updateCheckSum(LanPortAdoption adoption) {
    adoption.setChecksum(lanPortAdoptionService.calculateChecksum(adoption));
    return repositoryUtil.createOrUpdate(adoption, adoption.getTenant().getId(), randomTxId());
  }
}
