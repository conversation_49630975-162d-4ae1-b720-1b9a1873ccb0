package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceRule;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO venue (id, tenant) VALUES ('b1a2c59f92d243cba7ca5b4bfe83c289', '6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO ap_group (id, tenant, venue, is_default) VALUES
        ('753755ebcac84ce798ea5f3ee11b5731', '6700bc51acf84c4aa9510df2ca00b5f4', 'b1a2c59f92d243cba7ca5b4bfe83c289', TRUE),
        ('243280ce025f4def9ac45626271a077f', '6700bc51acf84c4aa9510df2ca00b5f4', 'b1a2c59f92d243cba7ca5b4bfe83c289', FALSE);
    INSERT INTO ap (id, tenant, ap_group) VALUES
        ('983518622414', '6700bc51acf84c4aa9510df2ca00b5f4', '753755ebcac84ce798ea5f3ee11b5731'),
        ('454643635712', '6700bc51acf84c4aa9510df2ca00b5f4', '243280ce025f4def9ac45626271a077f');
    INSERT INTO multicast_dns_proxy_service_profile (id, tenant) VALUES ('34ffc50c45284c0a8a3ce5809d9c1c76', '6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO multicast_dns_proxy_service_profile_ap (id, tenant, ap, multicast_dns_proxy_service_profile) VALUES
        ('d888fd374a974e548af80a7ecbc74230', '6700bc51acf84c4aa9510df2ca00b5f4', '983518622414', '34ffc50c45284c0a8a3ce5809d9c1c76'),
        ('e619cccb6bd74075ba0b220611969aab', '6700bc51acf84c4aa9510df2ca00b5f4', '454643635712', '34ffc50c45284c0a8a3ce5809d9c1c76');
    INSERT INTO multicast_dns_proxy_service_rule (id, tenant, multicast_dns_proxy_service_profile, rule_index, service) VALUES
        ('501c9b901e9c48eaae5ab1e9d42979c1', '6700bc51acf84c4aa9510df2ca00b5f4', '34ffc50c45284c0a8a3ce5809d9c1c76', 0, 'AIRDISK'),
        ('06bb3825b6454978a48d783ed4f2d1f0', '6700bc51acf84c4aa9510df2ca00b5f4', '34ffc50c45284c0a8a3ce5809d9c1c76', 0, 'AIRPLAY');
    """)
class MulticastDnsProxyServiceRuleRepositoryTest {
  private static final String TENANT_ID = "6700bc51acf84c4aa9510df2ca00b5f4";
  private static final String MULTICAST_DNS_PROXY_SERVICE_PROFILE_ID = "34ffc50c45284c0a8a3ce5809d9c1c76";
  private static final List<String> MULTICAST_DNS_PROXY_SERVICE_RULE_ID = Arrays.asList("06bb3825b6454978a48d783ed4f2d1f0", "501c9b901e9c48eaae5ab1e9d42979c1");

  @Autowired
  private MulticastDnsProxyServiceRuleRepository repository;

  @Test
  void findByTenantIdAndMulticastDnsProxyServiceProfileId() {
    assertThat(repository.findByTenantIdAndMulticastDnsProxyServiceProfileId(TENANT_ID, MULTICAST_DNS_PROXY_SERVICE_PROFILE_ID))
            .isNotNull()
            .hasSize(2)
            .extracting(MulticastDnsProxyServiceRule::getId)
            .containsAll(MULTICAST_DNS_PROXY_SERVICE_RULE_ID);
  }
}
