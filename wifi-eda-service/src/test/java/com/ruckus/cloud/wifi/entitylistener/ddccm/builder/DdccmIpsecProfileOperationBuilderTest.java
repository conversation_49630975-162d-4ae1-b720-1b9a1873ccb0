package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.acx.ddccm.protobuf.wifi.CcmIpsecProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.IpsecAdvancedOptionEnableEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelUsageTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class DdccmIpsecProfileOperationBuilderTest {

  @SpyBean
  private DdccmIpsecProfileOperationBuilder unit;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  public void testAddIpsecProfile() {
    IpsecProfile profile = Generators.ipsecProfile().generate();
    List<Operation> operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasCcmIpsecProfile)
        .allSatisfy(operation -> {
          validate(profile, operation.getCcmIpsecProfile());
          assertEquals(CcmIpsecProfile.IpMode.IPV4, operation.getCcmIpsecProfile().getIpMode());
        });
  }

  @Test
  public void testAddIpsecProfileWithEmptyProposals() {
    IpsecProfile profile = Generators.ipsecProfile().generate();
    profile.getIkeSecurityAssociation().setIkeProposals(null);
    profile.getEspSecurityAssociation().setEspProposals(null);
    List<Operation> operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasCcmIpsecProfile)
        .allSatisfy(operation -> validate(profile, operation.getCcmIpsecProfile()));
  }

  @Test
  public void testAddIpsecProfileWithNullServerAddress() {
    IpsecProfile profile = Generators.ipsecProfile().generate();
    profile.setServerAddress(null);
    List<Operation> operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasCcmIpsecProfile)
        .allSatisfy(operation -> {
          validate(profile, operation.getCcmIpsecProfile());
          assertEquals(CcmIpsecProfile.IpMode.IPV4, operation.getCcmIpsecProfile().getIpMode());
        });
  }

  @Test
  public void testAddIpsecProfileWithEmptyServerAddress() {
    IpsecProfile profile = Generators.ipsecProfile().generate();
    profile.setServerAddress("");
    List<Operation> operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasCcmIpsecProfile)
        .allSatisfy(operation -> {
          validate(profile, operation.getCcmIpsecProfile());
          assertEquals(CcmIpsecProfile.IpMode.IPV4, operation.getCcmIpsecProfile().getIpMode());
        });
  }

  @Test
  public void testAddIpsecProfileWithTunnelUsage() {
    IpsecProfile profile = Generators.ipsecProfile().generate();
    profile.setTunnelUsageType(TunnelUsageTypeEnum.VXLAN_GPE);
    List<Operation> operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
            .hasSize(1)
            .allMatch(Operation::hasCcmIpsecProfile)
            .allSatisfy(operation -> {
              validate(profile, operation.getCcmIpsecProfile());
              assertEquals(CcmIpsecProfile.TunnelMode.VXLAN_GPE, operation.getCcmIpsecProfile().getTunnelMode());
            });
  }

  @Test
  public void testAddIpsecProfileWithIPv6ServerAddress() {
    IpsecProfile profile = Generators.ipsecProfile().generate();
    profile.setServerAddress("1234::abcd");
    List<Operation> operations = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
    assertThat(operations)
      .hasSize(1)
      .allMatch(Operation::hasCcmIpsecProfile)
      .allSatisfy(operation -> {
        validate(profile, operation.getCcmIpsecProfile());
        assertEquals(CcmIpsecProfile.IpMode.IPV6, operation.getCcmIpsecProfile().getIpMode());
      });
  }

  private void validate(IpsecProfile src, CcmIpsecProfile config) {
    assertEquals(src.getId(), config.getId());
    assertEquals(src.getAuthType().ordinal(), config.getAuthentication().getType().ordinal());
    assertEquals(src.getPreSharedKey(), config.getAuthentication().getPreSharedKey().getValue());
    if (src.getServerAddress() == null) {
      assertEquals("", config.getServerAddress());
    } else {
      assertEquals(src.getServerAddress(), config.getServerAddress());
    }
    if (src.getTunnelUsageType() == TunnelUsageTypeEnum.SOFT_GRE) {
      assertEquals(CcmIpsecProfile.TunnelMode.SOFT_GRE, config.getTunnelMode());
    } else {
      assertEquals(CcmIpsecProfile.TunnelMode.VXLAN_GPE, config.getTunnelMode());
    }
    if (src.getIkeSecurityAssociation().getIkeProposals() != null) {
      assertEquals(src.getIkeSecurityAssociation().getIkeProposalType().ordinal(),
          config.getSecurityAssociation().getIkeProposalType());
      assertEquals(src.getIkeSecurityAssociation().getIkeProposals().size(),
          config.getSecurityAssociation().getIkeProposalCount());
    }
    if (src.getEspSecurityAssociation().getEspProposals() != null) {
      assertEquals(src.getIkeSecurityAssociation().getIkeProposalType().ordinal(),
          config.getSecurityAssociation().getIkeProposalType());
      assertEquals(src.getEspSecurityAssociation().getEspProposals().size(),
          config.getSecurityAssociation().getEspProposalCount());
    }

    var srcAdvanced = src.getAdvancedOption();
    var configAdvanced = config.getAdvancedOptions();
    assertEquals(srcAdvanced.getDhcpOpt43Subcode(), configAdvanced.getDhcpOption43Subcode());
    assertEquals(srcAdvanced.getRetryLimit(), configAdvanced.getRetryLimit());
    assertEquals(srcAdvanced.getReplayWindow(), configAdvanced.getReplayWindow());
    assertEquals(srcAdvanced.getIpcompEnable() == IpsecAdvancedOptionEnableEnum.ENABLED,
        configAdvanced.getIpCompressionEnabled());
    assertEquals(srcAdvanced.getEnforceNatt() == IpsecAdvancedOptionEnableEnum.ENABLED,
        configAdvanced.getForceNattEnabled());
    assertEquals(srcAdvanced.getDpdDelay(), configAdvanced.getDeadPeerDetectionDelay());
    assertEquals(srcAdvanced.getKeepAliveInterval(), configAdvanced.getNattKeepAliveInterval());
    assertEquals(srcAdvanced.getFailoverRetryPeriod(), configAdvanced.getFailoverRetryPeriod());
    assertEquals(srcAdvanced.getFailoverRetryInterval(), configAdvanced.getFailoverRetryInterval());
    assertEquals(srcAdvanced.getFailoverMode().ordinal(),
        configAdvanced.getFailoverMode().ordinal());
    assertEquals(srcAdvanced.getFailoverPrimaryCheckInterval(),
        configAdvanced.getFailoverPrimaryCheckInterval());

    assertEquals((int) src.getIkeRekeyTime(), config.getInternetKeyExchange().getRekeyTime());
    assertEquals(unit.toTimeUnit(src.getIkeRekeyTimeUnit()),
        config.getInternetKeyExchange().getTimeUnit());
    assertEquals((int) src.getEspRekeyTime(),
        config.getEncapsulatingSecurityPayload().getRekeyTime());
    assertEquals(unit.toTimeUnit(src.getEspRekeyTimeUnit()),
        config.getEncapsulatingSecurityPayload().getTimeUnit());
  }
}