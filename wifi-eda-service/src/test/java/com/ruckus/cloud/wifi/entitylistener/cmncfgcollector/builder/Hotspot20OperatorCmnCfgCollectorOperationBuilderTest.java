package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20FriendlyName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20Operator;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.txChangesReader;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.core.model.Identifiable;
import com.ruckus.cloud.wifi.eda.conf.WifiBaseEntityFieldNames;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20FriendlyName;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Hotspot20FriendlyNameLanguageEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.repository.Hotspot20NetworkRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesImpl;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class Hotspot20OperatorCmnCfgCollectorOperationBuilderTest {

  @MockBean
  private Hotspot20NetworkRepository hotspot20NetworkRepository;

  @SpyBean
  private Hotspot20OperatorCmnCfgCollectorOperationBuilder hotspot20OperatorCmnCfgCollectorOperationBuilder;

  @Test
  void testGetEntityClass() {
    assertThat(hotspot20OperatorCmnCfgCollectorOperationBuilder.entityClass())
      .isEqualTo(Hotspot20Operator.class);
  }

  @Test
  void testGetIndex() {
    assertThat(hotspot20OperatorCmnCfgCollectorOperationBuilder.index()).isEqualTo(
      Index.HOTSPOT20_OPERATOR_INDEX_NAME);
  }

  @Nested
  class TestBuildConfig {

    @Test
    public void givenEntityActionIsDelete() {
      Operations operations = hotspot20OperatorCmnCfgCollectorOperationBuilder.build(
        new TxEntity<>(Generators.hotspot20Operator().generate(), EntityAction.DELETE), emptyTxChanges()).get(0);

      assertThat(operations.getDocCount())
        .isZero();
    }

    @Test
    public void givenAddOperator(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20Operator hotspot20Operator = Generators.hotspot20Operator()
        .setTenant(always(tenant))
        .generate();

      hotspot20OperatorCmnCfgCollectorOperationBuilder.config(builder, hotspot20Operator, EntityAction.ADD);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20Operator, docMap, Collections.EMPTY_LIST);
    }
  }

  static class IdentifiableImpl implements Identifiable {

    String id;

    IdentifiableImpl(String id) {
      this.id = id;
    }

    @Override
    public String getId() {
      return id;
    }
  }

  @Nested
  class TestHasModification {

    @Test
    public void givenEntityActionIsNotModify() {
      assertThat(hotspot20OperatorCmnCfgCollectorOperationBuilder
        .hasChanged(new NewTxEntity<>(hotspot20Operator().generate()), emptyTxChanges()))
        .isTrue();
    }

    @Test
    public void givenUpdateOperator(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20Operator hotspot20Operator = Generators.hotspot20Operator()
        .setTenant(always(tenant))
        .generate();

      hotspot20OperatorCmnCfgCollectorOperationBuilder.config(builder, hotspot20Operator, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20Operator, docMap, Collections.EMPTY_LIST);
    }

    @Test
    public void givenUpdateOperatorWithNetworkIds(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20Operator hotspot20Operator = Generators.hotspot20Operator()
          .setId(defaultIdGenerator())
          .setTenant(always(tenant))
          .generate();
      List<Identifiable> networkIds = defaultIdGenerator().toListGenerator(3).generate()
          .stream().map(id -> (Identifiable) new IdentifiableImpl(id)).toList();

      when(hotspot20NetworkRepository.findByTenantIdAndHotspot20SettingsOperatorId(eq(tenant.getId()),
          eq(hotspot20Operator.getId())))
        .thenReturn(networkIds);

      hotspot20OperatorCmnCfgCollectorOperationBuilder.config(builder, hotspot20Operator, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20Operator, docMap, networkIds.stream().map(Identifiable::getId).toList());
    }

    @Test
    public void givenPropertiesOrFriendlyNameEntity() {
      hotspot20OperatorCmnCfgCollectorOperationBuilder.setBaseEntityFieldNames(new WifiBaseEntityFieldNames());

      com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator hotspot20Operator = Generators.hotspot20Operator()
        .setFriendlyNames(hotspot20FriendlyName()
          .setLanguage(options(Arrays.stream(Hotspot20FriendlyNameLanguageEnum.values()).collect(
            collectingAndThen(toList(), l-> {
              Collections.shuffle(l);
              return l;
            })).stream().toList()))
          .toListGenerator(Hotspot20FriendlyNameLanguageEnum.values().length))
        .generate();

      TxChanges txChanges = new TxChangesImpl(null);
      hotspot20Operator.getFriendlyNames().stream().forEach(friendlyName -> {
        txChanges.add(friendlyName, Set.of("name"),
          options(EntityAction.ADD, EntityAction.MODIFY, EntityAction.DELETE).setRandom(true).generate());
      });

      assertThat(hotspot20OperatorCmnCfgCollectorOperationBuilder
          .hasChanged(new ModifiedTxEntity<>(hotspot20Operator, Set.of("updatedDate")),
        txChangesReader(txChanges)))
        .isFalse();
      assertThat(hotspot20OperatorCmnCfgCollectorOperationBuilder
          .hasChanged(new ModifiedTxEntity<>(hotspot20Operator, Set.of("domainNames", "updatedDate")),
        emptyTxChanges()))
        .isTrue();
    }
  }

  private void validateResult(Hotspot20Operator hotspot20Operator, Map<String, Value> docMap,
      List<String> wifiNetworkId) {
    assertThat(docMap.get(Key.ID))
      .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
      .extracting(p -> p.getStringValue())
      .isEqualTo(hotspot20Operator.getId());

    assertThat(docMap.get(Key.NAME))
      .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
      .extracting(p -> p.getStringValue())
      .isEqualTo(hotspot20Operator.getName());

    assertThat(docMap.get(Key.TENANT_ID))
      .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
      .extracting(p -> p.getStringValue())
      .isEqualTo(hotspot20Operator.getTenant().getId());

    assertThat(docMap.get(Key.DOMAIN_NAMES))
      .matches(doc -> doc.getKindCase() == KindCase.LIST_VALUE)
      .extracting(p -> p.getListValue().getValuesList().stream().map(Value::getStringValue).toList())
      .isEqualTo(hotspot20Operator.getDomainNames());

    assertThat(docMap.get(Key.FRIENDLY_NAMES))
      .matches(doc -> doc.getKindCase() == KindCase.LIST_VALUE)
      .isNotNull();

    assertThat(docMap.get(Key.FRIENDLY_NAMES).getListValue().getValuesList().stream().map(Value::getStructValue).toList())
      .isNotNull()
      .allSatisfy(fn -> assertThat(fn.getFieldsMap()).matches(map ->
        hotspot20Operator.getFriendlyNames().stream().map(Hotspot20FriendlyName::getLanguage)
          .filter(language -> language.name().equals(map.get(Key.LANGUAGE).getStringValue()))
          .findAny()
          .isPresent()
        ));

    assertThat(docMap.get(Key.WIFI_NETWORK_IDS))
        .matches(doc -> doc.getKindCase() == KindCase.LIST_VALUE)
        .extracting(p -> p.getListValue().getValuesList().stream().map(Value::getStringValue).toList())
        .isEqualTo(wifiNetworkId);
  }
}
