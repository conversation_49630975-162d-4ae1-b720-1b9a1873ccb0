package com.ruckus.cloud.wifi.integration.template.networkactivation;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.venueWifiNetwork;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertNetworkVenueSoftly;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.AaaType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetwork;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("NetworkActivationTemplateTest")
@WifiIntegrationTest
class ConsumeActivateWifiNetworkTemplateOnVenueRequestTest {

  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private ExtendedMessageUtil messageUtil;

  void validateResult(String tenantId, String requestId, String venueId, String networkId,
      String apiAction) {
    final var revision = revisionService.changes(requestId, tenantId,
        ConfigRequestHandler.apiActionToFlowName(apiAction));
    final var networkVenueId = revision.getNewEntities().stream()
        .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
        .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
    final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);

    assertThat(networkVenue)
        .isNotNull()
        .matches(nv -> Objects.equals(nv.getNetwork().getId(), networkId))
        .matches(nv -> Objects.equals(nv.getVenue().getId(), venueId));

    if (NetworkTypeEnum.GUEST.equals(networkVenue.getNetwork().getType())) {
      assertThat(networkVenue.getVenuePortal())
          .isNotNull();
    }

    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(wifiCfgChangeMessage).isNotNull()
        .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(wifiCfgChangeMessage.getPayload())
            .satisfies(msg -> assertSoftly(softly -> {
              softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
              softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
            }))
            .extracting(WifiConfigChange::getOperationList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
            .satisfies(ops -> assertThat(ops)
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .filteredOn(
                    op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                .as("The ADD NetworkVenue operation count should be 1")
                .hasSize(1)
                .singleElement()
                .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                .satisfies(assertNetworkVenueSoftly(networkId, venueId))));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
            .hasSize(1)
            .singleElement()
            .matches(op -> op.getOpType() == OpType.ADD)
            .matches(op -> networkVenueId.equals(op.getId()))
            .extracting(Operations::getDocMap)
            .matches(doc -> networkVenueId.equals(doc.get(Key.ID).getStringValue()))
            .matches(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
            .matches(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
            .matches(doc -> networkId.equals(doc.get(Key.NETWORK_ID).getStringValue()))
            .matches(doc -> venueId.equals(doc.get(Key.VENUE_ID).getStringValue()))
            .matches(doc -> doc.get(Key.VENUE_NAME).getStringValue() != null));

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiAction))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operation.class::cast)
            .allSatisfy(op -> assertThat(op)
                .extracting(Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                    String.format("The value of `requestId` field in CommonInfo should be [%s]",
                        requestId))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                    String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                        tenantId))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                    String.format("The value of `sender` field in CommonInfo should be [%s]",
                        ServiceType.WIFI_SERVICE)))
            .satisfies(op -> assertSoftly(softly -> {
              softly.assertThat(op)
                  .filteredOn(Operation::hasWlanVenue)
                  .describedAs("The count of WlanVenue operations should be 1")
                  .hasSize(1)
                  .singleElement()
                  .matches(wlanVenueOp -> wlanVenueOp.getAction() == Action.ADD,
                      String.format(
                          "The value of `action` field in WlanVenue operation should be %s",
                          Action.ADD))
                  .matches(wlanVenueOp -> networkVenueId.equals(wlanVenueOp.getId()),
                      String.format("The value of `id` field in WlanVenue operation should be %s",
                          networkVenueId))
                  .extracting(Operation::getWlanVenue)
                  .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                    alsoSoftly.assertThat(wlanVenue.getWlanId())
                        .isEqualTo(networkId);
                    alsoSoftly.assertThat(wlanVenue.getVenueId())
                        .isEqualTo(venueId);
                  }));
            })));
  }

  @Nested
  class GivenOpenNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String apGroupId;

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupPersistedInDb(@Template @OpenNetwork Network network,
        @Template Venue venue, @Template @DefaultApGroup ApGroup apGroup) {
      networkId = network.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
    }

    @Payload
    private VenueWifiNetwork payload() {
      return venueWifiNetwork().generate();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), venueId, networkId,
          CfgAction.ACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE.key());
    }

    @Nested
    class WithMacRegistration {

      @BeforeEach
      void givenOpenNetworkWithMacRegistrationPersistedInDb(@Template @OpenNetwork Network network,
          @Template Venue venue,
          @Template @DefaultApGroup ApGroup apGroup) {
        network.getWlan().setMacRegistrationListId(randomId());
        network.getWlan().setMacAddressAuthentication(true);
        repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());
      }

      @Payload
      private VenueWifiNetwork payload() {
        return venueWifiNetwork().generate();
      }

      @ApiAction.RequestParams
      private RequestParams requestParams() {
        return new RequestParams().addPathVariable("venueId", venueId)
            .addPathVariable("wifiNetworkId", networkId);
      }

      @Test
      @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE)
      void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
        final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(txCtx);
        assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(Operation.class::cast)
            .filteredOn(Operation::hasWlanVenue).first().matches(operation ->
                operation.getWlanVenue().getAuthenticationServerId()
                    .equals(txCtx.getTenant() + "-Auth-Radius-AAA") && operation.getWlanVenue()
                    .getAuthAaa().getThroughController() && operation.getWlanVenue().getAuthAaa()
                    .getType().equals(AaaType.RADIUS));
      }
    }
  }

  @Nested
  class GivenOweTransitionNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String apGroupId;

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupPersistedInDb(Tenant tenant,
        @Template Venue venue, @Template @DefaultApGroup ApGroup apGroup) {
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      Network master = Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .setIsTemplate(alwaysTrue())
          .generate();
      Network slave = Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .setIsTemplate(alwaysTrue())
          .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setIsOweMaster(true);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setOwePairNetworkId(
          slave.getId());
      master = repositoryUtil.createOrUpdate(master, master.getTenant().getId(), randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setOwePairNetworkId(
          master.getId());
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setIsOweMaster(false);
      slave = repositoryUtil.createOrUpdate(slave, slave.getTenant().getId(), randomTxId());

      networkId = master.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
    }

    @Payload
    private VenueWifiNetwork payload() {
      return venueWifiNetwork().generate();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateOweTransitionResult(txCtx.getTenant(), txCtx.getTxId(),
          CfgAction.ACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE.key(), venueId, networkId);
    }

    void validateOweTransitionResult(String tenantId, String requestId, String apiAction,
        String venueId, String networkId) {
      final var revision = revisionService.changes(requestId, tenantId,
          ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueId = revision.getNewEntities().stream()
          .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
          .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
      assertThat(networkVenue.getOweTransWlanId()).isNotNull();
      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                  .as("The ADD NetworkVenue operation count should be 2")
                  .hasSize(2)));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(apiAction))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .satisfies(op -> assertSoftly(softly -> {
                softly.assertThat(op)
                    .filteredOn(Operation::hasWlanVenue)
                    .describedAs("The count of WlanVenue operations should be 2")
                    .hasSize(2);
              })));
    }

  }

  @Nested
  class GivenGuestNetworkPersistedInDb {

    private String networkId;
    private String venueId;

    @BeforeEach

    void givenGuestNetworkAndDefaultApGroupPersistedInDb(
        @Template @GuestNetwork com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork network,
        @Template Venue venue,
        @Template @DefaultApGroup ApGroup apGroup) {
      networkId = network.getId();
      venueId = venue.getId();
      sendActivatePortalServiceTemplateProfileOnNetworkTemplate(network.getTenant().getId(),
          networkId);
    }

    @Payload
    private VenueWifiNetwork payload() {
      return venueWifiNetwork().generate();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), venueId, networkId,
          CfgAction.ACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE.key());
    }

    private void sendActivatePortalServiceTemplateProfileOnNetworkTemplate(String tenantId, String networkId) {
      WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .apiAction(CfgAction.ACTIVATE_PORTAL_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE)
          .requestParams(new RequestParams().addPathVariable("wifiNetworkTemplateId", networkId)
              .addPathVariable("portalServiceProfileTemplateId", "portalServiceProfileTemplateId")).build();
      messageUtil.sendWifiCfgRequest(wifiCfgRequest);
    }
  }
}
