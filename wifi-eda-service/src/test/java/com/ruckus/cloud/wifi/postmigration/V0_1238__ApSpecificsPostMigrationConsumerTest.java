package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.postmigration.V0_1238__ApSpecificsPostMigrationConsumer.JOB_NAME;
import static com.ruckus.cloud.wifi.postmigration.V0_1238__ApSpecificsPostMigrationConsumer.REDIS_KEY_AP_SPECIFICS_MIGRATION;
import static com.ruckus.cloud.wifi.postmigration.V0_1238__ApSpecificsPostMigrationConsumer.ROUTINE_JOB_SCHEDULE_TIME;
import static com.ruckus.cloud.wifi.test.Assertions.argThat;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.client.kairos.KairosApiClient;
import com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RegistrationStateEnum;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.redis.service.RedisClientService;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class V0_1238__ApSpecificsPostMigrationConsumerTest {

  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private V0_1238__ApSpecificsPostMigrationConsumer postMigrationConsumer;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private ApRepository apRepository;
  @Autowired
  private KairosApiClient kairosApiClient;
  @Autowired
  private RedisClientService redisClientService;

  @Nested
  class WhenRunPostMigration {

    @BeforeEach
    void beforeEach() {
      final var tenants =
          Stream.generate(() -> TenantTestFixture.randomTenant(t -> {
              }))
              .limit(5)
              .peek(tenant -> repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId()))
              .collect(Collectors.toList());
      Collections.shuffle(tenants);
      var apVersion = ApVersionTestFixture.recommendedApVersion("6.2.3.103.1234", a -> {
      });
      repositoryUtil.createOrUpdate(apVersion, apVersion.getId(), randomTxId());
      List<String> apTenantIds = tenants.stream()
          .limit(3)
          .map(VenueTestFixture::randomVenue)
          .peek(venue -> {
            venue.setWifiFirmwareVersion(apVersion);
            venue.setCountryCode("ES");
            repositoryUtil.createOrUpdate(
                venue, venue.getTenant().getId(), randomTxId());
          })
          .map(ApGroupTestFixture::randomApGroup)
          .peek(apGroup -> repositoryUtil.createOrUpdate(
              apGroup, apGroup.getTenant().getId(), randomTxId()))
          .map(ApTestFixture::randomAp)
          .peek(ap -> {
            ap.setRegistrationState(RegistrationStateEnum.APPROVED);
            repositoryUtil.createOrUpdate(
                    ap, ap.getTenant().getId(), randomTxId())
                .setRegistrationState(RegistrationStateEnum.APPROVED);
          })
          .map(ap -> ap.getTenant().getId())
          .toList();
    }

    @Test
    void thenRegisterAsyncJobForEachTenant() throws Exception {
      final var expectedTenantIds = new HashSet<>(
          apRepository.findAllDistinctTenantIdsByProblematicVenueWifiFirmwareVersionAndCountryCodes());
      doReturn(false)
          .when(kairosApiClient).hasScheduleJob(JOB_NAME);
      reset(kairosApiClient);
      postMigrationConsumer.run(null);
      // Get the elements from the list
      var cacheResult = new ArrayList<>();
      var tenantId = redisClientService.lindex(REDIS_KEY_AP_SPECIFICS_MIGRATION, 0);
      while (tenantId != null) {
        cacheResult.add(tenantId);
        redisClientService.lremove(REDIS_KEY_AP_SPECIFICS_MIGRATION, 0, tenantId);
        tenantId = redisClientService.lindex(REDIS_KEY_AP_SPECIFICS_MIGRATION, 0);
      }
      // Verify that three elements are popped
      assertEquals(3, cacheResult.size());
      verify(kairosApiClient, never())
          .deleteScheduleJob(JOB_NAME);
      verify(kairosApiClient, times(1))
          .createScheduleJob(eq(JOB_NAME), argThat(req -> assertThat(req)
              .isNotNull()
              .satisfies(request -> assertSoftly(softly -> {
                softly.assertThat(request.getScheduleTime()).isEqualTo(ROUTINE_JOB_SCHEDULE_TIME);
                softly.assertThat(request.getTenantId()).isEqualTo(JOB_NAME);
                softly.assertThat(request.getTimeZone()).isEqualTo(TimeZone.getDefault().getID());
                softly.assertThat(request.getKafkaTarget()).isNotNull()
                    .satisfies(kafkaTarget -> assertSoftly(alsoSoftly -> {
                      alsoSoftly.assertThat(kafkaTarget.getHeader())
                          .extractingByKey(WifiCommonHeader.WIFI_REQUEST_ID).isNotNull();
                      alsoSoftly.assertThat(kafkaTarget.getHeader())
                          .extractingByKey(WifiCommonHeader.WIFI_TENANT_ID).isEqualTo(JOB_NAME);
                      alsoSoftly.assertThat(kafkaTarget.getTopicName()).isNotEmpty();
                      alsoSoftly.assertThat(kafkaTarget.getKey()).isNotEmpty();
                      alsoSoftly.assertThat(kafkaTarget.getData()).isNotEmpty()
                          .extractingByKey("apSpecificsPostMigrationJob")
                          .isNotNull();
                    }));
              }))));
      verify(kairosApiClient, never())
          .updateScheduleJob(eq(JOB_NAME), any(RegisterKairosRequest.class));
      assertEquals(expectedTenantIds.size(), 3);
      reset(redisClientService);
    }
  }
}
