package com.ruckus.cloud.wifi.integration.clientisolation;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("ClientIsolationProfileTest")
@FeatureFlag(
    enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
@WifiIntegrationTest
class ConsumeDeleteClientIsolationProfileRequestTest {

  @Autowired private LanPortAdoptionDataHelper dataHelper;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private ExtendedMessageUtil messageUtil;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.DELETE_CLIENT_ISOLATION_PROFILE)
  class DeleteNoUsedProfile {
    private String profileId;

    @BeforeEach
    void prepareData(ClientIsolationAllowlist profile) {
      profileId = profile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("clientIsolationProfileId", profileId);
    }

    @Test
    void shouldPass() {
      verifyDdccmForUnusedProfileDeletion(profileId);
      verifyCmnConfigForUnusedProfileDeletion(profileId);
      verifyActivityForUnusedProfileDeletion();
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_CLIENT_ISOLATION_PROFILE)
  class DeleteNoUsedProfileWithUnusedAdoptions {
    private String profileId;
    private String adoptionId;

    @BeforeEach
    void prepareData(ClientIsolationAllowlist profile) {
      profileId = profile.getId();
      var activation = new ClientIsolationLanPortActivation();
      activation.setClientIsolationAllowlist(profile);
      var adoption =
          dataHelper.createLanPortAdoption(
              profile.getTenant(),
              dataHelper.createEthernetPortProfile(profile.getTenant(), 777),
              of(activation));
      adoptionId = adoption.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("clientIsolationProfileId", profileId);
    }

    @Test
    void shouldPass() {
      verifyDdccmForUnusedProfileDeletionWithUnusedAdoptions(profileId, adoptionId);
      verifyCmnConfigForUnusedProfileDeletion(profileId);
      verifyActivityForUnusedProfileDeletion();
    }
  }

  @Nested
  class DeleteProfileButVenueLanPortUsed {
    private String profileId;
    private String tenantId;

    @BeforeEach
    void prepareData(ClientIsolationAllowlist profile, Venue venue) {
      profileId = profile.getId();
      tenantId = profile.getTenant().getId();
      var activation = new ClientIsolationLanPortActivation();
      activation.setClientIsolationAllowlist(profile);
      dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-01", "1", 777, of(activation));
    }

    @Test
    void shouldPass() {
      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenantId,
                      txCtxExtension.getRequestId(),
                      CfgAction.DELETE_CLIENT_ISOLATION_PROFILE,
                      randomName(),
                      new RequestParams().addPathVariable("clientIsolationProfileId", profileId),
                      ""))
          .isNotNull()
          .getRootCause()
          .isNotNull()
          .isInstanceOf(ObjectInUseException.class);
    }
  }

  @Nested
  class DeleteProfileButApLanPortUsed {
    private String profileId;
    private String tenantId;

    @BeforeEach
    void prepareData(ClientIsolationAllowlist profile, Venue venue, Ap ap) {
      profileId = profile.getId();
      tenantId = profile.getTenant().getId();
      var activation = new ClientIsolationLanPortActivation();
      activation.setClientIsolationAllowlist(profile);
      dataHelper.createApLanPortDataWithAdoption(venue, ap, "1", 777, of(activation));
    }

    @Test
    void shouldPass() {
      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenantId,
                      txCtxExtension.getRequestId(),
                      CfgAction.DELETE_CLIENT_ISOLATION_PROFILE,
                      randomName(),
                      new RequestParams().addPathVariable("clientIsolationProfileId", profileId),
                      ""))
          .isNotNull()
          .getRootCause()
          .isNotNull()
          .isInstanceOf(ObjectInUseException.class);
    }
  }

  private List<Operation> extractDdccmOperations() {
    var requestId = txCtxExtension.getRequestId();
    var tenantId = txCtxExtension.getTenantId();
    var messages = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(messages)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId));
    var payload = messages.getPayload();
    assertThat(payload).isNotNull();
    var operations = payload.getOperationsList();
    assertThat(operations).isNotEmpty();
    return operations;
  }

  private void verifyDdccmForUnusedProfileDeletion(String profileId) {
    var operations = extractDdccmOperations();
    assertThat(operations)
        .hasSize(1)
        .filteredOn(operation -> operation.getAction() == Action.DELETE)
        .filteredOn(Operation::hasVenueClientIsolationWhitelist)
        .hasSize(1)
        .allSatisfy(
            op -> assertThat(op.getVenueClientIsolationWhitelist().getId()).isEqualTo(profileId));
  }

  private void verifyDdccmForUnusedProfileDeletionWithUnusedAdoptions(
      String profileId, String adoptionId) {
    var operations = extractDdccmOperations();
    assertThat(operations)
        .hasSize(2)
        .filteredOn(operation -> operation.getAction() == Action.DELETE)
        .hasSize(2);
    assertThat(operations)
        .filteredOn(Operation::hasVenueClientIsolationWhitelist)
        .hasSize(1)
        .allSatisfy(
            op -> assertThat(op.getVenueClientIsolationWhitelist().getId()).isEqualTo(profileId));
    assertThat(operations)
        .filteredOn(Operation::hasApLanPortProfile)
        .hasSize(1)
        .allSatisfy(op -> assertThat(op.getId()).isEqualTo(adoptionId));
  }

  private void verifyActivityForUnusedProfileDeletion() {
    var status = extractActivityConfigurationStatus();
    assertThat(status)
        .matches(s -> s.getStatus().equals(Status.OK))
        .matches(s -> s.getStep().equals(ApiFlowNames.DELETE_CLIENT_ISOLATION_PROFILE))
        .extracting(ConfigurationStatus::getEventDate)
        .isNotNull();
    messageCaptors
        .getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(txCtxExtension.getTenantId());
  }

  private ConfigurationStatus extractActivityConfigurationStatus() {
    var requestId = txCtxExtension.getRequestId();
    var tenantId = txCtxExtension.getTenantId();
    var message = messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);
    assertThat(message)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();
    return message.getPayload();
  }

  private void verifyCmnConfigForUnusedProfileDeletion(String profileId) {
    var operations = extractCmnConfigOperations();
    assertThat(operations)
        .hasSize(1)
        .singleElement()
        .matches(op -> op.getOpType() == OpType.DEL && profileId.equals(op.getId()));
  }

  private List<Operations> extractCmnConfigOperations() {
    var requestId = txCtxExtension.getRequestId();
    var tenantId = txCtxExtension.getTenantId();

    var message = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    return message.getPayload().getOperationsList().stream()
        .filter(op -> EsConstants.Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME.equals(op.getIndex()))
        .toList();
  }
}
