package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('633b7de0bc1545b7ac5f8396721a0668');
    INSERT INTO directory_server_profile (id, name, tenant) VALUES (
      '1d447865e50447e58005a7a972b71ed0',
      'profile1',
      '633b7de0bc1545b7ac5f8396721a0668');
    INSERT INTO network (id, name, type, tenant, directory_server_profile) VALUES (
      '5296251a44fb4ecdb0a5677163ca834e',
      'name1',
      'GUEST',
      '633b7de0bc1545b7ac5f8396721a0668',
      '1d447865e50447e58005a7a972b71ed0');
    """)
public class GuestNetworkRepositoryTest {

  @Autowired
  private GuestNetworkRepository target;

  @Test
  void testExistsByTenantIdAndDirectoryServerProfileId() {
    var result = target.existsByTenantIdAndDirectoryServerProfileId(
        "633b7de0bc1545b7ac5f8396721a0668", "1d447865e50447e58005a7a972b71ed0");
    assertTrue(result);
  }

  @Test
  void testFindByTenantIdAndDirectoryServerProfileId() {
    var result = target.findByTenantIdAndDirectoryServerProfileId(
        "633b7de0bc1545b7ac5f8396721a0668", "1d447865e50447e58005a7a972b71ed0");
    assertEquals(1, result.size());
    assertTrue(result.stream()
        .anyMatch(guestNetwork -> guestNetwork.getId().equals("5296251a44fb4ecdb0a5677163ca834e")));
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('633b7de0bc1545b7ac5f8396721a0668');
      INSERT INTO network (id, name, type, tenant, workflow_profile_id) VALUES (
      '0a98875abe484272b89c41e4873caeea',
      'name1',
      'GUEST',
      '633b7de0bc1545b7ac5f8396721a0668',
      '1d447865e50447e58005a7a972b71ed0'
      ),(
      '25f7a7a6e8cf4115a375a24476622696',
      'name2',
      'GUEST',
      '633b7de0bc1545b7ac5f8396721a0668',
      '1d447865e50447e58005a7a972b71ed0');     
      INSERT INTO network (id, name, type, tenant) VALUES (
      '0a98875abe484272b89c41e4873caeec',
      'name3',
      'GUEST',
      '633b7de0bc1545b7ac5f8396721a0668');
      """)
  void testFindByTenantIdAndWorkflowProfileIdNotNull() {
    var result = target.findByTenantIdAndWorkflowProfileIdNotNull("633b7de0bc1545b7ac5f8396721a0668");
    assertEquals(2, result.size());
  }
}
