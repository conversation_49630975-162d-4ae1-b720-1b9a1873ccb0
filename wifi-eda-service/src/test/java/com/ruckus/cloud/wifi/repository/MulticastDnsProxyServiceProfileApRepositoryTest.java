package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.test.fixture.ApTestFixture.randomAp;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity.Fields;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.fixture.MulticaseDnsProxyTestFixture;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES
        ('6700bc51acf84c4aa9510df2ca00b5f4'), ('d149c579c7bc41e19e71563a5b2e5268');
    INSERT INTO venue (id, tenant) VALUES
        ('b1a2c59f92d243cba7ca5b4bfe83c289', '6700bc51acf84c4aa9510df2ca00b5f4'),
        ('5180c0857c074dec97ed2fe4c215628c', '6700bc51acf84c4aa9510df2ca00b5f4'),
        ('90027f4df1104d71aeef4d622db56b65', 'd149c579c7bc41e19e71563a5b2e5268');
    INSERT INTO ap_group (id, tenant, venue, is_default) VALUES
        ('753755ebcac84ce798ea5f3ee11b5731', '6700bc51acf84c4aa9510df2ca00b5f4', 'b1a2c59f92d243cba7ca5b4bfe83c289', TRUE),
        ('243280ce025f4def9ac45626271a077f', '6700bc51acf84c4aa9510df2ca00b5f4', 'b1a2c59f92d243cba7ca5b4bfe83c289', FALSE),
        ('3b52e107c0df4524a8d2da97eecdc309', '6700bc51acf84c4aa9510df2ca00b5f4', '5180c0857c074dec97ed2fe4c215628c', FALSE),
        ('85da4266bdea49b0bb6e6ddbcdf3d53e', 'd149c579c7bc41e19e71563a5b2e5268', '90027f4df1104d71aeef4d622db56b65', TRUE);
    INSERT INTO ap (id, tenant, ap_group, soft_deleted) VALUES
        ('983518622414', '6700bc51acf84c4aa9510df2ca00b5f4', '753755ebcac84ce798ea5f3ee11b5731', false),
        ('983518622415', '6700bc51acf84c4aa9510df2ca00b5f4', '753755ebcac84ce798ea5f3ee11b5731', false),
        ('454643635712', '6700bc51acf84c4aa9510df2ca00b5f4', '243280ce025f4def9ac45626271a077f', false),
        ('963852741242', '6700bc51acf84c4aa9510df2ca00b5f4', '3b52e107c0df4524a8d2da97eecdc309', false),
        ('123456789102', 'd149c579c7bc41e19e71563a5b2e5268', '85da4266bdea49b0bb6e6ddbcdf3d53e', false);
    INSERT INTO multicast_dns_proxy_service_profile (id, tenant) VALUES
        ('34ffc50c45284c0a8a3ce5809d9c1c76', '6700bc51acf84c4aa9510df2ca00b5f4'),
        ('f55bc40635a745d7a320616644e4339f', '6700bc51acf84c4aa9510df2ca00b5f4'),
        ('aaaa374691934521b7f9bbb54722c6bf', 'd149c579c7bc41e19e71563a5b2e5268');
    INSERT INTO multicast_dns_proxy_service_profile_ap (id, tenant, ap, multicast_dns_proxy_service_profile) VALUES
        ('d888fd374a974e548af80a7ecbc74230', '6700bc51acf84c4aa9510df2ca00b5f4', '983518622414', '34ffc50c45284c0a8a3ce5809d9c1c76'),
        ('e619cccb6bd74075ba0b220611969aab', '6700bc51acf84c4aa9510df2ca00b5f4', '454643635712', '34ffc50c45284c0a8a3ce5809d9c1c76'),
        ('2753f0c022e6493e8016d5a8c049cae4', '6700bc51acf84c4aa9510df2ca00b5f4', '963852741242', 'f55bc40635a745d7a320616644e4339f'),
        ('6cff2270114840f3b7b64fd6e26134b8', 'd149c579c7bc41e19e71563a5b2e5268', '123456789102', 'aaaa374691934521b7f9bbb54722c6bf');
    INSERT INTO multicast_dns_proxy_service_rule (id, tenant, multicast_dns_proxy_service_profile, rule_index, service) VALUES
        ('501c9b901e9c48eaae5ab1e9d42979c1', '6700bc51acf84c4aa9510df2ca00b5f4', '34ffc50c45284c0a8a3ce5809d9c1c76', 0, 'AIRDISK'),
        ('06bb3825b6454978a48d783ed4f2d1f0', '6700bc51acf84c4aa9510df2ca00b5f4', '34ffc50c45284c0a8a3ce5809d9c1c76', 0, 'AIRPLAY'),
        ('5180c0857c074dec97ed2fe4c215628c', 'd149c579c7bc41e19e71563a5b2e5268', 'aaaa374691934521b7f9bbb54722c6bf', 0, 'AIRDISK');
    """)
class MulticastDnsProxyServiceProfileApRepositoryTest {
  private static final String TENANT_ID = "6700bc51acf84c4aa9510df2ca00b5f4";
  private static final String VENUE_ID = "b1a2c59f92d243cba7ca5b4bfe83c289";
  private static final String AP_ID = "983518622414";
  private static final String AP_GROUP_ID = "753755ebcac84ce798ea5f3ee11b5731";
  private static final String MULTICAST_DNS_PROXY_SERVICE_PROFILE_ID = "34ffc50c45284c0a8a3ce5809d9c1c76";
  private static final List<String> MULTICAST_DNS_PROXY_SERVICE_PROFILE_AP_IDS = List.of("e619cccb6bd74075ba0b220611969aab", "d888fd374a974e548af80a7ecbc74230");
  private static final List<String> AP_IDS = List.of("454643635712", "983518622414");

  @Autowired
  private MulticastDnsProxyServiceProfileApRepository repository;

  @Test
  void countByTenantIdAndMulticastDnsProxyServiceProfileId() {
    assertThat(repository.countByTenantIdAndMulticastDnsProxyServiceProfileId(TENANT_ID, MULTICAST_DNS_PROXY_SERVICE_PROFILE_ID))
            .isEqualTo(2);
  }

  @Test
  void findByApIdAndTenantId() {
    assertThat(repository.findByApIdAndTenantId(AP_ID, TENANT_ID))
            .isNotNull()
            .hasSize(1)
            .singleElement()
            .extracting(MulticastDnsProxyServiceProfileAp::getId)
            .isEqualTo(MULTICAST_DNS_PROXY_SERVICE_PROFILE_AP_IDS.get(1));
  }

  @Test
  void findByTenantIdAndMulticastDnsProxyServiceProfileId() {
    assertThat(repository.findByTenantIdAndMulticastDnsProxyServiceProfileId(TENANT_ID, MULTICAST_DNS_PROXY_SERVICE_PROFILE_ID))
            .isNotNull()
            .hasSize(2)
            .extracting(MulticastDnsProxyServiceProfileAp::getId)
            .containsAll(MULTICAST_DNS_PROXY_SERVICE_PROFILE_AP_IDS);
  }

  @Test
  void findByTenantIdAndApIdIn() {
    assertThat(repository.findByTenantIdAndApIdIn(TENANT_ID, List.of(AP_ID)))
            .isNotNull()
            .hasSize(1)
            .singleElement()
            .extracting(MulticastDnsProxyServiceProfileAp::getId)
            .isEqualTo(MULTICAST_DNS_PROXY_SERVICE_PROFILE_AP_IDS.get(1));
  }

  @Test
  void findApIdsByTenantIdAndMulticastDnsProxyServiceProfileIds() {
    assertThat(repository.findApIdsByTenantIdAndMulticastDnsProxyServiceProfileIds(TENANT_ID, List.of(MULTICAST_DNS_PROXY_SERVICE_PROFILE_ID)))
            .isNotNull()
            .hasSize(2)
            .containsAll(AP_IDS);
  }

  @Test
  void findByTenantIdAndVenueId() {
    assertThat(repository.findByTenantIdAndVenueId(TENANT_ID, VENUE_ID))
            .isNotNull()
            .hasSize(2)
            .extracting(MulticastDnsProxyServiceProfileAp::getId)
            .containsAll(MULTICAST_DNS_PROXY_SERVICE_PROFILE_AP_IDS);
  }

  @Test
  void findApsByTenantIdAndMulticastDnsProxyProfileId() {
    assertThat(repository.findApsByTenantIdAndMulticastDnsProxyServiceProfileId(TENANT_ID,
        MULTICAST_DNS_PROXY_SERVICE_PROFILE_ID))
        .isNotNull()
        .hasSize(2)
        .extracting(Ap::getApGroup)
        .extracting(ApGroup::getVenue)
        .extracting(Venue::getId)
        .allSatisfy(v -> {
          assertThat(v).isEqualTo(VENUE_ID);
        });
  }

  @Test
  void save() {
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDNSProfile");
    multicastDnsProxyServiceProfile.setId("34ffc50c45284c0a8a3ce5809d9c1c76");
    Ap ap = randomAp();
    ap.setId("983518622415");
    ap.getApGroup().setId(AP_GROUP_ID);
    ap.getApGroup().getVenue().setId(VENUE_ID);
    ap.setTenant(new Tenant(TENANT_ID));
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setId(randomId());
    mDnsAp.setAp(ap);
    mDnsAp.setTenant(ap.getTenant());
    mDnsAp.setMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile);
    MulticastDnsProxyServiceProfileAp profileApFromRepo = repository.save(mDnsAp);
    assertThat(profileApFromRepo)
            .isNotNull()
            .extracting(MulticastDnsProxyServiceProfileAp::getId)
            .isEqualTo(mDnsAp.getId());
    assertThat(profileApFromRepo)
            .isNotNull()
            .extracting(MulticastDnsProxyServiceProfileAp::getAp)
            .extracting(Ap::getId)
            .isEqualTo(mDnsAp.getAp().getId());
  }

  @Test
  void testFindDistinctMulticastServiceProfiles() {
    final Sort sortByCreatedDate = Sort.by(Fields.CREATEDDATE);
    Pageable pageRequest = PageRequest.of(0, 100, sortByCreatedDate);
    var result = repository.findDistinctMulticastDnsProxyServiceProfilesByTenantId(
        pageRequest, TENANT_ID);
    assertThat(result)
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(2)
        .extracting(MulticastDnsProxyServiceProfile::getId)
        .contains("34ffc50c45284c0a8a3ce5809d9c1c76", "f55bc40635a745d7a320616644e4339f");
  }

  @Test
  void testFindAllDistinctTenantIds() {
    var result = repository.findAllDistinctTenantIds();
    assertThat(result)
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(2)
        .contains("6700bc51acf84c4aa9510df2ca00b5f4", "d149c579c7bc41e19e71563a5b2e5268");
  }
}
