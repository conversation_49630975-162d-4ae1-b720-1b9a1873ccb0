package com.ruckus.cloud.wifi.scheduler;

import com.ruckus.cloud.wifi.client.kairos.KairosApiClient;
import com.ruckus.cloud.wifi.client.kairos.dto.KafkaTarget;
import com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;

import static com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.REMOTE_TRIGGER_METHOD;
import static com.ruckus.cloud.wifi.test.Assertions.argThat;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@WifiUnitTest
class AbstractScheduleRegisterTest {

  private static final String JOB_NAME = "jobName";
  private static final String FEATURE_FLAG = "featureFlag";
  private static final String ROUTINE_JOB_SCHEDULE_TIME = "0 0/30 * * * ? *";

  @MockBean
  private KairosApiClient kairosApiClient;
  @MockBean
  private FeatureFlagService featureFlagService;

  private DummyScheduleRegister unit;

  @BeforeEach
  void initUnit() {
    unit = spy(new DummyScheduleRegister(kairosApiClient, featureFlagService));
  }

  @SneakyThrows
  @Test
  void testOnApplicationReadyWithFeatureFlagOff() {
    doReturn(false)
        .when(kairosApiClient).hasScheduleJob(JOB_NAME);
    doReturn(false).when(featureFlagService).isFeatureEnable(eq(FEATURE_FLAG), any());

    unit.execute();

    verify(kairosApiClient, never())
        .deleteScheduleJob(JOB_NAME);
    verify(kairosApiClient, never())
        .createScheduleJob(eq(JOB_NAME), any(RegisterKairosRequest.class));
    verify(kairosApiClient, never())
        .updateScheduleJob(eq(JOB_NAME), any(RegisterKairosRequest.class));

    clearInvocations(kairosApiClient);
    doReturn(true)
        .when(kairosApiClient).hasScheduleJob(JOB_NAME);

    unit.execute();

    verify(kairosApiClient, times(1))
        .deleteScheduleJob(JOB_NAME);
    verify(kairosApiClient, never())
        .createScheduleJob(eq(JOB_NAME), any(RegisterKairosRequest.class));
    verify(kairosApiClient, never())
        .updateScheduleJob(eq(JOB_NAME), any(RegisterKairosRequest.class));
  }

  @SneakyThrows
  @Test
  void testOnApplicationReadyWithFeatureFlagOnThenCreateScheduleJob() {
    doReturn(false)
        .when(kairosApiClient).hasScheduleJob(JOB_NAME);
    doReturn(true).when(featureFlagService).isFeatureEnable(eq(FEATURE_FLAG), any());

    unit.execute();

    verify(kairosApiClient, never())
        .deleteScheduleJob(JOB_NAME);
    verify(kairosApiClient, times(1))
        .createScheduleJob(eq(JOB_NAME), argThat(req -> assertThat(req)
            .isNotNull()
            .satisfies(request -> assertSoftly(softly -> {
              softly.assertThat(request.getScheduleTime()).isEqualTo(ROUTINE_JOB_SCHEDULE_TIME);
              softly.assertThat(request.getTenantId()).isEqualTo(JOB_NAME);
              softly.assertThat(request.getTimeZone()).isEqualTo(TimeZone.getDefault().getID());
              softly.assertThat(request.getKafkaTarget()).isNotNull()
                  .satisfies(kafkaTarget -> assertSoftly(alsoSoftly -> {
                    alsoSoftly.assertThat(kafkaTarget.getTopicName()).isNotEmpty();
                    alsoSoftly.assertThat(kafkaTarget.getKey()).isNotEmpty();
                    alsoSoftly.assertThat(kafkaTarget.getData()).isNotEmpty()
                        .extractingByKey("methodName")
                        .isNotNull();
                  }));
            }))));
    verify(kairosApiClient, never())
        .updateScheduleJob(eq(JOB_NAME), any(RegisterKairosRequest.class));
  }

  public static class DummyScheduleRegister extends AbstractScheduleRegister {
    @Override
    public String getJobName() {
      return JOB_NAME;
    }

    @Override
    public String getFeatureFlag() {
      return FEATURE_FLAG;
    }

    @Override
    public RegisterKairosRequest kairosRequestPayload() {
      String requestId = UUID.randomUUID().toString();

      Map<String, Object> rpcDhcpEventSchedule = new HashMap<>();
      rpcDhcpEventSchedule.put(REMOTE_TRIGGER_METHOD, JOB_NAME);

      return RegisterKairosRequest.builder()
          .scheduleTime(ROUTINE_JOB_SCHEDULE_TIME)
          .tenantId(this.getJobName())
          .kafkaTarget(
              KafkaTarget.newKafkaTarget(
                  () -> rpcDhcpEventSchedule, requestId, this.getJobName()))
          .build();
    }

    protected DummyScheduleRegister(KairosApiClient kairosApiClient, FeatureFlagService featureFlagService) {
      super(kairosApiClient, featureFlagService);
    }
  }

}