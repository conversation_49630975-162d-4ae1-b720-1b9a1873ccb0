package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radSecOptions;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.client.certificatetemplate.CertificateDto;
import com.ruckus.cloud.wifi.client.certificatetemplate.CertificateTemplateApiClient;
import com.ruckus.cloud.wifi.eda.service.CertificateAuthorityServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.RadSecOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.repository.RadiusRepository;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.enums.CertStatusEnum;
import com.ruckus.cloud.wifi.service.integration.config.CertificateAuthorityServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.Hotspot20IdentityProviderTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Arrays;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Import;

@WifiJpaDataTest
public class CertificateAuthorityServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private CertificateTemplateApiClient certificateTemplateApiClient;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private CertificateAuthorityServiceCtrl certificateAuthorityServiceCtrl;

  @Autowired
  private RadiusRepository radiusRepository;

  private Radius radius;

  private Tenant tenant;

  @BeforeEach
  public void setUp(Tenant tenant) throws CommonException {
    String radiusId = txCtxExtension.newRandomId();
    radius = Generators.radiusProfile().generate();
    radius.setRadSecOptions(radSecOptions()
        .setCertificateAuthorityId(randomString())
        .generate());
    radius.setId(radiusId);

    var savedAuthRadius =
        repositoryUtil.createOrUpdate(radius, tenant.getId(),
            txCtxExtension.newRandomId());

    CertificateDto certificateDto = CertificateDto.builder()
        .id("certificateAuthorityId")
        .name("certificateName")
        .status(Arrays.asList(CertStatusEnum.VALID))
        .build();

    when(certificateTemplateApiClient.getCertificateAuthority(eq("certificateAuthorityId")))
        .thenReturn(Optional.of(certificateDto));
  }

  @Test
  public void testActivateCertificateAuthorityOnRadiusServerProfile() throws Exception {
    String certificateAuthorityId = "certificateAuthorityId";

    certificateAuthorityServiceCtrl.activateCertificateAuthorityOnRadiusServerProfile(
        radius.getId(), certificateAuthorityId);

    var radiusResult = radiusRepository.findByIdAndTenantId(radius.getId(), TxCtxHolder.tenantId());
    assertThat(radiusResult.get())
        .isNotNull()
        .extracting(Radius::getRadSecOptions)
        .extracting(RadSecOptions::getCertificateAuthorityId)
        .isEqualTo(certificateAuthorityId);
  }

  @Test
  public void testDeactivateCertificateAuthorityOnRadiusServerProfile() throws Exception {
    String certificateAuthorityId = "certificateAuthorityId";

    certificateAuthorityServiceCtrl.deactivateCertificateAuthorityOnRadiusServerProfile(
        radius.getId(), certificateAuthorityId);

    var radiusResult = radiusRepository.findByIdAndTenantId(radius.getId(), TxCtxHolder.tenantId());
    assertThat(radiusResult.get())
        .isNotNull()
        .extracting(Radius::getRadSecOptions)
        .extracting(RadSecOptions::getCertificateAuthorityId)
        .isNull();
  }

  @Test
  public void testDeactivateCertificateAuthorityOnRadiusServerProfileInUseOfOpenNetwork() throws Exception {
    Network network = NetworkTestFixture.randomOpenNetwork(tenant, openNetwork -> {
      openNetwork.getWlan().setMacAddressAuthentication(true);
      openNetwork.setAuthRadius(radius);
    });
    repositoryUtil.createOrUpdate(network);

    String certificateAuthorityId = "certificateAuthorityId";

    assertThrows(InvalidPropertyValueException.class, () ->
        certificateAuthorityServiceCtrl.deactivateCertificateAuthorityOnRadiusServerProfile(
            radius.getId(), certificateAuthorityId),
        "Cannot deactivate Certificate Authority on RADIUS server profile. It is being used by network(s), Identity Provider profile(s), or Ethernet Port profile(s).");
  }

  @Test
  public void testDeactivateCertificateAuthorityOnRadiusServerProfileInUseOfGuestNetwork() throws Exception {
    Network network = NetworkTestFixture.randomGuestNetwork(tenant, guestNetwork -> {
      guestNetwork.getGuestPortal().getWisprPage().setAuthRadius(radius);
    });
    repositoryUtil.createOrUpdate(network);

    String certificateAuthorityId = "certificateAuthorityId";

    assertThrows(InvalidPropertyValueException.class, () ->
            certificateAuthorityServiceCtrl.deactivateCertificateAuthorityOnRadiusServerProfile(
                radius.getId(), certificateAuthorityId),
        "Cannot deactivate Certificate Authority on RADIUS server profile. It is being used by network(s), Identity Provider profile(s), or Ethernet Port profile(s).");
  }

  @Test
  public void testDeactivateCertificateAuthorityOnRadiusServerProfileInUseOfHotspot20IdentityProvider() throws Exception {
    Hotspot20IdentityProvider hotspot20IdentityProvider = Hotspot20IdentityProviderTestFixture
        .randomHotspot20IdentityProvider(tenant, hotspot20Idp -> {
          hotspot20Idp.setAuthRadius(radius);
        });
    repositoryUtil.createOrUpdate(hotspot20IdentityProvider);

    String certificateAuthorityId = "certificateAuthorityId";

    assertThrows(InvalidPropertyValueException.class, () ->
            certificateAuthorityServiceCtrl.deactivateCertificateAuthorityOnRadiusServerProfile(
                radius.getId(), certificateAuthorityId),
        "Cannot deactivate Certificate Authority on RADIUS server profile. It is being used by network(s), Identity Provider profile(s), or Ethernet Port profile(s).");
  }

  @Test
  public void testDeactivateCertificateAuthorityOnRadiusServerProfileInUseOfEthernetPortProfile() throws Exception {
    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setEnableAuthProxy(always(true))
        .setAuthRadius(always(radius))
        .generate();
    repositoryUtil.createOrUpdate(ethernetPortProfile);

    String certificateAuthorityId = "certificateAuthorityId";

    assertThrows(InvalidPropertyValueException.class, () ->
            certificateAuthorityServiceCtrl.deactivateCertificateAuthorityOnRadiusServerProfile(
                radius.getId(), certificateAuthorityId),
        "Cannot deactivate Certificate Authority on RADIUS server profile. It is being used by network(s), Identity Provider profile(s), or Ethernet Port profile(s).");
  }

  @TestConfiguration
  @Import({CertificateAuthorityServiceCtrlImplTestConfig.class})
  static class TestConfig {
  }
}
