package com.ruckus.cloud.wifi.service.housekeeping;

import static com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest.JOB_NAME_KEY;
import static com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest.PROJECT_NAME_KEY;
import static com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest.PROJECT_NAME_VALUE;
import static com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest.SERVICE_NAME_KEY;
import static com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest.SERVICE_NAME_VALUE;
import static com.ruckus.cloud.wifi.service.housekeeping.HousekeepingRoutineJob.HOUSEKEEPING_DELETE_EXPIRED_COMPATIBILITY_ACTIVITIES_REQUESTID;
import static com.ruckus.cloud.wifi.service.housekeeping.HousekeepingRoutineJob.HOUSEKEEPING_DELETE_EXPIRED_REVISIONS_REQUESTID;
import static com.ruckus.cloud.wifi.service.housekeeping.HousekeepingRoutineJob.JOB_NAME_DELETE_EXPIRED_COMPATIBILITY_ACTIVITIES;
import static com.ruckus.cloud.wifi.service.housekeeping.HousekeepingRoutineJob.JOB_NAME_DELETE_EXPIRED_REVISIONS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.kafka.support.KafkaHeaders.KEY;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest;
import com.ruckus.cloud.wifi.kafka.publisher.WifiSchedulePublisher;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.net.URI;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpMethod;

@WifiUnitTest
class HousekeepingRoutineJobTest {

  @MockBean
  private WifiSchedulePublisher wifiSchedulePublisher;

  @SuppressWarnings("unchecked")
  @Test
  void testRegisterRoutingJobs() throws JsonProcessingException {
    HousekeepingRoutineJob unit = new HousekeepingRoutineJob(wifiSchedulePublisher,
        new ObjectMapper(), "wifi-eda", 8080,
        true,  10, 500);
    unit.registerRoutineJobs();

    ArgumentCaptor<String> payloadCaptor = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<Map<String, Object>> headerCaptor = ArgumentCaptor.forClass(Map.class);
    verify(wifiSchedulePublisher, times(2)).publishKairosRegister(payloadCaptor.capture(),
        headerCaptor.capture());
    assertDeleteExpiredRevisions(payloadCaptor.getAllValues().get(0), headerCaptor.getAllValues().get(0));
    assertDeleteCompatibilityActivities(payloadCaptor.getAllValues().get(1), headerCaptor.getAllValues().get(1));
  }

  private void assertDeleteExpiredRevisions(String payload, Map header) throws JsonProcessingException {
    var registerKairosRequest = new ObjectMapper().readValue(payload, RegisterKairosRequest.class);
    assertThat(registerKairosRequest)
        .satisfies(e -> {
          assertThat(e.getScheduleTime()).isNotEmpty();
          assertThat(e.getTimeZone()).isEqualTo("Etc/UTC");
          assertThat(e.getKafkaTarget()).isNull();
        }).extracting(RegisterKairosRequest::getHttpTarget)
        .satisfies(e -> {
          assertThat(new URI(e.getUri())).hasHost("wifi-eda");
          assertThat(new URI(e.getUri())).hasPort(8080);
          assertThat(e.getHttpMethod()).isNotNull();
          assertThat(e.getBody()).isNotNull();
        });

    assertThat(header).contains(entry(KEY, HOUSEKEEPING_DELETE_EXPIRED_REVISIONS_REQUESTID + "@@"
            + HousekeepingRoutineJob.HOUSEKEEPING_TENANT_ID),
        entry(PROJECT_NAME_KEY, PROJECT_NAME_VALUE),
        entry(SERVICE_NAME_KEY, SERVICE_NAME_VALUE),
        entry(JOB_NAME_KEY, JOB_NAME_DELETE_EXPIRED_REVISIONS));
  }

  private void assertDeleteCompatibilityActivities(String payload, Map<String, Object> header) throws JsonProcessingException {
      final var registerKairosRequest = new ObjectMapper().readValue(payload, RegisterKairosRequest.class);
    assertThat(registerKairosRequest)
        .satisfies(
            e -> {
              assertThat(e.getScheduleTime()).isNotEmpty();
              assertThat(e.getTimeZone()).isEqualTo("Etc/UTC");
              assertThat(e.getKafkaTarget()).isNull();
            })
        .extracting(RegisterKairosRequest::getHttpTarget)
        .satisfies(
            e -> {
              assertThat(new URI(e.getUri())).hasHost("wifi-eda");
              assertThat(new URI(e.getUri())).hasPort(8080);
              assertThat(e.getHttpMethod()).isEqualTo(HttpMethod.DELETE);
              assertThat(e.getBody()).isNotNull().isEmpty();
            });

    assertThat(header)
        .containsEntry(
            KEY,
            HOUSEKEEPING_DELETE_EXPIRED_COMPATIBILITY_ACTIVITIES_REQUESTID
                + "@@"
                + HousekeepingRoutineJob.HOUSEKEEPING_TENANT_ID)
        .containsEntry(PROJECT_NAME_KEY, PROJECT_NAME_VALUE)
        .containsEntry(SERVICE_NAME_KEY, SERVICE_NAME_VALUE)
        .containsEntry(JOB_NAME_KEY, JOB_NAME_DELETE_EXPIRED_COMPATIBILITY_ACTIVITIES);
  }
}
