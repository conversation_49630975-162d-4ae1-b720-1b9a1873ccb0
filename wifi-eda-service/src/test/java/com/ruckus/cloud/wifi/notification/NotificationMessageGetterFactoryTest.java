package com.ruckus.cloud.wifi.notification;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class NotificationMessageGetterFactoryTest {

  @SpyBean
  private NotificationMessageGetterFactory notificationMessageGetterFactory;

  @MockBean
  private FeatureFlagService featureFlagService;

  @MockBean
  private AcxFirmwareNotificationMessageGetter acxFirmwareNotificationMessage;

  @MockBean
  private AcxNewFirmwareNotificationMessageGetter acxNewFirmwareNotificationMessageGetter;

  @Test
  public void getAcxFirmwareNotificationMessageGetter() {
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM), anyString()))
        .thenReturn(false);

    FirmwareNotificationMessageGetter mg = notificationMessageGetterFactory.getByFeatureFlag(
        "tenant-id");

    Assertions.assertTrue(mg instanceof AcxFirmwareNotificationMessageGetter);
  }

  @Test
  public void getAcxNewFirmwareNotificationMessageGetter() {
    when(featureFlagService.isFeatureEnable(eq(WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM), anyString()))
        .thenReturn(true);

    FirmwareNotificationMessageGetter mg = notificationMessageGetterFactory.getByFeatureFlag(
        "tenant-id");

    Assertions.assertTrue(mg instanceof AcxNewFirmwareNotificationMessageGetter);
  }
}