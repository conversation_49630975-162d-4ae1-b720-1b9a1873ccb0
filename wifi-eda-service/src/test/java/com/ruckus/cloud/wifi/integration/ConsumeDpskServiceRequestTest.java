package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_DPSK_SERVICE_ON_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Objects;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("DpskServiceTest")
@WifiIntegrationTest
class ConsumeDpskServiceRequestTest extends AbstractRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Test
  void testActivateDpskServiceOnWifiNetwork(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String dpskServiceId = randomTxId();

    final var dpskNetwork = (DpskNetwork) network(DpskNetwork.class).generate();
    dpskNetwork.setUseDpskService(true);
    dpskNetwork.getWlan().setNetwork(dpskNetwork);
    repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());
    networkId = dpskNetwork.getId();

    RequestParams requestParams = new RequestParams().addPathVariable("wifiNetworkId", networkId)
        .addPathVariable("dpskServiceId", dpskServiceId);

    messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.ACTIVATE_DPSK_SERVICE_ON_WIFI_NETWORK, userName, requestParams, null);
    assertActivityStatusSuccess(ACTIVATE_DPSK_SERVICE_ON_WIFI_NETWORK, tenantId);

    final DpskNetwork network = repositoryUtil.find(DpskNetwork.class, networkId);
    assertThat(network).isNotNull().matches(n -> Objects.equals(n.getId(), networkId))
        .matches(n -> Objects.equals(n.getDpskServiceProfileId(), dpskServiceId));
  }

  @Test
  void testActivateDpskServiceOnWifiNetwork_notUsingDpskService_fail(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String certificateTemplateId = randomTxId();

    final var dpskNetwork = (DpskNetwork) network(DpskNetwork.class).generate();
    dpskNetwork.getWlan().setNetwork(dpskNetwork);
    dpskNetwork.setUseDpskService(false);
    repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());
    networkId = dpskNetwork.getId();

    RequestParams requestParams = new RequestParams().addPathVariable("wifiNetworkId", networkId)
        .addPathVariable("dpskServiceId", certificateTemplateId);

    assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.ACTIVATE_DPSK_SERVICE_ON_WIFI_NETWORK, userName, requestParams, null)).isNotNull()
        .getRootCause().isInstanceOf(InvalidPropertyValueException.class);
    assertActivityStatusFail(ACTIVATE_DPSK_SERVICE_ON_WIFI_NETWORK, tenantId);
  }

  @Test
  void testActivateDpskServiceOnDpsk3OnboardNetwork_fail(Tenant tenant) {
    String networkId;
    var tenantId = tenant.getId();
    var userName = randomName();
    var requestId = randomTxId();
    var certificateTemplateId = randomTxId();

    final var dpskNetwork = (DpskNetwork) network(DpskNetwork.class).generate();
    networkId = dpskNetwork.getId();
    dpskNetwork.getWlan().setNetwork(dpskNetwork);
    dpskNetwork.setUseDpskService(true);
    dpskNetwork.setIsDsaeServiceNetwork(false);
    dpskNetwork.setDsaeNetworkPairId(networkId);
    repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());

    var requestParams = new RequestParams().addPathVariable("wifiNetworkId", networkId)
        .addPathVariable("dpskServiceId", certificateTemplateId);

    assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(tenantId, requestId,
        CfgAction.ACTIVATE_DPSK_SERVICE_ON_WIFI_NETWORK, userName, requestParams, null)).isNotNull()
        .getRootCause().isInstanceOf(InvalidPropertyValueException.class).hasMessage(
        "Cannot activate DPSK service on DPSK3 onboard network [" + dpskNetwork.getId() + "].");;
    assertActivityStatusFail(ACTIVATE_DPSK_SERVICE_ON_WIFI_NETWORK, tenantId);
  }

}
