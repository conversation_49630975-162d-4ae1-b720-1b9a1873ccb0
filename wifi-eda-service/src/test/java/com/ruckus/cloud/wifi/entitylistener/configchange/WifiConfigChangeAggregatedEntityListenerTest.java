package com.ruckus.cloud.wifi.entitylistener.configchange;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.proto.view.mapper.service.IdReferenceProxyService;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.kafka.publisher.WifiCfgChangePublisher;
import com.ruckus.cloud.wifi.proto.Operation;
import com.ruckus.cloud.wifi.service.core.tx.BasicTxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.EntitiesOrderService;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesImpl;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class WifiConfigChangeAggregatedEntityListenerTest {

  @MockBean
  private WifiCfgChangePublisher wifiCfgChangePublisher;
  @SpyBean
  private WifiConfigChangeBulkAggregatedEntityListener listener;
  @MockBean
  private EntityManager entityManager;
  @MockBean
  private IdReferenceProxyService idReferenceProxyService;
  @MockBean
  private EntitiesOrderService entitiesOrderService;
  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withFlow("WifiCfgRequest$Test");

  @Test
  public void testBuildAndFlush() {
    TxChangesReader changes = new BasicTxChangesReader(new TxChangesImpl(null));
    List<Pair<String, Operation>> operations = new ArrayList<>();
    operations.addAll(listener.bulkBuild(
        List.of(
            new TxEntity<>(new GuestNetwork(), EntityAction.ADD),
            new TxEntity<>(new GuestNetwork(), EntityAction.ADD),
            new TxEntity<>(new Wlan(), EntityAction.ADD),
            new TxEntity<>(new GuestPortal(), EntityAction.ADD)),
        Collections.emptyList(),
        Collections.emptyList(),
        changes));
    listener.flush(operations);
    verify(wifiCfgChangePublisher, times(1)).publish(
        argThat(message -> (message.getRequestId().equals(txCtxExtension.getRequestId())
            && message.getTenantId().equals(txCtxExtension.getTenantId()))),
        argThat(apiAction -> "Test".equals(apiAction)),
        any());
  }

  @Test
  void testExcludeEntity() {
    assertThat(listener.filter().test(new ApFirmwareUpgradeRequest())).isFalse();
    assertThat(listener.filter().test(new GuestNetwork())).isTrue();
  }
}
