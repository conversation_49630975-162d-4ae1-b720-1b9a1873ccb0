package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Ap.GpsMode;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLocation.CcmHeightType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLocation.CcmSource;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApBssColoring;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGeoLocation;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueHeight;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApVersionLabelEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LocationHeightTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LocationSourceEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ScanMethodEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.ApFloorPosition;
import com.ruckus.cloud.wifi.eda.viewmodel.ApPosition;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRadioCustomization;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRadioParams6G;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRadioParamsDual5G;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.servicemodel.TenantEarlyAccessInfo;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ScheduleTimeSlotTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantAvailableApFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleFirmwareVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckuswireless.acx.franz.gpb.ServiceDrivenStatusProto.ServiceDrivenDeviceStatus.ConfigFwStatus;
import com.ruckuswireless.acx.franz.gpb.ServiceDrivenStatusProto.ServiceDrivenDeviceStatus.DeviceType;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.listener.ListenerExecutionFailedException;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
public class ConsumeUpdateApRequestTest extends AbstractRequestTest {
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private TenantClient tenantClient;

  @Nested
  class whenConsumeUpdateApRequest {

    @Deprecated(forRemoval = true)
    @Test
    void thenSaveAp_modifyApPosition(Venue venue, Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = venue.getTenant().getId();

      final var apRequest = toApRequest(ap);

      final var apPosition = new ApPosition();
      apPosition.setFloorplanId(randomId());
      apPosition.setXPercent(123.456F);
      apPosition.setYPercent(654.321F);
      apRequest.setPosition(apPosition);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);

      validateResult(tenantId, requestId, apRequest);
    }

    @Test
    void thenSaveAp_modifyApPosition(Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var floorplanId = randomId();

      final var apFloorPosition = new ApFloorPosition();
      apFloorPosition.setXPercent(123.456F);
      apFloorPosition.setYPercent(654.321F);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.ACTIVATE_AP_FLOOR_POSITION,
          userName,
          new RequestParams()
              .addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("floorplanId", floorplanId),
          apFloorPosition);
      validateActivateApFloorPositionResult(ap, ap.getTenant().getId(), requestId, apFloorPosition);

      final var requestId2 = randomTxId();
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId2,
          CfgAction.DEACTIVATE_AP_FLOOR_POSITION,
          userName,
          new RequestParams()
              .addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("floorplanId", floorplanId),
          StringUtils.EMPTY);
      validateDeactivateApFloorPositionResult(ap, ap.getTenant().getId(), requestId2);
    }

    @Deprecated(forRemoval = true)
    @Test
    void thenSaveAp_modifyApGroup(Venue venue, @DefaultApGroup ApGroup defaultApGroup, Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var tenantId = venue.getTenant().getId();

      final var apRequest = toApRequest(ap);
      apRequest.setApGroupId(apGroupId);

      repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(defaultApGroup.getVenue(), c -> c.setId(apGroupId)),
          tenantId, randomTxId());

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);

      validateResult(tenantId, requestId, apRequest);
    }

    @Deprecated(forRemoval = true)
    @Test
    void thenSaveAp_moveVenue_checkMdnsDisabled(Tenant tenant, Venue venue, Ap ap, MulticastDnsProxyServiceProfile mDnsProfile) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = venue.getTenant().getId();

      MulticastDnsProxyServiceProfileAp mDnsProfileAp = new MulticastDnsProxyServiceProfileAp();
      mDnsProfileAp.setId(randomId());
      mDnsProfileAp.setAp(ap);
      mDnsProfileAp.setMulticastDnsProxyServiceProfile(mDnsProfile);
      mDnsProfile.setMulticastDnsProxyServiceProfileAps(List.of(mDnsProfileAp));
      repositoryUtil.createOrUpdate(mDnsProfileAp, tenantId, randomTxId());
      ap.setMulticastDnsProxyServiceProfileAp(List.of(mDnsProfileAp));

      Venue newVenue = createVenue(tenant, "newVenue");
      final var apRequest = toApRequest(ap);
      apRequest.setVenueId(newVenue.getId());
      apRequest.setMulticastDnsProxyServiceProfileId(mDnsProfile.getId());

      assertEquals(mDnsProfileAp.getId(), repositoryUtil.find(MulticastDnsProxyServiceProfileAp.class, mDnsProfileAp.getId()).getId());

      assertThatNoException().isThrownBy( () -> {
        messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);
      });
      final var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
      assertThat(ddccmMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();
      assertThat(ddccmMessage.getPayload().getOperationsList())
          .filteredOn(Operation::hasAp)
          .filteredOn(e -> e.getAction() == Action.MODIFY)
          .extracting(Operation::getAp)
          .last()
          .matches(e -> e.hasBonjourGateway() == false);
    }
  }

  @Test
  void testSaveAp_moveVenue_checkGeoLocationCleared(Tenant tenant, Venue venue, Ap ap) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var tenantId = venue.getTenant().getId();
    final var geoLocation = new ApGeoLocation();
    geoLocation.setSource(LocationSourceEnum.GEO_LOCATOR);
    geoLocation.setHeight(1f);
    geoLocation.setHeightType(LocationHeightTypeEnum.AGL);
    geoLocation.setLatitude(8.8f);
    geoLocation.setLongitude(3.1f);
    geoLocation.setLateralUncertainty(0.02f);
    geoLocation.setVerticalUncertainty(0.1f);
    ap.setGeoLocation(geoLocation);

    repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());

    var apFromDb = repositoryUtil.find(Ap.class, ap.getId());

    assertNotNull(apFromDb);
    assertNotNull(apFromDb.getGeoLocation());
    assertEquals(1f, apFromDb.getGeoLocation().getHeight());
    assertEquals(LocationHeightTypeEnum.AGL.name(),
        apFromDb.getGeoLocation().getHeightType().name());
    assertEquals(LocationSourceEnum.GEO_LOCATOR.name(),
        apFromDb.getGeoLocation().getSource().name());
    assertEquals(8.8f, apFromDb.getGeoLocation().getLatitude());
    assertEquals(3.1f, apFromDb.getGeoLocation().getLongitude());
    assertEquals(0.02f, apFromDb.getGeoLocation().getLateralUncertainty());
    assertEquals(0.1f, apFromDb.getGeoLocation().getVerticalUncertainty());

    Venue newVenue = createVenue(tenant, "newVenue");
    final var apRequest = toApRequest(ap);
    apRequest.setVenueId(newVenue.getId());

    assertThatNoException().isThrownBy(() -> {
      messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);
    });

    apFromDb = repositoryUtil.find(Ap.class, ap.getId());

    assertNotNull(apFromDb);
    assertNotNull(apFromDb.getGeoLocation());
    assertEquals(0, apFromDb.getGeoLocation().getHeight());
    assertEquals(LocationHeightTypeEnum.UNDEFINED.name(),
        apFromDb.getGeoLocation().getHeightType().name());
    assertEquals(LocationSourceEnum.NO_LOCATION.name(),
        apFromDb.getGeoLocation().getSource().name());
    assertEquals(0, apFromDb.getGeoLocation().getLatitude());
    assertEquals(0, apFromDb.getGeoLocation().getLongitude());
    assertEquals(0, apFromDb.getGeoLocation().getLateralUncertainty());
    assertEquals(0, apFromDb.getGeoLocation().getVerticalUncertainty());

    final var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();
    assertThat(ddccmMessage.getPayload().getOperationsList())
        .filteredOn(Operation::hasAp)
        .filteredOn(e -> e.getAction() == Action.MODIFY)
        .extracting(Operation::getAp)
        .last()
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasApLocation)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getApLocation)
        .matches(a -> !a.hasHeight())
        .matches(a -> !a.hasLatitude())
        .matches(a -> !a.hasLongitude())
        .matches(a -> !a.hasLateralUncertainty())
        .matches(a -> !a.hasVerticalUncertainty())
        .matches(a -> CcmHeightType.UNDEFINED.equals(a.getHeightType()))
        .matches(a -> CcmSource.NO_LOCATION.equals(a.getSource()));
  }

  @Test
  void testSaveAp_moveVenue_checkBssColoringReset(Tenant tenant, Venue venue, Ap ap) {
    final var requestId = randomTxId();
    final var tenantId = venue.getTenant().getId();
    final var userName = randomName();
    final var apBssColoring = new ApBssColoring();
    apBssColoring.setBssColoringEnabled(false);
    ap.setBssColoring(apBssColoring);

    repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());

    var apFromDb = repositoryUtil.find(Ap.class, ap.getId());

    assertNotNull(apFromDb);
    assertNotNull(apFromDb.getBssColoring());
    assertFalse(apFromDb.getBssColoring().getBssColoringEnabled());

    Venue newVenue = createVenue(tenant, "newVenue");
    final var apRequest = toApRequest(ap);
    apRequest.setVenueId(newVenue.getId());

    assertThatNoException().isThrownBy(() -> {
      messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName, apRequest);
    });


    apFromDb = repositoryUtil.find(Ap.class, ap.getId());

    assertNotNull(apFromDb);
    assertNotNull(apFromDb.getBssColoring());
    assertNull(apFromDb.getBssColoring().getBssColoringEnabled());

    final var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .filteredOn(Operation::hasAp)
        .filteredOn(e -> e.getAction() == Action.MODIFY)
        .extracting(Operation::getAp)
        .last()
        .matches(a -> !a.hasBssColoring());
  }

  @Nested
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  class whenApFwMgmtUpgradeByModelFfTurnOn {

    @Deprecated(forRemoval = true)
    @Test
    void testSaveAp_moveVenue_updateVenueCurrentFirmwareAndSchedule(Tenant tenant, Venue venue,
        Ap ap,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.1.103.511") ApVersion version491,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.2.103.588") ApVersion version492)
        throws Exception {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = venue.getTenant().getId();
      final var apModel = "R550";
      ap.setModel(apModel);
      repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
      version491.setSupportedApModels(List.of(apModel));
      repositoryUtil.createOrUpdate(version491, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(
          VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, version491,
              ap.getModel()), tenantId, randomTxId());
      repositoryUtil.createOrUpdate(
          TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant, version491),
          tenantId, randomTxId());

      UpgradeSchedule schedule = createUpgradeSchedule(venue, version492);
      UpgradeScheduleFirmwareVersion scheduleVer492 =
          UpgradeScheduleFirmwareVersionTestFixture.randomUpgradeScheduleFirmwareVersion(
              schedule, version492, List.of(ap.getModel()), c -> {
              });
      repositoryUtil.createOrUpdate(scheduleVer492, tenantId, randomTxId());

      Venue newVenue = createVenue(tenant, "newVenue");
      ApGroup newApGroup = createApGroup(newVenue, "newApGroup");

      final var apRequest = toApRequest(ap);
      apRequest.setVenueId(newVenue.getId());
      apRequest.setApGroupId(newApGroup.getId());

      assertThatNoException().isThrownBy(() -> {
        messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName,
            apRequest);
      });

      final var cmnCfgCollectorMsg = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);
      validateResult(tenantId, requestId, apRequest, cmnCfgCollectorMsg);
      validateVenueCurrentFirmwaresCmnCfgMsg(cmnCfgCollectorMsg, venue.getId(), List.of(), null);
      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId))
          .filteredOn(vcf -> newVenue.getId().equals(vcf.getVenue().getId()))
          .hasSize(1)
          .singleElement()
          .satisfies(newVcf -> {
            assertEquals(version491.getId(), newVcf.getFirmware().getId());
            assertEquals(ap.getModel(), newVcf.getApModel());
            assertEquals(newVenue.getId(), newVcf.getVenue().getId());
            validateVenueCurrentFirmwaresCmnCfgMsg(cmnCfgCollectorMsg, newVenue.getId(),
                List.of(newVcf), true);
          });
      assertThat(repositoryUtil.find(UpgradeSchedule.class, schedule.getId(), tenantId)).isNull();
      assertThat(repositoryUtil.find(UpgradeScheduleFirmwareVersion.class, scheduleVer492.getId(),
          tenantId)).isNull();
    }

    @Nested
    @FeatureFlag(enable = FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE)
    class whenGreenFieldByApModelFfTurnOn {

      @Deprecated(forRemoval = true)
      @Test
      void testSaveAp_moveVenue_updateVenueCurrentFirmwareAndSchedule(Tenant tenant, Venue venue,
          Ap ap,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.1.103.511") ApVersion version491,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.2.103.588") ApVersion version492)
          throws Exception {
        final var requestId = randomTxId();
        final var userName = randomName();
        final var tenantId = venue.getTenant().getId();
        final var apModel = "R550";
        ap.setModel(apModel);
        repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
        version491.setSupportedApModels(List.of(apModel));
        repositoryUtil.createOrUpdate(version491, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(
            TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, version491,
                ap.getModel()), tenantId, randomTxId());
        repositoryUtil.createOrUpdate(
            VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, version491,
                ap.getModel()), tenantId, randomTxId());
        repositoryUtil.createOrUpdate(
            TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant,
                version491),
            tenantId, randomTxId());

        UpgradeSchedule schedule = createUpgradeSchedule(venue, version492);
        UpgradeScheduleFirmwareVersion scheduleVer492 =
            UpgradeScheduleFirmwareVersionTestFixture.randomUpgradeScheduleFirmwareVersion(
                schedule, version492, List.of(ap.getModel()), c -> {
                });
        repositoryUtil.createOrUpdate(scheduleVer492, tenantId, randomTxId());

        Venue newVenue = createVenue(tenant, "newVenue");
        ApGroup newApGroup = createApGroup(newVenue, "newApGroup");

        final var apRequest = toApRequest(ap);
        apRequest.setVenueId(newVenue.getId());
        apRequest.setApGroupId(newApGroup.getId());

        assertThatNoException().isThrownBy(() -> {
          messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName,
              apRequest);
        });

        final var cmnCfgCollectorMsg = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId);
        validateResult(tenantId, requestId, apRequest, cmnCfgCollectorMsg);
        validateVenueCurrentFirmwaresCmnCfgMsg(cmnCfgCollectorMsg, venue.getId(), List.of(), null);
        assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId))
            .filteredOn(vcf -> newVenue.getId().equals(vcf.getVenue().getId()))
            .hasSize(1)
            .singleElement()
            .satisfies(newVcf -> {
              assertEquals(version491.getId(), newVcf.getFirmware().getId());
              assertEquals(ap.getModel(), newVcf.getApModel());
              assertEquals(newVenue.getId(), newVcf.getVenue().getId());
              validateVenueCurrentFirmwaresCmnCfgMsg(cmnCfgCollectorMsg, newVenue.getId(),
                  List.of(newVcf), true);
            });
        assertThat(repositoryUtil.find(UpgradeSchedule.class, schedule.getId(), tenantId)).isNull();
        assertThat(repositoryUtil.find(UpgradeScheduleFirmwareVersion.class, scheduleVer492.getId(),
            tenantId)).isNull();
      }
    }

    @Nested
    @FeatureFlag(enable = FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE)
    class whenApFwMgmtEarlyAccessToggleOn {

      @Deprecated(forRemoval = true)
      @Test
      void testSaveAp_moveVenue_updateVenueCurrentFirmware(Tenant tenant, Venue venue,
          Ap ap,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.1.103.511") ApVersion gaVersion,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.2.103.588") ApVersion biggestGaVersion)
          throws Exception {
        doReturn(new TenantEarlyAccessInfo(true, false))
            .when(tenantClient).getEarlyAccessInfo(anyString(), anyString());

        final var requestId = randomTxId();
        final var userName = randomName();
        final var tenantId = venue.getTenant().getId();
        final var apModel = "R550";
        ap.setModel(apModel);
        repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
        gaVersion.setSupportedApModels(List.of(apModel));
        repositoryUtil.createOrUpdate(gaVersion, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, gaVersion,
            ap.getModel()), tenantId, randomTxId());
        repositoryUtil.createOrUpdate(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, gaVersion,
            ap.getModel()), tenantId, randomTxId());
        repositoryUtil.createOrUpdate(TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant,
            gaVersion), tenantId, randomTxId());
        repositoryUtil.createOrUpdate(TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant,
                biggestGaVersion), tenantId, randomTxId());
        ApVersion betaVersion = ApVersionTestFixture.recommendedApVersion("4.9.2.103.589", v -> {
          v.setSupportedApModels(List.of(apModel));
          v.setLabels(List.of(ApVersionLabelEnum.BETA));
        });
        ApVersion alphaVersion = ApVersionTestFixture.recommendedApVersion("4.9.2.103.590", v -> {
          v.setSupportedApModels(List.of(apModel));
          v.setLabels(List.of(ApVersionLabelEnum.ALPHA));
        });
        repositoryUtil.createOrUpdate(betaVersion, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(alphaVersion, tenantId, randomTxId());

        Venue newVenue = createVenue(tenant, "newVenue");
        ApGroup newApGroup = createApGroup(newVenue, "newApGroup");

        final var apRequest = toApRequest(ap);
        apRequest.setVenueId(newVenue.getId());
        apRequest.setApGroupId(newApGroup.getId());
        assertThatNoException().isThrownBy(() -> {
          messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP, userName,
              apRequest);
        });

        final var cmnCfgCollectorMsg = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId);
        validateResult(tenantId, requestId, apRequest, cmnCfgCollectorMsg);
        validateVenueCurrentFirmwaresCmnCfgMsg(cmnCfgCollectorMsg, venue.getId(), List.of(), null);
        assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId))
            .filteredOn(vcf -> newVenue.getId().equals(vcf.getVenue().getId()))
            .hasSize(1)
            .singleElement()
            .satisfies(newVcf -> {
              assertEquals(betaVersion.getId(), newVcf.getFirmware().getId());
              assertEquals(ap.getModel(), newVcf.getApModel());
              assertEquals(newVenue.getId(), newVcf.getVenue().getId());
              validateVenueCurrentFirmwaresCmnCfgMsg(cmnCfgCollectorMsg, newVenue.getId(),
                  List.of(newVcf), true);
            });
      }
    }
  }

  @Test
  void testUpdateApRadioParams6G(Venue venue, @ApModel("R770") Ap ap)
      throws ExecutionException, InterruptedException {
    // US afcEnabled: true, AFC can be enabled or disabled
    testUpdateApRadioParams6G(venue, ap, true, false);
    testUpdateApRadioParams6G(venue, ap, false, false);

    Throwable ex = assertThrows(RuntimeException.class,
        () -> testUpdateApRadioParams6G(venue, ap, true, true));

    ex = ex.getCause();
    assertInstanceOf(ListenerExecutionFailedException.class, ex);
    ex = ex.getCause();
    assertInstanceOf(InvalidPropertyValueException.class, ex);

    assertEquals(Errors.WIFI_10520.code(),
        ((InvalidPropertyValueException) ex).getErrorCode().code());
    assertEquals(Errors.WIFI_10520.message(), ex.getMessage());

    messageUtil.clearMessage();

    var venueHeight = new VenueHeight();
    venueHeight.setMinFloor(1);
    venueHeight.setMaxFloor(2);
    venue.setHeight(venueHeight);

    repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

    testUpdateApRadioParams6G(venue, ap, true, true);

    venue.setCountryCode("GB");

    repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

    // GB afcEnabled: false, AFC must be disabled
    testUpdateApRadioParams6G(venue, ap, true, false);
    testUpdateApRadioParams6G(venue, ap, false, false);

    ex = assertThrows(RuntimeException.class,
        () -> testUpdateApRadioParams6G(venue, ap, true, true));

    ex = ex.getCause();
    assertInstanceOf(ListenerExecutionFailedException.class, ex);
    ex = ex.getCause();
    assertInstanceOf(InvalidPropertyValueException.class, ex);

    assertEquals(Errors.WIFI_10502.code(),
        ((InvalidPropertyValueException) ex).getErrorCode().code());
    assertEquals(Errors.WIFI_10502.message(), ex.getMessage());
  }

  @Test
  void testUpdateOutdoorApRadioParams6G(Venue venue, @ApModel("T670") Ap ap) {
    var venueHeight = new VenueHeight();
    venueHeight.setMinFloor(1);
    venueHeight.setMaxFloor(2);
    venue.setHeight(venueHeight);

    repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

    // US afcEnabled: true, AFC can be enabled or disabled
    testUpdateApRadioParams6G(venue, ap, true, false);
    testUpdateApRadioParams6G(venue, ap, false, false);
    testUpdateApRadioParams6G(venue, ap, true, true);
  }

  private void testUpdateApRadioParams6G(Venue venue, Ap ap, boolean enable6G, boolean enableAFC) {
    final var requestId = randomTxId();
    final var userName = randomName();
    final var tenantId = venue.getTenant().getId();

    repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());

    var radioCustomization = new ApRadioCustomization();
    radioCustomization.setUseVenueSettings(false);
    radioCustomization.setEnable24G(false);
    radioCustomization.setEnable50G(false);
    radioCustomization.setEnable6G(enable6G);
    var radioParamsDual5G = new ApRadioParamsDual5G();
    radioParamsDual5G.setEnabled(false);
    radioParamsDual5G.setLower5gEnabled(false);
    radioParamsDual5G.setUpper5gEnabled(false);
    radioCustomization.setApRadioParamsDual5G(radioParamsDual5G);
    var radioParams6G = new ApRadioParams6G();
    radioParams6G.setEnableAfc(enableAFC);
    radioParams6G.setAllowedChannels(List.of(Channel6GEnum._1, Channel6GEnum._5, Channel6GEnum._9));
    radioCustomization.setApRadioParams6G(radioParams6G);

    var requestParams = new RequestParams().addPathVariable("serialNumber", ap.getId());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgAction.UPDATE_AP_RADIO_CUSTOMIZATION,
        userName, requestParams, radioCustomization);

    var apFromDb = repositoryUtil.find(Ap.class, ap.getId());

    assertNotNull(apFromDb);

    var radioCustomizationFromDb = apFromDb.getRadioCustomization();

    assertNotNull(radioCustomizationFromDb);
    assertFalse(radioCustomizationFromDb.getEnable24G());
    assertFalse(radioCustomizationFromDb.getEnable50G());
    assertFalse(radioCustomizationFromDb.getApRadioParamsDual5G().getEnabled());
    assertFalse(radioCustomizationFromDb.getApRadioParamsDual5G().getLower5gEnabled());
    assertFalse(radioCustomizationFromDb.getApRadioParamsDual5G().getUpper5gEnabled());
    assertEquals(ScanMethodEnum.BACKGROUND_SCANNING,
        radioCustomizationFromDb.getApRadioParams24G().getMethod());
    assertEquals(ScanMethodEnum.BACKGROUND_SCANNING,
        radioCustomizationFromDb.getApRadioParams50G().getMethod());
    assertEquals(ScanMethodEnum.BACKGROUND_SCANNING,
        radioCustomizationFromDb.getApRadioParamsDual5G().getRadioParamsLower5G().getMethod());
    assertEquals(ScanMethodEnum.BACKGROUND_SCANNING,
        radioCustomizationFromDb.getApRadioParamsDual5G().getRadioParamsUpper5G().getMethod());
    assertEquals(enable6G, radioCustomizationFromDb.getEnable6G());

    var radioParams6GFromDb = radioCustomizationFromDb.getApRadioParams6G();

    if ("T670".equals(ap.getModel()) && enable6G) {
      assertEquals(true, radioParams6GFromDb.getEnableAfc());
    } else {
      assertEquals(enableAFC, radioParams6GFromDb.getEnableAfc());
    }

    var allowedChannels6G = radioParams6G.getAllowedChannels();
    var allowedChannels6GFromDb = radioParams6GFromDb.getAllowedChannels();

    assertTrue(
        allowedChannels6G.size() == allowedChannels6GFromDb.size() && allowedChannels6G.containsAll(
            allowedChannels6GFromDb) && allowedChannels6GFromDb.containsAll(allowedChannels6G));

    final var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();
    assertThat(ddccmMessage.getPayload().getOperationsList())
        .filteredOn(Operation::hasAp)
        .filteredOn(e -> e.getAction() == Action.MODIFY)
        .extracting(Operation::getAp)
        .last()
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .matches(ddccmAp -> ddccmAp.getGpsMode() == GpsMode.GPS)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio60)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::getRadio60)
        .matches(radio60 -> {
          if (enable6G) {
            return ("T670".equals(ap.getModel()) || enableAFC) == !radio60.getLowPowerIndoorModeEnable();
          }
          return !enableAFC;
        })
        .matches(radio60 -> {
          if (enable6G) {
            return allowedChannels6G.size() == radio60.getAllowedChannelListList().size();
          }
          return true;
        });
  }

  private ApRequest toApRequest(Ap ap) {
    final var apRequest = new ApRequest();
    apRequest.setSerialNumber(ap.getId());
    apRequest.setName(ap.getName());
    apRequest.setApGroupId(ap.getApGroup().getId());
    apRequest.setVenueId(ap.getApGroup().getVenue().getId());
    return apRequest;
  }

  void validateResult(String tenantId, String requestId, ApRequest apRequest) {
    validateResult(tenantId, requestId, apRequest, null);
  }

  void validateResult(String tenantId, String requestId, ApRequest apRequest,
      KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage) {
    final var apFromDb = repositoryUtil.find(Ap.class, apRequest.getSerialNumber());

    assertThat(apFromDb)
        .isNotNull()
        .satisfies(ap -> assertSoftly(softly -> {
          softly.assertThat(ap.getId()).isEqualTo(apRequest.getSerialNumber());
          softly.assertThat(ap.getName()).isEqualTo(apRequest.getName());
          softly.assertThat(ap.getApGroup()).isNotNull()
              .satisfies(apGroup -> {
                assertThat(apGroup.getId()).isEqualTo(apRequest.getApGroupId());
                assertThat(apGroup.getVenue()).isNotNull()
                    .extracting(Venue::getId).isEqualTo(apRequest.getVenueId());
              });
        }));

    if(cmnCfgCollectorMessage == null) {
      cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);
    }

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    final var finalCmnCfgCollectorMessage = cmnCfgCollectorMessage;
    assertThatNoException().isThrownBy(() ->
        assertThat(finalCmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.DEVICE.equals(op.getIndex())) // Just care for device operation
            .hasSize(1)
            .singleElement()
            .matches(op -> op.getOpType() == OpType.MOD)
            .matches(op -> op.getId().equals(apRequest.getSerialNumber()))
            .extracting(Operations::getDocMap)
            .satisfies(docMap -> assertSoftly(softly -> {
              // AP position modification is ignored by com.ruckus.cloud.wifi.mapper.ApMerge.merge
              softly.assertThat(docMap.get(Key.FLOORPLAN_ID)).isNull();
              softly.assertThat(docMap.get(Key.X_PERCENT)).isNull();
              softly.assertThat(docMap.get(Key.Y_PERCENT)).isNull();
            })));

    messageCaptors.assertThat(
        kafkaTopicProvider.getDeviceRegist(),
        kafkaTopicProvider.getEntitlementDeviceOperationRequest()
    ).doesNotSendByTenant(tenantId);

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(ApiFlowNames.UPDATE_AP))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> msg.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList)
            .isEqualTo(List.of(apRequest.getSerialNumber())));

    final var franzDeviceStatusMessage = messageCaptors.getFranzDeviceStatusMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(franzDeviceStatusMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(franzDeviceStatusMessage.getPayload())
            .satisfies(msg -> assertSoftly(softly -> {
              softly.assertThat(msg.getConfigId()).isEqualTo(requestId);
              softly.assertThat(msg.getDeviceType()).isEqualTo(DeviceType.AP);
              softly.assertThat(msg.getSerialNumber()).isEqualTo(apRequest.getSerialNumber());
              softly.assertThat(msg.getConfigStatus()).isEqualTo(ConfigFwStatus.CONF_UPD_ONGOING);
            })));
  }

  void validateActivateApFloorPositionResult(Ap ap, String tenantId, String requestId,
      ApFloorPosition apFloorPosition) {
    final var apFromDb = repositoryUtil.find(Ap.class, ap.getId());

    assertThat(apFromDb)
        .isNotNull()
        .satisfies(apDb -> assertSoftly(softly -> {
          softly.assertThat(apDb.getId()).isEqualTo(ap.getId());
          softly.assertThat(apDb.getName()).isEqualTo(ap.getName());
          softly.assertThat(apDb.getApGroup()).isNotNull()
              .satisfies(apGroup -> {
                assertThat(apGroup.getId()).isEqualTo(ap.getApGroup().getId());
                assertThat(apGroup.getVenue()).isNotNull()
                    .extracting(Venue::getId).isEqualTo(ap.getApGroup().getVenue().getId());
              });
        }));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.DEVICE.equals(op.getIndex())) // Just care for device operation
            .hasSize(1)
            .singleElement()
            .matches(op -> op.getOpType() == OpType.MOD)
            .matches(op -> op.getId().equals(ap.getId()))
            .extracting(Operations::getDocMap)
            .satisfies(docMap -> assertSoftly(softly -> {
              // AP position modification is ignored by com.ruckus.cloud.wifi.mapper.ApMerge.merge
              //softly.assertThat(docMap.get(Key.FLOORPLAN_ID)).isNull();
              softly.assertThat(docMap.get(Key.X_PERCENT).equals(apFloorPosition.getXPercent()));
              softly.assertThat(docMap.get(Key.Y_PERCENT).equals(apFloorPosition.getYPercent()));
            })));

    messageCaptors.assertThat(
        kafkaTopicProvider.getDeviceRegist(),
        kafkaTopicProvider.getEntitlementDeviceOperationRequest()
    ).doesNotSendByTenant(tenantId);

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(ApiFlowNames.ACTIVATE_AP_FLOOR_POSITION))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> msg.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID)));
  }

  void validateDeactivateApFloorPositionResult(Ap ap, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, ap.getId());

    assertThat(apFromDb)
        .isNotNull()
        .satisfies(apDb -> assertSoftly(softly -> {
          softly.assertThat(apDb.getId()).isEqualTo(ap.getId());
          softly.assertThat(apDb.getName()).isEqualTo(ap.getName());
          softly.assertThat(apDb.getApGroup()).isNotNull()
              .satisfies(apGroup -> {
                assertThat(apGroup.getId()).isEqualTo(ap.getApGroup().getId());
                assertThat(apGroup.getVenue()).isNotNull()
                    .extracting(Venue::getId).isEqualTo(ap.getApGroup().getVenue().getId());
              });
        }));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.DEVICE.equals(op.getIndex())) // Just care for device operation
            .hasSize(1)
            .singleElement()
            .matches(op -> op.getOpType() == OpType.MOD)
            .matches(op -> op.getId().equals(ap.getId())));

    messageCaptors.assertThat(
        kafkaTopicProvider.getDeviceRegist(),
        kafkaTopicProvider.getEntitlementDeviceOperationRequest()
    ).doesNotSendByTenant(tenantId);

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(ApiFlowNames.DEACTIVATE_AP_FLOOR_POSITION))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> msg.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID)));
  }

  private UpgradeSchedule createUpgradeSchedule(Venue venue, ApVersion apVersion) {
    ScheduleTimeSlot sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
    repositoryUtil.createOrUpdate(sts, venue.getTenant().getId(), randomTxId());
    UpgradeSchedule schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, apVersion, sts,
        c -> {});
    return repositoryUtil.createOrUpdate(schedule, venue.getTenant().getId(), randomTxId());
  }

  private void validateVenueCurrentFirmwaresCmnCfgMsg(KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage,
      String venueId, List<VenueCurrentFirmware> vcfList, Boolean isApUpToUpdate) {
    Tuple[] vcfTuples = vcfList.stream().map(vcf -> new Tuple(vcf.getApModel(), vcf.getFirmware().getId()))
        .toArray(Tuple[]::new);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> Index.VENUE.equals(op.getIndex()) && op.getOpType() == OpType.MOD)
        .filteredOn(
            op -> venueId.equals(op.getId()) && op.getDocMap().containsKey(EsConstants.Key.CURRENT_AP_FIRMWARES))
        .isNotEmpty().singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(docMap -> {
          assertThat(docMap.get(EsConstants.Key.CURRENT_AP_FIRMWARES).getListValue().getValuesList())
              .extracting(c -> c.getStructValue().getFieldsMap())
              .extracting(map -> map.get(Key.AP_MODEL).getStringValue(), map -> map.get(Key.FIRMWARE).getStringValue())
              .containsExactlyInAnyOrder(vcfTuples);
          if (isApUpToUpdate != null) {
            assertThat(docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE).getBoolValue()).isEqualTo(isApUpToUpdate);
          } else {
            assertThat(docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE).getNullValue()).isEqualTo(NullValue.NULL_VALUE);
          }
          assertThat(docMap.get(Key.LAST_AP_FIRMWARE_UPDATE).getNullValue()).isEqualTo(NullValue.NULL_VALUE);
        });
  }
}