package com.ruckus.cloud.wifi.integration.template.decouple;

import com.google.common.collect.Maps;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DECOUPLE_AP_GROUP_INSTANCES;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@WifiIntegrationTest
public class ConsumeDecoupleApGroupInstancesRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void decouple_apGroupInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add venue template
    Venue venueTemplate = VenueTestFixture.randomVenue(mspTenant, v -> {
      v.setName("venue-template");
      v.setIsTemplate(true);
    });
    venueTemplate = repositoryUtil.createOrUpdate(venueTemplate, mspTenant.getId());

    // msp tenant add ApGroup template
    ApGroup apGroupTemplate = ApGroupTestFixture.randomApGroup(venueTemplate, ag -> {
      ag.setName("apgroup-template");
      ag.setIsTemplate(true);
    });
    apGroupTemplate = repositoryUtil.createOrUpdate(apGroupTemplate, mspTenant.getId());

    // ec tenant add venue instance
    changeTxCtxTenant(ecTenantId);
    Venue venueInstance = VenueTestFixture.randomVenue(ecTenant, v -> {
      v.setName("venue-instance");
      v.setIsTemplate(false);
    });
    venueInstance = repositoryUtil.createOrUpdate(venueInstance, ecTenantId);

    // ec tenant add ApGroup instance from template
    ApGroup finalApGroupTemplate = apGroupTemplate;
    ApGroup apGroupInstance = ApGroupTestFixture.randomApGroup(venueInstance, ag -> {
      ag.setName("apgroup-instance");
      ag.setTemplateId(finalApGroupTemplate.getId());
      ag.setIsTemplate(false);
      ag.setIsEnforced(true);
    });
    apGroupInstance = repositoryUtil.createOrUpdate(apGroupInstance, ecTenantId);
    log.warn("apGroupInstance id: {}", apGroupInstance.getId());

    // Verify initial state - instance should have templateId and isEnforced = true
    ApGroup instanceBeforeDecouple = repositoryUtil.find(ApGroup.class, apGroupInstance.getId(), ecTenantId, false);
    ApGroup finalApGroupTemplate1 = apGroupTemplate;
    assertAll("Verify ApGroup instance before decoupling",
        () -> assertEquals(finalApGroupTemplate1.getId(), instanceBeforeDecouple.getTemplateId()),
        () -> assertFalse(instanceBeforeDecouple.getIsTemplate()),
        () -> assertTrue(instanceBeforeDecouple.getIsEnforced()));

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_AP_GROUP_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_AP_GROUP_INSTANCES, ecTenantId);

    // Verify ApGroup instance was decoupled
    ApGroup instanceAfterDecouple = repositoryUtil.find(ApGroup.class, apGroupInstance.getId(), ecTenantId, false);
    assertAll("Verify ApGroup instance after decoupling",
        () -> assertNull(instanceAfterDecouple.getTemplateId(), "templateId should be null after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsEnforced(), "isEnforced should be false after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsTemplate(), "isTemplate should remain false"));

    // Verify template is not affected
    changeTxCtxTenant(mspTenant.getId());
    ApGroup templateAfterDecouple = repositoryUtil.find(ApGroup.class, apGroupTemplate.getId(), mspTenant.getId(), true);
    ApGroup finalApGroupTemplate2 = apGroupTemplate;
    assertAll("Verify template is not affected",
        () -> assertTrue(templateAfterDecouple.getIsTemplate(), "Template should remain as template"),
        () -> assertEquals(finalApGroupTemplate2.getName(), templateAfterDecouple.getName(),
            "Template name should be unchanged"));

    assertDdccmCfgRequestNotSent(ecTenantId);
    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instanceAfterDecouple.getId());
  }

  @Test
  public void decouple_multipleApGroupInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add venue template
    Venue venueTemplate = VenueTestFixture.randomVenue(mspTenant, v -> {
      v.setName("venue-template");
      v.setIsTemplate(true);
    });
    venueTemplate = repositoryUtil.createOrUpdate(venueTemplate, mspTenant.getId());

    // msp tenant add ApGroup templates
    ApGroup apGroupTemplate1 = ApGroupTestFixture.randomApGroup(venueTemplate, ag -> {
      ag.setName("apgroup-template-1");
      ag.setIsTemplate(true);
    });
    apGroupTemplate1 = repositoryUtil.createOrUpdate(apGroupTemplate1, mspTenant.getId());

    ApGroup apGroupTemplate2 = ApGroupTestFixture.randomApGroup(venueTemplate, ag -> {
      ag.setName("apgroup-template-2");
      ag.setIsTemplate(true);
    });
    apGroupTemplate2 = repositoryUtil.createOrUpdate(apGroupTemplate2, mspTenant.getId());

    // ec tenant add venue instance
    changeTxCtxTenant(ecTenantId);
    Venue venueInstance = VenueTestFixture.randomVenue(ecTenant, v -> {
      v.setName("venue-instance");
      v.setIsTemplate(false);
    });
    venueInstance = repositoryUtil.createOrUpdate(venueInstance, ecTenantId);

    // ec tenant add multiple ApGroup instances from templates
    ApGroup finalApGroupTemplate = apGroupTemplate1;
    ApGroup apGroupInstance1 = ApGroupTestFixture.randomApGroup(venueInstance, ag -> {
      ag.setName("apgroup-instance-1");
      ag.setTemplateId(finalApGroupTemplate.getId());
      ag.setIsTemplate(false);
      ag.setIsEnforced(true);
    });
    apGroupInstance1 = repositoryUtil.createOrUpdate(apGroupInstance1, ecTenantId);

    ApGroup finalApGroupTemplate1 = apGroupTemplate2;
    ApGroup apGroupInstance2 = ApGroupTestFixture.randomApGroup(venueInstance, ag -> {
      ag.setName("apgroup-instance-2");
      ag.setTemplateId(finalApGroupTemplate1.getId());
      ag.setIsTemplate(false);
      ag.setIsEnforced(true);
    });
    apGroupInstance2 = repositoryUtil.createOrUpdate(apGroupInstance2, ecTenantId);

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_AP_GROUP_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_AP_GROUP_INSTANCES, ecTenantId);

    // Verify all ApGroup instances were decoupled
    ApGroup instance1AfterDecouple = repositoryUtil.find(ApGroup.class, apGroupInstance1.getId(), ecTenantId, false);
    ApGroup instance2AfterDecouple = repositoryUtil.find(ApGroup.class, apGroupInstance2.getId(), ecTenantId, false);

    assertAll("Verify all ApGroup instances were decoupled",
        () -> assertNull(instance1AfterDecouple.getTemplateId(), "ApGroup 1 templateId should be null"),
        () -> assertFalse(instance1AfterDecouple.getIsEnforced(), "ApGroup 1 isEnforced should be false"),
        () -> assertNull(instance2AfterDecouple.getTemplateId(), "ApGroup 2 templateId should be null"),
        () -> assertFalse(instance2AfterDecouple.getIsEnforced(), "ApGroup 2 isEnforced should be false"));

    assertDdccmCfgRequestNotSent(ecTenantId);
  }

  @Test
  public void decouple_noApGroupInstances_shouldSucceed(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant with no ApGroup instances
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // Execute decouple operation on tenant with no ApGroups
    changeTxCtxTenant(ecTenantId);
    String requestId = randomTxId();
    sendWifiCfgRequest(ecTenantId, requestId, CfgAction.DECOUPLE_AP_GROUP_INSTANCES, userName, new RequestParams(),
        Maps.newHashMap(), "", false);

    assertActivityStatusSuccess(DECOUPLE_AP_GROUP_INSTANCES, ecTenantId);

    // Should not receive any viewmodel operations since no ApGroups were affected
    assertViewmodelOpsNotSent(ecTenantId);
    assertDdccmCfgRequestNotSent(ecTenantId);
  }

  private void assertViewmodelOpsNotSent(String tenantId) {
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }

  private void validateCmnCfgCollectorMessage(String tenantId, String requestId, String instanceId) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(p -> p.getTenantId().equals(tenantId))
        .matches(p -> p.getRequestId().equals(requestId));

    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .hasSize(1)
        .filteredOn(o -> o.getId().equals(instanceId)).first()
        .matches(o -> EsConstants.Index.DEVICE_GROUP.equals(o.getIndex()))
        .matches(o -> o.getOpType().equals(OpType.MOD))
        .matches(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(tenantId))
        .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())
        .matches(o -> !o.getDocMap().containsKey(EsConstants.Key.IS_ENFORCED));
//        .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue());
  }
} 