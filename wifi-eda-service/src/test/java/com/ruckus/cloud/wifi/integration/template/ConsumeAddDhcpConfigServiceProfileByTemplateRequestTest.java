package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_DHCP_CONFIG_SERVICE_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DEACTIVATE_VENUE_TEMPLATE_DHCP_POOL;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_DHCP_CONFIG_SERVICE_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.TemplateCreateInstanceOperation.newViewProperty;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile;
import static com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture.venueDhcpConfigServiceProfileSetting;
import static com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture.venueDhcpConfigServiceProfileSettings;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.getGuestNetwork;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueDhcpServiceSetting;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.client.guest.GuestClient;
import com.ruckus.cloud.wifi.client.guest.PortalServiceProfileDto;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApDhcpRoleEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpPool;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpPoolUsage;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpServiceAp;
import com.ruckus.cloud.wifi.eda.viewmodel.Property;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSetting;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsage;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.DhcpConfigServiceProfileRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddDhcpConfigServiceProfileByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private VenueRepository venueRepository;
  @Autowired
  private DhcpConfigServiceProfileRepository dhcpConfigServiceProfileRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;
  @Autowired
  private GuestClient guestClient;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  void create_dhcp_instance_and_apply_to_ec_venue(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var apVersion = mspTenant.getLatestReleaseVersion();

    // create msp venue and dhcp

    Venue v = createVenueTemplate(mspTenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();

    // When - enable venue dhcp setting
    // enable profile with 3 dhcpPools
    var poolsAmount = 3;
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(poolsAmount, 1, DhcpModeEnum.EnableOnEachAPs);
    edaAddDhcpConfigServiceProfileTemplate(mspTenantId, userName, dhcpConfigServiceProfileDeep);
    assertCmnCfgCollectorCfgRequestNotSent(mspTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, mspTenantId);
    DhcpConfigServiceProfileDeep templateDhcpConfigServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, null, templateDhcpConfigServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(mspTenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, mspTenantId);

    // assert template - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting templateVenueDhcpSetting = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    assertVenueDhcpConfigServiceProfileSetting(templateVenueDhcpSetting, venueDhcpServiceProfileSetting, null,
        true, poolsAmount);

    // create ec tenant and ec venue

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    Venue ecV = createVenue(ecTenant, "v", apVersion);
    ApGroup ecApGroup = createApGroup(ecV, "apg");
    String ecVenueId = ecV.getId();

    clearMessage();

    // create ec dhcp by msp dhcp template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(instanceId, templateDhcpConfigServiceProfile.getId(),
        ecTenantId, mspTenantId, mspRequestId, userName, instanceCreateRequest);

    assertActivityPlan(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    {
      DhcpConfigServiceProfileDeep ecConfigServiceProfile = getDhcpConfigServiceProfileDeep(instanceId);

      // assert ddccm
      var ddccmOperations = receiveDdccmOperations(1, ecTenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccm(ddccmOperations, 3),
          () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD, ecConfigServiceProfile.getDhcpPools())
      );

      // assert cmn-cfg-collector
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, ecTenantId);
      assertAll("assert viewmodel collector",
          () -> assertCmnViewModelCollector(viewmodelOperations, 1),
          () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.ADD, ecConfigServiceProfile)
      );
    }

    // ---------------------------------

    // apply ec venue to the new instance
    VenueDhcpConfigServiceProfileSetting ecVenueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, null, instanceId);
    edaUpdateVenueDhcpConfigServiceProfileSetting(ecTenantId, userName, ecVenueId,
        ecVenueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING,
        ecTenantId);

    // assert ec - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting ecVenueDhcpSetting = getVenueDhcpConfigServiceProfileSetting(ecVenueId);
    assertVenueDhcpConfigServiceProfileSetting(ecVenueDhcpSetting, ecVenueDhcpServiceProfileSetting, null,
        true, poolsAmount);

    // assert ec - getDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep ecConfigServiceProfile = getDhcpConfigServiceProfileDeep(instanceId);
    assertEquals(1, ecConfigServiceProfile.getUsage().size());
    assertEquals(ecVenueId, ecConfigServiceProfile.getUsage().get(0).getVenueId());
    // getDhcpConfigServiceProfileDeep() return usage
    // getDhcpConfigServiceProfileDeeps() return venueIds
    final DhcpConfigServiceProfileDeep finalConfigServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    assertDhcpConfigServiceProfile(finalConfigServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnEachAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"),
        List.of(ecVenueId));

    // assert ec - getVenueDhcpPoolUsage
    List<DhcpPoolUsage> ecPoolUsages = getVenueDhcpPoolUsage(ecVenueId);
    assertDhcpPoolUsage(ecPoolUsages, ecConfigServiceProfile.getDhcpPools(),
        ecConfigServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, ecTenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), ecConfigServiceProfile.getDhcpPools(),
            ecVenueDhcpSetting.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    // expected invoke 1 dhcpConfigServiceProfile
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, ecVenueId, true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalConfigServiceProfile)
    );
  }

  @Test
  void v1_1_create_dhcp_instance_and_apply_venue(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var apVersion = mspTenant.getLatestReleaseVersion();

    // create msp venue and dhcp

    Venue v = createVenueTemplate(mspTenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueTemplateId = v.getId();

    // create dhcp config profile with 3 dhcpPools
    {
      var poolsAmount = 3;
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture
          .dhcpConfigServiceProfileViewModel(poolsAmount, 1, DhcpModeEnum.EnableOnEachAPs);
      edaAddDhcpConfigServiceProfileTemplateV1_1(mspTenantId, userName, dhcpConfigServiceProfile);
      assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, mspTenantId);
    }

    // activate dhcp with 2 of 3 pools
    DhcpConfigServiceProfile templateDhcpConfigServiceProfile = getDhcpConfigServiceProfileTemplates().get(0);
    String disabledPoolName = templateDhcpConfigServiceProfile.getDhcpPools().get(2).getName();
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        null, templateDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getName)
            .filter(name -> !name.equals(disabledPoolName)).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(mspTenantId, userName, v.getId(),
        templateDhcpConfigServiceProfile.getId(), venueDhcpConfigServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, mspTenantId);

    List<WifiDhcpPoolUsage> mspPoolUsages = getVenueTemplateWifiDhcpPoolUsages(venueTemplateId).getWifiDhcpPoolUsages();
    assertEquals(2, mspPoolUsages.size());
    assertTrue(mspPoolUsages.stream().noneMatch(o -> o.getName().equals(disabledPoolName)));

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    ecTenant.setLatestReleaseVersion(apVersion);
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create ec dhcp by msp dhcp template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String ecDhcpConfigServiceProfileInstanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(ecDhcpConfigServiceProfileInstanceId, templateDhcpConfigServiceProfile.getId(),
        ecTenantId, mspTenantId, mspRequestId, userName, instanceCreateRequest);

    assertActivityPlan(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);

    clearMessage();

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    // apply venue template to create ec venue, venue-dhcp and disable venue-pools
    String ecVenueId = randomId();
    var event = buildTemplateInstanceEvent(v, ecVenueId, ecTenantId);
    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(ecTenant.getId(), requestId, event);

    assertActivityStatusSuccess("AddVenueByTemplateInWifi", ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST, ecTenantId);

    // assert ec - VenueDhcpConfigServiceProfileSettings
    VenueDhcpConfigServiceProfileSettings ecVenueDhcpConfigServiceProfileSettings = getVenueDhcpConfigServiceProfileSettings(ecVenueId, ecDhcpConfigServiceProfileInstanceId);
    assertFalse(ecVenueDhcpConfigServiceProfileSettings.getActiveDhcpPoolNames().isEmpty());

    List<WifiDhcpPoolUsage> ecPoolUsages = getVenueWifiDhcpPoolUsages(ecVenueId).getWifiDhcpPoolUsages();
    assertEquals(2, ecPoolUsages.size());
    assertTrue(ecPoolUsages.stream().noneMatch(o -> o.getName().equals(disabledPoolName)));
  }

  @Test
  void create_dhcp_instance_and_apply_venue(Tenant mspTenant) throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var apVersion = mspTenant.getLatestReleaseVersion();

    // create msp venue and dhcp

    Venue v = createVenueTemplate(mspTenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueTemplateId = v.getId();

    // When - enable venue dhcp setting
    // enable profile with 3 dhcpPools
    var poolsAmount = 3;
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(poolsAmount, 1, DhcpModeEnum.EnableOnEachAPs);
    edaAddDhcpConfigServiceProfileTemplate(mspTenantId, userName, dhcpConfigServiceProfileDeep);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, mspTenantId);
    DhcpConfigServiceProfileDeep templateDhcpConfigServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, null, templateDhcpConfigServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(mspTenantId, userName, venueTemplateId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, mspTenantId);

    // assert template - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting templateVenueDhcpSetting = getVenueTemplateDhcpConfigServiceProfileSetting(venueTemplateId);
    assertVenueDhcpConfigServiceProfileSetting(templateVenueDhcpSetting, venueDhcpServiceProfileSetting, null,
        true, poolsAmount);

    // disable 1 of 3 pools
    String disabledPoolId = templateDhcpConfigServiceProfile.getDhcpPools().get(2).getId();
    String disabledPoolName = templateDhcpConfigServiceProfile.getDhcpPools().get(2).getName();
    edaDeactivateTemplateDhcpPool(mspTenantId, userName, venueTemplateId, disabledPoolId);
    assertActivityStatusSuccess(DEACTIVATE_VENUE_TEMPLATE_DHCP_POOL, mspTenantId);
    List<DhcpPoolUsage> mspPoolUsages = getVenueTemplateDhcpPoolUsage(venueTemplateId);
    Set<String> mspActivePoolNames = mspPoolUsages.stream()
        .filter(DhcpPoolUsage::getActive).map(DhcpPoolUsage::getName).collect(Collectors.toSet());
    assertEquals(2, mspActivePoolNames.size());
    assertEquals(templateDhcpConfigServiceProfile.getDhcpPools().stream()
        .filter(o -> !o.getId().equals(disabledPoolId)).map(DhcpPool::getName)
        .collect(Collectors.toSet()), mspActivePoolNames);

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    ecTenant.setLatestReleaseVersion(apVersion);
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create ec dhcp by msp dhcp template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String dhcpConfigServiceProfileInstanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(dhcpConfigServiceProfileInstanceId, templateDhcpConfigServiceProfile.getId(),
        ecTenantId, mspTenantId, mspRequestId, userName, instanceCreateRequest);

    assertActivityPlan(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);

    clearMessage();

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    // apply venue template to create ec venue, venue-dhcp and disable venue-pools
    String ecVenueId = randomId();
    var event = buildTemplateInstanceEvent(v, ecVenueId, ecTenantId);
    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(ecTenant.getId(), requestId, event);

    assertActivityStatusSuccess("AddVenueByTemplateInWifi", ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST, ecTenantId);

    // assert ec - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting ecVenueDhcpSetting = getVenueDhcpConfigServiceProfileSetting(ecVenueId);
    assertEquals(dhcpConfigServiceProfileInstanceId, ecVenueDhcpSetting.getServiceProfileId());
    assertTrue(ecVenueDhcpSetting.getEnabled());

    // assert ec - getDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep ecConfigServiceProfile =
        getDhcpConfigServiceProfileDeep(dhcpConfigServiceProfileInstanceId);
    assertEquals(1, ecConfigServiceProfile.getUsage().size());
    assertEquals(ecVenueId, ecConfigServiceProfile.getUsage().get(0).getVenueId());
    // getDhcpConfigServiceProfileDeep() return usage
    // getDhcpConfigServiceProfileDeeps() return venueIds
    final DhcpConfigServiceProfileDeep finalConfigServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    assertDhcpConfigServiceProfile(finalConfigServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnEachAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"),
        List.of(ecVenueId));

    // assert ec - getVenueDhcpPoolUsage
    List<DhcpPoolUsage> ecPoolUsages = getVenueDhcpPoolUsage(ecVenueId);
    assertDhcpPoolUsage(ecPoolUsages, ecConfigServiceProfile.getDhcpPools(),
        ecConfigServiceProfile.getDhcpPools().stream().filter(o -> !o.getName().equals(disabledPoolName)).toList());
  }

  @Test
  void apply_venue_with_dhcp_enabled_guest_network(Tenant mspTenant) throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var apVersion = mspTenant.getLatestReleaseVersion();

    // create msp venue and dhcp

    Venue v = createVenueTemplate(mspTenant, "v", apVersion);
    createApGroupTemplate(v, "apg");

    var venueTemplateId = v.getId();

    // addGuestNetwork with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork guestNetwork = addGuestNetworkTemplate(map(networkDeepReq));
    assertNotNull(guestNetwork.getPortalServiceProfileId());
    edaAddNetworkVenueTemplate(mspTenantId, userName, guestNetwork.getId(), venueTemplateId, null);

    // assert template - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting templateVenueDhcpSetting = getVenueTemplateDhcpConfigServiceProfileSetting(venueTemplateId);
    assertNotNull(templateVenueDhcpSetting.getServiceProfileId());

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    ecTenant.setLatestReleaseVersion(apVersion);
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // mock ec portalServiceProfile
    var instancePortalServiceProfile = new PortalServiceProfileDto(randomId(), "portalServiceProfile");
    when(guestClient.findByTenantIdAndPortalServiceTemplateId(ecTenantId, guestNetwork.getPortalServiceProfileId()))
        .thenReturn(Optional.of(instancePortalServiceProfile));

    // create ec network by msp network template

    var instanceCreateRequest = new TemplateInstanceCreateRequest();
    var instanceId = randomId();

    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.NETWORK, instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, guestNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    clearMessage();

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    // apply venue template to create ec venue and venue-network
    String ecVenueId = randomId();
    var event = buildTemplateInstanceEvent(v, ecVenueId, ecTenantId);
    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(ecTenant.getId(), requestId, event);

    assertActivityStatusSuccess(ecTenantId, List.of(
        "AddVenueByTemplateInWifi",
        STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST));

    // assert ec - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting ecVenueDhcpSetting = getVenueDhcpConfigServiceProfileSetting(ecVenueId);
    assertNotNull(ecVenueDhcpSetting.getServiceProfileId());
    assertTrue(ecVenueDhcpSetting.getEnabled());

    // assert ec - getDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep ecConfigServiceProfile =
        getDhcpConfigServiceProfileDeep(ecVenueDhcpSetting.getServiceProfileId());
    assertEquals(1, ecConfigServiceProfile.getUsage().size());
    assertEquals(ecVenueId, ecConfigServiceProfile.getUsage().get(0).getVenueId());
    assertEquals("DHCP-Guest", ecConfigServiceProfile.getServiceName());
  }

  @Test
  void apply_venue_but_post_operations_fail(Tenant mspTenant) throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var apVersion = mspTenant.getLatestReleaseVersion();

    // create msp venue and dhcp

    Venue v = createVenueTemplate(mspTenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueTemplateId = v.getId();

    // When - enable venue dhcp setting
    // enable profile with 3 dhcpPools
    var poolsAmount = 3;
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(poolsAmount, 1, DhcpModeEnum.EnableOnEachAPs);
    edaAddDhcpConfigServiceProfileTemplate(mspTenantId, userName, dhcpConfigServiceProfileDeep);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, mspTenantId);
    DhcpConfigServiceProfileDeep templateDhcpConfigServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, null, templateDhcpConfigServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(mspTenantId, userName, venueTemplateId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, mspTenantId);

    // assert template - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting templateVenueDhcpSetting = getVenueTemplateDhcpConfigServiceProfileSetting(venueTemplateId);
    assertVenueDhcpConfigServiceProfileSetting(templateVenueDhcpSetting, venueDhcpServiceProfileSetting, null,
        true, poolsAmount);

    // disable 2 of 3 pools
    String disabledPoolId1 = templateDhcpConfigServiceProfile.getDhcpPools().get(1).getId();
    String disabledPoolId2 = templateDhcpConfigServiceProfile.getDhcpPools().get(2).getId();
    edaDeactivateTemplateDhcpPool(mspTenantId, userName, venueTemplateId, disabledPoolId1);
    assertActivityStatusSuccess(DEACTIVATE_VENUE_TEMPLATE_DHCP_POOL, mspTenantId);
    edaDeactivateTemplateDhcpPool(mspTenantId, userName, venueTemplateId, disabledPoolId2);
    assertActivityStatusSuccess(DEACTIVATE_VENUE_TEMPLATE_DHCP_POOL, mspTenantId);

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    ecTenant.setLatestReleaseVersion(apVersion);
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create ec dhcp by msp dhcp template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String dhcpConfigServiceProfileInstanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(dhcpConfigServiceProfileInstanceId, templateDhcpConfigServiceProfile.getId(),
        ecTenantId, mspTenantId, mspRequestId, userName, instanceCreateRequest);

    assertActivityPlan(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    // ec create an incorrect dhcp to make post operations fail
    log.debug("====ec create an incorrect dhcp to make post operations fail===");
    DhcpConfigServiceProfileDeep ecDhcpConfigServiceProfileDeep = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(1, 1, DhcpModeEnum.EnableOnEachAPs);
    ecDhcpConfigServiceProfileDeep.setId(dhcpConfigServiceProfileInstanceId);
    ecDhcpConfigServiceProfileDeep.getDhcpPools().get(0).setName("DhcpPool#11"); // not sure why can use the same DhcpPool#1
    edaUpdateDhcpConfigServiceProfile(ecTenantId, userName, dhcpConfigServiceProfileInstanceId, ecDhcpConfigServiceProfileDeep);
    assertActivityStatusSuccess(UPDATE_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);

    clearMessage();

    // apply venue template to create ec venue, venue-dhcp and disable venue-pools
    String ecVenueId = randomId();
    var event = buildTemplateInstanceEvent(v, ecVenueId, ecTenantId);
    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(ecTenant.getId(), requestId, event);

    assertActivityStatusSuccess("AddVenueByTemplateInWifi", ecTenantId);
    ConfigurationStatus failedStatus =
        assertActivityStatusFail(STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST, ecTenantId);
    // 1 failed activate dhcp operation
    assertEquals(1, StringUtils.countMatches(failedStatus.getError(), Errors.WIFI_10420.code()));
    assertEquals(1, StringUtils.countMatches(failedStatus.getError(), Errors.WIFI_10420.message()));
  }

  @Test
  void v_1_1_apply_venue_but_post_operations_fail(Tenant mspTenant) throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var apVersion = mspTenant.getLatestReleaseVersion();

    // create msp venue and dhcp

    Venue v = createVenueTemplate(mspTenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueTemplateId = v.getId();

    // create dhcp config profile with 3 dhcpPools
    {
      var poolsAmount = 3;
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture
          .dhcpConfigServiceProfileViewModel(poolsAmount, 1, DhcpModeEnum.EnableOnEachAPs);
      edaAddDhcpConfigServiceProfileTemplateV1_1(mspTenantId, userName, dhcpConfigServiceProfile);
      assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, mspTenantId);
    }

    // activate dhcp with 3 pools
    DhcpConfigServiceProfile templateDhcpConfigServiceProfile = getDhcpConfigServiceProfileTemplates().get(0);
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        null, templateDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(mspTenantId, userName, v.getId(),
        templateDhcpConfigServiceProfile.getId(), venueDhcpConfigServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, mspTenantId);

    List<WifiDhcpPoolUsage> mspPoolUsages = getVenueTemplateWifiDhcpPoolUsages(venueTemplateId).getWifiDhcpPoolUsages();
    assertEquals(3, mspPoolUsages.size());

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    ecTenant.setLatestReleaseVersion(apVersion);
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create ec dhcp by msp dhcp template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String dhcpConfigServiceProfileInstanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(dhcpConfigServiceProfileInstanceId, templateDhcpConfigServiceProfile.getId(),
        ecTenantId, mspTenantId, mspRequestId, userName, instanceCreateRequest);

    assertActivityPlan(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    // ec create an incorrect dhcp to make post operations fail
    log.debug("====ec create an incorrect dhcp to make post operations fail===");
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile ecDhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(1, 1, DhcpModeEnum.EnableOnEachAPs);
    ecDhcpConfigServiceProfile.setId(dhcpConfigServiceProfileInstanceId);
    ecDhcpConfigServiceProfile.getDhcpPools().get(0).setName("DhcpPool#11"); // not sure why can use the same DhcpPool#1
    edaUpdateDhcpConfigServiceProfileV1_1(ecTenantId, userName, dhcpConfigServiceProfileInstanceId, ecDhcpConfigServiceProfile);
    assertActivityStatusSuccess(UPDATE_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);

    clearMessage();

    // apply venue template to create ec venue, venue-dhcp and disable venue-pools
    String ecVenueId = randomId();
    var event = buildTemplateInstanceEvent(v, ecVenueId, ecTenantId);
    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(ecTenant.getId(), requestId, event);

    assertActivityStatusSuccess("AddVenueByTemplateInWifi", ecTenantId);
    ConfigurationStatus failedStatus =
        assertActivityStatusFail(STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST, ecTenantId);
    // 1 failed activate dhcp operation
    assertEquals(1, StringUtils.countMatches(failedStatus.getError(), Errors.WIFI_10420.code()));
    assertEquals(1, StringUtils.countMatches(failedStatus.getError(), Errors.WIFI_10420.message()));
  }

  @Test
  public void ec_add_dhcp_fail_then_msp_activity_should_fail(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add template

    DhcpConfigServiceProfile template = randomDhcpConfigServiceProfile(true);
    template.setServiceName("test-dhcp");
    repositoryUtil.createOrUpdate(template, mspTenant.getId(), randomTxId());
    DhcpConfigServiceProfile templateAdded =
        repositoryUtil.find(DhcpConfigServiceProfile.class, template.getId(), mspTenantId, true);
    assertNotNull(templateAdded);

    // ec tenant add dhcp with same name earlier to make template-create-instance fail

    DhcpConfigServiceProfile ecDhcp1 = randomDhcpConfigServiceProfile();
    ecDhcp1.setServiceName("test-dhcp");
    repositoryUtil.createOrUpdate(ecDhcp1, ecTenantId, randomTxId());
    DhcpConfigServiceProfile ecDhcpAdded1 =
        repositoryUtil.find(DhcpConfigServiceProfile.class, ecDhcp1.getId(), ecTenantId, false);
    assertNotNull(ecDhcpAdded1);

    // create ec instance by msp template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(instanceId, template.getId(),
        ecTenantId, mspTenantId, mspRequestId, userName, instanceCreateRequest);

    assertActivityPlan(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusFail(ADD_DHCP_CONFIG_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusFail(ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void ec_add_dhcp_fail_msp_should_get_activity_fail_because_incorrect_overrides(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add template

    DhcpConfigServiceProfile template = randomDhcpConfigServiceProfile(true);
    template.setServiceName("test-dhcp");
    repositoryUtil.createOrUpdate(template, mspTenant.getId(), randomTxId());
    DhcpConfigServiceProfile templateAdded =
        repositoryUtil.find(DhcpConfigServiceProfile.class, template.getId(), mspTenantId, true);
    assertNotNull(templateAdded);

    // create ec instance by msp template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    instanceCreateRequest.setOverrides(List.of(newViewProperty("x", "x"))); // incorrect

    String instanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(instanceId, template.getId(), ecTenantId, mspTenantId, mspRequestId, userName,
        instanceCreateRequest);

    assertActivityPlanNotSent(ecTenantId);
    assertActivityStatusFail(ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  private void addByTemplate(String instanceId, String templateId, String ecTenantId,
      String mspTenantId, String mspRequestId, String userName,
      TemplateInstanceCreateRequest instanceCreateRequest) {

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ADD_DHCP_CONFIG_SERVICE_PROFILE, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.DHCP_CONFIG_SERVICE_PROFILE,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, templateId)
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE,
        ADD_DHCP_CONFIG_SERVICE_PROFILE_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);
  }

  private void assertVenueDhcpConfigServiceProfileSetting(
      VenueDhcpConfigServiceProfileSetting result,
      VenueDhcpConfigServiceProfileSetting expectedVenueDhcpServiceProfileSetting,
      List<DhcpServiceAp> expectedDhcpAps, boolean shouldAssertIp, int ipAmount) {
    assertEquals(expectedVenueDhcpServiceProfileSetting.getServiceProfileId(), result.getServiceProfileId(),
        "ServiceProfileId should equal test data");
    assertEquals(expectedVenueDhcpServiceProfileSetting.getEnabled(), result.getEnabled(),
        "VenueDhcpServiceProfileSetting should be enabled / disabled");

    if (expectedDhcpAps != null) {
      List<String> expectedSerialNumbers = expectedDhcpAps.stream()
          .map(DhcpServiceAp::getSerialNumber).collect(Collectors.toList());
      List<String> actualSerialNumbers = result.getDhcpServiceAps().stream()
          .map(DhcpServiceAp::getSerialNumber).collect(Collectors.toList());
      DhcpServiceAp primaryDhcpSeviceAp = result.getDhcpServiceAps().stream()
          .filter(ap -> ApDhcpRoleEnum.PrimaryServer.equals(ap.getRole())).findFirst().get();
      DhcpServiceAp backupDhcpSeviceAp = result.getDhcpServiceAps().stream()
          .filter(ap -> ApDhcpRoleEnum.BackupServer.equals(ap.getRole())).findFirst().get();
      assertEquals(expectedDhcpAps.size(), result.getDhcpServiceAps().size(),
          "Should have DhcpServiceAps");
      assertTrue(actualSerialNumbers.containsAll(expectedSerialNumbers),
          "DhcpServiceAps serial numbers should equal test data");

      if (shouldAssertIp && ipAmount > 0) {
        List<String> primaryIps = Arrays.asList("***********", "***********", "***********",
            "***********");
        List<String> secondaryIps = Arrays.asList("***********", "***********", "***********",
            "***********");
        assertTrue(primaryDhcpSeviceAp.getDhcpIps().containsAll(primaryIps.subList(0, ipAmount)),
            "Primary DhcpServiceAp IP should equal test data");
        assertTrue(backupDhcpSeviceAp.getDhcpIps().containsAll(secondaryIps.subList(0, ipAmount)),
            "Secondary DhcpServiceAp IP should equal test data");
      }
    } else {
      assertEquals(0, result.getDhcpServiceAps().size(), "Should have no DhcpServiceAps");
    }
  }

  private void assertDhcpConfigServiceProfile(DhcpConfigServiceProfileDeep result,
      String expectedServiceName, DhcpModeEnum expectedDhcpMode, List<String> expectedDhcpPoolNames,
      List<String> expectedVenueIds) {
    assertEquals(expectedServiceName, result.getServiceName());
    assertEquals(expectedDhcpMode, result.getDhcpMode());
    assertEquals(expectedDhcpPoolNames.size(), result.getDhcpPools().size());
    List<String> actualDhcpPoolNames = result.getDhcpPools().stream().map(DhcpPool::getName)
        .collect(Collectors.toList());
    assertTrue(actualDhcpPoolNames.containsAll(expectedDhcpPoolNames));
    if (expectedVenueIds != null) {
      assertEquals(expectedVenueIds.size(), result.getVenueIds().size());
      assertTrue(result.getVenueIds().containsAll(expectedVenueIds));
      assertNull(result.getUsage());
    } else {
      assertEquals(0, result.getVenueIds().size());
    }
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getVlanId).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getSubnetAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getSubnetMask).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getStartIpAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getEndIpAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getLeaseTimeHours).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getLeaseTimeMinutes).count());
  }

  private void assertDhcpPoolUsage(List<DhcpPoolUsage> poolUsages,
      List<DhcpPool> expectedAllDhcpPools, List<DhcpPool> expectedActiveDhcpPools) {
    if (expectedAllDhcpPools == null) {
      assertEquals(0, poolUsages.size());
      return;
    }
    List<String> dhcpPoolIds = expectedAllDhcpPools.stream()
        .map(DhcpPool::getId).toList();
    List<Short> vlanIds = expectedAllDhcpPools.stream()
        .map(DhcpPool::getVlanId).toList();
    List<String> startIpAddress = expectedAllDhcpPools.stream()
        .map(DhcpPool::getStartIpAddress).toList();
    List<String> subnetAddress = expectedAllDhcpPools.stream()
        .map(DhcpPool::getSubnetAddress).toList();
    assertEquals(expectedAllDhcpPools.size(), poolUsages.size(), "Should have dhcpPool");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getId).toList()
        .containsAll(dhcpPoolIds), "dhcpPoolIds should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getVlanId).toList()
        .containsAll(vlanIds), "vlanId should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getStartIpAddress).toList()
        .containsAll(startIpAddress), "startIpAddress should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getSubnetAddress).toList()
        .containsAll(subnetAddress), "subnetAddress should equal test data");

    List<String> activeUsageIds = poolUsages.stream().filter(DhcpPoolUsage::getActive)
        .map(DhcpPoolUsage::getId).toList();
    if (expectedActiveDhcpPools != null) {
      assertEquals(expectedActiveDhcpPools.size(),
          activeUsageIds.size(), "Should have active dhcpPool");
      assertTrue(expectedActiveDhcpPools.stream().map(DhcpPool::getId).toList()
          .containsAll(activeUsageIds));
    } else {
      assertEquals(0, activeUsageIds.size(), "Should have no active dhcpPool");
    }
  }

  private void assertDdccmVenueAndVenueDhcpSetting(List<Operation> operations,
      boolean shouldAssertVenue, boolean expectedEnabled, String dhcpModeEnum,
      List<DhcpPool> expectedDhcpPools, List<DhcpServiceAp> expectedDhcpServiceAps) {

    // assert ddccm - venue
    List<com.ruckus.acx.ddccm.protobuf.wifi.Venue> ddccmVenue = operations.stream()
        .filter(o -> o.getAction().equals(Action.MODIFY))
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUE)).map(Operation::getVenue)
        .collect(Collectors.toList());
    if (shouldAssertVenue) {
      assertEquals(1, ddccmVenue.size(), "Should have one Venue operation send to ddccm");
    } else {
      assertEquals(0, ddccmVenue.size(), "Should have no Venue operation send to ddccm");
    }

    // assert ddccm - VenueDhcpServiceSetting
    List<VenueDhcpServiceSetting> ddccmVenueDhcpServiceSettings = operations.stream()
        .filter(o -> o.getAction().equals(Action.ADD)) // VENUEDHCPSERVICESETTING action is always add
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUEDHCPSERVICESETTING))
        .map(Operation::getVenueDhcpServiceSetting).collect(Collectors.toList());

    assertEquals(1, ddccmVenueDhcpServiceSettings.size(),
        "Should have one VenueDhcpServiceSetting operation send to ddccm");
    VenueDhcpServiceSetting ddccmVenueDhcpServiceSetting = ddccmVenueDhcpServiceSettings.get(0);
    assertEquals(expectedEnabled, ddccmVenueDhcpServiceSetting.getEnabled().getValue(),
        "Ddccm VenueDhcpServiceSetting enabled should be " + expectedEnabled);

    if (expectedEnabled) {
      assertEquals(dhcpModeEnum, ddccmVenueDhcpServiceSetting.getMode(),
          "Ddccm VenueDhcpServiceSetting mode error");
      assertEquals(
          expectedDhcpPools.size(),
          ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().size(),
          "Ddccm VenueDhcpServiceSetting should contain dhcpServiceProfileIds(dhcpPoolIds)");
      assertTrue(
          ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().containsAll(
              expectedDhcpPools.stream().map(DhcpPool::getId).collect(Collectors.toList())),
          "Ddccm VenueDhcpServiceSetting dhcpServiceProfileIds(dhcpPoolIds) should equal test data");

      if (expectedDhcpServiceAps != null) {
        assertEquals(expectedDhcpServiceAps.size(),
            ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
            "Ddccm Should have DhcpServiceAps");
        List<String> expectedIps = expectedDhcpServiceAps.stream()
            .flatMap(ap -> ap.getDhcpIps().stream()).collect(Collectors.toList());
        List<String> actualIps = ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().stream()
            .flatMap(ap -> ap.getNatGatewayIpList().stream()).collect(Collectors.toList());
        assertEquals(expectedIps.size(), actualIps.size());
        assertTrue(actualIps.containsAll(expectedIps),
            "Ddccm VenueDhcpServiceSetting ip should equal test data");
      } else {
        assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
            "Ddccm Should have no DhcpServiceAps");
      }
    } else {
      assertEquals("", ddccmVenueDhcpServiceSetting.getMode());
      assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().size(),
          "Ddccm VenueDhcpServiceSetting should have no dhcpServiceProfileIds(dhcpPoolIds)");
      assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
          "Ddccm Should have no DhcpServiceAps");
    }
  }

  private void assertCmnViewModelCollectorDhcpConfigServiceProfile(
      List<Operations> operations, String venueId, boolean enabled) {
    assertTrue(operations.stream()
        .filter(o -> venueId.equals(o.getId()))
        .allMatch(o -> o.getDocMap().get("dhcp").getStructValue().getFieldsMap()
            .get("enabled").getBoolValue() == enabled));
  }

  private void assertCmnViewModelCollectorDhcpConfigServiceProfile(
      List<Operations> operations, OpType expectedOpType, DhcpConfigServiceProfileDeep expectedServiceProfile) {
    List<Operations> viewModelOperations = operations.stream()
        .filter(o -> expectedServiceProfile.getId().equals(o.getId())).collect(Collectors.toList());
    Operations operation = viewModelOperations.stream()
        .filter(o -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .findFirst().get();
    assertEquals(expectedOpType, operation.getOpType());

    if (!operation.getOpType().equals(OpType.DEL)) {
      assertEquals(expectedServiceProfile.getServiceName(),
          operation.getDocMap().get(Key.NAME).getStringValue());
      assertEquals(expectedServiceProfile.getDhcpPools().size(),
          operation.getDocMap().get(Key.DHCP_POOLS)
              .getListValue().getValuesCount());
      assertEquals(false,
          operation.getDocMap().get(Key.IS_TEMPLATE)
              .getBoolValue());

      List<String> venueIds = operation.getDocMap()
          .get(Key.VENUE_IDS).getListValue().getValuesList().stream()
          .map(Value::getStringValue).collect(Collectors.toList());
      if (expectedServiceProfile.getVenueIds() != null) {
        List<String> expectedVenueIds = expectedServiceProfile.getVenueIds();
        assertEquals(expectedVenueIds.size(), venueIds.size());
        assertTrue(expectedVenueIds.containsAll(venueIds));
      } else {
        assertEquals(0, venueIds.size());
      }
    }
  }

  private void assertDdccmDhcpServiceProfile(
      List<Operation> operations, Action expectedAction, List<DhcpPool> expectedDhcpPools) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile> ddccmDhcpServiceProfiles = operations
        .stream()
        .filter(o -> o.getAction().equals(expectedAction))
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.DHCPSERVICEPROFILE))
        .map(Operation::getDhcpServiceProfile).collect(Collectors.toList());

    // assert dhcpPoolId
    List<String> expectedDhcpPoolIds = expectedDhcpPools.stream().map(DhcpPool::getId)
        .collect(Collectors.toList());
    List<String> actualDhcpPoolIds = ddccmDhcpServiceProfiles.stream()
        .map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getId).collect(Collectors.toList());
    assertEquals(expectedDhcpPoolIds.size(), actualDhcpPoolIds.size());
    assertTrue(actualDhcpPoolIds.containsAll(expectedDhcpPoolIds));

    // assert dhcpPoolName
    List<String> expectedDhcpPoolNames = expectedDhcpPools.stream().map(DhcpPool::getName)
        .collect(Collectors.toList());
    List<String> actualDhcpPoolNames = ddccmDhcpServiceProfiles.stream()
        .map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getName).collect(Collectors.toList());
    assertEquals(expectedDhcpPoolNames.size(), actualDhcpPoolNames.size());
    assertTrue(ddccmDhcpServiceProfiles.stream().map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getName)
        .collect(Collectors.toList()).containsAll(actualDhcpPoolNames));
  }

  private void assertDdccm(List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertCmnViewModelCollector(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

}
