package com.ruckus.cloud.wifi.integration.firmware;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApVersionLabelEnum;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.viewmodel.UpdateApVersionLabelsRequest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeUpdateApVersionLabelsRequestTest extends AbstractRequestTest {
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired
  private MessageCaptors messageCaptors;

  @Test
  public void updateApVersionLabelsRequest(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.100") ApVersion currentBetaVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.101") ApVersion currentAlphaVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.102") ApVersion gaVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.103") ApVersion legacyBetaVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.104") ApVersion targetVersion) {
    // given
    currentBetaVersion.setLabels(List.of(ApVersionLabelEnum.BETA));
    currentAlphaVersion.setLabels(List.of(ApVersionLabelEnum.ALPHA));
    gaVersion.setLabels(List.of(ApVersionLabelEnum.GA));
    legacyBetaVersion.setLabels(List.of(ApVersionLabelEnum.GA, ApVersionLabelEnum.LEGACY_BETA));
    targetVersion.setLabels(List.of(ApVersionLabelEnum.GA));
    repositoryUtil.createOrUpdate(currentBetaVersion, txCtxExtension.getTenantId(), randomTxId());
    repositoryUtil.createOrUpdate(currentAlphaVersion, txCtxExtension.getTenantId(), randomTxId());
    repositoryUtil.createOrUpdate(gaVersion, txCtxExtension.getTenantId(), randomTxId());
    repositoryUtil.createOrUpdate(legacyBetaVersion, txCtxExtension.getTenantId(), randomTxId());
    repositoryUtil.createOrUpdate(targetVersion, txCtxExtension.getTenantId(), randomTxId());

    // when
    UpdateApVersionLabelsRequest request = new UpdateApVersionLabelsRequest();
    request.setLabels(List.of(ApVersionLabelEnum.BETA.toString()));
    messageUtil.sendWifiCfgRequest(
        txCtxExtension.getTenantId(),
        randomTxId(),
        CfgExtendedAction.UPDATE_AP_VERSION_LABELS,
        randomName(),
        new RequestParams().addPathVariable("apFirmwareVersion", targetVersion.getId()),
        request);

    // then
    assertThat(repositoryUtil.find(ApVersion.class, targetVersion.getId()))
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.BETA);
    assertThat(repositoryUtil.find(ApVersion.class, currentBetaVersion.getId()))
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.LEGACY_BETA);
    assertThat(repositoryUtil.find(ApVersion.class, currentAlphaVersion.getId()))
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.ALPHA);
    assertThat(repositoryUtil.find(ApVersion.class, gaVersion.getId()))
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.GA);
    assertThat(repositoryUtil.find(ApVersion.class, legacyBetaVersion.getId()))
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.LEGACY_BETA, ApVersionLabelEnum.GA);
  }
}
