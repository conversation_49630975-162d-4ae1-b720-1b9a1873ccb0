package com.ruckus.cloud.wifi.service.integration;


import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpModeEnum.EnableOnEachAPs;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpModeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.mapper.DhcpConfigServiceProfileMerge;
import com.ruckus.cloud.wifi.mapper.DhcpConfigServiceProfileMergeImpl;
import com.ruckus.cloud.wifi.repository.DhcpConfigServiceProfileRepository;
import com.ruckus.cloud.wifi.repository.DhcpServiceProfileRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.ExtendedApServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.FirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectAlreadyExistException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.ExtendedDhcpConfigServiceProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.ExtendedDhcpServiceProfileVenueServiceCtrlImpl.DefaultGuestNetworkConfig;
import com.ruckus.cloud.wifi.service.validator.DhcpServiceValidator;
import com.ruckus.cloud.wifi.service.validator.SubnetAddressValidator;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.TestPropertySource;

@Tag("DhcpConfigServiceProfileTest")
@WifiJpaDataTest
@TestPropertySource(properties = {"default.dhcp-service-profile.tenant-max-count=2"})
class ExtendedDhcpConfigServiceProfileTest {

  @SpyBean
  private ExtendedDhcpConfigServiceProfileServiceCtrlImpl service;
  @SpyBean
  private DhcpServiceValidator dhcpServiceValidator;
  @Autowired
  private DhcpConfigServiceProfileRepository dhcpConfigServiceProfileRepository;
  @Autowired
  private VenueRepository venueRepository;
  @Autowired
  private DhcpServiceProfileRepository dhcpServiceProfileRepository;
  @Autowired
  private NetworkVenueRepository networkVenueRepository;
  @MockBean
  private SubnetAddressValidator subnetAddressValidator;
  @MockBean
  private FirmwareCapabilityService firmwareCapabilityService;
  @Autowired
  private DhcpConfigServiceProfileMerge dhcpConfigServiceProfileMerge;
  @Autowired
  private DefaultGuestNetworkConfig defaultGuestNetworkConfig;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @MockBean
  private ExtendedApServiceCtrl apServiceCtrl;
  @MockBean
  private ExtendedVenueServiceCtrl venueServiceCtrl;
  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Deprecated(forRemoval = true)
  @Test
  void testGetDhcpConfigServiceProfiles(DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    // When
    List<DhcpConfigServiceProfile> saved = service.getDhcpConfigServiceProfiles(Optional.empty());

    // Then
    assertEquals(1, saved.size());
    assertEquals(dhcpConfigServiceProfile, saved.get(0));
  }

  @Test
  void testGetDhcpConfigServiceProfiles_withPage(
      DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    // When
    List<DhcpConfigServiceProfile> saved = service.getDhcpConfigServiceProfiles(
        Optional.of(PageRequest.of(0, 10)));

    // Then
    assertEquals(1, saved.size());
    assertEquals(dhcpConfigServiceProfile, saved.get(0));
  }

  @Test
  void testGetDhcpConfigServiceProfile(DhcpConfigServiceProfile dhcpConfigServiceProfile)
      throws Exception {
    assertEquals(dhcpConfigServiceProfile,
        service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId()));
  }

  @Test
  void testGetDhcpConfigServiceProfileV1_1(DhcpConfigServiceProfile dhcpConfigServiceProfile)
    throws Exception {
    DhcpConfigServiceProfile response = service
      .getDhcpConfigServiceProfileV1_1(dhcpConfigServiceProfile.getId());
    assertDhcpConfigServiceProfile(dhcpConfigServiceProfile, response);
  }

  @Test
  void testAddDhcpConfigServiceProfile(Tenant tenant) throws Exception {
    testAddDhcpConfigServiceProfile(false);
  }

  @ApplyTemplateFilter
  @Test
  void template_testAddDhcpConfigServiceProfile(Tenant tenant) throws Exception {
    testAddDhcpConfigServiceProfile(true);
  }

  void testAddDhcpConfigServiceProfile(boolean isTemplate) throws Exception {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile =
        DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile(isTemplate);

    // When
    service.addDhcpConfigServiceProfile(copyOf(dhcpConfigServiceProfile));

    // Then
    assertDhcpConfigServiceProfile(dhcpConfigServiceProfile,
        service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId(), true));
  }

  @Test
  void testAddDhcpConfigServiceProfileV1_1(Tenant tenant) throws Exception {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();

    // When
    service.addDhcpConfigServiceProfileV1_1(copyOf(dhcpConfigServiceProfile));

    // Then
    assertDhcpConfigServiceProfile(dhcpConfigServiceProfile,
      service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId()));
  }

  @Test
  void testAddDhcpConfigServiceProfile_dhcpModeIsEnableOnMultipleAp(Tenant tenant) throws Exception {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile.setDhcpMode(DhcpModeEnum.EnableOnMultipleAPs);
    dhcpConfigServiceProfile.getDhcpPools().get(0).setVlanId((short) 1000);

    // When
    service.addDhcpConfigServiceProfile(copyOf(dhcpConfigServiceProfile));

    // Then
    assertDhcpConfigServiceProfile(dhcpConfigServiceProfile,
        service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId()));
  }

  @Test
  void testAddDhcpConfigServiceProfile_withMaxAmount(Tenant tenant) throws Exception {
    // Given
    for (int i = 0; i < 2; i++) {
      DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
      repositoryUtil.createOrUpdate(dhcpConfigServiceProfile, txCtxExtension.getTenantId(),
          txCtxExtension.newRandomId());
    }

    DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    // Then
    assertThrows(CommonException.class,
        () -> service.addDhcpConfigServiceProfile(copyOf(dhcpConfigServiceProfile)));
  }

  @Test
  void testAddDhcpConfigServiceProfile_defaultProfile(Tenant tenant) throws Exception {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile.setServiceName(defaultGuestNetworkConfig.getName());
    // When
    service.addDhcpConfigServiceProfile(copyOf(dhcpConfigServiceProfile));
    // Then
    assertDefaultDhcpConfigServiceProfile(
        service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId()));

    // add exist default profile
    dhcpConfigServiceProfile.setId(txCtxExtension.newRandomId());
    assertThrows(ObjectAlreadyExistException.class,
        () -> service.addDhcpConfigServiceProfile(copyOf(dhcpConfigServiceProfile)));
  }

  @ApplyTemplateFilter
  @Test
  void testAddDhcpConfigServiceProfile_duplicateServiceProfileNameForTemplate(
      DhcpConfigServiceProfile dhcpConfigServiceProfile) throws Exception {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile2 =
        DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    // When
    DhcpConfigServiceProfile profile = copyOf(dhcpConfigServiceProfile2);
    profile.setIsTemplate(true);
    profile.setServiceName(dhcpConfigServiceProfile.getServiceName());
    // Then
    // should be ok to add a template with the same name
    assertDoesNotThrow(() -> service.addDhcpConfigServiceProfile(profile));
  }

  @Test
  void testAddDhcpConfigServiceProfile_duplicateServiceProfileName_fail(
      DhcpConfigServiceProfile dhcpConfigServiceProfile) throws Exception {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile2 =
        DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    // When
    DhcpConfigServiceProfile profile = copyOf(dhcpConfigServiceProfile2);
    profile.setServiceName(dhcpConfigServiceProfile.getServiceName());
    // Then
    assertThrows(ObjectAlreadyExistException.class,
        () -> service.addDhcpConfigServiceProfile(profile));
  }

  @Test
  void testAddDhcpConfigServiceProfile_duplicatePoolName_fail(Tenant tenant) {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile.getDhcpPools().get(0).setName("duplicateName");
    dhcpConfigServiceProfile.getDhcpPools().get(1).setName("duplicateName");
    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> service.addDhcpConfigServiceProfile(dhcpConfigServiceProfile),
        "DHCP Pool name must be unique in all profiles in the Venue");
  }

  @Test
  void testAddDhcpConfigServiceProfile_duplicateVlanId_fail(Tenant tenant) {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile.getDhcpPools().get(0).setVlanId((short) 1);
    dhcpConfigServiceProfile.getDhcpPools().get(1).setVlanId((short) 1);
    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> service.addDhcpConfigServiceProfile(dhcpConfigServiceProfile),
        "VLAN ID must be unique in all profiles in the Venue");
  }

  @Test
  void testAddDhcpConfigServiceProfile_duplicateSubnetAddress_fail(Tenant tenant) {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile.getDhcpPools().get(0).setSubnetAddress("*************");
    dhcpConfigServiceProfile.getDhcpPools().get(1).setSubnetAddress("*************");
    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> service.addDhcpConfigServiceProfile(dhcpConfigServiceProfile),
        "VLAN ID must be unique in all profiles in the Venue");
  }

  @Test
  void testUpdateDhcpConfigServiceProfile(Tenant tenant)
      throws Exception {
    testUpdateDhcpConfigServiceProfile( false);
  }

  @ApplyTemplateFilter
  @Test
  void template_testUpdateDhcpConfigServiceProfile(Tenant tenant)
      throws Exception {
    testUpdateDhcpConfigServiceProfile(true);
  }

  void testUpdateDhcpConfigServiceProfile(boolean isTemplate)
      throws Exception {

    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile =
        DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile(isTemplate);
    dhcpConfigServiceProfile = service.addDhcpConfigServiceProfile(copyOf(dhcpConfigServiceProfile));
    dhcpConfigServiceProfile = copyOf(dhcpConfigServiceProfile);
    dhcpConfigServiceProfile.setServiceName("updatedName");
    // update dhcpPool
    DhcpServiceProfile profile1 = dhcpConfigServiceProfile.getDhcpPools().get(1);
    profile1.setName("pool1");
    // delete dhcpPools
    dhcpConfigServiceProfile.getDhcpPools().stream().filter(e -> e.getVlanId() == 1)
        .findAny().ifPresent(dhcpConfigServiceProfile.getDhcpPools()::remove);
    // add dhcpPool
    DhcpServiceProfile profile4 = Generators.dhcpServiceProfile().generate();
    profile4.setId(null);
    profile4.setName("pool4");
    profile4.setVlanId((short) 100);
    dhcpConfigServiceProfile.getDhcpPools().add(profile4);

    // When
    service.updateDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId(),
        dhcpConfigServiceProfile);

    // Then
    dhcpConfigServiceProfile.setIsTemplate(isTemplate);
    assertDhcpConfigServiceProfile(dhcpConfigServiceProfile,
        service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId(), true));
  }

  @Test
  void testUpdateDhcpConfigServiceProfileV1_1(DhcpConfigServiceProfile dhcpConfigServiceProfile)
    throws Exception {
    // Given
    dhcpConfigServiceProfile = copyOf(dhcpConfigServiceProfile);
    dhcpConfigServiceProfile.setServiceName("updatedName");
    // update dhcpPool
    DhcpServiceProfile profile1 = dhcpConfigServiceProfile.getDhcpPools().get(1);
    profile1.setName("pool1");
    // delete dhcpPools
    dhcpConfigServiceProfile.getDhcpPools().stream().filter(e -> e.getVlanId() == 1)
        .findAny().ifPresent(dhcpConfigServiceProfile.getDhcpPools()::remove);
    // add dhcpPool
    DhcpServiceProfile profile4 = Generators.dhcpServiceProfile().generate();
    profile4.setId(null);
    profile4.setName("pool4");
    profile4.setVlanId((short) 100);
    dhcpConfigServiceProfile.getDhcpPools().add(profile4);

    // When
    service.updateDhcpConfigServiceProfileV1_1(dhcpConfigServiceProfile.getId(),
      dhcpConfigServiceProfile);

    // Then
    assertDhcpConfigServiceProfile(dhcpConfigServiceProfile,
      service.getDhcpConfigServiceProfileV1_1(dhcpConfigServiceProfile.getId()));
  }

  @Test
  void testUpdateDhcpConfigServiceProfile_emptyDhcpPoolId(DhcpConfigServiceProfile dhcpConfigServiceProfile)
      throws Exception {
    // Given
    dhcpConfigServiceProfile = copyOf(dhcpConfigServiceProfile);
    // delete dhcpPools
    var it = dhcpConfigServiceProfile.getDhcpPools().iterator();
    while (it.hasNext()) {
      var pool = it.next();
      if (pool.getVlanId() == (short) 1 || pool.getVlanId() == (short) 2) {
        it.remove();
      }
    }
    // add dhcpPool
    List<DhcpServiceProfile> profiles = Generators.dhcpServiceProfile().generate(2);
    profiles.forEach(p -> p.setId(""));
    dhcpConfigServiceProfile.getDhcpPools().addAll(profiles);

    // When
    service.updateDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId(),
        dhcpConfigServiceProfile);

    // Then
    assertDhcpConfigServiceProfile(dhcpConfigServiceProfile,
        service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId()));
  }

  @Test
  void testUpdateDhcpConfigServiceProfile_updateSameData(
      DhcpConfigServiceProfile dhcpConfigServiceProfile) throws Exception {
    // Given
    dhcpConfigServiceProfile = copyOf(dhcpConfigServiceProfile);

    // When
    service.updateDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId(),
        dhcpConfigServiceProfile);

    // Then
    assertDhcpConfigServiceProfile(dhcpConfigServiceProfile,
        service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId()));
  }

  @Test
  void testUpdateDhcpConfigServiceProfileV1_1_updateSameData(
    DhcpConfigServiceProfile dhcpConfigServiceProfile) throws Exception {
    // Given
    dhcpConfigServiceProfile = copyOf(dhcpConfigServiceProfile);

    // When
    service.updateDhcpConfigServiceProfileV1_1(dhcpConfigServiceProfile.getId(),
      dhcpConfigServiceProfile);

    // Then
    assertDhcpConfigServiceProfile(dhcpConfigServiceProfile,
      service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId()));
  }

  @Test
  void testUpdateDhcpConfigServiceProfile_venueDhcpSettingEnabled_fail(Venue venue,
      DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    // Given
    venue.setDhcpConfigServiceProfile(dhcpConfigServiceProfile);
    repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(),
        txCtxExtension.newRandomId());
    DhcpConfigServiceProfile profile = copyOf(dhcpConfigServiceProfile);

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> service.updateDhcpConfigServiceProfile(profile.getId(), profile));
  }

  @Test
  void testUpdateDhcpConfigServiceProfile_defaultDhcpConfigServiceProfileName_fail(Tenant tenant) {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile.setServiceName(defaultGuestNetworkConfig.getName());
    repositoryUtil.createOrUpdate(dhcpConfigServiceProfile, txCtxExtension.getTenantId(),
        txCtxExtension.newRandomId());
    // When
    DhcpConfigServiceProfile profile = copyOf(dhcpConfigServiceProfile);
    profile.setServiceName("updatedName");
    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> service.updateDhcpConfigServiceProfile(profile.getId(), profile));
  }

  @Test
  void testUpdateDhcpConfigServiceProfile_dhcpConfigServiceProfileNameUnique_fail(
      DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile2 = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile2.setServiceName("dhcpConfigServiceProfileName2");
    dhcpConfigServiceProfile2 = repositoryUtil.createOrUpdate(dhcpConfigServiceProfile2,
        txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    // When
    DhcpConfigServiceProfile profile = copyOf(dhcpConfigServiceProfile2);
    profile.setServiceName(dhcpConfigServiceProfile.getServiceName());
    // Then
    assertThrows(ObjectAlreadyExistException.class,
        () -> service.updateDhcpConfigServiceProfile(profile.getId(), profile));
  }

  @Test
  void testUpdateDhcpConfigServiceProfile_defaultDhcpConfigServiceProfileDhcpMode_fail(
      Tenant tenant) {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile.setServiceName(defaultGuestNetworkConfig.getName());
    repositoryUtil.createOrUpdate(dhcpConfigServiceProfile, txCtxExtension.getTenantId(),
        txCtxExtension.newRandomId());
    // When
    DhcpConfigServiceProfile profile = copyOf(dhcpConfigServiceProfile);
    profile.setDhcpMode(DhcpModeEnum.EnableOnHierarchicalAPs);
    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> service.updateDhcpConfigServiceProfile(profile.getId(), profile));
  }

  @Test
  void testUpdateDhcpConfigServiceProfile_dhcpConfigServiceProfileWithVlanId_fail(
      DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    // When
    DhcpConfigServiceProfile profile = copyOf(dhcpConfigServiceProfile);
    profile.setDhcpMode(DhcpModeEnum.EnableOnMultipleAPs);
    // Then
    assertThrows(CommonException.class,
        () -> service.updateDhcpConfigServiceProfile(profile.getId(), profile));
  }

  @ApplyTemplateFilter
  @Test
  void testDeleteDhcpConfigServiceProfile(Tenant tenant) throws Exception {
    testDeleteDhcpConfigServiceProfile(false);
  }

  @Test
  void template_testDeleteDhcpConfigServiceProfile(Tenant tenant) throws Exception {
    testDeleteDhcpConfigServiceProfile(true);
  }

  void testDeleteDhcpConfigServiceProfile(boolean isTemplate)
      throws Exception {
    DhcpConfigServiceProfile dhcpConfigServiceProfile =
        DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile = service.addDhcpConfigServiceProfile(copyOf(dhcpConfigServiceProfile));
    String dhcpConfigServiceProfileId = dhcpConfigServiceProfile.getId();
    service.deleteDhcpConfigServiceProfile(dhcpConfigServiceProfileId);
    assertThrows(ObjectNotFoundException.class,
        () -> service.getDhcpConfigServiceProfile(dhcpConfigServiceProfileId, true));
  }

  @Test
  void testDeleteDhcpConfigServiceProfileV1_1(Tenant tenant) throws Exception {
    DhcpConfigServiceProfile dhcpConfigServiceProfile =
        DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
    dhcpConfigServiceProfile = service.addDhcpConfigServiceProfileV1_1(copyOf(dhcpConfigServiceProfile));
    String dhcpConfigServiceProfileId = dhcpConfigServiceProfile.getId();
    service.deleteDhcpConfigServiceProfileV1_1(dhcpConfigServiceProfileId);
    assertThrows(ObjectNotFoundException.class,
        () -> service.getDhcpConfigServiceProfile(dhcpConfigServiceProfileId, true));
  }

  @Test
  void testDeleteDhcpConfigServiceProfile_profileInUse(Venue venue,
      DhcpConfigServiceProfile dhcpConfigServiceProfile) throws Exception {
    testDeleteDhcpConfigServiceProfile_profileInUse(venue, dhcpConfigServiceProfile, false);
  }

  @ApplyTemplateFilter
  @Test
  void template_testDeleteDhcpConfigServiceProfile_profileInUse(@Template Venue venue,
      DhcpConfigServiceProfile dhcpConfigServiceProfile) throws Exception {
    testDeleteDhcpConfigServiceProfile_profileInUse(venue, dhcpConfigServiceProfile, true);
  }

  void testDeleteDhcpConfigServiceProfile_profileInUse(Venue venue,
      DhcpConfigServiceProfile dhcpConfigServiceProfile, boolean isTemplate) throws Exception {
    // Given
    dhcpConfigServiceProfile.setIsTemplate(isTemplate);
    venue.setDhcpConfigServiceProfile(dhcpConfigServiceProfile);
    repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(),
        txCtxExtension.newRandomId());
    // Then
    assertEquals(isTemplate,
        service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId(), true)
            .getIsTemplate());
    String dhcpConfigServiceProfileId = dhcpConfigServiceProfile.getId();
    if (isTemplate) {
      assertThrows(InvalidPropertyValueException.class,
          () -> service.deleteDhcpConfigServiceProfileTemplate(dhcpConfigServiceProfileId));
    } else {
      assertThrows(InvalidPropertyValueException.class,
          () -> service.deleteDhcpConfigServiceProfile(dhcpConfigServiceProfileId));
    }
  }

  @Test
  void testDeleteDhcpConfigServiceProfileV1_1_profileInUse(Venue venue,
      DhcpConfigServiceProfile dhcpConfigServiceProfile) throws Exception {
    // Given
    dhcpConfigServiceProfile.setIsTemplate(false);
    venue.setDhcpConfigServiceProfile(dhcpConfigServiceProfile);
    repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(),
        txCtxExtension.newRandomId());
    // Then
    assertEquals(false,
        service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId(), true)
            .getIsTemplate());
    String dhcpConfigServiceProfileId = dhcpConfigServiceProfile.getId();
    assertThrows(InvalidPropertyValueException.class,
        () -> service.deleteDhcpConfigServiceProfileV1_1(dhcpConfigServiceProfileId));
  }

  @Test
  void testDeleteDhcpConfigServiceProfile_defaultProfile(Tenant tenant) throws Exception {
    testDeleteDhcpConfigServiceProfile_defaultProfile(false);
    assertEquals(1,
        repositoryUtil.findAll(DhcpConfigServiceProfile.class, txCtxExtension.getTenantId()).size());
  }

  @ApplyTemplateFilter
  @Test
  void testDeleteDhcpConfigServiceProfile_defaultProfileTemplate(Tenant tenant) throws Exception {
    testDeleteDhcpConfigServiceProfile_defaultProfile(true);
    assertEquals(1,
        repositoryUtil.findAll(DhcpConfigServiceProfile.class, txCtxExtension.getTenantId()).size());
  }

  void testDeleteDhcpConfigServiceProfile_defaultProfile(boolean isTemplate) throws Exception {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile =
        DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile(isTemplate);
    dhcpConfigServiceProfile.setServiceName(defaultGuestNetworkConfig.getName());
    repositoryUtil.createOrUpdate(dhcpConfigServiceProfile, txCtxExtension.getTenantId(),
        txCtxExtension.newRandomId());
    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> service.deleteDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId()));
  }

  @Test
  void testDeleteDhcpConfigServiceProfileV1_1_defaultProfile(Tenant tenant) throws Exception {
    // Given
    DhcpConfigServiceProfile dhcpConfigServiceProfile =
        DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile(false);
    dhcpConfigServiceProfile.setServiceName(defaultGuestNetworkConfig.getName());
    repositoryUtil.createOrUpdate(dhcpConfigServiceProfile, txCtxExtension.getTenantId(),
        txCtxExtension.newRandomId());
    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> service.deleteDhcpConfigServiceProfileV1_1(dhcpConfigServiceProfile.getId()));
  }

  @Test
  void testDeleteDhcpConfigServiceProfiles(DhcpConfigServiceProfile dhcpConfigServiceProfile)
      throws Exception {
    // Given
    service.deleteDhcpConfigServiceProfiles(List.of(dhcpConfigServiceProfile.getId()));
    // Then
    assertThrows(ObjectNotFoundException.class,
        () -> service.getDhcpConfigServiceProfile(dhcpConfigServiceProfile.getId()));
    for (DhcpServiceProfile dhcpPool : dhcpConfigServiceProfile.getDhcpPools()) {
      assertNull(repositoryUtil.find(DhcpServiceProfile.class, dhcpPool.getId()));
    }
  }

  @Test
  void testDeleteDhcpConfigServiceProfiles(Venue venue) throws Exception {
    // Given
    List<DhcpConfigServiceProfile> dhcpConfigServiceProfiles = new ArrayList<>();
    for (int i = 0; i < 5; i++) {
      DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
      repositoryUtil.createOrUpdate(dhcpConfigServiceProfile, txCtxExtension.getTenantId(),
          txCtxExtension.newRandomId());
      dhcpConfigServiceProfiles.add(dhcpConfigServiceProfile);
    }
    venue.setDhcpConfigServiceProfile(dhcpConfigServiceProfiles.get(4));
    repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(),
        txCtxExtension.newRandomId());

    // Then
    List<String> ids = dhcpConfigServiceProfiles.stream().map(DhcpConfigServiceProfile::getId)
        .collect(Collectors.toList());
    assertThrows(InvalidPropertyValueException.class,
        () -> service.deleteDhcpConfigServiceProfiles(ids));
    assertEquals(5, service.getDhcpConfigServiceProfiles(Optional.empty()).size());
  }

  private void assertDefaultDhcpConfigServiceProfile(
      DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    assertEquals(defaultGuestNetworkConfig.getName(), dhcpConfigServiceProfile.getServiceName());
    assertEquals(EnableOnEachAPs, dhcpConfigServiceProfile.getDhcpMode());
    assertEquals(1, dhcpConfigServiceProfile.getDhcpPools().size());
    assertThat(dhcpConfigServiceProfile.getDhcpPools().get(0)).satisfies(pool -> {
      assertEquals(defaultGuestNetworkConfig.getName(), pool.getName());
      assertEquals(defaultGuestNetworkConfig.getDescription(), pool.getDescription());
      assertEquals(defaultGuestNetworkConfig.getVlanId(), pool.getVlanId());
      assertEquals(defaultGuestNetworkConfig.getSubnetAddress(), pool.getSubnetAddress());
      assertEquals(defaultGuestNetworkConfig.getSubnetMask(), pool.getSubnetMask());
      assertEquals(defaultGuestNetworkConfig.getStartIpAddress(), pool.getStartIpAddress());
      assertEquals(defaultGuestNetworkConfig.getEndIpAddress(), pool.getEndIpAddress());
      assertEquals(defaultGuestNetworkConfig.getPrimaryDnsIp(), pool.getPrimaryDnsIp());
      assertEquals(defaultGuestNetworkConfig.getSecondaryDnsIp(), pool.getSecondaryDnsIp());
      assertEquals(defaultGuestNetworkConfig.getLeaseTimeHours(), pool.getLeaseTimeHours());
      assertEquals(defaultGuestNetworkConfig.getLeaseTimeMinutes(), pool.getLeaseTimeMinutes());
    });
  }

  private void assertDhcpConfigServiceProfile(DhcpConfigServiceProfile expected,
      DhcpConfigServiceProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getServiceName(), actual.getServiceName());
    assertEquals(expected.getDhcpMode(), actual.getDhcpMode());
    assertEquals(expected.getIsTemplate(), actual.getIsTemplate());

    for (DhcpServiceProfile expectedProfile : expected.getDhcpPools()) {
      DhcpServiceProfile actualProfile = actual.getDhcpPools().stream()
          .filter(p -> p.getName().equals(expectedProfile.getName())).findFirst().orElse(null);
      assertNotNull(actualProfile);
      assertEquals(expectedProfile.getName(), actualProfile.getName());
      assertEquals(expectedProfile.getDescription(), actualProfile.getDescription());
      assertEquals(expectedProfile.getVlanId(), actualProfile.getVlanId());
      assertEquals(expectedProfile.getSubnetAddress(), actualProfile.getSubnetAddress());
      assertEquals(expectedProfile.getSubnetMask(), actualProfile.getSubnetMask());
      assertEquals(expectedProfile.getStartIpAddress(), actualProfile.getStartIpAddress());
      assertEquals(expectedProfile.getEndIpAddress(), actualProfile.getEndIpAddress());
      assertEquals(expectedProfile.getPrimaryDnsIp(), actualProfile.getPrimaryDnsIp());
      assertEquals(expectedProfile.getSecondaryDnsIp(), actualProfile.getSecondaryDnsIp());
      assertEquals(expectedProfile.getLeaseTimeHours(), actualProfile.getLeaseTimeHours());
      assertEquals(expectedProfile.getLeaseTimeMinutes(), actualProfile.getLeaseTimeMinutes());
      assertEquals(expectedProfile.getDhcpConfigServiceProfile().getId(),
          actualProfile.getDhcpConfigServiceProfile().getId());
    }
  }

  private DhcpConfigServiceProfile copyOf(DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    DhcpConfigServiceProfile copy = new DhcpConfigServiceProfile();
    copy.setId(dhcpConfigServiceProfile.getId());
    copy.setServiceName(dhcpConfigServiceProfile.getServiceName());
    copy.setDhcpMode(dhcpConfigServiceProfile.getDhcpMode());
    copy.setDhcpPools(new ArrayList<>());
    for (DhcpServiceProfile dhcpPool : dhcpConfigServiceProfile.getDhcpPools()) {
      DhcpServiceProfile pool = new DhcpServiceProfile();
      pool.setId(dhcpPool.getId());
      pool.setName(dhcpPool.getName());
      pool.setDescription(dhcpPool.getDescription());
      pool.setVlanId(dhcpPool.getVlanId());
      pool.setSubnetAddress(dhcpPool.getSubnetAddress());
      pool.setSubnetMask(dhcpPool.getSubnetMask());
      pool.setStartIpAddress(dhcpPool.getStartIpAddress());
      pool.setEndIpAddress(dhcpPool.getEndIpAddress());
      pool.setPrimaryDnsIp(dhcpPool.getPrimaryDnsIp());
      pool.setSecondaryDnsIp(dhcpPool.getSecondaryDnsIp());
      pool.setLeaseTimeHours(dhcpPool.getLeaseTimeHours());
      pool.setLeaseTimeMinutes(dhcpPool.getLeaseTimeMinutes());
      copy.getDhcpPools().add(pool);
    }
    return copy;
  }

  @TestConfiguration
  static class testConfig {

    @Bean
    public DhcpConfigServiceProfileMerge dhcpConfigServiceProfileMerge() {
      return new DhcpConfigServiceProfileMergeImpl();
    }

    @Bean
    public DefaultGuestNetworkConfig defaultGuestNetworkConfig() {
      DefaultGuestNetworkConfig config = new DefaultGuestNetworkConfig();
      config.setName("DHCP-Guest");
      config.setVlanId((short) 3000);
      config.setSubnetAddress("************");
      config.setSubnetMask("*************");
      config.setStartIpAddress("************");
      config.setEndIpAddress("**************");
      config.setLeaseTimeHours((short) 12);
      config.setLeaseTimeMinutes((short) 0);
      return config;
    }
  }
}
