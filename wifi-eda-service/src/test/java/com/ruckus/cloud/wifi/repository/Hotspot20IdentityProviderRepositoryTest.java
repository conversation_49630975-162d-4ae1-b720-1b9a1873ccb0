package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.core.model.Identifiable;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenantId00');
    INSERT INTO radius (id, tenant, is_template)
        VALUES ('radiusId01', 'tenantId00', 'false');
    INSERT INTO auth_radius_service(id, tenant, radius)
        VALUES ('authServiceId01', 'tenantId00', 'radiusId01');
    INSERT INTO auth_radius_profile (id, tenant, auth_radius_service)
        VALUES ('authProfileId01', 'tenantId00', 'authServiceId01');
    INSERT INTO hotspot20identity_provider(name, id, tenant, auth_radius, auth_radius_profile)
        VALUES ('ipName', 'ipId01', 'tenantId00', 'radiusId01', 'authProfileId01');
  
    INSERT INTO radius (id, tenant, is_template)
      VALUES ('radiusId02', 'tenantId00', 'false');
    INSERT INTO accounting_radius_service(id, tenant, radius)
      VALUES ('acctServiceId02', 'tenantId00', 'radiusId02');
    INSERT INTO accounting_radius_profile (id, tenant, accounting_radius_service)
      VALUES ('acctProfile02', 'tenantId00', 'acctServiceId02');
    INSERT INTO hotspot20identity_provider(name, id, tenant, accounting_radius, accounting_radius_profile)
      VALUES ('ipName2', 'ipId02', 'tenantId00', 'radiusId02', 'acctProfile02');
      
    INSERT INTO hotspot20identity_provider(name, id, tenant, auth_radius, auth_radius_profile)
      VALUES ('ipName3', 'ipId03', 'tenantId00', 'radiusId01', 'authProfileId01');
     
    INSERT INTO tenant (id) VALUES ('tenantId01');
    INSERT INTO hotspot20identity_provider(name, id, tenant, is_preconfigured)
      VALUES ('ipName4', 'ipId04', 'tenantId00', true);
    INSERT INTO hotspot20identity_provider(name, id, tenant, is_preconfigured)
      VALUES ('ipName5', 'ipId05', 'tenantId01', true);
        INSERT INTO hotspot20identity_provider(name, id, tenant, is_preconfigured)
      VALUES ('ipName6', 'ipId06', 'tenantId01', false);
    """)
class Hotspot20IdentityProviderRepositoryTest {

  @Autowired
  Hotspot20IdentityProviderRepository repository;

  @Test
  void existsByNameAndIsPreconfiguredTrue() {
    assertTrue(repository.existsByNameAndIsPreconfiguredTrue("ipName4")); // ipName4 exists and it is preconfigured IDP
    assertFalse(repository.existsByNameAndIsPreconfiguredTrue("ipName6")); // ipName6 exists and it is not preconfigured IDP
    assertFalse(repository.existsByNameAndIsPreconfiguredTrue("ipName7474"));  // ipName7474 does not exists
  }

  @Test
  void findByTenantIdAndRadiusId() {
    List<String> results = repository.findByTenantIdAndAuthRadiusId("tenantId00", "radiusId01")
        .stream().map(Identifiable::getId).toList();
    List<String> expected = List.of("ipId01", "ipId03");

    assertTrue(expected.containsAll(results));

    assertEquals(Collections.emptyList(),
        repository.findByTenantIdAndAuthRadiusId("tenantId00", "radiusId02")
            .stream().map(Identifiable::getId).toList());

    assertEquals(List.of("ipId02"),
        repository.findByTenantIdAndAccountingRadiusId("tenantId00", "radiusId02")
            .stream().map(Identifiable::getId).toList());

    assertEquals(Collections.emptyList(),
        repository.findByTenantIdAndAccountingRadiusId("tenantId00", "radiusId01")
            .stream().map(Identifiable::getId).toList());

    assertEquals(List.of("ipId02"),
        repository.findByTenantIdAndRadiusId("tenantId00", "radiusId02")
            .stream().map(Identifiable::getId).toList());
  }

  @Test
  void testFindByNameAndIdNot() {
    assertFalse(repository.existsByTenantIdAndNameAndIdNot("tenantId00", "ipName3", "ipId03"));
    assertFalse(repository.existsByTenantIdAndNameAndIdNot("tenantId00", "ipName1", "ipId01"));
    assertTrue(repository.existsByTenantIdAndNameAndIdNot("tenantId00", "ipName3", "ipId01"));
    assertTrue(repository.existsByTenantIdAndNameAndIdNot("tenantId00", "ipName2", "ipId01"));
  }

  @Test
  void existsByTenantIdAndNameAndIdNot() {
    assertTrue(repository.existsByTenantIdAndNameAndId("tenantId00", "ipName", "ipId01"));
    assertFalse(repository.existsByTenantIdAndNameAndId("tenantId00", "wrongName", "ipId01"));
    assertFalse(repository.existsByTenantIdAndNameAndId("tenantId00", "ipName", "ipId4444"));

    assertTrue(repository.existsByTenantIdAndName("tenantId00", "ipName"));
    assertFalse(repository.existsByTenantIdAndName("tenantId00", "wrongName"));
  }

  @Test
  void testFindByIsPreconfiguredTrue() {
    assertThat(repository.findByIsPreconfiguredTrue())
        .hasSize(2)
        .extracting(AbstractBaseEntity::getId)
        .containsExactlyInAnyOrder("ipId04", "ipId05");
  }

  @Test
  void testByIdAndTenantIdAndIsPreconfiguredFalse() {
    assertEquals("ipId01",
        repository.findByIdAndTenantIdAndIsPreconfiguredFalse("ipId01", "tenantId00")
            .map(AbstractBaseEntity::getId)
            .orElseThrow(() -> new AssertionError("Entity not found"))); // isPreconfigured : null
    assertEquals("ipId06",
        repository.findByIdAndTenantIdAndIsPreconfiguredFalse("ipId06", "tenantId01")
            .map(AbstractBaseEntity::getId)
            .orElseThrow(() -> new AssertionError("Entity not found"))); // isPreconfigured : false
    assertFalse(repository.findByIdAndTenantIdAndIsPreconfiguredFalse("ipId01", "tenantId01").isPresent()); // wrong tenant
    assertFalse(repository.findByIdAndTenantIdAndIsPreconfiguredFalse("ipId04", "tenantId00").isPresent()); // isPreconfigured : true
  }

  @Test
  void testFindByIdAndPreconfiguredTrue() {
    assertThat(repository.findByIdAndIsPreconfiguredTrue("ipId04"))
        .map(AbstractBaseEntity::getId)
        .hasValue("ipId04");
    assertFalse(repository.findByIdAndIsPreconfiguredTrue("ipId03").isPresent());
  }
}