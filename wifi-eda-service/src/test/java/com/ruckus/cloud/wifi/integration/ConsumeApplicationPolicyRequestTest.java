package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_APPLICATION_POLICY_ON_ACCESS_CONTROL_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DEACTIVATE_APPLICATION_POLICY_ON_ACCESS_CONTROL_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DEACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_APPLICATION_POLICIES;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_APPLICATION_POLICY;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value.TYPE_APPLICATION_POLICY;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApplicationType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.ApplicationPolicyRestCtrl.ApplicationPolicyMapper;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApplicationRuleTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApplicationTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.ApplicationPolicyGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.ApplicationPolicyV1_1Generator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.fixture.ApplicationPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

;

@Tag("ApplicationPolicyTest")
@WifiIntegrationTest
public class ConsumeApplicationPolicyRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  protected MessageCaptors messageCaptors;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_APPLICATION_POLICY)
  class ConsumeAddApplicationPolicyRequestTest {

    @Payload
    private final ApplicationPolicyGenerator applicationPolicy = Generators.applicationPolicy();

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicy payload) {
      final ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
          payload.getId());
      assertNotNull(applicationPolicy);
      List<String> userDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());

      validateRepositoryData(payload, applicationPolicy);
      validateDdccmCfgRequestMessages(Action.ADD, List.of(applicationPolicy.getId()),
          Map.of(Action.ADD, userDefinedIds), applicationPolicy);
      validateCmnCfgCollectorMessages(OpType.ADD, List.of(applicationPolicy.getId()),
          applicationPolicy, null, false);
      validateActivityMessages(ApiFlowNames.ADD_APPLICATION_POLICY);
    }
  }

  @Nested
  @ApiAction(CfgAction.ADD_APPLICATION_POLICY_V1_1)
  class ConsumeAddApplicationPolicyV1_1RequestTest {

    @Payload
    private final ApplicationPolicyV1_1Generator applicationPolicy = Generators.applicationPolicyV1_1();

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicyV1_1 payload) {
      final ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
          payload.getId());
      assertNotNull(applicationPolicy);
      List<String> userDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());

      validateRepositoryData(payload, applicationPolicy);
      validateDdccmCfgRequestMessages(Action.ADD, List.of(applicationPolicy.getId()),
          Map.of(Action.ADD, userDefinedIds), applicationPolicy);
      validateCmnCfgCollectorMessages(OpType.ADD, List.of(applicationPolicy.getId()),
          applicationPolicy, null, false);
      validateActivityMessages(ApiFlowNames.ADD_APPLICATION_POLICY);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_APPLICATION_POLICY)
  class ConsumeUpdateApplicationPolicyRequestTest {

    @Payload
    private final ApplicationPolicyGenerator applicationPolicy = Generators.applicationPolicy();

    private String applicationPolicyId;
    private ApplicationPolicy originalApplicationPolicy;

    private List<String> originalUserDefinedIds;

    @BeforeEach
    void givenOneApplicationPolicyPersistedInDb(final Tenant tenant,
        ApplicationPolicy applicationPolicy) {
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
          .generate();
      accessControlProfile.setApplicationPolicy(applicationPolicy);
      accessControlProfile.setApplicationPolicyEnable(true);
      repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());
      applicationPolicyId = applicationPolicy.getId();
      originalApplicationPolicy = applicationPolicy;
      originalUserDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("applicationPolicyId", applicationPolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicy payload) {
      final ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
          applicationPolicyId);
      payload.setId(applicationPolicyId);
      List<String> userDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());

      validateRepositoryData(payload, applicationPolicy);
      validateDdccmCfgRequestMessages(Action.MODIFY, List.of(applicationPolicy.getId()),
          Map.of(Action.ADD, userDefinedIds, Action.DELETE, originalUserDefinedIds),
          applicationPolicy);
      validateCmnCfgCollectorMessages(OpType.MOD, List.of(applicationPolicy.getId()),
          applicationPolicy, null, true);
      validateActivityMessages(ApiFlowNames.UPDATE_APPLICATION_POLICY);
    }

    @Payload("onlyModifyRules")
    com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicy applicationPolicy() {
      com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicy ap1 = ApplicationPolicyMapper.INSTANCE.ServiceApplicationPolicy2ApplicationPolicy(
          originalApplicationPolicy);
      ap1.getRules().get(0).setName("newName");
      return ap1;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_APPLICATION_POLICY, payload = @Payload("onlyModifyRules"))
    void thenShouldHandleTheRequestSuccessfully_onlyModifyRules(
        @Payload("onlyModifyRules") com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicy payload) {
      final ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
          applicationPolicyId);
      payload.setId(applicationPolicyId);
      List<String> userDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());

      validateRepositoryData(payload, applicationPolicy);
      validateDdccmCfgRequestMessages(Action.MODIFY, List.of(applicationPolicy.getId()),
          null,
          applicationPolicy);
      validateCmnCfgCollectorMessages(OpType.MOD, List.of(applicationPolicy.getId()),
          applicationPolicy, null, false);
      validateActivityMessages(ApiFlowNames.UPDATE_APPLICATION_POLICY);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_APPLICATION_POLICY_V1_1)
  class ConsumeUpdateApplicationPolicyV1_1RequestTest {

    @Payload
    private final ApplicationPolicyV1_1Generator applicationPolicy = Generators.applicationPolicyV1_1();

    private String applicationPolicyId;

    private ApplicationPolicy originalApplicationPolicy;

    private List<String> originalUserDefinedIds;

    @BeforeEach
    void givenOneApplicationPolicyPersistedInDb(final Tenant tenant,
        ApplicationPolicy applicationPolicy) {
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
          .generate();
      accessControlProfile.setApplicationPolicy(applicationPolicy);
      accessControlProfile.setApplicationPolicyEnable(true);
      repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());
      applicationPolicyId = applicationPolicy.getId();
      originalApplicationPolicy = applicationPolicy;
      originalUserDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("applicationPolicyId", applicationPolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicyV1_1 payload) {
      final ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
          applicationPolicyId);
      payload.setId(applicationPolicyId);
      List<String> userDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());

      validateRepositoryData(payload, applicationPolicy);
      validateDdccmCfgRequestMessages(Action.MODIFY, List.of(applicationPolicy.getId()),
          Map.of(Action.ADD, userDefinedIds, Action.DELETE, originalUserDefinedIds),
          applicationPolicy);
      validateCmnCfgCollectorMessages(OpType.MOD, List.of(applicationPolicy.getId()),
          applicationPolicy, null, true);
      validateActivityMessages(ApiFlowNames.UPDATE_APPLICATION_POLICY);
    }

    @Payload("onlyModifyRules")
    com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicyV1_1 applicationPolicy() {
      com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicyV1_1 ap1 = ApplicationPolicyMapper.INSTANCE.ServiceApplicationPolicy2ApplicationPolicyV1_1(
          originalApplicationPolicy);
      ap1.getRules().get(0).setName("newName");
      return ap1;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_APPLICATION_POLICY, payload = @Payload("onlyModifyRules"))
    void thenShouldHandleTheRequestSuccessfully_onlyModifyRules(
        @Payload("onlyModifyRules") com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicyV1_1 payload) {
      final ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
          applicationPolicyId);
      payload.setId(applicationPolicyId);
      List<String> userDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());

      validateRepositoryData(payload, applicationPolicy);
      validateDdccmCfgRequestMessages(Action.MODIFY, List.of(applicationPolicy.getId()),
          null,
          applicationPolicy);
      validateCmnCfgCollectorMessages(OpType.MOD, List.of(applicationPolicy.getId()),
          applicationPolicy, null, false);
      validateActivityMessages(ApiFlowNames.UPDATE_APPLICATION_POLICY);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_APPLICATION_POLICY)
  class ConsumeDeleteApplicationPolicyRequestTest {

    private String applicationPolicyId;

    private List<String> userDefinedIds;

    @BeforeEach
    void givenOneApplicationPolicyPersistedInDb(final ApplicationPolicy applicationPolicy) {
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
          .generate();
      accessControlProfile.setApplicationPolicy(applicationPolicy);
      accessControlProfile.setApplicationPolicyEnable(true);
      repositoryUtil.createOrUpdate(accessControlProfile, applicationPolicy.getTenant().getId(),
          randomTxId());
      applicationPolicyId = applicationPolicy.getId();
      userDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("applicationPolicyId", applicationPolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      assertNull(repositoryUtil.find(ApplicationPolicy.class, applicationPolicyId));
      validateDdccmCfgRequestMessages(Action.DELETE, List.of(applicationPolicyId),
          Map.of(Action.DELETE, userDefinedIds), null);
      validateCmnCfgCollectorMessages(OpType.DEL, List.of(applicationPolicyId), null, null, true);
      validateActivityMessages(DELETE_APPLICATION_POLICY);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_APPLICATION_POLICY_V1_1)
  class ConsumeDeleteApplicationPolicyV1_1RequestTest {

    private String applicationPolicyId;

    private List<String> userDefinedIds;

    @BeforeEach
    void givenOneApplicationPolicyPersistedInDb(final ApplicationPolicy applicationPolicy) {
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
          .generate();
      accessControlProfile.setApplicationPolicy(applicationPolicy);
      accessControlProfile.setApplicationPolicyEnable(true);
      repositoryUtil.createOrUpdate(accessControlProfile, applicationPolicy.getTenant().getId(),
          randomTxId());
      applicationPolicyId = applicationPolicy.getId();
      userDefinedIds = applicationPolicy.getRules().stream()
          .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
          .map(ApplicationPolicyRule::getId).collect(Collectors.toList());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("applicationPolicyId", applicationPolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      assertNull(repositoryUtil.find(ApplicationPolicy.class, applicationPolicyId));
      validateDdccmCfgRequestMessages(Action.DELETE, List.of(applicationPolicyId),
          Map.of(Action.DELETE, userDefinedIds), null);
      validateCmnCfgCollectorMessages(OpType.DEL, List.of(applicationPolicyId), null, null, true);
      validateActivityMessages(DELETE_APPLICATION_POLICY);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_APPLICATION_POLICIES)
  class ConsumeDeleteApplicationPoliciesRequestTest {

    @Payload
    private final List<String> applicationPolicyIds = new ArrayList<>();

    private final List<String> userDefinedRuleIdList = new ArrayList<>();

    @BeforeEach
    void givenTenApplicationPoliciesPersistedInDb(final Tenant tenant,
        ApplicationPolicy applicationPolicy) {
      for (int i = 0; i < 9; i++) {
        applicationPolicy = ApplicationPolicyTestFixture.applicationPolicy();
        applicationPolicy = repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(),
            randomTxId());
        applicationPolicyIds.add(applicationPolicy.getId());
        userDefinedRuleIdList.addAll(applicationPolicy.getRules().stream()
            .filter(rule -> rule.getRuleType() == ApplicationTypeEnum.USER_DEFINED)
            .map(ApplicationPolicyRule::getId).collect(Collectors.toList()));
      }
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
          .generate();
      accessControlProfile.setApplicationPolicy(applicationPolicy);
      accessControlProfile.setApplicationPolicyEnable(true);
      repositoryUtil.createOrUpdate(accessControlProfile, applicationPolicy.getTenant().getId(),
          randomTxId());
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload List<String> payload) {
      assertThat(applicationPolicyIds).extracting(
              applicationPolicyId -> repositoryUtil.find(ApplicationPolicy.class, applicationPolicyId))
          .isNotEmpty().allMatch(Objects::isNull);
      validateDdccmCfgRequestMessages(Action.DELETE, applicationPolicyIds,
          Map.of(Action.DELETE, userDefinedRuleIdList), null);
      validateCmnCfgCollectorMessages(OpType.DEL, applicationPolicyIds, null, null, true);
      validateActivityMessages(DELETE_APPLICATION_POLICIES);
    }
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK)
  class ConsumeActivateApplicationPolicyOnWifiNetworkRequestTest {

    private String applicationPolicyId;

    private String wifiNetworkId;

    @BeforeEach
    void givenOneApplicationPolicyPersistedInDb(final Tenant tenant,
        final ApplicationPolicy applicationPolicy) {
      final var network = NetworkTestFixture.randomNetwork(tenant);
      Network wifiNetwork = repositoryUtil.createOrUpdate(network,
          txCtxExtension.getTenantId(), randomTxId());
      ApplicationPolicy applicationPolicy1 = repositoryUtil.createOrUpdate(applicationPolicy,
          txCtxExtension.getTenantId(), randomTxId());
      applicationPolicyId = applicationPolicy1.getId();
      wifiNetworkId = wifiNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId)
          .addPathVariable("applicationPolicyId", applicationPolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      Network wifiNetwork = repositoryUtil.find(Network.class, wifiNetworkId);
      ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
          applicationPolicyId);

      assertNotNull(applicationPolicy);
      assertNotNull(wifiNetwork);

      assertTrue(wifiNetwork.getWlan().getAdvancedCustomization().getApplicationPolicyEnable());
      assertEquals(wifiNetwork.getWlan().getAdvancedCustomization().getApplicationPolicy().getId(),
          applicationPolicyId);

      validateCmnCfgCollectorMessages(OpType.MOD, List.of(applicationPolicy.getId()),
          applicationPolicy, wifiNetworkId, false);
      validateActivityMessages(ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK);
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK)
  class ConsumeDeactivateApplicationPolicyOnWifiNetworkRequestTest {

    private String applicationPolicyId;

    private String wifiNetworkId;

    @BeforeEach
    void givenOneApplicationPolicyPersistedInDb(final Tenant tenant,
        final ApplicationPolicy applicationPolicy) {
      ApplicationPolicy savedApplicationPolicy = repositoryUtil.createOrUpdate(applicationPolicy,
          txCtxExtension.getTenantId(), randomTxId());
      final var network = NetworkTestFixture.randomNetwork(tenant);
      network.getWlan().getAdvancedCustomization().setApplicationPolicyEnable(true);
      network.getWlan().getAdvancedCustomization().setApplicationPolicy(savedApplicationPolicy);
      Network wifiNetwork = repositoryUtil.createOrUpdate(network,
          txCtxExtension.getTenantId(), randomTxId());
      applicationPolicyId = savedApplicationPolicy.getId();
      wifiNetworkId = wifiNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId)
          .addPathVariable("applicationPolicyId", applicationPolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      Network wifiNetwork = repositoryUtil.find(Network.class, wifiNetworkId);
      ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
          applicationPolicyId);

      assertNotNull(applicationPolicy);
      assertNotNull(wifiNetwork);

      assertFalse(wifiNetwork.getWlan().getAdvancedCustomization().getApplicationPolicyEnable());
      assertNull(wifiNetwork.getWlan().getAdvancedCustomization().getApplicationPolicy());

      validateCmnCfgCollectorMessages(OpType.MOD, List.of(applicationPolicy.getId()),
          applicationPolicy, null, false);
      validateActivityMessages(DEACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK);
    }
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_APPLICATION_POLICY_ON_ACCESS_CONTROL_PROFILE)
  class ConsumeActivateApplicationPolicyOnAccessControlProfileRequestTest {

    private String applicationPolicyId;

    private String accessControlProfileId;

    @BeforeEach
    void givenOneApplicationPolicyPersistedInDb(
        final ApplicationPolicy applicationPolicy) {
      ApplicationPolicy savedApplicationPolicy = repositoryUtil.createOrUpdate(applicationPolicy,
          txCtxExtension.getTenantId(), randomTxId());
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
          .generate();
      accessControlProfile.setApplicationPolicy(null);
      accessControlProfile.setApplicationPolicyEnable(false);
      repositoryUtil.createOrUpdate(accessControlProfile, txCtxExtension.getTenantId(),
          randomTxId());
      applicationPolicyId = savedApplicationPolicy.getId();
      accessControlProfileId = accessControlProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("accessControlProfileId", accessControlProfileId)
          .addPathVariable("applicationPolicyId", applicationPolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      AccessControlProfile accessControlProfile = repositoryUtil.find(AccessControlProfile.class, accessControlProfileId);
      ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
          applicationPolicyId);

      assertNotNull(applicationPolicy);
      assertNotNull(accessControlProfile);

      assertTrue(accessControlProfile.getApplicationPolicyEnable());
      assertEquals(accessControlProfile.getApplicationPolicy().getId(), applicationPolicyId);

      validateCmnCfgCollectorMessages(OpType.MOD, Collections.emptyList(),
          applicationPolicy, null, true);
      validateActivateAccessControlProfileDdccmCfgRequestMessage(Action.MODIFY, applicationPolicy, true);
      validateActivityMessages(ACTIVATE_APPLICATION_POLICY_ON_ACCESS_CONTROL_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_APPLICATION_POLICY_ON_ACCESS_CONTROL_PROFILE)
  class ConsumeDeactivateApplicationPolicyOnAccessControlProfileRequestTest {

    private String applicationPolicyId;

    private String accessControlProfileId;

    @BeforeEach
    void givenOneApplicationPolicyPersistedInDb(
            final ApplicationPolicy applicationPolicy) {
      ApplicationPolicy savedApplicationPolicy = repositoryUtil.createOrUpdate(applicationPolicy,
              txCtxExtension.getTenantId(), randomTxId());
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
              .generate();
      accessControlProfile.setApplicationPolicy(savedApplicationPolicy);
      accessControlProfile.setApplicationPolicyEnable(true);
      repositoryUtil.createOrUpdate(accessControlProfile, txCtxExtension.getTenantId(),
              randomTxId());
      applicationPolicyId = savedApplicationPolicy.getId();
      accessControlProfileId = accessControlProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("accessControlProfileId", accessControlProfileId)
              .addPathVariable("applicationPolicyId", applicationPolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      AccessControlProfile accessControlProfile = repositoryUtil.find(AccessControlProfile.class, accessControlProfileId);
      ApplicationPolicy applicationPolicy = repositoryUtil.find(ApplicationPolicy.class,
              applicationPolicyId);

      assertNotNull(applicationPolicy);
      assertNotNull(accessControlProfile);

      assertFalse(accessControlProfile.getApplicationPolicyEnable());
      assertNull(accessControlProfile.getApplicationPolicy());

      applicationPolicy.setId("");
      applicationPolicy.setName("");
      validateCmnCfgCollectorMessages(OpType.MOD, Collections.emptyList(),
              applicationPolicy, null, true);
      validateActivateAccessControlProfileDdccmCfgRequestMessage(
              Action.MODIFY, applicationPolicy, false);
      validateActivityMessages(DEACTIVATE_APPLICATION_POLICY_ON_ACCESS_CONTROL_PROFILE);
    }
  }

  private void validateNothingHasHappened(CfgAction action) {
    final TxChanges txChanges = revisionService.changes(txCtxExtension.getRequestId(),
        txCtxExtension.getTenantId(), action.key());
    assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
    messageCaptors.assertThat(
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest(),
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(txCtxExtension.getTenantId());
  }

  boolean validateRepositoryData(com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicy expected,
      ApplicationPolicy actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getRules().size(), actual.getRules().size());
    for (com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicyRule expectedRule : expected.getRules()) {
      ApplicationPolicyRule applicationPolicyRule = actual.getRules().stream()
          .filter(rule -> expectedRule.getName().equals(rule.getName())).findFirst().orElse(null);
      assertApplicationPolicyRule(expectedRule, applicationPolicyRule);
    }
    return true;
  }

  boolean validateRepositoryData(com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicyV1_1 expected,
      ApplicationPolicy actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getRules().size(), actual.getRules().size());
    for (com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicyRule expectedRule : expected.getRules()) {
      ApplicationPolicyRule applicationPolicyRule = actual.getRules().stream()
          .filter(rule -> expectedRule.getName().equals(rule.getName())).findFirst().orElse(null);
      assertApplicationPolicyRule(expectedRule, applicationPolicyRule);
    }
    return true;
  }

  private void assertApplicationPolicyRule(
      com.ruckus.cloud.wifi.eda.viewmodel.ApplicationPolicyRule expected,
      ApplicationPolicyRule actual) {
    assertNotNull(actual);
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getRuleType(), actual.getRuleType());
    assertEquals(expected.getAccessControl(), actual.getAccessControl());
    assertEquals(expected.getPriority(), actual.getPriority());
    assertEquals(expected.getCategory(), actual.getCategory());
    assertEquals(expected.getCategoryId(), actual.getCategoryId());
    assertEquals(expected.getApplicationName(), actual.getApplicationName());
    assertEquals(expected.getApplicationId(), actual.getApplicationId());
    assertEquals(expected.getDownlink(), actual.getDownlink());
    assertEquals(expected.getUplink(), actual.getUplink());
    assertEquals(expected.getMarkingPriority(), actual.getMarkingPriority());
    assertEquals(expected.getUpLinkMarkingType(), actual.getUpLinkMarkingType());
    assertEquals(expected.getDownLinkMarkingType(), actual.getDownLinkMarkingType());
    assertEquals(expected.getPortMapping(), actual.getPortMapping());
    assertEquals(expected.getDestinationIp(), actual.getDestinationIp());
    assertEquals(expected.getNetmask(), actual.getNetmask());
    assertEquals(expected.getDestinationPort(), actual.getDestinationPort());
    assertEquals(expected.getProtocol(), actual.getProtocol());
  }

  private void validateActivateAccessControlProfileDdccmCfgRequestMessage(
          Action accessControlProfileAction, ApplicationPolicy applicationPolicy, boolean isActivate) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
            .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
            .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
            .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
            .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> {
      assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                      WifiConfigRequest::getOperationsList).asList().isNotEmpty()
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast).satisfies(ops -> {
                assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                                com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                        .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                        .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                        .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
                assertThat(ops).filteredOn(
                                com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasCcmFirewallProfile)
                        .hasSize(1).first()
                        .matches(op -> op.getAction() == accessControlProfileAction)
                        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCcmFirewallProfile)
                        .matches(op -> {
                          if (isActivate) {
                            return op.hasFirewallQmApplicationPolicy() &&
                                    op.getFirewallQmApplicationPolicy().getQmApplicationPolicyId().getValue().equals(applicationPolicy.getId()) &&
                                    op.getFirewallQmApplicationPolicy().getEnabled().getValue();
                          } else {
                            return !op.hasFirewallQmApplicationPolicy();
                          }
                        });
              });
    });
  }

  private void validateDdccmCfgRequestMessages(Action applicationPolicyAction,
      List<String> applicationPolicyIdList, Map<Action, List<String>> userDefinedRuleIdMap,
      ApplicationPolicy payload) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> {
      assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
              WifiConfigRequest::getOperationsList).asList().isNotEmpty()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast).satisfies(ops -> {
            assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                    com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
            assertThat(ops).filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApplicationPolicy)
                .hasSize(applicationPolicyIdList.size())
                .allMatch(op -> applicationPolicyIdList.contains(op.getId())).allSatisfy(op -> {
                  assertEquals(applicationPolicyAction, op.getAction());
                  if (op.getAction() == Action.DELETE) {
                    assertThat(op).extracting(
                            com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApplicationPolicy)
                        .matches(vp -> applicationPolicyIdList.contains(vp.getId()));
                  } else {
                    assertThat(payload).isNotNull();
                    assertThat(op).extracting(
                            com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApplicationPolicy)
                        .matches(ap -> applicationPolicyIdList.contains(ap.getId()))
                        .matches(ap -> payload.getName().equals(ap.getName())).extracting(
                            com.ruckus.acx.ddccm.protobuf.wifi.ApplicationPolicy::getApplicationPolicyRulesList)
                        .satisfies(
                            applicationPolicyRules -> assertDdccmApplicationPolicyRules(payload.getRules(),
                                applicationPolicyRules));
                  }
                });
            if (userDefinedRuleIdMap != null) {
              assertThat(ops).filteredOn(
                      com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasUserDefinedApplicationRule)
                  .hasSize(userDefinedRuleIdMap.values().stream().mapToInt(List::size).sum())
                  .allSatisfy(op -> {
                    if (op.getAction() == Action.DELETE) {
                      assertThat(op).extracting(
                              com.ruckus.acx.ddccm.protobuf.wifi.Operation::getUserDefinedApplicationRule)
                          .matches(
                              vp -> userDefinedRuleIdMap.get(Action.DELETE).contains(vp.getId()));
                    } else {
                      assertThat(payload).isNotNull();
                      ApplicationPolicyRule rule = payload.getRules().stream()
                          .filter(rule1 -> rule1.getId().equals(op.getId())).findFirst().get();
                      assertThat(op).extracting(
                              com.ruckus.acx.ddccm.protobuf.wifi.Operation::getUserDefinedApplicationRule)
                          .matches(ap -> rule.getApplicationName().equals(ap.getAppName()))
                          .matches(ap -> rule.getId().equals(ap.getId()))
                          .matches(ap -> rule.getApplicationId().equals(ap.getAppId()))
                          .matches(ap -> rule.getTenant().getId().equals(ap.getTenantId()))
                          .matches(ap -> rule.getDestinationIp()
                              .equals(ap.getDestinationIp().getValue()))
                          .matches(
                              ap -> rule.getDestinationPort()
                                  .equals(ap.getDestinationPort().getValue()))
                          .matches(ap -> rule.getNetmask().equals(ap.getNetmask().getValue()));
                    }
                  });
            }
            if (applicationPolicyAction == Action.DELETE) {
              assertThat(ops).filteredOn(
                      com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasCcmFirewallProfile)
                  .hasSize(1).first()
                  .matches(op -> op.getAction() == Action.MODIFY)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCcmFirewallProfile)
                  .matches(op -> !op.hasFirewallQmApplicationPolicy());
            }
          });
    });
  }

  private void assertDdccmApplicationPolicyRules(List<ApplicationPolicyRule> rules,
      List<com.ruckus.acx.ddccm.protobuf.wifi.ApplicationPolicyRule> ddccmRules) {
    assertEquals(rules.size(), ddccmRules.size());
    for (com.ruckus.acx.ddccm.protobuf.wifi.ApplicationPolicyRule ddccmRule : ddccmRules) {
      ApplicationPolicyRule rule = rules.stream().filter(
              rule1 -> rule1.getApplicationName().equals(ddccmRule.getApplicationName().getValue()))
          .findFirst().get();

      assertEquals(rule.getPriority(), ddccmRule.getPriority().getValue());
      assertEquals(rule.getApplicationId(), ddccmRule.getApplicationId().getValue());

      if (ddccmRule.getApplicationType() == ApplicationType.ApplicationType_SIGNATURE) {
        assertEquals(rule.getCategory(), ddccmRule.getCategoryName().getValue());
        assertEquals(rule.getCategoryId(), ddccmRule.getCategoryId().getValue());
      } else {
        assertEquals(ApplicationType.ApplicationType_USER_DEFINED, ddccmRule.getApplicationType());
        assertEquals("User Defined", ddccmRule.getCategoryName().getValue());
        assertEquals(32768, ddccmRule.getCategoryId().getValue());
        if (rule.getAccessControl() == ApplicationRuleTypeEnum.RATE_LIMIT) {
          assertEquals(rule.getDownlink(), ddccmRule.getDownlink().getValue());
          assertEquals(rule.getUplink(), ddccmRule.getUplink().getValue());
        }
      }
    }
  }

  private void validateCmnCfgCollectorMessages(OpType applicationPolicyOpType, List<String> idList,
      ApplicationPolicy payload, String wifiNetworkId, boolean assertAccessControl) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast).satisfies(ops -> {
              assertThat(ops).filteredOn(
                      op -> Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME.equals(op.getIndex()))
                  .hasSize(idList.size()).allMatch(op -> idList.contains(op.getId()))
                  .allMatch(op -> op.getOpType() == applicationPolicyOpType).allSatisfy(op -> {
                    if (op.getOpType() != OpType.DEL) {
                      assertThat(op).extracting(Operations::getDocMap)
                          .matches(doc -> idList.contains(doc.get(EsConstants.Key.ID).getStringValue()))
                          .matches(doc -> tenantId.equals(
                              doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                          .matches(doc -> payload.getName().equals(doc.get(Key.NAME).getStringValue()))
                          .matches(
                              doc -> TYPE_APPLICATION_POLICY.equals(doc.get(Key.TYPE)))
                          .matches(doc -> {
                            if (payload.getDescription() != null) {
                              return payload.getDescription()
                                  .equals(doc.get(Key.DESCRIPTION).getStringValue());
                            } else {
                              return doc.get(Key.DESCRIPTION).getStringValue().isBlank();
                            }
                          })
                          .matches(
                              doc -> payload.getRules().size() == (doc.get(Key.RULES).getNumberValue()))
                          .matches(
                              doc -> {
                                if (wifiNetworkId == null) {
                                  return 0 == doc.get(Key.NETWORK_IDS).getListValue()
                                      .getValuesCount();
                                } else {
                                  return doc.get(Key.NETWORK_IDS).getListValue().getValuesList()
                                      .stream().map(
                                          Value::getStringValue).toList().contains(wifiNetworkId);
                                }
                              }
                          );
                    }
                  });
              if (assertAccessControl) {
                assertThat(ops).filteredOn(
                        op -> Index.ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
                    .hasSize(1)
                    .allMatch(op -> op.getOpType() == OpType.MOD).allSatisfy(op -> {
                      if (applicationPolicyOpType == OpType.DEL) {
                        assertThat(op)
                            .extracting(Operations::getDocMap)
                            .matches(
                                doc -> tenantId.equals(
                                    doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                            .matches(
                                doc -> doc.get(Key.APPLICATION_POLICY_NAME).getStringValue().isEmpty())
                            .matches(
                                doc -> doc.get(Key.APPLICATION_POLICY_ID).getStringValue().isEmpty());
                      } else {
                        assertThat(op)
                            .extracting(Operations::getDocMap)
                            .matches(
                                doc -> tenantId.equals(
                                    doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                            .matches(doc -> payload.getName()
                                .equals(doc.get(Key.APPLICATION_POLICY_NAME).getStringValue()))
                            .matches(
                                doc -> payload.getId()
                                    .equals(doc.get(Key.APPLICATION_POLICY_ID).getStringValue()));
                      }
                    });
              }
            }));
  }

  private void validateActivityMessages(String apiFlowNames) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> assertThat(
        activityCfgChangeRespMessage.getPayload()).matches(
            msg -> msg.getStatus().equals(Status.OK)).matches(msg -> msg.getStep().equals(apiFlowNames))
        .extracting(ConfigurationStatus::getEventDate).isNotNull());
  }

}
