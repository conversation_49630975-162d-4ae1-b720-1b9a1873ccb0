package com.ruckus.cloud.wifi.integration.workflow;

import static com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum.Workflow;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.reflect.TypeToken;
import com.google.protobuf.ProtocolStringList;
import com.ruckus.cloud.entity.association.protobuf.AssociatedProfile;
import com.ruckus.cloud.entity.association.protobuf.EntityAssociation;
import com.ruckus.cloud.entity.association.protobuf.EntityAssociationList;
import com.ruckus.cloud.entity.association.protobuf.EntityType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.Operation;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("WorkflowProfileTest")
@WifiIntegrationTest
class ConsumeActivateWorkflowProfileOnWifiNetworkRequestTest {

  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  private void validateWifiCfgChangeMessage(TxCtx txCtx, String workflowProfileId) {
    final var wifiCfgChangeMessageRecord =
        messageCaptors.getWifiCfgChangeMessageCaptor().getValue(txCtx);
    assertThat(wifiCfgChangeMessageRecord)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .isNotNull()
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(txCtx.getTxId());
    var wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
    assertThat(wifiConfigChange.getOperationList())
        .filteredOn(Operation::hasGuestNetwork)
        .anyMatch(
            operation -> {
              var network = operation.getGuestNetwork();
              return network.getWorkflowProfileId().getValue().equals(workflowProfileId);
            });
    assertThat(wifiCfgChangeMessageRecord.getPayload()).isNotNull();
  }

  private void validateEntityAssociationListMessage(
      TxCtx txCtx, Set<String> networkIds, String workflowProfileId, Set<String> entityTypes) {
    final var entityAssociationListMessage =
        messageCaptors.getExternalAssociationChangeMessageCaptor().getValue(txCtx);
    assertThat(entityAssociationListMessage)
        .isNotNull()
        .satisfies(
            msg -> {
              var headers = msg.getHeaders();
              assertThat(headers).isNotNull();
              assertThat(headers.lastHeader(WifiCommonHeader.WIFI_ENTITY_TYPES))
                  .isNotNull()
                  .extracting(Header::value)
                  .extracting(b -> new String(b, StandardCharsets.UTF_8))
                  .satisfies(
                      json -> {
                        JsonArray jsonArray = new Gson().fromJson(json, JsonArray.class);
                        Set<String> parsedSet =
                            new Gson()
                                .fromJson(jsonArray, new TypeToken<Set<String>>() {}.getType());
                        assertThat(parsedSet).containsAll(entityTypes);
                      });
              assertThat(headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
                  .isNotNull()
                  .extracting(Header::value)
                  .extracting(String::new)
                  .isEqualTo(txCtx.getTxId());
              EntityAssociationList entityAssociationList = msg.getPayload();
              assertThat(entityAssociationList.getAssociationsCount()).isEqualTo(networkIds.size());

              Set<String> foundNetworkIds = new HashSet<>();

              for (EntityAssociation association : entityAssociationList.getAssociationsList()) {
                assertThat(association.getEntityType().name()).isEqualTo("NETWORK");

                String nid = association.getEntityId();
                foundNetworkIds.add(nid);

                var profileMap =
                    association.getProfilesList().stream()
                        .collect(
                            Collectors.toMap(
                                AssociatedProfile::getEntityType,
                                AssociatedProfile::getEntityIdsList));

                assertEntityProfile(profileMap, EntityType.WORKFLOW, workflowProfileId);
              }
              assertThat(foundNetworkIds).containsAll(networkIds);
            });
  }

  private void assertEntityProfile(
      Map<EntityType, ProtocolStringList> profileMap, EntityType entityType, String expectedId) {
    if (expectedId != null && !expectedId.isBlank()) {
      assertThat(profileMap).containsKey(entityType);
      assertThat(profileMap.get(entityType)).contains(expectedId);
    } else {
      assertThat(profileMap).doesNotContainKey(entityType);
    }
  }

  @Nested
  class ConsumeWorkflowProfileOnWifiNetworkRequestTest {

    String workflowProfileId;
    String networkId;

    @BeforeEach
    void setup(final Tenant tenant) {
      workflowProfileId = "workflowProfileId";
      final var guestNetwork = network(GuestNetwork.class).generate();
      var guestPortal = Generators.guestPortal(Workflow).generate();
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      networkId = guestNetwork.getId();
    }

    @ApiAction.RequestParams("activateWorkflowProfile")
    private RequestParams activateWorkflowProfileRequestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkId", networkId)
          .addPathVariable("workflowProfileId", workflowProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_WORKFLOW_PROFILE_ON_WIFI_NETWORK,
        requestParams = @ApiAction.RequestParams("activateWorkflowProfile"))
    void thenShouldActivateWorkflowProfileOnWifiNetworkSuccessfully(
        TxCtx txCtx,
        @ApiAction.RequestParams("activateWorkflowProfile") RequestParams requestParams) {
      assertThat(networkId).isEqualTo(requestParams.getPathVariables().get("wifiNetworkId"));
      assertThat(workflowProfileId)
          .isEqualTo(requestParams.getPathVariables().get("workflowProfileId"));
      validateWifiCfgChangeMessage(txCtx, workflowProfileId);
      validateEntityAssociationListMessage(
          txCtx, Set.of(networkId), workflowProfileId, Set.of(EntityType.WORKFLOW.name()));
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
  class ConsumeWorkflowProfileOnWifiNetworkForOweRequestTest {

    String workflowProfileId;
    String networkId1;
    String networkId2;

    @BeforeEach
    void setup(final Tenant tenant) {
      workflowProfileId = "workflowProfileId";
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      var primary =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(GuestNetwork.class)
              .generate();
      var secondary =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(GuestNetwork.class)
              .generate();

      primary.setTenant(tenant);
      primary.setName(masterSsid);
      primary.getWlan().setNetwork(primary);
      primary.getWlan().setSsid(masterSsid);
      primary.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      primary.setIsOweMaster(true);
      primary.setOwePairNetworkId(secondary.getId());
      primary.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Workflow)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      primary.getGuestPortal().setNetwork(primary);
      primary = repositoryUtil.createOrUpdate(primary, primary.getTenant().getId(), randomTxId());

      secondary.setTenant(tenant);
      secondary.setName(masterSsid + "-owe-tr");
      secondary.getWlan().setNetwork(secondary);
      secondary.getWlan().setSsid(masterSsid + "-owe-tr");
      secondary.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      secondary.setOwePairNetworkId(primary.getId());
      secondary.setIsOweMaster(false);
      secondary.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Workflow)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      secondary.getGuestPortal().setNetwork(secondary);
      repositoryUtil.createOrUpdate(secondary, secondary.getTenant().getId(), randomTxId());
      networkId1 = primary.getId();
      networkId2 = secondary.getId();
    }

    @ApiAction.RequestParams("activateWorkflowProfile")
    private RequestParams activateWorkflowProfileRequestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkId", networkId1)
          .addPathVariable("workflowProfileId", workflowProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_WORKFLOW_PROFILE_ON_WIFI_NETWORK,
        requestParams = @ApiAction.RequestParams("activateWorkflowProfile"))
    void thenShouldActivateWorkflowProfileOnWifiNetworkSuccessfully(
        TxCtx txCtx,
        @ApiAction.RequestParams("activateWorkflowProfile") RequestParams requestParams) {
      assertThat(networkId1).isEqualTo(requestParams.getPathVariables().get("wifiNetworkId"));
      assertThat(workflowProfileId)
          .isEqualTo(requestParams.getPathVariables().get("workflowProfileId"));
      validateWifiCfgChangeMessage(txCtx, workflowProfileId);
      validateEntityAssociationListMessage(
          txCtx,
          Set.of(networkId1, networkId2),
          workflowProfileId,
          Set.of(EntityType.WORKFLOW.name()));
    }
  }
}
