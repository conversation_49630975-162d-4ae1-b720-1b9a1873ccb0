package com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.NetworkVenueInstanceCreateRequestBuilder.buildVlanPoolInApGroupOverrideEntry;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.NetworkVenueCreateInstanceOperation.OVERRIDE_KEY_AP_GROUP_IDS;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.NetworkVenueCreateInstanceOperation.OVERRIDE_KEY_NETWORK_ID;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.NetworkVenueCreateInstanceOperation.OVERRIDE_KEY_VENUE_ID;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.NetworkVenueCreateInstanceOperation.OVERRIDE_KEY_VLAN_POOL_ID;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.NetworkVenueCreateInstanceOperation.OVERRIDE_KEY_VLAN_POOL_IN_AP_GROUP_IDS;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.TemplateCreateInstanceOperation.newServiceProperty;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomNetworkTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplate;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.TenantTestFixture.randomTenant;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.VlanPoolTestFixture.randomVlanPoolInstance;
import static com.ruckus.cloud.wifi.test.fixture.VlanPoolTestFixture.randomVlanPoolTemplate;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Property;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.ApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupRadioGenerator;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request.NetworkVenueInstanceCreateRequestBuilder.NetworkVenueInstanceCreateResource;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.NetworkVenueCreateInstanceOperation;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.repository.VlanPoolRepository;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;

@ExtendWith(TxCtxExtension.class)
@WifiUnitTest
public class NetworkVenueInstanceCreateRequestBuilderTest {

  @Autowired
  private NetworkVenueInstanceCreateRequestBuilder unit;
  @MockBean
  private NetworkVenueRepository networkVenueRepository;
  @MockBean
  private VenueRepository venueRepository;
  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private VlanPoolRepository vlanPoolRepository;
  @MockBean
  private NetworkVenueCreateInstanceOperation networkVenueCreateInstanceOperation;

  private Tenant mspTenant;
  private Tenant ecTenant;
  private NetworkVenue networkVenueTemplate;
  private Venue venueTemplate;
  private Network networkTemplate;
  private NetworkVenue networkVenueInstance;
  private Venue venueInstance;
  private Network networkInstance;
  private VlanPool vlanPoolTemplate;
  private VlanPool vlanPoolInstance;

  @BeforeEach
  public void setUp() {
    ecTenant = randomTenant((e) -> {
    });
    mspTenant = randomTenant(e -> e.setId(TxCtxHolder.tenantId()));
    unit.setTemplateTenantId(mspTenant.getId());
    unit.setTargetTenantId(ecTenant.getId());
    networkVenueTemplate = randomNetworkVenueTemplate(mspTenant);
    venueTemplate = networkVenueTemplate.getVenue();
    networkTemplate = networkVenueTemplate.getNetwork();
    networkInstance = randomNetworkTemplateInstance(
        networkTemplate.getId(),
        ecTenant);
    venueInstance = randomVenueTemplateInstance(venueTemplate.getId(),
        ecTenant);
    networkVenueInstance = randomNetworkVenueTemplateInstance(networkVenueTemplate.getId(),
        networkInstance, venueInstance);
    vlanPoolTemplate = randomVlanPoolTemplate(mspTenant);
    vlanPoolInstance = randomVlanPoolInstance(vlanPoolTemplate.getId(), ecTenant);
  }

  @Test
  void testBuildOnAllApGroups() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.empty()).when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));

    var result = unit.build(networkVenueTemplate);

    assertThat(result).isNotEmpty();

    var request = result.get().request();
    var operation = result.get().operation();
    var templateEntity = result.get().templateEntity();

    assertThat(request).satisfies(e -> {
      assertThat(e.getTargetTenantId()).isEqualTo(ecTenant.getId());
      assertThat(e.getInstanceId()).isNotEmpty();
      assertThat(e.getOverrides())
          .hasSize(2)
          .contains(
              newServiceProperty(OVERRIDE_KEY_NETWORK_ID, networkInstance.getId()),
              newServiceProperty(OVERRIDE_KEY_VENUE_ID, venueInstance.getId()));
    });
    assertThat(operation)
        .isEqualTo(networkVenueCreateInstanceOperation);
    assertThat(templateEntity)
        .isEqualTo(networkVenueTemplate);
  }

  @Test
  void testBuildOnAllApGroups_withVlanPoolIdInNetworkVenue() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.empty()).when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(vlanPoolInstance))
        .when(vlanPoolRepository)
        .findByTemplateIdAndTenantId(eq(vlanPoolTemplate.getId()),
            eq(TxCtxHolder.tenantId()));

    networkVenueTemplate.setVlanPoolId(vlanPoolTemplate.getId());

    var result = unit.build(networkVenueTemplate);

    assertThat(result).isNotEmpty();

    var request = result.get().request();
    var operation = result.get().operation();
    var templateEntity = result.get().templateEntity();

    assertThat(request).satisfies(e -> {
      assertThat(e.getTargetTenantId()).isEqualTo(ecTenant.getId());
      assertThat(e.getInstanceId()).isNotEmpty();
      assertThat(e.getOverrides())
          .hasSize(3)
          .contains(
              newServiceProperty(OVERRIDE_KEY_NETWORK_ID, networkInstance.getId()),
              newServiceProperty(OVERRIDE_KEY_VENUE_ID, venueInstance.getId()),
              newServiceProperty(OVERRIDE_KEY_VLAN_POOL_ID, vlanPoolInstance.getId()));
    });
    assertThat(operation)
        .isEqualTo(networkVenueCreateInstanceOperation);
    assertThat(templateEntity)
        .isEqualTo(networkVenueTemplate);
  }

  @Test
  void testBuildOnSpecificApGroups() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.empty()).when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));

    var apGroupTemplateId = randomId();
    var apGroupTemplateId2 = randomId();
    networkVenueTemplate.setIsAllApGroups(false);
    networkVenueTemplate.setNetworkApGroups(List.of(
        new NetworkApGroupGenerator().setApGroup(always(new ApGroup(apGroupTemplateId))).generate(),
        new NetworkApGroupGenerator().setApGroup(always(new ApGroup(apGroupTemplateId2)))
            .generate()));

    var apGroupId = randomId();
    var apGroupId2 = randomId();
    venueInstance.setApGroups(List.of(
        new ApGroupGenerator()
            .setId(always(apGroupId))
            .setTemplateId(always(apGroupTemplateId)).generate(),
        new ApGroupGenerator()
            .setId(always(apGroupId2))
            .setTemplateId(always(apGroupTemplateId2)).generate()));

    var result = unit.build(networkVenueTemplate);

    assertThat(result).isNotEmpty();

    var request = result.get().request();
    var operation = result.get().operation();
    var templateEntity = result.get().templateEntity();

    assertThat(request).satisfies(e -> {
      assertThat(e.getTargetTenantId()).isEqualTo(ecTenant.getId());
      assertThat(e.getInstanceId()).isNotEmpty();
      assertThat(e.getOverrides())
          .hasSize(3)
          .contains(
              newServiceProperty(OVERRIDE_KEY_NETWORK_ID, networkInstance.getId()),
              newServiceProperty(OVERRIDE_KEY_VENUE_ID, venueInstance.getId()))
          .filteredOn(p -> p.getKey().equals(OVERRIDE_KEY_AP_GROUP_IDS))
          .hasSize(1)
          .singleElement()
          .extracting(Property::getValue)
          .isEqualTo(NetworkVenueCreateInstanceOperation.buildApGroupOverrideValue(
              List.of(apGroupId, apGroupId2)));
    });
    assertThat(operation)
        .isEqualTo(networkVenueCreateInstanceOperation);
    assertThat(templateEntity)
        .isEqualTo(networkVenueTemplate);
  }

  @Test
  void testBuildOnSpecificApGroups_withVlanPoolInNetworkApGroupRadio() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.empty()).when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(vlanPoolInstance))
        .when(vlanPoolRepository)
        .findByTemplateIdAndTenantId(eq(vlanPoolTemplate.getId()),
            eq(TxCtxHolder.tenantId()));

    var apGroupTemplateId = randomId();
    var apGroupTemplateId2 = randomId();
    networkVenueTemplate.setIsAllApGroups(false);
    networkVenueTemplate.setNetworkApGroups(List.of(
        new NetworkApGroupGenerator().setApGroup(always(new ApGroup(apGroupTemplateId)))
            .setNetworkApGroupRadios(list(new NetworkApGroupRadioGenerator(), 1))
            .generate(),
        new NetworkApGroupGenerator().setApGroup(always(new ApGroup(apGroupTemplateId2)))
            .generate()));

    networkVenueTemplate.getNetworkApGroups().stream()
        .findFirst().ifPresent(
        networkApGroup -> networkApGroup.setNetworkApGroupRadios(List.of(
            new NetworkApGroupRadioGenerator().setNetworkApGroup(always(networkApGroup)).setRadio(always(
                StrictRadioTypeEnum._2_4_GHz)).setVlanPool(always(vlanPoolTemplate)).generate()
        ))
    );

    var apGroupId = randomId();
    var apGroupId2 = randomId();
    venueInstance.setApGroups(List.of(
        new ApGroupGenerator()
            .setId(always(apGroupId))
            .setTemplateId(always(apGroupTemplateId)).generate(),
        new ApGroupGenerator()
            .setId(always(apGroupId2))
            .setTemplateId(always(apGroupTemplateId2)).generate()));

    var result = unit.build(networkVenueTemplate);

    assertThat(result).isNotEmpty();

    var request = result.get().request();
    var operation = result.get().operation();
    var templateEntity = result.get().templateEntity();

    assertThat(request).satisfies(e -> {
      assertThat(e.getTargetTenantId()).isEqualTo(ecTenant.getId());
      assertThat(e.getInstanceId()).isNotEmpty();
      assertThat(e.getOverrides())
          .hasSize(4)
          .contains(
              newServiceProperty(OVERRIDE_KEY_NETWORK_ID, networkInstance.getId()),
              newServiceProperty(OVERRIDE_KEY_VENUE_ID, venueInstance.getId()))
          .satisfies(overrides -> {
            assertThat(overrides).filteredOn(p -> p.getKey().equals(OVERRIDE_KEY_AP_GROUP_IDS))
                .hasSize(1)
                .singleElement()
                .extracting(Property::getValue)
                .isEqualTo(NetworkVenueCreateInstanceOperation.buildApGroupOverrideValue(
                    List.of(apGroupId, apGroupId2)));
            assertThat(overrides).filteredOn(p -> p.getKey().equals(OVERRIDE_KEY_VLAN_POOL_IN_AP_GROUP_IDS))
                .hasSize(1)
                .singleElement()
                .extracting(Property::getValue)
                .isEqualTo(buildVlanPoolInApGroupOverrideEntry(apGroupId, vlanPoolInstance.getId()));
          });
    });
    assertThat(operation)
        .isEqualTo(networkVenueCreateInstanceOperation);
    assertThat(templateEntity)
        .isEqualTo(networkVenueTemplate);
  }


  @Test
  void testBuild_getException_whenValidationDoesNotPass() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()), eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()), eq(TxCtxHolder.tenantId()));

    doReturn(Optional.of(networkVenueInstance)).when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));

    assertThrows(RuntimeException.class, () -> unit.build(networkVenueTemplate));
  }

  @Test
  void testValidate() {
    var resource = new NetworkVenueInstanceCreateResource(networkInstance, venueInstance, null,
        randomId());

    doReturn(Optional.of(resource.instanceNetwork()))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(resource.instanceVenue()))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.empty()).when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));

    assertThat(unit.validate(networkVenueTemplate, resource))
        .isTrue();
  }

  @Test
  void testValidate_activateOnSpecificApGroups() {
    var resource = new NetworkVenueInstanceCreateResource(networkInstance, venueInstance, Map.of(),
        randomId());

    doReturn(Optional.of(resource.instanceNetwork()))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(resource.instanceVenue()))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.empty()).when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));

    var apGroupTemplateId = randomId();
    var apGroupTemplateId2 = randomId();
    networkVenueTemplate.setIsAllApGroups(false);
    networkVenueTemplate.setNetworkApGroups(List.of(
        new NetworkApGroupGenerator().setApGroup(always(new ApGroup(apGroupTemplateId))).generate(),
        new NetworkApGroupGenerator().setApGroup(always(new ApGroup(apGroupTemplateId2)))
            .generate()));

    venueInstance.setApGroups(List.of(
        new ApGroupGenerator().setTemplateId(always(apGroupTemplateId)).generate(),
        new ApGroupGenerator().setTemplateId(always(apGroupTemplateId2)).generate()));

    assertThat(unit.validate(networkVenueTemplate, resource))
        .isTrue();
  }

  @Test
  void testValidate_getException_whenEntityIsNotTemplate() {
    assertThrows(CommonException.class, () -> unit.validate(new NetworkVenue(randomId()), null));
  }

  @Test
  void testValidate_getException_whenNetworkNotFound() {
    var resource = new NetworkVenueInstanceCreateResource(null, venueInstance, null, randomId());

    assertThrows(NullPointerException.class, () -> unit.validate(networkVenueTemplate, resource));
  }

  @Test
  void testValidate_getException_whenApGroupNotFoundByTemplateId() {
    var resource = new NetworkVenueInstanceCreateResource(networkInstance, venueInstance, null,
        randomId());

    doReturn(Optional.of(resource.instanceNetwork()))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(resource.instanceVenue()))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.empty()).when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));

    var apGroupTemplateId = randomId();
    var apGroupTemplateId2 = randomId();
    networkVenueTemplate.setIsAllApGroups(false);
    networkVenueTemplate.setNetworkApGroups(List.of(
        new NetworkApGroupGenerator().setApGroup(always(new ApGroup(apGroupTemplateId))).generate(),
        new NetworkApGroupGenerator().setApGroup(always(new ApGroup(apGroupTemplateId2)))
            .generate()));

    // Only one ap group in venue instance
    venueInstance.setApGroups(List.of(
        new ApGroupGenerator().setTemplateId(always(apGroupTemplateId)).generate()));

    assertThrows(IllegalStateException.class, () -> unit.validate(networkVenueTemplate, resource));
  }

  @Test
  void testValidate_getException_whenVenueNotFound() {
    var resource = new NetworkVenueInstanceCreateResource(networkInstance, null, null,
        randomId());

    assertThrows(NullPointerException.class, () -> unit.validate(networkVenueTemplate, resource));
  }

  @Test
  void testValidate_getException_whenNetworkVenueExistsAlready() {
    var resource = new NetworkVenueInstanceCreateResource(networkInstance, venueInstance, null,
        randomId());
    doReturn(Optional.of(networkVenueInstance)).when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));

    assertThrows(IllegalStateException.class, () -> unit.validate(networkVenueTemplate, resource));
  }

  @Test
  void testBuildResource() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.empty())
        .when(networkVenueRepository)
        .findByTemplateIdAndTenantId(eq(networkVenueTemplate.getId()), eq(TxCtxHolder.tenantId()));

    var result = unit.buildRequestResource(networkVenueTemplate);
    assertThat(result.instanceNetwork()).extracting(AbstractBaseEntity::getId)
        .isEqualTo(networkInstance.getId());
    assertThat(result.instanceVenue()).extracting(AbstractBaseEntity::getId)
        .isEqualTo(venueInstance.getId());
    assertThat(result.instanceId()).isNotEmpty();
  }

  @Test
  void testBuildResource_networkIsNull_whenNetworkDoesNotExist() {
    doReturn(Optional.empty())
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));

    var result = unit.buildRequestResource(networkVenueTemplate);
    assertThat(result.instanceNetwork()).isNull();
    assertThat(result.instanceVenue()).extracting(AbstractBaseEntity::getId)
        .isEqualTo(venueInstance.getId());
    assertThat(result.instanceId()).isNotEmpty();
  }

  @Test
  void testBuildResource_venueIsNull_whenVenueDoesNotExist() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(TxCtxHolder.tenantId()));
    doReturn(Optional.empty())
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(TxCtxHolder.tenantId()));

    var result = unit.buildRequestResource(networkVenueTemplate);
    assertThat(result.instanceNetwork()).extracting(AbstractBaseEntity::getId)
        .isEqualTo(networkInstance.getId());
    assertThat(result.instanceVenue()).isNull();
    assertThat(result.instanceId()).isNotEmpty();
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    NetworkVenueInstanceCreateRequestBuilder networkVenueInstanceCreateRequestBuilder(
        NetworkVenueRepository networkVenueRepository, VenueRepository venueRepository,
        NetworkRepository networkRepository, VlanPoolRepository vlanPoolRepository,
        NetworkVenueCreateInstanceOperation networkVenueCreateInstanceOperation) {
      return new NetworkVenueInstanceCreateRequestBuilder(networkRepository,
          venueRepository, networkVenueRepository, vlanPoolRepository, networkVenueCreateInstanceOperation);
    }
  }

}
