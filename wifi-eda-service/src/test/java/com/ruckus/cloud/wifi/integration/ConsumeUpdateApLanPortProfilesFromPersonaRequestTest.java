package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.mac;
import static com.ruckus.cloud.wifi.entitylistener.ddccm.builder.AbstractDdccmOperationBuilder.DDCCM_VERSION;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.acx.ddccm.protobuf.wifi.PortNameEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.PinLanPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Stream;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("EthernetPortProfileTest")
@Tag("VxLanTunnelFeatureTest")
@WifiIntegrationTest
class ConsumeUpdateApLanPortProfilesFromPersonaRequestTest {
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Deprecated(forRemoval = true)
  @FeatureFlag(disable = FlagNames.ACX_UI_ETHERNET_TOGGLE)
  @Nested
  class BeforeEthernetPortProfileReleased extends AbstractBaseTest {
    @Override
    protected Predicate<ApLanPort> apLanPortPredicate(int expectedVni) {
      return apLanPort ->
          apLanPort.getApLanPortProfile() instanceof EthernetPortProfile ethernetPortProfile
              && ethernetPortProfile.getVni() != null
              && ethernetPortProfile.getVni() == expectedVni;
    }
  }

  @FeatureFlag(enable = FlagNames.ACX_UI_ETHERNET_TOGGLE)
  @Nested
  class AfterEthernetPortProfileReleased extends AbstractBaseTest {
    @Override
    protected Predicate<ApLanPort> apLanPortPredicate(int expectedVni) {
      return apLanPort ->
          apLanPort.getApLanPortProfile() instanceof PinLanPortProfile pinLanPortProfile
              && pinLanPortProfile.getVni() != null
              && pinLanPortProfile.getVni() == expectedVni;
    }
  }

  @FeatureFlag(
      enable = {
        FlagNames.ACX_UI_ETHERNET_TOGGLE,
        FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
      })
  @Nested
  class AfterEthernetPortProfileAndSoftGreOnPortReleased extends AfterEthernetPortProfileReleased {

    @Override
    protected boolean migratedToLanPortAdoption() {
      return true;
    }
  }

  @FeatureFlag(
      enable = {
        FlagNames.ACX_UI_ETHERNET_TOGGLE,
        FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE,
      })
  @Nested
  class AfterEthernetPortProfileAndClientIsolationOnPortReleased
      extends AfterEthernetPortProfileReleased {
    @Override
    protected boolean migratedToLanPortAdoption() {
      return true;
    }
  }

  private abstract class AbstractBaseTest {

    protected boolean migratedToLanPortAdoption() {
      return false;
    }

    protected abstract Predicate<ApLanPort> apLanPortPredicate(int expectedVni);

    @Test
    void consumePersonaMsgAndUpdateApLanPorts(final Tenant tenant,
        @ApModel("H550") final Ap apFromDb) {
      apFromDb.setMac(mac().setRandom(true).generate());
      repositoryUtil.createOrUpdate(apFromDb, tenant.getId(), randomTxId());

      // Assign vni on LAN1, LAN2, LAN4
      final int expectedVni = Generators.rangeInteger(8193).setRandom(true).generate();
      List<Map<String, Object>> payload1 = generateVxLanEnabledPayload(apFromDb.getMac(),
          expectedVni, 1, 2, 4);
      final var requestId1 = randomTxId();
      messageUtil.sendWifiCfgRequest(tenant.getId(),
          requestId1,
          CfgExtendedAction.UPDATE_AP_LAN_PORTS_FROM_PERSONA,
          randomName(),
          new RequestParams(),
          payload1);

      final Predicate<ApLanPort> apLanPortPredicate = apLanPortPredicate(expectedVni);
      validateVxLanEnabledUpdateApLanPortsResult(apFromDb.getId(), expectedVni, apLanPortPredicate,
          payload1, tenant.getId(), requestId1, migratedToLanPortAdoption());

      // remove vni on LAN4
      List<Map<String, Object>> payload2 = generateVxLanDisabledPayload(apFromDb.getMac(), 4);
      final var requestId2 = randomTxId();
      messageUtil.sendWifiCfgRequest(tenant.getId(),
          requestId2,
          CfgExtendedAction.UPDATE_AP_LAN_PORTS_FROM_PERSONA,
          randomName(),
          new RequestParams(),
          payload2);

      validateVxLanDisabledUpdateApLanPortsResult(apFromDb.getId(), expectedVni, apLanPortPredicate,
          new PortNameEnum[]{PortNameEnum.LAN1, PortNameEnum.LAN2}, tenant.getId(), requestId2, migratedToLanPortAdoption());

      // remove vni on LAN1 & assign vni on LAN3
      List<Map<String, Object>> payload3 = Stream.concat(
          generateVxLanDisabledPayload(apFromDb.getMac(), 1).stream(),
          generateVxLanEnabledPayload(apFromDb.getMac(), expectedVni, 3).stream()).toList();
      final var requestId3 = randomTxId();
      messageUtil.sendWifiCfgRequest(tenant.getId(),
          requestId3,
          CfgExtendedAction.UPDATE_AP_LAN_PORTS_FROM_PERSONA,
          randomName(),
          new RequestParams(),
          payload3);

      validateVxLanDisabledUpdateApLanPortsResult(apFromDb.getId(), expectedVni, apLanPortPredicate,
          new PortNameEnum[]{PortNameEnum.LAN2, PortNameEnum.LAN3}, tenant.getId(), requestId3, migratedToLanPortAdoption());

      // remove vni on LAN2 & LAN3
      List<Map<String, Object>> payload4 = generateVxLanDisabledPayload(apFromDb.getMac(), 2, 3);
      final var requestId4 = randomTxId();
      messageUtil.sendWifiCfgRequest(tenant.getId(),
          requestId4,
          CfgExtendedAction.UPDATE_AP_LAN_PORTS_FROM_PERSONA,
          randomName(),
          new RequestParams(),
          payload4);

      validateVxLanDisabledUpdateApLanPortsResult(apFromDb.getId(), expectedVni, apLanPortPredicate,
          new PortNameEnum[]{}, tenant.getId(), requestId4, migratedToLanPortAdoption());
    }
  }

  record IdAndApLanPortId(String id, int apLanPortId) {
    static IdAndApLanPortId of(String id, int apLanPortId) {
      return new IdAndApLanPortId(id, apLanPortId);
    }
  }

  void validateVxLanEnabledUpdateApLanPortsResult(String apId,
      int expectedVni, Predicate<ApLanPort> apLanPortPredicate, List<Map<String, Object>> payload,
      String tenantId, String requestId, boolean migratedToLanPortAdoption) {

    final Ap apFromDb = Optional.ofNullable(repositoryUtil.find(Ap.class, apId)).orElseThrow();

    // get ApLanPorts with EthernetPortProfile contains expectedVni
    final var apLanPortsWithExpectedVni = apFromDb.getModelSpecific().getLanPorts().stream()
        .filter(apLanPortPredicate).toList();

    assertThat(apLanPortsWithExpectedVni).isNotEmpty().hasSize(payload.size())
        .extracting(ApLanPort::getPortId)
        .containsExactlyInAnyOrderElementsOf(
            () -> payload.stream()
                .map(personaMsgContent -> String.valueOf(personaMsgContent.get("portIndex")))
                .iterator());

    // get id and apLanPortId from EthernetPortProfile with expectedVni
    final IdAndApLanPortId expectedIdAndApLanPortId =
        migratedToLanPortAdoption
            ? apLanPortsWithExpectedVni.stream()
                .map(ApLanPort::getLanPortAdoption)
                .findAny()
                .map(
                    adoption ->
                        IdAndApLanPortId.of(
                            adoption.getApLanPortProfile().getId(),
                            adoption.getEthernetPortProfileId()))
                .orElseThrow()
            : apLanPortsWithExpectedVni.stream()
                .map(ApLanPort::getApLanPortProfile)
                .findAny()
                .map(
                    apLanPortProfile ->
                        IdAndApLanPortId.of(
                            apLanPortProfile.getId(), apLanPortProfile.getApLanPortId()))
                .orElseThrow();
    // After migrated to LanPortAdoption, the EthernetPortProfile and the related profiles need to
    // be updated based on the migrated model, so the venue should be updated based on the latest
    // model. That's why the size here will add 1.
    final var operationSize = migratedToLanPortAdoption ? 8 : 5;
    assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId)).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .satisfies(assertHeader(WifiCommonHeader.SENDER, ServiceType.WIFI_SERVICE.name()))
        .satisfies(assertHeader(WifiCommonHeader.VERSION, DDCCM_VERSION))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty().hasSize(operationSize)
        .allSatisfy(op -> {
          assertThat(op.hasCommonInfo()).isTrue();
          assertThat(op.getCommonInfo())
              .satisfies(commonInfo -> {
                assertThat(commonInfo.getRequestId()).isEqualTo(requestId);
                assertThat(commonInfo.getTenantId()).isEqualTo(tenantId);
                assertThat(commonInfo.getSender()).isEqualTo(ServiceType.WIFI_SERVICE);
                assertThat(commonInfo.getVersion()).isEqualTo(DDCCM_VERSION);
              });
          assertThat(op.getCompensation()).isFalse();
        })
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> op.getAction() == Action.MODIFY && op.getConfigCase() == ConfigCase.AP && op.hasAp())
              .isNotEmpty().singleElement()
              .extracting(Operation::getAp)
              .satisfies(ap -> {
                assertThat(ap.getId()).isEqualTo(apFromDb.getId());
                assertThat(ap.getMac()).isEqualTo(StringValue.of(apFromDb.getMac()));
                assertThat(ap.hasModel()).isTrue();
                assertThat(ap.getModel()).isNotNull()
                    .satisfies(apModel -> {
                      assertThat(apModel.getName()).isEqualTo(apFromDb.getModel());
                      assertThat(apModel.getLanPortCount()).isEqualTo(5);
                      assertThat(apModel.getLanPortList()).hasSize(5)
                          .filteredOn(lanPort -> lanPort.getApLanPortProfileId()
                              == expectedIdAndApLanPortId.apLanPortId())
                          .hasSize(3)
                          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort::getPortName)
                          .containsExactlyInAnyOrder(PortNameEnum.LAN1, PortNameEnum.LAN2, PortNameEnum.LAN4);
                    });
              });
          assertThat(ops)
              .filteredOn(op -> op.getAction() == Action.ADD && op.getConfigCase() == ConfigCase.APLANPORTPROFILE && op.hasApLanPortProfile())
              .isNotEmpty().hasSize( migratedToLanPortAdoption ? 5 : 3)
              .extracting(Operation::getApLanPortProfile)
              .filteredOn(apLanPortProfile -> apLanPortProfile.getVni() > 0)
              .isNotEmpty().singleElement()
              .satisfies(apLanPortProfile -> {
                assertThat(apLanPortProfile.getId()).isEqualTo(
                    expectedIdAndApLanPortId.apLanPortId());
                assertThat(apLanPortProfile.getVni()).isEqualTo(expectedVni);
              });
        });

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(
            ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSize(3)
        .allSatisfy(op -> {
          assertThat(op.getIndex()).isEqualTo(Index.ETHERNET_PORT_PROFILE_INDEX_NAME);
          assertThat(op.getOpType()).isEqualTo(OpType.ADD);
          assertThat(op.getDocMap())
              .containsKey(Key.IS_DEFAULT)
              .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
              .containsEntry(Key.VENUE_IDS, ValueUtils.listValue(List.of()))
              .containsEntry(Key.VENUE_ACTIVATIONS, ValueUtils.listValue(List.of()));
        })
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> op.getId().equals(expectedIdAndApLanPortId.id()))
              .isNotEmpty().singleElement()
              .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
              .containsEntry(Key.VNI, ValueUtils.numberValue(expectedVni))
              .hasEntrySatisfying(Key.AP_SERIAL_NUMBERS, apSerialNumbers -> {
                assertThat(apSerialNumbers.getKindCase()).isEqualTo(Value.KindCase.LIST_VALUE);
                assertThat(apSerialNumbers.hasListValue()).isTrue();
                assertThat(apSerialNumbers.getListValue().getValuesList()).isNotEmpty()
                    .singleElement().satisfies(value -> {
                      assertThat(value.getKindCase()).isEqualTo(Value.KindCase.STRING_VALUE);
                      assertThat(value.getStringValue()).isEqualTo(apFromDb.getId());
                    });
              })
              .hasEntrySatisfying(Key.AP_ACTIVATIONS, apActivations -> {
                assertThat(apActivations.getKindCase()).isEqualTo(Value.KindCase.LIST_VALUE);
                assertThat(apActivations.hasListValue()).isTrue();
                assertThat(apActivations.getListValue().getValuesList()).isNotEmpty()
                    .hasSize(payload.size())
                    .allSatisfy(value -> {
                      assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                      assertThat(value.hasStructValue()).isTrue();
                      assertThat(value.getStructValue()).isNotNull()
                          .satisfies(structValue -> assertThat(structValue.getFieldsMap())
                              .containsEntry(Key.VENUE_ID,
                                  ValueUtils.stringValue(apFromDb.getApGroup().getVenue().getId()))
                              .containsEntry(Key.AP_SERIAL_NUMBER, ValueUtils.stringValue(apFromDb.getId())));
                    })
                    .map(value -> value.getStructValue().getFieldsOrThrow(Key.PORT_ID))
                    .containsExactlyInAnyOrderElementsOf(
                        () -> payload.stream()
                            .map(personaMsgContent -> String.valueOf(personaMsgContent.get("portIndex")))
                            .map(ValueUtils::stringValue).iterator());
              });
        });
  }

  void validateVxLanDisabledUpdateApLanPortsResult(String apId,
      int expectedVni, Predicate<ApLanPort> apLanPortPredicate, PortNameEnum[] remainingPortNames,
      String tenantId, String requestId, boolean migratedToLanPortAdoption) {

    final Ap apFromDb = Optional.ofNullable(repositoryUtil.find(Ap.class, apId)).orElseThrow();

    // get ApLanPorts with EthernetPortProfile contains expectedVni
    final var apLanPortsWithExpectedVni = apFromDb.getModelSpecific().getLanPorts().stream()
        .filter(apLanPortPredicate).toList();

    assertThat(apLanPortsWithExpectedVni).hasSize(remainingPortNames.length)
        .extracting(ApLanPort::getPortId)
        .containsExactlyInAnyOrderElementsOf(
            () -> Arrays.stream(remainingPortNames).map(PortNameEnum::getNumber)
                .map(String::valueOf).iterator());

    // get id and apLanPortId from EthernetPortProfile with expectedVni
    final var optExpectedIdAndApLanPortId =
        migratedToLanPortAdoption
            ? apLanPortsWithExpectedVni.stream()
                .map(ApLanPort::getLanPortAdoption)
                .findAny()
                .map(
                    adoption ->
                        IdAndApLanPortId.of(
                            adoption.getApLanPortProfile().getId(),
                            adoption.getEthernetPortProfileId()))
            : apLanPortsWithExpectedVni.stream()
                .map(ApLanPort::getApLanPortProfile)
                .findAny()
                .map(
                    apLanPortProfile ->
                        IdAndApLanPortId.of(
                            apLanPortProfile.getId(), apLanPortProfile.getApLanPortId()));

    if (remainingPortNames.length > 0) {
      assertThat(optExpectedIdAndApLanPortId).isPresent();
    } else {
      assertThat(optExpectedIdAndApLanPortId).isEmpty();
    }
    // The LanPortAdoption mechanism is re-use otherwise create a new one, at the same time the
    // useless adoption will be removed. That why the message bound will increase 1.
    final var operationHigherBound = migratedToLanPortAdoption ? 4 : 3;
    assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId)).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .satisfies(assertHeader(WifiCommonHeader.SENDER, ServiceType.WIFI_SERVICE.name()))
        .satisfies(assertHeader(WifiCommonHeader.VERSION, DDCCM_VERSION))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty().hasSizeBetween(2, operationHigherBound) // new flow contains 1 unnecessary op
        .allSatisfy(op -> {
          assertThat(op.hasCommonInfo()).isTrue();
          assertThat(op.getCommonInfo())
              .satisfies(commonInfo -> {
                assertThat(commonInfo.getRequestId()).isEqualTo(requestId);
                assertThat(commonInfo.getTenantId()).isEqualTo(tenantId);
                assertThat(commonInfo.getSender()).isEqualTo(ServiceType.WIFI_SERVICE);
                assertThat(commonInfo.getVersion()).isEqualTo(DDCCM_VERSION);
              });
          assertThat(op.getCompensation()).isFalse();
        })
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> op.getAction() == Action.MODIFY && op.getConfigCase() == ConfigCase.AP && op.hasAp())
              .isNotEmpty().singleElement()
              .extracting(Operation::getAp)
              .satisfies(ap -> {
                assertThat(ap.getId()).isEqualTo(apFromDb.getId());
                assertThat(ap.getMac()).isEqualTo(StringValue.of(apFromDb.getMac()));
                assertThat(ap.hasModel()).isTrue();
                assertThat(ap.getModel()).isNotNull()
                    .satisfies(apModel -> {
                      assertThat(apModel.getName()).isEqualTo(apFromDb.getModel());
                      assertThat(apModel.getLanPortCount()).isEqualTo(5);
                      optExpectedIdAndApLanPortId.ifPresentOrElse(
                          expectedIdAndApLanPortId -> assertThat(apModel.getLanPortList()).hasSize(5)
                              .filteredOn(lanPort -> lanPort.getApLanPortProfileId()
                                  == expectedIdAndApLanPortId.apLanPortId())
                              .hasSize(remainingPortNames.length)
                              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort::getPortName)
                              .containsExactlyInAnyOrder(remainingPortNames),
                          () -> {

                          });
                    });
              });
        });

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(
            ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSizeBetween(2, 3) // legacy flow contains 1 unnecessary op
        .allSatisfy(op -> {
          assertThat(op.getIndex()).isEqualTo(Index.ETHERNET_PORT_PROFILE_INDEX_NAME);
          assertThat(op.getOpType()).isEqualTo(OpType.MOD);
          assertThat(op.getDocMap())
              .containsKey(Key.IS_DEFAULT)
              .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
              .containsEntry(Key.VENUE_IDS, ValueUtils.listValue(List.of()))
              .containsEntry(Key.VENUE_ACTIVATIONS, ValueUtils.listValue(List.of()));
        })
        .satisfies(ops -> optExpectedIdAndApLanPortId.ifPresent(expectedIdAndApLanPortId ->
            assertThat(ops).filteredOn(op -> op.getId()
                    .equals(optExpectedIdAndApLanPortId.get().id()))
                .isNotEmpty().singleElement()
                .extracting(Operations::getDocMap,
                    InstanceOfAssertFactories.map(String.class, Value.class))
                .containsEntry(Key.VNI, ValueUtils.numberValue(expectedVni))
                .hasEntrySatisfying(Key.AP_SERIAL_NUMBERS, apSerialNumbers -> {
                  assertThat(apSerialNumbers.getKindCase()).isEqualTo(Value.KindCase.LIST_VALUE);
                  assertThat(apSerialNumbers.hasListValue()).isTrue();
                  assertThat(apSerialNumbers.getListValue().getValuesList()).isNotEmpty()
                      .singleElement().satisfies(value -> {
                        assertThat(value.getKindCase()).isEqualTo(Value.KindCase.STRING_VALUE);
                        assertThat(value.getStringValue()).isEqualTo(apFromDb.getId());
                      });
                })
                .hasEntrySatisfying(Key.AP_ACTIVATIONS, apActivations -> {
                  assertThat(apActivations.getKindCase()).isEqualTo(Value.KindCase.LIST_VALUE);
                  assertThat(apActivations.hasListValue()).isTrue();
                  assertThat(apActivations.getListValue().getValuesList()).isNotEmpty()
                      .hasSize(apLanPortsWithExpectedVni.size()).allSatisfy(value -> {
                        assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                        assertThat(value.hasStructValue()).isTrue();
                        assertThat(value.getStructValue()).isNotNull().satisfies(
                            structValue -> assertThat(structValue.getFieldsMap()).containsEntry(
                                    Key.VENUE_ID,
                                    ValueUtils.stringValue(apFromDb.getApGroup().getVenue().getId()))
                                .containsEntry(Key.AP_SERIAL_NUMBER,
                                    ValueUtils.stringValue(apFromDb.getId())));
                      }).map(value -> value.getStructValue().getFieldsOrThrow(Key.PORT_ID))
                      .containsExactlyInAnyOrderElementsOf(
                          () -> apLanPortsWithExpectedVni.stream()
                              .map(ApLanPort::getPortId).map(ValueUtils::stringValue).iterator());
                })));
  }

  private List<Map<String, Object>> generateVxLanEnabledPayload(String mac, int expectedVni, int ... portIndexes) {
    return Arrays.stream(portIndexes)
        .mapToObj(portIndex -> {
          Map<String, Object> personaMsgContent = new HashMap<>();
          personaMsgContent.put("mac", mac);
          personaMsgContent.put("vni", expectedVni);
          personaMsgContent.put("portIndex", portIndex);
          return personaMsgContent;
        }).toList();
  }

  private List<Map<String, Object>> generateVxLanDisabledPayload(String mac, int ... portIndexes) {
    return Arrays.stream(portIndexes)
        .mapToObj(portIndex -> {
          Map<String, Object> personaMsgContent = new HashMap<>();
          personaMsgContent.put("mac", mac);
          personaMsgContent.put("vni", null);
          personaMsgContent.put("portIndex", portIndex);
          return personaMsgContent;
        }).toList();
  }
}
