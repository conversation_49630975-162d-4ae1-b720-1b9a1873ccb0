package com.ruckus.cloud.wifi.integration.edge;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.integration.edge.ConsumeEdgeSdLanServiceTest.EdgeSdLanServiceContext.contextOf;
import static com.ruckus.cloud.wifi.integration.edge.ConsumeEdgeSdLanServiceTest.EdgeSdLanServiceContext.contextOfActivateGuestTunnel;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.edge.protobuf.BulkNetworkOperations;
import com.ruckus.cloud.edge.protobuf.EdgeSdLanService;
import com.ruckus.cloud.edge.protobuf.EdgeSdLanService.Action;
import com.ruckus.cloud.edge.protobuf.TunneledWlan;
import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TunnelProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import java.util.stream.IntStream;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("VxLanTunnelFeatureTest")
@WifiIntegrationTest
class ConsumeBulkNetworkOperationsTest {
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  private TunneledWlan createTunneledWlan(Network network, Venue venue) {
    return TunneledWlan.newBuilder().setNetworkId(network.getId()).setVenueId(venue.getId()).build();
  }

  private TunneledWlan createTunneledWlan(Network network, Venue venue, boolean isGuestTunnelUtilized) {
    return TunneledWlan.newBuilder().setNetworkId(network.getId()).setVenueId(venue.getId())
        .setIsGuestTunnelUtilized(isGuestTunnelUtilized).build();
  }

  @Test
  void activateNetworksAndDeactivateNetworksByBulkNetworkOperations(final Tenant tenant,
      final Venue venue, final TunnelProfile tunnelProfile) {

    //prepare test data
    final var networks = createAndSaveOpenNetworks(tenant, 4);
    final Network network1 = networks.get(0);
    final Network network2 = networks.get(1);
    final Network network3 = networks.get(2);
    final Network network4 = networks.get(3);
    createAndSaveNetworkVenues(tenant, venue, List.of(network1, network2, network3, network4));

    final TunneledWlan tunneledWlan1 = createTunneledWlan(network1, venue, true);
    final TunneledWlan tunneledWlan2 = createTunneledWlan(network2, venue, false);
    final TunneledWlan tunneledWlan3 = createTunneledWlan(network3, venue, true);
    final TunneledWlan tunneledWlan4 = createTunneledWlan(network4, venue, false);

    final String sdLanProfileId = txCtxExtension.newRandomId();;

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOf(tenant, sdLanProfileId, tunnelProfile).build(Action.CREATE_SD_LAN));

    final String activateNetworksRequestId = randomTxId();
    messageUtil.sendBulkNetworkOperations(tenant.getId(), activateNetworksRequestId,
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addActivateWlans(tunneledWlan1)
            .addActivateWlans(tunneledWlan2)
            .addActivateWlans(tunneledWlan3)
            .addActivateWlans(tunneledWlan4)
            .build());

    // Verify the isGuestTrafficTunnel value of SdLanProfileNetworkMapping
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId)).isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .singleElement()
        .extracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings,
            InstanceOfAssertFactories.list(SdLanProfileNetworkMapping.class))
        .hasSize(4)
        .satisfiesExactlyInAnyOrder(
            networkMapping -> {
              assertThat(networkMapping.getNetwork().getId()).isEqualTo(network1.getId());
              assertThat(networkMapping.getIsGuestTrafficTunnel()).isTrue();
            },
            networkMapping -> {
              assertThat(networkMapping.getNetwork().getId()).isEqualTo(network2.getId());
              assertThat(networkMapping.getIsGuestTrafficTunnel()).isFalse();
            },
            networkMapping -> {
              assertThat(networkMapping.getNetwork().getId()).isEqualTo(network3.getId());
              assertThat(networkMapping.getIsGuestTrafficTunnel()).isTrue();
            },
            networkMapping -> {
              assertThat(networkMapping.getNetwork().getId()).isEqualTo(network4.getId());
              assertThat(networkMapping.getIsGuestTrafficTunnel()).isFalse();
            });

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNetworksRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(4)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(network1.getId(), network2.getId(),
                    network3.getId(), network4.getId()));

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNetworksRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .as("The total operation count should be 4").hasSize(4)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .as("The modify WlanVenue operation count should be 4").hasSize(4)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .allSatisfy(wlanVenue -> {
          assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
          assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
        });


    final String deactivateNetworksRequestId = randomTxId();
    messageUtil.sendBulkNetworkOperations(tenant.getId(), deactivateNetworksRequestId,
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addDeactivateWlans(tunneledWlan2)
            .addDeactivateWlans(tunneledWlan3)
            .build());

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), deactivateNetworksRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(2)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(network1.getId(), network4.getId()));

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), deactivateNetworksRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .as("The total operation count should be 2").hasSize(2)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .as("The modify WlanVenue operation count should be 2").hasSize(2)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .allSatisfy(wlanVenue -> {
          assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(StringUtils.EMPTY);
          assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
        });
  }

  @Test
  void skipBindingNetworkProcessWhenNetworksAlreadyActivated(final Tenant tenant, final Venue venue,
      final TunnelProfile tunnelProfile) {

    // Prepare test data
    final var networks = createAndSaveOpenNetworks(tenant, 3);
    final Network network1 = networks.get(0);
    final Network network2 = networks.get(1);
    final Network network3 = networks.get(2);
    createAndSaveNetworkVenues(tenant, venue, List.of(network1, network2, network3));

    final TunneledWlan tunneledWlan1 = createTunneledWlan(network1, venue);
    final TunneledWlan tunneledWlan2 = createTunneledWlan(network2, venue);
    final TunneledWlan tunneledWlan3 = createTunneledWlan(network3, venue);

    final String sdLanProfileId = txCtxExtension.newRandomId();

    // Create SdLanProfile first
    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        contextOf(tenant, sdLanProfileId, tunnelProfile).build(Action.CREATE_SD_LAN));

    // First time activate networks
    final String firstTimeActivateRequestId = randomTxId();
    messageUtil.sendBulkNetworkOperations(tenant.getId(), firstTimeActivateRequestId,
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addActivateWlans(tunneledWlan1)
            .addActivateWlans(tunneledWlan2)
            .build());

    // Verify that the new network mappings for network1 & network2 was created
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId)).isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .singleElement()
        .extracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings,
            InstanceOfAssertFactories.list(SdLanProfileNetworkMapping.class))
        .hasSize(2)
        .extracting(networkMapping -> networkMapping.getNetwork().getId())
        .containsExactlyInAnyOrder(network1.getId(), network2.getId());

    // Second time activate the networks with some were activated already
    final String secondTimeActivateRequestId = randomTxId();
    messageUtil.sendBulkNetworkOperations(tenant.getId(), secondTimeActivateRequestId,
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addActivateWlans(tunneledWlan1)
            .addActivateWlans(tunneledWlan2)
            .addActivateWlans(tunneledWlan3)
            .build());

    // Verify that the new network mapping for network3 was created
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId)).isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .singleElement()
        .extracting(SdLanProfileRegularSetting::getSdLanProfileNetworkMappings,
            InstanceOfAssertFactories.list(SdLanProfileNetworkMapping.class))
        .hasSize(3)
        .extracting(networkMapping -> networkMapping.getNetwork().getId())
        .containsExactlyInAnyOrder(network1.getId(), network2.getId(), network3.getId());

    // Verify that only the new network (network3) was processed in the second binding
    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), secondTimeActivateRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .as("Only network3 should be processed").hasSize(1)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .as("Only network3 should be modified").hasSize(1)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .singleElement()
        .satisfies(wlanVenue -> {
          assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(tunnelProfile.getId());
          assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
        });
  }

  @Test
  void activateGuestNetworksAndDeactivateGuestNetworksByBulkNetworkOperations(final Tenant tenant,
      final Venue venue1, final TunnelProfile tunnelProfile) {

    //prepare test data
    final var networks = createAndSaveOpenNetworks(tenant, 4);
    final Network network1 = networks.get(0);
    final Network network2 = networks.get(1);
    final Network network3 = networks.get(2);
    final Network network4 = networks.get(3);

    final Venue venue2 = VenueTestFixture.randomVenue(tenant);
    repositoryUtil.createOrUpdate(venue2, tenant.getId(), randomTxId());
    createAndSaveNetworkVenues(tenant, venue1, List.of(network1, network2, network3, network4));
    createAndSaveNetworkVenues(tenant, venue2, List.of(network1, network2));

    final TunneledWlan tunneledWlan1 = createTunneledWlan(network1, venue1);
    final TunneledWlan tunneledWlan2 = createTunneledWlan(network2, venue1);
    final TunneledWlan tunneledWlan3 = createTunneledWlan(network3, venue1);
    final TunneledWlan tunneledWlan4 = createTunneledWlan(network4, venue1);
    final TunneledWlan tunneledWlan5 = createTunneledWlan(network1, venue2);
    final TunneledWlan tunneledWlan6 = createTunneledWlan(network2, venue2);

    final String sdLanProfileId = txCtxExtension.newRandomId();

    final String createSdLanRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), createSdLanRequestId,
        contextOf(tenant, sdLanProfileId, tunnelProfile).build(Action.CREATE_SD_LAN));

    messageUtil.sendBulkNetworkOperations(tenant.getId(), randomTxId(),
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue1.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addActivateWlans(tunneledWlan1)
            .addActivateWlans(tunneledWlan2)
            .addActivateWlans(tunneledWlan3)
            .addActivateWlans(tunneledWlan4)
            .build());
    messageUtil.sendBulkNetworkOperations(tenant.getId(), randomTxId(),
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue2.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addActivateWlans(tunneledWlan5)
            .addActivateWlans(tunneledWlan6)
            .build());

    final TunnelProfile guestTunnelProfile = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> t.setName(randomString().generate()));
    repositoryUtil.createOrUpdate(guestTunnelProfile, tenant.getId(), randomTxId());

    final String activateGuestTunnelRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), activateGuestTunnelRequestId,
        contextOfActivateGuestTunnel(tenant, sdLanProfileId, guestTunnelProfile).build(Action.ACTIVATE_GUEST_TUNNEL));
    assertThat(repositoryUtil.find(SdLanProfile.class, sdLanProfileId))
        .isNotNull()
        .extracting(SdLanProfile::getSdLanProfileRegularSettings,
            InstanceOfAssertFactories.list(SdLanProfileRegularSetting.class))
        .isNotEmpty()
        .allSatisfy(rs -> assertThat(rs.getGuestTrafficTunnelProfile().getId())
            .isEqualTo(guestTunnelProfile.getId()));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateGuestTunnelRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(guestTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));


    final String activateGuestNetworkRequestId = randomTxId();
    messageUtil.sendBulkNetworkOperations(tenant.getId(), activateGuestNetworkRequestId,
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue1.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addActivateGuestWlans(tunneledWlan1) // network1 + venue1
            .addActivateGuestWlans(tunneledWlan4) // network4 + venue1
            .addActivateGuestWlans(tunneledWlan6) // network2 + venue2
            .build());

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateGuestNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(guestTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(3)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(network1.getId(), network2.getId(), network4.getId()));


    final String deactivateGuestNetworkRequestId = randomTxId();
    messageUtil.sendBulkNetworkOperations(tenant.getId(), deactivateGuestNetworkRequestId,
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue1.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addDeactivateGuestWlans(tunneledWlan4) // network4 + venue1
            .addDeactivateGuestWlans(tunneledWlan6) // network2 + venue2
            .build());

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), deactivateGuestNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(guestTunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .containsEntry(Key.NETWORK_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(network1.getId()))));
  }

  @Test
  void activateNetworkAndChangeTunnelProfileInSdLanProfile(final Tenant tenant, final Venue venue,
      final TunnelProfile tunnelProfile) {

    final String sdLanProfileId = txCtxExtension.newRandomId();

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        EdgeSdLanService.newBuilder()
            .setId(sdLanProfileId)
            .setTenantId(tenant.getId())
            .setTunnelProfileId(tunnelProfile.getId())
            .setAction(Action.CREATE_SD_LAN)
            .build());

    final var networks = createAndSaveDpskNetworks(tenant, 3);
    createAndSaveNetworkVenues(tenant, venue, networks);

    messageUtil.sendBulkNetworkOperations(tenant.getId(), randomTxId(),
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addAllActivateWlans(() -> networks.stream()
                .map(network -> createTunneledWlan(network, venue)).iterator())
            .build());


    final TunnelProfile newTunnelProfile = TunnelProfileTestFixture.randomTunnelProfile(
        tenant, (t) -> t.setName(randomString().generate()));
    repositoryUtil.createOrUpdate(newTunnelProfile, tenant.getId(), randomTxId());

    final String updateSdLanRequestId = randomTxId();
    messageUtil.sendEdgeSdLanService(tenant.getId(), updateSdLanRequestId,
        EdgeSdLanService.newBuilder()
            .setId(sdLanProfileId)
            .setTenantId(tenant.getId())
            .setTunnelProfileId(newTunnelProfile.getId())
            .setAction(Action.UPDATE_SD_LAN)
            .build());

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), updateSdLanRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .as("The total operation count should be 3").hasSize(3)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .as("The modify WlanVenue operation count should be 2").hasSize(3)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .allSatisfy(wlanVenue -> {
          assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(newTunnelProfile.getId());
          assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
        });

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), updateSdLanRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .hasSize(2)
        .allSatisfy(op -> {
          if (StringUtils.equals(tunnelProfile.getId(), op.getId())) {
            assertThat(op.getDocMap())
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
                .containsEntry(Key.NETWORK_SEGMENTATION_IDS, ValueUtils.listValue(List.of()))
                .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()));
          } else if (StringUtils.equals(newTunnelProfile.getId(), op.getId())) {
            assertThat(op.getDocMap())
                .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
                    ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
                .containsEntry(Key.NETWORK_SEGMENTATION_IDS, ValueUtils.listValue(List.of()))
                .hasEntrySatisfying(Key.NETWORK_IDS,
                    networkIdListValue -> assertThat(networkIdListValue.getListValue())
                        .extracting(ListValue::getValuesList,
                            InstanceOfAssertFactories.list(Value.class))
                        .hasSize(networks.size())
                        .extracting(Value::getStringValue)
                        .containsExactlyInAnyOrderElementsOf(() -> networks.stream().map(Network::getId).iterator()));
          }
        });
  }

  @Test
  void activateNetworkAndDeactivateNetworkInSdLanProfile(final Tenant tenant, final Venue venue,
      final TunnelProfile tunnelProfile) {

    final String sdLanProfileId = txCtxExtension.newRandomId();

    messageUtil.sendEdgeSdLanService(tenant.getId(), randomTxId(),
        EdgeSdLanService.newBuilder()
            .setId(sdLanProfileId)
            .setTenantId(tenant.getId())
            .setTunnelProfileId(tunnelProfile.getId())
            .setAction(Action.CREATE_SD_LAN)
            .build());

    final var networks = createAndSaveDpskNetworks(tenant, 5);
    createAndSaveNetworkVenues(tenant, venue, networks);

    final String activateNetworkRequestId = randomTxId();
    messageUtil.sendBulkNetworkOperations(tenant.getId(), activateNetworkRequestId,
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addAllActivateWlans(() -> networks.stream()
                .map(network -> createTunneledWlan(network, venue)).iterator())
            .build());

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .as("The total operation count should be 5").hasSize(5)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .as("The modify WlanVenue operation count should be 5").hasSize(5)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .allSatisfy(wlanVenue -> {
          assertThat(wlanVenue.getVxlanTunnelProfileId()).isEqualTo(
              tunnelProfile.getId());
          assertThat(wlanVenue.getCentralizedForwardingEnabled()).isTrue();
        });

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), activateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(networks.size())
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrderElementsOf(() -> networks.stream().map(Network::getId).iterator()));


    final var removingNetworks = List.of(networks.get(0), networks.get(3));
    final String deactivateNetworkRequestId = randomTxId();
    messageUtil.sendBulkNetworkOperations(tenant.getId(), deactivateNetworkRequestId,
        BulkNetworkOperations.newBuilder()
            .setTenantId(tenant.getId())
            .setVenueId(venue.getId())
            .setEdgeSdLanServiceId(sdLanProfileId)
            .addAllDeactivateWlans(() -> removingNetworks.stream()
                .map(network -> createTunneledWlan(network, venue)).iterator())
            .build());

    assertThat(messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), deactivateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .as("The total operation count should be 2").hasSize(2)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .filteredOn(op -> op.getAction() != com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .as("The modify WlanVenue operation count should be 2").hasSize(2)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .allSatisfy(wlanVenue -> {
          assertThat(wlanVenue.getVxlanTunnelProfileId()).isEmpty();
          assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
        })
        .extracting(WlanVenue::getWlanId)
        .containsExactlyInAnyOrder(removingNetworks.get(0).getId(), removingNetworks.get(1).getId());

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), deactivateNetworkRequestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .singleElement()
        .extracting(Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
        .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
            ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))))
        .hasEntrySatisfying(Key.NETWORK_IDS,
            networkIdListValue -> assertThat(networkIdListValue.getListValue())
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(3)
                .extracting(Value::getStringValue)
                .containsExactlyInAnyOrder(networks.get(1).getId(), networks.get(2).getId(), networks.get(4).getId()));
  }

  private List<OpenNetwork> createAndSaveOpenNetworks(Tenant tenant, int count) {
    return IntStream.rangeClosed(1, count)
        .mapToObj(i -> {
          final var openNetwork = network(OpenNetwork.class).setName(randomString()).generate();
          openNetwork.getWlan().setNetwork(openNetwork);
          openNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
          repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());
          return openNetwork;
        }).toList();
  }

  private List<DpskNetwork> createAndSaveDpskNetworks(Tenant tenant, int count) {
    return IntStream.rangeClosed(1, count)
        .mapToObj(i -> {
          final var authRadius = RadiusTestFixture.authRadius();
          repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());

          final var dpskNetwork = network(DpskNetwork.class).setName(randomString()).generate();
          dpskNetwork.getWlan().setNetwork(dpskNetwork);
          dpskNetwork.setAuthRadius(authRadius);
          dpskNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
          authRadius.setAuthNetworks(List.of(dpskNetwork));
          repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());

          final var authRadiusService = new AuthRadiusService();
          authRadiusService.setRadius(authRadius);
          repositoryUtil.createOrUpdate(authRadiusService, tenant.getId(), randomTxId());

          return dpskNetwork;
        }).toList();
  }

  private void createAndSaveNetworkVenues(Tenant tenant, Venue venue, List<? extends Network> networks) {
    networks.forEach(n -> {
      final var networkVenue = Generators.networkVenue()
          .setNetwork(always(n)).setVenue(always(venue)).generate();
      n.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    });
  }
}
