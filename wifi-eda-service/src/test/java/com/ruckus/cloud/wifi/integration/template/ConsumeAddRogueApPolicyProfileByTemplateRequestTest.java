package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ROGUE_AP_POLICY_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ROGUE_AP_POLICY_PROFILE_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_VENUE_ROGUE_AP;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_VENUE_TEMPLATE_ROGUE_AP;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.TemplateCreateInstanceOperation.newViewProperty;
import static com.ruckus.cloud.wifi.integration.ConsumeRogueApPolicyProfileRequestTest.assertViewmodelCollector;
import static com.ruckus.cloud.wifi.integration.ConsumeRogueApPolicyProfileRequestTest.assertViewmodelCollectorRogueApPolicy;
import static com.ruckus.cloud.wifi.integration.ConsumeRogueApPolicyProfileRequestTest.assertViewmodelCollectorVenueRogueAp;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.service.impl.RogueApPolicyProfileServiceCtrlImpl.DEFAULT_ROGUE_AP_POLICY_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.integration.ConsumeRogueApPolicyProfileRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated // Wait for non-RBAC API cleanup
@Slf4j
@WifiIntegrationTest
public class ConsumeAddRogueApPolicyProfileByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  void create_rogue_policy_instance_and_apply_to_ec_venue(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var apVersion = mspTenant.getLatestReleaseVersion();

    // create msp venue and rogue

    Venue v = createVenueTemplate(mspTenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();

    // create rogue template and apply to venue
    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    edaAddRogueApPolicyProfileTemplate(mspTenantId, userName, map(p));
    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, mspTenantId);

    RogueClassificationPolicy templateRoguePolicy = getRogueApPolicyProfileTemplates().get(0);
    assertEquals(p.getName(), templateRoguePolicy.getName());

    {
      VenueRogueAp venueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();
      venueRogueAp.setRoguePolicyId(templateRoguePolicy.getId());
      edaUpdateVenueTemplateRogueAp(mspTenantId, userName, venueId, map(venueRogueAp));
      assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_ROGUE_AP, mspTenantId);

      VenueRogueAp getVenueRogueAp = getVenueRogueAp(venueId, true);
      assertTrue(getVenueRogueAp.getEnabled());
      assertEquals(templateRoguePolicy.getId(), getVenueRogueAp.getRoguePolicyId());
      assertEquals(2, getRogueApPolicyProfileTemplates().size()); // including default after binding
    }

    // create ec tenant and ec venue

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    Venue ecV = createVenue(ecTenant, "v", apVersion);
    ApGroup ecApGroup = createApGroup(ecV, "apg");
    String ecVenueId = ecV.getId();

    clearMessage();

    // create ec rogue policy by msp template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(instanceId, templateRoguePolicy, ecTenantId, mspTenantId,
        mspRequestId, userName, instanceCreateRequest);

    assertActivityPlan(ADD_ROGUE_AP_POLICY_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_BY_TEMPLATE, mspTenantId);

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    RogueClassificationPolicy ecRogueClassificationPolicy = getRogueApPolicy(instanceId);

    {
      assertDdccmCfgRequestNotSent(ecTenantId);

      // 1 RoguePolicy
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, ecTenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 1),
          () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
              OpType.ADD, ecRogueClassificationPolicy.getId(), "Rogue Policy", 0.0)
      );
    }

    // ---------------------------------

    // apply ec venue to the new instance
    {
      VenueRogueAp ecVenueRogueAp = new VenueRogueAp();
      ecVenueRogueAp.setEnabled(true);
      ecVenueRogueAp.setReportThreshold((short) 0);
      ecVenueRogueAp.setRoguePolicyId(ecRogueClassificationPolicy.getId());
      edaUpdateVenueRogueAp(ecTenantId, userName, ecVenueId, map(ecVenueRogueAp));
      assertActivityStatusSuccess(UPDATE_VENUE_ROGUE_AP, ecTenantId);

      assertEquals(2, getRogueApPolicies().size()); // including default
      VenueRogueAp getVenueRogueAp = getVenueRogueAp(ecVenueId, false);
      assertTrue(getVenueRogueAp.getEnabled());
      assertEquals(ecRogueClassificationPolicy.getId(), getVenueRogueAp.getRoguePolicyId());
    }

    RogueClassificationPolicy ecDefaultRogueClassificationPolicy =
        getRogueApPolicyProfile(ecTenantId, DEFAULT_ROGUE_AP_POLICY_NAME, false);

    // assert ddccm - venue-rogue
    var ddccmOperations = receiveDdccmOperations(1, ecTenantId);
    assertAll("assert ddccm publisher",
        () -> ConsumeRogueApPolicyProfileRequestTest.assertDdccmPublisher(ddccmOperations, 1),
        () -> ConsumeRogueApPolicyProfileRequestTest.assertDdccmPublisherVenue(ddccmOperations, ecVenueId, true, 0)
    );

    // 2 RoguePolicy, 1 Venue
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 3),
        () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
            OpType.ADD, ecDefaultRogueClassificationPolicy.getId(), DEFAULT_ROGUE_AP_POLICY_NAME, 0.0),
        () -> assertViewmodelCollectorRogueApPolicy(viewmodelOperations,
            OpType.MOD, ecRogueClassificationPolicy.getId(), "Rogue Policy", 1.0),
        () -> assertViewmodelCollectorVenueRogueAp(viewmodelOperations,
            OpType.MOD, ecVenueId, ecRogueClassificationPolicy.getId())
    );
  }

  private void addByTemplate(String instanceId, RogueClassificationPolicy templateRoguePolicy,
      String ecTenantId, String mspTenantId, String mspRequestId, String userName,
      TemplateInstanceCreateRequest instanceCreateRequest) {

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ADD_ROGUE_AP_POLICY_PROFILE, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.ROGUE_CLASSIFICATION_POLICY,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, templateRoguePolicy.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_ROGUE_AP_POLICY_PROFILE_BY_TEMPLATE,
        ADD_ROGUE_AP_POLICY_PROFILE_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);
  }

  @Test
  void create_rogue_policy_instance_and_apply_venue(Tenant mspTenant) throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var apVersion = mspTenant.getLatestReleaseVersion();

    // create msp venue and rogue

    Venue v = createVenueTemplate(mspTenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();

    // create rogue template and apply to venue
    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    edaAddRogueApPolicyProfileTemplate(mspTenantId, userName, map(p));
    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, mspTenantId);

    RogueClassificationPolicy templateRoguePolicy = getRogueApPolicyProfileTemplates().get(0);
    assertEquals(p.getName(), templateRoguePolicy.getName());

    {
      VenueRogueAp venueRogueAp = new VenueRogueAp();
      venueRogueAp.setEnabled(true);
      venueRogueAp.setReportThreshold((short) 90);
      venueRogueAp.setRoguePolicyId(templateRoguePolicy.getId());
      edaUpdateVenueTemplateRogueAp(mspTenantId, userName, venueId, map(venueRogueAp));
      assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_ROGUE_AP, mspTenantId);

      VenueRogueAp getVenueRogueAp = getVenueRogueAp(venueId, true);
      assertTrue(getVenueRogueAp.getEnabled());
      assertEquals(90, getVenueRogueAp.getReportThreshold().intValue());
      assertEquals(templateRoguePolicy.getId(), getVenueRogueAp.getRoguePolicyId());
      assertEquals(2, getRogueApPolicyProfileTemplates().size()); // including default after binding
    }

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    ecTenant.setLatestReleaseVersion(apVersion);
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create ec rogue policy by msp template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(instanceId, templateRoguePolicy, ecTenantId, mspTenantId,
        mspRequestId, userName, instanceCreateRequest);

    assertActivityPlan(ADD_ROGUE_AP_POLICY_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_BY_TEMPLATE, mspTenantId);

    clearMessage();

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    // apply venue template to create ec venue and set venue-rogue
    String ecVenueId = randomId();
    var event = buildTemplateInstanceEvent(v, ecVenueId, ecTenantId);
    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(ecTenant.getId(), requestId, event);

    assertActivityStatusSuccess("AddVenueByTemplateInWifi", ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST, ecTenantId);

    {
      VenueRogueAp getVenueRogueAp = getVenueRogueAp(ecVenueId, false);
      assertTrue(getVenueRogueAp.getEnabled());
      assertEquals(90, getVenueRogueAp.getReportThreshold().intValue());
      assertEquals(instanceId, getVenueRogueAp.getRoguePolicyId());
      assertEquals(2, getRogueApPolicies().size()); // including default after binding
    }
  }

  @Test
  void apply_venue_with_default_rogue_policy(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var apVersion = mspTenant.getLatestReleaseVersion();

    // create msp venue and rogue

    Venue v = createVenueTemplate(mspTenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();

    // apply default rogue to venue
    {
      VenueRogueAp venueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();
      edaUpdateVenueTemplateRogueAp(mspTenantId, userName, venueId, map(venueRogueAp));
      assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_ROGUE_AP, mspTenantId);

      VenueRogueAp getVenueRogueAp = getVenueRogueAp(venueId, true);
      assertTrue(getVenueRogueAp.getEnabled());
    }

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    ecTenant.setLatestReleaseVersion(apVersion);
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    // apply venue template to create ec venue and set venue-rogue
    String ecVenueId = randomId();
    var event = buildTemplateInstanceEvent(v, ecVenueId, ecTenantId);
    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(ecTenant.getId(), requestId, event);

    assertActivityStatusSuccess("AddVenueByTemplateInWifi", ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST, ecTenantId);

    {
      List<RogueClassificationPolicy> getRogueApPolicies = getRogueApPolicies();
      assertEquals(1, getRogueApPolicies.size()); // only default
      VenueRogueAp getVenueRogueAp = getVenueRogueAp(ecVenueId, false);
      assertTrue(getVenueRogueAp.getEnabled());
      assertEquals(getRogueApPolicies.get(0).getId(), getVenueRogueAp.getRoguePolicyId());
    }
  }

  @Test
  public void ec_add_rogue_policy_fail_then_msp_activity_should_fail(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add template

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    edaAddRogueApPolicyProfileTemplate(mspTenantId, userName, map(p));
    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, mspTenantId);
    RogueClassificationPolicy templateRoguePolicy = getRogueApPolicyProfile(mspTenantId, "Rogue Policy", true);

    // ec tenant add rogue with same name earlier to make template-create-instance fail

    RogueClassificationPolicy ecRogue1 = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    ecRogue1.setRules(null);
    repositoryUtil.createOrUpdate(ecRogue1, ecTenantId, randomTxId());
    RogueClassificationPolicy ecRogueAdded1 = getRogueApPolicyProfile(ecTenantId, "Rogue Policy", false);

    // create ec instance by msp template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(instanceId, templateRoguePolicy, ecTenantId, mspTenantId,
        mspRequestId, userName, instanceCreateRequest);

    assertActivityPlan(ADD_ROGUE_AP_POLICY_PROFILE, ecTenantId);
    assertActivityStatusFail(ADD_ROGUE_AP_POLICY_PROFILE, ecTenantId);
    assertActivityStatusFail(ADD_ROGUE_AP_POLICY_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void ec_add_rogue_policy_fail_msp_should_get_activity_fail_because_incorrect_overrides(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add template

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    edaAddRogueApPolicyProfileTemplate(mspTenantId, userName, map(p));
    assertActivityStatusSuccess(ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE, mspTenantId);
    RogueClassificationPolicy templateRoguePolicy = getRogueApPolicyProfile(mspTenantId, "Rogue Policy", true);

    // create ec instance by msp template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    instanceCreateRequest.setOverrides(List.of(newViewProperty("x", "x"))); // incorrect

    String instanceId = randomId();
    String mspRequestId = randomTxId();

    addByTemplate(instanceId, templateRoguePolicy, ecTenantId, mspTenantId,
        mspRequestId, userName, instanceCreateRequest);

    assertActivityPlanNotSent(ecTenantId);
    assertActivityStatusFail(ADD_ROGUE_AP_POLICY_PROFILE_BY_TEMPLATE, mspTenantId);
  }
}
