package com.ruckus.cloud.wifi.service.integration;

import com.ruckus.cloud.wifi.core.autoconfig.HibernateAutoConfiguration;
import com.ruckus.cloud.wifi.repository.UrlFilteringPolicyRepository;
import com.ruckus.cloud.wifi.service.impl.ExtendedUrlFilteringPolicyServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedUrlFilteringPolicyServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Import;

@WifiJpaDataTest
public class ExtendedUrlFilteringPolicyServiceCtrlImplTest {

  @Autowired
  private ExtendedUrlFilteringPolicyServiceCtrlImpl extendedUrlFilteringPolicyServiceCtrl;

  @Autowired
  private UrlFilteringPolicyRepository urlFilteringPolicyRepository;

  @Test
  void nonNullTest() {
    Assertions.assertNotNull(extendedUrlFilteringPolicyServiceCtrl);
  }

  @TestConfiguration
  @Import({HibernateAutoConfiguration.class, ExtendedUrlFilteringPolicyServiceCtrlImplTestConfig.class})
  static class TestConfig {

  }
}
