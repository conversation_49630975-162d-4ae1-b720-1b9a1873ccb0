package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.openWlan;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.openWlanAdvancedCustomization;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertWlanSoftly;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.ConsumerRecordAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.VlanPoolRestCtrl.VlanPoolMapper;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.OpenNetworkGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("VlanPoolTemplateTest")
@WifiIntegrationTest
public class ConsumeNetworkTemplateWithVlanPoolRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class AddNetworkTemplateWithVlanPoolTest {

    private String vlanPoolTemplateId;

    private com.ruckus.cloud.wifi.eda.viewmodel.VlanPool vlanPool;

    @BeforeEach
    void givenVlanPoolTemplatePersistedInDb(Tenant tenant, @Template VlanPool vlanPoolTemplate) {
      vlanPool = VlanPoolMapper.INSTANCE.ServiceVlanPool2VlanPool(vlanPoolTemplate);
      vlanPoolTemplateId = vlanPool.getId();
    }

    @Payload("openNetworkWithVlanPool")
    private OpenNetworkGenerator networkWithVlanPool() {
      return Generators.openNetwork()
          .setWlan(openWlan()
              .setVlanId(always(null))
              .setAdvancedCustomization(openWlanAdvancedCustomization().setVlanPool(always(vlanPool)))
              .setMacAddressAuthentication(always(false)));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_TEMPLATE, payload = @Payload("openNetworkWithVlanPool"))
    void thenShouldHandleTheRequestSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
        @Payload("openNetworkWithVlanPool") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload, vlanPoolTemplateId);
    }
  }

  @Nested
  class UpdateNetworkTemplateWithVlanPoolTest {

    private String vlanPoolTemplateId;

    private String networkId;

    private com.ruckus.cloud.wifi.eda.viewmodel.VlanPool vlanPool;

    @BeforeEach
    void givenOneOpenNetworkPersistedInDbAndActivatedOnOneVenueAndVlanPoolTemplateInDb(Tenant tenant,
        @Template Venue venue, @Template VlanPool vlanPoolTemplate) {
      vlanPool = VlanPoolMapper.INSTANCE.ServiceVlanPool2VlanPool(vlanPoolTemplate);
      vlanPoolTemplateId = vlanPool.getId();

      final var openNetwork = network(OpenNetwork.class).generate();
      openNetwork.setIsTemplate(true);
      openNetwork.getWlan().setNetwork(openNetwork);
      openNetwork.getWlan().setMacAddressAuthentication(false);
      repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
        .setNetwork(always(openNetwork)).setVenue(always(venue)).generate();
      openNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      networkId = openNetwork.getId();
    }

    @Payload("openNetworkWithVlanPool")
    private OpenNetworkGenerator networkWithVlanPool() {
      return Generators.openNetwork()
          .setId(nullValue(String.class))
          .setName(serialName("UpdatedOpenNetwork"))
          .setWlan(openWlan()
              .setVlanId(always(null))
              .setAdvancedCustomization(openWlanAdvancedCustomization().setVlanPool(always(vlanPool)))
              .setMacAddressAuthentication(always(false)));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkTemplateId", networkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK_TEMPLATE, payload = @Payload("openNetworkWithVlanPool"))
    void thenShouldHandleTheRequestSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
      @Payload("openNetworkWithVlanPool") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      validateResult(txCtx, apiAction, networkId, payload, vlanPoolTemplateId);
    }
  }

  private void validateResult(TxCtxHolder.TxCtx txCtx, CfgAction apiAction, String networkId,
      com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload, String vlanPoolTemplateId) {
    validateActivityMessages(apiAction);
    validateRepositoryData(txCtx, apiAction, networkId, payload, vlanPoolTemplateId);
    validateWifiCfgChangeMessages(txCtx, apiAction, List.of(networkId), payload);
    validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
    validateCmnCfgCollectorMessages(txCtx, apiAction, List.of(networkId), vlanPoolTemplateId);
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
     messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(KafkaMessageAssertions.assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(KafkaMessageAssertions.assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());
  }

  private void validateRepositoryData(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
    String networkId, com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload, String vlanPoolTemplateId) {
    if (networkId == null) {
      final String requestId = txCtx.getTxId();
      final String tenantId = txCtx.getTenant();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty, "should be empty");
      return;
    }

    final OpenNetwork network = repositoryUtil.find(OpenNetwork.class, networkId);

    if (apiAction == null || apiAction == CfgAction.DELETE_NETWORK_TEMPLATE || payload == null) {
      assertThat(network).isNull();
      return;
    }

    assertThat(network)
        .isNotNull()
        .matches(n -> Objects.equals(n.getId(), networkId))
        .matches(n -> Objects.equals(n.getName(), payload.getName()))
        .matches(n -> Objects.equals(n.getDescription(), payload.getDescription()));

    assertThat(network.getWlan())
        .isNotNull()
        .extracting(Wlan::getAdvancedCustomization)
        .matches(customization -> customization.getVlanPool().getId().equals(vlanPoolTemplateId));
  }

  private void validateWifiCfgChangeMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
    List<String> networkIdList, com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
    if (apiAction == null || networkIdList == null) {
      messageCaptors.getWifiCfgChangeMessageCaptor().assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final var wifiCfgChangeMessage = messageUtil.receive(kafkaTopicProvider.getWifiCfgChange());

    assertThat(wifiCfgChangeMessage).isNotNull()
        .satisfies(msg -> assertThat(msg.key()).isEqualTo(txCtx.getTenant()))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction.key()))
        .extracting(ConsumerRecord::value).isNotNull();

    assertThatNoException().isThrownBy(() ->
      assertThat(WifiConfigChange.parseFrom(wifiCfgChangeMessage.value()))
        .satisfies(msg -> assertSoftly(softly -> {
          softly.assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
          softly.assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
        }))
        .extracting(WifiConfigChange::getOperationList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
        .satisfies(ops -> {
          assertThat(ops)
            .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasOpenNetwork)
            .filteredOn(op -> op.getAction() == wifiProtoAction(apiAction))
            .as("The %s OpenNetwork operation count should be 1", wifiProtoAction(apiAction).name())
            .hasSize(1)
            .singleElement()
            .extracting(com.ruckus.cloud.wifi.proto.Operation::getOpenNetwork)
            .isNotNull();

          assertThat(ops)
            .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasWlan)
            .filteredOn(op -> op.getAction() == wifiProtoAction(apiAction))
            .as("The %s Wlan operation count should be 1", wifiProtoAction(apiAction).name())
            .hasSize(1)
            .singleElement()
            .extracting(com.ruckus.cloud.wifi.proto.Operation::getWlan)
            .satisfies(assertWlanSoftly(payload.getWlan()));
        }));
  }

  private void validateDdccmCfgRequestMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
    List<String> networkIdList, com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
    if (apiAction == null || apiAction == CfgAction.ADD_NETWORK_TEMPLATE
        || networkIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final var ddccmCfgRequestMessage = messageUtil.receive(kafkaTopicProvider.getDdccmCfgRequest());

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(ConsumerRecord::value).isNotNull();

    if (!apiAction.equals(CfgAction.UPDATE_NETWORK_TEMPLATE)) {
      return;
    }

    assertThatNoException().isThrownBy(() ->
      assertThat(WifiConfigRequest.parseFrom(ddccmCfgRequestMessage.value()))
        .extracting(WifiConfigRequest::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .describedAs("Should contains %s WlanVenue operations", networkIdList.size())
        .hasSize(networkIdList.size())
        .allMatch(op -> StringUtils.isNotEmpty(op.getId()),
          "All the ids of the WlanVenue operations should not be empty")
        .allMatch(op -> op.getAction() == action(apiAction),
          String.format("All the actions of the WlanVenue operations should be [%s]", apiAction))
        .allSatisfy(op -> {
          if (op.getAction() == Action.DELETE) {
            assertThat(op)
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
              .matches(n -> networkIdList.contains(n.getWlanId()));
          } else {
            assertThat(payload).isNotNull();
            assertSoftly(softly -> {
              softly.assertThat(op.getWlanVenue().getWlanId())
                .matches(networkIdList::contains);
              softly.assertThat(op.getWlanVenue().getSsid())
                .isEqualTo(payload.getWlan().getSsid());
              Optional.ofNullable(payload.getWlan().getAdvancedCustomization())
                .map(com.ruckus.cloud.wifi.eda.viewmodel.OpenWlanAdvancedCustomization::getVlanPool)
                .map(com.ruckus.cloud.wifi.eda.viewmodel.VlanPool::getId).ifPresentOrElse(
                  // payload has wlan.advancedCustomization.vlanPool.id
                  vlanPoolId -> softly.assertThat(op.getWlanVenue())
                    .matches(WlanVenue::hasVlanPoolId)
                    .extracting(WlanVenue::getVlanPoolId)
                    .extracting(StringValue::getValue)
                    .isEqualTo(vlanPoolId),
                  // payload doesn't have wlan.advancedCustomization.vlanPool.id
                  () -> softly.assertThat(op.getWlanVenue())
                    .matches(WlanVenue::hasVlanId)
                    .extracting(WlanVenue::getVlanId)
                    .extracting(Int32Value::getValue)
                    .isEqualTo(payload.getWlan().getVlanId().intValue()));
            });
          }
        }));
  }

  private void validateCmnCfgCollectorMessages(TxCtx txCtx, CfgAction apiAction, List<String> networkIdList, String vlanPoolTemplateId) {
    if (apiAction == null || networkIdList == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtx.getTxId();
    final String tenantId = txCtx.getTenant();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
            .hasSize(networkIdList.size())
            .allMatch(op -> networkIdList.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction))
            .allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> networkIdList.contains(doc.get(Key.ID).getStringValue()))
                    .matches(doc -> Value.TYPE_NETWORK.equals(doc.get(Key.TYPE)))
                    .matches(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()));
              }
            }));
    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.VLAN_POOL_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(1)
            .allMatch(op -> vlanPoolTemplateId.equals(op.getId()))
            .allMatch(op -> op.getOpType() == OpType.MOD)
            .allMatch(op -> Boolean.TRUE.equals(op.getDocMap().get(Key.IS_TEMPLATE).getBoolValue()))
            .allMatch(op -> networkIdList.containsAll(op.getDocMap().get(Key.WIFI_NETWORK_IDS)
                .getListValue().getValuesList().stream().map(v -> v.getStringValue()).collect(Collectors.toList()))));
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK_TEMPLATE -> ApiFlowNames.ADD_NETWORK_TEMPLATE;
      case UPDATE_NETWORK_TEMPLATE -> ApiFlowNames.UPDATE_NETWORK_TEMPLATE;
      case DELETE_NETWORK_TEMPLATE -> ApiFlowNames.DELETE_NETWORK_TEMPLATE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private com.ruckus.cloud.wifi.proto.Operation.Action wifiProtoAction(CfgAction apiAction) {
    switch (apiAction) {
      case ADD_NETWORK_TEMPLATE:
        return com.ruckus.cloud.wifi.proto.Operation.Action.ADD;
      case UPDATE_NETWORK_TEMPLATE:
        return com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY;
      case DELETE_NETWORK_TEMPLATE:
        return com.ruckus.cloud.wifi.proto.Operation.Action.DELETE;
    }
    throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
  }

  private Action action(CfgAction apiAction) {
    switch (apiAction) {
      case ADD_NETWORK_TEMPLATE:
        return Action.ADD;
      case UPDATE_NETWORK_TEMPLATE:
        return Action.MODIFY;
      case DELETE_NETWORK_TEMPLATE:
        return Action.DELETE;
    }
    throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK_TEMPLATE -> OpType.ADD;
      case UPDATE_NETWORK_TEMPLATE -> OpType.MOD;
      case DELETE_NETWORK_TEMPLATE -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
