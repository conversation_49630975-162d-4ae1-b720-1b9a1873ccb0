package com.ruckus.cloud.wifi.requirement.feature;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModel;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModels;
import com.ruckus.cloud.wifi.viewmodel.FeatureGroup;
import com.ruckus.cloud.wifi.viewmodel.FeatureLevel;
import com.ruckus.cloud.wifi.viewmodel.FeatureType;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;

@MockitoSettings
class ApFeatureTest {

  @Spy
  private ApFeature unit;

  @Test
  void getRequirementTest() {
    assertThat(unit.getRequirement()).isNotNull()
        .satisfies(apRequirementV1 -> {
          assertThat(apRequirementV1.getFeatureLevel()).isNull();
          assertThat(apRequirementV1.getFeatureType()).isNull();
          assertThat(apRequirementV1.getFeatureGroup()).isEmpty();
          assertThat(apRequirementV1.getRequirements()).isNullOrEmpty();
          assertThat(apRequirementV1.getVenueIds()).isNotNull().isEmpty();
          assertThat(apRequirementV1.getWifiNetworkIds()).isNotNull().isEmpty();
        });

    doReturn(List.of(ApFirmwareModels.builder()
        .firmware("7.1.0.510.100")
        .models(Set.of("R550", "R560"))
        .build()))
        .when(unit).getRequirements();

    assertThat(unit.getRequirement().getRequirements()).isNotNull()
        .isEqualTo(unit.getRequirements());
  }

  private static Stream<Arguments> getRequirementParameterizedTest() {
    return Stream.of(FeatureLevel.values()).flatMap(featureLevel ->
        Stream.of(FeatureType.values()).flatMap(featureType ->
            Stream.of(FeatureGroup.values()).map(featureGroup ->
                arguments(featureLevel, featureType, featureGroup.getDisplayName()))));
  }

  @ParameterizedTest
  @MethodSource
  void getRequirementParameterizedTest(
      FeatureLevel featureLevel,
      FeatureType featureType,
      String featureGroup) {
    doReturn(featureLevel).when(unit).getFeatureLevel();
    doReturn(featureType).when(unit).getFeatureType();
    doReturn(featureGroup).when(unit).getFeatureGroup();

    assertThat(unit.getRequirement()).isNotNull()
        .satisfies(apRequirementV1 -> {
          assertThat(apRequirementV1.getFeatureLevel()).isEqualTo(featureLevel);
          assertThat(apRequirementV1.getFeatureType()).isEqualTo(featureType);
          assertThat(apRequirementV1.getFeatureGroup()).isEqualTo(featureGroup);
        });
  }

  @Test
  void isDeviceSupportTest() {
    assertThat(unit.isDeviceSupport(ApFirmwareModel.builder().build())).isTrue();

    doReturn(List.of(ApFirmwareModels.builder()
        .firmware("7.1.0.510.100")
        .models(Set.of("R550", "R560"))
        .build()))
        .when(unit).getRequirements();

    assertThat(unit.isDeviceSupport(ApFirmwareModel.builder().build())).isFalse();

    assertThat(unit.isDeviceSupport(ApFirmwareModel.builder()
        .firmware("7.1.0.510.99")
        .build())).isFalse();

    assertThat(unit.isDeviceSupport(ApFirmwareModel.builder()
        .firmware("7.1.0.510.100")
        .build())).isFalse();

    assertThat(unit.isDeviceSupport(ApFirmwareModel.builder()
        .firmware("7.1.0.510.100")
        .model("R550")
        .build())).isTrue();

    assertThat(unit.isDeviceSupport(ApFirmwareModel.builder()
        .firmware("7.1.0.510.101")
        .model("R550")
        .build())).isTrue();

    assertThat(unit.isDeviceSupport(ApFirmwareModel.builder()
        .firmware("7.1.0.510.101")
        .model("R560")
        .build())).isTrue();

    assertThat(unit.isDeviceSupport(ApFirmwareModel.builder()
        .firmware("7.1.0.510.101")
        .model("R350")
        .build())).isFalse();
  }
}
