package com.ruckus.cloud.wifi.integration.wifinetwork;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomIp;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkTestFixture.wisprPage;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.CloneSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.DnsProxy;
import com.ruckus.cloud.wifi.eda.servicemodel.DnsProxyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfileNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.integration.AbstractRadiusRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.DPSKNetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeCloneWifiNetworkTemplateRequestTest extends AbstractRadiusRequestTest {
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class whenConsumeCloneNetworkRequest {
    @Test
    void thenCloneOpenNetwork(Tenant tenant, @Template Network networkInDB, @Template WifiCallingServiceProfile wifiCallingServiceProfile) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var request = getCloneRequest(randomId());

      // DNS Proxy
      var dnsProxy = createDnsProxy(tenant);
      networkInDB.getWlan().getAdvancedCustomization().setDnsProxyEnabled(true);
      networkInDB.getWlan().getAdvancedCustomization().setDnsProxy(dnsProxy);

      // wifi-calling
      var wifiCallingServiceProfileNetwork = new WifiCallingServiceProfileNetwork();
      wifiCallingServiceProfileNetwork.setNetwork(networkInDB);
      wifiCallingServiceProfileNetwork.setWifiCallingServiceProfile(wifiCallingServiceProfile);
      repositoryUtil.createOrUpdate(wifiCallingServiceProfileNetwork, tenant.getId());
      repositoryUtil.createOrUpdate(networkInDB, tenant.getId());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(), requestId, CfgAction.CLONE_WIFI_NETWORK_TEMPLATE, userName,
          new RequestParams().addPathVariable("wifiNetworkTemplateId", networkInDB.getId()), request);

      validateResult(request, networkInDB.getId(), OpenNetwork.class);

      final var newNetwork = repositoryUtil.find(OpenNetwork.class, request.getId());
      assertThat(newNetwork)
          .isNotNull()
          .matches(n -> n.getWifiCallingServiceProfileNetworks().size() == 1)
          .matches(n -> Objects.equals(n.getWifiCallingServiceProfileNetworks().get(0).getNetwork().getId(), request.getId()))
          .matches(n -> Objects.equals(n.getWifiCallingServiceProfileNetworks().get(0).getWifiCallingServiceProfile().getId(), wifiCallingServiceProfile.getId()))
          .extracting(n -> n.getWlan().getAdvancedCustomization())
          .matches(d -> Objects.equals(d.getDnsProxy().getDnsProxyRules().size(), dnsProxy.getDnsProxyRules().size()))
          .matches(d -> Objects.equals(d.getDnsProxy().getDnsProxyRules().get(0).getDomainName(), dnsProxy.getDnsProxyRules().get(0).getDomainName()))
          .matches(d -> Objects.equals(d.getDnsProxy().getDnsProxyRules().get(0).getIpList(), dnsProxy.getDnsProxyRules().get(0).getIpList()));
    }

    @Test
    void thenCloneAAANetwork(Tenant tenant, @Template AAANetwork networkInDB) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var request = getCloneRequest(randomId());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(), requestId, CfgAction.CLONE_WIFI_NETWORK_TEMPLATE, userName,
          new RequestParams().addPathVariable("wifiNetworkTemplateId", networkInDB.getId()), request);

      validateResult(request, networkInDB.getId(), AAANetwork.class);
    }

    @Test
    void thenCloneDpskNetwork(Tenant tenant, @Template Radius radiusInDB) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var request = getCloneRequest(randomId());

      var authRadius = new AuthRadiusService();
      authRadius.setRadius(radiusInDB);
      var networkInDB = DPSKNetworkTestFixture.randomDpskNetwork(tenant, authRadius,
          network -> {
            network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA3);
            network.setUseDpskService(false);
            network.setIsTemplate(true);
          });
      repositoryUtil.createOrUpdate(networkInDB, tenant.getId());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(), requestId, CfgAction.CLONE_WIFI_NETWORK_TEMPLATE, userName,
          new RequestParams().addPathVariable("wifiNetworkTemplateId", networkInDB.getId()), request);

      validateResult(request, networkInDB.getId(), DpskNetwork.class);

      final var newNetwork = repositoryUtil.find(DpskNetwork.class, request.getId());
      assertThat(newNetwork)
          .isNotNull()
          .matches(n -> Objects.equals(n.getUseDpskService(), networkInDB.getUseDpskService()))
          .matches(n -> Objects.equals(n.getAuthRadius().getId(), radiusInDB.getId()));
    }

    @Test
    void thenCloneGuestNetwork(Tenant tenant, @Template GuestNetwork networkInDB) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var request = getCloneRequest(randomId());

      var authRadius = RadiusTestFixture.authRadius();
      authRadius.setIsTemplate(true);
      repositoryUtil.createOrUpdate(authRadius, tenant.getId());
      var acctRadius = RadiusTestFixture.acctRadius();
      acctRadius.setIsTemplate(true);
      repositoryUtil.createOrUpdate(acctRadius, tenant.getId());

      networkInDB.setPortalServiceProfileId(randomId());
      networkInDB.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
      networkInDB.getGuestPortal().setWisprPage(wisprPage());
      networkInDB.getGuestPortal().getWisprPage().setCustomExternalProvider(true);
      networkInDB.getGuestPortal().getWisprPage().setAuthRadius(authRadius);
      networkInDB.getGuestPortal().getWisprPage().setAccountingRadius(acctRadius);
      repositoryUtil.createOrUpdate(networkInDB, tenant.getId());
      repositoryUtil.createOrUpdate(networkInDB.getGuestPortal(), tenant.getId());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(), requestId, CfgAction.CLONE_WIFI_NETWORK_TEMPLATE, userName,
          new RequestParams().addPathVariable("wifiNetworkTemplateId", networkInDB.getId()), request);

      validateResult(request, networkInDB.getId(), GuestNetwork.class);

      final var newNetwork = repositoryUtil.find(GuestNetwork.class, request.getId());
      assertThat(newNetwork)
          .isNotNull()
          .matches(n -> Objects.equals(n.getPortalServiceProfileId(), networkInDB.getPortalServiceProfileId()))
          .matches(n -> Objects.equals(n.getGuestPortal().getExternalPortalUrl(), networkInDB.getGuestPortal().getExternalPortalUrl()))
          .matches(n -> Objects.equals(n.getGuestPortal().getGuestNetworkType(), networkInDB.getGuestPortal().getGuestNetworkType()))
          .matches(n -> Objects.equals(n.getGuestPortal().getWisprPage().getAuthRadius().getId(), networkInDB.getGuestPortal().getWisprPage().getAuthRadius().getId()))
          .matches(n -> Objects.equals(n.getGuestPortal().getWisprPage().getAccountingRadius().getId(), networkInDB.getGuestPortal().getWisprPage().getAccountingRadius().getId()))
          .matches(n -> Objects.equals(n.getGuestPortal().getWisprPage().getCaptivePortalUrl(), networkInDB.getGuestPortal().getWisprPage().getCaptivePortalUrl()))
          .matches(n -> Objects.equals(n.getGuestPortal().getWalledGardens().get(0), networkInDB.getGuestPortal().getWalledGardens().get(0)));
    }
  }

  CloneSettings getCloneRequest(String id) {
    var cloneSettings = new CloneSettings();
    cloneSettings.setId(id);
    cloneSettings.setName(randomName());
    return cloneSettings;
  }

  DnsProxy createDnsProxy(Tenant tenant) {
    DnsProxy dnsProxy = new DnsProxy();
    dnsProxy.setId(randomId());
    dnsProxy.setTenant(tenant);

    DnsProxyRule dnsProxyRule1 = new DnsProxyRule();
    dnsProxyRule1.setId(randomId());
    dnsProxyRule1.setDomainName("rks.com");
    dnsProxyRule1.setIpList(List.of("**********"));
    dnsProxyRule1.setTenant(tenant);
    dnsProxyRule1.setDnsProxy(dnsProxy);

    repositoryUtil.createOrUpdate(dnsProxy, tenant.getId());
    repositoryUtil.createOrUpdate(dnsProxyRule1, tenant.getId());
    return repositoryUtil.find(DnsProxy.class, dnsProxy.getId());
  }

  <T extends Network> void validateResult(CloneSettings request, String cloneSourceId, Class<T> classType) {
    final var originalNetwork = repositoryUtil.find(classType, cloneSourceId);
    final var newNetwork = repositoryUtil.find(classType, request.getId());

    assertThat(newNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(request.getId()))
        .matches(n -> n.getName().equals(request.getName()))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.Network::getWlan)
        .isNotNull()
        .matches(wlan -> wlan.getSsid().equals(request.getName()));

    assertThat(originalNetwork)
        .isNotNull()
        .matches(n -> !n.getId().equals(newNetwork.getId()))
        .matches(n -> !n.getName().equals(newNetwork.getName()))
        .matches(n -> n.getType().equals(newNetwork.getType()))
        .matches(n -> Objects.equals(n.getIsTemplate(), true))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.Network::getWlan)
        .isNotNull()
        .matches(wlan -> !wlan.getSsid().equals(newNetwork.getWlan().getSsid()))
        .matches(wlan -> wlan.getVlanId().equals(newNetwork.getWlan().getVlanId()))
        .extracting(Wlan::getAdvancedCustomization)
        .isNotNull()
        .matches(wlanAdvancedCustomization -> wlanAdvancedCustomization.getDnsProxyEnabled()
            .equals(newNetwork.getWlan().getAdvancedCustomization().getDnsProxyEnabled()));
  }

  @Nested
  class whenConsumeCloneOWENetworkRequest {
    final String OWE_POST_FIX = "-owe-tr";

    @Test
    void thenCloneOpenOWENetwork(Tenant tenant) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var request = getCloneRequest(randomId());

      // OWE
      var masterSsid = "oweMasterNameSsid";
      var master = Generators.network(OpenNetwork.class).generate();
      var slave = Generators.network(OpenNetwork.class).generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.setIsTemplate(true);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      master.setIsOweMaster(true);
      master.setOwePairNetworkId(slave.getId());
      final var finalMaster = repositoryUtil.createOrUpdate(master, tenant.getId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + OWE_POST_FIX);
      slave.setIsTemplate(true);
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + OWE_POST_FIX);
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      slave.setOwePairNetworkId(finalMaster.getId());
      slave.setIsOweMaster(false);

      messageUtil.sendWifiCfgRequest(
          tenant.getId(), requestId, CfgAction.CLONE_WIFI_NETWORK_TEMPLATE, userName,
          new RequestParams().addPathVariable("wifiNetworkTemplateId", finalMaster.getId()), request);

      validateResult(request, finalMaster.getId(), OpenNetwork.class);

      final var newMasterNetwork = repositoryUtil.find(OpenNetwork.class, request.getId());
      assertThat(newMasterNetwork)
          .isNotNull()
          .matches(network -> Objects.equals(network.getId(), request.getId()))
          .matches(network -> Objects.equals(network.getName(), request.getName()))
          .matches(network -> Objects.equals(network.getIsOweMaster(), true))
          .matches(network -> !Objects.equals(network.getOwePairNetworkId(), slave.getId()))
          .matches(network -> Objects.equals(network.getWlan().getWlanSecurity(), WlanSecurityEnum.OWETransition))
          .matches(network -> Objects.equals(network.getWlan().getSsid(), request.getName()));

      final var newSlaveId = newMasterNetwork.getOwePairNetworkId();
      final var newSlaveNetwork = repositoryUtil.find(OpenNetwork.class, newSlaveId);
      assertThat(newSlaveNetwork)
          .isNotNull()
          .matches(network -> Objects.equals(network.getOwePairNetworkId(), request.getId()))
          .matches(network -> Objects.equals(network.getIsOweMaster(), false))
          .matches(network -> Objects.equals(network.getName(), request.getName() + OWE_POST_FIX))
          .matches(network -> Objects.equals(network.getWlan().getWlanSecurity(), WlanSecurityEnum.OWETransition))
          .matches(network -> Objects.equals(network.getWlan().getSsid(), request.getName() + OWE_POST_FIX));
    }

    @Test
    @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
    void thenCloneGuestOWENetwork(Tenant tenant) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var request = getCloneRequest(randomId());

      // OWE
      var masterSsid = "masterSsid";
      var master = Generators.network(GuestNetwork.class).generate();
      var slave = Generators.network(GuestNetwork.class).generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.setIsTemplate(true);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      master.setIsOweMaster(true);
      master.setOwePairNetworkId(slave.getId());
      var guestPortal = Generators.guestPortal(GuestNetworkTypeEnum.WISPr)
          .setWisprPage(Generators
              .wisprPage().setCustomExternalProvider(always(false))
              .setCaptivePortalUrl(always("https://guestPortal.com"))
              .setExternalProviderRegion(always("RegionA"))
              .setExternalProviderName(always("TestA")))
          .generate();
      guestPortal.setNetwork(master);
      master.setGuestPortal(guestPortal);
      final var finalMaster = repositoryUtil.createOrUpdate(master, tenant.getId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + OWE_POST_FIX);
      slave.setIsTemplate(true);
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + OWE_POST_FIX);
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      slave.setOwePairNetworkId(finalMaster.getId());
      slave.setIsOweMaster(false);
      guestPortal.setNetwork(slave);
      slave.setGuestPortal(guestPortal);

      messageUtil.sendWifiCfgRequest(
          tenant.getId(), requestId, CfgAction.CLONE_WIFI_NETWORK_TEMPLATE, userName,
          new RequestParams().addPathVariable("wifiNetworkTemplateId", finalMaster.getId()), request);

      validateResult(request, finalMaster.getId(), GuestNetwork.class);

      final var newMasterNetwork = repositoryUtil.find(GuestNetwork.class, request.getId());
      assertThat(newMasterNetwork)
          .isNotNull()
          .matches(network -> Objects.equals(network.getIsOweMaster(), true))
          .matches(network -> !Objects.equals(network.getOwePairNetworkId(), slave.getId()))
          .matches(network -> Objects.equals(network.getWlan().getWlanSecurity(), WlanSecurityEnum.OWETransition))
          .matches(network -> Objects.equals(network.getWlan().getSsid(), request.getName()));

      final var newSlaveId = newMasterNetwork.getOwePairNetworkId();
      final var newSlaveNetwork = repositoryUtil.find(GuestNetwork.class, newSlaveId);
      assertThat(newSlaveNetwork)
          .isNotNull()
          .matches(network -> Objects.equals(network.getOwePairNetworkId(), request.getId()))
          .matches(network -> Objects.equals(network.getIsOweMaster(), false))
          .matches(network -> Objects.equals(network.getName(), request.getName() + OWE_POST_FIX))
          .matches(network -> Objects.equals(network.getWlan().getWlanSecurity(), WlanSecurityEnum.OWETransition))
          .matches(network -> Objects.equals(network.getWlan().getSsid(), request.getName() + OWE_POST_FIX));
    }
  }

  @Nested
  class whenConsumeCloneDsaeNetworkRequest {
    @Test
    @FeatureFlag(enable = WIFI_EDA_WPA3_DSAE_TOGGLE)
    void testCloneDsaeNetwork(Tenant tenant) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var request = getCloneRequest(randomId());

      var dsaeNetwork = Generators.network(DpskNetwork.class).generate();
      dsaeNetwork.setDpskServiceProfileId(randomId());
      dsaeNetwork.setTenant(tenant);
      dsaeNetwork.setIsTemplate(true);
      dsaeNetwork.getWlan().setNetwork(dsaeNetwork);
      dsaeNetwork.getWlan().setSsid(randomName());
      dsaeNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
      final var finalDsaeNetwork = repositoryUtil.createOrUpdate(dsaeNetwork, tenant.getId());

      Map<String, String> headerMap = new HashMap<>();
      headerMap.put(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3");
      messageUtil.sendWifiCfgRequest(
          tenant.getId(), requestId, CfgAction.CLONE_WIFI_NETWORK_TEMPLATE, userName,
          new RequestParams().addPathVariable("wifiNetworkTemplateId", finalDsaeNetwork.getId()), headerMap, request);

      validateResult(request, finalDsaeNetwork.getId(), DpskNetwork.class);

      final var newDsaeNetwork = repositoryUtil.find(DpskNetwork.class, request.getId());
      assertThat(newDsaeNetwork)
          .isNotNull()
          .matches(network -> Objects.equals(network.getDsaeNetworkPairId(), request.getId()))
          .matches(network -> Objects.equals(network.getIsDsaeServiceNetwork(), true))
          .matches(network -> Objects.equals(network.getWlan().getWlanSecurity(), WlanSecurityEnum.WPA23Mixed));
    }
  }
}
