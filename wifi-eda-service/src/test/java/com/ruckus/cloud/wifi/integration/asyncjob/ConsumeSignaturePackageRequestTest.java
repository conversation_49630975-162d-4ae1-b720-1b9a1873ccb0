package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertPrefixHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackage;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SignaturePackagePatchRequestEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.SignaturePackagePatchRequestGenerator;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.SignaturePackageJob.JobType;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SignaturePackageTest")
@WifiIntegrationTest
public class ConsumeSignaturePackageRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.TRIGGER_SIGNATURE_PACKAGE_ACTION)
  class ConsumeSignaturePackagePatchRequestTestWithSetDefault {

    @Payload
    private final SignaturePackagePatchRequestGenerator requestGenerator = Generators.signaturePackagePatchRequest(
        SignaturePackagePatchRequestEnum.SET_DEFAULT, "SignaturePackage-SetDefault-v2");

    @BeforeEach
    void givenRequiredDataPersistedInDb(final Tenant tenant) {
      var sp1 = new SignaturePackage("SignaturePackage-SetDefault-v1");
      sp1.setVersion(sp1.getId());
      sp1.setReleasable(true);
      sp1.setDefaultVersion(true);
      sp1.setSignaturePackageMappings(new ArrayList<>());
      sp1 = repositoryUtil.createOrUpdate(sp1, tenant.getId(), randomTxId());

      var sp2 = new SignaturePackage("SignaturePackage-SetDefault-v2");
      sp2.setVersion(sp2.getId());
      sp2.setReleasable(true);
      sp2.setDefaultVersion(false);
      sp2.setSignaturePackageMappings(new ArrayList<>());
      sp2 = repositoryUtil.createOrUpdate(sp2, tenant.getId(), randomTxId());
    }


    @Test
    void thenShouldHandleTheRequestSuccessfully(final Tenant tenant) {
      // For the SET_DEFAULT case, there should no DDCCM
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());
      assertThat(repositoryUtil.find(SignaturePackage.class, "SignaturePackage-SetDefault-v1"))
          .matches(sp -> !sp.getDefaultVersion());
      assertThat(repositoryUtil.find(SignaturePackage.class, "SignaturePackage-SetDefault-v2"))
          .matches(sp -> sp.getDefaultVersion());
    }
  }

  @Nested
  @ApiAction(CfgAction.TRIGGER_SIGNATURE_PACKAGE_ACTION)
  class ConsumeSignaturePackagePatchRequestTestWithRelease {

    @Payload
    private final SignaturePackagePatchRequestGenerator requestGenerator = Generators.signaturePackagePatchRequest(
        SignaturePackagePatchRequestEnum.RELEASE, "SignaturePackage-Release-v2",
        List.of("SignaturePackage-Release-t1"));

    @BeforeEach
    void givenRequiredDataPersistedInDb(final Tenant tenant) {
      var sp1 = new SignaturePackage("SignaturePackage-Release-v1");
      var sp2 = new SignaturePackage("SignaturePackage-Release-v2");
      sp1.setVersion(sp1.getId());
      sp1.setReleasable(true);
      sp1.setDefaultVersion(true);
      sp1.setNextVersion(sp2.getId());
      sp1.setSignaturePackageMappings(new ArrayList<>());
      sp1 = repositoryUtil.createOrUpdate(sp1, tenant.getId(), randomTxId());

      sp2.setVersion(sp2.getId());
      sp2.setReleasable(true);
      sp2.setDefaultVersion(false);
      sp2.setSignaturePackageMappings(new ArrayList<>());
      sp2 = repositoryUtil.createOrUpdate(sp2, tenant.getId(), randomTxId());

      var t1 = new Tenant("SignaturePackage-Release-t1");
      t1.setSignaturePackage(sp1);
      t1.setSignaturePackageUpdatedDate(new Date());
      t1.setLatestSignaturePackage(sp1);
      t1.setLatestSignaturePackageReleasedDate(new Date());
      repositoryUtil.createOrUpdate(t1, t1.getId(), randomTxId());

      var t2 = new Tenant("SignaturePackage-Release-t2");
      t2.setSignaturePackage(sp1);
      t2.setSignaturePackageUpdatedDate(new Date());
      t2.setLatestSignaturePackage(sp1);
      t2.setLatestSignaturePackageReleasedDate(new Date());
      repositoryUtil.createOrUpdate(t2, t2.getId(), randomTxId());
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(final Tenant tenant) {
      // For the RELEASE case, there should no DDCCM/CmnCfg/WifiCfgChange
      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor(),
          messageCaptors.getWifiCfgChangeMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());
      assertThat(repositoryUtil.find(Tenant.class, "SignaturePackage-Release-t1"))
          .matches(
              t -> t.getLatestSignaturePackage().getId().equals("SignaturePackage-Release-v2"));
      assertThat(repositoryUtil.find(Tenant.class, "SignaturePackage-Release-t2"))
          .matches(
              t -> t.getLatestSignaturePackage().getId().equals("SignaturePackage-Release-v1"));
    }
  }

  @Nested
  @ApiAction(CfgAction.TRIGGER_SIGNATURE_PACKAGE_ACTION)
  class ConsumeSignaturePackagePatchRequestTestWithReleaseAll {

    @Payload
    private final SignaturePackagePatchRequestGenerator requestGenerator = Generators.signaturePackagePatchRequest(
        SignaturePackagePatchRequestEnum.RELEASE_ALL, "SignaturePackage-ReleaseAll-v2");

    @BeforeEach
    void givenRequiredDataPersistedInDb(final Tenant tenant) {
      var sp1 = new SignaturePackage("SignaturePackage-ReleaseAll-v1");
      var sp2 = new SignaturePackage("SignaturePackage-ReleaseAll-v2");
      sp1.setVersion(sp1.getId());
      sp1.setReleasable(true);
      sp1.setDefaultVersion(true);
      sp1.setNextVersion(sp2.getId());
      sp1.setSignaturePackageMappings(new ArrayList<>());
      sp1 = repositoryUtil.createOrUpdate(sp1, tenant.getId(), randomTxId());

      sp2.setVersion(sp2.getId());
      sp2.setReleasable(true);
      sp2.setDefaultVersion(false);
      sp2.setSignaturePackageMappings(new ArrayList<>());
      sp2 = repositoryUtil.createOrUpdate(sp2, tenant.getId(), randomTxId());

      var t1 = new Tenant("SignaturePackage-ReleaseAll-t1");
      t1.setSignaturePackage(sp1);
      t1.setSignaturePackageUpdatedDate(new Date());
      t1.setLatestSignaturePackage(sp1);
      t1.setLatestSignaturePackageReleasedDate(new Date());
      repositoryUtil.createOrUpdate(t1, t1.getId(), randomTxId());

      var t2 = new Tenant("SignaturePackage-ReleaseAll-t2");
      t2.setSignaturePackage(sp1);
      t2.setSignaturePackageUpdatedDate(new Date());
      t2.setLatestSignaturePackage(sp1);
      t2.setLatestSignaturePackageReleasedDate(new Date());
      repositoryUtil.createOrUpdate(t2, t2.getId(), randomTxId());
    }

    @Test
    @Disabled("Due to the DB contains tenants created in other cases, this test might fail")
    void thenShouldHandleTheRequestSuccessfully(final Tenant tenant) {
      // For the RELEASE case, there should no DDCCM/CmnCfg/WifiCfgChange
      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor(),
          messageCaptors.getWifiCfgChangeMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());
      assertThat(repositoryUtil.find(Tenant.class, "SignaturePackage-ReleaseAll-t1"))
          .matches(
              t -> t.getLatestSignaturePackage().getId().equals("SignaturePackage-ReleaseAll-v2"));
      assertThat(repositoryUtil.find(Tenant.class, "SignaturePackage-ReleaseAll-t2"))
          .matches(
              t -> t.getLatestSignaturePackage().getId().equals("SignaturePackage-ReleaseAll-v2"));
    }
  }

  @Nested
  @ApiAction(CfgAction.TRIGGER_SIGNATURE_PACKAGE_ACTION)
  class ConsumeSignaturePackagePatchRequestTestWithRevert {

    @Payload
    private final SignaturePackagePatchRequestGenerator requestGenerator = Generators.signaturePackagePatchRequest(
        SignaturePackagePatchRequestEnum.REVERT, "SignaturePackage-Revert-v2",
        List.of("SignaturePackage-Revert-t1"));

    @BeforeEach
    void givenRequiredDataPersistedInDb(final Tenant tenant) {
      var sp1 = new SignaturePackage("SignaturePackage-Revert-v1");
      var sp2 = new SignaturePackage("SignaturePackage-Revert-v2");
      sp1.setVersion(sp1.getId());
      sp1.setReleasable(true);
      sp1.setDefaultVersion(true);
      sp1.setNextVersion(sp2.getId());
      sp1.setSignaturePackageMappings(new ArrayList<>());
      sp1 = repositoryUtil.createOrUpdate(sp1, tenant.getId(), randomTxId());

      sp2.setVersion(sp2.getId());
      sp2.setReleasable(true);
      sp2.setDefaultVersion(false);
      sp2.setSignaturePackageMappings(new ArrayList<>());
      sp2 = repositoryUtil.createOrUpdate(sp2, tenant.getId(), randomTxId());

      var t1 = new Tenant("SignaturePackage-Revert-t1");
      t1.setSignaturePackage(sp2);
      t1.setSignaturePackageUpdatedDate(new Date());
      t1.setLatestSignaturePackage(sp2);
      t1.setLatestSignaturePackageReleasedDate(new Date());
      t1.setPreviousSignaturePackage(sp1);
      t1.setPreviousSignaturePackageUpdatedDate(new Date());
      repositoryUtil.createOrUpdate(t1, t1.getId(), randomTxId());

      var t2 = new Tenant("SignaturePackage-Revert-t2");
      t2.setSignaturePackage(sp2);
      t2.setSignaturePackageUpdatedDate(new Date());
      t2.setLatestSignaturePackage(sp2);
      t2.setLatestSignaturePackageReleasedDate(new Date());
      t2.setPreviousSignaturePackage(sp1);
      t2.setPreviousSignaturePackageUpdatedDate(new Date());
      repositoryUtil.createOrUpdate(t2, t2.getId(), randomTxId());
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      final var requestId = txCtxExtension.getRequestId();
      final var tenantId = "SignaturePackage-Revert-t1";
      // For the Revert case, there should contain t1's tenant update
      // One is tenant, the other one is CcmQmSignaturePackageAcx
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage).isNotNull()
          .satisfies(assertPrefixHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();
      assertThatNoException().isThrownBy(() -> {
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> {
              assertThat(ops).hasSize(2);
              assertThat(ops).allSatisfy(op -> assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                  .matches(commonInfo -> commonInfo.getRequestId().startsWith(requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
              assertThat(ops).filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasTenant)
                  .hasSize(1);
              assertThat(ops).filteredOn(
                      com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasCcmQmSignaturePackageAcx)
                  .hasSize(1);
            });
      });
      // For the Revert case, there should contain aync job
      final var wifiAsyncJobMessage = messageCaptors.getWifiAsyncJobMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(wifiAsyncJobMessage).extracting(KafkaProtoMessage::getPayload).isNotNull();
      assertThatNoException().isThrownBy(() -> {
        assertThat(wifiAsyncJobMessage.getPayload())
            .satisfies(job -> assertThat(job).matches(WifiAsyncJob::hasSignaturePackageJob)
                .extracting(WifiAsyncJob::getSignaturePackageJob)
                .matches(revertJob -> revertJob.getJobType() == JobType.REVERT)
                .matches(revertJob -> revertJob.getSignaturePackageVersion()
                    .equals("SignaturePackage-Revert-v2"))
            );
      });

      assertThat(repositoryUtil.find(Tenant.class, "SignaturePackage-Revert-t1"))
          .matches(t -> t.getSignaturePackage().getId().equals("SignaturePackage-Revert-v1"))
          .matches(t -> t.getLatestSignaturePackage().getId().equals("SignaturePackage-Revert-v1"))
          .matches(t -> t.getPreviousSignaturePackage() == null);
      assertThat(repositoryUtil.find(Tenant.class, "SignaturePackage-Revert-t2"))
          .matches(t -> t.getSignaturePackage().getId().equals("SignaturePackage-Revert-v2"))
          .matches(t -> t.getLatestSignaturePackage().getId().equals("SignaturePackage-Revert-v2"))
          .matches(
              t -> t.getPreviousSignaturePackage().getId().equals("SignaturePackage-Revert-v1"));
    }
  }

  @Nested
  @ApiAction(CfgAction.TRIGGER_SIGNATURE_PACKAGE_ACTION)
  class ConsumeSignaturePackagePatchRequestTestWithRevertAll {

    @Payload
    private final SignaturePackagePatchRequestGenerator requestGenerator = Generators.signaturePackagePatchRequest(
        SignaturePackagePatchRequestEnum.REVERT_ALL, "SignaturePackage-RevertAll-v2");

    @BeforeEach
    void givenRequiredDataPersistedInDb(final Tenant tenant) {
      var sp1 = new SignaturePackage("SignaturePackage-RevertAll-v1");
      var sp2 = new SignaturePackage("SignaturePackage-RevertAll-v2");
      sp1.setVersion(sp1.getId());
      sp1.setReleasable(true);
      sp1.setDefaultVersion(true);
      sp1.setNextVersion(sp2.getId());
      sp1.setSignaturePackageMappings(new ArrayList<>());
      sp1 = repositoryUtil.createOrUpdate(sp1, tenant.getId(), randomTxId());

      sp2.setVersion(sp2.getId());
      sp2.setReleasable(true);
      sp2.setDefaultVersion(false);
      sp2.setSignaturePackageMappings(new ArrayList<>());
      sp2 = repositoryUtil.createOrUpdate(sp2, tenant.getId(), randomTxId());

      var t1 = new Tenant("SignaturePackage-RevertAll-t1");
      t1.setSignaturePackage(sp2);
      t1.setSignaturePackageUpdatedDate(new Date());
      t1.setLatestSignaturePackage(sp2);
      t1.setLatestSignaturePackageReleasedDate(new Date());
      t1.setPreviousSignaturePackage(sp1);
      t1.setPreviousSignaturePackageUpdatedDate(new Date());
      repositoryUtil.createOrUpdate(t1, t1.getId(), randomTxId());

      var t2 = new Tenant("SignaturePackage-RevertAll-t2");
      t2.setSignaturePackage(sp2);
      t2.setSignaturePackageUpdatedDate(new Date());
      t2.setLatestSignaturePackage(sp2);
      t2.setLatestSignaturePackageReleasedDate(new Date());
      repositoryUtil.createOrUpdate(t2, t2.getId(), randomTxId());
    }

    @Test
    @Disabled("Due to the DB contains tenants created in other cases, this test might fail")
    void thenShouldHandleTheRequestSuccessfully() {
      final var requestId = txCtxExtension.getRequestId();
      final var tenantId = "SignaturePackage-RevertAll-t1";
      // For the Revert case, there should contain t1's tenant update
      // One is tenant, the other one is CcmQmSignaturePackageAcx
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();
      assertThatNoException().isThrownBy(() -> {
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> {
              assertThat(ops).hasSize(2);
              assertThat(ops).allSatisfy(op -> assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
              assertThat(ops).filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasTenant)
                  .hasSize(1);
              assertThat(ops).filteredOn(
                      com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasCcmQmSignaturePackageAcx)
                  .hasSize(1);
            });
      });
      // For the Revert case, there should contain aync job
      final var wifiAsyncJobMessage = messageCaptors.getWifiAsyncJobMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(wifiAsyncJobMessage).extracting(KafkaProtoMessage::getPayload).isNotNull();
      assertThatNoException().isThrownBy(() -> {
        assertThat(wifiAsyncJobMessage.getPayload())
            .satisfies(job -> assertThat(job).matches(WifiAsyncJob::hasSignaturePackageJob)
                .extracting(WifiAsyncJob::getSignaturePackageJob)
                .matches(revertJob -> revertJob.getJobType() == JobType.REVERT)
                .matches(revertJob -> revertJob.getSignaturePackageVersion()
                    .equals("SignaturePackage-RevertAll-v2"))
            );
      });

      assertThat(repositoryUtil.find(Tenant.class, "SignaturePackage-RevertAll-t1"))
          .matches(t -> t.getSignaturePackage().getId().equals("SignaturePackage-RevertAll-v1"))
          .matches(
              t -> t.getLatestSignaturePackage().getId().equals("SignaturePackage-RevertAll-v1"))
          .matches(t -> t.getPreviousSignaturePackage() == null);
      assertThat(repositoryUtil.find(Tenant.class, "SignaturePackage-RevertAll-t2"))
          .matches(t -> t.getSignaturePackage().getId().equals("SignaturePackage-RevertAll-v2"))
          .matches(
              t -> t.getLatestSignaturePackage().getId().equals("SignaturePackage-RevertAll-v2"));
    }
  }
}
