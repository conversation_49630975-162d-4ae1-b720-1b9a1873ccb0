package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest(showSql = false)
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
    INSERT INTO ap_version (id, name, category) VALUES ('6.2.4.103.123', '6.2.4.103.123', 'RECOMMENDED'),
      ('7.0.0.103.123', '7.0.0.103.123', 'RECOMMENDED');
    INSERT INTO venue (id, tenant) VALUES ('e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c'),
      ('6krckdoekfo2l3l4fo4k3k3o3ok33o3e', '3057a924ea8148bab5cc1682ff09275c');
    INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant, is_ignore_data_migration) VALUES
      ('a09c1264ae704ba1969152aa44cc083a', 'R550', '6.2.4.103.123', 
        'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c', false), 
      ('r904kdf4ki5kfieldwsk3ij445j4imdm', 'R770', '6.2.4.103.123', 
        'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c', false),
      ('2dmeiroejdjicm5j23j2mm2i3jemdi2j', 'R550', '7.0.0.103.123', 
        '6krckdoekfo2l3l4fo4k3k3o3ok33o3e','3057a924ea8148bab5cc1682ff09275c', false),
      ('3k4kdek3k4jkj1dlkfkrodk2kk3kk22k', 'R770', '7.0.0.103.123', 
        '6krckdoekfo2l3l4fo4k3k3o3ok33o3e','3057a924ea8148bab5cc1682ff09275c', false);
    """)
public class VenueCurrentFirmwareRepositoryTest {

  @Autowired
  private VenueCurrentFirmwareRepository vcfRepository;
  private static final String TENANT_ID = "3057a924ea8148bab5cc1682ff09275c";

  @Test
  void findByTenantIdAndVenueIdIn() {
    assertThat(vcfRepository.findByTenantIdAndVenueIdIn(TENANT_ID,
        List.of("e619cccb6bd74075ba0b220611969aab", "6krckdoekfo2l3l4fo4k3k3o3ok33o3e")))
        .isNotEmpty()
        .extracting(AbstractBaseEntity::getId)
        .containsExactlyInAnyOrder(
            "a09c1264ae704ba1969152aa44cc083a",
            "r904kdf4ki5kfieldwsk3ij445j4imdm",
            "2dmeiroejdjicm5j23j2mm2i3jemdi2j",
            "3k4kdek3k4jkj1dlkfkrodk2kk3kk22k");
    assertThat(vcfRepository.findByTenantIdAndVenueIdIn(TENANT_ID,
        List.of("e619cccb6bd74075ba0b220611969aab")))
        .isNotEmpty()
        .extracting(AbstractBaseEntity::getId)
        .containsExactlyInAnyOrder(
            "a09c1264ae704ba1969152aa44cc083a",
            "r904kdf4ki5kfieldwsk3ij445j4imdm");
  }

  @Test
  void findByVenueIdAndApModel() {
    assertThat(vcfRepository.findByVenueIdAndApModel("e619cccb6bd74075ba0b220611969aab", "R550"))
        .isPresent()
        .get()
        .extracting(VenueCurrentFirmware::getFirmware)
        .extracting(ApVersion::getId)
        .isEqualTo("6.2.4.103.123");
    assertThat(vcfRepository.findByVenueIdAndApModel("6krckdoekfo2l3l4fo4k3k3o3ok33o3e", "R550"))
        .isPresent()
        .get()
        .extracting(VenueCurrentFirmware::getFirmware)
        .extracting(ApVersion::getId)
        .isEqualTo("7.0.0.103.123");
  }

  @Test
  void findByTenantIdAndVenueIdAndApModel() {
    assertThat(vcfRepository.findByTenantIdAndVenueIdAndApModel(TENANT_ID, "e619cccb6bd74075ba0b220611969aab", "R550"))
        .isPresent()
        .get()
        .extracting(VenueCurrentFirmware::getFirmware)
        .extracting(ApVersion::getId)
        .isEqualTo("6.2.4.103.123");
    assertThat(vcfRepository.findByTenantIdAndVenueIdAndApModel(TENANT_ID, "6krckdoekfo2l3l4fo4k3k3o3ok33o3e", "R550"))
        .isPresent()
        .get()
        .extracting(VenueCurrentFirmware::getFirmware)
        .extracting(ApVersion::getId)
        .isEqualTo("7.0.0.103.123");
  }

  @Test
  void testFindBiggestVersionByTenantIdVenudId() {
    assertThat(vcfRepository
        .findBiggestVersionByTenantIdVenueId(TENANT_ID, "6krckdoekfo2l3l4fo4k3k3o3ok33o3e"))
        .isPresent()
        .get()
        .isEqualTo("7.0.0.103.123");
  }

  @Test
  void testFindMaximumAndMinimumTargetVersion() {
    assertThat(vcfRepository
        .findMaximumAndMinimumTargetVersion(TENANT_ID, "6krckdoekfo2l3l4fo4k3k3o3ok33o3e"))
        .isNotEmpty()
        .containsExactly("7.0.0.103.123", "7.0.0.103.123");
  }

  @Test
  void findByTenantIdAndVenueId() {
    assertThat(vcfRepository.findByTenantIdAndVenueId(TENANT_ID, "e619cccb6bd74075ba0b220611969aab"))
        .asList()
        .hasSize(2);
    assertThat(vcfRepository.findByTenantIdAndVenueId(TENANT_ID, "6krckdoekfo2l3l4fo4k3k3o3ok33o3e"))
        .asList()
        .hasSize(2);
  }

  @Test
  void deleteByTenantIdAndVenueIdAndApModelIn() {
    assertThat(vcfRepository.findByTenantIdAndVenueIdAndApModel(TENANT_ID,"e619cccb6bd74075ba0b220611969aab", "R550"))
        .isPresent();

    vcfRepository.deleteByTenantIdAndVenueIdAndApModelIn(TENANT_ID, "e619cccb6bd74075ba0b220611969aab",
        List.of("R550"));

    assertThat(vcfRepository.findByTenantIdAndVenueIdAndApModel(TENANT_ID,"e619cccb6bd74075ba0b220611969aab", "R550"))
        .isNotPresent();
  }

  @Test
  void deleteByTenantIdAndVenueId() {
    assertThat(vcfRepository.findByTenantIdAndVenueId(TENANT_ID,"e619cccb6bd74075ba0b220611969aab"))
        .isNotEmpty();

    vcfRepository.deleteByTenantIdAndVenueId(TENANT_ID, "e619cccb6bd74075ba0b220611969aab");

    assertThat(vcfRepository.findByTenantIdAndVenueId(TENANT_ID,"e619cccb6bd74075ba0b220611969aab"))
        .isEmpty();
  }

  @Sql(statements = """
    INSERT INTO tenant (id) VALUES 
      ('3057a924ea8148bab5cc1682ff09275c'), 
      ('5e5f8r2we8148bab5cc16eder5dc4dee'), 
      ('e23de32dea8148bab5cc167t34de567f'),
      ('rkrk4odf903kdkro4ofkfko3k2o2o1l3');
    INSERT INTO ap_version (id) VALUES ('6.2.0.103.21'), ('6.2.0.103.22'),
      ('6.2.4.103.122'), ('6.2.4.103.123'),
      ('7.0.0.103.333');
    INSERT INTO venue (id, tenant, ap_version) VALUES 
      ('e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c', '6.2.0.103.22'),
      ('6krckdoekfo2l3l4fo4k3k3o3ok33o3e', '5e5f8r2we8148bab5cc16eder5dc4dee', '6.2.4.103.123'),
      ('e3dr5674dfo2l3l4fo4k3k3o3errg562', 'e23de32dea8148bab5cc167t34de567f', '7.0.0.103.333'),
      ('gg4453rrreo2l3l4fo4k3kr3lfp5678r', 'e23de32dea8148bab5cc167t34de567f', '6.2.0.103.22'),
      ('3ofko4kfolfo4lgio54io3l2loeodkfo', 'rkrk4odf903kdkro4ofkfko3k2o2o1l3', '6.2.0.103.22');
    INSERT INTO ap_group (id, venue, tenant) VALUES 
      ('818552afabf544878057e510b9bb88b5', 'e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c'),
      ('95jfkeieirkf904854klf04lr03i5klf', '6krckdoekfo2l3l4fo4k3k3o3ok33o3e', '5e5f8r2we8148bab5cc16eder5dc4dee'),
      ('kr98ik9ekekr90fk0jgxler9o3kdleoe', 'e3dr5674dfo2l3l4fo4k3k3o3errg562', 'e23de32dea8148bab5cc167t34de567f'),
      ('eko094ufmfjtu8fjenmdhtyuifjeiwiw', 'gg4453rrreo2l3l4fo4k3kr3lfp5678r', 'e23de32dea8148bab5cc167t34de567f'),
      ('gooek23o4550folk3l4o5lweldo3l2l3', '3ofko4kfolfo4lgio54io3l2loeodkfo', 'rkrk4odf903kdkro4ofkfko3k2o2o1l3');
    INSERT INTO ap (id, name, ap_group, tenant, model, mac) VALUES 
      ('900000111111', 'AP1', '818552afabf544878057e510b9bb88b5', '3057a924ea8148bab5cc1682ff09275c', 'R550', '00:00:00:00:00:00'),
      ('900000111112', 'AP2', '95jfkeieirkf904854klf04lr03i5klf', '3057a924ea8148bab5cc1682ff09275c', 'R550', '00:00:00:00:00:01'),
      ('900000111113', 'AP3', '95jfkeieirkf904854klf04lr03i5klf', '3057a924ea8148bab5cc1682ff09275c', 'R770', '00:00:00:00:00:02'),
      ('900000111114', 'AP4', 'kr98ik9ekekr90fk0jgxler9o3kdleoe', '5e5f8r2we8148bab5cc16eder5dc4dee', 'R550', '00:00:00:00:00:03'),
      ('900000111115', 'AP5', 'kr98ik9ekekr90fk0jgxler9o3kdleoe', '5e5f8r2we8148bab5cc16eder5dc4dee', 'R550', '00:00:00:00:00:04'),
      ('900000111116', 'AP6', 'gooek23o4550folk3l4o5lweldo3l2l3', 'rkrk4odf903kdkro4ofkfko3k2o2o1l3', 'R650', '00:00:00:00:00:05');
    INSERT INTO ap_firmware_upgrade_request (id, venue_id, tenant, source_version, target_version, model, serial_number) VALUES 
      ('ek3ko2ko2kok2ok3ok3ok3ok3ok3ok3o', 'e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c', '6.2.0.103.22', '6.2.0.103.22', 'R550', '900000111111'), 
      ('feoekekekekekekekekeksddsdsdsdds', '6krckdoekfo2l3l4fo4k3k3o3ok33o3e', '5e5f8r2we8148bab5cc16eder5dc4dee', '6.2.0.103.22', '6.2.0.103.22', 'R550', '900000111112'),
      ('ofkrkekkddkkeoekwoekowekoekwoeek', '6krckdoekfo2l3l4fo4k3k3o3ok33o3e', '5e5f8r2we8148bab5cc16eder5dc4dee', '7.0.0.103.333', '7.0.0.103.333', 'R770', '900000111113'),
      ('oedkekekrkcvkfkrekdkdkekdkdkekfk', 'e3dr5674dfo2l3l4fo4k3k3o3errg562', 'e23de32dea8148bab5cc167t34de567f', '6.2.4.103.123', '6.2.4.103.123', 'R550', '900000111114'),
      ('ekeidkdkjrjcvjfjfirjfirjekskfier', 'e3dr5674dfo2l3l4fo4k3k3o3errg562', 'e23de32dea8148bab5cc167t34de567f', '6.2.4.103.123', '6.2.4.103.123', 'R550', '900000111115'),
      ('tirke3o2k4orko4kok4o4oo43ko3k2o2', '3ofko4kfolfo4lgio54io3l2loeodkfo', 'rkrk4odf903kdkro4ofkfko3k2o2o1l3', '6.2.0.103.22', '6.2.0.103.22', 'R650', '900000111116');
    INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant, is_ignore_data_migration) VALUES
      ('a09c1264ae704ba1969152aa44cc083a', 'R550', '6.2.0.103.22', 
        'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c', false), 
      ('r904kdf4ki5kfieldwsk3ij445j4imdm', 'R550', '6.2.0.103.22', 
        '6krckdoekfo2l3l4fo4k3k3o3ok33o3e','5e5f8r2we8148bab5cc16eder5dc4dee', false),
      ('2dmeiroejdjicm5j23j2mm2i3jemdi2j', 'R770', '6.2.4.103.123', 
        '6krckdoekfo2l3l4fo4k3k3o3ok33o3e','5e5f8r2we8148bab5cc16eder5dc4dee', false),
      ('rro3k3ek3k4krkrkkfkfkkwektktkgke', 'R550', '6.2.4.103.122', 
        'e3dr5674dfo2l3l4fo4k3k3o3errg562','e23de32dea8148bab5cc167t34de567f', false);
    """)
  @Test
  void getTenantIdsWithNonSyncedVenueCurrentVersion() {
    /*
      tenant, venue, model
      5e5f8r2we8148bab5cc16eder5dc4dee, 6krckdoekfo2l3l4fo4k3k3o3ok33o3e, R770
           -> venue_current_firmware should be sync to 7.0.0.103.333
      e23de32dea8148bab5cc167t34de567f, e3dr5674dfo2l3l4fo4k3k3o3errg562, R550
           -> venue_current_firmware should be sync to 6.2.4.103.123
      rkrk4odf903kdkro4ofkfko3k2o2o1l3, 3ofko4kfolfo4lgio54io3l2loeodkfo, R650 -> without venue_current_firmware
     */
    assertThat(vcfRepository.getTenantIdsWithNonSyncedVenueCurrentVersion())
        .hasSize(3)
        .containsExactlyInAnyOrder(
            "5e5f8r2we8148bab5cc16eder5dc4dee",
            "e23de32dea8148bab5cc167t34de567f",
            "rkrk4odf903kdkro4ofkfko3k2o2o1l3");
  }

  @Sql(statements = """
    INSERT INTO tenant (id) VALUES 
      ('3057a924ea8148bab5cc1682ff09275c'), 
      ('5e5f8r2we8148bab5cc16eder5dc4dee'), 
      ('e23de32dea8148bab5cc167t34de567f'),
      ('rkrk4odf903kdkro4ofkfko3k2o2o1l3');
    INSERT INTO ap_version (id) VALUES ('6.2.0.103.21'), ('6.2.0.103.22'),
      ('6.2.4.103.122'), ('6.2.4.103.123'),
      ('7.0.0.103.333');
    INSERT INTO venue (id, tenant, ap_version) VALUES 
      ('e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c', '6.2.0.103.22'),
      ('6krckdoekfo2l3l4fo4k3k3o3ok33o3e', '5e5f8r2we8148bab5cc16eder5dc4dee', '6.2.4.103.123'),
      ('e3dr5674dfo2l3l4fo4k3k3o3errg562', 'e23de32dea8148bab5cc167t34de567f', '7.0.0.103.333'),
      ('gg4453rrreo2l3l4fo4k3kr3lfp5678r', 'e23de32dea8148bab5cc167t34de567f', '6.2.0.103.22'),
      ('3ofko4kfolfo4lgio54io3l2loeodkfo', 'rkrk4odf903kdkro4ofkfko3k2o2o1l3', '6.2.0.103.22');
    INSERT INTO ap_group (id, venue, tenant) VALUES 
      ('818552afabf544878057e510b9bb88b5', 'e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c'),
      ('95jfkeieirkf904854klf04lr03i5klf', '6krckdoekfo2l3l4fo4k3k3o3ok33o3e', '5e5f8r2we8148bab5cc16eder5dc4dee'),
      ('kr98ik9ekekr90fk0jgxler9o3kdleoe', 'e3dr5674dfo2l3l4fo4k3k3o3errg562', 'e23de32dea8148bab5cc167t34de567f'),
      ('eko094ufmfjtu8fjenmdhtyuifjeiwiw', 'gg4453rrreo2l3l4fo4k3kr3lfp5678r', 'e23de32dea8148bab5cc167t34de567f'),
      ('gooek23o4550folk3l4o5lweldo3l2l3', '3ofko4kfolfo4lgio54io3l2loeodkfo', 'rkrk4odf903kdkro4ofkfko3k2o2o1l3');
    INSERT INTO ap (id, ap_group, tenant, model, mac, registration_state) VALUES 
      ('900000111111', '818552afabf544878057e510b9bb88b5', '3057a924ea8148bab5cc1682ff09275c', 'R550', '00:00:00:00:00:00', 'UNKNOWN'),
      ('900000111112', '95jfkeieirkf904854klf04lr03i5klf', '3057a924ea8148bab5cc1682ff09275c', 'R550', '00:00:00:00:00:01', 'UNKNOWN'),
      ('900000111113', '95jfkeieirkf904854klf04lr03i5klf', '3057a924ea8148bab5cc1682ff09275c', 'R770', '00:00:00:00:00:02', 'UNKNOWN'),
      ('900000111114', 'kr98ik9ekekr90fk0jgxler9o3kdleoe', '5e5f8r2we8148bab5cc16eder5dc4dee', 'R550', '00:00:00:00:00:03', 'UNKNOWN'),
      ('900000111115', 'kr98ik9ekekr90fk0jgxler9o3kdleoe', '5e5f8r2we8148bab5cc16eder5dc4dee', 'R550', '00:00:00:00:00:04', 'UNKNOWN'),
      ('900000111116', 'gooek23o4550folk3l4o5lweldo3l2l3', 'rkrk4odf903kdkro4ofkfko3k2o2o1l3', 'R650', '00:00:00:00:00:05', 'UNKNOWN');
    INSERT INTO venue_firmware_version (id, venue, tenant, branch_type, current_firmware_version) VALUES
      ('e3k45ofk34ko34eko23edko43kfoer34', 'e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c', 'eol-ap-2022-12', '6.2.0.103.22'),
      ('rorok4k4o4kj5oko34ko3k3o32ko3k3o', 'e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c', 'ABF2-3R', '6.2.4.103.123'),
      ('35kokodko3eko343ko434ko34k3o43k4', '6krckdoekfo2l3l4fo4k3k3o3ok33o3e', '5e5f8r2we8148bab5cc16eder5dc4dee', 'eol-ap-2022-12', '6.2.0.103.22'),
      ('kkkdkdkeokokokokoko3kkeokeokeoee', '6krckdoekfo2l3l4fo4k3k3o3ok33o3e', '5e5f8r2we8148bab5cc16eder5dc4dee', 'ABF2-3R', '6.2.4.103.123'),
      ('dcdcd09fd0sf90df9d0f9d0f9d0f9d0f', 'e3dr5674dfo2l3l4fo4k3k3o3errg562', 'e23de32dea8148bab5cc167t34de567f', 'eol-ap-2022-12', '6.2.0.103.22'),
      ('54o5o6l3o2ol5krorkodkookwoekweee', '3ofko4kfolfo4lgio54io3l2loeodkfo', 'rkrk4odf903kdkro4ofkfko3k2o2o1l3', 'eol-ap-2022-12', '6.2.0.103.22');
    INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant, is_ignore_data_migration) VALUES
      ('a09c1264ae704ba1969152aa44cc083a', 'R550', '6.2.0.103.22', 
        'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c', false), 
      ('r904kdf4ki5kfieldwsk3ij445j4imdm', 'R550', '6.2.0.103.22', 
        '6krckdoekfo2l3l4fo4k3k3o3ok33o3e','5e5f8r2we8148bab5cc16eder5dc4dee', false),
      ('2dmeiroejdjicm5j23j2mm2i3jemdi2j', 'R770', '6.2.4.103.123', 
        '6krckdoekfo2l3l4fo4k3k3o3ok33o3e','5e5f8r2we8148bab5cc16eder5dc4dee', false),
      ('rro3k3ek3k4krkrkkfkfkkwektktkgke', 'R550', '6.2.4.103.122', 
        'e3dr5674dfo2l3l4fo4k3k3o3errg562','e23de32dea8148bab5cc167t34de567f', false);
    """)
  @Test
  void getTenantIdsWithNonSyncedVenueCurrentVersion_apWithoutApFirmwareUpgradeRequest_checkByVenueFirmwareVersion() {
    /*
      tenant, venue, model
      e23de32dea8148bab5cc167t34de567f, e3dr5674dfo2l3l4fo4k3k3o3errg562, R550
           -> venue_current_firmware should be sync to 6.2.0.103.22 or 6.2.4.103.123 or 7.0.0.103.333
      rkrk4odf903kdkro4ofkfko3k2o2o1l3, 3ofko4kfolfo4lgio54io3l2loeodkfo, R650 -> without venue_current_firmware
     */
    assertThat(vcfRepository.getTenantIdsWithNonSyncedVenueCurrentVersion())
        .hasSize(2)
        .containsExactlyInAnyOrder(
            "e23de32dea8148bab5cc167t34de567f",
            "rkrk4odf903kdkro4ofkfko3k2o2o1l3");
  }
}
