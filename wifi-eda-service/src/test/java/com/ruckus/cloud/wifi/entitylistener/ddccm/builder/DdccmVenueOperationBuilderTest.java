package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.acx.ddccm.protobuf.wifi.RogueProtectionStrategyEnum.RogueProtectionStrategyEnum_AGGRESSIVE;
import static com.ruckus.acx.ddccm.protobuf.wifi.RogueProtectionStrategyEnum.RogueProtectionStrategyEnum_UNSET;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.collect.Sets;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.wifi.AntennaTypeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.ManagementVlanModeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.MeshRadioType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RadioType;
import com.ruckus.acx.ddccm.protobuf.wifi.TxPowerOptionEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.UplinkSelectionEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.BandBalancing;
import com.ruckus.cloud.wifi.eda.servicemodel.Capabilities;
import com.ruckus.cloud.wifi.eda.servicemodel.CapabilitiesApModel;
import com.ruckus.cloud.wifi.eda.servicemodel.CapabilitiesLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.Channels24G;
import com.ruckus.cloud.wifi.eda.servicemodel.Channels5G;
import com.ruckus.cloud.wifi.eda.servicemodel.Channels5GIndoorOutdoor;
import com.ruckus.cloud.wifi.eda.servicemodel.Channels6G;
import com.ruckus.cloud.wifi.eda.servicemodel.Channels6GIndoorOutdoor;
import com.ruckus.cloud.wifi.eda.servicemodel.DeviceGps;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Mesh;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApIotSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApManagementTrafficVlanSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApRebootTimeout;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApSmartMonitor;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueClientAdmissionControl;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueHeight;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLoadBalancing;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRegulatoryChannels;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSnmpAgent;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BandModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BssMinRate6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel24Enum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel50Enum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth24GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth5GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.FacilityEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.IpModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LteBandRegionEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MeshRadioTypeEnumV1_1;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MgmtTxRate6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PoeModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PoeOutModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ProtocolEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RogueNetworkProtectionStrategyEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ScanMethodEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TxPower6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TxPowerEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.redis.YamlPropertySourceFactory;
import com.ruckus.cloud.wifi.repository.ApFirmwareUpgradeRequestRepository;
import com.ruckus.cloud.wifi.repository.IpsecProfileRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.ApUpgradeService;
import com.ruckus.cloud.wifi.service.FirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.FirmwareCountryCodeService;
import com.ruckus.cloud.wifi.service.VenueCurrentFirmwareService;
import com.ruckus.cloud.wifi.service.VenueRadioService;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.exception.CapabilityNotFoundException;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;

@WifiUnitTest
@PropertySource(value = "classpath:application-unit.yml", factory = YamlPropertySourceFactory.class)
public class DdccmVenueOperationBuilderTest {

  private final String defaultApLoginName = "admin";

  @MockBean
  private FirmwareCapabilityService firmwareCapabilityService;

  @MockBean
  private FirmwareCountryCodeService firmwareCountryCodeService;

  @MockBean
  private ApUpgradeService apUpgradeService;

  @MockBean
  private VenueCurrentFirmwareService venueCurrentFirmwareService;

  @MockBean
  private VenueRadioService venueRadioService;

  @MockBean
  private DdccmVenueDhcpServiceSettingOperationBuilder ddccmVenueDhcpServiceSettingOperationBuilder;

  @MockBean
  private DdccmVenueBonjourFencingOperationBuilder ddccmVenueBonjourFencingOperationBuilder;

  @SpyBean
  private DdccmVenueOperationBuilder ddccmVenueOperationBuilder;

  @Autowired
  private VenueRepository venueRepository;

  @Autowired
  private ApFirmwareUpgradeRequestRepository apFirmwareUpgradeRequestRepository;

  @MockBean
  private IpsecProfileRepository ipsecProfileRepository;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyMspTenantId");

  @BeforeEach
  void beforeEach() throws CapabilityNotFoundException {
    var capabilities = new Capabilities();
    capabilities.setApModels(List.of());
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());
  }

  @Test
  public void testVenueBasicSetting() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());
    assertEquals(venue.getId(), operations.get(0).getVenue().getId());
    assertEquals(venue.getTenant().getId(), operations.get(0).getVenue().getTenantId());
    assertTrue(operations.get(0).getVenue().getRecoveryWlan().getEnabled());
    assertEquals(venue.getTenant().getRecoveryPsk(),
        operations.get(0).getVenue().getRecoveryWlan().getPskKey());
    assertEquals(venue.getName(), operations.get(0).getVenue().getName());
    assertEquals(DigestUtils.sha256Hex(venue.getApPassword()),
        operations.get(0).getVenue().getApLoginPassword());
    assertEquals(venue.getTimezone(), operations.get(0).getVenue().getTimezone());
    assertEquals(venue.getCountryCode(), operations.get(0).getVenue().getCountryCode());
    assertTrue(operations.get(0).getVenue().getApHccdEnabled().getValue());
    assertTrue(operations.get(0).getVenue().getSyslog().getAddress().isEmpty());
    assertEquals(venue.getWifiFirmwareVersion().getId(),
        operations.get(0).getVenue().getVersion().getValue());
    assertTrue(operations.get(0).getVenue().getReachabilityTest().getEnableReachability());
    assertEquals("csrc-config",
        operations.get(0).getVenue().getReachabilityTest().getReachabilityConfigs());

    // verify radioCustomization
    assertRadioParams24G(venue.getRadioCustomization().getRadioParams24G(),
        operations.get(0).getVenue().getRadioCustomization().getRadio24());
    assertRadioParams50G(venue.getRadioCustomization().getRadioParams50G(),
        operations.get(0).getVenue().getRadioCustomization().getRadio50());
    assertFalse(operations.get(0).getVenue().getRadioCustomization().getDual5GEnabled());
  }

  @Test
  public void testHandleDhcpServiceSetting() {
    // Given
    doReturn(true).when(ddccmVenueDhcpServiceSettingOperationBuilder)
        .hasDhcpServiceChanges(Mockito.any(), Mockito.any());
    doAnswer(invocation -> {
      return null;
    }).when(ddccmVenueDhcpServiceSettingOperationBuilder).handleDhcpServiceSetting(
        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList());

    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    Set<String> modifiedProperties = Sets.newHashSet(AbstractBaseEntity.Fields.UPDATEDDATE,
        com.ruckus.cloud.wifi.eda.servicemodel.Venue.Fields.NAME);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new ModifiedTxEntity<>(venue, modifiedProperties),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
  }

  @Test
  public void testHandleBonjourFencing() {
    // Given
    doReturn(true).when(ddccmVenueBonjourFencingOperationBuilder)
        .hasBonjourFencingChanged(Mockito.any(), Mockito.any());
    doAnswer(invocation -> {
      return null;
    }).when(ddccmVenueBonjourFencingOperationBuilder).handleBonjourFencing(
        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList());

    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    Set<String> modifiedProperties = Sets.newHashSet(AbstractBaseEntity.Fields.UPDATEDDATE,
        com.ruckus.cloud.wifi.eda.servicemodel.Venue.Fields.NAME);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(
        new ModifiedTxEntity<>(venue, modifiedProperties),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
  }

  @Test
  public void testEnableVenueRogueAp() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp expectedVenueRogueAp = mockVenueRogueAp(
        true);
    venue.setRogueAp(expectedVenueRogueAp);
    venue.getRogueAp().setNetworkProtection(true);
    venue.getRogueAp().setNetworkProtectionStrategy(RogueNetworkProtectionStrategyEnum.AGGRESSIVE);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().getRogueEnabled());
    assertTrue(operations.get(0).getVenue().getRogueProtectionEnabled());
    assertEquals(RogueProtectionStrategyEnum_AGGRESSIVE, operations.get(0).getVenue().getRogueProtectionStrategy());
    assertEquals((int) expectedVenueRogueAp.getReportThreshold(),
        operations.get(0).getVenue().getRogueApReportThreshold().getValue());
  }

  @Test
  public void testDisableVenueRogueAP() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp venueRogueAp = mockVenueRogueAp(false);
    venue.setRogueAp(venueRogueAp);
    venue.getRogueAp().setNetworkProtection(false);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().getRogueEnabled());
    assertFalse(operations.get(0).getVenue().getRogueProtectionEnabled());
    assertEquals((int) venueRogueAp.getReportThreshold(),
        operations.get(0).getVenue().getRogueApReportThreshold().getValue());
    assertEquals(RogueProtectionStrategyEnum_UNSET, operations.get(0).getVenue().getRogueProtectionStrategy());
  }

  @Test
  public void testVenueLteBandLockChannels() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    List<com.ruckus.cloud.wifi.eda.servicemodel.LteBandLockChannel> lteBandLockChannels = mockLteBandLockChannelsSettings();
    venue.setLteBandLockChannels(lteBandLockChannels);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertEquals(lteBandLockChannels.size(),
        operations.get(0).getVenue().getLteBandLockChannelProfileList().size());
  }

  @Test
  public void testEnableVenueMeshSetting() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    com.ruckus.cloud.wifi.eda.servicemodel.Mesh fakeMesh = mockMeshOptions(true);
    venue.setMesh(fakeMesh);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertTrue(operations.get(0).getVenue().getMesh().getEnabled());
    assertEquals(fakeMesh.getPassphrase(), operations.get(0).getVenue().getMesh().getPassphrase());
    assertEquals(fakeMesh.getSsid(), operations.get(0).getVenue().getMesh().getSsid());
    assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.RadioType.RADIO50,
        operations.get(0).getVenue().getMesh().getRadio());
  }

  @Test
  public void testEnableVenueMeshSettingWithEnhancement() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    Mesh fakeMesh = mockMeshOptions(true);
    fakeMesh.setRadioType(MeshRadioTypeEnumV1_1._2_4_GHz);
    fakeMesh.setZeroTouchEnabled(true);
    venue.setMesh(fakeMesh);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertTrue(operations.get(0).getVenue().getMesh().getEnabled());
    assertTrue(operations.get(0).getVenue().getMesh().getZeroTouchEnabled());
    assertEquals(UplinkSelectionEnum.RSSI,
        operations.get(0).getVenue().getMesh().getUplinkSelection());
    assertEquals(RadioType.RADIO24, operations.get(0).getVenue().getMesh().getRadio());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_MESH_CONFIGURATION_FOR_5G_6G_ONLY_TOGGGLE)
  public void testEnableVenueMeshSettingSupport5G6G() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    Mesh fakeMesh = mockMeshOptions(true);
    fakeMesh.setRadioType(MeshRadioTypeEnumV1_1._5_GHz);
    venue.setMesh(fakeMesh);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertEquals(RadioType.RADIO50, operations.get(0).getVenue().getMesh().getRadio());
    assertEquals(MeshRadioType.RADIO_5G_ONLY, operations.get(0).getVenue().getMesh().getRadioType());

    // Given
    fakeMesh.setRadioType(MeshRadioTypeEnumV1_1._6_GHz);

    // When
    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertEquals(RadioType.RADIO50, operations.get(0).getVenue().getMesh().getRadio());
    assertEquals(MeshRadioType.RADIO_6G_ONLY, operations.get(0).getVenue().getMesh().getRadioType());

    // Given
    fakeMesh.setRadioType(MeshRadioTypeEnumV1_1._5_6_GHz);

    // When
    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertEquals(RadioType.RADIO50, operations.get(0).getVenue().getMesh().getRadio());
    assertEquals(MeshRadioType.RADIO_5G_AND_6G, operations.get(0).getVenue().getMesh().getRadioType());
  }

  @Test
  public void testDisableVenueMeshSetting() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    com.ruckus.cloud.wifi.eda.servicemodel.Mesh fakeMesh = mockMeshOptions(false);
    venue.setMesh(fakeMesh);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertFalse(operations.get(0).getVenue().getMesh().getEnabled());
  }

  @Test
  public void testEnableVenueSyslog() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslog fakeSyslog = mockVenueSyslog(true);
    venue.setSyslog(fakeSyslog);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertEquals(fakeSyslog.getServer(), operations.get(0).getVenue().getSyslog().getAddress());
    assertEquals((int) fakeSyslog.getPort(),
        operations.get(0).getVenue().getSyslog().getPort().getValue());
    assertEquals(fakeSyslog.getFacility().toString(),
        operations.get(0).getVenue().getSyslog().getFacility().toString());
    assertEquals("SyslogPriorityEnum_" + fakeSyslog.getPriority().toString(),
        operations.get(0).getVenue().getSyslog().getPriority().toString());
    assertEquals("IPPROTO_" + fakeSyslog.getProtocol().toString(),
        operations.get(0).getVenue().getSyslog().getProtocol().toString());
    assertEquals("SyslogFlowLevelEnum_" + fakeSyslog.getFlowLevel().toString(),
        operations.get(0).getVenue().getSyslog().getFlowLevel().toString());
    assertEquals(fakeSyslog.getSecondaryServer(),
        operations.get(0).getVenue().getSyslog().getSecondaryAddress().getValue());
    assertEquals((int) fakeSyslog.getSecondaryPort(),
        operations.get(0).getVenue().getSyslog().getSecondaryPort().getValue());
    assertEquals("IPPROTO_" + fakeSyslog.getSecondaryProtocol().toString(),
        operations.get(0).getVenue().getSyslog().getSecondaryProtocol().toString());
    assertEquals("PRIMARY_BACKUP", operations.get(0).getVenue().getSyslog().getRedundancyMode());
  }

  @Test
  public void testVenueDenialOfServiceProtection() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    com.ruckus.cloud.wifi.eda.servicemodel.DenialOfServiceProtection fakeDenial = mockDenialOfServiceProtection();
    venue.setDenialOfServiceProtection(fakeDenial);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertTrue(operations.get(0).getVenue().getDenialOfServiceProtection().getEnabled());
    assertEquals((int) fakeDenial.getBlockingPeriod(),
        operations.get(0).getVenue().getDenialOfServiceProtection().getBlockingPeriod().getValue());
    assertEquals((int) fakeDenial.getFailThreshold(),
        operations.get(0).getVenue().getDenialOfServiceProtection().getFailThreshold().getValue());
    assertEquals((int) fakeDenial.getCheckPeriod(),
        operations.get(0).getVenue().getDenialOfServiceProtection().getCheckPeriod().getValue());
  }

  @Test
  public void testEnableVenueDual5G() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G fakeDual5G = mockRadioParamsDual5G();
    venue.getRadioCustomization().setRadioParamsDual5G(fakeDual5G);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().getRadioCustomization().getDual5GEnabled());
    assertRadioParamsDual5G(fakeDual5G, operations.get(0).getVenue().getRadioCustomization());
  }

  public com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization testEnableVenue6G() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G fake6G =
        mockRadioParams6G(ScanMethodEnum.BACKGROUND_SCANNING, TxPower6GEnum.MAX,
            ChannelBandwidth6GEnum.AUTO);
    venue.getRadioCustomization().setRadioParams6G(fake6G);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertRadioParam6G(fake6G, operations.get(0).getVenue().getRadioCustomization().getRadio60());

    // When
    fake6G = mockRadioParams6G(ScanMethodEnum.CHANNELFLY, TxPower6GEnum.MIN,
        ChannelBandwidth6GEnum._20MHz);
    venue.getRadioCustomization().setRadioParams6G(fake6G);
    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertRadioParam6G(fake6G, operations.get(0).getVenue().getRadioCustomization().getRadio60());

    // When
    fake6G = mockRadioParams6G(ScanMethodEnum.MANUAL, TxPower6GEnum._1,
        ChannelBandwidth6GEnum._80MHz);
    venue.getRadioCustomization().setRadioParams6G(fake6G);
    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertRadioParam6G(fake6G, operations.get(0).getVenue().getRadioCustomization().getRadio60());

    return operations.get(0).getVenue().getRadioCustomization();
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_6G_CHANNEL_SEPARATION_TOGGLE)
  public void testEnableVenue6GWith6gDataRateControlEnabled() {
    com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization ddccmVenueRadioCustomization = testEnableVenue6G();

    assertEquals(BssMinRate6GEnum.HE_MCS_1.toString(),
        ddccmVenueRadioCustomization.getRadio60().getBssMinRate6G());
    assertEquals(MgmtTxRate6GEnum._12.toString(),
        ddccmVenueRadioCustomization.getRadio60().getMgmtTxRate6G());
  }

  @Test
  @FeatureFlag(enable =FlagNames.WIFI_AP_6G_CHANNEL_SEPARATION_TOGGLE)
  public void testEnableVenue6GWith6gAfcDisabled() {
    doReturn(false).when(ddccmVenueOperationBuilder).isAfcFeatureEnabled();

    com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization ddccmVenueRadioCustomization = testEnableVenue6G();

    assertTrue(ddccmVenueRadioCustomization.getRadio60().getLowPowerIndoorModeEnable());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_6G_CHANNEL_SEPARATION_TOGGLE)
  public void testEnableVenue6GWith6gAfcEnabled() {
    com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization ddccmVenueRadioCustomization = testEnableVenue6G();

    assertFalse(ddccmVenueRadioCustomization.getRadio60().getLowPowerIndoorModeEnable());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_6G_CHANNEL_SEPARATION_TOGGLE)
  public void testEnableVenue6GWith6gChannelSeparationEnabled() {
    com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization ddccmVenueRadioCustomization = testEnableVenue6G();

    assertEquals(2, ddccmVenueRadioCustomization.getRadio60().getAllowedOutdoorChannelListCount());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_6G_CHANNEL_SEPARATION_TOGGLE)
  public void testVenue6gWithFf6gChannelSeparationEnabled() {
    com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization ddccmVenueRadioCustomization = testEnableVenue6G();

    assertTrue(ddccmVenueRadioCustomization.getRadio60().getFf6GChannelSeparationEnabled());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_AP_6G_CHANNEL_SEPARATION_TOGGLE)
  public void testVenue6gWithFf6gChannelSeparationDisabled() {
    var venue = mockVenue();

    // When
    var operations = ddccmVenueOperationBuilder
        .build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().getRadioCustomization()
        .getRadio60().getFf6GChannelSeparationEnabled());
  }

  @Test
  public void testVenueDeviceGps() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();
    DeviceGps gps = new DeviceGps();
    gps.setLatitude("12.23");
    gps.setLongitude("12.23");
    venue.setDeviceGps(gps);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertEquals(StringValue.of("12.23,12.23"), operations.get(0).getVenue().getDeviceGps());

  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_SWITCHABLE_RF_TOGGLE,
      FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE})
  void testVenuex670() throws CapabilityNotFoundException {
    var capabilities = new Capabilities();
    var T670SN = new CapabilitiesApModel();
    T670SN.setModel("T670SN");
    T670SN.setBandCombinationCapabilities(List.of(BandModeEnum.DUAL, BandModeEnum.TRIPLE));

    var lan1 = new CapabilitiesLanPort();
    lan1.setId("1");
    lan1.setDefaultType("TRUNK");
    var lan2 = new CapabilitiesLanPort();
    lan2.setId("2");
    lan2.setDefaultType("TRUNK");
    T670SN.setLanPorts(List.of(lan1, lan2));

    capabilities.setApModels(List.of(T670SN));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());

    // Given
    var venue = mockVenue();
    venue.setModelSpecificAttributes(mockDisableT670SN(venue));
    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());
    // Then
    verify(firmwareCapabilityService, times(1)).getVenueCapabilities(any());

    var venueToVerify = operations.get(0).getVenue();
    assertEquals(1, venueToVerify.getVenueApModelsCount());

    var modelToVerify = venueToVerify.getVenueApModels(0);
    assertEquals("T670SN", modelToVerify.getModelName());
    assertEquals(venue.getId(), modelToVerify.getVenueId());
    assertEquals(StringValue.of("2-5-6"), modelToVerify.getBandCombinationMode());
    assertEquals(AntennaTypeEnum.Narrow, modelToVerify.getAntennaType());
    assertTrue(modelToVerify.getUsbPortEnabled().getValue());  // USB default is true

  }

  @Tag("EthernetPortProfileTest")
  @Test
  void testVenueLanPortSetting() throws CapabilityNotFoundException {
    var capabilities = new Capabilities();
    var r610 = new CapabilitiesApModel();
    r610.setModel("R610");
    r610.setLedOn(Boolean.TRUE);
    var lan1 = new CapabilitiesLanPort();
    lan1.setId("1");
    lan1.setDefaultType("TRUNK");
    lan1.setUntagId((short) 1);
    lan1.setVlanMembers("1-4094");
    lan1.setTrunkPortOnly(Boolean.FALSE);
    lan1.setSupportDisable(Boolean.FALSE);
    lan1.setIsPoePort(Boolean.TRUE);
    lan1.setIsPoeOutPort(Boolean.FALSE);
    var lan2 = new CapabilitiesLanPort();
    lan2.setId("2");
    lan2.setDefaultType("TRUNK");
    lan2.setUntagId((short) 1);
    lan2.setVlanMembers("1-4094");
    lan2.setTrunkPortOnly(Boolean.FALSE);
    lan2.setSupportDisable(Boolean.TRUE);
    lan2.setIsPoePort(Boolean.FALSE);
    lan2.setIsPoeOutPort(Boolean.FALSE);
    r610.setLanPorts(List.of(lan1, lan2));
    capabilities.setApModels(List.of(r610));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());
    // Given
    var venue = mockVenue();
    venue.setModelSpecificAttributes(mockDisableR610SecondLanPort(venue));
    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());
    // Then
    verify(firmwareCapabilityService, times(1))
        .getVenueCapabilities(any());
    var venueToVerify = operations.get(0).getVenue();
    assertEquals(1, venueToVerify.getVenueApModelsCount());
    var modelToVerify = venueToVerify.getVenueApModels(0);
    assertEquals("R610", modelToVerify.getModelName());
    assertEquals(venue.getId(), modelToVerify.getVenueId());
    assertEquals(
        BoolValue.newBuilder().setValue(true).build(),
        modelToVerify.getLedStatusEnabled());
    assertEquals(2, modelToVerify.getLanPortCount());
    assertEquals(
        BoolValue.newBuilder().setValue(true).build(),
        modelToVerify.getLanPort(0).getEnabled());
    assertEquals(
        BoolValue.newBuilder().setValue(false).build(),
        modelToVerify.getLanPort(1).getEnabled());
  }

  @Test
  public void testEnableVenueApSnmp() {
    // Given
    var venue = mockVenue();

    var apSnmpAgentProfile = new ApSnmpAgentProfile();
    apSnmpAgentProfile.setId(uuid());

    var venueSnmpAgent = new VenueSnmpAgent();
    venueSnmpAgent.setEnableApSnmp(true);
    venueSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);
    venue.setApSnmpAgent(venueSnmpAgent);

    // When
    var operations = ddccmVenueOperationBuilder
        .build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().getSnmpAgent().getEnabled());
    assertEquals(apSnmpAgentProfile.getId(),
        operations.get(0).getVenue().getSnmpAgent().getApSnmpAgentProfileId().getValue());
  }

  @Test
  public void testDisableVenueApSnmp() {
    // Given
    var venue = mockVenue();

    // When
    var operations = ddccmVenueOperationBuilder
        .build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().getSnmpAgent().getEnabled());
    assertFalse(operations.get(0).getVenue().getSnmpAgent().hasApSnmpAgentProfileId());
  }

  @Test
  public void testUpdateVenueWithHeight() {
    // Given
    var venue = mockVenue();

    venue.setHeight(new VenueHeight());
    venue.getHeight().setMinFloor(1);
    venue.getHeight().setMaxFloor(2);

    // When
    var operations = ddccmVenueOperationBuilder
        .build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertEquals(0f, operations.get(0).getVenue().getAfcVenueHeightAgl().getMinHeight().getValue());
    assertEquals(10.5f, operations.get(0).getVenue().getAfcVenueHeightAgl().getMaxHeight().getValue());
  }

  @Test
  public void testVenueLoadBalancing() {
    // Given
    var venue = mockVenue();
    VenueLoadBalancing vlb = new VenueLoadBalancing();
    vlb.setEnabled(false); // ruckus-one default value
    venue.setLoadBalancing(vlb);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertEquals(false, operations.get(0).getVenue().getLoadBalancing().getEnabled());
  }

  @Test
  public void testUpdateVenueWithoutHeight() {
    // Given
    var venue = mockVenue();

    assertNull(venue.getHeight().getMinFloor());
    assertNull(venue.getHeight().getMaxFloor());

    // When
    var operations = ddccmVenueOperationBuilder
        .build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().hasAfcVenueHeightAgl());
    assertEquals(0f, operations.get(0).getVenue().getAfcVenueHeightAgl().getMinHeight().getValue());
    assertEquals(0f, operations.get(0).getVenue().getAfcVenueHeightAgl().getMaxHeight().getValue());
  }

  @Test
  public void testVenueMulticastRateLimiting() {
    var venue = mockVenue();
    venue.getRadioCustomization().setRadioParams6G(new VenueRadioParams6G());
    venue.getRadioCustomization().getRadioParams6G().setEnableMulticastDownlinkRateLimiting(true);
    venue.getRadioCustomization().getRadioParams6G().setMulticastUplinkRateLimiting(100);
    venue.getRadioCustomization().getRadioParams6G().setMulticastDownlinkRateLimiting(6);
    venue.getRadioCustomization().getRadioParams6G().setEnableMulticastUplinkRateLimiting(true);

    var operations = ddccmVenueOperationBuilder
        .build(new NewTxEntity<>(venue), emptyTxChanges());

    assertThat(operations)
        .isNotNull()
        .hasSize(1)
        .first()
        .matches(Operation::hasVenue)
        .extracting(Operation::getVenue)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
        .extracting(VenueRadioCustomization::getRadio60)
        .matches(e -> e.getMulticastRateLimitUplinkRl().getValue() == 100)
        .matches(e -> e.getMulticastRateLimitDownlinkRl().getValue() == 6);
  }

  @Test
  public void testVenueMulticastRateLimiting_not_enable() {
    var venue = mockVenue();
    venue.getRadioCustomization().getRadioParams6G().setEnableMulticastDownlinkRateLimiting(false);
    venue.getRadioCustomization().getRadioParams6G().setMulticastUplinkRateLimiting(100);
    venue.getRadioCustomization().getRadioParams6G().setMulticastDownlinkRateLimiting(6);
    venue.getRadioCustomization().getRadioParams6G().setEnableMulticastUplinkRateLimiting(false);

    var operations = ddccmVenueOperationBuilder
        .build(new NewTxEntity<>(venue), emptyTxChanges());

    assertThat(operations)
        .isNotNull()
        .hasSize(1)
        .first()
        .matches(Operation::hasVenue)
        .extracting(Operation::getVenue)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
        .extracting(VenueRadioCustomization::getRadio60)
        .matches(e -> e.hasMulticastRateLimitUplinkRl() == true)
        .matches(e -> e.getMulticastRateLimitUplinkRl().getValue() == 0)
        .matches(e -> e.hasMulticastRateLimitDownlinkRl() == true)
        .matches(e -> e.getMulticastRateLimitDownlinkRl().getValue() == 0);

  }

  @Test
  public void testVenueClientAdmissionControlWhenFFEnable() {
    // Given
    var venue = mockVenue();
    VenueClientAdmissionControl venueClientAdmissionControl = new VenueClientAdmissionControl();
    venueClientAdmissionControl.setEnable24G(true);
    venueClientAdmissionControl.setMinClientCount24G((short) 11);
    venueClientAdmissionControl.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl.setEnable50G(true);
    venueClientAdmissionControl.setMinClientCount50G((short) 21);
    venueClientAdmissionControl.setMaxRadioLoad50G((short) 50);
    venueClientAdmissionControl.setMinClientThroughput50G((short) 10);
    venue.setClientAdmissionControl(venueClientAdmissionControl);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(
        operations
            .get(0)
            .getVenue()
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getEnabled());
    assertEquals(
        11,
        operations
            .get(0)
            .getVenue()
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getMinimumClientCount());
    assertEquals(
        50,
        operations
            .get(0)
            .getVenue()
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getMaxiumumRadioLoad());
    assertEquals(
        10,
        operations
            .get(0)
            .getVenue()
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getMinimumClientThroughput());
    assertTrue(
        operations
            .get(0)
            .getVenue()
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getEnabled());
    assertEquals(
        21,
        operations
            .get(0)
            .getVenue()
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getMinimumClientCount());
    assertEquals(
        50,
        operations
            .get(0)
            .getVenue()
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getMaxiumumRadioLoad());
    assertEquals(
        10,
        operations
            .get(0)
            .getVenue()
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getMinimumClientThroughput());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_VENUE_AP_MGMT_VLAN_TOGGLE)
  public void testVenueApManagementVlanWhenVlanOverrideEnabledIsFalse() {
    // Given
    var venue = mockVenue();
    VenueApManagementTrafficVlanSettings managementVlan = new VenueApManagementTrafficVlanSettings();
    managementVlan.setVlanId(null);
    venue.setApManagementVlan(managementVlan);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertEquals(operations.get(0).getVenue().getApManagementVlanMode(), ManagementVlanModeEnum.KEEP);
    assertFalse(operations.get(0).getVenue().hasApManagementVlanId());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_VENUE_AP_MGMT_VLAN_TOGGLE)
  public void testVenueApManagementVlanWhenVlanOverrideEnabledIsTrue() {
    // Given
    var venue = mockVenue();
    VenueApManagementTrafficVlanSettings managementVlan = new VenueApManagementTrafficVlanSettings();
    managementVlan.setVlanId((short) 7);
    venue.setApManagementVlan(managementVlan);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertEquals(ManagementVlanModeEnum.USER_DEFINED,
        operations.get(0).getVenue().getApManagementVlanMode());
    assertTrue(operations.get(0).getVenue().hasApManagementVlanId());
    assertEquals(Int32Value.of(7), operations.get(0).getVenue().getApManagementVlanId());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_VENUE_AP_MGMT_VLAN_TOGGLE)
  public void testVenueApManagementVlanWhenFFDisable() {
    // Given
    var venue = mockVenue();
    VenueApManagementTrafficVlanSettings managementVlan = new VenueApManagementTrafficVlanSettings();
    managementVlan.setVlanId((short) 7);
    venue.setApManagementVlan(managementVlan);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().hasApManagementVlanId());
    assertEquals(ManagementVlanModeEnum.KEEP,
        operations.get(0).getVenue().getApManagementVlanMode());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  public void testHotspot20VenueProfileWhenAnyExistsHotspot20Enabled() {
    // Given
    var venue = mockVenue();
    doReturn(true).when(venueRepository).existsHotspot20EnabledByByTenantIdAndVenueId(any(), any());

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertEquals(venue.getId(), operations.get(0).getVenue().getHotspot20VenueProfileId().getValue());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testHotspot20VenueProfileWhenNoAnyExistsHotspot20Enabled() {
    // Given
    var venue = mockVenue();
    doReturn(false).when(venueRepository).existsHotspot20EnabledByByTenantIdAndVenueId(any(), any());

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().hasHotspot20VenueProfileId());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_EDA_IP_MODE_CONFIG_TOGGLE)
  void testUpdateDeviceIpModeWhenFFEnable() {
    // Given
    var venue = mockVenue();
    venue.setDeviceIpMode(IpModeEnum.IPV4_IPV6);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.IpModeEnum.IPV4_IPV6,
        operations.get(0).getVenue().getDeviceIpMode());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_EDA_IP_MODE_CONFIG_TOGGLE)
  void testUpdateDeviceIpModeWhenFFDisable() {
    // Given
    var venue = mockVenue();
    venue.setDeviceIpMode(IpModeEnum.IPV4_IPV6);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.IpModeEnum.IpModeEnum_UNSET,
        operations.get(0).getVenue().getDeviceIpMode());
  }

  @Test
  public void testEnableContactDeviceRegistrar() {
    // Given default
    var venue = mockVenue();

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().getContactDeviceRegistrar());

    // Given enable
    venue.setContactDeviceRegistrarEnabled(true);

    // When
    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().getContactDeviceRegistrar());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE)
  void testUpdateTlsKeyEnhancedModeWhenFFEnable() {
    // Given
    var venue = mockVenue();
    venue.setTlsKeyEnhancedModeEnabled(true);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().getTlsKeyEnhancedModeEnabled());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE)
  void testUpdateTlsKeyEnhancedModeWhenFFDisable() {
    // Given
    var venue = mockVenue();
    venue.setTlsKeyEnhancedModeEnabled(true);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().getTlsKeyEnhancedModeEnabled());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_EDA_LBS_TOGGLE)
  @FeatureRole("LOCATION-BASED-SERVICES")
  void testLbsServerProfileWhenFFEnable_andBeta() {
    // Given
    var lbsServerProfile = Generators.lbsServerProfile().generate();
    var venue = mockVenue();
    venue.setLbsServerProfile(lbsServerProfile);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().hasLocationBasedService());
    assertEquals(lbsServerProfile.getId(), operations.get(0).getVenue().getLocationBasedService().getId());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_EDA_LBS_TOGGLE)
  void testLbsServerProfileWhenFFEnable() {
    // Given
    var lbsServerProfile = Generators.lbsServerProfile().generate();
    var venue = mockVenue();
    venue.setLbsServerProfile(lbsServerProfile);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().hasLocationBasedService());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_EDA_LBS_TOGGLE)
  void testLbsServerProfileWithoutLbsServerWhenFFEnable() {
    // Given
    var venue = mockVenue();

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().hasLocationBasedService());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_EDA_LBS_TOGGLE)
  void testLbsServerProfileWhenFFDisable() {
    // Given
    var lbsServerProfile = Generators.lbsServerProfile().generate();
    var venue = mockVenue();
    venue.setLbsServerProfile(lbsServerProfile);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().hasLocationBasedService());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_REBOOT_TIMEOUT_WLAN_TOGGLE)
  void testUpdateVenueApRebootTimeoutWhenFFEnable() {
    // Given
    var venue = mockVenue();
    venue.setRebootTimeout(new VenueApRebootTimeout());
    venue.getRebootTimeout().setGatewayLossTimeout(1800);
    venue.getRebootTimeout().setServerLossTimeout(7200);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().hasRebootTimeout());
    assertEquals(1800, operations.get(0).getVenue().getRebootTimeout().getGwLossTimeout());
    assertEquals(7200, operations.get(0).getVenue().getRebootTimeout().getServerLossTimeout());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_AP_REBOOT_TIMEOUT_WLAN_TOGGLE)
  void testUpdateVenueApRebootTimeoutWhenFFDisable() {
    // Given
    var venue = mockVenue();
    venue.setRebootTimeout(new VenueApRebootTimeout());

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().hasRebootTimeout());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_SMART_MONITOR_DISABLE_WLAN_TOGGLE)
  void testUpdateVenueApSmartMonitorWhenFFEnable() {
    // Given
    var venue = mockVenue();
    venue.setSmartMonitor(new VenueApSmartMonitor());
    venue.getSmartMonitor().setEnabled(true);
    venue.getSmartMonitor().setInterval((short) 10);
    venue.getSmartMonitor().setThreshold((short) 3);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().hasSmartMonitor());
    assertTrue(operations.get(0).getVenue().getSmartMonitor().getEnabled());
    assertEquals(10, operations.get(0).getVenue().getSmartMonitor().getIntervalSeconds());
    assertEquals(3, operations.get(0).getVenue().getSmartMonitor().getThresholdTimes());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_SMART_MONITOR_DISABLE_WLAN_TOGGLE)
  void testUpdateVenueApSmartMonitorWhenFFDisable() {
    // Given
    var venue = mockVenue();
    venue.setSmartMonitor(new VenueApSmartMonitor());
    venue.getSmartMonitor().setEnabled(true);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().hasSmartMonitor());
  }


  @Test
  @FeatureFlag(enable = FlagNames.IOT_MQTT_BROKER_TOGGLE)
  void testUpdateVenueApIotSettingsWhenFFEnable() {
    // Given
    var venue = mockVenue();
    venue.setIotSettings(new VenueApIotSettings());
    venue.getIotSettings().setEnabled(true);
    venue.getIotSettings().setMqttBrokerAddress("*************");

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertTrue(operations.get(0).getVenue().hasIotSettings());
    assertTrue(operations.get(0).getVenue().getIotSettings().getEnabled());
    assertEquals("*************", operations.get(0).getVenue().getIotSettings().getMqttBrokerAddress());
  }

  @Test
  @FeatureFlag(disable = FlagNames.IOT_MQTT_BROKER_TOGGLE)
  void testUpdateVenueApIotSettingsWhenFFDisable() {
    // Given
    var venue = mockVenue();
    venue.setIotSettings(new VenueApIotSettings());
    venue.getIotSettings().setEnabled(true);

    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getVenue());
    assertFalse(operations.get(0).getVenue().hasIotSettings());
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_TX_POWER_TOGGLE)
  public void testVenueTxPower23() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue(TxPowerEnum._23);
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G fake6G =
        mockRadioParams6G(ScanMethodEnum.BACKGROUND_SCANNING, TxPower6GEnum._23, ChannelBandwidth6GEnum.AUTO);
    venue.getRadioCustomization().setRadioParams6G(fake6G);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());
    assertEquals(venue.getId(), operations.get(0).getVenue().getId());
    assertEquals(venue.getTenant().getId(), operations.get(0).getVenue().getTenantId());
    assertEquals(TxPowerEnum._10.toString(), operations.get(0).getVenue().getRadioCustomization().getRadio24().getTxPower());
    assertEquals(TxPowerOptionEnum._23, operations.get(0).getVenue().getRadioCustomization().getRadio24().getTxPowerOption());
    assertEquals(TxPowerEnum._10.toString(), operations.get(0).getVenue().getRadioCustomization().getRadio50().getTxPower());
    assertEquals(TxPowerOptionEnum._23, operations.get(0).getVenue().getRadioCustomization().getRadio50().getTxPowerOption());
    assertEquals(TxPowerEnum._10.toString(), operations.get(0).getVenue().getRadioCustomization().getRadio60().getTxPower());
    assertEquals(TxPowerOptionEnum._23, operations.get(0).getVenue().getRadioCustomization().getRadio60().getTxPowerOption());
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_TX_POWER_TOGGLE)
  public void testVenueTxPower10() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue(TxPowerEnum._10);
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G fake6G =
        mockRadioParams6G(ScanMethodEnum.BACKGROUND_SCANNING, TxPower6GEnum._10, ChannelBandwidth6GEnum.AUTO);
    venue.getRadioCustomization().setRadioParams6G(fake6G);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());
    assertEquals(venue.getId(), operations.get(0).getVenue().getId());
    assertEquals(venue.getTenant().getId(), operations.get(0).getVenue().getTenantId());
    assertEquals(TxPowerEnum._10.toString(), operations.get(0).getVenue().getRadioCustomization().getRadio24().getTxPower());
    assertEquals(TxPowerOptionEnum._10, operations.get(0).getVenue().getRadioCustomization().getRadio24().getTxPowerOption());
    assertEquals(TxPowerEnum._10.toString(), operations.get(0).getVenue().getRadioCustomization().getRadio50().getTxPower());
    assertEquals(TxPowerOptionEnum._10, operations.get(0).getVenue().getRadioCustomization().getRadio50().getTxPowerOption());
    assertEquals(TxPowerEnum._10.toString(), operations.get(0).getVenue().getRadioCustomization().getRadio60().getTxPower());
    assertEquals(TxPowerOptionEnum._10, operations.get(0).getVenue().getRadioCustomization().getRadio60().getTxPowerOption());
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_TX_POWER_TOGGLE)
  public void testVenueTxPowerMin() {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue(TxPowerEnum.MIN);
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G fake6G =
        mockRadioParams6G(ScanMethodEnum.BACKGROUND_SCANNING, TxPower6GEnum.MIN, ChannelBandwidth6GEnum.AUTO);
    venue.getRadioCustomization().setRadioParams6G(fake6G);

    // When
    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());

    // Then
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());
    assertEquals(venue.getId(), operations.get(0).getVenue().getId());
    assertEquals(venue.getTenant().getId(), operations.get(0).getVenue().getTenantId());
    assertEquals(TxPowerEnum.MIN.toString().toLowerCase(), operations.get(0).getVenue().getRadioCustomization().getRadio24().getTxPower());
    assertEquals(TxPowerOptionEnum.MIN, operations.get(0).getVenue().getRadioCustomization().getRadio24().getTxPowerOption());
    assertEquals(TxPowerEnum.MIN.toString().toLowerCase(), operations.get(0).getVenue().getRadioCustomization().getRadio50().getTxPower());
    assertEquals(TxPowerOptionEnum.MIN, operations.get(0).getVenue().getRadioCustomization().getRadio50().getTxPowerOption());
    assertEquals(TxPowerEnum.MIN.toString().toLowerCase(), operations.get(0).getVenue().getRadioCustomization().getRadio60().getTxPower());
    assertEquals(TxPowerOptionEnum.MIN, operations.get(0).getVenue().getRadioCustomization().getRadio60().getTxPowerOption());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_SUPPORT_SECOND_ZONE_GPB_FOR_CBAND_TOGGLE)
  public void testVenueSetAllowCbandChannelsOverrideWhenFFEnable() {
    // Given
    var venue = mockVenue();
    venue.setWifiFirmwareVersion(new ApVersion("7.0.0.200.2365"));
    // When
    // Case1: venue 7.x + only ap 6.x
    List<ApFirmwareUpgradeRequest> requests = getApFirmwareUpgradeRequests(venue.getId());
    when(apFirmwareUpgradeRequestRepository.findByTenantIdAndVenueId(txCtxExtension.getTenantId(),
        venue.getId())).thenReturn(requests);
    when(apUpgradeService.isVenueNotFw6HasFw6Ap(txCtxExtension.getTenantId(),
        venue.getId())).thenReturn(true);
    var apMaxMinFw = Pair.of("6.2.4.103.259", "6.2.1.103.123");
    when(apUpgradeService.getMaximumAndMinimumFirmwareVersion(txCtxExtension.getTenantId(),
        venue)).thenReturn(
        apMaxMinFw);
    doAnswer(invocation -> {
      var apVersion = (ApVersion) invocation.getArgument(0);
      var channels5G = new Channels5GIndoorOutdoor();
      if (apVersion.getId().equals(venue.getWifiFirmwareVersion().getId())) {
        channels5G.setAuto(List.of(Channel50Enum._161));
      }
      return channels5G;
    }).when(firmwareCountryCodeService)
        .getCbandChannel(any(), any());
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());
    // Then
    assertFalse(operations.get(0).getVenue().getAllowCbandChannelsOverride().getValue());

    // Case2: venue 6.x + ap 6.x (No case for venue 6.x and ap 7.x now)
    venue.setWifiFirmwareVersion(new ApVersion("6.2.1.0.233"));
    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());
    // Then
    assertTrue(operations.get(0).getVenue().getAllowCbandChannelsOverride().equals(BoolValue.getDefaultInstance()));
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_SUPPORT_SECOND_ZONE_GPB_FOR_CBAND_TOGGLE, FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
  public void testVenueSetAllowCbandChannelsOverrideByApModelWhenFFEnable() {
    // Given
    var venue = mockVenue();
    // When
    // Case1: venue 7.x + only ap 6.x
    when(venueCurrentFirmwareService.getTheBiggestFwVersion(txCtxExtension.getTenantId(),
        venue.getId())).thenReturn(new ApVersion("7.0.0.200.2365"));
    List<ApFirmwareUpgradeRequest> requests = getApFirmwareUpgradeRequests(venue.getId());
    when(apFirmwareUpgradeRequestRepository.findByTenantIdAndVenueId(txCtxExtension.getTenantId(),
        venue.getId())).thenReturn(requests);
    when(venueCurrentFirmwareService.hasFw6ApAndOtherFwAp(txCtxExtension.getTenantId(),
        venue.getId())).thenReturn(true);
    var apMaxMinFw = Pair.of("6.2.3.103.244", "6.2.1.103.123");
    when(venueCurrentFirmwareService.getMaximumAndMinimumFirmwareVersion(txCtxExtension.getTenantId(),
        venue)).thenReturn(
        apMaxMinFw);
    doAnswer(invocation -> {
      var apVersion = (ApVersion) invocation.getArgument(0);
      var channels5G = new Channels5GIndoorOutdoor();
      if (apVersion.getId().equals("7.0.0.200.2365")) {
        channels5G.setAuto(List.of(Channel50Enum._161));
      }
      return channels5G;
    }).when(firmwareCountryCodeService)
        .getCbandChannel(any(), any());
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());
    // Then
    assertFalse(operations.get(0).getVenue().getAllowCbandChannelsOverride().getValue());

    // Case2: venue 6.x + ap 6.x
    when(venueCurrentFirmwareService.getTheBiggestFwVersion(txCtxExtension.getTenantId(),
        venue.getId())).thenReturn(new ApVersion("6.2.4.103.259"));
    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());
    // Then
    assertTrue(operations.get(0).getVenue().getAllowCbandChannelsOverride().equals(BoolValue.getDefaultInstance()));

    // Case3: venue 6.x + ap 7.x
    apMaxMinFw = Pair.of("7.0.0.200.2362", "6.2.4.103.259");
    when(venueCurrentFirmwareService.getMaximumAndMinimumFirmwareVersion(txCtxExtension.getTenantId(),
        venue)).thenReturn(
        apMaxMinFw);
    doAnswer(invocation -> {
      var apVersion = (ApVersion) invocation.getArgument(0);
      var channels5G = new Channels5GIndoorOutdoor();
      if (apVersion.getId().equals("7.0.0.200.2362")) {
        channels5G.setAuto(List.of(Channel50Enum._161));
      }
      return channels5G;
    }).when(firmwareCountryCodeService)
        .getCbandChannel(any(), any());
    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());
    // Then
    assertFalse(operations.get(0).getVenue().getAllowCbandChannelsOverride().getValue());
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_SUPPORT_SECOND_ZONE_GPB_FOR_CBAND_TOGGLE, FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
  public void testVenueSetAllowCbandChannelsOverrideWhenCbandNotSupported() {
    // Given
    var venue = mockVenue();
    when(venueCurrentFirmwareService.getTheBiggestFwVersion(txCtxExtension.getTenantId(),
        venue.getId())).thenReturn(new ApVersion("7.0.0.200.2365"));
    List<ApFirmwareUpgradeRequest> requests = getApFirmwareUpgradeRequests(venue.getId());
    when(apFirmwareUpgradeRequestRepository.findByTenantIdAndVenueId(txCtxExtension.getTenantId(),
        venue.getId())).thenReturn(requests);
    when(venueCurrentFirmwareService.hasFw6ApAndOtherFwAp(txCtxExtension.getTenantId(),
        venue.getId())).thenReturn(true);
    var apMaxMinFw = Pair.of("7.0.0.200.2365", "6.2.4.103.259");
    when(venueCurrentFirmwareService.getMaximumAndMinimumFirmwareVersion(txCtxExtension.getTenantId(),
        venue)).thenReturn(
        apMaxMinFw);
    doAnswer(invocation -> {
      var apVersion = (ApVersion) invocation.getArgument(0);
      if (apVersion.getId().equals("7.0.0.200.2365")) {
        var channels5G = new Channels5GIndoorOutdoor();
        channels5G.setAuto(List.of(Channel50Enum._161));
        return channels5G;
      }
      // no cband channel for 6.2.4.103.259
      return null;
    }).when(firmwareCountryCodeService)
        .getCbandChannel(any(), any());
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
        emptyTxChanges());
    assertFalse(operations.get(0).getVenue().getAllowCbandChannelsOverride().getValue());
  }

  @Test
  public void testVenueWhenManualChannelSelection() throws InvalidPropertyValueException {
    // Given
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = mockVenue();

    VenueRegulatoryChannels mockChannels = new VenueRegulatoryChannels();
    doReturn(mockChannels).when(venueRadioService)
            .getRegulatoryChannels(any(ApVersion.class), any(), any(), any());

    Channels24G channels24G = new Channels24G();
    List<Channel24Enum> channels24GList = Arrays.asList(Channel24Enum._1, Channel24Enum._2, Channel24Enum._3, Channel24Enum._4, Channel24Enum._5,
        Channel24Enum._6, Channel24Enum._7, Channel24Enum._8, Channel24Enum._9, Channel24Enum._10, Channel24Enum._11);
    channels24G.setAuto(channels24GList);
    mockChannels.set_24GChannels(channels24G);

    Channels5G channels5G = new Channels5G();
    Channels5GIndoorOutdoor channels5GIndoor = new Channels5GIndoorOutdoor();
    List<Channel50Enum> channels5GList = Arrays.asList(Channel50Enum._36, Channel50Enum._40, Channel50Enum._44, Channel50Enum._48,
        Channel50Enum._52, Channel50Enum._56, Channel50Enum._60, Channel50Enum._64,
        Channel50Enum._100, Channel50Enum._104, Channel50Enum._108, Channel50Enum._112,
        Channel50Enum._116, Channel50Enum._120, Channel50Enum._124, Channel50Enum._128,
        Channel50Enum._132, Channel50Enum._136, Channel50Enum._140, Channel50Enum._144,
        Channel50Enum._149, Channel50Enum._153, Channel50Enum._157, Channel50Enum._161);
    channels5GIndoor.setAuto(channels5GList);
    channels5G.setIndoor(channels5GIndoor);
    Channels5GIndoorOutdoor channels5GOutdoor = new Channels5GIndoorOutdoor();
    channels5GOutdoor.setAuto(channels5GList);
    channels5G.setOutdoor(channels5GOutdoor);
    mockChannels.set_5GChannels(channels5G);

    Channels5G channels5GLower = new Channels5G();
    Channels5GIndoorOutdoor channels5GLowerIndoor = new Channels5GIndoorOutdoor();
    List<Channel50Enum> channels5GLowerList = Arrays.asList(Channel50Enum._36, Channel50Enum._40, Channel50Enum._44, Channel50Enum._48,
        Channel50Enum._52, Channel50Enum._56, Channel50Enum._60, Channel50Enum._64);
    channels5GLowerIndoor.setAuto(channels5GLowerList);
    channels5GLower.setIndoor(channels5GLowerIndoor);
    Channels5GIndoorOutdoor channels5GLowerOutdoor = new Channels5GIndoorOutdoor();
    channels5GLowerOutdoor.setAuto(channels5GLowerList);
    channels5GLower.setOutdoor(channels5GLowerOutdoor);
    mockChannels.set_5GLowerChannels(channels5GLower);

    Channels5G channels5GUpper = new Channels5G();
    Channels5GIndoorOutdoor channels5GUpperIndoor = new Channels5GIndoorOutdoor();
    List<Channel50Enum> channels5GUpperList = Arrays.asList(Channel50Enum._100, Channel50Enum._104, Channel50Enum._108, Channel50Enum._112,
        Channel50Enum._116, Channel50Enum._120, Channel50Enum._124, Channel50Enum._128,
        Channel50Enum._132, Channel50Enum._136, Channel50Enum._140, Channel50Enum._144,
        Channel50Enum._149, Channel50Enum._153, Channel50Enum._157, Channel50Enum._161);
    channels5GUpperIndoor.setAuto(channels5GUpperList);
    channels5GUpper.setIndoor(channels5GUpperIndoor);
    Channels5GIndoorOutdoor channels5GUpperOutdoor = new Channels5GIndoorOutdoor();
    channels5GUpperOutdoor.setAuto(channels5GUpperList);
    channels5GUpper.setOutdoor(channels5GUpperOutdoor);
    mockChannels.set_5GUpperChannels(channels5GUpper);

    Channels6G channels6G = new Channels6G();
    Channels6GIndoorOutdoor channels6GIndoor = new Channels6GIndoorOutdoor();
    List<Channel6GEnum> channels6GList = Arrays.asList(Channel6GEnum._1, Channel6GEnum._5, Channel6GEnum._9, Channel6GEnum._13,
        Channel6GEnum._17, Channel6GEnum._21, Channel6GEnum._25, Channel6GEnum._29, Channel6GEnum._33, Channel6GEnum._37,
        Channel6GEnum._41, Channel6GEnum._45, Channel6GEnum._49, Channel6GEnum._53, Channel6GEnum._57, Channel6GEnum._61,
        Channel6GEnum._65, Channel6GEnum._69, Channel6GEnum._73, Channel6GEnum._77, Channel6GEnum._81, Channel6GEnum._85,
        Channel6GEnum._89, Channel6GEnum._93, Channel6GEnum._97, Channel6GEnum._101, Channel6GEnum._105, Channel6GEnum._109);
    channels6GIndoor.setAuto(channels6GList);
    channels6G.setIndoor(channels6GIndoor);
    mockChannels.set_6GChannels(channels6G);

    // When
    com.ruckus.cloud.wifi.eda.servicemodel.RadioParams24G fake24G =
            mockRadioParams24G(TxPowerEnum.MAX, ChannelBandwidth24GEnum.AUTO);
    venue.getRadioCustomization().setRadioParams24G(fake24G);

    List<Operation> operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
            emptyTxChanges());

    // Then
    assertEquals(1, operations.get(0).getVenue().getRadioCustomization().getRadio24().getChannel());
    assertEquals(channels24GList.stream().map(e -> Integer.parseInt(e.toString())).toList(),
        operations.get(0).getVenue().getRadioCustomization().getRadio24().getAllowedChannelListList());

    // When
    com.ruckus.cloud.wifi.eda.servicemodel.RadioParams50G fake5G =
            mockRadioParams50G(true, TxPowerEnum.MAX, ChannelBandwidth5GEnum.AUTO);
    venue.getRadioCustomization().setRadioParams50G(fake5G);

    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
            emptyTxChanges());

    // Then
    assertEquals(36, operations.get(0).getVenue().getRadioCustomization().getRadio50().getIndoorChannel());
    assertEquals(channels5GList.stream().map(e -> Integer.parseInt(e.toString())).toList(),
        operations.get(0).getVenue().getRadioCustomization().getRadio50().getAllowedIndoorChannelListList());
    assertEquals(40, operations.get(0).getVenue().getRadioCustomization().getRadio50().getChannel());
    assertEquals(channels5GList.stream().map(e -> Integer.parseInt(e.toString())).toList(),
        operations.get(0).getVenue().getRadioCustomization().getRadio50().getAllowedOutdoorChannelListList());

    // When
    com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G fakeDual5G =
            mockRadioParamsDual5GManual();
    venue.getRadioCustomization().setRadioParamsDual5G(fakeDual5G);

    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
            emptyTxChanges());

    // Then
    assertEquals(36, operations.get(0).getVenue().getRadioCustomization().getRadio50Lower().getIndoorChannel());
    assertEquals(channels5GLowerList.stream().map(e -> Integer.parseInt(e.toString())).toList(),
        operations.get(0).getVenue().getRadioCustomization().getRadio50Lower().getAllowedIndoorChannelListList());
    assertEquals(40, operations.get(0).getVenue().getRadioCustomization().getRadio50Lower().getChannel());
    assertEquals(channels5GLowerList.stream().map(e -> Integer.parseInt(e.toString())).toList(),
        operations.get(0).getVenue().getRadioCustomization().getRadio50Lower().getAllowedOutdoorChannelListList());
    assertEquals(149, operations.get(0).getVenue().getRadioCustomization().getRadio50Upper().getIndoorChannel());
    assertEquals(channels5GUpperList.stream().map(e -> Integer.parseInt(e.toString())).toList(),
        operations.get(0).getVenue().getRadioCustomization().getRadio50Upper().getAllowedIndoorChannelListList());
    assertEquals(153, operations.get(0).getVenue().getRadioCustomization().getRadio50Upper().getChannel());
    assertEquals(channels5GUpperList.stream().map(e -> Integer.parseInt(e.toString())).toList(),
        operations.get(0).getVenue().getRadioCustomization().getRadio50Upper().getAllowedOutdoorChannelListList());

    // When
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G fake6G =
            mockRadioParams6G(TxPower6GEnum.MAX, ChannelBandwidth6GEnum.AUTO);
    venue.getRadioCustomization().setRadioParams6G(fake6G);

    operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
            emptyTxChanges());

    // Then
    assertEquals(1, operations.get(0).getVenue().getRadioCustomization().getRadio60().getChannel());
    assertEquals(channels6GList.stream().map(e -> Integer.parseInt(e.toString())).toList(),
        operations.get(0).getVenue().getRadioCustomization().getRadio60().getAllowedIndoorChannelListList());
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_POE_OUT_MODE_SETTING_TOGGLE})
  public void testUpdateVenueAp_with_poe_out_mode() throws CapabilityNotFoundException {
    var capabilities = new Capabilities();
    var h670 = new CapabilitiesApModel();
    h670.setModel("H670");
    h670.setPoeOutModeCapabilities(List.of("802.3at","802.3af"));

    var lan1 = new CapabilitiesLanPort();
    lan1.setId("1");
    lan1.setDefaultType("TRUNK");
    var lan2 = new CapabilitiesLanPort();
    lan2.setId("2");
    lan2.setDefaultType("TRUNK");
    h670.setLanPorts(List.of(lan1, lan2));

    capabilities.setApModels(List.of(h670));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());

    // Given
    var venue = mockVenue();
    venue.setModelSpecificAttributes(mockH670(venue));
    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
            emptyTxChanges());
    // Then
    verify(firmwareCapabilityService, times(1)).getVenueCapabilities(any());

    var venueToVerify = operations.get(0).getVenue();
    assertEquals(1, venueToVerify.getVenueApModelsCount());
    assertEquals("PoeOutModeEnum_802_3at",venueToVerify.getVenueApModels(0).getPoeOutMode().toString());
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_POE_OUT_MODE_SETTING_TOGGLE})
  public void testUpdateVenueAp_without_poe_out_mode() throws CapabilityNotFoundException {
    var capabilities = new Capabilities();
    var h670 = new CapabilitiesApModel();
    h670.setModel("H670");
    h670.setPoeOutModeCapabilities(List.of("802.3at","802.3af"));

    var lan1 = new CapabilitiesLanPort();
    lan1.setId("1");
    lan1.setDefaultType("TRUNK");
    var lan2 = new CapabilitiesLanPort();
    lan2.setId("2");
    lan2.setDefaultType("TRUNK");
    h670.setLanPorts(List.of(lan1, lan2));

    capabilities.setApModels(List.of(h670));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());

    // Given
    var venue = mockVenue();
    venue.setModelSpecificAttributes(mockH670NotSupportPoe(venue));
    // When
    var operations = ddccmVenueOperationBuilder.build(new NewTxEntity<>(venue),
            emptyTxChanges());
    // Then
    verify(firmwareCapabilityService, times(1)).getVenueCapabilities(any());

    var venueToVerify = operations.get(0).getVenue();
    assertEquals(1, venueToVerify.getVenueApModelsCount());
    assertEquals("PoeOutModeEnum_UNSET",venueToVerify.getVenueApModels(0).getPoeOutMode().toString());
  }


  private void assertRadioParamsDual5G(
      com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G expectDual5G,
      com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization radio) {
    assertRadioParams50G(expectDual5G.getRadioParamsLower5G(), radio.getRadio50Lower());
    assertRadioParams50G(expectDual5G.getRadioParamsUpper5G(), radio.getRadio50Upper());
  }

  private static @NotNull List<ApFirmwareUpgradeRequest> getApFirmwareUpgradeRequests(String venueId) {
    ApFirmwareUpgradeRequest req1 = new ApFirmwareUpgradeRequest();
    req1.setSerialNumber("ap1");
    req1.setSourceVersion("6.2.4.103.259");
    req1.setTargetVersion("6.2.4.103.259");
    req1.setVenueId(venueId);
    req1.setRequestId("req1");
    req1.setModel("R720");
    ApFirmwareUpgradeRequest req2 = new ApFirmwareUpgradeRequest();
    req2.setSerialNumber("ap2");
    req2.setSourceVersion("6.2.1.103.123");
    req2.setTargetVersion("6.2.1.103.123");
    req2.setVenueId(venueId);
    req2.setRequestId("req2");
    req2.setModel("R550");
    return new ArrayList<>(List.of(req1, req2));
  }

  private void assertRadioParam6G(com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G expect60,
      com.ruckus.acx.ddccm.protobuf.wifi.VenueRadio60 radio60) {
    assertEquals(expect60.getTxPower().toString().toLowerCase(), radio60.getTxPower());

    if (expect60.getChannelBandwidth() == ChannelBandwidth6GEnum.AUTO) {
      assertEquals("Auto", radio60.getChannelBandWidth());
    } else {
      assertEquals(expect60.getChannelBandwidth().toString(), radio60.getChannelBandWidth());
    }
    assertEquals(expect60.getAllowedIndoorChannels().stream()
            .map(e -> Integer.parseInt(e.toString())).collect(Collectors.toList()),
        radio60.getAllowedIndoorChannelListList());

    if (expect60.getAllowedOutdoorChannels() != null) {
      assertEquals(expect60.getAllowedOutdoorChannels().size(),
          radio60.getAllowedOutdoorChannelListCount());
      assertEquals(
          expect60.getAllowedOutdoorChannels().stream().map(e -> Integer.parseInt(e.toString())).toList(),
          radio60.getAllowedOutdoorChannelListList());
    } else {
      assertEquals(0, radio60.getAllowedOutdoorChannelListCount());
    }

    switch (expect60.getMethod()) {
      case CHANNELFLY:
        assertEquals("ChannelFly", radio60.getChannelSelectionMethod().name());
        assertEquals(1, radio60.getBgScan());
        assertEquals((int) expect60.getChangeInterval(), radio60.getChannelChangeFrequency());
        assertEquals((int) expect60.getScanInterval(), radio60.getBgScanTimer().getValue());
        break;
      case BACKGROUND_SCANNING:
        assertEquals("BackgroundScanning", radio60.getChannelSelectionMethod().name());
        assertEquals(1, radio60.getBgScan());
        assertEquals((int) expect60.getScanInterval(), radio60.getBgScanTimer().getValue());
        break;
      case MANUAL:
        assertEquals("Manual", radio60.getChannelSelectionMethod().name());
        assertEquals(1, radio60.getBgScan());
        assertEquals((int) expect60.getScanInterval(), radio60.getBgScanTimer().getValue());
        break;
      default:
        assertEquals(0, radio60.getBgScan());
    }
  }

  private void assertRadioParams50G(
      com.ruckus.cloud.wifi.eda.servicemodel.AbstractRadioParams50G expect50,
      com.ruckus.acx.ddccm.protobuf.wifi.VenueRadio50 radio50) {
    if (expect50.getTxPower() == TxPowerEnum.Auto) {
      assertTrue(radio50.getAutoCellSizing());
    } else {
      assertEquals(expect50.getTxPower().toString().toLowerCase(), radio50.getTxPower());
    }

    if (expect50.getChannelBandwidth() == ChannelBandwidth5GEnum.AUTO) {
      assertEquals("Auto", radio50.getChannelBandWidth());
    } else {
      assertEquals(expect50.getChannelBandwidth().toString(), radio50.getChannelBandWidth());
    }

    if (expect50.getAllowedIndoorChannels() != null) {
      assertEquals(expect50.getAllowedIndoorChannels().stream()
              .map(e -> Integer.parseInt(e.toString())).collect(Collectors.toList()),
          radio50.getAllowedIndoorChannelListList());
    }

    if (expect50.getAllowedOutdoorChannels() != null) {
      assertEquals(expect50.getAllowedOutdoorChannels().stream()
              .map(e -> Integer.parseInt(e.toString())).collect(Collectors.toList()),
          radio50.getAllowedOutdoorChannelListList());
    }

    switch (expect50.getMethod()) {
      case CHANNELFLY:
        assertEquals("ChannelFly", radio50.getChannelSelectionMethod().name());
        assertEquals(1, radio50.getBgScan());
        assertEquals((int) expect50.getChangeInterval(), radio50.getChannelChangeFrequency());
        assertEquals((int) expect50.getScanInterval(), radio50.getBgScanTimer().getValue());
        break;
      case BACKGROUND_SCANNING:
        assertEquals("BackgroundScanning", radio50.getChannelSelectionMethod().name());
        assertEquals(1, radio50.getBgScan());
        assertEquals((int) expect50.getScanInterval(), radio50.getBgScanTimer().getValue());
        break;
      case MANUAL:
        assertEquals("Manual", radio50.getChannelSelectionMethod().name());
        assertEquals(1, radio50.getBgScan());
        assertEquals((int) expect50.getScanInterval(), radio50.getBgScanTimer().getValue());
        break;
      default:
        assertEquals(0, radio50.getBgScan());
    }
  }

  private void assertRadioParams24G(com.ruckus.cloud.wifi.eda.servicemodel.RadioParams24G expect24,
      com.ruckus.acx.ddccm.protobuf.wifi.VenueRadio24 radio24) {
    if (expect24.getTxPower() == TxPowerEnum.Auto) {
      assertTrue(radio24.getAutoCellSizing());
    } else {
      assertEquals(expect24.getTxPower().toString().toLowerCase(), radio24.getTxPower());
    }

    if (expect24.getChannelBandwidth() == ChannelBandwidth24GEnum.AUTO) {
      assertEquals("Auto", radio24.getChannelBandWidth());
    } else {
      assertEquals(expect24.getChannelBandwidth().toString(), radio24.getChannelBandWidth());
    }

    assertEquals(expect24.getAllowedChannels().stream()
            .map(e -> Integer.parseInt(e.toString())).collect(Collectors.toList()),
        radio24.getAllowedChannelListList());

    switch (expect24.getMethod()) {
      case CHANNELFLY:
        assertEquals("ChannelFly", radio24.getChannelSelectionMethod().name());
        assertEquals(1, radio24.getBgScan());
        assertEquals((int) expect24.getChangeInterval(), radio24.getChannelChangeFrequency());
        assertEquals((int) expect24.getScanInterval(), radio24.getBgScanTimer().getValue());
        break;
      case BACKGROUND_SCANNING:
        assertEquals("BackgroundScanning", radio24.getChannelSelectionMethod().name());
        assertEquals(1, radio24.getBgScan());
        assertEquals((int) expect24.getScanInterval(), radio24.getBgScanTimer().getValue());
        break;
      case MANUAL:
        assertEquals("Manual", radio24.getChannelSelectionMethod().name());
        assertEquals(1, radio24.getBgScan());
        assertEquals((int) expect24.getScanInterval(), radio24.getBgScanTimer().getValue());
        break;
      default:
        assertEquals(0, radio24.getBgScan());
    }
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.Mesh mockMeshOptions(boolean enableMesh) {
    com.ruckus.cloud.wifi.eda.servicemodel.Mesh fakeMesh = new com.ruckus.cloud.wifi.eda.servicemodel.Mesh();
    fakeMesh.setEnabled(enableMesh);
    if (enableMesh) {
      fakeMesh.setPassphrase("12345678");
      fakeMesh.setSsid("mock-mesh-ssid");
    }
    return fakeMesh;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.Venue mockVenue() {
    return mockVenue(TxPowerEnum.Auto);
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.Venue mockVenue(TxPowerEnum txPower) {
    com.ruckus.cloud.wifi.eda.servicemodel.Venue venue = new com.ruckus.cloud.wifi.eda.servicemodel.Venue(
        uuid());
    com.ruckus.cloud.wifi.eda.servicemodel.Tenant tenant = new com.ruckus.cloud.wifi.eda.servicemodel.Tenant(
        "9b9920ec0f9643a2b2399741c82923cd");
    tenant.setCSRCEnabled(true);
    tenant.setRecoveryPsk("1111222233334444");
    venue.setTenant(tenant);
    venue.setName("sunny_venue");
    venue.setApPassword("apPassword");
    venue.setBandBalancing(new BandBalancing());
    venue.setMesh(new com.ruckus.cloud.wifi.eda.servicemodel.Mesh());
    venue.setRadioCustomization(mockVenueRadioCustomization(txPower));
    venue.setTimezone("America/New_York");
    venue.setCountryCode("US");
    venue.setSyslog(mockVenueSyslog(false));
    venue.setModelSpecificAttributes(Collections.emptyList());
    com.ruckus.cloud.wifi.eda.servicemodel.ApVersion apVersion = new com.ruckus.cloud.wifi.eda.servicemodel.ApVersion(
        "6.0.0.1.118");
    venue.setWifiFirmwareVersion(apVersion);
    venue.setLteBandLockChannels(Collections.emptyList());
    return venue;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.DenialOfServiceProtection mockDenialOfServiceProtection() {
    com.ruckus.cloud.wifi.eda.servicemodel.DenialOfServiceProtection denialOfServiceProtection = new com.ruckus.cloud.wifi.eda.servicemodel.DenialOfServiceProtection();
    denialOfServiceProtection.setEnabled(true);
    denialOfServiceProtection.setBlockingPeriod((short) 60);
    denialOfServiceProtection.setFailThreshold((short) 5);
    denialOfServiceProtection.setCheckPeriod((short) 30);
    return denialOfServiceProtection;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp mockVenueRogueAp(
      boolean enableVenueRogueAp) {
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp venueRogueAp = new com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp();
    if (enableVenueRogueAp) {
      venueRogueAp.setEnabled(true);
      venueRogueAp.setReportThreshold((short) 80);
    } else {
      venueRogueAp.setEnabled(false);
      venueRogueAp.setReportThreshold((short) 0);
    }
    return venueRogueAp;
  }

  private List<com.ruckus.cloud.wifi.eda.servicemodel.LteBandLockChannel> mockLteBandLockChannelsSettings() {
    com.ruckus.cloud.wifi.eda.servicemodel.LteBandLockChannel firstCard = new com.ruckus.cloud.wifi.eda.servicemodel.LteBandLockChannel();
    firstCard.setSimCardId(0);
    firstCard.setRegion(LteBandRegionEnum.DOMAIN_2);
    firstCard.setBand3G(Arrays.asList("B1", "B2"));
    firstCard.setBand4G(Arrays.asList("B1", "B2", "B3", "B4"));

    com.ruckus.cloud.wifi.eda.servicemodel.LteBandLockChannel secondCard = new com.ruckus.cloud.wifi.eda.servicemodel.LteBandLockChannel();
    secondCard.setSimCardId(1);
    secondCard.setRegion(LteBandRegionEnum.DOMAIN_2);
    secondCard.setBand3G(Arrays.asList("B1", "B2"));
    secondCard.setBand4G(Arrays.asList("B1", "B2", "B3", "B4", "B5"));
    return Arrays.asList(firstCard, secondCard);
  }

  private String uuid() {
    return UUID.randomUUID().toString().replace("-", "");
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioCustomization mockVenueRadioCustomization(TxPowerEnum txPower) {
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioCustomization radioCustomization = new com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioCustomization();
    com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G radioParamsDual5G = new com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G();
    radioParamsDual5G.setEnabled(false);
    radioCustomization.setRadioParamsDual5G(radioParamsDual5G);
    radioCustomization.setRadioParams24G(mockRadioParams24G(ScanMethodEnum.CHANNELFLY,
        txPower, ChannelBandwidth24GEnum.AUTO));
    radioCustomization.setRadioParams50G(mockRadioParams50G(true, ScanMethodEnum.CHANNELFLY,
        txPower, ChannelBandwidth5GEnum.AUTO));
    return radioCustomization;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.RadioParams24G mockRadioParams24G(
      ScanMethodEnum method,
      TxPowerEnum txPower,
      ChannelBandwidth24GEnum channelBandwidth) {
    com.ruckus.cloud.wifi.eda.servicemodel.RadioParams24G radioParams24G = new com.ruckus.cloud.wifi.eda.servicemodel.RadioParams24G();
    radioParams24G.setChangeInterval((short) 35);
    radioParams24G.setScanInterval(1);
    radioParams24G.setMethod(method);
    radioParams24G.setAllowedChannels(Arrays.asList(Channel24Enum._1, Channel24Enum._3));
    radioParams24G.setChannelBandwidth(channelBandwidth);
    radioParams24G.setTxPower(txPower);
    return radioParams24G;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.RadioParams24G mockRadioParams24G(
          TxPowerEnum txPower,
          ChannelBandwidth24GEnum channelBandwidth) {
    com.ruckus.cloud.wifi.eda.servicemodel.RadioParams24G radioParams24G = new com.ruckus.cloud.wifi.eda.servicemodel.RadioParams24G();
    radioParams24G.setChangeInterval((short) 35);
    radioParams24G.setScanInterval(1);
    radioParams24G.setMethod(ScanMethodEnum.MANUAL);
    radioParams24G.setAllowedChannels(Arrays.asList(Channel24Enum._1));
    radioParams24G.setChannelBandwidth(channelBandwidth);
    radioParams24G.setTxPower(txPower);
    return radioParams24G;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.RadioParams50G mockRadioParams50G(boolean enable5G,
      ScanMethodEnum method,
      TxPowerEnum txPower,
      ChannelBandwidth5GEnum channelBandwidth) {
    com.ruckus.cloud.wifi.eda.servicemodel.RadioParams50G radioParams50G = new com.ruckus.cloud.wifi.eda.servicemodel.RadioParams50G();
    radioParams50G.setChangeInterval((short) 35);
    radioParams50G.setScanInterval(1);
    radioParams50G.setMethod(method);
    if (enable5G) {
      radioParams50G.setAllowedIndoorChannels(Arrays.asList(Channel50Enum._36, Channel50Enum._40));
      radioParams50G.setAllowedOutdoorChannels(Arrays.asList(Channel50Enum._36, Channel50Enum._40));
    }
    radioParams50G.setChannelBandwidth(channelBandwidth);
    radioParams50G.setTxPower(txPower);
    radioParams50G.setCombineChannels(false);
    return radioParams50G;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.RadioParams50G mockRadioParams50G(boolean enable5G,
                                                                                   TxPowerEnum txPower,
                                                                                   ChannelBandwidth5GEnum channelBandwidth) {
    com.ruckus.cloud.wifi.eda.servicemodel.RadioParams50G radioParams50G = new com.ruckus.cloud.wifi.eda.servicemodel.RadioParams50G();
    radioParams50G.setChangeInterval((short) 35);
    radioParams50G.setScanInterval(1);
    radioParams50G.setMethod(ScanMethodEnum.MANUAL);
    if (enable5G) {
      radioParams50G.setAllowedIndoorChannels(Arrays.asList(Channel50Enum._36));
      radioParams50G.setAllowedOutdoorChannels(Arrays.asList(Channel50Enum._40));
    }
    radioParams50G.setChannelBandwidth(channelBandwidth);
    radioParams50G.setTxPower(txPower);
    radioParams50G.setCombineChannels(false);
    return radioParams50G;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G mockRadioParamsDual5G() {
    com.ruckus.cloud.wifi.eda.servicemodel.Dual5GRadioParams50G radioParamsLower50G = new com.ruckus.cloud.wifi.eda.servicemodel.Dual5GRadioParams50G();
    radioParamsLower50G.setChangeInterval((short) 33);
    radioParamsLower50G.setMethod(ScanMethodEnum.CHANNELFLY);
    radioParamsLower50G.setScanInterval(20);
    radioParamsLower50G.setChannelBandwidth(ChannelBandwidth5GEnum._160MHz);
    radioParamsLower50G.setTxPower(TxPowerEnum._4);
    radioParamsLower50G.setAllowedIndoorChannels(
        Arrays.asList(Channel50Enum._36, Channel50Enum._40));
    radioParamsLower50G.setAllowedOutdoorChannels(
        Arrays.asList(Channel50Enum._36, Channel50Enum._40));

    com.ruckus.cloud.wifi.eda.servicemodel.Dual5GRadioParams50G radioParamsUpper50G = new com.ruckus.cloud.wifi.eda.servicemodel.Dual5GRadioParams50G();
    radioParamsUpper50G.setChangeInterval((short) 33);
    radioParamsUpper50G.setMethod(ScanMethodEnum.CHANNELFLY);
    radioParamsUpper50G.setScanInterval(20);
    radioParamsUpper50G.setChannelBandwidth(ChannelBandwidth5GEnum._160MHz);
    radioParamsUpper50G.setTxPower(TxPowerEnum._4);
    radioParamsUpper50G.setAllowedIndoorChannels(
        Arrays.asList(Channel50Enum._100, Channel50Enum._104));
    radioParamsUpper50G.setAllowedOutdoorChannels(
        Arrays.asList(Channel50Enum._100, Channel50Enum._104));

    com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G radioParamsDual5G = new com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G();
    radioParamsDual5G.setEnabled(true);
    radioParamsDual5G.setRadioParamsLower5G(radioParamsLower50G);
    radioParamsDual5G.setRadioParamsUpper5G(radioParamsUpper50G);
    return radioParamsDual5G;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G mockRadioParamsDual5GManual() {
    com.ruckus.cloud.wifi.eda.servicemodel.Dual5GRadioParams50G radioParamsLower50G = new com.ruckus.cloud.wifi.eda.servicemodel.Dual5GRadioParams50G();
    radioParamsLower50G.setChangeInterval((short) 33);
    radioParamsLower50G.setMethod(ScanMethodEnum.MANUAL);
    radioParamsLower50G.setScanInterval(20);
    radioParamsLower50G.setChannelBandwidth(ChannelBandwidth5GEnum.AUTO);
    radioParamsLower50G.setTxPower(TxPowerEnum._4);
    radioParamsLower50G.setAllowedIndoorChannels(Arrays.asList(Channel50Enum._36));
    radioParamsLower50G.setAllowedOutdoorChannels(Arrays.asList(Channel50Enum._40));

    com.ruckus.cloud.wifi.eda.servicemodel.Dual5GRadioParams50G radioParamsUpper50G = new com.ruckus.cloud.wifi.eda.servicemodel.Dual5GRadioParams50G();
    radioParamsUpper50G.setChangeInterval((short) 33);
    radioParamsUpper50G.setMethod(ScanMethodEnum.MANUAL);
    radioParamsUpper50G.setScanInterval(20);
    radioParamsUpper50G.setChannelBandwidth(ChannelBandwidth5GEnum.AUTO);
    radioParamsUpper50G.setTxPower(TxPowerEnum._4);
    radioParamsUpper50G.setAllowedIndoorChannels(Arrays.asList(Channel50Enum._149));
    radioParamsUpper50G.setAllowedOutdoorChannels(Arrays.asList(Channel50Enum._153));

    com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G radioParamsDual5G = new com.ruckus.cloud.wifi.eda.servicemodel.RadioParamsDual5G();
    radioParamsDual5G.setEnabled(true);
    radioParamsDual5G.setRadioParamsLower5G(radioParamsLower50G);
    radioParamsDual5G.setRadioParamsUpper5G(radioParamsUpper50G);
    return radioParamsDual5G;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G mockRadioParams6G(
      ScanMethodEnum method, TxPower6GEnum txPower, ChannelBandwidth6GEnum channelBandwidth) {
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G radioParams6G =
        new com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G();
    radioParams6G.setChangeInterval((short) 33);
    radioParams6G.setMethod(method);
    radioParams6G.setScanInterval(20);
    radioParams6G.setChannelBandwidth(channelBandwidth);
    radioParams6G.setAllowedIndoorChannels(Arrays.asList(Channel6GEnum._1, Channel6GEnum._5));
    radioParams6G.setAllowedOutdoorChannels(Arrays.asList(Channel6GEnum._21, Channel6GEnum._25));
    radioParams6G.setTxPower(txPower);
    radioParams6G.setBssMinRate6G(BssMinRate6GEnum.HE_MCS_1);
    radioParams6G.setMgmtTxRate6G(MgmtTxRate6GEnum._12);
    radioParams6G.setEnableAfc(true);

    return radioParams6G;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G mockRadioParams6G(TxPower6GEnum txPower, ChannelBandwidth6GEnum channelBandwidth) {
    com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G radioParams6G =
            new com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G();
    radioParams6G.setChangeInterval((short) 33);
    radioParams6G.setMethod(ScanMethodEnum.MANUAL);
    radioParams6G.setScanInterval(20);
    radioParams6G.setChannelBandwidth(channelBandwidth);
    radioParams6G.setAllowedIndoorChannels(Arrays.asList(Channel6GEnum._1));
    radioParams6G.setTxPower(txPower);
    radioParams6G.setBssMinRate6G(BssMinRate6GEnum.HE_MCS_1);
    radioParams6G.setMgmtTxRate6G(MgmtTxRate6GEnum._12);
    radioParams6G.setEnableAfc(true);

    return radioParams6G;
  }

  private com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslog mockVenueSyslog(boolean enableSyslog) {
    com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslog venueSyslog = new com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslog();
    venueSyslog.setEnabled(enableSyslog);
    if (enableSyslog) {
      venueSyslog.setPort(514);
      venueSyslog.setServer("1.1.1.1");
      venueSyslog.setProtocol(ProtocolEnum.UDP);
      venueSyslog.setSecondaryServer("1.1.1.2");
      venueSyslog.setSecondaryPort(515);
      venueSyslog.setFacility(FacilityEnum.KEEP_ORIGINAL);
      venueSyslog.setSecondaryProtocol(ProtocolEnum.TCP);
    }
    return venueSyslog;
  }

  private List<VenueApModelSpecificAttributes> mockDisableR610SecondLanPort(Venue venue) {
    // mock R610 disable second port
    var attributes = new VenueApModelSpecificAttributes(uuid());
    attributes.setTenant(venue.getTenant());
    attributes.setVenue(venue);
    attributes.setModel("R610");
    // R610 PoE default value
    attributes.setPoeMode(PoeModeEnum.Auto);
    attributes.setPoeOut(null);
    var lanPort1 = new VenueLanPort(uuid());
    lanPort1.setTenant(venue.getTenant());
    lanPort1.setVenueApModelSpecificAttributes(attributes);
    lanPort1.setEnabled(true);
    lanPort1.setPortId("1");
    var profile1 = new EthernetPortProfile(uuid());
    profile1.setTenant(venue.getTenant());
    profile1.setApLanPortId(1);
    profile1.setType(ApLanPortTypeEnum.TRUNK);
    profile1.setUntagId((short) 1);
    profile1.setVlanMembers("1-4094");
    lanPort1.setApLanPortProfile(profile1);
    var lanPort2 = new VenueLanPort(uuid());
    lanPort2.setTenant(venue.getTenant());
    lanPort2.setVenueApModelSpecificAttributes(attributes);
    lanPort2.setEnabled(false);
    lanPort2.setPortId("2");
    var profile2 = new EthernetPortProfile(uuid());
    profile2.setTenant(venue.getTenant());
    profile2.setApLanPortId(2);
    profile2.setType(ApLanPortTypeEnum.TRUNK);
    profile2.setUntagId((short) 1);
    profile2.setVlanMembers("1-4094");
    lanPort2.setApLanPortProfile(profile2);
    attributes.setLanPorts(List.of(lanPort1, lanPort2));
    return List.of(attributes);
  }

  private List<VenueApModelSpecificAttributes> mockDisableT670SN(Venue venue) {
    // mock R610 disable second port
    var attributes = new VenueApModelSpecificAttributes(uuid());
    attributes.setTenant(venue.getTenant());
    attributes.setVenue(venue);
    attributes.setModel("T670SN");

    attributes.setPoeMode(PoeModeEnum.Auto);
    attributes.setPoeOut(null);
    attributes.setBandMode(BandModeEnum.TRIPLE);
    attributes.setAntennaType(com.ruckus.cloud.wifi.eda.servicemodel.enums.AntennaTypeEnum.NARROW);

    return List.of(attributes);
  }

  private List<VenueApModelSpecificAttributes> mockH670(Venue venue) {
    var attributes = new VenueApModelSpecificAttributes(uuid());
    attributes.setTenant(venue.getTenant());
    attributes.setVenue(venue);
    attributes.setModel("H670");

    attributes.setPoeMode(PoeModeEnum.Auto);
    attributes.setPoeOut(true);
    attributes.setPoeOutMode(PoeOutModeEnum._802_3at);

    return List.of(attributes);
  }

  private List<VenueApModelSpecificAttributes> mockH670NotSupportPoe(Venue venue) {
    var attributes = new VenueApModelSpecificAttributes(uuid());
    attributes.setTenant(venue.getTenant());
    attributes.setVenue(venue);
    attributes.setModel("H670");

    attributes.setPoeMode(PoeModeEnum.Auto);
    attributes.setPoeOut(true);

    return List.of(attributes);
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    public DdccmVenueApModelOperationBuilder ddccmVenueApModelOperationBuilder() {
      return new DdccmVenueApModelOperationBuilder();
    }

    @Bean
    public DdccmVenueOperationBuilder ddccmVenueOperationBuilder() {
      return new DdccmVenueOperationBuilder();
    }

    @Bean
    public VenueRepository mockVenueRepository() {
      return Mockito.mock(VenueRepository.class);
    }

    @Bean
    public SoftGreProfileRepository
        mockSoftGreProfileRepository() {
      return Mockito.mock(SoftGreProfileRepository.class);
    }

    @Bean
    public ApFirmwareUpgradeRequestRepository
    mockApFirmwareUpgradeRequestRepository() {
      return Mockito.mock(ApFirmwareUpgradeRequestRepository.class);
    }
  }
}
