package com.ruckus.cloud.wifi.integration.durga;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.proto.mapper.WifiProtoMapper;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.proto.PredefinedAaaJob;
import com.ruckus.cloud.wifi.proto.PredefinedAaaJobEnum;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumePredefinedAaaAsyncRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  @Test
  void testConsumePredefinedAaaServiceAsyncJob() {
    Tenant tenant = TenantTestFixture.randomTenant(
        c -> c.setPredefinedAaaServices(List.of("dpsk-AAA")));
    repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId());

    var service = new com.ruckus.cloud.wifi.eda.servicemodel.DurgaPredefinedAaaModel();
    service.setName("DPSK");
    service.setRegions(new ArrayList<>());
    var region = new com.ruckus.cloud.wifi.eda.servicemodel.DurgaPredefinedAaaRegion();
    service.getRegions().add(region);
    region.setName("DPSK");
    region.setAaaName("dpsk-AAA");
    var authRadius = new com.ruckus.cloud.wifi.eda.servicemodel.DurgaExternalAuthRadius();
    region.setAuthRadius(authRadius);
    var tupple = new com.ruckus.cloud.wifi.eda.servicemodel.DurgaExternalRadiusTupple();
    authRadius.setPrimary(tupple);
    authRadius.setSecondary(tupple);
    tupple.setIp("*******");
    tupple.setPort("1234");
    tupple.setSharedSecret("SharedSecret");
    authRadius.setCnSanIdentity("cnSanIdentity");

    var job = PredefinedAaaJob.newBuilder()
        .setJobEnum(PredefinedAaaJobEnum.PredefinedAaaJobEnum_UPDATE_DPSK_SERVICE)
        .setPredefinedAaaModel(WifiProtoMapper.INSTANCE.map(service))
        .build();
    var request = com.ruckus.cloud.wifi.proto.WifiAsyncJob.newBuilder()
        .setPredefinedAaaJob(job)
        .build();
    var requestId = randomTxId();

    messageUtil.sendAsyncJob(
        tenant.getId(),
        requestId,
        request);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
            .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage).isNotNull();
  }
}
