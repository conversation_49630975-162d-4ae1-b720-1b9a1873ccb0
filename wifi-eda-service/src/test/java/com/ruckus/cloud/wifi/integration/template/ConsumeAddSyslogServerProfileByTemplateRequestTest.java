package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_SYSLOG_SERVER_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.SYSLOG_SERVER_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.TemplateCreateInstanceOperation.newViewProperty;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.service.VenueTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslogServerProfile;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.SyslogServerProfileRepository;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.SyslogServerProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddSyslogServerProfileByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private SyslogServerProfileRepository syslogServerProfileRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;
  @Autowired
  protected ExtendedVenueServiceCtrl venueServiceCtrl;
  @Autowired
  private VenueTemplateServiceCtrl venueTemplateServiceCtrl;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void add_syslogServerProfile(Tenant mspTenant, @Template SyslogServerProfile profile) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // check inserted into DB
    SyslogServerProfile profileTemplateFromDB =
        repositoryUtil.find(SyslogServerProfile.class, profile.getId(), mspTenant.getId(), true);
    assertSyslogServerProfile(profile, profileTemplateFromDB);

    // create ec syslogServerProfile set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_SYSLOG_SERVER_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.SYSLOG_SERVER_PROFILE, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, profileTemplateFromDB.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE,
        ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_SYSLOG_SERVER_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_SYSLOG_SERVER_PROFILE, ecTenantId, List.of(instanceId));
    assertActivityStatusSuccess(ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE, mspTenantId);

    SyslogServerProfile ecProfile = syslogServerProfileRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecProfile.getId());
    assertEquals(profileTemplateFromDB.getId(), ecProfile.getTemplateId());
    assertEquals(profileTemplateFromDB.getUpdatedDate().getTime(), ecProfile.getTemplateVersion());

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert 1 viewmodel ops: SyslogServerProfile",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, SYSLOG_SERVER_PROFILE_INDEX_NAME,
            ecProfile.getId(), ecProfile.getName())
    );
  }

  @Test
  public void ec_add_syslog_server_fail_then_msp_activity_should_fail(Tenant mspTenant, @Template SyslogServerProfile profile) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add SyslogServerProfile Template
    profile.setName("test-profile");
    repositoryUtil.createOrUpdate(profile, mspTenant.getId(), randomTxId());

    // check inserted into DB
    SyslogServerProfile profileTemplateFromDB =
        repositoryUtil.find(SyslogServerProfile.class, profile.getId(), mspTenant.getId(), true);
    assertSyslogServerProfile(profile, profileTemplateFromDB);

    // profile already in ec tenant before apply template
    SyslogServerProfile existedEcProfile = SyslogServerProfileTestFixture.randomSyslogServerProfile();
    existedEcProfile.setName("test-profile");
    repositoryUtil.createOrUpdate(existedEcProfile, ecTenant.getId(), randomTxId());

    // create ec syslogServerProfile set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_SYSLOG_SERVER_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.SYSLOG_SERVER_PROFILE, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, profileTemplateFromDB.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE,
        ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_SYSLOG_SERVER_PROFILE, ecTenantId);
    assertActivityStatusFail(ADD_SYSLOG_SERVER_PROFILE, ecTenantId);
    assertActivityStatusFail(ApiFlowNames.ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void ec_add_syslog_server_fail_msp_should_get_activity_fail_because_incorrect_overrides(Tenant mspTenant,
      @Template SyslogServerProfile profile) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add SyslogServerProfile Template
    profile.setName("test-profile");
    repositoryUtil.createOrUpdate(profile, mspTenant.getId(), randomTxId());

    // check inserted into DB
    SyslogServerProfile profileTemplateFromDB =
        repositoryUtil.find(SyslogServerProfile.class, profile.getId(), mspTenant.getId(), true);
    assertSyslogServerProfile(profile, profileTemplateFromDB);

    // create ec syslogServerProfile set by msp template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    instanceCreateRequest.setOverrides(List.of(newViewProperty("x", "x"))); // incorrect

    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_SYSLOG_SERVER_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.SYSLOG_SERVER_PROFILE, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, profileTemplateFromDB.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE,
        ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE, userName, requestParams, instanceCreateRequest);

    assertActivityPlanNotSent(ecTenantId);
    assertActivityStatusFail(ApiFlowNames.ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  @Test
  void apply_venue_with_syslog_server_profile(Tenant mspTenant, @Template Venue venue, @Template SyslogServerProfile profile) throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var venueId = venue.getId();
    // activate syslog server profile in venue
    VenueSyslogServerProfile venueSyslog = new VenueSyslogServerProfile();
    venueSyslog.setEnabled(true);
    venueSyslog.setServiceProfileId(profile.getId());

    RequestParams rps = new RequestParams().addPathVariable("venueTemplateId", venueId);
    rps.addPathVariable("syslogServerProfileTemplateId", profile.getId());
    sendWifiCfgRequest(mspTenantId, randomTxId(), CfgAction.ACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE,
        userName, rps, venueSyslog);
    assertActivityStatusSuccess(ACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE, mspTenantId);

    VenueSyslogServerProfile venueSyslogServerProfile = venueTemplateServiceCtrl.getVenueTemplateSyslogServerProfileSettings(venueId);
    assertTrue(venueSyslogServerProfile.getEnabled());
    assertEquals(profile.getId(), venueSyslogServerProfile.getServiceProfileId());

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(mspTenant.getLatestReleaseVersion()));
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create ec syslogServerProfile set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_SYSLOG_SERVER_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.SYSLOG_SERVER_PROFILE, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, profile.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE,
        ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_SYSLOG_SERVER_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_SYSLOG_SERVER_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ApiFlowNames.ADD_SYSLOG_SERVER_PROFILE_BY_TEMPLATE, mspTenantId);

    clearMessage();

    // otherwise test can't get correct ec data
    changeTxCtxTenant(ecTenantId);

    // apply venue template to create ec venue and set venue-syslog
    String ecVenueId = randomId();
    var event = buildTemplateInstanceEvent(venue, ecVenueId, ecTenantId);
    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(ecTenant.getId(), requestId, event);

    assertActivityStatusSuccess("AddVenueByTemplateInWifi", ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST, ecTenantId);

    Venue ecVenue = repositoryUtil.find(Venue.class, ecVenueId);
    assertTrue(ecVenue.getSyslog().getEnabled());
    assertEquals(instanceId, ecVenue.getSyslogServerProfile().getId());
  }

  private static void assertViewmodelCollector(List<Operations> operations, OpType opType,
      String index, String id, String name) {
    assertTrue(operations.stream()
        .filter(o -> index.equals(o.getIndex()))
        .filter(o -> id.equals(o.getId()))
        .filter(o -> o.getOpType() == opType)
        .anyMatch(o -> name.equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())));
  }

  private void assertSyslogServerProfile(SyslogServerProfile expected, SyslogServerProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getIsTemplate(), actual.getIsTemplate());
    assertEquals(expected.getTenant().getId(), actual.getTenant().getId());
    for (int i = 0; i < expected.getVenues().size(); i++) {
      var venueExpected = expected.getVenues().get(i);
      var venueActual = actual.getVenues().get(i);
      assertEquals(venueExpected.getId(), venueActual.getId());
      assertEquals(venueExpected.getIsTemplate(), venueActual.getIsTemplate());
      assertEquals(venueExpected.getSyslog().getEnabled(), venueActual.getSyslog().getEnabled());
      assertEquals(venueExpected.getSyslogServerProfile().getId(), venueActual.getSyslogServerProfile().getId());
    }
  }

  private void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }
}
