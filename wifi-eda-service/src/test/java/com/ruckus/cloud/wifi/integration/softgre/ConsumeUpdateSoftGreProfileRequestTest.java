package com.ruckus.cloud.wifi.integration.softgre;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.SoftGreProfileRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SoftGreProfileTest")
@WifiIntegrationTest
class ConsumeUpdateSoftGreProfileRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeUpdateSoftGreProfileV1Message {

    @Test
    void givenProfileNotExist(Tenant tenant) {
      var request =
          SoftGreProfileRestCtrl.SoftGreProfileMapper.INSTANCE.ServiceSoftGreProfile2SoftGreProfile(
              randomSoftGreProfile());
      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.UPDATE_SOFT_GRE_PROFILE,
                      randomName(),
                      new RequestParams().addPathVariable("softGreProfileId", request.getId()),
                      request))
          .isNotNull()
          .getRootCause()
          .isNotNull()
          .isInstanceOf(ObjectNotFoundException.class);

      messageCaptors
          .assertThat(
              messageCaptors.getDdccmMessageCaptor(),
              messageCaptors.getCmnCfgCollectorMessageCaptor())
          .doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_SOFT_GRE_PROFILE));
    }

    @Test
    void givenValidProfile(Tenant tenant, NetworkVenue networkVenue) {
      var profile =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());

      var request =
          SoftGreProfileRestCtrl.SoftGreProfileMapper.INSTANCE.ServiceSoftGreProfile2SoftGreProfile(
              randomSoftGreProfile(
                  tenant, softGreProfile -> softGreProfile.setId(profile.getId())));

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.UPDATE_SOFT_GRE_PROFILE,
          randomName(),
          new RequestParams().addPathVariable("softGreProfileId", profile.getId()),
          request);

      // TODO: Add test cases for DDCCM

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.MOD)
          .matches(o -> o.getId().equals(profile.getId()))
          .matches(
              o ->
                  o.getDocMap()
                      .get(EsConstants.Key.ACTIVATIONS)
                      .getListValue()
                      .getValuesList()
                      .isEmpty());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_SOFT_GRE_PROFILE));
    }

    @Test
    void givenActivatedValidProfile(Tenant tenant, NetworkVenue networkVenue) {
      var profile =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      var activation = new SoftGreProfileNetworkVenueActivation();
      activation.setTenant(tenant);
      activation.setNetworkVenue(networkVenue);
      activation.setSoftGreProfile(profile);
      repositoryUtil.createOrUpdate(activation, tenant.getId(), randomTxId());

      var request =
          SoftGreProfileRestCtrl.SoftGreProfileMapper.INSTANCE.ServiceSoftGreProfile2SoftGreProfile(
              randomSoftGreProfile(
                  tenant, softGreProfile -> softGreProfile.setId(profile.getId())));

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.UPDATE_SOFT_GRE_PROFILE,
          randomName(),
          new RequestParams().addPathVariable("softGreProfileId", profile.getId()),
          request);

      // TODO: Add test cases for DDCCM

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.MOD)
          .matches(o -> o.getId().equals(profile.getId()))
          .matches(
              o ->
                  !o.getDocMap()
                      .get(EsConstants.Key.ACTIVATIONS)
                      .getListValue()
                      .getValuesList()
                      .isEmpty());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_SOFT_GRE_PROFILE));
    }
  }
}
