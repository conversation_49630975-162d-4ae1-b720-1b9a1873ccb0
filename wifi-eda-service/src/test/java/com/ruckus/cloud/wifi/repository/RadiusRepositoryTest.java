package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import java.util.Optional;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO radius (id, tenant, is_template)
        VALUES ('92cd017b19874c7bb2f401ca281ec729', '4c8279f79307415fa9e4c88a1819f0fc', 'true');
    INSERT INTO radius (id, tenant, is_template)
        VALUES ('8e64f1098a1041a4915b69a6bb2406dd', '4c8279f79307415fa9e4c88a1819f0fc', 'false');
    INSERT INTO radius (id, tenant, name, type, prim_ip, prim_port, sec_ip, sec_port, is_template)
        VALUES ('11111-1', '4c8279f79307415fa9e4c88a1819f0fc', 'auth-radius', 'AUTHENTICATION', '*******', '1812', '*******', '1812', 'true');
    INSERT INTO radius (id, tenant, name, type, prim_ip, prim_port, sec_ip, sec_port, is_template)
        VALUES ('11111-2', '4c8279f79307415fa9e4c88a1819f0fc', 'auth-radius', 'AUTHENTICATION', '*******', '1812', '*******', '1812', 'false');
    INSERT INTO radius (id, tenant, name, type, prim_ip, prim_port, sec_ip, sec_port, is_template)
            VALUES ('33333', '4c8279f79307415fa9e4c88a1819f0fc', 'my-radius', 'AUTHENTICATION', '*******', '1812', '*******', '1812', 'true');
    """)
public class RadiusRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";

  private static final String RADIUS_TEMPLATE_ID = "92cd017b19874c7bb2f401ca281ec729";

  private static final String RADIUS_NON_TEMPLATE_ID = "8e64f1098a1041a4915b69a6bb2406dd";

  @Autowired
  RadiusRepository radiusRepository;

  @Nested
  @ApplyTemplateFilter
  class InTemplateFlow {

    @Test
    void countByTenantIdAndIsTemplateTest() {
      assertEquals(3, radiusRepository.countByTenantId(TENANT_ID));
    }

    @Test
    void existsByTenantIdAndNameAndIsTemplateTest() {
      // name: auth-radius already in DB
      assertEquals(Boolean.TRUE,
          radiusRepository.existsByTenantIdAndName(TENANT_ID, "auth-radius"));

      // another name should return false (not found)
      assertEquals(Boolean.FALSE,
          radiusRepository.existsByTenantIdAndName(TENANT_ID, "another-radius"));
    }

    @Test
    void existsByTenantIdAndNameAndIsTemplateAndIdNotTest() {
      assertEquals(Boolean.FALSE,
          radiusRepository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "auth-radius", "11111-1"));

      assertEquals(Boolean.TRUE,
          radiusRepository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "auth-radius", "1234567"));
    }

    @Test
    void findByIdAndTenantIdTest() {
      Optional<Radius> radiusTemplate = radiusRepository.findByIdAndTenantId(
          RADIUS_TEMPLATE_ID,
          TENANT_ID);
      assertNotNull(radiusTemplate.get());
      assertEquals(true, radiusTemplate.get().getIsTemplate());

      Optional<Radius> radiusNonTemplate = radiusRepository.findByIdAndTenantId(
          RADIUS_NON_TEMPLATE_ID,
          TENANT_ID);
      assertThat(radiusNonTemplate).isEmpty();
    }

    @Test
    void findRadiusByTenantIdAndByPrimaryOrSecondaryTest() {
      // 11111-1
      Radius found = radiusRepository.findRadiusByTenantIdAndTypeAndByPrimaryOrSecondary(
          TENANT_ID, RadiusProfileTypeEnum.AUTHENTICATION, "*******", 1812, "*******", 1812);
      assertEquals("11111-1", found.getId());

      found = radiusRepository.findRadiusByTenantIdAndTypeAndByPrimaryOrSecondary(
          TENANT_ID, RadiusProfileTypeEnum.AUTHENTICATION, "*******", 1812, "*******", 1812);
      assertEquals("11111-1", found.getId());

      found = radiusRepository.findRadiusByTenantIdAndTypeAndByPrimaryOrSecondary(
          TENANT_ID, RadiusProfileTypeEnum.AUTHENTICATION, "*******", 1812, "*******", 1812);
      assertEquals("11111-1", found.getId());

      // not found
      found = radiusRepository.findRadiusByTenantIdAndTypeAndByPrimaryOrSecondary(
          TENANT_ID, RadiusProfileTypeEnum.AUTHENTICATION, "*******", 1812, "*******", 1812);
      assertNull(found);
    }

    @Test
    void findRadiusByTenantIdAndIsTemplateByPrimary() {
      Radius found = radiusRepository.findRadiusByTenantIdByPrimary(
          TENANT_ID, "*******", 1812);
      assertEquals("11111-1", found.getId());

      // not found
      found = radiusRepository.findRadiusByTenantIdByPrimary(
          TENANT_ID, "*******", 1812);
      assertNull(found);

      found = radiusRepository.findRadiusByTenantIdByPrimary(
          TENANT_ID, "*******", 1812);
      assertNull(found);

      found = radiusRepository.findRadiusByTenantIdByPrimary(
          TENANT_ID,  "*******", 1812);
      assertNull(found);
    }
  }

  @Nested
  class OutOfTemplateFlow {

    @Test
    void countByTenantIdAndIsTemplateTest() {
      assertEquals(2, radiusRepository.countByTenantId(TENANT_ID));
    }

    @Test
    void existsByTenantIdAndNameAndIsTemplateTest() {
      // name: auth-radius already in DB
      assertEquals(Boolean.TRUE,
          radiusRepository.existsByTenantIdAndName(TENANT_ID, "auth-radius"));

      // another name should return false (not found)
      assertEquals(Boolean.FALSE,
          radiusRepository.existsByTenantIdAndName(TENANT_ID, "another-radius"));
    }

    @Test
    void existsByTenantIdAndNameAndIsTemplateAndIdNotTest() {
      assertEquals(Boolean.FALSE,
          radiusRepository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "auth-radius", "11111-2"));

      assertEquals(Boolean.TRUE,
          radiusRepository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "auth-radius", "1234567"));
    }

    @Test
    void findByIdAndTenantIdTest() {
      Optional<Radius> radiusTemplate = radiusRepository.findByIdAndTenantId(
          RADIUS_TEMPLATE_ID,
          TENANT_ID);
      assertThat(radiusTemplate).isEmpty();

      Optional<Radius> radiusNonTemplate = radiusRepository.findByIdAndTenantId(
          RADIUS_NON_TEMPLATE_ID,
          TENANT_ID);
      assertNotNull(radiusNonTemplate.get());
      assertEquals(false, radiusNonTemplate.get().getIsTemplate());
    }

    @Test
    void findRadiusByTenantIdAndByPrimaryOrSecondaryTest() {
      // 11111-2
      Radius found = radiusRepository.findRadiusByTenantIdAndTypeAndByPrimaryOrSecondary(
          TENANT_ID, RadiusProfileTypeEnum.AUTHENTICATION, "*******", 1812, "*******", 1812);
      assertEquals("11111-2", found.getId());

      found = radiusRepository.findRadiusByTenantIdAndTypeAndByPrimaryOrSecondary(
          TENANT_ID, RadiusProfileTypeEnum.AUTHENTICATION, "*******", 1812, "*******", 1812);
      assertEquals("11111-2", found.getId());

      found = radiusRepository.findRadiusByTenantIdAndTypeAndByPrimaryOrSecondary(
          TENANT_ID, RadiusProfileTypeEnum.AUTHENTICATION, "*******", 1812, "*******", 1812);
      assertEquals("11111-2", found.getId());

      found = radiusRepository.findRadiusByTenantIdAndTypeAndByPrimaryOrSecondary(
          TENANT_ID, RadiusProfileTypeEnum.AUTHENTICATION, "*******", 1812, "*******", 1812);
      assertNull(found);
    }

    @Test
    void findRadiusByTenantIdAndIsTemplateByPrimary() {
      Radius found = radiusRepository.findRadiusByTenantIdByPrimary(
          TENANT_ID, "*******", 1812);
      assertEquals("11111-2", found.getId());

      found = radiusRepository.findRadiusByTenantIdByPrimary(
          TENANT_ID, "*******", 1812);
      assertNull(found);

      found = radiusRepository.findRadiusByTenantIdByPrimary(
          TENANT_ID, "*******", 1812);
      assertNull(found);

      found = radiusRepository.findRadiusByTenantIdByPrimary(
          TENANT_ID, "*******", 1812);
      assertNull(found);
    }
  }
}
