package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.conf.WifiBaseEntityFieldNames;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesImpl;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.template.TemplateManagementService;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.Collections;
import java.util.Set;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;

@WifiUnitTest
class Hotspot20NetworkCfgCollectorOperationBuilderTest {

  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private TemplateManagementService templateManagementService;
  @SpyBean
  private Hotspot20NetworkCfgCollectorOperationBuilder hotspot20NetworkCfgCollectorOperationBuilder;

  @Test
  void testGetEntityClass() {
    assertThat(new Hotspot20NetworkCfgCollectorOperationBuilder().entityClass())
        .isEqualTo(Hotspot20Network.class);
  }

  @Test
  void testHasChanged() {
    hotspot20NetworkCfgCollectorOperationBuilder.setBaseEntityFieldNames(new WifiBaseEntityFieldNames());

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    var vlanPool = Generators.vlanPool().generate();
    hotspot20Network.getWlan().getAdvancedCustomization().setVlanPool(vlanPool);

    // Network and VlanPool haven't modified
    assertThat(hotspot20NetworkCfgCollectorOperationBuilder
        .hasChanged(new ModifiedTxEntity<>(hotspot20Network, Collections.emptySet()), emptyTxChanges()))
        .isFalse();

    TxChanges txChanges = new TxChangesImpl(null);
    txChanges.add(hotspot20Network, Set.of("name"), EntityAction.MODIFY);

    // Only Network has modified
    assertThat(hotspot20NetworkCfgCollectorOperationBuilder
        .hasChanged(new ModifiedTxEntity<>(hotspot20Network, Set.of("name", "updatedDate")),
            emptyTxChanges()))
        .isTrue();

    TxChanges vlanPoolTxChanges = new TxChangesImpl(null);
    vlanPoolTxChanges.add(vlanPool, Set.of("name"), EntityAction.MODIFY);

    // Only VlanPool has modified
    assertThat(hotspot20NetworkCfgCollectorOperationBuilder
        .hasChanged(new ModifiedTxEntity<>(hotspot20Network, Set.of("updatedDate")),
            vlanPoolTxChanges))
        .isTrue();
  }


}
