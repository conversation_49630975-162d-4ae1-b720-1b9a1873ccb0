package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleFirmwareVersionRepository;
import com.ruckus.cloud.wifi.service.entity.UpdateFirmwareSchedule;
import com.ruckus.cloud.wifi.servicemodel.enums.ScheduleTimeSlotStatus;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.time.DayOfWeek;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@WifiIntegrationTest
@FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
    FlagNames.WIFI_EDA_EMAIL_TEMPLATE_LONG_TERM,
    FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
public class ConsumeWifiScheduleByApModelRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Value("${topic.kairos.jobRegister}")
  private String kairosTopic;

  @Value("${topic.nuvo.notificationRequests}")
  private String nuvoTopic;

  @Autowired
  private UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository;

  @Autowired
  private MessageCaptors messageCaptors;

  @Test
  public void createScheduleNoAp(Venue venue) {
    // Given
    String newFirmwareVersion = "7.0.0.104.100";
    String apR560 = "R560";
    String apR550 = "R550";
    List<String> apModels = List.of(apR560, apR550);

    ApVersion firmware70 = ApVersionTestFixture.recommendedApVersion(newFirmwareVersion, a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(firmware70, firmware70.getId(), txCtxExtension.getRequestId());

    // When
    final var requestId = randomTxId();
    UpdateFirmwareSchedule updateFirmwareSchedule = newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), newFirmwareVersion);
    messageUtil.sendWifiScheduleCreate(venue.getTenant().getId(), requestId, updateFirmwareSchedule);

    // Then
    var usfvList = upgradeScheduleFirmwareVersionRepository.findScheduleVersionsByVenueIdAndStatus(
        venue.getId(), UpgradeScheduleStatus.PENDING);
    assertThat(usfvList).isEmpty();

    messageCaptors.assertThat(
        messageCaptors.getNuvoMessageCaptor(),
        messageCaptors.getKairosMessageCaptor()
    ).doesNotSendByTenant(venue.getTenant().getId());

    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(venue.getTenant().getId());
  }

  @Test
  public void createScheduleWithAp(Tenant tenant, Venue venue, Ap ap) {
    // Given
    final String newFirmwareVersion = "7.0.0.104.200";
    String userApR550 = "R550";
    String apR560 = "R560";
    List<String> apModels = List.of(apR560, userApR550);

    ApVersion firmware70 = ApVersionTestFixture.recommendedApVersion(newFirmwareVersion, a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(firmware70, firmware70.getId(), txCtxExtension.getRequestId());
    ApVersion firmware624 = ApVersionTestFixture.recommendedApVersion("6.2.4.103.200", a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(firmware624, firmware624.getId(), txCtxExtension.getRequestId());

    ap.setModel("R550");
    repositoryUtil.createOrUpdate(ap,tenant.getId(),txCtxExtension.getRequestId());
    createApFirmwareUpgradeRequestAndVenueCurrentFirmwareVersion(tenant, venue, ap, userApR550, firmware624);

    // When
    final var requestId = randomTxId();
    UpdateFirmwareSchedule updateFirmwareSchedule = newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), newFirmwareVersion);
    messageUtil.sendWifiScheduleCreate(venue.getTenant().getId(), requestId, updateFirmwareSchedule);

    // Then
    var usfvList = upgradeScheduleFirmwareVersionRepository.findScheduleVersionsByVenueIdAndStatus(
        venue.getId(), UpgradeScheduleStatus.PENDING);
    assertEquals(1, usfvList.size());
    var usfv = usfvList.get(0);
    assertEquals(newFirmwareVersion, usfv.getApFirmwareVersion().getId());
    assertEquals(1, usfv.getTargetApModels().size());
    assertTrue(usfv.getApFirmwareVersion().getSupportedApModels().contains(userApR550));
    messageCaptors.assertThat(
        messageCaptors.getNuvoMessageCaptor(),
        messageCaptors.getKairosMessageCaptor()
    ).doesNotSendByTenant(venue.getTenant().getId());
    verifyScheduleCmnCfgMessage(venue.getTenant(), venue, List.of(usfv.getApFirmwareVersion()));
  }

  @Test
  public void createScheduleWithApThatApFirmwareIsBigger(Tenant tenant, Venue venue, Ap ap) {
    // Given
    final String newFirmwareVersion = "7.0.0.104.200";
    String userApR550 = "R550";
    String apR560 = "R560";
    List<String> apModels = List.of(apR560, userApR550);

    ApVersion firmware70 = ApVersionTestFixture.recommendedApVersion(newFirmwareVersion, a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(firmware70, firmware70.getId(), txCtxExtension.getRequestId());
    ApVersion firmware70201 = ApVersionTestFixture.recommendedApVersion("7.0.0.104.201", a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(firmware70201, firmware70201.getId(), txCtxExtension.getRequestId());

    createApFirmwareUpgradeRequestAndVenueCurrentFirmwareVersion(tenant, venue, ap, userApR550, firmware70201);

    // When
    final var requestId = randomTxId();
    UpdateFirmwareSchedule updateFirmwareSchedule = newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), newFirmwareVersion);
    messageUtil.sendWifiScheduleCreate(venue.getTenant().getId(), requestId, updateFirmwareSchedule);

    // Then
    var usfvList = upgradeScheduleFirmwareVersionRepository.findScheduleVersionsByVenueIdAndStatus(
        venue.getId(), UpgradeScheduleStatus.PENDING);
    assertThat(usfvList).isEmpty();
    messageCaptors.assertThat(
        messageCaptors.getNuvoMessageCaptor(),
        messageCaptors.getKairosMessageCaptor()
    ).doesNotSendByTenant(venue.getTenant().getId());
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(venue.getTenant().getId());
  }

  @Test
  public void createScheduleWithApExistingSchedule(Tenant tenant, Venue venue, Ap ap) {
    // Given
    String newFirmwareVersion = "7.0.0.104.300";
    String userApR560 = "R560";
    String ApR550 = "R550";
    List<String> apModels = List.of(userApR560, ApR550);

    ApVersion firmware70 = ApVersionTestFixture.recommendedApVersion(newFirmwareVersion, a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(firmware70, firmware70.getId(), txCtxExtension.getRequestId());
    ApVersion firmware624 = ApVersionTestFixture.recommendedApVersion("6.2.4.103.200", a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(firmware624, firmware624.getId(), txCtxExtension.getRequestId());

    ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
    sts.setStatus(ScheduleTimeSlotStatus.WAITING_REMINDER);
    sts.setStartDateTime(new Date());
    sts.setEndDateTime(new Date());
    repositoryUtil.createOrUpdate(sts, tenant.getId(), txCtxExtension.newRequestId());

    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(sts);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(firmware624);
    us.setVenue(venue);
    repositoryUtil.createOrUpdate(us, tenant.getId(), txCtxExtension.newRequestId());

    UpgradeScheduleFirmwareVersion usfv = new UpgradeScheduleFirmwareVersion(randomId());
    usfv.setApFirmwareVersion(firmware624);
    usfv.setUpgradeSchedule(us);
    usfv.setTenant(tenant);
    usfv.setTargetApModels(List.of(userApR560));
    repositoryUtil.createOrUpdate(usfv, tenant.getId(), txCtxExtension.newRequestId());

    createApFirmwareUpgradeRequestAndVenueCurrentFirmwareVersion(tenant, venue, ap, userApR560, firmware624);

    // When
    final var requestId = randomTxId();
    UpdateFirmwareSchedule updateFirmwareSchedule = newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), newFirmwareVersion);
    messageUtil.sendWifiScheduleCreate(venue.getTenant().getId(), requestId, updateFirmwareSchedule);

    // Then
    var usfvList = upgradeScheduleFirmwareVersionRepository.findScheduleVersionsByVenueIdAndStatus(
        venue.getId(), UpgradeScheduleStatus.PENDING);
    assertEquals(1, usfvList.size());
    var usfvResult = usfvList.get(0);
    assertEquals(newFirmwareVersion, usfvResult.getApFirmwareVersion().getId());
    assertEquals(1, usfvResult.getTargetApModels().size());
    assertTrue(usfvResult.getApFirmwareVersion().getSupportedApModels().contains(userApR560));
    messageCaptors.assertThat(
        messageCaptors.getNuvoMessageCaptor(),
        messageCaptors.getKairosMessageCaptor()
    ).doesNotSendByTenant(venue.getTenant().getId());
    verifyScheduleCmnCfgMessage(venue.getTenant(), venue, List.of(usfvResult.getApFirmwareVersion()));
  }

  /**
   * Case1.
   * scheduled firmware:7.0.0.200.600(R550)
   * new firmware: 7.0.0.200.500(R550)
   * firmware in R550: 6.2.4.103.244
   * AP model mapping apply priority: 7.0.0.200.500 > 7.0.0.200.600 (Same branch and the latest one has higher priority)
   * Result: new firmware for R550
   *
   * Case2.
   * scheduled firmware:7.0.0.200.600 (R550)
   * new firmware: 7.0.0.105.500 (R550)
   * AP model mapping apply priority: 7.0.0.200.600 > 7.0.0.105.500 (different branch, bigger firmware version has higher priority)
   * Result: scheduled firmware for R550
   */
  @ParameterizedTest
  @CsvSource({
      "7.0.0.200.500, 7.0.0.200.500",
      "7.0.0.105.500, 7.0.0.200.600",
      "7.0.0.200.601, 7.0.0.200.601",
      "7.0.0.201.100, 7.0.0.201.100",
  })
  public void createScheduleWithApExistingParams(String newFirmwareVersion, String expectedFirmwareVersion, Tenant tenant, Venue venue, Ap ap) {
    // Given
    String scheduledFirmwareVersion = "7.0.0.200.600";
    String apFirmwareVersion = "6.2.4.103.244";
    String ApR550 = "R550";
    List<String> apModels = List.of(ApR550);

    ApVersion scheduledFirmware = ApVersionTestFixture.recommendedApVersion(scheduledFirmwareVersion, a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(scheduledFirmware, scheduledFirmware.getId(), txCtxExtension.getRequestId());
    ApVersion newFirmware = ApVersionTestFixture.recommendedApVersion(newFirmwareVersion, a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(newFirmware, newFirmware.getId(), txCtxExtension.getRequestId());
    ApVersion apFirmware = ApVersionTestFixture.recommendedApVersion(apFirmwareVersion, a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(apFirmware, apFirmware.getId(), txCtxExtension.getRequestId());

    ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
    sts.setStatus(ScheduleTimeSlotStatus.WAITING_REMINDER);
    sts.setStartDateTime(new Date());
    sts.setEndDateTime(new Date());
    repositoryUtil.createOrUpdate(sts, tenant.getId(), txCtxExtension.newRequestId());

    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(sts);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(scheduledFirmware);
    us.setVenue(venue);
    repositoryUtil.createOrUpdate(us, tenant.getId(), txCtxExtension.newRequestId());

    UpgradeScheduleFirmwareVersion usfv = new UpgradeScheduleFirmwareVersion(randomId());
    usfv.setApFirmwareVersion(scheduledFirmware);
    usfv.setUpgradeSchedule(us);
    usfv.setTenant(tenant);
    usfv.setTargetApModels(apModels);
    repositoryUtil.createOrUpdate(usfv, tenant.getId(), txCtxExtension.newRequestId());

    createApFirmwareUpgradeRequestAndVenueCurrentFirmwareVersion(tenant, venue, ap, ApR550, apFirmware);

    // When
    final var requestId = randomTxId();
    UpdateFirmwareSchedule updateFirmwareSchedule = newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), newFirmwareVersion);
    messageUtil.sendWifiScheduleCreate(venue.getTenant().getId(), requestId, updateFirmwareSchedule);

    // Then
    var usfvList = upgradeScheduleFirmwareVersionRepository.findScheduleVersionsByVenueIdAndStatus(
        venue.getId(), UpgradeScheduleStatus.PENDING);
    assertEquals(1, usfvList.size());
    var usfvResult = usfvList.get(0);
    assertEquals(expectedFirmwareVersion, usfvResult.getApFirmwareVersion().getId());
    assertEquals(1, usfvResult.getTargetApModels().size());
    assertTrue(usfvResult.getApFirmwareVersion().getSupportedApModels().contains(ApR550));
    messageCaptors.assertThat(
        messageCaptors.getNuvoMessageCaptor(),
        messageCaptors.getKairosMessageCaptor()
    ).doesNotSendByTenant(venue.getTenant().getId());
    verifyScheduleCmnCfgMessage(venue.getTenant(), venue, List.of(usfvResult.getApFirmwareVersion()));
  }

  @Test
  public void createScheduleWithDifferentApDifferentFirmwareGroup(Tenant tenant, Venue venue, Ap ap) {
    // Given
    String newScheduleFirmwareVersion = "7.0.0.104.310";
    String oldScheudleFirmwareVersion = "6.2.4.103.210";
    String userApR500FirmwareVersion = "6.2.4.103.209";
    String userApR560 = "R560";
    String userApR500 = "R500";

    ApVersion firmware70 = ApVersionTestFixture.recommendedApVersion(newScheduleFirmwareVersion, a -> a.setSupportedApModels(List.of(userApR560)));
    repositoryUtil.createOrUpdate(firmware70, firmware70.getId(), txCtxExtension.getRequestId());
    ApVersion firmware624 = ApVersionTestFixture.recommendedApVersion(oldScheudleFirmwareVersion, a -> a.setSupportedApModels(List.of(userApR500)));
    repositoryUtil.createOrUpdate(firmware624, firmware624.getId(), txCtxExtension.getRequestId());
    ApVersion firmware6249 = ApVersionTestFixture.recommendedApVersion(userApR500FirmwareVersion, a -> a.setSupportedApModels(List.of(userApR500)));
    repositoryUtil.createOrUpdate(firmware6249, firmware6249.getId(), txCtxExtension.getRequestId());

    ap.setModel(userApR560);
    repositoryUtil.createOrUpdate(ap, tenant.getId(), txCtxExtension.newRequestId());

    ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
    sts.setStatus(ScheduleTimeSlotStatus.WAITING_REMINDER);
    sts.setStartDateTime(new Date());
    sts.setEndDateTime(new Date());
    repositoryUtil.createOrUpdate(sts, tenant.getId(), txCtxExtension.newRequestId());

    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(sts);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(firmware624);
    us.setVenue(venue);
    repositoryUtil.createOrUpdate(us, tenant.getId(), txCtxExtension.newRequestId());

    UpgradeScheduleFirmwareVersion usfv = new UpgradeScheduleFirmwareVersion(randomId());
    usfv.setApFirmwareVersion(firmware624);
    usfv.setUpgradeSchedule(us);
    usfv.setTenant(tenant);
    usfv.setTargetApModels(List.of(userApR500));
    repositoryUtil.createOrUpdate(usfv, tenant.getId(), txCtxExtension.newRequestId());

    Ap ap2  = ApTestFixture.randomAp(ap.getApGroup());
    ap2.setModel(userApR500);
    repositoryUtil.createOrUpdate(ap2, tenant.getId(), txCtxExtension.newRequestId());
    createApFirmwareUpgradeRequestAndVenueCurrentFirmwareVersion(tenant, venue, ap, userApR560, firmware624);
    createApFirmwareUpgradeRequestAndVenueCurrentFirmwareVersion(tenant, venue, ap2, userApR500, firmware6249);

    // When
    final var requestId = randomTxId();
    UpdateFirmwareSchedule updateFirmwareSchedule = newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), newScheduleFirmwareVersion);
    messageUtil.sendWifiScheduleCreate(venue.getTenant().getId(), requestId, updateFirmwareSchedule);

    // Then
    var usfvList = upgradeScheduleFirmwareVersionRepository.findScheduleVersionsByVenueIdAndStatus(
        venue.getId(), UpgradeScheduleStatus.PENDING);
    assertEquals(2, usfvList.size());
    assertEquals(newScheduleFirmwareVersion,
        usfvList.stream().filter(uu -> uu.getTargetApModels().get(0).equals(userApR560)).findFirst().get().getApFirmwareVersion().getId());
    assertEquals(oldScheudleFirmwareVersion,
        usfvList.stream().filter(uu -> uu.getTargetApModels().get(0).equals(userApR500)).findFirst().get().getApFirmwareVersion().getId());
    List<ApVersion> scheduleVersions = usfvList.stream().map(UpgradeScheduleFirmwareVersion::getApFirmwareVersion)
        .toList();
    verifyScheduleCmnCfgMessage(venue.getTenant(), venue, scheduleVersions);
  }

  @Test
  public void createScheduleWith11acApButNewFirmwareNotSupported(Tenant tenant, Venue venue, Ap ap) {
    // Given
    final String newFirmwareVersion = "7.0.0.104.400";
    String apR560 = "R560";
    String apR550 = "R550";
    String userApR500 = "R500";
    List<String> apModels = List.of(apR560, apR550);

    ApVersion firmware70 = ApVersionTestFixture.recommendedApVersion(newFirmwareVersion, a -> a.setSupportedApModels(apModels));
    repositoryUtil.createOrUpdate(firmware70, firmware70.getId(), txCtxExtension.getRequestId());
    ApVersion firmware624 = ApVersionTestFixture.recommendedApVersion("6.2.0.103.200", a -> a.setSupportedApModels(List.of(userApR500)));
    repositoryUtil.createOrUpdate(firmware624, firmware624.getId(), txCtxExtension.getRequestId());

    createApFirmwareUpgradeRequestAndVenueCurrentFirmwareVersion(tenant, venue, ap, userApR500, firmware624);

    // When
    final var requestId = randomTxId();
    UpdateFirmwareSchedule updateFirmwareSchedule = newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), newFirmwareVersion);
    messageUtil.sendWifiScheduleCreate(venue.getTenant().getId(), requestId, updateFirmwareSchedule);

    //Then
    var usfvList = upgradeScheduleFirmwareVersionRepository.findScheduleVersionsByVenueIdAndStatus(
        venue.getId(), UpgradeScheduleStatus.PENDING);
    assertThat(usfvList).isEmpty();
    messageCaptors.assertThat(
        messageCaptors.getNuvoMessageCaptor(),
        messageCaptors.getKairosMessageCaptor()
    ).doesNotSendByTenant(venue.getTenant().getId());
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(venue.getTenant().getId());
  }

  private void verifyScheduleCmnCfgMessage(Tenant tenant, Venue venue, List<ApVersion> targetVersions) {
    List<String> versionStrList = targetVersions.stream().map(AbstractBaseEntity::getId).toList();
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenant.getId());
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> Index.VENUE.equals(op.getIndex()) && op.getOpType() == OpType.MOD)
        .filteredOn(
            op -> venue.getId().equals(op.getId()) && op.getDocMap().containsKey(Key.NEXT_AP_FIRMWARE_SCHEDULES))
        .isNotEmpty().singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(docMap -> {
          assertThat(docMap.get(Key.NEXT_AP_FIRMWARE_SCHEDULES).getListValue().getValuesList())
              .extracting(value -> value.getStructValue().getFieldsMap().get(Key.VERSION_INFO))
              .extracting(value -> value.getStructValue().getFieldsMap().get(Key.VERSION_INFO_VERSION))
              .extracting(com.ruckus.cloud.events.gpb.Value::getStringValue)
              .containsExactlyInAnyOrderElementsOf(versionStrList);
        });
  }

  private UpdateFirmwareSchedule newUpdateFirmwareSchedule(String requestId, String tenantId, String version) {
    return UpdateFirmwareSchedule.builder()
        .tenantId(tenantId)
        .requestId(requestId)
        .version(version)
        .days(List.of(DayOfWeek.SATURDAY))
        .times(List.of("TIME_PERIOD_00_02"))
        .timeZone("Asia/Taipei")
        .build();
  }

  private void createApFirmwareUpgradeRequestAndVenueCurrentFirmwareVersion(Tenant tenant, Venue venue, Ap ap, String apModel,
      ApVersion firmware) {
    ApFirmwareUpgradeRequest apFirmwareUpgradeRequest = new ApFirmwareUpgradeRequest(randomId());
    apFirmwareUpgradeRequest.setTenant(tenant);
    apFirmwareUpgradeRequest.setModel(apModel);
    apFirmwareUpgradeRequest.setSerialNumber(ap.getId());
    repositoryUtil.createOrUpdate(apFirmwareUpgradeRequest, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

    VenueCurrentFirmware vcf = new VenueCurrentFirmware(randomId());
    vcf.setTenant(tenant);
    vcf.setVenue(venue);
    vcf.setApModel(apModel);
    vcf.setFirmware(firmware);
    repositoryUtil.createOrUpdate(vcf, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
  }
}
