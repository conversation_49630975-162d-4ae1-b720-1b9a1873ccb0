package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.assertj.core.api.Assertions.tuple;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceRule;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.repository.MulticastDnsProxyServiceProfileApRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.assertj.core.api.BDDAssertions;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

@WifiUnitTest
public class MulticastDnsProxyServiceProfileCmnCfgCollectorOperationBuilderTest {

  @Mock
  private VenueRepository venueRepository;

  @Mock
  private MulticastDnsProxyServiceProfileApRepository multicastDnsProxyServiceProfileApRepository;

  @InjectMocks
  @Spy
  private MulticastDnsProxyServiceProfileCmnCfgCollectorOperationBuilder unit;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void whenEntityClass() {
    BDDAssertions.then(unit.entityClass()).isNotNull().isEqualTo(
        MulticastDnsProxyServiceProfile.class);
  }

  @Test
  void whenIndex() {
    BDDAssertions.then(unit.index())
        .isNotNull()
        .isEqualTo(EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME);
  }

  @Nested
  class WhenConfig {

    private final Operations.Builder builder = Operations.newBuilder();
    private final String venueId = randomId();
    private final List<String> apSerialNumbers = new ArrayList<>();
    private MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile;
    private Map<String, Value> docMap;

    @BeforeEach
    void beforeEach() {
      multicastDnsProxyServiceProfile = new MulticastDnsProxyServiceProfile(randomId());
      multicastDnsProxyServiceProfile.setServiceName(randomName());
      multicastDnsProxyServiceProfile.setTenant(new Tenant(txCtxExtension.getTenantId()));
      List<Ap> aps = new ArrayList<>();
      MulticastDnsProxyServiceRule rule = new MulticastDnsProxyServiceRule();
      rule.setRuleIndex(0);
      rule.setService(BridgeServiceEnum.APPLETV);
      rule.setEnabled(true);
      rule.setFromVlan(1);
      rule.setToVlan(3);
      multicastDnsProxyServiceProfile.setRules(List.of(rule));
      ApGroup apGroup = new ApGroup(randomId());
      apGroup.setVenue(new Venue(venueId));
      Ap ap1 = new Ap(randomSerialNumber());
      ap1.setApGroup(apGroup);
      aps.add(ap1);
      Ap ap2 = new Ap(randomSerialNumber());
      ap2.setApGroup(apGroup);
      aps.add(ap2);
      MulticastDnsProxyServiceProfileAp profileAp1 = new MulticastDnsProxyServiceProfileAp(
          randomId());
      profileAp1.setAp(ap1);
      apSerialNumbers.add(ap1.getId());
      MulticastDnsProxyServiceProfileAp profileAp2 = new MulticastDnsProxyServiceProfileAp(
          randomId());
      profileAp2.setAp(ap2);
      apSerialNumbers.add(ap2.getId());
      multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(
          List.of(profileAp1, profileAp2));

      doReturn(aps)
          .when(multicastDnsProxyServiceProfileApRepository)
          .findApsByTenantIdAndMulticastDnsProxyServiceProfileId(
              eq(txCtxExtension.getTenantId()), eq(multicastDnsProxyServiceProfile.getId()));
    }

    @Test
    void testApSerialNumbersInAddMulticastDnsProxyServiceProfile() {
      unit.config(builder, multicastDnsProxyServiceProfile, EntityAction.ADD);
      docMap = builder.build().getDocMap();
      validateApSerialNumbersInMulticastDnsProxyServiceProfileCfgMsg();
    }

    @Test
    void testApSerialNumbersInUpdateMulticastDnsProxyServiceProfile() {
      unit.config(builder, multicastDnsProxyServiceProfile, EntityAction.MODIFY);
      docMap = builder.build().getDocMap();
      validateApSerialNumbersInMulticastDnsProxyServiceProfileCfgMsg();
    }

    private void validateApSerialNumbersInMulticastDnsProxyServiceProfileCfgMsg() {
      assertThat(docMap.get(Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(multicastDnsProxyServiceProfile.getId());
      assertThat(docMap.get(Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(multicastDnsProxyServiceProfile.getServiceName());
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(multicastDnsProxyServiceProfile.getTenant().getId());
      assertThat(docMap.get(Key.RULES))
          .extracting(Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(Value.class))
          .satisfies(rules -> assertEquals(1, rules.size()));
      assertThat(docMap.get(Key.AP_SERIAL_NUMBERS))
          .extracting(Value::getNullValue)
          .isEqualTo(NullValue.NULL_VALUE);
      assertThat(docMap.get(Key.ACTIVATIONS))
          .extracting(Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(Value.class))
          .satisfies(venueAps -> assertEquals(1, venueAps.size()))
          .extracting(
              v -> v.getStructValue()
                  .getFieldsOrThrow(Key.AP_SERIAL_NUMBERS)
                  .getListValue().getValuesList()
                  .stream().map(Value::getStringValue).toList(),
              v -> v.getStructValue().getFieldsOrThrow(Key.VENUE_ID).getStringValue())
          .containsExactly(tuple(apSerialNumbers, venueId));
    }
  }
}
