package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApGroupsProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import com.ruckus.cloud.wifi.utils.TxCtxUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.jdbc.Sql;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.tuple;
import static org.junit.jupiter.api.Assertions.assertEquals;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES
        ('4c8279f79307415fa9e4c88a1819f0fc'),
        ('a782b402d95c46b4aabce87b77e68612'),
        ('msp-tenant-id'),
        ('ec-tenant-id-001'),
        ('ec-tenant-id-002');
    INSERT INTO network (id, name, type, tenant, is_template) VALUES
        ('c9845a491cbc43d596ffcf3b5fca8c4f','OpenNetwork','OPEN','4c8279f79307415fa9e4c88a1819f0fc', false),
        ('f1c66aa6279142cb8f23bc6d2df9ce26', 'Open1', 'OPEN', '4c8279f79307415fa9e4c88a1819f0fc', false),
        ('db6d7f287d9a4b51892a9b202cfadf31', 'Open2', 'OPEN', '4c8279f79307415fa9e4c88a1819f0fc', false),
        ('dpsk-network-non-template-id', 'dpsk-network-non-template', 'DPSK', 'a782b402d95c46b4aabce87b77e68612', false),
        ('dpsk-network-template-id', 'dpsk-network-template', 'DPSK', 'a782b402d95c46b4aabce87b77e68612', true);
    INSERT INTO venue (id, name, tenant) VALUES
        ('a263782672754f339ad34cb5f22c5c40','dVenue', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('144c424d8ef74d09b5435fdb354712d4','My-Venue', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('144c424d8ef74d09b5435fdb354712d5','test-Venue1', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('144c424d8ef74d09b5435fdb354712d6','test-Venue2', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('144c424d8ef74d09b5435fdb354712d7','test-Venue3', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('144c424d8ef74d09b5435fdb354712d8','test-Venue4', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('144c424d8ef74d09b5435fdb354712d9','test-Venue5', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network_venue (id, is_all_ap_groups, venue, network, tenant) VALUES
        ('839b5743185747b6ae442be3b98f331a', true, '144c424d8ef74d09b5435fdb354712d4', 'f1c66aa6279142cb8f23bc6d2df9ce26', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('64166dfc4f5849619bff2d1e2f385254', true, 'a263782672754f339ad34cb5f22c5c40', 'c9845a491cbc43d596ffcf3b5fca8c4f', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('3aed3ac03da445fb99b70e9e6d32c7d8', false, '144c424d8ef74d09b5435fdb354712d4', 'db6d7f287d9a4b51892a9b202cfadf31', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('3aed3ac03da445fb99b70e9e6d32c7d1', true, '144c424d8ef74d09b5435fdb354712d5', 'db6d7f287d9a4b51892a9b202cfadf31', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('3aed3ac03da445fb99b70e9e6d32c7d2', true, '144c424d8ef74d09b5435fdb354712d6', 'db6d7f287d9a4b51892a9b202cfadf31', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('3aed3ac03da445fb99b70e9e6d32c7d3', true, '144c424d8ef74d09b5435fdb354712d7', 'db6d7f287d9a4b51892a9b202cfadf31', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('3aed3ac03da445fb99b70e9e6d32c7d4', true, '144c424d8ef74d09b5435fdb354712d8', 'db6d7f287d9a4b51892a9b202cfadf31', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('3aed3ac03da445fb99b70e9e6d32c7d5', true, '144c424d8ef74d09b5435fdb354712d9', 'db6d7f287d9a4b51892a9b202cfadf31', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap_group (id, name, is_default, tenant, venue) VALUES
        ('bd703743f85b4887b75826cb654a8eb7', 'apGroup1', false, '4c8279f79307415fa9e4c88a1819f0fc', '144c424d8ef74d09b5435fdb354712d4'),
        ('dad50fd9d56b4c4bba634f596647504a', 'apGroup2', false, '4c8279f79307415fa9e4c88a1819f0fc', '144c424d8ef74d09b5435fdb354712d4'),
        ('c2b0fd39957e4314ba27f90a468c5e8d', '', true, '4c8279f79307415fa9e4c88a1819f0fc', '144c424d8ef74d09b5435fdb354712d4'),
        ('c2b0fd39957e4314ba27f90a468c5e8e', 'apGroupTest1', true, '4c8279f79307415fa9e4c88a1819f0fc', '144c424d8ef74d09b5435fdb354712d5'),
        ('c2b0fd39957e4314ba27f90a468c5e8f', 'apGroupTest2', true, '4c8279f79307415fa9e4c88a1819f0fc', '144c424d8ef74d09b5435fdb354712d6'),
        ('c2b0fd39957e4314ba27f90a468c5e8g', 'apGroupTest3', true, '4c8279f79307415fa9e4c88a1819f0fc', '144c424d8ef74d09b5435fdb354712d7'),        
        ('e60a8e3f013c47cc97f12eb7a9a6812f', 'apGroupTest4', true, '4c8279f79307415fa9e4c88a1819f0fc', 'a263782672754f339ad34cb5f22c5c40'),
        ('c2b0fd39957e4314ba27f90a468c5e8h', 'apGroupTest5', true, '4c8279f79307415fa9e4c88a1819f0fc', 'a263782672754f339ad34cb5f22c5c40'),
        ('c2b0fd39957e4314ba27f90a468c5e8i', 'apGroupTest6', true, '4c8279f79307415fa9e4c88a1819f0fc', 'a263782672754f339ad34cb5f22c5c40');
    INSERT INTO network_ap_group (id, network_venue, ap_group, tenant) VALUES
        ('a9c2872816c1455d856f43c4107db739','64166dfc4f5849619bff2d1e2f385254','e60a8e3f013c47cc97f12eb7a9a6812f', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('8a6d34b5dff9421592f32630cc95a6a1','839b5743185747b6ae442be3b98f331a','bd703743f85b4887b75826cb654a8eb7', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('5fa7568f015b4aa5993912a1419eb1f0','839b5743185747b6ae442be3b98f331a','c2b0fd39957e4314ba27f90a468c5e8d', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('5fa7568f015b4aa5993912a1419eb1f1','3aed3ac03da445fb99b70e9e6d32c7d1','c2b0fd39957e4314ba27f90a468c5e8e', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('5fa7568f015b4aa5993912a1419eb1f2','3aed3ac03da445fb99b70e9e6d32c7d2','c2b0fd39957e4314ba27f90a468c5e8f', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('5fa7568f015b4aa5993912a1419eb1f3','3aed3ac03da445fb99b70e9e6d32c7d3','c2b0fd39957e4314ba27f90a468c5e8g', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('5fa7568f015b4aa5993912a1419eb1f4','3aed3ac03da445fb99b70e9e6d32c7d4','c2b0fd39957e4314ba27f90a468c5e8h', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('5fa7568f015b4aa5993912a1419eb1f5','3aed3ac03da445fb99b70e9e6d32c7d5','c2b0fd39957e4314ba27f90a468c5e8i', '4c8279f79307415fa9e4c88a1819f0fc'),                   
        ('2976309148194955beb256798ce17077','3aed3ac03da445fb99b70e9e6d32c7d8','bd703743f85b4887b75826cb654a8eb7', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('23c6d74b7f934c9aaf755e4995181bf2','3aed3ac03da445fb99b70e9e6d32c7d8','dad50fd9d56b4c4bba634f596647504a', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('03ab6d21c3d0415fa6a2532c95fca231','839b5743185747b6ae442be3b98f331a','dad50fd9d56b4c4bba634f596647504a', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap (id, ap_group, tenant) VALUES
        ('961211111151', 'e60a8e3f013c47cc97f12eb7a9a6812f', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network_ap_group_radio (id, network_ap_group, tenant) VALUES
        ('aa72c0e5e45f4b18b6b67f524a265eea','23c6d74b7f934c9aaf755e4995181bf2','4c8279f79307415fa9e4c88a1819f0fc'),
        ('5a7cb87d9a604d9882c991f52da75fc6','2976309148194955beb256798ce17077','4c8279f79307415fa9e4c88a1819f0fc'),
        ('f0631d23e6924aed82d3c2d8385830a9','a9c2872816c1455d856f43c4107db739','4c8279f79307415fa9e4c88a1819f0fc'),
        ('9cf461a59dae4d29bfc0e875e5a7b5ae','03ab6d21c3d0415fa6a2532c95fca231','4c8279f79307415fa9e4c88a1819f0fc'),
        ('ae3012fb48af410eabdc346f76a47aff','8a6d34b5dff9421592f32630cc95a6a1','4c8279f79307415fa9e4c88a1819f0fc'),
        ('73cfb8ea68a9431fb6391c17da9d5805','5fa7568f015b4aa5993912a1419eb1f0','4c8279f79307415fa9e4c88a1819f0fc'),
        ('73cfb8ea68a9431fb6391c17da9d5806','5fa7568f015b4aa5993912a1419eb1f1','4c8279f79307415fa9e4c88a1819f0fc'),
        ('73cfb8ea68a9431fb6391c17da9d5807','5fa7568f015b4aa5993912a1419eb1f2','4c8279f79307415fa9e4c88a1819f0fc'),
        ('73cfb8ea68a9431fb6391c17da9d5808','5fa7568f015b4aa5993912a1419eb1f3','4c8279f79307415fa9e4c88a1819f0fc'),
        ('73cfb8ea68a9431fb6391c17da9d5809','5fa7568f015b4aa5993912a1419eb1f4','4c8279f79307415fa9e4c88a1819f0fc'),
        ('73cfb8ea68a9431fb6391c17da9d5810','5fa7568f015b4aa5993912a1419eb1f5','4c8279f79307415fa9e4c88a1819f0fc');                       
    INSERT INTO network (id, name, type, tenant, template_id) VALUES (
        '9f6f1626d15711eea5060242ac120002',
        '9f6f1626-d157-11ee-a506-0242ac120002',
        'OPEN',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'dpsk-network-template-id');
    INSERT INTO network (id, name, type, tenant, is_template, is_enforced, template_id) VALUES
        ('open-network-template-id', 'open-network-template', 'OPEN', 'msp-tenant-id', true, true, 'xxx'),
        ('open-network-instance-id-001', 'open-network-instance-001', 'OPEN', 'ec-tenant-id-001', false, true, 'open-network-template-id'),
        ('open-network-instance-id-002', 'open-network-instance-002', 'OPEN', 'ec-tenant-id-002', false, true, 'open-network-template-id');
    """)
class NetworkRepositoryTest {

  @Autowired
  private NetworkRepository repository;

  @PersistenceContext
  private EntityManager entityManager;

  @Test
  void testFindTypeByIdAndTenantId() {
    final var tenantId = "4c8279f79307415fa9e4c88a1819f0fc";
    final var networkId = "c9845a491cbc43d596ffcf3b5fca8c4f";

    assertThat(repository.findTypeByIdAndTenantId(networkId, tenantId))
        .isNotNull()
        .isEqualTo(NetworkTypeEnum.OPEN);
  }

  @Test
  void testUpdateTypeByIdAndTenantId() {
    final var tenantId = "4c8279f79307415fa9e4c88a1819f0fc";
    final var networkId = "c9845a491cbc43d596ffcf3b5fca8c4f";

    assertThat(repository.findByIdAndTenantId(networkId, tenantId))
        .isPresent()
        .get().isNotNull()
        .satisfies(network -> {
          assertThat(network.getName()).isEqualTo("OpenNetwork");
          assertThat(network.getType()).isEqualTo(NetworkTypeEnum.OPEN);
          assertThat(network).isInstanceOf(OpenNetwork.class);
        });

    assertThat(repository.updateTypeByIdAndTenantId(NetworkTypeEnum.PSK, networkId, tenantId))
        .isOne();

    final var pskNetwork = repository.findByIdAndTenantId(networkId, tenantId).orElseThrow();
    pskNetwork.setName("OpenToPskNetwork");
    repository.save(pskNetwork);

    assertThat(repository.findByIdAndTenantId(networkId, tenantId))
        .isPresent()
        .get().isNotNull()
        .satisfies(network -> {
          assertThat(network.getName()).isEqualTo("OpenToPskNetwork");
          assertThat(network.getType()).isEqualTo(NetworkTypeEnum.PSK);
          assertThat(network).isInstanceOf(PskNetwork.class);
        });
  }

  @Test
  void testFindTenantIdByType() {
    final List<String> result = repository.findTenantIdByType(NetworkTypeEnum.DPSK);
    assertThat(result)
        .hasSize(1)
        .containsExactly("a782b402d95c46b4aabce87b77e68612");
  }

  @Test
  void testFindAllDistinctTenantId() {
    final var pageable = PageRequest.of(0, 10);
    final List<String> result = repository.findAllDistinctTenantId(pageable);
    assertThat(result).isNotNull();
  }

  @ApplyTemplateFilter
  @Test
  void testFindByIdInAndTenantIdAndIsTemplate() {
    // template, isTemplate:false
    final Optional<Network> templateFoundAsTemplate = repository.findByIdAndTenantId(
        "dpsk-network-template-id", "a782b402d95c46b4aabce87b77e68612");
    assertThat(templateFoundAsTemplate)
        .isPresent()
        .get().isNotNull()
        .extracting(Network::getId).isEqualTo("dpsk-network-template-id");

    // non-template, isTemplate:false
    final Optional<Network> nonTemplateFoundAsTemplate = repository.findByIdAndTenantId(
        "dpsk-network-non-template-id", "a782b402d95c46b4aabce87b77e68612");
    assertThat(nonTemplateFoundAsTemplate).isEmpty();
  }

  @Test
  void testFindByIdInAndTenantIdAndIsNotTemplate() {
    // legacy, isTemplate:NULL -> default is false with ACX-48154
    final Optional<Network> legacyFoundAsNonTemplate = repository.findByIdAndTenantId(
        "c9845a491cbc43d596ffcf3b5fca8c4f", "4c8279f79307415fa9e4c88a1819f0fc");
    assertThat(legacyFoundAsNonTemplate)
        .isPresent()
        .get().isNotNull()
        .extracting(Network::getId).isEqualTo("c9845a491cbc43d596ffcf3b5fca8c4f");

    // non-template, isTemplate:false
    final Optional<Network> nonTemplateFoundAsNonTemplate = repository.findByIdAndTenantId(
        "dpsk-network-non-template-id", "a782b402d95c46b4aabce87b77e68612");
    assertThat(nonTemplateFoundAsNonTemplate)
        .isPresent()
        .get().isNotNull()
        .extracting(Network::getId).isEqualTo("dpsk-network-non-template-id");

    // template, isTemplate:true
    final Optional<Network> templateFoundAsNonTemplate = repository.findByIdAndTenantId(
        "dpsk-network-template-id", "a782b402d95c46b4aabce87b77e68612");
    assertThat(templateFoundAsNonTemplate).isEmpty();
  }

  @Test
  void testFindVenueApGroupsByNetworkId() {
    final var tenantId = "4c8279f79307415fa9e4c88a1819f0fc";
    final var networkId = "f1c66aa6279142cb8f23bc6d2df9ce26";
    final var pageable = PageRequest.of(0, 10);
    final var result = repository.findVenueApGroupsByNetworkId(tenantId, networkId, pageable);
    assertThat(result)
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .satisfies(venueApGroupsQueryProjection -> {
          assertThat(venueApGroupsQueryProjection.getVenueId())
              .isEqualTo("144c424d8ef74d09b5435fdb354712d4");
          assertThat(venueApGroupsQueryProjection.getIsAllApGroups())
              .as("Expected all AP groups to be enabled")
              .isTrue();
          // Convert apGroups to list, sort, and check for distinct values
          var actualApGroups = Arrays.asList(venueApGroupsQueryProjection.getApGroups().split(","));
          assertThat(actualApGroups)
              .as("ApGroups should contain only distinct values")
              .doesNotHaveDuplicates();
          Collections.sort(actualApGroups);
          var expectedApGroups = Arrays.asList(
              "bd703743f85b4887b75826cb654a8eb7",
              "c2b0fd39957e4314ba27f90a468c5e8d",
              "dad50fd9d56b4c4bba634f596647504a"
          );
          Collections.sort(expectedApGroups);
          assertThat(actualApGroups).isEqualTo(expectedApGroups);
        });
  }

  @Test
  void testFindVenueApGroupsByNetworkIdWithPagination() {
    final var tenantId = "4c8279f79307415fa9e4c88a1819f0fc";
    final var networkId = "db6d7f287d9a4b51892a9b202cfadf31";
    final var pageable = PageRequest.of(2, 2);
    final var result = repository.findVenueApGroupsByNetworkId(tenantId, networkId, pageable);
    assertThat(result)
        .isNotNull()
        .hasSize(2)
        .extracting(
            VenueApGroupsProjection::getVenueId,
            VenueApGroupsProjection::getIsAllApGroups,
            VenueApGroupsProjection::getApGroups
        )
        .containsExactlyInAnyOrder(
            tuple("144c424d8ef74d09b5435fdb354712d8", true, "c2b0fd39957e4314ba27f90a468c5e8h"),
            tuple("144c424d8ef74d09b5435fdb354712d9", true, "c2b0fd39957e4314ba27f90a468c5e8i")
        );
  }

  @Test
  void testFindByTemplateIdAndTenantId() {
    final Optional<Network> result = repository.findByTemplateIdAndTenantId(
        "dpsk-network-template-id", "4c8279f79307415fa9e4c88a1819f0fc");
    assertThat(result)
        .isPresent()
        .get().isNotNull()
        .extracting(Network::getTemplateId).isEqualTo("dpsk-network-template-id");
  }

  @ApplyTemplateFilter
  @Test
  void testExistsByTenantIdAndNameAndIdNotAndIsTemplate() {
    assertThat(repository.existsByTenantIdAndNameAndIdNot(
        "a782b402d95c46b4aabce87b77e68612", "dpsk-network-template", "id"))
        .isTrue();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(
        "a782b402d95c46b4aabce87b77e68612", "dpsk-network-template", "dpsk-network-template-id"))
        .isFalse();
  }

  @Test
  void testExistsByTenantIdAndNameAndIdNotAndIsNotTemplate() {
    assertThat(repository.existsByTenantIdAndNameAndIdNot(
        "a782b402d95c46b4aabce87b77e68612", "dpsk-network-non-template", "id"))
        .isTrue();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(
        "a782b402d95c46b4aabce87b77e68612", "dpsk-network-non-template", "dpsk-network-non-template-id"))
        .isFalse();
  }

  @Test
  void testFindByTemplateId() {
    TxCtxUtils.allowCrossingTenantQuery(Network.class);
    final List<Network> result = repository.findByTemplateId("open-network-template-id");
    assertThat(result)
        .isNotNull()
        .hasSize(2)
        .extracting(Network::getId)
        .containsExactlyInAnyOrder(
            "open-network-instance-id-001",
            "open-network-instance-id-002"
        );
  }

  @Test
  void testFindAllNetworksApplicationVisibilityNotDisabled() {
    var tenantId = "4c8279f79307415fa9e4c88a1819f0fc";
    var allNetworks =
        repository.findByTenantId(tenantId).stream()
            .filter(
                network ->
                    Optional.of(network)
                        .map(Network::getWlan)
                        .map(Wlan::getAdvancedCustomization)
                        .filter(c -> Boolean.FALSE.equals(c.getApplicationVisibilityEnabled()))
                        .isEmpty())
            .map(Network::getId)
            .collect(Collectors.toSet());
    var result =
        repository.findAllNetworksApplicationVisibilityNotDisabled(tenantId).stream()
            .map(Network::getId)
            .collect(Collectors.toSet());
    assertEquals(allNetworks, result);
  }

  @Test
  void testFindAllNetworksApplicationPolicyNotDisabled() {
    var tenantId = "4c8279f79307415fa9e4c88a1819f0fc";
    var allNetworks =
        repository.findByTenantId(tenantId).stream()
            .filter(
                network ->
                    Optional.of(network)
                        .map(Network::getWlan)
                        .map(Wlan::getAdvancedCustomization)
                        .filter(c -> Boolean.FALSE.equals(c.getApplicationPolicyEnable()))
                        .isEmpty())
            .map(Network::getId)
            .collect(Collectors.toSet());
    var result =
        repository.findAllNetworksApplicationPolicyNotDisabled(tenantId).stream()
            .map(Network::getId)
            .collect(Collectors.toSet());
    assertEquals(allNetworks, result);
  }

  @Test
  void testFindByTenantIdAndIsTemplateFalse() {
    final var ecTenantId = "ec-tenant-id-001";

    final List<Network> instances = repository.findByTenantIdAndIsTemplateFalse(ecTenantId);

    assertThat(instances)
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .satisfies(network -> {
          assertThat(network.getId()).isEqualTo("open-network-instance-id-001");
          assertThat(network.getName()).isEqualTo("open-network-instance-001");
          assertThat(network.getIsTemplate()).isFalse();
          assertThat(network.getIsEnforced()).isTrue();
          assertThat(network.getTemplateId()).isEqualTo("open-network-template-id");
          assertThat(network.getTenant().getId()).isEqualTo(ecTenantId);
        });
  }
}
