package com.ruckus.cloud.wifi.entitylistener.associationchange;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.entitylistener.associationchange.builder.BaseAssociationProfileBuilder;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntityFactory;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

public class AssociationChangeAggregatedEntityListenerTest {

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  public void testFilter() {
    BaseAssociationProfileBuilder guestNetworkBuilder =
        mock(BaseAssociationProfileBuilder.class);

    doReturn(GuestNetwork.class).when(guestNetworkBuilder).entityClass();

    AssociationChangeEntityListener listener =
        new AssociationChangeEntityListener(null, List.of(guestNetworkBuilder));

    assertThat(listener.filter().test(new GuestNetwork())).isTrue();
  }

  @Test
  public void testBuild() {
    BaseAssociationProfileBuilder guestNetworkBuilder =
        mock(BaseAssociationProfileBuilder.class);

    doReturn(GuestNetwork.class).when(guestNetworkBuilder).entityClass();

    AssociationChangeEntityListener listener =
        new AssociationChangeEntityListener(null, List.of(guestNetworkBuilder));

    listener.build(
        TxEntityFactory.createTxEntity(new GuestNetwork(), null, EntityAction.ADD),
        emptyTxChanges());

    verify(guestNetworkBuilder, times(1)).build(any(NewTxEntity.class), any(TxChangesReader.class));
  }

  @Test
  void testBulkBuild() {
    BaseAssociationProfileBuilder guestNetworkBuilder =
        mock(BaseAssociationProfileBuilder.class);

    doReturn(GuestNetwork.class).when(guestNetworkBuilder).entityClass();

    AssociationChangeEntityListener listener =
        new AssociationChangeEntityListener(null, List.of(guestNetworkBuilder));

    List<TxEntity<?>> newTxEntities =
        List.of(
            TxEntityFactory.createTxEntity(new GuestNetwork(), null, EntityAction.ADD),
            TxEntityFactory.createTxEntity(new GuestNetwork(), null, EntityAction.ADD));

    listener.bulkBuild(newTxEntities, List.of(), List.of(), emptyTxChanges());

    verify(guestNetworkBuilder, times(2)).build(any(NewTxEntity.class), any(TxChangesReader.class));
  }
}
