package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apGroup;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkApGroup;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.VlanPoolAlgoEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.VlanPoolProfileGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.VlanPoolProfile;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("VlanPoolProfileTemplateTest")
@WifiIntegrationTest
public class ConsumeVlanPoolProfileTemplateRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_VLAN_POOL_PROFILE_TEMPLATE)
  class ConsumeAddVlanPoolTemplateRequestTest {

    @Payload
    private final VlanPoolProfileGenerator generator = Generators.vlanPoolProfile();

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload VlanPoolProfile payload) {
      validateResult(CfgAction.ADD_VLAN_POOL_PROFILE_TEMPLATE, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VLAN_POOL_PROFILE_TEMPLATE)
  class ConsumeUpdateVlanPoolTemplateRequestTest {

    @Payload
    private final VlanPoolProfileGenerator generator = Generators.vlanPoolProfile()
        .setId(nullValue(String.class))
        .setName(serialName("UpdatedVlanPoolProfile"))
        .setDescription(randomString(64));

    private String vlanPoolProfileTemplateId;

    @BeforeEach
    void givenOneVlanPoolPersistedInDb(@Template final VlanPool vlanPool) {
      vlanPoolProfileTemplateId = vlanPool.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("vlanPoolProfileTemplateId", vlanPoolProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VlanPoolProfile payload) {
      validateResult(CfgAction.UPDATE_VLAN_POOL_PROFILE_TEMPLATE, vlanPoolProfileTemplateId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_VLAN_POOL_PROFILE_TEMPLATE)
  class ConsumeDeleteVlanPoolTemplateRequestTest {

    private String vlanPoolProfileTemplateId;

    @BeforeEach
    void givenOneVlanPoolPersistedInDb(@Template final VlanPool vlanPool) {
      vlanPoolProfileTemplateId = vlanPool.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("vlanPoolProfileTemplateId", vlanPoolProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateResult(CfgAction.DELETE_VLAN_POOL_PROFILE_TEMPLATE, vlanPoolProfileTemplateId, null);
    }
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE)
  class ConsumeActivateVlanPoolProfileTemplateOnWifiNetworkTemplateRequestTest {

    private String vlanPoolProfileTemplateId;
    private String wifiNetworkTemplateId;

    @BeforeEach
    void givenOneVlanPoolAndNetworkPersistedInDb(@Template final VlanPool vlanPool, @Template final Network network) {
      vlanPoolProfileTemplateId = vlanPool.getId();
      wifiNetworkTemplateId = network.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId)
          .addPathVariable("vlanPoolProfileTemplateId", vlanPoolProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateWifiNetworkActivationResult(CfgAction.ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          wifiNetworkTemplateId, vlanPoolProfileTemplateId);
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE)
  class ConsumeDeactivateVlanPoolProfileTemplateOnWifiNetworkTemplateRequestTest {

    private String vlanPoolProfileTemplateId;
    private String wifiNetworkTemplateId;

    @BeforeEach
    void givenOneVlanPoolAndNetworkPersistedInDb(@Template final VlanPool vlanPool) {

      final GuestNetwork guestNetwork = (GuestNetwork) network(GuestNetwork.class).generate();
      final GuestPortal guestPortal = guestPortal(GuestNetworkTypeEnum.HostApproval).generate();
      guestNetwork.setPortalServiceProfileId(randomId());
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      guestNetwork.getWlan().getAdvancedCustomization().setVlanPool(vlanPool);
      guestNetwork.setIsTemplate(true);
      repositoryUtil.createOrUpdate(guestNetwork, txCtxExtension.getTenantId(), randomTxId());
      wifiNetworkTemplateId = guestNetwork.getId();
      vlanPoolProfileTemplateId = vlanPool.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId)
          .addPathVariable("vlanPoolProfileTemplateId", vlanPoolProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateWifiNetworkActivationResult(CfgAction.DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          wifiNetworkTemplateId, vlanPoolProfileTemplateId);
    }
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE)
  class ConsumeActivateVlanPoolProfileTemplateOnNetworkApGroupTemplateRequestTest {

    private String venueTemplateId;
    private String vlanPoolProfileTemplateId;
    private String wifiNetworkTemplateId;
    private String apGroupTemplateId;
    private String wifiNetworkApGroupRadioTemplateId;

    @BeforeEach
    void givenOneVlanPoolAndNetworkApGroupPersistedInDb(@Template final Venue venue,
        @DefaultApGroup ApGroup apGroup,
        @Template final VlanPool vlanPool, @Template final Network network) {
      venueTemplateId = venue.getId();
      vlanPoolProfileTemplateId = vlanPool.getId();
      wifiNetworkTemplateId = network.getId();
      apGroupTemplateId = randomId();
      wifiNetworkApGroupRadioTemplateId = randomId();
      ApGroup apGroup1 = apGroup().generate();
      apGroup1.setVenue(venue);
      apGroup1.setIsDefault(false);
      apGroup1.setId(apGroupTemplateId);
      apGroup1.setIsTemplate(true);
      repositoryUtil.createOrUpdate(apGroup1, txCtxExtension.getTenantId(), randomTxId());
      NetworkVenue networkVenue = networkVenue().generate();
      networkVenue.setId(randomId());
      networkVenue.setNetwork(network);
      networkVenue.setVenue(venue);
      networkVenue.setIsAllApGroups(false);
      networkVenue.setIsTemplate(true);
      repositoryUtil.createOrUpdate(networkVenue, txCtxExtension.getTenantId(), randomTxId());
      NetworkApGroup networkApGroup = networkApGroup().generate();
      networkApGroup.setId(randomId());
      networkApGroup.setApGroup(apGroup1);
      networkApGroup.setNetworkVenue(networkVenue);
      repositoryUtil.createOrUpdate(networkApGroup, txCtxExtension.getTenantId(), randomTxId());
      NetworkApGroupRadio networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
          networkApGroup,
          v -> v.setId(wifiNetworkApGroupRadioTemplateId));
      repositoryUtil.createOrUpdate(networkApGroupRadio, txCtxExtension.getTenantId(),
          randomTxId());
      networkApGroup.setNetworkApGroupRadios(List.of(networkApGroupRadio));
      repositoryUtil.createOrUpdate(networkApGroup, txCtxExtension.getTenantId(), randomTxId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueTemplateId", venueTemplateId)
          .addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId)
          .addPathVariable("vlanPoolProfileTemplateId", vlanPoolProfileTemplateId)
          .addPathVariable("apGroupTemplateId", apGroupTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateNetworkApGroupActivationResult(
          CfgAction.ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE,
          wifiNetworkApGroupRadioTemplateId, apGroupTemplateId, vlanPoolProfileTemplateId);
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE)
  class ConsumeDeactivateVlanPoolProfileTemplateOnNetworkApGroupTemplateRequestTest {

    private String venueTemplateId;
    private String vlanPoolProfileTemplateId;
    private String wifiNetworkTemplateId;
    private String apGroupTemplateId;
    private String wifiNetworkApGroupRadioTemplateId;

    @BeforeEach
    void givenOneVlanPoolAndNetworkApGroupPersistedInDb(@Template final Venue venue,
        @DefaultApGroup ApGroup apGroup,
        @Template final VlanPool vlanPool, @Template final Network network) {
      venueTemplateId = venue.getId();
      vlanPoolProfileTemplateId = vlanPool.getId();
      wifiNetworkTemplateId = network.getId();
      apGroupTemplateId = randomId();
      wifiNetworkApGroupRadioTemplateId = randomId();
      ApGroup apGroup1 = apGroup().generate();
      apGroup1.setVenue(venue);
      apGroup1.setIsDefault(false);
      apGroup1.setId(apGroupTemplateId);
      apGroup1.setIsTemplate(true);
      repositoryUtil.createOrUpdate(apGroup1, txCtxExtension.getTenantId(), randomTxId());
      NetworkVenue networkVenue = networkVenue().generate();
      networkVenue.setId(randomId());
      networkVenue.setNetwork(network);
      networkVenue.setVenue(venue);
      networkVenue.setIsAllApGroups(false);
      networkVenue.setIsTemplate(true);
      repositoryUtil.createOrUpdate(networkVenue, txCtxExtension.getTenantId(), randomTxId());
      NetworkApGroup networkApGroup = networkApGroup().generate();
      networkApGroup.setId(randomId());
      networkApGroup.setApGroup(apGroup1);
      networkApGroup.setNetworkVenue(networkVenue);
      repositoryUtil.createOrUpdate(networkApGroup, txCtxExtension.getTenantId(), randomTxId());
      NetworkApGroupRadio networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
          networkApGroup,
          v -> v.setId(wifiNetworkApGroupRadioTemplateId));
      networkApGroupRadio.setVlanPool(vlanPool);
      repositoryUtil.createOrUpdate(networkApGroupRadio, txCtxExtension.getTenantId(),
          randomTxId());
      networkApGroup.setNetworkApGroupRadios(List.of(networkApGroupRadio));
      repositoryUtil.createOrUpdate(networkApGroup, txCtxExtension.getTenantId(), randomTxId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueTemplateId", venueTemplateId)
          .addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId)
          .addPathVariable("vlanPoolProfileTemplateId", vlanPoolProfileTemplateId)
          .addPathVariable("apGroupTemplateId", apGroupTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateNetworkApGroupActivationResult(
          CfgAction.DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE,
          wifiNetworkApGroupRadioTemplateId, apGroupTemplateId, vlanPoolProfileTemplateId);
    }
  }

  private void validateResult(CfgAction apiAction, VlanPoolProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges = revisionService.changes(requestId, tenantId,
        ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    final String vlanPoolId = txChanges.getNewEntities().stream()
        .filter(txEntity -> txEntity.getEntity() instanceof VlanPool)
        .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
    validateResult(apiAction, vlanPoolId, payload);
  }

  private void validateResult(CfgAction apiAction, String vlanPoolId, VlanPoolProfile payload) {
    validateRepositoryData(vlanPoolId, payload, apiAction);
    validateDdccmCfgRequestMessages(apiAction, List.of(vlanPoolId), payload);
    validateCmnCfgCollectorMessages(apiAction, vlanPoolId, null);
    validateActivityMessages(apiAction);
  }

  private void validateWifiNetworkActivationResult(CfgAction apiAction, String entityId,
      String vlanPoolProfileId) {
    validateRepositoryData(apiAction, entityId, vlanPoolProfileId);
    validateCmnCfgCollectorMessages(apiAction, vlanPoolProfileId, entityId);
    validateDdccmCfgRequestMessages(apiAction, null, null);
    validateActivityMessages(apiAction);
  }

  private void validateNetworkApGroupActivationResult(CfgAction apiAction,
      String wifiNetworkApGroupRadioId,
      String apGroupId,
      String vlanPoolProfileId) {
    validateRepositoryData(apiAction, wifiNetworkApGroupRadioId, vlanPoolProfileId);
    validateCmnCfgCollectorMessages(apiAction, vlanPoolProfileId, null);
    validateWlanApGroupDdccmCfgRequestMessages(apiAction, apGroupId, vlanPoolProfileId);
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(String vlanPoolId, VlanPoolProfile payload,
      CfgAction apiAction) {
    if (vlanPoolId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final VlanPool vlanPool = repositoryUtil.find(VlanPool.class, vlanPoolId);

    if (payload == null) {
      assertThat(vlanPool).isNull();
      return;
    }

    assertThat(vlanPool)
        .isNotNull()
        .matches(vp -> Objects.equals(vp.getId(), vlanPoolId))
        .matches(vp -> Objects.equals(vp.getName(), payload.getName()))
        .matches(vp -> Objects.equals(vp.getDescription(), payload.getDescription()))
        .matches(vp -> Objects.equals(vp.getVlanMembers(), payload.getVlanMembers()))
        .matches(vp -> Boolean.TRUE.equals(vp.getIsTemplate()));
  }

  private void validateRepositoryData(CfgAction apiAction, String entityId,
      String vlanPoolProfileId) {
    assertThat(entityId).isNotNull();
    switch (apiAction) {
      case ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE -> {
        final Network network = repositoryUtil.find(Network.class, entityId);
        assertThat(network)
            .isNotNull()
            .matches(
                nw -> Objects.equals(nw.getWlan().getAdvancedCustomization().getVlanPool().getId(),
                    vlanPoolProfileId));
      }
      case DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE -> {
        final Network network = repositoryUtil.find(Network.class, entityId);
        assertThat(network)
            .isNotNull()
            .matches(
                nw -> Objects.isNull(nw.getWlan().getAdvancedCustomization().getVlanPool()));
      }
      case ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE -> {
        NetworkApGroupRadio networkApGroupRadio = repositoryUtil.find(NetworkApGroupRadio.class,
            entityId);
        assertThat(networkApGroupRadio)
            .isNotNull()
            .matches(nwApGroupRadio -> Objects.equals(nwApGroupRadio.getVlanPool().getId(),
                vlanPoolProfileId));
      }
      case DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE -> {
        NetworkApGroupRadio networkApGroupRadio = repositoryUtil.find(NetworkApGroupRadio.class,
            entityId);
        assertThat(networkApGroupRadio)
            .isNotNull()
            .matches(nwApGroupRadio -> Objects.isNull(nwApGroupRadio.getVlanPool()));
      }
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    }
  }

  private void validateDdccmCfgRequestMessages(CfgAction apiAction, List<String> vlanPoolIdList,
      VlanPoolProfile payload) {
    if (apiAction == null || vlanPoolIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVlanPool)
            .hasSize(vlanPoolIdList.size())
            .allMatch(op -> vlanPoolIdList.contains(op.getId()))
            .allMatch(op -> op.getAction() == action(apiAction))
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVlanPool)
                    .matches(vp -> vlanPoolIdList.contains(vp.getId()));
              } else {
                assertThat(payload).isNotNull();
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVlanPool)
                    .matches(vp -> vlanPoolIdList.contains(vp.getId()))
                    .matches(vp -> vp.getAlgo() == VlanPoolAlgoEnum.VlanPool_MAC_HASH)
                    .matches(vp -> payload.getName().equals(vp.getName()))
                    .matches(vp -> payload.getVlanMembers().equals(vp.getVlanMembersList()));
              }
            }));
  }

  private void validateWlanApGroupDdccmCfgRequestMessages(CfgAction apiAction, String apGroupId,
      String vlanPoolProfileId) {
    if (apiAction == null || apGroupId == null || vlanPoolProfileId == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanGroup)
            .allMatch(op -> op.getAction() == action(apiAction))
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .allSatisfy(op -> {
              if (op.getAction() != Action.DELETE) {
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanApGroup)
                    .matches(wlanApGroup -> apGroupId.equals(wlanApGroup.getApGroupId()))
                    .matches(wlanApGroup -> vlanPoolProfileId.equals(
                        wlanApGroup.getVlanPoolId().getValue()));
              }
            }));
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, String vlanPoolProfileId,
      String wifiNetworkId) {
    if (apiAction == null || vlanPoolProfileId == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(Operations.class::cast).satisfies(ops -> {
              assertThat(ops).filteredOn(
                      op -> Index.VLAN_POOL_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
                  .hasSize(1)
                  .allMatch(op -> vlanPoolProfileId.equals(op.getId()))
                  .allMatch(op -> op.getOpType() == opType(apiAction))
                  .allSatisfy(op -> {
                    if (op.getOpType() != OpType.DEL) {
                      assertThat(op)
                          .extracting(Operations::getDocMap)
                          .matches(doc -> vlanPoolProfileId.equals(
                              doc.get(Key.ID).getStringValue()))
                          .matches(doc -> tenantId.equals(
                              doc.get(Key.TENANT_ID).getStringValue()));
                      switch (apiAction) {
                        case ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE -> {
                          assertThat(op)
                              .extracting(Operations::getDocMap)
                              .matches(
                                  doc -> !doc.get(Key.VENUE_APGROUPS).getListValue().getValuesList()
                                      .isEmpty());
                        }
                        case DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE -> {
                          assertThat(op)
                              .extracting(Operations::getDocMap)
                              .matches(doc -> doc.get(Key.VENUE_APGROUPS).getListValue().getValuesList()
                                  .isEmpty());
                        }
                      }
                    }
                  });
              if (wifiNetworkId != null) {
                switch (apiAction) {
                  case ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE -> {
                    assertThat(ops).filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
                        .hasSize(1)
                        .allMatch(op -> wifiNetworkId.equals(op.getId()))
                        .allMatch(op -> op.getOpType() == opType(apiAction))
                        .allSatisfy(op -> {
                          if (op.getOpType() != OpType.DEL) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(doc -> doc.get(Key.VLAN_POOL).hasStructValue())
                                .matches(doc -> tenantId.equals(
                                    doc.get(Key.TENANT_ID).getStringValue()));
                          }
                        });
                  }
                  case DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE -> {
                    assertThat(ops).filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
                        .hasSize(1)
                        .allMatch(op -> wifiNetworkId.equals(op.getId()))
                        .allMatch(op -> op.getOpType() == opType(apiAction))
                        .allSatisfy(op -> {
                          if (op.getOpType() != OpType.DEL) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(doc -> doc.get(Key.VLAN_POOL).getNullValue().equals(
                                    NullValue.NULL_VALUE))
                                .matches(doc -> tenantId.equals(
                                    doc.get(Key.TENANT_ID).getStringValue()));
                          }
                        });
                  }
                  default ->
                      throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
                }
              }
            }));
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    ;
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(tenantId);  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_VLAN_POOL_PROFILE_TEMPLATE -> Action.ADD;
      case UPDATE_VLAN_POOL_PROFILE_TEMPLATE,
          ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE,
          DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE -> Action.MODIFY;
      case DELETE_VLAN_POOL_PROFILE_TEMPLATE -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_VLAN_POOL_PROFILE_TEMPLATE -> OpType.ADD;
      case UPDATE_VLAN_POOL_PROFILE_TEMPLATE,
          ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE,
          DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE -> OpType.MOD;
      case DELETE_VLAN_POOL_PROFILE_TEMPLATE -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_VLAN_POOL_PROFILE_TEMPLATE -> ApiFlowNames.ADD_VLAN_POOL_PROFILE_TEMPLATE;
      case UPDATE_VLAN_POOL_PROFILE_TEMPLATE -> ApiFlowNames.UPDATE_VLAN_POOL_PROFILE_TEMPLATE;
      case DELETE_VLAN_POOL_PROFILE_TEMPLATE -> ApiFlowNames.DELETE_VLAN_POOL_PROFILE_TEMPLATE;
      case ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE ->
          ApiFlowNames.ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE;
      case DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE ->
          ApiFlowNames.DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE;
      case ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE ->
          ApiFlowNames.ACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE;
      case DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE ->
          ApiFlowNames.DEACTIVATE_VLAN_POOL_PROFILE_TEMPLATE_ON_VENUE_WIFI_NETWORK_AP_GROUP_TEMPLATE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
