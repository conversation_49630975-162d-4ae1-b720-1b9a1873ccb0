package com.ruckus.cloud.wifi.integration;

import static com.ruckus.acx.ddccm.protobuf.wifi.AntennaTypeEnum.AntennaTypeEnum_UNSET;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_FEATURE_ROLES;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static com.ruckus.cloud.wifi.kafka.publisher.ViewmodelConfigPublisher.RKS_CFG_ACTIVITY;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile;
import static com.ruckus.cloud.wifi.test.fixture.MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfileAp;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.Int32Value;
import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.AutoChannelSelectionMethod;
import com.ruckus.acx.ddccm.protobuf.wifi.ManagementVlanModeApLevelEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.UplinkModeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApDirectedMulticast;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.ApRadioParamsDual5G;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Mesh;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApIotSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApManagementTrafficVlanSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApSmartMonitor;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLoadBalancing;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AntennaTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BandModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel24Enum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.IpTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MeshModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MeshUplinkModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PoeModeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.ApAntennaTypeSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApBandModeSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApBssColoring;
import com.ruckus.cloud.wifi.eda.viewmodel.ApBssColoringSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApClientAdmissionControl;
import com.ruckus.cloud.wifi.eda.viewmodel.ApClientAdmissionControlSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApClientAdmissionControlSettingsV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.ApDirectedMulticastSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApExternalAntennaSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApExternalAntennaSettingsV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.ApIotSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.viewmodel.ApLanPortSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApLedSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApManagementTrafficVlanSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApMesh;
import com.ruckus.cloud.wifi.eda.viewmodel.ApMeshSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApNetworkSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRadioCustomization;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRadioParams24G;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRadioParams50G;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRadioParams6G;
import com.ruckus.cloud.wifi.eda.viewmodel.ApSmartMonitorSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApStickyClientSteeringSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.ApStickyClientSteeringSettingsV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.ApV1;
import com.ruckus.cloud.wifi.eda.viewmodel.ExternalAntenna;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.entitylistener.ddccm.DdccmConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

@WifiIntegrationTest
public class ConsumeUpdateApCfgRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private LanPortAdoptionDataHelper dataHelper;
  @Autowired
  private ObjectMapper objectMapper;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Deprecated(forRemoval = true)
  void validateUpdateApDirectedMulticastResult(String apId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApDirectedMulticast apDirectedMulticast,
      String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getDirectedMulticast)
        .isNotNull()
        .matches(v -> v.getWirelessEnabled().equals(apDirectedMulticast.getWirelessEnabled()),
            "Wireless enabled status is equal")
        .matches(
            v -> v.getWiredEnabled().equals(apDirectedMulticast.getWiredEnabled()),
            "Wired enabled status is equal")
        .matches(
            v -> v.getNetworkEnabled().equals(apDirectedMulticast.getNetworkEnabled()),
            "Network enabled status is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasDirectedMulticast);
  }

  @Deprecated(forRemoval = true)
  void validateUpdateApBssColoringResult(String apId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApBssColoring apBssColoring,
      String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getBssColoring)
        .isNotNull()
        .matches(v -> v.getBssColoringEnabled().equals(apBssColoring.getBssColoringEnabled()),
            "AP BSS Coloring enabled is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasBssColoring);
  }

  void validateUpdateApSnmpAgentResult(String requestId, String apId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgent viewApSnmpAgent)
      throws InvalidProtocolBufferException {
    final var ap = repositoryUtil.find(Ap.class, apId);

    var tenantId = ap.getTenant().getId();

    assertThat(ap.getApSnmpAgent()).isNotNull()
        .matches(
            a -> a.getEnableApSnmp().equals(viewApSnmpAgent.getEnableApSnmp()),
            "AP SNMP enabled status is equal")
        .extracting(ApSnmpAgent::getApSnmpAgentProfile)
        .matches(p -> p.getId().equals(viewApSnmpAgent.getApSnmpAgentProfileId()),
            "AP SNMP agent profile ID is equal");

    validateApSnmpAgentDdccmCfgRequest(requestId, tenantId,
        viewApSnmpAgent.getApSnmpAgentProfileId(), false);
    validateApSnmpAgentCmnCfgCollectorRequest(requestId, tenantId,
        viewApSnmpAgent.getApSnmpAgentProfileId(),
        ap.getApSnmpAgent().getApSnmpAgentProfile().getPolicyName(), 1);
    validateApSnmpAgentActivityMessages(ApiFlowNames.UPDATE_AP_SNMP_AGENT, apId, tenantId, requestId);
  }

  void validateUpdateApSnmpAgentUseVenueSettingsResult(String requestId, String apId, String profileId, String profileName)
      throws InvalidProtocolBufferException {
    final var ap = repositoryUtil.find(Ap.class, apId);

    var tenantId = ap.getTenant().getId();

    assertThat(ap.getApSnmpAgent()).isNotNull().matches(a -> !a.getEnableApSnmp())
        .matches(a -> a.getApSnmpAgentProfile() == null);

    validateApSnmpAgentDdccmCfgRequest(requestId, tenantId, null, false);
    validateApSnmpAgentCmnCfgCollectorRequest(requestId, tenantId, profileId, profileName, 0);
    validateApSnmpAgentActivityMessages(ApiFlowNames.UPDATE_AP_SNMP_AGENT, apId, tenantId, requestId);
  }

  @Deprecated(forRemoval = true)
  void validateResetApDirectedMulticastResult(String apId, String tenantId, String requestId)
      throws InvalidProtocolBufferException {
    final var ap = repositoryUtil.find(Ap.class, apId);
    assertThat(ap.getDirectedMulticast())
        .isNotNull()
        .matches(v -> v.getWirelessEnabled() == null)
        .matches(v -> v.getWiredEnabled() == null)
        .matches(v -> v.getNetworkEnabled() == null);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);;
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(a -> !a.hasDirectedMulticast());
  }

  void validateResetApSnmpAgentResult(String requestId, String apId, String profileId, String profileName)
      throws InvalidProtocolBufferException {
    final var ap = repositoryUtil.find(Ap.class, apId);

    var tenantId = ap.getTenant().getId();

    assertThat(ap.getApSnmpAgent()).isNotNull().matches(a -> a.getEnableApSnmp() == null)
        .matches(a -> a.getApSnmpAgentProfile() == null);

    validateApSnmpAgentDdccmCfgRequest(requestId, tenantId, null, true);
    validateApSnmpAgentCmnCfgCollectorRequest(requestId, tenantId, profileId, profileName, 0);
    validateApSnmpAgentActivityMessages(ApiFlowNames.RESET_AP_SNMP_AGENT, apId, tenantId, requestId);
  }

  void validateApSnmpAgentDdccmCfgRequest(String requestId, String tenantId, String profileId,
      boolean reset)
      throws InvalidProtocolBufferException {
    var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage).extracting(KafkaProtoMessage::getPayload).isNotNull().isNotNull();
    var ddccmRequest = ddccmCfgRequestMessage.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .hasSize(2).filteredOn(op -> op.hasAp()).first()
        .matches(op -> op.getAction() == Action.MODIFY)
        .satisfies(op -> assertThat(op)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
            .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
            .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
            .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .satisfies(ap -> {
          if (reset) {
            assertThat(ap).matches(a -> !a.hasSnmpAgent());
          } else {
            if (profileId == null) { // use venue settings
              assertThat(ap).matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasSnmpAgent)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getSnmpAgent)
                  .matches(a -> !a.getEnabled())
                  .matches(a -> !a.hasApSnmpAgentProfileId());
            } else {
              assertThat(ap).matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasSnmpAgent)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getSnmpAgent)
                  .matches(a -> a.getEnabled())
                  .matches(a -> a.hasApSnmpAgentProfileId())
                  .matches(a -> profileId
                      .equals(a.getApSnmpAgentProfileId().getValue()));
            }
          }
        });
  }

  void validateApSnmpAgentCmnCfgCollectorRequest(String requestId, String tenantId,
      String profileId, String profileName, int expectedAps)
      throws InvalidProtocolBufferException {
    var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(msg -> msg.getTenantId().equals(tenantId))
        .matches(msg -> msg.getRequestId().equals(requestId))
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> EsConstants.Index.AP_SNMP_AGENT_PROFILE.equals(op.getIndex()))
        .hasSize(1).first()
        .matches(op -> op.getOpType() == OpType.MOD)
        .matches(op -> profileId.equals(op.getId()))
        .satisfies(op -> {
          assertThat(op)
              .extracting(Operations::getDocMap)
              .matches(doc -> profileId
                  .equals(doc.get(EsConstants.Key.ID).getStringValue()))
              .matches(doc -> profileName
                  .equals(doc.get(EsConstants.Key.NAME).getStringValue()))
              .matches(doc -> doc.get("v2Agents").getListValue().getValuesCount() == 1)
              .matches(doc -> doc.get("v3Agents").getListValue().getValuesCount() == 1)
              .matches(doc -> doc.get("venues").getListValue().getValuesCount() == 0)
              .matches(doc -> doc.get("aps").getListValue().getValuesCount() == expectedAps);
        });
  }

  private void validateApSnmpAgentActivityMessages(String apiFlowName, String apId,
      String tenantId, String requestId) {
    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage =  messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));
  }

  @Deprecated(forRemoval = true)
  void validateUpdateApMeshResult(String apId, ApMesh apMesh, String tenantId, String requestId)
      throws InvalidProtocolBufferException {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getMesh)
        .isNotNull()
        .matches(v -> v.getMeshMode().equals(apMesh.getMeshMode()),
            "Mesh mode is equal")
        .matches(
            v -> v.getUplinkMode().equals(apMesh.getUplinkMode()),
            "Uplink mode is equal")
        .matches(
            v -> v.getUplinkMacAddresses().containsAll(apMesh.getUplinkMacAddresses()),
            "Uplink MAC addresses are equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasApMesh)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getApMesh)
        .matches(m -> m.getMeshMode().equals(com.ruckus.acx.ddccm.protobuf.wifi.MeshModeEnum.MESH))
        .matches(m -> m.getUplinkMode().equals(UplinkModeEnum.MANUAL))
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApMesh::getMeshDownlinkEnabled)
        .matches(m -> m.getUplinkEntryList().equals(
            StringUtils.join(apMesh.getUplinkMacAddresses(), ',')));
  }

  @Deprecated(forRemoval = true)
  void validateUpdateApClientAdmissionControlResult(
      String apId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApClientAdmissionControl apClientAdmissionControl,
      String tenantId,
      String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getClientAdmissionControl)
        .isNotNull()
        .matches(
            v -> v.getEnable24G().equals(apClientAdmissionControl.getEnable24G()),
            "DB ClientAdmissionControl Enable24G is equal")
        .matches(
            v -> v.getEnable50G().equals(apClientAdmissionControl.getEnable50G()),
            "DB ClientAdmissionControl Enable50G is equal")
        .matches(
            v -> v.getMinClientCount24G().equals(apClientAdmissionControl.getMinClientCount24G()),
            "DB ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            v -> v.getMinClientCount50G().equals(apClientAdmissionControl.getMinClientCount50G()),
            "DB ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            v -> v.getMaxRadioLoad24G().equals(apClientAdmissionControl.getMaxRadioLoad24G()),
            "DB ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            v -> v.getMaxRadioLoad50G().equals(apClientAdmissionControl.getMaxRadioLoad50G()),
            "DB ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            v ->
                v.getMinClientThroughput24G()
                    .equals(apClientAdmissionControl.getMinClientThroughput24G()),
            "DB ClientAdmissionControl MinClientThroughput24G is equal")
        .matches(
            v ->
                v.getMinClientThroughput50G()
                    .equals(apClientAdmissionControl.getMinClientThroughput50G()),
            "DB ClientAdmissionControl MinClientThroughput50G is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio24)
        .extracting(
            radioCustomization -> radioCustomization.getRadio24().getChannelSelectionMethod())
        .matches(
            channelSelectionMethod ->
                channelSelectionMethod.equals(
                    AutoChannelSelectionMethod.AutoChannelSelectionMethod_UNSET));

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio24)
        .extracting(
            radioCustomization -> radioCustomization.getRadio24().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled() == apClientAdmissionControl.getEnable24G(),
            "DDCCM ClientAdmissionControl Enable24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == apClientAdmissionControl.getMinClientCount24G(),
            "DDCCM ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == apClientAdmissionControl.getMaxRadioLoad24G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == apClientAdmissionControl.getMinClientThroughput24G(),
            "DDCCM ClientAdmissionControl MinClientThroughput24G is equal");

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio50)
        .extracting(
            radioCustomization -> radioCustomization.getRadio50().getChannelSelectionMethod())
        .matches(
            channelSelectionMethod ->
                channelSelectionMethod.equals(
                    AutoChannelSelectionMethod.AutoChannelSelectionMethod_UNSET));

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio50)
        .extracting(
            radioCustomization -> radioCustomization.getRadio50().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled() == apClientAdmissionControl.getEnable50G(),
            "DDCCM ClientAdmissionControl Enable50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == apClientAdmissionControl.getMinClientCount50G(),
            "DDCCM ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == apClientAdmissionControl.getMaxRadioLoad50G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == apClientAdmissionControl.getMinClientThroughput50G(),
            "DDCCM ClientAdmissionControl MinClientThroughput50G is equal");
  }

  @Deprecated(forRemoval = true)
  void validateResetApClientAdmissionControlResult(
      String apId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApClientAdmissionControl apClientAdmissionControl,
      String tenantId,
      String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getClientAdmissionControl)
        .isNotNull()
        .matches(v -> v.getEnable24G() == null, "DB ClientAdmissionControl Enable24G is equal")
        .matches(v -> v.getEnable50G() == null, "DB ClientAdmissionControl Enable50G is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertFalse(
        ddccmRequest
            .getOperationsList()
            .get(0)
            .getAp()
            .getRadioCustomization()
            .getRadio24()
            .hasClientAdmissionControl());
    assertFalse(
        ddccmRequest
            .getOperationsList()
            .get(0)
            .getAp()
            .getRadioCustomization()
            .getRadio50()
            .hasClientAdmissionControl());
  }

  void validateUpdateApRadioCustomizationUseVenueResult(String apId, String tenantId,
      String requestId, boolean isEnableFF) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);

    if (isEnableFF) {
      assertThat(apFromDb)
          .isNotNull()
          .extracting(Ap::getRadioCustomization)
          .isNotNull()
          .matches(v -> v.getEnable24G() != null, "enable24G is not null")
          .matches(v -> v.getEnable50G() != null, "enable50G is not null")
          .matches(v -> v.getApRadioParams24G().getUseVenueSettings())
          .matches(v -> v.getApRadioParams50G().getUseVenueSettings())
          .matches(v -> v.getApRadioParams6G().getUseVenueSettings())
          .extracting(
              com.ruckus.cloud.wifi.eda.servicemodel.ApRadioCustomization::getApRadioParamsDual5G)
          .matches(ApRadioParamsDual5G::getUseVenueEnabled)
          .matches(v -> v.getRadioParamsLower5G().getUseVenueSettings())
          .matches(v -> v.getRadioParamsUpper5G().getUseVenueSettings());
    } else {
      assertThat(apFromDb)
          .isNotNull()
          .extracting(Ap::getRadioCustomization)
          .isNotNull()
          .matches(v -> v.getEnable24G() == null, "enable24G is null")
          .matches(v -> v.getEnable50G() == null, "enable50G is null")
          .matches(v -> v.getEnable6G() == null, "enable6G is null")
          .extracting(
              com.ruckus.cloud.wifi.eda.servicemodel.ApRadioCustomization::getApRadioParamsDual5G)
          .matches(v -> v.getLower5gEnabled() == null,
              "lower5gEnabled is null")
          .matches(v -> v.getUpper5gEnabled() == null,
              "upper5gEnabled is null");
    }

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(a -> !a.hasRadioCustomization());
  }

  void validateUpdateApRadioCustomizationCustomizeResult(String apId, String tenantId,
      String requestId, ApRadioCustomization apRadioCustomization,
      boolean supportTriRadio, boolean supportDual5G) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getRadioCustomization)
        .isNotNull()
        .matches(v -> v.getEnable24G()
            .equals(Optional.ofNullable(apRadioCustomization.getEnable24G()).orElse(true)))
        .matches(v -> v.getEnable50G()
            .equals(Optional.ofNullable(apRadioCustomization.getEnable50G()).orElse(true)))
        .matches(v -> v.getEnable6G()
            .equals(Optional.ofNullable(apRadioCustomization.getEnable6G()).orElse(true)
                && supportTriRadio))
        .matches(v -> v.getApRadioParams24G().getAllowedChannels() != null)
        .matches(v -> v.getApRadioParams50G().getAllowedChannels() == null)
        .matches(v -> v.getApRadioParams6G().getAllowedChannels() != null)
        .extracting(
            com.ruckus.cloud.wifi.eda.servicemodel.ApRadioCustomization::getApRadioParamsDual5G)
        .matches(v -> v.getLower5gEnabled()
            .equals(Optional.ofNullable(
                apRadioCustomization.getApRadioParamsDual5G().getLower5gEnabled()).orElse(true)
                && supportDual5G)).isNotNull()
        .matches(v -> v.getUpper5gEnabled()
            .equals(Optional.ofNullable(
                apRadioCustomization.getApRadioParamsDual5G().getUpper5gEnabled()).orElse(true)
                && supportDual5G)).isNotNull()
        .matches(v -> v.getRadioParamsLower5G().getAllowedChannels() == null)
        .matches(v -> v.getRadioParamsUpper5G().getAllowedChannels() == null);

    assertThat(apFromDb.getRadioCustomization())
        .matches(v -> !v.getApRadioParams24G().getUseVenueSettings())
        .matches(v -> !v.getApRadioParams50G().getUseVenueSettings())
        .matches(v -> !v.getApRadioParams6G().getUseVenueSettings())
        .extracting(
            com.ruckus.cloud.wifi.eda.servicemodel.ApRadioCustomization::getApRadioParamsDual5G)
        .matches(v -> !v.getUseVenueEnabled())
        .matches(v -> !v.getRadioParamsLower5G().getUseVenueSettings())
        .matches(v -> !v.getRadioParamsUpper5G().getUseVenueSettings());

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(r -> r.getRadio24().getWlanServiceEnabled().getValue())
        .matches(r -> r.getRadio24().getAllowedChannelListList().equals(Arrays.asList(1, 2)))
        .matches(r -> !r.getRadio50().getWlanServiceEnabled().getValue())
        .matches(r -> r.getRadio60().getWlanServiceEnabled().getValue())
        .matches(r -> r.getRadio60().getAllowedChannelListList().equals(Arrays.asList(1, 5)))
        .matches(r -> !r.getRadio50Upper().getWlanServiceEnabled().getValue())
        .matches(r -> !r.getRadio50Lower().getWlanServiceEnabled().getValue());
  }

  void validateUpdateApMulticastDnsProxyServiceProfileResult(String apId, String tenantId,
      String requestId, String multicastDnsProxyServiceProfileId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getMulticastDnsProxyServiceProfileAp)
        .satisfies(profileApList -> {
          assertThat(profileApList)
              .hasSize(1);
        });

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasBonjourGateway)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getBonjourGateway)
        .matches(apBonjourGateway -> multicastDnsProxyServiceProfileId.equals(apBonjourGateway.getId()));
  }

  void validateUpdateApRadioCustomizationPartialResult(String apId, String tenantId,
      String requestId, ApRadioCustomization apRadioCustomization) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getRadioCustomization)
        .isNotNull()
        .matches(v -> !v.getEnable24G().equals(apRadioCustomization.getEnable24G())) // null -> true
        .matches(v -> !v.getApRadioParams50G().getUseVenueSettings().equals(
            apRadioCustomization.getApRadioParams50G()
                .getUseVenueSettings())) // inner useVenueSettings is null, outer useVenueSettings is true
        .matches(v -> v.getApRadioParams24G().getAllowedChannels()
            .equals(apRadioCustomization.getApRadioParams24G().getAllowedChannels()))
        .matches(v -> v.getApRadioParams6G().getAllowedChannels() != null)
        .extracting(
            com.ruckus.cloud.wifi.eda.servicemodel.ApRadioCustomization::getApRadioParamsDual5G)
        .matches(v -> !v.getUseVenueEnabled().equals(
            apRadioCustomization.getApRadioParamsDual5G().getUseVenueEnabled())) // null -> true
        .matches(v -> !v.getRadioParamsUpper5G().getUseVenueSettings().equals(
            apRadioCustomization.getApRadioParamsDual5G().getRadioParamsUpper5G()
                .getUseVenueSettings())); // inner useVenueSettings is null, outer useVenueSettings is true;

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(r -> !r.hasRadio50())
        .matches(r -> !r.hasRadio50Lower())
        .matches(r -> !r.hasRadio50Upper())
        .matches(r -> !r.getOldDual5GEnabled())
        .matches(r -> !r.hasDual5GEnabled())
        .matches(r -> r.getRadio24().getWlanServiceEnabled().getValue())
        .matches(r -> r.getRadio24().getAllowedChannelListList().equals(Arrays.asList(1, 2)))
        .matches(r -> !r.getRadio60().getWlanServiceEnabled().getValue());
  }

  void validateResetApRadioCustomizationResult(String apId, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getRadioCustomization)
        .isNotNull()
        .matches(v -> v.getEnable24G() == null,"enable24G is null")
        .matches(v -> v.getEnable50G() == null,"enable50G is null")
        .matches(v -> v.getEnable6G() == null,"enable6G is null")
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.ApRadioCustomization::getApRadioParamsDual5G)
        .matches(v -> v.getLower5gEnabled() == null,
            "lower5gEnabled is null")
        .matches(v -> v.getUpper5gEnabled() == null,
            "upper5gEnabled is null");

    // validate new fields
    assertThat(apFromDb.getRadioCustomization())
        .matches(v -> v.getApRadioParams24G().getUseVenueSettings())
        .matches(v -> v.getApRadioParams50G().getUseVenueSettings())
        .matches(v -> v.getApRadioParams6G().getUseVenueSettings())
        .extracting(
            com.ruckus.cloud.wifi.eda.servicemodel.ApRadioCustomization::getApRadioParamsDual5G)
        .matches(ApRadioParamsDual5G::getUseVenueEnabled)
        .matches(v -> v.getRadioParamsLower5G().getUseVenueSettings())
        .matches(v -> v.getRadioParamsUpper5G().getUseVenueSettings());

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(a -> !a.hasRadioCustomization());
  }

  void validateUpdateApManagementVlanSettingsResult(
      String apId,
      ApManagementTrafficVlanSettings apManagementTrafficVlanSettings,
      String tenantId,
      String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getApManagementVlan)
        .isNotNull()
        .matches(
            v ->
                v.getVlanOverrideEnabled()
                    .equals(apManagementTrafficVlanSettings.getVlanOverrideEnabled()),
            "DB ApManagementVlan VlanOverrideEnabled is equal")
        .matches(
            v -> v.getVlanId().equals(apManagementTrafficVlanSettings.getVlanId()),
            "DB ApManagementVlan VlanId is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(
            v ->
                apManagementTrafficVlanSettings.getVlanOverrideEnabled()
                    ? v.getApManagementVlanMode()
                        .equals(
                            ManagementVlanModeApLevelEnum
                                .ManagementVlanModeApLevelEnum_USER_DEFINED)
                    : v.getApManagementVlanMode()
                        .equals(ManagementVlanModeApLevelEnum.ManagementVlanModeApLevelEnum_KEEP),
            "DDCCM ApManagementVlan VlanOverrideEnabled is equal")
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasApManagementVlanId)
        .matches(
            v ->
                v.getApManagementVlanId()
                    .equals(Int32Value.of(apManagementTrafficVlanSettings.getVlanId())),
            "DDCCM ApManagementVlan VlanId is equal");

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull();
    assertThat(cmnCfgCollectorMessage.getHeaders())
        .anyMatch(header -> header.key().equals(RKS_CFG_ACTIVITY));
    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(msg -> msg.getTenantId().equals(tenantId))
        .matches(msg -> msg.getRequestId().equals(requestId))
        .extracting(ViewmodelCollector::getOperationsList)
        .asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> EsConstants.Index.DEVICE.equals(op.getIndex()))
        .hasSize(1)
        .first()
        .matches(op -> op.getOpType() == OpType.MOD)
        .matches(op -> apFromDb.getId().equals(op.getId()))
        .satisfies(
            op -> {
              assertThat(op)
                  .extracting(Operations::getDocMap)
                  .extracting(docMap -> docMap.get(EsConstants.Key.AP_STATUS_DATA))
                  .extracting(struct -> struct.getStructValue().getFieldsMap())
                  .extracting(apStatusDataMap -> apStatusDataMap.get(EsConstants.Key.AP_SYSTEM))
                  .extracting(struct -> struct.getStructValue().getFieldsMap())
                  .extracting(
                      apStatusDataMap -> apStatusDataMap.get(EsConstants.Key.MANAGEMENT_VLAN))
                  .matches(
                      managementVlan -> managementVlan.getKindCase() == Value.KindCase.NUMBER_VALUE)
                  .extracting(p -> (short) p.getNumberValue())
                  .isEqualTo(apFromDb.getApManagementVlan().getVlanId());
            });
  }

  void validateResetApManagementVlanSettingsResult(String apId, String tenantId, String requestId, Short venueVlanId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getApManagementVlan)
        .isNotNull()
        .matches(
            v -> v.getVlanOverrideEnabled() == null,
            "DB ApManagementVlan VlanOverrideEnabled is equal")
        .matches(v -> v.getVlanId() == 1, "DB ApManagementVlan VlanId is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertFalse(ddccmRequest.getOperationsList().get(0).getAp().hasApManagementVlanId());

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull();
    assertThat(cmnCfgCollectorMessage.getHeaders())
        .anyMatch(header -> header.key().equals(RKS_CFG_ACTIVITY));
    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(msg -> msg.getTenantId().equals(tenantId))
        .matches(msg -> msg.getRequestId().equals(requestId))
        .extracting(ViewmodelCollector::getOperationsList)
        .asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> EsConstants.Index.DEVICE.equals(op.getIndex()))
        .hasSize(1)
        .first()
        .matches(op -> op.getOpType() == OpType.MOD)
        .matches(op -> apFromDb.getId().equals(op.getId()))
        .satisfies(
            op -> {
              assertThat(op)
                  .extracting(Operations::getDocMap)
                  .extracting(docMap -> docMap.get(EsConstants.Key.AP_STATUS_DATA))
                  .extracting(struct -> struct.getStructValue().getFieldsMap())
                  .extracting(apStatusDataMap -> apStatusDataMap.get(EsConstants.Key.AP_SYSTEM))
                  .extracting(struct -> struct.getStructValue().getFieldsMap())
                  .extracting(
                      apStatusDataMap -> apStatusDataMap.get(EsConstants.Key.MANAGEMENT_VLAN))
                  .matches(
                      managementVlan -> managementVlan.getKindCase() == Value.KindCase.NUMBER_VALUE)
                  .extracting(p -> (short) p.getNumberValue())
                  .isEqualTo(apFromDb.getApGroup().getVenue().getApManagementVlan().getVlanId());
            });
    assertEquals(apFromDb.getApGroup().getVenue().getApManagementVlan().getVlanId(), venueVlanId);
  }

  void validateUpdateApResult(String apId, String tenantId, String requestId, ApV1 updateAp) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .matches(v -> v.getName().equals(updateAp.getName()),
            "AP name is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(m -> m.getName().equals(updateAp.getName()));
  }

  void validateUpdateApLanPortSettingsResult(String apId,
      ApLanPortSettings apLanPortSettings, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .matches(v -> v.getPoeOut().equals(apLanPortSettings.getPoeOut()),
            "POE out is equal")
        .matches(
            v -> v.getPoeMode().equals(apLanPortSettings.getPoeMode()),
            "POE mode is equal")
        .matches(
            v -> v.getLanPorts().size() == apLanPortSettings.getLanPorts().size(),
            "LanPorts is equal");

    assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId)).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(3).filteredOn(Operation::hasAp)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> m.getLanPortCount() == apLanPortSettings.getLanPorts().size());

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(
            ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().singleElement()
        .satisfies(op -> {
          assertThat(op.getIndex()).isEqualTo(Index.ETHERNET_PORT_PROFILE_INDEX_NAME);
          assertThat(op.getOpType()).isEqualTo(OpType.ADD);
          assertThat(op.getDocMap())
              .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
              .containsEntry(Key.TYPE, ValueUtils.stringValue("TRUNK"))
              .containsEntry(Key.UNTAG_ID, ValueUtils.numberValue(1))
              .containsEntry(Key.VLAN_MEMBERS, ValueUtils.stringValue("1-4094"))
              .containsEntry(Key.NAME, ValueUtils.stringValue("TRUNK_1_1-4094_3"))
              .containsEntry(Key.IS_DEFAULT, ValueUtils.boolValue(false))
              .containsEntry(Key.AUTH_TYPE, ValueUtils.stringValue(ApLanPortAuthTypeEnum.DISABLED.name()))
              .containsEntry(Key.AUTH_RADIUS_ID, ValueUtils.nullValue())
              .containsEntry(Key.ACCOUNTING_RADIUS_ID, ValueUtils.nullValue())
              .containsEntry(Key.VENUE_IDS, ValueUtils.listValue(List.of()))
              .containsEntry(Key.VENUE_ACTIVATIONS, ValueUtils.listValue(List.of()))
              .hasEntrySatisfying(Key.AP_SERIAL_NUMBERS, apSerialNumbers -> {
                assertThat(apSerialNumbers.getKindCase()).isEqualTo(Value.KindCase.LIST_VALUE);
                assertThat(apSerialNumbers.hasListValue()).isTrue();
                assertThat(apSerialNumbers.getListValue().getValuesList()).isNotEmpty()
                    .singleElement().satisfies(value -> {
                      assertThat(value.getKindCase()).isEqualTo(Value.KindCase.STRING_VALUE);
                      assertThat(value.getStringValue()).isEqualTo(apId);
                    });
              })
              .hasEntrySatisfying(Key.AP_ACTIVATIONS, apActivations -> {
                assertThat(apActivations.getKindCase()).isEqualTo(Value.KindCase.LIST_VALUE);
                assertThat(apActivations.hasListValue()).isTrue();
                assertThat(apActivations.getListValue().getValuesList()).isNotEmpty()
                    .hasSize(apLanPortSettings.getLanPorts().size())
                    .allSatisfy(value -> {
                      assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                      assertThat(value.hasStructValue()).isTrue();
                      assertThat(value.getStructValue()).isNotNull()
                          .satisfies(structValue -> assertThat(structValue.getFieldsMap())
                              .containsEntry(Key.VENUE_ID,
                                  ValueUtils.stringValue(apFromDb.getApGroup().getVenue().getId()))
                              .containsEntry(Key.AP_SERIAL_NUMBER, ValueUtils.stringValue(apId)));
                    })
                    .map(value -> value.getStructValue().getFieldsOrThrow(Key.PORT_ID))
                    .containsExactlyInAnyOrderElementsOf(
                        () -> apLanPortSettings.getLanPorts().stream().map(ApLanPort::getPortId)
                            .map(ValueUtils::stringValue).iterator());
              });
        });
  }

  void validateResetApLanPortSettingsResult(String apId,
      String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .matches(v -> v.getPoeOut() == null,
            "POE out is null")
        .matches(
            v -> v.getPoeMode() == null,
            "POE mode is null")
        .matches(
            v -> CollectionUtils.isEmpty(v.getLanPorts()),
            "LanPorts is empty");

    assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId)).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> !m.hasPoeOutPortEnabled())
        .matches(m -> m.getPoeModeSetting().equals(com.ruckus.acx.ddccm.protobuf.wifi.PoeModeEnum.PoeModeEnum_UNSET))
        .matches(m -> m.getLanPortList().isEmpty());

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(
            ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().singleElement()
        .satisfies(op -> {
          assertThat(op.getIndex()).isEqualTo(Index.ETHERNET_PORT_PROFILE_INDEX_NAME);
          assertThat(op.getOpType()).isEqualTo(OpType.MOD);
          assertThat(op.getDocMap())
              .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
              .containsEntry(Key.TYPE, ValueUtils.stringValue("TRUNK"))
              .containsEntry(Key.UNTAG_ID, ValueUtils.numberValue(1))
              .containsEntry(Key.VLAN_MEMBERS, ValueUtils.stringValue("1-4094"))
              .containsEntry(Key.NAME, ValueUtils.stringValue("TRUNK_1_1-4094_3"))
              .containsEntry(Key.IS_DEFAULT, ValueUtils.boolValue(false))
              .containsEntry(Key.AUTH_TYPE, ValueUtils.stringValue(ApLanPortAuthTypeEnum.DISABLED.name()))
              .containsEntry(Key.AUTH_RADIUS_ID, ValueUtils.nullValue())
              .containsEntry(Key.ACCOUNTING_RADIUS_ID, ValueUtils.nullValue())
              .containsEntry(Key.VENUE_IDS, ValueUtils.listValue(List.of()))
              .containsEntry(Key.VENUE_ACTIVATIONS, ValueUtils.listValue(List.of()))
              .containsEntry(Key.AP_SERIAL_NUMBERS, ValueUtils.listValue(List.of()))
              .containsEntry(Key.AP_ACTIVATIONS, ValueUtils.listValue(List.of()));
        });
  }

  void validateUpdateApLedSettingsResult(String apId,
      ApLedSettings apLedSettings, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .matches(v -> v.getLedOn().equals(apLedSettings.getLedEnabled()),
            "LED on is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2).filteredOn(Operation::hasAp)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> m.getLedStatusEnabled().getValue() == apLedSettings.getLedEnabled());
  }

  void validateResetApLedSettingsResult(String apId, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .matches(v -> v.getLedOn() == null, "LED on is null");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> !m.hasLedStatusEnabled());
  }

  void validateUpdateApExternalAntennaSettingsResult(String apId,
                                                     ApExternalAntennaSettingsV1_1 apExternalAntennaSettingsV1_1,
                                                     String tenantId,
                                                     String requestId) {
    ApExternalAntennaSettings apExternalAntennaSettings = new ApExternalAntennaSettings();
    apExternalAntennaSettings.setExternalAntenna(apExternalAntennaSettingsV1_1.getExternalAntenna());
    apExternalAntennaSettings.setUseVenueSettings(apExternalAntennaSettingsV1_1.getUseVenueOrApGroupSettings());
    this.validateUpdateApExternalAntennaSettingsResult(apId, apExternalAntennaSettings, tenantId, requestId);
  }

  void validateUpdateApExternalAntennaSettingsResult(String apId,
      ApExternalAntennaSettings apExternalAntennaSettings, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .extracting(ApModelSpecific::getExternalAntenna)
        .isNotNull()
        .matches(v -> v.getExternalAntenna24enable()
                .equals(apExternalAntennaSettings.getExternalAntenna().getEnable24G()),
            "enable 24G is equal")
        .matches(v -> v.getExternalAntenna50enable()
                .equals(apExternalAntennaSettings.getExternalAntenna().getEnable50G()),
            "enable 50G is equal")
        .matches(v -> v.getExternalAntenna24dbi()
                .equals(apExternalAntennaSettings.getExternalAntenna().getGain24G()),
            "gain 24G is equal")
        .matches(v -> v.getExternalAntenna50dbi()
                .equals(apExternalAntennaSettings.getExternalAntenna().getGain50G()),
            "gain 50G is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2).filteredOn(Operation::hasAp)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> m.getExternalAntennaCount() == 2);
  }

  void validateResetApExternalAntennaSettingsResult(String apId, String tenantId,
      String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .extracting(ApModelSpecific::getExternalAntenna)
        .isNull();

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> m.getExternalAntennaCount() == 0);
  }

  void validateUpdateApBandModeResult(String apId,
      ApBandModeSettings apBandModeSettings, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .matches(v -> v.getBandMode().equals(apBandModeSettings.getBandMode()),
            "band mode is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2).filteredOn(Operation::hasAp)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> (m.getBandCombinationMode().getValue()).equals(DdccmConstants.BAND_MODE_DUAL));
  }

  void validateResetApBandModeResult(String apId, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .matches(v -> v.getBandMode() == null, "band mode is null");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> !m.hasBandCombinationMode());
  }

  void validateUpdateApAntennaTypeResult(String apId,
      ApAntennaTypeSettings apAntennaTypeSettings, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .matches(v -> v.getAntennaType().equals(apAntennaTypeSettings.getAntennaType()),
            "antenna type is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2).filteredOn(Operation::hasAp)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> m.getAntennaType().equals(com.ruckus.acx.ddccm.protobuf.wifi.AntennaTypeEnum.Sector));
  }

  void validateResetApAntennaTypeResult(String apId, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getModelSpecific)
        .isNotNull()
        .matches(v -> v.getAntennaType() == null, "antenna type is null");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .matches(m -> m.getAntennaType().equals(AntennaTypeEnum_UNSET));
  }

  void validateUpdateApDirectedMulticastSettingsResult(String apId,
      ApDirectedMulticastSettings apDirectedMulticast,
      String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getDirectedMulticast)
        .isNotNull()
        .matches(v -> v.getWirelessEnabled().equals(apDirectedMulticast.getWirelessEnabled()),
            "Wireless enabled status is equal")
        .matches(
            v -> v.getWiredEnabled().equals(apDirectedMulticast.getWiredEnabled()),
            "Wired enabled status is equal")
        .matches(
            v -> v.getNetworkEnabled().equals(apDirectedMulticast.getNetworkEnabled()),
            "Network enabled status is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasDirectedMulticast);
  }

  void validateResetApDirectedMulticastSettingsResult(String apId, String tenantId,
      String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getDirectedMulticast)
        .isNotNull()
        .matches(v -> v.getWirelessEnabled() == null,
            "Wireless enabled status is null")
        .matches(
            v -> v.getWiredEnabled() == null,
            "Wired enabled status is null")
        .matches(
            v -> v.getNetworkEnabled() == null,
            "Network enabled status is null");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(a -> !a.hasDirectedMulticast());
  }

  void validateUpdateApNetworkSettingsResult(String apId,
      ApNetworkSettings apNetworkSettings,
      String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getNetworkSettings)
        .isNotNull()
        .matches(v -> v.getIpType().equals(apNetworkSettings.getIpType()),
            "IP type is equal")
        .matches(
            v -> v.getIp().equals(apNetworkSettings.getIp()),
            "IP is equal")
        .matches(v -> v.getNetmask().equals(apNetworkSettings.getNetmask()),
            "netmask is equal")
        .matches(
            v -> v.getGateway().equals(apNetworkSettings.getGateway()),
            "gateway is equal")
        .matches(
            v -> v.getPrimaryDnsServer().equals(apNetworkSettings.getPrimaryDnsServer()),
            "primary DNS server is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasNetworkSettings);
  }

  void validateUpdateApMeshSettingsResult(String apId, ApMeshSettings apMesh, String tenantId, String requestId)
      throws InvalidProtocolBufferException {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getMesh)
        .isNotNull()
        .matches(v -> v.getMeshMode().equals(apMesh.getMeshMode()),
            "Mesh mode is equal")
        .matches(
            v -> v.getUplinkMode().equals(apMesh.getUplinkMode()),
            "Uplink mode is equal")
        .matches(
            v -> v.getUplinkMacAddresses().containsAll(apMesh.getUplinkMacAddresses()),
            "Uplink MAC addresses are equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasApMesh)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getApMesh)
        .matches(m -> m.getMeshMode().equals(com.ruckus.acx.ddccm.protobuf.wifi.MeshModeEnum.MESH))
        .matches(m -> m.getUplinkMode().equals(UplinkModeEnum.MANUAL))
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApMesh::getMeshDownlinkEnabled)
        .matches(m -> m.getUplinkEntryList().equals(
            StringUtils.join(apMesh.getUplinkMacAddresses(), ',')));
  }

  void validateUpdateApBssColoringSettingsResult(String apId,
      ApBssColoringSettings apBssColoring,
      String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getBssColoring)
        .isNotNull()
        .matches(v -> v.getBssColoringEnabled().equals(apBssColoring.getBssColoringEnabled()),
            "AP BSS Coloring enabled is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasBssColoring);
  }

  void validateResetApBssColoringSettingsResult(String apId, String tenantId, String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getBssColoring)
        .isNotNull()
        .matches(v -> v.getBssColoringEnabled() == null,
            "AP BSS Coloring enabled is null");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(a -> !a.hasBssColoring());
  }

  void validateUpdateApClientAdmissionControlSettingsResult(
      String apId,
      ApClientAdmissionControlSettings apClientAdmissionControl,
      String tenantId,
      String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getClientAdmissionControl)
        .isNotNull()
        .matches(
            v -> v.getEnable24G().equals(apClientAdmissionControl.getEnable24G()),
            "DB ClientAdmissionControl Enable24G is equal")
        .matches(
            v -> v.getEnable50G().equals(apClientAdmissionControl.getEnable50G()),
            "DB ClientAdmissionControl Enable50G is equal")
        .matches(
            v -> v.getMinClientCount24G().equals(apClientAdmissionControl.getMinClientCount24G()),
            "DB ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            v -> v.getMinClientCount50G().equals(apClientAdmissionControl.getMinClientCount50G()),
            "DB ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            v -> v.getMaxRadioLoad24G().equals(apClientAdmissionControl.getMaxRadioLoad24G()),
            "DB ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            v -> v.getMaxRadioLoad50G().equals(apClientAdmissionControl.getMaxRadioLoad50G()),
            "DB ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            v ->
                v.getMinClientThroughput24G()
                    .equals(apClientAdmissionControl.getMinClientThroughput24G()),
            "DB ClientAdmissionControl MinClientThroughput24G is equal")
        .matches(
            v ->
                v.getMinClientThroughput50G()
                    .equals(apClientAdmissionControl.getMinClientThroughput50G()),
            "DB ClientAdmissionControl MinClientThroughput50G is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio24)
        .extracting(
            radioCustomization -> radioCustomization.getRadio24().getChannelSelectionMethod())
        .matches(
            channelSelectionMethod ->
                channelSelectionMethod.equals(
                    AutoChannelSelectionMethod.AutoChannelSelectionMethod_UNSET));

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio24)
        .extracting(
            radioCustomization -> radioCustomization.getRadio24().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled() == apClientAdmissionControl.getEnable24G(),
            "DDCCM ClientAdmissionControl Enable24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == apClientAdmissionControl.getMinClientCount24G(),
            "DDCCM ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == apClientAdmissionControl.getMaxRadioLoad24G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == apClientAdmissionControl.getMinClientThroughput24G(),
            "DDCCM ClientAdmissionControl MinClientThroughput24G is equal");

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio50)
        .extracting(
            radioCustomization -> radioCustomization.getRadio50().getChannelSelectionMethod())
        .matches(
            channelSelectionMethod ->
                channelSelectionMethod.equals(
                    AutoChannelSelectionMethod.AutoChannelSelectionMethod_UNSET));

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio50)
        .extracting(
            radioCustomization -> radioCustomization.getRadio50().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled() == apClientAdmissionControl.getEnable50G(),
            "DDCCM ClientAdmissionControl Enable50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == apClientAdmissionControl.getMinClientCount50G(),
            "DDCCM ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == apClientAdmissionControl.getMaxRadioLoad50G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == apClientAdmissionControl.getMinClientThroughput50G(),
            "DDCCM ClientAdmissionControl MinClientThroughput50G is equal");
  }

  void validateUpdateApClientAdmissionControlSettingsResult(
      String apId,
      ApClientAdmissionControlSettingsV1_1 apClientAdmissionControl,
      String tenantId,
      String requestId) {
    final var apFromDb = repositoryUtil.find(Ap.class, apId);
    assertThat(apFromDb)
        .isNotNull()
        .extracting(Ap::getClientAdmissionControl)
        .isNotNull()
        .matches(
            v -> v.getEnable24G().equals(apClientAdmissionControl.getEnable24G()),
            "DB ClientAdmissionControl Enable24G is equal")
        .matches(
            v -> v.getEnable50G().equals(apClientAdmissionControl.getEnable50G()),
            "DB ClientAdmissionControl Enable50G is equal")
        .matches(
            v -> v.getMinClientCount24G().equals(apClientAdmissionControl.getMinClientCount24G()),
            "DB ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            v -> v.getMinClientCount50G().equals(apClientAdmissionControl.getMinClientCount50G()),
            "DB ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            v -> v.getMaxRadioLoad24G().equals(apClientAdmissionControl.getMaxRadioLoad24G()),
            "DB ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            v -> v.getMaxRadioLoad50G().equals(apClientAdmissionControl.getMaxRadioLoad50G()),
            "DB ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            v ->
                v.getMinClientThroughput24G()
                    .equals(apClientAdmissionControl.getMinClientThroughput24G()),
            "DB ClientAdmissionControl MinClientThroughput24G is equal")
        .matches(
            v ->
                v.getMinClientThroughput50G()
                    .equals(apClientAdmissionControl.getMinClientThroughput50G()),
            "DB ClientAdmissionControl MinClientThroughput50G is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio24)
        .extracting(
            radioCustomization -> radioCustomization.getRadio24().getChannelSelectionMethod())
        .matches(
            channelSelectionMethod ->
                channelSelectionMethod.equals(
                    AutoChannelSelectionMethod.AutoChannelSelectionMethod_UNSET));

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio24)
        .extracting(
            radioCustomization -> radioCustomization.getRadio24().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled() == apClientAdmissionControl.getEnable24G(),
            "DDCCM ClientAdmissionControl Enable24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == apClientAdmissionControl.getMinClientCount24G(),
            "DDCCM ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == apClientAdmissionControl.getMaxRadioLoad24G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == apClientAdmissionControl.getMinClientThroughput24G(),
            "DDCCM ClientAdmissionControl MinClientThroughput24G is equal");

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio50)
        .extracting(
            radioCustomization -> radioCustomization.getRadio50().getChannelSelectionMethod())
        .matches(
            channelSelectionMethod ->
                channelSelectionMethod.equals(
                    AutoChannelSelectionMethod.AutoChannelSelectionMethod_UNSET));

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp))
        .hasSize(2)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Ap::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.ApRadioCustomization::hasRadio50)
        .extracting(
            radioCustomization -> radioCustomization.getRadio50().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled() == apClientAdmissionControl.getEnable50G(),
            "DDCCM ClientAdmissionControl Enable50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == apClientAdmissionControl.getMinClientCount50G(),
            "DDCCM ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == apClientAdmissionControl.getMaxRadioLoad50G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == apClientAdmissionControl.getMinClientThroughput50G(),
            "DDCCM ClientAdmissionControl MinClientThroughput50G is equal");
  }

  @Nested
  class whenConsumeUpdateApCfgRequestTest {

    @Deprecated(forRemoval = true)
    @Test
    void thenUpdateApDirectedMulticast(Ap ap) throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apDirectedMulticast = new com.ruckus.cloud.wifi.eda.viewmodel.ApDirectedMulticast();
      apDirectedMulticast.setUseVenueSettings(false);
      apDirectedMulticast.setWirelessEnabled(true);
      apDirectedMulticast.setWiredEnabled(true);
      apDirectedMulticast.setNetworkEnabled(false);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_DIRECTED_MULTICAST,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()),
          apDirectedMulticast);
      validateUpdateApDirectedMulticastResult(ap.getId(), apDirectedMulticast,
          ap.getTenant().getId(), requestId);
    }

    @Deprecated(forRemoval = true)
    @Test
    void thenResetApDirectedMulticast(Ap ap) throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apDirectedMulticast = new ApDirectedMulticast();
      apDirectedMulticast.setWirelessEnabled(true);
      apDirectedMulticast.setWiredEnabled(false);
      apDirectedMulticast.setNetworkEnabled(true);
      ap.setDirectedMulticast(apDirectedMulticast);
      repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), requestId);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.RESET_AP_DIRECTED_MULTICAST,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()), "");
      validateResetApDirectedMulticastResult(ap.getId(), ap.getTenant().getId(), requestId);
    }

    @Test
    void thenUpdateApSnmpAgent(Ap ap, ApSnmpAgentProfile apSnmpAgentProfile)
        throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();

      var viewApSnmpAgent = new com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgent();
      viewApSnmpAgent.setUseVenueSettings(false);
      viewApSnmpAgent.setEnableApSnmp(true);
      viewApSnmpAgent.setApSnmpAgentProfileId(apSnmpAgentProfile.getId());

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_SNMP_AGENT,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()),
          viewApSnmpAgent);

      validateUpdateApSnmpAgentResult(requestId, ap.getId(), viewApSnmpAgent);
    }

    @Test
    void thenUpdateApSnmpAgentUseVenueSettings(Ap ap, ApSnmpAgentProfile apSnmpAgentProfile)
        throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();

      var apSnmpAgent = new ApSnmpAgent();
      apSnmpAgent.setUseVenueSettings(false);
      apSnmpAgent.setEnableApSnmp(true);
      apSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);

      ap.setApSnmpAgent(apSnmpAgent);

      repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), randomTxId());

      var viewApSnmpAgent = new com.ruckus.cloud.wifi.eda.viewmodel.ApSnmpAgent();
      viewApSnmpAgent.setUseVenueSettings(true);
      viewApSnmpAgent.setEnableApSnmp(true);
      viewApSnmpAgent.setApSnmpAgentProfileId(apSnmpAgentProfile.getId());

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_SNMP_AGENT,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()),
          viewApSnmpAgent);

      validateUpdateApSnmpAgentUseVenueSettingsResult(requestId, ap.getId(),
          apSnmpAgentProfile.getId(), apSnmpAgentProfile.getPolicyName());
    }

    @Test
    void thenResetApSnmpAgent(Ap ap, ApSnmpAgentProfile apSnmpAgentProfile)
        throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();

      var apSnmpAgent = new ApSnmpAgent();
      apSnmpAgent.setUseVenueSettings(false);
      apSnmpAgent.setEnableApSnmp(true);
      apSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);

      ap.setApSnmpAgent(apSnmpAgent);
      repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), randomTxId());

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.RESET_AP_SNMP_AGENT,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()), "");

      validateResetApSnmpAgentResult(requestId, ap.getId(), apSnmpAgentProfile.getId(),
          apSnmpAgentProfile.getPolicyName());
    }

    @Deprecated(forRemoval = true)
    @Test
    void thenUpdateApMesh(Ap ap)
        throws InvalidProtocolBufferException, JsonProcessingException {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      Mesh mesh = venue.getMesh();
      mesh.setEnabled(true);
      venue.setMesh(mesh);
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      ApMesh apMesh = new ApMesh();
      apMesh.setMeshMode(MeshModeEnum.MESH);
      apMesh.setUplinkMode(MeshUplinkModeEnum.MANUAL);
      apMesh.setUplinkMacAddresses(List.of("AA:BB:CC:DD:EE:FF", "11:22:33:44:55:66"));

      ObjectMapper objectMapper = new ObjectMapper();
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_MESH_OPTIONS,
          objectMapper.writeValueAsString(
              Map.of(
                  RKS_IDM_USER_ID.getName(), userName,
                  RKS_FEATURE_ROLES.getName(), "BETA-MESH"
              )),
          new RequestParams().addPathVariable("serialNumber", ap.getId()), apMesh);

      validateUpdateApMeshResult(ap.getId(), apMesh, ap.getTenant().getId(), requestId);
    }

    @Deprecated(forRemoval = true)
    @Test
    void thenUpdateApBssColoring(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      final ApBssColoring apBssColoring = new ApBssColoring();
      apBssColoring.setUseVenueSettings(false);
      apBssColoring.setBssColoringEnabled(true);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_BSS_COLORING_SETTINGS,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()),
          apBssColoring);

      validateUpdateApBssColoringResult(ap.getId(), apBssColoring, ap.getTenant().getId(), requestId);
    }

    @Deprecated(forRemoval = true)
    @Test
    void thenUpdateApClientAdmissionControl(Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      final var apClientAdmissionControl = new ApClientAdmissionControl();
      apClientAdmissionControl.setEnable24G(true);
      apClientAdmissionControl.setMinClientCount24G((short) 11);
      apClientAdmissionControl.setMaxRadioLoad24G((short) 50);
      apClientAdmissionControl.setMinClientThroughput24G((short) 10);
      apClientAdmissionControl.setEnable50G(true);
      apClientAdmissionControl.setMinClientCount50G((short) 21);
      apClientAdmissionControl.setMaxRadioLoad50G((short) 50);
      apClientAdmissionControl.setMinClientThroughput50G((short) 20);
      apClientAdmissionControl.setUseVenueSettings(false);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_CLIENT_ADMISSION_CONTROL_SETTINGS,
          userName,
          new RequestParams().addPathVariable("serialNumber", ap.getId()),
          apClientAdmissionControl);

      validateUpdateApClientAdmissionControlResult(
          ap.getId(), apClientAdmissionControl, ap.getTenant().getId(), requestId);

      final var requestId2 = randomTxId();
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId2,
          CfgAction.RESET_AP_CLIENT_ADMISSION_CONTROL_SETTINGS,
          userName,
          new RequestParams().addPathVariable("serialNumber", ap.getId()),
          apClientAdmissionControl);

      validateResetApClientAdmissionControlResult(
          ap.getId(), apClientAdmissionControl, ap.getTenant().getId(), requestId2);
    }

    @Test
    void thenUpdateApRadioCustomizationUseVenue(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      ap.getRadioCustomization().setEnable24G(true);
      repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), ap.getId());

      ApRadioCustomization apRadioCustomization = new ApRadioCustomization();
      apRadioCustomization.setUseVenueSettings(true);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_RADIO_CUSTOMIZATION,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()),
          apRadioCustomization);

      validateUpdateApRadioCustomizationUseVenueResult(ap.getId(), ap.getTenant().getId(),
          requestId, false);
    }

    @Test
    void thenUpdateApRadioCustomizationUseVenueWithNewFields(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      ap.getRadioCustomization().setEnable24G(true);
      ap.getRadioCustomization().setEnable50G(false);
      repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), ap.getId());

      ApRadioCustomization apRadioCustomization = new ApRadioCustomization();
      apRadioCustomization.setUseVenueSettings(true);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_RADIO_CUSTOMIZATION,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()),
          apRadioCustomization);

      validateUpdateApRadioCustomizationUseVenueResult(ap.getId(), ap.getTenant().getId(),
          requestId, false);
    }

    @Test
    void thenUpdateApRadioCustomizationCustomizeWithMulticastDnsProxyProfile2Rules(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());
      var profile = multicastDnsProxyServiceProfile("m1");
      var saveProfile = repositoryUtil.createOrUpdate(profile, venue.getTenant().getId(), randomTxId());
      var profileAp = multicastDnsProxyServiceProfileAp(ap, saveProfile);
      saveProfile.setMulticastDnsProxyServiceProfileAps(List.of(profileAp));
      repositoryUtil.createOrUpdate(profileAp, venue.getTenant().getId(), randomTxId());

      ApRadioCustomization apRadioCustomization = new ApRadioCustomization();
      apRadioCustomization.setUseVenueSettings(false);
      apRadioCustomization.setApRadioParams24G(new ApRadioParams24G());
      apRadioCustomization.getApRadioParams24G().setAllowedChannels(
          Arrays.asList(Channel24Enum._1, Channel24Enum._2));
      apRadioCustomization.setEnable50G(false);
      apRadioCustomization.setEnable6G(true);
      apRadioCustomization.setApRadioParams6G(new ApRadioParams6G());
      apRadioCustomization.getApRadioParams6G().setAllowedChannels(
          Arrays.asList(Channel6GEnum._1, Channel6GEnum._5));

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_RADIO_CUSTOMIZATION,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()),
          apRadioCustomization);

      validateUpdateApMulticastDnsProxyServiceProfileResult(ap.getId(), ap.getTenant().getId(),
          requestId, saveProfile.getId());
    }

    @Test
    void thenUpdateApRadioCustomizationCustomizeWithNewFields(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      ApRadioCustomization apRadioCustomization = new ApRadioCustomization();
      apRadioCustomization.setUseVenueSettings(false);
      apRadioCustomization.setApRadioParams24G(new ApRadioParams24G());
      apRadioCustomization.getApRadioParams24G().setAllowedChannels(
          Arrays.asList(Channel24Enum._1, Channel24Enum._2));
      apRadioCustomization.setEnable50G(false);
      apRadioCustomization.setEnable6G(true);
      apRadioCustomization.setApRadioParams6G(new ApRadioParams6G());
      apRadioCustomization.getApRadioParams6G().setAllowedChannels(
          Arrays.asList(Channel6GEnum._1, Channel6GEnum._5));

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_RADIO_CUSTOMIZATION,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()),
          apRadioCustomization);

      validateUpdateApRadioCustomizationCustomizeResult(ap.getId(), ap.getTenant().getId(),
          requestId, apRadioCustomization, true, false);
    }

    @Test
    void thenUpdateApRadioCustomizationPartialCustomize(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      ApRadioCustomization apRadioCustomization = new ApRadioCustomization();
      apRadioCustomization.setUseVenueSettings(true);
      // 24G
      apRadioCustomization.setApRadioParams24G(new ApRadioParams24G());
      apRadioCustomization.getApRadioParams24G().setUseVenueSettings(false);
      apRadioCustomization.getApRadioParams24G().setAllowedChannels(
          Arrays.asList(Channel24Enum._1, Channel24Enum._2));
      // 50G
      apRadioCustomization.setEnable50G(false);
      // 6G
      apRadioCustomization.setEnable6G(false);
      apRadioCustomization.setApRadioParams6G(new ApRadioParams6G());
      apRadioCustomization.getApRadioParams6G().setUseVenueSettings(false);
      apRadioCustomization.getApRadioParams6G().setAllowedChannels(
          Arrays.asList(Channel6GEnum._1, Channel6GEnum._5));
      // Dual 5G
      apRadioCustomization.setApRadioParamsDual5G(
          new com.ruckus.cloud.wifi.eda.viewmodel.ApRadioParamsDual5G());
      apRadioCustomization.getApRadioParamsDual5G().setEnabled(false);
      // Lower 5G
      apRadioCustomization.getApRadioParamsDual5G().setLower5gEnabled(false);
      apRadioCustomization.getApRadioParamsDual5G().setRadioParamsLower5G(new ApRadioParams50G());
      apRadioCustomization.getApRadioParamsDual5G().getRadioParamsLower5G().setUseVenueSettings(true);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_RADIO_CUSTOMIZATION,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()),
          apRadioCustomization);

      validateUpdateApRadioCustomizationPartialResult(ap.getId(), ap.getTenant().getId(), requestId,
          apRadioCustomization);
    }

    @Test
    void thenResetApRadioCustomizationWithNewFields(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      ap.getRadioCustomization().setEnable24G(true);
      repositoryUtil.createOrUpdate(ap, ap.getTenant().getId(), ap.getId());

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.RESET_AP_RADIO_CUSTOMIZATION,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId()), "");

      validateResetApRadioCustomizationResult(ap.getId(), ap.getTenant().getId(), requestId);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_AP_MGMT_VLAN_AP_LEVEL_TOGGLE)
    void thenUpdateApManagementVlanSettings(Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var venueVlanId = (short) 100;

      // Set venue management VLAN ID
      final var venueApManagementTrafficVlanSettings = new VenueApManagementTrafficVlanSettings();
      venueApManagementTrafficVlanSettings.setVlanId(venueVlanId);
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_VENUE_AP_MANAGEMENT_VLAN_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          venueApManagementTrafficVlanSettings);

      // Reset when db has no record to make sure that always triggers the DDCCM operation builder
      final var apManagementTrafficVlanSettings = new ApManagementTrafficVlanSettings();
      apManagementTrafficVlanSettings.setUseVenueSettings(true);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_MANAGEMENT_TRAFFIC_VLAN_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          apManagementTrafficVlanSettings);

      validateResetApManagementVlanSettingsResult(ap.getId(), ap.getTenant().getId(), requestId, venueVlanId);

      final var requestId1 = randomTxId();
      final var apManagementTrafficVlanSettings1 = new ApManagementTrafficVlanSettings();
      apManagementTrafficVlanSettings1.setVlanOverrideEnabled(true);
      apManagementTrafficVlanSettings1.setVlanId((short) 1234);
      apManagementTrafficVlanSettings1.setUseVenueSettings(false);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId1,
          CfgAction.UPDATE_AP_MANAGEMENT_TRAFFIC_VLAN_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          apManagementTrafficVlanSettings1);

      validateUpdateApManagementVlanSettingsResult(
          ap.getId(), apManagementTrafficVlanSettings1, ap.getTenant().getId(), requestId1);

      // reset case
      final var apManagementTrafficVlanSettings2 = new ApManagementTrafficVlanSettings();
      apManagementTrafficVlanSettings2.setUseVenueSettings(true);

      final var requestId2 = randomTxId();
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId2,
          CfgAction.UPDATE_AP_MANAGEMENT_TRAFFIC_VLAN_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          apManagementTrafficVlanSettings2);

      validateResetApManagementVlanSettingsResult(ap.getId(), ap.getTenant().getId(), requestId2, venueVlanId);
    }

    @Test
    void thenUpdateAp(Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var updateAp = new ApV1();
      updateAp.setName(ap.getName() + "new");

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_V1,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), updateAp);
      validateUpdateApResult(ap.getId(), ap.getTenant().getId(), requestId, updateAp);
    }

    @Tag("EthernetPortProfileTest")
    @Test
    void thenUpdateApLanPortSettings(@ApModel("T750SE") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apLanPortSettings = new ApLanPortSettings();
      apLanPortSettings.setPoeOut(false);
      List<ApLanPort> apLanPortList = new ArrayList<>();
      apLanPortList.add(createApLanPort("1"));
      apLanPortList.add(createApLanPort("2"));
      apLanPortList.add(createApLanPort("3"));
      apLanPortSettings.setLanPorts(apLanPortList);
      apLanPortSettings.setPoeMode(PoeModeEnum._802_3at);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_LAN_PORT_SETTINGS,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apLanPortSettings);
      validateUpdateApLanPortSettingsResult(ap.getId(), apLanPortSettings,
          ap.getTenant().getId(), requestId);

      final var requestId2 = randomTxId();
      apLanPortSettings.setUseVenueSettings(true);
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId2, CfgAction.UPDATE_AP_LAN_PORT_SETTINGS,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apLanPortSettings);
      validateResetApLanPortSettingsResult(ap.getId(), ap.getTenant().getId(), requestId2);
    }

    @Test
    void thenUpdateApLedSettings(@ApModel("R310") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apLedSettings = new ApLedSettings();
      apLedSettings.setLedEnabled(true);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_LED_SETTINGS,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apLedSettings);
      validateUpdateApLedSettingsResult(ap.getId(), apLedSettings,
          ap.getTenant().getId(), requestId);

      final var requestId2 = randomTxId();
      apLedSettings.setUseVenueSettings(true);
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId2, CfgAction.UPDATE_AP_LED_SETTINGS,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apLedSettings);
      validateResetApLedSettingsResult(ap.getId(), ap.getTenant().getId(), requestId2);
    }

    @Test
    void thenUpdateApExternalAntennaSettings(@ApModel("T750SE") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apExternalAntennaSettings = new ApExternalAntennaSettings();
      ExternalAntenna externalAntenna = new ExternalAntenna();
      //externalAntenna.setCoupled();
      externalAntenna.setEnable24G(true);
      externalAntenna.setGain24G(2);
      externalAntenna.setEnable50G(true);
      externalAntenna.setGain50G(5);
      apExternalAntennaSettings.setExternalAntenna(externalAntenna);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_EXTERNAL_ANTENNA_SETTINGS,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apExternalAntennaSettings);
      validateUpdateApExternalAntennaSettingsResult(ap.getId(), apExternalAntennaSettings,
          ap.getTenant().getId(), requestId);

      final var requestId2 = randomTxId();
      apExternalAntennaSettings.setUseVenueSettings(true);
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId2, CfgAction.UPDATE_AP_EXTERNAL_ANTENNA_SETTINGS,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apExternalAntennaSettings);
      validateResetApExternalAntennaSettingsResult(ap.getId(), ap.getTenant().getId(), requestId2);
    }

    @Test
    void thenUpdateApExternalAntennaSettingsV1_1(@ApModel("T750SE") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apExternalAntennaSettingsV1_1 = new ApExternalAntennaSettingsV1_1();
      ExternalAntenna externalAntenna = new ExternalAntenna();
      //externalAntenna.setCoupled();
      externalAntenna.setEnable24G(true);
      externalAntenna.setGain24G(2);
      externalAntenna.setEnable50G(true);
      externalAntenna.setGain50G(5);
      apExternalAntennaSettingsV1_1.setExternalAntenna(externalAntenna);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_EXTERNAL_ANTENNA_SETTINGS_V1_1,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apExternalAntennaSettingsV1_1);
      validateUpdateApExternalAntennaSettingsResult(ap.getId(), apExternalAntennaSettingsV1_1,
          ap.getTenant().getId(), requestId);

      final var requestId2 = randomTxId();
      apExternalAntennaSettingsV1_1.setUseVenueOrApGroupSettings(true);
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId2, CfgAction.UPDATE_AP_EXTERNAL_ANTENNA_SETTINGS_V1_1,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apExternalAntennaSettingsV1_1);
      validateResetApExternalAntennaSettingsResult(ap.getId(), ap.getTenant().getId(), requestId2);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_SWITCHABLE_RF_TOGGLE)
    void thenUpdateApBandMode(@ApModel("R670") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apBandModeSettings = new ApBandModeSettings();
      apBandModeSettings.setBandMode(BandModeEnum.DUAL);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_BAND_MODE,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apBandModeSettings);
      validateUpdateApBandModeResult(ap.getId(), apBandModeSettings,
          ap.getTenant().getId(), requestId);

      final var requestId2 = randomTxId();
      apBandModeSettings.setUseVenueSettings(true);
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId2, CfgAction.UPDATE_AP_BAND_MODE,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apBandModeSettings);
      validateResetApBandModeResult(ap.getId(), ap.getTenant().getId(), requestId2);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE)
    void thenUpdateApAntennaType(@ApModel("R670") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apAntennaTypeSettings = new ApAntennaTypeSettings();
      apAntennaTypeSettings.setAntennaType(AntennaTypeEnum.SECTOR);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_ANTENNA_TYPE,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apAntennaTypeSettings);
      validateUpdateApAntennaTypeResult(ap.getId(), apAntennaTypeSettings,
          ap.getTenant().getId(), requestId);

      final var requestId2 = randomTxId();
      apAntennaTypeSettings.setUseVenueSettings(true);
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId2, CfgAction.UPDATE_AP_ANTENNA_TYPE,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apAntennaTypeSettings);
      validateResetApAntennaTypeResult(ap.getId(), ap.getTenant().getId(), requestId2);
    }

    @Test
    void thenUpdateApDirectedMulticastSettings(Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apDirectedMulticast = new ApDirectedMulticastSettings();
      apDirectedMulticast.setUseVenueSettings(false);
      apDirectedMulticast.setWirelessEnabled(true);
      apDirectedMulticast.setWiredEnabled(true);
      apDirectedMulticast.setNetworkEnabled(false);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_DIRECTED_MULTICAST_SETTINGS,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apDirectedMulticast);
      validateUpdateApDirectedMulticastSettingsResult(ap.getId(), apDirectedMulticast,
          ap.getTenant().getId(), requestId);

      final var requestId2 = randomTxId();
      apDirectedMulticast.setUseVenueSettings(true);
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId2, CfgAction.UPDATE_AP_DIRECTED_MULTICAST_SETTINGS,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apDirectedMulticast);
      validateResetApDirectedMulticastSettingsResult(ap.getId(), ap.getTenant().getId(), requestId2);
    }

    @Test
    void thenUpdateApNetworkSettings(Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apNetworkSettings = new ApNetworkSettings();
      apNetworkSettings.setIpType(IpTypeEnum.STATIC);
      apNetworkSettings.setIp("*************");
      apNetworkSettings.setNetmask("*************");
      apNetworkSettings.setGateway("*************");
      apNetworkSettings.setPrimaryDnsServer("***********");

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_NETWORK_SETTINGS_V1,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apNetworkSettings);
      validateUpdateApNetworkSettingsResult(ap.getId(), apNetworkSettings,
          ap.getTenant().getId(), requestId);
    }

    @Test
    void thenUpdateApMeshSettings(Ap ap)
        throws InvalidProtocolBufferException, JsonProcessingException {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      Mesh mesh = venue.getMesh();
      mesh.setEnabled(true);
      venue.setMesh(mesh);
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      ApMeshSettings apMesh = new ApMeshSettings();
      apMesh.setMeshMode(MeshModeEnum.MESH);
      apMesh.setUplinkMode(MeshUplinkModeEnum.MANUAL);
      apMesh.setUplinkMacAddresses(List.of("AA:BB:CC:DD:EE:FF", "11:22:33:44:55:66"));

      ObjectMapper objectMapper = new ObjectMapper();
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_MESH_SETTINGS,
          objectMapper.writeValueAsString(
              Map.of(
                  RKS_IDM_USER_ID.getName(), userName,
                  RKS_FEATURE_ROLES.getName(), "BETA-MESH"
              )),
          new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apMesh);

      validateUpdateApMeshSettingsResult(ap.getId(), apMesh, ap.getTenant().getId(), requestId);
    }

    @Test
    void thenUpdateApBssColoringSettings(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      Venue venue = ap.getApGroup().getVenue();
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      final ApBssColoringSettings apBssColoring = new ApBssColoringSettings();
      apBssColoring.setUseVenueSettings(false);
      apBssColoring.setBssColoringEnabled(true);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId, CfgAction.UPDATE_AP_BSS_COLORING_SETTINGS_V1,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apBssColoring);

      validateUpdateApBssColoringSettingsResult(ap.getId(), apBssColoring, ap.getTenant().getId(), requestId);

      final var requestId2 = randomTxId();
      apBssColoring.setUseVenueSettings(true);
      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(), requestId2, CfgAction.UPDATE_AP_BSS_COLORING_SETTINGS_V1,
          userName, new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()), apBssColoring);

      validateResetApBssColoringSettingsResult(ap.getId(), ap.getTenant().getId(), requestId2);
    }

    @Test
    void thenUpdateApClientAdmissionControlSettings(Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();

      final var apClientAdmissionControl = new ApClientAdmissionControlSettings();
      apClientAdmissionControl.setEnable24G(true);
      apClientAdmissionControl.setMinClientCount24G((short) 11);
      apClientAdmissionControl.setMaxRadioLoad24G((short) 50);
      apClientAdmissionControl.setMinClientThroughput24G((short) 10);
      apClientAdmissionControl.setEnable50G(true);
      apClientAdmissionControl.setMinClientCount50G((short) 21);
      apClientAdmissionControl.setMaxRadioLoad50G((short) 50);
      apClientAdmissionControl.setMinClientThroughput50G((short) 20);
      apClientAdmissionControl.setUseVenueSettings(false);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_CLIENT_ADMISSION_CONTROL_SETTINGS_V1,
          userName,
          new RequestParams().addPathVariable("serialNumber", ap.getId())
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId()),
          apClientAdmissionControl);

      validateUpdateApClientAdmissionControlSettingsResult(
          ap.getId(), apClientAdmissionControl, ap.getTenant().getId(), requestId);
    }

    private ApLanPort createApLanPort(String portId) {
      ApLanPort apLanPort = new ApLanPort();
      apLanPort.setPortId(portId);
      apLanPort.setUntagId(Short.valueOf("1"));
      apLanPort.setVlanMembers("1-4094");
      apLanPort.setType(ApLanPortTypeEnum.TRUNK);
      apLanPort.setEnabled(true);
      return apLanPort;
    }


    @Test
    @ApVersion(value = "7.1.0.103.200")
    void thenUpdateApStickyClientSteeringSettingsWithApSetting(@ApModel("R770") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = ap.getTenant().getId();

      Venue venue = ap.getApGroup().getVenue();
      VenueLoadBalancing venueLoadBalancing = venue.getLoadBalancing();
      venueLoadBalancing.setEnabled(true);
      venue.setLoadBalancing(venueLoadBalancing);
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      final var apStickyClientSteeringSettings = new ApStickyClientSteeringSettings();
      apStickyClientSteeringSettings.setUseVenueSettings(false);
      apStickyClientSteeringSettings.setEnabled(true);
      apStickyClientSteeringSettings.setSnrThreshold((short) 11);
      apStickyClientSteeringSettings.setNeighborApPercentageThreshold((short) 12);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_STICKY_CLIENT_STEERING_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          apStickyClientSteeringSettings);

      final var apFromDb = repositoryUtil.find(Ap.class, ap.getId());
      assertThat(apFromDb)
          .isNotNull()
          .extracting(Ap::getStickyClientSteeringSettings)
          .isNotNull()
          .satisfies(stickyClientSteering -> assertSoftly(softly -> {
            softly.assertThat(stickyClientSteering.getEnabled()).isEqualTo(true);
            softly.assertThat(stickyClientSteering.getSnrThreshold()).isEqualTo((short) 11);
            softly.assertThat(stickyClientSteering.getNeighborApPercentageThreshold())
                .isEqualTo((short) 12);
          }));

      var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
      assertThat(ddccmMessage).isNotNull();
      WifiConfigRequest ddccmRequest = ddccmMessage.getPayload();
      assertTrue(ddccmRequest.getOperationsList().get(0).getAp().hasStickyClientSteering());

      assertThat(ddccmMessage.getPayload().getOperationsList())
          .filteredOn(Operation::hasAp)
          .hasSize(1)
          .singleElement()
          .extracting(Operation::getAp)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getStickyClientSteering)
          .satisfies(stickyClientSteering -> assertSoftly(softly -> {
            softly.assertThat(stickyClientSteering.getStickyClientSteeringEnabled().getValue())
                .isEqualTo(true);
            softly.assertThat(stickyClientSteering.getStickyClientSnrThreshold().getValue())
                .isEqualTo(11);
            softly.assertThat(
                    stickyClientSteering.getStickyClientNbrApPercentageThreshold().getValue())
                .isEqualTo(12);
          }));
    }

    @Test
    @ApVersion(value = "7.1.0.103.200")
    void thenResetApStickyClientSteeringSettingsWithApSetting(@ApModel("R770") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = ap.getTenant().getId();

      Venue venue = ap.getApGroup().getVenue();
      VenueLoadBalancing venueLoadBalancing = venue.getLoadBalancing();
      venueLoadBalancing.setEnabled(true);
      venue.setLoadBalancing(venueLoadBalancing);
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      var apStickyClientSteeringSettings = ap.getStickyClientSteeringSettings();
      apStickyClientSteeringSettings.setUseVenueSettings(false);
      apStickyClientSteeringSettings.setEnabled(true);
      apStickyClientSteeringSettings.setSnrThreshold((short) 11);
      apStickyClientSteeringSettings.setNeighborApPercentageThreshold((short) 12);

      repositoryUtil.createOrUpdate(ap, ap.getId(), randomTxId());

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.RESET_AP_STICKY_CLIENT_STEERING_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()), StringUtils.EMPTY);

      final var apFromDb = repositoryUtil.find(Ap.class, ap.getId());
      assertThat(apFromDb)
          .isNotNull()
          .extracting(Ap::getStickyClientSteeringSettings)
          .isNotNull()
          .satisfies(stickyClientSteering -> assertSoftly(softly -> {
            softly.assertThat(stickyClientSteering.getEnabled()).isNull();
            softly.assertThat(stickyClientSteering.getSnrThreshold()).isEqualTo((short) 15);
            softly.assertThat(stickyClientSteering.getNeighborApPercentageThreshold())
                .isEqualTo((short) 20);
          }));

      var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
      assertThat(ddccmMessage).isNotNull();
      WifiConfigRequest ddccmRequest = ddccmMessage.getPayload();
      assertFalse(ddccmRequest.getOperationsList().get(0).getAp().hasStickyClientSteering());
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE)
    @ApVersion(value = "7.1.0.103.200")
    void thenUpdateApStickyClientSteeringSettingsWithApSettingV1_1(@ApModel("R770") Ap ap)
        throws JsonProcessingException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = ap.getTenant().getId();

      Venue venue = ap.getApGroup().getVenue();
      VenueLoadBalancing venueLoadBalancing = venue.getLoadBalancing();
      venueLoadBalancing.setEnabled(true);
      venue.setLoadBalancing(venueLoadBalancing);
      repositoryUtil.createOrUpdate(venue);

      final ApStickyClientSteeringSettingsV1_1 request =
          objectMapper.readValue(
              """
                  {
                    "useVenueOrApGroupSettings": false,
                    "enabled": true,
                    "neighborApPercentageThreshold": 12,
                    "snrThreshold": 11
                  }
                  """,
              new TypeReference<>() {});

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_STICKY_CLIENT_STEERING_SETTINGS_V1_1,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          request);

      final var apFromDb = repositoryUtil.find(Ap.class, ap.getId());
      assertThat(apFromDb)
          .isNotNull()
          .extracting(Ap::getStickyClientSteeringSettings)
          .isNotNull()
          .satisfies(
              stickyClientSteering ->
                  assertSoftly(
                      softly -> {
                        softly.assertThat(stickyClientSteering.getEnabled()).isEqualTo(true);
                        softly
                            .assertThat(stickyClientSteering.getSnrThreshold())
                            .isEqualTo((short) 11);
                        softly
                            .assertThat(stickyClientSteering.getNeighborApPercentageThreshold())
                            .isEqualTo((short) 12);
                      }));

      var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
      assertThat(ddccmMessage).isNotNull();
      WifiConfigRequest ddccmRequest = ddccmMessage.getPayload();
      assertTrue(ddccmRequest.getOperationsList().get(0).getAp().hasStickyClientSteering());

      assertThat(ddccmMessage.getPayload().getOperationsList())
          .filteredOn(Operation::hasAp)
          .hasSize(1)
          .singleElement()
          .extracting(Operation::getAp)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getStickyClientSteering)
          .satisfies(
              stickyClientSteering ->
                  assertSoftly(
                      softly -> {
                        softly
                            .assertThat(
                                stickyClientSteering.getStickyClientSteeringEnabled().getValue())
                            .isEqualTo(true);
                        softly
                            .assertThat(
                                stickyClientSteering.getStickyClientSnrThreshold().getValue())
                            .isEqualTo(11);
                        softly
                            .assertThat(
                                stickyClientSteering
                                    .getStickyClientNbrApPercentageThreshold()
                                    .getValue())
                            .isEqualTo(12);
                      }));
    }

    @Test
    @FeatureFlag(
        enable = {
          FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE3_TOGGLE,
        })
    void thenUpdateApClientAdmissionControlSettingsV1_1(Ap ap) throws JsonProcessingException {
      final var requestId = randomTxId();
      final var userName = randomName();

      final ApClientAdmissionControlSettingsV1_1 request =
          objectMapper.readValue(
              """
                          {
                              "useVenueOrApGroupSettings": false,
                              "enable24G": true,
                              "enable50G": true,
                              "minClientCount24G": 11,
                              "minClientCount50G": 21,
                              "maxRadioLoad24G": 50,
                              "maxRadioLoad50G": 50,
                              "minClientThroughput24G": 10,
                              "minClientThroughput50G": 20
                          }
                          """,
              new TypeReference<>() {});

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_CLIENT_ADMISSION_CONTROL_SETTINGS_V1_1,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          request);

      validateUpdateApClientAdmissionControlSettingsResult(
          ap.getId(), request, ap.getTenant().getId(), requestId);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_SMART_MONITOR_DISABLE_WLAN_TOGGLE)
    @ApVersion(value = "7.1.0.103.200")
    void thenUpdateApSmartMonitorSettingsUseVenueSetting(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = ap.getTenant().getId();

      final var apSmartMonitorSettings = new ApSmartMonitorSettings();
      apSmartMonitorSettings.setUseVenueSettings(true);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_SMART_MONITOR_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          apSmartMonitorSettings);

      final var apFromDb = repositoryUtil.find(Ap.class, ap.getId());
      assertThat(apFromDb)
          .isNotNull()
          .extracting(Ap::getSmartMonitor)
          .isNotNull()
          .satisfies(smartMonitor -> assertSoftly(softly -> {
            softly.assertThat(smartMonitor.getEnabled()).isNull();
            softly.assertThat(smartMonitor.getInterval()).isEqualTo((short) 10);
            softly.assertThat(smartMonitor.getThreshold()).isEqualTo((short) 3);
          }));

      var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
      assertThat(ddccmMessage).isNotNull();
      WifiConfigRequest ddccmRequest = ddccmMessage.getPayload();
      assertFalse(ddccmRequest.getOperationsList().get(0).getAp().hasSmartMonitor());
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_SMART_MONITOR_DISABLE_WLAN_TOGGLE)
    @ApVersion(value = "7.1.0.103.200")
    void thenUpdateApSmartMonitorSettingsWithApSetting(@ApModel("R770")Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = ap.getTenant().getId();

      Venue venue = ap.getApGroup().getVenue();
      VenueApSmartMonitor venueApSmartMonitor = venue.getSmartMonitor();
      venueApSmartMonitor.setEnabled(true);
      venue.setSmartMonitor(venueApSmartMonitor);
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      final var apSmartMonitorSettings = new ApSmartMonitorSettings();
      apSmartMonitorSettings.setUseVenueSettings(false);
      apSmartMonitorSettings.setEnabled(true);
      apSmartMonitorSettings.setInterval((short) 11);
      apSmartMonitorSettings.setThreshold((short) 12);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_SMART_MONITOR_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          apSmartMonitorSettings);

      final var apFromDb = repositoryUtil.find(Ap.class, ap.getId());
      assertThat(apFromDb)
          .isNotNull()
          .extracting(Ap::getSmartMonitor)
          .isNotNull()
          .satisfies(smartMonitor -> assertSoftly(softly -> {
            softly.assertThat(smartMonitor.getEnabled()).isEqualTo(true);
            softly.assertThat(smartMonitor.getInterval()).isEqualTo((short) 11);
            softly.assertThat(smartMonitor.getThreshold()).isEqualTo((short) 12);
          }));

      var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
      assertThat(ddccmMessage).isNotNull();
      WifiConfigRequest ddccmRequest = ddccmMessage.getPayload();
      assertTrue(ddccmRequest.getOperationsList().get(0).getAp().hasSmartMonitor());

      assertThat(ddccmMessage.getPayload().getOperationsList())
          .filteredOn(Operation::hasAp)
          .hasSize(1)
          .singleElement()
          .extracting(Operation::getAp)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getSmartMonitor)
          .satisfies(smartMonitor -> assertSoftly(softly -> {
            softly.assertThat(smartMonitor.getEnabled()).isEqualTo(true);
            softly.assertThat(smartMonitor.getIntervalSeconds()).isEqualTo(11);
            softly.assertThat(smartMonitor.getThresholdTimes()).isEqualTo(12);
          }));
    }

    @Test
    @FeatureFlag(enable = FlagNames.IOT_MQTT_BROKER_TOGGLE)
    @ApVersion(value = "7.1.0.510.1005")
    void thenUpdateApIotSettingsUseVenueSetting(@ApModel("R550") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = ap.getTenant().getId();

      final var apIotSettings = new ApIotSettings();
      apIotSettings.setUseVenueSettings(true);

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_IOT_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          apIotSettings);

      final var apFromDb = repositoryUtil.find(Ap.class, ap.getId());
      assertThat(apFromDb)
          .isNotNull()
          .extracting(Ap::getIotSettings)
          .isNotNull()
          .satisfies(s -> assertSoftly(softly -> {
            softly.assertThat(s.getEnabled()).isNull();
            softly.assertThat(s.getMqttBrokerAddress()).isNull();
          }));

      var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
      assertThat(ddccmMessage).isNotNull();
      WifiConfigRequest ddccmRequest = ddccmMessage.getPayload();
      assertFalse(ddccmRequest.getOperationsList().get(0).getAp().hasIotSettings());
    }

    @Test
    @FeatureFlag(enable = FlagNames.IOT_MQTT_BROKER_TOGGLE)
    @ApVersion(value = "7.1.0.510.1005")
    void thenUpdateApIotSettingsWithApSetting(@ApModel("R550") Ap ap) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var tenantId = ap.getTenant().getId();

      Venue venue = ap.getApGroup().getVenue();
      VenueApIotSettings venueApIotSettings = new VenueApIotSettings();
      venueApIotSettings.setEnabled(true);
      venueApIotSettings.setMqttBrokerAddress("iot.ruckus.com");
      venue.setIotSettings(venueApIotSettings);
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());

      final var apIotSettings = new ApIotSettings();
      apIotSettings.setUseVenueSettings(false);
      apIotSettings.setEnabled(true);
      apIotSettings.setMqttBrokerAddress("bot.ruckus.com");

      messageUtil.sendWifiCfgRequest(
          ap.getTenant().getId(),
          requestId,
          CfgAction.UPDATE_AP_IOT_SETTINGS,
          userName,
          new RequestParams()
              .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
              .addPathVariable("serialNumber", ap.getId()),
          apIotSettings);

      final var apFromDb = repositoryUtil.find(Ap.class, ap.getId());
      assertThat(apFromDb)
          .isNotNull()
          .extracting(Ap::getIotSettings)
          .isNotNull()
          .satisfies(s -> assertSoftly(softly -> {
            softly.assertThat(s.getEnabled()).isEqualTo(true);
            softly.assertThat(s.getMqttBrokerAddress()).isEqualTo("bot.ruckus.com");
          }));

      var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
      assertThat(ddccmMessage).isNotNull();
      WifiConfigRequest ddccmRequest = ddccmMessage.getPayload();
      assertTrue(ddccmRequest.getOperationsList().get(0).getAp().hasIotSettings());

      assertThat(ddccmMessage.getPayload().getOperationsList())
          .filteredOn(Operation::hasAp)
          .hasSize(1)
          .singleElement()
          .extracting(Operation::getAp)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getIotSettings)
          .satisfies(s -> assertSoftly(softly -> {
            softly.assertThat(s.getEnabled()).isEqualTo(true);
            softly.assertThat(s.getMqttBrokerAddress()).isEqualTo("bot.ruckus.com");
          }));
    }

    private ClientIsolationLanPortActivation getClientIsolationLanPortActivation(
        ClientIsolationAllowlist clientIsolationProfile) {
      ClientIsolationLanPortActivation activation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .clientIsolationLanPortActivation()
              .generate();
      activation.setClientIsolationAllowlist(clientIsolationProfile);
      return activation;
    }

    private SoftGreProfileLanPortActivation getSoftGreLanPortActivation(
        SoftGreProfile softGreProfile) {
      SoftGreProfileLanPortActivation activation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.softGreProfileLanPortActivation()
              .setDhcpOption82Enabled(always(true))
              .setDhcpOption82Settings(always(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.dhcpOption82Settings()
                      .generate()))
              .generate();
      activation.setSoftGreProfile(softGreProfile);
      return activation;
    }

    void validateResetApLanPortSettingsWillTriggerCmnCfgCollectorForSoftGreOrClientIsolation(
        String apId, String tenantId, String requestId) {
      final var apFromDb = repositoryUtil.find(Ap.class, apId);
      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId))
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty()
          .anySatisfy(op -> {
            if (op.getIndex().equals(Index.SOFT_GRE_PROFILE_INDEX_NAME)) {
              assertThat(op.getOpType()).isEqualTo(OpType.MOD);
              assertThat(op.getDocMap())
                  .containsEntry(Key.AP_ACTIVATIONS, ValueUtils.listValue(List.of()))
                  .hasEntrySatisfying(Key.VENUE_ACTIVATIONS, venueActivations -> {
                    assertThat(venueActivations.getKindCase())
                        .isEqualTo(Value.KindCase.LIST_VALUE);
                    assertThat(venueActivations.hasListValue())
                        .isTrue();
                    assertThat(venueActivations.getListValue().getValuesList())
                        .isNotEmpty()
                        .anySatisfy(value -> {
                          assertThat(value.getKindCase())
                              .isEqualTo(KindCase.STRUCT_VALUE);
                          assertThat(value.hasStructValue())
                              .isTrue();
                          assertThat(value.getStructValue())
                              .isNotNull()
                              .satisfies(structValue -> assertThat(structValue.getFieldsMap())
                                  .containsEntry(
                                      Key.VENUE_ID,
                                      ValueUtils.stringValue(
                                          apFromDb.getApGroup().getVenue().getId()))
                                  .hasEntrySatisfying(Key.AP_SERIAL_NUMBERS, apSerialNumbers -> {
                                    assertThat(apSerialNumbers.getKindCase())
                                        .isEqualTo(Value.KindCase.LIST_VALUE);
                                    assertThat(apSerialNumbers.hasListValue())
                                        .isTrue();
                                    assertThat(apSerialNumbers.getListValue().getValuesList())
                                        .isNotEmpty()
                                        .anySatisfy(ap -> {
                                          assertThat(ap.getKindCase())
                                              .isEqualTo(Value.KindCase.STRING_VALUE);
                                          assertThat(ap.getStringValue())
                                              .isEqualTo(apId);
                                        });
                                  }));
                        });
                  });
            }
            if (op.getIndex().equals(Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME)) {
              assertThat(op.getOpType()).isEqualTo(OpType.MOD);
              assertThat(op.getDocMap())
                  .containsEntry(Key.AP_ACTIVATIONS, ValueUtils.listValue(List.of()))
                  .hasEntrySatisfying(Key.VENUE_ACTIVATIONS, venueActivations -> {
                    assertThat(venueActivations.getKindCase())
                        .isEqualTo(Value.KindCase.LIST_VALUE);
                    assertThat(venueActivations.hasListValue())
                        .isTrue();
                    assertThat(venueActivations.getListValue().getValuesList())
                        .isNotEmpty()
                        .anySatisfy(value -> {
                          assertThat(value.getKindCase())
                              .isEqualTo(KindCase.STRUCT_VALUE);
                          assertThat(value.hasStructValue())
                              .isTrue();
                          assertThat(value.getStructValue())
                              .isNotNull()
                              .satisfies(structValue -> assertThat(structValue.getFieldsMap())
                                  .containsEntry(
                                      Key.VENUE_ID,
                                      ValueUtils.stringValue(
                                          apFromDb.getApGroup().getVenue().getId()))
                                  .hasEntrySatisfying(Key.AP_SERIAL_NUMBERS, apSerialNumbers -> {
                                    assertThat(apSerialNumbers.getKindCase())
                                        .isEqualTo(Value.KindCase.LIST_VALUE);
                                    assertThat(apSerialNumbers.hasListValue())
                                        .isTrue();
                                    assertThat(apSerialNumbers.getListValue().getValuesList())
                                        .isNotEmpty()
                                        .anySatisfy(ap -> {
                                          assertThat(ap.getKindCase())
                                              .isEqualTo(Value.KindCase.STRING_VALUE);
                                          assertThat(ap.getStringValue())
                                              .isEqualTo(apId);
                                        });
                                  }));
                        });
                  });
            }
          });
    }

    @Nested
    @FeatureFlag(
        enable = {
            WIFI_ETHERNET_SOFTGRE_TOGGLE,
            WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE,
            ACX_UI_ETHERNET_TOGGLE
        })
    class WhenUpdateApLanPortSettingsWithUseVenueSettings {

      private String venueId;
      private String apId;
      private String portId = "1";
      private String clientIsolationProfileIdOnSourceVenue;
      private String softGreProfileIdOnSourceVenue;

      @BeforeEach
      void beforeEach(Tenant tenant, @ApModel("R550") Ap ap, ApGroup apGroup, Venue venue) {
        apId = ap.getId();
        apGroup.getVenue().setCountryCode("US");
        createApLanPort(tenant, ap, venue);
        createVenueModel(tenant, ap.getModel(), venue);
      }

      void createApLanPort(Tenant tenant, Ap ap, Venue venue) {
        venueId = venue.getId();
        dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 5);
        dataHelper.createApLanPortDataWithAdoption(venue, ap, "2", 6);
      }

      void createVenueModel(Tenant tenant, String apModel, Venue venue) {
        var randomClientIsolation = dataHelper.createClientIsolationAllowlist(tenant);
        clientIsolationProfileIdOnSourceVenue = randomClientIsolation.getId();
        var randomSoftGreProfile = dataHelper.createSoftGreProfile(tenant);
        softGreProfileIdOnSourceVenue = randomSoftGreProfile.getId();
        var clientIsolationLanPortActivation =
            getClientIsolationLanPortActivation(randomClientIsolation);
        var softGreLanPortActivation = getSoftGreLanPortActivation(randomSoftGreProfile);
        dataHelper.createVenueLanPortDataWithAdoption(
            venue,
            apModel,
            portId,
            7,
            of(softGreLanPortActivation, clientIsolationLanPortActivation));
        dataHelper.createVenueLanPortDataWithAdoption(venue, apModel, "2", 8);
      }

      @Test
      void thenShouldHandleTheRequestSuccessfully() {
        final var requestId = randomTxId();
        final var userName = randomName();
        final var apLanPortSettings = new ApLanPortSettings();
        apLanPortSettings.setUseVenueSettings(true);
        messageUtil.sendWifiCfgRequest(
            txCtxExtension.getTenantId(), requestId, CfgAction.UPDATE_AP_LAN_PORT_SETTINGS,
            userName, new RequestParams().addPathVariable("serialNumber", apId)
                .addPathVariable("venueId", venueId), apLanPortSettings);
        validateResetApLanPortSettingsWillTriggerCmnCfgCollectorForSoftGreOrClientIsolation(apId,
            txCtxExtension.getTenantId(), requestId);
      }
    }
  }
}
