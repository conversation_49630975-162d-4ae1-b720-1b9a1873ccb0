package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.AssertionsForClassTypes.tuple;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantCurrentFirmware;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Sql(statements = """
    INSERT INTO tenant(id) VALUES 
      ('8bfeae74-63fb-4269-a5a0-a8e9b8189f07'), 
      ('5o6ko3k2-3l4o-555l-34ko-3ko4kofk4okf'), 
      ('4kfk5kgk-gkke-rkrk-5543-loprtoro<PERSON>ee'),
      ('rr900093-er44-333e-frrr-ekofo3k23445');
    INSERT INTO ap_version(id) VALUES ('6.2.0.103.1234'), ('6.2.4.103.2321'), ('7.0.0.104.5423');
    INSERT INTO tenant_current_firmware(id, ap_model, firmware, tenant) VALUES 
      ('d969f2fcf8134c928af4a900461a55fb', 'R550', '6.2.0.103.1234', '8bfeae74-63fb-4269-a5a0-a8e9b8189f07'),
      ('56ji3j2ii4i5kfki4ji4k3k5kfgk4i4k', 'R560', '6.2.4.103.2321', '8bfeae74-63fb-4269-a5a0-a8e9b8189f07'),
      ('rtofkeoetrlfo3o45k5o2l4l5l3l2l4l', 'R700', '6.2.4.103.2321', '5o6ko3k2-3l4o-555l-34ko-3ko4kofk4okf'),
      ('6kfgkri5jfijrekkeji4kjrifi5jijdf', 'R760', '7.0.0.104.5423', '5o6ko3k2-3l4o-555l-34ko-3ko4kofk4okf'),
      ('35k6i2k4k56i45k3k3ik5k43k3k23k45', 'R650', '7.0.0.104.5423', '4kfk5kgk-gkke-rkrk-5543-loprtororoee');
    """)
@WifiJpaDataTest
public class TenantCurrentFirmwareRepositoryTest {
  @Autowired
  private TenantCurrentFirmwareRepository repository;

  @Test
  public void findByTenantIdAndApModelInTest() {
    Assertions.assertThat(repository
            .findByTenantIdAndApModelIn("8bfeae74-63fb-4269-a5a0-a8e9b8189f07", List.of("R550", "R560", "R700")))
        .isNotNull()
        .extracting(TenantCurrentFirmware::getApModel, tcf -> tcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R550","6.2.0.103.1234"),
            tuple("R560","6.2.4.103.2321"));
    Assertions.assertThat(repository
            .findByTenantIdAndApModelIn("5o6ko3k2-3l4o-555l-34ko-3ko4kofk4okf", List.of("R700")))
        .isNotNull()
        .extracting(TenantCurrentFirmware::getApModel, tcf -> tcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R700","6.2.4.103.2321"));
    Assertions.assertThat(repository
            .findByTenantIdAndApModelIn("4kfk5kgk-gkke-rkrk-5543-loprtororoee", List.of("R650")))
        .isNotNull()
        .extracting(TenantCurrentFirmware::getApModel, tcf -> tcf.getFirmware().getId())
        .containsExactlyInAnyOrder(tuple("R650","7.0.0.104.5423"));
  }

  @Test
  public void findByTenantIdAndApModelTest() {
    Assertions.assertThat(repository.findByTenantIdAndApModel("8bfeae74-63fb-4269-a5a0-a8e9b8189f07", "R550"))
        .isPresent().get()
        .extracting(TenantCurrentFirmware::getFirmware)
        .extracting(ApVersion::getId)
        .isEqualTo("6.2.0.103.1234");
    Assertions.assertThat(repository.findByTenantIdAndApModel("8bfeae74-63fb-4269-a5a0-a8e9b8189f07", "R650"))
        .isNotPresent();
    Assertions.assertThat(repository.findByTenantIdAndApModel("4kfk5kgk-gkke-rkrk-5543-loprtororoee", "R650"))
        .isPresent().get()
        .extracting(TenantCurrentFirmware::getFirmware)
        .extracting(ApVersion::getId)
        .isEqualTo("7.0.0.104.5423");
  }

  @Test
  public void existsByTenantIdTest() {
    Assertions.assertThat(repository.existsByTenantId("8bfeae74-63fb-4269-a5a0-a8e9b8189f07")).isTrue();
    Assertions.assertThat(repository.existsByTenantId("4kfk5kgk-gkke-rkrk-5543-loprtororoee")).isTrue();
    Assertions.assertThat(repository.existsByTenantId("5o6ko3k2-3l4o-555l-34ko-3ko4kofk4okf")).isTrue();
    Assertions.assertThat(repository.existsByTenantId("rr900093-er44-333e-frrr-ekofo3k23445")).isFalse();
  }
}
