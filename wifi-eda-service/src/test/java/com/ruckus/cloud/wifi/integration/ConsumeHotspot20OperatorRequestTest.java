package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.friendlyName;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.HOTSPOT20_OPERATOR_INDEX_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20FriendlyName;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Hotspot20FriendlyNameLanguageEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Hotspot20OperatorGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.utils.ValidatorUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeHotspot20OperatorRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private RevisionService revisionService;

  @Nested
  @ApiAction(CfgAction.ADD_HOTSPOT20OPERATOR)
  class ConsumeAddHotspot20OperatorRequestTest {

    @Payload
    private Hotspot20OperatorGenerator hotspot20Operator() {
      return Generators.hotspot20Operator();
    }

    @Test
    void thenShouldAddHotspot20OperatorSuccessfully(TxCtx txCtx, CfgAction apiAction,
      @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Operator payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
      final Hotspot20Operator hotspot20Operator = repositoryUtil.find(
          Hotspot20Operator.class, payload.getId());
      validateDdccmCfgRequestMessages(Map.of(Action.ADD, List.of(payload.getId())), hotspot20Operator);
      validateCmnCfgCollectorMessages(apiAction, payload.getId(), hotspot20Operator,
          Collections.emptyList());
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_HOTSPOT20OPERATOR)
  class ConsumUpdateHotspot20OperatorRequestTest {

    private String hotspot20OperatorId;

    private List<Hotspot20FriendlyNameLanguageEnum> friendlyNameLanguageEnumList;

    private Hotspot20Operator hotspot20Operator;

    @BeforeEach
    void giveHotspot20Operator(final Tenant tenant) {
      hotspot20Operator = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20Operator()
        .generate();
      hotspot20Operator.getFriendlyNames().forEach(friendlyName -> friendlyName.setOperator(hotspot20Operator));
      repositoryUtil.createOrUpdate(hotspot20Operator, tenant.getId(), randomTxId());
      friendlyNameLanguageEnumList = hotspot20Operator.getFriendlyNames().stream().map(Hotspot20FriendlyName::getLanguage).toList();
      hotspot20OperatorId = hotspot20Operator.getId();
    }

    @Payload
    private Hotspot20OperatorGenerator hotspot20Operator() {
      return Generators.hotspot20Operator()
        .setFriendlyNames(friendlyName().setLanguage(options(friendlyNameLanguageEnumList))
          .toListGenerator(friendlyNameLanguageEnumList.size()));
    }

    @Payload("lessThanFriendlyNameInDB")
    private Hotspot20OperatorGenerator lessThanFriendlyNameInDB() {
      return Generators.hotspot20Operator()
        .setFriendlyNames(friendlyName().setLanguage(options(
            friendlyNameLanguageEnumList.stream().collect(
            collectingAndThen(toList(), l-> {
              Collections.shuffle(l);
              return l;
            })).stream().toList())).toListGenerator(friendlyNameLanguageEnumList.size() > 1 ? friendlyNameLanguageEnumList.size() - 1 : 1));
    }

    @Payload("greaterThanFriendlyNameInDB")
    private Hotspot20OperatorGenerator greaterThanFriendlyNameInDB() {
      return Generators.hotspot20Operator()
        .setFriendlyNames(friendlyName().setLanguage(options(
            Arrays.stream(Hotspot20FriendlyNameLanguageEnum.values()).collect(
            collectingAndThen(toList(), l-> {
              Collections.shuffle(l);
              return l;
            })).stream().toList()))
          .toListGenerator(Hotspot20FriendlyNameLanguageEnum.values().length));
    }

    @Payload("onlyFriendlyNamesUpdated")
    private Hotspot20OperatorGenerator onlyFriendlyNamesUpdated() {
      return Generators.hotspot20Operator()
          .setName(always(hotspot20Operator.getName()))
          .setDomainNames(options(hotspot20Operator.getDomainNames()).toListGenerator(hotspot20Operator.getDomainNames().size()))
          .setFriendlyNames(friendlyName().setLanguage(options(friendlyNameLanguageEnumList))
              .toListGenerator(friendlyNameLanguageEnumList.size()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("hotspot20OperatorId", hotspot20OperatorId);
    }

    @Test
    void thenShouldUpdateHotspot20OperatorSuccessfully(TxCtx txCtx, CfgAction apiAction,
      @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Operator payload) {
      validateResult(txCtx, apiAction, hotspot20OperatorId, payload);
      final Hotspot20Operator hotspot20Operator = repositoryUtil.find(
          Hotspot20Operator.class, hotspot20OperatorId);
      validateDdccmCfgRequestMessages(Map.of(Action.MODIFY, List.of(hotspot20OperatorId)), hotspot20Operator);
      validateCmnCfgCollectorMessages(apiAction, hotspot20OperatorId, hotspot20Operator,
          Collections.emptyList());
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_HOTSPOT20OPERATOR, payload = @Payload("lessThanFriendlyNameInDB"))
    void thenShouldUpdateHotspot20OperatorLessThanFriendlyNameInDBSuccessfully(TxCtx txCtx, CfgAction apiAction,
      @Payload("lessThanFriendlyNameInDB") com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Operator payload) {
      validateResult(txCtx, apiAction, hotspot20OperatorId, payload);
      final Hotspot20Operator hotspot20Operator = repositoryUtil.find(
          Hotspot20Operator.class, hotspot20OperatorId);
      validateDdccmCfgRequestMessages(Map.of(Action.MODIFY, List.of(hotspot20OperatorId)), hotspot20Operator);
      validateCmnCfgCollectorMessages(apiAction, hotspot20OperatorId, hotspot20Operator,
          Collections.emptyList());
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_HOTSPOT20OPERATOR, payload = @Payload("greaterThanFriendlyNameInDB"))
    void thenShouldUpdateHotspot20OperatorGreaterThanFriendlyNameInDBSuccessfully(TxCtx txCtx, CfgAction apiAction,
      @Payload("greaterThanFriendlyNameInDB") com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Operator payload) {
      validateResult(txCtx, apiAction, hotspot20OperatorId, payload);
      final Hotspot20Operator hotspot20Operator = repositoryUtil.find(
          Hotspot20Operator.class, hotspot20OperatorId);
      validateDdccmCfgRequestMessages(Map.of(Action.MODIFY, List.of(hotspot20OperatorId)), hotspot20Operator);
      validateCmnCfgCollectorMessages(apiAction, hotspot20OperatorId, hotspot20Operator,
          Collections.emptyList());
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_HOTSPOT20OPERATOR, payload = @Payload("onlyFriendlyNamesUpdated"))
    void thenShouldUpdateHotspot20OperatorOnlyFriendlyNamesUpdatedSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("onlyFriendlyNamesUpdated") com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Operator payload) {
      validateResult(txCtx, apiAction, hotspot20OperatorId, payload);
      final Hotspot20Operator hotspot20Operator = repositoryUtil.find(
          Hotspot20Operator.class, hotspot20OperatorId);
      validateDdccmCfgRequestMessages(Map.of(Action.MODIFY, List.of(hotspot20OperatorId)), hotspot20Operator);
      validateCmnCfgCollectorMessages(apiAction, hotspot20OperatorId, hotspot20Operator,
          Collections.emptyList());
    }
  }


  @Nested
  class ConsumActivateOrDeactivateHotspot20OperatorOnWifiNetworkRequestTest {

    String hotspot20Operator1Id;

    String hotspot20Operator2Id;

    List<String> networkIds;

    @BeforeEach
    void giveHotspot20Operator(final Tenant tenant) {
      Hotspot20Operator hotspot20Operator1 = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20Operator()
          .generate();
      Hotspot20Operator hotspot20Operator2 = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20Operator()
          .generate();
      hotspot20Operator1.getFriendlyNames().forEach(friendlyName -> friendlyName.setOperator(hotspot20Operator1));
      hotspot20Operator2.getFriendlyNames().forEach(friendlyName -> friendlyName.setOperator(hotspot20Operator2));
      repositoryUtil.createOrUpdate(hotspot20Operator1, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20Operator2, tenant.getId(), randomTxId());
      hotspot20Operator1Id = hotspot20Operator1.getId();
      hotspot20Operator2Id = hotspot20Operator2.getId();

      final var network = network(Hotspot20Network.class).generate();

      final var connectionCapability = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20ConnectionCapability().generate();
      final var networkHotspot20Settings = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkHotspot20Settings().generate();
      networkHotspot20Settings.setOperator(hotspot20Operator2);
      connectionCapability.setNetworkHotspot20Settings(networkHotspot20Settings);
      networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability));

      final var hotspot20Network = (Hotspot20Network) network;
      hotspot20Network.getWlan().setNetwork(hotspot20Network);
      hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

      repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(connectionCapability, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

      networkIds = List.of(network.getId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkIds.get(0))
          .addPathVariable("hotspot20OperatorId", hotspot20Operator1Id);
    }

    @ApiAction.RequestParams("activatedHotspotOperator")
    private RequestParams activatedHotspotOperatorRequestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkIds.get(0))
          .addPathVariable("hotspot20OperatorId", hotspot20Operator2Id);
    }

    @Test
    @ApiAction(CfgAction.ACTIVATE_HOTSPOT20OPERATOR_ON_WIFI_NETWORK)
    void thenShouldActivateHotspot20Operator1OnWifiNetworkSuccessfully(TxCtx txCtx, CfgAction apiAction) {
      final Hotspot20Operator hotspot20Operator = repositoryUtil.find(
          Hotspot20Operator.class, hotspot20Operator1Id);
      validateCmnCfgCollectorMessages(apiAction, hotspot20Operator1Id, hotspot20Operator,
          networkIds);
    }

    @Test
    @ApiAction(value = CfgAction.ACTIVATE_HOTSPOT20OPERATOR_ON_WIFI_NETWORK,
        requestParams = @ApiAction.RequestParams("activatedHotspotOperator"))
    void thenShouldActivateHotspot20Operator2OnWifiNetworkWithoutChange(TxCtx txCtx, CfgAction apiAction,
        @ApiAction.RequestParams("activatedHotspotOperator") RequestParams requestParams) {
      assertThat(hotspot20Operator2Id).isEqualTo(requestParams.getPathVariables().get("hotspot20OperatorId"));
      assertCmnCfgCollectorCfgRequestNotSent(txCtx.getTenant());
    }

    @Test
    @ApiAction(value = CfgAction.DEACTIVATE_HOTSPOT20OPERATOR_ON_WIFI_NETWORK,
        requestParams = @ApiAction.RequestParams("activatedHotspotOperator"))
    void thenShouldDeactivateHotspot20Operator2OnWifiNetworkSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @ApiAction.RequestParams("activatedHotspotOperator") RequestParams expectedRequestParams) {
      final Hotspot20Operator hotspot20Operator = repositoryUtil.find(
          Hotspot20Operator.class, hotspot20Operator2Id);
      validateCmnCfgCollectorMessages(apiAction, hotspot20Operator2Id, hotspot20Operator,
          Collections.emptyList());
    }
  }

  @Test
  void shouldThrowsException_DeactivateUnbindedWifiNetwork(Tenant tenant) {
    Hotspot20Operator hotspot20Operator = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20Operator()
        .generate();
    hotspot20Operator.getFriendlyNames().forEach(friendlyName -> friendlyName.setOperator(hotspot20Operator));
    repositoryUtil.createOrUpdate(hotspot20Operator, tenant.getId(), randomTxId());

    final var network = network(Hotspot20Network.class).generate();

    final var connectionCapability = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20ConnectionCapability().generate();
    final var networkHotspot20Settings = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkHotspot20Settings().generate();
    connectionCapability.setNetworkHotspot20Settings(networkHotspot20Settings);
    networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability));

    final var hotspot20Network = (Hotspot20Network) network;
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

    repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(connectionCapability, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

    RequestParams requestParams = new RequestParams()
        .addPathVariable("wifiNetworkId", hotspot20Network.getId())
        .addPathVariable("hotspot20OperatorId", hotspot20Operator.getId());

    assertThatThrownBy(
        () ->
            messageUtil.sendWifiCfgRequest(
                tenant.getId(),
                txCtxExtension.getRequestId(),
                CfgAction.DEACTIVATE_HOTSPOT20OPERATOR_ON_WIFI_NETWORK,
                randomName(),
                requestParams,
                ""))
        .isNotNull()
        .getRootCause()
        .isInstanceOf(InvalidPropertyValueException.class);
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String hotspot20OperatorId,
    com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Operator payload) {

    if (hotspot20OperatorId == null) {
      final String requestId = txCtx.getTxId();
      final String tenantId = txCtx.getTenant();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty, "should be empty");
      return;
    }

    final Hotspot20Operator hotspot20Operator = repositoryUtil.find(
      Hotspot20Operator.class, hotspot20OperatorId);

    if (apiAction == null || apiAction == CfgAction.DELETE_HOTSPOT20OPERATOR || payload == null) {
      assertThat(hotspot20Operator).isNull();
      return;
    }

    assertThat(hotspot20Operator)
      .isNotNull()
      .matches(o -> Objects.equals(o.getId(), hotspot20OperatorId))
      .matches(o -> Objects.equals(o.getName(), payload.getName()));

    assertThat(hotspot20Operator.getDomainNames())
      .isNotNull()
      .hasSizeBetween(1, 8)
      .filteredOn(domainName -> ValidatorUtils.isValidDomainName(domainName))
      .isNotEmpty()
      .allSatisfy(domainName -> assertThat(domainName).isNotEmpty().isIn(payload.getDomainNames()));

    assertThat(hotspot20Operator.getFriendlyNames())
      .isNotNull()
      .hasSizeBetween(1, Hotspot20FriendlyNameLanguageEnum.values().length)
      .extracting(Hotspot20FriendlyName::getLanguage)
      .doesNotHaveDuplicates();

    assertThat(hotspot20Operator.getFriendlyNames())
      .allSatisfy(fn -> assertThat(fn).matches(friendlyName ->
        payload.getFriendlyNames().stream()
          .filter(payloadFn -> payloadFn.getLanguage().equals(friendlyName.getLanguage()))
          .findAny()
          .map(com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20FriendlyName::getName)
          .get()
          .equals(friendlyName.getName())
      ));
  }

  private void validateDdccmCfgRequestMessages(Map<Action, List<String>> actionIdMap,
      Hotspot20Operator payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(Operation.class::cast)
            .filteredOn(Operation::hasHotspot20Operator)
            .hasSize(actionIdMap.values().stream().mapToInt(List::size).sum()).satisfies(ops -> {
              assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                      Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
              if (actionIdMap.containsKey(Action.DELETE)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.DELETE)
                    .hasSize(actionIdMap.get(Action.DELETE).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.DELETE)
                                .contains(dhcpServiceProfile.getId())));
              }
              if (actionIdMap.containsKey(Action.ADD)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.ADD)
                    .hasSize(actionIdMap.get(Action.ADD).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getHotspot20Operator)
                            .matches(hotspot20Operator -> actionIdMap.get(Action.ADD)
                                .contains(hotspot20Operator.getId())).matches(
                                hotspot20Operator -> assertDdccmDetail(hotspot20Operator, payload)));
              }
              if (actionIdMap.containsKey(Action.MODIFY)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.MODIFY)
                    .hasSize(actionIdMap.get(Action.MODIFY).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getHotspot20Operator)
                            .matches(hotspot20Operator -> actionIdMap.get(Action.MODIFY)
                                .contains(hotspot20Operator.getId())).matches(
                                hotspot20Operator -> actionIdMap.get(Action.MODIFY)
                                    .contains(hotspot20Operator.getId())).matches(
                                hotspot20Operator -> assertDdccmDetail(hotspot20Operator, payload)));
              }
            }));
  }

  private boolean assertDdccmDetail(
      com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20Operator hotspot20Operator,
      Hotspot20Operator payload) {
    assertThat(hotspot20Operator)
        .isNotNull()
        .matches(ho -> ho.getId().equals(payload.getId()))
        .matches(ho -> ho.getName().equals(payload.getName()));

    List<String> ddccmDomainNames = hotspot20Operator.getDomainNameList()
            .stream().map(StringValue::getValue)
            .toList();
    assertThat(ddccmDomainNames)
        .isNotEmpty()
        .matches(domainNames -> domainNames.size() == payload.getDomainNames().size())
        .allSatisfy(domainName -> assertTrue(payload.getDomainNames().contains(domainName)));

    assertThat(hotspot20Operator.getFriendlyNameList())
        .isNotEmpty()
        .matches(friendlyNames -> friendlyNames.size() == payload.getFriendlyNames().size())
        .allSatisfy(friendlyName ->
            assertThat(friendlyName).matches(fn ->
              payload.getFriendlyNames().stream().anyMatch(
                pfn -> pfn.getName().equals(fn.getName())
                    && pfn.getLanguage().ordinal() + 1 == fn.getLanguage().ordinal()))
        );

    return true;
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction,
      String operatorId, Hotspot20Operator payload, List<String> networkIds) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException().isThrownBy(
        () -> {
          assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                  msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> HOTSPOT20_OPERATOR_INDEX_NAME.equals(op.getIndex()))
              .filteredOn(op -> operatorId.equals(op.getId()))
              .allMatch(op -> op.getOpType() == opType(apiAction)).allSatisfy(op -> {
                if (op.getOpType() != OpType.DEL) {
                  assertThat(op).extracting(Operations::getDocMap).matches(
                          doc -> operatorId.equals(
                              doc.get(Key.ID).getStringValue())).matches(
                          doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                      .matches(
                          doc -> payload.getName().equals(doc.get(Key.NAME).getStringValue()))
                      .matches(doc -> assertCmmDetails(payload, doc))
                      .matches(doc -> {
                        List<String> cmnNetworkIds = doc.get(Key.WIFI_NETWORK_IDS).getListValue().getValuesList()
                            .stream().map(Value::getStringValue).toList();
                        return networkIds.size() == cmnNetworkIds.size() &&
                            networkIds.stream().allMatch(id -> cmnNetworkIds.contains(id));
                      });
                }
              });
        });
  }

  private boolean assertCmmDetails(Hotspot20Operator operator, Map<String, Value> doc) {
    List<String> cmnDomainNames = doc.get(Key.DOMAIN_NAMES).getListValue().getValuesList()
        .stream().map(Value::getStringValue).toList();
    assertThat(operator.getDomainNames())
        .isNotEmpty()
        .allSatisfy(domainName -> assertThat(domainName).isNotEmpty().isIn(cmnDomainNames));

    List<Hotspot20FriendlyName> cmnFriendlyNames = doc.get(Key.FRIENDLY_NAMES).getListValue()
        .getValuesList()
        .stream().map(Value::getStructValue).map(struct -> {
          Hotspot20FriendlyName fn = new Hotspot20FriendlyName();
          fn.setName(struct.getFieldsMap().get(Key.NAME).getStringValue());
          fn.setLanguage(Hotspot20FriendlyNameLanguageEnum
              .valueOf(struct.getFieldsMap().get(Key.LANGUAGE).getStringValue()));
          return fn;
        }).toList();

    assertThat(operator.getFriendlyNames())
        .isNotEmpty()
        .allSatisfy(friendlyName -> assertThat(friendlyName).matches(fn ->
          cmnFriendlyNames.stream().anyMatch(cmnFn -> cmnFn.getName().equals(fn.getName()) &&
              cmnFn.getLanguage().equals(fn.getLanguage()))
        ));
    return true;
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_HOTSPOT20OPERATOR -> OpType.ADD;
      case UPDATE_HOTSPOT20OPERATOR, ACTIVATE_HOTSPOT20OPERATOR_ON_WIFI_NETWORK,
          DEACTIVATE_HOTSPOT20OPERATOR_ON_WIFI_NETWORK -> OpType.MOD;
      case DELETE_HOTSPOT20OPERATOR -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
