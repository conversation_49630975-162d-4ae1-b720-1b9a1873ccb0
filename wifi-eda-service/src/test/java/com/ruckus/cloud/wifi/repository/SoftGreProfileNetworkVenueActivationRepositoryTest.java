package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('id-1');
    INSERT INTO venue (id, name, tenant) VALUES ('id-2', 'venue-1', 'id-1');
    INSERT INTO network (id, tenant, type) VALUES ('id-3', 'id-1', 'OPEN');
    INSERT INTO network (id, tenant, type) VALUES ('id-9', 'id-1', 'OPEN');
    INSERT INTO network_venue (id, tenant, venue, network) VALUES ('id-4', 'id-1', 'id-2', 'id-3');
    INSERT INTO network_venue (id, tenant, venue, network) VALUES ('id-10', 'id-1', 'id-2', 'id-9');
    INSERT INTO soft_gre_profile (id, tenant) VALUES ('id-5', 'id-1');
    INSERT INTO soft_gre_profile_network_venue_activation (id, tenant, soft_gre_profile, network_venue) VALUES ('id-6', 'id-1', 'id-5', 'id-4');
    INSERT INTO ipsec_profile (id, tenant) VALUES ('id-7', 'id-1');
    INSERT INTO soft_gre_profile_network_venue_activation (id, tenant, soft_gre_profile, network_venue, ipsec_profile) VALUES ('id-8', 'id-1', 'id-5', 'id-10', 'id-7');
    """)
public class SoftGreProfileNetworkVenueActivationRepositoryTest {

  @Autowired
  private SoftGreProfileNetworkVenueActivationRepository repository;

  @Test
  public void testExistsByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId() {
    var exist = repository.existsByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId(
        "id-1", "id-3", "id-2", "id-5");
    assertThat(exist).isTrue();
  }

  @Test
  public void testExistsByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileIdAndIpsecProfileId() {
    var exist = repository.existsByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileIdAndIpsecProfileId(
        "id-1", "id-9", "id-2", "id-5", "id-7");
    assertThat(exist).isTrue();
  }

  @Test
  public void testFindByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileIdAndIpsecProfileId() {
    var data = repository.findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileIdAndIpsecProfileId(
        "id-1", "id-9", "id-2", "id-5", "id-7");
    assertThat(data.isPresent()).isTrue();
  }

  @Test
  public void testFindByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId() {
    var data = repository.findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId(
        "id-1", "id-9", "id-2", "id-5");
    assertThat(data.isPresent()).isTrue();
  }
}