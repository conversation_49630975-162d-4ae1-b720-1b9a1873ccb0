package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.acx.aggregate.notification.protobuf.message.NotificationRecord;
import com.ruckus.cloud.tenantservice.api.GetTenantResponse;
import com.ruckus.cloud.tenantservice.api.TenantType;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.sql.Date;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@WifiIntegrationTest
@FeatureFlag(enable = {FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
public class ConsumeSkipScheduleRequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private VenueRepository venueRepository;
  @Autowired private UpgradeScheduleRepository upgradeScheduleRepository;
  @Autowired private TenantClient tenantClient;

  @Value("${topic.nuvo.notificationRequests}")
  private String nuvoNotificationRequests;

  @Value("${topic.aggregateNotification.records}")
  private String aggregateNotificationRecords;

  @Test
  public void skipMultipleSchedule(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(
          value = "*********.55555")
          ApVersion version) {

    List<Venue> venueList =
        List.of(VenueTestFixture.randomVenue(tenant), VenueTestFixture.randomVenue(tenant));

    ScheduleTimeSlot sts = newScheduleTimeSlot();
    repositoryUtil.createOrUpdate(sts, tenant.getId(), randomTxId());
    // Create test venues and upgrade schedules
    venueList.forEach(
        venue -> {
          repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
          UpgradeSchedule schedule = newUpgradeSchedule(sts, version, tenant);
          schedule.setVenue(venue);
          repositoryUtil.createOrUpdate(schedule, tenant.getId(), randomTxId());
          ApGroup apGroup =
              ApGroupTestFixture.randomApGroup(venue, (group) -> group.setIsDefault(true));
          repositoryUtil.createOrUpdate(apGroup, tenant.getId(), randomTxId());
          venue.setApGroups(List.of(apGroup));
          repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
          Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
          Assertions.assertNotNull(updatedVenue);
          assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId()))
              .isNotNull().hasSize(1);
        });

    List<String> venueIdList =
        venueList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());

    // When
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.SKIP_UPGRADE_SCHEDULE,
        randomName(),
        new RequestParams(),
        venueIdList);

    // Then
    Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
    assertThat(updatedTenant.getVenues()).isNotNull().hasSize(2);
    updatedTenant.getVenues().forEach(
        venue -> assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId()))
            .isNotNull().hasSize(0));
    assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
  }

  @Nested
  class SkipScheduleWithMspEcTest {

    @BeforeEach
    void givenMspEc(Tenant tenant) {
      GetTenantResponse getTenantResponse = GetTenantResponse.newBuilder()
        .setTenantType(TenantType.MSP_EC)
        .build();
      doReturn(getTenantResponse).when(tenantClient).getTenantData(eq(tenant.getId()), anyString());
    }
    @Test
    public void multiVenuesWithFF(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55555")
      ApVersion version) {

      List<Venue> venueList =
        List.of(VenueTestFixture.randomVenue(tenant), VenueTestFixture.randomVenue(tenant));
      doTest(tenant, venueList, version);
    }

    @Test
    public void oneVenueWithFF(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55555")
      ApVersion version) {

      List<Venue> venueList = List.of(VenueTestFixture.randomVenue(tenant));
      doTest(tenant, venueList, version);
    }

    @Test
    public void multiVenues(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55555")
      ApVersion version) {

      List<Venue> venueList =
        List.of(VenueTestFixture.randomVenue(tenant), VenueTestFixture.randomVenue(tenant));
      doTest(tenant, venueList, version);
    }

    @Test
    public void oneVenue(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55555")
      ApVersion version) {

      List<Venue> venueList = List.of(VenueTestFixture.randomVenue(tenant));
      doTest(tenant, venueList, version);
    }

    private void doTest(Tenant tenant, List<Venue> venueList, ApVersion version) {

      ScheduleTimeSlot sts = newScheduleTimeSlot();
      repositoryUtil.createOrUpdate(sts, tenant.getId(), randomTxId());
      final ApVersion defaultApVersion = repositoryUtil.createOrUpdate(new ApVersion("*********.44444"), tenant.getId(), randomTxId());
      // Create test venues and upgrade schedules
      venueList.forEach(
        venue -> {
          venue.setWifiFirmwareVersion(defaultApVersion);
          repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

          UpgradeSchedule schedule = newUpgradeSchedule(sts, version, tenant);
          schedule.setVenue(venue);
          schedule = repositoryUtil.createOrUpdate(schedule, tenant.getId(), randomTxId());
          UpgradeScheduleFirmwareVersion upgradeScheduleFirmwareVersion = new UpgradeScheduleFirmwareVersion();
          upgradeScheduleFirmwareVersion.setApFirmwareVersion(version);
          upgradeScheduleFirmwareVersion.setUpgradeSchedule(schedule);
          repositoryUtil.createOrUpdate(upgradeScheduleFirmwareVersion, tenant.getId(), randomTxId());

          ApGroup apGroup =
            ApGroupTestFixture.randomApGroup(venue, (group) -> group.setIsDefault(true));
          repositoryUtil.createOrUpdate(apGroup, tenant.getId(), randomTxId());
          venue.setApGroups(List.of(apGroup));
          repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
          Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
          Assertions.assertNotNull(updatedVenue);
          assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId()))
            .isNotNull().hasSize(1);
        });

      List<String> venueIdList =
        venueList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());
      int venueSize = venueIdList.size();

      // When
      messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.SKIP_UPGRADE_SCHEDULE,
        randomName(),
        new RequestParams(),
        venueIdList);

      // Then
      Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
      assertThat(updatedTenant.getVenues()).isNotNull().hasSize(venueSize);
      updatedTenant.getVenues().forEach(
        venue -> assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId()))
          .isNotNull().hasSize(0));
      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(messageUtil.receive(aggregateNotificationRecords)).isNotNull()
        .extracting(ConsumerRecord::value)
        .satisfies(bytes -> assertThat(NotificationRecord.parseFrom(bytes))
          .isNotNull()
          .extracting(NotificationRecord::getVenueList).asList()
          .extracting(com.ruckus.acx.aggregate.notification.protobuf.message.Venue.class::cast)
          .satisfies(venues -> assertSoftly(softly -> softly.assertThat(venues)
            .hasSize(venueSize)
            .allMatch(venue -> venue.getNewVersion().contains(version.getName()))
            .allMatch(venue -> venue.getLegacyVersion().equals("*********.44444")))
          ));
    }
  }



  private ScheduleTimeSlot newScheduleTimeSlot() {
    ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
    Instant startTime = Instant.now();
    sts.setStartDateTime(Date.from(startTime));
    sts.setEndDateTime(Date.from(startTime.plusSeconds(60 * 60 * 2)));
    sts.setTotalCapacityVenue(2500);
    return sts;
  }

  private UpgradeSchedule newUpgradeSchedule(
      ScheduleTimeSlot timeSlot, ApVersion version, Tenant tenant) {
    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(timeSlot);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(version);
    us.setTenant(tenant);
    return us;
  }
}
