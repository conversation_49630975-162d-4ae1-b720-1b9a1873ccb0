package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.service.ExtendedNetworkTemplateServiceCtrl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
@FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
public class ConsumeGuestNetworkTemplateWithOweTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedNetworkTemplateServiceCtrl networkTemplateServiceCtrl;

  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;
  
  @SneakyThrows
  @Test
  public void testAddGuestNetworkTemplate(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    var guestNetwork = NetworkTestFixture.randomGuestOweNetwork(tenant, r -> {});

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, map(guestNetwork));
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, tenantId);

    var guestNetworkAdded = (GuestNetwork) networkTemplateServiceCtrl.getNetworkTemplate(guestNetwork.getId());
    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops",
        () -> assertEquals(2, viewOps.size()),
        // "OWE Master network"
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> guestNetworkAdded.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> guestNetworkAdded.getOwePairNetworkId().equals(o.getDocMap().get(EsConstants.Key.OWE_PAIR_NETWORK_ID).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())),
        // "OWE Slave network"
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> guestNetworkAdded.getOwePairNetworkId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> guestNetworkAdded.getId().equals(o.getDocMap().get(EsConstants.Key.OWE_PAIR_NETWORK_ID).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue()))
    );
  }

  @Test
  public void testUpdateGuestNetworkTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    var guestOweMasterNetwork = NetworkTestFixture.randomGuestOweNetwork(tenant, r -> {
      r.setIsTemplate(true);
      r.setIsOweMaster(true);
    });
    guestOweMasterNetwork.getGuestPortal().setNetwork(guestOweMasterNetwork);
    var guestOweSlaveNetwork = NetworkTestFixture.randomGuestOweNetwork(tenant, r -> {
      r.setIsTemplate(true);
      r.setIsOweMaster(false);
    });
    guestOweSlaveNetwork.getGuestPortal().setNetwork(guestOweSlaveNetwork);
    guestOweMasterNetwork.setOwePairNetworkId(guestOweSlaveNetwork.getId());
    guestOweSlaveNetwork.setOwePairNetworkId(guestOweMasterNetwork.getId());
    repositoryUtil.createOrUpdate(guestOweMasterNetwork, tenantId, randomTxId());
    repositoryUtil.createOrUpdate(guestOweSlaveNetwork, tenantId, randomTxId());

    // update Network
    guestOweMasterNetwork.setName(guestOweMasterNetwork.getName() + "-updated");
    RequestParams rps = new RequestParams().addPathVariable("networkTemplateId", guestOweMasterNetwork.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_NETWORK_TEMPLATE, userName, rps, map(guestOweMasterNetwork));
    assertActivityStatusSuccess(UPDATE_NETWORK_TEMPLATE, tenantId);

    var guestNetworkUpdated = (GuestNetwork) networkTemplateServiceCtrl.getNetworkTemplate(guestOweMasterNetwork.getId());

    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops",
        () -> assertEquals(2, viewOps.size()),
        // "OWE Master network"
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> guestNetworkUpdated.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.MOD)
            .filter(o -> guestNetworkUpdated.getOwePairNetworkId().equals(o.getDocMap().get(EsConstants.Key.OWE_PAIR_NETWORK_ID).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())),
        // "OWE Slave network"
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> guestNetworkUpdated.getOwePairNetworkId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.MOD)
            .filter(o -> guestNetworkUpdated.getId().equals(o.getDocMap().get(EsConstants.Key.OWE_PAIR_NETWORK_ID).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue()))
    );
  }

  @Test
  public void testDeleteGuestNetworkTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    var guestNetwork = NetworkTestFixture.randomGuestOweNetwork(tenant, r -> {});
    guestNetwork.getGuestPortal().setNetwork(guestNetwork);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, map(guestNetwork));
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, tenantId);
    var viewOpsAdd = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops", () -> assertEquals(2, viewOpsAdd.size()));

    var guestOweMasterNetwork = (GuestNetwork) networkTemplateServiceCtrl.getNetworkTemplate(guestNetwork.getId());

    RequestParams rps = new RequestParams().addPathVariable("networkTemplateId", guestOweMasterNetwork.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_NETWORK_TEMPLATE, userName, rps, map(guestOweMasterNetwork));
    assertActivityStatusSuccess(DELETE_NETWORK_TEMPLATE, tenantId);

    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops",
        () -> assertEquals(2, viewOps.size()),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> guestOweMasterNetwork.getId().equals(o.getId()))
            .anyMatch(o -> o.getOpType() == OpType.DEL)),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> guestOweMasterNetwork.getOwePairNetworkId().equals(o.getId()))
            .anyMatch(o -> o.getOpType() == OpType.DEL))
    );
  }

  @Test
  public void addGuestNetworkWithOweByTemplate(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    var ecTenant = TenantTestFixture.randomTenant((t) -> {});
    var ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenantId, randomTxId());

    var guestNetwork = NetworkTestFixture.randomGuestOweNetwork(mspTenant, n -> {
      n.setIsTemplate(true);
      n.getGuestPortal().setUserSessionTimeout((short) 2);
      n.getGuestPortal().setUserSessionGracePeriod((short) 2);
    });
    guestNetwork.getGuestPortal().setNetwork(guestNetwork);
    sendWifiCfgRequest(mspTenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, map(guestNetwork));
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, mspTenantId);

    var viewOps = receiveViewmodelCollectorOperations(1, mspTenantId);
    log.warn("viewOps: {}", viewOps);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelHas2Ops(viewOps), // 2 networks, one is OWE transition slave network
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> guestNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .anyMatch(o -> guestNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())))
    );

    var guestNetworkAdded = repositoryUtil.find(GuestNetwork.class, guestNetwork.getId(), mspTenantId);
    assertNotNull(guestNetworkAdded);
    assertTrue(guestNetworkAdded.getIsOweMaster());
    assertNotNull(guestNetworkAdded.getOwePairNetworkId());

    // create ec network by msp network template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.NETWORK, instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, guestNetworkAdded.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);

    var ecGuestNetwork = repositoryUtil.find(GuestNetwork.class, instanceId, ecTenantId);
    assertNotNull(ecGuestNetwork);
    assertEquals(instanceId, ecGuestNetwork.getId());
    assertEquals(guestNetworkAdded.getId(), ecGuestNetwork.getTemplateId());
    assertEquals(guestNetworkAdded.getUpdatedDate().getTime(), ecGuestNetwork.getTemplateVersion());
    assertTrue(ecGuestNetwork.getIsOweMaster());
    assertNotNull(ecGuestNetwork.getOwePairNetworkId());

    var ecViewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelHas2Ops(ecViewOps),
        () -> assertTrue(ecViewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecGuestNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .anyMatch(o -> ecGuestNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())))
    );
  }

  private void assertViewmodelHas2Ops(List<Operations> operations) {
    assertEquals(2, operations.size());
  }
}
