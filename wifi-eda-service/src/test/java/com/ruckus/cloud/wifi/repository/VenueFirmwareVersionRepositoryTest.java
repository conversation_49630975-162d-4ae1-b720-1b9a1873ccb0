package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.repository.VenueFirmwareVersionRepository.BranchApFirmware;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

@WifiJpaDataTest(showSql = false)
class VenueFirmwareVersionRepositoryTest {

  @Autowired
  private VenueFirmwareVersionRepository vfvRepository;

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO ap_version (id, name) VALUES ('*********.123', '*********.123');
      INSERT INTO venue (id, tenant) VALUES
          ('e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO venue_firmware_version (id, branch_type, current_firmware_version, venue, tenant) VALUES
          ('a09c1264ae704ba1969152aa44cc083a', 'eol-ap-2022-12', '*********.123', 'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c');
      """)
  void findByVenue() {
    String venue = "e619cccb6bd74075ba0b220611969aab";
    assertThat(vfvRepository.findBranchApFirmwareByVenue(venue))
        .isNotNull()
        .singleElement()
        .extracting(BranchApFirmware::getFirmwareVersion)
        .isEqualTo("*********.123");
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO ap_version (id, name) VALUES ('*********.123', '*********.123');
      INSERT INTO ap_version (id, name) VALUES ('*********.123', '*********.123');
      INSERT INTO venue (id, tenant) VALUES
          ('e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO venue_firmware_version (id, branch_type, current_firmware_version, venue, tenant) VALUES
          ('a09c1264ae704ba1969152aa44cc083a', 'ABF2-3R', '*********.123', 'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO venue_firmware_version (id, branch_type, current_firmware_version, venue, tenant) VALUES
          ('a09c1264ae704ba1969152aa44cc0111', 'ABF2-3R', '*********.123', 'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c');
      """)
  void findMultipleFirmwareByVenue() {
    String venue = "e619cccb6bd74075ba0b220611969aab";
    assertThat(vfvRepository.findBranchApFirmwareByVenue(venue))
        .isNotNull()
        .hasSize(2);
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO ap_version (id, name) VALUES 
          ('*********.123', '*********.123'), 
          ('*********.123', '*********.123'), 
          ('*********.552', '*********.552');
      INSERT INTO venue (id, tenant) VALUES
          ('e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c'), 
          ('1zogrv5f8r2e5d5eerf2f2d2y2jy2jhy', '3057a924ea8148bab5cc1682ff09275c'),
          ('g5ttgeqwfgodkjkof85854fgwojoweee', '3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO venue_firmware_version (id, branch_type, current_firmware_version, venue, tenant) VALUES
          ('h4g47h4t11r4r4r4w5e5re5r5wrwe4rf', 'ABF1', '*********.552', 'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c'),
          ('h5y2555h22h04ba1969152aa44cc083a', 'ABF2-3R', '*********.123', 'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c'),
          ('a09c1264ae704ba1969152aa44cc0111', 'ABF2-3R', '*********.123', 'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c'),
          ('e4e3e3ere4ba19691ee52aa44cc0e111', 'ABF2-3R', '*********.123', '1zogrv5f8r2e5d5eerf2f2d2y2jy2jhy','3057a924ea8148bab5cc1682ff09275c'),
          ('foefekrok4k76kl4k3lle3odkfeffwef', 'ABF2-3R', '*********.123', 'g5ttgeqwfgodkjkof85854fgwojoweee','3057a924ea8148bab5cc1682ff09275c');
      """)
  void findByTenantIdAndVenueIn() {
    List<Venue> venues = List.of(
        new Venue("e619cccb6bd74075ba0b220611969aab"),
        new Venue("1zogrv5f8r2e5d5eerf2f2d2y2jy2jhy"));

    assertThat(vfvRepository.findByTenantIdAndVenueIn("3057a924ea8148bab5cc1682ff09275c", venues))
        .isNotNull()
        .hasSize(4)
        .extracting(VenueFirmwareVersion::getVenue)
        .extracting(Venue::getId)
        .contains(
            "e619cccb6bd74075ba0b220611969aab",
            "1zogrv5f8r2e5d5eerf2f2d2y2jy2jhy");
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO ap_version (id, name) VALUES 
          ('*********.123', '*********.123'), 
          ('*********.123', '*********.123'), 
          ('*********.552', '*********.552');
      INSERT INTO venue (id, tenant) VALUES
          ('e619cccb6bd74075ba0b220611969aab', '3057a924ea8148bab5cc1682ff09275c'), 
          ('1zogrv5f8r2e5d5eerf2f2d2y2jy2jhy', '3057a924ea8148bab5cc1682ff09275c'),
          ('g5ttgeqwfgodkjkof85854fgwojoweee', '3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO venue_firmware_version (id, branch_type, current_firmware_version, venue, tenant) VALUES
          ('h4g47h4t11r4r4r4w5e5re5r5wrwe4rf', 'ABF1', '*********.552', 'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c'),
          ('h5y2555h22h04ba1969152aa44cc083a', 'ABF2-3R', '*********.123', 'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c'),
          ('a09c1264ae704ba1969152aa44cc0111', 'ABF2-3R', '*********.123', 'e619cccb6bd74075ba0b220611969aab','3057a924ea8148bab5cc1682ff09275c'),
          ('e4e3e3ere4ba19691ee52aa44cc0e111', 'ABF2-3R', '*********.123', '1zogrv5f8r2e5d5eerf2f2d2y2jy2jhy','3057a924ea8148bab5cc1682ff09275c'),
          ('foefekrok4k76kl4k3lle3odkfeffwef', 'ABF2-3R', '*********.123', 'g5ttgeqwfgodkjkof85854fgwojoweee','3057a924ea8148bab5cc1682ff09275c');
      """)
  void findByTenantIdAndIdIn() {
    List<String> venues = List.of(
        "e619cccb6bd74075ba0b220611969aab",
        "1zogrv5f8r2e5d5eerf2f2d2y2jy2jhy");

    assertThat(vfvRepository.findByTenantIdAndVenueIdIn("3057a924ea8148bab5cc1682ff09275c", venues))
        .isNotNull()
        .hasSize(4)
        .extracting(VenueFirmwareVersion::getVenue)
        .extracting(Venue::getId)
        .contains(
            "e619cccb6bd74075ba0b220611969aab",
            "1zogrv5f8r2e5d5eerf2f2d2y2jy2jhy");
  }
}
