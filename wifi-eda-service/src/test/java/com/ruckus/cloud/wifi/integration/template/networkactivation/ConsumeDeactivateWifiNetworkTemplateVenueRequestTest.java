package com.ruckus.cloud.wifi.integration.template.networkactivation;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio;
import static com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture.randomNetworkApGroup;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueSchedulerTestFixture.randomNetworkVenueScheduler;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplate;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("NetworkActivationTemplateTest")
@WifiIntegrationTest
class ConsumeDeactivateWifiNetworkTemplateVenueRequestTest {

  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private RepositoryUtil repositoryUtil;

  @Nested
  class GivenOpenNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String networkVenueId;

    @BeforeEach
    void givenOpenNetworkAndRelatedEntitiesPersistedInDb(@Template @OpenNetwork Network network,
        @Template Venue venue,
        @Template @DefaultApGroup ApGroup apGroup,
        @Template @ScheduledNetworkVenue NetworkVenue networkVenue, NetworkApGroup networkApGroup,
        NetworkApGroupRadio networkApGroupRadio) {
      networkId = network.getId();
      venueId = venue.getId();
      networkVenueId = networkVenue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.DEACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, networkId, venueId);
    }
  }

  @Nested
  class GivenOweTransitionNetworkPersistedInDb {

    private String networkId1;
    private String networkId2;
    private String venueId;
    private String networkVenueId1;
    private String networkVenueId2;

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupPersistedInDb(Tenant tenant,
        @Template Venue venue, @Template @DefaultApGroup ApGroup apGroup) {

      final var tenantId = tenant.getId();
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      Network master = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .setIsTemplate(alwaysTrue())
          .generate();
      Network slave = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .setIsTemplate(alwaysTrue())
          .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setIsOweMaster(true);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setOwePairNetworkId(
          slave.getId());
      master = repositoryUtil.createOrUpdate(master, tenantId, randomTxId());
      final var networkVenue1 = randomNetworkVenueTemplate(master, venue);
      randomNetworkVenueScheduler(networkVenue1);
      repositoryUtil.createOrUpdate(networkVenue1, tenantId, randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setOwePairNetworkId(
          master.getId());
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setIsOweMaster(false);
      slave = repositoryUtil.createOrUpdate(slave, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(slave, tenantId, randomTxId());
      final var networkVenue2 = randomNetworkVenueTemplate(slave, venue);
      randomNetworkVenueScheduler(networkVenue2);
      repositoryUtil.createOrUpdate(networkVenue2, slave.getTenant().getId(), randomTxId());

      final var networkApGroup1 = randomNetworkApGroup(networkVenue1, apGroup);
      repositoryUtil.createOrUpdate(networkApGroup1, tenantId, randomTxId());
      final var networkApGroup2 = randomNetworkApGroup(networkVenue2, apGroup);
      repositoryUtil.createOrUpdate(networkApGroup2, tenantId, randomTxId());

      final var networkApGroupRadio1 = randomNetworkApGroupRadio(networkApGroup1);
      repositoryUtil.createOrUpdate(networkApGroupRadio1, tenantId, randomTxId());
      final var networkApGroupRadio2 = randomNetworkApGroupRadio(networkApGroup2);
      repositoryUtil.createOrUpdate(networkApGroupRadio2, tenantId, randomTxId());

      networkId1 = master.getId();
      networkId2 = slave.getId();
      venueId = venue.getId();
      networkVenueId1 = networkVenue1.getId();
      networkVenueId2 = networkVenue2.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId1);
    }

    @Test
    @ApiAction(CfgAction.DEACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE)
    void thenShouldHandleDeactivateOweTransitionNetworkOnVenueSuccessfully(TxCtx txCtx) {
      validateOweTransitionResult(txCtx.getTenant(), txCtx.getTxId(),
          List.of(networkVenueId1, networkVenueId2), List.of(networkId1, networkId2), venueId);
      validateOweTransitionWifiCfgChangeMessage(txCtx.getTenant(), txCtx.getTxId());
    }

    private void validateOweTransitionResult(String tenantId, String requestId,
        List<String> networkVenueIds,
        List<String> networkIds, String venueId) {
      final var networkVenue1 = repositoryUtil.find(NetworkVenue.class, networkVenueIds.get(0));
      final var networkVenue2 = repositoryUtil.find(NetworkVenue.class, networkVenueIds.get(1));

      assertThat(networkVenue1).isNull();
      assertThat(networkVenue2).isNull();

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2)
              .allMatch(op -> op.getOpType() == OpType.DEL)
              .allMatch(op -> List.of(networkVenueId1, networkVenueId2).contains(op.getId())));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.DEACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allMatch(op -> op.getAction() == Action.DELETE)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasWlanApGroup).count() == 2,
                  "The count of WlanApGroup operations should be 2") // 2.4G
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 2,
                  "The count of VenueSchedule operations should be 2")
              .filteredOn(Operation::hasWlanVenue)
              .hasSize(2)
              .allMatch(op -> op.getAction() == Action.DELETE,
                  String.format("The value of `action` field in WlanVenue operation should be %s",
                      Action.DELETE))
              .allSatisfy(op -> {
                assertSoftly(softly -> {
                  softly.assertThat(
                      List.of(networkId1, networkId2).contains(op.getWlanVenue().getWlanId()));
                  softly.assertThat(op.getWlanVenue().getVenueId())
                      .isEqualTo(venueId);
                });
              }));
    }

    private void validateOweTransitionWifiCfgChangeMessage(String tenantId, String txId) {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, txId);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txId);
      assertThat(wifiCfgChangeMessageRecord.getPayload()).isNotNull();
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(txId))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasNetworkVenue()).findFirst().get()
                  .getNetworkVenue())
          .matches(n -> List.of(StringValue.of(networkId1), StringValue.of(networkId2))
              .contains(n.getNetworkId()));
    }
  }

  @Nested
  class GivenGuestNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String networkVenueId;

    @BeforeEach
    void givenGuestNetworkAndRelatedEntitiesPersistedInDb(@Template GuestNetwork network,
        @Template Venue venue, @Template @DefaultApGroup ApGroup apGroup) {
      network.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
      repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());

      final var networkVenue = randomNetworkVenueTemplate(network, venue);
      randomNetworkVenueScheduler(networkVenue);
      repositoryUtil.createOrUpdate(networkVenue, network.getTenant().getId(), randomTxId());

      final var networkApGroup = randomNetworkApGroup(networkVenue, apGroup);
      repositoryUtil.createOrUpdate(networkApGroup, network.getTenant().getId(), randomTxId());

      final var networkApGroupRadio = randomNetworkApGroupRadio(networkApGroup);
      repositoryUtil.createOrUpdate(networkApGroupRadio, network.getTenant().getId(), randomTxId());

      networkId = network.getId();
      venueId = venue.getId();
      networkVenueId = networkVenue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.DEACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, networkId, venueId);
    }
  }

  @Nested
  class GivenDPSKNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String networkVenueId;

    @BeforeEach
    void givenDPSKNetworkAndRelatedEntitiesPersistedInDb(
        @Template DpskNetwork dpskNetwork, @Template Venue venue,
        @Template @DefaultApGroup ApGroup apGroup) {
      final var tenantId = dpskNetwork.getTenant().getId();
      dpskNetwork.getAuthRadius().setAuthNetworks(List.of(dpskNetwork));
      repositoryUtil.createOrUpdate(dpskNetwork, tenantId, randomTxId());

      final var networkVenue = randomNetworkVenueTemplate(dpskNetwork, venue);
      randomNetworkVenueScheduler(networkVenue);
      repositoryUtil.createOrUpdate(networkVenue, tenantId, randomTxId());

      final var networkApGroup = randomNetworkApGroup(networkVenue, apGroup);
      repositoryUtil.createOrUpdate(networkApGroup, dpskNetwork.getTenant().getId(), randomTxId());

      final var networkApGroupRadio = randomNetworkApGroupRadio(networkApGroup);
      repositoryUtil.createOrUpdate(networkApGroupRadio, dpskNetwork.getTenant().getId(),
          randomTxId());

      networkId = dpskNetwork.getId();
      venueId = venue.getId();
      networkVenueId = networkVenue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.DEACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) throws InvalidProtocolBufferException {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueId, networkId, venueId);
      validateWifiCfgChangeMessage(txCtx.getTenant(), txCtx.getTxId());
    }

    private void validateWifiCfgChangeMessage(String tenantId, String txId) {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, txId);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txId);
      assertThat(wifiCfgChangeMessageRecord.getPayload()).isNotNull();
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(txId))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasNetworkVenue()).findFirst().get()
                  .getNetworkVenue())
          .matches(n -> n.getNetworkId().equals(StringValue.of(networkId)));
    }
  }

  void validateResult(String tenantId, String requestId, String networkVenueId,
      String networkId, String venueId) {
    final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);

    assertThat(networkVenue).isNull();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
            .hasSize(1)
            .singleElement()
            .matches(op -> op.getOpType() == OpType.DEL)
            .matches(op -> networkVenueId.equals(op.getId())));

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(ApiFlowNames.DEACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operation.class::cast)
            .allMatch(op -> op.getAction() == Action.DELETE)
            .allSatisfy(op -> assertThat(op)
                .extracting(Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                    String.format("The value of `requestId` field in CommonInfo should be [%s]",
                        requestId))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                    String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                        tenantId))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                    String.format("The value of `sender` field in CommonInfo should be [%s]",
                        ServiceType.WIFI_SERVICE)))
            .matches(ops -> ops.stream().filter(Operation::hasWlanApGroup).count() == 1,
                "The count of WlanApGroup operations should be 1") // 2.4G
            .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 1,
                "The count of VenueSchedule operations should be 1")
            .filteredOn(Operation::hasWlanVenue)
            .hasSize(1)
            .singleElement()
            .matches(op -> op.getAction() == Action.DELETE,
                String.format("The value of `action` field in WlanVenue operation should be %s",
                    Action.DELETE))
            .matches(op -> networkVenueId.equals(op.getId()),
                String.format("The value of `id` field in WlanVenue operation should be %s",
                    networkVenueId))
            .satisfies(op -> {
              assertSoftly(softly -> {
                softly.assertThat(op.getWlanVenue().getWlanId())
                    .isEqualTo(networkId);
                softly.assertThat(op.getWlanVenue().getVenueId())
                    .isEqualTo(venueId);
              });
            }));
  }

  @Nested
  class GivenDsaeNetworkPersistedInDb {

    private String networkId1;
    private String networkId2;
    private String venueId;
    private String networkVenueId1;
    private String networkVenueId2;

    @BeforeEach
    void givenDsaeNetworkAndRelatedEntitiesPersistedInDb(
        Tenant tenant, @Template Venue venue, @Template @DefaultApGroup ApGroup apGroup) {
      final var tenantId = tenant.getId();

      final var dsaeNetwork1 = (DpskNetwork) network(DpskNetwork.class).generate();
      dsaeNetwork1.getWlan().setNetwork(dsaeNetwork1);
      dsaeNetwork1.setName("dsaeNetwork1");
      dsaeNetwork1.setDsaeNetworkPairId(dsaeNetwork1.getId());
      dsaeNetwork1.setIsDsaeServiceNetwork(true);
      dsaeNetwork1.getWlan().setWlanSecurity(
          WlanSecurityEnum.WPA23Mixed);
      dsaeNetwork1.setIsTemplate(true);
      repositoryUtil.createOrUpdate(dsaeNetwork1, tenant.getId(), randomTxId());
      final var networkVenue1 = randomNetworkVenueTemplate(dsaeNetwork1, venue);
      randomNetworkVenueScheduler(networkVenue1);
      repositoryUtil.createOrUpdate(networkVenue1, tenantId, randomTxId());

      final var dsaeNetwork2 = (DpskNetwork) network(DpskNetwork.class).generate();
      dsaeNetwork2.getWlan().setNetwork(dsaeNetwork2);
      dsaeNetwork2.setName("dsaeNetwork2");
      dsaeNetwork2.setDsaeNetworkPairId(dsaeNetwork1.getId());
      dsaeNetwork2.setIsDsaeServiceNetwork(false);
      dsaeNetwork2.getWlan().setWlanSecurity(
          WlanSecurityEnum.WPA2Personal);
      dsaeNetwork2.setIsTemplate(true);
      repositoryUtil.createOrUpdate(dsaeNetwork2, tenantId, randomTxId());
      final var networkVenue2 = randomNetworkVenueTemplate(dsaeNetwork2, venue);
      randomNetworkVenueScheduler(networkVenue2);
      repositoryUtil.createOrUpdate(networkVenue2, tenantId, randomTxId());

      final var networkApGroup1 = randomNetworkApGroup(networkVenue1, apGroup);
      repositoryUtil.createOrUpdate(networkApGroup1, tenantId, randomTxId());
      final var networkApGroup2 = randomNetworkApGroup(networkVenue2, apGroup);
      repositoryUtil.createOrUpdate(networkApGroup2, tenantId, randomTxId());

      final var networkApGroupRadio1 = randomNetworkApGroupRadio(networkApGroup1);
      repositoryUtil.createOrUpdate(networkApGroupRadio1, tenantId, randomTxId());
      final var networkApGroupRadio2 = randomNetworkApGroupRadio(networkApGroup2);
      repositoryUtil.createOrUpdate(networkApGroupRadio2, tenantId, randomTxId());

      networkId1 = dsaeNetwork1.getId();
      networkId2 = dsaeNetwork2.getId();
      venueId = venue.getId();
      networkVenueId1 = networkVenue1.getId();
      networkVenueId2 = networkVenue2.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId1);
    }

    @Test
    @ApiAction(CfgAction.DEACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE)
    void thenShouldHandleDeactivateWifiDsaeNetworkOnVenueHandlerSuccessfully(TxCtx txCtx) {
      validateDsaeResult(txCtx.getTenant(), txCtx.getTxId(),
          List.of(networkVenueId1, networkVenueId2), List.of(networkId1, networkId2), venueId);
      validateDsaeWifiCfgChangeMessage(txCtx.getTenant(), txCtx.getTxId());
    }

    private void validateDsaeWifiCfgChangeMessage(String tenantId, String txId) {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, txId);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txId);
      assertThat(wifiCfgChangeMessageRecord.getPayload()).isNotNull();
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(txId))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasNetworkVenue()).findFirst().get()
                  .getNetworkVenue())
          .matches(n -> List.of(StringValue.of(networkId1), StringValue.of(networkId2))
              .contains(n.getNetworkId()));
    }

    private void validateDsaeResult(String tenantId, String requestId, List<String> networkVenueIds,
        List<String> networkIds, String venueId) {
      final var networkVenue1 = repositoryUtil.find(NetworkVenue.class, networkVenueIds.get(0));
      final var networkVenue2 = repositoryUtil.find(NetworkVenue.class, networkVenueIds.get(1));

      assertThat(networkVenue1).isNull();
      assertThat(networkVenue2).isNull();

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2)
              .allMatch(op -> op.getOpType() == OpType.DEL)
              .allMatch(op -> List.of(networkVenueId1, networkVenueId2).contains(op.getId())));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.DEACTIVATE_WIFI_NETWORK_TEMPLATE_ON_VENUE))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allMatch(op -> op.getAction() == Action.DELETE)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasWlanApGroup).count() == 2,
                  "The count of WlanApGroup operations should be 2") // 2.4G
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 2,
                  "The count of VenueSchedule operations should be 2")
              .filteredOn(Operation::hasWlanVenue)
              .hasSize(2)
              .allMatch(op -> op.getAction() == Action.DELETE,
                  String.format("The value of `action` field in WlanVenue operation should be %s",
                      Action.DELETE))
              .allSatisfy(op -> {
                assertSoftly(softly -> {
                  softly.assertThat(
                      List.of(networkId1, networkId2).contains(op.getWlanVenue().getWlanId()));
                  softly.assertThat(op.getWlanVenue().getVenueId())
                      .isEqualTo(venueId);
                });
              }));
    }
  }
}