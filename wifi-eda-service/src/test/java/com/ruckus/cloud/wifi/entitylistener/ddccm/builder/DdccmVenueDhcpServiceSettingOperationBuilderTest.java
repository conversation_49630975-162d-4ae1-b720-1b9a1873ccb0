package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpPoolVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceAp;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfileVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueDhcpServiceSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApDhcpRoleEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpWanPortSelectionModeEnum;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.DhcpCapabilitiesHelper;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;

@WifiUnitTest
public class DdccmVenueDhcpServiceSettingOperationBuilderTest {

  @Autowired
  private DdccmVenueDhcpServiceSettingOperationBuilder builder;

  @Autowired
  private ApRepository apRepository;

  @Autowired
  private VenueRepository venueRepository;

  @Autowired
  private DhcpCapabilitiesHelper dhcpCapabilitiesHelper;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyMspTenantId");

  @Test
  public void testAddVenueDhcpServiceSetting_FromDhcpConfigServiceProfile() {
    Venue venue = createEachAPsDhcpVenue();
    List<DhcpPoolVenue> dhcpPoolVenues = createDhcpPoolVenue(3);
    venue.setDhcpPoolVenues(dhcpPoolVenues);
    Operation operation = builder.buildVenueDhcpServiceSettingOperation(EntityAction.ADD, venue, emptyTxChanges());

    assertEquals(Action.ADD, operation.getAction());
    assertEquals(venue.getId(), operation.getVenueDhcpServiceSetting().getId());
    assertEquals(venue.getId(), operation.getVenueDhcpServiceSetting().getVenueId());
    assertTrue(operation.getVenueDhcpServiceSetting().getEnabled().getValue());
    assertEquals(DhcpModeEnum.EnableOnEachAPs.name(), operation.getVenueDhcpServiceSetting().getMode());
    assertTrue(operation.getVenueDhcpServiceSetting().getDynamicWanPortDetection().getValue());

    assertEquals(venue.getDhcpPoolVenues().size(),
        operation.getVenueDhcpServiceSetting().getDhcpServiceProfileIdsCount());
  }

  @Test
  public void testAddVenueDhcpServiceSetting_FromDhcpConfigServiceProfile_DisableDhcpSetting() {
    Venue venue = createDisabledDhcpVenue();
    List<DhcpPoolVenue> dhcpPoolVenues = createDhcpPoolVenue(3);
    venue.setDhcpPoolVenues(dhcpPoolVenues);
    venue.setTenant(new Tenant(genUUID()));
    Operation operation = builder.buildVenueDhcpServiceSettingOperation(EntityAction.ADD, venue, emptyTxChanges());

    assertEquals(Action.ADD, operation.getAction());
    assertEquals(venue.getId(), operation.getVenueDhcpServiceSetting().getId());
    assertEquals(venue.getId(), operation.getVenueDhcpServiceSetting().getVenueId());
    assertFalse(operation.getVenueDhcpServiceSetting().getEnabled().getValue());
    assertEquals(0,
        operation.getVenueDhcpServiceSetting().getDhcpServiceProfileIdsCount());
  }

  private static Venue createDisabledDhcpVenue() {
    Venue venue = new Venue(genUUID());
    venue.setWifiFirmwareVersion(new ApVersion("*********.317"));

    VenueDhcpServiceSetting venueDhcpServiceSetting = new VenueDhcpServiceSetting();
    venueDhcpServiceSetting.setEnabled(false);
    venueDhcpServiceSetting.setMode(DhcpModeEnum.EnableOnEachAPs);
    venueDhcpServiceSetting.setWanPortSelectionMode(DhcpWanPortSelectionModeEnum.Dynamic);

    return venue;
  }

  private static Venue createEachAPsDhcpVenue() {
    Venue venue = new Venue(genUUID());
    venue.setWifiFirmwareVersion(new ApVersion("*********.317"));

    VenueDhcpServiceSetting venueDhcpServiceSetting = new VenueDhcpServiceSetting();
    venueDhcpServiceSetting.setEnabled(true);
    venueDhcpServiceSetting.setMode(DhcpModeEnum.EnableOnEachAPs);
    venueDhcpServiceSetting.setWanPortSelectionMode(DhcpWanPortSelectionModeEnum.Dynamic);

    DhcpServiceProfileVenue dhcpServiceProfileVenue = new DhcpServiceProfileVenue(genUUID());
    dhcpServiceProfileVenue.setDhcpServiceProfile(new DhcpServiceProfile(genUUID()));

    DhcpServiceProfileVenue dhcpServiceProfileVenue2 = new DhcpServiceProfileVenue(genUUID());
    dhcpServiceProfileVenue2.setDhcpServiceProfile(new DhcpServiceProfile(genUUID()));

    venue.setDhcpServiceSetting(venueDhcpServiceSetting);
    venue.setDhcpServiceProfileVenues(List.of(dhcpServiceProfileVenue, dhcpServiceProfileVenue2));
    return venue;
  }

  private static Venue createMultipleAPsDhcpVenue() {
    Venue venue = new Venue(genUUID());
    venue.setWifiFirmwareVersion(new ApVersion("*********.317"));

    VenueDhcpServiceSetting venueDhcpServiceSetting = new VenueDhcpServiceSetting();
    venueDhcpServiceSetting.setEnabled(true);
    venueDhcpServiceSetting.setMode(DhcpModeEnum.EnableOnMultipleAPs);
    venueDhcpServiceSetting.setWanPortSelectionMode(DhcpWanPortSelectionModeEnum.Dynamic);

    DhcpServiceProfileVenue dhcpServiceProfileVenue = new DhcpServiceProfileVenue(genUUID());
    dhcpServiceProfileVenue.setDhcpServiceProfile(new DhcpServiceProfile(genUUID()));

    venue.setDhcpServiceSetting(venueDhcpServiceSetting);
    venue.setDhcpServiceProfileVenues(List.of(dhcpServiceProfileVenue));
    venue.setDhcpServiceAps(Set.of(createPrimaryDhcpServiceAp(venue),
        createBackupDhcpServiceAp(venue),
        createNatGateway1DhcpServiceAp(venue),
        createNatGateway2DhcpServiceAp(venue)));
    return venue;
  }

  private static Venue createHierarchicalAPsDhcpVenue() {
    Venue venue = new Venue(genUUID());
    venue.setWifiFirmwareVersion(new ApVersion("*********.317"));

    VenueDhcpServiceSetting venueDhcpServiceSetting = new VenueDhcpServiceSetting();
    venueDhcpServiceSetting.setEnabled(true);
    venueDhcpServiceSetting.setMode(DhcpModeEnum.EnableOnHierarchicalAPs);
    venueDhcpServiceSetting.setWanPortSelectionMode(DhcpWanPortSelectionModeEnum.Dynamic);

    DhcpServiceProfileVenue dhcpServiceProfileVenue = new DhcpServiceProfileVenue(genUUID());
    dhcpServiceProfileVenue.setDhcpServiceProfile(new DhcpServiceProfile(genUUID()));

    venue.setDhcpServiceSetting(venueDhcpServiceSetting);
    venue.setDhcpServiceProfileVenues(List.of(dhcpServiceProfileVenue));
    venue.setDhcpServiceAps(Set.of(createPrimaryDhcpServiceAp(venue),
        createBackupDhcpServiceAp(venue)));
    return venue;
  }

  private static Ap createAp(String id, String mac) {
    Ap ap = new Ap(id);
    ap.setMac(mac);
    return ap;
  }

  private static DhcpServiceAp createPrimaryDhcpServiceAp(Venue venue) {
    DhcpServiceAp primaryDhcpAp = new DhcpServiceAp(genUUID());
    primaryDhcpAp.setSerialNumber("************");
    primaryDhcpAp.setRole(ApDhcpRoleEnum.PrimaryServer);
    primaryDhcpAp.setVenue(venue);
    primaryDhcpAp.setDhcpIps(List.of("************", "***********"));
    return primaryDhcpAp;
  }

  private static DhcpServiceAp createBackupDhcpServiceAp(Venue venue) {
    DhcpServiceAp backupDhcpAp = new DhcpServiceAp(genUUID());
    backupDhcpAp.setSerialNumber("************");
    backupDhcpAp.setRole(ApDhcpRoleEnum.BackupServer);
    backupDhcpAp.setVenue(venue);
    backupDhcpAp.setDhcpIps(List.of("************", "***********"));
    return backupDhcpAp;
  }

  private static DhcpServiceAp createNatGateway1DhcpServiceAp(Venue venue) {
    DhcpServiceAp natGateway1 = new DhcpServiceAp(genUUID());
    natGateway1.setSerialNumber("182172004472");
    natGateway1.setRole(ApDhcpRoleEnum.NatGateway);
    natGateway1.setVenue(venue);
    natGateway1.setDhcpIps(List.of("************", "***********"));
    return natGateway1;
  }

  private static DhcpServiceAp createNatGateway2DhcpServiceAp(Venue venue) {
    DhcpServiceAp natGateway2 = new DhcpServiceAp(genUUID());
    natGateway2.setSerialNumber("************");
    natGateway2.setRole(ApDhcpRoleEnum.NatGateway);
    natGateway2.setVenue(venue);
    natGateway2.setDhcpIps(List.of("************", "***********"));
    return natGateway2;
  }

  private List<DhcpPoolVenue> createDhcpPoolVenue(int amount) {
    List<DhcpPoolVenue> dhcpPoolVenues = new ArrayList<>();
    for (int i =0;i<amount;i++) {
      DhcpPoolVenue dhcpPoolVenue = new DhcpPoolVenue(genUUID());
      dhcpPoolVenue.setDhcpPool(new DhcpServiceProfile(genUUID()));
      dhcpPoolVenues.add(dhcpPoolVenue);
    }

    return dhcpPoolVenues;
  }


  private static String genUUID() {
    return UUID.randomUUID().toString();
  }

  @Profile("unit")
  @TestConfiguration
  static class TestConfig {

    @Bean
    public DdccmVenueDhcpServiceSettingOperationBuilder ddccmVenueDhcpServiceSettingOperationBuilder() {
      DdccmVenueDhcpServiceSettingOperationBuilder builder = Mockito.spy(DdccmVenueDhcpServiceSettingOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }

    @Bean
    public DhcpCapabilitiesHelper mockDhcpCapabilitiesHelper() {
      return Mockito.mock(DhcpCapabilitiesHelper.class);
    }

    @Bean
    public ApRepository mockApRepository() {
      return Mockito.mock(ApRepository.class);
    }

    @Bean
    public VenueRepository mockVenueRepository() {
      return Mockito.mock(VenueRepository.class);
    }
  }

}
