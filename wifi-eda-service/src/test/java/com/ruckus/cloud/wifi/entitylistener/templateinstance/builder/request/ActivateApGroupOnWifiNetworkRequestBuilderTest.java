package com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request;

import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.AP_GROUP_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.VENUE_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.WIFI_NETWORK_ID;
import static com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture.randomApGroup;
import static com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture.randomNetworkApGroup;
import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomNetworkTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplate;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.TenantTestFixture.randomTenant;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplateInstance;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.ActivateApGroupOnWifiNetworkInstanceOperation;
import com.ruckus.cloud.wifi.repository.ApGroupRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;

@ExtendWith(TxCtxExtension.class)
@WifiUnitTest
public class ActivateApGroupOnWifiNetworkRequestBuilderTest {

  @Autowired
  private ActivateApGroupOnWifiNetworkRequestBuilder unit;
  @MockBean
  private VenueRepository venueRepository;
  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private ApGroupRepository apGroupRepository;
  @MockBean
  private ActivateApGroupOnWifiNetworkInstanceOperation operation;

  private Tenant mspTenant;
  private Tenant ecTenant;
  private NetworkApGroup networkApGroupTemplate;
  private NetworkApGroup networkApGroupInstance;
  private ApGroup apGroupTemplate;
  private ApGroup apGroupInstance;
  private NetworkVenue networkVenueTemplate;
  private Venue venueTemplate;
  private Network networkTemplate;
  private NetworkVenue networkVenueInstance;
  private Venue venueInstance;
  private Network networkInstance;

  @BeforeEach
  public void setUp() {
    ecTenant = randomTenant((e) -> {
    });
    mspTenant = randomTenant(e -> e.setId(TxCtxHolder.tenantId()));
    unit.setTemplateTenantId(mspTenant.getId());
    unit.setTargetTenantId(ecTenant.getId());
    networkVenueTemplate = randomNetworkVenueTemplate(mspTenant);
    venueTemplate = networkVenueTemplate.getVenue();
    networkTemplate = networkVenueTemplate.getNetwork();
    networkInstance = randomNetworkTemplateInstance(
        networkTemplate.getId(),
        ecTenant);
    venueInstance = randomVenueTemplateInstance(venueTemplate.getId(),
        ecTenant);
    networkVenueInstance = randomNetworkVenueTemplateInstance(networkVenueTemplate.getId(),
        networkInstance, venueInstance);
    apGroupTemplate = randomApGroup(venueTemplate);
    apGroupInstance = randomApGroup(venueInstance);
    networkApGroupTemplate = randomNetworkApGroup(networkVenueTemplate, apGroupTemplate);
    networkApGroupInstance = randomNetworkApGroup(networkVenueInstance, apGroupInstance);
  }

  @Test
  void testBuild() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(apGroupInstance))
        .when(apGroupRepository)
        .findByTemplateIdAndTenantId(eq(apGroupTemplate.getId()),
            eq(ecTenant.getId()));

    var result = unit.build(networkApGroupTemplate);

    assertThat(result).isNotEmpty();

    var request = result.get().request();
    var templateEntity = result.get().templateEntity();

    assertThat(request).satisfies(e -> {
      assertThat(e.getTargetTenantId()).isEqualTo(ecTenant.getId());
      assertThat(e.getInstanceId()).isNotEmpty();
      assertThat(e.getOverrides()).isNull();
    });
    assertThat(request.getExtraPathVariables())
        .containsOnly(entry(VENUE_ID, venueInstance.getId()),
            entry(WIFI_NETWORK_ID, networkInstance.getId()),
            entry(AP_GROUP_ID, apGroupInstance.getId()));

    assertThat(templateEntity)
        .isEqualTo(networkApGroupTemplate);
  }

  @Test
  void testBuild_EntityIsNotTemplate() {
    final var networkApGroup = randomNetworkApGroup(networkVenueInstance, apGroupInstance);
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(apGroupInstance))
        .when(apGroupRepository)
        .findByTemplateIdAndTenantId(eq(apGroupTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(CommonException.class, () -> unit.build(networkApGroup));
  }

  @Test
  void testBuild_NetworkInstanceNotFound() {
    doReturn(Optional.empty())
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(apGroupInstance))
        .when(apGroupRepository)
        .findByTemplateIdAndTenantId(eq(apGroupTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(NullPointerException.class, () -> unit.build(networkApGroupTemplate));
  }

  @Test
  void testBuild_VenueInstanceNotFound() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.empty())
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(apGroupInstance))
        .when(apGroupRepository)
        .findByTemplateIdAndTenantId(eq(apGroupTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(NullPointerException.class, () -> unit.build(networkApGroupTemplate));
  }

  @Test
  void testBuild_ApGroupInstanceNotFound() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.empty())
        .when(apGroupRepository)
        .findByTemplateIdAndTenantId(eq(apGroupTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(NullPointerException.class, () -> unit.build(networkApGroupTemplate));
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    ActivateApGroupOnWifiNetworkRequestBuilder activateApGroupOnWifiNetworkRequestBuilder(
        VenueRepository venueRepository, NetworkRepository networkRepository,
        ApGroupRepository apGroupRepository,
        ActivateApGroupOnWifiNetworkInstanceOperation operation) {
      return new ActivateApGroupOnWifiNetworkRequestBuilder(networkRepository,
          venueRepository, apGroupRepository, operation);
    }
  }
}
