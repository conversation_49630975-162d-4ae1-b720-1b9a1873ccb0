package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value.TYPE_DEVICE_POLICY;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.DeviceTypeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.OSAccessEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.OsVendorEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DevicePolicyGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.viewmodel.DevicePolicyRule;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.fixture.DevicePolicyTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

;

@Tag("DevicePolicyTest")
@WifiIntegrationTest
public class ConsumeDevicePolicyRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_DEVICE_POLICY)
  class ConsumeAddDevicePolicyRequestTest {

    @Payload
    private final DevicePolicyGenerator generator = Generators.devicePolicy();

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload DevicePolicy payload) {
      final com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy devicePolicy = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy.class, payload.getId());
      validateRepositoryData(payload, devicePolicy);
      validateDdccmCfgRequestMessages(CfgAction.ADD_DEVICE_POLICY, List.of(payload.getId()),
          devicePolicy);
      validateCmnCfgCollectorMessages(CfgAction.ADD_DEVICE_POLICY, List.of(payload.getId()),
          devicePolicy);
      validateActivityMessages(CfgAction.ADD_DEVICE_POLICY);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_DEVICE_POLICY)
  class ConsumeUpdateDevicePolicyRequestTest {

    @Payload
    private final DevicePolicyGenerator generator = Generators.devicePolicy();

    private String devicePolicyId;

    @BeforeEach
    void givenOneRowPersistedInDb(final Tenant tenant,
        final com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy devicePolicy) {
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
          .generate();
      accessControlProfile.setDevicePolicy(devicePolicy);
      accessControlProfile.setDevicePolicyEnable(true);
      repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());
      devicePolicyId = devicePolicy.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("devicePolicyId", devicePolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload DevicePolicy payload) {
      final com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy devicePolicy = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy.class, devicePolicyId);
      payload.setId(devicePolicyId);
      validateRepositoryData(payload, devicePolicy);
      validateDdccmCfgRequestMessages(CfgAction.UPDATE_DEVICE_POLICY, List.of(payload.getId()),
          devicePolicy);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_DEVICE_POLICY, List.of(payload.getId()),
          devicePolicy);
      validateActivityMessages(CfgAction.UPDATE_DEVICE_POLICY);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_DEVICE_POLICY)
  class ConsumeDeleteDevicePolicyRequestTest {

    private String devicePolicyId;

    @BeforeEach
    void givenOneRowPersistedInDb(
        final com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy devicePolicy) {
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
          .generate();
      accessControlProfile.setDevicePolicy(devicePolicy);
      accessControlProfile.setDevicePolicyEnable(true);
      repositoryUtil.createOrUpdate(accessControlProfile, devicePolicy.getTenant().getId(),
          randomTxId());
      devicePolicyId = devicePolicy.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("devicePolicyId", devicePolicyId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      assertNull(repositoryUtil.find(com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy.class,
          devicePolicyId));
      validateDdccmCfgRequestMessages(CfgAction.DELETE_DEVICE_POLICY, List.of(devicePolicyId),
          null);
      validateCmnCfgCollectorMessages(CfgAction.DELETE_DEVICE_POLICY, List.of(devicePolicyId),
          null);
      validateActivityMessages(CfgAction.DELETE_DEVICE_POLICY);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_BULK_DEVICE_POLICIES)
  class ConsumeDeleteDevicePoliciesRequestTest {

    @Payload
    private final List<String> idList = new ArrayList<>();

    @BeforeEach
    void givenTenRowsPersistedInDb(final Tenant tenant,
        com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy devicePolicy) {
      for (int i = 0; i < 9; i++) {
        devicePolicy = DevicePolicyTestFixture.randomDevicePolicy();
        devicePolicy = repositoryUtil.createOrUpdate(devicePolicy, tenant.getId(), randomTxId());
        idList.add(devicePolicy.getId());
      }
      AccessControlProfile accessControlProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile()
          .generate();
      accessControlProfile.setDevicePolicy(devicePolicy);
      accessControlProfile.setDevicePolicyEnable(true);
      repositoryUtil.createOrUpdate(accessControlProfile, devicePolicy.getTenant().getId(),
          randomTxId());
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload List<String> payload) {
      assertThat(idList).extracting(
              id -> repositoryUtil.find(com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy.class, id))
          .isNotEmpty().allMatch(Objects::isNull);
      validateDdccmCfgRequestMessages(CfgAction.DELETE_BULK_DEVICE_POLICIES, idList, null);
      validateCmnCfgCollectorMessages(CfgAction.DELETE_BULK_DEVICE_POLICIES, idList, null);
      validateActivityMessages(CfgAction.DELETE_BULK_DEVICE_POLICIES);
    }
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, List<String> idList,
      com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy payload) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast).satisfies(ops -> {
              assertThat(ops).filteredOn(
                      op -> Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME.equals(op.getIndex()))
                  .hasSize(idList.size()).allMatch(op -> idList.contains(op.getId()))
                  .allMatch(op -> op.getOpType() == opType(apiAction)).allSatisfy(op -> {
                    if (op.getOpType() != OpType.DEL) {
                      assertThat(op).extracting(Operations::getDocMap)
                          .matches(doc -> idList.contains(doc.get(EsConstants.Key.ID).getStringValue()))
                          .matches(doc -> tenantId.equals(
                              doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                          .matches(doc -> payload.getName().equals(doc.get(Key.NAME).getStringValue()))
                          .matches(doc -> TYPE_DEVICE_POLICY.equals(doc.get(Key.TYPE))).matches(
                              doc -> payload.getDescription()
                                  .equals(doc.get(Key.DESCRIPTION).getStringValue())).matches(
                              doc -> payload.getRules().size() == (doc.get(Key.RULES).getNumberValue()))
                          .matches(
                              doc -> 0 == doc.get(Key.NETWORK_IDS).getListValue().getValuesCount());
                    }
                  });
              if (opType(apiAction) != OpType.ADD) {
                assertThat(ops).filteredOn(
                        op -> Index.ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
                    .hasSize(1).allMatch(op -> op.getOpType() == OpType.MOD).allSatisfy(op -> {
                      if (opType(apiAction) == OpType.DEL) {
                        assertThat(op).extracting(Operations::getDocMap).matches(
                                doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                            .matches(doc -> doc.get(Key.DEVICE_POLICY_ID).getStringValue().isEmpty())
                            .matches(doc -> doc.get(Key.DEVICE_POLICY_NAME).getStringValue().isEmpty());
                      } else {
                        assertThat(op).extracting(Operations::getDocMap).matches(
                                doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                            .matches(doc -> payload.getName()
                                .equals(doc.get(Key.DEVICE_POLICY_NAME).getStringValue())).matches(
                                doc -> payload.getId()
                                    .equals(doc.get(Key.DEVICE_POLICY_ID).getStringValue()));
                      }
                    });
              }
            }));
  }

  private void validateRepositoryData(DevicePolicy expected,
      com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getDescription(), actual.getDescription());
    assertEquals(expected.getDefaultAccess(), actual.getDefaultAccess());
    assertEquals(expected.getRules().size(), actual.getRules().size());

    for (DevicePolicyRule expectedRule : expected.getRules()) {
      com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicyRule actualRule = actual.getRules().stream()
          .filter(rule -> expectedRule.getName().equals(rule.getName())).findFirst().orElse(null);
      assertNotNull(actualRule);
      assertEquals(expectedRule.getName(), actualRule.getName());
      assertEquals(expectedRule.getAction(), actualRule.getAction());
      assertEquals(expectedRule.getDeviceType(), actualRule.getDeviceType());
      assertEquals(expectedRule.getOsVendor(), actualRule.getOsVendor());
      assertEquals(expectedRule.getDownloadRateLimit(), actualRule.getDownloadRateLimit());
      assertEquals(expectedRule.getUploadRateLimit(), actualRule.getUploadRateLimit());
      assertEquals(expectedRule.getVlan(), actualRule.getVlan());
    }
  }

  private void validateDdccmCfgRequestMessages(CfgAction apiAction, List<String> idList,
      com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .allSatisfy(
                op -> assertThat(op).extracting(
                        com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .satisfies(ops -> {
              assertThat(ops).filteredOn(
                      com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasDevicePolicy)
                  .hasSize(idList.size()).allMatch(op -> idList.contains(op.getId()))
                  .allMatch(op -> op.getAction() == action(apiAction))
                  .allSatisfy(op -> {
                    if (op.getAction() == Action.DELETE) {
                      assertThat(op).extracting(
                              com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDevicePolicy)
                          .matches(vp -> idList.contains(vp.getId()));
                    } else {
                      assertThat(payload).isNotNull();
                      assertThat(op).extracting(
                              com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDevicePolicy)
                          .matches(vp -> idList.contains(vp.getId()))
                          .matches(vp -> vp.getDefaultAccess() == OSAccessEnum.OS_ALLOW)
                          .matches(vp -> payload.getName().equals(vp.getName())).matches(
                              vp -> assertRules(payload.getRules(), vp.getDevicePolicyRulesList()));
                    }
                  });
              if (action(apiAction) == Action.DELETE) {
                assertThat(ops).filteredOn(
                        com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasCcmFirewallProfile).hasSize(1)
                    .first().matches(op -> op.getAction() == Action.MODIFY)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCcmFirewallProfile)
                    .matches(op -> !op.hasFirewallDevicePolicy());
              }
            }));
  }

  private void validateActivityMessages(CfgAction apiAction) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> assertThat(
        activityCfgChangeRespMessage.getPayload()).matches(
            msg -> msg.getStatus().equals(Status.OK))
        .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
        .extracting(ConfigurationStatus::getEventDate).isNotNull());
  }

  private boolean assertRules(List<com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicyRule> rules,
      List<com.ruckus.acx.ddccm.protobuf.wifi.DevicePolicyRule> devicePolicyRulesList) {
    assertEquals(rules.size(), devicePolicyRulesList.size());
    for (com.ruckus.acx.ddccm.protobuf.wifi.DevicePolicyRule actualRule: devicePolicyRulesList) {
      com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicyRule expectedRule = rules.stream().filter(
              rule1 -> rule1.getName().equals(actualRule.getDescription().getValue()))
          .findFirst().get();

      assertEquals(expectedRule.getName(), actualRule.getDescription().getValue());
      assertEquals(OSAccessEnum.OS_BLOCK, actualRule.getAction());
      assertEquals(DeviceTypeEnum.TABLET, actualRule.getDeviceType());
      assertEquals(OsVendorEnum.ALL, actualRule.getOsVendor());
      assertEquals(expectedRule.getDownloadRateLimit(),
          actualRule.getDownloadRateLimit().getValue());
      assertEquals(expectedRule.getUploadRateLimit(), actualRule.getUploadRateLimit().getValue());
      assertEquals(expectedRule.getVlan(), actualRule.getVlan().getValue());
    }
    return true;
  }

  private void validateNothingHappened(CfgAction apiAction) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
    assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
    messageCaptors.assertThat(
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest(),
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(txCtxExtension.getTenantId());
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_DEVICE_POLICY -> Action.ADD;
      case UPDATE_DEVICE_POLICY -> Action.MODIFY;
      case DELETE_DEVICE_POLICY, DELETE_BULK_DEVICE_POLICIES -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_DEVICE_POLICY -> OpType.ADD;
      case UPDATE_DEVICE_POLICY -> OpType.MOD;
      case DELETE_DEVICE_POLICY, DELETE_BULK_DEVICE_POLICIES -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_DEVICE_POLICY -> ApiFlowNames.ADD_DEVICE_POLICY;
      case UPDATE_DEVICE_POLICY -> ApiFlowNames.UPDATE_DEVICE_POLICY;
      case DELETE_DEVICE_POLICY -> ApiFlowNames.DELETE_DEVICE_POLICY;
      case DELETE_BULK_DEVICE_POLICIES -> ApiFlowNames.DELETE_BULK_DEVICE_POLICIES;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
