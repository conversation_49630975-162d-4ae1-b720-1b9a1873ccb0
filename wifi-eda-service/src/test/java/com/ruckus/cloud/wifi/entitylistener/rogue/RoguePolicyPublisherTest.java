package com.ruckus.cloud.wifi.entitylistener.rogue;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.acx.ddccm.protobuf.wifi.RogueApPolicy;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicyVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.kafka.publisher.RoguePolicyPublisher;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Import;

@WifiUnitTest
@Import(RoguePolicyMapperImpl.class)
public class RoguePolicyPublisherTest {

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @MockBean
  private RoguePolicyPublisher roguePolicyPublisher;
  @SpyBean
  private RoguePolicyAggregatedEntityListener listener;

  @Test
  public void test() {
    assertFalse(listener.filter().test(new GuestNetwork()));
    assertFalse(listener.filter().test(new Wlan()));
    assertTrue(listener.filter().test(new RogueClassificationPolicyVenue()));

    Tenant tenant = new Tenant(txCtxExtension.getTenantId());
    Venue v = VenueTestFixture.randomVenue(tenant);
    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("rogue-policy");
    p.setId("rogue-policy-id");
    RogueClassificationPolicyVenue pv = new RogueClassificationPolicyVenue();
    pv.setTenant(tenant);
    pv.setVenue(v);
    pv.setRogueClassificationPolicy(p);

    List<RogueApPolicy> rogueApPolicies = new ArrayList<>();
    rogueApPolicies.addAll(listener.build(new TxEntity<>(pv, EntityAction.ADD), emptyTxChanges()));
    rogueApPolicies.addAll(listener.build(new TxEntity<>(pv, EntityAction.MODIFY), emptyTxChanges()));
    listener.flush(rogueApPolicies);

    verify(roguePolicyPublisher, times(2))
        .publish(
            argThat(message -> StringUtils.isNotBlank(((RogueApPolicy) message).getName())),
            eq(txCtxExtension.getTenantId()), eq(txCtxExtension.getRequestId()));

    clearInvocations(listener);

    rogueApPolicies.clear();
    rogueApPolicies.addAll(listener.build(new TxEntity<>(pv, EntityAction.DELETE), emptyTxChanges()));
    listener.flush(rogueApPolicies);

    verify(roguePolicyPublisher, times(1))
        .publish(
            argThat(message -> StringUtils.isBlank(((RogueApPolicy) message).getName())),
            eq(txCtxExtension.getTenantId()), eq(txCtxExtension.getRequestId()));
  }
}
