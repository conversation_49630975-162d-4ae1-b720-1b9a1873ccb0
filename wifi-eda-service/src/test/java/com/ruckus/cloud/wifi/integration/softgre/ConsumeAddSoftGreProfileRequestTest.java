package com.ruckus.cloud.wifi.integration.softgre;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.SoftGreProfileRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Objects;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SoftGreProfileTest")
@WifiIntegrationTest
class ConsumeAddSoftGreProfileRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeAddSoftGreProfileV1Message {

    @Test
    void givenDuplicatedProfileName(Tenant tenant) {
      var duplicatedName = randomName();
      repositoryUtil.createOrUpdate(
          randomSoftGreProfile(tenant, p -> p.setName(duplicatedName)),
          tenant.getId(),
          randomTxId());

      var request =
          SoftGreProfileRestCtrl.SoftGreProfileMapper.INSTANCE.ServiceSoftGreProfile2SoftGreProfile(
              randomSoftGreProfile(
                  tenant, softGreProfile -> softGreProfile.setName(duplicatedName)));

      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.ADD_SOFT_GRE_PROFILE,
                      randomName(),
                      new RequestParams(),
                      request))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      messageCaptors
          .assertThat(
              messageCaptors.getDdccmMessageCaptor(),
              messageCaptors.getCmnCfgCollectorMessageCaptor())
          .doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ADD_SOFT_GRE_PROFILE));
    }

    @Test
    void givenValidProfile(Tenant tenant) {
      var request =
          SoftGreProfileRestCtrl.SoftGreProfileMapper.INSTANCE.ServiceSoftGreProfile2SoftGreProfile(
              randomSoftGreProfile());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.ADD_SOFT_GRE_PROFILE,
          randomName(),
          new RequestParams(),
          request);

      assertThat(repositoryUtil.find(SoftGreProfile.class, request.getId(), tenant.getId()))
          .isNotNull()
          .matches(p -> Objects.equals(p.getName(), request.getName()))
          .matches(p -> Objects.equals(p.getDescription(), request.getDescription()))
          .matches(p -> Objects.equals(p.getPrimaryGateway(), request.getPrimaryGatewayAddress()))
          .matches(
              p -> Objects.equals(p.getSecondaryGateway(), request.getSecondaryGatewayAddress()))
          .matches(p -> Objects.equals(p.getMtuType(), request.getMtuType()))
          .matches(p -> Objects.equals(p.getMtuSize(), request.getMtuSize()))
          .matches(p -> Objects.equals(p.getKeepAliveInterval(), request.getKeepAliveInterval()))
          .matches(
              p -> Objects.equals(p.getKeepAliveRetryTimes(), request.getKeepAliveRetryTimes()))
          .matches(
              p ->
                  Objects.equals(
                      p.getDisassociateClientEnabled(), request.getDisassociateClientEnabled()));

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getAction() == Action.ADD)
          .matches(o -> o.getId().equals(request.getId()));

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.ADD)
          .matches(o -> o.getId().equals(request.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.ADD_SOFT_GRE_PROFILE));
    }
  }
}
