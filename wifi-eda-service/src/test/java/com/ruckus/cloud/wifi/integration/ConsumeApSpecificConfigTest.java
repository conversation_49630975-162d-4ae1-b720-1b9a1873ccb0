package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.ddccm.protobuf.wifi.AntennaTypeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.ApModelExternalAntenna;
import com.ruckus.cloud.wifi.eda.viewmodel.ApModelLanPort;
import com.ruckus.cloud.wifi.entitylistener.ddccm.DdccmConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.VenueApModelSpecificAttributesTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@Slf4j
public class ConsumeApSpecificConfigTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Test
  void TestApCustomizationImpactedAPs(@ApModel("T750SE") Ap ap, Venue venue )
      throws JsonProcessingException, InvalidProtocolBufferException {

    String userName = randomName();
    String apId = ap.getId();
    String requestId = randomTxId();
    String tenantId = ap.getTenant().getId();

    final var apGroups = Generators.apGroup()
        .setVenue(always(venue)).generate(2);

    apGroups.forEach(apGroup -> {
      Ap apIn = new Ap(randomSerialNumber());
      apIn.setApGroup(apGroup);
      apIn.setName(randomName());
      repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(apIn, tenantId, randomTxId());
    });

    // AP Customization
    var json2 = """
        {
          "externalAntenna": {
            "enable24G": true,
            "enable50G": true,
            "gain24G": 55,
            "gain50G": 45,
            "supportDisable": true,
            "coupled": true
          },
          "poeOut": false,
          "lanPorts": [
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "1",
              "enabled": true
            },
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "2",
              "enabled": true
            },
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "3",
              "enabled": true
            }
          ],
          "ledEnabled": false,
          "useVenueSettings": false,
          "id": null
        }
        """;

    var request2 = objectMapper.readValue(json2, com.ruckus.cloud.wifi.eda.viewmodel.ApModelSpecific.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgAction.UPDATE_AP_CUSTOMIZATION,
        userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()),
        request2);

    var record02 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record02).isNotNull();
    WifiConfigRequest ddccmRequest02 = record02.getPayload();
    log.debug("ddccmRequest={}", ddccmRequest02);

    assertThat(ddccmRequest02.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .hasSize(1);

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));
  }

  @Test
  void TestApLanPortsImpactedAPs(@ApModel("T750SE") Ap ap, Venue venue )
      throws JsonProcessingException, InvalidProtocolBufferException {

    // case 1
    String userName = randomName();
    String apId = ap.getId();
    String requestId = randomTxId();
    String tenantId = ap.getTenant().getId();

    final var apGroups = Generators.apGroup()
        .setVenue(always(venue)).generate(2);

    apGroups.forEach(apGroup -> {
      Ap apIn = new Ap(randomSerialNumber());
      apIn.setApGroup(apGroup);
      apIn.setName(randomName());
      repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(apIn, tenantId, randomTxId());
    });

    // Add venueApModelSpecificAttributes to test get default attributes from venue that do not impact venue level
    final var venueApModelSpecificAttributes = VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(
        venue, v -> {
          v.setLedOn(Boolean.FALSE);
          v.setModel(ap.getModel());
        });
    repositoryUtil.createOrUpdate(venueApModelSpecificAttributes, tenantId, randomTxId());
    venue.setModelSpecificAttributes(List.of(venueApModelSpecificAttributes));
    repositoryUtil.createOrUpdate(venue, tenantId, randomTxId());

    // AP Customization
    var json = """
        {
          "externalAntenna": {
            "enable24G": true,
            "enable50G": true,
            "gain24G": 55,
            "gain50G": 45,
            "supportDisable": true,
            "coupled": true
          },
          "poeOut": false,
          "lanPorts": [
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "1",
              "enabled": true
            },
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "2",
              "enabled": true
            },
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "3",
              "enabled": true
            }
          ],
          "useVenueSettings": false,
          "id": null
        }
        """;

    var request = objectMapper.readValue(json, com.ruckus.cloud.wifi.eda.viewmodel.ApModelLanPort.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgAction.UPDATE_AP_LAN_PORTS,
        userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()),
        request);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .flatExtracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getLanPortList)
        .hasSize(3);

    var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));

    // case 2
    var requestId2 = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId2,
        CfgAction.RESET_AP_LAN_PORTS,
        userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()),
        "");

    var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
    assertThat(record2).isNotNull();
    WifiConfigRequest ddccmRequest2 = record2.getPayload();

    assertThat(ddccmRequest2.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .flatExtracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getLanPortList)
        .hasSize(0);

    var activityImpactedMessage2 = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId2);
    assertThat(activityImpactedMessage2).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId2))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage2.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));
  }

  @Test
  void TestApLedImpactedAPs(@ApModel("R610") Ap ap, Venue venue )
      throws JsonProcessingException, InvalidProtocolBufferException {

    // case 1
    String userName = randomName();
    String apId = ap.getId();
    String requestId = randomTxId();
    String tenantId = ap.getTenant().getId();

    final var apGroups = Generators.apGroup()
        .setVenue(always(venue)).generate(2);

    apGroups.forEach(apGroup -> {
      Ap apIn = new Ap(randomSerialNumber());
      apIn.setApGroup(apGroup);
      apIn.setName(randomName());
      repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(apIn, tenantId, randomTxId());
    });

    // Add venueApModelSpecificAttributes to test get default attributes from venue that do not impact venue level
    final var venueApModelSpecificAttributes = VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(
        venue, v -> {
          v.setLedOn(Boolean.FALSE);
          v.setModel(ap.getModel());
        });
    repositoryUtil.createOrUpdate(venueApModelSpecificAttributes, tenantId, randomTxId());
    venue.setModelSpecificAttributes(List.of(venueApModelSpecificAttributes));
    repositoryUtil.createOrUpdate(venue, tenantId, randomTxId());

    var json = """
        {
          "ledEnabled": true,
          "useVenueSettings": false
        }
        """;

    var request = objectMapper.readValue(json, com.ruckus.cloud.wifi.eda.viewmodel.ApLed.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgAction.UPDATE_AP_LED,
        userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()),
        request);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getLedStatusEnabled)
        .isNotEmpty();

    var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));

    // case 2, verify AP model specific DDCCM operation will be triggered
    String requestId2 = randomTxId();
    var json2 = """
        {
          "ledEnabled": false,
          "useVenueSettings": false
        }
        """;

    var request2 = objectMapper.readValue(json2, com.ruckus.cloud.wifi.eda.viewmodel.ApLed.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId2,
        CfgAction.UPDATE_AP_LED,
        userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()),
        request2);

    var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
    assertThat(record2).isNotNull();
    WifiConfigRequest ddccmRequest2 = record2.getPayload();

    assertThat(ddccmRequest2.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getLedStatusEnabled)
        .isNotEmpty();

    var activityImpactedMessage2 = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId2);
    assertThat(activityImpactedMessage2).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId2))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage2.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));

    // case 3
    var requestId3 = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId3,
        CfgAction.RESET_AP_LED,
        userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()),
        "");

    var record3 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId3);
    assertThat(record3).isNotNull();
    WifiConfigRequest ddccmRequest3 = record3.getPayload();

    assertThat(ddccmRequest3.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .noneMatch(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::hasLedStatusEnabled);

    var activityImpactedMessage3 = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId3);
    assertThat(activityImpactedMessage3).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId3))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage3.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_SWITCHABLE_RF_TOGGLE)
  void TestApBandModeImpactedAPs(@ApModel("R670") Ap ap ) throws JsonProcessingException {

    String userName = randomName();
    String apId = ap.getId();
    String requestId = randomTxId();
    String tenantId = ap.getTenant().getId();

    var json = """
        {
          "bandMode": "TRIPLE",
          "useVenueSettings": false
        }
        """;

    var request = objectMapper.readValue(json, com.ruckus.cloud.wifi.eda.viewmodel.ApBandModeSettings.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgAction.UPDATE_AP_BAND_MODE,
        userName,
        new RequestParams()
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("venueId", ap.getApGroup().getVenue().getId()),
        request);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getBandCombinationMode)
        .isNotEmpty();

    var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));

    // case UPDATE_AP_BAND_MODE
    String requestId2 = randomTxId();
    var json2 = """
        {
          "bandMode": "TRIPLE",
          "useVenueSettings": false
        }
        """;

    var request2 = objectMapper.readValue(json2, com.ruckus.cloud.wifi.eda.viewmodel.ApBandModeSettings.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId2,
        CfgAction.UPDATE_AP_BAND_MODE,
        userName,
        new RequestParams()
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("venueId", ap.getApGroup().getVenue().getId()),
        request2);

    var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
    assertThat(record2).isNotNull();
    WifiConfigRequest ddccmRequest2 = record2.getPayload();

    assertThat(ddccmRequest2.getOperationsList()).isNotNull()
        .isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp).hasSize(1).singleElement()
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getBandCombinationMode)
        .extracting(StringValue::getValue)
        .isEqualTo(DdccmConstants.BAND_MODE_TRIPLE);

    var activityImpactedMessage2 = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId2);
    assertThat(activityImpactedMessage2).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId2))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage2.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));

    // case: RESET_AP_BAND_MODE
    var requestId3 = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId3,
        CfgAction.RESET_AP_BAND_MODE,
        userName,
        new RequestParams()
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("venueId", ap.getApGroup().getVenue().getId()),
        "");

    var record3 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId3);
    assertThat(record3).isNotNull();
    WifiConfigRequest ddccmRequest3 = record3.getPayload();

    assertThat(ddccmRequest3.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .noneMatch(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::hasBandCombinationMode);

    var activityImpactedMessage3 = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId3);
    assertThat(activityImpactedMessage3).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId3))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage3.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE)
  void TestApAntennaTypeImpactedAPs(@ApModel("T670SN") Ap ap )
      throws JsonProcessingException {

    String userName = randomName();
    String apId = ap.getId();
    String requestId = randomTxId();
    String tenantId = ap.getTenant().getId();

    var json = """
        {
          "antennaType": "NARROW",
          "useVenueSettings": false
        }
        """;

    var request = objectMapper.readValue(json, com.ruckus.cloud.wifi.eda.viewmodel.ApAntennaTypeSettings.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgAction.UPDATE_AP_ANTENNA_TYPE,
        userName,
        new RequestParams()
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("venueId", ap.getApGroup().getVenue().getId()),
        request);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getAntennaType)
        .extracting(AntennaTypeEnum::name)
        .containsExactly(String.valueOf(AntennaTypeEnum.Narrow));

    var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));

    // case UPDATE_AP_ANTENNA_TYPE
    String requestId2 = randomTxId();
    var json2 = """
        {
          "antennaType": "SECTOR",
          "useVenueSettings": false
        }
        """;

    var request2 = objectMapper.readValue(json2, com.ruckus.cloud.wifi.eda.viewmodel.ApAntennaTypeSettings.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId2,
        CfgAction.UPDATE_AP_ANTENNA_TYPE,
        userName,
        new RequestParams()
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("venueId", ap.getApGroup().getVenue().getId()),
        request2);

    var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
    assertThat(record2).isNotNull();
    WifiConfigRequest ddccmRequest2 = record2.getPayload();

    assertThat(ddccmRequest2.getOperationsList()).isNotNull()
        .isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp).hasSize(1).singleElement()
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getAntennaType)
        .isEqualTo(AntennaTypeEnum.Sector);

    var activityImpactedMessage2 = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId2);
    assertThat(activityImpactedMessage2).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId2))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage2.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));

    // case: RESET_AP_ANTENNA_TYPE
    var requestId3 = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId3,
        CfgAction.RESET_AP_ANTENNA_TYPE,
        userName,
        new RequestParams()
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("venueId", ap.getApGroup().getVenue().getId()),
        "");

    var record3 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId3);
    assertThat(record3).isNotNull();
    WifiConfigRequest ddccmRequest3 = record3.getPayload();

    assertThat(ddccmRequest3.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getAntennaType)
        .containsExactly(AntennaTypeEnum.AntennaTypeEnum_UNSET);

    var activityImpactedMessage3 = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId3);
    assertThat(activityImpactedMessage3).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId3))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage3.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE)
  void TestApAntennaTypeV1_1ImpactedAPs(@ApModel("T670SN") Ap ap )
      throws JsonProcessingException {

    String userName = randomName();
    String apId = ap.getId();
    String requestId = randomTxId();
    String tenantId = ap.getTenant().getId();

    var json = """
        {
          "antennaType": "NARROW",
          "useVenueSettings": false
        }
        """;

    var request = objectMapper.readValue(json, com.ruckus.cloud.wifi.eda.viewmodel.ApAntennaTypeSettings.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgAction.UPDATE_AP_ANTENNA_TYPE_V1_1,
        userName,
        new RequestParams()
            .addPathVariable("serialNumber", ap.getId())
            .addPathVariable("venueId", ap.getApGroup().getVenue().getId()),
        request);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getAntennaType)
        .extracting(AntennaTypeEnum::name)
        .containsExactly(String.valueOf(AntennaTypeEnum.Narrow));

    var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 1)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList().first()
            .matches(id -> id.equals(apId)));
  }

  @Test
  void TestApLanPortsAndApLedImpactedAPs(@ApModel("T750SE") Ap ap, Venue venue )
      throws JsonProcessingException, InvalidProtocolBufferException {

    // case 1
    String userName = randomName();
    String apId = ap.getId();
    String requestId = randomTxId();
    String tenantId = ap.getTenant().getId();

    final var apGroups = Generators.apGroup()
        .setVenue(always(venue)).generate(2);

    apGroups.forEach(apGroup -> {
      Ap apIn = new Ap(randomSerialNumber());
      apIn.setApGroup(apGroup);
      apIn.setName(randomName());
      repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(apIn, tenantId, randomTxId());
    });

    // Add venueApModelSpecificAttributes to test get default attributes from venue that do not impact venue level
    final var venueApModelSpecificAttributes = VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(
        venue, v -> {
          v.setLedOn(Boolean.FALSE);
          v.setModel(ap.getModel());
        });
    repositoryUtil.createOrUpdate(venueApModelSpecificAttributes, tenantId, randomTxId());
    venue.setModelSpecificAttributes(List.of(venueApModelSpecificAttributes));
    repositoryUtil.createOrUpdate(venue, tenantId, randomTxId());

    // AP Customization
    var json = """
        {
          "externalAntenna": {
            "enable24G": true,
            "enable50G": true,
            "gain24G": 55,
            "gain50G": 45,
            "supportDisable": true,
            "coupled": true
          },
          "poeOut": false,
          "lanPorts": [
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "1",
              "enabled": true
            },
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "2",
              "enabled": true
            },
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "3",
              "enabled": true
            }
          ],
          "useVenueSettings": false,
          "id": null
        }
        """;

    var request = objectMapper.readValue(json, com.ruckus.cloud.wifi.eda.viewmodel.ApModelLanPort.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgAction.UPDATE_AP_LAN_PORTS,
        userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()),
        request);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();

    // case 2
    String requestId2 = randomTxId();
    var json2 = """
        {
          "ledEnabled": true,
          "useVenueSettings": false
        }
        """;

    var request2 = objectMapper.readValue(json2, com.ruckus.cloud.wifi.eda.viewmodel.ApLed.class);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId2,
        CfgAction.UPDATE_AP_LED,
        userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()),
        request2);

    var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
    assertThat(record2).isNotNull();
    WifiConfigRequest ddccmRequest2 = record2.getPayload();

    assertThat(ddccmRequest2.getOperationsList()).isNotNull()
        .asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAp)
        .extracting(Operation::getAp)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
        .flatExtracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getLanPortList)
        .hasSize(3);
  }

  @Test
  void TestApCustomizationWithVenueExternalAntennaModified(@ApModel("T750SE") Ap ap, Venue venue)
      throws JsonProcessingException, InvalidProtocolBufferException {

    // Venue external antenna modification
    var json = """
        [
          {
            "enable24G": false,
            "enable50G": false,
            "model": "T750SE"
          }
        ]
        """;
    var request = objectMapper.readValue(json, new TypeReference<List<ApModelExternalAntenna>>() {
    });

    final var userName = "testUserName-Alice";
    final var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(),
        requestId,
        CfgAction.UPDATE_VENUE_EXTERNAL_ANTENNA,
        userName,
        new RequestParams().addPathVariable("venueId", venue.getId()),
        request);

    final var record =  messageCaptors.getDdccmMessageCaptor().getValue(venue.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest =record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(req -> req.stream().anyMatch(Operation::hasVenue));

    // AP Customization
    var json2 = """
        {
          "externalAntenna": {
            "enable24G": true,
            "enable50G": true,
            "gain24G": 55,
            "gain50G": 45,
            "supportDisable": true,
            "coupled": true
          },
          "poeOut": false,
          "lanPorts": [
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "1",
              "enabled": true
            },
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "2",
              "enabled": true
            },
            {
              "type": "TRUNK",
              "untagId": 1,
              "vlanMembers": "1-4094",
              "portId": "3",
              "enabled": true
            }
          ],
          "useVenueSettings": false,
          "id": null
        }
        """;

    var request2 = objectMapper.readValue(json2, com.ruckus.cloud.wifi.eda.viewmodel.ApModelSpecific.class);

    var requestId2 = randomTxId();
    messageUtil.sendWifiCfgRequest(
        ap.getTenant().getId(),
        requestId2,
        CfgAction.UPDATE_AP_CUSTOMIZATION,
        userName,
        new RequestParams().addPathVariable("serialNumber", ap.getId()),
        request2);

    var record02 = messageCaptors.getDdccmMessageCaptor().getValue(ap.getTenant().getId(), requestId2);
    assertThat(record02).isNotNull();
    WifiConfigRequest ddccmRequest02 = record02.getPayload();
    assertThat(ddccmRequest02.getOperationsList()).isNotNull()
        .matches(req -> req.stream().anyMatch(Operation::hasAp));

    // Update Venue PoEMode for triggering AP update PoEMode
    var json3 = """
        [
          {
            "lanPorts": [
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "1",
                "enabled": true
              },
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "2",
                "enabled": true
              },
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "3",
                "enabled": true
              }
            ],
            "model": "T750SE",
            "poeOut": null,
            "poeMode": "Auto"
          }
        ]
        """;

    var request3 = objectMapper.readValue(json3, new TypeReference<List<ApModelLanPort>>() {});
    var requestId3 = randomTxId();
    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(),
        requestId3,
        CfgAction.UPDATE_VENUE_LAN_PORTS,
        userName,
        new RequestParams().addPathVariable("venueId", venue.getId()),
        request3);

    var record03 = messageCaptors.getDdccmMessageCaptor().getValue(venue.getTenant().getId(), requestId3);
    assertThat(record03).isNotNull();
    WifiConfigRequest ddccmRequest03 = record03.getPayload();
    assertThat(ddccmRequest03.getOperationsList()).isNotNull()
        .matches(req -> req.stream().anyMatch(Operation::hasAp));  //has AP
  }

  @Test
  void TestUpdateVenueExternalAntennaModified(Venue venue)
      throws JsonProcessingException, InvalidProtocolBufferException {

    final var userName = "testUserName-Alice";

    // Update Venue PoEMode Not triggering AP update PoEMode if no apSpecific
    var json3 = """
        [
          {
            "lanPorts": [
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "1",
                "enabled": true
              },
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "2",
                "enabled": true
              },
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "3",
                "enabled": true
              }
            ],
            "model": "T750SE",
            "poeOut": null,
            "poeMode": "Auto"
          }
        ]
        """;
    var request3 = objectMapper.readValue(json3, new TypeReference<List<ApModelLanPort>>() {});
    final var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(),
        requestId,
        CfgAction.UPDATE_VENUE_LAN_PORTS,
        userName,
        new RequestParams().addPathVariable("venueId", venue.getId()),
        request3);

    var record03 = messageCaptors.getDdccmMessageCaptor().getValue(venue.getTenant().getId(), requestId);
    assertThat(record03).isNotNull();
    WifiConfigRequest ddccmRequest03 =record03.getPayload();

    assertThat(ddccmRequest03.getOperationsList()).isNotNull()
        .matches(req -> req.stream().noneMatch(Operation::hasAp));  //No AP
  }

}
