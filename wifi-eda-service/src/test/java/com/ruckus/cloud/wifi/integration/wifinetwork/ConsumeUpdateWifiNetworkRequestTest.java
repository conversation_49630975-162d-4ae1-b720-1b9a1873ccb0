package com.ruckus.cloud.wifi.integration.wifinetwork;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.templateString;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_DPSK3_NON_PROXY_MODE_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE;
import static com.ruckus.cloud.wifi.integration.ConsumeWifiNetworkRadiusServerProfileSettingsRequestTest.WIFI_NETWORK_ID;
import static com.ruckus.cloud.wifi.integration.radius.ConsumeActivateRadiusServerProfileRequestTest.RADIUS_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.HOST_APPROVAL_EMAIL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.protobuf.BoolValue;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanSecurity;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractWlan;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiNetworkRadiusServerProfileSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DpskWifiNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.PskWlanSecurityEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.repository.DpskNetworkRepository;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenuePortalTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.kafka.common.header.Header;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeUpdateWifiNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private DpskNetworkRepository dpskNetworkRepository;

  @Nested
  class whenConsumeUpdateWifiNetworkRequest {

    private final static String networkName = "networkName_update";
    private final static String ssid = "ssid_update";

    private String wifiNetworkId;
    private Short vlanId;

    @Payload("HostApproval")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadClickthrough() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
            n.setName("networkName_update");
            n.getWlan().setSsid("ssid_update");
          });
      return networkRequest;
    }

    @Payload("HostApprovalWithEmail")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadHostApprovalWithEmail() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
            n.setName("networkName_update");
            n.getWlan().setSsid("ssid_update");
            n.getGuestPortal().getHostGuestConfig().setHostEmails(List.of(HOST_APPROVAL_EMAIL));
          });
      return networkRequest;
    }

    @BeforeEach
    void givenOneGuestWifiNetworkPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant, final Venue venue) {
      final GuestNetwork guestNetwork = (GuestNetwork) network(GuestNetwork.class).generate();
      final GuestPortal guestPortal = guestPortal(GuestNetworkTypeEnum.HostApproval).generate();
      guestNetwork.setPortalServiceProfileId(randomId());
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
          .setNetwork(always(guestNetwork)).setVenue(always(venue)).generate();
      guestNetwork.setNetworkVenues(List.of(networkVenue));
      final var venuePortal = VenuePortalTestFixture.randomVenuePortal(tenant, vportal -> {
        vportal.setNetworkVenue(networkVenue);
        vportal.setNetworkPortal(guestPortal);
      });
      networkVenue.setVenuePortal(venuePortal);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      wifiNetworkId = guestNetwork.getId();
      vlanId = guestNetwork.getWlan().getVlanId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("HostApproval"))
    void thenUpdatedGuestWifiNetworkClickThroughWithPortalServiceProfile(TxCtx txCtx)
        throws InvalidProtocolBufferException {
      validateWifiCfgChangeMessage(txCtx);
      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);
      validGuestWifiNetwork();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK, payload = @Payload("HostApprovalWithEmail"))
    void thenUpdatedGuestWifiNetworkHostApprovalWithEmail(TxCtx txCtx)
        throws InvalidProtocolBufferException {
      validateWifiCfgChangeMessage(txCtx);
      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);
      validGuestWifiNetwork();
      validHostGuestConfig();
    }

    private void validateWifiCfgChangeMessage(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
              .getValue(txCtx);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();

      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestNetwork()).findFirst().get()
                  .getGuestNetwork())
          .matches(n -> n.getId().equals(StringValue.of(wifiNetworkId)));

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> Objects.nonNull(gp.getId()));
    }

    private void validateCmnCfgCollectorMessage(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(txCtx);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.MOD)
          .matches(p -> p.getId().equals(wifiNetworkId));
    }

    private void validateActivityImpactedMessage(TxCtx txCtx) {
      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
              .getValue(txCtx);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());

      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
    }

    private void validateActivityMessages(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
              .getValue(txCtx);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.UPDATE_WIFI_NETWORK))
          .extracting(ConfigurationStatus::getEventDate)
          .isNotNull();
    }

    private void validGuestWifiNetwork() {
      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, wifiNetworkId);
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(wifiNetworkId))
          .matches(n -> n.getName().equals(networkName))
          .matches(n -> n.getType().equals(NetworkTypeEnum.GUEST))
          .extracting(GuestNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(ssid))
          .matches(wlan -> wlan.getVlanId().equals(vlanId));
    }

    private void validHostGuestConfig() {
      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, wifiNetworkId);
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(wifiNetworkId))
          .matches(n -> n.getIsTemplate().equals(Boolean.FALSE))
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .extracting(GuestPortal::getHostGuestConfig)
          .isNotNull()
          .matches(hostGuestConfig -> hostGuestConfig.getHostEmails().equals(List.of(HOST_APPROVAL_EMAIL)));
    }
  }

  @Nested
  class ConsumeUpdateWifiNetworkRequestOnAAANetworkWithRadius {

    private String profileId;
    private String networkId;
    private String RADIUS_ID = "radiusId";
    private String WIFI_NETWORK_ID = "wifiNetworkId";

    @BeforeEach
    void beforeEach(Radius radius, AAANetwork network) {
      profileId = radius.getId();
      networkId = network.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Test
    void updateAAAWifiNetworkWithoutRadius(Tenant tenant, AAANetwork network) {
      var tenantId = tenant.getId();
      //activate Radius on network
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
          randomName(),
          requestParams(),
          "");

      //update enableAuthProxy = true
      var settings = new WifiNetworkRadiusServerProfileSettings();
      settings.setEnableAuthProxy(true);
      messageUtil.sendWifiCfgRequest(
          tenantId,
          randomTxId(),
          CfgAction.UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS,
          randomName(),
          new RequestParams().addPathVariable(WIFI_NETWORK_ID, networkId),
          settings);

      //update AAA network without Radius settings
      var aaaWifiNetwork = Generators.aaaWifiNetwork().generate();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          randomTxId(),
          CfgAction.UPDATE_WIFI_NETWORK,
          randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", networkId),
          aaaWifiNetwork);
      final var savedNetwork = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork.class, network.getId());
      //ensure Radius settings are not overridden and ignored during the merge process
      assertTrue(savedNetwork.getEnableAuthProxy());
      assertEquals(savedNetwork.getAuthRadius().getId(), profileId);
    }
  }

  @Test
  void updateOpenWifiNetwork_whenTurnOnMulticastFilter(Tenant tenant, Venue venue) {
    var network = NetworkTestFixture.randomOpenNetwork(tenant, (v)->{});
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    var networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    var openWifiNetwork = Generators.openWifiNetwork().generate();
    openWifiNetwork.getWlan().getAdvancedCustomization().setMulticastFilterEnabled(true);
    openWifiNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_WIFI_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("wifiNetworkId", network.getId()),
        openWifiNetwork);

    final var savedNetwork = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork::getWlan)
        .isNotNull()
        .extracting(wlan -> wlan.getAdvancedCustomization().getMulticastFilterEnabled())
        .isEqualTo(true);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
            WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue).first().extracting(operation ->
            operation.getWlanVenue().getMulticastFilterDrop())
        .isEqualTo(true);
  }

  @Test
  void updateOpenWifiNetworkWithQosMapSet(Tenant tenant, Venue venue) {
    var network = NetworkTestFixture.randomOpenNetwork(tenant, (v)->{});
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

    var networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    var openWifiNetwork = Generators.openWifiNetwork().generate();
    openWifiNetwork.getWlan().getAdvancedCustomization().setQosMapSetEnabled(true);
    openWifiNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_WIFI_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("wifiNetworkId", network.getId()),
        openWifiNetwork);

    final var savedNetwork = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork::getWlan)
        .isNotNull()
        .matches((wlan -> wlan.getAdvancedCustomization().getQosMapSetEnabled()))
        .matches(wlan -> wlan.getAdvancedCustomization().getQosMapSetOptions().getRules().size() == 8);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload())
        .extracting(WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue)
        .first()
        .extracting(o -> o.getWlanVenue().getAdvancedCustomization().getQosMap())
        .matches(qosMap -> qosMap.getEnabled().equals(BoolValue.of(true)))
        .matches(qosMap -> qosMap.getDscpValuesCount() == 8);

    final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenant.getId(), requestId);
    WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
    assertThat(wifiConfigChange.getOperationList())
        .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasQosMapRule)
        .hasSize(8);
    assertThat(wifiConfigChange.getOperationList())
        .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasWlan)
        .first()
        .matches(o -> o.getWlan().getAdvancedCustomization().getQosMapSetEnabled().equals(BoolValue.of(true)));
  }

  @Test
  void updateOweWifiNetworkWithoutNetworkIdInRequestPayload(Tenant tenant) {
    var networkRequest = Generators.openWifiNetwork()
        .setId(templateString(randomId()))
        .setName(serialName("oweTransitionRequest"))
        .setDescription(randomString(64))
        .setWlan(Generators.oweTransitionWifiWlan()).generate();

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.ADD_WIFI_NETWORK,
        randomName(),
        new RequestParams(),
        networkRequest);

    OpenNetwork openOweMasterNetwork = repositoryUtil.find(OpenNetwork.class,
        networkRequest.getId());
    OpenNetwork openOweSlaveNetwork = repositoryUtil.find(OpenNetwork.class,
        openOweMasterNetwork.getOwePairNetworkId());

    // update owe network name without network id
    networkRequest.setName("updateOweNetworkName");
    networkRequest.setId(null);

    var requestId2 = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId2,
        CfgAction.UPDATE_WIFI_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("wifiNetworkId", openOweMasterNetwork.getId()),
        networkRequest);

    final var savedNetwork = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, openOweSlaveNetwork.getId());
    assertNotNull(savedNetwork.getOwePairNetworkId());
  }

  @Nested
  class ConsumeUpdateDpskNetworkRequestTest {
    String networkId;
    String radiusId;
    String tenantId;

    @Payload("updateDpsk3WifiNetwork")
    private DpskWifiNetworkGenerator updateDpsk3WifiNetwork() {
      return Generators.dpskWifiNetwork()
          .setId(templateString(networkId))
          .setName(serialName("UpdateDpsk3Network"))
          .setDescription(randomString(64))
          .setWlan(Generators.dpskWpa23MixedModeWifiWlan());
    }

    @Payload("addDpskWifiNetwork")
    private DpskWifiNetworkGenerator addDpskWifiNetwork() {
      return Generators.dpskWifiNetwork()
          .setName(serialName("AddDpskWifiNetwork"))
          .setDescription(randomString(64))
          .setUseDpskService(always(false))
          .setWlan(Generators.dpskWpa2WifiWlan());
    }

    @BeforeEach
    void givenDpskNetworkPersistedInDb(Radius radius, Tenant tenant) {
      tenantId = tenant.getId();
      radiusId = radius.getId();
      var networkRequest = addDpskWifiNetwork().generate();
      networkRequest.setId(randomId());
      var wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
          .apiAction(CfgAction.ADD_WIFI_NETWORK)
          .payload(networkRequest).build();
      messageUtil.sendWifiCfgRequest(wifiCfgRequest);
      var dpskNetwork = (DpskNetwork) dpskNetworkRepository.findByIdAndTenantId(
          networkRequest.getId(), tenantId).get();
      networkId = dpskNetwork.getId();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          randomTxId(),
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
          randomName(),
          requestParams2(),
          "");
    }

    @ApiAction.RequestParams
    private RequestParams requestParams1() {
      return new RequestParams().addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @ApiAction.RequestParams
    private RequestParams requestParams2() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, radiusId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Test
    @FeatureFlag(enable = WIFI_EDA_WPA3_DSAE_TOGGLE)
    void thenUpdateWpa2DpskNetworkWithEnableAuthProxyToDpsk3NetworkWithoutFFFail() {
      var networkRequest = updateDpsk3WifiNetwork().generate();
      networkRequest.setId(networkId);
      var wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
          .apiAction(CfgAction.UPDATE_WIFI_NETWORK)
          .requestParams(requestParams1())
          .payload(networkRequest).build();
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequest)).isNotNull()
          .getRootCause().isInstanceOf(InvalidPropertyValueException.class).hasMessage(
              "Cannot switch DPSK network [" + networkId + "] with RADIUS profile settings to DPSK3 network.");
    }

    @Test
    @FeatureFlag(enable = {WIFI_EDA_WPA3_DSAE_TOGGLE, WIFI_DPSK3_NON_PROXY_MODE_TOGGLE})
    void thenUpdateWpa2DpskNetworkWithoutRadiusProfileToDpsk3NetworkSuccess() {
      messageUtil.sendWifiCfgRequest(
          tenantId,
          randomTxId(),
          CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
          randomName(),
          requestParams2(),
          "");
      var networkRequest = updateDpsk3WifiNetwork().generate();
      networkRequest.setId(networkId);
      var wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
          .apiAction(CfgAction.UPDATE_WIFI_NETWORK)
          .requestParams(requestParams1())
          .payload(networkRequest).build();
      messageUtil.sendWifiCfgRequest(wifiCfgRequest);

      final var savedNetwork = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork.class, networkId);
      assertThat(savedNetwork)
          .isNotNull()
          .matches(n -> n.getId().equals(savedNetwork.getId()))
          .matches(n -> n.getName().equals(savedNetwork.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.DPSK))
          .matches(n -> n.getEnableAuthProxy().equals(false))
          .matches(n -> n.getEnableAccountingProxy().equals(false))
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork::getWlan)
          .isNotNull()
          .extracting(AbstractWlan::getWlanSecurity)
          .isEqualTo(WlanSecurityEnum.WPA23Mixed);
    }

    @Test
    @FeatureFlag(enable = {WIFI_EDA_WPA3_DSAE_TOGGLE, WIFI_DPSK3_NON_PROXY_MODE_TOGGLE})
    void thenUpdateWpa2DpskNetworkWithRadiusProfileRadSecEnabledToDpsk3NetworkFail() {
      var networkRequest = updateDpsk3WifiNetwork().generate();
      networkRequest.setId(networkId);

      //enable radsec
      var radius = repositoryUtil.find(Radius.class, radiusId);
      radius.getRadSecOptions().setTlsEnabled(true);
      repositoryUtil.createOrUpdate(radius, tenantId, randomTxId());

      var wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
          .apiAction(CfgAction.UPDATE_WIFI_NETWORK)
          .requestParams(requestParams1())
          .payload(networkRequest).build();

      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequest)).isNotNull()
          .getRootCause().isInstanceOf(InvalidPropertyValueException.class).hasMessage(
              "Cannot update DPSK network [" + networkId
                  + "] to DPSK3 because Auth RADIUS has RadSec enabled. Please disable RadSec"
                  + " on the Auth RADIUS server profile.");
    }
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_WLAN_DEPRECATE_WEP)
  void updatePskWifiNetwork_withWep_success(Tenant tenant, Venue venue) {
    var network = NetworkTestFixture.randomPskNetwork(tenant, n -> {
      n.getWlan().setWlanSecurity(WlanSecurityEnum.WEP);
    });
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    var networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    var pskWifiNetwork = Generators.pskWifiNetwork().generate();
    pskWifiNetwork.getWlan().setWlanSecurity(PskWlanSecurityEnum.WEP);
    pskWifiNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_WIFI_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("wifiNetworkId", network.getId()),
        pskWifiNetwork);

    final var savedNetwork = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.PSK))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork::getWlan)
        .isNotNull()
        .extracting(AbstractWlan::getWlanSecurity)
        .isEqualTo(WlanSecurityEnum.WEP);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
            WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue).first().extracting(operation ->
            operation.getWlanVenue().getWlanSecurity())
        .isEqualTo(WlanSecurity.WEP);
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_WLAN_DEPRECATE_WEP)
  void updatePskWifiNetwork_withWep_fail(Tenant tenant, Venue venue) {
    var network = NetworkTestFixture.randomPskNetwork(tenant, n -> {
      n.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
    });
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    var networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    var pskWifiNetwork = Generators.pskWifiNetwork().generate();
    pskWifiNetwork.getWlan().setWlanSecurity(PskWlanSecurityEnum.WEP);
    pskWifiNetwork.setId(network.getId());

    var requestId = randomTxId();
    try {
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgAction.UPDATE_WIFI_NETWORK,
          randomName(),
          new RequestParams().addPathVariable("wifiNetworkId", network.getId()),
          pskWifiNetwork);
    } catch (Exception ignored) {
    }

    var status = Optional.ofNullable(messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenant.getId(), Duration.ofSeconds(1L)))
        .map(KafkaProtoMessage::getPayload)
        .orElseThrow(() -> new RuntimeException("No ActivityStatus"));

    assertAll("assert activity status fail",
        () -> assertEquals(Status.FAIL, status.getStatus()),
        () -> assertEquals(ApiFlowNames.UPDATE_WIFI_NETWORK, status.getStep())
    );
    JSONObject error = new JSONObject(status.getError());

    assertAll("assert activity status fail error",
        () -> assertEquals(Errors.WIFI_10552.code(), error.get("code")),
        () -> assertEquals(Errors.WIFI_10552.message(), error.get("message"))
    );

    messageCaptors.assertThat(kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest()).doesNotSendByTenant(tenant.getId());
  }

  @Test
  void updatePskWifiNetwork_withVlanId(Tenant tenant, Venue venue) {
    var network = NetworkTestFixture.randomPskNetwork(tenant, n -> {
      n.getWlan().setVlanId((short) 6);
    });
    network = repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
    var networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    var pskWifiNetwork = Generators.pskWifiNetwork().generate();
    pskWifiNetwork.getWlan().setVlanId((short) 100);
    pskWifiNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_WIFI_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("wifiNetworkId", network.getId()),
        pskWifiNetwork);

    final var savedNetwork = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.PSK))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork::getWlan)
        .isNotNull()
        .extracting(AbstractWlan::getVlanId)
        .isEqualTo((short) 100);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
            WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue).first().extracting(operation ->
            operation.getWlanVenue().getVlanId().getValue())
        .isEqualTo(100);
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(cmnCfgCollectorMessage.getPayload())
        .extracting(p -> p.getOperationsList().stream().filter(o ->
            EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
        .satisfies(operation -> {
          assertThat(operation.getOpType()).isEqualTo(OpType.MOD);
          assertThat(operation.getDocMap().get(Key.VLAN).getNumberValue()).isEqualTo(100.0);
        });
  }
}
