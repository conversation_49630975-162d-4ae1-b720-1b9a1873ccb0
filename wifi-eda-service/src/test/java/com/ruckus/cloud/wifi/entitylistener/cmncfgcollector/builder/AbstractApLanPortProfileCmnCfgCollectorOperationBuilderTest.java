package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.AbstractApLanPortProfileCmnCfgCollectorOperationBuilder.getViewModelType;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Operations.Builder;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.hibernate.dto.ApActivationDto;
import com.ruckus.cloud.wifi.hibernate.dto.VenueActivationDto;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;

@Tag("EthernetPortProfileTest")
@WifiUnitTest
class AbstractApLanPortProfileCmnCfgCollectorOperationBuilderTest {

  @SpyBean
  private AbstractApLanPortProfileCmnCfgCollectorOperationBuilder<ApLanPortProfile> unit;
  @MockBean
  private VenueLanPortRepository venueLanPortRepository;
  @MockBean
  private ApLanPortRepository apLanPortRepository;

  @Test
  void testGetEntityClass() {
    assertThat(unit.entityClass()).isEqualTo(ApLanPortProfile.class);
  }

  @Test
  void testGetIndex() {
    assertThat(unit.index()).isEqualTo(Index.ETHERNET_PORT_PROFILE_INDEX_NAME);
  }

  @Nested
  class ConfigTest {

    private final List<VenueLanPort> givenVenueLanPorts = Generators.venueLanPort()
        .setVenueApModelSpecificAttributes(
            Generators.venueApModelSpecificAttributes().setVenue(Generators.venue())).generate(5);
    private final List<VenueActivationDto> givenVenueActivations = givenVenueLanPorts.stream()
        .map(venueLanPort -> new VenueActivationDto(venueLanPort.getVenueApModelSpecificAttributes().getVenue().getId(),
            venueLanPort.getVenueApModelSpecificAttributes().getModel(), venueLanPort.getPortId()))
        .collect(Collectors.toList()) ;
    private final List<ApLanPort> givenApLanPorts = Generators.apLanPort().generate(5);
    private final List<ApActivationDto> givenApActivations = givenApLanPorts.stream()
        .map(apLanPort -> new ApActivationDto(apLanPort.getModelSpecific().getAp().getApGroup().getVenue().getId(),
            apLanPort.getModelSpecific().getAp().getId(), apLanPort.getPortId()))
        .collect(Collectors.toList());

    @BeforeEach
    void mockRepositories() {
       doReturn(givenVenueActivations).when(venueLanPortRepository)
           .findVenueActivations(anyString(), anyString());
      doReturn(givenApActivations).when(apLanPortRepository)
          .findApActivations(anyString(), anyString());
    }

    @Nested
    class GivenAddEntityAction extends AbstractBaseTest {
      @Override
      protected EntityAction givenAction() {
        return EntityAction.ADD;
      }
    }

    @Nested
    class GivenModifyEntityAction extends AbstractBaseTest {
      @Override
      protected EntityAction givenAction() {
        return EntityAction.MODIFY;
      }
    }

    private abstract class AbstractBaseTest {

      protected abstract EntityAction givenAction();

      @Test
      void givenPinLanPortProfileSunnyDay() {
        whenConfigThenAssertResult(givenAction(), generateApLanPortProfile());
      }
    }

    private static ApLanPortProfile generateApLanPortProfile() {
      return Generators.apLanPortProfile(ApLanPortProfile.class)
          .setTenant(Generators.tenant()).generate();
    }

    private void whenConfigThenAssertResult(EntityAction action, ApLanPortProfile given) {
      final Builder builderResult = Operations.newBuilder();
      unit.config(builderResult, given, action);
      assertResult(given, builderResult);
    }

    private void assertResult(ApLanPortProfile given, Builder builderResult) {
      assertThat(builderResult.build().getDocMap())
          .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(given.getTenant().getId()))
          .containsEntry(Key.ID, ValueUtils.stringValue(given.getId()))
          .containsEntry(Key.TYPE, ValueUtils.stringValue(getViewModelType(given.getType())))
          .containsEntry(Key.UNTAG_ID, ValueUtils.numberValue(given.getUntagId()))
          .containsEntry(Key.VLAN_MEMBERS, ValueUtils.stringValue(given.getVlanMembers()))
          .hasEntrySatisfying(Key.VENUE_IDS, assertVenueIdsValue(givenVenueLanPorts))
          .hasEntrySatisfying(Key.AP_SERIAL_NUMBERS, assertApSerialNumbersValue(givenApLanPorts))
          .hasEntrySatisfying(Key.VENUE_ACTIVATIONS, assertVenueActivationsValue(givenVenueLanPorts))
          .hasEntrySatisfying(Key.AP_ACTIVATIONS, assertApActivationsValue(givenApLanPorts));
    }

    private static Consumer<Value> assertVenueIdsValue(Collection<VenueLanPort> givenVenueLanPorts) {
      return actualVenueIdsValue -> {
        assertThat(actualVenueIdsValue.getKindCase()).isEqualTo(KindCase.LIST_VALUE);
        assertThat(actualVenueIdsValue.hasListValue()).isTrue();
        assertThat(actualVenueIdsValue.getListValue()).isNotNull()
            .satisfies(listValue -> {
              assertThat(listValue.getValuesCount()).isEqualTo(givenVenueLanPorts.size());
              assertThat(listValue.getValuesList()).hasSize(givenVenueLanPorts.size())
                  .allSatisfy(value -> {
                    assertThat(value.getKindCase()).isEqualTo(KindCase.STRING_VALUE);
                    assertThat(value.getStringValue()).isNotEmpty();
                  })
                  .extracting(Value::getStringValue)
                  .containsExactlyInAnyOrderElementsOf(() -> givenVenueLanPorts.stream()
                      .map(VenueLanPort::getVenueApModelSpecificAttributes)
                      .map(VenueApModelSpecificAttributes::getVenue).map(Venue::getId).iterator());
            });
      };
    }

    private static Consumer<Value> assertApSerialNumbersValue(Collection<ApLanPort> givenApLanPorts) {
      return actualApSerialNumbersValue -> {
        assertThat(actualApSerialNumbersValue.getKindCase()).isEqualTo(KindCase.LIST_VALUE);
        assertThat(actualApSerialNumbersValue.hasListValue()).isTrue();
        assertThat(actualApSerialNumbersValue.getListValue()).isNotNull()
            .satisfies(listValue -> {
              assertThat(listValue.getValuesCount()).isEqualTo(givenApLanPorts.size());
              assertThat(listValue.getValuesList()).hasSize(givenApLanPorts.size())
                  .allSatisfy(value -> {
                    assertThat(value.getKindCase()).isEqualTo(KindCase.STRING_VALUE);
                    assertThat(value.getStringValue()).isNotEmpty();
                  })
                  .extracting(Value::getStringValue)
                  .containsExactlyInAnyOrderElementsOf(() -> givenApLanPorts.stream()
                      .map(ApLanPort::getModelSpecific)
                      .map(ApModelSpecific::getAp).map(Ap::getId).iterator());
            });
      };
    }

    private static Consumer<Value> assertVenueActivationsValue(Collection<VenueLanPort> givenVenueLanPorts) {
      return actualVenueActivationsValue -> {
        assertThat(actualVenueActivationsValue.getKindCase()).isEqualTo(KindCase.LIST_VALUE);
        assertThat(actualVenueActivationsValue.hasListValue()).isTrue();
        assertThat(actualVenueActivationsValue.getListValue()).isNotNull()
            .satisfies(listValue -> {
              assertThat(listValue.getValuesCount()).isEqualTo(givenVenueLanPorts.size());
              assertThat(listValue.getValuesList()).hasSize(givenVenueLanPorts.size())
                  .allSatisfy(value -> {
                    assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                    assertThat(value.hasStructValue()).isTrue();
                    assertThat(value.getStructValue()).isNotNull();
                  })
                  .extracting(Value::getStructValue)
                  .containsExactlyInAnyOrderElementsOf(() -> givenVenueLanPorts.stream()
                      .map(venueLanPort -> Struct.newBuilder()
                          .putFields(Key.VENUE_ID, ValueUtils.stringValue(venueLanPort.getVenueApModelSpecificAttributes().getVenue().getId()))
                          .putFields(Key.AP_MODEL, ValueUtils.stringValue(venueLanPort.getVenueApModelSpecificAttributes().getModel()))
                          .putFields(Key.PORT_ID, ValueUtils.stringValue(venueLanPort.getPortId())).build()).iterator());
            });
      };
    }

    private static Consumer<Value> assertApActivationsValue(Collection<ApLanPort> givenApLanPorts) {
      return actualVenueActivationsValue -> {
        assertThat(actualVenueActivationsValue.getKindCase()).isEqualTo(KindCase.LIST_VALUE);
        assertThat(actualVenueActivationsValue.hasListValue()).isTrue();
        assertThat(actualVenueActivationsValue.getListValue()).isNotNull()
            .satisfies(listValue -> {
              assertThat(listValue.getValuesCount()).isEqualTo(givenApLanPorts.size());
              assertThat(listValue.getValuesList()).hasSize(givenApLanPorts.size())
                  .allSatisfy(value -> {
                    assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                    assertThat(value.hasStructValue()).isTrue();
                    assertThat(value.getStructValue()).isNotNull();
                  })
                  .extracting(Value::getStructValue)
                  .containsExactlyInAnyOrderElementsOf(() -> givenApLanPorts.stream()
                      .map(apLanPort -> Struct.newBuilder()
                          .putFields(Key.VENUE_ID, ValueUtils.stringValue(apLanPort.getModelSpecific().getAp().getApGroup().getVenue().getId()))
                          .putFields(Key.AP_SERIAL_NUMBER, ValueUtils.stringValue(apLanPort.getModelSpecific().getAp().getId()))
                          .putFields(Key.PORT_ID, ValueUtils.stringValue(apLanPort.getPortId())).build()).iterator());
            });
      };
    }
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public AbstractApLanPortProfileCmnCfgCollectorOperationBuilder<ApLanPortProfile> abstractApLanPortProfileCmnCfgCollectorOperationBuilder(
        VenueLanPortRepository venueLanPortRepository, ApLanPortRepository apLanPortRepository) {
      return new AbstractApLanPortProfileCmnCfgCollectorOperationBuilder<>(
          venueLanPortRepository, apLanPortRepository) {

        @Override
        public Class<ApLanPortProfile> entityClass() {
          return ApLanPortProfile.class;
        }
      };
    }
  }
}
