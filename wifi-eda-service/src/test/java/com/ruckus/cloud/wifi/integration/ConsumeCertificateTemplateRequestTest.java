package com.ruckus.cloud.wifi.integration;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.*;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;

@Slf4j
@Tag("CertificateTemplateTest")
@WifiIntegrationTest
public class ConsumeCertificateTemplateRequestTest extends AbstractRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private ExtendedMessageUtil messageUtil;

  public static final String WIFI_NETWORK_ID = "wifiNetworkId";
  public static final String CERTIFICATE_TEMPLATE_ID = "certificateTemplateId";


  @Test
  void testActivateCertificateTemplateOnWifiNetwork(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String certificateTemplateId = randomTxId();

    final var aaaNetwork = (AAANetwork) network(AAANetwork.class).generate();
    aaaNetwork.setUseCertificateTemplate(true);
    aaaNetwork.getWlan().setNetwork(aaaNetwork);
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
    networkId = aaaNetwork.getId();

    RequestParams requestParams =
        new RequestParams()
            .addPathVariable(WIFI_NETWORK_ID, networkId)
            .addPathVariable(CERTIFICATE_TEMPLATE_ID, certificateTemplateId);

    messageUtil.sendWifiCfgRequest(
        tenantId, requestId,
        CfgAction.ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK,
        userName,
        requestParams,
        null);
    assertActivityStatusSuccess(ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK, tenantId);

    final AAANetwork network =
        repositoryUtil.find(AAANetwork.class, networkId);
    assertThat(network)
            .isNotNull()
            .matches(n -> Objects.equals(n.getId(), networkId))
            .matches(n -> Objects.equals(n.getWlan().getCertificateTemplateId(), certificateTemplateId))
            .matches(n -> Objects.equals(n.getWlan().getCertificateTemplateIds(), List.of(certificateTemplateId)));
  }

  @Test
  void testActivateCertificateTemplateOnWifiNetwork_notUsingCertTemplate_fail(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();
    String certificateTemplateId = randomTxId();

    final var aaaNetwork = (AAANetwork) network(AAANetwork.class).generate();
    aaaNetwork.getWlan().setNetwork(aaaNetwork);
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
    networkId = aaaNetwork.getId();

    RequestParams requestParams =
        new RequestParams()
            .addPathVariable(WIFI_NETWORK_ID, networkId)
            .addPathVariable(CERTIFICATE_TEMPLATE_ID, certificateTemplateId);

    assertThatThrownBy(() ->
        messageUtil.sendWifiCfgRequest(
            tenantId, requestId,
            CfgAction.ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK,
            userName,
            requestParams,
            null)).isNotNull()
        .rootCause()
        .isInstanceOf(InvalidPropertyValueException.class);
    assertActivityStatusFail(ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK, tenantId);
  }

  @Nested
  @FeatureFlag(enable = FlagNames.MULTIPLE_CERTIFICATE_TEMPLATES)
  class MultipleCertificateTemplateOn {
    @Test
    void testActivateCertificateTemplateOnWifiNetwork_givenMultipleFFOn_activateFirstTime_success(final Tenant tenant) {
      String networkId;
      String tenantId = tenant.getId();
      String userName = randomName();
      String requestId = randomTxId();
      String certificateTemplateId = randomTxId();

      final var aaaNetwork = (AAANetwork) network(AAANetwork.class).generate();
      aaaNetwork.setUseCertificateTemplate(true);
      aaaNetwork.getWlan().setNetwork(aaaNetwork);
      repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
      networkId = aaaNetwork.getId();

      RequestParams requestParams =
              new RequestParams()
                      .addPathVariable(WIFI_NETWORK_ID, networkId)
                      .addPathVariable(CERTIFICATE_TEMPLATE_ID, certificateTemplateId);

      messageUtil.sendWifiCfgRequest(
              tenantId, requestId,
              CfgAction.ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK,
              userName,
              requestParams,
              null);
      assertActivityStatusSuccess(ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK, tenantId);

      final AAANetwork network =
              repositoryUtil.find(AAANetwork.class, networkId);
      assertThat(network)
              .isNotNull()
              .matches(n -> Objects.equals(n.getId(), networkId))
              .matches(n -> Objects.equals(n.getWlan().getCertificateTemplateId(), certificateTemplateId))
              .matches(n -> Objects.equals(n.getWlan().getCertificateTemplateIds(), List.of(certificateTemplateId)));
    }

    @Test
    void testActivateCertificateTemplateOnWifiNetwork_givenMultipleFFOn_activateAppendTemplate_success(final Tenant tenant) {
      String networkId;
      String tenantId = tenant.getId();
      String userName = randomName();
      String requestId = randomTxId();
      String id1 = randomTxId();
      String id2 = randomTxId();

      final var aaaNetwork = (AAANetwork) network(AAANetwork.class).generate();
      aaaNetwork.setUseCertificateTemplate(true);
      aaaNetwork.getWlan().setNetwork(aaaNetwork);
      aaaNetwork.getWlan().setCertificateTemplateIds(List.of(id1));
      repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
      networkId = aaaNetwork.getId();

      RequestParams requestParams =
              new RequestParams()
                      .addPathVariable(WIFI_NETWORK_ID, networkId)
                      .addPathVariable(CERTIFICATE_TEMPLATE_ID, id2);

      messageUtil.sendWifiCfgRequest(
              tenantId, requestId,
              CfgAction.ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK,
              userName,
              requestParams,
              null);
      assertActivityStatusSuccess(ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK, tenantId);

      final AAANetwork network =
              repositoryUtil.find(AAANetwork.class, networkId);
      assertThat(network)
              .isNotNull()
              .matches(n -> Objects.equals(n.getId(), networkId))
              .matches(n -> Objects.equals(n.getWlan().getCertificateTemplateId(), id2))
              .matches(n -> Objects.equals(n.getWlan().getCertificateTemplateIds(), List.of(id1, id2)));
    }

    @Test
    void testActivateCertificateTemplateOnWifiNetwork_givenMultipleFFOn_overLimit_fail(final Tenant tenant) {
      String networkId;
      String tenantId = tenant.getId();
      String userName = randomName();
      String requestId = randomTxId();
      String id1 = randomTxId();

      final var aaaNetwork = (AAANetwork) network(AAANetwork.class).generate();
      aaaNetwork.setUseCertificateTemplate(true);
      aaaNetwork.getWlan().setCertificateTemplateIds(Stream.generate(CommonTestFixture::randomTxId).limit(5).toList());
      aaaNetwork.getWlan().setNetwork(aaaNetwork);
      repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
      networkId = aaaNetwork.getId();

      RequestParams requestParams =
              new RequestParams()
                      .addPathVariable(WIFI_NETWORK_ID, networkId)
                      .addPathVariable(CERTIFICATE_TEMPLATE_ID, id1);

      assertThatThrownBy(() ->
              messageUtil.sendWifiCfgRequest(
                      tenantId, requestId,
                      CfgAction.ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK,
                      userName,
                      requestParams,
                      null)
      ).isNotNull()
              .rootCause()
              .isInstanceOf(InvalidPropertyValueException.class);

      assertActivityStatusFail(ACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK, tenantId);

      final AAANetwork network = repositoryUtil.find(AAANetwork.class, networkId);
      assertThat(network)
              .isNotNull()
              .matches(n -> Objects.equals(n.getId(), networkId))
              .matches(n -> Objects.equals(n.getWlan().getCertificateTemplateIds(), aaaNetwork.getWlan().getCertificateTemplateIds()));
    }

    @Test
    void testDeactivateCertificateTemplateOnWifiNetwork(final Tenant tenant) {
      String networkId;
      String tenantId = tenant.getId();
      String userName = randomName();
      String requestId = randomTxId();
      String id1 = randomTxId();
      String removableId = randomTxId();

      final var aaaNetwork = (AAANetwork) network(AAANetwork.class).generate();
      aaaNetwork.setUseCertificateTemplate(true);
      aaaNetwork.getWlan().setNetwork(aaaNetwork);
      aaaNetwork.getWlan().setCertificateTemplateIds(List.of(id1, removableId));
      repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
      networkId = aaaNetwork.getId();

      RequestParams requestParams =
              new RequestParams()
                      .addPathVariable(WIFI_NETWORK_ID, networkId)
                      .addPathVariable(CERTIFICATE_TEMPLATE_ID, removableId);

      messageUtil.sendWifiCfgRequest(
              tenantId, requestId,
              CfgAction.DEACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK,
              userName,
              requestParams,
              null);
      assertActivityStatusSuccess(DEACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK, tenantId);

      final AAANetwork network =
              repositoryUtil.find(AAANetwork.class, networkId);
      assertThat(network)
              .isNotNull()
              .matches(n -> Objects.equals(n.getId(), networkId))
              .matches(n -> Objects.equals(n.getWlan().getCertificateTemplateIds(), List.of(id1)));
    }

    @Test
    void testDeactivateCertificateTemplateOnWifiNetwork_cannotDeactivateTheLastAssociation(final Tenant tenant) {
      String networkId;
      String tenantId = tenant.getId();
      String userName = randomName();
      String requestId = randomTxId();
      String removableId = randomTxId();

      final var aaaNetwork = (AAANetwork) network(AAANetwork.class).generate();
      aaaNetwork.setUseCertificateTemplate(true);
      aaaNetwork.getWlan().setNetwork(aaaNetwork);
      aaaNetwork.getWlan().setCertificateTemplateIds(List.of(removableId));
      repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
      networkId = aaaNetwork.getId();

      RequestParams requestParams =
              new RequestParams()
                      .addPathVariable(WIFI_NETWORK_ID, networkId)
                      .addPathVariable(CERTIFICATE_TEMPLATE_ID, removableId);

      assertThatThrownBy(() ->
              messageUtil.sendWifiCfgRequest(
                      tenantId, requestId,
                      CfgAction.DEACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK,
                      userName, requestParams, null))
              .isNotNull()
              .rootCause()
              .isInstanceOf(InvalidPropertyValueException.class);

      assertActivityStatusFail(DEACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK, tenantId);

      final AAANetwork network =
              repositoryUtil.find(AAANetwork.class, networkId);
      assertThat(network)
              .isNotNull()
              .matches(n -> Objects.equals(n.getId(), networkId))
              .matches(n -> Objects.equals(n.getWlan().getCertificateTemplateIds(), List.of(removableId)));
    }

    @Test
    void testDeactivateCertificateTemplateOnWifiNetwork_notUsingCertTemplate_fail(final Tenant tenant) {
      String networkId;
      String tenantId = tenant.getId();
      String userName = randomName();
      String requestId = randomTxId();
      String certificateTemplateId = randomTxId();

      final var aaaNetwork = (AAANetwork) network(AAANetwork.class).generate();
      aaaNetwork.getWlan().setNetwork(aaaNetwork);
      repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId());
      networkId = aaaNetwork.getId();

      RequestParams requestParams =
              new RequestParams()
                      .addPathVariable(WIFI_NETWORK_ID, networkId)
                      .addPathVariable(CERTIFICATE_TEMPLATE_ID, certificateTemplateId);

      assertThatThrownBy(() ->
              messageUtil.sendWifiCfgRequest(
                      tenantId, requestId,
                      CfgAction.DEACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK,
                      userName,
                      requestParams,
                      null)).isNotNull()
              .rootCause()
              .isInstanceOf(InvalidPropertyValueException.class);
      assertActivityStatusFail(DEACTIVATE_CERTIFICATE_TEMPLATE_ON_WIFI_NETWORK, tenantId);
    }
  }

}