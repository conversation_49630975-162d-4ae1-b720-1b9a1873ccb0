package com.ruckus.cloud.wifi.integration.network;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeShort;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.templateString;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.dpskWlanAdvancedCustomization;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertDpskNetworkSoftly;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertWlanSoftly;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DpskNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.DpskWlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkApGroup;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.proto.WlanSecurityEnum;
import com.ruckus.cloud.wifi.repository.AuthRadiusServiceRepository;
import com.ruckus.cloud.wifi.repository.DpskNetworkRepository;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption150Enum;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
public class ConsumeDpskNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  FeatureFlagService featureFlagService;

  @Autowired
  private AuthRadiusServiceRepository authRadiusServiceRepository;

  @Autowired
  private MessageCaptors messageCaptors;

  @Autowired
  private DpskNetworkRepository dpskNetworkRepository;

  @Nested
  class ConsumeAddDpskNetworkRequestTest {

    private String authRadiusId;

    @Payload
    private DpskNetworkGenerator dpskNetworkWithAuthRadius() {
      return Generators.dpskNetwork()
          .setAuthRadiusId(always(authRadiusId))
          .setAuthRadius(always(getAuthRadius(authRadiusId)));
    }

    @Payload("disableAuthProxyWithNonProxyDpsk")
    private DpskNetworkGenerator disableAuthProxyWithNonProxyDpsk() {
      return dpskNetworkWithAuthRadius()
          .setEnableAuthProxy(alwaysFalse());
    }

    @Payload("enableAuthProxyWithNonProxyDpsk")
    private DpskNetworkGenerator enableAuthProxyWithNonProxyDpsk() {
      return dpskNetworkWithAuthRadius()
          .setEnableAuthProxy(alwaysTrue());
    }

    @Payload("addDsaeNetwork")
    private DpskNetworkGenerator addDsaeNetwork() {
      return Generators.dpskNetwork()
          .setName(serialName("AddDsaeNetwork"))
          .setDescription(randomString(64))
          .setDpskServiceProfileId(defaultIdGenerator())
          .setWlan(Generators.dpskWpa23MixedModeWlan());
    }

    @BeforeEach
    void giveAuthRadiusPersistedInDb(final Tenant tenant) {
      final var radius = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
      authRadiusId = radius.getId();
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK)
    void thenShouldHandleDpskNetworkWithAuthRadiusSuccessfully(TxCtxHolder.TxCtx txCtx,
        CfgAction apiAction, @Payload com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("enableAuthProxyWithNonProxyDpsk"))
    void thenShouldHandleEnableAuthProxyWithNonProxyDpskSuccessfully(TxCtxHolder.TxCtx txCtx,
        CfgAction apiAction, @Payload("enableAuthProxyWithNonProxyDpsk") com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("disableAuthProxyWithNonProxyDpsk"))
    void thenShouldHandleDisableAuthProxyWithNonProxyDpskSuccessfully(TxCtxHolder.TxCtx txCtx,
        CfgAction apiAction, @Payload("disableAuthProxyWithNonProxyDpsk") com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("addDsaeNetwork"))
    @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
    @FeatureRole("BETA-DPSK3")
    void thenShouldHandleAddDsaeNetworkSuccessfully(TxCtxHolder.TxCtx txCtx,
        CfgAction apiAction, @Payload("addDsaeNetwork") com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      var createOrUpdateNetworks = dpskNetworkRepository.findByTenantId(txCtx.getTenant()).stream()
          .filter(f -> payload.getId().equals(f.getDsaeNetworkPairId()))
          .collect(Collectors.toList());
      var changedNetworkIds = createOrUpdateNetworks.stream().map(n -> n.getId())
          .collect(Collectors.toList());
      validateDsaeNetworkResult(txCtx, apiAction, changedNetworkIds, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_NETWORK)
  class ConsumeUpdateDpskNetworkRequestTest {

    String networkId;

    String authRadiusId;

    @Payload
    private DpskNetworkGenerator dpskNetworkWithAuthRadius() {
      return Generators.dpskNetwork()
          .setId(nullValue(String.class))
          .setName(serialName("UpdatedDpskNetwork"))
          .setDescription(randomString(64))
          .setAuthRadiusId(always(authRadiusId))
          .setAuthRadius(always(getAuthRadius(authRadiusId)));
    }

    @Payload("enableAuthProxyWithNonProxyDpsk")
    private DpskNetworkGenerator dpskNetworkWithEnableAuthProxy() {
      return dpskNetworkWithAuthRadius()
          .setEnableAuthProxy(alwaysTrue());
    }

    @Payload("updateDsaeNetwork")
    private DpskNetworkGenerator updateDsaeNetwork() {
      return Generators.dpskNetwork()
          .setId(templateString(networkId))
          .setName(serialName("UpdateDsaeNetwork"))
          .setDescription(randomString(64))
          .setDpskServiceProfileId(defaultIdGenerator())
          .setWlan(Generators.dpskWpa23MixedModeWlan());
    }

    @Payload("updateDsaeNetworkWithMlo")
    private DpskNetworkGenerator updateDsaeNetworkWithMlo() {
      return Generators.dpskNetwork()
          .setId(templateString(networkId))
          .setName(serialName("UpdateDsaeNetwork"))
          .setDescription(randomString(64))
          .setDpskServiceProfileId(defaultIdGenerator())
          .setWlan(Generators.dpskWpa23MixedModeWlan()
              .setAdvancedCustomization(
                  dpskWlanAdvancedCustomization().setMultiLinkOperationEnabled(always(true))));
    }

    @BeforeEach
    void givenOneDpskNetworkPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant, final Venue venue) {
      final var authRadius = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());
      authRadiusId = authRadius.getId();

      final var dpskNetwork = network(DpskNetwork.class).generate();
      dpskNetwork.getWlan().setNetwork(dpskNetwork);
      dpskNetwork.setAuthRadius(authRadius);
      authRadius.setAuthNetworks(List.of(dpskNetwork));
      repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
          .setNetwork(always(dpskNetwork)).setVenue(always(venue)).generate();
      dpskNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      networkId = dpskNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      payload.setId(networkId);
      validateResult(txCtx, apiAction, networkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("enableAuthProxyWithNonProxyDpsk"))
    void thenShouldHandleEnableAuthProxySuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
        @Payload("enableAuthProxyWithNonProxyDpsk") com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      payload.setId(networkId);
      validateResult(txCtx, apiAction, networkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK)
    void thenShouldHandleDisableAuthProxySuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      payload.setId(networkId);
      validateResult(txCtx, apiAction, networkId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("updateDsaeNetwork"))
    @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
    @FeatureRole("BETA-DPSK3")
    void thenShouldUpdateDpskNetworkToDsaeNetworkSuccessfully(TxCtxHolder.TxCtx txCtx,
        CfgAction apiAction, @Payload("updateDsaeNetwork") com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      payload.setId(networkId);
      validateDsaeNetworkResult(txCtx, apiAction, List.of(networkId), payload);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
    void thenShouldUpdateDpskNetworkToDsaeNetworkWithMloSuccessfully(TxCtxHolder.TxCtx txCtx) {
      var networkRequest = updateDsaeNetworkWithMlo().generate();
      networkRequest.setId(networkId);
      WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(txCtx.getTenant())
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3,AP-70")
          .apiAction(CfgAction.UPDATE_NETWORK)
          .requestParams(new RequestParams().addPathVariable("networkId", networkRequest.getId()))
          .payload(networkRequest).build();
      assertThatExceptionOfType(RuntimeException.class)
          .isThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequest));
    }

  }

  @Nested
  @ApiAction(CfgAction.UPDATE_NETWORK)
  class ConsumeUpdateDsaeNetworkRequestTest {

    String networkId1;
    String networkId2;
    String apGroupId;

    @Payload("updateDpskNetwork")
    private DpskNetworkGenerator updateDpskNetwork() {
      return Generators.dpskNetwork()
          .setId(always(networkId1))
          .setName(serialName("UpdateDpskNetwork"))
          .setDescription(randomString(64))
          .setDpskServiceProfileId(defaultIdGenerator())
          .setWlan(Generators.dpskWpa2Wlan().setSsid(always(networkId1)));
    }

    @Payload("updateDsaeNetwork")
    private DpskNetworkGenerator updateDsaeNetwork() {
      return Generators.dpskNetwork()
          .setId(templateString(networkId1))
          .setName(serialName("UpdateDsaeNetwork"))
          .setDescription(randomString(64))
          .setDpskServiceProfileId(defaultIdGenerator())
          .setWlan(Generators.dpskWpa23MixedModeWlan());
    }

    @Payload("addDsaeNetwork")
    private DpskNetworkGenerator addDsaeNetwork() {
      return Generators.dpskNetwork()
          .setName(serialName("AddDsaeNetwork"))
          .setDescription(randomString(64))
          .setDpskServiceProfileId(defaultIdGenerator())
          .setWlan(Generators.dpskWpa23MixedModeWlan());
    }

    @BeforeEach
    void givenDsaeNetworkPersistedInDbAndActivatedOnOneVenue(@DefaultApGroup ApGroup apGroup,
        final Venue venue, TxCtxHolder.TxCtx txCtx) {

      apGroupId = apGroup.getId();
      var networkRequest = addDsaeNetwork().generate();
      networkRequest.setId(randomId());
      WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(txCtx.getTenant())
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
          .apiAction(CfgAction.ADD_NETWORK)
          .payload(networkRequest).build();

      messageUtil.sendWifiCfgRequest(wifiCfgRequest);

      var dsaeNetwork = (DpskNetwork) dpskNetworkRepository.findByIdAndTenantId(networkRequest.getId(), txCtx.getTenant()).get();
      var dsaeOnboardNetwork = dpskNetworkRepository.findByDsaeNetworkPairIdAndTenantIdAndIsDsaeServiceNetwork(
          dsaeNetwork.getDsaeNetworkPairId(), TxCtxHolder.tenantId(), false);

      final var dsaeServiceNetworkActivationMapping = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
      final var networkApGroup = new NetworkApGroup();
      networkApGroup.setApGroupId(apGroupId);
      networkApGroup.setVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());

      dsaeServiceNetworkActivationMapping.setId(randomId()); // autoGenerated is true in wifi-api
      dsaeServiceNetworkActivationMapping.setNetworkId(dsaeNetwork.getId());
      dsaeServiceNetworkActivationMapping.setVenueId(venue.getId());
      dsaeServiceNetworkActivationMapping.setApGroups(List.of(networkApGroup));
      dsaeServiceNetworkActivationMapping.setIsAllApGroups(true);
      dsaeServiceNetworkActivationMapping.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dsaeServiceNetworkActivationMapping.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      sendCreateNetworkActivation(txCtx.getTenant(), List.of(dsaeServiceNetworkActivationMapping));

      networkId1 = dsaeNetwork.getId();
      networkId2 = dsaeOnboardNetwork.getId();

    }

    private void sendCreateNetworkActivation(String tenantId, List<com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping> networkVenues) {
      WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .apiAction(CfgAction.ADD_NETWORK_VENUE_MAPPINGS)
          .payload(networkVenues).build();

      messageUtil.sendWifiCfgRequest(wifiCfgRequest);
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId1);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("updateDsaeNetwork"))
    @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
    @FeatureRole("BETA-DPSK3")
    void thenShouldUpdateDsaeNetworkSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
        @Payload("updateDsaeNetwork") com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      payload.setId(networkId1);
      var createOrUpdateNetworks = dpskNetworkRepository.findByTenantId(txCtx.getTenant()).stream()
          .filter(f -> payload.getId().equals(f.getDsaeNetworkPairId()))
          .collect(Collectors.toList());
      var changedNetworkIds = createOrUpdateNetworks.stream().map(n -> n.getId())
          .collect(Collectors.toList());
      validateDsaeNetworkResult(txCtx, apiAction, changedNetworkIds, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("updateDpskNetwork"))
    @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
    @FeatureRole("BETA-DPSK3")
    void thenShouldUpdateDsaeNetworkToWpa2DpskNetworkSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
        @Payload("updateDpskNetwork") com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
      payload.setId(networkId1);
      validateDsaeNetworkResult(txCtx, apiAction, List.of(networkId1), payload);
    }
  }

  com.ruckus.cloud.wifi.eda.viewmodel.Radius getAuthRadius(String authRadiusId) {
    var authRadiusViewModel = new com.ruckus.cloud.wifi.eda.viewmodel.Radius();
    authRadiusViewModel.setId(authRadiusId);
    return authRadiusViewModel;
  }


  private void validateResult(TxCtx txCtx, CfgAction apiAction, String networkId,
      com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
    validateRepositoryData(txCtx, apiAction, networkId, payload);
    validateWifiCfgChangeMessages(txCtx, apiAction, List.of(networkId), payload);
    validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
  }

  private void validateDsaeNetworkResult(TxCtx txCtx, CfgAction apiAction, List<String> changedNetworkIds,
      com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
    validateRepositoryData(txCtx, apiAction, payload.getId(), payload);
    validateWifiCfgChangeMessages(txCtx, apiAction, changedNetworkIds, payload);
    validateDsaeDdccmCfgRequestMessages(txCtx, apiAction, changedNetworkIds, payload);
  }

  private void validateRepositoryData(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
      String networkId, com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
    if (networkId == null) {
      final String requestId = txCtx.getTxId();
      final String tenantId = txCtx.getTenant();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty, "should be empty");
      return;
    }

    final DpskNetwork network = repositoryUtil.find(DpskNetwork.class, networkId);

    if (apiAction == null || apiAction == CfgAction.DELETE_NETWORK
        || apiAction == CfgAction.DELETE_NETWORKS || payload == null) {
      assertThat(network).isNull();
      return;
    }

    assertThat(network)
        .isNotNull()
        .matches(n -> Objects.equals(n.getId(), networkId))
        .matches(n -> Objects.equals(n.getName(), payload.getName()))
        .matches(n -> Objects.equals(n.getDescription(), payload.getDescription()));

    if (payload.getAuthRadius() == null) {
      assertThat(network.getAuthRadius()).isNull();
      return;
    }

    assertThat(network.getAuthRadius())
        .isNotNull()
        .extracting(BaseEntity::getId)
        .isEqualTo(payload.getAuthRadius().getId());

    final List<AuthRadiusService> authRadiusServiceList = authRadiusServiceRepository
        .findByTenantIdAndRadiusId(txCtx.getTenant(), network.getAuthRadius().getId());

    if (network.getEnableAuthProxy()) {
      assertThat(authRadiusServiceList)
          .isNotEmpty();
    } else {
      assertThat(authRadiusServiceList)
          .isEmpty();
    }
  }

  private void validateWifiCfgChangeMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
      List<String> networkIdList, com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
    if (apiAction == null || networkIdList == null) {
      messageCaptors.getWifiCfgChangeMessageCaptor().assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final var networkIdListSize = networkIdList.size();
    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(txCtx.getTenant(), txCtx.getTxId());

    assertThat(wifiCfgChangeMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction.key()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
            assertThat(wifiCfgChangeMessage.getPayload())
                    .satisfies(msg -> assertSoftly(softly -> {
                      softly.assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
                      softly.assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
                    }))
                    .extracting(WifiConfigChange::getOperationList).asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
                    .satisfies(ops -> {
                      assertThat(ops)
                              .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasDpskNetwork)
                              .filteredOn(op -> op.getAction() == wifiProtoAction(apiAction))
                              .filteredOn(op -> op.getDpskNetwork().getId().getValue().equals(payload.getId()))
                              .as("The %s DpskNetwork operation count should be 1", wifiProtoAction(apiAction).name())
                              .hasSize(1)
                              .singleElement()
                              .extracting(com.ruckus.cloud.wifi.proto.Operation::getDpskNetwork)
                              .satisfies(assertDpskNetworkSoftly(payload));
                      assertThat(ops)
                              .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasWlan)
                              .filteredOn(op -> op.getAction() == wifiProtoAction(apiAction))
                              .as("The %s Wlan operation count should be 1", wifiProtoAction(apiAction).name())
                              .hasSize(networkIdListSize).filteredOn(op -> op.getWlan().getWlanSecurity().equals(
                                  WlanSecurityEnum.valueOf("WlanSecurityEnum_" + payload.getWlan().getWlanSecurity().name())))
                              .singleElement()
                              .extracting(com.ruckus.cloud.wifi.proto.Operation::getWlan)
                              .satisfies(assertWlanSoftly(payload.getWlan()));
                      if (payload.getAuthRadius() != null) {
                        assertThat(ops)
                                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasRadius)
                                .filteredOn(
                                        op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY)
                                .as("The MODIFY Radius operation count should be 1").hasSize(1)
                                .singleElement()
                                .extracting(com.ruckus.cloud.wifi.proto.Operation::getRadius)
                                .satisfies(radiusOp -> {
                                  assertThat(radiusOp.getId())
                                          .extracting(StringValue::getValue)
                                          .isEqualTo(payload.getAuthRadius().getId());
                                });
                        if (BooleanUtils.isTrue(payload.getEnableAuthProxy())) {
                          assertThat(ops)
                                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasAuthRadiusService)
                                  .filteredOn(op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                                  .as("The ADD AuthRadiusService operation count should be 1").hasSize(1)
                                  .singleElement()
                                  .extracting(com.ruckus.cloud.wifi.proto.Operation::getAuthRadiusService)
                                  .satisfies(authRadiusServiceOp -> {
                                    assertThat(authRadiusServiceOp.getRadiusId())
                                            .extracting(StringValue::getValue)
                                            .isEqualTo(payload.getAuthRadius().getId());
                                    assertThat(ops)
                                            .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasAuthRadiusProfile)
                                            .filteredOn(op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                                            .as("The ADD AuthRadiusProfile operation count should be 1")
                                            .hasSize(1)
                                            .singleElement()
                                            .extracting(com.ruckus.cloud.wifi.proto.Operation::getAuthRadiusProfile)
                                            .satisfies(authRadiusProfileOp -> {
                                              assertThat(authRadiusProfileOp.getAuthRadiusServiceId())
                                                      .isEqualTo(authRadiusServiceOp.getId());
                                            });
                                  });
                        }
                      }
                    }));
  }

  private com.ruckus.cloud.wifi.proto.Operation.Action wifiProtoAction(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK -> com.ruckus.cloud.wifi.proto.Operation.Action.ADD;
      case UPDATE_NETWORK -> com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY;
      case DELETE_NETWORK, DELETE_NETWORKS -> com.ruckus.cloud.wifi.proto.Operation.Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void validateDdccmCfgRequestMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
      List<String> networkIdList, com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {
    final boolean isNonProxy =
        payload.getAuthRadius() != null && BooleanUtils.isFalse(payload.getEnableAuthProxy());

    if (apiAction == null
        || (apiAction == CfgAction.ADD_NETWORK
        && isNonProxy)
        || networkIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(txCtx.getTenant(), txCtx.getTxId());

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    final int radiusAuthenticationServiceSize = isNonProxy ? 0 : 1;
    final int authRadiusVenueSize = isNonProxy ? 1 : 0;
    AtomicReference<String> radiusAuthenticationServiceId = new AtomicReference<>();
    AtomicReference<String> authRadiusVenueId = new AtomicReference<>();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn( com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasRadiusAuthenticationService)
            .describedAs("Should contains %s radiusAuthenticationService operations", radiusAuthenticationServiceSize)
            .hasSize(radiusAuthenticationServiceSize)
            .extracting(Operation::getRadiusAuthenticationService)
            .map(radiusAuthenticationService -> {
              radiusAuthenticationServiceId.set(radiusAuthenticationService.getId());
              return radiusAuthenticationServiceId;
            })

    );

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenueRadius)
            .describedAs("Should contains %s venueRadius operations", authRadiusVenueSize)
            .hasSize(authRadiusVenueSize)
            .extracting(Operation::getVenueRadius)
            .map(venueRadius -> {
              authRadiusVenueId.set(venueRadius.getId());
              return authRadiusVenueId;
            })
    );

    if (!apiAction.equals(CfgAction.UPDATE_NETWORK)) {
      return;
    }

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction().equals(action(apiAction)))
            .describedAs("Should contains %s WlanVenue operations", networkIdList.size())
            .hasSize(networkIdList.size())
            .allMatch(op -> StringUtils.isNotEmpty(op.getId()),
                "All the ids of the WlanVenue operations should not be empty")
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                    .matches(n -> networkIdList.contains(n.getWlanId()));
              } else {
                assertThat(payload).isNotNull();
                assertThat(op)
                    .extracting(Operation::getWlanVenue)
                    .extracting(WlanVenue::getAuthAaa)
                    .matches(wlanAuth -> Objects.equals(wlanAuth.getThroughController(), !isNonProxy))
                    .matches(wlanAuth -> Objects.equals(wlanAuth.getId(), isNonProxy ? authRadiusVenueId.get() : radiusAuthenticationServiceId.get()));
              }
            }));

    ddccmCfgRequestMessage.getPayload().getOperationsList()
            .stream()
            .filter(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .map(Operation::getWlanVenue)
            .forEach(wlanVenue -> {
              validateAdvancedCustomization(payload, wlanVenue);
            });
  }

  private void validateDsaeDdccmCfgRequestMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
      List<String> networkIdList, com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload) {

    if (apiAction == null || networkIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtx.getTenant());
      return;
    }
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(txCtx.getTenant(), txCtx.getTxId());

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    if (!apiAction.equals(CfgAction.UPDATE_NETWORK)) {
      return;
    }

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .filteredOn(op -> op.getAction().equals(action(apiAction)))
            .describedAs("Should contains %s WlanVenue operations", networkIdList.size())
            .hasSize(networkIdList.size())
            .allMatch(op -> StringUtils.isNotEmpty(op.getId()),
                "All the ids of the WlanVenue operations should not be empty")
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                    .matches(n -> networkIdList.contains(n.getWlanId()));
              } else {
                assertThat(payload).isNotNull();
              }
            }));

    ddccmCfgRequestMessage.getPayload().getOperationsList()
        .stream()
        .filter(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .map(Operation::getWlanVenue)
        .forEach(wlanVenue -> {
          if (StringUtils.isNotBlank(payload.getDpskServiceProfileId()) && StringUtils.isNotBlank(
              wlanVenue.getAuthenticationServerId())) {
            assertThat(wlanVenue).matches(
                    wlan -> wlan.getAuthAaa().getId().equals(TxCtxHolder.tenantId() + "-Auth-dpsk-AAA"))
                .matches(wlan -> wlan.getAuthenticationServerId()
                    .equals(TxCtxHolder.tenantId() + "-Auth-dpsk-AAA"));
          }
        });
  }

  private void validateAdvancedCustomization(com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork payload, WlanVenue wlanVenue) {
    DpskWlanAdvancedCustomization sourceAdv = payload.getWlan().getAdvancedCustomization();

    StringBuilder dhcpOpt82Format = new StringBuilder();
    dhcpOpt82Format.append(sourceAdv.getDhcpOption82SubOption1Format().getValue());
    dhcpOpt82Format.append(sourceAdv.getDhcpOption82SubOption2Format().getValue());
    dhcpOpt82Format.append(DhcpOption82SubOption150Enum.SUBOPT150_VLAN_ID.getValue());
    dhcpOpt82Format.append(sourceAdv.getDhcpOption82SubOption151Format().getValue());

    assertThat(wlanVenue)
        .isNotNull()
        .matches(wlan -> (sourceAdv.getDhcpOption82Enabled() ? 1 : 0) == wlan.getDhcpOption82(), "DHCP Opt 82 Enabled")
        .matches(wlan -> dhcpOpt82Format.toString().equals(wlan.getDhcpOption82Format()), "DHCP Option 82 format")
        .matches(wlan -> sourceAdv.getDhcpOption82MacFormat().getValue().equals(wlan.getDhcpOption82MacFormat().name()), "DHCP Option 82 mac format")
        .matches(wlan -> sourceAdv.getDtimInterval() == wlan.getDtimInterval().getValue(), "DTIM interval")
        .extracting(WlanVenue::getAdvancedCustomization)
        .isNotNull()
        .matches(wlanAdv -> sourceAdv.getTotalUplinkRateLimiting() == wlanAdv.getTotalUplinkRateLimiting(), "Total Uplink Rate Limiting")
        .matches(wlanAdv -> sourceAdv.getTotalDownlinkRateLimiting() == wlanAdv.getTotalDownlinkRateLimiting(), "Total Downlink Rate Limiting")
        .matches(wlanAdv -> sourceAdv.getAgileMultibandEnabled().equals(wlanAdv.getAgileMultibandEnabled().getValue()), "AMB");


    if (StringUtils.isNotBlank(payload.getDpskServiceProfileId()) && StringUtils.isNotBlank(
        wlanVenue.getAuthenticationServerId())) {
        assertThat(wlanVenue).matches(
                wlan -> wlan.getAuthAaa().getId().equals(TxCtxHolder.tenantId() + "-Auth-dpsk-AAA"))
            .matches(wlan -> wlan.getAuthenticationServerId()
                .equals(TxCtxHolder.tenantId() + "-Auth-dpsk-AAA"));
    }
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK -> Action.ADD;
      case UPDATE_NETWORK -> Action.MODIFY;
      case DELETE_NETWORK, DELETE_NETWORKS -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
