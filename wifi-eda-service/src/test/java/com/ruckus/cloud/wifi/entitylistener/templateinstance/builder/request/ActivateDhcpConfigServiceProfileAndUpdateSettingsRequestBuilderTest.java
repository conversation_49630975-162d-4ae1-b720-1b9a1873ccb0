package com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request;

import static com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile;
import static com.ruckus.cloud.wifi.test.fixture.TenantTestFixture.randomTenant;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplate;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplateInstance;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.templateinstance.TemplateInstancePostCreatePackage;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.ActivateDhcpConfigServiceProfileAndUpdateSettingsInstanceOperation;
import com.ruckus.cloud.wifi.repository.DhcpConfigServiceProfileRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class ActivateDhcpConfigServiceProfileAndUpdateSettingsRequestBuilderTest {

  @SpyBean
  ActivateDhcpConfigServiceProfileAndUpdateSettingsRequestBuilder builder;
  @MockBean
  VenueRepository venueRepository;
  @MockBean
  DhcpConfigServiceProfileRepository dhcpConfigServiceProfileRepository;
  @MockBean
  ActivateDhcpConfigServiceProfileAndUpdateSettingsInstanceOperation operation;

  private Tenant mspTenant;
  private Tenant ecTenant;
  private Venue venueTemplate;
  private Venue venueInstance;
  private DhcpConfigServiceProfile dhcpConfigServiceProfileTemplate;
  private DhcpConfigServiceProfile dhcpConfigServiceProfileInstance;

  @BeforeEach
  public void setUp() {
    ecTenant = randomTenant((e) -> {
    });
    mspTenant = randomTenant(e -> e.setId(TxCtxHolder.tenantId()));
    venueTemplate = randomVenueTemplate(mspTenant);
    venueInstance = randomVenueTemplateInstance(venueTemplate.getId(), ecTenant);
    dhcpConfigServiceProfileTemplate = randomDhcpConfigServiceProfile(true);
    venueTemplate.setDhcpConfigServiceProfile(dhcpConfigServiceProfileTemplate);
    dhcpConfigServiceProfileInstance = randomDhcpConfigServiceProfile(false);
  }

  @Test
  void build() {
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(venueTemplate.getId(), ecTenant.getId());

    doReturn(Optional.of(dhcpConfigServiceProfileInstance))
        .when(dhcpConfigServiceProfileRepository)
        .findByTemplateIdAndTenantId(dhcpConfigServiceProfileTemplate.getId(), ecTenant.getId());

    Optional<TemplateInstancePostCreatePackage<?, ?>> result = builder
            .setTemplateTenantId(mspTenant.getId())
            .setTargetTenantId(ecTenant.getId())
            .build(venueTemplate);

    assertTrue(result.isPresent());
  }
}
