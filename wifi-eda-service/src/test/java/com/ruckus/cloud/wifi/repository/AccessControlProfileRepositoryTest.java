package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO signature_package (id, version, default_version, next_version, releasable)
        VALUES ('v1', 'v1', true, 'v2', true);
    INSERT INTO signature_package (id, version, default_version, next_version, releasable)
        VALUES ('v2', 'v2', false, null, true);
    INSERT INTO application_policy (id, name, tenant, signature_package)
        VALUES ('ap_1', 'policy1', '4c8279f79307415fa9e4c88a1819f0fc', 'v1');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_1_1', 'rule1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_1', 'SIGNATURE',
            1496, 'COMM', 3, 'Audio/Video');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_1_2', 'rule2', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_1', 'SIGNATURE',
            3442, 'Realvnc', 13, 'Game');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_1_3', 'rule3', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_1', 'SIGNATURE',
            1138, 'Stafaband.info', 30, 'Web');
    INSERT INTO application_policy (id, name, tenant, signature_package)
        VALUES ('ap_2', 'policy1', '4c8279f79307415fa9e4c88a1819f0fc', 'v2');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_2_1', 'rule1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'SIGNATURE',
            1496, 'COMM', 3, 'Audio/Video');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_2_2', 'rule2', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'SIGNATURE',
            3442, 'Realvnc', 13, 'Game');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_2_3', 'rule3', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'SIGNATURE',
            1138, 'Stafaband.info', 30, 'Web');
    INSERT INTO l2acl_policy (id, name, tenant, mac_addresses, access)
        VALUES ('l2acl_1', 'l2acl-policy-1', '4c8279f79307415fa9e4c88a1819f0fc', '[11:22:33:44:55:66]', 'ALLOW');
    INSERT INTO l2acl_policy (id, name, tenant, mac_addresses, access, is_template)
        VALUES ('l2acl_template_1', 'l2acl-template-1', '4c8279f79307415fa9e4c88a1819f0fc', '[11:22:33:44:55:66]', 'ALLOW', true);
    INSERT INTO l2acl_policy (id, name, tenant, mac_addresses, access, is_template)
        VALUES ('l2acl_not_template_1', 'l2acl-not-template-1', '4c8279f79307415fa9e4c88a1819f0fc', '[11:22:33:44:55:66]', 'ALLOW', false);
    INSERT INTO l3acl_policy (id, name, tenant, default_access)
        VALUES ('l3acl_1', 'l3acl-policy-1', '4c8279f79307415fa9e4c88a1819f0fc', 'ALLOW');
    INSERT INTO l3rule (id, description, tenant, priority, source_enable_ip_subnet, access, l3acl_policy)
        VALUES ('l3rule_1', 'l3rule-1', '4c8279f79307415fa9e4c88a1819f0fc', 1, true, 'ALLOW', 'l3acl_1');
    INSERT INTO access_control_profile (id, name, tenant, application_policy, previous_application_policy)
        VALUES ('acp_1', 'profile1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'ap_1');
    INSERT INTO access_control_profile (id, name, tenant, application_policy, previous_application_policy)
        VALUES ('acp_2', 'profile2', '4c8279f79307415fa9e4c88a1819f0fc', null, 'ap_1');
    INSERT INTO access_control_profile (id, name, tenant, l2acl_policy, l2acl_enable)
        VALUES ('acp_3', 'profile3', '4c8279f79307415fa9e4c88a1819f0fc', 'l2acl_1', true);
    INSERT INTO access_control_profile (id, name, tenant, l3acl_policy, l3acl_enable)
        VALUES ('acp_4', 'profile4', '4c8279f79307415fa9e4c88a1819f0fc', 'l3acl_1', true);
    INSERT INTO access_control_profile (id, name, tenant, application_policy, previous_application_policy)
        VALUES ('acp_5', 'profile5', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', null);
    INSERT INTO access_control_profile (id, name, tenant, l2acl_policy, l2acl_enable, is_template)
        VALUES ('acp_template_1', 'profileTemplate', '4c8279f79307415fa9e4c88a1819f0fc', 'l2acl_template_1', true, true);
    INSERT INTO access_control_profile (id, name, tenant, l2acl_policy, l2acl_enable, is_template)
        VALUES ('acp_not_template_1', 'profileNotTemplate', '4c8279f79307415fa9e4c88a1819f0fc', 'l2acl_not_template_1', true, false);
    """)
class AccessControlProfileRepositoryTest {

  private static final String TENANT_ID ="4c8279f79307415fa9e4c88a1819f0fc";

  @Autowired
  private AccessControlProfileRepository accessControlProfileRepository;

  @Test
  void findByTenantIdAndApplicationPolicyInTest() {
    var result = accessControlProfileRepository.findByTenantIdAndApplicationPolicyIn(
        TENANT_ID, List.of(new ApplicationPolicy("ap_2")));
    assertEquals(2, result.size());
    assertEquals(Set.of("acp_1", "acp_5"),
        result.stream().map(AccessControlProfile::getId).collect(Collectors.toSet()));
    assertEquals(Set.of("ap_1"),
        result.stream()
            .filter(p -> p.getPreviousApplicationPolicy() != null)
            .map(AccessControlProfile::getPreviousApplicationPolicy)
            .map(ApplicationPolicy::getId)
            .collect(Collectors.toSet()));
  }

  @Test
  void findByTenantIdAndApplicationPolicyIsNullAndPreviousApplicationPolicyIsNotNullTest() {
    var result = accessControlProfileRepository.findByTenantIdAndApplicationPolicyIsNullAndPreviousApplicationPolicyIsNotNull(
            TENANT_ID);
    assertEquals(1, result.size());
    assertEquals("acp_2", result.get(0).getId());
  }

  @Test
  void findByTenantIdAndApplicationPolicyIsNotNullAndPreviousApplicationPolicyIsNullTest() {
    var result = accessControlProfileRepository.findByTenantIdAndApplicationPolicyIsNotNullAndPreviousApplicationPolicyIsNull(
        TENANT_ID);
    assertEquals(1, result.size());
    assertEquals("acp_5", result.get(0).getId());
  }

  @Test
  void findByTenantIdAndPreviousApplicationPolicyIsNotNullTest() {
    var result = accessControlProfileRepository.findByTenantIdAndPreviousApplicationPolicyIsNotNull(
        TENANT_ID);
    assertEquals(2, result.size());
    assertEquals(2, result.stream()
        .filter(p -> p.getPreviousApplicationPolicy().getId().equals("ap_1")).count());
    assertEquals(Set.of("acp_1", "acp_2"), result.stream().map(AccessControlProfile::getId)
        .collect(Collectors.toSet()));
  }

  @Test
  void findByNameLikeAndTenantIdTest() {
    Page<AccessControlProfile> result = accessControlProfileRepository.findByNameLikeAndTenantId(
        Pageable.unpaged(), "%profile1%", TENANT_ID);
    assertEquals(1, result.getTotalElements());
    assertThat(result.getContent().get(0).getName()).isEqualTo("profile1");
  }

  @Test
  void findByTenantIdAndL2AclPolicyIdInTest() {
    var result = accessControlProfileRepository.findByTenantIdAndL2AclPolicyIdIn(TENANT_ID, List.of("l2acl_1"));

    assertEquals(1, result.size());
    assertEquals("acp_3", result.get(0).getId());
    assertEquals("l2acl_1", result.get(0).getL2AclPolicy().getId());
  }

  @Test
  void findByTenantIdAndL3AclPolicyIdIn() {
    var result = accessControlProfileRepository.findByTenantIdAndL3AclPolicyIdIn(TENANT_ID, List.of("l3acl_1"));

    assertEquals(1, result.size());
    assertEquals("acp_4", result.get(0).getId());
    assertEquals("l3acl_1", result.get(0).getL3AclPolicy().getId());
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantIdAndIsTemplate() {
    var result = accessControlProfileRepository.findByIdAndTenantId("acp_template_1", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("acp_template_1", result.get().getId());
    assertEquals(true, result.get().getIsTemplate());
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantIdAndIsTemplateWithIsNotTemplateId() {
    var result = accessControlProfileRepository.findByIdAndTenantId("acp_not_template_1", TENANT_ID);

    assertTrue(result.isEmpty());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplate() {
    var result = accessControlProfileRepository.findByIdAndTenantId("acp_not_template_1", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("acp_not_template_1", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplateWithLegacyData() {
    var result = accessControlProfileRepository.findByIdAndTenantId("acp_1", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("acp_1", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }

  @ApplyTemplateFilter
  @Test
  void testExistsByNameAndIdNotAndTenantIdAndIsTemplate() {
    assertTrue(accessControlProfileRepository
        .existsByNameAndIdNotAndTenantId("profileTemplate", "id", TENANT_ID));
    assertFalse(accessControlProfileRepository
        .existsByNameAndIdNotAndTenantId("profileTemplate", "acp_template_1", TENANT_ID));
  }

  @Test
  void testExistsByNameAndIdNotAndTenantIdAndIsNotTemplate() {
    assertTrue(accessControlProfileRepository
        .existsByNameAndIdNotAndTenantId("profileNotTemplate", "id", TENANT_ID));
    assertFalse(accessControlProfileRepository
        .existsByNameAndIdNotAndTenantId("profileNotTemplate", "acp_not_template_1", TENANT_ID));
  }

  @Test
  void testFindAllProfilesApplicationPolicyNotDisabled() {
    var allProfiles =
        accessControlProfileRepository.findAll().stream()
            .filter(
                profile ->
                    profile.getApplicationPolicy() != null
                        || !Boolean.FALSE.equals(profile.getApplicationPolicyEnable()))
            .map(AccessControlProfile::getId)
            .collect(Collectors.toSet());
    var result =
        accessControlProfileRepository
            .findAllProfilesApplicationPolicyNotDisabled(TENANT_ID)
            .stream()
            .map(AccessControlProfile::getId)
            .collect(Collectors.toSet());
    assertEquals(allProfiles, result);
  }
}
