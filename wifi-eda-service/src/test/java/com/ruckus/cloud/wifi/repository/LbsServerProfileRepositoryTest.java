package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import static org.assertj.core.api.Assertions.assertThat;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('6700bc51acf84c4aa9510df2ca00b5f4'), ('ce84b64805604d7b91e2a35331e37e51');
    INSERT INTO lbs_server_profile (id, lbs_server_venue_name, server_address, tenant) VALUES
        ('059796b87b054c359a9828a354aab591', 'vspot-1', '***********', '6700bc51acf84c4aa9510df2ca00b5f4'),
        ('059796b87b054c359a9828a354aab592', 'vspot-2', '***********', '6700bc51acf84c4aa9510df2ca00b5f4'),
        ('059796b87b054c359a9828a354aab593', 'vspot-3', '***********', 'ce84b64805604d7b91e2a35331e37e51');
    """)
public class LbsServerProfileRepositoryTest {

  private static final String TENANT_ID = "6700bc51acf84c4aa9510df2ca00b5f4";

  @Autowired
  private LbsServerProfileRepository repository;

  @Test
  void testExistsByTenantIdAndLbsServerVenueNameAndServerAddressAndIdNot() {
    assertThat(repository.existsByTenantIdAndLbsServerVenueNameAndServerAddressAndIdNot(TENANT_ID, "vspot-1"
        , "***********", "6ce256cd9406475994ed319c2f4e278f")).isTrue(); // duplicate
    assertThat(repository.existsByTenantIdAndLbsServerVenueNameAndServerAddressAndIdNot(TENANT_ID, "vspot-2"
        , "***********", "6ce256cd9406475994ed319c2f4e278f")).isFalse(); // same serverAddress
    assertThat(repository.existsByTenantIdAndLbsServerVenueNameAndServerAddressAndIdNot(TENANT_ID, "vspot-1"
        , "***********", "6ce256cd9406475994ed319c2f4e278f")).isFalse(); // same lbsServerVenueName
    assertThat(repository.existsByTenantIdAndLbsServerVenueNameAndServerAddressAndIdNot(TENANT_ID, "vspot-1"
        , "***********", "059796b87b054c359a9828a354aab591")).isFalse(); // filter itself
    assertThat(repository.existsByTenantIdAndLbsServerVenueNameAndServerAddressAndIdNot(TENANT_ID, "vspot-3"
        , "***********", "6ce256cd9406475994ed319c2f4e278f")).isFalse(); // different tenant
  }

}
