package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.ApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupRadioGenerator;
import com.ruckus.cloud.wifi.mapper.NetworkApGroupMergerImpl;
import com.ruckus.cloud.wifi.repository.NetworkApGroupRadioRepository;
import com.ruckus.cloud.wifi.repository.NetworkApGroupRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.service.ExtendedVlanPoolServiceCtrl;
import com.ruckus.cloud.wifi.service.NetworkApGroupService;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.NetworkApGroupServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.InitVenueServiceImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.IsAllApGroups;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@WifiJpaDataTest
public class NetworkApGroupServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private NetworkApGroupService unit;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @MockBean
  private ExtendedVlanPoolServiceCtrl extendedVlanPoolServiceCtrl;

  @Test
  void testCreateNetworkApGroups_onAllApGroups(@OpenNetwork Network network, Venue venue,
      VlanPool vlanPool, NetworkVenue networkVenue) throws Exception {
    doReturn(vlanPool).when(extendedVlanPoolServiceCtrl).getVlanPool(eq(vlanPool.getId()));
    List<ApGroup> apGroups = new ApGroupGenerator()
        .setVenue(always(venue))
        .generate(3);
    apGroups.forEach(
        e -> repositoryUtil.createOrUpdate(e, venue.getTenant().getId(), randomTxId()));

    networkVenue.setIsAllApGroups(true);
    networkVenue.setVlanPoolId(vlanPool.getId());
    networkVenue.setAllApGroupsVlanId((short) 1);
    networkVenue.setAllApGroupsRadio(RadioEnum.Both);
    networkVenue.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz));

    List<NetworkApGroup> result = unit.createNetworkApGroups(
        null, networkVenue, Optional.empty());

    assertThat(result).hasSize(3)
        .satisfies(e ->
            e.forEach(networkApGroup ->
                assertThat(networkApGroup.getNetworkApGroupRadios())
                    .hasSize(4)
                    .satisfies(radio -> {
                      assertThat(radio).extracting("radio")
                          .containsOnly(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz,
                              StrictRadioTypeEnum.Lower_5_GHz, StrictRadioTypeEnum.Upper_5_GHz);
                      assertThat(radio).extracting("vlanId")
                          .containsOnly((short) 1);
                      assertThat(radio).extracting("vlanPool").extracting("id")
                          .containsOnly(vlanPool.getId());
                    })));
  }

  @Test
  void testCreateNetworkApGroups_onSpecificApGroups(@OpenNetwork Network network, Venue venue,
      @DefaultApGroup ApGroup apGroup, VlanPool vlanPool, NetworkVenue networkVenue)
      throws Exception {
    doReturn(vlanPool).when(extendedVlanPoolServiceCtrl).getVlanPool(eq(vlanPool.getId()));
    List<ApGroup> apGroups = new ApGroupGenerator()
        .setVenue(always(venue))
        .generate(3);
    apGroups.forEach(
        e -> repositoryUtil.createOrUpdate(e, venue.getTenant().getId(), randomTxId()));

    // Only apply 2 apGroups
    List<NetworkApGroup> networkApGroups = new NetworkApGroupGenerator()
        .setApGroup(options(apGroups))
        .setVlanPoolId(always(vlanPool.getId()))
        .setNetworkApGroupRadios(list(Generators.networkApGroupRadio()
            .setVlanId(always((short) 10)), StrictRadioTypeEnum.values().length))
        .generate(2);

    networkVenue.setNetworkApGroups(networkApGroups);
    networkVenue.setIsAllApGroups(false);
    networkApGroups.forEach(e -> {
      e.setNetworkVenue(networkVenue);
      e.getNetworkApGroupRadios().forEach(radio -> radio.setId(null));
    });

    List<NetworkApGroup> result = unit.createNetworkApGroups(
        networkVenue.getNetworkApGroups(), networkVenue, Optional.empty());

    assertNetworkApGroups(networkApGroups, result, vlanPool.getId());
  }

  @Test
  void testCreateNetworkApGroups_onDefaultApGroup(@OpenNetwork Network network, Venue venue,
      @DefaultApGroup ApGroup apGroup, VlanPool vlanPool, NetworkVenue networkVenue)
      throws Exception {
    doReturn(vlanPool).when(extendedVlanPoolServiceCtrl).getVlanPool(eq(vlanPool.getId()));
    List<NetworkApGroup> networkApGroups = new NetworkApGroupGenerator()
        .setApGroup(options(List.of(new ApGroup()))) // No id, should indicate to default Ap group
        .setNetworkApGroupRadios(list(Generators.networkApGroupRadio()
            .setVlanId(always((short) 10)), StrictRadioTypeEnum.values().length))
        .setVlanPoolId(always(vlanPool.getId()))
        .generate(1);

    networkVenue.setNetworkApGroups(networkApGroups);
    networkVenue.setIsAllApGroups(false);

    networkApGroups.forEach(e -> {
      e.setNetworkVenue(networkVenue);
      e.getNetworkApGroupRadios().forEach(radio -> radio.setId(null));
    });

    List<NetworkApGroup> result = unit.createNetworkApGroups(
        networkVenue.getNetworkApGroups(), networkVenue, Optional.empty());

    networkApGroups.get(0).getApGroup().setId(apGroup.getId());
    assertNetworkApGroups(networkApGroups, result, vlanPool.getId());
  }

  @Test
  void testCreateNetworkApGroupsForNewApGroup_doNothingIfNoNetworkActivateOnTheVenue(Venue venue)
      throws CommonException {
    var apGroup = new ApGroupGenerator().setVenue(always(venue)).generate();

    var result = unit.createNetworkApGroupsForNewApGroup(apGroup);

    assertThat(result).isEmpty();
  }

  @Test
  void testCreateNetworkApGroupsForNewApGroup_doNothingIfNoAllApGroupsNetworkActivateOnTheVenue(
      Venue venue, @OpenNetwork Network network, @IsAllApGroups(false) NetworkVenue networkVenue)
      throws CommonException {
    var apGroup = new ApGroupGenerator().setVenue(always(venue)).generate();

    var result = unit.createNetworkApGroupsForNewApGroup(apGroup);

    assertThat(result).isEmpty();
  }

  @Test
  void testCreateNetworkApGroupsForNewApGroup(
      Venue venue, @OpenNetwork Network network, @IsAllApGroups(true) NetworkVenue networkVenue)
      throws CommonException {
    var apGroup = new ApGroupGenerator().setVenue(always(venue)).generate();

    var result = unit.createNetworkApGroupsForNewApGroup(apGroup);

    assertThat(result)
        .hasSize(1)
        .singleElement()
        .extracting(NetworkApGroup::getApGroup)
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(apGroup.getId());
  }

  @Test
  void testUpdateNetworkApGroupSettings_CreateNewRadios(@OpenNetwork Network network, Venue venue,
      ApGroup apGroup, VlanPool vlanPool, NetworkVenue networkVenue)
      throws ObjectNotFoundException {
    final var targetNetworkApGroup = createApGroupWithRadio(apGroup, vlanPool, networkVenue,
        StrictRadioTypeEnum._2_4_GHz);

    final var networkApGroup = new NetworkApGroupGenerator()
        .setApGroup(always(apGroup))
        .setNetworkVenue(always(networkVenue))
        .generate();
    networkApGroup.setNetworkApGroupRadios(
        List.of(new NetworkApGroupRadioGenerator().setRadio(always(StrictRadioTypeEnum._2_4_GHz))
                .generate(),
            new NetworkApGroupRadioGenerator().setRadio(always(StrictRadioTypeEnum._5_GHz))
                .generate()));
    networkApGroup.getNetworkApGroupRadios().forEach(e -> e.setId(null));

    final var result = unit.updateNetworkApGroupSettings(networkApGroup, targetNetworkApGroup);

    assertThat(result.getNetworkApGroupRadios())
        .hasSize(2);
    assertThat(result.getNetworkApGroupRadios())
        .extracting(NetworkApGroupRadio::getRadio)
        .containsOnly(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz);
    assertThat(result.getNetworkApGroupRadios())
        .extracting(NetworkApGroupRadio::getVlanPool)
        .extracting(AbstractBaseEntity::getId)
        .containsOnly(vlanPool.getId());
  }

  @Test
  void testUpdateNetworkApGroupSettings_UpdateRadios(@OpenNetwork Network network, Venue venue,
      ApGroup apGroup, VlanPool vlanPool, NetworkVenue networkVenue)
      throws ObjectNotFoundException {
    final var targetNetworkApGroup = createApGroupWithRadio(apGroup, vlanPool, networkVenue,
        StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz);

    final var networkApGroup = new NetworkApGroupGenerator()
        .setApGroup(always(apGroup))
        .setNetworkVenue(always(networkVenue))
        .generate();
    networkApGroup.setNetworkApGroupRadios(
        List.of(new NetworkApGroupRadioGenerator().setRadio(always(StrictRadioTypeEnum._2_4_GHz))
                .generate(),
            new NetworkApGroupRadioGenerator().setRadio(always(StrictRadioTypeEnum._5_GHz))
                .generate()));
    networkApGroup.getNetworkApGroupRadios().forEach(e -> e.setId(null));

    final var result = unit.updateNetworkApGroupSettings(networkApGroup, targetNetworkApGroup);

    assertThat(result.getNetworkApGroupRadios())
        .hasSize(2);
    assertThat(result.getNetworkApGroupRadios())
        .extracting(NetworkApGroupRadio::getRadio)
        .containsOnly(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz);
    assertThat(result.getNetworkApGroupRadios())
        .extracting(NetworkApGroupRadio::getVlanPool)
        .extracting(AbstractBaseEntity::getId)
        .containsOnly(vlanPool.getId());
  }

  @Test
  void testUpdateNetworkApGroupSettings_UpdateRadiosWithVlanId_ShouldDisableVlanPool(
      @OpenNetwork Network network, Venue venue, ApGroup apGroup, VlanPool vlanPool,
      NetworkVenue networkVenue) throws ObjectNotFoundException {
    final var targetNetworkApGroup = createApGroupWithRadio(apGroup, vlanPool, networkVenue,
        StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz);

    final var networkApGroup = new NetworkApGroupGenerator()
        .setApGroup(always(apGroup))
        .setNetworkVenue(always(networkVenue))
        .generate();
    networkApGroup.setNetworkApGroupRadios(
        List.of(new NetworkApGroupRadioGenerator().setRadio(always(StrictRadioTypeEnum._2_4_GHz))
                .setVlanId(always((short) 10))
                .generate(),
            new NetworkApGroupRadioGenerator().setRadio(always(StrictRadioTypeEnum._5_GHz))
                .setVlanId(always((short) 10))
                .generate()));
    networkApGroup.getNetworkApGroupRadios().forEach(e -> e.setId(null));

    final var result = unit.updateNetworkApGroupSettings(networkApGroup, targetNetworkApGroup);

    assertThat(result.getNetworkApGroupRadios())
        .hasSize(2)
        .extracting(NetworkApGroupRadio::getVlanId)
        .containsOnly((short) 10);
    assertThat(result.getNetworkApGroupRadios())
        .extracting(NetworkApGroupRadio::getRadio)
        .containsOnly(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz);
    assertThat(result.getNetworkApGroupRadios())
        .extracting(NetworkApGroupRadio::getVlanPool)
        .allMatch(e -> e == null);
  }

  @Test
  void testUpdateNetworkApGroupSettings_RemoveRadios(@OpenNetwork Network network, Venue venue,
      ApGroup apGroup, VlanPool vlanPool,
      NetworkVenue networkVenue) throws ObjectNotFoundException {
    final var targetNetworkApGroup = createApGroupWithRadio(apGroup, vlanPool, networkVenue,
        StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz);

    final var networkApGroup = new NetworkApGroupGenerator()
        .setApGroup(always(apGroup))
        .setNetworkVenue(always(networkVenue))
        .generate();
    networkApGroup.setNetworkApGroupRadios(
        List.of(new NetworkApGroupRadioGenerator().setRadio(always(StrictRadioTypeEnum._2_4_GHz))
            .generate()));
    networkApGroup.getNetworkApGroupRadios().forEach(e -> e.setId(null));

    final var result = unit.updateNetworkApGroupSettings(networkApGroup, targetNetworkApGroup);

    assertThat(result.getNetworkApGroupRadios())
        .hasSize(1);
    assertThat(result.getNetworkApGroupRadios())
        .extracting(NetworkApGroupRadio::getRadio)
        .containsOnly(StrictRadioTypeEnum._2_4_GHz);
    assertThat(result.getNetworkApGroupRadios())
        .extracting(NetworkApGroupRadio::getVlanPool)
        .extracting(AbstractBaseEntity::getId)
        .containsOnly(vlanPool.getId());
  }

  private NetworkApGroup createApGroupWithRadio(ApGroup apGroup, VlanPool vlanPool,
      NetworkVenue networkVenue, StrictRadioTypeEnum... radioTypes) {
    var networkApGroup = new NetworkApGroupGenerator()
        .setApGroup(always(apGroup))
        .setNetworkVenue(always(networkVenue)).generate();
    networkApGroup = repositoryUtil.createOrUpdate(networkApGroup, txCtxExtension.getTenantId(),
        randomTxId());

    final var networkApGroupRadios = new ArrayList<NetworkApGroupRadio>();
    for (var radioType : radioTypes) {
      networkApGroupRadios.add(repositoryUtil.createOrUpdate(
          new NetworkApGroupRadioGenerator()
              .setRadio(always(radioType))
              .setNetworkApGroup(always(networkApGroup))
              .setVlanPool(always(vlanPool)).generate(), txCtxExtension.getTenantId(),
          randomTxId()));
    }

    networkApGroup.setNetworkApGroupRadios(networkApGroupRadios);

    return networkApGroup;
  }

  void assertNetworkApGroups(List<NetworkApGroup> expected, List<NetworkApGroup> actual,
      String expectedVlanPoolId) {
    assertThat(actual).hasSize(expected.size());
    for (int i = 0; i < actual.size(); i++) {
      assertNetworkApGroup(expected.get(i), actual.get(i), expectedVlanPoolId);
    }
  }

  void assertNetworkApGroup(NetworkApGroup expected, NetworkApGroup actual,
      String expectedVlanPoolId) {
    assertThat(actual)
        .satisfies(e -> assertSoftly(softly -> {
          softly.assertThat(e.getApGroup().getId()).isEqualTo(expected.getApGroup().getId());
          softly.assertThat(e.getNetworkVenue().getNetwork().getId())
              .isEqualTo(expected.getNetworkVenue().getNetwork().getId());
          softly.assertThat(e.getNetworkVenue().getVenue().getId())
              .isEqualTo(expected.getNetworkVenue().getVenue().getId());
          softly.assertThat(e.getNetworkVenue().getVlanPoolId())
              .isEqualTo(expected.getNetworkVenue().getVlanPoolId());
        }));
    assertNetworkApGroupRadios(expected.getNetworkApGroupRadios(),
        actual.getNetworkApGroupRadios(), expectedVlanPoolId);
  }

  void assertNetworkApGroupRadios(List<NetworkApGroupRadio> expected,
      List<NetworkApGroupRadio> actual, String expectedVlanPoolId) {
    assertThat(actual).hasSize(expected.size());
    for (int i = 0; i < actual.size(); i++) {
      assertThat(actual.get(i).getRadio()).isEqualTo(expected.get(i).getRadio());
      assertThat(actual.get(i).getVlanId()).isEqualTo(expected.get(i).getVlanId());
      assertThat(actual.get(i).getVlanPool().getId()).isEqualTo(
          expectedVlanPoolId);
    }
  }

  @TestConfiguration
  @Import(InitVenueServiceImplTestConfig.class)
  static class TestConfig {

    @Bean
    public NetworkApGroupService networkApGroupService(
        NetworkApGroupRepository networkApGroupRepository,
        NetworkApGroupRadioRepository networkApGroupRadioRepository,
        ExtendedVlanPoolServiceCtrl extendedVlanPoolServiceCtrl,
        NetworkVenueRepository networkVenueRepository) {
      return new NetworkApGroupServiceImpl(networkApGroupRepository, networkApGroupRadioRepository,
          networkVenueRepository, extendedVlanPoolServiceCtrl, new NetworkApGroupMergerImpl());
    }
  }
}
