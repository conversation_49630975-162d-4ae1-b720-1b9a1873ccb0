package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest(showSql = false)
class TenantFirmwareVersionRepositoryTest {

  @Autowired
  private TenantFirmwareVersionRepository repository;

  @Sql(statements = """
      INSERT INTO tenant(id) VALUES ('8bfeae74-63fb-4269-a5a0-a8e9b8189f07');
      INSERT INTO ap_version(id) VALUES ('6.2.0.103.1234');
      INSERT INTO tenant_firmware_version(id, branch_type, latest_firmware_version, tenant) 
      VALUES('d969f2fc-f813-4c92-8af4-a900461a55fb', 'eol-ap-2022-12', '6.2.0.103.1234', '8bfeae74-63fb-4269-a5a0-a8e9b8189f07');
      """)
  @Test
  public void findByTenantId() {
    Assertions.assertThat(repository.findByTenantId("8bfeae74-63fb-4269-a5a0-a8e9b8189f07"))
        .isNotNull()
        .singleElement()
        .extracting(e -> e.getLatestFirmwareVersion())
        .extracting(v -> v.getId())
        .isEqualTo("6.2.0.103.1234");
  }

  @Test
  public void findEmptyByTenantId() {
    Assertions.assertThat(repository.findByTenantId("8bfeae74-63fb-4269-a5a0-a8e9b8189f07"))
        .isNotNull()
        .hasSize(0);
  }
}