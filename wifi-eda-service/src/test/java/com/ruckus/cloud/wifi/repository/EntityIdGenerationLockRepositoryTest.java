package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.service.EntityIdGenerationLockService.BatchEntityLockEnum;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiJpaDataTest
class EntityIdGenerationLockRepositoryTest {

  private static final String TENANT_ID = randomId();

  @Autowired
  private EntityIdGenerationLockRepository repository;

  @Test
  void getAvailableApWlanIdInNetworkVenueTest() {
    int min = BatchEntityLockEnum.NETWORK_VENUE_AP_WLAN_ID.getMin();
    int max = BatchEntityLockEnum.NETWORK_VENUE_AP_WLAN_ID.getMax();
    Optional<Integer> value = repository.getAvailableApWlanIdInNetworkVenue(TENANT_ID, min, max);
    assertThat(value).isPresent()
        .hasValue(1);
  }

  @Test
  void getAvailableApWlanIdInNetworkVenueWithAmountTest() {
    int min = BatchEntityLockEnum.NETWORK_VENUE_AP_WLAN_ID.getMin();
    int max = BatchEntityLockEnum.NETWORK_VENUE_AP_WLAN_ID.getMax();
    List<Integer> value = repository.getAvailableApWlanIdInNetworkVenue(TENANT_ID, min, max, 5);
    assertThat(value).isNotNull()
        .hasSize(5)
        .containsExactly(1, 2, 3, 4, 5);
  }

  @Test
  void getAvailableApWlanIdInNetworkVenueTemplateTest() {
    int min = BatchEntityLockEnum.NETWORK_VENUE_TEMPLATE_AP_WLAN_ID.getMin();
    int max = BatchEntityLockEnum.NETWORK_VENUE_TEMPLATE_AP_WLAN_ID.getMax();
    Optional<Integer> value = repository.getAvailableApWlanIdInNetworkVenueTemplate(TENANT_ID, min, max);
    assertThat(value).isPresent()
        .hasValue(60_001);
  }

  @Test
  void getAvailableApWlanIdInNetworkVenueTemplateWithAmountTest() {
    int min = BatchEntityLockEnum.NETWORK_VENUE_TEMPLATE_AP_WLAN_ID.getMin();
    int max = BatchEntityLockEnum.NETWORK_VENUE_TEMPLATE_AP_WLAN_ID.getMax();
    List<Integer> value = repository.getAvailableApWlanIdInNetworkVenueTemplate(TENANT_ID, min, max, 5);
    assertThat(value).isNotNull()
        .hasSize(5)
        .containsExactly(60_001, 60_002, 60_003, 60_004, 60_005);
  }

  @Test
  void getAvailableApplicationIdInApplicationPolicyRuleTest() {
    int min = BatchEntityLockEnum.APPLICATION_POLICY_RULE_APPLICATION_ID.getMin();
    int max = BatchEntityLockEnum.APPLICATION_POLICY_RULE_APPLICATION_ID.getMax();
    Optional<Integer> value = repository.getAvailableApplicationIdInApplicationPolicyRule(TENANT_ID, min, max);
    assertThat(value).isPresent()
        .hasValue(1);
  }

  @Test
  void getAvailableApplicationIdInApplicationPolicyRuleWithAmountTest() {
    int min = BatchEntityLockEnum.APPLICATION_POLICY_RULE_APPLICATION_ID.getMin();
    int max = BatchEntityLockEnum.APPLICATION_POLICY_RULE_APPLICATION_ID.getMax();
    List<Integer> value = repository.getAvailableApplicationIdInApplicationPolicyRule(TENANT_ID, min, max, 5);
    assertThat(value).isNotNull()
        .hasSize(5)
        .containsExactly(1, 2, 3, 4, 5);
  }

  @Test
  void getAvailableApLanPortIdInApLanPortProfileTest() {
    int min = BatchEntityLockEnum.AP_LAN_PORT_PROFILE_AP_LAN_PORT_ID.getMin();
    int max = BatchEntityLockEnum.AP_LAN_PORT_PROFILE_AP_LAN_PORT_ID.getMax();
    Optional<Integer> value = repository.getAvailableApLanPortIdInApLanPortProfile(TENANT_ID, min, max);
    assertThat(value).isPresent()
        .hasValue(3);
  }

  @Test
  void getAvailableApLanPortIdInApLanPortProfileWithAmountTest() {
    int min = BatchEntityLockEnum.AP_LAN_PORT_PROFILE_AP_LAN_PORT_ID.getMin();
    int max = BatchEntityLockEnum.AP_LAN_PORT_PROFILE_AP_LAN_PORT_ID.getMax();
    List<Integer> value = repository.getAvailableApLanPortIdInApLanPortProfile(TENANT_ID, min, max, 5);
    assertThat(value).isNotNull()
        .hasSize(5)
        .containsExactly(3, 4, 5, 6, 7);
  }

  @Test
  void getAvailableApCfgIndexInAccessControlProfileTest() {
    int min = BatchEntityLockEnum.ACCESS_CONTROL_PROFILE_AP_CFG_INDEX.getMin();
    int max = BatchEntityLockEnum.ACCESS_CONTROL_PROFILE_AP_CFG_INDEX.getMax();
    Optional<Integer> value = repository.getAvailableApCfgIndexInAccessControlProfile(TENANT_ID, min, max);
    assertThat(value).isPresent()
        .hasValue(1);
  }

  @Test
  void getAvailableApCfgIndexInAccessControlProfileWithAmountTest() {
    int min = BatchEntityLockEnum.ACCESS_CONTROL_PROFILE_AP_CFG_INDEX.getMin();
    int max = BatchEntityLockEnum.ACCESS_CONTROL_PROFILE_AP_CFG_INDEX.getMax();
    List<Integer> value = repository.getAvailableApCfgIndexInAccessControlProfile(TENANT_ID, min, max, 5);
    assertThat(value).isNotNull()
        .hasSize(5)
        .containsExactly(1, 2, 3, 4, 5);
  }

  @Test
  void getAvailableEthernetPortProfileIdInLanPortAdoptionTest() {
    var min = BatchEntityLockEnum.LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID.getMin();
    var max = BatchEntityLockEnum.LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID.getMax();
    var value = repository.getAvailableEthernetPortProfileIdInLanPortAdoption(TENANT_ID, min, max);
    assertThat(value).isPresent()
        .hasValue(2_000);
  }

  @Test
  void getAvailableEthernetPortProfileIdInLanPortAdoptionWithAmountTest() {
    var min = BatchEntityLockEnum.LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID.getMin();
    var max = BatchEntityLockEnum.LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID.getMax();
    var value = repository.getAvailableEthernetPortProfileIdInLanPortAdoption(TENANT_ID, min, max, 5);
    assertThat(value).isNotNull()
        .hasSize(5)
        .containsExactly(2_000, 2_001, 2_002, 2_003, 2_004);
  }

  @Test
  void getAvailableEthernetPortProfileIdInLanPortAdoptionWithMaxAmountTest() {
    var min = BatchEntityLockEnum.LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID.getMin();
    var max = BatchEntityLockEnum.LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID.getMax();
    var expectedSize = (max - min) + 1;
    var value = repository.getAvailableEthernetPortProfileIdInLanPortAdoption(TENANT_ID, min, max, max);
    assertThat(value).isNotNull()
        .hasSize(expectedSize);
  }
}
