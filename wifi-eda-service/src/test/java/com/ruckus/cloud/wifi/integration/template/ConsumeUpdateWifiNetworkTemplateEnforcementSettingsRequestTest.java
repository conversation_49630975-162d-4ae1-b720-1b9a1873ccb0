package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.REQUEST_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_ADMIN_NAME;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_DELEGATION_SOURCE_TENANT_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_TENANT_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;

import com.google.common.collect.Maps;
import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateEnforcementSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.ActivityCfgChangeMessageCaptor;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.*;
import lombok.extern.slf4j.Slf4j;
import lombok.SneakyThrows;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ExecutionException;

@WifiIntegrationTest
@Slf4j
public class ConsumeUpdateWifiNetworkTemplateEnforcementSettingsRequestTest extends
    AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;
  private final String ERROR_MSG = "This Network is enforced by its Template, and this action [%s] is not allowed";
  private final String ERROR_MSG_REL = "This relation of Network is enforced by its Template, and this action [%s] is not allowed";

  @Nested
  class whenConsumeUpdateWifiNetworkTemplateEnforcementSettingsRequest {
    private String mspTenantId;
    private String ecTenantId;
    private final String wifiNetworkTemplateId = "template-1";
    private final String wifiNetworkInstanceId = "instance-1";

    @BeforeEach
    void givenNetworkTemplateAndNetworkInstanceInDb(final Tenant tenant) {
      mspTenantId = tenant.getId();

      Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
      ecTenantId = ecTenant.getId();
      repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());

      // Given
      OpenNetwork networkTemplate = network(OpenNetwork.class)
          .setWlan(Generators.wlan().setAdvancedCustomization(Generators.wlanAdvancedCustomization()))
          .generate();
      networkTemplate.getWlan().setNetwork(networkTemplate);
      networkTemplate.setId(wifiNetworkTemplateId);
      networkTemplate.setIsTemplate(true);
      repositoryUtil.createOrUpdate(networkTemplate, mspTenantId, randomTxId());

      OpenNetwork networkInstance = network(OpenNetwork.class)
          .setWlan(Generators.wlan().setAdvancedCustomization(Generators.wlanAdvancedCustomization()))
          .generate();
      networkInstance.getWlan().setNetwork(networkInstance);
      networkInstance.setId(wifiNetworkInstanceId);
      networkInstance.setIsTemplate(false);
      networkInstance.setTemplateId(wifiNetworkTemplateId);
      repositoryUtil.createOrUpdate(networkInstance, ecTenantId, randomTxId());
    }

    private TemplateEnforcementSettings templateEnforcementSettings() {
      TemplateEnforcementSettings templateEnforcementSettings = new TemplateEnforcementSettings();
      templateEnforcementSettings.setIsEnforced(true);
      return templateEnforcementSettings;
    }

    @SneakyThrows
    @Test
    public void shouldConsumeUpdateWifiNetworkTemplateEnforcementSettingsRequest() {
      var requestId = randomTxId();
      var userName = randomName();
      var wifiCfgRequest = WifiCfgRequest.builder()
              .tenantId(mspTenantId)
              .requestId(requestId)
              .apiAction(CfgAction.UPDATE_WIFI_NETWORK_TEMPLATE_ENFORCEMENT_SETTINGS)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), mspTenantId)
              .addHeader(REQUEST_ID.getName(), requestId)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId))
              .payload(templateEnforcementSettings()).build();
      messageUtil.sendWifiCfgRequest(wifiCfgRequest);

      validateActivityMessages(mspTenantId, requestId,
              ApiFlowNames.UPDATE_WIFI_NETWORK_TEMPLATE_ENFORCEMENT_SETTINGS + "InWifi", Status.OK);
      validateWifiCfgChangeMessage(mspTenantId, requestId);
      validateCmnCfgCollectorMessage(mspTenantId, requestId);
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenants(mspTenantId, ecTenantId);
      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenants(mspTenantId, ecTenantId);

      Network foundTemplate = repositoryUtil.find(Network.class, wifiNetworkTemplateId);
      assertTrue(foundTemplate.getIsEnforced());
      Network foundInstance = repositoryUtil.find(Network.class, wifiNetworkInstanceId);
      assertEquals(wifiNetworkTemplateId, foundInstance.getTemplateId());
      assertTrue(foundInstance.getIsEnforced());

      messageUtil.clearMessage();

      var requestIdDelete = randomTxId();
      var wifiCfgRequestDelete = WifiCfgRequest.builder()
          .tenantId(mspTenantId)
          .requestId(requestIdDelete)
          .apiAction(CfgAction.DELETE_WIFI_NETWORK_TEMPLATE)
          .requestHeaders(Maps.newHashMap())
          .addHeader(RKS_TENANT_ID.getName(), mspTenantId)
          .addHeader(REQUEST_ID.getName(), requestIdDelete)
          .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
          .addHeader(RKS_IDM_USER_ID.getName(), userName)
          .requestParams(new RequestParams().addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId))
          .payload(null).build();

      messageUtil.sendWifiCfgRequest(wifiCfgRequestDelete);

      validateActivityMessages(mspTenantId, requestIdDelete, ApiFlowNames.DELETE_WIFI_NETWORK_TEMPLATE, Status.OK);

      foundTemplate = repositoryUtil.find(Network.class, wifiNetworkTemplateId);
      assertNull(foundTemplate);
      foundInstance = repositoryUtil.find(Network.class, wifiNetworkInstanceId);
      assertEquals(wifiNetworkTemplateId, foundInstance.getTemplateId());
      assertFalse(foundInstance.getIsEnforced());
    }

    private void validateWifiCfgChangeMessage(String tenantId, String requestId) {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(requestId);
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();

      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(requestId));

      assertThat(wifiConfigChange.getOperationList())
          .hasSize(2)
          .filteredOn(o -> o.hasOpenNetwork())
          .hasSize(2)
          .extracting(o -> o.getOpenNetwork().getId().getValue())
          .containsExactlyInAnyOrder(wifiNetworkTemplateId, wifiNetworkInstanceId);
    }

    private void validateCmnCfgCollectorMessage(String tenantId, String requestId) {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(requestId));

      assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
          .hasSize(2)
          .filteredOn(o -> o.getId().equals(wifiNetworkTemplateId)).first()
          .matches(o -> EsConstants.Index.NETWORK.equals(o.getIndex()))
          .matches(o -> o.getOpType().equals(OpType.MOD))
          .matches(o -> o.getDocMap().get(Key.TENANT_ID).getStringValue().equals(mspTenantId))
          .matches(o -> Boolean.TRUE.equals(o.getDocMap().get(Key.IS_TEMPLATE).getBoolValue()))
          .matches(o -> Boolean.TRUE.equals(o.getDocMap().get(Key.IS_ENFORCED).getBoolValue()));

      assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
          .hasSize(2)
          .filteredOn(o -> o.getId().equals(wifiNetworkInstanceId)).first()
          .matches(o -> o.getIndex().equals(EsConstants.Index.NETWORK))
          .matches(o -> o.getOpType().equals(OpType.MOD))
          .matches(o -> o.getDocMap().get(Key.TENANT_ID).getStringValue().equals(ecTenantId))
          .matches(o -> Boolean.FALSE.equals(o.getDocMap().get(Key.IS_TEMPLATE).getBoolValue()))
          .matches(o -> Boolean.TRUE.equals(o.getDocMap().get(Key.IS_ENFORCED).getBoolValue()));
    }
  }

  public void validateActivityMessages(String tenantId, String requestId, String expectedStep, Status expectedStatus) throws InvalidProtocolBufferException {
    ActivityCfgChangeMessageCaptor captor = messageCaptors.getActivityCfgChangeMessageCaptor();
    final var activityCfgChangeRespMessage = captor
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
            .isNotNull()
            .extracting(KafkaProtoMessage::getHeaders)
            .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
            .isNotNull()
            .extracting(Header::value)
            .extracting(String::new)
            .isEqualTo(tenantId);
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(p -> p.getStatus().equals(expectedStatus))
            .matches(p -> p.getStep().equals(expectedStep))
            .extracting(ConfigurationStatus::getEventDate)
            .isNotNull();
  }

  @Nested
  class ConsumeUpdateWifiNetworkRequestWhichIsEnforced {
    private final String RADIUS_ID = "radiusId";
    private final String WIFI_NETWORK_ID = "wifiNetworkId";

    private String mspTenantId;
    private String ecTenantId;
    private final String wifiNetworkTemplateId = "template-1";
    private final String wifiNetworkInstanceId = "instance-1";
    private String authRadiusId;

    @BeforeEach
    void givenNetworkTemplateAndNetworkInstanceInDb(final Tenant tenant) {
      mspTenantId = tenant.getId();

      Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
      ecTenantId = ecTenant.getId();
      repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());

      // Given
      AAANetwork networkTemplate = network(AAANetwork.class)
          .setWlan(Generators.wlan().setAdvancedCustomization(Generators.wlanAdvancedCustomization()))
          .generate();
      networkTemplate.getWlan().setNetwork(networkTemplate);
      networkTemplate.setId(wifiNetworkTemplateId);
      networkTemplate.setIsTemplate(true);
      networkTemplate.setIsEnforced(true);
      repositoryUtil.createOrUpdate(networkTemplate, mspTenantId, randomTxId());

      AAANetwork networkInstance = network(AAANetwork.class)
          .setWlan(Generators.wlan().setAdvancedCustomization(Generators.wlanAdvancedCustomization()))
          .generate();
      networkInstance.getWlan().setNetwork(networkInstance);
      networkInstance.setId(wifiNetworkInstanceId);
      networkInstance.setIsTemplate(false);
      networkInstance.setTemplateId(wifiNetworkTemplateId);
      networkInstance.setIsEnforced(true);
      repositoryUtil.createOrUpdate(networkInstance, ecTenantId, randomTxId());

      Radius authRadius = Generators.radiusProfile()
              .setId(defaultIdGenerator())
              .setType(always(RadiusProfileTypeEnum.AUTHENTICATION))
              .generate();
      authRadiusId = authRadius.getId();
      repositoryUtil.createOrUpdate(authRadius, ecTenantId, randomTxId());
    }

    @Test
    void updateWifiNetwork() throws InvalidProtocolBufferException, ExecutionException, InterruptedException {
      var savedNetwork = repositoryUtil.find(AAANetwork.class, wifiNetworkInstanceId);
      assertNotNull(savedNetwork);
      assertNull(savedNetwork.getAuthRadius());

      messageUtil.clearMessage();

      // FROM_EC_DIRECTLY
      // update WifiNetwork
      var aaaWifiNetwork = com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.aaaWifiNetwork().generate();
      var requestId = randomTxId();
      log.warn("[1] [{}] ----------", requestId);
      var userName = randomName();
      var wifiCfgRequest1 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId)
              .apiAction(CfgAction.UPDATE_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(REQUEST_ID.getName(), requestId)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload(aaaWifiNetwork).build();
      // should fail to update WifiNetwork when it's enforced
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequest1)).isNotNull()
              .getRootCause().isInstanceOf(CommonException.class).hasMessage(String.format(ERROR_MSG, EntityAction.MODIFY));
      validateActivityMessages(ecTenantId, requestId, ApiFlowNames.UPDATE_WIFI_NETWORK, Status.FAIL);
      messageUtil.clearMessage();

      // FROM_EC_DIRECTLY
      // change Radius binding
      var requestId2 = randomTxId();
      log.warn("[2] [{}] ----------", requestId2);
      var wifiCfgRequest2 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId2)
              .apiAction(CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(REQUEST_ID.getName(), requestId2)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams()
                      .addPathVariable(RADIUS_ID, authRadiusId)
                      .addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload("").build();
      // should fail to change WifiNetwork -> Radius binding when it's enforced
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequest2)).isNotNull()
              .getRootCause().isInstanceOf(CommonException.class).hasMessage(String.format(ERROR_MSG, EntityAction.MODIFY));
      validateActivityMessages(ecTenantId, requestId2, ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK, Status.FAIL);
      messageUtil.clearMessage();

      savedNetwork = repositoryUtil.find(AAANetwork.class, wifiNetworkInstanceId);
      assertNotNull(savedNetwork);
      assertNull(savedNetwork.getAuthRadius());

      // FROM_TEMPLATE_ORIGIN
      // change Radius binding
      var requestId3 = randomTxId();
      log.warn("[3] [{}] ----------", requestId3);
      var wifiCfgRequest3 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId3)
              .apiAction(CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), mspTenantId)
              .addHeader(REQUEST_ID.getName(), requestId3)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams()
                      .addPathVariable(RADIUS_ID, authRadiusId)
                      .addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload("").build();
      // should be able to change WifiNetwork -> Radius binding since it's from MSP tenant
      messageUtil.sendWifiCfgRequest(wifiCfgRequest3);
      validateActivityMessages(ecTenantId, requestId3, ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK, Status.OK);
      messageUtil.clearMessage();

      savedNetwork = repositoryUtil.find(AAANetwork.class, wifiNetworkInstanceId);
      assertNotNull(savedNetwork);
      assertEquals(authRadiusId, savedNetwork.getAuthRadius().getId());

      // FROM_TEMPLATE_ORIGIN
      // update WifiNetwork
      var requestId4 = randomTxId();
      log.warn("[4] [{}] ----------", requestId4);
      var wifiCfgRequest4 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId4)
              .apiAction(CfgAction.UPDATE_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), mspTenantId)
              .addHeader(REQUEST_ID.getName(), requestId4)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload(aaaWifiNetwork).build();
      // should be able to update WifiNetwork since it's from MSP tenant
      messageUtil.sendWifiCfgRequest(wifiCfgRequest4);
      validateActivityMessages(ecTenantId, requestId4, ApiFlowNames.UPDATE_WIFI_NETWORK, Status.OK);
      messageUtil.clearMessage();
    }
  }

  @Nested
  class ConsumeDeleteWifiNetworkRequestWhichIsEnforced {
    private final String WIFI_NETWORK_ID = "wifiNetworkId";

    private String mspTenantId;
    private String ecTenantId;
    private final String wifiNetworkTemplateId = "template-1";
    private final String wifiNetworkInstanceId = "instance-1";

    @BeforeEach
    void givenNetworkTemplateAndNetworkInstanceInDb(final Tenant tenant) {
      mspTenantId = tenant.getId();

      Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
      ecTenantId = ecTenant.getId();
      repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());

      // Given
      AAANetwork networkTemplate = network(AAANetwork.class)
              .setWlan(Generators.wlan().setAdvancedCustomization(Generators.wlanAdvancedCustomization()))
              .generate();
      networkTemplate.getWlan().setNetwork(networkTemplate);
      networkTemplate.setId(wifiNetworkTemplateId);
      networkTemplate.setIsTemplate(true);
      networkTemplate.setIsEnforced(true);
      repositoryUtil.createOrUpdate(networkTemplate, mspTenantId, randomTxId());

      AAANetwork networkInstance = network(AAANetwork.class)
              .setWlan(Generators.wlan().setAdvancedCustomization(Generators.wlanAdvancedCustomization()))
              .generate();
      networkInstance.getWlan().setNetwork(networkInstance);
      networkInstance.setId(wifiNetworkInstanceId);
      networkInstance.setIsTemplate(false);
      networkInstance.setTemplateId(wifiNetworkTemplateId);
      networkInstance.setIsEnforced(true);
      repositoryUtil.createOrUpdate(networkInstance, ecTenantId, randomTxId());
    }

    @Test
    void deleteWifiNetwork() throws InvalidProtocolBufferException, ExecutionException, InterruptedException {
      //////////
      // check WifiNetwork isEnforced
      var savedNetwork = repositoryUtil.find(AAANetwork.class, wifiNetworkInstanceId);
      assertNotNull(savedNetwork);
      assertTrue(savedNetwork.getIsEnforced());

      // FROM_EC_DIRECTLY
      // update WifiNetwork
      var requestId = randomTxId();
      var userName = randomName();
      var wifiCfgRequest1 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId)
              .apiAction(CfgAction.DELETE_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(REQUEST_ID.getName(), requestId)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload("").build();
      // should fail to delete WifiNetwork when it's enforced
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequest1)).isNotNull()
              .getRootCause().isInstanceOf(CommonException.class)
              .hasMessage(String.format(ERROR_MSG, EntityAction.DELETE));
      validateActivityMessages(ecTenantId, requestId, ApiFlowNames.DELETE_WIFI_NETWORK, Status.FAIL);
      messageUtil.clearMessage();

      // FROM_TEMPLATE_ORIGIN
      // change Radius binding
      var requestId2 = randomTxId();
      var wifiCfgRequest2 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId2)
              .apiAction(CfgAction.DELETE_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), mspTenantId)
              .addHeader(REQUEST_ID.getName(), requestId2)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams().addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload("").build();
      // should be able to delete WifiNetwork since it's from MSP tenant
      messageUtil.sendWifiCfgRequest(wifiCfgRequest2);
      validateActivityMessages(ecTenantId, requestId2, ApiFlowNames.DELETE_WIFI_NETWORK, Status.OK);
      messageUtil.clearMessage();

      savedNetwork = repositoryUtil.find(AAANetwork.class, wifiNetworkInstanceId);
      assertNull(savedNetwork);
    }
  }

  @Nested
  class ConsumeWifiCallingProfileOnWifiNetworkRequestWhichIsEnforced {
    private final String WIFI_CALLING_ID = "wifiCallingServiceProfileId";
    private final String WIFI_NETWORK_ID = "wifiNetworkId";

    private String mspTenantId;
    private String ecTenantId;
    private final String wifiNetworkTemplateId = "template-1";
    private final String wifiNetworkInstanceId = "instance-1";
    private String wifiCallingId1;

    @BeforeEach
    void givenNetworkTemplateAndNetworkInstanceInDb(final Tenant tenant) {
      mspTenantId = tenant.getId();

      Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
      ecTenantId = ecTenant.getId();
      repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());

      // Given
      AAANetwork networkTemplate = network(AAANetwork.class)
              .setWlan(Generators.wlan().setAdvancedCustomization(Generators.wlanAdvancedCustomization()))
              .generate();
      networkTemplate.getWlan().setNetwork(networkTemplate);
      networkTemplate.setId(wifiNetworkTemplateId);
      networkTemplate.setIsTemplate(true);
      networkTemplate.setIsEnforced(true);
      repositoryUtil.createOrUpdate(networkTemplate, mspTenantId, randomTxId());

      AAANetwork networkInstance = network(AAANetwork.class)
              .setWlan(Generators.wlan().setAdvancedCustomization(Generators.wlanAdvancedCustomization()))
              .generate();
      networkInstance.getWlan().setNetwork(networkInstance);
      networkInstance.setId(wifiNetworkInstanceId);
      networkInstance.setIsTemplate(false);
      networkInstance.setTemplateId(wifiNetworkTemplateId);
      networkInstance.setIsEnforced(true);
      repositoryUtil.createOrUpdate(networkInstance, ecTenantId, randomTxId());

      WifiCallingServiceProfile wifiCallingServiceProfile1 = Generators.wifiCallingServiceProfile().generate();
      wifiCallingServiceProfile1.setEPDGs(Generators.epdg().generate(1));
      wifiCallingId1 = wifiCallingServiceProfile1.getId();
      repositoryUtil.createOrUpdate(wifiCallingServiceProfile1, ecTenantId, randomTxId());
    }

    @Test
    void updateWifiNetworkAndWifiCallingBinding() throws InvalidProtocolBufferException, ExecutionException, InterruptedException {
      var savedWifiCalling = repositoryUtil.find(WifiCallingServiceProfile.class, wifiCallingId1);
      assertNotNull(savedWifiCalling);
      assertNotNull(savedWifiCalling.getWifiCallingServiceProfileNetwork());
      assertEquals(0, savedWifiCalling.getWifiCallingServiceProfileNetwork().size());

      messageUtil.clearMessage();

      // FROM_EC_DIRECTLY
      // change Radius binding
      var requestId1 = randomTxId();
      var userName = randomName();
      var wifiCfgRequest1 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId1)
              .apiAction(CfgAction.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_ON_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(REQUEST_ID.getName(), requestId1)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams()
                      .addPathVariable(WIFI_CALLING_ID, wifiCallingId1)
                      .addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload("").build();
      // should fail to change WifiNetwork -> WifiCalling binding when it's enforced
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequest1)).isNotNull()
              .getRootCause().isInstanceOf(CommonException.class).hasMessage(String.format(ERROR_MSG_REL, EntityAction.ADD));
      validateActivityMessages(ecTenantId, requestId1, ApiFlowNames.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_ON_WIFI_NETWORK, Status.FAIL);
      messageUtil.clearMessage();

      // FROM_TEMPLATE_ORIGIN
      // change WIFI-Calling binding
      var requestId2 = randomTxId();
      var wifiCfgRequest2 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId2)
              .apiAction(CfgAction.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_ON_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), mspTenantId)
              .addHeader(REQUEST_ID.getName(), requestId2)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams()
                      .addPathVariable(WIFI_CALLING_ID, wifiCallingId1)
                      .addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload("").build();
      // should be able to change WifiNetwork -> WifiCalling binding since it's from MSP tenant
      messageUtil.sendWifiCfgRequest(wifiCfgRequest2);
      validateActivityMessages(ecTenantId, requestId2, ApiFlowNames.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_ON_WIFI_NETWORK, Status.OK);
      messageUtil.clearMessage();

      savedWifiCalling = repositoryUtil.find(WifiCallingServiceProfile.class, wifiCallingId1);
      assertNotNull(savedWifiCalling);
      assertNotNull(savedWifiCalling.getWifiCallingServiceProfileNetwork());
      assertEquals(1, savedWifiCalling.getWifiCallingServiceProfileNetwork().size());

      // FROM_EC_DIRECTLY
      // change WifiCalling binding
      var requestId3 = randomTxId();
      var wifiCfgRequest3 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId3)
              .apiAction(CfgAction.DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_ON_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(REQUEST_ID.getName(), requestId3)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams()
                      .addPathVariable(WIFI_CALLING_ID, wifiCallingId1)
                      .addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload("").build();
      // should fail to change WifiNetwork -> WifiCalling binding when it's enforced
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequest3)).isNotNull()
              .getRootCause().isInstanceOf(CommonException.class).hasMessage(String.format(ERROR_MSG_REL, EntityAction.DELETE));
      validateActivityMessages(ecTenantId, requestId3, ApiFlowNames.DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_ON_WIFI_NETWORK, Status.FAIL);
      messageUtil.clearMessage();

      savedWifiCalling = repositoryUtil.find(WifiCallingServiceProfile.class, wifiCallingId1);
      assertNotNull(savedWifiCalling);
      assertNotNull(savedWifiCalling.getWifiCallingServiceProfileNetwork());
      assertEquals(1, savedWifiCalling.getWifiCallingServiceProfileNetwork().size());

      // FROM_TEMPLATE_ORIGIN
      // change WIFI-Calling binding
      var requestId4 = randomTxId();
      var wifiCfgRequest4 = WifiCfgRequest.builder()
              .tenantId(ecTenantId)
              .requestId(requestId4)
              .apiAction(CfgAction.DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_ON_WIFI_NETWORK)
              .requestHeaders(Maps.newHashMap())
              .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
              .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), mspTenantId)
              .addHeader(REQUEST_ID.getName(), requestId4)
              .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
              .addHeader(RKS_IDM_USER_ID.getName(), userName)
              .requestParams(new RequestParams()
                      .addPathVariable(WIFI_CALLING_ID, wifiCallingId1)
                      .addPathVariable(WIFI_NETWORK_ID, wifiNetworkInstanceId))
              .payload("").build();
      // should be able to change WifiNetwork -> WifiCalling binding since it's from MSP tenant
      messageUtil.sendWifiCfgRequest(wifiCfgRequest4);
      validateActivityMessages(ecTenantId, requestId4, ApiFlowNames.DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_ON_WIFI_NETWORK, Status.OK);
      messageUtil.clearMessage();

      savedWifiCalling = repositoryUtil.find(WifiCallingServiceProfile.class, wifiCallingId1);
      assertNotNull(savedWifiCalling);
      assertNotNull(savedWifiCalling.getWifiCallingServiceProfileNetwork());
      assertEquals(0, savedWifiCalling.getWifiCallingServiceProfileNetwork().size());
    }
  }

  @Nested
  class ConsumeAddWifiNetworkByTemplateWithRadiusRequestWhichIsEnforced {
    private final String RADIUS_ID = "radiusId";
    private final String WIFI_NETWORK_ID = "wifiNetworkId";

    private String mspTenantId;
    private String ecTenantId;
    private final String wifiNetworkTemplateId = "template-1";
    private final String wifiNetworkInstanceId = "instance-1";
    private String authRadiusTemplateId;
    private String authRadiusInstanceId;

    @BeforeEach
    void givenNetworkTemplateAndNetworkInstanceInDb(final Tenant tenant) {
      mspTenantId = tenant.getId();

      Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
      ecTenantId = ecTenant.getId();
      repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());

      Radius mspRadius = RadiusTestFixture.authRadius();
      mspRadius.setName("test-radius-1");
      mspRadius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
      mspRadius.setIsTemplate(true);
      repositoryUtil.createOrUpdate(mspRadius, mspTenantId, randomTxId());
      Radius mspRadiusAdded = repositoryUtil.find(Radius.class, mspRadius.getId());
      assertNotNull(mspRadiusAdded);
      authRadiusTemplateId = mspRadiusAdded.getId();

      Radius ecRadius = RadiusTestFixture.authRadius();
      ecRadius.setName("test-radius-1");
      ecRadius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
      ecRadius.setTemplateId(mspRadiusAdded.getId());
      ecRadius.setTemplateVersion(mspRadiusAdded.getUpdatedDate().getTime());
      repositoryUtil.createOrUpdate(ecRadius, ecTenantId, randomTxId());
      Radius ecRadiusAdded = repositoryUtil.find(Radius.class, ecRadius.getId());
      assertNotNull(ecRadiusAdded);
      authRadiusInstanceId = ecRadiusAdded.getId();

      AAANetwork networkTemplate = network(AAANetwork.class)
          .setWlan(Generators.wlan().setAdvancedCustomization(Generators.wlanAdvancedCustomization()))
          .generate();
      networkTemplate.getWlan().setNetwork(networkTemplate);
      networkTemplate.setId(wifiNetworkTemplateId);
      networkTemplate.setIsTemplate(true);
      networkTemplate.setIsEnforced(true);
      networkTemplate.setAuthRadius(mspRadiusAdded);
      repositoryUtil.createOrUpdate(networkTemplate, mspTenantId, randomTxId());
    }

    @Test
    void addWifiNetworkByTemplate() {
      // AddWifiNetworkByTemplate
      String userName = randomName();
      TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

      ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
          ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
          wifiNetworkInstanceId);
      String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

      String mspRequestId = randomTxId();
      RequestParams rps = new RequestParams()
          .addPathVariable(TEMPLATE_ID, wifiNetworkTemplateId)
          .addPathVariable(TARGET_TENANT_ID, ecTenantId);
      rps.addRequestParam(INSTANCE_ID, wifiNetworkInstanceId);
      rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
      sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
          CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
          rps, instanceCreateRequest);

      assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
      assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
      assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
      assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
      assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

      Network networkInstance = repositoryUtil.find(Network.class, wifiNetworkInstanceId);
      assertNotNull(networkInstance);
      assertEquals(wifiNetworkTemplateId, networkInstance.getTemplateId());
      assertNotNull(networkInstance.getAuthRadius());
      assertEquals(authRadiusInstanceId, networkInstance.getAuthRadius().getId());
    }
  }
}
