package com.ruckus.cloud.wifi.requirement.feature;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.SdLanProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class SdLanFeatureTest {

  @MockBean
  private SdLanProfileRegularSettingRepository sdLanProfileRegularSettingRepository;
  @SpyBean
  private SdLanFeature unit;

  @Nested
  class WhenIsSdLanEnabled {

    @Test
    @FeatureFlag(disable = FlagNames.EDGE_COMPATIBILITY_CHECK_TOGGLE)
    void givenFFDisabled(Venue venue) {
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Nested
    @FeatureFlag(enable = FlagNames.EDGE_COMPATIBILITY_CHECK_TOGGLE)
    class GivenFFEnabled {

      @Test
      void givenSdLanProfileRegularSettingNotExist(Venue venue) {
        when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
            eq(TxCtxHolder.tenantId()), eq(venue.getId())))
            .thenReturn(false);

        BDDAssertions.then(unit.test(venue)).isFalse();
      }

      @Test
      void givenSdLanProfileRegularSettingExist(Venue venue) {
        when(sdLanProfileRegularSettingRepository.existsByTenantIdAndVenueId(
            eq(TxCtxHolder.tenantId()), eq(venue.getId())))
            .thenReturn(true);

        BDDAssertions.then(unit.test(venue)).isTrue();
      }
    }
  }
}
