package com.ruckus.cloud.wifi.integration.template;

import com.google.common.collect.Maps;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.L2AclPolicyRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.L2AclPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DECOUPLE_L2ACL_POLICY_INSTANCES;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@WifiIntegrationTest
public class ConsumeDecoupleL2AclPolicyInstancesRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private L2AclPolicyRepository l2AclPolicyRepository;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void decouple_l2AclPolicyInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add L2AclPolicy as a template
    final var l2AclPolicyTemplate = repositoryUtil.createOrUpdate(
        L2AclPolicyTestFixture.randomL2AclPolicy(mspTenant, p -> {
          p.setName("l2acl-template");
          p.setIsTemplate(true);
        }), mspTenant.getId());

    // ec tenant add L2AclPolicy instance from template
    changeTxCtxTenant(ecTenantId);
    var l2AclPolicyInstance = L2AclPolicyTestFixture.randomL2AclPolicy(ecTenant, p -> {
      p.setName("l2acl-instance");
      p.setTemplateId(l2AclPolicyTemplate.getId());
      p.setIsTemplate(false);
      p.setIsEnforced(true);
    });
    l2AclPolicyInstance = repositoryUtil.createOrUpdate(l2AclPolicyInstance, ecTenantId);

    // Verify initial state - instance should have templateId and isEnforced = true
    L2AclPolicy instanceBeforeDecouple = repositoryUtil.find(L2AclPolicy.class,
        l2AclPolicyInstance.getId(), ecTenantId, false);
    assertAll("Verify L2AclPolicy instance before decoupling",
        () -> assertEquals(l2AclPolicyTemplate.getId(), instanceBeforeDecouple.getTemplateId()),
        () -> assertFalse(instanceBeforeDecouple.getIsTemplate()),
        () -> assertTrue(instanceBeforeDecouple.getIsEnforced()));

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_L2ACL_POLICY_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_L2ACL_POLICY_INSTANCES, ecTenantId);

    // Verify L2AclPolicy instance was decoupled
    L2AclPolicy instanceAfterDecouple = repositoryUtil.find(L2AclPolicy.class,
        l2AclPolicyInstance.getId(), ecTenantId, false);
    assertAll("Verify L2AclPolicy instance after decoupling",
        () -> assertNull(instanceAfterDecouple.getTemplateId(), "templateId should be null after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsEnforced(), "isEnforced should be false after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsTemplate(), "isTemplate should remain false"));

    // Verify template is not affected
    changeTxCtxTenant(mspTenant.getId());
    L2AclPolicy templateAfterDecouple = repositoryUtil.find(L2AclPolicy.class,
        l2AclPolicyTemplate.getId(), mspTenant.getId(), true);
    assertAll("Verify template is not affected",
        () -> assertTrue(templateAfterDecouple.getIsTemplate(), "Template should remain as template"),
        () -> assertEquals(l2AclPolicyTemplate.getName(), templateAfterDecouple.getName(),
            "Template name should be unchanged"));

    assertDdccmCfgRequestNotSent(ecTenantId);
    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instanceAfterDecouple.getId());
  }

  @Test
  public void decouple_multipleL2AclPolicyInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add multiple L2AclPolicy templates
    final var template1 = repositoryUtil.createOrUpdate(
        L2AclPolicyTestFixture.randomL2AclPolicy(mspTenant, p -> {
          p.setName("l2acl-template-1");
          p.setIsTemplate(true);
        }), mspTenant.getId());

    final var template2 = repositoryUtil.createOrUpdate(
        L2AclPolicyTestFixture.randomL2AclPolicy(mspTenant, p -> {
          p.setName("l2acl-template-2");
          p.setIsTemplate(true);
        }), mspTenant.getId());

    // ec tenant add multiple L2AclPolicy instances from templates
    changeTxCtxTenant(ecTenantId);
    var instance1 = L2AclPolicyTestFixture.randomL2AclPolicy(ecTenant, p -> {
      p.setName("l2acl-instance-1");
      p.setTemplateId(template1.getId());
      p.setIsTemplate(false);
      p.setIsEnforced(true);
    });
    instance1 = repositoryUtil.createOrUpdate(instance1, ecTenantId);

    var instance2 = L2AclPolicyTestFixture.randomL2AclPolicy(ecTenant, p -> {
      p.setName("l2acl-instance-2");
      p.setTemplateId(template2.getId());
      p.setIsTemplate(false);
      p.setIsEnforced(true);
    });
    instance2 = repositoryUtil.createOrUpdate(instance2, ecTenantId);

    var instance3 = L2AclPolicyTestFixture.randomL2AclPolicy(ecTenant, p -> {
      p.setName("l2acl-instance-3");
      p.setTemplateId(template1.getId());
      p.setIsTemplate(false);
      p.setIsEnforced(true);
    });
    instance3 = repositoryUtil.createOrUpdate(instance3, ecTenantId);

    // Execute decouple operation for all L2AclPolicies
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_L2ACL_POLICY_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_L2ACL_POLICY_INSTANCES, ecTenantId);

    // Verify all L2AclPolicy instances were decoupled
    L2AclPolicy instance1AfterDecouple = repositoryUtil.find(L2AclPolicy.class,
        instance1.getId(), ecTenantId, false);
    L2AclPolicy instance2AfterDecouple = repositoryUtil.find(L2AclPolicy.class,
        instance2.getId(), ecTenantId, false);
    L2AclPolicy instance3AfterDecouple = repositoryUtil.find(L2AclPolicy.class,
        instance3.getId(), ecTenantId, false);

    assertAll("Verify all L2AclPolicy instances were decoupled",
        () -> assertNull(instance1AfterDecouple.getTemplateId(), "Instance 1 templateId should be null"),
        () -> assertFalse(instance1AfterDecouple.getIsEnforced(), "Instance 1 isEnforced should be false"),
        () -> assertNull(instance2AfterDecouple.getTemplateId(), "Instance 2 templateId should be null"),
        () -> assertFalse(instance2AfterDecouple.getIsEnforced(), "Instance 2 isEnforced should be false"),
        () -> assertNull(instance3AfterDecouple.getTemplateId(), "Instance 3 templateId should be null"),
        () -> assertFalse(instance3AfterDecouple.getIsEnforced(), "Instance 3 isEnforced should be false"));

    assertDdccmCfgRequestNotSent(ecTenantId);

    // Validate CmnCfgCollector message contains all three decoupled L2AclPolicy
    // instances
    String requestId = TxCtxHolder.txId();
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(ecTenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(p -> p.getTenantId().equals(ecTenantId))
        .matches(p -> p.getRequestId().equals(requestId));

    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .hasSize(3)
        .extracting(Operations::getId)
        .containsExactlyInAnyOrder(instance1.getId(), instance2.getId(), instance3.getId());

    // Verify each operation in the message
    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .allMatch(o -> Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .allMatch(o -> o.getOpType().equals(OpType.MOD))
        .allMatch(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(ecTenantId))
        .allMatch(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue());
    // .allMatch(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue()); // Uncomment if isEnforced ready

    // Verify templates are not affected
    changeTxCtxTenant(mspTenant.getId());
    L2AclPolicy template1After = repositoryUtil.find(L2AclPolicy.class,
        template1.getId(), mspTenant.getId(), true);
    L2AclPolicy template2After = repositoryUtil.find(L2AclPolicy.class,
        template2.getId(), mspTenant.getId(), true);

    assertAll("Verify templates are not affected",
        () -> assertTrue(template1After.getIsTemplate(), "Template 1 should remain as template"),
        () -> assertTrue(template2After.getIsTemplate(), "Template 2 should remain as template"));
  }

  @Test
  public void decouple_noL2AclPolicyInstances_shouldSucceed(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant with no L2AclPolicy instances
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // Execute decouple operation on tenant with no L2AclPolicies
    changeTxCtxTenant(ecTenantId);
    String requestId = randomTxId();
    sendWifiCfgRequest(ecTenantId, requestId, CfgAction.DECOUPLE_L2ACL_POLICY_INSTANCES, userName,
        new RequestParams(), Maps.newHashMap(), "", false);

    assertActivityStatusSuccess(DECOUPLE_L2ACL_POLICY_INSTANCES, ecTenantId);

    // Should not receive any viewmodel operations since no L2AclPolicies were
    // affected
    assertViewmodelOpsNotSent(ecTenantId);
    assertDdccmCfgRequestNotSent(ecTenantId);
  }

  @Test
  public void decouple_l2AclPolicyInstancesWithoutTemplateId_shouldSucceed(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // ec tenant add L2AclPolicy instance without templateId (standalone instance)
    changeTxCtxTenant(ecTenantId);
    var standaloneInstance = L2AclPolicyTestFixture.randomL2AclPolicy(ecTenant, p -> {
      p.setName("l2acl-standalone");
      p.setTemplateId(null); // No template association
      p.setIsTemplate(false);
      p.setIsEnforced(false);
    });
    standaloneInstance = repositoryUtil.createOrUpdate(standaloneInstance, ecTenantId);

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_L2ACL_POLICY_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_L2ACL_POLICY_INSTANCES, ecTenantId);

    // Verify standalone instance is not affected (no templateId to decouple)
    L2AclPolicy standaloneAfterDecouple = repositoryUtil.find(L2AclPolicy.class,
        standaloneInstance.getId(), ecTenantId, false);
    assertAll("Verify standalone instance is not affected",
        () -> assertNull(standaloneAfterDecouple.getTemplateId(), "templateId should remain null"),
        () -> assertFalse(standaloneAfterDecouple.getIsEnforced(), "isEnforced should remain false"),
        () -> assertFalse(standaloneAfterDecouple.getIsTemplate(), "isTemplate should remain false"));

    assertDdccmCfgRequestNotSent(ecTenantId);
    // No CmnCfgCollector message should be sent since no instances were actually
    // decoupled
    assertViewmodelOpsNotSent(ecTenantId);
  }

  private void assertViewmodelOpsNotSent(String tenantId) {
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }

  private void validateCmnCfgCollectorMessage(String tenantId, String requestId, String instanceId) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(p -> p.getTenantId().equals(tenantId))
        .matches(p -> p.getRequestId().equals(requestId));

    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .hasSize(1)
        .filteredOn(o -> o.getId().equals(instanceId)).first()
        .matches(o -> EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .matches(o -> o.getOpType().equals(OpType.MOD))
        .matches(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(tenantId))
        .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue());
    // .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue())  Uncomment if isEnforced ready
  }
}