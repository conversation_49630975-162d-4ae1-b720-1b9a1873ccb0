package com.ruckus.cloud.wifi.notification;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.notification.dto.VenueTemplateDto;
import com.ruckus.cloud.wifi.notification.template.VenueTemplateStatusEnum;
import com.ruckus.cloud.wifi.servicemodel.enums.ApVersionCategory;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public class TenantFirmwareNotificationUtilTest {

  public static List<ApVersion> mockApVersions(String... versions) {
    List<ApVersion> apVersions = new LinkedList<>();
    for(String version : versions) {
      ApVersion apVersion = new ApVersion(UUID.randomUUID().toString());
      apVersion.setName(version);
      apVersion.setCategory(ApVersionCategory.RECOMMENDED);
      apVersions.add(apVersion);
    }
    return apVersions;
  }

  public static ApVersion mockApVersion(String version) {
    ApVersion apVersion = new ApVersion(UUID.randomUUID().toString());
    apVersion.setName(version);
    apVersion.setCategory(ApVersionCategory.RECOMMENDED);
    return apVersion;
  }

  public static VenueUpgradeVersionsMapping mockVenueUpgradeVersionMapping(int number, ApVersion... apVersions) {
    List<ApVersion> versions = Arrays.asList(apVersions);
    Map<String, List<ApVersion>> mapping = new HashMap<>();
    for(int i=0; i<number; i++) {
      mapping.put(UUID.randomUUID().toString(), versions);
    }

    return VenueUpgradeVersionsMapping.create(mapping);
  }

  public static VenueUpgradeVersionsMapping mockVenueUpgradeVersionMappingByVenueIds(List<String> venueIds, List<ApVersion> apVersions) {
    Map<String, List<ApVersion>> mapping = new HashMap<>();
    venueIds.forEach(id -> mapping.put(id, apVersions));
    return VenueUpgradeVersionsMapping.create(mapping);
  }

  public static List<Venue> mockVenues(int number) {
    List<Venue> venues = new LinkedList<>();
    for(int i=0; i<number; i++) {
      Venue venue = new Venue(UUID.randomUUID().toString());
      venues.add(venue);
    }
    return venues;
  }

  public static Map<String, List<VenueTemplateDto>> mockVenueTemplateMap(int number) {
    Map<String, List<VenueTemplateDto>> mapping = new HashMap<>();
    for(int i=0; i<number; i++) {
      mapping.put(UUID.randomUUID().toString(), mockVenueTemplateDtos(5));
    }
    return mapping;
  }

  public static List<VenueTemplateDto> mockVenueTemplateDtos(int number) {
    List<VenueTemplateDto> dtos = new LinkedList<>();
    for(int i=0; i<number; i++) {
      VenueTemplateDto dto = new VenueTemplateDto();
      dto.setId(UUID.randomUUID().toString());
      dto.setName("mock-name");
      dto.setTimeZone("mock-timezone");
      dto.setAddress("mock-address");
      dto.setNumberOfAps(5);
      dto.setImpactedNumberOfAps(5);
      dto.setSrcVersion("mock-srcVersion");
      dto.setDstVersion("mock-dstVersion");
      dto.setStatus(VenueTemplateStatusEnum.SUCCESS);
      dtos.add(dto);
    }
    return dtos;
  }

  public static Set<String> mockTimezones(int number) {
    Set<String> timezones = new HashSet<>();
    for(int i=0; i<number; i++) {
      timezones.add("mock-timezone" + i);
    }
    return timezones;
  }
}
