package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO venue (id, tenant) VALUES ('b1a2c59f92d243cba7ca5b4bfe83c289', '6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO l2acl_policy (id, name, tenant, mac_addresses, access) VALUES
        ('059796b87b054c359a9828a354aab591', 'l2acl-policy-1', '6700bc51acf84c4aa9510df2ca00b5f4', '["11:22:33:44:55:66"]', 'ALLOW'),
        ('059796b87b054c359a9828a354aab592', 'l2acl-policy-2', '6700bc51acf84c4aa9510df2ca00b5f4', '["11:22:33:44:55:77", "11:22:33:44:55:88"]', 'ALLOW');
    INSERT INTO l2acl_policy (id, name, tenant, mac_addresses, access, is_template) VALUES
        ('l2acl-template-1', 'l2acl-template-1', '6700bc51acf84c4aa9510df2ca00b5f4', '["11:22:33:44:55:99"]', 'ALLOW', true),
        ('l2acl-not-template-1', 'l2acl-not-template-1', '6700bc51acf84c4aa9510df2ca00b5f4', '["11:22:33:44:55:00"]', 'ALLOW', false);
    """)
class L2AclPolicyRepositoryTest {

  private static final String TENANT_ID = "6700bc51acf84c4aa9510df2ca00b5f4";

  @Autowired
  private L2AclPolicyRepository repository;

  @Test
  void existsByTenantIdAndNameAndIdNot() {
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "l2acl-policy-1", "")).isTrue();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "l2acl-policy-2", "")).isTrue();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "l2acl-policy-3", "")).isFalse();
  }

  @Test
  void findByTenantIdAndQueryName() {
    // get l2acl-policy-1 check id
    assertThat(repository.findByTenantIdAndNameLike(TENANT_ID, "l2acl-policy-1", Pageable.unpaged()).getContent())
            .hasSize(1)
            .extracting(L2AclPolicy::getId)
            .singleElement()
            .isEqualTo("059796b87b054c359a9828a354aab591");
    // get l2acl-policy-2 check mac_addresses
    assertThat(repository.findByTenantIdAndNameLike(TENANT_ID, "l2acl-policy-2", Pageable.unpaged()).getContent())
            .hasSize(1)
            .extracting(L2AclPolicy::getMacAddresses)
            .singleElement().asString().contains("11:22:33:44:55:77", "11:22:33:44:55:88");
    // with wildcard mapping
    assertThat(repository.findByTenantIdAndNameLike(TENANT_ID, "%policy%", Pageable.unpaged()).getContent())
            .hasSize(2)
            .extracting(L2AclPolicy::getId)
            .containsAll(List.of("059796b87b054c359a9828a354aab592", "059796b87b054c359a9828a354aab591"));
    // no result
    assertThat(repository.findByTenantIdAndNameLike(TENANT_ID, "l2acl-policy-3", Pageable.unpaged()).getContent()).isEmpty();
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantId() {
    var result = repository.findByIdAndTenantId("l2acl-template-1", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("l2acl-template-1", result.get().getId());
    assertEquals(true, result.get().getIsTemplate());
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantIdWithIsNotTemplateId() {
    var result = repository.findByIdAndTenantId("l2acl-not-template-1", TENANT_ID);

    assertTrue(result.isEmpty());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplate() {
    var result = repository.findByIdAndTenantId("l2acl-not-template-1", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("l2acl-not-template-1", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplateWithLegacyData() {
    var result = repository.findByIdAndTenantId("059796b87b054c359a9828a354aab591", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("059796b87b054c359a9828a354aab591", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }
}
