package com.ruckus.cloud.wifi.service.impl;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.TemplateContext.REC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.utils.TemplateUtils;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.context.ConfigDataApplicationContextInitializer;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ContextConfiguration;

@WifiJpaDataTest
@ContextConfiguration(initializers = ConfigDataApplicationContextInitializer.class)
class WifiNetworkRecTemplateServiceCtrlImplTest {

  @MockBean
  private WifiNetworkTemplateServiceCtrlImpl wifiNetworkTemplateServiceCtrl;

  @SpyBean
  private WifiNetworkRecTemplateServiceCtrlImpl unit;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  private Network wifiNetworkRecTemplate;

  @BeforeEach
  void setUp() {
    // Create a REC template network
    wifiNetworkRecTemplate = NetworkTestFixture.randomNetworkTemplate(TenantTestFixture.randomTenant());
    wifiNetworkRecTemplate.setTemplateContext(REC.name());
  }

  @Test
  void getWifiNetworkRecTemplate_ShouldReturnNetworkWithRecTemplateContext() throws Exception {
    // Given
    String templateId = randomId();
    given(wifiNetworkTemplateServiceCtrl.getWifiNetworkTemplate(templateId))
        .willReturn(wifiNetworkRecTemplate);

    // When
    Network result = unit.getWifiNetworkRecTemplate(templateId);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getIsTemplate()).isTrue();
    assertThat(result.getTemplateContext()).isEqualTo(REC.name());
    
    // Verify that the underlying template service is called
    verify(wifiNetworkTemplateServiceCtrl).getWifiNetworkTemplate(templateId);
  }

  @Test
  void getWifiNetworkRecTemplate_ShouldThrowExceptionWhenTemplateNotFound() throws Exception {
    // Given
    String templateId = randomId();
    given(wifiNetworkTemplateServiceCtrl.getWifiNetworkTemplate(templateId))
        .willThrow(new ObjectNotFoundException("Template not found"));

    // When & Then
    assertThatThrownBy(() -> unit.getWifiNetworkRecTemplate(templateId))
        .isInstanceOf(ObjectNotFoundException.class)
        .hasMessageContaining("Template not found");
  }

  @Test
  void getWifiNetworkRecTemplate_ShouldEnsureRecTemplateContextIsSet() throws Exception {
    // Given - Create a network without template context
    Network networkWithoutContext = NetworkTestFixture.randomNetworkTemplate(TenantTestFixture.randomTenant());
    networkWithoutContext.setTemplateContext(null); // Intentionally set to null
    
    String templateId = randomId();
    given(wifiNetworkTemplateServiceCtrl.getWifiNetworkTemplate(templateId))
        .willReturn(networkWithoutContext);

    // When
    Network result = unit.getWifiNetworkRecTemplate(templateId);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getIsTemplate()).isTrue();
    // Due to the AllowTemplateOperationOnly(isRecTemplate = true) AOP aspect,
    // the template context should be set to REC during method execution
    // However, the Network object's templateContext won't be automatically set because it hasn't gone through database operations
    // We should check the value of TemplateUtils.getTemplateContext() during AOP aspect execution
    // Since the AOP aspect has already finished executing, we cannot check directly, but we can verify the method executes normally
    assertThatNoException().isThrownBy(() -> unit.getWifiNetworkRecTemplate(templateId));
  }

  @Test
  void getWifiNetworkRecTemplate_ShouldSetRecTemplateContextDuringExecution() throws Exception {
    // Given
    String templateId = randomId();
    Network network = NetworkTestFixture.randomNetworkTemplate(TenantTestFixture.randomTenant());
    
    // Use doAnswer to simulate checking templateContext during AOP aspect execution
    given(wifiNetworkTemplateServiceCtrl.getWifiNetworkTemplate(templateId))
        .willAnswer(invocation -> {
          // During AOP aspect execution, templateContext should be set to REC
          String currentContext = TemplateUtils.getTemplateContext();
          assertThat(currentContext).isEqualTo(REC.name());
          return network;
        });

    // When & Then
    Network result = unit.getWifiNetworkRecTemplate(templateId);
    
    // Verify the result
    assertThat(result).isNotNull();
    assertThat(result.getIsTemplate()).isTrue();
  }

  @Test
  void getWifiNetworkRecTemplate_ShouldWorkWithExistingRecTemplateContext() throws Exception {
    // Given - Create a network already set with REC context
    Network networkWithRecContext = NetworkTestFixture.randomNetworkTemplate(TenantTestFixture.randomTenant());
    networkWithRecContext.setTemplateContext(REC.name());
    
    String templateId = randomId();
    given(wifiNetworkTemplateServiceCtrl.getWifiNetworkTemplate(templateId))
        .willReturn(networkWithRecContext);

    // When
    Network result = unit.getWifiNetworkRecTemplate(templateId);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getIsTemplate()).isTrue();
    assertThat(result.getTemplateContext()).isEqualTo(REC.name());
  }

  @Test
  void getWifiNetworkRecTemplate_ShouldPreserveNetworkProperties() throws Exception {
    // Given
    String templateId = randomId();
    String networkName = "Test REC Template";
    wifiNetworkRecTemplate.setName(networkName);
    
    given(wifiNetworkTemplateServiceCtrl.getWifiNetworkTemplate(templateId))
        .willReturn(wifiNetworkRecTemplate);

    // When
    Network result = unit.getWifiNetworkRecTemplate(templateId);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getName()).isEqualTo(networkName);
    assertThat(result.getIsTemplate()).isTrue();
    assertThat(result.getTemplateContext()).isEqualTo(REC.name());
  }

  @Test
  void addWifiNetworkRecTemplate_ShouldThrowUnsupportedOperationException() {
    // When & Then
    assertThatThrownBy(() -> unit.addWifiNetworkRecTemplate(wifiNetworkRecTemplate))
        .isInstanceOf(UnsupportedOperationException.class)
        .hasMessage("Use WifiNetworkServiceCtrl instead");
  }

  @Test
  void updateWifiNetworkRecTemplate_ShouldThrowUnsupportedOperationException() {
    // When & Then
    assertThatThrownBy(() -> unit.updateWifiNetworkRecTemplate("templateId", wifiNetworkRecTemplate))
        .isInstanceOf(UnsupportedOperationException.class)
        .hasMessage("Use WifiNetworkServiceCtrl instead");
  }

  @Test
  void deleteWifiNetworkRecTemplate_ShouldThrowUnsupportedOperationException() {
    // When & Then
    assertThatThrownBy(() -> unit.deleteWifiNetworkRecTemplate("templateId"))
        .isInstanceOf(UnsupportedOperationException.class)
        .hasMessage("Use WifiNetworkServiceCtrl instead");
  }

  @Test
  void updateWifiNetworkRecTemplateEnforcementSettings_ShouldThrowUnsupportedOperationException() {
    // When & Then
    assertThatThrownBy(() -> unit.updateWifiNetworkRecTemplateEnforcementSettings("templateId", null))
        .isInstanceOf(UnsupportedOperationException.class)
        .hasMessage("Use WifiNetworkServiceCtrl instead");
  }
}
