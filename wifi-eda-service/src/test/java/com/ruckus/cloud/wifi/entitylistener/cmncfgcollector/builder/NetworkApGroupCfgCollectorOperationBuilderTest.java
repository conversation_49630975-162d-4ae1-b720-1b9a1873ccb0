package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Operations.Builder;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup.Fields;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.ApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkVenueGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.OpenNetworkGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Tag("ApGroupTest")
@WifiUnitTest
public class NetworkApGroupCfgCollectorOperationBuilderTest {

  @SpyBean
  private NetworkApGroupCmnCfgCollectorOperationBuilder networkApGroupCmnCfgCollectorOperationBuilder;
  @MockBean
  private ApGroupCmnCfgCollectorOperationBuilder apGroupCmnCfgCollectorOperationBuilder;
  @MockBean
  private AbstractNetworkCfgCollectorOperationBuilder abstractNetworkCfgCollectorOperationBuilder;
  @MockBean(name = "OpenNetworkCfgCollectorOperationBuilder")
  private OpenNetworkCfgCollectorOperationBuilder openNetworkCfgCollectorOperationBuilder;
  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  void testGetEntityClass() {
    assertThat(networkApGroupCmnCfgCollectorOperationBuilder.entityClass())
        .isEqualTo(NetworkApGroup.class);
  }

  @Nested
  class testBuildConfig {

    @Test
    public void givenEntityActionIsDelete() {
      Operations operations = networkApGroupCmnCfgCollectorOperationBuilder
          .build(new TxEntity<>(new NetworkApGroup(randomId()), EntityAction.DELETE), emptyTxChanges())
          .get(0);

      assertThat(operations.getDocCount())
          .isZero();
    }

    @Test
    void givenNetworkApGroup() {
      var builder = Operations.newBuilder();
      var tenant = new Tenant(txCtxExtension.getTenantId());
      var venue = VenueTestFixture.randomVenue(tenant);
      venue.setTenant(tenant);
      var network = new PskNetwork();
      network.setId(randomId());
      network.setName(String.format("PSK %s", randomName(4)));
      network.setTenant(tenant);

      final var networkVenue = new NetworkVenue();
      networkVenue.setId(randomId());
      networkVenue.setNetwork(network);
      networkVenue.setVenue(venue);
      networkVenue.setTenant(tenant);
      networkVenue.setApWlanId(123);
      networkVenue.setIsAllApGroups(false);

      final var apGroup = new ApGroup();
      apGroup.setId(randomId());
      apGroup.setName("New AP group");
      apGroup.setVenue(venue);
      apGroup.setTenant(tenant);

      final var networkApGroup = new NetworkApGroup();
      networkApGroup.setId(randomId());
      networkApGroup.setApGroup(apGroup);
      networkApGroup.setNetworkVenue(networkVenue);
      networkApGroup.setCreatedDate(new Date());
      networkApGroup.setUpdatedDate(new Date());
      networkApGroup.setTenant(tenant);

      networkApGroupCmnCfgCollectorOperationBuilder.config(builder, networkApGroup,
          EntityAction.ADD);
      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(Key.ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(networkApGroup.getId());
      assertThat(docMap.get(Key.NETWORK_ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(networkApGroup.getNetworkVenue().getNetwork().getId());
      assertThat(docMap.get(Key.NETWORK_NAME))
          .extracting(p -> p.getStringValue())
          .isEqualTo(networkApGroup.getNetworkVenue().getNetwork().getName());
      assertThat(docMap.get(Key.DEVICE_GROUP_ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(networkApGroup.getApGroup().getId());
      assertThat(docMap.get(Key.DEVICE_GROUP_NAME))
          .extracting(p -> p.getStringValue())
          .isEqualTo(networkApGroup.getApGroup().getName());
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(networkApGroup.getTenant().getId());
      assertThat(docMap.get(Key.IS_ALL_AP_GROUPS))
          .extracting(p -> p.getBoolValue())
          .isEqualTo(networkApGroup.getNetworkVenue().getIsAllApGroups());
    }
  }

  @Nested
  class testPostBuild {

    @BeforeEach
    void mockApBuilder() {
      doAnswer(invocation -> {
        final var builder = (Builder) invocation.getArgument(0);
        final var apGroup = (ApGroup) invocation.getArgument(1);
        builder.setIndex(Index.DEVICE_GROUP)
            .setOpType(OpType.MOD)
            .setId(apGroup.getId());
        return null;
      }).when(apGroupCmnCfgCollectorOperationBuilder)
          .config(any(), any(), any());
      doReturn(Index.DEVICE_GROUP).when(apGroupCmnCfgCollectorOperationBuilder).index();
      doAnswer(invocation -> {
        final var builder = (Builder) invocation.getArgument(0);
        final var network = (Network) invocation.getArgument(1);
        builder.setIndex(Index.NETWORK)
            .setOpType(OpType.MOD)
            .setId(network.getId());
        return null;
      }).when(abstractNetworkCfgCollectorOperationBuilder)
          .config(any(), any(), any());
      doReturn(Index.NETWORK).when(openNetworkCfgCollectorOperationBuilder).index();
    }

    @Test
    void doNothing_WhenApGroupOperationAndNetworkIsAlreadyBuilt() {
      final var txEntity = new TxEntity<>(new NetworkApGroupGenerator()
          .setApGroup(new ApGroupGenerator())
          .setNetworkVenue(new NetworkVenueGenerator().setNetwork(Generators.network(OpenNetwork.class)))
          .generate(), EntityAction.DELETE);
      List<Operations> operations = new ArrayList<>();
      operations.add(Operations.newBuilder()
          .setId(txEntity.getEntity().getApGroup().getId())
          .setIndex(Index.DEVICE_GROUP)
          .build());
      operations.add(Operations.newBuilder()
          .setId(txEntity.getEntity().getNetworkVenue().getNetwork().getId())
          .setIndex(Index.NETWORK)
          .build());

      operations = networkApGroupCmnCfgCollectorOperationBuilder.postBuild(txEntity,
          emptyTxChanges(), operations);

      assertThat(operations)
          .hasSize(2)
          .extracting(Operations::getIndex)
          .contains(Index.DEVICE_GROUP, Index.NETWORK);
    }

    @Test
    void doNothing_WhenNetworkIsNotChanged() {
      final var txEntity = new ModifiedTxEntity<>(new NetworkApGroupGenerator()
          .setApGroup(new ApGroupGenerator())
          .setNetworkVenue(new NetworkVenueGenerator().setNetwork(new OpenNetworkGenerator()))
          .generate(), Set.of(Fields.NETWORKAPGROUPRADIOS));
      List<Operations> operations = new ArrayList<>();
      operations.add(Operations.newBuilder()
          .setId(txEntity.getEntity().getApGroup().getId())
          .setIndex(Index.DEVICE_GROUP)
          .build());
      operations.add(Operations.newBuilder()
          .setId(txEntity.getEntity().getNetworkVenue().getNetwork().getId())
          .setIndex(Index.NETWORK)
          .build());

      operations = networkApGroupCmnCfgCollectorOperationBuilder.postBuild(txEntity,
          emptyTxChanges(), operations);

      assertThat(operations)
          .hasSize(2)
          .extracting(Operations::getIndex)
          .contains(Index.DEVICE_GROUP, Index.NETWORK);
    }

    @Test
    void buildApGroupAndNetworkOperation_WhenNoApGroupAndNoNetworkOperationBuilt() {
      final var txEntity = new TxEntity<>(new NetworkApGroupGenerator()
          .setApGroup(new ApGroupGenerator())
          .setNetworkVenue(new NetworkVenueGenerator().setNetwork(new OpenNetworkGenerator()))
          .generate(), EntityAction.DELETE);
      List<Operations> operations = new ArrayList<>();

      operations = networkApGroupCmnCfgCollectorOperationBuilder.postBuild(txEntity,
          emptyTxChanges(), operations);

      assertThat(operations)
          .hasSize(2)
          .extracting(Operations::getIndex)
          .contains(Index.DEVICE_GROUP, Index.NETWORK);
    }
  }
}
