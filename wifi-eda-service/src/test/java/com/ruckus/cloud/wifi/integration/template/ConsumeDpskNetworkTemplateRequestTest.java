package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.REQUEST_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_ADMIN_NAME;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_TENANT_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.templateString;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DpskNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.service.ExtendedNetworkTemplateServiceCtrl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.redisson.api.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
public class ConsumeDpskNetworkTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedNetworkTemplateServiceCtrl networkTemplateServiceCtrl;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Test
  public void testAddDpskNetworkTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // add DpskNetwork
    com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork dpskNetwork = addDsaeNetwork().generate();
    String requestId = changeTxId(randomTxId());
    WifiCfgRequest wifiCfgRequest =
        WifiCfgRequest.builder()
            .tenantId(txCtxExtension.getTenantId())
            .requestId(requestId)
            .addHeader(RKS_TENANT_ID.getName(), tenantId)
            .addHeader(REQUEST_ID.getName(), requestId)
            .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
            .addHeader(RKS_IDM_USER_ID.getName(), userName)
            .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
            .apiAction(CfgAction.ADD_NETWORK_TEMPLATE)
            .payload(dpskNetwork)
            .build();

    messageUtil.sendWifiCfgRequest(wifiCfgRequest);
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, tenantId);

    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops",
        () -> assertEquals(2, viewOps.size()),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> dpskNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> dpskNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue()))
    );
  }

  @Test
  public void testUpdateDpskNetworkTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // add DpskNetwork
    com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork dpskNetwork = addDsaeNetwork().generate();
    String requestId = changeTxId(randomTxId());
    messageUtil.sendWifiCfgRequest(WifiCfgRequest.builder()
        .tenantId(txCtxExtension.getTenantId())
        .requestId(requestId)
        .addHeader(RKS_TENANT_ID.getName(), tenantId)
        .addHeader(REQUEST_ID.getName(), requestId)
        .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
        .addHeader(RKS_IDM_USER_ID.getName(), userName)
        .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
        .apiAction(CfgAction.ADD_NETWORK_TEMPLATE)
        .payload(dpskNetwork)
        .build());
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    DpskNetwork dpskNetworkAdded = (DpskNetwork) networkTemplateServiceCtrl.getNetworkTemplate(dpskNetwork.getId());

    //////////
    // update DpskNetwork
    WifiCfgRequest wifiCfgRequest =
        WifiCfgRequest.builder()
            .tenantId(txCtxExtension.getTenantId())
            .requestId(requestId)
            .addHeader(RKS_TENANT_ID.getName(), tenantId)
            .addHeader(REQUEST_ID.getName(), requestId)
            .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
            .addHeader(RKS_IDM_USER_ID.getName(), userName)
            .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
            .apiAction(CfgAction.UPDATE_NETWORK_TEMPLATE)
            .requestParams(new RequestParams().addPathVariable("networkTemplateId", dpskNetworkAdded.getId()))
            .payload(updateDsaeNetwork(dpskNetworkAdded.getId()).generate())
            .build();
    messageUtil.sendWifiCfgRequest(wifiCfgRequest);
    assertActivityStatusSuccess(UPDATE_NETWORK_TEMPLATE, tenantId);

    DpskNetwork dpskNetworkUpdated = (DpskNetwork) networkTemplateServiceCtrl.getNetworkTemplate(dpskNetworkAdded.getId());

    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops",
        () -> assertEquals(2, viewOps.size()),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> dpskNetworkUpdated.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.MOD)
            .filter(o -> dpskNetworkUpdated.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue()))
    );
  }

  @Test
  public void testDeleteDpskNetworkTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // add DpskNetwork
    com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork dpskNetwork = addDsaeNetwork().generate();
    String requestId = changeTxId(randomTxId());
    messageUtil.sendWifiCfgRequest(WifiCfgRequest.builder()
        .tenantId(txCtxExtension.getTenantId())
        .requestId(requestId)
        .addHeader(RKS_TENANT_ID.getName(), tenantId)
        .addHeader(REQUEST_ID.getName(), requestId)
        .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
        .addHeader(RKS_IDM_USER_ID.getName(), userName)
        .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
        .apiAction(CfgAction.ADD_NETWORK_TEMPLATE)
        .payload(dpskNetwork)
        .build());
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    DpskNetwork dpskNetworkAdded = (DpskNetwork) networkTemplateServiceCtrl.getNetworkTemplate(dpskNetwork.getId());

    //////////
    // delete DpskNetwork
    WifiCfgRequest wifiCfgRequest =
        WifiCfgRequest.builder()
            .tenantId(txCtxExtension.getTenantId())
            .requestId(requestId)
            .addHeader(RKS_TENANT_ID.getName(), tenantId)
            .addHeader(REQUEST_ID.getName(), requestId)
            .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
            .addHeader(RKS_IDM_USER_ID.getName(), userName)
            .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
            .apiAction(CfgAction.DELETE_NETWORK_TEMPLATE)
            .requestParams(new RequestParams().addPathVariable("networkTemplateId", dpskNetworkAdded.getId()))
            .payload(dpskNetwork)
            .build();
    messageUtil.sendWifiCfgRequest(wifiCfgRequest);
    assertActivityStatusSuccess(DELETE_NETWORK_TEMPLATE, tenantId);

    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops",
        () -> assertEquals(2, viewOps.size()),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> dpskNetworkAdded.getId().equals(o.getId()))
            .anyMatch(o -> o.getOpType() == OpType.DEL))
    );
  }

  private DpskNetworkGenerator addDsaeNetwork() {
    return Generators.dpskNetwork()
        .setName(serialName("AddDsaeNetwork"))
        .setDescription(randomString(64))
        .setDpskServiceProfileId(defaultIdGenerator())
        .setWlan(Generators.dpskWpa23MixedModeWlan());
  }


  private DpskNetworkGenerator updateDsaeNetwork(String networkId) {
    return Generators.dpskNetwork()
        .setId(templateString(networkId))
        .setName(serialName("UpdateDsaeNetwork"))
        .setDescription(randomString(64))
        .setDpskServiceProfileId(defaultIdGenerator())
        .setWlan(Generators.dpskWpa23MixedModeWlan());
  }
}
