package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_IPSEC_PSK_OVER_NETWORK;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.protobuf.MessageLite;
import com.ruckus.cloud.wifi.capabilities.CloudSupportApModels;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceRegistrationPolicyProto.DeviceRegistrationPolicy.Policy;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceRegistrationProto.DeviceRegistration;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RegistrationStateEnum;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmApOperationBuilder;
import com.ruckus.cloud.wifi.kafka.KafkaTransactionHelper;
import com.ruckus.cloud.wifi.kafka.publisher.DdccmPublisher;
import com.ruckus.cloud.wifi.kafka.publisher.DeviceRegistrationPolicyPublisher;
import com.ruckus.cloud.wifi.kafka.publisher.EventPublisher;
import com.ruckus.cloud.wifi.kafka.publisher.WifiAsyncJobPublisher;
import com.ruckus.cloud.wifi.repository.ApModelMinimumFirmwareRepository;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.repository.ClientIsolationAllowlistRepository;
import com.ruckus.cloud.wifi.repository.IpsecProfileRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileRepository;
import com.ruckus.cloud.wifi.repository.TenantAvailableApFirmwareRepository;
import com.ruckus.cloud.wifi.repository.VenueApModelSpecificAttributesRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.ApDeleteService;
import com.ruckus.cloud.wifi.service.ApUpgradeService;
import com.ruckus.cloud.wifi.service.DeviceConfigUpdateNotificationService;
import com.ruckus.cloud.wifi.service.ExtendedApServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.FirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.FirmwareCountryCodeService;
import com.ruckus.cloud.wifi.service.InitApGroupService;
import com.ruckus.cloud.wifi.service.VenueApModelSpecificAttributesService;
import com.ruckus.cloud.wifi.service.VenueCurrentFirmwareService;
import com.ruckus.cloud.wifi.service.impl.DeviceRegistrationServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.DhcpServiceMockTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedApServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.FirmwareCapabilityServiceTestConfig;
import com.ruckus.cloud.wifi.servicemodel.projection.ApRegistrationProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DhcpVenue;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApModelMinimumFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.utils.TxCtxUtils;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;

@WifiJpaDataTest
public class DeviceRegistrationServiceTest {

  @Autowired
  private DeviceRegistrationServiceImpl deviceRegistrationService;

  @MockBean
  private DeviceRegistrationPolicyPublisher deviceRegistrationPolicyPublisher;

  @MockBean
  private VenueApModelSpecificAttributesService venueApModelSpecificAttributesService;

  @Autowired
  private ApRepository apRepository;

  @Autowired
  private VenueRepository venueRepository;

  @Autowired
  private ApVersionRepository apVersionRepository;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private VenueApModelSpecificAttributesRepository vamsaRepository;

  @Autowired
  private TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository;

  @Autowired
  private ApModelMinimumFirmwareRepository apModelMinimumFirmwareRepository;

  @MockBean
  private ApUpgradeService apUpgradeService;

  @MockBean
  private DeviceConfigUpdateNotificationService deviceConfigUpdateNotificationService;

  @MockBean
  private InitApGroupService initApGroupService;

  @MockBean
  private ApDeleteService apDeleteService;

  @MockBean
  private DdccmPublisher ddccmPublisher;

  @MockBean
  private DdccmApOperationBuilder ddccmApOperationBuilder;

  @MockBean
  private KafkaTransactionHelper kafkaTransactionHelper;

  @MockBean
  private ExtendedVenueServiceCtrl venueService;

  @MockBean
  private FirmwareCountryCodeService firmwareCountryCodeService;

  @MockBean
  private VenueCurrentFirmwareService venueCurrentFirmwareService;

  @MockBean
  private EventPublisher eventPublisher;

  @Autowired
  private FirmwareCapabilityService capabilityService;

  @MockBean
  private WifiAsyncJobPublisher wifiAsyncJobPublisher;

  @MockBean
  private SoftGreProfileRepository softGreProfileRepository;

  @MockBean
  private ClientIsolationAllowlistRepository clientIsolationAllowlistRepository;

  @MockBean
  private IpsecProfileRepository ipsecProfileRepository;

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  private static final String SUPPORTED_MODEL = "R710";

  private static final String CELLULAR_MODEL = "M510";

  @Test
  void testHandleDeviceRegistrationAsync_ApNotFound() throws Exception {
    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setSerial(randomSerialNumber() + "nonExistingApId")
        .setMac("00:11:22:33:44:55")
        .setModel(SUPPORTED_MODEL)
        .build();

    deviceRegistrationService.handleDeviceRegistrationAsync(deviceRegistration,
        MessageBuilder.createMessage(deviceRegistration.toByteArray(),
            new MessageHeaders(Collections.emptyMap())));

    verify(wifiAsyncJobPublisher, never()).publishDeviceRegistAsyncJob(any(), any());
    verify(deviceRegistrationPolicyPublisher, never()).publish(any(), any());
  }

  @Test
  void testHandleDeviceRegistrationAsync_RejectByCloudNotSupported(ApGroup apGroup) throws Exception {
    Ap ap = new Ap(randomSerialNumber());
    ap.setName(randomName());
    ap.setTenant(apGroup.getTenant());
    ap.setApGroup(apGroup);
    ap = apRepository.save(ap);

    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setSerial(ap.getId())
        .setMac("00:11:22:33:44:55")
        .setModel("NotSupported")
        .build();

    final var result = deviceRegistrationService.handleDeviceRegistrationAsync(deviceRegistration,
        MessageBuilder.createMessage(deviceRegistration.toByteArray(),
            new MessageHeaders(Collections.emptyMap())));
    result.ifPresent(deviceRegistrationService::postHandleAsyncDeviceRegistration);

    verify(wifiAsyncJobPublisher, never()).publishDeviceRegistAsyncJob(any(), any());
    verify(deviceRegistrationPolicyPublisher, times(1)).publish(any(),
        eq(Policy.REJECTED));
    assertThat(apRepository.findByIdAndTenantId(ap.getId(), ap.getTenant().getId()))
        .isNotEmpty()
        .get()
        .extracting(Ap::getRegistrationState)
        .isEqualTo(RegistrationStateEnum.REJECTED);
  }

  @Test
  void testHandleDeviceRegistrationAsync_RejectByCapabilityNotFound(ApGroup apGroup) throws Exception {
    Ap ap = new Ap(randomSerialNumber());
    ap.setName(randomName());
    ap.setTenant(apGroup.getTenant());
    ap.setApGroup(apGroup);
    ap = apRepository.save(ap);

    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setSerial(ap.getId())
        .setMac("00:11:22:33:44:55")
        .setModel("R770")
        .build();

    doReturn(Optional.empty()).when(capabilityService)
        .findVenueCapabilitiesApModel(any(String.class), eq("R770"));

    final var result = deviceRegistrationService.handleDeviceRegistrationAsync(deviceRegistration,
        MessageBuilder.createMessage(deviceRegistration.toByteArray(),
            new MessageHeaders(Collections.emptyMap())));
    result.ifPresent(deviceRegistrationService::postHandleAsyncDeviceRegistration);

    verify(wifiAsyncJobPublisher, never()).publishDeviceRegistAsyncJob(any(), any());
    verify(deviceRegistrationPolicyPublisher, times(1)).publish(any(),
        eq(Policy.REJECTED));
    assertThat(apRepository.findByIdAndTenantId(ap.getId(), ap.getTenant().getId()))
        .isNotEmpty()
        .get()
        .extracting(Ap::getRegistrationState)
        .isEqualTo(RegistrationStateEnum.REJECTED);
  }

  @Test
  @FeatureFlag(enable = AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testHandleDeviceRegistrationAsync_rejectByMinimumRequiredVersion(ApGroup apGroup) throws Exception {
    ApVersion availableVersion = ApVersionTestFixture.recommendedApVersion("7.0.0.103.99",
        c -> c.setSupportedApModels(List.of("R770")));
    ApVersion minimumVersion = ApVersionTestFixture.recommendedApVersion("7.0.0.103.100", c -> {
    });
    apVersionRepository.saveAll(List.of(availableVersion, minimumVersion));
    apModelMinimumFirmwareRepository.save(
        ApModelMinimumFirmwareTestFixture.randomApModelMinimumFirmware("R770", minimumVersion));
    doReturn(availableVersion).when(venueCurrentFirmwareService)
        .findApVersionByVenueOrTenantDefaultForModel(eq(apGroup.getTenant().getId()), eq(apGroup.getVenue().getId()),
            eq("R770"));
    doReturn(true).when(venueCurrentFirmwareService)
        .isLowerThanApModelMinimumFirmware(eq(availableVersion),eq("R770"));

    Ap ap = new Ap(randomSerialNumber());
    ap.setName(randomName());
    ap.setTenant(apGroup.getTenant());
    ap.setApGroup(apGroup);
    ap = apRepository.save(ap);
    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setTenantId(ap.getTenant().getId())
        .setSerial(ap.getId())
        .setMac("00:11:22:33:44:55")
        .setModel("R770")
        .build();

    final var result = deviceRegistrationService.handleDeviceRegistrationAsync(deviceRegistration,
        MessageBuilder.createMessage(deviceRegistration.toByteArray(),
            new MessageHeaders(Collections.emptyMap())));
    result.ifPresent(deviceRegistrationService::postHandleAsyncDeviceRegistration);

    verify(wifiAsyncJobPublisher, never()).publishDeviceRegistAsyncJob(any(), any());
    verify(deviceRegistrationPolicyPublisher, times(1)).publish(any(),
        eq(Policy.REJECTED));
    assertThat(apRepository.findByIdAndTenantId(ap.getId(), ap.getTenant().getId()))
        .isNotEmpty()
        .get()
        .extracting(Ap::getRegistrationState)
        .isEqualTo(RegistrationStateEnum.REJECTED);
  }

  @Test
  void testHandleDeviceRegistrationAsync_Approved(ApGroup apGroup) throws Exception {
    Ap ap = new Ap(randomSerialNumber());
    ap.setName(randomName());
    ap.setTenant(apGroup.getTenant());
    ap.setApGroup(apGroup);
    ap = apRepository.save(ap);

    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setSerial(ap.getId())
        .setMac("00:11:22:33:44:55")
        .setModel(SUPPORTED_MODEL)
        .build();

    final var result = deviceRegistrationService.handleDeviceRegistrationAsync(deviceRegistration,
        MessageBuilder.createMessage(deviceRegistration.toByteArray(),
            new MessageHeaders(Collections.emptyMap())));
    result.ifPresent(deviceRegistrationService::postHandleAsyncDeviceRegistration);

    verify(wifiAsyncJobPublisher, times(1)).publishDeviceRegistAsyncJob(any(), any());
    verify(deviceRegistrationPolicyPublisher, times(1)).publish(any(), eq(Policy.APPROVED));
  }

  @Test
  void testHandleApprovedAp_ApNotFound() throws Exception {
    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setSerial(randomSerialNumber() + "nonExistingApId")
        .setMac("00:11:22:33:44:55")
        .setModel(SUPPORTED_MODEL)
        .build();

    deviceRegistrationService.handleApprovedAp(deviceRegistration);

    verify(apUpgradeService, never()).doDiscovery(eq(txCtxExtension.getRequestId()), any(), any());
  }

  @Test
  void testHandleApprovedAp_FirstJoin(ApGroup apGroup) throws Exception {
    Ap ap = new Ap(randomSerialNumber());
    ap.setName(randomName());
    ap.setTenant(apGroup.getTenant());
    ap.setApGroup(apGroup);
    ap = apRepository.save(ap);

    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setSerial(ap.getId())
        .setMac("00:11:22:33:44:55")
        .setModel(SUPPORTED_MODEL)
        .build();

    deviceRegistrationService.handleApprovedAp(deviceRegistration);
    assertThat(ap.getRegistrationState()).isEqualTo(RegistrationStateEnum.APPROVED);
    verify(apUpgradeService, never()).doDiscovery(eq(txCtxExtension.getRequestId()), any(), any());
  }

  @Test
  void testHandleApprovedAp_Discovery(ApGroup apGroup) throws Exception {
    Ap ap = new Ap(randomSerialNumber());
    ap.setName(randomName());
    ap.setTenant(apGroup.getTenant());
    ap.setApGroup(apGroup);
    ap.setMac("00:11:22:33:44:55");
    ap.setModel(SUPPORTED_MODEL);
    ap.setApGroup(apGroup);
    ap = apRepository.save(ap);

    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setSerial(ap.getId())
        .setMac(ap.getMac())
        .setModel(ap.getModel())
        .build();

    deviceRegistrationService.handleApprovedAp(deviceRegistration);
    verify(apUpgradeService, times(1)).doDiscovery(eq(txCtxExtension.getRequestId()), any(), any());
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
  void testApFirstJoinWithEthernetSoftGre(Venue venue, ApGroup apGroup) throws Exception {
    Ap ap = new Ap(randomSerialNumber());
    ap.setName(randomName());
    ap.setTenant(apGroup.getTenant());
    ap.setApGroup(apGroup);
    ap = apRepository.save(ap);
    var mockSoftGreProfiles = List.of(
        new SoftGreProfile("softgre-profile-1"),
        new SoftGreProfile("softgre-profile-2")
    );
    var mockIpsecProfiles = List.of(
        new IpsecProfile("ipsec-profile-1")
    );
    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setSerial(ap.getId())
        .setMac("00:11:22:33:44:55")
        .setModel(SUPPORTED_MODEL)
        .build();
    when(venueService.getVenueOrDefault(eq(venue.getId()))).thenReturn(venue);
    when(softGreProfileRepository.countByTenantId(ap.getTenant().getId())).thenReturn(2l);
    when(softGreProfileRepository.findAllLanPortsActivatedSoftGreProfilesByVenueIdsOrApSerials(
        ap.getTenant().getId(), List.of(venue.getId()), List.of(ap.getId())))
        .thenReturn(mockSoftGreProfiles);
    when(ipsecProfileRepository.findAllLanPortsActivatedIpsecProfilesByVenueIdsOrApSerials(
        ap.getTenant().getId(), List.of(venue.getId()), List.of(ap.getId())))
        .thenReturn(mockIpsecProfiles);
    try (MockedStatic<TxCtxUtils> txCtxUtils = mockStatic(TxCtxUtils.class)) {
      deviceRegistrationService.handleApprovedAp(deviceRegistration);
      txCtxUtils.verify(() -> TxCtxUtils.markEntityAsModifiedToCmnCfgCollector(
          any(SoftGreProfile.class)), times(2));
    }
  }

  @Test
  @FeatureFlag(enable = {WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
  void testApFirstJoinWithEthernetClientIsolation(Venue venue, ApGroup apGroup) throws Exception {
    Ap ap = new Ap(randomSerialNumber());
    ap.setName(randomName());
    ap.setTenant(apGroup.getTenant());
    ap.setApGroup(apGroup);
    ap = apRepository.save(ap);
    var mockClientIsolationProfiles = List.of(
        new ClientIsolationAllowlist("client-isolation-profile-1"),
        new ClientIsolationAllowlist("client-isolation-profile-2")
    );
    DeviceRegistration deviceRegistration = DeviceRegistration.newBuilder()
        .setSerial(ap.getId())
        .setMac("00:11:22:33:44:55")
        .setModel(SUPPORTED_MODEL)
        .build();
    when(venueService.getVenueOrDefault(eq(venue.getId()))).thenReturn(venue);
    when(clientIsolationAllowlistRepository.countByTenantId(ap.getTenant().getId())).thenReturn(2l);
    when(clientIsolationAllowlistRepository.findAllLanPortsActivatedClientIsolationProfilesByVenueIdsOrApSerials(
        ap.getTenant().getId(), List.of(venue.getId()), List.of(ap.getId())))
        .thenReturn(mockClientIsolationProfiles);
    try (MockedStatic<TxCtxUtils> txCtxUtils = mockStatic(TxCtxUtils.class)) {
      deviceRegistrationService.handleApprovedAp(deviceRegistration);
      txCtxUtils.verify(() -> TxCtxUtils.markEntityAsModifiedToCmnCfgCollector(
          any(ClientIsolationAllowlist.class)), times(2));
    }
  }

  @TestConfiguration
  @Import({
      ExtendedApServiceCtrlImplTestConfig.class,
      DhcpServiceMockTestConfig.class,
      ExtendedVenueServiceCtrlImplTestConfig.class,
      FirmwareCapabilityServiceTestConfig.class
  })
  static class TestConfig {

    @Bean
    public DeviceRegistrationServiceImpl deviceRegistrationService(
        ExtendedApServiceCtrl extendedApServiceCtrl,
        DeviceRegistrationPolicyPublisher deviceRegistrationPolicyPublisher,
        CloudSupportApModels cloudSupportApModels,
        ApUpgradeService apUpgradeService,
        FirmwareCapabilityService capabilityService,
        ExtendedVenueServiceCtrl venueService,
        EventPublisher eventPublisher,
        FeatureFlagService featureFlagService,
        VenueCurrentFirmwareService venueCurrentFirmwareService,
        ApRepository apRepository,
        WifiAsyncJobPublisher wifiAsyncJobPublisher,
        ApModelMinimumFirmwareRepository apModelMinimumFirmwareRepository,
        FirmwareCountryCodeService firmwareCountryCodeService,
        SoftGreProfileRepository softGreProfileRepository,
        ClientIsolationAllowlistRepository clientIsolationAllowlistRepository,
        IpsecProfileRepository ipsecProfileRepository) {
      return new DeviceRegistrationServiceImpl(
          extendedApServiceCtrl,
          deviceRegistrationPolicyPublisher,
          cloudSupportApModels,
          apUpgradeService,
          capabilityService,
          venueService,
          eventPublisher,
          featureFlagService,
          venueCurrentFirmwareService,
          apRepository,
          wifiAsyncJobPublisher,
          apModelMinimumFirmwareRepository,
          firmwareCountryCodeService,
          softGreProfileRepository,
          clientIsolationAllowlistRepository,
          ipsecProfileRepository);
    }
  }
}
