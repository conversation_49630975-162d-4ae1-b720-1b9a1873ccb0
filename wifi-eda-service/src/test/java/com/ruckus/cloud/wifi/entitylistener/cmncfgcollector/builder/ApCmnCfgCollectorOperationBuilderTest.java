package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.conf.WifiBaseEntityFieldNames;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApManagementTrafficVlanSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.ApPosition;
import com.ruckus.cloud.wifi.eda.servicemodel.DeviceGps;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
@FeatureFlag(enable = FlagNames.WIFI_AP_MGMT_VLAN_AP_LEVEL_TOGGLE)
public class ApCmnCfgCollectorOperationBuilderTest {

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @SpyBean
  private ApCmnCfgCollectorOperationBuilder apCmnCfgCollectorOperationBuilder;

  @Test
  public void testGetEntityClass() {
    assertThat(apCmnCfgCollectorOperationBuilder.entityClass())
        .isEqualTo(Ap.class);
  }

  @Nested
  class testBuildConfig {

    @Test
    public void givenEntityActionIsDelete() {
      Operations operations = apCmnCfgCollectorOperationBuilder.build(
          new TxEntity<>(new Ap(randomSerialNumber()), EntityAction.DELETE), emptyTxChanges()).get(0);

      assertThat(operations.getDocCount())
          .isZero();
    }

    @Test
    public void givenAddAp() {
      Operations.Builder builder = Operations.newBuilder();
      Venue venue = new Venue(randomId());
      venue.setName("Default venue");
      ApGroup apGroup = new ApGroup(randomId());
      apGroup.setName("New AP group");
      apGroup.setVenue(venue);
      apGroup.setIsDefault(false);
      Ap ap = new Ap();
      ap.setTenant(new Tenant(randomId()));
      ap.setId(randomSerialNumber());
      ap.setCreatedDate(new Date());
      ap.setUpdatedDate(new Date());
      ap.setName(randomName());
      ap.setApGroup(apGroup);
      ap.setTags(List.of("one", "two", "three"));

      ApPosition apPosition = new ApPosition();
      apPosition.setFloorplanId(randomId());
      apPosition.setXPercent(50.f);
      apPosition.setYPercent(99.0f);
      ap.setPosition(apPosition);

      DeviceGps deviceGps = new DeviceGps();
      deviceGps.setLatitude("120");
      deviceGps.setLongitude("60");
      ap.setDeviceGps(deviceGps);

      ApManagementTrafficVlanSettings apManagementTrafficVlanSettings = new ApManagementTrafficVlanSettings();
      apManagementTrafficVlanSettings.setVlanId((short) 2);
      apManagementTrafficVlanSettings.setUseVenueSettings(true);
      apManagementTrafficVlanSettings.setVlanOverrideEnabled(true);
      ap.setApManagementVlan(apManagementTrafficVlanSettings);

      apCmnCfgCollectorOperationBuilder.config(builder, ap, EntityAction.ADD);
      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get("type"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .isEqualTo(EsConstants.Value.TYPE_DEVICE);
      assertThat(docMap.get("tenantId"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getTenant().getId());
      assertThat(docMap.get("serialNumber"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getId());
      assertThat(docMap.get("deviceType"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo("DVCNWTYPE_WIFI");
      assertThat(docMap.get("crtTime"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (long) p.getNumberValue())
          .isEqualTo(ap.getCreatedDate().getTime());
      assertThat(docMap.get("lastUpdTime"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (long) p.getNumberValue())
          .isEqualTo(ap.getUpdatedDate().getTime());
      assertThat(docMap.get("name"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getName());
      assertThat(docMap.get("venueId"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getApGroup().getVenue().getId());
      // FIXME: should unmark venueName when we figured out the issue in beforeCommit
//      assertThat(docMap.get("venueName"))
//          .extracting(p -> p.getStringValue())
//          .isEqualTo(ap.getApGroup().getVenue().getName());
      assertThat(docMap.get("deviceGroupId"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getApGroup().getId());
      assertThat(docMap.get("tags"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(String.join(",", ap.getTags()));
      assertThat(docMap.get("deviceGroupName"))
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getApGroup().getName());
      assertThat(docMap.get("floorplanId"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getPosition().getFloorplanId());
      assertThat(docMap.get("xPercent"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (float) p.getNumberValue())
          .isEqualTo(ap.getPosition().getXPercent());
      assertThat(docMap.get("yPercent"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (float) p.getNumberValue())
          .isEqualTo(ap.getPosition().getYPercent());
      assertThat(docMap.get("latitude"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getDeviceGps().getLatitude());
      assertThat(docMap.get("longitude"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getDeviceGps().getLongitude());
      assertThat(docMap.get("apStatusData")).isNull();
    }

    @Test
    public void givenUpdateAp() {
      Operations.Builder builder = Operations.newBuilder();
      Venue venue = new Venue(randomId());
      venue.setName("Default venue");
      ApGroup apGroup = new ApGroup(randomId());
      apGroup.setName("New AP group");
      apGroup.setVenue(venue);
      apGroup.setIsDefault(false);
      Ap ap = new Ap();
      ap.setTenant(new Tenant(randomId()));
      ap.setId(randomSerialNumber());
      ap.setCreatedDate(new Date());
      ap.setUpdatedDate(new Date());
      ap.setName(randomName());
      ap.setApGroup(apGroup);
      ap.setTags(List.of("one", "two", "three"));

      ApPosition apPosition = new ApPosition();
      apPosition.setFloorplanId(randomId());
      apPosition.setXPercent(50.f);
      apPosition.setYPercent(99.0f);
      ap.setPosition(apPosition);

      DeviceGps deviceGps = new DeviceGps();
      deviceGps.setLatitude("120");
      deviceGps.setLongitude("60");
      ap.setDeviceGps(deviceGps);

      ApManagementTrafficVlanSettings apManagementTrafficVlanSettings = new ApManagementTrafficVlanSettings();
      apManagementTrafficVlanSettings.setVlanId((short) 2);
      apManagementTrafficVlanSettings.setUseVenueSettings(true);
      apManagementTrafficVlanSettings.setVlanOverrideEnabled(true);
      ap.setApManagementVlan(apManagementTrafficVlanSettings);

      apCmnCfgCollectorOperationBuilder.config(builder, ap, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get("type"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .isEqualTo(EsConstants.Value.TYPE_DEVICE);
      assertThat(docMap.get("tenantId"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getTenant().getId());
      assertThat(docMap.get("serialNumber"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getId());
      assertThat(docMap.get("deviceType"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo("DVCNWTYPE_WIFI");
      assertThat(docMap.get("crtTime"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (long) p.getNumberValue())
          .isEqualTo(ap.getCreatedDate().getTime());
      assertThat(docMap.get("lastUpdTime"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (long) p.getNumberValue())
          .isEqualTo(ap.getUpdatedDate().getTime());
      assertThat(docMap.get("name"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getName());
      assertThat(docMap.get("venueId"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getApGroup().getVenue().getId());
      assertThat(docMap.get("deviceGroupId"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getApGroup().getId());
      assertThat(docMap.get("tags"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(String.join(",", ap.getTags()));
      assertThat(docMap.get("deviceGroupName"))
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getApGroup().getName());
      assertThat(docMap.get("floorplanId"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getPosition().getFloorplanId());
      assertThat(docMap.get("xPercent"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (float) p.getNumberValue())
          .isEqualTo(ap.getPosition().getXPercent());
      assertThat(docMap.get("yPercent"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (float) p.getNumberValue())
          .isEqualTo(ap.getPosition().getYPercent());
      assertThat(docMap.get("latitude"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getDeviceGps().getLatitude());
      assertThat(docMap.get("longitude"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(p -> p.getStringValue())
          .isEqualTo(ap.getDeviceGps().getLongitude());
      assertThat(docMap.get("apStatusData"))
          .matches(doc -> doc.getKindCase() == KindCase.STRUCT_VALUE)
          .extracting(Value::getStructValue)
          .extracting(Struct::getFieldsMap)
          .extracting(apStatusDataMap -> apStatusDataMap.get("APSystem"))
          .matches(doc -> doc.getKindCase() == KindCase.STRUCT_VALUE)
          .extracting(Value::getStructValue)
          .extracting(Struct::getFieldsMap)
          .extracting(apSystemMap -> apSystemMap.get("managementVlan"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (short) p.getNumberValue())
          .isEqualTo(ap.getApManagementVlan().getVlanId());
    }

    @Test
    public void givenAddApWithEmptyPosition() {
      var builder = Operations.newBuilder();
      var venue = new Venue(randomId());
      venue.setName("Default venue");
      var apGroup = new ApGroup(randomId());
      apGroup.setName("New AP group");
      apGroup.setVenue(venue);
      apGroup.setIsDefault(false);
      var ap = new Ap();
      ap.setTenant(new Tenant(randomId()));
      ap.setId(randomSerialNumber());
      ap.setCreatedDate(new Date());
      ap.setUpdatedDate(new Date());
      ap.setName(randomName());
      ap.setApGroup(apGroup);
      ap.setTags(List.of("one", "two", "three"));

      var apPosition = new ApPosition();
      apPosition.setFloorplanId(null);
      apPosition.setXPercent(0F);
      apPosition.setYPercent(0F);
      ap.setPosition(apPosition);

      apCmnCfgCollectorOperationBuilder.config(builder, ap, EntityAction.ADD);
      var docMap = builder.build().getDocMap();
      // It would be an empty string if we set NullValue
      assertThat(docMap.get("floorplanId"))
          .matches(doc -> doc.getKindCase() == KindCase.STRING_VALUE)
          .extracting(Value::getStringValue)
          .isEqualTo(StringUtils.EMPTY);
      assertThat(docMap.get("xPercent"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (float) p.getNumberValue())
          .isEqualTo(ap.getPosition().getXPercent());
      assertThat(docMap.get("yPercent"))
          .matches(doc -> doc.getKindCase() == KindCase.NUMBER_VALUE)
          .extracting(p -> (float) p.getNumberValue())
          .isEqualTo(ap.getPosition().getYPercent());
    }

    @Test
    public void testGetIndex() {
      assertThat(apCmnCfgCollectorOperationBuilder.index()).isEqualTo(EsConstants.Index.DEVICE);
    }

    @Nested
    class testHasModification {

      @Test
      public void givenEntityActionIsNotModify() {
        assertThat(apCmnCfgCollectorOperationBuilder.hasModification(EntityAction.ADD,
            Collections.emptySet(), false))
            .isTrue();
      }

      @Test
      public void givenGivenFieldsInExportedFields() {
        apCmnCfgCollectorOperationBuilder.setBaseEntityFieldNames(new WifiBaseEntityFieldNames());
        assertThat(apCmnCfgCollectorOperationBuilder.hasModification(EntityAction.MODIFY,
            Set.of("id"), false))
            .isTrue();
        assertThat(apCmnCfgCollectorOperationBuilder.hasModification(EntityAction.MODIFY,
            Set.of("id", "updatedDate"), false))
            .isTrue();
      }

      @Test
      public void givenGivenFieldsNotInExportedFields() {
        apCmnCfgCollectorOperationBuilder.setBaseEntityFieldNames(new WifiBaseEntityFieldNames());
        assertThat(apCmnCfgCollectorOperationBuilder.hasModification(EntityAction.MODIFY,
            Set.of("updatedDate"), false))
            .isFalse();
        assertThat(apCmnCfgCollectorOperationBuilder.hasModification(EntityAction.MODIFY,
            Set.of("mac"), false))
            .isFalse();
        assertThat(apCmnCfgCollectorOperationBuilder.hasModification(EntityAction.MODIFY,
            Set.of("mac", "model"), false))
            .isFalse();
      }
    }
  }
}
