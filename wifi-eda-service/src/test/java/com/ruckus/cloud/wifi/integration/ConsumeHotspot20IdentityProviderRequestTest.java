package com.ruckus.cloud.wifi.integration;

import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20AuthInfo;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Eap;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20NaiRealm;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Plmn;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20RoamConsortium;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.UUIDGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Hotspot20IdentityProviderGenerator;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wlan;
import static com.ruckus.cloud.wifi.kafka.publisher.ViewmodelConfigPublisher.RKS_CFG_ACTIVITY;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

@WifiIntegrationTest
public class ConsumeHotspot20IdentityProviderRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private RevisionService revisionService;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  private Hotspot20IdentityProvider createAndSaveIdentityProvider(Tenant tenant) {
    Radius radiusAuth = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuth().generate();
    Radius radiusAcct = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcct().generate();

    AccountingRadiusService accountingRadiusService =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accountingRadiusService(radiusAcct).generate();
    AccountingRadiusProfile accountingRadiusProfile =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accountingRadiusProfile(accountingRadiusService).generate();
    AuthRadiusService authRadiusService =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.authRadiusService(radiusAuth).generate();
    AuthRadiusProfile authRadiusProfile =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.authRadiusProfile(authRadiusService).generate();

    var hotspot20NaiRealm = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20NaiRealm().generate();
    hotspot20NaiRealm.getEaps().forEach(e -> {
      e.setNaiRealm(hotspot20NaiRealm);
      e.getAuthInfos().forEach(a -> a.setEap(e));
    });
    var hotspot20Plmn = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20Plmn().generate();
    var hotspot20RoamConsortium = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20RoamConsortium().generate();

    Hotspot20IdentityProvider hotspot20IdentityProvider = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20IdentityProvider().generate();
    hotspot20IdentityProvider.setAccountingRadiusEnabled(true);
    hotspot20IdentityProvider.setAccountingRadius(radiusAcct);
    hotspot20IdentityProvider.setAccountingRadiusProfile(accountingRadiusProfile);
    hotspot20IdentityProvider.setAuthRadius(radiusAuth);
    hotspot20IdentityProvider.setAuthRadiusProfile(authRadiusProfile);
    hotspot20IdentityProvider.setNaiRealms(List.of(hotspot20NaiRealm));
    hotspot20IdentityProvider.setPlmns(List.of(hotspot20Plmn));
    hotspot20IdentityProvider.setRoamConsortiumOis(List.of(hotspot20RoamConsortium));

    hotspot20NaiRealm.setIdentityProvider(hotspot20IdentityProvider);
    hotspot20Plmn.setIdentityProvider(hotspot20IdentityProvider);
    hotspot20RoamConsortium.setIdentityProvider(hotspot20IdentityProvider);

    repositoryUtil.createOrUpdate(radiusAuth);
    repositoryUtil.createOrUpdate(radiusAcct);
    repositoryUtil.createOrUpdate(accountingRadiusService);
    repositoryUtil.createOrUpdate(accountingRadiusProfile);
    repositoryUtil.createOrUpdate(authRadiusService);
    repositoryUtil.createOrUpdate(authRadiusProfile);

    return repositoryUtil.createOrUpdate(hotspot20IdentityProvider);
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String hotspot20IdentityProviderId,
                                           com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20IdentityProvider payload) {
    validateResult(txCtx, apiAction, hotspot20IdentityProviderId, payload, false);
  }

  private void validateResult(TxCtx txCtx, CfgAction apiAction, String hotspot20IdentityProviderId,
    com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20IdentityProvider payload, boolean isPreconfigured) {

    if (hotspot20IdentityProviderId == null) {
      final String requestId = txCtx.getTxId();
      final String tenantId = txCtx.getTenant();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty, "should be empty");
      return;
    }

    final Hotspot20IdentityProvider hotspot20IdentityProvider = repositoryUtil.find(
      Hotspot20IdentityProvider.class, hotspot20IdentityProviderId);

    if (apiAction == null || payload == null ||
        (apiAction == CfgAction.DELETE_HOTSPOT20IDENTITY_PROVIDER ||
            apiAction == CfgAction.DELETE_PRECONFIGURED_HOTSPOT20IDENTITY_PROVIDER) ) {
      assertThat(hotspot20IdentityProvider).isNull();
      return;
    }

    assertThat(hotspot20IdentityProvider)
      .isNotNull()
      .matches(o -> Objects.equals(o.getId(), hotspot20IdentityProviderId))
      .matches(o -> Objects.equals(o.getName(), payload.getName()))
      .matches(o -> Objects.equals(o.getIsPreconfigured(), isPreconfigured));

    // Check Plmn
    List<Hotspot20Plmn> dbPlmns = hotspot20IdentityProvider.getPlmns();
    List<Hotspot20Plmn> requestPlmns = payload.getPlmns().stream()
        .map(this::hotspot20PlmnViewToServiceModel).toList();

    for (Hotspot20Plmn dbPlmn : dbPlmns) {
      Hotspot20Plmn requestPlmn = requestPlmns.stream()
          .filter(payloadPlmn -> dbPlmn.getMcc().equals(payloadPlmn.getMcc()))
          .findFirst().orElse(null);

      assertNotNull(requestPlmn);
      assertEquals(dbPlmn.getMnc(), requestPlmn.getMnc());
    }

    // Check RoamConsortium
    List<Hotspot20RoamConsortium> dbRoamConsortiums = hotspot20IdentityProvider.getRoamConsortiumOis();
    List<Hotspot20RoamConsortium> requestRoamConsortiums = payload.getRoamConsortiumOIs().stream()
        .map(this::hotspot20RoamConsortiumViewToServiceModel).toList();
    for (Hotspot20RoamConsortium dbRoamConsortium : dbRoamConsortiums) {
      Hotspot20RoamConsortium requestRoamConsortium = requestRoamConsortiums.stream()
          .filter(roamConsortium -> dbRoamConsortium.getName().equals(roamConsortium.getName()))
          .findFirst().orElse(null);

      assertNotNull(requestRoamConsortium);
      assertEquals(dbRoamConsortium.getOrganizationId(), requestRoamConsortium.getOrganizationId());
    }

    // Check NaiRealm
    List<Hotspot20NaiRealm> dbNaiRealms = hotspot20IdentityProvider.getNaiRealms();
    List<Hotspot20NaiRealm> requestNaiRealms = payload.getNaiRealms().stream()
        .map(this::hotspot20NaiRealmViewToServiceModel).toList();

    for (Hotspot20NaiRealm dbNaiRealm : dbNaiRealms) {
      Hotspot20NaiRealm requestNaiRealm = requestNaiRealms.stream()
          .filter(naiRealm -> dbNaiRealm.getName().equals(naiRealm.getName()))
          .findFirst().orElse(null);

      assertNotNull(requestNaiRealm);
      assertEquals(dbNaiRealm.getEncoding(), requestNaiRealm.getEncoding());
      assertEquals(dbNaiRealm.getEaps().size(), requestNaiRealm.getEaps().size());

      for (Hotspot20Eap dbEap : dbNaiRealm.getEaps()) {
        Hotspot20Eap requestEap = requestNaiRealm.getEaps().stream()
            .filter(eap -> dbEap.getMethod().equals(eap.getMethod()))
            .findFirst().orElse(null);

        assertNotNull(requestEap);
        assertEquals(dbEap.getAuthInfos().size(), requestEap.getAuthInfos().size());
      }
    }

    // Should not update networkHotspot20Settings binding on update
    final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(), ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    final var anyNetworkBindingModified = txChanges.getAllModifiedProperties().contains(Hotspot20IdentityProvider.Fields.NETWORKHOTSPOT20SETTINGSLIST);
    assertFalse(anyNetworkBindingModified);
  }

  private void validateCmnCfgCollectorMessagesSyncToViewmodel(TxCtx txCtx) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage.getHeaders())
        .anyMatch(header -> header.key().equals(RKS_CFG_ACTIVITY));

    if (txCtx.getFlowName().contains("DeleteHotspot20IdentityProvider")) {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .extracting(ViewmodelCollector::getOperationsCount)
          .isEqualTo(3);
    }
  }

  private void validateDdccmCfgRequestMessages(TxCtx txCtx, int expectedIdentityProviderCount) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(KafkaProtoMessage::getHeaders).isNotNull();

    assertThat(ddccmCfgRequestMessage.getPayload())
        .extracting(WifiConfigRequest::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasHotspot20Profile)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getHotspot20Profile)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20Profile.class::cast)
        .allMatch(h -> h.getIdentityProviderIdCount() == expectedIdentityProviderCount);
  }

  protected com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20NaiRealm hotspot20NaiRealmViewToServiceModel(
      com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20NaiRealm hotspot20NaiRealm) {
    if ( hotspot20NaiRealm == null ) {
      return null;
    }

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20NaiRealm hotspot20NaiRealm1 =
        new com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20NaiRealm();
    hotspot20NaiRealm1.setName( hotspot20NaiRealm.getName() );
    hotspot20NaiRealm1.setEncoding( hotspot20NaiRealm.getEncoding() );
    hotspot20NaiRealm1.setEaps(hotspot20EapListToHotspot20EapList(hotspot20NaiRealm.getEaps()));

    return hotspot20NaiRealm1;
  }

  protected com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Plmn hotspot20PlmnViewToServiceModel(
      com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Plmn hotspot20Plmn) {
    if (hotspot20Plmn == null) {
      return null;
    }

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Plmn hotspot20Plmn1 =
        new com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Plmn();
    hotspot20Plmn1.setMcc(hotspot20Plmn.getMcc());
    hotspot20Plmn1.setMnc(hotspot20Plmn.getMnc());

    return hotspot20Plmn1;
  }

  protected com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20RoamConsortium hotspot20RoamConsortiumViewToServiceModel(
      com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20RoamConsortium hotspot20RoamConsortium) {
    if (hotspot20RoamConsortium == null) {
      return null;
    }

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20RoamConsortium hotspot20RoamConsortium1 =
        new com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20RoamConsortium();
    hotspot20RoamConsortium1.setOrganizationId(hotspot20RoamConsortium.getOrganizationId());
    hotspot20RoamConsortium1.setName(hotspot20RoamConsortium.getName());
    return hotspot20RoamConsortium1;
  }

  @Nested
  @ApiAction(CfgAction.ADD_HOTSPOT20IDENTITY_PROVIDER)
  class ConsumeAddHotspot20IdentityProviderRequestTest {

    String authRadiusId;
    String accountingRadiusId;

    @BeforeEach
    void giveAuthRadiusAndAcctRadius(final Tenant tenant) {

      Radius radiusAuth = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuth()
          .generate();
      Radius radiusAcct = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcct()
          .generate();
      authRadiusId = repositoryUtil.createOrUpdate(radiusAuth).getId();
      accountingRadiusId = repositoryUtil.createOrUpdate(radiusAcct).getId();
    }

    @Payload
    private Hotspot20IdentityProviderGenerator hotspot20IdentityProvider() {
      return Generators.hotspot20IdentityProvider();
    }

    @Test
    void thenShouldAddHotspot20IdentityProviderSuccessfully(TxCtx txCtx, CfgAction apiAction,
      @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20IdentityProvider payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
      validateCmnCfgCollectorMessagesSyncToViewmodel(txCtx);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_HOTSPOT20IDENTITY_PROVIDER)
  class ConsumeUpdateHotspot20IdentityProviderRequestTest {

    String hotspot20IdentityProviderId;
    String authRadiusId;
    String accountingRadiusId;

    @BeforeEach
    void giveHotspot20IdentityProvider(final Tenant tenant) {
      // Create and saved data
      hotspot20IdentityProviderId = createAndSaveIdentityProvider(tenant).getId();

      Radius radiusAuth = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuth().generate();
      Radius radiusAcct = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcct().generate();
      authRadiusId = repositoryUtil.createOrUpdate(radiusAuth).getId();
      accountingRadiusId = repositoryUtil.createOrUpdate(radiusAcct).getId();
    }

    @Payload
    private Hotspot20IdentityProviderGenerator hotspot20IdentityProvider() {
      Hotspot20IdentityProviderGenerator generator =
          Generators.hotspot20IdentityProvider();
      generator.setId(always(hotspot20IdentityProviderId));
      return generator;
    }

    @Payload("emptyPlmnAndRoamConsortium")
    private Hotspot20IdentityProviderGenerator emptyPlmnAndRoamConsortium() {

      Hotspot20IdentityProviderGenerator generator =
          Generators.hotspot20IdentityProvider(authRadiusId, accountingRadiusId, true, 1, 0, 0);

      generator.setId(nullValue(String.class));
      return generator;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("hotspot20IdentityProviderId", hotspot20IdentityProviderId);
    }

    @Test
    void thenShouldUpdateHotspot20IdentityProviderSuccessfully(TxCtx txCtx, CfgAction apiAction,
      @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20IdentityProvider payload) {
      validateResult(txCtx, apiAction, hotspot20IdentityProviderId, payload);
    }


    @Test
    @ApiAction(value = CfgAction.UPDATE_HOTSPOT20IDENTITY_PROVIDER, payload = @Payload("emptyPlmnAndRoamConsortium"))
    void thenShouldUpdateHotspot20IdentityProviderWithEmptyPlmnInDBSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("emptyPlmnAndRoamConsortium") com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20IdentityProvider payload) {
      validateResult(txCtx, apiAction, hotspot20IdentityProviderId, payload);
    }

    @Nested
    class ConsumeUpdateHotspot20IdentityProviderRequestOnHotspot20WifiNetworkPersistedInDbTest {

      String wifiNetworkId;

      @BeforeEach
      void givenOneHotspot20WifiNetworkPersistedInDbAndActivatedOnOneVenue(
          final Tenant tenant, final Venue venue) {
        final var hotspot20Network = network(Hotspot20Network.class).setWlan(
            wlan().setWlanSecurity(always(WlanSecurityEnum.WPA3))
        ).generate();

        final var hotspot20Operator = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20Operator().generate();
        final var hotspot20FriendlyName = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20FriendlyName().generate();
        hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));
        hotspot20FriendlyName.setOperator(hotspot20Operator);

        final var hotspot20IdentityProvider = repositoryUtil.find(Hotspot20IdentityProvider.class, hotspot20IdentityProviderId);
        final var hotspot20IdentityProvider2 = createAndSaveIdentityProvider(tenant);

        final var connectionCapability = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20ConnectionCapability().generate();
        final var networkHotspot20Settings = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkHotspot20Settings().generate();
        connectionCapability.setNetworkHotspot20Settings(networkHotspot20Settings);
        networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability));
        networkHotspot20Settings.setOperator(hotspot20Operator);

        hotspot20Network.getWlan().setNetwork(hotspot20Network);
        hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

        assert hotspot20IdentityProvider != null;
        hotspot20IdentityProvider.setNetworkHotspot20SettingsList(List.of(networkHotspot20Settings));
        hotspot20IdentityProvider.setName("tw");
        hotspot20IdentityProvider2.setNetworkHotspot20SettingsList(List.of(networkHotspot20Settings));

        repositoryUtil.createOrUpdate(hotspot20Operator);
        repositoryUtil.createOrUpdate(hotspot20FriendlyName);
        repositoryUtil.createOrUpdate(networkHotspot20Settings);
        repositoryUtil.createOrUpdate(hotspot20IdentityProvider);
        repositoryUtil.createOrUpdate(hotspot20IdentityProvider2);
        repositoryUtil.createOrUpdate(connectionCapability);
        repositoryUtil.createOrUpdate(hotspot20Network);

        hotspot20Network.getHotspot20Settings().setIdentityProviders(List.of(hotspot20IdentityProvider, hotspot20IdentityProvider2));
        repositoryUtil.createOrUpdate(networkHotspot20Settings);

        final var networkVenue = networkVenue()
            .setNetwork(always(hotspot20Network)).setVenue(always(venue)).generate();
        hotspot20Network.setNetworkVenues(List.of(networkVenue));
        repositoryUtil.createOrUpdate(networkVenue);

        wifiNetworkId = hotspot20Network.getId();
      }

      @Test
      void thenShouldUpdateNetworkBindingOnUpdateSuccessfully(TxCtx txCtx, CfgAction apiAction,
          @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20IdentityProvider payload) {
        validateResult(txCtx, apiAction, hotspot20IdentityProviderId, payload);
      }

      @Nested
      @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
      @TestMethodOrder(MethodOrderer.OrderAnnotation.class)
      class ConsumeHotspot20IdentityProviderActivationTest {

        String hotspot20IdentityProviderId3;

        @BeforeEach
        void givenOneHotspot20WifiNetworkPersistedInDbAndActivatedOnOneVenue(
            final Tenant tenant, final Venue venue) {
          hotspot20IdentityProviderId3 = createAndSaveIdentityProvider(tenant).getId();
        }

        @ApiAction.RequestParams("activatingRequestParams")
        private RequestParams activatingRequestParams() {
          return new RequestParams()
              .addPathVariable("hotspot20IdentityProviderId", hotspot20IdentityProviderId3)
              .addPathVariable("wifiNetworkId", wifiNetworkId);
        }

        @ApiAction.RequestParams("deactivatingRequestParams")
        private RequestParams deactivatingRequestParams() {
          return new RequestParams()
              .addPathVariable("hotspot20IdentityProviderId", hotspot20IdentityProviderId)
              .addPathVariable("wifiNetworkId", wifiNetworkId);
        }

        @Test
        @ApiAction(value = CfgAction.ACTIVATE_HOTSPOT20IDENTITY_PROVIDER_ON_WIFI_NETWORK,
            requestParams = @ApiAction.RequestParams("activatingRequestParams"))
        @Order(1)
        void thenActivateOnNetworkSuccessfully(TxCtx txCtx) {
          validateCmnCfgCollectorMessagesSyncToViewmodel(txCtx);
          validateDdccmCfgRequestMessages(txCtx, 3); // idp1, idp2 and activatingIdp3
        }

        @Test
        @ApiAction(value = CfgAction.DEACTIVATE_HOTSPOT20IDENTITY_PROVIDER_ON_WIFI_NETWORK,
            requestParams = @ApiAction.RequestParams("deactivatingRequestParams"))
        @Order(2)
        void thenDeactivateOnNetworkSuccessfully(TxCtx txCtx) {
          validateCmnCfgCollectorMessagesSyncToViewmodel(txCtx);
          validateDdccmCfgRequestMessages(txCtx, 1); // idp2
        }
      }

    }

  }

  @Nested
  @ApiAction(CfgAction.DELETE_HOTSPOT20IDENTITY_PROVIDER)
  class ConsumeDeleteHotspot20IdentityProviderRequestTest {

    String hotspot20IdentityProviderId;

    @BeforeEach
    void giveHotspot20IdentityProvider(final Tenant tenant) {
      // Create and saved data
      hotspot20IdentityProviderId = createAndSaveIdentityProvider(tenant).getId();

      Radius radiusAuth = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuth().generate();
      Radius radiusAcct = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcct().generate();
      repositoryUtil.createOrUpdate(radiusAuth);
      repositoryUtil.createOrUpdate(radiusAcct);
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("hotspot20IdentityProviderId", hotspot20IdentityProviderId);
    }

    @Test
    void thenShouldDeleteHotspot20IdentityProviderSuccessfully(TxCtx txCtx, CfgAction apiAction) {
      validateResult(txCtx, apiAction, hotspot20IdentityProviderId, null);
      validateCmnCfgCollectorMessagesSyncToViewmodel(txCtx);
    }
  }

  @Nested
  @TestMethodOrder(MethodOrderer.OrderAnnotation.class)
  class ConsumePreconfiguredHotspot20IdentityProviderRequestTest {

    private static final String PRECONFIGURED_ID = UUID.randomUUID().toString();

    @Payload
    private Hotspot20IdentityProviderGenerator hotspot20IdentityProvider() {
      return Generators.hotspot20IdentityProvider().setId(always(PRECONFIGURED_ID));
    }

    @ApiAction.RequestParams("preconfigureRequestParams")
    private RequestParams activatingRequestParams() {
      return new RequestParams().addPathVariable("hotspot20IdentityProviderId", PRECONFIGURED_ID);
    }

    @Order(1)
    @Test
    @ApiAction(CfgAction.ADD_PRECONFIGURED_HOTSPOT20IDENTITY_PROVIDER)
    void thenShouldAddPreconfiguredHotspot20IdentityProviderSuccessfully(TxCtx txCtx, CfgAction apiAction,
                                                            @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20IdentityProvider payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload, true);
    }

    @Order(2)
    @Test
    @ApiAction(value = CfgAction.UPDATE_PRECONFIGURED_HOTSPOT20IDENTITY_PROVIDER, requestParams = @ApiAction.RequestParams("preconfigureRequestParams"))
    void thenShouldUpdatePreconfiguredHotspot20IdentityProviderSuccessfully(TxCtx txCtx, CfgAction apiAction,
                                                                            @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20IdentityProvider payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload, true);
    }

    @Order(3)
    @Test
    @ApiAction(value = CfgAction.DELETE_PRECONFIGURED_HOTSPOT20IDENTITY_PROVIDER, requestParams = @ApiAction.RequestParams("preconfigureRequestParams"))
    void thenShouldDeletePreconfiguredHotspot20IdentityProviderSuccessfully(TxCtx txCtx, CfgAction apiAction) {
      validateResult(txCtx, apiAction, PRECONFIGURED_ID, null, true);
    }
  }

  protected List<Hotspot20Eap> hotspot20EapListToHotspot20EapList(List<com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Eap> list) {
    if ( list == null ) {
      return null;
    }

    List<Hotspot20Eap> list1 = new ArrayList<Hotspot20Eap>( list.size() );
    for ( com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Eap hotspot20Eap : list ) {
      list1.add( hotspot20EapToHotspot20Eap( hotspot20Eap ) );
    }

    return list1;
  }

  protected Hotspot20Eap hotspot20EapToHotspot20Eap(com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Eap hotspot20Eap) {
    if ( hotspot20Eap == null ) {
      return null;
    }

    Hotspot20Eap hotspot20Eap1 = new Hotspot20Eap();

    hotspot20Eap1.setMethod( hotspot20Eap.getMethod() );
    hotspot20Eap1.setAuthInfos( hotspot20AuthInfoListToHotspot20AuthInfoList( hotspot20Eap.getAuthInfos() ) );

    return hotspot20Eap1;
  }

  protected List<Hotspot20AuthInfo> hotspot20AuthInfoListToHotspot20AuthInfoList(List<com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20AuthInfo> list) {
    if ( list == null ) {
      return null;
    }

    List<Hotspot20AuthInfo> list1 = new ArrayList<Hotspot20AuthInfo>( list.size() );
    for ( com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20AuthInfo hotspot20AuthInfo : list ) {
      list1.add( hotspot20AuthInfoToHotspot20AuthInfo( hotspot20AuthInfo ) );
    }

    return list1;
  }

  protected Hotspot20AuthInfo hotspot20AuthInfoToHotspot20AuthInfo(com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20AuthInfo hotspot20AuthInfo) {
    if ( hotspot20AuthInfo == null ) {
      return null;
    }

    Hotspot20AuthInfo hotspot20AuthInfo1 = new Hotspot20AuthInfo();

    hotspot20AuthInfo1.setInfo( hotspot20AuthInfo.getInfo() );
    hotspot20AuthInfo1.setVendorId( hotspot20AuthInfo.getVendorId() );
    hotspot20AuthInfo1.setVendorType( hotspot20AuthInfo.getVendorType() );
    hotspot20AuthInfo1.setNonEapAuth( hotspot20AuthInfo.getNonEapAuth() );
    hotspot20AuthInfo1.setEapInnerAuth( hotspot20AuthInfo.getEapInnerAuth() );
    hotspot20AuthInfo1.setCredentialType( hotspot20AuthInfo.getCredentialType() );
    hotspot20AuthInfo1.setTunneledType( hotspot20AuthInfo.getTunneledType() );

    return hotspot20AuthInfo1;
  }
}
