package com.ruckus.cloud.wifi.entitylistener.impact;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.mapper.CustomizedImpactedApsContextCollectorImpl;
import com.ruckus.cloud.wifi.eda.mapper.ImpactedApsContextCollectorInterfaceImpl;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupGenerator;
import com.ruckus.cloud.wifi.kafka.publisher.FranzDeviceStatusPublisher;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.impl.ImpactedApServiceImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Import;

@WifiJpaDataTest
@Import(value = {CustomizedImpactedApsContextCollectorImpl.class,
    ImpactedApsContextCollectorInterfaceImpl.class,
    ImpactedApServiceImpl.class})
public class DdccmImpactedApAggregatedEntityListenerTestJpaTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @SpyBean
  private DdccmImpactedApAggregatedEntityListener ddccmImpactedApAggregatedEntityListener;
  @MockBean
  private FranzDeviceStatusPublisher franzDeviceStatusPublisher;

  @Test
  void testGetImpactedByApGroup(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork OpenNetwork network,
      Venue venue, @DefaultApGroup ApGroup apGroup,
      @ScheduledNetworkVenue NetworkVenue networkVenue, Ap ap) {
    NetworkApGroup networkApGroup = new NetworkApGroupGenerator()
        .setApGroup(always(apGroup))
        .setNetworkVenue(always(networkVenue))
        .generate();
    networkApGroup = repositoryUtil.createOrUpdate(networkApGroup, network.getTenant().getId(),
        randomTxId());
    networkVenue.setNetworkApGroups(List.of(networkApGroup));
    assertThat(ddccmImpactedApAggregatedEntityListener.bulkBuild(
        List.of(new TxEntity(networkVenue.getScheduler(), EntityAction.ADD)),
        Collections.emptyList(), Collections.emptyList(), null))
        .contains(ap.getId());
  }

}
