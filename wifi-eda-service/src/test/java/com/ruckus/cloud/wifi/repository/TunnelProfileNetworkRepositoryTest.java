package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("VxLanTunnelFeatureTest")
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        'e465bac6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        '7788bac6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tunnel_profile (id, name, tenant, mtu_type, mtu_size, force_fragmentation) VALUES (
        'afc284d992694d5c9d7a2fcf2289a0bd',
        'tunnel profile 1',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'MANUAL',
        1450,
        false);
    INSERT INTO network (id, name, type, tenant) VALUES (
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        'network1',
        'DPSK',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'efd0e13cc150444ca1956dd68c8999f2',
        'network2',
        'DPSK',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'c9845a491cbc43d596ffcf3b5fca5566',
        'network3',
        'PSK',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'efd0e13cc150444ca1956dd68c897788',
        'network4',
        'AAA',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tunnel_profile_network (id, tenant, network, tunnel_profile,
        network_segmentation_venue, network_segmentation_id)
        VALUES (
        '67b26672ce434dd98fa18e892ab5d4d0',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        'afc284d992694d5c9d7a2fcf2289a0bd',
        'e465bac6afb747a4987d0d0945f77221',
        '1234bac6afb747a4987d0d0945f77221'
        );
    INSERT INTO tunnel_profile_network (id, tenant, network, tunnel_profile,
        network_segmentation_venue, network_segmentation_id)
        VALUES (
        '55666672ce434dd98fa18e892ab5d4d0',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'efd0e13cc150444ca1956dd68c8999f2',
        'afc284d992694d5c9d7a2fcf2289a0bd',
        'e465bac6afb747a4987d0d0945f77221',
        '1234bac6afb747a4987d0d0945f77221'
        );
    INSERT INTO tunnel_profile_network (id, tenant, network, tunnel_profile,
        centralized_forwarding_service_venue, centralized_forwarding_service_id)
        VALUES (
        '12345672ce434dd98fa18e892ab5d4d0',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'c9845a491cbc43d596ffcf3b5fca5566',
        'afc284d992694d5c9d7a2fcf2289a0bd',
        '7788bac6afb747a4987d0d0945f77221',
        '55665a491cbc43d596ffcf3b5fca8c4f'
        );
    INSERT INTO tunnel_profile_network (id, tenant, network, tunnel_profile,
        centralized_forwarding_service_venue, centralized_forwarding_service_id)
        VALUES (
        '65432672ce434dd98fa18e892ab5d4d0',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'efd0e13cc150444ca1956dd68c897788',
        'afc284d992694d5c9d7a2fcf2289a0bd',
        '7788bac6afb747a4987d0d0945f77221',
        '55665a491cbc43d596ffcf3b5fca8c4f'
        );    
    """)
public class TunnelProfileNetworkRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String TUNNEL_PROFILE_ID = "afc284d992694d5c9d7a2fcf2289a0bd";
  private static final String VENUE_ID_1 = "e465bac6afb747a4987d0d0945f77221";
  private static final String VENUE_ID_2 = "7788bac6afb747a4987d0d0945f77221";
  private static final String NETWORK_ID_1 = "c9845a491cbc43d596ffcf3b5fca8c4f";
  private static final String NETWORK_ID_2 = "efd0e13cc150444ca1956dd68c8999f2";
  private static final String NETWORK_ID_3 = "c9845a491cbc43d596ffcf3b5fca5566";
  private static final String NETWORK_ID_4 = "efd0e13cc150444ca1956dd68c897788";
  private static final String NSG_ID = "1234bac6afb747a4987d0d0945f77221";
  private static final String CF_ID = "55665a491cbc43d596ffcf3b5fca8c4f";

  @Autowired
  private TunnelProfileNetworkRepository target;

  @Test
  public void findByTenantIdAndNetworkSegmentationVenueIdTest() {
    assertThat(target.findByTenantIdAndNetworkSegmentationVenueId(TENANT_ID, VENUE_ID_1))
        .isNotNull()
        .hasSize(2);
    assertThat(target.findByTenantIdAndNetworkSegmentationVenueId(TENANT_ID, "456"))
        .isNullOrEmpty();
  }

  @Test
  public void findByTenantIdAndCentralizedForwardingServiceVenueIdTest() {
    assertThat(target.findByTenantIdAndCentralizedForwardingServiceVenueId(TENANT_ID, VENUE_ID_2))
        .isNotNull()
        .hasSize(2);
    assertThat(target.findByTenantIdAndCentralizedForwardingServiceVenueId(TENANT_ID, "456"))
        .isNullOrEmpty();
  }
}
