package com.ruckus.cloud.wifi.integration.wifinetwork;

import static com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum.Directory;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.DirectoryServerProfileTestFixture.randomDirectoryServerProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatNoException;

import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Epdg;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfileNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.QosPriorityEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeDeleteWifiNetworkRequestTest {

  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private RepositoryUtil repositoryUtil;

  @Nested
  @ApiAction(value = CfgAction.DELETE_WIFI_NETWORK)
  class whenConsumeDeleteGuestNetworkRequest {

    private String wifiNetworkId;

    @BeforeEach
    void givenOneGuestNetworkPersistedInDb(
        final Tenant tenant, final Venue venue) {
      final GuestNetwork guestNetwork = (GuestNetwork) network(GuestNetwork.class).generate();
      final GuestPortal guestPortal = guestPortal(GuestNetworkTypeEnum.ClickThrough).generate();
      guestNetwork.setPortalServiceProfileId(randomId());
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());

      wifiNetworkId = guestNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId);
    }

    @Test
    void thenDeleteGuestWifiNetworkClickthrough(TxCtx txCtx)
        throws IOException {
      validateWifiCfgChangeMessage(txCtx);
      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);
      validateGuestNetwork();
    }

    private void validateWifiCfgChangeMessage(TxCtx txCtx) {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());
      assertThat(wifiCfgChangeMessageRecord.getPayload()).isNotNull();
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestNetwork()).findFirst().get()
                  .getGuestNetwork())
          .matches(n -> n.getId().equals(StringValue.of(wifiNetworkId)));

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> Objects.nonNull(gp.getId()));
    }

    private void validateCmnCfgCollectorMessage(TxCtx txCtx) {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.DEL)
          .matches(p -> p.getId().equals(wifiNetworkId));
    }

    private void validateActivityImpactedMessage(TxCtx txCtx) {
      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());

      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
    }

    private void validateActivityMessages(TxCtx txCtx) {
      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.DELETE_WIFI_NETWORK))
          .extracting(ConfigurationStatus::getEventDate)
          .isNotNull();
    }

    private void validateGuestNetwork() {
      final GuestNetwork deleteNetwork = repositoryUtil.find(
          GuestNetwork.class, wifiNetworkId);
      assertThat(deleteNetwork).isNull();
    }
  }

  @Nested
  @ApiAction(value = CfgAction.DELETE_WIFI_NETWORK)
  class whenConsumeDeleteNetworkWithWifiCallingProfileRequest {

    private String wifiNetworkId;
    private String wifiCallingServiceProfileId;

    @BeforeEach
    void givenOneGuestWifiNetworkPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant) {
      // Add WiFi Calling Service Profile
      WifiCallingServiceProfile wifiCallingServiceProfile = new WifiCallingServiceProfile();
      wifiCallingServiceProfile.setId(randomTxId());
      wifiCallingServiceProfile.setServiceName("wcsp1");
      Epdg epdg = new Epdg();
      epdg.setIp("*******");
      epdg.setDomain("abc.com");
      wifiCallingServiceProfile.setEPDGs(Arrays.asList(epdg));
      wifiCallingServiceProfile.setQosPriority(QosPriorityEnum.WIFICALLING_PRI_BE);
      repositoryUtil.createOrUpdate(wifiCallingServiceProfile, tenant.getId(), randomTxId());
      var wifiCallingServiceProfileFromRepo = repositoryUtil.find(WifiCallingServiceProfile.class,
          wifiCallingServiceProfile.getId());
      assertThat(wifiCallingServiceProfileFromRepo).isNotNull();

      final GuestNetwork guestNetwork = (GuestNetwork) network(GuestNetwork.class).generate();
      final GuestPortal guestPortal = guestPortal(GuestNetworkTypeEnum.HostApproval).generate();
      guestNetwork.setPortalServiceProfileId(randomId());
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      guestNetwork.getWlan().getAdvancedCustomization().setWifiCallingEnabled(true);
      guestNetwork.getWlan().getAdvancedCustomization()
          .setWifiCallingIds(Arrays.asList(wifiCallingServiceProfileFromRepo.getId()));
      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());

      // Add activation
      final var wifiCallingServiceProfileNetwork = new WifiCallingServiceProfileNetwork();
      wifiCallingServiceProfileNetwork.setId(randomId());
      wifiCallingServiceProfileNetwork.setNetwork(guestNetwork);
      wifiCallingServiceProfileNetwork.setWifiCallingServiceProfile(wifiCallingServiceProfile);
      repositoryUtil.createOrUpdate(wifiCallingServiceProfileNetwork, tenant.getId(), randomTxId());

      wifiNetworkId = guestNetwork.getId();
      wifiCallingServiceProfileId = wifiCallingServiceProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId);
    }

    @Test
    void thenDeleteWifiNetworkWithWifiCallingServiceProfile(TxCtx txCtx) {

      final var wifiCfgChangeDeleteMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(txCtx);
      assertThat(wifiCfgChangeDeleteMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());

      final GuestNetwork deleteNetwork = repositoryUtil.find(
          GuestNetwork.class, wifiNetworkId);

      assertThat(deleteNetwork).isNull();
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .as("The total operation count should be 2").hasSize(2)
              .extracting(Operations.class::cast)
              .satisfies(ops -> {
                assertThat(ops)
                    // Produced by WifiCallingServiceProfileCmnCfgCollectorOperationBuilder
                    .filteredOn(
                        op -> EsConstants.Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME.equals(
                            op.getIndex()))
                    .filteredOn(op -> op.getOpType() == OpType.MOD)
                    .as("The MOD %s operation count should be 1",
                        EsConstants.Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME).hasSize(1)
                    .singleElement()
                    .extracting(Operations::getId)
                    .as("The operation id should be %s", wifiCallingServiceProfileId)
                    .isEqualTo(wifiCallingServiceProfileId);
              }));
    }
  }

  @Nested
  @ApiAction(value = CfgAction.DELETE_WIFI_NETWORK)
  class whenConsumeDeleteGuestNetworkAdLdapRequest {

    private String wifiNetworkId;
    private String directoryServerProfileId;

    @BeforeEach
    void givenOneGuestNetworkPersistedInDb(Tenant tenant) {
      var guestNetwork = (GuestNetwork) network(GuestNetwork.class).generate();
      var guestPortal = guestPortal(Directory).generate();
      guestNetwork.setPortalServiceProfileId(randomId());
      var profile =
          repositoryUtil.createOrUpdate(
              randomDirectoryServerProfile(tenant, p -> {
              }), tenant.getId(), randomTxId());
      guestNetwork.setDirectoryServerProfile(profile);
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());

      wifiNetworkId = guestNetwork.getId();
      directoryServerProfileId = profile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", wifiNetworkId);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_DIRECTORY_PROFILE_REUSE_COMPONENT_TOGGLE)
    void thenDeleteGuestWifiNetworkWithFFEnabled(TxCtx txCtx) {
      validateDirectoryProfileEsPresence(txCtx, false);
    }

    @Test
    @FeatureFlag(disable = FlagNames.WIFI_DIRECTORY_PROFILE_REUSE_COMPONENT_TOGGLE)
    void thenDeleteGuestWifiNetworkWithFFDisabled(TxCtx txCtx) {
      validateDirectoryProfileEsPresence(txCtx, true);
    }

    private void validateDirectoryProfileEsPresence(TxCtx txCtx, boolean shouldExist) {
      final var message = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());

      assertThat(message.getPayload()).isNotNull();
      assertThat(message.getPayload().getTenantId()).isEqualTo(txCtx.getTenant());
      assertThat(message.getPayload().getRequestId()).isEqualTo(txCtx.getTxId());

      var op = message.getPayload().getOperationsList().stream()
          .filter(o -> EsConstants.Index.DIRECTORY_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .findFirst();

      if (shouldExist) {
        assertThat(op).isPresent();
        assertThat(op.get().getOpType()).isEqualTo(OpType.MOD);
        assertThat(op.get().getId()).isEqualTo(directoryServerProfileId);
      } else {
        assertThat(op).isNotPresent();
      }
    }
  }
}
