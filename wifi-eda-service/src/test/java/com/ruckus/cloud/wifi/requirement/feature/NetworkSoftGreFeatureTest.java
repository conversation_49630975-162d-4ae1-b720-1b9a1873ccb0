package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.SoftGreProfileNetworkVenueActivationRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class NetworkSoftGreFeatureTest {

  @MockBean
  private SoftGreProfileNetworkVenueActivationRepository repository;

  @SpyBean
  private NetworkSoftGreFeature unit;

  @Nested
  class WhenConfigSoftGre {

    @Test
    @FeatureFlag(disable = {WIFI_R370_TOGGLE})
    void givenR370Disabled(Network network) {
      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE})
    void givenSoftGreDisabled(Network network) {
      network.setNetworkVenues(Collections.emptyList());

      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE})
    void givenSoftGreEnabled(Venue venue, Network network) {
      var nv = new NetworkVenue();
      nv.setVenue(venue);
      network.setNetworkVenues(List.of(nv));

      when(repository.findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
          anyString(), anyString(), anyString())).thenReturn(
          Optional.of(new SoftGreProfileNetworkVenueActivation()));

      BDDAssertions.then(unit.test(network)).isTrue();
    }
  }
}
