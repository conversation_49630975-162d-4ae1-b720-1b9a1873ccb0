package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomIp;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity.Fields;
import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelMtuTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelNetworkSegmentTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.PinProfileNetworkMappingRepository;
import com.ruckus.cloud.wifi.repository.PinProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.repository.SdLanProfileNetworkMappingRepository;
import com.ruckus.cloud.wifi.repository.SdLanProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.utils.FeatureRolesUtils;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Tag("TunnelServiceProfileTest")
@WifiUnitTest
class TunnelProfileCfgCollectorOperationBuilderTest {

  @SpyBean
  private TunnelProfileCfgCollectorOperationBuilder tunnelProfileCfgCollectorOperationBuilder;
  @MockBean
  private SdLanProfileNetworkMappingRepository sdLanProfileNetworkMappingRepository;
  @MockBean
  private PinProfileNetworkMappingRepository pinProfileNetworkMappingRepository;
  @MockBean
  private SdLanProfileRegularSettingRepository sdLanProfileRegularSettingRepository;
  @MockBean
  private PinProfileRegularSettingRepository pinProfileRegularSettingRepository;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void testGetEntityClass() {
    assertThat(tunnelProfileCfgCollectorOperationBuilder.entityClass())
        .isEqualTo(TunnelProfile.class);
  }

  @Nested
  class BuildConfigTest {

    @Test
    void givenEntityActionIsDelete() {
      Operations operations = tunnelProfileCfgCollectorOperationBuilder
          .build(new TxEntity<>(new TunnelProfile(randomId()), EntityAction.DELETE),
              emptyTxChanges()).get(0);

      assertThat(operations.getDocCount())
          .isZero();
    }

    @Test
    void givenTunnelProfileWithoutRelation() {
      var tunnelProfile = Generators.tunnelProfile().generate();
      tunnelProfile.setTenant(new Tenant(randomId()));
      var builder = Operations.newBuilder();
      tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);

      assertThat(builder.build().getDocMap())
          .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
          .containsEntry(Key.NAME, ValueUtils.stringValue(tunnelProfile.getName()))
          .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tunnelProfile.getTenant().getId()))
          .containsEntry(Key.MTU_TYPE,
              ValueUtils.stringValue(tunnelProfile.getMtuType().toString()))
          .containsEntry(Key.MTU_SIZE, ValueUtils.nullValue())
          .containsEntry(Key.FORCE_FRAGMENTATION,
              ValueUtils.boolValue(tunnelProfile.getForceFragmentation()))
          .containsEntry(Key.NETWORK_IDS, ValueUtils.listValue(List.of()))
          .containsEntry(Key.NETWORK_SEGMENTATION_IDS, ValueUtils.listValue(List.of()))
          .containsEntry(Key.AGE_TIME_MINUTES,
              ValueUtils.numberValue(tunnelProfile.getAgeTimeMinutes()))
          .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(tunnelProfile.getType().name()))
          .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
          .containsEntry(Key.MTU_REQUEST_TIMEOUT,
              ValueUtils.numberValue(tunnelProfile.getMtuRequestTimeout()))
          .containsEntry(Key.MTU_REQUEST_RETRY,
              ValueUtils.numberValue(tunnelProfile.getMtuRequestRetry()))
          .containsEntry(Key.KEEP_ALIVE_RETRY,
              ValueUtils.numberValue(tunnelProfile.getKeepAliveRetry()))
          .containsEntry(Key.KEEP_ALIVE_INTERVAL,
              ValueUtils.numberValue(tunnelProfile.getKeepAliveInterval()))
          .containsEntry(Key.DESTINATION_IP_ADDRESS, ValueUtils.nullValue())
          .containsEntry(Key.IS_TEMPLATE, ValueUtils.boolValue(false))
          .containsEntry(Key.IS_TEMPLATE_INSTANCE, ValueUtils.boolValue(false));
    }

    @Test
    void givenTunnelProfileWithPinRelation() {
      final var tunnelProfile = new TunnelProfile(randomId());
      tunnelProfile.setName(randomName(16));
      tunnelProfile.setTenant(new Tenant(randomId()));
      tunnelProfile.setMtuType(TunnelMtuTypeEnum.AUTO);
      tunnelProfile.setForceFragmentation(false);
      tunnelProfile.setAgeTimeMinutes((short) 333);
      tunnelProfile.setType(TunnelNetworkSegmentTypeEnum.VXLAN);

      final var network1 = new Network(randomId());
      final var network2 = new Network(randomId());
      final var network3 = new Network(randomId());

      final var pinProfileId = randomId();

      final var rs1 = new PinProfileRegularSetting(randomId());
      rs1.setTunnelProfile(tunnelProfile);
      rs1.setVenue(new Venue(randomId()));
      rs1.setPinProfile(new PinProfile(pinProfileId));

      final var rs2 = new PinProfileRegularSetting(randomId());
      rs2.setTunnelProfile(tunnelProfile);
      rs2.setVenue(new Venue(randomId()));
      rs2.setPinProfile(new PinProfile(pinProfileId));

      final var rs3 = new PinProfileRegularSetting(randomId());
      rs3.setTunnelProfile(tunnelProfile);
      rs3.setVenue(new Venue(randomId()));
      rs3.setPinProfile(new PinProfile(pinProfileId));

      final var networkMapping1 = new PinProfileNetworkMapping(randomId());
      networkMapping1.setNetwork(network1);
      networkMapping1.setPinProfileRegularSetting(rs1);
      final var networkMapping2 = new PinProfileNetworkMapping(randomId());
      networkMapping2.setNetwork(network2);
      networkMapping2.setPinProfileRegularSetting(rs2);
      final var networkMapping3 = new PinProfileNetworkMapping(randomId());
      networkMapping3.setNetwork(network3);
      networkMapping3.setPinProfileRegularSetting(rs3);

      doReturn(List.of(rs1, rs2, rs3))
          .when(pinProfileRegularSettingRepository)
          .findByTenantIdAndTunnelProfileId(txCtxExtension.getTenantId(), tunnelProfile.getId());

      doReturn(List.of(networkMapping1, networkMapping2, networkMapping3))
          .when(pinProfileNetworkMappingRepository)
          .findByTenantIdAndPinProfileRegularSettingIdIn(txCtxExtension.getTenantId(),
              List.of(rs1.getId(), rs2.getId(), rs3.getId()));

      final var builder = Operations.newBuilder();
      tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);

      assertThat(builder.build().getDocMap())
          .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
          .containsEntry(Key.NAME, ValueUtils.stringValue(tunnelProfile.getName()))
          .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tunnelProfile.getTenant().getId()))
          .containsEntry(Key.MTU_TYPE,
              ValueUtils.stringValue(tunnelProfile.getMtuType().toString()))
          .containsEntry(Key.MTU_SIZE, ValueUtils.nullValue())
          .containsEntry(Key.FORCE_FRAGMENTATION,
              ValueUtils.boolValue(tunnelProfile.getForceFragmentation()))
          .hasEntrySatisfying(Key.NETWORK_IDS,
              networkIdListValue -> assertThat(networkIdListValue.getListValue())
                  .extracting(ListValue::getValuesList,
                      InstanceOfAssertFactories.list(Value.class))
                  .hasSize(3)
                  .extracting(Value::getStringValue)
                  .containsExactlyInAnyOrder(network1.getId(), network2.getId(), network3.getId()))
          .containsEntry(Key.NETWORK_SEGMENTATION_IDS,
              ValueUtils.listValue(List.of(ValueUtils.stringValue(pinProfileId))))
          .containsEntry(Key.AGE_TIME_MINUTES,
              ValueUtils.numberValue(tunnelProfile.getAgeTimeMinutes()))
          .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(tunnelProfile.getType().name()))
          .containsEntry(Key.CENTRALIZED_FORWARDING_IDS, ValueUtils.listValue(List.of()))
          .containsEntry(Key.IS_TEMPLATE, ValueUtils.boolValue(false))
          .containsEntry(Key.IS_TEMPLATE_INSTANCE, ValueUtils.boolValue(false));

    }

    @Test
    void givenTunnelProfileWithSdLanRelation() {
      final var tunnelProfile = new TunnelProfile(randomId());
      tunnelProfile.setName(randomName(16));
      tunnelProfile.setTenant(new Tenant(randomId()));
      tunnelProfile.setMtuType(TunnelMtuTypeEnum.AUTO);
      tunnelProfile.setForceFragmentation(false);
      tunnelProfile.setAgeTimeMinutes((short) 333);
      tunnelProfile.setType(TunnelNetworkSegmentTypeEnum.VLAN_VXLAN);

      final var network1 = new Network(randomId());
      final var network2 = new Network(randomId());
      final var network3 = new Network(randomId());

      final var sdLanProfileId = randomId();

      final var rs1 = new SdLanProfileRegularSetting(randomId());
      rs1.setTunnelProfile(tunnelProfile);
      rs1.setVenue(new Venue(randomId()));
      rs1.setSdLanProfile(new SdLanProfile(sdLanProfileId));

      final var rs2 = new SdLanProfileRegularSetting(randomId());
      rs2.setTunnelProfile(tunnelProfile);
      rs2.setVenue(new Venue(randomId()));
      rs2.setSdLanProfile(new SdLanProfile(sdLanProfileId));

      final var rs3 = new SdLanProfileRegularSetting(randomId());
      rs3.setTunnelProfile(tunnelProfile);
      rs3.setVenue(new Venue(randomId()));
      rs3.setSdLanProfile(new SdLanProfile(sdLanProfileId));

      final var networkMapping1 = new SdLanProfileNetworkMapping(randomId());
      networkMapping1.setNetwork(network1);
      networkMapping1.setSdLanProfileRegularSetting(rs1);
      final var networkMapping2 = new SdLanProfileNetworkMapping(randomId());
      networkMapping2.setNetwork(network2);
      networkMapping2.setSdLanProfileRegularSetting(rs2);
      final var networkMapping3 = new SdLanProfileNetworkMapping(randomId());
      networkMapping3.setNetwork(network3);
      networkMapping3.setSdLanProfileRegularSetting(rs3);

      doReturn(List.of(rs1, rs2, rs3))
          .when(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndTunnelProfileId(txCtxExtension.getTenantId(), tunnelProfile.getId());

      doReturn(List.of(networkMapping1, networkMapping2, networkMapping3))
          .when(sdLanProfileNetworkMappingRepository)
          .findByTenantIdAndSdLanProfileRegularSettingIdIn(txCtxExtension.getTenantId(),
              List.of(rs1.getId(), rs2.getId(), rs3.getId()));

      final var builder = Operations.newBuilder();
      tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);

      assertThat(builder.build().getDocMap())
          .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
          .containsEntry(Key.NAME, ValueUtils.stringValue(tunnelProfile.getName()))
          .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tunnelProfile.getTenant().getId()))
          .containsEntry(Key.MTU_TYPE,
              ValueUtils.stringValue(tunnelProfile.getMtuType().toString()))
          .containsEntry(Key.MTU_SIZE, ValueUtils.nullValue())
          .containsEntry(Key.FORCE_FRAGMENTATION,
              ValueUtils.boolValue(tunnelProfile.getForceFragmentation()))
          .hasEntrySatisfying(Key.NETWORK_IDS,
              networkIdListValue -> assertThat(networkIdListValue.getListValue())
                  .extracting(ListValue::getValuesList,
                      InstanceOfAssertFactories.list(Value.class))
                  .hasSize(3)
                  .extracting(Value::getStringValue)
                  .containsExactlyInAnyOrder(network1.getId(), network2.getId(), network3.getId()))
          .containsEntry(Key.NETWORK_SEGMENTATION_IDS, ValueUtils.listValue(List.of()))
          .containsEntry(Key.AGE_TIME_MINUTES,
              ValueUtils.numberValue(tunnelProfile.getAgeTimeMinutes()))
          .containsEntry(Key.NETWORK_SEGMENT_TYPE, ValueUtils.stringValue(tunnelProfile.getType().name()))
          .containsEntry(Key.CENTRALIZED_FORWARDING_IDS,
              ValueUtils.listValue(List.of(ValueUtils.stringValue(sdLanProfileId))));
    }

    @Test
    void givenTunnelProfileHasTemplateId_isTemplateInstanceShouldBeTrue() {
      var tunnelProfile = new TunnelProfile();
      tunnelProfile.setId(randomId());
      tunnelProfile.setName(randomName(16));
      tunnelProfile.setTenant(new Tenant(randomId()));
      tunnelProfile.setTemplateId(randomId());

      var builder = Operations.newBuilder();
      tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);

      assertThat(builder.build().getDocMap())
          .containsEntry(Key.ID, ValueUtils.stringValue(tunnelProfile.getId()))
          .containsEntry(Key.NAME, ValueUtils.stringValue(tunnelProfile.getName()))
          .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tunnelProfile.getTenant().getId()))
          .containsEntry(Key.IS_TEMPLATE, ValueUtils.boolValue(false))
          .containsEntry(Key.IS_TEMPLATE_INSTANCE, ValueUtils.boolValue(true));
    }

    @Test
    void givenTunnelProfileWithIpsecProfile() {
      var tunnelProfile = Generators.tunnelProfile().generate();
      tunnelProfile.setIpsecProfile(new IpsecProfile(randomId()));
      var builder = Operations.newBuilder();
      tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);
      assertThat(builder.build().getDocMap())
          .containsEntry(Key.IPSEC_PROFILE_ID, ValueUtils.stringValue(tunnelProfile.getIpsecProfile().getId()));
    }

    @FeatureFlag(disable = FlagNames.EDGE_NAT_TRAVERSAL_PHASE1_TOGGLE)
    @Nested
    class GivenEdgeNatTraversalPhase1ToggleDisabled {

      @Test
      void thenNatTraversalEnabledShouldNotBeProduced() {
        final var tunnelProfile = Generators.tunnelProfile()
            .setTenant(Generators.tenant())
            .setNatTraversalEnabled(alwaysTrue()).generate();

        final var builder = Operations.newBuilder();
        tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);

        assertThat(builder.build().getDocMap())
            .doesNotContainKey(Key.NAT_TRAVERSAL_ENABLED);
      }
    }

    @FeatureFlag(enable = FlagNames.EDGE_NAT_TRAVERSAL_PHASE1_TOGGLE)
    @Nested
    class GivenEdgeNatTraversalPhase1ToggleEnabled {

      @FeatureRole(FeatureRolesUtils.EDGE_NAT_TRAVERSAL)
      @Nested
      class WithEdgeNatTraversalFeatureRole {

        @Test
        void thenNatTraversalEnabledShouldBeProduced() {
          final var tunnelProfile = Generators.tunnelProfile()
              .setTenant(Generators.tenant())
              .setNatTraversalEnabled(alwaysTrue()).generate();

          final var builder = Operations.newBuilder();
          tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);

          assertThat(builder.build().getDocMap())
              .containsEntry(Key.NAT_TRAVERSAL_ENABLED, ValueUtils.boolValue(true));
        }
      }

      @Nested
      class WithoutEdgeNatTraversalFeatureRole {

        @Test
        void thenNatTraversalEnabledShouldBeAlwaysProducedAsFalse() {
          final var tunnelProfile = Generators.tunnelProfile()
              .setTenant(Generators.tenant())
              .setNatTraversalEnabled(alwaysTrue()).generate();

          final var builder = Operations.newBuilder();
          tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);

          assertThat(builder.build().getDocMap())
              .containsEntry(Key.NAT_TRAVERSAL_ENABLED, ValueUtils.boolValue(false));
        }
      }
    }

    @Test
    void givenTunnelProfileWithDestinationIpAddress() {
      final var tunnelProfile = Generators.tunnelProfile()
          .setTenant(Generators.tenant())
          .setTunnelType(always(TunnelTypeEnum.L2GRE))
          .setDestinationIpAddress(always(randomIp()))
          .generate();

      final var builder = Operations.newBuilder();
      tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);

      assertThat(builder.build().getDocMap())
          .containsEntry(Key.TUNNEL_TYPE,
              ValueUtils.stringValue(TunnelTypeEnum.L2GRE.name()))
          .containsEntry(Key.DESTINATION_IP_ADDRESS,
              ValueUtils.stringValue(tunnelProfile.getDestinationIpAddress()));
    }

    @Test
    void givenTunnelProfileWithoutDestinationIpAddress() {
      final var tunnelProfile = Generators.tunnelProfile()
          .setTenant(Generators.tenant())
          .setTunnelType(always(TunnelTypeEnum.VXLAN_GPE))
          .generate();

      final var builder = Operations.newBuilder();
      tunnelProfileCfgCollectorOperationBuilder.config(builder, tunnelProfile, EntityAction.ADD);

      assertThat(builder.build().getDocMap())
          .containsEntry(Key.TUNNEL_TYPE,
              ValueUtils.stringValue(TunnelTypeEnum.VXLAN_GPE.name()))
          .containsEntry(Key.DESTINATION_IP_ADDRESS, ValueUtils.nullValue());
    }
  }


  @Test
  void testGetIndex() {
    assertThat(tunnelProfileCfgCollectorOperationBuilder.index()).isEqualTo(
        Index.TUNNEL_PROFILE_INDEX_NAME);
  }

  @Nested
  class HasModificationTest {

    @Test
    void givenEntityActionIsNotModify() {
      assertThat(
          tunnelProfileCfgCollectorOperationBuilder.hasModification(EntityAction.ADD,
              Collections.emptySet(), false))
          .isTrue();
    }

    @Test
    void givenGivenFieldsInExportedFields() {
      assertThat(tunnelProfileCfgCollectorOperationBuilder.hasModification(EntityAction.MODIFY,
          Set.of(Key.NAME, Fields.UPDATEDDATE), false))
          .isTrue();
    }

    @Test
    void givenGivenFieldsNotInExportedFields() {
      assertThat(tunnelProfileCfgCollectorOperationBuilder.hasModification(EntityAction.MODIFY,
          Set.of(Fields.UPDATEDDATE), false))
          .isTrue();
    }
  }
}
