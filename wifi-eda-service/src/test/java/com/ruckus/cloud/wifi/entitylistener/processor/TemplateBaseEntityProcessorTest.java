package com.ruckus.cloud.wifi.entitylistener.processor;

import static com.ruckus.cloud.wifi.utils.TemplateUtils.TemplateContext;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractTenantAwareTemplateBaseEntity;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.utils.TemplateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class, TxCtxExtension.class})
class TemplateBaseEntityProcessorTest {

  private TemplateBaseEntityProcessor processor;

  @Mock
  private AbstractTenantAwareTemplateBaseEntity mockTemplateEntity;

  @Mock
  private BaseEntity mockBaseEntity;

  @BeforeEach
  void setUp() {
    processor = new TemplateBaseEntityProcessor();
  }

  @Test
  void testSupports_WithTemplateEntity_ShouldReturnTrue() {
    // Given
    BaseEntity templateEntity = mockTemplateEntity;

    // When
    boolean result = processor.supports(templateEntity);

    // Then
    assertThat(result).isTrue();
  }

  @Test
  void testSupports_WithNonTemplateEntity_ShouldReturnFalse() {
    // Given
    BaseEntity nonTemplateEntity = mockBaseEntity;

    // When
    boolean result = processor.supports(nonTemplateEntity);

    // Then
    assertThat(result).isFalse();
  }

  @Test
  void testSupports_WithNullEntity_ShouldReturnFalse() {
    // Given
    BaseEntity nullEntity = null;

    // When
    boolean result = processor.supports(nullEntity);

    // Then
    assertThat(result).isFalse();
  }

  @Test
  void testProcess_WithNonTemplateEntity_ShouldReturnEarly() {
    // Given
    when(mockTemplateEntity.getIsTemplate()).thenReturn(false);

    // When
    processor.process(mockTemplateEntity);

    // Then
    verify(mockTemplateEntity).getIsTemplate();
    verify(mockTemplateEntity, org.mockito.Mockito.never()).getTemplateContext();
    verify(mockTemplateEntity, org.mockito.Mockito.never()).setTemplateContext(org.mockito.ArgumentMatchers.anyString());
  }

  @Test
  void testProcess_WithTemplateEntityWithRecContext_ShouldReturnEarly() {
    // Given
    when(mockTemplateEntity.getIsTemplate()).thenReturn(true);
    when(mockTemplateEntity.getTemplateContext()).thenReturn(TemplateContext.REC.name());

    // When
    processor.process(mockTemplateEntity);

    // Then
    verify(mockTemplateEntity).getIsTemplate();
    verify(mockTemplateEntity, org.mockito.Mockito.times(1)).getTemplateContext();
    verify(mockTemplateEntity, org.mockito.Mockito.never()).setTemplateContext(org.mockito.ArgumentMatchers.anyString());
  }

  @Test
  void testProcess_WithTemplateEntityWithMspContext_ShouldReturnEarly() {
    // Given
    when(mockTemplateEntity.getIsTemplate()).thenReturn(true);
    when(mockTemplateEntity.getTemplateContext()).thenReturn(TemplateContext.MSP.name());

    // When
    processor.process(mockTemplateEntity);

    // Then
    verify(mockTemplateEntity).getIsTemplate();
    verify(mockTemplateEntity, org.mockito.Mockito.times(2)).getTemplateContext();
    verify(mockTemplateEntity, org.mockito.Mockito.never()).setTemplateContext(org.mockito.ArgumentMatchers.anyString());
  }

  @Test
  void testProcess_WithTemplateEntityWithNoneContext_ShouldSetMspContext() {
    // Given
    when(mockTemplateEntity.getIsTemplate()).thenReturn(true);
    when(mockTemplateEntity.getTemplateContext()).thenReturn(TemplateContext.NONE.name());

    // When
    processor.process(mockTemplateEntity);

    // Then
    verify(mockTemplateEntity).getIsTemplate();
    verify(mockTemplateEntity, org.mockito.Mockito.times(2)).getTemplateContext();
    verify(mockTemplateEntity).setTemplateContext(TemplateContext.MSP.name());
  }

  @Test
  void testProcess_WithTemplateEntityWithNullContext_ShouldSetMspContext() {
    // Given
    when(mockTemplateEntity.getIsTemplate()).thenReturn(true);
    when(mockTemplateEntity.getTemplateContext()).thenReturn(null);

    // When
    processor.process(mockTemplateEntity);

    // Then
    verify(mockTemplateEntity).getIsTemplate();
    verify(mockTemplateEntity, org.mockito.Mockito.times(2)).getTemplateContext();
    verify(mockTemplateEntity).setTemplateContext(TemplateContext.MSP.name());
  }

  @Test
  void testProcess_WithTemplateEntityWithEmptyContext_ShouldSetMspContext() {
    // Given
    when(mockTemplateEntity.getIsTemplate()).thenReturn(true);
    when(mockTemplateEntity.getTemplateContext()).thenReturn("");

    // When
    processor.process(mockTemplateEntity);

    // Then
    verify(mockTemplateEntity).getIsTemplate();
    verify(mockTemplateEntity, org.mockito.Mockito.times(2)).getTemplateContext();
    verify(mockTemplateEntity).setTemplateContext(TemplateContext.MSP.name());
  }

  @Test
  void testProcess_WithTemplateEntityWithUnknownContext_ShouldSetMspContext() {
    // Given
    when(mockTemplateEntity.getIsTemplate()).thenReturn(true);
    when(mockTemplateEntity.getTemplateContext()).thenReturn("UNKNOWN_CONTEXT");

    // When
    processor.process(mockTemplateEntity);

    // Then
    verify(mockTemplateEntity).getIsTemplate();
    verify(mockTemplateEntity, org.mockito.Mockito.times(2)).getTemplateContext();
    verify(mockTemplateEntity).setTemplateContext(TemplateContext.MSP.name());
  }

  @Test
  void testProcess_Integration_CompleteFlow() {
    // Given - Template entity with NONE context
    when(mockTemplateEntity.getIsTemplate()).thenReturn(true);
    when(mockTemplateEntity.getTemplateContext()).thenReturn(TemplateContext.NONE.name());

    // When
    processor.process(mockTemplateEntity);

    // Then - Should set MSP context
    verify(mockTemplateEntity).getIsTemplate();
    verify(mockTemplateEntity, org.mockito.Mockito.times(2)).getTemplateContext();
    verify(mockTemplateEntity).setTemplateContext(TemplateContext.MSP.name());

    // Reset mock for second test
    org.mockito.Mockito.reset(mockTemplateEntity);
    
    // Given - Template entity with REC context
    when(mockTemplateEntity.getIsTemplate()).thenReturn(true);
    when(mockTemplateEntity.getTemplateContext()).thenReturn(TemplateContext.REC.name());

    // When
    processor.process(mockTemplateEntity);

    // Then - Should not change context
    verify(mockTemplateEntity).getIsTemplate();
    verify(mockTemplateEntity, org.mockito.Mockito.times(1)).getTemplateContext();
    verify(mockTemplateEntity, org.mockito.Mockito.never()).setTemplateContext(org.mockito.ArgumentMatchers.anyString());
  }
} 