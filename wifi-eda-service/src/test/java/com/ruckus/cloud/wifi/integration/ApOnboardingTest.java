package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.entitlement.operation.eda.EdaDevice;
import com.ruckus.cloud.entitlement.operation.eda.EdaOperation;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.Device;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceConnectionStatusProto.DeviceConnectionStatus;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceConnectionStatusProto.DeviceConnectionStatus.Status;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceRegistrationPolicyProto.DeviceRegistrationPolicy.Policy;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceRegistrationProto.DeviceRegistration;
import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceRegistrationProto.DeviceRegistration.Type;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRequest;
import com.ruckus.cloud.wifi.proto.DeviceRealtimeDataTypeEnum;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ApOnboardingTest {

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Test
  void testApOnboardingAsync(@DefaultApGroup ApGroup apGroup) throws Exception {
    final var serialNumber = randomSerialNumber();
    var requestId = addAp(serialNumber, apGroup);
    verifyApAdded(requestId, serialNumber);

    final var mac = "00:11:22:33:44:55";
    final var model = "R730";
    final var ap = repositoryUtil.find(Ap.class, serialNumber);

    apRegistration(ap, model, mac);
    verifyApRegistrationAsync(serialNumber, mac, model);

    apOnline(ap);
    verifyApOnlineAsync();
  }

  private String addAp(String serialNumber, ApGroup apGroup) {
    final var requestId = randomTxId();
    final var apRequest = new ApRequest();
    apRequest.setSerialNumber(serialNumber);
    apRequest.setVenueId(apGroup.getVenue().getId());
    apRequest.setName(randomName());

    final RequestParams requestParams = new RequestParams();
    requestParams.addPathVariable("venueId", apGroup.getVenue().getId());

    messageUtil.sendWifiCfgRequest(apGroup.getTenant().getId(), requestId, CfgAction.ADD_AP_V1,
        txCtxExtension.getUserName(), requestParams, apRequest);
    return requestId;
  }

  private String apRegistration(Ap ap, String model, String mac) {
    final var requestId = randomTxId();
    messageUtil.sendDeviceRegistration(txCtxExtension.getTenantId(),
        requestId,
        DeviceRegistration.newBuilder()
            .setSerial(ap.getId())
            .setModel(model)
            .setMac(mac)
            .setTenantId(ap.getTenant().getId())
            .setType(Type.AP)
            .build());
    return requestId;
  }

  private String apOnline(Ap ap) {
    final var requestId = randomTxId();
    messageUtil.sendApStatus(txCtxExtension.getTenantId(), requestId,
        DeviceConnectionStatus.newBuilder()
            .setSerial(ap.getId())
            .setStatus(Status.ONLINE)
            .setTenantId(ap.getTenant().getId())
            .build());
    return requestId;
  }

  private void verifyApAdded(String requestId, String serialNumber) {
    final var ap = repositoryUtil.find(Ap.class, serialNumber);
    assertThat(ap).isNotNull();

    final var deviceRegistrationPolicyMessage =
        messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), requestId);

    assertThat(deviceRegistrationPolicyMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .satisfies(policy -> assertSoftly(softly -> {
          softly.assertThat(policy.getId()).isEqualTo(serialNumber);
          softly.assertThat(policy.getPolicy()).isEqualTo(Policy.PENDING);
        }));

    final var drsMessage = messageCaptors.getDrsMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId);

    assertThat(drsMessage).isNotNull();
    assertThat(drsMessage.getPayload().getDeviceCreateRequest().getDevicesList())
        .hasSize(1)
        .singleElement()
        .extracting(Device::getSerialNumber)
        .isEqualTo(serialNumber);

    final var entMessage = messageCaptors.getEntitlementDeviceOperationMessageCaptor()
        .getValue(txCtxExtension.getTenantId(), requestId);
    assertThat(entMessage).isNotNull();
    assertThat(entMessage.getPayload().getOperationList())
        .hasSize(1)
        .singleElement()
        .extracting(EdaOperation::getDevice)
        .extracting(EdaDevice::getSerialNumber)
        .isEqualTo(serialNumber);

    final var ddccmMessage =
        messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), requestId);

    assertThat(ddccmMessage).isNotNull();
    assertThat(ddccmMessage.getPayload().getOperationsList())
        .filteredOn(e -> e.hasAp())
        .hasSize(1)
        .singleElement()
        .extracting(Operation::getAp)
        .satisfies(e -> assertSoftly(softly -> {
          softly.assertThat(e.getId()).isEqualTo(serialNumber);
        }));
  }

  private void verifyApRegistration(Optional<String> requestId, String serialNumber, String mac,
      String model) {
    final var deviceRegistrationPolicyMessage = requestId.isPresent() ?
        messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), requestId.get()) :
        messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
            .getValue(txCtxExtension.getTenantId());

    assertThat(deviceRegistrationPolicyMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .satisfies(policy -> assertSoftly(softly -> {
          softly.assertThat(policy.getId()).isEqualTo(serialNumber);
          softly.assertThat(policy.getPolicy()).isEqualTo(Policy.APPROVED);
        }));

    final var ddccmMessage = requestId.isPresent() ?
        messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), requestId.get()) :
        messageCaptors.getDdccmMessageCaptor()
            .getValue(txCtxExtension.getTenantId());
    assertThat(ddccmMessage).isNotNull();
    assertThat(ddccmMessage.getPayload().getOperationsList())
        .filteredOn(e -> e.hasAp())
        .hasSize(1)
        .singleElement()
        .extracting(Operation::getAp)
        .satisfies(ap -> assertSoftly(softly -> {
          softly.assertThat(ap.getId()).isEqualTo(serialNumber);
          softly.assertThat(ap.getMac().getValue()).isEqualTo(mac);
          softly.assertThat(ap.getModel().getName()).isEqualTo(model);
        }));
  }

  private void verifyApRegistrationAsync(String serialNumber, String mac, String model) {
    final var wifiDeviceRegistAsyncJobMessage = messageCaptors.getWifiDeviceRegistAsyncJobMessageCaptor()
        .getValue(txCtxExtension.getTenantId());

    assertThat(wifiDeviceRegistAsyncJobMessage).isNotNull();
    assertThat(wifiDeviceRegistAsyncJobMessage.getPayload().getDeviceRealtimeDataJob().getType())
        .isEqualTo(DeviceRealtimeDataTypeEnum.ApRealtimeDataTypeEnum_DEVICE_REGISTRATION);

    verifyApRegistration(Optional.empty(), serialNumber, mac, model);
  }

  private void verifyApOnline(Optional<String> requestId) {
    final var kairosMessage = requestId.isPresent() ?
        messageCaptors.getKairosMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), requestId.get()) :
        messageCaptors.getKairosMessageCaptor().getValue(txCtxExtension.getTenantId());
    assertThat(kairosMessage).isNotNull();

    final var notificationMessage = requestId.isPresent() ?
        messageCaptors.getDeviceNotificationMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), requestId.get()) :
        messageCaptors.getDeviceNotificationMessageCaptor().getValue(txCtxExtension.getTenantId());
    assertThat(notificationMessage).isNotNull();

    final var franzDeviceStatusMessage = requestId.isPresent() ?
        messageCaptors.getFranzDeviceStatusMessageCaptor()
            .getValue(txCtxExtension.getTenantId(), requestId.get()) :
        messageCaptors.getFranzDeviceStatusMessageCaptor().getValue(txCtxExtension.getTenantId());
    assertThat(franzDeviceStatusMessage).isNotNull();
  }

  private void verifyApOnlineAsync() {
    final var wifiDeviceStatusMessage = messageCaptors.getWifiDeviceStatusAsyncJobMessageCaptor()
        .getValue(txCtxExtension.getTenantId());
    assertThat(wifiDeviceStatusMessage).isNotNull();
    assertThat(wifiDeviceStatusMessage.getPayload().getDeviceRealtimeDataJob().getType())
        .isEqualTo(DeviceRealtimeDataTypeEnum.ApRealtimeDataTypeEnum_CONNECTION_STATUS);

    verifyApOnline(Optional.empty());
  }
}
