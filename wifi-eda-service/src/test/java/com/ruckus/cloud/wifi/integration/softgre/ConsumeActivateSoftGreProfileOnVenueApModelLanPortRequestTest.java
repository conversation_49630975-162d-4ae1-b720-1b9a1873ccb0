package com.ruckus.cloud.wifi.integration.softgre;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.AP_MODEL;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.AP_SERIAL_NUMBER;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.AP_SERIAL_NUMBERS;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.PORT_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.VENUE_ID;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice.ImpactDevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.LanPortSoftGreProfileSettingsGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.LanPortSoftGreProfileSettings;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SoftGreProfileTest")
@WifiIntegrationTest
@FeatureFlag(
    enable = {
      WIFI_ETHERNET_SOFTGRE_TOGGLE,
      ACX_UI_ETHERNET_TOGGLE,
    })
class ConsumeActivateSoftGreProfileOnVenueApModelLanPortRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;
  @Autowired private LanPortAdoptionDataHelper dataHelper;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeActivateSoftGreProfileToVenueApModelLanPortRequestTest {

    private String softGreProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "2";
    private String venueLanPortId;

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        Generators.lanPortSoftGreProfileSettings();

    @BeforeEach
    void givenOnePersistedInDb(Venue venue, Tenant tenant) {
      var portData1 = dataHelper.createVenueLanPortDataWithAdoption(venue, apModel, portId, 3);

      var softGreProfile =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      softGreProfileId = softGreProfile.getId();
      venueId = venue.getId();
      venueLanPortId = portData1.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {
      validateActivateSoftGreProfileToVenueApModelLanPortResult(
          CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
          venueId,
          softGreProfileId,
          payload,
          venueLanPortId,
          apModel,
          portId,
          false);
    }
  }

  @Nested
  class ConsumeActivateSoftGreProfileWithExistActivationAndChangeDhcpSettingTest {

    private String softGreProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "2";
    private String venueLanPortId;

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        Generators.lanPortSoftGreProfileSettings();

    @BeforeEach
    void givenOnePersistedInDb(Venue venue, Tenant tenant) {
      var softGreProfile1 = dataHelper.createSoftGreProfile(tenant);
      var portData1 =
          dataHelper.createVenueLanPortDataWithAdoption(
              venue, apModel, portId, 3, of(getSoftGreLanPortActivation(softGreProfile1)));
      venueLanPortId = portData1.port().getId();
      venueId = venue.getId();
      softGreProfileId = softGreProfile1.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {
      validateActivateSoftGreProfileToVenueApModelLanPortResult(
          CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
          venueId,
          softGreProfileId,
          payload,
          venueLanPortId,
          apModel,
          portId,
          false);
    }
  }

  @Nested
  class ConsumeActivateSoftGreProfileWithExistActivationAndChangeSoftGreProfileTest {

    private String softGreProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "2";
    private String venueLanPortId;

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        Generators.lanPortSoftGreProfileSettings();

    @BeforeEach
    void givenOnePersistedInDb(Venue venue, Tenant tenant) {
      var softGreProfile1 = dataHelper.createSoftGreProfile(tenant);
      var portData1 =
          dataHelper.createVenueLanPortDataWithAdoption(
              venue, apModel, portId, 3, of(getSoftGreLanPortActivation(softGreProfile1)));

      // save SoftGreProfile
      var softGreProfile2 =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      softGreProfileId = softGreProfile2.getId();
      venueId = venue.getId();
      venueLanPortId = portData1.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {
      validateActivateSoftGreProfileToVenueApModelLanPortResult(
          CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
          venueId,
          softGreProfileId,
          payload,
          venueLanPortId,
          apModel,
          portId,
          true);
    }
  }

  private void initAp(Ap ap, ApGroup apGroup) {

    var apModelSpecific = new ApModelSpecific();
    apModelSpecific.setLanPorts(new ArrayList<>());
    apModelSpecific =
        repositoryUtil.createOrUpdate(apModelSpecific, txCtxExtension.getTenantId(), randomTxId());

    ap.setModelSpecific(apModelSpecific);
    ap.setApGroup(apGroup);
    repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());
  }

  @Nested
  class ConsumeActivateSoftGreProfileOnVenueModelAndApTest {

    private String softGreProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "2";
    private String venueLanPortId;

    private String serialNumber;
    private String apLanPortId;

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        Generators.lanPortSoftGreProfileSettings();

    @BeforeEach
    void givenOnePersistedInDb(
        Venue venue, Tenant tenant, final @ApModel("R500") Ap ap, ApGroup apGroup) {
      var softGreProfile1 = dataHelper.createSoftGreProfile(tenant);
      var portData1 =
          dataHelper.createVenueLanPortDataWithAdoption(
              venue, apModel, portId, 3, of(getSoftGreLanPortActivation(softGreProfile1)));
      venueLanPortId = portData1.port().getId();

      var apLanPortData = dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 4);

      serialNumber = ap.getId();
      venueId = venue.getId();
      apLanPortId = apLanPortData.port().getId();
      var softGreProfile2 =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      softGreProfileId = softGreProfile2.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {

      var payloadForAp = Generators.lanPortSoftGreProfileSettings().generate();
      RequestParams requestParamsAp =
          new RequestParams()
              .addPathVariable("venueId", venueId)
              .addPathVariable("serialNumber", serialNumber)
              .addPathVariable("portId", portId)
              .addPathVariable("softGreProfileId", softGreProfileId);
      messageUtil.sendWifiCfgRequest(
          txCtxExtension.getTenantId(),
          txCtxExtension.getRequestId(),
          CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
          "",
          requestParamsAp,
          payloadForAp);
      validateDdccmCfgRequestMessages(softGreProfileId, venueLanPortId);
      validateCmnCfgCollectorMessages(softGreProfileId, venueId, apModel, portId, false);

      final var cmnCfgCollectorMessage =
          messageCaptors
              .getCmnCfgCollectorMessageCaptor()
              .getValue(txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      var operations =
          cmnCfgCollectorMessage.getPayload().getOperationsList().stream()
              .filter(
                  op ->
                      Index.SOFT_GRE_PROFILE_INDEX_NAME.equals(op.getIndex())
                          && op.getOpType() == OpType.MOD);

      assertThat(operations)
          .isNotEmpty()
          .singleElement()
          .extracting(Operations::getDocMap)
          .satisfies(
              docMap -> {
                assertThat(docMap.get(Key.VENUE_ACTIVATIONS).getListValue().getValuesList())
                    .isNotEmpty()
                    .singleElement()
                    .extracting(Value::getStructValue)
                    .matches(c -> venueId.equals(c.getFieldsMap().get(VENUE_ID).getStringValue()))
                    .matches(c -> apModel.equals(c.getFieldsMap().get(AP_MODEL).getStringValue()))
                    .matches(
                        c ->
                            c.getFieldsMap()
                                .get(AP_SERIAL_NUMBERS)
                                .getListValue()
                                .getValuesList()
                                .isEmpty())
                    .matches(c -> portId.equals(c.getFieldsMap().get(PORT_ID).getStringValue()));
                assertThat(docMap.get(Key.AP_ACTIVATIONS).getListValue().getValuesList())
                    .isNotEmpty()
                    .singleElement()
                    .extracting(Value::getStructValue)
                    .matches(c -> venueId.equals(c.getFieldsMap().get(VENUE_ID).getStringValue()))
                    .matches(
                        c ->
                            serialNumber.equals(
                                c.getFieldsMap().get(AP_SERIAL_NUMBER).getStringValue()))
                    .matches(c -> portId.equals(c.getFieldsMap().get(PORT_ID).getStringValue()));
              });
    }
  }

  @Nested
  class ConsumeActivateSoftGreProfileExceedMaxSoftGreProfileOnVenueCount {

    private String softGreProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "2";
    private String venueLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Tenant tenant, final ApGroup apGroup, final Venue venue, final Ap ap) {
      var softGreProfile1 = dataHelper.createSoftGreProfile(tenant);
      dataHelper.createVenueLanPortDataWithAdoption(
          venue, apModel, "3", 3, of(getSoftGreLanPortActivation(softGreProfile1)));

      var softGreProfile2 = dataHelper.createSoftGreProfile(tenant);
      dataHelper.createVenueLanPortDataWithAdoption(
          venue, apModel, "4", 4, of(getSoftGreLanPortActivation(softGreProfile2)));

      var softGreProfile3 = dataHelper.createSoftGreProfile(tenant);
      dataHelper.createVenueLanPortDataWithAdoption(
          venue, apModel, "5", 5, of(getSoftGreLanPortActivation(softGreProfile3)));

      var portData = dataHelper.createVenueLanPortDataWithAdoption(venue, apModel, portId, 6);
      venueId = venue.getId();
      venueLanPortId = portData.port().getId();
      var softGreProfile4 = dataHelper.createSoftGreProfile(tenant);
      softGreProfileId = softGreProfile4.getId();
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {

      LanPortSoftGreProfileSettingsGenerator generator = Generators.lanPortSoftGreProfileSettings();
      RequestParams requestParams =
          new RequestParams()
              .addPathVariable("venueId", venueId)
              .addPathVariable("apModel", apModel)
              .addPathVariable("portId", portId)
              .addPathVariable("softGreProfileId", softGreProfileId);
      var payload = generator.generate();
      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      txCtxExtension.getTenantId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
                      "",
                      requestParams,
                      payload))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);
      final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
      assertThat(venueLanPort)
          .isNotNull()
          .extracting(VenueLanPort::getLanPortAdoption)
          .isNotNull()
          .extracting(LanPortAdoption::getSoftGreActivation)
          .isNull();
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
    }
  }

  @Nested
  class ConsumeActivateSoftGreProfileOnVenueApModelLanPortWithExistClientIsolationActivationTest {

    private String venueId;
    private final String apModel = "R550";
    private String portId = "1";
    private String venueLanPortId;
    private String clientIsolationProfileId;
    private String softGreProfileId;

    @BeforeEach
    void givenOnePersistedInDb(Tenant tenant, final Venue venue) {
      venueId = venue.getId();

      var softGreProfile = dataHelper.createSoftGreProfile(tenant);
      softGreProfileId = softGreProfile.getId();
      var randomClientIsolation = dataHelper.createClientIsolationAllowlist(venue.getTenant());
      clientIsolationProfileId = randomClientIsolation.getId();

      var portData1 =
          dataHelper.createVenueLanPortDataWithAdoption(
              venue,
              apModel,
              portId,
              3,
              of(getClientIsolationLanPortActivation(randomClientIsolation)));
      venueLanPortId = portData1.port().getId();
    }

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        Generators.lanPortSoftGreProfileSettings();

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {

      validateActivateSoftGreProfileToVenueApModelLanPortResult(
          CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
          venueId,
          softGreProfileId,
          payload,
          venueLanPortId,
          apModel,
          portId,
          false);
      final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);

      assertThat(venueLanPort)
          .extracting(VenueLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNotNull()
          .extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
          .isNotNull()
          .matches(c -> c.getId().equals(clientIsolationProfileId));
      ;
    }
  }

  private void validateActivateSoftGreProfileToVenueApModelLanPortResult(
      CfgAction apiAction,
      String venueId,
      String softGreProfileId,
      LanPortSoftGreProfileSettings payload,
      String venueLanPortId,
      String apModel,
      String portId,
      boolean isReplaceProfile) {

    validateVenueRepositoryData(apiAction, venueId, softGreProfileId, payload, venueLanPortId);
    validateDdccmCfgRequestMessages(softGreProfileId, venueLanPortId);
    validateCmnCfgCollectorMessages(softGreProfileId, venueId, apModel, portId, isReplaceProfile);
    validateActivityMessages(apiAction);
  }

  private void validateVenueRepositoryData(
      CfgAction apiAction,
      String venueId,
      String softGreProfileId,
      LanPortSoftGreProfileSettings payload,
      String venueLanPortId) {

    assertThat(venueId).isNotNull();

    switch (apiAction) {
      case ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT -> {
        final var allLanPortAdoption =
            repositoryUtil.findAll(LanPortAdoption.class, txCtxExtension.getTenantId());
        assertThat(allLanPortAdoption).isNotNull().matches(l -> l.size() == 1);
        final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
        assertThat(venueLanPort)
            .extracting(VenueLanPort::getLanPortAdoption)
            .extracting(LanPortAdoption::getSoftGreActivation)
            .isNotNull()
            .matches(activation -> activation.getSoftGreProfile().getId().equals(softGreProfileId));
      }
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    }
  }

  private void validateCmnCfgCollectorMessages(
      String softGreProfileId,
      String venueId,
      String apModel,
      String portId,
      boolean isReplaceProfile) {
    if (softGreProfileId == null) {
      messageCaptors
          .getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    var operations =
        cmnCfgCollectorMessage.getPayload().getOperationsList().stream()
            .filter(
                op ->
                    Index.SOFT_GRE_PROFILE_INDEX_NAME.equals(op.getIndex())
                        && op.getOpType() == OpType.MOD)
            .collect(Collectors.partitioningBy(op -> softGreProfileId.equals(op.getId())));

    assertThat(operations.get(true))
        .isNotEmpty()
        .singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(
            docMap -> {
              assertThat(docMap.get(Key.VENUE_ACTIVATIONS).getListValue().getValuesList())
                  .isNotEmpty()
                  .singleElement()
                  .extracting(Value::getStructValue)
                  .matches(c -> venueId.equals(c.getFieldsMap().get(VENUE_ID).getStringValue()))
                  .matches(c -> apModel.equals(c.getFieldsMap().get(AP_MODEL).getStringValue()))
                  .matches(c -> portId.equals(c.getFieldsMap().get(PORT_ID).getStringValue()));
            });
    var removeActivateOps = operations.get(false);
    if (isReplaceProfile) {
      assertThat(removeActivateOps)
          .singleElement()
          .extracting(Operations::getDocMap)
          .satisfies(
              docMap -> {
                assertThat(docMap.get(Key.VENUE_ACTIVATIONS).getListValue().getValuesList())
                    .isEmpty();
                assertThat(docMap.get(Key.AP_ACTIVATIONS).getListValue().getValuesList()).isEmpty();
              });
    }
  }

  private void validateDdccmCfgRequestMessages(String softGreProfileId, String venueLanPortId) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();
    final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
    var inUseEthernetPortProfileId = venueLanPort.getLanPortAdoption().getEthernetPortProfileId();
    assertThatNoException()
        .isThrownBy(
            () -> {
              var operations =
                  assertThat(ddccmCfgRequestMessage.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList)
                      .asList()
                      .isNotEmpty()
                      .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast);

              operations
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                  .isNotEmpty()
                  .allSatisfy(
                      op -> {
                        assertThat(op)
                            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                            .matches(
                                venue ->
                                    venue.getCcmMultipleTunnel().getSoftGreSettingList().stream()
                                        .anyMatch(
                                            softgre ->
                                                softgre.getProfileId().equals(softGreProfileId)))
                            .matches(
                                venue ->
                                    venue.getVenueApModelsList().stream()
                                        .anyMatch(
                                            model ->
                                                model.getLanPortList().stream()
                                                    .anyMatch(
                                                        lanPort ->
                                                            inUseEthernetPortProfileId.equals(
                                                                lanPort.getApLanPortProfileId()))));
                      });

              operations
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApLanPortProfile)
                  .filteredOn(operation -> operation.getAction() == Action.ADD)
                  .isNotEmpty()
                  .allSatisfy(
                      op ->
                          assertThat(op)
                              .extracting(
                                  com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                              .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                              .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                              .matches(
                                  commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                  .allSatisfy(op -> verifyAddAction(op, venueLanPort, softGreProfileId));
            });
  }

  private void verifyAddAction(
      com.ruckus.acx.ddccm.protobuf.wifi.Operation operation,
      VenueLanPort venueLanPort,
      String softGreProfileId) {
    assertThat(operation)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
        .matches(
            apLanPortProfile ->
                venueLanPort.getLanPortAdoption().getId().equals(apLanPortProfile.getName()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getLanPortAdoption()
                    .getEthernetPortProfileId()
                    .equals(apLanPortProfile.getId()))
        .matches(
            apLanPortProfile ->
                (venueLanPort.getApLanPortProfile().getType().name() + "PORT")
                    .equals(apLanPortProfile.getLanPortType().name()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagId()))
        .matches(
            apLanPortProfile ->
                softGreProfileId.equals(apLanPortProfile.getTunnelProfile().getProfileId()));
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors
          .assertThat(
              kafkaTopicProvider.getActivityCfgChangeResp(),
              kafkaTopicProvider.getActivityImpacted())
          .doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(ConfigurationStatus::getEventDate)
                    .isNotNull());

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ImpactDevice::getDeviceIdsCount).isEqualTo(0);
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT ->
          ApiFlowNames.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private ClientIsolationLanPortActivation getClientIsolationLanPortActivation(
      ClientIsolationAllowlist clientIsolationProfile) {
    ClientIsolationLanPortActivation activation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
            .clientIsolationLanPortActivation()
            .generate();
    activation.setClientIsolationAllowlist(clientIsolationProfile);
    return activation;
  }

  private SoftGreProfileLanPortActivation getSoftGreLanPortActivation(
      SoftGreProfile softGreProfile) {
    SoftGreProfileLanPortActivation activation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
            .softGreProfileLanPortActivation()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(
                always(
                    com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                        .dhcpOption82Settings()
                        .generate()))
            .generate();
    activation.setSoftGreProfile(softGreProfile);
    return activation;
  }
}
