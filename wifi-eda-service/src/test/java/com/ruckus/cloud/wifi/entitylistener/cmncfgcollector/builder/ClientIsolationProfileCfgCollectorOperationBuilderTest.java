package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlistEntry;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.servicemodel.projection.ApLanPortProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueLanPortProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class ClientIsolationProfileCfgCollectorOperationBuilderTest {

  @MockBean
  private VenueRepository venueRepository;

  @MockBean
  private NetworkVenueRepository networkVenueRepository;

  @MockBean
  private VenueLanPortRepository venueLanPortRepository;

  @MockBean
  private ApLanPortRepository apLanPortRepository;

  @MockBean
  private ApRepository apRepository;

  @SpyBean
  private ClientIsolationProfileCmnCfgCollectorOperationBuilder clientIsolationProfileCmnCfgCollectorOperationBuilder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  public void testGetEntityClass() {
    assertThat(clientIsolationProfileCmnCfgCollectorOperationBuilder.entityClass())
        .isEqualTo(ClientIsolationAllowlist.class);
  }

  @Nested
  class testBuildConfig {



    @Test
    public void givenEntityActionIsDelete() {
      var operations = clientIsolationProfileCmnCfgCollectorOperationBuilder
          .build(new TxEntity<>(new ClientIsolationAllowlist(randomId()), EntityAction.DELETE),
              emptyTxChanges()).get(0);
      assertThat(operations.getDocCount())
          .isZero();
    }

    @Test
    public void givenAddClientIsolationProfile() {
      var clientIsolationAllowlist = new ClientIsolationAllowlist(randomId());

      String tenantId = txCtxExtension.getTenantId();

      clientIsolationAllowlist.setTenant(new Tenant(tenantId));
      clientIsolationAllowlist.setName(randomName());

      var allowlistEntry1 = new ClientIsolationAllowlistEntry();
      allowlistEntry1.setMac(randomMacAddress());
      var allowlistEntry2 = new ClientIsolationAllowlistEntry();
      allowlistEntry2.setMac(randomMacAddress());
      clientIsolationAllowlist.setAllowlist(List.of(allowlistEntry1, allowlistEntry2));

      var venue = new Venue(randomId());
      venue.setName(randomName());
      var network1 = new OpenNetwork(randomId());
      network1.setName(randomName());
      var network2 = new OpenNetwork(randomId());
      network2.setName(randomName());
      var networkVenue1 = new NetworkVenue(randomId());
      networkVenue1.setClientIsolationAllowlist(clientIsolationAllowlist);
      networkVenue1.setVenue(venue);
      networkVenue1.setNetwork(network1);
      var networkVenue2 = new NetworkVenue(randomId());
      networkVenue2.setClientIsolationAllowlist(clientIsolationAllowlist);
      networkVenue2.setVenue(venue);
      networkVenue2.setNetwork(network2);

      doReturn(List.of(networkVenue1, networkVenue2)).when(networkVenueRepository)
          .findByTenantIdAndClientIsolationAllowlistIdIn(tenantId,
              List.of(clientIsolationAllowlist.getId()));

      doReturn(List.of(venue)).when(venueRepository)
          .findByTenantIdAndClientIsolationAllowlistId(tenantId, clientIsolationAllowlist.getId());

      var builder = Operations.newBuilder();

      clientIsolationProfileCmnCfgCollectorOperationBuilder
          .config(builder, clientIsolationAllowlist, EntityAction.ADD);

      var docMap = builder.build().getDocMap();

      assertThat(docMap.get(EsConstants.Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(clientIsolationAllowlist.getId());
      assertThat(docMap.get(EsConstants.Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(clientIsolationAllowlist.getName());
      assertThat(docMap.get(EsConstants.Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(clientIsolationAllowlist.getTenant().getId());
      this.compareVenues(docMap, tuple(venue.getId(), network1.getId()),
          tuple(venue.getId(), network2.getId()));
      this.compareListValue(docMap, EsConstants.Key.VENUE_IDS, venue.getId());
      this.compareListValue(docMap, EsConstants.Key.CLIENT_ENTRIES, allowlistEntry1.getMac(),
          allowlistEntry2.getMac());
    }

    private void compareVenues(Map<String, Value> docMap, Tuple... values) {
      var valuesList = docMap.get(EsConstants.Key.ACTIVATIONS).getListValue().getValuesList();
      assertThat(valuesList)
          .hasSameSizeAs(values)
          .extracting(
              v -> v.getStructValue().getFieldsOrThrow(EsConstants.Key.VENUE_ID).getStringValue(),
              v -> v.getStructValue().getFieldsOrThrow(EsConstants.Key.WIFI_NETWORK_ID)
                  .getStringValue())
          .containsExactlyInAnyOrder(values);
    }

    private void compareListValue(Map<String, Value> docMap, String key, String... values) {
      var valuesList = docMap.get(key).getListValue().getValuesList();
      assertThat(valuesList).hasSameSizeAs(values).extracting(Value::getStringValue)
          .containsExactlyInAnyOrder(values);
    }

    @Test
    public void givenAddClientIsolationProfileWithoutRelations() {
      var clientIsolationAllowlist = new ClientIsolationAllowlist(randomId());

      clientIsolationAllowlist.setTenant(new Tenant(txCtxExtension.getTenantId()));
      clientIsolationAllowlist.setName(randomName());

      var allowlistEntry1 = new ClientIsolationAllowlistEntry();
      allowlistEntry1.setMac(randomMacAddress());
      var allowlistEntry2 = new ClientIsolationAllowlistEntry();
      allowlistEntry2.setMac(randomMacAddress());
      clientIsolationAllowlist.setAllowlist(List.of(allowlistEntry1, allowlistEntry2));

      var builder = Operations.newBuilder();

      clientIsolationProfileCmnCfgCollectorOperationBuilder
          .config(builder, clientIsolationAllowlist, EntityAction.ADD);

      var docMap = builder.build().getDocMap();

      assertThat(docMap.get(EsConstants.Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(clientIsolationAllowlist.getId());
      assertThat(docMap.get(EsConstants.Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(clientIsolationAllowlist.getName());
      assertThat(docMap.get(EsConstants.Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(clientIsolationAllowlist.getTenant().getId());
      this.compareVenues(docMap);
      this.compareListValue(docMap, EsConstants.Key.VENUE_IDS);
      this.compareListValue(docMap, EsConstants.Key.CLIENT_ENTRIES, allowlistEntry1.getMac(),
          allowlistEntry2.getMac());
    }

    @Test
    void testClientIsolationWithActivations() {
      List<VenueLanPort> givenVenueLanPorts = Generators.venueLanPort()
          .setVenueApModelSpecificAttributes(Generators.venueApModelSpecificAttributes()
                  .setVenue(Generators.venue())).generate(3);
      List<ApLanPort> givenApLanPorts = Generators.apLanPort().generate(3);

      List<Ap> appliedApLanPortApList = List.of(givenApLanPorts.get(0).getModelSpecific().getAp());
      List<Ap> apList = Generators.ap().generate(4);
      Ap sameVenueButAppliedOtherProfileAp = apList.get(0);
      apList.addAll(appliedApLanPortApList);
      apList.forEach(ap -> ap.setModel(givenVenueLanPorts.get(0).getVenueApModelSpecificAttributes().getModel()));
      String apVenueId = givenVenueLanPorts.get(0).getVenueApModelSpecificAttributes().getVenue().getId();

      doReturn(mockApLanPortProjections(givenApLanPorts))
          .when(apLanPortRepository)
          .findApLanPortProjectionsWhichActivatedClientIsolationAllowlist(anyString(), anyString());
      doReturn(mockVenueLanPortProjections(givenVenueLanPorts))
          .when(venueLanPortRepository)
          .findVenueLanPortProjectionsWhichActivatedClientIsolationAllowlist(anyString(), anyString());
      doReturn(apList).when(apRepository)
          .findByApGroupVenueIdAndTenantId(eq(apVenueId), anyString());
      doReturn(List.of(sameVenueButAppliedOtherProfileAp.getId(), appliedApLanPortApList.get(0).getId()))
          .when(apLanPortRepository).findBySerialNumbersIn(anyList());

      var expectedApList = CollectionUtils.subtract(apList, appliedApLanPortApList);
      expectedApList = CollectionUtils.subtract(expectedApList, List.of(sameVenueButAppliedOtherProfileAp));

      var clientIsolationAllowlist = new ClientIsolationAllowlist(randomId());
      clientIsolationAllowlist.setTenant(new Tenant(txCtxExtension.getTenantId()));
      clientIsolationAllowlist.setName(randomName());

      var allowlistEntry1 = new ClientIsolationAllowlistEntry();
      allowlistEntry1.setMac(randomMacAddress());
      clientIsolationAllowlist.setAllowlist(List.of(allowlistEntry1));

      var builder = Operations.newBuilder();

      clientIsolationProfileCmnCfgCollectorOperationBuilder
          .config(builder, clientIsolationAllowlist, EntityAction.ADD);

      var docMap = builder.build().getDocMap();

      assertThat(docMap.get(EsConstants.Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(clientIsolationAllowlist.getId());
      assertThat(docMap.get(EsConstants.Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(clientIsolationAllowlist.getName());
      assertThat(docMap.get(EsConstants.Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(clientIsolationAllowlist.getTenant().getId());

      assertThat(docMap)
          .hasEntrySatisfying(Key.AP_ACTIVATIONS, assertApActivationsValue(givenApLanPorts))
          .hasEntrySatisfying(Key.VENUE_ACTIVATIONS, assertVenueActivationsValue(givenVenueLanPorts, expectedApList, apVenueId));
    }

    private static Consumer<Value> assertVenueActivationsValue(
        Collection<VenueLanPort> givenVenueLanPorts, Collection<Ap> apList, String apVenueId) {
      return actualVenueActivationsValue -> {
        assertThat(actualVenueActivationsValue.getKindCase()).isEqualTo(KindCase.LIST_VALUE);
        assertThat(actualVenueActivationsValue.hasListValue()).isTrue();
        assertThat(actualVenueActivationsValue.getListValue()).isNotNull()
            .satisfies(listValue -> {
              assertThat(listValue.getValuesCount()).isEqualTo(givenVenueLanPorts.size());
              assertThat(listValue.getValuesList()).hasSize(givenVenueLanPorts.size())
                  .allSatisfy(value -> {
                    assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                    assertThat(value.hasStructValue()).isTrue();
                    assertThat(value.getStructValue()).isNotNull();
                  })
                  .extracting(Value::getStructValue)
                  .extracting(
                      struct -> struct.getFieldsOrThrow(Key.VENUE_ID).getStringValue(),
                      struct -> struct.getFieldsOrThrow(Key.AP_MODEL).getStringValue(),
                      struct -> struct.getFieldsOrThrow(Key.PORT_ID).getStringValue(),
                      struct -> struct.getFieldsOrThrow(Key.AP_SERIAL_NUMBERS).getListValue().getValuesList().stream()
                          .map(Value::getStringValue).sorted().collect(Collectors.toList()))
                  .containsExactlyInAnyOrderElementsOf(() -> givenVenueLanPorts.stream()
                      .map(venueLanPort -> tuple(
                          venueLanPort.getVenueApModelSpecificAttributes().getVenue().getId(),
                          venueLanPort.getVenueApModelSpecificAttributes().getModel(),
                          venueLanPort.getPortId(),
                          venueLanPort.getVenueApModelSpecificAttributes().getVenue().getId()
                            .equals(apVenueId)
                            ? apList.stream().map(Ap::getId).sorted().collect(Collectors.toList())
                            : Collections.emptyList()
                  ))
                  .iterator());
            });
      };
    }

    private static Consumer<Value> assertApActivationsValue(Collection<ApLanPort> givenApLanPorts) {
      return actualVenueActivationsValue -> {
        assertThat(actualVenueActivationsValue.getKindCase()).isEqualTo(KindCase.LIST_VALUE);
        assertThat(actualVenueActivationsValue.hasListValue()).isTrue();
        assertThat(actualVenueActivationsValue.getListValue()).isNotNull()
            .satisfies(listValue -> {
              assertThat(listValue.getValuesCount()).isEqualTo(givenApLanPorts.size());
              assertThat(listValue.getValuesList()).hasSize(givenApLanPorts.size())
                  .allSatisfy(value -> {
                    assertThat(value.getKindCase()).isEqualTo(KindCase.STRUCT_VALUE);
                    assertThat(value.hasStructValue()).isTrue();
                    assertThat(value.getStructValue()).isNotNull();
                  })
                  .extracting(Value::getStructValue)
                  .containsExactlyInAnyOrderElementsOf(() -> givenApLanPorts.stream()
                      .map(apLanPort -> Struct.newBuilder()
                          .putFields(Key.VENUE_ID, ValueUtils.stringValue(apLanPort.getModelSpecific().getAp().getApGroup().getVenue().getId()))
                          .putFields(Key.AP_SERIAL_NUMBER, ValueUtils.stringValue(apLanPort.getModelSpecific().getAp().getId()))
                          .putFields(Key.PORT_ID, ValueUtils.stringValue(apLanPort.getPortId())).build()).iterator());
            });
      };
    }

    @Test
    public void testGetIndex() {
      assertThat(clientIsolationProfileCmnCfgCollectorOperationBuilder.index())
          .isEqualTo(EsConstants.Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME);
    }

    @Nested
    class testHasModification {

      @Test
      public void givenEntityActionIsNotModify() {
        assertThat(clientIsolationProfileCmnCfgCollectorOperationBuilder
            .hasModification(EntityAction.ADD, Collections.emptySet(), false)).isTrue();
      }

      @Test
      public void givenAnyFields() {
        assertThat(clientIsolationProfileCmnCfgCollectorOperationBuilder.hasModification(
            EntityAction.MODIFY, Set.of(ClientIsolationAllowlist.Fields.NAME), false)).isTrue();
        assertThat(clientIsolationProfileCmnCfgCollectorOperationBuilder.hasModification(
            EntityAction.MODIFY, Set.of(ClientIsolationAllowlist.Fields.ALLOWLIST),
            false)).isTrue();
        assertThat(clientIsolationProfileCmnCfgCollectorOperationBuilder.hasModification(
            EntityAction.MODIFY, Set.of(AbstractBaseEntity.Fields.UPDATEDDATE), false)).isFalse();
      }
    }
  }

  private List<ApLanPortProjection> mockApLanPortProjections(List<ApLanPort> apLanPorts) {
    return apLanPorts.stream()
        .map(a -> new ApLanPortProjection(
            a.getModelSpecific().getAp().getApGroup().getVenue().getId(),
            a.getModelSpecific().getAp().getId(),
            a.getPortId()))
        .toList();
  }

  private List<VenueLanPortProjection> mockVenueLanPortProjections(
      List<VenueLanPort> venueLanPorts) {
    return venueLanPorts.stream()
        .map(v -> new VenueLanPortProjection(
            v.getVenueApModelSpecificAttributes().getVenue().getId(),
            v.getVenueApModelSpecificAttributes().getModel(),
            v.getPortId()))
        .toList();
  }
}
