package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort8021XType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.CcmRoleSupplicant.Type;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.SupplicantAuthenticationOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortSupplicantTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.LanPortAdoptionRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.util.ObjectUtils;

@Tag("EthernetPortProfileTest")
@WifiUnitTest
class DdccmEthernetPortProfileOperationBuilderTest {

  @SpyBean
  private DdccmEthernetPortProfileOperationBuilder builder;

  @MockBean
  private VenueLanPortRepository venueLanPortRepository;
  @MockBean
  private ApLanPortRepository apLanPortRepository;
  @MockBean private LanPortAdoptionRepository lanPortAdoptionRepository;

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyMspTenantId");

  @Test
  void testBuild(Tenant tenant, Venue venue) {
    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setType(options(ApLanPortTypeEnum.ACCESS, ApLanPortTypeEnum.GENERAL).setRandom(true))
        .generate();
    ethernetPortProfile.setVenueId(venue.getId());
    ethernetPortProfile.setTenant(tenant);

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result =
        builder.build(new NewTxEntity<>(ethernetPortProfile), emptyTxChanges())
        .stream().filter(operation -> operation.hasApLanPortProfile())
            .findFirst().get().getApLanPortProfile();

    verifyApLanPortProfile(ethernetPortProfile, result);
    verifyNonTrunkPortUntagVlanId(ethernetPortProfile, result);
  }

  @Test
  void testTrunkPortUntagVlanId(Tenant tenant, Venue venue) {
    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .generate();
    ethernetPortProfile.setVenueId(venue.getId());
    ethernetPortProfile.setTenant(tenant);

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result =
        builder.build(new NewTxEntity<>(ethernetPortProfile), emptyTxChanges())
            .stream().filter(operation -> operation.hasApLanPortProfile())
            .findFirst().get().getApLanPortProfile();

    verifyApLanPortProfile(ethernetPortProfile, result);
    verifyTrunkPortUntagVlanId(ethernetPortProfile, result);
  }

  @FeatureFlag(enable = FlagNames.ACX_UI_ETHERNET_TOGGLE)
  @Test
  void testEthernetPortFields_disabled(Tenant tenant, Venue venue) {

    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .setAuthType(always(ApLanPortAuthTypeEnum.DISABLED))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result =
        builder.build(new NewTxEntity<>(ethernetPortProfile), emptyTxChanges())
            .stream().filter(operation -> operation.hasApLanPortProfile())
            .findFirst().get().getApLanPortProfile();

    assertEquals(ApLanPort8021XType.DISABLE, result.getPort8021X().getType());
    assertTrue(StringUtils.isEmpty(result.getRoleAuthenticator().getAuthenticationServerId()));
    assertTrue(StringUtils.isEmpty(result.getRoleAuthenticator().getAccountingServerId()));

    assertTrue(StringUtils.isEmpty(result.getRoleSupplicant().getUser()));
    assertTrue(StringUtils.isEmpty(result.getRoleSupplicant().getPassword()));

  }

  @FeatureFlag(enable = FlagNames.ACX_UI_ETHERNET_TOGGLE)
  @Test
  void testEthernetPortFields_macBasedAuthenticator(Tenant tenant, Venue venue) {

    String radiusServerId = "test-radius-server-id";
    Radius radius = Generators.radiusAuth().generate();
    AuthRadiusService authRadiusService = Generators.authRadiusService(radius).generate();
    authRadiusService.setId(radiusServerId);
    radius.setAuthRadiusServices(List.of(authRadiusService));

    String acctRadiusServerId = "test-acct-radius-server-id";
    Radius acctRadius = Generators.radiusAuth().generate();
    AccountingRadiusService accountingRadiusService = Generators.accountingRadiusService(acctRadius).generate();
    accountingRadiusService.setId(acctRadiusServerId);
    acctRadius.setAccountingRadiusServices(List.of(accountingRadiusService));

    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .setAuthType(always(ApLanPortAuthTypeEnum.MAC_BASED_AUTHENTICATOR))
        .setAuthRadius(always(radius))
        .setEnableAuthProxy(always(true))
        .setAccountingRadius(always(acctRadius))
        .setEnableAccountingProxy(always(true))
        .setBypassMacAddressAuthentication(always(true))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result =
        builder.build(new NewTxEntity<>(ethernetPortProfile), emptyTxChanges())
            .stream().filter(Operation::hasApLanPortProfile)
            .findFirst().get().getApLanPortProfile();

    assertEquals(ApLanPort8021XType.MACBASEDAUTHENTICATOR, result.getPort8021X().getType());
    assertTrue(result.getRoleAuthenticator().getAuthenticationServerProxyEnabled());
    assertTrue(result.getRoleAuthenticator().getAccountingServerProxyEnabled());
    assertTrue(result.getRoleAuthenticator().getMacByPassEnabled());
    assertEquals(acctRadiusServerId, result.getRoleAuthenticator().getAccountingServerId());
    assertEquals(radiusServerId, result.getRoleAuthenticator().getAuthenticationServerId());

  }

  @FeatureFlag(enable = FlagNames.ACX_UI_ETHERNET_TOGGLE)
  @Test
  void testEthernetPortFields_open(Tenant tenant, Venue venue) {

    String acctRadiusServerId = "test-acct-radius-server-id";
    Radius acctRadius = Generators.radiusAcct().generate();
    AccountingRadiusService accountingRadiusService = Generators.accountingRadiusService(acctRadius).generate();
    accountingRadiusService.setId(acctRadiusServerId);
    acctRadius.setAccountingRadiusServices(List.of(accountingRadiusService));

    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .setAuthType(always(ApLanPortAuthTypeEnum.OPEN))
        .setAccountingRadius(always(acctRadius))
        .setEnableAccountingProxy(always(true))
        .setBypassMacAddressAuthentication(always(true))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result =
        builder.build(new NewTxEntity<>(ethernetPortProfile), emptyTxChanges())
            .stream().filter(Operation::hasApLanPortProfile)
            .findFirst().get().getApLanPortProfile();

    assertEquals(ApLanPort8021XType.OPEN, result.getPort8021X().getType());
    assertTrue(result.getRoleOpen().getAccountingServerProxyEnabled());
    assertEquals(acctRadiusServerId, result.getRoleOpen().getAccountingServerId());

  }

  @FeatureFlag(enable = FlagNames.ACX_UI_ETHERNET_TOGGLE)
  @Test
  void testEthernetPortFields_portBasedAuthenticator(Tenant tenant, Venue venue) {

    boolean dVlan = true;
    Short guestVlan = 100;

    String radiusVenueId = "test-radius-venue-id";
    Radius radius = Generators.radiusAuth().generate();
    AuthRadiusVenue authRadiusVenue = Generators.authRadiusVenue(radius).generate();
    authRadiusVenue.setId(radiusVenueId);
    authRadiusVenue.setVenue(venue);
    radius.setAuthRadiusVenues(List.of(authRadiusVenue));

    String acctRadiusVenueId = "test-acct-radius-venue-id";
    Radius acctRadius = Generators.radiusAuth().generate();
    AccountingRadiusVenue accountingRadiusVenue = Generators.accountingRadiusVenue(acctRadius).generate();
    accountingRadiusVenue.setId(acctRadiusVenueId);
    accountingRadiusVenue.setVenue(venue);
    acctRadius.setAccountingRadiusVenues(List.of(accountingRadiusVenue));

    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .setAuthType(always(ApLanPortAuthTypeEnum.PORT_BASED_AUTHENTICATOR))
        .setAuthRadius(always(radius))
        .setEnableAuthProxy(always(false))
        .setAccountingRadius(always(acctRadius))
        .setEnableAccountingProxy(always(false))
        .setDynamicVlanEnabled(always(dVlan))
        .setUnauthenticatedGuestVlan(always(guestVlan))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();

    ApLanPort apLanPort = new ApLanPort();
    ApModelSpecific apModelSpecific = new ApModelSpecific();
    apLanPort.setModelSpecific(apModelSpecific);
    Ap ap = new Ap();
    ApGroup apGroup = new ApGroup();
    apGroup.setVenue(venue);
    ap.setApGroup(apGroup);
    apModelSpecific.setAp(ap);

    String venueId2 = defaultIdGenerator().generate();
    Venue venue2 = new Venue();
    venue2.setId(venueId2);
    VenueLanPort venueLanPort = new VenueLanPort();
    VenueApModelSpecificAttributes venueApModelSpecificAttributes = new VenueApModelSpecificAttributes();
    venueLanPort.setVenueApModelSpecificAttributes(venueApModelSpecificAttributes);
    venueApModelSpecificAttributes.setVenue(venue2);

    String tenantId = tenant.getId();
    String apLanPortProfileId = ethernetPortProfile.getId();

    doReturn(List.of(venue.getId())).when(apLanPortRepository).findVenueIdsByTenantIdAndApLanPortProfileId(eq(tenantId), eq(apLanPortProfileId));
    doReturn(List.of(venue2.getId())).when(venueLanPortRepository).findVenueIdsByTenantIdAndApLanPortProfileId(eq(tenantId), eq(apLanPortProfileId));

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result =
        builder.build(new NewTxEntity<>(ethernetPortProfile), emptyTxChanges())
            .stream().filter(Operation::hasApLanPortProfile)
            .findFirst().get().getApLanPortProfile();

    assertEquals(ApLanPort8021XType.PORTBASEDAUTHENTICATOR, result.getPort8021X().getType());
    assertEquals(radiusVenueId, result.getRoleAuthenticator().getAuthenticationServerId());
    assertEquals(acctRadiusVenueId, result.getRoleAuthenticator().getAccountingServerId());
    assertFalse(result.getRoleAuthenticator().getAuthenticationServerProxyEnabled());
    assertFalse(result.getRoleAuthenticator().getAccountingServerProxyEnabled());

    assertEquals(dVlan, result.getDynamicVlanEnabled().getValue());
    assertEquals(guestVlan, ethernetPortProfile.getUnauthenticatedGuestVlan());
  }

  @FeatureFlag(enable = FlagNames.ACX_UI_ETHERNET_TOGGLE)
  @Test
  void testEthernetPortFields_supplicant_macAuth(Tenant tenant, Venue venue) {

    SupplicantAuthenticationOptions options = new SupplicantAuthenticationOptions();
    options.setType(ApLanPortSupplicantTypeEnum.MAC_AUTH);

    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .setAuthType(always(ApLanPortAuthTypeEnum.SUPPLICANT))
        .setSupplicantAuthenticationOptions(always(options))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result =
        builder.build(new NewTxEntity<>(ethernetPortProfile), emptyTxChanges())
            .stream().filter(operation -> operation.hasApLanPortProfile())
            .findFirst().get().getApLanPortProfile();

    assertEquals(ApLanPort8021XType.SUPPLICANT, result.getPort8021X().getType());
    assertEquals(Type.MAC, result.getRoleSupplicant().getType());
  }

  @FeatureFlag(enable = FlagNames.ACX_UI_ETHERNET_TOGGLE)
  @Test
  void testEthernetPortFields_supplicant_custom(Tenant tenant, Venue venue) {

    String username = "username";
    String password = "password";

    SupplicantAuthenticationOptions options = new SupplicantAuthenticationOptions();
    options.setType(ApLanPortSupplicantTypeEnum.CUSTOM);
    options.setUsername(username);
    options.setPassword(password);

    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .setAuthType(always(ApLanPortAuthTypeEnum.SUPPLICANT))
        .setSupplicantAuthenticationOptions(always(options))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result =
        builder.build(new NewTxEntity<>(ethernetPortProfile), emptyTxChanges())
            .stream().filter(operation -> operation.hasApLanPortProfile())
            .findFirst().get().getApLanPortProfile();

    assertEquals(ApLanPort8021XType.SUPPLICANT, result.getPort8021X().getType());
    assertEquals(Type.CUSTOM, result.getRoleSupplicant().getType());
    assertEquals(username, result.getRoleSupplicant().getUser());
    assertEquals(password, result.getRoleSupplicant().getPassword());
  }

  @Test
  void test_filterRelateVenueId() {
    Set<String> sets = Set.of("aa", "bb", "");

    String target1 = "aa";
    assertTrue(builder.filterRelateVenueId(sets, target1));

    String target2 = "cc";
    assertFalse(builder.filterRelateVenueId(sets, target2));

    Set<String> nullSets = null;
    String target3 = null;
    assertFalse(builder.filterRelateVenueId(nullSets, target3));
  }

  @Test
  void test_getRelateVenueId_venueLanPort(Venue venue, Tenant tenant) {
    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setId(randomString())
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .setAuthType(always(ApLanPortAuthTypeEnum.SUPPLICANT))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();
    String apLanPortProfileId = ethernetPortProfile.getId();
    String tenantId = tenant.getId();

    doReturn(Collections.emptyList()).when(venueLanPortRepository).findByTenantIdAndApLanPortProfileId(eq(tenantId), eq(apLanPortProfileId));
    Set<String> venueResult = builder.getRelateVenueId(ethernetPortProfile);
    assertTrue(ObjectUtils.isEmpty(venueResult));

    doReturn(List.of(venue.getId())).when(venueLanPortRepository).findVenueIdsByTenantIdAndApLanPortProfileId(eq(tenantId), eq(apLanPortProfileId));
    Set<String> venueResultHasVenueId = builder.getRelateVenueId(ethernetPortProfile);
    assertTrue(venueResultHasVenueId.contains(venue.getId()));

  }

  @Test
  void test_getRelateVenueId_apLanPort(Venue venue, Tenant tenant) {
    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setId(randomString())
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .setAuthType(always(ApLanPortAuthTypeEnum.SUPPLICANT))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();
    String apLanPortProfileId = ethernetPortProfile.getId();
    String tenantId = tenant.getId();

    doReturn(Collections.emptyList()).when(apLanPortRepository).findVenueIdsByTenantIdAndApLanPortProfileId(eq(tenantId), eq(apLanPortProfileId));
    Set<String> apResult = builder.getRelateVenueId(ethernetPortProfile);
    assertTrue(ObjectUtils.isEmpty(apResult));

    doReturn(List.of(venue.getId())).when(apLanPortRepository).findVenueIdsByTenantIdAndApLanPortProfileId(eq(tenantId), eq(apLanPortProfileId));
    Set<String> apResultHasVenueId = builder.getRelateVenueId(ethernetPortProfile);
    assertTrue(apResultHasVenueId.contains(venue.getId()));
  }

  @Test
  void test_getRelateVenueId_both(Venue venue, Tenant tenant) {
    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setId(randomString())
        .setType(always(ApLanPortTypeEnum.TRUNK))
        .setAuthType(always(ApLanPortAuthTypeEnum.SUPPLICANT))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();
    String apLanPortProfileId = ethernetPortProfile.getId();
    String tenantId = tenant.getId();

    String venueId2 = defaultIdGenerator().generate();
    Venue venue2 = new Venue();
    venue2.setId(venueId2);


    doReturn(List.of(venue.getId())).when(apLanPortRepository).findVenueIdsByTenantIdAndApLanPortProfileId(eq(tenantId), eq(apLanPortProfileId));
    doReturn(List.of(venueId2)).when(venueLanPortRepository).findVenueIdsByTenantIdAndApLanPortProfileId(eq(tenantId), eq(apLanPortProfileId));

    Set<String> resultHasVenueId = builder.getRelateVenueId(ethernetPortProfile);
    assertTrue(resultHasVenueId.contains(venue.getId()));
    assertTrue(resultHasVenueId.contains(venue2.getId()));
    assertEquals(2, resultHasVenueId.size());
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Nested
  class EnableSoftGreOverEthernetPort {

    @Test
    void testWithLanPortAdoption(Tenant tenant, Venue venue) {
      testEthernetPortProfileWithLanPortAdoption(tenant, venue);
    }

    @Test
    void testWithoutLanPortAdoption(Tenant tenant, Venue venue) {
      testEthernetPortProfileWithoutLanPortAdoption(tenant, venue);
    }
  }

  @FeatureFlag(
      enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
  @Nested
  class EnableClientIsolationOverEthernetPort {

    @Test
    void testWithLanPortAdoption(Tenant tenant, Venue venue) {
      testEthernetPortProfileWithLanPortAdoption(tenant, venue);
    }

    @Test
    void testWithLanPortAdoptionAndDefaultProfile(Tenant tenant, Venue venue) {
      testEthernetPortProfileWithDefaultProfile(tenant, venue);
    }

    @Test
    void testWithoutLanPortAdoption(Tenant tenant, Venue venue) {
      testEthernetPortProfileWithoutLanPortAdoption(tenant, venue);
    }

  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_WIRED_CLIENT_VISIBILITY_FOR_LANPORT_TOGGLE})
  @Test
  void testWiredClientVisibility(Tenant tenant, Venue venue) {
    EthernetPortProfile ethernetPortProfile = Generators.ethernetPortProfile()
        .setType(always(ApLanPortTypeEnum.ACCESS))
        .setAuthType(always(ApLanPortAuthTypeEnum.OPEN))
        .setVenueId(always(venue.getId()))
        .setTenant(always(tenant))
        .generate();

    com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result =
        builder.build(new NewTxEntity<>(ethernetPortProfile), emptyTxChanges())
            .stream().filter(operation -> operation.hasApLanPortProfile())
            .findFirst().get().getApLanPortProfile();

    assertTrue(result.hasUserSidePort());
    assertTrue(result.getUserSidePort().getUserSidePortEnabled());
    assertEquals(16, result.getUserSidePort().getUserSidePortMaxClient().getValue());
  }

  private void testEthernetPortProfileWithDefaultProfile(Tenant tenant, Venue venue) {
    var options = new SupplicantAuthenticationOptions();
    options.setType(ApLanPortSupplicantTypeEnum.MAC_AUTH);
    var profile =
        Generators.ethernetPortProfile()
            .setType(always(ApLanPortTypeEnum.TRUNK))
            .setAuthType(always(ApLanPortAuthTypeEnum.SUPPLICANT))
            .setSupplicantAuthenticationOptions(always(options))
            .setVenueId(always(venue.getId()))
            .setTenant(always(tenant))
            .setIsDefault(always(true))
            .generate();
    doReturn(1L).when(lanPortAdoptionRepository).countByTenantId(anyString());
    var operations = builder.build(new NewTxEntity<>(profile), emptyTxChanges());

    assertFalse(operations.isEmpty());
    verify(builder, times(1)).skip(any());
    verify(builder).configEthernetPortProfile(any(), any(), any());
  }

  private void testEthernetPortProfileWithLanPortAdoption(Tenant tenant, Venue venue) {
    var options = new SupplicantAuthenticationOptions();
    options.setType(ApLanPortSupplicantTypeEnum.MAC_AUTH);
    var profile =
        Generators.ethernetPortProfile()
            .setType(always(ApLanPortTypeEnum.TRUNK))
            .setAuthType(always(ApLanPortAuthTypeEnum.SUPPLICANT))
            .setSupplicantAuthenticationOptions(always(options))
            .setVenueId(always(venue.getId()))
            .setTenant(always(tenant))
            .setIsDefault(always(false))
            .generate();
    doReturn(1L).when(lanPortAdoptionRepository).countByTenantId(anyString());
    var operations = builder.build(new NewTxEntity<>(profile), emptyTxChanges());

    assertTrue(operations.isEmpty());
    verify(builder, times(1)).skip(any());
    verify(builder, never()).configEthernetPortProfile(any(), any(), any());
  }

  private void testEthernetPortProfileWithoutLanPortAdoption(Tenant tenant, Venue venue) {
    var options = new SupplicantAuthenticationOptions();
    options.setType(ApLanPortSupplicantTypeEnum.MAC_AUTH);
    var profile =
        Generators.ethernetPortProfile()
            .setType(always(ApLanPortTypeEnum.TRUNK))
            .setAuthType(always(ApLanPortAuthTypeEnum.SUPPLICANT))
            .setSupplicantAuthenticationOptions(always(options))
            .setVenueId(always(venue.getId()))
            .setTenant(always(tenant))
            .generate();
    doReturn(0L).when(lanPortAdoptionRepository).countByTenantId(anyString());
    var operations = builder.build(new NewTxEntity<>(profile), emptyTxChanges());

    assertFalse(operations.isEmpty());
    verify(builder, times(1)).skip(any());
    verify(builder, times(1)).configEthernetPortProfile(any(), any(), any());
  }

  private void verifyApLanPortProfile(EthernetPortProfile ethernetPortProfile,
      com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result) {
    assertEquals(ethernetPortProfile.getApLanPortId(), result.getId());
    assertEquals(ethernetPortProfile.getId(), result.getName());
    assertEquals(ethernetPortProfile.getTenant().getId(), result.getTenantId());
    assertEquals(ethernetPortProfile.getVenueId(), result.getVenueId());
    assertEquals(ethernetPortProfile.getVlanMembers(), result.getVlanMembers());
    assertEquals(ethernetPortProfile.getType().toString() + "PORT", result.getLanPortType().toString());
  }

  private void verifyTrunkPortUntagVlanId(ApLanPortProfile apLanPortProfile,
      com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result) {
    assertEquals(1, result.getUntagId());
    assertEquals(apLanPortProfile.getUntagId().intValue(), result.getUntagVlanId());
  }

  private void verifyNonTrunkPortUntagVlanId(ApLanPortProfile apLanPortProfile,
      com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile result) {
    assertEquals(apLanPortProfile.getUntagId().intValue(), result.getUntagId());
    assertEquals(apLanPortProfile.getUntagId().intValue(), result.getUntagVlanId());
  }
}
