package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.service.core.util.ImportConfig.TX_ID;
import static com.ruckus.cloud.wifi.service.handler.ethernetport.EthernetPortProfileDefaultProfileHelper.DEFAULT_ACCESS_AP_LAN_PORT_ID;
import static com.ruckus.cloud.wifi.service.handler.ethernetport.EthernetPortProfileDefaultProfileHelper.DEFAULT_TRUNK_AP_LAN_PORT_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.context.ContextHelper;
import com.ruckus.cloud.wifi.core.autoconfig.HibernateAutoConfiguration;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackage;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.ApModelGreenfieldFirmwareRepository;
import com.ruckus.cloud.wifi.repository.EthernetPortProfileRepository;
import com.ruckus.cloud.wifi.repository.SignaturePackageRepository;
import com.ruckus.cloud.wifi.repository.TenantAvailableApFirmwareRepository;
import com.ruckus.cloud.wifi.repository.TenantCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.repository.TenantFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.TenantRepository;
import com.ruckus.cloud.wifi.service.ApVersionService;
import com.ruckus.cloud.wifi.service.ExtendedTenantServiceCtrl;
import com.ruckus.cloud.wifi.service.SignaturePackageService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.integration.config.ApBranchFamilyServiceRouterTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedTenantServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApModelGreenfieldFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO ap_version (id, category, target, created_date, updated_date) VALUES
        ('6.2.1.103.1588', 'RECOMMENDED', NULL, '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
        ('6.2.1.103.1587', 'RECOMMENDED', 'DEFAULT', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
        ('6.2.0.103.999999','RECOMMENDED', NULL, '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
        ('6.2.0.103.500', 'RECOMMENDED', NULL, '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
        ('6.2.1.103.100', 'RECOMMENDED', NULL, '2022-03-20 14:23:02.495', '2022-03-20 14:23:02.495');
    INSERT INTO tenant (id) VALUES ('rkg8y5147b3d45d892bd53f2761032b5');
    """)
class ExtendedTenantServiceTest {

  @Autowired
  private ExtendedTenantServiceCtrl tenantService;

  @Autowired
  private TenantFirmwareVersionRepository tenantFirmwareVersionRepository;

  @MockBean
  private SignaturePackageService signaturePackageService;

  @MockBean
  private ApVersionService apVersionService;

  @Autowired
  private TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository;

  @Autowired
  private TenantCurrentFirmwareRepository tenantCurrentFirmwareRepository;

  @Autowired
  private ApModelGreenfieldFirmwareRepository apModelGreenfieldFirmwareRepository;

  @Autowired
  private SignaturePackageRepository signaturePackageRepository;

  @Autowired
  private EthernetPortProfileRepository ethernetPortProfileRepository;

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withTenant(TENANT_ID);

  private static final String TENANT_ID = "rkg8y5147b3d45d892bd53f2761032b5";
  private static final String EOL_2022_VERSION = "6.2.0.103.500";
  private static final String ACTIVE_VERSION = "6.2.1.103.100";
  @Autowired
  private TenantRepository tenantRepository;

  @Test
  @FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL, FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE})
  void testAddTenant_alignApModelGreenfield(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.111") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.123") ApVersion version623)
      throws Exception {
    Tenant tenant = new Tenant(randomId());
    ContextHelper.execute(() -> {
      apModelGreenfieldFirmwareRepository.saveAll(List.of(
          ApModelGreenfieldFirmwareTestFixture.randomApModelGreenfieldFirmware("R500", version620),
          ApModelGreenfieldFirmwareTestFixture.randomApModelGreenfieldFirmware("R510", version620),
          ApModelGreenfieldFirmwareTestFixture.randomApModelGreenfieldFirmware("R550", version623),
          ApModelGreenfieldFirmwareTestFixture.randomApModelGreenfieldFirmware("R560", version623)
      ));

      tenantService.addTenant(tenant);

      assertEquals(version623.getId(), tenant.getLatestReleaseVersion().getId());
      assertThat(tenantCurrentFirmwareRepository.findByTenantId(tenant.getId()))
          .hasSize(4)
          .extracting(TenantCurrentFirmware::getApModel, tcf -> tcf.getFirmware().getId())
          .containsExactlyInAnyOrder(
              tuple("R500", version620.getId()),
              tuple("R510", version620.getId()),
              tuple("R550", version623.getId()),
              tuple("R560", version623.getId()));

      List<String> taafFirmwareVersionList = tenantAvailableApFirmwareRepository.findByTenant(tenant).stream()
          .map(taaf -> taaf.getApVersion().getId()).toList();
      assertThat(taafFirmwareVersionList)
          .hasSize(2)
          .containsExactlyInAnyOrder(version620.getId(), version623.getId());
    }, tenant.getId(), TxCtxHolder.txId(), TxCtxHolder.get().getFlowName());
  }

  @Test
  void testAddTenantWith_defaultFw_MultiRecommendedFw_EolFw() {
    // Give: FW in Sql annotation, EOL in ConfigurationProperties-ApBranchFamilyManagementConfiguration
    // Tenant should not in DB
    Tenant tenant = new Tenant(randomId());
    ApVersion activeVersion = newApVersion(ACTIVE_VERSION);
    ApVersion nonActiveVersion = newApVersion(EOL_2022_VERSION);
    when(apVersionService.getActiveDefaultFirmwareVersion()).thenReturn(activeVersion);
    when(apVersionService.getNonActiveApVersions()).thenReturn(List.of(nonActiveVersion));

    // When
    tenantService.addTenant(tenant);

    // Then
    assertEquals(ACTIVE_VERSION, tenant.getLatestReleaseVersion().getId());
    List<TenantFirmwareVersion> tfvList = tenantFirmwareVersionRepository.findByTenantId(tenant.getId());
    assertEquals(EOL_2022_VERSION, tfvList.get(0).getLatestFirmwareVersion().getId());
  }

  @Test
  void testAddAndGetTenantWith_signaturePackage() throws Exception {
    Tenant tenant = new Tenant(randomId());
    SignaturePackage signaturePackage = new SignaturePackage("v2-1.510.1");
    signaturePackageRepository.save(signaturePackage);
    when(signaturePackageService.getDefaultSignaturePackage()).thenReturn(signaturePackage);

    // When
    tenant = tenantService.addAndGet(tenant.getId(), randomName());

    // Then
    assertEquals("v2-1.510.1", tenant.getSignaturePackage().getId());
    assertEquals("v2-1.510.1", tenant.getLatestSignaturePackage().getId());
  }

  @Test
  void testAddTenantWith_abfDefaultFw() {
    // Give
    Tenant tenant = new Tenant(randomId());
    ApVersion activeVersion = newApVersion(ACTIVE_VERSION);
    ApVersion nonActiveVersion = newApVersion(EOL_2022_VERSION);
    when(apVersionService.getActiveDefaultFirmwareVersion()).thenReturn(activeVersion);
    when(apVersionService.getNonActiveApVersions()).thenReturn(List.of(nonActiveVersion));
    // When
    tenantService.addTenant(tenant);

    // Then
    assertEquals(ACTIVE_VERSION, tenant.getLatestReleaseVersion().getId());
    List<TenantAvailableApFirmware> taafList = tenantAvailableApFirmwareRepository.findByTenant(tenant);
    assertEquals(2, taafList.size());
    List<TenantFirmwareVersion> tfvList = tenantFirmwareVersionRepository.findByTenantId(tenant.getId());
    assertEquals(EOL_2022_VERSION, tfvList.get(0).getLatestFirmwareVersion().getId());
  }

  @Tag("EthernetPortProfileTest")
  @Test
  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE})
  void testAddTenantWith_defaultApLanPortProfile() {

    // Give
    String tenantId = randomId();
    Tenant tenant = new Tenant(tenantId);
    TxCtxHolder.set(new TxCtxHolder.TxCtx(tenantId, TX_ID, "test", "test flow"));

    Optional<Tenant> dbTenant = tenantRepository.findById(tenantId);
    assertFalse(dbTenant.isPresent());

    Optional<EthernetPortProfile> dbDataTrunk = ethernetPortProfileRepository
        .findByTenantIdAndApLanPortIdAndIsTemplate(tenant.getId(), DEFAULT_TRUNK_AP_LAN_PORT_ID, false);
    assertFalse(dbDataTrunk.isPresent());

    // When
    tenantService.addTenant(tenant);

    // Then
    dbTenant = tenantRepository.findById(tenantId);
    assertTrue(dbTenant.isPresent());

    Optional<EthernetPortProfile> dbDataAccess = ethernetPortProfileRepository
        .findByTenantIdAndApLanPortIdAndIsTemplate(tenant.getId(), DEFAULT_ACCESS_AP_LAN_PORT_ID, false);
    assertTrue(dbDataAccess.isPresent());

    dbDataTrunk = ethernetPortProfileRepository
        .findByTenantIdAndApLanPortIdAndIsTemplate(tenant.getId(), DEFAULT_TRUNK_AP_LAN_PORT_ID, false);
    assertTrue(dbDataTrunk.isPresent());
    assertEquals(DEFAULT_TRUNK_AP_LAN_PORT_ID, dbDataTrunk.get().getApLanPortId());
    assertEquals(ApLanPortTypeEnum.TRUNK, dbDataTrunk.get().getType());
    assertEquals("1-4094", dbDataTrunk.get().getVlanMembers());
    assertEquals((short) 1 , dbDataTrunk.get().getUntagId());
    assertTrue(dbDataTrunk.get().getIsDefault());
  }

  private ApVersion newApVersion(String version) {
    return ApVersionTestFixture.recommendedApVersion(version, v -> v.setName(version));
  }

  @TestConfiguration
  @Import({
      HibernateAutoConfiguration.class,
      ExtendedTenantServiceCtrlImplTestConfig.class,
      ApBranchFamilyServiceRouterTestConfig.class
  })
  static class TestConfig {

  }
}
