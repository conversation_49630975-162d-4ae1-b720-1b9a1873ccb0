package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.servicemodel.projection.ScheduleUpgradeFinishedQueryProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApModelCountProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueUpgradeVersionProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApVersionProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
class UpgradeScheduleRepositoryTest {

  @Autowired
  private UpgradeScheduleRepository repository;

  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.122'), ('*********.123'), ('*********.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line) 
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.123', 'venueId2'),
               ('tenantId1', 'scheduleId3', 'RUNNING', 'timeSlotId', '*********.122', 'venueId3');
      """)
  void findByTenantIdTest() {
    assertThat(repository.findByTenantId("tenantId1")).hasSize(2).extracting(schedule -> schedule.getVersion().getId())
        .containsExactlyInAnyOrder("*********.122", "*********.124");
    assertThat(repository.findByTenantId("tenantId2")).hasSize(1).extracting(schedule -> schedule.getVersion().getId())
        .containsExactlyInAnyOrder("*********.123");
    assertThat(repository.findByTenantId("tenantId3")).isEmpty();
  }

  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line) 
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.124', 'venueId2'),
               ('tenantId1', 'scheduleId3', 'RUNNING', 'timeSlotId', '*********.124', 'venueId3');
      """)
  void findByTenantIdAndVenueIdTest() {
    assertThat(repository.findByTenantIdAndVenueId("tenantId1", "venueId1"))
        .isNotEmpty().singleElement().extracting(AbstractBaseEntity::getId).isEqualTo("scheduleId1");
    assertThat(repository.findByTenantIdAndVenueId("tenantId2", "venueId2"))
        .isNotEmpty().singleElement().extracting(AbstractBaseEntity::getId).isEqualTo("scheduleId2");
    assertThat(repository.findByTenantIdAndVenueId("tenantId1", "venueId3"))
        .isNotEmpty().singleElement().extracting(AbstractBaseEntity::getId).isEqualTo("scheduleId3");
    assertThat(repository.findByTenantIdAndVenueId("tenantId1", "venueId2")).isEmpty();
    assertThat(repository.findByTenantIdAndVenueId("tenantId1", "venueId4")).isEmpty();
  }

  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line) 
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.124', 'venueId2'),
               ('tenantId1', 'scheduleId3', 'RUNNING', 'timeSlotId', '*********.124', 'venueId3');
      """)
  void findByTenantIdAndVenueIdInTest() {
    assertThat(repository.findByTenantIdAndVenueIdIn("tenantId1", List.of("venueId1", "venueId3")))
        .hasSize(2).extracting(AbstractBaseEntity::getId).containsExactlyInAnyOrder("scheduleId1", "scheduleId3");
    assertThat(repository.findByTenantIdAndVenueIdIn("tenantId1", List.of("venueId3")))
        .isNotEmpty().singleElement().extracting(AbstractBaseEntity::getId).isEqualTo("scheduleId3");
    assertThat(repository.findByTenantIdAndVenueIdIn("tenantId2", List.of("venueId1", "venueId2")))
        .isNotEmpty().singleElement().extracting(AbstractBaseEntity::getId).isEqualTo("scheduleId2");
    assertThat(repository.findByTenantIdAndVenueIdIn("tenantId1",  List.of("venueId2", "venueId5"))).isEmpty();
    assertThat(repository.findByTenantIdAndVenueIdIn("tenantId1", List.of())).isEmpty();
  }

  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line) 
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.124', 'venueId2'),
               ('tenantId1', 'scheduleId3', 'RUNNING', 'timeSlotId', '*********.124', 'venueId3');
      """)
  void findUpgradeFinishedByTimeSlotTest() {
    List<String> expectedScheduleIds = List.of("scheduleId1", "scheduleId3");

    List<ScheduleUpgradeFinishedQueryProjection> projections =
        repository.findUpgradeFinishedByTimeSlot(new Date());

    assertThat(projections).isNotNull().hasSize(3)
        .filteredOn(p -> p.getTenantId().equals("tenantId1")).hasSize(2)
        .allMatch(p -> expectedScheduleIds.contains(p.getUpgradeScheduleId()));
  }

  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id) VALUES ('tenantId1');
      INSERT INTO ap_version (id) VALUES ('6.2.2.103.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time) 
        VALUES ('timeSlotId', 'WAITING_REMINDER', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version) 
        VALUES ('tenantId1', 'venueId1', '6.2.2.103.124'),
               ('tenantId1', 'venueId2', '6.2.2.103.124');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '6.2.2.103.124', 'venueId1'),
               ('tenantId1', 'scheduleId2', 'RUNNING', 'timeSlotId', '6.2.2.103.124', 'venueId1');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId3', 'PENDING', 'timeSlotId', '6.2.2.103.124', 'venueId2'),
               ('tenantId1', 'scheduleId4', 'RUNNING', 'timeSlotId', '6.2.2.103.124', 'venueId2');
      """)
  void findAllByVenueIdTest() {
    List<UpgradeSchedule> upgradeSchedules = repository.findAllByVenueId("venueId1");
    assertThat(upgradeSchedules).isNotNull().hasSize(2)
        .anyMatch(s -> s.getStatus().equals(UpgradeScheduleStatus.PENDING))
        .anyMatch(s -> s.getStatus().equals(UpgradeScheduleStatus.RUNNING));

    upgradeSchedules = repository.findAllByVenueId("venueId2");
    assertThat(upgradeSchedules).isNotNull().hasSize(2)
        .anyMatch(s -> s.getStatus().equals(UpgradeScheduleStatus.PENDING))
        .anyMatch(s -> s.getStatus().equals(UpgradeScheduleStatus.RUNNING));
  }

  @Test
  @Sql(statements = """
    TRUNCATE TABLE upgrade_schedule CASCADE;
    INSERT INTO tenant (id) VALUES ('tenantId1');
    INSERT INTO venue (tenant, id) VALUES ('tenantId1', 'venueId1');
    INSERT INTO venue (tenant, id) VALUES ('tenantId1', 'venueId2');
    INSERT INTO ap_version (id) VALUES ('6.2.2.103.124');
    INSERT INTO ap_version (id) VALUES ('*********.124');
    INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time) 
      VALUES ('timeSlotId', 'WAITING_REMINDER', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
    INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
      VALUES ('tenantId1', 'upgrade-scheduleId1', 'PENDING', 'timeSlotId', '6.2.2.103.124', 'venueId1');
    INSERT INTO upgrade_schedule_firmware_version(id, tenant, ap_firmware_version, upgrade_schedule)
      VALUES ('upgrade-schedule-fwId1', 'tenantId1', '6.2.2.103.124', 'upgrade-scheduleId1');
    INSERT INTO upgrade_schedule_firmware_version(id, tenant, ap_firmware_version, upgrade_schedule)
      VALUES ('upgrade-schedule-fwId2', 'tenantId1', '*********.124', 'upgrade-scheduleId1');
    INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
      VALUES ('tenantId1', 'upgrade-scheduleId2', 'RUNNING', 'timeSlotId', '6.2.2.103.124', 'venueId2');
    INSERT INTO upgrade_schedule_firmware_version(id, tenant, ap_firmware_version, upgrade_schedule)
      VALUES ('upgrade-schedule-fwId3', 'tenantId1', '6.2.2.103.124', 'upgrade-scheduleId2');
  """)
  void findUpgradeVersionByVenues() {
    List<String> venues = List.of("venueId1", "venueId2");
    List<VenueUpgradeVersionProjection> projections = repository.findUpgradeVersionByVenueIds(venues);
    assertThat(projections)
        .isNotNull()
        .hasSize(3);

    List<VenueUpgradeVersionProjection> pendingUpgradeProjections = projections.stream()
        .filter(p -> p.status().equals(UpgradeScheduleStatus.PENDING))
        .toList();
    assertThat(pendingUpgradeProjections)
        .isNotNull()
        .hasSize(2);
  }

  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.124'), ('*********.100');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId1', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId2', 'IN_QUEUE', '2022-02-19 07:00:00.000', '2022-02-19 09:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line)
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId2', 'venueId4', '*********.124', 'venueName4', 'addr4');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue)
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId1', '*********.124', 'venueId1'),
               ('tenantId1', 'scheduleId3', 'RUNNING', 'timeSlotId1', '*********.124', 'venueId3'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId1', '*********.124', 'venueId2'),
               ('tenantId2', 'scheduleId4', 'RUNNING', 'timeSlotId1', '*********.124', 'venueId4');
      INSERT INTO upgrade_schedule_firmware_version(id, tenant, upgrade_schedule, ap_firmware_version)
        VALUES ('usfv1', 'tenantId1', 'scheduleId1', '*********.124'),
               ('usfv2','tenantId1', 'scheduleId1', '*********.100'),
               ('usfv3','tenantId1', 'scheduleId3', '*********.124'),
               ('usfv4','tenantId1', 'scheduleId3', '*********.100'),
               ('usfv5','tenantId2', 'scheduleId2', '*********.124'),
               ('usfv6','tenantId2', 'scheduleId2', '*********.100'),
               ('usfv7','tenantId2', 'scheduleId4', '*********.124'),
               ('usfv8','tenantId2', 'scheduleId4', '*********.100');
      """)
  void findVenueApVersionByTimeSlotInTest() {
    List<VenueApVersionProjection> projections =
        repository.findRunningApVersionsByTimeSlotIn(List.of("timeSlotId1", "timeSlotId2"));

    assertThat(projections).isNotNull().hasSize(4)
        .filteredOn(p -> p.venueId().equals("venueId3")).hasSize(2)
        .anyMatch(p -> p.apVersionId().equals("*********.100"))
        .anyMatch(p -> p.apVersionId().equals("*********.124"));
  }

  //Test findFirstUpgradeScheduleByVenueIdAndStatusIsPendingOrNull
  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line) 
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.124', 'venueId2'),
               ('tenantId1', 'scheduleId3', 'RUNNING', 'timeSlotId', '*********.124', 'venueId3');
      """)
  void findFirstUpgradeScheduleByVenueIdAndStatusIsPendingTest() {
    UpgradeSchedule schedule = repository
        .findFirstByVenueIdAndStatusIsPendingOrNullOrderById("venueId1");
    assertThat(schedule).isNotNull().extracting(UpgradeSchedule::getId)
        .isEqualTo("scheduleId1");
  }

  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line) 
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', NULL, 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.124', 'venueId2'),
               ('tenantId1', 'scheduleId3', NULL, 'timeSlotId', '*********.124', 'venueId3');
      """)
  void findFirstUpgradeScheduleByVenueIdAndStatusIsNullTest() {
    UpgradeSchedule schedule1 = repository
        .findFirstByVenueIdAndStatusIsPendingOrNullOrderById("venueId1");
    assertThat(schedule1).isNotNull().extracting(UpgradeSchedule::getId)
        .isEqualTo("scheduleId1");
    UpgradeSchedule schedule3 = repository
        .findFirstByVenueIdAndStatusIsPendingOrNullOrderById("venueId3");
    assertThat(schedule3).isNotNull().extracting(UpgradeSchedule::getId)
        .isEqualTo("scheduleId3");
  }

  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.124'), ('*********.2250');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line) 
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', NULL, 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId1', 'scheduleId4', 'RUNNING', 'timeSlotId', '*********.2250', 'venueId1'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.124', 'venueId2'),
               ('tenantId1', 'scheduleId3', NULL, 'timeSlotId', '*********.124', 'venueId3');
      """)
  void findFirstByTenantIdAndVenueIdInAndStatusIsPendingOrNull() {
    assertThat(repository.findFirstByTenantIdAndVenueIdInAndStatusIsPendingOrNull("tenantId1", List.of("venueId1")))
        .isNotEmpty()
        .extracting(UpgradeSchedule::getId)
        .containsExactlyInAnyOrder("scheduleId1");
    assertThat(repository.findFirstByTenantIdAndVenueIdInAndStatusIsPendingOrNull("tenantId1", List.of("venueId3")))
        .isNotEmpty()
        .extracting(UpgradeSchedule::getId)
        .containsExactlyInAnyOrder("scheduleId3");
    assertThat(repository.findFirstByTenantIdAndVenueIdInAndStatusIsPendingOrNull("tenantId1",
        List.of("venueId1", "venueId3")))
        .isNotEmpty()
        .extracting(UpgradeSchedule::getId)
        .containsExactlyInAnyOrder("scheduleId1", "scheduleId3");
    assertThat(repository.findFirstByTenantIdAndVenueIdInAndStatusIsPendingOrNull("tenantId2",
        List.of("venueId1", "venueId2", "venueId3")))
        .isNotEmpty()
        .extracting(UpgradeSchedule::getId)
        .containsExactlyInAnyOrder("scheduleId2");
  }

  @Test
  @Sql(
      statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.124'), ('*********.125');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line)
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue)
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.124', 'venueId2'),
               ('tenantId1', 'scheduleId3', 'PENDING', 'timeSlotId', '*********.125', 'venueId1');
      INSERT INTO upgrade_schedule_firmware_version(tenant, id, ap_firmware_version, upgrade_schedule, target_ap_models)
        VALUES ('tenantId1', 'scheduleId1', '*********.124', 'scheduleId1', '["R550", "R560"]'),
               ('tenantId2', 'scheduleId2', '*********.124', 'scheduleId2', '["R550", "R560"]'),
               ('tenantId1', 'scheduleId3', '*********.124', 'scheduleId3', '["R750", "R760", "R770"]');
      INSERT INTO ap_group (id, tenant, venue)
          VALUES ('apg1', 'tenantId1','venueId1'),
                 ('apg2', 'tenantId2','venueId2');
      INSERT INTO ap (id, model, tenant, ap_group, soft_deleted)
          VALUES ('ap1-1', 'R750', 'tenantId1','apg1', false),
                 ('ap1-2', 'R750', 'tenantId1','apg1', false),
                 ('ap1-3', 'R550', 'tenantId1','apg1', false),
                 ('ap2-1', 'R550', 'tenantId2','apg2', false),
                 ('ap2-2', 'R560', 'tenantId2','apg2', false),
                 ('ap2-3', 'R650', 'tenantId2','apg2', false);
          """
  )
  void findImpactedApCountByVenueIdsAndUpgradeScheduleStatusTest() {
    List<VenueApModelCountProjection> projections = repository
        .findImpactedApCountByVenueIdsAndUpgradeScheduleStatus(
            List.of("venueId1", "venueId2", "venueId3"), UpgradeScheduleStatus.PENDING);

    assertThat(projections).isNotNull().hasSize(4)
        .anyMatch(p -> p.getVenueId().equals("venueId1") && p.getApModel().equals("R750")
            && p.getApCount() == 2)
        .anyMatch(p -> p.getVenueId().equals("venueId1") && p.getApModel().equals("R550")
            && p.getApCount() == 1)
        .anyMatch(p -> p.getVenueId().equals("venueId2") && p.getApModel().equals("R550")
            && p.getApCount() == 1)
        .anyMatch(p -> p.getVenueId().equals("venueId2") && p.getApModel().equals("R560")
            && p.getApCount() == 1);
  }

  @Test
  @Sql(statements = """
      TRUNCATE TABLE upgrade_schedule CASCADE;
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2'), ('tenantId3', 'tenantName3');
      INSERT INTO ap_version (id) VALUES ('*********.124'), ('*********.100');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId1', 'IN_QUEUE', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line)
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId3', 'venueId4', '*********.124', 'venueName4', 'addr4');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue)
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId1', '*********.124', 'venueId1'),
               ('tenantId1', 'scheduleId3', 'RUNNING', 'timeSlotId1', '*********.124', 'venueId3'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId1', '*********.124', 'venueId2'),
               ('tenantId3', 'scheduleId4', 'RUNNING', 'timeSlotId1', '*********.124', 'venueId4');
      INSERT INTO upgrade_schedule_firmware_version(id, tenant, upgrade_schedule, ap_firmware_version, target_ap_models)
        VALUES ('usfv1', 'tenantId1', 'scheduleId1', '*********.124', '["R550"]'),
               ('usfv2','tenantId1', 'scheduleId1', '*********.100', '["R550"]'),
               ('usfv3','tenantId1', 'scheduleId3', '*********.124', '["R550"]'),
               ('usfv4','tenantId1', 'scheduleId3', '*********.100', '["R550"]'),
               ('usfv5','tenantId2', 'scheduleId2', '*********.124', '["R550"]'),
               ('usfv6','tenantId2', 'scheduleId2', '*********.100', null),
               ('usfv7','tenantId3', 'scheduleId4', '*********.124', '["R550"]'),
               ('usfv8','tenantId3', 'scheduleId4', '*********.100', null);
      """)
  void findTenantIdsByStatusAndTargetApModelsIsNullTest() {
    assertThat(repository.findTenantIdsByStatusAndTargetApModelsIsNull(UpgradeScheduleStatus.PENDING))
        .hasSize(1)
        .singleElement()
        .isEqualTo("tenantId2");
  }
}
