package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.core.model.Identifiable;
import com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.BDDMockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@WifiUnitTest
public class LbsServerProfileCmnCfgCollectorOperationBuilderTest {

  @MockBean
  private VenueRepository venueRepository;

  @SpyBean
  private LbsServerProfileCmnCfgCollectorOperationBuilder lbsServerProfileCmnCfgCollectorOperationBuilder;

  @Test
  void testGetEntityClass() {
    assertThat(lbsServerProfileCmnCfgCollectorOperationBuilder.entityClass())
        .isEqualTo(LbsServerProfile.class);
  }

  @Test
  void testGetIndex() {
    assertThat(lbsServerProfileCmnCfgCollectorOperationBuilder.index()).isEqualTo(
        EsConstants.Index.LBS_SERVER_PROFILE_INDEX_NAME);
  }

  @Nested
  class TestBuildConfig {

    @Test
    void givenEntityActionIsDelete() {
      Operations operations = lbsServerProfileCmnCfgCollectorOperationBuilder.build(
          new TxEntity<>(Generators.lbsServerProfile().generate(), EntityAction.DELETE), emptyTxChanges()).get(0);

      assertThat(operations.getDocCount())
          .isZero();
    }

    @Test
    void givenAddLbsServerProfile(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      var lbsServerProfile = Generators.lbsServerProfile()
          .setTenant(always(tenant))
          .generate();

      lbsServerProfileCmnCfgCollectorOperationBuilder.config(builder, lbsServerProfile, EntityAction.ADD);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(lbsServerProfile, docMap);
    }
  }

  @Nested
  class TestHasModification {

    @Test
    void givenEntityActionIsNotModify() {
      assertThat(lbsServerProfileCmnCfgCollectorOperationBuilder
          .hasChanged(new NewTxEntity<>(Generators.lbsServerProfile().generate()), emptyTxChanges()))
          .isTrue();
    }

    @Test
    void givenUpdateLbsProfile(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      var lbsServerProfile = Generators.lbsServerProfile()
          .setTenant(always(tenant))
          .generate();

      lbsServerProfileCmnCfgCollectorOperationBuilder.config(builder, lbsServerProfile, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(lbsServerProfile, docMap);
    }

    @Test
    void givenUpdateLbsProfileWithNetworkIds(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      var lbsServerProfile = Generators.lbsServerProfile()
          .setTenant(always(tenant))
          .generate();
      List<Identifiable> venueIds = defaultIdGenerator().toListGenerator(3).generate()
          .stream().map(id -> BDDMockito.given(mock(Identifiable.class).getId()).willReturn(id).<Identifiable>getMock()).toList();

      when(venueRepository.findByTenantIdAndLbsServerProfileId(eq(tenant.getId()),
          eq(lbsServerProfile.getId())))
          .thenReturn(venueIds);

      lbsServerProfileCmnCfgCollectorOperationBuilder.config(builder, lbsServerProfile, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(lbsServerProfile, docMap, venueIds.stream().map(Identifiable::getId).toList());
    }
  }

  private void validateResult(LbsServerProfile lbsServerProfile, Map<String, Value> docMap) {
    validateResult(lbsServerProfile, docMap, Collections.emptyList());
  }

  private void validateResult(LbsServerProfile lbsServerProfile, Map<String, Value> docMap, List<String> venueIds) {
    assertThat(docMap.get(EsConstants.Key.ID))
        .matches(doc -> doc.getKindCase() == Value.KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(lbsServerProfile.getId());

    assertThat(docMap.get(EsConstants.Key.NAME))
        .matches(doc -> doc.getKindCase() == Value.KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(lbsServerProfile.getName());

    assertThat(docMap.get(EsConstants.Key.TENANT_ID))
        .matches(doc -> doc.getKindCase() == Value.KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(lbsServerProfile.getTenant().getId());

    assertThat(docMap.get(EsConstants.Key.LBS_SERVER_VENUE_NAME))
        .matches(doc -> doc.getKindCase() == Value.KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(lbsServerProfile.getLbsServerVenueName());

    assertThat(docMap.get(EsConstants.Key.SERVER))
        .matches(doc -> doc.getKindCase() == Value.KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(String.format("%s:%d", lbsServerProfile.getServerAddress(), lbsServerProfile.getServerPort()));

    assertThat(docMap.get(EsConstants.Key.VENUE_IDS))
        .matches(doc -> doc.getKindCase() == Value.KindCase.LIST_VALUE)
        .extracting(p -> p.getListValue().getValuesList().stream().map(Value::getStringValue).toList())
        .isEqualTo(venueIds);
  }

}
