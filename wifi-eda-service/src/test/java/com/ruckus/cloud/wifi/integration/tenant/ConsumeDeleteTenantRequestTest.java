package com.ruckus.cloud.wifi.integration.tenant;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.kafka.publisher.ViewmodelConfigPublisher.RKS_CFG_TENANT_ACTION;
import static com.ruckus.cloud.wifi.kafka.publisher.ViewmodelConfigPublisher.RKS_TO_BE_DELETED_INDICES;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.nuketenant.protobuf.message.Nuketenant.DeleteTenant;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.notification.dnb.gpb.Dnb;
import com.ruckus.cloud.notification.dnb.gpb.Dnb.DNB;
import com.ruckus.cloud.notification.dnb.gpb.Dnb.TaskFactoryReset;
import com.ruckus.cloud.notification.service.gpb.DeviceNotification;
import com.ruckus.cloud.notification.service.gpb.DeviceNotification.DeviceType;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicyVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.GuestPortalGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.VenuePortalGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.TenantAction;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.AbstractCmnCfgCollectorOperationBuilder;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.Operation;
import com.ruckus.cloud.wifi.proto.Operation.Action;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiIntegrationTest
@Sql("classpath:sql/vspot.sql")
public class ConsumeDeleteTenantRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private List<AbstractCmnCfgCollectorOperationBuilder> cmnCfgCollectorOperationBuilderList;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Test
  void testConsumeDeleteTenant_shouldPublishDeleteDlcAndFactoryReset(Tenant tenant,
      Venue venue, Ap ap) {
    final var requestId = randomTxId();
    final var deleteTenantMessage = DeleteTenant.newBuilder()
        .setTenantId(tenant.getId())
        .build();

    messageUtil.sendDeleteTenant(requestId, deleteTenantMessage);

    final var deviceRegistrationMessage = messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
            .getValue(tenant.getId(), requestId);
    assertThat(deviceRegistrationMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(deviceRegistrationMessage.getPayload())
            .matches(msg -> msg.getId().equals(ap.getId()))
            .matches(msg -> msg.getSerial().equals(ap.getId()))
            .matches(p -> p.getGroupId().equals(ap.getApGroup().getId()))
            .matches(msg -> msg.getVenueId().equals(venue.getId()))
            .matches(msg -> msg.getTenantId().equals(tenant.getId())));

    // AP: factory reset
    final var deviceNotification = messageCaptors.getDeviceNotificationMessageCaptor()
            .getValue(tenant.getId(), requestId);
    assertThat(deviceNotification).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(deviceNotification.getPayload())
            .matches(msg -> msg.getDeviceType() == DeviceType.AP)
            .extracting(DeviceNotification::getContent)
            .satisfies(assertDnb(ap.getId())));

    assertThat(repositoryUtil.find(Tenant.class, tenant.getId())).isNull();
    assertThat(repositoryUtil.find(Ap.class, ap.getId())).isNull();
  }

  @Test
  void testConsumeDeleteTenant_shouldPublishDeleteRoguePolicy(Tenant tenant, Venue venue,
      RogueClassificationPolicyVenue roguePolicyVenue) {
    final var requestId = randomTxId();
    final var deleteTenantMessage = DeleteTenant.newBuilder()
        .setTenantId(tenant.getId())
        .build();

    messageUtil.sendDeleteTenant(requestId, deleteTenantMessage);

    final var rogueApPolicy = messageCaptors.getRogueApPolicyMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(rogueApPolicy).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenant.getId()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(rogueApPolicy.getPayload())
            .matches(policy -> policy.getTenantId().equals(tenant.getId()))
            .matches(policy -> policy.getVenueId().equals(venue.getId()))
            .matches(policy -> policy.getId()
                .equals(roguePolicyVenue.getRogueClassificationPolicy().getId()))
            .matches(policy -> policy.getRulesList().isEmpty()));

    assertThat(repositoryUtil.find(Tenant.class, tenant.getId())).isNull();
    assertThat(repositoryUtil.find(RogueClassificationPolicyVenue.class,
        roguePolicyVenue.getId())).isNull();
    assertThat(repositoryUtil.find(RogueClassificationPolicy.class,
        roguePolicyVenue.getRogueClassificationPolicy().getId())).isNull();
  }

  @Test
  void testConsumeDeleteTenant_shouldPublishCfgChange(Tenant tenant,
      @OpenNetwork Network network) {
    final var requestId = randomTxId();
    final var deleteTenantMessage = DeleteTenant.newBuilder()
        .setTenantId(tenant.getId())
        .build();

    messageUtil.sendDeleteTenant(requestId, deleteTenantMessage);

    final var cfgChange = messageCaptors.getWifiCfgChangeMessageCaptor()
            .getValue(tenant.getId(), requestId);
    assertThat(cfgChange).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();
    assertThatNoException().isThrownBy(() ->
        assertThat(cfgChange.getPayload())
            .matches(e -> e.getTenantId().equals(tenant.getId()))
            .matches(e -> e.getRequestId().equals(requestId))
            .extracting(WifiConfigChange::getOperationList).asList()
            .hasSize(1)
            .singleElement()
            .extracting(Operation.class::cast)
            .matches(e ->e.getAction() == Action.DELETE)
            .matches(e -> e.getOpenNetwork().getId().getValue().equals(network.getId())));
  }

  @Test
  void testConsumeDeleteTenant_shouldPublishCmnCfgCollector_deleteIndexes(Tenant tenant) throws Exception {
    Set<String> deletedIndexes = cmnCfgCollectorOperationBuilderList.stream()
        .filter(e -> e.shouldClearOnTenantDeleted())
        .map(AbstractCmnCfgCollectorOperationBuilder::index)
        .collect(Collectors.toSet());

    final var requestId = randomTxId();
    final var deleteTenantMessage = DeleteTenant.newBuilder()
        .setTenantId(tenant.getId())
        .build();

    messageUtil.sendDeleteTenant(requestId, deleteTenantMessage);

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenant.getId(), requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .satisfies(assertHeader(RKS_CFG_TENANT_ACTION, TenantAction.DELETE.getValue()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenant.getId()))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .hasSize(1)
            .singleElement()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .matches(e -> e.getOpType().equals(OpType.DEL))
            .extracting(Operations::getDocMap)
            .satisfies(docMap -> assertSoftly(softly -> {
              softly.assertThat(docMap.get(RKS_CFG_TENANT_ACTION))
                  .extracting(com.ruckus.cloud.events.gpb.Value.class::cast)
                  .extracting(e -> e.getStringValue())
                  .isEqualTo(TenantAction.DELETE.getValue());
              softly.assertThat(docMap.get(RKS_TO_BE_DELETED_INDICES))
                  .extracting(com.ruckus.cloud.events.gpb.Value.class::cast)
                  .extracting(e -> e.getStringValue())
                  .satisfies(assertDeletedIndexes(deletedIndexes));
            })));
  }

  @Test
  void testConsumeDeleteTenant_deleteGuestPortal(Tenant tenant,
      @GuestNetwork com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork guestNetwork,
      NetworkVenue networkVenue) {
    var guestPortal = new GuestPortalGenerator()
        .setNetwork(always(guestNetwork))
        .generate();
    guestPortal = repositoryUtil.createOrUpdate(guestPortal, tenant.getId(), randomTxId());
    var venuePortal = new VenuePortalGenerator()
        .setNetworkPortal(always(guestPortal))
        .generate();
    networkVenue.setVenuePortal(venuePortal);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    final var requestId = randomTxId();
    final var deleteTenantMessage = DeleteTenant.newBuilder()
        .setTenantId(tenant.getId())
        .build();

    messageUtil.sendDeleteTenant(requestId, deleteTenantMessage);

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenant.getId(), requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();
  }

  public static Consumer<String> assertDeletedIndexes(Set<String> deletedIndexes) {
    return s -> {
      assertThat(Arrays.stream(s.split(",")).collect(Collectors.toSet()))
          .hasSize(deletedIndexes.size())
          .hasSameElementsAs(deletedIndexes)
          .doesNotContain("venue"); // Venue should not be deleted by wifi-service
    };
  }

  public static Consumer<ByteString> assertDnb(String serialNumber) {
    return byteString -> {
      try {
        assertThat(Dnb.DNB.parseFrom(byteString))
            .extracting(DNB::getTaskFactoryReset)
            .extracting(TaskFactoryReset::getCallbackUrl)
            .matches(callback -> callback.contains(serialNumber));
      } catch (InvalidProtocolBufferException e) {
        throw new RuntimeException(e);
      }
    };
  }
}
