package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeInteger;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.Operation.Action;
import com.ruckus.cloud.wifi.proto.Operation.EntityCase;
import com.ruckus.cloud.wifi.proto.SyncWifiCfgChangePostMigrationJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeSyncWifiCfgChangePostMigrationJobTest {
  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  @Tag("TunnelServiceProfileTest")
  @Nested
  class GivenTunnelProfilePersistedInDb {

    private String tunnelProfileId;

    @BeforeEach
    void givenTunnelProfileRelatedEntitiesPersistedInDb(final Tenant tenant) {
      final TunnelProfile tp = Generators.tunnelProfile()
          .setTenant(always(tenant))
          .setId(always(randomId()))
          .setMtuSize(rangeInteger())
          .generate();
      TunnelProfile tunnelProfile = repositoryUtil.createOrUpdate(tp, tenant.getId(), randomTxId());
      tunnelProfileId = tunnelProfile.getId();
    }

    @Test
    void whenConsumeSyncWifiCfgChangePostMigrationJob(final Tenant tenant) {
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId,
          WifiAsyncJob.newBuilder().setSyncWifiCfgChangePostMigrationJob(
              SyncWifiCfgChangePostMigrationJob.newBuilder()
                  .addSelectedEntityNames(TunnelProfile.class.getSimpleName())).build());

      final TunnelProfile tunnelProfile = repositoryUtil.find(TunnelProfile.class, tunnelProfileId);

      assertThat(messageCaptors.getWifiCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID,
              asserter -> asserter.startsWith(requestId)))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, "TriggerWifiConfigChangeSync"))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_ENTITY_TYPES,
              "[\"%s\"]".formatted(TunnelProfile.class.getSimpleName())))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(wifiConfigChange -> {
            assertThat(wifiConfigChange.getRequestId()).startsWith(requestId);
            assertThat(wifiConfigChange.getTenantId()).isEqualTo(tenant.getId());
            assertThat(wifiConfigChange.getOperationCount()).isOne();
            assertThat(wifiConfigChange.getOperationList()).isNotEmpty()
                .singleElement()
                .satisfies(op -> {
                  assertThat(op.getAction()).isEqualTo(Action.SYNC);
                  assertThat(op.getEntityCase()).isEqualTo(EntityCase.TUNNEL_PROFILE);
                  assertThat(op.hasTunnelProfile()).isTrue();
                  assertThat(op.getTunnelProfile()).isNotNull()
                      .satisfies(tunnelProfileProto -> {
                        assertThat(tunnelProfileProto.getId().getValue())
                            .isEqualTo(tunnelProfileId);
                        assertThat(tunnelProfileProto.getName().getValue())
                            .isEqualTo(tunnelProfile.getName());
                      });
                });
          });
    }
  }
}
