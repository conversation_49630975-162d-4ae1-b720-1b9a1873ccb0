package com.ruckus.cloud.wifi.integration.lbsserverprofile;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@WifiIntegrationTest
public class ConsumeDeleteLbsServerProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeDeleteLbsServerProfileMessage {

    private LbsServerProfile lbsServerProfile;

    @BeforeEach
    void givenLbsServerProfile(final Tenant tenant) {
      lbsServerProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.lbsServerProfile().generate();
      repositoryUtil.createOrUpdate(lbsServerProfile, tenant.getId(), randomTxId());
    }

    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("lbsServerProfileId", lbsServerProfile.getId());
    }

    @Test
    void thenDeleteAndSendingMessages(Tenant tenant) {
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.DELETE_LBS_SERVER_PROFILE,
          randomName(),
          requestParams(),
          "");

      assertThat(repositoryUtil.find(LbsServerProfile.class, lbsServerProfile.getId(), tenant.getId()))
          .isNull();

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(1)
          .first()
          .matches(o -> o.getAction() == Action.DELETE)
          .matches(o -> o.getId().equals(lbsServerProfile.getId()));

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.DEL)
          .matches(o -> o.getId().equals(lbsServerProfile.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_LBS_SERVER_PROFILE));
    }

    @Test
    void givenProfileAlreadyActivatedOnVenue(Tenant tenant) {
      final var venue = Generators.venue().generate();
      venue.setLbsServerProfile(lbsServerProfile);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.DELETE_LBS_SERVER_PROFILE,
                  randomName(),
                  requestParams(),
                  "")
      ).isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      assertThat(repositoryUtil.find(LbsServerProfile.class, lbsServerProfile.getId(), tenant.getId()))
          .isNotNull()
          .matches(p -> p.getName().equals(lbsServerProfile.getName()))
          .matches(p -> p.getLbsServerVenueName().equals(lbsServerProfile.getLbsServerVenueName()))
          .matches(p -> p.getServerAddress().equals(lbsServerProfile.getServerAddress()))
          .matches(p -> Objects.equals(p.getServerPort(), lbsServerProfile.getServerPort()))
          .matches(p -> p.getPassword().equals(lbsServerProfile.getPassword()));

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_LBS_SERVER_PROFILE));
    }
  }

}
