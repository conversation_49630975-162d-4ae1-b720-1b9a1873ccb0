package com.ruckus.cloud.wifi.integration.softgre;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SoftGreProfileTest")
@WifiIntegrationTest
class ConsumeDeleteSoftGreProfileRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeDeleteSoftGreProfileV1Message {

    @Test
    void givenProfileNotExist(Tenant tenant) {
      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.DELETE_SOFT_GRE_PROFILE,
                      randomName(),
                      new RequestParams().addPathVariable("softGreProfileId", randomId()),
                      ""))
          .isNotNull()
          .getRootCause()
          .isNotNull()
          .isInstanceOf(ObjectNotFoundException.class);

      messageCaptors
          .assertThat(
              messageCaptors.getDdccmMessageCaptor(),
              messageCaptors.getCmnCfgCollectorMessageCaptor())
          .doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_SOFT_GRE_PROFILE));
    }

    @Test
    void givenProfileActivated(Tenant tenant, NetworkVenue networkVenue) {
      var profile =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      var activation = new SoftGreProfileNetworkVenueActivation();
      activation.setTenant(tenant);
      activation.setNetworkVenue(networkVenue);
      activation.setSoftGreProfile(profile);
      repositoryUtil.createOrUpdate(activation, tenant.getId(), randomTxId());

      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.DELETE_SOFT_GRE_PROFILE,
                      randomName(),
                      new RequestParams().addPathVariable("softGreProfileId", profile.getId()),
                      ""))
          .isNotNull()
          .getRootCause()
          .isNotNull()
          .isInstanceOf(ObjectInUseException.class);

      messageCaptors
          .assertThat(
              messageCaptors.getDdccmMessageCaptor(),
              messageCaptors.getCmnCfgCollectorMessageCaptor())
          .doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_SOFT_GRE_PROFILE));
    }

    @Test
    void givenValidProfile(Tenant tenant) {
      var profile =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.DELETE_SOFT_GRE_PROFILE,
          randomName(),
          new RequestParams().addPathVariable("softGreProfileId", profile.getId()),
          "");

      assertThat(repositoryUtil.find(SoftGreProfile.class, profile.getId(), tenant.getId()))
          .isNull();

      // TODO: Add test cases for DDCCM

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.DEL)
          .matches(o -> o.getId().equals(profile.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_SOFT_GRE_PROFILE));
    }
  }
}
