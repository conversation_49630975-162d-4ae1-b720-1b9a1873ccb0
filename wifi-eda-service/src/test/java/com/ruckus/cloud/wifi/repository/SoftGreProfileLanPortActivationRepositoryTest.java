package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('id-1');
    INSERT INTO venue (id, name, tenant) VALUES ('id-2', 'venue-1', 'id-1');
    INSERT INTO network (id, tenant, type) VALUES ('id-3', 'id-1', 'OPEN');
    INSERT INTO network (id, tenant, type) VALUES ('id-9', 'id-1', 'OPEN');;
    INSERT INTO soft_gre_profile (id, tenant) VALUES ('id-5', 'id-1');
    INSERT INTO soft_gre_profile (id, tenant) VALUES ('id-6', 'id-1');
    INSERT INTO ipsec_profile (id, tenant) VALUES ('id-7', 'id-1');
    INSERT INTO ipsec_profile (id, tenant) VALUES ('id-100', 'id-1');
    INSERT INTO ap_lan_port_profile (id, tenant, category) VALUES ('id-8', 'id-1', 'ETHERNET');
    INSERT INTO lan_port_adoption (id, checksum, tenant, ap_lan_port_profile) VALUES ('id-9', 'checksum', 'id-1', 'id-8');
    INSERT INTO lan_port_adoption (id, checksum, tenant, ap_lan_port_profile) VALUES ('id-10', 'checksum', 'id-1', 'id-8');
    INSERT INTO lan_port_adoption (id, checksum, tenant, ap_lan_port_profile) VALUES ('id-11', 'checksum', 'id-1', 'id-8');
    INSERT INTO soft_gre_profile_lan_port_activation (id, tenant, lan_port_adoption, soft_gre_profile, ipsec_profile) VALUES ('id-12', 'id-1', 'id-9', 'id-5', 'id-7');
    INSERT INTO soft_gre_profile_lan_port_activation (id, tenant, lan_port_adoption, soft_gre_profile, ipsec_profile) VALUES ('id-13', 'id-1', 'id-10', 'id-6', 'id-7');
    INSERT INTO soft_gre_profile_lan_port_activation (id, tenant, lan_port_adoption, soft_gre_profile, ipsec_profile) VALUES ('id-14', 'id-1', 'id-11', 'id-6', 'id-100');
    """)
class SoftGreProfileLanPortActivationRepositoryTest {

  @Autowired
  private SoftGreProfileLanPortActivationRepository repository;

  @Test
  public void testFindSoftGreProfileLanPortActivationsByTenantIdAndIpsecProfileId() {
    var activations = repository.findSoftGreProfileLanPortActivationsByTenantIdAndIpsecProfileId("id-1", "id-7");
    assertThat(activations).hasSize(2);

    activations = repository.findSoftGreProfileLanPortActivationsByTenantIdAndIpsecProfileId("id-1", "id-100");
    assertThat(activations).hasSize(1);

    activations = repository.findSoftGreProfileLanPortActivationsByTenantIdAndIpsecProfileId("id-1", "non-exist-id");
    assertThat(activations).hasSize(0);
  }
}