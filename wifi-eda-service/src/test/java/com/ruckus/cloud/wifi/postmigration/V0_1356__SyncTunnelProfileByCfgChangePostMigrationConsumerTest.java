package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.test.Assertions.argThat;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.acx.service.postmigration.entity.PostMigration;
import com.ruckus.cloud.wifi.core.header.HttpHeaderContext;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.kafka.context.WifiAsyncJobContext;
import com.ruckus.cloud.wifi.kafka.publisher.WifiAsyncJobPublisher;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob.JobCase;
import com.ruckus.cloud.wifi.repository.TunnelProfileRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class V0_1356__SyncTunnelProfileByCfgChangePostMigrationConsumerTest {

  @SpyBean
  private V0_1356__SyncTunnelProfileByCfgChangePostMigrationConsumer unit;

  @MockBean
  private TunnelProfileRepository tunnelProfileRepository;

  @MockBean
  private WifiAsyncJobPublisher wifiAsyncJobPublisher;

  @Test
  void testRunWhenRetrievedEmptyTenantIdsShouldNotPublishWifiAsyncJobMessage() {
    // Given
    doReturn(List.of())
        .when(tunnelProfileRepository).findAllDistinctTenantIds();

    // When
    unit.run(mock(PostMigration.class));

    // Then
    verify(wifiAsyncJobPublisher, never())
        .publish(anyString(), any(), any());
  }

  @Test
  void testRunWhenRetrievedTenantIdsShouldPublishWifiAsyncJobMessageForEachTenants() {
    // Given
    final var tenantIds = List.of(randomId(), randomId());
    doReturn(tenantIds)
        .when(tunnelProfileRepository).findAllDistinctTenantIds();

    // When
    unit.run(mock(PostMigration.class));

    // Then
    verify(wifiAsyncJobPublisher, times(2)).publish(
        anyString(), // requestId
        argThat(context -> {
          assertThat(context).isNotNull();
          assertThat(context.getTenantId()).isIn(tenantIds);
          assertThat(context.getJobName())
              .isEqualTo(JobCase.SYNC_WIFI_CFG_CHANGE_POST_MIGRATION_JOB.name());
        }),
        argThat(job -> {
          assertThat(job).isNotNull();
          assertThat(job.getJobCase()).isEqualTo(JobCase.SYNC_WIFI_CFG_CHANGE_POST_MIGRATION_JOB);
          assertThat(job.hasSyncWifiCfgChangePostMigrationJob()).isTrue();
          assertThat(job.getSyncWifiCfgChangePostMigrationJob()).isNotNull()
              .satisfies(syncWifiCfgChangePostMigrationJob -> {
                assertThat(syncWifiCfgChangePostMigrationJob.getSelectedEntityNamesCount()).isOne();
                assertThat(syncWifiCfgChangePostMigrationJob.getSelectedEntityNamesList())
                    .isNotEmpty()
                    .singleElement()
                    .isEqualTo(TunnelProfile.class.getSimpleName());
              });
        }));
  }

  @Test
  void testRunShouldGenerateValidRequestId() {
    // Given
    final var tenantIds = List.of("tenant1");
    doReturn(tenantIds)
        .when(tunnelProfileRepository).findAllDistinctTenantIds();

    // When
    unit.run(mock(PostMigration.class));

    // Then
    assertThat(HttpHeaderContext.getHeader(HttpHeaderName.REQUEST_ID))
        .isNotEmpty()
        .satisfies(requestIdFromHeader -> assertThatNoException()
            .isThrownBy(() -> assertThat(UUID.fromString(requestIdFromHeader)).isNotNull()));
    verify(wifiAsyncJobPublisher).publish(
        argThat(requestId -> assertThat(requestId)
            .isNotEmpty()
            .isEqualTo(HttpHeaderContext.getHeader(HttpHeaderName.REQUEST_ID))),
        any(WifiAsyncJobContext.class),
        any(WifiAsyncJob.class)
    );
  }
}
