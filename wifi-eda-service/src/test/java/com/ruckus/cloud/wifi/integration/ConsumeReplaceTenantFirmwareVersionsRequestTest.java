package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.notification.service.gpb.DeviceNotification.NotificationType.FIRMWARE_UPGRADE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;

import com.ruckus.cloud.notification.service.gpb.DeviceNotification;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApFirmwareUpgradeRequestTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ScheduleTimeSlotTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantAvailableApFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueApModelSpecificAttributesTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.viewmodel.ReplaceTenantFirmwareVersionsRequest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
    FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
public class ConsumeReplaceTenantFirmwareVersionsRequestTest extends AbstractRequestTest {
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired
  private MessageCaptors messageCaptors;

  @Test
  public void testReplace(Tenant tenant,
      Venue venue,
      ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100") ApVersion ver700,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.50") ApVersion ver624,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.20") ApVersion ver620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99") ApVersion targetVer700,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.49") ApVersion targetVer624,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.19") ApVersion targetVer620) {
    this.updateApVersionSupportedApModelList(ver700, List.of("R770", "R550"));
    this.updateApVersionSupportedApModelList(targetVer700, List.of("R770", "R550"));
    this.updateApVersionSupportedApModelList(ver624, List.of("R550"));
    this.updateApVersionSupportedApModelList(targetVer624, List.of("R550"));
    this.updateApVersionSupportedApModelList(ver620, List.of("R550", "R500"));
    this.updateApVersionSupportedApModelList(targetVer620, List.of("R550", "R500"));
    this.createTenantAvailableApFirmwares(tenant, List.of(ver700, ver624, ver620));
    createVenueCurrentFirmware(venue, ver700, "R770");
    createVenueCurrentFirmware(venue, ver624, "R550");
    createVenueCurrentFirmware(venue, ver620, "R500");
    Ap r770Ap = createAp(apGroup, randomSerialNumber(), "R770", randomMacAddress());
    Ap r550Ap = createAp(apGroup, randomSerialNumber(), "R550", randomMacAddress());
    Ap r500Ap = createAp(apGroup, randomSerialNumber(), "R500", randomMacAddress());
    createApFirmwareUpgradeRequest(tenant, r770Ap, ver700);
    createApFirmwareUpgradeRequest(tenant, r550Ap, ver624);
    createApFirmwareUpgradeRequest(tenant, r500Ap, ver620);

    // when
    ReplaceTenantFirmwareVersionsRequest request = new ReplaceTenantFirmwareVersionsRequest();
    request.setTenantId(tenant.getId());
    request.setFirmwares(List.of(targetVer700.getId(), targetVer624.getId(), targetVer620.getId()));
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.REPLACE_TENANT_FIRMWARE_VERSIONS,
        randomName(),
        new RequestParams(),
        request);

    // then
    assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenant.getId()))
        .hasSize(3)
        .extracting(VenueCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R770", targetVer700.getId()),
            tuple("R550", targetVer700.getId()),
            tuple("R500", targetVer620.getId()));
    assertThat(repositoryUtil.findAll(ApFirmwareUpgradeRequest.class, tenant.getId()))
        .hasSize(3)
        .extracting(ApFirmwareUpgradeRequest::getModel, ApFirmwareUpgradeRequest::getTargetVersion)
        .containsExactlyInAnyOrder(
            tuple("R770", targetVer700.getId()),
            tuple("R550", targetVer700.getId()),
            tuple("R500", targetVer620.getId()));
    assertThat(messageCaptors.getDeviceNotificationMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(DeviceNotification::getNotificationType)
        .isEqualTo(FIRMWARE_UPGRADE);
  }

  @Test
  public void testReplace_whenExistsUpgradeSchedule_skipSchedule(Tenant tenant,
      Venue venue,
      ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100") ApVersion ver700,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.50") ApVersion ver624,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99") ApVersion targetVer700,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.49") ApVersion targetVer624) {
    this.updateApVersionSupportedApModelList(ver700, List.of("R770", "R550", "R880"));
    this.updateApVersionSupportedApModelList(targetVer700, List.of("R770", "R550"));
    this.updateApVersionSupportedApModelList(ver624, List.of("R550"));
    this.updateApVersionSupportedApModelList(targetVer624, List.of("R550"));
    this.createTenantAvailableApFirmwares(tenant, List.of(ver700, ver624));
    this.createVenueCurrentFirmware(venue, ver700, "R770");
    this.createVenueCurrentFirmware(venue, ver624, "R550");
    Ap r770Ap = createAp(apGroup, randomSerialNumber(), "R770", randomMacAddress());
    Ap r550Ap = createAp(apGroup, randomSerialNumber(), "R550", randomMacAddress());
    this.createApFirmwareUpgradeRequest(tenant, r770Ap, ver700);
    this.createApFirmwareUpgradeRequest(tenant, r550Ap, ver624);
    this.createUpgradeSchedule(venue, targetVer700);

    // when
    ReplaceTenantFirmwareVersionsRequest request = new ReplaceTenantFirmwareVersionsRequest();
    request.setTenantId(tenant.getId());
    request.setFirmwares(List.of(targetVer700.getId(), targetVer624.getId()));
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.REPLACE_TENANT_FIRMWARE_VERSIONS,
        randomName(),
        new RequestParams(),
        request);

    // then
    assertThat(repositoryUtil.findAll(UpgradeSchedule.class, tenant.getId())).isEmpty();
    assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenant.getId()))
        .hasSize(2)
        .extracting(VenueCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R770", targetVer700.getId()),
            tuple("R550", targetVer700.getId()));
    assertThat(repositoryUtil.findAll(ApFirmwareUpgradeRequest.class, tenant.getId()))
        .hasSize(2)
        .extracting(ApFirmwareUpgradeRequest::getModel, ApFirmwareUpgradeRequest::getTargetVersion)
        .containsExactlyInAnyOrder(
            tuple("R770", targetVer700.getId()),
            tuple("R550", targetVer700.getId()));
    assertThat(messageCaptors.getDeviceNotificationMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(DeviceNotification::getNotificationType)
        .isEqualTo(FIRMWARE_UPGRADE);
  }

  @Test
  public void testReplace_whenUnsupportedVenueModelSpecificAttrsExist_deleteAttrs(Tenant tenant,
      Venue venue,
      ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100") ApVersion ver700,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.50") ApVersion ver624,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99") ApVersion targetVer700,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.49") ApVersion targetVer624) {
    this.updateApVersionSupportedApModelList(ver700, List.of("R770", "R550", "R880", "R666"));
    this.updateApVersionSupportedApModelList(targetVer700, List.of("R770", "R550"));
    this.updateApVersionSupportedApModelList(ver624, List.of("R550"));
    this.updateApVersionSupportedApModelList(targetVer624, List.of("R550"));
    this.createVenueApModelSpecificAttributes(venue, List.of("R770", "R550", "R880", "R666"));
    this.createTenantAvailableApFirmwares(tenant, List.of(ver700, ver624));
    this.createVenueCurrentFirmware(venue, ver700, "R770");
    this.createVenueCurrentFirmware(venue, ver624, "R550");
    Ap r770Ap = createAp(apGroup, randomSerialNumber(), "R770", randomMacAddress());
    Ap r550Ap = createAp(apGroup, randomSerialNumber(), "R550", randomMacAddress());
    this.createApFirmwareUpgradeRequest(tenant, r770Ap, ver700);
    this.createApFirmwareUpgradeRequest(tenant, r550Ap, ver624);

    // when
    ReplaceTenantFirmwareVersionsRequest request = new ReplaceTenantFirmwareVersionsRequest();
    request.setTenantId(tenant.getId());
    request.setFirmwares(List.of(targetVer700.getId(), targetVer624.getId()));
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.REPLACE_TENANT_FIRMWARE_VERSIONS,
        randomName(),
        new RequestParams(),
        request);

    // then
    assertThat(repositoryUtil.findAll(VenueApModelSpecificAttributes.class, tenant.getId()))
        .hasSize(2)
        .extracting(VenueApModelSpecificAttributes::getModel)
        .containsExactlyInAnyOrder("R770", "R550");
    assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenant.getId()))
        .hasSize(2)
        .extracting(VenueCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R770", targetVer700.getId()),
            tuple("R550", targetVer700.getId()));
    assertThat(repositoryUtil.findAll(ApFirmwareUpgradeRequest.class, tenant.getId()))
        .hasSize(2)
        .extracting(ApFirmwareUpgradeRequest::getModel, ApFirmwareUpgradeRequest::getTargetVersion)
        .containsExactlyInAnyOrder(
            tuple("R770", targetVer700.getId()),
            tuple("R550", targetVer700.getId()));
    assertThat(messageCaptors.getDeviceNotificationMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(DeviceNotification::getNotificationType)
        .isEqualTo(FIRMWARE_UPGRADE);
  }

  public void createTenantAvailableApFirmwares(Tenant tenant, List<ApVersion> apVersions) {
    apVersions.forEach(apVersion -> {
      repositoryUtil.createOrUpdate(
          TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant, apVersion), tenant.getId(),
          txCtxExtension.newRequestId());
    });
  }

  public void updateApVersionSupportedApModelList(ApVersion version, List<String> models) {
    version.setSupportedApModels(models);
    repositoryUtil.createOrUpdate(version, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
  }

  public void createVenueCurrentFirmware(Venue venue, ApVersion version, String model) {
    repositoryUtil.createOrUpdate(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, version, model),
        txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
  }

  public void createApFirmwareUpgradeRequest(Tenant tenant, Ap ap, ApVersion version) {
    repositoryUtil.createOrUpdate(ApFirmwareUpgradeRequestTestFixture.randomApFirmwareUpgradeRequest(ap, version),
        tenant.getId(), txCtxExtension.newRequestId());
  }

  private UpgradeSchedule createUpgradeSchedule(Venue venue, ApVersion apVersion) {
    ScheduleTimeSlot sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
    repositoryUtil.createOrUpdate(sts, venue.getTenant().getId(), randomTxId());
    UpgradeSchedule schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, apVersion, sts,
        c -> {});

    return repositoryUtil.createOrUpdate(schedule, venue.getTenant().getId(), randomTxId());
  }

  public void createVenueApModelSpecificAttributes(Venue venue, List<String> models) {
    models.forEach(model -> {
      repositoryUtil.createOrUpdate(
          VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(venue, v -> v.setModel(model)),
          venue.getTenant().getId(), txCtxExtension.newRequestId());
    });
  }

}
