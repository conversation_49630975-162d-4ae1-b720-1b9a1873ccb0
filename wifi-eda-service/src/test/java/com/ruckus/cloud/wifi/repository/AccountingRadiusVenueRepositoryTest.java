package com.ruckus.cloud.wifi.repository;


import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.specification.RadiusVenueSpecification;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('a5c9f2e00d364a86a22d764fdaea8b26');
    INSERT INTO venue (id, name, tenant) VALUES
      ('f81cd13b4c0f49448e3ac454d0f48ba1','venue-1', 'a5c9f2e00d364a86a22d764fdaea8b26');
    INSERT INTO network (id, name, type, tenant) VALUES (
      '3c70a2fecc8a438eaf4a4773e4b68b69','network','DPSK', 'a5c9f2e00d364a86a22d764fdaea8b26');
    INSERT INTO radius (id, tenant) VALUES 
    ('6b6d5cbdcc61480e87724e0e1b3f9cd2', 'a5c9f2e00d364a86a22d764fdaea8b26');
    INSERT INTO accounting_radius_venue (id, venue, radius, tenant) VALUES 
    ('0b137eeb612d48d08c79f9c1970f7a47', 'f81cd13b4c0f49448e3ac454d0f48ba1', '6b6d5cbdcc61480e87724e0e1b3f9cd2', 'a5c9f2e00d364a86a22d764fdaea8b26');
        
    INSERT INTO tenant (id) VALUES ('f920ff55be9d4e3b888d54a3dbb7f605');
    INSERT INTO venue (id, name, tenant) VALUES
        ('a95d6c1c1d4e49d8bcab0870e198e7a1','venue-2', 'f920ff55be9d4e3b888d54a3dbb7f605');
    INSERT INTO network (id, name, type, tenant) VALUES (
        '143e37b0e33549c087cace573525c2dc','network-2','PSK', 'f920ff55be9d4e3b888d54a3dbb7f605');
    INSERT INTO radius (id, tenant) VALUES
        ('3d7fc40c89c04fa3b3dd0f75ee4eb7d1', 'f920ff55be9d4e3b888d54a3dbb7f605');
    INSERT INTO accounting_radius_venue (id, venue, radius, tenant) VALUES
        ('70c574c739684b13ab8db4c3deeb5e36', 'a95d6c1c1d4e49d8bcab0870e198e7a1', '3d7fc40c89c04fa3b3dd0f75ee4eb7d1', 'f920ff55be9d4e3b888d54a3dbb7f605');
          
    INSERT INTO venue (id, name, tenant) VALUES
        ('f4a7b75057c94788939b9a1df880bb85','venue-3', 'f920ff55be9d4e3b888d54a3dbb7f605');
    INSERT INTO network (id, name, type, tenant) VALUES (
        '48d4688cb5d74180aa1c7d30af8939b7','network-3','WPA', 'f920ff55be9d4e3b888d54a3dbb7f605');
    INSERT INTO radius (id, tenant) VALUES
        ('0d3a724e35ee4985a5b3d9dd8a7a947c', 'f920ff55be9d4e3b888d54a3dbb7f605');
    INSERT INTO accounting_radius_venue (id, venue, radius, tenant) VALUES
        ('5f9c78d1c4fb4cbfb2f0081ccfe4c4a0', 'f4a7b75057c94788939b9a1df880bb85', '0d3a724e35ee4985a5b3d9dd8a7a947c', 'f920ff55be9d4e3b888d54a3dbb7f605');
    """)
@WifiJpaDataTest
public class AccountingRadiusVenueRepositoryTest {

  @Autowired
  private AccountingRadiusVenueRepository unit;
  @Test
  void queryAuthRadiusVenues() {
    assertThat(unit.findAll(RadiusVenueSpecification.findAccountingRadiusVenueByVenueIdAndRadiusId(
        List.of(toAccountingRadiusVenue("f81cd13b4c0f49448e3ac454d0f48ba1",
            "6b6d5cbdcc61480e87724e0e1b3f9cd2"))
        , "a5c9f2e00d364a86a22d764fdaea8b26")))
        .hasSize(1)
        .extracting("id")
        .contains("0b137eeb612d48d08c79f9c1970f7a47");

    assertThat(unit.findAll(RadiusVenueSpecification.findAccountingRadiusVenueByVenueIdAndRadiusId(
        List.of(toAccountingRadiusVenue("a95d6c1c1d4e49d8bcab0870e198e7a1",
                "3d7fc40c89c04fa3b3dd0f75ee4eb7d1"),
            toAccountingRadiusVenue("f4a7b75057c94788939b9a1df880bb85",
                "0d3a724e35ee4985a5b3d9dd8a7a947c"))
        , "f920ff55be9d4e3b888d54a3dbb7f605")))
        .hasSize(2)
        .extracting("id")
        .contains("70c574c739684b13ab8db4c3deeb5e36",
            "5f9c78d1c4fb4cbfb2f0081ccfe4c4a0");
  }

  private AccountingRadiusVenue toAccountingRadiusVenue(String venueId, String radiusId) {
    var authRadiusVenue = new AccountingRadiusVenue();
    authRadiusVenue.setVenue(new Venue(venueId));
    authRadiusVenue.setRadius(new Radius(radiusId));
    return authRadiusVenue;
  }
}
