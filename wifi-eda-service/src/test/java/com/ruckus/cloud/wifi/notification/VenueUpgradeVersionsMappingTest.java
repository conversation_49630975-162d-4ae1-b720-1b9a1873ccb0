package com.ruckus.cloud.wifi.notification;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

@WifiUnitTest
class VenueUpgradeVersionsMappingTest {

  @Test
  public void testGetUnionFromSameApVersions() {
    ApVersion apVersion = TenantFirmwareNotificationUtilTest.mockApVersion("6.2.0.103.100");
    ApVersion apVersion2 = TenantFirmwareNotificationUtilTest.mockApVersion("6.2.0.103.100");
    Map<String, List<ApVersion>> mapping = new HashMap<>();
    mapping.put("venue-id", List.of(apVersion, apVersion2));

    VenueUpgradeVersionsMapping versionsMapping = VenueUpgradeVersionsMapping.create(mapping);

    Assertions.assertThat(versionsMapping.getUnionVersions())
        .hasSize(1);
  }

  @Test
  public void testGetUnionFromDifferentApVersions() {
    ApVersion apVersion = TenantFirmwareNotificationUtilTest.mockApVersion("6.2.0.103.100");
    ApVersion apVersion2 = TenantFirmwareNotificationUtilTest.mockApVersion("6.2.0.103.200");
    Map<String, List<ApVersion>> mapping = new HashMap<>();
    mapping.put("venue-id", List.of(apVersion, apVersion2));

    VenueUpgradeVersionsMapping versionsMapping = VenueUpgradeVersionsMapping.create(mapping);

    Assertions.assertThat(versionsMapping.getUnionVersions())
        .hasSize(2);
  }
}