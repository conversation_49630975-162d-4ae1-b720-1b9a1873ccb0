package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class StickyClientSteeringFeatureTest {

  @SpyBean
  private StickyClientSteeringFeature unit;

  @Nested
  class WhenIsLbsEnabled {

    @Test
    void givenVenueLoadBalancingNull(Venue venue) {
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    void givenVenueLoadBalancingDisabled(Venue venue) {
      var venueLoadBalancing = Generators.venueLoadBalancing().generate();
      venueLoadBalancing.setEnabled(false);
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    void givenStickyClientSteeringDisabled(Venue venue) {
      var venueLoadBalancing = Generators.venueLoadBalancing().generate();
      venueLoadBalancing.setEnabled(true);
      venueLoadBalancing.setStickyClientSteeringEnabled(false);
      venue.setLoadBalancing(venueLoadBalancing);
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    void givenStickyClientSteeringEnabled(Venue venue) {
      var venueLoadBalancing = Generators.venueLoadBalancing().generate();
      venueLoadBalancing.setEnabled(true);
      venueLoadBalancing.setStickyClientSteeringEnabled(true);
      venue.setLoadBalancing(venueLoadBalancing);
      BDDAssertions.then(unit.test(venue)).isTrue();
    }

  }
}
