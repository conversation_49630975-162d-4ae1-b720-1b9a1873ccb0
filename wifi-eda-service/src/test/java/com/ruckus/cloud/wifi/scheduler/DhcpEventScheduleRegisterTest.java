package com.ruckus.cloud.wifi.scheduler;

import com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class DhcpEventScheduleRegisterTest {

  @Test
  void testKairosRequestPayload() {
    var unit = new DhcpEventScheduleRegister(null, null);
    var payload = unit.kairosRequestPayload();
    assertThat(payload)
        .isNotNull()
        .matches(p -> DhcpEventScheduleRegister.ROUTINE_JOB_SCHEDULE_TIME.equals(p.getScheduleTime()))
        .matches(p -> unit.getJobName().equals(p.getTenantId()))
        .extracting(RegisterKairosRequest::getKafkaTarget)
        .isNotNull();
  }

}
