package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector;

import static com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanStep.SYNC_DATA_TO_VIEWMODEL_STEP;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.AbstractCmnCfgCollectorOperationBuilder;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.publisher.ViewmodelConfigPublisher;
import com.ruckus.cloud.wifi.kafka.publisher.activity.ActivityCfgChangeRespPublisher;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntityFactory;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

public class ViewmodelAggregatedEntityListenerTest {

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  public void testFilter() {
    AbstractCmnCfgCollectorOperationBuilder apBuilder = mock(
        AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder venueBuilder = mock(
        AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder networkBuilder = mock(
      AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder networkVenueBuilder = mock(
      AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder apSnmpAgentProfileBuilder = mock(
      AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder networkApGroupBuilder = mock(
        AbstractCmnCfgCollectorOperationBuilder.class);
    doReturn(Ap.class).when(apBuilder).entityClass();
    doReturn(Venue.class).when(venueBuilder).entityClass();
    doReturn(OpenNetwork.class).when(networkBuilder).entityClass();
    doReturn(NetworkVenue.class).when(networkVenueBuilder).entityClass();
    doReturn(ApSnmpAgentProfile.class).when(apSnmpAgentProfileBuilder).entityClass();
    doReturn(NetworkApGroup.class).when(networkApGroupBuilder).entityClass();

    ViewmodelAggregatedEntityListener listener = new ViewmodelAggregatedEntityListener(
        null, null, null,
        List.of(apBuilder, venueBuilder, networkBuilder, networkVenueBuilder,
            apSnmpAgentProfileBuilder, networkApGroupBuilder));

    assertThat(listener.filter().test(new Ap())).isTrue();
    assertThat(listener.filter().test(new Venue())).isTrue();
    assertThat(listener.filter().test(new Tenant())).isFalse();
    assertThat(listener.filter().test(new OpenNetwork())).isTrue();
    assertThat(listener.filter().test(new NetworkVenue())).isTrue();
    assertThat(listener.filter().test(new ApSnmpAgentProfile())).isTrue();
    assertThat(listener.filter().test(new NetworkApGroup())).isTrue();
  }

  @Test
  public void testBuild() {
    AbstractCmnCfgCollectorOperationBuilder apBuilder = mock(
        AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder networkBuilder = mock(
      AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder apSnmpAgentProfileBuilder = mock(
      AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder networkApGroupBuilder = mock(
        AbstractCmnCfgCollectorOperationBuilder.class);

    doReturn(Ap.class).when(apBuilder).entityClass();
    doReturn(OpenNetwork.class).when(networkBuilder).entityClass();
    doReturn(ApSnmpAgentProfile.class).when(apSnmpAgentProfileBuilder).entityClass();
    doReturn(NetworkApGroup.class).when(networkApGroupBuilder).entityClass();

    ViewmodelAggregatedEntityListener listener = new ViewmodelAggregatedEntityListener(
        null, null, null,
        List.of(apBuilder, networkBuilder, apSnmpAgentProfileBuilder, networkApGroupBuilder));

    Ap ap = new Ap();
    OpenNetwork network = new OpenNetwork();
    listener.build(TxEntityFactory.createTxEntity(ap, null, EntityAction.ADD), emptyTxChanges());
    listener.build(TxEntityFactory.createTxEntity(network, null, EntityAction.ADD), emptyTxChanges());
    listener.build(TxEntityFactory.createTxEntity(new ApSnmpAgentProfile(), null, EntityAction.ADD),
        emptyTxChanges());
    listener.build(TxEntityFactory.createTxEntity(new NetworkApGroup(), null, EntityAction.ADD),
        emptyTxChanges());

    verify(apBuilder, times(1)).build(any(NewTxEntity.class), any(TxChangesReader.class));
    verify(networkBuilder, times(1)).build(any(NewTxEntity.class), any(TxChangesReader.class));
    verify(apSnmpAgentProfileBuilder, times(1)).build(any(NewTxEntity.class), any(TxChangesReader.class));
    verify(networkApGroupBuilder, times(1)).build(any(NewTxEntity.class), any(TxChangesReader.class));
  }

  @Test
  void testBulkBuild() {
    AbstractCmnCfgCollectorOperationBuilder apBuilder = mock(AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder networkBuilder = mock(AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder networkApGroupBuilder = mock(AbstractCmnCfgCollectorOperationBuilder.class);
    FeatureFlagService featureFlagService = mock(FeatureFlagService.class);

    doReturn(Ap.class).when(apBuilder).entityClass();
    doReturn(OpenNetwork.class).when(networkBuilder).entityClass();
    doReturn(NetworkApGroup.class).when(networkApGroupBuilder).entityClass();

    ViewmodelAggregatedEntityListener listener = new ViewmodelAggregatedEntityListener(
        null, null,
        featureFlagService,
        List.of(apBuilder, networkBuilder, networkApGroupBuilder));
    List<TxEntity<?>> newTxEntities = List.of(
        TxEntityFactory.createTxEntity(new Ap(), null, EntityAction.ADD),
        TxEntityFactory.createTxEntity(new OpenNetwork(), null, EntityAction.ADD),
        TxEntityFactory.createTxEntity(new NetworkApGroup(), null, EntityAction.ADD),
        TxEntityFactory.createTxEntity(new NetworkApGroup(), null, EntityAction.ADD)
    );

    listener.bulkBuild(newTxEntities, List.of(), List.of(), emptyTxChanges());

    verify(apBuilder, times(1)).build(any(NewTxEntity.class), any(TxChangesReader.class));
    verify(networkBuilder, times(1)).build(any(NewTxEntity.class), any(TxChangesReader.class));
    verify(networkApGroupBuilder, times(2)).build(any(NewTxEntity.class), any(TxChangesReader.class));
    verify(apBuilder, never()).preload(any(), any(TxChangesReader.class));
    verify(networkApGroupBuilder, never()).preload(any(), any(TxChangesReader.class));
    verify(apBuilder, never()).clearPreload(any(), any(TxChangesReader.class));
    verify(networkApGroupBuilder, never()).clearPreload(any(), any(TxChangesReader.class));
  }

  @Test
  void testBulkBuildWithPreload() {
    AbstractCmnCfgCollectorOperationBuilder apBuilder = mock(AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder networkApGroupBuilder = mock(AbstractCmnCfgCollectorOperationBuilder.class);
    FeatureFlagService featureFlagService = mock(FeatureFlagService.class);

    doReturn(Ap.class).when(apBuilder).entityClass();
    doReturn(NetworkApGroup.class).when(networkApGroupBuilder).entityClass();
    doReturn(true).when(featureFlagService).isFeatureEnable(anyString(), anyString());

    ViewmodelAggregatedEntityListener listener = new ViewmodelAggregatedEntityListener(
        null, null,
        featureFlagService,
        List.of(apBuilder, networkApGroupBuilder));
    List<TxEntity<?>> newTxEntities = List.of(
        TxEntityFactory.createTxEntity(new Ap(), null, EntityAction.ADD),
        TxEntityFactory.createTxEntity(new NetworkApGroup(), null, EntityAction.ADD),
        TxEntityFactory.createTxEntity(new NetworkApGroup(), null, EntityAction.ADD)
    );

    listener.bulkBuild(newTxEntities, List.of(), List.of(), emptyTxChanges());

    verify(apBuilder, times(1)).build(any(NewTxEntity.class), any(TxChangesReader.class));
    verify(networkApGroupBuilder, times(2)).build(any(NewTxEntity.class), any(TxChangesReader.class));
    verify(apBuilder, times(1)).preload(any(), any(TxChangesReader.class));
    verify(networkApGroupBuilder, times(1)).preload(any(), any(TxChangesReader.class));
    verify(apBuilder, times(1)).clearPreload(any(), any(TxChangesReader.class));
    verify(networkApGroupBuilder, times(1)).clearPreload(any(), any(TxChangesReader.class));
  }

  @Test
  void testPostBuild() {
    AbstractCmnCfgCollectorOperationBuilder apBuilder = mock(
        AbstractCmnCfgCollectorOperationBuilder.class);
    AbstractCmnCfgCollectorOperationBuilder networkBuilder = mock(
        AbstractCmnCfgCollectorOperationBuilder.class);

    doReturn(Ap.class).when(apBuilder).entityClass();
    doReturn(OpenNetwork.class).when(networkBuilder).entityClass();

    ViewmodelAggregatedEntityListener listener = new ViewmodelAggregatedEntityListener(
        null, null, null,
        List.of(apBuilder, networkBuilder));

    Ap ap = new Ap();
    listener.postBuild(TxEntityFactory.createTxEntity(ap, null, EntityAction.ADD),
        Collections.emptyList(), emptyTxChanges());

    verify(apBuilder, times(1)).postBuild(any(NewTxEntity.class),
        any(TxChangesReader.class), anyList());
    verify(networkBuilder, never()).postBuild(any(NewTxEntity.class), any(TxChangesReader.class),
        anyList());
  }

  @Nested
  class testFlushGivenNoOperation {

    @Test
    public void thenPublishNothing_NotInWifiCfgRequestFlow() {
      ViewmodelConfigPublisher viewmodelConfigPublisher = mock(ViewmodelConfigPublisher.class);
      ActivityCfgChangeRespPublisher activityCfgChangeRespPublisher =
          mock(ActivityCfgChangeRespPublisher.class);
      ViewmodelAggregatedEntityListener listener = new ViewmodelAggregatedEntityListener(
          viewmodelConfigPublisher, activityCfgChangeRespPublisher, null,
          Collections.emptyList());

      listener.flush(Collections.emptyList());

      verify(viewmodelConfigPublisher, never()).publish(any(ViewmodelCollector.class));
      verify(activityCfgChangeRespPublisher, never())
          .publishCfgStatusSuccess(any());
    }
  }

  @Test
  public void thenPublishSyncDataToViewmodelStep_InWifiCfgRequestFlow() {
    ViewmodelConfigPublisher viewmodelConfigPublisher = mock(ViewmodelConfigPublisher.class);
    ActivityCfgChangeRespPublisher activityCfgChangeRespPublisher =
        mock(ActivityCfgChangeRespPublisher.class);
    ViewmodelAggregatedEntityListener listener = new ViewmodelAggregatedEntityListener(
        viewmodelConfigPublisher, activityCfgChangeRespPublisher, null,
        Collections.emptyList());

    final var ctx = TxCtxHolder.get();
    ctx.setFlowName(ConfigRequestHandler.apiActionToFlowName("test"));

    listener.flush(Collections.emptyList());

    verify(viewmodelConfigPublisher, never()).publish(any(ViewmodelCollector.class));
    verify(activityCfgChangeRespPublisher, times(1))
        .publishCfgStatusSuccess(eq(SYNC_DATA_TO_VIEWMODEL_STEP));
  }

  @Nested
  class testFlushGivenOperation {

    @Test
    public void thenPublishWithOutSyncDataToViewmodel_NotInWifiCfgRequestFlow() {
      ViewmodelConfigPublisher viewmodelConfigPublisher = mock(ViewmodelConfigPublisher.class);
      ActivityCfgChangeRespPublisher activityCfgChangeRespPublisher =
          mock(ActivityCfgChangeRespPublisher.class);
      ViewmodelAggregatedEntityListener listener = new ViewmodelAggregatedEntityListener(
          viewmodelConfigPublisher, activityCfgChangeRespPublisher, null,
          Collections.emptyList());

      listener.flush(List.of(mock(Operations.class)));

      verify(viewmodelConfigPublisher, times(1))
          .publish(any(ViewmodelCollector.class), eq(Optional.empty()));
    }

    @Test
    public void thenPublishWithSyncDataToViewmodel_InWifiCfgRequestFlow() {
      ViewmodelConfigPublisher viewmodelConfigPublisher = mock(ViewmodelConfigPublisher.class);
      ActivityCfgChangeRespPublisher activityCfgChangeRespPublisher =
          mock(ActivityCfgChangeRespPublisher.class);
      ViewmodelAggregatedEntityListener listener = new ViewmodelAggregatedEntityListener(
          viewmodelConfigPublisher, activityCfgChangeRespPublisher, null,
          Collections.emptyList());

      final var ctx = TxCtxHolder.get();
      ctx.setFlowName(ConfigRequestHandler.apiActionToFlowName("test"));

      listener.flush(List.of(mock(Operations.class)));

      verify(viewmodelConfigPublisher, times(1))
          .publish(any(ViewmodelCollector.class), eq(Optional.of(SYNC_DATA_TO_VIEWMODEL_STEP)));
    }
  }
}