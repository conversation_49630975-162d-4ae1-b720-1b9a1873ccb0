package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO dhcp_config_service_profile (id, service_name, tenant)
        VALUES ('dhcpConfig1', 'dhcpConfig1', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO dhcp_config_service_profile (id, service_name, tenant)
        VALUES ('dhcpConfig2', 'dhcpConfig2', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO dhcp_service_profile (id, name, tenant, dhcp_config_service_profile)
        VALUES ('pool_11', 'pool1', '4c8279f79307415fa9e4c88a1819f0fc', 'dhcpConfig1');
    INSERT INTO dhcp_service_profile (id, name, tenant, dhcp_config_service_profile)
        VALUES ('pool_12', 'pool2', '4c8279f79307415fa9e4c88a1819f0fc', 'dhcpConfig1');
    INSERT INTO dhcp_service_profile (id, name, tenant, dhcp_config_service_profile)
        VALUES ('pool_21', 'pool1', '4c8279f79307415fa9e4c88a1819f0fc', 'dhcpConfig2');
    INSERT INTO dhcp_service_profile (id, name, tenant, dhcp_config_service_profile)
        VALUES ('pool_22', 'pool2', '4c8279f79307415fa9e4c88a1819f0fc', 'dhcpConfig2');
    """)
public class DhcpServiceProfileRepositoryTest {

  @Autowired
  private DhcpServiceProfileRepository target;

  @Test
  void findByTenantIdAndDhcpConfigServiceProfileIdAndNameIn() {
    {
      List<DhcpServiceProfile> results =
          target.findByTenantId("4c8279f79307415fa9e4c88a1819f0fc");
      assertEquals(4, results.size());
    }
    {
      List<DhcpServiceProfile> results =
          target.findByTenantIdAndDhcpConfigServiceProfileIdAndNameIn(
              "4c8279f79307415fa9e4c88a1819f0fc", "dhcpConfig1", List.of("pool1"));
      assertEquals(1, results.size());
    }
    {
      List<DhcpServiceProfile> results =
          target.findByTenantIdAndDhcpConfigServiceProfileIdAndNameIn(
              "4c8279f79307415fa9e4c88a1819f0fc", "dhcpConfig1", List.of("pool1", "pool2"));
      assertEquals(2, results.size());
    }
  }
}
