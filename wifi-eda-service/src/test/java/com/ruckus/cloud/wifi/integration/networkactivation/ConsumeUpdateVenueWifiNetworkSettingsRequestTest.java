package com.ruckus.cloud.wifi.integration.networkactivation;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeShort;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertNetworkVenueSoftly;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RadioType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanApGroup;
import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DpskWifiNetworkGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.OpenWifiNetworkGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.repository.DpskNetworkRepository;
import com.ruckus.cloud.wifi.repository.NetworkApGroupRadioRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.OpenNetworkRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("NetworkActivationTest")
@WifiIntegrationTest
class ConsumeUpdateVenueWifiNetworkSettingsRequestTest {

  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private DpskNetworkRepository dpskNetworkRepository;
  @Autowired
  private NetworkVenueRepository networkVenueRepository;
  @Autowired
  private OpenNetworkRepository openNetworkRepository;
  @Autowired
  private NetworkApGroupRadioRepository networkApGroupRadioRepository;

  private static com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler dummyNetworkVenueScheduler() {
    final var scheduler = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler();
    scheduler.setType(SchedulerTypeEnum.ALWAYS_ON);
    return scheduler;
  }

  private void sendActivateWifiNetworkOnVenue(String tenantId, String venueId, String networkId) {
    WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
        .tenantId(tenantId)
        .requestId(randomTxId())
        .addHeader(RKS_IDM_USER_ID.getName(), randomName())
        .apiAction(CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE)
        .requestParams(new RequestParams().addPathVariable("venueId", venueId)
            .addPathVariable("wifiNetworkId", networkId)).build();

    messageUtil.sendWifiCfgRequest(wifiCfgRequest);
  }

  private void sendUpdateVenueWifiNetworkSettings(String tenantId, String requestId, String venueId,
      String networkId,
      com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkSettings venueWifiNetworkSettings) {
    WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
        .tenantId(tenantId)
        .requestId(requestId)
        .addHeader(RKS_IDM_USER_ID.getName(), randomName())
        .apiAction(CfgAction.UPDATE_VENUE_WIFI_NETWORK_SETTINGS)
        .requestParams(new RequestParams().addPathVariable("venueId", venueId)
            .addPathVariable("wifiNetworkId", networkId))
        .payload(venueWifiNetworkSettings).build();

    messageUtil.sendWifiCfgRequest(wifiCfgRequest);
  }

  void validateResult(String tenantId, String requestId, String venueId, String networkId,
      String networkVenueId,
      com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkSettings payload) {
    final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);

    assertThat(networkVenue)
        .isNotNull()
        .matches(nv -> Objects.equals(nv.getNetwork().getId(), networkId))
        .matches(nv -> Objects.equals(nv.getVenue().getId(), venueId));

    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(wifiCfgChangeMessage).isNotNull()
        .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.API_ACTION,
            CfgAction.UPDATE_VENUE_WIFI_NETWORK_SETTINGS.key()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(wifiCfgChangeMessage.getPayload())
            .satisfies(msg -> assertSoftly(softly -> {
              softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
              softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
            }))
            .extracting(WifiConfigChange::getOperationList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
            .satisfies(ops -> assertThat(ops)
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .filteredOn(
                    op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY)
                .as("The MODIFY NetworkVenue operation count should be 1")
                .hasSize(1)
                .singleElement()
                .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                .satisfies(assertNetworkVenueSoftly(networkId, venueId))));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().allMatch(ops -> !CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME.equals(ops.getIndex()))
        .hasSize(3)
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(1)
              .singleElement()
              .matches(op -> op.getOpType() == OpType.MOD)
              .matches(op -> networkVenueId.equals(op.getId()))
              .extracting(Operations::getDocMap)
              .matches(doc -> networkVenueId.equals(doc.get(Key.ID).getStringValue()))
              .matches(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
              .matches(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
              .matches(doc -> networkId.equals(doc.get(Key.NETWORK_ID).getStringValue()))
              .matches(doc -> venueId.equals(doc.get(Key.VENUE_ID).getStringValue()));
          assertThat(ops)
              .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
              .isNotEmpty().hasSize(1)
              .allSatisfy(op -> {
                assertThat(op.getId()).isNotEmpty();
                assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                assertThat(op.getDocMap())
                    .containsEntry(Key.TENANT_ID,
                        ValueUtils.stringValue(tenantId));
                assertThat(op.getDocMap().get(Key.VENUE_APGROUPS))
                    .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
                    .extracting(
                        ListValue::getValuesList,
                        InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
                    .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
                    .allSatisfy(vap -> {
                      assertEquals(venueId, vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue());
                      assertTrue(vap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
                      assertEquals(1,
                          vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesCount());
                    });
              });
        });

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(ApiFlowNames.UPDATE_VENUE_WIFI_NETWORK_SETTINGS))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(activityImpactedMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> msg.getDeviceIdsList().isEmpty())
            .matches(msg -> msg.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID))
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
            .isEmpty());

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operation.class::cast)
            .allSatisfy(op -> assertThat(op)
                .extracting(Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                    String.format("The value of `requestId` field in CommonInfo should be [%s]",
                        requestId))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                    String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                        tenantId))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                    String.format("The value of `sender` field in CommonInfo should be [%s]",
                        ServiceType.WIFI_SERVICE)))
            .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 1,
                "The count of VenueSchedule operations should be 1")
            .satisfies(op -> assertSoftly(softly -> {
              softly.assertThat(payload).isNotNull();
              softly.assertThat(op)
                  .filteredOn(Operation::hasWlanVenue)
                  .hasSize(1)
                  .singleElement()
                  .matches(wlanVenueOp -> wlanVenueOp.getAction() == Action.MODIFY,
                      String.format(
                          "The value of `action` field in WlanVenue operation should be %s",
                          Action.MODIFY))
                  .matches(wlanVenueOp -> networkVenueId.equals(wlanVenueOp.getId()),
                      String.format("The value of `id` field in WlanVenue operation should be %s",
                          networkVenueId))
                  .extracting(Operation::getWlanVenue)
                  .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                    alsoSoftly.assertThat(wlanVenue.getWlanId())
                        .isEqualTo(networkId);
                    alsoSoftly.assertThat(wlanVenue.getVenueId())
                        .isEqualTo(venueId);
                  }));
            })));
  }

  @Nested
  class GivenOpenNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String networkVenueId;
    private String clientIsolationProfileId;

    @BeforeEach
    void givenOpenNetworkAndRelatedEntitiesPersistedInDb(@OpenNetwork Network network, Venue venue,
        @DefaultApGroup ApGroup ignoredApGroup,
        @ScheduledNetworkVenue NetworkVenue networkVenue, NetworkApGroup ignoredNetworkApGroup,
        NetworkApGroupRadio ignoredNetworkApGroupRadio) {
      networkId = network.getId();
      venueId = venue.getId();
      networkVenueId = networkVenue.getId();
      var tenantId = venue.getTenant().getId();
      network.getWlan().getAdvancedCustomization().setClientIsolation(true);
      repositoryUtil.createOrUpdate(network, tenantId, randomTxId());
      var clientIsolationAllowlist = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.clientIsolationAllowlist()
          .generate();
      clientIsolationAllowlist.getAllowlist()
          .forEach(entry -> entry.setClientIsolationAllowlist(clientIsolationAllowlist));
      networkVenue.setClientIsolationAllowlist(
          repositoryUtil.createOrUpdate(clientIsolationAllowlist, tenantId, randomTxId()));
      clientIsolationProfileId = networkVenue.getClientIsolationAllowlist().getId();
      repositoryUtil.createOrUpdate(networkVenue, tenantId, randomTxId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkSettings payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkSettings();
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz));
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(CfgAction.UPDATE_VENUE_WIFI_NETWORK_SETTINGS)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkSettings payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), venueId, networkId, networkVenueId,
          payload);

      var requestId = randomTxId();
      setIsAllApGroupsToFalse(txCtx.getTenant(), requestId);
      verifyApGroupCmnCfgCollectorRequestSent(txCtx.getTenant(), requestId);
      var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
      assertNotNull(networkVenue);
      var clientIsolationAllowlist = networkVenue.getClientIsolationAllowlist();
      assertNotNull(clientIsolationAllowlist);
      assertEquals(clientIsolationProfileId, clientIsolationAllowlist.getId());
    }

    private void setIsAllApGroupsToFalse(String tenantId, String requestId) {
      final var networkVenueMod = new com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkSettings();
      networkVenueMod.setIsAllApGroups(false);
      networkVenueMod.setAllApGroupsVlanId(
          rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      networkVenueMod.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz));

      sendUpdateVenueWifiNetworkSettings(tenantId, requestId, venueId, networkId,
          networkVenueMod);
    }

    private void verifyApGroupCmnCfgCollectorRequestSent(String tenantId, String requestId) {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty()
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK_DEVICE_GROUP_MAPPING.equals(op.getIndex()))
                .allMatch(op -> op.getOpType() == OpType.DEL)
                .isNotEmpty();

            assertThat(ops)
                .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
                .hasSize(1)
                .allSatisfy(op -> assertThat(op.getDocMap().get(Key.WIFI_NETWORK_IDS))
                    .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
                    .extracting(ListValue::getValuesList)
                    .asList()
                    .isEmpty());
          });
    }
  }

  @Nested
  class GivenDsaeNetworkPersistedInDb {

    @Payload("addDsaeNetwork")
    private DpskWifiNetworkGenerator addDsaeNetwork() {
      return Generators.dpskWifiNetwork()
          .setName(serialName("AddDsaeNetwork"))
          .setDescription(randomString(64))
          .setWlan(Generators.dpskWpa23MixedModeWifiWlan());
    }

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_NETWORK, payload = @Payload("addDsaeNetwork"))
    @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
    @FeatureRole("BETA-DPSK3")
    void thenShouldHandleUpdateDsaeNetworkVenueSuccessfully(TxCtx txCtx,
        @Payload("addDsaeNetwork") com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork payload,
        Venue venue) {

      var dsaeNetwork = (DpskNetwork) dpskNetworkRepository.findByIdAndTenantId(payload.getId(),
          txCtx.getTenant()).orElse(null);
      assertNotNull(dsaeNetwork);
      dsaeNetwork.setDpskServiceProfileId(UUID.randomUUID().toString());
      dpskNetworkRepository.save(dsaeNetwork);

      final var dsaeServiceNetworkVenue = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
      dsaeServiceNetworkVenue.setId(randomId()); // autoGenerated is true in wifi-api
      dsaeServiceNetworkVenue.setNetworkId(dsaeNetwork.getId());
      dsaeServiceNetworkVenue.setVenueId(venue.getId());
      dsaeServiceNetworkVenue.setIsAllApGroups(true);
      dsaeServiceNetworkVenue.setAllApGroupsVlanId(
          rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dsaeServiceNetworkVenue.setAllApGroupsRadioTypes(
          List.of(RadioTypeEnum.values())); // All radio types
      sendActivateWifiNetworkOnVenue(txCtx.getTenant(), venue.getId(), dsaeNetwork.getId());

      final var dsaeServiceNetworkVenueMod = new com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkSettings();
      dsaeServiceNetworkVenueMod.setIsAllApGroups(true);
      dsaeServiceNetworkVenueMod.setAllApGroupsVlanId(
          rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dsaeServiceNetworkVenueMod.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._5_GHz));
      var requestId = randomTxId();
      sendUpdateVenueWifiNetworkSettings(txCtx.getTenant(), requestId, venue.getId(),
          dsaeNetwork.getId(), dsaeServiceNetworkVenueMod);

      validateDsaeResult(txCtx.getTenant(), requestId, venue.getId(), dsaeNetwork.getId(),
          dsaeServiceNetworkVenueMod);
    }

    void validateDsaeResult(String tenantId, String requestId, String venueId, String networkId,
        com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkSettings payload) {
      final var networkVenue = networkVenueRepository
          .findByTenantIdAndNetworkIdAndVenueId(tenantId, networkId, venueId).orElse(null);
      assertNotNull(networkVenue);
      var networkVenueId = networkVenue.getId();
      assertThat(networkVenue)
          .isNotNull()
          .matches(nv -> Objects.equals(networkId, nv.getNetwork().getId()))
          .matches(nv -> Objects.equals(venueId, nv.getVenue().getId()));

      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(
              assertHeader(WifiCommonHeader.API_ACTION,
                  CfgAction.UPDATE_VENUE_WIFI_NETWORK_SETTINGS.key()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY)
                  .as("The MODIFY NetworkVenue operation count should be 2")
                  .hasSize(2)
                  .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                  .anySatisfy(
                      assertNetworkVenueSoftly(networkId, venueId))));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(4)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                .hasSize(2)
                .allMatch(op -> op.getOpType() == OpType.MOD)
                .extracting(Operations::getDocMap)
                .allMatch(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
                .allMatch(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                .allMatch(doc -> venueId.equals(doc.get(Key.VENUE_ID).getStringValue()))
                .anyMatch(doc -> networkVenueId.equals(doc.get(Key.ID).getStringValue()))
                .anyMatch(
                    doc -> networkId.equals(doc.get(Key.NETWORK_ID).getStringValue()));
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
                .isNotEmpty().hasSize(1)
                .allSatisfy(op -> {
                  assertThat(op.getId()).isNotEmpty();
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TENANT_ID,
                          ValueUtils.stringValue(tenantId));
                  assertThat(op.getDocMap().get(Key.VENUE_APGROUPS))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
                      .extracting(
                          ListValue::getValuesList,
                          InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
                      .allSatisfy(vap -> {
                        assertEquals(venueId, vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue());
                        assertTrue(vap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
                        assertEquals(1,
                            vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesCount());
                      });
                });
          });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.UPDATE_VENUE_WIFI_NETWORK_SETTINGS))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(activityImpactedMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityImpactedMessage.getPayload())
              .matches(msg -> msg.getDeviceIdsList().isEmpty())
              .matches(msg -> msg.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID))
              .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
              .isEmpty());

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .satisfies(op -> assertSoftly(softly -> {
                softly.assertThat(payload).isNotNull();
                softly.assertThat(op)
                    .filteredOn(Operation::hasWlanVenue)
                    .hasSize(2)
                    .allMatch(wlanVenueOp -> wlanVenueOp.getAction() == Action.MODIFY,
                        String.format(
                            "The value of `action` field in WlanVenue operation should be %s",
                            Action.MODIFY))
                    .allMatch(wlanVenueOp -> networkVenueId.equals(wlanVenueOp.getId()),
                        String.format("The value of `id` field in WlanVenue operation should be %s",
                            networkVenueId))
                    .extracting(Operation::getWlanVenue)
                    .anySatisfy(wlanVenue -> assertSoftly(alsoSoftly -> {
                      alsoSoftly.assertThat(wlanVenue.getWlanId())
                          .isEqualTo(networkId);
                      alsoSoftly.assertThat(wlanVenue.getVenueId())
                          .isEqualTo(venueId);
                    }));
              })));
    }
  }

  @Nested
  class GivenOweTransitionNetworkPersistedInDb {

    @Payload("addOweTransitionNetwork")
    private OpenWifiNetworkGenerator addOweTransitionNetwork() {
      return Generators.openWifiNetwork()
          .setName(serialName("addOweTransitionNetwork"))
          .setDescription(randomString(64))
          .setWlan(Generators.oweTransitionWifiWlan());
    }

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_NETWORK, payload = @Payload("addOweTransitionNetwork"))
    void thenShouldHandleUpdateOweTransitionNetworkVenueSuccessfully(TxCtx txCtx,
        @Payload("addOweTransitionNetwork") com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiNetwork payload,
        Venue venue, ApGroup apGroup) {
      final var tenantId = txCtx.getTenant();
      final var networkId = payload.getId();
      final var venueId = venue.getId();
      sendActivateWifiNetworkOnVenue(tenantId, venue.getId(), payload.getId());

      final var networkVenue = networkVenueRepository
          .findByTenantIdAndNetworkIdAndVenueId(tenantId, networkId, venueId).orElse(null);
      final var masterNetwork = openNetworkRepository.findByIdAndTenantId(networkId, tenantId).orElse(null);
      assertNotNull(masterNetwork);
      final var slaveNetwork = openNetworkRepository.findByIdAndTenantId(masterNetwork.getOwePairNetworkId(), tenantId).orElse(null);
      assertNotNull(slaveNetwork);
      final var slaveNetworkVenue = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(tenantId, slaveNetwork.getId(), venueId).orElse(null);
      assertNotNull(slaveNetworkVenue);

      assertThat(networkVenue)
          .isNotNull()
          .matches(nv -> Objects.equals(networkId, nv.getNetwork().getId()))
          .matches(nv -> Objects.equals(venueId, nv.getVenue().getId()));
      assertThat(slaveNetworkVenue)
          .isNotNull()
          .matches(nv -> Objects.equals(slaveNetwork.getId(), nv.getNetwork().getId()))
          .matches(nv -> Objects.equals(venueId, nv.getVenue().getId()));

      var networkVenueId = networkVenue.getId();

      var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId);
      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList).asList()
          .isNotEmpty()
          .extracting(Operation.class::cast)
          .satisfies(op -> assertSoftly(softly -> {
            softly.assertThat(payload).isNotNull();
            softly.assertThat(op)
                .filteredOn(Operation::hasWlanVenue)
                .hasSize(2)
                .extracting(Operation::getId)
                .containsExactlyInAnyOrder(networkVenueId, slaveNetworkVenue.getId());
          }));

      final var venueWifiNetworkSettings = new com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetworkSettings();
      venueWifiNetworkSettings.setIsAllApGroups(true);
      venueWifiNetworkSettings.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._2_4_GHz,
          RadioTypeEnum._5_GHz, RadioTypeEnum._6_GHz));

      sendUpdateVenueWifiNetworkSettings(tenantId, randomTxId(), venueId, networkId, venueWifiNetworkSettings);

      var masterNetworkRadios = networkApGroupRadioRepository.findByNetworkIdAndVenueIdAndTenantId(networkId, venueId, tenantId);
      var slaveNetworkRadios = networkApGroupRadioRepository.findByNetworkIdAndVenueIdAndTenantId(slaveNetwork.getId(), venueId, tenantId);
      assertThat(masterNetworkRadios)
          .extracting(NetworkApGroupRadio::getRadio)
          .filteredOn(radio -> radio == StrictRadioTypeEnum._6_GHz)
          .hasSize(1);
      assertThat(slaveNetworkRadios)
          .extracting(NetworkApGroupRadio::getRadio)
          .filteredOn(radio -> radio == StrictRadioTypeEnum._6_GHz)
          .hasSize(1);
      ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId);
      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList).asList()
          .isNotEmpty()
          .extracting(Operation.class::cast)
          .satisfies(op -> assertSoftly(softly -> {
            softly.assertThat(payload).isNotNull();
            softly.assertThat(op)
                .filteredOn(Operation::hasWlanApGroup)
                .extracting(Operation::getWlanApGroup)
                .extracting(WlanApGroup::getRadio)
                .filteredOn(radio -> radio == RadioType.RADIO60)
                .hasSize(2); // Both master and slave should enable 6G
            softly.assertThat(op)
                .filteredOn(Operation::hasWlanVenue)
                .extracting(Operation::getWlanVenue)
                .filteredOn(wlanVenue -> StringUtils.isNotEmpty(wlanVenue.getOweTransWlanId().getValue()))
                .hasSize(2); // OweTransWlanId is not null
          }));
    }
  }
}
