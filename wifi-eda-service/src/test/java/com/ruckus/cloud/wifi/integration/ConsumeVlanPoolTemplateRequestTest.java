package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.VlanPoolAlgoEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.VlanPoolGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@Tag("VlanPoolTemplateTest")
@WifiIntegrationTest
class ConsumeVlanPoolTemplateRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_VLAN_POOL_TEMPLATE)
  class ConsumeAddVlanPoolTemplateRequestTest {

    @Payload
    private final VlanPoolGenerator generator = Generators.vlanPool();

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.VlanPool payload) {
      validateResult(CfgAction.ADD_VLAN_POOL_TEMPLATE, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VLAN_POOL_TEMPLATE)
  class ConsumeUpdateVlanPoolTemplateRequestTest {

    @Payload
    private final VlanPoolGenerator generator = Generators.vlanPool()
        .setId(nullValue(String.class))
        .setName(serialName("UpdatedVlanPoolTemplate"))
        .setDescription(randomString(64));

    private String vlanPoolTemplateId;

    @BeforeEach
    void givenOneVlanPoolPersistedInDb(@Template final VlanPool vlanPool) {
      vlanPoolTemplateId = vlanPool.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("vlanPoolTemplateId", vlanPoolTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.VlanPool payload) {
      validateResult(CfgAction.UPDATE_VLAN_POOL_TEMPLATE, vlanPoolTemplateId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_VLAN_POOL_TEMPLATE)
  class ConsumeDeleteVlanPoolTemplateRequestTest {

    private String vlanPoolTemplateId;

    private VlanPool vlanPoolTemplate;

    @BeforeEach
    void givenOneVlanPoolPersistedInDb(@Template final VlanPool vlanPool) {
      vlanPoolTemplateId = vlanPool.getId();
      vlanPoolTemplate = vlanPool;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("vlanPoolTemplateId", vlanPoolTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateResult(CfgAction.DELETE_VLAN_POOL_TEMPLATE, vlanPoolTemplateId, null);
    }

    @Nested
    @ApiAction(CfgAction.DELETE_VLAN_POOL_TEMPLATE)
    class WithNetworkVenueBinding {

      private String networkId;
      @BeforeEach
      void givenOneOpenNetworkPersistedInDbAndActivatedOnOneVenueInDb(Tenant tenant,
          @Template Venue venue) {
        final var openNetwork = network(OpenNetwork.class).generate();
        openNetwork.setIsTemplate(true);
        openNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
        openNetwork.getWlan().setNetwork(openNetwork);
        openNetwork.getWlan().setMacAddressAuthentication(false);
        openNetwork.getWlan().getAdvancedCustomization().setVlanPool(vlanPoolTemplate);
        repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());

        final var networkVenue = networkVenue()
            .setNetwork(always(openNetwork)).setVenue(always(venue))
            .setVlanPoolId(always(vlanPoolTemplateId))
            .setIsTemplate(always(true)).generate();
        openNetwork.setNetworkVenues(List.of(networkVenue));
        repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

        networkId = openNetwork.getId();
      }

      @ApiAction.RequestParams
      private RequestParams requestParams() {
        return new RequestParams().addPathVariable("vlanPoolTemplateId", vlanPoolTemplateId);
      }

      @Test
      void thenShouldHandleTheRequestSuccessfully() {
        validateResult(CfgAction.DELETE_VLAN_POOL_TEMPLATE, vlanPoolTemplateId, null);
      }
    }
  }

  private void validateResult(CfgAction apiAction,
      com.ruckus.cloud.wifi.eda.viewmodel.VlanPool payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges = revisionService.changes(requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    final String vlanPoolId = txChanges.getNewEntities().stream()
        .filter(txEntity -> txEntity.getEntity() instanceof VlanPool)
        .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
    validateResult(apiAction, vlanPoolId, payload);
  }

  private void validateResult(CfgAction apiAction, String vlanPoolId,
      com.ruckus.cloud.wifi.eda.viewmodel.VlanPool payload) {
    validateRepositoryData(vlanPoolId, payload, apiAction);
    validateDdccmCfgRequestMessages(apiAction, List.of(vlanPoolId), payload);
    validateCmnCfgCollectorMessages(apiAction, List.of(vlanPoolId));
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(String vlanPoolId,
      com.ruckus.cloud.wifi.eda.viewmodel.VlanPool payload, CfgAction apiAction) {
    if (vlanPoolId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final VlanPool vlanPool = repositoryUtil.find(VlanPool.class, vlanPoolId);

    if (payload == null) {
      assertThat(vlanPool).isNull();
      return;
    }

    assertThat(vlanPool)
        .isNotNull()
        .matches(vp -> Objects.equals(vp.getId(), vlanPoolId))
        .matches(vp -> Objects.equals(vp.getName(), payload.getName()))
        .matches(vp -> Objects.equals(vp.getDescription(), payload.getDescription()))
        .matches(vp -> Objects.equals(vp.getVlanMembers(), payload.getVlanMembers()));
  }

  private void validateDdccmCfgRequestMessages(CfgAction apiAction, List<String> vlanPoolIdList,
      com.ruckus.cloud.wifi.eda.viewmodel.VlanPool payload) {
    if (apiAction == null || vlanPoolIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVlanPool)
            .hasSize(vlanPoolIdList.size())
            .allMatch(op -> vlanPoolIdList.contains(op.getId()))
            .allMatch(op -> op.getAction() == action(apiAction))
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVlanPool)
                    .matches(vp -> vlanPoolIdList.contains(vp.getId()));
              } else {
                assertThat(payload).isNotNull();
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVlanPool)
                    .matches(vp -> vlanPoolIdList.contains(vp.getId()))
                    .matches(vp -> vp.getAlgo() == VlanPoolAlgoEnum.VlanPool_MAC_HASH)
                    .matches(vp -> payload.getName().equals(vp.getName()))
                    .matches(vp -> payload.getVlanMembers().equals(vp.getVlanMembersList()));
              }
            }));
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_VLAN_POOL_TEMPLATE -> Action.ADD;
      case UPDATE_VLAN_POOL_TEMPLATE -> Action.MODIFY;
      case DELETE_VLAN_POOL_TEMPLATE -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, List<String> vlanPoolIdList) {
    if (apiAction == null || vlanPoolIdList == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> EsConstants.Index.VLAN_POOL_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(vlanPoolIdList.size())
            .allMatch(op -> vlanPoolIdList.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction))
            .allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> vlanPoolIdList.contains(doc.get(EsConstants.Key.ID).getStringValue()))
                    .matches(doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()));
              }
            }));
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_VLAN_POOL_TEMPLATE -> OpType.ADD;
      case UPDATE_VLAN_POOL_TEMPLATE -> OpType.MOD;
      case DELETE_VLAN_POOL_TEMPLATE -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);;
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(msg -> msg.getStatus().equals(Status.OK))
          .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
          .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(tenantId);  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_VLAN_POOL_TEMPLATE -> ApiFlowNames.ADD_VLAN_POOL_TEMPLATE;
      case UPDATE_VLAN_POOL_TEMPLATE -> ApiFlowNames.UPDATE_VLAN_POOL_TEMPLATE;
      case DELETE_VLAN_POOL_TEMPLATE -> ApiFlowNames.DELETE_VLAN_POOL_TEMPLATE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
