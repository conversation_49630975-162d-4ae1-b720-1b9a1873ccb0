package com.ruckus.cloud.wifi.notification;

import com.ruckus.cloud.notification.NotificationRequests;
import com.ruckus.cloud.tenantservice.api.GetNotificationRecipientsResponse;
import com.ruckus.cloud.tenantservice.api.NotificationEndpoint;
import com.ruckus.cloud.tenantservice.api.NotificationEndpointStatus;
import com.ruckus.cloud.tenantservice.api.NotificationEndpointType;
import com.ruckus.cloud.tenantservice.api.NotificationRecipient;
import com.ruckus.cloud.tenantservice.api.NotificationSubscriptionType;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.notification.client.nuvo.NuvoClient;
import com.ruckus.cloud.wifi.notification.sender.NotificationSenderFactory;
import com.ruckus.cloud.wifi.notification.sender.AcxFirmwareNotificationSender;
import com.ruckus.cloud.wifi.notification.sender.AcxNewFirmwareNotificationSender;
import com.ruckus.cloud.wifi.notification.template.FirmwareNotificationMessage;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.AggregateNotificationService;
import com.ruckus.cloud.wifi.service.entity.TenantFirmwareReleaseType;
import com.ruckus.cloud.wifi.service.impl.ApBranchFamilyServiceRouter;
import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.notification.dto.ImpactModelTemplateDto;
import com.ruckus.cloud.wifi.notification.dto.ScheduleTemplateDto;
import com.ruckus.cloud.wifi.notification.dto.VenueTemplateDto;
import com.ruckus.cloud.wifi.notification.template.TemplateManager;
import com.ruckus.cloud.wifi.notification.template.TemplateManagerImpl;
import com.ruckus.cloud.wifi.notification.template.UpdateNotificationTemplate;
import com.ruckus.cloud.wifi.notification.template.VenueTemplateStatusEnum;
import com.ruckus.cloud.wifi.notification.utils.UpgradeFirmwareNotificationUtil;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.service.integration.config.ApBranchFamilyServiceRouterTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import java.util.LinkedList;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;

import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.context.annotation.Import;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
@WifiJpaDataTest
public class NotificationUpdateFirmwareHandlerTest {
  @MockBean private ApVersionRepository apVersionRepository;

  @SpyBean private NotificationUpdateFirmwareHandler notificationUpdateFirmwareHandler;

  @MockBean private TenantClient tenantClient;

  @MockBean private NuvoClient nuvoClient;

  @SpyBean private TemplateManager templateManager;

  @MockBean private VenueTemplateConverter converter;

  @MockBean private NotificationMessageGetterFactory notificationMessageGetterFactory;

  @MockBean private AggregateNotificationService aggregateNotificationService;

  @MockBean private VenueRepository venueRepository;

  @SpyBean private AcxFirmwareNotificationMessageGetter acxFirmwareNotificationMessageGetter;

  @SpyBean private AcxNewFirmwareNotificationMessageGetter acxNewFirmwareNotificationMessageGetter;

  @MockBean private NotificationSenderFactory notificationSenderFactory;

  @SpyBean private AcxNewFirmwareNotificationSender acxNewFirmwareNotificationSender;

  @SpyBean private AcxFirmwareNotificationSender acxFirmwareNotificationSender;

  @SpyBean private SimpleFirmwareNotificationMessageProvider simpleFirmwareNotificationMessageProvider;

  private TenantFirmwareNotificationUtilTest notificationUtil;

  private ArgumentCaptor<NotificationRequests> requestCaptor =
      ArgumentCaptor.forClass(NotificationRequests.class);
  private ArgumentCaptor<FirmwareNotificationMessage> messageArgumentCaptor =
      ArgumentCaptor.forClass(FirmwareNotificationMessage.class);

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  public void setup(Tenant tenant) {
    doReturn(
            GetNotificationRecipientsResponse.newBuilder()
                .setTenantName("dummy tenant " + tenant.getId())
                .addRecipients(
                    NotificationRecipient.newBuilder()
                        .setIsDelegatedVar(false)
                        .setDescription("dummy recipient " + tenant.getId())
                        .setTenantName("dummy tenant " + tenant.getId())
                        .addEndpoints(
                            NotificationEndpoint.newBuilder()
                                .setStatus(NotificationEndpointStatus.OK)
                                .setType(NotificationEndpointType.EMAIL)
                                .setActive(true)
                                .setDestination("<EMAIL>"))
                        .addEndpoints(
                            NotificationEndpoint.newBuilder()
                                .setStatus(NotificationEndpointStatus.OK)
                                .setType(NotificationEndpointType.SMS)
                                .setActive(true)
                                .setDestination("+972541234567"))
                        .addEndpoints(
                            NotificationEndpoint.newBuilder()
                                .setStatus(NotificationEndpointStatus.OK)
                                .setType(NotificationEndpointType.MOBILE_PUSH)
                                .setMobilePlatform("GCM")
                                .setActive(true)
                                .setDestination("android-device-id")))
                .addRecipients(
                    NotificationRecipient.newBuilder()
                        .setIsDelegatedVar(false)
                        .setDescription("dummy var of " + tenant.getId())
                        .setTenantName("dummy var")
                        .setIsDelegatedVar(true)
                        .addEndpoints(
                            NotificationEndpoint.newBuilder()
                                .setStatus(NotificationEndpointStatus.OK)
                                .setType(NotificationEndpointType.MOBILE_PUSH)
                                .setActive(true)
                                .setDestination("ios-device-id"))
                        .addEndpoints(
                            NotificationEndpoint.newBuilder()
                                .setStatus(NotificationEndpointStatus.OK)
                                .setType(NotificationEndpointType.EMAIL)
                                .setActive(false)
                                .setDestination("<EMAIL>")))
                .build())
        .when(tenantClient)
        .getAggregatedNotificationRecipients(eq(tenant.getId()), anyString());
  }

  @Test
  public void sendUpdateFinishNotificationsTest(Tenant tenant, Venue venue) {
    // Given
    String requestId = randomTxId();
    ApVersion apVersion = ApVersionTestFixture.recommendedApVersion("*********.100", a -> {});
    apVersionRepository.saveAll(List.of(apVersion));
    tenant.setLatestReleaseVersion(apVersion);
    tenant.setName("TenantName");

    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName(venue.getName());
    dto.setTimeZone(UpgradeFirmwareNotificationUtil.convertVenueTimezone(venue.getTimezone()));
    dto.setNumberOfAps(1);
    dto.setAddress(venue.getAddressLine());
    when(converter.toTemplate(eq(tenant.getId()), eq(List.of(venue)), eq(VenueTemplateStatusEnum.SUCCESS), any()))
        .thenReturn(List.of(dto));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxFirmwareNotificationSender);


    // When
    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(
        tenant, List.of(apVersion), List.of(dto), true, requestId);

    // Then
    verify(nuvoClient, times(1)).send(any(), anyString(), anyString(), anyString());
  }

  @Test
  public void testSendUpdateFinishNotificationsWhenTenantUnsubscribeFirmware(Tenant tenant, Venue venue) {
    // Given
    String requestId = randomTxId();
    ApVersion apVersion = ApVersionTestFixture.recommendedApVersion("*********.100", a -> {});
    apVersionRepository.saveAll(List.of(apVersion));
    tenant.setLatestReleaseVersion(apVersion);
    tenant.setName("TenantName");

    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName(venue.getName());
    dto.setTimeZone(UpgradeFirmwareNotificationUtil.convertVenueTimezone(venue.getTimezone()));
    dto.setNumberOfAps(1);
    dto.setAddress(venue.getAddressLine());
    when(converter.toTemplate(eq(tenant.getId()), eq(List.of(venue)), eq(VenueTemplateStatusEnum.SUCCESS), any()))
        .thenReturn(List.of(dto));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxFirmwareNotificationSender);
    when(tenantClient.getAggregatedNotificationRecipients(eq(tenant.getId()), eq(requestId)))
        .thenReturn(mockNotificationRecipientsWithUnsubscribes());

    // When
    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(
        tenant, List.of(apVersion), List.of(dto), true, requestId);

    // Then
    verify(nuvoClient, times(0)).send(any(), anyString(), anyString(), anyString());
  }

  @Test
  public void sendReminderScheduleNotificationsTest(Tenant tenant, Venue venue) {
    // Given
    String requestId = randomTxId();
    ApVersion apVersion = ApVersionTestFixture.recommendedApVersion("*********.100", a -> {});
    apVersionRepository.saveAll(List.of(apVersion));
    tenant.setLatestReleaseVersion(apVersion);
    tenant.setName("TenantName");

    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName(venue.getName());
    dto.setTimeZone(UpgradeFirmwareNotificationUtil.convertVenueTimezone(venue.getTimezone()));
    dto.setNumberOfAps(1);
    dto.setAddress(venue.getAddressLine());
    when(converter.toTemplate(venue)).thenReturn(dto);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxFirmwareNotificationSender);

    // When
    notificationUpdateFirmwareHandler.sendReminderScheduleNotifications(
        tenant, List.of(apVersion), "testSlotDate", List.of(venue), requestId, Set.of("Asia/Taipei"));

    // Then
    verify(nuvoClient, times(1)).send(any(), anyString(), anyString(), anyString());
  }

  @Test
  public void sendScheduleUpdateFinishNotificationsTest(Tenant tenant, Venue venue) {
    // Given
    String requestId = randomTxId();
    ApVersion apVersion = ApVersionTestFixture.recommendedApVersion("*********.100", a -> {});
    apVersionRepository.saveAll(List.of(apVersion));
    tenant.setLatestReleaseVersion(apVersion);
    tenant.setName("TenantName");

    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName(venue.getName());
    dto.setTimeZone(UpgradeFirmwareNotificationUtil.convertVenueTimezone(venue.getTimezone()));
    dto.setNumberOfAps(1);
    dto.setAddress(venue.getAddressLine());
    when(converter.toTemplate(venue, VenueTemplateStatusEnum.SUCCESS)).thenReturn(dto);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxFirmwareNotificationSender);

    // When
    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(
        tenant, List.of(apVersion), List.of(dto), true, requestId);

    // Then
    verify(nuvoClient, times(1)).send(any(), anyString(), anyString(), anyString());
  }

  @Test
  public void testSendCreateScheduleWithImpactModel(Tenant tenant, Venue venue) {
    ApVersion apVersion = ApVersionTestFixture.recommendedApVersion("*********.100", a -> {});
    tenant.setLatestReleaseVersion(apVersion);
    tenant.setName("TenantName");
    VenueUpgradeVersionsMapping venueVersionMapping = notificationUtil.mockVenueUpgradeVersionMapping(1, apVersion);

    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName(venue.getName());
    dto.setTimeZone(UpgradeFirmwareNotificationUtil.convertVenueTimezone(venue.getTimezone()));
    dto.setNumberOfAps(1);
    dto.setAddress(venue.getAddressLine());

    when(converter.groupVenueTemplateByTimeSlot(anyList(), any()))
        .thenReturn(Map.of("time1", List.of(dto)));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(
        tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());

    verify(nuvoClient, times(1)).send(any(), anyString(), anyString(), anyString());

    assertEquals(
        UpdateNotificationTemplate.CREATE_SCHEDULE_WITH_IMPACT_MODEL_ACX,
        messageArgumentCaptor.getValue().getTemplate());

    String tenantName = tenantClient.getAggregatedNotificationRecipients(tenant.getId(), randomTxId()).getTenantName();
    verifyModelData(tenantName, messageArgumentCaptor.getValue().getModel(), "*********.100 (Recommended) on active devices",
        1);
  }

  @Test
  public void testSendCreateScheduleReleaseMultipleAbf(Tenant tenant, Venue venue) {
    ApVersion latestApVersion = ApVersionTestFixture.recommendedApVersion("*********.544", a -> {});
    ApVersion legacyApVersion = ApVersionTestFixture.recommendedApVersion("*********.534", a -> {});
    apVersionRepository.saveAll(List.of(latestApVersion, legacyApVersion));
    tenant.setLatestReleaseVersion(latestApVersion);
    tenant.setName("TenantName");
    VenueUpgradeVersionsMapping venueVersionMapping =
        notificationUtil.mockVenueUpgradeVersionMapping(1, latestApVersion, legacyApVersion);

    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName(venue.getName());
    dto.setTimeZone(UpgradeFirmwareNotificationUtil.convertVenueTimezone(venue.getTimezone()));
    dto.setNumberOfAps(1);
    dto.setAddress(venue.getAddressLine());

    when(converter.groupVenueTemplateByTimeSlot(anyList(), any()))
        .thenReturn(Map.of("time1", List.of(dto)));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(
        tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());

    verify(nuvoClient, times(1)).send(any(), anyString(), anyString(), anyString());

    assertEquals(
        UpdateNotificationTemplate.CREATE_SCHEDULE_WITH_IMPACT_MODEL_ACX,
        messageArgumentCaptor.getValue().getTemplate());

    String tenantName = tenantClient.getAggregatedNotificationRecipients(tenant.getId(), randomTxId()).getTenantName();
    verifyModelData(tenantName, messageArgumentCaptor.getValue().getModel(),
        "*********.544 (Recommended) on active devices, *********.534 (Recommended) on legacy devices",
        2);
  }

  @Test
  public void testSendCreateScheduleReleaseSameAbfTwice(Tenant tenant, Venue venue) {
    ApVersion latestApVersion = ApVersionTestFixture.recommendedApVersion("*********.544", a -> {});
    ApVersion legacyApVersion = ApVersionTestFixture.recommendedApVersion("*********.534", a -> {});
    apVersionRepository.saveAll(List.of(latestApVersion, legacyApVersion));
    VenueUpgradeVersionsMapping venueVersionMapping =
        notificationUtil.mockVenueUpgradeVersionMapping(1, latestApVersion, legacyApVersion);

    tenant.setLatestReleaseVersion(latestApVersion);
    tenant.setName("TenantName");

    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName(venue.getName());
    dto.setTimeZone(UpgradeFirmwareNotificationUtil.convertVenueTimezone(venue.getTimezone()));
    dto.setNumberOfAps(1);
    dto.setAddress(venue.getAddressLine());

    when(converter.groupVenueTemplateByTimeSlot(anyList(), any()))
        .thenReturn(Map.of("time1", List.of(dto)));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(
        tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());

    verify(nuvoClient, times(1)).send(any(), anyString(), anyString(), anyString());

    assertEquals(
        UpdateNotificationTemplate.CREATE_SCHEDULE_WITH_IMPACT_MODEL_ACX,
        messageArgumentCaptor.getValue().getTemplate());

    String tenantName = tenantClient.getAggregatedNotificationRecipients(tenant.getId(), randomTxId()).getTenantName();
    verifyModelData(tenantName, messageArgumentCaptor.getValue().getModel(),
        "*********.544 (Recommended), *********.534 (Recommended) on active devices",
        1);
  }

  private void verifyModelData(String tenantName, Map<String, Object> models, String expectedVersionNumber, int expectedImpactModelListSize) {
    assertTrue(models.containsKey("version_number"));
    assertEquals(expectedVersionNumber, models.get("version_number"));
    assertTrue(models.containsKey("impactModelList"));
    List<ImpactModelTemplateDto> modelList = (List) models.get("impactModelList");
    assertEquals(expectedImpactModelListSize, modelList.size());
    assertNotNull(modelList.get(0).getModelListString());
    assertThat(modelList.get(0).getModelListString().split(",\\s*")).containsAll(List.of("R550"));

    assertTrue(models.containsKey("Subject"));
    assertEquals(tenantName + ": RUCKUS One Scheduled Update (United States Region)", models.get("Subject"));
  }

  @Test
  public void testSendCreateScheduleWithNewContent(Tenant tenant, Venue venue) {
    tenant.setName("TenantName");
    VenueTemplateDto dto = mockVenueTemplateDto();
    when(converter.groupVenueTemplateByTimeSlot(anyList()))
        .thenReturn(Map.of("time1", List.of(dto), "time2", List.of(dto)));;
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    VenueUpgradeVersionsMapping venueVersionMapping =
        notificationUtil.mockVenueUpgradeVersionMapping(2,
            TenantFirmwareNotificationUtilTest.mockApVersion("*********.100"));
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(
        tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Scheduled firmware update"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize == null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendCreateScheduleWithIndividualMail(Tenant tenant, Venue venue) {
    // Given
    tenant.setName("TenantName");
    when(venueRepository.findScheduledVenues(any())).thenReturn(List.of(venue));
    when(aggregateNotificationService.sendAggregateNotification(any())).thenReturn(false);
    VenueTemplateDto dto = mockVenueTemplateDto();
    when(converter.groupVenueTemplateByTimeSlot(anyList())).thenReturn(Map.of("time1", List.of(dto), "time2", List.of(dto)));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any())).thenReturn(acxNewFirmwareNotificationSender);
    VenueUpgradeVersionsMapping venueVersionMapping = notificationUtil.mockVenueUpgradeVersionMapping(2,
        TenantFirmwareNotificationUtilTest.mockApVersion("*********.100"));

    // When
    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then
    verify(notificationUpdateFirmwareHandler).send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_ACX_NEW, messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Scheduled firmware update"));
  }

  @Test
  public void testSendCreateScheduleWithoutIndividualMail(Tenant tenant, Venue venue) {
    // Given
    when(venueRepository.findScheduledVenues(any())).thenReturn(List.of(venue));
    when(aggregateNotificationService.sendAggregateNotification(any())).thenReturn(true);
    VenueUpgradeVersionsMapping venueVersionMapping = notificationUtil.mockVenueUpgradeVersionMapping(2,
        TenantFirmwareNotificationUtilTest.mockApVersion("*********.100"));

    // When
    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then
    verify(notificationUpdateFirmwareHandler, never()).send(any(), any(), anyString(), any());
  }

  @Test
  public void testSendScheduleChangeWithNewContent(Tenant tenant) {
    tenant.setName("TenantName");
    String testSlotDate = "time-1@@time-2";
    VenueTemplateDto dto = mockVenueTemplateDto();
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendChangeScheduleNotifications(
        tenant, new ApVersion(), Map.of(testSlotDate, List.of(dto)), randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.CHANGE_SCHEDULE_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS ONE Network firmware update schedule modified"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize == null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendScheduleReminderWithNewContent(Tenant tenant) {
    tenant.setName("TenantName");
    String testSlotDate = "time-1";
    List<Venue> venues = List.of(new Venue());
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(1);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(converter.toTemplate(eq(tenant.getId()), eq(venues), any())).thenReturn(dtos);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendReminderScheduleNotifications(
        tenant, List.of(new ApVersion()), testSlotDate, venues, randomTxId(), null);

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.SCHEDULE_REMINDER_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Scheduled firmware update"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize == null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendCancelUpdateWithNewContent(Tenant tenant) {
    tenant.setName("TenantName");
    List<Venue> venues = List.of(new Venue());
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(1);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    VenueUpgradeVersionsMapping venueVersionMapping = notificationUtil.mockVenueUpgradeVersionMapping(1, new ApVersion());
    when(converter.toTemplate(eq(tenant.getId()), eq(venues), eq(venueVersionMapping)))
        .thenReturn(dtos);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCancelScheduleNotifications(tenant, venues, venueVersionMapping, null, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.UPDATE_CANCEL_ADMIN_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Network firmware update was cancelled"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize == null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendUpdateSuccessWithNewContent(Tenant tenant) {
    tenant.setName("TenantName");
    List<Venue> venues = List.of(new Venue());
    VenueTemplateDto dto = mockVenueTemplateDto();
    dto.setStatus(VenueTemplateStatusEnum.SUCCESS);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(tenant, List.of(new ApVersion()),
        List.of(dto), true, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.UPDATE_FINISHED_SUCCESSFULLY_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Network firmware was updated successfully"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize == null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendUpdateFailWithNewContent(Tenant tenant) {
    tenant.setName("TenantName");
    List<Venue> venues = List.of(new Venue());
    VenueTemplateDto dto = mockVenueTemplateDto();
    dto.setStatus(VenueTemplateStatusEnum.FAILED);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(tenant, List.of(new ApVersion()),
        List.of(dto), false, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.UPDATE_FINISHED_FAIL_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Network firmware update was failed"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize == null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendCreateScheduleWithOverSizedVenue(Tenant tenant, Venue venue) {
    tenant.setName("TenantName");
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(150);
    List<VenueTemplateDto> dtos2 = notificationUtil.mockVenueTemplateDtos(150);
    when(converter.groupVenueTemplateByTimeSlot(anyList()))
        .thenReturn(Map.of("time1", dtos, "time2", dtos2));;
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    VenueUpgradeVersionsMapping venueVersionMapping =
        notificationUtil.mockVenueUpgradeVersionMapping(1, new ApVersion());
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(
        tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Scheduled firmware update"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize != null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendScheduleChangeWithOverSizedVenue(Tenant tenant) {
    tenant.setName("TenantName");
    String testSlotDate = "time-1@@time-2";
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(150);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendChangeScheduleNotifications(
        tenant, new ApVersion(), Map.of(testSlotDate, dtos), randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.CHANGE_SCHEDULE_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS ONE Network firmware update schedule modified"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize != null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendScheduleReminderWithOverSizedVenue(Tenant tenant) {
    tenant.setName("TenantName");
    String testSlotDate = "time-1";
    List<Venue> venues = notificationUtil.mockVenues(123);
    List<ApVersion> versions = List.of(new ApVersion("*********.100"));
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(123);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(converter.toTemplate(eq(tenant.getId()), eq(venues), any()))
        .thenReturn(dtos);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendReminderScheduleNotifications(
        tenant, versions, testSlotDate, venues, randomTxId(), null);

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.SCHEDULE_REMINDER_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Scheduled firmware update"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize != null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendCancelUpdateWithOverSizedVenue(Tenant tenant) {
    tenant.setName("TenantName");
    List<Venue> venues = notificationUtil.mockVenues(123);
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(123);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    VenueUpgradeVersionsMapping venueVersionMapping = notificationUtil.mockVenueUpgradeVersionMapping(123, new ApVersion());
    when(converter.toTemplate(tenant.getId(), venues, venueVersionMapping))
        .thenReturn(dtos);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCancelScheduleNotifications(tenant, venues, venueVersionMapping, null, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.UPDATE_CANCEL_ADMIN_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Network firmware update was cancelled"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize != null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendUpdateSuccessWithOverSizedVenue(Tenant tenant) {
    tenant.setName("TenantName");
    List<VenueTemplateDto> dtos =
        mockVenueTemplateDtosWithStatus(111, VenueTemplateStatusEnum.SUCCESS);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(tenant, List.of(new ApVersion()),
        dtos, true, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.UPDATE_FINISHED_SUCCESSFULLY_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Network firmware was updated successfully"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize != null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendUpdateFailWithOverSizedVenue(Tenant tenant) {
    tenant.setName("TenantName");
    List<VenueTemplateDto> dtos = mockVenueTemplateDtosWithStatus(138, VenueTemplateStatusEnum.FAILED);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(tenant, List.of(new ApVersion()),
        dtos, false, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.UPDATE_FINISHED_FAIL_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    String subject = (String) messageArgumentCaptor.getValue().getModel().get("Subject");
    assertTrue(subject.contains("RUCKUS One Network firmware update was failed"));
    Object moreVenueSize = messageArgumentCaptor.getValue().getModel().get("more_venue_size");
    assertTrue(moreVenueSize != null);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendCreateScheduleWithImpactApNumber(Tenant tenant, Venue venue) {
    tenant.setName("TenantName");
    List<Venue> venues = List.of(venue);
    VenueTemplateDto dto = mockVenueTemplateDtoWithApNumber(3,5);
    when(converter.groupVenueTemplateByTimeSlot(anyList()))
        .thenReturn(Map.of("time1", List.of(dto)));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    VenueUpgradeVersionsMapping venueVersionMapping =
        notificationUtil.mockVenueUpgradeVersionMapping(1, new ApVersion());
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(
        tenant, venues, venueVersionMapping, randomTxId());

    // Then
    verify(converter).toTemplate(eq(tenant.getId()), eq(venues), eq(venueVersionMapping));
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    List<ScheduleTemplateDto> scheduleTemplateDtos =
        (List<ScheduleTemplateDto>) messageArgumentCaptor.getValue().getModel().get("scheduleList");
    verifyImpactApNumber(scheduleTemplateDtos, dto);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendScheduleChangeWithImpactApNumber(Tenant tenant) {
    tenant.setName("TenantName");
    String testSlotDate = "time-1@@time-2";
    VenueTemplateDto dto = mockVenueTemplateDtoWithApNumber(2,4);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendChangeScheduleNotifications(
        tenant, new ApVersion(), Map.of(testSlotDate, List.of(dto)), randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.CHANGE_SCHEDULE_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    List<ScheduleTemplateDto> scheduleTemplateDtos =
        (List<ScheduleTemplateDto>) messageArgumentCaptor.getValue().getModel().get("scheduleList");
    verifyImpactApNumber(scheduleTemplateDtos, dto);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendScheduleReminderWithImpactApNumber(Tenant tenant) {
    tenant.setName("TenantName");
    String testSlotDate = "time-1";
    List<Venue> venues = List.of(new Venue());
    List<ApVersion> versions = List.of(new ApVersion("*********.100"));
    VenueTemplateDto dto = mockVenueTemplateDtoWithApNumber(1,3);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(converter.toTemplate(eq(tenant.getId()), eq(venues), any())).thenReturn(List.of(dto));
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendReminderScheduleNotifications(
        tenant, versions, testSlotDate, venues, randomTxId(), null);

    verify(converter).toTemplate(eq(tenant.getId()), eq(venues), any());
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.SCHEDULE_REMINDER_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    List<ScheduleTemplateDto> scheduleTemplateDtos =
        (List<ScheduleTemplateDto>) messageArgumentCaptor.getValue().getModel().get("scheduleList");
    verifyImpactApNumber(scheduleTemplateDtos, dto);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendCancelUpdateWithImpactApNumber(Tenant tenant) {
    tenant.setName("TenantName");
    List<Venue> venues = List.of(new Venue());
    VenueTemplateDto dto = mockVenueTemplateDtoWithApNumber(8,8);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);;
    VenueUpgradeVersionsMapping venueVersionMapping = notificationUtil.mockVenueUpgradeVersionMapping(1, new ApVersion());
    when(converter.toTemplate(eq(tenant.getId()), eq(venues), eq(venueVersionMapping)))
        .thenReturn(List.of(dto));
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCancelScheduleNotifications(tenant, venues, venueVersionMapping, null, randomTxId());

    verify(converter).toTemplate(eq(tenant.getId()), eq(venues), eq(venueVersionMapping));
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.UPDATE_CANCEL_ADMIN_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    List<ScheduleTemplateDto> scheduleTemplateDtos =
        (List<ScheduleTemplateDto>) messageArgumentCaptor.getValue().getModel().get("scheduleList");
    verifyImpactApNumber(scheduleTemplateDtos, dto);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendUpdateSuccessWithImpactApNumber(Tenant tenant) {
    tenant.setName("TenantName");
    VenueTemplateDto dto = mockVenueTemplateDtoWithApNumber(3,7);
    dto.setStatus(VenueTemplateStatusEnum.SUCCESS);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(tenant, List.of(new ApVersion()),
        List.of(dto), true, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.UPDATE_FINISHED_SUCCESSFULLY_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    List<ScheduleTemplateDto> scheduleTemplateDtos =
        (List<ScheduleTemplateDto>) messageArgumentCaptor.getValue().getModel().get("scheduleList");
    verifyImpactApNumber(scheduleTemplateDtos, dto);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendUpdateFailWithImpactApNumber(Tenant tenant) {
    tenant.setName("TenantName");
    VenueTemplateDto dto = mockVenueTemplateDtoWithApNumber(2,8);
    dto.setStatus(VenueTemplateStatusEnum.FAILED);
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(tenant, List.of(new ApVersion()),
        List.of(dto), false, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.UPDATE_FINISHED_FAIL_ACX_NEW,
        messageArgumentCaptor.getValue().getTemplate());
    List<ScheduleTemplateDto> scheduleTemplateDtos =
        (List<ScheduleTemplateDto>) messageArgumentCaptor.getValue().getModel().get("scheduleList");
    verifyImpactApNumber(scheduleTemplateDtos, dto);

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendCreateScheduleWithVersionNumber(Tenant tenant, Venue venue) {
    tenant.setName("TenantName");
    VenueTemplateDto dto = mockVenueTemplateDto();
    when(converter.groupVenueTemplateByTimeSlot(anyList()))
        .thenReturn(Map.of("time1", List.of(dto)));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    List<ApVersion> versions = notificationUtil.mockApVersions("*********.100", "6.2.2.103.200", "7.0.0.103.111");
    VenueUpgradeVersionsMapping venueVersionMapping =
        notificationUtil.mockVenueUpgradeVersionMapping(1, versions.toArray(ApVersion[]::new));
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(
        tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then;
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    String apVersion =
        (String) messageArgumentCaptor.getValue().getModel().get("version_number");
    verifyVersionNumber(apVersion, "*********.100", "6.2.2.103.200", "7.0.0.103.111");

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendScheduleReminderWithVersionNumber(Tenant tenant) {
    tenant.setName("TenantName");
    String testSlotDate = "time-1";
    List<Venue> venues = List.of(new Venue());
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(1);
    List<ApVersion> versions = notificationUtil.mockApVersions("*********.100", "6.2.2.103.200", "7.0.0.103.111");
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(converter.toTemplate(eq(tenant.getId()), eq(venues), any()))
        .thenReturn(dtos);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendReminderScheduleNotifications(
        tenant, versions, testSlotDate, venues, randomTxId(), null);

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    String apVersion =
        (String) messageArgumentCaptor.getValue().getModel().get("version_number");
    verifyVersionNumber(apVersion, "*********.100", "6.2.2.103.200", "7.0.0.103.111");

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendUpdateSuccessWithVersionNumber(Tenant tenant) {
    tenant.setName("TenantName");
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(1);
    List<ApVersion> versions = notificationUtil.mockApVersions("*********.100", "6.2.2.103.200", "7.0.0.103.111");
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(tenant, versions,
        dtos, true, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    String apVersion =
        (String) messageArgumentCaptor.getValue().getModel().get("version_number");
    verifyVersionNumber(apVersion, "*********.100", "6.2.2.103.200", "7.0.0.103.111");

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendUpdateFailWithVersionNumber(Tenant tenant) {
    tenant.setName("TenantName");
    List<VenueTemplateDto> dtos = notificationUtil.mockVenueTemplateDtos(1);
    List<ApVersion> versions = notificationUtil.mockApVersions("*********.100", "6.2.2.103.200", "7.0.0.103.111");
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendUpdateFinishNotifications(tenant, versions,
        dtos, false, randomTxId());

    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    String apVersion =
        (String) messageArgumentCaptor.getValue().getModel().get("version_number");
    verifyVersionNumber(apVersion, "*********.100", "6.2.2.103.200", "7.0.0.103.111");

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testNotSendCreateScheduleWithoutAPInTenant(Tenant tenant, Venue venue) {
    tenant.setName("TenantName");
    VenueTemplateDto dto = mockVenueTemplateDtoWithoutAP();
    when(converter.groupVenueTemplateByTimeSlot(anyList()))
        .thenReturn(Map.of("time1", List.of(dto)));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    List<ApVersion> versions = notificationUtil.mockApVersions("*********.100");
    VenueUpgradeVersionsMapping venueVersionMapping =
        notificationUtil.mockVenueUpgradeVersionMapping(1, versions.toArray(ApVersion[]::new));
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(
        tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then;
    verify(notificationUpdateFirmwareHandler)
        .send(any(), any(), anyString(), any());
    verify(nuvoClient, never()).send(any(NotificationRequests.class), anyString(), anyString(), anyString());
  }

  @Test
  public void testSendCreateScheduleOnlyForVenueWithAP(Tenant tenant, Venue venue) {
    tenant.setName("TenantName");
    VenueTemplateDto dto = mockVenueTemplateDtoWithoutAP();
    VenueTemplateDto dto2 = mockVenueTemplateDto();
    when(converter.groupVenueTemplateByTimeSlot(anyList()))
        .thenReturn(Map.of("time1", List.of(dto, dto2)));
    when(notificationMessageGetterFactory.getByFeatureFlag(anyString())).
        thenReturn(acxNewFirmwareNotificationMessageGetter);
    List<ApVersion> versions = notificationUtil.mockApVersions("*********.100");
    VenueUpgradeVersionsMapping venueVersionMapping =
        notificationUtil.mockVenueUpgradeVersionMapping(1, versions.toArray(ApVersion[]::new));
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendCreateScheduleNotifications(
        tenant, List.of(venue), venueVersionMapping, randomTxId());

    // Then;
    verify(notificationUpdateFirmwareHandler)
        .send(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient).send(any(), anyString(), anyString(), anyString());
    List<ScheduleTemplateDto> scheduleTemplateDtos =
        (List<ScheduleTemplateDto>) messageArgumentCaptor.getValue().getModel().get("scheduleList");
    verifyImpactApNumber(scheduleTemplateDtos, dto2);
  }

  @Test
  public void testSendGaFirmwareRelease(Tenant tenant) {
    tenant.setName("TenantName");
    ApVersion apVersion = TenantFirmwareNotificationUtilTest.mockApVersion("7.0.0.103.100");
    TenantFirmwareReleaseType releaseType = TenantFirmwareReleaseType.GA;
    String requestId = "request-id";
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendRemindReleaseNotification(tenant,
        apVersion, releaseType, requestId);

    verify(notificationUpdateFirmwareHandler)
        .sendWithoutCriteria(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.RELEASE_GA, messageArgumentCaptor.getValue().getTemplate());
    assertThat((String) messageArgumentCaptor.getValue().getModel().get("version_number"))
        .isEqualTo("7.0.0.103.100");

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendGaFirmwareReleaseSMS(Tenant tenant) {
    tenant.setName("TenantName");
    ApVersion apVersion = TenantFirmwareNotificationUtilTest.mockApVersion("7.0.0.103.100");
    TenantFirmwareReleaseType releaseType = TenantFirmwareReleaseType.GA;
    String requestId = "request-id";
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendRemindReleaseNotification(tenant,
        apVersion, releaseType, requestId);

    verify(notificationUpdateFirmwareHandler)
        .sendWithoutCriteria(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.RELEASE_GA, messageArgumentCaptor.getValue().getTemplate());

    String SMS = requestCaptor.getValue().getNotificationRequests(1).getMessageContent();
    Assertions.assertThat(SMS)
        .doesNotContain("${account}", "${region}");
    log.info("SMS message: ");
    log.info(SMS);
  }

  @Test
  public void testSendPatchFirmwareRelease(Tenant tenant) {
    tenant.setName("TenantName");
    ApVersion apVersion = TenantFirmwareNotificationUtilTest.mockApVersion("7.0.0.103.101");
    TenantFirmwareReleaseType releaseType = TenantFirmwareReleaseType.PATCH;
    String requestId = "request-id";
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendRemindReleaseNotification(tenant,
        apVersion, releaseType, requestId);

    verify(notificationUpdateFirmwareHandler)
        .sendWithoutCriteria(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.RELEASE_PATCH, messageArgumentCaptor.getValue().getTemplate());
    assertThat((String) messageArgumentCaptor.getValue().getModel().get("version_number"))
        .isEqualTo("7.0.0.103.101");

    log.info("notification message: ");
    log.info(requestCaptor.getValue().getNotificationRequests(0).getMessageContent());
  }

  @Test
  public void testSendPatchFirmwareReleaseSMS(Tenant tenant) {
    tenant.setName("TenantName");
    ApVersion apVersion = TenantFirmwareNotificationUtilTest.mockApVersion("7.0.0.103.101");
    TenantFirmwareReleaseType releaseType = TenantFirmwareReleaseType.PATCH;
    String requestId = "request-id";
    when(notificationSenderFactory.getByTenantId(any()))
        .thenReturn(acxNewFirmwareNotificationSender);

    notificationUpdateFirmwareHandler.sendRemindReleaseNotification(tenant,
        apVersion, releaseType, requestId);

    verify(notificationUpdateFirmwareHandler)
        .sendWithoutCriteria(any(), messageArgumentCaptor.capture(), anyString(), any());
    verify(nuvoClient, times(1)).send(requestCaptor.capture(), anyString(), anyString(), anyString());
    assertEquals(UpdateNotificationTemplate.RELEASE_PATCH, messageArgumentCaptor.getValue().getTemplate());

    String SMS = requestCaptor.getValue().getNotificationRequests(1).getMessageContent();
    Assertions.assertThat(SMS)
        .doesNotContain("${account}", "${region}");
    log.info("SMS message: ");
    log.info(SMS);
  }

  private void verifyImpactApNumber(List<ScheduleTemplateDto> scheduleTemplateDtos, VenueTemplateDto expectedDto) {
    assertThat(scheduleTemplateDtos)
        .asList()
        .singleElement()
        .extracting(ScheduleTemplateDto.class::cast)
        .extracting(ScheduleTemplateDto::getVenueDtoList)
        .asList()
        .singleElement()
        .isEqualTo(expectedDto);
  }

  private void verifyVersionNumber(String templateVersion, String... apVersions) {
    assertThat(templateVersion)
        .contains(apVersions);
  }

  private VenueTemplateDto mockVenueTemplateDtoWithApNumber(int impactNumber, int totalNumber) {
    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName("venue-1");
    dto.setAddress("address-1");
    dto.setTimeZone("zone-1");
    dto.setImpactedNumberOfAps(impactNumber);
    dto.setNumberOfAps(totalNumber);
    return dto;
  }

  private VenueTemplateDto mockVenueTemplateDto() {
    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName("venue-1");
    dto.setAddress("address-1");
    dto.setTimeZone("zone-1");
    dto.setNumberOfAps(2);
    dto.setImpactedNumberOfAps(2);
    return dto;
  }

  private VenueTemplateDto mockVenueTemplateDtoWithoutAP() {
    VenueTemplateDto dto = new VenueTemplateDto();
    dto.setName("venue-1");
    dto.setAddress("address-1");
    dto.setTimeZone("zone-1");
    dto.setNumberOfAps(0);
    return dto;
  }

  private List<VenueTemplateDto> mockVenueTemplateDtosWithStatus(int number, VenueTemplateStatusEnum status) {
    VenueTemplateDto dto = mockVenueTemplateDto();
    dto.setStatus(status);
    List<VenueTemplateDto> dtos = new LinkedList<>();
    for(int i=0; i<number; i++) {
      dtos.add(dto);
    }
    return dtos;
  }

  private GetNotificationRecipientsResponse mockNotificationRecipientsWithUnsubscribes() {
    return GetNotificationRecipientsResponse.newBuilder()
        .addUnsubscribes(NotificationSubscriptionType.DEVICE_AP_FIRMWARE)
        .build();
  }

  @TestConfiguration
  @Import(ApBranchFamilyServiceRouterTestConfig.class)
  static class TestConfig {
    @Bean
    @ConditionalOnMissingBean
    public NotificationUpdateFirmwareHandler notificationUpdateFirmwareHandler(
        TenantClient tenantClient,
        NotificationMessageGetterFactory notificationMessageFactory,
        NotificationSenderFactory notificationSenderFactory,
        SimpleFirmwareNotificationMessageProvider simpleFirmwareNotificationMessageGetter,
        AggregateNotificationService aggregateNotificationService,
        VenueRepository venueRepository) {
      return new NotificationUpdateFirmwareHandler(
          tenantClient,
          notificationMessageFactory,
          notificationSenderFactory,
          simpleFirmwareNotificationMessageGetter,
          aggregateNotificationService,
          venueRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public AcxFirmwareNotificationMessageGetter acxFirmwareNotificationMessageGetter(
        VenueTemplateConverter converter,
        ApBranchFamilyServiceRouter apBranchFamilyService) {
      return new AcxFirmwareNotificationMessageGetter(
          converter,
          apBranchFamilyService);
    }

    @Bean
    @ConditionalOnMissingBean
    public AcxNewFirmwareNotificationMessageGetter acxNewFirmwareNotificationMessageGetter(
        VenueTemplateConverter converter, TenantClient tenantClient, FeatureFlagService featureFlagService) {
      return new AcxNewFirmwareNotificationMessageGetter(converter, tenantClient, featureFlagService);
    }

    @Bean
    @ConditionalOnMissingBean
    public TemplateManager templateManager() {
      return new TemplateManagerImpl();
    }

    @Bean
    @ConditionalOnMissingBean
    public AcxNewFirmwareNotificationSender acxNewFirmwareNotificationSender(
        NuvoClient nuvoClient,
        TemplateManager templateManager) {
      return new AcxNewFirmwareNotificationSender(
          nuvoClient, templateManager);
    }
  }
}
