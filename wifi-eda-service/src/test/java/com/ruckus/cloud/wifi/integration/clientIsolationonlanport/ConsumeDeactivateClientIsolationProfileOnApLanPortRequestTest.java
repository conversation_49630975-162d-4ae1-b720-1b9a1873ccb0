package com.ruckus.cloud.wifi.integration.clientIsolationonlanport;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.AP_ACTIVATIONS;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.VENUE_ACTIVATIONS;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice.ImpactDevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.CcmUserSidePort;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("EthernetPortProfileTest")
@FeatureFlag(
    enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
@WifiIntegrationTest
class ConsumeDeactivateClientIsolationProfileOnApLanPortRequestTest {

  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private LanPortAdoptionDataHelper dataHelper;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_AP_LAN_PORT)
  class ConsumeActivateClientIsolationProfileOnApLanPortAndChangeClientIsolationProfileTest {
    private String venueId;
    private String serialNumber;
    private final String portId = "1";
    private String apLanPortId;
    private String profileId;

    @BeforeEach
    void prepareData(final Venue venue, Ap ap, ClientIsolationAllowlist profile) {
      venueId = venue.getId();
      serialNumber = ap.getId();
      profileId = profile.getId();
      var activation = Generators.clientIsolationLanPortActivation().generate();
      activation.setClientIsolationAllowlist(profile);

      var portData1 =
          dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 3, of(activation));
      var portData2 = dataHelper.createApLanPortDataWithAdoption(venue, ap, "2", 4);
      apLanPortId = portData1.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("clientIsolationProfileId", profileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      verifyResult(
          CfgAction.DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_AP_LAN_PORT,
          venueId,
          serialNumber,
          portId,
          profileId,
          apLanPortId);
    }
  }

  private void verifyResult(
      CfgAction apiAction,
      String venueId,
      String serialNumber,
      String portId,
      String profileId,
      String apLanPortId) {
    verifyDb(apLanPortId, profileId);
    verifyDdccm(apLanPortId, profileId);
    verifyCmnConfig(venueId, serialNumber, portId, profileId);
    verifyActivity(apiAction);
  }

  private void verifyDb(String apLanPortId, String profileId) {
    assertThat(apLanPortId).isNotNull();
    final var allAdoptions =
        repositoryUtil.findAll(LanPortAdoption.class, txCtxExtension.getTenantId());
    assertThat(allAdoptions).isNotNull().matches(l -> l.size() == 2);
    final var port = repositoryUtil.find(ApLanPort.class, apLanPortId);
    assertThat(port)
        .extracting(ApLanPort::getLanPortAdoption)
        .extracting(LanPortAdoption::getClientIsolationActivation)
        .extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
        .isNull();
  }

  private void verifyDdccm(String apLanPort, String profileId) {
    final var requestId = txCtxExtension.getRequestId();
    final var tenantId = txCtxExtension.getTenantId();
    final var ddccmMessages = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmMessages)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();
    final var port = repositoryUtil.find(ApLanPort.class, apLanPort);
    var ethernetPortProfileId = port.getLanPortAdoption().getEthernetPortProfileId();

    var operations =
        assertThat(ddccmMessages.getPayload())
            .extracting(WifiConfigRequest::getOperationsList)
            .asList()
            .isNotEmpty()
            .extracting(Operation.class::cast);
    operations
        .filteredOn(Operation::hasAp)
        .isNotEmpty()
        .allSatisfy(
            op ->
                assertThat(op.getAp().getModel().getLanPortList())
                    .anySatisfy(
                        lanPort ->
                            assertThat(lanPort.getApLanPortProfileId())
                                .isEqualTo(ethernetPortProfileId)));
    operations
        .filteredOn(Operation::hasApLanPortProfile)
        .filteredOn(operation -> operation.getAction() == Action.ADD)
        .isNotEmpty()
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .allSatisfy(op -> verifyAddAction(op, port));
    operations
        .filteredOn(operation -> operation.getAction() == Action.DELETE)
        .allSatisfy(this::verifyDeleteAction);
  }

  private void verifyAddAction(Operation operation, ApLanPort port) {
    var apLanPortProfileAssert = assertThat(operation).extracting(Operation::getApLanPortProfile);
    apLanPortProfileAssert
        .matches(
            apLanPortProfile ->
                port.getLanPortAdoption().getId().equals(apLanPortProfile.getName()))
        .matches(
            apLanPortProfile ->
                port.getLanPortAdoption()
                    .getEthernetPortProfileId()
                    .equals(apLanPortProfile.getId()))
        .matches(
            apLanPortProfile ->
                (port.getApLanPortProfile().getType().name() + "PORT")
                    .equals(apLanPortProfile.getLanPortType().name()))
        .matches(
            apLanPortProfile ->
                port.getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(
            apLanPortProfile ->
                port.getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagId()));

    apLanPortProfileAssert.extracting(ApLanPortProfile::getClientIsolation).isNotNull();

    apLanPortProfileAssert
        .extracting(ApLanPortProfile::getClientIsolation)
        .matches(
            ccmWiredClientIsolation ->
                ccmWiredClientIsolation.getClientIsolationWhitelistId().isEmpty());

    apLanPortProfileAssert
        .extracting(ApLanPortProfile::getUserSidePort)
        .isNotNull()
        .matches(CcmUserSidePort::getUserSidePortEnabled)
        .matches(ccmUserSidePort -> ccmUserSidePort.getUserSidePortMaxClient().getValue() == 32);
  }

  private void verifyDeleteAction(Operation operation) {
    assertThat(operation).extracting(Operation::getApLanPortProfile).isNotNull();
  }

  private void verifyActivity(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors
          .assertThat(
              kafkaTopicProvider.getActivityCfgChangeResp(),
              kafkaTopicProvider.getActivityImpacted())
          .doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(ConfigurationStatus::getEventDate)
                    .isNotNull());

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ImpactDevice::getDeviceIdsCount).isEqualTo(1);
  }

  private void verifyCmnConfig(
      String venueId, String serialNumber, String portId, String profileId) {
    if (profileId == null) {
      messageCaptors
          .getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final var cmnConfigMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    var operations =
        cmnConfigMessage.getPayload().getOperationsList().stream()
            .filter(
                op ->
                    EsConstants.Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME.equals(op.getIndex())
                        && op.getOpType() == OpType.MOD)
            .collect(Collectors.partitioningBy(op -> profileId.equals(op.getId())));

    assertThat(operations.get(true))
        .singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(
            docMap -> {
              assertThat(docMap.get(AP_ACTIVATIONS).getListValue().getValuesList()).isEmpty();
              assertThat(docMap.get(VENUE_ACTIVATIONS).getListValue().getValuesList()).isEmpty();
            });
    assertThat(operations.get(false)).isEmpty();
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_AP_LAN_PORT ->
          ApiFlowNames.DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_AP_LAN_PORT;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
