package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import com.ruckus.acx.ddccm.protobuf.wifi.FriendlyNameLanguageEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20VenueGroupEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20VenueProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.Objects;

import static org.assertj.core.api.Assertions.assertThat;

@WifiUnitTest
class DdccmHotspot20VenueProfileOperationBuilderTest {

  @SpyBean
  private DdccmHotspot20VenueProfileOperationBuilder unit;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void testCreateHotspot20VenueProfileOperation_venueEmbedded() {
    var venue = Generators.venue().generate();

    var operation = unit.createHotspot20VenueProfileOperation(venue, EntityAction.ADD);
    assertThat(operation).isNotNull()
        .matches(op -> Objects.equals(venue.getId(), op.getId()))
        .extracting(Operation::getHotspot20VenueProfile).isNotNull()
        .matches(hvp -> Objects.equals(venue.getId(), hvp.getId()))
        .matches(hvp -> Objects.equals(venue.getId(), hvp.getName()))
        .matches(hvp -> Objects.equals(Hotspot20VenueGroupEnum.Hotspot20VenueGroupEnum_Unspecified, hvp.getVenueCategory().getVenueGroup()))
        .matches(Hotspot20VenueProfile::hasWanMetrics)
        .matches(hvp -> Objects.equals(1, hvp.getFriendlyNameCount()))
        .matches(hvp -> Objects.equals(FriendlyNameLanguageEnum.FriendlyNameLanguageEnum_ENG, hvp.getFriendlyName(0).getLanguage()))
        .matches(hvp -> Objects.equals(venue.getName(), hvp.getFriendlyName(0).getName()))
        .matches(hvp -> Objects.equals(0, hvp.getFriendlyName(0).getVenueUrlCount()));
  }

}
