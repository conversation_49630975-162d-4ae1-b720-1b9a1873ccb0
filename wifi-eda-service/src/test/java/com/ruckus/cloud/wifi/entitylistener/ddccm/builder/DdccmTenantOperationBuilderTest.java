package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackage;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.redis.YamlPropertySourceFactory;
import com.ruckus.cloud.wifi.service.SignaturePackageService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.PropertySource;

@WifiUnitTest
@PropertySource(value = "classpath:application-unit.yml", factory = YamlPropertySourceFactory.class)
public class DdccmTenantOperationBuilderTest {

  @MockBean
  private DdccmNetworkVenueOperationBuilder ddccmNetworkVenueOperationBuilder;

  @MockBean
  private DdccmPredefinedAaaOperationBuilder ddccmPredefinedAaaOperationBuilder;

  @MockBean
  private DdccmVenueOperationBuilder ddccmVenueOperationBuilder;

  @SpyBean
  private DdccmTenantOperationBuilder ddccmTenantOperationBuilder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  public void testTenantBasicSetting() {
    // Given
    var tenant = new Tenant("TestNewCreateTenant");
    when(ddccmPredefinedAaaOperationBuilder.handlePredefinedAaa(any(), any(), any(), any()))
        .thenAnswer(i -> i.getArguments()[3]);

    // When
    var operations = ddccmTenantOperationBuilder.build(
        new NewTxEntity<>(tenant), emptyTxChanges());

    // Then
    // Should only contains Tenant & SignaturePacakge
    assertEquals(2, operations.size());
    assertEquals(1, operations.stream().filter(o -> o.hasTenant()).count());
    assertEquals(1, operations.stream().filter(o -> o.hasCcmQmSignaturePackageAcx()).count());

    var sp = operations.stream()
        .filter(o -> o.hasCcmQmSignaturePackageAcx())
        .findFirst().get().getCcmQmSignaturePackageAcx();
    assertEquals(DdccmTenantOperationBuilder.QM_SIGNATURE_PACKAGE_DEFAULT_ID, sp.getId());
    assertEquals(DdccmTenantOperationBuilder.QM_SIGNATURE_PACKAGE_FIRST_VERSION, sp.getVersion());
    assertEquals("", sp.getArmOwrtDownloadHost());
    assertEquals("", sp.getArmOwrtDownloadPath());
    assertEquals("", sp.getArmOwrtRegularDownloadHost());
    assertEquals("", sp.getArmOwrtRegularDownloadPath());
    var hosts = new HashSet<>(List.of(
        sp.getArmDownloadHost(),
        sp.getMipsDownloadHost(),
        sp.getPpcDownloadHost(),
        sp.getArmRegularDownloadHost()
    ));
    assertEquals(1, hosts.size());
    assertEquals("device.dev.ruckus.cloud", hosts.iterator().next());
    var paths = new HashSet<>(List.of(
        sp.getArmDownloadPath(),
        sp.getMipsDownloadPath(),
        sp.getPpcDownloadPath(),
        sp.getArmRegularDownloadPath()
    ));
    assertEquals(4, paths.size());
  }

  @Test
  public void testTenantBasicSettingWithOwrtVersionSignaturePackage() {
    // Given
    var tenant = new Tenant("TestNewCreateTenant");
    tenant.setSignaturePackage(new SignaturePackage(SignaturePackageService.MINIMUM_OWRT_VERSION));
    when(ddccmPredefinedAaaOperationBuilder.handlePredefinedAaa(any(), any(), any(), any()))
        .thenAnswer(i -> i.getArguments()[3]);

    // When
    var operations = ddccmTenantOperationBuilder.build(
        new NewTxEntity<>(tenant), emptyTxChanges());

    // Then
    // Should only contains Tenant & SignaturePacakge
    assertEquals(2, operations.size());
    assertEquals(1, operations.stream().filter(o -> o.hasTenant()).count());
    assertEquals(1, operations.stream().filter(o -> o.hasCcmQmSignaturePackageAcx()).count());

    var sp = operations.stream()
        .filter(o -> o.hasCcmQmSignaturePackageAcx())
        .findFirst().get().getCcmQmSignaturePackageAcx();
    assertEquals(DdccmTenantOperationBuilder.QM_SIGNATURE_PACKAGE_DEFAULT_ID, sp.getId());
    assertEquals(SignaturePackageService.MINIMUM_OWRT_VERSION, sp.getVersion());
    var hosts = new HashSet<>(List.of(
        sp.getArmDownloadHost(),
        sp.getMipsDownloadHost(),
        sp.getPpcDownloadHost(),
        sp.getArmRegularDownloadHost(),
        sp.getArmOwrtDownloadHost(),
        sp.getArmOwrtRegularDownloadHost()
    ));
    assertEquals(1, hosts.size());
    assertEquals("device.dev.ruckus.cloud", hosts.iterator().next());
    var paths = new HashSet<>(List.of(
        sp.getArmDownloadPath(),
        sp.getMipsDownloadPath(),
        sp.getPpcDownloadPath(),
        sp.getArmRegularDownloadPath(),
        sp.getArmOwrtDownloadPath(),
        sp.getArmOwrtRegularDownloadPath()
    ));
    assertEquals(6, paths.size());
  }

  @Test
  public void testTenantBasicSettingWithMtkVersionSignaturePackage() {
    // Given
    var tenant = new Tenant("TestNewCreateTenant");
    tenant.setSignaturePackage(new SignaturePackage(SignaturePackageService.MINIMUM_MTK_VERSION));
    when(ddccmPredefinedAaaOperationBuilder.handlePredefinedAaa(any(), any(), any(), any()))
        .thenAnswer(i -> i.getArguments()[3]);

    // When
    var operations = ddccmTenantOperationBuilder.build(
        new NewTxEntity<>(tenant), emptyTxChanges());

    // Then
    // Should only contains Tenant & SignaturePacakge
    assertEquals(2, operations.size());
    assertEquals(1, operations.stream().filter(o -> o.hasTenant()).count());
    assertEquals(1, operations.stream().filter(o -> o.hasCcmQmSignaturePackageAcx()).count());

    var sp = operations.stream()
        .filter(o -> o.hasCcmQmSignaturePackageAcx())
        .findFirst().get().getCcmQmSignaturePackageAcx();
    assertEquals(DdccmTenantOperationBuilder.QM_SIGNATURE_PACKAGE_DEFAULT_ID, sp.getId());
    assertEquals(SignaturePackageService.MINIMUM_MTK_VERSION, sp.getVersion());
    var hosts = new HashSet<>(List.of(
        sp.getArmDownloadHost(),
        sp.getMipsDownloadHost(),
        sp.getPpcDownloadHost(),
        sp.getArmRegularDownloadHost(),
        sp.getArmOwrtDownloadHost(),
        sp.getArmOwrtRegularDownloadHost(),
        sp.getArmMtkDownloadHost(),
        sp.getArmMtkRegularDownloadHost()
    ));
    assertEquals(1, hosts.size());
    assertEquals("device.dev.ruckus.cloud", hosts.iterator().next());
    var paths = new HashSet<>(List.of(
        sp.getArmDownloadPath(),
        sp.getMipsDownloadPath(),
        sp.getPpcDownloadPath(),
        sp.getArmRegularDownloadPath(),
        sp.getArmOwrtDownloadPath(),
        sp.getArmOwrtRegularDownloadPath(),
        sp.getArmMtkDownloadPath(),
        sp.getArmMtkRegularDownloadPath()
    ));
    assertEquals(8, paths.size());
  }

  @Test
  public void testTenantBasicSettingWithNonFirstSignaturePackage() {
    // Given
    var tenant = new Tenant("TestNewCreateTenant");
    var signaturePackage = new SignaturePackage("v2");
    tenant.setSignaturePackage(signaturePackage);
    when(ddccmPredefinedAaaOperationBuilder.handlePredefinedAaa(any(), any(), any(), any()))
        .thenAnswer(i -> i.getArguments()[3]);

    // When
    var operations = ddccmTenantOperationBuilder.build(
        new NewTxEntity<>(tenant), emptyTxChanges());

    // Then
    // Should only contains Tenant & SignaturePacakge
    assertEquals(2, operations.size());
    assertEquals(1, operations.stream().filter(o -> o.hasTenant()).count());
    assertEquals(1, operations.stream().filter(o -> o.hasCcmQmSignaturePackageAcx()).count());

    var sp = operations.stream()
        .filter(o -> o.hasCcmQmSignaturePackageAcx())
        .findFirst().get().getCcmQmSignaturePackageAcx();
    assertEquals(DdccmTenantOperationBuilder.QM_SIGNATURE_PACKAGE_DEFAULT_ID, sp.getId());
    assertEquals(signaturePackage.getId(), sp.getVersion());
    var hosts = new HashSet<>(
        List.of(sp.getArmDownloadHost(), sp.getMipsDownloadHost(), sp.getPpcDownloadHost(),
            sp.getArmRegularDownloadHost()));
    assertEquals(1, hosts.size());
    assertEquals("device.dev.ruckus.cloud", hosts.iterator().next());
    assertTrue(sp.getArmDownloadPath().contains("v2"));
  }

  @Test
  public void testReleaseSignaturePackageToTenant() {
    // Given
    TxChanges txChanges = spy(TxChanges.class);
    var tenant = new Tenant("TestReleaseSignaturePackageToTenant");
    var signaturePackage = new SignaturePackage("v2");
    tenant.setLatestSignaturePackage(signaturePackage);
    tenant.setLatestSignaturePackageReleasedDate(new Date());

    doReturn(Set.of("latestSignaturePackage", "latestSignaturePackageReleasedDate", "updatedDate"))
        .when(txChanges).getModifiedProperties(tenant);

    // When
    var operations = ddccmTenantOperationBuilder.build(
        new ModifiedTxEntity<>(tenant, Set.of()), txChanges);

    // Then
    // Should nothing
    assertEquals(0, operations.size());
  }
}
