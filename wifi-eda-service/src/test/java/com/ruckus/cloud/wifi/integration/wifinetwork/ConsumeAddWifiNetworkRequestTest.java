package com.ruckus.cloud.wifi.integration.wifinetwork;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.BoolValue;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractWlan;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CalledStationIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdDelimiterEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiWlan;
import com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiWlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.viewmodel.DnsProxy;
import com.ruckus.cloud.wifi.eda.viewmodel.DnsProxyRule;
import com.ruckus.cloud.wifi.eda.viewmodel.GuestWlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiNetwork;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiWlan;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenWifiWlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenWlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.PskWifiNetwork;
import com.ruckus.cloud.wifi.eda.viewmodel.PskWifiWlan;
import com.ruckus.cloud.wifi.eda.viewmodel.PskWifiWlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.viewmodel.PskWlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.WifiNetwork;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.integration.AbstractRadiusRequestTest;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.GuestPortal;
import com.ruckus.cloud.wifi.proto.Operation;
import com.ruckus.cloud.wifi.proto.SocialIdentities;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.HOST_APPROVAL_EMAIL;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_BSS_PRIORITY_TOGGLE)
class ConsumeAddWifiNetworkRequestTest extends AbstractRadiusRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  protected MessageCaptors messageCaptors;

  String getRequestJson(String file) throws IOException {
    return IOUtils.toString(
        Objects.requireNonNull(this.getClass().getResourceAsStream(file)), StandardCharsets.UTF_8);
  }

  @Nested
  class whenConsumeAddNetworkRequest {

    @Test
    void thenSaveNetwork(Tenant tenant) {
      final var requestId = randomTxId();
      final var userName = randomName();
      // wifi-api prepares id in ADD NETWORK case
      final var id = randomId();
      final var network = getOpenWifiNetwork(id);

      messageUtil.sendWifiCfgRequest(tenant.getId(), requestId, CfgAction.ADD_WIFI_NETWORK, userName,
          network);

      validateResult(network);
    }

    @Test
    void thenCreateAaaWifiNetwork(Tenant tenant) throws IOException {

      var userName = "userName01";

      String json = IOUtils.toString(
          Objects.requireNonNull(this.getClass().getResourceAsStream(
              "/requests/addAaaWifiNetwork.json")),
          StandardCharsets.UTF_8);

      var radius = RadiusTestFixture.authRadius();
      radius.setName("test-radius-auth");
      radius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
      var radius1Id = radius.getId();

      messageUtil.sendWifiCfgRequest(tenant.getId(), randomTxId(),
          CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius));

      com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork networkRequest;
      networkRequest = (com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork)
          objectMapper.readValue(json, new TypeReference<WifiNetwork>() {
          });
      networkRequest.setId(randomId());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ADD_WIFI_NETWORK,
          userName,
          new RequestParams(),
          networkRequest);

      final var network = repositoryUtil.find(
          AAANetwork.class, networkRequest.getId());

      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.AAA))
          .extracting(AAANetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(networkRequest.getWlan().getSsid()))
          .matches(wlan -> wlan.getVlanId().equals(networkRequest.getWlan().getVlanId()))
          .matches(wlan -> wlan.getAdvancedCustomization().getEnableAdditionalRegulatoryDomains().equals(
                  networkRequest.getWlan().getAdvancedCustomization().getEnableAdditionalRegulatoryDomains()));
    }

    @Test
    void thenCreateOpenWifiNetworkWithQosMapSet(Tenant tenant) throws IOException {
      final var id = randomId();
      final var networkRequest = getOpenWifiNetwork(id);
      networkRequest.getWlan().getAdvancedCustomization().setQosMapSetEnabled(true);

      var requestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgAction.ADD_WIFI_NETWORK,
          randomName(),
          new RequestParams(),
          networkRequest);

      // assert db
      final var network = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, networkRequest.getId());
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
          .extracting(n -> n.getWlan().getAdvancedCustomization().getQosMapSetOptions())
          .isNotNull()
          .matches(options -> options.getRules().size() == 8);

      // assert ddccm
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());

      // assert wifiCfgChange
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenant.getId(), requestId);
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
      assertThat(wifiConfigChange.getOperationList())
          .filteredOn(Operation::hasQosMapRule)
          .hasSize(8);
      assertThat(wifiConfigChange.getOperationList())
          .filteredOn(Operation::hasWlan)
          .first()
          .matches(o -> o.getWlan().getAdvancedCustomization().getQosMapSetEnabled().equals(BoolValue.of(true)));
    }

    @Test
    void thenCreatePskWifiNetworkWithDynamicVlan(Tenant tenant) throws IOException {
      final var id = randomId();
      final var networkRequest = getPskWifiNetwork(id);

      var requestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgAction.ADD_WIFI_NETWORK,
          randomName(),
          new RequestParams(),
          networkRequest);

      // assert db
      final var network = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork.class, networkRequest.getId());
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.PSK))
          .extracting(Network::getWlan)
          .extracting(AbstractWlan::getAdvancedCustomization)
          .extracting(WlanAdvancedCustomization::getEnableAaaVlanOverride)
          .isEqualTo(true);

      // assert ddccm
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());

      // assert wifiCfgChange
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenant.getId(), requestId);
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
      assertThat(wifiConfigChange.getOperationList())
          .filteredOn(Operation::hasWlan)
          .first()
          .extracting(Operation::getWlan)
          .extracting(com.ruckus.cloud.wifi.proto.Wlan::getAdvancedCustomization)
          .extracting(com.ruckus.cloud.wifi.proto.WlanAdvancedCustomization::getEnableAaaVlanOverride)
          .isEqualTo(BoolValue.of(true));
    }
  }

  com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork getAddDpskWifiNetworkRequest(
      String id, String name) throws IOException {

    String requestJson = getRequestJson("/requests/addDpskWifiNetwork.json");

    com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork addDpskRequest;
    addDpskRequest = (com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork)
        objectMapper.readValue(requestJson, new TypeReference<WifiNetwork>() {
        });
    addDpskRequest.setId(id);
    addDpskRequest.setName(name);
    addDpskRequest.getWlan().setSsid(name);
    addDpskRequest.setDpskPassphraseGeneration(null);

    return addDpskRequest;
  }

  @Nested
  class whenConsumeDPSKNetworkRequest {

    final String userName = randomName();

    @Test
    void thenDpskWifiNetworkRuckusOnePver(Tenant tenant) throws IOException {

      final String tenantId = tenant.getId();
      com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork addDpskRequest =
          getAddDpskWifiNetworkRequest(randomId(), "network00");

      final String addRequestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          addRequestId,
          CfgAction.ADD_WIFI_NETWORK,
          userName,
          new RequestParams(),
          addDpskRequest);

      final DpskNetwork addedDpsk = repositoryUtil.find(
          DpskNetwork.class, addDpskRequest.getId());

      assertThat(addedDpsk)
          .isNotNull()
          .matches(n -> n.getId().equals(addDpskRequest.getId()), "match id")
          .matches(n -> n.getName().equals(addDpskRequest.getName()), "match name")
          .matches(n -> n.getType().equals(NetworkTypeEnum.DPSK), "match DPSK")
          .extracting(DpskNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(addDpskRequest.getWlan().getSsid()))
          .matches(wlan -> wlan.getVlanId().equals(addDpskRequest.getWlan().getVlanId()));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, addRequestId);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(addRequestId))
          .matches(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK.equals(o.getIndex())).count() == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o ->
              EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.ADD)
          .matches(p -> p.getId().equals(addedDpsk.getId()));

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(tenantId, addRequestId);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, addRequestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> {
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
            assertThat(msg.getDeviceIdsCount()).isZero();
          });

      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(tenantId);

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, addRequestId);
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, addRequestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(ApiFlowNames.ADD_WIFI_NETWORK);
          })
          .extracting(ConfigurationStatus::getEventDate).isNotNull();
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(tenantId);

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.ADD_WIFI_NETWORK))
          .matches(p -> p.getEventDate() != null);

      assertCfgChangeDpskNetworkIsTemplate(tenantId, addRequestId, false);

      //Update
      String updateReqJson = getRequestJson("/requests/updateDpskNetwork.json");

      com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork updateDpskRequest;
      updateDpskRequest = (com.ruckus.cloud.wifi.eda.viewmodel.DpskWifiNetwork)
          objectMapper.readValue(updateReqJson, new TypeReference<WifiNetwork>() {
          });

      updateDpskRequest.setId(addedDpsk.getId());
      updateDpskRequest.setDescription("updated DPSK description!");
      updateDpskRequest.setDpskPassphraseGeneration(null);
      final String updateRequestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          updateRequestId,
          CfgAction.UPDATE_WIFI_NETWORK,
          userName,
          new RequestParams().addPathVariable("wifiNetworkId", updateDpskRequest.getId()),
          updateDpskRequest);

      final DpskNetwork updatedDpsk = repositoryUtil.find(
          DpskNetwork.class, addDpskRequest.getId());

      assertThat(updatedDpsk)
          .isNotNull()
          .matches(n -> n.getId().equals(updateDpskRequest.getId()), "match id")
          .matches(n -> n.getName().equals(updateDpskRequest.getName()), "match name")
          .matches(n -> n.getType().equals(NetworkTypeEnum.DPSK), "match DPSK")
          .matches(n -> n.getDescription().equals(updateDpskRequest.getDescription()),
              "match Description")
          .extracting(DpskNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(updateDpskRequest.getWlan().getSsid()))
          .matches(wlan -> wlan.getVlanId().equals(updateDpskRequest.getWlan().getVlanId()));

      assertCfgChangeDpskNetworkIsTemplate(tenantId, updateRequestId, false);

      //Delete
      final String deleteRequestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          deleteRequestId,
          CfgAction.DELETE_WIFI_NETWORK,
          userName,
          new RequestParams().addPathVariable("wifiNetworkId", updateDpskRequest.getId()),
          StringUtils.EMPTY);

      final DpskNetwork deletedDpsk = repositoryUtil.find(
          DpskNetwork.class, addDpskRequest.getId());

      assertThat(deletedDpsk).isNull();

      assertCfgChangeDpskNetworkIsTemplate(tenantId, deleteRequestId, false);
    }
  }

  private void assertCfgChangeDpskNetworkIsTemplate(String tenantId, String addRequestId, boolean isTemplate)
      throws InvalidProtocolBufferException {
    final var wifiCfgChangeMessage =
        messageCaptors.getWifiCfgChangeMessageCaptor()
            .getValue(tenantId, addRequestId);
    WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
        wifiCfgChangeMessage.getPayload().toByteArray());
    assertThat(wifiConfigChange)
        .extracting(p -> p.getOperationList().stream()
            .filter(Operation::hasDpskNetwork)
            .findFirst().orElseThrow().getDpskNetwork())
        .matches(dpsk -> dpsk.hasIsTemplate())
        .matches(dpsk -> dpsk.getIsTemplate().getValue() == isTemplate);
  }

  @Nested
  class ConsumeAddGuestWifiNetworkRequestTest {

    @Payload("Clickthrough")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadClickthrough() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.ClickThrough,
          n -> {
          });
      return networkRequest;
    }

    @Payload("SelfSignIn")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadSelfSignIn() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.SelfSignIn,
          n -> {
          });
      return networkRequest;
    }

    @Payload("HostApproval")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadHostApproval() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
          });
      return networkRequest;
    }

    @Payload("HostApprovalWithEmail")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadHostApprovalWithEmail() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
            n.getGuestPortal().getHostGuestConfig().setHostEmails(List.of(HOST_APPROVAL_EMAIL));
          });
      return networkRequest;
    }

    @Payload("GuestPass")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadGuestPass() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.GuestPass,
          n -> {
            n.getWlan().setWlanSecurity(GuestWlanSecurityEnum.OWE);
          });
      return networkRequest;
    }

    @Payload("Directory")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadDirectory() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.Directory,
          n -> {
            n.getWlan().setWlanSecurity(GuestWlanSecurityEnum.OWE);
          });
      return networkRequest;
    }

    private String wifiNetworkId;
    private String networkName;
    private String ssid;
    private Short vlanId;

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_NETWORK, payload = @Payload("Clickthrough"))
    void thenCreateGuestWifiNetworkClickthrough(TxCtx txCtx,
        @Payload("Clickthrough") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork networkRequest)
        throws InvalidProtocolBufferException {
      wifiNetworkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));

      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> gp.getGuestNetworkType().equals(
              com.ruckus.cloud.wifi.proto.GuestNetworkTypeEnum.GuestNetworkTypeEnum_ClickThrough))
          .matches(gp -> gp.getExternalPortalUrl()
              .equals(StringValue.of(networkRequest.getGuestPortal().getExternalPortalUrl())))
          .matches(gp -> gp.getRedirectUrl()
              .equals(StringValue.of(networkRequest.getGuestPortal().getRedirectUrl())));

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasWlan()).findFirst().get()
                  .getWlan())
          .isNotNull()
          .matches(
              w -> w.getSsid().equals(StringValue.of(networkRequest.getWlan().getSsid())));

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);

      assertThat(validGuestWifiNetworkAndGet())
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(portal -> portal.getGuestNetworkType().equals(GuestNetworkTypeEnum.ClickThrough))
          .matches(portal -> portal.getGuestPage().getLangCode().equals("en"))
          .matches(portal -> portal.getEnableSmsLogin().equals(Boolean.FALSE));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_NETWORK, payload = @Payload("GuestPass"))
    void thenCreateGuestWifiNetworkGuestPassOwe(TxCtx txCtx,
        @Payload("GuestPass") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork networkRequest)
        throws InvalidProtocolBufferException {
      wifiNetworkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));

      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(p -> p.getOperationList().stream().filter(o -> o.hasWlan()).findFirst().get().getWlan())
          .isNotNull()
          .matches(w -> w.getSsid().equals(StringValue.of(networkRequest.getWlan().getSsid())))
          .matches(w -> w.getWlanSecurity().name().contains(GuestWlanSecurityEnum.OWE.name()));

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_NETWORK, payload = @Payload("GuestPass"))
    void thenCreateGuestWifiNetworkGuestPass(TxCtx txCtx,
        @Payload("GuestPass") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork networkRequest)
        throws InvalidProtocolBufferException {
      wifiNetworkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));

      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> gp.getGuestNetworkType().equals(
              com.ruckus.cloud.wifi.proto.GuestNetworkTypeEnum.GuestNetworkTypeEnum_GuestPass))
          .matches(gp -> gp.getExternalPortalUrl()
              .equals(StringValue.of(networkRequest.getGuestPortal().getExternalPortalUrl())))
          .matches(gp -> gp.getRedirectUrl()
              .equals(StringValue.of(networkRequest.getGuestPortal().getRedirectUrl())))
          .extracting(GuestPortal::getSmsPasswordDuration)
          .isNotNull();

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);

      assertThat(validGuestWifiNetworkAndGet())
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(portal -> portal.getGuestNetworkType().equals(GuestNetworkTypeEnum.GuestPass));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_NETWORK, payload = @Payload("HostApproval"))
    void thenCreateGuestWifiNetworkHostApproval(TxCtx txCtx,
        @Payload("HostApproval") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork networkRequest)
        throws InvalidProtocolBufferException {
      wifiNetworkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));
      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> gp.getGuestNetworkType().equals(
              com.ruckus.cloud.wifi.proto.GuestNetworkTypeEnum.GuestNetworkTypeEnum_HostApproval))
          .extracting(GuestPortal::getHostGuestConfig)
          .isNotNull()
          .matches(config -> config.getHostDurationChoicesCount() > 0)
          .matches(config -> config.getHostDomainsCount() > 0);

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);

      assertThat(validGuestWifiNetworkAndGet())
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(portal -> portal.getGuestNetworkType().equals(GuestNetworkTypeEnum.HostApproval))
          .matches(portal -> portal.getHostGuestConfig().getHostDomains().get(0)
              .equals(
                  networkRequest.getGuestPortal().getHostGuestConfig().getHostDomains().get(0)));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_NETWORK, payload = @Payload("HostApprovalWithEmail"))
    void thenCreateGuestWifiNetworkHostApprovalWithEmail(TxCtx txCtx,
        @Payload("HostApprovalWithEmail") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork networkRequest) {
      wifiNetworkId = networkRequest.getId();

      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, wifiNetworkId);
      assertThat(network)
          .isNotNull()
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal::getHostGuestConfig)
          .isNotNull()
          .matches(hostGuestConfig -> hostGuestConfig.getHostEmails()
              .equals(List.of(HOST_APPROVAL_EMAIL)));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_NETWORK, payload = @Payload("SelfSignIn"))
    void thenCreateGuestWifiNetworkSelfSignIn(TxCtx txCtx,
        @Payload("SelfSignIn") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork networkRequest)
        throws InvalidProtocolBufferException {
      wifiNetworkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));

      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> gp.getGuestNetworkType().equals(
              com.ruckus.cloud.wifi.proto.GuestNetworkTypeEnum.GuestNetworkTypeEnum_SelfSignIn))
          .extracting(GuestPortal::getSocialIdentities)
          .isNotNull()
          .extracting(SocialIdentities::getFacebook)
          .isNotNull();

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);

      assertThat(validGuestWifiNetworkAndGet())
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(portal -> portal.getGuestNetworkType().equals(GuestNetworkTypeEnum.SelfSignIn))
          .matches(portal -> Boolean.TRUE.equals(portal.getEnableEmailLogin()))
          .matches(portal -> Boolean.TRUE.equals(portal.getEnableWhatsappLogin()))
          .matches(portal -> portal.getSocialIdentities().getFacebook().getSource().equals(
              networkRequest.getGuestPortal().getSocialIdentities().getFacebook().getSource()));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_NETWORK, payload = @Payload("Directory"))
    void thenCreateGuestWifiNetworkDirectory(TxCtx txCtx,
        @Payload("Directory") com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork networkRequest)
        throws InvalidProtocolBufferException {
      wifiNetworkId = networkRequest.getId();
      networkName = networkRequest.getName();
      ssid = networkRequest.getWlan().getSsid();
      vlanId = networkRequest.getWlan().getVlanId();

      WifiConfigChange wifiConfigChange = WifiConfigChange.parseFrom(
          validateWifiCfgChangeMessageAndGetValue(txCtx));

      validateWifiConfigChangeAndNetwork(wifiConfigChange, txCtx);

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> gp.getGuestNetworkType().equals(
              com.ruckus.cloud.wifi.proto.GuestNetworkTypeEnum.GuestNetworkTypeEnum_Directory));

      validateCmnCfgCollectorMessage(txCtx);
      validateActivityImpactedMessage(txCtx);
      validateActivityMessages(txCtx);

      assertThat(validGuestWifiNetworkAndGet())
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .matches(portal -> portal.getGuestNetworkType().equals(GuestNetworkTypeEnum.Directory));
    }

    private byte[] validateWifiCfgChangeMessageAndGetValue(TxCtx txCtx) {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
              .getValue(txCtx);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());
      assertThat(wifiCfgChangeMessageRecord.getPayload()).isNotNull();
      return wifiCfgChangeMessageRecord.getPayload().toByteArray();
    }

    private void validateWifiConfigChangeAndNetwork(WifiConfigChange wifiConfigChange,
        TxCtx txCtx) {
      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestNetwork()).findFirst().get()
                  .getGuestNetwork())
          .matches(n -> n.getId().equals(StringValue.of(wifiNetworkId)))
          .matches(n -> n.getName().equals(StringValue.of(networkName)));
    }

    private void validateCmnCfgCollectorMessage(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(txCtx);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.ADD)
          .matches(p -> p.getId().equals(wifiNetworkId));
    }

    private void validateActivityImpactedMessage(TxCtx txCtx) {
      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());

      assertThat(activityImpactedMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
    }

    private void validateActivityMessages(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.ADD_WIFI_NETWORK))
          .extracting(ConfigurationStatus::getEventDate)
          .isNotNull();
    }

    private GuestNetwork validGuestWifiNetworkAndGet() {
      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, wifiNetworkId);
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(wifiNetworkId))
          .matches(n -> n.getName().equals(networkName))
          .matches(n -> n.getType().equals(NetworkTypeEnum.GUEST))
          .extracting(GuestNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(ssid))
          .matches(wlan -> wlan.getVlanId().equals(vlanId));
      return network;
    }
  }

  @Nested
  class ConsumeAddNetworkWithRadiusOptionsTest {

    @Test
    void thenCreateAaaWifiNetworkWithOldPayload(Tenant tenant) throws IOException {

      com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork networkRequest = getAAAWifiNetwork(tenant,
          randomId());
      var advancedCustomization = networkRequest.getWlan().getAdvancedCustomization();
      assertThat(advancedCustomization.getRadiusOptions())
          .isNotNull()
          .matches(r -> !r.getSingleSessionIdAccounting())
          .extracting(com.ruckus.cloud.wifi.eda.viewmodel.RadiusOptions::getCalledStationIdType)
          .isNull();

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ADD_WIFI_NETWORK,
          randomName(),
          new RequestParams(),
          networkRequest);

      final var network = repositoryUtil.find(
          AAANetwork.class, networkRequest.getId());

      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.AAA))
          .extracting(AAANetwork::getWlan)
          .isNotNull()
          .extracting(Wlan::getAdvancedCustomization)
          .extracting(WlanAdvancedCustomization::getRadiusOptions)
          .isNotNull()
          .matches(r -> Objects.equals(r.getCalledStationIdType(), CalledStationIdTypeEnum.BSSID),
              "RadiusOptions.calledStationIdType")
          .matches(r -> Objects.equals(r.getNasIdType(), NasIdTypeEnum.BSSID),
          "RadiusOptions.nasIdType")
          .matches(r -> Objects.equals(r.getNasMaxRetry(), 2),
          "RadiusOptions.nasMaxRetry")
          .matches(r -> Objects.equals(r.getNasReconnectPrimaryMin(), 5),
          "RadiusOptions.nasReconnectPrimaryMin")
          .matches(r -> Objects.equals(r.getNasRequestTimeoutSec(), 3),
          "RadiusOptions.nasRequestTimeoutSec")
          .matches(r -> Objects.equals(r.getNasIdDelimiter(), NasIdDelimiterEnum.DASH),
          "RadiusOptions.nasIdDelimiter")
          .matches(r -> Objects.isNull(r.getUserDefinedNasId()),
          "RadiusOptions.userDefinedNasId");
    }

    @Test
    void thenCreateAaaWifiNetworkWithNewPayload(Tenant tenant) throws IOException {

      com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork networkRequest = getAAAWifiNetwork(tenant,
          randomId());
      com.ruckus.cloud.wifi.eda.viewmodel.RadiusOptions radiusOptions = new com.ruckus.cloud.wifi.eda.viewmodel.RadiusOptions();
      radiusOptions.setSingleSessionIdAccounting(true);
      radiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_GROUP);
      networkRequest.getWlan().getAdvancedCustomization().setRadiusOptions(radiusOptions);

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ADD_WIFI_NETWORK,
          randomName(),
          new RequestParams(),
          networkRequest);

      final var network = repositoryUtil.find(
          AAANetwork.class, networkRequest.getId());

      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(networkRequest.getId()))
          .matches(n -> n.getName().equals(networkRequest.getName()))
          .matches(n -> n.getType().equals(NetworkTypeEnum.AAA))
          .extracting(AAANetwork::getWlan)
          .isNotNull()
          .extracting(Wlan::getAdvancedCustomization)
          .extracting(WlanAdvancedCustomization::getRadiusOptions)
          .isNotNull()
          .matches(RadiusOptions::getSingleSessionIdAccounting,
              "RadiusOptions.SingleSessionIdAccounting")
          .matches(r -> Objects.equals(r.getCalledStationIdType(), CalledStationIdTypeEnum.AP_GROUP),
              "RadiusOptions.calledStationIdType")
          .matches(r -> Objects.equals(r.getNasIdType(), NasIdTypeEnum.BSSID),
          "RadiusOptions.nasIdType")
          .matches(r -> Objects.equals(r.getNasMaxRetry(), 2),
              "RadiusOptions.nasMaxRetry")
          .matches(r -> Objects.equals(r.getNasReconnectPrimaryMin(), 5),
              "RadiusOptions.nasReconnectPrimaryMin")
          .matches(r -> Objects.equals(r.getNasRequestTimeoutSec(), 3),
              "RadiusOptions.nasRequestTimeoutSec")
          .matches(r -> Objects.equals(r.getNasIdDelimiter(), NasIdDelimiterEnum.DASH),
              "RadiusOptions.nasIdDelimiter")
          .matches(r -> Objects.isNull(r.getUserDefinedNasId()),
              "RadiusOptions.userDefinedNasId");
    }
  }

  OpenWifiNetwork getOpenWifiNetwork(String id) {
    var network = new OpenWifiNetwork();
    network.setId(id);
    network.setName("open3");
    network.setDescription(StringUtils.EMPTY);
    network.setWlan(new OpenWifiWlan());
    network.getWlan().setEnabled(true);
    network.getWlan().setSsid("open3");
    network.getWlan().setVlanId((short) 1);

    network.getWlan().setAdvancedCustomization(new OpenWifiWlanAdvancedCustomization());
    network.getWlan().getAdvancedCustomization().setDnsProxyEnabled(true);
    network.getWlan().getAdvancedCustomization().setDnsProxy(getDnsProxy());
    network.getWlan().setWlanSecurity(OpenWlanSecurityEnum.OWE);

    return network;
  }

  PskWifiNetwork getPskWifiNetwork(String id) {
    var network = new PskWifiNetwork();
    network.setId(id);
    network.setName(randomName());
    network.setDescription(StringUtils.EMPTY);
    network.setWlan(new PskWifiWlan());
    network.getWlan().setEnabled(true);
    network.getWlan().setSsid(randomName());
    network.getWlan().setVlanId((short) 1);

    network.getWlan().setAdvancedCustomization(new PskWifiWlanAdvancedCustomization());
    network.getWlan().getAdvancedCustomization().setDnsProxyEnabled(true);
    network.getWlan().getAdvancedCustomization().setDnsProxy(getDnsProxy());
    network.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);
    network.getWlan().setWlanSecurity(PskWlanSecurityEnum.WPA2Personal);
    network.getWlan().setMacAddressAuthentication(true);

    return network;
  }

  com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork getAAAWifiNetwork(Tenant tenant, String id) {
    com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork network = new com.ruckus.cloud.wifi.eda.viewmodel.AAAWifiNetwork();
    network.setId(id);
    network.setName(randomName());
    network.setDescription("");
    network.setWlan(new AAAWifiWlan());
    network.getWlan().setSsid(randomName());
    network.getWlan().setAdvancedCustomization(new AAAWifiWlanAdvancedCustomization());
    return network;
  }

  DnsProxy getDnsProxy() {
    DnsProxy dnsProxy = new DnsProxy();
    DnsProxyRule dnsProxyRule1 = new DnsProxyRule();
    dnsProxyRule1.setDomainName("rks.com");
    dnsProxyRule1.setIpList(List.of("**********"));
    dnsProxy.setDnsProxyRules(List.of(dnsProxyRule1));
    return dnsProxy;
  }

  void validateResult(OpenWifiNetwork networkRequest) {
    final var network = repositoryUtil.find(
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, networkRequest.getId());

    assertThat(network)
        .isNotNull()
        .matches(n -> n.getId().equals(networkRequest.getId()))
        .matches(n -> n.getName().equals(networkRequest.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .matches(n -> Objects.equals(n.getIsTemplate(), false))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork::getWlan)
        .isNotNull()
        .matches(wlan -> wlan.getSsid().equals(networkRequest.getWlan().getSsid()))
        .matches(wlan -> wlan.getVlanId().equals(networkRequest.getWlan().getVlanId()))
        .extracting(Wlan::getAdvancedCustomization)
        .isNotNull()
        .matches(wlanAdvancedCustomization -> wlanAdvancedCustomization.getDnsProxyEnabled()
            .equals(networkRequest.getWlan().getAdvancedCustomization().getDnsProxyEnabled()))
        .matches(
            wlanAdvancedCustomization -> wlanAdvancedCustomization.getDnsProxy().getDnsProxyRules()
                .get(0).getDomainName()
                .equals(networkRequest.getWlan().getAdvancedCustomization().getDnsProxy()
                    .getDnsProxyRules().get(0).getDomainName()))
        .matches(
            wlanAdvancedCustomization -> wlanAdvancedCustomization.getBssPriority()
                .equals(networkRequest.getWlan().getAdvancedCustomization().getBssPriority()));
  }
}
