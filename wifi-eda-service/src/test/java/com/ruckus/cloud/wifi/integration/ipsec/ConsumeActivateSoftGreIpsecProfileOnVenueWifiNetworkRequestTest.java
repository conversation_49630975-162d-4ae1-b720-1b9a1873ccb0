package com.ruckus.cloud.wifi.integration.ipsec;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.IpsecProfileTestFixture.randomIpsecProfile;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("IpsecProfileTest")
@WifiIntegrationTest
@FeatureFlag(enable = {
    FlagNames.WIFI_IPSEC_PSK_OVER_NETWORK})
public class ConsumeActivateSoftGreIpsecProfileOnVenueWifiNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void givenValidActivation(Tenant tenant, NetworkVenue networkVenue) {
    var ipsecProfile =
        repositoryUtil.createOrUpdate(
            randomIpsecProfile(tenant, p -> {
            }), tenant.getId());
    var softGreProfile =
        repositoryUtil.createOrUpdate(
            randomSoftGreProfile(tenant, p -> {
            }), tenant.getId());

    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        txCtxExtension.getRequestId(),
        CfgAction.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK,
        randomName(),
        new RequestParams()
            .addPathVariable("venueId", networkVenue.getVenue().getId())
            .addPathVariable("wifiNetworkId", networkVenue.getNetwork().getId())
            .addPathVariable("softGreProfileId", softGreProfile.getId())
            .addPathVariable("ipsecProfileId", ipsecProfile.getId()),
        "");

    var operationList = assertThat(
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .hasSize(2);
    operationList
        .filteredOn(o -> Index.SOFT_GRE_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .hasSize(1)
        .first()
        .matches(o -> o.getOpType() == OpType.MOD)
        .matches(o -> o.getId().equals(softGreProfile.getId()))
        .matches(
            o ->
                o.getDocMap().get(EsConstants.Key.ACTIVATIONS).getListValue().getValuesList().size()
                    == 1);
    operationList
        .filteredOn(o -> Index.IPSEC_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .hasSize(1)
        .first()
        .matches(o -> o.getOpType() == OpType.MOD)
        .matches(o -> o.getId().equals(ipsecProfile.getId()))
        .matches(
            o ->
                o.getDocMap().get(EsConstants.Key.ACTIVATIONS).getListValue().getValuesList().size()
                    == 1);

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
        .matches(
            a -> a.getStep()
                .equals(ApiFlowNames.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK));

    var operations =
        assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(
                m -> m.getPayload().getOperationsList(),
                InstanceOfAssertFactories.list(Operation.class));
    operations
        .filteredOn(
            o ->
                o.getAction() == Action.MODIFY
                    && o.getId().equals(networkVenue.getVenue().getId())
                    && o.hasVenue()
                    && o.getVenue().hasCcmMultipleTunnel())
        .hasSize(1)
        .extracting(o -> o.getVenue().getCcmMultipleTunnel())
        .first()
        .matches(tunnels -> !tunnels.getRksGreForwardBroadcast())
        .matches(tunnels -> tunnels.getSoftGreSettingCount() == 1)
        .matches(
            tunnels ->
                !tunnels.getSoftGreSetting(0).getAaaAffinityEnabled()
                    && softGreProfile.getId().equals(tunnels.getSoftGreSetting(0).getProfileId()))
        .matches(
            tunnels ->
                tunnels.getIpsecSettingCount() == 1 &&
                ipsecProfile.getId().equals(tunnels.getIpsecSetting(0).getProfileId()));

    operations
        .filteredOn(
            o ->
                o.getAction() == Action.MODIFY
                    && o.getId().equals(networkVenue.getId())
                    && o.hasWlanVenue())
        .hasSize(1)
        .extracting(Operation::getWlanVenue)
        .first()
        .matches(
            wlanVenue ->
                wlanVenue.getTunnelEnabled()
                    && softGreProfile.getId().equals(wlanVenue.getAccessTunnelProfileId()));
  }

  @Test
  void givenValidActivationWithNetworkVenueActivatedAlready(
      Tenant tenant, NetworkVenue networkVenue) {
    var softGreProfile =
        repositoryUtil.createOrUpdate(
            randomSoftGreProfile(tenant, p -> {
            }), tenant.getId());
    var profile1 =
        repositoryUtil.createOrUpdate(
            randomIpsecProfile(tenant, p -> {
            }), tenant.getId());
    var profile2 =
        repositoryUtil.createOrUpdate(
            randomIpsecProfile(tenant, p -> {
            }), tenant.getId());
    var activation = new SoftGreProfileNetworkVenueActivation();
    activation.setNetworkVenue(networkVenue);
    activation.setSoftGreProfile(softGreProfile);
    activation.setIpsecProfile(profile1);
    repositoryUtil.createOrUpdate(activation, tenant.getId());

    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        txCtxExtension.getRequestId(),
        CfgAction.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK,
        randomName(),
        new RequestParams()
            .addPathVariable("venueId", networkVenue.getVenue().getId())
            .addPathVariable("wifiNetworkId", networkVenue.getNetwork().getId())
            .addPathVariable("softGreProfileId", softGreProfile.getId())
            .addPathVariable("ipsecProfileId", profile2.getId()),
        "");

    var operationsList =
        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(3);
    operationsList
        .filteredOn(o -> o.getOpType() == OpType.MOD && o.getId().equals(profile1.getId()))
        .hasSize(1)
        .first()
        .matches(
            o ->
                o.getDocMap().get(EsConstants.Key.ACTIVATIONS).getListValue().getValuesList()
                    .isEmpty());
    operationsList
        .filteredOn(o -> o.getOpType() == OpType.MOD && o.getId().equals(profile2.getId()))
        .hasSize(1)
        .first()
        .matches(
            o ->
                o.getDocMap().get(EsConstants.Key.ACTIVATIONS).getListValue().getValuesList().size()
                    == 1);
    operationsList
        .filteredOn(o -> o.getOpType() == OpType.MOD && o.getId().equals(softGreProfile.getId()))
        .hasSize(1)
        .first()
        .matches(
            o ->
                o.getDocMap().get(EsConstants.Key.ACTIVATIONS).getListValue().getValuesList().size()
                    == 1);

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
        .matches(
            a -> a.getStep()
                .equals(ApiFlowNames.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK));

    var operations =
        assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(
                m -> m.getPayload().getOperationsList(),
                InstanceOfAssertFactories.list(Operation.class));
    operations
        .filteredOn(
            o ->
                o.getAction() == Action.MODIFY
                    && o.getId().equals(networkVenue.getVenue().getId())
                    && o.hasVenue()
                    && o.getVenue().hasCcmMultipleTunnel())
        .hasSize(1)
        .extracting(o -> o.getVenue().getCcmMultipleTunnel())
        .first()
        .matches(tunnels -> !tunnels.getRksGreForwardBroadcast())
        .matches(tunnels -> tunnels.getSoftGreSettingCount() == 1)
        .matches(
            tunnels ->
                !tunnels.getSoftGreSetting(0).getAaaAffinityEnabled()
                    && softGreProfile.getId().equals(tunnels.getSoftGreSetting(0).getProfileId()));
    operations
        .filteredOn(
            o ->
                o.getAction() == Action.MODIFY
                    && o.getId().equals(networkVenue.getId())
                    && o.hasWlanVenue())
        .hasSize(1)
        .extracting(Operation::getWlanVenue)
        .first()
        .matches(
            wlanVenue ->
                wlanVenue.getTunnelEnabled()
                    && softGreProfile.getId().equals(wlanVenue.getAccessTunnelProfileId()));
  }

  @Test
  void givenSoftGreProfilesAreNotUniqueInVenue(Tenant tenant, NetworkVenue networkVenue1, NetworkVenue networkVenue2) {
    var softGreProfile1 =
        repositoryUtil.createOrUpdate(
            randomSoftGreProfile(tenant, p -> {
            }), tenant.getId());
    var softGreProfile2 =
        repositoryUtil.createOrUpdate(
            randomSoftGreProfile(tenant, p -> {
            }), tenant.getId());
    var ipsecProfile =
        repositoryUtil.createOrUpdate(
            randomIpsecProfile(tenant, p -> {
            }), tenant.getId());
    var activation = new SoftGreProfileNetworkVenueActivation();
    activation.setNetworkVenue(networkVenue1);
    activation.setSoftGreProfile(softGreProfile1);
    activation.setIpsecProfile(ipsecProfile);
    repositoryUtil.createOrUpdate(activation, tenant.getId());

    assertThatThrownBy(
        () ->
            messageUtil.sendWifiCfgRequest(
                tenant.getId(),
                txCtxExtension.getRequestId(),
                CfgAction.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK,
                randomName(),
                new RequestParams()
                    .addPathVariable("venueId", networkVenue2.getVenue().getId())
                    .addPathVariable("wifiNetworkId", networkVenue2.getNetwork().getId())
                    .addPathVariable("softGreProfileId", softGreProfile2.getId())
                    .addPathVariable("ipsecProfileId", ipsecProfile.getId()),
                ""))
        .isNotNull()
        .rootCause()
        .isInstanceOf(InvalidPropertyValueException.class);

    messageCaptors
        .assertThat(
            messageCaptors.getDdccmMessageCaptor(),
            messageCaptors.getCmnCfgCollectorMessageCaptor())
        .doesNotSendByTenant(tenant.getId());

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
        .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_SOFT_GRE_IPSEC_PROFILE_ON_VENUE_WIFI_NETWORK));
  }
}
