package com.ruckus.cloud.wifi.integration.dhcpoption82onlanport;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice.ImpactDevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.CcmDhcpOption82Settings;
import com.ruckus.acx.ddccm.protobuf.wifi.DhcpOption82MacFormat;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.mapper.CustomMapper;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpOption82LanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.VenueApModelLanPortSettingsV1Generator;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelLanPortSettingsV1;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption150Enum;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption151Enum;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("EthernetPortProfileTest")
@FeatureFlag(
    enable = {
      FlagNames.ACX_UI_ETHERNET_TOGGLE,
      FlagNames.WIFI_ETHERNET_DHCP_OPTION_82_TOGGLE,
      FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
      FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE
    })
@WifiIntegrationTest
class ConsumeUpdateVenueApModelLanPortOverwriteSettingsForDhcpOption82RequestTest {

  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private LanPortAdoptionDataHelper dataHelper;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeEnableDhcpOption82OnVenueApModelLanPortRequestTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator =
        Generators.venueApModelLanPortSettingsV1()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(Generators.dhcpOption82Settings())
            .setEnabled(always(true));

    private String venueId;
    private String apModel = "R320";
    private String portId = "1";
    private String venueLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(final Venue venue) {
      venueId = venue.getId();
      var portData1 = dataHelper.createVenueLanPortDataWithAdoption(venue, apModel, portId, 3);
      venueLanPortId = portData1.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS, venueLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeUpdateDhcpOption82OnVenueApModelLanPortAndExistDhcpOption82SettingsRequestTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator =
        Generators.venueApModelLanPortSettingsV1()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(Generators.dhcpOption82Settings())
            .setEnabled(always(true));

    private String venueId;
    private String apModel = "R320";
    private String portId = "1";
    private String venueLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(final Venue venue) {

      DhcpOption82LanPortActivation dhcpOption82LanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .dhcpOption82LanPortActivation()
              .generate();

      venueId = venue.getId();
      var portData1 =
          dataHelper.createVenueLanPortDataWithAdoption(
              venue, apModel, portId, 3, of(dhcpOption82LanPortActivation));
      venueLanPortId = portData1.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS, venueLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeDisableDhcpOption82OnVenueApModelLanPortRequestTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator =
        Generators.venueApModelLanPortSettingsV1()
            .setDhcpOption82Enabled(always(false))
            .setEnabled(always(true));

    private String venueId;
    private String apModel = "R320";
    private String portId = "1";
    private String venueLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(final Venue venue) {

      DhcpOption82LanPortActivation dhcpOption82LanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .dhcpOption82LanPortActivation()
              .generate();

      venueId = venue.getId();
      var portData1 =
          dataHelper.createVenueLanPortDataWithAdoption(
              venue, apModel, portId, 3, of(dhcpOption82LanPortActivation));
      venueLanPortId = portData1.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS, venueLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeDisablePortOnVenueApModelLanPortRequestTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator =
        Generators.venueApModelLanPortSettingsV1()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(Generators.dhcpOption82Settings())
            .setEnabled(always(false));

    private String venueId;
    private String apModel = "R550";
    private String portId = "1";
    private String venueLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(final Venue venue) {
      DhcpOption82LanPortActivation dhcpOption82LanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .dhcpOption82LanPortActivation()
              .generate();

      venueId = venue.getId();
      var portData1 =
          dataHelper.createVenueLanPortDataWithAdoption(
              venue, apModel, portId, 3, of(dhcpOption82LanPortActivation));
      var portData2 = dataHelper.createVenueLanPortDataWithAdoption(venue, apModel, "2", 4);
      venueLanPortId = portData1.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {

      VenueApModelLanPortSettingsV1 expected = new VenueApModelLanPortSettingsV1();
      expected.setClientIsolationEnabled(false);
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS, venueLanPortId, expected);
    }
  }

  private void validateEthernetPortProfileToVenueApModelLanPortActivationResult(
      CfgAction apiAction,
      String venueLanPortId,
      VenueApModelLanPortSettingsV1 expectedVenueApModelLanPortSettings) {
    validateRepositoryData(venueLanPortId, expectedVenueApModelLanPortSettings);
    validateDdccmCfgRequestMessages(venueLanPortId, expectedVenueApModelLanPortSettings);
    validateCmnCfgCollectorMessages();
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(
      String venueLanPortId, VenueApModelLanPortSettingsV1 expectedVenueApModelLanPortSettings) {

    assertThat(venueLanPortId).isNotNull();

    final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
    if (expectedVenueApModelLanPortSettings.getDhcpOption82Enabled()) {
      var dhcpOption82Activation =
          assertThat(venueLanPort)
              .extracting(VenueLanPort::getLanPortAdoption)
              .extracting(LanPortAdoption::getDhcpOption82Activation)
              .isNotNull();
      var expectedSettings = expectedVenueApModelLanPortSettings.getDhcpOption82Settings();
      dhcpOption82Activation.matches(
          settings ->
              settings.getSubOption1Enabled().equals(expectedSettings.getSubOption1Enabled())
                  && settings.getSubOption2Enabled().equals(expectedSettings.getSubOption2Enabled())
                  && settings
                      .getSubOption150Enabled()
                      .equals(expectedSettings.getSubOption150Enabled())
                  && settings
                      .getSubOption151Enabled()
                      .equals(expectedSettings.getSubOption151Enabled())
                  && settings.getMacDelimiter()
                      == CustomMapper.INSTANCE.mapMacDelimiter(expectedSettings.getMacDelimiter())
                  && settings.getSubOption1Format()
                      == CustomMapper.INSTANCE.mapSubOption1Format(
                          expectedSettings.getSubOption1Format())
                  && settings
                      .getSubOption1Customization()
                      .equals(
                          CustomMapper.INSTANCE.dhcpOption82SubOption1CustomizationToJson(
                              expectedSettings.getSubOption1Customization()))
                  && settings.getSubOption2Format()
                      == CustomMapper.INSTANCE.mapSubOption2Format(
                          expectedSettings.getSubOption2Format())
                  && settings.getSubOption151Format()
                      == CustomMapper.INSTANCE.mapSubOption151Format(
                          expectedSettings.getSubOption151Format())
                  && settings.getSubOption151Text().equals(expectedSettings.getSubOption151Text()));

    } else {
      assertThat(venueLanPort)
          .extracting(VenueLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getDhcpOption82Activation)
          .isNull();
    }
  }

  private void validateDdccmCfgRequestMessages(
      String venueLanPortId, VenueApModelLanPortSettingsV1 expectedVenueApModelLanPortSettings) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();
    final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
    var inUseEthernetPortProfileId = venueLanPort.getLanPortAdoption().getEthernetPortProfileId();
    var operations =
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList)
            .asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast);
    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .allSatisfy(
            op -> {
              assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                  .matches(
                      venue ->
                          venue.getVenueApModelsList().stream()
                              .anyMatch(
                                  model ->
                                      model.getLanPortList().stream()
                                          .anyMatch(
                                              lanPort ->
                                                  inUseEthernetPortProfileId.equals(
                                                      lanPort.getApLanPortProfileId()))));
            });
    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApLanPortProfile)
        .filteredOn(operation -> operation.getAction() == Action.ADD)
        .isNotEmpty()
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .allSatisfy(op -> verifyAddAction(op, venueLanPort, expectedVenueApModelLanPortSettings));
    operations
        .filteredOn(operation -> operation.getAction() == Action.DELETE)
        .isNotEmpty()
        .allSatisfy(this::verifyDeleteAction);
  }

  private void verifyAddAction(
      com.ruckus.acx.ddccm.protobuf.wifi.Operation operation,
      VenueLanPort venueLanPort,
      VenueApModelLanPortSettingsV1 venueApModelLanPortSettings) {
    var lanPortProfileAssert =
        assertThat(operation)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile);

    lanPortProfileAssert
        .matches(
            apLanPortProfile ->
                venueLanPort.getLanPortAdoption().getId().equals(apLanPortProfile.getName()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getLanPortAdoption()
                    .getEthernetPortProfileId()
                    .equals(apLanPortProfile.getId()))
        .matches(
            apLanPortProfile ->
                (venueLanPort.getApLanPortProfile().getType().name() + "PORT")
                    .equals(apLanPortProfile.getLanPortType().name()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagId()));

    if (venueApModelLanPortSettings.getDhcpOption82Enabled()) {
      var dhcp = venueLanPort.getLanPortAdoption().getDhcpOption82Activation();
      String dhcpOption82OptionFormat =
          "1,string,ETH:$IFNAME$:$IFNAME$:$VLAN$:$SSID$:$MODEL$:$HOSTNAME$:$DEVMAC$:123456789012345678901234;"
              + dhcp.getSubOption2Format().getValue()
              + DhcpOption82SubOption150Enum.SUBOPT150_VLAN_ID.getValue()
              + String.format(
                  DhcpOption82SubOption151Enum.SUBOPT151_AREA_NAME.getValue(), "151Input");

      lanPortProfileAssert
          .extracting(ApLanPortProfile::getDhcpOption82Settings)
          .isNotNull()
          .matches(ccmDhcpOption82Settings -> ccmDhcpOption82Settings.getDhcpOption82() == 1)
          .matches(
              ccmDhcpOption82Settings ->
                  dhcpOption82OptionFormat.equals(ccmDhcpOption82Settings.getDhcpOption82Format()))
          .matches(
              ccmDhcpOption82Settings ->
                  ccmDhcpOption82Settings
                      .getDhcpOption82MacFormat()
                      .equals(DhcpOption82MacFormat.MAC_NODELIMITER));

    } else {
      lanPortProfileAssert
          .extracting(ApLanPortProfile::getDhcpOption82Settings)
          .matches(
              ccmDhcpOption82Settings ->
                  CcmDhcpOption82Settings.getDefaultInstance().equals(ccmDhcpOption82Settings));
    }
  }

  private void verifyDeleteAction(com.ruckus.acx.ddccm.protobuf.wifi.Operation operation) {
    assertThat(operation)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
        .isNotNull();
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors
          .assertThat(
              kafkaTopicProvider.getActivityCfgChangeResp(),
              kafkaTopicProvider.getActivityImpacted())
          .doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(ConfigurationStatus::getEventDate)
                    .isNotNull());

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull()
        .extracting(ImpactDevice::getDeviceIdsCount)
        .isEqualTo(0);
  }

  private void validateCmnCfgCollectorMessages() {
    final String tenantId = txCtxExtension.getTenantId();
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS ->
          ApiFlowNames.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
