package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.openNetwork;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.ClientIsolationTestFixture.clientIsolationAllowlist;
import static com.ruckus.cloud.wifi.test.fixture.ClientIsolationTestFixture.clientIsolationAllowlistEntry;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.util.AssertionErrors.assertEquals;

import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.service.ClientIsolationProfileServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueDhcpServiceSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpModeEnum;
import com.ruckus.cloud.wifi.entitylistener.ddccm.mapper.DhcpServiceProfileMergeImpl;
import com.ruckus.cloud.wifi.mapper.ClientIsolationAllowlistMerge;
import com.ruckus.cloud.wifi.mapper.ClientIsolationAllowlistMergeImpl;
import com.ruckus.cloud.wifi.mapper.ClientIsolationLanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.mapper.DhcpConfigServiceProfileMergeImpl;
import com.ruckus.cloud.wifi.mapper.DhcpOption82LanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.mapper.SoftGreProfileLanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.ClientIsolationAllowlistEntryRepository;
import com.ruckus.cloud.wifi.repository.ClientIsolationAllowlistRepository;
import com.ruckus.cloud.wifi.repository.DhcpPoolVenueRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.service.LanPortAdoptionService;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.service.impl.ClientIsolationAllowlistServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.ClientIsolationLanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.ClientIsolationProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.DhcpOption82LanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.ExtendedDhcpConfigServiceProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.ExtendedDhcpServiceProfileVenueServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.service.impl.SoftGreProfileLanPortActivationHandler;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedNetworkVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedTenantServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.FirmwareCapabilityServiceTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.InitVenueServiceImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.NetworkServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.config.MockGrpcBeanConfiguration;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@Slf4j
@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
class ClientIsolationAllowlistServiceTest extends AbstractServiceTest {

  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private LanPortAdoptionDataHelper dataHelper;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired private ClientIsolationProfileServiceCtrl clientIsolationProfileServiceCtrl;

  @Test
  void r1_testBasicCRUDClientIsolationAllowlist_Successfully(Tenant tenant) {
    // Initially clientIsolationAllowlist of a tenant has 0 element without default
    List<ClientIsolationAllowlist> existingClientIsolationAllowlists =
        getClientIsolationAllowlists();
    assertNotNull(existingClientIsolationAllowlists);
    assertEquals(
        "Initially clientIsolationAllowlists should have 0 element without default",
        0,
        existingClientIsolationAllowlists.size());

    // AddClientIsolationAllowlist
    String myId = randomId();
    ClientIsolationAllowlist clientIsolationAllowlistReq = clientIsolationAllowlist("al");
    clientIsolationAllowlistReq.setId(myId); // should not work
    final ClientIsolationAllowlist addedClientIsolationAllowlist =
        addClientIsolationAllowlist(clientIsolationAllowlistReq);
    assertNotNull(addedClientIsolationAllowlist);
    assertEquals("id can be set", myId, addedClientIsolationAllowlist.getId());
    assertEquals(
        "ClientIsolationAllowlist Name should be equal to requested profile name",
        clientIsolationAllowlistReq.getName(),
        addedClientIsolationAllowlist.getName());

    // GetClientIsolationAllowlist
    ClientIsolationAllowlist gotClientIsolationAllowlist =
        getClientIsolationAllowlist(addedClientIsolationAllowlist.getId());
    assertNotNull(gotClientIsolationAllowlist);
    assertEquals(
        "ClientIsolationAllowlist Id should be equal to added profile id",
        addedClientIsolationAllowlist.getId(),
        gotClientIsolationAllowlist.getId());

    // UpdateClientIsolationAllowlist
    clientIsolationAllowlistReq.setName("UpdatedIsolationAllowlistName");
    final ClientIsolationAllowlist updatedClientIsolationAllowlist =
        updateClientIsolationAllowlist(
            addedClientIsolationAllowlist.getId(), clientIsolationAllowlistReq);
    assertNotNull(updatedClientIsolationAllowlist);
    assertEquals(
        "ClientIsolationAllowlist Name should be equal to requested profile name",
        clientIsolationAllowlistReq.getName(),
        updatedClientIsolationAllowlist.getName());

    // DeleteClientIsolationAllowlist
    existingClientIsolationAllowlists = getClientIsolationAllowlists();
    assertEquals(
        "Before delete, clientIsolationAllowlist query should get 1 result",
        1,
        existingClientIsolationAllowlists.size());
    deleteClientIsolationAllowlist(addedClientIsolationAllowlist.getId());
    existingClientIsolationAllowlists = getClientIsolationAllowlists();
    assertEquals(
        "After delete, clientIsolationAllowlist query should get 0 result",
        0,
        existingClientIsolationAllowlists.size());

    final List<String> addedClientIsolationAllowlistIds = new ArrayList<>();
    for (int i = 1; i <= 3; ++i) {
      var temp = clientIsolationAllowlist("SequentialIsolationAllowlist-" + i);
      addedClientIsolationAllowlistIds.add(addClientIsolationAllowlist(temp).getId());
    }

    // DeleteClientIsolationAllowlists
    existingClientIsolationAllowlists = getClientIsolationAllowlists();
    assertEquals(
        "Before batch delete, clientIsolationAllowlist query should get 3 result",
        3,
        existingClientIsolationAllowlists.size());
    deleteClientIsolationAllowlists(addedClientIsolationAllowlistIds);
    existingClientIsolationAllowlists = getClientIsolationAllowlists();
    assertEquals(
        "After delete, clientIsolationAllowlist query should get 0 result",
        0,
        existingClientIsolationAllowlists.size());
  }

  @Test
  void r1_testAddClientIsolationAllowlist_DuplicateClients_ShouldBeDisallowed(Tenant tenant) {
    ClientIsolationAllowlist clientIsolationAllowlistReq = clientIsolationAllowlist("al");

    clientIsolationAllowlistReq.setAllowlist(
        List.of(clientIsolationAllowlistEntry(), clientIsolationAllowlistEntry()));

    assertThrows(
        CommonException.class,
        () -> {
          addClientIsolationAllowlist(clientIsolationAllowlistReq);
        });
  }

  @Test
  void r1_testUpdateNetworkVenueClientIsolationAllowlist_whenDhcpServiceEnabled_ShouldBeDisallowed(
      Tenant tenant) {
    // Given
    Venue v1 = createVenue(tenant, "v1");
    Venue v2 = createVenue(tenant, "v2");

    // AddClientIsolationAllowlist
    ClientIsolationAllowlist al = addClientIsolationAllowlist(clientIsolationAllowlist("al"));

    {
      OpenNetwork openNetwork = addOpenNetwork(map(openNetwork("open").generate()));

      // should not work
      applyVenue(openNetwork, v1);
      NetworkVenue nv1 = openNetwork.getNetworkVenues().get(0);
      nv1.setClientIsolationAllowlist(al);

      // should be successful
      updateNetwork(openNetwork.getId(), openNetwork);

      // addNetwork() should not add network-venue
      assertNull(getNetworkVenueByNetworkAndVenue(openNetwork.getId(), v1.getId()));

      // r1 needs to use adding network-venue
      addNetworkVenue(nv1);
      assertNotNull(getNetworkVenueByNetworkAndVenue(openNetwork.getId(), v1.getId()));
    }

    DhcpConfigServiceProfile dhcpConfigServiceProfile =
        map(
            DhcpConfigServiceProfileTestFixture.dhcpConfigServiceProfileDeep(
                3, 1, DhcpModeEnum.EnableOnEachAPs));
    dhcpConfigServiceProfile = addDhcpConfigServiceProfile(dhcpConfigServiceProfile);
    VenueDhcpServiceSetting venueDhcpServiceSetting = new VenueDhcpServiceSetting();
    venueDhcpServiceSetting.setEnabled(true);
    v2.setDhcpServiceSetting(venueDhcpServiceSetting);
    v2.setDhcpConfigServiceProfile(dhcpConfigServiceProfile);
    updateVenueDhcpConfigServiceProfileSetting(v2.getId(), v2);

    // try to add allowlist to v2
    PskNetwork pskNetwork = addPskNetwork(map(pskNetwork("psk").generate()));
    applyVenue(pskNetwork, v2);
    NetworkVenue nv2 = pskNetwork.getNetworkVenues().get(0);
    nv2.setClientIsolationAllowlist(al);

    CommonException exception =
        assertThrows(
            CommonException.class,
            () -> {
              addNetworkVenue(nv2);
            });
    Assertions.assertEquals(Errors.WIFI_10243, exception.getErrorCode());
  }

  @Nested
  @FeatureFlag(enable = {ACX_UI_ETHERNET_TOGGLE, WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
  class TestActivateClientIsolationProfileOnVenueApModelLanPort {

    private String venueId;
    private final String apModel = "R550";
    private final String portId = "1";
    private String venueLanPortId;
    private String clientIsolationProfileId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue, ClientIsolationAllowlist clientIsolationAllowlist) {
      venueId = venue.getId();
      clientIsolationProfileId = clientIsolationAllowlist.getId();

      ClientIsolationLanPortActivation clientIsolationLanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .clientIsolationLanPortActivation()
              .generate();

      var portData1 =
          dataHelper.createVenueLanPortDataWithAdoption(
              venue, apModel, portId, 3, of(clientIsolationLanPortActivation));
      venueLanPortId = portData1.port().getId();
    }

    @Test
    void activateClientIsolationProfileOnVenueApModelLanPort() throws Exception {

      clientIsolationProfileServiceCtrl.activateClientIsolationProfileOnVenueApModelLanPort(
          venueId, apModel, portId, clientIsolationProfileId);

      final VenueLanPort venueLanPort =
          repositoryUtil.findAndRefresh(VenueLanPort.class, venueLanPortId);
      assertThat(venueLanPort)
          .isNotNull()
          .extracting(VenueLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNotNull()
          .extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
          .matches(profile -> profile.getId().equals(clientIsolationProfileId));
    }
  }

  @Nested
  @FeatureFlag(enable = {ACX_UI_ETHERNET_TOGGLE, WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
  class TestDeactivateClientIsolationProfileOnVenueApModelLanPort {

    private String venueId;
    private final String apModel = "R550";
    private final String portId = "1";
    private String venueLanPortId;
    private String clientIsolationProfileId;

    @AfterEach
    void tearDown() {
      Mockito.reset();
    }

    @Test
    void deactivateClientIsolationProfileOnVenueApModelLanPort(
        final Venue venue, final ClientIsolationAllowlist clientIsolationAllowlist)
        throws Exception {
      ClientIsolationLanPortActivation clientIsolationLanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .clientIsolationLanPortActivation()
              .generate();
      clientIsolationLanPortActivation.setClientIsolationAllowlist(clientIsolationAllowlist);
      var portData1 =
          dataHelper.createVenueLanPortDataWithAdoption(
              venue, apModel, portId, 3, of(clientIsolationLanPortActivation));
      venueId = venue.getId();
      clientIsolationProfileId = clientIsolationAllowlist.getId();
      venueLanPortId = portData1.port().getId();

      clientIsolationProfileServiceCtrl.deactivateClientIsolationProfileOnVenueApModelLanPort(
          venueId, apModel, portId, clientIsolationProfileId);

      final VenueLanPort venueLanPort =
          repositoryUtil.findAndRefresh(VenueLanPort.class, venueLanPortId);

      assertThat(venueLanPort)
          .isNotNull()
          .extracting(VenueLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNotNull()
          .extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
          .isNull();
    }

    @Test
    void deactivateClientIsolationProfileOnVenueApModelLanPortButNotEnableClientIsolation(
        final Venue venue, final ClientIsolationAllowlist clientIsolationAllowlist)
        throws Exception {

      var portData1 = dataHelper.createVenueLanPortDataWithAdoption(venue, apModel, portId, 3);
      venueId = venue.getId();
      clientIsolationProfileId = clientIsolationAllowlist.getId();
      venueLanPortId = portData1.port().getId();

      clientIsolationProfileServiceCtrl.deactivateClientIsolationProfileOnVenueApModelLanPort(
          venueId, apModel, portId, clientIsolationProfileId);
      final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);

      assertThat(venueLanPort)
          .isNotNull()
          .extracting(VenueLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNull();
    }

    @Test
    void deactivateClientIsolationProfileOnVenueApModelLanPortButNotEnablePort(
        final Venue venue, final ClientIsolationAllowlist clientIsolationAllowlist)
        throws Exception {

      var portData1 = dataHelper.createVenueLanPortDataWithAdoption(venue, apModel, portId, 3);
      venueId = venue.getId();
      clientIsolationProfileId = clientIsolationAllowlist.getId();
      venueLanPortId = portData1.port().getId();
      portData1.port().setEnabled(false);
      repositoryUtil.createOrUpdate(portData1.port(), txCtxExtension.getTenantId(), randomTxId());

      clientIsolationProfileServiceCtrl.deactivateClientIsolationProfileOnVenueApModelLanPort(
          venueId, apModel, portId, clientIsolationProfileId);
      final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);

      assertThat(venueLanPort)
          .isNotNull()
          .extracting(VenueLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNull();
    }
  }

  @Nested
  @FeatureFlag(enable = {ACX_UI_ETHERNET_TOGGLE, WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
  class TestActivateClientIsolationProfileOnApLanPort {

    private String venueId;
    private final String portId = "1";
    private String apLanPortId;
    private String profileId;
    private String serialNumber;

    @BeforeEach
    void prepareData(final Venue venue, Ap ap, ClientIsolationAllowlist clientIsolationAllowlist) {
      venueId = venue.getId();
      serialNumber = ap.getId();
      profileId = clientIsolationAllowlist.getId();
      var activation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .clientIsolationLanPortActivation()
              .generate();

      var portData1 =
          dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 3, of(activation));
      apLanPortId = portData1.port().getId();
    }

    @Test
    void activateClientIsolationProfileOnApLanPort() throws Exception {
      clientIsolationProfileServiceCtrl.activateClientIsolationProfileOnApLanPort(
          venueId, serialNumber, portId, profileId);

      final var port = repositoryUtil.findAndRefresh(ApLanPort.class, apLanPortId);
      assertThat(port)
          .isNotNull()
          .extracting(ApLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNotNull()
          .extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
          .matches(profile -> profile.getId().equals(profileId));
    }
  }

  @Nested
  @FeatureFlag(enable = {ACX_UI_ETHERNET_TOGGLE, WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
  class TestDeactivateClientIsolationProfileOnApLanPort {

    private String venueId;
    private final String portId = "1";
    private String apLanPortId;
    private String profileId;
    private String serialNumber;

    @Test
    void deactivateClientIsolationProfileOnApLanPort(
        final Venue venue, final Ap ap, final ClientIsolationAllowlist clientIsolationAllowlist)
        throws Exception {
      var activation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .clientIsolationLanPortActivation()
              .generate();
      activation.setClientIsolationAllowlist(clientIsolationAllowlist);
      var portData1 =
          dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 3, of(activation));
      venueId = venue.getId();
      serialNumber = ap.getId();
      profileId = clientIsolationAllowlist.getId();
      apLanPortId = portData1.port().getId();

      clientIsolationProfileServiceCtrl.deactivateClientIsolationProfileOnApLanPort(
          venueId, serialNumber, portId, profileId);

      final var port = repositoryUtil.findAndRefresh(ApLanPort.class, apLanPortId);

      assertThat(port)
          .isNotNull()
          .extracting(ApLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNotNull()
          .extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
          .isNull();
    }

    @Test
    void deactivateClientIsolationProfileOnApLanPortButNotEnableClientIsolation(
        final Venue venue, final Ap ap, final ClientIsolationAllowlist clientIsolationAllowlist)
        throws Exception {
      var portData1 = dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 3);
      venueId = venue.getId();
      serialNumber = ap.getId();
      profileId = clientIsolationAllowlist.getId();
      apLanPortId = portData1.port().getId();

      clientIsolationProfileServiceCtrl.deactivateClientIsolationProfileOnVenueApModelLanPort(
          venueId, serialNumber, portId, profileId);
      final var apLanPort = repositoryUtil.find(ApLanPort.class, apLanPortId);

      assertThat(apLanPort)
          .isNotNull()
          .extracting(ApLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNull();
    }

    @Test
    void deactivateClientIsolationProfileOnApLanPortButNotEnablePort(
        final Venue venue, final Ap ap, final ClientIsolationAllowlist clientIsolationAllowlist)
        throws Exception {
      var portData1 = dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 3);
      venueId = venue.getId();
      serialNumber = ap.getId();
      profileId = clientIsolationAllowlist.getId();
      apLanPortId = portData1.port().getId();
      portData1.port().setEnabled(false);
      repositoryUtil.createOrUpdate(portData1.port(), txCtxExtension.getTenantId(), randomTxId());

      clientIsolationProfileServiceCtrl.deactivateClientIsolationProfileOnVenueApModelLanPort(
          venueId, serialNumber, portId, profileId);
      final var apLanPort = repositoryUtil.find(ApLanPort.class, apLanPortId);

      assertThat(apLanPort)
          .isNotNull()
          .extracting(ApLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNull();
    }
  }

  @Test
  void deleteClientIsolationProfile(ClientIsolationAllowlist allowlist) {
    assertDoesNotThrow(
        () -> clientIsolationProfileServiceCtrl.deleteClientIsolationProfile(allowlist.getId()));
  }

  @Test
  void deleteClientIsolationProfileWithVenueLanPortActivations(
      ClientIsolationAllowlist allowlist, Venue venue) {
    var activation = new ClientIsolationLanPortActivation();
    activation.setClientIsolationAllowlist(allowlist);
    dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-01", "1", 777, of(activation));

    assertThrows(
        ObjectInUseException.class,
        () -> clientIsolationProfileServiceCtrl.deleteClientIsolationProfile(allowlist.getId()));
  }

  @Test
  void deleteClientIsolationProfileWithApLanPortActivations(
      ClientIsolationAllowlist allowlist, Venue venue, Ap ap) {
    var activation = new ClientIsolationLanPortActivation();
    activation.setClientIsolationAllowlist(allowlist);
    dataHelper.createApLanPortDataWithAdoption(venue, ap, "1", 777, of(activation));

    assertThrows(
        ObjectInUseException.class,
        () -> clientIsolationProfileServiceCtrl.deleteClientIsolationProfile(allowlist.getId()));
  }

  @Test
  void deleteClientIsolationProfileWithUnusedLanPortAdoptions(
      Tenant tenant, ClientIsolationAllowlist allowlist) {
    dataHelper.createLanPortAdoption(tenant, dataHelper.createEthernetPortProfile(tenant, 777));
    dataHelper.createLanPortAdoption(
        tenant,
        dataHelper.createEthernetPortProfile(tenant, 888),
        of(dataHelper.createSoftGreProfile(tenant)));
    var activation = new ClientIsolationLanPortActivation();
    activation.setClientIsolationAllowlist(allowlist);
    dataHelper.createLanPortAdoption(
        tenant, dataHelper.createEthernetPortProfile(tenant, 999), of(activation));

    assertDoesNotThrow(
        () -> clientIsolationProfileServiceCtrl.deleteClientIsolationProfile(allowlist.getId()));

    assertTrue(repositoryUtil.findAll(LanPortAdoption.class, tenant.getId()).isEmpty());
    assertTrue(
        repositoryUtil.findAll(SoftGreProfileLanPortActivation.class, tenant.getId()).isEmpty());
    assertTrue(
        repositoryUtil.findAll(ClientIsolationLanPortActivation.class, tenant.getId()).isEmpty());
  }

  @TestConfiguration
  @Import({
    MockGrpcBeanConfiguration.class,
    FirmwareCapabilityServiceTestConfig.class,
    ExtendedTenantServiceCtrlImplTestConfig.class,
    ClientIsolationAllowlistServiceCtrlImpl.class,
    ClientIsolationAllowlistMergeImpl.class,
    DhcpServiceProfileMergeImpl.class,
    DhcpConfigServiceProfileMergeImpl.class,
    ExtendedDhcpConfigServiceProfileServiceCtrlImpl.class,
    ExtendedDhcpServiceProfileVenueServiceCtrlImpl.class,
    ExtendedNetworkVenueServiceCtrlImplTestConfig.class,
    NetworkServiceCtrlImplTestConfig.class,
    ExtendedVenueServiceCtrlImplTestConfig.class,
    InitVenueServiceImplTestConfig.class,
    SoftGreProfileLanPortActivationMapperImpl.class,
    ClientIsolationLanPortActivationMapperImpl.class,
    DhcpOption82LanPortActivationMapperImpl.class,
    SoftGreProfileLanPortActivationHandler.class,
    ClientIsolationLanPortActivationHandler.class,
    DhcpOption82LanPortActivationHandler.class,
    LanPortAdoptionServiceImpl.class,
  })
  static class TestConfig {

    @Bean
    ClientIsolationProfileServiceCtrl clientIsolationProfileServiceCtrl(
        ClientIsolationAllowlistMerge clientIsolationAllowlistMerge,
        ClientIsolationAllowlistRepository clientIsolationAllowlistRepository,
        ClientIsolationAllowlistEntryRepository clientIsolationAllowlistEntryRepository,
        NetworkVenueRepository networkVenueRepository,
        DhcpPoolVenueRepository dhcpPoolVenueRepository,
        VenueLanPortRepository venueLanPortRepository,
        ApLanPortRepository apLanPortRepository,
        LanPortAdoptionService lanPortAdoptionService) {
      return new ClientIsolationProfileServiceCtrlImpl(
          clientIsolationAllowlistMerge,
          clientIsolationAllowlistRepository,
          clientIsolationAllowlistEntryRepository,
          networkVenueRepository,
          dhcpPoolVenueRepository,
          venueLanPortRepository,
          apLanPortRepository,
          lanPortAdoptionService);
    }

    @Bean
    @ConditionalOnMissingBean
    LanPortAdoptionDataHelper dataHelper(
        RepositoryUtil repositoryUtil, LanPortAdoptionServiceImpl lanPortAdoptionService) {
      return new LanPortAdoptionDataHelper(repositoryUtil, lanPortAdoptionService);
    }
  }
}
