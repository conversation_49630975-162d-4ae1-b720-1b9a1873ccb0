package com.ruckus.cloud.wifi.service.integration;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.config.RadiusServerSettingConfig;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20NaiRealm;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Plmn;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20RoamConsortium;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkHotspot20Settings;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.ViewmodelAggregatedEntityListener;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.Hotspot20IdentityProviderCmnCfgCollectorOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.RadiusServerProfileCmnCfgCollectorOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmAuthRadiusVenueOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmHotspot20ProfileOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmHotspot20VenueProfileOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmNetworkVenueOperationBuilder;
import com.ruckus.cloud.wifi.kafka.publisher.DdccmPublisher;
import com.ruckus.cloud.wifi.kafka.publisher.ViewmodelConfigPublisher;
import com.ruckus.cloud.wifi.mapper.Hotspot20IdentityProviderMergerImpl;
import com.ruckus.cloud.wifi.service.InitVenueService;
import com.ruckus.cloud.wifi.service.TenantService;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.ExtendedRadiusServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.Hotspot20IdentityProviderServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.config.DdccmListenerTestKits;
import com.ruckus.cloud.wifi.service.integration.config.NetworkServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.template.TemplateManagementService;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.TestContextHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.service.impl.Hotspot20IdentityProviderServiceCtrlImpl.PRECONFIGURED_IDP_TENANT_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@WifiJpaDataTest
class Hotspot20IdentityProviderServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @SpyBean
  private Hotspot20IdentityProviderServiceCtrlImpl hotspot20IdentityProviderService;
  @MockBean
  private DdccmPublisher ddccmPublisher;
  @MockBean
  private ViewmodelConfigPublisher viewmodelConfigPublisher;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private TestContextHelper testContextHelper;
  @MockBean
  private TemplateManagementService templateManagementService;
  @MockBean
  private TenantService tenantService;
  @MockBean
  private InitVenueService initVenueService;

  @Test
  void testAddHotspot20IdentityProvider(Tenant tenant) throws Exception {
    // Given
    var generatedHotspot20IdentityProvider = this.generateHotspot20IdentityProvider();
    var identityProviderId = generatedHotspot20IdentityProvider.getId();
    generatedHotspot20IdentityProvider.setAccountingRadius(null);

    // ADD
    hotspot20IdentityProviderService.addHotspot20IdentityProvider(generatedHotspot20IdentityProvider);

    // GET
    Hotspot20IdentityProvider gotHotspot20IdentityProvider =
        hotspot20IdentityProviderService.getHotspot20IdentityProvider(identityProviderId);

    assertHotspot20IdentityProvider(generatedHotspot20IdentityProvider,
        gotHotspot20IdentityProvider);
  }

  @Test
  void testAddDuplicateName(Tenant tenant) throws Exception {
    String MY_NAME = "myName";
    var provider1 = this.generateHotspot20IdentityProvider();
    provider1.setName(MY_NAME);

    var provider2 = this.generateHotspot20IdentityProvider();
    provider2.setName(MY_NAME);

    hotspot20IdentityProviderService.addHotspot20IdentityProvider(provider1);

    assertThrows(
        InvalidPropertyValueException.class,
        () -> hotspot20IdentityProviderService.addHotspot20IdentityProvider(provider2));
  }

  @Test
  void testAddHotspot20IdentityProvider_nullEapPlmnRoam(Tenant tenant) throws Exception {
    // Given
    var generatedHotspot20IdentityProvider = Generators
        .hotspot20IdentityProvider(null, null, 1, null, null).generate();

    var identityProviderId = generatedHotspot20IdentityProvider.getId();

    // ADD
    hotspot20IdentityProviderService.addHotspot20IdentityProvider(generatedHotspot20IdentityProvider);

    // GET
    Hotspot20IdentityProvider gotHotspot20IdentityProvider =
        hotspot20IdentityProviderService.getHotspot20IdentityProvider(identityProviderId);

    assertHotspot20IdentityProvider(generatedHotspot20IdentityProvider,
        gotHotspot20IdentityProvider);
  }

  @Test
  void testAddHotspot20IdentityProvider_nullAuthInfo(Tenant tenant) throws Exception {
    // Given
    var generatedHotspot20IdentityProvider =
        Generators.hotspot20IdentityProvider(1, 1, 1, null, null).generate();

    var identityProviderId = generatedHotspot20IdentityProvider.getId();

    // ADD
    hotspot20IdentityProviderService.addHotspot20IdentityProvider(generatedHotspot20IdentityProvider);

    // GET
    Hotspot20IdentityProvider gotHotspot20IdentityProvider =
        hotspot20IdentityProviderService.getHotspot20IdentityProvider(identityProviderId);

    assertHotspot20IdentityProvider(generatedHotspot20IdentityProvider,
        gotHotspot20IdentityProvider);
  }

  @Test
  void testUpdateHotspot20IdentityProvider(Tenant tenant) throws Exception {
    // Given
    var presavedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    var identityProviderId = presavedHotspot20IdentityProvider.getId();

    var requestHotspot20IdentityProvider = this.generateHotspot20IdentityProvider();
    requestHotspot20IdentityProvider.setId(null);
    var result = hotspot20IdentityProviderService.updateHotspot20IdentityProvider(
        identityProviderId, requestHotspot20IdentityProvider);

    final Hotspot20IdentityProvider findHotspot20IdentityProvider = repositoryUtil.find(
        Hotspot20IdentityProvider.class, identityProviderId);

    assert findHotspot20IdentityProvider != null;
    assertHotspot20IdentityProvider(requestHotspot20IdentityProvider, findHotspot20IdentityProvider);
  }


  @Test
  void testDeleteHotspot20IdentityProvider(Tenant tenant) throws Exception {
    // Given
    var generatedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    var identityProviderId = generatedHotspot20IdentityProvider.getId();

    hotspot20IdentityProviderService.deleteHotspot20IdentityProvider(identityProviderId);

    assertThrows(ObjectNotFoundException.class,
        () -> hotspot20IdentityProviderService.getHotspot20IdentityProvider(identityProviderId));
  }

  @Test
  void testActivateHotspot20IdentityProviderOnWifiNetworkAndUpdate(Tenant tenant) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    this.generateRadiusAndSetToHotspot20IdentityProvider(savedHotspot20IdentityProvider);

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    repositoryUtil.createOrUpdate(hotspot20Network);

    // When
    hotspot20IdentityProviderService.activateHotspot20IdentityProviderOnWifiNetwork(hotspot20Network.getId(),
        savedHotspot20IdentityProvider.getId());

    // Then
    String providerId = savedHotspot20IdentityProvider.getId();
    var requestHotspot20IdentityProvider = this.generateHotspot20IdentityProvider();

    requestHotspot20IdentityProvider.setId(null);
    hotspot20IdentityProviderService.updateHotspot20IdentityProvider(
        providerId, requestHotspot20IdentityProvider);

    final Hotspot20IdentityProvider findHotspot20IdentityProvider = repositoryUtil.find(
        Hotspot20IdentityProvider.class, providerId);

    assert findHotspot20IdentityProvider != null;
    assertHotspot20IdentityProvider(requestHotspot20IdentityProvider, findHotspot20IdentityProvider);
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateHotspot20IdentityProviderOnWifiNetwork(Tenant tenant) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    this.generateRadiusAndSetToHotspot20IdentityProvider(savedHotspot20IdentityProvider);

    var hotspot20Network = Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    repositoryUtil.createOrUpdate(hotspot20Network);

    // When
    hotspot20IdentityProviderService.activateHotspot20IdentityProviderOnWifiNetwork(hotspot20Network.getId(),
        savedHotspot20IdentityProvider.getId());

    // Then
    testContextHelper.executeInNewTransaction(() -> {
      var savedNetwork = (Hotspot20Network) repositoryUtil.find(Network.class, hotspot20Network.getId());
      assertThat(savedNetwork)
          .isNotNull()
          .extracting(Hotspot20Network::getHotspot20Settings)
          .isNotNull()
          .extracting(NetworkHotspot20Settings::getIdentityProviders).asList()
          .isNotNull()
          .extracting("id")
          .contains(savedHotspot20IdentityProvider.getId());
    }, tenant.getId(), randomTxId());

    ArgumentCaptor<ViewmodelCollector> captor = ArgumentCaptor.forClass(ViewmodelCollector.class);
    verify(viewmodelConfigPublisher, times(1)).publish(captor.capture(),
        any(Optional.class));

    List<ViewmodelCollector> viewmodelCfgRequests = captor.getAllValues();
    assertThat(viewmodelCfgRequests.get(0))
        .isNotNull()
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .hasSize(3)
        .extracting(Operations.class::cast)
        .anyMatch(op -> savedHotspot20IdentityProvider.getId().equals(op.getId()))
        .anyMatch(op -> savedHotspot20IdentityProvider.getAuthRadius().getId().equals(op.getId()))
        .anyMatch(op -> savedHotspot20IdentityProvider.getAccountingRadius().getId().equals(op.getId()));
  }

  @Test
  void testActivateHotspot20IdentityProviderOnWifiNetwork_nonHotspot20Network(Tenant tenant, @OpenNetwork Network openNetwork) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> hotspot20IdentityProviderService.activateHotspot20IdentityProviderOnWifiNetwork(
            openNetwork.getId(), savedHotspot20IdentityProvider.getId()));
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateHotspot20IdentityProviderOnWifiNetwork_networkIsAlreadyActivated(Tenant tenant, Venue venue) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    this.generateRadiusAndSetToHotspot20IdentityProvider(savedHotspot20IdentityProvider);

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Enterprise);
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    var networkVenue = Generators.networkVenue().setNetwork(always(hotspot20Network)).setVenue(always(venue)).generate();

    repositoryUtil.createOrUpdate(hotspot20Network);
    repositoryUtil.createOrUpdate(networkVenue);

    // When
    hotspot20IdentityProviderService.activateHotspot20IdentityProviderOnWifiNetwork(hotspot20Network.getId(),
        savedHotspot20IdentityProvider.getId());

    // Then
    ArgumentCaptor<WifiConfigRequest> captor = ArgumentCaptor.forClass(WifiConfigRequest.class);
    verify(ddccmPublisher, times(1)).publish(captor.capture());

    List<WifiConfigRequest> ddccmRequests = captor.getAllValues();
    assertThat(ddccmRequests)
        .isNotNull()
        .flatExtracting(WifiConfigRequest::getOperationsList)
        .filteredOn(Operation::hasWlanVenue)
        .extracting(Operation::getWlanVenue)
        .isNotNull();
  }

  // ManyToMany test case
  @Test
  void testActivateHotspot20IdentityProviderOnTwoWifiNetwork(Tenant tenant) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    this.generateRadiusAndSetToHotspot20IdentityProvider(savedHotspot20IdentityProvider);

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    var hotspot20Network2 = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network2.getWlan().setNetwork(hotspot20Network2);

    repositoryUtil.createOrUpdate(hotspot20Network);
    repositoryUtil.createOrUpdate(hotspot20Network2);

    // When
    hotspot20IdentityProviderService.activateHotspot20IdentityProviderOnWifiNetwork(hotspot20Network.getId(),
        savedHotspot20IdentityProvider.getId());

    // Then
    assertDoesNotThrow(() -> hotspot20IdentityProviderService.activateHotspot20IdentityProviderOnWifiNetwork(hotspot20Network2.getId(),
        savedHotspot20IdentityProvider.getId())
    );
  }

  // ManyToMany test case
  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateTwoHotspot20IdentityProviderOnWifiNetwork(Tenant tenant) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    this.generateRadiusAndSetToHotspot20IdentityProvider(savedHotspot20IdentityProvider);
    var savedHotspot20IdentityProvider2 = this.generateAndSaveHotspot20IdentityProvider();
    this.generateRadiusAndSetToHotspot20IdentityProvider(savedHotspot20IdentityProvider2);

    var hotspot20Network = Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    repositoryUtil.createOrUpdate(hotspot20Network);

    // When
    this.activateHotspot20IdentityProviderOnWifiNetworkInNewTransaction(tenant.getId(), hotspot20Network.getId(),
        savedHotspot20IdentityProvider.getId());
    this.activateHotspot20IdentityProviderOnWifiNetworkInNewTransaction(tenant.getId(), hotspot20Network.getId(),
        savedHotspot20IdentityProvider2.getId());

    // Then
    testContextHelper.executeInNewTransaction(() -> {
      var savedNetwork = (Hotspot20Network) repositoryUtil.find(Network.class, hotspot20Network.getId());
      assertThat(savedNetwork)
          .isNotNull()
          .extracting(Hotspot20Network::getHotspot20Settings)
          .isNotNull()
          .extracting(NetworkHotspot20Settings::getIdentityProviders).asList()
          .hasSize(2);
    }, tenant.getId(), randomTxId());
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateHotspot20IdentityProviderOnWifiNetwork_reachedMaximumProfiles(Tenant tenant, Venue venue) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    this.generateRadiusAndSetToHotspot20IdentityProvider(savedHotspot20IdentityProvider);

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Enterprise);
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.getHotspot20Settings().setIdentityProviders(List.of(
        this.generateAndSaveHotspot20IdentityProvider(),
        this.generateAndSaveHotspot20IdentityProvider(),
        this.generateAndSaveHotspot20IdentityProvider(),
        this.generateAndSaveHotspot20IdentityProvider(),
        this.generateAndSaveHotspot20IdentityProvider(),
        this.generateAndSaveHotspot20IdentityProvider()
    ));

    repositoryUtil.createOrUpdate(hotspot20Network);

    for (var hotspot20IdentityProvider : hotspot20Network.getHotspot20Settings().getIdentityProviders()) {
      bindingHotspot20IdentityProviderToNetwork(hotspot20IdentityProvider, hotspot20Network);
    }

    // When
    InvalidPropertyValueException exception = assertThrows(InvalidPropertyValueException.class, () -> {
      hotspot20IdentityProviderService.activateHotspot20IdentityProviderOnWifiNetwork(hotspot20Network.getId(),
          savedHotspot20IdentityProvider.getId());
    });
    assertEquals(Errors.WIFI_10539, exception.getErrorCode());
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testDeactivateHotspot20IdentityProviderOnWifiNetwork(Tenant tenant) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    this.generateRadiusAndSetToHotspot20IdentityProvider(savedHotspot20IdentityProvider);

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.getHotspot20Settings().setIdentityProviders(List.of(savedHotspot20IdentityProvider));

    repositoryUtil.createOrUpdate(hotspot20Network);

    savedHotspot20IdentityProvider.setNetworkHotspot20SettingsList(List.of(hotspot20Network.getHotspot20Settings()));
    repositoryUtil.createOrUpdate(savedHotspot20IdentityProvider);

    // When
    hotspot20IdentityProviderService.deactivateHotspot20IdentityProviderOnWifiNetwork(hotspot20Network.getId(),
        savedHotspot20IdentityProvider.getId());

    testContextHelper.executeInNewTransaction(() -> {
      // Then
      var savedNetwork = (Hotspot20Network) repositoryUtil.find(Network.class, hotspot20Network.getId());
      assertThat(savedNetwork)
          .isNotNull()
          .extracting(Hotspot20Network::getHotspot20Settings)
          .isNotNull()
          .extracting(NetworkHotspot20Settings::getIdentityProviders).asList()
          .hasSize(0);
    }, tenant.getId(), randomTxId());

    ArgumentCaptor<ViewmodelCollector> captor = ArgumentCaptor.forClass(ViewmodelCollector.class);
    verify(viewmodelConfigPublisher, times(1)).publish(captor.capture(),
        any(Optional.class));

    List<ViewmodelCollector> viewmodelCfgRequests = captor.getAllValues();
    assertThat(viewmodelCfgRequests.get(0))
        .isNotNull()
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .hasSize(3)
        .extracting(Operations.class::cast)
        .anyMatch(op -> savedHotspot20IdentityProvider.getId().equals(op.getId()))
        .anyMatch(op -> savedHotspot20IdentityProvider.getAuthRadius().getId().equals(op.getId()))
        .anyMatch(op -> savedHotspot20IdentityProvider.getAccountingRadius().getId().equals(op.getId()));
  }

  @Test
  void testDeactivateHotspot20IdentityProviderOnWifiNetwork_nonHotspot20Network(Tenant tenant, @OpenNetwork Network openNetwork) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> hotspot20IdentityProviderService.deactivateHotspot20IdentityProviderOnWifiNetwork(
            openNetwork.getId(), savedHotspot20IdentityProvider.getId()));
  }

  @Test
  void testDeactivateHotspot20IdentityProviderOnWifiNetwork_wrongIdentityProvider(Tenant tenant) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    repositoryUtil.createOrUpdate(hotspot20Network);

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> hotspot20IdentityProviderService.deactivateHotspot20IdentityProviderOnWifiNetwork(
            hotspot20Network.getId(), savedHotspot20IdentityProvider.getId()));
  }

  @Test
  void testDeactivateHotspot20IdentityProviderOnWifiNetwork_networkIsAlreadyActivated(Tenant tenant, Venue venue) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.getHotspot20Settings().setIdentityProviders(List.of(savedHotspot20IdentityProvider));

    repositoryUtil.createOrUpdate(hotspot20Network);

    savedHotspot20IdentityProvider.setNetworkHotspot20SettingsList(List.of(hotspot20Network.getHotspot20Settings()));
    repositoryUtil.createOrUpdate(savedHotspot20IdentityProvider);

    var networkVenue = Generators.networkVenue().setNetwork(always(hotspot20Network)).setVenue(always(venue)).generate();

    repositoryUtil.createOrUpdate(networkVenue);

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> hotspot20IdentityProviderService.deactivateHotspot20IdentityProviderOnWifiNetwork(
            hotspot20Network.getId(), savedHotspot20IdentityProvider.getId()));
  }

  @Test
  void testValidationOnDeleteHotspot20Identity_withNetworkBinding(Tenant tenant) throws Exception {
    // Given
    var savedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.getHotspot20Settings().setIdentityProviders(List.of(savedHotspot20IdentityProvider));

    repositoryUtil.createOrUpdate(hotspot20Network);

    savedHotspot20IdentityProvider.setNetworkHotspot20SettingsList(List.of(hotspot20Network.getHotspot20Settings()));
    repositoryUtil.createOrUpdate(savedHotspot20IdentityProvider);

    // Then
    InvalidPropertyValueException exception = assertThrows(InvalidPropertyValueException.class, () -> {
      hotspot20IdentityProviderService.deleteHotspot20IdentityProvider(savedHotspot20IdentityProvider.getId());
    });
    assertEquals(Errors.WIFI_10529, exception.getErrorCode());
  }

  @Test
  void testGetPreconfiguredHotspot20IdentityProvidersOnTenantCrossing(Tenant tenant) throws Exception {
    // Given
    var generatedHotspot20IdentityProvider1 = this.generateHotspot20IdentityProvider();
    generatedHotspot20IdentityProvider1.setIsPreconfigured(true);
    generatedHotspot20IdentityProvider1.setTenant(Generators.tenant().generate());
    var identityProviderId1 = generatedHotspot20IdentityProvider1.getId();
    repositoryUtil.createOrUpdate(generatedHotspot20IdentityProvider1);

    var generatedHotspot20IdentityProvider2 = this.generateHotspot20IdentityProvider();
    generatedHotspot20IdentityProvider2.setIsPreconfigured(true);
    generatedHotspot20IdentityProvider2.setTenant(Generators.tenant().generate());
    var identityProviderId2 = generatedHotspot20IdentityProvider2.getId();
    repositoryUtil.createOrUpdate(generatedHotspot20IdentityProvider2);

    // GET
    assertThat(hotspot20IdentityProviderService.getPreconfiguredHotspot20IdentityProviders())
        .extracting(AbstractBaseEntity::getId)
        .containsAll(List.of(identityProviderId1, identityProviderId2));
  }

  @Test
  void testAddPreconfiguredHotspot20IdentityProviderOnTenantCrossing(Tenant tenant) throws Exception {
    // Given
    var generatedHotspot20IdentityProvider = this.generateHotspot20IdentityProvider();
    generatedHotspot20IdentityProvider.setTenant(Generators.tenant().generate());
    var identityProviderId = generatedHotspot20IdentityProvider.getId();

    // ADD
    hotspot20IdentityProviderService.addPreconfiguredHotspot20IdentityProvider(generatedHotspot20IdentityProvider);

    // GET
    Hotspot20IdentityProvider findHotspot20IdentityProvider =
        repositoryUtil.find(Hotspot20IdentityProvider.class, identityProviderId);

    assert findHotspot20IdentityProvider != null;
    assertEquals(PRECONFIGURED_IDP_TENANT_ID, findHotspot20IdentityProvider.getTenant().getId());
    assertHotspot20IdentityProvider(generatedHotspot20IdentityProvider, findHotspot20IdentityProvider);
  }

  @Test
  void testUpdatePreconfiguredHotspot20IdentityProviderOnTenantCrossing(Tenant tenant) throws Exception {
    // Given
    var generatedHotspot20IdentityProvider = this.generateHotspot20IdentityProvider();
    generatedHotspot20IdentityProvider.setIsPreconfigured(true);
    generatedHotspot20IdentityProvider.setTenant(Generators.tenant().generate());
    var presavedHotspot20IdentityProvider = repositoryUtil.createOrUpdate(generatedHotspot20IdentityProvider);
    var identityProviderId = presavedHotspot20IdentityProvider.getId();

    var requestHotspot20IdentityProvider = this.generateHotspot20IdentityProvider();
    requestHotspot20IdentityProvider.setId(null);
    hotspot20IdentityProviderService.updatePreconfiguredHotspot20IdentityProvider(
        identityProviderId, requestHotspot20IdentityProvider);

    final Hotspot20IdentityProvider findHotspot20IdentityProvider = repositoryUtil.find(
        Hotspot20IdentityProvider.class, identityProviderId);

    assert findHotspot20IdentityProvider != null;
    assertHotspot20IdentityProvider(requestHotspot20IdentityProvider, findHotspot20IdentityProvider);
  }

  @Test
  void testUpdatePreconfiguredHotspot20IdentityProviderOnTenantCrossing_failed(Tenant tenant) {
    // Given
    var generatedHotspot20IdentityProvider = this.generateHotspot20IdentityProvider();
    generatedHotspot20IdentityProvider.setTenant(Generators.tenant().generate());
    var presavedHotspot20IdentityProvider = repositoryUtil.createOrUpdate(generatedHotspot20IdentityProvider);
    var identityProviderId = presavedHotspot20IdentityProvider.getId();

    var requestHotspot20IdentityProvider = this.generateHotspot20IdentityProvider();
    requestHotspot20IdentityProvider.setId(null);
    assertThrows(ObjectNotFoundException.class,
        () -> hotspot20IdentityProviderService.updatePreconfiguredHotspot20IdentityProvider(
            identityProviderId, requestHotspot20IdentityProvider));
  }

  @Test
  void testDeletePreconfiguredHotspot20IdentityProviderOnTenantCrossing(Tenant tenant) throws Exception {
    // Given
    var generatedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    generatedHotspot20IdentityProvider.setIsPreconfigured(true);
    generatedHotspot20IdentityProvider.setTenant(Generators.tenant().generate());
    repositoryUtil.createOrUpdate(generatedHotspot20IdentityProvider);
    var identityProviderId = generatedHotspot20IdentityProvider.getId();

    hotspot20IdentityProviderService.deletePreconfiguredHotspot20IdentityProvider(identityProviderId);

    assertThrows(ObjectNotFoundException.class,
        () -> hotspot20IdentityProviderService.getHotspot20IdentityProvider(identityProviderId));
  }

  @Test
  void testDeletePreconfiguredHotspot20IdentityProviderOnTenantCrossing_failed(Tenant tenant) throws Exception {
    // Given
    var generatedHotspot20IdentityProvider = this.generateAndSaveHotspot20IdentityProvider();
    generatedHotspot20IdentityProvider.setTenant(Generators.tenant().generate());
    repositoryUtil.createOrUpdate(generatedHotspot20IdentityProvider);
    var identityProviderId = generatedHotspot20IdentityProvider.getId();

    assertThrows(ObjectNotFoundException.class,
        () -> hotspot20IdentityProviderService.deletePreconfiguredHotspot20IdentityProvider(identityProviderId));
  }

  private void assertHotspot20IdentityProvider(Hotspot20IdentityProvider expected, Hotspot20IdentityProvider actual) {
    assertNotNull(expected);
    assertEquals(expected.getName(), actual.getName());

    Optional.ofNullable(expected.getPlmns())
            .stream().flatMap(List::stream)
            .forEach(expectedPlmn -> {
              Hotspot20Plmn actualPlmn = actual.getPlmns().stream()
                  .filter(plmn -> expectedPlmn.getMcc().equals(plmn.getMcc()))
                  .findFirst().orElse(null);
              assertEquals(expectedPlmn.getMnc(), actualPlmn.getMnc());
            });

    Optional.ofNullable(expected.getRoamConsortiumOis())
        .stream().flatMap(List::stream)
        .forEach(expectedRoamConsortium -> {
          Hotspot20RoamConsortium actualRoamConsortium = actual.getRoamConsortiumOis().stream()
              .filter(r -> expectedRoamConsortium.getOrganizationId().equals(r.getOrganizationId()))
              .findFirst().orElse(null);
          assertEquals(expectedRoamConsortium.getOrganizationId(), actualRoamConsortium.getOrganizationId());
        });

    for (Hotspot20NaiRealm expectedNaiRealm: expected.getNaiRealms()) {
      Hotspot20NaiRealm actualNaiRealm = actual.getNaiRealms().stream()
          .filter(naiRealm -> expectedNaiRealm.getName().equals(naiRealm.getName()))
          .findFirst().orElse(null);
      assertEquals(expectedNaiRealm.getEncoding(), actualNaiRealm.getEncoding());

      Optional.ofNullable(expectedNaiRealm.getEaps())
              .ifPresent(expectedEaps -> {
                assertEquals(expectedEaps.size(), actualNaiRealm.getEaps().size());
              }) ;
    }
  }

  private Hotspot20IdentityProvider generateHotspot20IdentityProvider() {
    return generateAndSaveHotspot20IdentityProvider(false);
  }

  private Hotspot20IdentityProvider generateAndSaveHotspot20IdentityProvider() {
    return generateAndSaveHotspot20IdentityProvider(true);
  }

  private void bindingHotspot20IdentityProviderToNetwork(Hotspot20IdentityProvider identityProvider, Hotspot20Network hotspot20Network) {
    identityProvider.setNetworkHotspot20SettingsList(List.of(hotspot20Network.getHotspot20Settings()));
    repositoryUtil.createOrUpdate(identityProvider);
  }

  private Hotspot20IdentityProvider generateAndSaveHotspot20IdentityProvider(boolean toSaveProvider) {
    var hotspot20NaiRealm = Generators.hotspot20NaiRealm().generate();
    hotspot20NaiRealm.setId(null);
    hotspot20NaiRealm.getEaps().forEach(e -> {
      e.setId(null);
      e.setNaiRealm(hotspot20NaiRealm);
      e.getAuthInfos().forEach(a -> {
        a.setId(null);
        a.setEap(e);});
    });
    var hotspot20Plmn = Generators.hotspot20Plmn().generate();
    hotspot20Plmn.setId(null);
    var hotspot20RoamConsortium = Generators.hotspot20RoamConsortium().generate();
    hotspot20RoamConsortium.setId(null);

    Hotspot20IdentityProvider hotspot20IdentityProvider = Generators.hotspot20IdentityProvider().generate();
    hotspot20IdentityProvider.setNaiRealms(List.of(hotspot20NaiRealm));
    hotspot20IdentityProvider.setPlmns(List.of(hotspot20Plmn));
    hotspot20IdentityProvider.setRoamConsortiumOis(List.of(hotspot20RoamConsortium));

    // Add FK
    hotspot20NaiRealm.setIdentityProvider(hotspot20IdentityProvider);
    hotspot20Plmn.setIdentityProvider(hotspot20IdentityProvider);
    hotspot20RoamConsortium.setIdentityProvider(hotspot20IdentityProvider);

    if (!toSaveProvider) {
      return hotspot20IdentityProvider;
    }
    return repositoryUtil.createOrUpdate(hotspot20IdentityProvider);
  }

  private void generateRadiusAndSetToHotspot20IdentityProvider(Hotspot20IdentityProvider hotspot20IdentityProvider) {
    Radius radiusAuth = Generators.radiusAuth().generate();
    Radius radiusAcct = Generators.radiusAcct().generate();
    AccountingRadiusService accountingRadiusService = Generators.accountingRadiusService(radiusAcct).generate();
    AccountingRadiusProfile accountingRadiusProfile = Generators.accountingRadiusProfile(accountingRadiusService).generate();
    AuthRadiusService authRadiusService = Generators.authRadiusService(radiusAuth).generate();
    AuthRadiusProfile authRadiusProfile = Generators.authRadiusProfile(authRadiusService).generate();

    hotspot20IdentityProvider.setAccountingRadiusEnabled(true);
    hotspot20IdentityProvider.setAccountingRadius(radiusAcct);
    hotspot20IdentityProvider.setAccountingRadiusProfile(accountingRadiusProfile);
    hotspot20IdentityProvider.setAuthRadius(radiusAuth);
    hotspot20IdentityProvider.setAuthRadiusProfile(authRadiusProfile);

    repositoryUtil.createOrUpdate(radiusAuth);
    repositoryUtil.createOrUpdate(radiusAcct);
    repositoryUtil.createOrUpdate(accountingRadiusService);
    repositoryUtil.createOrUpdate(accountingRadiusProfile);
    repositoryUtil.createOrUpdate(authRadiusService);
    repositoryUtil.createOrUpdate(authRadiusProfile);
    repositoryUtil.createOrUpdate(hotspot20IdentityProvider);
  }

  private void activateHotspot20IdentityProviderOnWifiNetworkInNewTransaction(String tenantId, String wifiNetworkId, String hotspot20IdentityProviderId) throws Exception {
    testContextHelper.executeInNewTransaction(() -> {
      try {
        hotspot20IdentityProviderService.activateHotspot20IdentityProviderOnWifiNetwork(wifiNetworkId,
            hotspot20IdentityProviderId);
      } catch (Exception e) {
        fail("Should not throw any exception");
      }
    }, tenantId, randomTxId());
  }

  @TestConfiguration
  @Import({
      DdccmListenerTestKits.class,
      DdccmNetworkVenueOperationBuilder.class,
      DdccmHotspot20ProfileOperationBuilder.class,
      DdccmHotspot20VenueProfileOperationBuilder.class,
      DdccmAuthRadiusVenueOperationBuilder.class,
      ViewmodelAggregatedEntityListener.class,
      RadiusServerProfileCmnCfgCollectorOperationBuilder.class,
      Hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.class,
      NetworkServiceCtrlImplTestConfig.class,
      Hotspot20IdentityProviderMergerImpl.class,
      ExtendedRadiusServiceCtrlImpl.class,
      RadiusServerSettingConfig.class
  })
  static class Config {
  }
}

