package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueHeight;
import com.ruckus.cloud.wifi.proto.VenueHeightPostMigrationJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("VenueHeightTest")
@WifiIntegrationTest
class ConsumeVenueHeightPostMigrationJobTest {

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  @RequiredArgsConstructor
  private static class VenueHeightData {

    private final String venueId = randomId();
    private final Integer minFloor;
    private final Integer maxFloor;
  }

  @Nested
  class GivenVenuePersistedInDb {

    private static final List<VenueHeightData> VENUES = List.of(new VenueHeightData(null, null),
        new VenueHeightData(0, null), new VenueHeightData(null, 0), new VenueHeightData(0, 0),
        new VenueHeightData(0, 1), new VenueHeightData(1, 0), new VenueHeightData(1, 1));

    @BeforeEach
    void givenRequiredDataPersistedInDb(final Tenant tenant) {
      VENUES.forEach(data -> {
        var venue = new Venue(data.venueId);
        var height = new VenueHeight();
        height.setMinFloor(data.minFloor);
        height.setMaxFloor(data.maxFloor);
        venue.setTenant(tenant);
        venue.setHeight(height);
        venue.setApPassword("1qaz@WSX");
        venue.setCountryCode("US");
        venue.setName(randomName());
        venue.setTimezone("America/Los_Angeles");
        repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
      });
    }

    @Test
    void whenConsumeVenueHeightPostMigrationJob(final Tenant tenant) {
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
          .setVenueHeightPostMigrationJob(VenueHeightPostMigrationJob.newBuilder()).build());

      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());

      final var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId());

      assertThat(ddccmMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(1)
          .filteredOn(Operation::hasVenue)
          .filteredOn(e -> e.getAction() == Action.MODIFY)
          .isNotEmpty().singleElement()
          .extracting(Operation::getVenue)
          .matches(v -> VENUES.get(3).venueId.equals(v.getId()))
          .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasAfcVenueHeightAgl)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getAfcVenueHeightAgl)
          .matches(h -> h.hasMinHeight() && h.getMinHeight().getValue() == 0 && h.hasMaxHeight()
              && h.getMaxHeight().getValue() == 0);
    }
  }
}
