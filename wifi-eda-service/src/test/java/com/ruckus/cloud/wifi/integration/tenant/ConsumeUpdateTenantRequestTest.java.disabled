package com.ruckus.cloud.wifi.integration.tenant;

import static com.ruckus.cloud.wifi.eda.servicemodel.enums.RogueNetworkProtectionStrategyEnum.CONSERVATIVE;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.APPLICATION_POLICY_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.WIFI_NETWORK_IDS;
import static com.ruckus.cloud.wifi.service.tenant.TenantTierChangeListener.CORE_TIER;
import static com.ruckus.cloud.wifi.service.tenant.TenantTierChangeListener.ESSENTIAL_TIER;
import static com.ruckus.cloud.wifi.service.tenant.TenantTierChangeListener.PROFESSIONAL_TIER;
import static com.ruckus.cloud.wifi.service.tenant.TenantTierChangeListener.getSubtaskType;
import static com.ruckus.cloud.wifi.service.tenant.impl.WorkflowNetworkTierChangeListener.Operation.DEACTIVATE_VENUE;
import static com.ruckus.cloud.wifi.service.tenant.impl.WorkflowNetworkTierChangeListener.Operation.DELETE_NETWORK;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.tenantservice.api.PreviousStateTenantDetails;
import com.ruckus.cloud.tenantservice.api.TenantDetails;
import com.ruckus.cloud.tenantservice.api.TenantUpdateEvent;
import com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicyVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.service.tenant.impl.LbsTierChangeListener;
import com.ruckus.cloud.wifi.service.tenant.impl.ApplicationPolicyTierChangeListener;
import com.ruckus.cloud.wifi.service.tenant.impl.NetworkArcTierChangeListener;
import com.ruckus.cloud.wifi.service.tenant.impl.RogueTierChangeListener;
import com.ruckus.cloud.wifi.service.tenant.impl.WorkflowNetworkTierChangeListener;
import com.ruckus.cloud.wifi.service.tenant.impl.WorkflowNetworkTierChangeListener.WorkflowNetworkSubtask;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeUpdateTenantRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Test
  void testConsumeUpdateTenantName_ShouldNotifyDdccm(Tenant tenant) {
    final var requestId = randomTxId();
    final var message = TenantUpdateEvent.newBuilder()
        .setTenantDetails(TenantDetails.newBuilder()
            .setTenantId(tenant.getId())
            .setTenantName("newName").build())
        .build();

    messageUtil.sendUpdateTenant(requestId, message);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull();

    assertThatNoException().isThrownBy(() -> {
      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList).asList()
          .isNotEmpty()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
          .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasTenant).hasSize(1)
          .first().matches(op -> op.getAction() == Action.MODIFY)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getTenant)
          .matches(e -> e.getName().equals("newName"));
    });

    assertThat(repositoryUtil.find(Tenant.class, tenant.getId()))
        .isNotNull()
        .matches(t -> t.getName().equals("newName"));
  }

  @Nested
  class TierChangeHandlingForArc {

    // ARC true -> false
    private OpenNetwork network1;
    // Template ARC true -> false
    private OpenNetwork network2;
    // ARC null -> false
    private OpenNetwork network3;
    private NetworkVenue networkVenue1;
    private NetworkVenue networkVenue2;
    private NetworkVenue networkVenue3;

    @BeforeEach
    void prepareData(Tenant tenant, Venue venue) {
      network1 = Generators.openNetwork().generate();
      network1.getWlan().setNetwork(network1);
      network1.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
      network1.getWlan().getAdvancedCustomization().setApplicationVisibilityEnabled(true);
      network1 = repositoryUtil.createOrUpdate(network1, tenant.getId());

      network2 = Generators.openNetwork().generate();
      network2.setIsTemplate(true);
      network2.getWlan().setNetwork(network2);
      network2.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
      network2.getWlan().getAdvancedCustomization().setApplicationVisibilityEnabled(true);
      network2 = repositoryUtil.createOrUpdate(network2, tenant.getId());

      network3 = Generators.openNetwork().generate();
      network3.getWlan().setNetwork(network3);
      network3.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
      network3 = repositoryUtil.createOrUpdate(network3, tenant.getId());

      networkVenue1 =
          repositoryUtil.createOrUpdate(
              NetworkVenueTestFixture.randomNetworkVenue(network1, venue), tenant.getId());
      networkVenue2 =
          repositoryUtil.createOrUpdate(
              NetworkVenueTestFixture.randomNetworkVenue(network2, venue), tenant.getId());
      networkVenue3 =
          repositoryUtil.createOrUpdate(
              NetworkVenueTestFixture.randomNetworkVenue(network3, venue), tenant.getId());
    }

    @Test
    void changeTierFromEssentialToCore(Tenant tenant, Venue venue) {
      final var requestId = randomTxId();
      final var message =
          TenantUpdateEvent.newBuilder()
              .setTenantDetails(
                  TenantDetails.newBuilder()
                      .setTenantId(tenant.getId())
                      .setTenantName(tenant.getName())
                      .setAccountTier(CORE_TIER)
                      .setPreviousStateTenantDetails(
                          PreviousStateTenantDetails.newBuilder()
                              .setAccountTier(ESSENTIAL_TIER)
                              .build())
                      .build())
              .build();

      messageUtil.sendUpdateTenant(requestId, message);

      // AsyncJob
      var asyncJobMessages =
          messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId(), requestId);
      assertThat(asyncJobMessages)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(WifiAsyncJob::hasTenantTierChangedSubtaskJob)
          .extracting(WifiAsyncJob::getTenantTierChangedSubtaskJob)
          .matches(
              subtask ->
                  ESSENTIAL_TIER.equals(subtask.getFromTier())
                      && CORE_TIER.equals(subtask.getToTier())
                      && getSubtaskType(NetworkArcTierChangeListener.NetworkArcSubtask.class)
                          .equals(subtask.getSubtaskType()));
      // DDCCM
      var ddccmMessages = messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId());
      assertNotNull(ddccmMessages);
      assertNotNull(ddccmMessages.getPayload());
      assertNotNull(ddccmMessages.getPayload().getOperationsList());
      var operations = ddccmMessages.getPayload().getOperationsList();
      // Should have 3 operations including all networks
      assertEquals(3, operations.size());
      var networkIds = new HashSet<String>();
      var venueIds = new HashSet<String>();
      operations.forEach(
          o -> {
            assertTrue(o.hasWlanVenue());
            networkIds.add(o.getWlanVenue().getWlanId());
            venueIds.add(o.getWlanVenue().getVenueId());
          });
      assertEquals(Set.of(network1.getId(), network2.getId(), network3.getId()), networkIds);
      assertEquals(Set.of(venue.getId()), venueIds);
      // CmnCfg
      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());
      // DB
      verifyNetwork(network1.getId());
      verifyNetwork(network2.getId());
      verifyNetwork(network3.getId());
    }

    private void verifyNetwork(String networkId) {
      assertThat(repositoryUtil.find(Network.class, networkId))
          .isNotNull()
          .matches(
              n ->
                  Boolean.FALSE.equals(
                      n.getWlan().getAdvancedCustomization().getApplicationVisibilityEnabled()));
    }
  }

  @Nested
  class TierChangeHandlingForRogue {

    private Venue venue;
    private Venue venueTemplate;
    private Venue venueWithRogueDisabled;

    private RogueClassificationPolicy rogueClassificationPolicy;
    private RogueClassificationPolicy rogueClassificationPolicyTemplate;

    @BeforeEach
    public void prepareData(Tenant tenant) {
      final var rogueAp = new VenueRogueAp();
      rogueAp.setEnabled(true);
      venue = VenueTestFixture.randomVenue(tenant);
      venue.setRogueAp(rogueAp);
      venue = repositoryUtil.createOrUpdate(venue, tenant.getId());
      venueTemplate = VenueTestFixture.randomVenue(tenant);
      venueTemplate.setRogueAp(rogueAp);
      venueTemplate.setIsTemplate(true);
      venueTemplate = repositoryUtil.createOrUpdate(venueTemplate, tenant.getId());
      venueWithRogueDisabled = VenueTestFixture.randomVenue(tenant);
      venueWithRogueDisabled = repositoryUtil.createOrUpdate(venueWithRogueDisabled, tenant.getId());
      rogueClassificationPolicy = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy 1");
      rogueClassificationPolicy.setId(randomId());
      rogueClassificationPolicy.getRules().forEach(rule ->
          rule.setRogueClassificationPolicy(rogueClassificationPolicy));
      rogueClassificationPolicy = repositoryUtil.createOrUpdate(rogueClassificationPolicy, tenant.getId());
      applyVenue(tenant, rogueClassificationPolicy, venue);
      rogueClassificationPolicyTemplate = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy 2");
      rogueClassificationPolicyTemplate.setId(randomId());
      rogueClassificationPolicyTemplate.setIsTemplate(true);
      rogueClassificationPolicyTemplate.getRules().forEach(rule ->
          rule.setRogueClassificationPolicy(rogueClassificationPolicyTemplate));
      rogueClassificationPolicyTemplate = repositoryUtil.createOrUpdate(
          rogueClassificationPolicyTemplate, tenant.getId());
      applyVenue(tenant, rogueClassificationPolicyTemplate, venueTemplate);
    }

    private void applyVenue(Tenant tenant, RogueClassificationPolicy rogueClassificationPolicy, Venue venue) {
      RogueClassificationPolicyVenue rogueClassificationPolicyVenue = new RogueClassificationPolicyVenue();
      rogueClassificationPolicyVenue.setId(randomId());
      rogueClassificationPolicyVenue.setVenue(venue);
      rogueClassificationPolicyVenue.setRogueClassificationPolicy(rogueClassificationPolicy);
      repositoryUtil.createOrUpdate(rogueClassificationPolicyVenue, tenant.getId());
    }

    @Test
    void changeTierFromEssentialToCore(Tenant tenant) throws JsonProcessingException {
      final var requestId = randomTxId();
      final var message =
          TenantUpdateEvent.newBuilder()
              .setTenantDetails(
                  TenantDetails.newBuilder()
                      .setTenantId(tenant.getId())
                      .setTenantName(tenant.getName())
                      .setAccountTier(CORE_TIER)
                      .setPreviousStateTenantDetails(
                          PreviousStateTenantDetails.newBuilder()
                              .setAccountTier(ESSENTIAL_TIER)
                              .build())
                      .build())
              .build();

      messageUtil.sendUpdateTenant(requestId, message);

      // AsyncJob
      var asyncJobMessages =
          messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId(), requestId);
      assertThat(asyncJobMessages)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(WifiAsyncJob::hasTenantTierChangedSubtaskJob)
          .extracting(WifiAsyncJob::getTenantTierChangedSubtaskJob)
          .matches(
              subtask ->
                  ESSENTIAL_TIER.equals(subtask.getFromTier())
                      && CORE_TIER.equals(subtask.getToTier())
                      && RogueTierChangeListener.VenueRogueSubtask.class
                      .getSimpleName()
                      .equals(subtask.getSubtaskType()));

      final var subTask = new ObjectMapper().readValue(
          asyncJobMessages.getPayload().getTenantTierChangedSubtaskJob().getSubtaskContent(),
          RogueTierChangeListener.VenueRogueSubtask.class);
      assertThat(subTask.venueIds()).contains(venue.getId(), venueTemplate.getId());

      var ddccmMessages = messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId());
      assertNotNull(ddccmMessages);
      assertNotNull(ddccmMessages.getPayload());
      assertNotNull(ddccmMessages.getPayload().getOperationsList());
      assertThat(ddccmMessages.getPayload().getOperationsList())
          .filteredOn(e -> e.hasVenue()).hasSize(2)
          .filteredOn(e -> e.getVenue().getRogueEnabled() == false)
          .extracting(Operation::getId)
          .contains(venue.getId(), venueTemplate.getId());

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId());

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .satisfies(operations -> {
                assertThat(operations)
                    .filteredOn(op -> Index.VENUE.equals(op.getIndex()))
                    .hasSize(2)
                    .allMatch(op -> op.getOpType() == OpType.MOD)
                    .filteredOn(op -> !op.getDocMap().containsKey("rogueDetection"))
                    .allMatch(op -> List.of(venue, venueTemplate).contains(op.getId()));

                assertThat(operations)
                    .filteredOn(op ->
                        Index.ROGUE_AP_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
                    .hasSize(2)
                    .allMatch(op -> op.getOpType() == OpType.MOD)
                    .allMatch(op ->
                        op.getDocMap().get(Key.VENUE_IDS).getListValue().getValuesList().isEmpty());
              })
              );
      verifyVenue(venue.getId());
    }

    private void verifyVenue(String venueId) {
      var venue = repositoryUtil.find(Venue.class, venueId);
      assertThat(venue).isNotNull();
      assertThat(venue.getRogueAp()).isNotNull();

      assertThat(venue.getRogueAp()).satisfies(rogueAp -> {
        assertThat(rogueAp.getEnabled()).isFalse();
        assertThat(rogueAp.getRoguePolicyId()).isNull();
        assertEquals(Short.valueOf((short)0), rogueAp.getReportThreshold());
        assertThat(rogueAp.getNetworkProtection()).isFalse();
        assertEquals(CONSERVATIVE, rogueAp.getNetworkProtectionStrategy());
      });
    }
  }

  @Nested
  class TierChangeHandlingForApplicationPolicy {
    private String policyId;
    private String policyIdInProfile;
    private String profileId;
    private String templatePolicyId;
    private String templatePolicyIdInProfile;
    private String templateProfileId;

    private String networkWithPolicyId;
    private String networkWithProfileId;
    private String templateNetworkWithPolicyId;
    private String templateNetworkWithProfileId;

    @BeforeEach
    void prepareData(Tenant tenant, Venue venue) {
      var networkWithPolicy = createNetworkWithPolicy(tenant, false);
      var networkWithProfile = createNetworkWithProfile(tenant, false);
      var templateNetworkWithPolicy = createNetworkWithPolicy(tenant, true);
      var templateNetworkWithProfile = createNetworkWithProfile(tenant, true);

      var policy = networkWithPolicy.getWlan().getAdvancedCustomization().getApplicationPolicy();
      var profile =
          networkWithProfile.getWlan().getAdvancedCustomization().getAccessControlProfile();
      var templatePolicy =
          templateNetworkWithPolicy.getWlan().getAdvancedCustomization().getApplicationPolicy();
      var templateProfile =
          templateNetworkWithProfile.getWlan().getAdvancedCustomization().getAccessControlProfile();

      createNetworkVenue(networkWithPolicy, venue);
      createNetworkVenue(networkWithProfile, venue);
      createNetworkVenue(templateNetworkWithPolicy, venue);
      createNetworkVenue(templateNetworkWithProfile, venue);

      policyId = policy.getId();
      policyIdInProfile = profile.getApplicationPolicy().getId();
      profileId = profile.getId();
      templatePolicyId = templatePolicy.getId();
      templatePolicyIdInProfile = templateProfile.getApplicationPolicy().getId();
      templateProfileId = templateProfile.getId();

      networkWithPolicyId = networkWithPolicy.getId();
      networkWithProfileId = networkWithProfile.getId();
      templateNetworkWithPolicyId = templateNetworkWithPolicy.getId();
      templateNetworkWithProfileId = templateNetworkWithProfile.getId();
    }

    @Test
    void changeTierFromEssentialToCore(Tenant tenant, Venue venue) {
      final var requestId = randomTxId();
      final var message =
          TenantUpdateEvent.newBuilder()
              .setTenantDetails(
                  TenantDetails.newBuilder()
                      .setTenantId(tenant.getId())
                      .setTenantName(tenant.getName())
                      .setAccountTier(CORE_TIER)
                      .setPreviousStateTenantDetails(
                          PreviousStateTenantDetails.newBuilder()
                              .setAccountTier(ESSENTIAL_TIER)
                              .build())
                      .build())
              .build();
      messageUtil.sendUpdateTenant(requestId, message);

      // #AsyncJob
      var asyncJobMessages =
          messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId(), requestId);
      assertThat(asyncJobMessages)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(WifiAsyncJob::hasTenantTierChangedSubtaskJob)
          .extracting(WifiAsyncJob::getTenantTierChangedSubtaskJob)
          .matches(
              subtask ->
                  ESSENTIAL_TIER.equals(subtask.getFromTier())
                      && CORE_TIER.equals(subtask.getToTier())
                      && getSubtaskType(
                              ApplicationPolicyTierChangeListener.ApplicationPolicySubtask.class)
                          .equals(subtask.getSubtaskType()));

      // #DDCCM
      // 1st message should have 2 operations including 2 profiles triigered by main tier change
      // event
      var ddccmMessages = messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId());
      assertNotNull(ddccmMessages);
      assertNotNull(ddccmMessages.getPayload());
      assertNotNull(ddccmMessages.getPayload().getOperationsList());
      var operations = ddccmMessages.getPayload().getOperationsList();
      assertEquals(2, operations.size());
      assertTrue(
          operations.stream()
              .allMatch(
                  o ->
                      o.hasCcmFirewallProfile()
                          && !o.getCcmFirewallProfile().hasFirewallQmApplicationPolicy()));
      // 2nd message should have 2 operations including 2 networks with policies triggered by async
      // job
      ddccmMessages = messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId());
      assertNotNull(ddccmMessages);
      assertNotNull(ddccmMessages.getPayload());
      assertNotNull(ddccmMessages.getPayload().getOperationsList());
      operations = ddccmMessages.getPayload().getOperationsList();
      assertEquals(2, operations.size());
      var networkIds = new HashSet<String>();
      var venueIds = new HashSet<String>();
      operations.forEach(
          o -> {
            assertTrue(o.hasWlanVenue());
            assertFalse(o.getWlanVenue().hasAppPolicyId());
            networkIds.add(o.getWlanVenue().getWlanId());
            venueIds.add(o.getWlanVenue().getVenueId());
          });
      assertEquals(Set.of(networkWithPolicyId, templateNetworkWithPolicyId), networkIds);
      assertEquals(Set.of(venue.getId()), venueIds);

      // #CmnCfg - Should have 6 operations for 2 policies and 4 policies
      // AccessControlProfile -> r1-accesscontrolpolicyprofile-alias
      // ApplicationPolicy -> r1-accesscontrolsubprofile-alias
      var cmnCfgMessages =
          messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId(), requestId);
      assertNotNull(cmnCfgMessages);
      assertNotNull(cmnCfgMessages.getPayload());
      assertNotNull(cmnCfgMessages.getPayload().getOperationsList());
      var cmnCfgOperations = cmnCfgMessages.getPayload().getOperationsList();
      assertEquals(6, cmnCfgOperations.size());
      var profileOperations =
          cmnCfgOperations.stream()
              .filter(o -> ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME.equals(o.getIndex()))
              .toList();
      assertEquals(2, profileOperations.size());
      var profileIds = new HashSet<String>();
      profileOperations.forEach(
          o -> {
            profileIds.add(o.getId());
            assertTrue(o.getDocMap().get(APPLICATION_POLICY_ID).getStringValue().isEmpty());
          });
      assertEquals(Set.of(profileId, templateProfileId), profileIds);
      var policyOperations =
          cmnCfgOperations.stream()
              .filter(o -> ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME.equals(o.getIndex()))
              .toList();
      assertEquals(4, policyOperations.size());
      var policyIds = new HashSet<String>();
      policyOperations.forEach(
          o -> {
            policyIds.add(o.getId());
            assertTrue(
                o.getDocMap().get(WIFI_NETWORK_IDS).getListValue().getValuesList().isEmpty());
          });
      assertEquals(
          Set.of(policyId, policyIdInProfile, templatePolicyId, templatePolicyIdInProfile),
          policyIds);

      // #DB
      verifyProfile(profileId);
      verifyProfile(templateProfileId);
      verifyNetwork(networkWithPolicyId);
      verifyNetwork(networkWithProfileId);
      verifyNetwork(templateNetworkWithPolicyId);
      verifyNetwork(templateNetworkWithProfileId);
    }

    private ApplicationPolicy createApplicationPolicy(Tenant tenant, boolean template) {
      var result = Generators.applicationPolicy().generate();
      if (template) {
        result.setIsTemplate(true);
      }
      result.setRules(List.of(Generators.applicationPolicyRule(result).generate()));
      result = repositoryUtil.createOrUpdate(result, tenant.getId());
      return result;
    }

    private AccessControlProfile createAccessControlProfile(Tenant tenant, boolean template) {
      var result = Generators.accessControlProfile().generate();
      if (template) {
        result.setIsTemplate(true);
      }
      result.setApplicationPolicyEnable(true);
      result.setApplicationPolicy(createApplicationPolicy(tenant, template));
      result = repositoryUtil.createOrUpdate(result, tenant.getId());
      return result;
    }

    private OpenNetwork createNetworkWithPolicy(Tenant tenant, boolean template) {
      var result = Generators.openNetwork().generate();
      result.getWlan().setNetwork(result);
      result.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
      result.getWlan().getAdvancedCustomization().setApplicationPolicyEnable(true);
      result
          .getWlan()
          .getAdvancedCustomization()
          .setApplicationPolicy(createApplicationPolicy(tenant, template));
      result = repositoryUtil.createOrUpdate(result, tenant.getId());
      return result;
    }

    private OpenNetwork createNetworkWithProfile(Tenant tenant, boolean template) {
      var result = Generators.openNetwork().generate();
      result.getWlan().setNetwork(result);
      result.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
      result.getWlan().getAdvancedCustomization().setAccessControlEnable(true);
      result
          .getWlan()
          .getAdvancedCustomization()
          .setAccessControlProfile(createAccessControlProfile(tenant, template));
      result = repositoryUtil.createOrUpdate(result, tenant.getId());
      return result;
    }

    private NetworkVenue createNetworkVenue(OpenNetwork network, Venue venue) {
      return repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(network, venue), venue.getTenant().getId());
    }

    private void verifyProfile(String profileId) {
      assertThat(repositoryUtil.find(AccessControlProfile.class, profileId))
          .isNotNull()
          .matches(
              p ->
                  Boolean.FALSE.equals(p.getApplicationPolicyEnable())
                      && Objects.isNull(p.getApplicationPolicy()));
    }

    private void verifyNetwork(String networkId) {
      assertThat(repositoryUtil.find(Network.class, networkId))
          .isNotNull()
          .matches(
              n ->
                  Boolean.FALSE.equals(
                          n.getWlan().getAdvancedCustomization().getApplicationPolicyEnable())
                      && Objects.isNull(
                          n.getWlan().getAdvancedCustomization().getApplicationPolicy()));
    }
  }

  @Nested
  class TierChangeHandlingForLbs {

    private LbsServerProfile lbsServerProfile;
    private Venue venueWithLbs;
    private Venue venueWithoutLbs;

    @BeforeEach
    public void prepareData(Tenant tenant) {
      lbsServerProfile = Generators.lbsServerProfile().setTenant(always(tenant)).generate();
      lbsServerProfile = repositoryUtil.createOrUpdate(lbsServerProfile, tenant.getId());

      venueWithLbs = VenueTestFixture.randomVenue(tenant);
      venueWithLbs.setLbsServerProfile(lbsServerProfile);
      venueWithLbs = repositoryUtil.createOrUpdate(venueWithLbs, tenant.getId());
      venueWithoutLbs = VenueTestFixture.randomVenue(tenant);
      venueWithoutLbs = repositoryUtil.createOrUpdate(venueWithoutLbs, tenant.getId());
    }

    @Test
    void changeTierFromEssentialToCore_ShouldGenerateLbsSubtask(Tenant tenant) throws Exception {
      final var requestId = randomTxId();
      final var message =
          TenantUpdateEvent.newBuilder()
              .setTenantDetails(
                  TenantDetails.newBuilder()
                      .setTenantId(tenant.getId())
                      .setTenantName(tenant.getName())
                      .setAccountTier(CORE_TIER)
                      .setPreviousStateTenantDetails(
                          PreviousStateTenantDetails.newBuilder()
                              .setAccountTier(ESSENTIAL_TIER)
                              .build())
                      .build())
              .build();

      messageUtil.sendUpdateTenant(requestId, message);

      var asyncJobMessages =
          messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId(), requestId);
      assertThat(asyncJobMessages)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(WifiAsyncJob::hasTenantTierChangedSubtaskJob)
          .extracting(WifiAsyncJob::getTenantTierChangedSubtaskJob)
          .matches(
              subtask ->
                  ESSENTIAL_TIER.equals(subtask.getFromTier())
                      && CORE_TIER.equals(subtask.getToTier())
                      && LbsTierChangeListener.VenueLbsSubtask.class.getSimpleName()
                      .equals(subtask.getSubtaskType()));

      final var subTask = new ObjectMapper().readValue(
          asyncJobMessages.getPayload().getTenantTierChangedSubtaskJob().getSubtaskContent(),
          LbsTierChangeListener.VenueLbsSubtask.class);
      var venueIds = subTask.venueIds();
      assertThat(venueIds).contains(venueWithLbs.getId());
      assertThat(venueIds).doesNotContain(venueWithoutLbs.getId());

      // ddccm
      var ddccmMessages = messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId());
      assertThat(ddccmMessages).isNotNull();
      assertThat(ddccmMessages.getPayload()).isNotNull();
      assertThat(ddccmMessages.getPayload().getOperationsList())
          .filteredOn(e -> e.hasVenue()).hasSize(1)
          .filteredOn(e -> e.getVenue().hasLocationBasedService())
          .isEmpty();

      // cmnCfg
      var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId());
      assertThat(cmnCfgCollectorMessage).isNotNull();
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();
      assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
          .filteredOn(op -> Index.VENUE.equals(op.getIndex()))
          .isEmpty();

      assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
          .filteredOn(op -> Index.LBS_SERVER_PROFILE_INDEX_NAME.equals(op.getIndex()))
          .hasSize(1)
          .allMatch(op -> op.getOpType() == OpType.MOD)
          .allMatch(
              op -> op.getDocMap().get(Key.VENUE_IDS).getListValue().getValuesList().isEmpty());
    }
  }

  @Nested
  class TierChangeHandlingForWorkflowNetwork {

    private Venue venue1;

    private GuestNetwork workflowNetwork;
    private GuestNetwork anotherWorkflowNetwork;
    private GuestNetwork nonWorkflowNetwork;
    private GuestNetwork workflowNetworkOweMaster;
    private GuestNetwork workflowNetworkOweSlave;

    private NetworkVenue workflowNetworkVenue;
    private NetworkVenue anotherWorkflowNetworkVenue;
    private NetworkVenue anotherWorkflowNetworkVenue1;

    @BeforeEach
    void prepareData(Tenant tenant, Venue venue) {
      // Regular workflow network
      workflowNetwork = createGuestNetwork(tenant, true, WlanSecurityEnum.Open, true);

      // Another regular workflow network for batch testing
      anotherWorkflowNetwork = createGuestNetwork(tenant, true, WlanSecurityEnum.Open, true);

      // Non-workflow guest network (shouldn't be affected)
      nonWorkflowNetwork = createGuestNetwork(tenant, false, WlanSecurityEnum.Open, true);

      // OWE Transition networks for workflow
      workflowNetworkOweMaster = createGuestNetwork(tenant, true, WlanSecurityEnum.OWETransition, false);
      workflowNetworkOweSlave = createGuestNetwork(tenant, true, WlanSecurityEnum.OWETransition, false);
      workflowNetworkOweMaster.setIsOweMaster(true);
      workflowNetworkOweMaster.setOwePairNetworkId(workflowNetworkOweSlave.getId());
      workflowNetworkOweSlave.setOwePairNetworkId(workflowNetworkOweMaster.getId());
      repositoryUtil.createOrUpdate(workflowNetworkOweMaster, tenant.getId());
      repositoryUtil.createOrUpdate(workflowNetworkOweSlave, tenant.getId());

      // Create network venue relationships
      workflowNetworkVenue = repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(workflowNetwork, venue), tenant.getId());

      anotherWorkflowNetworkVenue = repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(anotherWorkflowNetwork, venue), tenant.getId());

      venue1 = VenueTestFixture.randomVenue(tenant);
      venue1 = repositoryUtil.createOrUpdate(venue1, tenant.getId());
      anotherWorkflowNetworkVenue1 = repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(anotherWorkflowNetwork, venue1), tenant.getId());
    }

    @Test
    void changeTierFromEssentialToCore_NoImpactOnWorkflow(Tenant tenant) {
      // Setup - create tenant update event from Essential to Core tier
      final var requestId = randomTxId();
      final var tierUpdateMessage = createTierChangeEvent(
          tenant.getId(),
          tenant.getName(),
          ESSENTIAL_TIER,
          CORE_TIER);

      // Act - send tenant update message
      messageUtil.sendUpdateTenant(requestId, tierUpdateMessage);

      // Assert - verify that workflow networks are NOT affected
      // No AsyncJob messages should be sent
      assertThat(messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId(), requestId))
          .isNull();

      // No DDCCM messages should be sent
      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNull();

      // No ViewModel messages should be sent
      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNull();

      // All networks should still exist in the database
      assertThat(repositoryUtil.find(Network.class, workflowNetwork.getId()))
          .isNotNull();
      assertThat(repositoryUtil.find(Network.class, anotherWorkflowNetwork.getId()))
          .isNotNull();
    }

    @Test
    @SneakyThrows
    void changeTierFromProfessionalToCore_ShouldDeleteWorkflowNetworks(Tenant tenant, Venue venue) {
      final var requestId = randomTxId();
      final var tierUpdateMessage = createTierChangeEvent(
          tenant.getId(),
          tenant.getName(),
          PROFESSIONAL_TIER,
          CORE_TIER);

      messageUtil.sendUpdateTenant(requestId, tierUpdateMessage);

      // Verify AsyncJob message was sent
      var deactivateVenueAsyncJobMsg = messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId(), requestId);
      assertThat(deactivateVenueAsyncJobMsg)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(WifiAsyncJob::hasTenantTierChangedSubtaskJob)
          .extracting(WifiAsyncJob::getTenantTierChangedSubtaskJob)
          .matches(
              subtask ->
                  PROFESSIONAL_TIER.equals(subtask.getFromTier()) &&
                  CORE_TIER.equals(subtask.getToTier()) &&
                  getSubtaskType(WorkflowNetworkTierChangeListener.WorkflowNetworkSubtask.class)
                      .equals(subtask.getSubtaskType()));

      // Verify subtask content contains workflow networks and template networks
      var deactivateVenueSubtaskContent = getSubtaskContent(deactivateVenueAsyncJobMsg);
      assertThat(deactivateVenueSubtaskContent.operation()).isEqualTo(DEACTIVATE_VENUE);

      var deactivateVenue1SubtaskContent = getSubtaskContent(
          messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId(), requestId));
      assertThat(deactivateVenue1SubtaskContent.operation()).isEqualTo(DEACTIVATE_VENUE);

      var deleteNetworksSubtaskContent = getSubtaskContent(
          messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId(), requestId));
      assertThat(deleteNetworksSubtaskContent.operation()).isEqualTo(DELETE_NETWORK);
      assertThat(deleteNetworksSubtaskContent.networkIds())
          .contains(workflowNetwork.getId(),
              anotherWorkflowNetwork.getId(),
              workflowNetworkOweMaster.getId(),
              workflowNetworkOweSlave.getId())
          .doesNotContain(nonWorkflowNetwork.getId());

      // Verify DDCCM messages exist
      var ddccmMessages = messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId());
      assertNotNull(ddccmMessages.getPayload());
      assertNotNull(ddccmMessages.getPayload().getOperationsList());
      assertThat(ddccmMessages.getPayload().getOperationsList())
          .isNotEmpty()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
          .filteredOn(op -> op.hasWlanVenue())
          .filteredOn(op -> op.getWlanVenue().getVenueId().equals(venue.getId()))
          .allMatch(op -> op.getAction() == Action.DELETE);

      // Verify ViewModel collector messages exist
      var deactivateViewModelMsg = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId());
      assertThat(deactivateViewModelMsg)
          .isNotNull()
          .satisfies(msg -> {
            assertThat(msg.getPayload().getOperationsList())
                .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                .filteredOn(op -> op.getOpType() == OpType.DEL)
                .hasSizeGreaterThanOrEqualTo(1);
          });

      var deactivateViewmodelMsg2 = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId());
      assertThat(deactivateViewmodelMsg2).isNotNull();

      var deleteViewModelMsg = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId());
      assertThat(deleteViewModelMsg)
          .isNotNull()
          .satisfies(msg -> {
            assertThat(msg.getPayload().getOperationsList())
                .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
                .hasSize(4)
                .allMatch(op -> op.getOpType() == OpType.DEL)
                .extracting(op -> op.getId())
                .containsExactlyInAnyOrder(
                    workflowNetwork.getId(),
                    anotherWorkflowNetwork.getId(),
                    workflowNetworkOweMaster.getId(),
                    workflowNetworkOweSlave.getId());
          });
      // Non-workflow networks should remain untouched
      assertThat(repositoryUtil.find(Network.class, nonWorkflowNetwork.getId())).isNotNull();
    }

    private static WorkflowNetworkSubtask getSubtaskContent(
        KafkaProtoMessage<WifiAsyncJob> asyncJobMsg_venue1) throws JsonProcessingException {
      return new ObjectMapper().readValue(
          asyncJobMsg_venue1.getPayload().getTenantTierChangedSubtaskJob().getSubtaskContent(),
          WorkflowNetworkSubtask.class);
    }

    private GuestNetwork createGuestNetwork(
        Tenant tenant, boolean isWorkflow, WlanSecurityEnum wlanSecurity, boolean saveToDb) {
      GuestNetwork network = Generators.guestNetwork().generate();
      if (isWorkflow) {
        network.setWorkflowProfileId(randomId());
      }
      network.setWlan(Generators.wlan().generate());
      network.getWlan().setNetwork(network);
      network.getWlan().setWlanSecurity(wlanSecurity);
      network.getWlan().getAdvancedCustomization().setApplicationVisibilityEnabled(false);
      network.setGuestPortal(Generators.guestPortal(GuestNetworkTypeEnum.Workflow).generate());
      network.getGuestPortal().setNetwork(network);
      return saveToDb ? repositoryUtil.createOrUpdate(network, tenant.getId()): network;
    }

    private TenantUpdateEvent createTierChangeEvent(String tenantId, String tenantName,
        String fromTier, String toTier) {
      return TenantUpdateEvent.newBuilder()
          .setTenantDetails(
              TenantDetails.newBuilder()
                  .setTenantId(tenantId)
                  .setTenantName(tenantName)
                  .setAccountTier(toTier)
                  .setPreviousStateTenantDetails(
                      PreviousStateTenantDetails.newBuilder()
                          .setAccountTier(fromTier)
                          .build())
                  .build())
          .build();
    }
  }
}

