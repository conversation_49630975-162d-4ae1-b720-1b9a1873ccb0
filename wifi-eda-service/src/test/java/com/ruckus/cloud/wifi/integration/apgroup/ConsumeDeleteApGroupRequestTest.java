package com.ruckus.cloud.wifi.integration.apgroup;

import static com.ruckus.cloud.wifi.service.impl.ExtendedApGroupServiceCtrlImpl.DEFAULT_AP_GROUP_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.header.Header;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@Tag("ApGroupTest")
@WifiIntegrationTest
public class ConsumeDeleteApGroupRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class whenConsumeDeleteApGroupRequest {

    @Test
    void thenDeleteApGroup_withoutAps(Venue venue, @DefaultApGroup ApGroup defaultApGroup)
        throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var tenantId = defaultApGroup.getTenant().getId();

      repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(defaultApGroup.getVenue(), c -> c.setId(apGroupId)),
          tenantId, randomTxId());

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.DELETE_AP_GROUP, userName,
          requestParams, "");

      validateResult(venue.getTenant().getId(), requestId, venue.getId(), defaultApGroup.getId(),
          apGroupId, Collections.emptyList(), null);
    }

    @Test
    void thenDeleteApGroup_withAp(Venue venue, @DefaultApGroup ApGroup defaultApGroup, Ap ap)
        throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var tenantId = defaultApGroup.getTenant().getId();

      ApGroup apGroup = ApGroupTestFixture.randomApGroup(defaultApGroup.getVenue(), c -> {
        c.setId(apGroupId);
        c.setAps(List.of(ap));
      });
      repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
      ap.setApGroup(apGroup);
      repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.DELETE_AP_GROUP, userName,
          requestParams, "");

      validateResult(venue.getTenant().getId(), requestId, venue.getId(), defaultApGroup.getId(),
          apGroupId, List.of(ap.getId()), null);
    }

    @Test
    void thenDeleteApGroup_deleteNetworkApGroupAndRadios(Venue venue,
        @DefaultApGroup ApGroup defaultApGroup, Ap ap,
        @OpenNetwork Network network, NetworkVenue networkVenue)
        throws InvalidProtocolBufferException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apGroupId = randomId();
      final var networkApGroupId = randomId();
      final var tenantId = defaultApGroup.getTenant().getId();

      ApGroup apGroup = ApGroupTestFixture.randomApGroup(defaultApGroup.getVenue(), c -> {
        c.setId(apGroupId);
        c.setAps(List.of(ap));
      });
      NetworkApGroup networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
          apGroup, c -> c.setId(networkApGroupId));
      NetworkApGroupRadio networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
          networkApGroup);
      repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
      ap.setApGroup(apGroup);
      repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(networkApGroup, tenantId, randomTxId());
      repositoryUtil.createOrUpdate(networkApGroupRadio, tenantId, randomTxId());

      RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("apGroupId", apGroupId);

      messageUtil.sendWifiCfgRequest(
          tenantId, requestId, CfgAction.DELETE_AP_GROUP, userName,
          requestParams, StringUtils.EMPTY);

      validateResult(venue.getTenant().getId(), requestId, venue.getId(), defaultApGroup.getId(),
          apGroupId, List.of(ap.getId()), networkVenue.getNetwork().getId());
      validateNetworkApGroupAndRadios(networkApGroup, networkApGroupRadio);
    }
  }

  void validateResult(String tenantId, String requestId,
      String venueId, String defaultApGroupId, String apGroupId, List<String> apIds, String networkId)
      throws InvalidProtocolBufferException {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    // validate AP Group cmnCfgCollectorMessage
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
        .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
        .isNotEmpty().hasSize(apIds.isEmpty() ? 1 : 2)
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> apGroupId.equals(op.getId()))
              .isNotEmpty().singleElement()
              .extracting(Operations::getOpType).isEqualTo(OpType.DEL);
          if (!apIds.isEmpty()) {
            assertThat(ops)
                .filteredOn(op -> defaultApGroupId.equals(op.getId()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                      .containsEntry(Key.ID, ValueUtils.stringValue(defaultApGroupId))
                      .containsEntry(Key.NAME, ValueUtils.stringValue(DEFAULT_AP_GROUP_NAME.trim()))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                      .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(true));
                });
          }
        });

    // validate AP cmnCfgCollectorMessage, should send empty deviceGroupName when we delete AP Group in EDA
    if (!apIds.isEmpty()) {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(tenantId))
          .matches(p -> p.getRequestId().equals(requestId))
          .matches(p ->
              p.getOperationsList().stream().filter(o -> EsConstants.Index.DEVICE.equals(o.getIndex())).count()
                  == 1)
          .extracting(p -> p.getOperationsList().stream().filter(o -> EsConstants.Index.DEVICE.equals(o.getIndex()))
              .findFirst().get())
          .matches(p -> p.getOpType() == OpType.MOD)
          .matches(p -> p.getDocMap().get("deviceGroupName").getStringValue().equals(""))
          .matches(p -> p.getId().equals(apIds.get(0))); // Empty string
    }

    // validate Network cmnCfgCollectorMessage
    if (networkId != null) {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
          .isNotEmpty().hasSize(apIds.size())
          .allSatisfy(op -> {
            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
            assertThat(op.getDocOrThrow(Key.ID))
                .isEqualTo(ValueUtils.stringValue(networkId));
          });
    } else {
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
          .isEmpty();
    }

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull();
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(Status.OK))
        .matches(p -> p.getStep().equals(ApiFlowNames.DELETE_AP_GROUP))
        .matches(p -> p.getEventDate() != null);

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(requestId);

    assertThat(activityImpactedMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);

    assertThat(activityImpactedMessage.getPayload())
        .matches(p -> p.getDeviceIdsList().size() == apIds.size())
        .matches(p -> p.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID));
  }

  void validateNetworkApGroupAndRadios(NetworkApGroup networkApGroup,
      NetworkApGroupRadio networkApGroupRadio) {

    final var networkApGroupInDb = repositoryUtil.find(NetworkApGroup.class,
        networkApGroup.getId());
    assertThat(networkApGroupInDb).isNull();

    final var networkApGroupRadioInDb = repositoryUtil.find(NetworkApGroupRadio.class,
        networkApGroupRadio.getId());
    assertThat(networkApGroupRadioInDb).isNull();
  }
}
