package com.ruckus.cloud.wifi.integration.directoryserverprofile;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.DirectoryServerProfileTestFixture.randomDirectoryServerProfile;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanStep;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("DirectoryServerProfileTest")
@WifiIntegrationTest
class ConsumeActivateDirectoryServerProfileOnWifiNetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void givenValidActivation(Tenant tenant) {
    var profile =
        repositoryUtil.createOrUpdate(
            randomDirectoryServerProfile(tenant, p -> {
            }), tenant.getId(), randomTxId());

    GuestNetwork guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(tenant,
        n -> {
          n.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.Directory);
        });
    repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());

    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        txCtxExtension.getRequestId(),
        CfgExtendedAction.ACTIVATE_DIRECTORY_SERVER_PROFILE_ON_WIFI_NETWORK,
        randomName(),
        new RequestParams()
            .addPathVariable("wifiNetworkId", guestNetwork.getId())
            .addPathVariable("directoryServerProfileId", profile.getId()),
        "");

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(
            ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .hasSize(2)
        .filteredOn(o -> o.getId().equals(profile.getId()))
        .singleElement()
        .matches(o -> o.getOpType() == OpType.MOD)
        .extracting(
            Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
        .extractingByKey(Key.WIFI_NETWORK_IDS)
        .isNotNull()
        .extracting(Value::getListValue)
        .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
        .hasSize(1)
        .first()
        .extracting(Value::getStringValue)
        .isEqualTo(guestNetwork.getId());

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
        .matches(
            a -> a.getStep()
                .equals(ExecutionPlanStep.ACTIVATE_DIRECTORY_SERVER_PROFILE_ON_WIFI_NETWORK));
  }

  @Test
  void givenValidActivationWithNetworkVenueActivatedAlready(Tenant tenant) {
    var profile1 =
        repositoryUtil.createOrUpdate(
            randomDirectoryServerProfile(tenant, p -> {
            }), tenant.getId(), randomTxId());
    var profile2 =
        repositoryUtil.createOrUpdate(
            randomDirectoryServerProfile(tenant, p -> {
            }), tenant.getId(), randomTxId());
    GuestNetwork guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(tenant,
        n -> {
          n.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.Directory);
          n.setDirectoryServerProfile(profile1);
        });
    repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());

    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        txCtxExtension.getRequestId(),
        CfgExtendedAction.ACTIVATE_DIRECTORY_SERVER_PROFILE_ON_WIFI_NETWORK,
        randomName(),
        new RequestParams()
            .addPathVariable("wifiNetworkId", guestNetwork.getId())
            .addPathVariable("directoryServerProfileId", profile2.getId()),
        "");

    var operationsList =
        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(3);
    operationsList
        .filteredOn(o -> o.getOpType() == OpType.MOD && o.getId().equals(profile1.getId()))
        .hasSize(1)
        .first()
        .matches(
            o ->
                o.getDocMap().get(Key.WIFI_NETWORK_IDS).getListValue().getValuesList().isEmpty());
    operationsList
        .filteredOn(o -> o.getOpType() == OpType.MOD && o.getId().equals(profile2.getId()))
        .hasSize(1)
        .first()
        .matches(
            o ->
                o.getDocMap().get(EsConstants.Key.WIFI_NETWORK_IDS).getListValue().getValuesList()
                    .size() == 1);

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
        .matches(
            a -> a.getStep()
                .equals(ExecutionPlanStep.ACTIVATE_DIRECTORY_SERVER_PROFILE_ON_WIFI_NETWORK));
  }
}
