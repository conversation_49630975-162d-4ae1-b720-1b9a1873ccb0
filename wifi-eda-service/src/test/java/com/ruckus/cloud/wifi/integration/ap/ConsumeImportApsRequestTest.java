package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.entitlement.Action;
import com.ruckus.cloud.entitlement.ProtoDeviceType;
import com.ruckus.cloud.entitlement.ProtoRequestSource;
import com.ruckus.cloud.entitlement.operation.eda.EdaOperation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.DeviceType;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.RequestType;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanStep;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.client.file.FileDto;
import com.ruckus.cloud.wifi.client.file.ImportFileClient;
import com.ruckus.cloud.wifi.core.error.SimpleError;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.api.rest.ErrorResponse;
import com.ruckus.cloud.wifi.service.core.exception.ImportException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.kafka.common.header.Header;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
@Slf4j
public class ConsumeImportApsRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private ImportFileClient importFileClient;
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  class whenConsumeImportApsRequest {

    @BeforeEach
    public void setup() {
      clearInvocations(importFileClient);
      when(importFileClient.getDownloadUrl(anyString(), anyString())).thenReturn(
          new FileDto(randomId(), "http://www.download.url"));
    }

    @Test
    void thenShouldDownloadCsvFile_sendActivityFail_WhenFileNotFound() {
      final var tenantId = randomId();
      final var requestId = randomTxId();
      final var userName = randomName();
      final var fileName = "aps_import.csv";
      final var fileId = randomId();
      final var signUrl = "http://test.signed.url";
      final var fileDto = new FileDto(fileName, fileId, signUrl);

      assertThrows(RuntimeException.class, () -> messageUtil.sendWifiCfgRequest(
          tenantId,
          requestId,
          CfgExtendedAction.IMPORT_APS_CSV,
          userName,
          new RequestParams(),
          fileDto));
      validateFileClientAndImportApsActivityMessage(tenantId, requestId, null);
      validatePostProcessImportApsActivityMessage(tenantId, requestId);
    }

    @Test
    void thenShouldSaveAps(Tenant tenant, Network network) throws IOException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var fileName = "aps_import_with_optional_apgroup.csv";
      final var fileId = randomId();
      final var signUrl = "http://test.signed.url";
      final var fileDto = new FileDto(fileName, fileId, signUrl);
      final var venueId1 = "875356424900";
      final var venueId2 = "875356424901";
      String filePath = String.format("/tmp/%s_aps_import_with_optional_apgroup.csv", requestId);
      File source = new File("./src/test/resources/csv/aps_import_with_optional_apgroup.csv");
      File dest = new File(filePath);
      FileUtils.copyFile(source, dest);
      log.warn("Write tmp file [{}]", filePath);

      Venue venue1 = new Venue(venueId1);
      venue1.setTenant(tenant);
      venue1.setName("My Venue id=875356424900");
      Venue venue2 = new Venue(venueId2);
      venue2.setTenant(tenant);
      venue2.setName("My Venue id=875356424901");
      repositoryUtil.createOrUpdate(venue1, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(venue2, tenant.getId(), randomTxId());

      NetworkVenue networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue1);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      ApGroup apGroup = ApGroupTestFixture.randomApGroup(venue1, c -> c.setName("New Ap Group"));
      NetworkApGroup networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
          apGroup);
      NetworkApGroupRadio networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
          networkApGroup);
      repositoryUtil.createOrUpdate(apGroup, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkApGroup, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId());
      venue1.setApGroups(List.of(apGroup));
      repositoryUtil.createOrUpdate(venue1, tenant.getId(), randomTxId());

      ApGroup defaultApGroup = ApGroupTestFixture.randomApGroup(venue2,
          (group) -> group.setIsDefault(true));
      repositoryUtil.createOrUpdate(defaultApGroup, tenant.getId(), randomTxId());
      venue2.setApGroups(List.of(defaultApGroup));
      repositoryUtil.createOrUpdate(venue2, tenant.getId(), randomTxId());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgExtendedAction.IMPORT_APS_CSV,
          userName,
          new RequestParams(),
          fileDto);

      validateDatabaseAndKafkaMessage(tenant.getId(), requestId);
      validateCmnCfgCollectorMessage(tenant.getId(), requestId);
    }

    @Test
    void shouldSendErrorMessageToActivity_whenFileValidationFailed() throws IOException {
      final var expectedErrorMsg = "Serial number is invalid. In row: 3";

      final var tenantId = randomId();
      final var requestId = randomTxId();
      final var userName = randomName();
      final var fileName = "aps_import_duplicate_serial_numbers.csv";
      final var fileId = randomId();
      final var signUrl = "http://test.signed.url";
      final var fileDto = new FileDto(fileName, fileId, signUrl);
      String filePath = String.format("/tmp/%s_%s", requestId, fileName);
      File source = new File(String.format("./src/test/resources/csv/%s", fileName));
      File dest = new File(filePath);
      FileUtils.copyFile(source, dest);

      RuntimeException ex = assertThrows(RuntimeException.class,
          () -> messageUtil.sendWifiCfgRequest(
              tenantId,
              requestId,
              CfgExtendedAction.IMPORT_APS_CSV,
              userName,
              new RequestParams(),
              fileDto));

      assertEquals(expectedErrorMsg, ex.getCause().getCause().getMessage());
      assertEquals(ImportException.class, ex.getCause().getCause().getClass());

      validateFileClientAndImportApsActivityMessage(tenantId, requestId, expectedErrorMsg);
    }
  }

  void validateDatabaseAndKafkaMessage(String tenantId, String requestId) {
    final String apSerial1 = "875356424900";
    final String apSerial2 = "875356424901";
    final String apSerial3 = "875356424902";
    var ap1 = repositoryUtil.find(Ap.class, apSerial1);
    var ap2 = repositoryUtil.find(Ap.class, apSerial2);
    var ap3 = repositoryUtil.find(Ap.class, apSerial3);
    List<String> apSerials = Arrays.asList(apSerial1, apSerial2, apSerial3);
    List<String> venueIds = Arrays.asList("875356424900", "875356424901", "875356424901");
    List<Ap> aps = Arrays.asList(ap1, ap2, ap3);
    for (int i = 0; i < aps.size(); i++) {
      int finalI = i;
      assertThat(aps.get(i))
          .isNotNull()
          .matches(a -> Objects.equals(aps.get(finalI).getId(), apSerials.get(finalI)))
          .matches(a -> Objects.equals(a.getName(), "AP-".concat(apSerials.get(finalI))))
          .extracting(Ap::getApGroup)
          .isNotNull()
          .extracting(ApGroup::getVenue)
          .isNotNull()
          .matches(v -> Objects.equals(v.getId(), venueIds.get(finalI)));
    }

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    final var payload = activityCfgChangeRespMessage.getPayload();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange requestId should match payload: [%s]", payload)
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange tenantId should match")
        .isEqualTo(tenantId);

    assertThat(payload)
        .matches(p -> p.getStatus().equals(Status.OK))
        .matches(p -> p.getStep().equals(ExecutionPlanStep.IMPORT_APS))
        .matches(p -> p.getEventDate() != null);

    assertThat(payload.getEntityIdList())
        .hasSize(3)
        .contains(apSerial1, apSerial2, apSerial3);

    final var entMessage = messageCaptors.getEntitlementDeviceOperationMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(entMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("entitleDeviceOperationRequest requestId should match")
        .isEqualTo(requestId);

    assertThat(entMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("entitleDeviceOperationRequest tenantId should match")
        .isEqualTo(tenantId);

    assertThat(entMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_DEVICE_TYPE))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(ProtoDeviceType.WIFI.name());

    assertThat(entMessage.getPayload()).isNotNull();

    assertThat(entMessage.getPayload())
        .matches(p -> p.getTenantId().equals(tenantId))
        .matches(p -> p.getRequestId().equals(requestId))
        .matches(p -> p.getRequestSource() == ProtoRequestSource.WIFI_SERVICE)
        .matches(p -> p.getOperationCount() == 3)
        .extracting(p -> p.getOperation(0))
        .matches(p -> p.getAction() == Action.CONSUME)
        .extracting(EdaOperation::getDevice)
        .matches(p -> p.getDeviceType() == ProtoDeviceType.WIFI);

    final var drsMessage = messageCaptors.getDrsMessageCaptor().getValue(tenantId, requestId);
    assertThat(drsMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(DeviceRegistrar.Header.REQUEST_ID.name()))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("Drs message requestId should match")
        .isEqualTo(requestId);

    assertThat(drsMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(DeviceRegistrar.Header.TENANT_ID.name()))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("Drs message tenantId should match")
        .isEqualTo(tenantId);

    assertThat(drsMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(DeviceRegistrar.Header.SERVICE_NAME.name()))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(DeviceRegistrar.SERVICES.WIFI_CONSUMER_SERVICE.name());

    assertThat(drsMessage.getPayload())
        .matches(p -> p.getType().equals(RequestType.CREATE))
        .extracting(DeviceRegistrar.Request::getDeviceCreateRequest)
        .matches(r -> r.getTenant().equals(tenantId))
        .matches(r -> r.getDevicesList().size() == 3)
        .extracting(r -> r.getDevices(0))
        .matches(d -> d.getType().equals(DeviceType.WIFI));
  }

  void validateFileClientAndImportApsActivityMessage(String tenantId, String requestId,
      String errorMsg) {
    verify(importFileClient, times(1))
        .getDownloadUrl(anyString(), anyString());
    var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertEquals("acx.activity.cfg-change-resp", activityCfgChangeRespMessage.getTopic());

    assertThat(activityCfgChangeRespMessage).isNotNull();
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange requestId should match")
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange tenantId should match")
        .isEqualTo(tenantId);

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(Status.FAIL))
        .matches(p -> p.getStep().equals(ExecutionPlanStep.IMPORT_APS))
        .matches(p -> p.getEventDate() != null)
        .matches(p -> {
          try {
            SimpleError error = new ObjectMapper().readValue(p.getError(), SimpleError.class);
            if (errorMsg != null) {
              assertEquals(errorMsg, error.getMessage());
            }
            return error.getCode().equals(Errors.WIFI_10000.code());
          } catch (IOException e) {
            throw new RuntimeException(e);
          }
        });
  }

  void validatePostProcessImportApsActivityMessage(String tenantId, String requestId) {
    var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage.getTopic())
        .isEqualTo("acx.activity.cfg-change-resp");
    assertThat(activityCfgChangeRespMessage).isNotNull();
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange requestId should match")
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange tenantId should match")
        .isEqualTo(tenantId);

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(Status.FAIL))
        .matches(p -> p.getStep().equals(ExecutionPlanStep.POST_PROCESSED_IMPORT_APS))
        .matches(p -> p.getEventDate() != null)
        .matches(p -> {
          try {
            SimpleError error = new ObjectMapper().readValue(p.getError(),
                SimpleError.class);
            return error.getCode().equals(Errors.WIFI_10000.code());
          } catch (IOException e) {
            throw new RuntimeException(e);
          }
        });
  }

  void validateCmnCfgCollectorMessage(String tenantId, String requestId) {
    List<String> apSerials = Arrays.asList("875356424900", "875356424901", "875356424902");
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    // validate AP cmnCfgCollectorMessage
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
        .isNotEmpty().hasSize(apSerials.size())
        .allSatisfy(op -> {
          assertThat(op.getOpType()).isEqualTo(OpType.ADD);
          assertTrue(op.getDocMap().containsKey(Key.DEVICE_GROUP_ID));
          assertTrue(op.getDocMap().containsKey(Key.DEVICE_GROUP_NAME));
        })
        .extracting(Operations::getId)
        .containsExactlyInAnyOrderElementsOf(apSerials);
  }
}
