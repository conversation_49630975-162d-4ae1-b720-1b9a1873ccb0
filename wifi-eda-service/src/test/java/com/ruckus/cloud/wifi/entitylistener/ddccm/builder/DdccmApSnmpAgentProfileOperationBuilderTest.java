package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV2Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV3Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SnmpNotificationTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SnmpPrivacyProtocolEnum;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
public class DdccmApSnmpAgentProfileOperationBuilderTest {

  @Autowired
  private DdccmApSnmpAgentProfileOperationBuilder builder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyMspTenantId");

  @Test
  public void testAddApSnmpAgentProfile() {
    var apSnmpAgentProfile = apSnmpAgentProfile();

    var txChanges = spy(TxChangesReader.class);

    doReturn(List.of(new NewTxEntity<>(apSnmpAgentProfile))).when(txChanges).getNewEntities();

    var operations = builder.build(new NewTxEntity<>(apSnmpAgentProfile), emptyTxChanges());

    assertEquals(1, operations.size());

    assertApSnmpAgentProfileOperation(Action.ADD, apSnmpAgentProfile, Objects.requireNonNull(
        operations.stream().filter(op -> apSnmpAgentProfile.getId().equals(op.getId())).findFirst()
            .orElse(null)));
  }

  @Test
  public void testModifyApSnmpAgentProfile() {
    var apSnmpAgentProfile = apSnmpAgentProfile();

    var txChanges = spy(TxChangesReader.class);

    doReturn(List.of(new ModifiedTxEntity<>(apSnmpAgentProfile, Collections.emptySet())))
        .when(txChanges).getModifiedEntities();

    var operations = builder
        .build(new ModifiedTxEntity<>(apSnmpAgentProfile, Collections.emptySet()),
            emptyTxChanges());

    assertEquals(1, operations.size());

    assertApSnmpAgentProfileOperation(Action.MODIFY, apSnmpAgentProfile, Objects.requireNonNull(
        operations.stream().filter(op -> apSnmpAgentProfile.getId().equals(op.getId())).findFirst()
            .orElse(null)));
  }

  @Test
  public void testDeleteApSnmpAgentProfile() {
    var apSnmpAgentProfile = apSnmpAgentProfile();

    var txChanges = spy(TxChangesReader.class);

    doReturn(List.of(new DeletedTxEntity<>(apSnmpAgentProfile))).when(txChanges)
        .getDeletedEntities();

    var operations = builder.build(new DeletedTxEntity<>(apSnmpAgentProfile), emptyTxChanges());

    assertEquals(1, operations.size());

    assertApSnmpAgentProfileOperation(Action.DELETE, apSnmpAgentProfile, Objects.requireNonNull(
        operations.stream().filter(op -> apSnmpAgentProfile.getId().equals(op.getId())).findFirst()
            .orElse(null)));
  }

  private ApSnmpAgentProfile apSnmpAgentProfile() {
    var apSnmpAgentProfile = new ApSnmpAgentProfile(genUUID());
    apSnmpAgentProfile.setPolicyName("name");
    var apSnmpV2Agent = new ApSnmpV2Agent();
    apSnmpV2Agent.setCommunityName("community");
    apSnmpV2Agent.setReadPrivilege(true);
    apSnmpV2Agent.setTrapPrivilege(true);
    apSnmpV2Agent.setNotificationType(SnmpNotificationTypeEnum.Inform);
    apSnmpV2Agent.setTargetAddr("1.1.1.1");
    apSnmpAgentProfile.setSnmpV2Agents(List.of(apSnmpV2Agent));
    var apSnmpV3Agent = new ApSnmpV3Agent();
    apSnmpV3Agent.setUserName("user");
    apSnmpV3Agent.setReadPrivilege(true);
    apSnmpV3Agent.setAuthPassword("password");
    apSnmpV3Agent.setPrivacyProtocol(SnmpPrivacyProtocolEnum.AES);
    apSnmpV3Agent.setPrivacyPassword("password");
    apSnmpAgentProfile.setSnmpV3Agents(List.of(apSnmpV3Agent));
    apSnmpAgentProfile.setTenant(new Tenant(TxCtxHolder.get().getTenant()));
    return apSnmpAgentProfile;
  }

  private void assertApSnmpAgentProfileOperation(Action action, ApSnmpAgentProfile profile,
      Operation operation) {
    var apSnmpAgentProfile = operation.getApSnmpAgentProfile();

    assertEquals(action, operation.getAction());
    assertNotNull(apSnmpAgentProfile);
    assertEquals(profile.getId(), apSnmpAgentProfile.getId());
    assertEquals(profile.getTenant().getId(), apSnmpAgentProfile.getTenantId());

    assertEquals(Action.DELETE != action ? 1 : 0, apSnmpAgentProfile.getSnmpV2AgentCount());
    assertEquals(Action.DELETE != action ? 1 : 0, apSnmpAgentProfile.getSnmpV3AgentCount());

    if (Action.DELETE != action) {
      assertEquals(profile.getPolicyName(), apSnmpAgentProfile.getName());
      assertSnmpV2Agents(profile.getSnmpV2Agents().get(0), apSnmpAgentProfile.getSnmpV2Agent(0));
      assertSnmpV3Agents(profile.getSnmpV3Agents().get(0), apSnmpAgentProfile.getSnmpV3Agent(0));
    }
  }

  private void assertSnmpV2Agents(ApSnmpV2Agent apSnmpV2Agent,
      com.ruckus.acx.ddccm.protobuf.wifi.ApSnmpV2Agent ddccmApSnmpV2Agent) {
    assertEquals(apSnmpV2Agent.getCommunityName(), ddccmApSnmpV2Agent.getName());
    assertEquals(apSnmpV2Agent.getReadPrivilege(),
        ddccmApSnmpV2Agent.getIsAllowedToRead());
    assertEquals(apSnmpV2Agent.getTrapPrivilege(),
        ddccmApSnmpV2Agent.getIsAllowedToNotify());
    if (apSnmpV2Agent.getTrapPrivilege()) {
      assertEquals(1, ddccmApSnmpV2Agent.getNotificationTargetCount());
      var notificationTarget = ddccmApSnmpV2Agent.getNotificationTarget(0);
      assertEquals(apSnmpV2Agent.getNotificationType().toString().toUpperCase(),
          notificationTarget.getNotificationType().toString());
      assertEquals(apSnmpV2Agent.getTargetAddr(), notificationTarget.getIp());
      assertEquals(apSnmpV2Agent.getTargetPort().intValue(), notificationTarget.getPort());
    }
  }

  private void assertSnmpV3Agents(ApSnmpV3Agent apSnmpV3Agent,
      com.ruckus.acx.ddccm.protobuf.wifi.ApSnmpV3Agent ddccmApSnmpV3Agent) {
    assertEquals(apSnmpV3Agent.getUserName(), ddccmApSnmpV3Agent.getName());
    assertEquals(apSnmpV3Agent.getReadPrivilege(),
        ddccmApSnmpV3Agent.getIsAllowedToRead());
    assertEquals(apSnmpV3Agent.getTrapPrivilege(),
        ddccmApSnmpV3Agent.getIsAllowedToNotify());
    assertEquals(apSnmpV3Agent.getAuthProtocol().toString(),
        ddccmApSnmpV3Agent.getAuthenticationType().toString());
    assertEquals(apSnmpV3Agent.getAuthPassword(), ddccmApSnmpV3Agent.getAuthenticationPassphrase());
    if (apSnmpV3Agent.getPrivacyProtocol() != SnmpPrivacyProtocolEnum.None) {
      assertEquals(apSnmpV3Agent.getPrivacyProtocol().toString(),
          ddccmApSnmpV3Agent.getPrivacyType().toString());
      assertTrue(ddccmApSnmpV3Agent.hasPrivacyPassphrase());
      assertEquals(apSnmpV3Agent.getPrivacyPassword(),
          ddccmApSnmpV3Agent.getPrivacyPassphrase().getValue());
    }
    if (apSnmpV3Agent.getTrapPrivilege()) {
      assertEquals(1, ddccmApSnmpV3Agent.getNotificationTargetCount());
      var notificationTarget = ddccmApSnmpV3Agent.getNotificationTarget(0);
      assertEquals(apSnmpV3Agent.getNotificationType().toString().toUpperCase(),
          notificationTarget.getNotificationType().toString());
      assertEquals(apSnmpV3Agent.getTargetAddr(), notificationTarget.getIp());
      assertEquals(apSnmpV3Agent.getTargetPort().intValue(), notificationTarget.getPort());
    }
  }

  private static String genUUID() {
    return UUID.randomUUID().toString();
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public DdccmApSnmpAgentProfileOperationBuilder ddccmApSnmpAgentProfileOperationBuilder() {
      var builder = spy(DdccmApSnmpAgentProfileOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }
  }
}
