package com.ruckus.cloud.wifi.integration.softgre;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.newLanPortAdoption;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice.ImpactDevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.LanPortSoftGreProfileSettingsGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.LanPortSoftGreProfileSettings;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.Objects;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SoftGreProfileTest")
@WifiIntegrationTest
@FeatureFlag(
    enable = {
      WIFI_ETHERNET_SOFTGRE_TOGGLE,
      ACX_UI_ETHERNET_TOGGLE,
    })
class ConsumeDeactivateSoftGreProfileOnVenueApModelLanPortRequestTest {

  private record VenueLanPortData(
      VenueApModelSpecificAttributes modelAttributes,
      VenueLanPort port,
      EthernetPortProfile profile) {}

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;
  @Autowired private LanPortAdoptionServiceImpl lanPortAdoptionService;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeDeactivateSoftGreProfileOnVenueApModelLanPortTest {

    private String softGreProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "2";
    private String venueLanPortId;

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        Generators.lanPortSoftGreProfileSettings();

    @BeforeEach
    void givenOnePersistedInDb(Venue venue, Tenant tenant) {
      venueId = venue.getId();
      var softGreProfile =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      softGreProfileId = softGreProfile.getId();
      var portData1 = createVenueLanPortData(venue, 3, apModel, portId, softGreProfile);
      venueLanPortId = portData1.port.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.DEACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {
      validateActivateSoftGreProfileToVenueApModelLanPortResult(
          CfgAction.DEACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT,
          venueId,
          apModel,
          portId,
          softGreProfileId,
          payload,
          venueLanPortId);
    }
  }

  private void validateActivateSoftGreProfileToVenueApModelLanPortResult(
      CfgAction apiAction,
      String venueId,
      String apModel,
      String portId,
      String softGreProfileId,
      LanPortSoftGreProfileSettings payload,
      String venueLanPortId) {

    validateVenueRepositoryData(
        apiAction, venueId, apModel, portId, softGreProfileId, payload, venueLanPortId);
    validateDdccmCfgRequestMessages(apiAction, softGreProfileId, venueLanPortId);
    validateCmnCfgCollectorMessages(apiAction, softGreProfileId);
    validateActivityMessages(apiAction);
  }

  private void validateVenueRepositoryData(
      CfgAction apiAction,
      String venueId,
      String apModel,
      String portId,
      String softGreProfileId,
      LanPortSoftGreProfileSettings payload,
      String venueLanPortId) {

    assertThat(venueId).isNotNull();

    if (Objects.requireNonNull(apiAction)
        == CfgAction.DEACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT) {
      final var allLanPortAdoption =
          repositoryUtil.findAll(LanPortAdoption.class, txCtxExtension.getTenantId());
      assertThat(allLanPortAdoption).isNotNull().matches(l -> l.size() == 1);
      final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, venueLanPortId);
      assertThat(venueLanPort)
          .extracting(VenueLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getSoftGreActivation)
          .isNull();
    } else {
      throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    }
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, String softGreProfileId) {
    if (apiAction == null || softGreProfileId == null) {
      messageCaptors
          .getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    var operations =
        cmnCfgCollectorMessage.getPayload().getOperationsList().stream()
            .filter(
                op ->
                    Index.SOFT_GRE_PROFILE_INDEX_NAME.equals(op.getIndex())
                        && op.getOpType() == OpType.MOD)
            .collect(Collectors.partitioningBy(op -> softGreProfileId.equals(op.getId())));
    assertThat(operations.get(true))
        .singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(
            docMap -> {
              assertThat(docMap.get(Key.VENUE_ACTIVATIONS).getListValue().getValuesList())
                  .isEmpty();
              assertThat(docMap.get(Key.AP_ACTIVATIONS).getListValue().getValuesList()).isEmpty();
            });
    assertThat(operations.get(false)).isEmpty();
  }

  private void validateDdccmCfgRequestMessages(
      CfgAction apiAction, String softGreProfileId, String apLanPortId) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();
    final VenueLanPort venueLanPort = repositoryUtil.find(VenueLanPort.class, apLanPortId);
    var inUseEthernetPortProfileId = venueLanPort.getLanPortAdoption().getEthernetPortProfileId();
    assertThatNoException()
        .isThrownBy(
            () -> {
              var operations =
                  assertThat(ddccmCfgRequestMessage.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList)
                      .asList()
                      .isNotEmpty()
                      .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast);

              operations
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                  .isNotEmpty()
                  .allSatisfy(
                      op -> {
                        assertThat(op)
                            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                            .matches(
                                venue ->
                                    venue.getCcmMultipleTunnel().getSoftGreSettingList().isEmpty())
                            .matches(
                                venue ->
                                    venue.getVenueApModelsList().stream()
                                        .anyMatch(
                                            model ->
                                                model.getLanPortList().stream()
                                                    .anyMatch(
                                                        lanPort ->
                                                            inUseEthernetPortProfileId.equals(
                                                                lanPort.getApLanPortProfileId()))));
                      });

              operations
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApLanPortProfile)
                  .filteredOn(operation -> operation.getAction() == Action.ADD)
                  .isNotEmpty()
                  .allSatisfy(
                      op ->
                          assertThat(op)
                              .extracting(
                                  com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                              .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                              .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                              .matches(
                                  commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                  .allSatisfy(op -> verifyAddAction(op, venueLanPort));
              operations
                  .filteredOn(operation -> operation.getAction() == Action.DELETE)
                  .allSatisfy(this::verifyDeleteAction);
            });
  }

  private void verifyAddAction(
      com.ruckus.acx.ddccm.protobuf.wifi.Operation operation, VenueLanPort venueLanPort) {
    assertThat(operation)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
        .matches(
            apLanPortProfile ->
                venueLanPort.getLanPortAdoption().getId().equals(apLanPortProfile.getName()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getLanPortAdoption()
                    .getEthernetPortProfileId()
                    .equals(apLanPortProfile.getId()))
        .matches(
            apLanPortProfile ->
                (venueLanPort.getApLanPortProfile().getType().name() + "PORT")
                    .equals(apLanPortProfile.getLanPortType().name()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(
            apLanPortProfile ->
                venueLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagId()));
  }

  private void verifyDeleteAction(com.ruckus.acx.ddccm.protobuf.wifi.Operation operation) {
    assertThat(operation)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
        .isNotNull();
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors
          .assertThat(
              kafkaTopicProvider.getActivityCfgChangeResp(),
              kafkaTopicProvider.getActivityImpacted())
          .doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(ConfigurationStatus::getEventDate)
                    .isNotNull());

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ImpactDevice::getDeviceIdsCount).isEqualTo(0);
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case DEACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT ->
          ApiFlowNames.DEACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_MODEL_LAN_PORT;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private EthernetPortProfile createEthernetPortProfile(int ethernetPortProfileId) {
    var ethernetPortProfile = new EthernetPortProfile();
    ethernetPortProfile.setApLanPortId(ethernetPortProfileId);
    ethernetPortProfile.setName(randomName());
    ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
    ethernetPortProfile.setUntagId((short) 2);
    ethernetPortProfile.setVlanMembers("2");
    ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
    return repositoryUtil.createOrUpdate(
        ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());
  }

  private VenueLanPortData createVenueLanPortData(
      Venue venue,
      int ethernetPortProfileId,
      String apModel,
      String portId,
      SoftGreProfile softGreProfile) {
    var apLanPortProfile = createEthernetPortProfile(ethernetPortProfileId);

    var modelAttributes = new VenueApModelSpecificAttributes();
    modelAttributes.setModel(apModel);
    modelAttributes.setVenue(venue);
    modelAttributes.setLanPorts(new ArrayList<>());
    modelAttributes =
        repositoryUtil.createOrUpdate(modelAttributes, txCtxExtension.getTenantId(), randomTxId());

    var port = new VenueLanPort();
    port.setApLanPortProfile(apLanPortProfile);
    port.setPortId(portId);
    port.setVenueApModelSpecificAttributes(modelAttributes);
    port = repositoryUtil.createOrUpdate(port, txCtxExtension.getTenantId(), randomTxId());

    LanPortAdoption adoption = new LanPortAdoption();
    SoftGreProfileLanPortActivation softGreProfileLanPortActivation = null;
    if (softGreProfile != null) {
      softGreProfileLanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .softGreProfileLanPortActivation()
              .setDhcpOption82Enabled(always(true))
              .setDhcpOption82Settings(
                  always(
                      com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                          .dhcpOption82Settings()
                          .generate()))
              .generate();
      softGreProfileLanPortActivation.setSoftGreProfile(softGreProfile);
    }

    adoption.setApLanPortProfile(apLanPortProfile);
    adoption.setEthernetPortProfileId(apLanPortProfile.getApLanPortId());
    adoption.setChecksum(
        lanPortAdoptionService.calculateChecksum(
            newLanPortAdoption(apLanPortProfile, softGreProfileLanPortActivation)));
    adoption = repositoryUtil.createOrUpdate(adoption, txCtxExtension.getTenantId(), randomTxId());
    if (softGreProfileLanPortActivation != null) {
      softGreProfileLanPortActivation.setLanPortAdoption(adoption);
      softGreProfileLanPortActivation =
          repositoryUtil.createOrUpdate(
              softGreProfileLanPortActivation, txCtxExtension.getTenantId(), randomTxId());
      adoption.setSoftGreActivation(softGreProfileLanPortActivation);
    }
    adoption =
        repositoryUtil.createOrUpdate(adoption, txCtxExtension.getTenantId(), adoption.getId());
    port.setLanPortAdoption(adoption);
    port = repositoryUtil.createOrUpdate(port, txCtxExtension.getTenantId(), port.getId());

    return new VenueLanPortData(modelAttributes, port, apLanPortProfile);
  }
}
