package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatNoException;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.notification.dnb.gpb.Dnb.DNB;
import com.ruckus.cloud.notification.dnb.gpb.Dnb.TaskFactoryReset;
import com.ruckus.cloud.notification.service.gpb.DeviceNotification;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.client.viewmodel.ViewmodelClientGrpc;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApSubStateEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeDeleteApCallbackRequestTest extends AbstractRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private ViewmodelClientGrpc viewmodelClientGrpc;

  @Test
  void deleteApWithFactoryReset(Ap ap) throws Exception {
    final var tenantId = ap.getTenant().getId();
    final var userName = randomName();

    // Set the AP is operational
    mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.Operational);

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams().addPathVariable("serialNumber", ap.getId());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.FACTORY_RESET_AP, userName, requestParams, "");

    final var factoryResetMessage = messageCaptors.getDeviceNotificationMessageCaptor()
            .getValue(tenantId, delRequestId);
    assertThat(factoryResetMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(factoryResetMessage.getPayload())
            .extracting(DeviceNotification::getContent)
            .satisfies(assertDnb(ap.getId())));
  }

  @Test
  void deleteApWithoutFactoryReset(Ap ap) throws Exception {
    final var tenantId = ap.getTenant().getId();
    final var userName = randomName();

    // Set the AP is operational
    mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.NeverContactedCloud);

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams().addPathVariable("serialNumber", ap.getId());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.FACTORY_RESET_AP, userName, requestParams, "");

    messageCaptors.getDeviceNotificationMessageCaptor().assertNotSentByTenant(tenantId);
  }

  @Test
  void deleteApWithMulticastDnsProxyServiceProfile(@DefaultApGroup ApGroup apGroup) throws Exception {
    // Add AP
    final var tenantId = apGroup.getTenant().getId();
    final var requestId = randomTxId();
    final var userName = randomName();
    final var serialNumber = randomSerialNumber();
    final var apRequest = new ApRequest();
    apRequest.setSerialNumber(serialNumber);
    apRequest.setVenueId(apGroup.getVenue().getId());
    apRequest.setName(randomName());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgExtendedAction.ADD_AP, userName, apRequest);
    final var ap = repositoryUtil.find(Ap.class, apRequest.getSerialNumber());

    // Add mDns Proxy service profile
    final MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = Generators.multicastDnsProxyServiceProfile().generate();
    multicastDnsProxyServiceProfile.getRules()
            .forEach(rule -> rule.setMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile));
    repositoryUtil.createOrUpdate(multicastDnsProxyServiceProfile, tenantId, randomTxId());
    var mDnsProxyServiceProfile = repositoryUtil.find(MulticastDnsProxyServiceProfile.class,
        multicastDnsProxyServiceProfile.getId());
    assertThat(mDnsProxyServiceProfile).isNotNull();

    // Add activation
    MulticastDnsProxyServiceProfileAp multicastDnsProxyServiceProfileAp = new MulticastDnsProxyServiceProfileAp();
    multicastDnsProxyServiceProfileAp.setId(randomId());
    multicastDnsProxyServiceProfileAp.setAp(ap);
    multicastDnsProxyServiceProfileAp.setMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile);
    repositoryUtil.createOrUpdate(multicastDnsProxyServiceProfileAp, tenantId, randomTxId());

    final var mDnsProxyServiceProfileAp =
        repositoryUtil.find(MulticastDnsProxyServiceProfileAp.class, multicastDnsProxyServiceProfileAp.getId());
    assertThat(mDnsProxyServiceProfileAp).isNotNull()
        .extracting(MulticastDnsProxyServiceProfileAp::getAp)
        .matches( mAp -> mAp.getId().equals(apRequest.getSerialNumber()));

    messageUtil.clearMessage();

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams().addPathVariable("serialNumber", apRequest.getSerialNumber());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.FACTORY_RESET_AP, userName, requestParams, apRequest);

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, delRequestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 2").hasSize(2)
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by MulticastDnsProxyServiceProfileCmnCfgCollectorOperationBuilder
                  .filteredOn(op -> EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD %s operation count should be 1",
                      EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME).hasSize(1)
                  .singleElement()
                  .extracting(Operations::getId)
                  .as("The operation id should be %s", multicastDnsProxyServiceProfile.getId())
                  .isEqualTo(multicastDnsProxyServiceProfile.getId());
            }));
  }

  private void mockOperationalAp(String serialNumber, String tenantId, ApSubStateEnum stateEnum) {
    List<ApDTO> apsList = new ArrayList<>();
    apsList.add(ApDTO.builder().serialNumber(serialNumber).subState(stateEnum)
        .build());
    doReturn(apsList).when(viewmodelClientGrpc).getApsBySerialNumber(anyString(),
        eq(tenantId), anySet());
  }

  public static Consumer<ByteString> assertDnb(String serialNumber) {
    return byteString -> {
      try {
        assertThat(DNB.parseFrom(byteString))
            .extracting(DNB::getTaskFactoryReset)
            .extracting(TaskFactoryReset::getCallbackUrl)
            .matches(callback -> callback.contains(serialNumber));
      } catch (InvalidProtocolBufferException e) {
        throw new RuntimeException(e);
      }
    };
  }
}
