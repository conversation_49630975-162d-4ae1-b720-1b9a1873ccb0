package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_DEVICE_POLICY;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_DEVICE_POLICY_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.DevicePolicyRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.DevicePolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddDevicePolicyByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private DevicePolicyRepository devicePolicyRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void add_device_policies(Tenant mspTenant, @Template DevicePolicy policy) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create ec policy set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_DEVICE_POLICY,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.DEVICE_POLICY, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, policy.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_DEVICE_POLICY_BY_TEMPLATE,
        ADD_DEVICE_POLICY_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_DEVICE_POLICY, ecTenantId);
    assertActivityStatusSuccess(ADD_DEVICE_POLICY, ecTenantId);
    assertActivityStatusSuccess(ADD_DEVICE_POLICY_BY_TEMPLATE, mspTenantId);

    // check policy rule in ec policy not override MSP's rule
    DevicePolicy mspPolicy =
        repositoryUtil.find(DevicePolicy.class, policy.getId(), mspTenantId, true);
    DevicePolicy ecPolicy = devicePolicyRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecPolicy.getId());
    assertDevicePolicy(mspPolicy, ecPolicy);

    // check policy rule id and tenant in ec policy is different from MSP
    assertNotEquals(mspPolicy.getRules().stream().map(DevicePolicyRule::getId).collect(Collectors.toSet()),
        ecPolicy.getRules().stream().map(DevicePolicyRule::getId).collect(Collectors.toSet()));

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert 1 viewmodel ops: DevicePolicy",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME,
            ecPolicy.getId(), ecPolicy.getName())
    );
  }

  @Test
  public void ec_add_device_policy_fail_then_msp_activity_should_fail(Tenant mspTenant, @Template DevicePolicy policy) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // policy already in ec tenant before apply template
    DevicePolicy existedEcPolicy = DevicePolicyTestFixture.randomDevicePolicy(ecTenant, (o) -> {
      o.setName(policy.getName());
    });
    repositoryUtil.createOrUpdate(existedEcPolicy, ecTenant.getId(), randomTxId());

    // create ec policy control set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_DEVICE_POLICY,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.DEVICE_POLICY, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, policy.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_DEVICE_POLICY_BY_TEMPLATE,
        ADD_DEVICE_POLICY_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_DEVICE_POLICY, ecTenantId);
    assertActivityStatusFail(ADD_DEVICE_POLICY, ecTenantId);
    assertActivityStatusFail(ApiFlowNames.ADD_DEVICE_POLICY_BY_TEMPLATE, mspTenantId);
  }

  private static void assertViewmodelCollector(List<Operations> operations, OpType opType,
      String index, String id, String name) {
    assertTrue(operations.stream()
        .filter(o -> index.equals(o.getIndex()))
        .filter(o -> id.equals(o.getId()))
        .filter(o -> o.getOpType() == opType)
        .anyMatch(o -> name.equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())));
  }

  private void assertDevicePolicy(DevicePolicy expected, DevicePolicy actual) {
    assertNotNull(expected);
    assertNotEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getDescription(), actual.getDescription());
    assertEquals(expected.getDefaultAccess(), actual.getDefaultAccess());
    assertEquals(expected.getRules().size(), actual.getRules().size());

    for (DevicePolicyRule expectedRule : expected.getRules()) {
      DevicePolicyRule actualRule = actual.getRules().stream()
          .filter(rule -> expectedRule.getName().equals(rule.getName())).findFirst().orElse(null);
      if (expectedRule.getId() != null) {
        assertNotEquals(expectedRule.getId(), actualRule.getId());
      }
      assertEquals(expectedRule.getName(), actualRule.getName());
      assertEquals(expectedRule.getAction(), actualRule.getAction());
      assertEquals(expectedRule.getDeviceType(), actualRule.getDeviceType());
      assertEquals(expectedRule.getOsVendor(), actualRule.getOsVendor());
      assertEquals(expectedRule.getDownloadRateLimit(), actualRule.getDownloadRateLimit());
      assertEquals(expectedRule.getUploadRateLimit(), actualRule.getUploadRateLimit());
      assertEquals(expectedRule.getVlan(), actualRule.getVlan());
    }
  }

  private void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }
}
