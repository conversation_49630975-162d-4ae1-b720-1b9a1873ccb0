package com.ruckus.cloud.wifi.integration.ipsec;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.IpsecProfileTestFixture.randomIpsecProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.IpsecProfileRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Objects;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("IpsecProfileTest")
@WifiIntegrationTest
public class ConsumeAddIpsecProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeAddIpsecProfileMessage {

    @Test
    void givenValidProfile(Tenant tenant) {
      var request = randomIpsecProfile();
      var requestViewModel =
          IpsecProfileRestCtrl.IpsecProfileMapper.INSTANCE.ServiceIpsecProfile2IpsecProfile(
              request);

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.ADD_IPSEC_PROFILE,
          randomName(),
          new RequestParams(),
          requestViewModel);

      assertThat(repositoryUtil.find(IpsecProfile.class, request.getId(), tenant.getId()))
          .isNotNull()
          .matches(p -> Objects.equals(p.getName(), request.getName()))
          .matches(p -> Objects.equals(p.getServerAddress(), request.getServerAddress()))
          .matches(p -> Objects.equals(p.getAuthType(), request.getAuthType()))
          .matches(p -> Objects.equals(p.getPreSharedKey(), request.getPreSharedKey()))
          .matches(p -> Objects.equals(p.getIkeSecurityAssociation(),
              request.getIkeSecurityAssociation()))
          .matches(p -> Objects.equals(p.getEspSecurityAssociation(),
              request.getEspSecurityAssociation()))
          .matches(p -> Objects.equals(p.getAdvancedOption(), request.getAdvancedOption()))
          .matches(p -> Objects.equals(p.getIkeRekeyTime(), request.getIkeRekeyTime()))
          .matches(p -> Objects.equals(p.getIkeRekeyTimeUnit(), request.getIkeRekeyTimeUnit()))
          .matches(p -> Objects.equals(p.getEspRekeyTime(), request.getEspRekeyTime()))
          .matches(p -> Objects.equals(p.getEspRekeyTimeUnit(), request.getEspRekeyTimeUnit()));

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.ADD)
          .matches(o -> o.getId().equals(request.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.ADD_IPSEC_PROFILE));

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getAction() == Action.ADD)
          .matches(o -> o.getId().equals(request.getId()));
    }

    @Test
    void givenDuplicatedProfileName(Tenant tenant) {
      var duplicatedName = randomName();
      repositoryUtil.createOrUpdate(
          randomIpsecProfile(tenant, p -> p.setName(duplicatedName)),
          tenant.getId());
      var request =
          IpsecProfileRestCtrl.IpsecProfileMapper.INSTANCE.ServiceIpsecProfile2IpsecProfile(
              randomIpsecProfile(tenant, p -> p.setName(duplicatedName)));

      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ADD_IPSEC_PROFILE,
                  randomName(),
                  new RequestParams(),
                  request))
          .isNotNull()
          .rootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      messageCaptors
          .assertThat(
              messageCaptors.getDdccmMessageCaptor(),
              messageCaptors.getCmnCfgCollectorMessageCaptor())
          .doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ADD_IPSEC_PROFILE));
    }
  }
}
