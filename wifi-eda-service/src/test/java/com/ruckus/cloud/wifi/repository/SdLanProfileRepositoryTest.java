package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.servicemodel.projection.SdLanProfileTenantIdProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import static org.assertj.core.api.Assertions.assertThat;

@Tag("VxLanTunnelFeatureTest")
@WifiJpaDataTest
public class SdLanProfileRepositoryTest {

  @Autowired
  private SdLanProfileRepository repository;

  @Nested
  @Tag("L2GreTunnelProfileMigrationTest")
  @DisplayName("Test findAllWithSharedTunnels()")
  class FindAllProjectionsWithSharedTunnelProfilesTests {

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel2', 'tenant1', 'tenant1-tunnel2');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel3', 'tenant1', 'tenant1-tunnel3');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan2', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan3', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
            VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs2', 'tenant1', 'tenant1-sdlan2', 'tenant1-venue2', 'tenant1-tunnel2', 'tenant1-tunnel3');

        INSERT INTO tenant (id) VALUES ('tenant2');
        INSERT INTO venue (id, tenant) VALUES ('tenant2-venue1', 'tenant2');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant2-tunnel1', 'tenant2', 'tenant2-tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant2-tunnel2', 'tenant2', 'tenant2-tunnel2');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant2-sdlan1', 'tenant2');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant2-sdlan2', 'tenant2');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
            VALUES ('tenant2-rs1', 'tenant2', 'tenant2-sdlan1', 'tenant2-venue1', 'tenant2-tunnel1');
        """)
    void shouldReturnEmptyWhenNoTunnelProfileIsShared() {
      assertThat(repository.findAllProjectionsWithSharedTunnelProfiles()).isEmpty();
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel2', 'tenant1', 'tenant1-tunnel2');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1', 'tenant1-tunnel2');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs2', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue2', 'tenant1-tunnel1', 'tenant1-tunnel2');
        """)
    void shouldReturnEmptyWhenMultipleRegularSettingsBelongToTheSameSdLan() {
      assertThat(repository.findAllProjectionsWithSharedTunnelProfiles()).isEmpty();
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan2', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
            VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
            VALUES ('tenant1-rs2', 'tenant1', 'tenant1-sdlan2', 'tenant1-venue2', 'tenant1-tunnel1');
        """)
    void shouldReturnProfilesSharingDcTunnel() {
      assertThat(repository.findAllProjectionsWithSharedTunnelProfiles())
          .hasSize(2)
          .extracting(SdLanProfileTenantIdProjection::sdLanId)
          .containsExactlyInAnyOrder("tenant1-sdlan1", "tenant1-sdlan2");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel2', 'tenant1', 'tenant1-tunnel2');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel3', 'tenant1', 'tenant1-tunnel3');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan2', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1', 'tenant1-tunnel3');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs2', 'tenant1', 'tenant1-sdlan2', 'tenant1-venue2', 'tenant1-tunnel2', 'tenant1-tunnel3');
        """)
    void shouldReturnProfilesSharingGuestTunnel() {
      assertThat(repository.findAllProjectionsWithSharedTunnelProfiles())
          .hasSize(2)
          .extracting(SdLanProfileTenantIdProjection::sdLanId)
          .containsExactlyInAnyOrder("tenant1-sdlan1", "tenant1-sdlan2");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel2', 'tenant1', 'tenant1-tunnel2');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel3', 'tenant1', 'tenant1-tunnel3');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan2', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1', 'tenant1-tunnel2');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs2', 'tenant1', 'tenant1-sdlan2', 'tenant1-venue2', 'tenant1-tunnel2', 'tenant1-tunnel3');
        """)
    void shouldReturnProfilesSharingDcAndGuestTunnel() {
      assertThat(repository.findAllProjectionsWithSharedTunnelProfiles())
          .hasSize(2)
          .extracting(SdLanProfileTenantIdProjection::sdLanId)
          .containsExactlyInAnyOrder("tenant1-sdlan1", "tenant1-sdlan2");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue3', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue4', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue5', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel2', 'tenant1', 'tenant1-tunnel2');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel3', 'tenant1', 'tenant1-tunnel3');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel4', 'tenant1', 'tenant1-tunnel4');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel5', 'tenant1', 'tenant1-tunnel5');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel6', 'tenant1', 'tenant1-tunnel6');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel7', 'tenant1', 'tenant1-tunnel7');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan2', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan3', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan4', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan5', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1', 'tenant1-tunnel2');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs2', 'tenant1', 'tenant1-sdlan2', 'tenant1-venue2', 'tenant1-tunnel2', 'tenant1-tunnel3');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs3', 'tenant1', 'tenant1-sdlan3', 'tenant1-venue3', 'tenant1-tunnel4', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs4', 'tenant1', 'tenant1-sdlan4', 'tenant1-venue4', 'tenant1-tunnel5', 'tenant1-tunnel5');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant1-rs5', 'tenant1', 'tenant1-sdlan5', 'tenant1-venue5', 'tenant1-tunnel6', 'tenant1-tunnel7');

        INSERT INTO tenant (id) VALUES ('tenant2');
        INSERT INTO venue (id, tenant) VALUES ('tenant2-venue1', 'tenant2');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant2-tunnel1', 'tenant2', 'tenant2-tunnel1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant2-sdlan1', 'tenant2');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('tenant2-rs1', 'tenant2', 'tenant2-sdlan1', 'tenant2-venue1', 'tenant2-tunnel1', 'tenant2-tunnel1');
        """)
    void testComplexScenario() {
      assertThat(repository.findAllProjectionsWithSharedTunnelProfiles())
          .hasSize(5)
          .extracting(SdLanProfileTenantIdProjection::sdLanId)
          .containsExactlyInAnyOrder("tenant1-sdlan1", "tenant1-sdlan2", "tenant1-sdlan3", "tenant1-sdlan4", "tenant2-sdlan1");
    }
  }

  @Nested
  @Tag("L2GreTunnelProfileMigrationTest")
  @DisplayName("Test findAllProjectionsWithDefaultTunnelProfiles()")
  class FindAllProjectionsWithDefaultTunnelProfilesTests {

    @Test
    @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant1');
      INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tenant1-tunnel1', 'tenant1', 't1', false);
      INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
      INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
          VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1');
      """)
    void shouldReturnEmptyWhenNoDefaultTunnelUsed() {
      assertThat(repository.findAllProjectionsWithDefaultTunnelProfiles()).isEmpty();
    }

    @Test
    @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant1');
      INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tenant1-tunnel1', 'tenant1', 't1', true);
      INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
      INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
          VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1');
      """)
    void shouldReturnWhenDcTunnelIsDefault() {
      assertThat(repository.findAllProjectionsWithDefaultTunnelProfiles())
          .hasSize(1)
          .extracting(SdLanProfileTenantIdProjection::sdLanId)
          .containsExactly("tenant1-sdlan1");
    }

    @Test
    @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant1');
      INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tenant1-tunnel1', 'tenant1', 't1', false);
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tenant1-tunnel2', 'tenant1', 't2', true);
      INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
      INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
          VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1', 'tenant1-tunnel2');
      """)
    void shouldReturnWhenGuestTunnelIsDefault() {
      assertThat(repository.findAllProjectionsWithDefaultTunnelProfiles())
          .hasSize(1)
          .extracting(SdLanProfileTenantIdProjection::sdLanId)
          .containsExactly("tenant1-sdlan1");
    }

    @Test
    @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant1');
      INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tenant1-tunnel1', 'tenant1', 't1', true);
      INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
      INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
          VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1', 'tenant1-tunnel1');
      """)
    void shouldReturnWhenDcAndGuestAreSameAndDefault() {
      assertThat(repository.findAllProjectionsWithDefaultTunnelProfiles())
          .hasSize(1)
          .extracting(SdLanProfileTenantIdProjection::sdLanId)
          .containsExactly("tenant1-sdlan1");
    }

    @Test
    @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant1');
      INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
      INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tenant1-tunnel1', 'tenant1', 't1', true);
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tenant1-tunnel2', 'tenant1', 't2', false);
      INSERT INTO tunnel_profile (id, tenant, name, is_default) VALUES ('tenant1-tunnel3', 'tenant1', 't3', true);
      INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
      INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan2', 'tenant1');
      INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
          VALUES ('tenant1-rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1');
      INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
          VALUES ('tenant1-rs2', 'tenant1', 'tenant1-sdlan2', 'tenant1-venue2', 'tenant1-tunnel2', 'tenant1-tunnel3');
      """)
    void shouldReturnMultipleProfilesWithDifferentDefaultTunnels() {
      assertThat(repository.findAllProjectionsWithDefaultTunnelProfiles())
          .hasSize(2)
          .extracting(SdLanProfileTenantIdProjection::sdLanId)
          .containsExactlyInAnyOrder("tenant1-sdlan1", "tenant1-sdlan2");
    }
  }


  @Tag("L2GreTunnelProfileMigrationTest")
  @Nested
  @DisplayName("Test findSdLanAndSharedSdLans()")
  class FindSdLanAndOthersWithSameTunnelProfilesTests {

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
            VALUES ('rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1');
        """)
    void shouldReturnSelfOnlyWhenNoShare() {
      assertThat(repository.findSdLanAndOthersWithSameTunnelProfiles("tenant1-sdlan1"))
          .singleElement()
          .extracting(SdLanProfile::getId)
          .isEqualTo("tenant1-sdlan1");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1', 'tenant1-tunnel1');
        """)
    void shouldReturnSelfWhenSelfSharing() {
      assertThat(repository.findSdLanAndOthersWithSameTunnelProfiles("tenant1-sdlan1"))
          .hasSize(1)
          .extracting(SdLanProfile::getId)
          .containsExactly("tenant1-sdlan1");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan2', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
            VALUES ('rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
            VALUES ('rs2', 'tenant1', 'tenant1-sdlan2', 'tenant1-venue2', 'tenant1-tunnel1');
        """)
    void shouldReturnBothWhenSharingDcTunnel() {
      assertThat(repository.findSdLanAndOthersWithSameTunnelProfiles("tenant1-sdlan1"))
          .hasSize(2)
          .extracting(SdLanProfile::getId)
          .containsExactlyInAnyOrder("tenant1-sdlan1", "tenant1-sdlan2");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel2', 'tenant1', 'tenant1-tunnel2');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan2', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1', 'tenant1-tunnel2');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile, guest_traffic_tunnel_profile)
            VALUES ('rs2', 'tenant1', 'tenant1-sdlan2', 'tenant1-venue2', 'tenant1-tunnel2', 'tenant1-tunnel1');
        """)
    void shouldReturnBothWhenCrossSharingDcGuestTunnel() {
      assertThat(repository.findSdLanAndOthersWithSameTunnelProfiles("tenant1-sdlan1"))
          .hasSize(2)
          .extracting(SdLanProfile::getId)
          .containsExactlyInAnyOrder("tenant1-sdlan1", "tenant1-sdlan2");
    }

    @Test
    @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue1', 'tenant1');
        INSERT INTO venue (id, tenant) VALUES ('tenant1-venue2', 'tenant1');
        INSERT INTO tunnel_profile (id, tenant, name) VALUES ('tenant1-tunnel1', 'tenant1', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan1', 'tenant1');
        INSERT INTO sd_lan_profile (id, tenant) VALUES ('tenant1-sdlan2', 'tenant1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
            VALUES ('rs1', 'tenant1', 'tenant1-sdlan1', 'tenant1-venue1', 'tenant1-tunnel1');
        INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, venue, tunnel_profile)
            VALUES ('rs2', 'tenant1', 'tenant1-sdlan2', 'tenant1-venue2', 'tenant1-tunnel1');
        """)
    void shouldHandleNullGuestTrafficTunnelProfileAndStillDetectDcSharing() {
      assertThat(repository.findSdLanAndOthersWithSameTunnelProfiles("tenant1-sdlan1"))
          .hasSize(2)
          .extracting(SdLanProfile::getId)
          .containsExactlyInAnyOrder("tenant1-sdlan1", "tenant1-sdlan2");
    }
  }
}
