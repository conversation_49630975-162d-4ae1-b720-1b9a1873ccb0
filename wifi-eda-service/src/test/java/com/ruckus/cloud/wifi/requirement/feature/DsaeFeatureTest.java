package com.ruckus.cloud.wifi.requirement.feature;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Arrays;
import org.apache.commons.lang3.RandomUtils;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class DsaeFeatureTest {

  @SpyBean
  private DsaeFeature unit;

  @Nested
  class WhenIsDSAEEnabled {

    @Test
    void givenNetworkTypeNotDpskNetwork() {
      final var network = mock(OpenNetwork.class);

      BDDAssertions.then(unit.test(network)).isFalse();
      verify(network, times(1)).getType();
      verifyNoMoreInteractions(network);
    }

    @Nested
    class GivenNetworkTypIsDpskNetwork {

      @Test
      void givenWlanIsNull() {
        final var network = new DpskNetwork();
        network.setWlan(null);

        BDDAssertions.then(unit.test(network)).isFalse();
      }

      @Nested
      class GivenWlanIsNotNull {

        @Test
        void givenWlanSecurityNotWPA32Mixed() {
          final var wlanSecurities =
              Arrays.stream(WlanSecurityEnum.values())
                  .filter(s -> s != WlanSecurityEnum.WPA23Mixed)
                  .toList();
          final var wlan = new Wlan();
          wlan.setWlanSecurity(wlanSecurities.get(RandomUtils.nextInt(0, wlanSecurities.size())));
          final var network = new DpskNetwork();
          network.setWlan(wlan);

          BDDAssertions.then(unit.test(network)).isFalse();
        }

        @Test
        void givenWlanSecurityIsWPA32Mixed() {
          final var wlan = new Wlan();
          wlan.setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
          final var network = new DpskNetwork();
          network.setType(NetworkTypeEnum.DPSK);
          network.setWlan(wlan);

          BDDAssertions.then(unit.test(network)).isTrue();
        }
      }
    }
  }
}
