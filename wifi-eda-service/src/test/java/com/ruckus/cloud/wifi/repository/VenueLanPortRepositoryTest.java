package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.groups.Tuple.tuple;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.hibernate.dto.VenueActivationDto;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueLanPortProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("EthernetPortProfileTest")
@WifiJpaDataTest
@Sql(statements = """    
    INSERT INTO tenant (id) VALUES ('tenantId00');

    INSERT INTO ap_lan_port_profile (category, id, tenant, enable_auth_proxy, enable_accounting_proxy) VALUES
      ('ETHERNET', 'apLanPortProfileId01', 'tenantId00', 'true', 'true'),
      ('ETHERNET', 'apLanPortProfileId02', 'tenantId00', 'true', 'false'),
      ('ETHERNET', 'apLanPortProfileId03', 'tenantId00', 'true', 'false'),
      ('ETHERNET', 'apLanPortProfileId04', 'tenantId00', 'false', 'false'),
      ('ETHERNET', 'apLanPortProfileId05', 'tenantId00', 'false', 'true'),
      ('ETHERNET', 'apLanPortProfileId06', 'tenantId00', 'true', 'true');
      
    INSERT INTO lan_port_adoption (id, checksum, ap_lan_port_profile, tenant) VALUES
      ('lanPortAdoptionId01', 'checksum1', 'apLanPortProfileId01', 'tenantId00'),
      ('lanPortAdoptionId02', 'checksum2', 'apLanPortProfileId02', 'tenantId00'),
      ('lanPortAdoptionId03', 'checksum3', 'apLanPortProfileId03', 'tenantId00'),
      ('lanPortAdoptionId04', 'checksum4', 'apLanPortProfileId04', 'tenantId00'),
      ('lanPortAdoptionId05', 'checksum5', 'apLanPortProfileId05', 'tenantId00'),
      ('lanPortAdoptionId06', 'checksum6', 'apLanPortProfileId06', 'tenantId00');

    INSERT INTO venue (id, tenant)
      VALUES ('venueId01', 'tenantId00');
    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model)
      VALUES ('attrId01', 'venueId01', 'tenantId00', 'R600');
    INSERT INTO venue_lan_port (id, port_id, venue_ap_model_specific_attributes, ap_lan_port_profile, lan_port_adoption, tenant)
      VALUES ('venueLanPort01', '1', 'attrId01', 'apLanPortProfileId01', 'lanPortAdoptionId01', 'tenantId00'),
             ('venueLanPort01-1', '2', 'attrId01', 'apLanPortProfileId06', null, 'tenantId00');
    
    INSERT INTO venue (id, tenant)
      VALUES ('venueId02', 'tenantId00');
    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model)
      VALUES ('attrId02', 'venueId02', 'tenantId00', 'R700');
    INSERT INTO venue_lan_port (id, port_id, venue_ap_model_specific_attributes, ap_lan_port_profile, lan_port_adoption, tenant)
      VALUES ('venueLanPort02', '2', 'attrId02', 'apLanPortProfileId01', 'lanPortAdoptionId01', 'tenantId00');
    
    INSERT INTO venue (id, tenant)
      VALUES 
      ('venueId03', 'tenantId00'),
      ('venueId04', 'tenantId00'),
      ('venueId05', 'tenantId00'),
      ('venueId06', 'tenantId00');
    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model)
      VALUES 
      ('attrId03', 'venueId03', 'tenantId00', 'R500'),
      ('attrId04', 'venueId04', 'tenantId00', 'R500'),
      ('attrId05', 'venueId05', 'tenantId00', 'R500');
    INSERT INTO venue_lan_port (id, port_id, venue_ap_model_specific_attributes, ap_lan_port_profile, lan_port_adoption, tenant)
      VALUES 
      ('venueLanPort03', '3', 'attrId03', 'apLanPortProfileId02', 'lanPortAdoptionId02', 'tenantId00'),
      ('venueLanPort04', '4', 'attrId04', 'apLanPortProfileId01', 'lanPortAdoptionId01', 'tenantId00'),
      ('venueLanPort05', '5', 'attrId04', 'apLanPortProfileId02', 'lanPortAdoptionId02', 'tenantId00'),
      ('venueLanPort06', '6', 'attrId04', 'apLanPortProfileId03', 'lanPortAdoptionId03', 'tenantId00'),
      ('venueLanPort07', '7', 'attrId04', 'apLanPortProfileId04', 'lanPortAdoptionId04', 'tenantId00'),
      ('venueLanPort08', '8', 'attrId04', 'apLanPortProfileId05', 'lanPortAdoptionId05', 'tenantId00'),  
      ('venueLanPort09', '9', 'attrId04', 'apLanPortProfileId06', 'lanPortAdoptionId06', 'tenantId00'),  
      ('venueLanPort10', '10', 'attrId05', 'apLanPortProfileId01', null, 'tenantId00');
      
      INSERT INTO client_isolation_allowlist (id, tenant) VALUES ('clientIsolationAllowlist01', 'tenantId00');
      INSERT INTO client_isolation_lan_port_activation (id, client_isolation_allowlist, lan_port_adoption, tenant)
      VALUES ('clientIsolationLanPortActivation01', 'clientIsolationAllowlist01', 'lanPortAdoptionId01', 'tenantId00'),
             ('clientIsolationLanPortActivation02', 'clientIsolationAllowlist01', 'lanPortAdoptionId02', 'tenantId00');
    
    INSERT INTO soft_gre_profile (id, tenant) 
        VALUES ('softGreProfileId01', 'tenantId00'),
               ('softGreProfileId02', 'tenantId00');
    INSERT INTO ipsec_profile (id, tenant)
        VALUES ('ipsecProfileId01', 'tenantId00'),
               ('ipsecProfileId02', 'tenantId00');
    INSERT INTO soft_gre_profile_lan_port_activation (id, soft_gre_profile, ipsec_profile, lan_port_adoption, tenant)
        VALUES ('softGreProfileLanPortActivation01', 'softGreProfileId01', 'ipsecProfileId01', 'lanPortAdoptionId01', 'tenantId00'),
               ('softGreProfileLanPortActivation02', 'softGreProfileId02', 'ipsecProfileId02', 'lanPortAdoptionId02', 'tenantId00');               
    INSERT INTO dhcp_option82lan_port_activation (id, lan_port_adoption, tenant)
        VALUES ('dhcpOption82lanPortActivation01', 'lanPortAdoptionId01', 'tenantId00');
    """)
class VenueLanPortRepositoryTest {

  @Autowired
  private VenueLanPortRepository venueLanPortRepository;

  @Test
  void testFindByTenantIdAndApLanPortProfileIdIn() {
    List<VenueLanPort> venueLanPorts =
        venueLanPortRepository.findByTenantIdAndApLanPortProfileIdIn(
            "tenantId00", List.of("apLanPortProfileId01", "apLanPortProfileId02"));
    assertEquals(6, venueLanPorts.size());
    assertThat( venueLanPorts.stream().map(VenueLanPort::getId)).contains(
        "venueLanPort01", "venueLanPort02", "venueLanPort03", "venueLanPort04", "venueLanPort05");

    List<VenueLanPort> venueLanPorts2 =
        venueLanPortRepository.findByTenantIdAndApLanPortProfileIdIn(
            "tenantId00", List.of("apLanPortProfileId01"));
    assertEquals(4, venueLanPorts2.size());
    assertThat( venueLanPorts2.stream().map(VenueLanPort::getId)).contains(
        "venueLanPort01", "venueLanPort02", "venueLanPort04");
  }

  @Test
  void findByApLanPortProfileId() {

    assertThat(venueLanPortRepository.findByTenantIdAndApLanPortProfileId("tenantId00", "apLanPortProfileId01").stream()
        .map(VenueLanPort::getId)
        .collect(Collectors.toList()))
        .contains("venueLanPort01", "venueLanPort02");

    assertThat(venueLanPortRepository.findByTenantIdAndApLanPortProfileId("tenantId00", "apLanPortProfileId01").stream()
        .map(VenueLanPort::getVenueApModelSpecificAttributes)
        .map(VenueApModelSpecificAttributes::getVenue)
        .map(Venue::getId)
        .toList())
        .contains("venueId01", "venueId02");
  }

  @Test
  void findVenueIdByApLanPortProfileId() {
    assertThat(
        venueLanPortRepository.findVenueIdsByTenantIdAndApLanPortProfileId("tenantId00", "apLanPortProfileId01"))
        .contains("venueId01", "venueId02");

    assertEquals(List.of("venueId03", "venueId04"),
        venueLanPortRepository.findVenueIdsByTenantIdAndApLanPortProfileId("tenantId00", "apLanPortProfileId02")
    );
  }

  @Test
  void findAllPortsInVenueWhichHasNoLanPortAdoptionTest() {
    assertThat(
            venueLanPortRepository
                .findAllPortsInVenueWhichHasNoLanPortAdoption("tenantId00", "venueId01")
                .stream()
                .map(VenueLanPort::getId)
                .toList())
        .hasSize(1)
        .contains("venueLanPort01-1");
  }

  @Test
  void findAllPortsInVenueWhichIdNotAndHasNoLanPortAdoptionTest() {
    assertThat(
            venueLanPortRepository
                .findAllPortsInVenueWhichIdNotAndHasNoLanPortAdoption(
                    "tenantId00", "venueId01", "venueLanPort01-1")
                .stream()
                .map(VenueLanPort::getId)
                .toList())
        .hasSize(0);
    assertThat(
            venueLanPortRepository
                .findAllPortsInVenueWhichIdNotAndHasNoLanPortAdoption(
                    "tenantId00", "venueId01", "venueLanPort01")
                .stream()
                .map(VenueLanPort::getId)
                .toList())
        .hasSize(1)
        .contains("venueLanPort01-1");
  }

  @Test
  void existsByTenantIdAndVenueApModelSpecificAttributesIdIn() {
    assertTrue(venueLanPortRepository.existsByTenantIdAndVenueApModelSpecificAttributesIdIn(
        "tenantId00", List.of("attrId01", "attrId02")));
    assertTrue(venueLanPortRepository.existsByTenantIdAndVenueApModelSpecificAttributesIdIn(
        "tenantId00", List.of("attrId01", "attrId88")));

    assertFalse(venueLanPortRepository.existsByTenantIdAndVenueApModelSpecificAttributesIdIn(
        "tenantId00", List.of("attrId88", "attrId99")));
    assertFalse(venueLanPortRepository.existsByTenantIdAndVenueApModelSpecificAttributesIdIn(
        "tenantId01", List.of("attrId01", "attrId02")));
  }

  @Test
  void test_findEthernetPortProfileIdsByTenantIdAndVenueApModelSpecificAttributesVenueId() {
    List<String> ids = venueLanPortRepository.findEthernetPortProfileIdsByTenantIdAndVenueApModelSpecificAttributesVenueId(
        "tenantId00", "venueId03");

    assertEquals(1, ids.size());
    assertEquals("apLanPortProfileId02", ids.get(0));

    List<String> ids2 = venueLanPortRepository.findEthernetPortProfileIdsByTenantIdAndVenueApModelSpecificAttributesVenueId(
        "tenantId00", "venueId02");

    assertEquals(1, ids2.size());
    assertEquals("apLanPortProfileId01", ids2.get(0));
  }

  @Test
  void test_findEthernetPortProfileByTenantIdAndVenueApModelSpecificAttributesVenueId() {
    List<EthernetPortProfile> eths = venueLanPortRepository.findEthernetPortProfileByTenantIdAndVenueApModelSpecificAttributesVenueId(
        "tenantId00", "venueId03");

    assertEquals(1, eths.size());
    assertEquals("apLanPortProfileId02", eths.get(0).getId());
    assertEquals(false, eths.get(0).getEnableAccountingProxy());

    List<EthernetPortProfile> eths2 = venueLanPortRepository.findEthernetPortProfileByTenantIdAndVenueApModelSpecificAttributesVenueId(
        "tenantId00", "venueId02");

    assertEquals(1, eths2.size());
    assertEquals("apLanPortProfileId01", eths2.get(0).getId());
    assertEquals(true, eths2.get(0).getEnableAccountingProxy());
  }

  @Test
  void test_findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAuthProxyTrue() {

    assertThat(
        venueLanPortRepository
            .findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAuthProxyTrue("tenantId00", "venueId03").stream()
            .map(VenueLanPort::getId)
            .toList())
        .hasSize(1)
        .contains("venueLanPort03");

    assertThat(
        venueLanPortRepository
            .findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAuthProxyTrue("tenantId00", "venueId04").stream()
            .map(VenueLanPort::getId)
            .toList())
        .hasSize(4)
        .contains("venueLanPort04", "venueLanPort05", "venueLanPort06", "venueLanPort09");
  }

  @Test
  void test_findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAccountingProxyTrue() {

    assertThat(
        venueLanPortRepository
            .findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAccountingProxyTrue("tenantId00", "venueId03").stream()
            .map(VenueLanPort::getId)
            .toList())
        .hasSize(0);

    assertThat(
        venueLanPortRepository
            .findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAccountingProxyTrue("tenantId00", "venueId04").stream()
            .map(VenueLanPort::getId)
            .toList())
        .hasSize(3)
        .contains("venueLanPort04", "venueLanPort08", "venueLanPort09");
  }

  @Test
  void findByTenantIdAndPortIdAndVenueIdAndModel() {
    var result = venueLanPortRepository.findByTenantIdAndPortIdAndVenueIdAndModel("tenantId00", "3", "venueId03", "R500");
    assertTrue(result.isPresent());
    assertEquals("venueLanPort03", result.get().getId());
  }

  @Test
  void findByLanPortAdoptionId() {
    assertThat(venueLanPortRepository.findByTenantIdAndLanPortAdoptionIdIn("tenantId00", List.of("lanPortAdoptionId01")).stream()
        .map(VenueLanPort::getId)
        .collect(Collectors.toList()))
        .contains("venueLanPort01", "venueLanPort02");

    assertThat(venueLanPortRepository.findByTenantIdAndLanPortAdoptionIdIn("tenantId00", List.of("lanPortAdoptionId02")).stream()
        .map(VenueLanPort::getVenueApModelSpecificAttributes)
        .map(VenueApModelSpecificAttributes::getVenue)
        .map(Venue::getId)
        .toList())
        .contains("venueId03");
  }

  @Test
  void findVenueIdModelPortIdApGroupsByVenueLanPorts() {
    var venueLanPortProjections = venueLanPortRepository.findVenueIdModelPortIdApGroupsByVenueLanPorts(
        List.of("venueLanPort01", "venueLanPort02"));
    assertThat(venueLanPortProjections)
        .isNotNull()
        .hasSize(2)
        .extracting(VenueLanPortProjection::venueId, VenueLanPortProjection::apModel,
            VenueLanPortProjection::portId)
        .containsExactlyInAnyOrder(
            tuple("venueId01", "R600", "1"),
            tuple("venueId02", "R700", "2")
        );
  }

  @Test
  void findVenueIdModelPortIdApGroupsByClientIsolationAllowlist() {
    var venueLanPortProjections =
        venueLanPortRepository.findVenueLanPortProjectionsWhichActivatedClientIsolationAllowlist(
            "tenantId00", "clientIsolationAllowlist01");
    assertThat(venueLanPortProjections)
        .isNotNull()
        .hasSize(5)
        .extracting(VenueLanPortProjection::venueId, VenueLanPortProjection::apModel,
            VenueLanPortProjection::portId)
        .containsExactlyInAnyOrder(
            tuple("venueId01", "R600", "1"),
            tuple("venueId02", "R700", "2"),
            tuple("venueId03", "R500", "3"),
            tuple("venueId04", "R500", "4"),
            tuple("venueId04", "R500", "5")
        );
  }

  @Test
  void findVenueActivationsTest() {
    var venueActivations = venueLanPortRepository
        .findVenueActivations("tenantId00", "apLanPortProfileId01");
    assertEquals(4, venueActivations.size());
    assertThat(venueActivations).extracting(VenueActivationDto::venueId, VenueActivationDto::apModel, VenueActivationDto::portId)
        .containsExactlyInAnyOrder(
            tuple("venueId01", "R600", "1"),
            tuple("venueId02", "R700", "2"),
            tuple("venueId04", "R500", "4"),
            tuple("venueId05", "R500", "10")
        );
  }

  @Test
  void findAllPortsInAllVenuesWhichActivatedTargetSoftGreProfileTest() {
    var ports =
        venueLanPortRepository.findAllPortsInAllVenuesWhichActivatedTargetSoftGreProfile(
            "tenantId00", "softGreProfileId01");
    // softGreProfileId01 is activated in lanPortAdoptionId01
    // lanPortAdoptionId01 is used by venueLanPort01[venueId01], venueLanPort02[venueId02], and
    // venueLanPort04[venueId04]
    assertEquals(3, ports.size());
    assertThat(ports).filteredOn(p -> "venueLanPort01".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "venueLanPort02".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "venueLanPort04".equals(p.getId())).hasSize(1);
  }

  @Test
  void findAllPortsInAllVenuesWhichActivatedTargetIpsecProfileTest() {
    var ports =
        venueLanPortRepository.findAllPortsInAllVenuesWhichActivatedTargetIpsecProfile(
            "tenantId00", "ipsecProfileId01");
    assertEquals(3, ports.size());
    assertThat(ports).filteredOn(p -> "venueLanPort01".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "venueLanPort02".equals(p.getId())).hasSize(1);
    assertThat(ports).filteredOn(p -> "venueLanPort04".equals(p.getId())).hasSize(1);

    ports =
        venueLanPortRepository.findAllPortsInAllVenuesWhichActivatedTargetIpsecProfile(
            "tenantId00", "non-exist-id");
    assertEquals(0, ports.size());
  }

  @Test
  void findOtherActivatedPortsInAllVenuesWhichActivatedTargetSoftGreProfileTest() {
    var ports =
        venueLanPortRepository.findOtherActivatedPortsInAllVenuesWhichActivatedTargetSoftGreProfile(
            "tenantId00", "softGreProfileId01");
    // softGreProfileId01 is activated in lanPortAdoptionId01
    // lanPortAdoptionId01 is used by venueLanPort01[venueId01], venueLanPort02[venueId02], and
    // venueLanPort04[venueId04] these should not be found
    // softGreProfileId02 is activated in lanPortAdoptionId02
    // lanPortAdoptionId02 is used by venueLanPort03[venueId03], and venueLanPort05[venueId04],
    // venueId03 is not included in the activated ports' venue, should not be finding out.
    assertEquals(1, ports.size());
    assertThat(ports).filteredOn(p -> "venueLanPort05".equals(p.getId())).hasSize(1);
  }

  @Test
  void findAllPortsInVenueWhichHasDhcpOption82ActivationTest() {
    // venue01 has DhcpOption82Activation
    // venue03 DhcpOption82Activation NULL
    // venue05 lanPortAdoption NULL
    // venue06 No VenueApModelSpecificAttributes
    assertThat(
        venueLanPortRepository
            .findAllPortsInVenueWhichHasDhcpOption82Activation("tenantId00", "venueId01").stream()
            .map(VenueLanPort::getId)
            .toList())
        .hasSize(1)
        .contains("venueLanPort01");

    assertThat(
        venueLanPortRepository
            .findAllPortsInVenueWhichHasDhcpOption82Activation("tenantId00", "venueId03").stream()
            .map(VenueLanPort::getId)
            .toList())
        .hasSize(0);

    assertThat(
        venueLanPortRepository
            .findAllPortsInVenueWhichHasDhcpOption82Activation("tenantId00", "venueId05").stream()
            .map(VenueLanPort::getId)
            .toList())
        .hasSize(0);

    assertThat(
        venueLanPortRepository
            .findAllPortsInVenueWhichHasDhcpOption82Activation("tenantId00", "venueId06").stream()
            .map(VenueLanPort::getId)
            .toList())
        .hasSize(0);
  }
}