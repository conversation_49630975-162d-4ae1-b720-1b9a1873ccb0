package com.ruckus.cloud.wifi.service.integration;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantFirmwareVersion;
import com.ruckus.cloud.wifi.repository.TenantFirmwareVersionRepository;
import com.ruckus.cloud.wifi.service.TenantFirmwareVersionService;
import com.ruckus.cloud.wifi.service.impl.TenantFirmwareVersionServiceImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@WifiJpaDataTest
public class TenantFirmwareVersionServiceTest {

  @Autowired private TenantFirmwareVersionService tenantFirmwareVersionService;

  @Autowired private TenantFirmwareVersionRepository tenantFirmwareVersionRepository;

  @Autowired private RepositoryUtil repositoryUtil;

  @Test
  public void testSave_createAndGenerateCustomIdWhenIdNull(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version) {
    TenantFirmwareVersion tfv = newTenantFirmwareVersion(version, tenant, "test-branch");
    tenantFirmwareVersionService.save(tfv);

    Optional<TenantFirmwareVersion> createdRes =
        tenantFirmwareVersionRepository.findByTenantIdAndBranchType(
            tenant.getId(), tfv.getBranchType());
    Assertions.assertTrue(createdRes.isPresent());
    Assertions.assertEquals(tfv.getTenant().getId() + tfv.getBranchType(), createdRes.get().getId());
  }

  @Test
  public void testSave_createNonCustomIdTfv(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version) {
    String nonCustomId = UUID.randomUUID().toString();
    TenantFirmwareVersion tfv = newTenantFirmwareVersion(version, tenant, "test-branch");
    tfv.setId(nonCustomId);
    tenantFirmwareVersionService.save(tfv);

    Optional<TenantFirmwareVersion> createdRes =
        tenantFirmwareVersionRepository.findByTenantIdAndBranchType(
            tenant.getId(), tfv.getBranchType());
    Assertions.assertTrue(createdRes.isPresent());
    Assertions.assertNotEquals(tfv.getTenant().getId() + tfv.getBranchType(), createdRes.get().getId());
    Assertions.assertEquals(nonCustomId, createdRes.get().getId());
  }

  @Test
  public void testSave_updateNonCustomIdTfv(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.2")
          ApVersion newVersion) {
    String nonCustomId = UUID.randomUUID().toString();
    TenantFirmwareVersion tfv = newTenantFirmwareVersion(version, tenant, "test-branch");
    tfv.setId(nonCustomId);
    tenantFirmwareVersionRepository.save(tfv);

    tfv.setLatestFirmwareVersion(newVersion);
    tenantFirmwareVersionService.save(tfv);

    List<TenantFirmwareVersion> updatedRes = tenantFirmwareVersionRepository.findByTenantId(tenant.getId());
    Assertions.assertEquals(1, updatedRes.size(), "check duplicate data");
    Assertions.assertNotEquals(tfv.getTenant().getId() + tfv.getBranchType(), updatedRes.get(0).getId());
    Assertions.assertEquals(nonCustomId, updatedRes.get(0).getId());
  }

  @Test
  public void testSaveAll_createAndGenerateCustomIdWhenIdNull(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version) {
    List<Tenant> tenants = List.of(TenantTestFixture.randomTenant(c -> {}), TenantTestFixture.randomTenant(c -> {}));
    List<TenantFirmwareVersion> tenantFirmwareVersions = tenants.stream().map(tenant -> {
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), UUID.randomUUID().toString());
      return newTenantFirmwareVersion(version, tenant, "test-branch");
    }).collect(Collectors.toList());

    tenantFirmwareVersionService.saveAll(tenantFirmwareVersions);

    tenantFirmwareVersions.forEach( tfv -> {
      Optional<TenantFirmwareVersion> createdRes =
          tenantFirmwareVersionRepository.findByTenantIdAndBranchType(
              tfv.getTenant().getId(), tfv.getBranchType());

      Assertions.assertTrue(createdRes.isPresent());
      Assertions.assertEquals(tfv.getTenant().getId() + tfv.getBranchType(), createdRes.get().getId());
    });
  }

  @Test
  public void testSaveAll_updateNonCustomIdTenantFWVersions(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.0.0.0.1")
          ApVersion version) {
    List<Tenant> tenants = List.of(TenantTestFixture.randomTenant(c -> {}), TenantTestFixture.randomTenant(c -> {}));
    List<TenantFirmwareVersion> tenantFirmwareVersions = tenants.stream().map(tenant -> {
      String nonCustomId = UUID.randomUUID().toString();
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), UUID.randomUUID().toString());
      TenantFirmwareVersion tfv = newTenantFirmwareVersion(version, tenant, "test-branch");
      tfv.setId(nonCustomId);
      return tenantFirmwareVersionRepository.save(tfv);
    }).collect(Collectors.toList());

    tenantFirmwareVersionService.saveAll(tenantFirmwareVersions);

    tenantFirmwareVersions.forEach( tfv -> {
      List<TenantFirmwareVersion> updatedRes = tenantFirmwareVersionRepository.findByTenantId(tfv.getTenant().getId());
      Assertions.assertEquals(1, updatedRes.size(), "check duplicate data");
      Assertions.assertNotEquals(tfv.getTenant().getId() + tfv.getBranchType(), updatedRes.get(0).getId());
      Assertions.assertEquals(tfv.getId(), updatedRes.get(0).getId());
    });
  }

  private TenantFirmwareVersion newTenantFirmwareVersion(ApVersion version, Tenant tenant, String branchType){
    TenantFirmwareVersion tfv = new TenantFirmwareVersion();
    tfv.setLatestFirmwareVersion(version);
    tfv.setTenant(tenant);
    tfv.setBranchType(branchType);
    return tfv;
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    @ConditionalOnMissingBean
    public TenantFirmwareVersionService tenantFirmwareVersionService(
        TenantFirmwareVersionRepository tenantFirmwareVersionRepository) {
      return new TenantFirmwareVersionServiceImpl(tenantFirmwareVersionRepository);
    }
  }
}
