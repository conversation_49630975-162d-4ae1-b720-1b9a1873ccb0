package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.postmigration.V0_1316__ApLanPortProfilePostMigrationConsumer.JOB_NAME;
import static com.ruckus.cloud.wifi.postmigration.V0_1316__ApLanPortProfilePostMigrationConsumer.REDIS_KEY_AP_LAN_PORT_PROFILE_MIGRATION;
import static com.ruckus.cloud.wifi.postmigration.V0_1316__ApLanPortProfilePostMigrationConsumer.ROUTINE_JOB_SCHEDULE_TIME;
import static com.ruckus.cloud.wifi.test.Assertions.argThat;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.client.kairos.KairosApiClient;
import com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.redis.service.RedisClientService;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

@ExtendWith(MockitoExtension.class)
@WifiIntegrationTest
public class V0_1316__ApLanPortProfilePostMigrationConsumerTest {

  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private V0_1316__ApLanPortProfilePostMigrationConsumer postMigrationConsumer;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private KairosApiClient kairosApiClient;
  @Autowired
  private RedisClientService redisClientService;

  @Nested
  class WhenRunPostMigration {

    @BeforeEach
    void beforeEach() {
      List<Tenant> tenants =
          Stream.generate(() -> TenantTestFixture.randomTenant(t -> {
              }))
              .limit(3)
              .peek(tenant -> repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId()))
              .collect(Collectors.toList());
      Collections.shuffle(tenants);

      EthernetPortProfile apLanPortProfile0 = new EthernetPortProfile();
      apLanPortProfile0.setTenant(tenants.get(0));
      apLanPortProfile0.setIsDefault(true);
      repositoryUtil.createOrUpdate(apLanPortProfile0, tenants.get(0).getId(), randomTxId());
      EthernetPortProfile apLanPortProfile1 = new EthernetPortProfile();
      apLanPortProfile1.setTenant(tenants.get(1));
      apLanPortProfile1.setIsDefault(true);
      repositoryUtil.createOrUpdate(apLanPortProfile1, tenants.get(1).getId(), randomTxId());
      EthernetPortProfile apLanPortProfile2 = new EthernetPortProfile();
      apLanPortProfile2.setTenant(tenants.get(2));
      apLanPortProfile2.setIsDefault(true);
      repositoryUtil.createOrUpdate(apLanPortProfile2, tenants.get(2).getId(), randomTxId());
    }

    @Test
    void thenRegisterAsyncJobForEachTenant() throws Exception {

      doReturn(false)
          .when(kairosApiClient).hasScheduleJob(JOB_NAME);
      reset(kairosApiClient);

      postMigrationConsumer.run(null);  // Run the migration

      // Get the elements from the list
      var cacheResult = new ArrayList<>();
      var tenantId = redisClientService.lindex(REDIS_KEY_AP_LAN_PORT_PROFILE_MIGRATION, 0);
      while (tenantId != null) {
        cacheResult.add(tenantId);
        redisClientService.lremove(REDIS_KEY_AP_LAN_PORT_PROFILE_MIGRATION, 0, tenantId);
        tenantId = redisClientService.lindex(REDIS_KEY_AP_LAN_PORT_PROFILE_MIGRATION, 0);
      }
      // Verify that three elements are popped
      assertEquals(3, cacheResult.size());

      verify(kairosApiClient, never())
          .deleteScheduleJob(JOB_NAME);
      verify(kairosApiClient, times(1))
          .createScheduleJob(eq(JOB_NAME), argThat(req -> assertThat(req)
              .isNotNull()
              .satisfies(request -> assertSoftly(softly -> {
                softly.assertThat(request.getScheduleTime()).isEqualTo(ROUTINE_JOB_SCHEDULE_TIME);
                softly.assertThat(request.getTenantId()).isEqualTo(JOB_NAME);
                softly.assertThat(request.getTimeZone()).isEqualTo(TimeZone.getDefault().getID());
                softly.assertThat(request.getKafkaTarget()).isNotNull()
                    .satisfies(kafkaTarget -> assertSoftly(alsoSoftly -> {
                      alsoSoftly.assertThat(kafkaTarget.getHeader())
                          .extractingByKey(WifiCommonHeader.WIFI_REQUEST_ID).isNotNull();
                      alsoSoftly.assertThat(kafkaTarget.getHeader())
                          .extractingByKey(WifiCommonHeader.WIFI_TENANT_ID).isEqualTo(JOB_NAME);
                      alsoSoftly.assertThat(kafkaTarget.getTopicName()).isNotEmpty();
                      alsoSoftly.assertThat(kafkaTarget.getKey()).isNotEmpty();
                      alsoSoftly.assertThat(kafkaTarget.getData()).isNotEmpty()
                          .extractingByKey("apLanPortProfilePostMigrationJob")
                          .isNotNull();
                    }));
              }))));
      verify(kairosApiClient, never())
          .updateScheduleJob(eq(JOB_NAME), any(RegisterKairosRequest.class));
      assertEquals(cacheResult.size(), 3);
      reset(redisClientService);
    }
  }
}
