package com.ruckus.cloud.wifi.entitylistener.observation;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.ruckus.cloud.wifi.core.hibernate.entitylistener.AggregatedEntityListener;
import com.ruckus.cloud.wifi.entitylistener.observation.ListenerMetricObserver.ListenerMetricContext;
import com.ruckus.cloud.wifi.entitylistener.observation.ListenerMetricObserver.ListenerMetricContextDataItem;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(TxCtxExtension.class)
public class ListenerMetricObserverTest {

  @Nested
  public class OnStart {

    ListenerMetricObserver unit;
    ListenerMetricContext context;

    @BeforeEach
    public void before() {
      unit = new ListenerMetricObserver(new SimpleMeterRegistry());
      context = unit.getContext();
    }

    @Test
    public void shouldCreateContextDataAndStartTimer() {
      unit.onStart();

      assertThat(context.getTotalTimerSample()).isNotNull();
    }

    @Nested
    public class OnCreate {

      AggregatedEntityListener mockListener = mock(AggregatedEntityListener.class);

      @BeforeEach
      public void before() {
        unit.onStart();
        doReturn("endpoint").when(mockListener).getEndpoint();
      }

      @Test
      public void shouldCreateContextItem() {
        unit.onCreate(mockListener);

        assertThat(context.getItemMap()).containsKey("endpoint");
      }


      @Nested
      public class OnBuildStart {

        @BeforeEach
        public void before() {
          unit.onCreate(mockListener);
        }

        @Test
        public void shouldStartBuildTimer() {
          unit.onBuildStart(mockListener);

          assertThat(context.getItemMap().get("endpoint")).extracting(
                  ListenerMetricContextDataItem::getBuildTimerSample)
              .isNotNull();
        }

        @Nested
        public class OnBuildStop {

          @BeforeEach
          public void before() {
            unit.onBuildStart(mockListener);
          }

          @Test
          public void shouldStopBuildTimer() {
            unit.onBuildStop(mockListener, 0);

            assertThat(context.getItemMap().get("endpoint")).extracting(
                    ListenerMetricContextDataItem::getBuildTimer)
                .extracting(Timer::count)
                .isEqualTo(1l);
          }
        }

        @Nested
        public class OnBuildFail {

          @BeforeEach
          public void before() {
            unit.onBuildStart(mockListener);
          }

          @Test
          public void shouldIncreaseErrorCounter() {
            unit.onBuildFail(mockListener, new RuntimeException(""));

            assertThat(context.getItemMap().get("endpoint"))
                .extracting(ListenerMetricContextDataItem::getBuildFailedCounter)
                .extracting(Counter::count)
                .isEqualTo(1.0);
          }
        }
      }

      @Nested
      public class OnFlushStart {

        @BeforeEach
        public void before() {
          unit.onCreate(mockListener);
        }

        @Test
        public void shouldStartFlushTimer() {
          unit.onFlushStart(mockListener);

          assertThat(context.getItemMap().get("endpoint")).extracting(
                  ListenerMetricContextDataItem::getFlushTimerSample)
              .isNotNull();
        }

        @Nested
        public class OnFlushStop {

          @BeforeEach
          public void before() {
            unit.onFlushStart(mockListener);
          }

          @Test
          public void shouldStopFlushTimer() {
            unit.onFlushStop(mockListener, 0);

            assertThat(context.getItemMap().get("endpoint")).extracting(
                    ListenerMetricContextDataItem::getFlushTimer)
                .extracting(Timer::count)
                .isEqualTo(1l);
          }
        }

        @Nested
        public class OnFlushFail {

          @BeforeEach
          public void before() {
            unit.onFlushStart(mockListener);
          }

          @Test
          public void shouldIncreaseErrorCounter() {
            unit.onFlushFail(mockListener, new RuntimeException(""));

            assertThat(context.getItemMap().get("endpoint"))
                .extracting(ListenerMetricContextDataItem::getFlushFailedCounter)
                .extracting(Counter::count)
                .isEqualTo(1.0);
          }
        }
      }

    }

    @Nested
    public class OnStop {

      @BeforeEach
      public void before() {
        unit.onStart();
      }

      @Test
      public void shouldStopTotalTimer() {
        unit.onStop();

        ListenerMetricContext context = unit.getContext();
        assertThat(context.getTotalTimer())
            .extracting(Timer::count)
            .isEqualTo(1l);
      }
    }
  }
}
