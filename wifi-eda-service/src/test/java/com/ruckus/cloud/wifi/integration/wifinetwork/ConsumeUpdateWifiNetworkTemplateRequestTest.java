package com.ruckus.cloud.wifi.integration.wifinetwork;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.templateString;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.HOST_APPROVAL_EMAIL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.protobuf.BoolValue;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiNetworkRadiusServerProfileSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenuePortalTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeUpdateWifiNetworkTemplateRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  class whenConsumeUpdateWifiNetworkRequest {

    private final static String networkName = "networkName_update";
    private final static String ssid = "ssid_update";

    private String wifiNetworkId;
    private Short vlanId;

    @Payload("HostApproval")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadClickthrough() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
            n.setName("networkName_update");
            n.getWlan().setSsid("ssid_update");
          });
      return networkRequest;
    }

    @Payload("HostApprovalWithEmail")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestWifiNetwork payloadHostApprovalWithEmail() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestWifiNetwork(
          GuestNetworkTypeEnum.HostApproval,
          n -> {
            n.setName("networkName_update");
            n.getWlan().setSsid("ssid_update");
            n.getGuestPortal().getHostGuestConfig().setHostEmails(List.of(HOST_APPROVAL_EMAIL));
          });
      return networkRequest;
    }

    @BeforeEach
    void givenOneGuestWifiNetworkPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant, @Template final Venue venue) {
      final GuestNetwork guestNetwork = (GuestNetwork) network(GuestNetwork.class).generate();
      final GuestPortal guestPortal = guestPortal(GuestNetworkTypeEnum.HostApproval).generate();
      guestNetwork.setPortalServiceProfileId(randomId());
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      guestNetwork.setIsTemplate(true);
      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
          .setNetwork(always(guestNetwork)).setVenue(always(venue)).setIsTemplate(always(true)).generate();
      guestNetwork.setNetworkVenues(List.of(networkVenue));
      final var venuePortal = VenuePortalTestFixture.randomVenuePortal(tenant, vportal -> {
        vportal.setNetworkVenue(networkVenue);
        vportal.setNetworkPortal(guestPortal);
      });
      networkVenue.setVenuePortal(venuePortal);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      wifiNetworkId = guestNetwork.getId();
      vlanId = guestNetwork.getWlan().getVlanId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiNetworkTemplateId", wifiNetworkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK_TEMPLATE, payload = @Payload("HostApproval"))
    void thenUpdatedGuestWifiNetworkClickThroughWithPortalServiceProfile(TxCtx txCtx)
        throws InvalidProtocolBufferException {
      validateWifiCfgChangeMessage(txCtx);
      validateCmnCfgCollectorMessage(txCtx);
      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(txCtx.getTenant());
      validateActivityMessages(txCtx);
      validGuestWifiNetwork();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_NETWORK_TEMPLATE, payload = @Payload("HostApprovalWithEmail"))
    void thenUpdatedGuestWifiNetworkHostApprovalWithEmail(TxCtx txCtx)
        throws InvalidProtocolBufferException {
      validateWifiCfgChangeMessage(txCtx);
      validateCmnCfgCollectorMessage(txCtx);
      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(txCtx.getTenant());
      validateActivityMessages(txCtx);
      validGuestWifiNetwork();
      validHostGuestConfig();
    }

    private void validateWifiCfgChangeMessage(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
              .getValue(txCtx);
      assertThat(wifiCfgChangeMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());
      WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();

      assertThat(wifiConfigChange)
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestNetwork()).findFirst().get()
                  .getGuestNetwork())
          .matches(n -> n.getId().equals(StringValue.of(wifiNetworkId)));

      assertThat(wifiConfigChange)
          .extracting(
              p -> p.getOperationList().stream().filter(o -> o.hasGuestPortal()).findFirst().get()
                  .getGuestPortal())
          .matches(gp -> Objects.nonNull(gp.getId()));
    }

    private void validateCmnCfgCollectorMessage(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(txCtx);
      assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .matches(p -> p.getTenantId().equals(txCtx.getTenant()))
          .matches(p -> p.getRequestId().equals(txCtx.getTxId()))
          .extracting(p -> p.getOperationsList().stream().filter(o ->
                  EsConstants.Index.NETWORK.equals(o.getIndex())).findFirst().get())
          .matches(p -> p.getOpType() == OpType.MOD)
          .matches(p -> p.getId().equals(wifiNetworkId));
    }

    private void validateActivityMessages(TxCtx txCtx) throws InvalidProtocolBufferException {
      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
              .getValue(txCtx);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTenant());
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.UPDATE_WIFI_NETWORK_TEMPLATE))
          .extracting(ConfigurationStatus::getEventDate)
          .isNotNull();
    }

    private void validGuestWifiNetwork() {
      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, wifiNetworkId);
      assertThat(network)
          .isNotNull()
          .matches(n -> n.getId().equals(wifiNetworkId))
          .matches(n -> n.getName().equals(networkName))
          .matches(n -> n.getType().equals(NetworkTypeEnum.GUEST))
          .matches(n -> n.getIsTemplate().equals(Boolean.TRUE))
          .extracting(GuestNetwork::getWlan)
          .isNotNull()
          .matches(wlan -> wlan.getSsid().equals(ssid))
          .matches(wlan -> wlan.getVlanId().equals(vlanId));
    }

    private void validHostGuestConfig() {
      final GuestNetwork network = repositoryUtil.find(
          GuestNetwork.class, wifiNetworkId);
      assertThat(network)
          .isNotNull()
          .extracting(GuestNetwork::getGuestPortal)
          .isNotNull()
          .extracting(GuestPortal::getHostGuestConfig)
          .isNotNull()
          .matches(hostGuestConfig -> hostGuestConfig.getHostEmails().equals(List.of(HOST_APPROVAL_EMAIL)));
    }
  }

  @Nested
  @Disabled("Enable when Activate Radius Template is ready")
  class ConsumeUpdateWifiNetworkRequestOnAAANetworkWithRadius {

    private String profileId;
    private String networkId;
    private String RADIUS_ID = "radiusId";
    private String WIFI_NETWORK_ID = "wifiNetworkTemplateId";

    @BeforeEach
    void beforeEach(Radius radius, AAANetwork network) {
      profileId = radius.getId();
      networkId = network.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Test
    void updateAAAWifiNetworkWithoutRadius(Tenant tenant, @Template AAANetwork network) {
      var tenantId = tenant.getId();
      //activate Radius on network
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          randomTxId(),
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
          randomName(),
          requestParams(),
          "");

      //update enableAuthProxy = true
      var settings = new WifiNetworkRadiusServerProfileSettings();
      settings.setEnableAuthProxy(true);
      messageUtil.sendWifiCfgRequest(
          tenantId,
          randomTxId(),
          CfgAction.UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS,
          randomName(),
          new RequestParams().addPathVariable(WIFI_NETWORK_ID, networkId),
          settings);

      //update AAA network without Radius settings
      var aaaWifiNetwork = Generators.aaaWifiNetwork().generate();
      messageUtil.sendWifiCfgRequest(
          tenantId,
          randomTxId(),
          CfgAction.UPDATE_WIFI_NETWORK_TEMPLATE,
          randomName(),
          new RequestParams().addPathVariable("wifiNetworkTemplateId", networkId),
          aaaWifiNetwork);
      final var savedNetwork = repositoryUtil.find(AAANetwork.class, network.getId());
      //ensure Radius settings are not overridden and ignored during the merge process
      assertTrue(savedNetwork.getEnableAuthProxy());
      assertEquals(savedNetwork.getAuthRadius().getId(), profileId);
    }
  }

  @Test
  void updateOpenWifiNetwork_whenTurnOnMulticastFilter(Tenant tenant, @Template Venue venue, @Template OpenNetwork network) {
    var networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    var openWifiNetwork = Generators.openWifiNetwork().generate();
    openWifiNetwork.getWlan().getAdvancedCustomization().setMulticastFilterEnabled(true);
    openWifiNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_WIFI_NETWORK_TEMPLATE,
        randomName(),
        new RequestParams().addPathVariable("wifiNetworkTemplateId", network.getId()),
        openWifiNetwork);

    final var savedNetwork = repositoryUtil.find(
        OpenNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .extracting(OpenNetwork::getWlan)
        .isNotNull()
        .extracting(wlan -> wlan.getAdvancedCustomization().getMulticastFilterEnabled())
        .isEqualTo(true);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
            WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue).first().extracting(operation ->
            operation.getWlanVenue().getMulticastFilterDrop())
        .isEqualTo(true);
  }

  @Test
  void updateOpenWifiNetworkWithQosMapSet(Tenant tenant, @Template Venue venue, @Template OpenNetwork network) {
    var networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
    networkVenue.setIsTemplate(true);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
    var openWifiNetwork = Generators.openWifiNetwork().generate();
    openWifiNetwork.getWlan().getAdvancedCustomization().setQosMapSetEnabled(true);
    openWifiNetwork.setId(network.getId());

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.UPDATE_WIFI_NETWORK_TEMPLATE,
        randomName(),
        new RequestParams().addPathVariable("wifiNetworkTemplateId", network.getId()),
        openWifiNetwork);

    final var savedNetwork = repositoryUtil.find(
        OpenNetwork.class, network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .matches(n -> n.getId().equals(savedNetwork.getId()))
        .matches(n -> n.getName().equals(savedNetwork.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .matches(n -> n.getIsTemplate().equals(true))
        .extracting(OpenNetwork::getWlan)
        .isNotNull()
        .matches((wlan -> wlan.getAdvancedCustomization().getQosMapSetEnabled()))
        .matches(wlan -> wlan.getAdvancedCustomization().getQosMapSetOptions().getRules().size() == 8);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestId);
    assertThat(ddccmCfgRequestMessage.getPayload())
        .extracting(WifiConfigRequest::getOperationsList).asList().isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasWlanVenue)
        .first()
        .extracting(o -> o.getWlanVenue().getAdvancedCustomization().getQosMap())
        .matches(qosMap -> qosMap.getEnabled().equals(BoolValue.of(true)))
        .matches(qosMap -> qosMap.getDscpValuesCount() == 8);

    final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenant.getId(), requestId);
    WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
    assertThat(wifiConfigChange.getOperationList())
        .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasQosMapRule)
        .hasSize(8);
    assertThat(wifiConfigChange.getOperationList())
        .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasWlan)
        .first()
        .matches(o -> o.getWlan().getAdvancedCustomization().getQosMapSetEnabled().equals(BoolValue.of(true)));
  }

  @Test
  void updateOweWifiNetworkWithoutNetworkIdInRequestPayload(Tenant tenant) {
    var networkRequest = Generators.openWifiNetwork()
        .setId(templateString(randomId()))
        .setName(serialName("oweTransitionRequest"))
        .setDescription(randomString(64))
        .setWlan(Generators.oweTransitionWifiWlan()).generate();

    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.ADD_WIFI_NETWORK_TEMPLATE,
        randomName(),
        new RequestParams(),
        networkRequest);

    OpenNetwork openOweMasterNetwork = repositoryUtil.find(OpenNetwork.class,
        networkRequest.getId());
    OpenNetwork openOweSlaveNetwork = repositoryUtil.find(OpenNetwork.class,
        openOweMasterNetwork.getOwePairNetworkId());

    // update owe network name without network id
    networkRequest.setName("updateOweNetworkName");
    networkRequest.setId(null);

    var requestId2 = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId2,
        CfgAction.UPDATE_WIFI_NETWORK_TEMPLATE,
        randomName(),
        new RequestParams().addPathVariable("wifiNetworkTemplateId", openOweMasterNetwork.getId()),
        networkRequest);

    final var savedNetwork = repositoryUtil.find(
        OpenNetwork.class, openOweSlaveNetwork.getId());
    assertNotNull(savedNetwork.getOwePairNetworkId());
  }
}
