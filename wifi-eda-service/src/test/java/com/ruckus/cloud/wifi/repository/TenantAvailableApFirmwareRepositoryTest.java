package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.servicemodel.projection.ApModelFirmwaresProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.ApModelFirmwareLabelProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.ApModelVersionProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant(id) VALUES ('8bfeae74-63fb-4269-a5a0-a8e9b8189f07'), ('4k3kook3-o232l-3le2-22ef-flf4lf4lr34'),
    ('9520ee78bb1222ec84220242ac120002'), ('5krkee5ewrqw4rwedeederf2ac643256');
    INSERT INTO ap_version(id, supported_ap_models, labels, category, created_date, updated_date) VALUES
    ('6.2.0.103.800', '["R500", "R510", "R610"]', 'ga', 'RECOMMENDED', '2022-03-23 00:12:53.427', '2022-03-23 00:12:53.427'),
    ('6.2.0.103.1234', '[ "R500", "R510", "R610"]', 'ga', 'RECOMMENDED', '2022-03-23 00:13:53.427', '2022-03-23 00:13:53.427'),
    ('6.2.2.103.434', '["R510","R610","R550"]', 'ga', 'RECOMMENDED', '2022-03-23 00:14:53.427', '2022-03-23 00:14:53.427'),
    ('6.2.2.103.555', '["R510","R610","R550"]', 'ga', 'RECOMMENDED', '2022-03-23 00:15:53.427', '2022-03-23 00:15:53.427'),
    ('6.2.4.103.983', null, 'ga', 'RECOMMENDED', '2022-03-23 00:16:53.427', '2022-03-23 00:16:53.427'),
    ('7.0.0.103.300', '["R770"]', 'legacyAlpha,beta,ga', 'RECOMMENDED', '2022-03-23 00:17:53.427', '2022-03-23 00:17:53.427'),
    ('7.0.0.105.230', '["R550","T350:T350e","R770"]', 'legacyAlpha', 'RECOMMENDED', '2022-03-23 00:18:53.427', '2022-03-23 00:18:53.427'),
    ('6.2.0.103.123', '["R500","R600","R510","R610"]', 'ga', 'RECOMMENDED', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
    ('6.2.2.103.556', '["R550","R650","R750","R770"]', 'ga', 'RECOMMENDED', '2022-03-23 00:11:53.428', '2022-03-23 00:11:53.428'),
    ('7.0.0.999.100', '["R550","T350:T350e","R770"]', null, 'BETA', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427');
    INSERT INTO tenant_available_ap_firmware(id, ap_version, tenant, created_date, updated_date)
    VALUES ('d969f2fc-f813-4c92-8af4-a900461a55fb', '6.2.0.103.1234', '8bfeae74-63fb-4269-a5a0-a8e9b8189f07', '2022-03-24 00:02:53.427', '2022-03-24 00:02:53.427'),
    ('wekekdd-e423-ef34-der2-23ee3edd3edd', '6.2.2.103.555', '4k3kook3-o232l-3le2-22ef-flf4lf4lr34', '2022-03-24 00:01:53.427', '2022-03-24 00:01:53.427'),
    ('7720c78b733e41e19275a6abb24a0f3f76.2.0.103.800', '6.2.0.103.800', '9520ee78bb1222ec84220242ac120002', '2022-03-24 00:11:53.427', '2022-03-24 00:11:53.427'),
    ('7720c78b733e41e19275a6abb24a0f3f86.2.0.103.123', '6.2.0.103.123', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
    ('7720c78b733e41e19275a6abb24a0f3f96.2.2.103.434', '6.2.2.103.434', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:54.427', '2022-03-23 00:11:54.427'),
    ('7720c78b733e41e19275a6abb24a0f3d16.2.2.103.556', '6.2.2.103.556', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:55.427', '2022-03-23 00:11:55.427'),
    ('7720c78b733e41e19275a6abb24a0f3d26.2.4.103.983', '6.2.4.103.983', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:56.427', '2022-03-23 00:11:56.427'),
    ('7720c78b733e41e19275a6abb24a0f3d37.0.0.103.300', '7.0.0.103.300', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:57.427', '2022-03-23 00:11:57.427'),
    ('7720c78b733e41e19275a6abb24a0f3d37.0.0.999.100', '7.0.0.999.100', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:10:57.427', '2022-03-23 00:10:57.427'),
    ('7720c78b733e41e19275a6abb24a0f3d37.0.0.105.230', '7.0.0.105.230', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:10:57.427', '2022-03-23 00:10:57.427');
    """)
public class TenantAvailableApFirmwareRepositoryTest {

  @Autowired
  private TenantAvailableApFirmwareRepository repository;

  @Test
  public void testFindByTenantIdAndApVersionId() {
    Assertions.assertThat(repository.findByTenantIdAndApVersionId("8bfeae74-63fb-4269-a5a0-a8e9b8189f07", "6.2.0.103.1234"))
        .isPresent()
        .get()
        .satisfies(taaf -> {
          assertThat(taaf.getApVersion().getId()).isEqualTo("6.2.0.103.1234");
          assertThat(taaf.getTenant().getId()).isEqualTo("8bfeae74-63fb-4269-a5a0-a8e9b8189f07");
        });

    Assertions.assertThat(repository.findByTenantIdAndApVersionId("8bfeae74-63fb-4269-a5a0-a8e9b8189f07", "6.2.2.103.555"))
        .isNotPresent();

    Assertions.assertThat(repository.findByTenantIdAndApVersionId("4k3kook3-o232l-3le2-22ef-flf4lf4lr34", "6.2.0.103.1234"))
        .isNotPresent();
  }

  @Test
  public void findApModelFirmwaresByTenantIdV2() {
    var result = repository.findApModelFirmwaresByTenantIdV2("9520ee78bb1222ec84220242ac120002");
    Assertions.assertThat(result)
        .isNotEmpty()
        .hasSize(7)
        .extracting(
            ApModelFirmwareLabelProjection::getId, ApModelFirmwareLabelProjection::getLabelList,
            ApModelFirmwareLabelProjection::getSupportedApModelList)
        .containsExactly(
            tuple("7.0.0.105.230", List.of("legacyAlpha"), List.of("R550", "T350:T350e", "R770")),
            tuple("7.0.0.103.300", List.of("legacyAlpha", "beta", "ga"), List.of("R770")),
            tuple("6.2.4.103.983", List.of("ga"), List.of()),
            tuple("6.2.2.103.556", List.of("ga"), List.of("R550", "R650", "R750", "R770")),
            tuple("6.2.2.103.434", List.of("ga"), List.of("R510", "R610", "R550")),
            tuple("6.2.0.103.800", List.of("ga"), List.of("R500", "R510", "R610")),
            tuple("6.2.0.103.123", List.of("ga"), List.of("R500", "R600", "R510", "R610")));
  }

  @Deprecated(since = "Use findApModelFirmwaresByTenantIdV2 instead")
  @Test
  public void findApModelFirmwaresByTenantId() {
    Assertions.assertThat(
            repository.findApModelFirmwaresByTenantId("9520ee78bb1222ec84220242ac120002"))
        .isNotEmpty()
        .hasSize(8)
        .extracting(
            ApModelFirmwaresProjection::getId, ApModelFirmwaresProjection::getSupportedApModels)
        .containsExactlyInAnyOrder(
            tuple("7.0.0.999.100", "[\"R550\",\"T350:T350e\",\"R770\"]"),
            tuple("7.0.0.105.230", "[\"R550\",\"T350:T350e\",\"R770\"]"),
            tuple("7.0.0.103.300", "[\"R770\"]"),
            tuple("6.2.4.103.983", null),
            tuple("6.2.2.103.556", "[\"R550\",\"R650\",\"R750\",\"R770\"]"),
            tuple("6.2.2.103.434", "[\"R510\",\"R610\",\"R550\"]"),
            tuple("6.2.0.103.800", "[\"R500\", \"R510\", \"R610\"]"),
            tuple("6.2.0.103.123", "[\"R500\",\"R600\",\"R510\",\"R610\"]"));
  }

  @Test
  public void testExistsByTenantIdAndApVersionId() {
    Assertions.assertThat(repository.existsByTenantIdAndApVersionId("8bfeae74-63fb-4269-a5a0-a8e9b8189f07", "6.2.0.103.1234"))
        .isTrue();

    Assertions.assertThat(repository.existsByTenantIdAndApVersionId("8bfeae74-63fb-4269-a5a0-a8e9b8189f07", "6.2.2.103.555"))
        .isFalse();

    Assertions.assertThat(repository.existsByTenantIdAndApVersionId("4k3kook3-o232l-3le2-22ef-flf4lf4lr34", "6.2.0.103.1234"))
        .isFalse();
  }

  @Test
  void testFindModelLatestSupportedRecommendedVersionByTenantId() {
    assertThat(repository
        .findModelLatestSupportedRecommendedVersionByTenantId("9520ee78bb1222ec84220242ac120002", "R550"))
        .isPresent()
        .get()
        .isEqualTo("7.0.0.105.230");

    assertThat(repository
        .findModelLatestSupportedRecommendedVersionByTenantId("9520ee78bb1222ec84220242ac120002", "R770"))
        .isPresent()
        .get()
        .isEqualTo("7.0.0.105.230");

    assertThat(repository
        .findModelLatestSupportedRecommendedVersionByTenantId("9520ee78bb1222ec84220242ac120002", "R970"))
        .isEmpty();
    assertThat(repository
        .findModelLatestSupportedRecommendedVersionByTenantId("4k3kook3-o232l-3le2-22ef-flf4lf4lr34", "R500"))
        .isEmpty();
  }

  @Test
  void testFindBiggestVersionByTenantId() {
    assertThat(repository
        .findBiggestVersionByTenantId("9520ee78bb1222ec84220242ac120002"))
        .isPresent()
        .get()
        .isEqualTo("7.0.0.105.230");
  }

  @Test
  @Sql(statements = """
    INSERT INTO tenant(id) VALUES ('8bfeae74-63fb-4269-a5a0-a8e9b8189f07'), ('4k3kook3-o232l-3le2-22ef-flf4lf4lr34');
    INSERT INTO ap_version(id, category, supported_ap_models) VALUES 
    ('SOLO.1.0.0.0', 'SOLO', null),
    ('6.2.0.103.800', 'RECOMMENDED', '["R500", "R510", "R610"]'),
    ('6.2.0.103.1234', 'RECOMMENDED', '[ "R500", "R510", "R610" ]'),
    ('6.2.2.103.434', 'RECOMMENDED', '["R510","R610","R550"]'),
    ('6.2.2.103.555', 'RECOMMENDED', '["R510","R610","R550"]'),
    ('6.2.4.103.983', 'RECOMMENDED', null),
    ('7.0.0.103.300', 'RECOMMENDED', '["R770"]'),
    ('7.0.0.105.230', 'RECOMMENDED', '["R550","T350:T350e","R770"]'),
    ('7.0.0.999.1', 'BETA', '["R550","T350:T350e","R770"]');
    """)
  public void testGetModelLatestSupportedRecommendedVersionByTenantId() {
    createTenantAvailableApFirmware("8bfeae74-63fb-4269-a5a0-a8e9b8189f07",
        List.of("SOLO.1.0.0.0", "6.2.0.103.1234", "6.2.2.103.555", "6.2.4.103.983", "7.0.0.105.230", "7.0.0.999.1"));
    assertThat(repository.getModelLatestSupportedRecommendedVersionByTenantId("8bfeae74-63fb-4269-a5a0-a8e9b8189f07"))
        .isNotNull()
        .hasSize(6)
        .extracting(ApModelVersionProjection::getApModel, ApModelVersionProjection::getApVersion)
        .containsExactlyInAnyOrder(
            tuple("R500", "6.2.0.103.1234"),
            tuple("R510", "6.2.2.103.555"),
            tuple("R610", "6.2.2.103.555"),
            tuple("R550", "7.0.0.105.230"),
            tuple("R770", "7.0.0.105.230"),
            tuple("T350:T350e", "7.0.0.105.230"));

    createTenantAvailableApFirmware("4k3kook3-o232l-3le2-22ef-flf4lf4lr34",
        List.of("SOLO.1.0.0.0", "6.2.0.103.800", "6.2.4.103.983", "7.0.0.103.300", "7.0.0.999.1"));
    assertThat(repository.getModelLatestSupportedRecommendedVersionByTenantId("4k3kook3-o232l-3le2-22ef-flf4lf4lr34"))
        .isNotNull()
        .hasSize(4)
        .extracting(ApModelVersionProjection::getApModel, ApModelVersionProjection::getApVersion)
        .containsExactlyInAnyOrder(
            tuple("R500", "6.2.0.103.800"),
            tuple("R510", "6.2.0.103.800"),
            tuple("R610", "6.2.0.103.800"),
            tuple("R770", "7.0.0.103.300"));
  }

  @Test
  public void testExistsByTenantId() {
    assertThat(repository.existsByTenantId("8bfeae74-63fb-4269-a5a0-a8e9b8189f07")).isTrue();
    assertThat(repository.existsByTenantId("4k3kook3-o232l-3le2-22ef-flf4lf4lr34")).isTrue();
    assertThat(repository.existsByTenantId("9520ee78bb1222ec84220242ac120002")).isTrue();
    assertThat(repository.existsByTenantId("5krkee5ewrqw4rwedeederf2ac643256")).isFalse();
  }

  private void createTenantAvailableApFirmware(String tenantId, List<String> versions) {
    versions.forEach(version -> {
      TenantAvailableApFirmware taaf = new TenantAvailableApFirmware();
      taaf.setTenant(new Tenant(tenantId));
      taaf.setApVersion(new ApVersion(version));
      repository.save(taaf);
    });
  }
}
