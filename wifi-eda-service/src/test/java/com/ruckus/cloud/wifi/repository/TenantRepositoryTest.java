package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tenant (id, predefined_aaa_providers, predefined_aaa_services) VALUES ('6de6a5239a1441cfb9c7fde93aa613fe', '["Cloud4WiX"]', '["ruckSM-AAA","dpsk-AAA"]');
    INSERT INTO tenant (id, predefined_aaa_services) VALUES ('67849883186e4d4d9ae050a0cb2a3592', '["ruckSM-AAA"]');
    """)
public class TenantRepositoryTest {

  @Autowired
  private TenantRepository tenantRepository;

  @Test
  void testFindById() {
    assertTrue(tenantRepository.findById("4c8279f79307415fa9e4c88a1819f0fc").isPresent());
  }

  @Test
  void testGetById() {
    assertNotNull(tenantRepository.getReferenceById("4c8279f79307415fa9e4c88a1819f0fc"));
  }

  @Test
  void testFindTenantIdByPredefinedAaaProvidersNotEmpty() {
    var result = tenantRepository.findTenantIdByPredefinedAaaProvidersNotEmpty();
    assertEquals(1, result.size());
    assertTrue(result.contains("6de6a5239a1441cfb9c7fde93aa613fe"));
  }

  @Test
  void testUpdateAaaProvidersWithEmptyList() {
    // The AaaProviders is actually a string using the convertor to convert to a list.
    // This case is to ensure that even if the list is empty, the IsNotNull check can also work as expected.
    tenantRepository.findById("6de6a5239a1441cfb9c7fde93aa613fe").ifPresent(target -> {
      target.setPredefinedAaaProviders(List.of());
      tenantRepository.save(target);
    });
    var result = tenantRepository.findTenantIdByPredefinedAaaProvidersNotEmpty();
    assertEquals(0, result.size());
  }

  @Test
  void testFindTenantIdByPredefinedAaaServicesNotEmpty() {
    var result = tenantRepository.findTenantIdByPredefinedAaaServicesNotEmpty();
    assertEquals(2, result.size());
    assertTrue(result.contains("6de6a5239a1441cfb9c7fde93aa613fe"));
    assertTrue(result.contains("67849883186e4d4d9ae050a0cb2a3592"));
  }

  @Test
  void testUpdateAaaServicesWithEmptyList() {
    // The AaaServices is actually a string using the convertor to convert to a list.
    // This case is to ensure that even if the list is empty, the IsNotNull check can also work as expected.
    tenantRepository.findById("6de6a5239a1441cfb9c7fde93aa613fe").ifPresent(target -> {
      target.setPredefinedAaaServices(List.of());
      tenantRepository.save(target);
    });
    var result = tenantRepository.findTenantIdByPredefinedAaaServicesNotEmpty();
    assertEquals(1, result.size());
    assertTrue(result.contains("67849883186e4d4d9ae050a0cb2a3592"));
  }

  @Sql(statements = """
    INSERT INTO ap_version (id) VALUES ('6.2.2.103.182');
    INSERT INTO tenant (id, latest_release_version) VALUES ('16f188ad-345c-4765-bc0e-e556a6412fcc', '6.2.2.103.182');                        
    """)
  @Test
  void testFindLatestReleaseVersionById() {
    String tenantId = "16f188ad-345c-4765-bc0e-e556a6412fcc";
    Assertions.assertThat(tenantRepository.findLatestReleaseVersionById(tenantId))
        .isNotNull()
        .isEqualTo(Optional.of("6.2.2.103.182"));
  }

  @Sql(statements = """
    INSERT INTO ap_version (id) VALUES ('7.0.0.103.200');
    INSERT INTO tenant (id, latest_release_version) VALUES ('333188ad-345c-4765-bc0e-e556a6412444', '7.0.0.103.200');
    INSERT INTO tenant (id, latest_release_version) VALUES ('666188ad-345c-4765-bc0e-e556a6412666', '7.0.0.103.200');
    INSERT INTO tenant (id, latest_release_version) VALUES ('999188ad-345c-4765-bc0e-e556a6412999', '7.0.0.103.200');
    """)
  @Test
  void testFindTenantIdByLatestReleaseVersionNotEmpty() {
    var result = tenantRepository.findTenantIdByLatestReleaseVersionNotEmpty();
    assertTrue(result.contains("333188ad-345c-4765-bc0e-e556a6412444"));
    assertTrue(result.contains("666188ad-345c-4765-bc0e-e556a6412666"));
    assertTrue(result.contains("999188ad-345c-4765-bc0e-e556a6412999"));
  }

  @Test
  void testFindAllIdsOrderByCreatedDate() {
    var result = tenantRepository.findAllIdsOrderByCreatedDate();
    assertFalse(result.isEmpty());
    assertTrue(
        result.containsAll(
            Set.of(
                "4c8279f79307415fa9e4c88a1819f0fc",
                "6de6a5239a1441cfb9c7fde93aa613fe",
                "67849883186e4d4d9ae050a0cb2a3592")));
  }
}
