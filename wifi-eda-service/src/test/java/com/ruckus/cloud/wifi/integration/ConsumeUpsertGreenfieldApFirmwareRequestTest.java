package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.proto.Operation.Action;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareRequest;
import com.ruckus.cloud.wifi.viewmodel.ApModelFirmware;
import com.ruckus.cloud.wifi.viewmodel.ApModelFirmwareSchedules;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
public class ConsumeUpsertGreenfieldApFirmwareRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private MessageCaptors messageCaptors;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Test
  public void testUpsertGreenfieldApFirmware(Tenant tenant) {
    ApVersion version100 = new ApVersion("7.0.0.105.100");
    version100.setSupportedApModels(List.of("R770"));
    repositoryUtil.createOrUpdate(version100, randomTxId(), randomTxId());

    ApModelFirmwareSchedules request = new ApModelFirmwareSchedules();
    request.setTargetFirmwares(List.of(ApModelFirmware.builder().firmware(version100.getId()).apModel("R770").build()));

    String requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgExtendedAction.CREATE_GREENFIELD_AP_FIRMWARES,
        randomName(),
        new RequestParams(),
        request);

    var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor().getValue(tenant.getId(), requestId);

    assertThat(wifiCfgChangeMessage.getPayload())
        .isNotNull()
        .extracting(WifiConfigChange::getOperationList)
        .asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
        .satisfies(ops -> assertThat(ops)
            .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasApModelGreenfieldFirmware)
            .filteredOn(op -> op.getAction() == Action.ADD)
            .hasSize(1));
  }
}
