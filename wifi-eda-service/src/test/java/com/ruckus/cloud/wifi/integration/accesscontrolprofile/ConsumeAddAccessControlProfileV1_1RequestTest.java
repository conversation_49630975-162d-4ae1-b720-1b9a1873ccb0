package com.ruckus.cloud.wifi.integration.accesscontrolprofile;

import static com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture.randomAccessControlProfile;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeAddAccessControlProfileV1_1RequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeAddAccessControlProfileV1_1Message {

    @Test
    void givenAccessControlProfileActivatedOnNetwork(Tenant tenant) {
      AccessControlProfile accessControlProfile = randomAccessControlProfile();
      accessControlProfile.setTenant(tenant);

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.ADD_ACCESS_CONTROL_PROFILE_V1_1,
          randomName(),
          new RequestParams(),
          accessControlProfile);

      assertThat(
          messageCaptors
              .getCmnCfgCollectorMessageCaptor()
              .getValue(tenant.getId(), Duration.ofSeconds(1)))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1);

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(
              a -> a.getStep().equals(ApiFlowNames.ADD_ACCESS_CONTROL_PROFILE));
    }
  }
}
