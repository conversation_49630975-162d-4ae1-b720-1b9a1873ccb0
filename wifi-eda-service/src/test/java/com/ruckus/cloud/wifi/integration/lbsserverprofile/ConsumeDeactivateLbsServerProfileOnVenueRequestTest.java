package com.ruckus.cloud.wifi.integration.lbsserverprofile;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static net.logstash.logback.composite.LogstashVersionJsonProvider.DEFAULT_VERSION;
import static org.assertj.core.api.Assertions.assertThat;

@WifiIntegrationTest
public class ConsumeDeactivateLbsServerProfileOnVenueRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeDeactivateLbsServerProfileOnVenueMessage {

    private Venue venue;

    private LbsServerProfile lbsServerProfile;

    @BeforeEach
    void givenLbsServerProfile(final Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = DEFAULT_VERSION) ApVersion defaultVersion) {
      lbsServerProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.lbsServerProfile().generate();
      repositoryUtil.createOrUpdate(lbsServerProfile, tenant.getId(), randomTxId());

      venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
      venue.setLbsServerProfile(lbsServerProfile);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    }

    @Test
    void thenDeactivateAndSendingMessages(Tenant tenant) {
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.DEACTIVATE_LBS_SERVER_PROFILE_ON_VENUE,
          randomName(),
          new RequestParams()
              .addPathVariable("venueId", venue.getId())
              .addPathVariable("lbsServerProfileId", lbsServerProfile.getId()),
          "");

      assertThat(repositoryUtil.find(Venue.class, venue.getId(), tenant.getId()))
          .isNotNull()
          .extracting(Venue::getLbsServerProfile)
          .isNull();

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(1)
          .first()
          .matches(o -> o.getAction() == Action.MODIFY)
          .matches(o -> o.getId().equals(venue.getId()));

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .first()
          .matches(o -> o.getOpType() == OpType.MOD)
          .matches(o -> o.getId().equals(lbsServerProfile.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.DEACTIVATE_LBS_SERVER_PROFILE_ON_VENUE));
    }
  }

}
