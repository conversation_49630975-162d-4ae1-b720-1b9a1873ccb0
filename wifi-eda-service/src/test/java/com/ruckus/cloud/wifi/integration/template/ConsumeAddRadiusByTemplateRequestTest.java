package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_RADIUS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_RADIUS_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_RADIUS;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_DELEGATION_SOURCE_TENANT_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.RADIUS_SERVER_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.TemplateCreateInstanceOperation.newViewProperty;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.util.TemplateRetriever.retrieveTemplate;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRadiusRequestTest;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.RadiusRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddRadiusByTemplateRequestTest extends AbstractRadiusRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  private static final String NETWORK_ID = "networkId";

  @Autowired
  private NetworkRepository networkRepository;
  @Autowired
  private RadiusRepository radiusRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void add_aaaNetwork_with_radius(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AuthRadius and AAANetwork

    Radius radius = RadiusTestFixture.authRadius();
    radius.setName("test-radius");
    radius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius, mspTenant.getId(), randomTxId());
    Radius radiusAdded = repositoryUtil.find(Radius.class, radius.getId(), mspTenantId, true);
    assertRadiusServers(radius, radiusAdded);

    AAANetwork aaaNetwork = NetworkTestFixture.randomAAANetwork(mspTenant, n -> n.setId(randomId()));
    aaaNetwork.setAuthRadius(radiusAdded);
    repositoryUtil.createOrUpdate(aaaNetwork, mspTenantId, randomTxId());

    radiusAdded = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius.getPrimary().getIp(), radius.getPrimary().getPort()));

    assertRadiusNetworks(radiusAdded, 1);

    // create ec radius by msp radius template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_RADIUS, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.RADIUS,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, radiusAdded.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_RADIUS_BY_TEMPLATE, ADD_RADIUS_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_RADIUS, ecTenantId);
    assertActivityStatusSuccess(ADD_RADIUS, ecTenantId);
    assertActivityStatusSuccess(ADD_RADIUS_BY_TEMPLATE, mspTenantId);

    Radius ecRadius = radiusRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecRadius.getId());
    assertEquals(radiusAdded.getId(), ecRadius.getTemplateId());
    assertEquals(radiusAdded.getUpdatedDate().getTime(), ecRadius.getTemplateVersion());
    assertRadiusServers(radiusAdded, ecRadius);

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
            .filter(o -> ecRadius.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecRadius.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );

    assertDdccmCfgRequestNotSent(ecTenantId);

    // add network to link radius

    AAANetwork ecAaaNetwork = NetworkTestFixture.randomAAANetwork(mspTenant, n -> n.setId(randomId()));
    ecAaaNetwork.setAuthRadius(ecRadius);
    repositoryUtil.createOrUpdate(ecAaaNetwork, ecTenantId, randomTxId());

    ecAaaNetwork =
        (AAANetwork) networkRepository.getByIdAndTenantId(ecAaaNetwork.getId(), ecTenantId);
    assertEquals(instanceId, ecAaaNetwork.getAuthRadius().getId());
  }

  @Test
  public void addRadiusByTemplate_changeRadiusTypeFromEcNotAllowed(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AuthRadius
    Radius radius = RadiusTestFixture.authRadius();
    radius.setName("test-radius");
    radius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius, mspTenant.getId(), randomTxId());
    Radius radiusAdded = repositoryUtil.find(Radius.class, radius.getId(), mspTenantId, true);
    assertRadiusServers(radius, radiusAdded);

    radiusAdded = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius.getPrimary().getIp(), radius.getPrimary().getPort()));

    assertRadiusNetworks(radiusAdded, 0);

    // create ec radius by msp radius template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_RADIUS, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.RADIUS,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, radiusAdded.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_RADIUS_BY_TEMPLATE, ADD_RADIUS_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_RADIUS, ecTenantId);
    assertActivityStatusSuccess(ADD_RADIUS, ecTenantId);
    assertActivityStatusSuccess(ADD_RADIUS_BY_TEMPLATE, mspTenantId);

    Radius ecRadius = radiusRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecRadius.getId());
    assertEquals(radiusAdded.getId(), ecRadius.getTemplateId());
    assertEquals(radiusAdded.getUpdatedDate().getTime(), ecRadius.getTemplateVersion());
    assertRadiusServers(radiusAdded, ecRadius);

    var viewOps1 = receiveViewmodelCollectorOperations(1, ecTenantId);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps1, 1),
        () -> assertTrue(viewOps1.stream()
            .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
            .filter(o -> ecRadius.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecRadius.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );

    assertDdccmCfgRequestNotSent(ecTenantId);

    //////////
    // now updating RADIUS type of ecRadius

    // case 1
    // update type -> fail
    // because instance managed by template and request from EC tenant
    ecRadius.setType(RadiusProfileTypeEnum.ACCOUNTING);
    RequestParams rps2 = new RequestParams().addPathVariable("radiusId", ecRadius.getId());
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName,
        rps2, mapToRadiusServerProfile(ecRadius));
    assertActivityStatusFail(UPDATE_RADIUS, Errors.WIFI_10575, ecTenantId);

    // case 2
    // request from MSP tenant
    Map<String, String> rhs = new HashMap<>(Map.of(
        RKS_DELEGATION_SOURCE_TENANT_ID.getName(), mspTenantId
    ));
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName,
        rps2, rhs, mapToRadiusServerProfile(ecRadius), true);
    assertActivityStatusSuccess(UPDATE_RADIUS, ecTenantId);

    var viewOps2 = receiveViewmodelCollectorOperations(1, ecTenantId);
    log.info("viewOps2 {}", viewOps2);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps2, 1),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, ecRadius, 0)
    );

    Radius ecRadiusModified = radiusRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecRadiusModified.getId());
    assertEquals(radiusAdded.getId(), ecRadiusModified.getTemplateId());
    assertRadiusServers(ecRadius, ecRadiusModified);
    assertEquals(RadiusProfileTypeEnum.ACCOUNTING, ecRadiusModified.getType());
  }

  @Test
  public void ec_add_radius_fail_then_msp_activity_should_fail(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AuthRadius

    Radius radius = RadiusTestFixture.authRadius();
    radius.setName("test-radius");
    radius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius, mspTenant.getId(), randomTxId());
    Radius radiusAdded = repositoryUtil.find(Radius.class, radius.getId(), mspTenantId, true);
    assertRadiusServers(radius, radiusAdded);

    // ec tenant add AuthRadius earlier to make template-create-instance fail

    Radius ecRadius1 = RadiusTestFixture.authRadius();
    ecRadius1.setName("test-radius");
    ecRadius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    ecRadius1.setIsTemplate(false);
    repositoryUtil.createOrUpdate(ecRadius1, ecTenantId, randomTxId());
    Radius ecRadiusAdded1 = repositoryUtil.find(Radius.class, ecRadius1.getId(), ecTenantId, false);
    assertRadiusServers(ecRadius1, ecRadiusAdded1);

    // create ec radius by msp radius template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_RADIUS, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.RADIUS,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, radiusAdded.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_RADIUS_BY_TEMPLATE, ADD_RADIUS_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_RADIUS, ecTenantId);
    assertActivityStatusFail(ADD_RADIUS, ecTenantId);
    assertActivityStatusFail(ADD_RADIUS_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void ec_add_radius_fail_msp_should_get_activity_fail_because_incorrect_overrides(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AuthRadius

    Radius radius = RadiusTestFixture.authRadius();
    radius.setName("test-radius");
    radius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius, mspTenant.getId(), randomTxId());
    Radius radiusAdded = repositoryUtil.find(Radius.class, radius.getId(), mspTenantId, true);
    assertRadiusServers(radius, radiusAdded);

    // create ec radius by msp radius template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    instanceCreateRequest.setOverrides(List.of(newViewProperty("x", "x"))); // incorrect

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_RADIUS, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.RADIUS,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, radius.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_RADIUS_BY_TEMPLATE, ADD_RADIUS_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlanNotSent(ecTenantId);
    assertActivityStatusFail(ADD_RADIUS_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void deprecated_add_aaaNetwork_with_radius(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AuthRadius and AAANetwork

    Radius radius = RadiusTestFixture.authRadius();
    radius.setName("test-radius");
    radius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius, mspTenant.getId(), randomTxId());
    Radius radiusAdded = repositoryUtil.find(Radius.class, radius.getId(), mspTenantId, true);
    assertRadiusServers(radius, radiusAdded);

    AAANetwork aaaNetwork = NetworkTestFixture.randomAAANetwork(mspTenant, n -> n.setId(randomId()));
    aaaNetwork.setAuthRadius(radiusAdded);
    repositoryUtil.createOrUpdate(aaaNetwork, mspTenantId, randomTxId());

    radiusAdded = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius.getPrimary().getIp(), radius.getPrimary().getPort()));

    assertRadiusNetworks(radiusAdded, 1);

    // create ec radius by msp radius template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    instanceCreateRequest.setTargetTenantId(ecTenant.getId());

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_RADIUS, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.RADIUS,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams().addPathVariable(TEMPLATE_ID, radiusAdded.getId());
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_RADIUS_BY_TEMPLATE, ADD_RADIUS_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_RADIUS, ecTenantId);
    assertActivityStatusSuccess(ADD_RADIUS, ecTenantId);
    assertActivityStatusSuccess(ADD_RADIUS_BY_TEMPLATE, mspTenantId);

    Radius ecRadius = radiusRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecRadius.getId());
    assertEquals(radiusAdded.getId(), ecRadius.getTemplateId());
    assertEquals(radiusAdded.getUpdatedDate().getTime(), ecRadius.getTemplateVersion());
    assertRadiusServers(radiusAdded, ecRadius);

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
            .filter(o -> ecRadius.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecRadius.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );

    assertDdccmCfgRequestNotSent(ecTenantId);

    // add network to link radius

    AAANetwork ecAaaNetwork = NetworkTestFixture.randomAAANetwork(mspTenant, n -> n.setId(randomId()));
    ecAaaNetwork.setAuthRadius(ecRadius);
    repositoryUtil.createOrUpdate(ecAaaNetwork, ecTenantId, randomTxId());

    ecAaaNetwork =
        (AAANetwork) networkRepository.getByIdAndTenantId(ecAaaNetwork.getId(), ecTenantId);
    assertEquals(instanceId, ecAaaNetwork.getAuthRadius().getId());
  }

  private void assertRadiusServers(Radius expectedRadius, Radius actualRadius) {
    assertNotNull(actualRadius.getId());
    assertEquals(expectedRadius.getName(), actualRadius.getName());
    assertEquals(expectedRadius.getType(), actualRadius.getType());
    // Validate radius primary
    assertEquals(expectedRadius.getPrimary().getIp(), actualRadius.getPrimary().getIp());
    assertEquals(expectedRadius.getPrimary().getPort(), actualRadius.getPrimary().getPort());
    assertEquals(expectedRadius.getPrimary().getSharedSecret(),
        actualRadius.getPrimary().getSharedSecret());
    // Validate radius secondary
    if (expectedRadius.getSecondary() != null) {
      assertEquals(expectedRadius.getSecondary().getIp(), actualRadius.getSecondary().getIp());
      assertEquals(expectedRadius.getSecondary().getPort(), actualRadius.getSecondary().getPort());
      assertEquals(expectedRadius.getSecondary().getSharedSecret(),
          actualRadius.getSecondary().getSharedSecret());
    }
  }

}
