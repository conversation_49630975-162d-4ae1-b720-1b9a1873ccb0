package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_L2ACL_POLICY;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_L2ACL_POLICY_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.L2AclPolicyRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.L2AclPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.HashSet;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddL2AclPolicyByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private L2AclPolicyRepository l2AclPolicyRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void add_l2Acl_policies(Tenant mspTenant, @Template L2AclPolicy policy) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create ec policy set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_L2ACL_POLICY,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.L2ACL_POLICY, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, policy.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_L2ACL_POLICY_BY_TEMPLATE,
        ADD_L2ACL_POLICY_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_L2ACL_POLICY, ecTenantId);
    assertActivityStatusSuccess(ADD_L2ACL_POLICY, ecTenantId);
    assertActivityStatusSuccess(ADD_L2ACL_POLICY_BY_TEMPLATE, mspTenantId);

    L2AclPolicy mspPolicy = repositoryUtil.find(L2AclPolicy.class, policy.getId(), mspTenantId, true);
    L2AclPolicy ecPolicy = l2AclPolicyRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecPolicy.getId());
    assertL2AclPolicy(mspPolicy, ecPolicy);

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert 1 viewmodel ops: L2AclPolicy",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME,
            ecPolicy.getId(), ecPolicy.getName())
    );
  }

  @Test
  public void ec_add_l2Acl_policy_fail_then_msp_activity_should_fail(Tenant mspTenant, @Template L2AclPolicy policy) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // policy already in ec tenant before apply template
    L2AclPolicy existedEcPolicy = L2AclPolicyTestFixture.randomL2AclPolicy(ecTenant, (o) -> {
      o.setName(policy.getName());
    });
    repositoryUtil.createOrUpdate(existedEcPolicy, ecTenant.getId(), randomTxId());

    // create ec policy set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_L2ACL_POLICY,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.L2ACL_POLICY, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, policy.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_L2ACL_POLICY_BY_TEMPLATE,
        ADD_L2ACL_POLICY_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_L2ACL_POLICY, ecTenantId);
    assertActivityStatusFail(ADD_L2ACL_POLICY, ecTenantId);
    assertActivityStatusFail(ApiFlowNames.ADD_L2ACL_POLICY_BY_TEMPLATE, mspTenantId);
  }

  private static void assertViewmodelCollector(List<Operations> operations, OpType opType,
      String index, String id, String name) {
    assertTrue(operations.stream()
        .filter(o -> index.equals(o.getIndex()))
        .filter(o -> id.equals(o.getId()))
        .filter(o -> o.getOpType() == opType)
        .anyMatch(o -> name.equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())));
  }

  private void assertL2AclPolicy(L2AclPolicy source, L2AclPolicy target) {
    if (source == null && target == null) {
      return;
    }

    List<String> sourceMacs = source.getMacAddresses();
    List<String> targetMacs = target.getMacAddresses();

    assertL2AclPolicyMacs(sourceMacs, targetMacs);
  }

  private void assertL2AclPolicyMacs(List<String> sourceMacs, List<String> targetMacs) {
    assertTrue(((sourceMacs == null && targetMacs == null) || (sourceMacs != null && targetMacs != null)), "sourceMacs and targetMacs should be null or not null together");

    if (sourceMacs != null) {
      assertEquals(sourceMacs.size(), targetMacs.size(), "sourceMacs and targetMacs should have equal size");
      HashSet<String> set = new HashSet<>(targetMacs);
      for (String sourceMac : sourceMacs) {
        assertTrue(set.contains(sourceMac), "targetMacs should contain every sourceMac element");
      }
    }
  }

  private void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }
}
