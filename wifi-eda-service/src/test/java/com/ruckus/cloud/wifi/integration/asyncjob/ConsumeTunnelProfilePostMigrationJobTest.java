package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeInteger;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.proto.TunnelProfilePostMigrationJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("TunnelServiceProfileTest")
@WifiIntegrationTest
public class ConsumeTunnelProfilePostMigrationJobTest {
  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  protected MessageCaptors messageCaptors;


  @Nested
  class GivenTunnelProfilePersistedInDb {

    private String tunnelProfileId;

    @BeforeEach
    void givenTunnelProfileRelatedEntitiesPersistedInDb(final Tenant tenant) {
      final TunnelProfile tp = Generators.tunnelProfile()
          .setTenant(always(tenant))
          .setName(always("tp1"))
          .setId(always(randomId()))
          .setMtuSize(rangeInteger())
          .generate();
      final TunnelProfile tpGpe = Generators.tunnelProfile()
          .setTenant(always(tenant))
          .setName(always("tp2"))
          .setId(always(randomId()))
          .setMtuSize(rangeInteger())
          .setTunnelType(always(TunnelTypeEnum.VXLAN_GPE))
          .generate();
      final TunnelProfile tpGre = Generators.tunnelProfile()
          .setTenant(always(tenant))
          .setName(always("tp3"))
          .setId(always(randomId()))
          .setMtuSize(rangeInteger())
          .setTunnelType(always(TunnelTypeEnum.L2GRE))
          .generate();
      repositoryUtil.createOrUpdate(tp, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(tpGpe, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(tpGre, tenant.getId(), randomTxId());
    }

    @Test
    void whenConsumeTunnelProfilePostMigrationJob(final Tenant tenant) {
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId,
          WifiAsyncJob.newBuilder().setTunnelProfilePostMigrationJob(
              TunnelProfilePostMigrationJob.newBuilder()).build());

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId());;
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(3)
          .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
          .isNotEmpty()
          .hasSize(3)
          .satisfiesExactlyInAnyOrder(
              op -> assertThat(op.getDocMap())
                  .containsEntry(Key.TUNNEL_TYPE, ValueUtils.stringValue(TunnelTypeEnum.VXLAN_GPE.name())),
              op -> assertThat(op.getDocMap())
                  .containsEntry(Key.TUNNEL_TYPE, ValueUtils.stringValue(TunnelTypeEnum.VXLAN_GPE.name())),
              op -> assertThat(op.getDocMap())
                  .containsEntry(Key.TUNNEL_TYPE, ValueUtils.stringValue(TunnelTypeEnum.L2GRE.name()))
          );
    }
  }
}
