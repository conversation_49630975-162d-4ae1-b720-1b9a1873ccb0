package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.RecoveryPassphraseSettingsGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.RecoveryPassphraseSettings;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("RecoveryPassphraseSettingsTest")
@WifiIntegrationTest
public class ConsumeRecoveryPassphraseSettingsTest {

  @Autowired
  protected MessageCaptors messageCaptors;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.UPDATE_RECOVERY_PASSPHRASE_SETTINGS)
  class ConsumeUpdateRecoveryPassphraseSettingsTest {

    private String tenantId;
    private String venueId;

    @Payload
    private final RecoveryPassphraseSettings request = new RecoveryPassphraseSettingsGenerator()
        .setPassphrase(randomString().setLength(16).setAllowLetters(false)).generate();

    @BeforeEach
    void givenRequiredDataPersistedInDb(Tenant tenant, Venue venue) {
      tenantId = tenant.getId();
      venueId = venue.getId();
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      final String requestId = txCtxExtension.getRequestId();

      // Validate DDCCM
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(ddccmCfgRequestMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() -> {
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> {
              // For all matched the same request
              assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                          com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                      .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                      .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                      .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                  .hasSize(2);
              // Should contain 1 tenant and 1 venue
              assertThat(ops)
                  .filteredOn(op -> op.getAction() == Action.MODIFY && op.hasTenant())
                  .hasSize(1);
              assertThat(ops)
                  .filteredOn(op -> op.getAction() == Action.MODIFY && op.hasVenue())
                  .hasSize(1)
                  .allMatch(op -> StringUtils.equals(venueId, op.getVenue().getId()))
                  .allMatch(op -> StringUtils.equals(
                      request.getPassphrase(), op.getVenue().getRecoveryWlan().getPskKey()));
            });
      });
    }
  }
}
