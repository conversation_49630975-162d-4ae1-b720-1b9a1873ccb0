package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.SoftGreProfileNetworkVenueActivationRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@WifiUnitTest
public class NetworkSoftGreGatewayFallbackFeatureTest {

  @MockBean
  private SoftGreProfileNetworkVenueActivationRepository repository;

  @SpyBean
  private NetworkSoftGreGatewayFallbackFeature unit;

  @Nested
  class WhenConfigSoftGre {

    @Test
    @FeatureFlag(disable = {WIFI_R370_TOGGLE, WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE})
    void givenR370Disabled(Network network) {
      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE})
    void givenSoftGreDisabled(Network network) {
      network.setNetworkVenues(Collections.emptyList());

      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE})
    void givenSoftGreGatewayFallbackEnabled(Venue venue, Network network) {
      var nv = new NetworkVenue();
      nv.setVenue(venue);
      network.setNetworkVenues(List.of(nv));

      SoftGreProfile enabledProfile = new SoftGreProfile();
      enabledProfile.setGatewayFailbackEnabled(true);
      SoftGreProfileNetworkVenueActivation activation = new SoftGreProfileNetworkVenueActivation();
      activation.setSoftGreProfile(enabledProfile);

      when(repository.findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
          anyString(), anyString(), anyString())).thenReturn(
          Optional.of(activation));

      BDDAssertions.then(unit.test(network)).isTrue();
    }
  }
}
