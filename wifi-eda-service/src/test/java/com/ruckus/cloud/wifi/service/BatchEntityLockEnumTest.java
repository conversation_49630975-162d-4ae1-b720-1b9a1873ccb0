package com.ruckus.cloud.wifi.service;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomNumber;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.repository.EntityIdGenerationLockIndexRepository;
import com.ruckus.cloud.wifi.repository.EntityIdGenerationLockRepository;
import com.ruckus.cloud.wifi.service.EntityIdGenerationLockService.BatchEntityLockEnum;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.springframework.boot.test.mock.mockito.MockBean;

@WifiUnitTest
class BatchEntityLockEnumTest {

  @MockBean
  private EntityIdGenerationLockRepository repository;

  @MockBean
  private EntityIdGenerationLockIndexRepository indexRepository;

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @ParameterizedTest
  @EnumSource(BatchEntityLockEnum.class)
  void testGetAvailableId(BatchEntityLockEnum entityLockEnum) {
    // Given
    final String tenantId = txCtxExtension.getTenantId();
    final int expectedResult = randomNumber(entityLockEnum.getMin(), entityLockEnum.getMax());
    mockRepositoryForGetAvailableId(entityLockEnum, expectedResult);

    // When
    Optional<Integer> result = entityLockEnum.getAvailableId(repository, indexRepository, tenantId);

    // Then
    verifyRepositoryForGetAvailableId(entityLockEnum);
    assertThat(result).isPresent().hasValue(expectedResult);
  }

  private void mockRepositoryForGetAvailableId(BatchEntityLockEnum entityLockEnum, int expectedResult) {
    final String tenantId = txCtxExtension.getTenantId();
    final int min = entityLockEnum.getMin();
    final int max = entityLockEnum.getMax();
    switch (entityLockEnum) {
      case NETWORK_VENUE_AP_WLAN_ID ->
          doReturn(Optional.of(expectedResult))
              .when(repository).getAvailableApWlanIdInNetworkVenue(tenantId, min, max);
      case NETWORK_VENUE_TEMPLATE_AP_WLAN_ID ->
          doReturn(Optional.of(expectedResult))
              .when(repository).getAvailableApWlanIdInNetworkVenueTemplate(tenantId, min, max);
      case APPLICATION_POLICY_RULE_APPLICATION_ID -> doReturn(Optional.of(expectedResult))
          .when(repository).getAvailableApplicationIdInApplicationPolicyRule(tenantId, min, max);
      case AP_LAN_PORT_PROFILE_AP_LAN_PORT_ID -> doReturn(Optional.of(expectedResult))
          .when(repository).getAvailableApLanPortIdInApLanPortProfile(tenantId, min, max);
      case ACCESS_CONTROL_PROFILE_AP_CFG_INDEX -> doReturn(Optional.of(expectedResult))
          .when(repository).getAvailableApCfgIndexInAccessControlProfile(tenantId, min, max);
      case LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID -> doReturn(Optional.of(expectedResult))
          .when(repository).getAvailableEthernetPortProfileIdInLanPortAdoption(tenantId, min, max);
      default -> throw new IllegalArgumentException("Unsupported entity lock enum: " + entityLockEnum);
    }
  }

  private void verifyRepositoryForGetAvailableId(BatchEntityLockEnum entityLockEnum) {
    final String tenantId = txCtxExtension.getTenantId();
    final int min = entityLockEnum.getMin();
    final int max = entityLockEnum.getMax();
    switch (entityLockEnum) {
      case NETWORK_VENUE_AP_WLAN_ID ->
          verify(repository).getAvailableApWlanIdInNetworkVenue(tenantId, min, max);
      case NETWORK_VENUE_TEMPLATE_AP_WLAN_ID ->
          verify(repository).getAvailableApWlanIdInNetworkVenueTemplate(tenantId, min, max);
      case APPLICATION_POLICY_RULE_APPLICATION_ID ->
          verify(repository).getAvailableApplicationIdInApplicationPolicyRule(tenantId, min, max);
      case AP_LAN_PORT_PROFILE_AP_LAN_PORT_ID ->
          verify(repository).getAvailableApLanPortIdInApLanPortProfile(tenantId, min, max);
      case ACCESS_CONTROL_PROFILE_AP_CFG_INDEX ->
          verify(repository).getAvailableApCfgIndexInAccessControlProfile(tenantId, min, max);
      case LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID ->
          verify(repository).getAvailableEthernetPortProfileIdInLanPortAdoption(tenantId, min, max);
      default -> throw new IllegalArgumentException("Unsupported entity lock enum: " + entityLockEnum);
    }
  }

  @ParameterizedTest
  @EnumSource(BatchEntityLockEnum.class)
  void testGetAvailableIds(BatchEntityLockEnum entityLockEnum) {
    // Given
    final String tenantId = txCtxExtension.getTenantId();
    final int amount = randomNumber(1, 5);
    final List<Integer> expectedResult = IntStream.range(entityLockEnum.getMin(),
        entityLockEnum.getMin() + amount).boxed().toList();
    mockRepositoryForGetAvailableIds(entityLockEnum, expectedResult);

    // When
    List<Integer> result = entityLockEnum.getAvailableIds(repository, indexRepository, tenantId, amount);

    // Then
    verifyRepositoryForGetAvailableIds(entityLockEnum, amount);
    assertThat(result).isNotNull()
        .hasSize(amount)
        .containsExactlyElementsOf(expectedResult);
  }

  private void mockRepositoryForGetAvailableIds(BatchEntityLockEnum entityLockEnum,
      List<Integer> expectedResult) {
    final String tenantId = txCtxExtension.getTenantId();
    final int min = entityLockEnum.getMin();
    final int max = entityLockEnum.getMax();
    switch (entityLockEnum) {
      case NETWORK_VENUE_AP_WLAN_ID ->
        doReturn(expectedResult)
            .when(repository).getAvailableApWlanIdInNetworkVenue(tenantId, min, max, expectedResult.size());
      case NETWORK_VENUE_TEMPLATE_AP_WLAN_ID ->
        doReturn(expectedResult)
            .when(repository).getAvailableApWlanIdInNetworkVenueTemplate(tenantId, min, max, expectedResult.size());
      case APPLICATION_POLICY_RULE_APPLICATION_ID ->
        doReturn(expectedResult)
            .when(repository).getAvailableApplicationIdInApplicationPolicyRule(tenantId, min, max, expectedResult.size());
      case AP_LAN_PORT_PROFILE_AP_LAN_PORT_ID ->
        doReturn(expectedResult)
            .when(repository).getAvailableApLanPortIdInApLanPortProfile(tenantId, min, max, expectedResult.size());
      case ACCESS_CONTROL_PROFILE_AP_CFG_INDEX ->
        doReturn(expectedResult)
            .when(repository).getAvailableApCfgIndexInAccessControlProfile(tenantId, min, max, expectedResult.size());
      case LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID ->
        doReturn(expectedResult)
            .when(repository).getAvailableEthernetPortProfileIdInLanPortAdoption(tenantId, min, max, expectedResult.size());
      default -> throw new IllegalArgumentException("Unsupported entity lock enum: " + entityLockEnum);
    }
  }

  private void verifyRepositoryForGetAvailableIds(BatchEntityLockEnum entityLockEnum, int amount) {
    final String tenantId = txCtxExtension.getTenantId();
    final int min = entityLockEnum.getMin();
    final int max = entityLockEnum.getMax();
    switch (entityLockEnum) {
      case NETWORK_VENUE_AP_WLAN_ID ->
          verify(repository).getAvailableApWlanIdInNetworkVenue(tenantId, min, max, amount);
      case NETWORK_VENUE_TEMPLATE_AP_WLAN_ID ->
          verify(repository).getAvailableApWlanIdInNetworkVenueTemplate(tenantId, min, max, amount);
      case APPLICATION_POLICY_RULE_APPLICATION_ID ->
          verify(repository).getAvailableApplicationIdInApplicationPolicyRule(tenantId, min, max, amount);
      case AP_LAN_PORT_PROFILE_AP_LAN_PORT_ID ->
          verify(repository).getAvailableApLanPortIdInApLanPortProfile(tenantId, min, max, amount);
      case ACCESS_CONTROL_PROFILE_AP_CFG_INDEX ->
          verify(repository).getAvailableApCfgIndexInAccessControlProfile(tenantId, min, max, amount);
      case LAN_PORT_ADOPTION_ETHERNET_PORT_PROFILE_ID ->
          verify(repository).getAvailableEthernetPortProfileIdInLanPortAdoption(tenantId, min, max, amount);
      default -> throw new IllegalArgumentException("Unsupported entity lock enum: " + entityLockEnum);
    }
  }
}