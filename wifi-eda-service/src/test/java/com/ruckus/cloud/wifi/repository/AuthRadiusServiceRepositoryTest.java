package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenantId01');
    
    INSERT INTO radius (id, tenant, is_template)
        VALUES ('radiusId01', 'tenantId01', 'false');
    
    INSERT INTO cloudpath_server(id, tenant)
        VALUES ('cloudpathId01', 'tenantId01');

    INSERT INTO auth_radius_service(id, tenant, radius)
        VALUES ('radiusServiceId01', 'tenantId01', 'radiusId01');
    INSERT INTO auth_radius_service(id, tenant, radius)
        VALUES ('radiusServiceId02', 'tenantId01', 'radiusId01');
    INSERT INTO auth_radius_service(id, tenant, radius, guest_portal, cloudpath_server)
        VALUES ('radiusServiceId03', 'tenantId01', 'radiusId01', null, null);
    INSERT INTO auth_radius_service(id, tenant, radius,  cloudpath_server)
        VALUES ('radiusServiceId04', 'tenantId01', 'radiusId01', 'cloudpathId01');

    INSERT INTO auth_radius_profile (id, tenant, auth_radius_service, hotspot20support_enabled)
        VALUES ('radiusProfileId1', 'tenantId01', 'radiusServiceId01', 'true');;
    INSERT INTO auth_radius_profile (id, tenant, auth_radius_service)
        VALUES ('radiusProfileId3', 'tenantId01', 'radiusServiceId03');
    INSERT INTO auth_radius_profile (id, tenant, auth_radius_service)
        VALUES ('radiusProfileId4', 'tenantId01', 'radiusServiceId04');    
    """)
class AuthRadiusServiceRepositoryTest {

  private static final String TENANT_ID = "tenantId01";

  @Autowired
  AuthRadiusServiceRepository repository;

  @Test
  void findByTenantIdAndAuthRadiusServiceId() {

    var service1 = repository.findByTenantIdAndRadiusIdAndGuestPortalIdIsNullAndCloudpathServerIdIsNullAndHotspot20SupportEnabledIsTrue(TENANT_ID, "radiusId01");
    assertEquals("radiusServiceId01", service1.getId());

    var service3 = repository.findByTenantIdAndRadiusIdAndGuestPortalIdIsNullAndCloudpathServerIdIsNullAndAndHotspot20SupportEnabledIsNotTrue(TENANT_ID, "radiusId01");
    assertEquals("radiusServiceId03", service3.getId());

  }
}