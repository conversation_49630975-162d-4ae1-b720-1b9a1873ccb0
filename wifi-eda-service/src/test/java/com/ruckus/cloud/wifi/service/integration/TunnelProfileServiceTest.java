package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelMtuTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelNetworkSegmentTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.TunnelProfileGenerator;
import com.ruckus.cloud.wifi.mapper.TunnelServiceProfileMerger;
import com.ruckus.cloud.wifi.mapper.TunnelServiceProfileMergerImpl;
import com.ruckus.cloud.wifi.mapper.TunnelServiceProfilePartialMerger;
import com.ruckus.cloud.wifi.mapper.TunnelServiceProfilePartialMergerImpl;
import com.ruckus.cloud.wifi.repository.PinProfileNetworkMappingRepository;
import com.ruckus.cloud.wifi.repository.SdLanProfileNetworkMappingRepository;
import com.ruckus.cloud.wifi.repository.TunnelProfileRepository;
import com.ruckus.cloud.wifi.service.ExtendedTunnelServiceProfileCtrl;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.enums.TunnelProfileAppliedFeature;
import com.ruckus.cloud.wifi.service.impl.ExtendedTunnelServiceProfileCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.ExtendedTunnelServiceProfileCtrlImpl.DefaultSdLanTunnelProfile;
import com.ruckus.cloud.wifi.service.impl.ExtendedTunnelServiceProfileCtrlImpl.DefaultVxLanTunnelProfile;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.function.Consumer;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.ConfigDataApplicationContextInitializer;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ContextConfiguration;

@Tag("TunnelServiceProfileTest")
@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
@ContextConfiguration(initializers = ConfigDataApplicationContextInitializer.class)
@Import({ DefaultVxLanTunnelProfile.class, DefaultSdLanTunnelProfile.class })
class TunnelProfileServiceTest {
  @Autowired
  private ExtendedTunnelServiceProfileCtrl extendedTunnelServiceProfileCtrl;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private DefaultVxLanTunnelProfile defaultVxLanTunnelProfile;
  @Autowired
  private DefaultSdLanTunnelProfile defaultSdLanTunnelProfile;

  private final TunnelProfileGenerator generator = Generators.tunnelProfile();

  @Nested
  class TestTunnelServiceProfile {
    @Test
    void add(final Tenant tenant) {
      final TunnelProfile tunnelProfile = dummyTunnelProfile();

      assertThatNoException().isThrownBy(
          () -> assertThat(extendedTunnelServiceProfileCtrl
              .addTunnelServiceProfile(tunnelProfile)).isNotNull());

      assertThat(repositoryUtil.find(TunnelProfile.class, tunnelProfile.getId(), tenant.getId()))
          .isNotNull()
          .satisfies(assertTunnelProfileServiceProfileSoftly(tunnelProfile));
    }

    @Test
    void addAndGetPinDefaultTunnelProfile(final Tenant tenant) {
      final String tunnelProfileId = randomId();

      assertThatNoException().isThrownBy(
          () -> assertThat(extendedTunnelServiceProfileCtrl
              .addAndGetDefaultVxLANTunnelProfile(tunnelProfileId,
                  TunnelProfileAppliedFeature.PIN)).isNotNull());

      assertThat(repositoryUtil.find(TunnelProfile.class, tunnelProfileId, tenant.getId()))
          .isNotNull()
          .satisfies(assertTunnelProfileServiceProfileSoftly(defaultVxLanTunnelProfile));
    }

    @Test
    void addAndGetSdLanDefaultTunnelProfile(final Tenant tenant) {
      final String tunnelProfileId = randomId();

      assertThatNoException().isThrownBy(
          () -> assertThat(extendedTunnelServiceProfileCtrl
              .addAndGetDefaultVxLANTunnelProfile(tunnelProfileId,
                  TunnelProfileAppliedFeature.SD_LAN)).isNotNull());

      assertThat(repositoryUtil.find(TunnelProfile.class, tunnelProfileId, tenant.getId()))
          .isNotNull()
          .satisfies(assertTunnelProfileServiceProfileSoftly(defaultSdLanTunnelProfile));
    }

    @Test
    void sunnyUpdate(final Tenant tenant) {
      final TunnelProfile tunnelProfile = dummyTunnelProfile();
      final TunnelProfile addedTunnelProfile =
          repositoryUtil.createOrUpdate(tunnelProfile, tenant.getId(), randomTxId());

      tunnelProfile.setName("testTunnelProfile");
      tunnelProfile.setForceFragmentation(true);
      tunnelProfile.setAgeTimeMinutes((short) 333);
      tunnelProfile.setMtuType(TunnelMtuTypeEnum.MANUAL);
      tunnelProfile.setMtuSize(1000);
      tunnelProfile.setType(TunnelNetworkSegmentTypeEnum.VXLAN);
      tunnelProfile.setKeepAliveInterval((short) 3);
      tunnelProfile.setKeepAliveRetry((short) 3);
      tunnelProfile.setMtuRequestRetry((short) 3);
      tunnelProfile.setMtuRequestTimeout((short) 300);

      assertThatNoException().isThrownBy(
          () -> assertThat(extendedTunnelServiceProfileCtrl.updateTunnelServiceProfile(
          addedTunnelProfile.getId(), tunnelProfile)).isNotNull());

      assertThat(repositoryUtil.find(TunnelProfile.class,
          addedTunnelProfile.getId(), tenant.getId()))
          .isNotNull()
          .satisfies(assertTunnelProfileServiceProfileSoftly(tunnelProfile));
    }

    @Test
    void failedUpdatePinDefaultTunnelProfile(final Tenant tenant) {
      TunnelProfile addedTunnelProfile =
          extendedTunnelServiceProfileCtrl.addAndGetDefaultVxLANTunnelProfile(tenant.getId(), TunnelProfileAppliedFeature.PIN);

      addedTunnelProfile.setName("test update default tunnel profile");
      addedTunnelProfile.setAgeTimeMinutes((short)40);

      resetTxId();
      assertThatExceptionOfType(InvalidPropertyValueException.class)
          .isThrownBy(() -> extendedTunnelServiceProfileCtrl
              .updateTunnelServiceProfile(addedTunnelProfile.getId(), addedTunnelProfile))
          .withMessage(Errors.WIFI_10448.message())
          .extracting(InvalidPropertyValueException::getErrorCode)
          .isEqualTo(Errors.WIFI_10448);
    }

    @Test
    void failedUpdateSdLanDefaultTunnelProfile(final Tenant tenant) {
      TunnelProfile addedTunnelProfile =
          extendedTunnelServiceProfileCtrl.addAndGetDefaultVxLANTunnelProfile(tenant.getId(), TunnelProfileAppliedFeature.SD_LAN);

      addedTunnelProfile.setName("test update default tunnel profile");
      addedTunnelProfile.setAgeTimeMinutes((short)40);

      resetTxId();
      assertThatExceptionOfType(InvalidPropertyValueException.class)
          .isThrownBy(() -> extendedTunnelServiceProfileCtrl
              .updateTunnelServiceProfile(addedTunnelProfile.getId(), addedTunnelProfile))
          .withMessage(Errors.WIFI_10448.message())
          .extracting(InvalidPropertyValueException::getErrorCode)
          .isEqualTo(Errors.WIFI_10448);
    }

    @Test
    void sunnyDelete(final Tenant tenant) {
      final TunnelProfile addedTunnelProfile =
          repositoryUtil.createOrUpdate(dummyTunnelProfile(), tenant.getId(), randomTxId());

      assertThatNoException()
          .isThrownBy(() -> extendedTunnelServiceProfileCtrl
              .deleteTunnelServiceProfile(addedTunnelProfile.getId()));

      assertThat(repositoryUtil.find(
          TunnelProfile.class, addedTunnelProfile.getId(), tenant.getId()))
          .isNull();
    }

    @Test
    void failedDeleteDcTunnelProfileInRegularSetting(final Tenant tenant, final Venue venue, final Network network) {
      final TunnelProfile addedDcTunnelProfile =
          repositoryUtil.createOrUpdate(dummyTunnelProfile(), tenant.getId(), randomTxId());

      SdLanProfile sdLanProfile = new SdLanProfile(randomId());
      repositoryUtil.createOrUpdate(sdLanProfile, tenant.getId(), randomTxId());

      SdLanProfileRegularSetting regularSetting = new SdLanProfileRegularSetting(randomId());
      regularSetting.setTunnelProfile(addedDcTunnelProfile);
      regularSetting.setVenue(venue);
      regularSetting.setSdLanProfile(sdLanProfile);
      repositoryUtil.createOrUpdate(regularSetting, tenant.getId(), randomTxId());

      SdLanProfileNetworkMapping networkMapping = new SdLanProfileNetworkMapping(randomId());
      networkMapping.setNetwork(network);
      networkMapping.setSdLanProfileRegularSetting(regularSetting);
      repositoryUtil.createOrUpdate(networkMapping, tenant.getId(), randomTxId());

      assertThatExceptionOfType(InvalidPropertyValueException.class)
          .isThrownBy(() -> extendedTunnelServiceProfileCtrl
              .deleteTunnelServiceProfile(addedDcTunnelProfile.getId()))
          .withMessage(Errors.WIFI_10450.message())
          .extracting(InvalidPropertyValueException::getErrorCode)
          .isEqualTo(Errors.WIFI_10450);
    }

    @Test
    void failedDeleteForwardingProfileInNetworkMapping(final Tenant tenant, final Venue venue, final Network network) {
      SdLanProfile sdLanProfile = new SdLanProfile(randomId());
      repositoryUtil.createOrUpdate(sdLanProfile, tenant.getId(), randomTxId());

      final TunnelProfile addedDcTunnelProfile =
          repositoryUtil.createOrUpdate(dummyTunnelProfile(), tenant.getId(), randomTxId());

      SdLanProfileRegularSetting regularSetting = new SdLanProfileRegularSetting(randomId());
      regularSetting.setTunnelProfile(addedDcTunnelProfile);
      regularSetting.setVenue(venue);
      regularSetting.setSdLanProfile(sdLanProfile);
      repositoryUtil.createOrUpdate(regularSetting, tenant.getId(), randomTxId());

      final TunnelProfile addedFwdTunnelProfile =
          repositoryUtil.createOrUpdate(dummyTunnelProfile(), tenant.getId(), randomTxId());
      SdLanProfileNetworkMapping networkMapping = new SdLanProfileNetworkMapping(randomId());
      networkMapping.setNetwork(network);
      networkMapping.setForwardingTunnelProfile(addedFwdTunnelProfile);
      networkMapping.setSdLanProfileRegularSetting(regularSetting);
      repositoryUtil.createOrUpdate(networkMapping, tenant.getId(), randomTxId());

      assertThatExceptionOfType(InvalidPropertyValueException.class)
              .isThrownBy(() -> extendedTunnelServiceProfileCtrl
                      .deleteTunnelServiceProfile(addedFwdTunnelProfile.getId()))
              .withMessage(Errors.WIFI_10450.message())
              .extracting(InvalidPropertyValueException::getErrorCode)
              .isEqualTo(Errors.WIFI_10450);
    }

    @Test
    void failedDeletePinDefaultTunnelProfile(final Tenant tenant) {
      final TunnelProfile addedTunnelProfile =
          extendedTunnelServiceProfileCtrl.addAndGetDefaultVxLANTunnelProfile(tenant.getId(), TunnelProfileAppliedFeature.PIN);

      resetTxId();
      assertThatExceptionOfType(InvalidPropertyValueException.class)
          .isThrownBy(() -> extendedTunnelServiceProfileCtrl
              .deleteTunnelServiceProfile(addedTunnelProfile.getId()))
          .withMessage(Errors.WIFI_10448.message())
          .extracting(InvalidPropertyValueException::getErrorCode)
          .isEqualTo(Errors.WIFI_10448);
    }

    @Test
    void failedDeleteSdLanDefaultTunnelProfile(final Tenant tenant) {
      final TunnelProfile addedTunnelProfile =
          extendedTunnelServiceProfileCtrl.addAndGetDefaultVxLANTunnelProfile(tenant.getId(), TunnelProfileAppliedFeature.SD_LAN);

      resetTxId();
      assertThatExceptionOfType(InvalidPropertyValueException.class)
          .isThrownBy(() -> extendedTunnelServiceProfileCtrl
              .deleteTunnelServiceProfile(addedTunnelProfile.getId()))
          .withMessage(Errors.WIFI_10448.message())
          .extracting(InvalidPropertyValueException::getErrorCode)
          .isEqualTo(Errors.WIFI_10448);
    }
  }

  private void resetTxId() {
    TxCtxHolder.set(new TxCtx(TxCtxHolder.tenantId(), randomTxId(), randomName(), randomName()));
  }

  private TunnelProfile dummyTunnelProfile() {
    return generator.setId(nullValue(String.class)).generate();
  }

  static Consumer<TunnelProfile> assertTunnelProfileServiceProfileSoftly(final TunnelProfile expected) {
    return (final TunnelProfile actual) -> assertSoftly(softly -> {
      softly.assertThat(actual.getName()).isEqualTo(expected.getName());
      softly.assertThat(actual.getMtuSize()).isEqualTo(expected.getMtuSize());
      softly.assertThat(actual.getMtuType()).isEqualTo(expected.getMtuType());
      softly.assertThat(actual.getForceFragmentation()).isEqualTo(expected.getForceFragmentation());
      softly.assertThat(actual.getAgeTimeMinutes()).isEqualTo(expected.getAgeTimeMinutes());
      softly.assertThat(actual.getType()).isEqualTo(expected.getType());
      softly.assertThat(actual.getIsDefault()).isEqualTo(expected.getIsDefault());
      softly.assertThat(actual.getKeepAliveRetry()).isEqualTo(expected.getKeepAliveRetry());
      softly.assertThat(actual.getKeepAliveInterval()).isEqualTo(expected.getKeepAliveInterval());
      softly.assertThat(actual.getMtuRequestRetry()).isEqualTo(expected.getMtuRequestRetry());
      softly.assertThat(actual.getMtuRequestTimeout()).isEqualTo(expected.getMtuRequestTimeout());
    });
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    ExtendedTunnelServiceProfileCtrl extendedTunnelServiceProfileCtrl(
        TunnelProfileRepository tunnelProfileRepository,
        TunnelServiceProfileMerger tunnelServiceProfileMerger,
        TunnelServiceProfilePartialMerger tunnelServiceProfilePartialMerger,
        DefaultVxLanTunnelProfile defaultVxLanTunnelProfile,
        DefaultSdLanTunnelProfile defaultSdLanTunnelProfile,
        PinProfileNetworkMappingRepository pinProfileNetworkMappingRepository,
        SdLanProfileNetworkMappingRepository sdLanProfileNetworkMappingRepository,
        FeatureFlagService featureFlagService,
        @Value("64") long tenantMaxCount
    ) {
      return new ExtendedTunnelServiceProfileCtrlImpl(
          tunnelProfileRepository,
          tunnelServiceProfileMerger,
          tunnelServiceProfilePartialMerger,
          defaultVxLanTunnelProfile,
          defaultSdLanTunnelProfile,
          pinProfileNetworkMappingRepository,
          sdLanProfileNetworkMappingRepository,
          featureFlagService,
          tenantMaxCount
      );
    }

    @Bean
    TunnelServiceProfileMerger tunnelServiceProfileMerger() {
      return new TunnelServiceProfileMergerImpl();
    }

    @Bean
    TunnelServiceProfilePartialMerger tunnelServiceProfilePartialMerger() {
      return new TunnelServiceProfilePartialMergerImpl();
    }
  }
}
