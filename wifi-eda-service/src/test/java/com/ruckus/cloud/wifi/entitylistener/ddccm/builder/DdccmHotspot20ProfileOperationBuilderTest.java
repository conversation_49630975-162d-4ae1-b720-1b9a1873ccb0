package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkHotspot20Settings;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Hotspot20PortStatusEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@WifiUnitTest
class DdccmHotspot20ProfileOperationBuilderTest {

  @SpyBean
  private DdccmHotspot20ProfileOperationBuilder unit;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void testCreateHotspot20ProfileOperation_withoutConnectionCapabilities() {
    var expected = createNetworkHotspot20Settings();

    var operation = unit.createHotspot20ProfileOperation(expected, EntityAction.ADD);
    assertThat(operation).isNotNull()
        .extracting(Operation::getHotspot20Profile).isNotNull()
        .matches(hs -> Objects.equals(expected.getId(), hs.getId()))
        .matches(hs -> Objects.equals(expected.getId(), hs.getName()))
        .matches(hs -> Objects.equals(expected.getOperator().getId(), hs.getOperatorId()))
        .matches(hs -> Objects.equals(expected.getIdentityProviders().get(0).getId(), hs.getDefaultIdentityProviderId()))
        .matches(hs -> Objects.equals(expected.getIdentityProviders().size(), hs.getIdentityProviderIdCount()))
        .matches(hs -> Objects.equals(expected.getIdentityProviders().get(1).getId(), hs.getIdentityProviderId(1)))
        .matches(hs -> Objects.equals(expected.getAllowInternetAccess(), hs.getInternetOption()))
        .matches(hs -> Objects.equals(unit.toAccessNetworkTypeEnum(expected.getAccessNetworkType()), hs.getAccessNetworkType()))
        .matches(hs -> Objects.equals(unit.toIpv4AddressTypeEnum(expected.getIpv4AddressType()), hs.getIpv4AddressType()))
        .matches(hs -> Objects.equals(0, hs.getConnCapCount()))
        .matches(hs -> Objects.equals(0, hs.getCustomConnCapCount()));
  }

  @Test
  void testCreateHotspot20ProfileOperation_withConnectionCapabilities() {
    var expected = createHotspot20SettingsWithConnectionCapabilities();

    var operation = unit.createHotspot20ProfileOperation(expected, EntityAction.ADD);
    assertThat(operation).isNotNull()
        .extracting(Operation::getHotspot20Profile).isNotNull()
        .matches(hs -> Objects.equals(2, hs.getConnCapCount()))
        .matches(hs -> Objects.equals(1, hs.getCustomConnCapCount()))
        .satisfies(hs -> {
          final var cc1 = expected.getConnectionCapabilities().get(0);
          final var cc2 = expected.getConnectionCapabilities().get(1);
          final var cc3 = expected.getConnectionCapabilities().get(2); // Custom
          assertThat(hs.getConnCap(0))
              .matches(cc -> Objects.equals(cc1.getProtocol(), cc.getName()))
              .matches(cc -> Objects.equals(cc1.getPort(), cc.getPort()))
              .matches(cc -> Objects.equals((int) cc1.getProtocolNumber(), cc.getProtocol()))
              .matches(cc -> Objects.equals(unit.toPortStatusEnum(cc1.getStatus()), cc.getStatus()));
          assertThat(hs.getConnCap(1))
              .matches(cc -> Objects.equals(cc2.getProtocol(), cc.getName()))
              .matches(cc -> Objects.equals(cc2.getPort(), cc.getPort()))
              .matches(cc -> Objects.equals((int) cc2.getProtocolNumber(), cc.getProtocol()))
              .matches(cc -> Objects.equals(unit.toPortStatusEnum(cc2.getStatus()), cc.getStatus()));
          assertThat(hs.getCustomConnCap(0)) // Custom
              .matches(cc -> Objects.equals(cc3.getProtocol(), cc.getName()))
              .matches(cc -> Objects.equals(cc3.getPort(), cc.getPort()))
              .matches(cc -> Objects.equals((int) cc3.getProtocolNumber(), cc.getProtocol()))
              .matches(cc -> Objects.equals(unit.toPortStatusEnum(cc3.getStatus()), cc.getStatus()));
        });
  }

  private static NetworkHotspot20Settings createNetworkHotspot20Settings() {
    final var hotspot20Settings = Generators.networkHotspot20Settings().generate();
    hotspot20Settings.setOperator(new Hotspot20Operator(UUID.randomUUID().toString()));
    hotspot20Settings.setIdentityProviders(List.of(
        new Hotspot20IdentityProvider(UUID.randomUUID().toString()),
        new Hotspot20IdentityProvider(UUID.randomUUID().toString())
    ));
    return hotspot20Settings;
  }

  private static NetworkHotspot20Settings createHotspot20SettingsWithConnectionCapabilities() {
    final var hotspot20Setting = createNetworkHotspot20Settings();
    final var connectionCapability1 = Generators.hotspot20ConnectionCapability((short) 6, 22).generate();
    final var connectionCapability2 = Generators.hotspot20ConnectionCapability((short) 6, 443).generate();
    connectionCapability2.setStatus(Hotspot20PortStatusEnum.CLOSED);
    final var connectionCapability3 = Generators.hotspot20ConnectionCapability((short) 6, 8443).generate();
    hotspot20Setting.setConnectionCapabilities(
        List.of(connectionCapability1, connectionCapability2, connectionCapability3)
    );
    return hotspot20Setting;
  }

}
