package com.ruckus.cloud.wifi.integration.durga;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;

import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.viewmodel.DurgaExternalAuthRadius;
import com.ruckus.cloud.wifi.eda.viewmodel.DurgaExternalRadiusTupple;
import com.ruckus.cloud.wifi.eda.viewmodel.DurgaPredefinedAaaModel;
import com.ruckus.cloud.wifi.eda.viewmodel.DurgaPredefinedAaaRegion;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumePredefinedAaaRelatedRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  private MessageCaptors messageCaptors;

  @Test
  void testInsertPredefinedAaaProvider(Tenant tenant) {
    var request = new DurgaPredefinedAaaModel();
    request.setName("ToInsert");
    request.setRegions(new ArrayList<>());

    final var userName = randomName();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.INSERT_PREDEFINED_AAA_PROVIDER,
        userName,
        new RequestParams(),
        request);

    messageCaptors.assertThat(
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(tenant.getId());
  }

  @Test
  void testInsertPredefinedAaaService(Tenant tenant) {
    var request = new DurgaPredefinedAaaModel();
    request.setName("DPSK-test");
    request.setRegions(new ArrayList<>());
    var region = new DurgaPredefinedAaaRegion();
    request.getRegions().add(region);
    region.setName("DPSK");
    region.setAaaName("dpsk-AAA-test");
    var authRadius = new DurgaExternalAuthRadius();
    region.setAuthRadius(authRadius);
    var tupple = new DurgaExternalRadiusTupple();
    authRadius.setPrimary(tupple);
    tupple.setIp("*******");
    tupple.setPort("1234");
    tupple.setSharedSecret("SharedSecret");
    authRadius.setCnSanIdentity("cnSanIdentity");

    final var userName = randomName();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.INSERT_PREDEFINED_AAA_SERVICE,
        userName,
        new RequestParams(),
        request);

    messageCaptors.assertThat(
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(tenant.getId());
  }

  @Test
  void testUpdatePredefinedAaaProvider(Tenant tenant) {
    var request = new DurgaPredefinedAaaModel();
    request.setName("TestA");
    request.setRegions(new ArrayList<>());

    final var userName = randomName();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.UPDATE_PREDEFINED_AAA_PROVIDER,
        userName,
        new RequestParams().addPathVariable("name", "TestA"),
        request);

    messageCaptors.assertThat(
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(tenant.getId());
  }

  @Test
  void testUpdatePredefinedAaaService(Tenant tenant) {
    var request = new DurgaPredefinedAaaModel();
    request.setName("DPSK");
    request.setRegions(new ArrayList<>());
    var region = new DurgaPredefinedAaaRegion();
    request.getRegions().add(region);
    region.setName("DPSK");
    region.setAaaName("dpsk-AAA");
    var authRadius = new DurgaExternalAuthRadius();
    region.setAuthRadius(authRadius);
    var tupple = new DurgaExternalRadiusTupple();
    authRadius.setPrimary(tupple);
    tupple.setIp("*******");
    tupple.setPort("1234");
    tupple.setSharedSecret("SharedSecret");
    authRadius.setCnSanIdentity("cnSanIdentity");

    final var userName = randomName();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.UPDATE_PREDEFINED_AAA_SERVICE,
        userName,
        new RequestParams().addPathVariable("name", "DPSK"),
        request);

    messageCaptors.assertThat(
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(tenant.getId());
  }

  @Test
  void testDeletePredefinedAaaProvider(Tenant tenant) {
    final var userName = randomName();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.DELETE_PREDEFINED_AAA_PROVIDER,
        userName,
        new RequestParams().addPathVariable("name", "TestA"),
        StringUtils.EMPTY);

    messageCaptors.assertThat(
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(tenant.getId());
  }

  @Test
  void testDeletePredefinedAaaService(Tenant tenant) {
    final var userName = randomName();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.DELETE_PREDEFINED_AAA_SERVICE,
        userName,
        new RequestParams().addPathVariable("name", "DPSK"),
        StringUtils.EMPTY);

    messageCaptors.assertThat(
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(tenant.getId());
  }
}
