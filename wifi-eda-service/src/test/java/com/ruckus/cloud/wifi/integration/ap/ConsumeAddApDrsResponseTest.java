package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;

import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.Device;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.DeviceType;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.Error;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.Response;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.ResponseContent;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.ResponseType;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.Status;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeAddApDrsResponseTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void consumeAddApDrsFailureMessage(Ap ap) throws Exception {
    // Given
    final var tenantId = ap.getTenant().getId();
    final var requestId = randomTxId();

    // When
    messageUtil.sendDrsResponse(tenantId, requestId, mockFailureDrsResponse(ap));

    // Then
    messageCaptors.getDrsMessageCaptor().assertNotSentByTenant(tenantId);
  }

  private Response mockFailureDrsResponse(Ap ap) {
    return Response.newBuilder()
        .setType(ResponseType.CREATE_DEVICE)
        .setSuccess(false)
        .setMessage("")
        .addAllResponseContents(generateResponseContents(ap))
        .build();
  }

  private List<ResponseContent> generateResponseContents(Ap ap) {
    return new ArrayList<>(generateFailureContent(ap));
  }

  private List<ResponseContent> generateFailureContent(Ap ap) {
    List<ResponseContent> responseContents = new ArrayList<>();
    responseContents.add(ResponseContent.newBuilder()
        .setStatus(Status.FAILED)
        .setDevice(Device.newBuilder()
            .setSerialNumber(ap.getId())
            .setType(DeviceType.WIFI))
        .setMessage("Device serial number already registered.")
        .setError(Error.DRS_10005).build());
    return responseContents;
  }
}
