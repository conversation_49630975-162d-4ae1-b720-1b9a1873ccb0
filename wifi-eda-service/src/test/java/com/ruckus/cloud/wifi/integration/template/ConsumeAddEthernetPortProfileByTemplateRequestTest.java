package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ETHERNET_PORT_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ETHERNET_PORT_PROFILE_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_PORT_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.EthernetPortProfileRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddEthernetPortProfileByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private EthernetPortProfileRepository ethernetPortProfileRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  @FeatureFlag(enable = WIFI_ETHERNET_PORT_PROFILE_TEMPLATE)
  public void add_ethernetPortProfile_by_template(Tenant mspTenant, @Template EthernetPortProfile profile) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenantId, randomTxId());

    // check template data
    EthernetPortProfile profileTemplateFromDB =
        repositoryUtil.find(EthernetPortProfile.class, profile.getId(), mspTenantId, true);
    assertEthernetPortProfile(profile, profileTemplateFromDB);

    // trigger add ethernetPortProfile by template
    String instanceId = randomId();
    addEthernetPortProfileByTemplate(instanceId, profile.getId(), ecTenantId, mspTenantId,
        userName);

    assertActivityPlan(ADD_ETHERNET_PORT_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_ETHERNET_PORT_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_ETHERNET_PORT_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  private void assertEthernetPortProfile(EthernetPortProfile expected, EthernetPortProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getIsTemplate(), actual.getIsTemplate());
    assertEquals(expected.getTenant().getId(), actual.getTenant().getId());
  }

  private void addEthernetPortProfileByTemplate(String instanceId, String templateId, String ecTenantId,
      String mspTenantId, String userName) {
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ADD_ETHERNET_PORT_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.ETHERNET_PORT_PROFILE,
        instanceId);
    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, templateId)
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES,
            executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, randomTxId(),
        CfgAction.ADD_ETHERNET_PORT_PROFILE_BY_TEMPLATE,
        ADD_ETHERNET_PORT_PROFILE_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());
  }
}
