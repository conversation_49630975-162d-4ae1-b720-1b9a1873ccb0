package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;

import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.requirement.ApModelFamily;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModel;
import java.util.Set;
import jakarta.annotation.PostConstruct;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class OweTransition6GFeatureTest {

  @SpyBean
  private OweTransition6GFeature unit;

  @TestConfiguration
  static class TestApModelFamilyConfig {
    @PostConstruct
    public void init() {
      ApModelFamily.WIFI_6.updateModels(Set.of("R550", "R750", "T350C"));
      ApModelFamily.WIFI_6E.updateModels(Set.of("R560", "R760"));
      ApModelFamily.WIFI_7.updateModels(Set.of("R560", "R670"));
    }
  }

  @Nested
  class TestIsDeviceSupport {
    @ParameterizedTest
    @CsvSource({
        "7.1.0.510.930, R550, false",
        "7.1.0.510.968, R750, false",
        "7.1.0.510.968, R560, true",
        "7.1.0.510.968, R670, true",
        "7.1.0.510.999, R370, false",
        "7.1.1.520.300, R550, false",
        "7.1.1.520.310, R750, false",
        "7.1.1.520.310, R670, true",
        "7.1.1.520.310, R760, true",
        "7.1.1.520.310, R370, false",
        "7.1.2.500.100, R560, true",
        "7.1.2.500.100, R670, true",
    })
    void testIsDeviceSupport(String apVersion, String apModel, boolean expectedResult) {
      ApFirmwareModel apFwModel = ApFirmwareModel.builder()
          .firmware(apVersion).model(apModel).build();
      BDDAssertions.then(unit.isDeviceSupport(apFwModel)).isEqualTo(expectedResult);
    }
  }


  @Test
  void givenFFisDisabled() {
    Network network = network(OpenNetwork.class).generate();
    network.setType(NetworkTypeEnum.OPEN);
    network.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
    BDDAssertions.then(unit.test(network)).isFalse();
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_OWE_TRANSITION_FOR_6G)
  class GivenFFisEnabled {

    @Test
    void givenOpenNetwork() {
      Network network = network(OpenNetwork.class).generate();
      network.setType(NetworkTypeEnum.OPEN);
      network.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      BDDAssertions.then(unit.test(network)).isTrue();
    }

    @Test
    void givenGuestNetwork() {
      Network network = network(GuestNetwork.class).generate();
      network.setType(NetworkTypeEnum.GUEST);
      network.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      BDDAssertions.then(unit.test(network)).isTrue();
    }

    @Test
    void givenOpenNetworkOtherSecurityType() {
      Network network = network(OpenNetwork.class).generate();
      network.setType(NetworkTypeEnum.OPEN);
      network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
      BDDAssertions.then(unit.test(network)).isFalse();
    }
  }

}
