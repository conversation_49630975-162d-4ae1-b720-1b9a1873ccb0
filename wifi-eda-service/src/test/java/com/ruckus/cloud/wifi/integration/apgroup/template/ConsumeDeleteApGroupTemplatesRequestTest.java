package com.ruckus.cloud.wifi.integration.apgroup.template;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.kafka.common.header.Header;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@Tag("ApGroupTemplateTest")
@WifiIntegrationTest
public class ConsumeDeleteApGroupTemplatesRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class whenConsumeDeleteApGroupTemplatesRequest {

    @Nested
    class givenVenueIsTemplate {

      @Test
      void thenDeleteApGroups(@Template Venue venue, @Template ApGroup defaultApGroup) {
        final var requestId = randomTxId();
        final var userName = randomName();
        final var tenantId = defaultApGroup.getTenant().getId();

        final var apGroups = Stream.generate(ApGroup::new)
            .peek(a -> a.setId(randomId()))
            .peek(a -> a.setVenue(venue))
            .peek(a -> a.setName(randomName()))
            .peek(a -> a.setIsTemplate(true))
            .limit(2)
            .collect(Collectors.toList());

        apGroups.forEach(apGroup -> {
          repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
        });

        final var payload = apGroups.stream()
            .map(ApGroup::getId).collect(Collectors.toList());

        messageUtil.sendWifiCfgRequest(
            tenantId, requestId, CfgAction.DELETE_AP_GROUP_TEMPLATES, userName,
            payload);

        validateResult(tenantId, requestId, payload);
      }
    }

    @Disabled("Enable after apply isTemplate filter on ApGroup")
    @Nested
    class givenVenueIsNotTemplate {

      @Test
      void thenApGroupsShouldNotBeDeleted(Venue venue, @DefaultApGroup ApGroup defaultApGroup) {
        final var requestId = randomTxId();
        final var userName = randomName();
        final var tenantId = defaultApGroup.getTenant().getId();

        final var apGroups = Stream.generate(ApGroup::new)
            .peek(a -> a.setId(randomId()))
            .peek(a -> a.setVenue(venue))
            .peek(a -> a.setName(randomName()))
            .peek(a -> a.setIsTemplate(true))
            .limit(2)
            .collect(Collectors.toList());

        apGroups.forEach(apGroup -> {
          repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
        });

        final var payload = apGroups.stream()
            .map(ApGroup::getId).collect(Collectors.toList());

        assertThrows(Exception.class, () ->
            messageUtil.sendWifiCfgRequest(
                tenantId, requestId, CfgAction.DELETE_AP_GROUP_TEMPLATES, userName,
                payload));
      }
    }


    void validateResult(String tenantId, String requestId, List<String> apGroupIds) {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      // validate AP Group cmnCfgCollectorMessage
      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(tenantId);
            assertThat(msg.getRequestId()).isEqualTo(requestId);
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSizeGreaterThanOrEqualTo(apGroupIds.size())
          .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
          .isNotEmpty()
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> apGroupIds.contains(op.getId()))
                .isNotEmpty().hasSize(apGroupIds.size())
                .extracting(Operations::getOpType).allMatch(OpType.DEL::equals);
          });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage).isNotNull();
      assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(requestId);

      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(tenantId);

      assertThat(activityCfgChangeRespMessage.getPayload())
          .matches(p -> p.getStatus().equals(Status.OK))
          .matches(p -> p.getStep().equals(ApiFlowNames.DELETE_AP_GROUP_TEMPLATES))
          .matches(p -> p.getEventDate() != null);
    }
  }
}
