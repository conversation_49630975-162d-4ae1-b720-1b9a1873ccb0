package com.ruckus.cloud.wifi.integration.template.enforce;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.BonjourFencingDeviceTypeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.BonjourFencingRangeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.CalledStaIdType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RadioType;
import com.ruckus.acx.ddccm.protobuf.wifi.UplinkSelectionEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueMesh;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.venue.proto.VenueEvent;
import com.ruckus.cloud.venue.proto.VenueTemplateEnforcementSettings;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.REQUEST_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_ADMIN_NAME;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_DELEGATION_SOURCE_TENANT_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_TENANT_ID;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Mesh;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkHotspot20Settings;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenuePortal;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadiusOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CalledStationIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.FencingRangeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LoadBalancingMethodEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MeshRadioTypeEnumV1_1;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdDelimiterEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PoeModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SteeringModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import com.ruckus.cloud.wifi.eda.viewmodel.MulticastDnsFencingRule;
import com.ruckus.cloud.wifi.eda.viewmodel.MulticastDnsFencingWirelessRule;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApBssColoringSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApClientAdmissionControlSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApDirectedMulticastSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApDosProtectionSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApLoadBalancingSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelLanPortSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelLedSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApMulticastDnsFencingSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApRadio6GHzSettingsV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApRadioDual5GHzSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApRadioSettingsV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueHeight;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueLanPort;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplate;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplateInstance;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import com.ruckus.cloud.wifi.test.kafka.captor.ActivityCfgChangeMessageCaptor;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest.WifiCfgRequestBuilder;
import java.util.List;
import lombok.SneakyThrows;
import org.apache.kafka.common.header.Header;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.UnexpectedRollbackException;

/*
Clone from ConsumeUpdateVenueCfgV1RequestTest
 */
@WifiIntegrationTest
public class ConsumeUpdateVenueCfgV1WithEnforceRequestTest {

  private static final Logger log = LoggerFactory.getLogger(
      ConsumeUpdateVenueCfgV1WithEnforceRequestTest.class);
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private ObjectMapper objectMapper;
  private final String ERROR_MSG = "This Venue is enforced by its Template, and this action [%s] is not allowed";

  private Tenant createEcTenant(Tenant tenant, String ecTenantId) {
    var ecTenant = new Tenant(ecTenantId);
    ecTenant.setRecoveryPsk("recover");
    ecTenant.setLatestReleaseVersion(tenant.getLatestReleaseVersion());
    return repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());
  }

  VenueEvent buildVenueTemplateEnforcementSettingsEvent(Venue venueTemplate, boolean isEnforced) {
    return VenueEvent.newBuilder()
        .setTenantId(venueTemplate.getTenant().getId())
        .addOperation(com.ruckus.cloud.venue.proto.Operation.newBuilder().setAction(
                com.ruckus.cloud.venue.proto.Action.MODIFY)
            .setVenueTemplateEnforcementSettings(
                VenueTemplateEnforcementSettings.newBuilder()
                    .setVenueId(venueTemplate.getId())
                    .setVenueName(venueTemplate.getName())
                    .setIsEnforced(isEnforced)
            )).build();
  }

  void validateActivityMessages(String tenantId, String requestId, String expectedStep, Status expectedStatus) throws InvalidProtocolBufferException {
    ActivityCfgChangeMessageCaptor captor = messageCaptors.getActivityCfgChangeMessageCaptor();
    final var activityCfgChangeRespMessage = captor
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(expectedStatus))
        .matches(p -> p.getStep().equals(expectedStep))
        .extracting(ConfigurationStatus::getEventDate)
        .isNotNull();
  }

  void validateUpdateVenueApLoadBalancingSettingsResult(String venueId,
      VenueApLoadBalancingSettings venueApLoadBalancingSettings, String tenantId,
      String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getLoadBalancing)
        .isNotNull()
        .matches(v -> v.getEnabled().equals(venueApLoadBalancingSettings.getEnabled()))
        .matches(
            v -> v.getLoadBalancingMethod()
                .equals(venueApLoadBalancingSettings.getLoadBalancingMethod()),
            "LoadBalancingMethod is equal")
        .matches(
            v -> v.getBandBalancingEnabled()
                .equals(venueApLoadBalancingSettings.getBandBalancingEnabled()),
            "BandBalancingEnabled is equal")
        .matches(v -> v.getSteeringMode().equals(venueApLoadBalancingSettings.getSteeringMode()),
            "SteeringMode is equal")
        .matches(v -> v.getBandBalancingClientPercent24G()
                .equals(venueApLoadBalancingSettings.getBandBalancingClientPercent24G()),
            "BandBalancingClientPercent24G is equal")
        .matches(v -> v.getStickyClientSteeringEnabled()
                .equals(venueApLoadBalancingSettings.getStickyClientSteeringEnabled()),
            "getStickyClientSteeringEnabled is equal")
        .matches(v -> v.getStickyClientSnrThreshold()
                .equals(venueApLoadBalancingSettings.getStickyClientSnrThreshold()),
            "getStickyClientSnrThreshold is equal")
        .matches(v -> v.getStickyClientNbrApPercentageThreshold()
                .equals(venueApLoadBalancingSettings.getStickyClientNbrApPercentageThreshold()),
            "getStickyClientNbrApPercentageThreshold is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasLoadBalancing)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getLoadBalancing)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.VenueLoadBalancing::getStickyClientSteering)
        .matches(v -> v.getStickyClientSteeringEnabled()
                .equals(BoolValue.of(venueApLoadBalancingSettings.getStickyClientSteeringEnabled())),
            "getStickyClientSteeringEnabled is equal")
        .matches(v -> v.getStickyClientSnrThreshold()
                .equals(Int32Value.of(venueApLoadBalancingSettings.getStickyClientSnrThreshold())),
            "getStickyClientSnrThreshold is equal")
        .matches(v -> v.getStickyClientNbrApPercentageThreshold()
                .equals(Int32Value.of(
                    venueApLoadBalancingSettings.getStickyClientNbrApPercentageThreshold())),
            "getStickyClientNbrApPercentageThreshold is equal");

  }

  void validateUpdateVenueApDirectedMulticastSettingsResult(String venueId,
      VenueApDirectedMulticastSettings venueApDirectedMulticastSettings, String tenantId,
      String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getDirectedMulticast)
        .isNotNull()
        .matches(v -> v.getWirelessEnabled()
                .equals(venueApDirectedMulticastSettings.getWirelessEnabled()),
            "Wireless enabled status is equal")
        .matches(
            v -> v.getWiredEnabled().equals(venueApDirectedMulticastSettings.getWiredEnabled()),
            "Wired enabled status is equal")
        .matches(
            v -> v.getNetworkEnabled().equals(venueApDirectedMulticastSettings.getNetworkEnabled()),
            "Network enabled status is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasDirectedMulticast);
  }

  void validateUpdateVenueApDosProtectionSettings(String venueId,
      VenueApDosProtectionSettings venueApDosProtectionSettings, String tenantId,
      String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getDenialOfServiceProtection)
        .isNotNull()
        .matches(v -> v.getEnabled().equals(venueApDosProtectionSettings.getEnabled()))
        .matches(
            v -> v.getBlockingPeriod().equals(venueApDosProtectionSettings.getBlockingPeriod()),
            "BlockingPeriod is equal")
        .matches(
            v -> v.getFailThreshold().equals(venueApDosProtectionSettings.getFailThreshold()),
            "FailThreshold is equal")
        .matches(v -> v.getCheckPeriod().equals(venueApDosProtectionSettings.getCheckPeriod()),
            "CheckPeriod is equal");
    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasDenialOfServiceProtection)
        .matches(v -> v.getDenialOfServiceProtection().getEnabled() == true);
  }

  void validateUpdateVenueApRadiusOptionSettingsEnableTest(String venueId, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getRadiusOptions)
        .extracting(VenueRadiusOptions::getOverrideEnabled)
        .isEqualTo(true);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(venueFromDb.getTenant().getId(),
        requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .hasSize(2)
        .filteredOn(Operation::hasWlanVenue)
        .hasSize(1)
        .singleElement()
        .extracting(Operation::getWlanVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue::hasRadiusOptions)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue::getRadiusOptions)
        .matches(r -> r.getNasIdType().equals(StringValue.of(NasIdTypeEnum.USER.toString())))
        .matches(r -> r.getCalledStaIdType().equals(CalledStaIdType.CalledStaIdType_AP_MAC))
        .matches(r -> r.getUserDefinedNasId().equals(StringValue.of("User_Defined")))
        .matches(r -> r.getAuthenticationMaxRetry().equals(Int32Value.of(3)))
        .matches(r -> r.getAccountingMaxRetry().equals(Int32Value.of(3)))
        .matches(r -> r.getSingleSessionIdAcctEnabled().equals(BoolValue.of(true)))
        .matches(r -> !r.hasNasIdDelimiter());
  }

  void validateUpdateVenueApRadiusOptionSettingsDisableTest(String venueId, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getRadiusOptions)
        .extracting(VenueRadiusOptions::getOverrideEnabled)
        .isEqualTo(false);

    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .hasSize(2)
        .filteredOn(Operation::hasWlanVenue)
        .hasSize(1)
        .singleElement()
        .extracting(Operation::getWlanVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue::hasRadiusOptions)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue::getRadiusOptions)
        .matches(r -> r.getNasIdType().equals(StringValue.of(NasIdTypeEnum.BSSID.toString())))
        .matches(r -> r.getCalledStaIdType().equals(CalledStaIdType.CalledStaIdType_WLAN_BSSID))
        .matches(r -> r.getUserDefinedNasId().equals(StringValue.of("")))
        .matches(r -> r.getAuthenticationMaxRetry().equals(Int32Value.of(2)))
        .matches(r -> r.getAccountingMaxRetry().equals(Int32Value.of(2)))
        .matches(r -> r.getSingleSessionIdAcctEnabled().equals(BoolValue.of(false)))
        .matches(
            r -> r.getNasIdDelimiter().equals(StringValue.of(NasIdDelimiterEnum.COLON.toString())));
  }

  void validateUpdateVenueApMeshSettings(String venueId, Mesh mesh, String requestId) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getMesh)
        .isNotNull()
        .matches(v -> v.getEnabled().equals(mesh.getEnabled()),
            "Enabled is equal")
        .matches(v -> v.getZeroTouchEnabled().equals(mesh.getZeroTouchEnabled()),
            "ZeroTouchEnabled is equal")
        .matches(v -> v.getPassphrase().equals(mesh.getPassphrase()),
            "Passphrase is equal")
        .matches(v -> v.getSsid().equals(mesh.getSsid()),
            "Ssid is equal")
        .matches(v -> v.getRadioType().equals(mesh.getRadioType()),
            "RadioType is equal");
    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasMesh)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getMesh)
        .matches(VenueMesh::getEnabled)
        .matches(VenueMesh::getZeroTouchEnabled)
        .matches(m -> m.getPassphrase().equals(mesh.getPassphrase()))
        .matches(m -> m.getSsid().equals(mesh.getSsid()))
        .matches(m -> m.getUplinkSelection().equals(UplinkSelectionEnum.RSSI))
        .matches(m -> m.getRadio().equals(RadioType.RADIO24));
  }

  void validateUpdateVenueApBssColoringSettings(String venueId, String requestId,
      VenueApBssColoringSettings venueApBssColoringSettings) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getBssColoring)
        .isNotNull()
        .matches(v -> v.getBssColoringEnabled()
                .equals(venueApBssColoringSettings.getBssColoringEnabled()),
            "DB bssColoringEnabled is equal");

    var record = messageCaptors.getDdccmMessageCaptor()
        .getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasBssColoring)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getBssColoring)
        .matches(venueBssColoring -> venueApBssColoringSettings.getBssColoringEnabled()
                .equals(venueBssColoring.getBssColoringEnabled().getValue()),
            "DDCCM bssColoringEnabled is equal");
  }

  void validateUpdateVenueApClientAdmissionControlSettings(
      String venueId, String requestId,
      VenueApClientAdmissionControlSettings venueApClientAdmissionControlSettings) {
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .extracting(Venue::getClientAdmissionControl)
        .isNotNull()
        .matches(
            v ->
                v.getEnable24G()
                    .equals(venueApClientAdmissionControlSettings.getEnable24G()),
            "DB ClientAdmissionControl Enable24G is equal")
        .matches(
            v ->
                v.getEnable50G()
                    .equals(venueApClientAdmissionControlSettings.getEnable50G()),
            "DB ClientAdmissionControl Enable50G is equal")
        .matches(
            v ->
                v.getMinClientCount24G()
                    .equals(
                        venueApClientAdmissionControlSettings.getMinClientCount24G()),
            "DB ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            v ->
                v.getMinClientCount50G()
                    .equals(
                        venueApClientAdmissionControlSettings.getMinClientCount50G()),
            "DB ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            v ->
                v.getMaxRadioLoad24G()
                    .equals(venueApClientAdmissionControlSettings.getMaxRadioLoad24G()),
            "DB ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            v ->
                v.getMaxRadioLoad50G()
                    .equals(venueApClientAdmissionControlSettings.getMaxRadioLoad50G()),
            "DB ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            v ->
                v.getMinClientThroughput24G()
                    .equals(
                        venueApClientAdmissionControlSettings
                            .getMinClientThroughput24G()),
            "DB ClientAdmissionControl MinClientThroughput24G is equal")
        .matches(
            v ->
                v.getMinClientThroughput50G()
                    .equals(
                        venueApClientAdmissionControlSettings
                            .getMinClientThroughput50G()),
            "DB ClientAdmissionControl MinClientThroughput50G is equal");

    var record =
        messageCaptors.getDdccmMessageCaptor().getValue(venueFromDb.getTenant().getId(), requestId);
    assertThat(record).isNotNull();

    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization::hasRadio24)
        .extracting(
            radioCustomization -> radioCustomization.getRadio24().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled()
                    == venueApClientAdmissionControlSettings.getEnable24G(),
            "DDCCM ClientAdmissionControl Enable24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == venueApClientAdmissionControlSettings.getMinClientCount24G(),
            "DDCCM ClientAdmissionControl MinClientCount24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == venueApClientAdmissionControlSettings.getMaxRadioLoad24G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad24G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == venueApClientAdmissionControlSettings
                    .getMinClientThroughput24G(),
            "DDCCM ClientAdmissionControl MinClientThroughput24G is equal");

    assertThat(ddccmRequest.getOperationsList())
        .isNotNull()
        .matches(
            request ->
                request.stream().anyMatch(Operation::hasVenue))
        .hasSize(1)
        .first()
        .extracting(Operation::getVenue)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasRadioCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
        .matches(com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization::hasRadio50)
        .extracting(
            radioCustomization -> radioCustomization.getRadio50().getClientAdmissionControl())
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getEnabled()
                    == venueApClientAdmissionControlSettings.getEnable50G(),
            "DDCCM ClientAdmissionControl Enable50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientCount()
                    == venueApClientAdmissionControlSettings.getMinClientCount50G(),
            "DDCCM ClientAdmissionControl MinClientCount50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMaxiumumRadioLoad()
                    == venueApClientAdmissionControlSettings.getMaxRadioLoad50G(),
            "DDCCM ClientAdmissionControl MaxRadioLoad50G is equal")
        .matches(
            clientAdmissionControl ->
                clientAdmissionControl.getMinimumClientThroughput()
                    == venueApClientAdmissionControlSettings
                    .getMinClientThroughput50G(),
            "DDCCM ClientAdmissionControl MinClientThroughput50G is equal");
  }

  void validateUpdateVenueApMulticastDnsFencingSettingsResult(String venueId,
      VenueApMulticastDnsFencingSettings venueApMulticastDnsFencingSettings, String tenantId, String requestId) {
    final var bonjourFencingRequest = venueApMulticastDnsFencingSettings.getRules().get(0);
    final var venueFromDb = repositoryUtil.find(Venue.class, venueId);
    assertThat(venueFromDb)
        .isNotNull()
        .matches(venue -> venue.getBonjourFencingEnabled()
            .equals(venueApMulticastDnsFencingSettings.getEnabled()))
        .extracting(venue -> venue.getBonjourFencing().get(0))
        .isNotNull()
        .matches(
            v -> v.getWirelessEnabled().equals(bonjourFencingRequest.getWirelessEnabled()),
            "Wireless rule enabled status is equal")
        .matches(v -> v.getWirelessRule().getFencingRange()
                .equals(bonjourFencingRequest.getWirelessRule().getFencingRange()),
            "Wireless rule fencing range is equal")
        .matches(
            v -> v.getWiredEnabled().equals(bonjourFencingRequest.getWiredEnabled()),
            "Wired rule enabled status is equal");

    var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(Operation::hasBonjourFencing))
        .hasSize(2)
        .extracting(Operation::getBonjourFencing)
        .matches(bf -> bf.get(1).getFencingRuleCount() == 1)
        .flatExtracting(com.ruckus.acx.ddccm.protobuf.wifi.BonjourFencing::getFencingRuleList)
        .matches(
            rule -> rule.get(0).getServiceType().name().equals((BridgeServiceEnum.AIRDISK).name()))
        .matches(rule -> rule.get(0).getDeviceType().name()
            .equals((BonjourFencingDeviceTypeEnum.WIRELESS).name()))
        .matches(rule -> rule.get(0).getFencingRange().name()
            .equals((BonjourFencingRangeEnum.SAME_AP).name()));
  }

  @Nested
  class whenConsumeUpdateVenueCfgRequestTest {
    private String mspTenantId;
    private String ecTenantId;
    private String venueTemplateId;
    private String venueInstanceId;

    @BeforeEach
    void givenEnforcedVenueTemplateAndInstanceInDb(Tenant tenant) {
      mspTenantId = tenant.getId();
      ecTenantId = randomId();

      Tenant ecTenant = createEcTenant(tenant, ecTenantId);
      repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());

      Venue venueTemplate = randomVenueTemplate(tenant);
      venueTemplate.setIsEnforced(false);
      repositoryUtil.createOrUpdate(venueTemplate, mspTenantId, randomTxId());
      venueTemplateId = venueTemplate.getId();

      Venue venueInstance = randomVenueTemplateInstance(venueTemplateId, ecTenant);
      venueInstance.setIsEnforced(false);
      repositoryUtil.createOrUpdate(venueInstance, ecTenantId, randomTxId());
      venueInstanceId = venueInstance.getId();
    }

    @SneakyThrows
    void toggleEnforce(boolean toggle) {
      Venue savedVenueTemplate = repositoryUtil.find(Venue.class, venueTemplateId, mspTenantId);
      Venue savedVenueInstance;

      var eventEnforce = buildVenueTemplateEnforcementSettingsEvent(savedVenueTemplate, toggle);
      var requestIdEnforce = randomTxId() + "$Enforce";
      messageUtil.sendVenueCfgChange(mspTenantId, requestIdEnforce, eventEnforce);

      validateActivityMessages(mspTenantId, requestIdEnforce,
          "UpdateVenueTemplateEnforcementSettingsInWifi", Status.OK);

      savedVenueTemplate = repositoryUtil.find(Venue.class, venueTemplateId, mspTenantId);
      assertThat(savedVenueTemplate).isNotNull()
          .extracting(Venue::getIsEnforced).isEqualTo(toggle);

      savedVenueInstance = repositoryUtil.find(Venue.class, venueInstanceId, ecTenantId);
      assertThat(savedVenueInstance).isNotNull()
          .extracting(Venue::getIsEnforced).isEqualTo(toggle);

      messageUtil.clearMessage();
    }

    WifiCfgRequest makeWifiCfgRequest(String requestId, CfgAction action, Object payload, boolean isDelegation) {
      var userName = randomName();
      WifiCfgRequestBuilder builder = WifiCfgRequest.builder()
          .tenantId(ecTenantId)
          .requestId(requestId)
          .apiAction(action)
          .requestHeaders(Maps.newHashMap())
          .addHeader(RKS_TENANT_ID.getName(), ecTenantId)
          .addHeader(RKS_DELEGATION_SOURCE_TENANT_ID.getName(), isDelegation ? mspTenantId : "")
          .addHeader(REQUEST_ID.getName(), requestId)
          .addHeader(RKS_ADMIN_NAME.getName(), "admin-name@" + userName)
          .addHeader(RKS_IDM_USER_ID.getName(), userName)
          .requestParams(new RequestParams().addPathVariable("venueId", venueInstanceId))
          .payload(payload);

      return builder.build();
    }

    @SneakyThrows
    private String doEcDirectRequestAndThenDelegation(CfgAction action, Object payload) {
      return doEcDirectRequestAndThenDelegation(action, action.key(), payload, ERROR_MSG, EntityAction.MODIFY);
    }

    @SneakyThrows
    private String doEcDirectRequestAndThenDelegation(CfgAction action, String flowStep, Object payload, String errorMsg, EntityAction entityAction) {
      var requestIdDirect = randomTxId();
      var wifiCfgRequestDirect = makeWifiCfgRequest(requestIdDirect, action, payload, false);
      // should fail to update Venue when it's enforced
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequestDirect)).isNotNull()
          .getRootCause().isInstanceOf(CommonException.class).hasMessage(String.format(errorMsg, entityAction));
      validateActivityMessages(ecTenantId, requestIdDirect, flowStep, Status.FAIL);

      messageUtil.clearMessage();

      var requestIdDelegate = randomTxId();
      var wifiCfgRequestDelegate = makeWifiCfgRequest(requestIdDelegate, action, payload, true);
      messageUtil.sendWifiCfgRequest(wifiCfgRequestDelegate);
      validateActivityMessages(ecTenantId, requestIdDelegate, flowStep, Status.OK);

      return requestIdDelegate;
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_AP_6G_CHANNEL_SEPARATION_TOGGLE)
    public void testUpdateVenueApRadio6GHzSettingsV1_1() {
      // Same from testUpdateVenueApRadio6GHzSettings() but add 6g outdoor channels
      toggleEnforce(true);
      boolean enableAfc = true;

      var venueApRadioSettingsV1_1 = new VenueApRadioSettingsV1_1();
      var venueApRadioDual5GHzSettings = new VenueApRadioDual5GHzSettings();
      venueApRadioDual5GHzSettings.setEnabled(false);
      venueApRadioSettingsV1_1.setRadioParamsDual5G(venueApRadioDual5GHzSettings);
      var venueApRadio6GHzSettingsV1_1 = new VenueApRadio6GHzSettingsV1_1();
      venueApRadio6GHzSettingsV1_1.setEnableAfc(enableAfc);
      venueApRadio6GHzSettingsV1_1.setAllowedChannels(List.of(Channel6GEnum._1, Channel6GEnum._5, Channel6GEnum._9));

      // Set 6g outdoor channels
      venueApRadio6GHzSettingsV1_1.setAllowedOutdoorChannels(List.of(Channel6GEnum._1, Channel6GEnum._5, Channel6GEnum._9));
      if (enableAfc) {
        var venueHeight = new VenueHeight();
        venueHeight.setMinFloor(3);
        venueHeight.setMaxFloor(5);
        venueApRadio6GHzSettingsV1_1.setVenueHeight(venueHeight);
      }
      venueApRadioSettingsV1_1.setRadioParams6G(venueApRadio6GHzSettingsV1_1);

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_RADIO_SETTINGS_V1_1, ApiFlowNames.UPDATE_VENUE_AP_RADIO_SETTINGS,
          venueApRadioSettingsV1_1, ERROR_MSG, EntityAction.MODIFY);

      var venueFromDb = repositoryUtil.find(Venue.class, venueInstanceId);

      assertNotNull(venueFromDb);

      var radioCustomizationFromDb = venueFromDb.getRadioCustomization();

      assertNotNull(radioCustomizationFromDb);
      assertFalse(radioCustomizationFromDb.getRadioParamsDual5G().getEnabled());

      var radioParams6GFromDb = radioCustomizationFromDb.getRadioParams6G();

      assertEquals(enableAfc, radioParams6GFromDb.getEnableAfc());

      var allowedChannels6G = venueApRadio6GHzSettingsV1_1.getAllowedChannels();
      // get 6g outdoor channels
      var allowedOutdoorChannels6G = venueApRadio6GHzSettingsV1_1.getAllowedOutdoorChannels();

      var allowedChannels6GFromDb = radioParams6GFromDb.getAllowedIndoorChannels();

      assertTrue(
          allowedChannels6G.size() == allowedChannels6GFromDb.size() && allowedChannels6G.containsAll(
              allowedChannels6GFromDb) && allowedChannels6GFromDb.containsAll(allowedChannels6G));

      final var ddccmMessage = messageCaptors.getDdccmMessageCaptor().getValue(ecTenantId, requestId);
      assertThat(ddccmMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();
      assertThat(ddccmMessage.getPayload().getOperationsList())
          .filteredOn(Operation::hasVenue)
          .filteredOn(e -> e.getAction() == Action.MODIFY)
          .extracting(Operation::getVenue)
          .last()
          .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasRadioCustomization)
          .matches(v -> enableAfc ? v.hasAfcVenueHeightAgl()
              && v.getAfcVenueHeightAgl().getMinHeight().getValue() == 3 * 3.5f - 3.5f
              && v.getAfcVenueHeightAgl().getMaxHeight().getValue() == 5 * 3.5f + 3.5f
              : v.hasAfcVenueHeightAgl() && v.getAfcVenueHeightAgl().hasMinHeight()
                  && v.getAfcVenueHeightAgl().hasMaxHeight()
                  && v.getAfcVenueHeightAgl().getMinHeight().getValue() == 0 &&
                  v.getAfcVenueHeightAgl().getMaxHeight().getValue() == 0)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getRadioCustomization)
          .matches(com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization::hasRadio60)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.VenueRadioCustomization::getRadio60)
          .matches(radio60 -> enableAfc == !radio60.getLowPowerIndoorModeEnable())
          .matches(radio60 -> allowedChannels6G.size() == radio60.getAllowedIndoorChannelListList().size())
          .matches(radio60 -> allowedOutdoorChannels6G.size() == radio60.getAllowedOutdoorChannelListList().size());  // verify 6g outdoor

      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(ecTenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(ecTenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, CfgAction.UPDATE_VENUE_AP_RADIO_SETTINGS_V1_1.key()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(wifiCfgChangeMessage.getPayload().getOperationList())
          .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasVenue)
          .filteredOn(e -> e.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY)
          .extracting(com.ruckus.cloud.wifi.proto.Operation::getVenue)
          .last()
          .matches(v -> enableAfc ? v.hasHeight() && v.getHeight().getMinFloor().getValue() == 3
              && v.getHeight().getMaxFloor().getValue() == 5
              : v.hasHeight() && !v.getHeight().hasMinFloor() && !v.getHeight().hasMaxFloor());
    }

    @Test
    void thenUpdateVenueApLoadBalancingSettings() {
      toggleEnforce(true);

      final var venueApLoadBalancingSettings = new VenueApLoadBalancingSettings();
      venueApLoadBalancingSettings.setEnabled(true);
      venueApLoadBalancingSettings.setLoadBalancingMethod(
          LoadBalancingMethodEnum.BASED_ON_CAPACITY);
      venueApLoadBalancingSettings.setSteeringMode(SteeringModeEnum.STRICT);
      venueApLoadBalancingSettings.setBandBalancingEnabled(false);
      venueApLoadBalancingSettings.setBandBalancingClientPercent24G((short) 25);
      venueApLoadBalancingSettings.setStickyClientSteeringEnabled(true);
      venueApLoadBalancingSettings.setStickyClientSnrThreshold((short) 16);
      venueApLoadBalancingSettings.setStickyClientNbrApPercentageThreshold((short) 22);

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_LOAD_BALANCING_SETTINGS, venueApLoadBalancingSettings);

      validateUpdateVenueApLoadBalancingSettingsResult(venueInstanceId, venueApLoadBalancingSettings,
          ecTenantId, requestId);
    }

    @Test
    void thenUpdateVenueDirectedMulticast() {
      toggleEnforce(true);

      final var venueApDirectedMulticastSettings = new VenueApDirectedMulticastSettings();
      venueApDirectedMulticastSettings.setWirelessEnabled(true);
      venueApDirectedMulticastSettings.setWiredEnabled(true);
      venueApDirectedMulticastSettings.setNetworkEnabled(false);

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_DIRECTED_MULTICAST_SETTINGS, venueApDirectedMulticastSettings);

      validateUpdateVenueApDirectedMulticastSettingsResult(venueInstanceId, venueApDirectedMulticastSettings,
          ecTenantId, requestId);
    }

    @Test
    void thenUpdateVenueApDosProtectionSettings() {
      toggleEnforce(true);

      VenueApDosProtectionSettings venueApDosProtectionSettings = new VenueApDosProtectionSettings();
      venueApDosProtectionSettings.setEnabled(true);

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_DOS_PROTECTION_SETTINGS, venueApDosProtectionSettings);

      validateUpdateVenueApDosProtectionSettings(venueInstanceId, venueApDosProtectionSettings,
          ecTenantId, requestId);
    }

    /*
    - VenueLanPort
    - VenueApModelSpecificAttributes
     */
    @SneakyThrows
    @Test
    void thenUpdateVenueApModelLanPortSettings() {
      toggleEnforce(true);

      var venueLanPort1 = new VenueLanPort();
      venueLanPort1.setPortId("1");
      venueLanPort1.setEnabled(true);
      venueLanPort1.setType(ApLanPortTypeEnum.TRUNK);
      venueLanPort1.setUntagId((short) 1);
      venueLanPort1.setVlanMembers("1-4094");

      var venueLanPort2 = new VenueLanPort();
      venueLanPort2.setPortId("2");
      venueLanPort2.setEnabled(true);
      venueLanPort2.setType(ApLanPortTypeEnum.TRUNK);
      venueLanPort2.setUntagId((short) 1);
      venueLanPort2.setVlanMembers("1-4094");

      var venueLanPort3 = new VenueLanPort();
      venueLanPort3.setPortId("3");
      venueLanPort3.setEnabled(true);
      venueLanPort3.setType(ApLanPortTypeEnum.TRUNK);
      venueLanPort3.setUntagId((short) 1);
      venueLanPort3.setVlanMembers("1-4094");

      var venueApModelLanPortSettings = new VenueApModelLanPortSettings();
      venueApModelLanPortSettings.setModel("T750SE");
      venueApModelLanPortSettings.setPoeOut(true);
      venueApModelLanPortSettings.setPoeMode(PoeModeEnum._802_3at);
      venueApModelLanPortSettings.setLanPorts(List.of(venueLanPort1, venueLanPort2, venueLanPort3));

      var requestIdDirect = randomTxId();
      var wifiCfgRequestDirect = makeWifiCfgRequest(requestIdDirect,
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_SETTINGS, List.of(venueApModelLanPortSettings),
          false);
      // should fail to update Venue when it's enforced
      assertThatThrownBy(() -> messageUtil.sendWifiCfgRequest(wifiCfgRequestDirect)).isNotNull()
          .getRootCause().isInstanceOf(UnexpectedRollbackException.class)
          .hasMessage("Transaction silently rolled back because it has been marked as rollback-only");
      validateActivityMessages(ecTenantId, requestIdDirect, ApiFlowNames.UPDATE_VENUE_AP_MODEL_LAN_PORT_SETTINGS, Status.FAIL);

      messageUtil.clearMessage();

      var requestIdDelegate = randomTxId();
      var wifiCfgRequestDelegate = makeWifiCfgRequest(requestIdDelegate,
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_SETTINGS, List.of(venueApModelLanPortSettings),
          true);
      messageUtil.sendWifiCfgRequest(wifiCfgRequestDelegate);
      validateActivityMessages(ecTenantId, requestIdDelegate, ApiFlowNames.UPDATE_VENUE_AP_MODEL_LAN_PORT_SETTINGS, Status.OK);

      // TODO expect to throw CommonException
//      doEcDirectRequestAndThenDelegation(
//          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_SETTINGS,
//          ApiFlowNames.UPDATE_VENUE_AP_MODEL_LAN_PORT_SETTINGS,
//          List.of(venueApModelLanPortSettings),
//          "This VenueApModelSpecificAttributes is enforced by its Template, and this action [%s] is not allowed",
//          EntityAction.MODIFY);

      var venueInstanceFromDb = repositoryUtil.find(Venue.class, venueInstanceId);

      assertThat(venueInstanceFromDb).isNotNull().extracting(Venue::getModelSpecificAttributes).asList()
          .extracting(VenueApModelSpecificAttributes.class::cast).hasSize(1).first()
          .matches(attr -> "T750SE".equals(attr.getModel()))
          .matches(attr -> PoeModeEnum._802_3at.equals(attr.getPoeMode()))
          .matches(attr -> Boolean.TRUE.equals(attr.getPoeOut()));
    }

    /*
    - VenueApModelSpecificAttributes
     */
    @SneakyThrows
    @Test
    void thenUpdateVenueApModelLedSettings() {
      toggleEnforce(true);

      // default
      var json = "[{\"ledEnabled\":true,\"model\":\"R610\"}]";
      var requests = objectMapper.readValue(json, new TypeReference<List<VenueApModelLedSettings>>() {
      });

      // Disable R610 LED
      requests.get(0).setLedEnabled(Boolean.FALSE);

      doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_MODEL_LED_SETTINGS,
          ApiFlowNames.UPDATE_VENUE_AP_MODEL_LED_SETTINGS,
          requests,
          "This VenueApModelSpecificAttributes is enforced by its Template, and this action [%s] is not allowed",
          EntityAction.ADD);

      var venueFromDb = repositoryUtil.find(Venue.class, venueInstanceId);

      assertThat(venueFromDb).isNotNull().extracting(Venue::getModelSpecificAttributes).asList()
          .extracting(VenueApModelSpecificAttributes.class::cast).hasSize(1).first()
          .matches(attr -> "R610".equals(attr.getModel()))
          .matches(attr -> Boolean.FALSE.equals(attr.getLedOn()));

      messageUtil.clearMessage();

      // Re-enable R610 LED
      requests.get(0).setLedEnabled(Boolean.TRUE);

      doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_MODEL_LED_SETTINGS,
          ApiFlowNames.UPDATE_VENUE_AP_MODEL_LED_SETTINGS,
          requests,
          "This VenueApModelSpecificAttributes is enforced by its Template, and this action [%s] is not allowed",
          EntityAction.MODIFY);

      venueFromDb = repositoryUtil.find(Venue.class, venueInstanceId);

      assertThat(venueFromDb).isNotNull().extracting(Venue::getModelSpecificAttributes).asList()
          .extracting(VenueApModelSpecificAttributes.class::cast).hasSize(1).first()
          .matches(attr -> "R610".equals(attr.getModel()))
          .matches(attr -> Boolean.TRUE.equals(attr.getLedOn()));
    }

    @Test
    void thenUpdateVenueRadiusOptionsTest() {
      var venueInstanceFromDb = repositoryUtil.find(Venue.class, venueInstanceId);

      Radius authRadius = repositoryUtil.createOrUpdate(new Radius(), ecTenantId);
      AuthRadiusVenue authRadiusVenue = new AuthRadiusVenue();
      authRadiusVenue.setVenue(venueInstanceFromDb);
      authRadiusVenue.setRadius(authRadius);
      authRadiusVenue = repositoryUtil.createOrUpdate(authRadiusVenue, ecTenantId, randomTxId());

      OpenNetwork openNetwork = network(OpenNetwork.class).generate();
      openNetwork.getWlan().setNetwork(openNetwork);
      openNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
      RadiusOptions initRadiusOptions = new RadiusOptions();
      initRadiusOptions.setNasIdDelimiter(NasIdDelimiterEnum.COLON);
      initRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.BSSID);
      openNetwork.getWlan().getAdvancedCustomization().setRadiusOptions(initRadiusOptions);
      // RADIUS options works on OpenNetwork only when:
      // 1. has authRadius
      // 2. macAddressAuthentication is enabled
      openNetwork.setAuthRadius(authRadius);
      openNetwork.getWlan().setMacAddressAuthentication(true);
      repositoryUtil.createOrUpdate(openNetwork, ecTenantId, randomTxId());

      NetworkVenue networkVenue = networkVenue()
          .setNetwork(always(openNetwork)).setVenue(always(venueInstanceFromDb)).generate();
      openNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, ecTenantId, randomTxId());

      // Assertion before config override
      RadiusOptions radiusOptions = openNetwork.getWlan().getAdvancedCustomization()
          .getRadiusOptions();
      assertEquals(NasIdTypeEnum.BSSID, radiusOptions.getNasIdType());
      assertEquals(CalledStationIdTypeEnum.BSSID, radiusOptions.getCalledStationIdType());
      assertEquals(NasIdDelimiterEnum.COLON, radiusOptions.getNasIdDelimiter());
      assertEquals(2, radiusOptions.getNasMaxRetry());
      assertNull(radiusOptions.getUserDefinedNasId());
      assertFalse(radiusOptions.getSingleSessionIdAccounting());

      // start test with enforce
      toggleEnforce(true);

      VenueRadiusOptions venueRadiusOptions = new VenueRadiusOptions();
      venueRadiusOptions.setOverrideEnabled(true);
      venueRadiusOptions.setNasIdType(NasIdTypeEnum.USER);
      venueRadiusOptions.setUserDefinedNasId("User_Defined");
      venueRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_MAC);
      venueRadiusOptions.setNasMaxRetry(3);
      venueRadiusOptions.setSingleSessionIdAccounting(true);
      venueRadiusOptions.setNasIdDelimiter(null);

      // Enable override
      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_RADIUS_OPTIONS, venueRadiusOptions);

      validateUpdateVenueApRadiusOptionSettingsEnableTest(venueInstanceId, requestId);

      // Disable override
      venueRadiusOptions.setOverrideEnabled(false);

      requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_RADIUS_OPTIONS, venueRadiusOptions);

      validateUpdateVenueApRadiusOptionSettingsDisableTest(venueInstanceId, requestId);
    }

    @Test
    void thenUpdateVenueApMeshSettings() {
      toggleEnforce(true);

      Mesh mesh = new Mesh();
      mesh.setEnabled(true);
      mesh.setZeroTouchEnabled(true);
      mesh.setRadioType(MeshRadioTypeEnumV1_1._2_4_GHz);
      mesh.setPassphrase("test_passphrase");
      mesh.setSsid("test_ssid");

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_MESH_SETTINGS, mesh);

      validateUpdateVenueApMeshSettings(venueInstanceId, mesh, requestId);
    }

    @Test
    void thenUpdateVenueApMeshSettingsV1_1() {
      toggleEnforce(true);

      Mesh mesh = new Mesh();
      mesh.setEnabled(true);
      mesh.setZeroTouchEnabled(true);
      mesh.setRadioType(MeshRadioTypeEnumV1_1._2_4_GHz);
      mesh.setPassphrase("test_passphrase");
      mesh.setSsid("test_ssid");

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_MESH_SETTINGS_V1_1, mesh);

      validateUpdateVenueApMeshSettings(venueInstanceId, mesh, requestId);
    }

    @Test
    void thenUpdateVenueApMulticastDnsFencingSettings() {
      toggleEnforce(true);

      var venueApMulticastDnsFencing = new VenueApMulticastDnsFencingSettings();
      var multicastDnsFencing = new MulticastDnsFencingRule();

      //wireless rule
      var wirelessRule = new MulticastDnsFencingWirelessRule();
      wirelessRule.setFencingRange(FencingRangeEnum.SAME_AP);
      multicastDnsFencing.setService(BridgeServiceEnum.AIRDISK);
      multicastDnsFencing.setWirelessEnabled(true);
      multicastDnsFencing.setWirelessRule(wirelessRule);

      //wired rules
      multicastDnsFencing.setWiredEnabled(false);

      venueApMulticastDnsFencing.setEnabled(true);
      venueApMulticastDnsFencing.setRules(List.of(multicastDnsFencing));

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_MULTICAST_DNS_FENCING_SETTINGS,
          ApiFlowNames.UPDATE_VENUE_BONJOUR_FENCING,
          venueApMulticastDnsFencing,
          ERROR_MSG,
          EntityAction.MODIFY);

      validateUpdateVenueApMulticastDnsFencingSettingsResult(venueInstanceId, venueApMulticastDnsFencing,
          ecTenantId, requestId);
    }

    @Test
    void thenUpdateVenueApBssColoringSettings() {
      toggleEnforce(true);

      final var venueApBssColoringSettings = new VenueApBssColoringSettings();
      venueApBssColoringSettings.setBssColoringEnabled(false);

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_BSS_COLORING_SETTINGS, venueApBssColoringSettings);

      validateUpdateVenueApBssColoringSettings(venueInstanceId, requestId, venueApBssColoringSettings);
    }

    @SneakyThrows
    @Test
    void thenUpdateVenueApClientAdmissionControlSettings() throws JsonProcessingException {
      toggleEnforce(true);

      // Disable load balancing and band balancing
      final var venueApLoadBalancingSettings = new VenueApLoadBalancingSettings();
      venueApLoadBalancingSettings.setEnabled(false);
      venueApLoadBalancingSettings.setLoadBalancingMethod(LoadBalancingMethodEnum.BASED_ON_CAPACITY);
      venueApLoadBalancingSettings.setSteeringMode(SteeringModeEnum.STRICT);
      venueApLoadBalancingSettings.setBandBalancingEnabled(false);
      venueApLoadBalancingSettings.setBandBalancingClientPercent24G((short) 25);
      venueApLoadBalancingSettings.setStickyClientSteeringEnabled(true);
      venueApLoadBalancingSettings.setStickyClientSnrThreshold((short) 16);
      venueApLoadBalancingSettings.setStickyClientNbrApPercentageThreshold((short) 22);

      doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_LOAD_BALANCING_SETTINGS, venueApLoadBalancingSettings);

      messageUtil.clearMessage();

      final var venueApClientAdmissionControlSettings = new VenueApClientAdmissionControlSettings();
      venueApClientAdmissionControlSettings.setEnable24G(true);
      venueApClientAdmissionControlSettings.setMinClientCount24G((short) 11);
      venueApClientAdmissionControlSettings.setMaxRadioLoad24G((short) 50);
      venueApClientAdmissionControlSettings.setMinClientThroughput24G((short) 10);
      venueApClientAdmissionControlSettings.setEnable50G(true);
      venueApClientAdmissionControlSettings.setMinClientCount50G((short) 21);
      venueApClientAdmissionControlSettings.setMaxRadioLoad50G((short) 50);
      venueApClientAdmissionControlSettings.setMinClientThroughput50G((short) 20);

      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_CLIENT_ADMISSION_CONTROL_SETTINGS, venueApClientAdmissionControlSettings);

      validateUpdateVenueApClientAdmissionControlSettings(
          venueInstanceId, requestId, venueApClientAdmissionControlSettings);
    }

    @Test
    void thenUpdateVenueRadiusOptionsTest_GuestNetwork() {
      var venueInstanceFromDb = repositoryUtil.find(Venue.class, venueInstanceId);

      Radius authRadius = new Radius("authRadius-new-id");
      repositoryUtil.createOrUpdate(authRadius, ecTenantId, randomTxId());
      AuthRadiusVenue authRadiusVenue = new AuthRadiusVenue("authRadiusVenue-new-id");
      authRadiusVenue.setVenue(venueInstanceFromDb);
      authRadiusVenue.setRadius(authRadius);
      repositoryUtil.createOrUpdate(authRadiusVenue, ecTenantId, randomTxId());

      GuestNetwork guestNetwork = (GuestNetwork) network(GuestNetwork.class).generate();
      guestNetwork.getWlan().setNetwork(guestNetwork);
      guestNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
      RadiusOptions initRadiusOptions = new RadiusOptions();
      initRadiusOptions.setNasIdDelimiter(NasIdDelimiterEnum.COLON);
      initRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.BSSID);
      guestNetwork.getWlan().getAdvancedCustomization().setRadiusOptions(initRadiusOptions);

      GuestPortal guestPortal = new GuestPortal("guestPortal-new-id");
      guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);

      // RADIUS options works on GuestNetwork only when:
      // 1. has authRadius
      // 2. has GuestPortal
      guestNetwork.setAuthRadius(authRadius);
      guestNetwork.getWlan().setMacAddressAuthentication(true);
      repositoryUtil.createOrUpdate(guestNetwork, ecTenantId, randomTxId());

      NetworkVenue networkVenue = networkVenue()
          .setNetwork(always(guestNetwork)).setVenue(always(venueInstanceFromDb)).generate();
      guestNetwork.setNetworkVenues(List.of(networkVenue));
      VenuePortal venuePortal = new VenuePortal("venuePortal-new-id");
      venuePortal.setNetworkVenue(networkVenue);
      venuePortal.setNetworkPortal(guestPortal);
      networkVenue.setVenuePortal(venuePortal);
      repositoryUtil.createOrUpdate(networkVenue, ecTenantId, randomTxId());

      // Assertion before config override
      RadiusOptions radiusOptions = guestNetwork.getWlan().getAdvancedCustomization()
          .getRadiusOptions();
      assertEquals(NasIdTypeEnum.BSSID, radiusOptions.getNasIdType());
      assertEquals(CalledStationIdTypeEnum.BSSID, radiusOptions.getCalledStationIdType());
      assertEquals(NasIdDelimiterEnum.COLON, radiusOptions.getNasIdDelimiter());
      assertEquals(2, radiusOptions.getNasMaxRetry());
      assertNull(radiusOptions.getUserDefinedNasId());
      assertFalse(radiusOptions.getSingleSessionIdAccounting());

      // start test with enforce
      toggleEnforce(true);

      VenueRadiusOptions venueRadiusOptions = new VenueRadiusOptions();
      venueRadiusOptions.setOverrideEnabled(true);
      venueRadiusOptions.setNasIdType(NasIdTypeEnum.USER);
      venueRadiusOptions.setUserDefinedNasId("User_Defined");
      venueRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_MAC);
      venueRadiusOptions.setNasMaxRetry(3);
      venueRadiusOptions.setSingleSessionIdAccounting(true);
      venueRadiusOptions.setNasIdDelimiter(null);

      // Enable override
      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_RADIUS_OPTIONS, venueRadiusOptions);

      validateUpdateVenueApRadiusOptionSettingsEnableTest(venueInstanceId, requestId);

      // Disable override
      venueRadiusOptions.setOverrideEnabled(false);
      requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_RADIUS_OPTIONS, venueRadiusOptions);

      validateUpdateVenueApRadiusOptionSettingsDisableTest(venueInstanceId, requestId);
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_NAS_ID_FOR_HOTSPOT20_NETWORK)
    void thenUpdateVenueRadiusOptionsTest_Hotspot20Network() {
      var venueInstanceFromDb = repositoryUtil.find(Venue.class, venueInstanceId);

      Radius authRadius = new Radius("authRadius-new-id");
      repositoryUtil.createOrUpdate(authRadius, ecTenantId, randomTxId());
      AuthRadiusVenue authRadiusVenue = new AuthRadiusVenue("authRadiusVenue-new-id");
      authRadiusVenue.setVenue(venueInstanceFromDb);
      authRadiusVenue.setRadius(authRadius);
      repositoryUtil.createOrUpdate(authRadiusVenue, ecTenantId, randomTxId());

      Hotspot20Network hotspot20Network = (Hotspot20Network) network(Hotspot20Network.class).generate();
      hotspot20Network.getWlan().setNetwork(hotspot20Network);
      hotspot20Network.getWlan().setWlanSecurity(WlanSecurityEnum.Open);
      RadiusOptions initRadiusOptions = new RadiusOptions();
      initRadiusOptions.setNasIdDelimiter(NasIdDelimiterEnum.COLON);
      initRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.BSSID);
      hotspot20Network.getWlan().getAdvancedCustomization().setRadiusOptions(initRadiusOptions);

      NetworkHotspot20Settings networkHotspot20Settings = new NetworkHotspot20Settings("networkHotspot20Settings-new-id");
      hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

      // RADIUS options works on GuestNetwork only when:
      // 1. has Hotspot20IdentityProvider.authRadius
      // 2. has NetworkHotspot20Settings
      hotspot20Network.setAuthRadius(authRadius);
      hotspot20Network.getWlan().setMacAddressAuthentication(true);
      repositoryUtil.createOrUpdate(hotspot20Network, ecTenantId, randomTxId());

      Hotspot20IdentityProvider hotspot20IdentityProvider = new Hotspot20IdentityProvider("hotspot20IdentityProvider-new-id");
      hotspot20IdentityProvider.setAuthRadius(authRadius);
      hotspot20IdentityProvider.setNetworkHotspot20SettingsList(List.of(networkHotspot20Settings));
      repositoryUtil.createOrUpdate(hotspot20IdentityProvider, ecTenantId, randomTxId());

      NetworkVenue networkVenue = networkVenue()
          .setNetwork(always(hotspot20Network)).setVenue(always(venueInstanceFromDb)).generate();
      hotspot20Network.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, ecTenantId, randomTxId());

      // Assertion before config override
      RadiusOptions radiusOptions = hotspot20Network.getWlan().getAdvancedCustomization()
          .getRadiusOptions();
      assertEquals(NasIdTypeEnum.BSSID, radiusOptions.getNasIdType());
      assertEquals(CalledStationIdTypeEnum.BSSID, radiusOptions.getCalledStationIdType());
      assertEquals(NasIdDelimiterEnum.COLON, radiusOptions.getNasIdDelimiter());
      assertEquals(2, radiusOptions.getNasMaxRetry());
      assertNull(radiusOptions.getUserDefinedNasId());
      assertFalse(radiusOptions.getSingleSessionIdAccounting());

      // start testing with enforce
      toggleEnforce(true);

      VenueRadiusOptions venueRadiusOptions = new VenueRadiusOptions();
      venueRadiusOptions.setOverrideEnabled(true);
      venueRadiusOptions.setNasIdType(NasIdTypeEnum.USER);
      venueRadiusOptions.setUserDefinedNasId("User_Defined");
      venueRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_MAC);
      venueRadiusOptions.setNasMaxRetry(3);
      venueRadiusOptions.setSingleSessionIdAccounting(true);
      venueRadiusOptions.setNasIdDelimiter(null);

      // Enable override
      var requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_RADIUS_OPTIONS, venueRadiusOptions);

      validateUpdateVenueApRadiusOptionSettingsEnableTest(venueInstanceId, requestId);

      // Disable override
      venueRadiusOptions.setOverrideEnabled(false);
      requestId = doEcDirectRequestAndThenDelegation(
          CfgAction.UPDATE_VENUE_AP_RADIUS_OPTIONS, venueRadiusOptions);

      validateUpdateVenueApRadiusOptionSettingsDisableTest(venueInstanceId, requestId);
    }
  }
}
