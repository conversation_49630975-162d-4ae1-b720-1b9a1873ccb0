package com.ruckus.cloud.wifi.integration.clientIsolationonlanport;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.newLanPortAdoption;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice.ImpactDevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortOverwriteSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.IsolatePacketsTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.ApLanPortSettingsV1Generator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.ApLanPortSettingsV1;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApLanPortTestFixture;
import com.ruckus.cloud.wifi.test.fixture.EthernetPortProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.LanPortAdoptionTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("EthernetPortProfileTest")
@FeatureFlag(
    enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
@WifiIntegrationTest
class ConsumeUpdateApLanPortOverwriteSettingsForClientIsolationRequestTest {

  private record ApLanPortData(
      ApModelSpecific apAttributes, ApLanPort port, EthernetPortProfile profile) {}

  private final Map<IsolatePacketsTypeEnum, Boolean> MULTICAST_ENABLED =
      Map.of(
          IsolatePacketsTypeEnum.MULTICAST, true,
          IsolatePacketsTypeEnum.UNICAST_MULTICAST, true,
          IsolatePacketsTypeEnum.UNICAST, false);

  private final Map<IsolatePacketsTypeEnum, Boolean> UNICAST_ENABLED =
      Map.of(
          IsolatePacketsTypeEnum.UNICAST, true,
          IsolatePacketsTypeEnum.UNICAST_MULTICAST, true,
          IsolatePacketsTypeEnum.MULTICAST, false);

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private RevisionService revisionService;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private LanPortAdoptionServiceImpl lanPortAdoptionService;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeEnableClientIsolationOnApLanPortRequestTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator =
        Generators.apLanPortSettingsV1()
            .setClientIsolationEnabled(always(true))
            .setClientIsolationSettings(Generators.clientIsolationSettings())
            .setOverwriteUntagId(always((short) 1))
            .setOverwriteVlanMembers(always("1-4094"))
            .setEnabled(always(true));

    private String ethernetPortProfileId;
    private String venueId;
    private String serialNumber;
    private String portId = "1";
    private String apLanPortId;
    private String allowlistId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue, final @ApModel("R320") Ap ap, final ApGroup apGroup) {
      initAp(ap, apGroup);
      var portData1 = createApLanPortData(venue, ap, 3, portId, null);
      venueId = venue.getId();
      serialNumber = ap.getId();
      apLanPortId = portData1.port.getId();
      ethernetPortProfileId =
          portData1.port.getLanPortAdoption().getEthernetPortProfileId().toString();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      verifyResult(
          CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS, allowlistId, apLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeEnableClientIsolationOnApLanPortAndExistsClientIsolationAllowlistRequestTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator =
        Generators.apLanPortSettingsV1()
            .setClientIsolationEnabled(always(true))
            .setClientIsolationSettings(Generators.clientIsolationSettings())
            .setOverwriteUntagId(always((short) 1))
            .setOverwriteVlanMembers(always("1-4094"))
            .setEnabled(always(true));

    private String ethernetPortProfileId;
    private String venueId;
    private String serialNumber;
    private String portId = "1";
    private String apLanPortId;
    private String allowlistId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue,
        final @ApModel("R320") Ap ap,
        final ApGroup apGroup,
        final ClientIsolationAllowlist allowlist) {
      initAp(ap, apGroup);
      var portData1 =
          createApLanPortData(
              venue,
              ap,
              3,
              portId,
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                  .clientIsolationLanPortActivation()
                  .setClientIsolationAllowlist(always(allowlist))
                  .generate());
      venueId = venue.getId();
      serialNumber = ap.getId();
      apLanPortId = portData1.port.getId();
      ethernetPortProfileId =
          portData1.port.getLanPortAdoption().getEthernetPortProfileId().toString();
      allowlistId = allowlist.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      verifyResult(
          CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS, allowlistId, apLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeDisableClientIsolationOnApLanPortRequestTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator =
        Generators.apLanPortSettingsV1()
            .setClientIsolationEnabled(always(false))
            .setOverwriteUntagId(always((short) 1))
            .setOverwriteVlanMembers(always("1-4094"))
            .setEnabled(always(true));

    private String ethernetPortProfileId;
    private String venueId;
    private String serialNumber;
    private String portId = "1";
    private String apLanPortId;
    private String allowlistId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue, final @ApModel("R320") Ap ap, final ApGroup apGroup) {
      initAp(ap, apGroup);
      var portData1 =
          createApLanPortData(
              venue,
              ap,
              3,
              portId,
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                  .clientIsolationLanPortActivation()
                  .generate());
      venueId = venue.getId();
      serialNumber = ap.getId();
      apLanPortId = portData1.port.getId();
      ethernetPortProfileId =
          portData1.port.getLanPortAdoption().getEthernetPortProfileId().toString();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      verifyResult(
          CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS, allowlistId, apLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeDisablePortOnApLanPortRequestTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator =
        Generators.apLanPortSettingsV1()
            .setClientIsolationEnabled(always(true))
            .setOverwriteUntagId(always((short) 1))
            .setOverwriteVlanMembers(always("1-4094"))
            .setEnabled(always(false));

    private String ethernetPortProfileId;
    private String venueId;
    private String serialNumber;
    private String portId = "1";
    private String apLanPortId;
    private String allowlistId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue, final @ApModel("R550") Ap ap, final ApGroup apGroup) {
      initAp(ap, apGroup);
      var portData1 =
          createApLanPortData(
              venue,
              ap,
              3,
              portId,
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                  .clientIsolationLanPortActivation()
                  .generate());
      var portData2 = createApLanPortData(venue, ap, 4, "2", null);
      venueId = venue.getId();
      serialNumber = ap.getId();
      apLanPortId = portData1.port.getId();
      ethernetPortProfileId =
          portData1.port.getLanPortAdoption().getEthernetPortProfileId().toString();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      var expectedSettings = new ApLanPortSettingsV1();
      expectedSettings.setClientIsolationEnabled(false);
      verifyResult(
          CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS,
          allowlistId,
          apLanPortId,
          expectedSettings);
    }
  }

  private void verifyResult(
      CfgAction apiAction,
      String allowListId,
      String apLanPortId,
      ApLanPortSettingsV1 expectedSettings) {
    verifyDb(apLanPortId, expectedSettings, allowListId);
    verifyDdccm(apLanPortId, expectedSettings, allowListId);
    verifyCmnConfig(expectedSettings);
    verifyActivity(apiAction);
  }

  private void verifyDb(
      String apLanPortId, ApLanPortSettingsV1 expectedSettings, String allowlistId) {
    assertThat(apLanPortId).isNotNull();
    final var port = repositoryUtil.find(ApLanPort.class, apLanPortId);
    if (expectedSettings.getClientIsolationEnabled()) {
      var clientIsolationActivation =
          assertThat(port)
              .extracting(ApLanPort::getLanPortAdoption)
              .extracting(LanPortAdoption::getClientIsolationActivation)
              .isNotNull();
      clientIsolationActivation
          .matches(
              activation ->
                  activation
                      .getAutoVrrp()
                      .equals(expectedSettings.getClientIsolationSettings().getAutoVrrp()))
          .matches(
              activation ->
                  activation.getPacketsType()
                      == expectedSettings.getClientIsolationSettings().getPacketsType());
      if (allowlistId != null) {
        clientIsolationActivation
            .extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
            .isNotNull()
            .matches(c -> c.getId().equals(allowlistId));
      }
    } else {
      assertThat(port)
          .extracting(ApLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getClientIsolationActivation)
          .isNull();
    }
  }

  private void verifyDdccm(
      String apLanPortId, ApLanPortSettingsV1 expectedSettings, String clientIsolationAllowlistId) {
    final var requestId = txCtxExtension.getRequestId();
    final var tenantId = txCtxExtension.getTenantId();

    final var ddccmMessages = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmMessages)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    final var apLanPort = repositoryUtil.find(ApLanPort.class, apLanPortId);
    var ethernetPortProfileId = apLanPort.getLanPortAdoption().getEthernetPortProfileId();
    var operations =
        assertThat(ddccmMessages.getPayload())
            .extracting(WifiConfigRequest::getOperationsList)
            .asList()
            .isNotEmpty()
            .extracting(Operation.class::cast);
    operations
        .filteredOn(Operation::hasAp)
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(Operation::getAp)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getLanPortList)
                    .asList()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.class::cast)
                    .anyMatch(port -> ethernetPortProfileId.equals(port.getApLanPortProfileId())));
    operations
        .filteredOn(Operation::hasApLanPortProfile)
        .filteredOn(operation -> operation.getAction() == Action.ADD)
        .isNotEmpty()
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .allSatisfy(
            op -> verifyAddOperation(op, apLanPort, expectedSettings, clientIsolationAllowlistId));
    operations
        .filteredOn(operation -> operation.getAction() == Action.DELETE)
        .isNotEmpty()
        .allSatisfy(this::verifyDeleteOperation);
  }

  private void verifyAddOperation(
      Operation operation,
      ApLanPort apLanPort,
      ApLanPortSettingsV1 expectedSettings,
      String allowlistId) {
    var profile = assertThat(operation).extracting(Operation::getApLanPortProfile);
    profile
        .matches(
            apLanPortProfile ->
                apLanPort.getLanPortAdoption().getId().equals(apLanPortProfile.getName()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getLanPortAdoption()
                    .getEthernetPortProfileId()
                    .equals(apLanPortProfile.getId()))
        .matches(
            apLanPortProfile ->
                (apLanPort.getApLanPortProfile().getType().name() + "PORT")
                    .equals(apLanPortProfile.getLanPortType().name()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagId()));

    if (expectedSettings.getClientIsolationEnabled()) {
      var clientIsolationSettings = expectedSettings.getClientIsolationSettings();
      profile
          .extracting(ApLanPortProfile::getClientIsolation)
          .isNotNull()
          .matches(ApLanPortProfile.CcmWiredClientIsolation::getClientIsolationEnabled)
          .matches(
              ccmWiredClientIsolation ->
                  clientIsolationSettings
                      .getAutoVrrp()
                      .equals(ccmWiredClientIsolation.getClientIsolationAutoVrrpEnabled()))
          .matches(
              ccmWiredClientIsolation ->
                  MULTICAST_ENABLED
                      .get(clientIsolationSettings.getPacketsType())
                      .equals(ccmWiredClientIsolation.getClientIsolationMulticastEnabled()))
          .matches(
              ccmWiredClientIsolation ->
                  UNICAST_ENABLED
                      .get(clientIsolationSettings.getPacketsType())
                      .equals(ccmWiredClientIsolation.getClientIsolationUnicastEnabled()));
      if (allowlistId != null) {
        profile
            .extracting(ApLanPortProfile::getClientIsolation)
            .matches(
                ccmWiredClientIsolation ->
                    allowlistId.equals(ccmWiredClientIsolation.getClientIsolationWhitelistId()));
      }
      profile
          .extracting(ApLanPortProfile::getUserSidePort)
          .isNotNull()
          .matches(ApLanPortProfile.CcmUserSidePort::getUserSidePortEnabled)
          .matches(ccmUserSidePort -> ccmUserSidePort.getUserSidePortMaxClient().getValue() == 32);
    } else {
      profile
          .extracting(ApLanPortProfile::getClientIsolation)
          .matches(
              ccmWiredClientIsolation ->
                  ApLanPortProfile.CcmWiredClientIsolation.getDefaultInstance()
                      .equals(ccmWiredClientIsolation));
      profile
          .extracting(ApLanPortProfile::getUserSidePort)
          .matches(
              ccmUserSidePort ->
                  ApLanPortProfile.CcmUserSidePort.getDefaultInstance().equals(ccmUserSidePort));
    }
  }

  private void verifyDeleteOperation(Operation operation) {
    assertThat(operation).extracting(Operation::getApLanPortProfile).isNotNull();
  }

  private void verifyActivity(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors
          .assertThat(
              kafkaTopicProvider.getActivityCfgChangeResp(),
              kafkaTopicProvider.getActivityImpacted())
          .doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(ConfigurationStatus::getEventDate)
                    .isNotNull());

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ImpactDevice::getDeviceIdsCount).isEqualTo(1);
  }

  private void verifyCmnConfig(ApLanPortSettingsV1 expectedSettings) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    messageCaptors.getCmnCfgCollectorMessageCaptor()
        .assertNotSentByTenant(tenantId);
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ETHERNET_PORT_PROFILE -> ApiFlowNames.ADD_ETHERNET_PORT_PROFILE;
      case UPDATE_ETHERNET_PORT_PROFILE -> ApiFlowNames.UPDATE_ETHERNET_PORT_PROFILE;
      case DELETE_ETHERNET_PORT_PROFILE -> ApiFlowNames.DELETE_ETHERNET_PORT_PROFILE;
      case ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE ->
          ApiFlowNames.ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE;
      case DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE ->
          ApiFlowNames.DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE;
      case ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT ->
          ApiFlowNames.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT;
      case DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT ->
          ApiFlowNames.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT;
      case ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT ->
          ApiFlowNames.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT;
      case DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT ->
          ApiFlowNames.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT;
      case UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS ->
          ApiFlowNames.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS;
      case UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS ->
          ApiFlowNames.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS;

      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void initAp(Ap ap, ApGroup apGroup) {
    var apModelSpecific = new ApModelSpecific();
    apModelSpecific.setLanPorts(new ArrayList<>());
    apModelSpecific =
        repositoryUtil.createOrUpdate(apModelSpecific, txCtxExtension.getTenantId(), randomTxId());

    ap.setModelSpecific(apModelSpecific);
    ap.setApGroup(apGroup);
    repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());
  }

  private EthernetPortProfile createEthernetPortProfile(Venue venue, int ethernetPortProfileId) {
    return repositoryUtil.createOrUpdate(
        EthernetPortProfileTestFixture.randomEthernetPortProfile(
            venue,
            e -> {
              e.setApLanPortId(ethernetPortProfileId);
              e.setName(randomName());
              e.setType(ApLanPortTypeEnum.TRUNK);
              e.setUntagId((short) 1);
              e.setVlanMembers("1-4094");
              e.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
            }),
        txCtxExtension.getTenantId(),
        randomTxId());
  }

  private ApLanPortData createApLanPortData(
      Venue venue,
      Ap ap,
      int ethernetPortProfileId,
      String portId,
      ClientIsolationLanPortActivation activation) {
    var apLanPortProfile = createEthernetPortProfile(venue, ethernetPortProfileId);
    var apLanPort =
        repositoryUtil.createOrUpdate(
            ApLanPortTestFixture.randomApLanPort(
                venue,
                ap.getModelSpecific(),
                port -> {
                  port.setApLanPortProfile(apLanPortProfile);
                  port.setPortId(portId);
                  port.setApLanPortOverwriteSettings(new LanPortOverwriteSettings());
                  port.getApLanPortOverwriteSettings().setUntagId(apLanPortProfile.getUntagId());
                  port.getApLanPortOverwriteSettings()
                      .setVlanMembers(apLanPortProfile.getVlanMembers());
                }),
            txCtxExtension.getTenantId(),
            randomTxId());
    var adoption =
        repositoryUtil.createOrUpdate(
            LanPortAdoptionTestFixture.randomLanPortAdoption(
                apLanPortProfile,
                a ->
                    a.setChecksum(
                        lanPortAdoptionService.calculateChecksum(
                            newLanPortAdoption(apLanPortProfile, activation)))),
            txCtxExtension.getTenantId(),
            randomTxId());
    if (activation != null) {
      activation.setLanPortAdoption(adoption);
      repositoryUtil.createOrUpdate(activation, txCtxExtension.getTenantId(), randomTxId());
    }
    apLanPort.setLanPortAdoption(adoption);
    apLanPort =
        repositoryUtil.createOrUpdate(apLanPort, txCtxExtension.getTenantId(), randomTxId());
    return new ApLanPortData(ap.getModelSpecific(), apLanPort, apLanPortProfile);
  }
}
