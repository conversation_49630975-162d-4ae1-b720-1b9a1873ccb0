package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO dhcp_config_service_profile (id, service_name, tenant, is_template)
        VALUES ('dhcpConfig', 'dhcpConfig', '4c8279f79307415fa9e4c88a1819f0fc', false);
    INSERT INTO dhcp_config_service_profile (id, service_name, tenant, is_template)
        VALUES ('dhcpConfigTemplate', 'dhcpConfigTemplate', '4c8279f79307415fa9e4c88a1819f0fc', true);
    INSERT INTO dhcp_config_service_profile (id, service_name, tenant, is_template, template_id)
        VALUES ('dhcpConfigInstance', 'dhcpConfigInstance', '4c8279f79307415fa9e4c88a1819f0fc', false, 'dhcpConfigTemplate');
    """)
public class DhcpConfigServiceProfileRepositoryTest {

  @Autowired
  private DhcpConfigServiceProfileRepository target;

  @Test
  void findByTemplateIdAndTenantId() {
    List<DhcpConfigServiceProfile> results =
        target.findByTenantId("4c8279f79307415fa9e4c88a1819f0fc");
    assertEquals(2, results.size());

    Optional<DhcpConfigServiceProfile> result =
        target.findByTemplateIdAndTenantId("dhcpConfigTemplate", "4c8279f79307415fa9e4c88a1819f0fc");
    assertEquals("dhcpConfigInstance", result.orElseThrow().getServiceName());
  }
}
