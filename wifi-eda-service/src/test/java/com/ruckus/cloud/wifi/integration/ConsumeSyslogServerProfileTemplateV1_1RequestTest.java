package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslog;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfileV1_1;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SyslogServerProfileTemplateTest")
@WifiIntegrationTest
public class ConsumeSyslogServerProfileTemplateV1_1RequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  @ApiAction(CfgAction.ACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE)
  class ConsumeActivateSyslogServerProfileTemplateOnVenueRequestTest {

    private String syslogServerProfileTemplateId;
    private String venueTemplateId;

    @BeforeEach
    void givenOneSyslogServerProfileAndVenueInDb(Tenant tenant, @Template Venue venue,
        @Template final SyslogServerProfile syslogServerProfile) {
      syslogServerProfileTemplateId = syslogServerProfile.getId();
      venueTemplateId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("syslogServerProfileTemplateId", syslogServerProfileTemplateId)
          .addPathVariable("venueTemplateId", venueTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(CfgAction.ACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE);
    }

    private void validateResult(CfgAction apiAction) {
      var venue = repositoryUtil.find(Venue.class, venueTemplateId);
      var syslogServerProfile = repositoryUtil.find(SyslogServerProfile.class, syslogServerProfileTemplateId);

      assertThat(syslogServerProfile)
          .isNotNull()
          .matches(profile -> Objects.equals(profile.getId(), syslogServerProfileTemplateId))
          .matches(profile -> Objects.equals(profile.getVenues().get(0).getId(), venueTemplateId));
      assertThat(venue)
          .isNotNull()
          .matches(v -> Objects.equals(v.getId(), venueTemplateId))
          .matches(v -> Objects.equals(v.getSyslogServerProfile().getId(), syslogServerProfileTemplateId));

      validateCmnCfgCollectorMessages(apiAction, List.of(syslogServerProfileTemplateId));
      validateActivityMessages(apiAction);
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE)
  class ConsumeDeactivateSyslogServerProfileTemplateOnVenueRequestTest {

    private String syslogServerProfileTemplateId;
    private String venueTemplateId;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(@Template Venue venue,
        @Template final SyslogServerProfile syslogServerProfile) {
      syslogServerProfile.setVenues(List.of(venue));
      repositoryUtil.createOrUpdate(syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
      syslogServerProfileTemplateId = syslogServerProfile.getId();

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      venue.setSyslogServerProfile(syslogServerProfile);
      venue.setSyslog(venueSyslog);
      venue.setApPassword("1qaz@WSX");
      venue.setCountryCode("US");
      repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
      venueTemplateId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("syslogServerProfileTemplateId", syslogServerProfileTemplateId)
          .addPathVariable("venueTemplateId", venueTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(CfgAction.DEACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE);
    }

    private void validateResult(CfgAction apiAction) {
      var venue = repositoryUtil.find(Venue.class, venueTemplateId);
      var syslogServerProfile = repositoryUtil.find(SyslogServerProfile.class, syslogServerProfileTemplateId);

      assertThat(syslogServerProfile)
          .isNotNull()
          .matches(profile -> Objects.equals(profile.getId(), syslogServerProfileTemplateId))
          .matches(profile -> profile.getVenues().isEmpty());
      assertThat(venue)
          .isNotNull()
          .matches(v -> Objects.equals(v.getId(), venueTemplateId))
          .matches(v -> v.getSyslogServerProfile() == null);

      validateCmnCfgCollectorMessages(apiAction, List.of(syslogServerProfileTemplateId));
      validateActivityMessages(apiAction);
    }
  }

  private void validateRepositoryData(
      String syslogServerProfileId,
      SyslogServerProfileV1_1 payload,
      CfgAction apiAction) {
    if (syslogServerProfileId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final SyslogServerProfile syslogServerProfile =
        repositoryUtil.find(SyslogServerProfile.class, syslogServerProfileId);

    if (payload == null) {
      assertThat(syslogServerProfile).isNull();
      return;
    }

    assertThat(syslogServerProfile)
        .isNotNull()
        .matches(profile -> Objects.equals(profile.getId(), syslogServerProfileId))
        .matches(profile -> Objects.equals(profile.getName(), payload.getName()))
        .matches(
            profile ->
                Objects.equals(profile.getPrimary().getServer(), payload.getPrimary().getServer()));
  }

  private void validateDdccmCfgRequestMessages(
      CfgAction apiAction,
      List<String> venueIds,
      SyslogServerProfileV1_1 profileV2) {
    if (apiAction == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(ddccmCfgRequestMessage.getPayload())
                    .extracting(WifiConfigRequest::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
                    .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                    .filteredOn(op -> venueIds.contains(op.getId()))
                    .hasSize(1)
                    .allMatch(op -> op.getAction() == Action.MODIFY)
                    .allSatisfy(
                        op ->
                            assertThat(op)
                                .extracting(
                                    com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                                .matches(
                                    commonInfo ->
                                        commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                    .allSatisfy(
                        op -> {
                          assertThat(profileV2).isNotNull();
                          assertThat(op)
                              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                              .matches(v -> venueIds.contains(v.getId()))
                              .matches(
                                  v ->
                                      profileV2
                                          .getPrimary()
                                          .getServer()
                                          .equals(v.getSyslog().getAddress()));
                        })
        );
  }

  private void validateCmnCfgCollectorMessages(
      CfgAction apiAction, List<String> syslogServerProfileIdList) {
    if (apiAction == null || syslogServerProfileIdList == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(cmnCfgCollectorMessage.getPayload())
                    .matches(msg -> msg.getTenantId().equals(tenantId))
                    .matches(msg -> msg.getRequestId().equals(requestId))
                    .extracting(ViewmodelCollector::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(Operations.class::cast)
                    .filteredOn(
                        op ->
                            EsConstants.Index.SYSLOG_SERVER_PROFILE_INDEX_NAME.equals(
                                op.getIndex()))
                    .filteredOn(op -> syslogServerProfileIdList.contains(op.getId()))
                    .filteredOn(op -> op.getOpType() == opType(apiAction))
                    .hasSize(syslogServerProfileIdList.size())
                    .singleElement()
                    .satisfies(
                        op -> {
                          if (op.getOpType() != OpType.DEL) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(
                                    doc ->
                                        syslogServerProfileIdList.contains(
                                            doc.get(EsConstants.Key.ID).getStringValue()))
                                .matches(
                                    doc ->
                                        tenantId.equals(
                                            doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                                .matches(doc -> Boolean.TRUE.equals(
                                    doc.get(EsConstants.Key.IS_TEMPLATE).getBoolValue()));
                          }
                        }));
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors.assertThat(
          kafkaTopicProvider.getActivityImpacted(),
          kafkaTopicProvider.getActivityCfgChangeResp()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(
                    activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(CfgStatus.ConfigurationStatus.Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(CfgStatus.ConfigurationStatus::getEventDate)
                    .isNotNull());
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1 -> OpType.ADD;
      case UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1 -> OpType.MOD;
      case DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1 -> OpType.DEL;
      case ACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE, DEACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE ->
          OpType.MOD;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1 -> ApiFlowNames.ADD_SYSLOG_SERVER_PROFILE_TEMPLATE;
      case UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1 -> ApiFlowNames.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE;
      case DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1 -> ApiFlowNames.DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE;
      case ACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE -> ApiFlowNames.ACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE;
      case DEACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE -> ApiFlowNames.DEACTIVATE_SYSLOG_SERVER_PROFILE_TEMPLATE_ON_VENUE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  @Nested
  @ApiAction(CfgAction.ADD_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1)
  class ConsumeAddSyslogServerProfileRequestTest {

    private SyslogServerProfileV1_1 syslogServerProfileV1_1;

    private Venue venue;

    @BeforeEach
    void givenOneVenuePersistedInDb(@Template final Venue venue) {
      this.venue = venue;
    }

    @Payload
    private SyslogServerProfileV1_1 givenUpdateProfile() {
      syslogServerProfileV1_1 = Generators.syslogServerProfileV1_1().generate();
      syslogServerProfileV1_1.setId(CommonTestFixture.randomId());
      return syslogServerProfileV1_1;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload SyslogServerProfileV1_1 syslogServerProfileV1_1) {
      // Then
      var apiAction = CfgAction.ADD_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1;
      var syslogServerProfileId = syslogServerProfileV1_1.getId();

      var profile = syslogServerProfileV1_1;
      validateRepositoryData(syslogServerProfileId, profile, apiAction);
      validateCmnCfgCollectorMessages(apiAction, List.of(syslogServerProfileId));
      validateActivityMessages(apiAction);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1)
  class ConsumeUpdateSyslogServerProfileRequestTest {

    private SyslogServerProfile syslogServerProfile;

    private SyslogServerProfileV1_1 updateSyslogServerProfileV1_1;

    private Venue oldVenue;
    private Venue newBindVenue;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(final Tenant tenant, @Template final Venue oldVenue,
        @Template final SyslogServerProfile syslogServerProfile) {
      this.syslogServerProfile = syslogServerProfile;
      this.oldVenue = oldVenue;
      this.oldVenue.setApPassword("1qaz@WSX");
      this.oldVenue.setCountryCode("US");
      this.newBindVenue =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venue()
              .setApPassword(always("1qaz@WSX"))
              .setAddressLine(always("Somewhere"))
              .setCountryCode(always("US"))
              .setTimezone(always("America/Los_Angeles"))
              .generate();

      syslogServerProfile.setVenues(List.of(oldVenue));
      syslogServerProfile.setTenant(tenant);

      repositoryUtil.createOrUpdate(
          syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      oldVenue.setSyslogServerProfile(syslogServerProfile);
      oldVenue.setSyslog(venueSyslog);
      repositoryUtil.createOrUpdate(
          oldVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      this.newBindVenue.setTenant(tenant);
      repositoryUtil.createOrUpdate(
          this.newBindVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("syslogServerProfileTemplateId", syslogServerProfile.getId());
    }

    @Payload
    private SyslogServerProfileV1_1 givenUpdateProfile() {
      updateSyslogServerProfileV1_1 = Generators.syslogServerProfileV1_1().generate();
      updateSyslogServerProfileV1_1.setId(syslogServerProfile.getId());
      return updateSyslogServerProfileV1_1;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      var apiAction = CfgAction.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1;
      var syslogServerProfileId = syslogServerProfile.getId();
      var payload = updateSyslogServerProfileV1_1;

      validateRepositoryData(syslogServerProfileId, payload, apiAction);
      validateDdccmCfgRequestMessages(
          apiAction,
          syslogServerProfile.getVenues().stream().map(Venue::getId).collect(Collectors.toList()),
          payload);
      validateCmnCfgCollectorMessages(apiAction, List.of(syslogServerProfileId));
      validateActivityMessages(apiAction);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1)
  class ConsumeDeleteSyslogServerProfileRequestTest {

    private String syslogServerProfileId;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(@Template final Venue venue,
        @Template final SyslogServerProfile syslogServerProfile) {
      syslogServerProfile.setVenues(List.of(venue));
      repositoryUtil.createOrUpdate(
          syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());
      syslogServerProfileId = syslogServerProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("syslogServerProfileTemplateId", syslogServerProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(CfgAction.DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE_V1_1);
    }

    private void validateResult(CfgAction apiAction) {
      assertThat(repositoryUtil.find(SyslogServerProfile.class, syslogServerProfileId)).isNull();
      validateDdccmCfgRequestMessages(null, null, null);
      validateCmnCfgCollectorMessages(apiAction, List.of(syslogServerProfileId));
      validateActivityMessages(apiAction);
    }
  }
}
