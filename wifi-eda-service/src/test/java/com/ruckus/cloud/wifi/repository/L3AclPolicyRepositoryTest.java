package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.servicemodel.projection.AccessPolicyQueryProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO venue (id, tenant) VALUES ('b1a2c59f92d243cba7ca5b4bfe83c289', '6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO l3acl_policy (id, name, tenant, default_access) VALUES
        ('059796b87b054c359a9828a354aab591', 'l3acl-policy-1', '6700bc51acf84c4aa9510df2ca00b5f4', 'ALLOW'),
        ('059796b87b054c359a9828a354aab592', 'l3acl-policy-2', '6700bc51acf84c4aa9510df2ca00b5f4', 'ALLOW');
    INSERT INTO l3acl_policy (id, name, tenant, default_access, is_template) VALUES
        ('l3acl-policy-template-1', 'l3acl-policy-template-1', '6700bc51acf84c4aa9510df2ca00b5f4', 'ALLOW', true),
        ('l3acl-policy-not-template-1', 'l3acl-policy-not-template-1', '6700bc51acf84c4aa9510df2ca00b5f4', 'ALLOW', false);
    INSERT INTO l3rule (id, description, tenant, priority, source_enable_ip_subnet, access, l3acl_policy) VALUES
        ('ec0a51f6bde14074b06dcdb2d6a18380', 'l3rule-1', '6700bc51acf84c4aa9510df2ca00b5f4', 1, true, 'ALLOW', '059796b87b054c359a9828a354aab591'),
        ('ec0a51f6bde14074b06dcdb2d6a18381', 'l3rule-2', '6700bc51acf84c4aa9510df2ca00b5f4', 1, true, 'ALLOW', 'l3acl-policy-template-1'),
        ('ec0a51f6bde14074b06dcdb2d6a18382', 'l3rule-3', '6700bc51acf84c4aa9510df2ca00b5f4', 1, true, 'ALLOW', 'l3acl-policy-not-template-1');
    """)
class L3AclPolicyRepositoryTest {

  private static final String TENANT_ID = "6700bc51acf84c4aa9510df2ca00b5f4";

  @Autowired
  private L3AclPolicyRepository repository;

  @Test
  void existsByTenantIdAndNameAndIdNot() {
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "l3acl-policy-1", "")).isTrue();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "l3acl-policy-2", "")).isTrue();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "l3acl-policy-3", "")).isFalse();
  }

  @Test
  void findByTenantIdAndQueryName() {
    // get l3acl-policy-1 check id
    assertThat(repository.findByTenantIdAndQueryName(TENANT_ID, "l3acl-policy-1", Pageable.unpaged()).getContent())
            .hasSize(1)
            .extracting(AccessPolicyQueryProjection::getId)
            .singleElement()
            .isEqualTo("059796b87b054c359a9828a354aab591");
    // no result
    assertThat(repository.findByTenantIdAndQueryName(TENANT_ID, "l3acl-policy-3", Pageable.unpaged()).getContent()).isEmpty();
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantId() {
    var result = repository.findByIdAndTenantId("l3acl-policy-template-1", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("l3acl-policy-template-1", result.get().getId());
    assertEquals(true, result.get().getIsTemplate());
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantIdWithIsNotTemplateId() {
    var result = repository.findByIdAndTenantId("l3acl-policy-not-template-1", TENANT_ID);

    assertTrue(result.isEmpty());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplate() {
    var result = repository.findByIdAndTenantId("l3acl-policy-not-template-1", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("l3acl-policy-not-template-1", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplateWithLegacyData() {
    var result = repository.findByIdAndTenantId("059796b87b054c359a9828a354aab591", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("059796b87b054c359a9828a354aab591", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }
}
