package com.ruckus.cloud.wifi.requirement.feature;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.SdLanProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.utils.FeatureRolesUtils;
import com.ruckus.cloud.wifi.viewmodel.FeatureGroup;
import com.ruckus.cloud.wifi.viewmodel.FeatureLevel;
import com.ruckus.cloud.wifi.viewmodel.FeatureType;
import java.util.List;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class NatTraversalFeatureTest {

  @MockBean
  private SdLanProfileRegularSettingRepository sdLanProfileRegularSettingRepository;

  @SpyBean
  private NatTraversalFeature unit;

  @Test
  void getFeatureNameTest() {
    assertThat(unit.getFeatureName())
        .isEqualTo(NatTraversalFeature.NAME);
  }

  @Test
  void getFeatureGroupTest() {
    assertThat(unit.getFeatureGroup())
        .isEqualTo(FeatureGroup.TUNNEL_PROFILE.getDisplayName());
  }

  @Test
  void getFeatureLevelTest() {
    assertThat(unit.getFeatureLevel())
        .isEqualTo(FeatureLevel.VENUE);
  }

  @Test
  void getFeatureTypeTest() {
    assertThat(unit.getFeatureType())
        .isEqualTo(FeatureType.EDGE);
  }

  @Test
  void getRequirementsTest() {
    assertThat(unit.getRequirements())
        .isNotEmpty().singleElement()
        .satisfies(apFirmwareModels -> {
          assertThat(apFirmwareModels.getFirmware()).isEqualTo(NatTraversalFeature.REQUIRED_MINIMUM_VERSION);
          assertThat(apFirmwareModels.getModels()).isEqualTo(NatTraversalFeature.SUPPORTED_MODELS);
        });
  }

  @Test
  @FeatureRole(FeatureRolesUtils.EDGE_NAT_TRAVERSAL)
  @FeatureFlag(disable = FlagNames.EDGE_NAT_TRAVERSAL_PHASE1_TOGGLE)
  void GivenNatTraversalFFIsDisabled(Venue venue) {
    BDDAssertions.then(unit.test(venue)).isFalse();
  }

  @Nested
  @FeatureFlag(enable = FlagNames.EDGE_NAT_TRAVERSAL_PHASE1_TOGGLE)
  class GivenNatTraversalFFEnabled {

    @Test
    void givenSdLanNotExist(Venue venue) {
      when(sdLanProfileRegularSettingRepository.findByTenantIdAndVenueIdIn(
          eq(TxCtxHolder.tenantId()), eq(List.of(venue.getId())))
      ).thenReturn(List.of());
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Nested
    @FeatureRole(FeatureRolesUtils.EDGE_NAT_TRAVERSAL)
    class givenSdLanExistWithFeatureRole {

      @Test
      void givenNatTraversalEnabledTunnelWithSdLan(Venue venue) {
        // given
        TunnelProfile tunnelProfile = new TunnelProfile();
        tunnelProfile.setNatTraversalEnabled(true);
        var sdLanProfileRegularSetting = newSdLanProfileRegularSetting(
            CommonTestFixture.randomId(), venue.getId());
        sdLanProfileRegularSetting.setTunnelProfile(tunnelProfile);

        // when
        when(sdLanProfileRegularSettingRepository.findByTenantIdAndVenueIdIn(
            eq(TxCtxHolder.tenantId()), eq(List.of(venue.getId())))
        ).thenReturn(List.of(sdLanProfileRegularSetting));

        // then
        BDDAssertions.then(unit.test(venue)).isTrue();
      }

      @Test
      void givenNatTraversalDisabledTunnelWithSdLan(Venue venue) {
        // given
        TunnelProfile tunnelProfile1 = new TunnelProfile();
        tunnelProfile1.setNatTraversalEnabled(false);
        var sdLanProfileRegularSetting1 = newSdLanProfileRegularSetting(
            CommonTestFixture.randomId(), venue.getId());
        sdLanProfileRegularSetting1.setTunnelProfile(tunnelProfile1);
        TunnelProfile tunnelProfile2 = new TunnelProfile();
        tunnelProfile2.setNatTraversalEnabled(null);
        var sdLanProfileRegularSetting2 = newSdLanProfileRegularSetting(
            CommonTestFixture.randomId(), venue.getId());
        sdLanProfileRegularSetting2.setTunnelProfile(tunnelProfile2);

        // when
        when(sdLanProfileRegularSettingRepository.findByTenantIdAndVenueIdIn(
            eq(TxCtxHolder.tenantId()), eq(List.of(venue.getId())))
        ).thenReturn(List.of(sdLanProfileRegularSetting1, sdLanProfileRegularSetting2));

        // then
        BDDAssertions.then(unit.test(venue)).isFalse();
      }
    }

    @Nested
    class givenSdLanExistWithoutFeatureRole {

      @Test
      void givenNatTraversalEnabledTunnelWithSdLan(Venue venue) {
        // given
        TunnelProfile tunnelProfile = new TunnelProfile();
        tunnelProfile.setNatTraversalEnabled(true);
        var sdLanProfileRegularSetting = newSdLanProfileRegularSetting(
            CommonTestFixture.randomId(), venue.getId());
        sdLanProfileRegularSetting.setTunnelProfile(tunnelProfile);

        // when
        when(sdLanProfileRegularSettingRepository.findByTenantIdAndVenueIdIn(
            eq(TxCtxHolder.tenantId()), eq(List.of(venue.getId())))
        ).thenReturn(List.of(sdLanProfileRegularSetting));

        // then
        BDDAssertions.then(unit.test(venue)).isFalse();
      }
    }
  }

  private SdLanProfileRegularSetting newSdLanProfileRegularSetting(String sdLanId, String venueId) {
    SdLanProfileRegularSetting sdLanProfileRegularSetting = new SdLanProfileRegularSetting();
    sdLanProfileRegularSetting.setSdLanProfile(new SdLanProfile(sdLanId));
    sdLanProfileRegularSetting.setVenue(new Venue(venueId));
    return sdLanProfileRegularSetting;
  }
}
