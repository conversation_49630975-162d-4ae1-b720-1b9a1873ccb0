package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class VenueMulticastRateLimitFeatureTest {

  @SpyBean
  private VenueMulticastRateLimitFeature unit;

  @Nested
  class WhenMulticastRateLimitConfigInVenue {

    @Test
    @FeatureFlag(disable = WIFI_R370_TOGGLE)
    void givenR370FFDisable_venue(Venue venue) {
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = WIFI_R370_TOGGLE)
    void givenMulticastRateLimitDisableForVenue(Venue venue) {
      venue.getRadioCustomization().getRadioParams6G().setEnableMulticastUplinkRateLimiting(false);
      venue.getRadioCustomization().getRadioParams6G().setEnableMulticastDownlinkRateLimiting(false);

      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Test
    @FeatureFlag(enable = WIFI_R370_TOGGLE)
    void givenMulticastRateLimitEnableForVenue() {
      var radio6g = new VenueRadioParams6G();
      radio6g.setEnableMulticastDownlinkRateLimiting(true);
      radio6g.setEnableMulticastUplinkRateLimiting(true);
      var radioCustom = new VenueRadioCustomization();
      radioCustom.setRadioParams6G(radio6g);
      var venue = new Venue();
      venue.setRadioCustomization(radioCustom);

      BDDAssertions.then(unit.test(venue)).isTrue();
    }
  }
}
