package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE;
import static com.ruckus.cloud.wifi.kafka.consumer.MspServiceConsumer.ACTION;
import static com.ruckus.cloud.wifi.kafka.consumer.MspServiceConsumer.ACTION_TYPE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.mspservice.protobuf.message.CommonMsp;
import com.ruckus.acx.mspservice.protobuf.message.MspEcRequest;
import com.ruckus.cloud.wifi.core.header.HttpHeaderContext;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.kafka.consumer.MspServiceConsumer;
import com.ruckus.cloud.wifi.repository.VenueApFirmwareBatchOperationRepository;
import com.ruckus.cloud.wifi.service.UpgradeService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApModelGreenfieldFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.GenericMessage;

@Slf4j
@WifiIntegrationTest
public class ConsumeMspServiceTest extends AbstractRequestTest {
  @Autowired
  private UpgradeService upgradeService;

  @Autowired
  private MspServiceConsumer mspServiceConsumer;

  @Autowired
  VenueApFirmwareBatchOperationRepository venueApFirmwareBatchOperationRepository;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @SneakyThrows
  @Test
  public void updateSchedules(Tenant admin,
  @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.200") ApVersion targetVersion) {
    // Given
    HttpHeaderContext.setHeader(HttpHeaderName.REQUEST_ID, randomId());
    List<Tenant> tenantList = List.of(
            TenantTestFixture.randomTenant((t) -> {}),
            TenantTestFixture.randomTenant((t) -> {}));
    List<String> tenantIds = tenantList.stream().map((tenant) -> {
      repositoryUtil.createOrUpdate(tenant, admin.getId(), randomTxId());
      return tenant.getId();
    }).toList();

    Venue venue1 = createVenue(tenantList.get(0), "venue-1", targetVersion);
    Venue venue2 = createVenue(tenantList.get(1), "venue-2", targetVersion);

    // When
    for (String tenantId: tenantIds) {
      TxCtxHolder.set(new TxCtxHolder.TxCtx(tenantId, randomId(), "test", "test flow"));
      upgradeService.updateManualSchedule(tenantId, "2022-08-02", "22:00-00:00");
    }

    assertDdccmCfgRequestNotSent(tenantIds.toArray(new String[tenantIds.size()]));
  }

  @SneakyThrows
  @Test
  @FeatureFlag(enable = {AP_FW_MGMT_UPGRADE_BY_MODEL, GREENFIELD_BY_AP_MODEL_TOGGLE})
  public void updateSchedulesByApModel(@com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.199") ApVersion version199,
                                       @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.200") ApVersion targetVersion) {
    // Given
    String mspTenantId = txCtxExtension.getTenantId();
    String requestId = txCtxExtension.getRequestId();

    List<Tenant> tenantList = List.of(
        TenantTestFixture.randomTenant((t) -> {}),
        TenantTestFixture.randomTenant((t) -> {}));
    List<String> mspEcs = tenantList.stream().map((tenant) -> {
      repositoryUtil.createOrUpdate(tenant, mspTenantId);
      return tenant.getId();
    }).toList();

    Venue venue1 = createVenueAndSetSchedule(tenantList.get(0), targetVersion);
    Venue venue2 = createVenueAndSetSchedule(tenantList.get(1), targetVersion);
    repositoryUtil.createOrUpdate(ApModelGreenfieldFirmwareTestFixture.randomApModelGreenfieldFirmware("R550", targetVersion), mspEcs.get(0));
    repositoryUtil.createOrUpdate(ApModelGreenfieldFirmwareTestFixture.randomApModelGreenfieldFirmware("R500", targetVersion), mspEcs.get(1));
    repositoryUtil.createOrUpdate(TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue1.getTenant(), targetVersion, "R550"), mspEcs.get(0));
    repositoryUtil.createOrUpdate(TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue2.getTenant(), targetVersion, "R500"), mspEcs.get(1));
    repositoryUtil.createOrUpdate(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue1, version199, "R550"), mspEcs.get(0));
    repositoryUtil.createOrUpdate(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue2, version199, "R500"), mspEcs.get(1));

    Message<byte[]> message = getMessage(mspEcs, requestId, mspTenantId);

    // process and verify
    mspServiceConsumer.consume(message);

    assertThat(venueApFirmwareBatchOperationRepository.findByTenantId(mspEcs.get(0)))
        .isNotNull()
        .hasSize(1);
    assertThat(venueApFirmwareBatchOperationRepository.findByTenantId(mspEcs.get(1)))
        .isNotNull()
        .hasSize(1);
  }

  private static Message<byte[]> getMessage(List<String> mspEcs, String requestId, String mspTenantId) {
    MspEcRequest.UpgradeMspEcFirmwareRequest request = MspEcRequest.UpgradeMspEcFirmwareRequest.newBuilder()
        .addAllTenantIds(mspEcs)
        .setManualSchedule(CommonMsp.ManualSchedule.newBuilder()
            .setDate("2022-08-04")
            .setTime("22:00-00:00")
                .build())
        .build();

    return new GenericMessage<>(request.toByteArray(), new MessageHeaders(Map.of(
        ACTION, ACTION_TYPE,
        WifiCommonHeader.REQUEST_ID, requestId,
        WifiCommonHeader.TENANT_ID, mspTenantId)));
  }

  private Venue createVenueAndSetSchedule(Tenant tenant, ApVersion version) {
    Venue venue = repositoryUtil.createOrUpdate(
        VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("Asia/Taipei")), tenant.getId());
    setVenueSchedule(version, venue, "2022-08-04 08:00:00");
    return repositoryUtil.find(Venue.class, venue.getId());
  }

  private void setVenueSchedule(ApVersion version, Venue venue, String localStartDateTime) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime sldt = LocalDateTime.parse(localStartDateTime, formatter);
    LocalDateTime eldt = sldt.plusHours(2);
    ZonedDateTime startZonedDateTime = ZonedDateTime.of(sldt, ZoneId.systemDefault());
    ZonedDateTime endZonedDateTime = ZonedDateTime.of(eldt, ZoneId.systemDefault());
    ScheduleTimeSlot scheduleTimeSlot = new ScheduleTimeSlot(startZonedDateTime.toString());
    scheduleTimeSlot.setTotalCapacityVenue(2500);
    scheduleTimeSlot.setStartDateTime(Date.from(startZonedDateTime.toInstant()));
    scheduleTimeSlot.setEndDateTime(Date.from(endZonedDateTime.toInstant()));
    scheduleTimeSlot = repositoryUtil.createOrUpdate(scheduleTimeSlot, venue.getTenant().getId());

    UpgradeSchedule upgradeSchedule = new UpgradeSchedule(randomId());
    upgradeSchedule.setTenant(venue.getTenant());
    upgradeSchedule.setVersion(version);
    upgradeSchedule.setTimeSlot(scheduleTimeSlot);
    upgradeSchedule.setVenue(venue);
    repositoryUtil.createOrUpdate(upgradeSchedule, venue.getTenant().getId());
  }

}
