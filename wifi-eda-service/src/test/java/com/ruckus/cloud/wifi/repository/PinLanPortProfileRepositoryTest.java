package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("VxLanTunnelFeatureTest")
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES
        ('tenantId1'),
        ('tenantId2'),
        ('tenantId3');

    INSERT INTO ap_lan_port_profile (tenant, category, id, vni) VALUES
        ('tenantId1', 'PIN', 'pinLanPortTenant1Vni100', 100),
        ('tenantId1', 'PIN', 'pinLanPortTenant1Vni200', 200),
        ('tenantId1', 'ETHERNET', 'ethernetPortTenant1Vni100', 100),
        ('tenantId2', 'PIN', 'pinLanPortTenant2Vni100', 100),
        ('tenantId2', 'PIN', 'pinLanPortTenant2Vni200', 200),
        ('tenantId2', 'ETHERNET', 'ethernetPortTenant2Vni200', 200),
        ('tenantId3', 'ETHERNET', 'ethernetPortTenant3Vni100', 100),
        ('tenantId3', 'ETHERNET', 'ethernetPortTenant3Vni200', 200);
    """)
class PinLanPortProfileRepositoryTest {

  @Autowired
  private PinLanPortProfileRepository repository;

  @Test
  void findByTenantIdAndVniTest() {
    assertThat(repository.findByTenantIdAndVni("tenantId1", 100)).isPresent()
        .hasValueSatisfying(result -> {
          assertThat(result.getId()).isEqualTo("pinLanPortTenant1Vni100");
          assertThat(result.getTenant().getId()).isEqualTo("tenantId1");
          assertThat(result.getVni()).isEqualTo(100);
        });

    assertThat(repository.findByTenantIdAndVni("tenantId2", 200)).isPresent()
        .hasValueSatisfying(result -> {
          assertThat(result.getId()).isEqualTo("pinLanPortTenant2Vni200");
          assertThat(result.getTenant().getId()).isEqualTo("tenantId2");
          assertThat(result.getVni()).isEqualTo(200);
        });

    assertThat(repository.findByTenantIdAndVni("tenantId3", 100)).isEmpty();
    assertThat(repository.findByTenantIdAndVni("tenantId3", 200)).isEmpty();
  }
}
