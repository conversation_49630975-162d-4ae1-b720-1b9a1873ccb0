package com.ruckus.cloud.wifi.integration.network;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Eap;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Plmn;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20RoamConsortium;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Hotspot20NetworkGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
public class ConsumeHotspot20NetworkRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private RevisionService revisionService;

  @Autowired
  private MessageCaptors messageCaptors;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Nested
  @ApiAction(CfgAction.ADD_NETWORK)
  class ConsumeAddHotspot20NetworkRequestTest {

    @Payload
    private final Hotspot20NetworkGenerator generator =
        com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.hotspot20Network();

    @Test
    void thenShouldHandleTheRequestSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Network payload) {
      validateResult(txCtx, apiAction, payload.getId(), payload);
    }

  }

  @Nested
  @ApiAction(CfgAction.UPDATE_NETWORK)
  class ConsumeUpdateHotspot20NetworkRequestTest {

    @Payload
    private final Hotspot20NetworkGenerator generator =
        com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.hotspot20Network().setId(nullValue(String.class));

    private String networkId;

    @BeforeEach
    void givenOneHotspot20NetworkPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant, final Venue venue) {
      final var network = network(Hotspot20Network.class).generate();

      final var hotspot20Operator = Generators.hotspot20Operator().generate();
      final var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
      hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));
      hotspot20FriendlyName.setOperator(hotspot20Operator);

      final var hotspot20IdentityProvider = Generators.hotspot20IdentityProvider()
          .setRoamConsortiumOis(nullValue(Hotspot20RoamConsortium.class).toListGenerator(0))
          .setPlmns(nullValue(Hotspot20Plmn.class).toListGenerator(0)).generate();
      final var hotspot20NaiRealm = Generators.hotspot20NaiRealm()
          .setEaps(nullValue(Hotspot20Eap.class).toListGenerator(0)).generate();
      hotspot20IdentityProvider.setNaiRealms(List.of(hotspot20NaiRealm));
      hotspot20NaiRealm.setIdentityProvider(hotspot20IdentityProvider);

      final var connectionCapability = Generators.hotspot20ConnectionCapability().generate();
      final var networkHotspot20Settings = Generators.networkHotspot20Settings().generate();
      connectionCapability.setNetworkHotspot20Settings(networkHotspot20Settings);
      networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability));
      networkHotspot20Settings.setOperator(hotspot20Operator);

      final var hotspot20Network = (Hotspot20Network) network;
      hotspot20Network.getWlan().setNetwork(hotspot20Network);
      hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

      hotspot20IdentityProvider.setNetworkHotspot20SettingsList(List.of(networkHotspot20Settings));

      repositoryUtil.createOrUpdate(hotspot20Operator, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20FriendlyName, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20IdentityProvider, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20NaiRealm, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(connectionCapability, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20Network, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
          .setNetwork(always(hotspot20Network)).setVenue(always(venue)).generate();
      hotspot20Network.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      hotspot20Network.getHotspot20Settings().setIdentityProviders(List.of(hotspot20IdentityProvider));
      repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());

      networkId = hotspot20Network.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Network payload) {
      validateResult(txCtx, apiAction, networkId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_NETWORK)
  class ConsumeDeleteHotspot20NetworkRequestTest {

    private String networkId;

    @BeforeEach
    void givenOneHotspot20NetworkPersistedInDbAndActivatedOnOneVenue(
        final Tenant tenant, final Venue venue) {
      final var network = network(Hotspot20Network.class).generate();

      final var hotspot20Operator = Generators.hotspot20Operator().generate();
      final var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
      hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));
      hotspot20FriendlyName.setOperator(hotspot20Operator);

      final var hotspot20IdentityProvider = Generators.hotspot20IdentityProvider()
          .setRoamConsortiumOis(nullValue(Hotspot20RoamConsortium.class).toListGenerator(0))
          .setPlmns(nullValue(Hotspot20Plmn.class).toListGenerator(0)).generate();
      final var hotspot20NaiRealm = Generators.hotspot20NaiRealm()
          .setEaps(nullValue(Hotspot20Eap.class).toListGenerator(0)).generate();
      hotspot20IdentityProvider.setNaiRealms(List.of(hotspot20NaiRealm));
      hotspot20NaiRealm.setIdentityProvider(hotspot20IdentityProvider);

      final var connectionCapability = Generators.hotspot20ConnectionCapability().generate();
      final var networkHotspot20Settings = Generators.networkHotspot20Settings().generate();
      connectionCapability.setNetworkHotspot20Settings(networkHotspot20Settings);
      networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability));
      networkHotspot20Settings.setOperator(hotspot20Operator);

      final var hotspot20Network = (Hotspot20Network) network;
      hotspot20Network.getWlan().setNetwork(hotspot20Network);
      hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

      hotspot20IdentityProvider.setNetworkHotspot20SettingsList(List.of(networkHotspot20Settings));

      repositoryUtil.createOrUpdate(hotspot20Operator, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20FriendlyName, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20IdentityProvider, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20NaiRealm, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(connectionCapability, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20Network, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
          .setNetwork(always(hotspot20Network)).setVenue(always(venue)).generate();
      hotspot20Network.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      // Add relational of networkHotspot20Settings to hotspot20IdentityProvider to test cascade remove is working
      hotspot20Network.getHotspot20Settings().setIdentityProviders(List.of(hotspot20IdentityProvider));
      repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());

      networkId = hotspot20Network.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                @Payload com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Network payload) {
      validateResult(txCtx, apiAction, networkId, payload);
    }
  }

  private void validateResult(TxCtxHolder.TxCtx txCtx, CfgAction apiAction, String networkId,
                              com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Network payload) {
    validateRepositoryData(txCtx, apiAction, networkId, payload);
    validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
    validateCmnCfgCollectorMessages(txCtx, apiAction, List.of(networkId));
    validateActivityMessages(txCtx, apiAction);
  }

  private void validateRepositoryData(TxCtxHolder.TxCtx txCtx, CfgAction apiAction, String networkId,
                                      com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Network payload) {
    if (networkId == null) {
      final String requestId = txCtx.getTxId();
      final String tenantId = txCtx.getTenant();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty, "should be empty");
      return;
    }

    final Hotspot20Network network = repositoryUtil.find(Hotspot20Network.class, networkId);

    if (apiAction == null || apiAction == CfgAction.DELETE_NETWORK || payload == null) {
      assertThat(network).isNull();
      return;
    }

    assertThat(network)
        .isNotNull()
        .matches(n -> Objects.equals(n.getId(), networkId))
        .matches(n -> Objects.equals(n.getName(), payload.getName()))
        .matches(n -> Objects.equals(n.getDescription(), payload.getDescription()))
        .matches(n -> Objects.equals(NetworkTypeEnum.HOTSPOT20, n.getType()))
        .extracting(Hotspot20Network::getHotspot20Settings) // Hotspot 2.0 Settings
        .matches(hs -> Objects.equals(hs.getAllowInternetAccess(), payload.getHotspot20Settings().getAllowInternetAccess()))
        .matches(hs -> Objects.equals(hs.getAccessNetworkType(), payload.getHotspot20Settings().getAccessNetworkType()))
        .matches(hs -> Objects.equals(hs.getIpv4AddressType(), payload.getHotspot20Settings().getIpv4AddressType()))
        .matches(hs -> Objects.equals(hs.getConnectionCapabilities().size(), payload.getHotspot20Settings().getConnectionCapabilities().size()));
  }

  private void validateDdccmCfgRequestMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                               List<String> networkIdList, com.ruckus.cloud.wifi.eda.viewmodel.Hotspot20Network payload) {
    if (apiAction == null || apiAction == CfgAction.ADD_NETWORK || networkIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final String requestId = txCtx.getTxId();
    final String tenantId = txCtx.getTenant();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(KafkaProtoMessage::getHeaders).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
            .describedAs("Should contains %s WlanVenue operations", networkIdList.size())
            .hasSize(networkIdList.size())
            .allMatch(op -> StringUtils.isNotEmpty(op.getId()),
                "All the ids of the WlanVenue operations should not be empty")
            .allMatch(op -> op.getAction() == action(apiAction),
                String.format("All the actions of the WlanVenue operations should be [%s]", apiAction))
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                    String.format("The value of `requestId` field in CommonInfo should be [%s]", requestId))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                    String.format("The value of `tenantId` field in CommonInfo should be [%s]", tenantId))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                    String.format("The value of `sender` field in CommonInfo should be [%s]", ServiceType.WIFI_SERVICE)))
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                    .matches(n -> networkIdList.contains(n.getWlanId()));
              } else {
                assertThat(payload).isNotNull();
                assertSoftly(softly -> {
                  softly.assertThat(op.getWlanVenue().getWlanId())
                      .matches(networkIdList::contains);
                  softly.assertThat(op.getWlanVenue().getSsid())
                      .isEqualTo(payload.getWlan().getSsid());
                });
              }
            }));
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK -> Action.ADD;
      case UPDATE_NETWORK -> Action.MODIFY;
      case DELETE_NETWORK -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void validateCmnCfgCollectorMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction, List<String> networkIdList) {
    if (apiAction == null || networkIdList == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final String requestId = txCtx.getTxId();
    final String tenantId = txCtx.getTenant();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operations.class::cast)
            .satisfies(ops -> {
              if (Action.DELETE == action(apiAction)) {
                assertThat(ops)
                    .filteredOn(op -> EsConstants.Index.HOTSPOT20_OPERATOR_INDEX_NAME.equals(op.getIndex()))
                    .filteredOn(op -> op.getOpType() == OpType.MOD)
                    .asList().hasSize(1);
                assertThat(ops)
                    .filteredOn(op -> EsConstants.Index.HOTSPOT20_IDENTITY_PROVIDER_INDEX_NAME.equals(op.getIndex()))
                    .filteredOn(op -> op.getOpType() == OpType.MOD)
                    .asList().hasSize(1);
              }
            })
            .filteredOn(op -> EsConstants.Index.NETWORK.equals(op.getIndex()))
            .hasSize(networkIdList.size())
            .allMatch(op -> networkIdList.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction))
            .allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> networkIdList.contains(doc.get(EsConstants.Key.ID).getStringValue()))
                    .matches(doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()));
              }
            }));
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK -> OpType.ADD;
      case UPDATE_NETWORK -> OpType.MOD;
      case DELETE_NETWORK -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void validateActivityMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction) {
    if (apiAction == null) {
 messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtx.getTenant());
      return;
    }

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(txCtx.getTenant(), txCtx.getTxId());
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(CfgStatus.ConfigurationStatus.Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
            .extracting(CfgStatus.ConfigurationStatus::getEventDate).isNotNull());

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(txCtx.getTenant(), txCtx.getTxId());
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityImpactedMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 0)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList).asList()
            .isEmpty());
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_NETWORK -> ApiFlowNames.ADD_NETWORK;
      case UPDATE_NETWORK -> ApiFlowNames.UPDATE_NETWORK;
      case DELETE_NETWORK -> ApiFlowNames.DELETE_NETWORK;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

}
