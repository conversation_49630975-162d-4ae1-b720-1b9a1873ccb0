package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.protobuf.certificatetemplate.Action;
import com.ruckus.cloud.protobuf.certificatetemplate.CaResponse;
import com.ruckus.cloud.protobuf.certificatetemplate.CertificateTemplateResponse;
import com.ruckus.cloud.protobuf.certificatetemplate.Status;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.utils.CertificateUtil;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeCertificateAuthorityTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  class whenConsumeCertificateAuthorityResponse {

    final String tenantId = randomId();
    final String requestId = randomTxId();
    final String CA_ID = randomId();

    @Test
    void consumeCertificateAuthorityCreateMessage() {

      messageUtil.sendCertificateTemplateResponse(
        randomId(),
        randomTxId(),
        generateCaMsg(Action.CREATE));

      validateResult(Action.CREATE);
    }

    @Test
    void consumeCertificateAuthorityUpdateMessage() {

      messageUtil.sendCertificateTemplateResponse(
          randomId(),
          randomTxId(),
          generateCaMsg(Action.UPDATE));

      validateResult(Action.UPDATE);
    }

    @Test
    void consumeCertificateAuthorityDeleteMessage() {

      messageUtil.sendCertificateTemplateResponse(
          randomId(),
          randomTxId(),
          generateCaMsg(Action.DELETE));

      validateResult(Action.DELETE);
    }

    private CertificateTemplateResponse generateCaMsg(Action action) {
      return CertificateTemplateResponse.newBuilder()
          .setRequestId(requestId)
          .setTenantId(tenantId)
          .setAction(action)
          .setCaResponse(
              CaResponse.newBuilder()
                  .setResourceId(CA_ID)
                  .setStatus(Status.SUCCESSFUL).build()
          ).build();
    }

    void validateResult(Action action) {
      assertThat(messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId)).isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(1)
          .first()
          .matches(o -> o.getAction() == CertificateUtil.getDdccmActionByCertAction(action))
          .extracting(Operation::getTrustedCAChain)
          .matches(ca -> ca.getId().equals(CA_ID));
    }
  }
}
