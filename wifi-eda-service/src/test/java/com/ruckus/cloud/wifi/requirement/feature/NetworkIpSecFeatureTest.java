package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.SoftGreProfileNetworkVenueActivationRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.List;
import java.util.Optional;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_IPSEC_PSK_OVER_NETWORK;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@WifiUnitTest
public class NetworkIpSecFeatureTest {

  @MockBean
  private SoftGreProfileNetworkVenueActivationRepository repository;

  @SpyBean
  private NetworkIpSecFeature unit;

  @Nested
  class WhenConfigIpSec {

    @Test
    @FeatureFlag(disable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenR370Disable(Network network) {
      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenIpSecDisable(Venue venue, Network network) {
      var nv = new NetworkVenue();
      nv.setVenue(venue);
      network.setNetworkVenues(List.of(nv));

      SoftGreProfileNetworkVenueActivation activation = new SoftGreProfileNetworkVenueActivation();

      when(repository.findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
          anyString(), anyString(), anyString())).thenReturn(Optional.of(activation));

      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenSoftGreDisable(Venue venue, Network network) {
      var nv = new NetworkVenue();
      nv.setVenue(venue);
      network.setNetworkVenues(List.of(nv));

      when(repository.findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
          anyString(), anyString(), anyString())).thenReturn(Optional.empty());

      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = {WIFI_R370_TOGGLE, WIFI_IPSEC_PSK_OVER_NETWORK})
    void givenIpSecEnable(Venue venue, Network network) {
      var nv = new NetworkVenue();
      nv.setVenue(venue);
      network.setNetworkVenues(List.of(nv));

      SoftGreProfileNetworkVenueActivation activation = new SoftGreProfileNetworkVenueActivation();
      activation.setIpsecProfile(new IpsecProfile());
      when(repository.findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
          anyString(), anyString(), anyString())).thenReturn(
          Optional.of(activation));

      BDDAssertions.then(unit.test(network)).isTrue();
    }
  }
}
