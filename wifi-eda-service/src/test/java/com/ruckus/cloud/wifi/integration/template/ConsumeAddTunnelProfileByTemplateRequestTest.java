package com.ruckus.cloud.wifi.integration.template;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.TunnelProfileRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_TUNNEL_SERVICE_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_TUNNEL_SERVICE_PROFILE_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddTunnelProfileByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private TunnelProfileRepository tunnelProfileRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void add_tunnelProfile_by_template(Tenant mspTenant, @Template TunnelProfile profile) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenantId, randomTxId());

    // check template data
    TunnelProfile profileTemplateFromDB =
        repositoryUtil.find(TunnelProfile.class, profile.getId(), mspTenantId, true);
    Assertions.assertNotNull(profileTemplateFromDB);
    assertProfile(profile, profileTemplateFromDB);

    // trigger add tunnel Profile by template
    String instanceId = randomId();
    addProfileByTemplate(instanceId, profile.getId(), ecTenantId, mspTenantId,
        userName);

    assertActivityPlan(ADD_TUNNEL_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_TUNNEL_SERVICE_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_TUNNEL_SERVICE_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  private void assertProfile(TunnelProfile expected, TunnelProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getIsTemplate(), actual.getIsTemplate());
    assertEquals(expected.getTenant().getId(), actual.getTenant().getId());
  }

  private void addProfileByTemplate(String instanceId, String templateId, String ecTenantId,
      String mspTenantId, String userName) {
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
            ADD_TUNNEL_SERVICE_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.TUNNEL_PROFILE,
        instanceId);
    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, templateId)
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES,
            executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, randomTxId(),
        CfgAction.ADD_TUNNEL_SERVICE_PROFILE_BY_TEMPLATE,
            ADD_TUNNEL_SERVICE_PROFILE_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());
  }
}
