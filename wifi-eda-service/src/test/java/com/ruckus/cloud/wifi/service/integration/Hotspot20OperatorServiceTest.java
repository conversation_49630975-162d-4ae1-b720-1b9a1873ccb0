package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.config.RadiusServerSettingConfig;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkHotspot20Settings;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.ViewmodelAggregatedEntityListener;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.Hotspot20OperatorCmnCfgCollectorOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmAuthRadiusVenueOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmHotspot20ProfileOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmHotspot20VenueProfileOperationBuilder;
import com.ruckus.cloud.wifi.entitylistener.ddccm.builder.DdccmNetworkVenueOperationBuilder;
import com.ruckus.cloud.wifi.kafka.publisher.DdccmPublisher;
import com.ruckus.cloud.wifi.kafka.publisher.ViewmodelConfigPublisher;
import com.ruckus.cloud.wifi.mapper.Hotspot20OperatorMerge;
import com.ruckus.cloud.wifi.mapper.Hotspot20OperatorMergeImpl;
import com.ruckus.cloud.wifi.repository.Hotspot20OperatorRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.impl.Hotspot20OperatorServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.impl.config.DdccmListenerTestKits;
import com.ruckus.cloud.wifi.service.integration.config.NetworkServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.TestContextHelper;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@WifiJpaDataTest
class Hotspot20OperatorServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @SpyBean
  private Hotspot20OperatorServiceCtrlImpl hotspot20OperatorService;
  @Autowired
  private Hotspot20OperatorRepository hotspot20OperatorRepository;
  @Autowired
  private NetworkRepository networkRepository;
  @MockBean
  private DdccmPublisher ddccmPublisher;
  @MockBean
  private ViewmodelConfigPublisher viewmodelConfigPublisher;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private TestContextHelper testContextHelper;

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateHotspot20OperatorOnWifiNetwork(Tenant tenant) throws Exception {
    // Given
    var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
    var hotspot20Operator = Generators.hotspot20Operator().generate();
    hotspot20FriendlyName.setOperator(hotspot20Operator);
    hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    var savedHotspot20Operator =
        repositoryUtil.createOrUpdate(hotspot20Operator, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    // When
    hotspot20OperatorService.activateHotspot20OperatorOnWifiNetwork(hotspot20Network.getId(),
        savedHotspot20Operator.getId());

    // Then
    testContextHelper.executeInNewTransaction(() -> {
      var savedNetwork = (Hotspot20Network) repositoryUtil.find(Network.class, hotspot20Network.getId());
      assertThat(savedNetwork)
          .isNotNull()
          .extracting(Hotspot20Network::getHotspot20Settings)
          .isNotNull()
          .extracting(NetworkHotspot20Settings::getOperator)
          .isNotNull()
          .extracting(AbstractBaseEntity::getId)
          .isEqualTo(savedHotspot20Operator.getId());
    }, tenant.getId(), randomTxId());

    // Given for case - activate another hotspot20 operator
    var hotspot20FriendlyName2 = Generators.hotspot20FriendlyName().generate();
    var hotspot20Operator2 = Generators.hotspot20Operator().generate();
    hotspot20FriendlyName2.setOperator(hotspot20Operator2);
    hotspot20Operator2.setFriendlyNames(List.of(hotspot20FriendlyName2));

    var savedHotspot20Operator2 =
        repositoryUtil.createOrUpdate(hotspot20Operator2, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    // When
    testContextHelper.executeInNewTransaction(() -> {
      try {
        hotspot20OperatorService.activateHotspot20OperatorOnWifiNetwork(hotspot20Network.getId(),
            savedHotspot20Operator2.getId());
      } catch (Exception ignored) {
      }
    }, tenant.getId(), randomTxId());

    ArgumentCaptor<ViewmodelCollector> captor = ArgumentCaptor.forClass(ViewmodelCollector.class);
    verify(viewmodelConfigPublisher, times(2)).publish(captor.capture(),
        any(Optional.class));

    List<ViewmodelCollector> viewmodelCfgRequests = captor.getAllValues();
    assertThat(viewmodelCfgRequests.get(0))
        .isNotNull()
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .hasSize(1); // activating hotspot20 operator
    assertThat(viewmodelCfgRequests.get(1))
        .isNotNull()
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .hasSize(2); // original and activating hotspot20 operator
  }

  @Test
  void testActivateHotspot20OperatorOnWifiNetwork_nonHotspot20Network(Tenant tenant, @OpenNetwork Network openNetwork) throws Exception {
    // Given
    var savedHotspot20Operator = this.generateAndSaveHotspot20Hotspot20Operator();
    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> hotspot20OperatorService.activateHotspot20OperatorOnWifiNetwork(openNetwork.getId(), savedHotspot20Operator.getId()));
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateHotspot20OperatorOnWifiNetwork_networkIsAlreadyActivated(Tenant tenant, Venue venue) throws Exception {
    // Given
    var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
    var hotspot20Operator = Generators.hotspot20Operator().generate();
    hotspot20FriendlyName.setOperator(hotspot20Operator);
    hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Enterprise);
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    var networkVenue = Generators.networkVenue().setNetwork(always(hotspot20Network)).setVenue(always(venue)).generate();

    var savedHotspot20Operator =
        repositoryUtil.createOrUpdate(hotspot20Operator, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(networkVenue, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    // When
    hotspot20OperatorService.activateHotspot20OperatorOnWifiNetwork(hotspot20Network.getId(),
        savedHotspot20Operator.getId());

    // Then
    ArgumentCaptor<WifiConfigRequest> captor = ArgumentCaptor.forClass(WifiConfigRequest.class);
    verify(ddccmPublisher, times(1)).publish(captor.capture());

    List<WifiConfigRequest> ddccmRequests = captor.getAllValues();
    assertThat(ddccmRequests)
        .isNotNull()
        .flatExtracting(WifiConfigRequest::getOperationsList)
        .filteredOn(Operation::hasWlanVenue)
        .extracting(Operation::getWlanVenue)
        .isNotNull();
  }

  @Test
  void testDeactivateHotspot20OperatorOnWifiNetwork(Tenant tenant) throws Exception {
    // Given
    var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
    var hotspot20Operator = Generators.hotspot20Operator().generate();
    hotspot20FriendlyName.setOperator(hotspot20Operator);
    hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.getHotspot20Settings().setOperator(hotspot20Operator);

    var savedHotspot20Operator =
        repositoryUtil.createOrUpdate(hotspot20Operator, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    // When
    hotspot20OperatorService.deactivateHotspot20OperatorOnWifiNetwork(hotspot20Network.getId(),
        savedHotspot20Operator.getId());

    // Then
    var savedNetwork = (Hotspot20Network) repositoryUtil.find(Network.class, hotspot20Network.getId());
    assertThat(savedNetwork)
        .isNotNull()
        .extracting(Hotspot20Network::getHotspot20Settings)
        .isNotNull()
        .extracting(NetworkHotspot20Settings::getOperator)
        .isNull();
  }

  @Test
  void testDeactivateHotspot20OperatorOnWifiNetwork_nonHotspot20Network(Tenant tenant, @OpenNetwork Network openNetwork) throws Exception {
    // Given
    var savedHotspot20Operator = this.generateAndSaveHotspot20Hotspot20Operator();

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> hotspot20OperatorService.deactivateHotspot20OperatorOnWifiNetwork(openNetwork.getId(), savedHotspot20Operator.getId()));
  }

  @Test
  void testDeactivateHotspot20OperatorOnWifiNetwork_wrongOperator(Tenant tenant) throws Exception {
    // Given
    var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
    var hotspot20Operator = Generators.hotspot20Operator().generate();
    hotspot20FriendlyName.setOperator(hotspot20Operator);
    hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);

    var savedHotspot20Operator =
        repositoryUtil.createOrUpdate(hotspot20Operator, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> hotspot20OperatorService.deactivateHotspot20OperatorOnWifiNetwork(hotspot20Network.getId(), savedHotspot20Operator.getId()));
  }

  @Test
  void testDeactivateHotspot20OperatorOnWifiNetwork_networkIsAlreadyActivated(Tenant tenant, Venue venue) throws Exception {
    // Given
    var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
    var hotspot20Operator = Generators.hotspot20Operator().generate();
    hotspot20FriendlyName.setOperator(hotspot20Operator);
    hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.getHotspot20Settings().setOperator(hotspot20Operator);

    var networkVenue = Generators.networkVenue().setNetwork(always(hotspot20Network)).setVenue(always(venue)).generate();

    var savedHotspot20Operator =
        repositoryUtil.createOrUpdate(hotspot20Operator, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(networkVenue, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> hotspot20OperatorService.deactivateHotspot20OperatorOnWifiNetwork(hotspot20Network.getId(), savedHotspot20Operator.getId()));
  }

  @Test
  void testValidationOnDeleteHotspot20Operator_withNetworkBinding(Tenant tenant) throws Exception {
    // Given
    var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
    var hotspot20Operator = Generators.hotspot20Operator().generate();
    hotspot20FriendlyName.setOperator(hotspot20Operator);
    hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));

    var hotspot20Network = (Hotspot20Network) Generators.hotspot20Network().generate();
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.getHotspot20Settings().setOperator(hotspot20Operator);

    var savedHotspot20Operator =
        repositoryUtil.createOrUpdate(hotspot20Operator, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(hotspot20Network, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    // Then
    InvalidPropertyValueException exception = assertThrows(InvalidPropertyValueException.class, () -> {
      hotspot20OperatorService.deleteHotspot20Operator(savedHotspot20Operator.getId());
    });
    assertEquals(Errors.WIFI_10528, exception.getErrorCode());
  }

  private Hotspot20Operator generateAndSaveHotspot20Hotspot20Operator() {
    var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
    var hotspot20Operator = Generators.hotspot20Operator().generate();
    hotspot20FriendlyName.setOperator(hotspot20Operator);
    hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));

    return repositoryUtil.createOrUpdate(hotspot20Operator, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
  }

  @TestConfiguration
  @Import({
      DdccmListenerTestKits.class,
      DdccmNetworkVenueOperationBuilder.class,
      DdccmHotspot20ProfileOperationBuilder.class,
      DdccmHotspot20VenueProfileOperationBuilder.class,
      DdccmAuthRadiusVenueOperationBuilder.class,
      ViewmodelAggregatedEntityListener.class,
      Hotspot20OperatorCmnCfgCollectorOperationBuilder.class,
      NetworkServiceCtrlImplTestConfig.class,
      RadiusServerSettingConfig.class
  })
  static class Config {

    @Bean
    public Hotspot20OperatorMerge hotspot20OperatorMerge() {
      return new Hotspot20OperatorMergeImpl();
    }

  }
}
