package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.client.workflow.WorkflowClient;
import com.ruckus.cloud.wifi.client.workflow.WorkflowClient.WorkflowProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.WorkflowProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.TestContextHelper;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Tag("WorkflowProfileTest")
@WifiJpaDataTest
class WorkflowProfileServiceCtrlTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @SpyBean
  private WorkflowProfileServiceCtrlImpl workflowProfileServiceCtrl;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private TestContextHelper testContextHelper;
  @MockBean
  private WorkflowClient workflowClient;

  private GuestNetwork guestNetwork;
  private String workflowProfileId;

  @BeforeEach
  void setUp(Tenant tenant) {
    workflowProfileId = "workflowProfileId";
    guestNetwork = network(GuestNetwork.class).generate();
    var guestPortal = Generators.guestPortal(GuestNetworkTypeEnum.Workflow).generate();
    guestPortal.setNetwork(guestNetwork);
    guestNetwork.setGuestPortal(guestPortal);
    guestNetwork.getWlan().setNetwork(guestNetwork);
    guestNetwork.setTenant(tenant);
    repositoryUtil.createOrUpdate(guestNetwork);
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateWorkflowProfileOnWifiNetwork(Tenant tenant) throws Exception {
    // Given
    guestNetwork.setWorkflowProfileId(workflowProfileId);
    repositoryUtil.createOrUpdate(guestNetwork);

    // When
    var mockWorkflowProfile = new WorkflowProfile(workflowProfileId, "workflow",
        "http://portal.url");
    when(workflowClient.getWorkflowProfileWithPublished(anyString())).thenReturn(
        Optional.of(mockWorkflowProfile));
    workflowProfileServiceCtrl.activateWorkflowProfileOnWifiNetwork(guestNetwork.getId(),
        workflowProfileId);

    // Then
    testContextHelper.executeInNewTransaction(() -> {
      var savedNetwork = (GuestNetwork) repositoryUtil.find(Network.class, guestNetwork.getId());
      assertThat(savedNetwork).isNotNull();
      assertThat(savedNetwork.getWorkflowProfileId()).isEqualTo(workflowProfileId);
    }, tenant.getId(), randomTxId());
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateWorkflowProfileOnWifiNetworkThrowException() throws Exception {
    // Given
    guestNetwork.setWorkflowProfileId(workflowProfileId);
    repositoryUtil.createOrUpdate(guestNetwork);

    // When
    when(workflowClient.getWorkflowProfileWithPublished(anyString())).thenReturn(Optional.empty());

    // Then
    assertThrows(
        ObjectNotFoundException.class,
        () ->  workflowProfileServiceCtrl.activateWorkflowProfileOnWifiNetwork(guestNetwork.getId(),
            workflowProfileId));
  }
}
