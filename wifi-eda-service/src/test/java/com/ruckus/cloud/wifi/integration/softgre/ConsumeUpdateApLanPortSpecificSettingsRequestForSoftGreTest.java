package com.ruckus.cloud.wifi.integration.softgre;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.newLanPortAdoption;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortOverwriteSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.ApLanPortSpecificSettingsGenerator;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApLanPortTestFixture;
import com.ruckus.cloud.wifi.test.fixture.EthernetPortProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.LanPortAdoptionTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SoftGreProfileTest")
@WifiIntegrationTest
@FeatureFlag(enable = {WIFI_ETHERNET_SOFTGRE_TOGGLE, ACX_UI_ETHERNET_TOGGLE})
class ConsumeUpdateApLanPortSpecificSettingsRequestForSoftGreTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;
  @Autowired private LanPortAdoptionServiceImpl lanPortAdoptionService;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeUpdateApLanPortSpecificSettingsRequestTest {

    private String serialNumber;
    private String venueId;
    private String portId = "1";

    @Payload("apLanPortSpecificSettings")
    private final ApLanPortSpecificSettingsGenerator generator =
        new ApLanPortSpecificSettingsGenerator().setUseVenueSettings(always(Boolean.TRUE));

    @BeforeEach
    void givenOnePersistedInDb(
        final Tenant tenant,
        final ApGroup apGroup,
        final Venue venue,
        final @ApModel("R500") Ap ap) {
      initAp(ap, apGroup);
      var softGreProfile =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      createApLanPortData(venue, ap, 3, portId, softGreProfile);
      createApLanPortData(venue, ap, 4, "2", null);
      serialNumber = ap.getId();
      venueId = venue.getId();
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      RequestParams requestParams =
          new RequestParams()
              .addPathVariable("venueId", venueId)
              .addPathVariable("serialNumber", serialNumber);
      var payload = generator.generate();
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  txCtxExtension.getTenantId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.UPDATE_AP_LAN_PORT_SPECIFIC_SETTINGS,
                  "",
                  requestParams,
                  payload))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);
      messageCaptors
          .getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      messageCaptors
          .getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
    }
  }

  private void initAp(Ap ap, ApGroup apGroup) {
    var apModelSpecific = new ApModelSpecific();
    apModelSpecific.setLanPorts(new ArrayList<>());
    apModelSpecific =
        repositoryUtil.createOrUpdate(apModelSpecific, txCtxExtension.getTenantId(), randomTxId());

    ap.setModelSpecific(apModelSpecific);
    ap.setApGroup(apGroup);
    repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());
  }

  private EthernetPortProfile createEthernetPortProfile(Venue venue, int ethernetPortProfileId) {
    return repositoryUtil.createOrUpdate(
        EthernetPortProfileTestFixture.randomEthernetPortProfile(
            venue,
            e -> {
              e.setApLanPortId(ethernetPortProfileId);
              e.setName(randomName());
              e.setType(ApLanPortTypeEnum.TRUNK);
              e.setUntagId((short) 1);
              e.setVlanMembers("1-4094");
              e.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
            }),
        txCtxExtension.getTenantId(),
        randomTxId());
  }

  private void createApLanPortData(
      Venue venue, Ap ap, int ethernetPortProfileId, String portId, SoftGreProfile softGreProfile) {
    var apLanPortProfile = createEthernetPortProfile(venue, ethernetPortProfileId);
    var apLanPort =
        repositoryUtil.createOrUpdate(
            ApLanPortTestFixture.randomApLanPort(
                venue,
                ap.getModelSpecific(),
                port -> {
                  port.setApLanPortProfile(apLanPortProfile);
                  port.setPortId(portId);
                  port.setApLanPortOverwriteSettings(new LanPortOverwriteSettings());
                  port.getApLanPortOverwriteSettings().setUntagId(apLanPortProfile.getUntagId());
                  port.getApLanPortOverwriteSettings()
                      .setVlanMembers(apLanPortProfile.getVlanMembers());
                }),
            txCtxExtension.getTenantId(),
            randomTxId());

    SoftGreProfileLanPortActivation activation;
    if (softGreProfile != null) {
      activation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .softGreProfileLanPortActivation()
              .setDhcpOption82Enabled(always(true))
              .setDhcpOption82Settings(
                  always(
                      com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                          .dhcpOption82Settings()
                          .generate()))
              .generate();
      activation.setSoftGreProfile(softGreProfile);
    } else {
      activation = null;
    }
    var adoption =
        repositoryUtil.createOrUpdate(
            LanPortAdoptionTestFixture.randomLanPortAdoption(
                apLanPortProfile,
                a ->
                    a.setChecksum(
                        lanPortAdoptionService.calculateChecksum(
                            newLanPortAdoption(apLanPortProfile, activation)))),
            txCtxExtension.getTenantId(),
            randomTxId());
    if (activation != null) {
      activation.setLanPortAdoption(adoption);
      repositoryUtil.createOrUpdate(activation, txCtxExtension.getTenantId(), randomTxId());
    }
    apLanPort.setLanPortAdoption(adoption);
    repositoryUtil.createOrUpdate(apLanPort, txCtxExtension.getTenantId(), randomTxId());
  }
}