package com.ruckus.cloud.wifi.service.integration;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.entitlement.service.api.RefreshLicensesResponse;
import com.ruckus.cloud.wifi.client.entitlement.EntitlementClient;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.redis.service.RedisClientService;
import com.ruckus.cloud.wifi.redis.service.RedisLockTemplate;
import com.ruckus.cloud.wifi.service.ApLicenseCacheService;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.impl.ApLicenseCacheServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.TestContainerRedisConfig;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.RedisTestContainer;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisServerCommands;
import org.springframework.data.redis.core.RedisTemplate;

@RedisTestContainer
@WifiUnitTest
public class ApLicenseCacheServiceTest {

  @Autowired
  private ApLicenseCacheService apLicenseCacheService;

  @Autowired
  private RedisTemplate<?, ?> redisTemplate;

  @MockBean
  private EntitlementClient entitlementClient;

  private void redisFlushAll() {
    Optional.ofNullable(redisTemplate.getConnectionFactory())
        .map(RedisConnectionFactory::getConnection)
        .ifPresent(RedisServerCommands::flushAll);
  }

  @Test
  public void testRemainingCountIsEnoughFromEnt() throws CommonException {
    String tenantId = "tenantId-123123123";
    String requestId = "requestId-123123123";
    TxCtxHolder.set(new TxCtx(tenantId, requestId, "test-user", "TestApLicense"));
    when(entitlementClient.getRefreshLicensesRequest(any(), any(), any())).thenReturn(
        RefreshLicensesResponse.newBuilder()
            .setRemainingDevices(10)
            .build());

    this.redisFlushAll();
    apLicenseCacheService.validateApLicense(5, tenantId);
    apLicenseCacheService.updateRemainingCountInCache(-5, tenantId);
  }

  @Test
  public void testRemainingCountIsEnoughFromCache() throws CommonException {
    String tenantId = "tenantId-123123123";
    String requestId = "requestId-123123123";
    TxCtxHolder.set(new TxCtx(tenantId, requestId, "test-user", "TestApLicense"));
    when(entitlementClient.getRefreshLicensesRequest(any(), any(), any())).thenReturn(
        RefreshLicensesResponse.newBuilder()
            .setRemainingDevices(5)
            .build());

    this.redisFlushAll();
    apLicenseCacheService.updateRemainingCountInCache(5, tenantId);
    apLicenseCacheService.validateApLicense(5, tenantId);
  }

  @Test
  public void testRemainingCountNotEnough() throws CommonException {
    String tenantId = "tenantId-123123123";
    String requestId = "requestId-123123123";
    TxCtxHolder.set(new TxCtx(tenantId, requestId, "test-user", "TestApLicense"));
    when(entitlementClient.getRefreshLicensesRequest(any(), any(), any())).thenReturn(
        RefreshLicensesResponse.newBuilder()
            .setRemainingDevices(0)
            .build());

    this.redisFlushAll();
    apLicenseCacheService.updateRemainingCountInCache(0, tenantId);
    try {
      apLicenseCacheService.validateApLicense(5, tenantId);
      fail("Should not escape from the validation");
    } catch (CommonException e) {
      assertEquals(Errors.WIFI_10126, e.getErrorCode());
    }
  }

  @TestConfiguration
  @Import(TestContainerRedisConfig.class)
  static class TestConfig {

    @Bean
    public ApLicenseCacheService apLicenseCacheService(RedisClientService redisClientService,
        RedisLockTemplate redisLockTemplate,
        EntitlementClient entitlementClient) {
      return new ApLicenseCacheServiceImpl(redisClientService, redisLockTemplate,
          entitlementClient);
    }

  }
}
