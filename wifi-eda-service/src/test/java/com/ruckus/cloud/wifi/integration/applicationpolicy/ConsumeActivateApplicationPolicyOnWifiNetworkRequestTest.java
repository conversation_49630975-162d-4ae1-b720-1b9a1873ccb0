package com.ruckus.cloud.wifi.integration.applicationpolicy;

import static com.ruckus.cloud.wifi.test.fixture.ApplicationPolicyTestFixture.randomApplicationPolicy;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.List;
import java.util.stream.Stream;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeActivateApplicationPolicyOnWifiNetworkRequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeActivateApplicationPolicyOnWifiNetworkMessage {
    private String networkId;
    private String applicationPolicyId;

    @BeforeEach
    void beforeEach(Network network, ApplicationPolicy applicationPolicy) {
      networkId = network.getId();
      applicationPolicyId = applicationPolicy.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkId", networkId)
          .addPathVariable("applicationPolicyId", applicationPolicyId);
    }

    @Test
    void givenApplicationPolicyNotExists(Tenant tenant) {
      final var notExistApplicationPolicyId = randomId();
      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK,
                      randomName(),
                      requestParams()
                          .addPathVariable("applicationPolicyId", notExistApplicationPolicyId),
                      ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(ObjectNotFoundException.class);

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a -> a.getStep().equals(ApiFlowNames.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK));
    }

    @Nested
    class GivenApplicationPolicyExists {
      @Test
      void givenNetworkNotExists(Tenant tenant) {
        final var notExistNetworkId = randomId();
        assertThatThrownBy(
                () ->
                    messageUtil.sendWifiCfgRequest(
                        tenant.getId(),
                        txCtxExtension.getRequestId(),
                        CfgAction.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK,
                        randomName(),
                        requestParams().addPathVariable("wifiNetworkId", notExistNetworkId),
                        ""))
            .isNotNull()
            .getRootCause()
            .isInstanceOf(ObjectNotFoundException.class);

        messageCaptors.assertThat(
            messageCaptors.getDdccmMessageCaptor(),
            messageCaptors.getCmnCfgCollectorMessageCaptor()
        ).doesNotSendByTenant(tenant.getId());

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
            .matches(
                a -> a.getStep().equals(ApiFlowNames.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK));
      }

      @Nested
      class GivenNetworkExists {
        @Nested
        class GivenOtherApplicationPolicyAlreadyActivated {
          private String otherApplicationPolicyId;

          @BeforeEach
          void beforeEach(Tenant tenant, Network network) {
            final var otherApplicationPolicy = randomApplicationPolicy(tenant, p -> {});
            repositoryUtil.createOrUpdate(otherApplicationPolicy, tenant.getId(), randomTxId());
            network.getWlan().getAdvancedCustomization().setApplicationPolicyEnable(true);
            network
                .getWlan()
                .getAdvancedCustomization()
                .setApplicationPolicy(otherApplicationPolicy);
            repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
            otherApplicationPolicyId = otherApplicationPolicy.getId();
          }

          @Test
          void thenShouldAlsoSendCMNForOtherApplicationPolicy(Tenant tenant) {
            messageUtil.sendWifiCfgRequest(
                tenant.getId(),
                txCtxExtension.getRequestId(),
                CfgAction.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK,
                randomName(),
                requestParams(),
                "");

            messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());

            assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
                .isNotNull()
                .extracting(KafkaProtoMessage::getPayload)
                .extracting(
                    ViewmodelCollector::getOperationsList,
                    InstanceOfAssertFactories.list(Operations.class))
                .hasSize(2)
                .satisfies(
                    operations ->
                        assertThat(operations)
                            .filteredOn(o -> o.getId().equals(otherApplicationPolicyId))
                            .singleElement()
                            .matches(o -> o.getOpType() == OpType.MOD)
                            .extracting(
                                Operations::getDocMap,
                                InstanceOfAssertFactories.map(String.class, Value.class))
                            .extractingByKey(EsConstants.Key.NETWORK_IDS)
                            .extracting(Value::getListValue)
                            .extracting(
                                ListValue::getValuesList,
                                InstanceOfAssertFactories.list(Value.class))
                            .extracting(Value::getStringValue)
                            .doesNotContain(networkId))
                .filteredOn(o -> o.getId().equals(applicationPolicyId))
                .singleElement()
                .matches(o -> o.getOpType() == OpType.MOD)
                .extracting(
                    Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
                .extractingByKey(EsConstants.Key.NETWORK_IDS)
                .isNotNull()
                .extracting(Value::getListValue)
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(1)
                .first()
                .extracting(Value::getStringValue)
                .isEqualTo(networkId);

            assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
                .isNotNull()
                .extracting(KafkaProtoMessage::getPayload)
                .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
                .matches(
                    a ->
                        a.getStep()
                            .equals(ApiFlowNames.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK));
          }
        }

        @Nested
        class GivenNoNetworkVenueExists {
          @Test
          void thenActivateWithoutSendingDDCCM(Tenant tenant) {
            messageUtil.sendWifiCfgRequest(
                tenant.getId(),
                txCtxExtension.getRequestId(),
                CfgAction.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK,
                randomName(),
                requestParams(),
                "");

            messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());

            assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
                .isNotNull()
                .extracting(KafkaProtoMessage::getPayload)
                .extracting(
                    ViewmodelCollector::getOperationsList,
                    InstanceOfAssertFactories.list(Operations.class))
                .hasSize(1)
                .first()
                .matches(o -> o.getOpType() == OpType.MOD)
                .matches(o -> o.getId().equals(applicationPolicyId))
                .extracting(
                    Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
                .extractingByKey(EsConstants.Key.NETWORK_IDS)
                .isNotNull()
                .extracting(Value::getListValue)
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(1)
                .first()
                .extracting(Value::getStringValue)
                .isEqualTo(networkId);

            assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
                .isNotNull()
                .extracting(KafkaProtoMessage::getPayload)
                .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
                .matches(
                    a ->
                        a.getStep()
                            .equals(ApiFlowNames.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK));
          }
        }

        @Nested
        class GivenSomeNetworkVenuesExist {
          private List<NetworkVenue> networkVenues;

          @BeforeEach
          void beforeEach(Tenant tenant, Network network) {
            networkVenues =
                Stream.generate(() -> VenueTestFixture.randomVenue(tenant))
                    .limit(3)
                    .peek(
                        venue -> repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId()))
                    .map(venue -> NetworkVenueTestFixture.randomNetworkVenue(network, venue))
                    .peek(
                        networkVenue ->
                            repositoryUtil.createOrUpdate(
                                networkVenue, tenant.getId(), randomTxId()))
                    .toList();
          }

          @Test
          void thenActivateAndSendingDDCCM(Tenant tenant) {
            messageUtil.sendWifiCfgRequest(
                tenant.getId(),
                txCtxExtension.getRequestId(),
                CfgAction.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK,
                randomName(),
                requestParams(),
                "");

            assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
                .isNotNull()
                .extracting(
                    m -> m.getPayload().getOperationsList(),
                    InstanceOfAssertFactories.list(Operation.class))
                .isNotEmpty()
                .hasSize(networkVenues.size())
                .allSatisfy(
                    operation ->
                        assertThat(operation)
                            .matches(o -> o.getAction() == Action.MODIFY)
                            .extracting(Operation::getWlanVenue)
                            .extracting(WlanVenue::getAppPolicyId)
                            .extracting(StringValue::getValue)
                            .isEqualTo(applicationPolicyId))
                .extracting(Operation::getId)
                .allMatch(id -> networkVenues.stream().anyMatch(nv -> id.equals(nv.getId())));

            assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
                .isNotNull()
                .extracting(KafkaProtoMessage::getPayload)
                .extracting(
                    ViewmodelCollector::getOperationsList,
                    InstanceOfAssertFactories.list(Operations.class))
                .hasSize(1)
                .first()
                .matches(o -> o.getOpType() == OpType.MOD)
                .matches(o -> o.getId().equals(applicationPolicyId))
                .extracting(
                    Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
                .extractingByKey(EsConstants.Key.NETWORK_IDS)
                .isNotNull()
                .extracting(Value::getListValue)
                .extracting(ListValue::getValuesList, InstanceOfAssertFactories.list(Value.class))
                .hasSize(1)
                .first()
                .extracting(Value::getStringValue)
                .isEqualTo(networkId);

            assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
                .isNotNull()
                .extracting(KafkaProtoMessage::getPayload)
                .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
                .matches(
                    a ->
                        a.getStep()
                            .equals(ApiFlowNames.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK));
          }

          @Nested
          class GivenHasAccessControlProfileEnabled {
            @BeforeEach
            void beforeEach(
                Tenant tenant, Network network, AccessControlProfile accessControlProfile) {
              final var advancedCustomization = network.getWlan().getAdvancedCustomization();
              advancedCustomization.setAccessControlEnable(true);
              advancedCustomization.setAccessControlProfile(accessControlProfile);
              advancedCustomization.setRespectiveAccessControl(false);
              repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
            }

            @Test
            void thenAlsoClearAccessControlProfile(
                Tenant tenant, AccessControlProfile accessControlProfile) {
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK,
                  randomName(),
                  requestParams(),
                  "");

              assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
                  .isNotNull()
                  .extracting(
                      m -> m.getPayload().getOperationsList(),
                      InstanceOfAssertFactories.list(Operation.class))
                  .isNotEmpty()
                  .hasSize(networkVenues.size())
                  .allSatisfy(
                      operation ->
                          assertThat(operation)
                              .matches(o -> o.getAction() == Action.MODIFY)
                              .extracting(Operation::getWlanVenue)
                              .matches(wlanVenue -> !wlanVenue.hasFirewallProfileId())
                              .extracting(WlanVenue::getAppPolicyId)
                              .extracting(StringValue::getValue)
                              .isEqualTo(applicationPolicyId))
                  .extracting(Operation::getId)
                  .allMatch(id -> networkVenues.stream().anyMatch(nv -> id.equals(nv.getId())));

              assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
                  .isNotNull()
                  .extracting(KafkaProtoMessage::getPayload)
                  .extracting(
                      ViewmodelCollector::getOperationsList,
                      InstanceOfAssertFactories.list(Operations.class))
                  .hasSize(2)
                  .allMatch(o -> o.getOpType() == OpType.MOD)
                  .anySatisfy(
                      operation ->
                          assertThat(operation)
                              .matches(o -> o.getId().equals(accessControlProfile.getId())))
                  .anySatisfy(
                      operation ->
                          assertThat(operation)
                              .matches(o -> o.getId().equals(applicationPolicyId))
                              .extracting(
                                  Operations::getDocMap,
                                  InstanceOfAssertFactories.map(String.class, Value.class))
                              .extractingByKey(EsConstants.Key.NETWORK_IDS)
                              .isNotNull()
                              .extracting(Value::getListValue)
                              .extracting(
                                  ListValue::getValuesList,
                                  InstanceOfAssertFactories.list(Value.class))
                              .hasSize(1)
                              .first()
                              .extracting(Value::getStringValue)
                              .isEqualTo(networkId));

              assertThat(
                      messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
                  .isNotNull()
                  .extracting(KafkaProtoMessage::getPayload)
                  .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
                  .matches(
                      a ->
                          a.getStep()
                              .equals(ApiFlowNames.ACTIVATE_APPLICATION_POLICY_ON_WIFI_NETWORK));
            }
          }
        }
      }
    }
  }
}
