package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_RADSEC_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE;
import static com.ruckus.cloud.wifi.utils.FeatureRolesUtils.PROXY_RADSEC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.core.model.Identifiable;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.RadSecOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.repository.EthernetPortProfileRepository;
import com.ruckus.cloud.wifi.repository.Hotspot20IdentityProviderRepository;
import com.ruckus.cloud.wifi.repository.Hotspot20NetworkRepository;
import com.ruckus.cloud.wifi.repository.RadiusRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.template.TemplateManagementService;
import com.ruckus.cloud.wifi.servicemodel.projection.RadiusNetworkQueryProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.BDDMockito;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

@WifiUnitTest
class RadiusServerProfileCmnCfgCollectorOperationBuilderTest {

  @MockBean
  private Hotspot20IdentityProviderRepository hotspot20IdentityProviderRepository;
  @MockBean
  private RadiusRepository radiusRepository;
  @MockBean
  private Hotspot20NetworkRepository hotspot20NetworkRepository;
  @MockBean
  private EthernetPortProfileRepository ethernetPortProfileRepository;
  @MockBean
  private VenueRepository venueRepository;
  @MockBean
  private TemplateManagementService templateManagementService;
  @SpyBean
  private RadiusServerProfileCmnCfgCollectorOperationBuilder radiusServerProfileCmnCfgCollectorOperationBuilder;

  @Tag("EthernetPortProfileTest")
  @Test
  void givenUpdateRadiusWithIdentityProvider(Tenant tenant) {
    Operations.Builder builder = Operations.newBuilder();
    Radius radius = Generators.radiusAuth().setTenant(always(tenant)).generate();

    List<Identifiable> identityProviderIds = defaultIdGenerator().toListGenerator(3).generate()
        .stream().map(id -> BDDMockito.given(mock(Identifiable.class).getId()).willReturn(id).<Identifiable>getMock()).toList();

    Mockito.when(hotspot20IdentityProviderRepository.findByTenantIdAndAuthRadiusId(
        eq(tenant.getId()), eq(radius.getId())))
        .thenReturn(identityProviderIds);

    EthernetPortProfile authEthernetPortProfile = Generators.ethernetPortProfile()
        .setTenant(always(tenant)).generate();
    EthernetPortProfile acctEthernetPortProfile = Generators.ethernetPortProfile()
        .setTenant(always(tenant)).generate();
    Mockito.when(ethernetPortProfileRepository.findByTenantIdAndAuthRadiusId(
            eq(tenant.getId()), eq(radius.getId())))
        .thenReturn(List.of(authEthernetPortProfile));
    Mockito.when(ethernetPortProfileRepository.findByTenantIdAndAccountingRadiusId(
            eq(tenant.getId()), eq(radius.getId())))
        .thenReturn(List.of(acctEthernetPortProfile));
    Set<String> expectedApLanPortProfileIds = Set.of(authEthernetPortProfile)
        .stream().map(ApLanPortProfile::getId).collect(Collectors.toSet());

    Page<RadiusNetworkQueryProjection> mockPage = mockRadiusNetworkQueryProjections();
    Mockito.when(radiusRepository.findRadiusNetworkDataByRadiusIdAndTenantId(
        any(Pageable.class), eq(radius.getId()), eq(tenant.getId()), any()))
            .thenReturn(mockPage);

    List<Identifiable> mockHotspot20NetworkIdIdentifiableList = List.of(
        BDDMockito.given(mock(Identifiable.class).getId()).willReturn("networkId-2").<Identifiable>getMock(),
        BDDMockito.given(mock(Identifiable.class).getId()).willReturn("networkId-3").<Identifiable>getMock()
    );
    Mockito.when(hotspot20NetworkRepository
        .findDistinctByTenantIdAndHotspot20SettingsIdentityProvidersIdIn(
            eq(tenant.getId()), anyList()))
        .thenReturn(mockHotspot20NetworkIdIdentifiableList);

    radiusServerProfileCmnCfgCollectorOperationBuilder.config(builder, radius, EntityAction.MODIFY);
    Map<String, Value> docMap = builder.build().getDocMap();

    Set<String> expectedIdentityProviderIds = identityProviderIds.stream().map(Identifiable::getId).collect(Collectors.toSet());
    Set<String> expectedNetworkIds = Set.of("networkId-1", "networkId-2", "networkId-3");

    assertThat(docMap.get(Key.HOTSPOT20_IDENTITY_PROVIDER_IDS))
        .matches(doc -> doc.getKindCase() == KindCase.LIST_VALUE)
        .extracting(p -> p.getListValue().getValuesList().stream().map(Value::getStringValue).collect(Collectors.toSet()))
        .isEqualTo(expectedIdentityProviderIds);
    assertThat(docMap.get(Key.NETWORK_IDS))
        .matches(doc -> doc.getKindCase() == KindCase.LIST_VALUE)
        .extracting(p -> p.getListValue().getValuesList().stream().map(Value::getStringValue).collect(Collectors.toSet()))
        .isEqualTo(expectedNetworkIds);
    assertThat(docMap.get(Key.ETHERNET_PORT_PROFILE_IDS))
        .matches(doc -> doc.getKindCase() == KindCase.LIST_VALUE)
        .extracting(p -> p.getListValue().getValuesList().stream().map(Value::getStringValue)
            .collect(Collectors.toSet()))
        .isEqualTo(expectedApLanPortProfileIds);
  }

  @FeatureFlag(enable = WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE)
  @Test
  void givenUpdateAuthRadiusWithVenues(Tenant tenant) {
    Operations.Builder builder = Operations.newBuilder();
    Radius radius = Generators.radiusAuth().setTenant(always(tenant)).generate();

    Venue venue1 = Generators.venue().setTenant(always(tenant)).generate();
    Venue venue2 = Generators.venue().setTenant(always(tenant)).generate();

    Mockito.when(venueRepository.findByTenantIdAndAuthRadiusId(eq(tenant.getId()), eq(radius.getId())))
        .thenReturn(List.of(venue1, venue2));

    Set<String> expectedVenueIds = Set.of(venue1, venue2)
        .stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());

    radiusServerProfileCmnCfgCollectorOperationBuilder.config(builder, radius, EntityAction.MODIFY);
    Map<String, Value> docMap = builder.build().getDocMap();

    assertThat(docMap.get(Key.VENUE_IDS))
        .matches(doc -> doc.getKindCase() == KindCase.LIST_VALUE)
        .extracting(p -> p.getListValue().getValuesList().stream()
            .map(Value::getStringValue).collect(Collectors.toSet()))
        .isEqualTo(expectedVenueIds);
  }

  @FeatureFlag(disable = WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE)
  @Test
  void givenUpdateAuthRadiusWithVenues_noFF(Tenant tenant) {
    Operations.Builder builder = Operations.newBuilder();
    Radius radius = Generators.radiusAuth().setTenant(always(tenant)).generate();

    radiusServerProfileCmnCfgCollectorOperationBuilder.config(builder, radius, EntityAction.MODIFY);
    Map<String, Value> docMap = builder.build().getDocMap();

    assertThat(docMap.get(Key.VENUE_IDS)).isNull();
  }

  @FeatureFlag(enable = WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE)
  @Test
  void givenUpdateAccountingRadiusWithVenues(Tenant tenant) {
    Operations.Builder builder = Operations.newBuilder();
    Radius radius = Generators.radiusAcct().setTenant(always(tenant)).generate();

    Venue venue1 = Generators.venue().setTenant(always(tenant)).generate();
    Venue venue2 = Generators.venue().setTenant(always(tenant)).generate();

    Mockito.when(venueRepository.findByTenantIdAndAccountingRadiusId(eq(tenant.getId()), eq(radius.getId())))
        .thenReturn(List.of(venue1, venue2));

    Set<String> expectedVenueIds = Set.of(venue1, venue2)
        .stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());

    radiusServerProfileCmnCfgCollectorOperationBuilder.config(builder, radius, EntityAction.MODIFY);
    Map<String, Value> docMap = builder.build().getDocMap();

    assertThat(docMap.get(Key.VENUE_IDS))
        .matches(doc -> doc.getKindCase() == KindCase.LIST_VALUE)
        .extracting(p -> p.getListValue().getValuesList().stream()
            .map(Value::getStringValue).collect(Collectors.toSet()))
        .isEqualTo(expectedVenueIds);
  }

  @Test
  void givenUpdateAccountingRadiusWithVenues_noFF(Tenant tenant) {
    Operations.Builder builder = Operations.newBuilder();
    Radius radius = Generators.radiusAcct().setTenant(always(tenant)).generate();

    radiusServerProfileCmnCfgCollectorOperationBuilder.config(builder, radius, EntityAction.MODIFY);
    Map<String, Value> docMap = builder.build().getDocMap();

    assertThat(docMap.get(Key.VENUE_IDS)).isNull();
  }

  @Test
  @FeatureFlag(enable = WIFI_RADSEC_TOGGLE)
  @FeatureRole(PROXY_RADSEC)
  void updateRadiusWithRadSec(Tenant tenant) {
    // Given
    boolean tlsEnabled = true;
    String caId = "ca_id";
    String clientCertId = "client_cert_id";
    String serverCertId = "server_cert_id";
    String cnSanIdentity = "cn_san_identity";
    String ocspUrl = "http://ocsp.commscope.com";
    Radius radius = Generators.radiusAuth().setTenant(always(tenant)).generate();
    RadSecOptions radSecOptions = new RadSecOptions();
    radSecOptions.setTlsEnabled(tlsEnabled);
    radSecOptions.setCertificateAuthorityId(caId);
    radSecOptions.setClientCertificateId(clientCertId);
    radSecOptions.setServerCertificateId(serverCertId);
    radSecOptions.setCnSanIdentity(cnSanIdentity);
    radSecOptions.setOcspUrl(ocspUrl);
    radius.setRadSecOptions(radSecOptions);

    // When
    Operations.Builder builder = Operations.newBuilder();
    radiusServerProfileCmnCfgCollectorOperationBuilder.config(builder, radius, EntityAction.MODIFY);
    Map<String, Value> docMap = builder.build().getDocMap();

    // Then
    assertThat(docMap.get(Key.RADSEC_OPTIONS))
        .matches(doc -> doc.getKindCase() == KindCase.STRUCT_VALUE)
        .extracting(Value::getStructValue)
        .matches(struct -> struct.getFieldsCount() == 6)
        .extracting(Struct::getFieldsMap)
        .satisfies(radSecMap -> assertSoftly(softly -> {
          softly.assertThat(radSecMap.get(Key.TLS_ENABLED).getBoolValue()).isEqualTo(tlsEnabled);
          softly.assertThat(radSecMap.get(Key.CA_ID).getStringValue()).isEqualTo(caId);
          softly.assertThat(radSecMap.get(Key.CLIENT_CERT_ID).getStringValue()).isEqualTo(clientCertId);
          softly.assertThat(radSecMap.get(Key.SERVER_CERT_ID).getStringValue()).isEqualTo(serverCertId);
          softly.assertThat(radSecMap.get(Key.CN_SAN_IDENTITY).getStringValue()).isEqualTo(cnSanIdentity);
          softly.assertThat(radSecMap.get(Key.OCSP_URL).getStringValue()).isEqualTo(ocspUrl);
        }));
  }

  @Test
  @FeatureFlag(enable = WIFI_RADSEC_TOGGLE)
  @FeatureRole(PROXY_RADSEC)
  void updateRadiusWithRadSec_CertAuthorityOnly(Tenant tenant) {
    // Given
    boolean tlsEnabled = true;
    String caId = "ca_id";
    Radius radius = Generators.radiusAuth().setTenant(always(tenant)).generate();
    RadSecOptions radSecOptions = new RadSecOptions();
    radSecOptions.setTlsEnabled(tlsEnabled);
    radSecOptions.setCertificateAuthorityId(caId);
    radius.setRadSecOptions(radSecOptions);

    // When
    Operations.Builder builder = Operations.newBuilder();
    radiusServerProfileCmnCfgCollectorOperationBuilder.config(builder, radius, EntityAction.MODIFY);
    Map<String, Value> docMap = builder.build().getDocMap();

    // Then
    assertThat(docMap.get(Key.RADSEC_OPTIONS))
        .matches(doc -> doc.getKindCase() == KindCase.STRUCT_VALUE)
        .extracting(Value::getStructValue)
        .matches(struct -> struct.getFieldsCount() == 6)
        .extracting(Struct::getFieldsMap)
        .satisfies(radSecMap -> assertSoftly(softly -> {
          softly.assertThat(radSecMap.get(Key.TLS_ENABLED).getBoolValue()).isEqualTo(tlsEnabled);
          softly.assertThat(radSecMap.get(Key.CA_ID).getStringValue()).isEqualTo(caId);
          softly.assertThat(radSecMap.get(Key.CLIENT_CERT_ID))
              .extracting(Value::getNullValue)
              .isEqualTo(NullValue.NULL_VALUE);
          softly.assertThat(radSecMap.get(Key.SERVER_CERT_ID))
              .extracting(Value::getNullValue)
              .isEqualTo(NullValue.NULL_VALUE);
          softly.assertThat(radSecMap.get(Key.CN_SAN_IDENTITY))
              .extracting(Value::getNullValue)
              .isEqualTo(NullValue.NULL_VALUE);
          softly.assertThat(radSecMap.get(Key.OCSP_URL))
              .extracting(Value::getNullValue)
              .isEqualTo(NullValue.NULL_VALUE);
        }));
  }

  private Page<RadiusNetworkQueryProjection> mockRadiusNetworkQueryProjections() {
    RadiusNetworkQueryProjection radiusNetworkQueryProjection1 = mock(RadiusNetworkQueryProjection.class);
    doReturn("networkId-1").when(radiusNetworkQueryProjection1).getNetworkId();
    doReturn("aaaNetwork").when(radiusNetworkQueryProjection1).getNetworkName();
    doReturn(NetworkTypeEnum.AAA).when(radiusNetworkQueryProjection1).getNetworkType();

    RadiusNetworkQueryProjection radiusNetworkQueryProjection2 = mock(RadiusNetworkQueryProjection.class);
    doReturn("networkId-2").when(radiusNetworkQueryProjection2).getNetworkId();
    doReturn("guestNetwork").when(radiusNetworkQueryProjection2).getNetworkName();
    doReturn(NetworkTypeEnum.GUEST).when(radiusNetworkQueryProjection2).getNetworkType();
    doReturn(GuestNetworkTypeEnum.GuestPass).when(radiusNetworkQueryProjection2).getGuestNetworkType();

    List<RadiusNetworkQueryProjection> radiusNetworkQueryProjections = List.of(
        radiusNetworkQueryProjection1, radiusNetworkQueryProjection2
    );

    Page<RadiusNetworkQueryProjection> page = mock(Page.class);
    Mockito.when(page.getContent()).thenReturn(radiusNetworkQueryProjections);
    Mockito.when(page.getTotalElements()).thenReturn(2L);
    Mockito.when(page.getTotalPages()).thenReturn(2);
    return page;
  }

}
