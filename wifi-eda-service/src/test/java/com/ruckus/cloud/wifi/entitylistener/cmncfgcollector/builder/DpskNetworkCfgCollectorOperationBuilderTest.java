package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.conf.WifiBaseEntityFieldNames;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.AbstractNetworkCfgCollectorOperationBuilderTest.MockVenueApGroupsProjection;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.repository.DpskNetworkRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.template.TemplateManagementService;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApGroupsProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@WifiUnitTest
public class DpskNetworkCfgCollectorOperationBuilderTest {

  @MockBean
  private RepositoryUtil repositoryUtil;
  @MockBean
  private DpskNetworkRepository dpskNetworkRepository;
  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private TemplateManagementService templateManagementService;
  @SpyBean
  private DpskNetworkCfgCollectorOperationBuilder dpskNetworkCfgCollectorOperationBuilder;
  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  public void testGetEntityClass() {
    assertThat(new DpskNetworkCfgCollectorOperationBuilder().entityClass())
        .isEqualTo(DpskNetwork.class);
  }

  @Nested
  class testBuildConfig {

    final String apSerialNumber = "123456789012";

    @Test
    public void givenEntityActionIsDelete() {
      Operations operations = new DpskNetworkCfgCollectorOperationBuilder()
          .build(new TxEntity<>(new DpskNetwork(randomId()), EntityAction.DELETE), emptyTxChanges())
          .get(0);

      assertThat(operations.getDocCount())
          .isZero();
    }

    @Test
    public void givenAddDpskNetwork() {
      Operations.Builder builder = Operations.newBuilder();
      var tenant = new Tenant(txCtxExtension.getTenantId());
      final var venueId = randomId();
      final var apGroupId = randomId();
      final var dpskNetwork = (DpskNetwork) network(DpskNetwork.class).generate();
      dpskNetwork.setName("dpskNetwork");
      dpskNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
      dpskNetwork.setTenant(tenant);
      dpskNetwork.setCreatedDate(new Date());
      dpskNetwork.setUpdatedDate(new Date());
      dpskNetwork.getWlan().setNetwork(dpskNetwork);
      repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());

      var venueApGroupsQueryProjections = new ArrayList<>();
      venueApGroupsQueryProjections.add(
          new MockVenueApGroupsProjection(
              venueId, apGroupId, true));

      doReturn(new PageImpl<>(venueApGroupsQueryProjections), Page.empty()).when(networkRepository)
          .findVenueApGroupsByNetworkId(
              eq(txCtxExtension.getTenantId()), eq(dpskNetwork.getId()), any());

      dpskNetworkCfgCollectorOperationBuilder.config(builder, dpskNetwork,
          EntityAction.ADD);
      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(Key.ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dpskNetwork.getId());
      assertThat(docMap.get(Key.TYPE))
          .isEqualTo(EsConstants.Value.TYPE_NETWORK);
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dpskNetwork.getTenant().getId());
      assertThat(docMap.get(Key.NAME))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dpskNetwork.getName());
      assertThat(docMap.get(Key.NW_SUB_TYPE))
          .extracting(p -> p.getStringValue())
          .isEqualTo("dpsk");
      assertThat(docMap.get(Key.SECURITY_PROTOCOL))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dpskNetwork.getWlan().getWlanSecurity().name());
      assertThat(docMap.get(Key.DSAE_ONBOARD_NETWORK).getStructValue()
          .getFieldsMap()).isEmpty();
      assertThat(docMap.get(Key.VENUE_APGROUPS))
          .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
          .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
          .allSatisfy(vap -> {
            assertEquals(venueId, vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue());
            assertEquals(true,
                vap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertEquals(1,
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesCount());
            assertTrue(
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesList()
                    .contains(ValueUtils.stringValue(apGroupId)));
          });
    }

    @Test
    public void givenAddDsaeNetwork() {
      Operations.Builder builder = Operations.newBuilder();
      var tenant = new Tenant(txCtxExtension.getTenantId());
      final var venueId = randomId();
      final var apGroupId = randomId();
      final var dsaeNetwork1 = (DpskNetwork) network(DpskNetwork.class).generate();
      dsaeNetwork1.setName("dsaeNetwork1");
      dsaeNetwork1.setDsaeNetworkPairId(dsaeNetwork1.getId());
      dsaeNetwork1.setIsDsaeServiceNetwork(true);
      dsaeNetwork1.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
      dsaeNetwork1.setTenant(tenant);
      dsaeNetwork1.setCreatedDate(new Date());
      dsaeNetwork1.setUpdatedDate(new Date());
      var vlanPool = new VlanPool();
      vlanPool.setId("vlanPoo-id");
      vlanPool.setName("vlanPool-name");
      vlanPool.setVlanMembers(List.of("2-20"));
      dsaeNetwork1.getWlan().getAdvancedCustomization().setVlanPool(vlanPool);
      dsaeNetwork1.getWlan().setNetwork(dsaeNetwork1);
      repositoryUtil.createOrUpdate(dsaeNetwork1, tenant.getId(), randomTxId());

      final var dsaeNetwork2 = (DpskNetwork) network(DpskNetwork.class).generate();
      dsaeNetwork2.setName("dsaeNetwork2");
      dsaeNetwork2.setDsaeNetworkPairId(dsaeNetwork1.getId());
      dsaeNetwork2.setIsDsaeServiceNetwork(false);
      dsaeNetwork2.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
      dsaeNetwork2.setTenant(tenant);
      dsaeNetwork2.getWlan().setNetwork(dsaeNetwork2);
      repositoryUtil.createOrUpdate(dsaeNetwork2, tenant.getId(), randomTxId());

      when(dpskNetworkRepository.findByDsaeNetworkPairIdAndTenantIdAndIsDsaeServiceNetwork(
          dsaeNetwork1.getDsaeNetworkPairId(), tenant.getId(), false))
          .thenReturn(dsaeNetwork2);

      var venueApGroupsQueryProjections = new ArrayList<>();
      venueApGroupsQueryProjections.add(
          new MockVenueApGroupsProjection(
              venueId, apGroupId, true));

      doReturn(new PageImpl<>(venueApGroupsQueryProjections), Page.empty()).when(networkRepository)
          .findVenueApGroupsByNetworkId(
              eq(txCtxExtension.getTenantId()), eq(dsaeNetwork1.getId()), any());

      dpskNetworkCfgCollectorOperationBuilder.config(builder, dsaeNetwork1,
          EntityAction.ADD);
      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(Key.ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork1.getId());
      assertThat(docMap.get(Key.TYPE))
          .isEqualTo(EsConstants.Value.TYPE_NETWORK);
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork1.getTenant().getId());
      assertThat(docMap.get(Key.NAME))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork1.getName());
      assertThat(docMap.get(Key.NW_SUB_TYPE))
          .extracting(p -> p.getStringValue())
          .isEqualTo("dpsk");
      assertThat(docMap.get(Key.SECURITY_PROTOCOL))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork1.getWlan().getWlanSecurity().name());
      Map<String, Value> vlanPoolDocMap = docMap.get(Key.VLAN_POOL).getStructValue()
          .getFieldsMap();
      assertThat(vlanPoolDocMap.get(Key.NAME))
          .extracting(p -> p.getStringValue())
          .isEqualTo(vlanPool.getName());
      assertThat(vlanPoolDocMap.get(Key.VLAN_MEMBERS))
          .extracting(p -> p.getListValue().getValues(0).getStringValue())
          .isEqualTo(vlanPool.getVlanMembers().get(0));
      assertThat(docMap.get(Key.VENUE_APGROUPS))
          .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
          .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
          .allSatisfy(vap -> {
            assertEquals(venueId, vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue());
            assertEquals(true,
                vap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertEquals(1,
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesCount());
            assertTrue(
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesList()
                    .contains(ValueUtils.stringValue(apGroupId)));
          });

      Map<String, Value> onboardDocMap = docMap.get(Key.DSAE_ONBOARD_NETWORK).getStructValue()
          .getFieldsMap();
      assertThat(onboardDocMap.get(Key.ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork2.getId());
      assertThat(onboardDocMap.get(Key.NAME))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork2.getName());
      assertThat(onboardDocMap.get(Key.NW_SUB_TYPE))
          .extracting(p -> p.getStringValue())
          .isEqualTo("dpsk");
      assertThat(onboardDocMap.get(Key.SECURITY_PROTOCOL))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork2.getWlan().getWlanSecurity().name());
      Map<String, Value> onboardVlanPoolDocMap = onboardDocMap.get(Key.VLAN_POOL).getStructValue()
          .getFieldsMap();
      assertThat(onboardVlanPoolDocMap.get(Key.NAME))
          .extracting(p -> p.getStringValue())
          .isEqualTo(vlanPool.getName());
      assertThat(onboardVlanPoolDocMap.get(Key.VLAN_MEMBERS))
          .extracting(p -> p.getListValue().getValues(0).getStringValue())
          .isEqualTo(vlanPool.getVlanMembers().get(0));
    }

    @Test
    public void givenUpdateDsaeToDpskNetwork() {
      Operations.Builder builder = Operations.newBuilder();
      var tenant = new Tenant(txCtxExtension.getTenantId());
      final var venueId = randomId();
      final var apGroupId = randomId();
      final var dsaeNetwork1 = (DpskNetwork) network(DpskNetwork.class).generate();
      dsaeNetwork1.setName("dsaeNetwork");
      dsaeNetwork1.setDsaeNetworkPairId(dsaeNetwork1.getId());
      dsaeNetwork1.setIsDsaeServiceNetwork(true);
      dsaeNetwork1.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
      dsaeNetwork1.setTenant(tenant);
      dsaeNetwork1.setCreatedDate(new Date());
      dsaeNetwork1.setUpdatedDate(new Date());
      dsaeNetwork1.getWlan().setNetwork(dsaeNetwork1);
      repositoryUtil.createOrUpdate(dsaeNetwork1, tenant.getId(), randomTxId());

      dsaeNetwork1.setName("dpskNetwork");
      dsaeNetwork1.setDsaeNetworkPairId(null);
      dsaeNetwork1.setIsDsaeServiceNetwork(false);
      dsaeNetwork1.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
      repositoryUtil.createOrUpdate(dsaeNetwork1, tenant.getId(), randomTxId());

      var venueApGroupsProjections = new ArrayList<>();
      venueApGroupsProjections.add(
          new MockVenueApGroupsProjection(
              venueId, apGroupId, true));

      doReturn(new PageImpl<>(venueApGroupsProjections), Page.empty()).when(networkRepository)
          .findVenueApGroupsByNetworkId(
              eq(txCtxExtension.getTenantId()), eq(dsaeNetwork1.getId()), any());

      dpskNetworkCfgCollectorOperationBuilder.config(builder, dsaeNetwork1,
          EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(Key.ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork1.getId());
      assertThat(docMap.get(Key.TYPE))
          .isEqualTo(EsConstants.Value.TYPE_NETWORK);
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork1.getTenant().getId());
      assertThat(docMap.get(Key.NAME))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork1.getName());
      assertThat(docMap.get(Key.NW_SUB_TYPE))
          .extracting(p -> p.getStringValue())
          .isEqualTo("dpsk");
      assertThat(docMap.get(Key.SECURITY_PROTOCOL))
          .extracting(p -> p.getStringValue())
          .isEqualTo(dsaeNetwork1.getWlan().getWlanSecurity().name());
      assertThat(docMap.get(Key.VENUE_APGROUPS))
          .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
          .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
          .allSatisfy(vap -> {
            assertEquals(venueId, vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue());
            assertEquals(true,
                vap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertEquals(1,
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesCount());
            assertTrue(
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesList()
                    .contains(ValueUtils.stringValue(apGroupId)));
          });
      assertThat(docMap.get(Key.DSAE_ONBOARD_NETWORK).getStructValue()
          .getFieldsMap()).isEmpty();
    }

    @Nested
    class testHasModification {

      @Test
      public void givenEntityActionIsNotModify() {
        assertThat(
            new DpskNetworkCfgCollectorOperationBuilder().hasModification(EntityAction.ADD,
                Collections.emptySet(), false))
            .isTrue();
      }

      @Test
      public void givenGivenFieldsInExportedFields() {
        var builder = new DpskNetworkCfgCollectorOperationBuilder();
        builder.setBaseEntityFieldNames(new WifiBaseEntityFieldNames());
        assertThat(builder.hasModification(EntityAction.MODIFY,
            Set.of("name", "updatedDate"), false))
            .isTrue();
      }

      @Test
      public void givenGivenFieldsNotInExportedFields() {
        var builder = new DpskNetworkCfgCollectorOperationBuilder();
        builder.setBaseEntityFieldNames(new WifiBaseEntityFieldNames());
        assertThat(builder.hasModification(EntityAction.MODIFY,
            Set.of("updatedDate"), false))
            .isFalse();
      }
    }

    @Data
    @AllArgsConstructor
    static class VenueApGroupsApSerialNumbersProjectionIml implements
        VenueApGroupsProjection {
      String venueId;
      String apGroups;
      boolean isAllApGroups;
      String apSerialNumbers;

      @Override
      public boolean getIsAllApGroups() {
        return true;
      }
    }
  }
}
