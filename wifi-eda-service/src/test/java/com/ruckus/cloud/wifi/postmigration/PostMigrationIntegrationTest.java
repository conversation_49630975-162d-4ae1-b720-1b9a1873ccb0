package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.acx.service.postmigration.util.PostMigrationUtils.createPostMigrationTask;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;

import com.google.common.base.Preconditions;
import com.ruckus.acx.service.postmigration.kafka.PostMigrationConsumer;
import com.ruckus.acx.service.postmigration.kafka.PostMigrationKafkaProducer;
import com.ruckus.acx.service.postmigration.repository.PostMigrationRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import java.sql.Connection;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class PostMigrationIntegrationTest {

  @Autowired
  PostMigrationRepository repository;
  @Autowired
  V0_0__TestPostMigrationConsumer postMigrationConsumer;
  @Autowired
  DataSource dataSource;
  @Autowired
  PostMigrationKafkaProducer postMigrationKafkaProducer;

  @Test
  public void postMigrationKafkaConsumer() throws Exception {
    log.info("start postMigrationKafkaConsumer");

    try (Connection conn = dataSource.getConnection()) {
      extractPostMigrationName(postMigrationConsumer);
      createPostMigrationTask(conn, extractPostMigrationName(postMigrationConsumer).orElseThrow());
    }

    postMigrationKafkaProducer.pull();

    //wait to pull and consume
    await().atMost(30, SECONDS).until(() -> isAllConsumersCompleted());
  }

  private boolean isAllConsumersCompleted() {
    if (!postMigrationConsumer.isCompleted()) {
      return false;
    }
    return true;
  }

  private Optional<String> extractPostMigrationName(PostMigrationConsumer postMigrationConsumer) {
    String pattern = "(V[\\d]*_[\\d]*)__(\\S*)PostMigrationConsumer";
    Pattern regex = Pattern.compile(pattern);
    Matcher matcher = regex.matcher(postMigrationConsumer.getClass().getSimpleName());
    Preconditions.checkState(matcher.matches(),
        "Invalid PostMigrationConsumer name:" + postMigrationConsumer.getClass().getSimpleName());
    final var version = matcher.group(1);
    final var name = matcher.group(2);
    if (StringUtils.isEmpty(name)) {
      return Optional.empty();
    }
    return Optional.of(version + "__ACX_XXXXX_PostMigration_" + name);
  }
}
