package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('633b7de0bc1545b7ac5f8396721a0668');
    INSERT INTO directory_server_profile (id, name, tenant) VALUES (
      '1d447865e50447e58005a7a972b71ed0',
      'profile1',
      '633b7de0bc1545b7ac5f8396721a0668');
    INSERT INTO directory_server_profile (id, name, tenant) VALUES (
      '2d447865e50447e58005a7a972b71ed2',
      'profile2',
      '633b7de0bc1545b7ac5f8396721a0668');
    """)
public class DirectoryServerProfileRepositoryTest {

  @Autowired
  private DirectoryServerProfileRepository target;

  @Test
  void testExistsByTenantIdAndNameAndIdNot() {
    var result = target.existsByTenantIdAndNameAndIdNot("633b7de0bc1545b7ac5f8396721a0668",
        "profile1", "1d447865e50447e58005a7a972b71ed0");
    assertFalse(result);
  }

  @Test
  void testExistsByTenantIdAndNameAndIdNot2() {
    var result = target.existsByTenantIdAndNameAndIdNot("633b7de0bc1545b7ac5f8396721a0668",
        "profile2", "1d447865e50447e58005a7a972b71ed0");
    assertTrue(result);
  }


}
