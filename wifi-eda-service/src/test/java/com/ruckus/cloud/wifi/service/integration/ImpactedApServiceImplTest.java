package com.ruckus.cloud.wifi.service.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.impact.service.ImpactedApService;
import com.ruckus.cloud.wifi.impact.servicemodel.ImpactedAp;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiIntegrationTest
public class ImpactedApServiceImplTest {

  private static final String AP_GROUP_1_ID = "af47211f491a4170bc34259ff594e5d8";
  private static final String AP_GROUP_2_ID = "71a8df5da5a44808b714050b9c943bd2";

  private static final String NETWORK_1_ID = "39fc80a4bf0b43bcbf61e018f0ea264d";
  private static final String NETWORK_2_ID = "113f0bf9986041e3a4ed291bb4cfbb71";
  private static final String NETWORK_3_ID = "1262325a067f4eefbb4ec44cd12c2995";

  private static final String VENUE_1_ID = "71ad469e254e4bf9a98e5fbfbbcae8f9";
  private static final String VENUE_2_ID = "a308306a124e43baacf30dd83e5f4c4c";
  private static final String VENUE_3_ID = "e500884ebc0d4cb19d22cf3968f11bc3";

  private static final String DUMMY_AP_1_ID = "999999999911";
  private static final String DUMMY_AP_2_ID = "999999999922";
  private static final String DUMMY_AP_3_ID = "999999999933";
  private static final String DUMMY_AP_4_ID = "999999999944";
  private static final String DUMMY_AP_5_ID = "999999999955";
  private static final String DUMMY_AP_6_ID = "999999999966";
  private static final String DUMMY_AP_7_ID = "999999999977";
  private static final String DUMMY_AP_8_ID = "999999999988";

  @Autowired
  private ImpactedApService impactedApService;


  @Disabled
  @Test
  @Sql("classpath:sql/impacted-ap.sql")
  public void testNotNull() {
    assertNotNull(impactedApService);
  }

  @Test
  @Sql("classpath:sql/impacted-ap.sql")
  public void testFindById() {
    // When
    ImpactedAp impactedAp1 = impactedApService.findById(DUMMY_AP_1_ID);

    // Then
    assertNotNull(impactedAp1);
    assertEquals(DUMMY_AP_1_ID, impactedAp1.getId());

    // When
    ImpactedAp impactedAp7 = impactedApService.findById(DUMMY_AP_7_ID);
    ImpactedAp impactedAp8 = impactedApService.findById(DUMMY_AP_8_ID);

    // Then
    assertNull(impactedAp7);
    assertNull(impactedAp8);
  }

  @Test
  @Sql("classpath:sql/impacted-ap.sql")
  public void testFindByApGroupId() {
    // When
    List<ImpactedAp> impactedAps = impactedApService.findByApGroupId(AP_GROUP_1_ID);

    // Then
    assertNotNull(impactedAps);
    assertFalse(impactedAps.isEmpty());
    assertEquals(2, impactedAps.size());
    assertEquals(2, impactedAps.stream().filter(ap -> DUMMY_AP_1_ID.equals(ap.getId())).count());

    // When
    impactedAps = impactedApService.findByApGroupId(AP_GROUP_2_ID);

    // Then
    assertNotNull(impactedAps);
    assertFalse(impactedAps.isEmpty());
    assertEquals(4, impactedAps.size());
    assertEquals(2, impactedAps.stream().filter(ap -> DUMMY_AP_2_ID.equals(ap.getId())).count());
    assertEquals(2, impactedAps.stream().filter(ap -> DUMMY_AP_3_ID.equals(ap.getId())).count());
  }

  @Test
  @Sql("classpath:sql/impacted-ap.sql")
  public void testFindByNetworkId() {
    // When
    List<ImpactedAp> impactedAps = impactedApService.findByNetworkId(NETWORK_1_ID);

    // Then
    assertNotNull(impactedAps);
    assertFalse(impactedAps.isEmpty());
    assertEquals(5, impactedAps.size());
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_1_ID.equals(ap.getId())));
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_2_ID.equals(ap.getId())));
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_3_ID.equals(ap.getId())));
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_4_ID.equals(ap.getId())));
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_5_ID.equals(ap.getId())));

    // When
    impactedAps = impactedApService.findByNetworkId(NETWORK_2_ID);

    // Then
    assertNotNull(impactedAps);
    assertFalse(impactedAps.isEmpty());
    assertEquals(4, impactedAps.size());
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_1_ID.equals(ap.getId())));
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_2_ID.equals(ap.getId())));
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_3_ID.equals(ap.getId())));
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_5_ID.equals(ap.getId())));

    // When
    impactedAps = impactedApService.findByNetworkId(NETWORK_3_ID);

    // Then
    assertNotNull(impactedAps);
    assertFalse(impactedAps.isEmpty());
    assertEquals(2, impactedAps.size());
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_4_ID.equals(ap.getId())));
    assertTrue(impactedAps.stream().anyMatch(ap -> DUMMY_AP_5_ID.equals(ap.getId())));
  }

  @Test
  @Sql("classpath:sql/impacted-ap.sql")
  public void testFindByVenueId() {
    // When
    List<ImpactedAp> impactedAps = impactedApService.findByVenueId(VENUE_1_ID);

    // Then
    assertNotNull(impactedAps);
    assertFalse(impactedAps.isEmpty());
    assertEquals(8, impactedAps.size());
    assertEquals(2, impactedAps.stream().filter(ap -> DUMMY_AP_1_ID.equals(ap.getId())).count());
    assertEquals(2, impactedAps.stream().filter(ap -> DUMMY_AP_2_ID.equals(ap.getId())).count());
    assertEquals(2, impactedAps.stream().filter(ap -> DUMMY_AP_3_ID.equals(ap.getId())).count());
    assertEquals(2, impactedAps.stream().filter(ap -> DUMMY_AP_4_ID.equals(ap.getId())).count());

    // When
    impactedAps = impactedApService.findByVenueId(VENUE_2_ID);

    // Then
    assertNotNull(impactedAps);
    assertFalse(impactedAps.isEmpty());
    assertEquals(3, impactedAps.size());
    assertEquals(3, impactedAps.stream().filter(ap -> DUMMY_AP_5_ID.equals(ap.getId())).count());

    // When
    impactedAps = impactedApService.findByVenueId(VENUE_3_ID);

    // Then
    assertNotNull(impactedAps);
    assertFalse(impactedAps.isEmpty());
    assertEquals(1, impactedAps.size());
    assertEquals(1, impactedAps.stream().filter(ap -> DUMMY_AP_6_ID.equals(ap.getId())).count());
  }
}
