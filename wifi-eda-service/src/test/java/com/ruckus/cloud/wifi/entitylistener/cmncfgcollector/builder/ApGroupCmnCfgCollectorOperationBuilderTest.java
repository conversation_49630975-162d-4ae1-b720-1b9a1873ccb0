package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.DEVICE_GROUP;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.conf.WifiBaseEntityFieldNames;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup.Fields;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Tag("ApGroupTest")
@WifiUnitTest
class ApGroupCmnCfgCollectorOperationBuilderTest {

  @SpyBean
  private ApGroupCmnCfgCollectorOperationBuilder unit;

  @Test
  void testGetEntityClass() {
    assertThat(unit.entityClass())
        .isEqualTo(ApGroup.class);
  }

  @Nested
  class testBuildConfig {

    @Test
    void givenEntityActionIsDelete() {
      Operations operations = unit.build(
          new TxEntity<>(new ApGroup(randomId()), EntityAction.DELETE), emptyTxChanges()).get(0);

      assertThat(operations.getDocCount())
          .isZero();
    }

    @Test
    void givenAddApGroup() {
      final Ap ap = Generators.ap().generate();
      final ApGroup apGroup = Generators.apGroup()
          .setDescription(randomString())
          .setTenant(Generators.tenant())
          .setVenue(Generators.venue())
          .setAps(list(always(ap), 1))
          .setCreatedDate(always(new Date()))
          .setUpdatedDate(always(new Date()))
          .generate();
      final Network network = Generators.network(OpenNetwork.class).generate();

      final var networkApGroups = Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(Generators.networkVenue()
              .setNetwork(always(network))
              .setVenue(always(apGroup.getVenue()))).generate(1);
      apGroup.setNetworkApGroups(networkApGroups);

      Operations.Builder builder = Operations.newBuilder();

      unit.config(builder, apGroup, EntityAction.ADD);

      assertThat(builder.build().getDocMap())
          .containsEntry(Key.TYPE, EsConstants.Value.DEVICE_GROUP)
          .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(apGroup.getTenant().getId()))
          .containsEntry(Key.ID, ValueUtils.stringValue(apGroup.getId()))
          .containsEntry(Key.NAME, ValueUtils.stringValue(apGroup.getName()))
          .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(apGroup.getVenue().getId()))
          .containsEntry(Key.VENUE_NAME, ValueUtils.stringValue(apGroup.getVenue().getName()))
          .containsEntry(Key.CRT_TIME, ValueUtils.numberValue(apGroup.getCreatedDate().getTime()))
          .containsEntry(Key.LAST_UPD_TIME, ValueUtils.numberValue(apGroup.getUpdatedDate().getTime()))
          .containsEntry(Key.DESCRIPTION, ValueUtils.stringValue(apGroup.getDescription()))
          .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(apGroup.getIsDefault()))
          .containsEntry(Key.WIFI_NETWORK_IDS, ValueUtils.listValue(List.of(ValueUtils.stringValue(network.getId()))));
    }

    @Test
    void testGetIndex() {
      assertThat(unit.index()).isEqualTo(DEVICE_GROUP);
    }

    @Nested
    class testHasModification {

      @Test
      void givenEntityActionIsNotModify() {
        assertThat(unit.hasModification(EntityAction.ADD, Set.of(), false))
            .isTrue();
      }

      @Test
      void givenEntityActionIsModify() {
        assertThat(unit.hasModification(EntityAction.MODIFY, Set.of(), false))
            .isFalse();
      }

      @Test
      void givenEntityActionIsModifyButTaggedDirty() {
        assertThat(unit.hasModification(EntityAction.MODIFY, Set.of(), true))
            .isTrue();
      }

      @Test
      void givenGivenFieldsInExportedFields() {
        unit.setBaseEntityFieldNames(new WifiBaseEntityFieldNames());
        assertThat(unit.hasModification(EntityAction.MODIFY, Set.of(Fields.NAME), false))
            .isTrue();
        assertThat(unit.hasModification(EntityAction.MODIFY,
            Set.of(Fields.NAME, AbstractBaseEntity.Fields.UPDATEDDATE), false))
            .isTrue();
      }

      @Test
      void givenGivenFieldsNotInExportedFields() {
        unit.setBaseEntityFieldNames(new WifiBaseEntityFieldNames());
        assertThat(unit.hasModification(EntityAction.MODIFY,
            Set.of(AbstractBaseEntity.Fields.UPDATEDDATE), false))
            .isFalse();
        assertThat(unit.hasModification(EntityAction.MODIFY, Set.of(Fields.ISDEFAULT), false))
            .isFalse();
      }
    }
  }
}
