package com.ruckus.cloud.wifi.entitylistener.drs;

import static com.ruckus.cloud.wifi.test.fixture.ApTestFixture.randomAp;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.Controller;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.Device;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.DeviceType;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.RequestType;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.kafka.publisher.ApDrsPublisher;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.ConfigDataApplicationContextInitializer;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.ContextConfiguration;

@WifiUnitTest
@ContextConfiguration(initializers = ConfigDataApplicationContextInitializer.class)
public class ApDrsRequestPublisherTest {

  @Value("${ingress-gateway.url}")
  private String ingressGatewayUrl;

  @Autowired
  private ApDrsRequestPublisher requestPublisher;

  @MockBean
  private ApDrsPublisher apDrsPublisher;

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  public void testEntityListenerBuild() throws URISyntaxException {
    final String ingressGatewayFqdn = new URI(ingressGatewayUrl).getHost();
    final Ap ap = randomAp();
    final TxEntity<Ap> txEntity = new TxEntity<>(ap, EntityAction.ADD);

    List<Pair<RequestType, Device>> devices = requestPublisher.build(txEntity, null);

    assertEquals(RequestType.CREATE, devices.get(0).getKey());
    assertEquals(ap.getId(), devices.get(0).getValue().getSerialNumber());
    assertEquals(DeviceType.WIFI, devices.get(0).getValue().getType());
    assertEquals(ingressGatewayFqdn,
        devices.get(0).getValue().getControllers(0).getAddress());
  }

  @Test
  public void testEntityListenerFlushEmptyRequest() {
    List<Pair<RequestType, Device>> aps = new ArrayList<>();

    requestPublisher.flush(aps);

    verify(apDrsPublisher, never()).publish(any());
  }

  @Test
  public void testEntityListenerFlushCreateAP() throws URISyntaxException {
    final String ingressGatewayFqdn = new URI(ingressGatewayUrl).getHost();
    final Ap ap = randomAp();
    final List<Pair<RequestType, Device>> aps = new ArrayList<>();
    aps.add(Pair.of(RequestType.CREATE, Device.newBuilder()
        .setType(DeviceType.WIFI)
        .setSerialNumber(ap.getId())
        .addControllers(Controller.newBuilder()
            .setAddress(ingressGatewayFqdn)
            .setCertKey("")).build()));

    requestPublisher.flush(aps);

    verify(apDrsPublisher).publish(argThat(request -> {
      return request.hasDeviceCreateRequest() &&
          !request.hasDeviceDeleteRequest() &&
          request.getType().equals(RequestType.CREATE) &&
          request.getDeviceCreateRequest().getTenant().equals(txCtxExtension.getTenantId()) &&
          request.getDeviceCreateRequest().getDevicesCount() == aps.size() &&
          request.getDeviceCreateRequest().getDevices(0).getType().equals(DeviceType.WIFI) &&
          request.getDeviceCreateRequest().getDevices(0).getSerialNumber().equals(ap.getId()) &&
          request.getDeviceCreateRequest().getDevices(0).getControllers(0)
          .getAddress().equals(ingressGatewayFqdn);
    }));
  }

  @Test
  public void testEntityListenerFlushDeleteAP() throws URISyntaxException {
    final String ingressGatewayFqdn = new URI(ingressGatewayUrl).getHost();
    final Ap ap = randomAp();
    final List<Pair<RequestType, Device>> aps = new ArrayList<>();
    aps.add(Pair.of(RequestType.DELETE, Device.newBuilder()
        .setType(DeviceType.WIFI)
        .setSerialNumber(ap.getId())
        .addControllers(Controller.newBuilder()
            .setAddress(ingressGatewayFqdn)
            .setCertKey("")).build()));

    requestPublisher.flush(aps);

    verify(apDrsPublisher).publish(argThat(request -> {
      return !request.hasDeviceCreateRequest() &&
          request.hasDeviceDeleteRequest() &&
          request.getType().equals(RequestType.DELETE) &&
          request.getDeviceDeleteRequest().getTenant().equals(txCtxExtension.getTenantId()) &&
          request.getDeviceDeleteRequest().getDeviceCount() == aps.size() &&
          request.getDeviceDeleteRequest().getDevice(0).getType().equals(DeviceType.WIFI) &&
          request.getDeviceDeleteRequest().getDevice(0).getSerialNumber().equals(ap.getId()) &&
          request.getDeviceDeleteRequest().getDevice(0).getControllers(0)
              .getAddress().equals(ingressGatewayFqdn);
    }));
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public ApDrsRequestPublisher testApDrsRequestPublisher(
        @Value("${ingress-gateway.url}") String ingressGatewayUrl,
        ApDrsPublisher apDrsPublisher) throws URISyntaxException {
      return new ApDrsRequestPublisher(
          ingressGatewayUrl,
          apDrsPublisher);
    }
  }
}
