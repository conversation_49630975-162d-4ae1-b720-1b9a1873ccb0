package com.ruckus.cloud.wifi.service.compatibility.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.CompatibilityActivity;
import com.ruckus.cloud.wifi.requirement.feature.ApIotFeature;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class CompatibilityActivityBuilderTest {

  @Mock
  private ApIotFeature apIotFeature;

  @BeforeEach
  public void setup() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class whenActivityBuildByFeature {
    CompatibilityActivityBuilder builder;
    @BeforeEach
    void setUp() {
      // Given
      TxCtxHolder.set(new TxCtx("tenant-id", "request-id", "user-01", "mock-flow"));
      builder = new CompatibilityActivityBuilder("deviceId")
          .withApName("AP Name")
          .withFirmware("Firmware")
          .withModel("Model");
    }

    @Test
    public void testBuild_withAllCompatibleFeatures() {
      // Mocking features to be compatible
      when(apIotFeature.isDeviceSupport(any())).thenReturn(true);
      when(apIotFeature.getFeatureName()).thenReturn("AP IoT");

      // When
      CompatibilityActivity activity = builder.build(apIotFeature);

      // Then
      assertEquals("deviceId", activity.getApId());
      assertEquals("AP Name", activity.getApName());
      assertTrue(activity.getIncompatibleFeatures().isEmpty());
    }

    @Test
    public void testBuild_withSomeIncompatibleFeatures() {
      // Mocking features to have one incompatible feature
      when(apIotFeature.isDeviceSupport(any())).thenReturn(false);
      when(apIotFeature.getFeatureName()).thenReturn("AP IoT");

      // When
      CompatibilityActivity activity = builder.build(apIotFeature);

      // Then
      assertEquals("deviceId", activity.getApId());
      assertEquals("AP Name", activity.getApName());
      assertEquals(1, activity.getIncompatibleFeatures().size());
      assertEquals("AP IoT", activity.getIncompatibleFeatures().get(0));
    }
  }
}
