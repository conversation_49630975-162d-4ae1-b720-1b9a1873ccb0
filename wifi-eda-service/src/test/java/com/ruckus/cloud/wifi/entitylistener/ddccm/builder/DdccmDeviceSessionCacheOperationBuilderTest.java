package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.DeviceSessionCache;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class DdccmDeviceSessionCacheOperationBuilderTest {

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");
  @SpyBean
  private DdccmDeviceSessionCacheOperationBuilder builder;

  @Test
  void testAddDeviceSessionCacheBuilder() {

    DeviceSessionCache deviceSessionCache = Generators.deviceSessionCache()
        .setCoordinators(Generators.deviceSessionCacheCoordinator().toListGenerator(3)).generate();

    List<Operation> operations = builder.build(new NewTxEntity<>(deviceSessionCache),
        emptyTxChanges());

    assertEquals(1, operations.size());
    com.ruckus.acx.ddccm.protobuf.wifi.DeviceSessionCache result = operations.get(0)
        .getDeviceSessionCache();

    assertNotNull(result);
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());
    assertEquals(deviceSessionCache.getContainerId(), result.getVenueId());
    assertEquals(deviceSessionCache.getId(), result.getId());
    assertEquals(deviceSessionCache.getCoordinators().size(), result.getCoordinatorsCount());
    assertThat(deviceSessionCache.getCoordinators())
        .allSatisfy(coordinator -> assertThat(result.getCoordinatorsList())
            .anyMatch(ipMacPair -> ipMacPair.getIp().equals(coordinator.getIp())
                && ipMacPair.getMac().equals(coordinator.getMac())));
  }
}