package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.entitlement.Action;
import com.ruckus.cloud.entitlement.ProtoDeviceType;
import com.ruckus.cloud.entitlement.ProtoRequestSource;
import com.ruckus.cloud.entitlement.operation.eda.EdaOperation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.DeviceType;
import com.ruckus.cloud.protobuf.drs.DeviceRegistrar.RequestType;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanStep;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.client.file.FileDto;
import com.ruckus.cloud.wifi.client.file.ImportFileClient;
import com.ruckus.cloud.wifi.core.error.SimpleError;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.api.rest.ErrorResponse;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.kafka.common.header.Header;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@Slf4j
public class ConsumeImportVenueApsRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private ImportFileClient importFileClient;
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  class whenConsumeImportVenueApsRequest {

    @BeforeEach
    public void setup() {
      clearInvocations(importFileClient);
      when(importFileClient.getDownloadUrl(anyString(), anyString())).thenReturn(
          new FileDto(randomId(), "http://www.download.url"));
    }

    @Test
    void thenShouldDownloadCsvFile_sendActivityFail_WhenFileNotFound()
        throws InvalidProtocolBufferException {
      final var tenantId = randomId();
      final var requestId = randomTxId();
      final var userName = randomName();
      final var fileName = "aps_import.csv";
      final var fileId = randomId();
      final var signUrl = "http://test.signed.url";
      final var fileDto = new FileDto(fileName, fileId, signUrl);
      final var venueId = randomId();
      var tenant = new Tenant(tenantId);
      var venue = new Venue(venueId);
      venue.setTenant(tenant);
      venue.setName("Venue - " + venueId);
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

      var params = new RequestParams();
      params.getPathVariables().put("venueId", venue.getId());
      assertThrows(RuntimeException.class, () -> messageUtil.sendWifiCfgRequest(
          tenantId,
          requestId,
          CfgExtendedAction.IMPORT_VENUE_APS_CSV,
          userName,
          params,
          fileDto));
      validateFileClientAndActivityMessage(tenantId, requestId);
    }

    @Test
    void thenShouldSaveAps(Tenant tenant, Network network) throws IOException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var fileName = "aps_import_with_optional_apgroup.csv";
      final var fileId = randomId();
      final var signUrl = "http://test.signed.url";
      final var fileDto = new FileDto(fileName, fileId, signUrl);
      final var venueId = "875356424903";
      var filePath = String.format("/tmp/%s_aps_import_with_optional_apgroup.csv", requestId);
      var source = new File("./src/test/resources/csv/venue_aps_import_with_optional_apgroup.csv");
      var dest = new File(filePath);
      FileUtils.copyFile(source, dest);
      log.warn("Write tmp file [{}]", filePath);

      var venue = new Venue(venueId);
      venue.setTenant(tenant);
      venue.setName("My Venue id=875356424903");
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

      NetworkVenue networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      var defaultApGroup = ApGroupTestFixture.randomApGroup(venue,
          group -> group.setIsDefault(true));
      repositoryUtil.createOrUpdate(defaultApGroup, tenant.getId(), randomTxId());
      var customApGroup = ApGroupTestFixture.randomApGroup(venue,
          group -> group.setName("New Ap Group"));
      NetworkApGroup networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
          customApGroup);
      NetworkApGroupRadio networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
          networkApGroup);
      repositoryUtil.createOrUpdate(customApGroup, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkApGroup, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId());

      venue.setApGroups(List.of(defaultApGroup, customApGroup));
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

      var params = new RequestParams();
      params.getPathVariables().put("venueId", venue.getId());
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgExtendedAction.IMPORT_VENUE_APS_CSV,
          userName,
          params,
          fileDto);

      validateDatabaseAndKafkaMessage(tenant.getId(), requestId);
      validateCmnCfgCollectorMessage(tenant.getId(), requestId, venueId, customApGroup.getId(),
          defaultApGroup.getId(), networkVenue.getNetwork().getId());
    }

    @Test
    void thenShouldSaveApsWithUtf8BomCsv(Tenant tenant, Network network) throws IOException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var fileName = "utf8_venue_aps_import.csv";
      final var fileId = randomId();
      final var signUrl = "http://test.signed.url";
      final var fileDto = new FileDto(fileName, fileId, signUrl);
      final var venueId = "1234567";
      List<String> apSerials = List.of("123459009912", "123459009913", "123459009914");
      List<String> venueIds = List.of("1234567", "1234567", "1234567");
      var filePath = String.format("/tmp/%s_utf8_venue_aps_import.csv", requestId);
      var source = new File("./src/test/resources/csv/utf8_venue_aps_import.csv");
      var dest = new File(filePath);
      FileUtils.copyFile(source, dest);
      log.warn("Write tmp file [{}]", filePath);

      var venue = new Venue(venueId);
      venue.setTenant(tenant);
      venue.setName("Test Venue=1234567");
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

      NetworkVenue networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      var defaultApGroup = ApGroupTestFixture.randomApGroup(venue,
          group -> group.setIsDefault(true));
      repositoryUtil.createOrUpdate(defaultApGroup, tenant.getId(), randomTxId());

      venue.setApGroups(List.of(defaultApGroup));
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

      var params = new RequestParams();
      params.getPathVariables().put("venueId", venue.getId());
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgExtendedAction.IMPORT_VENUE_APS_CSV,
          userName,
          params,
          fileDto);


      validateDatabaseAndKafkaMessageWithCustomNaming(
        tenant.getId(), requestId, "15-2025-tdc.test-", apSerials, venueIds);
      validateCmnCfgCollectorMessageWithApSerials(tenant.getId(), requestId, venueId, null,
          defaultApGroup.getId(), networkVenue.getNetwork().getId(), apSerials);
    }

    @Test
    void thenShouldSaveApsWithSampleCsv(Tenant tenant, Network network) throws IOException {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var fileName = "new_aps_import_template_with_gps.csv";
      final var fileId = randomId();
      final var signUrl = "http://test.signed.url";
      final var fileDto = new FileDto(fileName, fileId, signUrl);
      final var venueId = "test_id";
      List<String> apSerials = List.of("716253400000", "716253400001", "716253400002");
      List<String> venueIds = List.of("test_id", "test_id", "test_id");
      var filePath = String.format("/tmp/%s_new_aps_import_template_with_gps.csv", requestId);
      var source = new File("./src/test/resources/csv/new_aps_import_template_with_gps.csv");
      var dest = new File(filePath);
      FileUtils.copyFile(source, dest);
      log.warn("Write tmp file [{}]", filePath);

      var venue = new Venue(venueId);
      venue.setTenant(tenant);
      venue.setName("T Venue=test_id");
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

      NetworkVenue networkVenue = NetworkVenueTestFixture.randomNetworkVenue(network, venue);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      var defaultApGroup = ApGroupTestFixture.randomApGroup(venue,
          group -> group.setIsDefault(true));
      repositoryUtil.createOrUpdate(defaultApGroup, tenant.getId(), randomTxId());

      venue.setApGroups(List.of(defaultApGroup));
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

      var params = new RequestParams();
      params.getPathVariables().put("venueId", venue.getId());
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgExtendedAction.IMPORT_VENUE_APS_CSV,
          userName,
          params,
          fileDto);


      validateDatabaseAndKafkaMessageWithCustomNaming(
          tenant.getId(), requestId, "AP-", apSerials, venueIds);
      validateCmnCfgCollectorMessageWithApSerials(tenant.getId(), requestId, venueId, null,
          defaultApGroup.getId(), networkVenue.getNetwork().getId(), apSerials);
    }
  }

  void validateDatabaseAndKafkaMessage(String tenantId, String requestId) {
    List<String> apSerials = Arrays.asList("875356424903", "875356424904", "875356424905");
    List<String> venueIds = Arrays.asList("875356424903", "875356424903", "875356424903");

    validateDatabaseAndKafkaMessageWithCustomNaming(
        tenantId, requestId, "AP-", apSerials, venueIds);
  }

  void validateDatabaseAndKafkaMessageWithCustomNaming(
      String tenantId, String requestId, String apNamePrefix, List<String> apSerials, List<String> venueIds) {
    final String apSerial1 = apSerials.get(0);
    final String apSerial2 = apSerials.get(1);
    final String apSerial3 = apSerials.get(2);

    var ap1 = repositoryUtil.find(Ap.class, apSerial1);
    var ap2 = repositoryUtil.find(Ap.class, apSerial2);
    var ap3 = repositoryUtil.find(Ap.class, apSerial3);
    List<Ap> aps = Arrays.asList(ap1, ap2, ap3);
    for (int i = 0; i < aps.size(); i++) {
      int finalI = i;
      assertThat(aps.get(i))
          .isNotNull()
          .matches(a -> Objects.equals(aps.get(finalI).getId(), apSerials.get(finalI)))
          .matches(a -> Objects.equals(a.getName(), apNamePrefix.concat(apSerials.get(finalI))))
          .extracting(Ap::getApGroup)
          .isNotNull()
          .extracting(ApGroup::getVenue)
          .isNotNull()
          .matches(v -> Objects.equals(v.getId(), venueIds.get(finalI)));
    }

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    final var payload = activityCfgChangeRespMessage.getPayload();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange requestId should match payload: [%s]", payload)
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange tenantId should match")
        .isEqualTo(tenantId);

    assertThat(payload)
        .matches(p -> p.getStatus().equals(Status.OK))
        .matches(p -> p.getStep().equals(ExecutionPlanStep.IMPORT_VENUE_APS))
        .matches(p -> p.getEventDate() != null);

    assertThat(payload.getEntityIdList())
        .hasSize(3)
        .contains(apSerial1, apSerial2, apSerial3);

    final var entMessage = messageCaptors.getEntitlementDeviceOperationMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(entMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("entitleDeviceOperationRequest requestId should match")
        .isEqualTo(requestId);

    assertThat(entMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("entitleDeviceOperationRequest tenantId should match")
        .isEqualTo(tenantId);

    assertThat(entMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_DEVICE_TYPE))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(ProtoDeviceType.WIFI.name());

    assertThat(entMessage.getPayload()).isNotNull();

    assertThat(entMessage.getPayload())
        .matches(p -> p.getTenantId().equals(tenantId))
        .matches(p -> p.getRequestId().equals(requestId))
        .matches(p -> p.getRequestSource() == ProtoRequestSource.WIFI_SERVICE)
        .matches(p -> p.getOperationCount() == 3)
        .extracting(p -> p.getOperation(0))
        .matches(p -> p.getAction() == Action.CONSUME)
        .extracting(EdaOperation::getDevice)
        .matches(p -> p.getDeviceType() == ProtoDeviceType.WIFI);

    final var drsMessage = messageCaptors.getDrsMessageCaptor().getValue(tenantId, requestId);
    assertThat(drsMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(DeviceRegistrar.Header.REQUEST_ID.name()))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("Drs message requestId should match")
        .isEqualTo(requestId);

    assertThat(drsMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(DeviceRegistrar.Header.TENANT_ID.name()))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("Drs message tenantId should match")
        .isEqualTo(tenantId);

    assertThat(drsMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(DeviceRegistrar.Header.SERVICE_NAME.name()))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(DeviceRegistrar.SERVICES.WIFI_CONSUMER_SERVICE.name());

    assertThat(drsMessage.getPayload())
        .matches(p -> p.getType().equals(RequestType.CREATE))
        .extracting(DeviceRegistrar.Request::getDeviceCreateRequest)
        .matches(r -> r.getTenant().equals(tenantId))
        .matches(r -> r.getDevicesList().size() == 3)
        .extracting(r -> r.getDevices(0))
        .matches(d -> d.getType().equals(DeviceType.WIFI));
  }

  void validateFileClientAndActivityMessage(String tenantId, String requestId)
      throws InvalidProtocolBufferException {
    verify(importFileClient, times(1))
        .getDownloadUrl(anyString(), anyString());
    var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull();
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange requestId should match")
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange tenantId should match")
        .isEqualTo(tenantId);

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(Status.FAIL))
        .matches(p -> p.getStep().equals(ExecutionPlanStep.IMPORT_VENUE_APS))
        .matches(p -> p.getEventDate() != null)
        .matches(p -> {
          try {
            SimpleError error = new ObjectMapper().readValue(p.getError(), SimpleError.class);
            return error.getCode().equals(Errors.WIFI_10000.code());
          } catch (IOException e) {
            throw new RuntimeException(e);
          }
        });

    activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull();
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange requestId should match")
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .as("activityCfgChange tenantId should match")
        .isEqualTo(tenantId);

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(Status.FAIL))
        .matches(p -> p.getStep().equals(ExecutionPlanStep.POST_PROCESSED_IMPORT_APS))
        .matches(p -> p.getEventDate() != null)
        .matches(p -> {
          try {
            SimpleError error = new ObjectMapper().readValue(p.getError(),
                SimpleError.class);
            return error.getCode().equals(Errors.WIFI_10000.code());
          } catch (IOException e) {
            throw new RuntimeException(e);
          }
        });
  }

  void validateCmnCfgCollectorMessage(
      String tenantId, String requestId, String venueId, String apGroupId1, String apGroupId2, String networkId) {
    List<String> apSerials = Arrays.asList("875356424903", "875356424904", "875356424905");
    validateCmnCfgCollectorMessageWithApSerials(
        tenantId, requestId, venueId, apGroupId1, apGroupId2, networkId, apSerials);
  }

  void validateCmnCfgCollectorMessageWithApSerials(
      String tenantId, String requestId, String venueId, String apGroupId1, String apGroupId2, String networkId,
      List<String> apSerials) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    // validate AP cmnCfgCollectorMessage
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
        .isNotEmpty().hasSize(apSerials.size())
        .allSatisfy(op -> {
          assertThat(op.getOpType()).isEqualTo(OpType.ADD);
          assertTrue(op.getDocMap().containsKey(Key.DEVICE_GROUP_ID));
          assertTrue(op.getDocMap().containsKey(Key.DEVICE_GROUP_NAME));
        })
        .extracting(Operations::getId)
        .containsExactlyInAnyOrderElementsOf(apSerials);
  }
}
