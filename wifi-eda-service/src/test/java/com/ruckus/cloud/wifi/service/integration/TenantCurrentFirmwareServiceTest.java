package com.ruckus.cloud.wifi.service.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantCurrentFirmware;
import com.ruckus.cloud.wifi.repository.ApModelGreenfieldFirmwareRepository;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.repository.TenantAvailableApFirmwareRepository;
import com.ruckus.cloud.wifi.repository.TenantCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.service.TenantCurrentFirmwareService;
import com.ruckus.cloud.wifi.service.impl.TenantCurrentFirmwareServiceImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.fixture.ApModelGreenfieldFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantAvailableApFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantCurrentFirmwareTestFixture;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

@WifiJpaDataTest
public class TenantCurrentFirmwareServiceTest {
  @Autowired
  private TenantCurrentFirmwareService tenantCurrentFirmwareService;
  @Autowired
  private TenantCurrentFirmwareRepository tenantCurrentFirmwareRepository;
  @Autowired
  private ApModelGreenfieldFirmwareRepository apModelGreenfieldFirmwareRepository;
  @Autowired
  private TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository;
  @Autowired
  private ApVersionRepository apVersionRepository;

  @Test
  void testUpsert(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.1") ApVersion apVersion) {
    apVersion.setSupportedApModels(List.of("R550", "R500", "R650", "R660"));
    apVersionRepository.save(apVersion);
    // When
    tenantCurrentFirmwareService.upsert(tenant, apVersion);
    // Then
    List<TenantCurrentFirmware> res = tenantCurrentFirmwareRepository
        .findByTenantIdAndApModelIn(tenant.getId(), apVersion.getSupportedApModels());
    assertThat(res)
        .hasSize(4)
        .extracting(TenantCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R550", "6.2.3.103.1"),
            tuple("R500", "6.2.3.103.1"),
            tuple("R650", "6.2.3.103.1"),
            tuple("R660", "6.2.3.103.1"));
  }

  @Test
  void testUpsert_whenVcfExists_updateVersion(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.1") ApVersion oldVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.2") ApVersion newVersion) {
    oldVersion.setSupportedApModels(List.of("R550", "R500", "R650", "R660"));
    newVersion.setSupportedApModels(List.of("R550", "R660", "R750", "R770"));
    apVersionRepository.saveAll(List.of(oldVersion, newVersion));
    // When
    tenantCurrentFirmwareService.upsert(tenant, oldVersion);
    tenantCurrentFirmwareService.upsert(tenant, newVersion);
    // Then
    List<TenantCurrentFirmware> res = tenantCurrentFirmwareRepository
        .findByTenantIdAndApModelIn(tenant.getId(), List.of("R550", "R500", "R650", "R660", "R750", "R770"));
    assertThat(res)
        .hasSize(6)
        .extracting(TenantCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R550", "6.2.3.103.2"),
            tuple("R500", "6.2.3.103.1"),
            tuple("R650", "6.2.3.103.1"),
            tuple("R660", "6.2.3.103.2"),
            tuple("R750", "6.2.3.103.2"),
            tuple("R770", "6.2.3.103.2"));
  }

  @Test
  void testUpsert_whenVcfExists_doNotUpdateVersion(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.200.100") ApVersion oldVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.400.200") ApVersion newVersion) {
    oldVersion.setSupportedApModels(List.of("R550", "R770", "R370"));
    newVersion.setSupportedApModels(List.of("R550", "R770"));
    apVersionRepository.saveAll(List.of(oldVersion, newVersion));
    // When
    tenantCurrentFirmwareService.upsert(tenant, newVersion);
    tenantCurrentFirmwareService.upsert(tenant, oldVersion);
    // Then
    List<TenantCurrentFirmware> res = tenantCurrentFirmwareRepository
        .findByTenantIdAndApModelIn(tenant.getId(), List.of("R550", "R770", "R370"));
    assertThat(res)
        .hasSize(3)
        .extracting(TenantCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R550", "7.0.0.400.200"),
            tuple("R770", "7.0.0.400.200"),
            tuple("R370", "7.0.0.200.100"));
  }

  @Test
  void testInitializeByApModelGreenfieldFirmwares(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.1") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.2") ApVersion version623) {
    apModelGreenfieldFirmwareRepository.saveAll(List.of(
            ApModelGreenfieldFirmwareTestFixture.randomApModelGreenfieldFirmware("R500", version620),
            ApModelGreenfieldFirmwareTestFixture.randomApModelGreenfieldFirmware("R550", version623)));
    // When
    tenantCurrentFirmwareService.initializeByApModelGreenfieldFirmwares(tenant);
    // Then
    List<TenantCurrentFirmware> res = tenantCurrentFirmwareRepository
        .findByTenantIdAndApModelIn(tenant.getId(), List.of("R550", "R500", "R650", "R770"));
    assertThat(res)
        .hasSize(2)
        .extracting(TenantCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R500", "6.2.0.103.1"),
            tuple("R550", "6.2.3.103.2"));
  }

  @Test
  void testMigrateByTenantAvailableApFirmwares(Tenant tenant) {
    List<ApVersion> versions = List.of(
        ApVersionTestFixture.recommendedApVersion("7.0.0.0.1", v -> v.setSupportedApModels(List.of("R770", "R550"))),
        ApVersionTestFixture.recommendedApVersion("7.0.0.0.2", v -> v.setSupportedApModels(List.of("R770", "R550"))),
        ApVersionTestFixture.recommendedApVersion("6.2.3.0.1", v -> v.setSupportedApModels(List.of("R550", "R510"))),
        ApVersionTestFixture.recommendedApVersion("6.2.3.0.2", v -> v.setSupportedApModels(List.of("R550", "R510"))),
        ApVersionTestFixture.recommendedApVersion("6.2.0.0.1", v -> v.setSupportedApModels(List.of("R510", "R500"))),
        ApVersionTestFixture.recommendedApVersion("6.2.0.0.2", v -> v.setSupportedApModels(List.of("R510", "R500")))
    );
    apVersionRepository.saveAll(versions);
    versions.forEach(version -> {
      tenantAvailableApFirmwareRepository.save(
          TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant, version));
    });

    // When
    tenantCurrentFirmwareService.migrateByTenantAvailableApFirmwares(tenant);
    // Then
    List<TenantCurrentFirmware> res = tenantCurrentFirmwareRepository
        .findByTenantIdAndApModelIn(tenant.getId(), List.of("R770", "R550", "R510", "R500"));

    assertThat(res)
        .hasSize(4)
        .extracting(TenantCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R770", "7.0.0.0.2"),
            tuple("R550", "7.0.0.0.2"),
            tuple("R510", "6.2.3.0.2"),
            tuple("R500", "6.2.0.0.2"));
  }

  @Test
  void testMigrateByTenantAvailableApFirmwares_whenExists_updateIfSmallerThanLatestSupportedVersion(Tenant tenant) {
    List<ApVersion> versions = List.of(
        ApVersionTestFixture.recommendedApVersion("7.0.0.0.1", v -> v.setSupportedApModels(List.of("R770", "R550"))),
        ApVersionTestFixture.recommendedApVersion("7.0.0.0.2", v -> v.setSupportedApModels(List.of("R770", "R550"))),
        ApVersionTestFixture.recommendedApVersion("6.2.3.0.1", v -> v.setSupportedApModels(List.of("R550", "R510"))),
        ApVersionTestFixture.recommendedApVersion("6.2.3.0.2", v -> v.setSupportedApModels(List.of("R550", "R510"))),
        ApVersionTestFixture.recommendedApVersion("6.2.0.0.1", v -> v.setSupportedApModels(List.of("R510", "R500"))),
        ApVersionTestFixture.recommendedApVersion("6.2.0.0.2", v -> v.setSupportedApModels(List.of("R510", "R500")))
    );
    apVersionRepository.saveAll(versions);
    versions.forEach(version -> {
      tenantAvailableApFirmwareRepository.save(
          TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant, version));
    });
    ApVersion ver70001 = apVersionRepository.getReferenceById("7.0.0.0.1");
    ApVersion ver62302 = apVersionRepository.getReferenceById("6.2.3.0.2");
    ApVersion ver62001 = apVersionRepository.getReferenceById("6.2.0.0.1");
    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver70001, "R770"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver62302, "R550"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver62302, "R510"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(tenant, ver62001, "R500")
    ));

    // When
    tenantCurrentFirmwareService.migrateByTenantAvailableApFirmwares(tenant);
    // Then
    List<TenantCurrentFirmware> res = tenantCurrentFirmwareRepository
        .findByTenantIdAndApModelIn(tenant.getId(), List.of("R770", "R550", "R510", "R500"));

    assertThat(res)
        .hasSize(4)
        .extracting(TenantCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R770", "7.0.0.0.2"),
            tuple("R550", "7.0.0.0.2"),
            tuple("R510", "6.2.3.0.2"),
            tuple("R500", "6.2.0.0.2"));
  }


  @TestConfiguration
  static class TestConfig {
    @Bean
    @Primary
    public TenantCurrentFirmwareService tenantCurrentFirmwareService(
        TenantCurrentFirmwareRepository tenantCurrentFirmwareRepository,
        ApModelGreenfieldFirmwareRepository apModelGreenfieldFirmwareRepository,
        TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository,
        ApVersionRepository apVersionRepository) {
      return new TenantCurrentFirmwareServiceImpl(
          tenantCurrentFirmwareRepository,
          apModelGreenfieldFirmwareRepository,
          tenantAvailableApFirmwareRepository,
          apVersionRepository);
    }
  }
}
