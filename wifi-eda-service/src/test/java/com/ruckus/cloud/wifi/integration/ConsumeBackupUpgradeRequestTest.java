package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeBackupUpgradeRequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;


  @Test
  public void BackupUpgradeTest(Tenant tenant, Ap ap) {
    String requestId = randomTxId();
    String tenantId = tenant.getId();
    String userName = randomName();

    var params = new RequestParams();
    params.addPathVariable("serialNumber", ap.getId());

    // When
    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgExtendedAction.BACKUP_UPGRADE, userName,
        params, "");

    // Then
    assertThat(repositoryUtil.find(ApFirmwareUpgradeRequest.class, ap.getId())).isNull();
  }
}
