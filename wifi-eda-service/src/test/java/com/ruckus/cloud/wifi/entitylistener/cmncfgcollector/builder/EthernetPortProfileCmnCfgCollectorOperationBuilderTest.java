package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.bool;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.enums;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeInteger;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeShort;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.templateString;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.ap;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apGroup;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apLanPort;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apModelSpecific;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venueApModelSpecificAttributes;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venueLanPort;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity.Fields;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.SupplicantAuthenticationOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortSupplicantTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.hibernate.dto.ApActivationDto;
import com.ruckus.cloud.wifi.hibernate.dto.VenueActivationDto;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.api.BDDAssertions;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Tag("EthernetPortProfileTest")
@WifiUnitTest
@FeatureFlag(enable = {ACX_UI_ETHERNET_TOGGLE})
class EthernetPortProfileCmnCfgCollectorOperationBuilderTest {

  @SpyBean
  private EthernetPortProfileCmnCfgCollectorOperationBuilder unit;
  @MockBean
  private VenueLanPortRepository venueLanPortRepository;
  @MockBean
  private ApLanPortRepository apLanPortRepository;


  @Nested
  class CmnCfgCollectorOperationBuilderBasicTests {

    Class<? extends BaseEntity> targetClass =
        com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile.class;
    String indexName = EsConstants.Index.ETHERNET_PORT_PROFILE_INDEX_NAME;

    @Test
    void whenEntityClass() {
      BDDAssertions.then(unit.entityClass())
          .isNotNull()
          .isEqualTo(targetClass);
    }

    @Test
    void whenIndex() {
      BDDAssertions.then(unit.index())
          .isNotNull()
          .isEqualTo(indexName);
    }

    @Test
    void givenTaggedDirty() {
      BDDAssertions.then(unit.hasModification(mock(EntityAction.class), Set.of(), true)).isTrue();
    }

    @Test
    void givenActionAdd() {
      BDDAssertions.then(unit.hasModification(EntityAction.ADD, Set.of(), false)).isTrue();
    }

    @Test
    void givenActionDelete() {
      BDDAssertions.then(unit.hasModification(EntityAction.DELETE, Set.of(), false)).isTrue();
    }

    @Test
    void givenActionSync() {
      BDDAssertions.then(unit.hasModification(EntityAction.SYNC, Set.of(), false)).isTrue();
    }

    @Nested
    class GivenActionModify {

      private final Set<String> modifiedProperties =
          Stream.generate(() -> RandomStringUtils.randomAlphanumeric(10))
              .limit(5)
              .collect(Collectors.toSet());

      @Test
      void givenUpdatedRandomProperty() {
        BDDAssertions.then(unit.hasModification(EntityAction.MODIFY, modifiedProperties, false))
            .isTrue();
      }

      @Test
      void givenOnlyUpdatedDateFieldProperty() {
        Set<String> updatedDateProperties = Set.of(Fields.UPDATEDDATE);
        BDDAssertions.then(unit.hasModification(EntityAction.MODIFY, updatedDateProperties, false))
            .isFalse();
      }
    }
  }


  @Test
  public void test_config_build(Tenant tenant) {
    Operations.Builder builder = Operations.newBuilder();

    Radius authRadius =
        Generators.radiusAuth().setTenant(always(tenant))
            .setType(always(RadiusProfileTypeEnum.AUTHENTICATION))
            .generate();
    Radius acctRadius =
        Generators.radiusAuth().setTenant(always(tenant))
            .setType(always(RadiusProfileTypeEnum.ACCOUNTING))
            .generate();

    SupplicantAuthenticationOptions options = new SupplicantAuthenticationOptions();
    options.setType(ApLanPortSupplicantTypeEnum.MAC_AUTH);
    options.setUsername("username");
    options.setPassword("password");

    EthernetPortProfile testData = Generators.ethernetPortProfile()
        .setTenant(always(tenant))
        .setId(defaultIdGenerator())
        .setName(randomString(12).setAllowDigits(true).setAllowLetters(false))
        .setVenueId(defaultIdGenerator())
        .setType(enums(ApLanPortTypeEnum.class).setRandom(true))
        .setUntagId(rangeShort((short) 1, (short) 4094).setRandom(true))
        .setVlanMembers(templateString("${start}-${end}")
            .with("start", rangeInteger(1, 1000).setRandom(true))
            .with("end", rangeInteger(1001, 4094).setRandom(true)))
        .setApLanPortId(rangeInteger(1).setRandom(true))
        .setIsDefault(always(false))
        .setVni(rangeInteger(8193).setRandom(true))
        .setAuthType(enums(ApLanPortAuthTypeEnum.class).setRandom(true))
        .setAuthRadius(always(authRadius))
        .setAccountingRadius(always(acctRadius))
        .setSupplicantAuthenticationOptions(always(options))
        .setBypassMacAddressAuthentication(bool().setRandom(true))
        .setDynamicVlanEnabled(bool().setRandom(true))
        .setUnauthenticatedGuestVlan(rangeShort((short) 1, (short) 4094).setRandom(true))
        .generate();

    String tenantId = "tenantId00";
    String venueId01 = "venueId01";
    String venueId02 = "venueId02";

    var venue01 = new Venue(venueId01);
    var venue02 = new Venue(venueId02);

    var vamsAttr1 = venueApModelSpecificAttributes("R100", venue01);
    var vamsAttr2 = venueApModelSpecificAttributes("R200", venue02);

    var venueLanPort01 = venueLanPort(1, vamsAttr1).generate();
    var venueLanPort02 = venueLanPort(1, vamsAttr2).generate();

    when(venueLanPortRepository.findVenueActivations(anyString(), anyString()))
        .thenReturn(List.of(new VenueActivationDto(venueId01, "R100", "1"),
            new VenueActivationDto(venueId02, "R200", "1")));

    String apId1 = "apId1";
    String apId2 = "apId2";

    var apg1 = apGroup();
    apg1.setVenue(always(venue01));
    var apg2 = apGroup();
    apg2.setVenue(always(venue02));

    var ap1 = ap();
    ap1.setId(always(apId1));
    ap1.setApGroup(apg1);
    var ap2 = ap();
    ap2.setId(always(apId2));
    ap2.setApGroup(apg2);

    var apModelSpecific1 = apModelSpecific(ap1);
    var apModelSpecific2 = apModelSpecific(ap2);

    var apLanPort01 = apLanPort(1, apModelSpecific1).generate();
    var apLanPort02 = apLanPort(1, apModelSpecific2).generate();

    when(apLanPortRepository.findApActivations(anyString(), anyString()))
        .thenReturn(List.of(new ApActivationDto(venueId01, apId1, "1"),
            new ApActivationDto(venueId02, apId2, "1")));

    unit.config(builder, testData, EntityAction.MODIFY);
    Map<String, Value> docMap = builder.build().getDocMap();

    assertThat(docMap.get(EsConstants.Key.TENANT_ID).getStringValue()).isEqualTo(
        testData.getTenant().getId());

    assertThat(docMap.get(EsConstants.Key.ID).getStringValue()).isEqualTo(testData.getId());
    assertThat(docMap.get(EsConstants.Key.NAME).getStringValue()).isEqualTo(testData.getName());

    assertThat(docMap.get(EsConstants.Key.TYPE).getStringValue()).isEqualTo(
        unit.getViewModelType(testData.getType()));
    assertThat((short) docMap.get(EsConstants.Key.UNTAG_ID).getNumberValue()).isEqualTo(
        testData.getUntagId());
    assertThat(docMap.get(EsConstants.Key.VLAN_MEMBERS).getStringValue()).isEqualTo(
        testData.getVlanMembers());

    assertThat(docMap.get(EsConstants.Key.IS_DEFAULT).getBoolValue()).isEqualTo(
        testData.getIsDefault());

    assertThat(docMap.get(EsConstants.Key.VNI).getNumberValue()).isEqualTo(
        testData.getVni().doubleValue());

    assertThat(docMap.get(EsConstants.Key.AUTH_TYPE).getStringValue()).isEqualTo(
        testData.getAuthType().toString());
    assertThat(docMap.get(EsConstants.Key.AUTH_RADIUS_ID).getStringValue()).isEqualTo(
        testData.getAuthRadius().getId());
    assertThat(docMap.get(EsConstants.Key.ACCOUNTING_RADIUS_ID).getStringValue()).isEqualTo(
        testData.getAccountingRadius().getId());
    // Handle Venue and AP level active eth profile
    assertThat(docMap.get(Key.VENUE_IDS))
        .matches(value -> value.getKindCase() == Value.KindCase.LIST_VALUE)
        .extracting(value -> value.getListValue().getValuesList(),
            InstanceOfAssertFactories.list(Value.class))
        .hasSize(2)
        .satisfies(
            values -> {
              assertThat(values.get(0).getStringValue())
                  .matches(id -> id.equals(venueId01) || id.equals(venueId02));
              assertThat(values.get(1).getStringValue()).isEqualTo(venueId02)
                  .matches(id -> id.equals(venueId01) || id.equals(venueId02));
            }
        );

    assertThat(docMap.get(Key.AP_SERIAL_NUMBERS))
        .matches(value -> value.getKindCase() == Value.KindCase.LIST_VALUE)
        .extracting(value -> value.getListValue().getValuesList(),
            InstanceOfAssertFactories.list(Value.class))
        .hasSize(2)
        .satisfies(
            values -> {
              assertThat(values.get(0).getStringValue())
                  .matches(id -> id.equals(apId1) || id.equals(apId2));
              assertThat(values.get(1).getStringValue())
                  .matches(id -> id.equals(apId1) || id.equals(apId2));
            }
        );

    assertThat(docMap.get(EsConstants.Key.VENUE_ACTIVATIONS))
        .matches(value -> value.getKindCase() == Value.KindCase.LIST_VALUE)
        .extracting(value -> value.getListValue().getValuesList(),
            InstanceOfAssertFactories.list(Value.class))
        .hasSize(2)
        .satisfies(
            values -> {
              for (var value : values) {
                assertThat(value).matches(v -> v.getKindCase() == KindCase.STRUCT_VALUE);
                var structValue = value.getStructValue();
                assertThat(structValue.getFieldsMap())
                    .hasSize(3)
                    .hasEntrySatisfying(Key.VENUE_ID,
                        venueId -> assertThat(venueId.getStringValue()).isIn(venueId01, venueId02))
                    .hasEntrySatisfying(Key.AP_MODEL,
                        apModel -> assertThat(apModel.getStringValue()).isIn("R100", "R200"))
                    .hasEntrySatisfying(Key.PORT_ID,
                        portId -> assertThat(portId.getStringValue()).isEqualTo("1"));
              }
            }
        );

    assertThat(docMap.get(Key.AP_ACTIVATIONS))
        .matches(value -> value.getKindCase() == Value.KindCase.LIST_VALUE)
        .extracting(value -> value.getListValue().getValuesList(),
            InstanceOfAssertFactories.list(Value.class))
        .hasSize(2)
        .satisfies(
            values -> {
              for (var value : values) {
                assertThat(value).matches(v -> v.getKindCase() == KindCase.STRUCT_VALUE);
                var structValue = value.getStructValue();
                assertThat(structValue.getFieldsMap())
                    .hasSize(3)
                    .hasEntrySatisfying(Key.VENUE_ID,
                        venueId -> assertThat(venueId.getStringValue()).isIn(venueId01, venueId02))
                    .hasEntrySatisfying(Key.AP_SERIAL_NUMBER,
                        apSerialNumber -> assertThat(apSerialNumber.getStringValue()).isIn(apId1, apId2))
                    .hasEntrySatisfying(Key.PORT_ID,
                        portId -> assertThat(portId.getStringValue()).isEqualTo("1"));
              }
            }
        );
  }
}
