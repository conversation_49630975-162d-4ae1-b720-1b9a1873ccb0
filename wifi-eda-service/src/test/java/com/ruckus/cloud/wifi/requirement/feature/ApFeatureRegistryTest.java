package com.ruckus.cloud.wifi.requirement.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ApFeatureRegistryTest {
  private ApFeatureRegistry apFeatureRegistry;

  @Mock
  private ApIotFeature apIotFeature;

  @Mock
  private BssColoringFeature bssColoringFeature;

  @BeforeEach
  void setUp() {
    // mock returned feature by name
    when(apIotFeature.getFeatureName()).thenReturn("AP IoT");
    when(bssColoringFeature.getFeatureName()).thenReturn("BSS Coloring");

    // initialize ApFeatureRegistry
    apFeatureRegistry = new ApFeatureRegistry(List.of(apIotFeature, bssColoringFeature));
  }

  @Test
  void shouldReturnFeatureByNameWhenExists() {
    // AP IoT
    ApFeature feature = apFeatureRegistry.getFeatureByName("AP IoT");
    assertNotNull(feature);
    assertEquals(apIotFeature, feature);

    // BSS Coloring
    feature = apFeatureRegistry.getFeatureByName("BSS Coloring");
    assertNotNull(feature);
    assertEquals(bssColoringFeature, feature);
  }

  @Test
  void shouldThrowExceptionWhenFeatureNameNotFound() {
    Exception exception = assertThrows(IllegalArgumentException.class, () -> {
      apFeatureRegistry.getFeatureByName("UnknownFeature");
    });

    assertEquals("No feature found for name: UnknownFeature", exception.getMessage());
  }

  @Test
  void shouldInitializeFeatureMapCorrectly() {
    // check if featureMap contains expected feature
    assertEquals(apIotFeature, apFeatureRegistry.getFeatureByName("AP IoT"));
    assertEquals(bssColoringFeature, apFeatureRegistry.getFeatureByName("BSS Coloring"));
  }
}