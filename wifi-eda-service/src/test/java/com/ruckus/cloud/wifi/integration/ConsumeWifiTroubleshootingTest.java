package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruckus.cloud.notification.dnb.gpb.Dnb;
import com.ruckus.cloud.notification.service.gpb.DeviceNotification;
import com.ruckus.cloud.notification.troubleshooting.gpb.Troubleshooting;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.service.entity.PacketCapture;
import com.ruckus.cloud.wifi.service.entity.PacketCaptureOpStatus;
import com.ruckus.cloud.wifi.service.entity.PcapCallbackContext;
import com.ruckus.cloud.wifi.service.impl.PacketCaptureDaoServiceImpl;
import com.ruckus.cloud.wifi.service.impl.PacketCaptureServiceImpl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import java.io.IOException;
import java.time.Duration;
import java.util.List;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.redisson.api.RTransaction;
import org.redisson.api.RedissonClient;
import org.redisson.api.TransactionOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@WifiIntegrationTest
public class ConsumeWifiTroubleshootingTest {

  private static final ObjectMapper objectMapper = new ObjectMapper();
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  @Qualifier("serviceRedissonClient")
  private RedissonClient redissonClient;
  @Autowired
  private PacketCaptureServiceImpl packetCaptureServiceImpl;
  @Autowired
  private PacketCaptureDaoServiceImpl packetCaptureDao;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  private void assertImpactedDeviceIsNull(String tenantId) {
    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(tenantId);
  }

  @Nested
  class whenConsumeWifiTroubleshootingTest {

    @Test
    public void handleStartPcap(Ap ap) throws IOException {
      final var requestId = randomTxId();

      // produce start message
      Troubleshooting.TroubleshootingMsg msg =
          Troubleshooting.TroubleshootingMsg.newBuilder()
              .setEventType(Troubleshooting.TroubleshootingMsg.EventType.PACKET_CAPTURE)
              .setEventPacketCapture(Troubleshooting.EventPacketCapture.newBuilder()
                  .setActionType(Troubleshooting.EventPacketCapture.ActionType.START)
                  .setSerial(ap.getId())
                  .setCaptureInterface(Dnb.CaptureInterface.RADIO_24.name())
                  .build())
              .build();
      messageUtil.sendWifiTroubleshooting(ap.getTenant().getId(), requestId, msg);

      var notificationRecord = messageCaptors.getDeviceNotificationMessageCaptor()
              .getValue(ap.getTenant().getId(), requestId);
      assertThat(notificationRecord).isNotNull();
      DeviceNotification notificationMessage = notificationRecord.getPayload();
      String callbackContext = Dnb.DNB.parseFrom(notificationMessage.getContent())
          .getTaskPacketCaptureStart().getCallbackContext();

      //assert
      assertThat(callbackContext).isEqualTo("{\"sessionId\":\"" + requestId + "\"}");
      assertThat(objectMapper.readValue(callbackContext, PcapCallbackContext.class)
          .getSessionId()).isEqualTo(requestId);

      PacketCapture pcap = packetCaptureServiceImpl.get(ap.getId());
      assertThat(pcap.getSerial()).isEqualTo(ap.getId());
      assertThat(pcap.getCaptureInterface()).isEqualTo(Dnb.CaptureInterface.RADIO_24.name());
      assertThat(pcap.getSessionId()).isEqualTo(requestId);
      assertThat(pcap.getOpStatus()).isEqualTo(PacketCaptureOpStatus.STARTING);

      assertImpactedDeviceIsNull(ap.getTenant().getId());

    }

    @Test
    public void handleStopPcap(Ap ap) throws IOException {
      final var requestId = randomTxId();

      // prepare capturing record
      RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());
      List<String> mockFrameTypeFilter = List.of("DATA", "CONTROL");

      packetCaptureDao.create(transaction, ap.getId(),
          packetCaptureServiceImpl.startDataSet(requestId, "gcsfile", "uploadurl",
              Dnb.CaptureInterface.RADIO_24.name(), mockFrameTypeFilter),
          Duration.ofSeconds(20));
      packetCaptureDao.update(transaction, ap.getId(),
          packetCaptureServiceImpl.reportStartedDataSet());
      transaction.commit();

      // produce start message
      Troubleshooting.TroubleshootingMsg msg =
          Troubleshooting.TroubleshootingMsg.newBuilder()
              .setEventType(Troubleshooting.TroubleshootingMsg.EventType.PACKET_CAPTURE)
              .setEventPacketCapture(Troubleshooting.EventPacketCapture.newBuilder()
                  .setActionType(Troubleshooting.EventPacketCapture.ActionType.STOP)
                  .setSerial(ap.getId())
                  .setSessionId(requestId)
                  .build())
              .build();
      messageUtil.sendWifiTroubleshooting(ap.getTenant().getId(), requestId, msg);

      var notificationRecord = messageCaptors.getDeviceNotificationMessageCaptor()
          .getValue(ap.getTenant().getId(), requestId);
      assertThat(notificationRecord).isNotNull();
      DeviceNotification notificationMessage = notificationRecord.getPayload();
      String callbackContext = Dnb.DNB.parseFrom(notificationMessage.getContent())
          .getTaskPacketCaptureStop().getCallbackContext();

      //assert
      assertThat(callbackContext).isEqualTo("{\"sessionId\":\"" + requestId + "\"}");
      assertThat(objectMapper.readValue(callbackContext, PcapCallbackContext.class)
          .getSessionId()).isEqualTo(requestId);

      PacketCapture pcap = packetCaptureServiceImpl.get(ap.getId());
      assertThat(pcap.getSerial()).isEqualTo(ap.getId());
      assertThat(pcap.getCaptureInterface()).isEqualTo(Dnb.CaptureInterface.RADIO_24.name());
      assertThat(pcap.getSessionId()).isEqualTo(requestId);
      assertThat(pcap.getOpStatus()).isEqualTo(PacketCaptureOpStatus.STOPPING);

    }

    @Test
    public void handlePcapReportStarted(Ap ap) {
      final var requestId = randomTxId();

      // prepare capturing record
      RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());
      List<String> mockFrameTypeFilter = List.of("DATA", "CONTROL");
      packetCaptureDao.create(transaction, ap.getId(),
          packetCaptureServiceImpl.startDataSet(requestId, "gcsFilePath",
              "mockUploadUrl", Dnb.CaptureInterface.RADIO_24.name(), mockFrameTypeFilter),
          Duration.ofSeconds(20));
      transaction.commit();

      // produce start message
      Troubleshooting.TroubleshootingMsg msg =
          Troubleshooting.TroubleshootingMsg.newBuilder()
              .setEventType(Troubleshooting.TroubleshootingMsg.EventType.PACKET_CAPTURE)
              .setEventPacketCapture(Troubleshooting.EventPacketCapture.newBuilder()
                  .setActionType(Troubleshooting.EventPacketCapture.ActionType.REPORT_STARTED)
                  .setSerial(ap.getId())
                  .setSessionId(requestId)
                  .build())
              .build();
      messageUtil.sendWifiTroubleshooting("", requestId, msg);

      //assert
      PacketCapture pcap = packetCaptureServiceImpl.get(ap.getId());
      assertThat(pcap.getSerial()).isEqualTo(ap.getId());
      assertThat(pcap.getCaptureInterface()).isEqualTo(Dnb.CaptureInterface.RADIO_24.name());
      assertThat(pcap.getSessionId()).isEqualTo(requestId);
      assertThat(pcap.getOpStatus()).isEqualTo(PacketCaptureOpStatus.CAPTURING);

    }

    @Test
    public void handlePcapReportReady(Ap ap) {
      final var requestId = randomTxId();

      // prepare capturing record
      RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());
      List<String> mockFrameTypeFilter = List.of("DATA", "CONTROL");

      packetCaptureDao.create(transaction, ap.getId(),
          packetCaptureServiceImpl.startDataSet(requestId, "gcsfile", "uploadurl",
              Dnb.CaptureInterface.RADIO_24.name(), mockFrameTypeFilter),
          Duration.ofSeconds(20));
      packetCaptureDao.update(transaction, ap.getId(),
          packetCaptureServiceImpl.reportStartedDataSet());
      transaction.commit();

      // produce start message
      Troubleshooting.TroubleshootingMsg msg =
          Troubleshooting.TroubleshootingMsg.newBuilder()
              .setEventType(Troubleshooting.TroubleshootingMsg.EventType.PACKET_CAPTURE)
              .setEventPacketCapture(Troubleshooting.EventPacketCapture.newBuilder()
                  .setActionType(Troubleshooting.EventPacketCapture.ActionType.REPORT_READY)
                  .setSerial(ap.getId())
                  .setSessionId(requestId)
                  .build())
              .build();
      messageUtil.sendWifiTroubleshooting(ap.getTenant().getId(), requestId, msg);

      //assert
      PacketCapture pcap = packetCaptureServiceImpl.get(ap.getId());
      assertThat(pcap.getSerial()).isEqualTo(ap.getId());
      assertThat(pcap.getCaptureInterface()).isEqualTo(Dnb.CaptureInterface.RADIO_24.name());
      assertThat(pcap.getSessionId()).isEqualTo(requestId);
      assertThat(pcap.getOpStatus()).isEqualTo(PacketCaptureOpStatus.READY);

    }

    @Test
    public void handlePcapReportStartingError(Ap ap) {
      final var requestId = randomTxId();
      String expectedErrMsg = "not now";

      // prepare capturing record
      RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());
      List<String> mockFrameTypeFilter = List.of("DATA", "CONTROL");

      packetCaptureDao.create(transaction, ap.getId(),
          packetCaptureServiceImpl.startDataSet(requestId, "gcsfile", "uploadurl",
              Dnb.CaptureInterface.RADIO_24.name(), mockFrameTypeFilter),
          Duration.ofSeconds(20));
      transaction.commit();

      // produce start message
      Troubleshooting.TroubleshootingMsg msg =
          Troubleshooting.TroubleshootingMsg.newBuilder()
              .setEventType(Troubleshooting.TroubleshootingMsg.EventType.PACKET_CAPTURE)
              .setEventPacketCapture(Troubleshooting.EventPacketCapture.newBuilder()
                  .setActionType(Troubleshooting.EventPacketCapture.ActionType.REPORT_ERROR)
                  .setSerial(ap.getId())
                  .setSessionId(requestId)
                  .setErrorMsg(expectedErrMsg)
                  .build())
              .build();
      messageUtil.sendWifiTroubleshooting(ap.getTenant().getId(), requestId, msg);

      //assert
      PacketCapture pcap = packetCaptureServiceImpl.get(ap.getId());
      assertThat(pcap.getSerial()).isEqualTo(ap.getId());
      assertThat(pcap.getCaptureInterface()).isEqualTo(Dnb.CaptureInterface.RADIO_24.name());
      assertThat(pcap.getSessionId()).isEqualTo(requestId);
      assertThat(pcap.getOpStatus()).isEqualTo(PacketCaptureOpStatus.STARTING_ERROR);
      assertThat(pcap.getErrMsg()).isEqualTo(expectedErrMsg);

    }

    @Test
    public void handlePcapReportStoppingError(Ap ap) {
      final var requestId = randomTxId();
      String expectedErrMsg = "not now";

      // prepare capturing record
      RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());
      List<String> mockFrameTypeFilter = List.of("DATA", "CONTROL");

      packetCaptureDao.create(transaction, ap.getId(),
          packetCaptureServiceImpl.startDataSet(requestId, "gcsfile", "uploadurl",
              Dnb.CaptureInterface.RADIO_24.name(), mockFrameTypeFilter),
          Duration.ofSeconds(20));
      packetCaptureDao.update(transaction, ap.getId(),
          packetCaptureServiceImpl.reportStartedDataSet());
      packetCaptureDao.update(transaction, ap.getId(), packetCaptureServiceImpl.stopDataSet());
      transaction.commit();

      // produce start message
      Troubleshooting.TroubleshootingMsg msg =
          Troubleshooting.TroubleshootingMsg.newBuilder()
              .setEventType(Troubleshooting.TroubleshootingMsg.EventType.PACKET_CAPTURE)
              .setEventPacketCapture(Troubleshooting.EventPacketCapture.newBuilder()
                  .setActionType(Troubleshooting.EventPacketCapture.ActionType.REPORT_ERROR)
                  .setSerial(ap.getId())
                  .setSessionId(requestId)
                  .setErrorMsg(expectedErrMsg)
                  .build())
              .build();
      messageUtil.sendWifiTroubleshooting(ap.getTenant().getId(), requestId, msg);

      //assert
      PacketCapture pcap = packetCaptureServiceImpl.get(ap.getId());
      assertThat(pcap.getSerial()).isEqualTo(ap.getId());
      assertThat(pcap.getCaptureInterface()).isEqualTo(Dnb.CaptureInterface.RADIO_24.name());
      assertThat(pcap.getSessionId()).isEqualTo(requestId);
      assertThat(pcap.getOpStatus()).isEqualTo(PacketCaptureOpStatus.STOPPING_ERROR);
      assertThat(pcap.getErrMsg()).isEqualTo(expectedErrMsg);

    }

    @Test
    public void handleStartPcapNotEnableEda(Ap ap) {
      final var requestId = randomTxId();

      // produce start message
      Troubleshooting.TroubleshootingMsg msg =
          Troubleshooting.TroubleshootingMsg.newBuilder()
              .setEventType(Troubleshooting.TroubleshootingMsg.EventType.PACKET_CAPTURE)
              .setEventPacketCapture(Troubleshooting.EventPacketCapture.newBuilder()
                  .setActionType(Troubleshooting.EventPacketCapture.ActionType.START)
                  .setSerial(ap.getId())
                  .setCaptureInterface(Dnb.CaptureInterface.RADIO_24.name())
                  .build())
              .build();
      messageUtil.sendWifiTroubleshooting(ap.getTenant().getId(), requestId, msg, "wifi-consumer");

      //assert
      messageCaptors.getDeviceNotificationMessageCaptor()
          .assertNotSentByTenant(ap.getTenant().getId());
    }
  }
}
