package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_AP_ON_AP_GROUP;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_MULTICAST_DNS_PROXY_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DEACTIVATE_MULTICAST_DNS_PROXY_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_MULTICAST_DNS_PROXY_SERVICE_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_MULTICAST_DNS_PROXY_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.enums;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.multicastDnsProxyProfile;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.multicastDnsProxyServiceRule;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.VENUE_IDS;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.MulticastDnsProxyServiceProfileRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceProtocolEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.MulticastDnsProxyProfileGenerator;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.viewmodel.MulticastDnsProxyProfile;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.repository.MulticastDnsProxyServiceProfileApRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeMulticastDnsProxyProfileRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  protected MulticastDnsProxyServiceProfileApRepository multicastDnsProxyServiceProfileApRepository;

  @Autowired
  protected VenueRepository venueRepository;


  @Nested
  @ApiAction(CfgAction.ADD_MULTICAST_DNS_PROXY_PROFILE)
  class ConsumeAddMultiDnsProxyProfileRequestTest {

    @Payload
    private final MulticastDnsProxyProfileGenerator generator = multicastDnsProxyProfile();

    @Payload("withOtherType")
    private MulticastDnsProxyProfile withOtherType() {
      MulticastDnsProxyProfile profile = generator.generate();
      profile.getRules().add(multicastDnsProxyServiceRule().setService(always(BridgeServiceEnum.OTHER))
          .setMdnsName(randomString(9))
          .setMdnsProtocol(enums(BridgeServiceProtocolEnum.class)).generate());
      return profile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload MulticastDnsProxyProfile payload) {
      final MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = repositoryUtil.find(
          MulticastDnsProxyServiceProfile.class, payload.getId());
      assertNotNull(multicastDnsProxyServiceProfile);

      validateRepositoryData(payload, multicastDnsProxyServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
          List.of(multicastDnsProxyServiceProfile.getId())), payload);
      validateCmnCfgCollectorMessages(CfgAction.ADD_MULTICAST_DNS_PROXY_PROFILE,
          List.of(payload.getId()), multicastDnsProxyServiceProfile, 0);
      validateActivityMessages(ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_MULTICAST_DNS_PROXY_PROFILE, payload = @Payload("withOtherType"))
    void thenShouldHandleWithOtherTypeRequestSuccessfully(
        @Payload("withOtherType") MulticastDnsProxyProfile payload) {
      final MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = repositoryUtil.find(
          MulticastDnsProxyServiceProfile.class, payload.getId());
      assertNotNull(multicastDnsProxyServiceProfile);

      validateRepositoryData(payload, multicastDnsProxyServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
          List.of(multicastDnsProxyServiceProfile.getId())), payload);
      validateCmnCfgCollectorMessages(CfgAction.ADD_MULTICAST_DNS_PROXY_PROFILE,
          List.of(payload.getId()), multicastDnsProxyServiceProfile, 0);
      validateActivityMessages(ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_MULTICAST_DNS_PROXY_PROFILE)
  class ConsumeUpdateMultiDnsProxyProfileRequestTest {

    private String id;
    private MulticastDnsProxyServiceProfile savedMulticastDnsProxyServiceProfile;

    @BeforeEach
    void givenOneRowPersistedInDb(MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile) {
      id = multicastDnsProxyServiceProfile.getId();
      this.savedMulticastDnsProxyServiceProfile = multicastDnsProxyServiceProfile;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("multicastDnsProxyProfileId", id);
    }

    @Payload
    private final MulticastDnsProxyProfileGenerator generator = multicastDnsProxyProfile();

    @Payload("withOtherType")
    private MulticastDnsProxyProfile withOtherType() {
      MulticastDnsProxyProfile profile = generator.generate();
      profile.getRules().add(multicastDnsProxyServiceRule().setService(always(BridgeServiceEnum.OTHER))
          .setMdnsName(randomString(9))
          .setMdnsProtocol(enums(BridgeServiceProtocolEnum.class)).generate());
      return profile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload MulticastDnsProxyProfile payload) {
      final MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = repositoryUtil.find(
          MulticastDnsProxyServiceProfile.class, id);
      payload.setId(id);
      assertNotNull(multicastDnsProxyServiceProfile);

      validateRepositoryData(payload, multicastDnsProxyServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.MODIFY,
          List.of(multicastDnsProxyServiceProfile.getId())), payload);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_MULTICAST_DNS_PROXY_PROFILE,
          List.of(payload.getId()), multicastDnsProxyServiceProfile, 0);
      validateActivityMessages(UPDATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_MULTICAST_DNS_PROXY_PROFILE, payload = @Payload("withOtherType"))
    void thenShouldHandleWithOtherTypeRequestSuccessfully(
        @Payload("withOtherType") MulticastDnsProxyProfile payload) {
      final MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = repositoryUtil.find(
          MulticastDnsProxyServiceProfile.class, id);
      payload.setId(id);
      assertNotNull(multicastDnsProxyServiceProfile);

      validateRepositoryData(payload, multicastDnsProxyServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.MODIFY,
          List.of(multicastDnsProxyServiceProfile.getId())), payload);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_MULTICAST_DNS_PROXY_PROFILE,
          List.of(payload.getId()), multicastDnsProxyServiceProfile, 0);
      validateActivityMessages(UPDATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_MULTICAST_DNS_PROXY_PROFILE)
  class ConsumeDeleteMultiDnsProxyProfileRequestTest {

    private String id;
    private MulticastDnsProxyServiceProfile savedMulticastDnsProxyServiceProfile;

    @BeforeEach
    void givenOneRowPersistedInDb(MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile) {
      id = multicastDnsProxyServiceProfile.getId();
      this.savedMulticastDnsProxyServiceProfile = multicastDnsProxyServiceProfile;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("multicastDnsProxyProfileId", id);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      assertNull(repositoryUtil.find(
          MulticastDnsProxyServiceProfile.class, id));

      validateDdccmCfgRequestMessages(Map.of(Action.DELETE, List.of(id)), null);
      validateCmnCfgCollectorMessages(CfgAction.DELETE_MULTICAST_DNS_PROXY_PROFILE,
          List.of(id), null, 0);
      validateActivityMessages(DELETE_MULTICAST_DNS_PROXY_SERVICE_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_MULTICAST_DNS_PROXY_PROFILE)
  class ConsumeNewBindingMultiDnsProxyServiceProfileRequestTest {

    private String venueId;

    private String apSerialNumber;

    private String id;

    private MulticastDnsProxyServiceProfile savedMulticastDnsProxyServiceProfile;
    @BeforeEach
    void givenOneRowPersistedInDb(Ap ap,
        MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile) {
      venueId = ap.getApGroup().getVenue().getId();
      apSerialNumber = ap.getId();
      id = multicastDnsProxyServiceProfile.getId();
      this.savedMulticastDnsProxyServiceProfile = multicastDnsProxyServiceProfile;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("apSerialNumber", apSerialNumber)
          .addPathVariable("multicastDnsProxyProfileId", id);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      final List<MulticastDnsProxyServiceProfileAp> multicastDnsProxyServiceProfileApList =
          multicastDnsProxyServiceProfileApRepository.findByApIdAndTenantId(apSerialNumber, txCtxExtension.getTenantId());

      validateApDdccmCfgRequestMessages(Map.of(Action.MODIFY,
          List.of(apSerialNumber)), id, ACTIVATE_MULTICAST_DNS_PROXY_PROFILE);
      validateCmnCfgCollectorMessages(CfgAction.ACTIVATE_MULTICAST_DNS_PROXY_PROFILE,
          List.of(id), savedMulticastDnsProxyServiceProfile, 1);
      validateActivityMessages(ACTIVATE_MULTICAST_DNS_PROXY_PROFILE);
    }
  }

  @Test
  public void testActivateProfileOnActivatedProfile(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyProfile multicastDnsProxyProfile = multicastDnsProxyProfile().generate();
    String venueId = venue.getId();
    String serialNumber = ap.getId();
    String multicastDnsProxyProfileId = multicastDnsProxyProfile.getId();

    activateMulticastDnsProxyProfile(tenantId, venueId, serialNumber,
        multicastDnsProxyProfileId, multicastDnsProxyProfile);

    // activate
    activateMulticastDnsProxyProfile_eda(tenantId, venueId, serialNumber, multicastDnsProxyProfileId);
    assertDdccmCfgRequestNotSent(tenantId);
    var activateViewmodelOperations2 = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(activateViewmodelOperations2, 1),
        () -> assertCmnMulticastDnsProxyServiceProfile(activateViewmodelOperations2, OpType.MOD, 1,
            venueId, multicastDnsProxyProfileId, null));
    assertActivityStatusSuccess(ACTIVATE_MULTICAST_DNS_PROXY_PROFILE, tenantId);
  }

  @Test
  public void testDeactivateProfileOnActivatedProfile(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyProfile multicastDnsProxyProfile = multicastDnsProxyProfile().generate();
    String venueId = venue.getId();
    String serialNumber = ap.getId();
    String multicastDnsProxyProfileId = multicastDnsProxyProfile.getId();

    activateMulticastDnsProxyProfile(tenantId, venueId, serialNumber,
        multicastDnsProxyProfileId, multicastDnsProxyProfile);

    // deactivate
    deactivateMulticastDnsProxyProfile_eda(tenantId, venueId, serialNumber, multicastDnsProxyProfileId);
    var deactivateApDdccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(deactivateApDdccmOperations, 1),
        () -> assertDdccmAp(deactivateApDdccmOperations, Action.DELETE, serialNumber, multicastDnsProxyProfileId)
    );
    var deactivateApViewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(deactivateApViewmodelOperations, 1),
        () -> assertCmnMulticastDnsProxyServiceProfile(deactivateApViewmodelOperations, OpType.MOD, 1,
            venueId, null, multicastDnsProxyProfileId));
    assertActivityStatusSuccess(DEACTIVATE_MULTICAST_DNS_PROXY_PROFILE, tenantId);
  }

  @Test
  public void testActivateAnotherProfileOnAp(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyProfile multicastDnsProxyProfile1
        = multicastDnsProxyProfile().generate();
    String venueId = venue.getId();
    String serialNumber = ap.getId();
    String multicastDnsProxyProfileId1 = multicastDnsProxyProfile1.getId();

    activateMulticastDnsProxyProfile(tenantId, venueId, serialNumber,
        multicastDnsProxyProfileId1, multicastDnsProxyProfile1);

    // add another profile
    MulticastDnsProxyProfile multicastDnsProxyProfile2
        = multicastDnsProxyProfile().setServiceName(always("anotherProfile")).generate();
    String multicastDnsProxyProfileId2 = multicastDnsProxyProfile2.getId();
    addMulticastDnsProxyProfile_eda(tenantId, multicastDnsProxyProfile2);
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 1),
        () -> assertDdccmVenueBonjourGateway(ddccmOperations, Action.ADD, multicastDnsProxyProfile2)
    );
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 1),
        () -> assertCmnMulticastDnsProxyServiceProfile(viewmodelOperations, OpType.ADD, 1, null,
            null, null));
    assertActivityStatusSuccess(ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    // activate
    activateMulticastDnsProxyProfile_eda(tenantId, venueId, serialNumber, multicastDnsProxyProfileId2);
    var activateApDdccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(activateApDdccmOperations, 1),
        () -> assertDdccmAp(activateApDdccmOperations, Action.MODIFY, serialNumber, multicastDnsProxyProfileId2)
    );
    var activateApViewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(activateApViewmodelOperations, 2),
        () -> assertCmnMulticastDnsProxyServiceProfile(activateApViewmodelOperations, OpType.MOD, 2,
            venueId, multicastDnsProxyProfileId2, multicastDnsProxyProfileId1));
    assertActivityStatusSuccess(ACTIVATE_MULTICAST_DNS_PROXY_PROFILE, tenantId);
  }

  @Test
  public void testUpdateProfileOnActivatedProfile(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyProfile MulticastDnsProxyProfile = multicastDnsProxyProfile().generate();
    String venueId = venue.getId();
    String serialNumber = ap.getId();
    String multicastDnsProxyProfileId = MulticastDnsProxyProfile.getId();

    activateMulticastDnsProxyProfile(tenantId, venueId, serialNumber,
        multicastDnsProxyProfileId, MulticastDnsProxyProfile);

    // update
    MulticastDnsProxyProfile.getRules().addAll(multicastDnsProxyServiceRule().toListGenerator(4).generate());
    updateMulticastDnsProxyProfile_eda(tenantId, multicastDnsProxyProfileId, MulticastDnsProxyProfile);
    var updateProfileDdccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(updateProfileDdccmOperations, 1),
        () -> assertDdccmVenueBonjourGateway(updateProfileDdccmOperations, Action.MODIFY, MulticastDnsProxyProfile)
    );
    var updateProfileViewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(updateProfileViewmodelOperations, 1),
        () -> assertCmnMulticastDnsProxyServiceProfile(updateProfileViewmodelOperations, OpType.MOD, 1,
            venueId, multicastDnsProxyProfileId, null));
    assertActivityStatusSuccess(UPDATE_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);
  }

  @Test
  public void testMoveApOnActivatedProfile(Tenant tenant) throws Exception {
    String tenantId = txCtxExtension.getTenantId();
    Venue venue = createVenue(tenant, "venue-test");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyProfile multicastDnsProxyProfile = multicastDnsProxyProfile().generate();
    String venueId = venue.getId();
    String serialNumber = ap.getId();
    String multicastDnsProxyProfileId = multicastDnsProxyProfile.getId();

    activateMulticastDnsProxyProfile(tenantId, venueId, serialNumber,
        multicastDnsProxyProfileId, multicastDnsProxyProfile);

    Venue newVenue = createVenue(tenant, "new-venue-test");
    ApGroup apGroup = createApGroup(newVenue, "new-ap-group");
    moveAp_eda(tenantId, newVenue.getId(), apGroup.getId(), serialNumber);
    var moveApDdccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccmMoveAp(moveApDdccmOperations)
    );
    var moveApViewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnMulticastDnsProxyServiceProfile(moveApViewmodelOperations, OpType.MOD, 1,
            venueId, null, multicastDnsProxyProfileId));
    assertActivityStatusSuccess(ACTIVATE_AP_ON_AP_GROUP, tenantId);
  }

  private String moveAp_eda(String tenantId, String venueId, String apGroupId, String apSerialNumber) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.ACTIVATE_AP_ON_AP_GROUP;
    RequestParams params = new RequestParams().addPathVariable("venueId", venueId)
        .addPathVariable("apGroupId", apGroupId)
        .addPathVariable("serialNumber", apSerialNumber);

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, null);

    return requestId;
  }

  private void activateMulticastDnsProxyProfile(String tenantId, String venueId,
      String serialNumber, String multicastDnsProxyProfileId,
      MulticastDnsProxyProfile multicastDnsProxyProfile) throws Exception {
    // add
    addMulticastDnsProxyProfile_eda(tenantId, multicastDnsProxyProfile);
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 1),
        () -> assertDdccmVenueBonjourGateway(ddccmOperations, Action.ADD, multicastDnsProxyProfile)
    );
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 1),
        () -> assertCmnMulticastDnsProxyServiceProfile(viewmodelOperations, OpType.ADD, 1,
            null, null, null));
    assertActivityStatusSuccess(ADD_MULTICAST_DNS_PROXY_SERVICE_PROFILE, tenantId);

    // activate
    activateMulticastDnsProxyProfile_eda(tenantId, venueId, serialNumber, multicastDnsProxyProfileId);
    var activateApDdccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(activateApDdccmOperations, 1),
        () -> assertDdccmAp(activateApDdccmOperations, Action.MODIFY, serialNumber, multicastDnsProxyProfileId)
    );
    var activateApViewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(activateApViewmodelOperations, 1),
        () -> assertCmnMulticastDnsProxyServiceProfile(activateApViewmodelOperations, OpType.MOD, 1,
            venueId, multicastDnsProxyProfileId, null));
    assertActivityStatusSuccess(ACTIVATE_MULTICAST_DNS_PROXY_PROFILE, tenantId);
  }

  private String activateMulticastDnsProxyProfile_eda(String tenantId, String venueId, String apSerialNumber,
      String multicastDnsProxyProfileId) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.ACTIVATE_MULTICAST_DNS_PROXY_PROFILE;
    RequestParams params = new RequestParams().addPathVariable("venueId", venueId)
        .addPathVariable("apSerialNumber", apSerialNumber)
        .addPathVariable("multicastDnsProxyProfileId", multicastDnsProxyProfileId);

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, null);

    return requestId;
  }

  private String deactivateMulticastDnsProxyProfile_eda(String tenantId, String venueId, String apSerialNumber,
      String multicastDnsProxyProfileId) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.DEACTIVATE_MULTICAST_DNS_PROXY_PROFILE;
    RequestParams params = new RequestParams().addPathVariable("venueId", venueId)
        .addPathVariable("apSerialNumber", apSerialNumber)
        .addPathVariable("multicastDnsProxyProfileId", multicastDnsProxyProfileId);

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, null);

    return requestId;
  }

  private String addMulticastDnsProxyProfile_eda(String tenantId
      , MulticastDnsProxyProfile mDnsProxyServiceProfile) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.ADD_MULTICAST_DNS_PROXY_PROFILE ;

    sendWifiCfgRequest(tenantId, requestId, action, userName, mDnsProxyServiceProfile);

    return requestId;
  }

  private String updateMulticastDnsProxyProfile_eda(String tenantId,
      String multicastDnsProxyProfileId,
      MulticastDnsProxyProfile mDnsProxyServiceProfile) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    CfgAction action = CfgAction.UPDATE_MULTICAST_DNS_PROXY_PROFILE;
    RequestParams params = new RequestParams()
        .addPathVariable("multicastDnsProxyProfileId", multicastDnsProxyProfileId);

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, mDnsProxyServiceProfile);

    return requestId;
  }

  public MulticastDnsProxyProfile mapViewModel(MulticastDnsProxyServiceProfile mDnsProxyProfile) {
    return MulticastDnsProxyServiceProfileRestCtrl.MulticastDnsProxyServiceProfileMapper.INSTANCE.ServiceMulticastDnsProxyServiceProfile2MulticastDnsProxyProfile(mDnsProxyProfile);
  }

  private void validateRepositoryData(MulticastDnsProxyProfile expected,
      MulticastDnsProxyServiceProfile actual) {

    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getServiceName(), actual.getServiceName());
    assertEquals(expected.getRules().size(), actual.getRules().size());

    for (com.ruckus.cloud.wifi.eda.viewmodel.MulticastDnsProxyServiceRule expectedRule : expected.getRules()) {
      assertTrue(actual.getRules().stream()
          .filter(rule -> rule.getService().equals(expectedRule.getService())
              && rule.getFromVlan().equals(expectedRule.getFromVlan())
              && rule.getToVlan().equals(expectedRule.getToVlan()))
          .findFirst().isPresent());
    }
  }

  private void assertDdccm(List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertCmnViewModelCollector(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void validateDdccmCfgRequestMessages(Map<Action, List<String>> actionIdMap,
      MulticastDnsProxyProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(Operation.class::cast)
            .filteredOn(Operation::hasVenueBonjourGateway)
            .hasSize(actionIdMap.values().stream().mapToInt(List::size).sum()).satisfies(ops -> {
              assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                      Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
              if (actionIdMap.containsKey(Action.DELETE)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.DELETE)
                    .hasSize(actionIdMap.get(Action.DELETE).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getVenueBonjourGateway)
                            .matches(venueBonjourGateway -> actionIdMap.get(Action.DELETE)
                                .contains(venueBonjourGateway.getId())));
              }
              if (actionIdMap.containsKey(Action.ADD)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.ADD)
                    .hasSize(actionIdMap.get(Action.ADD).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getVenueBonjourGateway)
                            .matches(venueBonjourGateway -> actionIdMap.get(Action.ADD)
                                .contains(venueBonjourGateway.getId()))
                            .matches(
                                venueBonjourGateway -> assertDdccmDetail(venueBonjourGateway, payload)));
              }
              if (actionIdMap.containsKey(Action.MODIFY)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.MODIFY)
                    .hasSize(actionIdMap.get(Action.MODIFY).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getVenueBonjourGateway)
                            .matches(venueBonjourGateway -> actionIdMap.get(Action.MODIFY)
                                .contains(venueBonjourGateway.getId()))
                            .matches(
                                venueBonjourGateway -> assertDdccmDetail(venueBonjourGateway, payload)));
              }
            }));
  }

  private boolean assertDdccmDetail(
      com.ruckus.acx.ddccm.protobuf.wifi.VenueBonjourGateway venueBonjourGateway,
      MulticastDnsProxyProfile payload) {
    assertEquals(venueBonjourGateway.getName(), payload.getServiceName());
    assertEquals(venueBonjourGateway.getBonjourGatewayRulesCount(), payload.getRules().size());

    for (com.ruckus.cloud.wifi.eda.viewmodel.MulticastDnsProxyServiceRule expectedRule : payload.getRules()) {
      assertTrue(venueBonjourGateway.getBonjourGatewayRulesList().stream()
          .filter(rule -> rule.getBridgeService().name().equals(expectedRule.getService().name())
              && expectedRule.getFromVlan().equals(rule.getFromVlan())
              && expectedRule.getToVlan().equals(rule.getToVlan()))
          .findFirst().isPresent());
    }

    return true;
  }

  private void validateApDdccmCfgRequestMessages(Map<Action, List<String>> actionIdMap,
      String profileId, String apiFlowName) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(Operation.class::cast)
            .filteredOn(Operation::hasAp)
            .hasSize(actionIdMap.values().stream().mapToInt(List::size).sum()).satisfies(ops -> {
              assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                      Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
              if (actionIdMap.containsKey(Action.MODIFY)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.MODIFY)
                    .hasSize(actionIdMap.get(Action.MODIFY).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getAp)
                            .matches(ap -> actionIdMap.get(Action.MODIFY).contains(ap.getId()),
                                "All action should be Update AP")
                            .matches(
                                ap -> (ACTIVATE_MULTICAST_DNS_PROXY_PROFILE.equals(apiFlowName)
                                    || UPDATE_MULTICAST_DNS_PROXY_PROFILE.equals(apiFlowName)
                                    && ap.getBonjourGateway().getId().equals(profileId))
                                    || (DEACTIVATE_MULTICAST_DNS_PROXY_PROFILE.equals(apiFlowName)
                                    && StringUtils.isEmpty(ap.getBonjourGateway().getId())),
                                "Should be Activate or Deactivate AP bonjour gateway"));
              }
            }));
  }

  private void validateActivityMessages(String apiFlowNames) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> assertThat(
        activityCfgChangeRespMessage.getPayload()).matches(
            msg -> msg.getStatus().equals(Status.OK)).matches(msg -> msg.getStep().equals(apiFlowNames))
        .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(tenantId);  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction,
      List<String> multicastDnsProxyProfileIds, MulticastDnsProxyServiceProfile payload,
      int venueIdsCount) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(multicastDnsProxyProfileIds.size())
            .allMatch(op -> multicastDnsProxyProfileIds.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction)).allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op).extracting(Operations::getDocMap).matches(
                        doc -> multicastDnsProxyProfileIds.contains(
                            doc.get(Key.ID).getStringValue())).matches(
                        doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                    .matches(
                        doc -> payload.getServiceName().equals(doc.get(Key.NAME).getStringValue()),
                        "service name not equal")
                    .matches(doc -> payload.getRules().size() == (doc.get(Key.RULES).getListValue().getValuesCount()),
                        "rule count not equal")
                    .matches(doc -> venueIdsCount == doc.get(VENUE_IDS).getListValue().getValuesCount(),
                        "venueIds count not equal");
              }
            }));
  }

  private void assertDdccmVenueBonjourGateway(List<Operation> operations, Action action,
      MulticastDnsProxyProfile payload) {

    List<com.ruckus.acx.ddccm.protobuf.wifi.VenueBonjourGateway> ddccmVenueBonjourGateway = operations.stream()
        .filter(o -> o.getAction().equals(action))
        .filter(o -> o.getConfigCase().equals(ConfigCase.VENUEBONJOURGATEWAY))
        .map(Operation::getVenueBonjourGateway)
        .collect(Collectors.toList());

    assertEquals(1, ddccmVenueBonjourGateway.size(), "Should have venueBonjourGateway");
    if (!action.equals(Action.DELETE)) {
      assertThat(ddccmVenueBonjourGateway)
          .allSatisfy(op -> assertThat(op)
              .matches(venueBonjourGateway -> venueBonjourGateway.getId().equals(payload.getId()))
              .matches(venueBonjourGateway -> assertDdccmDetail(venueBonjourGateway, payload)));
    }
  }

  private void assertDdccmMoveAp(List<Operation> operations) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.Ap> ddccmAp = operations.stream()
        .filter(o -> o.getAction().equals(Action.MODIFY))
        .filter(o -> o.getConfigCase().equals(ConfigCase.AP))
        .map(Operation::getAp)
        .collect(Collectors.toList());

    assertEquals(2, ddccmAp.size(), """
        Should move Ap, one is triggered from DdccmMulticastDnsProxyServiceProfileApOperationBuilder
        """);
  }

  private void assertDdccmAp(List<Operation> operations, Action action, String serialNumber,
      String multiDnsProxyServiceProfileId) {

    List<com.ruckus.acx.ddccm.protobuf.wifi.Ap> ddccmAp = operations.stream()
        .filter(o -> o.getAction().equals(Action.MODIFY))
        .filter(o -> o.getConfigCase().equals(ConfigCase.AP))
        .map(Operation::getAp)
        .collect(Collectors.toList());

    assertEquals(1, ddccmAp.size(), "Should have Ap");
    if (action.equals(Action.DELETE)) {
      assertThat(ddccmAp)
          .allSatisfy(op -> assertThat(op)
              .matches(ap -> ap.getId().equals(serialNumber)
                  && StringUtils.isEmpty(ap.getBonjourGateway().getId())));
    } else if (action.equals(Action.MODIFY)) {
      assertThat(ddccmAp)
          .allSatisfy(op -> assertThat(op)
              .matches(ap -> ap.getId().equals(serialNumber)
                  && multiDnsProxyServiceProfileId.equals(ap.getBonjourGateway().getId())));
    }
  }

  private void assertCmnMulticastDnsProxyServiceProfile(List<Operations> operations, OpType expectedOpType,
      int expectedOpCount, String venueId, String activatedMultiDnsProxyServiceProfileId,
      String deactivatedMultiDnsProxyServiceProfileId) {
    List<Operations> viewModelOperations = operations.stream()
        .filter(o -> expectedOpType.equals(o.getOpType()))
        .filter(o -> MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .toList();

    assertEquals(expectedOpCount, viewModelOperations.size(), "Should have cmn multicastDnsProxyServiceProfile");
    if (StringUtils.isNotEmpty(activatedMultiDnsProxyServiceProfileId)) {
      assertThat(viewModelOperations)
          .filteredOn(op -> op.getId().equals(activatedMultiDnsProxyServiceProfileId))
          .allSatisfy(op -> assertThat(op)
              .matches(esOp -> esOp.getId().equals(activatedMultiDnsProxyServiceProfileId)
                  && esOp.getDocMap().get(VENUE_IDS).getListValue().getValuesList()
                  .stream().map(Value::getStringValue)
                  .allMatch(venueId::equals)));
    }

    if (StringUtils.isNotEmpty(deactivatedMultiDnsProxyServiceProfileId)) {
      assertThat(viewModelOperations)
          .filteredOn(op -> op.getId().equals(deactivatedMultiDnsProxyServiceProfileId))
          .allSatisfy(op -> assertThat(op)
              .matches(esOp -> esOp.getId().equals(deactivatedMultiDnsProxyServiceProfileId)
                  && esOp.getDocMap().get(VENUE_IDS).getListValue().getValuesList().isEmpty()));
    }
  }


  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_MULTICAST_DNS_PROXY_PROFILE -> OpType.ADD;
      case UPDATE_MULTICAST_DNS_PROXY_PROFILE, ACTIVATE_MULTICAST_DNS_PROXY_PROFILE,
          DEACTIVATE_MULTICAST_DNS_PROXY_PROFILE -> OpType.MOD;
      case DELETE_MULTICAST_DNS_PROXY_PROFILE -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
