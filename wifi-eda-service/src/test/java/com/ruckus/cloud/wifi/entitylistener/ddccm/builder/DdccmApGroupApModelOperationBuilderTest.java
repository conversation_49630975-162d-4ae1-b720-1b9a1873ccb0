package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroupApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.Capabilities;
import com.ruckus.cloud.wifi.eda.servicemodel.CapabilitiesApModel;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BandModeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.FirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.exception.CapabilityNotFoundException;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;

import java.util.Collections;
import java.util.List;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@WifiUnitTest
public class DdccmApGroupApModelOperationBuilderTest {
  @Autowired private DdccmApGroupApModelOperationBuilder ddccmApGroupApModelOperationBuilder;

  @MockBean private FirmwareCapabilityService firmwareCapabilityService;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void beforeEach() throws CapabilityNotFoundException {
    Capabilities capabilities = new Capabilities();
    capabilities.setApModels(List.of(dummyR670Capabilities(), dummyR760Capabilities()));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());
  }

  private static CapabilitiesApModel dummyR760Capabilities() {
    return Generators.capabilitiesApModel("R760")
            .setLedOn(always(true))
            .setLanPorts(list(Generators.capabilitiesLanPort(), 2))
            .generate();
  }

  private static CapabilitiesApModel dummyR670Capabilities() {
    return Generators.capabilitiesApModel("R670")
        .setLedOn(always(true))
        .setLanPorts(list(Generators.capabilitiesLanPort(), 2))
        .generate();
  }

  @Test
  public void testBuildWithR700Model() {
    // Given
    ApGroupApModelSpecificAttributes attributes = new ApGroupApModelSpecificAttributes();
    attributes.setModel("R700");

    // When
    List<Operation> operations =
        ddccmApGroupApModelOperationBuilder.build(new NewTxEntity<>(attributes), emptyTxChanges());

    // Then
    assertEquals(Collections.emptyList(), operations);
  }

  @Test
  public void testBuildWithValidModel(Tenant tenant) {
    // Given
    ApGroup apGroup = new ApGroup();
    apGroup.setId("apGroupId");
    apGroup.setName("TestGroup");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(VenueTestFixture.randomVenue(tenant));

    ApGroupApModelSpecificAttributes attributes = new ApGroupApModelSpecificAttributes();
    attributes.setId(randomId());
    attributes.setTenant(apGroup.getVenue().getTenant());
    attributes.setVenue(apGroup.getVenue());
    attributes.setApGroup(apGroup);
    attributes.setModel("R670");
    attributes.setBandMode(BandModeEnum.TRIPLE);

    apGroup.setModelSpecificAttributes(List.of(attributes));

    // When
    List<Operation> operations =
        ddccmApGroupApModelOperationBuilder.build(new NewTxEntity<>(attributes), emptyTxChanges());

    // Then
    assertThat(operations)
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .satisfies(
            op -> {
              assertThat(op.getId()).isEqualTo(apGroup.getId());
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.hasApGroup()).isTrue();
            })
        .extracting(Operation::getApGroup)
        .isNotNull()
        .satisfies(
            apGroupProto -> {
              assertThat(apGroupProto.getApGroupApModelsCount()).isEqualTo(1);
              assertThat(apGroupProto.getApGroupApModels(0).getModelName()).isEqualTo("R670");
            });
  }

  @Test
  public void testConfigApGroupApModelDefaultBandMode(Tenant tenant) {
    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant);

    ApGroup apGroup = new ApGroup();
    apGroup.setId("apGroupId");
    apGroup.setName("TestGroup");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(venue);

    VenueApModelSpecificAttributes venueApModelSpecificAttributes =
        new VenueApModelSpecificAttributes();
    venueApModelSpecificAttributes.setId(randomId());
    venueApModelSpecificAttributes.setTenant(apGroup.getVenue().getTenant());
    venueApModelSpecificAttributes.setVenue(apGroup.getVenue());
    venueApModelSpecificAttributes.setModel("R760");
    venueApModelSpecificAttributes.setBandMode(BandModeEnum.TRIPLE);

    venue.setModelSpecificAttributes(List.of(venueApModelSpecificAttributes));

    ApGroupApModelSpecificAttributes apGroupApModelSpecificAttributes =
        new ApGroupApModelSpecificAttributes();
    apGroupApModelSpecificAttributes.setId(randomId());
    apGroupApModelSpecificAttributes.setTenant(apGroup.getVenue().getTenant());
    apGroupApModelSpecificAttributes.setVenue(apGroup.getVenue());
    apGroupApModelSpecificAttributes.setApGroup(apGroup);
    apGroupApModelSpecificAttributes.setModel("R670");
    apGroupApModelSpecificAttributes.setBandMode(BandModeEnum.TRIPLE);

    apGroup.setModelSpecificAttributes(List.of(apGroupApModelSpecificAttributes));

    // When
    List<Operation> operations =
        ddccmApGroupApModelOperationBuilder.build(
            new NewTxEntity<>(apGroupApModelSpecificAttributes), emptyTxChanges());

    // Then
    assertThat(operations)
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .satisfies(
            op -> {
              assertThat(op.getId()).isEqualTo(apGroup.getId());
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.hasApGroup()).isTrue();
            })
        .extracting(Operation::getApGroup)
        .isNotNull()
        .satisfies(
            apGroupProto -> {
              assertThat(apGroupProto.getApGroupApModelsCount()).isEqualTo(2);
              assertThat(
                      apGroupProto.getApGroupApModelsList().stream()
                          .anyMatch(
                              model ->
                                  model.getModelName().equals("R760")
                                      && model.getBandCombinationMode().getValue().equals("2-5")))
                  .isTrue();
              assertThat(
                      apGroupProto.getApGroupApModelsList().stream()
                          .anyMatch(
                              model ->
                                  model.getModelName().equals("R670")
                                      && model.getBandCombinationMode().getValue().equals("2-5-6")))
                  .isTrue();
            });
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    public DdccmApGroupApModelOperationBuilder ddccmApGroupApModelOperationBuilder() {
      return new DdccmApGroupApModelOperationBuilder();
    }

    @Bean
    public DdccmApGroupOperationBuilder ddccmApGroupOperationBuilder() {
      return new DdccmApGroupOperationBuilder();
    }
  }
}
