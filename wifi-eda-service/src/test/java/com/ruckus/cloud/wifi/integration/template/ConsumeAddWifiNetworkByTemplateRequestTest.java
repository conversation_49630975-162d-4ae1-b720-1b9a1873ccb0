package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.dpskWifiNetwork;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.guestWifiNetwork;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.RADIUS_SERVER_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.TemplateCreateInstanceOperation.newViewProperty;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.util.TemplateRetriever.retrieveTemplate;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.client.dpsk.DpskApiClient;
import com.ruckus.cloud.wifi.client.dpsk.DpskServiceDto;
import com.ruckus.cloud.wifi.client.guest.GuestClient;
import com.ruckus.cloud.wifi.client.guest.PortalServiceProfileDto;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.QosMapRule;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusNetworkDataQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SortOrderEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.ListValueGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.ApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupRadioGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.eda.viewmodel.WifiWisprPage;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.RadiusRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddWifiNetworkByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  private static final String NETWORK_ID = "networkId";

  @Autowired
  private NetworkRepository networkRepository;
  @Autowired
  private RadiusRepository radiusRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;
  @Autowired
  private DpskApiClient dpskApiClient;
  @Autowired
  private GuestClient guestClient;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void add_pskNetwork(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add PskNetwork with QosMapSet as a template

    PskNetwork pskNetwork = map(pskNetwork("psk").generate());
    pskNetwork.getWlan().getAdvancedCustomization().setQosMapSetEnabled(true);
    pskNetwork = addPskNetwork(pskNetwork, true);
    pskNetwork = repositoryUtil.find(PskNetwork.class, pskNetwork.getId(), mspTenantId, true);

    assertTrue(pskNetwork.getWlan().getAdvancedCustomization().getQosMapSetEnabled());
    final long pskNetworkEnabledRuleCount = pskNetwork.getWlan().getAdvancedCustomization().getQosMapSetOptions()
        .getRules().stream().filter(QosMapRule::getEnabled).count();
    assertTrue(pskNetworkEnabledRuleCount > 0);

    // disable one rule
    pskNetwork.getWlan().getAdvancedCustomization().getQosMapSetOptions().getRules().get(0).setEnabled(false);
    updateNetwork(pskNetwork.getId(), pskNetwork, true);
    PskNetwork addedPskNetwork = repositoryUtil.find(PskNetwork.class, pskNetwork.getId(), mspTenantId, true);

    assertTrue(addedPskNetwork.getWlan().getAdvancedCustomization().getQosMapSetEnabled());
    final long finalMspPskNetworkEnabledRuleCount = addedPskNetwork.getWlan().getAdvancedCustomization()
        .getQosMapSetOptions().getRules().stream().filter(QosMapRule::getEnabled).count();
    assertEquals(pskNetworkEnabledRuleCount - 1, finalMspPskNetworkEnabledRuleCount);

    // create ec network by msp network template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.NETWORK, instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, pskNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    PskNetwork ecPskNetwork =
        repositoryUtil.find(PskNetwork.class, instanceId, ecTenantId, false);
    assertEquals(instanceId, ecPskNetwork.getId());
    assertEquals(addedPskNetwork.getId(), ecPskNetwork.getTemplateId());
    assertEquals(addedPskNetwork.getUpdatedDate().getTime(), ecPskNetwork.getTemplateVersion());
    assertTrue(ecPskNetwork.getWlan().getAdvancedCustomization().getQosMapSetEnabled());
    assertEquals(finalMspPskNetworkEnabledRuleCount,
        ecPskNetwork.getWlan().getAdvancedCustomization().getQosMapSetOptions().getRules()
            .stream().filter(QosMapRule::getEnabled).count());

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecPskNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecPskNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );
  }

  @Test
  public void add_dpskNetwork(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add dpskNetwork as a template

    DpskNetwork dpskNetwork = addDpskWifiNetworkTemplate(map(
        dpskWifiNetwork("dpsk").setUseDpskService(always(true)).generate()));
    String dpskServiceTemplateId = randomId();
    activateDpskServiceOnWifiNetworkTemplate(dpskNetwork.getId(), dpskServiceTemplateId);

    // mock dpskApiClient

    DpskServiceDto instanceDpskService = new DpskServiceDto(randomId(), "dpsk");
    when(dpskApiClient.findByTenantIdAndDpskServiceTemplateId(ecTenantId, dpskServiceTemplateId))
        .thenReturn(Optional.of(instanceDpskService));

    // create ec network by msp network template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, dpskNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    DpskNetwork ecDpskNetwork =
        (DpskNetwork) networkRepository.getByIdAndTenantId(instanceId, ecTenantId);

    assertEquals(instanceDpskService.getId(), ecDpskNetwork.getDpskServiceProfileId());

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecDpskNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecDpskNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );
  }

  @Test
  public void add_guestNetwork_with_click_through_portal_type(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add GuestNetwork as a template
    GuestNetwork guestNetwork = addGuestNetworkTemplate(map(
        guestWifiNetwork().generate()));
    String portalServiceProfileServiceTemplateId = randomId();
    activatePortalServiceProfileTemplateOnWifiNetworkTemplate(guestNetwork.getId(), portalServiceProfileServiceTemplateId);

    // mock guestApiClient
    PortalServiceProfileDto instancePortalServiceProfile = new PortalServiceProfileDto(randomId(), "portalServiceProfile");
    when(guestClient.findByTenantIdAndPortalServiceTemplateId(ecTenantId, portalServiceProfileServiceTemplateId))
        .thenReturn(Optional.of(instancePortalServiceProfile));

    // create ec network by msp network template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, guestNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    GuestNetwork ecGuestNetwork =
        (GuestNetwork) networkRepository.getByIdAndTenantId(instanceId, ecTenantId);

    assertEquals(instancePortalServiceProfile.getId(), ecGuestNetwork.getPortalServiceProfileId());

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecGuestNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecGuestNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );
  }

  @Test
  public void add_guestNetwork_with_wispr_portal_type(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add GuestNetwork as a template
    var guestWifiNetwork = guestWifiNetwork().generate();
    guestWifiNetwork.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    var wifiWisprPage = new WifiWisprPage();
    wifiWisprPage.setCustomExternalProvider(false);
    wifiWisprPage.setCaptivePortalUrl("https://guestPortal.com");
    wifiWisprPage.setExternalProviderName("TestA");
    wifiWisprPage.setExternalProviderRegion("RegionA");
    guestWifiNetwork.getGuestPortal().setWisprPage(wifiWisprPage);
    GuestNetwork guestNetwork = addGuestNetworkTemplate(map(guestWifiNetwork));

    // create ec network by msp network template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, guestNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    GuestNetwork ecGuestNetwork =
        (GuestNetwork) networkRepository.getByIdAndTenantId(instanceId, ecTenantId);

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecGuestNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecGuestNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );
  }

  @Test
  public void add_guestNetwork_with_cloudPath_portal_type(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add GuestNetwork as a template
    Radius radius = RadiusTestFixture.authRadius();
    radius.setName("radius");
    radius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius, mspTenant.getId());
    Radius radiusAdded1 = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius.getPrimary().getIp(), radius.getPrimary().getPort()));
    assertRadiusServers(radius, radiusAdded1);

    Radius ecRadius = RadiusTestFixture.authRadius();
    ecRadius.setName("radius");
    ecRadius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    ecRadius.setTemplateId(radiusAdded1.getId());
    ecRadius.setTemplateVersion(radiusAdded1.getUpdatedDate().getTime());
    repositoryUtil.createOrUpdate(ecRadius, ecTenantId);
    Radius ecRadiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        ecTenantId, ecRadius.getPrimary().getIp(), ecRadius.getPrimary().getPort());
    assertRadiusServers(ecRadius, ecRadiusAdded1);
    var guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(mspTenant,
        n -> {
          n.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
          n.setAuthRadius(radius);
          n.setEnableAuthProxy(true);
          n.setIsTemplate(true);
        });
    guestNetwork = repositoryUtil.createOrUpdate(guestNetwork);

    // create ec network by msp network template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, guestNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName, rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    var ecGuestNetwork = networkRepository.getByIdAndTenantId(instanceId, ecTenantId);

    assertNotNull(ecGuestNetwork.getAuthRadius());

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecGuestNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .anyMatch(o -> ecGuestNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())))
    );
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_NETWORK_RADIUS_ACCOUNTING_TOGGLE)
  public void add_openNetwork_with_radius_acct_with_ff_on(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    var ecTenant = TenantTestFixture.randomTenant((t) -> {});
    var ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add OpenNetwork as a template
    var radius = RadiusTestFixture.acctRadius();
    radius.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius, mspTenant.getId());
    var radiusAdded1 = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius.getPrimary().getIp(), radius.getPrimary().getPort()));
    assertRadiusServers(radius, radiusAdded1);

    var ecRadius = RadiusTestFixture.acctRadius();
    ecRadius.setTemplateId(radiusAdded1.getId());
    ecRadius.setTemplateVersion(radiusAdded1.getUpdatedDate().getTime());
    repositoryUtil.createOrUpdate(ecRadius, ecTenantId);
    Radius ecRadiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        ecTenantId, ecRadius.getPrimary().getIp(), ecRadius.getPrimary().getPort());
    assertRadiusServers(ecRadius, ecRadiusAdded1);
    var openNetwork = NetworkTestFixture.randomOpenNetwork(mspTenant,
        n -> {
          n.setAccountingRadius(radius);
          n.setEnableAccountingProxy(true);
          n.setIsTemplate(true);
        });
    openNetwork = repositoryUtil.createOrUpdate(openNetwork);

    // create ec network by msp network template
    var instanceCreateRequest = new TemplateInstanceCreateRequest();
    var instanceId = randomId();

    var activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    var ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    var mspRequestId = randomTxId();
    var rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, openNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName, rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    var ecOpenNetwork = networkRepository.getByIdAndTenantId(instanceId, ecTenantId);

    assertNotNull(ecOpenNetwork.getAccountingRadius());

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecOpenNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .anyMatch(o -> ecOpenNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())))
    );
  }

  @Test
  public void add_pskNetwork_with_radius_acct_with_ff_off(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    var ecTenant = TenantTestFixture.randomTenant((t) -> {});
    var ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add OpenNetwork as a template
    var radius = RadiusTestFixture.acctRadius();
    radius.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius, mspTenant.getId());
    var radiusAdded1 = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius.getPrimary().getIp(), radius.getPrimary().getPort()));
    assertRadiusServers(radius, radiusAdded1);

    var ecRadius = RadiusTestFixture.acctRadius();
    ecRadius.setTemplateId(radiusAdded1.getId());
    ecRadius.setTemplateVersion(radiusAdded1.getUpdatedDate().getTime());
    repositoryUtil.createOrUpdate(ecRadius, ecTenantId);
    Radius ecRadiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        ecTenantId, ecRadius.getPrimary().getIp(), ecRadius.getPrimary().getPort());
    assertRadiusServers(ecRadius, ecRadiusAdded1);
    var pskNetwork = NetworkTestFixture.randomPskNetwork(mspTenant,
        n -> {
          n.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
          n.getWlan().setPassphrase("passphrase");
          n.setAccountingRadius(radius);
          n.setEnableAccountingProxy(true);
          n.setIsTemplate(true);
        });
    pskNetwork = repositoryUtil.createOrUpdate(pskNetwork);

    // create ec network by msp network template
    var instanceCreateRequest = new TemplateInstanceCreateRequest();
    var instanceId = randomId();

    var activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    var ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    var mspRequestId = randomTxId();
    var rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, pskNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName, rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    var ecPskNetwork = networkRepository.getByIdAndTenantId(instanceId, ecTenantId);

    assertNull(ecPskNetwork.getAccountingRadius());

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecPskNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .anyMatch(o -> ecPskNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())))
    );
  }

  @Test
  public void add_aaaNetwork_with_radius(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create a radius for msp tenant and ec tenant

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius1.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius1, mspTenant.getId(), randomTxId());
    Radius radiusAdded1 = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius1.getPrimary().getIp(), radius1.getPrimary().getPort()));
    assertRadiusServers(radius1, radiusAdded1);

    Radius ecRadius1 = RadiusTestFixture.authRadius();
    ecRadius1.setName("test-radius-1");
    ecRadius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    ecRadius1.setTemplateId(radiusAdded1.getId());
    ecRadius1.setTemplateVersion(radiusAdded1.getUpdatedDate().getTime());
    repositoryUtil.createOrUpdate(ecRadius1, ecTenantId, randomTxId());
    Radius ecRadiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        ecTenantId, ecRadius1.getPrimary().getIp(), ecRadius1.getPrimary().getPort());
    assertRadiusServers(ecRadius1, ecRadiusAdded1);

    // msp tenant add AAANetwork with AuthRadius as a template
    AAANetwork aaaNetwork = NetworkTestFixture.randomAAANetwork(mspTenant, n -> n.setId(randomId()));
    aaaNetwork.setAuthRadius(radiusAdded1);
    aaaNetwork.setIsTemplate(true);
    repositoryUtil.createOrUpdate(aaaNetwork, mspTenantId, randomTxId());

    radiusAdded1 = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius1.getPrimary().getIp(), radius1.getPrimary().getPort()));

    assertRadiusNetworks(radiusAdded1, 1);

    /// create ec network by msp network template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, aaaNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    AAANetwork ecAAANetwork =
        (AAANetwork) networkRepository.getByIdAndTenantId(instanceId, ecTenantId);

    assertEquals(ecRadiusAdded1.getId(), ecAAANetwork.getAuthRadius().getId());
    assertEquals(aaaNetwork.getEnableAuthProxy(), ecAAANetwork.getEnableAuthProxy());
    assertFalse(ecAAANetwork.getEnableAuthProxy());

    var viewOps = receiveViewmodelCollectorOperations(2, ecTenantId);
    log.warn("viewOps: {}", viewOps);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 3),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecAAANetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecAAANetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps, OpType.MOD, ecRadiusAdded1, 1)
    );
  }

  @Test
  public void add_aaaNetwork_with_radius_proxyMode(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create a radius for msp tenant and ec tenant

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius1.setIsTemplate(true);
    repositoryUtil.createOrUpdate(radius1, mspTenant.getId(), randomTxId());
    Radius radiusAdded1 = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius1.getPrimary().getIp(), radius1.getPrimary().getPort()));
    assertRadiusServers(radius1, radiusAdded1);

    Radius ecRadius1 = RadiusTestFixture.authRadius();
    ecRadius1.setName("test-radius-1");
    ecRadius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    ecRadius1.setTemplateId(radiusAdded1.getId());
    ecRadius1.setTemplateVersion(radiusAdded1.getUpdatedDate().getTime());
    repositoryUtil.createOrUpdate(ecRadius1, ecTenantId, randomTxId());
    Radius ecRadiusAdded1 = radiusRepository.findRadiusByTenantIdByPrimary(
        ecTenantId, ecRadius1.getPrimary().getIp(), ecRadius1.getPrimary().getPort());
    assertRadiusServers(ecRadius1, ecRadiusAdded1);

    // msp tenant add AAANetwork with AuthRadius as a template
    AAANetwork aaaNetwork = NetworkTestFixture.randomAAANetwork(mspTenant, n -> n.setId(randomId()));
    aaaNetwork.setAuthRadius(radiusAdded1);
    aaaNetwork.setEnableAuthProxy(true);
    aaaNetwork.setIsTemplate(true);
    repositoryUtil.createOrUpdate(aaaNetwork, mspTenantId, randomTxId());

    radiusAdded1 = retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        mspTenantId, radius1.getPrimary().getIp(), radius1.getPrimary().getPort()));

    assertRadiusNetworks(radiusAdded1, 1);

    /// create ec network by msp network template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, aaaNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    AAANetwork ecAAANetwork =
        (AAANetwork) networkRepository.getByIdAndTenantId(instanceId, ecTenantId);

    assertEquals(ecRadiusAdded1.getId(), ecAAANetwork.getAuthRadius().getId());
    assertEquals(aaaNetwork.getEnableAuthProxy(), ecAAANetwork.getEnableAuthProxy());
    assertTrue(ecAAANetwork.getEnableAuthProxy());

    var viewOps = receiveViewmodelCollectorOperations(2, ecTenantId);
    log.warn("viewOps: {}", viewOps);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 3),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecAAANetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecAAANetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps, OpType.MOD, ecRadiusAdded1, 1)
    );
  }

  @Test
  public void add_openNetwork_withOwe(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenantId, randomTxId());

    //////////
    // add OpenNetwork
    OpenNetwork openNetwork = NetworkTestFixture.randomOpenOweNetwork(mspTenant, n -> { n.setIsTemplate(true); });

    sendWifiCfgRequest(mspTenantId, randomTxId(), CfgAction.ADD_WIFI_NETWORK_TEMPLATE, userName, map(openNetwork));
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_TEMPLATE, mspTenantId);

    var viewOps = receiveViewmodelCollectorOperations(1, mspTenantId);
    log.warn("viewOps: {}", viewOps);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 2), // 2 networks, one is OWE transition slave network
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> openNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> openNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );

    OpenNetwork openNetworkAdded =
        repositoryUtil.find(OpenNetwork.class, openNetwork.getId(), mspTenantId, true);
    assertTrue(openNetworkAdded.getIsOweMaster());
    assertNotNull(openNetworkAdded.getOwePairNetworkId());

    // create ec network by msp network template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.NETWORK, instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, openNetworkAdded.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    OpenNetwork ecOpenNetwork =
        (OpenNetwork) networkRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecOpenNetwork.getId());
    assertEquals(openNetworkAdded.getId(), ecOpenNetwork.getTemplateId());
    assertEquals(openNetworkAdded.getUpdatedDate().getTime(), ecOpenNetwork.getTemplateVersion());
    assertTrue(ecOpenNetwork.getIsOweMaster());
    assertNotNull(ecOpenNetwork.getOwePairNetworkId());

    var ecViewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(ecViewOps, 2),
        () -> assertTrue(ecViewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecOpenNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecOpenNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );
  }

  @Test
  @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
  public void add_guestNetwork_withOwe(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenantId, randomTxId());

    // add GuestNetwork
    var guestNetwork = NetworkTestFixture.randomGuestOweNetwork(mspTenant, n -> {
      n.setIsTemplate(true);
      n.getGuestPortal().setUserSessionTimeout((short) 2);
      n.getGuestPortal().setUserSessionGracePeriod((short) 2);
    });
    guestNetwork.getGuestPortal().setNetwork(guestNetwork);
    sendWifiCfgRequest(mspTenantId, randomTxId(), CfgAction.ADD_WIFI_NETWORK_TEMPLATE, userName, map(guestNetwork));
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_TEMPLATE, mspTenantId);

    var viewOps = receiveViewmodelCollectorOperations(1, mspTenantId);
    log.warn("viewOps: {}", viewOps);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 2), // 2 networks, one is OWE transition slave network
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> guestNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .anyMatch(o -> guestNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())))
    );

    var guestNetworkAdded =
        repositoryUtil.find(GuestNetwork.class, guestNetwork.getId(), mspTenantId, true);
    assertNotNull(guestNetworkAdded);
    assertTrue(guestNetworkAdded.getIsOweMaster());
    assertNotNull(guestNetworkAdded.getOwePairNetworkId());

    // create ec network by msp network template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.NETWORK, instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, guestNetworkAdded.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_WIFI_NETWORK_TEMPLATE_INSTANCE_POST, mspTenantId);

    var ecGuestNetwork =
        (GuestNetwork) networkRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecGuestNetwork.getId());
    assertEquals(guestNetworkAdded.getId(), ecGuestNetwork.getTemplateId());
    assertEquals(guestNetworkAdded.getUpdatedDate().getTime(), ecGuestNetwork.getTemplateVersion());
    assertTrue(ecGuestNetwork.getIsOweMaster());
    assertNotNull(ecGuestNetwork.getOwePairNetworkId());

    var ecViewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(ecViewOps, 2),
        () -> assertTrue(ecViewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecGuestNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .anyMatch(o -> ecGuestNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())))
    );
  }

  @Test
  public void ec_add_network_fail_then_msp_activity_should_fail(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add PskNetwork as a template

    PskNetwork pskNetwork = addPskNetwork(map(pskNetwork("psk").generate()), true);

    // ec tenant also add PskNetwork with the same name to let apply fail in ec tenant
    changeTxCtxTenant(ecTenantId);
    addPskNetwork(map(pskNetwork("psk").generate()));

    // create ec network by msp network template
    // should fail because network "psk" exists

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, pskNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);
    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusFail(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusFail(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void ec_add_network_fail_msp_should_get_activity_fail_because_lacked_overrides(Tenant mspTenant) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add dpskNetwork as a template

    DpskNetwork dpskNetwork = addDpskWifiNetworkTemplate(map(
        dpskWifiNetwork("dpsk").setUseDpskService(always(true)).generate()));
    String dpskServiceTemplateId = randomId();
    activateDpskServiceOnWifiNetworkTemplate(dpskNetwork.getId(), dpskServiceTemplateId);

    /// create ec network by msp network template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    instanceCreateRequest.setOverrides(List.of(newViewProperty("x", "x"))); // incorrect

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.NETWORK,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, dpskNetwork.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlanNotSent(ecTenantId);
    assertActivityStatusFail(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void add_openNetwork_activatedOnApGroup(Tenant mspTenant, @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue) {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, ecTenantId, randomTxId());

    // prepare ApGroups, NetworkVenue, etc.

    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, venue.getTenant().getId(),
            randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup, venue.getTenant().getId(),
            randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.stream()
        .map(networkApGroup -> new NetworkApGroupRadioGenerator()
            .setNetworkApGroup(always(networkApGroup))
            .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
            .generate(1)
        )
        .forEach(radios -> radios.stream().forEach(
            radio -> repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId())));

    // create ec network by msp network template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ApiFlowNames.ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.NETWORK, instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, network.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);

    OpenNetwork ecOpenNetwork =
        (OpenNetwork) networkRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecOpenNetwork.getId());
    assertEquals(network.getId(), ecOpenNetwork.getTemplateId());
    assertEquals(network.getUpdatedDate().getTime(), ecOpenNetwork.getTemplateVersion());

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecOpenNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecOpenNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );
  }

  private void assertRadiusServers(Radius expectedRadius, Radius actualRadius) {
    assertNotNull(actualRadius.getId());
    assertEquals(expectedRadius.getName(), actualRadius.getName());
    assertEquals(expectedRadius.getType(), actualRadius.getType());
    // Validate radius primary
    assertEquals(expectedRadius.getPrimary().getIp(), actualRadius.getPrimary().getIp());
    assertEquals(expectedRadius.getPrimary().getPort(), actualRadius.getPrimary().getPort());
    assertEquals(expectedRadius.getPrimary().getSharedSecret(),
        actualRadius.getPrimary().getSharedSecret());
    // Validate radius secondary
    if (expectedRadius.getSecondary() != null) {
      assertEquals(expectedRadius.getSecondary().getIp(), actualRadius.getSecondary().getIp());
      assertEquals(expectedRadius.getSecondary().getPort(), actualRadius.getSecondary().getPort());
      assertEquals(expectedRadius.getSecondary().getSharedSecret(),
          actualRadius.getSecondary().getSharedSecret());
    }
  }

  @SneakyThrows
  private void assertRadiusNetworks(Radius radius, int radiusNetworksCount) {
    QueryRequest queryParams = new QueryRequest();
    queryParams.setPage(1);
    queryParams.setPageSize(100);
    queryParams.setSortField(NETWORK_ID);
    queryParams.setSortOrder(SortOrderEnum.ASC);
    RadiusNetworkDataQueryResponse result = radiusServiceCtrl.getRadiusNetworks(
        radius.getId(), queryParams);

    assertEquals(radiusNetworksCount, result.getTotalCount().intValue());
    assertEquals(radiusNetworksCount, result.getData().size());
  }

  private void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private static void assertDdccmOps(List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertRadiusIndexViewmodel(List<Operations> operations, OpType opType, Radius radius, int scope) {
    if (opType.equals(OpType.DEL)) {
      assertTrue(operations.stream()
          .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .filter(o -> radius.getId().equals(o.getId()))
          .filter(o -> o.getOpType() == opType)
          .findAny().isPresent());
    } else {
      assertTrue(operations.stream()
          .filter(o -> RADIUS_SERVER_PROFILE_INDEX_NAME.equals(o.getIndex()))
          .filter(o -> radius.getId().equals(o.getId()))
          .filter(o -> o.getOpType() == opType)
          .filter(o -> radius.getName()
              .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
          .filter(
              o -> o.getDocMap().get(EsConstants.Key.NETWORK_IDS).getListValue().getValuesCount()
                  == scope)
          .findAny().isPresent());
    }
  }

  private static void assertDdccmOpCount(List<Operation> operations, Action action, int expectCount,
      Predicate<? super Operation> predicate) {
    assertEquals(expectCount, operations.stream().filter(o -> o.getAction().equals(action)).filter(predicate).count());
  }
}
