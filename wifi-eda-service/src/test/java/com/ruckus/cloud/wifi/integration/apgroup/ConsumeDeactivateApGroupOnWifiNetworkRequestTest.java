package com.ruckus.cloud.wifi.integration.apgroup;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RadioType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanApGroup;
import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.DpskNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.fixture.DPSKNetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.Condition;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("ApGroupTest")
@WifiIntegrationTest
class ConsumeDeactivateApGroupOnWifiNetworkRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class ConsumeDeactivateApGroupOnOpenNetwork {

    private String venueId;
    private String networkId;
    private String networkVenueId;
    private String apGroupId;

    @BeforeEach
    void givenOpenNetworkActivatedOnAloneApGroupPersistedInDb(Tenant tenant, Venue venue,
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork network, NetworkVenue networkVenue) {
      venueId = venue.getId();
      networkId = network.getId();
      networkVenueId = networkVenue.getId();

      final var apGroup = repositoryUtil.createOrUpdate(Generators.apGroup()
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
      apGroupId = apGroup.getId();

      final var networkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(networkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(networkApGroup))
          .generate(StrictRadioTypeEnum.values().length)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId)
          .addPathVariable("apGroupId", apGroupId);
    }

    @Test
    @ApiAction(value = CfgAction.DEACTIVATE_AP_GROUP_ON_WIFI_NETWORK)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities()).isEmpty();
      assertThat(txChanges.getModifiedEntities()).isEmpty();
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(6).extracting(TxEntity::getEntity)
          .haveExactly(1, new Condition<>(NetworkApGroup.class::isInstance,
              "should be instance of NetworkApGroup"))
          .haveExactly(StrictRadioTypeEnum.values().length,
              new Condition<>(NetworkApGroupRadio.class::isInstance,
                  "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, networkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().isEmpty();

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(StrictRadioTypeEnum.values().length)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .filteredOn(op -> op.getAction() == Action.DELETE)
          .filteredOn(Operation::hasWlanApGroup)
          .describedAs("The count of DELETE WlanApGroup operations should be [%d]",
              StrictRadioTypeEnum.values().length)
          .hasSize(StrictRadioTypeEnum.values().length)
          .extracting(Operation::getWlanApGroup)
          .allSatisfy(wlanApGroup -> {
            assertThat(wlanApGroup.getWlanVenueId()).isEqualTo(networkVenueId);
            assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
            assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
          })
          .extracting(WlanApGroup::getRadio)
          .containsExactlyInAnyOrder(RadioType.RADIO24, RadioType.RADIO50,
              RadioType.RADIO50_LOWER, RadioType.RADIO50_UPPER, RadioType.RADIO60);

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(3)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getId()).isEqualTo(apGroupId);
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(txCtx.getTenant()))
                      .containsEntry(Key.ID, ValueUtils.stringValue(apGroupId))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                      .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                      .containsEntry(Key.WIFI_NETWORK_IDS, ValueUtils.nullValue());
                });
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK_DEVICE_GROUP_MAPPING.equals(op.getIndex()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getId()).isNotEmpty();
                  assertThat(op.getOpType()).isEqualTo(OpType.DEL);
                  assertThat(op.getDocCount()).isZero();
                });
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getId()).isNotEmpty();
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TENANT_ID,
                          ValueUtils.stringValue(txCtx.getTenant()))
                      .containsEntry(Key.ID, ValueUtils.stringValue(networkId));
                  assertThat(op.getDocMap().get(Key.VENUE_APGROUPS))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
                      .extracting(
                          ListValue::getValuesList,
                          InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
                      .isEmpty();
                });
          });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(ApiFlowNames.DEACTIVATE_AP_GROUP_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }
  }

  @Nested
  class ConsumeDeactivateApGroupOnOweTransitionNetwork {

    private String venueId;
    private String oweMasterNetworkId;
    private String oweSlaveNetworkId;
    private String oweMasterNetworkVenueId;
    private String oweSlaveNetworkVenueId;
    private String apGroupId;

    @BeforeEach
    void givenOweTransitionMasterAndSlaveNetworksActivatedOnAloneApGroupPersistedInDb(Tenant tenant, Venue venue,
        @OpenNetwork(wlanSecurity = WlanSecurityEnum.OWETransition, isOweMaster = true)
        com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork oweMasterNetwork,
        NetworkVenue oweMasterNetworkVenue) {
      venueId = venue.getId();
      oweMasterNetworkId = oweMasterNetwork.getId();
      oweMasterNetworkVenueId = oweMasterNetworkVenue.getId();

      final var apGroup = repositoryUtil.createOrUpdate(Generators.apGroup()
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
      apGroupId = apGroup.getId();

      final var oweMasterNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(oweMasterNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(oweMasterNetworkApGroup))
          .generate(StrictRadioTypeEnum.values().length)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));

      final var oweSlaveNetwork = repositoryUtil.createOrUpdate(
          NetworkTestFixture.randomOpenNetwork(tenant,
              network -> {
                network.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
                network.setOwePairNetworkId(oweMasterNetworkId);
              }), tenant.getId(), randomTxId());
      oweSlaveNetworkId = oweSlaveNetwork.getId();
      oweMasterNetwork.setOwePairNetworkId(oweSlaveNetworkId);
      repositoryUtil.createOrUpdate(oweMasterNetwork, tenant.getId(), randomTxId());
      final var oweSlaveNetworkVenue = repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(oweSlaveNetwork, venue), tenant.getId(),
          randomTxId());
      oweSlaveNetworkVenueId = oweSlaveNetworkVenue.getId();

      final var oweSlaveNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(oweSlaveNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(oweSlaveNetworkApGroup))
          .generate(StrictRadioTypeEnum.values().length)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", oweMasterNetworkId)
          .addPathVariable("apGroupId", apGroupId);
    }

    @Test
    @ApiAction(value = CfgAction.DEACTIVATE_AP_GROUP_ON_WIFI_NETWORK)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities()).isEmpty();
      assertThat(txChanges.getModifiedEntities()).isEmpty();
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(12).extracting(TxEntity::getEntity)
          .haveExactly(2, new Condition<>(NetworkApGroup.class::isInstance,
              "should be instance of NetworkApGroup"))
          .haveExactly(StrictRadioTypeEnum.values().length * 2,
              new Condition<>(NetworkApGroupRadio.class::isInstance,
                  "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, oweMasterNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().isEmpty();

      assertThat(repositoryUtil.find(NetworkVenue.class, oweSlaveNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().isEmpty();

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(StrictRadioTypeEnum.values().length * 2)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .filteredOn(op -> op.getAction() == Action.DELETE)
          .filteredOn(Operation::hasWlanApGroup)
          .describedAs("The count of DELETE WlanApGroup operations should be [%d]",
              StrictRadioTypeEnum.values().length * 2)
          .hasSize(StrictRadioTypeEnum.values().length * 2)
          .extracting(Operation::getWlanApGroup)
          .allSatisfy(wlanApGroup -> {
            assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
            assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
          })
          .satisfies(wlanApGroups -> {
            assertThat(wlanApGroups)
                .filteredOn(
                    wlanApGroup -> oweMasterNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                .hasSize(StrictRadioTypeEnum.values().length)
                .extracting(WlanApGroup::getRadio)
                .containsExactlyInAnyOrder(RadioType.RADIO24, RadioType.RADIO50,
                    RadioType.RADIO50_LOWER, RadioType.RADIO50_UPPER, RadioType.RADIO60);
            assertThat(wlanApGroups)
                .filteredOn(
                    wlanApGroup -> oweSlaveNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                .hasSize(StrictRadioTypeEnum.values().length)
                .extracting(WlanApGroup::getRadio)
                .containsExactlyInAnyOrder(RadioType.RADIO24, RadioType.RADIO50,
                    RadioType.RADIO50_LOWER, RadioType.RADIO50_UPPER, RadioType.RADIO60);
          });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(5)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getId()).isEqualTo(apGroupId);
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(txCtx.getTenant()))
                      .containsEntry(Key.ID, ValueUtils.stringValue(apGroupId))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                      .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                      .containsEntry(Key.WIFI_NETWORK_IDS, ValueUtils.nullValue());
                });
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK_DEVICE_GROUP_MAPPING.equals(op.getIndex()))
                .isNotEmpty().hasSize(2)
                .allSatisfy(op -> {
                  assertThat(op.getId()).isNotEmpty();
                  assertThat(op.getOpType()).isEqualTo(OpType.DEL);
                  assertThat(op.getDocCount()).isZero();
                });
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
                .isNotEmpty().hasSize(2)
                .allSatisfy(op -> {
                  assertThat(op.getId()).isNotEmpty();
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TENANT_ID,
                          ValueUtils.stringValue(txCtx.getTenant()));
                  assertThat(op.getDocMap().get(Key.VENUE_APGROUPS))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
                      .extracting(
                          ListValue::getValuesList,
                          InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
                      .isEmpty();
                });
          });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(ApiFlowNames.DEACTIVATE_AP_GROUP_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }
  }

  @Nested
  class ConsumeDeactivateApGroupOnOweTransitionGuestNetwork {

    private String venueId;
    private String oweMasterNetworkId;
    private String oweSlaveNetworkId;
    private String oweMasterNetworkVenueId;
    private String oweSlaveNetworkVenueId;
    private String apGroupId;

    @BeforeEach
    void givenOweTransitionMasterAndSlaveNetworksActivatedOnAloneApGroupPersistedInDb(Tenant tenant, Venue venue,
        @GuestNetwork(wlanSecurity = WlanSecurityEnum.OWETransition, isOweMaster = true)
        com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork oweMasterNetwork,
        NetworkVenue oweMasterNetworkVenue) {
      venueId = venue.getId();
      oweMasterNetworkId = oweMasterNetwork.getId();
      oweMasterNetworkVenueId = oweMasterNetworkVenue.getId();

      final var apGroup = repositoryUtil.createOrUpdate(Generators.apGroup()
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
      apGroupId = apGroup.getId();

      final var oweMasterNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(oweMasterNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(oweMasterNetworkApGroup))
          .generate(StrictRadioTypeEnum.values().length)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));

      var oweSlaveNetwork = NetworkTestFixture.randomGuestOweNetwork(tenant,
          network -> {
            network.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
            network.setOwePairNetworkId(oweMasterNetworkId);
          });
      oweSlaveNetwork.getGuestPortal().setNetwork(oweSlaveNetwork);

      oweSlaveNetwork = repositoryUtil.createOrUpdate(oweSlaveNetwork, tenant.getId(),
          randomTxId());
      oweSlaveNetworkId = oweSlaveNetwork.getId();
      oweMasterNetwork.setOwePairNetworkId(oweSlaveNetworkId);
      repositoryUtil.createOrUpdate(oweMasterNetwork, tenant.getId(), randomTxId());
      final var oweSlaveNetworkVenue = repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(oweSlaveNetwork, venue), tenant.getId(),
          randomTxId());
      oweSlaveNetworkVenueId = oweSlaveNetworkVenue.getId();

      final var oweSlaveNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(oweSlaveNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(oweSlaveNetworkApGroup))
          .generate(StrictRadioTypeEnum.values().length)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", oweMasterNetworkId)
          .addPathVariable("apGroupId", apGroupId);
    }

    @Test
    @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
    @ApiAction(value = CfgAction.DEACTIVATE_AP_GROUP_ON_WIFI_NETWORK)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities()).isEmpty();
      assertThat(txChanges.getModifiedEntities()).isEmpty();
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(12).extracting(TxEntity::getEntity)
          .haveExactly(2, new Condition<>(NetworkApGroup.class::isInstance,
              "should be instance of NetworkApGroup"))
          .haveExactly(StrictRadioTypeEnum.values().length * 2,
              new Condition<>(NetworkApGroupRadio.class::isInstance,
                  "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, oweMasterNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().isEmpty();

      assertThat(repositoryUtil.find(NetworkVenue.class, oweSlaveNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().isEmpty();

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(StrictRadioTypeEnum.values().length * 2)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .filteredOn(op -> op.getAction() == Action.DELETE)
          .filteredOn(Operation::hasWlanApGroup)
          .describedAs("The count of DELETE WlanApGroup operations should be [%d]",
              StrictRadioTypeEnum.values().length * 2)
          .hasSize(StrictRadioTypeEnum.values().length * 2)
          .extracting(Operation::getWlanApGroup)
          .allSatisfy(wlanApGroup -> {
            assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
            assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
          })
          .satisfies(wlanApGroups -> {
            assertThat(wlanApGroups)
                .filteredOn(
                    wlanApGroup -> oweMasterNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                .hasSize(StrictRadioTypeEnum.values().length)
                .extracting(WlanApGroup::getRadio)
                .containsExactlyInAnyOrder(RadioType.RADIO24, RadioType.RADIO50,
                    RadioType.RADIO50_LOWER, RadioType.RADIO50_UPPER, RadioType.RADIO60);
            assertThat(wlanApGroups)
                .filteredOn(
                    wlanApGroup -> oweSlaveNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                .hasSize(StrictRadioTypeEnum.values().length)
                .extracting(WlanApGroup::getRadio)
                .containsExactlyInAnyOrder(RadioType.RADIO24, RadioType.RADIO50,
                    RadioType.RADIO50_LOWER, RadioType.RADIO50_UPPER, RadioType.RADIO60);
          });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(5)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getId()).isEqualTo(apGroupId);
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(txCtx.getTenant()))
                      .containsEntry(Key.ID, ValueUtils.stringValue(apGroupId))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                      .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                      .containsEntry(Key.WIFI_NETWORK_IDS, ValueUtils.nullValue());
                });
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK_DEVICE_GROUP_MAPPING.equals(op.getIndex()))
                .isNotEmpty().hasSize(2)
                .allSatisfy(op -> {
                  assertThat(op.getId()).isNotEmpty();
                  assertThat(op.getOpType()).isEqualTo(OpType.DEL);
                  assertThat(op.getDocCount()).isZero();
                });
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
                .isNotEmpty().hasSize(2)
                .allSatisfy(op -> {
                  assertThat(op.getId()).isNotEmpty();
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TENANT_ID,
                          ValueUtils.stringValue(txCtx.getTenant()));
                  assertThat(op.getDocMap().get(Key.VENUE_APGROUPS))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
                      .extracting(
                          ListValue::getValuesList,
                          InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
                      .isEmpty();
                });
          });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(ApiFlowNames.DEACTIVATE_AP_GROUP_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }
  }

  @Nested
  class ConsumeDeactivateApGroupOnDsaeNetwork {

    private String venueId;
    private String dsaeServiceNetworkId;
    private String dsaeOnboardNetworkId;
    private String dsaeServiceNetworkVenueId;
    private String dsaeOnboardNetworkVenueId;
    private String apGroupId;

    @BeforeEach
    void givenDsaeServiceAndOnboardNetworksActivatedOnAloneApGroupPersistedInDb(Tenant tenant, Venue venue,
        @DpskNetwork(wlanSecurity = WlanSecurityEnum.WPA23Mixed, isDsaeServiceNetwork = true)
        com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork dsaeServiceNetwork,
        NetworkVenue dsaeServiceNetworkVenue, AuthRadiusService authRadiusService) {
      venueId = venue.getId();
      dsaeServiceNetworkId = dsaeServiceNetwork.getId();
      dsaeServiceNetworkVenueId = dsaeServiceNetworkVenue.getId();

      final var apGroup = repositoryUtil.createOrUpdate(Generators.apGroup()
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
      apGroupId = apGroup.getId();

      final var dsaeServiceNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(dsaeServiceNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(dsaeServiceNetworkApGroup))
          .generate(StrictRadioTypeEnum.values().length)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));

      final var dsaeOnboardNetwork = repositoryUtil.createOrUpdate(
          DPSKNetworkTestFixture.randomDpskNetwork(tenant, authRadiusService,
              network -> {
                network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
                network.setDsaeNetworkPairId(dsaeServiceNetworkId);
              }), tenant.getId(), randomTxId());
      dsaeOnboardNetworkId = dsaeOnboardNetwork.getId();
      dsaeServiceNetwork.setDsaeNetworkPairId(dsaeServiceNetworkId);
      repositoryUtil.createOrUpdate(dsaeServiceNetwork, tenant.getId(), randomTxId());
      final var dsaeOnboardNetworkVenue = repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(dsaeOnboardNetwork, venue), tenant.getId(),
          randomTxId());
      dsaeOnboardNetworkVenueId = dsaeOnboardNetworkVenue.getId();

      final var dsaeOnboardNetworkApGroup = repositoryUtil.createOrUpdate(Generators.networkApGroup()
          .setApGroup(always(apGroup))
          .setNetworkVenue(always(dsaeOnboardNetworkVenue))
          .generate(), tenant.getId(), randomTxId());

      Generators.networkApGroupRadio()
          .setNetworkApGroup(always(dsaeOnboardNetworkApGroup))
          .generate(StrictRadioTypeEnum.values().length)
          .forEach(networkApGroupRadio ->
              repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId()));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", dsaeServiceNetworkId)
          .addPathVariable("apGroupId", apGroupId);
    }

    @Test
    @ApiAction(value = CfgAction.DEACTIVATE_AP_GROUP_ON_WIFI_NETWORK)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx, CfgAction apiAction) {
      final var txChanges = revisionService.changes(txCtx.getTxId(), txCtx.getTenant(),
          ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
      assertThat(txChanges.getNewEntities()).isEmpty();
      assertThat(txChanges.getModifiedEntities()).isEmpty();
      assertThat(txChanges.getDeletedEntities())
          .isNotEmpty().hasSize(12).extracting(TxEntity::getEntity)
          .haveExactly(2, new Condition<>(NetworkApGroup.class::isInstance,
              "should be instance of NetworkApGroup"))
          .haveExactly(StrictRadioTypeEnum.values().length * 2,
              new Condition<>(NetworkApGroupRadio.class::isInstance,
                  "should be instance of NetworkApGroupRadio"));

      assertThat(repositoryUtil.find(NetworkVenue.class, dsaeServiceNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().isEmpty();

      assertThat(repositoryUtil.find(NetworkVenue.class, dsaeOnboardNetworkVenueId))
          .isNotNull()
          .extracting(NetworkVenue::getNetworkApGroups,
              InstanceOfAssertFactories.list(NetworkApGroup.class))
          .isNotNull().isEmpty();

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(ddccmCfgRequestMessage.getPayload())
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(StrictRadioTypeEnum.values().length * 2)
          .allSatisfy(op -> {
            assertThat(op.getCommonInfo().getRequestId())
                .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
                    txCtx.getTxId())
                .isEqualTo(txCtx.getTxId());
            assertThat(op.getCommonInfo().getTenantId())
                .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
                    txCtx.getTenant())
                .isEqualTo(txCtx.getTenant());
            assertThat(op.getCommonInfo().getSender())
                .describedAs("The value of `sender` field in CommonInfo should be [%s]",
                    ServiceType.WIFI_SERVICE)
                .isEqualTo(ServiceType.WIFI_SERVICE);
          })
          .filteredOn(op -> op.getAction() == Action.DELETE)
          .filteredOn(Operation::hasWlanApGroup)
          .describedAs("The count of DELETE WlanApGroup operations should be [%d]",
              StrictRadioTypeEnum.values().length * 2)
          .hasSize(StrictRadioTypeEnum.values().length * 2)
          .extracting(Operation::getWlanApGroup)
          .allSatisfy(wlanApGroup -> {
            assertThat(wlanApGroup.getApGroupId()).isEqualTo(apGroupId);
            assertThat(wlanApGroup.getVenueId()).isEqualTo(venueId);
          })
          .satisfies(wlanApGroups -> {
            assertThat(wlanApGroups)
                .filteredOn(
                    wlanApGroup -> dsaeServiceNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                .hasSize(StrictRadioTypeEnum.values().length)
                .extracting(WlanApGroup::getRadio)
                .containsExactlyInAnyOrder(RadioType.RADIO24, RadioType.RADIO50,
                    RadioType.RADIO50_LOWER, RadioType.RADIO50_UPPER, RadioType.RADIO60);
            assertThat(wlanApGroups)
                .filteredOn(
                    wlanApGroup -> dsaeOnboardNetworkVenueId.equals(wlanApGroup.getWlanVenueId()))
                .hasSize(StrictRadioTypeEnum.values().length)
                .extracting(WlanApGroup::getRadio)
                .containsExactlyInAnyOrder(RadioType.RADIO24, RadioType.RADIO50,
                    RadioType.RADIO50_LOWER, RadioType.RADIO50_UPPER, RadioType.RADIO60);
          });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(cmnCfgCollectorMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getTenantId()).isEqualTo(txCtx.getTenant());
            assertThat(msg.getRequestId()).isEqualTo(txCtx.getTxId());
          })
          .extracting(ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .isNotEmpty().hasSize(6)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
                .isNotEmpty().singleElement()
                .satisfies(op -> {
                  assertThat(op.getId()).isEqualTo(apGroupId);
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                      .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(txCtx.getTenant()))
                      .containsEntry(Key.ID, ValueUtils.stringValue(apGroupId))
                      .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                      .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                      .containsEntry(Key.WIFI_NETWORK_IDS, ValueUtils.nullValue());
                });
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK_DEVICE_GROUP_MAPPING.equals(op.getIndex()))
                .isNotEmpty().hasSize(2)
                .allSatisfy(op -> {
                  assertThat(op.getId()).isNotEmpty();
                  assertThat(op.getOpType()).isEqualTo(OpType.DEL);
                  assertThat(op.getDocCount()).isZero();
                });
            assertThat(ops)
                .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
                .isNotEmpty().hasSize(1)
                .allSatisfy(op -> {
                  assertThat(op.getId()).isNotEmpty();
                  assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                  assertThat(op.getDocMap())
                      .containsEntry(Key.TENANT_ID,
                          ValueUtils.stringValue(txCtx.getTenant()));
                  assertThat(op.getDocMap().get(Key.VENUE_APGROUPS))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
                      .extracting(
                          ListValue::getValuesList,
                          InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
                      .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
                      .isEmpty();
                });
          });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityCfgChangeRespMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getStatus()).isEqualTo(Status.OK);
            assertThat(msg.getStep()).isEqualTo(ApiFlowNames.DEACTIVATE_AP_GROUP_ON_WIFI_NETWORK);
            assertThat(msg.getEventDate()).isNotNull();
          });

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(txCtx.getTenant(), txCtx.getTxId());
      assertThat(activityImpactedMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThat(activityImpactedMessage.getPayload())
          .satisfies(msg -> {
            assertThat(msg.getDeviceIdsList()).isEmpty();
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          });
    }
  }
}
