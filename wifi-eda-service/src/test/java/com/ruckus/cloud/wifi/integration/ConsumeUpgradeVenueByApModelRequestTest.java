package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.REMOTE_TRIGGER_METHOD;
import static com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.REMOTE_TRIGGER_PARAMETERS;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.RPC;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.sql.Date;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
    FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
public class ConsumeUpgradeVenueByApModelRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private MessageCaptors messageCaptors;

  private final String REQUEST_ID = randomTxId();

  @Test
  public void testUpgradeVenueByApModel(
      Tenant tenant,
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.9") ApVersion targetVersion) {
    // Given
    HashMap<ApVersion, List<String>> firmwareApModelsMap = new HashMap<>();
    firmwareApModelsMap.put(targetVersion, List.of("R550"));
    UpgradeSchedule schedule = this.createUpgradeSchedule(venue, firmwareApModelsMap);
    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(tenant, venue, "R550", targetVersion);
    ApFirmwareUpgradeRequest r310ApFwUpgradeReq = createAndJoinApToVenue(tenant, venue, "R310", targetVersion);

    // When
    messageUtil.sendWifiTrigger(
        venue.getTenant().getId(),
        REQUEST_ID,
        prepareUpgradeVenuePayload(schedule.getId(), venue.getId()));

    // Then
    assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), schedule.getId()), targetVersion.getId());
    assertFalse(isApFirmwareUpgradeRequestUpdated(r310ApFwUpgradeReq.getId(), schedule.getId()));
    messageCaptors.assertThat(
        messageCaptors.getDdccmMessageCaptor(),
        messageCaptors.getDeviceNotificationMessageCaptor()
    ).doesNotSendByTenant(tenant.getId());
    verifyScheduleCmnCfgMessage(tenant, venue);
  }

  @Test
  public void testUpgradeVenueByApModel_twoFirmwareTwoApModels(
      Tenant tenant,
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.9") ApVersion targetVersion1,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.2.103.10") ApVersion targetVersion2) {
    // Given
    HashMap<ApVersion, List<String>> firmwareApModelsMap = new HashMap<>();
    firmwareApModelsMap.put(targetVersion1, List.of("R550"));
    firmwareApModelsMap.put(targetVersion2, List.of("R310"));
    UpgradeSchedule schedule = this.createUpgradeSchedule(venue, firmwareApModelsMap);
    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(tenant, venue, "R550", targetVersion1);
    ApFirmwareUpgradeRequest r310ApFwUpgradeReq = createAndJoinApToVenue(tenant, venue, "R310", targetVersion2);

    // When
    messageUtil.sendWifiTrigger(
        venue.getTenant().getId(),
        REQUEST_ID,
        prepareUpgradeVenuePayload(schedule.getId(), venue.getId()));

    // Then
    assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), schedule.getId()), targetVersion1.getId());
    assertTrue(isApFirmwareUpgradeRequestUpdated(r310ApFwUpgradeReq.getId(), schedule.getId()), targetVersion2.getId());
    messageCaptors.assertThat(
        messageCaptors.getDdccmMessageCaptor(),
        messageCaptors.getDeviceNotificationMessageCaptor()
    ).doesNotSendByTenant(tenant.getId());
    verifyScheduleCmnCfgMessage(tenant, venue);
  }

  @Test
  public void testUpgradeVenueByApModel_ApHasDeleted(
    Tenant tenant,
    Venue venue,
    @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.9") ApVersion targetVersion) {
    // Given
    HashMap<ApVersion, List<String>> firmwareApModelsMap = new HashMap<>();
    firmwareApModelsMap.put(targetVersion, List.of("R550"));
    UpgradeSchedule schedule = this.createUpgradeSchedule(venue, firmwareApModelsMap);
    TenantAvailableApFirmware taaf = new TenantAvailableApFirmware();
    taaf.setId(randomTxId());
    taaf.setTenant(tenant);
    taaf.setApVersion(targetVersion);
    repositoryUtil.createOrUpdate(taaf, tenant.getId(), randomTxId());

    // When
    messageUtil.sendWifiTrigger(
      venue.getTenant().getId(),
      REQUEST_ID,
      prepareUpgradeVenuePayload(schedule.getId(), venue.getId()));

    // Then
    messageCaptors.assertThat(
        messageCaptors.getDdccmMessageCaptor(),
        messageCaptors.getDeviceNotificationMessageCaptor()
    ).doesNotSendByTenant(tenant.getId());
    verifyScheduleCmnCfgMessage(tenant, venue);
  }

  private void verifyScheduleCmnCfgMessage(Tenant tenant, Venue venue) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenant.getId(), REQUEST_ID);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> Index.VENUE.equals(op.getIndex()) && op.getOpType() == OpType.MOD)
        .filteredOn(
            op -> venue.getId().equals(op.getId()) && op.getDocMap().containsKey(Key.NEXT_AP_FIRMWARE_SCHEDULES))
        .isNotEmpty().singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(docMap -> {
          assertThat(docMap.get(Key.NEXT_AP_FIRMWARE_SCHEDULES).getListValue().getValuesList()).isEmpty();
        });
  }

  private ApFirmwareUpgradeRequest createAndJoinApToVenue(Tenant tenant, Venue venue, String apModel, ApVersion apVersion) {
    Ap ap = ApTestFixture.randomAp(venue);
    ap.setModel(apModel);

    VenueCurrentFirmware vcf = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, apModel);
    repositoryUtil.createOrUpdate(vcf, tenant.getId(), randomTxId());

    return repositoryUtil.createOrUpdate(
        newApFirmwareUpgradeRequest(randomId(), venue.getTenant(), venue, ap),
        venue.getTenant().getId(),
        randomTxId());
  }

  private UpgradeSchedule createUpgradeSchedule(Venue venue, HashMap<ApVersion, List<String>> firmwareApModelsMap) {
    ScheduleTimeSlot sts = repositoryUtil.createOrUpdate(this.newScheduleTimeSlot(), venue.getTenant().getId(),
        randomTxId());
    UpgradeSchedule schedule = repositoryUtil.createOrUpdate(
        this.newUpgradeSchedule(sts, firmwareApModelsMap.entrySet().stream().findFirst().get()
            .getKey(), venue), venue.getTenant().getId(), randomTxId());

    firmwareApModelsMap.forEach(
        (apVersion, apModels) -> {
          UpgradeScheduleFirmwareVersion usfv1 = new UpgradeScheduleFirmwareVersion(randomId());
          usfv1.setApFirmwareVersion(apVersion);
          usfv1.setUpgradeSchedule(schedule);
          usfv1.setTenant(venue.getTenant());
          usfv1.setTargetApModels(apModels);
          repositoryUtil.createOrUpdate(usfv1, venue.getTenant().getId(), randomTxId());
        }
    );
    return schedule;
  }

  private ApFirmwareUpgradeRequest newApFirmwareUpgradeRequest(
      String id, Tenant tenant, Venue venue, Ap ap) {
    ApFirmwareUpgradeRequest apFirmwareUpgradeRequest = new ApFirmwareUpgradeRequest();
    apFirmwareUpgradeRequest.setId(id);
    apFirmwareUpgradeRequest.setRequestId(randomId());
    apFirmwareUpgradeRequest.setTenant(tenant);
    apFirmwareUpgradeRequest.setVenueId(venue.getId());
    apFirmwareUpgradeRequest.setSerialNumber(ap.getId());
    apFirmwareUpgradeRequest.setModel(ap.getModel());
    apFirmwareUpgradeRequest.setSourceVersion(venue.getWifiFirmwareVersion().getId());
    apFirmwareUpgradeRequest.setTargetVersion(venue.getWifiFirmwareVersion().getId());
    return apFirmwareUpgradeRequest;
  }

  private Map<String, Object> prepareUpgradeVenuePayload(String scheduleId, String venueId) {
    Map<String, Object> data = new HashMap<>();
    data.put(REMOTE_TRIGGER_METHOD, RPC.UPGRADE_VENUE);
    // schedule id, venue id, version
    data.put(REMOTE_TRIGGER_PARAMETERS, new Object[]{scheduleId, venueId, null});
    return data;
  }

  private ScheduleTimeSlot newScheduleTimeSlot() {
    ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
    Instant startTime = Instant.now();
    sts.setStartDateTime(Date.from(startTime));
    sts.setEndDateTime(Date.from(startTime.plusSeconds(60 * 60 * 2)));
    sts.setTotalCapacityVenue(2500);
    return sts;
  }

  private UpgradeSchedule newUpgradeSchedule(ScheduleTimeSlot timeSlot, ApVersion version, Venue venue) {
    UpgradeSchedule us = new UpgradeSchedule(randomId());
    us.setTimeSlot(timeSlot);
    us.setStatus(UpgradeScheduleStatus.PENDING);
    us.setVersion(version);
    us.setTenant(venue.getTenant());
    us.setVenue(venue);
    return us;
  }

  private boolean isApFirmwareUpgradeRequestUpdated(String apFirmwareUpgradeRequestId, String requestId) {
    return isApFirmwareUpgradeRequestUpdated(apFirmwareUpgradeRequestId, requestId, null);
  }

  private boolean isApFirmwareUpgradeRequestUpdated(String apFirmwareUpgradeRequestId, String requestId, String targetVersion) {
    ApFirmwareUpgradeRequest upgradeRequest =
        repositoryUtil.find(ApFirmwareUpgradeRequest.class, apFirmwareUpgradeRequestId);
    assertNotNull(upgradeRequest);

    return upgradeRequest.getRequestId().equals(requestId)
        && (Objects.isNull(targetVersion) || upgradeRequest.getTargetVersion().equals(targetVersion));
  }
}
