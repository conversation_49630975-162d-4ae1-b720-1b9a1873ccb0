package com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.AP_GROUP_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.VENUE_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.VLAN_POOL_PROFILE_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.WIFI_NETWORK_ID;
import static com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture.randomApGroup;
import static com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture.randomNetworkApGroup;
import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomNetworkTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplate;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.TenantTestFixture.randomTenant;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.VlanPoolTestFixture.randomVlanPool;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupRadioGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.VlanPoolGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.WlanAdvancedCustomizationGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.WlanGenerator;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.ActivateVlanPoolProfileOnApGroupInstanceOperation;
import com.ruckus.cloud.wifi.repository.ApGroupRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.VlanPoolRepository;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;

@ExtendWith(TxCtxExtension.class)
@WifiUnitTest
public class ActivateVlanPoolProfileOnApGroupRequestBuilderTest {

  @Autowired
  private ActivateVlanPoolProfileOnApGroupRequestBuilder unit;
  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private ApGroupRepository apGroupRepository;
  @MockBean
  private VlanPoolRepository vlanPoolRepository;
  @MockBean
  private ActivateVlanPoolProfileOnApGroupInstanceOperation operation;

  private Tenant mspTenant;
  private Tenant ecTenant;
  private NetworkApGroup networkApGroupTemplate;
  private ApGroup apGroupTemplate;
  private ApGroup apGroupInstance;
  private NetworkVenue networkVenueTemplate;
  private Venue venueTemplate;
  private Network networkTemplate;
  private NetworkVenue networkVenueInstance;
  private Venue venueInstance;
  private Network networkInstance;
  private VlanPool vlanPoolTemplate;
  private VlanPool vlanPoolInstance;

  @BeforeEach
  public void setUp() {
    ecTenant = randomTenant((e) -> {
    });
    mspTenant = randomTenant(e -> e.setId(TxCtxHolder.tenantId()));
    unit.setTemplateTenantId(mspTenant.getId());
    unit.setTargetTenantId(ecTenant.getId());
    networkVenueTemplate = randomNetworkVenueTemplate(mspTenant);
    venueTemplate = networkVenueTemplate.getVenue();
    networkTemplate = networkVenueTemplate.getNetwork();
    networkInstance = randomNetworkTemplateInstance(
        networkTemplate.getId(),
        ecTenant);
    venueInstance = randomVenueTemplateInstance(venueTemplate.getId(),
        ecTenant);
    networkVenueInstance = randomNetworkVenueTemplateInstance(networkVenueTemplate.getId(),
        networkInstance, venueInstance);
    apGroupTemplate = randomApGroup(venueTemplate);
    apGroupInstance = randomApGroup(venueInstance);
    networkApGroupTemplate = randomNetworkApGroup(networkVenueTemplate, apGroupTemplate);

    vlanPoolTemplate = randomVlanPool(e -> e.setIsTemplate(true));
    vlanPoolInstance = randomVlanPool(e -> e.setIsTemplate(false));

    networkApGroupTemplate.setNetworkApGroupRadios(List.of(
        new NetworkApGroupRadioGenerator()
            .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
            .setVlanPool(always(vlanPoolTemplate))
            .generate()
    ));

    networkTemplate.setWlan(new WlanGenerator()
        .setAdvancedCustomization(new WlanAdvancedCustomizationGenerator()
            .setVlanPool(always(vlanPoolTemplate))).generate());
    networkInstance.setWlan(new WlanGenerator()
        .setAdvancedCustomization(new WlanAdvancedCustomizationGenerator()
            .setVlanPool(always(vlanPoolInstance))).generate());
  }

  @Test
  void testBuild() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(
            eq(networkTemplate.getId()), eq(ecTenant.getId()));
    doReturn(Optional.of(apGroupInstance))
        .when(apGroupRepository)
        .findByTemplateIdAndTenantId(eq(apGroupTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(vlanPoolInstance))
        .when(vlanPoolRepository)
        .findByTemplateIdAndTenantId(eq(vlanPoolTemplate.getId()),
            eq(ecTenant.getId()));

    var result = unit.build(networkApGroupTemplate);

    assertThat(result).isNotEmpty();

    var request = result.get().request();
    var templateEntity = result.get().templateEntity();

    assertThat(request).satisfies(e -> {
      assertThat(e.getTargetTenantId()).isEqualTo(ecTenant.getId());
      assertThat(e.getInstanceId()).isNotEmpty();
      assertThat(e.getOverrides()).isNull();
    });
    assertThat(request.getExtraPathVariables())
        .containsOnly(entry(VENUE_ID, venueInstance.getId()),
            entry(WIFI_NETWORK_ID, networkInstance.getId()),
            entry(AP_GROUP_ID, apGroupInstance.getId()),
            entry(VLAN_POOL_PROFILE_ID, vlanPoolInstance.getId()));

    assertThat(templateEntity)
        .isEqualTo(networkApGroupTemplate);
  }

  @Test
  void testBuild_ReturnNothing_WhenVlanPoolNotActivated() {
    networkApGroupTemplate.setNetworkApGroupRadios(List.of(
        new NetworkApGroupRadioGenerator()
            .setRadio(always(StrictRadioTypeEnum._2_4_GHz)).generate()));
    var result = unit.build(networkApGroupTemplate);

    assertThat(result).isEmpty();
  }

  @Test
  void testBuild_ApGroupIsNotTemplate() {
    networkApGroupTemplate.setApGroup(new ApGroup());

    assertThrows(CommonException.class, () -> unit.build(networkApGroupTemplate));
  }

  @Test
  void testBuild_VlanPoolIsNotTemplate() {
    final var networkApGroup = randomNetworkApGroup(networkVenueInstance, apGroupInstance);
    networkApGroup.setNetworkApGroupRadios(List.of(
        new NetworkApGroupRadioGenerator()
            .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
            .setVlanPool(new VlanPoolGenerator()
                .setIsTemplate(alwaysFalse()))
            .generate()));

    assertThrows(CommonException.class, () -> unit.build(networkApGroup));
  }

  @Test
  void testBuild_ApGroupInstanceNotFound() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(vlanPoolInstance))
        .when(vlanPoolRepository)
        .findByTemplateIdAndTenantId(eq(vlanPoolTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.empty())
        .when(apGroupRepository)
        .findByTemplateIdAndTenantId(eq(apGroupTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(NullPointerException.class, () -> unit.build(networkApGroupTemplate));
  }

  @Test
  void testBuild_VlanPoolInstanceNotFound() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.empty())
        .when(vlanPoolRepository)
        .findByTemplateIdAndTenantId(eq(vlanPoolTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(apGroupInstance))
        .when(apGroupRepository)
        .findByTemplateIdAndTenantId(eq(apGroupTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(NullPointerException.class, () -> unit.build(networkApGroupTemplate));
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    ActivateVlanPoolProfileOnApGroupRequestBuilder activateVlanPoolProfileOnApGroupRequestBuilder(
        NetworkRepository networkRepository, VlanPoolRepository vlanPoolRepository,
        ApGroupRepository apGroupRepository,
        ActivateVlanPoolProfileOnApGroupInstanceOperation operation) {
      return new ActivateVlanPoolProfileOnApGroupRequestBuilder(networkRepository,
          vlanPoolRepository, apGroupRepository, operation);
    }
  }
}
