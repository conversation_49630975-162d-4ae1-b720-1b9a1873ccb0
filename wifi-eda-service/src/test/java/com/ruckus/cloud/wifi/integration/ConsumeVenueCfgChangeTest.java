package com.ruckus.cloud.wifi.integration;

import com.ruckus.cloud.venue.proto.VenueTemplateEnforcementSettings;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.CLONE_VENUE_AP_GROUP_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_FLOW;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_REQUEST_ID;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_TENANT_ID;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.DEVICE_GROUP;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_PORTS_TEMPLATE_LEGACY;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.common.ActivityCommon;
import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan;
import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanApGroup;
import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.venue.proto.Action;
import com.ruckus.cloud.venue.proto.Address;
import com.ruckus.cloud.venue.proto.Operation;
import com.ruckus.cloud.venue.proto.VenueCloneSettings;
import com.ruckus.cloud.venue.proto.VenueEvent;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractTenantAwareTemplateBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpPoolVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceAp;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicyVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfileNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApIotSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BandModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.ListValueGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.ApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.NetworkApGroupRadioGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantAvailableApFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueApModelSpecificAttributesTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VlanPoolTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.ActivityCfgChangeMessageCaptor;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.header.Header;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
class ConsumeVenueCfgChangeTest {

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private MessageCaptors messageCaptors;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  private NetworkVenueRepository networkVenueRepository;

  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @Test
  void testAddVenue(Tenant tenant) throws Exception {
    String venueId = randomId();
    VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(tenant.getId())
        .addOperation(Operation.newBuilder().setAction(Action.ADD)
            .setVenue(com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venueId).build()))
        .build();

    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(tenant.getId(), requestId, event);

    Venue venue = repositoryUtil.find(Venue.class, venueId);
    assertThat(venue).isNotNull();

    var record = messageCaptors.getDdccmMessageCaptor().getValue(txCtxExtension.getTenantId(),
        requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApGroup));

    var activity = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);
    assertThat(activity).isNotNull();
  }

  @Test
  void deleteVenue(Venue venue, ApGroup apGroup, Ap ap) throws Exception {
    VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    assertThat(repositoryUtil.find(Venue.class, venue.getId())).isNotNull();
    assertThat(repositoryUtil.find(ApGroup.class, apGroup.getId())).isNotNull();
    assertThat(repositoryUtil.find(Ap.class, ap.getId())).isNotNull();

    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(txCtxExtension.getTenantId(),
        requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApGroup))
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp));

    var activity = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);
    assertThat(activity).isNotNull();
  }

  @Test
  void testAddVenueTemplate(Tenant tenant) throws Exception {
    String venueId = randomId();
    VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(tenant.getId())
        .addOperation(Operation.newBuilder().setAction(Action.ADD)
            .setVenue(com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venueId)
                .setIsTemplate(true)
                .build()))
        .build();

    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(tenant.getId(), requestId, event);

    Venue venue = repositoryUtil.find(Venue.class, venueId);
    assertThat(venue).isNotNull();

    var record = messageCaptors.getDdccmMessageCaptor().getValue(txCtxExtension.getTenantId(),
        requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApGroup));
    assertThat(venue.getIsTemplate()).isTrue();
  }

  @Tag("VxLanTunnelFeatureTest")
  @Test
  void deleteVenueWithNsgTunnelProfileNetworks(Tenant tenant, Venue venue) {
    final var dpskNetwork1 = network(DpskNetwork.class).setName(randomString()).generate();
    dpskNetwork1.getWlan().setNetwork(dpskNetwork1);
    final var dpskNetwork2 = network(DpskNetwork.class).setName(randomString()).generate();
    dpskNetwork2.getWlan().setNetwork(dpskNetwork2);
    repositoryUtil.createOrUpdate(dpskNetwork1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(dpskNetwork2, tenant.getId(), randomTxId());

    final var tunnelProfile = Generators.tunnelProfile().generate();
    repositoryUtil.createOrUpdate(tunnelProfile, tenant.getId(), randomTxId());

    TunnelProfileNetwork tunnelProfileNetwork1 = Generators.tunnelProfileNetwork()
        .setNetworkSegmentationVenue(always(venue)).generate();
    TunnelProfileNetwork tunnelProfileNetwork2 = Generators.tunnelProfileNetwork()
        .setNetworkSegmentationVenue(always(venue)).generate();
    tunnelProfileNetwork1.setNetwork(dpskNetwork1);
    tunnelProfileNetwork2.setNetwork(dpskNetwork2);
    tunnelProfileNetwork1.setTunnelProfile(tunnelProfile);
    tunnelProfileNetwork2.setTunnelProfile(tunnelProfile);
    repositoryUtil.createOrUpdate(tunnelProfileNetwork1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(tunnelProfileNetwork2, tenant.getId(), randomTxId());

    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    // When
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), randomTxId(), event);

    // Then
    assertThat(
        repositoryUtil.find(TunnelProfileNetwork.class, tunnelProfileNetwork1.getId())).isNull();
    assertThat(
        repositoryUtil.find(TunnelProfileNetwork.class, tunnelProfileNetwork2.getId())).isNull();
  }

  @Tag("VxLanTunnelFeatureTest")
  @Test
  void deleteVenueWithCfTunnelProfileNetworks(Tenant tenant, Venue venue) {
    final var pskNetwork = network(PskNetwork.class).setName(randomString()).generate();
    pskNetwork.getWlan().setNetwork(pskNetwork);
    final var guestNetwork = network(GuestNetwork.class).setName(randomString()).generate();
    guestNetwork.getWlan().setNetwork(guestNetwork);
    final var dpskNetwork = network(DpskNetwork.class).setName(randomString()).generate();
    dpskNetwork.getWlan().setNetwork(dpskNetwork);
    repositoryUtil.createOrUpdate(pskNetwork, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());

    final var tunnelProfile = Generators.tunnelProfile().generate();
    repositoryUtil.createOrUpdate(tunnelProfile, tenant.getId(), randomTxId());

    TunnelProfileNetwork tunnelProfileNetwork1 = Generators.tunnelProfileNetwork()
        .setCentralizedForwardingServiceVenue(always(venue)).generate();
    TunnelProfileNetwork tunnelProfileNetwork2 = Generators.tunnelProfileNetwork()
        .setCentralizedForwardingServiceVenue(always(venue)).generate();
    TunnelProfileNetwork tunnelProfileNetwork3 = Generators.tunnelProfileNetwork()
        .setCentralizedForwardingServiceVenue(always(venue)).generate();
    tunnelProfileNetwork1.setNetwork(pskNetwork);
    tunnelProfileNetwork2.setNetwork(guestNetwork);
    tunnelProfileNetwork3.setNetwork(dpskNetwork);
    tunnelProfileNetwork1.setTunnelProfile(tunnelProfile);
    tunnelProfileNetwork2.setTunnelProfile(tunnelProfile);
    tunnelProfileNetwork3.setTunnelProfile(tunnelProfile);
    repositoryUtil.createOrUpdate(tunnelProfileNetwork1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(tunnelProfileNetwork2, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(tunnelProfileNetwork3, tenant.getId(), randomTxId());

    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    // When
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), randomTxId(), event);

    // Then
    assertThat(
        repositoryUtil.find(TunnelProfileNetwork.class, tunnelProfileNetwork1.getId())).isNull();
    assertThat(
        repositoryUtil.find(TunnelProfileNetwork.class, tunnelProfileNetwork2.getId())).isNull();
    assertThat(
        repositoryUtil.find(TunnelProfileNetwork.class, tunnelProfileNetwork3.getId())).isNull();
  }

  @Test
  void deleteVenueWithClientIsolationAllowlist(final Tenant tenant, final Venue venue,
      final NetworkVenue networkVenue) {
    // Given
    final var allowlist = Generators.clientIsolationAllowlist()
        .generate(list -> {
//          list.setVenue(venue); // no this in r1
          list.getAllowlist().forEach(entry -> entry.setClientIsolationAllowlist(list));
        });
    repositoryUtil.createOrUpdate(allowlist, tenant.getId(), randomTxId());

    networkVenue.setClientIsolationAllowlist(allowlist);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    venue.setEnableClientIsolationAllowlist(true);
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    // When
    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    // Then no delete ClientIsolationAllowlist in r1
    assertThat(repositoryUtil.find(ClientIsolationAllowlist.class, allowlist.getId())).isNotNull();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 3").hasSize(3)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> assertSoftly(softly -> {
              softly.assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE Venue operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", venue.getId())
                  .matches(op -> venue.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                  .satisfies(ddccmVenue -> {
                    assertThat(ddccmVenue.getId())
                        .as("The id in Venue message should be %s", venue.getId())
                        .isEqualTo(venue.getId());
                  });
              softly.assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE WlanVenue operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", networkVenue.getId())
                  .matches(op -> networkVenue.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                  .satisfies(ddccmWlanVenue -> {
                    assertThat(ddccmWlanVenue.getVenueId())
                        .as("The venueId in WlanVenue message should be %s", venue.getId())
                        .isEqualTo(venue.getId());
                  });
            })));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 3").hasSize(3)
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.DEL)
                  .as("The DEL networkvenuemapping operation count should be 1")
                  .hasSize(1)
                  .singleElement()
                  .extracting(Operations::getId)
                  .as("The operation id should be %s", networkVenue.getId())
                  .isEqualTo(networkVenue.getId());
              assertThat(ops)
                  // Produced by ClientIsolationProfileCmnCfgCollectorOperationBuilder
                  .filteredOn(op -> EsConstants.Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME.equals(
                      op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD %s operation count should be 1",
                      EsConstants.Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME).hasSize(1)
                  .singleElement()
                  .matches(o ->
                      o.getDocMap().get(EsConstants.Key.VENUE_IDS).getListValue().getValuesCount()
                          == 0)
                  .matches(o ->
                      o.getDocMap().get(EsConstants.Key.ACTIVATIONS).getListValue().getValuesCount()
                          == 0)
                  .extracting(Operations::getId)
                  .as("The operation id should be %s", allowlist.getId())
                  .isEqualTo(allowlist.getId());
              assertThat(ops)
                  .filteredOn(op -> Index.NETWORK.equals(op.getIndex()))
                  .isNotEmpty().singleElement()
                  .satisfies(op -> {
                    assertThat(op.getId()).isNotEmpty();
                    assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                    assertThat(op.getDocMap())
                        .containsEntry(Key.TENANT_ID,
                            ValueUtils.stringValue(venue.getTenant().getId()))
                        .containsEntry(Key.ID,
                            ValueUtils.stringValue(networkVenue.getNetwork().getId()));
                    assertThat(op.getDocMap().get(Key.VENUE_APGROUPS))
                        .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
                        .extracting(
                            ListValue::getValuesList,
                            InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
                        .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
                        .isEmpty();
                  });
            }));
  }

  @Test
  void deleteVenueWithDhcpConfigServiceProfile(final Tenant tenant, final Venue venue,
      final ApGroup apGroup, final Ap ap) {
    // Given
    final var dhcpConfig = repositoryUtil.createOrUpdate(
        Generators.dhcpConfigServiceProfile().generate(), tenant.getId(), randomTxId());
    final var dhcpPool = repositoryUtil.createOrUpdate(
        Generators.dhcpServiceProfile().setDhcpConfigServiceProfile(always(dhcpConfig)).generate(),
        tenant.getId(), randomTxId());

    final var dhcpPoolVenue = new DhcpPoolVenue();
    dhcpPoolVenue.setDhcpPool(dhcpPool);
    dhcpPoolVenue.setVenue(venue);
    repositoryUtil.createOrUpdate(dhcpPoolVenue, tenant.getId(), randomTxId());

    venue.getDhcpServiceSetting().setEnabled(true);
    venue.setDhcpConfigServiceProfile(dhcpConfig);
    venue.setDhcpPoolVenues(List.of(dhcpPoolVenue));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

    DhcpServiceAp dhcpServiceAp = new DhcpServiceAp();
    dhcpServiceAp.setVenue(venue);
    dhcpServiceAp.setSerialNumber(ap.getId());
    dhcpServiceAp = repositoryUtil.createOrUpdate(dhcpServiceAp, tenant.getId(), randomTxId());

    ap.setApGroup(apGroup);
    repositoryUtil.createOrUpdate(ap, tenant.getId(), randomTxId());

    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    // When
    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    // Then
    assertThat(repositoryUtil.find(DhcpServiceAp.class, dhcpServiceAp.getId())).isNull();
    assertThat(repositoryUtil.find(Venue.class, venue.getId())).isNull();
    assertThat(repositoryUtil.find(Ap.class, ap.getId())).isNull();
    assertThat(repositoryUtil.find(ApGroup.class, apGroup.getId())).isNull();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 5").hasSize(5)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE Venue operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", venue.getId())
                  .matches(op -> venue.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                  .satisfies(ddccmVenue -> {
                    assertThat(ddccmVenue.getId())
                        .as("The venue id should be %s", venue.getId())
                        .isEqualTo(venue.getId());
                  });
            }));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 3").hasSize(3)
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  .filteredOn(op -> EsConstants.Index.DEVICE.equals(op.getIndex())).hasSize(1);
              assertThat(ops)
                  .filteredOn(op -> DEVICE_GROUP.equals(op.getIndex())).hasSize(1);
              assertThat(ops)
                  // Produced by DhcpConfigServiceProfileCmnCfgCollectorOperationBuilder
                  .filteredOn(
                      op -> Index.DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD %s operation count should be 1",
                      Index.DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME).hasSize(1)
                  .singleElement()
                  .extracting(Operations::getDocMap)
                  .satisfies(doc -> assertSoftly(softly -> {
                    softly.assertThat(doc.get(EsConstants.Key.TENANT_ID).getStringValue())
                        .isEqualTo(tenant.getId());
                    softly.assertThat(doc.get(EsConstants.Key.ID).getStringValue())
                        .isEqualTo(dhcpConfig.getId());
                    softly.assertThat(doc.get(EsConstants.Key.NAME).getStringValue())
                        .isEqualTo(dhcpConfig.getServiceName());
                    softly.assertThat(doc.get(EsConstants.Key.TECHNOLOGY).getStringValue())
                        .isEqualTo(EsConstants.Value.WIFI.getStringValue());

                    // dhcpPools
                    softly.assertThat(doc.get(EsConstants.Key.DHCP_POOLS).getListValue())
                        .matches(listValue -> listValue.getValuesCount() == 1)
                        .extracting(ListValue::getValuesList).asList()
                        .hasSize(1)
                        .singleElement()
                        .extracting(Value.class::cast)
                        .extracting(Value::getStructValue)
                        .matches(struct -> struct.getFieldsCount() == 4)
                        .extracting(Struct::getFieldsMap)
                        .satisfies(dhcpPoolMap -> assertSoftly(alsoSoftly -> {
                          alsoSoftly.assertThat(
                                  dhcpPoolMap.get(EsConstants.Key.NAME).getStringValue())
                              .isEqualTo(dhcpPool.getName());
                          alsoSoftly.assertThat(
                                  dhcpPoolMap.get(EsConstants.Key.DHCP_POOL_START_ADDRESS)
                                      .getStringValue())
                              .isEqualTo(dhcpPool.getStartIpAddress());
                          alsoSoftly.assertThat(
                                  dhcpPoolMap.get(EsConstants.Key.DHCP_POOL_END_ADDRESS)
                                      .getStringValue())
                              .isEqualTo(dhcpPool.getEndIpAddress());
                          alsoSoftly.assertThat(
                                  dhcpPoolMap.get(EsConstants.Key.DHCP_POOL_NETWORK_ADDRESS)
                                      .getStringValue())
                              .isEqualTo(dhcpPool.getSubnetAddress());
                        }));

                    // venueIds
                    softly.assertThat(
                            doc.get(EsConstants.Key.VENUE_IDS).getListValue().getValuesList())
                        .isEmpty();
                  }));
            }));
  }

  @Test
  void deleteVenueWithMulticastDnsProxyServiceProfile(final Tenant tenant, final Ap ap) {
    // Given
    final ApGroup apGroup = ap.getApGroup();
    final Venue venue = apGroup.getVenue();

    final var mDnsProxyProfile = Generators.multicastDnsProxyServiceProfile()
        .generate(profile -> profile.getRules()
            .forEach(rule -> rule.setMulticastDnsProxyServiceProfile(profile)));

    final var mDnsProxyProfileAp = new MulticastDnsProxyServiceProfileAp();
    mDnsProxyProfileAp.setMulticastDnsProxyServiceProfile(mDnsProxyProfile);
    mDnsProxyProfileAp.setAp(ap);

    repositoryUtil.createOrUpdate(mDnsProxyProfile, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(mDnsProxyProfileAp, tenant.getId(), randomTxId());

    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    // When
    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    // Then
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 5").hasSize(5)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE Venue operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", venue.getId())
                  .matches(op -> venue.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                  .satisfies(ddccmVenue -> {
                    assertThat(ddccmVenue.getId())
                        .as("The venue id should be %s", venue.getId())
                        .isEqualTo(venue.getId());
                  });
              assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApGroup)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE ApGroup operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", apGroup.getId())
                  .matches(op -> apGroup.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApGroup)
                  .satisfies(ddccmApGroup -> assertSoftly(softly -> {
                    softly.assertThat(ddccmApGroup.getId())
                        .as("The id in ApGroup message should be %s", apGroup.getId())
                        .isEqualTo(apGroup.getId());
                    softly.assertThat(ddccmApGroup.getVenueId())
                        .as("The venueId in ApGroup message should be %s", venue.getId())
                        .isEqualTo(venue.getId());
                  }));
              assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE Ap operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", ap.getId())
                  .matches(op -> ap.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
                  .satisfies(ddccmAp -> assertSoftly(softly -> {
                    softly.assertThat(ddccmAp.getId())
                        .as("The id in Ap message should be %s", ap.getId())
                        .isEqualTo(ap.getId());
                    softly.assertThat(ddccmAp.getApGroupId())
                        .as("The apGroupId in ApGroup message should be %s", apGroup.getId())
                        .isEqualTo(apGroup.getId());
                  }));
              assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApTunnelCertInfo)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE ApTunnelCertInfo operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", ap.getId())
                  .matches(op -> ap.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApTunnelCertInfo)
                  .satisfies(ddccmApTunnelCertInfo -> {
                    assertThat(ddccmApTunnelCertInfo.getId())
                        .as("The id in ApTunnelCertInfo message should be %s", ap.getId())
                        .isEqualTo(ap.getId());
                  });
            }));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 3").hasSize(3)
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by ApCmnCfgCollectorOperationBuilder
                  .filteredOn(op -> EsConstants.Index.DEVICE.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.DEL)
                  .as("The DEL device operation count should be 1")
                  .hasSize(1)
                  .singleElement()
                  .extracting(Operations::getId)
                  .as("The operation id should be %s", ap.getId())
                  .isEqualTo(ap.getId());
              assertThat(ops)
                  // Produced by ApGroupCmnCfgCollectorOperationBuilder
                  .filteredOn(op -> DEVICE_GROUP.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.DEL)
                  .as("The DEL devicegroup operation count should be 1")
                  .hasSize(1)
                  .singleElement()
                  .extracting(Operations::getId)
                  .as("The operation id should be %s", apGroup.getId())
                  .isEqualTo(apGroup.getId());
              assertThat(ops)
                  // Produced by MulticastDnsProxyServiceProfileCmnCfgCollectorOperationBuilder
                  .filteredOn(
                      op -> EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME.equals(
                          op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD %s operation count should be 1",
                      EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME).hasSize(1)
                  .singleElement()
                  .extracting(Operations::getDocMap)
                  .satisfies(doc -> assertSoftly(softly -> {
                    softly.assertThat(doc.get(EsConstants.Key.TENANT_ID).getStringValue())
                        .isEqualTo(tenant.getId());
                    softly.assertThat(doc.get(EsConstants.Key.ID).getStringValue())
                        .isEqualTo(mDnsProxyProfile.getId());
                    softly.assertThat(doc.get(EsConstants.Key.NAME).getStringValue())
                        .isEqualTo(mDnsProxyProfile.getServiceName());

                    // rules
                    final var rules = mDnsProxyProfile.getRules();
                    softly.assertThat(doc.get(EsConstants.Key.RULES).getListValue())
                        .matches(listValue -> listValue.getValuesCount() == rules.size())
                        .extracting(ListValue::getValuesList).asList()
                        .hasSize(rules.size())
                        .first()
                        .extracting(Value.class::cast)
                        .extracting(Value::getStructValue)
                        .matches(
                            struct -> struct.getFieldsCount() == 5 || struct.getFieldsCount() == 7)
                        .extracting(Struct::getFieldsMap)
                        .satisfies(ruleMap -> assertSoftly(alsoSoftly -> {
                          alsoSoftly.assertThat(
                                  ruleMap.get(EsConstants.Key.RULES_RULE_INDEX).getNumberValue())
                              .isEqualTo(rules.get(0).getRuleIndex().doubleValue());
                          alsoSoftly.assertThat(
                                  ruleMap.get(EsConstants.Key.RULES_ENABLED).getBoolValue())
                              .isEqualTo(rules.get(0).getEnabled());
                          alsoSoftly.assertThat(
                                  ruleMap.get(EsConstants.Key.RULES_SERVICE).getStringValue())
                              .isEqualTo(rules.get(0).getService().name());
                          if (BridgeServiceEnum.OTHER.equals(rules.get(0).getService())) {
                            alsoSoftly.assertThat(
                                    ruleMap.get(EsConstants.Key.RULES_MDNS_NAME).getStringValue())
                                .isEqualTo(rules.get(0).getMdnsName());
                            alsoSoftly.assertThat(
                                    ruleMap.get(EsConstants.Key.RULES_MDNS_PROTOCOL).getStringValue())
                                .isEqualTo(rules.get(0).getMdnsProtocol().name());
                          }
                          alsoSoftly.assertThat(
                                  ruleMap.get(EsConstants.Key.RULES_FROM_VLAN).getNumberValue())
                              .isEqualTo(rules.get(0).getFromVlan().doubleValue());
                          alsoSoftly.assertThat(
                                  ruleMap.get(EsConstants.Key.RULES_TO_VLAN).getNumberValue())
                              .isEqualTo(rules.get(0).getToVlan().doubleValue());
                        }));

                    // venueIds
                    softly.assertThat(
                            doc.get(EsConstants.Key.VENUE_IDS).getListValue().getValuesList())
                        .isEmpty();
                  }));
            }));
  }

  @Test
  void deleteVenueWithRogueClassificationPolicy(final Tenant tenant, final Venue venue) {
    // Given
    final var roguePolicy = Generators.rogueClassificationPolicy()
        .generate(policy -> policy.getRules()
            .forEach(rule -> rule.setRogueClassificationPolicy(policy)));
    final var roguePolicyVenue = new RogueClassificationPolicyVenue(randomId());
    roguePolicyVenue.setRogueClassificationPolicy(roguePolicy);
    roguePolicyVenue.setVenue(venue);

    repositoryUtil.createOrUpdate(roguePolicy, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(roguePolicyVenue, tenant.getId(), randomTxId());

    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    // When
    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    // Then
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 2").hasSize(2)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE Venue operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", venue.getId())
                  .matches(op -> venue.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                  .satisfies(ddccmVenue -> {
                    assertThat(ddccmVenue.getId())
                        .as("The venue id should be %s", venue.getId())
                        .isEqualTo(venue.getId());
                  });
            }));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 1").hasSize(1)
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by RogueApDetectionProfileCmnCfgCollectorOperationBuilder
                  .filteredOn(op -> EsConstants.Index.ROGUE_AP_POLICY_PROFILE_INDEX_NAME.equals(
                      op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD %s operation count should be 1",
                      EsConstants.Index.ROGUE_AP_POLICY_PROFILE_INDEX_NAME).hasSize(1)
                  .singleElement()
                  .extracting(Operations::getDocMap)
                  .satisfies(doc -> assertSoftly(softly -> {
                    softly.assertThat(doc.get(EsConstants.Key.ID).getStringValue())
                        .isEqualTo(roguePolicy.getId());
                    softly.assertThat(doc.get(EsConstants.Key.NAME).getStringValue())
                        .isEqualTo(roguePolicy.getName());
                    softly.assertThat(doc.get(EsConstants.Key.DESCRIPTION).getStringValue())
                        .isEqualTo(StringUtils.defaultString(roguePolicy.getDescription(),
                            StringUtils.EMPTY));
                    softly.assertThat(doc.get(EsConstants.Key.TENANT_ID).getStringValue())
                        .isEqualTo(tenant.getId());
                    softly.assertThat(doc.get(EsConstants.Key.NUM_OF_RULES).getNumberValue())
                        .isEqualTo(roguePolicy.getRules().size());
                    softly.assertThat(
                            doc.get(EsConstants.Key.VENUE_IDS).getListValue().getValuesList())
                        .isEmpty();
                  }));
            }));
  }

  @Test
  void deleteVenueWithSyslogServerProfile(final Tenant tenant, final Venue venue) {
    // Given
    final var syslogProfile = repositoryUtil.createOrUpdate(
        Generators.syslogServerProfile().generate(), tenant.getId(), randomTxId());

    venue.setSyslogServerProfile(syslogProfile);
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    // When
    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    // Then
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 2").hasSize(2)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE Venue operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", venue.getId())
                  .matches(op -> venue.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                  .satisfies(ddccmVenue -> {
                    assertThat(ddccmVenue.getId())
                        .as("The venue id should be %s", venue.getId())
                        .isEqualTo(venue.getId());
                  });
            }));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 1").hasSize(1)
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by SyslogServerProfileCmnCfgCollectorOperationBuilder
                  .filteredOn(op -> EsConstants.Index.SYSLOG_SERVER_PROFILE_INDEX_NAME.equals(
                      op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD %s operation count should be 1",
                      EsConstants.Index.SYSLOG_SERVER_PROFILE_INDEX_NAME).hasSize(1)
                  .singleElement()
                  .extracting(Operations::getDocMap)
                  .satisfies(doc -> assertSoftly(softly -> {
                    softly.assertThat(doc.get(EsConstants.Key.TENANT_ID).getStringValue())
                        .isEqualTo(tenant.getId());
                    softly.assertThat(doc.get(EsConstants.Key.ID).getStringValue())
                        .isEqualTo(syslogProfile.getId());
                    softly.assertThat(doc.get(EsConstants.Key.NAME).getStringValue())
                        .isEqualTo(syslogProfile.getName());
                    softly.assertThat(doc.get(EsConstants.Key.FLOW_LEVEL).getStringValue())
                        .isEqualTo(syslogProfile.getFlowLevel().name());
                    softly.assertThat(doc.get(EsConstants.Key.FACILITY).getStringValue())
                        .isEqualTo(syslogProfile.getFacility().name());

                    // primaryServer
                    var primary = syslogProfile.getPrimary();
                    softly.assertThat(doc.get(EsConstants.Key.PRIMARY_SERVER).getStringValue())
                        .isEqualTo(String.format("%s:%d (%s)",
                            primary.getServer(), primary.getPort(), primary.getProtocol().name()));

                    // secondaryServer
                    softly.assertThat(doc.get(EsConstants.Key.SECONDARY_SERVER).getStringValue())
                        .isEqualTo(StringUtils.EMPTY);

                    // venueIds
                    softly.assertThat(
                            doc.get(EsConstants.Key.VENUE_IDS).getListValue().getValuesList())
                        .isEmpty();
                  }));
            }));
  }

  @Tag("VlanPoolTest")
  @Test
  void deleteVenueWithVlanPoolProfile(final Tenant tenant, final Venue venue,
      final NetworkVenue networkVenue) {
    // Given
    final Network network = networkVenue.getNetwork();
    final Wlan wlan = network.getWlan();

    final var vlanPool = repositoryUtil.createOrUpdate(
        Generators.vlanPool().generate(), tenant.getId(), randomTxId());

    wlan.getAdvancedCustomization().setVlanPool(vlanPool);
    repositoryUtil.createOrUpdate(wlan, tenant.getId(), randomTxId());

    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    // When
    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    // Then
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 3").hasSize(3)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE Venue operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", venue.getId())
                  .matches(op -> venue.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                  .satisfies(ddccmVenue -> {
                    assertThat(ddccmVenue.getId())
                        .as("The venue id should be %s", venue.getId())
                        .isEqualTo(venue.getId());
                  });
              assertThat(ops)
                  .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
                  .as("The DELETE WlanVenue operation count should be 1").hasSize(1)
                  .singleElement()
                  .as("The operation id should be %s", networkVenue.getId())
                  .matches(op -> networkVenue.getId().equals(op.getId()))
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                  .satisfies(ddccmWlanVenue -> assertSoftly(softly -> {
                    softly.assertThat(ddccmWlanVenue.getWlanId())
                        .as("The wlanId in WlanVenue message should be %s", network.getId())
                        .isEqualTo(network.getId());
                    softly.assertThat(ddccmWlanVenue.getVenueId())
                        .as("The venueId in WlanVenue message should be %s", venue.getId())
                        .isEqualTo(venue.getId());
                  }));
            }));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 3").hasSize(3)
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.DEL)
                  .as("The DEL networkvenuemapping operation count should be 1")
                  .hasSize(1)
                  .singleElement()
                  .extracting(Operations::getId)
                  .as("The operation id should be %s", networkVenue.getId())
                  .isEqualTo(networkVenue.getId());
              assertThat(ops)
                  // Produced by VlanPoolsProfileCmnCfgCollectorOperationBuilder
                  .filteredOn(op -> EsConstants.Index.VLAN_POOL_POLICY_PROFILE_INDEX_NAME.equals(
                      op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD %s operation count should be 1",
                      EsConstants.Index.VLAN_POOL_POLICY_PROFILE_INDEX_NAME).hasSize(1)
                  .singleElement()
                  .extracting(Operations::getDocMap)
                  .satisfies(doc -> assertSoftly(softly -> {
                    softly.assertThat(doc.get(EsConstants.Key.TENANT_ID).getStringValue())
                        .isEqualTo(tenant.getId());
                    softly.assertThat(doc.get(EsConstants.Key.ID).getStringValue())
                        .isEqualTo(vlanPool.getId());
                    softly.assertThat(doc.get(EsConstants.Key.NAME).getStringValue())
                        .isEqualTo(vlanPool.getName());
                    softly.assertThat(doc.get(EsConstants.Key.VLAN_MEMBERS).getListValue())
                        .matches(listValue -> listValue.getValuesCount() == 1)
                        .extracting(ListValue::getValuesList).asList()
                        .hasSize(1)
                        .singleElement()
                        .extracting(Value.class::cast)
                        .extracting(Value::getStringValue)
                        .isEqualTo(vlanPool.getVlanMembers().get(0));

                    // venueApGroups
                    softly.assertThat(
                            doc.get(EsConstants.Key.VENUE_IDS).getListValue().getValuesList())
                        .isEmpty();

                    // venueIds
                    softly.assertThat(
                            doc.get(EsConstants.Key.VENUE_IDS).getListValue().getValuesList())
                        .isEmpty();
                  }));
            }));
  }

  @Test
  void deleteVenueWithLbsServiceProfile(Tenant tenant, Venue venue) {
    final var lbsServiceProfile = Generators.lbsServerProfile().generate();
    repositoryUtil.createOrUpdate(lbsServiceProfile, tenant.getId(), randomTxId());

    venue.setLbsServerProfile(lbsServiceProfile);
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

    var venue2 = Generators.venue().generate();
    venue2.setLbsServerProfile(lbsServiceProfile);
    repositoryUtil.createOrUpdate(venue2, tenant.getId(), randomTxId());

    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder().setVenueId(venue.getId()).build()))
        .build();

    // When
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), randomTxId(), event);

    // Then
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenant.getId());
    assertThat(cmnCfgCollectorMessage)
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.LBS_SERVER_PROFILE_INDEX_NAME.equals(op.getIndex()))
        .hasSize(1)
        .allMatch(op -> op.getOpType() == OpType.MOD)
        .allSatisfy(op -> {
          assertThat(op.getDocMap().get(Key.VENUE_IDS))
              .extracting(Value::getListValue)
              .extracting(ListValue::getValuesList)
              .asList()
              .extracting(com.ruckus.cloud.events.gpb.Value.class::cast)
              .extracting(Value::getStringValue)
              .doesNotContain(venue.getId()) // venue is unbinding from lbsServiceProfile
              .contains(venue2.getId()); // venue1 is still binding to lbsServiceProfile
        });
  }

  @Test
  void updateVenue(Venue venue, @DefaultApGroup ApGroup apGroup)
      throws InvalidProtocolBufferException {
    VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.MODIFY)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(venue.getId())
                    .setVenueName("My-Venue")
                    .setAddress(Address.newBuilder()
                        .setCountryCode(venue.getCountryCode())
                        .setTimezone(venue.getTimezone())
                        .setAddressLine(venue.getAddressLine())
                        .setLongitude(Double.parseDouble(venue.getDeviceGps().getLongitude()))
                        .setLatitude(Double.parseDouble(venue.getDeviceGps().getLatitude()))
                        .build())))
        .build();

    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId,
        event);

    var record = messageCaptors.getDdccmMessageCaptor().getValue(txCtxExtension.getTenantId(),
        requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getName)
        .isEqualTo("My-Venue");

    var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(venue.getTenant().getId(), requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(msg -> msg.getTenantId().equals(venue.getTenant().getId()))
        .matches(msg -> msg.getRequestId().equals(requestId))
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
        .hasSize(1)
        .allMatch(op -> op.getOpType() == OpType.MOD)
        .allSatisfy(op -> {
          assertThat(op.getDocOrThrow(Key.VENUE_NAME))
              .isEqualTo(ValueUtils.stringValue("My-Venue"));
        });
  }

  @Test
  void testAddVenueTemplateInstance(Tenant tenant, @Template Venue venue,
      @DefaultApGroup ApGroup apGroup) throws Exception {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenantId);

    createEcTenant(tenant, ecTenantId);

    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
  }

  @Tag("EthernetPortProfileTest")
  @Test
  void testAddVenueTemplateInstanceWithEthernetPortProfile(Tenant tenant, @Template Venue venue,
      @DefaultApGroup ApGroup apGroup) throws Exception {
    var instanceId = randomId();
    var ecTenantId = randomId();

    var venueApModelLanPortSettings = new VenueApModelSpecificAttributes();
    venueApModelLanPortSettings.setModel("R710");
    venueApModelLanPortSettings.setVenue(venue);
    venueApModelLanPortSettings = repositoryUtil.
        createOrUpdate(venueApModelLanPortSettings, tenant.getId(), randomTxId());
    var ethernetPortProfile = new EthernetPortProfile();
    ethernetPortProfile.setType(ApLanPortTypeEnum.TRUNK);
    ethernetPortProfile.setApLanPortId(1);
    ethernetPortProfile.setTenant(tenant);
    ethernetPortProfile.setVlanMembers("1-4094");
    ethernetPortProfile.setUntagId((short) 1);
    ethernetPortProfile = repositoryUtil.createOrUpdate(ethernetPortProfile, tenant.getId(), randomTxId());

    var venueLanPort1 = new VenueLanPort();
    venueLanPort1.setEnabled(true);
    venueLanPort1.setPortId("1");
    venueLanPort1.setVenueApModelSpecificAttributes(venueApModelLanPortSettings);
    venueLanPort1.setApLanPortProfile(ethernetPortProfile);
    repositoryUtil.createOrUpdate(venueLanPort1, venue.getTenant().getId(), randomTxId());

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenantId);

    createEcTenant(tenant, ecTenantId);

    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
  }

  @Tag("EthernetPortProfileTest")
  @FeatureFlag(enable = WIFI_ETHERNET_PORTS_TEMPLATE_LEGACY)
  @Test
  void testAddVenueTemplateInstanceWithEthernetPortProfile_duplicate_profile_name(Tenant tenant, @Template Venue venue,
      @DefaultApGroup ApGroup apGroup) throws Exception {
    var instanceId = randomId();
    var ecTenantId = randomId();

    var venueApModelLanPortSettings = new VenueApModelSpecificAttributes();
    venueApModelLanPortSettings.setModel("R710");
    venueApModelLanPortSettings.setVenue(venue);
    venueApModelLanPortSettings = repositoryUtil.
        createOrUpdate(venueApModelLanPortSettings, tenant.getId(), randomTxId());
    var venueApModelLanPortSettings2 = new VenueApModelSpecificAttributes();
    venueApModelLanPortSettings2.setModel("R750");
    venueApModelLanPortSettings2.setVenue(venue);
    venueApModelLanPortSettings2 = repositoryUtil.
        createOrUpdate(venueApModelLanPortSettings2, tenant.getId(), randomTxId());

    var ethernetPortProfile1 = new EthernetPortProfile();
    ethernetPortProfile1.setName("ApLanPortProfile_TRUNK_1_1-4094_6");
    ethernetPortProfile1.setType(ApLanPortTypeEnum.TRUNK);
    ethernetPortProfile1.setApLanPortId(6);
    ethernetPortProfile1.setTenant(tenant);
    ethernetPortProfile1.setVlanMembers("1-4094");
    ethernetPortProfile1.setUntagId((short) 1);
    ethernetPortProfile1 = repositoryUtil.createOrUpdate(ethernetPortProfile1, tenant.getId(), randomTxId());

    var ethernetPortProfile2 = new EthernetPortProfile();
    ethernetPortProfile2.setName("ApLanPortProfile_ACCESS_100_100_7");
    ethernetPortProfile2.setType(ApLanPortTypeEnum.ACCESS);
    ethernetPortProfile2.setApLanPortId(7);
    ethernetPortProfile2.setTenant(tenant);
    ethernetPortProfile2.setVlanMembers("100");
    ethernetPortProfile2.setUntagId((short) 100);
    ethernetPortProfile2 = repositoryUtil.createOrUpdate(ethernetPortProfile2, tenant.getId(), randomTxId());

    var venueLanPort1 = new VenueLanPort();
    venueLanPort1.setEnabled(true);
    venueLanPort1.setPortId("1");
    venueLanPort1.setVenueApModelSpecificAttributes(venueApModelLanPortSettings);
    venueLanPort1.setApLanPortProfile(ethernetPortProfile1);
    repositoryUtil.createOrUpdate(venueLanPort1, venue.getTenant().getId(), randomTxId());

    var venueLanPort2 = new VenueLanPort();
    venueLanPort2.setEnabled(true);
    venueLanPort2.setPortId("2");
    venueLanPort2.setVenueApModelSpecificAttributes(venueApModelLanPortSettings);
    venueLanPort2.setApLanPortProfile(ethernetPortProfile2);
    repositoryUtil.createOrUpdate(venueLanPort2, venue.getTenant().getId(), randomTxId());

    var venueLanPort3 = new VenueLanPort();
    venueLanPort3.setEnabled(true);
    venueLanPort3.setPortId("1");
    venueLanPort3.setVenueApModelSpecificAttributes(venueApModelLanPortSettings2);
    venueLanPort3.setApLanPortProfile(ethernetPortProfile2);
    repositoryUtil.createOrUpdate(venueLanPort3, venue.getTenant().getId(), randomTxId());

    var venueLanPort4 = new VenueLanPort();
    venueLanPort4.setEnabled(true);
    venueLanPort4.setPortId("2");
    venueLanPort4.setVenueApModelSpecificAttributes(venueApModelLanPortSettings2);
    venueLanPort4.setApLanPortProfile(ethernetPortProfile2);
    repositoryUtil.createOrUpdate(venueLanPort4, venue.getTenant().getId(), randomTxId());


    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenantId);

    createEcTenant(tenant, ecTenantId);

    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertDuplicateProfileHandled(ecTenantId, venue, instanceId, Status.OK,
        ethernetPortProfile2.getUntagId(), ethernetPortProfile2.getVlanMembers());
  }

  @Tag("ApGroupTest")
  @Test
  void testAddVenueTemplateInstanceWithApGroups(Tenant tenant, @Template Venue venue,
      @DefaultApGroup ApGroup apGroup) throws Exception {
    var instanceId = randomId();
    var ecTenantId = randomId();
    repositoryUtil.createOrUpdate(ApGroupTestFixture.randomApGroup(venue),
        venue.getTenant().getId(), randomTxId());
    repositoryUtil.createOrUpdate(ApGroupTestFixture.randomApGroup(venue),
        venue.getTenant().getId(), randomTxId());

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenantId);

    createEcTenant(tenant, ecTenantId);

    var requestId = randomTxId() + "$AddVenueByTemplate";
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
  }

  @Test
  void testDeleteVenueTemplateWithApGroups(Tenant tenant, @Template Venue venue,
      @Template ApGroup apGroup) {
    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(venue.getId())
                    .setIsTemplate(true)
                    .build()))
        .build();

    // When
    var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    // Then
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(txCtxExtension.getTenantId(),
            requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();
  }

  private Network createInstanceFromTemplate(OpenNetwork template, Tenant ecTenant) {
    return createInstanceFromTemplate(template, ecTenant, null);
  }

  private Network createInstanceFromTemplate(OpenNetwork template, Tenant ecTenant,
      VlanPool vlanPoolInstance) {
    var instance = new OpenNetwork();
    instance.setId(randomId());
    instance.setName(template.getName());
    instance.setTemplateId(template.getId());
    instance.setTenant(ecTenant);
    instance.setWlan(new Wlan());
    instance.getWlan().setNetwork(instance);
    instance.getWlan().setSsid(template.getWlan().getSsid());
    instance.getWlan().setWlanSecurity(WlanSecurityEnum.Open);

    if (vlanPoolInstance != null) {
      instance.getWlan().setAdvancedCustomization(new WlanAdvancedCustomization());
      instance.getWlan().getAdvancedCustomization().setVlanPool(vlanPoolInstance);
    }
    return repositoryUtil.createOrUpdate(instance, ecTenant.getId(), randomTxId());
  }

  private VlanPool createInstanceFromTemplate(VlanPool template, Tenant ecTenant) {
    var instance = new VlanPool();
    instance.setId(randomId());
    instance.setName(template.getName());
    instance.setTemplateId(template.getId());
    instance.setTenant(ecTenant);
    instance.setVlanMembers(template.getVlanMembers());
    return repositoryUtil.createOrUpdate(instance, ecTenant.getId(), randomTxId());
  }

  private void assertVenueInstanceCreated(String ecTenantId, Venue venueTemplate,
      String instanceId) {
    assertVenueInstanceCreated(ecTenantId, venueTemplate, instanceId, Status.OK);
  }

  private void assertVenueInstanceCreated(String ecTenantId, Venue venueTemplate,
      String instanceId, Status expectedStatus) {
    KafkaProtoMessage<WifiConfigRequest> record = messageCaptors.getDdccmMessageCaptor()
        .getValue(ecTenantId);
    assertVenueInstanceCreated(ecTenantId, venueTemplate, instanceId, expectedStatus, record);
  }

  private void assertVenueInstanceCreated(String ecTenantId, Venue venueTemplate,
      String instanceId, Status expectedStatus, KafkaProtoMessage<WifiConfigRequest> record) {
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .hasSize(1)
        .first()
        .satisfies(e -> assertThat(e.getAction()).isEqualTo(
            com.ruckus.acx.ddccm.protobuf.common.Action.ADD))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
        .satisfies(e -> assertThat(e.getTenantId()).isEqualTo(ecTenantId))
        .satisfies(e -> assertThat(e.getName()).isEqualTo(venueTemplate.getName()))
        .satisfies(e -> assertThat(e.getId()).isEqualTo(instanceId));

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApGroup)
        .hasSize(venueTemplate.getApGroups().size())
        .first()
        .satisfies(e -> assertThat(e.getAction()).isEqualTo(
            com.ruckus.acx.ddccm.protobuf.common.Action.ADD))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApGroup)
        .satisfies(e -> assertThat(e.getVenueId()).isEqualTo(instanceId));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(ecTenantId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.VENUE.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.ADD)
                  .as("The ADD venue operation count should be 1")
                  .hasSize(1)
                  .singleElement()
                  .extracting(Operations::getId)
                  .as("The operation id should be %s", instanceId)
                  .isEqualTo(instanceId);
            })
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.ADD)
                  .as("The ADD apGroup operation count should be %d",
                      venueTemplate.getApGroups().size())
                  .hasSize(venueTemplate.getApGroups().size());
            }));
    // The tenant id of instance activity message should be the EC tenant ID.
    final var activityMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(ecTenantId);
    assertThat(activityMessage).isNotNull();
    assertThat(activityMessage.getPayload().getStatus()).isEqualTo(expectedStatus);

    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(ecTenantId);

    var venueInstance = repositoryUtil.find(Venue.class, instanceId);
    if (Status.OK.equals(expectedStatus)) {
      assertThat(venueInstance.getApGroups())
          .hasSize(venueTemplate.getApGroups().size())
          .extracting(ApGroup::getTemplateId)
          .containsAll(venueTemplate.getApGroups().stream().map(ApGroup::getId)
              .collect(Collectors.toList()));
    } else {
      assertThat(venueInstance).isNull();
    }
  }

  private void assertDuplicateProfileHandled(String ecTenantId, Venue venueTemplate,
      String instanceId, Status expectedStatus, Short unTagId, String vlanMembers) {
    var record = messageCaptors.getDdccmMessageCaptor().getValue(ecTenantId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    // venue, apgroup, aplanportprofile1, aplanportprofile2
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .hasSize(4);

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApLanPortProfile)
        .hasSize(2)
        .filteredOn(op -> op.getApLanPortProfile().getUntagId() == unTagId)
        .hasSize(1)
        .first()
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
        .satisfies(p -> assertThat(p.getVlanMembers()).isEqualTo(vlanMembers));

    // validate venue created
    assertVenueInstanceCreated(ecTenantId, venueTemplate, instanceId, expectedStatus, record);
  }



  @Test
  void testAddVenueTemplateInstanceWithNetworkVenueOnAllApGroup(Tenant tenant,
      @Template Venue venue,
      @DefaultApGroup ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue) {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);
    var networkInstance = createInstanceFromTemplate(network, ecTenant);

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
    assertWifiNetworkActivated(ecTenantId, instanceId, networkInstance, 1, null);
    assertWifiNetworkUpdated(ecTenantId, instanceId, networkInstance, null);
  }

  @Test
  void testAddVenueTemplateInstanceWithNetworkVenueOnSpecificApGroup(Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue) {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);
    var networkInstance = createInstanceFromTemplate(network, ecTenant);

    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, venue.getTenant().getId(),
            randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            venue.getTenant().getId(),
            randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.forEach(networkApGroup -> {
      networkApGroup.setNetworkApGroupRadios(new NetworkApGroupRadioGenerator()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
          .generate(1));
      networkApGroup.getNetworkApGroupRadios().forEach(
          radio -> repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId()));
    });

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
    assertWifiNetworkActivated(ecTenantId, instanceId, networkInstance, networkApGroups.size(),
        null);
    assertWifiNetworkUpdated(ecTenantId, instanceId, networkInstance, null);
    for (int i = 0; i < networkApGroups.size(); i++) {
      assertApGroupActivated(ecTenantId, instanceId);
      assertApGroupSettingsUpdated(ecTenantId, instanceId);
    }
  }

  @Test
  void testAddNetworkTemplateInstanceAndVenueTemplateInstanceWithNetworkVenueOnSpecificApGroup(
      Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue) throws ExecutionException, InterruptedException {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var venueInstanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    // prepare ApGroup, NetworkApGroup, NetworkApGroupRadio, etc.

    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, venue.getTenant().getId(),
            randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            venue.getTenant().getId(),
            randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.forEach(networkApGroup -> {
      networkApGroup.setNetworkApGroupRadios(new NetworkApGroupRadioGenerator()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
          .generate(1));
      networkApGroup.getNetworkApGroupRadios().forEach(
          radio -> repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId()));
    });

    // add network by template

    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String networkInstanceId = randomId();

    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.NETWORK, networkInstanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, network.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, networkInstanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    Map<String, String> rhs = new HashMap<>(Map.of(
        HEADER_TEMPLATE_FLOW, ADD_WIFI_NETWORK_BY_TEMPLATE,
        HEADER_TEMPLATE_TENANT_ID, mspTenantId,
        HEADER_TEMPLATE_REQUEST_ID, mspRequestId
    ));
    messageUtil.sendWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE,
        userName, rps, rhs, instanceCreateRequest);

    assertNetworkInstanceCreated(mspTenantId, ecTenantId, network, networkInstanceId);
    Network networkInstance = repositoryUtil.find(Network.class, networkInstanceId, ecTenantId);

    messageUtil.clearMessage();

    // add venue by template

    var event = buildTemplateInstanceEvent(venue, venueInstanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, venueInstanceId);
    assertWifiNetworkActivated(ecTenantId, venueInstanceId, networkInstance,
        networkApGroups.size(),
        null);
    assertWifiNetworkUpdated(ecTenantId, venueInstanceId, networkInstance, null);
    for (int i = 0; i < networkApGroups.size(); i++) {
      assertApGroupActivated(ecTenantId, venueInstanceId);
      assertApGroupSettingsUpdated(ecTenantId, venueInstanceId);
    }
  }

  // VlanPool activate on NetworkVenue/NetworkApGroupRadio

  /* Sunny
  VlanPool activation - type 1
  vp1 -> n1 -> v1
   */
  @Test
  void testAddVenueTemplateInstanceWithNetworkVenueOnAllApGroup_andVlanPoolActivatedOnNetwork(
      Tenant tenant, @Template Venue venue,
      @DefaultApGroup ApGroup apGroup,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue,
      @Template VlanPool vlanPool) {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    network.getWlan().getAdvancedCustomization().setVlanPool(vlanPool);
    repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());

    var networkApGroup = new NetworkApGroupGenerator().setApGroup(always(apGroup))
        .setNetworkVenue(always(networkVenue)).generate();
    networkApGroup = repositoryUtil.createOrUpdate(networkApGroup, venue.getTenant().getId(),
        randomTxId());
    networkApGroup.setNetworkApGroupRadios(new NetworkApGroupRadioGenerator()
        .setNetworkApGroup(always(networkApGroup))
        .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
        .generate(1));
    networkApGroup.getNetworkApGroupRadios().forEach(
        radio -> repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId()));

    networkVenue.setNetworkApGroups(List.of(networkApGroup));

    var vlanPoolInstance = createInstanceFromTemplate(vlanPool, ecTenant);
    var networkInstance = createInstanceFromTemplate(network, ecTenant, vlanPoolInstance);

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
    assertWifiNetworkActivated(ecTenantId, instanceId, networkInstance, 1,
        vlanPoolInstance.getId());
    assertWifiNetworkUpdated(ecTenantId, instanceId, networkInstance, vlanPoolInstance.getId());
    assertApGroupSettingsUpdated(ecTenantId, instanceId);
  }

  /* Sunny
  VlanPool activation - type 2
  vp2 ---\ (default)
       n2 -> v2
   */
  @Test
  void testAddVenueTemplateInstanceWithNetworkVenueOnSpecificApGroup_andVlanPoolActivatedOnApGroupOnly(
      Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue,
      @Template VlanPool vlanPool) {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);
    var vlanPoolInstance = createInstanceFromTemplate(vlanPool, ecTenant);
    var networkInstance = createInstanceFromTemplate(network, ecTenant);

    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, venue.getTenant().getId(),
            randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            venue.getTenant().getId(),
            randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.forEach(networkApGroup -> {
      networkApGroup.setNetworkApGroupRadios(new NetworkApGroupRadioGenerator()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
          .generate(1));
      networkApGroup.getNetworkApGroupRadios().forEach(
          radio -> {
            // Only the default ApGroup activates vlanPool2
            if (radio.getNetworkApGroup().getApGroup().getIsDefault()) {
              radio.setVlanPool(vlanPool);
            }
            repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId());
          });
    });

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
    assertWifiNetworkActivated(ecTenantId, instanceId, networkInstance, networkApGroups.size(),
        null);
    assertWifiNetworkUpdated(ecTenantId, instanceId, networkInstance, null);
    for (int i = 0; i < networkApGroups.size(); i++) {
      assertApGroupActivated(ecTenantId, instanceId);
      assertApGroupSettingsUpdated(ecTenantId, instanceId);
      if (networkApGroups.get(i).getNetworkApGroupRadios().stream()
          .map(NetworkApGroupRadio::getVlanPool).filter(Objects::nonNull).findFirst()
          .isPresent()) {
        assertVlanPoolAppliedOnApGroup(ecTenantId, instanceId, vlanPoolInstance.getId());
      }
    }
  }

  /* Rainy
  VlanPool activation - type 2
  vp2 ---\ (default)
       n2 -> v2
   */
  @Test
  void testAddVenueTemplateInstanceWithNetworkVenueOnSpecificApGroup_andVlanPoolActivatedOnApGroupOnly_andShouldFailWithoutVlanPoolInstance(
      Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue,
      @Template VlanPool vlanPool) {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);
//    var vlanPoolInstance = createInstanceFromTemplate(vlanPool, ecTenant);
    var networkInstance = createInstanceFromTemplate(network, ecTenant);

    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, venue.getTenant().getId(),
            randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            venue.getTenant().getId(),
            randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.forEach(networkApGroup -> {
      networkApGroup.setNetworkApGroupRadios(new NetworkApGroupRadioGenerator()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
          .generate(1));
      networkApGroup.getNetworkApGroupRadios().forEach(
          radio -> {
            // Only the default ApGroup activates vlanPool2
            if (radio.getNetworkApGroup().getApGroup().getIsDefault()) {
              radio.setVlanPool(vlanPool);
            }
            repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId());
          });
    });

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId, Status.FAIL);
    assertNetworkVenueInstanceCreate(ecTenantId, networkVenue, networkInstance,
        instanceId, networkApGroups.size(), null, null,
        Status.FAIL);
  }

  /* Sunny
  VlanPool activation - type 3
  vp3_2 -----\ (default)
  vp3_1 -> n3 -> v3
   */
  @Test
  void testAddVenueTemplateInstanceWithNetworkVenueOnSpecificApGroup_andVlanPoolActivatedOnNetworkAndApGroup(
      Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue,
      @Template VlanPool vlanPool1) {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    network.getWlan().getAdvancedCustomization().setVlanPool(vlanPool1);
    repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());

    VlanPool vlanPool2 = VlanPoolTestFixture.randomVlanPool(vp -> {
      vp.setName("vlanPool2");
      vp.setIsTemplate(true);
      vp.setTenant(vlanPool1.getTenant());
    });
    repositoryUtil.createOrUpdate(vlanPool2, vlanPool2.getTenant().getId(), randomTxId());

    var vlanPoolInstance1 = createInstanceFromTemplate(vlanPool1, ecTenant);
    var networkInstance = createInstanceFromTemplate(network, ecTenant, vlanPoolInstance1);
    var vlanPoolInstance2 = createInstanceFromTemplate(vlanPool2, ecTenant);

    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, venue.getTenant().getId(),
            randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            venue.getTenant().getId(),
            randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.forEach(networkApGroup -> {
      networkApGroup.setNetworkApGroupRadios(new NetworkApGroupRadioGenerator()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
          .generate(1));
      networkApGroup.getNetworkApGroupRadios().forEach(
          radio -> {
            // Only the default ApGroup activates vlanPool2
            if (radio.getNetworkApGroup().getApGroup().getIsDefault()) {
              radio.setVlanPool(vlanPool2);
            }
            repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId());
          });
    });

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
    assertWifiNetworkActivated(ecTenantId, instanceId, networkInstance, networkApGroups.size(),
        vlanPoolInstance1.getId());
    assertWifiNetworkUpdated(ecTenantId, instanceId, networkInstance, vlanPoolInstance1.getId());
    for (int i = 0; i < networkApGroups.size(); i++) {
      assertApGroupActivated(ecTenantId, instanceId);
      assertApGroupSettingsUpdated(ecTenantId, instanceId);
      if (networkApGroups.get(i).getNetworkApGroupRadios().stream()
          .map(NetworkApGroupRadio::getVlanPool).filter(Objects::nonNull).findFirst()
          .isPresent()) {
        assertVlanPoolAppliedOnApGroup(ecTenantId, instanceId, vlanPoolInstance2.getId());
      }
    }
  }

  /* Rainy
  VlanPool activation - type 3
  vp3_2 -----\ (default)
  vp3_1 -> n3 -> v3
   */
  @Test
  void testAddVenueTemplateInstanceWithNetworkVenueOnSpecificApGroup_andVlanPoolActivatedOnNetworkAndApGroup_shoudFailWithoutVlanPoolInstance(
      Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue,
      @Template VlanPool vlanPool1) {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    network.getWlan().getAdvancedCustomization().setVlanPool(vlanPool1);
    repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());

    VlanPool vlanPool2 = VlanPoolTestFixture.randomVlanPool(vp -> {
      vp.setName("vlanPool2");
      vp.setIsTemplate(true);
      vp.setTenant(vlanPool1.getTenant());
    });
    repositoryUtil.createOrUpdate(vlanPool2, vlanPool2.getTenant().getId(), randomTxId());

    var vlanPoolInstance1 = createInstanceFromTemplate(vlanPool1, ecTenant);
    var networkInstance = createInstanceFromTemplate(network, ecTenant, vlanPoolInstance1);
//    var vlanPoolInstance2 = createInstanceFromTemplate(vlanPool2, ecTenant);

    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, venue.getTenant().getId(),
            randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            venue.getTenant().getId(),
            randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.forEach(networkApGroup -> {
      networkApGroup.setNetworkApGroupRadios(new NetworkApGroupRadioGenerator()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
          .generate(1));
      networkApGroup.getNetworkApGroupRadios().forEach(
          radio -> {
            // Only the default ApGroup activates vlanPool2
            if (radio.getNetworkApGroup().getApGroup().getIsDefault()) {
              radio.setVlanPool(vlanPool2);
            }
            repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId());
          });
    });

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId, Status.FAIL);
    assertNetworkVenueInstanceCreate(ecTenantId, networkVenue, networkInstance,
        instanceId, networkApGroups.size(), vlanPoolInstance1.getId(), null, Status.FAIL);
  }

  /* Sunny
  VlanPool activation - type 4
  vp3_2 -----\ (default)
  vp3_1 -> n3 -> v3
              |
            vp3_3
   */
  @Test
  void testAddVenueTemplateInstanceWithNetworkVenueOnSpecificApGroup_andVlanPoolActivatedOnNetworkAndApGroupAndNetworkVenue(
      Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue,
      @Template VlanPool vlanPool1) {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    network.getWlan().getAdvancedCustomization().setVlanPool(vlanPool1);
    repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());

    VlanPool vlanPool2 = VlanPoolTestFixture.randomVlanPool(vp -> {
      vp.setName("vlanPool2");
      vp.setIsTemplate(true);
      vp.setTenant(vlanPool1.getTenant());
    });
    repositoryUtil.createOrUpdate(vlanPool2, vlanPool2.getTenant().getId(), randomTxId());

    VlanPool vlanPool3 = VlanPoolTestFixture.randomVlanPool(vp -> {
      vp.setName("vlanPool3");
      vp.setIsTemplate(true);
      vp.setTenant(vlanPool1.getTenant());
    });
    repositoryUtil.createOrUpdate(vlanPool3, vlanPool3.getTenant().getId(), randomTxId());

    var vlanPoolInstance1 = createInstanceFromTemplate(vlanPool1, ecTenant);
    var networkInstance = createInstanceFromTemplate(network, ecTenant, vlanPoolInstance1);
    var vlanPoolInstance2 = createInstanceFromTemplate(vlanPool2, ecTenant);
    var vlanPoolInstance3 = createInstanceFromTemplate(vlanPool3, ecTenant);

    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, venue.getTenant().getId(),
            randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    networkVenue.setVlanPoolId(
        vlanPool3.getId()); // vlanPoolId set in networkVenue doesn't matter
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            venue.getTenant().getId(),
            randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.forEach(networkApGroup -> {
      networkApGroup.setNetworkApGroupRadios(new NetworkApGroupRadioGenerator()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
          .generate(1));
      networkApGroup.getNetworkApGroupRadios().forEach(
          radio -> {
            // Only the default ApGroup activates vlanPool2
            if (radio.getNetworkApGroup().getApGroup().getIsDefault()) {
              radio.setVlanPool(vlanPool2);
            }
            repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId());
          });
    });

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
    assertWifiNetworkActivated(ecTenantId, instanceId, networkInstance, networkApGroups.size(),
        vlanPoolInstance1.getId());
    assertWifiNetworkUpdated(ecTenantId, instanceId, networkInstance, vlanPoolInstance1.getId());
    for (int i = 0; i < networkApGroups.size(); i++) {
      assertApGroupActivated(ecTenantId, instanceId);
      assertApGroupSettingsUpdated(ecTenantId, instanceId);
      if (networkApGroups.get(i).getNetworkApGroupRadios().stream()
          .map(NetworkApGroupRadio::getVlanPool).filter(Objects::nonNull).findFirst()
          .isPresent()) {
        assertVlanPoolAppliedOnApGroup(ecTenantId, instanceId, vlanPoolInstance2.getId());
      }
    }
  }

  private void assertWifiNetworkActivated(String ecTenantId, String venueInstanceId,
      Network networkInstance, int apGroupCount, String vlanPoolInstanceOnVenueId) {
    final var record = messageCaptors.getDdccmMessageCaptor().getValue(ecTenantId);
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(ecTenantId);

    // DDCCM
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .hasSize(1)
        .first()
        .satisfies(e -> assertThat(e.getAction()).isEqualTo(
            com.ruckus.acx.ddccm.protobuf.common.Action.ADD))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .satisfies(e -> assertThat(e.getTenantId()).isEqualTo(ecTenantId))
        .satisfies(e -> assertThat(e.getVenueId()).isEqualTo(venueInstanceId))
        .satisfies(e -> assertThat(e.getWlanId()).isEqualTo(networkInstance.getId()))
        .satisfies(e -> {
          if (vlanPoolInstanceOnVenueId == null) {
            assertThat(e.hasVlanPoolId()).isFalse();
          } else {
            assertThat(e.getVlanPoolId().getValue()).isEqualTo(vlanPoolInstanceOnVenueId);
          }
        });

    // The network should be activated on all apGroups by default
    Set<String> apGroupIds = ddccmRequest.getOperationsList().stream()
        .filter(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanApGroup)
        .map(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanApGroup)
        .map(WlanApGroup::getApGroupId)
        .collect(Collectors.toSet());
    assertThat(apGroupIds)
        .hasSize(apGroupCount);

    // VlanPoolId should be empty by default
    Set<String> vlanPoolIdOfApGroups = ddccmRequest.getOperationsList().stream()
        .filter(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanApGroup)
        .map(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanApGroup)
        .map(WlanApGroup::getVlanPoolId)
        .filter(vpId -> vpId != null && !StringUtils.isBlank(vpId.getValue()))
        .map(StringValue::getValue)
        .collect(Collectors.toSet());

    assertThat(vlanPoolIdOfApGroups).isEmpty();

    // CmnCfgCollector
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.ADD)
                  .as("The ADD venue operation count should be 1")
                  .hasSize(1);
            }));

    final var networkVenueInstance = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(ecTenantId, networkInstance.getId(), venueInstanceId);
    assertThat(networkVenueInstance)
        .isNotEmpty().get()
        .extracting(AbstractTenantAwareTemplateBaseEntity::getTemplateId)
        .isNotNull();
  }

  private void assertWifiNetworkUpdated(String ecTenantId, String venueInstanceId,
      Network networkInstance, String vlanPoolInstanceOnVenueId) {
    final var record = messageCaptors.getDdccmMessageCaptor().getValue(ecTenantId);
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(ecTenantId);

    // DDCCM
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .hasSize(1)
        .first()
        .satisfies(e -> assertThat(e.getAction()).isEqualTo(
            com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .satisfies(e -> assertThat(e.getTenantId()).isEqualTo(ecTenantId))
        .satisfies(e -> assertThat(e.getVenueId()).isEqualTo(venueInstanceId))
        .satisfies(e -> assertThat(e.getWlanId()).isEqualTo(networkInstance.getId()))
        .satisfies(e -> {
          if (vlanPoolInstanceOnVenueId == null) {
            assertThat(e.hasVlanPoolId()).isFalse();
          } else {
            assertThat(e.getVlanPoolId().getValue()).isEqualTo(vlanPoolInstanceOnVenueId);
          }
        });

    // CmnCfgCollector
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD venue operation count should be 1")
                  .hasSize(1);
            }));
  }

  private void assertApGroupActivated(String ecTenantId, String venueInstanceId) {
    final var record = messageCaptors.getDdccmMessageCaptor().getValue(ecTenantId);
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(ecTenantId);

    // DDCCM
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();

    // VlanPoolId should equal to networkVenue
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanApGroup)
        .hasSize(4) // Default 4 radio types
        .first()
        .satisfies(e -> assertThat(e.getAction()).isEqualTo(
            com.ruckus.acx.ddccm.protobuf.common.Action.ADD))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanApGroup)
        .satisfies(e -> assertThat(e.getVenueId()).isEqualTo(venueInstanceId))
        .satisfies(e -> assertThat(e.hasVlanPoolId()).isFalse());

    // CmnCfgCollector
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.NETWORK_DEVICE_GROUP_MAPPING.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.ADD)
                  .as("The MOD venue operation count should be 1")
                  .hasSize(1);
            }));
  }

  private void assertApGroupSettingsUpdated(String ecTenantId, String venueInstanceId) {
    final var record = messageCaptors.getDdccmMessageCaptor().getValue(ecTenantId);
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(ecTenantId);

    // DDCCM
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();

    // Delete 3 unused radios
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanApGroup)
        .filteredOn(e -> e.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.DELETE)
        .hasSize(3);

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanApGroup)
        .filteredOn(e -> e.getAction() == com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY)
        .isEmpty();

    // CmnCfgCollector
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.NETWORK_DEVICE_GROUP_MAPPING.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD venue operation count should be 1")
                  .hasSize(1);
            }));
  }

  private void assertVlanPoolAppliedOnApGroup(String ecTenantId, String venueInstanceId,
      String vlanPoolInstanceOnVenueId) {
    final var record = messageCaptors.getDdccmMessageCaptor().getValue(ecTenantId);
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(ecTenantId);

    // DDCCM
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanApGroup)
        .hasSize(1) // Only 2.4G radio
        .first()
        .satisfies(e -> assertThat(e.getAction()).isEqualTo(
            com.ruckus.acx.ddccm.protobuf.common.Action.MODIFY))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanApGroup)
        .satisfies(e -> assertThat(e.getVenueId()).isEqualTo(venueInstanceId))
        .satisfies(e -> {
          if (vlanPoolInstanceOnVenueId == null) {
            assertThat(e.hasVlanPoolId()).isFalse();
          } else {
            assertThat(e.getVlanPoolId().getValue()).isEqualTo(vlanPoolInstanceOnVenueId);
          }
        });

    // CmnCfgCollector
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(
                      op -> Index.VLAN_POOL_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD vlanPool operation count should be 1")
                  .hasSize(1);
            }));
  }

  /* Sunny
  VlanPool activation - type 5
  vp3_2 -----\ (default)
           n3 -> v3
              |
            vp3_3
   */
  @Test
  void testAddVenueTemplateInstanceWithNetworkVenueOnSpecificApGroup_andVlanPoolActivatedOnAndApGroupAndNetworkVenue(
      Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue,
      @Template VlanPool vlanPool1) {
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    VlanPool vlanPool2 = VlanPoolTestFixture.randomVlanPool(vp -> {
      vp.setName("vlanPool2");
      vp.setIsTemplate(true);
      vp.setTenant(vlanPool1.getTenant());
    });
    repositoryUtil.createOrUpdate(vlanPool2, vlanPool2.getTenant().getId(), randomTxId());

    VlanPool vlanPool3 = VlanPoolTestFixture.randomVlanPool(vp -> {
      vp.setName("vlanPool3");
      vp.setIsTemplate(true);
      vp.setTenant(vlanPool1.getTenant());
    });
    repositoryUtil.createOrUpdate(vlanPool3, vlanPool3.getTenant().getId(), randomTxId());

//    var vlanPoolInstance1 = createInstanceFromTemplate(vlanPool1, ecTenant);
    var networkInstance = createInstanceFromTemplate(network, ecTenant);
    var vlanPoolInstance2 = createInstanceFromTemplate(vlanPool2, ecTenant);
    var vlanPoolInstance3 = createInstanceFromTemplate(vlanPool3, ecTenant);

    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, venue.getTenant().getId(),
            randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    networkVenue.setVlanPoolId(
        vlanPool3.getId()); // vlanPoolId set in networkVenue doesn't matter
    repositoryUtil.createOrUpdate(networkVenue, networkVenue.getTenant().getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            venue.getTenant().getId(),
            randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.forEach(networkApGroup -> {
      networkApGroup.setNetworkApGroupRadios(new NetworkApGroupRadioGenerator()
          .setNetworkApGroup(always(networkApGroup))
          .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
          .generate(1));
      networkApGroup.getNetworkApGroupRadios().forEach(
          radio -> {
            // Only the default ApGroup activates vlanPool2
            if (radio.getNetworkApGroup().getApGroup().getIsDefault()) {
              radio.setVlanPool(vlanPool2);
            }
            repositoryUtil.createOrUpdate(radio, venue.getTenant().getId(), randomTxId());
          });
    });

    var event = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId());

    var requestId = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestId, event);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
    assertWifiNetworkActivated(ecTenantId, instanceId, networkInstance, networkApGroups.size(),
        null);
    assertWifiNetworkUpdated(ecTenantId, instanceId, networkInstance, null);
    for (int i = 0; i < networkApGroups.size(); i++) {
      assertApGroupActivated(ecTenantId, instanceId);
      assertApGroupSettingsUpdated(ecTenantId, instanceId);
      if (networkApGroups.get(i).getNetworkApGroupRadios().stream()
          .map(NetworkApGroupRadio::getVlanPool).filter(Objects::nonNull).findFirst()
          .isPresent()) {
        assertVlanPoolAppliedOnApGroup(ecTenantId, instanceId, vlanPoolInstance2.getId());
      }
    }
  }

  private void assertNetworkVenueInstanceCreate(String ecTenantId,
      NetworkVenue networkVenueTemplate,
      Network networkInstance, String venueInstanceId, int apGroupCount,
      String vlanPoolInstanceOnVenueId, String vlanPoolInstanceOnApGroupId) {
    assertNetworkVenueInstanceCreate(ecTenantId, networkVenueTemplate, networkInstance,
        venueInstanceId, apGroupCount, vlanPoolInstanceOnVenueId, vlanPoolInstanceOnApGroupId,
        Status.OK);
  }

  private void assertNetworkVenueInstanceCreate(String ecTenantId,
      NetworkVenue networkVenueTemplate,
      Network networkInstance, String venueInstanceId, int apGroupCount,
      String vlanPoolInstanceOnVenueId, String vlanPoolInstanceOnApGroupId, Status expectedStatus) {

    if (!expectedStatus.equals(Status.OK)) {
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(ecTenantId);
      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(ecTenantId);
      messageCaptors.getActivityCfgChangeMessageCaptor().assertNotSentByTenant(ecTenantId);
      return;
    }

    var record = messageCaptors.getDdccmMessageCaptor().getValue(ecTenantId);
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(ecTenantId);
    final var activityMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(ecTenantId);

    // DDCCM
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
        .hasSize(1)
        .first()
        .satisfies(e -> assertThat(e.getAction()).isEqualTo(
            com.ruckus.acx.ddccm.protobuf.common.Action.ADD))
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
        .satisfies(e -> assertThat(e.getTenantId()).isEqualTo(ecTenantId))
        .satisfies(e -> assertThat(e.getVenueId()).isEqualTo(venueInstanceId))
        .satisfies(e -> assertThat(e.getWlanId()).isEqualTo(networkInstance.getId()))
        .satisfies(e -> {
          if (vlanPoolInstanceOnVenueId == null) {
            assertThat(e.hasVlanPoolId()).isFalse();
          } else {
            assertThat(e.getVlanPoolId().getValue()).isEqualTo(vlanPoolInstanceOnVenueId);
          }
        });

    Set<String> apGroupIds = ddccmRequest.getOperationsList().stream()
        .filter(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanApGroup)
        .map(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanApGroup)
        .map(WlanApGroup::getApGroupId)
        .collect(Collectors.toSet());
    assertThat(apGroupIds)
        .hasSize(apGroupCount);

    Set<String> vlanPoolIdOfApGroups = ddccmRequest.getOperationsList().stream()
        .filter(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanApGroup)
        .map(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanApGroup)
        .map(WlanApGroup::getVlanPoolId)
        .filter(vpId -> vpId != null && !StringUtils.isBlank(vpId.getValue()))
        .map(StringValue::getValue)
        .collect(Collectors.toSet());

    if (vlanPoolInstanceOnApGroupId != null) {
      assertThat(vlanPoolIdOfApGroups).contains(vlanPoolInstanceOnApGroupId);
    } else {
      assertThat(vlanPoolIdOfApGroups).isEmpty();
    }

    // CmnCfgCollector
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by NetworkVenueCfgCollectorOperationBuilder
                  .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.ADD)
                  .as("The ADD venue operation count should be 1")
                  .hasSize(1);
            }));

    // Activity
    assertThat(activityMessage.getPayload().getStep()).isEqualTo(
        STEP_ADD_VENUE_TEMPLATE_INSTANCE_POST);

    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(ecTenantId);
  }

  private void assertNetworkInstanceCreated(String mspTenantId, String ecTenantId,
      Network networkTemplate, String networkInstanceId) {

    var planMessage = messageCaptors.getActivityPlanMessageCaptor().getValue(ecTenantId);
    assertThat(planMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(plan -> {
          assertThat(plan.getViewSteps(0).getName()).isEqualTo(ADD_WIFI_NETWORK);
        });

    var ecActivityMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(ecTenantId);
    assertThat(ecActivityMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(status -> {
          assertThat(status.getStatus()).isEqualTo(ConfigurationStatus.Status.OK);
          assertThat(status.getStep()).isEqualTo(ADD_WIFI_NETWORK);
        });

    var mspActivityMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(mspTenantId);
    assertThat(mspActivityMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(status -> {
          assertThat(status.getStatus()).isEqualTo(ConfigurationStatus.Status.OK);
          assertThat(status.getStep()).isEqualTo(ADD_WIFI_NETWORK_BY_TEMPLATE);
        });

    Network networkInstance = repositoryUtil.find(Network.class, networkInstanceId, ecTenantId);

    assertThat(networkInstance).isNotNull();
    assertThat(networkInstance.getTemplateId()).isEqualTo(networkTemplate.getId());
    assertThat(networkInstance.getTemplateVersion()).isEqualTo(
        networkTemplate.getUpdatedDate().getTime());

    messageCaptors.assertThat(kafkaTopicProvider.getDdccmCfgRequest())
        .doesNotSendByTenants(ecTenantId);

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(ecTenantId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();
  }

  private VenueEvent buildTemplateInstanceEvent(Venue venueTemplate, String instanceId,
      String ecTenantId) {
    return buildTemplateInstanceEvent(venueTemplate, instanceId, ecTenantId, false);
  }

  private VenueEvent buildTemplateInstanceEvent(Venue venueTemplate, String instanceId,
      String ecTenantId, boolean isEnforced) {
    return VenueEvent.newBuilder()
        .setTenantId(venueTemplate.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.ADD)
            .setVenueTemplateInstance(
                com.ruckus.cloud.venue.proto.VenueTemplateInstance.newBuilder()
                    .setVenueId(instanceId)
                    .setVenueName(venueTemplate.getName())
                    .setTemplateId(venueTemplate.getId())
                    .setTemplateVersion(System.currentTimeMillis())
                    .setIsEnforced(isEnforced)
                    .setEcTenantId(ecTenantId)
                    .setAddress(Address.newBuilder()
                        .setCountryCode(venueTemplate.getCountryCode())
                        .setTimezone(venueTemplate.getTimezone())
                        .setAddressLine(venueTemplate.getAddressLine())
                        .setLongitude(
                            Double.parseDouble(venueTemplate.getDeviceGps().getLongitude()))
                        .setLatitude(Double.parseDouble(venueTemplate.getDeviceGps().getLatitude()))
                        .build()))).build();
  }

  private ActivityExecutionPlan buildActivityExecutionPlan(
      String flowName, WifiActivityPlan.Action action, String entityType, String entityId) {
    return ActivityPlan.ActivityExecutionPlan.newBuilder()
        .setProductType("WIFI")
        .setEntityType(entityType)
        .setEntityId(entityId)
        .setSuccessTemplate("Success...")
        .setFailedTemplate("Failed...")
        .setDescriptionTemplate("Desc...")
        .addPlanSteps(ActivityPlan.PlanStep.newBuilder()
            .setService("wifi")
            .setResponseType(ActivityCommon.StepResponseType.REQUEST)
            .setName(flowName)
            .setViewStep(flowName)
            .build())
        .addViewSteps(ActivityPlan.ViewStep.newBuilder()
            .setDescription("Desc...")
            .setName(flowName)
            .build())
        .setAction(action.name())
        .build();
  }

  private Tenant createEcTenant(Tenant tenant, String ecTenantId) {
    var ecTenant = new Tenant(ecTenantId);
    ecTenant.setRecoveryPsk("recover");
    ecTenant.setLatestReleaseVersion(tenant.getLatestReleaseVersion());
    return repositoryUtil.createOrUpdate(ecTenant, tenant.getId(), randomTxId());
  }

  /*
  Enforcement Checklist
  - DONE Template + isEnforced:true -> create Instance -> check Instance.isEnforced:true
  - DONE Template + Instance -> set Template.IsEnforced:true ->  check Instance.isEnforced:true
  - DONE Template + isEnforced:true -> update Template -> check Template.isEnforced:true
  - DONE Template + Instance + isEnforced:true -> delete Template -> check Instance.isEnforced:false
  - check VenueEvent
    - TODO VenueEvent should always be valid?
  - check API blocking
    - TODO Template + Instance + isEnforced:true -> update Instance xxx -> should be blocked
    - TODO Template + Instance + isEnforced:true -> activate/deactivate Instance xxx -> should be blocked
  - check API delegation
    - TODO Template + Instance + isEnforced:true -> update Instance xxx -> should be allowed
    - TODO Template + Instance + isEnforced:true -> activate/deactivate Instance xxx -> should be allowed
   */

  /*
  VenueTemplate EnforcementSettings #1
  1. VenueTemplate -> set VenueTemplate.isEnforced:true
  2. VenueEvent -> update VenueTemplate -> check VenueTemplate.isEnforced:true
  3. AddVenueByTemplate -> check VenueInstance.isEnforced:true
  4. VenueEvent -> update VenueInstance -> check VenueInstance.isEnforced:true (should be delegation)
  5. set VenueTemplate.isEnforced:false -> check VenueInstance.isEnforced:false
   */
  @Test
  void testUpdateVenueTemplateEnforcementSettings_thenAddVenueTemplateInstance(
      Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue)
      throws InvalidProtocolBufferException, ExecutionException, InterruptedException {

    Venue savedVenueTemplate = repositoryUtil.find(Venue.class, venue.getId(), tenant.getId());
    assertThat(savedVenueTemplate).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(false);

    //////////
    // 0
    // add networkVenue and other bindings to VenueTemplate
    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, tenant.getId(), randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            tenant.getId(), randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.stream()
        .map(networkApGroup -> new NetworkApGroupRadioGenerator()
            .setNetworkApGroup(always(networkApGroup))
            .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
            .generate(1)
        )
        .forEach(radios -> radios.stream().forEach(
            radio -> repositoryUtil.createOrUpdate(radio, tenant.getId(),
                randomTxId())));

    //////////
    // 1
    // Update VenueTemplate EnforcementSettings -> true
    var eventEnforce = buildVenueTemplateEnforcementSettingsEvent(venue, true);
    var requestIdEnforce = randomTxId() + "$Enforce";
    messageUtil.sendVenueCfgChange(tenant.getId(), requestIdEnforce, eventEnforce);

    validateActivityMessages(tenant.getId(), requestIdEnforce,
        "UpdateVenueTemplateEnforcementSettingsInWifi", Status.OK);
    messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenant.getId());

    savedVenueTemplate = repositoryUtil.find(Venue.class, venue.getId(), tenant.getId());
    assertThat(savedVenueTemplate).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(true);

    messageUtil.clearMessage();

    //////////
    // 2
    // Update VenueTemplate name
    // should not affect the value of isEnforced
    venue.setName("My-Venue-Template");

    VenueEvent eventUpdateVenueTemplate = buildVenueUpdateEvent(venue);

    var requestIdUpdateVenueTemplate = randomTxId();
    messageUtil.sendVenueCfgChange(tenant.getId(), requestIdUpdateVenueTemplate,
        eventUpdateVenueTemplate);

    validateActivityMessages(tenant.getId(), requestIdUpdateVenueTemplate, "UpdateVenueTemplateInWifi", Status.OK);
    validateCmnCfgMessage(requestIdUpdateVenueTemplate, venue, 3);

    savedVenueTemplate = repositoryUtil.find(Venue.class, venue.getId(), tenant.getId());
    assertThat(savedVenueTemplate).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(true);

    messageUtil.clearMessage();

    //////////
    // 3
    // Add Venue By Template
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);
    var networkInstance = createInstanceFromTemplate(network, ecTenant);

    // when addVenueByTemplate, isEnforced is supposed to be included in VenueTemplateInstance message
    var eventAddByTemplate = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId(), true);
    var requestIdAddByTemplate = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(tenant.getId(), tenant.getId(), requestIdAddByTemplate, eventAddByTemplate);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
    assertNetworkVenueInstanceCreate(ecTenantId, networkVenue, networkInstance,
        instanceId, networkApGroups.size(), null, null);

    Venue savedVenueInstance = repositoryUtil.find(Venue.class, instanceId, ecTenantId);
    assertThat(savedVenueInstance).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(true);

    messageUtil.clearMessage();

    //////////
    // 4
    // Update Venue Instance
    // VenueEvent is sent from Venue service, the blocking should be done there already
    // any VenueEvent received should be valid to process
    savedVenueInstance.setName("My-Venue-Instance");

    VenueEvent eventUpdateVenueInstance = buildVenueUpdateEvent(savedVenueInstance);

    var requestIdUpdateVenueInstance = randomTxId();
    messageUtil.sendVenueCfgChange(ecTenantId, tenant.getId(), requestIdUpdateVenueInstance,
        eventUpdateVenueInstance);

    validateActivityMessages(ecTenantId, requestIdUpdateVenueInstance, "UpdateVenueInWifi", Status.OK);
    validateCmnCfgMessage(requestIdUpdateVenueInstance, savedVenueInstance, 3);

    savedVenueInstance = repositoryUtil.find(Venue.class, savedVenueInstance.getId(), ecTenantId);
    assertThat(savedVenueInstance).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(true);

    messageUtil.clearMessage();

    //////////
    // 5
    // Update VenueTemplate EnforcementSettings -> false
    var eventEnforce2 = buildVenueTemplateEnforcementSettingsEvent(venue, false);
    var requestIdEnforce2 = randomTxId() + "$Enforce2";
    messageUtil.sendVenueCfgChange(tenant.getId(), requestIdEnforce2, eventEnforce2);

    validateActivityMessages(tenant.getId(), requestIdEnforce2,
        "UpdateVenueTemplateEnforcementSettingsInWifi", Status.OK);
    messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenant.getId());

    savedVenueTemplate = repositoryUtil.find(Venue.class, venue.getId(), tenant.getId());
    assertThat(savedVenueTemplate).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(false);

    savedVenueInstance = repositoryUtil.find(Venue.class, instanceId, ecTenantId);
    assertThat(savedVenueInstance).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(false);
  }

  /*
  VenueTemplate EnforcementSettings #2
  1. AddVenueByTemplate -> check VenueInstance.isEnforced:false
  2. set VenueTemplate.isEnforced:true -> check VenueInstance.isEnforced:true
  3. delete VenueTemplate -> check VenueInstance.isEnforced:false
   */
  @Test
  void testAddVenueTemplateInstance_thenUpdateVenueTemplateEnforcementSettings(
      Tenant tenant,
      @Template Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork network,
      @Template NetworkVenue networkVenue) throws InvalidProtocolBufferException {

    venue.setName("my venue template");
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

    Venue savedVenueTemplate = repositoryUtil.find(Venue.class, venue.getId(), tenant.getId());
    assertThat(savedVenueTemplate).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(false);

    //////////
    // 0
    // add networkVenue and other bindings to VenueTemplate
    var apGroups = new ListValueGenerator<>(new ApGroupGenerator()
        .setVenue(always(venue))
        .setName(randomString())
        .setIsTemplate(alwaysTrue()), 3)
        .generate();
    apGroups.get(0).setIsDefault(true);
    apGroups.get(0).setName("");

    apGroups = apGroups.stream()
        .map(apGroup -> repositoryUtil.createOrUpdate(apGroup, tenant.getId(), randomTxId()))
        .toList();

    venue.setApGroups(apGroups);

    networkVenue.setIsAllApGroups(false);
    repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

    var networkApGroups = apGroups.stream()
        .map(apGroup -> new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate())
        .map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup,
            tenant.getId(), randomTxId()))
        .collect(Collectors.toList());
    networkVenue.setNetworkApGroups(networkApGroups);

    networkApGroups.stream()
        .map(networkApGroup -> new NetworkApGroupRadioGenerator()
            .setNetworkApGroup(always(networkApGroup))
            .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
            .generate(1)
        )
        .forEach(radios -> radios.stream().forEach(
            radio -> repositoryUtil.createOrUpdate(radio, tenant.getId(),
                randomTxId())));

    //////////
    // 1
    // Add Venue By Template
    var instanceId = randomId();
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);
    var networkInstance = createInstanceFromTemplate(network, ecTenant);

    // when addVenueByTemplate, isEnforced is supposed to be included in VenueTemplateInstance message
    var eventAddByTemplate = buildTemplateInstanceEvent(venue, instanceId, ecTenant.getId(), false);
    var requestIdAddByTemplate = randomTxId() + "$AddVenueByTemplate";

    messageUtil.sendVenueCfgChange(tenant.getId(), requestIdAddByTemplate, eventAddByTemplate);

    assertVenueInstanceCreated(ecTenantId, venue, instanceId);
    assertNetworkVenueInstanceCreate(ecTenantId, networkVenue, networkInstance,
        instanceId, networkApGroups.size(), null, null);

    Venue savedVenueInstance = repositoryUtil.find(Venue.class, instanceId, ecTenantId);
    assertThat(savedVenueInstance).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(false);


    //////////
    // 2
    // Update VenueTemplate EnforcementSettings -> true
    var eventEnforce2 = buildVenueTemplateEnforcementSettingsEvent(venue, true);
    var requestIdEnforce2 = randomTxId() + "$Enforce2";
    messageUtil.sendVenueCfgChange(tenant.getId(), requestIdEnforce2, eventEnforce2);

    validateActivityMessages(tenant.getId(), requestIdEnforce2,
        "UpdateVenueTemplateEnforcementSettingsInWifi", Status.OK);
    messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenant.getId());

    savedVenueTemplate = repositoryUtil.find(Venue.class, venue.getId(), tenant.getId());
    assertThat(savedVenueTemplate).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(true);

    savedVenueInstance = repositoryUtil.find(Venue.class, instanceId, ecTenantId);
    assertThat(savedVenueInstance).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(true);

    //////////
    // 3
    // Delete VenueTemplate -> VenueInstance isEnforced:false
    final VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(tenant.getId())
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(venue.getId())
                    .setIsTemplate(true)
                    .build()))
        .build();

    var requestIdDelete = randomTxId();
    messageUtil.sendVenueCfgChange(venue.getTenant().getId(), requestIdDelete, event);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenant.getId(), requestIdDelete);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    savedVenueTemplate = repositoryUtil.find(Venue.class, venue.getId(), tenant.getId());
    assertThat(savedVenueTemplate).isNull();

    savedVenueInstance = repositoryUtil.find(Venue.class, instanceId, ecTenantId);
    assertThat(savedVenueInstance).isNotNull()
        .extracting(Venue::getIsEnforced).isEqualTo(false);

  }

  @Test
  void testCloneVenueWithRadiusNetworkTemplate(Tenant tenant, @Template Venue venueInDb, @Template AAANetwork network, @Template Radius radius)
      throws Exception {
    final var cloneVenueSettings = new Venue();
    cloneVenueSettings.setId(randomId());
    cloneVenueSettings.setName(randomName());

    final var apGroup = new ApGroup(randomId());
    apGroup.setName(randomName());
    apGroup.setIsTemplate(true);
    apGroup.setVenue(venueInDb);
    final var apGroups = new ArrayList<ApGroup>();
    apGroups.add(apGroup);
    venueInDb.setApGroups(apGroups);
    repositoryUtil.createOrUpdate(apGroups.get(0));
    repositoryUtil.createOrUpdate(venueInDb);

    network.setAuthRadius(radius);
    var networkVenue = networkVenue()
        .setNetwork(always(network))
        .setVenue(always(venueInDb))
        .setIsTemplate(alwaysTrue())
        .setIsAllApGroups(alwaysFalse()).generate();
    network.setNetworkVenues(List.of(networkVenue));
    repositoryUtil.createOrUpdate(networkVenue);

    networkVenue.setNetworkApGroups(List.of(
        new NetworkApGroupGenerator()
            .setApGroup(always(venueInDb.getApGroups().get(0)))
            .setNetworkVenue(always(networkVenue))
            .generate()));
    var networkApGroup = networkVenue.getNetworkApGroups().get(0);
    repositoryUtil.createOrUpdate(networkApGroup);
    var networkApGroupRadio = new NetworkApGroupRadioGenerator()
        .setVlanId(always((short) 300))
        .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
        .setNetworkApGroup(always(networkApGroup))
        .generate();
    repositoryUtil.createOrUpdate(networkApGroupRadio);
    repositoryUtil.createOrUpdate(networkVenue);
    repositoryUtil.createOrUpdate(network);

    VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(tenant.getId())
        .addOperation(Operation.newBuilder().setAction(Action.ADD)
            .setVenueCloneSettings(VenueCloneSettings.newBuilder()
                .setVenue(com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(cloneVenueSettings.getId())
                    .setVenueName(cloneVenueSettings.getName()))
                .setCloneSourceId(venueInDb.getId())
                .build()))
        .build();

    final var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(tenant.getId(), requestId, event);

    Thread.sleep(1000);

    final var clonedVenue = repositoryUtil.find(Venue.class, cloneVenueSettings.getId());
    assertThat(clonedVenue).isNotNull();

    final var clonedApGroups = clonedVenue.getApGroups().stream().filter(a -> !a.getIsDefault()).toList();
    final var apGroupRequestId1 = requestId + "$" + CLONE_VENUE_AP_GROUP_TEMPLATE + "$$" + clonedApGroups.get(0).getName();
    validateActivityMessages(TxCtxHolder.tenantId(), requestId, "CloneVenueTemplateInWifi", Status.OK);
    validateActivityMessages(TxCtxHolder.tenantId(), apGroupRequestId1, CLONE_VENUE_AP_GROUP_TEMPLATE, Status.OK);
    assertThat(clonedApGroups).isNotNull()
        .first()
        .matches(AbstractTenantAwareTemplateBaseEntity::getIsTemplate)
        .matches(a -> Objects.equals(a.getNetworkApGroups().size(), apGroups.size()))
        .extracting(ApGroup::getNetworkApGroups).isNotNull()
        .matches(na -> Objects.equals(na.get(0).getNetworkVenue().getVenue().getId(), clonedVenue.getId()))
        .extracting(na -> na.get(0).getNetworkApGroupRadios())
        .matches(radio -> Objects.equals(radio.get(0).getVlanId(), networkApGroupRadio.getVlanId()));
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testCloneVenueTemplate(Tenant tenant, @Template Venue venueInDb, @Template AAANetwork network, @Template Radius radius,
      @Template SyslogServerProfile syslogServerProfile,
      @Template DhcpConfigServiceProfile dhcpConfigServiceProfile,
      @Template VlanPool vlanPool)
      throws Exception {
    final var cloneVenueSettings = new Venue();
    cloneVenueSettings.setId(randomId());
    cloneVenueSettings.setName(randomName());

    venueInDb.setApPassword("12345678");
    venueInDb.getRebootTimeout().setGatewayLossTimeout(2000);
    venueInDb.getRebootTimeout().setServerLossTimeout(8000);
    venueInDb.getRadioCustomization().getRadioParams24G().setChangeInterval((short)24);
    venueInDb.getRadioCustomization().getRadioParams50G().setChangeInterval((short)50);
    venueInDb.getRadioCustomization().getRadioParams6G().setChangeInterval((short)60);

    // VenueApModelSpecificAttributes Setting
    ApVersion firmware = ApVersionTestFixture.recommendedApVersion("6.2.0.103.200", a -> a.setSupportedApModels(List.of("R670")));
    VenueCurrentFirmware currentFirmware = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venueInDb, firmware, "R670");
    repositoryUtil.createOrUpdate(firmware);
    repositoryUtil.createOrUpdate(currentFirmware);
    repositoryUtil.createOrUpdate(TenantAvailableApFirmwareTestFixture.randomTenantAvailableApFirmware(tenant, firmware));
    var modelSpecificAttributes = VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(
        venueInDb, v -> {
          v.setLedOn(Boolean.FALSE);
          v.setModel(currentFirmware.getApModel());
          v.setBandMode(BandModeEnum.DUAL);
        });
    repositoryUtil.createOrUpdate(modelSpecificAttributes);
    venueInDb.setModelSpecificAttributes(List.of(modelSpecificAttributes));

    // IOT Setting
    final var iotSettings = new VenueApIotSettings();
    iotSettings.setEnabled(true);
    iotSettings.setMqttBrokerAddress("*************");
    venueInDb.setIotSettings(iotSettings);

    // SyslogServerProfile
    venueInDb.setSyslogServerProfile(syslogServerProfile);

    // Rogue
    final var roguePolicy = Generators.rogueClassificationPolicy()
        .generate(policy -> policy.getRules()
            .forEach(rule -> rule.setRogueClassificationPolicy(policy)));
    final var roguePolicyVenue = new RogueClassificationPolicyVenue(randomId());
    roguePolicy.setIsTemplate(true);
    roguePolicyVenue.setRogueClassificationPolicy(roguePolicy);
    roguePolicyVenue.setVenue(venueInDb);
    venueInDb.setRogueClassificationPolicyVenue(List.of(roguePolicyVenue));
    repositoryUtil.createOrUpdate(roguePolicy);
    repositoryUtil.createOrUpdate(roguePolicyVenue);

    // DHCP
    final var dhcpPoolVenue = new DhcpPoolVenue();
    dhcpPoolVenue.setDhcpPool(dhcpConfigServiceProfile.getDhcpPools().get(0));
    dhcpPoolVenue.setVenue(venueInDb);
    repositoryUtil.createOrUpdate(dhcpPoolVenue);
    venueInDb.getDhcpServiceSetting().setEnabled(true);
    venueInDb.setDhcpConfigServiceProfile(dhcpConfigServiceProfile);
    venueInDb.setDhcpPoolVenues(List.of(dhcpPoolVenue));

    // mDNS
    final var bonjourFencing = Generators.bonjourFencing().generate();
    bonjourFencing.setVenue(venueInDb);
    for (final var rule : bonjourFencing.getWiredRules()) {
      rule.setBonjourFencing(bonjourFencing);
    }
    venueInDb.setBonjourFencingEnabled(true);
    venueInDb.setBonjourFencing(List.of(bonjourFencing));
    repositoryUtil.createOrUpdate(bonjourFencing);

    // ApGroup
    final var defaultApGroup = new ApGroup(randomId());
    defaultApGroup.setIsDefault(true);
    defaultApGroup.setIsTemplate(true);
    defaultApGroup.setVenue(venueInDb);
    final var apGroup = new ApGroup(randomId());
    apGroup.setName(randomName());
    apGroup.setIsTemplate(true);
    apGroup.setVenue(venueInDb);
    final var apGroups = new ArrayList<ApGroup>();
    apGroups.add(defaultApGroup);
    apGroups.add(apGroup);
    venueInDb.setApGroups(apGroups);
    repositoryUtil.createOrUpdate(apGroups.get(0));
    repositoryUtil.createOrUpdate(apGroups.get(1));
    repositoryUtil.createOrUpdate(venueInDb);

    // Network with radius and vlan pool profile
    network.setAuthRadius(radius);
    var networkVenue = networkVenue()
        .setNetwork(always(network))
        .setVenue(always(venueInDb))
        .setIsTemplate(alwaysTrue())
        .setIsAllApGroups(alwaysFalse()).generate();
    network.setNetworkVenues(List.of(networkVenue));
    repositoryUtil.createOrUpdate(networkVenue);

    networkVenue.setNetworkApGroups(List.of(
        new NetworkApGroupGenerator()
            .setApGroup(always(apGroup))
            .setNetworkVenue(always(networkVenue))
            .generate()));
    var networkApGroup = networkVenue.getNetworkApGroups().get(0);
    repositoryUtil.createOrUpdate(networkApGroup);
    var networkApGroupRadio = new NetworkApGroupRadioGenerator()
        .setVlanPool(always(vlanPool))
        .setRadio(always(StrictRadioTypeEnum._2_4_GHz))
        .setNetworkApGroup(always(networkApGroup))
        .generate();
    repositoryUtil.createOrUpdate(networkApGroupRadio);
    repositoryUtil.createOrUpdate(networkVenue);
    repositoryUtil.createOrUpdate(network);

    VenueEvent event = VenueEvent.newBuilder()
        .setTenantId(tenant.getId())
        .addOperation(Operation.newBuilder().setAction(Action.ADD)
            .setVenueCloneSettings(VenueCloneSettings.newBuilder()
                .setVenue(com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(cloneVenueSettings.getId())
                    .setVenueName(cloneVenueSettings.getName()))
                .setCloneSourceId(venueInDb.getId())
                .build()))
        .build();

    final var requestId = randomTxId();
    messageUtil.sendVenueCfgChange(tenant.getId(), requestId, event);

    Thread.sleep(1000);

    final var record = messageCaptors.getDdccmMessageCaptor().getValue(txCtxExtension.getTenantId(), requestId);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue))
        .matches(request -> request.stream()
            .anyMatch(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApGroup));

    assertThat(repositoryUtil.find(Venue.class, cloneVenueSettings.getId())).isNotNull()
        .matches(v -> v.getId().equals(cloneVenueSettings.getId()))
        .matches(v -> v.getName().equals(cloneVenueSettings.getName()))
        .matches(v -> v.getCountryCode().equals(venueInDb.getCountryCode()))
        .matches(v -> v.getAddressLine().equals(venueInDb.getAddressLine()))
        .matches(v -> v.getTimezone().equals(venueInDb.getTimezone()))
        .matches(v -> v.getApPassword().equals(venueInDb.getApPassword()))
        .matches(v -> Objects.equals(v.getRebootTimeout().getGatewayLossTimeout(), (venueInDb.getRebootTimeout().getGatewayLossTimeout())))
        .matches(v -> Objects.equals(v.getRebootTimeout().getServerLossTimeout(), (venueInDb.getRebootTimeout().getServerLossTimeout())))
        .matches(v -> Objects.equals(v.getRadioCustomization().getRadioParams24G().getChangeInterval(), (venueInDb.getRadioCustomization().getRadioParams24G().getChangeInterval())))
        .matches(v -> Objects.equals(v.getRadioCustomization().getRadioParams50G().getChangeInterval(), (venueInDb.getRadioCustomization().getRadioParams50G().getChangeInterval())))
        .matches(v -> Objects.equals(v.getRadioCustomization().getRadioParams6G().getChangeInterval(), (venueInDb.getRadioCustomization().getRadioParams6G().getChangeInterval())))
        .matches(v -> Objects.equals(v.getModelSpecificAttributes().get(0).getModel(), (venueInDb.getModelSpecificAttributes().get(0).getModel())))
        .matches(v -> v.getIotSettings().getEnabled().equals(venueInDb.getIotSettings().getEnabled()))
        .matches(v -> v.getIotSettings().getMqttBrokerAddress().equals(venueInDb.getIotSettings().getMqttBrokerAddress()))
        .matches(v -> v.getBandBalancing().getEnabled().equals(venueInDb.getBandBalancing().getEnabled()))
        .matches(v -> v.getBandBalancing().getClientPercent24().equals(venueInDb.getBandBalancing().getClientPercent24()))
        .matches(v -> v.getSyslogServerProfile().getId().equals(venueInDb.getSyslogServerProfile().getId()))
        .matches(v -> v.getDhcpConfigServiceProfile().getId().equals(venueInDb.getDhcpConfigServiceProfile().getId()))
        .matches(v -> Objects.equals(v.getDhcpServiceSetting().getEnabled(), venueInDb.getDhcpServiceSetting().getEnabled()))
        .matches(v -> Objects.equals(v.getDhcpPoolVenues().size(), venueInDb.getDhcpPoolVenues().size()))
        .matches(v -> v.getRogueClassificationPolicyVenue().get(0).getRogueClassificationPolicy().getId().equals(venueInDb.getRogueClassificationPolicyVenue().get(0).getRogueClassificationPolicy().getId()))
        .matches(Venue::getBonjourFencingEnabled)
        .matches(v -> !Objects.equals(v.getBonjourFencing().get(0).getId(), venueInDb.getBonjourFencing().get(0).getId()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getDescription(), venueInDb.getBonjourFencing().get(0).getDescription()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getCustomServiceName(), venueInDb.getBonjourFencing().get(0).getCustomServiceName()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getCustomMappingEnabled(), venueInDb.getBonjourFencing().get(0).getCustomMappingEnabled()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getCustomStrings(), venueInDb.getBonjourFencing().get(0).getCustomStrings()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getService(), venueInDb.getBonjourFencing().get(0).getService()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getWirelessRule(), venueInDb.getBonjourFencing().get(0).getWirelessRule()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getWiredEnabled(), venueInDb.getBonjourFencing().get(0).getWiredEnabled()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getWirelessEnabled(), venueInDb.getBonjourFencing().get(0).getWirelessEnabled()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getWiredRules().get(0).getName(), venueInDb.getBonjourFencing().get(0).getWiredRules().get(0).getName()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getWiredRules().get(0).getFencingRange(), venueInDb.getBonjourFencing().get(0).getWiredRules().get(0).getFencingRange()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getWiredRules().get(0).getClosestApMac(), venueInDb.getBonjourFencing().get(0).getWiredRules().get(0).getClosestApMac()))
        .matches(v -> Objects.equals(v.getBonjourFencing().get(0).getWiredRules().get(0).getDeviceMacAddresses().toString(), venueInDb.getBonjourFencing().get(0).getWiredRules().get(0).getDeviceMacAddresses().toString()))
        .matches(AbstractTenantAwareTemplateBaseEntity::getIsTemplate)
        .matches(v -> !v.getIsEnforced());

    final var clonedVenue = repositoryUtil.find(Venue.class, cloneVenueSettings.getId());
    final var clonedApGroups = clonedVenue.getApGroups().stream().filter(a -> !a.getIsDefault()).toList();
    final var apGroupRequestId1 = requestId + "$" + CLONE_VENUE_AP_GROUP_TEMPLATE + "$$" + clonedApGroups.get(0).getName();
    validateActivityMessages(TxCtxHolder.tenantId(), requestId, "CloneVenueTemplateInWifi", Status.OK);
    validateActivityMessages(TxCtxHolder.tenantId(), apGroupRequestId1, CLONE_VENUE_AP_GROUP_TEMPLATE, Status.OK);
    assertThat(clonedApGroups).isNotNull()
        .first()
        .matches(AbstractTenantAwareTemplateBaseEntity::getIsTemplate)
        .extracting(ApGroup::getNetworkApGroups).isNotNull()
        .matches(na -> Objects.equals(na.get(0).getNetworkVenue().getVenue().getId(), clonedVenue.getId()))
        .extracting(na -> na.get(0).getNetworkApGroupRadios())
        .matches(radio -> Objects.equals(radio.get(0).getVlanId(), networkApGroupRadio.getVlanId()))
        .matches(radio -> Objects.equals(radio.get(0).getVlanPool().getId(), networkApGroupRadio.getVlanPool().getId()));
  }

  private VenueEvent buildVenueTemplateEnforcementSettingsEvent(Venue venueTemplate, boolean isEnforced) {
    return VenueEvent.newBuilder()
        .setTenantId(venueTemplate.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.MODIFY)
            .setVenueTemplateEnforcementSettings(
                VenueTemplateEnforcementSettings.newBuilder()
                    .setVenueId(venueTemplate.getId())
                    .setVenueName(venueTemplate.getName())
                    .setIsEnforced(isEnforced)
            )).build();
  }

  private VenueEvent buildVenueUpdateEvent(Venue venue) {
    return VenueEvent.newBuilder()
        .setTenantId(venue.getTenant().getId())
        .addOperation(Operation.newBuilder().setAction(Action.MODIFY)
            .setVenue(
                com.ruckus.cloud.venue.proto.Venue.newBuilder()
                    .setVenueId(venue.getId())
                    .setVenueName(venue.getName())
                    .setIsTemplate(venue.getIsTemplate())
                    .setAddress(Address.newBuilder()
                        .setCountryCode(venue.getCountryCode())
                        .setTimezone(venue.getTimezone())
                        .setAddressLine(venue.getAddressLine())
                        .setLongitude(Double.parseDouble(venue.getDeviceGps().getLongitude()))
                        .setLatitude(Double.parseDouble(venue.getDeviceGps().getLatitude()))
                        .build())))
        .build();
  }

  private void validateActivityMessages(String tenantId, String requestId, String expectedStep, Status expectedStatus) throws InvalidProtocolBufferException {
    ActivityCfgChangeMessageCaptor captor = messageCaptors.getActivityCfgChangeMessageCaptor();
    final var activityCfgChangeRespMessage = captor
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(expectedStatus))
        .matches(p -> p.getStep().equals(expectedStep))
        .extracting(ConfigurationStatus::getEventDate)
        .isNotNull();
  }

  private void validateCmnCfgMessage(String requestId, Venue venue, int expectedSize) {
    var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(venue.getTenant().getId(), requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    log.warn("opList[{}]: {}",
        cmnCfgCollectorMessage.getPayload().getOperationsList().size(),
        cmnCfgCollectorMessage.getPayload().getOperationsList());

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(msg -> msg.getTenantId().equals(venue.getTenant().getId()))
        .matches(msg -> msg.getRequestId().equals(requestId))
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty()
        .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
        .hasSize(expectedSize)
        .allMatch(op -> op.getOpType() == OpType.MOD)
        .allSatisfy(op -> {
          assertThat(op.getDocOrThrow(Key.VENUE_NAME))
              .isEqualTo(ValueUtils.stringValue(venue.getName()));
          assertThat(op.getDocOrThrow(Key.IS_TEMPLATE))
              .isEqualTo(ValueUtils.boolValue(venue.getIsTemplate()));
        });
  }

  /*
   * Tests for Decouple API
   */

  /**
   * Test decouple API when EC tenant has no venue instances
   */
  @Test
  void testDecoupleVenueTemplateInstances_whenEcTenantHasNoInstances(Tenant tenant) throws Exception {
    // Create an EC tenant with no venue instances
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    // Send decouple event
    var event = buildVenueTemplateInstanceDecoupleEvent(tenant.getId());
    var requestId = randomTxId() + "$DecoupleVenueTemplateInstances";

    messageUtil.sendVenueCfgChange(tenant.getId(), requestId, event);

    // Verify activity message indicates success (even with no instances to decouple)
    validateActivityMessages(tenant.getId(), requestId, "DecoupleVenueInstancesInWifi", Status.OK);

    // No DDCCM or CmnCfgCollector messages should be sent since there are no instances
    messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenant.getId());
  }

  /**
   * Test decouple API when EC tenant has mixed venue instances
   * Some instances have templateId (template instances) and some don't (standalone instances)
   */
  @Test
  void testDecoupleVenueTemplateInstances_whenEcTenantHasMixedInstances(
      Tenant tenant, @Template Venue venueTemplate) throws Exception {
    // Create an EC tenant
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    // Create venue template instance (has templateId)
    var templateInstanceId = randomId();
    var templateInstance = createVenueInstanceFromTemplate(venueTemplate, ecTenant, templateInstanceId);
    templateInstance.setIsEnforced(true);
    templateInstance.setTemplateVersion(System.currentTimeMillis());
    repositoryUtil.createOrUpdate(templateInstance, ecTenantId, randomTxId());

    // Create standalone venue instance (no templateId)
    var standaloneInstanceId = randomId();
    var standaloneInstance = new Venue(standaloneInstanceId);
    standaloneInstance.setName("Standalone Venue");
    standaloneInstance.setTenant(ecTenant);
    standaloneInstance.setIsTemplate(false);
    standaloneInstance.setIsEnforced(false);
    standaloneInstance.setTemplateId(null);
    standaloneInstance.setTemplateVersion(null);
    standaloneInstance.setCountryCode("US");
    standaloneInstance.setTimezone("America/New_York");
    standaloneInstance.setAddressLine("123 Main St");
    repositoryUtil.createOrUpdate(standaloneInstance, ecTenantId, randomTxId());

    // Create another venue template instance (has templateId)
    var templateInstance2Id = randomId();
    var templateInstance2 = createVenueInstanceFromTemplate(venueTemplate, ecTenant, templateInstance2Id);
    templateInstance2.setIsEnforced(true);
    templateInstance2.setTemplateVersion(System.currentTimeMillis() - 1000);
    repositoryUtil.createOrUpdate(templateInstance2, ecTenantId, randomTxId());

    // Verify initial state
    var initialTemplateInstance = repositoryUtil.find(Venue.class, templateInstanceId, ecTenantId);
    var initialStandaloneInstance = repositoryUtil.find(Venue.class, standaloneInstanceId, ecTenantId);
    var initialTemplateInstance2 = repositoryUtil.find(Venue.class, templateInstance2Id, ecTenantId);

    assertThat(initialTemplateInstance).isNotNull()
        .matches(v -> v.getTemplateId() != null, "should have templateId")
        .matches(v -> v.getTemplateVersion() != null, "should have templateVersion")
        .matches(v -> v.getIsEnforced(), "should be enforced");

    assertThat(initialStandaloneInstance).isNotNull()
        .matches(v -> v.getTemplateId() == null, "should not have templateId")
        .matches(v -> v.getTemplateVersion() == null, "should not have templateVersion")
        .matches(v -> !v.getIsEnforced(), "should not be enforced");

    assertThat(initialTemplateInstance2).isNotNull()
        .matches(v -> v.getTemplateId() != null, "should have templateId")
        .matches(v -> v.getTemplateVersion() != null, "should have templateVersion")
        .matches(v -> v.getIsEnforced(), "should be enforced");

    // Send decouple event for EC tenant
    var event = buildVenueTemplateInstanceDecoupleEvent(ecTenantId);
    var requestId = randomTxId() + "$DecoupleVenueTemplateInstances";

    messageUtil.sendVenueCfgChange(ecTenantId, requestId, event);

    // Verify activity message indicates success
    validateActivityMessages(ecTenantId, requestId, "DecoupleVenueInstancesInWifi", Status.OK);

    // Verify final state after decouple
    var finalTemplateInstance = repositoryUtil.find(Venue.class, templateInstanceId, ecTenantId);
    var finalStandaloneInstance = repositoryUtil.find(Venue.class, standaloneInstanceId, ecTenantId);
    var finalTemplateInstance2 = repositoryUtil.find(Venue.class, templateInstance2Id, ecTenantId);

    // Template instances should be decoupled
    assertThat(finalTemplateInstance).isNotNull()
        .matches(v -> v.getTemplateId() == null, "templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "isEnforced should be false after decouple");

    assertThat(finalTemplateInstance2).isNotNull()
        .matches(v -> v.getTemplateId() == null, "templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "isEnforced should be false after decouple");

    // Standalone instance should remain unchanged
    assertThat(finalStandaloneInstance).isNotNull()
        .matches(v -> v.getTemplateId() == null, "standalone instance should still have null templateId")
        .matches(v -> v.getTemplateVersion() == null, "standalone instance should still have null templateVersion")
        .matches(v -> !v.getIsEnforced(), "standalone instance should still not be enforced");

    // No DDCCM messages should be sent for decouple operation
    messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(ecTenantId);
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(ecTenantId);
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(ecTenantId);
  }

  /**
   * Test decouple API when EC tenant has instances from multiple venue templates
   */
  @Test
  void testDecoupleVenueTemplateInstances_whenEcTenantHasInstancesFromMultipleTemplates(
      Tenant tenant) throws Exception {
    // Create an EC tenant
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    // Create first venue template
    var venueTemplate1 = new Venue(randomId());
    venueTemplate1.setName("Venue Template 1");
    venueTemplate1.setTenant(tenant);
    venueTemplate1.setIsTemplate(true);
    venueTemplate1.setIsEnforced(true);
    venueTemplate1.setCountryCode("US");
    venueTemplate1.setTimezone("America/New_York");
    venueTemplate1.setAddressLine("123 Template St");
    repositoryUtil.createOrUpdate(venueTemplate1, tenant.getId(), randomTxId());

    // Create second venue template with different enforcement setting
    var venueTemplate2 = new Venue(randomId());
    venueTemplate2.setName("Venue Template 2");
    venueTemplate2.setTenant(tenant);
    venueTemplate2.setIsTemplate(true);
    venueTemplate2.setIsEnforced(false);
    venueTemplate2.setCountryCode("CA");
    venueTemplate2.setTimezone("America/Toronto");
    venueTemplate2.setAddressLine("456 Template Ave");
    repositoryUtil.createOrUpdate(venueTemplate2, tenant.getId(), randomTxId());

    // Create instances from first template (enforced)
    var template1Instance1Id = randomId();
    var template1Instance1 = createVenueInstanceFromTemplate(venueTemplate1, ecTenant, template1Instance1Id);
    template1Instance1.setIsEnforced(true); // Should match template1's enforcement
    template1Instance1.setTemplateVersion(System.currentTimeMillis());
    repositoryUtil.createOrUpdate(template1Instance1, ecTenantId, randomTxId());

    var template1Instance2Id = randomId();
    var template1Instance2 = createVenueInstanceFromTemplate(venueTemplate1, ecTenant, template1Instance2Id);
    template1Instance2.setIsEnforced(true); // Should match template1's enforcement
    template1Instance2.setTemplateVersion(System.currentTimeMillis() - 1000);
    repositoryUtil.createOrUpdate(template1Instance2, ecTenantId, randomTxId());

    // Create instances from second template (not enforced)
    var template2Instance1Id = randomId();
    var template2Instance1 = createVenueInstanceFromTemplate(venueTemplate2, ecTenant, template2Instance1Id);
    template2Instance1.setIsEnforced(false); // Should match template2's enforcement
    template2Instance1.setTemplateVersion(System.currentTimeMillis() - 2000);
    repositoryUtil.createOrUpdate(template2Instance1, ecTenantId, randomTxId());

    var template2Instance2Id = randomId();
    var template2Instance2 = createVenueInstanceFromTemplate(venueTemplate2, ecTenant, template2Instance2Id);
    template2Instance2.setIsEnforced(false); // Should match template2's enforcement
    template2Instance2.setTemplateVersion(System.currentTimeMillis() - 3000);
    repositoryUtil.createOrUpdate(template2Instance2, ecTenantId, randomTxId());

    // Create a standalone instance (no templateId) - should not be affected
    var standaloneInstanceId = randomId();
    var standaloneInstance = new Venue(standaloneInstanceId);
    standaloneInstance.setName("Standalone Venue");
    standaloneInstance.setTenant(ecTenant);
    standaloneInstance.setIsTemplate(false);
    standaloneInstance.setIsEnforced(false);
    standaloneInstance.setTemplateId(null);
    standaloneInstance.setTemplateVersion(null);
    standaloneInstance.setCountryCode("MX");
    standaloneInstance.setTimezone("America/Mexico_City");
    standaloneInstance.setAddressLine("789 Standalone Blvd");
    repositoryUtil.createOrUpdate(standaloneInstance, ecTenantId, randomTxId());

    // Verify initial state
    var initialTemplate1Instance1 = repositoryUtil.find(Venue.class, template1Instance1Id, ecTenantId);
    var initialTemplate1Instance2 = repositoryUtil.find(Venue.class, template1Instance2Id, ecTenantId);
    var initialTemplate2Instance1 = repositoryUtil.find(Venue.class, template2Instance1Id, ecTenantId);
    var initialTemplate2Instance2 = repositoryUtil.find(Venue.class, template2Instance2Id, ecTenantId);
    var initialStandaloneInstance = repositoryUtil.find(Venue.class, standaloneInstanceId, ecTenantId);

    // Verify instances from template1 (enforced)
    assertThat(initialTemplate1Instance1).isNotNull()
        .matches(v -> venueTemplate1.getId().equals(v.getTemplateId()), "should have template1 ID")
        .matches(v -> v.getTemplateVersion() != null, "should have templateVersion")
        .matches(v -> v.getIsEnforced(), "should be enforced like template1");

    assertThat(initialTemplate1Instance2).isNotNull()
        .matches(v -> venueTemplate1.getId().equals(v.getTemplateId()), "should have template1 ID")
        .matches(v -> v.getTemplateVersion() != null, "should have templateVersion")
        .matches(v -> v.getIsEnforced(), "should be enforced like template1");

    // Verify instances from template2 (not enforced)
    assertThat(initialTemplate2Instance1).isNotNull()
        .matches(v -> venueTemplate2.getId().equals(v.getTemplateId()), "should have template2 ID")
        .matches(v -> v.getTemplateVersion() != null, "should have templateVersion")
        .matches(v -> !v.getIsEnforced(), "should not be enforced like template2");

    assertThat(initialTemplate2Instance2).isNotNull()
        .matches(v -> venueTemplate2.getId().equals(v.getTemplateId()), "should have template2 ID")
        .matches(v -> v.getTemplateVersion() != null, "should have templateVersion")
        .matches(v -> !v.getIsEnforced(), "should not be enforced like template2");

    // Verify standalone instance
    assertThat(initialStandaloneInstance).isNotNull()
        .matches(v -> v.getTemplateId() == null, "should not have templateId")
        .matches(v -> v.getTemplateVersion() == null, "should not have templateVersion")
        .matches(v -> !v.getIsEnforced(), "should not be enforced");

    // Send decouple event for EC tenant
    var event = buildVenueTemplateInstanceDecoupleEvent(ecTenantId);
    var requestId = randomTxId() + "$DecoupleVenueTemplateInstances";

    messageUtil.sendVenueCfgChange(ecTenantId, requestId, event);

    // Verify activity message indicates success
    validateActivityMessages(ecTenantId, requestId, "DecoupleVenueInstancesInWifi", Status.OK);

    // Verify final state after decouple
    var finalTemplate1Instance1 = repositoryUtil.find(Venue.class, template1Instance1Id, ecTenantId);
    var finalTemplate1Instance2 = repositoryUtil.find(Venue.class, template1Instance2Id, ecTenantId);
    var finalTemplate2Instance1 = repositoryUtil.find(Venue.class, template2Instance1Id, ecTenantId);
    var finalTemplate2Instance2 = repositoryUtil.find(Venue.class, template2Instance2Id, ecTenantId);
    var finalStandaloneInstance = repositoryUtil.find(Venue.class, standaloneInstanceId, ecTenantId);

    // All template instances should be decoupled regardless of which template they came from
    assertThat(finalTemplate1Instance1).isNotNull()
        .matches(v -> v.getTemplateId() == null, "templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "isEnforced should be false after decouple");

    assertThat(finalTemplate1Instance2).isNotNull()
        .matches(v -> v.getTemplateId() == null, "templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "isEnforced should be false after decouple");

    assertThat(finalTemplate2Instance1).isNotNull()
        .matches(v -> v.getTemplateId() == null, "templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "isEnforced should be false after decouple");

    assertThat(finalTemplate2Instance2).isNotNull()
        .matches(v -> v.getTemplateId() == null, "templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "isEnforced should be false after decouple");

    // Standalone instance should remain unchanged
    assertThat(finalStandaloneInstance).isNotNull()
        .matches(v -> v.getTemplateId() == null, "standalone instance should still have null templateId")
        .matches(v -> v.getTemplateVersion() == null, "standalone instance should still have null templateVersion")
        .matches(v -> !v.getIsEnforced(), "standalone instance should still not be enforced");

    // No DDCCM messages should be sent for decouple operation
    messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(ecTenantId);
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(ecTenantId);
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(ecTenantId);
  }

  /**
   * Test decouple API when EC tenant has only template instances (no standalone instances)
   */
  @Test
  void testDecoupleVenueTemplateInstances_whenEcTenantHasOnlyTemplateInstances(
      Tenant tenant, @Template Venue venueTemplate) throws Exception {
    // Create an EC tenant
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    // Create multiple venue template instances (all have templateId)
    var templateInstance1Id = randomId();
    var templateInstance1 = createVenueInstanceFromTemplate(venueTemplate, ecTenant, templateInstance1Id);
    templateInstance1.setIsEnforced(true);
    templateInstance1.setTemplateVersion(System.currentTimeMillis());
    repositoryUtil.createOrUpdate(templateInstance1, ecTenantId, randomTxId());

    var templateInstance2Id = randomId();
    var templateInstance2 = createVenueInstanceFromTemplate(venueTemplate, ecTenant, templateInstance2Id);
    templateInstance2.setIsEnforced(true); // Same isEnforced value since they're from the same template
    templateInstance2.setTemplateVersion(System.currentTimeMillis() - 1000);
    repositoryUtil.createOrUpdate(templateInstance2, ecTenantId, randomTxId());

    // Verify initial state
    var initialTemplateInstance1 = repositoryUtil.find(Venue.class, templateInstance1Id, ecTenantId);
    var initialTemplateInstance2 = repositoryUtil.find(Venue.class, templateInstance2Id, ecTenantId);

    assertThat(initialTemplateInstance1).isNotNull()
        .matches(v -> v.getTemplateId() != null, "should have templateId")
        .matches(v -> v.getTemplateVersion() != null, "should have templateVersion")
        .matches(v -> v.getIsEnforced(), "should be enforced");

    assertThat(initialTemplateInstance2).isNotNull()
        .matches(v -> v.getTemplateId() != null, "should have templateId")
        .matches(v -> v.getTemplateVersion() != null, "should have templateVersion")
        .matches(v -> v.getIsEnforced(), "should be enforced");

    // Send decouple event for EC tenant
    var event = buildVenueTemplateInstanceDecoupleEvent(ecTenantId);
    var requestId = randomTxId() + "$DecoupleVenueTemplateInstances";

    messageUtil.sendVenueCfgChange(ecTenantId, requestId, event);

    // Verify activity message indicates success
    validateActivityMessages(ecTenantId, requestId, "DecoupleVenueInstancesInWifi", Status.OK);

    // Verify final state after decouple
    var finalTemplateInstance1 = repositoryUtil.find(Venue.class, templateInstance1Id, ecTenantId);
    var finalTemplateInstance2 = repositoryUtil.find(Venue.class, templateInstance2Id, ecTenantId);

    // Both template instances should be decoupled
    assertThat(finalTemplateInstance1).isNotNull()
        .matches(v -> v.getTemplateId() == null, "templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "isEnforced should be false after decouple");

    assertThat(finalTemplateInstance2).isNotNull()
        .matches(v -> v.getTemplateId() == null, "templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "isEnforced should be false after decouple");

    // No DDCCM messages should be sent for decouple operation
    messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(ecTenantId);
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(ecTenantId);
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(ecTenantId);
  }

  /**
   * Helper method to create a VenueEvent for decouple operation
   */
  private VenueEvent buildVenueTemplateInstanceDecoupleEvent(String tenantId) {
    return VenueEvent.newBuilder()
        .setTenantId(tenantId)
        .addOperation(Operation.newBuilder().setAction(Action.DELETE)
            .setVenueTemplateInstanceRelations(
                com.ruckus.cloud.venue.proto.VenueTemplateInstanceRelations.newBuilder().build()))
        .build();
  }

  /**
   * Helper method to create a venue instance from a template
   */
  private Venue createVenueInstanceFromTemplate(Venue template, Tenant ecTenant, String instanceId) {
    var instance = new Venue(instanceId);
    instance.setName(template.getName() + " Instance");
    instance.setTenant(ecTenant);
    instance.setIsTemplate(false);
    instance.setTemplateId(template.getId());
    instance.setCountryCode(template.getCountryCode());
    instance.setTimezone(template.getTimezone());
    instance.setAddressLine(template.getAddressLine());
    return instance;
  }

  /**
   * Test decouple API when EC tenant has both Venue and NetworkVenue template instances
   * Verifies that both types of template instances are properly decoupled
   */
  @Test
  void testDecoupleVenueTemplateInstances_whenEcTenantHasBothVenueAndNetworkVenueInstances(
      Tenant tenant, @Template Venue venueTemplate, 
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork @Template OpenNetwork networkTemplate,
      @Template NetworkVenue networkVenueTemplate) throws Exception {
    // Create an EC tenant
    var ecTenantId = randomId();
    var ecTenant = createEcTenant(tenant, ecTenantId);

    // Create venue template instances in EC tenant
    var venueInstance1Id = randomId();
    var venueInstance1 = createVenueInstanceFromTemplate(venueTemplate, ecTenant, venueInstance1Id);
    venueInstance1.setIsEnforced(true);
    venueInstance1.setTemplateVersion(System.currentTimeMillis());
    repositoryUtil.createOrUpdate(venueInstance1, ecTenantId, randomTxId());

    var venueInstance2Id = randomId();
    var venueInstance2 = createVenueInstanceFromTemplate(venueTemplate, ecTenant, venueInstance2Id);
    venueInstance2.setIsEnforced(true);
    venueInstance2.setTemplateVersion(System.currentTimeMillis() - 1000);
    repositoryUtil.createOrUpdate(venueInstance2, ecTenantId, randomTxId());

    // Create network template instance in EC tenant
    var networkInstance = createInstanceFromTemplate(networkTemplate, ecTenant);

    // Create a standalone venue instance (no templateId) for standalone NetworkVenue
    var standaloneVenueInstanceId = randomId();
    var standaloneVenueInstance = new Venue(standaloneVenueInstanceId);
    standaloneVenueInstance.setName("Standalone Venue");
    standaloneVenueInstance.setTenant(ecTenant);
    standaloneVenueInstance.setIsTemplate(false);
    standaloneVenueInstance.setIsEnforced(false);
    standaloneVenueInstance.setTemplateId(null);
    standaloneVenueInstance.setTemplateVersion(null);
    standaloneVenueInstance.setCountryCode("US");
    standaloneVenueInstance.setTimezone("America/New_York");
    standaloneVenueInstance.setAddressLine("456 Standalone St");
    repositoryUtil.createOrUpdate(standaloneVenueInstance, ecTenantId, randomTxId());

    // Create NetworkVenue template instances in EC tenant  
    var networkVenueInstance1 = new NetworkVenue();
    networkVenueInstance1.setId(randomId());
    networkVenueInstance1.setNetwork(networkInstance);
    networkVenueInstance1.setVenue(venueInstance1);
    networkVenueInstance1.setTenant(ecTenant);
    networkVenueInstance1.setIsTemplate(false);
    networkVenueInstance1.setTemplateId(networkVenueTemplate.getId());
    networkVenueInstance1.setTemplateVersion(System.currentTimeMillis() - 2000);
    networkVenueInstance1.setIsEnforced(true);
    networkVenueInstance1.setIsAllApGroups(true);
    repositoryUtil.createOrUpdate(networkVenueInstance1, ecTenantId, randomTxId());

    var networkVenueInstance2 = new NetworkVenue();
    networkVenueInstance2.setId(randomId());
    networkVenueInstance2.setNetwork(networkInstance);
    networkVenueInstance2.setVenue(venueInstance2);
    networkVenueInstance2.setTenant(ecTenant);
    networkVenueInstance2.setIsTemplate(false);
    networkVenueInstance2.setTemplateId(networkVenueTemplate.getId());
    networkVenueInstance2.setTemplateVersion(System.currentTimeMillis() - 3000);
    networkVenueInstance2.setIsEnforced(true);
    networkVenueInstance2.setIsAllApGroups(true);
    repositoryUtil.createOrUpdate(networkVenueInstance2, ecTenantId, randomTxId());

    // Create a standalone NetworkVenue instance (no templateId) - should not be affected
    var standaloneNetworkVenueInstanceId = randomId();
    var standaloneNetworkVenueInstance = new NetworkVenue();
    standaloneNetworkVenueInstance.setId(standaloneNetworkVenueInstanceId);
    standaloneNetworkVenueInstance.setNetwork(networkInstance);
    standaloneNetworkVenueInstance.setVenue(standaloneVenueInstance);
    standaloneNetworkVenueInstance.setTenant(ecTenant);
    standaloneNetworkVenueInstance.setIsTemplate(false);
    standaloneNetworkVenueInstance.setTemplateId(null);
    standaloneNetworkVenueInstance.setTemplateVersion(null);
    standaloneNetworkVenueInstance.setIsEnforced(false);
    standaloneNetworkVenueInstance.setIsAllApGroups(true);
    repositoryUtil.createOrUpdate(standaloneNetworkVenueInstance, ecTenantId, randomTxId());

    // Verify initial state for venue instances
    var initialVenueInstance1 = repositoryUtil.find(Venue.class, venueInstance1Id, ecTenantId);
    var initialVenueInstance2 = repositoryUtil.find(Venue.class, venueInstance2Id, ecTenantId);
    var initialStandaloneVenueInstance = repositoryUtil.find(Venue.class, standaloneVenueInstanceId, ecTenantId);

    assertThat(initialVenueInstance1).isNotNull()
        .matches(v -> v.getTemplateId() != null, "venue instance 1 should have templateId")
        .matches(v -> v.getTemplateVersion() != null, "venue instance 1 should have templateVersion")
        .matches(v -> v.getIsEnforced(), "venue instance 1 should be enforced");

    assertThat(initialVenueInstance2).isNotNull()
        .matches(v -> v.getTemplateId() != null, "venue instance 2 should have templateId")
        .matches(v -> v.getTemplateVersion() != null, "venue instance 2 should have templateVersion")
        .matches(v -> v.getIsEnforced(), "venue instance 2 should be enforced");

    assertThat(initialStandaloneVenueInstance).isNotNull()
        .matches(v -> v.getTemplateId() == null, "standalone venue should not have templateId")
        .matches(v -> v.getTemplateVersion() == null, "standalone venue should not have templateVersion")
        .matches(v -> !v.getIsEnforced(), "standalone venue should not be enforced");

    // Verify initial state for NetworkVenue instances
    var initialNetworkVenueInstance1 = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(
        ecTenantId, networkInstance.getId(), venueInstance1Id);
    var initialNetworkVenueInstance2 = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(
        ecTenantId, networkInstance.getId(), venueInstance2Id);
    var initialStandaloneNetworkVenueInstance = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(
        ecTenantId, networkInstance.getId(), standaloneVenueInstanceId);

    assertThat(initialNetworkVenueInstance1).isPresent().get()
        .matches(nv -> nv.getTemplateId() != null, "NetworkVenue instance 1 should have templateId")
        .matches(nv -> nv.getTemplateVersion() != null, "NetworkVenue instance 1 should have templateVersion")
        .matches(nv -> nv.getIsEnforced(), "NetworkVenue instance 1 should be enforced");

    assertThat(initialNetworkVenueInstance2).isPresent().get()
        .matches(nv -> nv.getTemplateId() != null, "NetworkVenue instance 2 should have templateId")
        .matches(nv -> nv.getTemplateVersion() != null, "NetworkVenue instance 2 should have templateVersion")
        .matches(nv -> nv.getIsEnforced(), "NetworkVenue instance 2 should be enforced");

    assertThat(initialStandaloneNetworkVenueInstance).isPresent().get()
        .matches(nv -> nv.getTemplateId() == null, "standalone NetworkVenue should not have templateId")
        .matches(nv -> nv.getTemplateVersion() == null, "standalone NetworkVenue should not have templateVersion")
        .matches(nv -> !nv.getIsEnforced(), "standalone NetworkVenue should not be enforced");

    // Send decouple event for EC tenant
    var event = buildVenueTemplateInstanceDecoupleEvent(ecTenantId);
    var requestId = randomTxId() + "$DecoupleVenueTemplateInstances";

    messageUtil.sendVenueCfgChange(ecTenantId, requestId, event);

    // Verify activity message indicates success
    validateActivityMessages(ecTenantId, requestId, "DecoupleVenueInstancesInWifi", Status.OK);

    // Verify final state for venue instances after decouple
    var finalVenueInstance1 = repositoryUtil.find(Venue.class, venueInstance1Id, ecTenantId);
    var finalVenueInstance2 = repositoryUtil.find(Venue.class, venueInstance2Id, ecTenantId);
    var finalStandaloneVenueInstance = repositoryUtil.find(Venue.class, standaloneVenueInstanceId, ecTenantId);

    // Venue template instances should be decoupled
    assertThat(finalVenueInstance1).isNotNull()
        .matches(v -> v.getTemplateId() == null, "venue instance 1 templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "venue instance 1 templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "venue instance 1 isEnforced should be false after decouple");

    assertThat(finalVenueInstance2).isNotNull()
        .matches(v -> v.getTemplateId() == null, "venue instance 2 templateId should be null after decouple")
        .matches(v -> v.getTemplateVersion() == null, "venue instance 2 templateVersion should be null after decouple")
        .matches(v -> !v.getIsEnforced(), "venue instance 2 isEnforced should be false after decouple");

    // Standalone venue instance should remain unchanged
    assertThat(finalStandaloneVenueInstance).isNotNull()
        .matches(v -> v.getTemplateId() == null, "standalone venue should still have null templateId")
        .matches(v -> v.getTemplateVersion() == null, "standalone venue should still have null templateVersion")
        .matches(v -> !v.getIsEnforced(), "standalone venue should still not be enforced");

    // Verify final state for NetworkVenue instances after decouple
    var finalNetworkVenueInstance1 = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(
        ecTenantId, networkInstance.getId(), venueInstance1Id);
    var finalNetworkVenueInstance2 = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(
        ecTenantId, networkInstance.getId(), venueInstance2Id);
    var finalStandaloneNetworkVenueInstance = networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(
        ecTenantId, networkInstance.getId(), standaloneVenueInstanceId);

    // NetworkVenue template instances should be decoupled
    assertThat(finalNetworkVenueInstance1).isPresent().get()
        .matches(nv -> nv.getTemplateId() == null, "NetworkVenue instance 1 templateId should be null after decouple")
        .matches(nv -> nv.getTemplateVersion() == null, "NetworkVenue instance 1 templateVersion should be null after decouple")
        .matches(nv -> !nv.getIsEnforced(), "NetworkVenue instance 1 isEnforced should be false after decouple");

    assertThat(finalNetworkVenueInstance2).isPresent().get()
        .matches(nv -> nv.getTemplateId() == null, "NetworkVenue instance 2 templateId should be null after decouple")
        .matches(nv -> nv.getTemplateVersion() == null, "NetworkVenue instance 2 templateVersion should be null after decouple")
        .matches(nv -> !nv.getIsEnforced(), "NetworkVenue instance 2 isEnforced should be false after decouple");

    // Standalone NetworkVenue instance should remain unchanged
    assertThat(finalStandaloneNetworkVenueInstance).isPresent().get()
        .matches(nv -> nv.getTemplateId() == null, "standalone NetworkVenue should still have null templateId")
        .matches(nv -> nv.getTemplateVersion() == null, "standalone NetworkVenue should still have null templateVersion")
        .matches(nv -> !nv.getIsEnforced(), "standalone NetworkVenue should still not be enforced");

    // No DDCCM messages should be sent for decouple operation
    messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(ecTenantId);
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(ecTenantId);
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(ecTenantId);
  }
}

