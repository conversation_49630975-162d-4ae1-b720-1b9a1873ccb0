package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.config.RadiusServerSettingConfig;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@WifiUnitTest
public class DdccmAuthRadiusVenueOperationBuilderTest {

  @Autowired
  private DdccmAuthRadiusVenueOperationBuilder builder;

  @Autowired
  private RadiusServerSettingConfig radiusServerSettingConfig;

  @Test
  void testCreateAuthRadiusVenue() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    Operation operation = Operation.newBuilder().setAction(Action.ADD).build();
    when(radiusServerSettingConfig.getHost()).thenReturn("127.0.0.1");
    when(radiusServerSettingConfig.getAuthenticationPort()).thenReturn(1812);
    when(radiusServerSettingConfig.getSharedSecret()).thenReturn("secret");
    Operation result = builder.createAuthRadiusVenue(operation, networkVenue);

    assertThat(result)
        .hasFieldOrPropertyWithValue("action", Action.ADD)
        .hasFieldOrPropertyWithValue("id", networkVenue.getId());
    assertThat(result.getVenueRadius())
        .hasFieldOrPropertyWithValue("id", networkVenue.getId())
        .hasFieldOrPropertyWithValue("name", networkVenue.getId());
    assertThat(result.getVenueRadius().getServerCount()).isEqualTo(1);
    assertThat(result.getVenueRadius().getServer(0))
        .hasFieldOrPropertyWithValue("ip", "127.0.0.1")
        .hasFieldOrPropertyWithValue("port", 1812)
        .hasFieldOrPropertyWithValue("sharedSecret", "secret");
  }

  @Test
  void testCreateAuthRadiusVenueWithoutSharedSecret() {
    var networkVenue = createAaaNetworkVenue();
    var operation = Operation.newBuilder().setAction(Action.ADD).build();
    when(radiusServerSettingConfig.getHost()).thenReturn("127.0.0.1");
    when(radiusServerSettingConfig.getAuthenticationPort()).thenReturn(1812);
    Operation result = builder.createAuthRadiusVenue(operation, networkVenue);

    assertThat(result)
        .hasFieldOrPropertyWithValue("action", Action.ADD)
        .hasFieldOrPropertyWithValue("id", networkVenue.getId());
    assertThat(result.getVenueRadius())
        .hasFieldOrPropertyWithValue("id", networkVenue.getId())
        .hasFieldOrPropertyWithValue("name", networkVenue.getId());
    assertThat(result.getVenueRadius().getServerCount()).isEqualTo(1);
    assertThat(result.getVenueRadius().getServer(0))
        .hasFieldOrPropertyWithValue("ip", "127.0.0.1")
        .hasFieldOrPropertyWithValue("port", 1812);
  }

  private static NetworkVenue createAaaNetworkVenue() {
    return setupNetworkVenue(new AAANetwork(UUID.randomUUID().toString()));
  }

  private static NetworkVenue setupNetworkVenue(Network network) {
    var tenant = new Tenant(UUID.randomUUID().toString());
    var venue = new Venue(UUID.randomUUID().toString());
    venue.setTenant(tenant);
    var networkVenue = new NetworkVenue(UUID.randomUUID().toString());
    networkVenue.setNetwork(network);
    networkVenue.setVenue(venue);
    networkVenue.setTenant(tenant);
    networkVenue.setApWlanId(1);

    var wlan = new Wlan();
    network.setName("fake-network");
    network.setWlan(wlan);
    wlan.setTenant(tenant);
    wlan.setAdvancedCustomization(new WlanAdvancedCustomization());
    wlan.setNetwork(network);
    wlan.setSsid("fake-ssid");
    wlan.setWlanSecurity(WlanSecurityEnum.WPA2Personal);
    return networkVenue;
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public DdccmAuthRadiusVenueOperationBuilder ddccmAuthRadiusVenueOperationBuilder() {
      DdccmAuthRadiusVenueOperationBuilder builder = Mockito.spy(DdccmAuthRadiusVenueOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }

    @Bean
    public RadiusServerSettingConfig mockRadiusServerSettingConfig() {
      return Mockito.mock(RadiusServerSettingConfig.class);
    }
  }
}