package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.device.firmware.operator.gpb.DeviceFirmwareOperatorProtobuf.NewFirmwareNotice;
import com.ruckus.cloud.device.firmware.operator.gpb.DeviceFirmwareOperatorProtobuf.NewFirmwareNotice.ReleaseType;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.DefaultApBranchFamily;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApVersionLabelEnum;
import com.ruckus.cloud.wifi.test.TestConstants;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeFirmwareOnboardRequestTest {
  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  private final String WIFI_ONBOARD_VERSION = "6.2.1.103.1580";

  private final String WIFI_ABF_ONBOARD_VERSION = "6.2.0.103.433";

  private final String WIFI_FIRST_TIME_ONBOARD_VERSION = "7.9.9.8.1";

  private final String SWITCH_ONBOARD_VERSION = "09010f_b406";

  private final String DEFAULT_VERSION = "DEFAULT";

  List<String> SUPPORTED_AP_MODELS = List.of("R700", "R500", "R650", "R550");

  @Test
  public void firmwareOnboard() {
    // Given
    NewFirmwareNotice newFirmwareNotice =
        NewFirmwareNotice.newBuilder()
            .setType(ReleaseType.RECOMMENDED)
            .setVersion(WIFI_ONBOARD_VERSION)
            .setReleaseNote("ReleaseNote")
            .setBaseUrl("BaseUrl")
            .addAllSupportedModels(SUPPORTED_AP_MODELS)
            .build();
    String deviceType = "wifi";

    // When
    messageUtil.sendFirmwareOnboard(
        kafkaTopicProvider.getFirmwareOnboardTopic(), randomId(), newFirmwareNotice, deviceType);

    // Then
    ApVersion apVersion = repositoryUtil.find(ApVersion.class, WIFI_ONBOARD_VERSION);
    assertThat(apVersion).isNotNull().matches(av -> av.getId().equals(WIFI_ONBOARD_VERSION));
    assertThat(apVersion.getSupportedApModels()).containsSequence(SUPPORTED_AP_MODELS.stream().sorted().toList());
    assertThat(apVersion.getLabels()).hasSize(1).contains(ApVersionLabelEnum.GA);
  }

  @Test
  public void firmwareOnboardEarlyAccessOnAndIgnoreDefaultLabelIsFalse() {
    // Property of "default.fw-onboard.ignore-default-label" is false
    // Given
    NewFirmwareNotice newFirmwareNotice =
            NewFirmwareNotice.newBuilder()
                    .setType(ReleaseType.RECOMMENDED)
                    .setVersion(WIFI_ONBOARD_VERSION)
                    .setReleaseNote("ReleaseNote")
                    .setBaseUrl("BaseUrl")
                    .addAllSupportedModels(SUPPORTED_AP_MODELS)
                    .build();
    String deviceType = "wifi";

    // When
    messageUtil.sendFirmwareOnboard(
            kafkaTopicProvider.getFirmwareOnboardTopic(), randomId(), newFirmwareNotice, deviceType);

    // Then
    ApVersion apVersion = repositoryUtil.find(ApVersion.class, WIFI_ONBOARD_VERSION);
    assertThat(apVersion).isNotNull().matches(av -> av.getId().equals(WIFI_ONBOARD_VERSION));
    assertThat(apVersion.getSupportedApModels()).containsSequence(SUPPORTED_AP_MODELS.stream().sorted().toList());
    assertThat(apVersion.getLabels()).hasSize(1).contains(ApVersionLabelEnum.GA);
  }

  @Test
  public void firmwareOnboardWhenReceiveNonWifiType() {
    // Given
    NewFirmwareNotice newFirmwareNotice =
        NewFirmwareNotice.newBuilder()
            .setType(ReleaseType.RECOMMENDED)
            .setVersion(SWITCH_ONBOARD_VERSION)
            .setReleaseNote("ReleaseNote")
            .setBaseUrl("BaseUrl")
            .build();
    String deviceType = "switch";

    // When
    messageUtil.sendFirmwareOnboard(
        kafkaTopicProvider.getFirmwareOnboardTopic(), randomId(), newFirmwareNotice, deviceType);

    // Then
    ApVersion apVersion = repositoryUtil.find(ApVersion.class, SWITCH_ONBOARD_VERSION);
    assertThat(apVersion).isNull();
  }

  @Test
  public void firmwareOnboardWithDefaultVersion(Tenant tenant) {
    // Given
    ApVersion apVersion1 = ApVersionTestFixture.recommendedApVersion("6.2.0.103.543", a -> {});
    apVersion1.setTarget(DEFAULT_VERSION);
    ApVersion apVersion2 = ApVersionTestFixture.recommendedApVersion("6.2.1.103.2573", a -> {});
    repositoryUtil.createOrUpdate(apVersion1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(apVersion2, tenant.getId(), randomTxId());

    DefaultApBranchFamily dabf1 = repositoryUtil.find(DefaultApBranchFamily.class, TestConstants.ABF1);
    dabf1.setActive(true);
    repositoryUtil.createOrUpdate(dabf1, randomTxId(), randomTxId());
    DefaultApBranchFamily dabf2 = repositoryUtil.find(DefaultApBranchFamily.class, TestConstants.ABF2);
    dabf1.setActive(false);
    repositoryUtil.createOrUpdate(dabf2, randomTxId(), randomTxId());

    NewFirmwareNotice newFirmwareNoticeDefault =
        NewFirmwareNotice.newBuilder()
            .setType(ReleaseType.RECOMMENDED)
            .setVersion(WIFI_ABF_ONBOARD_VERSION)
            .setReleaseNote("ReleaseNote")
            .setBaseUrl("BaseUrl")
            .setTarget(DEFAULT_VERSION)
            .addAllSupportedModels(SUPPORTED_AP_MODELS)
            .build();
    String deviceType = "wifi";

    // When
    messageUtil.sendFirmwareOnboard(
        kafkaTopicProvider.getFirmwareOnboardTopic(),
        randomId(),
        newFirmwareNoticeDefault,
        deviceType);

    // Then
    ApVersion apVersion = repositoryUtil.find(ApVersion.class, WIFI_ABF_ONBOARD_VERSION);
    assertThat(apVersion).isNotNull().matches(av -> av.getId().equals(WIFI_ABF_ONBOARD_VERSION));
    assertThat(apVersion.getLabels()).hasSize(1).contains(ApVersionLabelEnum.GA);
    DefaultApBranchFamily defaultApBranchFamily =
        repositoryUtil.find(DefaultApBranchFamily.class, TestConstants.ABF1);
    assertThat(defaultApBranchFamily)
        .isNotNull()
        .matches(d -> d.getBranchVersion().getId().equals(WIFI_ABF_ONBOARD_VERSION));
  }

  @Test
  public void firmwareOnboardWithNewBranchVersion(Tenant tenant) {
    // Given
    String abf = "ABFTest-1";

    NewFirmwareNotice newFirmwareNoticeDefault =
        NewFirmwareNotice.newBuilder()
            .setType(ReleaseType.RECOMMENDED)
            .setVersion(WIFI_FIRST_TIME_ONBOARD_VERSION)
            .setReleaseNote("ReleaseNote")
            .setBaseUrl("BaseUrl")
            .addAllSupportedModels(SUPPORTED_AP_MODELS)
            .build();
    String deviceType = "wifi";

    // When
    messageUtil.sendFirmwareOnboard(
        kafkaTopicProvider.getFirmwareOnboardTopic(),
        randomId(),
        newFirmwareNoticeDefault,
        deviceType);

    // Then
    ApVersion apVersion = repositoryUtil.find(ApVersion.class, WIFI_FIRST_TIME_ONBOARD_VERSION);
    assertThat(apVersion)
        .isNotNull()
        .matches(av -> av.getId().equals(WIFI_FIRST_TIME_ONBOARD_VERSION));
    assertThat(apVersion.getLabels()).hasSize(1).contains(ApVersionLabelEnum.GA);
    DefaultApBranchFamily defaultApBranchFamily =
        repositoryUtil.find(DefaultApBranchFamily.class, abf);
    assertThat(defaultApBranchFamily)
        .isNotNull()
        .matches(d -> d.getBranchVersion().getId().equals(WIFI_FIRST_TIME_ONBOARD_VERSION));
  }
}
