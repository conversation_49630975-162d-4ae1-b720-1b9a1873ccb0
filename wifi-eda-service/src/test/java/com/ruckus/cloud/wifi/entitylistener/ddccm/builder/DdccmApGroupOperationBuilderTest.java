package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.wifi.ApGroupRadio60;
import com.ruckus.acx.ddccm.protobuf.wifi.AutoChannelSelectionMethod;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroupApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroupClientAdmissionControl;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroupLoadBalancing;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroupRadioCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroupRadioParams24G;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroupRadioParams5G;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroupRadioParams6G;
import com.ruckus.cloud.wifi.eda.servicemodel.Capabilities;
import com.ruckus.cloud.wifi.eda.servicemodel.CapabilitiesApModel;
import com.ruckus.cloud.wifi.eda.servicemodel.ExternalAntenna;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AntennaTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BandModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BssMinRate6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel24Enum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth24GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MgmtTxRate6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ScanMethodEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TxPower6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TxPowerEnum;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.FirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.exception.CapabilityNotFoundException;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApGroupApModelSpecificAttributesTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueApModelSpecificAttributesTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
public class DdccmApGroupOperationBuilderTest {

  @Autowired
  private DdccmApGroupOperationBuilder builder;

  @MockBean
  private FirmwareCapabilityService firmwareCapabilityService;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE)
  public void testBasicRadio24(Tenant tenant) {
    // Given
    ApGroupRadioParams24G params24G = new ApGroupRadioParams24G();
    params24G.setUseVenueSettings(false);
    params24G.setAllowedChannels(List.of(Channel24Enum._1, Channel24Enum._11));
    params24G.setChannelBandwidth(ChannelBandwidth24GEnum.AUTO);
    params24G.setTxPower(TxPowerEnum.MAX);
    params24G.setMethod(ScanMethodEnum.CHANNELFLY);
    params24G.setChangeInterval((short)33);
    params24G.setScanInterval(20);

    ApGroupRadioCustomization customization = new ApGroupRadioCustomization();
    customization.setRadioParams24G(params24G);

    ApGroup apGroup = new ApGroup();
    apGroup.setId("apGroupId");
    apGroup.setName("TestGroup");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(VenueTestFixture.randomVenue(tenant));
    apGroup.setRadioCustomization(customization);

    TxChanges changes = mock(TxChanges.class);
    Operation.Builder operationBuilder = Operation.newBuilder();

    // Act
    builder.config(operationBuilder, apGroup, EntityAction.MODIFY, changes);

    // Assert
    var operation = operationBuilder.build();
    assertNotNull(operation.getApGroup());
    assertEquals("apGroupId", operation.getApGroup().getId());
    assertFalse(operation.getApGroup().getIsDefault().getValue());
    assertEquals("TestGroup", operation.getApGroup().getName());
    assertEquals(2, operation.getApGroup().getRadioCustomization().getRadio24().getAllowedChannelListCount());
    assertFalse(operation.getApGroup().getRadioCustomization().getRadio24().getAutoCellSizing());
    assertEquals("MAX", operation.getApGroup().getRadioCustomization().getRadio24().getTxPowerOption().name());
    assertEquals(0, operation.getApGroup().getRadioCustomization().getRadio24().getChannel());

    // Given
    apGroup.getRadioCustomization().getRadioParams24G().setMethod(ScanMethodEnum.MANUAL);
    apGroup.getRadioCustomization().getRadioParams24G().setChannel(7);

    // Act
    builder.config(operationBuilder, apGroup, EntityAction.MODIFY, changes);

    // Assert
    var operationManual = operationBuilder.build();
    assertTrue(operationManual.getApGroup().getRadioCustomization().getRadio24().getChannelSelectionMethod().equals(AutoChannelSelectionMethod.Manual));
    assertEquals(7, operationManual.getApGroup().getRadioCustomization().getRadio24().getChannel());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE)
  public void testBasicRadio50(Tenant tenant) {
    // Given
    var params5G = new ApGroupRadioParams5G();
    params5G.setUseVenueSettings(false);
    params5G.setAllowedIndoorChannels(List.of(com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel50Enum._36));
    params5G.setChannelBandwidth(com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth5GEnum.AUTO);
    params5G.setTxPower(TxPowerEnum.MIN);
    params5G.setMethod(ScanMethodEnum.BACKGROUND_SCANNING);
    params5G.setChangeInterval((short)44);
    params5G.setScanInterval(30);
    params5G.setCombineChannels(true);

    var customization = new ApGroupRadioCustomization();
    customization.setRadioParams5G(params5G);

    var apGroup = new ApGroup();
    apGroup.setId("apGroupId5G");
    apGroup.setName("TestGroup5G");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(VenueTestFixture.randomVenue(tenant));
    apGroup.setRadioCustomization(customization);

    TxChanges changes = mock(TxChanges.class);
    Operation.Builder operationBuilder = Operation.newBuilder();

    // Act
    builder.config(operationBuilder, apGroup, EntityAction.MODIFY, changes);

    // Assert
    var operation = operationBuilder.build();
    var radio50 = operation.getApGroup().getRadioCustomization().getRadio50();
    assertNotNull(radio50);
    assertEquals(1, radio50.getAllowedIndoorChannelListCount());
    assertEquals("MIN", radio50.getTxPowerOption().name());
    assertEquals("Auto", radio50.getChannelBandWidth());
    assertEquals(true, radio50.getIndoorChannelEnabledForOutdoor());
    assertEquals(30, radio50.getBgScanTimer().getValue());
    assertEquals(0, radio50.getOutdoorChannel());
    assertEquals(0, radio50.getIndoorChannel());

    // Given
    apGroup.getRadioCustomization().getRadioParams5G().setMethod(ScanMethodEnum.MANUAL);
    apGroup.getRadioCustomization().getRadioParams5G().setOutdoorChannel(157);
    apGroup.getRadioCustomization().getRadioParams5G().setIndoorChannel(100);

    // Act
    builder.config(operationBuilder, apGroup, EntityAction.MODIFY, changes);

    // Assert
    var operationManual = operationBuilder.build();
    assertTrue(operationManual.getApGroup().getRadioCustomization().getRadio50().getChannelSelectionMethod().equals(AutoChannelSelectionMethod.Manual));
    assertEquals(157, operationManual.getApGroup().getRadioCustomization().getRadio50().getOutdoorChannel());
    assertEquals(100, operationManual.getApGroup().getRadioCustomization().getRadio50().getIndoorChannel());

  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE)
  public void testBasicRadioDual50(Tenant tenant) {
    // Given
    var paramsLower5G = new com.ruckus.cloud.wifi.eda.servicemodel.ApGroupRadioParamsNarrow5G();
    paramsLower5G.setUseVenueSettings(false);
    paramsLower5G.setAllowedIndoorChannels(List.of(com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel50Enum._36));
    paramsLower5G.setChannelBandwidth(com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth5GEnum.AUTO);
    paramsLower5G.setTxPower(TxPowerEnum.MAX);
    paramsLower5G.setMethod(ScanMethodEnum.CHANNELFLY);
    paramsLower5G.setChangeInterval((short)55);
    paramsLower5G.setScanInterval(40);

    var paramsUpper5G = new com.ruckus.cloud.wifi.eda.servicemodel.ApGroupRadioParamsNarrow5G();
    paramsUpper5G.setUseVenueSettings(false);
    paramsUpper5G.setAllowedIndoorChannels(List.of(com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel50Enum._100));
    paramsUpper5G.setChannelBandwidth(com.ruckus.cloud.wifi.eda.servicemodel.enums.ChannelBandwidth5GEnum.AUTO);
    paramsUpper5G.setTxPower(TxPowerEnum.MIN);
    paramsUpper5G.setMethod(ScanMethodEnum.BACKGROUND_SCANNING);
    paramsUpper5G.setChangeInterval((short)66);
    paramsUpper5G.setScanInterval(50);

    var dual5G = new com.ruckus.cloud.wifi.eda.servicemodel.ApGroupRadioParamsDual5G();
    dual5G.setEnabled(true);
    dual5G.setRadioParamsLower5G(paramsLower5G);
    dual5G.setRadioParamsUpper5G(paramsUpper5G);

    var customization = new ApGroupRadioCustomization();
    customization.setRadioParamsDual5G(dual5G);

    var apGroup = new ApGroup();
    apGroup.setId("apGroupIdDual5G");
    apGroup.setName("TestGroupDual5G");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(VenueTestFixture.randomVenue(tenant));
    apGroup.setRadioCustomization(customization);

    TxChanges changes = mock(TxChanges.class);
    Operation.Builder operationBuilder = Operation.newBuilder();

    // Act
    builder.config(operationBuilder, apGroup, EntityAction.MODIFY, changes);

    // Assert
    var operation = operationBuilder.build();
    var radioCustomization = operation.getApGroup().getRadioCustomization();
    assertNotNull(radioCustomization.getRadio50Lower());
    assertNotNull(radioCustomization.getRadio50Upper());
    assertEquals(1, radioCustomization.getRadio50Lower().getAllowedIndoorChannelListCount());
    assertEquals("MAX", radioCustomization.getRadio50Lower().getTxPowerOption().name());
    assertEquals(40, radioCustomization.getRadio50Lower().getBgScanTimer().getValue());
    assertEquals(1, radioCustomization.getRadio50Upper().getAllowedIndoorChannelListCount());
    assertEquals("MIN", radioCustomization.getRadio50Upper().getTxPowerOption().name());
    assertEquals(50, radioCustomization.getRadio50Upper().getBgScanTimer().getValue());
    assertEquals(true, radioCustomization.getDual5GEnabled());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE)
  public void testBasicRadio60(Tenant tenant) {
    // Arrange
    ApGroupRadioParams6G radioParams6G = new ApGroupRadioParams6G();
    radioParams6G.setUseVenueSettings(false);
    radioParams6G.setChannelBandwidth(ChannelBandwidth6GEnum.AUTO);
    radioParams6G.setTxPower(TxPower6GEnum.MAX);
    radioParams6G.setMethod(ScanMethodEnum.BACKGROUND_SCANNING);
    radioParams6G.setAllowedIndoorChannels(List.of(Channel6GEnum._1, Channel6GEnum._5));
    radioParams6G.setAllowedOutdoorChannels(List.of(Channel6GEnum._161, Channel6GEnum._181, Channel6GEnum._193));
    radioParams6G.setScanInterval(30);
    radioParams6G.setChangeInterval((short)60);
    radioParams6G.setBssMinRate6G(BssMinRate6GEnum.HE_MCS_0);
    radioParams6G.setMgmtTxRate6G(MgmtTxRate6GEnum._6);
    radioParams6G.setEnableMulticastUplinkRateLimiting(true);
    radioParams6G.setMulticastUplinkRateLimiting(100);
    radioParams6G.setEnableMulticastDownlinkRateLimiting(true);
    radioParams6G.setMulticastDownlinkRateLimiting(200);
    ApGroupRadioCustomization customization = new ApGroupRadioCustomization();
    customization.setRadioParams6G(radioParams6G);
    ApGroup apGroup = new ApGroup();
    apGroup.setId("apGroupId");
    apGroup.setName("TestGroup");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(VenueTestFixture.randomVenue(tenant));
    apGroup.setRadioCustomization(customization);

    TxChanges changes = mock(TxChanges.class);
    Operation.Builder operationBuilder = Operation.newBuilder();

    // Act
    builder.config(operationBuilder, apGroup, EntityAction.MODIFY, changes);

    // Assert
    var operation = operationBuilder.build();
    ApGroupRadio60 radio60 = operation.getApGroup().getRadioCustomization().getRadio60();
    assertNotNull(radio60);
    assertEquals("Auto", radio60.getChannelBandWidth());
    assertEquals("max", radio60.getTxPower());
    assertEquals(2, radio60.getAllowedIndoorChannelListCount());
    assertEquals(3, radio60.getAllowedOutdoorChannelListCount());
    assertEquals(BssMinRate6GEnum.HE_MCS_0.name(), radio60.getBssMinRate6G());
    assertEquals(MgmtTxRate6GEnum._6.toString(), radio60.getMgmtTxRate6G());
    assertEquals(100, radio60.getMulticastRateLimitUplinkRl().getValue());
    assertEquals(200, radio60.getMulticastRateLimitDownlinkRl().getValue());
    assertEquals(0, radio60.getOutdoorChannel());
    assertEquals(0, radio60.getIndoorChannel());

    // Given
    apGroup.getRadioCustomization().getRadioParams6G().setMethod(ScanMethodEnum.MANUAL);
    apGroup.getRadioCustomization().getRadioParams6G().setOutdoorChannel(157);
    apGroup.getRadioCustomization().getRadioParams6G().setIndoorChannel(113);

    // Act
    builder.config(operationBuilder, apGroup, EntityAction.MODIFY, changes);

    // Assert
    var operationManual = operationBuilder.build();
    assertTrue(operationManual.getApGroup().getRadioCustomization().getRadio60().getChannelSelectionMethod().equals(AutoChannelSelectionMethod.Manual));
    assertEquals(157, operationManual.getApGroup().getRadioCustomization().getRadio60().getOutdoorChannel());
    assertEquals(113, operationManual.getApGroup().getRadioCustomization().getRadio60().getIndoorChannel());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE)
  public void testApGroupBandMode(Tenant tenant) throws CapabilityNotFoundException {
    Capabilities capabilities = new Capabilities();
    CapabilitiesApModel T670SN = new CapabilitiesApModel();
    T670SN.setModel("T670SN");
    T670SN.setBandCombinationCapabilities(List.of(BandModeEnum.DUAL, BandModeEnum.TRIPLE));

    capabilities.setApModels(List.of(T670SN));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());

    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant);

    ApGroup apGroup = new ApGroup();
    apGroup.setId("apGroupId");
    apGroup.setName("TestGroup");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(venue);
    apGroup.setModelSpecificAttributes(mockT670SNApGroupBandMode(apGroup.getVenue(), apGroup));

    venue.setModelSpecificAttributes(mockT670SNVenueBandMode(venue));

    // When
    List<Operation> operations =
            builder.build(new NewTxEntity<>(apGroup), emptyTxChanges());
    // Then
    verify(firmwareCapabilityService, times(1)).getVenueCapabilities(any());
    com.ruckus.acx.ddccm.protobuf.wifi.ApGroup apGroupToVerify = operations.get(0).getApGroup();
    assertEquals(1, apGroupToVerify.getApGroupApModelsCount());

    var modelToVerify = apGroupToVerify.getApGroupApModels(0);
    assertEquals("T670SN", modelToVerify.getModelName());
    assertEquals(apGroup.getId(), modelToVerify.getApGroupId());
    assertEquals(StringValue.of("2-5-6"), modelToVerify.getBandCombinationMode());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE)
  public void testApGroupBandModeWithConfigOnVenueOnly(Tenant tenant)
      throws CapabilityNotFoundException {
    Capabilities capabilities = new Capabilities();
    CapabilitiesApModel T670SN = new CapabilitiesApModel();
    T670SN.setModel("T670SN");
    T670SN.setBandCombinationCapabilities(List.of(BandModeEnum.DUAL, BandModeEnum.TRIPLE));

    capabilities.setApModels(List.of(T670SN));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());

    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant);

    ApGroup apGroup = new ApGroup();
    apGroup.setId("apGroupId");
    apGroup.setName("TestGroup");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(venue);

    venue.setModelSpecificAttributes(mockT670SNVenueBandMode(venue));

    // When
    List<Operation> operations = builder.build(new NewTxEntity<>(apGroup), emptyTxChanges());
    // Then
    verify(firmwareCapabilityService, times(1)).getVenueCapabilities(any());
    com.ruckus.acx.ddccm.protobuf.wifi.ApGroup apGroupToVerify = operations.get(0).getApGroup();
    assertEquals(1, apGroupToVerify.getApGroupApModelsCount());

    var modelToVerify = apGroupToVerify.getApGroupApModels(0);
    assertEquals("T670SN", modelToVerify.getModelName());
    assertEquals(apGroup.getId(), modelToVerify.getApGroupId());
    assertEquals(StringValue.of("2-5"), modelToVerify.getBandCombinationMode());
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE,
      FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE3_TOGGLE,
      FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE
  })
  public void testApGroupAntennaType(Tenant tenant) throws CapabilityNotFoundException {
    Capabilities capabilities = new Capabilities();
    CapabilitiesApModel T670SN = new CapabilitiesApModel();
    T670SN.setModel("T670SN");
    T670SN.setBandCombinationCapabilities(List.of(BandModeEnum.DUAL, BandModeEnum.TRIPLE));

    capabilities.setApModels(List.of(T670SN));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());

    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant);
    ApGroup apGroup = ApGroupTestFixture.randomApGroup(venue);;
    apGroup.setVenue(venue);
    ApGroupApModelSpecificAttributes apGroupModelSpecificAttrs =
    ApGroupApModelSpecificAttributesTestFixture.randomApGroupApModelSpecificAttributes(apGroup);
    apGroupModelSpecificAttrs.setAntennaType(AntennaTypeEnum.SECTOR);
    apGroupModelSpecificAttrs.setModel("T670SN");
    apGroup.setModelSpecificAttributes(List.of(apGroupModelSpecificAttrs));

    VenueApModelSpecificAttributes venueModelSpecificAttrs =
        VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(venue);
    venueModelSpecificAttrs.setAntennaType(AntennaTypeEnum.NARROW);
    venueModelSpecificAttrs.setModel("T670SN");
    venue.setModelSpecificAttributes(List.of(venueModelSpecificAttrs));

    // When
    List<Operation> operations =
        builder.build(new NewTxEntity<>(apGroup), emptyTxChanges());
    // Then
    verify(firmwareCapabilityService, times(1)).getVenueCapabilities(any());
    com.ruckus.acx.ddccm.protobuf.wifi.ApGroup apGroupToVerify = operations.get(0).getApGroup();
    assertEquals(1, apGroupToVerify.getApGroupApModelsCount());

    var modelToVerify = apGroupToVerify.getApGroupApModels(0);
    assertEquals("T670SN", modelToVerify.getModelName());
    assertEquals(apGroup.getId(), modelToVerify.getApGroupId());
    assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.AntennaTypeEnum.Sector, modelToVerify.getAntennaType());
  }

  @Test
  @FeatureFlag(enable = {
      FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE,
      FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE3_TOGGLE
  })
  public void testApGroupExternalAntenna(Tenant tenant) throws CapabilityNotFoundException {
    Capabilities capabilities = new Capabilities();
    CapabilitiesApModel T670SN = new CapabilitiesApModel();
    T670SN.setModel("T670SN");
    T670SN.setBandCombinationCapabilities(List.of(BandModeEnum.DUAL, BandModeEnum.TRIPLE));

    capabilities.setApModels(List.of(T670SN));
    doReturn(capabilities).when(firmwareCapabilityService).getVenueCapabilities(any());

    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant);
    ApGroup apGroup = ApGroupTestFixture.randomApGroup(venue);;
    apGroup.setVenue(venue);
    ApGroupApModelSpecificAttributes apGroupModelSpecificAttrs =
        ApGroupApModelSpecificAttributesTestFixture.randomApGroupApModelSpecificAttributes(apGroup);
    apGroupModelSpecificAttrs.setModel("T670SN");
    apGroupModelSpecificAttrs.setExternalAntenna(newExternalAntenna(10, 20, true, false));
    apGroup.setModelSpecificAttributes(List.of(apGroupModelSpecificAttrs));

    VenueApModelSpecificAttributes venueModelSpecificAttrs =
        VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(venue);
    venueModelSpecificAttrs.setModel("T670SN");
    venueModelSpecificAttrs.setExternalAntenna(newExternalAntenna(15, 25, false, true));
    venue.setModelSpecificAttributes(List.of(venueModelSpecificAttrs));

    // When
    List<Operation> operations =
        builder.build(new NewTxEntity<>(apGroup), emptyTxChanges());
    // Then
    verify(firmwareCapabilityService, times(1)).getVenueCapabilities(any());
    com.ruckus.acx.ddccm.protobuf.wifi.ApGroup apGroupToVerify = operations.get(0).getApGroup();
    assertEquals(1, apGroupToVerify.getApGroupApModelsCount());

    var modelToVerify = apGroupToVerify.getApGroupApModels(0);
    assertEquals("T670SN", modelToVerify.getModelName());
    assertEquals(apGroup.getId(), modelToVerify.getApGroupId());
    assertEquals(Int32Value.of(10), modelToVerify.getExternalAntenna24().getDbi());
    assertEquals(BoolValue.of(true), modelToVerify.getExternalAntenna24().getEnabled());
    assertEquals(BoolValue.of(false), modelToVerify.getExternalAntenna50().getEnabled());
  }

  private List<ApGroupApModelSpecificAttributes> mockT670SNApGroupBandMode(
      Venue venue, ApGroup apGroup) {
    ApGroupApModelSpecificAttributes attributes = new ApGroupApModelSpecificAttributes();
    attributes.setTenant(venue.getTenant());
    attributes.setVenue(venue);
    attributes.setApGroup(apGroup);
    attributes.setModel("T670SN");
    attributes.setBandMode(BandModeEnum.TRIPLE);
    return List.of(attributes);
  }

  private List<VenueApModelSpecificAttributes> mockT670SNVenueBandMode(Venue venue) {
    VenueApModelSpecificAttributes attributes = new VenueApModelSpecificAttributes();
    attributes.setId(randomId());
    attributes.setTenant(venue.getTenant());
    attributes.setVenue(venue);
    attributes.setModel("T670SN");
    attributes.setBandMode(BandModeEnum.TRIPLE);
    return List.of(attributes);
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE)
  public void testApGroupLoadBalancing(Tenant tenant) {
    // Given
    ApGroupLoadBalancing apGroupLoadBalancing = new ApGroupLoadBalancing();
    apGroupLoadBalancing.setEnabled(false);

    ApGroup apGroup = new ApGroup();
    apGroup.setId("apGroupId");
    apGroup.setName("TestGroup");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(VenueTestFixture.randomVenue(tenant));
    apGroup.setLoadBalancing(apGroupLoadBalancing);

    // When
    List<Operation> operations =
            builder.build(new NewTxEntity<>(apGroup), emptyTxChanges());

    // Then
    com.ruckus.acx.ddccm.protobuf.wifi.ApGroup apGroupToVerify = operations.get(0).getApGroup();
    assertFalse(apGroupToVerify.getLoadBalancing().getEnabled());
  }

  @Test
  @FeatureFlag(
      enable = {
        FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE,
        FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE3_TOGGLE
      })
  public void testApGroupClientAdmissionControl(Tenant tenant) {
    // Given
    ApGroupClientAdmissionControl apGroupClientAdmissionControl =
        new ApGroupClientAdmissionControl();
    apGroupClientAdmissionControl.setEnable24G(true);
    apGroupClientAdmissionControl.setMinClientCount24G((short) 11);
    apGroupClientAdmissionControl.setMaxRadioLoad24G((short) 50);
    apGroupClientAdmissionControl.setMinClientThroughput24G((short) 10);
    apGroupClientAdmissionControl.setEnable50G(true);
    apGroupClientAdmissionControl.setMinClientCount50G((short) 21);
    apGroupClientAdmissionControl.setMaxRadioLoad50G((short) 50);
    apGroupClientAdmissionControl.setMinClientThroughput50G((short) 10);
    apGroupClientAdmissionControl.setUseVenueSettings(false);

    ApGroup apGroup = new ApGroup();
    apGroup.setId("apGroupId");
    apGroup.setName("TestGroup");
    apGroup.setIsDefault(false);
    apGroup.setTenant(tenant);
    apGroup.setVenue(VenueTestFixture.randomVenue(tenant));
    apGroup.setClientAdmissionControl(apGroupClientAdmissionControl);

    // When
    List<Operation> operations = builder.build(new NewTxEntity<>(apGroup), emptyTxChanges());

    // Then
    assertNotNull(operations);
    assertEquals(1, operations.size());
    assertNotNull(operations.get(0).getApGroup());
    assertTrue(
        operations
            .get(0)
            .getApGroup()
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getEnabled());
    assertEquals(
        11,
        operations
            .get(0)
            .getApGroup()
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getMinimumClientCount());
    assertEquals(
        50,
        operations
            .get(0)
            .getApGroup()
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getMaxiumumRadioLoad());
    assertEquals(
        10,
        operations
            .get(0)
            .getApGroup()
            .getRadioCustomization()
            .getRadio24()
            .getClientAdmissionControl()
            .getMinimumClientThroughput());
    assertTrue(
        operations
            .get(0)
            .getApGroup()
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getEnabled());
    assertEquals(
        21,
        operations
            .get(0)
            .getApGroup()
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getMinimumClientCount());
    assertEquals(
        50,
        operations
            .get(0)
            .getApGroup()
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getMaxiumumRadioLoad());
    assertEquals(
        10,
        operations
            .get(0)
            .getApGroup()
            .getRadioCustomization()
            .getRadio50()
            .getClientAdmissionControl()
            .getMinimumClientThroughput());
  }

  private ExternalAntenna newExternalAntenna(int dbi24g, int dbi5g, boolean enable24g, boolean enable5g) {
    ExternalAntenna externalAntenna = new ExternalAntenna();
    externalAntenna.setExternalAntenna24dbi(dbi24g);
    externalAntenna.setExternalAntenna50dbi(dbi5g);
    externalAntenna.setExternalAntenna24enable(enable24g);
    externalAntenna.setExternalAntenna50enable(enable5g);
    return externalAntenna;
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    public DdccmApGroupOperationBuilder ddccmApGroupOperationBuilder() {
      var builder = spy(DdccmApGroupOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }

    @Bean
    public DdccmApGroupApModelOperationBuilder ddccmApGroupApModelOperationBuilder() {
      return new DdccmApGroupApModelOperationBuilder();
    }
  }
}