package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class AirtimeDecongestionFeatureTest {

  @SpyBean
  private AirtimeDecongestionFeature unit;

  @Test
  @FeatureFlag(disable = WIFI_R370_TOGGLE)
  void givenR370FFOff(Network network) {
    BDDAssertions.then(unit.test(network)).isFalse();
  }

  @Test
  @FeatureFlag(enable = WIFI_R370_TOGGLE)
  void givenAirtimeDecongestionDisable(Network network) {
    network.getWlan().getAdvancedCustomization().setEnableAirtimeDecongestion(false);
    BDDAssertions.then(unit.test(network)).isFalse();
  }

  @Test
  @FeatureFlag(enable = WIFI_R370_TOGGLE)
  void givenAirtimeDecongestionEnable(Network network) {
    network.getWlan().getAdvancedCustomization().setEnableAirtimeDecongestion(true);
    BDDAssertions.then(unit.test(network)).isTrue();
  }
}
