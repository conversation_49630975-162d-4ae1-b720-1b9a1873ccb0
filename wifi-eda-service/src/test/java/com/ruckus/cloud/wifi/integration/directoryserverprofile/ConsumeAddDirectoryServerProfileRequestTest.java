package com.ruckus.cloud.wifi.integration.directoryserverprofile;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.DirectoryServerProfileTestFixture.randomDirectoryServerProfile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanStep;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.api.rest.DirectoryServerProfileRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.DirectoryServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Objects;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("DirectoryServerProfileTest")
@WifiIntegrationTest
class ConsumeAddDirectoryServerProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeAddDirectoryServerProfileV1Message {

    @Test
    void givenDuplicatedProfileName(Tenant tenant) {
      var duplicatedName = randomName();
      repositoryUtil.createOrUpdate(
          randomDirectoryServerProfile(tenant, p -> p.setName(duplicatedName)),
          tenant.getId(),
          randomTxId());

      var request =
          DirectoryServerProfileRestCtrl.DirectoryServerProfileMapper.INSTANCE.ServiceDirectoryServerProfile2DirectoryServerProfile(
              randomDirectoryServerProfile(
                  tenant, p -> p.setName(duplicatedName)));

      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgExtendedAction.ADD_DIRECTORY_SERVER_PROFILE,
                  randomName(),
                  new RequestParams(),
                  request))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      messageCaptors
          .assertThat(
              messageCaptors.getCmnCfgCollectorMessageCaptor())
          .doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ExecutionPlanStep.ADD_DIRECTORY_SERVER_PROFILE));
    }

    @Test
    void givenValidProfile(Tenant tenant) {
      var request =
          DirectoryServerProfileRestCtrl.DirectoryServerProfileMapper.INSTANCE.ServiceDirectoryServerProfile2DirectoryServerProfile(
              randomDirectoryServerProfile());

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgExtendedAction.ADD_DIRECTORY_SERVER_PROFILE,
          randomName(),
          new RequestParams(),
          request);

      assertThat(repositoryUtil.find(DirectoryServerProfile.class, request.getId(), tenant.getId()))
          .isNotNull()
          .matches(p -> Objects.equals(p.getName(), request.getName()))
          .matches(p -> Objects.equals(p.getId(), request.getId()))
          .matches(p -> Objects.equals(p.getPort(), request.getPort()))
          .matches(p -> Objects.equals(p.getDomainName(), request.getDomainName()))
          .matches(p -> Objects.equals(p.getAdminDomainName(), request.getAdminDomainName()))
          .matches(p -> Objects.equals(p.getName(), request.getName()));

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.ADD)
          .matches(o -> o.getId().equals(request.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ExecutionPlanStep.ADD_DIRECTORY_SERVER_PROFILE));
    }
  }
}
