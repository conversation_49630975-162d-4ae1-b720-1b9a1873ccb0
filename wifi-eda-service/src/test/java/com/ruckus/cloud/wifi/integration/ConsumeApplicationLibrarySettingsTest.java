package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackage;
import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackageMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.ApplicationLibrarySettingsGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("ApplicationLibrarySettingTest")
@WifiIntegrationTest
public class ConsumeApplicationLibrarySettingsTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.PATCH_APPLICATION_LIBRARY_SETTINGS)
  class ConsumePatchApplicationLibrarySettingsTest {

    @Payload
    private final ApplicationLibrarySettingsGenerator requestGenerator =
        new ApplicationLibrarySettingsGenerator()
            .setVersion(always("ApplicationLibrarySettingsTest-v2"));

    private String wlanId;

    @BeforeEach
    void givenRequiredDataPersistedInDb(final Tenant tenant, final Venue venue,
        ApplicationPolicy applicationPolicy) {
      var sp1 = new SignaturePackage("ApplicationLibrarySettingsTest-v1");
      var sp2 = new SignaturePackage("ApplicationLibrarySettingsTest-v2");

      sp1.setVersion(sp1.getId());
      sp1.setReleasable(true);
      sp1.setDefaultVersion(true);
      sp1.setNextVersion(sp2.getId());
      sp1.setSignaturePackageMappings(new ArrayList<>());
      sp1 = repositoryUtil.createOrUpdate(sp1, tenant.getId(), randomTxId());

      sp2.setVersion(sp2.getId());
      sp2.setReleasable(true);
      sp1.setDefaultVersion(false);
      sp2.setSignaturePackageMappings(new ArrayList<>());
      sp2 = repositoryUtil.createOrUpdate(sp2, tenant.getId(), randomTxId());

      var mapping = new SignaturePackageMapping();
      mapping.setSignaturePackage(sp1);
      mapping.setVersion(sp1.getVersion());
      mapping.setToVersion(sp2.getVersion());
      mapping.setGenerated(false);
      mapping.setSignaturePackageMappingItems(new ArrayList<>());
      mapping = repositoryUtil.createOrUpdate(mapping, tenant.getId(), randomTxId());

      sp1.getSignaturePackageMappings().add(mapping);
      sp1 = repositoryUtil.createOrUpdate(sp1, tenant.getId(), randomTxId());

      applicationPolicy.setSignaturePackage(sp1);
      repositoryUtil.createOrUpdate(applicationPolicy, tenant.getId(), randomTxId());

      tenant.setSignaturePackage(sp1);
      tenant.setLatestSignaturePackage(sp2);
      tenant.setLatestSignaturePackageReleasedDate(new Date());
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId());

      final var pskNetwork = network(PskNetwork.class).generate();
      pskNetwork.getWlan().setNetwork(pskNetwork);
      pskNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
      pskNetwork.getWlan().getAdvancedCustomization().setApplicationPolicy(applicationPolicy);
      repositoryUtil.createOrUpdate(pskNetwork, tenant.getId(), randomTxId());

      final var networkVenue = networkVenue()
          .setNetwork(always(pskNetwork)).setVenue(always(venue)).generate();
      pskNetwork.setNetworkVenues(List.of(networkVenue));
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      wlanId = pskNetwork.getWlan().getId();
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();

      var wlan = repositoryUtil.find(Wlan.class, wlanId, tenantId);
      assertThat(wlan)
          .matches(w -> w.getPreviousApplicationPolicy() != null)
          .matches(w -> !w.getPreviousApplicationPolicy().getId()
              .equals(w.getAdvancedCustomization().getApplicationPolicy().getId()));
      var applicationPolicyId = wlan.getAdvancedCustomization().getApplicationPolicy().getId();
      // Validate DDCCM
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(ddccmCfgRequestMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() -> {
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(ops -> {
              // For all matched the same request
              assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                          com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                      .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                      .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                      .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                  .hasSize(7);
              // Should contain 2 ADD, 3 MODIFY and 2 DELETE
              // ADD - ApplicationPolicy and UserDefinedApplicationRule
              assertThat(ops)
                  .filteredOn(op -> op.getAction() == Action.ADD)
                  .hasSize(2)
                  .allMatch(op -> op.hasApplicationPolicy() || op.hasUserDefinedApplicationRule());
              // MODIFY - Tenant, SignaturePackage and WlanVenue
              assertThat(ops)
                  .filteredOn(op -> op.getAction() == Action.MODIFY)
                  .hasSize(3)
                  .allMatch(op -> op.hasTenant() || op.hasCcmQmSignaturePackageAcx()
                      || op.hasWlanVenue());
              assertThat(ops)
                  .filteredOn(op -> op.getAction() == Action.MODIFY && op.hasWlanVenue())
                  .hasSize(1)
                  .allMatch(op -> op.getWlanVenue().getAppPolicyId().getValue()
                      .equals(applicationPolicyId));
              // DELETE - ApplicationPolicy and UserDefinedApplicationRule
              assertThat(ops)
                  .filteredOn(op -> op.getAction() == Action.DELETE)
                  .hasSize(2)
                  .allMatch(op -> op.hasApplicationPolicy() || op.hasUserDefinedApplicationRule());
            });
      });
      // Validate Common Config
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThatNoException().isThrownBy(
          () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                  msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME.equals(op.getIndex()))
              .satisfies(ops -> {
                // One is for adding the migrated application policy
                // The other one is for deleting the snapshot application policy.
                assertThat(ops).hasSize(2);
                assertThat(ops).filteredOn(op -> op.getOpType() == OpType.ADD).hasSize(1);
                assertThat(ops).filteredOn(op -> op.getOpType() == OpType.DEL).hasSize(1);
              }));
    }
  }
}
