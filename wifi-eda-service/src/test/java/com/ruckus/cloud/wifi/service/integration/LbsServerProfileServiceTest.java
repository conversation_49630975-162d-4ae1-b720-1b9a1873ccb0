package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.ViewmodelAggregatedEntityListener;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.LbsServerProfileCmnCfgCollectorOperationBuilder;
import com.ruckus.cloud.wifi.kafka.publisher.ViewmodelConfigPublisher;
import com.ruckus.cloud.wifi.mapper.LbsServerProfileMerge;
import com.ruckus.cloud.wifi.mapper.LbsServerProfileMergeImpl;
import com.ruckus.cloud.wifi.repository.LbsServerProfileRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.InitVenueService;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.impl.LbsServerProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.TestContextHelper;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@WifiJpaDataTest
public class LbsServerProfileServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @SpyBean
  private LbsServerProfileServiceCtrlImpl lbsServerProfileService;
  @MockBean
  private InitVenueService initVenueService;
  @Autowired
  private LbsServerProfileRepository lbsServerProfileRepository;
  @Autowired
  private VenueRepository venueRepository;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @MockBean
  private ViewmodelConfigPublisher viewmodelConfigPublisher;
  @Autowired
  private TestContextHelper testContextHelper;

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateLbsServerProfileOnVenue(Tenant tenant, Venue venue) throws Exception {
    // Given
    var lbsServerProfile = Generators.lbsServerProfile().generate();
    var savedLbsServerProfile = repositoryUtil.createOrUpdate(lbsServerProfile, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    doReturn(venue).when(initVenueService).initVenueIfNotExists(eq(venue.getId()));

    // When
    lbsServerProfileService.activateLbsServerProfileOnVenue(venue.getId(), savedLbsServerProfile.getId());

    // Then
    var savedVenue = repositoryUtil.find(Venue.class, venue.getId());
    assertThat(savedVenue)
        .isNotNull()
        .extracting(Venue::getLbsServerProfile)
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(lbsServerProfile.getId());

    // Given for case - activate another lbsServerProfile
    var lbsServerProfile2 = Generators.lbsServerProfile().generate();

    var savedLbsServerProfile2 = repositoryUtil.createOrUpdate(lbsServerProfile2, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    testContextHelper.executeInNewTransaction(() -> {
      try {
        lbsServerProfileService.activateLbsServerProfileOnVenue(venue.getId(), savedLbsServerProfile2.getId());
      } catch (Exception ignored) {
      }
    }, tenant.getId(), randomTxId());

    ArgumentCaptor<ViewmodelCollector> captor = ArgumentCaptor.forClass(ViewmodelCollector.class);
    verify(viewmodelConfigPublisher, times(2)).publish(captor.capture(),
        any(Optional.class));

    List<ViewmodelCollector> viewmodelCfgRequests = captor.getAllValues();
    assertThat(viewmodelCfgRequests.get(0))
        .isNotNull()
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .hasSize(1); // activating lbsServerProfile
    assertThat(viewmodelCfgRequests.get(1))
        .isNotNull()
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .hasSize(2); // original and activating lbsServerProfile
  }

  @Test
  void testDeactivateLbsServerProfileOnVenue(Tenant tenant) throws Exception {
    // Given
    var lbsServerProfile = Generators.lbsServerProfile().generate();
    var savedLbsServerProfile = repositoryUtil.createOrUpdate(lbsServerProfile, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    var venue = Generators.venue().generate();
    venue.setLbsServerProfile(savedLbsServerProfile);
    repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    // When
    lbsServerProfileService.deactivateLbsServerProfileOnVenue(venue.getId(), savedLbsServerProfile.getId());

    // Then
    var savedVenue = repositoryUtil.find(Venue.class, venue.getId());
    assertThat(savedVenue)
        .extracting(Venue::getLbsServerProfile)
        .isNull();
  }

  @Test
  void testDeactivateLbsServerProfileOnVenue_wrongLbsServerProfile(Tenant tenant) throws Exception {
    // Given
    var deactivatingLbsServerProfile = Generators.lbsServerProfile().generate();
    var originalLbsServerProfile = Generators.lbsServerProfile().generate();
    repositoryUtil.createOrUpdate(deactivatingLbsServerProfile, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(originalLbsServerProfile, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    var venue = Generators.venue().generate();
    venue.setLbsServerProfile(originalLbsServerProfile);
    repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());

    // Then
    assertThrows(InvalidPropertyValueException.class,
        () -> lbsServerProfileService.deactivateLbsServerProfileOnVenue(venue.getId(), deactivatingLbsServerProfile.getId()));
  }

  @TestConfiguration
  @Import({
      ViewmodelAggregatedEntityListener.class,
      LbsServerProfileCmnCfgCollectorOperationBuilder.class,
  })
  static class Config {

    @Bean
    public LbsServerProfileMerge lbsServerProfileMerge() {
      return new LbsServerProfileMergeImpl();
    }

  }

}
