package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.txChangesReader;
import static java.util.Collections.emptyList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.AaaType;
import com.ruckus.acx.ddccm.protobuf.wifi.CalledStaIdType;
import com.ruckus.acx.ddccm.protobuf.wifi.DhcpOption82MacFormat;
import com.ruckus.acx.ddccm.protobuf.wifi.DscpExceptionValue;
import com.ruckus.acx.ddccm.protobuf.wifi.DynamicSaeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.MacAuthMacFormat;
import com.ruckus.acx.ddccm.protobuf.wifi.ManagementFrameProtection;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.acx.ddccm.protobuf.wifi.QosMirroringEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.ScheduleType;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueRadius;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanAcct;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanAcctProxy;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanAuth;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanAuthProxy;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanSecurity;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanType;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.CloudpathServer;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.EdgeConnectResource;
import com.ruckus.cloud.wifi.eda.servicemodel.FastRoamingOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.MultiLinkOperationOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkHotspot20Settings;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenueScheduler;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.PinProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.QosMapRule;
import com.ruckus.cloud.wifi.eda.servicemodel.QosMapSetOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueDnsProxy;
import com.ruckus.cloud.wifi.eda.servicemodel.VenuePortal;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadiusOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadiusServerProfileSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.WisprPage;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanRadioCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BSSPriorityEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CalledStationIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CloudpathDeploymentTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MacAuthMacFormatEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ManagementFrameProtectionEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdDelimiterEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PhyTypeConstraintEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.QosMirroringScopeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.mapper.NetworkVenueMerge;
import com.ruckus.cloud.wifi.repository.AccountingRadiusVenueRepository;
import com.ruckus.cloud.wifi.repository.AuthRadiusVenueRepository;
import com.ruckus.cloud.wifi.repository.DpskNetworkRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.PinProfileNetworkMappingRepository;
import com.ruckus.cloud.wifi.repository.SdLanProfileNetworkMappingRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileNetworkVenueActivationRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesImpl;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.utils.Hotspot20Helper;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82MacEnum;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption150Enum;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption151Enum;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption1Enum;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption2Enum;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkTestFixture;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.TestPropertySource;

@WifiUnitTest
@FeatureFlag(enable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
@TestPropertySource(properties = {
        "guest-network.wlan-auth-service=guest-network-auth-service"})
public class DdccmNetworkVenueOperationBuilderTest {

  @SpyBean
  private DdccmNetworkVenueOperationBuilder builder;

  @MockBean
  private DdccmHotspot20ProfileOperationBuilder ddccmHotspot20ProfileOperationBuilder;

  @MockBean
  private DdccmAuthRadiusVenueOperationBuilder ddccmAuthRadiusVenueOperationBuilder;

  @MockBean
  private DdccmHotspot20VenueProfileOperationBuilder ddccmHotspot20VenueProfileOperationBuilder;

  @MockBean
  private AuthRadiusVenueRepository authRadiusVenueRepository;

  @MockBean
  private AccountingRadiusVenueRepository accountingRadiusVenueRepository;

  @MockBean
  private DpskNetworkRepository dpskNetworkRepository;

  @MockBean
  private NetworkRepository networkRepository;

  @MockBean
  private NetworkVenueRepository networkVenueRepository;

  @MockBean
  private SdLanProfileNetworkMappingRepository sdLanProfileNetworkMappingRepository;

  @MockBean
  private PinProfileNetworkMappingRepository pinProfileNetworkMappingRepository;

  @MockBean
  private SoftGreProfileNetworkVenueActivationRepository
      softGreProfileNetworkVenueActivationRepository;

  private static MockedStatic<Hotspot20Helper> hotspot20Helper;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  public void beforeClass() {
    hotspot20Helper = mockStatic(Hotspot20Helper.class);
  }

  @AfterEach
  public void afterClass() {
    hotspot20Helper.close();
  }

  @SuppressWarnings("unchecked")
  @BeforeEach
  void mockHasChanged() {
    doReturn(true).when(builder).hasChanged(any(TxEntity.class), any(TxChanges.class));
  }

  @Test
  public void testAddPskNetworkVenue() {
    NetworkVenue networkVenue = createPskNetworkVenue();

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PRIVATE, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("PSK", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertEquals(networkVenue.getNetwork().getId(), operations.get(0).getWlanVenue().getWlanId());
    assertEquals(networkVenue.getNetwork().getName(), operations.get(0).getWlanVenue().getWlanName());
    assertEquals("fake-network", operations.get(0).getWlanVenue().getWlanName());
    assertEquals(networkVenue.getVenue().getId(), operations.get(0).getWlanVenue().getVenueId());
    assertEquals(networkVenue.getNetwork().getWlan().getSsid(), operations.get(0).getWlanVenue().getSsid());
    assertEquals("fake-ssid", operations.get(0).getWlanVenue().getSsid());

    // verify the client isolation default value
    var advancedCustomization = operations.get(0).getWlanVenue().getAdvancedCustomization();
    assertEquals(false, advancedCustomization.getClientIsolation().getValue());
    assertEquals(false, advancedCustomization.getClientIsolationUnicast().getValue());
    assertEquals(false, advancedCustomization.getClientIsolationMulticast().getValue());
  }

  @Test
  public void testAddOpenNetworkVenue() {
    NetworkVenue networkVenue = createOpenNetworkVenue();

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("OPEN", operations.get(0).getWlanVenue().getNetworkType().getValue());
  }

  @Test
  public void testAddPublicGuestNetworkVenue() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  public void testAddHotspot20NetworkVenue() {
    final var mockHotspot20ProfileOperation = Operation.newBuilder()
        .setHotspot20Profile(com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20Profile.newBuilder().build())
        .build();
    final var mockHotspot20VenueProfileOperation = Operation.newBuilder()
        .setHotspot20VenueProfile(com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20VenueProfile.newBuilder().build())
        .build();

    NetworkVenue networkVenue = createHotspot20NetworkVenue();
    when(Hotspot20Helper.isHotspot20Network(any())).thenReturn(true);
    when(Hotspot20Helper.isHotspot20SettingsActivated(any())).thenReturn(true);
    doReturn(mockHotspot20ProfileOperation)
        .when(ddccmHotspot20ProfileOperationBuilder).createHotspot20ProfileOperation(any(), any());
    doReturn(mockHotspot20VenueProfileOperation)
        .when(ddccmHotspot20VenueProfileOperationBuilder).createHotspot20VenueProfileOperation(any(), any());

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(3, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.HOTSPOT20, operations.get(0).getWlanVenue().getWlanType());
    assertEquals(WlanSecurity.WPA2Enterprise, operations.get(0).getWlanVenue().getWlanSecurity());
    assertEquals(NetworkTypeEnum.HOTSPOT20.name(), operations.get(0).getWlanVenue().getNetworkType().getValue());

    assertEquals(30, operations.get(0).getWlanVenue().getAccountingInterval().getValue());
    assertEquals("hs20sId", operations.get(0).getWlanVenue().getHotspot20Id());

    assertFalse(operations.get(0).getWlanVenue().hasAuthAaa());
    assertEquals("", operations.get(0).getWlanVenue().getAuthenticationServerId());
    assertFalse(operations.get(0).getWlanVenue().hasAcctAaa());
    assertEquals("", operations.get(0).getWlanVenue().getAccountingServerId());
    assertFalse(operations.get(0).getWlanVenue().getAuthenticationRealmBasedProxy());
    assertFalse(operations.get(0).getWlanVenue().getAccountingRealmBasedProxy());

    assertTrue(operations.get(1).hasHotspot20Profile());
    assertTrue(operations.get(2).hasHotspot20VenueProfile());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testAddHotspot20NetworkVenue_withoutActivated_shouldNotIncludeHotspotOperations() {
    NetworkVenue networkVenue = createHotspot20NetworkVenue();
    when(Hotspot20Helper.isHotspot20Network(any())).thenReturn(true);
    when(Hotspot20Helper.isHotspot20SettingsActivated(any())).thenReturn(false);

    RuntimeException exception = assertThrows(RuntimeException.class, () -> {
      builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    });
    Assertions.assertEquals(Errors.WIFI_10525.message(), exception.getMessage());
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE, FlagNames.WIFI_NAS_ID_FOR_HOTSPOT20_NETWORK})
  public void testAddHotspot20NetworkVenue_hotspot20NasId() {
    final var mockHotspot20ProfileOperation = Operation.newBuilder()
        .setHotspot20Profile(
            com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20Profile.newBuilder().build())
        .build();
    final var mockHotspot20VenueProfileOperation = Operation.newBuilder()
        .setHotspot20VenueProfile(
            com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20VenueProfile.newBuilder().build())
        .build();

    var networkVenue = createHotspot20NetworkVenueWithRadiusOptions();

    when(Hotspot20Helper.isHotspot20Network(any())).thenReturn(true);
    when(Hotspot20Helper.isHotspot20SettingsActivated(any())).thenReturn(true);
    doReturn(mockHotspot20ProfileOperation)
        .when(ddccmHotspot20ProfileOperationBuilder).createHotspot20ProfileOperation(any(), any());
    doReturn(mockHotspot20VenueProfileOperation)
        .when(ddccmHotspot20VenueProfileOperationBuilder)
        .createHotspot20VenueProfileOperation(any(), any());

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(3, operations.size());
    assertTrue(operations.get(1).hasHotspot20Profile());
    assertTrue(operations.get(2).hasHotspot20VenueProfile());

    assertEquals(StringValue.of(NasIdTypeEnum.USER.toString()), operations.get(0).getWlanVenue().getRadiusOptions().getNasIdType());
    assertEquals(Int32Value.of(55), operations.get(0).getWlanVenue().getRadiusOptions().getAccountingMaxRetry());
    assertEquals(StringValue.of("NasId"), operations.get(0).getWlanVenue().getRadiusOptions().getUserDefinedNasId());

  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  public void testAddHotspot20NetworkVenue_hotspot20NasId_noFF() {
    final var mockHotspot20ProfileOperation = Operation.newBuilder()
        .setHotspot20Profile(
            com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20Profile.newBuilder().build())
        .build();
    final var mockHotspot20VenueProfileOperation = Operation.newBuilder()
        .setHotspot20VenueProfile(
            com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20VenueProfile.newBuilder().build())
        .build();

    var networkVenue = createHotspot20NetworkVenueWithRadiusOptions();

    when(Hotspot20Helper.isHotspot20Network(any())).thenReturn(true);
    when(Hotspot20Helper.isHotspot20SettingsActivated(any())).thenReturn(true);
    doReturn(mockHotspot20ProfileOperation)
        .when(ddccmHotspot20ProfileOperationBuilder).createHotspot20ProfileOperation(any(), any());
    doReturn(mockHotspot20VenueProfileOperation)
        .when(ddccmHotspot20VenueProfileOperationBuilder)
        .createHotspot20VenueProfileOperation(any(), any());

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(3, operations.size());
    assertTrue(operations.get(1).hasHotspot20Profile());
    assertTrue(operations.get(2).hasHotspot20VenueProfile());

    assertEquals(StringValue.of(""), operations.get(0).getWlanVenue().getRadiusOptions().getNasIdType());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testAddAaaNetworkVenue_shouldNotIncludeHotspotOperations() {
    NetworkVenue networkVenue = createAaaNetworkVenue();

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertFalse(operations.get(0).getWlanVenue().hasAccountingInterval());
    assertEquals("", operations.get(0).getWlanVenue().getHotspot20Id());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_EDA_BSS_PRIORITY_TOGGLE)
  @FeatureRole("AP-70")
  public void testBssPriorityLow() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setBssPriority(BSSPriorityEnum.LOW);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(StringValue.of("PRIORITY_LOW"), operations.get(0).getWlanVenue().getPriority());
  }

  @Test
  @FeatureRole("AP-70")
  public void testWifi67Enabled() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setWifi6Enabled(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setWifi7Enabled(true);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getWifi6Enabled().getValue());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getWifi7Enabled().getValue());
  }

  @Test
  public void testWifi67Empty() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(BoolValue.newBuilder().build(),
        operations.get(0).getWlanVenue().getAdvancedCustomization().getWifi6Enabled());
    assertEquals(BoolValue.newBuilder().build(),
        operations.get(0).getWlanVenue().getAdvancedCustomization().getWifi6Enabled());
  }

  @Test
  public void testWifi67Disabled() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setWifi6Enabled(false);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setWifi7Enabled(false);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().getWifi6Enabled().getValue());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().getWifi7Enabled().getValue());
  }

  @Test
  @FeatureRole("AP-70")
  public void testMultiLinkOperation() {
    MultiLinkOperationOptions multiLinkOperationOptions = new MultiLinkOperationOptions();
    multiLinkOperationOptions.setEnable6G(false);
    multiLinkOperationOptions.setEnable50G(true);
    multiLinkOperationOptions.setEnable24G(true);
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setMultiLinkOperationEnabled(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setMultiLinkOperationOptions(multiLinkOperationOptions);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getMultiLinkOperation()
        .getEnabled());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().getMultiLinkOperation()
        .getEnable6G());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getMultiLinkOperation()
        .getEnable24G());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getMultiLinkOperation()
        .getEnable50G());
  }

  @Test
  @FeatureRole("AP-70")
  public void testQosMirroringMscsOnly() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setQosMirroringEnabled(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setQosMirroringScope(QosMirroringScopeEnum.MSCS_REQUESTS_ONLY);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(QosMirroringEnum.QOS_ENABLED_PROTOCOL,
        operations.get(0).getWlanVenue().getAdvancedCustomization().getQosMirroring());
  }

  @Test
  @FeatureRole("AP-70")
  public void testQosMirroringDisabled() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setQosMirroringEnabled(false);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setQosMirroringScope(QosMirroringScopeEnum.MSCS_REQUESTS_ONLY);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(QosMirroringEnum.QOS_DISABLED,
        operations.get(0).getWlanVenue().getAdvancedCustomization().getQosMirroring());
  }

  @Test
  public void testQosMirroringFeatureFlagDisabled() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setQosMirroringEnabled(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setQosMirroringScope(QosMirroringScopeEnum.MSCS_REQUESTS_ONLY);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(QosMirroringEnum.QosMirroringEnum_UNSET,
        operations.get(0).getWlanVenue().getAdvancedCustomization().getQosMirroring());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_PSK_DYNAMIC_VLAN_TOGGLE)
  void testPskDynamicVlan() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().hasEnableAaaVlanOverride());

    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);
    operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().hasEnableAaaVlanOverride());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_PSK_DYNAMIC_VLAN_TOGGLE)
  void testPskDynamicVlan_FeatureFlagDisabled() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().hasEnableAaaVlanOverride());
  }

  @Test
  public void testAddWisprGuestNetworkVenue_withAuthRadius_customExternalProvider() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.getNetwork().setEnableAuthProxy(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);

    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService("authRadiusService-id", authRadius, guestPortal)));
    wisprPage.setAuthRadius(authRadius);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());

    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals("authRadiusService-id", wlanAuth.getId());
    assertTrue(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());
  }

  @Test
  public void testAddWisprGuestNetworkVenue_customExternalProvider_radiusNotFound() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);

    Radius authRadius = new Radius("authRadius-id");
    // Remove the relationship between the radius and the portal
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService("authRadiusService-id", authRadius, null, null)));
    wisprPage.setAuthRadius(authRadius);

    Assertions.assertThrows(RuntimeException.class, ()-> builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges()));
  }

  @Test
  public void testAddWisprGuestNetworkVenue_customExternalProvider_withoutAuthRadius() {
    var networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    var guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    var wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);

    var operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    var wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertTrue(wlanAuth.getId().contains("-Auth-"));
    assertTrue(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());
  }

  @Test
  public void testAddWisprGuestNetworkVenue_usingPredefineAaa() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(false);
    wisprPage.setAaaName("aaa");
    guestPortal.setWisprPage(wisprPage);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());

    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals(TxCtxHolder.get().getTenant() + "-Auth-aaa", wlanAuth.getId());
    assertTrue(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());
  }

  @Test
  public void testAddWisprGuestNetworkVenue_withAccountingRadius_customExternalProvider() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);

    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService("authRadiusService-id", authRadius, guestPortal)));
    wisprPage.setAuthRadius(authRadius);
    Radius acctRadius = new Radius("acctRadius-id");
    acctRadius.setAccountingRadiusServices(
        List.of(accountingRadiusService("acctRadiusService-id", acctRadius, guestPortal)));
    wisprPage.setAccountingRadius(acctRadius);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());

    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusService-id", wlanAcct.getId());
    assertTrue(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Test
  public void testAddWisprGuestNetworkVenue_withAccountingRadius_failedWithRadiusNotFound() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);

    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService("authRadiusService-id", authRadius, guestPortal)));
    wisprPage.setAuthRadius(authRadius);
    Radius acctRadius = new Radius("acctRadius-id");
    acctRadius.setAccountingRadiusServices(emptyList());
    wisprPage.setAccountingRadius(acctRadius);

    Assertions.assertThrows(RuntimeException.class,
        ()->builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges()));
  }

  @Test
  public void testAddWisprGuestNetworkVenue_withAccountingRadius_customExternalProvider_alwaysAccept() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(false);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestPortal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    wisprPage.setAuthType(AuthTypeEnum.ALWAYS_ACCEPT);
    guestPortal.setWisprPage(wisprPage);

    Radius acctRadius = new Radius("acctRadius-id");
    acctRadius.setAccountingRadiusServices(
        List.of(accountingRadiusService("acctRadiusService-id", acctRadius, guestPortal)));
    wisprPage.setAccountingRadius(acctRadius);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());

    assertFalse(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertEquals("Always Accept", operations.get(0).getWlanVenue().getAuthenticationType().getValue());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());

    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusService-id", wlanAcct.getId());
    assertTrue(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Deprecated(forRemoval = true)
  @Test
  @FeatureFlag(disable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
  public void testAddGuestNetworkVenue_withCloudpathAccountingRadius() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();

    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);

    CloudpathServer cloudpathServer = new CloudpathServer("cloudpathServer-id");
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.Cloud);
    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);

    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService("authRadiusService-id", authRadius, guestPortal, cloudpathServer)));
    Radius acctRadius = new Radius("acctRadius-id");
    var accountingRadiusVenue = accountingRadiusVenue("acctRadiusService-id", acctRadius, networkVenue.getVenue());
    acctRadius.setAccountingRadiusVenues(List.of(accountingRadiusVenue));
    acctRadius.setAccountingRadiusServices(
        List.of(accountingRadiusService("acctRadiusService-id", acctRadius, guestPortal, cloudpathServer)));
    cloudpathServer.setAuthRadius(authRadius);
    cloudpathServer.setAccountingRadius(acctRadius);

    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(acctRadius.getId()));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());

    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusService-id", wlanAcct.getId());
    assertTrue(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Deprecated(forRemoval = true)
  @Test
  @FeatureFlag(disable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
  public void testAddDpskNetworkVenue_withCloudpathAccountingRadius() {
    NetworkVenue networkVenue = createDpskNetworkVenue();

    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));

    CloudpathServer cloudpathServer = new CloudpathServer("cloudpathServer-id");
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.Cloud);
    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);

    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService("authRadiusService-id", authRadius, cloudpathServer)));
    Radius acctRadius = new Radius("acctRadius-id");
    acctRadius.setAccountingRadiusServices(
        List.of(accountingRadiusService("acctRadiusService-id", acctRadius, cloudpathServer)));
    cloudpathServer.setAuthRadius(authRadius);
    cloudpathServer.setAccountingRadius(acctRadius);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());

    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusService-id", wlanAcct.getId());
    assertTrue(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Deprecated(forRemoval = true)
  @Test
  @FeatureFlag(disable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
  public void testAddWisprGuestNetworkVenue_withCloudpathAccountingRadius_failedWithAccountingNotFound() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();

    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);

    CloudpathServer cloudpathServer = new CloudpathServer("cloudpathServer-id");
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.Cloud);
    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);

    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService("authRadiusService-id", authRadius, guestPortal, cloudpathServer)));

    Radius acctRadius = new Radius("acctRadius-id");
    // Remove the relationship between the accounting radius and the cloudpath server
    acctRadius.setAccountingRadiusServices(
        List.of(accountingRadiusService("acctRadiusService-id", acctRadius, guestPortal, null)));
    cloudpathServer.setAuthRadius(authRadius);
    cloudpathServer.setAccountingRadius(acctRadius);

    Assertions.assertThrows(RuntimeException.class,
        ()->builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges()));
  }

  @Test
  public void testAddPrivateGuestOWENetworkVenue() {
    //Given
    NetworkVenue networkVenue = createPrivateGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.OWE);
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);

    //When
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    //Then
    assertEquals(1, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PRIVATEGUEST, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertEquals(WlanSecurityEnum.OWE, networkVenue.getNetwork().getWlan().getWlanSecurity());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());
    assertEquals(ManagementFrameProtection.Required, operations.get(0).getWlanVenue().getManagementFrameProtection());
  }

  @Test
  public void testAddOweOpenNetworkVenue() {
    //Given
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.OWE);

    //When
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    //Then
    assertEquals(1, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("OPEN", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertEquals(WlanSecurityEnum.OWE.name(), operations.get(0).getWlanVenue().getWlanSecurity().name());
  }

  @Test
  public void testAddOweTransitionNetworkVenueMaster() {
    //Given
    NetworkVenue masterNetworkVenue = createOpenNetworkVenue();
    NetworkVenue slaveNetworkVenue = createOpenNetworkVenue();

    ((OpenNetwork)masterNetworkVenue.getNetwork()).setOwePairNetworkId(slaveNetworkVenue.getNetwork().getId());
    ((OpenNetwork)masterNetworkVenue.getNetwork()).setIsOweMaster(true);
    masterNetworkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
    masterNetworkVenue.setOweTransWlanId(slaveNetworkVenue.getApWlanId());

    String slaveSsid = "xxxx-owe-tr";
    ((OpenNetwork)slaveNetworkVenue.getNetwork()).setOwePairNetworkId(masterNetworkVenue.getNetwork().getId());
    ((OpenNetwork)slaveNetworkVenue.getNetwork()).setIsOweMaster(false);
    slaveNetworkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
    slaveNetworkVenue.getNetwork().getWlan().setSsid(slaveSsid);
    slaveNetworkVenue.getNetwork().setName(slaveSsid);
    slaveNetworkVenue.setOweTransWlanId(masterNetworkVenue.getOweTransWlanId());

    when(networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(any(), any(), any())).thenReturn(Optional.of(slaveNetworkVenue));
    when(networkRepository.findByIdAndTenantId(any(), any())).thenReturn(Optional.of(slaveNetworkVenue.getNetwork()));

    //When
    List<Operation> operations = builder.build(new NewTxEntity<>(masterNetworkVenue), emptyTxChanges());

    //Then
    assertEquals(2, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals(Action.ADD, operations.get(1).getAction());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("OPEN", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertEquals(WlanType.PUBLIC, operations.get(1).getWlanVenue().getWlanType());
    assertEquals("OPEN", operations.get(1).getWlanVenue().getNetworkType().getValue());
    assertEquals(WlanSecurityEnum.Open.name(), operations.get(0).getWlanVenue().getWlanSecurity().name());
    assertEquals(WlanSecurityEnum.OWE.name(), operations.get(1).getWlanVenue().getWlanSecurity().name());
    assertEquals(slaveSsid, operations.get(1).getWlanVenue().getWlanName());
    assertEquals(slaveSsid, operations.get(1).getWlanVenue().getSsid());
  }

  @Test
  public void testAddOweTransitionNetworkVenueSlave() {
    //Given
    NetworkVenue networkVenue = createOpenNetworkVenue();
    ((OpenNetwork)networkVenue.getNetwork()).setOwePairNetworkId("masterNetworkId");
    ((OpenNetwork)networkVenue.getNetwork()).setIsOweMaster(false);
    networkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);

    //When
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    //Then
    assertNull(operations);
  }

  @Test
  @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
  public void testAddOweTransitionGuestNetworkVenueMaster() {
    //Given
    var masterNetworkVenue = createPublicGuestNetworkVenue();
    var slaveNetworkVenue = createPublicGuestNetworkVenue();

    masterNetworkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    slaveNetworkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));

    var guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);

    ((GuestNetwork) masterNetworkVenue.getNetwork()).setOwePairNetworkId(
        slaveNetworkVenue.getNetwork().getId());
    ((GuestNetwork) masterNetworkVenue.getNetwork()).setIsOweMaster(true);
    ((GuestNetwork) masterNetworkVenue.getNetwork()).setGuestPortal(guestPortal);
    masterNetworkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
    masterNetworkVenue.setOweTransWlanId(slaveNetworkVenue.getApWlanId());

    var slaveSsid = "xxxx-owe-tr";
    ((GuestNetwork) slaveNetworkVenue.getNetwork()).setOwePairNetworkId(
        masterNetworkVenue.getNetwork().getId());
    ((GuestNetwork) slaveNetworkVenue.getNetwork()).setIsOweMaster(false);
    ((GuestNetwork) slaveNetworkVenue.getNetwork()).setGuestPortal(guestPortal);
    slaveNetworkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
    slaveNetworkVenue.getNetwork().getWlan().setSsid(slaveSsid);
    slaveNetworkVenue.getNetwork().setName(slaveSsid);
    slaveNetworkVenue.setOweTransWlanId(masterNetworkVenue.getOweTransWlanId());

    when(networkVenueRepository.findByTenantIdAndNetworkIdAndVenueId(any(), any(),
        any())).thenReturn(Optional.of(slaveNetworkVenue));
    when(networkRepository.findByIdAndTenantId(any(), any())).thenReturn(
        Optional.of(slaveNetworkVenue.getNetwork()));

    //When
    var operations = builder.build(new NewTxEntity<>(masterNetworkVenue), emptyTxChanges());

    //Then
    assertEquals(2, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals(Action.ADD, operations.get(1).getAction());
    assertEquals(WlanType.PRIVATEGUEST, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertEquals(WlanType.PRIVATEGUEST, operations.get(1).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(1).getWlanVenue().getNetworkType().getValue());
    assertEquals(WlanSecurityEnum.OpenCaptivePortal.name(),
        operations.get(0).getWlanVenue().getWlanSecurity().name());
    assertEquals(WlanSecurityEnum.OWE.name(),
        operations.get(1).getWlanVenue().getWlanSecurity().name());
    assertEquals(slaveSsid, operations.get(1).getWlanVenue().getWlanName());
    assertEquals(slaveSsid, operations.get(1).getWlanVenue().getSsid());
  }

  @Test
  @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
  public void testAddOweTransitionGuestNetworkVenueSlave() {
    //Given
    var networkVenue = createPublicGuestNetworkVenue();
    ((GuestNetwork) networkVenue.getNetwork()).setOwePairNetworkId("masterNetworkId");
    ((GuestNetwork) networkVenue.getNetwork()).setIsOweMaster(false);
    networkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);

    //When
    var operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    //Then
    assertNull(operations);
  }

  @Test
  public void testAddPrivateGuestNetworkVenue() {
    NetworkVenue networkVenue = createPrivateGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PRIVATEGUEST, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());
  }

  @Test
  public void testAddDpskNetworkVenue() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.DPSK, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("DPSK", operations.get(0).getWlanVenue().getNetworkType().getValue());
  }

  @Test
  public void testAddNetworkVenue_withoutOperations() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    doReturn(false).when(builder).hasChanged(any(), any());
    assertEquals(builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges()).size(), 0);
    doReturn(true).when(builder).hasChanged(any(), any());
  }

  @Test
  public void testAddNetworkVenue_unsupportedNetwork() {
    NetworkVenue networkVenue = setupNetworkVenue(new Network(randomId()));
    Assertions.assertThrows(UnsupportedOperationException.class,
        ()->builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges()));
  }

  @Tag("VlanPoolTest")
  @Test
  public void testAddNetworkVenue_enableVlanPool() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().setVlanId((short)1);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setVlanPool(new VlanPool("vlanPool-id"));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("vlanPool-id", operations.get(0).getWlanVenue().getVlanPoolId().getValue());
    assertFalse(operations.get(0).getWlanVenue().hasVlanId());
  }

  @Test
  public void testAddNetworkVenue_enableAuthRadius() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    clearInvocations(authRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals("authRadiusVenue-id",
        operations.get(0).getWlanVenue().getAuthenticationServerId());
    assertEquals("authRadiusVenue-id", wlanAuth.getId());
    assertFalse(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());

    verify(authRadiusVenueRepository, never()).findByRadiusIdAndTenantId(anyString(), anyString());
  }

  @Test
  public void testAddPskNetworkVenue_enableAuthRadiusAndMacAuth() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));
    networkVenue.getNetwork().setAuthRadius(authRadius);
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    clearInvocations(authRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals("authRadiusVenue-id",
        operations.get(0).getWlanVenue().getAuthenticationServerId());
    assertEquals("authRadiusVenue-id", wlanAuth.getId());
    assertFalse(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());

    verify(authRadiusVenueRepository, never()).findByRadiusIdAndTenantId(anyString(), anyString());
  }

  @Test
  public void testAddAaaNetworkVenue_enableAuthRadiusWithMultipleAuthRadiusVenues() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    Radius authRadius = new Radius("authRadius-id");
    Radius authRadius2 = new Radius("authRadius-id-2");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    AuthRadiusVenue authRadiusVenue2 = authRadiusVenue("authRadiusVenue-id-2", authRadius2, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue, authRadiusVenue2));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals("authRadiusVenue-id",
        operations.get(0).getWlanVenue().getAuthenticationServerId());
    assertEquals("authRadiusVenue-id", wlanAuth.getId());
    assertFalse(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());
  }

  @Test
  public void testAddNetworkVenue_enableAuthRadius_failedWithRadiusNotFound() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusVenues(emptyList());
    networkVenue.getNetwork().setAuthRadius(authRadius);

    Assertions.assertThrows(RuntimeException.class, ()-> builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges()));
  }

  @Test
  public void testAddNetworkVenue_enableAcctRadius() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    Radius acctRadius = new Radius("acctRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = accountingRadiusVenue("acctRadiusVenue-id", acctRadius, networkVenue.getVenue());
    acctRadius.setAccountingRadiusVenues(List.of(accountingRadiusVenue));
    networkVenue.getNetwork().setAccountingRadius(acctRadius);

    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(acctRadius.getId()));

    // Prerequisites for enabling accounting
    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusVenue-id", operations.get(0).getWlanVenue().getAccountingServerId());
    assertEquals("acctRadiusVenue-id", wlanAcct.getId());
    assertFalse(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());

    verify(accountingRadiusVenueRepository, never()).findByRadiusIdAndTenantId(anyString(), anyString());
  }

  @Test
  public void testAddAaaNetworkVenue_enableAcctRadiusWithMultipleAccountingRadiusVenues() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    Radius acctRadius = new Radius("acctRadius-id");
    Radius acctRadius2 = new Radius("acctRadius-id-2");
    AccountingRadiusVenue acctRadiusVenue = accountingRadiusVenue("acctRadiusVenue-id", acctRadius, networkVenue.getVenue());
    AccountingRadiusVenue acctRadiusVenue2 = accountingRadiusVenue("acctRadiusVenue-id-2", acctRadius2, networkVenue.getVenue());
    acctRadius.setAccountingRadiusVenues(List.of(acctRadiusVenue, acctRadiusVenue2));
    networkVenue.getNetwork().setAccountingRadius(acctRadius);

    doReturn(acctRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(acctRadius.getId()));

    // Prerequisites for enabling accounting
    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    doAnswer(invocation -> List.of(accountingRadiusVenue("acctRadiusVenue-id",
        new Radius(invocation.getArgument(0)), new Venue(networkVenue.getVenue().getId())))
    ).when(accountingRadiusVenueRepository).findByRadiusIdAndTenantId(acctRadius.getId(), TxCtxHolder.tenantId());

    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusVenue-id", operations.get(0).getWlanVenue().getAccountingServerId());
    assertEquals("acctRadiusVenue-id", wlanAcct.getId());
    assertFalse(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Test
  public void testAddNetworkVenue_enableAcctRadius_failedWithRadiusVenueNotFound() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    Radius acctRadius = new Radius("acctRadius-id");
    acctRadius.setAccountingRadiusVenues(emptyList());
    networkVenue.getNetwork().setAccountingRadius(acctRadius);

    // Prerequisites for enabling accounting
    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusVenues(
        List.of(authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue())));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    Assertions.assertThrows(RuntimeException.class, ()-> builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges()));
  }

  @Deprecated(forRemoval = true)
  @Test
  @FeatureFlag(disable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
  public void testAddNetworkVenue_enableCloudpathAcctRadius() {
    NetworkVenue networkVenue = createAaaNetworkVenue();

    CloudpathServer cloudpathServer = new CloudpathServer("cloudpathServer-id");
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.OnPremise);
    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);

    Radius acctRadius = new Radius("acctRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = accountingRadiusVenue("acctRadiusVenue-id", acctRadius, networkVenue.getVenue());
    acctRadius.setAccountingRadiusVenues(List.of(accountingRadiusVenue));
    networkVenue.getNetwork().setAccountingRadius(acctRadius);

    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(acctRadius.getId()));

    cloudpathServer.setAccountingRadius(acctRadius);

    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusVenue-id", operations.get(0).getWlanVenue().getAccountingServerId());
    assertEquals("acctRadiusVenue-id", wlanAcct.getId());
    assertFalse(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Test
  public void testAddPskNetworkVenue_enableAcctRadiusWithNonProxyMode() {
    var networkVenue = createPskNetworkVenue();
    var acctRadius = new Radius("acctRadius-id");
    var acctRadiusVenue = accountingRadiusVenue("acctRadiusVenue-id", acctRadius, networkVenue.getVenue());
    acctRadius.setAccountingRadiusVenues(List.of(acctRadiusVenue));
    networkVenue.getNetwork().setAccountingRadius(acctRadius);

    doReturn(acctRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(acctRadius.getId()));

    doAnswer(invocation -> List.of(accountingRadiusVenue("acctRadiusVenue-id",
        new Radius(invocation.getArgument(0)), new Venue(networkVenue.getVenue().getId())))
    ).when(accountingRadiusVenueRepository).findByRadiusIdAndTenantId(acctRadius.getId(), TxCtxHolder.tenantId());

    clearInvocations(accountingRadiusVenueRepository);

    var operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusVenue-id", operations.get(0).getWlanVenue().getAccountingServerId());
    assertEquals("acctRadiusVenue-id", wlanAcct.getId());
    assertFalse(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Test
  public void testAddOpenNetworkVenue_enableAcctRadiusWithProxyMode() {
    var networkVenue = createOpenNetworkVenue();
    var network = networkVenue.getNetwork();
    network.setEnableAccountingProxy(true);
    network.getWlan().setMacAddressAuthentication(false);

    var acctRadius = new Radius("acctRadius-id");
    networkVenue.getNetwork().setAccountingRadius(acctRadius);
    var acctRadiusService = new AccountingRadiusService("acctRadiusService-id");
    acctRadiusService.setAccountingRadiusProfile(
            accountingRadiusProfile("acctRadiusProfile-id", acctRadiusService));
    acctRadiusService.setRadius(acctRadius);
    acctRadius.setAccountingRadiusServices(List.of(acctRadiusService));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    var wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusService-id", wlanAcct.getId());
    assertTrue(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Test
  public void testAddGuestWorkflowNetworkVenue_enableAcctRadiusWithProxyMode() {
    var networkVenue = createPrivateGuestNetworkVenue();
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    var guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Workflow);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    networkVenue.getNetwork().setEnableAccountingProxy(true);

    var acctRadius = new Radius("acctRadius-id");
    networkVenue.getNetwork().setAccountingRadius(acctRadius);
    var acctRadiusService = new AccountingRadiusService("acctRadiusService-id");
    acctRadiusService.setRadius(acctRadius);
    acctRadius.setAccountingRadiusServices(List.of(acctRadiusService));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    var wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusService-id", wlanAcct.getId());
    assertTrue(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Test
  public void testAddNetworkVenue_enableWlanAuthProxy() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    networkVenue.getNetwork().setEnableAuthProxy(true);
    Radius authRadius = new Radius("authRadius-id");
    networkVenue.getNetwork().setAuthRadius(authRadius);
    AuthRadiusService radiusService = new AuthRadiusService("authRadiusService-id");
    AuthRadiusProfile profile = new AuthRadiusProfile("authRadiusProfile-id");
    profile.setAuthRadiusService(radiusService);
    radiusService.setAuthRadiusProfile(profile);
    authRadius.setAuthRadiusServices(List.of(radiusService));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAuthProxy wlanAuthProxy = operations.get(0).getWlanVenue().getProxyAuthAaa();
    assertEquals("authRadiusProfile-id", wlanAuthProxy.getId());
    assertTrue(wlanAuthProxy.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuthProxy.getType());
  }

  @Deprecated(forRemoval = true)
  @Test
  @FeatureFlag(disable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
  public void testAddOpenNetworkVenue_disableWlanAuthProxy_whenNullCloudpathServer() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    networkVenue.getNetwork().setEnableAuthProxy(true);
    CloudpathServer cloudpathServer = new CloudpathServer("cloudpathServer-id");
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.OnPremise);
    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertFalse(operations.get(0).getWlanVenue().hasProxyAuthAaa());
  }

  @Test
  public void testAddAaaNetworkVenue_enableWlanAcctProxy() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    networkVenue.getNetwork().setEnableAccountingProxy(true);

    Radius acctRadius = new Radius("acctRadius-id");
    networkVenue.getNetwork().setAccountingRadius(acctRadius);
    AccountingRadiusService radiusService = new AccountingRadiusService("acctRadiusService-id");
    radiusService.setAccountingRadiusProfile(
        accountingRadiusProfile("acctRadiusProfile-id", radiusService));
    acctRadius.setAccountingRadiusServices(List.of(radiusService));
    radiusService.setRadius(acctRadius);

    // Prerequisites for enabling accounting
    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAcctProxy wlanAcctProxy = operations.get(0).getWlanVenue().getProxyAcctAaa();
    assertEquals("acctRadiusProfile-id", wlanAcctProxy.getId());
    assertTrue(wlanAcctProxy.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcctProxy.getType());
    assertEquals(10, wlanAcctProxy.getInterimUpdateMin().getValue());
    assertFalse(wlanAcctProxy.getAccountingDelayEnabled().getValue());
  }

  @Test
  public void testAddOpenWithExternalMacAuthNetworkVenue_enableWlanAcctProxy() {
    var networkVenue = createOpenNetworkVenue();
    var network = (OpenNetwork) networkVenue.getNetwork();
    network.getWlan().setMacAddressAuthentication(true);
    network.setEnableAccountingProxy(true);

    // Prerequisites for enabling accounting
    var acctRadius = new Radius("acctRadius-id");
    networkVenue.getNetwork().setAccountingRadius(acctRadius);
    var radiusService = new AccountingRadiusService("acctRadiusService-id");
    radiusService.setAccountingRadiusProfile(
        accountingRadiusProfile("acctRadiusProfile-id", radiusService));
    acctRadius.setAccountingRadiusServices(List.of(radiusService));
    radiusService.setRadius(acctRadius);

    var operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    var wlanAcctProxy = operations.get(0).getWlanVenue().getProxyAcctAaa();
    assertEquals("acctRadiusProfile-id", wlanAcctProxy.getId());
    assertTrue(wlanAcctProxy.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcctProxy.getType());
    assertEquals(10, wlanAcctProxy.getInterimUpdateMin().getValue());
    assertFalse(wlanAcctProxy.getAccountingDelayEnabled().getValue());
  }

  @Deprecated(forRemoval = true)
  @Test
  public void testAddAaaNetworkVenue_proxyAccountingRadiusToCloudpathServer() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    networkVenue.getNetwork().setEnableAccountingProxy(false);

    CloudpathServer cloudpathServer = new CloudpathServer("cloudpathServer-id");
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.Cloud);
    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);

    Radius acctRadius = new Radius("acctRadius-id");
    networkVenue.getNetwork().setAccountingRadius(acctRadius);
    var accountingRadiusVenue = accountingRadiusVenue("acctRadiusVenue-id", acctRadius, networkVenue.getVenue());
    acctRadius.setAccountingRadiusVenues(List.of(accountingRadiusVenue));
    AccountingRadiusService accountingRadiusService = accountingRadiusService("acctRadiusService-id", acctRadius, cloudpathServer);
    acctRadius.setAccountingRadiusServices(List.of(accountingRadiusService));
    accountingRadiusService.setAccountingRadiusProfile(
        accountingRadiusProfile("acctRadiusProfile-id", accountingRadiusService));

    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(acctRadius.getId()));

    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusVenues(
        List.of(authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue())));
    AuthRadiusService authRadiusService = authRadiusService("authRadiusService-id", authRadius, cloudpathServer);
    authRadius.setAuthRadiusServices(List.of(authRadiusService));
    authRadiusService.setAuthRadiusProfile(
        authRadiusProfile("authRadiusProfile-id", authRadiusService));

    cloudpathServer.setAuthRadius(authRadius);
    cloudpathServer.setAccountingRadius(acctRadius);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanAcctProxy wlanAcctProxy = operations.get(0).getWlanVenue().getProxyAcctAaa();
    assertEquals("acctRadiusProfile-id", wlanAcctProxy.getId());
    assertTrue(wlanAcctProxy.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcctProxy.getType());
    assertEquals(10, wlanAcctProxy.getInterimUpdateMin().getValue());
    assertFalse(wlanAcctProxy.getAccountingDelayEnabled().getValue());
  }

  @Deprecated(forRemoval = true)
  @Test
  public void testAddAaaNetworkVenue_proxyAccountingRadiusToCloudpathServer_failedWithAcctCloudpathServerNotFound() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    networkVenue.getNetwork().setEnableAccountingProxy(false);

    CloudpathServer cloudpathServer = new CloudpathServer("cloudpathServer-id");
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.Cloud);
    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);

    Radius acctRadius = new Radius("acctRadius-id");
    networkVenue.getNetwork().setAccountingRadius(acctRadius);
    acctRadius.setAccountingRadiusVenues(
        List.of(accountingRadiusVenue("acctRadiusVenue-id", acctRadius, networkVenue.getVenue()))
    );
    // Remove the relationship
    AccountingRadiusService accountingRadiusService = accountingRadiusService("acctRadiusService-id", acctRadius, null, null);
    acctRadius.setAccountingRadiusServices(
        List.of(accountingRadiusService));
    accountingRadiusService.setAccountingRadiusProfile(
        accountingRadiusProfile("acctRadiusProfile-id", accountingRadiusService));

    // Prerequisites for enabling accounting
    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusVenues(
        List.of(authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue())));
    AuthRadiusService authRadiusService = authRadiusService("authRadiusService-id", acctRadius, cloudpathServer);
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService));
    authRadiusService.setAuthRadiusProfile(
        authRadiusProfile("authRadiusProfile-id", authRadiusService));

    cloudpathServer.setAuthRadius(authRadius);
    cloudpathServer.setAccountingRadius(acctRadius);

    Assertions.assertThrows(RuntimeException.class, ()-> builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges()));
  }

  @Test
  public void testAddNetworkVenue_enablePassphrase() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().setPassphrase("passphrase");
    networkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("passphrase", operations.get(0).getWlanVenue().getPassphrase());
  }

  @Test
  public void testAddNetworkVenue_enableSaePassphrase() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().setSaePassphrase("passphrase");
    networkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("passphrase", operations.get(0).getWlanVenue().getSaePassphrase().getValue());
  }

  @Test
  public void testAddNetworkVenue_enableWepHexKey() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().setWepHexKey("wepHexKey");
    networkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.WEP);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("wepHexKey", operations.get(0).getWlanVenue().getWepHexKey());
  }

  @Test
  public void testAddNetworkVenue_enableDnsProxy() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    WlanAdvancedCustomization wlanAdvancedCustomization = networkVenue.getNetwork()
        .getWlan().getAdvancedCustomization();
    wlanAdvancedCustomization.setDnsProxyEnabled(true);
    networkVenue.setVenueDnsProxy(new VenueDnsProxy("venueDnsProxy-id"));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("venueDnsProxy-id", operations.get(0).getWlanVenue()
        .getAdvancedCustomization().getDnsProxyId().getValue());
  }

  @Test
  public void testAddNetworkVenue_wlanAdvancedCustomization() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    WlanAdvancedCustomization wlanAdvancedCustomization = networkVenue.getNetwork()
        .getWlan().getAdvancedCustomization();
    wlanAdvancedCustomization.setClientIsolation(true);
    ClientIsolationAllowlist clientIsolationAllowlist = new ClientIsolationAllowlist(
        "clientIsolationAllowlist-id");
    networkVenue.setClientIsolationAllowlist(clientIsolationAllowlist);
    wlanAdvancedCustomization.setEnableJoinRSSIThreshold(true);
    wlanAdvancedCustomization.setJoinRSSIThreshold((short) 20);

    wlanAdvancedCustomization.setEnableTransientClientManagement(true);
    wlanAdvancedCustomization.setJoinWaitTime((short) 10);
    wlanAdvancedCustomization.setJoinExpireTime((short) 11);
    wlanAdvancedCustomization.setJoinWaitThreshold((short) 12);

    wlanAdvancedCustomization.setEnableOptimizedConnectivityExperience(true);
    wlanAdvancedCustomization.setBroadcastProbeResponseDelay((short) 13);
    wlanAdvancedCustomization.setRssiAssociationRejectionThreshold((short) 14);

    wlanAdvancedCustomization.setEnableArpRequestRateLimit(false);
    wlanAdvancedCustomization.setEnableDhcpRequestRateLimit(false);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("clientIsolationAllowlist-id", operations.get(0).getWlanVenue()
        .getAdvancedCustomization().getClientIsolationWhitelistId().getValue());
    assertEquals(20, operations.get(0).getWlanVenue()
        .getAdvancedCustomization().getJoinRSSIThreshold().getValue());
    assertEquals(10, operations.get(0).getWlanVenue()
        .getAdvancedCustomization().getJoinWaitTime().getValue());
    assertEquals(11, operations.get(0).getWlanVenue()
        .getAdvancedCustomization().getJoinExpireTime().getValue());
    assertEquals(12, operations.get(0).getWlanVenue()
        .getAdvancedCustomization().getJoinWaitThreshold().getValue());
    assertEquals(13, operations.get(0).getWlanVenue()
        .getAdvancedCustomization().getOceBroadcastProbeResponseDelay().getValue());
    assertEquals(14, operations.get(0).getWlanVenue()
        .getAdvancedCustomization().getOceRssiBasedAssociationRejectionThreshold().getValue());
  }

  @Test
  public void testAddNetworkVenue_enableRespectiveAccessControl() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setL2AclEnable(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setL3AclEnable(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setApplicationPolicyEnable(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setL2AclPolicy(new L2AclPolicy("l2-id"));
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setL3AclPolicy(new L3AclPolicy("l3-id"));
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setApplicationPolicy(new ApplicationPolicy("app-id"));
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setDevicePolicy(new DevicePolicy("device-id"));
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setUserDownlinkRateLimiting((short) 15);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setUserUplinkRateLimiting((short) 30);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("l2-id", operations.get(0).getWlanVenue().getL2ACLId().getValue());
    assertEquals("l3-id", operations.get(0).getWlanVenue().getL3ACLId().getValue());
    assertEquals("app-id", operations.get(0).getWlanVenue().getAppPolicyId().getValue());
    assertEquals("device-id", operations.get(0).getWlanVenue().getDevicePolicyId().getValue());
    assertEquals(15,
        operations.get(0).getWlanVenue().getFirewallDownlinkRateLimitingMbps().getValue());
    assertEquals("15", operations.get(0).getWlanVenue().getRatePerStaDownlink().getValue());
    assertEquals(30,
        operations.get(0).getWlanVenue().getFirewallUplinkRateLimitingMbps().getValue());
    assertEquals("30", operations.get(0).getWlanVenue().getRatePerStaUplink().getValue());
  }

  @Test
  public void testAddNetworkVenue_enableAccessControlProfile() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setRespectiveAccessControl(false);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization()
        .setAccessControlProfile(new AccessControlProfile("accessControlProfile-id"));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("accessControlProfile-id",
        operations.get(0).getWlanVenue().getFirewallProfileId().getValue());
  }

  @Test
  public void testAddNetworkVenue_enableMacAddressAuth() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setMacAuthMacFormat(MacAuthMacFormatEnum.Upper);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertTrue(operations.get(0).getWlanVenue().getMacAddressAuthentication()
        .getMacAuthEnabled().getValue());
    assertEquals(MacAuthMacFormat.Upper, operations.get(0).getWlanVenue()
        .getMacAddressAuthentication().getMacAuthMacFormat());
  }

  @Test
  public void testAddNetworkVenue_enableMfp() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    networkVenue.getNetwork().getWlan()
        .setManagementFrameProtection(ManagementFrameProtectionEnum.Optional);
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals(ManagementFrameProtection.Optional, operations.get(0).getWlanVenue()
        .getManagementFrameProtection());
  }

  @Test
  public void testAddNetworkVenue_disableWlanSchedule() {
    var networkVenue = createPskNetworkVenue();
    networkVenue.getTenant().setActivated(false);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(ScheduleType.AlwaysOff, operations.get(0).getWlanVenue().getSchedule().getType());
  }

  @Test
  public void testAddNetworkVenue_customizedWlanSchedule() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    NetworkVenueScheduler networkVenueScheduler = new NetworkVenueScheduler("scheduler-id");
    networkVenue.setScheduler(networkVenueScheduler);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals("scheduler-id", operations.get(0).getWlanVenue().getSchedule().getId());
    assertEquals(ScheduleType.Customized, operations.get(0).getWlanVenue().getSchedule().getType());
  }

  @Deprecated(forRemoval = true)
  @Test
  public void testAddOpenNetwork_withCloudpathServer() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().setCloudpathServer(new CloudpathServer());
    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusVenues(
        List.of(authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue())));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    List<Operation> operations = builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getMacAddressAuthentication().getMacAuthEnabled()
        .getValue());
    assertEquals(MacAuthMacFormat.Lower, operations.get(0).getWlanVenue()
        .getMacAddressAuthentication().getMacAuthMacFormat());
  }

  @Deprecated(forRemoval = true)
  @Test
  @FeatureFlag(disable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
  public void testAddDpskNetwork_withCloudpathServer() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    CloudpathServer cloudpathServer = new CloudpathServer();
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.Cloud);

    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);
    Radius authRadius = new Radius("authRadius-id");
    cloudpathServer.setAuthRadius(authRadius);
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService("authRadiusService-id", authRadius, cloudpathServer)));

    List<Operation> operations = builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals("authRadiusService-id",
        operations.get(0).getWlanVenue().getAuthenticationServerId());
    assertEquals("authRadiusService-id", wlanAuth.getId());
    assertTrue(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());
  }

  @Deprecated(forRemoval = true)
  @Test
  @FeatureFlag(disable = FlagNames.WIFI_ENG_SKIP_LEGACY_CLOUDPATH_TOGGLE)
  public void testAddDpskNetwork_withCloudpathServer_failedWithNoRadiusFound() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    CloudpathServer cloudpathServer = new CloudpathServer();
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.Cloud);

    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);
    Radius authRadius = new Radius("authRadius-id");
    cloudpathServer.setAuthRadius(authRadius);
    authRadius.setAuthRadiusServices(emptyList());

    Assertions.assertThrows(RuntimeException.class, ()-> builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges()));
  }

  @Test
  public void testAddDpskNetwork_withNonProxyAAA() {
    NetworkVenue networkVenue = createDpskNetworkVenue();

    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));

    Radius acctRadius = new Radius("acctRadius-id");
    AccountingRadiusService radiusService = accountingRadiusService("acctRadiusService-id", acctRadius);
    radiusService.setAccountingRadiusProfile(
        accountingRadiusProfile("acctRadiusProfile-id", radiusService));
    acctRadius.setAccountingRadiusServices(List.of(radiusService));

    networkVenue.getNetwork().setAuthRadius(authRadius);
    networkVenue.getNetwork().setEnableAuthProxy(false);
    networkVenue.getNetwork().setAccountingRadius(acctRadius);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    List<Operation> operations = builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals("authRadiusVenue-id",
            operations.get(0).getWlanVenue().getAuthenticationServerId());
    assertEquals("authRadiusVenue-id", wlanAuth.getId());
    assertFalse(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());

    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusService-id", operations.get(0).getWlanVenue().getAccountingServerId());
    assertEquals("acctRadiusService-id", wlanAcct.getId());
    assertTrue(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Test
  public void testAddDpskNetwork_withProxyAAA() {
    NetworkVenue networkVenue = createDpskNetworkVenue();

    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusServices(
            List.of(authRadiusService("authRadiusService-id", authRadius, null, null)));

    Radius acctRadius = new Radius("acctRadius-id");
    AccountingRadiusService radiusService = accountingRadiusService("acctRadiusService-id", acctRadius);
    radiusService.setAccountingRadiusProfile(
        accountingRadiusProfile("acctRadiusProfile-id", radiusService));
    acctRadius.setAccountingRadiusServices(List.of(radiusService));

    networkVenue.getNetwork().setAuthRadius(authRadius);
    networkVenue.getNetwork().setAccountingRadius(acctRadius);
    networkVenue.getNetwork().setEnableAuthProxy(true);

    List<Operation> operations = builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals("authRadiusService-id",
            operations.get(0).getWlanVenue().getAuthenticationServerId());
    assertEquals("authRadiusService-id", wlanAuth.getId());
    assertTrue(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());

    WlanAcct wlanAcct = operations.get(0).getWlanVenue().getAcctAaa();
    assertEquals("acctRadiusService-id", operations.get(0).getWlanVenue().getAccountingServerId());
    assertEquals("acctRadiusService-id", wlanAcct.getId());
    assertTrue(wlanAcct.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAcct.getType());
    assertEquals(10, wlanAcct.getInterimUpdateMin().getValue());
    assertFalse(wlanAcct.getAccountingDelayEnabled().getValue());
  }

  @Deprecated(forRemoval = true)
  @Test
  public void testAddOpenNetworkVenue_enableWlanAuthProxy_failedWithCloudpathServerNotFound() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().setEnableAuthProxy(true);

    CloudpathServer cloudpathServer = new CloudpathServer();
    cloudpathServer.setDeploymentType(CloudpathDeploymentTypeEnum.Cloud);

    networkVenue.getNetwork().setCloudpathServer(cloudpathServer);
    Radius authRadius = new Radius("authRadius-id");
    cloudpathServer.setAuthRadius(authRadius);
    authRadius.setAuthRadiusServices(
        List.of(authRadiusService("authRadiusService-id", authRadius, null, null)));

    Assertions.assertThrows(RuntimeException.class, ()-> builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges()));
  }

  @Test
  public void testAddOpenNetworkVenue_macAuthEnabledAndProxy_thenDisableAaaVlanOverride() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);
    networkVenue.getNetwork().setEnableAuthProxy(true);

    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusService radiusService = authRadiusService("authRadiusService-id", authRadius, null, null);
    authRadius.setAuthRadiusServices(List.of(radiusService));
    radiusService.setAuthRadiusProfile(authRadiusProfile("authRadiusProfile-id", radiusService));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    List<Operation> operations = builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    WlanAuthProxy wlanAuthProxy = operations.get(0).getWlanVenue().getProxyAuthAaa();
    assertEquals("authRadiusProfile-id", wlanAuthProxy.getId());
    assertTrue(wlanAuthProxy.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuthProxy.getType());

    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getMacAddressAuthentication().getMacAuthEnabled()
            .getValue());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().hasEnableAaaVlanOverride());
  }

  @Test
  public void testAddOpenNetworkVenue_macAuthEnabledAndProxy_thenEnableAaaVlanOverride() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);
    networkVenue.getNetwork().setEnableAuthProxy(true);

    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusService radiusService = authRadiusService("authRadiusService-id", authRadius, null, null);
    authRadius.setAuthRadiusServices(List.of(radiusService));
    radiusService.setAuthRadiusProfile(authRadiusProfile("authRadiusProfile-id", radiusService));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    List<Operation> operations = builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    WlanAuthProxy wlanAuthProxy = operations.get(0).getWlanVenue().getProxyAuthAaa();
    assertEquals("authRadiusProfile-id", wlanAuthProxy.getId());
    assertTrue(wlanAuthProxy.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuthProxy.getType());

    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getMacAddressAuthentication().getMacAuthEnabled()
      .getValue());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getEnableAaaVlanOverride().getValue());
  }

  @Test
  public void testAddOpenNetworkVenue_macAuthDisabled_thenDisableAaaVlanOverride() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);

    List<Operation> operations = builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());

    assertFalse(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertFalse(operations.get(0).getWlanVenue().getMacAddressAuthentication().getMacAuthEnabled()
      .getValue());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().getEnableAaaVlanOverride().getValue());
  }

  @Test
  public void testAddOpenNetworkVenue_macAuthEnabledAndNonProxy_thenEnableAaaVlanOverride() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(false);
    networkVenue.getNetwork().setEnableAuthProxy(false);

    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));
    networkVenue.getNetwork().setAuthRadius(authRadius);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    List<Operation> operations = builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals("authRadiusVenue-id", wlanAuth.getId());
    assertFalse(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());

    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getMacAddressAuthentication().getMacAuthEnabled()
      .getValue());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().getEnableAaaVlanOverride().getValue());
  }

  @Test
  public void testAddWisprGuestNetworkVenue_macBypassEnabledAndPredefinedAaa_thenEnableAaaVlanOverride() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(false);
    wisprPage.setAaaName("aaa");
    guestPortal.setWisprPage(wisprPage);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getEnableAaaVlanOverride().getValue());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());

    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals(TxCtxHolder.get().getTenant() + "-Auth-aaa", wlanAuth.getId());
    assertTrue(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());
  }

  @Test
  public void testAddWisprGuestNetworkVenue_macBypassEnabledAndCustomExternalProvider_thenEnableAaaVlanOverride() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);

    Radius authRadius = new Radius("authRadius-id");
    authRadius.setAuthRadiusServices(
            List.of(authRadiusService("authRadiusService-id", authRadius, guestPortal)));
    wisprPage.setAuthRadius(authRadius);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getEnableAaaVlanOverride().getValue());
    assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());

    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals("authRadiusService-id", wlanAuth.getId());
    assertTrue(wlanAuth.getThroughController());
    assertEquals(AaaType.RADIUS, wlanAuth.getType());
  }

  @Test
  public void testAddGuestPassGuestNetworkVenue_macBypassEnabled_thenDisableAaaVlanOverride() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setBypassCNA(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);
    networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
    GuestPortal guestPortal = new GuestPortal("guestProtal-id");
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.GuestPass);
    ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, operations.get(0).getWlanVenue().getWlanType());
    assertEquals("GUEST", operations.get(0).getWlanVenue().getNetworkType().getValue());
    assertTrue(operations.get(0).getWlanVenue().getBypassCPUsingMacAddressAuthentication());
    assertTrue(operations.get(0).getWlanVenue().getBypassCNA());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().getEnableAaaVlanOverride().getValue());

    WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
    assertEquals(AaaType.RADIUS, wlanAuth.getType());
  }

  @Test
  public void testAddNetworkVenue_nonePhyTypeConstraint() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    WlanRadioCustomization wlanRadioCustomization = new WlanRadioCustomization();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setRadioCustomization(wlanRadioCustomization);
    wlanRadioCustomization.setPhyTypeConstraint(PhyTypeConstraintEnum.NONE);

    List<Operation> operations = builder.build( new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.PhyTypeConstraintEnum.None,
        operations.get(0).getWlanVenue().getAdvancedCustomization().getRadioCustomization().getPhyTypeConstraint());
  }

  @Test
  public void testBuildOperation_networkVenueChanged() {
    doReturn(true).when(builder).hasModification(any(), any(), eq(false));
    NetworkVenue networkVenue = createOpenNetworkVenue();

    List<Operation> operations = builder.build( new ModifiedTxEntity<>(networkVenue, Collections.emptySet()), emptyTxChanges());
    assertNotNull(operations);
  }

  @Test
  public void testBuildOperation_wlanSettingsChanged() {
    doReturn(false).when(builder).hasModification(any(), any(), eq(false));
    NetworkVenue networkVenue = createOpenNetworkVenue();

    TxChanges txChanges = new TxChangesImpl(null);

    txChanges.add(new Wlan(randomId()), Collections.emptySet(), EntityAction.MODIFY);

    List<Operation> operations = builder.build( new ModifiedTxEntity<>(networkVenue, Collections.emptySet()), emptyTxChanges());
    assertNotNull(operations);
  }

  @Test
  public void testBuildOperation_aaaSettingsChanged() {
    testBuildOperation_settingsChanged(AAANetwork.Fields.AUTHRADIUS);
    testBuildOperation_settingsChanged(AAANetwork.Fields.ACCOUNTINGRADIUS);
    testBuildOperation_settingsChanged(Network.Fields.CLOUDPATHSERVER);
    testBuildOperation_settingsChanged(Network.Fields.ENABLEAUTHPROXY);
    testBuildOperation_settingsChanged(Network.Fields.ENABLEACCOUNTINGPROXY);
  }

  @Test
  public void testFirewallWlanSpecificEnabled() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    WlanAdvancedCustomization wlanAdvancedCustomization = networkVenue.getNetwork().getWlan().getAdvancedCustomization();
    wlanAdvancedCustomization.setRespectiveAccessControl(true);

    assertTrue(builder.firewallWlanSpecificEnabled(networkVenue));

    wlanAdvancedCustomization.setRespectiveAccessControl(false);
    assertTrue(builder.firewallWlanSpecificEnabled(networkVenue));

    wlanAdvancedCustomization.setRespectiveAccessControl(false);
    wlanAdvancedCustomization.setAccessControlProfile(new AccessControlProfile("acp-id"));
    assertFalse(builder.firewallWlanSpecificEnabled(networkVenue));
  }

  @Test
  public void testRadiusOptions_enableFF() {
    NetworkVenue networkVenue1 = createPskNetworkVenue();
    NetworkVenue networkVenue2 = createPublicGuestNetworkVenue();
    networkVenue2.setVenuePortal(new VenuePortal("venuePortal-id"));
    ((GuestNetwork)networkVenue2.getNetwork()).setGuestPortal(GuestNetworkTestFixture.guestPortal());
    ((GuestNetwork)networkVenue2.getNetwork()).getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    ((GuestNetwork)networkVenue2.getNetwork()).getGuestPortal().setWisprPage(GuestNetworkTestFixture.wisprPageAlwaysAccept());

    WlanAdvancedCustomization wlanAdvancedCustomization1 = networkVenue1.getNetwork()
        .getWlan().getAdvancedCustomization();
    WlanAdvancedCustomization wlanAdvancedCustomization2 = networkVenue2.getNetwork()
        .getWlan().getAdvancedCustomization();
    RadiusOptions radiusOptions = new RadiusOptions();
    // check default value
    assertEquals(NasIdTypeEnum.BSSID, radiusOptions.getNasIdType());
    assertEquals(NasIdDelimiterEnum.DASH, radiusOptions.getNasIdDelimiter());
    assertEquals((Integer)3, radiusOptions.getNasRequestTimeoutSec());
    assertEquals((Integer)2, radiusOptions.getNasMaxRetry());
    assertEquals((Integer)5, radiusOptions.getNasReconnectPrimaryMin());
    assertNull(radiusOptions.getCalledStationIdType());
    assertNull(radiusOptions.getUserDefinedNasId());

    // modify RADIUS options
    radiusOptions.setNasIdType(NasIdTypeEnum.AP_MAC);
    radiusOptions.setNasIdDelimiter(NasIdDelimiterEnum.COLON);
    radiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_GROUP);
    radiusOptions.setNasRequestTimeoutSec(4);
    radiusOptions.setNasMaxRetry(3);
    radiusOptions.setNasReconnectPrimaryMin(6);
    radiusOptions.setSingleSessionIdAccounting(true);
    wlanAdvancedCustomization1.setRadiusOptions(radiusOptions);
    wlanAdvancedCustomization2.setRadiusOptions(radiusOptions);

    testModifyRadiusOptions_radiusUnavailable(networkVenue1);
    testModifyRadiusOptions_radiusUnavailable(networkVenue2);
    testModifyRadiusOptions_radiusAvailable(networkVenue1);
    testModifyRadiusOptions_radiusAvailableAlwaysAccept(networkVenue2);
    testModifyRadiusOptions_withNullRadiusOptions(networkVenue1);
    testModifyRadiusOptions_withVenueConfig(networkVenue1);
  }


  @Tag("VxLanTunnelFeatureTest")
  @Test
  void testNetworkVenue_enablePinVxLanTunnel() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    PinProfileNetworkMapping networkMapping =
        createPinProfileNetworkMapping(networkVenue);
    networkVenue.getNetwork().setPinProfileNetworkMappings(List.of(networkMapping));
    doReturn(networkMapping).when(pinProfileNetworkMappingRepository)
        .findByTenantIdAndNetworkIdAndPinProfileRegularSettingVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());

    assertThat(builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges()))
        .isNotEmpty()
        .singleElement()
        .satisfies(op -> {
          assertThat(op.getAction()).isEqualTo(Action.ADD);
          assertThat(op.getId()).isEqualTo(networkVenue.getId());
          assertThat(op.getConfigCase()).isEqualTo(ConfigCase.WLANVENUE);
          assertThat(op.hasWlanVenue()).isTrue();
          assertThat(op.getWlanVenue()).isNotNull()
              .satisfies(wlanVenue -> {
                assertThat(wlanVenue.getVenueId())
                    .isEqualTo(networkMapping.getPinProfileRegularSetting().getVenue().getId());
                assertThat(wlanVenue.getWlanId())
                    .isEqualTo(networkMapping.getNetwork().getId());
                assertThat(wlanVenue.getVxlanTunnelProfileId())
                    .isEqualTo(networkMapping.getPinProfileRegularSetting().getTunnelProfile().getId());
                assertThat(wlanVenue.getDpTunnelDhcpEnabled()).isTrue();
                assertThat(wlanVenue.getDpTunnelNatEnabled()).isTrue();
                assertThat(wlanVenue.getIpForward()).isEqualTo(StringValue.of("LBO"));
                assertThat(wlanVenue.getAdvancedCustomization()).isNotNull()
                    .satisfies(advancedCustomization -> {
                      assertThat(advancedCustomization.getClientIsolationUnicast().getValue()).isFalse();
                      assertThat(advancedCustomization.getClientIsolationMulticast().getValue()).isFalse();
                      assertThat(advancedCustomization.getEnableAaaVlanOverride().getValue()).isTrue();
                    });
                assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
                assertThat(wlanVenue.getIsNotifiedEdge()).isTrue();
                assertThat(wlanVenue.getEdgeConnectResourceId()).isEqualTo(StringValue.of(
                    networkMapping.getPinProfileRegularSetting().getPinProfile()
                        .getEdgeConnectResource().getId()));
              });
        });
  }

  @Tag("VxLanTunnelFeatureTest")
  @Test
  void testNetworkVenue_disablePinVxLanTunnel() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    PinProfileNetworkMapping networkMapping =
        createPinProfileNetworkMapping(networkVenue);
    doReturn(null).when(pinProfileNetworkMappingRepository)
        .findByTenantIdAndNetworkIdAndPinProfileRegularSettingVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());

    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new DeletedTxEntity<>(networkMapping)))
        .when(txChanges).getDeletedEntities();

    assertThat(builder.build(new ModifiedTxEntity<>(networkVenue, Collections.emptySet()), txChanges))
        .isNotEmpty()
        .singleElement()
        .satisfies(op -> {
          assertThat(op.getAction()).isEqualTo(Action.MODIFY);
          assertThat(op.getId()).isEqualTo(networkVenue.getId());
          assertThat(op.getConfigCase()).isEqualTo(ConfigCase.WLANVENUE);
          assertThat(op.hasWlanVenue()).isTrue();
          assertThat(op.getWlanVenue()).isNotNull()
              .satisfies(wlanVenue -> {
                assertThat(wlanVenue.getVenueId())
                    .isEqualTo(networkMapping.getPinProfileRegularSetting().getVenue().getId());
                assertThat(wlanVenue.getWlanId())
                    .isEqualTo(networkMapping.getNetwork().getId());
                assertThat(wlanVenue.getVxlanTunnelProfileId()).isNullOrEmpty();
                assertThat(wlanVenue.getDpTunnelDhcpEnabled()).isFalse();
                assertThat(wlanVenue.getDpTunnelNatEnabled()).isFalse();
                assertThat(wlanVenue.getIpForward()).isEqualTo(StringValue.of("LBOAP"));
                assertThat(wlanVenue.getAdvancedCustomization()).isNotNull()
                    .satisfies(advancedCustomization -> {
                      assertThat(advancedCustomization.getClientIsolationUnicast().getValue()).isFalse();
                      assertThat(advancedCustomization.getClientIsolationMulticast().getValue()).isFalse();
                      assertThat(advancedCustomization.getEnableAaaVlanOverride().getValue()).isTrue();
                    });
                assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
                assertThat(wlanVenue.getIsNotifiedEdge()).isTrue();
                assertThat(wlanVenue.hasEdgeConnectResourceId()).isFalse();
              });
        });
  }

  @Tag("VxLanTunnelFeatureTest")
  @Test
  void testNetworkVenue_withPinVxLanTunnel() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    PinProfileNetworkMapping networkMapping = createPinProfileNetworkMapping(networkVenue);
    networkVenue.getNetwork().setNetworkVenues(List.of(networkVenue));
    networkVenue.getNetwork().setPinProfileNetworkMappings(List.of(networkMapping));
    doReturn(Optional.of(networkVenue))
        .when(networkVenueRepository).findByTenantIdAndNetworkIdAndVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());
    doReturn(networkMapping)
        .when(pinProfileNetworkMappingRepository).findByTenantIdAndNetworkIdAndPinProfileRegularSettingVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());

    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new ModifiedTxEntity<>(networkMapping, Collections.emptySet())))
        .when(txChanges).getModifiedEntities();

    assertThat(builder.build(new ModifiedTxEntity<>(networkVenue, Collections.emptySet()), txChanges))
        .isNotEmpty()
        .singleElement()
        .satisfies(op -> {
          assertThat(op.getAction()).isEqualTo(Action.MODIFY);
          assertThat(op.getId()).isEqualTo(networkVenue.getId());
          assertThat(op.getConfigCase()).isEqualTo(ConfigCase.WLANVENUE);
          assertThat(op.hasWlanVenue()).isTrue();
          assertThat(op.getWlanVenue()).isNotNull()
              .satisfies(wlanVenue -> {
                assertThat(wlanVenue.getVenueId())
                    .isEqualTo(networkMapping.getPinProfileRegularSetting().getVenue().getId());
                assertThat(wlanVenue.getWlanId())
                    .isEqualTo(networkMapping.getNetwork().getId());
                assertThat(wlanVenue.getApWlanId()).isEqualTo(StringValue.of("1"));
                assertThat(wlanVenue.getVxlanTunnelProfileId())
                    .isEqualTo(networkMapping.getPinProfileRegularSetting().getTunnelProfile().getId());
                assertThat(wlanVenue.getDpTunnelDhcpEnabled()).isTrue();
                assertThat(wlanVenue.getDpTunnelNatEnabled()).isTrue();
                assertThat(wlanVenue.getIpForward()).isEqualTo(StringValue.of("LBO"));
                assertThat(wlanVenue.getAdvancedCustomization()).isNotNull()
                    .satisfies(advancedCustomization -> {
                      assertThat(advancedCustomization.getClientIsolationUnicast().getValue()).isFalse();
                      assertThat(advancedCustomization.getClientIsolationMulticast().getValue()).isFalse();
                      assertThat(advancedCustomization.getEnableAaaVlanOverride().getValue()).isTrue();
                    });
                assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
                assertThat(wlanVenue.getIsNotifiedEdge()).isTrue();
                assertThat(wlanVenue.getEdgeConnectResourceId()).isEqualTo(StringValue.of(
                    networkMapping.getPinProfileRegularSetting().getPinProfile()
                        .getEdgeConnectResource().getId()));
              });
        });

    networkVenue.getNetwork().setPinProfileNetworkMappings(null);
    doReturn(null)
        .when(pinProfileNetworkMappingRepository).findByTenantIdAndNetworkIdAndPinProfileRegularSettingVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());

    doReturn(List.of(new DeletedTxEntity<>(networkMapping)))
        .when(txChanges).getDeletedEntities();

    assertThat(builder.build(new ModifiedTxEntity<>(networkVenue, Collections.emptySet()), txChanges))
        .isNotEmpty()
        .singleElement()
        .satisfies(op -> {
          assertThat(op.getAction()).isEqualTo(Action.MODIFY);
          assertThat(op.getId()).isEqualTo(networkVenue.getId());
          assertThat(op.getConfigCase()).isEqualTo(ConfigCase.WLANVENUE);
          assertThat(op.hasWlanVenue()).isTrue();
          assertThat(op.getWlanVenue()).isNotNull()
              .satisfies(wlanVenue -> {
                assertThat(wlanVenue.getVenueId())
                    .isEqualTo(networkMapping.getPinProfileRegularSetting().getVenue().getId());
                assertThat(wlanVenue.getWlanId())
                    .isEqualTo(networkMapping.getNetwork().getId());
                assertThat(wlanVenue.getApWlanId()).isEqualTo(StringValue.of("1"));
                assertThat(wlanVenue.getVxlanTunnelProfileId()).isNullOrEmpty();
                assertThat(wlanVenue.getDpTunnelDhcpEnabled()).isFalse();
                assertThat(wlanVenue.getDpTunnelNatEnabled()).isFalse();
                assertThat(wlanVenue.getIpForward()).isEqualTo(StringValue.of("LBOAP"));
                assertThat(wlanVenue.getAdvancedCustomization()).isNotNull()
                    .satisfies(advancedCustomization -> {
                      assertThat(advancedCustomization.getClientIsolationUnicast().getValue()).isFalse();
                      assertThat(advancedCustomization.getClientIsolationMulticast().getValue()).isFalse();
                      assertThat(advancedCustomization.getEnableAaaVlanOverride().getValue()).isTrue();
                    });
                assertThat(wlanVenue.getCentralizedForwardingEnabled()).isFalse();
                assertThat(wlanVenue.getIsNotifiedEdge()).isTrue();
                assertThat(wlanVenue.hasEdgeConnectResourceId()).isFalse();
              });
        });
  }

  @Tag("VxLanTunnelFeatureTest")
  @Test
  public void testNetworkVenue_enableSdLanVxLanTunnel() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    SdLanProfileNetworkMapping networkMapping =
        createSdLanProfileNetworkMapping(networkVenue);
    networkVenue.getNetwork().setSdLanProfileNetworkMappings(List.of(networkMapping));
    doReturn(networkMapping).when(sdLanProfileNetworkMappingRepository)
        .findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals(networkMapping.getSdLanProfileRegularSetting().getTunnelProfile().getId(),
        operations.get(0).getWlanVenue().getVxlanTunnelProfileId());
    assertTrue(operations.get(0).getWlanVenue().getCentralizedForwardingEnabled());
    assertTrue(operations.get(0).getWlanVenue().getIsNotifiedEdge());
    assertEquals(networkMapping.getSdLanProfileRegularSetting().getSdLanProfile().getEdgeConnectResource().getId(),
        operations.get(0).getWlanVenue().getEdgeConnectResourceId().getValue());
  }

  @Tag("VxLanTunnelFeatureTest")
  @Test
  public void testNetworkVenue_disableSdLanVxLanTunnel() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    SdLanProfileNetworkMapping networkMapping =
        createSdLanProfileNetworkMapping(networkVenue);
    doReturn(null).when(sdLanProfileNetworkMappingRepository)
        .findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());

    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new DeletedTxEntity<>(networkMapping)))
        .when(txChanges).getDeletedEntities();

    List<Operation> operations = builder.build(new ModifiedTxEntity<>(networkVenue, Collections.emptySet()), txChanges);

    assertEquals(1, operations.size());
    assertEquals(Action.MODIFY, operations.get(0).getAction());
    assertEquals(StringUtils.EMPTY, operations.get(0).getWlanVenue().getVxlanTunnelProfileId());
    assertFalse(operations.get(0).getWlanVenue().getCentralizedForwardingEnabled());
    assertTrue(operations.get(0).getWlanVenue().getIsNotifiedEdge());
    assertFalse(operations.get(0).getWlanVenue().hasEdgeConnectResourceId());
  }

  @Tag("VxLanTunnelFeatureTest")
  @Test
  public void testNetworkVenue_withSdLanVxLanTunnel() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    SdLanProfileNetworkMapping networkMapping = createSdLanProfileNetworkMapping(networkVenue);
    networkVenue.getNetwork().setNetworkVenues(List.of(networkVenue));
    networkVenue.getNetwork().setSdLanProfileNetworkMappings(List.of(networkMapping));
    doReturn(Optional.of(networkVenue))
        .when(networkVenueRepository).findByTenantIdAndNetworkIdAndVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());
    doReturn(networkMapping)
        .when(sdLanProfileNetworkMappingRepository).findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());

    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new ModifiedTxEntity<>(networkMapping, Collections.emptySet())))
        .when(txChanges).getModifiedEntities();

    List<Operation> operations = builder.build(new ModifiedTxEntity<>(networkVenue, Collections.emptySet()), txChanges);

    assertEquals(1, operations.size());
    assertEquals(Action.MODIFY, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(networkMapping.getSdLanProfileRegularSetting().getTunnelProfile().getId(),
        operations.get(0).getWlanVenue().getVxlanTunnelProfileId());
    assertTrue(operations.get(0).getWlanVenue().getCentralizedForwardingEnabled());
    assertTrue(operations.get(0).getWlanVenue().getIsNotifiedEdge());
    assertEquals(networkMapping.getSdLanProfileRegularSetting().getSdLanProfile().getEdgeConnectResource().getId(),
        operations.get(0).getWlanVenue().getEdgeConnectResourceId().getValue());

    networkVenue.getNetwork().setSdLanProfileNetworkMappings(null);
    doReturn(null)
        .when(sdLanProfileNetworkMappingRepository).findByTenantIdAndNetworkIdAndSdLanProfileRegularSettingVenueId(
            eq(txCtxExtension.getTenantId()), anyString(), anyString());

    doReturn(List.of(new DeletedTxEntity<>(networkMapping)))
        .when(txChanges).getDeletedEntities();
    operations = builder.build(new ModifiedTxEntity<>(networkVenue, Collections.emptySet()), txChanges);

    assertEquals(1, operations.size());
    assertEquals(Action.MODIFY, operations.get(0).getAction());
    assertEquals("1", operations.get(0).getWlanVenue().getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(StringUtils.EMPTY, operations.get(0).getWlanVenue().getVxlanTunnelProfileId());
    assertFalse(operations.get(0).getWlanVenue().getCentralizedForwardingEnabled());
    assertTrue(operations.get(0).getWlanVenue().getIsNotifiedEdge());
    assertFalse(operations.get(0).getWlanVenue().hasEdgeConnectResourceId());
  }

  @Test
  public void testGtkReKey() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableGtkRekey(true);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(e -> e.hasWlanVenue())
        .hasSize(1)
        .first()
        .extracting(Operation::getWlanVenue)
        .extracting(WlanVenue::getAdvancedCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.WlanAdvancedCustomization::getGtkReKeyEnabled)
        .extracting(BoolValue::getValue)
        .isEqualTo(true);
  }

  @Test
  public void testApHostNameAdvertisement() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableApHostNameAdvertisement(true);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(e -> e.hasWlanVenue())
        .hasSize(1)
        .first()
        .extracting(Operation::getWlanVenue)
        .extracting(WlanVenue::getAdvancedCustomization)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.WlanAdvancedCustomization::getApHostNameAdvertisementEnabled)
        .extracting(BoolValue::getValue)
        .isEqualTo(true);
  }

  @Test
  public void testMulticastRateLimiting() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableMulticastUplinkRateLimiting(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableMulticastDownlinkRateLimiting(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setMulticastUplinkRateLimiting(100);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setMulticastDownlinkRateLimiting(6);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableMulticastUplinkRateLimiting6G(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableMulticastDownlinkRateLimiting6G(true);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(e -> e.hasWlanVenue())
        .hasSize(1)
        .first()
        .extracting(Operation::getWlanVenue)
        .matches(e -> e.getMulticastRateLimitUplinkRl().getValue() == 100)
        .matches(e -> e.getMulticastRateLimitDownlinkRl().getValue() == 6)
        .matches(e -> e.getMulticastRateLimit6GDownlinkEnabled().getValue())
        .matches(e -> e.getMulticastRateLimit6GUplinkEnabled().getValue());
  }

  @Test
  public void testMulticastRateLimiting_notEnabled() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableMulticastUplinkRateLimiting(false);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableMulticastDownlinkRateLimiting(false);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setMulticastUplinkRateLimiting(100);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setMulticastDownlinkRateLimiting(6);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableMulticastUplinkRateLimiting6G(false);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setEnableMulticastDownlinkRateLimiting6G(false);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(e -> e.hasWlanVenue())
        .hasSize(1)
        .first()
        .extracting(Operation::getWlanVenue)
        .matches(e -> e.hasMulticastRateLimitUplinkRl() == true)
        .matches(e -> e.getMulticastRateLimitUplinkRl().getValue() == 0)
        .matches(e -> e.hasMulticastRateLimitDownlinkRl() == true)
        .matches(e -> e.getMulticastRateLimitDownlinkRl().getValue() == 0)
        .matches(e -> e.getMulticastRateLimit6GDownlinkEnabled().getValue() == false)
        .matches(e -> e.getMulticastRateLimit6GUplinkEnabled().getValue() == false);
  }

  @Test
  @FeatureFlag(enable = WIFI_EDA_WPA3_DSAE_TOGGLE)
  public void testCreateDsaeNetwork_ffIsEnable() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    var dpskNetwork = (DpskNetwork) networkVenue.getNetwork();
    dpskNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
    dpskNetwork.setIsDsaeServiceNetwork(true);
    dpskNetwork.setDsaeNetworkPairId("id");
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(DynamicSaeEnum.DSAE_EXTERNAL, operations.get(0).getWlanVenue().getDynamicSae());
    assertEquals(true, operations.get(0).getWlanVenue().getTransitionDisable());
  }

  @Test
  @FeatureFlag(enable = WIFI_EDA_WPA3_DSAE_TOGGLE)
  public void testCreateDsaeOnboardNetwork_ffIsEnable() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    var dpskNetwork = (DpskNetwork) networkVenue.getNetwork();
    dpskNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
    dpskNetwork.setIsDsaeServiceNetwork(false);
    dpskNetwork.setDsaeNetworkPairId("id");
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertNull(operations);
  }

  private void testModifyRadiusOptions_radiusUnavailable(NetworkVenue networkVenue) {
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    com.ruckus.acx.ddccm.protobuf.wifi.RadiusOptions result = operations.get(0).getWlanVenue().getRadiusOptions();
    assertFalse(result.hasNasIdType());
    assertFalse(result.hasUserDefinedNasId());
    assertFalse(result.hasNasIdDelimiter());
    assertEquals(CalledStaIdType.CalledStaIdType_UNSET, result.getCalledStaIdType());
    assertEquals(Int32Value.of(0), result.getAuthenticationRequestTimeout());
    assertEquals(Int32Value.of(0), result.getAuthenticationMaxRetry());
    assertEquals(Int32Value.of(0), result.getAuthenticationRetryPrimaryInterval());
    assertEquals(Int32Value.of(0), result.getAccountingRequestTimeout());
    assertEquals(Int32Value.of(0), result.getAccountingMaxRetry());
    assertEquals(Int32Value.of(0), result.getAccountingRetryPrimaryInterval());
    assertFalse(result.hasSingleSessionIdAcctEnabled());
  }

  private void testModifyRadiusOptions_radiusAvailable(NetworkVenue networkVenue) {
    Radius authRadius = new Radius("authRadius-id");
    AuthRadiusVenue authRadiusVenue = authRadiusVenue("authRadiusVenue-id", authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));
    networkVenue.getNetwork().setAuthRadius(authRadius);
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);

    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
        eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    com.ruckus.acx.ddccm.protobuf.wifi.RadiusOptions result = operations.get(0).getWlanVenue().getRadiusOptions();
    assertEquals(StringValue.of("AP_MAC"), result.getNasIdType());
    assertEquals(CalledStaIdType.CalledStaIdType_AP_GROUP, result.getCalledStaIdType());
    assertEquals(StringValue.of("COLON"), result.getNasIdDelimiter());
    assertEquals(Int32Value.of(4), result.getAuthenticationRequestTimeout());
    assertEquals(Int32Value.of(3), result.getAuthenticationMaxRetry());
    assertEquals(Int32Value.of(6), result.getAuthenticationRetryPrimaryInterval());
    assertEquals(Int32Value.of(4), result.getAccountingRequestTimeout());
    assertEquals(Int32Value.of(3), result.getAccountingMaxRetry());
    assertEquals(Int32Value.of(6), result.getAccountingRetryPrimaryInterval());
    assertEquals(BoolValue.of(true), result.getSingleSessionIdAcctEnabled());
    assertFalse(result.hasUserDefinedNasId());
  }

  private void testModifyRadiusOptions_radiusAvailableAlwaysAccept(NetworkVenue networkVenue) {
    Radius acctRadius = new Radius("acctRadius-id");
    acctRadius.setAccountingRadiusServices(
        List.of(accountingRadiusService("acctRadiusService-id", acctRadius, ((GuestNetwork)networkVenue.getNetwork()).getGuestPortal())));
    ((GuestNetwork)networkVenue.getNetwork()).getGuestPortal().getWisprPage().setAccountingRadius(acctRadius);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    com.ruckus.acx.ddccm.protobuf.wifi.RadiusOptions result = operations.get(0).getWlanVenue().getRadiusOptions();
    assertEquals("Always Accept", operations.get(0).getWlanVenue().getAuthenticationType().getValue());
    assertEquals(StringValue.of("AP_MAC"), result.getNasIdType());
    assertEquals(CalledStaIdType.CalledStaIdType_AP_GROUP, result.getCalledStaIdType());
    assertEquals(StringValue.of("COLON"), result.getNasIdDelimiter());
    assertEquals(Int32Value.of(4), result.getAuthenticationRequestTimeout());
    assertEquals(Int32Value.of(3), result.getAuthenticationMaxRetry());
    assertEquals(Int32Value.of(6), result.getAuthenticationRetryPrimaryInterval());
    assertEquals(Int32Value.of(4), result.getAccountingRequestTimeout());
    assertEquals(Int32Value.of(3), result.getAccountingMaxRetry());
    assertEquals(Int32Value.of(6), result.getAccountingRetryPrimaryInterval());
    assertEquals(BoolValue.of(true), result.getSingleSessionIdAcctEnabled());
    assertFalse(result.hasUserDefinedNasId());
  }

  private void testModifyRadiusOptions_withNullRadiusOptions(NetworkVenue networkVenue) {
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setRadiusOptions(null);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    com.ruckus.acx.ddccm.protobuf.wifi.RadiusOptions result = operations.get(0).getWlanVenue().getRadiusOptions();
    assertEquals(StringValue.of("BSSID"), result.getNasIdType());
    assertEquals(CalledStaIdType.CalledStaIdType_WLAN_BSSID, result.getCalledStaIdType());
    assertEquals(StringValue.of("DASH"), result.getNasIdDelimiter());
    assertEquals(Int32Value.of(3), result.getAuthenticationRequestTimeout());
    assertEquals(Int32Value.of(2), result.getAuthenticationMaxRetry());
    assertEquals(Int32Value.of(5), result.getAuthenticationRetryPrimaryInterval());
    assertEquals(Int32Value.of(3), result.getAccountingRequestTimeout());
    assertEquals(Int32Value.of(2), result.getAccountingMaxRetry());
    assertEquals(Int32Value.of(5), result.getAccountingRetryPrimaryInterval());
    assertEquals(BoolValue.of(false), result.getSingleSessionIdAcctEnabled());
    assertFalse(result.hasUserDefinedNasId());
  }

  private void testModifyRadiusOptions_withVenueConfig(NetworkVenue networkVenue) {
    // modify venue RADIUS options
    VenueRadiusOptions venueRadiusOptions = new VenueRadiusOptions();
    venueRadiusOptions.setOverrideEnabled(true);
    venueRadiusOptions.setNasIdType(NasIdTypeEnum.VENUE_NAME);
    venueRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.NONE);
    venueRadiusOptions.setNasRequestTimeoutSec(4);
    venueRadiusOptions.setNasMaxRetry(3);
    venueRadiusOptions.setNasReconnectPrimaryMin(6);
    venueRadiusOptions.setSingleSessionIdAccounting(true);
    networkVenue.getVenue().setRadiusOptions(venueRadiusOptions);
    networkVenue.getVenue().setName("test");

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    assertEquals(Action.ADD, operations.get(0).getAction());
    com.ruckus.acx.ddccm.protobuf.wifi.RadiusOptions result = operations.get(0).getWlanVenue().getRadiusOptions();
    assertEquals(StringValue.of("USER"), result.getNasIdType());
    assertEquals(CalledStaIdType.CalledStaIdType_NONE, result.getCalledStaIdType());
    assertEquals(StringValue.of("test"), result.getUserDefinedNasId());
    assertEquals(Int32Value.of(4), result.getAuthenticationRequestTimeout());
    assertEquals(Int32Value.of(3), result.getAuthenticationMaxRetry());
    assertEquals(Int32Value.of(6), result.getAuthenticationRetryPrimaryInterval());
    assertEquals(Int32Value.of(4), result.getAccountingRequestTimeout());
    assertEquals(Int32Value.of(3), result.getAccountingMaxRetry());
    assertEquals(Int32Value.of(6), result.getAccountingRetryPrimaryInterval());
    assertEquals(BoolValue.of(true), result.getSingleSessionIdAcctEnabled());
    assertFalse(result.hasNasIdDelimiter());
  }

  @Test
  public void testSSID_rate_limiting_dhcp_opt_82_amb_dtim_interval() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    WlanAdvancedCustomization advancedCustomization = networkVenue.getNetwork().getWlan().getAdvancedCustomization();
    advancedCustomization.setTotalUplinkRateLimiting((short)123);
    advancedCustomization.setTotalDownlinkRateLimiting((short)321);
    advancedCustomization.setDhcpOption82Enabled(true);
    advancedCustomization.setDhcpOption82SubOption1Enabled(true);
    advancedCustomization.setDhcpOption82SubOption1Format(DhcpOption82SubOption1Enum.SUBOPT1_AP_INFO_LOCATION);
    advancedCustomization.setDhcpOption82SubOption2Enabled(true);
    advancedCustomization.setDhcpOption82SubOption2Format(DhcpOption82SubOption2Enum.SUBOPT2_AP_MAC_hex);
    advancedCustomization.setDhcpOption82SubOption150Enabled(true);
    advancedCustomization.setDhcpOption82SubOption151Enabled(true);
    advancedCustomization.setDhcpOption82SubOption151Format(DhcpOption82SubOption151Enum.SUBOPT151_AREA_NAME);
    advancedCustomization.setDhcpOption82SubOption151Input("AREA_NAME");
    advancedCustomization.setDhcpOption82MacFormat(DhcpOption82MacEnum.NODELIMITER);
    advancedCustomization.setAgileMultibandEnabled(true);
    advancedCustomization.setDtimInterval((short)200);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanVenue wlanVenue = operations.get(0).getWlanVenue();
    assertEquals("1", wlanVenue.getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, wlanVenue.getWlanType());
    assertEquals("OPEN", operations.get(0).getWlanVenue().getNetworkType().getValue());
    com.ruckus.acx.ddccm.protobuf.wifi.WlanAdvancedCustomization wlanAdvancedCustomization = wlanVenue.getAdvancedCustomization();

    assertEquals(123, wlanAdvancedCustomization.getTotalUplinkRateLimiting());
    assertEquals(321, wlanAdvancedCustomization.getTotalDownlinkRateLimiting());
    assertEquals(1, wlanVenue.getDhcpOption82());
    StringBuilder dhcpOption82OptionFormat = new StringBuilder();
    dhcpOption82OptionFormat.append(DhcpOption82SubOption1Enum.SUBOPT1_AP_INFO_LOCATION.getValue());
    dhcpOption82OptionFormat.append(DhcpOption82SubOption2Enum.SUBOPT2_AP_MAC_hex.getValue());
    dhcpOption82OptionFormat.append(DhcpOption82SubOption150Enum.SUBOPT150_VLAN_ID.getValue());
    dhcpOption82OptionFormat.append(String.format(DhcpOption82SubOption151Enum.SUBOPT151_AREA_NAME.getValue(), "AREA_NAME"));
    assertEquals(dhcpOption82OptionFormat.toString(), wlanVenue.getDhcpOption82Format());
    assertEquals(DhcpOption82MacFormat.valueOf(DhcpOption82MacEnum.NODELIMITER.getValue()), wlanVenue.getDhcpOption82MacFormat());
    assertTrue(wlanAdvancedCustomization.getAgileMultibandEnabled().getValue());
    assertEquals(200, wlanVenue.getDtimInterval().getValue());
  }

  @Test
  public void testSSID_rate_limiting_dhcp_opt_82_amb_dtim_interval_unset() {
    NetworkVenue networkVenue = createOpenNetworkVenue();

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    WlanVenue wlanVenue = operations.get(0).getWlanVenue();
    assertEquals("1", wlanVenue.getApWlanId().getValue());
    assertEquals(networkVenue.getId(), operations.get(0).getId());
    assertEquals(WlanType.PUBLIC, wlanVenue.getWlanType());
    com.ruckus.acx.ddccm.protobuf.wifi.WlanAdvancedCustomization wlanAdvancedCustomization = wlanVenue.getAdvancedCustomization();

    assertEquals(0, wlanAdvancedCustomization.getTotalUplinkRateLimiting());
    assertEquals(0, wlanAdvancedCustomization.getTotalDownlinkRateLimiting());
    assertEquals(0, wlanVenue.getDhcpOption82());
    assertEquals("", wlanVenue.getDhcpOption82Format());
    assertEquals(DhcpOption82MacFormat.MAC_COLON, wlanVenue.getDhcpOption82MacFormat());
    assertFalse(wlanAdvancedCustomization.getAgileMultibandEnabled().getValue());
    assertEquals(1, wlanVenue.getDtimInterval().getValue());
  }

  @Test
  public void testMulticastFilter() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setMulticastFilterEnabled(true);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
            .filteredOn(Operation::hasWlanVenue)
            .hasSize(1)
            .first()
            .extracting(e -> e.getWlanVenue().getMulticastFilterDrop())
            .isEqualTo(true);
  }

  @Test
  public void testQosMapSet() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setQosMapSetEnabled(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setQosMapSetOptions(new QosMapSetOptions());

    QosMapRule rule1 = new QosMapRule();
    rule1.setEnabled(false);
    rule1.setPriority((short) 0);
    rule1.setDscpLow((short) 0);
    rule1.setDscpHigh((short) 7);
    rule1.setDscpExceptionValues(List.of((short) 20, (short) 30));
    QosMapRule rule2 = new QosMapRule();
    rule2.setEnabled(true);
    rule2.setPriority((short) 2);
    rule2.setDscpLow((short) 10);
    rule2.setDscpHigh((short) 20);
    rule2.setDscpExceptionValues(List.of((short) 1, (short) 2, (short) 3));
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().getQosMapSetOptions()
        .setRules(List.of(rule1, rule2));

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(Operation::hasWlanVenue)
        .first()
        .extracting(e -> e.getWlanVenue().getAdvancedCustomization().getQosMap())
        .matches(qosMap -> qosMap.getEnabled().equals(BoolValue.of(true)))
        .matches(qosMap -> qosMap.getDscpExceptionValuesCount() == 3 && qosMap.getDscpValuesCount() == 2)
        .satisfies(qosMap -> {
          assertThat(qosMap.getDscpValuesList())
              .filteredOn(dscpValue -> dscpValue.getPriority().equals(Int32Value.of(0)))
              .hasSize(1)
              .first()
              .matches(dscpValue -> dscpValue.getDscpLow().equals(Int32Value.of(255)))
              .matches(dscpValue -> dscpValue.getDscpHigh().equals(Int32Value.of(255)));

          assertThat(qosMap.getDscpExceptionValuesList())
              .filteredOn(dscpExceptionValue -> dscpExceptionValue.getPriority().equals(Int32Value.of(0)))
              .isEmpty();

          assertThat(qosMap.getDscpValuesList())
              .filteredOn(dscpValue -> dscpValue.getPriority().equals(Int32Value.of(2)))
              .hasSize(1)
              .first()
              .matches(dscpValue -> dscpValue.getDscpLow().equals(Int32Value.of(10)))
              .matches(dscpValue -> dscpValue.getDscpHigh().equals(Int32Value.of(20)));

          assertThat(qosMap.getDscpExceptionValuesList())
              .filteredOn(dscpExceptionValue -> dscpExceptionValue.getPriority().equals(Int32Value.of(2)))
              .hasSize(3)
              .map(DscpExceptionValue::getDscp)
              .contains(Int32Value.of(1), Int32Value.of(2), Int32Value.of(3));
        });
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_FR_8021X_MAC_TOGGLE)
  public void test8021XMAC_FFDisable() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setMacAuthMacFormat(MacAuthMacFormatEnum.Lower);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(Operation::hasWlanVenue)
        .hasSize(1)
        .first()
        .matches(e -> !e.getWlanVenue().hasMacAddressAuthentication());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_8021X_MAC_TOGGLE)
  public void test8021XMAC_FFEnable() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);
    networkVenue.getNetwork().getWlan().setMacAuthMacFormat(MacAuthMacFormatEnum.Lower);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(Operation::hasWlanVenue)
        .hasSize(1)
        .first()
        .extracting(
            e -> e.getWlanVenue().getMacAddressAuthentication().getMacAuthMacFormat().name())
        .isEqualTo(MacAuthMacFormatEnum.Lower.name());
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_OVER_THE_DS_FT_SUPPORT_TOGGLE)
  public void testOverTheDSFtSupport_FFDisable() {
    var networkVenue = createPskNetworkVenue();
    var fastRoamingOptions = new FastRoamingOptions();
    fastRoamingOptions.setStatisticsOverDistributedSystemEnabled(true);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setFastRoamingOptions(fastRoamingOptions);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(Operation::hasWlanVenue)
        .hasSize(1)
        .first()
        .matches(e -> !e.getWlanVenue().getAdvancedCustomization().getOverTheDsEnabled().getValue());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_OVER_THE_DS_FT_SUPPORT_TOGGLE)
  public void testOverTheDSFtSupport_FFEnable() {
    var networkVenue = createPskNetworkVenue();
    var fastRoamingOptions = new FastRoamingOptions();
    fastRoamingOptions.setStatisticsOverDistributedSystemEnabled(true);
    fastRoamingOptions.setReassociationTimeout((short) 99);
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setFastRoamingOptions(fastRoamingOptions);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(Operation::hasWlanVenue)
        .hasSize(1)
        .first()
        .matches(
            e -> e.getWlanVenue().getAdvancedCustomization().getOverTheDsEnabled().getValue() &&
                e.getWlanVenue().getAdvancedCustomization().getReassociationTimeout()
                    .equals(Int32Value.of(99)));
  }

  @Test
  public void testAddNetworkVenue_AAANetworkWithCertificateTemplateId() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    ((AAANetwork) networkVenue.getNetwork()).setUseCertificateTemplate(true);
    networkVenue.getNetwork().getWlan().setCertificateTemplateId("certificateTemplateId");
    final var ddccmAuthRadiusVenueOperation = Operation.newBuilder()
        .setId(networkVenue.getId())
        .setVenueRadius(VenueRadius.newBuilder()
            .setId(networkVenue.getId())
            .setVenueId(networkVenue.getVenue().getId())
            .build())
        .build();
    doReturn(ddccmAuthRadiusVenueOperation).when(ddccmAuthRadiusVenueOperationBuilder).createAuthRadiusVenue(any(), any());

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
        .filteredOn(Operation::hasWlanVenue)
        .hasSize(1)
        .first()
        .extracting(e -> e.getWlanVenue().getAuthenticationServerId())
        .isEqualTo(networkVenue.getId());
    assertThat(operations)
        .filteredOn(Operation::hasVenueRadius)
        .hasSize(1)
        .first()
        .matches(
            e -> e.getVenueRadius().getVenueId().equals(networkVenue.getVenue().getId()) &&
                e.getVenueRadius().getId().equals(networkVenue.getId()));
  }

  @Test
  public void testAddNetworkVenue_AAANetworkWithCertificateTemplateIds() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    ((AAANetwork) networkVenue.getNetwork()).setUseCertificateTemplate(true);
    networkVenue.getNetwork().getWlan().setCertificateTemplateIds(List.of("certificateTemplateId"));
    final var ddccmAuthRadiusVenueOperation = Operation.newBuilder()
            .setId(networkVenue.getId())
            .setVenueRadius(VenueRadius.newBuilder()
                    .setId(networkVenue.getId())
                    .setVenueId(networkVenue.getVenue().getId())
                    .build())
            .build();
    doReturn(ddccmAuthRadiusVenueOperation).when(ddccmAuthRadiusVenueOperationBuilder).createAuthRadiusVenue(any(), any());

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());

    assertThat(operations)
            .filteredOn(Operation::hasWlanVenue)
            .hasSize(1)
            .first()
            .extracting(e -> e.getWlanVenue().getAuthenticationServerId())
            .isEqualTo(networkVenue.getId());
    assertThat(operations)
            .filteredOn(Operation::hasVenueRadius)
            .hasSize(1)
            .first()
            .matches(
                    e -> e.getVenueRadius().getVenueId().equals(networkVenue.getVenue().getId()) &&
                            e.getVenueRadius().getId().equals(networkVenue.getId()));
  }

  private void testBuildOperation_settingsChanged(String modifiedField) {
    doReturn(false).when(builder).hasModification(any(), any(), eq(false));
    NetworkVenue networkVenue = createOpenNetworkVenue();

    TxChanges txChanges = new TxChangesImpl(null);

    AAANetwork aaaNetwork = new AAANetwork("aaa-id");
    txChanges.add(aaaNetwork, Set.of(modifiedField), EntityAction.MODIFY);

    List<Operation> operations = builder.build( new ModifiedTxEntity<>(networkVenue, Collections.emptySet()), txChangesReader(txChanges));
    assertNotNull(operations);
  }

  @Test
  public void testDefaultApplicationVisibilityConfig() {
    NetworkVenue networkVenue = createPskNetworkVenue();

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getAvcEnabled().getValue());

    // set applicationVisibilityEnabled to false
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setApplicationVisibilityEnabled(false);
    operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().getAvcEnabled().getValue());

    // set applicationVisibilityEnabled to true
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setApplicationVisibilityEnabled(true);
    operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getAvcEnabled().getValue());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_NETWORK_APPLICATION_CONTROL_TOGGLE_GLOBAL_DEFAULT)
  public void testApplicationVisibilityConfig() {
    NetworkVenue networkVenue = createPskNetworkVenue();

    // set applicationVisibilityEnabled to null
    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getAvcEnabled().getValue());

    // set applicationVisibilityEnabled to false
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setApplicationVisibilityEnabled(false);
    operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertFalse(operations.get(0).getWlanVenue().getAdvancedCustomization().getAvcEnabled().getValue());

    // set applicationVisibilityEnabled to true
    networkVenue.getNetwork().getWlan().getAdvancedCustomization().setApplicationVisibilityEnabled(true);
    operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertTrue(operations.get(0).getWlanVenue().getAdvancedCustomization().getAvcEnabled().getValue());
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testVenueBothRadiusProfileOverrideNetWorkPskNonProxy() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testVenueAuthRadiusProfileOverrideNetWorkPskNonProxy() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = false;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("networkAccoutingRadius-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testVenueAccountingRadiusProfileOverrideNetWorkPskNonProxy() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("networkAuthRadius-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testVenueBothRadiusProfileOverrideNetWorkPskProxy() {
    NetworkVenue networkVenue = createPskNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = true;
    boolean enableAccountingProxy = true;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("", authResult);
    assertEquals("accountingService-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testBothVenueOverrideNetWorkRadiusProfileDpskNonProxy() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAuthVenueOverrideNetWorkRadiusProfileDpskNonProxy() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = false;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("networkAccoutingRadius-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAccountingVenueOverrideNetWorkRadiusProfileDpskNonProxy() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("networkAuthRadius-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testBothVenueOverrideNetWorkRadiusProfileDpskProxy() {
    NetworkVenue networkVenue = createDpskNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = true;
    boolean enableAccountingProxy = true;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authService-id", authResult);
    assertEquals("accountingService-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testBothVenueOverrideNetWorkRadiusProfileAaaNonProxy() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAuthVenueOverrideNetWorkRadiusProfileAaaNonProxy() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = false;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("networkAccoutingRadius-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAccountingVenueOverrideNetWorkRadiusProfileAaaNonProxy() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("networkAuthRadius-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testBothVenueOverrideNetWorkRadiusProfileAaaProxy() {
    NetworkVenue networkVenue = createAaaNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = true;
    boolean enableAccountingProxy = true;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("", authResult);
    assertEquals("accountingService-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testBothVenueOverrideNetWorkRadiusProfileOpenNonProxy() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAuthVenueOverrideNetWorkRadiusProfileOpenNonProxy() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = false;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("networkAccoutingRadius-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAccountingVenueOverrideNetWorkRadiusProfileOpenNonProxy() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("networkAuthRadius-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testBothVenueOverrideNetWorkRadiusProfileOpenProxy() {
    NetworkVenue networkVenue = createOpenNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = true;
    boolean enableAccountingProxy = true;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("", authResult);
    assertEquals("accountingService-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testBothVenueOverrideNetWorkRadiusProfilePublicGuestNonProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);


    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAuthVenueOverrideNetWorkRadiusProfilePublicGuestNonProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = false;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);


    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("networkAccoutingRadius-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAccountingVenueOverrideNetWorkRadiusProfilePublicGuestNonProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);


    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("networkAuthRadius-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testBothVenueOverrideNetWorkRadiusProfilePublicGuestProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = true;
    boolean enableAccountingProxy = true;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);


    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authService-id", authResult);
    assertEquals("accountingService-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testBothVenueOverrideNetWorkRadiusProfilePublicGuestWisPrNonProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);


    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAuthVenueOverrideNetWorkRadiusProfilePublicGuestWisPrNonProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = false;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);


    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("authRadiusVenue-id", authResult);
    assertEquals("", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAccountingVenueOverrideNetWorkRadiusProfilePublicGuestWisPrNonProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);


    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertNotEquals("authRadiusVenue-id", authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAccountingVenueOverrideNetworkRadiusProfilePublicGuestSelfSignInNonProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.SelfSignIn);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    String expectAuth = TxCtxHolder.tenantId() + "-Auth-" + "guest-network-auth-service";
    assertEquals( expectAuth, authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAccountingVenueOverrideNetworkRadiusProfilePublicGuestHostApprovalNonProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.HostApproval);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);

    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    String expectAuth = TxCtxHolder.tenantId() + "-Auth-" + "guest-network-auth-service";
    assertEquals( expectAuth, authResult);
    assertEquals("accountingRadiusVenue-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAccountingVenueOverrideNetworkRadiusProfilePublicGuestSelfSignInProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = true;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.SelfSignIn);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    String expectAuth = TxCtxHolder.tenantId() + "-Auth-" + "guest-network-auth-service";
    assertEquals( expectAuth, authResult);
    assertEquals("accountingService-id", accountingResult);
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE})
  public void testAccountingVenueOverrideNetworkRadiusProfilePublicGuestHostApprovalProxy() {
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = false;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = true;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.HostApproval);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    String expectAuth = TxCtxHolder.tenantId() + "-Auth-" + "guest-network-auth-service";
    assertEquals( expectAuth, authResult);
    assertEquals("accountingService-id", accountingResult);
  }

  @Test
  public void testVenueBothRadiusProfileOverrideNetWorkWithoutFF() {
    // This route in auth includes PSK, DPSK, AAA, Guest Cloudpath, Open.
    // And in accounting includes PSK, DPSK, AAA, Guest without Wispr, Open.
    // Just take one as a test example.
    NetworkVenue networkVenue = createPskNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);

    //auth mock data
    Radius authRadius = new Radius("networkAuthRadius-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("networkAccoutingRadius-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    assertEquals("networkAuthRadius-id", authResult);
    assertEquals("networkAccoutingRadius-id", accountingResult);
  }

  @Test
  public void testVenueBothRadiusProfileOverrideNetWorkWisprWithoutFF() {
    // This route in auth includes Guest Wispr.
    // And in accounting in Guest Wispr.
    NetworkVenue networkVenue = createPublicGuestNetworkVenue();
    boolean isVenueAuthOverride = true;
    boolean isVenueAccountingOverride = true;
    boolean enableAuthProxy = false;
    boolean enableAccountingProxy = false;
    prepareNetworkVenue(networkVenue, isVenueAuthOverride, isVenueAccountingOverride, enableAuthProxy, enableAccountingProxy);
    //set guestPortal for process
    GuestPortal guestPortal = new GuestPortal();
    guestPortal.setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    WisprPage wisprPage = new WisprPage();
    wisprPage.setCustomExternalProvider(true);
    guestPortal.setWisprPage(wisprPage);
    ((GuestNetwork)networkVenue.getNetwork()).setGuestPortal(guestPortal);
    //set venuePortal for process
    VenuePortal venuePortal = new VenuePortal("venuePortal-id");
    networkVenue.setVenuePortal(venuePortal);


    //auth mock data
    Radius authRadius = new Radius("authRadiusVenue-id");
    AuthRadiusVenue authRadiusVenue = initAuthRadiusVenue(networkVenue, authRadius);
    doReturn(authRadiusVenue).when(authRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(authRadius.getId()));
    //accounting mock data
    Radius accountingRadius = new Radius("accountingRadiusVenue-id");
    AccountingRadiusVenue accountingRadiusVenue = initAccountingRadiusVenue(networkVenue, accountingRadius);
    doReturn(accountingRadiusVenue).when(accountingRadiusVenueRepository).findByTenantIdAndVenueIdAndRadiusId(
            eq(TxCtxHolder.tenantId()), eq(networkVenue.getVenue().getId()), eq(accountingRadius.getId()));

    clearInvocations(authRadiusVenueRepository);
    clearInvocations(accountingRadiusVenueRepository);

    List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
    assertEquals(1, operations.size());
    String authResult = operations.get(0).getWlanVenue().getAuthenticationServerId();
    String accountingResult = operations.get(0).getWlanVenue().getAccountingServerId();
    String expectAuth = TxCtxHolder.tenantId() + "-Auth-" + "guest-network-auth-service";
    assertEquals(expectAuth, authResult);
    assertEquals("", accountingResult);
  }

  @Test
  public void testGuestNetworkVenue_withEnabledAuthProxy() {
    GuestNetworkTypeEnum[] typesToTest = {
        GuestNetworkTypeEnum.ClickThrough,
        GuestNetworkTypeEnum.SelfSignIn,
        GuestNetworkTypeEnum.HostApproval,
        GuestNetworkTypeEnum.GuestPass,
        GuestNetworkTypeEnum.Directory,
        GuestNetworkTypeEnum.SAML
    };
    for (GuestNetworkTypeEnum type : typesToTest) {
      NetworkVenue networkVenue = createPublicGuestNetworkVenue();
      networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
      networkVenue.getNetwork().getWlan().setBypassCNA(true);
      networkVenue.getNetwork().setEnableAuthProxy(true);
      networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
      GuestPortal guestPortal = new GuestPortal("guestProtal-id");
      guestPortal.setGuestNetworkType(type);
      ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);

      List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
      assertEquals(1, operations.size());

      assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());

      WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
      assertEquals(
          txCtxExtension.getTenantId() + "-Auth-guest-network-auth-service", wlanAuth.getId());
      assertTrue(wlanAuth.getThroughController());
      assertEquals(AaaType.RADIUS, wlanAuth.getType());
    }
  }

  @Test
  public void testGuestNetworkVenue_withDisabledAuthProxy() {
    GuestNetworkTypeEnum[] typesToTest = {
        GuestNetworkTypeEnum.ClickThrough,
        GuestNetworkTypeEnum.SelfSignIn,
        GuestNetworkTypeEnum.HostApproval,
        GuestNetworkTypeEnum.GuestPass,
        GuestNetworkTypeEnum.Directory,
        GuestNetworkTypeEnum.SAML
    };
    for (GuestNetworkTypeEnum type : typesToTest) {
      NetworkVenue networkVenue = createPublicGuestNetworkVenue();
      networkVenue.getNetwork().getWlan().setBypassCPUsingMacAddressAuthentication(true);
      networkVenue.getNetwork().getWlan().setBypassCNA(true);
      networkVenue.getNetwork().setEnableAuthProxy(false);
      networkVenue.setVenuePortal(new VenuePortal("venuePortal-id"));
      GuestPortal guestPortal = new GuestPortal("guestProtal-id");
      guestPortal.setGuestNetworkType(type);
      ((GuestNetwork) networkVenue.getNetwork()).setGuestPortal(guestPortal);

      List<Operation> operations = builder.build(new NewTxEntity<>(networkVenue), emptyTxChanges());
      assertEquals(1, operations.size());

      assertEquals("venuePortal-id", operations.get(0).getWlanVenue().getHotspotId());

      WlanAuth wlanAuth = operations.get(0).getWlanVenue().getAuthAaa();
      assertEquals(
          txCtxExtension.getTenantId() + "-Auth-guest-network-auth-service", wlanAuth.getId());
      assertTrue(wlanAuth.getThroughController());
      assertEquals(AaaType.RADIUS, wlanAuth.getType());
    }
  }

  private static void prepareNetworkVenue(NetworkVenue networkVenue, boolean isVenueAuthOverride, boolean isVenueAccountingOverride,
                                          boolean enableAuthProxy, boolean enableAccountingProxy) {
    networkVenue.getNetwork().setEnableAuthProxy(enableAuthProxy);
    networkVenue.getNetwork().setEnableAccountingProxy(enableAccountingProxy);
    networkVenue.getNetwork().getWlan().setMacAddressAuthentication(true);
    //if not override would be use this to query
    networkVenue.getNetwork().setAuthRadius(new Radius("networkAuthRadius-id"));
    networkVenue.getNetwork().setAccountingRadius(new Radius("networkAccoutingRadius-id"));
    //if override would be use this to query
    networkVenue.getVenue().setAuthRadius(new Radius("authRadiusVenue-id"));
    networkVenue.getVenue().setAccountingRadius(new Radius("accountingRadiusVenue-id"));

    if (isVenueAuthOverride && isVenueAccountingOverride) {
      prepareOverrideRadiusForBoth(isVenueAuthOverride, isVenueAccountingOverride, networkVenue);
    } else if (isVenueAuthOverride) {
      prepareOverrideAuthRadius(isVenueAuthOverride, networkVenue);
    } else if (isVenueAccountingOverride) {
      prepareOverrideAccountingRadius(isVenueAccountingOverride, networkVenue);
    }


    WlanAdvancedCustomization wlanAdvancedCustomization = networkVenue.getNetwork()
            .getWlan().getAdvancedCustomization();
    RadiusOptions radiusOptions = new RadiusOptions();
    radiusOptions.setNasIdType(NasIdTypeEnum.AP_MAC);
    radiusOptions.setNasIdDelimiter(NasIdDelimiterEnum.COLON);
    radiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_GROUP);
    radiusOptions.setNasRequestTimeoutSec(4);
    radiusOptions.setNasMaxRetry(3);
    radiusOptions.setNasReconnectPrimaryMin(6);
    radiusOptions.setSingleSessionIdAccounting(true);

    wlanAdvancedCustomization.setRadiusOptions(radiusOptions);
  }

  private static void prepareOverrideRadiusForBoth(boolean isAuthOverride, boolean isAccountingOverride, NetworkVenue networkVenue) {
    VenueRadiusServerProfileSettings settings = new VenueRadiusServerProfileSettings();
    settings.setOverrideAuthRadius(isAuthOverride);
    networkVenue.getVenue().setRadiusServerProfileSettings(settings);
    networkVenue.getVenue().getRadiusServerProfileSettings().setOverrideAccountingRadius(isAccountingOverride);
  }

  private static void prepareOverrideAuthRadius(boolean isOverride, NetworkVenue networkVenue) {
    VenueRadiusServerProfileSettings settings = new VenueRadiusServerProfileSettings();
    settings.setOverrideAuthRadius(isOverride);
    networkVenue.getVenue().setRadiusServerProfileSettings(settings);
  }

  private static void prepareOverrideAccountingRadius(boolean isOverride, NetworkVenue networkVenue) {
    VenueRadiusServerProfileSettings settings = new VenueRadiusServerProfileSettings();
    settings.setOverrideAccountingRadius(isOverride);
    networkVenue.getVenue().setRadiusServerProfileSettings(settings);
  }


  private static AuthRadiusVenue initAuthRadiusVenue(NetworkVenue networkVenue, Radius authRadius) {
    AuthRadiusVenue authRadiusVenue = authRadiusVenue(authRadius.getId(), authRadius, networkVenue.getVenue());
    authRadius.setAuthRadiusVenues(List.of(authRadiusVenue));

    AuthRadiusService authRadiusService = new AuthRadiusService("authService-id");
    authRadiusService.setRadius(new Radius("networkAuthRadius-id"));
    authRadiusService.setAuthRadiusProfile(new AuthRadiusProfile("authRadiusProfile-id"));
    authRadius.setAuthRadiusServices(List.of(authRadiusService));

    networkVenue.getNetwork().setAuthRadius(authRadius);
    return authRadiusVenue;
  }

  private static AccountingRadiusVenue initAccountingRadiusVenue(NetworkVenue networkVenue, Radius accountingRadius) {
    AccountingRadiusVenue accountingRadiusVenue = accountingRadiusVenue(accountingRadius.getId(), accountingRadius, networkVenue.getVenue());
    accountingRadius.setAccountingRadiusVenues(List.of(accountingRadiusVenue));

    AccountingRadiusService accountingRadiusService = new AccountingRadiusService("accountingService-id");
    accountingRadiusService.setRadius(new Radius("networkAccoutingRadius-id"));
    accountingRadiusService.setAccountingRadiusProfile(new AccountingRadiusProfile(("accountingRadiusProfile-id")));
    accountingRadius.setAccountingRadiusServices(List.of(accountingRadiusService));

    networkVenue.getNetwork().setAccountingRadius(accountingRadius);
    return accountingRadiusVenue;
  }

  private static NetworkVenue createOpenNetworkVenue() {
    return setupNetworkVenue(new OpenNetwork(randomId()));
  }

  private static NetworkVenue createPskNetworkVenue() {
    return setupNetworkVenue(new PskNetwork(randomId()));
  }

  private static NetworkVenue createAaaNetworkVenue() {
    return setupNetworkVenue(new AAANetwork(randomId()));
  }

  private static NetworkVenue createDpskNetworkVenue() {
    return setupNetworkVenue(new DpskNetwork(randomId()));
  }

  private static NetworkVenue createPublicGuestNetworkVenue() {
    NetworkVenue networkVenue = setupNetworkVenue(
        new GuestNetwork(randomId()));
    networkVenue.getNetwork().getWlan().setWlanSecurity(WlanSecurityEnum.Open);
    return networkVenue;
  }

  private static NetworkVenue createPrivateGuestNetworkVenue() {
    return setupNetworkVenue(new GuestNetwork(randomId()));
  }

  private static NetworkVenue createHotspot20NetworkVenue() {
    var networkVenue = setupNetworkVenue(new Hotspot20Network(randomId()));
    var networkHotspot20Settings = new NetworkHotspot20Settings("hs20sId");
    networkHotspot20Settings.setOperator(new Hotspot20Operator("hs20oId"));
    networkHotspot20Settings.setIdentityProviders(List.of(new Hotspot20IdentityProvider("hs20iId")));
    var aaaNetwork = (Hotspot20Network) networkVenue.getNetwork();
    aaaNetwork.setAccountingInterimUpdates((short) 30);
    aaaNetwork.setHotspot20Settings(networkHotspot20Settings);
    aaaNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Enterprise);
    return networkVenue;
  }

  private static NetworkVenue createHotspot20NetworkVenueWithRadiusOptions() {
    var networkVenue = setupNetworkVenue(new Hotspot20Network(randomId()));
    var networkHotspot20Settings = new NetworkHotspot20Settings("hs20sId");
    networkHotspot20Settings.setOperator(new Hotspot20Operator("hs20oId"));
    Hotspot20IdentityProvider hotspot20IdentityProvider = new Hotspot20IdentityProvider("hs20iId");
    // has radius
    hotspot20IdentityProvider.setAuthRadius(new Radius());
    networkHotspot20Settings.setIdentityProviders(List.of(hotspot20IdentityProvider));
    var aaaNetwork = (Hotspot20Network) networkVenue.getNetwork();
    aaaNetwork.setAccountingInterimUpdates((short) 30);
    aaaNetwork.setHotspot20Settings(networkHotspot20Settings);
    aaaNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Enterprise);

    // has radiusOptions
    Venue venue = networkVenue.getVenue();
    VenueRadiusOptions radiusOptions = new VenueRadiusOptions();
    radiusOptions.setNasIdType(NasIdTypeEnum.USER);
    radiusOptions.setNasMaxRetry(55);
    radiusOptions.setUserDefinedNasId("NasId");
    radiusOptions.setOverrideEnabled(true);
    venue.setRadiusOptions(radiusOptions);
    return networkVenue;
  }

  private static NetworkVenue setupNetworkVenue(Network network) {
    var tenant = new Tenant(randomId());
    var venue = new Venue(randomId());
    venue.setTenant(tenant);
    var networkVenue = new NetworkVenue(randomId());
    networkVenue.setNetwork(network);
    networkVenue.setVenue(venue);
    networkVenue.setTenant(tenant);
    networkVenue.setApWlanId(1);

    var wlan = new Wlan();
    network.setName("fake-network");
    network.setWlan(wlan);
    wlan.setTenant(tenant);
    wlan.setAdvancedCustomization(new WlanAdvancedCustomization());
    wlan.setNetwork(network);
    wlan.setSsid("fake-ssid");
    wlan.setWlanSecurity(WlanSecurityEnum.WPA2Personal);
    return networkVenue;
  }

  private static AuthRadiusVenue authRadiusVenue(String id, Radius radius, Venue venue) {
    AuthRadiusVenue authRadiusVenue = new AuthRadiusVenue(id);
    authRadiusVenue.setRadius(radius);
    authRadiusVenue.setVenue(venue);
    return authRadiusVenue;
  }

  private static AccountingRadiusVenue accountingRadiusVenue(String id, Radius radius, Venue venue) {
    AccountingRadiusVenue accountingRadiusVenue = new AccountingRadiusVenue(id);
    accountingRadiusVenue.setRadius(radius);
    accountingRadiusVenue.setVenue(venue);
    return accountingRadiusVenue;
  }

  private static AuthRadiusService authRadiusService(String id, Radius radius, GuestPortal guestPortal) {
    return authRadiusService(id, radius, guestPortal, null);
  }

  @Deprecated(forRemoval = true)
  private static AuthRadiusService authRadiusService(String id, Radius radius, CloudpathServer cloudpathServer) {
    return authRadiusService(id, radius, null, cloudpathServer);
  }

  @Deprecated(forRemoval = true)
  private static AuthRadiusService authRadiusService(String id, Radius radius, GuestPortal guestPortal, CloudpathServer cloudpathServer) {
    AuthRadiusService authRadiusService = new AuthRadiusService(id);
    authRadiusService.setRadius(radius);
    authRadiusService.setGuestPortal(guestPortal);
    authRadiusService.setCloudpathServer(cloudpathServer);
    return authRadiusService;
  }

  private static AuthRadiusProfile authRadiusProfile(String id, AuthRadiusService authRadiusService) {
    AuthRadiusProfile authRadiusProfile = new AuthRadiusProfile(id);
    authRadiusProfile.setAuthRadiusService(authRadiusService);
    return authRadiusProfile;
  }

  private static AccountingRadiusService accountingRadiusService(String id, Radius radius) {
    return accountingRadiusService(id, radius, null, null);
  }

  private static AccountingRadiusService accountingRadiusService(String id, Radius radius, GuestPortal guestPortal) {
    return accountingRadiusService(id, radius, guestPortal, null);
  }

  @Deprecated(forRemoval = true)
  private static AccountingRadiusService accountingRadiusService(String id, Radius radius, CloudpathServer cloudpathServer) {
    return accountingRadiusService(id, radius, null, cloudpathServer);
  }

  @Deprecated(forRemoval = true)
  private static AccountingRadiusService accountingRadiusService(String id, Radius radius, GuestPortal guestPortal, CloudpathServer cloudpathServer) {
    AccountingRadiusService accountingRadiusService = new AccountingRadiusService(id);
    accountingRadiusService.setRadius(radius);
    accountingRadiusService.setGuestPortal(guestPortal);
    accountingRadiusService.setCloudpathServer(cloudpathServer);
    return accountingRadiusService;
  }

  private static AccountingRadiusProfile accountingRadiusProfile(String id, AccountingRadiusService accountingRadiusService) {
    AccountingRadiusProfile accountingRadiusProfile = new AccountingRadiusProfile(id);
    accountingRadiusProfile.setAccountingRadiusService(accountingRadiusService);
    return accountingRadiusProfile;
  }

  private static PinProfileNetworkMapping createPinProfileNetworkMapping(NetworkVenue networkVenue) {
    final PinProfile pinProfile = new PinProfile(randomId());
    final EdgeConnectResource edgeConnectResource =new EdgeConnectResource(randomId());
    edgeConnectResource.setPinProfile(pinProfile);
    pinProfile.setEdgeConnectResource(edgeConnectResource);
    final PinProfileRegularSetting regularSetting = new PinProfileRegularSetting(randomId());
    regularSetting.setVenue(networkVenue.getVenue());
    regularSetting.setPinProfile(pinProfile);
    regularSetting.setTunnelProfile(new TunnelProfile(randomId()));
    final PinProfileNetworkMapping networkMapping = new PinProfileNetworkMapping(randomId());
    networkMapping.setNetwork(networkVenue.getNetwork());
    networkMapping.setPinProfileRegularSetting(regularSetting);
    return networkMapping;
  }

  private static SdLanProfileNetworkMapping createSdLanProfileNetworkMapping(NetworkVenue networkVenue) {
    final SdLanProfile sdLanProfile = new SdLanProfile(randomId());
    final EdgeConnectResource edgeConnectResource =new EdgeConnectResource(randomId());
    edgeConnectResource.setSdLanProfile(sdLanProfile);
    sdLanProfile.setEdgeConnectResource(edgeConnectResource);
    final SdLanProfileRegularSetting regularSetting = new SdLanProfileRegularSetting(randomId());
    regularSetting.setVenue(networkVenue.getVenue());
    regularSetting.setSdLanProfile(sdLanProfile);
    regularSetting.setTunnelProfile(new TunnelProfile(randomId()));
    final SdLanProfileNetworkMapping networkMapping = new SdLanProfileNetworkMapping(randomId());
    networkMapping.setNetwork(networkVenue.getNetwork());
    networkMapping.setSdLanProfileRegularSetting(regularSetting);
    return networkMapping;
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public NetworkVenueMerge mockNetworkVenueMerge() {
      return Mockito.mock(NetworkVenueMerge.class);
    }
  }
}
