package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_VENUE_TEMPLATE_DHCP_POOL;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_CLIENT_ISOLATION_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_VENUE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DEACTIVATE_VENUE_TEMPLATE_DHCP_POOL;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK_VENUE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.dhcpConfigServiceProfileDeep;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.test.fixture.ClientIsolationTestFixture.clientIsolationAllowlist;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture.venueDhcpConfigServiceProfileSetting;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.getGuestNetwork;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static com.ruckus.cloud.wifi.test.util.TemplateRetriever.retrieveTemplate;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueDhcpServiceSetting;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ConnectedClientDTO;
import com.ruckus.cloud.wifi.eda.api.rest.DhcpConfigServiceProfileRestCtrl.DhcpConfigServiceProfileMapper;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApDhcpRoleEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DhcpConfigServiceProfileDeepGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpPool;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpPoolUsage;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpServiceAp;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSetting;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.impl.ExtendedDhcpServiceProfileVenueServiceCtrlImpl.DefaultGuestNetworkConfig;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.TemplateRetriever;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("DhcpConfigServiceProfileTest")
@WifiIntegrationTest
class ConsumeDhcpConfigServiceProfileTemplateRequestTest extends AbstractRequestTest {

  private final String DEFAULT_VERSION = "6.2.0.103.1";

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private DefaultGuestNetworkConfig defaultGuestNetworkConfig;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Nested
  @ApiAction(CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE)
  class ConsumeAddDhcpConfigServiceProfileRequestTest {

    @Payload
    private final DhcpConfigServiceProfileDeepGenerator generator =
        Generators.dhcpConfigServiceProfileDeep();

    @Payload("defaultProfile")
    DhcpConfigServiceProfileDeep defaultProfile() {
      DhcpConfigServiceProfileDeep payload = dhcpConfigServiceProfileDeep().generate();
      payload.setServiceName("DHCP-Guest");
      return payload;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload DhcpConfigServiceProfileDeep payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, payload.getId());
      assertNotNull(dhcpConfigServiceProfile);

      validateRepositoryData(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
          dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
              .collect(Collectors.toList())), dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE)
  class ConsumeUpdateDhcpConfigServiceProfileRequestTest {

    private String id;
    private DhcpConfigServiceProfile savedDhcpConfigServiceProfile;

    @BeforeEach
    void givenOneRowPersistedInDb(@Template DhcpConfigServiceProfile dhcpConfigServiceProfile) {
      id = dhcpConfigServiceProfile.getId();
      savedDhcpConfigServiceProfile = dhcpConfigServiceProfile;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("dhcpConfigServiceProfileId", id);
    }

    @Payload
    private final DhcpConfigServiceProfileDeepGenerator generator =
        Generators.dhcpConfigServiceProfileDeep();

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload DhcpConfigServiceProfileDeep payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, id);
      assertNotNull(dhcpConfigServiceProfile);
      payload.setId(id);

      validateRepositoryData(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
          dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
              .collect(Collectors.toList()), Action.DELETE,
          savedDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
              .collect(Collectors.toList())), dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ApiFlowNames.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }

    @Payload("onlyModifyDhcpPools")
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep() {
      DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep = DhcpConfigServiceProfileMapper.INSTANCE.ServiceDhcpConfigServiceProfile2DhcpConfigServiceProfileDeep(
          savedDhcpConfigServiceProfile);
      dhcpConfigServiceProfileDeep.getDhcpPools().get(0).setName("name1");
      return dhcpConfigServiceProfileDeep;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, payload = @Payload("onlyModifyDhcpPools"))
    void thenShouldHandleTheRequestSuccessfully_onlyModifyDhcpPools(
        @Payload("onlyModifyDhcpPools") DhcpConfigServiceProfileDeep payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, id);
      assertNotNull(dhcpConfigServiceProfile);
      payload.setId(id);

      validateRepositoryData(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.MODIFY,
          dhcpConfigServiceProfile.getDhcpPools().stream()
              .filter(dhcpPool -> dhcpPool.getName().equals("name1")).map(DhcpServiceProfile::getId)
              .collect(Collectors.toList())), dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ApiFlowNames.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE)
  class ConsumeDeleteDhcpConfigServiceProfileRequestTest {

    private String id;
    private List<String> dhcpPoolIds;

    @BeforeEach
    void givenOneRowPersistedInDb(@Template DhcpConfigServiceProfile dhcpConfigServiceProfile) {
      id = dhcpConfigServiceProfile.getId();
      dhcpPoolIds = dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
          .collect(Collectors.toList());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("dhcpConfigServiceProfileId", id);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      assertNull(repositoryUtil.find(DhcpConfigServiceProfile.class, id));
      validateDdccmCfgRequestMessages(Map.of(Action.DELETE, dhcpPoolIds), null);
      validateCmnCfgCollectorMessages(CfgAction.DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, List.of(id),
          null);
      validateActivityMessages(DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction,
      List<String> dhcpConfigServiceProfileIds, DhcpConfigServiceProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(dhcpConfigServiceProfileIds.size())
            .allMatch(op -> dhcpConfigServiceProfileIds.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction)).allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op).extracting(Operations::getDocMap).matches(
                        doc -> dhcpConfigServiceProfileIds.contains(
                            doc.get(Key.ID).getStringValue())).matches(
                        doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                    .matches(
                        doc -> payload.getServiceName().equals(doc.get(Key.NAME).getStringValue()))
                    .matches(doc -> payload.getDhcpPools().size() == (doc.get(Key.DHCP_POOLS)
                        .getListValue().getValuesCount()))
                    .matches(doc -> 0 == doc.get(Key.VENUE_IDS).getListValue().getValuesCount())
                    .matches(doc -> true == doc.get(Key.IS_TEMPLATE).getBoolValue());
              }
            }));
  }

  private void validateDdccmCfgRequestMessages(Map<Action, List<String>> actionIdMap,
      DhcpConfigServiceProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(Operation.class::cast)
            .filteredOn(Operation::hasDhcpServiceProfile)
            .hasSize(actionIdMap.values().stream().mapToInt(List::size).sum()).satisfies(ops -> {
              assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                      Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
              if (actionIdMap.containsKey(Action.DELETE)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.DELETE)
                    .hasSize(actionIdMap.get(Action.DELETE).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.DELETE)
                                .contains(dhcpServiceProfile.getId())));
              }
              if (actionIdMap.containsKey(Action.ADD)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.ADD)
                    .hasSize(actionIdMap.get(Action.ADD).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.ADD)
                                .contains(dhcpServiceProfile.getId())).matches(
                                dhcpServiceProfile -> assertDdccmDetail(dhcpServiceProfile, payload)));
              }
              if (actionIdMap.containsKey(Action.MODIFY)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.MODIFY)
                    .hasSize(actionIdMap.get(Action.MODIFY).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.MODIFY)
                                .contains(dhcpServiceProfile.getId())).matches(
                                dhcpServiceProfile -> actionIdMap.get(Action.MODIFY)
                                    .contains(dhcpServiceProfile.getId())).matches(
                                dhcpServiceProfile -> assertDdccmDetail(dhcpServiceProfile, payload)));
              }
            }));
  }

  private boolean assertDdccmDetail(
      com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile dhcpServiceProfile,
      DhcpConfigServiceProfile payload) {
    DhcpServiceProfile dhcpPool = payload.getDhcpPools().stream()
        .filter(p -> p.getId().equals(dhcpServiceProfile.getId())).findFirst().get();
    assertEquals(dhcpPool.getName(), dhcpServiceProfile.getName());
    assertEquals(Integer.valueOf(dhcpPool.getVlanId()), dhcpServiceProfile.getVlanId());
    assertEquals(dhcpPool.getSubnetAddress(), dhcpServiceProfile.getSubnetAddress());
    assertEquals(dhcpPool.getSubnetMask(), dhcpServiceProfile.getSubnetMask());
    assertEquals(dhcpPool.getStartIpAddress(), dhcpServiceProfile.getStartIpAddress());
    assertEquals(dhcpPool.getEndIpAddress(), dhcpServiceProfile.getEndIpAddress());
    return true;
  }

  private void validateRepositoryData(DhcpConfigServiceProfileDeep expected,
      DhcpConfigServiceProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getServiceName(), actual.getServiceName());
    assertEquals(expected.getDhcpMode(), actual.getDhcpMode());
    assertEquals(expected.getDhcpPools().size(), actual.getDhcpPools().size());

    for (DhcpPool expectedPool : expected.getDhcpPools()) {
      DhcpServiceProfile actualPool = actual.getDhcpPools().stream()
          .filter(pool -> pool.getName().equals(expectedPool.getName())).findFirst().orElse(null);
      assertNotNull(actualPool);
      assertEquals(expectedPool.getName(), actualPool.getName());
      assertEquals(expectedPool.getDescription(), actualPool.getDescription());
      assertEquals(expectedPool.getVlanId(), actualPool.getVlanId());
      assertEquals(expectedPool.getSubnetAddress(), actualPool.getSubnetAddress());
      assertEquals(expectedPool.getSubnetMask(), actualPool.getSubnetMask());
      assertEquals(expectedPool.getPrimaryDnsIp(), actualPool.getPrimaryDnsIp());
      assertEquals(expectedPool.getSecondaryDnsIp(), actualPool.getSecondaryDnsIp());
      assertEquals(expectedPool.getLeaseTimeHours(), actualPool.getLeaseTimeHours());
      assertEquals(expectedPool.getLeaseTimeMinutes(), actualPool.getLeaseTimeMinutes());
      assertEquals(expectedPool.getStartIpAddress(), actualPool.getStartIpAddress());
      assertEquals(expectedPool.getEndIpAddress(), actualPool.getEndIpAddress());
    }
  }

  private void validateNothingHappened(CfgAction action) {
    final TxChanges txChanges = revisionService.changes(txCtxExtension.getRequestId(),
        txCtxExtension.getTenantId(), action.key());
    assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
    messageCaptors.assertThat(
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest(),
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(txCtxExtension.getTenantId());
  }

  private void validateActivityMessages(String apiFlowNames) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> assertThat(
        activityCfgChangeRespMessage.getPayload()).matches(
            msg -> msg.getStatus().equals(Status.OK)).matches(msg -> msg.getStep().equals(apiFlowNames))
        .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(tenantId);  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE -> OpType.ADD;
      case UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE -> OpType.MOD;
      case DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettings(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // When - enable venue dhcp setting
    // enable profile with 3 dhcpPools & 4 dhcpAps
    var poolsAmount = 3;
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(poolsAmount, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, dhcpConfigServiceProfileDeep);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpConfigServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // assert result - getVenueTemplateDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, dhcpAps,
        true, poolsAmount);

    // assert result - getDhcpConfigServiceProfile
    {
      DhcpConfigServiceProfileDeep configServiceProfile = getDhcpConfigServiceProfileTemplateDeep(venueDhcpServiceProfileSetting.getServiceProfileId());
      assertEquals(1, configServiceProfile.getUsage().size());
      assertEquals(venueId, configServiceProfile.getUsage().get(0).getVenueId());
    }
    // assert result - getDhcpConfigServiceProfiles
    final DhcpConfigServiceProfileDeep finalConfigServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    assertDhcpConfigServiceProfile(finalConfigServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"),
        List.of(venueId));

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalConfigServiceProfile.getDhcpPools(),
        finalConfigServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), finalConfigServiceProfile.getDhcpPools(),
            result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    // expected invoke 1 dhcpConfigServiceProfile
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId, true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalConfigServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndBindToAnotherEachApProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venue dhcp setting
    // 3 dhcpPools & 4 dhcpAps in payload
    String dhcpServiceProfileId;
    {
      edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, DhcpConfigServiceProfileTestFixture
          .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
      assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
      DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
      dhcpServiceProfileId = dhcpServiceProfile.getId();
      VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
          venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
      edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
      assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);
    }

    // When - venue bind To Another Profile
    String anotherDhcpServiceProfileId;
    {
      edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, DhcpConfigServiceProfileTestFixture
          .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnEachAPs));
      assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
      var a = getDhcpConfigServiceProfileTemplateDeeps();
      DhcpConfigServiceProfileDeep anotherDhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps()
          .stream().filter(x -> !x.getId().equals(dhcpServiceProfileId)).findFirst().orElse(null);
      anotherDhcpServiceProfileId = anotherDhcpServiceProfile.getId();
      VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
          venueDhcpConfigServiceProfileSetting(true, dhcpAps, anotherDhcpServiceProfile.getId());
      venueDhcpServiceProfileSetting.setServiceProfileId(anotherDhcpServiceProfile.getId());
      venueDhcpServiceProfileSetting.setDhcpServiceAps(null);
      edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
      assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

      // assert result - getVenueTemplateDhcpConfigServiceProfileSetting
      VenueDhcpConfigServiceProfileSetting result = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
      assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, null,
          false, 0);
    }

    // assert result - getDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep anotherDhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeep(anotherDhcpServiceProfileId);
    assertEquals(1, anotherDhcpServiceProfile.getUsage().size());
    assertEquals(venueId, anotherDhcpServiceProfile.getUsage().get(0).getVenueId());
    assertNull(getDhcpConfigServiceProfileTemplateDeep(dhcpServiceProfileId).getUsage());

    // assert result - getDhcpConfigServiceProfiles
    List<DhcpConfigServiceProfileDeep> deeps = getDhcpConfigServiceProfileTemplateDeeps();
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile = deeps.stream()
        .filter(e -> e.getId().equals(dhcpServiceProfileId)).findFirst().orElse(null);
    DhcpConfigServiceProfileDeep finalAnotherDhcpServiceProfile = deeps.stream()
        .filter(e -> e.getId().equals(anotherDhcpServiceProfileId)).findFirst().orElse(null);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"), null);
    assertDhcpConfigServiceProfile(finalAnotherDhcpServiceProfile, "DhcpConfigServiceProfile#2",
        DhcpModeEnum.EnableOnEachAPs, List.of("DhcpPool#1", "DhcpPool#2"), List.of(venueId));

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalAnotherDhcpServiceProfile.getDhcpPools(),
        finalAnotherDhcpServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), finalAnotherDhcpServiceProfile.getDhcpPools(), null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 3),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId, true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalAnotherDhcpServiceProfile),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalDhcpServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndBindToAnotherHierarchicalProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);
    List<DhcpServiceAp> anotherDhcpAps = dhcpService2ApList(apGroup);

    // enable venue dhcp setting
    // 3 dhcpPools & 4 dhcpAps in payload
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - venue bind To Another Profile
    DhcpConfigServiceProfileDeep hierarchical = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnHierarchicalAPs);
    hierarchical.getDhcpPools().get(0).setVlanId((short) 1);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, hierarchical);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep anotherDhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps()
        .stream().filter(x -> !x.getId().equals(dhcpServiceProfile.getId())).findFirst().orElse(null);
    venueDhcpServiceProfileSetting.setServiceProfileId(anotherDhcpServiceProfile.getId());
    venueDhcpServiceProfileSetting.setDhcpServiceAps(anotherDhcpAps);
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // assert result - getVenueTemplateDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, anotherDhcpAps,
        false, 0);

    // assert result - getDhcpConfigServiceProfile
    anotherDhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeep(anotherDhcpServiceProfile.getId());
    var anotherDhcpServiceProfileId = anotherDhcpServiceProfile.getId();
    assertEquals(1, anotherDhcpServiceProfile.getUsage().size());
    assertEquals(venueId, anotherDhcpServiceProfile.getUsage().get(0).getVenueId());

    // assert result - getDhcpConfigServiceProfiles
    List<DhcpConfigServiceProfileDeep> deeps = getDhcpConfigServiceProfileTemplateDeeps();
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile = deeps.stream()
        .filter(e -> e.getId().equals(dhcpServiceProfile.getId())).findFirst().orElse(null);
    DhcpConfigServiceProfileDeep finalAnotherDhcpServiceProfile = deeps.stream()
        .filter(e -> e.getId().equals(anotherDhcpServiceProfileId)).findFirst().orElse(null);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"), null);
    assertNull(getDhcpConfigServiceProfileTemplate(dhcpServiceProfile.getId()).getDhcpPoolVenueUsage());
    assertDhcpConfigServiceProfile(finalAnotherDhcpServiceProfile, "DhcpConfigServiceProfile#2",
        DhcpModeEnum.EnableOnHierarchicalAPs, List.of("DhcpPool#1", "DhcpPool#2"), List.of(venueId));

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalAnotherDhcpServiceProfile.getDhcpPools(),
        finalAnotherDhcpServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnHierarchicalAPs.toString(), finalAnotherDhcpServiceProfile.getDhcpPools(), result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 3),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId, true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalAnotherDhcpServiceProfile),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalDhcpServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndDisableDhcpSettings(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfileSettings
    // 3 dhcpPools & 4 dhcpAps in payload
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - disable venue dhcp setting
    venueDhcpServiceProfileSetting.setEnabled(false);
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // assert result - getVenueTemplateDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    venueDhcpServiceProfileSetting.setServiceProfileId(null);
    venueDhcpServiceProfileSetting.setDhcpServiceAps(null);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, null,
        false, 0);

    // assert result - getDhcpConfigServiceProfile
    dhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeep(dhcpServiceProfile.getId());
    assertNull(dhcpServiceProfile.getUsage());

    // assert result - getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"),
        null);

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, null, null);

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, false,
            null, null, null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId, false),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalDhcpServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndUpdateSameSettings(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfileSettings
    // 3 dhcpPools & 4 dhcpAps in payload
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - update the same venue dhcp setting
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // assert result - getVenueTemplateDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, dhcpAps,
        true, 3);

    // assert result - getDhcpConfigServiceProfile
    dhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeep(venueDhcpServiceProfileSetting.getServiceProfileId());
    assertEquals(1, dhcpServiceProfile.getUsage().size());
    assertEquals(venueId, dhcpServiceProfile.getUsage().get(0).getVenueId());

    // assert result - getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"),
        List.of(venueId));

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalDhcpServiceProfile.getDhcpPools(),
        finalDhcpServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile1 = dhcpServiceProfile;
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), finalDhcpServiceProfile1.getDhcpPools(), result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 1),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalDhcpServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_dhcpApOverMaxAmount_fail(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);
    dhcpAps.addAll(dhcpService4ApList(apGroup));
    dhcpAps.addAll(dhcpService4ApList(apGroup));

    // When - EnableOnEachAPs over max amount fail
    // 3 dhcpPools & 12 dhcpAps in payload
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnEachAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);

    // Then - EnableOnEachAPs over max amount fail
    assertActivityStatusFail(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10155, tenantId);

    // When - EnableOnMultipleAPs over max amount fail
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    List<DhcpConfigServiceProfileDeep> dhcpConfigServiceProfiles = getDhcpConfigServiceProfileTemplateDeeps();
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpConfigServiceProfiles
            .stream()
            .filter(e -> !e.getId().equals(dhcpServiceProfile.getId()))
            .findFirst().orElse(null).getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpConfigServiceProfileSetting);

    // Then - EnableOnMultipleAPs over max amount fail
    assertActivityStatusFail(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10155, tenantId);
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_invalidDhcpServiceApRole_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    String ap2SN = randomSerialNumber();
    createAp(apGroup, ap2SN, "R720", randomMacAddress());
    List<DhcpServiceAp> dhcpServiceApList = new LinkedList<>();
    dhcpServiceApList.add(createDhcpServiceAp(ap2SN, ApDhcpRoleEnum.BackupServer));

    // When - EnableOnMultipleAPs invalidDhcpServiceApRole
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    List<DhcpConfigServiceProfileDeep> dhcpConfigServiceProfiles = getDhcpConfigServiceProfileTemplateDeeps();
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpServiceApList, dhcpConfigServiceProfiles.get(0).getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpConfigServiceProfileSetting);

    // Then - EnableOnMultipleAPs over max amount fail
    assertActivityStatusFail(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10135, tenantId);
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_disableVenueWithCellularAp_fail(Tenant tenant, @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion("*******.111") ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");
    createAp(apGroup, randomSerialNumber(), "M510", randomMacAddress());
    var venueId = v.getId();

    // EnableOnEachAPs
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnEachAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    List<DhcpConfigServiceProfileDeep> dhcpConfigServiceProfiles = getDhcpConfigServiceProfileTemplateDeeps();
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, null, dhcpConfigServiceProfiles.get(0).getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpConfigServiceProfileSetting);

    // When - disable venueDhcpConfigServiceProfileSetting with cellular ap
    venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(false, null, dhcpConfigServiceProfiles.get(0).getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpConfigServiceProfileSetting);

    // Then - fail
    assertActivityStatusFail(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10139, tenantId);
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_EnableDhcpSettingsWithNetworkVlanNotInclude_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService2ApList(apGroup);

    // add network bind venue
    com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork networkDeepReq = pskNetwork("psk").generate();
    networkDeepReq.getWlan().setVlanId((short) 5);
    PskNetwork psk = addPskNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, psk.getId(), v.getId(), null);

    // When - enable venue dhcp setting
    // add dhcpConfigServiceProfile - EnableOnHierarchicalAPs
    DhcpConfigServiceProfileDeep hierarchical = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnHierarchicalAPs);
    hierarchical.getDhcpPools().get(0).setVlanId((short) 1);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, hierarchical);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    List<DhcpConfigServiceProfileDeep> dhcpConfigServiceProfiles = getDhcpConfigServiceProfileTemplateDeeps();
    DhcpConfigServiceProfileDeep customDhcpConfigServiceProfile = dhcpConfigServiceProfiles.get(0);

    // enable venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, customDhcpConfigServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpConfigServiceProfileSetting);

    // Then
    // assert activity
    assertActivityStatusFail(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10169, tenantId);
  }

  @Test
  void testDeactivateVenueDhcpPool_ActivateVenueDhcpPool(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, getDhcpConfigServiceProfileTemplateDeeps().get(0).getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - deactivate 1 dhcpPool
    final DhcpConfigServiceProfileDeep finalConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateDeep(venueDhcpServiceProfileSetting.getServiceProfileId());
    DhcpPool deactivatePool = finalConfigServiceProfile.getDhcpPools().get(2);
    edaDeactivateTemplateDhcpPool(tenantId, userName, venueId, deactivatePool.getId());

    // Then - deactivate 1 dhcpPool
    // assert activity
    assertActivityStatusSuccess(DEACTIVATE_VENUE_TEMPLATE_DHCP_POOL, tenantId);

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    List<DhcpPool> activeDhcpPools = finalConfigServiceProfile.getDhcpPools().stream()
        .filter(dhcpPool -> dhcpPool != deactivatePool).collect(Collectors.toList());
    assertDhcpPoolUsage(poolUsages, finalConfigServiceProfile.getDhcpPools(), activeDhcpPools);

    // assert result - getVenueTemplateDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueTemplateDhcpConfigServiceProfileSetting(
        venueId);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, dhcpAps,
        true, 2);

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), activeDhcpPools, result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    assertCmnCfgCollectorCfgRequestNotSent(tenantId);

    // When - activate 1 dhcpPool
    edaActivateTemplateDhcpPool(tenantId, userName, venueId, deactivatePool.getId());

    // Then - activate 1 dhcpPool
    // assert activity
    assertActivityStatusSuccess(ACTIVATE_VENUE_TEMPLATE_DHCP_POOL, tenantId);
    // assert result - getVenueTemplateDhcpPoolUsage
    poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalConfigServiceProfile.getDhcpPools(),
        finalConfigServiceProfile.getDhcpPools());
    VenueDhcpConfigServiceProfileSetting result2 = getVenueTemplateDhcpConfigServiceProfileSetting(
        venueId);
    assertVenueDhcpConfigServiceProfileSetting(result2, venueDhcpServiceProfileSetting, dhcpAps,
        true, 3);

    // assert ddccm
    var activateDdccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(activateDdccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(activateDdccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), finalConfigServiceProfile.getDhcpPools(), result2.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    assertCmnCfgCollectorCfgRequestNotSent(tenantId);
  }

  @Test
  void testDeactivateVenueDhcpPool_deactivateLastDhcpPool_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep savedConfigServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, savedConfigServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - deactivate last dhcpPool
    DhcpPool deactivatePool = configServiceProfile.getDhcpPools().get(0);
    edaDeactivateTemplateDhcpPool(tenantId, userName, venueId, deactivatePool.getId());

    // Then - deactivate last dhcpPool should disable venue dhcp setting
    // assert activity
    assertActivityStatusFail(DEACTIVATE_VENUE_TEMPLATE_DHCP_POOL, Errors.WIFI_10420, tenantId);

    // assert ddccm, cmn-cfg-collector
    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());
  }

  @Test
  void testActivateVenueDhcpPool_deactivateVenuePool_withVenueDhcpConfigSettingsDisabled_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();

    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);

    // When - activate 1 dhcpPool
    String dhcpPoolId1 = configServiceProfile.getDhcpPools().get(0).getId();
    edaActivateTemplateDhcpPool(tenantId, userName, venueId, dhcpPoolId1);

    // Then - activate 1 dhcpPool
    // assert activity
    assertActivityStatusFail(ACTIVATE_VENUE_TEMPLATE_DHCP_POOL, Errors.WIFI_10419, tenantId);

    // When - deactivate 1 dhcpPool
    edaDeactivateTemplateDhcpPool(tenantId, userName, venueId, dhcpPoolId1);

    // Then - deactivate 1 dhcpPool
    // assert activity
    assertActivityStatusFail(DEACTIVATE_VENUE_TEMPLATE_DHCP_POOL, Errors.WIFI_10419, tenantId);
  }

  @Test
  void testActivateVenueDhcpPool_activatePoolIdNotExistInDhcpConfigServiceProfile_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // update venueDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep savedConfigServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, savedConfigServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - activate 1 dhcpPool
    edaActivateTemplateDhcpPool(tenantId, userName, venueId, "otherDhcpPoolId");

    // Then - activate 1 dhcpPool
    // assert activity
    assertActivityStatusFail(ACTIVATE_VENUE_TEMPLATE_DHCP_POOL, Errors.WIFI_10420, tenantId);
  }

  private List<ConnectedClientDTO> getMockDhcpClients() {
    //mock ViewModelAgent
    List<ConnectedClientDTO> mockClients = new ArrayList<>();
    ConnectedClientDTO mockClient1 = new ConnectedClientDTO();
    mockClient1.setHostname("hostname");
    mockClient1.setClientMac("E4:A7:A0:D4:15:84");
    mockClients.add(mockClient1);
    ConnectedClientDTO mockClient2 = new ConnectedClientDTO();
    mockClient2.setHostname("hostname 2");
    mockClient2.setClientMac("f4:a7:a0:d4:15:99");
    mockClients.add(mockClient2);

    return mockClients;
  }

  @Test
  void testaddGuestNetworkTemplateWithDhcpEnableTwice_venueWithoutDhcpConfigServiceProfile_Successfully(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // When - addGuestNetworkTemplate1
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setName("Guest Deep 1");
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n1 = addGuestNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, n1.getId(), venueId, null);

    // Then - addGuestNetworkTemplate1
    // assert result - network
    assertNotNull(n1);
    assertTrue(n1.getEnableDhcp());
    assertNotNull(n1.getPortalServiceProfileId());

    // assert result - getDhcpConfigServiceProfiles
    final DhcpConfigServiceProfileDeep defaultDhcpServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultDhcpServiceProfile);
    assertEquals(venueId, defaultDhcpServiceProfile.getVenueIds().get(0));

    // assert result - venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedVenueProfileSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedVenueProfileSetting.setEnabled(true); // venue dhcp should be enabled
    expectedVenueProfileSetting.setServiceProfileId(defaultDhcpServiceProfile.getId());
    assertVenueDhcpConfigServiceProfileSetting(venueDhcpConfigServiceProfileSetting,
        expectedVenueProfileSetting, null, false, 0);

    // assert ddccm
    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccm(ddccmOperations, 5),
          () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD, defaultDhcpServiceProfile.getDhcpPools()),
          () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
              DhcpModeEnum.EnableOnEachAPs.toString(), defaultDhcpServiceProfile.getDhcpPools(), null)
      );
    }

    // assert cmn-cfg-collector
    {
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertCmnViewModelCollector(viewmodelOperations, 4),
          () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.ADD, defaultDhcpServiceProfile)
      );
    }

    // When - addGuestNetworkTemplate2
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq2 = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq2.setName("Guest Deep 2");
    networkDeepReq2.setEnableDhcp(true);
    networkDeepReq2.setPortalServiceProfileId(networkDeepReq.getPortalServiceProfileId());
    GuestNetwork n2 = addGuestNetworkTemplate(map(networkDeepReq2)); // This can't reuse networkDeepReq somehow in wifi-eda
    edaAddNetworkVenueTemplateMapping(tenantId, userName, n2.getId(), venueId);

    // Then - addGuestNetworkTemplate2
    // assert result - network
    assertTrue(n2.getEnableDhcp());
    assertNotNull(n2.getPortalServiceProfileId());

    // assert result - getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep defaultDhcpServiceProfile2 = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultDhcpServiceProfile2);
    assertEquals(venueId, defaultDhcpServiceProfile2.getVenueIds().get(0));

    // assert result - venueDhcpConfigServiceProfileSetting
    venueDhcpConfigServiceProfileSetting = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    assertVenueDhcpConfigServiceProfileSetting(venueDhcpConfigServiceProfileSetting,
        expectedVenueProfileSetting, null, false, 0);

    // assert ddccm
    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertDdccmNoDhcpServiceProfile(ddccmOperations);
      assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations);
    }

    // assert cmn-cfg-collector
    {
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertCmnViewModelCollector(viewmodelOperations, 2),
          () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
      );
    }
  }

  private void assertDefaultDhcpConfigServiceProfile(DhcpConfigServiceProfileDeep result) {
    assertEquals(defaultGuestNetworkConfig.getName(), result.getServiceName(),
        "Default DhcpConfigServiceProfile name should equal DHCP-Guest");
    assertEquals(DhcpModeEnum.EnableOnEachAPs, result.getDhcpMode(),
        "Default DhcpConfigServiceProfile mode should equal EnableOnEachAPs");
    assertEquals(1, result.getDhcpPools().size(),
        "Default DhcpConfigServiceProfile should have 1 dhcpPool");

    DhcpPool defaultDhcpPool = result.getDhcpPools().get(0);
    assertEquals(defaultGuestNetworkConfig.getName(), defaultDhcpPool.getName(),
        "Default DhcpPool name should equal DHCP-Guest");
    assertEquals((short) 3000, (short) defaultDhcpPool.getVlanId(),
        "Default DhcpPool vlanId should equal 3000");
  }

  @Test
  void testaddGuestNetworkTemplateWithDhcpEnable_venueWithDhcpConfigProfile_Successfully(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // add dhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep customConfigServiceProfileDeep = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, customConfigServiceProfileDeep.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - addGuestNetworkTemplate
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setName("Guest Deep 1");
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork addedNetwork = addGuestNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, addedNetwork.getId(), venueId, null);

    // Then - addGuestNetworkTemplate result
    assertTrue(addedNetwork.getEnableDhcp());
    assertNotNull(addedNetwork.getPortalServiceProfileId());

    // assert venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedSetting.setEnabled(true);
    expectedSetting.setServiceProfileId(customConfigServiceProfileDeep.getId());
    assertVenueDhcpConfigServiceProfileSetting(venueDhcpConfigServiceProfileSetting,
        expectedSetting, dhcpAps, true, 0);

    // assert getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> dhcpPoolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(dhcpPoolUsages, customConfigServiceProfileDeep.getDhcpPools(),
        customConfigServiceProfileDeep.getDhcpPools());

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 7),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 4),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  void testUpdateGuestNetworkWithDhcp_fromDhcpEnable_toDhcpDisable(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());
    // When - updateGuestNetwork to dhcp disable
    n.setEnableDhcp(false);
    edaUpdateNetworkTemplate(tenantId, userName, n.getId(), map(n));

    // Then
    GuestNetwork updatedNetwork = (GuestNetwork) getNetworkTemplate(n.getId());
    assertFalse(updatedNetwork.getEnableDhcp());
    assertNotNull(updatedNetwork.getPortalServiceProfileId());

    // assert ddccm - VenueDhcpServiceSetting
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  void testUpdateGuestNetworkWithDhcp_fromDhcpDisable_toDhcpEnable(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(false);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());
    // When - updateGuestNetwork to dhcp disable
    n.setEnableDhcp(true);
    edaUpdateNetworkTemplate(tenantId, userName, n.getId(), map(n));

    // Then
    GuestNetwork updatedNetwork = (GuestNetwork) getNetworkTemplate(n.getId());
    assertTrue(updatedNetwork.getEnableDhcp());
    assertNotNull(updatedNetwork.getPortalServiceProfileId());

    // assert getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep defaultProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultProfile);
    assertEquals(venueId, defaultProfile.getVenueIds().get(0));

    // assert getVenueTemplateDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting actualSetting = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedSetting.setEnabled(true);
    expectedSetting.setServiceProfileId(defaultProfile.getId());
    assertVenueDhcpConfigServiceProfileSetting(actualSetting, expectedSetting, null, false, 0);

    // assert ddccm - VenueDhcpServiceSetting
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 5),
        () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD, defaultProfile.getDhcpPools()),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), defaultProfile.getDhcpPools(), null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 4),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations,
            OpType.ADD, defaultProfile)
    );
  }

  @Test
  void testaddGuestNetworkTemplateVenueWithNetworkDhcpEnable_venueWithoutDhcpConfigProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());
    // When - add NetworkVenue
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);

    // Then - add NetworkVenue
    // assert getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep defaultProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultProfile);
    assertEquals(venueId, defaultProfile.getVenueIds().get(0));

    // assert getVenueTemplateDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting actualSetting = getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedSetting.setEnabled(true);
    expectedSetting.setServiceProfileId(defaultProfile.getId());
    assertVenueDhcpConfigServiceProfileSetting(actualSetting, expectedSetting, null, false, 0);

    // assert ddccm - VenueDhcpServiceSetting
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 5),
        () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD, defaultProfile.getDhcpPools()),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), defaultProfile.getDhcpPools(), null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 4),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations,
            OpType.ADD, defaultProfile)
    );
  }

  @Test
  void testaddGuestNetworkTemplateVenueWithNetworkDhcpEnable_venueWithDhcpConfigProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());

    // add dhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnEachAPs);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);

    // enable venue dhcp
    DhcpConfigServiceProfileDeep customConfigServiceProfileDeep = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, null, customConfigServiceProfileDeep.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - add NetworkVenue
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);

    // Then - add NetworkVenue
    // assert venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedSetting.setEnabled(true);
    expectedSetting.setServiceProfileId(customConfigServiceProfileDeep.getId());
    assertVenueDhcpConfigServiceProfileSetting(venueDhcpConfigServiceProfileSetting,
        expectedSetting, null, false, 0);

    // assert getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> dhcpPoolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(dhcpPoolUsages, customConfigServiceProfileDeep.getDhcpPools(), customConfigServiceProfileDeep.getDhcpPools());

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  public void testDeleteGuestNetworkVenueWithNetworkDhcpEnable_Successfully(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());

    // add networkVenue
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);
    NetworkVenue nv = retrieveTemplate(() -> getNetworkVenueByNetworkAndVenue(n.getId(), venueId));
    String networkVenueId = nv.getId();

    // When - delete networkVenue
    edaDeleteNetworkVenueTemplate(tenantId, userName, networkVenueId);

    // Then - delete networkVenue
    // assert result
    DhcpConfigServiceProfileDeep defaultProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultProfile);
    assertEquals(venueId, defaultProfile.getVenueIds().get(0));

    // assert venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting actualVenueSetting =
        getVenueTemplateDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedVenueSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedVenueSetting.setEnabled(true);
    expectedVenueSetting.setServiceProfileId(defaultProfile.getId());
    assertVenueDhcpConfigServiceProfileSetting(actualVenueSetting, expectedVenueSetting, null,
        false, 0);

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  //TODO wait for template client-isolation-profile
  @Disabled("wait for template client-isolation-profile")
  @Test
  void testUpdateNetworkVenueClientIsolationAllowlist_whenDhcpServiceEnabled_ShouldBeDisallowed(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");

    // AddClientIsolationAllowlist
    ClientIsolationAllowlist allowlist = clientIsolationAllowlist("allowlist");
    addEdaClientIsolationProfile(tenantId, userName, mapToClientIsolationProfile(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_PROFILE, tenantId); //FIXME ADD_CLIENT_ISOLATION_PROFILE_TEMPLATE

    List<ClientIsolationAllowlist> allowlists = getClientIsolationAllowlists();
    assertEquals(1, allowlists.size());
    String clientIsolationAllowlistId = allowlists.get(0).getId();

    // add network
    PskNetwork pskNetwork = addPskNetworkTemplate(map(pskNetwork("pskNetwork").generate()));

    // add dhcp
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnEachAPs);
    edaAddDhcpConfigServiceProfileTemplate(tenantId, userName, dhcpConfigServiceProfileDeep);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfile = getDhcpConfigServiceProfileTemplateDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, null, dhcpConfigServiceProfile.getId());
    edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(tenantId, userName, v.getId(), venueDhcpConfigServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    edaAddNetworkVenueTemplate(tenantId, userName, pskNetwork.getId(), v.getId(), clientIsolationAllowlistId);
    assertActivityStatusFail(ADD_NETWORK_VENUE_TEMPLATE, Errors.WIFI_10243, tenantId);

    edaAddNetworkVenueTemplate(tenantId, userName, pskNetwork.getId(), v.getId(), null);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE_TEMPLATE, tenantId);

    NetworkVenue nv = getNetworkVenueByNetworkAndVenue(pskNetwork.getId(), v.getId());
    edaUpdateNetworkVenueTemplate(tenantId, userName, nv.getId(), map(nv), clientIsolationAllowlistId);
    assertActivityStatusFail(UPDATE_NETWORK_VENUE_TEMPLATE, Errors.WIFI_10243, tenantId);
  }

  @SneakyThrows
  private List<DhcpServiceAp> dhcpService4ApList(ApGroup apGroup) {
    String ap1SN = randomSerialNumber();
    String ap2SN = randomSerialNumber();
    String ap3SN = randomSerialNumber();
    String ap4SN = randomSerialNumber();

    createAp(apGroup, ap1SN, "R510", randomMacAddress());
    createAp(apGroup, ap2SN, "R720", randomMacAddress());
    createAp(apGroup, ap3SN, "R750", randomMacAddress());
    createAp(apGroup, ap4SN, "R320", randomMacAddress());

    List<DhcpServiceAp> dhcpServiceApList = new LinkedList<>();
    dhcpServiceApList.add(createDhcpServiceAp(ap1SN, ApDhcpRoleEnum.PrimaryServer));
    dhcpServiceApList.add(createDhcpServiceAp(ap2SN, ApDhcpRoleEnum.BackupServer));
    dhcpServiceApList.add(createDhcpServiceAp(ap3SN, ApDhcpRoleEnum.NatGateway));
    dhcpServiceApList.add(createDhcpServiceAp(ap4SN, ApDhcpRoleEnum.NatGateway));

    return dhcpServiceApList;
  }

  @SneakyThrows
  private List<DhcpServiceAp> dhcpService2ApList(ApGroup apGroup) {
    String ap1SN = randomSerialNumber();
    String ap2SN = randomSerialNumber();

    createAp(apGroup, ap1SN, "R510", randomMacAddress());
    createAp(apGroup, ap2SN, "R720", randomMacAddress());

    List<DhcpServiceAp> dhcpServiceApList = new LinkedList<>();
    dhcpServiceApList.add(createDhcpServiceAp(ap1SN, ApDhcpRoleEnum.PrimaryServer));
    dhcpServiceApList.add(createDhcpServiceAp(ap2SN, ApDhcpRoleEnum.BackupServer));

    return dhcpServiceApList;
  }

  private DhcpServiceAp createDhcpServiceAp(String serialNumber, ApDhcpRoleEnum role) {
    DhcpServiceAp dhcpServiceAp = new DhcpServiceAp();
    dhcpServiceAp.setSerialNumber(serialNumber);
    dhcpServiceAp.setRole(role);
    return dhcpServiceAp;
  }

  private void assertVenueDhcpConfigServiceProfileSetting(
      VenueDhcpConfigServiceProfileSetting result,
      VenueDhcpConfigServiceProfileSetting expectedVenueDhcpServiceProfileSetting,
      List<DhcpServiceAp> expectedDhcpAps, boolean shouldAssertIp, int ipAmount) {
    assertEquals(expectedVenueDhcpServiceProfileSetting.getServiceProfileId(), result.getServiceProfileId(),
        "ServiceProfileId should equal test data");
    assertEquals(expectedVenueDhcpServiceProfileSetting.getEnabled(), result.getEnabled(),
        "VenueDhcpServiceProfileSetting should be enabled / disabled");

    if (expectedDhcpAps != null) {
      List<String> expectedSerialNumbers = expectedDhcpAps.stream()
          .map(DhcpServiceAp::getSerialNumber).collect(Collectors.toList());
      List<String> actualSerialNumbers = result.getDhcpServiceAps().stream()
          .map(DhcpServiceAp::getSerialNumber).collect(Collectors.toList());
      DhcpServiceAp primaryDhcpSeviceAp = result.getDhcpServiceAps().stream()
          .filter(ap -> ApDhcpRoleEnum.PrimaryServer.equals(ap.getRole())).findFirst().get();
      DhcpServiceAp backupDhcpSeviceAp = result.getDhcpServiceAps().stream()
          .filter(ap -> ApDhcpRoleEnum.BackupServer.equals(ap.getRole())).findFirst().get();
      assertEquals(expectedDhcpAps.size(), result.getDhcpServiceAps().size(),
          "Should have DhcpServiceAps");
      assertTrue(actualSerialNumbers.containsAll(expectedSerialNumbers),
          "DhcpServiceAps serial numbers should equal test data");

      if (shouldAssertIp && ipAmount > 0) {
        List<String> primaryIps = Arrays.asList("***********", "***********", "***********",
            "***********");
        List<String> secondaryIps = Arrays.asList("***********", "***********", "***********",
            "***********");
        assertTrue(primaryDhcpSeviceAp.getDhcpIps().containsAll(primaryIps.subList(0, ipAmount)),
            "Primary DhcpServiceAp IP should equal test data");
        assertTrue(backupDhcpSeviceAp.getDhcpIps().containsAll(secondaryIps.subList(0, ipAmount)),
            "Secondary DhcpServiceAp IP should equal test data");
      }
    } else {
      assertEquals(0, result.getDhcpServiceAps().size(), "Should have no DhcpServiceAps");
    }
  }

  private void assertDhcpConfigServiceProfile(DhcpConfigServiceProfileDeep result,
                                              String expectedServiceName, DhcpModeEnum expectedDhcpMode, List<String> expectedDhcpPoolNames,
                                              List<String> expectedVenueIds) {
    assertEquals(expectedServiceName, result.getServiceName());
    assertEquals(expectedDhcpMode, result.getDhcpMode());
    assertEquals(expectedDhcpPoolNames.size(), result.getDhcpPools().size());
    List<String> actualDhcpPoolNames = result.getDhcpPools().stream().map(DhcpPool::getName)
        .collect(Collectors.toList());
    assertTrue(actualDhcpPoolNames.containsAll(expectedDhcpPoolNames));
    if (expectedVenueIds != null) {
      assertEquals(expectedVenueIds.size(), result.getVenueIds().size());
      assertTrue(result.getVenueIds().containsAll(expectedVenueIds));
      assertNull(result.getUsage());
    } else {
      assertEquals(0, result.getVenueIds().size());
    }
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getVlanId).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getSubnetAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getSubnetMask).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getStartIpAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getEndIpAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getLeaseTimeHours).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getLeaseTimeMinutes).count());
  }

  private void assertDhcpPoolUsage(List<DhcpPoolUsage> poolUsages,
                                   List<DhcpPool> expectedAllDhcpPools, List<DhcpPool> expectedActiveDhcpPools) {
    if (expectedAllDhcpPools == null) {
      assertEquals(0, poolUsages.size());
      return;
    }
    List<String> dhcpPoolIds = expectedAllDhcpPools.stream().map(DhcpPool::getId)
        .collect(Collectors.toList());
    List<Short> vlanIds = expectedAllDhcpPools.stream().map(DhcpPool::getVlanId)
        .collect(Collectors.toList());
    List<String> startIpAddress = expectedAllDhcpPools.stream().map(DhcpPool::getStartIpAddress)
        .collect(Collectors.toList());
    List<String> subnetAddress = expectedAllDhcpPools.stream().map(DhcpPool::getSubnetAddress)
        .collect(Collectors.toList());
    assertEquals(expectedAllDhcpPools.size(), poolUsages.size(), "Should have dhcpPool");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getId).collect(Collectors.toList())
        .containsAll(dhcpPoolIds), "dhcpPoolIds should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getVlanId).collect(Collectors.toList())
        .containsAll(vlanIds), "vlanId should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getStartIpAddress).collect(Collectors.toList())
        .containsAll(startIpAddress), "startIpAddress should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getSubnetAddress).collect(Collectors.toList())
        .containsAll(subnetAddress), "subnetAddress should equal test data");

    List<String> activeUsageIds = poolUsages.stream().filter(DhcpPoolUsage::getActive)
        .map(DhcpPoolUsage::getId).collect(Collectors.toList());
    if (expectedActiveDhcpPools != null) {
      assertEquals(expectedActiveDhcpPools.size(),
          activeUsageIds.size(), "Should have active dhcpPool");
      assertTrue(expectedActiveDhcpPools.stream().map(DhcpPool::getId).collect(Collectors.toList())
          .containsAll(activeUsageIds));
    } else {
      assertEquals(0, activeUsageIds.size(), "Should have no active dhcpPool");
    }
  }

  private void assertDdccmVenueAndVenueDhcpSetting(List<Operation> operations,
      boolean shouldAssertVenue, boolean expectedEnabled, String dhcpModeEnum,
      List<DhcpPool> expectedDhcpPools, List<DhcpServiceAp> expectedDhcpServiceAps) {

    // assert ddccm - venue
    List<com.ruckus.acx.ddccm.protobuf.wifi.Venue> ddccmVenue = operations.stream()
        .filter(o -> o.getAction().equals(Action.MODIFY))
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUE)).map(Operation::getVenue)
        .collect(Collectors.toList());
    if (shouldAssertVenue) {
      assertEquals(1, ddccmVenue.size(), "Should have one Venue operation send to ddccm");
    } else {
      assertEquals(0, ddccmVenue.size(), "Should have no Venue operation send to ddccm");
    }

    // assert ddccm - VenueDhcpServiceSetting
    List<VenueDhcpServiceSetting> ddccmVenueDhcpServiceSettings = operations.stream()
        .filter(o -> o.getAction().equals(Action.ADD)) // VENUEDHCPSERVICESETTING action is always add
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUEDHCPSERVICESETTING))
        .map(Operation::getVenueDhcpServiceSetting).collect(Collectors.toList());

    assertEquals(1, ddccmVenueDhcpServiceSettings.size(),
        "Should have one VenueDhcpServiceSetting operation send to ddccm");
    VenueDhcpServiceSetting ddccmVenueDhcpServiceSetting = ddccmVenueDhcpServiceSettings.get(0);
    assertEquals(expectedEnabled, ddccmVenueDhcpServiceSetting.getEnabled().getValue(),
        "Ddccm VenueDhcpServiceSetting enabled should be " + expectedEnabled);

    if (expectedEnabled) {
      assertEquals(dhcpModeEnum, ddccmVenueDhcpServiceSetting.getMode(),
          "Ddccm VenueDhcpServiceSetting mode error");
      assertEquals(
          expectedDhcpPools.size(),
          ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().size(),
          "Ddccm VenueDhcpServiceSetting should contain dhcpServiceProfileIds(dhcpPoolIds)");
      assertTrue(
          ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().containsAll(
              expectedDhcpPools.stream().map(DhcpPool::getId).collect(Collectors.toList())),
          "Ddccm VenueDhcpServiceSetting dhcpServiceProfileIds(dhcpPoolIds) should equal test data");

      if (expectedDhcpServiceAps != null) {
        assertEquals(expectedDhcpServiceAps.size(),
            ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
            "Ddccm Should have DhcpServiceAps");
        List<String> expectedIps = expectedDhcpServiceAps.stream()
            .flatMap(ap -> ap.getDhcpIps().stream()).collect(Collectors.toList());
        List<String> actualIps = ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().stream()
            .flatMap(ap -> ap.getNatGatewayIpList().stream()).collect(Collectors.toList());
        assertEquals(expectedIps.size(), actualIps.size());
        assertTrue(actualIps.containsAll(expectedIps),
            "Ddccm VenueDhcpServiceSetting ip should equal test data");
      } else {
        assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
            "Ddccm Should have no DhcpServiceAps");
      }
    } else {
      assertEquals("", ddccmVenueDhcpServiceSetting.getMode());
      assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().size(),
          "Ddccm VenueDhcpServiceSetting should have no dhcpServiceProfileIds(dhcpPoolIds)");
      assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
          "Ddccm Should have no DhcpServiceAps");
    }
  }

  private void assertCmnViewModelCollectorDhcpConfigServiceProfile(
      List<Operations> operations, String venueId, boolean enabled) {
    assertTrue(operations.stream()
        .filter(o -> venueId.equals(o.getId()))
        .allMatch(o -> o.getDocMap().get("dhcp").getStructValue().getFieldsMap()
            .get("enabled").getBoolValue() == enabled));
  }

  private void assertCmnViewModelCollectorDhcpConfigServiceProfile(
      List<Operations> operations, OpType expectedOpType, DhcpConfigServiceProfileDeep expectedServiceProfile) {
    List<Operations> viewModelOperations = operations.stream()
        .filter(o -> expectedServiceProfile.getId().equals(o.getId())).collect(Collectors.toList());
    Operations operation = viewModelOperations.stream()
        .filter(o -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .findFirst().get();
    assertEquals(expectedOpType, operation.getOpType());

    if (!operation.getOpType().equals(OpType.DEL)) {
      assertEquals(expectedServiceProfile.getServiceName(),
          operation.getDocMap().get(Key.NAME).getStringValue());
      assertEquals(expectedServiceProfile.getDhcpPools().size(),
          operation.getDocMap().get(Key.DHCP_POOLS)
              .getListValue().getValuesCount());
      assertEquals(true,
          operation.getDocMap().get(Key.IS_TEMPLATE)
              .getBoolValue());

      List<String> venueIds = operation.getDocMap()
          .get(Key.VENUE_IDS).getListValue().getValuesList().stream()
          .map(Value::getStringValue).collect(Collectors.toList());
      if (expectedServiceProfile.getVenueIds() != null) {
        List<String> expectedVenueIds = expectedServiceProfile.getVenueIds();
        assertEquals(expectedVenueIds.size(), venueIds.size());
        assertTrue(expectedVenueIds.containsAll(venueIds));
      } else {
        assertEquals(0, venueIds.size());
      }
    }
  }

  private void assertCmnViewModelCollectorNoDhcpConfigServiceProfile(List<Operations> operations) {
    assertTrue(operations.stream().noneMatch(o -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex())));
  }

  private void assertDdccmDhcpServiceProfile(
      List<Operation> operations, Action expectedAction, List<DhcpPool> expectedDhcpPools) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile> ddccmDhcpServiceProfiles = operations
        .stream()
        .filter(o -> o.getAction().equals(expectedAction))
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.DHCPSERVICEPROFILE))
        .map(Operation::getDhcpServiceProfile).collect(Collectors.toList());

    // assert dhcpPoolId
    List<String> expectedDhcpPoolIds = expectedDhcpPools.stream().map(DhcpPool::getId)
        .collect(Collectors.toList());
    List<String> actualDhcpPoolIds = ddccmDhcpServiceProfiles.stream()
        .map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getId).collect(Collectors.toList());
    assertEquals(expectedDhcpPoolIds.size(), actualDhcpPoolIds.size());
    assertTrue(actualDhcpPoolIds.containsAll(expectedDhcpPoolIds));

    // assert dhcpPoolName
    List<String> expectedDhcpPoolNames = expectedDhcpPools.stream().map(DhcpPool::getName)
        .collect(Collectors.toList());
    List<String> actualDhcpPoolNames = ddccmDhcpServiceProfiles.stream()
        .map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getName).collect(Collectors.toList());
    assertEquals(expectedDhcpPoolNames.size(), actualDhcpPoolNames.size());
    assertTrue(ddccmDhcpServiceProfiles.stream().map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getName)
        .collect(Collectors.toList()).containsAll(actualDhcpPoolNames));
  }

  private void assertDdccm(List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertCmnViewModelCollector(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertDdccmNoDhcpServiceProfile(List<Operation> operations) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile> ddccmDhcpServiceProfiles = operations
        .stream()
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.DHCPSERVICEPROFILE))
        .map(Operation::getDhcpServiceProfile).collect(Collectors.toList());
    assertEquals(0, ddccmDhcpServiceProfiles.size(),
        "Should have no DhcpServiceProfile send to ddccm");
  }

  private void assertDdccmNoVenueAndVenueDhcpSetting(List<Operation> operations) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.Venue> ddccmVenue = operations
        .stream()
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUE)).map(Operation::getVenue)
        .collect(Collectors.toList());
    assertEquals(0, ddccmVenue.size(),
        "Should have no Venue operation send to ddccm");
  }
}
