package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_VENUE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_RADIUS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_RADIUS_SERVER_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_RADIUS_SERVER_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_RADIUS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_RADIUS_SERVER_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK_VENUE_MAPPING;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTimeSlot;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.util.TemplateRetriever.retrieveTemplate;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.service.RadiusTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusServer;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.repository.RadiusRepository;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.TemplateRetriever;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Tag("RadiusServerProfileTemplateTest")
@WifiIntegrationTest
public class ConsumeRadiusServerProfileTemplateRequestTest extends AbstractRadiusRequestTest {

  @Autowired
  private RadiusTemplateServiceCtrl radiusTemplateServiceCtrl;

  @Autowired
  private RadiusRepository radiusRepository;

  private void assertRadiusServers(Radius expectedRadius, Radius actualRadius) {
    assertRadiusCommon(expectedRadius, actualRadius);
    assertTrue(actualRadius.getIsTemplate());
  }

  @Test
  public void testAddRadiusTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // normal case
    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-auth");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius1.setIsTemplate(true);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);

    List<Radius> radiusList = repositoryUtil.findAll(Radius.class, tenantId, true);
    assertEquals(1, radiusList.size());

    Radius authRadiusTemplateCreated = radiusList.get(0);
    assertRadiusServers(radius1, authRadiusTemplateCreated);
    assertRadiusNetworks(authRadiusTemplateCreated, 0);
    assertTrue(authRadiusTemplateCreated.getIsTemplate());

    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps {}", viewOps);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertRadiusIndexViewmodel(viewOps, OpType.ADD, authRadiusTemplateCreated, 0)
    );

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());

    //////////
    // adding Radius Template with the same name should fail
    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-auth"); // name conflict
    radius2.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    String radius2Id = radius2.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius2)); // expect to fail
    assertActivityStatusFail(ADD_RADIUS_SERVER_PROFILE_TEMPLATE, Errors.WIFI_10465, tenantId);

    assertThrows(ObjectNotFoundException.class, () -> {
      radiusServiceCtrl.getRadiusServerProfile(radius2Id);
    });

    //////////
    // adding Radius with the same name should pass
    Radius radius3 = RadiusTestFixture.authRadius();
    radius3.setName("test-radius-auth");
    radius3.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    String radius3Id = radius3.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius3));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);

    Radius authRadiusCreated = radiusServiceCtrl.getRadiusServerProfile(radius3Id);
    assertNotNull(authRadiusCreated);

    //////////
    // adding another Radius with the same name should fail
    Radius radius4 = RadiusTestFixture.authRadius();
    radius4.setName("test-radius-auth");
    radius4.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    String radius4Id = radius4.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius4));
    assertActivityStatusFail(ADD_RADIUS, Errors.WIFI_10465, tenantId);

    assertThrows(ObjectNotFoundException.class, () -> {
      radiusServiceCtrl.getRadiusServerProfile(radius4Id);
    });
  }

  @Test
  public void testUpdateRadiusServerProfileIsTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-1");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius1.setIsTemplate(true);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    List<Radius> radiusList = repositoryUtil.findAll(Radius.class, tenantId, true);
    assertEquals(1, radiusList.size());

    Radius radiusAdded1 = radiusList.get(0);
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);
    assertTrue(radiusAdded1.getIsTemplate());

    //////////
    // update type -> success
    radiusAdded1.setType(RadiusProfileTypeEnum.ACCOUNTING);
    radiusAdded1.setIsTemplate(false); // isTemplate should not be changed
    RequestParams rps = new RequestParams().addPathVariable(RADIUS_SERVER_PROFILE_TEMPLATE_ID, radiusAdded1.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE_TEMPLATE, userName, rps,
        map(radiusAdded1));
    assertActivityStatusSuccess(UPDATE_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);

    radiusList = repositoryUtil.findAll(Radius.class, tenantId, true);
    assertEquals(1, radiusList.size());

    Radius authRadiusUpdated = radiusList.get(0);
    assertRadiusServers(radiusAdded1, authRadiusUpdated);
    assertRadiusNetworks(authRadiusUpdated, 0);
    assertTrue(authRadiusUpdated.getIsTemplate());

    var viewOps1 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps1 {}", viewOps1);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps1, 1),
        () -> assertRadiusIndexViewmodel(viewOps1, OpType.MOD, authRadiusUpdated, 0)
    );

  }

  @Test
  public void testRadiusTemplateWithIpPortConflict(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // normal case
    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-auth");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius1.setIsTemplate(true);

    RadiusServer primary = radius1.getPrimary();
    primary.setIp("*******");
    primary.setPort(1812);

    RadiusServer secondary = radius1.getSecondary();
    secondary.setIp("*******");
    secondary.setPort(1813);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);

    List<Radius> radiusList = repositoryUtil.findAll(Radius.class, tenantId, true);
    assertEquals(1, radiusList.size());

    Radius authRadiusTemplateCreated = radiusList.get(0);
    assertRadiusServers(radius1, authRadiusTemplateCreated);
    assertRadiusNetworks(authRadiusTemplateCreated, 0);
    assertTrue(authRadiusTemplateCreated.getIsTemplate());
    assertEquals("*******", authRadiusTemplateCreated.getPrimary().getIp());
    assertEquals(1812, authRadiusTemplateCreated.getPrimary().getPort());

    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps {}", viewOps);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertRadiusIndexViewmodel(viewOps, OpType.ADD, authRadiusTemplateCreated, 0)
    );

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());

    //////////
    // adding Radius Template with the same IP+Port fail
    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("another-radius-auth"); // a different name
    radius2.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius2.setIsTemplate(true);
    radius2.setPrimary(primary);
    String radius2Id = radius2.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius2)); // expect to fail
    assertActivityStatusFail(ADD_RADIUS_SERVER_PROFILE_TEMPLATE, Errors.WIFI_10466, tenantId);

    assertThrows(ObjectNotFoundException.class, () -> {
      radiusServiceCtrl.getRadiusServerProfile(radius2Id);
    });

    //////////
    // adding Radius with the same IP+Port should pass
    Radius radius3 = RadiusTestFixture.authRadius();
    radius3.setName("test-radius-auth");
    radius3.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius3.setPrimary(primary);
    radius3.setSecondary(secondary);
    String radius3Id = radius3.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius3));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);

    Radius authRadiusCreated = radiusServiceCtrl.getRadiusServerProfile(radius3Id);
    assertNotNull(authRadiusCreated);

    //////////
    // adding another Radius with the same IP+Port should fail
    Radius radius4 = RadiusTestFixture.authRadius();
    radius4.setName("another-radius-auth");
    radius4.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius4.setPrimary(primary);
    String radius4Id = radius4.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius4));
    assertActivityStatusFail(ADD_RADIUS, Errors.WIFI_10466, tenantId);

    assertThrows(ObjectNotFoundException.class, () -> {
      radiusServiceCtrl.getRadiusServerProfile(radius4Id);
    });

    //////////
    // adding another Radius with different IP+Port should pass
    Radius radius5 = RadiusTestFixture.authRadius();
    radius5.setName("another-radius-auth");
    radius5.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    String radius5Id = radius5.getId();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE, userName, mapToRadiusServerProfile(radius5));
    assertActivityStatusSuccess(ADD_RADIUS, tenantId);

    Radius authRadiusCreated2 = radiusServiceCtrl.getRadiusServerProfile(radius5Id);
    assertNotNull(authRadiusCreated2);

    //////////
    // changing to use the same secondary IP+Port should fail
    authRadiusCreated2.setSecondary(secondary);
    RequestParams rps = new RequestParams().addPathVariable(RADIUS_ID, authRadiusCreated2.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_RADIUS_SERVER_PROFILE, userName, rps,
        mapToRadiusServerProfile(authRadiusCreated2));
    assertActivityStatusFail(UPDATE_RADIUS, Errors.WIFI_10466, tenantId);
  }

  private static com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler dummyNetworkVenueScheduler() {
    final var scheduler = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler();
    scheduler.setType(SchedulerTypeEnum.CUSTOM);
    scheduler.setMon(randomTimeSlot());
    scheduler.setTue(randomTimeSlot());
    scheduler.setWed(randomTimeSlot());
    scheduler.setThu(randomTimeSlot());
    scheduler.setFri(randomTimeSlot());
    scheduler.setSat(randomTimeSlot());
    scheduler.setSun(randomTimeSlot());
    return scheduler;
  }

  private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue networkVenue(String networkId, String venueId) {
    final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
    payload.setId(randomId()); // autoGenerated is true in wifi-api
    payload.setNetworkId(networkId);
    payload.setVenueId(venueId);
    payload.setIsAllApGroups(true);
    payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
    payload.setScheduler(dummyNetworkVenueScheduler());
    return payload;
  }

  @Test
  public void testDeleteRadiusServerProfile(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    //////////
    // 1 setup
    Radius radius1 = RadiusTestFixture.authRadius();
    radius1.setName("test-radius-auth");
    radius1.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius1.setIsTemplate(true);

    Radius radius2 = RadiusTestFixture.authRadius();
    radius2.setName("test-radius-acct");
    radius2.setType(RadiusProfileTypeEnum.ACCOUNTING);
    radius2.setIsTemplate(true);

    Radius radius3 = RadiusTestFixture.authRadius();
    radius3.setName("test-radius-auth3");
    radius3.setType(RadiusProfileTypeEnum.AUTHENTICATION);
    radius3.setIsTemplate(true);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius1));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius2));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_RADIUS_SERVER_PROFILE_TEMPLATE, userName, map(radius3));
    assertActivityStatusSuccess(ADD_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);
    receiveViewmodelCollectorOperations(1, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());

    Radius radiusAdded1 =
        retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius1.getPrimary().getIp(), radius1.getPrimary().getPort()));
    assertRadiusServers(radius1, radiusAdded1);
    assertRadiusNetworks(radiusAdded1, 0);

    Radius radiusAdded2 =
        retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius2.getPrimary().getIp(), radius2.getPrimary().getPort()));
    assertRadiusServers(radius2, radiusAdded2);
    assertRadiusNetworks(radiusAdded2, 0);

    Radius radiusAdded3 =
        retrieveTemplate(() -> radiusRepository.findRadiusByTenantIdByPrimary(
        TxCtxHolder.tenantId(), radius3.getPrimary().getIp(), radius3.getPrimary().getPort()));
    assertRadiusServers(radius3, radiusAdded3);
    assertRadiusNetworks(radiusAdded3, 0);

    //////////
    // Add AAANetwork with authRadius
    AAANetwork aaaNetwork = NetworkTestFixture.randomAAANetwork(tenant, n -> {
      n.setId(randomId());
      n.setAuthRadius(radiusAdded1);
      n.setIsTemplate(true);
    });

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, map(aaaNetwork));
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, tenantId);

    assertRadiusNetworks(radiusAdded1, 1);

    var viewOps2 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps2 {}", viewOps2);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps2, 2),
        () -> assertTrue(viewOps2.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> aaaNetwork.getName().equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps2, OpType.MOD, radiusAdded1, 1)
    );

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());

    // activate Network to Venue
    Venue v1 = createVenueTemplate(tenant, "v1");
    v1.setIsTemplate(true);
    NetworkVenue nv1 = networkVenue(aaaNetwork.getId(), v1.getId());

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE_TEMPLATE, userName, nv1);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE_TEMPLATE, tenantId);

    var viewOps3 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps3 {}", viewOps3);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps3, 2) // networkvenuemapping
    );

    //////////
    // 2 delete Radius should fail since it's in-use
    RequestParams rps = new RequestParams().addPathVariable(RADIUS_SERVER_PROFILE_TEMPLATE_ID, radiusAdded1.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_RADIUS_SERVER_PROFILE_TEMPLATE, userName, rps, "");
    assertActivityStatusFail(DELETE_RADIUS_SERVER_PROFILE_TEMPLATE, Errors.WIFI_10470, tenantId);
    assertNoMessages(tenantId, kafkaTopicProvider.getCmnCfgCollectorCfgRequest(),
        kafkaTopicProvider.getDdccmCfgRequest());
    assertThat(radiusTemplateServiceCtrl.getRadiusServerProfileTemplate(radiusAdded1.getId()))
        .isNotNull();

    // delete others
    RequestParams rps2 = new RequestParams().addPathVariable(RADIUS_SERVER_PROFILE_TEMPLATE_ID, radiusAdded2.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_RADIUS_SERVER_PROFILE_TEMPLATE, userName, rps2, "");
    assertActivityStatusSuccess(DELETE_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);

    RequestParams rps3 = new RequestParams().addPathVariable(RADIUS_SERVER_PROFILE_TEMPLATE_ID, radiusAdded3.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_RADIUS_SERVER_PROFILE_TEMPLATE, userName, rps3, "");
    assertActivityStatusSuccess(DELETE_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);

    assertThatExceptionOfType(ObjectNotFoundException.class)
        .isThrownBy(() -> radiusTemplateServiceCtrl.getRadiusServerProfileTemplate(radiusAdded2.getId()));
    assertThatExceptionOfType(ObjectNotFoundException.class)
        .isThrownBy(() -> radiusTemplateServiceCtrl.getRadiusServerProfileTemplate(radiusAdded3.getId()));

    var viewOps4 = receiveViewmodelCollectorOperations(2, tenantId);
    log.info("viewOps4 {}", viewOps4);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps4, 2),
        () -> assertRadiusIndexViewmodel(viewOps4, OpType.DEL, radiusAdded2, 0),
        () -> assertRadiusIndexViewmodel(viewOps4, OpType.DEL, radiusAdded3, 0)
    );

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());

    //////////
    // 3 delete Network before deleting Radius
    RequestParams rps4 = new RequestParams().addPathVariable("networkTemplateId", aaaNetwork.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_NETWORK_TEMPLATE, userName, rps4, "");
    assertActivityStatusSuccess(DELETE_NETWORK_TEMPLATE, tenantId);
    // the radius in DB should not be deleted due to Network deletion in NewUI
    assertThat(radiusTemplateServiceCtrl.getRadiusServerProfileTemplate(radiusAdded1.getId()))
        .isNotNull();
    assertRadiusNetworks(radiusAdded1, 0);

    var viewOps5 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps5 {}", viewOps5);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps5, 3),
        () -> assertTrue(viewOps5.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> aaaNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.DEL)
            .findAny().isPresent()),
        () -> assertTrue(viewOps5.stream()
            .filter(o -> NETWORK_VENUE_MAPPING.equals(o.getIndex()))
            .filter(o -> o.getOpType() == OpType.DEL)
            .findAny().isPresent()),
        () -> assertRadiusIndexViewmodel(viewOps5, OpType.MOD, radiusAdded1, 0)
    );

    // venueRadius, venueSchedule, wlanVenue
    var ddccmOps2 = receiveDdccmOperations(1, tenantId);
    log.info("ddccmOps2 {}", ddccmOps2);
//    assertAll("assert ddccm ops",
//        () -> assertDdccmOps(ddccmOps2, 3),
//        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasVenueRadius),
//        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasVenueSchedule),
//        () -> assertDdccmOpCount(ddccmOps2, Action.DELETE, 1, Operation::hasWlanVenue)
//    );

    //////////
    // 4 delete Radius
    RequestParams rps5 = new RequestParams().addPathVariable(RADIUS_SERVER_PROFILE_TEMPLATE_ID, radiusAdded1.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_RADIUS_SERVER_PROFILE_TEMPLATE, userName, rps5, "");
    assertActivityStatusSuccess(DELETE_RADIUS_SERVER_PROFILE_TEMPLATE, tenantId);
    assertThatExceptionOfType(ObjectNotFoundException.class)
        .isThrownBy(() -> radiusTemplateServiceCtrl.getRadiusServerProfileTemplate(radiusAdded1.getId()));

    var viewOps6 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("viewOps6 {}", viewOps6);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelOps(viewOps6, 1),
        () -> assertRadiusIndexViewmodel(viewOps6, OpType.DEL, radiusAdded1, 0)
    );

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest());
  }

}
