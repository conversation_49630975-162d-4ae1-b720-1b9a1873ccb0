package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.config.RadiusServerSettingConfig;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusServer;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
public class DdccmAccountingRadiusVenueOperationBuilderTest {

  @Autowired
  private DdccmAccountingRadiusVenueOperationBuilder builder;

  @Test
  void testCreateAccountingRadiusVenue(Tenant tenant, Venue venue) {
    var operation = Operation.newBuilder();
    var acctRadius = new Radius();
    acctRadius.setName("accRadius");
    acctRadius.setType(RadiusProfileTypeEnum.ACCOUNTING);
    var radiusServer = new RadiusServer();
    radiusServer.setIp("127.0.0.1");
    radiusServer.setPort(1813);
    radiusServer.setSharedSecret("sharedSecret");
    acctRadius.setPrimary(radiusServer);
    var accountingRadiusVenue = Generators.accountingRadiusVenue(acctRadius).generate();
    accountingRadiusVenue.setId("acctRadiusVenueId");
    accountingRadiusVenue.setTenant(tenant);
    accountingRadiusVenue.setVenue(venue);
    builder.config(operation, accountingRadiusVenue, EntityAction.ADD, null);

    assertThat(operation.getVenueRadiusForAccounting().getServerCount()).isEqualTo(1);
    assertThat(operation.getVenueRadiusForAccounting().getServer(0))
        .hasFieldOrPropertyWithValue("ip", "127.0.0.1")
        .hasFieldOrPropertyWithValue("port", 1813)
        .hasFieldOrPropertyWithValue("sharedSecret", "sharedSecret");
  }

  @Test
  void testCreateAccountingRadiusVenueWithoutSharedSecret(Tenant tenant, Venue venue) {
    var operation = Operation.newBuilder();
    var acctRadius = new Radius();
    acctRadius.setName("accRadius");
    acctRadius.setType(RadiusProfileTypeEnum.ACCOUNTING);
    var radiusServer = new RadiusServer();
    radiusServer.setIp("127.0.0.1");
    radiusServer.setPort(1813);
    acctRadius.setPrimary(radiusServer);
    var accountingRadiusVenue = Generators.accountingRadiusVenue(acctRadius).generate();
    accountingRadiusVenue.setId("acctRadiusVenueId");
    accountingRadiusVenue.setTenant(tenant);
    accountingRadiusVenue.setVenue(venue);
    builder.config(operation, accountingRadiusVenue, EntityAction.ADD, null);

    assertThat(operation.getVenueRadiusForAccounting().getServerCount()).isEqualTo(1);
    assertThat(operation.getVenueRadiusForAccounting().getServer(0))
        .hasFieldOrPropertyWithValue("ip", "127.0.0.1")
        .hasFieldOrPropertyWithValue("port", 1813);
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    public DdccmAccountingRadiusVenueOperationBuilder ddccmAccountingRadiusVenueOperationBuilder() {
      var builder = Mockito.spy(DdccmAccountingRadiusVenueOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }
  }
}