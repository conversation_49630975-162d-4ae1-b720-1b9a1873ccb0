package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@WifiJpaDataTest(showSql = false)
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO rogue_classification_policy (id, tenant, name) VALUES
        ('b1a2c59f92d243cba7ca5b4bfe83c289', '6700bc51acf84c4aa9510df2ca00b5f4', 'Default profile'),
        ('b1a2c59f92d243cba7ca5b4bfe83c290', '6700bc51acf84c4aa9510df2ca00b5f4', 'Rogue profile');
    """)
public class RogueClassificationPolicyRepositoryTest {
  private static final String TENANT_ID = "6700bc51acf84c4aa9510df2ca00b5f4";
  private static final String DEFAULT_PROFILE = "Default profile";
  private static final String ROGUE_PROFILE = "Rogue profile";

  @Autowired
  private RogueClassificationPolicyRepository repository;

  @Test
  public void findByTenantIdAndNameInTest() {
    assertThat(repository.findByTenantIdAndNameIn(TENANT_ID, List.of(DEFAULT_PROFILE, ROGUE_PROFILE)))
        .isNotNull()
        .hasSize(2)
        .extracting(RogueClassificationPolicy::getName)
        .contains(DEFAULT_PROFILE)
        .contains(ROGUE_PROFILE);
  }
}
