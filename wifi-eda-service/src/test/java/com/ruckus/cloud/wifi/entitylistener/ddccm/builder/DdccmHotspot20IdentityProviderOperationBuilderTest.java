package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20IdentityProvider;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.redis.YamlPropertySourceFactory;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.PropertySource;

@WifiUnitTest
@PropertySource(value = "classpath:application-unit.yml", factory = YamlPropertySourceFactory.class)
public class DdccmHotspot20IdentityProviderOperationBuilderTest {

  @SpyBean
  private DdccmHotspot20IdentityProviderOperationBuilder ddccmHotspot20IdentityProviderOperationBuilder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  public void testAddHotspot20IdentityProvider() {

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider hotspot20IdentityProvider =
        Generators.hotspot20IdentityProvider().generate();

    Radius authRadius = Generators.radiusProfile().generate();
    authRadius.setName(authRadius.getName() + randomString(5).generate());
    AuthRadiusService authRadiusService = Generators.authRadiusService(authRadius).generate();
    AuthRadiusProfile authRadiusProfile = Generators.authRadiusProfile(authRadiusService).generate();

    Radius accountingRadius = Generators.radiusProfile().generate();
    accountingRadius.setName(authRadius.getName() + randomString(5).generate());
    AccountingRadiusService accountingRadiusService = Generators.accountingRadiusService(accountingRadius).generate();
    AccountingRadiusProfile accountingRadiusProfile = Generators.accountingRadiusProfile(accountingRadiusService).generate();

    hotspot20IdentityProvider.setAuthRadius(authRadius);
    hotspot20IdentityProvider.setAuthRadiusProfile(authRadiusProfile);

    hotspot20IdentityProvider.setAccountingRadiusEnabled(true);
    hotspot20IdentityProvider.setAccountingRadius(accountingRadius);
    hotspot20IdentityProvider.setAccountingRadiusProfile(accountingRadiusProfile);


    List<Operation> operations =
        ddccmHotspot20IdentityProviderOperationBuilder
            .build(new NewTxEntity<>(hotspot20IdentityProvider), emptyTxChanges());

    assertNotNull(operations);
    assertEquals(1, operations.size());

    Hotspot20IdentityProvider hotspot20IdentityProviderOperation =
        operations.get(0).getHotspot20IdentityProvider();

    assertNotNull(hotspot20IdentityProviderOperation);
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());

    validateProperties(hotspot20IdentityProvider, hotspot20IdentityProviderOperation);
    assertEquals(authRadiusService.getId(), hotspot20IdentityProviderOperation.getAuthenticationServiceId());
    assertEquals(accountingRadiusService.getId(), hotspot20IdentityProviderOperation.getAccountingServiceId());
  }

  @Test
  public void testUpdateHotspot20IdentityProvider() {

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider hotspot20IdentityProvider =
        Generators.hotspot20IdentityProvider().generate();

    Radius radius = Generators.radiusProfile().generate();
    radius.setName(radius.getName() + randomString(5).generate());
    AccountingRadiusService accountingRadiusService = Generators.accountingRadiusService(radius).generate();
    AccountingRadiusProfile accountingRadiusProfile = Generators.accountingRadiusProfile(accountingRadiusService).generate();
    AuthRadiusService authRadiusService = Generators.authRadiusService(radius).generate();
    AuthRadiusProfile authRadiusProfile = Generators.authRadiusProfile(authRadiusService).generate();
    hotspot20IdentityProvider.setAuthRadius(radius);

    hotspot20IdentityProvider.setAccountingRadiusEnabled(true);
    hotspot20IdentityProvider.setAccountingRadius(radius);
    hotspot20IdentityProvider.setAccountingRadiusProfile(accountingRadiusProfile);
    hotspot20IdentityProvider.setAuthRadius(radius);
    hotspot20IdentityProvider.setAuthRadiusProfile(authRadiusProfile);

    List<Operation> operations = ddccmHotspot20IdentityProviderOperationBuilder
      .build(new ModifiedTxEntity<>(hotspot20IdentityProvider,
          Set.of("plmns")), emptyTxChanges());

    assertNotNull(operations);
    assertEquals(1, operations.size());

    Hotspot20IdentityProvider hotspot20IdentityProviderOperation =
        operations.get(0).getHotspot20IdentityProvider();
    assertNotNull(hotspot20IdentityProviderOperation);
    assertEquals(EntityAction.MODIFY.toString(),
        operations.get(0).getAction().toString());

    validateProperties(hotspot20IdentityProvider, hotspot20IdentityProviderOperation);
  }

  @Test
  public void testDeleteHotspot20IdentityProvider() {

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider hotspot20IdentityProvider =
        Generators.hotspot20IdentityProvider().generate();

    List<Operation> operations = ddccmHotspot20IdentityProviderOperationBuilder
      .build(new DeletedTxEntity<>(hotspot20IdentityProvider), emptyTxChanges());

    assertNotNull(operations);
    assertEquals(1, operations.size());

    Hotspot20IdentityProvider hotspot20IdentityProviderOperation =
        operations.get(0).getHotspot20IdentityProvider();
    assertNotNull(hotspot20IdentityProviderOperation);
    assertEquals(EntityAction.DELETE.toString(), operations.get(0).getAction().toString());
    assertEquals(hotspot20IdentityProvider.getId(), hotspot20IdentityProviderOperation.getId());
    assertEquals(StringUtils.EMPTY, hotspot20IdentityProviderOperation.getName());

    assertEquals(Collections.emptyList(), hotspot20IdentityProviderOperation.getPlmnList());
    assertEquals(Collections.emptyList(), hotspot20IdentityProviderOperation.getHomeOiList());
  }

  @Test
  public void testGetRoamConsortiumOidLength() {
    //wifi-api organizationId pattern: "[0-9a-f]{6}(?:[0-9a-f]{4})?"
    String oid = "123456abcd";
    String oid2 = "123abc";
    String oid3 = "12ab";
    int length1 = ddccmHotspot20IdentityProviderOperationBuilder.getRoamConsortiumOidLength(oid);
    int length2 = ddccmHotspot20IdentityProviderOperationBuilder.getRoamConsortiumOidLength(oid2);
    assertEquals(5, length1);
    assertEquals(3, length2);

    Assertions.assertThrows(IllegalArgumentException.class, () -> {
      ddccmHotspot20IdentityProviderOperationBuilder.getRoamConsortiumOidLength(oid3);
    });
  }

  private void validateProperties(
      com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider hotspot20IdentityProvider,
    Hotspot20IdentityProvider hotspot20IdentityProviderOperation) {

    assertEquals(hotspot20IdentityProvider.getId(), hotspot20IdentityProviderOperation.getId());
    assertEquals(hotspot20IdentityProvider.getName(), hotspot20IdentityProviderOperation.getName());
    assertEquals(hotspot20IdentityProvider.getAuthRadiusProfile().getId(), hotspot20IdentityProviderOperation.getAuthenticationProfileId());
    assertEquals(hotspot20IdentityProvider.getAccountingRadiusProfile().getId(), hotspot20IdentityProviderOperation.getAccountingProfileId());
    assertEquals(hotspot20IdentityProvider.getAccountingRadiusEnabled(), hotspot20IdentityProviderOperation.getAccountingProfileEnabled());

    assertEquals(hotspot20IdentityProvider.getPlmns().stream()
            .map(ddccmHotspot20IdentityProviderOperationBuilder::toPlmn).toList(),
        hotspot20IdentityProviderOperation.getPlmnList());
    assertEquals(hotspot20IdentityProvider.getNaiRealms().stream()
            .map(ddccmHotspot20IdentityProviderOperationBuilder::toNaiRealm).toList(),
        hotspot20IdentityProviderOperation.getRealmList());
    assertEquals(hotspot20IdentityProvider.getRoamConsortiumOis().stream()
            .map(ddccmHotspot20IdentityProviderOperationBuilder::toRoamConsortium).toList(),
        hotspot20IdentityProviderOperation.getHomeOiList());

    hotspot20IdentityProviderOperation.getHomeOiList()
        .forEach(oi -> assertTrue(oi.getLength() == 3 || oi.getLength() == 5));

    int plmnCount = hotspot20IdentityProvider.getPlmns().size();
    assertEquals(plmnCount, hotspot20IdentityProviderOperation.getPlmnList().size());

  }
}
