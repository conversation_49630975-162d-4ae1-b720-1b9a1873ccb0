package com.ruckus.cloud.wifi.integration.networkactivation;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueSchedulerTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("NetworkActivationTest")
@WifiIntegrationTest
class ConsumeDeleteNetworkActivationsRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class GivenOpenNetworkPersistedInDb {

    private String networkId;
    private List<String> venueIds;

    @Payload
    private List<String> networkVenueIds;

    @BeforeEach
    void givenOpenNetworkAndRelatedEntitiesPersistedInDb(@OpenNetwork Network network) {
      networkId = network.getId();

      final var tenantId = network.getTenant().getId();
      final var networkVenues = new ArrayList<NetworkVenue>();

      final var venues = Stream.generate(Venue::new).peek(a -> a.setId(randomId()))
          .peek(a -> a.setTenant(network.getTenant())).peek(a -> a.setName(randomName())).limit(2)
          .collect(Collectors.toList());

      venues.forEach(venue -> {
        repositoryUtil.createOrUpdate(venue, tenantId, randomTxId());
        final var apGroup = ApGroupTestFixture.randomApGroup(venue);
        repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());

        final var networkVenue = new NetworkVenue();
        networkVenue.setId(randomId());
        networkVenue.setNetwork(network);
        networkVenue.setVenue(venue);
        networkVenue.setTenant(network.getTenant());

        NetworkVenueSchedulerTestFixture.randomNetworkVenueScheduler(networkVenue);
        repositoryUtil.createOrUpdate(networkVenue, tenantId, randomTxId());

        final var networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
            apGroup);
        repositoryUtil.createOrUpdate(networkApGroup, tenantId, randomTxId());

        final var networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
            networkApGroup);
        repositoryUtil.createOrUpdate(networkApGroupRadio, tenantId, randomTxId());

        networkVenues.add(networkVenue);
      });

      venueIds = venues.stream().map(Venue::getId).collect(Collectors.toList());
      networkVenueIds = networkVenues.stream().map(NetworkVenue::getId)
          .collect(Collectors.toList());
    }

    @Test
    @ApiAction(CfgAction.DELETE_NETWORK_VENUES)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueIds, networkId, venueIds);
    }

    void validateResult(String tenantId, String requestId, List<String> networkVenueIds,
        String networkId, List<String> venueIds) {

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2)
              .allMatch(op -> op.getOpType() == OpType.DEL)
              .allMatch(op -> networkVenueIds.contains(op.getId())));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload()).matches(
                  msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.DELETE_NETWORK_VENUES))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasWlanApGroup).count() == 2,
                  "The count of WlanApGroup operations should be 2") // 2.4G x 2 venues
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 2,
                  "The count of VenueSchedule operations should be 2")
              .filteredOn(Operation::hasWlanVenue)
              .hasSize(networkVenueIds.size())
              .allMatch(op -> op.getAction() == Action.DELETE,
                  String.format("The value of `action` field in WlanVenue operation should be %s",
                      Action.DELETE))
              .allMatch(op -> networkVenueIds.contains(op.getId()),
                  String.format(
                      "The value of `id` field in WlanVenue operation should be included in %s",
                      networkVenueIds))
              .allSatisfy(op -> {
                assertSoftly(softly -> {
                  softly.assertThat(op.getWlanVenue().getWlanId())
                      .isEqualTo(networkId);
                  softly.assertThat(op.getWlanVenue().getVenueId())
                      .matches(venueIds::contains);
                });
              }));
    }
  }

  @Nested
  class GivenOpenNetworkTemplatePersistedInDb {

    private String networkId;
    private List<String> venueIds;

    @Payload
    private List<String> networkVenueIds;

    @BeforeEach
    void givenOpenNetworkAndRelatedEntitiesPersistedInDb(
        @OpenNetwork(isTemplate = true) Network network) {
      networkId = network.getId();

      final var tenantId = network.getTenant().getId();
      final var networkVenues = new ArrayList<NetworkVenue>();

      final var venues = Stream.generate(Venue::new).peek(a -> a.setId(randomId()))
          .peek(a -> a.setTenant(network.getTenant())).peek(a -> a.setName(randomName()))
          .peek(a -> a.setIsTemplate(true)).limit(2)
          .collect(Collectors.toList());

      venues.forEach(venue -> {
        repositoryUtil.createOrUpdate(venue, tenantId, randomTxId());
        final var apGroup = ApGroupTestFixture.randomApGroup(venue);
        repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());

        final var networkVenue = new NetworkVenue();
        networkVenue.setId(randomId());
        networkVenue.setNetwork(network);
        networkVenue.setVenue(venue);
        networkVenue.setTenant(network.getTenant());
        networkVenue.setIsTemplate(true);

        NetworkVenueSchedulerTestFixture.randomNetworkVenueScheduler(networkVenue);
        repositoryUtil.createOrUpdate(networkVenue, tenantId, randomTxId());

        final var networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
            apGroup);
        repositoryUtil.createOrUpdate(networkApGroup, tenantId, randomTxId());

        final var networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
            networkApGroup);
        repositoryUtil.createOrUpdate(networkApGroupRadio, tenantId, randomTxId());

        networkVenues.add(networkVenue);
      });

      venueIds = venues.stream().map(Venue::getId).collect(Collectors.toList());
      networkVenueIds = networkVenues.stream().map(NetworkVenue::getId)
          .collect(Collectors.toList());
    }

    @Test
    @ApiAction(CfgAction.DELETE_NETWORK_VENUE_TEMPLATES)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), networkVenueIds, networkId, venueIds);
    }

    void validateResult(String tenantId, String requestId, List<String> networkVenueIds,
        String networkId, List<String> venueIds) {

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2)
              .allMatch(op -> op.getOpType() == OpType.DEL)
              .allMatch(op -> networkVenueIds.contains(op.getId())));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload()).matches(
                  msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.DELETE_NETWORK_VENUE_TEMPLATES))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]", requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]", tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]", ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasWlanApGroup).count() == 2,
                  "The count of WlanApGroup operations should be 2") // 2.4G x 2 venues
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 2,
                  "The count of VenueSchedule operations should be 2")
              .filteredOn(Operation::hasWlanVenue)
              .hasSize(networkVenueIds.size())
              .allMatch(op -> op.getAction() == Action.DELETE,
                  String.format("The value of `action` field in WlanVenue operation should be %s", Action.DELETE))
              .allMatch(op -> networkVenueIds.contains(op.getId()),
                  String.format("The value of `id` field in WlanVenue operation should be included in %s", networkVenueIds))
              .allSatisfy(op -> {
                assertSoftly(softly -> {
                  softly.assertThat(op.getWlanVenue().getWlanId())
                      .isEqualTo(networkId);
                  softly.assertThat(op.getWlanVenue().getVenueId())
                      .matches(venueIds::contains);
                });
              }));
    }
  }
}
