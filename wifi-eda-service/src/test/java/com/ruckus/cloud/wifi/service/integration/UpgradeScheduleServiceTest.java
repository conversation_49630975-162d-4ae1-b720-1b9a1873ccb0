package com.ruckus.cloud.wifi.service.integration;

import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.repository.VenueCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.service.impl.ApBranchFamilyServiceRouter;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.service.UpgradeScheduleService;
import com.ruckus.cloud.wifi.service.impl.UpgradeScheduleServiceImpl;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;

@WifiJpaDataTest
class UpgradeScheduleServiceTest {

  @Autowired UpgradeScheduleService upgradeScheduleService;
  @Autowired UpgradeScheduleRepository upgradeScheduleRepository;
  @Autowired UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository;

  @Test
  @Sql("classpath:sql/finished-upgrade-schedule.sql")
  void testDeleteUpgradeSchedule() {
    String tenantId = "f8a675147b3d45d892bd53f2761032b5";
    List<String> upgradeScheduleIds = List.of(
            "41a7069c062941249ebb410d8af62d47",
            "df54a8e1456b443187b438429fb9a28e",
            "e78e4af4f1ba4e9c8f2a9277cb24478a",
            "42a7e38dbf68444facb20cd045734676");

    assertEquals(4, upgradeScheduleRepository.findAllById(upgradeScheduleIds).size());
    assertEquals(4, upgradeScheduleFirmwareVersionRepository.findByTenantId(tenantId).size());

    upgradeScheduleService.deleteByIds(upgradeScheduleIds);

    assertEquals(0, upgradeScheduleRepository.findAllById(upgradeScheduleIds).size());
    assertEquals(0, upgradeScheduleFirmwareVersionRepository.findByTenantId(tenantId).size());
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('9520ee78bb1222ec84220242ac120002');
      INSERT INTO ap_version (id, category, created_date, updated_date, target) VALUES
        ('*********.125', 'RECOMMENDED', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427', NULL),
        ('*********.123', 'RECOMMENDED', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427', NULL),
        ('*********.111', 'RECOMMENDED', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427', NULL),
        ('*********.123', 'RECOMMENDED', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427', NULL);
      INSERT INTO schedule_time_slot (id, start_date_time, end_date_time, status, total_capacity_venue) VALUES
        ('schedule_time_slod_id_running', '2022-03-24 00:00:00.000', '2022-03-24 02:00:00.000', 'WAITING_REMINDER', 2500),
        ('schedule_time_slod_id', '2022-03-24 02:00:00.000', '2022-03-24 04:00:00.000', 'WAITING_REMINDER', 2500);
      INSERT INTO venue (id, tenant, ap_version, timezone) VALUES
        ('2dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002', '*********.111', 'Asia/Taipei');
      INSERT INTO upgrade_schedule (id, tenant, status, venue, time_slot, version) VALUES
        ('schedule_id_0', '9520ee78bb1222ec84220242ac120002', 'PENDING', '2dec899ad64e47649981797a26ab5668', 'schedule_time_slod_id', '*********.123'),
        ('schedule_id_1', '9520ee78bb1222ec84220242ac120002', 'PENDING', '2dec899ad64e47649981797a26ab5668', 'schedule_time_slod_id', '*********.125'),
        ('schedule_id_2', '9520ee78bb1222ec84220242ac120002', 'PENDING', '2dec899ad64e47649981797a26ab5668', 'schedule_time_slod_id', '*********.123'),
        ('schedule_id_3', '9520ee78bb1222ec84220242ac120002', 'RUNNING', '2dec899ad64e47649981797a26ab5668', 'schedule_time_slod_id_running', '*********.123');
      """)
  void testGetPendingSchedulesByVenueId() {
    String venueId = "2dec899ad64e47649981797a26ab5668";

    List<UpgradeSchedule> upgradeSchedules = upgradeScheduleService.getSchedulesByVenueId(venueId);
    assertThat(upgradeSchedules)
        .isNotNull()
        .hasSize(4)
        .filteredOn(us -> us.getStatus() == UpgradeScheduleStatus.PENDING)
        .hasSize(3);
  }

  @TestConfiguration
  static class TestConfig {
    @ConditionalOnMissingBean
    @Bean
    ApBranchFamilyServiceRouter apBranchFamilyService() {
      return mock(ApBranchFamilyServiceRouter.class);
    }

    @Bean
    @Primary
    public UpgradeScheduleService upgradeScheduleService(
        UpgradeScheduleRepository upgradeScheduleRepository,
        ApBranchFamilyServiceRouter apBranchFamilyService,
        UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository,
        ApVersionRepository apVersionRepository,
        VenueCurrentFirmwareRepository venueCurrentFirmwareRepository) {
      return new UpgradeScheduleServiceImpl(
          upgradeScheduleRepository,
          apBranchFamilyService,
          upgradeScheduleFirmwareVersionRepository,
          apVersionRepository,
          venueCurrentFirmwareRepository);
    }
  }
}
