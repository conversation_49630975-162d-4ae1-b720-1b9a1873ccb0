package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApGroupProtoProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.jdbc.Sql;

@Tag("ApGroupTest")
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES
      ('4c8279f79307415fa9e4c88a1819f0fg'),
      ('a782b402d95c46b4aabce87b77e68613'),
      ('eed4a8bf286e4d71b43c3d7ec9b13c2d');

    INSERT INTO venue (id, tenant, is_template) VALUES
      ('4a1df860e61c4c93a65d5472a0576c4c', '4c8279f79307415fa9e4c88a1819f0fg', false),
      ('71ad469e254e4bf9a98e5fbfbbcae8f0', 'a782b402d95c46b4aabce87b77e68613', false),
      ('bbf80fa1ca6e449e85677264f82b7eee', 'eed4a8bf286e4d71b43c3d7ec9b13c2d', true);

    INSERT INTO ap_group (id, is_default, name, tenant, venue, is_template) VALUES
      ('33335fa13e444d62ac64af9ab4ec5796', true, '', '4c8279f79307415fa9e4c88a1819f0fg', '4a1df860e61c4c93a65d5472a0576c4c', false),
      ('fdd94f9fd66641b9b893ae8f7c5848dg', false, 'AP-Group-1', '4c8279f79307415fa9e4c88a1819f0fg', '4a1df860e61c4c93a65d5472a0576c4c', false),
      ('58bc286013e1468e9867061e8dbed380', false, 'AP-Group-2', '4c8279f79307415fa9e4c88a1819f0fg', '4a1df860e61c4c93a65d5472a0576c4c', false),
      ('1b42a14ada374d6dbf563ef756ef70f2', true, '', 'a782b402d95c46b4aabce87b77e68613', '71ad469e254e4bf9a98e5fbfbbcae8f0', false),
      ('82fefdc0ca984a8f8fd03cc7dfe4bb34', true, '', 'eed4a8bf286e4d71b43c3d7ec9b13c2d', 'bbf80fa1ca6e449e85677264f82b7eee', true);

    INSERT INTO ap (id, name, ap_group, tenant, soft_deleted) VALUES
      ('777777777711', 'AP-11', '1b42a14ada374d6dbf563ef756ef70f2', 'a782b402d95c46b4aabce87b77e68613', false);
         
    INSERT INTO network (type, id, name, tenant) VALUES
        ('OPEN', '39fc80a4bf0b43bcbf61e018f0ea264f', 'OPEN NETWORK', 'eed4a8bf286e4d71b43c3d7ec9b13c2d'),
        ('AAA', '14fafa7aa9df4ce49f25b65a70badf17', 'AAA NETWORK', 'eed4a8bf286e4d71b43c3d7ec9b13c2d');
           
    INSERT INTO network_venue (id, tenant, network, venue, all_ap_groups_radio, is_all_ap_groups) VALUES
        ('a0e9b3360faa40a4bfa741cf4d8ce281', 'eed4a8bf286e4d71b43c3d7ec9b13c2d', '14fafa7aa9df4ce49f25b65a70badf17', 'bbf80fa1ca6e449e85677264f82b7eee', 'Both', false),
        ('edbfc4df1faf4517b099dc8bf17e376h', 'eed4a8bf286e4d71b43c3d7ec9b13c2d', '39fc80a4bf0b43bcbf61e018f0ea264f', 'bbf80fa1ca6e449e85677264f82b7eee', 'Both', false);
       
    INSERT INTO network_ap_group (id, tenant, ap_group, network_venue) VALUES
        ('d8590c060dd24ac4af583a76f24423fe', 'eed4a8bf286e4d71b43c3d7ec9b13c2d', '82fefdc0ca984a8f8fd03cc7dfe4bb34', 'a0e9b3360faa40a4bfa741cf4d8ce281'),
        ('fb3f510532a44b369855986524f940b6', 'eed4a8bf286e4d71b43c3d7ec9b13c2d', '82fefdc0ca984a8f8fd03cc7dfe4bb34', 'edbfc4df1faf4517b099dc8bf17e376h');
    """)
@ExtendWith(TxCtxExtension.class)
class ApGroupRepositoryTest {

  @Autowired
  private ApGroupRepository repository;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Nested
  @ApplyTemplateFilter
  class InTemplateFlow {

    @Test
    void testFindAllDistinctTenantIds() {
      var result = repository.findAllDistinctTenantIds();
      assertThat(result)
          .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
          .contains("eed4a8bf286e4d71b43c3d7ec9b13c2d");
    }

    @Test
    void testFindByIdAndVenueIdAndTenantId() {
      final String apGroupId = "1b42a14ada374d6dbf563ef756ef70f2";
      final String venueId = "71ad469e254e4bf9a98e5fbfbbcae8f0";
      final String tenantId = "a782b402d95c46b4aabce87b77e68613";

      var result = repository.findByIdAndVenueIdAndTenantId(apGroupId, venueId, tenantId);
      assertThat(result).isEmpty();
    }

    @Test
    void testFindByTenantIdAndApsNotEmptyOrNetworkApGroupNotEmpty() {
      final String tenantId1 = "4c8279f79307415fa9e4c88a1819f0fg";

      final var result1 = repository.findByTenantIdAndApsNotEmptyOrNetworkApGroupNotEmpty(
          Pageable.unpaged(), tenantId1);
      assertThat(result1).isEmpty();

      final String tenantId2 = "a782b402d95c46b4aabce87b77e68613";

      final var result2 = repository.findByTenantIdAndApsNotEmptyOrNetworkApGroupNotEmpty(
          Pageable.unpaged(), tenantId2);
      assertThat(result2).isEmpty();

      final String tenantId3 = "eed4a8bf286e4d71b43c3d7ec9b13c2d";

      final var result3 = repository.findByTenantIdAndApsNotEmptyOrNetworkApGroupNotEmpty(
          Pageable.unpaged(), tenantId3);
      assertThat(result3)
          .isNotEmpty().singleElement()
          .satisfies(apGroup -> assertSoftly(softly -> {
            softly.assertThat(apGroup.getId()).isEqualTo("82fefdc0ca984a8f8fd03cc7dfe4bb34");
            softly.assertThat(apGroup.getIsDefault()).isTrue();
            softly.assertThat(apGroup.getName()).isEmpty();
          }));
      ;
    }

    @Test
    void testFindByVenueIdInAndTenantId() {
      assertThat(repository
          .findByVenueIdInAndTenantId(List.of("4a1df860e61c4c93a65d5472a0576c4c"),
              "4c8279f79307415fa9e4c88a1819f0fg"))
          .isEmpty();

      assertThat(repository
          .findByVenueIdInAndTenantId(List.of("4a1df860e61c4c93a65d5472a0576c4c"),
              "another tenant"))
          .isEmpty();
    }

    @Test
    void testFindByVenueIdInAndisDefaultAndTenantId() {
      assertThat(repository
          .findByVenueIdInAndIsDefaultAndTenantId(List.of("4a1df860e61c4c93a65d5472a0576c4c"), true,
              "4c8279f79307415fa9e4c88a1819f0fg"))
          .isEmpty();

      assertThat(repository
          .findByVenueIdInAndIsDefaultAndTenantId(List.of("4a1df860e61c4c93a65d5472a0576c4c"),
              false,
              "4c8279f79307415fa9e4c88a1819f0fg"))
          .isEmpty();
    }

    @Test
    void testFindApGroupsByTenantIdAndApGroupIds() {
      final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fg";
      final String GROUP_ID_1 = "33335fa13e444d62ac64af9ab4ec5796";
      final String GROUP_ID_2 = "fdd94f9fd66641b9b893ae8f7c5848dg";
      var result = repository.findByTenantIdAndIdIn(TENANT_ID,
          List.of(GROUP_ID_1, GROUP_ID_2));
      assertThat(result).isEmpty();
    }

    @Test
    void testExistsByVenueIdAndNameAndIdNot() {
      assertThat(repository.existsByVenueIdAndNameAndIdNot("4a1df860e61c4c93a65d5472a0576c4c",
          "AP-Group-1", "fdd94f9fd66641b9b893ae8f7c5848dg"))
          .isFalse();
      assertThat(repository.existsByVenueIdAndNameAndIdNot("4a1df860e61c4c93a65d5472a0576c4c",
          "AP-Group-1", "otherId"))
          .isFalse();
    }

    @Test
    void testFindByVenueIsTemplateAndTenantId() {
      assertThat(repository.findByVenueIsTemplateAndTenantId("4c8279f79307415fa9e4c88a1819f0fg"))
          .isEmpty();
      assertThat(repository.findByVenueIsTemplateAndTenantId("a782b402d95c46b4aabce87b77e68613"))
          .isEmpty();
      assertThat(repository.findByVenueIsTemplateAndTenantId("eed4a8bf286e4d71b43c3d7ec9b13c2d"))
          .hasSize(1)
          .extracting(AbstractBaseEntity::getId)
          .containsOnly("82fefdc0ca984a8f8fd03cc7dfe4bb34");
    }
  }

  @Nested
  class OutOfTemplateFlow {

    @Test
    void testFindAllDistinctTenantIds() {
      var result = repository.findAllDistinctTenantIds();
      assertThat(result)
          .isNotEmpty().hasSizeGreaterThanOrEqualTo(2)
          .contains("4c8279f79307415fa9e4c88a1819f0fg",
              "a782b402d95c46b4aabce87b77e68613");
    }

    @Test
    void testFindByIdAndVenueIdAndTenantId() {
      final String apGroupId = "1b42a14ada374d6dbf563ef756ef70f2";
      final String venueId = "71ad469e254e4bf9a98e5fbfbbcae8f0";
      final String tenantId = "a782b402d95c46b4aabce87b77e68613";

      var result = repository.findByIdAndVenueIdAndTenantId(apGroupId, venueId, tenantId);
      assertThat(result)
          .isPresent()
          .hasValueSatisfying(apGroup -> assertSoftly(softly -> {
            softly.assertThat(apGroup.getId()).isEqualTo(apGroupId);
            softly.assertThat(apGroup.getIsDefault()).isTrue();
            softly.assertThat(apGroup.getName()).isEmpty();
            softly.assertThat(apGroup.getVenue().getId()).isEqualTo(venueId);
            softly.assertThat(apGroup.getTenant().getId()).isEqualTo(tenantId);
            softly.assertThat(apGroup.getAps())
                .isNotEmpty().singleElement()
                .satisfies(ap -> {
                  assertThat(ap.getId()).isEqualTo("777777777711");
                  assertThat(ap.getName()).isEqualTo("AP-11");
                });
          }));
    }

    @Test
    void testFindByTenantIdAndApsNotEmptyOrNetworkApGroupNotEmpty() {
      final String tenantId1 = "4c8279f79307415fa9e4c88a1819f0fg";

      final var result1 = repository.findByTenantIdAndApsNotEmptyOrNetworkApGroupNotEmpty(
          Pageable.unpaged(), tenantId1);
      assertThat(result1).isEmpty();

      final String tenantId2 = "a782b402d95c46b4aabce87b77e68613";

      final var result2 = repository.findByTenantIdAndApsNotEmptyOrNetworkApGroupNotEmpty(
          Pageable.unpaged(), tenantId2);
      assertThat(result2)
          .isNotEmpty().singleElement()
          .satisfies(apGroup -> assertSoftly(softly -> {
            softly.assertThat(apGroup.getId()).isEqualTo("1b42a14ada374d6dbf563ef756ef70f2");
            softly.assertThat(apGroup.getIsDefault()).isTrue();
            softly.assertThat(apGroup.getName()).isEmpty();
          }));

      final String tenantId3 = "eed4a8bf286e4d71b43c3d7ec9b13c2d";

      final var result3 = repository.findByTenantIdAndApsNotEmptyOrNetworkApGroupNotEmpty(
          Pageable.unpaged(), tenantId3);
      assertThat(result3).isEmpty();
    }

    @Test
    void testFindByVenueIdInAndTenantId() {
      assertThat(repository
          .findByVenueIdInAndTenantId(List.of("4a1df860e61c4c93a65d5472a0576c4c"),
              "4c8279f79307415fa9e4c88a1819f0fg"))
          .hasSize(3)
          .extracting(AbstractBaseEntity::getId)
          .containsOnly("33335fa13e444d62ac64af9ab4ec5796", "fdd94f9fd66641b9b893ae8f7c5848dg",
              "58bc286013e1468e9867061e8dbed380");

      assertThat(repository
          .findByVenueIdInAndTenantId(List.of("4a1df860e61c4c93a65d5472a0576c4c"),
              "another tenant"))
          .isEmpty();
    }

    @Test
    void testFindByVenueIdInAndisDefaultAndTenantId() {
      assertThat(repository
          .findByVenueIdInAndIsDefaultAndTenantId(List.of("4a1df860e61c4c93a65d5472a0576c4c"), true,
              "4c8279f79307415fa9e4c88a1819f0fg"))
          .hasSize(1)
          .extracting(AbstractBaseEntity::getId)
          .containsOnly("33335fa13e444d62ac64af9ab4ec5796");

      assertThat(repository
          .findByVenueIdInAndIsDefaultAndTenantId(List.of("4a1df860e61c4c93a65d5472a0576c4c"),
              false,
              "4c8279f79307415fa9e4c88a1819f0fg"))
          .hasSize(2)
          .extracting(AbstractBaseEntity::getId)
          .containsOnly("fdd94f9fd66641b9b893ae8f7c5848dg", "58bc286013e1468e9867061e8dbed380");
    }

    @Test
    void testFindApGroupsByTenantIdAndApGroupIds() {
      final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fg";
      final String GROUP_ID_1 = "33335fa13e444d62ac64af9ab4ec5796";
      final String GROUP_ID_2 = "fdd94f9fd66641b9b893ae8f7c5848dg";
      var result = repository.findByTenantIdAndIdIn(TENANT_ID,
          List.of(GROUP_ID_1, GROUP_ID_2));
      assertThat(result).isNotEmpty()
          .hasSize(2)
          .extracting(AbstractBaseEntity::getId)
          .containsOnly(GROUP_ID_1, GROUP_ID_2);
    }

    @Test
    void testExistsByVenueIdAndNameAndIdNot() {
      assertThat(repository.existsByVenueIdAndNameAndIdNot("4a1df860e61c4c93a65d5472a0576c4c",
          "AP-Group-1", "fdd94f9fd66641b9b893ae8f7c5848dg"))
          .isFalse();
      assertThat(repository.existsByVenueIdAndNameAndIdNot("4a1df860e61c4c93a65d5472a0576c4c",
          "AP-Group-1", "otherId"))
          .isTrue();
    }

    @Test
    void testFindByVenueIsTemplateAndTenantId() {
      assertThat(repository.findByVenueIsTemplateAndTenantId("4c8279f79307415fa9e4c88a1819f0fg"))
          .isEmpty();
      assertThat(repository.findByVenueIsTemplateAndTenantId("a782b402d95c46b4aabce87b77e68613"))
          .isEmpty();
      assertThat(repository.findByVenueIsTemplateAndTenantId("eed4a8bf286e4d71b43c3d7ec9b13c2d"))
          .isEmpty();
    }
  }

  @Test
  void testFindAllDistinctTenantIdsWithVenueIsTemplate() {
    assertThat(repository.findAllDistinctTenantIdsWithVenueIsTemplate())
        .hasSizeGreaterThanOrEqualTo(1)
        .contains("eed4a8bf286e4d71b43c3d7ec9b13c2d")
        .doesNotContain("4c8279f79307415fa9e4c88a1819f0fg", "a782b402d95c46b4aabce87b77e68613");
  }

  @Test
  void testFindProtoProjectionByTenantIdAndVenueIds() {
    List<VenueApGroupProtoProjection> result = repository.findProtoProjectionByTenantIdAndVenueIds(
        "4c8279f79307415fa9e4c88a1819f0fg", List.of("4a1df860e61c4c93a65d5472a0576c4c"));
    assertThat(result).hasSize(3)
        .satisfies(apGroups -> assertSoftly(softly -> {
              assertThat(apGroups.stream()
                  .filter(apGroup ->
                      apGroup.id().equals("33335fa13e444d62ac64af9ab4ec5796") &&
                          apGroup.venueId().equals("4a1df860e61c4c93a65d5472a0576c4c") &&
                          apGroup.isDefault().booleanValue() == true))
                  .hasSize(1);
              assertThat(apGroups.stream()
                  .filter(apGroup ->
                      apGroup.id().equals("fdd94f9fd66641b9b893ae8f7c5848dg") &&
                          apGroup.venueId().equals("4a1df860e61c4c93a65d5472a0576c4c") &&
                          apGroup.isDefault().booleanValue() == false &&
                          apGroup.name().equals("AP-Group-1")))
                  .hasSize(1);
              assertThat(apGroups.stream()
                  .filter(apGroup ->
                      apGroup.id().equals("58bc286013e1468e9867061e8dbed380") &&
                          apGroup.venueId().equals("4a1df860e61c4c93a65d5472a0576c4c") &&
                          apGroup.isDefault().booleanValue() == false &&
                          apGroup.name().equals("AP-Group-2")))
                  .hasSize(1);
            }
        ));
  }
}
