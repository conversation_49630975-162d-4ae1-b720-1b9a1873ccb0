package com.ruckus.cloud.wifi.service.housekeeping;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.eda.api.rest.viewmodel.HouseKeepingResponse.TableDetail;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ScheduledExecutorService;
import org.junit.jupiter.api.Test;

public class HousekeepingExecutorServiceImplTest {

  @Test
  void testSubmitDeleteRevisionTask() {
    var housekeepingMetric = mock(HousekeepingMetric.class);
    var housekeepingService = mock(HousekeepingService.class);
    var unit = new HousekeepingExecutorServiceImpl(true, 50, 50, 100,
        mock(ScheduledExecutorService.class), housekeepingMetric, housekeepingService);

    doReturn(10l).when(housekeepingService).getRevisionCount();
    doReturn(List.of(1l, 2l, 3l)).when(housekeepingService)
        .queryExpiredRevisionIds(anyInt(), anyInt());

    unit.submitDeleteRevisionTask(1, 10);

    assertThat(unit.getExpiredRevisionIdsToDelete()).isEqualTo(3);
    verify(housekeepingMetric, times(1)).updateRevisionCount(eq(10l));
  }

  @Test
  void testSubmitDeleteRevisionTask_NoExpiredRevisions() {
    var housekeepingMetric = mock(HousekeepingMetric.class);
    var housekeepingService = mock(HousekeepingService.class);
    var unit = new HousekeepingExecutorServiceImpl(true, 50, 50, 100,
        mock(ScheduledExecutorService.class), housekeepingMetric, housekeepingService);

    doReturn(10l).when(housekeepingService).getRevisionCount();
    doReturn(Collections.emptyList()).when(housekeepingService)
        .queryExpiredRevisionIds(anyInt(), anyInt());

    unit.submitDeleteRevisionTask(1, 10);

    assertThat(unit.getExpiredRevisionIdsToDelete()).isEqualTo(0);
    verify(housekeepingMetric, times(1)).onRevisionsDeleted(eq(0l));
  }

  @Test
  void testSubmitDeleteRevisionTask_NotEnable() {
    var housekeepingMetric = mock(HousekeepingMetric.class);
    var housekeepingService = mock(HousekeepingService.class);
    var unit = new HousekeepingExecutorServiceImpl(false, 50, 50, 100,
        mock(ScheduledExecutorService.class), housekeepingMetric, housekeepingService);

    unit.submitDeleteRevisionTask(1, 10);
    verify(housekeepingService, never()).queryExpiredRevisionIds(anyInt(), anyInt());
  }

  @Test
  void testSubmitDeleteRevisionTask_ExceedCapacity() {
    var housekeepingMetric = mock(HousekeepingMetric.class);
    var housekeepingService = mock(HousekeepingService.class);
    var unit = new HousekeepingExecutorServiceImpl(true, 50, 50, 0,
        mock(ScheduledExecutorService.class), housekeepingMetric, housekeepingService);

    unit.submitDeleteRevisionTask(1, 10);
    verify(housekeepingService, never()).queryExpiredRevisionIds(anyInt(), anyInt());
  }

  @Test
  void testDeleteExpiredRevisions() {
    var housekeepingMetric = mock(HousekeepingMetric.class);
    var housekeepingService = mock(HousekeepingService.class);
    var executor = mock(ScheduledExecutorService.class);
    var unit = new HousekeepingExecutorServiceImpl(true, 2, 50, 100,
        executor, housekeepingMetric, housekeepingService);

    doReturn(List.of(1l, 2l, 3l)).when(housekeepingService)
        .queryExpiredRevisionIds(anyInt(), anyInt());
    doReturn(List.of(
        new TableDetail("table1",5, 1l),
        new TableDetail("table2",6, 1l)
    )).when(housekeepingService).deleteRevisionData(anySet());

    unit.submitDeleteRevisionTask(1, 10);
    unit.deleteRevisionData();

    verify(housekeepingService, times(1)).deleteRevisionData(Set.of(1l, 2l));
    verify(housekeepingMetric, times(1)).onRevisionsDeleted(eq(2l));
  }

  @Test
  void testDeleteExpiredRevisions_DeleteFailed() {
    var housekeepingMetric = mock(HousekeepingMetric.class);
    var housekeepingService = mock(HousekeepingService.class);
    var executor = mock(ScheduledExecutorService.class);
    var unit = new HousekeepingExecutorServiceImpl(true, 2, 50, 100,
        executor, housekeepingMetric, housekeepingService);

    doThrow(new RuntimeException()).when(housekeepingService).deleteRevisionData(anySet());
    doReturn(List.of(1l, 2l, 3l)).when(housekeepingService)
        .queryExpiredRevisionIds(anyInt(), anyInt());

    unit.submitDeleteRevisionTask(1, 10);
    unit.deleteRevisionData();

    verify(housekeepingMetric, times(1)).onRevisionsDeleteFailed();
  }

  @Test
  void testDeleteExpiredRevisions_EmptyList() {
    var housekeepingMetric = mock(HousekeepingMetric.class);
    var housekeepingService = mock(HousekeepingService.class);
    var executor = mock(ScheduledExecutorService.class);
    var unit = new HousekeepingExecutorServiceImpl(true, 2, 50, 100,
        executor, housekeepingMetric, housekeepingService);

    unit.deleteRevisionData();

    verify(housekeepingService, never()).deleteRevisionData(anySet());
  }
}
