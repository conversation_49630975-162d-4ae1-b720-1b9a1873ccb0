package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DeviceTypeEnum.Gaming;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.PlayStation;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.PlayStation2;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.PlayStation3;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.Xbox;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.Xbox360;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.mockTxChanges;
import static java.util.Collections.emptySet;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.DeviceTypeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.OsVendorEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Tag("DevicePolicyTest")
@WifiUnitTest
class DdccmDevicePolicyOperationBuilderTest {

  @SpyBean
  private DdccmDevicePolicyOperationBuilder builder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void testAddDevicePolicy() {
    var devicePolicy = Generators.devicePolicy().generate();
    devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy).generate(OsVendorEnum.values().length));

    assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
        .isNotNull()
        .satisfies(op -> {
          assertThat(op.getAction()).isEqualTo(Action.ADD);
          assertThat(op.getId()).isEqualTo(devicePolicy.getId());
        })
        .extracting(Operation::getDevicePolicy)
        .satisfies(ddccmDevicePolicy -> {
          assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
          assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
          assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(devicePolicy.getRules().size());
        });
  }

  @Test
  void testModifyDevicePolicy() {
    var devicePolicy = Generators.devicePolicy().generate();
    devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy).generate(OsVendorEnum.values().length));

    assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
        .isNotNull()
        .satisfies(op -> {
          assertThat(op.getAction()).isEqualTo(Action.MODIFY);
          assertThat(op.getId()).isEqualTo(devicePolicy.getId());
        })
        .extracting(Operation::getDevicePolicy)
        .satisfies(ddccmDevicePolicy -> {
          assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
          assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
          assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(devicePolicy.getRules().size());
        });
  }

  @Test
  void testDeleteDevicePolicy() {
    var devicePolicy = Generators.devicePolicy().generate();
    devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy).generate(OsVendorEnum.values().length));

    assertThat(buildDevicePolicyOperation(new DeletedTxEntity<>(devicePolicy)))
        .isNotNull()
        .satisfies(op -> {
          assertThat(op.getAction()).isEqualTo(Action.DELETE);
          assertThat(op.getId()).isEqualTo(devicePolicy.getId());
        })
        .extracting(Operation::getDevicePolicy)
        .satisfies(ddccmDevicePolicy -> {
          assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
          assertThat(ddccmDevicePolicy.getName()).isEqualTo(StringUtils.EMPTY);
          assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isZero();
        });
  }

  @Nested
  class WithLegacyGamingDevicePolicyRules {

    abstract class AbstractOldImplementationTest {

      @Test
      void testAddDevicePolicyWithPlayStation() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.PLAYSTATION);
                  });
            });
      }

      @Test
      void testAddDevicePolicyWithPlayStation2() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation2)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.PS2);
                  });
            });
      }

      @Test
      void testAddDevicePolicyWithPlayStation3() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation3)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.PS3_PSP);
                  });
            });
      }

      @Test
      void testAddDevicePolicyWithPlayStation2AndPlayStation3() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming))
            .setOsVendor(options(PlayStation2, PlayStation3)).generate(2));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS2),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS3_PSP));
            });
      }

      @Test
      void testAddDevicePolicyWithXbox() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(Xbox)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.XBOX);
                  });
            });
      }

      @Test
      void testAddDevicePolicyWithXbox360() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(Xbox360)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.XBOX_360);
                  });
            });
      }

      @Test
      void testAddDevicePolicyWithXbox360AndXbox() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming))
            .setOsVendor(options(Xbox360, Xbox)).generate(2));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX_360),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX));
            });
      }

      @Test
      void testModifyDevicePolicyWithPlayStation() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.PLAYSTATION);
                  });
            });
      }

      @Test
      void testModifyDevicePolicyWithPlayStation2() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation2)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.PS2);
                  });
            });
      }

      @Test
      void testModifyDevicePolicyWithPlayStation3() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation3)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.PS3_PSP);
                  });
            });
      }

      @Test
      void testModifyDevicePolicyWithPlayStation2AndPlayStation3() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming))
            .setOsVendor(options(PlayStation2, PlayStation3)).generate(2));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS2),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS3_PSP));
            });
      }

      @Test
      void testModifyDevicePolicyWithXbox() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(Xbox)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.XBOX);
                  });
            });
      }

      @Test
      void testModifyDevicePolicyWithXbox360() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(Xbox360)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .singleElement().satisfies(ddccmRule -> {
                    assertThat(ddccmRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                    assertThat(ddccmRule.getOsVendor()).isEqualTo(OsVendorEnum.XBOX_360);
                  });
            });
      }

      @Test
      void testModifyDevicePolicyWithXbox360AndXbox() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming))
            .setOsVendor(options(Xbox360, Xbox)).generate(2));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX_360),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX));
            });
      }
    }

    abstract class AbstractNewImplementationTest {

      @Test
      void testAddDevicePolicyWithPlayStation() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PLAYSTATION),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS2));
            });
      }

      @Test
      void testAddDevicePolicyWithPlayStation2() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation2)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS2),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PLAYSTATION));
            });
      }

      @Test
      void testAddDevicePolicyWithPlayStation3() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation3)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(1)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PLAYSTATION));
            });
      }

      @Test
      void testAddDevicePolicyWithPlayStation2AndPlayStation3() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming))
            .setOsVendor(options(PlayStation2, PlayStation3)).generate(2));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS2),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PLAYSTATION));
            });
      }

      @Test
      void testAddDevicePolicyWithXbox() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(Xbox)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX_360));
            });
      }

      @Test
      void testAddDevicePolicyWithXbox360() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(Xbox360)).generate(1));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX_360),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX));
            });
      }

      @Test
      void testAddDevicePolicyWithXbox360AndXbox() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming))
            .setOsVendor(options(Xbox360, Xbox)).generate(2));

        assertThat(buildDevicePolicyOperation(new NewTxEntity<>(devicePolicy)))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX_360),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX));
            });
      }

      @Test
      void testModifyDevicePolicyWithPlayStation() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PLAYSTATION),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS2));
            });
      }

      @Test
      void testModifyDevicePolicyWithPlayStation2() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation2)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS2),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PLAYSTATION));
            });
      }

      @Test
      void testModifyDevicePolicyWithPlayStation3() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(PlayStation3)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(1);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(1)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PLAYSTATION));
            });
      }

      @Test
      void testModifyDevicePolicyWithPlayStation2AndPlayStation3() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming))
            .setOsVendor(options(PlayStation2, PlayStation3)).generate(2));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PS2),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.PLAYSTATION));
            });
      }

      @Test
      void testModifyDevicePolicyWithXbox() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(Xbox)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX_360));
            });
      }

      @Test
      void testModifyDevicePolicyWithXbox360() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming)).setOsVendor(always(Xbox360)).generate(1));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX_360),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX));
            });
      }

      @Test
      void testModifyDevicePolicyWithXbox360AndXbox() {
        final DevicePolicy devicePolicy = Generators.devicePolicy().generate();
        devicePolicy.setRules(Generators.devicePolicyRule(devicePolicy)
            .setDeviceType(always(Gaming))
            .setOsVendor(options(Xbox360, Xbox)).generate(2));

        assertThat(buildDevicePolicyOperation(new ModifiedTxEntity<>(devicePolicy, emptySet())))
            .isNotNull()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(devicePolicy.getId());
            })
            .extracting(Operation::getDevicePolicy)
            .satisfies(ddccmDevicePolicy -> {
              assertThat(ddccmDevicePolicy.getId()).isEqualTo(devicePolicy.getId());
              assertThat(ddccmDevicePolicy.getName()).isEqualTo(devicePolicy.getName());
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
              assertThat(ddccmDevicePolicy.getDevicePolicyRulesList())
                  .hasSize(2)
                  .extracting("deviceType", "osVendor")
                  .containsSequence(
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX_360),
                      tuple(DeviceTypeEnum.GAMING, OsVendorEnum.XBOX));
            });
      }
    }

    @FeatureFlag(disable = FlagNames.ACX_UI_NEW_OS_VENDOR_IN_DEVICE_POLICY_TOGGLE)
    @Nested
    class AndFeatureFlagDisabled extends AbstractOldImplementationTest {

      @FeatureRole("AP-70")
      @Nested
      class WithFeatureRoleAP70 extends AbstractOldImplementationTest {
      }
    }

    @FeatureFlag(enable = FlagNames.ACX_UI_NEW_OS_VENDOR_IN_DEVICE_POLICY_TOGGLE)
    @Nested
    class AndFeatureFlagEnabled extends AbstractOldImplementationTest {

      @FeatureRole("AP-70")
      @Nested
      class WithFeatureRoleAP70 extends AbstractNewImplementationTest {
      }
    }
  }

  private Operation buildDevicePolicyOperation(
      TxEntity<DevicePolicy> txEntity) {
    return builder.build(txEntity, mockTxChanges()).stream()
        .filter(Operation::hasDevicePolicy).findFirst().orElse(null);
  }
}
