package com.ruckus.cloud.wifi.integration.apgroup.template;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.header.Header;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@Tag("ApGroupTemplateTest")
@WifiIntegrationTest
public class ConsumeAddApGroupTemplateRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class whenConsumeAddApGroupTemplateRequest {

    @Nested
    class givenVenueIsTemplate {
      @Test
      void thenSaveApGroupIsTemplate(@Template Venue venue,
          @Template @DefaultApGroup ApGroup defaultApGroup) {
        final var requestId = randomTxId();
        final var userName = randomName();
        final var apGroupName = randomName();
        final var tenantId = defaultApGroup.getTenant().getId();

        final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.ApGroup();
        payload.setId(randomId()); // autoGenerated is true in wifi-api
        payload.setName(apGroupName);

        RequestParams requestParams = new RequestParams();
        requestParams.addPathVariable("venueId", venue.getId());

        messageUtil.sendWifiCfgRequest(
            tenantId, requestId, CfgAction.ADD_AP_GROUP_TEMPLATE, userName,
            requestParams, payload);

        validateResult(tenantId, requestId, venue.getId(), payload);
      }
    }
  }

  void validateResult(String tenantId, String requestId, String venueId,
      com.ruckus.cloud.wifi.eda.viewmodel.ApGroup payload) {
    final var revision = revisionService.changes(requestId, tenantId,
        ConfigRequestHandler.apiActionToFlowName(CfgAction.ADD_AP_GROUP_TEMPLATE.key()));
    final var apGroupId = revision.getNewEntities().stream()
        .filter(e -> e.getEntity() instanceof ApGroup).findFirst().get().getId();
    final var apGroup = repositoryUtil.find(ApGroup.class, apGroupId);

    assertThat(apGroup)
        .isNotNull()
        .matches(a -> Objects.equals(a.getId(), apGroupId))
        .matches(a -> Objects.equals(a.getName(), payload.getName()))
        .extracting(ApGroup::getVenue)
        .isNotNull()
        .matches(v -> Objects.equals(v.getId(), venueId));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    // validate AP Group cmnCfgCollectorMessage
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
        .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
        .isNotEmpty()
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> payload.getId().equals(op.getId()))
              .isNotEmpty().singleElement()
              .satisfies(op -> {
                assertThat(op.getOpType()).isEqualTo(OpType.ADD);
                assertThat(op.getDocMap())
                    .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                    .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                    .containsEntry(Key.ID, ValueUtils.stringValue(payload.getId()))
                    .containsEntry(Key.NAME, ValueUtils.stringValue(payload.getName()))
                    .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                    .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                    .containsEntry(Key.IS_TEMPLATE, ValueUtils.boolValue(true))
                    .satisfies(docMap -> {
                      if (StringUtils.isNotEmpty(payload.getDescription())) {
                        assertThat(docMap)
                            .containsEntry(Key.DESCRIPTION,
                                ValueUtils.stringValue(payload.getDescription()));
                      }
                    });
              });
        });

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenantId);

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(Status.OK))
        .matches(p -> p.getStep().equals(ApiFlowNames.ADD_AP_GROUP_TEMPLATE))
        .extracting(ConfigurationStatus::getEventDate).isNotNull();

    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);
  }
}
