package com.ruckus.cloud.wifi.integration.radius;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_NETWORK_RADIUS_ACCOUNTING_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.WisprPage;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeDeactivateRadiusServerProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  public static final String RADIUS_ID = "radiusId";
  public static final String WIFI_NETWORK_ID = "wifiNetworkId";

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeDeactivateRadiusServerProfileOnWifiNetworkMessage {

    private String profileId;
    private String networkId;

    @BeforeEach
    void beforeEach(Radius radius, Network network) {
      profileId = radius.getId();
      networkId = network.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Nested
    class GivenNetworkExists {

      @BeforeEach
      void beforeEach(Tenant tenant, Network network, Radius radius) {
        repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
        network.setAuthRadius(radius);
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      }

      @Test
      void givenRadiusServerProfileDeactivatedOnNetwork(Tenant tenant) {
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        assertThat(
            messageCaptors
                .getCmnCfgCollectorMessageCaptor()
                .getValue(tenant.getId(), Duration.ofSeconds(1)))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(2);

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a -> a.getStep()
                    .equals(ApiFlowNames.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
      }
    }

    @Nested
    class GivenNetworkBindingVenue {

      @BeforeEach
      void beforeEach(Tenant tenant, Network network, Radius authRadius, Radius acctRadius, Venue venue) {
        network.setAuthRadius(authRadius);
        acctRadius.setType(RadiusProfileTypeEnum.ACCOUNTING);
        network.setAccountingRadius(acctRadius);
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
        profileId = acctRadius.getId();
        repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
        NetworkVenue networkVenue = new NetworkVenue();
        networkVenue.setNetwork(network);
        networkVenue.setVenue(venue);
        network.setNetworkVenues(List.of(networkVenue));
        repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
        var accountingRadiusVenue = new AccountingRadiusVenue();
        accountingRadiusVenue.setVenue(venue);
        accountingRadiusVenue.setRadius(acctRadius);
        repositoryUtil.createOrUpdate(accountingRadiusVenue, tenant.getId(), randomTxId());
      }

      @Test
      void givenRadiusServerProfileDeactivatedOnNetwork(Tenant tenant) {
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        assertThat(
            messageCaptors
                .getCmnCfgCollectorMessageCaptor()
                .getValue(tenant.getId(), Duration.ofSeconds(1)))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(2);

        assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId(), Duration.ofSeconds(1)))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(WifiConfigRequest::getOperationsList)
            .asList()
            .extracting(Operation.class::cast)
            .hasSize(1)
            .singleElement()
            .matches(op -> op.getConfigCase() == ConfigCase.WLANVENUE)
            .satisfies(op -> {
              var wlanVenue = op.getWlanVenue();
              assertThat(wlanVenue.getAccountingServerId()).isNotEmpty();
            });

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a -> a.getStep()
                    .equals(ApiFlowNames.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
      }
    }
  }

  @Nested
  class WhenConsumeActivateAuthRadiusServerProfileOnGuestWISPrNetworkMessage {

    private String profileId2;
    private String networkId;
    private String authRadiusServiceId;
    private String authRadiusProfileId;

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId2)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Nested
    class GivenWISPrNetworkExists {

      @BeforeEach
      void beforeEach(Tenant tenant,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork(guestNetworkType = GuestNetworkTypeEnum.WISPr) GuestNetwork network,
          Radius radius, Radius radius2) {
        profileId2 = radius2.getId();
        networkId = network.getId();

        WisprPage wisprPage = Generators.wisprPage().generate();
        GuestPortal portal = network.getGuestPortal();
        portal.setWisprPage(wisprPage);
        wisprPage.setAuthRadius(radius);

        AuthRadiusService authRadiusService =
            com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.authRadiusService(radius)
                .generate();
        AuthRadiusProfile authRadiusProfile =
            com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.authRadiusProfile(
                authRadiusService).generate();
        authRadiusService.setRadius(radius);
        authRadiusService.setGuestPortal(portal);
        authRadiusServiceId = authRadiusService.getId();
        authRadiusProfileId = authRadiusProfile.getId();

        radius.setAuthRadiusServices(List.of(authRadiusService));

        repositoryUtil.createOrUpdate(authRadiusService, tenant.getId(), randomTxId());
        repositoryUtil.createOrUpdate(authRadiusProfile, tenant.getId(), randomTxId());
        repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
        network.setAuthRadius(radius);
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

      }

      @Test
      void givenRadiusServerProfileActivatedOnWISPrNetwork(Tenant tenant) {
        assertNotNull(repositoryUtil.find(AuthRadiusService.class, authRadiusServiceId));
        assertNotNull(repositoryUtil.find(AuthRadiusProfile.class, authRadiusProfileId));
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        assertThat(
            messageCaptors
                .getCmnCfgCollectorMessageCaptor()
                .getValue(tenant.getId(), Duration.ofSeconds(1)))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(1);

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a -> a.getStep()
                    .equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));

        assertNull(repositoryUtil.find(AuthRadiusService.class, authRadiusServiceId));
        assertNull(repositoryUtil.find(AuthRadiusProfile.class, authRadiusProfileId));
      }
    }
  }

  @Nested
  class WhenConsumeDeactivateAuthRadiusServerProfileOnGuestWISPrNetworkMessage {

    private String profileId;
    private String networkId;
    private String authRadiusServiceId;
    private String authRadiusProfileId;

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Nested
    class GivenWISPrNetworkExists {

      @BeforeEach
      void beforeEach(Tenant tenant,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork(guestNetworkType = GuestNetworkTypeEnum.WISPr) GuestNetwork network,
          Radius radius) {
        profileId = radius.getId();
        networkId = network.getId();

        WisprPage wisprPage = Generators.wisprPage().generate();
        GuestPortal portal = network.getGuestPortal();
        portal.setWisprPage(wisprPage);
        wisprPage.setAuthRadius(radius);

        AuthRadiusService authRadiusService =
            com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.authRadiusService(radius)
                .generate();
        AuthRadiusProfile authRadiusProfile =
            com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.authRadiusProfile(
                authRadiusService).generate();
        authRadiusService.setRadius(radius);
        authRadiusService.setGuestPortal(portal);
        authRadiusServiceId = authRadiusService.getId();
        authRadiusProfileId = authRadiusProfile.getId();

        radius.setAuthRadiusServices(List.of(authRadiusService));

        repositoryUtil.createOrUpdate(authRadiusService, tenant.getId(), randomTxId());
        repositoryUtil.createOrUpdate(authRadiusProfile, tenant.getId(), randomTxId());
        repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
        network.setAuthRadius(radius);
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

      }

      @Test
      void givenRadiusServerProfileDeactivatedOnWISPrNetwork(Tenant tenant) {
        assertNotNull(repositoryUtil.find(AuthRadiusService.class, authRadiusServiceId));
        assertNotNull(repositoryUtil.find(AuthRadiusProfile.class, authRadiusProfileId));
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        assertThat(
            messageCaptors
                .getCmnCfgCollectorMessageCaptor()
                .getValue(tenant.getId(), Duration.ofSeconds(1)))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(1);

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a -> a.getStep()
                    .equals(ApiFlowNames.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));

        assertNull(repositoryUtil.find(AuthRadiusService.class, authRadiusServiceId));
        assertNull(repositoryUtil.find(AuthRadiusProfile.class, authRadiusProfileId));
      }
    }
  }

  @Nested
  class WhenConsumeDeactivateAcctRadiusServerProfileOnGuestWISPrNetworkMessage {

    private String profileId;
    private String networkId;
    private String acctRadiusServiceId;
    private String acctRadiusProfileId;

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Nested
    class GivenWISPrNetworkExists {

      @BeforeEach
      void beforeEach(Tenant tenant,
          @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork(guestNetworkType = GuestNetworkTypeEnum.WISPr) GuestNetwork network,
          Radius radius) {
        profileId = radius.getId();
        networkId = network.getId();
        radius.setType(RadiusProfileTypeEnum.ACCOUNTING);

        WisprPage wisprPage = Generators.wisprPage().generate();
        GuestPortal portal = network.getGuestPortal();
        portal.setWisprPage(wisprPage);
        wisprPage.setAccountingRadius(radius);

        AccountingRadiusService accountingRadiusService =
            com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accountingRadiusService(radius)
                .generate();
        AccountingRadiusProfile accountingRadiusProfile =
            com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accountingRadiusProfile(
                accountingRadiusService).generate();
        accountingRadiusService.setRadius(radius);
        accountingRadiusService.setGuestPortal(portal);
        acctRadiusServiceId = accountingRadiusService.getId();
        acctRadiusProfileId = accountingRadiusProfile.getId();

        radius.setAccountingRadiusServices(List.of(accountingRadiusService));

        repositoryUtil.createOrUpdate(accountingRadiusService, tenant.getId(), randomTxId());
        repositoryUtil.createOrUpdate(accountingRadiusProfile, tenant.getId(), randomTxId());
        repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
        network.setAccountingRadius(radius);
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

      }

      @Test
      void givenRadiusServerProfileDeactivatedOnWISPrNetwork(Tenant tenant) {
        assertNotNull(repositoryUtil.find(AccountingRadiusService.class, acctRadiusServiceId));
        assertNotNull(repositoryUtil.find(AccountingRadiusProfile.class, acctRadiusProfileId));
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        assertThat(
            messageCaptors
                .getCmnCfgCollectorMessageCaptor()
                .getValue(tenant.getId(), Duration.ofSeconds(1)))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(1);

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a -> a.getStep()
                    .equals(ApiFlowNames.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));

        assertNull(repositoryUtil.find(AccountingRadiusService.class, acctRadiusServiceId));
        assertNull(repositoryUtil.find(AccountingRadiusProfile.class, acctRadiusProfileId));
      }
    }
  }

  @Nested
  class WhenConsumeDeactivateRadiusServerProfileOnDpskNetwork {

    private String profileId;
    private String networkId;

    @BeforeEach
    void beforeEach(Radius radius, DpskNetwork network) {
      profileId = radius.getId();
      networkId = network.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Test
    void givenRaduisServerProfileDeactivatedOnDpskNetwork(Tenant tenant, Venue venue,
        DpskNetwork network,
        Radius radius) {
      repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
      network.setAuthRadius(radius);
      repositoryUtil.createOrUpdate(
          NetworkVenueTestFixture.randomNetworkVenue(network, venue), tenant.getId(),
          randomTxId());
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
                  randomName(),
                  requestParams(),
                  ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class)
          .hasMessage(
              "Cannot deactivate RADIUS authentication server profile on DPSK network which has activated on venue");

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a ->
                  a.getStep()
                      .equals(ApiFlowNames.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }
  }

  @Nested
  class WhenConsumeActivateRadiusServerProfileOnIdentityProvider {

    private String radiusId;
    private String identityProviderId;
    private String authRadiusIdDocKey = "authRadiusId";
    private String IDENTITY_PROVIDER_ID = "hotspot20IdentityProviderId";

    @BeforeEach
    void beforeEach(Tenant tenant, Radius radius) {
      radiusId = radius.getId();
      repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());

      Hotspot20IdentityProvider hotspot20IdentityProvider =
          createAndSaveIdentityProvider(tenant);
      identityProviderId = hotspot20IdentityProvider.getId();

      String requestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_HOTSPOT20IDENTITY_PROVIDER,
          randomName(),
          requestParams(),
          "");
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, radiusId)
          .addPathVariable(IDENTITY_PROVIDER_ID, identityProviderId);
    }

    @Test
    void givenRadiusServerProfileActivatedOnIdentityProvider(Tenant tenant) {

      String requestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_HOTSPOT20IDENTITY_PROVIDER,
          randomName(),
          requestParams(),
          "");

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId(), requestId);

      assertEquals(2, cmnCfgCollectorMessage.getPayload().getOperationsList().size());

      List<Operations> operationsList = cmnCfgCollectorMessage.getPayload().getOperationsList();
      Optional<Operations> providerOps = operationsList.stream().filter(operations ->
          operations.getIndex().equals(Index.HOTSPOT20_IDENTITY_PROVIDER_INDEX_NAME)).findFirst();

      // Verify sent authRadiusId nullValue to cmnCfg
      assertEquals(ValueUtils.nullValue(), providerOps.get().getDocOrThrow(authRadiusIdDocKey));
    }

    private Hotspot20IdentityProvider createAndSaveIdentityProvider(Tenant tenant) {

      var hotspot20NaiRealm = Generators.hotspot20NaiRealm().generate();
      hotspot20NaiRealm.getEaps().forEach(e -> {
        e.setNaiRealm(hotspot20NaiRealm);
        e.getAuthInfos().forEach(a -> a.setEap(e));
      });
      var hotspot20Plmn = Generators.hotspot20Plmn().generate();
      var hotspot20RoamConsortium = Generators.hotspot20RoamConsortium().generate();

      Hotspot20IdentityProvider hotspot20IdentityProvider = Generators.hotspot20IdentityProvider()
          .generate();
      hotspot20IdentityProvider.setNaiRealms(List.of(hotspot20NaiRealm));
      hotspot20IdentityProvider.setPlmns(List.of(hotspot20Plmn));
      hotspot20IdentityProvider.setRoamConsortiumOis(List.of(hotspot20RoamConsortium));

      hotspot20NaiRealm.setIdentityProvider(hotspot20IdentityProvider);
      hotspot20Plmn.setIdentityProvider(hotspot20IdentityProvider);
      hotspot20RoamConsortium.setIdentityProvider(hotspot20IdentityProvider);

      return repositoryUtil.createOrUpdate(hotspot20IdentityProvider, tenant.getId(), randomTxId());
    }
  }

  @Nested
  class WhenDeactivateAccountingRadiusServerProfileOnGuestNetwork {

    private String networkId;
    private String acctRadiusId;
    private RequestParams requestParams;

    @BeforeEach
    void setUp(Tenant tenant, @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork(guestNetworkType = GuestNetworkTypeEnum.WISPr) GuestNetwork network) {
      // Create accounting RADIUS profile
      Radius acctRadius = Generators.radiusProfile().generate();
      acctRadius.setType(RadiusProfileTypeEnum.ACCOUNTING);
      acctRadius = repositoryUtil.createOrUpdate(acctRadius, tenant.getId(), randomTxId());
      acctRadiusId = acctRadius.getId();

      // Configure network with accounting radius
      network.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.SAML);
      network.setAccountingRadius(acctRadius);
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      networkId = network.getId();
      
      // Create request params for tests
      requestParams = new RequestParams()
          .addPathVariable(RADIUS_ID, acctRadiusId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Test
    @FeatureFlag(enable = WIFI_NETWORK_RADIUS_ACCOUNTING_TOGGLE)
    void deactivateRadiusProfileSuccessfully(Tenant tenant) {
      // Act - Send deactivation request
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
          randomName(),
          requestParams,
          "");

      // Assert - Verify RADIUS profile was deactivated
      Network resultNetwork = repositoryUtil.find(Network.class, networkId);
      assertNull(resultNetwork.getAccountingRadius());

      // Verify configuration messages were sent
      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId(), Duration.ofSeconds(1)))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(2);

      // Verify successful activity status
      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    void deactivateRadiusProfileFail(Tenant tenant) {
      var requestId = txCtxExtension.getRequestId();

      // Act & Assert - Verify exception is thrown when feature flag disabled
      assertThatThrownBy(() ->
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              requestId,
              CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
              randomName(),
              requestParams,
              ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      // Verify RADIUS profile was not deactivated
      Network resultNetwork = repositoryUtil.find(Network.class, networkId);
      assertNotNull(resultNetwork.getAccountingRadius());

      // Verify no configuration messages were sent
      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());


      // Verify failure activity status
      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId(), requestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE)
  class whenConsumeDeactivateRadiusServerOnVenueCfgRequestTest {

    private String tenantId;

    private String venueId;

    private String venueAuthRadiusId;

    private String venueAcctRadiusId;

    /**
     * Scenario:
     * 1. Create an open network with networkAuthRadius and networkAcctRadius
     * 2. Activate the network with a venue (At the time, networkAuthRadiusVenue and networkAcctRadiusVenue are created)
     * 3. Update both overrideAuthRadius and overrideAccountingRadius as true on the venue
     * 4. Activate the venueAuthRadius and venueAcctRadius on the venue
     *      (At the time, venueAuthRadiusVenue and venueAcctRadiusVenue are created)
     */
    @BeforeEach
    void persistedInDb(Tenant tenant, Venue venue, Network network) {
      var venueAuthRadius = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuth().generate();
      repositoryUtil.createOrUpdate(venueAuthRadius, tenant.getId());

      var venueAcctRadius = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcct().generate();
      repositoryUtil.createOrUpdate(venueAcctRadius, tenant.getId());

      var venueAuthRadiusVenue = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
          .authRadiusVenue(venueAuthRadius, venue).generate();
      repositoryUtil.createOrUpdate(venueAuthRadiusVenue, tenant.getId());

      var venueAcctRadiusVenue = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
          .accountingRadiusVenue(venueAcctRadius, venue).generate();
      repositoryUtil.createOrUpdate(venueAcctRadiusVenue, tenant.getId());

      var networkAuthRadius = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuth().generate();
      repositoryUtil.createOrUpdate(networkAuthRadius, tenant.getId());

      var networkAcctRadius = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcct().generate();
      repositoryUtil.createOrUpdate(networkAcctRadius, tenant.getId());

      var networkAuthRadiusVenue = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
          .authRadiusVenue(networkAuthRadius, venue).generate();
      repositoryUtil.createOrUpdate(networkAuthRadiusVenue, tenant.getId());

      var networkAcctRadiusVenue = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
          .accountingRadiusVenue(networkAcctRadius, venue).generate();
      repositoryUtil.createOrUpdate(networkAcctRadiusVenue, tenant.getId());

      var networkVenue = networkVenue()
          .setNetwork(always(network)).setVenue(always(venue)).generate();
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId());

      network.setAuthRadius(networkAuthRadius);
      network.setAccountingRadius(networkAcctRadius);
      network.setNetworkVenues(List.of(networkVenue));
      network.getWlan().setMacAddressAuthentication(true);
      repositoryUtil.createOrUpdate(network, tenant.getId());

      venue.setApPassword("1qaz@WSX");
      venue.setAuthRadius(venueAuthRadius);
      venue.setAccountingRadius(venueAcctRadius);
      venue.getRadiusServerProfileSettings().setOverrideAuthRadius(true);
      venue.getRadiusServerProfileSettings().setOverrideAccountingRadius(true);
      repositoryUtil.createOrUpdate(venue, tenant.getId());

      tenantId = tenant.getId();
      venueId = venue.getId();
      venueAuthRadiusId = venueAuthRadius.getId();
      venueAcctRadiusId = venueAcctRadius.getId();
    }

    @Test
    void thenDeactivateAuthRadiusServerProfileOnVenue() {
      messageUtil.sendWifiCfgRequest(
          tenantId,
          txCtxExtension.getRequestId(),
          CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_VENUE,
          randomName(),
          new RequestParams()
              .addPathVariable("venueId", venueId)
              .addPathVariable("radiusId", venueAuthRadiusId),
          "");

      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateVenueAuthRadiusServerProfileSettings(venue);
      validateDdccmMessage();
    }

    @Test
    void thenDeactivateAccountingRadiusServerProfileOnVenue() {
      messageUtil.sendWifiCfgRequest(
          tenantId,
          txCtxExtension.getRequestId(),
          CfgAction.DEACTIVATE_RADIUS_SERVER_PROFILE_ON_VENUE,
          randomName(),
          new RequestParams()
              .addPathVariable("venueId", venueId)
              .addPathVariable("radiusId", venueAcctRadiusId),
          "");

      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateVenueAccountingRadiusServerProfileSettings(venue);
      validateDdccmMessage();
    }

    void validateVenueAuthRadiusServerProfileSettings(Venue venueFromDb) {
      assertThat(venueFromDb)
          .extracting(Venue::getAuthRadius)
          .isNull();
    }

    void validateVenueAccountingRadiusServerProfileSettings(Venue venueFromDb) {
      assertThat(venueFromDb)
          .extracting(Venue::getAccountingRadius)
          .isNull();
    }

    void validateDdccmMessage() {
      var message = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, txCtxExtension.getRequestId());
      assertThat(message).isNotNull();
      WifiConfigRequest ddccmRequest = message.getPayload();
      assertThat(ddccmRequest.getOperationsList()).isNotNull()
          .hasSize(2)
          .filteredOn(Operation::hasWlanVenue)
          .hasSize(1);
    }
  }
}
