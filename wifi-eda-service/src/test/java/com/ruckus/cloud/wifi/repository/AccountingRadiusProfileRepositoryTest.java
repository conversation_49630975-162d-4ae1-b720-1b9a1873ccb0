package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.*;

import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusProfile;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenantId001');
    INSERT INTO radius (id, tenant, is_template)
        VALUES ('radiusId001', 'tenantId001', 'false');
    INSERT INTO accounting_radius_service(id, tenant, radius)
        VALUES ('acctServiceId001', 'tenantId001', 'radiusId001');
    INSERT INTO accounting_radius_profile (id, tenant, accounting_radius_service, hotspot20support_enabled)
        VALUES ('acctProfileId001', 'tenantId001', 'acctServiceId001', 'false');
    INSERT INTO accounting_radius_service(id, tenant, radius)
        VALUES ('acctServiceId002', 'tenantId001', 'radiusId001');
    INSERT INTO accounting_radius_profile (id, tenant, accounting_radius_service, hotspot20support_enabled)
        VALUES ('acctProfileId002', 'tenantId001', 'acctServiceId002', 'true');        
    """)
class AccountingRadiusProfileRepositoryTest {

  private static final String TENANT_ID = "tenantId001";

  @Autowired
  AccountingRadiusProfileRepository repository;

  @Test
  void findByTenantIdAndAccountingRadiusServiceId() {

    AccountingRadiusProfile profile  = repository
        .findByTenantIdAndAccountingRadiusServiceId(TENANT_ID, "acctServiceId001");
    assertEquals("acctProfileId001", profile.getId());

    AccountingRadiusProfile profile0  = repository
        .findByTenantIdAndAccountingRadiusServiceIdAndHotspot20SupportEnabled(
            TENANT_ID, "acctServiceId001", true);
    assertNull(profile0);

    AccountingRadiusProfile profile1  = repository
        .findByTenantIdAndAccountingRadiusServiceIdAndHotspot20SupportEnabled(
            TENANT_ID, "acctServiceId001", false);
    assertEquals("acctProfileId001", profile1.getId());

    AccountingRadiusProfile profile2  = repository
        .findByTenantIdAndAccountingRadiusServiceIdAndHotspot20SupportEnabled(
            TENANT_ID, "acctServiceId002", true);
    assertEquals("acctProfileId002", profile2.getId());

  }
}