package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.SyslogServerProfileRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslog;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.IdAndName;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SyslogServerProfileTemplateTest")
@WifiIntegrationTest
public class ConsumeSyslogServerProfileTemplateRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired private RevisionService revisionService;

  @Autowired private KafkaTopicProvider kafkaTopicProvider;

  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_SYSLOG_SERVER_PROFILE_TEMPLATE)
  class ConsumeAddSyslogServerProfileTemplateRequestTest {

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile syslogServerProfile;

    private Venue venue;

    @BeforeEach
    void givenOneVenuePersistedInDb(@Template final Venue venue) {
      this.venue = venue;
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenAddProfile() {
      syslogServerProfile = Generators.syslogServerProfile().generate();
      syslogServerProfile.setId(CommonTestFixture.randomId());
      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(this.venue.getId());
      syslogServerProfile.setVenues(List.of(newVenueIdAndName));
      return syslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile syslogServerProfile) {
      // Then
      validateResult(CfgAction.ADD_SYSLOG_SERVER_PROFILE_TEMPLATE, syslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.ADD_SYSLOG_SERVER_PROFILE_TEMPLATE)
  class ConsumeAddSyslogServerProfileTemplateAndRebindVenueRequestTest {

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile addSyslogServerProfile;

    private Venue venue;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(@Template final Venue venue,
        @Template final SyslogServerProfile syslogServerProfile) {
      this.venue = venue;
      this.venue.setApPassword("1qaz@WSX");
      this.venue.setCountryCode("US");

      syslogServerProfile.setVenues(List.of(venue));
      repositoryUtil.createOrUpdate(syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      venue.setSyslogServerProfile(syslogServerProfile);
      venue.setSyslog(venueSyslog);
      repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenAddProfile() {
      addSyslogServerProfile = Generators.syslogServerProfile().generate();
      addSyslogServerProfile.setId(CommonTestFixture.randomId());
      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(this.venue.getId());
      addSyslogServerProfile.setVenues(List.of(newVenueIdAndName));
      return addSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile syslogServerProfile) {
      // Then
      validateResult(CfgAction.ADD_SYSLOG_SERVER_PROFILE_TEMPLATE, syslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE)
  class ConsumeUpdateSyslogServerProfileTemplateRequestTest {

    private SyslogServerProfile syslogServerProfile;

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile updateSyslogServerProfile;

    private Venue oldVenue;
    private Venue newBindVenue;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(final Tenant tenant, @Template final Venue oldVenue,
        @Template final SyslogServerProfile syslogServerProfile) {
      this.oldVenue = oldVenue;
      this.oldVenue.setApPassword("1qaz@WSX");
      this.oldVenue.setCountryCode("US");
      this.syslogServerProfile = syslogServerProfile;
      this.newBindVenue = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venue()
          .setApPassword(always("1qaz@WSX"))
          .setAddressLine(always("Somewhere"))
          .setCountryCode(always("US"))
          .setTimezone(always("America/Los_Angeles"))
          .generate();

      syslogServerProfile.setVenues(List.of(oldVenue));
      repositoryUtil.createOrUpdate(syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      oldVenue.setSyslogServerProfile(syslogServerProfile);
      oldVenue.setSyslog(venueSyslog);
      repositoryUtil.createOrUpdate(oldVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      this.newBindVenue.setTenant(tenant);
      repositoryUtil.createOrUpdate(this.newBindVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("syslogServerProfileTemplateId", syslogServerProfile.getId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenUpdateProfile() {
      updateSyslogServerProfile = Generators.syslogServerProfile().generate();

      updateSyslogServerProfile.setId(syslogServerProfile.getId());

      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(newBindVenue.getId());
      updateSyslogServerProfile.setVenues(List.of(newVenueIdAndName));

      return updateSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(
          CfgAction.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE,
          syslogServerProfile.getId(),
          updateSyslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE)
  class ConsumeUpdateSyslogServerProfileTemplateChangeVenueRequestTest {

    private SyslogServerProfile syslogServerProfile;

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile updateSyslogServerProfile;

    private Venue oldVenue;
    private Venue newBindVenue;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(final Tenant tenant, @Template final Venue oldVenue,
        @Template final SyslogServerProfile syslogServerProfile) {
      this.oldVenue = oldVenue;
      this.oldVenue.setApPassword("1qaz@WSX");
      this.oldVenue.setCountryCode("US");
      this.syslogServerProfile = syslogServerProfile;
      this.newBindVenue = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venue()
          .setApPassword(always("1qaz@WSX"))
          .setAddressLine(always("Somewhere"))
          .setCountryCode(always("US"))
          .setTimezone(always("America/Los_Angeles"))
          .generate();

      syslogServerProfile.setVenues(List.of(oldVenue));
      repositoryUtil.createOrUpdate(syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      oldVenue.setSyslogServerProfile(syslogServerProfile);
      oldVenue.setSyslog(venueSyslog);
      repositoryUtil.createOrUpdate(oldVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      this.newBindVenue.setTenant(tenant);
      repositoryUtil.createOrUpdate(this.newBindVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("syslogServerProfileTemplateId", syslogServerProfile.getId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenUpdateProfile() {
      updateSyslogServerProfile = SyslogServerProfileRestCtrl.SyslogServerProfileMapper
          .INSTANCE.ServiceSyslogServerProfile2SyslogServerProfile(syslogServerProfile);

      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(newBindVenue.getId());
      updateSyslogServerProfile.setVenues(List.of(newVenueIdAndName));

      return updateSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(
          CfgAction.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE,
          syslogServerProfile.getId(),
          updateSyslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE)
  class ConsumeUpdateSyslogServerProfileChangeVenueWithBoundSyslogRequestTest {

    private SyslogServerProfile syslogServerProfile1;

    private SyslogServerProfile syslogServerProfile2;

    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile updateSyslogServerProfile;

    private Venue venue;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(final Tenant tenant, @Template final Venue venue,
        @Template final SyslogServerProfile syslogServerProfile) {
      this.venue = venue;
      this.venue.setApPassword("1qaz@WSX");
      this.venue.setCountryCode("US");
      this.syslogServerProfile1 = syslogServerProfile;

      syslogServerProfile1.setVenues(List.of(venue));
      repositoryUtil.createOrUpdate(syslogServerProfile1, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      syslogServerProfile2 = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile()
          .setName(always("SyslogServerProfile0002"))
          .setIsTemplate(always(true))
          .setTenant(always(tenant))
          .generate();
      repositoryUtil.createOrUpdate(syslogServerProfile2, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      venue.setSyslogServerProfile(syslogServerProfile1);
      venue.setSyslog(venueSyslog);
      venue.setIsTemplate(true);
      repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("syslogServerProfileTemplateId", syslogServerProfile2.getId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile givenUpdateProfile() {
      updateSyslogServerProfile = SyslogServerProfileRestCtrl.SyslogServerProfileMapper.INSTANCE.ServiceSyslogServerProfile2SyslogServerProfile(syslogServerProfile2);

      IdAndName newVenueIdAndName = new IdAndName();
      newVenueIdAndName.setId(venue.getId());
      updateSyslogServerProfile.setVenues(List.of(newVenueIdAndName));

      return updateSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(
          CfgAction.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE,
          updateSyslogServerProfile.getId(),
          updateSyslogServerProfile);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE)
  class ConsumeDeleteSyslogServerProfileTemplateRequestTest {

    private String syslogServerProfileTemplateId;

    @BeforeEach
    void givenOneSyslogServerProfilePersistedInDb(@Template final Venue venue,
        @Template final SyslogServerProfile syslogServerProfile) {
      syslogServerProfile.setVenues(List.of(venue));
      repositoryUtil.createOrUpdate(syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());
      syslogServerProfileTemplateId = syslogServerProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("syslogServerProfileTemplateId", syslogServerProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      // Then
      validateResult(CfgAction.DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE);
    }

    private void validateResult(CfgAction apiAction) {
      assertThat(repositoryUtil.find(SyslogServerProfile.class, syslogServerProfileTemplateId)).isNull();
      validateCmnCfgCollectorMessages(apiAction, List.of(syslogServerProfileTemplateId));
      validateActivityMessages(apiAction);
    }
  }

  private void validateResult(CfgAction apiAction, com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges = revisionService.changes(requestId, tenantId,
        ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    final String syslogServerProfileTemplateId = txChanges.getNewEntities().stream()
        .filter(txEntity -> txEntity.getEntity() instanceof SyslogServerProfile)
        .map(TxEntity::getId)
        .filter(id -> !id.isEmpty())
        .findAny()
        .orElseGet(Assertions::fail);
    validateResult(apiAction, syslogServerProfileTemplateId, payload);
  }

  private void validateResult(
      CfgAction apiAction,
      String syslogServerProfileTemplateId,
      com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile payload) {
    validateRepositoryData(syslogServerProfileTemplateId, payload, apiAction);
    validateCmnCfgCollectorMessages(apiAction, List.of(syslogServerProfileTemplateId));
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(
      String syslogServerProfileTemplateId,
      com.ruckus.cloud.wifi.eda.viewmodel.SyslogServerProfile payload,
      CfgAction apiAction) {
    if (syslogServerProfileTemplateId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final SyslogServerProfile syslogServerProfile =
        repositoryUtil.find(SyslogServerProfile.class, syslogServerProfileTemplateId);

    if (payload == null) {
      assertThat(syslogServerProfile).isNull();
      return;
    }

    assertThat(syslogServerProfile)
        .isNotNull()
        .matches(profile -> Objects.equals(profile.getId(), syslogServerProfileTemplateId))
        .matches(SyslogServerProfile::getIsTemplate)
        .matches(profile -> Objects.equals(profile.getName(), payload.getName()))
        .matches(profile ->
            Objects.equals(profile.getPrimary().getServer(), payload.getPrimary().getServer()));
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, List<String> syslogServerProfileIds) {
    if (apiAction == null || syslogServerProfileIds == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
              .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList)
            .asList()
            .isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> EsConstants.Index.SYSLOG_SERVER_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .filteredOn(op -> syslogServerProfileIds.contains(op.getId()))
            .filteredOn(op -> op.getOpType() == opType(apiAction))
            .hasSize(syslogServerProfileIds.size())
            .singleElement()
            .satisfies(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op).extracting(Operations::getDocMap)
                    .matches(doc -> syslogServerProfileIds.contains(doc.get(EsConstants.Key.ID).getStringValue()))
                    .matches(doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                    .matches(doc -> Boolean.TRUE.equals(doc.get(EsConstants.Key.IS_TEMPLATE).getBoolValue()));
              }
            }));
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(CfgStatus.ConfigurationStatus.Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
            .extracting(CfgStatus.ConfigurationStatus::getEventDate)
            .isNotNull());
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_SYSLOG_SERVER_PROFILE_TEMPLATE -> OpType.ADD;
      case UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE -> OpType.MOD;
      case DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_SYSLOG_SERVER_PROFILE_TEMPLATE -> ApiFlowNames.ADD_SYSLOG_SERVER_PROFILE_TEMPLATE;
      case UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE -> ApiFlowNames.UPDATE_SYSLOG_SERVER_PROFILE_TEMPLATE;
      case DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE -> ApiFlowNames.DELETE_SYSLOG_SERVER_PROFILE_TEMPLATE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
