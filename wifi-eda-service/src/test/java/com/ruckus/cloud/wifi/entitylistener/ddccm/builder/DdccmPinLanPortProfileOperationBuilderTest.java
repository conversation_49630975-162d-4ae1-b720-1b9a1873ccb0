package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeInteger;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.google.protobuf.Int32Value;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.Builder;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.CcmUserSidePort;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.PinLanPortProfile;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.LanPortAdoptionRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Tag("VxLanTunnelFeatureTest")
@WifiUnitTest
@FeatureFlag(enable = {ACX_UI_ETHERNET_TOGGLE})
class DdccmPinLanPortProfileOperationBuilderTest {

  @SpyBean
  private DdccmPinLanPortProfileOperationBuilder builder;

  @MockBean private LanPortAdoptionRepository lanPortAdoptionRepository;

  @Test
  void testEntityClass() {
    assertThat(builder.entityClass()).isEqualTo(PinLanPortProfile.class);
  }

  @Nested
  class ConfigPinLanPortProfileTest {

    @Nested
    class GivenAddEntityAction extends AbstractBaseTest {
      @Override
      protected EntityAction givenAction() {
        return EntityAction.ADD;
      }
    }

    @Nested
    class GivenModifyEntityAction extends AbstractBaseTest {
      @Override
      protected EntityAction givenAction() {
        return EntityAction.MODIFY;
      }
    }

    @Nested
    class GivenDeleteEntityAction extends AbstractBaseTest {
      @Override
      protected EntityAction givenAction() {
        return EntityAction.DELETE;
      }
    }

    private abstract class AbstractBaseTest {

      protected abstract EntityAction givenAction();

      @Test
      void givenPinLanPortProfileSunnyDay() {
        whenConfigThenAssertResult(givenAction(),
            generatePinLanPortProfile(rangeInteger(8193).setRandom(true).generate()));
      }

      @Test
      void givenPinLanPortProfileWithNullVni() {
        whenConfigThenAssertResult(givenAction(), generatePinLanPortProfile(null));
      }

      @Test
      void givenPinLanPortProfileWithZeroVni() {
        whenConfigThenAssertResult(givenAction(), generatePinLanPortProfile(0));
      }

      @Test
      void givenPinLanPortProfileWithNegativeValueVni() {
        whenConfigThenAssertResult(givenAction(), generatePinLanPortProfile(-1234));
      }
    }

    private void whenConfigThenAssertResult(EntityAction action, PinLanPortProfile given) {
      final Builder builderResult = com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.newBuilder();
      builder.configPinLanPortProfile(builderResult, given, action);
      assertResult(given, builderResult);
    }

    private static PinLanPortProfile generatePinLanPortProfile(Integer givenVni) {
      return Generators.pinLanPortProfile()
          .setTenant(Generators.tenant())
          .setVni(always(givenVni)).generate();
    }

    private static void assertResult(PinLanPortProfile given, Builder builderResult) {
      final var result = builderResult.build();
      if (given.getVni() == null) {
        assertThat(result.getVni()).isZero();
        assertThat(result.hasUserSidePort()).isFalse();
        assertThat(result.getUserSidePort()).isEqualTo(CcmUserSidePort.getDefaultInstance());
      } else {
        assertThat(result.getVni()).isEqualTo(given.getVni());
        assertThat(result.hasUserSidePort()).isTrue();
        assertThat(result.getUserSidePort()).isNotNull()
            .satisfies(ccmUserSidePort -> {
              if (given.getVni() > 0) {
                assertThat(ccmUserSidePort.getUserSidePortEnabled()).isTrue();
                assertThat(ccmUserSidePort.getUserSidePortMaxClient()).isEqualTo((Int32Value.of(32)));
              } else {
                assertThat(ccmUserSidePort.getUserSidePortEnabled()).isFalse();
                assertThat(ccmUserSidePort.getUserSidePortMaxClient()).isEqualTo((Int32Value.of(0)));
              }
            });
      }
    }
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Nested
  class EnableSoftGreOverEthernetPort {

    @Test
    void testWithLanPortAdoption() {
      testPinLanPortProfileWithLanPortAdoption();
    }

    @Test
    void testWithoutLanPortAdoption() {
      testPinLanPortProfileWithoutLanPortAdoption();
    }
  }

  @FeatureFlag(
      enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE})
  @Nested
  class EnableClientIsolationOverEthernetPort {

    @Test
    void testWithLanPortAdoption() {
      testPinLanPortProfileWithLanPortAdoption();
    }

    @Test
    void testWithoutLanPortAdoption() {
      testPinLanPortProfileWithoutLanPortAdoption();
    }
  }

  private void testPinLanPortProfileWithLanPortAdoption() {
    var profile =
        Generators.pinLanPortProfile().setTenant(Generators.tenant()).setVni(always(0)).generate();

    doReturn(1L).when(lanPortAdoptionRepository).countByTenantId(anyString());
    var operations = builder.build(new NewTxEntity<>(profile), emptyTxChanges());

    assertTrue(operations.isEmpty());
    verify(builder, times(1)).skip(any());
    verify(builder, never()).configPinLanPortProfile(any(), any(), any());
  }

  private void testPinLanPortProfileWithoutLanPortAdoption() {
    var profile =
        Generators.pinLanPortProfile().setTenant(Generators.tenant()).setVni(always(0)).generate();

    doReturn(0L).when(lanPortAdoptionRepository).countByTenantId(anyString());
    var operations = builder.build(new NewTxEntity<>(profile), emptyTxChanges());

    assertFalse(operations.isEmpty());
    verify(builder, times(1)).skip(any());
    verify(builder, times(1)).configPinLanPortProfile(any(), any(), any());
  }
}
