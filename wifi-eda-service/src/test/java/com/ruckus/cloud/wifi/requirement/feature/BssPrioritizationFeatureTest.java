package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BSSPriorityEnum;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class BssPrioritizationFeatureTest {

  @SpyBean
  private BssPrioritizationFeature unit;

  @Nested
  class WhenBssPriorityCanBeEnabled {

    @Test
    @FeatureFlag(disable = WIFI_R370_TOGGLE)
    void givenR370Disable(Network network) {
      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = WIFI_R370_TOGGLE)
    void givenBssPriorityNotSetByNetwork() {
      final var network = new Network();
      network.setWlan(new Wlan());
      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Test
    @FeatureFlag(enable = WIFI_R370_TOGGLE)
    void givenBssPrioritySetByNetwork(Network network) {
      network.getWlan().getAdvancedCustomization().setBssPriority(BSSPriorityEnum.HIGH);

      BDDAssertions.then(unit.test(network)).isTrue();
    }
  }
}
