package com.ruckus.cloud.wifi.requirement;

import static org.apache.commons.lang3.RandomStringUtils.randomAlphanumeric;
import static org.apache.commons.lang3.RandomUtils.nextBoolean;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.never;

import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModel;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

@WifiUnitTest
class ApFeatureRequirementTest {
  @Mock(answer = Answers.CALLS_REAL_METHODS)
  private ApFeatureRequirement unit;

  @Nested
  class WhenIsDeviceSupport {
    private final String model = randomAlphanumeric(5);

    @Test
    void givenModelNotSupport() {
      willReturn(false).given(unit).checkModelFamily(anyString());

      BDDAssertions.then(unit.isDeviceSupport(ApFirmwareModel.builder().model(model).build()))
          .isFalse();

      then(unit).should().checkModelFamily(eq(model));
      then(unit).should(never()).checkFirmwareVersion(any());
    }

    @Nested
    class GivenModelSupported {
      private final String firmware = randomAlphanumeric(10);

      @BeforeEach
      void beforeEach() {
        willReturn(true).given(unit).checkModelFamily(anyString());
      }

      @AfterEach
      void afterEach() {
        then(unit).should().checkModelFamily(eq(model));
      }

      @Test
      void givenFirmwareNotSupported() {
        willReturn(false).given(unit).checkFirmwareVersion(anyString());

        BDDAssertions.then(
                unit.isDeviceSupport(
                    ApFirmwareModel.builder().model(model).firmware(firmware).build()))
            .isFalse();

        then(unit).should().checkFirmwareVersion(eq(firmware));
      }

      @Test
      void givenFirmwareSupported() {
        willReturn(true).given(unit).checkFirmwareVersion(anyString());

        BDDAssertions.then(
                unit.isDeviceSupport(
                    ApFirmwareModel.builder().model(model).firmware(firmware).build()))
            .isTrue();

        then(unit).should().checkFirmwareVersion(eq(firmware));
      }
    }
  }

  @Nested
  class WhenCheckModelFamily {
    private final String model = randomAlphanumeric(10);
    @Mock private ApModelFamily apModelFamily;

    @Test
    void givenRequiredModelFamilyNotPresent() {
      BDDAssertions.then(unit.checkModelFamily(model)).isTrue();
    }

    @SuppressWarnings("ResultOfMethodCallIgnored")
    @Test
    void givenRequiredModelFamilyPresent() {
      final var result = nextBoolean();

      ReflectionTestUtils.setField(unit, "supportedModelFamilies", new ApModelFamily[]{apModelFamily});
      willReturn(result).given(apModelFamily).isModelSupported(anyString());

      BDDAssertions.then(unit.checkModelFamily(model)).isEqualTo(result);

      then(apModelFamily).should().isModelSupported(eq(model));
    }
  }

  @Nested
  class WhenCheckFirmwareVersion {
    @Test
    void givenRequiredFirmwareNotPresent() {
      BDDAssertions.then(unit.checkFirmwareVersion(randomAlphanumeric(20))).isTrue();
    }

    @Nested
    class GivenRequiredFirmwarePresent {
      @BeforeEach
      void beforeEach() {
        ReflectionTestUtils.setField(unit, "requiredFw", "6.2.2.103.23");
      }

      @Test
      void givenFirmwareSameAsRequired() {
        BDDAssertions.then(unit.checkFirmwareVersion("6.2.2.103.23")).isTrue();
      }

      @Test
      void givenFirmwareSmallerThanRequired() {
        BDDAssertions.then(unit.checkFirmwareVersion("6.2.1.103.23")).isFalse();
      }

      @Test
      void givenFirmwareGreaterThanRequired() {
        BDDAssertions.then(unit.checkFirmwareVersion("7.0.0.100.1")).isTrue();
      }
    }
  }
}
