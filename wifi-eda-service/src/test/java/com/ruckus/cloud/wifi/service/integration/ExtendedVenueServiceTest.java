package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.service.impl.ExtendedVenueServiceCtrlImpl.AP_MODEL_R760;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomNumber;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.google.common.collect.Lists;
import com.ruckus.cloud.wifi.capabilities.CloudSupportApModels;
import com.ruckus.cloud.wifi.client.kairos.KairosApiClient;
import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.client.venue.VenueClient;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV2Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpV3Agent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.BonjourFencing;
import com.ruckus.cloud.wifi.eda.servicemodel.BonjourFencingWiredRule;
import com.ruckus.cloud.wifi.eda.servicemodel.BonjourFencingWirelessRule;
import com.ruckus.cloud.wifi.eda.servicemodel.BssColoring;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.DenialOfServiceProtection;
import com.ruckus.cloud.wifi.eda.servicemodel.DeviceGps;
import com.ruckus.cloud.wifi.eda.servicemodel.ExternalAntenna;
import com.ruckus.cloud.wifi.eda.servicemodel.IpMode;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.LteBandLockChannel;
import com.ruckus.cloud.wifi.eda.servicemodel.Mesh;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.SimSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.TlsKeyEnhancedMode;
import com.ruckus.cloud.wifi.eda.servicemodel.TripleBand;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApFirmwareBatchOperation;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApIotSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApManagementTrafficVlanSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApRebootTimeout;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApSmartMonitor;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueBonjourFencing;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCellularSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueClientAdmissionControl;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueDhcpServiceSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueDirectedMulticast;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLoadBalancing;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadiusOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRegulatoryChannels;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSnmpAgent;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BandModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CalledStationIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CellularNetworkSelectionEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.FencingRangeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.IpModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LoadBalancingMethodEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LteBandRegionEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MeshRadioTypeEnumV1_1;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdDelimiterEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RogueClassificationEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RogueRuleTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ScanMethodEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SteeringModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WanConnectionEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.publisher.WifiSchedulePublisher;
import com.ruckus.cloud.wifi.mapper.ClientIsolationLanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.mapper.DhcpOption82LanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.mapper.SoftGreProfileLanPortActivationMapperImpl;
import com.ruckus.cloud.wifi.notification.VenueApCountExecutorFactory;
import com.ruckus.cloud.wifi.notification.VenueTemplateConverter;
import com.ruckus.cloud.wifi.notification.VenueTemplateConverterImpl;
import com.ruckus.cloud.wifi.notification.VenueTemplateDtoBuilderHelper;
import com.ruckus.cloud.wifi.notification.kafka.TenantFirmwareNotificationSender;
import com.ruckus.cloud.wifi.notification.route.TenantFirmwareNotificationRouter;
import com.ruckus.cloud.wifi.repository.ApGroupRepository;
import com.ruckus.cloud.wifi.repository.ApModelGreenfieldFirmwareRepository;
import com.ruckus.cloud.wifi.repository.ApModelMinimumFirmwareRepository;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.repository.DhcpPoolVenueRepository;
import com.ruckus.cloud.wifi.repository.ScheduleTimeSlotRepository;
import com.ruckus.cloud.wifi.repository.TenantAvailableApFirmwareRepository;
import com.ruckus.cloud.wifi.repository.TenantCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.repository.TenantFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.TenantRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.repository.VenueApFirmwareBatchOperationRepository;
import com.ruckus.cloud.wifi.repository.VenueApModelSpecificAttributesRepository;
import com.ruckus.cloud.wifi.repository.VenueCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.repository.VenueFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.AggregateNotificationService;
import com.ruckus.cloud.wifi.service.ApUpgradeService;
import com.ruckus.cloud.wifi.service.ApVersionService;
import com.ruckus.cloud.wifi.service.DhcpCapabilitiesHelper;
import com.ruckus.cloud.wifi.service.ExtendedApServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedDhcpConfigServiceProfileServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedRogueApPolicyProfileServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedTenantServiceCtrl;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.FirmwareManagementService;
import com.ruckus.cloud.wifi.service.ScheduleTimeSlotService;
import com.ruckus.cloud.wifi.service.ScopeDataService;
import com.ruckus.cloud.wifi.service.TenantAvailableApFirmwareService;
import com.ruckus.cloud.wifi.service.TenantCurrentFirmwareService;
import com.ruckus.cloud.wifi.service.TenantFirmwareVersionService;
import com.ruckus.cloud.wifi.service.TenantService;
import com.ruckus.cloud.wifi.service.UpgradeScheduleService;
import com.ruckus.cloud.wifi.service.VenueApModelSpecificAttributesService;
import com.ruckus.cloud.wifi.service.VenueFirmwareVersionService;
import com.ruckus.cloud.wifi.service.VenueRadioService;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.exception.OperationNotAllowedException;
import com.ruckus.cloud.wifi.service.impl.ApBranchFamilyServiceRouter;
import com.ruckus.cloud.wifi.service.impl.ClientIsolationLanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.DhcpOption82LanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.FirmwareManagementServiceImpl;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.service.impl.ScheduleTimeSlotServiceImpl;
import com.ruckus.cloud.wifi.service.impl.SoftGreProfileLanPortActivationHandler;
import com.ruckus.cloud.wifi.service.impl.TenantAvailableApFirmwareServiceImpl;
import com.ruckus.cloud.wifi.service.impl.TenantCurrentFirmwareServiceImpl;
import com.ruckus.cloud.wifi.service.impl.TenantFirmwareVersionServiceImpl;
import com.ruckus.cloud.wifi.service.impl.TenantServiceImpl;
import com.ruckus.cloud.wifi.service.impl.UpgradeScheduleServiceImpl;
import com.ruckus.cloud.wifi.service.impl.VenueFirmwareVersionServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedApGroupServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedNetworkVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.FirmwareCapabilityServiceTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.InitVenueServiceImplTestConfig;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.servicemodel.projection.ApModelVersionProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.config.MockGrpcBeanConfiguration;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.param.SkipSave;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleFirmwareVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.utils.RksTransactionHelper;
import com.ruckus.cloud.wifi.viewmodel.ApModelFirmware;
import com.ruckus.cloud.wifi.viewmodel.BatchOperationType;
import java.sql.Date;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockReset;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpStatus;

@WifiJpaDataTest
class ExtendedVenueServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired private LanPortAdoptionDataHelper dataHelper;

  @Autowired
  private ExtendedVenueServiceCtrl extendedVenueServiceCtrl;

  @Autowired
  private VenueApModelSpecificAttributesService venueApModelSpecificAttributesService;
  @Autowired
  private ExtendedRogueApPolicyProfileServiceCtrl rogueApPolicyProfileServiceCtrl;

  @SpyBean
  private VenueRepository venueRepository;

  @Autowired
  private ApGroupRepository apGroupRepository;

  @MockBean
  private ExtendedTenantServiceCtrl tenantService;

  @MockBean
  private VenueRadioService venueRadioService;

  @Autowired
  private TenantFirmwareVersionRepository tenantFirmwareVersionRepository;

  @Autowired
  private VenueFirmwareVersionRepository venueFirmwareVersionRepository;

  @Autowired
  private VenueTemplateConverter venueTemplateConverter;

  @Autowired
  private ApVersionRepository apVersionRepository;

  @SpyBean
  private UpgradeScheduleService scheduleService;

  @Autowired
  private UpgradeScheduleRepository scheduleRepository;

  @Autowired
  private ScheduleTimeSlotRepository timeSlotRepository;

  @Autowired
  private DhcpPoolVenueRepository dhcpPoolVenueRepository;

  @Autowired
  private UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private UpgradeScheduleRepository upgradeScheduleRepository;

  @MockBean(reset = MockReset.BEFORE)
  private KairosApiClient kairosApiClient;

  @MockBean
  private DhcpCapabilitiesHelper dhcpCapabilitiesHelper;

  @MockBean
  private ExtendedDhcpConfigServiceProfileServiceCtrl dhcpConfigServiceProfileService;

  @Autowired
  private ExtendedApServiceCtrl apServiceCtrl;

  @MockBean
  private ApVersionService apVersionService;

  @MockBean
  private AggregateNotificationService aggregateNotificationService;

  @MockBean
  private TenantFirmwareNotificationRouter tenantFirmwareNotificationRouter;

  @Autowired
  private FirmwareManagementService firmwareManagementService;

  @MockBean
  private VenueCurrentFirmwareRepository venueCurrentFirmwareRepository;

  @MockBean
  private TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository;

  @Autowired
  private ScheduleTimeSlotService scheduleTimeSlotService;

  @Autowired
  private VenueApCountExecutorFactory venueApCountExecutorFactory;

  @MockBean
  TenantService tenantServiceMock;
  @Autowired
  private VenueApModelSpecificAttributesRepository venueApModelSpecificAttributesRepository;

  @Autowired
  private VenueApFirmwareBatchOperationRepository venueApFirmwareBatchOperationRepository;

  @MockBean
  private TenantFirmwareNotificationSender tenantFirmwareNotificationSender;

  @MockBean
  private ApUpgradeService apUpgradeService;

  @MockBean
  private ScopeDataService scopeDataService;

  @MockBean
  private TenantClient tenantClient;

  @Tag("ApGroupTest")
  @Test
  void testAddVenue(Tenant tenant) throws Exception {
    ApVersion eolApVersion = ApVersionTestFixture.recommendedApVersion("*********.513", a -> {
    });
    ApVersion activeApVersion = ApVersionTestFixture.recommendedApVersion("6.2.1.103.1580", a -> {
    });
    apVersionRepository.saveAll(List.of(eolApVersion, activeApVersion));

    final String venueId = randomId();
    TenantFirmwareVersion tfv = new TenantFirmwareVersion();
    tfv.setBranchType("eol-ap-2022-12");
    tfv.setTenant(tenant);
    tfv.setLatestFirmwareVersion(eolApVersion);
    tenantFirmwareVersionRepository.save(tfv);
    tenant.setLatestReleaseVersion(activeApVersion);
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());
    doReturn(tenant).when(tenantService).getTenant();

    extendedVenueServiceCtrl.addVenue(new Venue(venueId));
    venueRepository.findByIdAndTenantId(venueId, tenant.getId());

    assertTrue(
        venueRepository.findByIdAndTenantId(venueId, tenant.getId()).isPresent());

    List<ApGroup> apGroups = apGroupRepository.findByTenantId(tenant.getId());
    assertEquals(1, apGroups.size());
    assertTrue(apGroups.get(0).getIsDefault());
    List<VenueFirmwareVersion> vfvList = venueFirmwareVersionRepository.findByVenueId(venueId);
    assertEquals(eolApVersion.getId(), vfvList.get(0).getCurrentFirmwareVersion().getId());
  }

  @Test
  @FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
  void testAddVenueForUpgradeByApModelFfOn(Tenant tenant) throws Exception {
    // Given
    String eolVersion = "6.2.3.103.500";
    String activeVersion = "*********.100";
    String biggestVersion = "*********.888";
    prepareTenantAndFirmwares(tenant, eolVersion, activeVersion, biggestVersion);

    // When
    final String venueId = randomId();
    extendedVenueServiceCtrl.addVenue(new Venue(venueId));

    // Then
    var venue = venueRepository.findByIdAndTenantId(venueId, tenant.getId()).get();
    assertEquals(activeVersion, venue.getWifiFirmwareVersion().getId());
    List<VenueFirmwareVersion> vfvList = venueFirmwareVersionRepository.findByVenueId(venueId);
    assertEquals(0, vfvList.size());
  }

  @Test
  @FeatureFlag(disable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL})
  void testAddVenueForUpgradeByApModelFfOff(Tenant tenant) throws Exception {
    // Given
    String eolVersion = "6.2.3.103.600";
    String activeVersion = "*********.200";
    String biggestVersion = "*********.888";
    prepareTenantAndFirmwares(tenant, eolVersion, activeVersion, biggestVersion);

    // When
    final String venueId = randomId();
    extendedVenueServiceCtrl.addVenue(new Venue(venueId));

    // Then
    var venue = venueRepository.findByIdAndTenantId(venueId, tenant.getId()).get();
    assertEquals(biggestVersion, venue.getWifiFirmwareVersion().getId());
    List<VenueFirmwareVersion> vfvList = venueFirmwareVersionRepository.findByVenueId(venueId);
    assertEquals(1, vfvList.size());
    assertEquals(eolVersion, vfvList.get(0).getCurrentFirmwareVersion().getId());
  }

  @Test
  void testAddVenueForMeshRadioEnhance(Tenant tenant) throws Exception {
    // When
    final String venueId = randomId();
    doReturn(tenant).when(tenantService).getTenant();
    // Then
    extendedVenueServiceCtrl.addVenue(new Venue(venueId));
    var venue = venueRepository.findByIdAndTenantId(venueId, tenant.getId());
    assertTrue(venue.isPresent());
    assertEquals(MeshRadioTypeEnumV1_1._5_6_GHz, venue.get().getMesh().getRadioType());
  }

  private void prepareTenantAndFirmwares(Tenant tenant, String eolFwVersion, String activeFwVersion, String biggestFwVersion) throws Exception {
    ApVersion eolFirmware = ApVersionTestFixture.recommendedApVersion(eolFwVersion, a -> {});
    ApVersion activeFirmware = ApVersionTestFixture.recommendedApVersion(activeFwVersion, a -> {});
    ApVersion biggestFirmware = ApVersionTestFixture.recommendedApVersion(biggestFwVersion, a -> {});
    apVersionRepository.saveAll(List.of(eolFirmware, activeFirmware, biggestFirmware));
    tenant.setLatestReleaseVersion(activeFirmware);
    TenantFirmwareVersion tfv = new TenantFirmwareVersion();
    tfv.setBranchType("eol-ap-2022-12");
    tfv.setTenant(tenant);
    tfv.setLatestFirmwareVersion(eolFirmware);
    tenantFirmwareVersionRepository.save(tfv);
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());
    doReturn(tenant).when(tenantService).getTenant();
    doReturn(biggestFirmware).when(apVersionService).getTenantBiggestAvailableReleaseVersionInTenantActiveAbf(any());
  }

  @Test
  void testDeleteVenue(Tenant tenant) throws Exception {
    final Venue venue = venueRepository.save(new Venue(randomId()));

    extendedVenueServiceCtrl.deleteVenue(venue);

    assertFalse(venueRepository.findByIdAndTenantId(venue.getId(), tenant.getId()).isPresent());
  }

  @Test
  void testDeleteVenue_venueIsNotExisted() {
    try {
      extendedVenueServiceCtrl.deleteVenue(new Venue(randomId()));
    } catch (Exception e) {
      fail("Should not throw any exception");
    }
  }

  @Test
  void testUpdateVenue(Tenant tenant) throws Exception {
    final BiFunction<String, String, DeviceGps> deviceGpsFactory = (latitude, longitude) -> {
      var deviceGps = new DeviceGps();
      deviceGps.setLatitude(latitude);
      deviceGps.setLongitude(longitude);
      return deviceGps;
    };

    final Venue venue = new Venue(randomId());
    venue.setName("LA");
    venue.setTimezone("PST");
    venue.setCountryCode("US");
    venue.setAddressLine("No 1, some rd.");
    venue.setDeviceGps(deviceGpsFactory.apply("34.052235", "-118.243683"));

    final Venue savedVenue = venueRepository.save(venue);

    extendedVenueServiceCtrl.updateVenue(savedVenue.getId(), "New York", "ET", "US",
        "No 2, some rd.", deviceGpsFactory.apply("40.712776", "-74.005974"));

    final Venue updatedVenue = venueRepository.getByIdAndTenantId(savedVenue.getId(),
        tenant.getId());
    assertEquals("New York", updatedVenue.getName());
    assertEquals("ET", updatedVenue.getTimezone());
    assertEquals("US", updatedVenue.getCountryCode());
    assertEquals("No 2, some rd.", updatedVenue.getAddressLine());
    assertEquals("40.712776", updatedVenue.getDeviceGps().getLatitude());
    assertEquals("-74.005974", updatedVenue.getDeviceGps().getLongitude());
  }

  @Deprecated
  @Test
  void getAvailableLteBandsTest() throws Exception {
    var result = extendedVenueServiceCtrl.getAvailableLteBands();
    assertFalse(result.isEmpty());
  }

  @Deprecated
  @Test
  void getVenueLanPortsTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueLanPorts(venue.getId());
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Deprecated
  @Test
  void updateVenueLanPortsTestWithNoModification(Venue venue) throws Exception {
    var attributesList = venueApModelSpecificAttributesService
        .getLanPortsFromCapabilities(venue);
    var result = extendedVenueServiceCtrl.updateVenueLanPorts(venue.getId(), attributesList);
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Deprecated
  @Test
  void updateVenueLanPortsTestWithModification(Venue venue) throws Exception {
    var attributesList = venueApModelSpecificAttributesService
        .getLanPortsFromCapabilities(venue);
    var r610 = attributesList.stream()
        .filter(a -> a.getModel().equals("R610"))
        .findAny();
    r610.map(VenueApModelSpecificAttributes::getLanPorts)
        .map(ports -> ports.get(1))
        .map(port -> {
          port.setEnabled(false);
          return port;
        });
    var result = extendedVenueServiceCtrl.updateVenueLanPorts(venue.getId(), attributesList);
    assertNotNull(result);
    assertEquals(1, result.size());
    var r610toVerify = result.get(0);
    assertEquals("R610", r610toVerify.getModel());
    assertEquals(2, r610toVerify.getLanPorts().size());
    assertTrue(r610toVerify.getLanPorts().get(0).getEnabled());
    assertNotNull(r610toVerify.getLanPorts().get(0).getApLanPortProfile());
    assertNotNull(r610toVerify.getLanPorts().get(0).getApLanPortProfile().getApLanPortId());
    assertFalse(r610toVerify.getLanPorts().get(1).getEnabled());
    assertNotNull(r610toVerify.getLanPorts().get(1).getApLanPortProfile());
    assertNotNull(r610toVerify.getLanPorts().get(1).getApLanPortProfile().getApLanPortId());

    attributesList = venueApModelSpecificAttributesService
        .getLanPortsFromCapabilities(venue);
    r610 = attributesList.stream()
        .filter(a -> a.getModel().equals("R610"))
        .findAny();
    r610.map(VenueApModelSpecificAttributes::getLanPorts)
        .map(ports -> ports.get(1))
        .map(port -> {
          port.setEnabled(true);
          return port;
        });
    r610.map(VenueApModelSpecificAttributes::getLanPorts)
        .map(ports -> ports.get(1))
        .map(port -> {
          port.setEnabled(true);
          return port;
        });
    result = extendedVenueServiceCtrl.updateVenueLanPorts(venue.getId(), attributesList);
    assertNotNull(result);
    r610toVerify = result.get(0);
    assertEquals("R610", r610toVerify.getModel());
    assertEquals(2, r610toVerify.getLanPorts().size());
    assertTrue(r610toVerify.getLanPorts().get(0).getEnabled());
    assertNotNull(r610toVerify.getLanPorts().get(0).getApLanPortProfile());
    assertNotNull(r610toVerify.getLanPorts().get(0).getApLanPortProfile().getApLanPortId());
    assertTrue(r610toVerify.getLanPorts().get(1).getEnabled());
    assertNotNull(r610toVerify.getLanPorts().get(1).getApLanPortProfile());
    assertNotNull(r610toVerify.getLanPorts().get(1).getApLanPortProfile().getApLanPortId());
  }

  @Deprecated
  @Test
  void getVenueLedOnTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueLedOn(venue.getId());
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Deprecated
  @Test
  void updateVenueLedOnTestWithNewVenue(Tenant tenant, Venue venue) throws Exception {
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());

    var result = extendedVenueServiceCtrl.updateVenueLedOn(venue.getId(), List.of(
        createLedOnRequest("R610", Boolean.TRUE),
        createLedOnRequest("R550", Boolean.FALSE))
    );
    // CREATE VERIFY
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("R550", result.get(0).getModel());
    assertEquals(Boolean.FALSE, result.get(0).getLedOn());
    assertEquals("R610", result.get(1).getModel());
    assertEquals(Boolean.TRUE, result.get(1).getLedOn());
  }

  @Deprecated
  @Test
  void updateVenueLedOnTestWithExistedVenue(Venue venue) throws Exception {
    // CREATE
    var result = extendedVenueServiceCtrl.updateVenueLedOn(venue.getId(), List.of(
        createLedOnRequest("R610", Boolean.TRUE),
        createLedOnRequest("R550", Boolean.FALSE))
    );
    // CREATE VERIFY
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("R550", result.get(0).getModel());
    assertEquals(Boolean.FALSE, result.get(0).getLedOn());
    assertEquals("R610", result.get(1).getModel());
    assertEquals(Boolean.TRUE, result.get(1).getLedOn());

    // UPDATE
    result = extendedVenueServiceCtrl.updateVenueLedOn(venue.getId(), List.of(
        createLedOnRequest("M510", Boolean.TRUE),
        createLedOnRequest("R610", Boolean.FALSE)
    ));
    // UPDATE VERIFY
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals("M510", result.get(0).getModel());
    assertEquals(Boolean.TRUE, result.get(0).getLedOn());
    assertEquals("R550", result.get(1).getModel());
    assertNull(result.get(1).getLedOn());
    assertEquals("R610", result.get(2).getModel());
    assertEquals(Boolean.FALSE, result.get(2).getLedOn());
  }

  @Deprecated
  @Test
  void updateVenueCellularSettingsTestWithNewVenue(Tenant tenant, Venue venue) throws Exception {
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());

    var request = createCellularSettings();
    var result = extendedVenueServiceCtrl.updateVenueApModelCellular(venue.getId(), request);
    // CREATE VERIFY
    assertNotNull(result.getVenueApModelSpecificAttributes());
    assertNotNull(result.getVenueApModelSpecificAttributes().getVenue());
    assertEquals(result, result.getVenueApModelSpecificAttributes().getCellularSettings());
    assertEquals(1,
        result.getVenueApModelSpecificAttributes().getVenue().getModelSpecificAttributes().size());
    assertEquals(4, result.getLteBandLockChannels().size());
  }

  @Deprecated
  @Test
  void updateVenueCellularSettingsTestWithExistedVenue(Venue venue) throws Exception {
    // CREATE
    var request = createCellularSettings();
    var result = extendedVenueServiceCtrl.updateVenueApModelCellular(venue.getId(), request);
    // CREATE VERIFY
    assertNotNull(result.getVenueApModelSpecificAttributes());
    assertNotNull(result.getVenueApModelSpecificAttributes().getVenue());
    assertEquals(result, result.getVenueApModelSpecificAttributes().getCellularSettings());
    assertEquals(1,
        result.getVenueApModelSpecificAttributes().getVenue().getModelSpecificAttributes().size());
    assertEquals(4, result.getLteBandLockChannels().size());

    // UPDATE
    var updateRequest = createCellularSettings();
    var sim2 = updateRequest.getSecondarySim();
    updateRequest.setSecondarySim(updateRequest.getPrimarySim());
    updateRequest.setPrimarySim(sim2);
    updateRequest.getLteBandLockChannels().get(0).setRegion(LteBandRegionEnum.DOMAIN_1);
    updateRequest.getLteBandLockChannels().get(1)
        .setBand3G(new ArrayList<>(List.of("B1", "A1", "B2")));
    updateRequest.getLteBandLockChannels().get(1)
        .setBand4G(new ArrayList<>(List.of("B1", "B2", "A1", "B4", "B5")));
    var updateResult = extendedVenueServiceCtrl.updateVenueApModelCellular(
        venue.getId(), updateRequest);
    // UPDATE VERIFY
    assertNotNull(updateResult.getVenueApModelSpecificAttributes());
    assertNotNull(updateResult.getVenueApModelSpecificAttributes().getVenue());
    assertEquals(updateResult,
        updateResult.getVenueApModelSpecificAttributes().getCellularSettings());
    assertEquals(1,
        updateResult.getVenueApModelSpecificAttributes().getVenue().getModelSpecificAttributes()
            .size());
    assertEquals(4, updateResult.getLteBandLockChannels().size());
    var resultSim1Band = updateResult.getLteBandLockChannels().stream()
        .filter(b -> Integer.valueOf(0).equals(b.getSimCardId()))
        .findAny().get();
    var resultSim2Band = updateResult.getLteBandLockChannels().stream()
        .filter(b -> Integer.valueOf(1).equals(b.getSimCardId()))
        .findAny().get();
    assertEquals(LteBandRegionEnum.DOMAIN_1, resultSim1Band.getRegion());
    assertEquals(List.of("B1", "A1", "B2"), resultSim2Band.getBand3G());
    assertEquals(List.of("B1", "B2", "A1", "B4", "B5"), resultSim2Band.getBand4G());
  }

  @Deprecated
  @Test
  void getVenueExternalAntennaTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueExternalAntenna(venue.getId());
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Deprecated
  @Test
  void updateVenueExternalAntennaTestWithInvalidModel(Tenant tenant) throws Exception {
    final String venueId = randomId();
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());
    doReturn(tenant).when(tenantService).getTenant();
    extendedVenueServiceCtrl.addVenue(new Venue(venueId));

    assertThrows(InvalidPropertyValueException.class,
        () -> extendedVenueServiceCtrl.updateVenueExternalAntenna(venueId,
            List.of(createExternalAntenna("R610"))));
  }

  @Deprecated
  @Test
  void updateVenueExternalAntennaTestWithValidModel(Tenant tenant, Venue venue) throws Exception {
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());

    var result = extendedVenueServiceCtrl.updateVenueExternalAntenna(venue.getId(),
        List.of(createExternalAntenna("E510")));
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Deprecated
  @Test
  void getVenueLoadBalancingTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueLoadBalancing(venue.getId());
    assertEquals(result.getEnabled(), false);
    assertEquals(result.getLoadBalancingMethod(), LoadBalancingMethodEnum.BASED_ON_CLIENT_COUNT);
    assertEquals(result.getSteeringMode(), SteeringModeEnum.BASIC);
    assertEquals(result.getBandBalancingEnabled(), true);
    assertEquals(result.getBandBalancingClientPercent24G(), (short) 25);
    assertEquals(result.getStickyClientSteeringEnabled(), false);
    assertEquals(result.getStickyClientSnrThreshold(), (short) 15);
    assertEquals(result.getStickyClientNbrApPercentageThreshold(), (short) 20);
  }

  @Deprecated
  @Test
  void updateVenueLoadBalancingBasedOnClientCountTestWithModification(Venue venue)
      throws Exception {
    VenueLoadBalancing venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(false);
    venueLoadBalancing.setLoadBalancingMethod(LoadBalancingMethodEnum.BASED_ON_CLIENT_COUNT);
    venueLoadBalancing.setSteeringMode(SteeringModeEnum.STRICT);
    venueLoadBalancing.setBandBalancingEnabled(false);
    venueLoadBalancing.setBandBalancingClientPercent24G((short) 55);
    venueLoadBalancing.setStickyClientSteeringEnabled(true);
    venueLoadBalancing.setStickyClientSnrThreshold((short) 16);
    venueLoadBalancing.setStickyClientNbrApPercentageThreshold((short) 21);
    VenueLoadBalancing result;

    extendedVenueServiceCtrl.updateVenueLoadBalancing(venue.getId(), venueLoadBalancing);

    result = extendedVenueServiceCtrl.getVenueLoadBalancing(venue.getId());

    assertEquals(result.getEnabled(), false);
    assertEquals(result.getLoadBalancingMethod(), LoadBalancingMethodEnum.BASED_ON_CLIENT_COUNT);
    assertEquals(result.getSteeringMode(), SteeringModeEnum.STRICT);
    assertEquals(result.getBandBalancingEnabled(), false);
    assertEquals(result.getBandBalancingClientPercent24G(), (short) 55);
    assertEquals(result.getStickyClientSteeringEnabled(), true);
    assertEquals(result.getStickyClientSnrThreshold(), (short) 16);
    assertEquals(result.getStickyClientNbrApPercentageThreshold(), (short) 21);
  }

  @Deprecated
  @Test
  void updateVenueLoadBalancingBasedOnCapacityTestWithModification(Venue venue) throws Exception {
    VenueLoadBalancing venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(false);
    venueLoadBalancing.setLoadBalancingMethod(LoadBalancingMethodEnum.BASED_ON_CAPACITY);
    venueLoadBalancing.setSteeringMode(SteeringModeEnum.STRICT);
    venueLoadBalancing.setBandBalancingEnabled(true);
    venueLoadBalancing.setBandBalancingClientPercent24G((short) 55);
    extendedVenueServiceCtrl.updateVenueLoadBalancing(venue.getId(), venueLoadBalancing);
    var result = extendedVenueServiceCtrl.getVenueLoadBalancing(venue.getId());
    assertEquals(result.getEnabled(), false);
    assertEquals(result.getLoadBalancingMethod(), LoadBalancingMethodEnum.BASED_ON_CAPACITY);
    assertEquals(result.getSteeringMode(), SteeringModeEnum.STRICT);
    assertEquals(result.getBandBalancingEnabled(), false);
    assertEquals(result.getBandBalancingClientPercent24G(), (short) 25);
  }

  @Test
  void testGetDefaultRadioCustomization(Venue venue) throws Exception {
    VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
    doReturn(venueRadioCustomization).when(venueRadioService)
        .getDefaultRadioCustomization(any(Venue.class));
    VenueRadioCustomization result = extendedVenueServiceCtrl.getDefaultRadioCustomization(
        venue.getId());

    assertThat(result).isEqualTo(venueRadioCustomization);
  }

  @Deprecated
  @Test
  void testGetVenueRadioCustomization_defaultOnly(Venue venue) throws Exception {
    VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
    doReturn(venueRadioCustomization).when(venueRadioService)
        .getDefaultRadioCustomization(any(Venue.class));
    VenueRadioCustomization result = extendedVenueServiceCtrl.getVenueRadioCustomization(
        venue.getId(),
        Optional.of(true));

    assertThat(result).isEqualTo(venueRadioCustomization);
  }

  @Deprecated
  @Test
  void testGetVenueRadioCustomization(Venue venue) throws Exception {
    VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
    doReturn(venueRadioCustomization).when(venueRadioService)
        .getVenueRadioCustomization(any(Venue.class));
    VenueRadioCustomization result = extendedVenueServiceCtrl.getVenueRadioCustomization(
        venue.getId(),
        Optional.of(false));

    assertThat(result).isEqualTo(venueRadioCustomization);
  }

  @Deprecated
  @Test
  void getVenueDirectedMulticastTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueDirectedMulticast(venue.getId());
    assertEquals(result.getWirelessEnabled(), true);
    assertEquals(result.getWiredEnabled(), true);
    assertEquals(result.getNetworkEnabled(), true);
  }

  @Deprecated
  @Test
  void updateVenueDirectedMulticastTest(Venue venue) throws Exception {
    VenueDirectedMulticast venueDirectedMulticast = new VenueDirectedMulticast();
    venueDirectedMulticast.setWirelessEnabled(true);
    venueDirectedMulticast.setWiredEnabled(true);
    venueDirectedMulticast.setNetworkEnabled(false);
    extendedVenueServiceCtrl.updateVenueDirectedMulticast(venue.getId(), venueDirectedMulticast);
    var result = extendedVenueServiceCtrl.getVenueDirectedMulticast(venue.getId());
    assertEquals(result.getWirelessEnabled(), true);
    assertEquals(result.getWiredEnabled(), true);
    assertEquals(result.getNetworkEnabled(), false);
  }

  @Test
  void getVenueSnmpAgentTest(Venue venue) throws Exception {
    var venueSnmpAgent = new VenueSnmpAgent();
    var result = extendedVenueServiceCtrl.getVenueApSnmpAgent(venue.getId());
    assertEquals(result.getEnableApSnmp(), venueSnmpAgent.getEnableApSnmp());
    assertEquals(result.getApSnmpAgentProfile(), venueSnmpAgent.getApSnmpAgentProfile());
  }

  @Test
  void updateVenueSnmpAgentTest(Venue venue, ApSnmpAgentProfile apSnmpAgentProfile)
      throws Exception {
    var venueSnmpAgent = new VenueSnmpAgent();
    venueSnmpAgent.setEnableApSnmp(true);
    venueSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);

    extendedVenueServiceCtrl.updateVenueApSnmpAgent(venue.getId(), venueSnmpAgent);
    var result = extendedVenueServiceCtrl.getVenueApSnmpAgent(venue.getId());
    var apSnmpAgentProfileResult = result.getApSnmpAgentProfile();

    assertEquals(result.getEnableApSnmp(), venueSnmpAgent.getEnableApSnmp());
    assertEquals(apSnmpAgentProfileResult.getId(), apSnmpAgentProfile.getId());
    assertEquals(apSnmpAgentProfileResult.getPolicyName(), apSnmpAgentProfile.getPolicyName());
    assertEquals(apSnmpAgentProfileResult.getSnmpV2Agents().size(),
        apSnmpAgentProfile.getSnmpV2Agents().size());
    assertEquals(apSnmpAgentProfileResult.getSnmpV3Agents().size(),
        apSnmpAgentProfile.getSnmpV3Agents().size());
    assertSnmpV2Agents(apSnmpAgentProfileResult.getSnmpV2Agents().get(0),
        apSnmpAgentProfile.getSnmpV2Agents().get(0));
    assertSnmpV3Agents(apSnmpAgentProfileResult.getSnmpV3Agents().get(0),
        apSnmpAgentProfile.getSnmpV3Agents().get(0));
  }

  private void assertSnmpV2Agents(ApSnmpV2Agent apSnmpV2Agent, ApSnmpV2Agent oriApSnmpV2Agent) {
    assertEquals(apSnmpV2Agent.getCommunityName(), oriApSnmpV2Agent.getCommunityName());
    assertEquals(apSnmpV2Agent.getReadPrivilege(), oriApSnmpV2Agent.getReadPrivilege());
    assertEquals(apSnmpV2Agent.getTrapPrivilege(), oriApSnmpV2Agent.getTrapPrivilege());
    assertEquals(apSnmpV2Agent.getNotificationType(), oriApSnmpV2Agent.getNotificationType());
    assertEquals(apSnmpV2Agent.getTargetAddr(), oriApSnmpV2Agent.getTargetAddr());
    assertEquals(apSnmpV2Agent.getTargetPort(), oriApSnmpV2Agent.getTargetPort());
  }

  private void assertSnmpV3Agents(ApSnmpV3Agent apSnmpV3Agent, ApSnmpV3Agent oriApSnmpV3Agent) {
    assertEquals(apSnmpV3Agent.getUserName(), oriApSnmpV3Agent.getUserName());
    assertEquals(apSnmpV3Agent.getReadPrivilege(), oriApSnmpV3Agent.getReadPrivilege());
    assertEquals(apSnmpV3Agent.getTrapPrivilege(), oriApSnmpV3Agent.getTrapPrivilege());
    assertEquals(apSnmpV3Agent.getAuthProtocol(), oriApSnmpV3Agent.getAuthProtocol());
    assertEquals(apSnmpV3Agent.getAuthPassword(), oriApSnmpV3Agent.getAuthPassword());
    assertEquals(apSnmpV3Agent.getPrivacyPassword(), oriApSnmpV3Agent.getPrivacyPassword());
    assertEquals(apSnmpV3Agent.getPrivacyProtocol(), oriApSnmpV3Agent.getPrivacyProtocol());
    assertEquals(apSnmpV3Agent.getNotificationType(), oriApSnmpV3Agent.getNotificationType());
    assertEquals(apSnmpV3Agent.getTargetAddr(), oriApSnmpV3Agent.getTargetAddr());
    assertEquals(apSnmpV3Agent.getTargetPort(), oriApSnmpV3Agent.getTargetPort());
  }

  @Test
  void getVenueBonjourFencingTest(Venue venue) throws Exception {
    var venueBonjourFencing = new VenueBonjourFencing();
    var result = extendedVenueServiceCtrl.getVenueBonjourFencing(venue.getId());
    assertEquals(result.getEnabled(), venueBonjourFencing.getEnabled());
  }

  @Test
  void updateVenueBonjourFencingTest(Venue venue) throws Exception {
    var venueBonjourFencing = new VenueBonjourFencing();
    var bonjourFencing = new BonjourFencing();

    //wireless rule
    var wirelessRule = new BonjourFencingWirelessRule();
    wirelessRule.setFencingRange(FencingRangeEnum.ONE_HOP_AP);
    bonjourFencing.setService(BridgeServiceEnum.AIRDISK);
    bonjourFencing.setWirelessEnabled(true);
    bonjourFencing.setWirelessRule(wirelessRule);

    //wired rules
    var wiredRuleList = new ArrayList<BonjourFencingWiredRule>();
    var wiredRule = new BonjourFencingWiredRule();
    bonjourFencing.setWiredEnabled(true);
    wiredRule.setFencingRange(FencingRangeEnum.ONE_HOP_AP);
    wiredRule.setClosestApMac("55:66:77:88:99:99");
    wiredRule.setDeviceMacAddresses(List.of("00:0A:95:9D:68:01", "00:0A:95:9D:68:02"));
    wiredRuleList.add(wiredRule);
    bonjourFencing.setWiredRules(wiredRuleList);

    venueBonjourFencing.setEnabled(true);
    venueBonjourFencing.setServices(Arrays.asList(bonjourFencing));

    extendedVenueServiceCtrl.updateVenueBonjourFencing(venue.getId(), venueBonjourFencing);
    var result = extendedVenueServiceCtrl.getVenueBonjourFencing(venue.getId());
    var bonjourFencingList = result.getServices();
    var bf = bonjourFencingList.get(0);

    assertTrue(bf.getWirelessEnabled());
    assertTrue(bf.getWiredEnabled());
    assertEquals(BridgeServiceEnum.AIRDISK, bf.getService());
    assertEquals(FencingRangeEnum.ONE_HOP_AP, bf.getWirelessRule().getFencingRange());
    assertEquals(FencingRangeEnum.ONE_HOP_AP, bf.getWiredRules().get(0).getFencingRange());
    assertEquals("55:66:77:88:99:99", bf.getWiredRules().get(0).getClosestApMac());
  }

  @Deprecated
  @Test
  void testUpdateMeshOption(Venue venue) throws Exception {
    // Mesh default is disabled
    assertFalse(venue.getMesh().getEnabled());
    // Then update and validate
    Mesh mesh = new Mesh();
    mesh.setEnabled(true);
    extendedVenueServiceCtrl.updateMeshOptions(venue.getId(), mesh);
    assertTrue(venue.getMesh().getEnabled());
    assertNotNull(venue.getMesh().getPassphrase());
    assertNotNull(venue.getMesh().getSsid());
    // Test disable mesh
    mesh.setEnabled(false);
    extendedVenueServiceCtrl.updateMeshOptions(venue.getId(), mesh);
    assertFalse(venue.getMesh().getEnabled());
  }

  @Deprecated
  @Test
  void testUpdateMeshOptionWithMeshEnhancement(Venue venue) throws Exception {
    assertFalse(venue.getMesh().getEnabled());
    assertFalse(venue.getMesh().getZeroTouchEnabled());
    assertEquals(MeshRadioTypeEnumV1_1._5_6_GHz, venue.getMesh().getRadioType());
    assertNull(venue.getMesh().getSsid());
    assertNull(venue.getMesh().getPassphrase());

    Mesh mesh = new Mesh();
    mesh.setEnabled(true);
    mesh.setSsid("test_mesh");
    mesh.setPassphrase("test_passphrase");
    mesh.setRadioType(MeshRadioTypeEnumV1_1._2_4_GHz);
    extendedVenueServiceCtrl.updateMeshOptions(venue.getId(), mesh);
    var result = extendedVenueServiceCtrl.getVenue(venue.getId()).getMesh();
    assertTrue(result.getEnabled());
    assertEquals("test_mesh", result.getSsid());
    assertEquals("test_passphrase", result.getPassphrase());
    assertEquals(MeshRadioTypeEnumV1_1._2_4_GHz, result.getRadioType());

    // Test disable mesh
    mesh = new Mesh();
    extendedVenueServiceCtrl.updateMeshOptions(venue.getId(), mesh);
    assertFalse(venue.getMesh().getEnabled());
    assertFalse(venue.getMesh().getZeroTouchEnabled());
    assertNull(venue.getMesh().getPassphrase());
    assertNull(venue.getMesh().getSsid());
  }

  @Deprecated
  @Test
  void testUpdateMeshOptionsWithDhcpEnabled(Venue venue) throws Exception {
    // Mesh and DHCP default are disabled
    assertFalse(venue.getMesh().getEnabled());
    assertFalse(venue.getDhcpServiceSetting().getEnabled());
    // Set DHCP enabled
    VenueDhcpServiceSetting venueDhcpServiceSetting = new VenueDhcpServiceSetting();
    venueDhcpServiceSetting.setEnabled(true);
    venue.setDhcpServiceSetting(venueDhcpServiceSetting);
    assertTrue(venue.getDhcpServiceSetting().getEnabled());
    // Then
    Mesh mesh = new Mesh();
    mesh.setEnabled(true);
    assertThrows(CommonException.class,
        () -> extendedVenueServiceCtrl.updateMeshOptions(venue.getId(), mesh));
  }

  @Deprecated
  @Test
  void testUpdateMeshOptionOperationNotAllowed(Venue venue) throws Exception {
    venue.getMesh().setEnabled(true);
    venue.getMesh().setSsid("test_mesh");

    // Not allow to update mesh settings when it is active
    Mesh mesh = new Mesh();
    mesh.setEnabled(true);
    mesh.setSsid("test_mesh");
    mesh.setRadioType(MeshRadioTypeEnumV1_1._2_4_GHz);
    String venueId = venue.getId();
    assertNull(mesh.getPassphrase());
    assertThrows(OperationNotAllowedException.class, () ->
        extendedVenueServiceCtrl.updateMeshOptions(venueId, mesh));

    // Not allow to disable mesh when any AP is MAP or eMAP
    mesh.setEnabled(false);
    doThrow(new OperationNotAllowedException("Mesh can not be disabled.")).when(apServiceCtrl)
        .resetApMeshFromVenue(eq(venueId));
    assertThrows(OperationNotAllowedException.class, () ->
        extendedVenueServiceCtrl.updateMeshOptions(venueId, mesh));
  }

  @Deprecated
  @Test
  void getDenialOfServiceProtectionTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getDenialOfServiceProtection(venue.getId());
    assertFalse(result.getEnabled());
    assertEquals(result.getBlockingPeriod(), (short) 60);
    assertEquals(result.getFailThreshold(), (short) 5);
    assertEquals(result.getCheckPeriod(), (short) 30);
  }

  @Deprecated
  @Test
  void updateDenialOfServiceProtectionTest(Venue venue) throws Exception {
    DenialOfServiceProtection denialOfServiceProtection = new DenialOfServiceProtection();
    denialOfServiceProtection.setEnabled(true);
    denialOfServiceProtection.setBlockingPeriod((short) 50);
    denialOfServiceProtection.setFailThreshold((short) 3);
    denialOfServiceProtection.setCheckPeriod((short) 20);
    extendedVenueServiceCtrl.updateDenialOfServiceProtection(venue.getId(),
        denialOfServiceProtection);
    var result = extendedVenueServiceCtrl.getDenialOfServiceProtection(venue.getId());
    assertTrue(result.getEnabled());
    assertEquals(result.getBlockingPeriod(), (short) 50);
    assertEquals(result.getFailThreshold(), (short) 3);
    assertEquals(result.getCheckPeriod(), (short) 20);
  }

  @Deprecated
  @Test
  void getVenueRadiusOptionsTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueRadiusOptions(venue.getId());
    assertFalse(result.getOverrideEnabled());
    assertEquals(NasIdTypeEnum.BSSID, result.getNasIdType());
    assertEquals(CalledStationIdTypeEnum.BSSID, result.getCalledStationIdType());
    assertEquals(NasIdDelimiterEnum.DASH, result.getNasIdDelimiter());
    assertEquals(3, result.getNasRequestTimeoutSec());
    assertEquals(2, result.getNasMaxRetry());
    assertEquals(5, result.getNasReconnectPrimaryMin());
    assertFalse(result.getSingleSessionIdAccounting());
    assertNull(result.getUserDefinedNasId());
  }

  @Deprecated
  @Test
  void updateVenueRadiusOptionsTest(Venue venue)
      throws Exception {
    VenueRadiusOptions venueRadiusOptions = new VenueRadiusOptions();
    venueRadiusOptions.setOverrideEnabled(true);
    venueRadiusOptions.setSingleSessionIdAccounting(true);
    venueRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_MAC);
    venueRadiusOptions.setNasIdType(NasIdTypeEnum.VENUE_NAME);
    venueRadiusOptions.setNasRequestTimeoutSec(4);
    venueRadiusOptions.setNasMaxRetry(3);
    venueRadiusOptions.setNasReconnectPrimaryMin(6);

    extendedVenueServiceCtrl.updateVenueRadiusOptions(venue.getId(), venueRadiusOptions);
    var result = extendedVenueServiceCtrl.getVenueRadiusOptions(venue.getId());

    assertTrue(result.getOverrideEnabled());
    assertEquals(NasIdTypeEnum.VENUE_NAME, result.getNasIdType());
    assertEquals(CalledStationIdTypeEnum.AP_MAC, result.getCalledStationIdType());
    assertEquals(NasIdDelimiterEnum.DASH, result.getNasIdDelimiter());
    assertEquals(4, result.getNasRequestTimeoutSec());
    assertEquals(3, result.getNasMaxRetry());
    assertEquals(6, result.getNasReconnectPrimaryMin());
    assertTrue(result.getSingleSessionIdAccounting());
    assertNull(result.getUserDefinedNasId());
  }

  @Deprecated
  @Test
  void getDefaultVenueBssColoringTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueBssColoringSettings(venue.getId());
    assertTrue(result.getBssColoringEnabled());
  }

  @Deprecated
  @Test
  void updateVenueBssColoringTest(Venue venue) throws Exception {
    BssColoring bssColoring = new BssColoring();
    bssColoring.setBssColoringEnabled(false);
    extendedVenueServiceCtrl.updateVenueBssColoringSettings(venue.getId(), bssColoring);
    var result = extendedVenueServiceCtrl.getVenueBssColoringSettings(venue.getId());
    assertFalse(result.getBssColoringEnabled());
  }

  @Test
  void getDefaultVenueApManagementVlanTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApManagementVlanSettings(venue.getId());
    assertEquals((short) 1, result.getVlanId());
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_VENUE_AP_MGMT_VLAN_TOGGLE})
  void updateVenueApManagementTest(Venue venue) throws Exception {
    VenueApManagementTrafficVlanSettings managementVlan = new VenueApManagementTrafficVlanSettings();
    managementVlan.setVlanId((short) 7);
    extendedVenueServiceCtrl.updateVenueApManagementVlanSettings(venue.getId(), managementVlan);
    var result = extendedVenueServiceCtrl.getVenueApManagementVlanSettings(venue.getId());
    assertEquals((short) 7, result.getVlanId());
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_AP_MGMT_VLAN_AP_LEVEL_TOGGLE})
  void updateVenueApManagementTestWithCliSettingFirst(Venue venue) throws Exception {
    // Given
    VenueApManagementTrafficVlanSettings managementVlan = new VenueApManagementTrafficVlanSettings();
    managementVlan.setVlanId((short) 100);

    // When
    extendedVenueServiceCtrl.updateVenueApManagementVlanSettings(venue.getId(), managementVlan);

    // Then
    verify(apServiceCtrl, times(1))
        .updateManagementVlanData(anyString(), eq((short) 100));
  }

  private VenueCellularSettings createCellularSettings() {
    var result = new VenueCellularSettings();
    var sim1 = new SimSettings();
    sim1.setNetworkSelection(CellularNetworkSelectionEnum.AUTO);
    sim1.setApn("");
    result.setPrimarySim(sim1);
    var sim2 = new SimSettings();
    sim2.setEnabled(false);
    sim2.setRoaming(false);
    sim2.setApn("apn111");
    sim2.setNetworkSelection(CellularNetworkSelectionEnum.LTE);
    result.setSecondarySim(sim2);
    result.setWanConnection(WanConnectionEnum.CELLULAR);
    result.setPrimaryWanRecoveryTimer(99);
    var attributes = new VenueApModelSpecificAttributes();
    attributes.setModel("M510");
    result.setVenueApModelSpecificAttributes(attributes);
    var channel1 = new LteBandLockChannel();
    channel1.setSimCardId(0);
    channel1.setRegion(LteBandRegionEnum.DOMAIN_2);
    channel1.setBand3G(new ArrayList<>(List.of("B1", "B2")));
    channel1.setBand4G(new ArrayList<>(List.of("B1", "B2", "B3", "B4")));
    var channel2 = new LteBandLockChannel();
    channel2.setSimCardId(1);
    channel2.setRegion(LteBandRegionEnum.DOMAIN_2);
    channel2.setBand3G(new ArrayList<>(List.of("B1", "B2")));
    channel2.setBand4G(new ArrayList<>(List.of("B1", "B2", "B3", "B4", "B5")));
    var channel3 = new LteBandLockChannel();
    channel3.setSimCardId(0);
    channel3.setRegion(LteBandRegionEnum.JAPAN);
    channel3.setBand3G(new ArrayList<>(List.of("B1", "B6")));
    channel3.setBand4G(new ArrayList<>(List.of("B1", "B19", "B26", "B41")));
    var channel4 = new LteBandLockChannel();
    channel4.setSimCardId(1);
    channel4.setRegion(LteBandRegionEnum.JAPAN);
    channel4.setBand3G(new ArrayList<>(List.of("B1", "B8")));
    channel4.setBand4G(new ArrayList<>(List.of("B3", "B19", "B41")));
    result.setLteBandLockChannels(Arrays.asList(channel1, channel2, channel3, channel4));
    return result;
  }

  private VenueApModelSpecificAttributes createLedOnRequest(String model, Boolean ledOn) {
    var result = new VenueApModelSpecificAttributes();
    result.setModel(model);
    result.setLedOn(ledOn);
    return result;
  }

  private VenueApModelSpecificAttributes createExternalAntenna(String model) {
    var result = new VenueApModelSpecificAttributes();
    result.setModel(model);
    result.setExternalAntenna(new ExternalAntenna());
    return result;
  }

  @Test
  void testUpdateVenueRadioCustomization(Venue venue) throws Exception {
    VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
    doReturn(venueRadioCustomization).when(venueRadioService).updateVenueRadioCustomization(any(
        Venue.class), any(VenueRadioCustomization.class));

    VenueRadioCustomization result = extendedVenueServiceCtrl.updateVenueRadioCustomization(
        venue.getId(), venueRadioCustomization);
    assertThat(result).isEqualTo(venueRadioCustomization);
  }

  @Deprecated
  @Test
  void testResetVenueRadioCustomization(Venue venue) throws Exception {
    VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
    doReturn(venueRadioCustomization).when(venueRadioService).resetVenueRadioCustomization(any(
        Venue.class));

    VenueRadioCustomization result = extendedVenueServiceCtrl.resetVenueRadioCustomization(
        venue.getId());
    assertThat(result).isEqualTo(venueRadioCustomization);
  }

  @Test
  void getVenueApAvailableLteBands() throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApAvailableLteBands();
    assertFalse(result.isEmpty());
  }

  @Test
  void getVenueApModelCapabilities(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApModelCapabilities(venue.getId());
    assertNotNull(result);
  }

  @Test
  void getVenueApMeshSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApMeshSettings(venue.getId());
    assertNotNull(result);
  }

  @Test
  void getVenueApMeshSettingsV1_1(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApMeshSettingsV1_1(venue.getId());
    assertNotNull(result);
  }

  @Test
  void testUpdateVenueApMeshSettings(Venue venue) throws Exception {
    // Mesh default is disabled
    assertFalse(venue.getMesh().getEnabled());
    // Then update and validate
    Mesh mesh = new Mesh();
    mesh.setEnabled(true);
    extendedVenueServiceCtrl.updateVenueApMeshSettings(venue.getId(), mesh);
    assertTrue(venue.getMesh().getEnabled());
    assertNotNull(venue.getMesh().getPassphrase());
    assertNotNull(venue.getMesh().getSsid());
    // Test disable mesh
    mesh.setEnabled(false);
    extendedVenueServiceCtrl.updateVenueApMeshSettings(venue.getId(), mesh);
    assertFalse(venue.getMesh().getEnabled());
  }

  @Test
  void testUpdateVenueApMeshSettingsWithMeshEnhancement(Venue venue) throws Exception {
    assertFalse(venue.getMesh().getEnabled());
    assertFalse(venue.getMesh().getZeroTouchEnabled());
    assertEquals(MeshRadioTypeEnumV1_1._5_6_GHz, venue.getMesh().getRadioType());
    assertNull(venue.getMesh().getSsid());
    assertNull(venue.getMesh().getPassphrase());

    Mesh mesh = new Mesh();
    mesh.setEnabled(true);
    mesh.setSsid("test_mesh");
    mesh.setPassphrase("test_passphrase");
    mesh.setRadioType(MeshRadioTypeEnumV1_1._2_4_GHz);
    extendedVenueServiceCtrl.updateVenueApMeshSettings(venue.getId(), mesh);
    var result = extendedVenueServiceCtrl.getVenue(venue.getId()).getMesh();
    assertTrue(result.getEnabled());
    assertEquals("test_mesh", result.getSsid());
    assertEquals("test_passphrase", result.getPassphrase());
    assertEquals(MeshRadioTypeEnumV1_1._2_4_GHz, result.getRadioType());
  }

  @Test
  void testUpdateVenueApMeshSettingsV1_1WithMeshRadioEnhance(Venue venue) throws Exception {
    assertFalse(venue.getMesh().getEnabled());
    assertFalse(venue.getMesh().getZeroTouchEnabled());
    assertEquals(MeshRadioTypeEnumV1_1._5_6_GHz, venue.getMesh().getRadioType());
    assertNull(venue.getMesh().getSsid());
    assertNull(venue.getMesh().getPassphrase());

    Mesh mesh = new Mesh();
    mesh.setEnabled(true);
    mesh.setSsid("test_mesh");
    mesh.setPassphrase("test_passphrase");
    mesh.setRadioType(MeshRadioTypeEnumV1_1._5_6_GHz);
    extendedVenueServiceCtrl.updateVenueApMeshSettings(venue.getId(), mesh);
    var result = extendedVenueServiceCtrl.getVenue(venue.getId()).getMesh();
    assertTrue(result.getEnabled());
    assertEquals("test_mesh", result.getSsid());
    assertEquals("test_passphrase", result.getPassphrase());
    assertEquals(MeshRadioTypeEnumV1_1._5_6_GHz, result.getRadioType());
  }

  @Test
  void testUpdateVenueApMeshSettingsWithDhcpEnabled(Venue venue) throws Exception {
    // Mesh and DHCP default are disabled
    assertFalse(venue.getMesh().getEnabled());
    assertFalse(venue.getDhcpServiceSetting().getEnabled());
    // Set DHCP enabled
    VenueDhcpServiceSetting venueDhcpServiceSetting = new VenueDhcpServiceSetting();
    venueDhcpServiceSetting.setEnabled(true);
    venue.setDhcpServiceSetting(venueDhcpServiceSetting);
    assertTrue(venue.getDhcpServiceSetting().getEnabled());
    // Then
    Mesh mesh = new Mesh();
    mesh.setEnabled(true);
    assertThrows(CommonException.class,
        () -> extendedVenueServiceCtrl.updateVenueApMeshSettings(venue.getId(), mesh));
  }

  @Test
  void testUpdateVenueApMeshSettingsOperationNotAllowed(Venue venue) throws Exception {
    venue.getMesh().setEnabled(true);
    venue.getMesh().setSsid("test_mesh");

    // Not allow to update mesh settings when it is active
    Mesh mesh = new Mesh();
    mesh.setEnabled(true);
    mesh.setSsid("test_mesh");
    mesh.setRadioType(MeshRadioTypeEnumV1_1._2_4_GHz);
    String venueId = venue.getId();
    assertNull(mesh.getPassphrase());
    assertThrows(OperationNotAllowedException.class, () ->
        extendedVenueServiceCtrl.updateVenueApMeshSettings(venueId, mesh));

    // Not allow to disable mesh when any AP is MAP or eMAP
    mesh.setEnabled(false);
    doThrow(new OperationNotAllowedException("Mesh can not be disabled.")).when(apServiceCtrl)
        .resetApMeshFromVenue(eq(venueId));
    assertThrows(OperationNotAllowedException.class, () ->
        extendedVenueServiceCtrl.updateVenueApMeshSettings(venueId, mesh));
  }

  @Test
  void getVenueApModelLanPortSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApModelLanPortSettings(venue.getId(), Optional.of(false));
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Test
  void getVenueApModelLanPortSettingsWithDefaultValues(Venue venue) throws Exception {
    List<VenueApModelSpecificAttributes> results = extendedVenueServiceCtrl.getVenueApModelLanPortSettings(
        venue.getId(), Optional.of(true));
    assertNotNull(results);
    assertFalse(results.isEmpty());
    for (var result : results) {
      assertThat(result.getLanPorts()).isNotEmpty();
    }
  }

  @Test
  void updateVenueApModelLanPortSettingsTestWithNoModification(Venue venue) throws Exception {
    var attributesList = venueApModelSpecificAttributesService
        .getLanPortsFromCapabilities(venue);
    var result = extendedVenueServiceCtrl.updateVenueApModelLanPortSettings(venue.getId(),
        attributesList);
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void updateVenueApModelLanPortSettingsTestWithModification(Venue venue) throws Exception {
    var attributesList = venueApModelSpecificAttributesService
        .getLanPortsFromCapabilities(venue);
    var r610 = attributesList.stream()
        .filter(a -> a.getModel().equals("R610"))
        .findAny();
    r610.map(VenueApModelSpecificAttributes::getLanPorts)
        .map(ports -> ports.get(1))
        .map(port -> {
          port.setEnabled(false);
          return port;
        });
    var result = extendedVenueServiceCtrl.updateVenueApModelLanPortSettings(venue.getId(),
        attributesList);
    assertNotNull(result);
    assertEquals(1, result.size());
    var r610toVerify = result.get(0);
    assertEquals("R610", r610toVerify.getModel());
    assertEquals(2, r610toVerify.getLanPorts().size());
    assertTrue(r610toVerify.getLanPorts().get(0).getEnabled());
    assertNotNull(r610toVerify.getLanPorts().get(0).getApLanPortProfile());
    assertNotNull(r610toVerify.getLanPorts().get(0).getApLanPortProfile().getApLanPortId());
    assertFalse(r610toVerify.getLanPorts().get(1).getEnabled());
    assertNotNull(r610toVerify.getLanPorts().get(1).getApLanPortProfile());
    assertNotNull(r610toVerify.getLanPorts().get(1).getApLanPortProfile().getApLanPortId());

    attributesList = venueApModelSpecificAttributesService
        .getLanPortsFromCapabilities(venue);
    r610 = attributesList.stream()
        .filter(a -> a.getModel().equals("R610"))
        .findAny();
    r610.map(VenueApModelSpecificAttributes::getLanPorts)
        .map(ports -> ports.get(1))
        .map(port -> {
          port.setEnabled(true);
          return port;
        });
    r610.map(VenueApModelSpecificAttributes::getLanPorts)
        .map(ports -> ports.get(1))
        .map(port -> {
          port.setEnabled(true);
          return port;
        });
    result = extendedVenueServiceCtrl.updateVenueApModelLanPortSettings(venue.getId(),
        attributesList);
    assertNotNull(result);
    r610toVerify = result.get(0);
    assertEquals("R610", r610toVerify.getModel());
    assertEquals(2, r610toVerify.getLanPorts().size());
    assertTrue(r610toVerify.getLanPorts().get(0).getEnabled());
    assertNotNull(r610toVerify.getLanPorts().get(0).getApLanPortProfile());
    assertNotNull(r610toVerify.getLanPorts().get(0).getApLanPortProfile().getApLanPortId());
    assertTrue(r610toVerify.getLanPorts().get(1).getEnabled());
    assertNotNull(r610toVerify.getLanPorts().get(1).getApLanPortProfile());
    assertNotNull(r610toVerify.getLanPorts().get(1).getApLanPortProfile().getApLanPortId());
  }

  @Test
  void getVenueApModelLedSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApModelLedSettings(venue.getId());
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void updateVenueApModelLedSettingsTestWithNewVenue(Tenant tenant, Venue venue) throws Exception {
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());

    var result = extendedVenueServiceCtrl.updateVenueApModelLedSettings(venue.getId(), List.of(
        createLedOnRequest("R610", Boolean.TRUE),
        createLedOnRequest("R550", Boolean.FALSE))
    );
    // CREATE VERIFY
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("R550", result.get(0).getModel());
    assertEquals(Boolean.FALSE, result.get(0).getLedOn());
    assertEquals("R610", result.get(1).getModel());
    assertEquals(Boolean.TRUE, result.get(1).getLedOn());
  }

  @Test
  void updateVenueApModelLedSettingsTestWithExistedVenue(Venue venue) throws Exception {
    // CREATE
    var result = extendedVenueServiceCtrl.updateVenueApModelLedSettings(venue.getId(), List.of(
        createLedOnRequest("R610", Boolean.TRUE),
        createLedOnRequest("R550", Boolean.FALSE))
    );
    // CREATE VERIFY
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("R550", result.get(0).getModel());
    assertEquals(Boolean.FALSE, result.get(0).getLedOn());
    assertEquals("R610", result.get(1).getModel());
    assertEquals(Boolean.TRUE, result.get(1).getLedOn());

    // UPDATE
    result = extendedVenueServiceCtrl.updateVenueApModelLedSettings(venue.getId(), List.of(
        createLedOnRequest("M510", Boolean.TRUE),
        createLedOnRequest("R610", Boolean.FALSE)
    ));
    // UPDATE VERIFY
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals("M510", result.get(0).getModel());
    assertEquals(Boolean.TRUE, result.get(0).getLedOn());
    assertEquals("R550", result.get(1).getModel());
    assertNull(result.get(1).getLedOn());
    assertEquals("R610", result.get(2).getModel());
    assertEquals(Boolean.FALSE, result.get(2).getLedOn());
  }

  @Test
  void getVenueBandModeTestForTripleBand(Venue venue) throws Exception {
    // For TripleBand backward compatibility

    venue.getRadioCustomization().setTripleBandEnabled(true);
    venue.getRadioCustomization().getRadioParamsDual5G().setEnabled(false); //dual5g disable = 2,5,6
    venueRepository.save(venue);
    var result = extendedVenueServiceCtrl.getVenueBandMode(venue.getId());

    assertNotNull(result);
    boolean isR760TripleBand = result.stream().anyMatch(v -> v.getModel().equals(AP_MODEL_R760) &&
        v.getBandMode().equals(BandModeEnum.TRIPLE));
    assertTrue(isR760TripleBand);

    venue.getRadioCustomization().getRadioParamsDual5G().setEnabled(true); //dual5g enable = 2,5,5
    venueRepository.save(venue);
    result = extendedVenueServiceCtrl.getVenueBandMode(venue.getId());
    assertNotNull(result);
    boolean isR760DualBand = result.stream().anyMatch(v -> v.getModel().equals(AP_MODEL_R760) &&
        v.getBandMode().equals(BandModeEnum.DUAL));
    assertTrue(isR760DualBand);
    venue.getRadioCustomization().setTripleBandEnabled(false);
    venueRepository.save(venue);
    result = extendedVenueServiceCtrl.getVenueBandMode(venue.getId());
    assertNotNull(result);
    assertFalse(result.stream().anyMatch(v -> v.getModel().equals(AP_MODEL_R760)));
  }

  @Test
  void updateVenueBandModeTestForTripleBand(Venue venue) throws Exception {
    // For TripleBand backward compatibility

    assertFalse(venue.getRadioCustomization().getTripleBandEnabled());

    VenueApModelSpecificAttributes attributes = new VenueApModelSpecificAttributes();
    attributes.setModel(AP_MODEL_R760);
    attributes.setBandMode(BandModeEnum.TRIPLE);
    attributes.setVenue(venue);
    attributes.setTenant(venue.getTenant());
    var result = extendedVenueServiceCtrl.updateVenueBandMode(venue.getId(), List.of(attributes));

    assertNotNull(result);
    assertTrue(venue.getRadioCustomization().getTripleBandEnabled());
    assertFalse(venue.getRadioCustomization().getRadioParamsDual5G().getEnabled());

    // Test R760 + T670
    String AP_MODEL_T670 = "T670";
    VenueApModelSpecificAttributes attrT670 = new VenueApModelSpecificAttributes();
    attrT670.setModel(AP_MODEL_T670);
    attrT670.setBandMode(BandModeEnum.TRIPLE);
    attrT670.setVenue(venue);
    attrT670.setTenant(venue.getTenant());

    attributes.setBandMode(BandModeEnum.DUAL);
    result = extendedVenueServiceCtrl.updateVenueBandMode(venue.getId(), List.of(attributes, attrT670)); // 2 models
    assertNotNull(result);
    assertTrue(venue.getRadioCustomization().getTripleBandEnabled());

    extendedVenueServiceCtrl.getVenueBandMode(venue.getId());
    assertNotNull(result);
    assertEquals(2, result.size());
    boolean isR760DualBand = result.stream().anyMatch(v -> v.getModel().equals(AP_MODEL_R760) &&
        v.getBandMode().equals(BandModeEnum.DUAL));
    assertTrue(isR760DualBand);
    boolean isT670TripleBand = result.stream().anyMatch(v -> v.getModel().equals(AP_MODEL_T670) &&
        v.getBandMode().equals(BandModeEnum.TRIPLE));
    assertTrue(isT670TripleBand);

    // Test R670
    String AP_MODEL_R670 = "R670";
    VenueApModelSpecificAttributes attrR670 = new VenueApModelSpecificAttributes();
    attrR670.setModel(AP_MODEL_R670);
    attrR670.setBandMode(BandModeEnum.TRIPLE);
    attrR670.setVenue(venue);
    attrR670.setTenant(venue.getTenant());

    result = extendedVenueServiceCtrl.updateVenueBandMode(venue.getId(), List.of(attrR670));
    assertFalse(venue.getRadioCustomization().getTripleBandEnabled());
    assertEquals(1, result.size());
    boolean isR670TripleBand = result.stream().anyMatch(v -> v.getModel().equals(AP_MODEL_R670) &&
        v.getBandMode().equals(BandModeEnum.TRIPLE));
    assertTrue(isR670TripleBand);
  }

  @Test
  void updateVenueTripleBandRadioSettingsTest(Venue venue) throws Exception {
    TripleBand tripleBand = new TripleBand();
    tripleBand.setEnabled(Boolean.TRUE);

    extendedVenueServiceCtrl
        .updateVenueTripleBandRadioSettings(venue.getId(), tripleBand);

    Venue venueResult = extendedVenueServiceCtrl.getVenue(venue.getId());
    assertTrue(venueResult.getRadioCustomization().getTripleBandEnabled());
    assertFalse(venueResult.getRadioCustomization().getRadioParamsDual5G().getEnabled()); //dual5g false = tripleBand

    tripleBand.setEnabled(Boolean.FALSE);
    extendedVenueServiceCtrl
        .updateVenueTripleBandRadioSettings(venue.getId(), tripleBand);
    venueResult = extendedVenueServiceCtrl.getVenue(venue.getId());
    assertFalse(venueResult.getRadioCustomization().getTripleBandEnabled());
  }

  @Test
  void getVenueApModelExternalAntennaSettingsTest(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApModelExternalAntennaSettings(venue.getId());
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Test
  void updateVenueApModelExternalAntennaSettingsTestWithInvalidModel(Tenant tenant)
      throws Exception {
    final String venueId = randomId();
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());
    doReturn(tenant).when(tenantService).getTenant();
    extendedVenueServiceCtrl.addVenue(new Venue(venueId));

    assertThrows(InvalidPropertyValueException.class,
        () -> extendedVenueServiceCtrl.updateVenueApModelExternalAntennaSettings(venueId,
            List.of(createExternalAntenna("R610"))));
  }

  @Test
  void updateVenueApModelExternalAntennaSettingsTestWithValidModel(Tenant tenant, Venue venue)
      throws Exception {
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());

    var result = extendedVenueServiceCtrl.updateVenueApModelExternalAntennaSettings(venue.getId(),
        List.of(createExternalAntenna("E510")));
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Test
  void updategetVenueApCellularSettingsTestWithNewVenue(Tenant tenant, Venue venue)
      throws Exception {
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());

    var request = createCellularSettings();
    var result = extendedVenueServiceCtrl.updateVenueApCellularSettings(venue.getId(), request);
    // CREATE VERIFY
    assertNotNull(result.getVenueApModelSpecificAttributes());
    assertNotNull(result.getVenueApModelSpecificAttributes().getVenue());
    assertEquals(result, result.getVenueApModelSpecificAttributes().getCellularSettings());
    assertEquals(1,
        result.getVenueApModelSpecificAttributes().getVenue().getModelSpecificAttributes().size());
    assertEquals(4, result.getLteBandLockChannels().size());
  }

  @Test
  void updateVenueApCellularSettingsTestWithExistedVenue(Venue venue) throws Exception {
    // CREATE
    var request = createCellularSettings();
    var result = extendedVenueServiceCtrl.updateVenueApCellularSettings(venue.getId(), request);
    // CREATE VERIFY
    assertNotNull(result.getVenueApModelSpecificAttributes());
    assertNotNull(result.getVenueApModelSpecificAttributes().getVenue());
    assertEquals(result, result.getVenueApModelSpecificAttributes().getCellularSettings());
    assertEquals(1,
        result.getVenueApModelSpecificAttributes().getVenue().getModelSpecificAttributes().size());
    assertEquals(4, result.getLteBandLockChannels().size());

    // UPDATE
    var updateRequest = createCellularSettings();
    var sim2 = updateRequest.getSecondarySim();
    updateRequest.setSecondarySim(updateRequest.getPrimarySim());
    updateRequest.setPrimarySim(sim2);
    updateRequest.getLteBandLockChannels().get(0).setRegion(LteBandRegionEnum.DOMAIN_1);
    updateRequest.getLteBandLockChannels().get(1)
        .setBand3G(new ArrayList<>(List.of("B1", "A1", "B2")));
    updateRequest.getLteBandLockChannels().get(1)
        .setBand4G(new ArrayList<>(List.of("B1", "B2", "A1", "B4", "B5")));
    var updateResult = extendedVenueServiceCtrl.updateVenueApCellularSettings(
        venue.getId(), updateRequest);
    // UPDATE VERIFY
    assertNotNull(updateResult.getVenueApModelSpecificAttributes());
    assertNotNull(updateResult.getVenueApModelSpecificAttributes().getVenue());
    assertEquals(updateResult,
        updateResult.getVenueApModelSpecificAttributes().getCellularSettings());
    assertEquals(1,
        updateResult.getVenueApModelSpecificAttributes().getVenue().getModelSpecificAttributes()
            .size());
    assertEquals(4, updateResult.getLteBandLockChannels().size());
    var resultSim1Band = updateResult.getLteBandLockChannels().stream()
        .filter(b -> Integer.valueOf(0).equals(b.getSimCardId()))
        .findAny().get();
    var resultSim2Band = updateResult.getLteBandLockChannels().stream()
        .filter(b -> Integer.valueOf(1).equals(b.getSimCardId()))
        .findAny().get();
    assertEquals(LteBandRegionEnum.DOMAIN_1, resultSim1Band.getRegion());
    assertEquals(List.of("B1", "A1", "B2"), resultSim2Band.getBand3G());
    assertEquals(List.of("B1", "B2", "A1", "B4", "B5"), resultSim2Band.getBand4G());
  }

  @Test
  void testGetVenueApRadioSettings_defaultOnly(Venue venue) throws Exception {
    VenueRadioCustomization venueApRadioSettings = new VenueRadioCustomization();
    doReturn(venueApRadioSettings).when(venueRadioService)
        .getDefaultRadioCustomization(any(Venue.class));
    VenueRadioCustomization result = extendedVenueServiceCtrl.getVenueApRadioSettings(
        venue.getId(),
        Optional.of(true));

    assertEquals(ScanMethodEnum.CHANNELFLY, result.getRadioParams24G().getMethod(),
        "RadioParams24G method should be CHANNELFLY ");
    assertEquals(ScanMethodEnum.CHANNELFLY, result.getRadioParams50G().getMethod(),
        "RadioParams50G Method should be CHANNELFLY ");
    assertEquals(ScanMethodEnum.CHANNELFLY,
        result.getRadioParamsDual5G().getRadioParamsLower5G().getMethod(),
        "RadioParamsLower5G Method should be CHANNELFLY ");
    assertEquals(ScanMethodEnum.CHANNELFLY,
        result.getRadioParamsDual5G().getRadioParamsUpper5G().getMethod(),
        "RadioParamsUpper5G Method should be CHANNELFLY ");
    assertEquals(ScanMethodEnum.CHANNELFLY, result.getRadioParams6G().getMethod(),
        "RadioParams6G Method should be CHANNELFLY ");
    assertNotNull(result.getRadioParams6G().getVenueHeight(),
        "AFC venue height should not be null");
    assertNull(result.getRadioParams6G().getVenueHeight().getMinFloor(),
        "AFC venue height min floor should be null");
    assertNull(result.getRadioParams6G().getVenueHeight().getMaxFloor(),
        "AFC venue height min floor should be null");
    assertThat(result).isEqualTo(venueApRadioSettings);
  }

  @Test
  void testGetVenueApRadioSettingsV1_1_defaultOnly(Venue venue) throws Exception {
    VenueRadioCustomization venueApRadioSettings = new VenueRadioCustomization();
    doReturn(venueApRadioSettings).when(venueRadioService)
        .getDefaultRadioCustomization(any(Venue.class));
    VenueRadioCustomization result = extendedVenueServiceCtrl.getVenueApRadioSettingsV1_1(
        venue.getId(),
        Optional.of(true));

    assertEquals(ScanMethodEnum.CHANNELFLY, result.getRadioParams24G().getMethod(),
        "RadioParams24G method should be CHANNELFLY ");
    assertEquals(ScanMethodEnum.CHANNELFLY, result.getRadioParams50G().getMethod(),
        "RadioParams50G Method should be CHANNELFLY ");
    assertEquals(ScanMethodEnum.CHANNELFLY,
        result.getRadioParamsDual5G().getRadioParamsLower5G().getMethod(),
        "RadioParamsLower5G Method should be CHANNELFLY ");
    assertEquals(ScanMethodEnum.CHANNELFLY,
        result.getRadioParamsDual5G().getRadioParamsUpper5G().getMethod(),
        "RadioParamsUpper5G Method should be CHANNELFLY ");
    assertEquals(ScanMethodEnum.CHANNELFLY, result.getRadioParams6G().getMethod(),
        "RadioParams6G Method should be CHANNELFLY ");
    assertNotNull(result.getRadioParams6G().getVenueHeight(),
        "AFC venue height should not be null");
    assertNull(result.getRadioParams6G().getVenueHeight().getMinFloor(),
        "AFC venue height min floor should be null");
    assertNull(result.getRadioParams6G().getVenueHeight().getMaxFloor(),
        "AFC venue height min floor should be null");
    assertThat(result).isEqualTo(venueApRadioSettings);
  }

  @Test
  void testGetVenueApRadioSettings(Venue venue) throws Exception {
    VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
    doReturn(venueRadioCustomization).when(venueRadioService)
        .getVenueRadioCustomization(any(Venue.class));
    VenueRadioCustomization result = extendedVenueServiceCtrl.getVenueApRadioSettings(
        venue.getId(),
        Optional.of(false));

    assertNotNull(result.getRadioParams6G().getVenueHeight(),
        "AFC venue height should not be null");
    assertNull(result.getRadioParams6G().getVenueHeight().getMinFloor(),
        "AFC venue height min floor should be null");
    assertNull(result.getRadioParams6G().getVenueHeight().getMaxFloor(),
        "AFC venue height min floor should be null");
    assertThat(result).isEqualTo(venueRadioCustomization);
  }

  @Test
  void testGetVenueApRadioSettingsV1_1(Venue venue) throws Exception {
    VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
    doReturn(venueRadioCustomization).when(venueRadioService)
        .getVenueRadioCustomization(any(Venue.class));
    VenueRadioCustomization result = extendedVenueServiceCtrl.getVenueApRadioSettingsV1_1(
        venue.getId(),
        Optional.of(false));

    assertNotNull(result.getRadioParams6G().getVenueHeight(),
        "AFC venue height should not be null");
    assertNull(result.getRadioParams6G().getVenueHeight().getMinFloor(),
        "AFC venue height min floor should be null");
    assertNull(result.getRadioParams6G().getVenueHeight().getMaxFloor(),
        "AFC venue height min floor should be null");
    assertThat(result).isEqualTo(venueRadioCustomization);
  }

  @Test
  void testUpdateVenueApRadioSettings(Venue venue) throws Exception {
    VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
    doReturn(venueRadioCustomization).when(venueRadioService).updateVenueRadioCustomization(any(
        Venue.class), any(VenueRadioCustomization.class));

    VenueRadioCustomization result = extendedVenueServiceCtrl.updateVenueApRadioSettings(
        venue.getId(), venueRadioCustomization);
    assertThat(result).isEqualTo(venueRadioCustomization);
  }

  @Test
  void testUpdateVenueApRadioSettingsV1_1(Venue venue) throws Exception {
    VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
    doReturn(venueRadioCustomization).when(venueRadioService).updateVenueRadioCustomization(any(
        Venue.class), any(VenueRadioCustomization.class));

    VenueRadioCustomization result = extendedVenueServiceCtrl.updateVenueApRadioSettingsV1_1(
        venue.getId(), venueRadioCustomization);
    assertThat(result).isEqualTo(venueRadioCustomization);
  }

  @Test
  void getVenueApDosProtectionSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApDosProtectionSettings(venue.getId());
    assertFalse(result.getEnabled());
    assertEquals(result.getBlockingPeriod(), (short) 60);
    assertEquals(result.getFailThreshold(), (short) 5);
    assertEquals(result.getCheckPeriod(), (short) 30);
  }

  @Test
  void updateVenueApDosProtectionSettings(Venue venue) throws Exception {
    DenialOfServiceProtection denialOfServiceProtection = new DenialOfServiceProtection();
    denialOfServiceProtection.setEnabled(true);
    denialOfServiceProtection.setBlockingPeriod((short) 50);
    denialOfServiceProtection.setFailThreshold((short) 3);
    denialOfServiceProtection.setCheckPeriod((short) 20);
    extendedVenueServiceCtrl.updateVenueApDosProtectionSettings(venue.getId(),
        denialOfServiceProtection);
    var result = extendedVenueServiceCtrl.getVenueApDosProtectionSettings(venue.getId());
    assertTrue(result.getEnabled());
    assertEquals(result.getBlockingPeriod(), (short) 50);
    assertEquals(result.getFailThreshold(), (short) 3);
    assertEquals(result.getCheckPeriod(), (short) 20);
  }

  @Test
  void getVenueApBssColoringSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApBssColoringSettings(venue.getId());
    assertTrue(result.getBssColoringEnabled());
  }

  @Test
  void updateVenueApBssColoringSettings(Venue venue) throws Exception {
    BssColoring bssColoring = new BssColoring();
    bssColoring.setBssColoringEnabled(false);
    extendedVenueServiceCtrl.updateVenueApBssColoringSettings(venue.getId(), bssColoring);
    var result = extendedVenueServiceCtrl.getVenueApBssColoringSettings(venue.getId());
    assertFalse(result.getBssColoringEnabled());
  }

  @Test
  void getVenueApDirectedMulticastSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApDirectedMulticastSettings(venue.getId());
    assertEquals(result.getWirelessEnabled(), true);
    assertEquals(result.getWiredEnabled(), true);
    assertEquals(result.getNetworkEnabled(), true);
  }

  @Test
  void updateVenueApDirectedMulticastSettings(Venue venue) throws Exception {
    VenueDirectedMulticast venueDirectedMulticast = new VenueDirectedMulticast();
    venueDirectedMulticast.setWirelessEnabled(true);
    venueDirectedMulticast.setWiredEnabled(true);
    venueDirectedMulticast.setNetworkEnabled(false);
    extendedVenueServiceCtrl.updateVenueApDirectedMulticastSettings(venue.getId(),
        venueDirectedMulticast);
    var result = extendedVenueServiceCtrl.getVenueApDirectedMulticastSettings(venue.getId());
    assertEquals(result.getWirelessEnabled(), true);
    assertEquals(result.getWiredEnabled(), true);
    assertEquals(result.getNetworkEnabled(), false);
  }

  @Test
  void getVenueApLoadBalancingSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApLoadBalancingSettings(venue.getId());
    assertEquals(result.getEnabled(), false);
    assertEquals(result.getLoadBalancingMethod(), LoadBalancingMethodEnum.BASED_ON_CLIENT_COUNT);
    assertEquals(result.getSteeringMode(), SteeringModeEnum.BASIC);
    assertEquals(result.getBandBalancingEnabled(), true);
    assertEquals(result.getBandBalancingClientPercent24G(), (short) 25);
    assertEquals(result.getStickyClientSteeringEnabled(), false);
    assertEquals(result.getStickyClientSnrThreshold(), (short) 15);
    assertEquals(result.getStickyClientNbrApPercentageThreshold(), (short) 20);
  }

  @Test
  void updateVenueApLoadBalancingSettingsBasedOnClientCountTestWithModification(Venue venue)
      throws Exception {
    VenueLoadBalancing venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(false);
    venueLoadBalancing.setLoadBalancingMethod(LoadBalancingMethodEnum.BASED_ON_CLIENT_COUNT);
    venueLoadBalancing.setSteeringMode(SteeringModeEnum.STRICT);
    venueLoadBalancing.setBandBalancingEnabled(false);
    venueLoadBalancing.setBandBalancingClientPercent24G((short) 55);
    venueLoadBalancing.setStickyClientSteeringEnabled(true);
    venueLoadBalancing.setStickyClientSnrThreshold((short) 16);
    venueLoadBalancing.setStickyClientNbrApPercentageThreshold((short) 21);
    VenueLoadBalancing result;

    extendedVenueServiceCtrl.updateVenueApLoadBalancingSettings(venue.getId(), venueLoadBalancing);

    result = extendedVenueServiceCtrl.getVenueApLoadBalancingSettings(venue.getId());

    assertEquals(result.getEnabled(), false);
    assertEquals(result.getLoadBalancingMethod(), LoadBalancingMethodEnum.BASED_ON_CLIENT_COUNT);
    assertEquals(result.getSteeringMode(), SteeringModeEnum.STRICT);
    assertEquals(result.getBandBalancingEnabled(), false);
    assertEquals(result.getBandBalancingClientPercent24G(), (short) 55);
    assertEquals(result.getStickyClientSteeringEnabled(), true);
    assertEquals(result.getStickyClientSnrThreshold(), (short) 16);
    assertEquals(result.getStickyClientNbrApPercentageThreshold(), (short) 21);
  }

  @Test
  void updateVenueApLoadBalancingSettingsBasedOnCapacityTestWithModification(Venue venue)
      throws Exception {
    VenueLoadBalancing venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(false);
    venueLoadBalancing.setLoadBalancingMethod(LoadBalancingMethodEnum.BASED_ON_CAPACITY);
    venueLoadBalancing.setSteeringMode(SteeringModeEnum.STRICT);
    venueLoadBalancing.setBandBalancingEnabled(true);
    venueLoadBalancing.setBandBalancingClientPercent24G((short) 55);
    extendedVenueServiceCtrl.updateVenueApLoadBalancingSettings(venue.getId(), venueLoadBalancing);
    var result = extendedVenueServiceCtrl.getVenueApLoadBalancingSettings(venue.getId());
    assertEquals(result.getEnabled(), false);
    assertEquals(result.getLoadBalancingMethod(), LoadBalancingMethodEnum.BASED_ON_CAPACITY);
    assertEquals(result.getSteeringMode(), SteeringModeEnum.STRICT);
    assertEquals(result.getBandBalancingEnabled(), false);
    assertEquals(result.getBandBalancingClientPercent24G(), (short) 25);
  }

  @Test
  void getVenueApRadiusOptions(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApRadiusOptions(venue.getId());
    assertFalse(result.getOverrideEnabled());
    assertEquals(NasIdTypeEnum.BSSID, result.getNasIdType());
    assertEquals(CalledStationIdTypeEnum.BSSID, result.getCalledStationIdType());
    assertEquals(NasIdDelimiterEnum.DASH, result.getNasIdDelimiter());
    assertEquals(3, result.getNasRequestTimeoutSec());
    assertEquals(2, result.getNasMaxRetry());
    assertEquals(5, result.getNasReconnectPrimaryMin());
    assertFalse(result.getSingleSessionIdAccounting());
    assertNull(result.getUserDefinedNasId());
  }

  @Test
  void updateVenueApRadiusOptions(Venue venue)
      throws Exception {
    VenueRadiusOptions venueRadiusOptions = new VenueRadiusOptions();
    venueRadiusOptions.setOverrideEnabled(true);
    venueRadiusOptions.setSingleSessionIdAccounting(true);
    venueRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_MAC);
    venueRadiusOptions.setNasIdType(NasIdTypeEnum.VENUE_NAME);
    venueRadiusOptions.setNasRequestTimeoutSec(4);
    venueRadiusOptions.setNasMaxRetry(3);
    venueRadiusOptions.setNasReconnectPrimaryMin(6);

    extendedVenueServiceCtrl.updateVenueApRadiusOptions(venue.getId(), venueRadiusOptions);
    var result = extendedVenueServiceCtrl.getVenueApRadiusOptions(venue.getId());

    assertTrue(result.getOverrideEnabled());
    assertEquals(NasIdTypeEnum.VENUE_NAME, result.getNasIdType());
    assertEquals(CalledStationIdTypeEnum.AP_MAC, result.getCalledStationIdType());
    assertEquals(NasIdDelimiterEnum.DASH, result.getNasIdDelimiter());
    assertEquals(4, result.getNasRequestTimeoutSec());
    assertEquals(3, result.getNasMaxRetry());
    assertEquals(6, result.getNasReconnectPrimaryMin());
    assertTrue(result.getSingleSessionIdAccounting());
    assertNull(result.getUserDefinedNasId());
  }

  @Test
  void updateVenueApRadiusOptions_WhenOverrideDisabled(Venue venue)
      throws Exception {
    VenueRadiusOptions venueRadiusOptions = new VenueRadiusOptions();
    venueRadiusOptions.setOverrideEnabled(false);
    venueRadiusOptions.setSingleSessionIdAccounting(true);
    venueRadiusOptions.setCalledStationIdType(CalledStationIdTypeEnum.AP_MAC);
    venueRadiusOptions.setNasIdType(NasIdTypeEnum.VENUE_NAME);
    venueRadiusOptions.setNasRequestTimeoutSec(4);
    venueRadiusOptions.setNasMaxRetry(3);
    venueRadiusOptions.setNasReconnectPrimaryMin(6);

    extendedVenueServiceCtrl.updateVenueApRadiusOptions(venue.getId(), venueRadiusOptions);
    var result = extendedVenueServiceCtrl.getVenueApRadiusOptions(venue.getId());

    assertFalse(result.getOverrideEnabled());
    assertEquals(NasIdTypeEnum.VENUE_NAME, result.getNasIdType());
    assertEquals(CalledStationIdTypeEnum.AP_MAC, result.getCalledStationIdType());
    assertEquals(NasIdDelimiterEnum.DASH, result.getNasIdDelimiter());
    assertEquals(4, result.getNasRequestTimeoutSec());
    assertEquals(3, result.getNasMaxRetry());
    assertEquals(6, result.getNasReconnectPrimaryMin());
    assertTrue(result.getSingleSessionIdAccounting());
    assertNull(result.getUserDefinedNasId());
  }

  @Test
  void testGetVenueApClientAdmissionControlSettings(Venue venue) throws Exception {
    // Get the default settings as radio 2.4g disable and radio 5g disable
    var result = extendedVenueServiceCtrl.getVenueApClientAdmissionControlSettings(venue.getId());
    assertFalse(result.getEnable24G());
    assertFalse(result.getEnable50G());
    assertEquals((short) 10, result.getMinClientCount24G());
    assertEquals((short) 20, result.getMinClientCount50G());
    assertEquals((short) 75, result.getMaxRadioLoad24G());
    assertEquals((short) 75, result.getMaxRadioLoad50G());
    assertEquals((short) 0, result.getMinClientThroughput24G());
    assertEquals((short) 0, result.getMinClientThroughput50G());
  }

  @Test
  void testUpdateVenueApClientAdmissionControlSettings(Venue venue) throws Exception {
    // Disable load balancing and band balancing
    VenueLoadBalancing venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(false);
    venueLoadBalancing.setBandBalancingEnabled(false);
    extendedVenueServiceCtrl.updateVenueApLoadBalancingSettings(
        venue.getId(), venueLoadBalancing);

    // Given radio 2.4g enable and radio 5g disable
    VenueClientAdmissionControl venueClientAdmissionControl1 = new VenueClientAdmissionControl();
    venueClientAdmissionControl1.setEnable24G(true);
    venueClientAdmissionControl1.setMinClientCount24G((short) 11);
    venueClientAdmissionControl1.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl1.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl1.setEnable50G(false);

    // When
    extendedVenueServiceCtrl.updateVenueApClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl1);

    // Then
    var result = extendedVenueServiceCtrl.getVenueApClientAdmissionControlSettings(venue.getId());
    assertTrue(result.getEnable24G());
    assertFalse(result.getEnable50G());
    assertEquals((short) 11, result.getMinClientCount24G());
    assertEquals((short) 20, result.getMinClientCount50G());
    assertEquals((short) 50, result.getMaxRadioLoad24G());
    assertEquals((short) 75, result.getMaxRadioLoad50G());
    assertEquals((short) 10, result.getMinClientThroughput24G());
    assertEquals((short) 0, result.getMinClientThroughput50G());

    // Given radio 2.4g disable and radio 5g enable
    VenueClientAdmissionControl venueClientAdmissionControl2 = new VenueClientAdmissionControl();
    venueClientAdmissionControl2.setEnable24G(false);
    venueClientAdmissionControl2.setEnable50G(true);
    venueClientAdmissionControl2.setMinClientCount50G((short) 21);
    venueClientAdmissionControl2.setMaxRadioLoad50G((short) 50);
    venueClientAdmissionControl2.setMinClientThroughput50G((short) 10);

    // When
    extendedVenueServiceCtrl.updateVenueApClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl2);

    // Then
    result = extendedVenueServiceCtrl.getVenueApClientAdmissionControlSettings(venue.getId());
    assertFalse(result.getEnable24G());
    assertTrue(result.getEnable50G());
    assertEquals((short) 10, result.getMinClientCount24G());
    assertEquals((short) 21, result.getMinClientCount50G());
    assertEquals((short) 75, result.getMaxRadioLoad24G());
    assertEquals((short) 50, result.getMaxRadioLoad50G());
    assertEquals((short) 0, result.getMinClientThroughput24G());
    assertEquals((short) 10, result.getMinClientThroughput50G());

    // Given radio 2.4g disable and radio 5g disable
    VenueClientAdmissionControl venueClientAdmissionControl3 = new VenueClientAdmissionControl();
    venueClientAdmissionControl3.setEnable24G(false);
    venueClientAdmissionControl3.setEnable50G(false);

    // When
    extendedVenueServiceCtrl.updateVenueApClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl3);

    // Then
    result = extendedVenueServiceCtrl.getVenueApClientAdmissionControlSettings(venue.getId());
    assertFalse(result.getEnable24G());
    assertFalse(result.getEnable50G());
    assertEquals((short) 10, result.getMinClientCount24G());
    assertEquals((short) 20, result.getMinClientCount50G());
    assertEquals((short) 75, result.getMaxRadioLoad24G());
    assertEquals((short) 75, result.getMaxRadioLoad50G());
    assertEquals((short) 0, result.getMinClientThroughput24G());
    assertEquals((short) 0, result.getMinClientThroughput50G());

    // Given radio 2.4g enable and radio 5g enable
    VenueClientAdmissionControl venueClientAdmissionControl4 = new VenueClientAdmissionControl();
    venueClientAdmissionControl4.setEnable24G(true);
    venueClientAdmissionControl4.setMinClientCount24G((short) 11);
    venueClientAdmissionControl4.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl4.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl4.setEnable50G(true);
    venueClientAdmissionControl4.setMinClientCount50G((short) 21);
    venueClientAdmissionControl4.setMaxRadioLoad50G((short) 50);
    venueClientAdmissionControl4.setMinClientThroughput50G((short) 10);

    // When
    extendedVenueServiceCtrl.updateVenueApClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl4);

    // Then
    result = extendedVenueServiceCtrl.getVenueApClientAdmissionControlSettings(venue.getId());
    assertTrue(result.getEnable24G());
    assertTrue(result.getEnable50G());
    assertEquals((short) 11, result.getMinClientCount24G());
    assertEquals((short) 21, result.getMinClientCount50G());
    assertEquals((short) 50, result.getMaxRadioLoad24G());
    assertEquals((short) 50, result.getMaxRadioLoad50G());
    assertEquals((short) 10, result.getMinClientThroughput24G());
    assertEquals((short) 10, result.getMinClientThroughput50G());

    // Given radio 2.4g enable and radio 5g disable with invalid setting
    VenueClientAdmissionControl venueClientAdmissionControl5 = new VenueClientAdmissionControl();
    venueClientAdmissionControl5.setEnable24G(true);
    venueClientAdmissionControl5.setMinClientCount24G((short) 11);
    venueClientAdmissionControl5.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl5.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl5.setEnable50G(false);
    venueClientAdmissionControl5.setMinClientCount50G((short) 21);
    venueClientAdmissionControl5.setMaxRadioLoad50G((short) 50);
    venueClientAdmissionControl5.setMinClientThroughput50G((short) 10);

    // When
    extendedVenueServiceCtrl.updateVenueApClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl5);

    // Then
    result = extendedVenueServiceCtrl.getVenueApClientAdmissionControlSettings(venue.getId());
    assertTrue(result.getEnable24G());
    assertFalse(result.getEnable50G());
    assertEquals((short) 11, result.getMinClientCount24G());
    assertEquals((short) 20, result.getMinClientCount50G());
    assertEquals((short) 50, result.getMaxRadioLoad24G());
    assertEquals((short) 75, result.getMaxRadioLoad50G());
    assertEquals((short) 10, result.getMinClientThroughput24G());
    assertEquals((short) 0, result.getMinClientThroughput50G());
  }

  @Test
  void testUpdateVenueApLoadBalancingSettingsWhenVenueApClientAdmissionControlSettingsEnabled(
      Venue venue)
      throws Exception {
    // Disable load balancing
    VenueLoadBalancing venueLoadBalancing1 = new VenueLoadBalancing();
    venueLoadBalancing1.setEnabled(false);
    venueLoadBalancing1.setBandBalancingEnabled(false);
    extendedVenueServiceCtrl.updateVenueApLoadBalancingSettings(venue.getId(), venueLoadBalancing1);

    // Enable client admission control
    VenueClientAdmissionControl venueClientAdmissionControl = new VenueClientAdmissionControl();
    venueClientAdmissionControl.setEnable24G(true);
    venueClientAdmissionControl.setMinClientCount24G((short) 11);
    venueClientAdmissionControl.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl.setEnable50G(false);
    extendedVenueServiceCtrl.updateVenueApClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl);

    // Given load balancing enable
    VenueLoadBalancing venueLoadBalancing2 = new VenueLoadBalancing();
    venueLoadBalancing2.setEnabled(true);
    venueLoadBalancing2.setBandBalancingEnabled(false);

    assertThrows(
        CommonException.class,
        () ->
            extendedVenueServiceCtrl.updateVenueApLoadBalancingSettings(venue.getId(),
                venueLoadBalancing2));
  }

  @Test
  void testUpdateVenueApClientAdmissionControlSettingsWhenVenueLoadBalancingEnabled(Venue venue)
      throws Exception {
    // Enable load balancing
    VenueLoadBalancing venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(true);
    venueLoadBalancing.setBandBalancingEnabled(false);
    extendedVenueServiceCtrl.updateVenueApLoadBalancingSettings(venue.getId(), venueLoadBalancing);

    // Given radio 2.4g enable and radio 5g disable
    VenueClientAdmissionControl venueClientAdmissionControl = new VenueClientAdmissionControl();
    venueClientAdmissionControl.setEnable24G(true);
    venueClientAdmissionControl.setMinClientCount24G((short) 11);
    venueClientAdmissionControl.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl.setEnable50G(false);

    assertThrows(
        CommonException.class,
        () ->
            extendedVenueServiceCtrl.updateVenueApClientAdmissionControlSettings(
                venue.getId(), venueClientAdmissionControl));
  }

  @Test
  void testUpdateVenueApClientAdmissionControlSettingsToDisabled_WhenVenueLoadBalancingEnabled(Venue venue)
      throws Exception {
    // Enable load balancing
    VenueLoadBalancing venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(true);
    venueLoadBalancing.setBandBalancingEnabled(false);
    extendedVenueServiceCtrl.updateVenueApLoadBalancingSettings(venue.getId(), venueLoadBalancing);

    // Given radio 2.4g enable and radio 5g disable
    VenueClientAdmissionControl venueClientAdmissionControl = new VenueClientAdmissionControl();
    venueClientAdmissionControl.setEnable24G(false);
    venueClientAdmissionControl.setEnable50G(false);

    assertThatNoException().isThrownBy(
        () ->
            extendedVenueServiceCtrl.updateVenueApClientAdmissionControlSettings(
                venue.getId(), venueClientAdmissionControl));
  }

  @Test
  void testGetVenueDefaultRegulatoryChannels(Venue venue) throws Exception {
    VenueRegulatoryChannels venueRegulatoryChannels = new VenueRegulatoryChannels();
    doReturn(venueRegulatoryChannels).when(venueRadioService)
        .getVenueDefaultRegulatoryChannels(any(Venue.class));

    VenueRegulatoryChannels result = extendedVenueServiceCtrl.getVenueDefaultRegulatoryChannels(
        venue.getId());
    assertThat(result).isEqualTo(venueRegulatoryChannels);
  }

  @Test
  void testGetWifiAvailableChannelsOfVenue(Venue venue) throws Exception {
    VenueRegulatoryChannels venueRegulatoryChannels = new VenueRegulatoryChannels();
    doReturn(venueRegulatoryChannels).when(venueRadioService)
        .getVenueDefaultRegulatoryChannels(any(Venue.class));

    VenueRegulatoryChannels result = extendedVenueServiceCtrl.getWifiAvailableChannelsOfVenue(
        venue.getId());
    assertThat(result).isEqualTo(venueRegulatoryChannels);
  }

  @Test
  void testDeleteSchedules(Tenant tenant, Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*******.2")
      ApVersion apVersion) {
    String tenantId = tenant.getId();

    ScheduleTimeSlot scheduleTimeSlot = createScheduleTimeSlot();

    UpgradeSchedule pendingSchedule = new UpgradeSchedule(randomId());
    pendingSchedule.setStatus(UpgradeScheduleStatus.PENDING);
    pendingSchedule.setTimeSlot(scheduleTimeSlot);
    pendingSchedule.setTenant(tenant);
    pendingSchedule.setVersion(apVersion);
    pendingSchedule.setVenue(venue);
    scheduleRepository.save(pendingSchedule);

    UpgradeSchedule runningSchedule = new UpgradeSchedule(randomId());
    runningSchedule.setStatus(UpgradeScheduleStatus.RUNNING);
    runningSchedule.setTimeSlot(scheduleTimeSlot);
    runningSchedule.setTenant(tenant);
    runningSchedule.setVersion(apVersion);
    runningSchedule.setVenue(venue);
    scheduleRepository.save(runningSchedule);

    List<String> venueIds = List.of(venue.getId());
    extendedVenueServiceCtrl.deleteSchedules(tenantId, venueIds, true);

    verify(venueRepository, times(1)).findByIdInAndTenantId(venueIds, tenantId);
    verify(scheduleService, times(1)).deleteByIds(anyList());
    verify(kairosApiClient, times(1)).deleteScheduleJobs(any());

    reset(venueRepository);
  }

  @Test
  void testDeletePendingSchedulesWhenMultipleScheduleFFEnabled(Tenant tenant, Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*******.2")
      ApVersion apVersion) {
    String tenantId = tenant.getId();

    ScheduleTimeSlot scheduleTimeSlot = createScheduleTimeSlot();

    UpgradeSchedule prevSchedule = new UpgradeSchedule(randomId());
    prevSchedule.setStatus(UpgradeScheduleStatus.PENDING);
    prevSchedule.setTimeSlot(scheduleTimeSlot);
    prevSchedule.setVenue(venue);
    prevSchedule.setTenant(tenant);
    prevSchedule.setVersion(apVersion);
    scheduleRepository.save(prevSchedule);

    Venue venueWithoutSchedule = new Venue("venueWithoutSchedule");
    venueWithoutSchedule.setTenant(tenant);
    venueRepository.save(venueWithoutSchedule);

    List<String> venueIds = List.of(venueWithoutSchedule.getId(), venue.getId());
    extendedVenueServiceCtrl.deletePendingSchedules(tenantId, venueIds, true);

    verify(venueRepository, times(1)).findByIdInAndTenantId(venueIds, tenantId);
    verify(scheduleService, times(1)).deleteByIds(anyList());
    verify(kairosApiClient, times(1)).deleteScheduleJobs(any());

    reset(venueRepository);
  }

  @Test
  void testDeletePendingSchedules(Tenant tenant, Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*******.2")
      ApVersion apVersion) {
    String tenantId = tenant.getId();

    ScheduleTimeSlot scheduleTimeSlot = createScheduleTimeSlot();

    UpgradeSchedule prevSchedule = new UpgradeSchedule(randomId());
    prevSchedule.setStatus(UpgradeScheduleStatus.PENDING);
    prevSchedule.setTimeSlot(scheduleTimeSlot);
    prevSchedule.setTenant(tenant);
    prevSchedule.setVersion(apVersion);
    prevSchedule.setVenue(venue);
    scheduleRepository.save(prevSchedule);

    Venue venueWithoutSchedule = new Venue(randomId());
    venueWithoutSchedule.setTenant(tenant);
    venueRepository.save(venueWithoutSchedule);

    List<String> venueIds = List.of(venueWithoutSchedule.getId(), venue.getId());
    extendedVenueServiceCtrl.deletePendingSchedules(tenantId, venueIds, true);

    verify(venueRepository, times(1)).findByIdInAndTenantId(venueIds, tenantId);
    verify(scheduleService, times(1)).deleteByIds(anyList());
    verify(kairosApiClient, times(1)).deleteScheduleJobs(any());

    reset(venueRepository);
  }

  @Test
  public void testDeleteUpgradeScheduleModels(Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.555") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.1251") ApVersion version623) {
    String tenantId = venue.getTenant().getId();
    ScheduleTimeSlot timeSlot = createScheduleTimeSlot();
    UpgradeSchedule schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version623, timeSlot, s -> {});
    UpgradeScheduleFirmwareVersion usfvFor623 = UpgradeScheduleFirmwareVersionTestFixture
        .randomUpgradeScheduleFirmwareVersion(schedule, version623, List.of("R550", "R650"), c -> {});
    UpgradeScheduleFirmwareVersion usfvFor620 = UpgradeScheduleFirmwareVersionTestFixture
        .randomUpgradeScheduleFirmwareVersion(schedule, version620, List.of("R500", "R600", "R700"), c -> {});
    scheduleRepository.save(schedule);
    assertThat(scheduleRepository.findById(schedule.getId())).isPresent();
    upgradeScheduleFirmwareVersionRepository.saveAll(List.of(usfvFor623, usfvFor620));
    assertThat(upgradeScheduleFirmwareVersionRepository.findById(usfvFor620.getId())).isPresent();
    assertThat(upgradeScheduleFirmwareVersionRepository.findById(usfvFor623.getId())).isPresent();

    extendedVenueServiceCtrl.deleteUpgradeScheduleModels(tenantId, venue.getId(), List.of("R550", "R700"));

    assertThat(upgradeScheduleFirmwareVersionRepository.findByTenantId(tenantId))
        .hasSize(2)
        .flatExtracting(UpgradeScheduleFirmwareVersion::getTargetApModels)
        .containsExactlyInAnyOrder("R650", "R500", "R600");
  }

  @Test
  public void testDeleteUpgradeScheduleModels_whenOneOfScheduleVersionsHasEmptyTargetModels_deleteScheduleFwVersion(Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.555") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.1251") ApVersion version623) {
    String tenantId = venue.getTenant().getId();
    ScheduleTimeSlot timeSlot = createScheduleTimeSlot();
    UpgradeSchedule schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version623, timeSlot, s -> {});
    UpgradeScheduleFirmwareVersion usfvFor623 = UpgradeScheduleFirmwareVersionTestFixture
        .randomUpgradeScheduleFirmwareVersion(schedule, version623, List.of("R550", "R650"), c -> {});
    UpgradeScheduleFirmwareVersion usfvFor620 = UpgradeScheduleFirmwareVersionTestFixture
        .randomUpgradeScheduleFirmwareVersion(schedule, version620, List.of("R500", "R600", "R700"), c -> {});
    scheduleRepository.save(schedule);
    assertThat(scheduleRepository.findById(schedule.getId())).isPresent();
    upgradeScheduleFirmwareVersionRepository.saveAll(List.of(usfvFor623, usfvFor620));
    assertThat(upgradeScheduleFirmwareVersionRepository.findById(usfvFor620.getId())).isPresent();
    assertThat(upgradeScheduleFirmwareVersionRepository.findById(usfvFor623.getId())).isPresent();

    extendedVenueServiceCtrl.deleteUpgradeScheduleModels(tenantId, venue.getId(), List.of("R550", "R650", "R700"));

    assertThat(upgradeScheduleFirmwareVersionRepository.findByTenantId(tenantId))
        .hasSize(1)
        .singleElement()
        .extracting(UpgradeScheduleFirmwareVersion::getTargetApModels)
        .satisfies(models -> assertThat(models).containsExactlyInAnyOrder("R500", "R600"));
  }

  @Test
  public void testDeleteUpgradeScheduleModels_whenAllScheduleVersionsHasEmptyTargetModels_deleteSchedule(Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.555") ApVersion version620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.1251") ApVersion version623) {
    String tenantId = venue.getTenant().getId();
    ScheduleTimeSlot timeSlot = createScheduleTimeSlot();
    UpgradeSchedule schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version623, timeSlot, s -> {});
    UpgradeScheduleFirmwareVersion usfvFor623 = UpgradeScheduleFirmwareVersionTestFixture
        .randomUpgradeScheduleFirmwareVersion(schedule, version623, List.of("R500", "R600", "R700"), c -> {});
    UpgradeScheduleFirmwareVersion usfvFor620 = UpgradeScheduleFirmwareVersionTestFixture
        .randomUpgradeScheduleFirmwareVersion(schedule, version620, List.of("R550", "R650"), c -> {});
    scheduleRepository.save(schedule);
    assertThat(scheduleRepository.findById(schedule.getId())).isPresent();
    upgradeScheduleFirmwareVersionRepository.saveAll(List.of(usfvFor623, usfvFor620));
    assertThat(upgradeScheduleFirmwareVersionRepository.findById(usfvFor620.getId())).isPresent();
    assertThat(upgradeScheduleFirmwareVersionRepository.findById(usfvFor623.getId())).isPresent();

    extendedVenueServiceCtrl.deleteUpgradeScheduleModels(tenantId, venue.getId(),
        List.of("R550", "R650", "R500", "R600", "R700"));

    assertThat(upgradeScheduleFirmwareVersionRepository.findByTenantId(tenantId)).asList().hasSize(0);
    assertThat(scheduleRepository.findAllByVenueId(venue.getId())).asList().hasSize(0);
  }

  @Test
  public void testUpdateScheduleStatus(
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*******.2")
      ApVersion apVersion) {
    String tenantId = venue.getTenant().getId();
    ScheduleTimeSlot sts = createScheduleTimeSlot();

    UpgradeSchedule schedule = new UpgradeSchedule("scheduleId");
    schedule.setStatus(UpgradeScheduleStatus.PENDING);
    schedule.setTimeSlot(sts);
    schedule.setVersion(apVersion);
    scheduleRepository.save(schedule);

    extendedVenueServiceCtrl.updateScheduleStatus(tenantId, venue.getId(),
        UpgradeScheduleStatus.RUNNING);
    assertEquals(0, scheduleRepository.findAllByVenueId(venue.getId()).size());

    schedule.setVenue(venue);
    scheduleRepository.save(schedule);
    extendedVenueServiceCtrl.updateScheduleStatus(tenantId, venue.getId(),
        UpgradeScheduleStatus.RUNNING);
    assertEquals(UpgradeScheduleStatus.RUNNING,
        scheduleRepository.getReferenceById("scheduleId").getStatus());

    schedule = scheduleRepository.getReferenceById(schedule.getId());
    schedule.setStatus(UpgradeScheduleStatus.PENDING);
    scheduleRepository.save(schedule);
    extendedVenueServiceCtrl.updateScheduleStatus(tenantId, venue.getId(),
        UpgradeScheduleStatus.FINISHED);
    assertEquals(UpgradeScheduleStatus.FINISHED,
        scheduleRepository.getReferenceById("scheduleId").getStatus());
  }


  @Test
  void testFindVenuesByVenueIds(Tenant tenant) {
    // Given
    List<Venue> venueList = Generators.venue().setTenant(always(tenant)).generate(2);
    venueRepository.saveAll(venueList);

    List<String> venueIds = venueList.stream().map(AbstractBaseEntity::getId)
        .collect(Collectors.toList());

    // When
    List<Venue> actual = extendedVenueServiceCtrl.findVenuesByVenueIds(venueIds);

    // Then
    verify(venueRepository, times(1)).findByIdInAndTenantId(venueIds, tenant.getId());
    assertThat(actual).asList()
        .hasSize(venueIds.size())
        .extracting("id").containsAll(venueIds);
  }

  @Test
  void testFindByTenantIdAndIdIn(Tenant tenant) {
    // Given
    List<Venue> venueList = Generators.venue().setTenant(always(tenant)).generate(2);
    venueRepository.saveAll(venueList);

    List<String> venueIds = venueList.stream().map(AbstractBaseEntity::getId)
        .collect(Collectors.toList());

    // When
    List<Venue> actual = extendedVenueServiceCtrl.findVenuesByTenantIdAndVenueIds(tenant.getId(),
        venueIds);

    // Then
    verify(venueRepository, times(1)).findByIdInAndTenantId(venueIds, tenant.getId());
    assertThat(actual).asList()
        .hasSize(venueIds.size())
        .extracting("id").containsAll(venueIds);
  }

  @Test
  public void testGetVenueRogueAp_getDefaultVenueRogueAp_defaultVenueRogueApRetrievedSuccessfully(
      Venue venue)
      throws Exception {

    var defaultVenueRogueAp = new VenueRogueAp();
    var actualVenueRogueAp = extendedVenueServiceCtrl.getVenueRogueAp(venue.getId());

    //Then
    assertNotNull(actualVenueRogueAp);
    verifyVenueRogueAp(defaultVenueRogueAp, actualVenueRogueAp);
    assertNull(actualVenueRogueAp.getRoguePolicyId());
  }

  @Test
  public void testUpdateVenueRogueAp_updateVenueRogueAp_updateVenueRogueApSuccessfully(Venue venue)
      throws Exception {
    var venueId = venue.getId();
    var expectedVenueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();

    String defaultPolicyId = extendedVenueServiceCtrl.updateVenueRogueAp(venueId,
        expectedVenueRogueAp).getRoguePolicyId();
    expectedVenueRogueAp.setRoguePolicyId(defaultPolicyId);
    var actualVenueRogueAp = extendedVenueServiceCtrl.getVenueRogueAp(venue.getId());

    //Then
    assertNotNull(actualVenueRogueAp);
    verifyVenueRogueAp(expectedVenueRogueAp, actualVenueRogueAp);
    assertNotNull(actualVenueRogueAp.getRoguePolicyId());
  }

  @Test
  public void testUpdateVenueRogueAp_updateVenueRogueAp_disableByNullPolicyIdSuccessfully(
      Venue venue)
      throws Exception {

    //Given
    var venueId = venue.getId();
    var expectedVenueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();

    RogueClassificationPolicy policy = new RogueClassificationPolicy();
    RogueClassificationPolicyRule rule = new RogueClassificationPolicyRule();
    rule.setType(RogueRuleTypeEnum.AdhocRule);
    rule.setClassification(RogueClassificationEnum.Ignored);
    rule.setName("rule");
    rule.setPriority(1);
    policy.setName("policy");
    policy.setRules(Lists.newArrayList(rule));

    //When
    policy = rogueApPolicyProfileServiceCtrl.addRogueApPolicyProfile(policy);
    expectedVenueRogueAp.setRoguePolicyId(policy.getId());
    extendedVenueServiceCtrl.updateVenueRogueAp(venueId, expectedVenueRogueAp);

    VenueRogueAp venueRogueAp = new VenueRogueAp();
    venueRogueAp.setEnabled(false);
    venueRogueAp.setReportThreshold((short) 10);
    venueRogueAp.setRoguePolicyId("xxx");
    extendedVenueServiceCtrl.updateVenueRogueAp(venueId, venueRogueAp);
    VenueRogueAp actualVenueRogueAp = extendedVenueServiceCtrl.getVenueRogueAp(venueId);

    //Then
    assertNotNull(actualVenueRogueAp);
    assertFalse(actualVenueRogueAp.getEnabled());
    assertEquals(10, actualVenueRogueAp.getReportThreshold().intValue());
    assertNull(actualVenueRogueAp.getRoguePolicyId());
  }

  @Test
  public void testUpdateVenueRogueAp_updateVenueRogueAp_setPolicyIdSuccessfully(Venue venue)
      throws Exception {
    //Given
    var venueId = venue.getId();
    var expectedVenueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();
    RogueClassificationPolicy policy = new RogueClassificationPolicy();
    RogueClassificationPolicyRule rule = new RogueClassificationPolicyRule();
    rule.setType(RogueRuleTypeEnum.AdhocRule);
    rule.setClassification(RogueClassificationEnum.Ignored);
    rule.setName("rule");
    rule.setPriority(1);
    policy.setName("policy");
    policy.setRules(Lists.newArrayList(rule));

    //When
    //Set to default policy
    expectedVenueRogueAp = extendedVenueServiceCtrl.updateVenueRogueAp(venueId,
        expectedVenueRogueAp);

    policy = rogueApPolicyProfileServiceCtrl.addRogueApPolicyProfile(policy);
    expectedVenueRogueAp.setRoguePolicyId(policy.getId());
    extendedVenueServiceCtrl.updateVenueRogueAp(venueId, expectedVenueRogueAp);

    VenueRogueAp actualVenueRogueAp = extendedVenueServiceCtrl.getVenueRogueAp(venueId);

    //Then
    assertNotNull(actualVenueRogueAp);
    verifyVenueRogueAp(expectedVenueRogueAp, actualVenueRogueAp);
    assertEquals(policy.getId(), actualVenueRogueAp.getRoguePolicyId());
  }

  @Test
  public void testUpdateVenueRogueAp_updateVenueRogueApWithNonExistedPolicy_failed(Venue venue) {
    var venueId = venue.getId();
    var venueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();
    venueRogueAp.setRoguePolicyId("00000");

    assertThrows(ObjectNotFoundException.class,
        () -> extendedVenueServiceCtrl.updateVenueRogueAp(venueId, venueRogueAp)
    );
  }

  @Test
  public void testUpdateVenueRogueAp_updateVenueRogueApWithEmptyPolicyId_failed(Venue venue) {
    var venueId = venue.getId();
    var venueRogueAp = VenueTestFixture.venueRogueApWithDefaultProfile();
    venueRogueAp.setRoguePolicyId(null);

    CommonException exception = assertThrows(CommonException.class,
        () -> extendedVenueServiceCtrl.updateVenueRogueAp(venueId, venueRogueAp)
    );

    assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
  }

  private void verifyVenueRogueAp(VenueRogueAp expectedVenueRogueAp,
      VenueRogueAp originalVenueRogueAp) {
    assertEquals(expectedVenueRogueAp.getEnabled(),
        originalVenueRogueAp.getEnabled());

    assertEquals(expectedVenueRogueAp.getReportThreshold(),
        originalVenueRogueAp.getReportThreshold());

    assertEquals(expectedVenueRogueAp.getRoguePolicyId(),
        originalVenueRogueAp.getRoguePolicyId());
  }

  @Test
  void testGetVenueClientAdmissionControlSettings(Venue venue) throws Exception {
    // Get the default settings as radio 2.4g disable and radio 5g disable
    var result = extendedVenueServiceCtrl.getVenueClientAdmissionControlSettings(venue.getId());
    assertFalse(result.getEnable24G());
    assertFalse(result.getEnable50G());
    assertEquals((short) 10, result.getMinClientCount24G());
    assertEquals((short) 20, result.getMinClientCount50G());
    assertEquals((short) 75, result.getMaxRadioLoad24G());
    assertEquals((short) 75, result.getMaxRadioLoad50G());
    assertEquals((short) 0, result.getMinClientThroughput24G());
    assertEquals((short) 0, result.getMinClientThroughput50G());
  }

  @Test
  void testUpdateVenueClientAdmissionControlSettings(Venue venue) throws Exception {
    // Disable load balancing and band balancing
    VenueLoadBalancing venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(false);
    venueLoadBalancing.setBandBalancingEnabled(false);
    extendedVenueServiceCtrl.updateVenueLoadBalancing(
        venue.getId(), venueLoadBalancing);

    // Given radio 2.4g enable and radio 5g disable
    VenueClientAdmissionControl venueClientAdmissionControl1 = new VenueClientAdmissionControl();
    venueClientAdmissionControl1.setEnable24G(true);
    venueClientAdmissionControl1.setMinClientCount24G((short) 11);
    venueClientAdmissionControl1.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl1.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl1.setEnable50G(false);

    // When
    extendedVenueServiceCtrl.updateVenueClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl1);

    // Then
    var result = extendedVenueServiceCtrl.getVenueClientAdmissionControlSettings(venue.getId());
    assertTrue(result.getEnable24G());
    assertFalse(result.getEnable50G());
    assertEquals((short) 11, result.getMinClientCount24G());
    assertEquals((short) 20, result.getMinClientCount50G());
    assertEquals((short) 50, result.getMaxRadioLoad24G());
    assertEquals((short) 75, result.getMaxRadioLoad50G());
    assertEquals((short) 10, result.getMinClientThroughput24G());
    assertEquals((short) 0, result.getMinClientThroughput50G());

    // Given radio 2.4g disable and radio 5g enable
    VenueClientAdmissionControl venueClientAdmissionControl2 = new VenueClientAdmissionControl();
    venueClientAdmissionControl2.setEnable24G(false);
    venueClientAdmissionControl2.setEnable50G(true);
    venueClientAdmissionControl2.setMinClientCount50G((short) 21);
    venueClientAdmissionControl2.setMaxRadioLoad50G((short) 50);
    venueClientAdmissionControl2.setMinClientThroughput50G((short) 10);

    // When
    extendedVenueServiceCtrl.updateVenueClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl2);

    // Then
    result = extendedVenueServiceCtrl.getVenueClientAdmissionControlSettings(venue.getId());
    assertFalse(result.getEnable24G());
    assertTrue(result.getEnable50G());
    assertEquals((short) 10, result.getMinClientCount24G());
    assertEquals((short) 21, result.getMinClientCount50G());
    assertEquals((short) 75, result.getMaxRadioLoad24G());
    assertEquals((short) 50, result.getMaxRadioLoad50G());
    assertEquals((short) 0, result.getMinClientThroughput24G());
    assertEquals((short) 10, result.getMinClientThroughput50G());

    // Given radio 2.4g disable and radio 5g disable
    VenueClientAdmissionControl venueClientAdmissionControl3 = new VenueClientAdmissionControl();
    venueClientAdmissionControl3.setEnable24G(false);
    venueClientAdmissionControl3.setEnable50G(false);

    // When
    extendedVenueServiceCtrl.updateVenueClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl3);

    // Then
    result = extendedVenueServiceCtrl.getVenueClientAdmissionControlSettings(venue.getId());
    assertFalse(result.getEnable24G());
    assertFalse(result.getEnable50G());
    assertEquals((short) 10, result.getMinClientCount24G());
    assertEquals((short) 20, result.getMinClientCount50G());
    assertEquals((short) 75, result.getMaxRadioLoad24G());
    assertEquals((short) 75, result.getMaxRadioLoad50G());
    assertEquals((short) 0, result.getMinClientThroughput24G());
    assertEquals((short) 0, result.getMinClientThroughput50G());

    // Given radio 2.4g enable and radio 5g enable
    VenueClientAdmissionControl venueClientAdmissionControl4 = new VenueClientAdmissionControl();
    venueClientAdmissionControl4.setEnable24G(true);
    venueClientAdmissionControl4.setMinClientCount24G((short) 11);
    venueClientAdmissionControl4.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl4.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl4.setEnable50G(true);
    venueClientAdmissionControl4.setMinClientCount50G((short) 21);
    venueClientAdmissionControl4.setMaxRadioLoad50G((short) 50);
    venueClientAdmissionControl4.setMinClientThroughput50G((short) 10);

    // When
    extendedVenueServiceCtrl.updateVenueClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl4);

    // Then
    result = extendedVenueServiceCtrl.getVenueClientAdmissionControlSettings(venue.getId());
    assertTrue(result.getEnable24G());
    assertTrue(result.getEnable50G());
    assertEquals((short) 11, result.getMinClientCount24G());
    assertEquals((short) 21, result.getMinClientCount50G());
    assertEquals((short) 50, result.getMaxRadioLoad24G());
    assertEquals((short) 50, result.getMaxRadioLoad50G());
    assertEquals((short) 10, result.getMinClientThroughput24G());
    assertEquals((short) 10, result.getMinClientThroughput50G());

    // Given radio 2.4g enable and radio 5g disable with invalid setting
    VenueClientAdmissionControl venueClientAdmissionControl5 = new VenueClientAdmissionControl();
    venueClientAdmissionControl5.setEnable24G(true);
    venueClientAdmissionControl5.setMinClientCount24G((short) 11);
    venueClientAdmissionControl5.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl5.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl5.setEnable50G(false);
    venueClientAdmissionControl5.setMinClientCount50G((short) 21);
    venueClientAdmissionControl5.setMaxRadioLoad50G((short) 50);
    venueClientAdmissionControl5.setMinClientThroughput50G((short) 10);

    // When
    extendedVenueServiceCtrl.updateVenueClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl5);

    // Then
    result = extendedVenueServiceCtrl.getVenueClientAdmissionControlSettings(venue.getId());
    assertTrue(result.getEnable24G());
    assertFalse(result.getEnable50G());
    assertEquals((short) 11, result.getMinClientCount24G());
    assertEquals((short) 20, result.getMinClientCount50G());
    assertEquals((short) 50, result.getMaxRadioLoad24G());
    assertEquals((short) 75, result.getMaxRadioLoad50G());
    assertEquals((short) 10, result.getMinClientThroughput24G());
    assertEquals((short) 0, result.getMinClientThroughput50G());
  }

  @Test
  void testUpdateVenueLoadBalancingSettingsWhenVenueClientAdmissionControlEnabled(Venue venue)
      throws Exception {
    // Disable load balancing
    VenueLoadBalancing venueLoadBalancing1 = new VenueLoadBalancing();
    venueLoadBalancing1.setEnabled(false);
    venueLoadBalancing1.setBandBalancingEnabled(false);
    extendedVenueServiceCtrl.updateVenueLoadBalancing(venue.getId(), venueLoadBalancing1);

    // Enable client admission control
    VenueClientAdmissionControl venueClientAdmissionControl = new VenueClientAdmissionControl();
    venueClientAdmissionControl.setEnable24G(true);
    venueClientAdmissionControl.setMinClientCount24G((short) 11);
    venueClientAdmissionControl.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl.setEnable50G(false);
    extendedVenueServiceCtrl.updateVenueClientAdmissionControlSettings(
        venue.getId(), venueClientAdmissionControl);

    // Given load balancing enable
    VenueLoadBalancing venueLoadBalancing2 = new VenueLoadBalancing();
    venueLoadBalancing2.setEnabled(true);
    venueLoadBalancing2.setBandBalancingEnabled(false);

    assertThrows(
        CommonException.class,
        () ->
            extendedVenueServiceCtrl.updateVenueLoadBalancing(venue.getId(), venueLoadBalancing2));
  }

  @Test
  void testUpdateVenueClientAdmissionControlSettingsWhenVenueLoadBalancingEnabled(Venue venue)
      throws Exception {
    // Enable load balancing
    VenueLoadBalancing venueLoadBalancing = new VenueLoadBalancing();
    venueLoadBalancing.setEnabled(true);
    venueLoadBalancing.setBandBalancingEnabled(false);
    extendedVenueServiceCtrl.updateVenueLoadBalancing(venue.getId(), venueLoadBalancing);

    // Given radio 2.4g enable and radio 5g disable
    VenueClientAdmissionControl venueClientAdmissionControl = new VenueClientAdmissionControl();
    venueClientAdmissionControl.setEnable24G(true);
    venueClientAdmissionControl.setMinClientCount24G((short) 11);
    venueClientAdmissionControl.setMaxRadioLoad24G((short) 50);
    venueClientAdmissionControl.setMinClientThroughput24G((short) 10);
    venueClientAdmissionControl.setEnable50G(false);

    assertThrows(
        CommonException.class,
        () ->
            extendedVenueServiceCtrl.updateVenueClientAdmissionControlSettings(
                venue.getId(), venueClientAdmissionControl));
  }

  @Test
  void testGetVenueApIpModeSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApIpModeSettings(venue.getId());
    assertEquals(IpModeEnum.IPV4, result.getMode());

    venue.setDeviceIpMode(IpModeEnum.IPV4_IPV6);
    venueRepository.save(venue);

    result = extendedVenueServiceCtrl.getVenueApIpModeSettings(venue.getId());
    assertEquals(IpModeEnum.IPV4_IPV6, result.getMode());
  }

  @Test
  void testUpdateVenueApIpModeSettings(Venue venue) throws Exception {
    IpMode venueDeviceIpMode = new IpMode();
    venueDeviceIpMode.setMode(IpModeEnum.IPV6);
    extendedVenueServiceCtrl.updateVenueApIpModeSettings(venue.getId(), venueDeviceIpMode);

    var result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId());
    assertEquals(IpModeEnum.IPV6, result.getDeviceIpMode());

    venueDeviceIpMode.setMode(IpModeEnum.IPV4_IPV6);
    extendedVenueServiceCtrl.updateVenueApIpModeSettings(venue.getId(), venueDeviceIpMode);

    result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId());
    assertEquals(IpModeEnum.IPV4_IPV6, result.getDeviceIpMode());
  }

  @Test
  void testGetVenueApTlsKeyEnhancedModeSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApTlsKeyEnhancedModeSettings(venue.getId());
    assertFalse(result.getTlsKeyEnhancedModeEnabled());

    venue.setTlsKeyEnhancedModeEnabled(true);
    venueRepository.save(venue);

    result = extendedVenueServiceCtrl.getVenueApTlsKeyEnhancedModeSettings(venue.getId());
    assertTrue(result.getTlsKeyEnhancedModeEnabled());
  }

  @Test
  void testUpdateVenueApTlsKeyEnhancedModeSettings(Venue venue) throws Exception {
    TlsKeyEnhancedMode tlsKeyEnhancedMode = new TlsKeyEnhancedMode();
    tlsKeyEnhancedMode.setTlsKeyEnhancedModeEnabled(true);
    extendedVenueServiceCtrl.updateVenueApTlsKeyEnhancedModeSettings(venue.getId(), tlsKeyEnhancedMode);

    var result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId());
    assertTrue(result.getTlsKeyEnhancedModeEnabled());

    tlsKeyEnhancedMode.setTlsKeyEnhancedModeEnabled(false);
    extendedVenueServiceCtrl.updateVenueApTlsKeyEnhancedModeSettings(venue.getId(), tlsKeyEnhancedMode);

    result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId());
    assertFalse(result.getTlsKeyEnhancedModeEnabled());
  }

  @Test
  void testGetVenueApRebootTimeoutSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApRebootTimeoutSettings(venue.getId());
    assertEquals(1800, result.getGatewayLossTimeout().intValue());
    assertEquals(7200, result.getServerLossTimeout().intValue());

    VenueApRebootTimeout venueApRebootTimeout = new VenueApRebootTimeout();
    venueApRebootTimeout.setGatewayLossTimeout(1800);
    venueApRebootTimeout.setServerLossTimeout(7200);
    venue.setRebootTimeout(venueApRebootTimeout);
    venueRepository.save(venue);

    result = extendedVenueServiceCtrl.getVenueApRebootTimeoutSettings(venue.getId());
    assertEquals(1800, result.getGatewayLossTimeout().intValue());
    assertEquals(7200, result.getServerLossTimeout().intValue());
  }

  @Test
  void testUpdateVenueApRebootTimeoutSettings(Venue venue) throws Exception {
    var result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId()).getRebootTimeout();
    assertEquals(1800, result.getGatewayLossTimeout().intValue());
    assertEquals(7200, result.getServerLossTimeout().intValue());

    VenueApRebootTimeout venueApRebootTimeout = new VenueApRebootTimeout();
    venueApRebootTimeout.setGatewayLossTimeout(1800);
    venueApRebootTimeout.setServerLossTimeout(7200);
    extendedVenueServiceCtrl.updateVenueApRebootTimeoutSettings(venue.getId(), venueApRebootTimeout);

    result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId()).getRebootTimeout();
    assertEquals(1800, result.getGatewayLossTimeout().intValue());
    assertEquals(7200, result.getServerLossTimeout().intValue());
  }

  @Test
  void testGetVenueApSmartMonitorSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApSmartMonitorSettings(venue.getId());
    assertFalse(result.getEnabled());
    assertEquals(10, result.getInterval().intValue());
    assertEquals(3, result.getThreshold().intValue());

    VenueApSmartMonitor venueApSmartMonitor = new VenueApSmartMonitor();
    venueApSmartMonitor.setEnabled(true);
    venueApSmartMonitor.setInterval((short) 20);
    venueApSmartMonitor.setThreshold((short) 8);
    venue.setSmartMonitor(venueApSmartMonitor);
    venueRepository.save(venue);

    result = extendedVenueServiceCtrl.getVenueApSmartMonitorSettings(venue.getId());
    assertTrue(result.getEnabled());
    assertEquals(20, result.getInterval().intValue());
    assertEquals(8, result.getThreshold().intValue());
  }

  @Test
  void testUpdateVenueApSmartMonitorSettings(Venue venue) throws Exception {
    var result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId()).getSmartMonitor();
    assertFalse(result.getEnabled());
    assertEquals(10, result.getInterval().intValue());
    assertEquals(3, result.getThreshold().intValue());

    VenueApSmartMonitor venueApSmartMonitor = new VenueApSmartMonitor();
    venueApSmartMonitor.setEnabled(true);
    venueApSmartMonitor.setInterval((short) 20);
    venueApSmartMonitor.setThreshold((short) 8);
    extendedVenueServiceCtrl.updateVenueApSmartMonitorSettings(venue.getId(), venueApSmartMonitor);

    result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId()).getSmartMonitor();
    assertTrue(result.getEnabled());
    assertEquals(20, result.getInterval().intValue());
    assertEquals(8, result.getThreshold().intValue());
  }

  @Test
  void testGetVenueApIotSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApIotSettings(venue.getId());
    assertNotNull(result);
    assertFalse(result.getEnabled());
    assertNull(result.getMqttBrokerAddress());

    var venueApIotSettings = new VenueApIotSettings();
    venueApIotSettings.setEnabled(true);
    venueApIotSettings.setMqttBrokerAddress("*************");
    venue.setIotSettings(venueApIotSettings);
    venueRepository.save(venue);

    result = extendedVenueServiceCtrl.getVenueApIotSettings(venue.getId());
    assertTrue(result.getEnabled());
    assertEquals("*************", result.getMqttBrokerAddress());
  }

  @Test
  void testUpdateVenueApIotSettings(Venue venue) throws Exception {
    var result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId()).getIotSettings();
    assertNotNull(result);
    assertFalse(result.getEnabled());
    assertNull(result.getMqttBrokerAddress());

    // case 1 - enable w/ address
    var venueApIotSettings = new VenueApIotSettings();
    venueApIotSettings.setEnabled(true);
    venueApIotSettings.setMqttBrokerAddress("*************");
    venue.setIotSettings(venueApIotSettings);
    extendedVenueServiceCtrl.updateVenueApIotSettings(venue.getId(), venueApIotSettings);

    result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId()).getIotSettings();
    assertTrue(result.getEnabled());
    assertEquals("*************", result.getMqttBrokerAddress());

    // case 2 - disable w/ address
    venueApIotSettings.setEnabled(false);
    venue.setIotSettings(venueApIotSettings);
    extendedVenueServiceCtrl.updateVenueApIotSettings(venue.getId(), venueApIotSettings);

    result = venueRepository.getByIdAndTenantId(venue.getId(), venue.getTenant().getId()).getIotSettings();
    assertFalse(result.getEnabled());
    assertNull(result.getMqttBrokerAddress());
  }

  @Test
  void testDeleteVenueWithSchedule(
      Tenant tenant,
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*******.2")
      ApVersion apVersion)
      throws Exception {

    ScheduleTimeSlot sts = createScheduleTimeSlot();

    UpgradeSchedule schedule = new UpgradeSchedule("scheduleId");
    schedule.setStatus(UpgradeScheduleStatus.PENDING);
    schedule.setTimeSlot(sts);
    schedule.setVersion(apVersion);
    schedule.setVenue(venue);
    scheduleRepository.save(schedule);

    List<UpgradeSchedule> schedules = scheduleRepository.findAllByVenueId(venue.getId());
    assertThat(schedules).isNotNull();
    assertEquals(1, schedules.size());

    extendedVenueServiceCtrl.deleteVenue(venue);

    verifyDeleteVenueWithSchedule(tenant, venue);

    reset(venueRepository);
  }

  @Test
  void testDeleteVenueWithMultipleSchedules(
      Tenant tenant,
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*******.2")
      ApVersion apVersion)
      throws Exception {

    ScheduleTimeSlot sts = createScheduleTimeSlot();

    UpgradeSchedule schedule = new UpgradeSchedule("scheduleId");
    schedule.setStatus(UpgradeScheduleStatus.PENDING);
    schedule.setTimeSlot(sts);
    schedule.setVersion(apVersion);
    schedule.setVenue(venue);
    scheduleRepository.save(schedule);

    UpgradeSchedule schedule2 = new UpgradeSchedule("scheduleId2");
    schedule2.setStatus(UpgradeScheduleStatus.RUNNING);
    schedule2.setTimeSlot(sts);
    schedule2.setVersion(apVersion);
    schedule2.setVenue(venue);
    scheduleRepository.save(schedule2);

    List<UpgradeSchedule> schedules = scheduleRepository.findAllByVenueId(venue.getId());
    assertThat(schedules).isNotNull();
    assertEquals(2, schedules.size());

    extendedVenueServiceCtrl.deleteVenue(venue);

    verifyDeleteVenueWithSchedule(tenant, venue);

    reset(venueRepository);
  }

  @Test
  void testDeleteVenueWithLanPortAdoptions(Tenant tenant, Venue venue, ClientIsolationAllowlist allowlist) throws Exception {
    // Unused LanPortAdoption
    dataHelper.createLanPortAdoption(tenant, dataHelper.createEthernetPortProfile(tenant, 600));
    dataHelper.createLanPortAdoption(
        tenant,
        dataHelper.createEthernetPortProfile(tenant, 601),
        of(dataHelper.createSoftGreProfile(tenant), dataHelper.createIpsecProfile(tenant)));
    dataHelper.createLanPortAdoption(
        tenant,
        dataHelper.createEthernetPortProfile(tenant, 602),
        of(new ClientIsolationLanPortActivation()));
    // The venue used LanPortAdoption
    dataHelper.createVenueLanPortDataWithAdoption(venue, "TEST-01", "1", 700);
    dataHelper.createVenueLanPortDataWithAdoption(
        venue, "TEST-02", "1", 701, of(dataHelper.createSoftGreProfile(tenant)));
    var activationWithAllowlist = new ClientIsolationLanPortActivation();
    activationWithAllowlist.setClientIsolationAllowlist(allowlist);
    dataHelper.createVenueLanPortDataWithAdoption(
        venue, "TEST-03", "1", 702, of(activationWithAllowlist));

    extendedVenueServiceCtrl.deleteVenue(venueRepository.save(venue));

    assertTrue(repositoryUtil.findAll(LanPortAdoption.class, tenant.getId()).isEmpty());
    assertTrue(
        repositoryUtil.findAll(SoftGreProfileLanPortActivation.class, tenant.getId()).isEmpty());
    assertTrue(
        repositoryUtil.findAll(ClientIsolationLanPortActivation.class, tenant.getId()).isEmpty());
  }

  private ScheduleTimeSlot createScheduleTimeSlot() {
    ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
    Instant startTime = Instant.now();
    sts.setStartDateTime(Date.from(startTime));
    sts.setEndDateTime(Date.from(startTime.plusSeconds(60 * 60 * 2)));
    sts.setTotalCapacityVenue(2500);
    timeSlotRepository.save(sts);
    return sts;
  }

  private void verifyDeleteVenueWithSchedule(Tenant tenant, Venue venue) {
    assertFalse(venueRepository.findByIdAndTenantId(venue.getId(), tenant.getId()).isPresent());
    List<UpgradeSchedule> schedules = scheduleRepository.findAllByVenueId(venue.getId());
    assertThat(schedules).isNotNull();
    assertEquals(0, schedules.size());
  }

  @TestConfiguration
  @Import({
      MockGrpcBeanConfiguration.class,
      FirmwareCapabilityServiceTestConfig.class,
      ExtendedVenueServiceCtrlImplTestConfig.class,
      ExtendedNetworkVenueServiceCtrlImplTestConfig.class,
      ExtendedApGroupServiceCtrlImplTestConfig.class,
      InitVenueServiceImplTestConfig.class,
      SoftGreProfileLanPortActivationMapperImpl.class,
      ClientIsolationLanPortActivationMapperImpl.class,
      DhcpOption82LanPortActivationMapperImpl.class,
      SoftGreProfileLanPortActivationHandler.class,
      ClientIsolationLanPortActivationHandler.class,
      DhcpOption82LanPortActivationHandler.class,
      LanPortAdoptionServiceImpl.class
  })
  static class TestConfig {

    @Bean
    @Primary
    VenueFirmwareVersionService venueFirmwareVersionService(
        VenueFirmwareVersionRepository venueFirmwareVersionRepository) {
      return new VenueFirmwareVersionServiceImpl(venueFirmwareVersionRepository);
    }

    @Bean
    TenantFirmwareVersionService tenantFirmwareVersionService(
        TenantFirmwareVersionRepository tenantFirmwareVersionRepository) {
      return new TenantFirmwareVersionServiceImpl(tenantFirmwareVersionRepository);
    }

    @ConditionalOnMissingBean
    @Bean
    ApBranchFamilyServiceRouter apBranchFamilyService() {
      return mock(ApBranchFamilyServiceRouter.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public ScheduleTimeSlotService scheduleTimeSlotService(
        ScheduleTimeSlotRepository scheduleTimeSlotRepository,
        RksTransactionHelper rksTransactionHelper) {
      return new ScheduleTimeSlotServiceImpl(scheduleTimeSlotRepository, rksTransactionHelper);
    }

    @Bean
    @ConditionalOnMissingBean
    public FirmwareManagementService firmwareManagementService(
        TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository,VenueRepository venueRepository,
        VenueCurrentFirmwareRepository venueCurrentFirmwareRepository,UpgradeScheduleRepository upgradeScheduleRepository,
        ApRepository apRepository,ApVersionRepository apVersionRepository,
        ApModelMinimumFirmwareRepository apModelMinimumFirmwareRepository, VenueClient venueClient,
        FeatureFlagService featureFlagService, CloudSupportApModels cloudSupportApModels,
        UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository,
        ApModelGreenfieldFirmwareRepository apModelGreenfieldFirmwareRepository,
        TenantService tenantService,
        TenantAvailableApFirmwareService tenantAvailableApFirmwareService,
        TenantCurrentFirmwareService tenantCurrentFirmwareService,
        WifiSchedulePublisher wifiSchedulePublisher,
        TenantFirmwareNotificationSender tenantFirmwareNotificationSender,
        ExtendedVenueServiceCtrl extendedVenueServiceCtrl,
        ApUpgradeService apUpgradeService,
        ScopeDataService scopeDataService) {
      return new FirmwareManagementServiceImpl(tenantAvailableApFirmwareRepository,venueRepository,
          venueCurrentFirmwareRepository, upgradeScheduleRepository, apRepository, apVersionRepository,
          apModelMinimumFirmwareRepository, venueClient, featureFlagService, cloudSupportApModels,
          upgradeScheduleFirmwareVersionRepository, apModelGreenfieldFirmwareRepository,
          tenantService, tenantAvailableApFirmwareService, tenantCurrentFirmwareService, wifiSchedulePublisher,
          tenantFirmwareNotificationSender, extendedVenueServiceCtrl, apUpgradeService, scopeDataService);
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantService tenantService(TenantRepository tenantRepository) {
      return new TenantServiceImpl(tenantRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantAvailableApFirmwareService tenantAvailableApFirmwareService(
        TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository) {
      return new TenantAvailableApFirmwareServiceImpl(tenantAvailableApFirmwareRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantCurrentFirmwareService tenantCurrentFirmwareService(
        TenantCurrentFirmwareRepository tenantCurrentFirmwareRepository,
        ApModelGreenfieldFirmwareRepository apModelGreenfieldFirmwareRepository,
        TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository,
        ApVersionRepository apVersionRepository) {
      return new TenantCurrentFirmwareServiceImpl(
          tenantCurrentFirmwareRepository, apModelGreenfieldFirmwareRepository,
          tenantAvailableApFirmwareRepository, apVersionRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public VenueApCountExecutorFactory venueApCountExecutorFactory(UpgradeScheduleRepository upgradeScheduleRepository,
        ApRepository apRepository, ApVersionService apVersionService) {
      return new VenueApCountExecutorFactory(upgradeScheduleRepository, apRepository, apVersionService);
    }

    @Bean
    @ConditionalOnMissingBean
    public VenueTemplateConverter venueTemplateConverter(ScheduleTimeSlotRepository scheduleTimeSlotRepository,
        VenueTemplateDtoBuilderHelper venueTemplateDtoBuilderHelper,
        VenueApCountExecutorFactory apCountExecutorFactory) {
      return new VenueTemplateConverterImpl(scheduleTimeSlotRepository,
          venueTemplateDtoBuilderHelper,
          apCountExecutorFactory);
    }
    @Bean
    @ConditionalOnMissingBean
    public VenueTemplateDtoBuilderHelper venueTemplateDtoBuilderHelper() {
      return new VenueTemplateDtoBuilderHelper();
    }

    @Bean
    @Primary
    public UpgradeScheduleService upgradeScheduleService(
        UpgradeScheduleRepository upgradeScheduleRepository,
        ApBranchFamilyServiceRouter apBranchFamilyService,
        UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository,
        ApVersionRepository apVersionRepository,
        VenueCurrentFirmwareRepository venueCurrentFirmwareRepository) {
      return new UpgradeScheduleServiceImpl(upgradeScheduleRepository, apBranchFamilyService,
          upgradeScheduleFirmwareVersionRepository, apVersionRepository, venueCurrentFirmwareRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    LanPortAdoptionDataHelper dataHelper(
        RepositoryUtil repositoryUtil, LanPortAdoptionServiceImpl lanPortAdoptionService) {
      return new LanPortAdoptionDataHelper(repositoryUtil, lanPortAdoptionService);
    }
  }
  @Test
  public void testSkipSchedule(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*******.111") ApVersion scheduleVer) {
    // Given
    String localStartDateTime = "2022-08-04 08:00:00";
    Venue venue = createVenueAndSetSchedule(tenant, scheduleVer, "Asia/Taipei", localStartDateTime);
    assertNotNull(venue);
    assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(1);
    // When
    extendedVenueServiceCtrl.skipSchedule("batchId", venue.getTenant().getId(), List.of(venue.getId()));

    // Then
    Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
    assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(0);

    List<VenueApFirmwareBatchOperation> operations =
        venueApFirmwareBatchOperationRepository.findAllByTenantIdAndBatchId(tenant.getId(), "batchId");
    assertThat(operations).hasSize(1).element(0).matches(
        op -> op.getVenue().getId().equals(venue.getId()));
  }

  @Test
  public void testSkipScheduleWithScheduleFirmwareVersions(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*******.111") ApVersion scheduleVer) {
    // Given
    String localStartDateTime = "2022-08-04 08:00:00";
    Venue venue = createVenueAndSetSchedule(tenant, scheduleVer, "Asia/Taipei", localStartDateTime);
    UpgradeSchedule schedule = upgradeScheduleRepository.findAllByVenueId(venue.getId()).get(0);
    createUpgradeScheduleFirmwareVersion(schedule, scheduleVer);

    assertNotNull(venue);
    assertNotNull(schedule);
    assertNotNull(schedule.getUpgradeScheduleFirmwareVersions());

    // When
    extendedVenueServiceCtrl.skipSchedule("batchId", venue.getTenant().getId(), List.of(venue.getId()));

    // Then
    assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(0);

    List<UpgradeScheduleFirmwareVersion> scheduleFirmwares = repositoryUtil.findAll(UpgradeScheduleFirmwareVersion.class, tenant.getId());
    assertEquals(0, scheduleFirmwares.size());

    List<VenueApFirmwareBatchOperation> operations =
        venueApFirmwareBatchOperationRepository.findAllByTenantIdAndBatchId(tenant.getId(), "batchId");
    assertThat(operations).hasSize(1).element(0).matches(
        op -> op.getTargetApVersionList().stream().map(ApVersion::getId).toList().contains(scheduleVer.getId()));
  }

  @Test
  public void testChangeScheduleWithSkipLatestFirmwares(Tenant tenant, Venue venue,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.6139") ApVersion venueApVersion1,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.6130") ApVersion venueApVersion2,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.6135") ApVersion targetApVerssion)
      throws Exception {
    // Given
    venueApVersion1.setSupportedApModels(List.of("R750", "R760"));
    venueApVersion2.setSupportedApModels(List.of("R760"));
    targetApVerssion.setSupportedApModels(List.of("R760"));
    apVersionRepository.saveAll(List.of(venueApVersion1, venueApVersion2, targetApVerssion));

    List<ApModelFirmware> targetFirmwares = List.of(
        ApModelFirmware.builder().apModel("R750").firmware(venueApVersion1.getId()).build(),
        ApModelFirmware.builder().apModel("R760").firmware(targetApVerssion.getId()).build());

    var date = new java.util.Date();
    var venuesCurrentFirmware1 = new VenueCurrentFirmware();
    venuesCurrentFirmware1.setFirmware(venueApVersion1);
    venuesCurrentFirmware1.setApModel(venueApVersion1.getSupportedApModels().get(0));
    venuesCurrentFirmware1.setUpdatedDate(date);
    var venuesCurrentFirmware2 = new VenueCurrentFirmware();
    venuesCurrentFirmware2.setFirmware(venueApVersion2);
    venuesCurrentFirmware2.setApModel(venueApVersion2.getSupportedApModels().get(0));
    venuesCurrentFirmware2.setUpdatedDate(date);

    doReturn(List.of(venuesCurrentFirmware1, venuesCurrentFirmware2))
        .when(venueCurrentFirmwareRepository).findByTenantIdAndVenueId(tenant.getId(), venue.getId());
    doReturn(List.of(newApModelVersionProjection(venueApVersion1.getSupportedApModels().get(0), venueApVersion1.getId()),
        newApModelVersionProjection(venueApVersion1.getSupportedApModels().get(1), venueApVersion1.getId())))
        .when(tenantAvailableApFirmwareRepository).getModelLatestSupportedRecommendedVersionByTenantId(tenant.getId());

    // When
    extendedVenueServiceCtrl.updateScheduleByApModel(
        "batchId", tenant.getId(), venue, "2022-08-04", "22:00-00:00", targetFirmwares);

    // Then
    verifyScheduleTimeSlotResult(venue.getId(), 4, 22);
    List<UpgradeSchedule> schedules = upgradeScheduleRepository.findAllByVenueId(venue.getId());
    assertThat(schedules).isNotNull().hasSize(1).element(0)
        .matches(op -> op.getUpgradeScheduleFirmwareVersions().get(0).getApFirmwareVersion().getId().equals("*********.6135"));
  }

  private ApModelVersionProjection newApModelVersionProjection(String apModel, String apVersion) {
    return new ApModelVersionProjection() {
      @Override
      public String getApModel() {
        return apModel;
      }

      @Override
      public String getApVersion() {
        return apVersion;
      }
    };
  }

  @Test
  @FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
  public void testChangeScheduleWithUpgradeToLatest(Tenant tenant, Venue venue,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.533") ApVersion venueCurrentVersion,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.554") ApVersion targetApVerssion)
      throws Exception {
    venueCurrentVersion.setSupportedApModels(List.of("R730"));
    targetApVerssion.setSupportedApModels(List.of("R730"));
    apVersionRepository.saveAll(List.of(venueCurrentVersion, targetApVerssion));
    List<ApModelFirmware> targetFirmwares = List.of(
        ApModelFirmware.builder().apModel("R730").firmware(targetApVerssion.getId()).build()
    );

    // Given
    var venuesCurrentFirmware1 = new VenueCurrentFirmware();
    venuesCurrentFirmware1.setFirmware(venueCurrentVersion);
    venuesCurrentFirmware1.setApModel("R730");
    venuesCurrentFirmware1.setUpdatedDate(new java.util.Date());

    doReturn(List.of(venuesCurrentFirmware1))
        .when(venueCurrentFirmwareRepository)
        .findByTenantIdAndVenueId(tenant.getId(), venue.getId());
    doReturn(List.of(newApModelVersionProjection(targetApVerssion.getSupportedApModels().get(0),
        targetApVerssion.getId())))
        .when(tenantAvailableApFirmwareRepository)
        .getModelLatestSupportedRecommendedVersionByTenantId(tenant.getId());

    // When
    extendedVenueServiceCtrl.updateScheduleByApModel(
        "batchId", tenant.getId(), venue, "2022-08-04", "22:00-00:00", targetFirmwares);

    // Then
    verifyScheduleTimeSlotResult(venue.getId(), 4, 22);
    List<UpgradeSchedule> schedules = upgradeScheduleRepository.findAllByVenueId(venue.getId());
    assertThat(schedules).isNotNull().hasSize(1).element(0)
        .matches(op -> op.getUpgradeScheduleFirmwareVersions().get(0).getApFirmwareVersion().getId()
            .equals("*********.554"));
  }

  @Test
  @FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
  public void testChangeScheduleWithNoAp(Tenant tenant, Venue venue) throws Exception {
    // When
    extendedVenueServiceCtrl.updateScheduleByApModel(
        "batchId", tenant.getId(), venue, "2022-08-04", "22:00-00:00", List.of());

    // Then
    List<UpgradeSchedule> schedules = upgradeScheduleRepository.findAllByVenueId(venue.getId());
    assertThat(schedules).isNotNull().hasSize(0);
  }

  @Test
  public void testExtendedVenueChangeSchedule(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100") ApVersion prevScheduleVer,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.126") ApVersion targetVersion1,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.127") ApVersion targetVersion2,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.128") ApVersion targetVersion3)
      throws Exception {
    targetVersion1.setSupportedApModels(List.of("R730"));
    targetVersion2.setSupportedApModels(List.of("R750"));
    targetVersion3.setSupportedApModels(List.of("R770"));
    prevScheduleVer.setSupportedApModels(List.of("R730", "R750", "R770"));
    apVersionRepository.saveAll(List.of(targetVersion1, targetVersion2, targetVersion3, prevScheduleVer));

    List<ApModelFirmware> targetFirmwares = List.of(
        ApModelFirmware.builder().apModel("R730").firmware(targetVersion1.getId()).build(),
        ApModelFirmware.builder().apModel("R750").firmware(targetVersion2.getId()).build(),
        // Not exist ApModel should be pass
        ApModelFirmware.builder().apModel("R770").firmware(targetVersion3.getId()).build()
    );

    // Given

    String localStartDateTime = "2022-08-04 08:00:00";
    Venue venueTaipei = createVenueAndSetSchedule(tenant, prevScheduleVer, "Asia/Taipei", localStartDateTime);
    venueTaipei.setWifiFirmwareVersion(targetVersion1);
    venueRepository.save(venueTaipei);
    assertThat(upgradeScheduleRepository.findAllByVenueId(venueTaipei.getId())).isNotNull().hasSize(1);
    assertEquals(venueTaipei.getTimezone(), "Asia/Taipei");
    var date = new java.util.Date();
    var venuesCurrentFirmware1 = new VenueCurrentFirmware();
    venuesCurrentFirmware1.setFirmware(prevScheduleVer);
    venuesCurrentFirmware1.setApModel("R730");
    venuesCurrentFirmware1.setUpdatedDate(date);
    var venuesCurrentFirmware2 = new VenueCurrentFirmware();
    venuesCurrentFirmware2.setFirmware(prevScheduleVer);
    venuesCurrentFirmware2.setApModel("R750");
    venuesCurrentFirmware2.setUpdatedDate(date);

    doReturn(List.of(venuesCurrentFirmware1, venuesCurrentFirmware2)).when(venueCurrentFirmwareRepository).findByTenantIdAndVenueId(tenant.getId(), venueTaipei.getId());

    // When
    extendedVenueServiceCtrl.updateScheduleByApModel(
        "batchId", tenant.getId(), venueTaipei, "2022-08-04", "22:00-00:00", targetFirmwares);

    // Then
    verifyScheduleTimeSlotResult(venueTaipei.getId(), 4, 22);
    List<UpgradeScheduleFirmwareVersion> scheduleFirmwares = upgradeScheduleRepository.findAllByVenueId(venueTaipei.getId()).get(0).getUpgradeScheduleFirmwareVersions();
    assertThat(scheduleFirmwares.size()).isEqualTo( 2 );
    verify(kairosApiClient, times(1)).deleteScheduleJob(any(String.class));

    List<VenueApFirmwareBatchOperation> operations = venueApFirmwareBatchOperationRepository.findAllByTenantIdAndBatchId(tenant.getId(), "batchId");
    assertThat(operations).hasSize(1).element(0).matches(
        op -> op.getTargetApVersionList().stream().map(ApVersion::getId).toList().containsAll(List.of("*********.126", "*********.127")));
  }

  private Venue verifyScheduleTimeSlotResult(String venueId, int expectedDayOfMonth, int expectedHour) {
    Venue venue = repositoryUtil.find(Venue.class, venueId);
    UpgradeSchedule schedule = upgradeScheduleRepository.findAllByVenueId(venueId)
        .stream()
        .filter(s-> Objects.isNull(s.getStatus()) || s.getStatus().equals(UpgradeScheduleStatus.PENDING))
        .findFirst().orElseThrow();
    assertNotNull(schedule);

    ScheduleTimeSlot timeSlot = schedule.getTimeSlot();
    assertNotNull(timeSlot);

    ZonedDateTime zonedDateTime =
        ZonedDateTime.ofInstant(timeSlot.getStartDateTime().toInstant(), ZoneId.systemDefault())
            .withZoneSameInstant(ZoneId.of(venue.getTimezone()));

    assertEquals(expectedDayOfMonth, zonedDateTime.getDayOfMonth());
    assertEquals(expectedHour, zonedDateTime.getHour());

    return venue;
  }

  private Venue createVenueAndSetSchedule(Tenant tenant, ApVersion version, String timezone, String localStartDateTime) {
    Venue venue = repositoryUtil.createOrUpdate(
        VenueTestFixture.randomVenue(tenant, v -> v.setTimezone(timezone)), tenant.getId(),
        randomId());
    setVenueSchedule(version, venue, localStartDateTime);
    return repositoryUtil.find(Venue.class, venue.getId());
  }

  private void createUpgradeScheduleFirmwareVersion(UpgradeSchedule schedule, ApVersion apVersion) {
    UpgradeScheduleFirmwareVersion upgradeScheduleFirmwareVersion = new UpgradeScheduleFirmwareVersion(randomId());
    upgradeScheduleFirmwareVersion.setTenant(schedule.getTenant());
    upgradeScheduleFirmwareVersion.setUpgradeSchedule(schedule);
    upgradeScheduleFirmwareVersion.setApFirmwareVersion(apVersion);
    repositoryUtil.createOrUpdate(upgradeScheduleFirmwareVersion, upgradeScheduleFirmwareVersion.getTenant().getId(), randomId());
  }

  private void setVenueSchedule(ApVersion version, Venue venue, String localStartDateTime){
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime sldt = LocalDateTime.parse(localStartDateTime, formatter);
    LocalDateTime eldt = sldt.plusHours(2);
    ZonedDateTime startZonedDateTime = ZonedDateTime.of(sldt, ZoneId.systemDefault());
    ZonedDateTime endZonedDateTime = ZonedDateTime.of(eldt, ZoneId.systemDefault());
    ScheduleTimeSlot scheduleTimeSlot = new ScheduleTimeSlot(startZonedDateTime.toString());
    scheduleTimeSlot.setTotalCapacityVenue(2500);
    scheduleTimeSlot.setStartDateTime(java.util.Date.from(startZonedDateTime.toInstant()));
    scheduleTimeSlot.setEndDateTime(java.util.Date.from(endZonedDateTime.toInstant()));
    scheduleTimeSlot = repositoryUtil.createOrUpdate(scheduleTimeSlot, venue.getTenant().getId(), randomId());
    UpgradeSchedule upgradeSchedule = new UpgradeSchedule(randomId());
    upgradeSchedule.setTenant(venue.getTenant());
    upgradeSchedule.setVersion(version);
    upgradeSchedule.setTimeSlot(scheduleTimeSlot);
    upgradeSchedule.setVenue(venue);
    upgradeSchedule.setStatus(UpgradeScheduleStatus.PENDING);
    repositoryUtil.createOrUpdate(upgradeSchedule, venue.getTenant().getId(), randomId());
  }

  @Test
  void testHandlePostBatchScheduleNotificationForSkip(Tenant tenant,
  @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100") ApVersion ver620,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100") ApVersion ver700,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100") ApVersion ver710) {
    // Given
    Venue venue = repositoryUtil.createOrUpdate(
        VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("Asia/Taipei")), tenant.getId(),
        randomId());
    Venue venue2 = repositoryUtil.createOrUpdate(
        VenueTestFixture.randomVenue(tenant, v -> v.setTimezone("Asia/Taipei")), tenant.getId(),
        randomId());
    createApFirmwareBatchOperation(tenant, venue, "batchId", List.of(ver620, ver700), null);
    createApFirmwareBatchOperation(tenant, venue2, "batchId", List.of(ver700, ver710), null);
    assertThat(venueApFirmwareBatchOperationRepository.countByTenantId(tenant.getId())).isEqualTo(2);

    // When
    extendedVenueServiceCtrl.handlePostBatchScheduleNotification("batchId", tenant.getId(),
        BatchOperationType.SKIP_SCHEDULE);

    // Then
    assertThat(venueApFirmwareBatchOperationRepository.countByTenantId(tenant.getId())).isZero();
    verify(tenantFirmwareNotificationRouter, times(1))
        .sendCancelScheduleNotificationsByApModel(
            any(),
            any(),
            argThat(mapping -> mapping.getUnionVersions().stream().map(ApVersion::getId).toList()
                .containsAll(List.of(ver620.getId(), ver700.getId(), ver710.getId()))),
            any(), any());
  }

  @Test
  void testHandlePostBatchScheduleNotificationForChange(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.100") ApVersion ver710) {
    // Given
    String localStartDateTime = "2022-08-04 08:00:00";
    Venue venueTaipei = createVenueAndSetSchedule(tenant, ver710, "Asia/Taipei", localStartDateTime);
    Venue venueTaipei2 = createVenueAndSetSchedule(tenant, ver710, "Asia/Taipei", localStartDateTime);

    createApFirmwareBatchOperation(tenant, venueTaipei, "batchId", List.of(ver710), "2022-08-04 22:00");
    createApFirmwareBatchOperation(tenant, venueTaipei2, "batchId", List.of(ver710), "2022-08-04 22:00");
    assertThat(venueApFirmwareBatchOperationRepository.countByTenantId(tenant.getId())).isEqualTo(2);

    // When
    extendedVenueServiceCtrl.handlePostBatchScheduleNotification("batchId", tenant.getId(),
        BatchOperationType.CHANGE_SCHEDULE);

    // Then
    assertThat(venueApFirmwareBatchOperationRepository.countByTenantId(tenant.getId())).isZero();
    verify(tenantFirmwareNotificationRouter, times(1))
        .sendChangeScheduleNotificationsByApVersions(
            any(),
            argThat(list->list.stream().map(ApVersion::getId).toList()
                .contains(ver710.getId())),
            any(), argThat(bId -> bId.equals("batchId")));
  }

  private void createApFirmwareBatchOperation(Tenant tenant, Venue venue, String batchId,
      List<ApVersion> versions, String previousTimeslot) {
    VenueApFirmwareBatchOperation operation = new VenueApFirmwareBatchOperation();
    operation.setBatchId(batchId);
    operation.setTenant(tenant);
    operation.setVenue(venue);
    operation.setTargetApVersionList(versions);
    operation.setImpactedApCount(randomNumber(1, 100));
    operation.setPreviousTimeslot(previousTimeslot);
    repositoryUtil.createOrUpdate(
        operation, tenant.getId(), randomId());
  }

  @Test
  void testDeleteVenueWithVenueApFirmwareBatchOperation(
      Tenant tenant,
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*******.2")
      ApVersion apVersion)
      throws Exception {
    createApFirmwareBatchOperation(tenant, venue, "batchId", List.of(apVersion), null);

    List<VenueApFirmwareBatchOperation> operations =
        venueApFirmwareBatchOperationRepository.findAllByTenantIdAndBatchId(tenant.getId(), "batchId");
    assertThat(operations).isNotNull().hasSize(1);

    extendedVenueServiceCtrl.deleteVenue(venue);

    assertFalse(venueRepository.findByIdAndTenantId(venue.getId(), tenant.getId()).isPresent());

    assertFalse(venueApFirmwareBatchOperationRepository.findByIdAndTenantId(operations.get(0).getId(), tenant.getId()).isPresent());

    reset(venueRepository);
  }

  // region ApModelUsbPortSettings
  private VenueApModelSpecificAttributes createUsbPortEnableRequest(String model, Boolean enabled) {
    var result = new VenueApModelSpecificAttributes();
    result.setModel(model);
    result.setUsbPortEnable(enabled);
    return result;
  }

  @Test
  void getVenueApModelUsbPortSettings(Venue venue) throws Exception {
    var result = extendedVenueServiceCtrl.getVenueApModelUsbPortSettings(venue.getId());
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void updateVenueApModelUsbPortSettingsTestWithNewVenue(Tenant tenant, Venue venue)
      throws Exception {
    doReturn(tenant).when(tenantService).addAndGet(eq(tenant.getId()), isNull());

    var result = extendedVenueServiceCtrl.updateVenueApModelUsbPortSettings(venue.getId(), List.of(
        createUsbPortEnableRequest("T670", Boolean.TRUE),
        createUsbPortEnableRequest("T670SN", Boolean.FALSE))
    );
    // CREATE VERIFY
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("T670", result.get(0).getModel());
    assertEquals(Boolean.TRUE, result.get(0).getUsbPortEnable());
    assertEquals("T670SN", result.get(1).getModel());
    assertEquals(Boolean.FALSE, result.get(1).getUsbPortEnable());
  }

  @Test
  void updateVenueApModelUsbPortSettingsTestWithExistedVenue(Venue venue) throws Exception {
    // CREATE
    var result = extendedVenueServiceCtrl.updateVenueApModelUsbPortSettings(venue.getId(), List.of(
        createUsbPortEnableRequest("T670", Boolean.TRUE),
        createUsbPortEnableRequest("T670SN", Boolean.FALSE))
    );
    // CREATE VERIFY
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("T670", result.get(0).getModel());
    assertEquals(Boolean.TRUE, result.get(0).getUsbPortEnable());
    assertEquals("T670SN", result.get(1).getModel());
    assertEquals(Boolean.FALSE, result.get(1).getUsbPortEnable());

    // UPDATE
    result = extendedVenueServiceCtrl.updateVenueApModelUsbPortSettings(venue.getId(), List.of(
        createUsbPortEnableRequest("R670", Boolean.TRUE),
        createUsbPortEnableRequest("T670", Boolean.FALSE)
    ));
    // UPDATE VERIFY
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals("R670", result.get(0).getModel());
    assertEquals(Boolean.TRUE, result.get(0).getUsbPortEnable());
    assertEquals("T670", result.get(1).getModel());
    assertEquals(Boolean.FALSE, result.get(1).getUsbPortEnable());
    assertEquals("T670SN", result.get(2).getModel());
    assertNull(result.get(2).getUsbPortEnable());
  }
  // endregion ApModelUsbPortSettings
}
