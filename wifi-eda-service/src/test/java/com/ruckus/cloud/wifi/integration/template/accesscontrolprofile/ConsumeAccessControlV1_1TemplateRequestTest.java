package com.ruckus.cloud.wifi.integration.template.accesscontrolprofile;

import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.APPLICATION_POLICY_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.APPLICATION_POLICY_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.DEVICE_POLICY_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.DEVICE_POLICY_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.L2_ACL_POLICY_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.L2_ACL_POLICY_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.L3_ACL_POLICY_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.L3_ACL_POLICY_NAME;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmFirewallProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.AccessControlProfileRestCtrl.AccessControlProfileMapper;
import com.ruckus.cloud.wifi.eda.api.rest.DevicePolicyRestCtrl;
import com.ruckus.cloud.wifi.eda.service.AccessControlProfileServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.AccessControlProfileV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.DevicePolicyV1_1;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("AccessControlProfileTest")
@WifiIntegrationTest
public class ConsumeAccessControlV1_1TemplateRequestTest extends AbstractRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  protected AccessControlProfileServiceCtrl accessControlProfileServiceCtrl;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1)
  class ConsumeAddAccessControlTemplateRequestTest {

    @Payload
    final AccessControlProfileV1_1 profile = Generators.accessControlProfileV1_1().generate();

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload AccessControlProfileV1_1 payload) {
      final AccessControlProfile profile = repositoryUtil.find(AccessControlProfile.class, payload.getId());

      assertNotNull(profile);
      validateRepositoryData(payload, profile);
      validateDdccmCfgRequestMessages(CfgAction.ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1, List.of(payload.getId()), profile);
      validateCmnCfgCollectorMessages(CfgAction.ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1,
          List.of(payload.getId()), profile, 0);
      validateActivityMessages(CfgAction.ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1)
  class ConsumeUpdateAccessControlTemplateRequestTest {

    String accessControlProfileId;

    @Payload
    final AccessControlProfileV1_1 profile = Generators.accessControlProfileV1_1().generate();

    @BeforeEach
    public void givenOneRowPersistedInDb(@Template final AccessControlProfile accessControlProfile) {
      accessControlProfileId = accessControlProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("accessControlProfileTemplateId", accessControlProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload AccessControlProfileV1_1 payload) {
      final AccessControlProfile profile = repositoryUtil.find(AccessControlProfile.class, accessControlProfileId);
      assertNotNull(profile);
      payload.setId(accessControlProfileId);
      validateRepositoryData(payload, profile);
      validateActivityMessages(CfgAction.UPDATE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1)
  class ConsumeDeleteAccessControlTemplateRequestTest {

    private String accessControlProfileId;

    @BeforeEach
    void givenOneRowPersistedInDb(@Template final AccessControlProfile accessControlProfile) {
      accessControlProfileId = accessControlProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("accessControlProfileTemplateId", accessControlProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      assertNull(repositoryUtil.find(AccessControlProfile.class, accessControlProfileId));
      validateActivityMessages(CfgAction.DELETE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1);
    }
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK)
  class ConsumeActivateAccessControlTemplateOnWifiNetworkRequestTest {

    private String wifiNetworkTemplateId;
    private String accessControlProfileTemplateId;

    @BeforeEach
    void givenOneRowPersistedInDb(@Template final Network network, @Template final AccessControlProfile accessControlProfile) {
      wifiNetworkTemplateId = network.getId();
      accessControlProfileTemplateId = accessControlProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId)
          .addPathVariable("accessControlProfileTemplateId", accessControlProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      var wifiNetwork = repositoryUtil.find(Network.class, wifiNetworkTemplateId);
      var accessControlProfile = repositoryUtil.find(AccessControlProfile.class, accessControlProfileTemplateId);

      assertThat(accessControlProfile)
          .isNotNull()
          .matches(profile -> Objects.equals(profile.getId(), accessControlProfileTemplateId));
      assertThat(wifiNetwork)
          .isNotNull()
          .matches(v -> Objects.equals(v.getId(), wifiNetworkTemplateId))
          .matches(v -> Objects.equals(v.getWlan().getAdvancedCustomization().getAccessControlProfile().getId(), accessControlProfileTemplateId));

      validateCmnCfgCollectorMessages(CfgAction.ACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK,
          List.of(accessControlProfileTemplateId), accessControlProfile, 1);
      validateActivityMessages(CfgAction.ACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK);
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK)
  class ConsumeDeactivateAccessControlTemplateOnWifiNetworkRequestTest {

    private String wifiNetworkTemplateId;
    private String accessControlProfileTemplateId;

    @BeforeEach
    void givenOneRowPersistedInDb(@Template final Network network, @Template final AccessControlProfile accessControlProfile) {
      wifiNetworkTemplateId = network.getId();
      accessControlProfileTemplateId = accessControlProfile.getId();

      network.getWlan().getAdvancedCustomization().setAccessControlEnable(true);
      network.getWlan().getAdvancedCustomization().setAccessControlProfile(accessControlProfile);
      repositoryUtil.createOrUpdate(network, txCtxExtension.getTenantId(), randomTxId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId)
          .addPathVariable("accessControlProfileTemplateId", accessControlProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      var wifiNetwork = repositoryUtil.find(Network.class, wifiNetworkTemplateId);
      var accessControlProfile = repositoryUtil.find(AccessControlProfile.class, accessControlProfileTemplateId);

      assertThat(accessControlProfile)
          .isNotNull()
          .matches(profile -> Objects.equals(profile.getId(), accessControlProfileTemplateId));
      assertThat(wifiNetwork)
          .isNotNull()
          .matches(v -> Objects.equals(v.getId(), wifiNetworkTemplateId))
          .matches(v -> Objects.equals(v.getWlan().getAdvancedCustomization().getAccessControlProfile(), null));

      validateCmnCfgCollectorMessages(CfgAction.DEACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK,
          List.of(accessControlProfileTemplateId), accessControlProfile, 0);
      validateActivityMessages(CfgAction.DEACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK);
    }
  }

  private void validateDdccmCfgRequestMessages(CfgAction apiAction, List<String> idList,
      AccessControlProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasCcmFirewallProfile)
            .hasSize(idList.size()).allMatch(op -> idList.contains(op.getId()))
            .allMatch(op -> op.getAction() == action(apiAction)).allSatisfy(
                op -> assertThat(op).extracting(
                        com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op).extracting(
                        com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCcmFirewallProfile)
                    .matches(config -> idList.contains(config.getId()));
              } else {
                assertThat(payload).isNotNull();
                assertThat(op).extracting(
                        com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCcmFirewallProfile)
                    .matches(config -> idList.contains(config.getId()))
                    .matches(config -> payload.getName().equals(config.getName()))
                    .matches(config -> assertPolicies(payload, config)).matches(
                        config -> ((long) payload.getRateLimiting().getDownlinkLimit() * 1000000)
                            == config.getDownlinkRateLimitingBps()).matches(
                        config -> ((long) payload.getRateLimiting().getUplinkLimit() * 1000000)
                            == config.getUplinkRateLimitingBps());
              }
            }));
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, List<String> idList,
      AccessControlProfile payload, int expectedNetworkCount) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> Index.ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(idList.size()).allMatch(op -> idList.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction)).allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op).extracting(Operations::getDocMap)
                    .matches(doc -> idList.contains(doc.get(Key.ID).getStringValue()))
                    .matches(
                        doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                    .matches(doc -> payload.getName().equals(doc.get(Key.NAME).getStringValue()))
                    .matches(doc -> assertCfgPolicies(payload, doc)).matches(
                        doc -> expectedNetworkCount == doc.get(Key.NETWORK_IDS).getListValue()
                            .getValuesCount());
              }
            }));
  }

  private void validateActivityMessages(CfgAction apiAction) {
    validateActivityMessages(apiAction, txCtxExtension.getRequestId());
  }

  private void validateActivityMessages(CfgAction apiAction, String requestId) {
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> assertThat(
        activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());
  }

  private boolean assertPolicies(AccessControlProfile payload, CcmFirewallProfile config) {
    assertFalse(config.hasFirewallL2AccessControlPolicy());
    assertFalse(config.hasFirewallL3AccessControlPolicy());
    if (payload.getApplicationPolicy() != null) {
      assertEquals(payload.getApplicationPolicy().getId(),
          config.getFirewallQmApplicationPolicy().getQmApplicationPolicyId().getValue());
    } else {
      assertFalse(config.hasFirewallQmApplicationPolicy());
    }
    if (payload.getDevicePolicy() != null) {
      assertEquals(payload.getDevicePolicy().getId(), config.getFirewallDevicePolicy().getDevicePolicyId().getValue());
    } else {
      assertFalse(config.hasFirewallDevicePolicy());
    }

    return true;
  }

  private boolean assertCfgPolicies(AccessControlProfile payload, Map<String, Value> doc) {
    assertEquals(NullValue.NULL_VALUE, doc.get(L2_ACL_POLICY_NAME).getNullValue());
    assertEquals(NullValue.NULL_VALUE, doc.get(L2_ACL_POLICY_ID).getNullValue());
    assertEquals(NullValue.NULL_VALUE, doc.get(L3_ACL_POLICY_NAME).getNullValue());
    assertEquals(NullValue.NULL_VALUE, doc.get(L3_ACL_POLICY_ID).getNullValue());
    if (payload.getDevicePolicy() != null) {
      assertEquals(payload.getDevicePolicy().getName(),
          doc.get(DEVICE_POLICY_NAME).getStringValue());
      assertEquals(payload.getDevicePolicy().getId(), doc.get(DEVICE_POLICY_ID).getStringValue());
    } else {
      assertEquals(NullValue.NULL_VALUE, doc.get(DEVICE_POLICY_NAME).getNullValue());
      assertEquals(NullValue.NULL_VALUE, doc.get(DEVICE_POLICY_ID).getNullValue());
    }

    if (payload.getApplicationPolicy() != null) {
      assertEquals(payload.getApplicationPolicy().getName(),
          doc.get(APPLICATION_POLICY_NAME).getStringValue());
      assertEquals(payload.getApplicationPolicy().getId(),
          doc.get(APPLICATION_POLICY_ID).getStringValue());
    } else {
      assertEquals(NullValue.NULL_VALUE, doc.get(APPLICATION_POLICY_NAME).getNullValue());
      assertEquals(NullValue.NULL_VALUE, doc.get(APPLICATION_POLICY_ID).getNullValue());
    }

    return true;
  }

  private void validateRepositoryData(AccessControlProfileV1_1 expected, AccessControlProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getDescription(), actual.getDescription());
    assertTrue(actual.getIsTemplate());
    if (expected.getRateLimiting() != null) {
      assertEquals(expected.getRateLimiting().getDownlinkLimit(), actual.getRateLimiting().getDownlinkLimit());
      assertEquals(expected.getRateLimiting().getUplinkLimit(), actual.getRateLimiting().getUplinkLimit());
      assertEquals(expected.getRateLimiting().getEnabled(), actual.getRateLimiting().getEnabled());
    } else {
      assertNull(actual.getRateLimiting());
    }
  }

  public AccessControlProfileV1_1 mapViewModel(AccessControlProfile accessControlProfile) {
    return AccessControlProfileMapper.INSTANCE.ServiceAccessControlProfile2AccessControlProfileV1_1(accessControlProfile);
  }

  public DevicePolicyV1_1 mapViewModel(DevicePolicy policy) {
    return DevicePolicyRestCtrl.DevicePolicyMapper.INSTANCE.ServiceDevicePolicy2DevicePolicyV1_1(policy);
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1 -> Action.ADD;
      case UPDATE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1, ACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK,
           DEACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK -> Action.MODIFY;
      case DELETE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1 -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1 -> OpType.ADD;
      case UPDATE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1, ACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK,
           DEACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK -> OpType.MOD;
      case DELETE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1 -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1 -> ApiFlowNames.ADD_ACCESS_CONTROL_PROFILE_TEMPLATE;
      case UPDATE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1 -> ApiFlowNames.UPDATE_ACCESS_CONTROL_PROFILE_TEMPLATE;
      case DELETE_ACCESS_CONTROL_PROFILE_TEMPLATE_V1_1 -> ApiFlowNames.DELETE_ACCESS_CONTROL_PROFILE_TEMPLATE;
      case ACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK -> ApiFlowNames.ACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK;
      case DEACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK -> ApiFlowNames.DEACTIVATE_ACCESS_CONTROL_PROFILE_TEMPLATE_ON_WIFI_NETWORK;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
