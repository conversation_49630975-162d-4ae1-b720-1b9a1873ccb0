package com.ruckus.cloud.wifi.requirement;

import static org.apache.commons.lang3.RandomStringUtils.randomAlphanumeric;
import static org.apache.commons.lang3.RandomUtils.nextBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willReturn;

import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Set;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

@WifiUnitTest
class ApModelFamilyTest {
  @Mock private Set<String> models;

  @Mock(answer = Answers.CALLS_REAL_METHODS)
  private ApModelFamily unit;

  @BeforeEach
  void beforeEach() {
    ReflectionTestUtils.setField(unit, "models", models);
  }

  @SuppressWarnings("ResultOfMethodCallIgnored")
  @Test
  void whenIsModelSupported() {
    final var model = randomAlphanumeric(10);
    final var result = nextBoolean();

    willReturn(result).given(models).contains(anyString());

    BDDAssertions.then(unit.isModelSupported(model)).isEqualTo(result);

    then(models).should().contains(eq(model));
  }
}
