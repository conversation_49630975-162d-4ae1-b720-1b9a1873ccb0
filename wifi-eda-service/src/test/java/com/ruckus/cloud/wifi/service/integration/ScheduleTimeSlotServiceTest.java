package com.ruckus.cloud.wifi.service.integration;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.repository.ScheduleTimeSlotRepository;
import com.ruckus.cloud.wifi.service.ScheduleTimeSlotService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.impl.ScheduleTimeSlotServiceImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.utils.RksTransactionHelper;
import java.time.Instant;
import java.util.Date;
import java.util.function.Supplier;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.Transactional;

@WifiJpaDataTest
public class ScheduleTimeSlotServiceTest {

  @Autowired ScheduleTimeSlotService scheduleTimeSlotService;

  RksTransactionHelper rksTransactionHelper;

  @BeforeEach
  public void setUp() {
    TxCtxHolder.set(new TxCtxHolder.TxCtx("test", "request-1", "test", "testFlow"));
    rksTransactionHelper =
        new RksTransactionHelper() {
          @Override
          public <T> T runInNewTransactionWithNewTxId(Supplier<T> supplier) {
            return supplier.get();
          }
        };
  }

  @Test
  public void testFindByStartDateTime() {
    ScheduleTimeSlotRepository repository = mock(ScheduleTimeSlotRepository.class);
    ScheduleTimeSlotService service =
        new ScheduleTimeSlotServiceImpl(repository, rksTransactionHelper);
    Date startDateTime = Date.from(Instant.now());
    service.findByStartDateTime(startDateTime);
    verify(repository, times(1)).findByStartDateTime(startDateTime);
  }

  @Test
  public void testSaveAndFlush() {
    ScheduleTimeSlotRepository repository = mock(ScheduleTimeSlotRepository.class);
    ScheduleTimeSlotService service =
        new ScheduleTimeSlotServiceImpl(repository, rksTransactionHelper);
    ScheduleTimeSlot timeSlot = new ScheduleTimeSlot();
    service.saveAndFlush(timeSlot);
    verify(repository, times(1)).saveAndFlush(timeSlot);
  }

  @Test
  @Transactional
  public void testSaveAndFlush_saveTwiceWithSameStartTime_returnExistedTimeslot() {
    ScheduleTimeSlotService service = scheduleTimeSlotService;
    ScheduleTimeSlot timeSlot = new ScheduleTimeSlot();
    Instant start1 = Instant.now();
    Instant end1 = start1.plusSeconds(60 * 60 * 2);
    timeSlot.setStartDateTime(java.sql.Date.from(start1));
    timeSlot.setEndDateTime(java.sql.Date.from(end1));

    ScheduleTimeSlot duplicateTimeSlot = new ScheduleTimeSlot();
    duplicateTimeSlot.setStartDateTime(java.sql.Date.from(start1));
    duplicateTimeSlot.setEndDateTime(java.sql.Date.from(end1));

    ScheduleTimeSlot createdTimeSlot = service.saveAndFlush(timeSlot);
    ScheduleTimeSlot returnExistTimeSlot = service.saveAndFlush(duplicateTimeSlot);

    Assertions.assertEquals(timeSlot.getStartDateTime(), createdTimeSlot.getStartDateTime());
    Assertions.assertEquals(createdTimeSlot.getId(), returnExistTimeSlot.getId());
    Assertions.assertEquals(
        createdTimeSlot.getId(),
        service.findByStartDateTime(java.sql.Date.from(start1)).get(0).getId());
  }

  @TestConfiguration
  @Import({ScheduleTimeSlotServiceImpl.class, RksTransactionHelper.class})
  static class TestConfig {

    @Bean
    @ConditionalOnMissingBean
    public ScheduleTimeSlotService scheduleTimeSlotService(
        ScheduleTimeSlotRepository scheduleTimeSlotRepository,
        RksTransactionHelper rksTransactionHelper) {
      return new ScheduleTimeSlotServiceImpl(scheduleTimeSlotRepository, rksTransactionHelper);
    }
  }
}
