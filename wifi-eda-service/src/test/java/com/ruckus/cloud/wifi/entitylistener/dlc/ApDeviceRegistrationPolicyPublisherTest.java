package com.ruckus.cloud.wifi.entitylistener.dlc;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

import com.ruckus.cloud.wifi.dlc.stream.protobuf.device.DeviceRegistrationPolicyProto.DeviceRegistrationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap.Fields;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.ApGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.ApGroupGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.VenueGenerator;
import com.ruckus.cloud.wifi.entitylistener.dlc.builder.DeviceRegistrationPolicyBuilder;
import com.ruckus.cloud.wifi.kafka.publisher.DeviceRegistrationPolicyPublisher;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesImpl;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(TxCtxExtension.class)
public class ApDeviceRegistrationPolicyPublisherTest {

  private DeviceRegistrationPolicyBuilder deviceRegistrationPolicyBuilder = new DeviceRegistrationPolicyBuilder();

  @Test
  void testBuildDeviceRegistrationPolicy_whenApGroupChanged() {
    DeviceRegistrationPolicyPublisher deviceRegistrationPolicyPublisher = mock(
        DeviceRegistrationPolicyPublisher.class);
    ApDeviceRegistrationPolicyPublisher unit = new ApDeviceRegistrationPolicyPublisher(
        deviceRegistrationPolicyPublisher, deviceRegistrationPolicyBuilder);

    final var ap = new ApGenerator()
        .setApGroup(new ApGroupGenerator()
            .setVenue(new VenueGenerator()))
        .setName(always("test_ap"))
        .generate();
    final var result = unit.build(new ModifiedTxEntity<>(ap, Set.of(Fields.APGROUP)),
        TxChangesImpl.EMPTY);

    assertThat(result)
        .hasSize(1)
        .extracting(DeviceRegistrationPolicy::getGroupId)
        .containsExactly(ap.getApGroup().getId());
  }

  @Test
  void testBuildDeviceRegistrationPolicy_whenNameChanged() {
    DeviceRegistrationPolicyPublisher deviceRegistrationPolicyPublisher = mock(
        DeviceRegistrationPolicyPublisher.class);
    ApDeviceRegistrationPolicyPublisher unit = new ApDeviceRegistrationPolicyPublisher(
        deviceRegistrationPolicyPublisher, deviceRegistrationPolicyBuilder);

    final var ap = new ApGenerator()
        .setApGroup(new ApGroupGenerator()
            .setVenue(new VenueGenerator()))
        .setName(always("test_ap"))
        .generate();
    final var result = unit.build(new ModifiedTxEntity<>(ap, Set.of(Fields.NAME)),
        TxChangesImpl.EMPTY);

    assertThat(result)
        .hasSize(1)
        .extracting(DeviceRegistrationPolicy::getGroupId)
        .containsExactly(ap.getApGroup().getId());
  }

  @Test
  void testBuildDeviceRegistrationPolicy_ProduceNothing_WhenApGroupOrNameNotChanged() {
    DeviceRegistrationPolicyPublisher deviceRegistrationPolicyPublisher = mock(
        DeviceRegistrationPolicyPublisher.class);
    ApDeviceRegistrationPolicyPublisher unit = new ApDeviceRegistrationPolicyPublisher(
        deviceRegistrationPolicyPublisher, deviceRegistrationPolicyBuilder);

    final var ap = new ApGenerator()
        .setApGroup(new ApGroupGenerator()
            .setVenue(new VenueGenerator()))
        .generate();
    final var result = unit.build(new ModifiedTxEntity<>(ap, Set.of(Fields.DESCRIPTION)),
        TxChangesImpl.EMPTY);

    assertThat(result)
        .isEmpty();
  }
}
