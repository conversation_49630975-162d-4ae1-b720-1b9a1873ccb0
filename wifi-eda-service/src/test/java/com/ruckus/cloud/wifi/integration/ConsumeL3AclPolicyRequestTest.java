package com.ruckus.cloud.wifi.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.L3AccessControl;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.UTPDefaultActionEnum;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.L3AclPolicyRestCtrl;
import com.ruckus.cloud.wifi.eda.service.L3AclPolicyServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AccessEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.L3AclPolicyTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;


@Slf4j
@WifiIntegrationTest
public class ConsumeL3AclPolicyRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private L3AclPolicyServiceCtrl l3AclPolicyServiceCtrl;

  @Test
  public void testCudL3AclPolicy(Tenant tenant) throws Exception {
    // Given
    String tenantId = txCtxExtension.getTenantId();
    createVenue(tenant, "venue-test");

    // When - add L3 policy
    L3AclPolicy l3AclPolicy = L3AclPolicyTestFixture.randomL3AclPolicy();
    l3AclPolicy.setDefaultAccess(AccessEnum.ALLOW);
    l3AclPolicy.setTenant(tenant);
    executeNewUiEdaFlow(tenantId, l3AclPolicy, CfgAction.ADD_L3ACL_POLICY);
    // Then - add L3 policy
    // assert activity service
    assertActivityStatusSuccess(ApiFlowNames.ADD_L3ACL_POLICY, tenantId);
    // assert result
    List<L3AclPolicy> policies = getAllL3AclPolicies();
    assertEquals(1, policies.size(), "Should have one L3 Policy");
    assertEquals(l3AclPolicy.getName(), policies.get(0).getName(), "Policy name should equal test data");
    assertEquals(AccessEnum.ALLOW, policies.get(0).getDefaultAccess(), "Policy access should equal test data");

    // assert ddccm - expected invoke 1 l3 policy
    List<L3AccessControl> ddccmPolicies = receiveDdccmOperations(1, tenantId).stream()
            .filter(o -> o.getAction().equals(Action.ADD))
            .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.L3ACCESSCONTROL))
            .map(Operation::getL3AccessControl).collect(Collectors.toList());
    assertEquals(1, ddccmPolicies.size(), "Ddccm - Should have one L3 Policy");
    assertEquals(l3AclPolicy.getName(), ddccmPolicies.get(0).getName(), "Ddccm - Policy name should equal test data");
    assertEquals(UTPDefaultActionEnum.DefaultActionEnum_ALLOW, ddccmPolicies.get(0).getDefaultAction(), "Ddccm - Policy access should equal test data");

    // assert cmnViewModelCollector - expected invoke 1 l3 policy
    assertCmnViewModelCollectorPolicies(OpType.ADD, policies.get(0).getId(), policies.get(0).getName(), tenantId, 0);

    // When - update L3 policy
    l3AclPolicy.setName("policy2");
    l3AclPolicy.setDefaultAccess(AccessEnum.BLOCK);
    executeNewUiEdaFlow(tenantId, l3AclPolicy, CfgAction.UPDATE_L3ACL_POLICY, l3AclPolicy.getId());

    // Then - update L3 policy
    // assert activity service
    assertActivityStatusSuccess(ApiFlowNames.UPDATE_L3ACL_POLICY, tenantId);
    // assert result
    policies = getAllL3AclPolicies();
    assertEquals(1, policies.size(), "Should have one L3 Policy");
    assertEquals("policy2", policies.get(0).getName(), "Policy name should equal test data");
    assertEquals(AccessEnum.BLOCK, policies.get(0).getDefaultAccess(), "Policy access should equal test data");
    // assert ddccm - expected invoke 1 l3 policy
    ddccmPolicies = receiveDdccmOperations(1, tenantId).stream()
            .filter(o -> o.getAction().equals(Action.MODIFY))
            .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.L3ACCESSCONTROL))
            .map(Operation::getL3AccessControl).collect(Collectors.toList());
    assertEquals(1, ddccmPolicies.size(), "Ddccm - Should have one L3 Policy");
    assertEquals("policy2", ddccmPolicies.get(0).getName(), "Ddccm - Policy name should equal test data");
    assertEquals(AccessEnum.BLOCK, policies.get(0).getDefaultAccess(), "Policy access should equal test data");
    // assert cmnViewModelCollector - expected invoke 1 l3 policy
    assertCmnViewModelCollectorPolicies(OpType.MOD, policies.get(0).getId(), policies.get(0).getName(), tenantId, 0);

    // When - delete L3 policy
    executeNewUiEdaFlow(tenantId, null, CfgAction.DELETE_L3ACL_POLICY, l3AclPolicy.getId());
    // Then - update L3 policy
    // assert activity service
    assertActivityStatusSuccess(ApiFlowNames.DELETE_L3ACL_POLICY, tenantId);
    // assert result
    policies = getAllL3AclPolicies();
    assertEquals(0, policies.size(), "Should have no L3 Policy");
    // assert ddccm - expected invoke 1 l3 policy
    ddccmPolicies = receiveDdccmOperations(1, tenantId).stream()
            .filter(o -> o.getAction().equals(Action.DELETE))
            .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.L3ACCESSCONTROL))
            .map(Operation::getL3AccessControl).collect(Collectors.toList());
    assertEquals(1, ddccmPolicies.size(), "Ddccm - Should have one L3 Policy");
    assertEquals(l3AclPolicy.getId(), ddccmPolicies.get(0).getId(), "Ddccm - Policy id should equal test data");
    assertCmnViewModelCollectorPolicies(OpType.DEL, l3AclPolicy.getId(), null, tenantId, 0);
  }

  private void executeNewUiEdaFlow(String tenantId, L3AclPolicy l3AclPolicy, CfgAction cfgAction) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();

    sendWifiCfgRequest(tenantId, requestId, cfgAction, userName, mapViewModel(l3AclPolicy));
  }

  private void executeNewUiEdaFlow(String tenantId, L3AclPolicy policy, CfgAction action, String policyId) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    RequestParams params = new RequestParams().addPathVariable("l3AclPolicyId", policyId);

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, mapViewModel(policy));
  }

  @SneakyThrows
  public List<L3AclPolicy> getAllL3AclPolicies() {
    return l3AclPolicyServiceCtrl.getAllL3AclPolicies().stream().map(this::map).collect(Collectors.toList());
  }

  public L3AclPolicy map(L3AclPolicy l3AclPolicy) {
    return L3AclPolicyRestCtrl.L3AclPolicyMapper.INSTANCE.L3AclPolicy2ServiceL3AclPolicy(
            L3AclPolicyRestCtrl.L3AclPolicyMapper.INSTANCE.ServiceL3AclPolicy2L3AclPolicy(l3AclPolicy));
  }

  public com.ruckus.cloud.wifi.eda.viewmodel.L3AclPolicy mapViewModel(L3AclPolicy l3AclPolicy) {
    return L3AclPolicyRestCtrl.L3AclPolicyMapper.INSTANCE.ServiceL3AclPolicy2L3AclPolicy(l3AclPolicy);
  }

  private void assertCmnViewModelCollectorPolicies(OpType expectedOpType,
                                                   String expectedServiceProfileId, String serviceName, String tenantId, int scope) {
    List<Operations> viewModelOperations = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> expectedServiceProfileId.equals(o.getId())).collect(Collectors.toList());
    Operations accessControlProfileIndexOps = viewModelOperations.stream()
      .filter(op -> op.getIndex().equals(EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME)).findFirst().get();
    assertEquals(expectedOpType, accessControlProfileIndexOps.getOpType());
    if (!accessControlProfileIndexOps.getOpType().equals(OpType.DEL)) {
      assertEquals(serviceName,
        accessControlProfileIndexOps.getDocMap().get(EsConstants.Key.NAME).getStringValue());
      assertEquals(scope,
        accessControlProfileIndexOps.getDocMap().get(EsConstants.Key.NETWORK_IDS).getListValue().getValuesCount());
    }
  }
}
