package com.ruckus.cloud.wifi.integration;

import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.DefaultApBranchFamily;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.test.TestConstants;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.viewmodel.DefaultApBranchFamilyRequest;
import com.ruckus.cloud.wifi.viewmodel.DefaultApFirmwareRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertEquals;

@WifiIntegrationTest
public class ConsumeSetDefaultABFRecommendedApFirmwareTest {
  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Test
  public void testSetDefaultABFRecommendedApFirmware(Tenant tenant) {
    DefaultApBranchFamily dabf1 = repositoryUtil.find(DefaultApBranchFamily.class, TestConstants.ABF1);
    dabf1.setActive(true);
    repositoryUtil.createOrUpdate(dabf1, randomTxId(), randomTxId());

    DefaultApBranchFamily dabf2 = repositoryUtil.find(DefaultApBranchFamily.class, TestConstants.ABF2);
    dabf1.setActive(false);
    repositoryUtil.createOrUpdate(dabf2, randomTxId(), randomTxId());

    DefaultApFirmwareRequest defaultApFirmwareRequest1 = new DefaultApFirmwareRequest();
    defaultApFirmwareRequest1.setName(TestConstants.ABF1);
    defaultApFirmwareRequest1.setDefaultApFirmware(TestConstants.DABF1_VERSION);
    DefaultApFirmwareRequest defaultApFirmwareRequest2 = new DefaultApFirmwareRequest();
    defaultApFirmwareRequest2.setName(TestConstants.ABF2);
    defaultApFirmwareRequest2.setDefaultApFirmware(TestConstants.DABF2_VERSION);
    DefaultApBranchFamilyRequest defaultApBranchFamilyRequest = new DefaultApBranchFamilyRequest();
    defaultApBranchFamilyRequest.setDefaultABF(TestConstants.ABF2);
    defaultApBranchFamilyRequest.setDefaultApFirmwareRequests(List.of(defaultApFirmwareRequest1, defaultApFirmwareRequest2));

    // When
    var params = new RequestParams();
    params.addRequestParam("type", "RECOMMENDED");
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.SET_DEFAULT_ABF_RECOMMENDED_AP_FIRMWARE,
        randomName(),
        params,
        defaultApBranchFamilyRequest);

    // Then
    DefaultApBranchFamily defaultApBranchFamily =
        repositoryUtil.find(DefaultApBranchFamily.class, TestConstants.ABF2);
    assert defaultApBranchFamily != null;
    assertEquals(true, defaultApBranchFamily.getActive());
    assertEquals(TestConstants.DABF2_VERSION, defaultApBranchFamily.getBranchVersion().getId());

  }
}
