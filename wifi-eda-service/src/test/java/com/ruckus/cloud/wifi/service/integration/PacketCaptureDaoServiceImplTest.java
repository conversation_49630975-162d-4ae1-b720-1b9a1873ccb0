package com.ruckus.cloud.wifi.service.integration;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.service.PacketCaptureDaoService;
import com.ruckus.cloud.wifi.service.entity.PacketCapture;
import com.ruckus.cloud.wifi.service.entity.PacketCaptureField;
import com.ruckus.cloud.wifi.service.entity.PacketCaptureState;
import com.ruckus.cloud.wifi.service.exception.PcapOperationFailureException;
import com.ruckus.cloud.wifi.service.impl.PacketCaptureDaoServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.TestContainerRedisConfig;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.RedisTestContainer;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.redisson.api.RTransaction;
import org.redisson.api.RedissonClient;
import org.redisson.api.TransactionOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Import;

@RedisTestContainer
@WifiUnitTest
public class PacketCaptureDaoServiceImplTest {
  protected final String SERIAL_NUMBER = CommonTestFixture.randomSerialNumber();
  protected final String SESSION_ID = CommonTestFixture.randomTxId();
  @Autowired
  private RedissonClient redissonClient;

  @Test
  public void createAndGet() {
    String expectSerial = SERIAL_NUMBER;
    String expectSessionId = SESSION_ID;
    Instant expectStartTime = Instant.now();
    String expectedUploadUrl = "https://upload.test";
    PacketCaptureState expectedState = PacketCaptureState.STARTING;

    Map<PacketCaptureField, String> data = new HashMap<>();
    data.put(PacketCaptureField.SESSION_ID, expectSessionId);
    data.put(PacketCaptureField.START_TIME, String.valueOf(expectStartTime.getEpochSecond()));
    data.put(PacketCaptureField.UPLOAD_URL, expectedUploadUrl);
    data.put(PacketCaptureField.STATE, expectedState.name());

    PacketCaptureDaoService packetCaptureDao = new PacketCaptureDaoServiceImpl(redissonClient);

    RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());
    try {
      packetCaptureDao.create(transaction, expectSerial, data, Duration.ofSeconds(3));
      transaction.commit();
    } catch (Exception e) {
      transaction.rollback();
      throw new PcapOperationFailureException(e);
    }

    Optional<PacketCapture> optionalPcap = packetCaptureDao.get(expectSerial);
    PacketCapture pcap = optionalPcap.get();

    assertEquals(expectSerial, pcap.getSerial());
    assertEquals(expectSessionId, pcap.getSessionId());
    assertEquals(expectStartTime.getEpochSecond(), pcap.getStartTime().getEpochSecond());
    assertEquals(expectedUploadUrl, pcap.getUploadUrl());
    assertEquals(expectedState, pcap.getState());
    assertNull(pcap.getErrMsg());
    assertNull(pcap.getReadyTime());
    assertNull(pcap.getOpStatus());
  }

  @Test
  public void getNonExistentRecord() {
    String nonExistentSerial = CommonTestFixture.randomSerialNumber();;

    PacketCaptureDaoService packetCaptureDao = new PacketCaptureDaoServiceImpl(redissonClient);
    Optional<PacketCapture> pcap = packetCaptureDao.get(nonExistentSerial);
    assertTrue(pcap.isEmpty());
  }

  @TestConfiguration
  @Import(TestContainerRedisConfig.class)
  static class TestConfig {

  }

}
