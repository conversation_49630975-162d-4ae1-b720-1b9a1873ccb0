package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatRuntimeException;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.utils.TxCtxUtils;
import java.util.List;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("VxLanTunnelFeatureTest")
@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
@Sql(statements = """
    INSERT INTO tenant (id) VALUES
        ('tenant1ac8279f79307415fa9e4c88a1'),
        ('tenant2cb5cab621ad14fa9a4a84ff10'),
        ('tenant3e3302f62d6cd414f9bac55983');
    INSERT INTO venue (id, tenant) VALUES
        ('venue1c6afb747a4987d0d0945f77221', 'tenant1ac8279f79307415fa9e4c88a1'),
        ('venue2c6afb747a4987d0d0945f77221', 'tenant1ac8279f79307415fa9e4c88a1'),
        ('venue3c6afb747a4987d0d0945f77221', 'tenant1ac8279f79307415fa9e4c88a1'),
        ('venue4c6afb747a4987d0d0945f77221', 'tenant1ac8279f79307415fa9e4c88a1');
    INSERT INTO sd_lan_profile (id, tenant) VALUES
        ('sdlan10ac6afb747a4987d0d0945f772', 'tenant1ac8279f79307415fa9e4c88a1'),
        ('sdlan20ac6afb747a4987d0d0945f772', 'tenant1ac8279f79307415fa9e4c88a1'),
        ('sdlan30ac6afb747a4987d0d0945f772', 'tenant1ac8279f79307415fa9e4c88a1'),
        ('sdlan40ac6afb747a4987d0d0945f772', 'tenant1ac8279f79307415fa9e4c88a1');
    INSERT INTO tunnel_profile (id, name, tenant, mtu_type, mtu_size, force_fragmentation) VALUES
        ('afc284d992694d5c9d7a2fcf2289a0bd', 'tunnel profile 1', 'tenant1ac8279f79307415fa9e4c88a1', 'MANUAL', 1450, false),
        ('qwert4d992694d5c9d7a2fcf2289a0bd', 'tunnel profile 2', 'tenant1ac8279f79307415fa9e4c88a1', 'MANUAL', 1450, false),
        ('guest4d992694d5c9d7a2fcf2289a0bd', 'tunnel profile 3', 'tenant1ac8279f79307415fa9e4c88a1', 'MANUAL', 1450, false),
        ('guest4d992694d5c9d7a2fcf2289a0be', 'tunnel profile 4', 'tenant1ac8279f79307415fa9e4c88a1', 'MANUAL', 1450, false);
    INSERT INTO sd_lan_profile_regular_setting (id, tenant, tunnel_profile, guest_traffic_tunnel_profile, sd_lan_profile, venue) VALUES
        ('rs10e13cc150444ca1956dd68c8999f2', 'tenant1ac8279f79307415fa9e4c88a1', 'afc284d992694d5c9d7a2fcf2289a0bd',
         'guest4d992694d5c9d7a2fcf2289a0bd', 'sdlan10ac6afb747a4987d0d0945f772', 'venue1c6afb747a4987d0d0945f77221'),
        ('rs20e13cc150444ca1956dd68c8999f2', 'tenant1ac8279f79307415fa9e4c88a1', 'guest4d992694d5c9d7a2fcf2289a0bd',
         'afc284d992694d5c9d7a2fcf2289a0bd', 'sdlan20ac6afb747a4987d0d0945f772', 'venue2c6afb747a4987d0d0945f77221');
    INSERT INTO sd_lan_profile_regular_setting (id, tenant, tunnel_profile, sd_lan_profile, venue) VALUES
        ('rs30e13cc150444ca1956dd68c8999f2', 'tenant1ac8279f79307415fa9e4c88a1', 'qwert4d992694d5c9d7a2fcf2289a0bd',
         'sdlan30ac6afb747a4987d0d0945f772', 'venue3c6afb747a4987d0d0945f77221'),
        ('rs40e13cc150444ca1956dd68c8999f2', 'tenant1ac8279f79307415fa9e4c88a1', 'afc284d992694d5c9d7a2fcf2289a0bd',
         'sdlan40ac6afb747a4987d0d0945f772', 'venue4c6afb747a4987d0d0945f77221');
     INSERT INTO sd_lan_profile_regular_setting (id, tenant, tunnel_profile, sd_lan_profile) VALUES
        ('rs50e13cc150444ca1956dd68c8999f3', 'tenant1ac8279f79307415fa9e4c88a1', 'guest4d992694d5c9d7a2fcf2289a0be',
         'sdlan10ac6afb747a4987d0d0945f772'),
        ('rs6e827d8a088944e4ca847a2f38ecdf', 'tenant2cb5cab621ad14fa9a4a84ff10', 'guest4d992694d5c9d7a2fcf2289a0be',
         'sdlan10ac6afb747a4987d0d0945f772'),
        ('rs7f24032d8c0f747098d68b461f8a84', 'tenant3e3302f62d6cd414f9bac55983', 'guest4d992694d5c9d7a2fcf2289a0be',
         'sdlan20ac6afb747a4987d0d0945f772');
    """)
class SdLanProfileRegularSettingRepositoryTest {
  private static final String TENANT_1_ID = "tenant1ac8279f79307415fa9e4c88a1";
  private static final String TENANT_2_ID = "tenant2cb5cab621ad14fa9a4a84ff10";
  private static final String TENANT_3_ID = "tenant3e3302f62d6cd414f9bac55983";
  private static final String TUNNEL_PROFILE_1_ID = "afc284d992694d5c9d7a2fcf2289a0bd";
  private static final String TUNNEL_PROFILE_2_ID = "qwert4d992694d5c9d7a2fcf2289a0bd";
  private static final String TUNNEL_PROFILE_3_ID = "guest4d992694d5c9d7a2fcf2289a0bd";
  private static final String VENUE_ID_1 = "venue1c6afb747a4987d0d0945f77221";
  private static final String VENUE_ID_2 = "venue2c6afb747a4987d0d0945f77221";
  private static final String SD_LAN_ID_1 = "sdlan10ac6afb747a4987d0d0945f772";
  private static final String SD_LAN_ID_2 = "sdlan20ac6afb747a4987d0d0945f772";

  @Autowired
  private SdLanProfileRegularSettingRepository target;

  @Test
  void countByTenantIdAndTunnelProfileOrGuestTrafficTunnelProfileIdTest() {
    assertThat(target.countByTenantIdAndTunnelProfileOrGuestTrafficTunnelProfileId(TENANT_1_ID,
        TUNNEL_PROFILE_1_ID)).isEqualTo(3);
    assertThat(target.countByTenantIdAndTunnelProfileOrGuestTrafficTunnelProfileId(TENANT_1_ID,
        TUNNEL_PROFILE_2_ID)).isEqualTo(1);
    assertThat(target.countByTenantIdAndTunnelProfileOrGuestTrafficTunnelProfileId(TENANT_1_ID,
        TUNNEL_PROFILE_3_ID)).isEqualTo(2);
  }

  @Test
  void findDistinctTenantIdsBySdLanProfileIdTest() {
    assertThatRuntimeException().isThrownBy(
            () -> target.findDistinctTenantIdsBySdLanProfileId(SD_LAN_ID_1))
        .withRootCauseExactlyInstanceOf(IllegalAccessException.class);

    TxCtxUtils.allowCrossingTenantQuery(SdLanProfileRegularSetting.class);

    assertThat(target.findDistinctTenantIdsBySdLanProfileId(SD_LAN_ID_1))
        .containsExactlyInAnyOrder(TENANT_1_ID, TENANT_2_ID);
    assertThat(target.findDistinctTenantIdsBySdLanProfileId(SD_LAN_ID_2))
        .containsExactlyInAnyOrder(TENANT_1_ID, TENANT_3_ID);
  }

  @Test
  void findByTunnelProfileIdOrGuestTrafficTunnelProfileIdAndTenantIdTest() {
    assertThat(target.findByTenantIdAndTunnelProfileId(TENANT_1_ID, TUNNEL_PROFILE_1_ID))
        .isNotEmpty()
        .hasSize(2);
    assertThat(target.findByTenantIdAndTunnelProfileId(TENANT_1_ID, TUNNEL_PROFILE_2_ID))
        .isNotEmpty()
        .hasSize(1);
    assertThat(target.findByTenantIdAndTunnelProfileId(TENANT_1_ID, TUNNEL_PROFILE_3_ID))
        .isNotEmpty()
        .hasSize(1);
    assertThat(target.findByTenantIdAndGuestTrafficTunnelProfileId(TENANT_1_ID, TUNNEL_PROFILE_1_ID))
        .isNotEmpty()
        .hasSize(1);
    assertThat(target.findByTenantIdAndGuestTrafficTunnelProfileId(TENANT_1_ID, TUNNEL_PROFILE_2_ID))
        .isEmpty();
    assertThat(target.findByTenantIdAndGuestTrafficTunnelProfileId(TENANT_1_ID, TUNNEL_PROFILE_3_ID))
        .isNotEmpty()
        .hasSize(1);
  }

  @Test
  void findByTenantIdAndVenueIdInTest() {
    assertThat(target.findByTenantIdAndVenueIdIn(TENANT_1_ID, List.of(VENUE_ID_1, VENUE_ID_2)))
        .hasSize(2);
  }

  @Test
  void existsByTenantIdAndVenueId() {
    assertTrue(target.existsByTenantIdAndVenueId(TENANT_1_ID, VENUE_ID_1));
    assertTrue(target.existsByTenantIdAndVenueId(TENANT_1_ID, VENUE_ID_2));
    assertFalse(target.existsByTenantIdAndVenueId(TENANT_1_ID, "nonExistVenueId"));
  }

  @Test
  void findByTenantIdAndSdLanProfileIdInAndVenueIsNotNullTest() {
    assertThat(target.findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(TENANT_1_ID,
        List.of(SD_LAN_ID_1, SD_LAN_ID_2)))
        .hasSize(2);
    assertThat(target.findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(TENANT_1_ID, List.of()))
        .isEmpty();
  }

  @Test
  void findByTenantIdAndVenueIsNotNullTest() {
    assertThat(target.findByTenantIdAndVenueIsNotNull(TENANT_1_ID))
        .hasSize(4);
  }
}
