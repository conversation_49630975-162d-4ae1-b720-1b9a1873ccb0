package com.ruckus.cloud.wifi.capabilities;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.ConfigDataApplicationContextInitializer;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ContextConfiguration;

@WifiUnitTest
@ContextConfiguration(initializers = ConfigDataApplicationContextInitializer.class)
class CloudSupportApModelUtilsTest {

  @Autowired
  private CloudSupportApModelUtils unit;

  @Test
  void testCloudSupportApModelFilter() throws IOException {
    List<String> supportApModels = unit.getCloudSupportApModels();
    assertEquals(48, supportApModels.size());
    assertTrue(supportApModels.contains("R720"));
    assertTrue(supportApModels.contains("T750"));
    assertTrue(supportApModels.contains("R760"));
    assertTrue(supportApModels.contains("R560"));
    assertTrue(supportApModels.contains("R770"));
    assertTrue(supportApModels.contains("R670"));
    assertTrue(supportApModels.contains("T670SN"));
    assertTrue(supportApModels.contains("R370"));

    JsonNode configNode = getResourceJsonNode("/capabilities/config.json");
    assertEquals(46, configNode.size());
    configNode = unit.filterCloudSupportApModels(configNode);
    assertEquals(42, configNode.size());
  }

  private JsonNode getResourceJsonNode(String resourceName) throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    String jsonString =
        IOUtils.toString(
            Objects.requireNonNull(
                this.getClass().getResourceAsStream(resourceName)),
            StandardCharsets.UTF_8);
    return objectMapper.readTree(jsonString);
  }

  @TestConfiguration
  @Import(CloudSupportApModels.class)
  static class TestConfig {
    @Bean
    public CloudSupportApModelUtils cloudSupportApModelUtils(
        CloudSupportApModels cloudSupportApModels) {
      return new CloudSupportApModelUtils(cloudSupportApModels);
    }
  }
}
