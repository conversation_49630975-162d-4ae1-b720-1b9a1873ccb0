package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.TenantTestFixture.randomTenant;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.groups.Tuple.tuple;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmVxLANTunnelProfile.VlanHandleType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.SdLanProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Deprecated(forRemoval = true) // For PoC Only
@Tag("VxLanTunnelFeatureTest")
@WifiUnitTest
class DdccmSdLanProfileRegularSettingOperationBuilderTest {

  @SpyBean
  private DdccmSdLanProfileRegularSettingOperationBuilder builder;

  @SpyBean
  private DdccmTunnelProfileOperationBuilder tunnelProfileBuilder;

  @MockBean
  private SdLanProfileRegularSettingRepository regularSettingRepository;

  @Test
  void testEntityClass() {
    assertThat(builder.entityClass()).isEqualTo(SdLanProfileRegularSetting.class);
  }

  @Test
  void testConfig() {
    assertThatExceptionOfType(UnsupportedOperationException.class)
        .isThrownBy(() -> builder.config(null, null, null, null))
        .withMessage("This method should not be called directly. Use build() method instead.");
  }

  @FeatureFlag(disable = FlagNames.EDGE_DELEGATION_POC_TOGGLE)
  @Nested
  class GivenEdgeDelegationPocToggleDisabled {

    @Test
    void testBuildShouldDoNothing(Tenant tenant) {
      final var regularSetting = Generators.sdLanProfileRegularSetting()
          .setSdLanProfile(Generators.sdLanProfile().setTenant(always(tenant)))
          .generate();

      final TxEntity<SdLanProfileRegularSetting> dummyTxEntity = mock(TxEntity.class);
      doReturn(regularSetting).when(dummyTxEntity).getEntity();

      assertThat(builder.build(dummyTxEntity, mock(TxChanges.class)))
          .isNotNull().isEmpty();

      verify(builder, never()).buildTunnelProfileOperation(any(), any(), any());
    }
  }

  @FeatureFlag(enable = FlagNames.EDGE_DELEGATION_POC_TOGGLE)
  @Nested
  class GivenEdgeDelegationPocToggleEnabled {

    @FeatureFlag(enable = FlagNames.EDGE_DELEGATION_TOGGLE)
    @Nested
    class GivenEdgeDelegationToggleEnabled {

      @Test
      void testBuildShouldDoNothing(Tenant tenant) {
        final var regularSetting = Generators.sdLanProfileRegularSetting()
            .setSdLanProfile(Generators.sdLanProfile().setTenant(always(tenant)))
            .generate();

        final TxEntity<SdLanProfileRegularSetting> dummyTxEntity = mock(TxEntity.class);
        doReturn(regularSetting).when(dummyTxEntity).getEntity();

        assertThat(builder.build(dummyTxEntity, mock(TxChanges.class)))
            .isNotNull().isEmpty();

        verify(builder, never()).buildTunnelProfileOperation(any(), any(), any());
      }
    }

    @Test
    void testBuildWithHasChangedAsFalse(Tenant tenant) {
      doReturn(false)
          .when(builder).hasChanged(any(TxEntity.class), any(TxChanges.class));

      final var regularSetting = Generators.sdLanProfileRegularSetting()
          .setSdLanProfile(Generators.sdLanProfile().setTenant(always(tenant)))
          .generate();

      final TxEntity<SdLanProfileRegularSetting> dummyTxEntity = mock(TxEntity.class);
      doReturn(regularSetting).when(dummyTxEntity).getEntity();

      assertThat(builder.build(dummyTxEntity, mock(TxChanges.class)))
          .isNotNull().isEmpty();

      verify(builder, never()).buildTunnelProfileOperation(any(), any(), any());
    }

    @Nested
    class GivenHasChangedAsAlwaysTrue {

      @BeforeEach
      void givenHasChangedAsAlwaysTrue() {
        doReturn(true)
            .when(builder).hasChanged(any(TxEntity.class), any(TxChanges.class));
      }

      @Test
      void testBuildWithSameTenantInSdLanProfileRegularSettingAndTunnelProfile(Tenant tenant) {
        final var regularSetting = Generators.sdLanProfileRegularSetting()
            .setSdLanProfile(Generators.sdLanProfile().setTenant(always(tenant)))
            .setTunnelProfile(Generators.tunnelProfile().setTenant(always(tenant)))
            .setTenant(always(tenant))
            .generate();

        final TxEntity<SdLanProfileRegularSetting> dummyTxEntity = mock(TxEntity.class);
        doReturn(regularSetting).when(dummyTxEntity).getEntity();

        assertThat(builder.build(dummyTxEntity, mock(TxChanges.class)))
            .isNotNull().isEmpty();

        verify(builder).buildTunnelProfileOperation(eq(dummyTxEntity),
            eq(regularSetting.getTunnelProfile()), any(TxChanges.class));
      }

      @Test
      void testBuildWithEcTenantRegularSettingAndOnlyTunnelProfile(Tenant ecTenant) {
        final Tenant mspTenant = randomTenant(t -> t.setName("MSP Tenant"));
        final var regularSetting = Generators.sdLanProfileRegularSetting()
            .setSdLanProfile(Generators.sdLanProfile().setTenant(always(mspTenant)))
            .setTunnelProfile(Generators.tunnelProfile().setTenant(always(mspTenant)))
            .setTenant(always(ecTenant))
            .generate();

        final TxEntity<SdLanProfileRegularSetting> dummyTxEntity = mock(TxEntity.class);
        doReturn(regularSetting).when(dummyTxEntity).getEntity();
        doReturn(EntityAction.MODIFY).when(dummyTxEntity).getAction();

        assertThat(builder.build(dummyTxEntity, mock(TxChanges.class)))
            .isNotNull()
            .singleElement()
            .satisfies(op -> {
              assertThat(op.getCommonInfo()).isNotNull()
                  .satisfies(commonInfo -> {
                    assertThat(commonInfo.getTenantId()).isEqualTo(ecTenant.getId());
                    assertThat(commonInfo.getRequestId()).isEqualTo(TxCtxHolder.txId());
                  });
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(regularSetting.getTunnelProfile().getId());
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile -> {
                    assertThat(ccmVxLANTunnelProfile.getId())
                        .isEqualTo(regularSetting.getTunnelProfile().getId());
                    assertThat(ccmVxLANTunnelProfile.getName())
                        .isEqualTo(regularSetting.getTunnelProfile().getName());
                    assertThat(ccmVxLANTunnelProfile.getVxlanVlanHandleType())
                        .isEqualTo(switch (regularSetting.getTunnelProfile().getType()) {
                          case VXLAN -> VlanHandleType.VXLAN_VNI_ONLY;
                          case VLAN_VXLAN -> VlanHandleType.VXLAN_VLAN_TO_VNI_MAP;
                        });
                  });
            });

        verify(builder).buildTunnelProfileOperation(eq(dummyTxEntity),
            eq(regularSetting.getTunnelProfile()), any(TxChanges.class));
      }

      @Test
      void testBuildWithEcTenantRegularSettingAndSameTunnelProfileInGuestTraffic(Tenant ecTenant) {
        final Tenant mspTenant = randomTenant(t -> t.setName("MSP Tenant"));
        final var tunnelProfile = Generators.tunnelProfile().setTenant(always(mspTenant))
            .generate();
        final var regularSetting = Generators.sdLanProfileRegularSetting()
            .setSdLanProfile(Generators.sdLanProfile().setTenant(always(mspTenant)))
            .setTunnelProfile(always(tunnelProfile))
            .setGuestTrafficTunnelProfile(always(tunnelProfile))
            .setTenant(always(ecTenant))
            .generate();

        final TxEntity<SdLanProfileRegularSetting> dummyTxEntity = mock(TxEntity.class);
        doReturn(regularSetting).when(dummyTxEntity).getEntity();
        doReturn(EntityAction.MODIFY).when(dummyTxEntity).getAction();

        assertThat(builder.build(dummyTxEntity, mock(TxChanges.class)))
            .isNotNull()
            .singleElement()
            .satisfies(op -> {
              assertThat(op.getCommonInfo()).isNotNull()
                  .satisfies(commonInfo -> {
                    assertThat(commonInfo.getTenantId()).isEqualTo(ecTenant.getId());
                    assertThat(commonInfo.getRequestId()).isEqualTo(TxCtxHolder.txId());
                  });
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getId()).isEqualTo(regularSetting.getTunnelProfile().getId());
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile -> {
                    assertThat(ccmVxLANTunnelProfile.getId())
                        .isEqualTo(regularSetting.getTunnelProfile().getId());
                    assertThat(ccmVxLANTunnelProfile.getName())
                        .isEqualTo(regularSetting.getTunnelProfile().getName());
                    assertThat(ccmVxLANTunnelProfile.getVxlanVlanHandleType())
                        .isEqualTo(switch (regularSetting.getTunnelProfile().getType()) {
                          case VXLAN -> VlanHandleType.VXLAN_VNI_ONLY;
                          case VLAN_VXLAN -> VlanHandleType.VXLAN_VLAN_TO_VNI_MAP;
                        });
                  });
            });

        verify(builder).buildTunnelProfileOperation(eq(dummyTxEntity),
            eq(regularSetting.getTunnelProfile()), any(TxChanges.class));
      }

      @Test
      void testBuildWithEcTenantRegularSettingAndDifferentTunnelProfileInGuestTraffic(
          Tenant ecTenant) {
        final Tenant mspTenant = randomTenant(t -> t.setName("MSP Tenant"));
        final var tunnelProfileGenerator = Generators.tunnelProfile().setTenant(always(mspTenant));
        final var regularSetting = Generators.sdLanProfileRegularSetting()
            .setSdLanProfile(Generators.sdLanProfile().setTenant(always(mspTenant)))
            .setTunnelProfile(tunnelProfileGenerator)
            .setGuestTrafficTunnelProfile(tunnelProfileGenerator)
            .setTenant(always(ecTenant))
            .generate();

        final TxEntity<SdLanProfileRegularSetting> dummyTxEntity = mock(TxEntity.class);
        doReturn(regularSetting).when(dummyTxEntity).getEntity();
        doReturn(EntityAction.MODIFY).when(dummyTxEntity).getAction();

        assertThat(builder.build(dummyTxEntity, mock(TxChanges.class)))
            .isNotNull().hasSize(2)
            .allSatisfy(op -> {
              assertThat(op.getCommonInfo()).isNotNull()
                  .satisfies(commonInfo -> {
                    assertThat(commonInfo.getTenantId()).isEqualTo(ecTenant.getId());
                    assertThat(commonInfo.getRequestId()).isEqualTo(TxCtxHolder.txId());
                  });
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
            })
            .extracting(Operation::getId,
                op -> op.getCcmVxLANTunnelProfile().getId(),
                op -> op.getCcmVxLANTunnelProfile().getName(),
                op -> op.getCcmVxLANTunnelProfile().getVxlanVlanHandleType())
            .containsExactlyInAnyOrder(
                tuple(regularSetting.getTunnelProfile().getId(),
                    regularSetting.getTunnelProfile().getId(),
                    regularSetting.getTunnelProfile().getName(),
                    switch (regularSetting.getTunnelProfile().getType()) {
                      case VXLAN -> VlanHandleType.VXLAN_VNI_ONLY;
                      case VLAN_VXLAN -> VlanHandleType.VXLAN_VLAN_TO_VNI_MAP;
                    }),
                tuple(regularSetting.getGuestTrafficTunnelProfile().getId(),
                    regularSetting.getGuestTrafficTunnelProfile().getId(),
                    regularSetting.getGuestTrafficTunnelProfile().getName(),
                    switch (regularSetting.getGuestTrafficTunnelProfile().getType()) {
                      case VXLAN -> VlanHandleType.VXLAN_VNI_ONLY;
                      case VLAN_VXLAN -> VlanHandleType.VXLAN_VLAN_TO_VNI_MAP;
                    }));

        verify(builder).buildTunnelProfileOperation(eq(dummyTxEntity),
            eq(regularSetting.getTunnelProfile()), any(TxChanges.class));
        verify(builder).buildTunnelProfileOperation(eq(dummyTxEntity),
            eq(regularSetting.getGuestTrafficTunnelProfile()), any(TxChanges.class));
      }
    }
  }

  @Nested
  class BuildTunnelProfileOperationTest {

    @Test
    void testWithSameTenantInSdLanProfileRegularSettingAndTunnelProfile(Tenant tenant) {
      final var tunnelProfile = Generators.tunnelProfile()
          .setTenant(always(tenant)).generate();
      final var regularSetting = Generators.sdLanProfileRegularSetting()
          .setSdLanProfile(Generators.sdLanProfile().setTenant(always(tenant)))
          .setTunnelProfile(Generators.tunnelProfile().setTenant(always(tenant)))
          .setTenant(always(tenant))
          .generate();

      final TxEntity<SdLanProfileRegularSetting> dummyTxEntity = mock(TxEntity.class);
      doReturn(regularSetting).when(dummyTxEntity).getEntity();

      assertThat(builder.buildTunnelProfileOperation(dummyTxEntity, tunnelProfile, mock(TxChanges.class)))
          .isNotNull().isEmpty();

      verify(tunnelProfileBuilder, never()).build(any(), any());
    }

    @Nested
    class GivenEcTenantRegularSetting {

      private TunnelProfile tunnelProfile;
      private SdLanProfileRegularSetting regularSetting;

      @BeforeEach
      void givenTunnelProfileAndEcTenantRegularSetting(Tenant mspTenant) {
        tunnelProfile = Generators.tunnelProfile()
            .setTenant(always(mspTenant)).generate();
        regularSetting = Generators.sdLanProfileRegularSetting()
            .setSdLanProfile(Generators.sdLanProfile().setTenant(always(mspTenant)))
            .setTunnelProfile(always(tunnelProfile))
            .setTenant(always(randomTenant(t -> t.setName("EC Tenant"))))
            .generate();
      }

      @ParameterizedTest
      @EnumSource(value = EntityAction.class)
      void testWithEntityAction(EntityAction action) {
        final TxEntity<SdLanProfileRegularSetting> dummyTxEntity = mock(TxEntity.class);
        doReturn(regularSetting).when(dummyTxEntity).getEntity();
        doReturn(action).when(dummyTxEntity).getAction();

        doReturn(0)
            .when(regularSettingRepository).countByTenantIdAndTunnelProfileOrGuestTrafficTunnelProfileId(
                regularSetting.getTenant().getId(), tunnelProfile.getId());

        if (action == EntityAction.DELETE || action == EntityAction.MODIFY) {
          assertThat(builder.buildTunnelProfileOperation(dummyTxEntity, tunnelProfile, mock(TxChanges.class)))
              .isNotNull().isNotEmpty();
          verify(tunnelProfileBuilder).build(any(TxEntity.class), any(TxChanges.class));
        } else {
          assertThat(builder.buildTunnelProfileOperation(dummyTxEntity, tunnelProfile, mock(TxChanges.class)))
              .isNotNull().isEmpty();
          verify(tunnelProfileBuilder, never()).build(any(), any());
        }

        clearInvocations(tunnelProfileBuilder);

        doReturn(1)
            .when(regularSettingRepository).countByTenantIdAndTunnelProfileOrGuestTrafficTunnelProfileId(
                regularSetting.getTenant().getId(), tunnelProfile.getId());

        if (action == EntityAction.ADD || action == EntityAction.MODIFY) {
          assertThat(builder.buildTunnelProfileOperation(dummyTxEntity, tunnelProfile, mock(TxChanges.class)))
              .isNotNull().isNotEmpty();
          verify(tunnelProfileBuilder).build(any(TxEntity.class), any(TxChanges.class));
        } else {
          assertThat(builder.buildTunnelProfileOperation(dummyTxEntity, tunnelProfile, mock(TxChanges.class)))
              .isNotNull().isEmpty();
          verify(tunnelProfileBuilder, never()).build(any(), any());
        }

        clearInvocations(tunnelProfileBuilder);

        doReturn(2)
            .when(regularSettingRepository).countByTenantIdAndTunnelProfileOrGuestTrafficTunnelProfileId(
                regularSetting.getTenant().getId(), tunnelProfile.getId());

        if (action == EntityAction.MODIFY) {
          assertThat(builder.buildTunnelProfileOperation(dummyTxEntity, tunnelProfile, mock(TxChanges.class)))
              .isNotNull().isNotEmpty();
          verify(tunnelProfileBuilder).build(any(TxEntity.class), any(TxChanges.class));
        } else {
          assertThat(builder.buildTunnelProfileOperation(dummyTxEntity, tunnelProfile, mock(TxChanges.class)))
              .isNotNull().isEmpty();
          verify(tunnelProfileBuilder, never()).build(any(), any());
        }
      }
    }
  }
}