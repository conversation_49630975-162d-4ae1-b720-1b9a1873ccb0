package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.core.model.Identifiable;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20NaiRealm;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NaiRealmEncodingEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.repository.Hotspot20NetworkRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@WifiUnitTest
class Hotspot20IdentityProviderCmnCfgCollectorOperationBuilderTest {

  @MockBean
  private Hotspot20NetworkRepository hotspot20NetworkRepository;

  @SpyBean
  private Hotspot20IdentityProviderCmnCfgCollectorOperationBuilder hotspot20IdentityProviderCmnCfgCollectorOperationBuilder;

  @Test
  void testGetEntityClass() {
    assertThat(hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.entityClass())
        .isEqualTo(Hotspot20IdentityProvider.class);
  }

  @Test
  void testGetIndex() {
    assertThat(hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.index()).isEqualTo(
        EsConstants.Index.HOTSPOT20_IDENTITY_PROVIDER_INDEX_NAME);
  }

  @Nested
  class TestBuildConfig {

    @Test
    void givenEntityActionIsDelete() {
      Operations operations = hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.build(
          new TxEntity<>(Generators.hotspot20IdentityProvider().generate(), EntityAction.DELETE), emptyTxChanges()).get(0);

      assertThat(operations.getDocCount())
          .isZero();
    }

    @Test
    void givenAddIdentityProvider(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20IdentityProvider hotspot20IdentityProvider = Generators.hotspot20IdentityProvider()
          .setTenant(always(tenant))
          .generate();

      hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.config(builder, hotspot20IdentityProvider, EntityAction.ADD);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20IdentityProvider, docMap);
    }
  }

  static class IdentifiableImpl implements Identifiable {

    String id;

    IdentifiableImpl(String id) {
      this.id = id;
    }

    @Override
    public String getId() {
      return id;
    }
  }

  @Nested
  class TestHasModification {

    @Test
    void givenEntityActionIsNotModify() {
      assertThat(hotspot20IdentityProviderCmnCfgCollectorOperationBuilder
          .hasChanged(new NewTxEntity<>(Generators.hotspot20IdentityProvider().generate()), emptyTxChanges()))
          .isTrue();
    }

    @Test
    void givenUpdateIdentityProvider(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20IdentityProvider hotspot20IdentityProvider = Generators.hotspot20IdentityProvider()
          .setTenant(always(tenant))
          .generate();

      hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.config(builder, hotspot20IdentityProvider, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20IdentityProvider, docMap);
    }

    @Test
    void givenUpdateIdentityProvider_NoEap(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20IdentityProvider hotspot20IdentityProvider =
          Generators.hotspot20IdentityProvider(1,1,1,0,0)
              .setTenant(always(tenant)).generate();

      hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.config(builder, hotspot20IdentityProvider, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20IdentityProvider, docMap);
    }

    @Test
    void givenUpdateIdentityProvider_NoAuthInfo(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20IdentityProvider hotspot20IdentityProvider =
          Generators.hotspot20IdentityProvider(1,1,1,1,0)
              .setTenant(always(tenant)).generate();

      hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.config(builder, hotspot20IdentityProvider, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20IdentityProvider, docMap);
    }

    @Test
    void givenUpdateIdentityProvider_NoPlmnNoRoam(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20IdentityProvider hotspot20IdentityProvider =
          Generators.hotspot20IdentityProvider(0,0,1,0,0)
              .setTenant(always(tenant)).generate();

      hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.config(builder, hotspot20IdentityProvider, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20IdentityProvider, docMap);
    }

    @Test
    void givenUpdateIdentityProvider_nullEapPlmnRoam(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20IdentityProvider hotspot20IdentityProvider =
          Generators.hotspot20IdentityProvider(null, null, 1, null, null)
              .setTenant(always(tenant)).generate();

      hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.config(builder, hotspot20IdentityProvider, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20IdentityProvider, docMap);
    }

    @Test
    void givenUpdateIdentityProvider_nullAuthInfo(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20IdentityProvider hotspot20IdentityProvider =
          Generators.hotspot20IdentityProvider(1, 1, 1, 1, null)
              .setTenant(always(tenant)).generate();

      hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.config(builder, hotspot20IdentityProvider, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20IdentityProvider, docMap);
    }

    @Test
    void givenUpdateIdentityProvider_nullEap(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20IdentityProvider hotspot20IdentityProvider =
          Generators.hotspot20IdentityProvider(1, 1, 1, null, null)
              .setTenant(always(tenant)).generate();

      hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.config(builder, hotspot20IdentityProvider, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20IdentityProvider, docMap);
    }

    @Test
    void givenUpdateIdentityProviderWithNetworkIds(Tenant tenant) {
      Operations.Builder builder = Operations.newBuilder();
      Hotspot20IdentityProvider hotspot20IdentityProvider = Generators.hotspot20IdentityProvider()
          .setTenant(always(tenant))
          .generate();
      List<Identifiable> networkIds = defaultIdGenerator().toListGenerator(3).generate()
          .stream().map(id -> (Identifiable) new IdentifiableImpl(id)).toList();

      when(hotspot20NetworkRepository.findDistinctByTenantIdAndHotspot20SettingsIdentityProvidersIdIn(eq(tenant.getId()),
          eq(List.of(hotspot20IdentityProvider.getId()))))
          .thenReturn(networkIds);

      hotspot20IdentityProviderCmnCfgCollectorOperationBuilder.config(builder, hotspot20IdentityProvider, EntityAction.MODIFY);
      Map<String, Value> docMap = builder.build().getDocMap();

      validateResult(hotspot20IdentityProvider, docMap, networkIds.stream().map(Identifiable::getId).toList());
    }
  }

  private void validateResult(Hotspot20IdentityProvider hotspot20IdentityProvider, Map<String, Value> docMap) {
    validateResult(hotspot20IdentityProvider, docMap, Collections.emptyList());
  }

  private void validateResult(Hotspot20IdentityProvider hotspot20IdentityProvider, Map<String, Value> docMap, List<String> wifiNetworkId) {
    assertThat(docMap.get(EsConstants.Key.ID))
        .matches(doc -> doc.getKindCase() == Value.KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(hotspot20IdentityProvider.getId());

    assertThat(docMap.get(EsConstants.Key.NAME))
        .matches(doc -> doc.getKindCase() == Value.KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(hotspot20IdentityProvider.getName());

    assertThat(docMap.get(EsConstants.Key.TENANT_ID))
        .matches(doc -> doc.getKindCase() == Value.KindCase.STRING_VALUE)
        .extracting(Value::getStringValue)
        .isEqualTo(hotspot20IdentityProvider.getTenant().getId());

    assertThat(docMap.get(EsConstants.Key.NAI_REALMS).getListValue().getValuesList().stream().map(Value::getStructValue).toList())
        .extracting(Struct::getFieldsMap)
        .allMatch(map -> hotspot20IdentityProvider.getNaiRealms().stream().map(Hotspot20NaiRealm::getName)
            .anyMatch(name -> name.equals(map.get(EsConstants.Key.NAME).getStringValue())))
        .allMatch(map -> hotspot20IdentityProvider.getNaiRealms().stream().map(Hotspot20NaiRealm::getEncoding)
            .map(NaiRealmEncodingEnum::toString)
            .anyMatch(encoding -> encoding.equals(map.get(EsConstants.Key.ENCODING).getStringValue())))
        .filteredOn(m -> m.get(EsConstants.Key.EAPS) != null)
        .extracting(m -> m.get(EsConstants.Key.EAPS).getListValue().getValuesList().stream().map(Value::getStructValue).toList())
        .allMatch(eap ->
            eap.stream().map(Struct::getFieldsMap).map(f -> f.get(EsConstants.Key.METHOD)).allMatch(Objects::nonNull)
        );

    if (hotspot20IdentityProvider.getPlmns() == null || hotspot20IdentityProvider.getPlmns().isEmpty()) {
      assertThat(docMap.get(EsConstants.Key.PLMNS))
          .extracting(Value::getStringValue)
          .isEqualTo("");
    } else {
      assertThat(docMap.get(EsConstants.Key.PLMNS).getListValue().getValuesList().stream().map(Value::getStructValue).toList())
          .hasSize(hotspot20IdentityProvider.getPlmns().size())
          .extracting(Struct::getFieldsMap)
          .allSatisfy(map -> {
            assertThat(map.get(EsConstants.Key.MCC)).isNotNull();
            assertThat(map.get(EsConstants.Key.MNC)).isNotNull();
          });
    }

    if (hotspot20IdentityProvider.getRoamConsortiumOis() == null || hotspot20IdentityProvider.getRoamConsortiumOis().isEmpty()) {
      assertThat(docMap.get(EsConstants.Key.ROAM_CONSORTIUM_OIS))
          .extracting(Value::getStringValue)
          .isEqualTo("");
    } else {
      assertThat(docMap.get(EsConstants.Key.ROAM_CONSORTIUM_OIS).getListValue().getValuesList().stream().map(Value::getStructValue).toList())
          .hasSize(hotspot20IdentityProvider.getRoamConsortiumOis().size())
          .extracting(Struct::getFieldsMap)
          .allSatisfy(map -> {
            assertThat(map.get(EsConstants.Key.NAME)).isNotNull();
            assertThat(map.get(EsConstants.Key.ORGANIZATION_ID)).isNotNull()
                .extracting(Value::getStringValue)
                .extracting(String::length)
                .isIn(6, 10);
          });
    }

    assertThat(docMap.get(EsConstants.Key.WIFI_NETWORK_IDS))
        .matches(doc -> doc.getKindCase() == Value.KindCase.LIST_VALUE)
        .extracting(p -> p.getListValue().getValuesList().stream().map(Value::getStringValue).toList())
        .isEqualTo(wifiNetworkId);
  }

}
