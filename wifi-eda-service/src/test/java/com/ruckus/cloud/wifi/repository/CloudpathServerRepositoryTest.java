package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Deprecated(forRemoval = true)
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant)
        VALUES ('e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO cloudpath_server (id, tenant, deployed_in_venue)
        VALUES ('900000005015', '4c8279f79307415fa9e4c88a1819f0fc', 'e465bac6afb747a4987d0d0945f77221');
    """)
public class CloudpathServerRepositoryTest {

  private static final String ID = "900000005015";

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";

  private static final String VENUE_ID = "e465bac6afb747a4987d0d0945f77221";

  @Autowired
  private CloudpathServerRepository cloudpathServerRepository;

  @Test
  void findIdByTenantIdAndDeployedInVenueIdTest() {
    var result = cloudpathServerRepository.findIdByTenantIdAndDeployedInVenueId(TENANT_ID, VENUE_ID);
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(ID, result.get(0));
  }
}
