package com.ruckus.cloud.wifi.integration.radius;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_DPSK3_NON_PROXY_MODE_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_NETWORK_RADIUS_ACCOUNTING_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20IdentityProvider;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadiusProfileTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueRadiusServerProfileSettings;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.AuthRadiusProfileRepository;
import com.ruckus.cloud.wifi.repository.AuthRadiusServiceRepository;
import com.ruckus.cloud.wifi.repository.AuthRadiusVenueRepository;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.DPSKNetworkTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeActivateRadiusServerProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private AuthRadiusServiceRepository authRadiusServiceRepository;
  @Autowired
  private AuthRadiusProfileRepository authRadiusProfileRepository;
  @Autowired
  private AuthRadiusVenueRepository authRadiusVenueRepository;

  public static final String RADIUS_ID = "radiusId";
  public static final String WIFI_NETWORK_ID = "wifiNetworkId";

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeActivateRadiusServerProfileOnWifiNetworkMessage {

    private String profileId;
    private String networkId;

    @BeforeEach
    void beforeEach(Radius radius, Network network) {
      profileId = radius.getId();
      networkId = network.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Nested
    class GivenNetworkExists {

      String networkId;

      @BeforeEach
      void beforeEach(Tenant tenant, Network network, Radius radius) {
        networkId = network.getId();
        repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
        network.setAuthRadius(null);
        network.getWlan().setMacAddressAuthentication(true);
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      }

      @Test
      void givenRadiusServerProfileActivatedOnNetwork(Tenant tenant) {
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        assertThat(
            messageCaptors
                .getCmnCfgCollectorMessageCaptor()
                .getValue(tenant.getId(), Duration.ofSeconds(1)))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(2);

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a -> a.getStep().equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));

        // Verify no auth radius service
        Network network = repositoryUtil.find(Network.class, networkId);
        List<AuthRadiusService> authRadiusServices = authRadiusServiceRepository
            .findByTenantIdAndRadiusId(tenant.getId(), network.getAuthRadius().getId());
        assertThat(authRadiusServices).isEmpty();

        List<AuthRadiusVenue> authRadiusVenues = authRadiusVenueRepository
            .findByRadiusIdAndTenantId(network.getAuthRadius().getId(), tenant.getId());
        assertThat(authRadiusVenues).isEmpty();
      }
    }

    @Nested
    class GivenNetworkDisabledAuthProxy {

      String networkId;

      @BeforeEach
      void beforeEach(Tenant tenant, Venue venue, Network network, Radius radius) {
        networkId = network.getId();

        NetworkVenue networkVenue = new NetworkVenue();
        networkVenue.setNetwork(network);
        networkVenue.setVenue(venue);
        repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

        repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
        network.setAuthRadius(radius);
        network.getWlan().setMacAddressAuthentication(true);
        network.setNetworkVenues(List.of(networkVenue));  // must have networkVenue
        network.setEnableAuthProxy(false);    // <- disable auth proxy
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      }

      @Test
      void givenRadiusVenueShouldBeCreated(Tenant tenant) {

        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        Network network = repositoryUtil.find(Network.class, networkId);

        // Verify auth radius venue created
        List<AuthRadiusVenue> authRadiusVenues = authRadiusVenueRepository
            .findByRadiusIdAndTenantId(network.getAuthRadius().getId(), tenant.getId());
        assertThat(authRadiusVenues).hasSize(1);

      }
    }

    @Nested
    class GivenNetworkExistsEnableAuthProxy {

      String networkId;

      @BeforeEach
      void beforeEach(Tenant tenant, Network network, Radius radius) {
        networkId = network.getId();
        repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
        network.setAuthRadius(null);
        network.getWlan().setMacAddressAuthentication(true);
        network.setEnableAuthProxy(true);    // <- Enable auth proxy
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      }

      @Test
      void givenRadiusServerProfileActivatedOnNetworkForRadiusService(Tenant tenant) {

        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
            randomName(),
            requestParams(),
            "");

        Network network = repositoryUtil.find(Network.class, networkId);

        // Verify auth radius service & profile created
        List<AuthRadiusService> authRadiusServices = authRadiusServiceRepository
            .findByTenantIdAndRadiusId(tenant.getId(), network.getAuthRadius().getId());
        assertThat(authRadiusServices).hasSize(1);

        AuthRadiusProfile authRadiusProfile = authRadiusProfileRepository
            .findByTenantIdAndAuthRadiusServiceId(tenant.getId(),
                authRadiusServices.get(0).getId());
        assertThat(authRadiusProfile).isNotNull();
      }
    }

    @Test
    void givenMacAuthDisabledActivatedOnNetwork(Tenant tenant, Network network, Radius radius) {
      repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
      network.getWlan().setMacAddressAuthentication(false);
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
                  randomName(),
                  requestParams(),
                  ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class)
          .hasMessage(Errors.WIFI_10523.message());

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a ->
                  a.getStep()
                      .equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    void givenMacRegistrationPoolActivatedOnNetwork(Tenant tenant, Network network, Radius radius) {
      repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
      network.getWlan().setMacAddressAuthentication(true);
      network.getWlan().setMacRegistrationListId("macRegistrationListId");
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
                  randomName(),
                  requestParams(),
                  ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class)
          .hasMessage(
              "The OPEN network's mac address authentication is already enabled and bind with mac registration pool: [macRegistrationListId]");

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a ->
                  a.getStep()
                      .equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }
  }

  @Nested
  class WhenConsumeActivateRadiusServerProfileOnDpskNetwork {

    private String profileId;
    private String networkId;

    @BeforeEach
    void beforeEach(Radius radius, DpskNetwork network) {
      profileId = radius.getId();
      networkId = network.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Test
    void givenDpskServiceProfileActivatedOnDpskNetwork(Tenant tenant,
        Radius radius) {
      repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
      final var dpskNetwork = (DpskNetwork) network(DpskNetwork.class).generate();
      dpskNetwork.getWlan().setNetwork(dpskNetwork);
      dpskNetwork.setDpskServiceProfileId("dpskServiceProfileId");
      dpskNetwork.setUseDpskService(true);
      repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());
      networkId = dpskNetwork.getId();

      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
                  randomName(),
                  requestParams(),
                  ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class)
          .hasMessage(
              "Cannot activate RADIUS server profile on DPSK network with DPSK Service configured.");

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a ->
                  a.getStep()
                      .equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }
  }

  @Nested
  class WhenConsumeActivateRadiusServerProfileOnDsaeNetwork {

    private String dsaeServiceNetworkId;
    private String dsaeOnboardNetworkId;
    private String profileId;

    @BeforeEach
    void givenDsaeServiceAndOnboardNetworksPersistedInDb(Tenant tenant, AuthRadiusService authRadiusService) {
      profileId = authRadiusService.getRadius().getId();
      final var dsaeServiceNetwork = repositoryUtil.createOrUpdate(
          DPSKNetworkTestFixture.randomDpskNetwork(tenant, authRadiusService,
              network -> {
                network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
                network.setIsDsaeServiceNetwork(true);
                network.setUseDpskService(false);
              }), tenant.getId(), randomTxId());
      dsaeServiceNetworkId = dsaeServiceNetwork.getId();
      final var dsaeOnboardNetwork = repositoryUtil.createOrUpdate(
          DPSKNetworkTestFixture.randomDpskNetwork(tenant, authRadiusService,
              network -> {
                network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
                network.setUseDpskService(false);
                network.setIsDsaeServiceNetwork(false);
                network.setDsaeNetworkPairId(dsaeServiceNetworkId);
              }), tenant.getId(), randomTxId());
      dsaeOnboardNetworkId = dsaeOnboardNetwork.getId();
      dsaeServiceNetwork.setDsaeNetworkPairId(dsaeServiceNetworkId);
      repositoryUtil.createOrUpdate(dsaeServiceNetwork, tenant.getId(), randomTxId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams1() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, dsaeServiceNetworkId);
    }

    @ApiAction.RequestParams
    private RequestParams requestParams2() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, profileId)
          .addPathVariable(WIFI_NETWORK_ID, dsaeOnboardNetworkId);
    }

    @Test
    void givenRadiusServiceProfileActivatedOnDpsk3OnboardNetworkFail(Tenant tenant) {
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
                  randomName(),
                  requestParams1(),
                  ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class)
          .hasMessage(
              "Cannot activate RADIUS server profile on DPSK3 network ["
                  + dsaeServiceNetworkId + "] as DPSK3 network is not supported.");
    }

    @Test
    @FeatureFlag(enable = WIFI_DPSK3_NON_PROXY_MODE_TOGGLE)
    void givenRadiusServiceProfileActivatedOnDpsk3OnboardNetwork(Tenant tenant) {
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
                  randomName(),
                  requestParams2(),
                  ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class)
          .hasMessage(
              "Cannot activate RADIUS server profile on DPSK3 onboard network ["
                  + dsaeOnboardNetworkId + "].");

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a ->
                  a.getStep()
                      .equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    @FeatureFlag(enable = WIFI_DPSK3_NON_PROXY_MODE_TOGGLE)
    void givenRadiusServiceProfileWithRadSecOnActivatedOnDsaeServiceNetwork(Tenant tenant) {
      var network = repositoryUtil.find(Network.class, dsaeServiceNetworkId);
      network.setEnableAuthProxy(false);
      network.setEnableAccountingProxy(false);
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      var radius = repositoryUtil.find(Radius.class, profileId);
      radius.getRadSecOptions().setTlsEnabled(true);
      repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
                  randomName(),
                  requestParams1(),
                  ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class)
          .hasMessage(
              "Cannot activate RADIUS server profile on DPSK3 network [" + dsaeServiceNetworkId
                  + "] with RadSec enabled.");

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a ->
                  a.getStep()
                      .equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    @FeatureFlag(enable = WIFI_DPSK3_NON_PROXY_MODE_TOGGLE)
    void givenRadiusServiceProfileWithProxyModeActivatedOnDpsk3ServiceNetwork(Tenant tenant) {
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
                  randomName(),
                  requestParams1(),
                  ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class)
          .hasMessage(
              "Cannot activate RADIUS server profile on DPSK3 network [" + dsaeServiceNetworkId
                  + "] with Auth RADIUS proxy mode enabled. Need to update network's RADIUS setting to non-proxy mode.");

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a ->
                  a.getStep()
                      .equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }
  }

  @Nested
  class WhenConsumeActivateRadiusServerProfileOnIdentityProvider {

    private String radiusId;
    private String identityProviderId;
    private final String authRadiusIdDocKey = "authRadiusId";
    private final String IDENTITY_PROVIDER_ID = "hotspot20IdentityProviderId";


    @BeforeEach
    void beforeEach(Tenant tenant, Radius radius) {
      radiusId = radius.getId();
      repositoryUtil.createOrUpdate(radius, tenant.getId(), randomTxId());

      Hotspot20IdentityProvider hotspot20IdentityProvider =
          createAndSaveIdentityProvider(tenant);
      identityProviderId = hotspot20IdentityProvider.getId();

    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, radiusId)
          .addPathVariable(IDENTITY_PROVIDER_ID, identityProviderId);
    }

    @Test
    void givenRadiusServerProfileActivatedOnIdentityProvider(Tenant tenant) {

      String requestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          requestId,
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_HOTSPOT20IDENTITY_PROVIDER,
          randomName(),
          requestParams(),
          "");

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId(), requestId);

      assertEquals(2, cmnCfgCollectorMessage.getPayload().getOperationsList().size());

      List<Operations> operationsList = cmnCfgCollectorMessage.getPayload().getOperationsList();
      Optional<Operations> providerOps = operationsList.stream().filter(operations ->
          operations.getIndex().equals(Index.HOTSPOT20_IDENTITY_PROVIDER_INDEX_NAME)).findFirst();

      // Verify sent authRadiusId to cmnCfg
      assertEquals(radiusId, providerOps.get().getDocOrThrow(authRadiusIdDocKey).getStringValue());

    }

    private Hotspot20IdentityProvider createAndSaveIdentityProvider(Tenant tenant) {

      var hotspot20NaiRealm = Generators.hotspot20NaiRealm().generate();
      hotspot20NaiRealm.getEaps().forEach(e -> {
        e.setNaiRealm(hotspot20NaiRealm);
        e.getAuthInfos().forEach(a -> a.setEap(e));
      });
      var hotspot20Plmn = Generators.hotspot20Plmn().generate();
      var hotspot20RoamConsortium = Generators.hotspot20RoamConsortium().generate();

      Hotspot20IdentityProvider hotspot20IdentityProvider = Generators.hotspot20IdentityProvider()
          .generate();
      hotspot20IdentityProvider.setNaiRealms(List.of(hotspot20NaiRealm));
      hotspot20IdentityProvider.setPlmns(List.of(hotspot20Plmn));
      hotspot20IdentityProvider.setRoamConsortiumOis(List.of(hotspot20RoamConsortium));

      hotspot20NaiRealm.setIdentityProvider(hotspot20IdentityProvider);
      hotspot20Plmn.setIdentityProvider(hotspot20IdentityProvider);
      hotspot20RoamConsortium.setIdentityProvider(hotspot20IdentityProvider);

      return repositoryUtil.createOrUpdate(hotspot20IdentityProvider, tenant.getId(), randomTxId());
    }
  }

  @Nested
  class WhenConsumeActivateRadiusServerProfileWithAccounting {

    private String authRadiusId;
    private String acctRadiusId;
    private String networkId;

    @BeforeEach
    void beforeEach(Tenant tenant, Network network) {
      // Create authentication RADIUS profile
      Radius authRadius = Generators.radiusProfile().generate();
      authRadius.setType(RadiusProfileTypeEnum.AUTHENTICATION);
      authRadius = repositoryUtil.createOrUpdate(authRadius, tenant.getId(), randomTxId());
      authRadiusId = authRadius.getId();

      // Create accounting RADIUS profile
      Radius acctRadius = Generators.radiusProfile().generate();
      acctRadius.setType(RadiusProfileTypeEnum.ACCOUNTING);
      acctRadius = repositoryUtil.createOrUpdate(acctRadius, tenant.getId(), randomTxId());
      acctRadiusId = acctRadius.getId();

      // network
      networkId = network.getId();
    }

    private RequestParams authRequestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, authRadiusId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    private RequestParams acctRequestParams() {
      return new RequestParams()
          .addPathVariable(RADIUS_ID, acctRadiusId)
          .addPathVariable(WIFI_NETWORK_ID, networkId);
    }

    @Test
    @FeatureFlag(enable = WIFI_NETWORK_RADIUS_ACCOUNTING_TOGGLE)
    void activateAccountingRadiusSuccessfully_openNetwork(Tenant tenant, Network network) {
      // arrange
      String acctRequestId = randomTxId();
      network.getWlan().setMacAddressAuthentication(true);
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      Network networkBeforeUpdate = repositoryUtil.find(Network.class, networkId);
      assertTrue(networkBeforeUpdate.getWlan().getMacAddressAuthentication());
      assertNull(networkBeforeUpdate.getAccountingRadius());
      
      // act
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          acctRequestId,
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
          randomName(),
          acctRequestParams(),
          "");
      
      // assert - Verify accounting RADIUS was activated on the network
      Network updatedNetwork = repositoryUtil.find(Network.class, networkId);
      assertNotNull(updatedNetwork.getAccountingRadius());
      assertEquals(acctRadiusId, updatedNetwork.getAccountingRadius().getId());
      
      // Verify configuration messages were sent correctly
      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId(), acctRequestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(2);
      
      // Verify activity status shows successful completion
      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId(), acctRequestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    void activateAuthRadiusFail_openNetwork(Tenant tenant, Network network) {
      // arrange
      var requestId = txCtxExtension.getRequestId();
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      Network networkBeforeUpdate = repositoryUtil.find(Network.class, networkId);
      assertFalse(networkBeforeUpdate.getWlan().getMacAddressAuthentication());
      
      // act & assert
      assertThatThrownBy(() -> 
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              requestId,
              CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
              randomName(),
              authRequestParams(),
              ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);
      
      Network resultNetwork = repositoryUtil.find(Network.class, networkId);
      assertNull(resultNetwork.getAuthRadius());

      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId(), requestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    void activateAccountingRadiusFail_openNetworkAndFFDisabled(Tenant tenant, Network network) {
      // arrange
      var requestId = txCtxExtension.getRequestId();
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
      Network networkBeforeUpdate = repositoryUtil.find(Network.class, networkId);
      assertFalse(networkBeforeUpdate.getWlan().getMacAddressAuthentication());

      // act & assert
      assertThatThrownBy(() ->
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              requestId,
              CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
              randomName(),
              acctRequestParams(),
              ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      Network resultNetwork = repositoryUtil.find(Network.class, networkId);
      assertNull(resultNetwork.getAccountingRadius());

      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId(), requestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    @FeatureFlag(enable = WIFI_NETWORK_RADIUS_ACCOUNTING_TOGGLE)
    void activateAccountingRadiusSuccessfully_AAANetwork(Tenant tenant) {
      // arrange
      String acctRequestId = randomTxId();
      final var aaaNetwork = network(AAANetwork.class).generate();
      aaaNetwork.getWlan().setNetwork(aaaNetwork);
      aaaNetwork.setUseCertificateTemplate(true);
      repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId(), randomTxId());
      networkId = aaaNetwork.getId();

      AAANetwork networkBeforeUpdate = repositoryUtil.find(AAANetwork.class, networkId);
      assertNull(networkBeforeUpdate.getAccountingRadius());

      // act
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          acctRequestId,
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
          randomName(),
          acctRequestParams(),
          "");

      // assert - Verify accounting RADIUS was activated on the network
      AAANetwork updatedNetwork = repositoryUtil.find(AAANetwork.class, networkId);
      assertNotNull(updatedNetwork.getAccountingRadius());
      assertEquals(acctRadiusId, updatedNetwork.getAccountingRadius().getId());

      // Verify configuration messages were sent correctly
      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId(), acctRequestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(2);

      // Verify activity status shows successful completion
      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId(), acctRequestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    void activateAuthRadiusFail_AAANetwork(Tenant tenant) {
      // arrange
      var requestId = txCtxExtension.getRequestId();

      final var aaaNetwork = network(AAANetwork.class).generate();
      aaaNetwork.getWlan().setNetwork(aaaNetwork);
      aaaNetwork.setUseCertificateTemplate(true);
      repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId(), randomTxId());
      networkId = aaaNetwork.getId();

      AAANetwork networkBeforeUpdate = repositoryUtil.find(AAANetwork.class, networkId);
      assertNull(networkBeforeUpdate.getAccountingRadius());

      // act & assert
      assertThatThrownBy(() ->
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              requestId,
              CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
              randomName(),
              authRequestParams(),
              ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      AAANetwork resultNetwork = repositoryUtil.find(AAANetwork.class, networkId);
      assertNull(resultNetwork.getAuthRadius());

      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId(), requestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    @FeatureFlag(enable = WIFI_NETWORK_RADIUS_ACCOUNTING_TOGGLE)
    void activateAccountingRadiusSuccessfully_dpskNetwork(Tenant tenant) {
      // arrange
      String acctRequestId = randomTxId();
      final var dpskNetwork = (DpskNetwork) network(DpskNetwork.class).generate();
      dpskNetwork.setUseDpskService(true);
      dpskNetwork.getWlan().setNetwork(dpskNetwork);
      repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());
      networkId = dpskNetwork.getId();

      var networkBeforeUpdate = repositoryUtil.find(DpskNetwork.class, networkId);
      assertNull(networkBeforeUpdate.getAccountingRadius());

      // act
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          acctRequestId,
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
          randomName(),
          acctRequestParams(),
          "");

      // assert - Verify accounting RADIUS was activated on the network
      var updatedNetwork = repositoryUtil.find(DpskNetwork.class, networkId);
      assertNotNull(updatedNetwork.getAccountingRadius());
      assertEquals(acctRadiusId, updatedNetwork.getAccountingRadius().getId());

      // Verify configuration messages were sent correctly
      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenant.getId(), acctRequestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(2);

      // Verify activity status shows successful completion
      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId(), acctRequestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    void activateAccountingRadiusFail_dpskNetwork(Tenant tenant) {
      // arrange
      String requestId = txCtxExtension.getRequestId();
      final var dpskNetwork = (DpskNetwork) network(DpskNetwork.class).generate();
      dpskNetwork.setUseDpskService(true);
      dpskNetwork.getWlan().setNetwork(dpskNetwork);
      repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());
      networkId = dpskNetwork.getId();

      var networkBeforeUpdate = repositoryUtil.find(DpskNetwork.class, networkId);
      assertNull(networkBeforeUpdate.getAccountingRadius());

      // act
      assertThatThrownBy(() ->
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              requestId,
              CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
              randomName(),
              acctRequestParams(),
              ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      Network resultNetwork = repositoryUtil.find(Network.class, networkId);
      assertNull(resultNetwork.getAccountingRadius());

      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId(), requestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }

    @Test
    void activateAuthRadiusFail_dpskNetwork(Tenant tenant) {
      // arrange
      var requestId = txCtxExtension.getRequestId();

      final var dpskNetwork = (DpskNetwork) network(DpskNetwork.class).generate();
      dpskNetwork.setUseDpskService(true);
      dpskNetwork.getWlan().setNetwork(dpskNetwork);
      dpskNetwork.setUseDpskService(true);
      repositoryUtil.createOrUpdate(dpskNetwork, tenant.getId(), randomTxId());
      networkId = dpskNetwork.getId();

      var networkBeforeUpdate = repositoryUtil.find(DpskNetwork.class, networkId);
      assertNull(networkBeforeUpdate.getAuthRadius());

      // act & assert
      assertThatThrownBy(() ->
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              requestId,
              CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK,
              randomName(),
              authRequestParams(),
              ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      var resultNetwork = repositoryUtil.find(DpskNetwork.class, networkId);
      assertNull(resultNetwork.getAuthRadius());

      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId(), requestId))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_RADIUS_SERVER_PROFILE_ON_WIFI_NETWORK));
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_VENUE_RADIUS_CUSTOMIZATION_TOGGLE)
  class whenConsumeActivateRadiusServerOnVenueCfgRequestTest {

    private String tenantId;

    private String venueId;

    private String venueAuthRadiusId;

    private String venueAcctRadiusId;

    /**
     * Scenario:
     * 1. Create an open network with networkAuthRadius and networkAcctRadius
     * 2. Activate the network with a venue (At the time, networkAuthRadiusVenue and networkAcctRadiusVenue are created)
     * 3. Update both overrideAuthRadius and overrideAccountingRadius as true on the venue
     */
    @BeforeEach
    void persistedInDb(Tenant tenant, Venue venue, Network network) {
      var venueAuthRadius = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuth().generate();
      repositoryUtil.createOrUpdate(venueAuthRadius, tenant.getId());

      var venueAcctRadius = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcct().generate();
      repositoryUtil.createOrUpdate(venueAcctRadius, tenant.getId());

      var networkAuthRadius = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAuth().generate();
      repositoryUtil.createOrUpdate(networkAuthRadius, tenant.getId());

      var networkAcctRadius = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcct().generate();
      repositoryUtil.createOrUpdate(networkAcctRadius, tenant.getId());

      var networkAuthRadiusVenue = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
          .authRadiusVenue(networkAuthRadius, venue).generate();
      repositoryUtil.createOrUpdate(networkAuthRadiusVenue, tenant.getId());

      var networkAcctRadiusVenue = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
          .accountingRadiusVenue(networkAcctRadius, venue).generate();
      repositoryUtil.createOrUpdate(networkAcctRadiusVenue, tenant.getId());

      var networkVenue = networkVenue()
          .setNetwork(always(network)).setVenue(always(venue)).generate();
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId());

      network.setAuthRadius(networkAuthRadius);
      network.setAccountingRadius(networkAcctRadius);
      network.setNetworkVenues(List.of(networkVenue));
      network.getWlan().setMacAddressAuthentication(true);
      repositoryUtil.createOrUpdate(network, tenant.getId());

      venue.setApPassword("1qaz@WSX");
      repositoryUtil.createOrUpdate(venue, tenant.getId());

      tenantId = tenant.getId();
      venueId = venue.getId();
      venueAuthRadiusId = venueAuthRadius.getId();
      venueAcctRadiusId = venueAcctRadius.getId();
    }

    @Test
    void thenActivateAuthRadiusServerProfileOnVenue() {
      messageUtil.sendWifiCfgRequest(
          tenantId,
          txCtxExtension.getRequestId(),
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_VENUE,
          randomName(),
          new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("radiusId", venueAuthRadiusId),
          "");

      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateVenueAuthRadiusServerProfileSettings(venue);
      validateDdccmMessage();
    }

    @Test
    void thenActivateAccountingRadiusServerProfileOnVenue() {
      messageUtil.sendWifiCfgRequest(
          tenantId,
          txCtxExtension.getRequestId(),
          CfgAction.ACTIVATE_RADIUS_SERVER_PROFILE_ON_VENUE,
          randomName(),
          new RequestParams()
              .addPathVariable("venueId", venueId)
              .addPathVariable("radiusId", venueAcctRadiusId),
          "");

      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      validateVenueAccountingRadiusServerProfileSettings(venue);
      validateDdccmMessage();
    }

    void validateVenueAuthRadiusServerProfileSettings(Venue venueFromDb) {
      assertThat(venueFromDb)
          .extracting(Venue::getAuthRadius)
          .extracting(AbstractBaseEntity::getId)
          .isEqualTo(venueAuthRadiusId);
    }

    void validateVenueAccountingRadiusServerProfileSettings(Venue venueFromDb) {
      assertThat(venueFromDb)
          .extracting(Venue::getAccountingRadius)
          .extracting(AbstractBaseEntity::getId)
          .isEqualTo(venueAcctRadiusId);
    }

    void validateDdccmMessage() {
      var message = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, txCtxExtension.getRequestId());
      assertThat(message).isNotNull();
      WifiConfigRequest ddccmRequest = message.getPayload();
      assertThat(ddccmRequest.getOperationsList()).isNotNull()
          .hasSize(3)
          .filteredOn(Operation::hasWlanVenue)
          .hasSize(1);
    }
  }

}

