package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apGroup;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkApGroup;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.vlanPool;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkApGroupRadio;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wlan;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wlanAdvancedCustomization;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.proto.VlanPoolProfilePostMigrationJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("VlanPoolProfileTest")
@WifiIntegrationTest
public class ConsumeVlanPoolProfilePostMigrationJobTest {

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class GivenVlanPoolPersistedInDb {

    private VlanPool vlanPool;

    @BeforeEach
    void givenVlanPoolPersistedInDb(final Tenant tenant) {
      vlanPool = repositoryUtil.createOrUpdate(vlanPool()
          .setTenant(always(tenant))
          .generate(), tenant.getId(), randomTxId());
    }

    @Test
    void whenConsumeVlanPoolProfilePostMigrationJob(final Tenant tenant) {
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
          .setVlanPoolProfilePostMigrationJob(VlanPoolProfilePostMigrationJob.newBuilder())
          .build());

      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());
    }

    @Nested
    class GivenVlanPoolWith5NetworksPersistedInDb {

      private List<ApGroup> apGroups;
      private List<NetworkApGroup> networkApGroups = new ArrayList<>();
      private List<NetworkApGroupRadio> networkApGroupRadios = new ArrayList<>();

      @BeforeEach
      void given10NetworkApGroupsPersistedInDb(final Tenant tenant, final Venue venue) {
        apGroups = apGroup().setName(serialName("apGroup"))
            .setTenant(always(tenant))
            .setVenue(always(venue))
            .setIsDefault(alwaysFalse())
            .generate(3, ag -> repositoryUtil.createOrUpdate(ag
            , tenant.getId(), randomTxId())).stream().toList();
        networkApGroups = networkApGroup()
            .setApGroup(always(apGroups.get(0)))
            .setNetworkVenue(networkVenue()
                .setVenue(always(venue)).setIsAllApGroups(alwaysFalse())
                .setNetwork(network(OpenNetwork.class)
                    .setTenant(always(tenant))
                    .setWlan(wlan()
                        .setTenant(always(tenant))
                        .setAdvancedCustomization(
                            wlanAdvancedCustomization().setVlanPool(always(vlanPool))))))
            .generate(5, networkApGroup -> {
              networkApGroup.getNetworkVenue().getNetwork().getWlan()
                  .setNetwork(networkApGroup.getNetworkVenue().getNetwork());
              networkApGroup.getNetworkVenue().setNetwork(
                  repositoryUtil.createOrUpdate(networkApGroup.getNetworkVenue().getNetwork(),
                      tenant.getId(), randomTxId()));
              networkApGroup.setNetworkVenue(
                  repositoryUtil.createOrUpdate(networkApGroup.getNetworkVenue(), tenant.getId(),
                      randomTxId()));
            }).stream().map(
                networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup, tenant.getId(),
                    randomTxId())).collect(Collectors.toList());
        networkVenue()
            .setVenue(always(venue)).setIsAllApGroups(alwaysTrue())
            .setNetwork(network(PskNetwork.class)
                .setTenant(always(tenant))
                .setWlan(wlan()
                    .setTenant(always(tenant))
                    .setAdvancedCustomization(
                        wlanAdvancedCustomization().setVlanPool(always(vlanPool)))))
                .generate(1, nv -> {
                  for (int i = 1; i < 3; i++) {
                      NetworkApGroup networkApGroup = repositoryUtil.createOrUpdate(networkApGroup()
                          .setApGroup(always(apGroups.get(i)))
                          .setNetworkVenue(always(nv))
                          .generate(1, nag -> {
                            nag.getNetworkVenue().getNetwork().getWlan()
                                .setNetwork(nag.getNetworkVenue().getNetwork());
                            nag.getNetworkVenue().setNetwork(
                                repositoryUtil.createOrUpdate(nag.getNetworkVenue().getNetwork(),
                                    tenant.getId(), randomTxId()));
                            nag.setNetworkVenue(
                                repositoryUtil.createOrUpdate(nag.getNetworkVenue(), tenant.getId(),
                                    randomTxId()));
                          }).get(0), tenant.getId(), randomTxId());
                      networkApGroups.add(networkApGroup);
                    }
                  });
        networkApGroups.forEach(networkApGroup -> {
          networkApGroupRadio()
              .setTenant(always(tenant))
              .setVlanPool(always(vlanPool))
              .setNetworkApGroup(always(networkApGroup))
              .setRadio(options(StrictRadioTypeEnum._2_4_GHz, StrictRadioTypeEnum._5_GHz))
              .generate(2)
              .forEach(networkApGroupRadio -> {
                repositoryUtil.createOrUpdate(networkApGroupRadio, tenant.getId(), randomTxId());
                networkApGroupRadios.add(networkApGroupRadio);
              });
        });
      }

      @Test
      void whenConsumeVlanPoolProfilePostMigrationJob(final Tenant tenant) {
        final var requestId = randomTxId();

        messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
            .setVlanPoolProfilePostMigrationJob(VlanPoolProfilePostMigrationJob.newBuilder())
            .build());

        final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenant.getId());
        assertThat(cmnCfgCollectorMessage).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull();

        List<String> expectedWifiNetworkIds = networkApGroups.stream()
            .map(g -> g.getNetworkVenue().getNetwork().getId()).collect(Collectors.toSet())
            .stream().toList();
        assertThat(cmnCfgCollectorMessage.getPayload())
            .satisfies(msg -> {
              assertThat(msg.getTenantId()).isEqualTo(tenant.getId());
              assertThat(msg.getRequestId()).startsWith(requestId);
            })
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
            .filteredOn(op -> Index.VLAN_POOL_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getId()).isEqualTo(vlanPool.getId());
              assertThat(op.getOpType()).isEqualTo(OpType.MOD);
              assertThat(op.getDocMap())
                  .containsEntry(Key.ID, ValueUtils.stringValue(vlanPool.getId()))
                  .containsEntry(Key.NAME, ValueUtils.stringValue(vlanPool.getName()))
                  .containsEntry(Key.VLAN_MEMBERS,
                      ValueUtils.listValue(
                          vlanPool.getVlanMembers().stream().map(ValueUtils::stringValue).toList()))
                  .satisfies(docMap -> {
                    assertThat(docMap.get(Key.WIFI_NETWORK_IDS))
                        .isNotNull()
                        .extracting(value -> value.getListValue().getValuesList(),
                            InstanceOfAssertFactories.list(
                                com.ruckus.cloud.events.gpb.Value.class))
                        .hasSize(6)
                        .extracting(com.ruckus.cloud.events.gpb.Value::getStringValue)
                        .containsExactlyInAnyOrderElementsOf(expectedWifiNetworkIds);
                    assertThat(docMap.get(Key.WIFI_NETWORK_VENUE_AP_GROUPS))
                        .isNotNull()
                        .extracting(value -> value.getListValue().getValuesList(),
                            InstanceOfAssertFactories.list(
                                com.ruckus.cloud.events.gpb.Value.class))
                        .hasSize(expectedWifiNetworkIds.size())
                        .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
                        .extracting(Struct::getFieldsMap)
                        .allSatisfy(m -> {
                          m.get(Key.VENUE_ID).getStringValue().equals(networkApGroups.get(0).getNetworkVenue().getVenue().getId());
                          expectedWifiNetworkIds.contains(m.get(Key.WIFI_NETWORK_ID).getStringValue());
                          if (m.get(Key.IS_ALL_AP_GROUPS).getBoolValue()) {
                            assertThat(m.get(Key.APGROUP_IDS).getListValue().getValuesList()).hasSize(2);
                          } else {
                            assertThat(m.get(Key.APGROUP_IDS).getListValue().getValuesList()).hasSize(1);
                          }
                        });
                  });
            });
      }
    }
  }
}
