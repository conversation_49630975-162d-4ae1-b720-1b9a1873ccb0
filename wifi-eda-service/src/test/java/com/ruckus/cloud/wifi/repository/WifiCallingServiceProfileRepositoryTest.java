package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Epdg;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfile;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest(showSql = false)
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenet'), ('to-delete'), ('ec-tenant'), ('epdg-tenant');
    INSERT INTO wifi_calling_service_profile (id, tenant, service_name) VALUES
        ('wp1', 'tenet', 'my-calling'),
        ('wp2', 'tenet', 'whos-call');
    UPDATE wifi_calling_service_profile SET is_template = false;
    INSERT INTO wifi_calling_service_profile (id, tenant, service_name, is_template) VALUES
        ('wp3', 'tenet', 'my-calling', true);
    INSERT INTO wifi_calling_service_profile (id, tenant, is_template) VALUES
        ('wp4', 'to-delete', true),
        ('wp5', 'to-delete', false),
        ('wp6', 'to-delete', false);
    INSERT INTO wifi_calling_service_profile (id, tenant, template_id) VALUES
        ('wp7', 'ec-tenant', 'wpt7'),
        ('wp8', 'ec-tenant', 'wpt8');
    INSERT INTO wifi_calling_service_profile (id, tenant, is_template, epdgs) VALUES
        ('wp9', 'epdg-tenant', false, '[{"ip":null,"domain":"rksbu2.com"},{"ip":null,"domain":"commscope.com"},{"ip":null,"domain":"rksbu.com"}]');
    """)
public class WifiCallingServiceProfileRepositoryTest {

  private static final String TENANT_ID = "tenet";

  private static final String TO_DEL_TENANT_ID = "to-delete";

  @Autowired
  private WifiCallingServiceProfileRepository repository;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @ApplyTemplateFilter
  @Nested
  class InTemplateFlow {
    @Test
    public void countByTenantIdAndIsTemplateTest() {
      assertThat(repository.countByTenantId(TENANT_ID)).isEqualTo(1);
    }

    @Test
    public void findByTenantIdAndIsTemplateTest() {
      assertThat(repository.findByTenantId(TENANT_ID))
          .isNotNull()
          .hasSize(1)
          .singleElement()
          .extracting(wp -> wp.getId())
          .isEqualTo("wp3");
    }

    @Test
    public void findByIdAndTenantIdTest() {
      assertThat(repository.findByIdAndTenantId("wp1", TENANT_ID))
          .isNotPresent();

      assertThat(repository.findByIdAndTenantId("wp3", TENANT_ID))
          .isPresent()
          .map(wp -> wp.getId())
          .hasValue("wp3");

      assertThat(repository.findByIdAndTenantId("wp4", TENANT_ID))
          .isNotPresent();
    }

    @Test
    public void deleteByIdInAndTenantIdTest() {
      repository.deleteByIdInAndTenantId(List.of("wp1", "wp2"), TO_DEL_TENANT_ID);

      assertThat(repository.countByTenantId(TENANT_ID)).isEqualTo(1);
      assertThat(repository.countByTenantId(TO_DEL_TENANT_ID)).isEqualTo(1);

      repository.deleteByIdInAndTenantId(List.of("wp4", "wp5"), TO_DEL_TENANT_ID);

      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp1", TENANT_ID, false)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp2", TENANT_ID, false)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp3", TENANT_ID, true)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp4", TO_DEL_TENANT_ID, true)).isNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp5", TO_DEL_TENANT_ID, false)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp6", TO_DEL_TENANT_ID, false)).isNotNull();

      assertThat(repository.countByTenantId(TENANT_ID)).isEqualTo(1);
      assertThat(repository.countByTenantId(TO_DEL_TENANT_ID)).isEqualTo(0);

      repository.deleteByIdInAndTenantId(List.of("wp5", "wp6"), TO_DEL_TENANT_ID);

      assertThat(repository.countByTenantId(TENANT_ID)).isEqualTo(1);
      assertThat(repository.countByTenantId(TO_DEL_TENANT_ID)).isEqualTo(0);

      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp1", TENANT_ID, false)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp2", TENANT_ID, false)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp3", TENANT_ID, true)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp4", TO_DEL_TENANT_ID, true)).isNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp5", TO_DEL_TENANT_ID, false)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class,"wp6", TO_DEL_TENANT_ID, false)).isNotNull();
    }

    @Test
    public void existsByTenantIdAndServiceNameAndIsTemplateTest() {
      assertThat(repository.existsByTenantIdAndServiceName("tenet", "my-calling")).isTrue();
      assertThat(repository.existsByTenantIdAndServiceName("tenet", "whos-call")).isFalse();
    }

    @Test
    public void existsByTenantIdAndServiceNameAndIsTemplateAndIdNotTest() {
      assertThat(repository.existsByTenantIdAndServiceNameAndIdNot("tenet", "my-calling", "123")).isTrue();
      assertThat(repository.existsByTenantIdAndServiceNameAndIdNot("tenet", "my-calling", "wp3")).isFalse();
      assertThat(repository.existsByTenantIdAndServiceNameAndIdNot("tenet", "whos-call", "wp3")).isFalse();
    }
  }

  @Nested
  class OutTemplateFlow {

    @Test
    public void countByTenantId() {
      assertThat(repository.countByTenantId(TENANT_ID)).isEqualTo(2);
    }

    @Test
    public void findByTenantId() {
      assertThat(repository.findByTenantId(TENANT_ID))
          .isNotNull()
          .hasSize(2)
          .extracting(wp -> wp.getId())
          .containsOnly("wp1", "wp2");
    }

    @Test
    public void findByTenantIdAndPageableTest() {
      assertThat(repository.findByTenantId(PageRequest.of(0, 1,
          Sort.by(Sort.Direction.ASC, AbstractBaseEntity.Fields.ID)), TENANT_ID))
          .isNotNull()
          .hasSize(1)
          .singleElement()
          .extracting(wp -> wp.getId())
          .isEqualTo("wp1");

      assertThat(repository.findByTenantId(PageRequest.of(1, 1,
          Sort.by(Sort.Direction.ASC, AbstractBaseEntity.Fields.ID)), TENANT_ID))
          .isNotNull()
          .hasSize(1)
          .singleElement()
          .extracting(wp -> wp.getId())
          .isEqualTo("wp2");

      assertThat(repository.findByTenantId(PageRequest.of(2, 1), TENANT_ID))
          .isNotNull()
          .hasSize(0);
    }

    @Test
    public void findByIdAndTenantIdTest() {
      assertThat(repository.findByIdAndTenantId("wp1", TENANT_ID))
          .isPresent()
          .map(wp -> wp.getId())
          .hasValue("wp1");

      assertThat(repository.findByIdAndTenantId("wp2", TENANT_ID))
          .isPresent()
          .map(wp -> wp.getId())
          .hasValue("wp2");
    }

    @Test
    public void deleteByIdInAndTenantIdAndIsTemplateTest() {
      repository.deleteByIdInAndTenantId(List.of("wp1", "wp2"), TO_DEL_TENANT_ID);

      assertThat(repository.countByTenantId(TENANT_ID)).isEqualTo(2);
      assertThat(repository.countByTenantId(TO_DEL_TENANT_ID)).isEqualTo(2);

      repository.deleteByIdInAndTenantId(List.of("wp4", "wp5"), TO_DEL_TENANT_ID);

      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp1", TENANT_ID,
          false)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp2", TENANT_ID,
          false)).isNotNull();
      assertThat(
          repositoryUtil.find(WifiCallingServiceProfile.class, "wp3", TENANT_ID, true)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp4", TO_DEL_TENANT_ID,
          true)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp5", TO_DEL_TENANT_ID,
          false)).isNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp6", TO_DEL_TENANT_ID,
          false)).isNotNull();

      assertThat(repository.countByTenantId(TENANT_ID)).isEqualTo(2);
      assertThat(repository.countByTenantId(TO_DEL_TENANT_ID)).isEqualTo(1);

      repository.deleteByIdInAndTenantId(List.of("wp5", "wp6"), TO_DEL_TENANT_ID);

      assertThat(repository.countByTenantId(TENANT_ID)).isEqualTo(2);
      assertThat(repository.countByTenantId(TO_DEL_TENANT_ID)).isEqualTo(0);

      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp1", TENANT_ID,
          false)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp2", TENANT_ID,
          false)).isNotNull();
      assertThat(
          repositoryUtil.find(WifiCallingServiceProfile.class, "wp3", TENANT_ID, true)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp4", TO_DEL_TENANT_ID,
          true)).isNotNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp5", TO_DEL_TENANT_ID,
          false)).isNull();
      assertThat(repositoryUtil.find(WifiCallingServiceProfile.class, "wp6", TO_DEL_TENANT_ID,
          false)).isNull();
    }

    @Test
    public void existsByTenantIdAndServiceNameTest() {
      assertThat(repository.existsByTenantIdAndServiceName("tenet", "my-calling")).isTrue();
      assertThat(repository.existsByTenantIdAndServiceName("tenet", "whos-call")).isTrue();
    }

    @Test
    public void existsByTenantIdAndServiceNameAndIdNotTest() {
      assertThat(
          repository.existsByTenantIdAndServiceNameAndIdNot("tenet", "my-calling", "123")).isTrue();
      assertThat(repository.existsByTenantIdAndServiceNameAndIdNot("tenet", "my-calling",
          "wp1")).isFalse();
      assertThat(
          repository.existsByTenantIdAndServiceNameAndIdNot("tenet", "whos-call", "456")).isTrue();
      assertThat(
          repository.existsByTenantIdAndServiceNameAndIdNot("tenet", "whos-call", "wp2")).isFalse();
    }
  }

  @Test
  public void findByTenantIdAndTemplateIdIn() {
    assertThat(repository.findByTemplateIdInAndTenantId(List.of("wpt7"), "ec-tenant").size()).isEqualTo(1);
    assertThat(repository.findByTemplateIdInAndTenantId(List.of("wpt8"), "ec-tenant").size()).isEqualTo(1);
    assertThat(repository.findByTemplateIdInAndTenantId(List.of("wpt7", "wpt8"), "ec-tenant").size()).isEqualTo(2);
  }

  @Test
  public void testEpdgSorting() {
    assertThat(repository.findByTenantId("epdg-tenant"))
        .isNotNull()
        .hasSize(1)
        .extracting(wp -> wp.getId())
        .containsOnly("wp9");

    List<String> epdgDomainList =  repository.findByTenantId("epdg-tenant").get(0).getEPDGs()
        .stream().map(Epdg::getDomain).toList();

    assertThat(epdgDomainList).containsExactlyElementsOf(
        List.of("commscope.com", "rksbu.com", "rksbu2.com"));
  }

}
