package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.entity.UpdateFirmwareSchedule;
import com.ruckus.cloud.wifi.servicemodel.enums.ScheduleTimeSlotStatus;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.time.DayOfWeek;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
public class ConsumeWifiScheduleRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Value("${topic.kairos.jobRegister}")
  private String kairosJobRegister;

  @Value("${topic.nuvo.notificationRequests}")
  private String nuvoNotificationRequests;

  @Autowired private UpgradeScheduleRepository upgradeScheduleRepository;

  @Autowired private VenueRepository venueRepository;

  @Nested
  class whenConsumeWifiScheduleTest {

    @Test
    void thenCreateSchedule(
        Venue venue,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.1")
            ApVersion apVersion) {
      final var requestId = randomTxId();

      UpdateFirmwareSchedule updateFirmwareSchedule =
          newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), apVersion.getId());
      messageUtil.sendWifiScheduleCreate(
          venue.getTenant().getId(), requestId, updateFirmwareSchedule);

      assertThat(messageUtil.receive(kairosJobRegister)).isNotNull();
      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      List<UpgradeSchedule> schedules = upgradeScheduleRepository.findAllByVenueId(venue.getId());
      assertThat(schedules).isNotNull().hasSize(1).singleElement()
          .matches(s -> s.getVersion().getId().equals(apVersion.getId()));
    }
  }

  @Nested
  class whenConsumeWifiScheduleWithOneActiveTwoLegacyReleaseTest {
    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
                ApVersion apVersion700,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion622,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion620,
        @DefaultApGroup ApGroup apGroup,
        Venue venue) {
      tenant.setLatestReleaseVersion(apVersion700);
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.getRequestId());

      ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
      sts.setStatus(ScheduleTimeSlotStatus.WAITING_REMINDER);
      sts.setStartDateTime(new Date());
      sts.setEndDateTime(new Date());
      repositoryUtil.createOrUpdate(sts, tenant.getId(), txCtxExtension.newRequestId());

      UpgradeSchedule us = new UpgradeSchedule(randomId());
      us.setTimeSlot(sts);
      us.setStatus(UpgradeScheduleStatus.PENDING);
      us.setVersion(apVersion622);
      us.setVenue(venue);
      repositoryUtil.createOrUpdate(us, tenant.getId(), txCtxExtension.newRequestId());

      UpgradeScheduleFirmwareVersion usfv1 = new UpgradeScheduleFirmwareVersion(randomId());
      usfv1.setApFirmwareVersion(apVersion622);
      usfv1.setUpgradeSchedule(us);
      usfv1.setTenant(tenant);
      repositoryUtil.createOrUpdate(usfv1, tenant.getId(), txCtxExtension.newRequestId());

      UpgradeScheduleFirmwareVersion usfv2 = new UpgradeScheduleFirmwareVersion(randomId());
      usfv2.setApFirmwareVersion(apVersion620);
      usfv2.setUpgradeSchedule(us);
      usfv2.setTenant(tenant);
      repositoryUtil.createOrUpdate(usfv2, tenant.getId(), txCtxExtension.newRequestId());

      venue.setApPassword("1qaz@WSX");
      venue.setCountryCode("US");
      venue.setWifiFirmwareVersion(apVersion622);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRequestId());

      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup, randomSerialNumber(), "R560");
    }

    @Test
    void thenCreateSchedule(
        Venue venue,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion) {
      final var requestId = randomTxId();

      UpdateFirmwareSchedule updateFirmwareSchedule =
          newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), apVersion.getId());
      messageUtil.sendWifiScheduleCreate(
          venue.getTenant().getId(), requestId, updateFirmwareSchedule);

      assertThat(messageUtil.receive(kairosJobRegister)).isNotNull();
      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(1)
          .singleElement().matches(s -> s.getVersion().getId().equals(apVersion.getId()));
    }
  }

  @Nested
  class whenConsumeWifiScheduleWithMultipleTimezoneAndTwoLegacyReleaseTest {
    @BeforeEach
    void setup(
            Tenant tenant,
            @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
                    ApVersion apVersion700,
            @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
                    ApVersion apVersion622,
            Venue venue) {
      tenant.setLatestReleaseVersion(apVersion700);
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.getRequestId());

      ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
      sts.setStatus(ScheduleTimeSlotStatus.WAITING_REMINDER);
      sts.setStartDateTime(new Date());
      sts.setEndDateTime(new Date());
      repositoryUtil.createOrUpdate(sts, tenant.getId(), txCtxExtension.newRequestId());

      UpgradeSchedule us = new UpgradeSchedule(randomId());
      us.setTimeSlot(sts);
      us.setStatus(UpgradeScheduleStatus.PENDING);
      us.setVersion(apVersion622);
      us.setVenue(venue);
      repositoryUtil.createOrUpdate(us, tenant.getId(), txCtxExtension.newRequestId());

      UpgradeScheduleFirmwareVersion usfv1 = new UpgradeScheduleFirmwareVersion(randomId());
      usfv1.setApFirmwareVersion(apVersion622);
      usfv1.setUpgradeSchedule(us);
      usfv1.setTenant(tenant);
      repositoryUtil.createOrUpdate(usfv1, tenant.getId(), txCtxExtension.newRequestId());

      venue.setApPassword("1qaz@WSX");
      venue.setCountryCode("US");
      venue.setWifiFirmwareVersion(apVersion622);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRequestId());

      Venue newBindVenue =
              com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venue()
                      .setApPassword(always("1qaz@WSX"))
                      .setAddressLine(always("Somewhere"))
                      .setCountryCode(always("US"))
                      .setTimezone(always("America/New_York"))
                      .setWifiFirmwareVersion(always(apVersion622))
                      .generate();
      newBindVenue.setTenant(tenant);
      repositoryUtil.createOrUpdate(
              newBindVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      ApGroup apGroup2 = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apGroup().generate();
      apGroup2.setVenue(newBindVenue);
      apGroup2.setTenant(tenant);
      apGroup2.setIsDefault(true);
      repositoryUtil.createOrUpdate(
              apGroup2, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R500");
    }

    @Test
    void thenCreateScheduleWithImpactModelEmail(
            Venue venue,
            @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
                    ApVersion apVersion) {
      final var requestId = randomTxId();

      UpdateFirmwareSchedule updateFirmwareSchedule =
              newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), apVersion.getId());
      messageUtil.sendWifiScheduleCreate(
              venue.getTenant().getId(), requestId, updateFirmwareSchedule);

      assertThat(messageUtil.receive(kairosJobRegister)).isNotNull();
      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(1)
          .singleElement().matches(s -> s.getVersion().getId().equals(apVersion.getId()));
    }
  }

  @Nested
  class whenConsumeWifiScheduleWithMultipleUpgradeSchedulesTest {
    @BeforeEach
    void setup(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion700,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion622,
        Venue venue) {
      tenant.setLatestReleaseVersion(apVersion700);
      repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.getRequestId());

      ScheduleTimeSlot sts = new ScheduleTimeSlot(randomId());
      sts.setStatus(ScheduleTimeSlotStatus.WAITING_REMINDER);
      sts.setStartDateTime(new Date());
      sts.setEndDateTime(new Date());
      repositoryUtil.createOrUpdate(sts, tenant.getId(), txCtxExtension.newRequestId());

      UpgradeSchedule us = new UpgradeSchedule(randomId());
      us.setTimeSlot(sts);
      us.setStatus(UpgradeScheduleStatus.PENDING);
      us.setVersion(apVersion622);
      us.setVenue(venue);
      repositoryUtil.createOrUpdate(us, tenant.getId(), txCtxExtension.newRequestId());

      UpgradeScheduleFirmwareVersion usfv1 = new UpgradeScheduleFirmwareVersion(randomId());
      usfv1.setApFirmwareVersion(apVersion622);
      usfv1.setUpgradeSchedule(us);
      usfv1.setTenant(tenant);
      repositoryUtil.createOrUpdate(usfv1, tenant.getId(), txCtxExtension.newRequestId());

      venue.setApPassword("1qaz@WSX");
      venue.setCountryCode("US");
      venue.setWifiFirmwareVersion(apVersion622);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRequestId());

      Venue newBindVenue =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venue()
              .setApPassword(always("1qaz@WSX"))
              .setAddressLine(always("Somewhere"))
              .setCountryCode(always("US"))
              .setTimezone(always("America/New_York"))
              .setWifiFirmwareVersion(always(apVersion622))
              .generate();
      newBindVenue.setTenant(tenant);
      repositoryUtil.createOrUpdate(
          newBindVenue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      ApGroup apGroup2 = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apGroup().generate();
      apGroup2.setVenue(newBindVenue);
      apGroup2.setTenant(tenant);
      apGroup2.setIsDefault(true);
      repositoryUtil.createOrUpdate(
          apGroup2, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      createNewApAndApFirmwareUpgradeRequest(tenant, apGroup2, randomSerialNumber(), "R500");
    }

    @Test
    void thenCreateScheduleWithImpactModelEmail(
        Venue venue,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.99")
            ApVersion apVersion) {
      final var requestId = randomTxId();

      UpdateFirmwareSchedule updateFirmwareSchedule =
          newUpdateFirmwareSchedule(requestId, venue.getTenant().getId(), apVersion.getId());
      messageUtil.sendWifiScheduleCreate(
          venue.getTenant().getId(), requestId, updateFirmwareSchedule);

      assertThat(messageUtil.receive(kairosJobRegister)).isNotNull();
      assertThat(messageUtil.receive(nuvoNotificationRequests)).isNotNull();
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(1)
          .singleElement().matches(s -> s.getVersion().getId().equals(apVersion.getId()));
    }
  }

  private void createNewApAndApFirmwareUpgradeRequest(
      Tenant tenant, ApGroup apGroup, String serial, String model) {
    Ap ap = new Ap(serial);
    ap.setApGroup(apGroup);
    ap.setTenant(tenant);
    repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

    ApFirmwareUpgradeRequest apFirmwareUpgradeRequest = new ApFirmwareUpgradeRequest(randomId());
    apFirmwareUpgradeRequest.setTenant(tenant);
    apFirmwareUpgradeRequest.setModel(model);
    apFirmwareUpgradeRequest.setSerialNumber(serial);
    repositoryUtil.createOrUpdate(
        apFirmwareUpgradeRequest, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
  }

  private UpdateFirmwareSchedule newUpdateFirmwareSchedule(
      String requestId, String tenantId, String version) {
    UpdateFirmwareSchedule updateFirmwareSchedule =
        UpdateFirmwareSchedule.builder()
            .tenantId(tenantId)
            .requestId(requestId)
            .version(version)
            .days(List.of(DayOfWeek.SATURDAY))
            .times(List.of("TIME_PERIOD_00_02"))
            .timeZone("Asia/Taipei")
            .build();

    return updateFirmwareSchedule;
  }
}
