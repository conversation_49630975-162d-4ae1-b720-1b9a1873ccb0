package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.tuple;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.core.model.Identifiable;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.service.utils.VenueUtils;
import com.ruckus.cloud.wifi.servicemodel.projection.ScheduleReminderQueryProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueTenantIdProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueWifiFirmwareVersionProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
class VenueRepositoryTest {

  private static final String TENANT_ID = "3057a924ea8148bab5cc1682ff09275c";

  @Autowired
  private VenueRepository repository;

  @Autowired
  private UpgradeScheduleRepository upgradeScheduleRepository;

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('8829b85337054458bd4ba72bc86052de'),
               ('b13f49a850ba4517b4941ab5025cbc00');
      INSERT INTO venue (id, tenant, dhcp_service_enabled)
        VALUES ('8ac2cceddcd849d5acfd37d6e36885f1', '8829b85337054458bd4ba72bc86052de', true),
               ('d041960eda194873a2a1c809cbe6d6cd', '8829b85337054458bd4ba72bc86052de', false),
               ('5beb3e96a19c4700a005cdd14c6e186d', 'b13f49a850ba4517b4941ab5025cbc00', true);
      """)
  void findIdsByTenantIdTest() {
    assertThat(repository.findIdsByTenantId("8829b85337054458bd4ba72bc86052de"))
        .isNotNull()
        .hasSize(2)
        .containsExactlyInAnyOrder("8ac2cceddcd849d5acfd37d6e36885f1", "d041960eda194873a2a1c809cbe6d6cd");
    assertThat(repository.findIdsByTenantId("d041960eda194873a2a1c809cbe6d6cd"))
        .isEmpty();
    assertThat(repository.findIdsByTenantId("b13f49a850ba4517b4941ab5025cbc00"))
        .isNotNull()
        .hasSize(1)
        .containsExactlyInAnyOrder("5beb3e96a19c4700a005cdd14c6e186d");
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('8829b85337054458bd4ba72bc86052de'),
               ('b13f49a850ba4517b4941ab5025cbc00');
      INSERT INTO venue (id, tenant, dhcp_service_enabled)
        VALUES ('8ac2cceddcd849d5acfd37d6e36885f1', '8829b85337054458bd4ba72bc86052de', true),
               ('d041960eda194873a2a1c809cbe6d6cd', '8829b85337054458bd4ba72bc86052de', false),
               ('5beb3e96a19c4700a005cdd14c6e186d', 'b13f49a850ba4517b4941ab5025cbc00', true);
      """)
  void findByIdInAndTenantIdTest() {
    assertThat(repository.findByIdInAndTenantId(
        List.of("8ac2cceddcd849d5acfd37d6e36885f1", "d041960eda194873a2a1c809cbe6d6cd"),
        "8829b85337054458bd4ba72bc86052de"))
        .isNotNull()
        .hasSize(2);
    assertThat(repository.findByIdInAndTenantId(
        List.of("8ac2cceddcd849d5acfd37d6e36885f1", "d041960eda194873a2a1c809cbe6d6cd"),
        "b13f49a850ba4517b4941ab5025cbc00"))
        .isEmpty();
    assertThat(repository.findByIdInAndTenantId(
        List.of("5beb3e96a19c4700a005cdd14c6e186d"),
        "b13f49a850ba4517b4941ab5025cbc00"))
        .isNotNull()
        .hasSize(1);
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id)
        VALUES ('8829b85337054458bd4ba72bc86052de'),
               ('b13f49a850ba4517b4941ab5025cbc00');
      INSERT INTO venue (id, tenant, dhcp_service_enabled, dhcp_service_mode)
        VALUES ('8ac2cceddcd849d5acfd37d6e36885f1', '8829b85337054458bd4ba72bc86052de', true, 'EnableOnEachAPs'),
               ('d041960eda194873a2a1c809cbe6d6cd', '8829b85337054458bd4ba72bc86052de', false, null),
               ('5beb3e96a19c4700a005cdd14c6e186d', 'b13f49a850ba4517b4941ab5025cbc00', true, 'EnableOnMultipleAPs'),
               ('9da59f0e868c4a3e922e0733c6bcd4c1', '8829b85337054458bd4ba72bc86052de', true, 'EnableOnHierarchicalAPs');
      INSERT INTO ap_group (id, tenant, venue, is_template)
        VALUES ('c10daabb8b5e41c095d0fd3b27a388f9', '8829b85337054458bd4ba72bc86052de', '8ac2cceddcd849d5acfd37d6e36885f1', false),
               ('b802858520744e7098066c1165407fe3', '8829b85337054458bd4ba72bc86052de', '8ac2cceddcd849d5acfd37d6e36885f1', false),
               ('4e2f6755fb9a42d0ac5f5c137b890574', '8829b85337054458bd4ba72bc86052de', 'd041960eda194873a2a1c809cbe6d6cd', false),
               ('a91dd7a413564737a804be69453fcce0', 'b13f49a850ba4517b4941ab5025cbc00', '5beb3e96a19c4700a005cdd14c6e186d', false),
               ('725604a2028b41148c1dfa3526946b62', '8829b85337054458bd4ba72bc86052de', '9da59f0e868c4a3e922e0733c6bcd4c1', false);
      INSERT INTO ap (id, tenant, ap_group)
        VALUES ('341703003166', '8829b85337054458bd4ba72bc86052de', 'c10daabb8b5e41c095d0fd3b27a388f9'),
               ('341703003188', '8829b85337054458bd4ba72bc86052de', 'b802858520744e7098066c1165407fe3'),
               ('401604703937', '8829b85337054458bd4ba72bc86052de', '4e2f6755fb9a42d0ac5f5c137b890574'),
               ('501604703937', '8829b85337054458bd4ba72bc86052de', 'a91dd7a413564737a804be69453fcce0'),
               ('601604703937', '8829b85337054458bd4ba72bc86052de', '725604a2028b41148c1dfa3526946b62');
      INSERT INTO dhcp_service_profile (id, tenant)
        VALUES ('3d5c0d4109ce442dbb07eddd1d25063a', '8829b85337054458bd4ba72bc86052de'),
               ('9ad88b2dfa7543bebc9b962b60390149', '8829b85337054458bd4ba72bc86052de'),
               ('810c54e9666c487e8eef6cfc8e70da6c', 'b13f49a850ba4517b4941ab5025cbc00'),
               ('3ebcc20365e141969371440021fd9126', '8829b85337054458bd4ba72bc86052de');
      INSERT INTO dhcp_service_profile_venue (id, tenant, dhcp_service_profile, venue)
        VALUES ('ffaaa3a0f6064db68b373870aef64eb5', '8829b85337054458bd4ba72bc86052de', '3d5c0d4109ce442dbb07eddd1d25063a', '8ac2cceddcd849d5acfd37d6e36885f1'),
               ('be62c5e7cd81477a8aad695ff6898f65', '8829b85337054458bd4ba72bc86052de', '9ad88b2dfa7543bebc9b962b60390149', 'd041960eda194873a2a1c809cbe6d6cd'),
               ('3cd1ef3a0b5b493cba1f939460238fa7', 'b13f49a850ba4517b4941ab5025cbc00', '810c54e9666c487e8eef6cfc8e70da6c', '5beb3e96a19c4700a005cdd14c6e186d'),
               ('aa383c6d36d644388caab3cb19dcd54a', '8829b85337054458bd4ba72bc86052de', '3ebcc20365e141969371440021fd9126', '9da59f0e868c4a3e922e0733c6bcd4c1');
      """)
  void findVenuesByDhcpServiceSettingEnabledAndApExistsTest() {
    var eachAPs = new VenueTenantIdProjection("8ac2cceddcd849d5acfd37d6e36885f1", "8829b85337054458bd4ba72bc86052de");
    var dhcpDisabled = new VenueTenantIdProjection("d041960eda194873a2a1c809cbe6d6cd", "8829b85337054458bd4ba72bc86052de");
    var multipleAPs = new VenueTenantIdProjection("5beb3e96a19c4700a005cdd14c6e186d", "b13f49a850ba4517b4941ab5025cbc00");
    var hierarchicalAPs = new VenueTenantIdProjection("9da59f0e868c4a3e922e0733c6bcd4c1", "8829b85337054458bd4ba72bc86052de");

    Pageable pageRequest = PageRequest.of(0, 100, Sort.by(AbstractBaseEntity.Fields.CREATEDDATE));
    Page<VenueTenantIdProjection> venuePage = repository.findVenuesByDhcpServiceSettingEnabledAndApExists(pageRequest);

    assertThat(venuePage.getContent())
        .hasSize(2)
        .satisfies(page -> {
          assertThat(page).noneMatch(eachAPs::equals);
          assertThat(page).noneMatch(dhcpDisabled::equals);
          assertThat(page).anyMatch(multipleAPs::equals);
          assertThat(page).anyMatch(hierarchicalAPs::equals);
        });
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO venue (tenant, id) VALUES ('3057a924ea8148bab5cc1682ff09275c', '553029015f9f4c16be055a5354e857a3');
      INSERT INTO client_isolation_allowlist (tenant, id, venue) VALUES ('3057a924ea8148bab5cc1682ff09275c', 'b337efab7ae94aaf97df10117848ac73', '553029015f9f4c16be055a5354e857a3');
      INSERT INTO network (tenant, id, type) VALUES
          ('3057a924ea8148bab5cc1682ff09275c', '158a464c457d49828f9a22bf50dd7a69', 'OPEN'),
          ('3057a924ea8148bab5cc1682ff09275c', '23164ed850a14721b325ef4c300f9b9d', 'OPEN');
      INSERT INTO network_venue (tenant, id, client_isolation_allowlist, network, venue) VALUES
          ('3057a924ea8148bab5cc1682ff09275c', 'bedb91e5d83d44f9a665177801ef986e', 'b337efab7ae94aaf97df10117848ac73', '158a464c457d49828f9a22bf50dd7a69', '553029015f9f4c16be055a5354e857a3'),
          ('3057a924ea8148bab5cc1682ff09275c', 'bd619ceb0d254e259e894f9f2227cb84', 'b337efab7ae94aaf97df10117848ac73', '23164ed850a14721b325ef4c300f9b9d', '553029015f9f4c16be055a5354e857a3');
      """)
  void findByTenantIdAndClientIsolationAllowlistIdInTest() {
    final String clientIsolationAllowlistId = "b337efab7ae94aaf97df10117848ac73";
    final String expectedVenueId = "553029015f9f4c16be055a5354e857a3";
    assertThat(repository.findByTenantIdAndClientIsolationAllowlistId(TENANT_ID, clientIsolationAllowlistId))
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .extracting(Venue::getId)
        .isEqualTo(expectedVenueId);
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO venue (tenant, id) VALUES ('3057a924ea8148bab5cc1682ff09275c', 'a09c1264ae704ba1969152aa44cc083a');
      INSERT INTO ap_group (tenant, id, venue, is_default) VALUES
          ('3057a924ea8148bab5cc1682ff09275c', '6fb01014b4b24e5685bb1e85446bc364', 'a09c1264ae704ba1969152aa44cc083a', TRUE),
          ('3057a924ea8148bab5cc1682ff09275c', 'ac6723e9cf7640e5a528167ba8d8957a', 'a09c1264ae704ba1969152aa44cc083a', FALSE);
      INSERT INTO ap (tenant, id, ap_group) VALUES
          ('3057a924ea8148bab5cc1682ff09275c', '601247601720', '6fb01014b4b24e5685bb1e85446bc364'),
          ('3057a924ea8148bab5cc1682ff09275c', '843751924937', 'ac6723e9cf7640e5a528167ba8d8957a');
      INSERT INTO multicast_dns_proxy_service_profile (tenant, id) VALUES ('3057a924ea8148bab5cc1682ff09275c', 'b2e533bed43a4e2aadc1ad5fbe121257');
      INSERT INTO multicast_dns_proxy_service_profile_ap (tenant, id, ap, multicast_dns_proxy_service_profile) VALUES
          ('3057a924ea8148bab5cc1682ff09275c', 'd888fd374a974e548af80a7ecbc74230', '601247601720', 'b2e533bed43a4e2aadc1ad5fbe121257'),
          ('3057a924ea8148bab5cc1682ff09275c', 'e619cccb6bd74075ba0b220611969aab', '843751924937', 'b2e533bed43a4e2aadc1ad5fbe121257');
      """)
  void findByTenantIdAndMulticastDnsProxyServiceProfileIdTest() {
    final String multicastDnsProxyServiceProfileId = "b2e533bed43a4e2aadc1ad5fbe121257";
    final String expectedVenueId = "a09c1264ae704ba1969152aa44cc083a";
    assertThat(repository.findByTenantIdAndMulticastDnsProxyServiceProfileId(TENANT_ID, multicastDnsProxyServiceProfileId))
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .extracting(Venue::getId)
        .isEqualTo(expectedVenueId);
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c'), ('a09c1264ae704ba1969152aa44cc083a');
      INSERT INTO ap_version (id) VALUES ('6.2.2.103.124');
      INSERT INTO venue (tenant, id, ap_version)
        VALUES ('3057a924ea8148bab5cc1682ff09275c', 'venueId1', '6.2.2.103.124'),
               ('3057a924ea8148bab5cc1682ff09275c', 'venueId2', '6.2.2.103.124'),
               ('3057a924ea8148bab5cc1682ff09275c', 'venueId3', '6.2.2.103.124'),
               ('a09c1264ae704ba1969152aa44cc083a', 'venueId4', '6.2.2.103.124'),
               ('a09c1264ae704ba1969152aa44cc083a', 'venueId5', '6.2.2.103.124');
      """)
  void findAllVenueWifiFirmwareVersionsByTenantId() {
    assertThat(repository.findAllVenueWifiFirmwareVersionsByTenantIdAndIdIn("3057a924ea8148bab5cc1682ff09275c",
          List.of("venueId1", "venueId2")))
        .hasSize(2)
        .extracting(VenueWifiFirmwareVersionProjection::venueId, proj -> proj.apVersion().getId())
        .containsExactlyInAnyOrder(
            tuple("venueId1", "6.2.2.103.124"),
            tuple("venueId2", "6.2.2.103.124")
        );
    assertThat(repository.findAllVenueWifiFirmwareVersionsByTenantIdAndIdIn("a09c1264ae704ba1969152aa44cc083a",
        List.of("venueId4", "venueId5", "venueId1", "venueId2")))
        .hasSize(2)
        .extracting(VenueWifiFirmwareVersionProjection::venueId, proj -> proj.apVersion().getId())
        .containsExactlyInAnyOrder(
            tuple("venueId4", "6.2.2.103.124"),
            tuple("venueId5", "6.2.2.103.124")
        );
    assertThat(repository.findAllVenueWifiFirmwareVersionsByTenantIdAndIdIn("a09c1264ae704ba1969152aa44cc083a",
        List.of())).isEmpty();
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO ap_version (id) VALUES ('6.2.2.103.124');
      INSERT INTO venue (tenant, id, ap_version) VALUES
          ('3057a924ea8148bab5cc1682ff09275c', '553029015f9f4c16be055a5354e857a3', '6.2.2.103.124');
      """)
  public void findApVersionByIdTest() {
    String expectedApVersion = "6.2.2.103.124";
    String venueId = "553029015f9f4c16be055a5354e857a3";

    assertThat(repository.findApVersionIdByVenueId(venueId))
        .hasValue(expectedApVersion);
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO ap_version (id) VALUES ('6.2.2.103.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time) 
        VALUES ('timeSlotId', 'WAITING_REMINDER', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version)
        VALUES ('3057a924ea8148bab5cc1682ff09275c', 'venueId1', '6.2.2.103.124'),
               ('3057a924ea8148bab5cc1682ff09275c', 'venueId2', '6.2.2.103.124'),
               ('3057a924ea8148bab5cc1682ff09275c', 'venueId3', '6.2.2.103.124'),
               ('3057a924ea8148bab5cc1682ff09275c', 'venueId4', '6.2.2.103.124'),
               ('3057a924ea8148bab5cc1682ff09275c', 'venueId5', '6.2.2.103.124');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue)
        VALUES ('3057a924ea8148bab5cc1682ff09275c', 'scheduleId1', 'PENDING', 'timeSlotId', '6.2.2.103.124', 'venueId1'),
               ('3057a924ea8148bab5cc1682ff09275c', 'scheduleId2', 'PENDING', 'timeSlotId', '6.2.2.103.124', 'venueId2'),
               ('3057a924ea8148bab5cc1682ff09275c', 'scheduleId3', 'PENDING', 'timeSlotId', '6.2.2.103.124', 'venueId3'),
               ('3057a924ea8148bab5cc1682ff09275c', 'scheduleId4', 'RUNNING', 'timeSlotId', '6.2.2.103.124', 'venueId4');
      """)
  void findScheduledVenuesTest() {
    List<String> expectedVenueIDs = List.of("venueId1", "venueId2", "venueId3");

    List<Venue> venues = repository.findScheduledVenues(TENANT_ID);

    assertThat(venues).isNotNull().hasSize(3).allMatch(v -> expectedVenueIDs.contains(v.getId()));
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id, name)
        VALUES ('tenantId1', 'tenantName1'), ('tenantId2', 'tenantName2');
      INSERT INTO ap_version (id) VALUES ('*********.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'WAITING_REMINDER', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version, name, address_line) 
        VALUES ('tenantId1', 'venueId1', '*********.124', 'venueName1', 'addr1'),
               ('tenantId2', 'venueId2', '*********.124', 'venueName2', 'addr2'),
               ('tenantId1', 'venueId3', '*********.124', 'venueName3', 'addr3');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId2', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.124', 'venueId2'),
               ('tenantId1', 'scheduleId3', 'RUNNING', 'timeSlotId', '*********.124', 'venueId3');
      """)
  void findVenuesBySlotTimeTest() {
    List<String> expectedVenueIDs = List.of("venueId1", "venueId2");

    List<ScheduleReminderQueryProjection> projections = repository.findVenuesBySlotTime(new Date());

    assertThat(projections).isNotNull().hasSize(2)
        .allMatch(p -> expectedVenueIDs.contains(p.getVenueId()));
  }

  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO ap_version (id) VALUES ('6.2.2.103.123');
      INSERT INTO venue (tenant, id, ap_version, mesh_options_enabled) VALUES
          ('3057a924ea8148bab5cc1682ff09275c', '09f833ae579d11ee8c990242ac120002', '6.2.2.103.123', true);
      """)
  public void findVenueApUpgradeProjectionTest() {
    var projection = repository.findVenueApUpgradeData("09f833ae579d11ee8c990242ac120002", TENANT_ID);
    assertThat(projection)
        .matches(e -> e.meshEnabled() == true)
        .matches(e -> e.wifiFirmwareVersion().getId().equals("6.2.2.103.123"))
        .matches(e -> e.id().equals("09f833ae579d11ee8c990242ac120002"));
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('3057a924ea8148bab5cc1682ff09275c');
      INSERT INTO venue (tenant, id, mesh_options_enabled) VALUES
          ('3057a924ea8148bab5cc1682ff09275c', '09f833ae579d11ee8c990242ac120002',  true);
      """)
  public void findVenueApUpgradeProjectionTest_noApVersion() {
    var projection = repository.findVenueApUpgradeData("09f833ae579d11ee8c990242ac120002", TENANT_ID);
    assertThat(projection)
        .isNotNull()
        .matches(e -> e.meshEnabled() == true)
        .matches(e -> e.id().equals("09f833ae579d11ee8c990242ac120002"));
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('2548b9b0d15c11eea5060242ac120002');
      INSERT INTO venue (tenant, id, is_template) VALUES
          ('2548b9b0d15c11eea5060242ac120002', '2d5da160d15c11eea5060242ac120002',  true);
      INSERT INTO venue (tenant, id, is_template, template_id) VALUES
          ('2548b9b0d15c11eea5060242ac120002', '389c0dbed15c11eea5060242ac120002',  false, '2d5da160d15c11eea5060242ac120002');    
      """)
  void findByTemplateIdAndTenantId() {
    var venue = repository.findByTemplateIdAndTenantId("2d5da160d15c11eea5060242ac120002", "2548b9b0d15c11eea5060242ac120002");
    assertThat(venue.isPresent()).isTrue();
    assertThat(venue.get()).extracting(AbstractBaseEntity::getId)
        .isEqualTo("389c0dbed15c11eea5060242ac120002");

    venue = repository.findByTemplateIdAndTenantId("2d5da160d15c11eea5060242ac120002", "non-exist");
    assertThat(venue.isPresent()).isFalse();
  }

  @Test
  @Sql(
      statements = """
      INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
      INSERT INTO venue (id, tenant) VALUES
          ('06f00c816aed4fc09acdd3ddf59ad6b6', '4c8279f79307415fa9e4c88a1819f0fc'),
          ('e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc'),
          ('0f30b3ced0ab4a70a84de669c9f27246', '4c8279f79307415fa9e4c88a1819f0fc');
      INSERT INTO network (id, name, type, tenant) VALUES
          ('e5123f27d8c549d98b4d121e400f9cff', 'Hotspot 20 Network', 'HOTSPOT20', '4c8279f79307415fa9e4c88a1819f0fc'),
          ('7ec535276d4643bb8108e326c7f1c45a', 'AAA Network', 'AAA', '4c8279f79307415fa9e4c88a1819f0fc');
      INSERT INTO network_venue (id, network, venue, tenant) VALUES
          ('67f31a3948d848d08e1c8dbb0a8bbc6f', 'e5123f27d8c549d98b4d121e400f9cff',
           '06f00c816aed4fc09acdd3ddf59ad6b6', '4c8279f79307415fa9e4c88a1819f0fc'),
          ('b4fec1b0b3ec455499834cc3d4652eb3', '7ec535276d4643bb8108e326c7f1c45a',
           '06f00c816aed4fc09acdd3ddf59ad6b6', '4c8279f79307415fa9e4c88a1819f0fc'),
          ('eb5db59075374089a5fa4415a474983e', '7ec535276d4643bb8108e326c7f1c45a',
           'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
      """)
  void existsHotspot20EnabledByByTenantIdAndVenueId() {
    var useHotspot20 =
        repository.existsHotspot20EnabledByByTenantIdAndVenueId("4c8279f79307415fa9e4c88a1819f0fc", "06f00c816aed4fc09acdd3ddf59ad6b6");
    assertTrue(useHotspot20);

    useHotspot20 =
        repository.existsHotspot20EnabledByByTenantIdAndVenueId("4c8279f79307415fa9e4c88a1819f0fc", "e465bac6afb747a4987d0d0945f77221");
    assertFalse(useHotspot20);

    // For deleting network venue case
    useHotspot20 =
        repository.existsHotspot20EnabledByByTenantIdAndVenueId("4c8279f79307415fa9e4c88a1819f0fc", "0f30b3ced0ab4a70a84de669c9f27246");
    assertFalse(useHotspot20);
  }

  @Test
  @Sql(
      statements = """
          INSERT INTO tenant (id)
            VALUES ('4c8279f79307415fa9e4c88a1819f0fg'),
                   ('a782b402d95c46b4aabce87b77e68613'),
                   ('eed4a8bf286e4d71b43c3d7ec9b13c2d');
          INSERT INTO venue (id, tenant, min_floor, max_floor)
            VALUES ('8ac2cceddcd849d5acfd37d6e36885f1', '4c8279f79307415fa9e4c88a1819f0fg', null, null),
                   ('d041960eda194873a2a1c809cbe6d6cd', 'a782b402d95c46b4aabce87b77e68613', 2, 3),
                   ('5beb3e96a19c4700a005cdd14c6e186d', 'eed4a8bf286e4d71b43c3d7ec9b13c2d', 0, 0);
          """)
  void testFindByTenantIdAndMinFloorIsZeroAndMaxFloorIsZero() {
    final String tenantId1 = "4c8279f79307415fa9e4c88a1819f0fg";

    final var result1 = repository.findByTenantIdAndMinFloorIsZeroAndMaxFloorIsZero(
        Pageable.unpaged(), tenantId1);
    assertThat(result1).isEmpty();

    final String tenantId2 = "a782b402d95c46b4aabce87b77e68613";

    final var result2 = repository.findByTenantIdAndMinFloorIsZeroAndMaxFloorIsZero(
        Pageable.unpaged(), tenantId2);
    assertThat(result2).isEmpty();

    final String tenantId3 = "eed4a8bf286e4d71b43c3d7ec9b13c2d";

    final var result3 = repository.findByTenantIdAndMinFloorIsZeroAndMaxFloorIsZero(
        Pageable.unpaged(), tenantId3);
    assertThat(result3)
        .isNotEmpty().singleElement()
        .satisfies(venue -> assertSoftly(softly -> {
          softly.assertThat(venue.getId()).isEqualTo("5beb3e96a19c4700a005cdd14c6e186d");
          softly.assertThat(venue.getName()).isNull();
          softly.assertThat(venue.getTenant().getId()).isEqualTo(tenantId3);
          softly.assertThat(venue.getHeight().getMinFloor()).isEqualTo(0);
          softly.assertThat(venue.getHeight().getMaxFloor()).isEqualTo(0);
        }));
    ;
  }

  @Nested
  @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('acc6a2798e464ca2adf1b19aa626f829');
        INSERT INTO lbs_server_profile (id, tenant) VALUES
            ('08c1a1abb1bb41869918b94b406c22ce', 'acc6a2798e464ca2adf1b19aa626f829'),
            ('cc540c14c16f4e2a9dbcd1ec0413802c', 'acc6a2798e464ca2adf1b19aa626f829'),
            ('321c94cea2e14f80bf8bbe0ed237a614', 'acc6a2798e464ca2adf1b19aa626f829');
        INSERT INTO venue (id, tenant, lbs_server_profile) VALUES
            ('f8fc08e9f61540119769c837c4543345', 'acc6a2798e464ca2adf1b19aa626f829', '08c1a1abb1bb41869918b94b406c22ce'),
            ('c44b86e6277b4044845ef50aaa061471', 'acc6a2798e464ca2adf1b19aa626f829', '08c1a1abb1bb41869918b94b406c22ce'),
            ('c7a86c5ca2644f338bd0cc1e7140baeb', 'acc6a2798e464ca2adf1b19aa626f829', 'cc540c14c16f4e2a9dbcd1ec0413802c'),
            ('1cde9b556f0c44b395b3bf692e80b73d', 'acc6a2798e464ca2adf1b19aa626f829', null);
        """)
  class GivenLbsServerProfileAndVenueAndTenant {

    @Test
    void testExistsByTenantIdAndLbsServerProfileId() {
      var shouldBeFalse = repository.existsByTenantIdAndLbsServerProfileId(
          "acc6a2798e464ca2adf1b19aa626f829", "321c94cea2e14f80bf8bbe0ed237a614");
      assertFalse(shouldBeFalse);

      var shouldBeTrue = repository.existsByTenantIdAndLbsServerProfileId(
          "acc6a2798e464ca2adf1b19aa626f829", "08c1a1abb1bb41869918b94b406c22ce");
      assertTrue(shouldBeTrue);
    }

    @Test
    void testFindTenantIdAndLbsServerProfileId() {
      var result = repository.findByTenantIdAndLbsServerProfileId(
          "acc6a2798e464ca2adf1b19aa626f829", "08c1a1abb1bb41869918b94b406c22ce");
      assertThat(result)
          .isNotEmpty()
          .extracting(Identifiable::getId)
          .hasSizeGreaterThanOrEqualTo(2)
          .contains("f8fc08e9f61540119769c837c4543345", "c44b86e6277b4044845ef50aaa061471");
    }
  }

  @Nested
  @Sql(statements = """
        INSERT INTO tenant (id) VALUES ('acc6a2798e464ca2adf1b19aa626f829');
        INSERT INTO radius (id, tenant, type) VALUES
            ('08c1a1abb1bb41869918b94b406c22ce', 'acc6a2798e464ca2adf1b19aa626f829', 'AUTHENTICATION'),
            ('cc540c14c16f4e2a9dbcd1ec0413802c', 'acc6a2798e464ca2adf1b19aa626f829', 'ACCOUNTING'),
            ('321c94cea2e14f80bf8bbe0ed237a614', 'acc6a2798e464ca2adf1b19aa626f829', 'ACCOUNTING');
        INSERT INTO venue (id, tenant, auth_radius, accounting_radius) VALUES
            ('f8fc08e9f61540119769c837c4543345', 'acc6a2798e464ca2adf1b19aa626f829', '08c1a1abb1bb41869918b94b406c22ce', '321c94cea2e14f80bf8bbe0ed237a614'),
            ('c44b86e6277b4044845ef50aaa061471', 'acc6a2798e464ca2adf1b19aa626f829', '08c1a1abb1bb41869918b94b406c22ce', null),
            ('c7a86c5ca2644f338bd0cc1e7140baeb', 'acc6a2798e464ca2adf1b19aa626f829', null, 'cc540c14c16f4e2a9dbcd1ec0413802c'),
            ('1cde9b556f0c44b395b3bf692e80b73d', 'acc6a2798e464ca2adf1b19aa626f829', null, null);
        """)
  class GivenRadiusAndVenueAndTenant {

    @Test
    void testFindByTenantIdAndAuthRadiusId() {
      var authOverrideVenues = repository.findByTenantIdAndAuthRadiusId(
          "acc6a2798e464ca2adf1b19aa626f829", "08c1a1abb1bb41869918b94b406c22ce");
      assertThat(authOverrideVenues)
          .isNotEmpty()
          .extracting(Identifiable::getId)
          .hasSizeGreaterThanOrEqualTo(2)
          .contains("f8fc08e9f61540119769c837c4543345", "c44b86e6277b4044845ef50aaa061471");

      var authOverrideVenues2 = repository.findByTenantIdAndAuthRadiusId(
          "acc6a2798e464ca2adf1b19aa626f829", "cc540c14c16f4e2a9dbcd1ec0413802c");
      assertThat(authOverrideVenues2)
          .isEmpty();

      var authOverrideVenues3 = repository.findByTenantIdAndAuthRadiusId(
          "acc6a2798e464ca2adf1b19aa626f829", "");
      assertThat(authOverrideVenues3)
          .isEmpty();
    }

    @Test
    void testFindByTenantIdAndAccountingRadiusId() {
      var accountingOverrideVenues = repository.findByTenantIdAndAccountingRadiusId(
          "acc6a2798e464ca2adf1b19aa626f829", "321c94cea2e14f80bf8bbe0ed237a614");
      assertThat(accountingOverrideVenues)
          .isNotEmpty()
          .extracting(Identifiable::getId)
          .hasSizeGreaterThanOrEqualTo(1)
          .contains("f8fc08e9f61540119769c837c4543345");

      var accountingOverrideVenues2 = repository.findByTenantIdAndAccountingRadiusId(
          "acc6a2798e464ca2adf1b19aa626f829", "08c1a1abb1bb41869918b94b406c22ce");
      assertThat(accountingOverrideVenues2)
          .isEmpty();

      var accountingOverrideVenues3 = repository.findByTenantIdAndAccountingRadiusId(
          "acc6a2798e464ca2adf1b19aa626f829", "");
      assertThat(accountingOverrideVenues3)
          .isEmpty();
    }
  }


  @Test
  @Sql(
      statements = """
          INSERT INTO tenant (id)
            VALUES ('8829b85337054458bd4ba72bc86052de'),
                   ('b13f49a850ba4517b4941ab5025cbc00');
          INSERT INTO venue (id, tenant)
            VALUES ('8ac2cceddcd849d5acfd37d6e36885f1', '8829b85337054458bd4ba72bc86052de'),
                   ('d041960eda194873a2a1c809cbe6d6cd', '8829b85337054458bd4ba72bc86052de'),
                   ('5beb3e96a19c4700a005cdd14c6e186d', 'b13f49a850ba4517b4941ab5025cbc00');
          """)
  void testFindAllDistinctTenantIds() {
    var result = repository.findAllDistinctTenantIds();
    assertThat(result).isNotEmpty().hasSizeGreaterThanOrEqualTo(2)
        .contains("8829b85337054458bd4ba72bc86052de", "b13f49a850ba4517b4941ab5025cbc00");
  }

  @Test
  @Sql(
      statements =
          """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        'venue1', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model) VALUES (
        '4ce4e037e89f433da02ced7993613955', 'venue1', '4c8279f79307415fa9e4c88a1819f0fc', 'M510');
    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model, band_mode) VALUES (
        'venue_ap_model_specific_attributes1', 'venue1', '4c8279f79307415fa9e4c88a1819f0fc', 'T670', 'TRIPLE');
    INSERT INTO dhcp_service_ap (id, venue, tenant, serial_number) VALUES (
        'dhcp_service_ap1', 'venue1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_serial_number-1');
    INSERT INTO dhcp_service_ap (id, venue, tenant, serial_number) VALUES (
        'dhcp_service_ap2', 'venue1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_serial_number-2');
    INSERT INTO dhcp_service_ap (id, venue, tenant, serial_number) VALUES (
        'dhcp_service_ap3', 'venue1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_serial_number-3');
    """)
  void testDhcpServiceApWithApModelSpecificAttributes() {
    var result = repository.findByIdAndTenantId("venue1", "4c8279f79307415fa9e4c88a1819f0fc");
    assertTrue(result.isPresent());
    var targetVenue = result.get();
    // We suggest that Hibernate has a bug which would cause the one-to-many relationship in the
    // list might have multiple duplicated entities, the duplicated entities would share the same
    // reference.
    // The issue would happen when the following criteria matched
    // 1. The entity has multiple eager one-to-many relationships
    // 2. The relationship is associated as a list
    // ex.
    // a. Venue 1-to-many-eager DhcpServiceAp and Venue 1-to-many-eager
    // VenueApModelSpecificAttributes
    // b. The relationship for Venue-VenueApModelSpecificAttributes is List.

    // Due to the wrong behavior mentioned above, the ModelSpecificAttributes size here would be 2
    // (in DB) * 3 (count of DhcpServiceAp) = 6
    // If someday the bug is fixed by hibernate, the value here should be 2.
    assertEquals(2, targetVenue.getModelSpecificAttributes().size());
    var modelSpecificAttributesSet = Set.copyOf(targetVenue.getModelSpecificAttributes());
    assertEquals(2, modelSpecificAttributesSet.size());

    // If hibernate still has the mentioned wrong behavior, then the collect to map logic should fail and throw an IllegalStateException exception.
    assertThatNoException().isThrownBy(
        () ->
            targetVenue.getModelSpecificAttributes().stream()
                .collect(
                    Collectors.toMap(
                        VenueApModelSpecificAttributes::getModel, Function.identity())));
    // This verifies the enhanced logic in VenueUtils bypassing the hibernate bug.
    assertEquals(2, VenueUtils.getModelAttributesMap(targetVenue).size());
  }

  @Test
  @Sql(
      statements =
          """
          INSERT INTO tenant (id) VALUES ('tenant-01');

          INSERT INTO ap_lan_port_profile (category, id, tenant, enable_auth_proxy, enable_accounting_proxy) VALUES
            ('ETHERNET', 'profile-01', 'tenant-01', 'true', 'true'),
            ('ETHERNET', 'profile-02', 'tenant-01', 'true', 'false');

          INSERT INTO lan_port_adoption (id, checksum, ap_lan_port_profile, tenant) VALUES
            ('adoption-01', 'checksum1', 'profile-01', 'tenant-01');

          INSERT INTO venue (id, tenant) VALUES ('venue-01', 'tenant-01');
          INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model) VALUES
            ('model-specific-01', 'venue-01', 'tenant-01', 'R600');
          INSERT INTO venue_lan_port (id, port_id, venue_ap_model_specific_attributes, ap_lan_port_profile, lan_port_adoption, tenant) VALUES
            ('venue-port-01', '1', 'model-specific-01', 'profile-01', 'adoption-01', 'tenant-01'),
            ('venue-port-02', '2', 'model-specific-01', 'profile-02',          null, 'tenant-01');

          INSERT INTO venue (id, tenant) VALUES ('venue-02', 'tenant-01');
          INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model) VALUES
            ('model-specific-02', 'venue-02', 'tenant-01', 'R700');
          INSERT INTO venue_lan_port (id, port_id, venue_ap_model_specific_attributes, ap_lan_port_profile, lan_port_adoption, tenant) VALUES
            ('venue-port-03', '2', 'model-specific-02', 'profile-01', 'adoption-01', 'tenant-01');
          INSERT INTO ap_group (id, venue, tenant) VALUES ('group-01', 'venue-02', 'tenant-01');
          INSERT INTO ap_model_specific (id, tenant) VALUES ('ap-specific-01', 'tenant-01');
          INSERT INTO ap (id, name, ap_group, tenant, model, enable50g, soft_deleted, model_specific) VALUES
            ('ap-01', 'AP-01', 'group-01', 'tenant-01', 'R777', true, false, 'ap-specific-01');
          INSERT INTO ap_lan_port (id, port_id, model_specific, ap_lan_port_profile, lan_port_adoption, tenant) VALUES
            ('ap-port-01', '1', 'ap-specific-01', 'profile-01', 'adoption-01', 'tenant-01'),
            ('ap-port-02', '2', 'ap-specific-01', 'profile-02',          null, 'tenant-01');
          """)
  void findAllVenueIdsThatNeedToMigrateToLanPortAdoptionByTenantIdTest() {
    var result =
        repository.findAllVenueIdsThatNeedToMigrateToLanPortAdoptionByTenantId("tenant-01");
    assertEquals(2, result.size());
    assertEquals(Set.of("venue-01", "venue-02"), Set.copyOf(result));
  }

  @Test
  @Sql(
      statements =
          """
          INSERT INTO tenant (id) VALUES ('692efa4439344cda9837135c4772ab31');
          INSERT INTO venue (id, tenant, rogue_ap_enabled) VALUES (
              '0909a9aaf84c4d7dbd0edbefd4a910d8', '692efa4439344cda9837135c4772ab31', true);
          INSERT INTO venue (id, tenant, rogue_ap_enabled) VALUES (
              'e58d81aa23fe46bb9d5f440e07acbd0a', '692efa4439344cda9837135c4772ab31', false);
          """)
  void testFindIdsByRogueEnabledAndTenantId() {
    assertThat(repository.findIdsByRogueEnabledAndTenantId("692efa4439344cda9837135c4772ab31"))
        .containsOnly("0909a9aaf84c4d7dbd0edbefd4a910d8");
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant-lbs-01');
      INSERT INTO lbs_server_profile (id, tenant) VALUES
          ('lbs-profile-01', 'tenant-lbs-01'),
          ('lbs-profile-02', 'tenant-lbs-01');
      INSERT INTO venue (id, tenant, lbs_server_profile) VALUES
          ('venue-with-lbs-1', 'tenant-lbs-01', 'lbs-profile-01'),
          ('venue-with-lbs-2', 'tenant-lbs-01', 'lbs-profile-02'),
          ('venue-without-lbs', 'tenant-lbs-01', null);
      """)
  void testFindByTenantIdAndLbsServerProfileIdIsNotNull() {
      var result = repository.findByTenantIdAndLbsServerProfileIdIsNotNull("tenant-lbs-01");
      assertThat(result)
          .extracting(Identifiable::getId)
          .containsExactlyInAnyOrder("venue-with-lbs-1", "venue-with-lbs-2");
  }
}
