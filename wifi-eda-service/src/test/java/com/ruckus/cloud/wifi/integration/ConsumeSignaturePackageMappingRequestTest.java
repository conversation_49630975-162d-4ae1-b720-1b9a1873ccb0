package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackage;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.SignaturePackageMapping;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SignaturePackageMappingTest")
@WifiIntegrationTest
public class ConsumeSignaturePackageMappingRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_SIGNATURE_PACKAGE_MAPPINGS)
  class ConsumeAddSignaturePackagesMappingsTest {

    @Payload
    private final List<SignaturePackageMapping> payload = List.of(
        Generators.signaturePackageMappingGenerator("SignaturePackageMapping-v1", "SignaturePackageMapping-v2").generate());

    @BeforeEach
    void givenRequiredDataPersistedInDb(final Tenant tenant, ApplicationPolicy applicationPolicy) {
      var sp1 = new SignaturePackage("SignaturePackageMapping-v1");
      sp1.setVersion(sp1.getId());
      sp1.setReleasable(true);
      sp1.setDefaultVersion(true);
      sp1.setSignaturePackageMappings(new ArrayList<>());
      sp1 = repositoryUtil.createOrUpdate(sp1, tenant.getId(), randomTxId());
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(final Tenant tenant) {
      // For the addSignaturePackageMappings case, there should no DDCCM
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());
      assertThat(repositoryUtil.find(SignaturePackage.class, "SignaturePackageMapping-v1"))
          .matches(sp -> "SignaturePackageMapping-v2".equals(sp.getNextVersion()))
          .matches(sp -> sp.getSignaturePackageMappings().size() == 1)
          .extracting(sp -> sp.getSignaturePackageMappings().get(0))
          .matches(mapping -> mapping.getSignaturePackageMappingItems().size() == 5);
      assertThat(repositoryUtil.find(SignaturePackage.class, "SignaturePackageMapping-v2"))
          .matches(sp -> sp.getNextVersion() == null)
          .matches(sp -> sp.getSignaturePackageMappings().isEmpty());
    }
  }
}
