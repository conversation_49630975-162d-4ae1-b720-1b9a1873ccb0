package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractTenantAwareBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceAp;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.DhcpServiceApGenerator;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;

@WifiJpaDataTest
public class DhcpServiceApRepositoryTest {

  @Autowired
  DhcpServiceApRepository dhcpServiceApRepository;

  @Autowired
  RepositoryUtil repositoryUtil;

  @Test
  void findByTenantIdAndVenueIdIn(Venue venue) {
    var dhcpApSettings = new DhcpServiceApGenerator().setVenue(always(venue))
        .setSerialNumber(always("123")).generate();
    dhcpApSettings = repositoryUtil.createOrUpdate(dhcpApSettings, venue.getTenant().getId(),
        randomTxId());

    assertThat(dhcpServiceApRepository.findByTenantIdAndVenueIdIn(venue.getTenant().getId(),
        List.of(venue.getId()))).hasSize(1)
        .extracting(AbstractTenantAwareBaseEntity::getId)
        .containsExactly(dhcpApSettings.getId());

    assertThat(dhcpServiceApRepository.findByTenantIdAndVenueIdIn(venue.getTenant().getId(),
        List.of("dummyId"))
        .isEmpty());
  }
}
