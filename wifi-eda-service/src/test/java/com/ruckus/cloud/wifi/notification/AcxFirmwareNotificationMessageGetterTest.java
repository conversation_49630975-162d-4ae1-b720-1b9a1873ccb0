package com.ruckus.cloud.wifi.notification;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.notification.dto.ChangeScheduleTemplateDto;
import com.ruckus.cloud.wifi.notification.dto.ScheduleTemplateDto;
import com.ruckus.cloud.wifi.notification.dto.VenueTemplateDto;
import com.ruckus.cloud.wifi.notification.template.FirmwareNotificationMessage;
import com.ruckus.cloud.wifi.notification.template.UpdateNotificationTemplate;
import com.ruckus.cloud.wifi.service.impl.ApBranchFamilyServiceRouter;
import com.ruckus.cloud.wifi.servicemodel.enums.ApVersionCategory;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class AcxFirmwareNotificationMessageGetterTest {
  @SpyBean
  private AcxFirmwareNotificationMessageGetter messageGetter;

  @MockBean
  private VenueTemplateConverter converter;

  @MockBean
  private ApBranchFamilyServiceRouter apBranchFamilyServiceRouter;

  @Test
  void testCreateScheduleWithMultipleVenues() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    List<Venue> venues = Arrays.asList(new Venue());
    VenueTemplateDto testVenueDto = getMockVenueTemplateDto(1);
    VenueTemplateDto testVenueDto2 = getMockVenueTemplateDto(2);
    Map<String, List<VenueTemplateDto>> venueTemplateMap = Map.of("time-1", List.of(testVenueDto),
        "time-2", List.of(testVenueDto2));
    when(converter.groupVenueTemplateByTimeSlot(anyList(), any())).
        thenReturn(venueTemplateMap);
    VenueUpgradeVersionsMapping venueVersionMapping = VenueUpgradeVersionsMapping.create();

    FirmwareNotificationMessage message = messageGetter.createSchedule(tenant.getName(), tenant, venues, venueVersionMapping);

    verify(converter).groupVenueTemplateByTimeSlot(anyList(), any());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_WITH_IMPACT_MODEL_MULTIPLE_SLOTS_ACX, message.getTemplate());
    verifyModelShcheduleList(message.getModel(), ScheduleTemplateDto.class);
  }

  @Test
  void testChangeScheduleWithScheduledTime() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    String slotDate1 = "time-1@@oldTime-1";
    String slotDate2 = "time-2@@oldTime-2";
    VenueTemplateDto testVenueDto = getMockVenueTemplateDto(1);
    VenueTemplateDto testVenueDto2 = getMockVenueTemplateDto(2);
    ApVersion apVersion = new ApVersion("*********.100");
    apVersion.setCategory(ApVersionCategory.RECOMMENDED);
    apVersion.setName("*********.100");

    FirmwareNotificationMessage message = messageGetter.changeSchedule(tenant.getName(), tenant, apVersion,
        Map.of(slotDate1, List.of(testVenueDto), slotDate2, List.of(testVenueDto2)));

    assertEquals(UpdateNotificationTemplate.CHANGE_SCHEDULE_MULTIPLE_TIME_ZONES_ACX, message.getTemplate());
    verifyModelShcheduleList(message.getModel(), ChangeScheduleTemplateDto.class);
  }

  private VenueTemplateDto getMockVenueTemplateDto(int postfix) {
    VenueTemplateDto testVenueDto= new VenueTemplateDto();
    testVenueDto.setName("venue-" + postfix);
    testVenueDto.setTimeZone("zone-" + postfix);
    testVenueDto.setNumberOfAps(20);
    return testVenueDto;
  }

  private <T> void verifyModelShcheduleList(Map<String, Object> model, Class<T> c) {
    assertThat(model.get("scheduleList"))
        .isNotNull()
        .asList()
        .hasSize(2)
        .allSatisfy(s -> assertThat(s).isExactlyInstanceOf(c));
  }
}
