package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackage;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO signature_package (id, version, default_version, next_version, releasable)
        VALUES ('v1', 'v1', true, 'v2', true);
    INSERT INTO signature_package (id, version, default_version, next_version, releasable)
        VALUES ('v2', 'v2', false, null, true);
    INSERT INTO application_policy (id, name, tenant, signature_package, is_template)
        VALUES ('ap_1', 'policy1', '4c8279f79307415fa9e4c88a1819f0fc', 'v1', false);
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_1_1', 'rule1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_1', 'SIGNATURE',
            1496, 'COMM', 3, 'Audio/Video');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_1_2', 'rule2', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_1', 'SIGNATURE',
            3442, 'Realvnc', 13, 'Game');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_1_3', 'rule3', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_1', 'SIGNATURE',
            1138, 'Stafaband.info', 30, 'Web');
    INSERT INTO application_policy (id, name, tenant, signature_package, is_template)
        VALUES ('ap_2', 'policy1', '4c8279f79307415fa9e4c88a1819f0fc', 'v2', false);
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_2_1', 'rule1', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'SIGNATURE',
            1496, 'COMM', 3, 'Audio/Video');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
        application_id, application_name, category_id, category)
        VALUES ('apr_2_2', 'rule2', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'SIGNATURE',
            3442, 'Realvnc', 13, 'Game');
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_2_3', 'rule3', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_2', 'SIGNATURE',
            1138, 'Stafaband.info', 30, 'Web');
    INSERT INTO application_policy (id, name, tenant, is_template)
        VALUES ('ap_3_template', 'policyTemplate1', '4c8279f79307415fa9e4c88a1819f0fc', true);
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_3', 'rule3', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_3_template', 'SIGNATURE',
            1138, 'Stafaband.info', 30, 'Web');
    INSERT INTO application_policy (id, name, tenant, is_template)
        VALUES ('ap_3_not_template', 'policyTemplate2', '4c8279f79307415fa9e4c88a1819f0fc', false);
    INSERT INTO application_policy_rule (id, name, tenant, application_policy, rule_type,
            application_id, application_name, category_id, category)
        VALUES ('apr_4', 'rule4', '4c8279f79307415fa9e4c88a1819f0fc', 'ap_3_not_template', 'SIGNATURE',
            1138, 'Stafaband.info', 30, 'Web');
    """)
public class ApplicationPolicyRepositoryTest {

  private static final String TENANT_ID ="4c8279f79307415fa9e4c88a1819f0fc";

  @Autowired
  private ApplicationPolicyRepository applicationPolicyRepository;

  @Test
  void findPolicyAndRuleByTenantIdAndSignaturePackageIdInRuleIdsTest() {
    var result = applicationPolicyRepository.findPolicyAndRuleByTenantIdAndSignaturePackageIdInApplicationIds(
        "4c8279f79307415fa9e4c88a1819f0fc", "v1", List.of(1496, 3442, 1138)
    );
    assertEquals(3, result.size());
    var rule1 = result.stream().filter(p -> p.getApplicationId() == 1496).findFirst();
    assertTrue(rule1.isPresent());
    assertEquals("ap_1", rule1.get().getPolicyId());
    assertEquals("policy1", rule1.get().getPolicyName());
    assertEquals("apr_1_1", rule1.get().getRuleId());
    assertEquals("rule1", rule1.get().getRuleName());
    var rule2 = result.stream().filter(p -> p.getApplicationId() == 3442).findFirst();
    assertTrue(rule2.isPresent());
    assertEquals("ap_1", rule2.get().getPolicyId());
    assertEquals("policy1", rule2.get().getPolicyName());
    assertEquals("apr_1_2", rule2.get().getRuleId());
    assertEquals("rule2", rule2.get().getRuleName());
    var rule3 = result.stream().filter(p -> p.getApplicationId() == 1138).findFirst();
    assertTrue(rule3.isPresent());
    assertEquals("ap_1", rule3.get().getPolicyId());
    assertEquals("policy1", rule3.get().getPolicyName());
    assertEquals("apr_1_3", rule3.get().getRuleId());
    assertEquals("rule3", rule3.get().getRuleName());
  }

  @Test
  void findByTenantIdAndSignaturePackageTest() {
    var v2 = new SignaturePackage();
    v2.setId("v2");
    var result = applicationPolicyRepository.findByTenantIdAndSignaturePackage(
        "4c8279f79307415fa9e4c88a1819f0fc", v2
    );
    assertEquals(1, result.size());
    var policy = result.get(0);
    assertEquals("ap_2", policy.getId());
    assertEquals(3, policy.getRules().size());
  }

  @Test
  void deleteByTenantIdAndSignaturePackageTest() {
    var v2 = new SignaturePackage();
    v2.setId("v2");
    var result = applicationPolicyRepository.deleteByTenantIdAndSignaturePackage(
        "4c8279f79307415fa9e4c88a1819f0fc", v2
    );
    assertEquals(1, result);
    var remainPolicies = applicationPolicyRepository.findByTenantId(
        "4c8279f79307415fa9e4c88a1819f0fc");
    assertEquals(2, remainPolicies.size());
    assertEquals(3,
        remainPolicies.stream().filter(p -> p.getId().equals("ap_1")).findAny()
            .orElseThrow().getRules().size());
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantIdAndIsTemplate() {
    var result = applicationPolicyRepository.findByIdAndTenantId("ap_3_template", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("ap_3_template", result.get().getId());
    assertEquals(true, result.get().getIsTemplate());
  }

  @ApplyTemplateFilter
  @Test
  void findByIdAndTenantIdAndIsTemplateWithIsNotTemplateId() {
    var result = applicationPolicyRepository.findByIdAndTenantId("ap_3_not_template", TENANT_ID);

    assertTrue(result.isEmpty());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplate() {
    var result = applicationPolicyRepository.findByIdAndTenantId("ap_3_not_template", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("ap_3_not_template", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }

  @Test
  void findByIdAndTenantIdAndIsNotTemplateWithLegacyData() {
    var result = applicationPolicyRepository.findByIdAndTenantId("ap_2", TENANT_ID);

    assertTrue(result.isPresent());
    assertEquals("ap_2", result.get().getId());
    assertEquals(false, result.get().getIsTemplate());
  }
}
