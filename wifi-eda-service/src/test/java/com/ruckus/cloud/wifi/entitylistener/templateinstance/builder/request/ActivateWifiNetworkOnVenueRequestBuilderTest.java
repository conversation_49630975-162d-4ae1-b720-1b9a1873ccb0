package com.ruckus.cloud.wifi.entitylistener.templateinstance.builder.request;

import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.VENUE_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.WIFI_NETWORK_ID;
import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomNetworkTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenue;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplate;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenueTemplateInstance;
import static com.ruckus.cloud.wifi.test.fixture.TenantTestFixture.randomTenant;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenueTemplateInstance;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.ActivateWifiNetworkOnVenueInstanceOperation;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;

@ExtendWith(TxCtxExtension.class)
@WifiUnitTest
public class ActivateWifiNetworkOnVenueRequestBuilderTest {

  @Autowired
  private ActivateWifiNetworkOnVenueRequestBuilder unit;
  @MockBean
  private VenueRepository venueRepository;
  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private ActivateWifiNetworkOnVenueInstanceOperation operation;

  private Tenant mspTenant;
  private Tenant ecTenant;
  private NetworkVenue networkVenueTemplate;
  private Venue venueTemplate;
  private Network networkTemplate;
  private NetworkVenue networkVenueInstance;
  private Venue venueInstance;
  private Network networkInstance;

  @BeforeEach
  public void setUp() {
    ecTenant = randomTenant((e) -> {
    });
    mspTenant = randomTenant(e -> e.setId(TxCtxHolder.tenantId()));
    unit.setTemplateTenantId(mspTenant.getId());
    unit.setTargetTenantId(ecTenant.getId());
    networkVenueTemplate = randomNetworkVenueTemplate(mspTenant);
    venueTemplate = networkVenueTemplate.getVenue();
    networkTemplate = networkVenueTemplate.getNetwork();
    networkInstance = randomNetworkTemplateInstance(
        networkTemplate.getId(),
        ecTenant);
    venueInstance = randomVenueTemplateInstance(venueTemplate.getId(),
        ecTenant);
    networkVenueInstance = randomNetworkVenueTemplateInstance(networkVenueTemplate.getId(),
        networkInstance, venueInstance);
  }

  @Test
  void testBuild() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));

    var result = unit.build(networkVenueTemplate);

    assertThat(result).isNotEmpty();

    var request = result.get().request();
    var templateEntity = result.get().templateEntity();

    assertThat(request).satisfies(e -> {
      assertThat(e.getTargetTenantId()).isEqualTo(ecTenant.getId());
      assertThat(e.getInstanceId()).isNotEmpty();
      assertThat(e.getOverrides()).isNull();
    });
    assertThat(request.getExtraPathVariables())
        .containsOnly(entry(VENUE_ID, venueInstance.getId()),
            entry(WIFI_NETWORK_ID, networkInstance.getId()));

    assertThat(templateEntity)
        .isEqualTo(networkVenueTemplate);
  }

  @Test
  void testBuild_EntityIsNotTemplate() {
    final var networkVenue = randomNetworkVenue(mspTenant);
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(CommonException.class, () -> unit.build(networkVenue));
  }

  @Test
  void testBuild_NetworkInstanceNotFound() {
    doReturn(Optional.empty())
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.of(venueInstance))
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(NullPointerException.class, () -> unit.build(networkVenueTemplate));
  }

  @Test
  void testBuild_VenueInstanceNotFound() {
    doReturn(Optional.of(networkInstance))
        .when(networkRepository)
        .findByTemplateIdAndTenantId(eq(networkTemplate.getId()),
            eq(ecTenant.getId()));
    doReturn(Optional.empty())
        .when(venueRepository)
        .findByTemplateIdAndTenantId(eq(venueTemplate.getId()),
            eq(ecTenant.getId()));

    assertThrows(NullPointerException.class, () -> unit.build(networkVenueTemplate));
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    ActivateWifiNetworkOnVenueRequestBuilder activateWifiNetworkOnVenueRequestBuilder(
        VenueRepository venueRepository, NetworkRepository networkRepository,
        ActivateWifiNetworkOnVenueInstanceOperation operation) {
      return new ActivateWifiNetworkOnVenueRequestBuilder(networkRepository,
          venueRepository, operation);
    }
  }
}
