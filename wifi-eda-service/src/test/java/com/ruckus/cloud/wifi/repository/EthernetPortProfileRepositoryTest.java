package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.jdbc.Sql;

@Tag("EthernetPortProfileTest")
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tenant (id) VALUES ('2222av90876va9875deafddd09872222');
    INSERT INTO tenant (id) VALUES ('3333av90876va9875deafddd09872233');
    INSERT INTO venue (id, tenant)
        VALUES ('a435afeecd4352ef9876ac2340897521', '2222av90876va9875deafddd09872222');
    INSERT INTO venue (id, tenant)
        VALUES ('e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant)
        VALUES ('77645601d9b043e28905875b8c669760', '4c8279f79307415fa9e4c88a1819f0fc');    

    INSERT INTO radius (id, tenant, is_template) VALUES
        ('radiusId001', '4c8279f79307415fa9e4c88a1819f0fc', 'false'),
        ('radiusId002', '4c8279f79307415fa9e4c88a1819f0fc', 'false'),
        ('radiusId003', '4c8279f79307415fa9e4c88a1819f0fc', 'false'),
        ('radiusId004', '4c8279f79307415fa9e4c88a1819f0fc', 'false');

    INSERT INTO ap_lan_port_profile (category, id,
            tenant, name,
            type, untag_id, vlan_members) VALUES
        ('ETHERNET', 'bb89fdf273e74e8d8c1f5d497538c96e',
            '4c8279f79307415fa9e4c88a1819f0fc', 'profile1',
            'ACCESS', 2, '2'),
        ('ETHERNET', 'bb89fdf273e74e8d8c1f5d497538c96d',
            '4c8279f79307415fa9e4c88a1819f0fc', 'profile2',
            'ACCESS', 2, '2');
    INSERT INTO ap_lan_port_profile (category, id,
            venue_id, tenant,
            type, untag_id, vlan_members) VALUES
        ('ETHERNET', 'bb89fdf273e74e8d8c1f5d497538c96f',
            'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc',
            'ACCESS', 2, '2'),
        ('ETHERNET', '92943f2b5dd84a1eac29d013058dd892',
            'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc',
            'TRUNK', 2, '2');
    INSERT INTO ap_lan_port_profile (category, id,
            venue_id, tenant,
            type, untag_id, vlan_members, vni) VALUES
        ('ETHERNET', '384f09477f5a43a986cabe56c9972631',
            'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc',
            'ACCESS', 1, '1', 123),
        ('PIN', 'd48947d60901466894ecfbbc5bb47bcc',
            null, '4c8279f79307415fa9e4c88a1819f0fc',
            'ACCESS', 1, '1', 123);
    INSERT INTO ap_lan_port_profile (category, id,
            venue_id, tenant, name,
            type, untag_id, vlan_members, is_default) VALUES
        ('ETHERNET', '067f4d0c30ce4aa4960168f52b00ccff',
            'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc', '4c8279f79307415fa9e4c88a1819f0fc_TRUNK',
            'TRUNK', 1, '1-4094', true);
    INSERT INTO ap_lan_port_profile (category, id,
            tenant, name,
            type, untag_id, vlan_members) VALUES
        ('ETHERNET', 'a6e2d04aea5549bea9fcb430d23f9bcf',
            '4c8279f79307415fa9e4c88a1819f0fc', 'TR_2',
            'TRUNK', 2, '2'),
        ('ETHERNET', 'f30dfc2929ec477d85e3ab71b0df1b79',
            '4c8279f79307415fa9e4c88a1819f0fc', 'TR_1',
            'TRUNK', 1, '1');

    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model)
    VALUES ('8fdd2008f6384869831c4ee0cd506ecb', 'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc', 'M510');
    
    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model)
    VALUES ('89b04d08c9ec42d2b0c166f34fa4697a', '77645601d9b043e28905875b8c669760', '4c8279f79307415fa9e4c88a1819f0fc', 'R550');

    INSERT INTO venue_lan_port (id, ap_lan_port_profile, tenant, venue_ap_model_specific_attributes)
    VALUES ('23b83ad2ae2c471bb6e7dcad5aae795f', '92943f2b5dd84a1eac29d013058dd892', '4c8279f79307415fa9e4c88a1819f0fc', '8fdd2008f6384869831c4ee0cd506ecb');
    
    INSERT INTO venue_lan_port (id, ap_lan_port_profile, tenant, venue_ap_model_specific_attributes)
    VALUES ('ac52e7e0eea944478b1c46c55dc385ed', 'a6e2d04aea5549bea9fcb430d23f9bcf', '4c8279f79307415fa9e4c88a1819f0fc', '89b04d08c9ec42d2b0c166f34fa4697a');

    INSERT INTO venue (id, tenant)
    VALUES ('ce6cc14289c149a3a899db78157e8b09', '4c8279f79307415fa9e4c88a1819f0fc');

    INSERT INTO ap_lan_port_profile (category, id, venue_id, tenant, type, untag_id, vlan_members)
        VALUES ('ETHERNET', '9cb61e6656d24e2781c77485c504afae', 'ce6cc14289c149a3a899db78157e8b09', '4c8279f79307415fa9e4c88a1819f0fc', 'TRUNK', 3, '2');
    INSERT INTO ap_lan_port_profile (category, id, venue_id, tenant, type, untag_id, vlan_members, name)
        VALUES ('ETHERNET', '8cb61e6656d24e2781c77485c504afaf', 'ce6cc14289c149a3a899db78157e8b09', '4c8279f79307415fa9e4c88a1819f0fc', 'TRUNK', 4, '3', 'testEthernet');
    INSERT INTO ap_lan_port_profile (category, id, venue_id, tenant, type, untag_id, vlan_members, name, auth_radius, accounting_radius, enable_auth_proxy)
        VALUES ('ETHERNET', '8cb61e6656d24e2781c77485c504afaa', 'ce6cc14289c149a3a899db78157e8b09', '4c8279f79307415fa9e4c88a1819f0fc', 'TRUNK', 4, '3', 'testRadius1', 'radiusId001', 'radiusId002', 'true');
    INSERT INTO ap_lan_port_profile (category, id, venue_id, tenant, type, untag_id, vlan_members, name, auth_radius, enable_auth_proxy)
        VALUES ('ETHERNET', '8cb61e6656d24e2781c77485c504afab', 'ce6cc14289c149a3a899db78157e8b09', '4c8279f79307415fa9e4c88a1819f0fc', 'TRUNK', 4, '3', 'testRadius2', 'radiusId003', 'true');
    INSERT INTO ap_lan_port_profile (category, id, venue_id, tenant, type, untag_id, vlan_members, name, auth_radius, accounting_radius, enable_accounting_proxy)
        VALUES ('ETHERNET', '8cb61e6656d24e2781c77485c504afac', 'ce6cc14289c149a3a899db78157e8b09', '4c8279f79307415fa9e4c88a1819f0fc', 'TRUNK', 4, '3', 'testRadius3', 'radiusId003', 'radiusId004', 'true');
    INSERT INTO ap_lan_port_profile (category, id, venue_id, tenant, type, untag_id, vlan_members, ap_lan_port_id, name, enable_auth_proxy, enable_accounting_proxy)
        VALUES ('ETHERNET', '8cb61e6656d24e2781c77485c504afae', 'ce6cc14289c149a3a899db78157e8b09', '4c8279f79307415fa9e4c88a1819f0fc', 'TRUNK', 5, '6', 5, 'testApLanPort', 'true', 'true');
   INSERT INTO ap_lan_port_profile (category, id, venue_id, tenant, type, untag_id, vlan_members, ap_lan_port_id, name, enable_auth_proxy)
        VALUES ('ETHERNET', '8647654edea674ff243acde24b3c9a4e', 'a435afeecd4352ef9876ac2340897521', '2222av90876va9875deafddd09872222', 'TRUNK', 5, '6', 5, 'testApLanPort2', 'true');
   INSERT INTO ap_lan_port_profile (category, id, tenant, type, untag_id, vlan_members, ap_lan_port_id)
        VALUES ('PIN', '********************************', '3333av90876va9875deafddd09872233', 'TRUNK', 5, '6', 5);
    """)
class EthernetPortProfileRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String VENUE_ID = "e465bac6afb747a4987d0d0945f77221";
  private static final String VENUE_ID_2 = "ce6cc14289c149a3a899db78157e8b09";
  // test for ethernet port profile without venueId
  private static final String VENUE_ID_3 = "77645601d9b043e28905875b8c669760";
  private static final ApLanPortTypeEnum TYPE = ApLanPortTypeEnum.ACCESS;
  private static final short UNTAG_ID = 2;
  private static final String VLAN_MEMBERS = "2";
  private static final String ETH_NAME = "testEthernet";


  @Autowired
  private EthernetPortProfileRepository target;

  @Test
  void findByTenantIdAndNameIsNullTest() {
    List<EthernetPortProfile> result = target.findByTenantIdAndNameIsNull(TENANT_ID);
    assertEquals(4, result.size());
    assertNull(result.get(0).getName());
  }

  @Test
  void findByTenantIdAndApLanPortIdIsNullTest() {
    List<EthernetPortProfile> result = target.findByTenantIdAndApLanPortIdIsNull(TENANT_ID);
    assertEquals(13, result.size());
    assertNull(result.get(0).getApLanPortId());
  }

  @Test
  void findByVenueIdAndTypeAndUntagIdAndVlanMembersTest() {
    var result = target.findByTenantIdAndVenueIdAndTypeAndUntagIdAndVlanMembers(
        TENANT_ID, VENUE_ID, TYPE, UNTAG_ID, VLAN_MEMBERS);
    assertNotNull(result);
    result = target.findByTenantIdAndVenueIdAndTypeAndUntagIdAndVlanMembers(
        TENANT_ID, VENUE_ID, TYPE, UNTAG_ID, "1");
    assertNull(result);
  }

  @Test
  void findByTenantIdAndVenueIdAndTypeAndUntagIdAndVlanMembersAndVniTest() {
    var result = target.findByTenantIdAndVenueIdAndTypeAndUntagIdAndVlanMembersAndVni(
        TENANT_ID, VENUE_ID, TYPE, (short) 1, "1", 123);
    assertNotNull(result);
    result = target.findByTenantIdAndVenueIdAndTypeAndUntagIdAndVlanMembersAndVni(
        TENANT_ID, VENUE_ID, TYPE, (short) 1, "1", null
    );
    assertNull(result);
  }

  @Test
  void existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueIdTest() {
    var shouldBeTRUE = target.existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueId(TENANT_ID, VENUE_ID);
    var shouldBeFALSE = target.existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueId(TENANT_ID, VENUE_ID_2);
    var shouldBeTRUEForEthernetPortProfile = target.existTrunkPortUntagIdNotEqualOneByTenantIdAndVenueId(TENANT_ID, VENUE_ID_3);

    assertTrue(shouldBeTRUE);
    assertFalse(shouldBeFALSE);
    assertTrue(shouldBeTRUEForEthernetPortProfile);
  }

  @Test
  void test_findByTenantIdAndNameLikeIgnoreCase() {
    var result = target.findByTenantIdAndNameLikeIgnoreCase(
        TENANT_ID, ETH_NAME, Pageable.unpaged());
    assertThat(result).isNotEmpty();
    var result2 = target.findByTenantIdAndNameLikeIgnoreCase(
        TENANT_ID, "%TestEther%", Pageable.unpaged());
    assertThat(result2).isNotEmpty().singleElement()
        .satisfies(apLanPortProfile -> assertEquals(ETH_NAME, apLanPortProfile.getName()));

    var result3 = target.findByTenantIdAndNameLikeIgnoreCase(
        TENANT_ID, "%Err%", Pageable.unpaged());
    assertThat(result3).isEmpty();
  }

  @Test
  void test_findByTenantIdAndAuthRadiusId() {
    var result = target.findByTenantIdAndAuthRadiusId(
        TENANT_ID, "radiusId001");
    assertThat(result).isNotEmpty().singleElement()
        .matches(apLanPortProfile -> apLanPortProfile.getName().equals("testRadius1"))
        .matches(
            apLanPortProfile -> apLanPortProfile.getAuthRadius().getId().equals("radiusId001"));
    var result2 = target.findByTenantIdAndAuthRadiusId(
        TENANT_ID, "radiusId003");
    assertThat(result2).isNotEmpty();
    assertThat(result2).matches(
        apLanPortProfiles -> apLanPortProfiles.size() == 2);
  }

  @Test
  void test_findByTenantIdAndAuthRadiusId_empty() {
    var result = target.findByTenantIdAndAuthRadiusId(
        TENANT_ID, "radiusId006");
    assertThat(result).isEmpty();
  }

  @Test
  void test_findByTenantIdAndAccountingRadiusId() {
    var result = target.findByTenantIdAndAccountingRadiusId(
        TENANT_ID, "radiusId002");
    assertThat(result).isNotEmpty().singleElement()
        .matches(apLanPortProfile -> apLanPortProfile.getName().equals("testRadius1"))
        .matches(apLanPortProfile -> apLanPortProfile.getAuthRadius().getId().equals("radiusId001"))
        .matches(apLanPortProfile -> apLanPortProfile.getAccountingRadius().getId()
            .equals("radiusId002"));
    var result2 = target.findByTenantIdAndAccountingRadiusId(
        TENANT_ID, "radiusId004");
    assertThat(result2).isNotEmpty().singleElement()
        .matches(apLanPortProfile -> apLanPortProfile.getName().equals("testRadius3"))
        .matches(apLanPortProfile -> apLanPortProfile.getAuthRadius().getId().equals("radiusId003"))
        .matches(apLanPortProfile -> apLanPortProfile.getAccountingRadius().getId()
            .equals("radiusId004"));
  }

  @Test
  void test_findByTenantIdAndApLanPortId() {
    var result = target.findByTenantIdAndApLanPortIdAndIsTemplate(
        TENANT_ID, 5, false).get();
    assertThat(result)
        .matches(apLanPortProfile -> apLanPortProfile.getName().equals("testApLanPort"), "Wrong name");
  }

  @Test
  void test_duplicateName() {

    assertThat(target.existsByTenantIdAndNameAndIdNot(TENANT_ID, "testApLanPort", "8cb61e6656d24e2781c77485c504afae"))
        .isFalse();

    assertThat(target.existsByTenantIdAndNameAndIdNot(TENANT_ID, "testApLanPort", "8cb61e6656d24e2781c77485c504afac"))
        .isTrue();
  }

  @Test
  void test_findByTypeAndUntagIdAndVlanMembersAndTenantId() {
    var result = target.findByTenantIdAndTypeAndUntagIdAndVlanMembers(
        TENANT_ID, TYPE, UNTAG_ID, VLAN_MEMBERS);
    assertEquals(3, result.size());
    result = target.findByTenantIdAndTypeAndUntagIdAndVlanMembers(
        TENANT_ID, TYPE, UNTAG_ID, "1");
    assertTrue(result.isEmpty());
  }

  @Test
  void test_findByTenantIdAndTypeAndUntagIdAndVlanMembersAndVni() {
    var result = target.findByTenantIdAndTypeAndUntagIdAndVlanMembersAndVni(
        TENANT_ID, TYPE, (short) 1, "1", 123);
    assertEquals(1, result.size());
    result = target.findByTenantIdAndTypeAndUntagIdAndVlanMembersAndVni(
        TENANT_ID, TYPE, (short) 1, "1", null
    );
    assertTrue(result.isEmpty());
  }

  @Test
  void findDistinctTenantIdsWithNullNameInApLanPortProfile() {
    var allDistinctTenantIds = target.findAllDistinctTenantIds();
    assertEquals(2, allDistinctTenantIds.size());

    var result = target.findDistinctTenantIdsWithNullNameInEthernetPortProfile();
    assertEquals(1, result.size());
    assertEquals(TENANT_ID, result.get(0));
  }

  @Test
  void findDistinctTenantIdsWithIsDefaultTrueInEthernetPortProfile() {
    var allDistinctTenantIds = target.findAllDistinctTenantIds();
    assertEquals(2, allDistinctTenantIds.size());

    var result = target.findDistinctTenantIdsWithIsDefaultTrueInEthernetPortProfile();
    assertEquals(1, result.size());
    assertEquals(TENANT_ID, result.get(0));
  }

  @Test
  void test_countByTenantIdAndEnableAuthProxyTrueAndIdIn() {
    long r = target.countByTenantIdAndEnableAuthProxyTrueAndIdIn("2222av90876va9875deafddd09872222",
        List.of("8647654edea674ff243acde24b3c9a4e"));
    assertEquals(1, r);

    long r2 = target.countByTenantIdAndEnableAuthProxyTrueAndIdIn(TENANT_ID,
        List.of("8cb61e6656d24e2781c77485c504afae", "8cb61e6656d24e2781c77485c504afac"));
    assertEquals(1, r2);

    long r3 = target.countByTenantIdAndEnableAuthProxyTrueAndIdIn(TENANT_ID,
        List.of("8cb61e6656d24e2781c77485c504afae", "8cb61e6656d24e2781c77485c504afac", "8cb61e6656d24e2781c77485c504afab",
            "8cb61e6656d24e2781c77485c504afaa", "8cb61e6656d24e2781c77485c504afaf"));
    assertEquals(3, r3);
  }

  @Test
  void test_countByTenantIdAndEnableAccountingProxyTrueAndIdIn() {
    long r = target.countByTenantIdAndEnableAccountingProxyTrueAndIdIn("2222av90876va9875deafddd09872222",
        List.of("8647654edea674ff243acde24b3c9a4e"));
    assertEquals(0, r);

    long r2 = target.countByTenantIdAndEnableAccountingProxyTrueAndIdIn(TENANT_ID,
        List.of("8cb61e6656d24e2781c77485c504afae", "8cb61e6656d24e2781c77485c504afac"));
    assertEquals(2, r2);

    long r3 = target.countByTenantIdAndEnableAccountingProxyTrueAndIdIn(TENANT_ID,
        List.of("8cb61e6656d24e2781c77485c504afae", "8cb61e6656d24e2781c77485c504afac", "8cb61e6656d24e2781c77485c504afab",
            "8cb61e6656d24e2781c77485c504afaa", "8cb61e6656d24e2781c77485c504afaf"));
    assertEquals(2, r3);
  }

  @Test
  void test_findByTenantIdAndIsDefault() {
    var result = target.findByTenantIdAndIsDefault("4c8279f79307415fa9e4c88a1819f0fc", true);
    assertEquals(1, result.size());
  }
}
