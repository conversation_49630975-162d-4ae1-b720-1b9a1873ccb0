package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_L3ACL_POLICY;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_L3ACL_POLICY_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3IpPort;
import com.ruckus.cloud.wifi.eda.servicemodel.L3Rule;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.L3AclPolicyRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.L3AclPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddL3AclPolicyByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private L3AclPolicyRepository l3AclPolicyRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void add_l3Acl_policies(Tenant mspTenant, @Template L3AclPolicy policy) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // create ec policy set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_L3ACL_POLICY,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.L3ACL_POLICY, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, policy.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_L3ACL_POLICY_BY_TEMPLATE,
        ADD_L3ACL_POLICY_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_L3ACL_POLICY, ecTenantId);
    assertActivityStatusSuccess(ADD_L3ACL_POLICY, ecTenantId);
    assertActivityStatusSuccess(ADD_L3ACL_POLICY_BY_TEMPLATE, mspTenantId);

    // check policy rule in ec policy not override MSP's rule
    L3AclPolicy mspPolicy =
        repositoryUtil.find(L3AclPolicy.class, policy.getId(), mspTenantId, true);
    L3AclPolicy ecPolicy = l3AclPolicyRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecPolicy.getId());
    assertL3AclPolicy(mspPolicy, ecPolicy);

    // check policy rule id and tenant in ec policy is different from MSP
    assertNotEquals(mspPolicy.getL3Rules().stream().map(L3Rule::getId).collect(Collectors.toSet()),
        ecPolicy.getL3Rules().stream().map(L3Rule::getId).collect(Collectors.toSet()));

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert 1 viewmodel ops: L3AclPolicy",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME,
            ecPolicy.getId(), ecPolicy.getName())
    );
  }

  @Test
  public void ec_add_l3Acl_policy_fail_then_msp_activity_should_fail(Tenant mspTenant, @Template L3AclPolicy policy) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // policy already in ec tenant before apply template
    L3AclPolicy existedEcPolicy = L3AclPolicyTestFixture.randomL3AclPolicy(ecTenant, (o) -> {
      o.setName(policy.getName());
    });
    repositoryUtil.createOrUpdate(existedEcPolicy, ecTenant.getId(), randomTxId());

    // create ec policy set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_L3ACL_POLICY,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.L3ACL_POLICY, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, policy.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_L3ACL_POLICY_BY_TEMPLATE,
        ADD_L3ACL_POLICY_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_L3ACL_POLICY, ecTenantId);
    assertActivityStatusFail(ADD_L3ACL_POLICY, ecTenantId);
    assertActivityStatusFail(ApiFlowNames.ADD_L3ACL_POLICY_BY_TEMPLATE, mspTenantId);
  }

  private static void assertViewmodelCollector(List<Operations> operations, OpType opType,
      String index, String id, String name) {
    assertTrue(operations.stream()
        .filter(o -> index.equals(o.getIndex()))
        .filter(o -> id.equals(o.getId()))
        .filter(o -> o.getOpType() == opType)
        .anyMatch(o -> name.equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())));
  }

  private void assertL3AclPolicy(L3AclPolicy sourceL3Acl, L3AclPolicy targetL3Acl) {
    Assertions.assertTrue(((sourceL3Acl == null && targetL3Acl == null) || (sourceL3Acl != null
        && targetL3Acl != null)), "sourceL3Acl and targetL3Acl should be null or not null together");
    if (sourceL3Acl != null) {
      assertEquals(sourceL3Acl.getDefaultAccess(), targetL3Acl.getDefaultAccess(), "default access should be equal");
      Assertions.assertTrue(((sourceL3Acl.getL3Rules() == null && targetL3Acl.getL3Rules() == null)
          || (sourceL3Acl.getL3Rules() != null && targetL3Acl.getL3Rules() != null)), "sourceL3Acl's rules and targetL3Acl's rules should be null or not null together");
      if (sourceL3Acl.getL3Rules() == null) {
        return;
      }
      assertEquals(sourceL3Acl.getL3Rules().size(), targetL3Acl.getL3Rules().size(), "sourceL3Acl's rules and targetL3Acl's rules should have equal size");
      Map<Integer, L3Rule> map = new HashMap<>();
      for (L3Rule targetL3Rule : targetL3Acl.getL3Rules()) {
        map.put(targetL3Rule.getPriority(), targetL3Rule);
      }
      for (L3Rule sourceL3Rule : sourceL3Acl.getL3Rules()) {
        assertL3AclRule(sourceL3Rule, map.get(sourceL3Rule.getPriority()));
      }
      assertEquals(sourceL3Acl.getDefaultAccess(), targetL3Acl.getDefaultAccess(), "l3 Acl default access value should be equal");
    }
  }

  private void assertL3AclRule(L3Rule sourceL3Rule, L3Rule targetL3Rule) {
    assertEquals(sourceL3Rule.getPriority(), targetL3Rule.getPriority(), "sourceL3Rule and targetL3Rule should have same priority ");
    assertEquals(sourceL3Rule.getDescription(), targetL3Rule.getDescription(), "sourceL3Rule and targetL3Rule should have same description ");
    assertEquals(sourceL3Rule.getAccess(), targetL3Rule.getAccess(), "sourceL3Rule and targetL3Rule should have same access type");
    assertEquals(sourceL3Rule.getCustomProtocol(), targetL3Rule.getCustomProtocol(), "sourceL3Rule and targetL3Rule should have same getCustomProtocol");
    assertEquals(sourceL3Rule.getProtocol(), targetL3Rule.getProtocol(), "sourceL3Rule and targetL3Rule should have same protocol");
    assertL3RuleIpPort(sourceL3Rule.getSource(), targetL3Rule.getSource());
    assertL3RuleIpPort(sourceL3Rule.getDestination(), targetL3Rule.getDestination());
  }

  private void assertL3RuleIpPort(L3IpPort sourceL3IpPort, L3IpPort targetL3IpPort) {
    if (sourceL3IpPort != null && targetL3IpPort != null) {
      assertEquals(sourceL3IpPort.getIp(), targetL3IpPort.getIp(), "sourceL3IpPort and targetL3IpPort should have same value of ip");
      assertEquals(sourceL3IpPort.getEnableIpSubnet(), targetL3IpPort.getEnableIpSubnet(), "sourceL3IpPort and targetL3IpPort should have same value of enableIpSubnet");
      assertEquals(sourceL3IpPort.getIpMask(), targetL3IpPort.getIpMask(), "sourceL3IpPort and targetL3IpPort should have same value of ipMask");
      assertEquals(sourceL3IpPort.getPort(), targetL3IpPort.getPort(), "sourceL3IpPort and targetL3IpPort should have same value of port");
    }
  }

  private void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }
}
