package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeInteger;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Operations.Builder;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.PinLanPortProfile;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.function.Consumer;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Tag("VxLanTunnelFeatureTest")
@MockBean({
    ApLanPortRepository.class,
    VenueLanPortRepository.class
})
@WifiUnitTest
class PinLanPortProfileCmnCfgCollectorOperationBuilderTest {

  @SpyBean
  private PinLanPortProfileCmnCfgCollectorOperationBuilder unit;

  @Test
  void testGetEntityClass() {
    assertThat(unit.entityClass()).isEqualTo(PinLanPortProfile.class);
  }

  @Test
  void testGetIndex() {
    assertThat(unit.index()).isEqualTo(Index.ETHERNET_PORT_PROFILE_INDEX_NAME);
  }

  @Nested
  class ConfigTest {

    @Nested
    class GivenAddEntityAction extends AbstractBaseTest {
      @Override
      protected EntityAction givenAction() {
        return EntityAction.ADD;
      }
    }

    @Nested
    class GivenModifyEntityAction extends AbstractBaseTest {
      @Override
      protected EntityAction givenAction() {
        return EntityAction.MODIFY;
      }
    }

    private abstract class AbstractBaseTest {

      protected abstract EntityAction givenAction();

      @Test
      void givenPinLanPortProfileSunnyDay() {
        whenConfigThenAssertResult(givenAction(),
            generatePinLanPortProfile(rangeInteger(8193).setRandom(true).generate()));
      }

      @Test
      void givenPinLanPortProfileWithNullVni() {
        whenConfigThenAssertResult(givenAction(), generatePinLanPortProfile(null));
      }

      @Test
      void givenPinLanPortProfileWithZeroVni() {
        whenConfigThenAssertResult(givenAction(), generatePinLanPortProfile(0));
      }

      @Test
      void givenPinLanPortProfileWithNegativeValueVni() {
        whenConfigThenAssertResult(givenAction(), generatePinLanPortProfile(-1234));
      }
    }

    private void whenConfigThenAssertResult(EntityAction action, PinLanPortProfile given) {
      final Builder builderResult = Operations.newBuilder();
      unit.config(builderResult, given, action);
      assertResult(given, builderResult);
    }

    private static PinLanPortProfile generatePinLanPortProfile(Integer givenVni) {
      return Generators.pinLanPortProfile()
          .setTenant(Generators.tenant())
          .setVni(always(givenVni)).generate();
    }

    private static void assertResult(PinLanPortProfile given, Builder builderResult) {
      assertThat(builderResult.build().getDocMap())
          .containsEntry(Key.IS_DEFAULT, ValueUtils.boolValue(false))
          .hasEntrySatisfying(Key.VNI, assertVniValue(given.getVni()));
    }

    private static Consumer<Value> assertVniValue(Integer givenVni) {
      return actualVniValue -> {
        if (givenVni != null && givenVni > 0) {
          assertThat(actualVniValue).isEqualTo(ValueUtils.numberValue(givenVni.doubleValue()));
        } else {
          assertThat(actualVniValue).isEqualTo(ValueUtils.nullValue());
        }
      };
    }
  }
}
