package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.core.model.Identifiable;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fd');
    INSERT INTO hotspot20operator (id, name, tenant) VALUES
        ('059796b87b054c359a9828a354aab593', 'hotspot20operator-1', '4c8279f79307415fa9e4c88a1819f0fd'),
        ('8cfcd0b846cb4eb78089fbc76067a1fa', 'hotspot20operator-2', '4c8279f79307415fa9e4c88a1819f0fd');
    INSERT INTO hotspot20friendly_name (id, tenant, name, language, operator) VALUES
        ('ec0a51f6bde14074b06dcdb2d6a18381', '4c8279f79307415fa9e4c88a1819f0fd', 'friendlyName-1', 'ENG', '059796b87b054c359a9828a354aab593'),
        ('1659bedba86c43faa575bfc8c05f5c40', '4c8279f79307415fa9e4c88a1819f0fd', 'friendlyName-2', 'ENG', '8cfcd0b846cb4eb78089fbc76067a1fa');
    INSERT INTO network_hotspot20settings (id, tenant, operator) VALUES
        ('ab0a51f6bde14074b06dcdb2d6a18381', '4c8279f79307415fa9e4c88a1819f0fd', '059796b87b054c359a9828a354aab593'),
        ('be632d9bee66441abddaa3cd6df9cfdb', '4c8279f79307415fa9e4c88a1819f0fd', '059796b87b054c359a9828a354aab593');
    INSERT INTO network (id, name, type, tenant, hotspot20settings) VALUES
        ('c9845a491cbc43d596ffcf3b5fca8c40','hotspot 2.0 network 1','HOTSPOT20','4c8279f79307415fa9e4c88a1819f0fd', 'ab0a51f6bde14074b06dcdb2d6a18381'),
        ('cfc8cd6e16eb4fdeb1aa4b86364177dc','hotspot 2.0 network 2','HOTSPOT20','4c8279f79307415fa9e4c88a1819f0fd', 'be632d9bee66441abddaa3cd6df9cfdb');
    INSERT INTO radius (id, tenant, is_template) VALUES
        ('ecd6c212248f42b7afaf6a079d5375aa', '4c8279f79307415fa9e4c88a1819f0fd', false);
    INSERT INTO auth_radius_service (id, tenant, radius) VALUES
        ('6037a6db43774a64b9b73f332de2a52e', '4c8279f79307415fa9e4c88a1819f0fd', 'ecd6c212248f42b7afaf6a079d5375aa');
    INSERT INTO auth_radius_profile (id, tenant, auth_radius_service) VALUES
        ('8bf07bc24e0342c780f19c270bf79340', '4c8279f79307415fa9e4c88a1819f0fd', '6037a6db43774a64b9b73f332de2a52e');
    INSERT INTO hotspot20identity_provider (id, name, auth_radius, auth_radius_profile, tenant) VALUES
        ('049890c118734be0b0666e5daed74098', 'hotspot20IdentityProvider-1', 'ecd6c212248f42b7afaf6a079d5375aa', '8bf07bc24e0342c780f19c270bf79340', '4c8279f79307415fa9e4c88a1819f0fd'),
        ('affaf3fbfd0a4aed8b643988a5ba0f9d', 'hotspot20IdentityProvider-2', 'ecd6c212248f42b7afaf6a079d5375aa', '8bf07bc24e0342c780f19c270bf79340', '4c8279f79307415fa9e4c88a1819f0fd'),
        ('4ea67fb6a3724936877927deba2ed00d', 'hotspot20IdentityProvider-3', 'ecd6c212248f42b7afaf6a079d5375aa', '8bf07bc24e0342c780f19c270bf79340', '4c8279f79307415fa9e4c88a1819f0fd');
    INSERT INTO hotspot20_settings_identity_provider (identity_provider, network_hotspot20settings) VALUES
        ('049890c118734be0b0666e5daed74098', 'ab0a51f6bde14074b06dcdb2d6a18381'),
        ('affaf3fbfd0a4aed8b643988a5ba0f9d', 'ab0a51f6bde14074b06dcdb2d6a18381'),
        ('4ea67fb6a3724936877927deba2ed00d', 'be632d9bee66441abddaa3cd6df9cfdb');
    """)
public class Hotspot20NetworkRepositoryTest {

  @Autowired
  private Hotspot20NetworkRepository target;

  @Test
  void testExistsByTenantIdAndHotspot20SettingsOperatorId() {
    var existsExpected = target.existsByTenantIdAndHotspot20SettingsOperatorId(
        "4c8279f79307415fa9e4c88a1819f0fd", "059796b87b054c359a9828a354aab593");
    assertTrue(existsExpected);

    var notExistsExpected = target.existsByTenantIdAndHotspot20SettingsOperatorId(
        "4c8279f79307415fa9e4c88a1819f0fd", "8cfcd0b846cb4eb78089fbc76067a1fa");
    assertFalse(notExistsExpected);
  }

  @Test
  void testFindNetworkIdByOperatorId() {
    var result = target.findByTenantIdAndHotspot20SettingsOperatorId(
        "4c8279f79307415fa9e4c88a1819f0fd", "059796b87b054c359a9828a354aab593");
    assertEquals(2, result.size());
    assertThat(result).extracting(Identifiable::getId).matches(
        ids -> ids.containsAll(List.of("c9845a491cbc43d596ffcf3b5fca8c40", "cfc8cd6e16eb4fdeb1aa4b86364177dc")));;
  }

  @Test
  void testFindNetworkIdByIdentityProviderId() {
    var result = target.findDistinctByTenantIdAndHotspot20SettingsIdentityProvidersIdIn(
        "4c8279f79307415fa9e4c88a1819f0fd", List.of(
            "049890c118734be0b0666e5daed74098",
            "affaf3fbfd0a4aed8b643988a5ba0f9d",
            "4ea67fb6a3724936877927deba2ed00d"
        ));
    assertEquals( 2, result.size());
    assertThat(result).extracting(Identifiable::getId).matches(
        ids -> ids.containsAll(List.of("c9845a491cbc43d596ffcf3b5fca8c40", "cfc8cd6e16eb4fdeb1aa4b86364177dc")));;
  }

}
