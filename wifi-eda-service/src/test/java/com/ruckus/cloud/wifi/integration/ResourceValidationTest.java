package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.resource.delegate.PropertiesResourceDelegate;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ResourceValidationTest {

  @Autowired
  private PropertiesResourceDelegate propertiesResourceDelegate;
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;

  @Tag("ApGroupTest")
  @Test
  public void addApGroupTest(Venue venue, @DefaultApGroup ApGroup apGroup) {
    doReturn(2).when(propertiesResourceDelegate).get(anyString(), eq("venue.ap-group"));

    addApGroup(venue);
    // Limit = 3, one reserve for default AP group, so only one Ap group can be added.
    assertThatExceptionOfType(RuntimeException.class)
        .isThrownBy(() -> addApGroup(venue))
        .withRootCauseInstanceOf(CommonException.class);
  }

  @Test
  public void addNetworkVenueTest(Tenant tenant, Venue venue) throws Exception {
    doReturn(2).when(propertiesResourceDelegate).get(anyString(), eq("venue.network-activation"));

    List<Network> networks = Stream.generate(() -> NetworkTestFixture.randomNetwork(tenant))
        .limit(3)
        .peek(e -> repositoryUtil.createOrUpdate(e, tenant.getId(), randomTxId()))
        .collect(Collectors.toList());

    activateNetwork(networks.get(0), venue.getId());
    activateNetwork(networks.get(1), venue.getId());
    assertThatExceptionOfType(RuntimeException.class)
        .isThrownBy(() -> activateNetwork(networks.get(2), venue.getId()))
        .withRootCauseInstanceOf(CommonException.class);
  }

  @Test
  public void addNetworkVenuesTest(Tenant tenant, Venue venue) {
    doReturn(2).when(propertiesResourceDelegate).get(anyString(), eq("venue.network-activation"));

    List<Network> networks = Stream.generate(() -> NetworkTestFixture.randomNetwork(tenant))
        .limit(3)
        .peek(e -> repositoryUtil.createOrUpdate(e, tenant.getId(), randomTxId()))
        .collect(Collectors.toList());

    assertThatExceptionOfType(RuntimeException.class)
        .isThrownBy(() -> activateNetworks(networks, venue.getId(), tenant.getId()))
        .withRootCauseInstanceOf(CommonException.class);
  }

  private void addApGroup(Venue venue) {
    final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.ApGroup();
    payload.setId(randomId()); // autoGenerated is true in wifi-api
    payload.setName(randomName());

    RequestParams requestParams = new RequestParams();
    requestParams.addPathVariable("venueId", venue.getId());

    messageUtil.sendWifiCfgRequest(
        venue.getTenant().getId(), randomTxId(), CfgAction.ADD_AP_GROUP, randomName(),
        requestParams, payload);
  }

  private void activateNetwork(Network network, String venueId) {
    final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
    payload.setId(randomId()); // autoGenerated is true in wifi-api
    payload.setNetworkId(network.getId());
    payload.setVenueId(venueId);
    payload.setIsAllApGroups(true);
    payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types

    messageUtil.sendWifiCfgRequest(network.getTenant().getId(), randomTxId(),
        CfgAction.ADD_NETWORK_VENUE, randomName(), payload);
  }

  private void activateNetworks(List<Network> networks, String venueId, String tenantId) {

    var payload = networks.stream().map(network -> {
      final var networkVenue = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
      networkVenue.setId(randomId()); // autoGenerated is true in wifi-api
      networkVenue.setNetworkId(network.getId());
      networkVenue.setVenueId(venueId);
      networkVenue.setIsAllApGroups(true);
      networkVenue.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radi
      return networkVenue;
    }).collect(Collectors.toList());


    messageUtil.sendWifiCfgRequest(tenantId, randomTxId(),
        CfgAction.ADD_NETWORK_VENUE_MAPPINGS, randomName(), payload);
  }
}
