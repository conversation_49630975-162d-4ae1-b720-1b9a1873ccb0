package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.MessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class V0_1293__TenantCurrentFirmwarePostMigrationConsumerTest {

  @Autowired
  private V0_1293__TenantCurrentFirmwarePostMigrationConsumer postMigrationConsumer;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private MessageUtil messageUtil;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  private MessageCaptors messageCaptors;

  @Test
  void testPostMigrate(
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.200.100") ApVersion version)
      throws Exception {
    List<Tenant> tenantsWithLatestReleaseVersion = List.of(
        TenantTestFixture.randomTenant(t -> t.setLatestReleaseVersion(version)),
        TenantTestFixture.randomTenant(t -> t.setLatestReleaseVersion(version)),
        TenantTestFixture.randomTenant(t -> t.setLatestReleaseVersion(version)),
        TenantTestFixture.randomTenant(t -> t.setLatestReleaseVersion(version)));
    tenantsWithLatestReleaseVersion.forEach(
        tenant -> repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId()));

    postMigrationConsumer.run(null);

    final var tenantIds = tenantsWithLatestReleaseVersion.stream().map(AbstractBaseEntity::getId)
        .collect(Collectors.toSet());
    final var message = messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenantIds, tenantIds.size());

    assertThat(message)
        .isNotNull()
        .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID) != null)
        .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID) != null)
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(WifiAsyncJob::getJobCase)
        .isEqualTo(WifiAsyncJob.JobCase.TENANT_CURRENT_FIRMWARE_POST_MIGRATION_JOB);

    String receivedTenantId = new String(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID).value());
    assertTrue(tenantIds.contains(receivedTenantId));
    assertThat(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID).value()).isNotNull();
  }
}
