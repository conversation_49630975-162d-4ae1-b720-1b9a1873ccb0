package com.ruckus.cloud.wifi.integration.networkactivation;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeShort;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.vlanPool;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.networkApGroup;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertNetworkVenueSoftly;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTimeSlot;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.Int32Value;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.AaaType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkApGroup;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.VenueDecorator;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.DPSKNetworkTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

@Tag("NetworkActivationTest")
@WifiIntegrationTest
class ConsumeAddNetworkActivationRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class GivenOpenNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String apGroupId;

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupPersistedInDb(@OpenNetwork Network network,
        Venue venue, @DefaultApGroup ApGroup apGroup, Ap ap) {
      networkId = network.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE.key());
    }

    @Payload("WithSpecificApGroup")
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithSpecificApGroup() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(false);
      final var networkApGroup = new NetworkApGroup();
      networkApGroup.setApGroupId(apGroupId);
      networkApGroup.setVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      networkApGroup.setRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      payload.setApGroups(List.of(networkApGroup));
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE, payload = @Payload("WithSpecificApGroup"))
    void thenShouldHandleTheRequestWithSpecificApGroupSuccessfully(TxCtx txCtx,
        @Payload("WithSpecificApGroup") com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE.key());
    }

    @Payload("WithNoRadioTypes")
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithNoRadioTypes() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      // No Radio types
//       payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio ty
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE, payload = @Payload("WithNoRadioTypes"))
    void thenShouldHandleTheRequestSuccessfullyAndApplyDefaultRadioTypes(TxCtx txCtx,
        @Payload("WithNoRadioTypes") com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE.key());

      NetworkVenue result = repositoryUtil.find(NetworkVenue.class, payload.getId());
      assertThat(result)
          .isNotNull()
          .extracting(NetworkVenue::getAllApGroupsRadioTypes)
          .asList()
          .isNotEmpty();
    }

    @Nested
    class WithMoreApGroups {

      private String emptyApGroupId;

      @BeforeEach
      void givenOneEmptyApGroup(Tenant tenant, Venue venue) {
        var emptyApGroup = repositoryUtil.createOrUpdate(
            ApGroupTestFixture.randomApGroup(venue, (e) -> e.setIsDefault(true)), tenant.getId(),
            randomTxId());
        emptyApGroupId = emptyApGroup.getId();
      }

      @Payload("WithSpecificEmptyApGroup")
      private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithSpecificEmptyApGroup() {
        final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
        payload.setId(randomId()); // autoGenerated is true in wifi-api
        payload.setNetworkId(networkId);
        payload.setVenueId(venueId);
        payload.setIsAllApGroups(false);
        final var networkApGroup = new NetworkApGroup();
        networkApGroup.setApGroupId(emptyApGroupId);
        networkApGroup.setVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
        networkApGroup.setRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
        payload.setApGroups(List.of(networkApGroup));
        payload.setScheduler(dummyNetworkVenueScheduler());
        return payload;
      }

      @Test
      @ApiAction(value = CfgAction.ADD_NETWORK_VENUE, payload = @Payload("WithSpecificEmptyApGroup"))
      void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
          @Payload("WithSpecificEmptyApGroup") com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE.key());
      }
    }


    @Tag("VlanPoolTest")
    @Nested
    class WithVlanPoolAssociation {

      private String vlanPoolId;

      @BeforeEach
      void givenOneVlanPoolPersistedInDb(final Tenant tenant) {
        final var vlanPool = vlanPool().generate();
        repositoryUtil.createOrUpdate(vlanPool, tenant.getId(), randomTxId());
        vlanPoolId = vlanPool.getId();

      }

      @Payload
      private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithVlanPool() {
        final var generator = Generators.networkVenue()
            .setNetworkId(always(networkId))
            .setVenueId(always(venueId))
            .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM))
            .setVlanPoolId(always(vlanPoolId));
        return generator.generate();
      }

      @Test
      @ApiAction(CfgAction.ADD_NETWORK_VENUE)
      void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
          @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE.key());
      }

      @Payload("WithVlanPoolInApGroup")
      private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payloadWithVlanPoolInApGroup() {
        final var generator = Generators.networkVenue()
            .setNetworkId(always(networkId))
            .setVenueId(always(venueId))
            .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM))
            .setIsAllApGroups(alwaysFalse())
            .setApGroups(list(networkApGroup()
                .setId(nullValue(String.class))
                .setApGroupId(always(apGroupId))
                .setVlanPoolId(always(vlanPoolId)), 1));
        return generator.generate();
      }

      @Test
      @ApiAction(value = CfgAction.ADD_NETWORK_VENUE, payload = @Payload("WithVlanPoolInApGroup"))
      void thenShouldHandleTheRequestWithVlanPoolInApGroupSuccessfully(TxCtx txCtx,
          @Payload("WithVlanPoolInApGroup") com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
        validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE.key());
      }
    }

    @Nested
    class WithMacRegistration {

      @BeforeEach
      void givenOpenNetworkWithMacRegistrationPersistedInDb(@OpenNetwork Network network,
          Venue venue,
          @DefaultApGroup ApGroup apGroup) {
        network.getWlan().setMacRegistrationListId(randomId());
        network.getWlan().setMacAddressAuthentication(true);
        repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());
      }

      @Payload
      private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
        final var generator = Generators.networkVenue()
            .setNetworkId(always(networkId))
            .setVenueId(always(venueId))
            .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM));
        return generator.generate();
      }

      @Test
      @ApiAction(CfgAction.ADD_NETWORK_VENUE)
      void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
          @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload)
          throws InvalidProtocolBufferException {
        final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(txCtx);
        assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(Operation::hasWlanVenue).first().matches(operation ->
                operation.getWlanVenue().getAuthenticationServerId()
                    .equals(txCtx.getTenant() + "-Auth-Radius-AAA") && operation.getWlanVenue()
                    .getAuthAaa().getThroughController() && operation.getWlanVenue().getAuthAaa()
                    .getType().equals(AaaType.RADIUS));
      }
    }
  }

  @Nested
  class GivenOpenNetworkTemplatePersistedInDb {

    private String networkId;
    private String venueId;

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupPersistedInDb(
        @OpenNetwork(isTemplate = true) Network network,
        @VenueDecorator(isTemplate = true) Venue venue,
        @DefaultApGroup ApGroup apGroup, Ap ap) {
      networkId = network.getId();
      venueId = venue.getId();
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK_VENUE_TEMPLATE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE_TEMPLATE.key());

      NetworkVenue networkVenue = repositoryUtil.find(NetworkVenue.class, payload.getId());
      assertThat(networkVenue.getIsTemplate()).isTrue();
    }
  }

  @FeatureFlag(enable = WIFI_EDA_WPA3_DSAE_TOGGLE)
  @Nested
  class GivenDsaeNetworkTemplatePersistedInDb {

    private String venueId;
    private String dsaeServiceNetworkId;

    @BeforeEach
    void givenDsaeServiceAndOnboardNetworksPersistedInDb(Tenant tenant, @Template Venue venue,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.DpskNetwork(
            wlanSecurity = WlanSecurityEnum.WPA23Mixed, isDsaeServiceNetwork = true, isTemplate = true)
        com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork dsaeServiceNetwork,
        AuthRadiusService authRadiusService) {
      venueId = venue.getId();
      dsaeServiceNetworkId = dsaeServiceNetwork.getId();
      repositoryUtil.createOrUpdate(
          DPSKNetworkTestFixture.randomDpskNetwork(tenant, authRadiusService,
              network -> {
                network.setIsTemplate(true);
                network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
                network.setDsaeNetworkPairId(dsaeServiceNetworkId);
              }), tenant.getId(), randomTxId());
      dsaeServiceNetwork.setDsaeNetworkPairId(dsaeServiceNetworkId);
      repositoryUtil.createOrUpdate(dsaeServiceNetwork, tenant.getId(), randomTxId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(dsaeServiceNetworkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK_VENUE_TEMPLATE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateDsaeResult(txCtx.getTenant(), txCtx.getTxId(),
          CfgAction.ADD_NETWORK_VENUE_TEMPLATE.key());

      NetworkVenue networkVenue = repositoryUtil.find(NetworkVenue.class, payload.getId());
      assertThat(networkVenue.getIsTemplate()).isTrue();
    }

    void validateDsaeResult(String tenantId, String requestId, String apiAction) {
      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                  .as("The ADD NetworkVenue operation count should be 2")
                  .hasSize(2)));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(apiAction))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();
    }
  }

  @Nested
  class GivenOweTransitionNetworkPersistedInDb {

    private String networkId;
    private String venueId;

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupPersistedInDb(Tenant tenant,
        Venue venue, @DefaultApGroup ApGroup apGroup, Ap ap) {
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      Network master = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .generate();
      Network slave = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setIsOweMaster(true);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setOwePairNetworkId(
          slave.getId());
      master = repositoryUtil.createOrUpdate(master, master.getTenant().getId(), randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setOwePairNetworkId(
          master.getId());
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setIsOweMaster(false);
      slave = repositoryUtil.createOrUpdate(slave, slave.getTenant().getId(), randomTxId());

      networkId = master.getId();
      venueId = venue.getId();
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateOweTransitionResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE.key());
    }

    void validateOweTransitionResult(String tenantId, String requestId,
        com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload, String apiAction) {
      final var revision = revisionService.changes(requestId, tenantId,
          ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueId = revision.getNewEntities().stream()
          .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
          .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
      assertThat(networkVenue.getOweTransWlanId()).isNotNull();
      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                  .as("The ADD NetworkVenue operation count should be 2")
                  .hasSize(2)));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.ADD_NETWORK_VENUE))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 2,
                  "The count of VenueSchedule operations should be 2")
              .satisfies(op -> assertSoftly(softly -> {
                softly.assertThat(payload).isNotNull();
                softly.assertThat(op)
                    .filteredOn(Operation::hasWlanVenue)
                    .describedAs("The count of WlanVenue operations should be 2")
                    .hasSize(2);
                softly.assertThat(op)
                    .filteredOn(Operation::hasWlanApGroup)
                    .hasSize(StrictRadioTypeEnum.values().length * 2)
                    .allMatch(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.ADD,
                        String.format(
                            "The value of `action` field in all WlanApGroup operations should be %s",
                            Action.ADD))
                    .extracting(Operation::getWlanApGroup)
                    .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                      if (BooleanUtils.isFalse(payload.getIsAllApGroups())) {
                        return;
                      }
                      // isAllApGroups != false
                      if (StringUtils.isEmpty(payload.getVlanPoolId())) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                        if (payload.getAllApGroupsVlanId() != null) {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                          alsoSoftly.assertThat(wlanApGroup.getVlanId())
                              .extracting(Int32Value::getValue)
                              .isEqualTo(payload.getAllApGroupsVlanId().intValue());
                        } else {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                        }
                        return;
                      }
                      alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                      alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                          .extracting(StringValue::getValue)
                          .isEqualTo(payload.getVlanPoolId());
                    }))
                    .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                      if (BooleanUtils.isNotFalse(payload.getIsAllApGroups())
                          || CollectionUtils.isEmpty(payload.getApGroups())) {
                        return;
                      }
                      // isAllApGroups == false
                      final var networkApGroupReq = payload.getApGroups().get(0);
                      if (StringUtils.isEmpty(networkApGroupReq.getVlanPoolId())) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                        if (networkApGroupReq.getVlanId() != null) {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                          alsoSoftly.assertThat(wlanApGroup.getVlanId())
                              .extracting(Int32Value::getValue)
                              .isEqualTo(networkApGroupReq.getVlanId().intValue());
                        } else {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                        }
                        return;
                      }
                      alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                      alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                          .extracting(StringValue::getValue)
                          .isEqualTo(networkApGroupReq.getVlanPoolId());
                    }));
              })));
    }

  }

  @Nested
  class GivenOweTransitionGuestNetworkPersistedInDb {

    private String networkId;
    private String venueId;

    @BeforeEach
    void givenGuestNetworkAndDefaultApGroupPersistedInDb(Tenant tenant, Venue venue,
        @DefaultApGroup ApGroup apGroup, Ap ap) {
      var masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      var master = (com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork) com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class)
          .generate();
      var slave = (com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork) com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class)
          .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      master.setIsOweMaster(true);
      master.setOwePairNetworkId(slave.getId());
      master.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.WISPr)
              .setWisprPage(com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                  .wisprPage().setCustomExternalProvider(always(false)))
              .generate());
      master.getGuestPortal().setNetwork(master);
      master = repositoryUtil.createOrUpdate(master, master.getTenant().getId(), randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      slave.setOwePairNetworkId(master.getId());
      slave.setIsOweMaster(false);
      slave.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.WISPr)
              .setWisprPage(com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                  .wisprPage().setCustomExternalProvider(always(false)))
              .generate());
      slave.getGuestPortal().setNetwork(slave);
      repositoryUtil.createOrUpdate(slave, slave.getTenant().getId(), randomTxId());

      networkId = master.getId();
      venueId = venue.getId();
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK_VENUE)
    @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateOweTransitionResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE.key());
    }

    void validateOweTransitionResult(String tenantId, String requestId,
        com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload, String apiAction) {
      final var revision = revisionService.changes(requestId, tenantId,
          ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueId = revision.getNewEntities().stream()
          .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
          .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
      assertThat(networkVenue.getOweTransWlanId()).isNotNull();
      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                  .as("The ADD NetworkVenue operation count should be 2")
                  .hasSize(2)));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.ADD_NETWORK_VENUE))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 2,
                  "The count of VenueSchedule operations should be 2")
              .satisfies(op -> assertSoftly(softly -> {
                softly.assertThat(payload).isNotNull();
                softly.assertThat(op)
                    .filteredOn(Operation::hasWlanVenue)
                    .describedAs("The count of WlanVenue operations should be 2")
                    .hasSize(2);
                softly.assertThat(op)
                    .filteredOn(Operation::hasWlanApGroup)
                    .hasSize(StrictRadioTypeEnum.values().length * 2)
                    .allMatch(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.ADD,
                        String.format(
                            "The value of `action` field in all WlanApGroup operations should be %s",
                            Action.ADD))
                    .extracting(Operation::getWlanApGroup)
                    .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                      if (BooleanUtils.isFalse(payload.getIsAllApGroups())) {
                        return;
                      }
                      // isAllApGroups != false
                      if (StringUtils.isEmpty(payload.getVlanPoolId())) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                        if (payload.getAllApGroupsVlanId() != null) {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                          alsoSoftly.assertThat(wlanApGroup.getVlanId())
                              .extracting(Int32Value::getValue)
                              .isEqualTo(payload.getAllApGroupsVlanId().intValue());
                        } else {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                        }
                        return;
                      }
                      alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                      alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                          .extracting(StringValue::getValue)
                          .isEqualTo(payload.getVlanPoolId());
                    }))
                    .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                      if (BooleanUtils.isNotFalse(payload.getIsAllApGroups())
                          || CollectionUtils.isEmpty(payload.getApGroups())) {
                        return;
                      }
                      // isAllApGroups == false
                      final var networkApGroupReq = payload.getApGroups().get(0);
                      if (StringUtils.isEmpty(networkApGroupReq.getVlanPoolId())) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                        if (networkApGroupReq.getVlanId() != null) {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                          alsoSoftly.assertThat(wlanApGroup.getVlanId())
                              .extracting(Int32Value::getValue)
                              .isEqualTo(networkApGroupReq.getVlanId().intValue());
                        } else {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                        }
                        return;
                      }
                      alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                      alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                          .extracting(StringValue::getValue)
                          .isEqualTo(networkApGroupReq.getVlanPoolId());
                    }));
              })));
    }
  }

  @Nested
  class GivenGuestNetworkPersistedInDb {

    private String networkId;
    private String venueId;

    @BeforeEach
    void givenGuestNetworkAndDefaultApGroupPersistedInDb(
        @GuestNetwork com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork network, Venue venue,
        @DefaultApGroup ApGroup apGroup) {
      networkId = network.getId();
      venueId = venue.getId();
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE.key());
    }
  }

  void validateResult(String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload, String apiAction) {
    final var revision = revisionService.changes(requestId, tenantId,
        ConfigRequestHandler.apiActionToFlowName(apiAction));
    final var networkVenueId = revision.getNewEntities().stream()
        .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
        .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
    final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);

    assertThat(networkVenue)
        .isNotNull()
        .matches(nv -> Objects.equals(nv.getNetwork().getId(), payload.getNetworkId()))
        .matches(nv -> Objects.equals(nv.getVenue().getId(), payload.getVenueId()));

    if (NetworkTypeEnum.GUEST.equals(networkVenue.getNetwork().getType())) {
      assertThat(networkVenue.getVenuePortal())
          .isNotNull();
    }

    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(wifiCfgChangeMessage).isNotNull()
        .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(wifiCfgChangeMessage.getPayload())
            .satisfies(msg -> assertSoftly(softly -> {
              softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
              softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
            }))
            .extracting(WifiConfigChange::getOperationList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
            .satisfies(ops -> assertThat(ops)
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .filteredOn(
                    op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                .as("The ADD NetworkVenue operation count should be 1")
                .hasSize(1)
                .singleElement()
                .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                .satisfies(
                    assertNetworkVenueSoftly(payload.getNetworkId(), payload.getVenueId()))));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
            .hasSize(1)
            .singleElement()
            .matches(op -> op.getOpType() == OpType.ADD)
            .matches(op -> networkVenueId.equals(op.getId()))
            .extracting(Operations::getDocMap)
            .matches(doc -> networkVenueId.equals(doc.get(Key.ID).getStringValue()))
            .matches(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
            .matches(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
            .matches(doc -> payload.getNetworkId().equals(doc.get(Key.NETWORK_ID).getStringValue()))
            .matches(doc -> payload.getVenueId().equals(doc.get(Key.VENUE_ID).getStringValue()))
            .matches(doc -> doc.get(Key.VENUE_NAME).getStringValue() != null));

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiAction))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                    String.format("The value of `requestId` field in CommonInfo should be [%s]",
                        requestId))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                    String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                        tenantId))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                    String.format("The value of `sender` field in CommonInfo should be [%s]",
                        ServiceType.WIFI_SERVICE)))
            .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count() == 1,
                "The count of VenueSchedule operations should be 1")
            .satisfies(op -> assertSoftly(softly -> {
              softly.assertThat(payload).isNotNull();
              softly.assertThat(op)
                  .filteredOn(Operation::hasWlanVenue)
                  .describedAs("The count of WlanVenue operations should be 1")
                  .hasSize(1)
                  .singleElement()
                  .matches(wlanVenueOp -> wlanVenueOp.getAction() == Action.ADD,
                      String.format(
                          "The value of `action` field in WlanVenue operation should be %s",
                          Action.ADD))
                  .matches(wlanVenueOp -> networkVenueId.equals(wlanVenueOp.getId()),
                      String.format("The value of `id` field in WlanVenue operation should be %s",
                          networkVenueId))
                  .extracting(Operation::getWlanVenue)
                  .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                    alsoSoftly.assertThat(wlanVenue.getWlanId())
                        .isEqualTo(payload.getNetworkId());
                    alsoSoftly.assertThat(wlanVenue.getVenueId())
                        .isEqualTo(payload.getVenueId());
                  }));
              int expectedRadioTypeSize = 0;
              if (networkVenue.getIsAllApGroups()) {
                if (networkVenue.getAllApGroupsRadioTypes().contains(RadioTypeEnum._2_4_GHz)) {
                  expectedRadioTypeSize++;
                }
                if (networkVenue.getAllApGroupsRadioTypes().contains(RadioTypeEnum._5_GHz)) {
                  expectedRadioTypeSize += 3;
                }
                if (networkVenue.getAllApGroupsRadioTypes().contains(RadioTypeEnum._6_GHz)) {
                  expectedRadioTypeSize++;
                }
              } else {
                expectedRadioTypeSize = StrictRadioTypeEnum.values().length;
              }

              softly.assertThat(op)
                  .filteredOn(Operation::hasWlanApGroup)
                  .describedAs("The count of WlanApGroup operations should be %d",
                      expectedRadioTypeSize)
                  .hasSize(expectedRadioTypeSize)
                  .allMatch(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.ADD,
                      String.format(
                          "The value of `action` field in all WlanApGroup operations should be %s",
                          Action.ADD))
                  .extracting(Operation::getWlanApGroup)
                  .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                    if (BooleanUtils.isFalse(payload.getIsAllApGroups())) {
                      return;
                    }
                    // isAllApGroups != false
                    if (StringUtils.isEmpty(payload.getVlanPoolId())) {
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                      if (payload.getAllApGroupsVlanId() != null) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanId())
                            .extracting(Int32Value::getValue)
                            .isEqualTo(payload.getAllApGroupsVlanId().intValue());
                      } else {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      }
                      return;
                    }
                    alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                    alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                    alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                        .extracting(StringValue::getValue)
                        .isEqualTo(payload.getVlanPoolId());
                  }))
                  .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                    if (BooleanUtils.isNotFalse(payload.getIsAllApGroups())
                        || CollectionUtils.isEmpty(payload.getApGroups())) {
                      return;
                    }
                    // isAllApGroups == false
                    final var networkApGroupReq = payload.getApGroups().get(0);
                    if (StringUtils.isEmpty(networkApGroupReq.getVlanPoolId())) {
                      alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                      if (networkApGroupReq.getVlanId() != null) {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanId())
                            .extracting(Int32Value::getValue)
                            .isEqualTo(networkApGroupReq.getVlanId().intValue());
                      } else {
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                      }
                      return;
                    }
                    alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                    alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                    alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                        .extracting(StringValue::getValue)
                        .isEqualTo(networkApGroupReq.getVlanPoolId());
                  }));
            })));
  }

  private static com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler dummyNetworkVenueScheduler() {
    final var scheduler = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler();
    scheduler.setType(SchedulerTypeEnum.CUSTOM);
    scheduler.setMon(randomTimeSlot());
    scheduler.setTue(randomTimeSlot());
    scheduler.setWed(randomTimeSlot());
    scheduler.setThu(randomTimeSlot());
    scheduler.setFri(randomTimeSlot());
    scheduler.setSat(randomTimeSlot());
    scheduler.setSun(randomTimeSlot());
    return scheduler;
  }

}
