package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("SoftGreProfileTest")
@WifiJpaDataTest
@Sql(
    statements =
        """
            -- base data
            INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
            INSERT INTO venue (id, tenant) VALUES ('78c2e1ccebd745be839198cddf686a86', '4c8279f79307415fa9e4c88a1819f0fc');
            INSERT INTO soft_gre_profile (id, name, tenant, mtu_type, mtu_size, primary_gateway, secondary_gateway, description, disassociate_client_enabled, keep_alive_interval, keep_alive_retry_times)
                VALUES ('afc284d992694d5c9d7a2fcf2289a0bd', 'SoftGRE Profile 1', '4c8279f79307415fa9e4c88a1819f0fc', 'MANUAL', 1450, '*******', '*******', 'Test SoftGRE Profile', false, 10, 5),
                       ('afc284d992694d5c9d7a2fcf2289a0be', 'SoftGRE Profile 2', '4c8279f79307415fa9e4c88a1819f0fc', 'MANUAL', 1450, '*******', '*******', 'Test SoftGRE Profile', false, 10, 5),
                       ('afc284d992694d5c9d7a2fcf2289a0bf', 'SoftGRE Profile 3', '4c8279f79307415fa9e4c88a1819f0fc', 'MANUAL', 1450, '*******', '*******', 'Test SoftGRE Profile', false, 10, 5);
            -- network-venue activations
            INSERT INTO network (id, name, type, tenant)
                VALUES ('c9845a491cbc43d596ffcf3b5fca8c4f', 'Network 1', 'OPEN', '4c8279f79307415fa9e4c88a1819f0fc'),
                       ('c9845a491cbc43d596ffcf3b5fca8c4g', 'Network 2', 'OPEN', '4c8279f79307415fa9e4c88a1819f0fc'),
                       ('c9845a491cbc43d596ffcf3b5fca8c4h', 'Network 3', 'OPEN', '4c8279f79307415fa9e4c88a1819f0fc'),
                       ('c9845a491cbc43d596ffcf3b5fca8c4i', 'Network 4', 'OPEN', '4c8279f79307415fa9e4c88a1819f0fc');
            INSERT INTO network_venue (id, network, venue, tenant)
                VALUES ('d20a29ee20eb4ce5b4eb507cb35fa972', 'c9845a491cbc43d596ffcf3b5fca8c4f', '78c2e1ccebd745be839198cddf686a86', '4c8279f79307415fa9e4c88a1819f0fc'),
                       ('d20a29ee20eb4ce5b4eb507cb35fa973', 'c9845a491cbc43d596ffcf3b5fca8c4g', '78c2e1ccebd745be839198cddf686a86', '4c8279f79307415fa9e4c88a1819f0fc'),
                       ('d20a29ee20eb4ce5b4eb507cb35fa974', 'c9845a491cbc43d596ffcf3b5fca8c4h', '78c2e1ccebd745be839198cddf686a86', '4c8279f79307415fa9e4c88a1819f0fc'),
                       ('d20a29ee20eb4ce5b4eb507cb35fa975', 'c9845a491cbc43d596ffcf3b5fca8c4i', '78c2e1ccebd745be839198cddf686a86', '4c8279f79307415fa9e4c88a1819f0fc');
            INSERT INTO soft_gre_profile_network_venue_activation (id, tenant, network_venue, soft_gre_profile)
                VALUES ('d20a29ee20eb4ce5b4eb507cb35fa979', '4c8279f79307415fa9e4c88a1819f0fc', 'd20a29ee20eb4ce5b4eb507cb35fa972', 'afc284d992694d5c9d7a2fcf2289a0bd'),
                       ('d20a29ee20eb4ce5b4eb507cb35fa980', '4c8279f79307415fa9e4c88a1819f0fc', 'd20a29ee20eb4ce5b4eb507cb35fa973', 'afc284d992694d5c9d7a2fcf2289a0be'),
                       ('d20a29ee20eb4ce5b4eb507cb35fa981', '4c8279f79307415fa9e4c88a1819f0fc', 'd20a29ee20eb4ce5b4eb507cb35fa974', 'afc284d992694d5c9d7a2fcf2289a0bf'),
                       ('d20a29ee20eb4ce5b4eb507cb35fa982', '4c8279f79307415fa9e4c88a1819f0fc', 'd20a29ee20eb4ce5b4eb507cb35fa975', 'afc284d992694d5c9d7a2fcf2289a0bf');
            -- lan-port-adoption and soft-gre activation
            INSERT INTO ap_lan_port_profile (id, tenant, category)
                VALUES ('04b9de2f3f174f95a6bd432d9df79113', '4c8279f79307415fa9e4c88a1819f0fc', 'ETHERNET'),
                       ('04b9de2f3f174f95a6bd432d9df79114', '4c8279f79307415fa9e4c88a1819f0fc', 'ETHERNET');
            INSERT INTO lan_port_adoption (id, tenant, ap_lan_port_profile, checksum)
                VALUES ('f11920c90d7444a596b9a499008007b6', '4c8279f79307415fa9e4c88a1819f0fc', '04b9de2f3f174f95a6bd432d9df79113', 'TEST-CHECKSUM-1'),
                       ('03e17c01cfd446cf93efd1cf6dfe22f1', '4c8279f79307415fa9e4c88a1819f0fc', '04b9de2f3f174f95a6bd432d9df79113', 'TEST-CHECKSUM-2'),
                       ('2fd0160d42fa43b085f88db893532c8c', '4c8279f79307415fa9e4c88a1819f0fc', '04b9de2f3f174f95a6bd432d9df79113', 'TEST-CHECKSUM-3'),
                       ('5fbc1cae48fe4e1ba909b7372e90aec7', '4c8279f79307415fa9e4c88a1819f0fc', '04b9de2f3f174f95a6bd432d9df79114', 'TEST-CHECKSUM-4');
            INSERT INTO soft_gre_profile_lan_port_activation (id, tenant, lan_port_adoption, soft_gre_profile)
                VALUES ('404fb76fd8f04e2c9bbccfa35e8f3efe', '4c8279f79307415fa9e4c88a1819f0fc', 'f11920c90d7444a596b9a499008007b6', 'afc284d992694d5c9d7a2fcf2289a0bd'),
                       ('b5f1e64ea06b4170831a09f7530eaac4', '4c8279f79307415fa9e4c88a1819f0fc', '03e17c01cfd446cf93efd1cf6dfe22f1', 'afc284d992694d5c9d7a2fcf2289a0be'),
                       ('cbeb8916e41e469c9bfaec93d0ddf7f3', '4c8279f79307415fa9e4c88a1819f0fc', '2fd0160d42fa43b085f88db893532c8c', 'afc284d992694d5c9d7a2fcf2289a0bf');
            -- venue-lan-port
            INSERT INTO venue_ap_model_specific_attributes (id, model, tenant, venue)
                VALUES ('9f4ba5e6690d43a4a4fd8e69d3e6556e', 'R720', '4c8279f79307415fa9e4c88a1819f0fc', '78c2e1ccebd745be839198cddf686a86'),
                       ('06c6017ebbce4d93947a3f85959d450d', 'R730', '4c8279f79307415fa9e4c88a1819f0fc', '78c2e1ccebd745be839198cddf686a86');
            INSERT INTO venue_lan_port (id, tenant, ap_lan_port_profile, venue_ap_model_specific_attributes, lan_port_adoption)
                VALUES ('31f9e514cc904b298b2082e682bc1b82', '4c8279f79307415fa9e4c88a1819f0fc', '04b9de2f3f174f95a6bd432d9df79113', '9f4ba5e6690d43a4a4fd8e69d3e6556e', 'f11920c90d7444a596b9a499008007b6'),
                       ('246012ca780f43e083e9357715ed1582', '4c8279f79307415fa9e4c88a1819f0fc', '04b9de2f3f174f95a6bd432d9df79113', '9f4ba5e6690d43a4a4fd8e69d3e6556e', '03e17c01cfd446cf93efd1cf6dfe22f1'),
                       ('a0bbed8ed73f42118f8775f25ad8e458', '4c8279f79307415fa9e4c88a1819f0fc', '04b9de2f3f174f95a6bd432d9df79113', '06c6017ebbce4d93947a3f85959d450d', '2fd0160d42fa43b085f88db893532c8c');
            -- ap-lan-port
            INSERT INTO ap_group (id, tenant, venue)
                VALUES ('9f88e013eee440c0b9eec3813ef90766', '4c8279f79307415fa9e4c88a1819f0fc', '78c2e1ccebd745be839198cddf686a86');
            INSERT INTO ap_model_specific (id, tenant)
                VALUES ('a373aa0c325f4eec9df989e3473a44c3', '4c8279f79307415fa9e4c88a1819f0fc'),
                       ('47f0229657a7449b90e918e54ccb7c83', '4c8279f79307415fa9e4c88a1819f0fc');
            INSERT INTO ap (id, tenant, ap_group, model_specific, soft_deleted)
                VALUES ('963736948005', '4c8279f79307415fa9e4c88a1819f0fc', '9f88e013eee440c0b9eec3813ef90766', 'a373aa0c325f4eec9df989e3473a44c3', false),
                       ('178550000012', '4c8279f79307415fa9e4c88a1819f0fc', '9f88e013eee440c0b9eec3813ef90766', '47f0229657a7449b90e918e54ccb7c83', false);
            INSERT INTO ap_lan_port (id, tenant, model_specific, ap_lan_port_profile, lan_port_adoption)
                VALUES ('190b16625118404cbf920c136f2e5ab5', '4c8279f79307415fa9e4c88a1819f0fc', 'a373aa0c325f4eec9df989e3473a44c3', '04b9de2f3f174f95a6bd432d9df79113', 'f11920c90d7444a596b9a499008007b6'),
                       ('a793bcc9e589491ba48065c2977b8960', '4c8279f79307415fa9e4c88a1819f0fc', 'a373aa0c325f4eec9df989e3473a44c3', '04b9de2f3f174f95a6bd432d9df79113', '03e17c01cfd446cf93efd1cf6dfe22f1'),
                       ('04a34f1f15e44ad1b2663aa99c0184c7', '4c8279f79307415fa9e4c88a1819f0fc', '47f0229657a7449b90e918e54ccb7c83', '04b9de2f3f174f95a6bd432d9df79113', '2fd0160d42fa43b085f88db893532c8c');
            """)
class SoftGreProfileRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String SOFTGRE_PROFILE_ID = "afc284d992694d5c9d7a2fcf2289a0bd";
  private static final String SOFTGRE_PROFILE_NAME = "SoftGRE Profile 1";
  private static final String VENUE_ID = "78c2e1ccebd745be839198cddf686a86";
  private static final String NETWORK_ID = "c9845a491cbc43d596ffcf3b5fca8c4f";
  private static final String LAN_PORT_ADOPTION_ID = "f11920c90d7444a596b9a499008007b6";

  @Autowired
  private SoftGreProfileRepository profileRepository;
  @Autowired
  private SoftGreProfileNetworkVenueActivationRepository activationRepository;
  @Autowired
  private ApLanPortRepository apLanPortRepository;
  @Autowired
  private ApRepository apRepository;
  @Autowired
  private ApModelSpecificRepository apModelSpecificRepository;
  @Autowired
  private ApGroupRepository apGroupRepository;
  @Autowired
  private LanPortAdoptionRepository lanPortAdoptionRepository;

  @Test
  void existsByTenantIdAndNameAndIdNotTest() {
    var result =
        profileRepository.existsByTenantIdAndNameAndIdNot(TENANT_ID, SOFTGRE_PROFILE_NAME, "12345");
    assertTrue(result);
    result =
        profileRepository.existsByTenantIdAndNameAndIdNot(
            TENANT_ID, SOFTGRE_PROFILE_NAME, SOFTGRE_PROFILE_ID);
    assertFalse(result);
    result =
        profileRepository.existsByTenantIdAndNameAndIdNot(
            TENANT_ID, "New SoftGRE Profile 1", SOFTGRE_PROFILE_ID);
    assertFalse(result);
  }

  @Test
  void findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId() {
    var result =
        activationRepository.findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueId(
            TENANT_ID, NETWORK_ID, VENUE_ID);
    assertTrue(result.isPresent());
  }

  @Test
  void findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileIdTest() {
    var result =
        activationRepository
            .findByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId(
                TENANT_ID, NETWORK_ID, VENUE_ID, SOFTGRE_PROFILE_ID);
    assertTrue(result.isPresent());
  }

  @Test
  void findByTenantIdAndSoftGreProfileIdTest() {
    var result =
        activationRepository.findByTenantIdAndSoftGreProfileId(TENANT_ID, SOFTGRE_PROFILE_ID);
    assertEquals(1, result.size());
  }

  @Test
  void existsByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileIdTest() {
    var result =
        activationRepository
            .existsByTenantIdAndNetworkVenueNetworkIdAndNetworkVenueVenueIdAndSoftGreProfileId(
                TENANT_ID, NETWORK_ID, VENUE_ID, SOFTGRE_PROFILE_ID);
    assertTrue(result);
  }

  @Test
  void findOtherActivationsInAllVenuesWhichActivatedTargetSoftGreProfileTest() {
    var result =
        activationRepository.findOtherActivationsInAllVenuesWhichActivatedTargetSoftGreProfile(
            TENANT_ID, SOFTGRE_PROFILE_ID);
    assertEquals(3, result.size());
    var activation = result.iterator().next();
    assertEquals("Network 2", activation.getNetworkVenue().getNetwork().getName());
    assertEquals("SoftGRE Profile 2", activation.getSoftGreProfile().getName());
  }

  @Test
  void findOtherActivationsTest() {
    var result = activationRepository.findOtherActivations(TENANT_ID, VENUE_ID, SOFTGRE_PROFILE_ID);
    assertEquals(3, result.size());
    result = activationRepository.findOtherActivations(TENANT_ID, VENUE_ID, "TEST");
    assertEquals(4, result.size());
  }

  @Test
  void findAllNetworkVenueActivatedSoftGreProfilesTest() {
    var result = profileRepository.findAllNetworkVenueActivatedSoftGreProfiles(TENANT_ID, VENUE_ID);
    assertEquals(3, result.size());
  }

  @Test
  void findAllVenueLanPortActivatedSoftGreProfilesTest() {
    var result = profileRepository.findAllVenueLanPortActivatedSoftGreProfiles(TENANT_ID, VENUE_ID);
    assertEquals(3, result.size());
  }

  @Test
  void findVenueLanPortActivatedSoftGreProfiles() {
    var result = profileRepository.findSoftGreProfilesActivatedOnVenueApModelLanPorts(TENANT_ID, List.of(VENUE_ID), "R720");
    assertEquals(2, result.size());
  }

  @Test
  void findAllApLanPortActivatedSoftGreProfilesTest() {
    var result = profileRepository.findAllApLanPortActivatedSoftGreProfiles(TENANT_ID, VENUE_ID);
    assertEquals(3, result.size());
  }

  @Test
  void findAllSoftGreProfilesTest() {
    var result =
        Stream.of(
                profileRepository.findAllNetworkVenueActivatedSoftGreProfiles(TENANT_ID, VENUE_ID),
                profileRepository.findAllVenueLanPortActivatedSoftGreProfiles(TENANT_ID, VENUE_ID),
                profileRepository.findAllApLanPortActivatedSoftGreProfiles(TENANT_ID, VENUE_ID))
            .flatMap(profiles -> profiles.stream())
            .collect(Collectors.toSet());
    assertEquals(3, result.size());
  }

  @Test
  void findAllSoftGreProfileIdsTest() {
    var result = profileRepository.findAllActivatedSoftGreProfileIdsByTenantIdAndVenueId(TENANT_ID, VENUE_ID);
    assertEquals(3, result.size());
  }

  @Test
  void findByTenantIdAndSoftGreProfileIdForLanPortAdoptionTest() {
    var result =
        lanPortAdoptionRepository.findLanPortAdoptionIdsByTenantAndSoftGreProfile(TENANT_ID, SOFTGRE_PROFILE_ID);
    assertEquals(1, result.size());
    assertEquals(result.get(0), LAN_PORT_ADOPTION_ID);
  }

  @Test
  void findByIdInAndTenantIdTest() {
    var result = profileRepository.findByIdInAndTenantId(
        List.of("afc284d992694d5c9d7a2fcf2289a0bd", "afc284d992694d5c9d7a2fcf2289a0be",
            "afc284d992694d5c9d7a2fcf2289a0bf"), TENANT_ID);
    assertEquals(3, result.size());
  }

  @Test
  void findAllVenueLanPortActivatedSoftGreProfilesByVenueIds() {
    var result = profileRepository.findAllLanPortsActivatedSoftGreProfilesByVenueIdsOrApSerials(
        TENANT_ID, List.of(VENUE_ID), List.of("963736948005"));
    assertEquals(3, result.size());
  }

  @Test
  void countByTenantId() {
    var result = profileRepository.countByTenantId(TENANT_ID);
    assertEquals(3, result);
  }

  @Test
  void findUnusedLanPortAdoptionsByTenantId() {
    var result = lanPortAdoptionRepository.findUnusedLanPortAdoptionsByTenantId(TENANT_ID);
    assertEquals(1, result.size());
  }
}
