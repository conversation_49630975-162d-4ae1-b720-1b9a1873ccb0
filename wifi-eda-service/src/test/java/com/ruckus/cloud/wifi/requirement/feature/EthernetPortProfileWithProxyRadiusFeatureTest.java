package com.ruckus.cloud.wifi.requirement.feature;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.EthernetPortProfileRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Tag("EthernetPortProfileWithProxyRadiusFeatureTest")
@WifiUnitTest
@FeatureFlag(enable = FlagNames.ACX_ETHERNET_PORT_SUPPORT_PROXY_RADIUS)
public class EthernetPortProfileWithProxyRadiusFeatureTest {

  @MockBean
  private EthernetPortProfileRepository ethernetPortProfileRepository;
  @MockBean
  private VenueLanPortRepository venueLanPortRepository;
  @MockBean
  private ApLanPortRepository apLanPortRepository;
  @SpyBean
  private EthernetPortProfileWithProxyRadiusFeature unit;

  @Test
  @FeatureFlag(disable = FlagNames.ACX_ETHERNET_PORT_SUPPORT_PROXY_RADIUS)
  void givenVenueTestFFDisabled(Venue venue) {
    BDDAssertions.then(unit.test(venue)).isFalse();
  }

  @Test
  @FeatureFlag(disable = FlagNames.ACX_ETHERNET_PORT_SUPPORT_PROXY_RADIUS)
  void givenApTestFFDisabled(Ap ap) {
    BDDAssertions.then(unit.test(ap)).isFalse();
  }

  @Test
  void givenVenueProxyNotExist(Venue venue) {
    when(venueLanPortRepository.findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAuthProxyTrue(
        eq(venue.getTenant().getId()), eq(venue.getId())))
        .thenReturn(new ArrayList<>());

    when(venueLanPortRepository.findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAccountingProxyTrue(
        eq(venue.getTenant().getId()), eq(venue.getId())))
        .thenReturn(new ArrayList<>());

    BDDAssertions.then(unit.test(venue)).isFalse();
  }

  @Test
  void givenVenueAuthProxyExist(Venue venue) {
    VenueLanPort venueLanPort = new VenueLanPort();
    when(venueLanPortRepository.findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAuthProxyTrue(
        eq(venue.getTenant().getId()), eq(venue.getId())))
        .thenReturn(List.of(venueLanPort));

    BDDAssertions.then(unit.test(venue)).isTrue();
  }

  @Test
  void givenVenueAcctProxyExist(Venue venue) {
    VenueLanPort venueLanPort = new VenueLanPort();
    when(venueLanPortRepository.findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAccountingProxyTrue(
        eq(venue.getTenant().getId()), eq(venue.getId())))
        .thenReturn(List.of(venueLanPort));

    BDDAssertions.then(unit.test(venue)).isTrue();
  }

  @Test
  void givenVenueAuthProxyAcctProxyExist(Venue venue) {
    VenueLanPort venueLanPort = new VenueLanPort();
    when(venueLanPortRepository.findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAuthProxyTrue(
        eq(venue.getTenant().getId()), eq(venue.getId())))
        .thenReturn(List.of(venueLanPort));
    when(venueLanPortRepository.findByTenantIdAndVenueApModelSpecificAttributesVenueIdAndApLanPortProfileEnableAccountingProxyTrue(
        eq(venue.getTenant().getId()), eq(venue.getId())))
        .thenReturn(List.of(venueLanPort));

    BDDAssertions.then(unit.test(venue)).isTrue();
  }

  @Test
  void givenApLanPortNotExist(Ap ap) {
    when(apLanPortRepository.findApLanPortProfileIdsByTenantIdAndModelSpecificApId(
        eq(ap.getTenant().getId()), eq(ap.getId())))
        .thenReturn(new ArrayList<>());

    BDDAssertions.then(unit.test(ap)).isFalse();
  }

  @Test
  void givenApAuthProxyExist(Ap ap) {
    String apLanPortProfileId = "apLanPortProfileId";
    when(apLanPortRepository.findApLanPortProfileIdsByTenantIdAndModelSpecificApId(
        eq(ap.getTenant().getId()), eq(ap.getId())))
        .thenReturn(List.of(apLanPortProfileId));

    when(ethernetPortProfileRepository.countByTenantIdAndEnableAuthProxyTrueAndIdIn(
        eq(ap.getTenant().getId()), eq(List.of(apLanPortProfileId))))
        .thenReturn(1L);

    BDDAssertions.then(unit.test(ap)).isTrue();
  }

  @Test
  void givenApAcctProxyExist(Ap ap) {
    String apLanPortProfileId = "apLanPortProfileId";
    when(apLanPortRepository.findApLanPortProfileIdsByTenantIdAndModelSpecificApId(
        eq(ap.getTenant().getId()), eq(ap.getId())))
        .thenReturn(List.of(apLanPortProfileId));

    when(ethernetPortProfileRepository.countByTenantIdAndEnableAccountingProxyTrueAndIdIn(
        eq(ap.getTenant().getId()), eq(List.of(apLanPortProfileId))))
        .thenReturn(1L);

    BDDAssertions.then(unit.test(ap)).isTrue();
  }

  @Test
  void givenApAuthProxyAcctProxyExist(Ap ap) {
    String apLanPortProfileId = "apLanPortProfileId";
    when(apLanPortRepository.findApLanPortProfileIdsByTenantIdAndModelSpecificApId(
        eq(ap.getTenant().getId()), eq(ap.getId())))
        .thenReturn(List.of(apLanPortProfileId));

    when(ethernetPortProfileRepository.countByTenantIdAndEnableAuthProxyTrueAndIdIn(
        eq(ap.getTenant().getId()), eq(List.of(apLanPortProfileId))))
        .thenReturn(1L);

    when(ethernetPortProfileRepository.countByTenantIdAndEnableAccountingProxyTrueAndIdIn(
        eq(ap.getTenant().getId()), eq(List.of(apLanPortProfileId))))
        .thenReturn(1L);

    BDDAssertions.then(unit.test(ap)).isTrue();
  }
}
