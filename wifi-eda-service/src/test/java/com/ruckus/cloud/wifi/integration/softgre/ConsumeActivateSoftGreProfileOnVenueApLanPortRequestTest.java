package com.ruckus.cloud.wifi.integration.softgre;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.AP_SERIAL_NUMBER;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.PORT_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.VENUE_ID;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.SoftGreProfileTestFixture.randomSoftGreProfile;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice.ImpactDevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.LanPortSoftGreProfileSettingsGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.LanPortSoftGreProfileSettings;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("SoftGreProfileTest")
@WifiIntegrationTest
@FeatureFlag(
    enable = {
      WIFI_ETHERNET_SOFTGRE_TOGGLE,
      ACX_UI_ETHERNET_TOGGLE,
    })
class ConsumeActivateSoftGreProfileOnVenueApLanPortRequestTest {

  private record ApLanPortData(
      ApModelSpecific apAttributes, ApLanPort port, EthernetPortProfile profile) {}

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;
  @Autowired private LanPortAdoptionDataHelper dataHelper;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class ConsumeActivateSoftGreProfileToVenueApLanPortRequestTest {

    private String softGreProfileId;
    private String serialNumber;
    private String venueId;
    private final String portId = "2";
    private String apLanPortId;

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        Generators.lanPortSoftGreProfileSettings();

    @BeforeEach
    void givenOnePersistedInDb(
        final Tenant tenant, final ApGroup apGroup, final Venue venue, final Ap ap) {
      var apLanPortData = dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 3);
      serialNumber = ap.getId();
      venueId = venue.getId();
      apLanPortId = apLanPortData.port().getId();
      var softGreProfile = dataHelper.createSoftGreProfile(tenant);
      softGreProfileId = softGreProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {
      validateActivateSoftGreProfileToVenueApModelLanPortResult(
          CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
          venueId,
          softGreProfileId,
          payload,
          apLanPortId,
          serialNumber,
          portId,
          false);
    }
  }

  @Nested
  class ConsumeActivateSoftGreProfileWithExistActivationAndChangeDhcpSettingTest {

    private String softGreProfileId;
    private String serialNumber;
    private String venueId;
    private String portId = "2";
    private String apLanPortId;

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        Generators.lanPortSoftGreProfileSettings();

    @BeforeEach
    void givenOnePersistedInDb(
        final Tenant tenant, final ApGroup apGroup, final Venue venue, final Ap ap) {
      var softGreProfile = dataHelper.createSoftGreProfile(tenant);

      var apLanPortData =
          dataHelper.createApLanPortDataWithAdoption(
              venue, ap, portId, 3, of(getSoftGreLanPortActivation(softGreProfile)));
      serialNumber = ap.getId();
      venueId = venue.getId();
      apLanPortId = apLanPortData.port().getId();
      softGreProfileId = softGreProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {
      validateActivateSoftGreProfileToVenueApModelLanPortResult(
          CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
          venueId,
          softGreProfileId,
          payload,
          apLanPortId,
          serialNumber,
          portId,
          false);
    }
  }

  @Nested
  class ConsumeActivateSoftGreProfileWithExistActivationAndChangeSoftGreProfileTest {

    private String softGreProfileId;
    private String serialNumber;
    private String venueId;
    private String portId = "2";
    private String apLanPortId;

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        Generators.lanPortSoftGreProfileSettings();

    @BeforeEach
    void givenOnePersistedInDb(
        final Tenant tenant, final ApGroup apGroup, final Venue venue, final Ap ap) {
      var softGreProfile1 = dataHelper.createSoftGreProfile(tenant);
      var apLanPortData =
          dataHelper.createApLanPortDataWithAdoption(
              venue, ap, portId, 3, of(getSoftGreLanPortActivation(softGreProfile1)));
      serialNumber = ap.getId();
      venueId = venue.getId();
      apLanPortId = apLanPortData.port().getId();

      // save SoftGreProfile
      var softGreProfile2 =
          repositoryUtil.createOrUpdate(
              randomSoftGreProfile(tenant, p -> {}), tenant.getId(), randomTxId());
      softGreProfileId = softGreProfile2.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {
      validateActivateSoftGreProfileToVenueApModelLanPortResult(
          CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
          venueId,
          softGreProfileId,
          payload,
          apLanPortId,
          serialNumber,
          portId,
          true);
    }
  }

  @Nested
  class ConsumeActivateSoftGreProfileExceedMaxSoftGreProfileOnVenueCount {

    private String softGreProfileId;
    private String serialNumber;
    private String venueId;
    private String portId = "2";
    private String apLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Tenant tenant, final ApGroup apGroup, final Venue venue, final Ap ap) {
      var softGreProfile1 = dataHelper.createSoftGreProfile(tenant);
      dataHelper.createApLanPortDataWithAdoption(
          venue, ap, "3", 3, of(getSoftGreLanPortActivation(softGreProfile1)));

      var softGreProfile2 = dataHelper.createSoftGreProfile(tenant);
      dataHelper.createApLanPortDataWithAdoption(
          venue, ap, "4", 4, of(getSoftGreLanPortActivation(softGreProfile2)));

      var softGreProfile3 = dataHelper.createSoftGreProfile(tenant);
      dataHelper.createApLanPortDataWithAdoption(
          venue, ap, "5", 5, of(getSoftGreLanPortActivation(softGreProfile3)));

      var apLanPortData4 = dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 6);
      serialNumber = ap.getId();
      venueId = venue.getId();
      apLanPortId = apLanPortData4.port().getId();

      var softGreProfile4 = dataHelper.createSoftGreProfile(tenant);
      softGreProfileId = softGreProfile4.getId();
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {

      LanPortSoftGreProfileSettingsGenerator generator = Generators.lanPortSoftGreProfileSettings();
      RequestParams requestParams =
          new RequestParams()
              .addPathVariable("venueId", venueId)
              .addPathVariable("serialNumber", serialNumber)
              .addPathVariable("portId", portId)
              .addPathVariable("softGreProfileId", softGreProfileId);
      var payload = generator.generate();
      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      txCtxExtension.getTenantId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
                      "",
                      requestParams,
                      payload))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);
      final ApLanPort apLanPort = repositoryUtil.find(ApLanPort.class, apLanPortId);
      assertThat(apLanPort)
          .isNotNull()
          .extracting(ApLanPort::getLanPortAdoption)
          .isNotNull()
          .extracting(LanPortAdoption::getSoftGreActivation)
          .isNull();
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
    }
  }

  @Nested
  class ConsumeActivateSoftGreProfileOnApLanPortWithExistClientIsolationActivationTest {

    private String serialNumber;
    private String venueId;
    private String portId = "1";
    private String clientIsolationProfileId;
    private String softGreProfileId;
    private String apLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(Tenant tenant, final Venue venue, Ap ap) {
      venueId = venue.getId();
      serialNumber = ap.getId();

      var softGreProfile = dataHelper.createSoftGreProfile(tenant);
      softGreProfileId = softGreProfile.getId();
      var randomClientIsolation = dataHelper.createClientIsolationAllowlist(venue.getTenant());
      clientIsolationProfileId = randomClientIsolation.getId();

      var portData1 =
          dataHelper.createApLanPortDataWithAdoption(
              venue, ap, portId, 3, of(getClientIsolationLanPortActivation(randomClientIsolation)));
      apLanPortId = portData1.port().getId();
    }

    @Payload("lanPortSoftGreProfileSettings")
    private final LanPortSoftGreProfileSettingsGenerator generator =
        com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.lanPortSoftGreProfileSettings();

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("softGreProfileId", softGreProfileId);
    }

    @Test
    @ApiAction(
        value = CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
        payload = @Payload("lanPortSoftGreProfileSettings"))
    void thenShouldHandleTheRequestSuccessfully(
        @Payload("lanPortSoftGreProfileSettings") LanPortSoftGreProfileSettings payload) {
      validateActivateSoftGreProfileToVenueApModelLanPortResult(
          CfgAction.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT,
          venueId,
          softGreProfileId,
          payload,
          apLanPortId,
          serialNumber,
          portId,
          false);
    }
  }

  private void validateActivateSoftGreProfileToVenueApModelLanPortResult(
      CfgAction apiAction,
      String venueId,
      String softGreProfileId,
      LanPortSoftGreProfileSettings payload,
      String venueLanPortId,
      String serialNumber,
      String portId,
      boolean isReplaceProfile) {

    validateVenueRepositoryData(apiAction, venueId, softGreProfileId, payload, venueLanPortId);
    validateDdccmCfgRequestMessages(apiAction, softGreProfileId, venueLanPortId);
    validateCmnCfgCollectorMessages(
        apiAction, softGreProfileId, venueId, serialNumber, portId, isReplaceProfile);
    validateActivityMessages(apiAction);
  }

  private void validateVenueRepositoryData(
      CfgAction apiAction,
      String venueId,
      String softGreProfileId,
      LanPortSoftGreProfileSettings payload,
      String apLanPortId) {

    assertThat(venueId).isNotNull();

    switch (apiAction) {
      case ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT -> {
        final var allLanPortAdoption =
            repositoryUtil.findAll(LanPortAdoption.class, txCtxExtension.getTenantId());
        assertThat(allLanPortAdoption).isNotNull().matches(l -> l.size() == 1);
        final ApLanPort apLanPort = repositoryUtil.find(ApLanPort.class, apLanPortId);
        assertThat(apLanPort)
            .extracting(ApLanPort::getLanPortAdoption)
            .extracting(LanPortAdoption::getSoftGreActivation)
            .isNotNull()
            .matches(activation -> activation.getSoftGreProfile().getId().equals(softGreProfileId));
      }
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    }
  }

  private void validateDdccmCfgRequestMessages(
      CfgAction apiAction, String softGreProfileId, String apLanPortId) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();
    final ApLanPort apLanPort = repositoryUtil.find(ApLanPort.class, apLanPortId);

    var operations =
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList)
            .asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast);

    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .isNotEmpty()
        .allSatisfy(
            op -> {
              assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                  .matches(
                      venue ->
                          venue.getCcmMultipleTunnel().getSoftGreSettingList().stream()
                              .anyMatch(
                                  softgre -> softgre.getProfileId().equals(softGreProfileId)));
            });

    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp)
        .isNotEmpty()
        .allSatisfy(
            op -> {
              assertThat(op)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
                  .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
                  .isNotNull()
                  .matches(
                      model ->
                          model.getLanPortList().stream()
                              .anyMatch(
                                  l ->
                                      apLanPort.getLanPortAdoption().getEthernetPortProfileId()
                                          == l.getApLanPortProfileId()));
            });

    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApLanPortProfile)
        .filteredOn(operation -> operation.getAction() == Action.ADD)
        .isNotNull()
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .allSatisfy(op -> verifyAddAction(op, apLanPort, softGreProfileId));
  }

  private void verifyAddAction(
      com.ruckus.acx.ddccm.protobuf.wifi.Operation operation,
      ApLanPort apLanPort,
      String softGreProfileId) {
    assertThat(operation)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
        .matches(
            apLanPortProfile ->
                apLanPort.getLanPortAdoption().getId().equals(apLanPortProfile.getName()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getLanPortAdoption()
                    .getEthernetPortProfileId()
                    .equals(apLanPortProfile.getId()))
        .matches(
            apLanPortProfile ->
                (apLanPort.getApLanPortProfile().getType().name() + "PORT")
                    .equals(apLanPortProfile.getLanPortType().name()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagId()))
        .matches(
            apLanPortProfile ->
                softGreProfileId.equals(apLanPortProfile.getTunnelProfile().getProfileId()));
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors
          .assertThat(
              kafkaTopicProvider.getActivityCfgChangeResp(),
              kafkaTopicProvider.getActivityImpacted())
          .doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(ConfigurationStatus::getEventDate)
                    .isNotNull());

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(ImpactDevice::getDeviceIdsCount).isEqualTo(1);
  }

  private void validateCmnCfgCollectorMessages(
      CfgAction apiAction,
      String softGreProfileId,
      String venueId,
      String serialNumber,
      String portId,
      boolean isReplaceProfile) {
    if (apiAction == null || softGreProfileId == null) {
      messageCaptors
          .getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    var operations =
        cmnCfgCollectorMessage.getPayload().getOperationsList().stream()
            .filter(
                op ->
                    Index.SOFT_GRE_PROFILE_INDEX_NAME.equals(op.getIndex())
                        && op.getOpType() == OpType.MOD)
            .collect(Collectors.partitioningBy(op -> softGreProfileId.equals(op.getId())));

    assertThat(operations.get(true))
        .isNotEmpty()
        .singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(
            docMap -> {
              assertThat(docMap.get(Key.AP_ACTIVATIONS).getListValue().getValuesList())
                  .isNotEmpty()
                  .singleElement()
                  .extracting(Value::getStructValue)
                  .matches(c -> venueId.equals(c.getFieldsMap().get(VENUE_ID).getStringValue()))
                  .matches(
                      c ->
                          serialNumber.equals(
                              c.getFieldsMap().get(AP_SERIAL_NUMBER).getStringValue()))
                  .matches(c -> portId.equals(c.getFieldsMap().get(PORT_ID).getStringValue()));
            });
    var removeActivateOps = operations.get(false);
    if (isReplaceProfile) {
      assertThat(removeActivateOps)
          .singleElement()
          .extracting(Operations::getDocMap)
          .satisfies(
              docMap -> {
                assertThat(docMap.get(Key.VENUE_ACTIVATIONS).getListValue().getValuesList())
                    .isEmpty();
                assertThat(docMap.get(Key.AP_ACTIVATIONS).getListValue().getValuesList()).isEmpty();
              });
    }
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT ->
          ApiFlowNames.ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ACTIVATE_SOFT_GRE_PROFILE_ON_VENUE_AP_LAN_PORT -> Action.ADD;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private ClientIsolationLanPortActivation getClientIsolationLanPortActivation(
      ClientIsolationAllowlist clientIsolationProfile) {
    ClientIsolationLanPortActivation activation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
            .clientIsolationLanPortActivation()
            .generate();
    activation.setClientIsolationAllowlist(clientIsolationProfile);
    return activation;
  }

  private SoftGreProfileLanPortActivation getSoftGreLanPortActivation(
      SoftGreProfile softGreProfile) {
    SoftGreProfileLanPortActivation activation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
            .softGreProfileLanPortActivation()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(
                always(
                    com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                        .dhcpOption82Settings()
                        .generate()))
            .generate();
    activation.setSoftGreProfile(softGreProfile);
    return activation;
  }
}
