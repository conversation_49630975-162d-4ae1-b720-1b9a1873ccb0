package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.groups.Tuple.tuple;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.viewmodel.PresetApModelVersionMappingRequest;
import java.util.List;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
    FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
public class ConsumePresetVersionMappingForUnjoinedApModelsRequestTest extends AbstractRequestTest {
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired
  private MessageCaptors messageCaptors;

  @Test
  public void testPreset(Tenant tenant,
      Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.56") ApVersion ver700,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.55") ApVersion ver624) {
    ver700.setSupportedApModels(List.of("R550", "R560", "R650", "R670", "R770"));
    ver624.setSupportedApModels(List.of("R550", "R560", "R650"));
    repositoryUtil.createOrUpdate(ver700, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(ver624, tenant.getId(), randomTxId());
    ApGroup apGroupA = this.createApGroup(venue, "apGroupA");
    this.createAp(apGroupA, randomSerialNumber(), "R770", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R770", randomMacAddress());
    this.createAp(apGroupA, randomSerialNumber(), "R650", randomMacAddress());
    repositoryUtil.createOrUpdate(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, ver700, "R770"),
        tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, ver700, "R650"),
        tenant.getId(), randomTxId());

    PresetApModelVersionMappingRequest request = new PresetApModelVersionMappingRequest();
    request.setApVersion(ver624.getId());
    request.setTenantId(tenant.getId());
    request.setVenueId(venue.getId());

    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        randomTxId(),
        CfgExtendedAction.PRESET_VERSION_MAPPING_FOR_UNJOINED_AP_MODELS,
        randomName(),
        new RequestParams(),
        request);

    final var cmnCfgCollectorMsg = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId());
    validateVenueCmnCfgCollectorMsg(cmnCfgCollectorMsg, venue);

   assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenant.getId()))
       .isNotEmpty()
       .filteredOn(vcf -> vcf.getVenue().getId().equals(venue.getId()))
       .extracting(VenueCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
       .containsExactlyInAnyOrder(
           tuple("R770", ver700.getId()),
           tuple("R650", ver700.getId()),
           tuple("R550", ver624.getId()),
           tuple("R560", ver624.getId()));

    assertThat(repositoryUtil.find(Venue.class, venue.getId(), tenant.getId()))
        .isNotNull()
        .extracting(Venue::getWifiFirmwareVersion)
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(ver624.getId());
  }

  private void validateVenueCmnCfgCollectorMsg(KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage,
      Venue venue) {
    Tuple[] vcfTuples = repositoryUtil.findAll(VenueCurrentFirmware.class, venue.getTenant().getId())
        .stream()
        .filter(vcf -> vcf.getVenue().getId().equals(venue.getId()))
        .map(vcf -> new Tuple(vcf.getApModel(), vcf.getFirmware().getId()))
        .toArray(Tuple[]::new);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .extracting(ViewmodelCollector::getOperationsList).asList()
        .isNotEmpty()
        .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
        .filteredOn(op -> Index.VENUE.equals(op.getIndex()) && op.getOpType() == OpType.MOD)
        .filteredOn(
            op -> venue.getId().equals(op.getId()) && op.getDocMap().containsKey(EsConstants.Key.CURRENT_AP_FIRMWARES))
        .isNotEmpty().singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(docMap -> {
          assertThat(docMap.get(EsConstants.Key.CURRENT_AP_FIRMWARES).getListValue().getValuesList())
              .extracting(c -> c.getStructValue().getFieldsMap())
              .extracting(map -> map.get(Key.AP_MODEL).getStringValue(), map -> map.get(Key.FIRMWARE).getStringValue())
              .containsExactlyInAnyOrder(vcfTuples);
        });
  }
}
