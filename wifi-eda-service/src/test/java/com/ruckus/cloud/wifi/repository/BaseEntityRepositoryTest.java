package com.ruckus.cloud.wifi.repository;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4d5de7c804bf434e97696fe0f1921ec8');
    INSERT INTO tenant (id) VALUES ('3e38f08665e04435b81e0f08d9b86315');
    INSERT INTO venue (id, tenant) VALUES ('1446c8422cb74d4ba41a1af815aacc07', '4d5de7c804bf434e97696fe0f1921ec8');
    INSERT INTO venue (id, tenant) VALUES ('65b90044381445f2ad6f38633850a002', '4d5de7c804bf434e97696fe0f1921ec8');
    """)
public class BaseEntityRepositoryTest {

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("4d5de7c804bf434e97696fe0f1921ec8");

  @Autowired
  private VenueRepository venueRepository;

  @Test
  public void testFindByIdAndTenantId() {
    assertTrue(venueRepository.findByIdAndTenantId("1446c8422cb74d4ba41a1af815aacc07",
        "4d5de7c804bf434e97696fe0f1921ec8").isPresent());
  }

  @Test
  public void testGetByIdAndTenantId() {
    assertNotNull(venueRepository.getByIdAndTenantId("1446c8422cb74d4ba41a1af815aacc07",
        "4d5de7c804bf434e97696fe0f1921ec8"));
  }

  @Test
  public void testFindByTenantId() {
    List<Venue> venues = venueRepository.findByTenantId("4d5de7c804bf434e97696fe0f1921ec8");
    assertEquals(2, venues.size());
    assertTrue(
        venues.stream().filter(venue -> venue.getId().equals("1446c8422cb74d4ba41a1af815aacc07"))
            .findAny().isPresent());
    assertTrue(
        venues.stream().filter(venue -> venue.getId().equals("65b90044381445f2ad6f38633850a002"))
            .findAny().isPresent());
  }

  @Test
  public void testFindByTenantIdAndPaging() {
    Page<Venue> venues = venueRepository.findByTenantId(PageRequest.of(0, 1, Sort.by("id")),
        "4d5de7c804bf434e97696fe0f1921ec8");
    assertEquals(2, venues.getTotalPages());
    assertEquals(2, venues.getTotalElements());
    assertTrue(
        venues.stream().filter(venue -> venue.getId().equals("1446c8422cb74d4ba41a1af815aacc07"))
            .findAny().isPresent());

    Page<Venue> venuesNextPage = venueRepository.findByTenantId(PageRequest.of(1, 1, Sort.by("id")),
        "4d5de7c804bf434e97696fe0f1921ec8");
    assertTrue(
        venuesNextPage.stream()
            .filter(venue -> venue.getId().equals("65b90044381445f2ad6f38633850a002"))
            .findAny().isPresent());
  }
}
