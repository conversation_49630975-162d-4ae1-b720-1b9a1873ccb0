package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.enums.PoeModeEnum;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant)
        VALUES ('e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap_group (id, venue, tenant)
        VALUES ('818552afabf544878057e510b9bb88b5', 'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap_model_specific (id, poe_out, poe_mode, tenant)
        VALUES ('d565bac6afb747a4987d0d0945f77333', true, 'Auto', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap (id, name, ap_group, model_specific, tenant, soft_deleted)
        VALUES ('900000005015', 'My AP 15', '818552afabf544878057e510b9bb88b5', 'd565bac6afb747a4987d0d0945f77333', '4c8279f79307415fa9e4c88a1819f0fc', false);
    """)
public class ApModelSpecificRepositoryTest {

  private static final String AP_MODEL_SPECIFIC_ID = "d565bac6afb747a4987d0d0945f77333";
  private static final String VENUE_ID = "e465bac6afb747a4987d0d0945f77221";
  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String AP_ID = "900000005015";

  @Autowired
  private ApModelSpecificRepository target;

  @Test
  public void test_findByApIdAndTenantId() {
    var result = target.findByApIdAndTenantId(AP_ID, TENANT_ID);
    assertEquals(result.getId(), AP_MODEL_SPECIFIC_ID);
    assertTrue(result.getPoeOut());
    assertEquals(result.getPoeMode(), PoeModeEnum.Auto);
  }

}
