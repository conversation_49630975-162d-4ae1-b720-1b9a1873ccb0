package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_AP_GROUP;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_AP_GROUP_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_VENUE_AP_GROUP;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.FLOW_NAME;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.PATH_VARIABLES;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.common.ActivityCommon.NameValuePair;
import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.VenueGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.utils.TemplateUtils;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;

@WifiIntegrationTest
public class ConsumeAddApGroupByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @Test
  void testAddApGroupByTemplate(Tenant mspTenant, @Template Venue venue,
      @Template ApGroup apGroup) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    Venue venueInstance = new VenueGenerator()
        .setName(always(venue.getName()))
        .setIsTemplate(alwaysFalse())
        .setTemplateId(always(venue.getId()))
        .generate();
    repositoryUtil.createOrUpdate(venueInstance, ecTenantId, randomTxId());

    // create ec radius by msp radius template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(
        ADD_AP_GROUP, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.AP_GROUP,
        instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(activityExecutionPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, apGroup.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_AP_GROUP_BY_TEMPLATE, ADD_AP_GROUP_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    var plan = assertActivityPlan(ADD_AP_GROUP, ecTenantId);
    assertThat(plan.getDescriptionVariablesList())
        .contains(NameValuePair.newBuilder()
                .setName(PATH_VARIABLES).setValue(TemplateUtils.buildPathVariablesForDiffAndSync(
                    List.of(
                        Pair.of("venueId", venueInstance.getId()),
                        Pair.of("apGroupId", instanceId)))).build(),
            NameValuePair.newBuilder().setName(FLOW_NAME).setValue(ADD_VENUE_AP_GROUP).build());
    assertActivityStatusSuccess(ADD_AP_GROUP, ecTenantId);
    assertActivityStatusSuccess(ADD_AP_GROUP_BY_TEMPLATE, mspTenantId);

    assertThat(repositoryUtil.find(ApGroup.class, instanceId, ecTenantId)).isNotNull();
  }
}
