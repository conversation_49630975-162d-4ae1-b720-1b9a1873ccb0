package com.ruckus.cloud.wifi.integration.template;

import com.google.common.collect.Maps;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DECOUPLE_WIFI_NETWORK_INSTANCES;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.guestWifiNetwork;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@WifiIntegrationTest
public class ConsumeDecoupleWifiNetworkInstancesRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void decouple_pskNetworkInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add PskNetwork as a template
    final var pskNetworkTemplate = repositoryUtil.find(PskNetwork.class,
        addPskNetwork(map(pskNetwork("psk-template").generate()), true).getId(), mspTenant.getId(), true);

    // ec tenant add PskNetwork instance from template
    changeTxCtxTenant(ecTenantId);
    var pskNetworkInstance = NetworkTestFixture.randomPskNetwork(ecTenant, n -> {
      n.setName("psk-instance");
      n.setTemplateId(pskNetworkTemplate.getId());
      n.setIsTemplate(false);
      n.setIsEnforced(true);
    });
    pskNetworkInstance = repositoryUtil.createOrUpdate(pskNetworkInstance, ecTenantId);

    // Verify initial state - instance should have templateId and isEnforced = true
    PskNetwork instanceBeforeDecouple = repositoryUtil.find(PskNetwork.class, pskNetworkInstance.getId(), ecTenantId, false);
    assertAll("Verify network instance before decoupling",
        () -> assertEquals(pskNetworkTemplate.getId(), instanceBeforeDecouple.getTemplateId()),
        () -> assertFalse(instanceBeforeDecouple.getIsTemplate()),
        () -> assertTrue(instanceBeforeDecouple.getIsEnforced()));

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_WIFI_NETWORK_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_WIFI_NETWORK_INSTANCES, ecTenantId);

    // Verify network instance was decoupled
    PskNetwork instanceAfterDecouple = repositoryUtil.find(PskNetwork.class, pskNetworkInstance.getId(), ecTenantId,
        false);
    assertAll("Verify network instance after decoupling",
        () -> assertNull(instanceAfterDecouple.getTemplateId(), "templateId should be null after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsEnforced(), "isEnforced should be false after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsTemplate(), "isTemplate should remain false"));

    // Verify template is not affected
    changeTxCtxTenant(mspTenant.getId());
    PskNetwork templateAfterDecouple = repositoryUtil.find(PskNetwork.class, pskNetworkTemplate.getId(), mspTenant.getId(), true);
    assertAll("Verify template is not affected",
        () -> assertTrue(templateAfterDecouple.getIsTemplate(), "Template should remain as template"),
        () -> assertEquals(pskNetworkTemplate.getName(), templateAfterDecouple.getName(),
            "Template name should be unchanged"));

    assertDdccmCfgRequestNotSent(ecTenantId);
    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instanceAfterDecouple.getId());
  }

  @Test
  public void decouple_guestNetworkInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add GuestNetwork as a template
    GuestNetwork guestNetworkTemplate = addGuestNetworkTemplate(map(
        guestWifiNetwork().generate()));

    // ec tenant add GuestNetwork instance from template
    changeTxCtxTenant(ecTenantId);
    var guestNetworkInstance = GuestNetworkTestFixture.randomGuestNetwork(ecTenant, n -> {
      n.setName("guest-instance");
      n.setTemplateId(guestNetworkTemplate.getId());
      n.setIsTemplate(false);
      n.setIsEnforced(true);
      n.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.ClickThrough);
    });
    guestNetworkInstance = repositoryUtil.createOrUpdate(guestNetworkInstance, ecTenantId);

    // Verify initial state
    GuestNetwork instanceBeforeDecouple = repositoryUtil.find(GuestNetwork.class, guestNetworkInstance.getId(),
        ecTenantId, false);
    assertAll("Verify Guest network instance before decoupling",
        () -> assertEquals(guestNetworkTemplate.getId(), instanceBeforeDecouple.getTemplateId()),
        () -> assertFalse(instanceBeforeDecouple.getIsTemplate()),
        () -> assertTrue(instanceBeforeDecouple.getIsEnforced()));

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_WIFI_NETWORK_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_WIFI_NETWORK_INSTANCES, ecTenantId);

    // Verify network instance was decoupled
    GuestNetwork instanceAfterDecouple = repositoryUtil.find(GuestNetwork.class, guestNetworkInstance.getId(),
        ecTenantId, false);
    assertAll("Verify Guest network instance after decoupling",
        () -> assertNull(instanceAfterDecouple.getTemplateId(), "templateId should be null after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsEnforced(), "isEnforced should be false after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsTemplate(), "isTemplate should remain false"));

    assertDdccmCfgRequestNotSent(ecTenantId);
    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instanceAfterDecouple.getId());
  }

  @Test
  public void decouple_openNetworkInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add OpenNetwork as a template
    final var openNetworkTemplate = repositoryUtil.createOrUpdate(NetworkTestFixture.randomOpenNetwork(mspTenant, n -> {
      n.setName("open-template");
      n.setIsTemplate(true);
    }), mspTenant.getId());

    // ec tenant add OpenNetwork instance from template
    changeTxCtxTenant(ecTenantId);
    var openNetworkInstance = NetworkTestFixture.randomOpenNetwork(ecTenant, n -> {
      n.setName("open-instance");
      n.setTemplateId(openNetworkTemplate.getId());
      n.setIsTemplate(false);
      n.setIsEnforced(true);
    });
    openNetworkInstance = repositoryUtil.createOrUpdate(openNetworkInstance, ecTenantId);

    // Verify initial state
    OpenNetwork instanceBeforeDecouple = repositoryUtil.find(OpenNetwork.class, openNetworkInstance.getId(), ecTenantId, false);
    assertAll("Verify Open network instance before decoupling",
        () -> assertEquals(openNetworkTemplate.getId(), instanceBeforeDecouple.getTemplateId()),
        () -> assertFalse(instanceBeforeDecouple.getIsTemplate()),
        () -> assertTrue(instanceBeforeDecouple.getIsEnforced()));

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_WIFI_NETWORK_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_WIFI_NETWORK_INSTANCES, ecTenantId);

    // Verify network instance was decoupled
    OpenNetwork instanceAfterDecouple = repositoryUtil.find(OpenNetwork.class, openNetworkInstance.getId(), ecTenantId, false);
    assertAll("Verify Open network instance after decoupling",
        () -> assertNull(instanceAfterDecouple.getTemplateId(), "templateId should be null after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsEnforced(), "isEnforced should be false after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsTemplate(), "isTemplate should remain false"));

    assertDdccmCfgRequestNotSent(ecTenantId);
    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instanceAfterDecouple.getId());
  }

  @Test
  public void decouple_multipleNetworkTypes(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add multiple network templates
    final var pskTemplate = addPskNetwork(map(pskNetwork("psk-template").generate()), true);
    GuestNetwork guestTemplate = addGuestNetworkTemplate(map(guestWifiNetwork().generate()));
    final var openTemplate = repositoryUtil.createOrUpdate(NetworkTestFixture.randomOpenNetwork(mspTenant, n -> {
      n.setName("open-template");
      n.setIsTemplate(true);
    }), mspTenant.getId());

    // ec tenant add multiple network instances from templates
    changeTxCtxTenant(ecTenantId);
    var pskInstance = NetworkTestFixture.randomPskNetwork(ecTenant, n -> {
      n.setName("psk-instance");
      n.setTemplateId(pskTemplate.getId());
      n.setIsTemplate(false);
      n.setIsEnforced(true);
    });
    pskInstance = repositoryUtil.createOrUpdate(pskInstance, ecTenantId);

    var guestInstance = GuestNetworkTestFixture.randomGuestNetwork(ecTenant, n -> {
      n.setName("guest-instance");
      n.setTemplateId(guestTemplate.getId());
      n.setIsTemplate(false);
      n.setIsEnforced(true);
      n.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.ClickThrough);
    });
    guestInstance = repositoryUtil.createOrUpdate(guestInstance, ecTenantId);

    var openInstance = NetworkTestFixture.randomOpenNetwork(ecTenant, n -> {
      n.setName("open-instance");
      n.setTemplateId(openTemplate.getId());
      n.setIsTemplate(false);
      n.setIsEnforced(true);
    });
    openInstance = repositoryUtil.createOrUpdate(openInstance, ecTenantId);

    // Execute decouple operation for all networks
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_WIFI_NETWORK_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_WIFI_NETWORK_INSTANCES, ecTenantId);

    // Verify all network instances were decoupled
    PskNetwork pskAfterDecouple = repositoryUtil.find(PskNetwork.class, pskInstance.getId(), ecTenantId, false);
    GuestNetwork guestAfterDecouple = repositoryUtil.find(GuestNetwork.class, guestInstance.getId(), ecTenantId, false);
    OpenNetwork openAfterDecouple = repositoryUtil.find(OpenNetwork.class, openInstance.getId(), ecTenantId, false);

    assertAll("Verify all network instances were decoupled",
        () -> assertNull(pskAfterDecouple.getTemplateId(), "PSK network templateId should be null"),
        () -> assertFalse(pskAfterDecouple.getIsEnforced(), "PSK network isEnforced should be false"),
        () -> assertNull(guestAfterDecouple.getTemplateId(), "Guest network templateId should be null"),
        () -> assertFalse(guestAfterDecouple.getIsEnforced(), "Guest network isEnforced should be false"),
        () -> assertNull(openAfterDecouple.getTemplateId(), "Open network templateId should be null"),
        () -> assertFalse(openAfterDecouple.getIsEnforced(), "Open network isEnforced should be false"));

    assertDdccmCfgRequestNotSent(ecTenantId);

    // Validate CmnCfgCollector message contains all three decoupled network instances
    String requestId = TxCtxHolder.txId();
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(ecTenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(p -> p.getTenantId().equals(ecTenantId))
        .matches(p -> p.getRequestId().equals(requestId));

    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .hasSize(3)
        .extracting(Operations::getId)
        .containsExactlyInAnyOrder(pskInstance.getId(), guestInstance.getId(), openInstance.getId());

    // Verify each operation in the message
    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .allMatch(o -> EsConstants.Index.NETWORK.equals(o.getIndex()))
        .allMatch(o -> o.getOpType().equals(OpType.MOD))
        .allMatch(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(ecTenantId))
        .allMatch(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())
        .allMatch(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue());

    // Verify templates are not affected
    changeTxCtxTenant(mspTenant.getId());
    PskNetwork pskTemplateAfter = repositoryUtil.find(PskNetwork.class, pskTemplate.getId(), mspTenant.getId(), true);
    GuestNetwork guestTemplateAfter = repositoryUtil.find(GuestNetwork.class, guestTemplate.getId(), mspTenant.getId(), true);
    OpenNetwork openTemplateAfter = repositoryUtil.find(OpenNetwork.class, openTemplate.getId(), mspTenant.getId(), true);

    assertAll("Verify templates are not affected",
        () -> assertTrue(pskTemplateAfter.getIsTemplate(), "PSK template should remain as template"),
        () -> assertTrue(guestTemplateAfter.getIsTemplate(), "Guest template should remain as template"),
        () -> assertTrue(openTemplateAfter.getIsTemplate(), "Open template should remain as template"));
  }

  @Test
  public void decouple_noNetworkInstances_shouldSucceed(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant with no network instances
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // Execute decouple operation on tenant with no networks
    changeTxCtxTenant(ecTenantId);
    String requestId = randomTxId();
    sendWifiCfgRequest(ecTenantId, requestId, CfgAction.DECOUPLE_WIFI_NETWORK_INSTANCES, userName, new RequestParams(),
        Maps.newHashMap(), "", false);

    assertActivityStatusSuccess(DECOUPLE_WIFI_NETWORK_INSTANCES, ecTenantId);

    // Should not receive any viewmodel operations since no networks were affected
    assertViewmodelOpsNotSent(ecTenantId);
    assertDdccmCfgRequestNotSent(ecTenantId);
  }

  private void assertViewmodelOpsNotSent(String tenantId) {
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }

  private void validateCmnCfgCollectorMessage(String tenantId, String requestId, String instanceId) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(p -> p.getTenantId().equals(tenantId))
        .matches(p -> p.getRequestId().equals(requestId));

    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .hasSize(1)
        .filteredOn(o -> o.getId().equals(instanceId)).first()
        .matches(o -> EsConstants.Index.NETWORK.equals(o.getIndex()))
        .matches(o -> o.getOpType().equals(OpType.MOD))
        .matches(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(tenantId))
        .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())
        .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue());
  }
}