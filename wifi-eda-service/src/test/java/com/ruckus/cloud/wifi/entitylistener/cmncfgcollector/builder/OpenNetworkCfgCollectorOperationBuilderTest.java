package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.AbstractNetworkCfgCollectorOperationBuilderTest.MockVenueApGroupsProjection;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.service.template.TemplateManagementService;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApGroupsProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@WifiUnitTest
public class OpenNetworkCfgCollectorOperationBuilderTest {

  @MockBean
  private RepositoryUtil repositoryUtil;
  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private TemplateManagementService templateManagementService;
  @SpyBean
  private OpenNetworkCfgCollectorOperationBuilder openNetworkCfgCollectorOperationBuilder;
  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  public void testGetEntityClass() {
    assertThat(new OpenNetworkCfgCollectorOperationBuilder().entityClass())
        .isEqualTo(OpenNetwork.class);
  }

  @Nested
  class testBuildConfig {

    final String apSerialNumber = "123456789012";

    @Test
    public void givenAddOweTransitionNetwork() {
      Operations.Builder builder = Operations.newBuilder();
      var tenant = new Tenant(txCtxExtension.getTenantId());
      final var venueId = randomId();
      final var apGroupId = randomId();
      final var openNetwork1 = (OpenNetwork) network(OpenNetwork.class).generate();
      openNetwork1.setName("openNetwork1");
      openNetwork1.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      openNetwork1.setIsOweMaster(true);
      openNetwork1.setOwePairNetworkId("childId");
      openNetwork1.setTenant(tenant);
      openNetwork1.setCreatedDate(new Date());
      openNetwork1.setUpdatedDate(new Date());
      openNetwork1.getWlan().setNetwork(openNetwork1);
      repositoryUtil.createOrUpdate(openNetwork1, tenant.getId(), randomTxId());
      var venueApGroupsProjections = new ArrayList<>();
      venueApGroupsProjections.add(
          new MockVenueApGroupsProjection(
              venueId, apGroupId, true));

      doReturn(new PageImpl<>(venueApGroupsProjections), Page.empty()).when(networkRepository)
          .findVenueApGroupsByNetworkId(
              eq(txCtxExtension.getTenantId()), eq(openNetwork1.getId()), any());
      openNetworkCfgCollectorOperationBuilder.config(builder, openNetwork1, EntityAction.ADD);

      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(Key.ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(openNetwork1.getId());
      assertThat(docMap.get(Key.TYPE))
          .isEqualTo(EsConstants.Value.TYPE_NETWORK);
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(openNetwork1.getTenant().getId());
      assertThat(docMap.get(Key.NAME))
          .extracting(p -> p.getStringValue())
          .isEqualTo(openNetwork1.getName());
      assertThat(docMap.get(Key.NW_SUB_TYPE))
          .extracting(p -> p.getStringValue())
          .isEqualTo("open");
      assertThat(docMap.get(Key.SECURITY_PROTOCOL))
          .extracting(p -> p.getStringValue())
          .isEqualTo(openNetwork1.getWlan().getWlanSecurity().name());
      assertThat(docMap.get(Key.OWE_PAIR_NETWORK_ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(openNetwork1.getOwePairNetworkId());
      assertThat(docMap.get(Key.OWE_PAIR_NETWORK_ID))
          .extracting(p -> p.getStringValue())
          .isEqualTo(openNetwork1.getOwePairNetworkId());
      assertThat(docMap.get(Key.IS_OWE_MASTER))
          .extracting(p -> p.getBoolValue())
          .isEqualTo(true);
      assertThat(docMap.get(Key.VENUE_APGROUPS))
          .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
          .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
          .allSatisfy(vap -> {
            assertEquals(venueId, vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue());
            assertEquals(true,
                vap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertEquals(1,
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesCount());
            assertTrue(
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesList()
                    .contains(ValueUtils.stringValue(apGroupId)));
          });
    }

    @Test
    public void givenPureOweFromOweTransition() {
      Operations.Builder builder = Operations.newBuilder();
      var tenant = new Tenant(txCtxExtension.getTenantId());
      final var venueId = randomId();
      final var apGroupId = randomId();
      final var openNetwork1 = (OpenNetwork) network(OpenNetwork.class).generate();
      openNetwork1.setName("openNetwork2");
      openNetwork1.getWlan().setWlanSecurity(WlanSecurityEnum.OWE);
      openNetwork1.setIsOweMaster(null);
      openNetwork1.setOwePairNetworkId(null);
      openNetwork1.setTenant(tenant);
      openNetwork1.setCreatedDate(new Date());
      openNetwork1.setUpdatedDate(new Date());
      openNetwork1.getWlan().setNetwork(openNetwork1);
      repositoryUtil.createOrUpdate(openNetwork1, tenant.getId(), randomTxId());
      var venueApGroupsQueryProjections = new ArrayList<>();
      venueApGroupsQueryProjections.add(
          new MockVenueApGroupsProjection(
              venueId, apGroupId, true));

      doReturn(new PageImpl<>(venueApGroupsQueryProjections), Page.empty()).when(networkRepository)
          .findVenueApGroupsByNetworkId(
              eq(txCtxExtension.getTenantId()), eq(openNetwork1.getId()), any());
      openNetworkCfgCollectorOperationBuilder.config(builder, openNetwork1, EntityAction.MODIFY);

      Map<String, Value> docMap = builder.build().getDocMap();

      assertThat(docMap.get(Key.ID))
          .extracting(Value::getStringValue)
          .isEqualTo(openNetwork1.getId());
      assertThat(docMap.get(Key.TYPE))
          .isEqualTo(EsConstants.Value.TYPE_NETWORK);
      assertThat(docMap.get(Key.TENANT_ID))
          .extracting(Value::getStringValue)
          .isEqualTo(openNetwork1.getTenant().getId());
      assertThat(docMap.get(Key.NAME))
          .extracting(Value::getStringValue)
          .isEqualTo(openNetwork1.getName());
      assertThat(docMap.get(Key.NW_SUB_TYPE))
          .extracting(Value::getStringValue)
          .isEqualTo("open");
      assertThat(docMap.get(Key.SECURITY_PROTOCOL))
          .extracting(Value::getStringValue)
          .isEqualTo(openNetwork1.getWlan().getWlanSecurity().name());
      assertThat(docMap.get(Key.OWE_PAIR_NETWORK_ID))
          .extracting(Value::getNullValue)
          .isEqualTo(NullValue.NULL_VALUE);
      assertThat(docMap.get(Key.IS_OWE_MASTER))
          .extracting(Value::getNullValue)
          .isEqualTo(NullValue.NULL_VALUE);
      assertThat(docMap.get(Key.VENUE_APGROUPS))
          .extracting(com.ruckus.cloud.events.gpb.Value::getListValue)
          .extracting(
              ListValue::getValuesList,
              InstanceOfAssertFactories.list(com.ruckus.cloud.events.gpb.Value.class))
          .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
          .allSatisfy(vap -> {
            assertEquals(venueId, vap.getFieldsOrThrow(Key.VENUE_ID).getStringValue());
            assertEquals(true,
                vap.getFieldsOrThrow(Key.IS_ALL_AP_GROUPS).getBoolValue());
            assertEquals(1,
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesCount());
            assertTrue(
                vap.getFieldsOrThrow(Key.APGROUP_IDS).getListValue().getValuesList()
                    .contains(ValueUtils.stringValue(apGroupId)));
          });
    }

    @Data
    @AllArgsConstructor
    static class VenueApGroupsApSerialNumbersProjectionIml implements
        VenueApGroupsProjection {
      String venueId;
      String apGroups;
      boolean isAllApGroups;
      String apSerialNumbers;

      @Override
      public boolean getIsAllApGroups() {
        return true;
      }
    }
  }
}
