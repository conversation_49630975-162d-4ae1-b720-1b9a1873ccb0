package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.AP_SERIAL_NUMBERS;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.VENUE_ID;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_ETHERNET_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroup;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.service.core.exception.ObjectAlreadyExistException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeActivateApOnApGroupRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private LanPortAdoptionDataHelper dataHelper;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenActivateApOnApGroupRequest {
    private String venueId;
    private String apGroupId;
    private String apId;

    @BeforeEach
    void beforeEach(Ap ap, ApGroup apGroup) {
      apId = ap.getId();
      apGroupId = apGroup.getId();
      venueId = apGroup.getVenue().getId();
      apGroup.getVenue().setCountryCode("US");
    }

    @Test
    void givenApGroupNotExists(Tenant tenant) {
      final var notExistApGroupId = randomId();
      assertThatThrownBy(
          () ->
              messageUtil.sendWifiCfgRequest(
                  tenant.getId(),
                  txCtxExtension.getRequestId(),
                  CfgAction.ACTIVATE_AP_ON_AP_GROUP,
                  randomName(),
                  new RequestParams()
                      .addPathVariable("venueId", venueId)
                      .addPathVariable("apGroupId", notExistApGroupId)
                      .addPathVariable("serialNumber", apId),
                  ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(ObjectNotFoundException.class);

      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());
      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_AP_ON_AP_GROUP));
    }

    @Nested
    class GivenApGroupExists {

      @Test
      void givenApNotExists(Tenant tenant) {
        final var notExistApId = randomSerialNumber();
        assertThatThrownBy(
            () ->
                messageUtil.sendWifiCfgRequest(
                    tenant.getId(),
                    txCtxExtension.getRequestId(),
                    CfgAction.ACTIVATE_AP_ON_AP_GROUP,
                    randomName(),
                    new RequestParams()
                        .addPathVariable("venueId", venueId)
                        .addPathVariable("apGroupId", apGroupId)
                        .addPathVariable("serialNumber", notExistApId),
                    ""))
            .isNotNull()
            .getRootCause()
            .isInstanceOf(ObjectNotFoundException.class);

        messageCaptors
            .assertThat(
                messageCaptors.getDdccmMessageCaptor(),
                messageCaptors.getCmnCfgCollectorMessageCaptor())
            .doesNotSendByTenant(tenant.getId());

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
            .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_AP_ON_AP_GROUP));
      }

      @Test
      void givenApGroupTheSame(Tenant tenant) {
        assertThatThrownBy(
            () ->
                messageUtil.sendWifiCfgRequest(
                    tenant.getId(),
                    txCtxExtension.getRequestId(),
                    CfgAction.ACTIVATE_AP_ON_AP_GROUP,
                    randomName(),
                    new RequestParams()
                        .addPathVariable("venueId", venueId)
                        .addPathVariable("apGroupId", apGroupId)
                        .addPathVariable("serialNumber", apId),
                    ""))
            .isNotNull()
            .getRootCause()
            .isInstanceOf(ObjectAlreadyExistException.class);

        messageCaptors
            .assertThat(
                messageCaptors.getDdccmMessageCaptor(),
                messageCaptors.getCmnCfgCollectorMessageCaptor())
            .doesNotSendByTenant(tenant.getId());

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
            .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_AP_ON_AP_GROUP));
      }

      @Test
      void givenApGroupAvailable(Tenant tenant) {
        Venue venue = new Venue(randomId());
        venue.setName(randomName());
        venue.setCountryCode("US");
        ApGroup apGroup = new ApGroup(randomId());
        apGroup.setName(randomName());
        apGroup.setVenue(venue);
        repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRandomId());
        repositoryUtil.createOrUpdate(apGroup, tenant.getId(), txCtxExtension.newRandomId());
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.ACTIVATE_AP_ON_AP_GROUP,
            randomName(),
            new RequestParams()
                .addPathVariable("venueId", venue.getId())
                .addPathVariable("apGroupId", apGroup.getId())
                .addPathVariable("serialNumber", apId),
            "");

        assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(
                m -> m.getPayload().getOperationsList(),
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .hasSize(2)
            .first()
            .matches(o -> o.getAction() == Action.MODIFY)
            .extracting(Operation::getAp)
            .matches(ap -> ap.getId().equals(apId))
            .matches(ap -> ap.getApGroupId().equals(apGroup.getId()));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
            .isNotEmpty()
            .hasSize(1)
            .satisfies(
                ops -> {
                  assertThat(ops)
                      .isNotEmpty()
                      .singleElement()
                      .satisfies(
                          op -> {
                            assertThat(op.getId()).isEqualTo(apId);
                            assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                            assertThat(op.getDocMap())
                                .containsEntry(Key.TYPE, Value.TYPE_DEVICE)
                                .containsEntry(
                                    Key.TENANT_ID, ValueUtils.stringValue(tenant.getId()))
                                .containsEntry(Key.SERIAL_NUMBER, ValueUtils.stringValue(apId))
                                .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venue.getId()))
                                .containsEntry(
                                    Key.DEVICE_GROUP_ID, ValueUtils.stringValue(apGroup.getId()));
                          });
                });

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_AP_ON_AP_GROUP));
      }
    }
  }

  @Disabled("For scaling test purpose only")
  @Nested
  class With1KApOnApGroupRequest {

    private String networkId;
    private String venueId;
    private String apGroupId;
    private List<String> apSerialNumbers;
    private long start;

    @BeforeEach
    void beforeEach(ApGroup apGroup, @OpenNetwork Network network) {
      networkId = network.getId();
      apGroupId = apGroup.getId();
      venueId = apGroup.getVenue().getId();
      apGroup.getVenue().setCountryCode("US");

      apSerialNumbers = new ArrayList<>();
      List<Ap> apList = Generators.ap().setApGroup(always(apGroup)).toListGenerator(300).generate();
      apList.forEach(ap -> {
        repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), txCtxExtension.newRandomId());
        apSerialNumbers.add(ap.getId());
      });

      start = System.nanoTime();
    }

    @AfterEach
    void afterEach() {
      System.out.println("Time elapsed: " + (System.nanoTime() - start) / 1000000 + " ms");
    }

    @Test
    void givenApGroupAvailable(Tenant tenant) {
      Venue venue = new Venue(randomId());
      venue.setName(randomName());
      venue.setCountryCode("US");
      ApGroup apGroup = new ApGroup(randomId());
      apGroup.setName(randomName());
      apGroup.setVenue(venue);
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRandomId());
      repositoryUtil.createOrUpdate(apGroup, tenant.getId(), txCtxExtension.newRandomId());

      String testApId = apSerialNumbers.get(0);
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.ACTIVATE_AP_ON_AP_GROUP,
          randomName(),
          new RequestParams()
              .addPathVariable("venueId", venue.getId())
              .addPathVariable("apGroupId", apGroup.getId())
              .addPathVariable("serialNumber", testApId),
          "");

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(2)
          .first()
          .matches(o -> o.getAction() == Action.MODIFY)
          .extracting(Operation::getAp)
          .matches(ap -> ap.getId().equals(testApId))
          .matches(ap -> ap.getApGroupId().equals(apGroup.getId()));
    }

    @Test
    void givenApGroupAvailableInSameVenueWithAllAps(Tenant tenant) {
      // Construct payload with all AP serial numbers
      VenueApGroup venueApGroup = new VenueApGroup();
      venueApGroup.setId(randomId());
      venueApGroup.setName(randomName());
      venueApGroup.setApSerialNumbers(apSerialNumbers);

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.ADD_VENUE_AP_GROUP,
          randomName(),
          new RequestParams()
              .addPathVariable("venueId", venueId),
          venueApGroup);

      // Validate the request
      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(apSerialNumbers.size() + 1) // 1 for ApGroup add, 100 for Ap modify
          .satisfies(ops -> {
            // Validate the ApGroup add operation
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.ADD)
                .hasSize(1)
                .singleElement()
                .satisfies(op -> {
                  assertThat(op.getApGroup().getId()).isEqualTo(venueApGroup.getId());
                  assertThat(op.getApGroup().getVenueId()).isEqualTo(venueId);
                });

            // Validate the 100 Ap modify operations
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.MODIFY)
                .hasSize(apSerialNumbers.size())
                .allSatisfy(op -> {
                  assertThat(apSerialNumbers).contains(op.getAp().getId());
                  assertThat(op.getAp().getApGroupId()).isEqualTo(venueApGroup.getId());
                });
          });
    }

    @Test
    void givenApGroupAvailableInAnotherVenueWithAllAps(Tenant tenant) {

      Venue venue = new Venue(randomId());
      venue.setName(randomName());
      venue.setCountryCode("US");
      repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRandomId());

      // Construct payload with all AP serial numbers
      VenueApGroup venueApGroup = new VenueApGroup();
      venueApGroup.setId(randomId());
      venueApGroup.setName(randomName());
      venueApGroup.setApSerialNumbers(apSerialNumbers);

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.ADD_VENUE_AP_GROUP,
          randomName(),
          new RequestParams()
              .addPathVariable("venueId", venue.getId()),
          venueApGroup);

      // Validate the request
      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(apSerialNumbers.size() + 1) // 1 for ApGroup add, 100 for Ap modify
          .satisfies(ops -> {
            // Validate the ApGroup add operation
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.ADD)
                .hasSize(1)
                .singleElement()
                .satisfies(op -> {
                  assertThat(op.getApGroup().getId()).isEqualTo(venueApGroup.getId());
                  assertThat(op.getApGroup().getVenueId()).isEqualTo(venue.getId());
                });

            // Validate the 100 Ap modify operations
            assertThat(ops)
                .filteredOn(op -> op.getAction() == Action.MODIFY)
                .hasSize(apSerialNumbers.size())
                .allSatisfy(op -> {
                  assertThat(apSerialNumbers).contains(op.getAp().getId());
                  assertThat(op.getAp().getApGroupId()).isEqualTo(venueApGroup.getId());
                });
          });
    }
  }

  @Nested
  class WhenActivateApOnApGroupButEthernetPortProfileOnApNotVenueRequest {

    private String venueId;
    private String apGroupId;
    private String apId;
    private String portId = "1";
    private String softGreProfileId;
    private String clientIsolationProfileId;

    @BeforeEach
    void beforeEach(Tenant tenant, @ApModel("R550") Ap ap, ApGroup apGroup, Venue venue) {
      apId = ap.getId();
      apGroupId = apGroup.getId();
      venueId = apGroup.getVenue().getId();
      apGroup.getVenue().setCountryCode("US");

      var randomClientIsolation = dataHelper.createClientIsolationAllowlist(tenant);
      clientIsolationProfileId = randomClientIsolation.getId();
      var randomSoftGreProfile = dataHelper.createSoftGreProfile(tenant);
      softGreProfileId = randomSoftGreProfile.getId();

      var clientIsolationLanPortActivation =
          getClientIsolationLanPortActivation(randomClientIsolation);
      var softGreLanPortActivation = getSoftGreLanPortActivation(randomSoftGreProfile);
      dataHelper.createApLanPortDataWithAdoption(
          venue, ap, portId, 3, of(softGreLanPortActivation, clientIsolationLanPortActivation));
      dataHelper.createApLanPortData(venue, ap, "2", 4);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      Venue venue = new Venue(randomId());
      venue.setName(randomName());
      venue.setCountryCode("US");
      ApGroup apGroup = new ApGroup(randomId());
      apGroup.setName(randomName());
      apGroup.setVenue(venue);
      repositoryUtil.createOrUpdate(venue, tenantId, txCtxExtension.newRandomId());
      repositoryUtil.createOrUpdate(apGroup, tenantId, txCtxExtension.newRandomId());
      messageUtil.sendWifiCfgRequest(
          tenantId,
          txCtxExtension.getRequestId(),
          CfgAction.ACTIVATE_AP_ON_AP_GROUP,
          randomName(),
          new RequestParams()
              .addPathVariable("venueId", venue.getId())
              .addPathVariable("apGroupId", apGroup.getId())
              .addPathVariable("serialNumber", apId),
          "");
      final var apLanPorts = repositoryUtil.findAll(LanPortAdoption.class, tenantId);
      assertThat(apLanPorts).isEmpty();
      final var softGreProfileLanPortActivations =
          repositoryUtil.findAll(SoftGreProfileLanPortActivation.class, tenantId);
      assertThat(softGreProfileLanPortActivations).isEmpty();
      final var clientIsolationLanPortActivations =
          repositoryUtil.findAll(ClientIsolationLanPortActivation.class, tenantId);
      assertThat(clientIsolationLanPortActivations).isEmpty();

      final var cmnCfgCollectorMessage =
          messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
      var operations = cmnCfgCollectorMessage.getPayload().getOperationsList();

      assertProfileIndexNoActivationList(
          operations, Index.SOFT_GRE_PROFILE_INDEX_NAME, softGreProfileId);
      assertProfileIndexNoActivationList(
          operations, Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME, clientIsolationProfileId);
    }
  }

  @Nested
  @FeatureFlag(
      enable = {
        WIFI_ETHERNET_SOFTGRE_TOGGLE,
        WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE,
        ACX_UI_ETHERNET_TOGGLE
      })
  class WhenActivateApOnApGroupButEthernetPortProfileOnApAndVenueRequest {
    private String targetVenueId;
    private String targetApGroupId;
    private String sourceVenueId;

    private String apId;
    private String portId = "1";
    private String softGreProfileIdOnAp;
    private String clientIsolationProfileIdOnAp;
    private String softGreProfileIdOnSourceVenue;
    private String clientIsolationProfileIdOnSourceVenue;
    private String softGreProfileIdOnTargetVenue;
    private String clientIsolationProfileIdOnTargetVenue;

    @BeforeEach
    void beforeEach(Tenant tenant, @ApModel("R550") Ap ap, ApGroup apGroup, Venue venue) {
      apId = ap.getId();
      apGroup.getVenue().setCountryCode("US");
      createApLanPort(tenant, ap, venue);
      createVenueModel(tenant, ap.getModel());
      createVenueModel(tenant, ap.getModel(), venue);
    }

    void createApLanPort(Tenant tenant, Ap ap, Venue venue) {
      sourceVenueId = venue.getId();
      var randomClientIsolation = dataHelper.createClientIsolationAllowlist(tenant);
      clientIsolationProfileIdOnAp = randomClientIsolation.getId();
      var randomSoftGreProfile = dataHelper.createSoftGreProfile(tenant);
      softGreProfileIdOnAp = randomSoftGreProfile.getId();

      var clientIsolationLanPortActivation =
          getClientIsolationLanPortActivation(randomClientIsolation);
      var softGreLanPortActivation = getSoftGreLanPortActivation(randomSoftGreProfile);
      dataHelper.createApLanPortDataWithAdoption(
          venue, ap, portId, 5, of(softGreLanPortActivation, clientIsolationLanPortActivation));
      dataHelper.createApLanPortDataWithAdoption(venue, ap, "2", 6);
    }

    void createVenueModel(Tenant tenant, String apModel, Venue venue) {
      var randomClientIsolation = dataHelper.createClientIsolationAllowlist(tenant);
      clientIsolationProfileIdOnSourceVenue = randomClientIsolation.getId();
      var randomSoftGreProfile = dataHelper.createSoftGreProfile(tenant);
      softGreProfileIdOnSourceVenue = randomSoftGreProfile.getId();
      var clientIsolationLanPortActivation =
          getClientIsolationLanPortActivation(randomClientIsolation);
      var softGreLanPortActivation = getSoftGreLanPortActivation(randomSoftGreProfile);
      dataHelper.createVenueLanPortDataWithAdoption(
          venue,
          apModel,
          portId,
          7,
          of(softGreLanPortActivation, clientIsolationLanPortActivation));
      dataHelper.createVenueLanPortDataWithAdoption(venue, apModel, "2", 8);
    }

    void createVenueModel(Tenant tenant, String apModel) {
      var randomClientIsolation = dataHelper.createClientIsolationAllowlist(tenant);
      clientIsolationProfileIdOnTargetVenue = randomClientIsolation.getId();
      var randomSoftGreProfile = dataHelper.createSoftGreProfile(tenant);
      softGreProfileIdOnTargetVenue = randomSoftGreProfile.getId();

      Venue venue = VenueTestFixture.randomVenue(tenant);
      venue.setName(randomName());
      venue.setCountryCode("US");
      ApGroup apGroup = new ApGroup(randomId());
      apGroup.setName(randomName());
      apGroup.setVenue(venue);
      venue = repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRandomId());
      apGroup =
          repositoryUtil.createOrUpdate(apGroup, tenant.getId(), txCtxExtension.newRandomId());
      targetApGroupId = apGroup.getId();
      targetVenueId = venue.getId();
      var clientIsolationLanPortActivation =
          getClientIsolationLanPortActivation(randomClientIsolation);
      var softGreLanPortActivation = getSoftGreLanPortActivation(randomSoftGreProfile);
      dataHelper.createVenueLanPortDataWithAdoption(
          venue,
          apModel,
          portId,
          3,
          of(softGreLanPortActivation, clientIsolationLanPortActivation));
      dataHelper.createVenueLanPortDataWithAdoption(venue, apModel, "2", 4);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final var lanPortAdoptionsBeforeUpdate = repositoryUtil.findAll(LanPortAdoption.class, tenantId);
      assertThat(lanPortAdoptionsBeforeUpdate).filteredOn(op -> op.getVenueLanPorts().isEmpty()).hasSize(2);
      assertThat(lanPortAdoptionsBeforeUpdate).filteredOn(op -> op.getApLanPorts().isEmpty()).hasSize(4);
      messageUtil.sendWifiCfgRequest(
          tenantId,
          txCtxExtension.getRequestId(),
          CfgAction.ACTIVATE_AP_ON_AP_GROUP,
          randomName(),
          new RequestParams()
              .addPathVariable("venueId", targetVenueId)
              .addPathVariable("apGroupId", targetApGroupId)
              .addPathVariable("serialNumber", apId),
          "");
      final var lanPortAdoptions = repositoryUtil.findAll(LanPortAdoption.class, tenantId);
      assertThat(lanPortAdoptions).filteredOn(op -> op.getVenueLanPorts().isEmpty()).isEmpty();
      assertThat(lanPortAdoptions).filteredOn(op -> op.getApLanPorts().isEmpty()).hasSize(4);
      final var softGreProfileLanPortActivations =
          repositoryUtil.findAll(SoftGreProfileLanPortActivation.class, tenantId);
      assertThat(softGreProfileLanPortActivations).hasSize(2);
      final var clientIsolationLanPortActivations =
          repositoryUtil.findAll(ClientIsolationLanPortActivation.class, tenantId);
      assertThat(clientIsolationLanPortActivations).hasSize(2);

      final var cmnCfgCollectorMessage =
          messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
      var operations = cmnCfgCollectorMessage.getPayload().getOperationsList();

      assertProfileIndexNoActivationList(
          operations, Index.SOFT_GRE_PROFILE_INDEX_NAME, softGreProfileIdOnAp);
      assertProfileIndexNoActivationList(
          operations, Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME, clientIsolationProfileIdOnAp);

      assertProfileIndexHaveActivationList(
          operations,
          Index.SOFT_GRE_PROFILE_INDEX_NAME,
          softGreProfileIdOnSourceVenue,
          sourceVenueId,
          null);
      assertProfileIndexHaveActivationList(
          operations,
          Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME,
          clientIsolationProfileIdOnSourceVenue,
          sourceVenueId,
          null);

      assertProfileIndexHaveActivationList(
          operations,
          Index.SOFT_GRE_PROFILE_INDEX_NAME,
          softGreProfileIdOnTargetVenue,
          targetVenueId,
          apId);
      assertProfileIndexHaveActivationList(
          operations,
          Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME,
          clientIsolationProfileIdOnTargetVenue,
          targetVenueId,
          apId);
    }
  }

  private void assertProfileIndexNoActivationList(
      List<Operations> operations, String indexName, String profileId) {
    assertThat(operations)
        .filteredOn(op -> indexName.equals(op.getIndex()) && op.getOpType() == OpType.MOD)
        .filteredOn(op -> profileId.equals(op.getId()))
        .hasSize(1)
        .singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(
            docMap -> {
              assertThat(docMap.get(Key.VENUE_ACTIVATIONS).getListValue().getValuesList())
                  .isEmpty();
              assertThat(docMap.get(Key.AP_ACTIVATIONS).getListValue().getValuesList()).isEmpty();
            });
  }

  private void assertProfileIndexHaveActivationList(
      List<Operations> operations,
      String indexName,
      String profileId,
      String venueId,
      String apId) {
    assertThat(operations)
        .filteredOn(op -> indexName.equals(op.getIndex()) && op.getOpType() == OpType.MOD)
        .filteredOn(op -> profileId.equals(op.getId()))
        .hasSize(1)
        .singleElement()
        .extracting(Operations::getDocMap)
        .satisfies(
            docMap -> {
              assertThat(docMap.get(Key.VENUE_ACTIVATIONS).getListValue().getValuesList())
                  .isNotEmpty()
                  .singleElement()
                  .extracting(com.ruckus.cloud.events.gpb.Value::getStructValue)
                  .matches(c -> venueId.equals(c.getFieldsMap().get(VENUE_ID).getStringValue()))
                  .matches(
                      c -> {
                        if (apId == null) {
                          return c.getFieldsMap()
                              .get(AP_SERIAL_NUMBERS)
                              .getListValue()
                              .getValuesList()
                              .isEmpty();
                        } else {
                          return c
                              .getFieldsMap()
                              .get(AP_SERIAL_NUMBERS)
                              .getListValue()
                              .getValuesList()
                              .stream()
                              .anyMatch(value -> apId.equals(value.getStringValue()));
                        }
                      });

              assertThat(docMap.get(Key.AP_ACTIVATIONS).getListValue().getValuesList()).isEmpty();
            });
  }

  private ClientIsolationLanPortActivation getClientIsolationLanPortActivation(
      ClientIsolationAllowlist clientIsolationProfile) {
    ClientIsolationLanPortActivation activation =
        com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
            .clientIsolationLanPortActivation()
            .generate();
    activation.setClientIsolationAllowlist(clientIsolationProfile);
    return activation;
  }

  private SoftGreProfileLanPortActivation getSoftGreLanPortActivation(
      SoftGreProfile softGreProfile) {
    SoftGreProfileLanPortActivation activation =
        Generators.softGreProfileLanPortActivation()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(always(Generators.dhcpOption82Settings().generate()))
            .generate();
    activation.setSoftGreProfile(softGreProfile);
    return activation;
  }
}
