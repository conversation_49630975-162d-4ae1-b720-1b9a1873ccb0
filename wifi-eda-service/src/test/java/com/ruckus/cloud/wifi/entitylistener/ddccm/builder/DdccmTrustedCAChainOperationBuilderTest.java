package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.TrustedCAChain;
import com.ruckus.cloud.wifi.eda.servicemodel.X509Certificate;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class DdccmTrustedCAChainOperationBuilderTest {

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");
  @SpyBean
  private DdccmTrustedCAChainOperationBuilder builder;

  @Test
  void testAddTrustedCAChainBuilder() {

    TrustedCAChain trustedCAChain = Generators.trustedCAChain().generate();

    List<Operation> operations = builder.build(new NewTxEntity<>(trustedCAChain),
        emptyTxChanges());

    assertEquals(1, operations.size());
    com.ruckus.acx.ddccm.protobuf.wifi.TrustedCAChain result = operations.get(0)
        .getTrustedCAChain();

    assertNotNull(result);
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());
    assertEquals(trustedCAChain.getTenant().getId(), result.getTenantId());
    assertEquals(trustedCAChain.getId(), result.getId());
    assertEquals(trustedCAChain.getId(), result.getName());
    assertEquals(trustedCAChain.getRootCert().getCertData(), result.getRootCertData());
    assertArrayEquals(
        trustedCAChain.getInterCerts().stream().map(X509Certificate::getCertData).toArray(),
        result.getInterCertDataList().toArray());
  }
}