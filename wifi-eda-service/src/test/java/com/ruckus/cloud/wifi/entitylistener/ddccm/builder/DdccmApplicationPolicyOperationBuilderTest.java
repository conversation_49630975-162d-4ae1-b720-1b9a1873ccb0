package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.ApplicationRuleType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApplicationType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.SignaturePackage;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApplicationPolicyProtocolEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApplicationRuleTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApplicationTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AvcUserDefinedTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MarkingPriorityEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MarkingTypeEnum;
import com.ruckus.cloud.wifi.repository.ApplicationPolicyRuleRepository;
import com.ruckus.cloud.wifi.repository.TenantRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;


@WifiUnitTest
public class DdccmApplicationPolicyOperationBuilderTest {

  @Autowired
  private DdccmApplicationPolicyOperationBuilder builder;

  @MockBean
  private TenantRepository tenantRepository;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyMspTenantId");

  @Test
  public void testAddApplicationPolicy() {
    Tenant tenant = new Tenant(TxCtxHolder.get().getTenant());
    ApplicationPolicy applicationPolicy = createApplicationPolicy(tenant);
    ApplicationPolicyRule systemDefinedRule = createSystemDefinedApplicationPolicyRule(
        applicationPolicy, 1);
    ApplicationPolicyRule userDefinedIpWithPortRule = createUserDefinedIpWithPortApplicationPolicyRule(
        applicationPolicy, 2);
    ApplicationPolicyRule userDefinedPortOnlyRule = createUserDefinedPortOnlyApplicationPolicyRule(
        applicationPolicy, 3);

    applicationPolicy.setRules(List.of(
        systemDefinedRule,
        userDefinedIpWithPortRule,
        userDefinedPortOnlyRule
    ));

    var txChanges = spy(TxChangesReader.class);

    doReturn(List.of(
        new NewTxEntity(applicationPolicy),
        new NewTxEntity(systemDefinedRule),
        new NewTxEntity(userDefinedIpWithPortRule),
        new NewTxEntity(userDefinedPortOnlyRule)
    )).when(txChanges).getNewEntities();
    doReturn(tenant).when(tenantRepository).getReferenceById(tenant.getId());

    List<Operation> operations = builder.build(new NewTxEntity<>(applicationPolicy), txChanges);

    assertEquals(3, operations.size());
    assertApplicationPolicyOperation(Action.ADD, applicationPolicy,
        operations.stream().filter(op -> applicationPolicy.getId().equals(op.getId())).findFirst()
            .get());
    assertUserDefinedIpWithPortRuleOperation(Action.ADD, userDefinedIpWithPortRule,
        operations.stream().filter(op -> userDefinedIpWithPortRule.getId().equals(op.getId()))
            .findFirst().get());
    assertUserDefinedPortOnlyRuleOperation(Action.ADD, userDefinedPortOnlyRule,
        operations.stream().filter(op -> userDefinedPortOnlyRule.getId().equals(op.getId()))
            .findFirst().get());
  }

  @Test
  public void testModifyApplicationPolicy() {
    Tenant tenant = new Tenant(TxCtxHolder.get().getTenant());
    ApplicationPolicy applicationPolicy = createApplicationPolicy(tenant);
    ApplicationPolicyRule systemDefinedRule = createSystemDefinedApplicationPolicyRule(
        applicationPolicy, 1);
    ApplicationPolicyRule userDefinedIpWithPortRule = createUserDefinedIpWithPortApplicationPolicyRule(
        applicationPolicy, 2);
    ApplicationPolicyRule userDefinedPortOnlyRule = createUserDefinedPortOnlyApplicationPolicyRule(
        applicationPolicy, 3);

    applicationPolicy.setRules(List.of(
        systemDefinedRule,
        userDefinedIpWithPortRule,
        userDefinedPortOnlyRule
    ));

    var txChanges = spy(TxChangesReader.class);

    Set<String> systemDefinedRuleModifiedProperties = Set.of("ruleType");
    Set<String> userDefinedRuleModifiedProperties = Set.of("ruleType");

    doReturn(List.of(
        new ModifiedTxEntity(applicationPolicy, Collections.emptySet()),
        new ModifiedTxEntity(systemDefinedRule, systemDefinedRuleModifiedProperties),
        new ModifiedTxEntity(userDefinedIpWithPortRule, userDefinedRuleModifiedProperties),
        new ModifiedTxEntity(userDefinedPortOnlyRule, userDefinedRuleModifiedProperties)
    )).when(txChanges).getModifiedEntities();
    doReturn(systemDefinedRuleModifiedProperties).when(txChanges).getModifiedProperties(eq(systemDefinedRule));
    doReturn(userDefinedRuleModifiedProperties).when(txChanges).getModifiedProperties(eq(userDefinedIpWithPortRule));
    doReturn(Collections.emptySet()).when(txChanges).getModifiedProperties(eq(userDefinedPortOnlyRule));
    doReturn(tenant).when(tenantRepository).getReferenceById(tenant.getId());

    List<Operation> operations = builder.build(new ModifiedTxEntity<>(applicationPolicy,
        Collections.emptySet()), txChanges);

    assertEquals(4, operations.size());
    assertApplicationPolicyOperation(Action.MODIFY, applicationPolicy,
        operations.stream().filter(op -> applicationPolicy.getId().equals(op.getId())).findFirst()
            .get());
    assertUserDefinedIpWithPortRuleOperation(Action.ADD, userDefinedIpWithPortRule,
        operations.stream().filter(op -> userDefinedIpWithPortRule.getId().equals(op.getId()))
            .findFirst().get());
    assertUserDefinedPortOnlyRuleOperation(Action.MODIFY, userDefinedPortOnlyRule,
        operations.stream().filter(op -> userDefinedPortOnlyRule.getId().equals(op.getId()))
            .findFirst().get());
  }

  @Test
  public void testDeleteApplicationPolicy() {
    Tenant tenant = new Tenant(TxCtxHolder.get().getTenant());
    ApplicationPolicy applicationPolicy = createApplicationPolicy(tenant);
    ApplicationPolicyRule systemDefinedRule = createSystemDefinedApplicationPolicyRule(
        applicationPolicy, 1);
    ApplicationPolicyRule userDefinedIpWithPortRule = createUserDefinedIpWithPortApplicationPolicyRule(
        applicationPolicy, 2);
    ApplicationPolicyRule userDefinedPortOnlyRule = createUserDefinedPortOnlyApplicationPolicyRule(
        applicationPolicy, 3);

    applicationPolicy.setRules(List.of(
        systemDefinedRule,
        userDefinedIpWithPortRule,
        userDefinedPortOnlyRule
    ));

    var txChanges = spy(TxChangesReader.class);

    doReturn(List.of(
        new DeletedTxEntity(applicationPolicy),
        new DeletedTxEntity(systemDefinedRule),
        new DeletedTxEntity(userDefinedIpWithPortRule),
        new DeletedTxEntity( userDefinedPortOnlyRule)
    )).when(txChanges).getDeletedEntities();
    doReturn(tenant).when(tenantRepository).getReferenceById(tenant.getId());

    List<Operation> operations = builder.build(new DeletedTxEntity<>(applicationPolicy), txChanges);

    assertEquals(3, operations.size());
    assertApplicationPolicyOperation(Action.DELETE, applicationPolicy,
        operations.stream().filter(op -> applicationPolicy.getId().equals(op.getId())).findFirst()
            .get());
    assertUserDefinedIpWithPortRuleOperation(Action.DELETE, userDefinedIpWithPortRule,
        operations.stream().filter(op -> userDefinedIpWithPortRule.getId().equals(op.getId()))
            .findFirst().get());
    assertUserDefinedPortOnlyRuleOperation(Action.DELETE, userDefinedPortOnlyRule,
        operations.stream().filter(op -> userDefinedPortOnlyRule.getId().equals(op.getId()))
            .findFirst().get());
  }

  @Test
  public void testHandleApplicationPolicySnapshot() {
    var sp1 = new SignaturePackage("v1");
    var sp2 = new SignaturePackage("v2");
    var tenant = new Tenant(TxCtxHolder.get().getTenant());
    tenant.setSignaturePackage(sp2);
    var applicationPolicy = createApplicationPolicy(tenant);
    var systemDefinedRule = createSystemDefinedApplicationPolicyRule(
        applicationPolicy, 1);
    var userDefinedIpWithPortRule = createUserDefinedIpWithPortApplicationPolicyRule(
        applicationPolicy, 2);
    var userDefinedPortOnlyRule = createUserDefinedPortOnlyApplicationPolicyRule(
        applicationPolicy, 3);
    applicationPolicy.setSignaturePackage(sp1);

    applicationPolicy.setRules(List.of(
        systemDefinedRule,
        userDefinedIpWithPortRule,
        userDefinedPortOnlyRule
    ));

    var txChanges = spy(TxChangesReader.class);

    var policyModifiedProperties = Set.of("updatedDate");
    var systemDefinedRuleModifiedProperties = Set.of("updatedDate");
    var userDefinedRuleModifiedProperties = Set.of("updatedDate");

    doReturn(List.of(
        new ModifiedTxEntity(applicationPolicy, policyModifiedProperties),
        new ModifiedTxEntity(systemDefinedRule, systemDefinedRuleModifiedProperties),
        new ModifiedTxEntity(userDefinedIpWithPortRule, userDefinedRuleModifiedProperties),
        new ModifiedTxEntity(userDefinedPortOnlyRule, userDefinedRuleModifiedProperties)
    )).when(txChanges).getModifiedEntities();
    doReturn(systemDefinedRuleModifiedProperties).when(txChanges).getModifiedProperties(eq(systemDefinedRule));
    doReturn(userDefinedRuleModifiedProperties).when(txChanges).getModifiedProperties(eq(userDefinedIpWithPortRule));
    doReturn(userDefinedRuleModifiedProperties).when(txChanges).getModifiedProperties(eq(userDefinedPortOnlyRule));
    doReturn(tenant).when(tenantRepository).getReferenceById(tenant.getId());

    var operations = builder.build(
        new ModifiedTxEntity<>(applicationPolicy, policyModifiedProperties), txChanges);

    // Should be the same just like delete.
    assertEquals(3, operations.size());
    assertApplicationPolicyOperation(Action.DELETE, applicationPolicy,
        operations.stream().filter(op -> applicationPolicy.getId().equals(op.getId())).findFirst()
            .get());
    assertUserDefinedIpWithPortRuleOperation(Action.DELETE, userDefinedIpWithPortRule,
        operations.stream().filter(op -> userDefinedIpWithPortRule.getId().equals(op.getId()))
            .findFirst().get());
    assertUserDefinedPortOnlyRuleOperation(Action.DELETE, userDefinedPortOnlyRule,
        operations.stream().filter(op -> userDefinedPortOnlyRule.getId().equals(op.getId()))
            .findFirst().get());
  }

  private ApplicationPolicy createApplicationPolicy(Tenant tenant) {
    ApplicationPolicy policy = new ApplicationPolicy(genUUID());
    policy.setName("Application policy");
    policy.setDescription("Description");
    policy.setTenant(tenant);
    return policy;
  }

  private ApplicationPolicyRule createSystemDefinedApplicationPolicyRule(ApplicationPolicy policy,
      int index) {
    ApplicationPolicyRule rule = new ApplicationPolicyRule(genUUID());
    rule.setName("System Defined");
    rule.setRuleType(ApplicationTypeEnum.SIGNATURE);
    rule.setCategory("Antivirus");
    rule.setAccessControl(ApplicationRuleTypeEnum.DENY);
    rule.setCategoryId(1);
    rule.setApplicationId(index);
    rule.setApplicationName("Lookout Mobile Security");
    rule.setPriority(index);
    rule.setTenant(policy.getTenant());
    rule.setApplicationPolicy(policy);
    return rule;
  }

  private ApplicationPolicyRule createUserDefinedIpWithPortApplicationPolicyRule(
      ApplicationPolicy policy, int index) {
    ApplicationPolicyRule rule = new ApplicationPolicyRule(genUUID());
    rule.setName("User Defined");
    rule.setRuleType(ApplicationTypeEnum.USER_DEFINED);
    rule.setPortMapping(AvcUserDefinedTypeEnum.IP_WITH_PORT);
    rule.setDestinationIp("127.0.0.1");
    rule.setNetmask("*************");
    rule.setDestinationPort(6666);
    rule.setProtocol(ApplicationPolicyProtocolEnum.TCP);
    rule.setAccessControl(ApplicationRuleTypeEnum.RATE_LIMIT);
    rule.setUplink(20000);
    rule.setDownlink(250);
    rule.setApplicationName("Test it");
    rule.setApplicationId(index);
    rule.setPriority(index);
    rule.setTenant(policy.getTenant());
    rule.setApplicationPolicy(policy);
    return rule;
  }

  private ApplicationPolicyRule createUserDefinedPortOnlyApplicationPolicyRule(
      ApplicationPolicy policy, int index) {
    ApplicationPolicyRule rule = new ApplicationPolicyRule(genUUID());
    rule.setName("User Defined 2");
    rule.setRuleType(ApplicationTypeEnum.USER_DEFINED);
    rule.setPortMapping(AvcUserDefinedTypeEnum.PORT_ONLY);
    rule.setDestinationPort(7777);
    rule.setProtocol(ApplicationPolicyProtocolEnum.UDP);
    rule.setAccessControl(ApplicationRuleTypeEnum.QOS);
    rule.setMarkingPriority(MarkingPriorityEnum.IEEE802_1P);
    rule.setUpLinkMarkingType(MarkingTypeEnum.BACKGROUND);
    rule.setDownLinkMarkingType(MarkingTypeEnum.VOICE);
    rule.setUplink(20000);
    rule.setDownlink(250);
    rule.setApplicationName("Test it again");
    rule.setApplicationId(index);
    rule.setPriority(index);
    rule.setTenant(policy.getTenant());
    rule.setApplicationPolicy(policy);
    return rule;
  }

  private void assertApplicationPolicyOperation(Action action, ApplicationPolicy policy,
      Operation operation) {
    assertEquals(action, operation.getAction());
    assertNotNull(operation.getApplicationPolicy());
    assertEquals(policy.getName(), operation.getApplicationPolicy().getName());
    assertEquals(policy.getTenant().getId(), operation.getApplicationPolicy().getTenantId());
    assertFalse(operation.getApplicationPolicy().hasAvcEventEnable());
    assertFalse(operation.getApplicationPolicy().hasAvcLogEnable());

    assertEquals(Action.DELETE != action ? 3 : 0,
        operation.getApplicationPolicy().getApplicationPolicyRulesCount());
    if (Action.DELETE != action) {
      // Assert system defined rule
      assertApplicationPolicyRule(Action.ADD,
          policy.getRules().stream().filter(r -> ApplicationTypeEnum.SIGNATURE == r.getRuleType())
              .findFirst().get(),
          operation.getApplicationPolicy().getApplicationPolicyRulesList().stream().
              filter(op -> ApplicationType.ApplicationType_SIGNATURE == op.getApplicationType())
              .findFirst().get());
      // Assert user defined ip with port rule
      assertApplicationPolicyRule(Action.ADD,
          policy.getRules().stream()
              .filter(r -> ApplicationTypeEnum.USER_DEFINED == r.getRuleType()).
              filter(r -> AvcUserDefinedTypeEnum.IP_WITH_PORT == r.getPortMapping()).findFirst()
              .get(),
          operation.getApplicationPolicy().getApplicationPolicyRulesList().stream().
              filter(op -> ApplicationType.ApplicationType_USER_DEFINED == op.getApplicationType()).
              filter(op -> ApplicationRuleType.ApplicationRuleType_RATE_LIMITING == op
                  .getApplicationRuleType()).findFirst().get());
      // Assert user defined port only rule
      assertApplicationPolicyRule(Action.ADD,
          policy.getRules().stream()
              .filter(r -> ApplicationTypeEnum.USER_DEFINED == r.getRuleType()).
              filter(r -> AvcUserDefinedTypeEnum.PORT_ONLY == r.getPortMapping()).findFirst().get(),
          operation.getApplicationPolicy().getApplicationPolicyRulesList().stream().
              filter(op -> ApplicationType.ApplicationType_USER_DEFINED == op.getApplicationType()).
              filter(
                  op -> ApplicationRuleType.ApplicationRuleType_QOS == op.getApplicationRuleType())
              .findFirst().get());
    }
  }

  private void assertUserDefinedIpWithPortRuleOperation(Action action, ApplicationPolicyRule rule,
      Operation operation) {
    assertEquals(action, operation.getAction());
    assertNotNull(operation.getUserDefinedApplicationRule());
    assertEquals(builder.getUserDefinedType(rule.getPortMapping()),
        operation.getUserDefinedApplicationRule().getType());
    assertEquals(rule.getDestinationIp(),
        operation.getUserDefinedApplicationRule().getDestinationIp().getValue());
    assertEquals(rule.getNetmask(),
        operation.getUserDefinedApplicationRule().getNetmask().getValue());
    assertEquals(rule.getDestinationPort(),
        Integer.valueOf(operation.getUserDefinedApplicationRule().getDestinationPort().getValue()));
    assertEquals(builder.getApplicationPolicyProtocol(rule.getProtocol()),
        operation.getUserDefinedApplicationRule().getApplicationPolicyProtocol());
    assertEquals(rule.getApplicationId(),
        Integer.valueOf(operation.getUserDefinedApplicationRule().getAppId()));
    assertEquals(rule.getApplicationName(), operation.getUserDefinedApplicationRule().getAppName());
    assertEquals(rule.getTenant().getId(), operation.getUserDefinedApplicationRule().getTenantId());
  }

  private void assertUserDefinedPortOnlyRuleOperation(Action action, ApplicationPolicyRule rule,
      Operation operation) {
    assertEquals(action, operation.getAction());
    assertNotNull(operation.getUserDefinedApplicationRule());
    assertEquals(builder.getUserDefinedType(rule.getPortMapping()),
        operation.getUserDefinedApplicationRule().getType());
    assertFalse(operation.getUserDefinedApplicationRule().hasDestinationIp());
    assertFalse(operation.getUserDefinedApplicationRule().hasNetmask());
    assertEquals(rule.getDestinationPort(),
        Integer.valueOf(operation.getUserDefinedApplicationRule().getDestinationPort().getValue()));
    assertEquals(builder.getApplicationPolicyProtocol(rule.getProtocol()),
        operation.getUserDefinedApplicationRule().getApplicationPolicyProtocol());
    assertEquals(rule.getApplicationId(),
        Integer.valueOf(operation.getUserDefinedApplicationRule().getAppId()));
    assertEquals(rule.getApplicationName(), operation.getUserDefinedApplicationRule().getAppName());
    assertEquals(rule.getTenant().getId(), operation.getUserDefinedApplicationRule().getTenantId());
  }

  private void assertApplicationPolicyRule(Action action, ApplicationPolicyRule rule,
      com.ruckus.acx.ddccm.protobuf.wifi.ApplicationPolicyRule proto) {
    assertEquals(rule.getPriority(), Integer.valueOf(proto.getPriority().getValue()));
    assertEquals(builder.getApplicationRuleType(rule.getAccessControl()),
        proto.getApplicationRuleType());

    if (rule.getRuleType() == ApplicationTypeEnum.SIGNATURE) {
      assertEquals(rule.getCategoryId(), Integer.valueOf(proto.getCategoryId().getValue()));
      assertEquals(rule.getCategory(), proto.getCategoryName().getValue());
    } else {
      assertEquals(Integer.valueOf(32768), Integer.valueOf(proto.getCategoryId().getValue()));
      assertEquals("User Defined", proto.getCategoryName().getValue());
    }

    assertEquals(rule.getApplicationId(), Integer.valueOf(proto.getApplicationId().getValue()));
    assertEquals(rule.getApplicationName(), proto.getApplicationName().getValue());

    if (ApplicationRuleTypeEnum.RATE_LIMIT == rule.getAccessControl()) {
      assertEquals(rule.getUplink(), Integer.valueOf(proto.getUplink().getValue()));
      assertEquals(rule.getDownlink(), Integer.valueOf(proto.getDownlink().getValue()));
    } else {
      assertFalse(proto.hasUplink());
      assertFalse(proto.hasDownlink());
    }

    if (ApplicationRuleTypeEnum.QOS == rule.getAccessControl()) {
      assertEquals(builder.getMarkingPriority(rule.getMarkingPriority()),
          proto.getMarkingPriority());
      assertEquals(builder.getMarkingType(rule.getUpLinkMarkingType()),
          proto.getUplinkMarkingType());
      assertEquals(builder.getMarkingType(rule.getDownLinkMarkingType()),
          proto.getDownlinkMarkingType());
    } else {
      assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.MarkingPriorityEnum.MarkingPriorityEnum_UNSET,
          proto.getMarkingPriority());
      assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.MarkingTypeEnum.MarkingTypeEnum_UNSET,
          proto.getUplinkMarkingType());
      assertEquals(com.ruckus.acx.ddccm.protobuf.wifi.MarkingTypeEnum.MarkingTypeEnum_UNSET,
          proto.getDownlinkMarkingType());
    }
  }

  private static String genUUID() {
    return UUID.randomUUID().toString();
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public DdccmApplicationPolicyOperationBuilder ddccmApplicationPolicyOperationBuilder() {
      DdccmApplicationPolicyOperationBuilder builder = spy(
          DdccmApplicationPolicyOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }

    @Bean
    public ApplicationPolicyRuleRepository mockApplicationPolicyRuleRepository() {
      return Mockito.mock(ApplicationPolicyRuleRepository.class);
    }

    @Bean
    public TenantRepository mockTenantRepository() {
      return Mockito.mock(TenantRepository.class);
    }
  }
}
