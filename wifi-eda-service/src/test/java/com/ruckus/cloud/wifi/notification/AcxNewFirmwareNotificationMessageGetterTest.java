package com.ruckus.cloud.wifi.notification;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.ACX_UI_RE_SKINNING_TOGGLE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.tenantservice.api.GetTenantResponse;
import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.notification.dto.ScheduleTemplateDto;
import com.ruckus.cloud.wifi.notification.dto.VenueTemplateDto;
import com.ruckus.cloud.wifi.notification.template.FirmwareNotificationMessage;
import com.ruckus.cloud.wifi.notification.template.UpdateNotificationTemplate;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
class AcxNewFirmwareNotificationMessageGetterTest {

  @SpyBean
  private AcxNewFirmwareNotificationMessageGetter messageGetter;

  @MockBean
  private VenueTemplateConverter converter;

  @MockBean
  private TenantClient tenantClient;

  @Test
  public void testCreateSchedule() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    List<Venue> venues = Arrays.asList(new Venue());
    VenueTemplateDto testVenueDto = new VenueTemplateDto();
    testVenueDto.setName("venue-1");
    testVenueDto.setTimeZone("zone-1");
    testVenueDto.setNumberOfAps(20);
    testVenueDto.setImpactedNumberOfAps(2);
    Map<String, List<VenueTemplateDto>> venueTemplateMap = Map.of("time-1", List.of(testVenueDto));
    when(converter.groupVenueTemplateByTimeSlot(anyList())).
        thenReturn(venueTemplateMap);
    VenueUpgradeVersionsMapping venueVersionMapping = VenueUpgradeVersionsMapping.create();

    FirmwareNotificationMessage message = messageGetter.createSchedule(tenant.getName(), tenant, venues, venueVersionMapping);

    verify(converter).toTemplate(eq(tenant.getId()), eq(venues), eq(venueVersionMapping));
    verify(converter).groupVenueTemplateByTimeSlot(anyList());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_ACX_NEW, message.getTemplate());
    verifyNewAndReminderScheduleListModel(message, testVenueDto);
    assertThat(message.getModel()).doesNotContainKey("verticalSkinName");
  }

  @Test
  @FeatureFlag(enable = ACX_UI_RE_SKINNING_TOGGLE)
  void testCreateScheduleWithReskinningFFEnabled() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    tenant.setId("test-tenantId");
    List<Venue> venues = Arrays.asList(new Venue());
    VenueTemplateDto testVenueDto = new VenueTemplateDto();
    testVenueDto.setName("venue-1");
    testVenueDto.setTimeZone("zone-1");
    testVenueDto.setNumberOfAps(20);
    testVenueDto.setImpactedNumberOfAps(2);
    Map<String, List<VenueTemplateDto>> venueTemplateMap = Map.of("time-1", List.of(testVenueDto));
    when(converter.groupVenueTemplateByTimeSlot(anyList())).
        thenReturn(venueTemplateMap);
    VenueUpgradeVersionsMapping venueVersionMapping = VenueUpgradeVersionsMapping.create();
    when(tenantClient.getTenantData(anyString(), anyString())).thenReturn(
        GetTenantResponse.newBuilder().setAccountVertical("Hospitality").build());

    FirmwareNotificationMessage message = messageGetter.createSchedule(tenant.getName(), tenant, venues, venueVersionMapping);

    verify(converter).toTemplate(eq(tenant.getId()), eq(venues), eq(venueVersionMapping));
    verify(converter).groupVenueTemplateByTimeSlot(anyList());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_ACX_NEW, message.getTemplate());
    verifyNewAndReminderScheduleListModel(message, testVenueDto);
    assertThat(message.getModel()).hasEntrySatisfying("verticalSkinName",
        v -> assertThat(v).isNotNull().isEqualTo("Space"));
  }

  @Test
  @FeatureFlag(enable = ACX_UI_RE_SKINNING_TOGGLE)
  void testCreateScheduleWithReskinningFFEnabledAndUnexpectedTenantResponse() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    tenant.setId("test-tenantId");
    List<Venue> venues = Arrays.asList(new Venue());
    VenueTemplateDto testVenueDto = new VenueTemplateDto();
    testVenueDto.setName("venue-1");
    testVenueDto.setTimeZone("zone-1");
    testVenueDto.setNumberOfAps(20);
    testVenueDto.setImpactedNumberOfAps(2);
    Map<String, List<VenueTemplateDto>> venueTemplateMap = Map.of("time-1", List.of(testVenueDto));
    when(converter.groupVenueTemplateByTimeSlot(anyList())).
        thenReturn(venueTemplateMap);
    VenueUpgradeVersionsMapping venueVersionMapping = VenueUpgradeVersionsMapping.create();
    when(tenantClient.getTenantData(anyString(), anyString())).thenReturn(
        GetTenantResponse.newBuilder().build());

    FirmwareNotificationMessage message = messageGetter.createSchedule(tenant.getName(), tenant, venues, venueVersionMapping);

    verify(converter).toTemplate(eq(tenant.getId()), eq(venues), eq(venueVersionMapping));
    verify(converter).groupVenueTemplateByTimeSlot(anyList());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_ACX_NEW, message.getTemplate());
    verifyNewAndReminderScheduleListModel(message, testVenueDto);
    assertThat(message.getModel()).doesNotContainKey("verticalSkinName");
  }

  @Test
  public void testChangeSchedule() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    String slotDate = "time-1";
    VenueTemplateDto testVenueDto = new VenueTemplateDto();
    testVenueDto.setName("venue-1");
    testVenueDto.setTimeZone("zone-1");
    testVenueDto.setNumberOfAps(20);
    testVenueDto.setImpactedNumberOfAps(2);
    List<VenueTemplateDto> venueDtos = List.of(testVenueDto);
    ApVersion apVersion = new ApVersion();

    FirmwareNotificationMessage message = messageGetter.changeSchedule(tenant.getName(), tenant, apVersion,
        Map.of(slotDate, venueDtos));

    assertEquals(UpdateNotificationTemplate.CHANGE_SCHEDULE_ACX_NEW, message.getTemplate());
    verifyNewAndReminderScheduleListModel(message, testVenueDto);
  }

  @Test
  public void testScheduleReminder() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    List<Venue> venues = Arrays.asList(new Venue());
    List<ApVersion> versions = List.of(new ApVersion("6.2.1.103.100"));
    String slotDate = "time-1";
    VenueTemplateDto testVenueDto = new VenueTemplateDto();
    testVenueDto.setName("venue-1");
    testVenueDto.setTimeZone("zone-1");
    testVenueDto.setNumberOfAps(20);
    testVenueDto.setImpactedNumberOfAps(2);
    when(converter.toTemplate(eq(tenant.getId()), eq(venues), any()))
        .thenReturn(List.of(testVenueDto));

    FirmwareNotificationMessage message = messageGetter.scheduleReminder(tenant.getName(), tenant, versions,
        slotDate, venues, null);

    assertEquals(UpdateNotificationTemplate.SCHEDULE_REMINDER_ACX_NEW, message.getTemplate());
    verifyNewAndReminderScheduleListModel(message, testVenueDto);
  }

  @Test
  public void testCancelSchedule() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    List<Venue> venues = Arrays.asList(new Venue());
    VenueTemplateDto testVenueDto = new VenueTemplateDto();
    testVenueDto.setName("venue-1");
    testVenueDto.setTimeZone("zone-1");
    testVenueDto.setNumberOfAps(20);
    testVenueDto.setImpactedNumberOfAps(2);
    VenueUpgradeVersionsMapping venueVersionMapping = VenueUpgradeVersionsMapping.create();
    when(converter.toTemplate(tenant.getId(), venues, venueVersionMapping))
        .thenReturn(List.of(testVenueDto));

    FirmwareNotificationMessage message = messageGetter.cancelSchedule(tenant.getName(), tenant, venues, venueVersionMapping, null);

    assertEquals(UpdateNotificationTemplate.UPDATE_CANCEL_ADMIN_ACX_NEW, message.getTemplate());
    assertThat(message.getModel().get("scheduleList"))
        .isNotNull()
        .asList()
        .singleElement()
        .extracting(ScheduleTemplateDto.class::cast)
        .extracting(ScheduleTemplateDto::getVenueDtoList)
        .asList()
        .singleElement()
        .extracting(VenueTemplateDto.class::cast)
        .isEqualTo(testVenueDto);
  }

  @Test
  public void testUpdateSuccess() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    VenueTemplateDto testVenueDto = new VenueTemplateDto();
    testVenueDto.setName("venue-1");
    testVenueDto.setTimeZone("zone-1");
    testVenueDto.setNumberOfAps(20);
    testVenueDto.setImpactedNumberOfAps(2);
    List<VenueTemplateDto> dtos = Arrays.asList(testVenueDto);

    FirmwareNotificationMessage message = messageGetter.updateSuccess(tenant.getName(), tenant, List.of(), dtos);

    assertEquals(UpdateNotificationTemplate.UPDATE_FINISHED_SUCCESSFULLY_ACX_NEW,
        message.getTemplate());
    assertThat(message.getModel().get("scheduleList"))
        .isNotNull()
        .asList()
        .singleElement()
        .extracting(ScheduleTemplateDto.class::cast)
        .extracting(ScheduleTemplateDto::getVenueDtoList)
        .asList()
        .singleElement()
        .extracting(VenueTemplateDto.class::cast)
        .isEqualTo(testVenueDto);
  }

  @Test
  public void updateFail() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    VenueTemplateDto testVenueDto = new VenueTemplateDto();
    testVenueDto.setName("venue-1");
    testVenueDto.setTimeZone("zone-1");
    testVenueDto.setNumberOfAps(20);
    testVenueDto.setImpactedNumberOfAps(2);
    List<VenueTemplateDto> dtos = Arrays.asList(testVenueDto);

    FirmwareNotificationMessage message = messageGetter.updateFail(tenant.getName(), tenant, List.of(), dtos);

    assertEquals(UpdateNotificationTemplate.UPDATE_FINISHED_FAIL_ACX_NEW, message.getTemplate());
    assertThat(message.getModel().get("scheduleList"))
        .isNotNull()
        .asList()
        .singleElement()
        .extracting(ScheduleTemplateDto.class::cast)
        .extracting(ScheduleTemplateDto::getVenueDtoList)
        .asList()
        .singleElement()
        .extracting(VenueTemplateDto.class::cast)
        .isEqualTo(testVenueDto);
  }

  @Test
  public void testOverSizedScheduleOnCreateSchedule() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    List<Venue> venues = Arrays.asList(new Venue());
    List<VenueTemplateDto> dto1 = mockVenueTemplateDtos(50);
    List<VenueTemplateDto> dto2 = mockVenueTemplateDtos(60);
    List<VenueTemplateDto> dto3 = mockVenueTemplateDtos(30);
    Map<String, List<VenueTemplateDto>> venueTemplateMap = Map.of("time-1", dto1,
        "time-2", dto2, "time-3", dto3);
    when(converter.groupVenueTemplateByTimeSlot(anyList())).
        thenReturn(venueTemplateMap);
    VenueUpgradeVersionsMapping venueVersionMapping = VenueUpgradeVersionsMapping.create();

    FirmwareNotificationMessage message = messageGetter.createSchedule(tenant.getName(), tenant, venues, venueVersionMapping);

    verify(converter).toTemplate(eq(tenant.getId()), eq(venues), eq(venueVersionMapping));
    verify(converter).groupVenueTemplateByTimeSlot(anyList());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_ACX_NEW, message.getTemplate());
    assertThat(message.getModel().get("scheduleList"))
        .isNotNull()
        .asList()
        .extracting(ScheduleTemplateDto.class::cast)
        .flatExtracting(ScheduleTemplateDto::getVenueDtoList)
        .hasSize(100);
    assertThat(message.getModel().get("more_venue_size"))
        .isNotNull()
        .isEqualTo(40);
  }

  @Test
  public void testUnderSizedScheduleOnCreateSchedule() {
    Tenant tenant = new Tenant();
    tenant.setName("test-tenant");
    List<Venue> venues = Arrays.asList(new Venue());
    List<VenueTemplateDto> dtos = mockVenueTemplateDtos(30);
    Map<String, List<VenueTemplateDto>> venueTemplateMap = Map.of("time-1", dtos);
    when(converter.groupVenueTemplateByTimeSlot(anyList())).
        thenReturn(venueTemplateMap);
    VenueUpgradeVersionsMapping venueVersionMapping = VenueUpgradeVersionsMapping.create();

    FirmwareNotificationMessage message = messageGetter.createSchedule(tenant.getName(), tenant, venues, venueVersionMapping);

    verify(converter).toTemplate(eq(tenant.getId()), eq(venues), eq(venueVersionMapping));
    verify(converter).groupVenueTemplateByTimeSlot(anyList());
    assertEquals(UpdateNotificationTemplate.CREATE_SCHEDULE_ACX_NEW, message.getTemplate());
    assertThat(message.getModel().get("scheduleList"))
        .isNotNull()
        .asList()
        .singleElement()
        .extracting(ScheduleTemplateDto.class::cast)
        .satisfies(scheduleDto -> assertThat(scheduleDto.getTimeSlot()).isEqualTo("time-1"))
        .extracting(ScheduleTemplateDto::getVenueDtoList)
        .asList()
        .hasSize(30);
    assertThat(message.getModel().get("more_venue_size"))
        .isNull();
  }

  private List<VenueTemplateDto> mockVenueTemplateDtos(int number) {
    VenueTemplateDto testVenueDto = new VenueTemplateDto();
    testVenueDto.setName("venue-1");
    testVenueDto.setTimeZone("zone-1");
    testVenueDto.setNumberOfAps(20);
    testVenueDto.setImpactedNumberOfAps(2);
    List<VenueTemplateDto> dtos = new LinkedList<>();
    for(int i=0; i<number; i++) {
      dtos.add(testVenueDto);
    }
    return dtos;
  }

  private static void verifyNewAndReminderScheduleListModel(FirmwareNotificationMessage message,
      VenueTemplateDto testVenueDto) {
    assertThat(message.getModel().get("scheduleList"))
        .isNotNull()
        .asList()
        .singleElement()
        .extracting(ScheduleTemplateDto.class::cast)
        .satisfies(scheduleDto -> assertThat(scheduleDto.getTimeSlot()).isEqualTo("time-1"))
        .extracting(ScheduleTemplateDto::getVenueDtoList)
        .asList()
        .singleElement()
        .extracting(VenueTemplateDto.class::cast)
        .isEqualTo(testVenueDto);
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    @ConditionalOnMissingBean
    public TenantClient tenantClient() {
      return mock(TenantClient.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public AcxNewFirmwareNotificationMessageGetter acxNewFirmwareNotificationMessageGetter(
        VenueTemplateConverter converter, TenantClient tenantClient, FeatureFlagService featureFlagService) {
      return new AcxNewFirmwareNotificationMessageGetter(converter, tenantClient, featureFlagService);
    }
  }
}