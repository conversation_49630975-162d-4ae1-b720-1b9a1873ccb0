package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.hotspot20FriendlyName;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.txChangesReader;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.wifi.FriendlyName;
import com.ruckus.acx.ddccm.protobuf.wifi.Hotspot20Operator;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Hotspot20FriendlyNameLanguageEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.redis.YamlPropertySourceFactory;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesImpl;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.PropertySource;

@WifiUnitTest
@PropertySource(value = "classpath:application-unit.yml", factory = YamlPropertySourceFactory.class)
public class DdccmHotspot20OperatorOperationBuilderTest {

  @SpyBean
  private DdccmHotspot20OperatorOperationBuilder ddccmHotspot20OperatorOperationBuilder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  public void testAddHotspot20Operator() {

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator hotspot20Operator = Generators.hotspot20Operator().generate();

    List<Operation> operations = ddccmHotspot20OperatorOperationBuilder.build(new NewTxEntity<>(hotspot20Operator),
      emptyTxChanges());

    assertNotNull(operations);
    assertEquals(1, operations.size());

    Hotspot20Operator hotspot20OperatorOperation = operations.get(0).getHotspot20Operator();
    assertNotNull(hotspot20OperatorOperation);
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());

    validateProperties(hotspot20Operator, hotspot20OperatorOperation);
  }

  @Test
  public void testUpdateHotspot20OperatorFriendlyName() {

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator hotspot20Operator = Generators.hotspot20Operator()
      .setFriendlyNames(hotspot20FriendlyName()
        .setLanguage(options(Arrays.stream(Hotspot20FriendlyNameLanguageEnum.values()).collect(
          collectingAndThen(toList(), l-> {
            Collections.shuffle(l);
            return l;
          })).stream().toList()))
        .toListGenerator(Hotspot20FriendlyNameLanguageEnum.values().length))
      .generate();

    TxChanges txChanges = new TxChangesImpl(null);
    hotspot20Operator.getFriendlyNames().stream().forEach(friendlyName -> {
      txChanges.add(friendlyName, Set.of("name"),
        options(EntityAction.ADD, EntityAction.MODIFY, EntityAction.DELETE).setRandom(true).generate());
    });

    List<Operation> operations = ddccmHotspot20OperatorOperationBuilder
      .build(new ModifiedTxEntity<>(hotspot20Operator, Set.of("updatedDate")), txChangesReader(txChanges));

    assertNotNull(operations);
    assertEquals(1, operations.size());

    Hotspot20Operator hotspot20OperatorOperation = operations.get(0).getHotspot20Operator();
    assertNotNull(hotspot20OperatorOperation);
    assertEquals(EntityAction.MODIFY.toString(), operations.get(0).getAction().toString());

    validateProperties(hotspot20Operator, hotspot20OperatorOperation);
  }

  @Test
  public void testUpdateHotspot20Operator() {

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator hotspot20Operator = Generators.hotspot20Operator()
      .setFriendlyNames(hotspot20FriendlyName()
        .setLanguage(options(Arrays.stream(Hotspot20FriendlyNameLanguageEnum.values()).collect(
          collectingAndThen(toList(), l-> {
            Collections.shuffle(l);
            return l;
          })).stream().toList()))
        .toListGenerator(Hotspot20FriendlyNameLanguageEnum.values().length))
      .generate();

    List<Operation> operations = ddccmHotspot20OperatorOperationBuilder
      .build(new ModifiedTxEntity<>(hotspot20Operator, Set.of("domainNames")), emptyTxChanges());

    assertNotNull(operations);
    assertEquals(1, operations.size());

    Hotspot20Operator hotspot20OperatorOperation = operations.get(0).getHotspot20Operator();
    assertNotNull(hotspot20OperatorOperation);
    assertEquals(EntityAction.MODIFY.toString(), operations.get(0).getAction().toString());

    validateProperties(hotspot20Operator, hotspot20OperatorOperation);
  }

  @Test
  public void testDeleteHotspot20Operator() {

    com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator hotspot20Operator = Generators.hotspot20Operator().generate();

    List<Operation> operations = ddccmHotspot20OperatorOperationBuilder
      .build(new DeletedTxEntity<>(hotspot20Operator), emptyTxChanges());

    assertNotNull(operations);
    assertEquals(1, operations.size());

    Hotspot20Operator hotspot20OperatorOperation = operations.get(0).getHotspot20Operator();
    assertNotNull(hotspot20OperatorOperation);
    assertEquals(EntityAction.DELETE.toString(), operations.get(0).getAction().toString());
    assertEquals(hotspot20Operator.getId(), hotspot20OperatorOperation.getId());
    assertEquals(StringUtils.EMPTY, hotspot20OperatorOperation.getName());
    assertEquals(Collections.emptyList(), hotspot20OperatorOperation.getDomainNameList());
    assertEquals(Collections.emptyList(), hotspot20OperatorOperation.getFriendlyNameList());
  }

  private void validateProperties(com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Operator hotspot20Operator,
    Hotspot20Operator hotspot20OperatorOperation) {

    assertEquals(hotspot20Operator.getId(), hotspot20OperatorOperation.getId());
    assertEquals(hotspot20Operator.getName(), hotspot20OperatorOperation.getName());
    assertEquals(hotspot20Operator.getDomainNames(), hotspot20OperatorOperation.getDomainNameList().stream().map(
      StringValue::getValue).toList());

    int friendlyNameSize = hotspot20Operator.getFriendlyNames().size();
    assertEquals(friendlyNameSize, hotspot20OperatorOperation.getFriendlyNameList().size());

    assertThat(hotspot20OperatorOperation.getFriendlyNameList())
      .isNotNull()
      .hasSizeBetween(1, Hotspot20FriendlyNameLanguageEnum.values().length)
      .extracting(FriendlyName::getLanguage)
      .doesNotHaveDuplicates();

    assertThat(hotspot20OperatorOperation.getFriendlyNameList())
      .allSatisfy(fn -> assertThat(fn).matches(friendlyName ->
        hotspot20Operator.getFriendlyNames().stream()
          .filter(srcFn -> com.ruckus.acx.ddccm.protobuf.wifi.FriendlyNameLanguageEnum.forNumber(srcFn.getLanguage().ordinal()+1).equals(friendlyName.getLanguage()))
          .findAny()
          .map(com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20FriendlyName::getName)
          .get()
          .equals(friendlyName.getName())
      ));
  }
}
