package com.ruckus.cloud.wifi.integration.l3aclpolicy;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmFirewallL3AccessControlPolicy;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmFirewallProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeDeactivateL3AclPolicyTemplateOnAccessControlProfileRequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeDeactivateL3AclPolicyOnAccessControlProfileMessage {
    private String accessControlProfileTemplateId;
    private String l3AclPolicyTemplateId;

    @BeforeEach
    void beforeEach(@Template AccessControlProfile accessControlProfile, @Template L3AclPolicy l3AclPolicy) {
      accessControlProfileTemplateId = accessControlProfile.getId();
      l3AclPolicyTemplateId = l3AclPolicy.getId();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("accessControlProfileTemplateId", accessControlProfileTemplateId)
          .addPathVariable("l3AclPolicyTemplateId", l3AclPolicyTemplateId);
    }

    @Test
    void givenAccessControlProfileNotExists(Tenant tenant) {
      final var notExistAccessControlProfileId = randomId();
      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.DEACTIVATE_L3ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE,
                      randomName(),
                      requestParams()
                          .addPathVariable(
                              "accessControlProfileTemplateId", notExistAccessControlProfileId),
                      ""))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(ObjectNotFoundException.class);

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(
              a ->
                  a.getStep()
                      .equals(ApiFlowNames.DEACTIVATE_L3ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE));
    }

    @Nested
    class GivenAccessControlProfileExists {
      @Test
      void givenL3AclPolicyNotActivatedOnAccessControlProfile(Tenant tenant) {
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.DEACTIVATE_L3ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE,
            randomName(),
            requestParams(),
            "");

        messageCaptors.assertThat(
            messageCaptors.getDdccmMessageCaptor(),
            messageCaptors.getCmnCfgCollectorMessageCaptor()
        ).doesNotSendByTenant(tenant.getId());

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(
                a ->
                    a.getStep()
                        .equals(ApiFlowNames.DEACTIVATE_L3ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE));
      }

      @Nested
      class GivenL3AclPolicyAlreadyActivatedOnAccessControlProfile {
        @BeforeEach
        void beforeEach(
            Tenant tenant, AccessControlProfile accessControlProfile, L3AclPolicy l3AclPolicy) {
          accessControlProfile.setL3AclPolicy(l3AclPolicy);
          accessControlProfile.setL3AclEnable(true);
          repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId(), randomTxId());
        }

        @Test
        void thenDeactivatedAndSendingMessages(Tenant tenant) {
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.DEACTIVATE_L3ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE,
              randomName(),
              requestParams(),
              "");

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .hasSize(1)
              .first()
              .matches(o -> o.getAction() == Action.MODIFY)
              .extracting(Operation::getCcmFirewallProfile)
              .extracting(CcmFirewallProfile::getFirewallL3AccessControlPolicy)
              .extracting(CcmFirewallL3AccessControlPolicy::hasL3AccessControlPolicyId)
              .isEqualTo(false);

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .hasSize(1)
              .first()
              .matches(o -> o.getOpType() == OpType.MOD)
              .matches(o -> o.getId().equals(accessControlProfileTemplateId))
              .extracting(
                  Operations::getDocMap, InstanceOfAssertFactories.map(String.class, Value.class))
              .extractingByKey(EsConstants.Key.L3_ACL_POLICY_ID)
              .isNotNull()
              .extracting(Value::getStringValue)
              .isEqualTo("");

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(
                  a ->
                      a.getStep()
                          .equals(ApiFlowNames.DEACTIVATE_L3ACL_POLICY_TEMPLATE_ON_ACCESS_CONTROL_PROFILE));
        }
      }
    }
  }
}
