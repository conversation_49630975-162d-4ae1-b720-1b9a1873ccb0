package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_CLIENT_ISOLATION_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_VENUE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DEACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK_VENUE_TEMPLATE;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.dhcpConfigServiceProfile;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.test.fixture.ClientIsolationTestFixture.clientIsolationAllowlist;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture.venueDhcpConfigServiceProfileSettings;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.getGuestNetwork;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static com.ruckus.cloud.wifi.test.util.TemplateRetriever.retrieveTemplate;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueDhcpServiceSetting;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.client.viewmodel.ViewmodelClientGrpc;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApStateEnum;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApSubStateEnum;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ConnectedClientDTO;
import com.ruckus.cloud.wifi.eda.api.rest.DhcpConfigServiceProfileRestCtrl.DhcpConfigServiceProfileMapper;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApDhcpRoleEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpWanPortSelectionModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DhcpConfigServiceProfileGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpPoolUsage;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpPoolV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpServiceAp;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsage;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.impl.ExtendedDhcpServiceProfileVenueServiceCtrlImpl.DefaultGuestNetworkConfig;
import com.ruckus.cloud.wifi.service.pubsub.DhcpPoolStatsService;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.TemplateRetriever;
import com.ruckuswireless.scg.protobuf.DnbApDhcpPools;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("DhcpConfigServiceProfileV1_1Test")
@WifiIntegrationTest
class ConsumeDhcpConfigServiceProfileTemplateRequestV1_1Test extends AbstractRequestTest {

  private final String DEFAULT_VERSION = "6.2.0.103.1";

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private ViewmodelClientGrpc viewmodelClientGrpc;
  @Autowired
  protected DhcpPoolStatsService dhcpPoolStatsService;
  @Autowired
  private DefaultGuestNetworkConfig defaultGuestNetworkConfig;
  @Autowired
  private MessageCaptors messageCaptors;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Nested
  @ApiAction(CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1)
  class ConsumeAddDhcpConfigServiceProfileV1_1RequestTest {

    @Payload
    private final DhcpConfigServiceProfileGenerator generator = dhcpConfigServiceProfile();

    @Payload("defaultProfile")
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile defaultProfile() {
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile payload
          = dhcpConfigServiceProfile().generate();
      payload.setServiceName("DHCP-Guest");
      return payload;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, payload.getId());
      assertNotNull(dhcpConfigServiceProfile);

      validateRepositoryDataV2(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
          dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
              .collect(Collectors.toList())), dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1)
  class ConsumeUpdateDhcpConfigServiceProfileV1_1RequestTest {

    private String id;
    private DhcpConfigServiceProfile savedDhcpConfigServiceProfile;

    @BeforeEach
    void givenOneRowPersistedInDb(@Template DhcpConfigServiceProfile dhcpConfigServiceProfile) {
      id = dhcpConfigServiceProfile.getId();
      this.savedDhcpConfigServiceProfile = dhcpConfigServiceProfile;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("dhcpConfigServiceProfileId", id);
    }

    @Payload
    private final DhcpConfigServiceProfileGenerator generator = Generators.dhcpConfigServiceProfile();

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, id);
      assertNotNull(dhcpConfigServiceProfile);
      payload.setId(id);

      validateRepositoryDataV2(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
          dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
              .collect(Collectors.toList()), Action.DELETE,
          savedDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
              .collect(Collectors.toList())), dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ApiFlowNames.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }

    @Payload("onlyModifyDhcpPools")
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile onlyModifyDhcpPoolsViewModel() {
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile =
          DhcpConfigServiceProfileMapper.INSTANCE.ServiceDhcpConfigServiceProfile2DhcpConfigServiceProfile(
              savedDhcpConfigServiceProfile);
      dhcpConfigServiceProfile.getDhcpPools().get(0).setName("name1");
      return dhcpConfigServiceProfile;
    }

    @Payload("renameAllDhcpPools")
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile renameAllDhcpPoolsViewModel() {
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile =
          DhcpConfigServiceProfileMapper.INSTANCE.ServiceDhcpConfigServiceProfile2DhcpConfigServiceProfile(
              savedDhcpConfigServiceProfile);
      dhcpConfigServiceProfile.getDhcpPools()
          .forEach(dhcpPoolV1_1 -> dhcpPoolV1_1.setName(dhcpPoolV1_1.getName() + "#new"));
      return dhcpConfigServiceProfile;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1, payload = @Payload("onlyModifyDhcpPools"))
    void thenShouldHandleTheRequestSuccessfully_onlyModifyDhcpPools(
        @Payload("onlyModifyDhcpPools") com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, id);
      assertNotNull(dhcpConfigServiceProfile);
      payload.setId(id);

      validateRepositoryDataV2(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
              dhcpConfigServiceProfile.getDhcpPools().stream()
                  .filter(dhcpPool -> dhcpPool.getName().equals("name1")).map(DhcpServiceProfile::getId)
                  .collect(Collectors.toList()), Action.DELETE,
              List.of(savedDhcpConfigServiceProfile.getDhcpPools().get(0).getId())),
          dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ApiFlowNames.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1, payload = @Payload("renameAllDhcpPools"))
    void thenShouldHandleTheRequestSuccessfully_renameAllDhcpPools(
        @Payload("renameAllDhcpPools") com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, id);
      assertNotNull(dhcpConfigServiceProfile);
      payload.setId(id);

      validateRepositoryDataV2(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
              dhcpConfigServiceProfile.getDhcpPools().stream()
                  .filter(dhcpPool -> dhcpPool.getName().contains("#new"))
                  .map(DhcpServiceProfile::getId)
                  .collect(Collectors.toList()), Action.DELETE,
              savedDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
                  .toList()),
          dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ApiFlowNames.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1)
  class ConsumeDeleteDhcpConfigServiceProfileRequestTest {

    private String id;
    private List<String> dhcpPoolIds;

    @BeforeEach
    void givenOneRowPersistedInDb(@Template DhcpConfigServiceProfile dhcpConfigServiceProfile) {
      id = dhcpConfigServiceProfile.getId();
      dhcpPoolIds = dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
          .collect(Collectors.toList());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("dhcpConfigServiceProfileId", id);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      assertNull(repositoryUtil.find(DhcpConfigServiceProfile.class, id));
      validateDdccmCfgRequestMessages(Map.of(Action.DELETE, dhcpPoolIds), null);
      validateCmnCfgCollectorMessages(CfgAction.DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1, List.of(id),
          null);
      validateActivityMessages(DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS)
  class ConsumeActivateDhcpConfigServiceProfileAndUpdateSettingsRequestTest {

    private String id;
    private DhcpConfigServiceProfile savedDhcpConfigServiceProfile;
    private String venueId;
    private Venue savedVenue;

    @BeforeEach
    void givenOneRowPersistedInDb(
        @Template DhcpConfigServiceProfile dhcpConfigServiceProfile,
        @Template Venue venue) {
      id = dhcpConfigServiceProfile.getId();
      this.savedDhcpConfigServiceProfile = dhcpConfigServiceProfile;
      venueId = venue.getId();
      savedVenue = venue;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("dhcpConfigServiceProfileId", id).
          addPathVariable("venueTemplateId", venueId);
    }

    @Payload
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings() {
      VenueDhcpConfigServiceProfileSettings settings = Generators
          .venueDhcpConfigServiceProfileSettings().generate();

      settings.setActiveDhcpPoolNames(savedDhcpConfigServiceProfile.getDhcpPools().stream()
          .map(DhcpServiceProfile::getName).toList());
      return settings;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload VenueDhcpConfigServiceProfileSettings payload) {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      assertNotNull(venue);
      assertEquals(savedDhcpConfigServiceProfile.getDhcpPools().size(),
          venue.getDhcpPoolVenues().size());

      validateVenueRepositoryData(payload, venue);
      validateActivityMessages(
          ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS);
    }

    @Payload("deactivate1Pool")
    VenueDhcpConfigServiceProfileSettings deactivate1Pools() {
      VenueDhcpConfigServiceProfileSettings settings = Generators
          .venueDhcpConfigServiceProfileSettings().generate();
      settings.setWanPortSelectionMode(DhcpWanPortSelectionModeEnum.Manual);
      settings.setActiveDhcpPoolNames(savedDhcpConfigServiceProfile.getDhcpPools().stream()
          .map(DhcpServiceProfile::getName).limit(2).toList());
      return settings;
    }

    @Test
    @ApiAction(value = CfgAction.ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        payload = @Payload("deactivate1Pool"))
    void thenShouldHandleTheRequestSuccessfully_deactivate1Pool(
        @Payload("deactivate1Pool") VenueDhcpConfigServiceProfileSettings payload) {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      assertNotNull(venue);
      assertEquals(2, venue.getDhcpPoolVenues().size());

      validateVenueRepositoryData(payload, venue);
      validateActivityMessages(
          ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS);
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE)
  class ConsumeDeactivateDhcpConfigServiceProfileRequestTest {

    private String id;
    private DhcpConfigServiceProfile savedDhcpConfigServiceProfile;
    private String venueId;
    private Venue savedVenue;

    @BeforeEach
    void givenOneRowPersistedInDb(
        @Template DhcpConfigServiceProfile dhcpConfigServiceProfile,
        @Template Venue venue)
        throws Exception {
      id = dhcpConfigServiceProfile.getId();
      this.savedDhcpConfigServiceProfile = dhcpConfigServiceProfile;
      venueId = venue.getId();
      savedVenue = venue;
      VenueDhcpConfigServiceProfileSettings settings = Generators.venueDhcpConfigServiceProfileSettings()
          .generate();
      settings.setActiveDhcpPoolNames(dhcpConfigServiceProfile.getDhcpPools().stream()
          .map(DhcpServiceProfile::getName).toList());
      dhcpConfigServiceProfileTemplateServiceCtrl
          .activateDhcpConfigServiceProfileTemplateAndUpdateSettings(venueId, id,
              DhcpConfigServiceProfileMapper.INSTANCE
                  .VenueDhcpConfigServiceProfileSettings2ServiceVenue(settings));
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("dhcpConfigServiceProfileId", id).
          addPathVariable("venueTemplateId", venueId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      final Venue venue = repositoryUtil.find(Venue.class, venueId);
      assertNotNull(venue);
      assertEquals(0, venue.getDhcpPoolVenues().size());

      validateActivityMessages(DEACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE);
    }
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction,
      List<String> dhcpConfigServiceProfileIds, DhcpConfigServiceProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(dhcpConfigServiceProfileIds.size())
            .allMatch(op -> dhcpConfigServiceProfileIds.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction)).allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op).extracting(Operations::getDocMap).matches(
                        doc -> dhcpConfigServiceProfileIds.contains(
                            doc.get(Key.ID).getStringValue())).matches(
                        doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                    .matches(
                        doc -> payload.getServiceName().equals(doc.get(Key.NAME).getStringValue()))
                    .matches(doc -> payload.getDhcpPools().size() == (doc.get(Key.DHCP_POOLS)
                        .getListValue().getValuesCount()))
                    .matches(doc -> 0 == doc.get(Key.VENUE_IDS).getListValue().getValuesCount())
                    .matches(doc -> true == doc.get(Key.IS_TEMPLATE).getBoolValue());
              }
            }));
  }

  private void validateDdccmCfgRequestMessages(Map<Action, List<String>> actionIdMap,
      DhcpConfigServiceProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(Operation.class::cast)
            .filteredOn(Operation::hasDhcpServiceProfile)
            .hasSize(actionIdMap.values().stream().mapToInt(List::size).sum()).satisfies(ops -> {
              assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                      Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
              if (actionIdMap.containsKey(Action.DELETE)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.DELETE)
                    .hasSize(actionIdMap.get(Action.DELETE).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.DELETE)
                                .contains(dhcpServiceProfile.getId())));
              }
              if (actionIdMap.containsKey(Action.ADD)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.ADD)
                    .hasSize(actionIdMap.get(Action.ADD).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.ADD)
                                .contains(dhcpServiceProfile.getId())).matches(
                                dhcpServiceProfile -> assertDdccmDetail(dhcpServiceProfile, payload)));
              }
              if (actionIdMap.containsKey(Action.MODIFY)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.MODIFY)
                    .hasSize(actionIdMap.get(Action.MODIFY).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.MODIFY)
                                .contains(dhcpServiceProfile.getId())).matches(
                                dhcpServiceProfile -> actionIdMap.get(Action.MODIFY)
                                    .contains(dhcpServiceProfile.getId())).matches(
                                dhcpServiceProfile -> assertDdccmDetail(dhcpServiceProfile, payload)));
              }
            }));
  }

  private boolean assertDdccmDetail(
      com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile dhcpServiceProfile,
      DhcpConfigServiceProfile payload) {
    DhcpServiceProfile dhcpPool = payload.getDhcpPools().stream()
        .filter(p -> p.getId().equals(dhcpServiceProfile.getId())).findFirst().get();
    assertEquals(dhcpPool.getName(), dhcpServiceProfile.getName());
    assertEquals(Integer.valueOf(dhcpPool.getVlanId()), dhcpServiceProfile.getVlanId());
    assertEquals(dhcpPool.getSubnetAddress(), dhcpServiceProfile.getSubnetAddress());
    assertEquals(dhcpPool.getSubnetMask(), dhcpServiceProfile.getSubnetMask());
    assertEquals(dhcpPool.getStartIpAddress(), dhcpServiceProfile.getStartIpAddress());
    assertEquals(dhcpPool.getEndIpAddress(), dhcpServiceProfile.getEndIpAddress());
    return true;
  }

  private void validateRepositoryDataV2(
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile expected,
      DhcpConfigServiceProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getServiceName(), actual.getServiceName());
    assertEquals(expected.getDhcpMode(), actual.getDhcpMode());
    assertEquals(expected.getDhcpPools().size(), actual.getDhcpPools().size());

    for (DhcpPoolV1_1 expectedPoolV2 : expected.getDhcpPools()) {
      DhcpServiceProfile actualPool = actual.getDhcpPools().stream()
          .filter(pool -> pool.getName().equals(expectedPoolV2.getName())).findFirst().orElse(null);
      assertNotNull(actualPool);
      assertEquals(expectedPoolV2.getName(), actualPool.getName());
      assertEquals(expectedPoolV2.getDescription(), actualPool.getDescription());
      assertEquals(expectedPoolV2.getVlanId(), actualPool.getVlanId());
      assertEquals(expectedPoolV2.getSubnetAddress(), actualPool.getSubnetAddress());
      assertEquals(expectedPoolV2.getSubnetMask(), actualPool.getSubnetMask());
      assertEquals(expectedPoolV2.getPrimaryDnsIp(), actualPool.getPrimaryDnsIp());
      assertEquals(expectedPoolV2.getSecondaryDnsIp(), actualPool.getSecondaryDnsIp());
      assertEquals(expectedPoolV2.getLeaseTimeHours(), actualPool.getLeaseTimeHours());
      assertEquals(expectedPoolV2.getLeaseTimeMinutes(), actualPool.getLeaseTimeMinutes());
      assertEquals(expectedPoolV2.getStartIpAddress(), actualPool.getStartIpAddress());
      assertEquals(expectedPoolV2.getEndIpAddress(), actualPool.getEndIpAddress());
    }
  }

  private void validateVenueRepositoryData(VenueDhcpConfigServiceProfileSettings expected,
      Venue actual) {
    assertEquals(expected.getWanPortSelectionMode(),
        actual.getDhcpServiceSetting().getWanPortSelectionMode());
//    assertEquals(expected.getDhcpServiceAps().size(), actual.getDhcpServiceAps().size());
    assertEquals(expected.getActiveDhcpPoolNames().size(), actual.getDhcpPoolVenues().size());
  }

  private void validateNothingHappened(CfgAction action) {
    final TxChanges txChanges = revisionService.changes(txCtxExtension.getRequestId(),
        txCtxExtension.getTenantId(), action.key());
    assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
    messageCaptors.assertThat(
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest(),
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(txCtxExtension.getTenantId());
  }

  private void validateActivityMessages(String apiFlowNames) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> assertThat(
        activityCfgChangeRespMessage.getPayload()).matches(
            msg -> msg.getStatus().equals(Status.OK)).matches(msg -> msg.getStep().equals(apiFlowNames))
        .extracting(ConfigurationStatus::getEventDate).isNotNull());

    if (apiFlowNames.equals(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS)
        || apiFlowNames.equals(ApiFlowNames.DEACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE)) {
      return;
    }
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1 -> OpType.ADD;
      case UPDATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1 -> OpType.MOD;
      case DELETE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1 -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettings(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // When - enable venue dhcp setting
    // enable profile with 3 dhcpPools & 4 dhcpAps
    var poolsAmount = 3;
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfileViewModel
        = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(poolsAmount, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, dhcpConfigServiceProfileViewModel);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile
        = getDhcpConfigServiceProfileTemplateViewModels().get(0);
    List<String> activePoolNames = dhcpConfigServiceProfile.getDhcpPools().stream()
        .map(DhcpPoolV1_1::getName).toList();
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps, activePoolNames);
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // assert result - getVenueDhcpConfigServiceProfileTemplateSettings
    VenueDhcpConfigServiceProfileSettings result = getVenueDhcpConfigServiceProfileTemplateSettings(
        venueId, dhcpConfigServiceProfile.getId());
    assertVenueDhcpConfigServiceProfileSettings(result, true, activePoolNames, dhcpAps,
        true, poolsAmount);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    {
      com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
          = getVenueTemplateWifiDhcpPoolUsages(venueId);
      assertEquals(poolsAmount, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());
    }
    // assert result - getDhcpConfigServiceProfiles
    final com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile finalConfigServiceProfile
        = getDhcpConfigServiceProfileTemplateViewModels().get(0);
    assertDhcpConfigServiceProfile(finalConfigServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPoolV1_1#1", "DhcpPoolV1_1#2", "DhcpPoolV1_1#3"));

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalConfigServiceProfile.getDhcpPools(),
        finalConfigServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), finalConfigServiceProfile.getDhcpPools(),
            result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    // expected invoke 1 dhcpConfigServiceProfile
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId,
            true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD,
            finalConfigServiceProfile, List.of(venueId))
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndBindToAnotherEachApProfile(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venue dhcp setting
    // 3 dhcpPools & 4 dhcpAps in payload
    String dhcpServiceProfileId;
    {
      edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, DhcpConfigServiceProfileTestFixture
          .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
      assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile
          = getDhcpConfigServiceProfileTemplateViewModels().get(0);
      dhcpServiceProfileId = dhcpConfigServiceProfile.getId();
      VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
          dhcpAps,
          dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
      edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
          dhcpServiceProfileId,
          venueDhcpServiceProfileSettings);
      assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
          tenantId);
    }

    // When - venue bind To Another Profile
    String anotherDhcpServiceProfileId;
    {
      edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, DhcpConfigServiceProfileTestFixture
          .dhcpConfigServiceProfileViewModel(2, 2, DhcpModeEnum.EnableOnEachAPs));
      assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile anotherDhcpConfigServiceProfile
          = getDhcpConfigServiceProfileTemplateViewModels().stream()
              .filter(e -> !e.getId().equals(dhcpServiceProfileId))
              .findFirst().orElseThrow();
      List<String> activePoolNames = anotherDhcpConfigServiceProfile.getDhcpPools().stream()
          .map(DhcpPoolV1_1::getName).toList();
      VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
          null, activePoolNames);
      edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
          anotherDhcpConfigServiceProfile.getId(),
          venueDhcpServiceProfileSettings);
      assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
          tenantId);

      // assert result - getVenueDhcpConfigServiceProfileTemplateSettings
      VenueDhcpConfigServiceProfileSettings result = getVenueDhcpConfigServiceProfileTemplateSettings(
          venueId, anotherDhcpConfigServiceProfile.getId());
      assertVenueDhcpConfigServiceProfileSettings(result, true, activePoolNames, null,
          false, 0);
    }

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(2, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert result - getDhcpConfigServiceProfiles
    List<com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile> viewModels =
        getDhcpConfigServiceProfileTemplateViewModels();
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile finalDhcpServiceProfile = viewModels
        .stream().filter(e -> e.getServiceName().equals("DhcpConfigServiceProfile#1")).findFirst().orElseThrow();
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile finalAnotherDhcpServiceProfile = viewModels
        .stream().filter(e -> e.getServiceName().equals("DhcpConfigServiceProfile#2")).findFirst().orElseThrow();
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPoolV1_1#1", "DhcpPoolV1_1#2", "DhcpPoolV1_1#3"));
    assertDhcpConfigServiceProfile(finalAnotherDhcpServiceProfile, "DhcpConfigServiceProfile#2",
        DhcpModeEnum.EnableOnEachAPs, List.of("DhcpPoolV1_1#1", "DhcpPoolV1_1#2"));

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalAnotherDhcpServiceProfile.getDhcpPools(),
        finalAnotherDhcpServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), finalAnotherDhcpServiceProfile.getDhcpPools(),
            null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 3),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId,
            true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD,
            finalAnotherDhcpServiceProfile, List.of(venueId)),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD,
            finalDhcpServiceProfile, null)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndBindToAnotherHierarchicalProfile(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);
    List<DhcpServiceAp> anotherDhcpAps = dhcpService2ApList(apGroup);

    // enable venue dhcp setting
    // 3 dhcpPools & 4 dhcpAps in payload
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile
        = getDhcpConfigServiceProfileTemplateViewModels().get(0);
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps,
        dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfile.getId(),
        venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - venue bind To Another Profile
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile hierarchical = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(2, 2, DhcpModeEnum.EnableOnHierarchicalAPs);
    hierarchical.getDhcpPools().get(0).setVlanId((short) 1);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, hierarchical);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile anotherDhcpConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().stream()
            .filter(e -> !e.getId().equals(dhcpConfigServiceProfile.getId()))
            .findFirst().orElseThrow();
    List<String> activePoolNames = anotherDhcpConfigServiceProfile.getDhcpPools().stream()
        .map(DhcpPoolV1_1::getName).toList();
    venueDhcpServiceProfileSettings.setDhcpServiceAps(anotherDhcpAps);
    venueDhcpServiceProfileSettings.setActiveDhcpPoolNames(activePoolNames);
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        anotherDhcpConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // assert result - getVenueDhcpConfigServiceProfileTemplateSettings

    VenueDhcpConfigServiceProfileSettings result = getVenueDhcpConfigServiceProfileTemplateSettings(
        venueId, anotherDhcpConfigServiceProfile.getId());
    assertVenueDhcpConfigServiceProfileSettings(result, true, activePoolNames, anotherDhcpAps,
        false, 0);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(2, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert result - getDhcpConfigServiceProfiles
    List<com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile> deeps =
        getDhcpConfigServiceProfileTemplateViewModels();
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile finalDhcpServiceProfile = deeps
        .stream().filter(e -> e.getServiceName().equals("DhcpConfigServiceProfile#1")).findFirst().orElseThrow();
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile finalAnotherDhcpServiceProfile = deeps
        .stream().filter(e -> e.getServiceName().equals("DhcpConfigServiceProfile#2")).findFirst().orElseThrow();
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPoolV1_1#1", "DhcpPoolV1_1#2", "DhcpPoolV1_1#3"));
    assertNull(
        getDhcpConfigServiceProfileTemplate(dhcpConfigServiceProfile.getId()).getDhcpPoolVenueUsage());
    assertDhcpConfigServiceProfile(finalAnotherDhcpServiceProfile, "DhcpConfigServiceProfile#2",
        DhcpModeEnum.EnableOnHierarchicalAPs, List.of("DhcpPoolV1_1#1", "DhcpPoolV1_1#2"));

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalAnotherDhcpServiceProfile.getDhcpPools(),
        finalAnotherDhcpServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnHierarchicalAPs.toString(),
            finalAnotherDhcpServiceProfile.getDhcpPools(), result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 3),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId,
            true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD,
            finalAnotherDhcpServiceProfile, List.of(venueId)),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD,
            finalDhcpServiceProfile, null)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndDisableDhcpSettings(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfileSettings
    // 3 dhcpPools & 4 dhcpAps in payload
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps,
        dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - disable venue dhcp setting
    edaDeactivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfile.getId());
    assertActivityStatusSuccess(DEACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);

    // assert result - getVenueDhcpConfigServiceProfileTemplateSettings
    VenueDhcpConfigServiceProfileSettings result = getVenueDhcpConfigServiceProfileTemplateSettings(
        venueId, dhcpConfigServiceProfile.getId());
    venueDhcpServiceProfileSettings.setDhcpServiceAps(null);
    assertVenueDhcpConfigServiceProfileSettings(result, false, null, null,
        false, 0);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(0, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert result - getDhcpConfigServiceProfiles
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile finalDhcpServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPoolV1_1#1", "DhcpPoolV1_1#2", "DhcpPoolV1_1#3"));

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, null, null);

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, false,
            null, null, null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId,
            false),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD,
            finalDhcpServiceProfile, null)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndUpdateSameSettings(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfileSettings
    // 3 dhcpPools & 4 dhcpAps in payload
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    List<String> activePoolNames = dhcpConfigServiceProfile.getDhcpPools().stream()
        .map(DhcpPoolV1_1::getName).toList();
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps, activePoolNames);
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - update the same venue dhcp setting
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // assert result - getVenueDhcpConfigServiceProfileTemplateSettings
    VenueDhcpConfigServiceProfileSettings result = getVenueDhcpConfigServiceProfileTemplateSettings(
        venueId, dhcpConfigServiceProfile.getId());
    assertVenueDhcpConfigServiceProfileSettings(result, true, activePoolNames, dhcpAps,
        true, 3);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(3, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert result - getDhcpConfigServiceProfiles
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile finalDhcpServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPoolV1_1#1", "DhcpPoolV1_1#2", "DhcpPoolV1_1#3"));

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalDhcpServiceProfile.getDhcpPools(),
        finalDhcpServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile finalDhcpServiceProfile1 = dhcpConfigServiceProfile;
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), finalDhcpServiceProfile1.getDhcpPools(),
            result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    assertCmnCfgCollectorCfgRequestNotSent(tenantId);
//    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
//    assertAll("assert viewmodel collector",
//        () -> assertCmnViewModelCollector(viewmodelOperations, 1),
//        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalDhcpServiceProfile, List.of(venueId))
//    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_dhcpApOverMaxAmount_fail(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);
    dhcpAps.addAll(dhcpService4ApList(apGroup));
    dhcpAps.addAll(dhcpService4ApList(apGroup));

    // When - EnableOnEachAPs over max amount fail
    // 3 dhcpPools & 12 dhcpAps in payload
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnEachAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels()
        .stream().filter(e -> e.getServiceName().equals("DhcpConfigServiceProfile#1"))
        .findFirst().orElseThrow();
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps,
        dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);

    // Then - EnableOnEachAPs over max amount fail
    assertActivityStatusFail(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        Errors.WIFI_10155, tenantId);

    // When - EnableOnMultipleAPs over max amount fail
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(2, 2, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile secondDhcpConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels()
        .stream().filter(e -> e.getServiceName().equals("DhcpConfigServiceProfile#2"))
        .findFirst().orElseThrow();
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps,
        secondDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        secondDhcpConfigServiceProfile.getId(), venueDhcpConfigServiceProfileSettings);

    // Then - EnableOnMultipleAPs over max amount fail
    assertActivityStatusFail(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        Errors.WIFI_10155, tenantId);
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_EnableDhcpSettingsWithNetworkVlanNotInclude_fail(
      Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService2ApList(apGroup);

    // add network bind venue
    com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork networkDeepReq = pskNetwork("psk").generate();
    networkDeepReq.getWlan().setVlanId((short) 5);
    PskNetwork psk = addPskNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, psk.getId(), v.getId(), null);

    // When - enable venue dhcp setting
    // add dhcpConfigServiceProfile - EnableOnHierarchicalAPs
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile hierarchical = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnHierarchicalAPs);
    hierarchical.getDhcpPools().get(0).setVlanId((short) 1);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, hierarchical);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    List<com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile> dhcpConfigServiceProfiles =
        getDhcpConfigServiceProfileTemplateViewModels();
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile customDhcpConfigServiceProfile =
        dhcpConfigServiceProfiles.get(0);

    // enable venueDhcpConfigServiceProfileSettings
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps,
        customDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        customDhcpConfigServiceProfile.getId(),
        venueDhcpConfigServiceProfileSettings);

    // Then
    // assert activity
    assertActivityStatusFail(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        Errors.WIFI_10169, tenantId);
  }

  @Test
  void testDeactivateVenueDhcpPool_ActivateVenueDhcpPool(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfile
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile savedDhcpConfigServiceProfile
        = getDhcpConfigServiceProfileTemplateViewModels().get(0);
    String dhcpConfigServiceProfileId = savedDhcpConfigServiceProfile.getId();
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps,
        savedDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfileId, venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - deactivate 1 dhcpPool
    final com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile finalConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModel(dhcpConfigServiceProfileId);
    DhcpPoolV1_1 deactivatePoolV2 = finalConfigServiceProfile.getDhcpPools().get(2);
    List<String> activePoolNames = savedDhcpConfigServiceProfile.getDhcpPools().stream()
        .map(DhcpPoolV1_1::getName)
        .filter(name -> !name.equals(deactivatePoolV2.getName())).toList();
    VenueDhcpConfigServiceProfileSettings deactivateOnePoolSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps, activePoolNames);
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfileId, deactivateOnePoolSettings);

    // Then - deactivate 1 dhcpPool
    // assert activity
    assertActivityStatusSuccess(
        ApiFlowNames.ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // assert result - getVenueDhcpConfigServiceProfileTemplateSettings
    VenueDhcpConfigServiceProfileSettings result = getVenueDhcpConfigServiceProfileTemplateSettings(
        venueId, dhcpConfigServiceProfileId);
    assertVenueDhcpConfigServiceProfileSettings(result, true, activePoolNames, dhcpAps,
        true, 2);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(2, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert result - getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    List<DhcpPoolV1_1> activeDhcpPools = finalConfigServiceProfile.getDhcpPools().stream()
        .filter(dhcpPoolV1_1 -> dhcpPoolV1_1 != deactivatePoolV2).collect(Collectors.toList());
    assertDhcpPoolUsage(poolUsages, finalConfigServiceProfile.getDhcpPools(), activeDhcpPools);

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), activeDhcpPools,
            result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    assertCmnCfgCollectorCfgRequestNotSent(tenantId);

    // When - activate 1 dhcpPool
    VenueDhcpConfigServiceProfileSettings activateOnePoolSettings =
        venueDhcpConfigServiceProfileSettings(
            dhcpAps, savedDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName)
                .toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId,
        dhcpConfigServiceProfileId, activateOnePoolSettings);

    // Then - activate 1 dhcpPool
    // assert activity
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);
    // assert result - getVenueTemplateDhcpPoolUsage
    poolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalConfigServiceProfile.getDhcpPools(),
        finalConfigServiceProfile.getDhcpPools());
    VenueDhcpConfigServiceProfileSettings activatedResult = getVenueDhcpConfigServiceProfileTemplateSettings(
        venueId, dhcpConfigServiceProfileId);

    // assert ddccm
    var activateDdccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(activateDdccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(activateDdccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), finalConfigServiceProfile.getDhcpPools(),
            activatedResult.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    assertCmnCfgCollectorCfgRequestNotSent(tenantId);
  }

  @Test
  void testDeactivateVenueDhcpPool_deactivateLastDhcpPool_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfile
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile savedDhcpConfigServiceProfile
        = getDhcpConfigServiceProfileTemplateViewModels().get(0);
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps,
        savedDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedDhcpConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - deactivate last dhcpPool
    VenueDhcpConfigServiceProfileSettings deactivatePoolSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps, Collections.emptyList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedDhcpConfigServiceProfile.getId(), deactivatePoolSettings);

    // Then - deactivate last dhcpPool should disable venue dhcp setting
    // assert activity
    assertActivityStatusFail(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        Errors.WIFI_10423, tenantId);

    // assert ddccm, cmn-cfg-collector
    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());
  }

  @Test
  void testActivateVenueDhcpPool_deactivateVenuePool_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);

    // When - activate 1 dhcpPool
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile savedConfigServiceProfile
        = getDhcpConfigServiceProfileTemplateViewModels().get(0);
    VenueDhcpConfigServiceProfileSettings activatePoolSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps, configServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedConfigServiceProfile.getId(), activatePoolSettings);

    // Then - activate 1 dhcpPool
    // assert activity
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - deactivate 1 dhcpPool
    VenueDhcpConfigServiceProfileSettings deactivatePoolSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps, Collections.emptyList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedConfigServiceProfile.getId(), deactivatePoolSettings);

    // Then - deactivate 1 dhcpPool
    // assert activity
    assertActivityStatusFail(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        Errors.WIFI_10423, tenantId);
  }

  @Test
  void testActivateVenueDhcpPool_activatePoolIdNotExistInDhcpConfigServiceProfile_fail(
      Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // update venueDhcpConfigServiceProfile
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile savedConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings =
        venueDhcpConfigServiceProfileSettings(dhcpAps,
            savedConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - activate 1 dhcpPool
//    activateEdaDhcpPool(tenantId, userName, venueId, "otherDhcpPoolId");
    List<String> otherPoolList = savedConfigServiceProfile.getDhcpPools().stream()
        .map(DhcpPoolV1_1::getName)
        .collect(Collectors.toCollection(ArrayList::new));
    otherPoolList.add("otherPoolName");
    VenueDhcpConfigServiceProfileSettings addOtherPoolSettings =
        venueDhcpConfigServiceProfileSettings(dhcpAps, otherPoolList);
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedConfigServiceProfile.getId(), addOtherPoolSettings);

    // Then - activate 1 dhcpPool
    // assert activity
    assertActivityStatusFail(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        Errors.WIFI_10420, tenantId);
  }

  @Test
  void testActivateVenueZeroDhcpPool_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // update venueDhcpConfigServiceProfile
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(0, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile savedConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings =
        venueDhcpConfigServiceProfileSettings(dhcpAps,
            savedConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);

    // Then - zero dhcpPools
    // assert activity
    assertActivityStatusFail(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        Errors.WIFI_10423, tenantId);
  }

  @Test
  void testActivateVenueDhcpPool_removeAllPools_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // update venueDhcpConfigServiceProfile
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile savedConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings =
        venueDhcpConfigServiceProfileSettings(dhcpAps,
            savedConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - remove all dhcpPools
    VenueDhcpConfigServiceProfileSettings removeAllPoolSettings =
        venueDhcpConfigServiceProfileSettings(dhcpAps, Collections.emptyList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedConfigServiceProfile.getId(), removeAllPoolSettings);

    // Then - remove all dhcpPools
    // assert activity
    assertActivityStatusFail(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        Errors.WIFI_10423, tenantId);
  }

  @Test
  void testGetVenueDhcpPoolUsageWithIpCount(Tenant tenant) throws Exception {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService2ApList(apGroup);

    // enable venueDhcpConfigServiceProfileSettings
    // 4 dhcpPools & 2 dhcpAps in payload
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(4, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile savedConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings =
        venueDhcpConfigServiceProfileSettings(dhcpAps,
            savedConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , savedConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // get dhcpConfigServiceProfile
    configServiceProfile = getDhcpConfigServiceProfileTemplateViewModel(savedConfigServiceProfile.getId());
    final var deactivatePools = new ArrayList<>(configServiceProfile.getDhcpPools());
    deactivatePools.sort(Comparator.comparing(DhcpPoolV1_1::getName));
    VenueDhcpConfigServiceProfileSettings deactivatePoolsSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps, deactivatePools.subList(0, 2).stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , configServiceProfile.getId(), deactivatePoolsSettings);

    // When
    List<WifiDhcpPoolUsage> wifiDhcpPoolUsages = getVenueTemplateWifiDhcpPoolUsages(venueId)
        .getWifiDhcpPoolUsages();

    // Then
    var expectedUsagePool = new ArrayList<>(configServiceProfile.getDhcpPools());
    expectedUsagePool.sort(Comparator.comparing(DhcpPoolV1_1::getName));
    assertWifiDhcpPoolUsage(wifiDhcpPoolUsages, expectedUsagePool.subList(0, 2));

    // template won't have aps so no ip count
    assertThat(wifiDhcpPoolUsages)
        .filteredOn(e -> e.getTotalIpCount() != null || e.getUsedIpCount() != null)
        .hasSize(0);
  }

  private ApDTO apOperationalData(String serialNumber, ApStateEnum state,
      ApSubStateEnum subState) {
    String mockModel = "R730";
    ApDTO.Radio radio =
        new ApDTO.Radio();
    radio.setRadio5Ghz(new ApDTO.RadioParms(
        6, "max", 2, 1, StrictRadioTypeEnum._5_GHz));
    radio.setRadio24Ghz(new ApDTO.RadioParms(
        6, "max", 4, 0, StrictRadioTypeEnum._2_4_GHz));
    radio.setRadios(Arrays.asList(radio.getRadio5Ghz(), radio.getRadio24Ghz()));
    return ApDTO.builder().serialNumber(serialNumber)
        .model(mockModel)
        .clientCount(4)
        .externalIp("*******")
        .firmware("*******")
        .indoorModel(true)
        .ip("*******")
        .lastContacted("2020-01-23T10:49:19.562+0000")
        .lastUpdated("2020-01-23T10:49:19.562+0000")
        .mac("12:CF:23:44:55:55")
        .state(state)
        .subState(subState)
        .uptimeSeconds(1234125l)
        .radio(radio)
//        .poePortStatus("Up 1000Mbps")
        .build();

  }

  private List<ConnectedClientDTO> getMockDhcpClients() {
    //mock ViewModelAgent
    List<ConnectedClientDTO> mockClients = new ArrayList<>();
    ConnectedClientDTO mockClient1 = new ConnectedClientDTO();
    mockClient1.setHostname("hostname");
    mockClient1.setClientMac("E4:A7:A0:D4:15:84");
    mockClients.add(mockClient1);
    ConnectedClientDTO mockClient2 = new ConnectedClientDTO();
    mockClient2.setHostname("hostname 2");
    mockClient2.setClientMac("f4:a7:a0:d4:15:99");
    mockClients.add(mockClient2);

    return mockClients;
  }

  private DnbApDhcpPools.DhcpPoolData createMockApDhcpPoolData() {
    DnbApDhcpPools.DhcpPoolData.Builder dhcpPoolDataBuilder = DnbApDhcpPools.DhcpPoolData.newBuilder();
    dhcpPoolDataBuilder.setApMac("24:79:2A:28:48:D0");

    // poolInfoList
    DnbApDhcpPools.DhcpPerPoolStats.Builder dhcpPerPoolStatsBuilder1 = DnbApDhcpPools.DhcpPerPoolStats.newBuilder();
    dhcpPerPoolStatsBuilder1.setVlan(1001);
    dhcpPerPoolStatsBuilder1.setTotalIpCount(100);
    dhcpPerPoolStatsBuilder1.setUsedIpCount(2);

    // list 1 for poolInfoList 1
    DnbApDhcpPools.DhcpClientInfo.Builder dhcpClientInfoBuilder1 = DnbApDhcpPools.DhcpClientInfo.newBuilder();
    dhcpClientInfoBuilder1.setClientMac("e4:a7:a0:d4:15:84");
    dhcpClientInfoBuilder1.setClientIp("*************");
    dhcpClientInfoBuilder1.setLeaseExpiryTime(1590822371);

    // list 2 for poolInfoList 1
    DnbApDhcpPools.DhcpClientInfo.Builder dhcpClientInfoBuilder2 = DnbApDhcpPools.DhcpClientInfo.newBuilder();
    dhcpClientInfoBuilder2.setClientMac("f4:a7:a0:d4:15:99");
    dhcpClientInfoBuilder2.setClientIp("*************");
    dhcpClientInfoBuilder2.setLeaseExpiryTime(1590825971);

    dhcpPerPoolStatsBuilder1.addDhcpClients(dhcpClientInfoBuilder1);
    dhcpPerPoolStatsBuilder1.addDhcpClients(dhcpClientInfoBuilder2);

    dhcpPoolDataBuilder.addDhcpPoolInfo(dhcpPerPoolStatsBuilder1);

    // poolInfoList 2
    DnbApDhcpPools.DhcpPerPoolStats.Builder dhcpPerPoolStatsBuilder2 = DnbApDhcpPools.DhcpPerPoolStats.newBuilder();
    dhcpPerPoolStatsBuilder2.setVlan(1002);
    dhcpPerPoolStatsBuilder2.setTotalIpCount(10);
    dhcpPerPoolStatsBuilder2.setUsedIpCount(1);

    // list 3 for poolInfoList 2
    DnbApDhcpPools.DhcpClientInfo.Builder dhcpClientInfoBuilder3 = DnbApDhcpPools.DhcpClientInfo.newBuilder();
    dhcpClientInfoBuilder3.setClientMac("g1:a7:a0:d4:15:00");
    dhcpClientInfoBuilder3.setClientIp("**************");
    dhcpClientInfoBuilder3.setLeaseExpiryTime(1590821171);

    dhcpPerPoolStatsBuilder2.addDhcpClients(dhcpClientInfoBuilder3);

    dhcpPoolDataBuilder.addDhcpPoolInfo(dhcpPerPoolStatsBuilder2);

    return dhcpPoolDataBuilder.build();
  }

  @Test
  void testAddGuestNetworkWithDhcpEnableTwice_venueWithoutDhcpConfigServiceProfile_Successfully(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // When - addGuestNetworkTemplate1
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {
        });
    networkDeepReq.setName("Guest Deep 1");
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n1 = addGuestNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, n1.getId(), venueId, null);

    // Then - addGuestNetworkTemplate1
    // assert result - network
    assertNotNull(n1);
    assertTrue(n1.getEnableDhcp());
    assertNotNull(n1.getPortalServiceProfileId());

    // assert result - getDhcpConfigServiceProfiles
    final com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile defaultDhcpServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    List<String> activePoolNames = defaultDhcpServiceProfile.getDhcpPools().stream()
        .map(DhcpPoolV1_1::getName).toList();
    assertDefaultDhcpConfigServiceProfile(defaultDhcpServiceProfile);

    // assert result - venueDhcpConfigServiceProfileSettings
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings =
        getVenueDhcpConfigServiceProfileTemplateSettings(venueId, defaultDhcpServiceProfile.getId());
    assertVenueDhcpConfigServiceProfileSettings(venueDhcpConfigServiceProfileSettings,
        true, activePoolNames, null, false, 0);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(1, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert ddccm
    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccm(ddccmOperations, 5),
          () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD,
              defaultDhcpServiceProfile.getDhcpPools()),
          () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
              DhcpModeEnum.EnableOnEachAPs.toString(), defaultDhcpServiceProfile.getDhcpPools(),
              null)
      );
    }

    // assert cmn-cfg-collector
    {
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertCmnViewModelCollector(viewmodelOperations, 4),
          () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.ADD,
              defaultDhcpServiceProfile, List.of(venueId))
      );
    }

    // When - addGuestNetworkTemplate2
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq2 = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {
        });
    networkDeepReq2.setName("Guest Deep 2");
    networkDeepReq2.setEnableDhcp(true);
    networkDeepReq2.setPortalServiceProfileId(networkDeepReq.getPortalServiceProfileId());
    GuestNetwork n2 = addGuestNetworkTemplate(
        map(networkDeepReq2)); // This can't reuse networkDeepReq somehow in wifi-eda
    edaAddNetworkVenueTemplateMapping(tenantId, userName, n2.getId(), venueId);

    // Then - addGuestNetworkTemplate2
    // assert result - network
    assertTrue(n2.getEnableDhcp());
    assertNotNull(n2.getPortalServiceProfileId());

    // assert result - getDhcpConfigServiceProfiles
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile defaultDhcpServiceProfile2 =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    List<String> activePoolNames2 = defaultDhcpServiceProfile2.getDhcpPools().stream()
        .map(DhcpPoolV1_1::getName).toList();
    assertDefaultDhcpConfigServiceProfile(defaultDhcpServiceProfile2);

    // assert result - venueDhcpConfigServiceProfileSettings
    venueDhcpConfigServiceProfileSettings = getVenueDhcpConfigServiceProfileTemplateSettings(
        venueId, defaultDhcpServiceProfile2.getId());
    assertVenueDhcpConfigServiceProfileSettings(venueDhcpConfigServiceProfileSettings,
        true, activePoolNames2, null, false, 0);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages2
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(1, venueWifiDhcpPoolUsages2.getWifiDhcpPoolUsages().size());

    // assert ddccm
    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertDdccmNoDhcpServiceProfile(ddccmOperations);
      assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations);
    }

    // assert cmn-cfg-collector
    {
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertCmnViewModelCollector(viewmodelOperations, 2),
          () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
      );
    }
  }

  private void assertDefaultDhcpConfigServiceProfile(
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile result) {
    assertEquals(defaultGuestNetworkConfig.getName(), result.getServiceName(),
        "Default DhcpConfigServiceProfile name should equal DHCP-Guest");
    assertEquals(DhcpModeEnum.EnableOnEachAPs, result.getDhcpMode(),
        "Default DhcpConfigServiceProfile mode should equal EnableOnEachAPs");
    assertEquals(1, result.getDhcpPools().size(),
        "Default DhcpConfigServiceProfile should have 1 dhcpPool");

    DhcpPoolV1_1 defaultDhcpPoolV1_1 = result.getDhcpPools().get(0);
    assertEquals(defaultGuestNetworkConfig.getName(), defaultDhcpPoolV1_1.getName(),
        "Default DhcpPool name should equal DHCP-Guest");
    assertEquals((short) 3000, (short) defaultDhcpPoolV1_1.getVlanId(),
        "Default DhcpPool vlanId should equal 3000");
  }

  @Test
  void testAddGuestNetworkWithDhcpEnable_venueWithDhcpConfigProfile_Successfully(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // add dhcpConfigServiceProfile
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile customConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    List<String> activePoolNames = customConfigServiceProfile.getDhcpPools().stream()
        .map(DhcpPoolV1_1::getName).toList();
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        dhcpAps,
        customConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , customConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - addGuestNetworkTemplate
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {
        });
    networkDeepReq.setName("Guest Deep 1");
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork addedNetwork = addGuestNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, addedNetwork.getId(), venueId, null);

    // Then - addGuestNetworkTemplate result
    assertTrue(addedNetwork.getEnableDhcp());
    assertNotNull(addedNetwork.getPortalServiceProfileId());

    // assert venueDhcpConfigServiceProfileSettings
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings =
        getVenueDhcpConfigServiceProfileTemplateSettings(venueId, customConfigServiceProfile.getId());
    assertVenueDhcpConfigServiceProfileSettings(venueDhcpConfigServiceProfileSettings,
        true, activePoolNames, dhcpAps, true, 0);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(3, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> dhcpPoolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(dhcpPoolUsages, customConfigServiceProfile.getDhcpPools(),
        customConfigServiceProfile.getDhcpPools());

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 7),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 4),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  void testUpdateGuestNetworkWithDhcp_fromDhcpEnable_toDhcpDisable(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {
        });
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());
    // When - updateGuestNetwork to dhcp disable
    n.setEnableDhcp(false);
    edaUpdateNetworkTemplate(tenantId, userName, n.getId(), map(n));

    // Then
    GuestNetwork updatedNetwork = (GuestNetwork) getNetworkTemplate(n.getId());
    assertFalse(updatedNetwork.getEnableDhcp());
    assertNotNull(updatedNetwork.getPortalServiceProfileId());

    // assert ddccm - VenueDhcpServiceSetting
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  void testUpdateGuestNetworkWithDhcp_fromDhcpDisable_toDhcpEnable(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {
        });
    networkDeepReq.setEnableDhcp(false);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());
    // When - updateGuestNetwork to dhcp disable
    n.setEnableDhcp(true);
    edaUpdateNetworkTemplate(tenantId, userName, n.getId(), map(n));

    // Then
    GuestNetwork updatedNetwork = (GuestNetwork) getNetworkTemplate(n.getId());
    assertTrue(updatedNetwork.getEnableDhcp());
    assertNotNull(updatedNetwork.getPortalServiceProfileId());

    // assert getDhcpConfigServiceProfiles
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile defaultProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    List<String> activePoolNames = defaultProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName)
        .toList();
    assertDefaultDhcpConfigServiceProfile(defaultProfile);

    // assert getVenueDhcpConfigServiceProfileTemplateSettings
    VenueDhcpConfigServiceProfileSettings actualSetting = getVenueDhcpConfigServiceProfileTemplateSettings(
        venueId, defaultProfile.getId());
    assertVenueDhcpConfigServiceProfileSettings(actualSetting, true, activePoolNames, null, false,
        0);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(1, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert ddccm - VenueDhcpServiceSetting
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 5),
        () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD,
            defaultProfile.getDhcpPools()),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), defaultProfile.getDhcpPools(), null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 4),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations,
            OpType.ADD, defaultProfile, List.of(venueId))
    );
  }

  @Test
  void testAddGuestNetworkVenueWithNetworkDhcpEnable_venueWithoutDhcpConfigProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {
        });
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());
    // When - add NetworkVenue
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);

    // Then - add NetworkVenue
    // assert getDhcpConfigServiceProfiles
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile defaultProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultProfile);

    // assert getVenueDhcpConfigServiceProfileTemplateSettings
    List<String> activePoolNames = defaultProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName)
        .toList();
    VenueDhcpConfigServiceProfileSettings actualSetting = getVenueDhcpConfigServiceProfileTemplateSettings(
        venueId, defaultProfile.getId());
    assertVenueDhcpConfigServiceProfileSettings(actualSetting, true, activePoolNames, null, false,
        0);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(1, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert ddccm - VenueDhcpServiceSetting
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 5),
        () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD,
            defaultProfile.getDhcpPools()),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), defaultProfile.getDhcpPools(), null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 4),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations,
            OpType.ADD, defaultProfile, List.of(venueId))
    );
  }

  @Test
  void testAddGuestNetworkVenueWithNetworkDhcpEnable_venueWithDhcpConfigProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {
        });
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());

    // add dhcpConfigServiceProfile
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnEachAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);

    // enable venue dhcp
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile customConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    List<String> activePoolNames = customConfigServiceProfile.getDhcpPools().stream()
        .map(DhcpPoolV1_1::getName).toList();
    VenueDhcpConfigServiceProfileSettings venueDhcpServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        null, customConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, venueId
        , customConfigServiceProfile.getId(), venueDhcpServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    // When - add NetworkVenue
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);

    // Then - add NetworkVenue
    // assert venueDhcpConfigServiceProfileSettings
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings =
        getVenueDhcpConfigServiceProfileTemplateSettings(venueId, customConfigServiceProfile.getId());
    assertVenueDhcpConfigServiceProfileSettings(venueDhcpConfigServiceProfileSettings,
        true, activePoolNames, null, false, 0);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(3, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert getVenueTemplateDhcpPoolUsage
    List<DhcpPoolUsage> dhcpPoolUsages = getVenueTemplateDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(dhcpPoolUsages, customConfigServiceProfile.getDhcpPools(),
        customConfigServiceProfile.getDhcpPools());

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  void testDeleteGuestNetworkVenueWithNetworkDhcpEnable_Successfully(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetworkTemplate with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {
        });
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetworkTemplate(map(networkDeepReq));

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());

    // add networkVenue
    edaAddNetworkVenueTemplate(tenantId, userName, n.getId(), venueId, null);
    NetworkVenue nv = retrieveTemplate(() -> getNetworkVenueByNetworkAndVenue(n.getId(), venueId));
    String networkVenueId = nv.getId();

    // When - delete networkVenue
    edaDeleteNetworkVenueTemplate(tenantId, userName, networkVenueId);

    // Then - delete networkVenue
    // assert result
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile defaultProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultProfile);

    // assert venueDhcpConfigServiceProfileSettings
    VenueDhcpConfigServiceProfileSettings actualVenueSetting =
        getVenueDhcpConfigServiceProfileTemplateSettings(venueId, defaultProfile.getId());
    assertVenueDhcpConfigServiceProfileSettings(actualVenueSetting, true, null, null,
        false, 0);

    // assert result - getVenueTemplateWifiDhcpPoolUsages
    com.ruckus.cloud.wifi.eda.viewmodel.WifiDhcpPoolUsages venueWifiDhcpPoolUsages
        = getVenueTemplateWifiDhcpPoolUsages(venueId);
    assertEquals(1, venueWifiDhcpPoolUsages.getWifiDhcpPoolUsages().size());

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  void testUpdateNetworkVenueClientIsolationAllowlist_whenDhcpServiceEnabled_ShouldBeDisallowed(
      Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenueTemplate(tenant, "v");

    // AddClientIsolationAllowlist
    ClientIsolationAllowlist allowlist = clientIsolationAllowlist("allowlist");
    addEdaClientIsolationProfile(tenantId, userName, mapToClientIsolationProfile(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_PROFILE, tenantId);

    List<ClientIsolationAllowlist> allowlists = getClientIsolationAllowlists();
    assertEquals(1, allowlists.size());
    String clientIsolationAllowlistId = allowlists.get(0).getId();

    // add network
    PskNetwork pskNetwork = addPskNetworkTemplate(map(pskNetwork("pskNetwork").generate()));

    // add dhcp
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfileViewModel = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileViewModel(3, 1, DhcpModeEnum.EnableOnEachAPs);
    edaAddDhcpConfigServiceProfileTemplateV1_1(tenantId, userName, dhcpConfigServiceProfileViewModel);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, tenantId);
    com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile =
        getDhcpConfigServiceProfileTemplateViewModels().get(0);
    VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings = venueDhcpConfigServiceProfileSettings(
        null, dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpPoolV1_1::getName).toList());
    edaActivateVenueDhcpConfigServiceProfileTemplateSettings(tenantId, userName, v.getId(),
        dhcpConfigServiceProfile.getId(), venueDhcpConfigServiceProfileSettings);
    assertActivityStatusSuccess(ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS, tenantId);

    edaAddNetworkVenueTemplate(tenantId, userName, pskNetwork.getId(), v.getId(),
        clientIsolationAllowlistId);
    assertActivityStatusFail(ADD_NETWORK_VENUE_TEMPLATE, Errors.WIFI_10243, tenantId);

    edaAddNetworkVenueTemplate(tenantId, userName, pskNetwork.getId(), v.getId(), null);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE_TEMPLATE, tenantId);

    NetworkVenue nv = retrieveTemplate(() -> getNetworkVenueByNetworkAndVenue(pskNetwork.getId(), v.getId()));
    edaUpdateNetworkVenueTemplate(tenantId, userName, nv.getId(), map(nv), clientIsolationAllowlistId);
    assertActivityStatusFail(UPDATE_NETWORK_VENUE_TEMPLATE, Errors.WIFI_10243, tenantId);
  }

  @SneakyThrows
  private List<DhcpServiceAp> dhcpService4ApList(ApGroup apGroup) {
    String ap1SN = randomSerialNumber();
    String ap2SN = randomSerialNumber();
    String ap3SN = randomSerialNumber();
    String ap4SN = randomSerialNumber();

    createAp(apGroup, ap1SN, "R510", randomMacAddress());
    createAp(apGroup, ap2SN, "R720", randomMacAddress());
    createAp(apGroup, ap3SN, "R750", randomMacAddress());
    createAp(apGroup, ap4SN, "R320", randomMacAddress());

    List<DhcpServiceAp> dhcpServiceApList = new LinkedList<>();
    dhcpServiceApList.add(createDhcpServiceAp(ap1SN, ApDhcpRoleEnum.PrimaryServer));
    dhcpServiceApList.add(createDhcpServiceAp(ap2SN, ApDhcpRoleEnum.BackupServer));
    dhcpServiceApList.add(createDhcpServiceAp(ap3SN, ApDhcpRoleEnum.NatGateway));
    dhcpServiceApList.add(createDhcpServiceAp(ap4SN, ApDhcpRoleEnum.NatGateway));

    return dhcpServiceApList;
  }

  @SneakyThrows
  private List<DhcpServiceAp> dhcpService2ApList(ApGroup apGroup) {
    String ap1SN = randomSerialNumber();
    String ap2SN = randomSerialNumber();

    createAp(apGroup, ap1SN, "R510", randomMacAddress());
    createAp(apGroup, ap2SN, "R720", randomMacAddress());

    List<DhcpServiceAp> dhcpServiceApList = new LinkedList<>();
    dhcpServiceApList.add(createDhcpServiceAp(ap1SN, ApDhcpRoleEnum.PrimaryServer));
    dhcpServiceApList.add(createDhcpServiceAp(ap2SN, ApDhcpRoleEnum.BackupServer));

    return dhcpServiceApList;
  }

  private DhcpServiceAp createDhcpServiceAp(String serialNumber, ApDhcpRoleEnum role) {
    DhcpServiceAp dhcpServiceAp = new DhcpServiceAp();
    dhcpServiceAp.setSerialNumber(serialNumber);
    dhcpServiceAp.setRole(role);
    return dhcpServiceAp;
  }

  private void assertVenueDhcpConfigServiceProfileSettings(
      VenueDhcpConfigServiceProfileSettings result,
      boolean expectedEnabled, List<String> expectedActivePoolNames,
      List<DhcpServiceAp> expectedDhcpAps, boolean shouldAssertIp, int ipAmount) {
    if (!expectedEnabled) {
      assertNull(result.getActiveDhcpPoolNames());
    }

    if (expectedActivePoolNames != null) {
      assertEquals(expectedActivePoolNames.size(), result.getActiveDhcpPoolNames().size());
      assertTrue(expectedActivePoolNames.containsAll(result.getActiveDhcpPoolNames()));
    }

    if (expectedDhcpAps != null) {
      List<String> expectedSerialNumbers = expectedDhcpAps.stream()
          .map(DhcpServiceAp::getSerialNumber).collect(Collectors.toList());
      List<String> actualSerialNumbers = result.getDhcpServiceAps().stream()
          .map(DhcpServiceAp::getSerialNumber).collect(Collectors.toList());
      DhcpServiceAp primaryDhcpSeviceAp = result.getDhcpServiceAps().stream()
          .filter(ap -> ApDhcpRoleEnum.PrimaryServer.equals(ap.getRole())).findFirst().get();
      DhcpServiceAp backupDhcpSeviceAp = result.getDhcpServiceAps().stream()
          .filter(ap -> ApDhcpRoleEnum.BackupServer.equals(ap.getRole())).findFirst().get();
      assertEquals(expectedDhcpAps.size(), result.getDhcpServiceAps().size(),
          "Should have DhcpServiceAps");
      assertTrue(actualSerialNumbers.containsAll(expectedSerialNumbers),
          "DhcpServiceAps serial numbers should equal test data");

      if (shouldAssertIp && ipAmount > 0) {
        List<String> primaryIps = Arrays.asList("***********", "***********", "***********",
            "***********");
        List<String> secondaryIps = Arrays.asList("***********", "***********", "***********",
            "***********");
        assertTrue(primaryDhcpSeviceAp.getDhcpIps().containsAll(primaryIps.subList(0, ipAmount)),
            "Primary DhcpServiceAp IP should equal test data");
        assertTrue(backupDhcpSeviceAp.getDhcpIps().containsAll(secondaryIps.subList(0, ipAmount)),
            "Secondary DhcpServiceAp IP should equal test data");
      }
    } else {
      assertEquals(0, result.getDhcpServiceAps().size(), "Should have no DhcpServiceAps");
    }
  }

  private void assertDhcpConfigServiceProfile(
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile result,
      String expectedServiceName, DhcpModeEnum expectedDhcpMode,
      List<String> expectedDhcpPoolNames) {
    assertEquals(expectedServiceName, result.getServiceName());
    assertEquals(expectedDhcpMode, result.getDhcpMode());
    assertEquals(expectedDhcpPoolNames.size(), result.getDhcpPools().size());
    List<String> actualDhcpPoolNames = result.getDhcpPools().stream().map(DhcpPoolV1_1::getName)
        .collect(Collectors.toList());
    assertTrue(actualDhcpPoolNames.containsAll(expectedDhcpPoolNames));
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPoolV1_1::getVlanId).count());
    assertNotSame(0,
        (int) result.getDhcpPools().stream().map(DhcpPoolV1_1::getSubnetAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPoolV1_1::getSubnetMask).count());
    assertNotSame(0,
        (int) result.getDhcpPools().stream().map(DhcpPoolV1_1::getStartIpAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPoolV1_1::getEndIpAddress).count());
    assertNotSame(0,
        (int) result.getDhcpPools().stream().map(DhcpPoolV1_1::getLeaseTimeHours).count());
    assertNotSame(0,
        (int) result.getDhcpPools().stream().map(DhcpPoolV1_1::getLeaseTimeMinutes).count());
  }

  private void assertWifiDhcpPoolUsage(List<WifiDhcpPoolUsage> poolUsages, List<DhcpPoolV1_1> expectedActiveDhcpPools) {
    List<String> activeUsageNames = poolUsages.stream()
        .map(WifiDhcpPoolUsage::getName).toList();
    if (expectedActiveDhcpPools != null) {
      assertEquals(expectedActiveDhcpPools.size(),
          activeUsageNames.size(), "Should have active dhcpPool");
      assertTrue(
          expectedActiveDhcpPools.stream().map(DhcpPoolV1_1::getName).collect(Collectors.toList())
              .containsAll(activeUsageNames));
    } else {
      assertEquals(0, activeUsageNames.size(), "Should have no active dhcpPool");
    }
  }

  private void assertDhcpPoolUsage(List<DhcpPoolUsage> poolUsages,
      List<DhcpPoolV1_1> expectedAllDhcpPools, List<DhcpPoolV1_1> expectedActiveDhcpPools) {
    if (expectedAllDhcpPools == null) {
      assertEquals(0, poolUsages.size());
      return;
    }
    List<String> dhcpPoolNames = expectedAllDhcpPools.stream().map(DhcpPoolV1_1::getName)
        .collect(Collectors.toList());
    List<Short> vlanIds = expectedAllDhcpPools.stream().map(DhcpPoolV1_1::getVlanId)
        .collect(Collectors.toList());
    List<String> startIpAddress = expectedAllDhcpPools.stream().map(DhcpPoolV1_1::getStartIpAddress)
        .collect(Collectors.toList());
    List<String> subnetAddress = expectedAllDhcpPools.stream().map(DhcpPoolV1_1::getSubnetAddress)
        .collect(Collectors.toList());
    assertEquals(expectedAllDhcpPools.size(), poolUsages.size(), "Should have dhcpPool");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getName).collect(Collectors.toList())
        .containsAll(dhcpPoolNames), "dhcpPoolNames should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getVlanId).collect(Collectors.toList())
        .containsAll(vlanIds), "vlanId should equal test data");
    assertTrue(
        poolUsages.stream().map(DhcpPoolUsage::getStartIpAddress).collect(Collectors.toList())
            .containsAll(startIpAddress), "startIpAddress should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getSubnetAddress).collect(Collectors.toList())
        .containsAll(subnetAddress), "subnetAddress should equal test data");

    List<String> activeUsageNames = poolUsages.stream().filter(DhcpPoolUsage::getActive)
        .map(DhcpPoolUsage::getName).collect(Collectors.toList());
    if (expectedActiveDhcpPools != null) {
      assertEquals(expectedActiveDhcpPools.size(),
          activeUsageNames.size(), "Should have active dhcpPool");
      assertTrue(
          expectedActiveDhcpPools.stream().map(DhcpPoolV1_1::getName).collect(Collectors.toList())
              .containsAll(activeUsageNames));
    } else {
      assertEquals(0, activeUsageNames.size(), "Should have no active dhcpPool");
    }
  }

  private void assertDdccmVenueAndVenueDhcpSetting(List<Operation> operations,
      boolean shouldAssertVenue, boolean expectedEnabled, String dhcpModeEnum,
      List<DhcpPoolV1_1> expectedDhcpPoolV1_1s, List<DhcpServiceAp> expectedDhcpServiceAps) {

    // assert ddccm - venue
    List<com.ruckus.acx.ddccm.protobuf.wifi.Venue> ddccmVenue = operations.stream()
        .filter(o -> o.getAction().equals(Action.MODIFY))
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUE)).map(Operation::getVenue)
        .collect(Collectors.toList());
    if (shouldAssertVenue) {
      assertEquals(1, ddccmVenue.size(), "Should have one Venue operation send to ddccm");
    } else {
      assertEquals(0, ddccmVenue.size(), "Should have no Venue operation send to ddccm");
    }

    // assert ddccm - VenueDhcpServiceSetting
    List<VenueDhcpServiceSetting> ddccmVenueDhcpServiceSettings = operations.stream()
        .filter(
            o -> o.getAction().equals(Action.ADD)) // VENUEDHCPSERVICESETTING action is always add
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUEDHCPSERVICESETTING))
        .map(Operation::getVenueDhcpServiceSetting).collect(Collectors.toList());

    assertEquals(1, ddccmVenueDhcpServiceSettings.size(),
        "Should have one VenueDhcpServiceSetting operation send to ddccm");
    VenueDhcpServiceSetting ddccmVenueDhcpServiceSetting = ddccmVenueDhcpServiceSettings.get(0);
    assertEquals(expectedEnabled, ddccmVenueDhcpServiceSetting.getEnabled().getValue(),
        "Ddccm VenueDhcpServiceSetting enabled should be " + expectedEnabled);

    if (expectedEnabled) {
      assertEquals(dhcpModeEnum, ddccmVenueDhcpServiceSetting.getMode(),
          "Ddccm VenueDhcpServiceSetting mode error");
      assertEquals(
          expectedDhcpPoolV1_1s.size(),
          ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().size(),
          "Ddccm VenueDhcpServiceSetting should contain dhcpServiceProfileIds(dhcpPoolIds)");
//      assertTrue(
//          ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().containsAll(
//              expectedDhcpPoolV1_1s.stream().map(DhcpPool::getId).collect(Collectors.toList())),
//          "Ddccm VenueDhcpServiceSetting dhcpServiceProfileIds(dhcpPoolIds) should equal test data");

      if (expectedDhcpServiceAps != null) {
        assertEquals(expectedDhcpServiceAps.size(),
            ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
            "Ddccm Should have DhcpServiceAps");
        List<String> expectedIps = expectedDhcpServiceAps.stream()
            .flatMap(ap -> ap.getDhcpIps().stream()).collect(Collectors.toList());
        List<String> actualIps = ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().stream()
            .flatMap(ap -> ap.getNatGatewayIpList().stream()).collect(Collectors.toList());
        assertEquals(expectedIps.size(), actualIps.size());
        assertTrue(actualIps.containsAll(expectedIps),
            "Ddccm VenueDhcpServiceSetting ip should equal test data");
      } else {
        assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
            "Ddccm Should have no DhcpServiceAps");
      }
    } else {
      assertEquals("", ddccmVenueDhcpServiceSetting.getMode());
      assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().size(),
          "Ddccm VenueDhcpServiceSetting should have no dhcpServiceProfileIds(dhcpPoolIds)");
      assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
          "Ddccm Should have no DhcpServiceAps");
    }
  }

  private void assertCmnViewModelCollectorDhcpConfigServiceProfile(
      List<Operations> operations, String venueId, boolean enabled) {
    assertTrue(operations.stream()
        .filter(o -> venueId.equals(o.getId()))
        .allMatch(o -> o.getDocMap().get("dhcp").getStructValue().getFieldsMap()
            .get("enabled").getBoolValue() == enabled));
  }

  private void assertCmnViewModelCollectorDhcpConfigServiceProfile(
      List<Operations> operations, OpType expectedOpType,
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile expectedServiceProfile,
      List<String> expectedVenueIds) {
    List<Operations> viewModelOperations = operations.stream()
        .filter(o -> expectedServiceProfile.getId().equals(o.getId())).collect(Collectors.toList());
    Operations operation = viewModelOperations.stream()
        .filter(o -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .findFirst().get();
    assertEquals(expectedOpType, operation.getOpType());

    if (!operation.getOpType().equals(OpType.DEL)) {
      assertEquals(expectedServiceProfile.getServiceName(),
          operation.getDocMap().get(Key.NAME).getStringValue());
      assertEquals(expectedServiceProfile.getDhcpPools().size(),
          operation.getDocMap().get(Key.DHCP_POOLS)
              .getListValue().getValuesCount());
      assertEquals(true,
          operation.getDocMap().get(Key.IS_TEMPLATE)
              .getBoolValue());

      List<String> venueIds = operation.getDocMap()
          .get(Key.VENUE_IDS).getListValue().getValuesList().stream()
          .map(Value::getStringValue).collect(Collectors.toList());
      if (CollectionUtils.isNotEmpty(expectedVenueIds)) {
        assertEquals(expectedVenueIds.size(), venueIds.size());
        assertTrue(expectedVenueIds.containsAll(venueIds));
      } else {
        assertEquals(0, venueIds.size());
      }
    }
  }

  private void assertCmnViewModelCollectorNoDhcpConfigServiceProfile(List<Operations> operations) {
    assertTrue(operations.stream()
        .noneMatch(o -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex())));
  }

  private void assertDdccmDhcpServiceProfile(
      List<Operation> operations, Action expectedAction, List<DhcpPoolV1_1> expectedDhcpPools) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile> ddccmDhcpServiceProfiles = operations
        .stream()
        .filter(o -> o.getAction().equals(expectedAction))
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.DHCPSERVICEPROFILE))
        .map(Operation::getDhcpServiceProfile).collect(Collectors.toList());

    // assert dhcpPoolId
//    List<String> expectedDhcpPoolIds = expectedDhcpPools.stream().map(DhcpPool::getId)
//        .collect(Collectors.toList());
//    List<String> actualDhcpPoolIds = ddccmDhcpServiceProfiles.stream()
//        .map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getId).collect(Collectors.toList());
//    assertEquals(expectedDhcpPoolIds.size(), actualDhcpPoolIds.size());
//    assertTrue(actualDhcpPoolIds.containsAll(expectedDhcpPoolIds));

    // assert dhcpPoolName
    List<String> expectedDhcpPoolNames = expectedDhcpPools.stream().map(DhcpPoolV1_1::getName)
        .collect(Collectors.toList());
    List<String> actualDhcpPoolNames = ddccmDhcpServiceProfiles.stream()
        .map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getName)
        .collect(Collectors.toList());
    assertEquals(expectedDhcpPoolNames.size(), actualDhcpPoolNames.size());
    assertTrue(ddccmDhcpServiceProfiles.stream()
        .map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getName)
        .collect(Collectors.toList()).containsAll(actualDhcpPoolNames));
  }

  private void assertDdccm(List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertCmnViewModelCollector(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertDdccmNoDhcpServiceProfile(List<Operation> operations) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile> ddccmDhcpServiceProfiles = operations
        .stream()
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.DHCPSERVICEPROFILE))
        .map(Operation::getDhcpServiceProfile).collect(Collectors.toList());
    assertEquals(0, ddccmDhcpServiceProfiles.size(),
        "Should have no DhcpServiceProfile send to ddccm");
  }

  private void assertDdccmNoVenueAndVenueDhcpSetting(List<Operation> operations) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.Venue> ddccmVenue = operations
        .stream()
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUE)).map(Operation::getVenue)
        .collect(Collectors.toList());
    assertEquals(0, ddccmVenue.size(),
        "Should have no Venue operation send to ddccm");
  }
}
