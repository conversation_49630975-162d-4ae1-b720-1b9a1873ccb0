package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.service.impl.IpsecProfileServiceCtrlImpl.SoftGreProfileIpsecProfileConverter;
import com.ruckus.cloud.wifi.servicemodel.projection.SoftGreProfileIpsecProfileProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("IpsecProfileTest")
@WifiJpaDataTest
@Sql(
    statements =
        """
            INSERT INTO tenant (id) VALUES ('id-2');
            INSERT INTO venue(id, tenant) VALUES ('id-3', 'id-2');
            INSERT INTO network(id, type, tenant) VALUES ('id-4', 'OPEN', 'id-2');
            INSERT INTO network(id, type, tenant) VALUES ('id-15', 'OPEN', 'id-2');
            INSERT INTO network_venue(id, tenant, network, venue) VALUES ('id-5', 'id-2', 'id-4', 'id-3');
            INSERT INTO network_venue(id, tenant, network, venue) VALUES ('id-16', 'id-2', 'id-15', 'id-3');
            INSERT INTO ipsec_profile (id, tenant, name) VALUES ('id-1', 'id-2', 'ipsec-test');
            INSERT INTO soft_gre_profile(id, tenant) VALUES ('id-7', 'id-2');
            INSERT INTO soft_gre_profile(id, tenant) VALUES ('id-13', 'id-2');
            INSERT INTO soft_gre_profile_network_venue_activation(id, tenant, network_venue, soft_gre_profile, ipsec_profile) 
                VALUES ('id-6', 'id-2', 'id-5', 'id-7', 'id-1');
            INSERT INTO soft_gre_profile_network_venue_activation(id, tenant, network_venue, soft_gre_profile) 
                VALUES ('id-17', 'id-2', 'id-16', 'id-13');
            INSERT INTO venue_ap_model_specific_attributes (id, tenant, venue) VALUES ('id-8', 'id-2', 'id-3');
            INSERT INTO ap_lan_port_profile(id, tenant, category) VALUES ('id-9', 'id-2', 'ETHERNET');
            INSERT INTO lan_port_adoption(id, checksum, tenant, ap_lan_port_profile) VALUES ('id-11', 'checksum', 'id-2', 'id-9');
            INSERT INTO venue_lan_port(id, tenant, ap_lan_port_profile, venue_ap_model_specific_attributes, lan_port_adoption)
                VALUES ('id-10', 'id-2', 'id-9', 'id-8', 'id-11');
            INSERT INTO soft_gre_profile_lan_port_activation(id, tenant, lan_port_adoption, soft_gre_profile, ipsec_profile)
                VALUES ('id-12', 'id-2', 'id-11', 'id-7', 'id-1');
            INSERT INTO ap_model_specific (id, tenant) VALUES ('id-14', 'id-2');
            INSERT INTO ap_group (id, tenant, venue) VALUES ('id-18', 'id-2', 'id-3');
            INSERT INTO ap (id, tenant, model_specific, ap_group) VALUES ('id-19', 'id-2', 'id-14', 'id-18');
            INSERT INTO ap_lan_port (id, tenant, model_specific, ap_lan_port_profile) VALUES ('id-20', 'id-2', 'id-14', 'id-9');
            """)
class IpsecProfileRepositoryTest {

  @Autowired
  private IpsecProfileRepository repository;

  @Test
  public void testExistsByTenantIdAndNameAndIdNot() {
    var result = repository.existsByTenantIdAndNameAndIdNot("id-2",
        "ipsec-test", "id-1");
    assertFalse(result);

    result = repository.existsByTenantIdAndNameAndIdNot("id-2",
        "ipsec-test", "wrong-id");
    assertTrue(result);
  }

  @Test
  public void testFindAllActivationIdByTenantIdAndVenueIdAndIpsecId() {
    var result = repository.findAllActivationIdByTenantIdAndVenueIdAndIpsecId("id-2", "id-3", "id-1");
    assertTrue(result.contains("id-6"));
    assertTrue(result.contains("id-12"));
  }

  @Test
  public void testFindAllSoftGreProfileIdAndIpsecProfileIdByTenantIdAndVenueId() {
    var result = repository.findAllSoftGreProfileAndIpsecProfileByTenantIdAndVenueId("id-2", "id-3");
    var converted = SoftGreProfileIpsecProfileConverter.convert(result);
    assertEquals(2, converted.size());

    List<String> softGreProfileIds = converted.stream().map(
        SoftGreProfileIpsecProfileProjection::getSoftGreProfileId).toList();
    assertTrue(softGreProfileIds.containsAll(List.of("id-7", "id-13")));
    List<String> ipsecProfileIds = converted.stream().map(
        SoftGreProfileIpsecProfileProjection::getIpsecProfileId).toList();
    assertTrue(ipsecProfileIds.contains("id-1"));
  }

  @Test
  public void testFindAllActivatedIpsecProfiles() {
    String tenantId = "id-2";
    String venueId = "id-3";

    var result = repository.findAllActivatedIpsecProfiles(tenantId, venueId);
    assertEquals(1, result.size());
  }

  @Test
  public void testFindAllLanPortsActivatedIpsecProfiles() {
    String tenantId = "id-2";
    String venueId = "id-3";

    var result = repository.findAllLanPortsActivatedIpsecProfiles(tenantId, venueId);
    assertEquals(1, result.size());
  }

  @Test
  public void testFindAllLanPortsActivatedIpsecProfilesByVenueIdsOrApSerials() {
    String tenantId = "id-2";
    String venueId = "id-3";
    String apSerial = "id-19";

    var result = repository.findAllLanPortsActivatedIpsecProfilesByVenueIdsOrApSerials(tenantId, List.of(venueId), List.of(apSerial));
    assertEquals(1, result.size());
  }
}