package com.ruckus.cloud.wifi.notification;

import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.notification.VenueApCountExecutorFactory.VenueAllApCountExecutor;
import com.ruckus.cloud.wifi.notification.VenueApCountExecutorFactory.VenueImpactApCountExecutor;
import com.ruckus.cloud.wifi.notification.dto.VenueTemplateDto;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
class VenueTemplateDtoBuilderHelperTest {

  @SpyBean
  private VenueTemplateDtoBuilderHelper venueTemplateDtoBuilderHelper;

  @MockBean
  private VenueAllApCountExecutor venueAllApCountExecutor;

  @MockBean
  private VenueImpactApCountExecutor venueImpactApCountExecutor;

  @Test
  public void setVenueTemplateDtoBuilder() {
    String venueId = "venueId";
    List<Venue> venues = List.of(mockVenue(venueId));
    Map<String, Integer> ApCountMap = mockVenueApCountMap(venues, 5);
    Map<String, Integer> ImpactedApCountMap = mockVenueApCountMap(venues, 3);
    when(venueAllApCountExecutor.execute()).thenReturn(ApCountMap);
    when(venueImpactApCountExecutor.execute()).thenReturn(ImpactedApCountMap);

    List<VenueTemplateDto> dtos = venueTemplateDtoBuilderHelper.getBuilder(venues)
        .addVenueAllApCountExecutor(venueAllApCountExecutor)
        .addVenueImpactApCountExecutor(venueImpactApCountExecutor)
        .build();

    Assertions.assertThat(dtos)
        .isNotNull()
        .singleElement()
        .extracting(VenueTemplateDto::getNumberOfAps, VenueTemplateDto::getImpactedNumberOfAps)
        .containsExactly(5, 3);
  }

  private Venue mockVenue(String Id) {
    Venue venue = new Venue();
    venue.setId(Id);
    venue.setTimezone("Asia/Taipei");
    return venue;
  }

  private Map<String, Integer> mockVenueApCountMap(List<Venue> venues, int apCount) {
    return venues.stream()
        .map(Venue::getId)
        .collect(Collectors.toMap(Function.identity(), v -> apCount));
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    @ConditionalOnMissingBean
    public VenueTemplateDtoBuilderHelper VenueTemplateDtoBuilderHelper() {
      return new VenueTemplateDtoBuilderHelper();
    }
  }
}