package com.ruckus.cloud.wifi.scheduler;

import com.ruckus.cloud.wifi.kafka.publisher.WifiAsyncJobPublisher;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueTenantIdProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.util.List;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@WifiUnitTest
class DhcpEventSchedulerTest {

  @MockBean
  private WifiAsyncJobPublisher jobPublisher;

  @MockBean
  private VenueRepository venueRepository;

  @SpyBean
  private DhcpEventScheduler unit;

  @Test
  void testDhcpEventSchedule() {
    var mockVenuePage = new PageImpl<>(List.of(mockVenue(randomId()), mockVenue(randomId())));
    doReturn(mockVenuePage, mockVenuePage, Page.empty()).when(venueRepository).findVenuesByDhcpServiceSettingEnabledAndApExists(any());

    unit.dhcpEventSchedule();
    verify(jobPublisher, times(4)).publish(any(), any());
  }

  private VenueTenantIdProjection mockVenue(String id) {
    return new VenueTenantIdProjection(id, "tenantId");
  }

}
