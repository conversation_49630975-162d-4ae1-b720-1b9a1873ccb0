package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils.listValue;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils.stringValue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.events.gpb.ListValue;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value.KindCase;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.IpsecProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileNetworkVenueActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelUsageTypeEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.IpsecProfileCmnCfgCollectorOperationBuilder.Activation;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.IpsecProfileCmnCfgCollectorOperationBuilder.ApActivation;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.IpsecProfileCmnCfgCollectorOperationBuilder.VenueActivation;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileLanPortActivationRepository;
import com.ruckus.cloud.wifi.repository.SoftGreProfileNetworkVenueActivationRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.servicemodel.projection.ApLanPortProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueLanPortProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.events.gpb.Value;

@WifiUnitTest
class IpsecProfileCmnCfgCollectorOperationBuilderTest {

  @SpyBean
  private IpsecProfileCmnCfgCollectorOperationBuilder unit;

  @MockBean
  private SoftGreProfileNetworkVenueActivationRepository
      softGreProfileNetworkVenueActivationRepository;

  @MockBean
  private VenueLanPortRepository venueLanPortRepository;

  @MockBean
  private ApLanPortRepository apLanPortRepository;

  @MockBean
  private ApRepository apRepository;

  @MockBean
  private SoftGreProfileLanPortActivationRepository softGreProfileLanPortActivationRepository;

  @Test
  void testGetEntityClass() {
    assertThat(unit.entityClass()).isEqualTo(IpsecProfile.class);
  }

  @Test
  void testGetIndex() {
    assertThat(unit.index()).isEqualTo(Index.IPSEC_PROFILE_INDEX_NAME);
  }


  @Nested
  class TestBuildConfig {

    @Test
    void testAddIpsecProfile() {
      var profile = Generators.ipsecProfile().generate();
      profile.setTenant(new Tenant("tenantId"));

      var operationsList = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(operations -> validate(profile, operations.getDocMap()));
    }

    @Test
    void testAddIpsecWithTunnelUsageType() {
      var profile = Generators.ipsecProfile().generate();
      profile.setTenant(new Tenant(randomId()));
      var operationsList = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(operations -> validate(profile, operations.getDocMap()));

      profile.setTunnelUsageType(TunnelUsageTypeEnum.VXLAN_GPE);
      operationsList = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(operations -> validate(profile, operations.getDocMap()));
    }

    @Test
    void testAddIpsecProfileWithNullServerAddress() {
      var profile = Generators.ipsecProfile().generate();
      profile.setTenant(new Tenant("tenantId"));
      profile.setServerAddress(null);

      var operationsList = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(operations -> validate(profile, operations.getDocMap()));
    }

    @Test
    void testAddIpsecProfileWithEmptyServerAddress() {
      var profile = Generators.ipsecProfile().generate();
      profile.setTenant(new Tenant("tenantId"));
      profile.setServerAddress("");

      var operationsList = unit.build(new NewTxEntity<>(profile), emptyTxChanges());
      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(operations -> validate(profile, operations.getDocMap()));
    }

    @Test
    void testUpdateIpsecProfile() {
      var profile = Generators.ipsecProfile().generate();
      profile.setTenant(new Tenant("tenantId"));

      var operationsList = unit.build(new ModifiedTxEntity<>(profile, Set.of("name")), emptyTxChanges());
      assertThat(operationsList)
          .hasSize(1)
          .allSatisfy(operations -> validate(profile, operations.getDocMap()));
    }

    @Test
    void testDeleteIpsecProfile() {
      var operationsList = unit.build(
          new DeletedTxEntity<>(Generators.ipsecProfile().generate()), emptyTxChanges());
      assertThat(operationsList).hasSize(1)
          .allMatch(operations -> operations.getDocCount() == 0);
    }

    private void validate(IpsecProfile profile, Map<String, Value> docMap) {
      assertThat(docMap.get(Key.TENANT_ID))
          .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
          .extracting(Value::getStringValue)
          .isEqualTo(profile.getTenant().getId());
      assertThat(docMap.get(Key.ID))
          .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
          .extracting(Value::getStringValue)
          .isEqualTo(profile.getId());
      assertThat(docMap.get(Key.NAME))
          .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
          .extracting(Value::getStringValue)
          .isEqualTo(profile.getName());
      assertThat(docMap.get(Key.IPSEC_AUTHENTICATION_TYPE))
          .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
          .extracting(Value::getStringValue)
          .isEqualTo(profile.getAuthType().name());
      assertThat(docMap.get(Key.IKE_PROPOSAL_TYPE))
          .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
          .extracting(Value::getStringValue)
          .isEqualTo(profile.getIkeSecurityAssociation().getIkeProposalType().name());
      assertThat(docMap.get(Key.IKE_PROPOSALS))
          .matches(value -> value.getKindCase() == KindCase.LIST_VALUE)
          .extracting(Value::getListValue)
          .extracting(ListValue::getValuesList)
          .matches(list -> list.size() == profile.getIkeSecurityAssociation().getIkeProposals().size())
          .satisfies(values -> {
            for (var value : values) {
              var struct = value.getStructValue();
              assertThat(struct.getFieldsMap())
                      .matches(map -> map.containsKey(Key.ENC_ALG))
                      .matches(map -> map.containsKey(Key.AUTH_ALG))
                      .matches(map -> map.containsKey(Key.PRF_ALG))
                      .matches(map -> map.containsKey(Key.DH_GROUP));
            }
          });
      assertThat(docMap.get(Key.ESP_PROPOSAL_TYPE))
          .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
          .extracting(Value::getStringValue)
          .isEqualTo(profile.getEspSecurityAssociation().getEspProposalType().name());
      assertThat(docMap.get(Key.ESP_PROPOSALS))
              .matches(value -> value.getKindCase() == KindCase.LIST_VALUE)
              .extracting(Value::getListValue)
              .extracting(ListValue::getValuesList)
          .matches(list -> list.size() == profile.getEspSecurityAssociation().getEspProposals().size())
          .satisfies(values -> {
            for (var value : values) {
              var struct = value.getStructValue();
              assertThat(struct.getFieldsMap())
                  .matches(map -> map.containsKey(Key.ENC_ALG))
                  .matches(map -> map.containsKey(Key.AUTH_ALG))
                  .matches(map -> map.containsKey(Key.DH_GROUP));
            }
          });
      assertThat(docMap.get(Key.PRE_SHARED_KEY))
          .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
          .extracting(Value::getStringValue)
          .isEqualTo(profile.getPreSharedKey());

      var serverAddress = profile.getServerAddress();
      if (serverAddress == null) {
        assertThat(docMap.get(Key.IPSEC_SERVER_ADDRESS))
            .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
            .extracting(Value::getStringValue)
            .isEqualTo("");
      } else {
        assertThat(docMap.get(Key.IPSEC_SERVER_ADDRESS))
            .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
            .extracting(Value::getStringValue)
            .isEqualTo(profile.getServerAddress());
      }

      assertThat(docMap.get(Key.TUNNEL_USAGE_TYPE))
          .matches(value -> value.getKindCase() == KindCase.STRING_VALUE)
          .extracting(Value::getStringValue)
          .isEqualTo(profile.getTunnelUsageType().toString());
    }
  }

  @Test
  void testFindNetworkVenueActivations() {
    String tenantId = "tenantId";
    String ipsecProfileId = "ipsecProfileId";
    when(softGreProfileNetworkVenueActivationRepository
        .findByTenantIdAndIpsecProfileId(eq(tenantId), eq(ipsecProfileId)))
        .thenReturn(mockNetworkActivationsOnMultipleVenues());

    var result = unit.findNetworkVenueActivations(tenantId, ipsecProfileId);
    assertThat(result).hasSize(2);

    var venue1Activation = result.stream().filter(activation -> activation.getVenueId().equals("venueId-1"))
        .findFirst()
        .orElseThrow(() -> new AssertionError("Activation with venueId='venueId-1' not found"));
    assertThat(venue1Activation.getNetworkIds()).containsAll(List.of("networkId-1", "networkId-2"));
    assertThat(venue1Activation.getSoftGreProfileIds()).contains("softGreProfileId-1");

    var venue2Activation = result.stream().filter(activation -> activation.getVenueId().equals("venueId-2"))
        .findFirst()
        .orElseThrow(() -> new AssertionError("Activation with venueId='venueId-2' not found"));
    assertThat(venue2Activation.getNetworkIds()).containsAll(List.of("networkId-3"));
    assertThat(venue2Activation.getSoftGreProfileIds()).contains("softGreProfileId-2");
  }

  private List<SoftGreProfileNetworkVenueActivation> mockNetworkActivationsOnMultipleVenues() {
    SoftGreProfileNetworkVenueActivation activation1 = new SoftGreProfileNetworkVenueActivation();
    NetworkVenue networkVenue1 = new NetworkVenue("networkVenueId-1");
    networkVenue1.setVenue(new Venue("venueId-1"));
    networkVenue1.setNetwork(new Network("networkId-1"));
    activation1.setNetworkVenue(networkVenue1);
    activation1.setSoftGreProfile(new SoftGreProfile("softGreProfileId-1"));
    activation1.setIpsecProfile(new IpsecProfile("ipsecProfileId-1"));

    SoftGreProfileNetworkVenueActivation activation2 = new SoftGreProfileNetworkVenueActivation();
    NetworkVenue networkVenue2 = new NetworkVenue("networkVenueId-2");
    networkVenue2.setVenue(new Venue("venueId-1"));
    networkVenue2.setNetwork(new Network("networkId-2"));
    activation2.setNetworkVenue(networkVenue2);
    activation2.setSoftGreProfile(new SoftGreProfile("softGreProfileId-1"));
    activation2.setIpsecProfile(new IpsecProfile("ipsecProfileId-1"));

    SoftGreProfileNetworkVenueActivation activation3 = new SoftGreProfileNetworkVenueActivation();
    NetworkVenue networkVenue3 = new NetworkVenue("networkVenueId-3");
    networkVenue3.setVenue(new Venue("venueId-2"));
    networkVenue3.setNetwork(new Network("networkId-3"));
    activation3.setNetworkVenue(networkVenue3);
    activation3.setSoftGreProfile(new SoftGreProfile("softGreProfileId-2"));
    activation3.setIpsecProfile(new IpsecProfile("ipsecProfileId-1"));

    return List.of(activation1, activation2, activation3);
  }

  @Test
  public void testFindVenueActivations() {
    String tenantId = "tenantId";
    String ipsecProfileId = "ipsecProfileId";
    when(softGreProfileLanPortActivationRepository
        .findSoftGreProfileLanPortActivationsByTenantIdAndIpsecProfileId(eq(tenantId), eq(ipsecProfileId)))
        .thenReturn(mockLanPortActivations());
    when(venueLanPortRepository.findByTenantIdAndLanPortAdoptionIdIn(eq(tenantId), eq(List.of("lanPortAdoptionId-1"))))
        .thenReturn(mockVenueLanPorts("venueLanPortId-1"));
    when(venueLanPortRepository.findByTenantIdAndLanPortAdoptionIdIn(eq(tenantId), eq(List.of("lanPortAdoptionId-2"))))
        .thenReturn(mockVenueLanPorts("venueLanPortId-2"));
    when(venueLanPortRepository.findVenueIdModelPortIdApGroupsByVenueLanPorts(eq(List.of("venueLanPortId-1"))))
        .thenReturn(mockVenueLanPortProjections("venueId-1"));
    when(venueLanPortRepository.findVenueIdModelPortIdApGroupsByVenueLanPorts(eq(List.of("venueLanPortId-2"))))
        .thenReturn(mockVenueLanPortProjections("venueId-2"));
    when(apRepository.findByApGroupVenueIdAndTenantId(eq("venueId-1"), eq(tenantId)))
        .thenReturn(mockAps("apId-1"));
    when(apRepository.findByApGroupVenueIdAndTenantId(eq("venueId-2"), eq(tenantId)))
        .thenReturn(mockAps("apId-2"));

    var result = unit.findVenueActivations(tenantId, ipsecProfileId);
    assertThat(result).hasSize(2);
    var activation1 = result.stream().filter(activation -> activation.getVenueId().equals("venueId-1"))
            .findFirst().orElseThrow(() -> new AssertionError("Activation with venueId='venueId-1' not found"));
    var activation2 = result.stream().filter(activation -> activation.getVenueId().equals("venueId-2"))
            .findFirst().orElseThrow(() -> new AssertionError("Activation with venueId='venueId-2' not found"));
    assertThat(activation1.getSerials()).containsAll(List.of("apId-1"));
    assertThat(activation2.getSerials()).containsAll(List.of("apId-2"));
  }

  @Test
  public void testFindApActivations() {
    String tenantId = "tenantId";
    String ipsecProfileId = "ipsecProfileId";
    when(softGreProfileLanPortActivationRepository
        .findSoftGreProfileLanPortActivationsByTenantIdAndIpsecProfileId(eq(tenantId), eq(ipsecProfileId)))
        .thenReturn(mockLanPortActivations());
    when(apLanPortRepository.findByTenantIdAndLanPortAdoptionIdIn(eq(tenantId), eq(List.of("lanPortAdoptionId-1"))))
        .thenReturn(mockApLanPorts("apLanPortId-1"));
    when(apLanPortRepository.findByTenantIdAndLanPortAdoptionIdIn(eq(tenantId), eq(List.of("lanPortAdoptionId-2"))))
        .thenReturn(mockApLanPorts("apLanPortId-2"));
    when(apLanPortRepository.findVenueIdApSerialPortIdByApLanPorts(eq(List.of("apLanPortId-1"))))
        .thenReturn(mockApLanPortProjections("venueId-1", "serial-1"));
    when(apLanPortRepository.findVenueIdApSerialPortIdByApLanPorts(eq(List.of("apLanPortId-2"))))
        .thenReturn(mockApLanPortProjections("venueId-2", "serial-2"));

    var result = unit.findApActivations(tenantId, ipsecProfileId);
    assertThat(result).hasSize(2);
    var activation1 = result.stream().filter(activation -> activation.getVenueId().equals("venueId-1"))
            .findFirst().orElseThrow(() -> new AssertionError("Activation with venueId='venueId-1' not found"));
    var activation2 = result.stream().filter(activation -> activation.getVenueId().equals("venueId-2"))
            .findFirst().orElseThrow(() -> new AssertionError("Activation with venueId='venueId-2' not found"));
    assertThat(activation1.getSerial()).isEqualTo("serial-1");
    assertThat(activation2.getSerial()).isEqualTo("serial-2");
  }

  private List<SoftGreProfileLanPortActivation> mockLanPortActivations() {
    var activation1 = new SoftGreProfileLanPortActivation();
    activation1.setTenant(new Tenant("tenantId"));
    activation1.setLanPortAdoption(new LanPortAdoption("lanPortAdoptionId-1"));
    activation1.setSoftGreProfile(new SoftGreProfile("softGreProfileId-1"));
    var activation2 = new SoftGreProfileLanPortActivation();
    activation2.setTenant(new Tenant("tenantId"));
    activation2.setLanPortAdoption(new LanPortAdoption("lanPortAdoptionId-2"));
    activation2.setSoftGreProfile(new SoftGreProfile("softGreProfileId-2"));
    return List.of(activation1, activation2);
  }

  private List<VenueLanPort> mockVenueLanPorts(String id) {
    var venueLanPort = new VenueLanPort(id);
    return List.of(venueLanPort);
  }

  private List<ApLanPort> mockApLanPorts(String id) {
    var apLanPort = new ApLanPort(id);
    return List.of(apLanPort);
  }

  private List<VenueLanPortProjection> mockVenueLanPortProjections(String venueId) {
    var venueLanPortProjection = new VenueLanPortProjection(venueId, "apModel-1", "portId-1");
    return List.of(venueLanPortProjection);
  }

  private List<ApLanPortProjection> mockApLanPortProjections(String venueId, String serial) {
    var apLanPortProjection = new ApLanPortProjection(venueId, serial, "portId-1");
    return List.of(apLanPortProjection);
  }

  private List<Ap> mockAps(String id) {
    Ap ap = new Ap(id);
    ap.setModel("apModel-1");
    return List.of(ap);
  }

  @Test
  public void testConvertNetworkActivationsToValue() {
    List<Activation> activations = new ArrayList<>(List.of(
        new Activation("venue-id", List.of("soft-gre-profile-id-1"), List.of("network-id-1"))));

    var result = unit.convertActivationsToValue(activations);

    assertThat(result).matches(value -> value.getKindCase() == KindCase.LIST_VALUE);
    assertThat(result.getListValue().getValuesList()).hasSize(1);
    Struct structValue = result.getListValue().getValuesList().get(0).getStructValue();
    assertThat(structValue.getFieldsMap())
        .containsEntry(Key.VENUE_ID, stringValue("venue-id"))
        .containsEntry(Key.SOFT_GRE_PROFILE_ID, stringValue("soft-gre-profile-id-1"))
        .containsEntry(Key.WIFI_NETWORK_IDS, listValue(List.of(stringValue("network-id-1"))));
  }

  @Test
  public void testConvertVenueActivationsToValue() {
    List<VenueActivation> venueActivations = new ArrayList<>(List.of(
        new VenueActivation("venue-id", "ap-model",
            "port-id", "soft-gre-profile-id", List.of("serial-1"))));

    var result = unit.convertVenueActivationsToValue(venueActivations);

    assertThat(result).matches(value -> value.getKindCase() == KindCase.LIST_VALUE);
    assertThat(result.getListValue().getValuesList()).hasSize(1);
    Struct structValue = result.getListValue().getValuesList().get(0).getStructValue();
    assertThat(structValue.getFieldsMap())
        .containsEntry(Key.VENUE_ID, stringValue("venue-id"))
        .containsEntry(Key.AP_MODEL, stringValue("ap-model"))
        .containsEntry(Key.PORT_ID, stringValue("port-id"))
        .containsEntry(Key.SOFT_GRE_PROFILE_ID, stringValue("soft-gre-profile-id"))
        .containsEntry(Key.AP_SERIAL_NUMBERS, listValue(List.of(stringValue("serial-1"))));
  }

  @Test
  public void testConvertApActivationsToValue() {
    List<ApActivation> apActivations = new ArrayList<>(List.of(
        new ApActivation("venue-id", "port-id", "soft-gre-profile-id", "serial")));

    var result = unit.convertApActivationsToValue(apActivations);

    assertThat(result).matches(value -> value.getKindCase() == KindCase.LIST_VALUE);
    assertThat(result.getListValue().getValuesList()).hasSize(1);
    Struct structValue = result.getListValue().getValuesList().get(0).getStructValue();
    assertThat(structValue.getFieldsMap())
        .containsEntry(Key.VENUE_ID, stringValue("venue-id"))
        .containsEntry(Key.AP_SERIAL_NUMBER, stringValue("serial"))
        .containsEntry(Key.PORT_ID, stringValue("port-id"))
        .containsEntry(Key.SOFT_GRE_PROFILE_ID, stringValue("soft-gre-profile-id"));
  }

  @Test
  public void testRemoveSameSerialsFromVenueActivationsByApActivations() {
    List<VenueActivation> venueActivations = new ArrayList<>(List.of(
        new VenueActivation("venue-id", "ap-model", "port-id", "soft-gre-profile-id",
            new ArrayList<>(List.of("serial-1", "serial-2")))));
    List<ApActivation> apActivations = new ArrayList<>(List.of(
        new ApActivation("venue-id", "port-id", "soft-gre-profile-id", "serial-1")));

    unit.removeSameSerialsFromVenueActivationsByApActivations(venueActivations, apActivations);

    assertThat(venueActivations).hasSize(1);
    assertThat(venueActivations.get(0).getSerials()).hasSize(1);
    assertThat(venueActivations.get(0).getSerials()).containsExactly("serial-2");
  }
}