package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_CLIENT_ISOLATION_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_CLIENT_ISOLATION_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_CLIENT_ISOLATION_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.openNetwork;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME;
import static com.ruckus.cloud.wifi.test.fixture.ClientIsolationTestFixture.clientIsolationAllowlist;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeClientIsolationProfileRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  protected MessageCaptors messageCaptors;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void addClientIsolationProfile(Tenant tenant) {
    clearMessageBeforeEachEdaOperation = false;

    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var myAllowlistId = randomId();

    var v1 = createVenue(tenant, "v1");
    var v2 = createVenue(tenant, "v2");

    var allowlist = clientIsolationAllowlist("name");
    allowlist.setId(myAllowlistId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_PROFILE,
        userName, mapToClientIsolationProfile(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_PROFILE, tenantId);

    var profile = getClientIsolationProfile(myAllowlistId);
    assertNotNull(profile);
    assertEquals(myAllowlistId, profile.getId());

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 1),
          () -> assertDdccmPublisherAllowlist(ddccmOperations,
              Action.ADD, myAllowlistId, "name")
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 1),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.ADD, myAllowlistId, "name", List.of())
      );
    }

    clearMessage();

    final var n1 = addOpenNetwork(map(openNetwork("n1").generate()));
    final var n2 = addPskNetwork(map(pskNetwork("n2").generate()));

    edaAddNetworkVenue(tenantId, userName, n1.getId(), v1.getId(), null);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    edaAddNetworkVenue(tenantId, userName, n2.getId(), v2.getId(), null);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    assertNull(getNetwork(n1.getId()).getNetworkVenues().get(0).getClientIsolationAllowlist());
    assertNull(getNetwork(n2.getId()).getNetworkVenues().get(0).getClientIsolationAllowlist());

    {
      var ddccmOperations = receiveDdccmOperations(2, tenantId);

      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 2),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );
    }

    clearMessage();

    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v1.getId(), n1.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v2.getId(), n2.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    {
      var ddccmOperations = receiveDdccmOperations(2, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 2),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(2, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 4),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, myAllowlistId, "name", List.of(getEsVenue(v1, n1))),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations, OpType.MOD, myAllowlistId,
              "name", List.of(getEsVenue(v1, n1), getEsVenue(v2, n2)))
      );
    }

    // bind same client isolation profile
    clearMessage();

    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v1.getId(), n1.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v2.getId(), n2.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    // test tenant-max-count 2
    clearMessage();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_PROFILE,
        userName, mapToClientIsolationProfile(clientIsolationAllowlist("al2")));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_PROFILE, tenantId);

    clearMessage();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_PROFILE,
        userName, mapToClientIsolationProfile(clientIsolationAllowlist("al3")));
    assertActivityStatusFail(ADD_CLIENT_ISOLATION_PROFILE, Errors.WIFI_10241, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    // test bind-max-count 3
    clearMessage();

    final var n3 = addOpenNetwork(map(openNetwork("n3").generate()));
    final var n4 = addPskNetwork(map(pskNetwork("n4").generate()));

    edaAddNetworkVenue(tenantId, userName, n3.getId(), v1.getId(), null);
    edaAddNetworkVenue(tenantId, userName, n4.getId(), v2.getId(), null);

    clearMessage();

    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v1.getId(), n3.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 1),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 2),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations, OpType.MOD, myAllowlistId,
              "name", List.of(getEsVenue(v1, n1), getEsVenue(v2, n2), getEsVenue(v1, n3)))
      );
    }

    clearMessage();

    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v2.getId(), n4.getId(),
        myAllowlistId);
    assertActivityStatusFail(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, Errors.WIFI_10455,
        tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    // unset it from venue Wi-Fi networks
    clearMessage();

    deactivateEdaClientIsolationProfileWithNetwork(tenantId, userName, v1.getId(), n1.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);
    deactivateEdaClientIsolationProfileWithNetwork(tenantId, userName, v2.getId(), n2.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);
    deactivateEdaClientIsolationProfileWithNetwork(tenantId, userName, v1.getId(), n3.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    {
      var ddccmOperations = receiveDdccmOperations(3, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 3),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(3, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 6),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, myAllowlistId, "name", List.of(getEsVenue(v2, n2), getEsVenue(v1, n3))),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, myAllowlistId, "name", List.of(getEsVenue(v1, n3))),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, myAllowlistId, "name", List.of())
      );
    }

    // network disable client-isolation
    clearMessage();

    var network1 = (OpenNetwork) getNetwork(n1.getId());
    network1.getWlan().getAdvancedCustomization().setClientIsolation(false);
    network1.getWlan().getAdvancedCustomization().setClientIsolationOptions(null);
    updateEdaNetwork(tenantId, userName, network1.getId(), map(network1));
    assertActivityStatusSuccess(UPDATE_NETWORK, tenantId);

    network1 = (OpenNetwork) getNetwork(network1.getId());
    assertFalse(network1.getWlan().getAdvancedCustomization().getClientIsolation());
    assertNull(network1.getNetworkVenues().get(0).getClientIsolationAllowlist());

    // if network didn't enable client-isolation, can't set isolation profile by network-venue
    clearMessage();

    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v1.getId(),
        network1.getId(),
        myAllowlistId);
    assertActivityStatusFail(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, Errors.WIFI_10457,
        tenantId);

    network1 = (OpenNetwork) getNetwork(network1.getId());
    assertFalse(network1.getWlan().getAdvancedCustomization().getClientIsolation());
    assertNull(network1.getNetworkVenues().get(0).getClientIsolationAllowlist());

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());
  }

  @Test
  public void networkOrNetworkVenueForClientIsolationProfile(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var myAllowlistId = randomId();

    var v = createVenue(tenant, "v");

    var allowlist = clientIsolationAllowlist("name");
    allowlist.setId(myAllowlistId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_PROFILE,
        userName, mapToClientIsolationProfile(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_PROFILE, tenantId);

    var profile = getClientIsolationProfile(myAllowlistId);
    assertNotNull(profile);
    assertEquals(myAllowlistId, profile.getId());

    clearMessage();

    // add network + add network-venue with clientIsolationAllowlist
    var n = addOpenNetwork(map(openNetwork("n").generate()));
    edaAddNetworkVenue(tenantId, userName, n.getId(), v.getId(), null);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    clearMessage();

    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v.getId(), n.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 1),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 2),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, myAllowlistId, "name", List.of(getEsVenue(v, n)))
      );
    }

    clearMessage();

    // delete network-venue
    var networkVenue = getNetworkVenueByNetwork(n.getId()).get(0);
    deleteEdaNetworkVenue(tenantId, userName, networkVenue.getId());
    assertActivityStatusSuccess(DELETE_NETWORK_VENUE, tenantId);

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 1),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 3),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, myAllowlistId, "name", List.of())
      );
    }

    clearMessage();

    // add network-venue
    edaAddNetworkVenue(tenantId, userName, n.getId(), v.getId(), null);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    clearMessage();

    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v.getId(), n.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 1),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 2),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, myAllowlistId, "name", List.of(getEsVenue(v, n))
          ));
    }

    // delete network
    deleteEdaNetwork(tenantId, userName, n.getId());

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 1),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 3),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, myAllowlistId, "name", List.of())
      );
    }
  }

  @Test
  public void updateClientIsolationProfile() {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var myAllowlistId = randomId();

    var allowlist = clientIsolationAllowlist("name");
    allowlist.setId(myAllowlistId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_PROFILE,
        userName, mapToClientIsolationProfile(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_PROFILE, tenantId);

    var profile = getClientIsolationProfile(myAllowlistId);
    assertNotNull(profile);
    assertEquals(myAllowlistId, profile.getId());

    // start to update
    allowlist.setName("newname");

    var rps = new RequestParams().addPathVariable("clientIsolationProfileId", myAllowlistId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_CLIENT_ISOLATION_PROFILE, userName,
        rps, mapToClientIsolationProfile(allowlist));
    assertActivityStatusSuccess(UPDATE_CLIENT_ISOLATION_PROFILE, tenantId);

    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccmPublisher(ddccmOperations, 1),
        () -> assertDdccmPublisherAllowlist(ddccmOperations,
            Action.MODIFY, myAllowlistId, "newname")
    );

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 1),
        () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
            OpType.MOD, myAllowlistId, "newname", List.of())
    );
  }

  @Test
  public void deleteClientIsolationProfile(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var myAllowlistId = randomId();

    var v = createVenue(tenant, "v");

    var allowlist = clientIsolationAllowlist("name");
    allowlist.setId(myAllowlistId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_PROFILE,
        userName, mapToClientIsolationProfile(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_PROFILE, tenantId);

    var profile = getClientIsolationProfile(myAllowlistId);
    assertNotNull(profile);
    assertEquals(myAllowlistId, profile.getId());

    // bind allowlist to network
    OpenNetwork n = addOpenNetwork(map(openNetwork("n").generate()));
    edaAddNetworkVenue(tenantId, userName, n.getId(), v.getId(), null);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);
    activateEdaClientIsolationProfileWithNetwork(tenantId, userName, v.getId(), n.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    clearMessage();

    // start to delete
    // not allow to delete allowlist when it is being used
    RequestParams rps = new RequestParams().addPathVariable("clientIsolationProfileId",
        myAllowlistId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DELETE_CLIENT_ISOLATION_PROFILE, userName,
        rps, "");
    assertActivityStatusFail(DELETE_CLIENT_ISOLATION_PROFILE, Errors.WIFI_10454, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    // remove binding
    deactivateEdaClientIsolationProfileWithNetwork(tenantId, userName, v.getId(), n.getId(),
        myAllowlistId);
    assertActivityStatusSuccess(DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, tenantId);

    // delete again
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DELETE_CLIENT_ISOLATION_PROFILE, userName,
        rps, "");
    assertActivityStatusSuccess(DELETE_CLIENT_ISOLATION_PROFILE, tenantId);

    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccmPublisher(ddccmOperations, 1),
        () -> assertDdccmPublisherAllowlist(ddccmOperations,
            Action.DELETE, myAllowlistId, null)
    );

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 1),
        () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
            OpType.DEL, myAllowlistId, null, null)
    );
  }

  private Map<String, String> getEsVenue(Venue venue, Network network) {
    return Map.of(EsConstants.Key.VENUE_ID, venue.getId(), EsConstants.Key.WIFI_NETWORK_ID,
        network.getId());
  }

  private static void assertDdccmPublisher(
      List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private static void assertDdccmPublisherAllowlist(
      List<Operation> operations, Action action, String allowlistId, String allowlistName) {
    assertTrue(operations.stream()
        .filter(o -> o.getAction() == action)
        .filter(Operation::hasVenueClientIsolationWhitelist)
        .map(Operation::getVenueClientIsolationWhitelist)
        .filter(v -> allowlistName == null || allowlistName.equals(v.getName()))
        .anyMatch(v -> allowlistId.equals(v.getId())));
  }

  private static void assertDdccmPublisherNoAllowlist(
      List<Operation> operations) {
    assertTrue(operations.stream()
        .noneMatch(Operation::hasVenueClientIsolationWhitelist));
  }

  private static void assertViewmodelCollector(
      List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size(),
        "viewmodel collector size not equal, expected=" + opsSize + " actual=" + operations.size());
  }

  private static void assertViewmodelCollectorAllowlist(List<Operations> operations, OpType opType,
      String allowlistId, String allowlistName, List<Map<String, String>> venues) {
    assertTrue(operations.stream()
            .filter(o -> CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME.equals(o.getIndex()))
            .filter(o -> allowlistId.equals(o.getId()))
            .filter(o -> o.getOpType() == opType)
            .filter(o -> allowlistName == null || allowlistName.equals(
                o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .anyMatch(o -> {
              if (venues == null) {
                return true;
              }
              var values = o.getDocMap().get(EsConstants.Key.ACTIVATIONS).getListValue().getValuesList()
                  .stream().map(Value::getStructValue).map(Struct::getFieldsMap).map(
                      m -> m.entrySet().stream().collect(
                          Collectors.toMap(Entry::getKey, e -> e.getValue().getStringValue())))
                  .toList();
              return values.size() == venues.size() && values.containsAll(venues) && venues.containsAll(
                  values);
            }),
        "viewmodel collector allow list opType=" + opType + ", allowlistId=" + allowlistId
            + ",allowlistName=" + allowlistName + ", venues=" + venues + ", operations="
            + operations);
  }
}
