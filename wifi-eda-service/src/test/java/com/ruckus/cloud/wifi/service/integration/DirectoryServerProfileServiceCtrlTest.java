package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_DIRECTORY_PROFILE_REUSE_COMPONENT_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.willReturn;

import com.ruckus.cloud.wifi.client.extauth.DirectoryProfileDto;
import com.ruckus.cloud.wifi.client.extauth.ExternalAuthClient;
import com.ruckus.cloud.wifi.core.autoconfig.HibernateAutoConfiguration;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.DirectoryServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.kafka.publisher.activity.ActivityCfgChangeRespPublisher;
import com.ruckus.cloud.wifi.mapper.DirectoryServerProfileMerger;
import com.ruckus.cloud.wifi.mapper.DirectoryServerProfileMergerImpl;
import com.ruckus.cloud.wifi.repository.DirectoryServerProfileRepository;
import com.ruckus.cloud.wifi.repository.GuestNetworkRepository;
import com.ruckus.cloud.wifi.service.DirectoryServerProfileServiceCtrl;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.DirectoryServerProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Optional;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@Tag("DirectoryServerProfileTest")
@WifiJpaDataTest
class DirectoryServerProfileServiceCtrlTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private DirectoryServerProfileServiceCtrlImpl directoryServerProfileService;

  @MockBean
  private ExternalAuthClient externalAuthClient;

  @MockBean
  private FeatureFlagService featureFlagService;

  @Test
  void addDirectoryServerProfile(Tenant tenant) throws Exception {
    var profile = new DirectoryServerProfile();
    profile.setName("name");

    profile = directoryServerProfileService.addDirectoryServerProfile(profile);
    assertNotNull(profile);
    assertNotNull(profile.getId());
    var result = directoryServerProfileService.getDirectoryServerProfile(profile.getId());
    assertEquals(profile.getId(), result.getId());

    var profile2 = new DirectoryServerProfile();
    profile2.setName("name");
    assertThrows(
        InvalidPropertyValueException.class,
        () -> directoryServerProfileService.addDirectoryServerProfile(profile2));
  }

  @Test
  void updateDirectoryServerProfile(Tenant tenant) throws Exception {
    var existedProfile = new DirectoryServerProfile();
    existedProfile.setName("My Profile");
    directoryServerProfileService.addDirectoryServerProfile(existedProfile);

    var profileToUpdate = new DirectoryServerProfile();
    profileToUpdate.setName("name");
    profileToUpdate = directoryServerProfileService.addDirectoryServerProfile(profileToUpdate);
    var profileToUpdateId = profileToUpdate.getId();

    profileToUpdate = new DirectoryServerProfile();
    profileToUpdate.setName("name - updated");
    profileToUpdate =
        directoryServerProfileService.updateDirectoryServerProfile(profileToUpdateId,
            profileToUpdate);
    assertEquals(profileToUpdateId, profileToUpdate.getId());
    assertEquals("name - updated", profileToUpdate.getName());

    var profileToUpdate2 = new DirectoryServerProfile();
    profileToUpdate2.setName("My Profile");
    assertThrows(
        InvalidPropertyValueException.class,
        () -> directoryServerProfileService.updateDirectoryServerProfile(profileToUpdateId,
            profileToUpdate2));
  }


  @Test
  void deleteDirectoryServerProfile(Tenant tenant) throws Exception {
    var profile = new DirectoryServerProfile();
    profile.setName("name");
    profile = directoryServerProfileService.addDirectoryServerProfile(profile);

    var profileId = profile.getId();
    directoryServerProfileService.deleteDirectoryServerProfile(profileId);
    assertThrows(
        ObjectNotFoundException.class,
        () -> directoryServerProfileService.getDirectoryServerProfile(profileId));
  }

  @Test
  void activateDirectoryServerProfileOnWifiNetwork(Tenant tenant) throws Exception {
    var profile1 = new DirectoryServerProfile();
    profile1.setName("name");
    DirectoryServerProfile finalProfile =
        directoryServerProfileService.addDirectoryServerProfile(profile1);
    var profileId = finalProfile.getId();

    GuestNetwork guestNetwork =
        GuestNetworkTestFixture.randomGuestNetwork(
            tenant,
            n -> {
              n.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.Directory);
            });
    repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
    directoryServerProfileService.activateDirectoryServerProfileOnWifiNetwork(
        guestNetwork.getId(), profileId);
    var result = repositoryUtil.find(GuestNetwork.class, guestNetwork.getId(), tenant.getId());
    assertNotNull(result);
    assertEquals(profileId, result.getDirectoryServerProfile().getId());
    assertEquals(profileId, result.getDirectoryProfileId());
  }

  @Test
  void deleteDirectoryServerProfileButActivatedOnNetwork(Tenant tenant)
      throws Exception {
    var profile1 = new DirectoryServerProfile();
    profile1.setName("name");
    DirectoryServerProfile finalProfile = directoryServerProfileService.addDirectoryServerProfile(
        profile1);
    var profileId = finalProfile.getId();

    GuestNetwork guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(tenant,
        n -> {
          n.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.Directory);
        });
    repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
    directoryServerProfileService.activateDirectoryServerProfileOnWifiNetwork(
        guestNetwork.getId(), profileId);

    assertThrows(
        ObjectInUseException.class,
        () -> directoryServerProfileService.deleteDirectoryServerProfile(profileId));
  }

  @Test
  void activateDirectoryProfileOnNetworkWithProfileInExternalAuth(Tenant tenant) throws Exception {
    // Arrange
    String profileId = randomId();
    String profileName = randomName();
    willReturn(true).given(featureFlagService)
        .isFeatureEnable(eq(WIFI_DIRECTORY_PROFILE_REUSE_COMPONENT_TOGGLE), any());
    willReturn(Optional.of(new DirectoryProfileDto(profileId, profileName)))
        .given(externalAuthClient).getDirectoryProfile(profileId);

    GuestNetwork guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(
        tenant, n -> n.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.Directory));
    repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());

    // Act
    directoryServerProfileService.activateDirectoryServerProfileOnWifiNetwork(
        guestNetwork.getId(), profileId);

    // Assert
    GuestNetwork updatedNetwork = repositoryUtil.find(GuestNetwork.class, guestNetwork.getId(), tenant.getId());
    assertNotNull(updatedNetwork);
    assertEquals(profileId, updatedNetwork.getDirectoryProfileId());
  }

  @TestConfiguration
  @Import({
      HibernateAutoConfiguration.class,
  })
  static class TestConfig {

    @Bean
    DirectoryServerProfileServiceCtrl directoryServerProfileServiceCtrl(
        DirectoryServerProfileRepository directoryServerProfileRepository,
        GuestNetworkRepository guestNetworkRepository,
        DirectoryServerProfileMerger directoryServerProfileMerger,
        ActivityCfgChangeRespPublisher activityCfgChangeRespPublisher,
        FeatureFlagService featureFlagService,
        ExternalAuthClient externalAuthClient) {
      return new DirectoryServerProfileServiceCtrlImpl(
          directoryServerProfileRepository,
          guestNetworkRepository,
          directoryServerProfileMerger,
          activityCfgChangeRespPublisher,
          featureFlagService,
          externalAuthClient,
          64);
    }

    @Bean
    @ConditionalOnMissingBean
    public DirectoryServerProfileMerger directoryServerProfileMerger() {
      return new DirectoryServerProfileMergerImpl();
    }
  }
}
