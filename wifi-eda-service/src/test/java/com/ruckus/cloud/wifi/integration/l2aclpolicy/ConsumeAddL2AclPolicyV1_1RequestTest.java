package com.ruckus.cloud.wifi.integration.l2aclpolicy;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.L2AclPolicyTestFixture.randomL2AclPolicy;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.L2AclPolicyRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeAddL2AclPolicyV1_1RequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeAddL2AclPolicyV1_1Message {
    @Test
    void givenDuplicatePolicyName(Tenant tenant) {
      final var duplicateName = randomName();
      repositoryUtil.createOrUpdate(
          randomL2AclPolicy(tenant, p -> p.setName(duplicateName)), tenant.getId(), randomTxId());

      final var l2AclPolicy =
          L2AclPolicyRestCtrl.L2AclPolicyMapper.INSTANCE.ServiceL2AclPolicy2L2AclPolicy(
              randomL2AclPolicy(tenant, p -> p.setName(duplicateName)));

      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.ADD_L2ACL_POLICY_V1_1,
                      randomName(),
                      new RequestParams(),
                      l2AclPolicy))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(InvalidPropertyValueException.class);

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.ADD_L2ACL_POLICY));
    }

    @Test
    void thenSaveAndSendingMessages(Tenant tenant) {
      final var l2AclPolicy =
          L2AclPolicyRestCtrl.L2AclPolicyMapper.INSTANCE.ServiceL2AclPolicy2L2AclPolicy(
              randomL2AclPolicy(tenant, p -> {}));

      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.ADD_L2ACL_POLICY_V1_1,
          randomName(),
          new RequestParams(),
          l2AclPolicy);

      assertThat(repositoryUtil.find(L2AclPolicy.class, l2AclPolicy.getId(), tenant.getId()))
          .isNotNull()
          .matches(p -> p.getName().equals(l2AclPolicy.getName()))
          .matches(p -> p.getAccess() == l2AclPolicy.getAccess())
          .matches(p -> p.getDescription().equals(l2AclPolicy.getDescription()))
          .extracting(L2AclPolicy::getMacAddresses, InstanceOfAssertFactories.list(String.class))
          .hasSize(l2AclPolicy.getMacAddresses().size())
          .allMatch(
              mac -> l2AclPolicy.getMacAddresses().stream().anyMatch(s -> s.equalsIgnoreCase(mac)));

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(
              m -> m.getPayload().getOperationsList(),
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .hasSize(1)
          .first()
          .matches(o -> o.getAction() == Action.ADD)
          .matches(o -> o.getId().equals(l2AclPolicy.getId()));

      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(
              ViewmodelCollector::getOperationsList,
              InstanceOfAssertFactories.list(Operations.class))
          .hasSize(1)
          .first()
          .matches(o -> o.getOpType() == OpType.ADD)
          .matches(o -> o.getId().equals(l2AclPolicy.getId()));

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.ADD_L2ACL_POLICY));
    }
  }
}
