package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlistEntry;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
public class DdccmClientIsolationProfileOperationBuilderTest {

  @Autowired
  private DdccmClientIsolationAllowlistOperationBuilder builder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyMspTenantId");

  @Test
  public void testAddClientIsolationProfile() {
    var clientIsolationProfile = clientIsolationProfile();

    var txChanges = spy(TxChangesReader.class);

    doReturn(List.of(new NewTxEntity<>(clientIsolationProfile))).when(txChanges).getNewEntities();

    var operations = builder.build(new NewTxEntity<>(clientIsolationProfile), emptyTxChanges());

    assertEquals(1, operations.size());

    assertClientIsolationProfileOperation(Action.ADD, clientIsolationProfile,
        Objects.requireNonNull(
            operations.stream().filter(op -> clientIsolationProfile.getId().equals(op.getId()))
                .findFirst()
                .orElse(null)));
  }

  @Test
  public void testModifyClientIsolationProfile() {
    var clientIsolationProfile = clientIsolationProfile();

    var txChanges = spy(TxChangesReader.class);

    doReturn(List.of(new ModifiedTxEntity<>(clientIsolationProfile, Collections.emptySet())))
        .when(txChanges).getModifiedEntities();

    var operations = builder
        .build(new ModifiedTxEntity<>(clientIsolationProfile, Collections.emptySet()),
            emptyTxChanges());

    assertEquals(1, operations.size());

    assertClientIsolationProfileOperation(Action.MODIFY, clientIsolationProfile,
        Objects.requireNonNull(
            operations.stream().filter(op -> clientIsolationProfile.getId().equals(op.getId()))
                .findFirst()
                .orElse(null)));
  }

  @Test
  public void testDeleteClientIsolationProfile() {
    var clientIsolationProfile = clientIsolationProfile();

    var txChanges = spy(TxChangesReader.class);

    doReturn(List.of(new DeletedTxEntity<>(clientIsolationProfile))).when(txChanges)
        .getDeletedEntities();

    var operations = builder.build(new DeletedTxEntity<>(clientIsolationProfile), emptyTxChanges());

    assertEquals(1, operations.size());

    assertClientIsolationProfileOperation(Action.DELETE, clientIsolationProfile,
        Objects.requireNonNull(
            operations.stream().filter(op -> clientIsolationProfile.getId().equals(op.getId()))
                .findFirst()
                .orElse(null)));
  }

  private ClientIsolationAllowlist clientIsolationProfile() {
    var clientIsolationAllowlist = new ClientIsolationAllowlist(genUUID());
    clientIsolationAllowlist.setName(randomName());
    clientIsolationAllowlist.setDescription(randomName());
    var clientIsolationAllowlistEntry = new ClientIsolationAllowlistEntry();
    clientIsolationAllowlistEntry.setDescription(randomName());
    clientIsolationAllowlistEntry.setMac(randomMacAddress());
    clientIsolationAllowlist.setTenant(new Tenant(TxCtxHolder.get().getTenant()));
    clientIsolationAllowlist.setAllowlist(List.of(clientIsolationAllowlistEntry));
    return clientIsolationAllowlist;
  }

  private void assertClientIsolationProfileOperation(Action action,
      ClientIsolationAllowlist profile,
      Operation operation) {
    var clientIsolationWhitelist = operation.getVenueClientIsolationWhitelist();

    assertEquals(action, operation.getAction());
    assertNotNull(clientIsolationWhitelist);
    assertEquals(profile.getId(), clientIsolationWhitelist.getId());

    assertEquals(Action.DELETE != action ? 1 : 0, clientIsolationWhitelist.getWhitelistCount());

    if (Action.DELETE != action) {
      assertEquals(profile.getName(), clientIsolationWhitelist.getName());
      assertClientIsolationAllowlistEntry(profile.getAllowlist().get(0),
          clientIsolationWhitelist.getWhitelist(0));
    }
  }

  private void assertClientIsolationAllowlistEntry(ClientIsolationAllowlistEntry allowlistEntry,
      com.ruckus.acx.ddccm.protobuf.wifi.ClientIsolationWhitelistEntry ddccmAllowlistEntry) {
    assertEquals(allowlistEntry.getMac(), ddccmAllowlistEntry.getMac());
  }

  private static String genUUID() {
    return UUID.randomUUID().toString();
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public DdccmClientIsolationAllowlistOperationBuilder ddccmClientIsolationAllowlistOperationBuilder() {
      var builder = spy(DdccmClientIsolationAllowlistOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }
  }
}
