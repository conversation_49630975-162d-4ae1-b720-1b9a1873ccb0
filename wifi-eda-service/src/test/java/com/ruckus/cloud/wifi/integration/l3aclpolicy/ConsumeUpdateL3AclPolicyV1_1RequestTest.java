package com.ruckus.cloud.wifi.integration.l3aclpolicy;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.L3AclPolicyTestFixture.randomL3AclPolicy;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.L3AclPolicyRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3IpPort;
import com.ruckus.cloud.wifi.eda.servicemodel.L3Rule;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.stream.Stream;
import org.apache.commons.lang3.builder.CompareToBuilder;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeUpdateL3AclPolicyV1_1RequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeUpdateL3AclPolicyV1_1Message {
    @Test
    void givenPolicyNotExists(Tenant tenant) {
      final var l3AclPolicy =
          L3AclPolicyRestCtrl.L3AclPolicyMapper.INSTANCE.ServiceL3AclPolicy2L3AclPolicy(
              randomL3AclPolicy(tenant, p -> {}));

      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.UPDATE_L3ACL_POLICY_V1_1,
                      randomName(),
                      new RequestParams().addPathVariable("l3AclPolicyId", l3AclPolicy.getId()),
                      l3AclPolicy))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(ObjectNotFoundException.class);

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_L3ACL_POLICY));
    }

    @Nested
    class GivenPolicyExists {
      @Nested
      class GivenPolicyNameChanged {
        @Test
        void givenPolicyAlreadyActivatedOnSomeAccessControlProfiles(
            Tenant tenant, L3AclPolicy targetL3AclPolicy) {
          final var accessControlProfileIds =
              Stream.generate(AccessControlProfileTestFixture::randomAccessControlProfile)
                  .limit(3)
                  .peek(p -> p.setTenant(tenant))
                  .peek(p -> p.setL3AclPolicy(targetL3AclPolicy))
                  .peek(p -> p.setL3AclEnable(true))
                  .peek(p -> repositoryUtil.createOrUpdate(p, tenant.getId(), randomTxId()))
                  .map(AbstractBaseEntity::getId)
                  .toList();

          final var l3AclPolicy =
              L3AclPolicyRestCtrl.L3AclPolicyMapper.INSTANCE.ServiceL3AclPolicy2L3AclPolicy(
                  randomL3AclPolicy(tenant, p -> p.setId(targetL3AclPolicy.getId())));

          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.UPDATE_L3ACL_POLICY_V1_1,
              randomName(),
              new RequestParams().addPathVariable("l3AclPolicyId", l3AclPolicy.getId()),
              l3AclPolicy);

          assertThat(repositoryUtil.find(L3AclPolicy.class, l3AclPolicy.getId(), tenant.getId()))
              .isNotNull()
              .matches(p -> p.getName().equals(l3AclPolicy.getName()))
              .matches(p -> p.getDefaultAccess() == l3AclPolicy.getDefaultAccess())
              .matches(p -> p.getDescription().equals(l3AclPolicy.getDescription()))
              .extracting(L3AclPolicy::getL3Rules, InstanceOfAssertFactories.list(L3Rule.class))
              .hasSize(l3AclPolicy.getL3Rules().size())
              .allMatch(
                  rule -> l3AclPolicy.getL3Rules().stream().anyMatch(r -> sameL3Rule(rule, r)));

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .hasSize(1)
              .first()
              .matches(o -> o.getAction() == Action.MODIFY)
              .matches(o -> o.getId().equals(l3AclPolicy.getId()));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .hasSize(accessControlProfileIds.size() + 1)
              .allMatch(o -> o.getOpType() == OpType.MOD)
              .satisfies(
                  operations ->
                      assertThat(operations)
                          .filteredOn(
                              o ->
                                  o.getIndex()
                                      .equals(
                                          EsConstants.Index
                                              .ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME))
                          .hasSize(accessControlProfileIds.size())
                          .allMatch(
                              o ->
                                  o.getDocOrDefault(
                                          EsConstants.Key.L3_ACL_POLICY_ID,
                                          Value.getDefaultInstance())
                                      .getStringValue()
                                      .equals(l3AclPolicy.getId()))
                          .allMatch(
                              o ->
                                  o.getDocOrDefault(
                                          EsConstants.Key.L3_ACL_POLICY_NAME,
                                          Value.getDefaultInstance())
                                      .getStringValue()
                                      .equals(l3AclPolicy.getName()))
                          .extracting(Operations::getId)
                          .containsExactlyInAnyOrderElementsOf(accessControlProfileIds))
              .filteredOn(
                  o -> o.getIndex().equals(EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME))
              .singleElement()
              .matches(o -> o.getOpType() == OpType.MOD)
              .matches(o -> o.getId().equals(l3AclPolicy.getId()));

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_L3ACL_POLICY));
        }

        @Test
        void givenPolicyNotActivatedOnAccessControlProfile(
            Tenant tenant, L3AclPolicy targetL3AclPolicy) {
          final var l3AclPolicy =
              L3AclPolicyRestCtrl.L3AclPolicyMapper.INSTANCE.ServiceL3AclPolicy2L3AclPolicy(
                  randomL3AclPolicy(tenant, p -> p.setId(targetL3AclPolicy.getId())));

          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.UPDATE_L3ACL_POLICY_V1_1,
              randomName(),
              new RequestParams().addPathVariable("l3AclPolicyId", l3AclPolicy.getId()),
              l3AclPolicy);

          assertThat(repositoryUtil.find(L3AclPolicy.class, l3AclPolicy.getId(), tenant.getId()))
              .isNotNull()
              .matches(p -> p.getName().equals(l3AclPolicy.getName()))
              .matches(p -> p.getDefaultAccess() == l3AclPolicy.getDefaultAccess())
              .matches(p -> p.getDescription().equals(l3AclPolicy.getDescription()))
              .extracting(L3AclPolicy::getL3Rules, InstanceOfAssertFactories.list(L3Rule.class))
              .hasSize(l3AclPolicy.getL3Rules().size())
              .allMatch(
                  rule -> l3AclPolicy.getL3Rules().stream().anyMatch(r -> sameL3Rule(rule, r)));

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .hasSize(1)
              .first()
              .matches(o -> o.getAction() == Action.MODIFY)
              .matches(o -> o.getId().equals(l3AclPolicy.getId()));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .hasSize(1)
              .first()
              .matches(o -> o.getOpType() == OpType.MOD)
              .matches(o -> o.getId().equals(l3AclPolicy.getId()));

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_L3ACL_POLICY));
        }
      }

      @Test
      void givenPolicyNameNotChanged(Tenant tenant, L3AclPolicy targetL3AclPolicy) {
        final var l3AclPolicy =
            L3AclPolicyRestCtrl.L3AclPolicyMapper.INSTANCE.ServiceL3AclPolicy2L3AclPolicy(
                randomL3AclPolicy(
                    tenant,
                    p -> {
                      p.setId(targetL3AclPolicy.getId());
                      p.setName(targetL3AclPolicy.getName());
                    }));

        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.UPDATE_L3ACL_POLICY_V1_1,
            randomName(),
            new RequestParams().addPathVariable("l3AclPolicyId", l3AclPolicy.getId()),
            l3AclPolicy);

        assertThat(repositoryUtil.find(L3AclPolicy.class, l3AclPolicy.getId(), tenant.getId()))
            .isNotNull()
            .matches(p -> p.getName().equals(l3AclPolicy.getName()))
            .matches(p -> p.getDefaultAccess() == l3AclPolicy.getDefaultAccess())
            .matches(p -> p.getDescription().equals(l3AclPolicy.getDescription()))
            .extracting(L3AclPolicy::getL3Rules, InstanceOfAssertFactories.list(L3Rule.class))
            .hasSize(l3AclPolicy.getL3Rules().size())
            .allMatch(rule -> l3AclPolicy.getL3Rules().stream().anyMatch(r -> sameL3Rule(rule, r)));

        assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(
                m -> m.getPayload().getOperationsList(),
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .hasSize(1)
            .first()
            .matches(o -> o.getAction() == Action.MODIFY)
            .matches(o -> o.getId().equals(l3AclPolicy.getId()));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(1)
            .first()
            .matches(o -> o.getOpType() == OpType.MOD)
            .matches(o -> o.getId().equals(l3AclPolicy.getId()));

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_L3ACL_POLICY));
      }
    }
  }

  private boolean sameL3Rule(
      L3Rule serviceModel, com.ruckus.cloud.wifi.eda.viewmodel.L3Rule viewModel) {
    final var builder =
        new CompareToBuilder()
            .append(serviceModel.getAccess(), viewModel.getAccess())
            .append(serviceModel.getPriority(), viewModel.getPriority())
            .append(serviceModel.getDescription(), viewModel.getDescription())
            .append(serviceModel.getProtocol(), viewModel.getProtocol())
            .append(serviceModel.getCustomProtocol(), viewModel.getCustomProtocol());

    return builder.toComparison() == 0
        && sameL3IpPort(serviceModel.getSource(), viewModel.getSource())
        && sameL3IpPort(serviceModel.getDestination(), viewModel.getDestination());
  }

  private boolean sameL3IpPort(
      L3IpPort serviceModel, com.ruckus.cloud.wifi.eda.viewmodel.L3IpPort viewModel) {
    if (serviceModel == null && viewModel == null) {
      return true;
    } else if (serviceModel == null || viewModel == null) {
      return false;
    }
    return new CompareToBuilder()
            .append(serviceModel.getIp(), viewModel.getIp())
            .append(serviceModel.getPort(), viewModel.getPort())
            .append(serviceModel.getIpMask(), viewModel.getIpMask())
            .append(serviceModel.getEnableIpSubnet(), viewModel.getEnableIpSubnet())
            .toComparison()
        == 0;
  }
}
