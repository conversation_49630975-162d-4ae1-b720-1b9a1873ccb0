package com.ruckus.cloud.wifi.postmigration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class V0_1180__NetworkTypePostMigrationConsumerTest {

  @Autowired
  protected MessageCaptors messageCaptors;

  @Autowired
  private V0_1180__NetworkTypePostMigrationConsumer postMigrationConsumer;

  @Nested
  class PostMigrationNetworkData {

    private String networkId;
    private String venueId;
    private String networkVenueId;

    @BeforeEach
    void givenOpenNetworkAndRelatedEntitiesPersistedInDb(@OpenNetwork Network network, Venue venue,
        @DefaultApGroup ApGroup apGroup,
        @ScheduledNetworkVenue NetworkVenue networkVenue, NetworkApGroup networkApGroup,
        NetworkApGroupRadio networkApGroupRadio) {
      networkId = network.getId();
      venueId = venue.getId();
      networkVenueId = networkVenue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkVenueId", networkVenueId);
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload() {
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setNetworkId(networkId);
      payload.setVenueId(venueId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz));
      payload.setScheduler(dummyNetworkVenueScheduler());
      return payload;
    }

    @Test
    @ApiAction(CfgAction.UPDATE_NETWORK_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) throws Exception {

      postMigrationConsumer.run(null);
      // create network
      var result1 = messageCaptors.getDdccmMessageCaptor().getValue(txCtx.getTenant());
      // migration
      var result2 = messageCaptors.getDdccmMessageCaptor().getValue(txCtx.getTenant());

      assertThat(result1.getPayload()).isNotNull();
      assertThat(result2.getPayload()).isNotNull()
          .matches(r -> r.getOperationsCount() == 1)
          .extracting(r -> r.getOperations(0))
          .matches(o -> o.hasWlanVenue())
          .extracting(o -> o.getWlanVenue())
          .matches(wlanVenue -> wlanVenue.hasNetworkType())
          .matches(wlanVenue -> wlanVenue.getNetworkType().getValue().equals("OPEN"));
    }
  }

  private static com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler dummyNetworkVenueScheduler() {
    final var scheduler = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler();
    scheduler.setType(SchedulerTypeEnum.ALWAYS_ON);
    return scheduler;
  }
}
