package com.ruckus.cloud.wifi.notification;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruckus.cloud.wifi.client.kairos.KairosApiClient;
import com.ruckus.cloud.wifi.client.kairos.dto.RegisterKairosRequest;
import com.ruckus.cloud.wifi.kafka.consumer.WifiTriggerConsumer.RPC;
import com.ruckus.cloud.wifi.kafka.publisher.WifiSchedulePublisher;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockReset;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
@WifiJpaDataTest
public class ApUpgradeRoutineJobTest {

  @SpyBean private ObjectMapper objectMapper;

  @MockBean(reset = MockReset.BEFORE)
  private WifiSchedulePublisher wifiStatePublisher;

  @SpyBean private ApUpgradeRoutineJob routineJob;

  @MockBean(reset = MockReset.BEFORE)
  private KairosApiClient kairosApiClient;

  @Test
  public void testRegisterRoutineJobs() throws Exception {
    //Given
    when(kairosApiClient.hasScheduleJob(anyString())).thenReturn(true);
    // When
    routineJob.registerRoutineJobs();

    // Then
    ArgumentCaptor<Map<String, Object>> headerCaptor = ArgumentCaptor.forClass(Map.class);
    ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);

    verify(wifiStatePublisher, atLeast(2))
        .publishKairosRegister(messageCaptor.capture(), headerCaptor.capture());

    Map<String, Object> reminderHeader = headerCaptor.getAllValues().get(0);
    Map<String, Object> finishHeader = headerCaptor.getAllValues().get(1);
    Map<String, Object> backupUpgradeRetryHeader = headerCaptor.getAllValues().get(2);

    RegisterKairosRequest reminderMessage =
        objectMapper.readValue(messageCaptor.getAllValues().get(0), RegisterKairosRequest.class);
    RegisterKairosRequest finishMessage =
        objectMapper.readValue(messageCaptor.getAllValues().get(1), RegisterKairosRequest.class);
    RegisterKairosRequest backupUpgradeRetryMessage =
        objectMapper.readValue(messageCaptor.getAllValues().get(2), RegisterKairosRequest.class);

    log.info(objectMapper.writeValueAsString(reminderHeader));
    log.info(objectMapper.writeValueAsString(reminderMessage));

    log.info(objectMapper.writeValueAsString(finishHeader));
    log.info(objectMapper.writeValueAsString(finishMessage));

    log.info(objectMapper.writeValueAsString(backupUpgradeRetryHeader));
    log.info(objectMapper.writeValueAsString(backupUpgradeRetryMessage));

    Assertions.assertEquals(RPC.NOTIFY_SCHEDULE_REMINDER.toString(), reminderHeader.get("jobName"));
    Assertions.assertEquals(
        RPC.NOTIFY_SCHEDULE_REMINDER.toString(),
        reminderMessage.getKafkaTarget().getData().get("methodName"));

    Assertions.assertEquals(
        RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.toString(), finishHeader.get("jobName"));
    Assertions.assertEquals(
        RPC.NOTIFY_SCHEDULE_UPGRADE_FINISH.toString(),
        finishMessage.getKafkaTarget().getData().get("methodName"));

    Assertions.assertEquals(
        RPC.UPGRADE_BACKUP_PARTITION.toString(), backupUpgradeRetryHeader.get("jobName"));
    Assertions.assertEquals(
        RPC.UPGRADE_BACKUP_PARTITION.toString(),
        backupUpgradeRetryMessage.getKafkaTarget().getData().get("methodName"));
    verify(kairosApiClient, times(1)).deleteScheduleJob(ApUpgradeRoutineJob.BAK_UPGRADE_REQUEST_ID);
  }
}
