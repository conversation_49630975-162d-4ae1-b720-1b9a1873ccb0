package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest(showSql = false)
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO network (id, tenant, type) VALUES
        ('b1a2c59f92d243cba7ca5b4bfe83c289', '6700bc51acf84c4aa9510df2ca00b5f4', 'open'),
        ('b1a2c59f92d243cba7ca5b4bfe83c290', '6700bc51acf84c4aa9510df2ca00b5f4', 'open');
    INSERT INTO wifi_calling_service_profile (id, tenant) VALUES
        ('34ffc50c45284c0a8a3ce5809d9c1c76', '6700bc51acf84c4aa9510df2ca00b5f4'),
        ('34ffc50c45284c0a8a3ce5809d9c1c77', '6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO wifi_calling_service_profile_network (id, tenant, network, wifi_calling_service_profile) VALUES
        ('d888fd374a974e548af80a7ecbc74230', '6700bc51acf84c4aa9510df2ca00b5f4', 'b1a2c59f92d243cba7ca5b4bfe83c289', '34ffc50c45284c0a8a3ce5809d9c1c76'),
        ('e619cccb6bd74075ba0b220611969aab', '6700bc51acf84c4aa9510df2ca00b5f4', 'b1a2c59f92d243cba7ca5b4bfe83c290', '34ffc50c45284c0a8a3ce5809d9c1c77');
    """)
public class WifiCallingServiceProfileNetworkRepositoryTest {
  private static final String TENANT_ID = "6700bc51acf84c4aa9510df2ca00b5f4";
  private static final String NETWORK_ID = "b1a2c59f92d243cba7ca5b4bfe83c289";
  private static final String NETWORK_ID1 = "b1a2c59f92d243cba7ca5b4bfe83c290";
  private static final String WIFI_CALLING_PROXY_SERVICE_PROFILE_ID = "34ffc50c45284c0a8a3ce5809d9c1c76";
  private static final String WIFI_CALLING_PROXY_SERVICE_PROFILE_ID1 = "34ffc50c45284c0a8a3ce5809d9c1c77";

  @Autowired
  private WifiCallingServiceProfileNetworkRepository repository;

  @Test
  public void findByNetworkIdAndTenantIdTest() {
    assertThat(repository.findByNetworkIdAndTenantId(NETWORK_ID, TENANT_ID))
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .extracting(wifiCallingServiceProfileNetwork ->
            wifiCallingServiceProfileNetwork.getWifiCallingServiceProfile().getId())
        .isEqualTo(WIFI_CALLING_PROXY_SERVICE_PROFILE_ID);
  }

  @Test
  public void findByTenantIdAndNetworkIdInTest() {
    assertThat(repository.findByTenantIdAndNetworkIdIn(TENANT_ID, List.of(NETWORK_ID, NETWORK_ID1)))
        .isNotNull()
        .hasSize(2)
        .extracting(wifiCallingServiceProfileNetwork ->
            wifiCallingServiceProfileNetwork.getWifiCallingServiceProfile().getId())
        .contains(WIFI_CALLING_PROXY_SERVICE_PROFILE_ID)
        .contains(WIFI_CALLING_PROXY_SERVICE_PROFILE_ID1);
  }

  @Test
  public void findByWifiCallingServiceProfileIdAndTenantIdTest() {
    assertThat(repository.findByWifiCallingServiceProfileIdAndTenantId(WIFI_CALLING_PROXY_SERVICE_PROFILE_ID, TENANT_ID))
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .extracting(wifiCallingServiceProfileNetwork ->
            wifiCallingServiceProfileNetwork.getNetwork().getId())
        .isEqualTo(NETWORK_ID);
  }

  @Test
  public void findByWifiCallingServiceProfileInAndTenantIdTest() {
    assertThat(repository.findByWifiCallingServiceProfileIdInAndTenantId(
        List.of(WIFI_CALLING_PROXY_SERVICE_PROFILE_ID, WIFI_CALLING_PROXY_SERVICE_PROFILE_ID1),
        TENANT_ID))
        .isNotNull()
        .hasSize(2)
        .extracting(wifiCallingServiceProfileNetwork ->
            wifiCallingServiceProfileNetwork.getNetwork().getId())
        .contains(NETWORK_ID)
        .contains(NETWORK_ID1);
  }
}
