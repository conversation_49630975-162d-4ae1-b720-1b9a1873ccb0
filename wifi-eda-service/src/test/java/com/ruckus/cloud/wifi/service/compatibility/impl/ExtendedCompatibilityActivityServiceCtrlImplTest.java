package com.ruckus.cloud.wifi.service.compatibility.impl;

import static com.ruckus.cloud.wifi.requirement.ApFeatureRequirement.BSS_COLORING;
import static com.ruckus.cloud.wifi.requirement.ApFeatureRequirement.QOS_MIRRORING;
import static com.ruckus.cloud.wifi.service.compatibility.impl.ExtendedCompatibilityActivityServiceCtrlImpl.COMPATIBILITY_ACTIVITY_EXPIRED_DAYS;
import static com.ruckus.cloud.wifi.test.fixture.ApFirmwareUpgradeRequestTestFixture.randomApFirmwareUpgradeRequest;
import static com.ruckus.cloud.wifi.test.fixture.CompatibilityActivityTestFixture.randomCompatibilityActivity;
import static org.apache.commons.lang3.RandomStringUtils.randomAlphanumeric;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willAnswer;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.ruckus.cloud.wifi.client.viewmodel.ViewmodelClientGrpc;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApStateEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractTenantAwareBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.CompatibilityActivity;
import com.ruckus.cloud.wifi.eda.servicemodel.CompatibilityActivityInfo;
import com.ruckus.cloud.wifi.eda.servicemodel.CompatibilityQueryData;
import com.ruckus.cloud.wifi.eda.servicemodel.CompatibilityQueryData.Fields;
import com.ruckus.cloud.wifi.eda.servicemodel.CompatibilityQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.kafka.publisher.activity.ActivityCfgChangeRespPublisher;
import com.ruckus.cloud.wifi.repository.ApFirmwareUpgradeRequestRepository;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.CompatibilityActivityInfoRepository;
import com.ruckus.cloud.wifi.repository.CompatibilityActivityRepository;
import com.ruckus.cloud.wifi.repository.NetworkApGroupRepository;
import com.ruckus.cloud.wifi.requirement.ApFeatureRequirement;
import com.ruckus.cloud.wifi.requirement.ApModelFamily;
import com.ruckus.cloud.wifi.requirement.feature.ApFeatureRegistry;
import com.ruckus.cloud.wifi.requirement.feature.ApIotFeature;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.servicemodel.projection.ApCompatibilityProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.utils.VersionUtil;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModel;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.assertj.core.api.BDDAssertions;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

@Slf4j
@WifiUnitTest
class ExtendedCompatibilityActivityServiceCtrlImplTest {

  @MockBean
  private ApRepository apRepository;
  @MockBean
  private NetworkApGroupRepository networkApGroupRepository;
  @MockBean
  private ApFirmwareUpgradeRequestRepository firmwareUpgradeRequestRepository;
  @MockBean
  private CompatibilityActivityRepository compatibilityActivityRepository;
  @MockBean
  private CompatibilityActivityInfoRepository compatibilityActivityInfoRepository;
  @MockBean
  private ViewmodelClientGrpc viewmodelClientGrpc;
  @MockBean
  private ActivityCfgChangeRespPublisher activityPublisher;
  @MockBean
  private ApFeatureRegistry featureRegistry;
  @MockBean
  private ApIotFeature apIotFeature;
  @Captor
  private ArgumentCaptor<List<CompatibilityActivity>> argumentCaptor;
  @Captor
  private ArgumentCaptor<CompatibilityActivityInfo> infoArgumentCaptor;
  @Captor
  private ArgumentCaptor<Date> dateArgumentCaptor;

  @SpyBean
  private ExtendedCompatibilityActivityServiceCtrlImpl unit;

  @Test
  void testGetIncompatibleDevicesByFeatureName() {
    // given
    List<ApCompatibilityProjection> devices = List.of(
        // unsupported due to firmware version
        new ApCompatibilityProjection("000000001", "7.1.0.510.930", "H670"),
        // unsupported due to model
        new ApCompatibilityProjection("000000002", "7.1.0.510.1005", "T350C"),
        // supported due to model & version
        new ApCompatibilityProjection("000000003", "7.1.0.510.1005", "R770"),
        // supported due to model & version
        new ApCompatibilityProjection("000000004", "7.1.0.520.1005", "T670")
    );

    when(featureRegistry.getFeatureByName("AP IoT")).thenReturn(apIotFeature);
    when(apIotFeature.isDeviceSupport(any())).thenAnswer(invocation -> {
       ApFirmwareModel model = invocation.getArgument(0);
       return !model.getModel().equals("T350C") && VersionUtil.compareVersions(model.getFirmware(), "7.1.0.510.1005") >= 0;
    });

    // when
    Set<String> incompatibleDevices = unit.getIncompatibleDevices(devices, "AP IoT");

    // then
    assertThat(incompatibleDevices).containsAll(List.of("000000001", "000000002"));
  }

  @Test
  void shouldCreateCompatibilityActivitySuccessfully(Tenant tenant, Venue venue) {
    var devices = Lists.newArrayList(
        randomApFirmwareUpgradeRequest(
            tenant, venue,
            c -> {
              c.setSerialNumber("000000000001");
              c.setSourceVersion("7.1.0.510.930");
              c.setModel("R770");
            }),
        randomApFirmwareUpgradeRequest(
            tenant, venue,
            c -> {
              c.setSerialNumber("000000000002");
              c.setSourceVersion("7.1.0.510.1005");
              c.setModel("R770");
            })
        );
    var device1 = devices.get(0);
    var onlineDevice = new ApDTO();
    onlineDevice.setSerialNumber(device1.getSerialNumber());
    onlineDevice.setModel(device1.getModel());
    onlineDevice.setFirmware(device1.getSourceVersion());
    onlineDevice.setState(ApStateEnum.Operational);
    onlineDevice.setName("onlineDevice");
    var onlineDevices = Lists.newArrayList(onlineDevice);

    when(firmwareUpgradeRequestRepository.findByTenantIdAndVenueId(eq(tenant.getId()), eq(venue.getId()), any()))
        .thenAnswer(invocation -> devices)
        .thenAnswer(invocation -> Lists.newArrayList());
    willAnswer(invocation -> onlineDevices)
        .given(viewmodelClientGrpc)
        .getApsBySerialNumber(anyString(), anyString(), anySet(), anyList());
    when(featureRegistry.getFeatureByName("AP IoT")).thenReturn(apIotFeature);
    when(apIotFeature.getFeatureName()).thenReturn("AP IoT");
    when(apIotFeature.isDeviceSupport(any())).thenAnswer(invocation -> {
      ApFirmwareModel model = invocation.getArgument(0);
      return !model.getModel().equals("T350C") && VersionUtil.compareVersions(model.getFirmware(), "7.1.0.510.1005") >= 0;
    });

    // when
    unit.createCompatibilityActivityByVenue(venue.getId(), "AP IoT");

    // then
    verify(compatibilityActivityRepository, times(1)).saveAll(argumentCaptor.capture());
    verify(compatibilityActivityInfoRepository, times(1)).save(infoArgumentCaptor.capture());
    verify(activityPublisher, times(1)).publishCfgStatusSuccess(anyString());

    BDDAssertions.then(argumentCaptor.getValue())
        .isNotNull()
        .hasSize(1)
        .matches(c -> c.stream()
            .allMatch(
                a -> Objects.equals(a.getApName(), onlineDevice.getName())
                && Objects.equals(a.getApId(), onlineDevice.getSerialNumber())
                && CollectionUtils.isEqualCollection(
                    a.getIncompatibleFeatures(), Lists.newArrayList("AP IoT"))
        ));
    BDDAssertions.then(infoArgumentCaptor.getValue())
        .isNotNull()
        .matches(i -> Objects.equals(i.getActivityId(), TxCtxHolder.txId()))
        .matches(i -> i.getImpactedCount() == 2)
        .extracting(AbstractTenantAwareBaseEntity::getTenant)
        .isNotNull()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(TxCtxHolder.tenantId());
  }

  @Nested
  class WhenGetCompatibilityByActivity {

    private final String activityId = CommonTestFixture.randomTxId();

    @Test
    void givenNoRecordExists(Tenant tenant) {
      willReturn(Page.empty())
          .given(compatibilityActivityRepository)
          .findByActivityIdAndTenantId(anyString(), anyString(), any(Pageable.class));
      willReturn(Optional.empty())
          .given(compatibilityActivityInfoRepository)
          .findByActivityIdAndTenantId(anyString(), anyString());

      final var request = new QueryRequest();
      request.setPage(1);
      request.setPageSize(10);

      BDDAssertions.then(unit.getCompatibilityByActivity(activityId, request))
          .isNotNull()
          .matches(d -> d.getPage() == 1)
          .matches(d -> d.getTotalCount() == 0)
          .matches(d -> d.getTotalPages() == 1)
          .matches(d -> d.getImpactedCount() == 0)
          .satisfies(
              d ->
                  assertThat(d.getFields())
                      .containsExactlyInAnyOrder(
                          Fields.ID, Fields.NAME, Fields.INCOMPATIBLEFEATURES))
          .extracting(
              CompatibilityQueryResponse::getData,
              InstanceOfAssertFactories.list(CompatibilityQueryData.class))
          .isEmpty();

      final var captor = ArgumentCaptor.forClass(Pageable.class);
      then(compatibilityActivityRepository)
          .should()
          .findByActivityIdAndTenantId(eq(activityId), eq(tenant.getId()), captor.capture());
      then(compatibilityActivityInfoRepository)
          .should()
          .findByActivityIdAndTenantId(eq(activityId), eq(tenant.getId()));

      assertThat(captor.getValue())
          .isNotNull()
          .matches(p -> p.getPageNumber() == 0)
          .matches(p -> p.getPageSize() == 10);
    }

    @Test
    void givenSomeRecordExist(Tenant tenant) {
      final var compatibilityActivities =
          Stream.generate(
                  () -> randomCompatibilityActivity(tenant, c -> c.setActivityId(activityId)))
              .limit(10)
              .toList();
      final var impactedCount = RandomUtils.nextInt(10, 100);
      final var info = mock(CompatibilityActivityInfo.class);

      willAnswer(
          invocation ->
              new PageImpl<>(
                  compatibilityActivities, invocation.getArgument(2, Pageable.class), 100))
          .given(compatibilityActivityRepository)
          .findByActivityIdAndTenantId(anyString(), anyString(), any(Pageable.class));
      willReturn(Optional.of(info))
          .given(compatibilityActivityInfoRepository)
          .findByActivityIdAndTenantId(anyString(), anyString());
      willReturn(impactedCount).given(info).getImpactedCount();

      final var request = new QueryRequest();
      request.setPage(1);
      request.setPageSize(10);

      BDDAssertions.then(unit.getCompatibilityByActivity(activityId, request))
          .isNotNull()
          .matches(d -> d.getPage() == 1)
          .matches(d -> d.getTotalCount() == 100)
          .matches(d -> d.getTotalPages() == 10)
          .matches(d -> d.getImpactedCount() == impactedCount)
          .satisfies(
              d ->
                  assertThat(d.getFields())
                      .containsExactlyInAnyOrder(
                          Fields.ID, Fields.NAME, Fields.INCOMPATIBLEFEATURES))
          .extracting(
              CompatibilityQueryResponse::getData,
              InstanceOfAssertFactories.list(CompatibilityQueryData.class))
          .hasSize(10)
          .allMatch(
              c ->
                  compatibilityActivities.stream()
                      .anyMatch(
                          a ->
                              Objects.equals(c.getId(), a.getApId())
                                  && Objects.equals(c.getName(), a.getApName())
                                  && Objects.equals(
                                      c.getIncompatibleFeatures(), a.getIncompatibleFeatures())));

      final var captor = ArgumentCaptor.forClass(Pageable.class);
      then(compatibilityActivityRepository)
          .should()
          .findByActivityIdAndTenantId(eq(activityId), eq(tenant.getId()), captor.capture());
      then(compatibilityActivityInfoRepository)
          .should()
          .findByActivityIdAndTenantId(eq(activityId), eq(tenant.getId()));
      then(info).should().getImpactedCount();

      assertThat(captor.getValue())
          .isNotNull()
          .matches(p -> p.getPageNumber() == 0)
          .matches(p -> p.getPageSize() == 10);
    }
  }

  @Nested
  class WhenCreateCompatibilityActivityByVenue {

    @Test
    void givenIncompatibleDevicesExist(Tenant tenant, Venue venue) {
      // given
      var device1 = randomApFirmwareUpgradeRequest(
          tenant, venue,
          c -> {
            c.setSerialNumber("000000001");
            c.setSourceVersion("6.0.0.0.0");
            c.setModel("R550");
          });
      var device2 = randomApFirmwareUpgradeRequest(
          tenant, venue,
          c -> {
            c.setSerialNumber("000000002");
            c.setVenueId(venue.getId());
            c.setSourceVersion("7.0.0.0.0");
            c.setModel("R770");
          });
      var devices = Lists.newArrayList(device1, device2);

      var onlineDevice1 = new ApDTO();
      onlineDevice1.setSerialNumber(device1.getSerialNumber());
      onlineDevice1.setModel(device1.getModel());
      onlineDevice1.setFirmware(device1.getSourceVersion());
      onlineDevice1.setState(ApStateEnum.Operational);
      onlineDevice1.setName("apName");
      var onlineDevices = Lists.newArrayList(onlineDevice1);

      when(firmwareUpgradeRequestRepository.findByTenantIdAndVenueId(anyString(), anyString(), any()))
          .thenAnswer(invocation -> devices)
          .thenAnswer(invocation -> Lists.newArrayList());
      willAnswer(invocation -> onlineDevices)
          .given(viewmodelClientGrpc)
          .getApsBySerialNumber(anyString(), anyString(), anySet(), anyList());

      // when
      unit.createCompatibilityActivityByVenue(venue.getId(), BSS_COLORING);

      // then
      verify(compatibilityActivityRepository, times(1)).saveAll(argumentCaptor.capture());
      verify(compatibilityActivityInfoRepository, times(1)).save(infoArgumentCaptor.capture());
      verify(activityPublisher, times(1)).publishCfgStatusSuccess(anyString());
      BDDAssertions.then(argumentCaptor.getValue())
          .isNotNull()
          .hasSize(1)
          .matches(c -> c.stream().allMatch(
              a -> Objects.equals(a.getApName(), onlineDevice1.getName())
                  && Objects.equals(a.getApId(), onlineDevice1.getSerialNumber())
                  && CollectionUtils.isEqualCollection(
                      a.getIncompatibleFeatures(),
                  Lists.newArrayList(BSS_COLORING.getFeatureName()))
          ));
      BDDAssertions.then(infoArgumentCaptor.getValue())
          .isNotNull()
          .matches(i -> Objects.equals(i.getActivityId(), TxCtxHolder.txId()))
          .matches(i -> i.getImpactedCount() == 2)
          .extracting(AbstractTenantAwareBaseEntity::getTenant)
          .isNotNull()
          .extracting(AbstractBaseEntity::getId)
          .isEqualTo(TxCtxHolder.tenantId());
    }
  }

  @Nested
  class WhenCreateCompatibilityActivityByNetwork {

    @Test
    void givenIncompatibleDevicesExist(Tenant tenant, Network network) {
      // given
      var device1 = new ApCompatibilityProjection("000000001", "6.0.0.0.0", "R550");
      var device2 = new ApCompatibilityProjection("000000002", "7.0.0.0.0", "R770");
      var devices = Lists.newArrayList(device1, device2);

      var onlineDevice1 = new ApDTO();
      onlineDevice1.setSerialNumber(device1.id());
      onlineDevice1.setModel(device1.model());
      onlineDevice1.setFirmware(device1.firmware());
      onlineDevice1.setState(ApStateEnum.Operational);
      onlineDevice1.setName("apName");
      var onlineDevices = Lists.newArrayList(onlineDevice1);

      when(apRepository.findByTenantIdAndApGroupIdIn(anyString(), anyList(), any()))
        .thenAnswer(invocation -> devices)
        .thenAnswer(invocation -> Lists.newArrayList());
      willAnswer(invocation -> onlineDevices)
        .given(viewmodelClientGrpc)
        .getApsBySerialNumber(anyString(), anyString(), anySet(), anyList());

      // when
      unit.createCompatibilityActivityByNetwork(network.getId(), QOS_MIRRORING);

      // then
      verify(compatibilityActivityRepository, times(1)).saveAll(argumentCaptor.capture());
      verify(compatibilityActivityInfoRepository, times(1)).save(infoArgumentCaptor.capture());
      verify(activityPublisher, times(1)).publishCfgStatusSuccess(anyString());
      BDDAssertions.then(argumentCaptor.getValue())
        .isNotNull()
        .hasSize(1)
        .matches(c -> c.stream().allMatch(
          a -> Objects.equals(a.getApName(), onlineDevice1.getName())
            && Objects.equals(a.getApId(), onlineDevice1.getSerialNumber())
            && CollectionUtils.isEqualCollection(
            a.getIncompatibleFeatures(),
            Lists.newArrayList(QOS_MIRRORING.getFeatureName()))
        ));
      BDDAssertions.then(infoArgumentCaptor.getValue())
          .isNotNull()
          .matches(i -> Objects.equals(i.getActivityId(), TxCtxHolder.txId()))
          .matches(i -> i.getImpactedCount() == 2)
          .extracting(AbstractTenantAwareBaseEntity::getTenant)
          .isNotNull()
          .extracting(AbstractBaseEntity::getId)
          .isEqualTo(TxCtxHolder.tenantId());
    }
  }

  @Nested
  class WhenGetIncompatibleDevices {
    private final ApFeatureRequirement[] requirements =
        new ApFeatureRequirement[] {BSS_COLORING, QOS_MIRRORING};
    private final List<String> versions = List.of("7.0.1.0.100", "7.1.2.1.99");
    private final List<String> models =
        Stream.generate(() -> randomAlphanumeric(4)).limit(3).toList();

    @BeforeEach
    void beforeEach() {
      ApModelFamily.WIFI_7.updateModels(models);
    }

    @Test
    void givenAllDevicesSupported() {
      final var devices =
          Stream.generate(CommonTestFixture::randomSerialNumber)
              .limit(10)
              .map(
                  id ->
                      new ApCompatibilityProjection(
                          id,
                          versions.get(RandomUtils.nextInt(0, versions.size())),
                          models.get(RandomUtils.nextInt(0, models.size()))))
              .toList();

      BDDAssertions.then(unit.getIncompatibleDevices(devices, requirements)).isEmpty();
    }

    @Nested
    class GivenSomeDevicesSupported {
      @Test
      void givenSomeDevicesVersionInvalid() {
        final var invalidDevices =
            Stream.generate(CommonTestFixture::randomSerialNumber)
                .limit(10)
                .map(
                    id ->
                        new ApCompatibilityProjection(
                            id, "no_fw_version", models.get(RandomUtils.nextInt(0, models.size()))))
                .toList();
        final var supportedDevices =
            Stream.generate(CommonTestFixture::randomSerialNumber)
                .limit(10)
                .map(
                    id ->
                        new ApCompatibilityProjection(
                            id,
                            versions.get(RandomUtils.nextInt(0, versions.size())),
                            models.get(RandomUtils.nextInt(0, models.size()))))
                .toList();
        final var devices =
            Stream.concat(invalidDevices.stream(), supportedDevices.stream())
                .collect(Collectors.toList());
        Collections.shuffle(devices);

        BDDAssertions.then(unit.getIncompatibleDevices(devices, requirements)).isEmpty();
      }

      @Test
      void thenReturnNotSupportedIds() {
        final var notSupportedVersions = List.of("6.1.1.99.101", "6.2.2.104.0");
        final var notSupportedDevices =
            Stream.generate(CommonTestFixture::randomSerialNumber)
                .limit(10)
                .map(
                    id ->
                        new ApCompatibilityProjection(
                            id,
                            notSupportedVersions.get(
                                RandomUtils.nextInt(0, notSupportedVersions.size())),
                            models.get(RandomUtils.nextInt(0, models.size()))))
                .toList();
        final var supportedDevices =
            Stream.generate(CommonTestFixture::randomSerialNumber)
                .limit(10)
                .map(
                    id ->
                        new ApCompatibilityProjection(
                            id,
                            versions.get(RandomUtils.nextInt(0, versions.size())),
                            models.get(RandomUtils.nextInt(0, models.size()))))
                .toList();

        final var devices =
            Stream.concat(notSupportedDevices.stream(), supportedDevices.stream())
                .collect(Collectors.toList());
        Collections.shuffle(devices);

        BDDAssertions.then(unit.getIncompatibleDevices(devices, requirements))
            .isNotEmpty()
            .hasSize(notSupportedDevices.size())
            .allMatch(id -> notSupportedDevices.stream().anyMatch(d -> Objects.equals(d.id(), id)));
      }
    }
  }

  @Test
  void whenDeleteExpiredCompatibilityActivities() {
    final var activityCount = RandomUtils.nextLong(1, 10000);
    final var activityInfoCount = RandomUtils.nextLong(1, 10000);
    willReturn(activityCount)
        .given(compatibilityActivityRepository)
        .deleteByCreatedDateBefore(any(Date.class));
    willReturn(activityInfoCount)
        .given(compatibilityActivityInfoRepository)
        .deleteByCreatedDateBefore(any(Date.class));

    unit.deleteExpiredCompatibilityActivities();

    then(compatibilityActivityRepository)
        .should()
        .deleteByCreatedDateBefore(dateArgumentCaptor.capture());
    then(compatibilityActivityInfoRepository)
        .should()
        .deleteByCreatedDateBefore(dateArgumentCaptor.capture());

    assertThat(dateArgumentCaptor.getAllValues().get(0))
        .isCloseTo(
            Instant.now().minus(COMPATIBILITY_ACTIVITY_EXPIRED_DAYS, ChronoUnit.DAYS),
            TimeUnit.MINUTES.toMillis(3));
    assertThat(dateArgumentCaptor.getAllValues().get(1))
        .isCloseTo(
            Instant.now().minus(COMPATIBILITY_ACTIVITY_EXPIRED_DAYS, ChronoUnit.DAYS),
            TimeUnit.MINUTES.toMillis(3));
  }
}
