package com.ruckus.cloud.wifi.requirement.feature;

import static org.apache.commons.lang3.RandomUtils.nextBoolean;

import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class QosMirroringFeatureTest {

  @SpyBean
  private QosMirroringFeature unit;

  @Nested
  class WhenIsQosMirroringEnabled {

    @Test
    void givenWlanIsNull() {
      final var network = new Network();
      network.setWlan(null);

      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Nested
    class GivenWlanIsNotNull {

      @Test
      void givenAdvancedCustomizationIsNull() {
        final var wlan = new Wlan();
        wlan.setAdvancedCustomization(null);
        final var network = new Network();
        network.setWlan(wlan);

        BDDAssertions.then(unit.test(network)).isFalse();
      }

      @Nested
      class GivenAdvancedCustomizationIsNotNull {

        @Test
        void thenReturnEnabled() {
          final var enabled = nextBoolean();
          final var customization = new WlanAdvancedCustomization();
          customization.setQosMirroringEnabled(enabled);
          final var wlan = new Wlan();
          wlan.setAdvancedCustomization(customization);
          final var network = new Network();
          network.setWlan(wlan);

          BDDAssertions.then(unit.test(network)).isEqualTo(enabled);
        }
      }
    }
  }
}
