package com.ruckus.cloud.wifi.postmigration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.ruckus.acx.service.postmigration.kafka.PostMigrationConsumer;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Capabilities;
import com.ruckus.cloud.wifi.eda.servicemodel.CapabilitiesApModel;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.service.InternalFirmwareCapabilityService;
import com.ruckus.cloud.wifi.service.exception.CapabilityNotFoundException;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.transaction.PlatformTransactionManager;

@WifiJpaDataTest
public class V0_1259__ApVersionPostMigrationConsumerTest {

  @SpyBean
  private ApVersionRepository apVersionRepository;

  @MockBean
  private InternalFirmwareCapabilityService internalFirmwareCapabilityService;

  @Autowired
  private V0_1259__ApVersionPostMigrationConsumer postMigrationConsumer;

  @Test
  void testMigrateApVersion() throws CapabilityNotFoundException {
    List<String> modelsFor620 = List.of("R500", "R700", "R600");
    List<String> modelsFor624 = List.of("R650", "R550", "R750");
    List<String> modelsFor700 = List.of("R770", "R670");

    List<String> versionStrList = List.of("6.2.0.103.100", "6.2.0.103.102", "6.2.4.103.200", "7.0.0.105.300");
    List<ApVersion> versions = versionStrList.stream().map(versionStr -> {
      return apVersionRepository.save(ApVersionTestFixture.recommendedApVersion(versionStr, c -> {
      }));
    }).toList();

    doReturn(versions).when(apVersionRepository).findBySupportedApModelsIsNull();
    doReturn(newCapabilities(modelsFor620, "6.2.0.103.100")).when(internalFirmwareCapabilityService)
        .getCapabilities("6.2.0.103.100", false);
    doReturn(newCapabilities(modelsFor620, "6.2.0.103.102")).when(internalFirmwareCapabilityService)
        .getCapabilities("6.2.0.103.102", false);
    doReturn(newCapabilities(modelsFor624, "6.2.4.103.200")).when(internalFirmwareCapabilityService)
        .getCapabilities("6.2.4.103.200", false);
    doReturn(newCapabilities(modelsFor700, "7.0.0.105.300")).when(internalFirmwareCapabilityService)
        .getCapabilities("7.0.0.105.300", false);

    postMigrationConsumer.run(null);

    validateApVersionMigrateResult("6.2.0.103.100", modelsFor620);
    validateApVersionMigrateResult("6.2.0.103.102", modelsFor620);
    validateApVersionMigrateResult("6.2.4.103.200", modelsFor624);
    validateApVersionMigrateResult("7.0.0.105.300", modelsFor700);
  }

  private void validateApVersionMigrateResult(String version, List<String> models) {
    assertThat(apVersionRepository.findById(version))
        .isPresent()
        .get()
        .extracting(ApVersion::getSupportedApModels)
        .asList()
        .containsExactlyElementsOf(models.stream().sorted().toList());
  }

  private Capabilities newCapabilities(List<String> models, String version) {
    Capabilities capabilities = new Capabilities();
    capabilities.setApModels(
        models.stream().map(m -> newCapabilitiesApModel(version, m)).toList());
    return capabilities;
  }

  private CapabilitiesApModel newCapabilitiesApModel(String version, String model) {
    CapabilitiesApModel capabilitiesApModel = new CapabilitiesApModel();
    capabilitiesApModel.setModel(model);
    capabilitiesApModel.setVersion(version);
    return capabilitiesApModel;
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public PostMigrationConsumer V0_1259__ApVersionPostMigrationConsumer(ApVersionRepository apVersionRepository,
        InternalFirmwareCapabilityService internalFirmwareCapabilityService,
        PlatformTransactionManager platformTransactionManager) {
      return new V0_1259__ApVersionPostMigrationConsumer(apVersionRepository, internalFirmwareCapabilityService,
          platformTransactionManager);
    }

    @Bean
    @ConditionalOnMissingBean
    public InternalFirmwareCapabilityService internalFirmwareCapabilityService() {
      return mock(InternalFirmwareCapabilityService.class);
    }
  }
}
