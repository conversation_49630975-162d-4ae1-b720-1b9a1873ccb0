package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum.SAML;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.Operation;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.SneakyThrows;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class ConsumeSamlIdpProfileRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  private byte[] validateWifiCfgChangeMessageAndGetValue(TxCtx txCtx, String samlIdpProfileId) {
    final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(txCtx);
    assertThat(wifiCfgChangeMessageRecord)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .isNotNull()
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(txCtx.getTxId());
    var wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();
    assertThat(wifiConfigChange.getOperationList())
        .filteredOn(Operation::hasGuestNetwork)
        .anyMatch(operation -> {
          var network = operation.getGuestNetwork();
          System.out.println("txCtx = >" + network.getSamlIdpProfileId() + ", samlIdpProfileId = >" + samlIdpProfileId);
          return network.getSamlIdpProfileId().getValue().equals(samlIdpProfileId);
        });
    assertThat(wifiCfgChangeMessageRecord.getPayload()).isNotNull();
    return wifiCfgChangeMessageRecord.getPayload().toByteArray();
  }

  @Nested
  class ConsumeActivateSamlIdpProfileOnWifiNetworkRequestTest {

    String samlIdpProfileId;
    String networkId;

    @SneakyThrows
    @BeforeEach
    void setup(final Tenant tenant) {
      samlIdpProfileId = "samlIdpProfileId1";

      final var guestNetwork = network(GuestNetwork.class).generate();
      var guestPortal = Generators.guestPortal(SAML).generate();
      guestPortal.setNetwork(guestNetwork);
      guestNetwork.setGuestPortal(guestPortal);
      guestNetwork.getWlan().setNetwork(guestNetwork);
      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      networkId = guestNetwork.getId();
    }

    @ApiAction.RequestParams("activateSamlIdpProfile")
    private RequestParams activateSamlIdpProfileRequestParams() {
      return new RequestParams().addPathVariable("wifiNetworkId", networkId)
          .addPathVariable("samlIdpProfileId", samlIdpProfileId);
    }

    @Test
    @ApiAction(value = CfgAction.ACTIVATE_SAML_IDP_PROFILE_ON_WIFI_NETWORK,
        requestParams = @ApiAction.RequestParams("activateSamlIdpProfile"))
    void thenShouldActivateSamlIdpProfileOnWifiNetworkSuccessfully(TxCtx txCtx,
        @ApiAction.RequestParams("activateSamlIdpProfile") RequestParams requestParams) {
      assertThat(networkId).isEqualTo(requestParams.getPathVariables().get("wifiNetworkId"));
      assertThat(samlIdpProfileId).isEqualTo(
          requestParams.getPathVariables().get("samlIdpProfileId"));
      validateWifiCfgChangeMessageAndGetValue(txCtx, samlIdpProfileId);
    }
  }
}
