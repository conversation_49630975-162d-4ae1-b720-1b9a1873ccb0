package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_DOWNGRADE_VENUE_ABF_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = {WIFI_DOWNGRADE_VENUE_ABF_TOGGLE, WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE})
class ConsumeDowngradeVenueAbfRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private VenueLanPortRepository venueLanPortRepository;

  @Autowired
  private MessageCaptors messageCaptors;

  @Tag("EthernetPortProfileTest")
  @Test
  void downgradeVenueAbfTest(Tenant tenant, Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.100") ApVersion venueCurrentVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.556") ApVersion eolVersion) {
    // When
    String tenantId = tenant.getId();
    String requestId = randomTxId();
    createTenantFirmwareVersion(tenant, "eol-ap-2022-12", eolVersion);
    createVenueFirmwareVersion(venue, "eol-ap-2022-12", eolVersion);

    venue.setWifiFirmwareVersion(venueCurrentVersion);
    venue.setApPassword("1qaz@WSX");
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    createVenueApModelSpecificAttributes(tenant, venue, List.of("R560"));

    ApVersion targetVersion = eolVersion;
    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgExtendedAction.DOWNGRADE_VENUE_ABF,
        randomName(),
        new RequestParams().addPathVariable("venueId", venue.getId())
            .addPathVariable("firmwareVersion", targetVersion.getId()), StringUtils.EMPTY);

    // Then
    Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
    assertThat(updatedVenue)
        .isNotNull()
        .matches(v -> v.getWifiFirmwareVersion().getId().equals(targetVersion.getId()))
        .matches(v -> v.getModelSpecificAttributes().isEmpty());

    assertThat(venueLanPortRepository.findByTenantId(tenant.getId())).isNotNull().isEmpty();
    validateActivityCfgChangeRespMessage(tenantId, requestId);
  }

  @Tag("EthernetPortProfileTest")
  @Test
  void downgradeVenueAbfTest_whenVenueIn700105(Tenant tenant, Venue venue,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.556") ApVersion currentVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.556") ApVersion versionInAbf2,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.556") ApVersion versionInAbf1) {
    String tenantId = tenant.getId();
    String requestId = randomTxId();
    createTenantFirmwareVersion(tenant, "eol-ap-2022-12", versionInAbf1);
    createVenueFirmwareVersion(venue, "eol-ap-2022-12", versionInAbf1);
    createTenantFirmwareVersion(tenant, "ABF2-3R", versionInAbf2);
    createVenueFirmwareVersion(venue, "ABF2-3R", versionInAbf2);

    venue.setWifiFirmwareVersion(currentVersion);
    venue.setApPassword("1qaz@WSX");
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    createVenueApModelSpecificAttributes(tenant, venue, List.of("R770", "R350:R350E"));

    ApVersion targetVersion = versionInAbf2;
    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgExtendedAction.DOWNGRADE_VENUE_ABF,
        randomName(),
        new RequestParams().addPathVariable("venueId", venue.getId())
            .addPathVariable("firmwareVersion", targetVersion.getId()), StringUtils.EMPTY);

    // Then
    Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
    assertThat(updatedVenue)
        .isNotNull()
        .matches(v -> v.getWifiFirmwareVersion().getId().equals(targetVersion.getId()))
        .matches(v -> v.getModelSpecificAttributes().isEmpty());

    assertThat(venueLanPortRepository.findByTenantId(tenant.getId())).isNotNull().isEmpty();
    validateActivityCfgChangeRespMessage(tenantId, requestId);
  }

  void validateActivityCfgChangeRespMessage(String tenantId, String requestId) {
    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(
                    activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(CfgStatus.ConfigurationStatus.Status.OK))
                    .matches(msg -> msg.getStep().equals(CfgExtendedAction.DOWNGRADE_VENUE_ABF.key()))
                    .extracting(CfgStatus.ConfigurationStatus::getEventDate)
                    .isNotNull());
  }

  void createVenueApModelSpecificAttributes(Tenant tenant, Venue venue, List<String> models) {
    for (String model : models) {
      VenueApModelSpecificAttributes venueApModelSpecificAttributes = new VenueApModelSpecificAttributes();
      venueApModelSpecificAttributes.setVenue(venue);
      venueApModelSpecificAttributes.setModel(model);
      venueApModelSpecificAttributes.setTenant(tenant);
      venueApModelSpecificAttributes = repositoryUtil.createOrUpdate(venueApModelSpecificAttributes,
          tenant.getId(), randomTxId());

      EthernetPortProfile ethernetPortProfile = new EthernetPortProfile();
      ethernetPortProfile.setTenant(tenant);
      ethernetPortProfile.setApLanPortId(1);
      ethernetPortProfile = repositoryUtil.createOrUpdate(ethernetPortProfile, tenant.getId(),
          randomTxId());

      VenueLanPort venueLanPort = new VenueLanPort();
      venueLanPort.setVenueApModelSpecificAttributes(venueApModelSpecificAttributes);
      venueLanPort.setTenant(tenant);
      venueLanPort.setApLanPortProfile(ethernetPortProfile);
      repositoryUtil.createOrUpdate(venueLanPort, tenant.getId(), randomTxId());

      venueApModelSpecificAttributes.setLanPorts(List.of(venueLanPort));
      repositoryUtil.createOrUpdate(venueApModelSpecificAttributes, tenant.getId(), randomTxId());
    }
  }

  void createTenantFirmwareVersion(Tenant tenant, String branchType, ApVersion version) {
    TenantFirmwareVersion tft = new TenantFirmwareVersion();
    tft.setTenant(tenant);
    tft.setBranchType(branchType);
    tft.setLatestFirmwareVersion(version);
    repositoryUtil.createOrUpdate(tft, tenant.getId(), randomTxId());
  }

  void createVenueFirmwareVersion(Venue venue, String branchType, ApVersion version) {
    VenueFirmwareVersion vfv = new VenueFirmwareVersion();
    vfv.setTenant(venue.getTenant());
    vfv.setBranchType(branchType);
    vfv.setVenue(venue);
    vfv.setCurrentFirmwareVersion(version);
    repositoryUtil.createOrUpdate(vfv, venue.getTenant().getId(), randomTxId());
  }
}
