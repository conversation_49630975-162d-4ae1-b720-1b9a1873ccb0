package com.ruckus.cloud.wifi.service.housekeeping;


import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.api.rest.viewmodel.HouseKeepingResponse.TableDetail;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql("classpath:sql/housekeeping.sql")
public class HousekeepingServiceImplTest {

  @Autowired
  private EntityManager entityManager;

  @Autowired
  private HousekeepingService houseKeepingService;

  @Test
  public void testGetRevisionCount() {
    assertThat(houseKeepingService.getRevisionCount()).isGreaterThanOrEqualTo(22l);
  }

  @Test
  public void testQueryExpiredRevisionIds() {
    assertThat(
        houseKeepingService.queryExpiredRevisionIds(90, 100))
        .hasSizeGreaterThanOrEqualTo(11)
        .contains(3000001l, 3000002l, 3000003l, 3000004l, 3000005l, 3000006l,
            3000021l, 3000022l, 3000023l, 3000024l, 3000025l);
    assertThat(
        houseKeepingService.queryExpiredRevisionIds(90, 1))
        .hasSize(1);
  }

  @Test
  public void testDeleteRevisionData() {
    List<TableDetail> ret = houseKeepingService.deleteRevisionData(
        List.of(2247130l, 2247131l));

    assertThat(ret)
        .filteredOn(e -> e.getDeleteRecords() > 0)
        .hasSize(6)
        .extracting(TableDetail::getTableName)
        .contains("ap_aud", "venue_aud", "ap_group_aud", "network_aud", "revision_info",
            "revision_type_info");

    assertDeletedRecords(ret, "ap_aud", 2);
    assertDeletedRecords(ret, "venue_aud", 1);
    assertDeletedRecords(ret, "ap_group_aud", 1);
    assertDeletedRecords(ret, "network_aud", 1);
    assertDeletedRecords(ret, "revision_info", 2);
    assertDeletedRecords(ret, "revision_type_info", 5);
  }

  public void assertDeletedRecords(List<TableDetail> result, String tableName, int expected) {
    assertThat(result)
        .filteredOn(e -> e.getTableName().equals(tableName))
        .singleElement()
        .extracting(TableDetail::getDeleteRecords)
        .isEqualTo(expected);
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public HousekeepingService houseKeepingService(EntityManager entityManager) {
      return new HousekeepingServiceImpl(entityManager,true);
    }
  }
}
