package com.ruckus.cloud.wifi.entitylistener.ddccm.mapper;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpPoolVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.DhcpServiceProfileGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.DhcpServiceProfileVenueGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class DhcpServiceProfileMergeTest {

  private final static DhcpServiceProfileGenerator PROFILE_GENERATOR = Generators.dhcpServiceProfile();
  private final static DhcpServiceProfileVenueGenerator PROFILE_VENUE_GENERATOR = Generators.dhcpServiceProfileVenue();
  private final DhcpServiceProfileMerge dhcpServiceProfileMerge = Mappers.getMapper(
      DhcpServiceProfileMerge.class);

  @Test
  void testMerge() {
    DhcpServiceProfile source = PROFILE_GENERATOR.generate();
    source.setCreatedDate(new Date());
    source.setUpdatedDate(new Date());
    source.setId(randomId());
    source.setTenant(new Tenant(randomId()));
    source.setDhcpServiceProfileVenues(List.of(PROFILE_VENUE_GENERATOR.generate()));
    source.setDhcpConfigServiceProfile(new DhcpConfigServiceProfile());
    source.setDhcpPoolVenues(List.of(new DhcpPoolVenue()));

    DhcpServiceProfile target = PROFILE_GENERATOR.setDhcpServiceProfileVenues(
        list(always(PROFILE_VENUE_GENERATOR.generate()), 2)).generate();

    dhcpServiceProfileMerge.merge(source, target);

    assertThat(target)
        .matches(t -> t.getId().equals(target.getId()))
        .matches(t -> Objects.isNull(t.getCreatedDate()))
        .matches(t -> Objects.isNull(t.getUpdatedDate()))
        .matches(t -> Objects.isNull(t.getTenant()))
        .matches(t -> Objects.isNull(t.getDhcpPoolVenues()))
        .matches(t -> Objects.isNull(t.getDhcpConfigServiceProfile()))
        .matches(t -> Objects.isNull(t.getDhcpServiceProfileVenues()));
  }
}
