package com.ruckus.cloud.wifi.integration.networkactivation;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeShort;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.vlanPool;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertNetworkVenueSoftly;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTimeSlot;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.eda.service.VlanPoolTemplateServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.QueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPoolVenueDataQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkApGroup;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

@Tag("NetworkActivationTest")
@WifiIntegrationTest
class ConsumeAddNetworkActivationMappingRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private VlanPoolTemplateServiceCtrl vlanPoolTemplateServiceCtrl;

  @Nested
  class GivenOpenNetworkPersistedInDb {

    private String networkId;
    private List<Venue> venues;
    private int totalVenues = 3;

    @BeforeEach
    void givenVenuesPersistedInDb(Tenant tenant, @OpenNetwork Network network) {
      networkId = network.getId();
      venues = Stream.generate(() -> createVenueWithDefaultApGroup(tenant))
          .limit(totalVenues)
          .collect(Collectors.toList());
    }

    private ApGroup getDefaultApGroup(Venue venue) {
      return venue.getApGroups().stream().filter(ApGroup::getIsDefault)
          .findFirst().orElse(null);
    }

    private Venue createVenueWithDefaultApGroup(Tenant tenant) {
      var venue = repositoryUtil.createOrUpdate(VenueTestFixture.randomVenue(tenant),
          tenant.getId(),
          randomTxId());
      var apGroup = repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(venue, (e) -> e.setIsDefault(true)), tenant.getId(),
          randomTxId());
      venue.setApGroups(List.of(apGroup));
      return venue;
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK_VENUE_MAPPINGS)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE_MAPPINGS.key());
    }

    @Payload
    private List<NetworkActivationMapping> payload() {
      return venues.stream().map(venue -> {
        final var payload = new NetworkActivationMapping();
        payload.setId(randomId()); // autoGenerated is true in wifi-api
        payload.setNetworkId(networkId);
        payload.setVenueId(venue.getId());
        payload.setIsAllApGroups(true);
        payload.setAllApGroupsVlanId(
            rangeShort((short) 1, (short) 4094).setRandom(true).generate());
        payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
        payload.setScheduler(dummyNetworkVenueScheduler());
        return payload;
      }).collect(Collectors.toList());
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_MAPPINGS, payload = @Payload("WithSpecificApGroup"))
    void thenShouldHandleTheRequestWithSpecificApGroupSuccessfully(TxCtx txCtx,
        @Payload("WithSpecificApGroup") List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE_MAPPINGS.key());
    }

    @Payload("WithSpecificApGroup")
    private List<NetworkActivationMapping> payloadWithSpecificApGroup() {
      return venues.stream().map(venue -> {
        final var payload = new NetworkActivationMapping();
        payload.setId(randomId()); // autoGenerated is true in wifi-api
        payload.setNetworkId(networkId);
        payload.setVenueId(venue.getId());
        payload.setIsAllApGroups(false);
        final var networkApGroup = new NetworkApGroup();
        networkApGroup.setApGroupId(getDefaultApGroup(venue).getId());
        networkApGroup.setVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
        networkApGroup.setRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
        payload.setApGroups(List.of(networkApGroup));
        payload.setScheduler(dummyNetworkVenueScheduler());
        return payload;
      }).collect(Collectors.toList());
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_MAPPINGS, payload = @Payload("WithSpecificApGroup"))
    void thenShouldHandleNetworkApGroupCmnCfgCollectorSuccessfullyWhenUpdateNetworkName(TxCtx txCtx,
        @Payload("WithSpecificApGroup") List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE_MAPPINGS.key());

      // Update network name
      com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork openNetwork = com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.openNetwork().generate();
      openNetwork.setId(networkId);
      openNetwork.setName("network_123");

      var requestId = randomTxId();
      messageUtil.sendWifiCfgRequest(
          txCtx.getTenant(),
          requestId,
          CfgAction.UPDATE_NETWORK,
          randomName(),
          new RequestParams().addPathVariable("networkId", networkId),
          openNetwork);

      validateNetworkDeviceGroupCmnCfgCollectorMessage(txCtx.getTenant(), requestId, networkId);
    }

    @Nested
    class GivenVenueNetworkAlreadyExists {

      @BeforeEach
      void createVenueNetworkMapping(Tenant tenant, @OpenNetwork Network network) {
        networkId = network.getId();
        Venue venue = venues.get(0);
        repositoryUtil.createOrUpdate(NetworkVenueTestFixture.randomNetworkVenue(network, venue),
            tenant.getId(), randomTxId());
      }

      @Test
      @ApiAction(CfgAction.ADD_NETWORK_VENUE_MAPPINGS)
      void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
          @Payload List<NetworkActivationMapping> payload) {
        var changedPayload = payload.stream()
            .filter(mapping -> !mapping.getVenueId().equals(venues.get(0).getId()))
                .collect(Collectors.toList());
        validateResult(txCtx.getTenant(), txCtx.getTxId(), changedPayload,
            CfgAction.ADD_NETWORK_VENUE_MAPPINGS.key());
      }

      @Payload
      private List<NetworkActivationMapping> payload() {
        return venues.stream().map(venue -> {
          final var payload = new NetworkActivationMapping();
          payload.setId(randomId()); // autoGenerated is true in wifi-api
          payload.setNetworkId(networkId);
          payload.setVenueId(venue.getId());
          payload.setIsAllApGroups(true);
          payload.setAllApGroupsVlanId(
              rangeShort((short) 1, (short) 4094).setRandom(true).generate());
          payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
          payload.setScheduler(dummyNetworkVenueScheduler());
          return payload;
        }).collect(Collectors.toList());
      }
    }

    private void validateNetworkDeviceGroupCmnCfgCollectorMessage(String tenantId, String requestId, String networkId) {
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_DEVICE_GROUP_MAPPING.equals(op.getIndex()))
              .hasSize(3)
              .allMatch(op -> op.getOpType() == OpType.MOD)
              .allSatisfy(op -> {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> networkId.equals(doc.get(Key.NETWORK_ID).getStringValue()))
                    .matches(doc -> Boolean.FALSE.equals(doc.get(Key.IS_ALL_AP_GROUPS).getBoolValue()));
              }));
    }

    void validateResult(String tenantId, String requestId,
        List<NetworkActivationMapping> payloads, String apiAction) {
      final var revision = revisionService.changes(requestId, tenantId, apiAction);
      final var networkVenueIds = revision.getNewEntities().stream()
          .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
          .map(TxEntity::getId)
          .collect(Collectors.toList());
      var payloadVenueMap = payloads.stream()
          .collect(Collectors.toMap(NetworkActivationMapping::getVenueId, Function.identity()));
      for (var networkVenueId : networkVenueIds) {
        final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
        final var payload = payloadVenueMap.get(networkVenue.getVenue().getId());
        assertThat(networkVenue)
            .isNotNull()
            .matches(nv -> Objects.equals(nv.getNetwork().getId(), payload.getNetworkId()))
            .matches(nv -> Objects.equals(nv.getVenue().getId(), payload.getVenueId()));

        if (NetworkTypeEnum.GUEST.equals(networkVenue.getNetwork().getType())) {
          assertThat(networkVenue.getVenuePortal())
              .isNotNull();
        }
      }

      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                  .as("The ADD NetworkVenue operation count should be [%d]", payloads.size())
                  .hasSize(payloads.size())));

      payloadVenueMap.forEach((venueId, payload) -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(wifiCfgChangeMessage.getPayload().getOperationList())
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                .filteredOn(nv -> nv.getVenueId().getValue().equals(venueId))
                .singleElement()
                .satisfies(nv ->
                    assertNetworkVenueSoftly(payload.getNetworkId(), payload.getVenueId())));
      });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(payloads.size()));

      payloadVenueMap.forEach((venueId, payload) -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
                .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                .filteredOn(op -> op.getId().equals(payload.getId()))
                .singleElement()
                .matches(op -> op.getOpType() == OpType.ADD)
                .extracting(Operations::getDocMap)
                .matches(doc -> payload.getId().equals(doc.get(Key.ID).getStringValue()))
                .matches(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
                .matches(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                .matches(
                    doc -> payload.getNetworkId().equals(doc.get(Key.NETWORK_ID).getStringValue()))
                .matches(
                    doc -> payload.getVenueId().equals(doc.get(Key.VENUE_ID).getStringValue())));
      });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.ADD_NETWORK_VENUE_MAPPINGS))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count()
                      == payloads.size(),
                  String.format("The count of VenueSchedule operations should be [%d]",
                      payloads.size())));

      payloadVenueMap.forEach((venueId, payload) -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(ddccmCfgRequestMessage.getPayload().getOperationsList())
                .satisfies(op -> assertSoftly(softly -> {
                  softly.assertThat(op)
                      .filteredOn(Operation::hasWlanVenue)
                      .describedAs("The count of WlanVenue operations should be [%d]",
                          payloads.size())
                      .hasSize(payloads.size())
                      .filteredOn(wlanVenueOp -> wlanVenueOp.getId().equals(payload.getId()))
                      .singleElement()
                      .matches(wlanVenueOp -> wlanVenueOp.getAction() == Action.ADD,
                          String.format(
                              "The value of `action` field in WlanVenue operation should be %s",
                              Action.ADD))
                      .extracting(Operation::getWlanVenue)
                      .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                        alsoSoftly.assertThat(wlanVenue.getWlanId())
                            .isEqualTo(payload.getNetworkId());
                        alsoSoftly.assertThat(wlanVenue.getVenueId())
                            .isEqualTo(payload.getVenueId());
                      }));
                  softly.assertThat(op)
                      .filteredOn(Operation::hasWlanApGroup)
                      .filteredOn(wlanApGroupOp -> wlanApGroupOp.getWlanApGroup().getVenueId()
                          .equals(venueId))
                      .describedAs("The count of WlanApGroup operations should be %d",
                          StrictRadioTypeEnum.values().length)
                      .hasSize(StrictRadioTypeEnum.values().length)
                      .allMatch(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.ADD,
                          String.format(
                              "The value of `action` field in all WlanApGroup operations should be %s",
                              Action.ADD))
                      .extracting(Operation::getWlanApGroup)
                      .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                        if (BooleanUtils.isFalse(payload.getIsAllApGroups())) {
                          return;
                        }
                        // isAllApGroups != false
                        if (StringUtils.isEmpty(payload.getVlanPoolId())) {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                          if (payload.getAllApGroupsVlanId() != null) {
                            alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                            alsoSoftly.assertThat(wlanApGroup.getVlanId())
                                .extracting(Int32Value::getValue)
                                .isEqualTo(payload.getAllApGroupsVlanId().intValue());
                          } else {
                            alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                          }
                          return;
                        }
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                        alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                            .extracting(StringValue::getValue)
                            .isEqualTo(payload.getVlanPoolId());
                      }))
                      .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                        if (BooleanUtils.isNotFalse(payload.getIsAllApGroups())
                            || CollectionUtils.isEmpty(payload.getApGroups())) {
                          return;
                        }
                        // isAllApGroups == false
                        final var networkApGroupReq = payload.getApGroups().get(0);
                        if (StringUtils.isEmpty(networkApGroupReq.getVlanPoolId())) {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                          if (networkApGroupReq.getVlanId() != null) {
                            alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                            alsoSoftly.assertThat(wlanApGroup.getVlanId())
                                .extracting(Int32Value::getValue)
                                .isEqualTo(networkApGroupReq.getVlanId().intValue());
                          } else {
                            alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                          }
                          return;
                        }
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                        alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                            .extracting(StringValue::getValue)
                            .isEqualTo(networkApGroupReq.getVlanPoolId());
                      }));
                })));
      });
    }
  }

  @Nested
  class GivenDsaeNetworkPersistedInDb {

    private String networkId;
    private List<Venue> venues;
    private int totalVenues = 1;

    @BeforeEach
    void givenDsaeNetworkVenuesPersistedInDb(Tenant tenant) {
      final var dsaeNetwork1 = (DpskNetwork)network(DpskNetwork.class).generate();
      dsaeNetwork1.getWlan().setNetwork(dsaeNetwork1);
      dsaeNetwork1.setName("dsaeNetwork1");
      dsaeNetwork1.setDsaeNetworkPairId(dsaeNetwork1.getId());
      dsaeNetwork1.setIsDsaeServiceNetwork(true);
      dsaeNetwork1.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
      dsaeNetwork1.getWlan().setSsid(dsaeNetwork1.getName());
      repositoryUtil.createOrUpdate(dsaeNetwork1, tenant.getId(), randomTxId());

      final var dsaeNetwork2 = (DpskNetwork)network(DpskNetwork.class).generate();
      dsaeNetwork2.getWlan().setNetwork(dsaeNetwork2);
      dsaeNetwork2.setName("dsaeNetwork2");
      dsaeNetwork2.setDsaeNetworkPairId(dsaeNetwork1.getId());
      dsaeNetwork2.setIsDsaeServiceNetwork(false);
      dsaeNetwork2.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Personal);
      dsaeNetwork2.getWlan().setSsid(dsaeNetwork2.getName());
      repositoryUtil.createOrUpdate(dsaeNetwork2, tenant.getId(), randomTxId());

      networkId = dsaeNetwork1.getId();

      venues = Stream.generate(() -> createVenueWithDefaultApGroup(tenant))
          .limit(totalVenues)
          .collect(Collectors.toList());
    }

    private Venue createVenueWithDefaultApGroup(Tenant tenant) {
      var venue = repositoryUtil.createOrUpdate(VenueTestFixture.randomVenue(tenant),
          tenant.getId(),
          randomTxId());
      var apGroup = repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(venue, (e) -> e.setIsDefault(true)), tenant.getId(),
          randomTxId());
      venue.setApGroups(List.of(apGroup));
      return venue;
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK_VENUE_MAPPINGS)
    @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
    void thenShouldHandleAddDsaeNetworkVenueSuccessfully(TxCtx txCtx,
        @Payload List<NetworkActivationMapping> payload) {
      validateDsaeResult(txCtx.getTenant(), txCtx.getTxId(), payload, networkId,
          CfgAction.ADD_NETWORK_VENUE_MAPPINGS.key());
    }

    @Payload
    private List<NetworkActivationMapping> payload() {
      return venues.stream().map(venue -> {
        final var payload = new NetworkActivationMapping();
        payload.setId(randomId()); // autoGenerated is true in wifi-api
        payload.setNetworkId(networkId);
        payload.setVenueId(venue.getId());
        payload.setIsAllApGroups(true);
        payload.setAllApGroupsVlanId(
            rangeShort((short) 1, (short) 4094).setRandom(true).generate());
        payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
        payload.setScheduler(dummyNetworkVenueScheduler());
        return payload;
      }).collect(Collectors.toList());
    }

    private void validateDsaeResult(String tenantId, String requestId,
        List<NetworkActivationMapping> payloads, String networkId, String apiAction) {

      payloads.stream().filter(f -> f.getNetworkId().equals(networkId))
          .collect(Collectors.toList());
      var payloadVenueMap = payloads.stream()
          .collect(Collectors.toMap(NetworkActivationMapping::getVenueId, Function.identity()));
      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                  .filteredOn(op -> op.getNetworkVenue().getNetworkId().equals(StringValue.of(networkId)))
                  .as("The ADD NetworkVenue operation count should be [%d]", payloads.size())
                  .hasSize(payloads.size())));

      payloadVenueMap.forEach((venueId, payload) -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(wifiCfgChangeMessage.getPayload().getOperationList())
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                .filteredOn(nv -> nv.getVenueId().getValue().equals(venueId))
                .satisfies(nv ->
                    assertNetworkVenueSoftly(payload.getNetworkId(), payload.getVenueId())));
      });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(2));

      payloadVenueMap.forEach((venueId, payload) -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
                .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                .filteredOn(op -> op.getId().equals(payload.getId()))
                .allMatch(op -> op.getOpType() == OpType.ADD)
                .extracting(Operations::getDocMap)
                .allMatch(doc -> payload.getId().equals(doc.get(Key.ID).getStringValue()))
                .allMatch(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
                .allMatch(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                .allMatch(
                    doc -> payload.getNetworkId().equals(doc.get(Key.NETWORK_ID).getStringValue()))
                .allMatch(
                    doc -> payload.getVenueId().equals(doc.get(Key.VENUE_ID).getStringValue())));
      });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.ADD_NETWORK_VENUE_MAPPINGS))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count()
                      == payloads.size(),
                  String.format("The count of VenueSchedule operations should be [%d]",
                      payloads.size())));
    }
  }

  @Nested
  class GivenOweTransitionNetworkPersistedInDb {

    private String networkId;
    private String dpskNetworkId;
    private Venue venue;

    @BeforeEach
    void givenOweNetworkVenuesPersistedInDb(Tenant tenant) {
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      Network master = Generators.network(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .generate();
      Network slave = Generators.network(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .generate();
      //Add dpsk to test will there is exception
      Network dpsk = Generators.network(com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork.class)
          .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setIsOweMaster(true);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setOwePairNetworkId(
          slave.getId());
      master = repositoryUtil.createOrUpdate(master, master.getTenant().getId(), randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setOwePairNetworkId(
          master.getId());
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setIsOweMaster(false);
      slave = repositoryUtil.createOrUpdate(slave, slave.getTenant().getId(), randomTxId());

      dpsk.setTenant(tenant);
      dpsk.setName("dpskNoException");
      dpsk.getWlan().setNetwork(dpsk);
      dpsk.getWlan().setSsid("dpskNoException");
      dpsk.getWlan().setWlanSecurity(WlanSecurityEnum.WPA3);
      dpsk = repositoryUtil.createOrUpdate(dpsk, dpsk.getTenant().getId(), randomTxId());

      networkId = master.getId();
      dpskNetworkId = dpsk.getId();
      venue = createVenueWithDefaultApGroup(tenant);
    }

    private Venue createVenueWithDefaultApGroup(Tenant tenant) {
      var venue = repositoryUtil.createOrUpdate(VenueTestFixture.randomVenue(tenant),
          tenant.getId(),
          randomTxId());
      var apGroup = repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(venue, (e) -> e.setIsDefault(true)), tenant.getId(),
          randomTxId());
      venue.setApGroups(List.of(apGroup));
      return venue;
    }

    @Payload("allApRequest")
    private List<NetworkActivationMapping> allApRequest() {
      var openCase = new NetworkActivationMapping();
      openCase.setId(randomId()); // autoGenerated is true in wifi-api
      openCase.setNetworkId(networkId);
      openCase.setVenueId(venue.getId());
      openCase.setIsAllApGroups(true);
      openCase.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      openCase.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      openCase.setScheduler(dummyNetworkVenueScheduler());

      var dpskCase = new NetworkActivationMapping();
      dpskCase.setId(randomId());
      dpskCase.setNetworkId(dpskNetworkId);
      dpskCase.setVenueId(venue.getId());
      dpskCase.setIsAllApGroups(true);
      dpskCase.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dpskCase.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values()));

      return new ArrayList<>(List.of(openCase, dpskCase));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_MAPPINGS, payload = @Payload("allApRequest"))
    void testAllApRequest(TxCtx txCtx, @Payload("allApRequest") List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE_MAPPINGS.key());
    }

    private ApGroup getDefaultApGroup(Venue venue) {
      return venue.getApGroups().stream().filter(ApGroup::getIsDefault)
          .findFirst().orElse(null);
    }

    @Payload("sepcificApGroupRequest")
    private List<NetworkActivationMapping> sepcificApGroupRequest() {
      var openCase = new NetworkActivationMapping();
      openCase.setId(randomId()); // autoGenerated is true in wifi-api
      openCase.setNetworkId(networkId);
      openCase.setVenueId(venue.getId());
      openCase.setIsAllApGroups(false);
      var networkApGroup = new NetworkApGroup();
      networkApGroup.setApGroupId(getDefaultApGroup(venue).getId());
      networkApGroup.setVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      networkApGroup.setRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      openCase.setApGroups(List.of(networkApGroup));

      var dpskCase = new NetworkActivationMapping();
      dpskCase.setId(randomId());
      dpskCase.setNetworkId(dpskNetworkId);
      dpskCase.setVenueId(venue.getId());
      dpskCase.setIsAllApGroups(true);
      dpskCase.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dpskCase.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values()));
      return new ArrayList<>(List.of(openCase, dpskCase));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_MAPPINGS, payload = @Payload("sepcificApGroupRequest"))
    void testSepcificApGroupRequest(TxCtx txCtx, @Payload("sepcificApGroupRequest") List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE_MAPPINGS.key());
    }

    void validateResult(String tenantId, String requestId, List<NetworkActivationMapping> payloads, String apiAction) {
      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      final var revision = revisionService.changes(requestId, tenantId,
          ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueIds = revision.getAll().stream()
          .filter(entity -> entity instanceof NetworkVenue)
          .map(BaseEntity::getId).toList();
      assertTrue(networkVenueIds.size() > 0);

      for (var id : networkVenueIds) {
        final var networkVenue = repositoryUtil.find(NetworkVenue.class, id);
        assertNotEquals(networkVenue.getOweTransWlanId(), networkVenue.getApWlanId());
        assertNotEquals(0, networkVenue.getApWlanId());
        assertEquals(5, networkVenue.getNetworkApGroups().get(0).getNetworkApGroupRadios().size());
      }

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .hasSize(3)));

      payloads.forEach( payload -> assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload().getOperationList())
              .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
              .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
              .filteredOn(nv -> nv.getVenueId().getValue().equals(venue.getId()))
              .satisfies(nv ->
                  assertNetworkVenueSoftly(payload.getNetworkId(), payload.getVenueId()))));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(3));

      payloads.forEach(payload -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
                .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                .filteredOn(op -> op.getId().equals(payload.getId()))
                .allMatch(op -> op.getOpType() == OpType.ADD)
                .extracting(Operations::getDocMap)
                .allMatch(doc -> payload.getId().equals(doc.get(Key.ID).getStringValue()))
                .allMatch(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
                .allMatch(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                .allMatch(
                    doc -> payload.getNetworkId().equals(doc.get(Key.NETWORK_ID).getStringValue()))
                .allMatch(
                    doc -> payload.getVenueId().equals(doc.get(Key.VENUE_ID).getStringValue())));
      });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.ADD_NETWORK_VENUE_MAPPINGS))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .filteredOn(Operation::hasWlanVenue)
              .hasSize(3)
              .extracting(Operation::getWlanVenue)
              .extracting(WlanVenue::getOweTransWlanId)
              .isNotEmpty());
    }
  }

  @Nested
  class GivenOweTransitionGuestNetworkPersistedInDb {

    private String networkId;
    private String dpskNetworkId;
    private Venue venue;

    @BeforeEach
    void givenOweNetworkVenuesPersistedInDb(Tenant tenant) {
      var masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      var master = (com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork) com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class)
          .generate();
      var slave = (com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork) com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class)
          .generate();
      //Add dpsk to test will there is exception
      var dpsk = Generators.network(com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork.class)
          .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      master.setIsOweMaster(true);
      master.setOwePairNetworkId(slave.getId());
      master.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.WISPr)
              .setWisprPage(com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                  .wisprPage().setCustomExternalProvider(always(false)))
              .generate());
      master.getGuestPortal().setNetwork(master);
      master = repositoryUtil.createOrUpdate(master, master.getTenant().getId(), randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      slave.setOwePairNetworkId(master.getId());
      slave.setIsOweMaster(false);
      slave.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.WISPr)
              .setWisprPage(com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                  .wisprPage().setCustomExternalProvider(always(false)))
              .generate());
      slave.getGuestPortal().setNetwork(slave);
      repositoryUtil.createOrUpdate(slave, slave.getTenant().getId(), randomTxId());

      dpsk.setTenant(tenant);
      dpsk.setName("dpskNoException");
      dpsk.getWlan().setNetwork(dpsk);
      dpsk.getWlan().setSsid("dpskNoException");
      dpsk.getWlan().setWlanSecurity(WlanSecurityEnum.WPA3);
      dpsk = repositoryUtil.createOrUpdate(dpsk, dpsk.getTenant().getId(), randomTxId());

      networkId = master.getId();
      dpskNetworkId = dpsk.getId();
      venue = createVenueWithDefaultApGroup(tenant);
    }

    private Venue createVenueWithDefaultApGroup(Tenant tenant) {
      var venue = repositoryUtil.createOrUpdate(VenueTestFixture.randomVenue(tenant),
          tenant.getId(),
          randomTxId());
      var apGroup = repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(venue, (e) -> e.setIsDefault(true)), tenant.getId(),
          randomTxId());
      venue.setApGroups(List.of(apGroup));
      return venue;
    }

    @Payload("allApRequest")
    private List<NetworkActivationMapping> allApRequest() {
      var guestCase = new NetworkActivationMapping();
      guestCase.setId(randomId()); // autoGenerated is true in wifi-api
      guestCase.setNetworkId(networkId);
      guestCase.setVenueId(venue.getId());
      guestCase.setIsAllApGroups(true);
      guestCase.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      guestCase.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      guestCase.setScheduler(dummyNetworkVenueScheduler());

      var dpskCase = new NetworkActivationMapping();
      dpskCase.setId(randomId());
      dpskCase.setNetworkId(dpskNetworkId);
      dpskCase.setVenueId(venue.getId());
      dpskCase.setIsAllApGroups(true);
      dpskCase.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dpskCase.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values()));

      return new ArrayList<>(List.of(guestCase, dpskCase));
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_MAPPINGS, payload = @Payload("allApRequest"))
    void testAllApRequest(TxCtx txCtx, @Payload("allApRequest") List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE_MAPPINGS.key());
    }

    private ApGroup getDefaultApGroup(Venue venue) {
      return venue.getApGroups().stream().filter(ApGroup::getIsDefault)
          .findFirst().orElse(null);
    }

    @Payload("sepcificApGroupRequest")
    private List<NetworkActivationMapping> sepcificApGroupRequest() {
      var guestCase = new NetworkActivationMapping();
      guestCase.setId(randomId()); // autoGenerated is true in wifi-api
      guestCase.setNetworkId(networkId);
      guestCase.setVenueId(venue.getId());
      guestCase.setIsAllApGroups(false);
      var networkApGroup = new NetworkApGroup();
      networkApGroup.setApGroupId(getDefaultApGroup(venue).getId());
      networkApGroup.setVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      networkApGroup.setRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      guestCase.setApGroups(List.of(networkApGroup));

      var dpskCase = new NetworkActivationMapping();
      dpskCase.setId(randomId());
      dpskCase.setNetworkId(dpskNetworkId);
      dpskCase.setVenueId(venue.getId());
      dpskCase.setIsAllApGroups(true);
      dpskCase.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dpskCase.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values()));
      return new ArrayList<>(List.of(guestCase, dpskCase));
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_MAPPINGS, payload = @Payload("sepcificApGroupRequest"))
    void testSepcificApGroupRequest(TxCtx txCtx, @Payload("sepcificApGroupRequest") List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.ADD_NETWORK_VENUE_MAPPINGS.key());
    }

    void validateResult(String tenantId, String requestId, List<NetworkActivationMapping> payloads, String apiAction) {
      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      final var revision = revisionService.changes(requestId, tenantId,
          ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueIds = revision.getAll().stream()
          .filter(entity -> entity instanceof NetworkVenue)
          .map(BaseEntity::getId).toList();
      assertTrue(networkVenueIds.size() > 0);

      for (var id : networkVenueIds) {
        final var networkVenue = repositoryUtil.find(NetworkVenue.class, id);
        assertNotEquals(networkVenue.getOweTransWlanId(), networkVenue.getApWlanId());
        assertNotEquals(0, networkVenue.getApWlanId());
        assertEquals(5, networkVenue.getNetworkApGroups().get(0).getNetworkApGroupRadios().size());
      }

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .hasSize(3)));

      payloads.forEach( payload -> assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload().getOperationList())
              .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
              .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
              .filteredOn(nv -> nv.getVenueId().getValue().equals(venue.getId()))
              .satisfies(nv ->
                  assertNetworkVenueSoftly(payload.getNetworkId(), payload.getVenueId()))));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(3));

      payloads.forEach(payload -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
                .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                .filteredOn(op -> op.getId().equals(payload.getId()))
                .allMatch(op -> op.getOpType() == OpType.ADD)
                .extracting(Operations::getDocMap)
                .allMatch(doc -> payload.getId().equals(doc.get(Key.ID).getStringValue()))
                .allMatch(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
                .allMatch(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                .allMatch(
                    doc -> payload.getNetworkId().equals(doc.get(Key.NETWORK_ID).getStringValue()))
                .allMatch(
                    doc -> payload.getVenueId().equals(doc.get(Key.VENUE_ID).getStringValue())));
      });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.ADD_NETWORK_VENUE_MAPPINGS))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .filteredOn(Operation::hasWlanVenue)
              .hasSize(3)
              .extracting(Operation::getWlanVenue)
              .extracting(WlanVenue::getOweTransWlanId)
              .isNotEmpty());
    }
  }

  @Nested
  class GivenOpenNetworkTemplatePersistedInDb {

    private String networkId;
    private List<Venue> venues;
    private int totalVenues = 3;

    @BeforeEach
    void givenVenuesAndVlanPoolTemplatePersistedInDb(Tenant tenant, @OpenNetwork(isTemplate = true) Network network) {
      networkId = network.getId();
      venues = Stream.generate(() -> createVenueTemplateWithDefaultApGroup(tenant))
          .limit(totalVenues)
          .collect(Collectors.toList());
    }

    private ApGroup getDefaultApGroup(Venue venue) {
      return venue.getApGroups().stream().filter(ApGroup::getIsDefault)
          .findFirst().orElse(null);
    }

    private Venue createVenueTemplateWithDefaultApGroup(Tenant tenant) {
      var venue = repositoryUtil.createOrUpdate(VenueTestFixture.randomVenue(tenant,
              (v) -> v.setIsTemplate(true)),
          tenant.getId(),
          randomTxId());
      var apGroup = repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(venue, (e) -> {
            e.setIsDefault(true);
            e.setIsTemplate(true);
          }), tenant.getId(),
          randomTxId());
      venue.setApGroups(List.of(apGroup));
      return venue;
    }

    @Test
    @ApiAction(CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS.key());
    }

    @Payload
    private List<NetworkActivationMapping> payload() {
      return venues.stream().map(venue -> {
        final var payload = new NetworkActivationMapping();
        payload.setId(randomId()); // autoGenerated is true in wifi-api
        payload.setNetworkId(networkId);
        payload.setVenueId(venue.getId());
        payload.setIsAllApGroups(true);
        payload.setAllApGroupsVlanId(
            rangeShort((short) 1, (short) 4094).setRandom(true).generate());
        payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
        payload.setScheduler(dummyNetworkVenueScheduler());
        return payload;
      }).collect(Collectors.toList());
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS, payload = @Payload("WithSpecificApGroup"))
    void thenShouldHandleTheRequestWithSpecificApGroupSuccessfully(TxCtx txCtx,
        @Payload("WithSpecificApGroup") List<NetworkActivationMapping> payload) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS.key());
    }

    @Payload("WithSpecificApGroup")
    private List<NetworkActivationMapping> payloadWithSpecificApGroup() {
      return venues.stream().map(venue -> {
        final var payload = new NetworkActivationMapping();
        payload.setId(randomId()); // autoGenerated is true in wifi-api
        payload.setNetworkId(networkId);
        payload.setVenueId(venue.getId());
        payload.setIsAllApGroups(false);
        final var networkApGroup = new NetworkApGroup();
        networkApGroup.setApGroupId(getDefaultApGroup(venue).getId());
        networkApGroup.setVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
        networkApGroup.setRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
        payload.setApGroups(List.of(networkApGroup));
        payload.setScheduler(dummyNetworkVenueScheduler());
        return payload;
      }).collect(Collectors.toList());
    }

    @Tag("VlanPoolTemplateTest")
    @Nested
    class WithVlanPoolTemplateAssociation {

      private String vlanPoolTemplateId;

      @BeforeEach
      void givenOneVlanPoolPersistedInDb(final Tenant tenant) {
        final var vlanPool = vlanPool().setIsTemplate(always(true)).generate();
        repositoryUtil.createOrUpdate(vlanPool, tenant.getId(), randomTxId());
        vlanPoolTemplateId = vlanPool.getId();
      }

      @Test
      @ApiAction(CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS)
      void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx,
        @Payload List<NetworkActivationMapping> payload) {
        validateResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS.key());
      }

      @Payload
      private List<NetworkActivationMapping> payload() {
        return venues.stream().map(venue -> {
          final var payload = new NetworkActivationMapping();
          payload.setId(randomId()); // autoGenerated is true in wifi-api
          payload.setNetworkId(networkId);
          payload.setVenueId(venue.getId());
          payload.setIsAllApGroups(true);
          payload.setVlanPoolId(vlanPoolTemplateId);
          payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
          payload.setScheduler(dummyNetworkVenueScheduler());
          return payload;
        }).collect(Collectors.toList());
      }

      @Test
      @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS, payload = @Payload("WithVlanPoolTemplateInApGroup"))
      void thenShouldHandleTheRequestWithVlanPoolInApGroupSuccessfully(TxCtx txCtx,
        @Payload("WithVlanPoolTemplateInApGroup") List<NetworkActivationMapping> payload) {
        validateResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS.key());
        assertVlanPoolTemplateVenue(vlanPoolTemplateId, totalVenues);
      }

      @Payload("WithVlanPoolTemplateInApGroup")
      private List<NetworkActivationMapping> payloadWithVlanPoolInApGroup() {
        return venues.stream().map(venue -> {
          final var payload = new NetworkActivationMapping();
          payload.setId(randomId()); // autoGenerated is true in wifi-api
          payload.setNetworkId(networkId);
          payload.setVenueId(venue.getId());
          payload.setIsAllApGroups(false);
          final var networkApGroup = new NetworkApGroup();
          networkApGroup.setApGroupId(getDefaultApGroup(venue).getId());
          networkApGroup.setVlanPoolId(vlanPoolTemplateId);
          networkApGroup.setRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
          payload.setApGroups(List.of(networkApGroup));
          payload.setScheduler(dummyNetworkVenueScheduler());
          return payload;
        }).collect(Collectors.toList());
      }
    }

    void validateResult(String tenantId, String requestId,
        List<NetworkActivationMapping> payloads, String apiAction) {
      final var revision = revisionService.changes(requestId, tenantId, apiAction);
      final var networkVenueIds = revision.getNewEntities().stream()
          .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
          .map(TxEntity::getId)
          .collect(Collectors.toList());
      var payloadVenueMap = payloads.stream()
          .collect(Collectors.toMap(NetworkActivationMapping::getVenueId, Function.identity()));
      for (var networkVenueId : networkVenueIds) {
        final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
        final var payload = payloadVenueMap.get(networkVenue.getVenue().getId());
        assertThat(networkVenue)
            .isNotNull()
            .matches(nv -> Objects.equals(nv.getNetwork().getId(), payload.getNetworkId()))
            .matches(nv -> Objects.equals(nv.getVenue().getId(), payload.getVenueId()));

        if (NetworkTypeEnum.GUEST.equals(networkVenue.getNetwork().getType())) {
          assertThat(networkVenue.getVenuePortal())
              .isNotNull();
        }
      }

      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(wifiCfgChangeMessage.getPayload())
              .satisfies(msg -> assertSoftly(softly -> {
                softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
                softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
              }))
              .extracting(WifiConfigChange::getOperationList).asList()
              .isNotEmpty()
              .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
              .satisfies(ops -> assertThat(ops)
                  .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                  .filteredOn(
                      op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                  .as("The ADD NetworkVenue operation count should be [%d]", payloads.size())
                  .hasSize(payloads.size())));

      payloadVenueMap.forEach((venueId, payload) -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(wifiCfgChangeMessage.getPayload().getOperationList())
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
                .filteredOn(nv -> nv.getVenueId().getValue().equals(venueId))
                .singleElement()
                .satisfies(nv ->
                    assertNetworkVenueSoftly(payload.getNetworkId(), payload.getVenueId())));
      });

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(tenantId))
              .matches(msg -> msg.getRequestId().equals(requestId))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .hasSize(payloads.size()));

      payloadVenueMap.forEach((venueId, payload) -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
                .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                .filteredOn(op -> op.getId().equals(payload.getId()))
                .singleElement()
                .matches(op -> op.getOpType() == OpType.ADD)
                .extracting(Operations::getDocMap)
                .matches(doc -> payload.getId().equals(doc.get(Key.ID).getStringValue()))
                .matches(doc -> EsConstants.Value.VENUE_NETWORK_MAP.equals(doc.get(Key.TYPE)))
                .matches(doc -> tenantId.equals(doc.get(Key.TENANT_ID).getStringValue()))
                .matches(
                    doc -> payload.getNetworkId().equals(doc.get(Key.NETWORK_ID).getStringValue()))
                .matches(
                    doc -> payload.getVenueId().equals(doc.get(Key.VENUE_ID).getStringValue())));
      });

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.OK))
              .matches(msg -> msg.getStep().equals(ApiFlowNames.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());

      messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(ddccmCfgRequestMessage.getPayload())
              .extracting(WifiConfigRequest::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operation.class::cast)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()),
                      String.format("The value of `requestId` field in CommonInfo should be [%s]",
                          requestId))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()),
                      String.format("The value of `tenantId` field in CommonInfo should be [%s]",
                          tenantId))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE,
                      String.format("The value of `sender` field in CommonInfo should be [%s]",
                          ServiceType.WIFI_SERVICE)))
              .matches(ops -> ops.stream().filter(Operation::hasVenueSchedule).count()
                      == payloads.size(),
                  String.format("The count of VenueSchedule operations should be [%d]",
                      payloads.size())));

      payloadVenueMap.forEach((venueId, payload) -> {
        assertThatNoException().isThrownBy(() ->
            assertThat(ddccmCfgRequestMessage.getPayload().getOperationsList())
                .satisfies(op -> assertSoftly(softly -> {
                  softly.assertThat(op)
                      .filteredOn(Operation::hasWlanVenue)
                      .describedAs("The count of WlanVenue operations should be [%d]",
                          payloads.size())
                      .hasSize(payloads.size())
                      .filteredOn(wlanVenueOp -> wlanVenueOp.getId().equals(payload.getId()))
                      .singleElement()
                      .matches(wlanVenueOp -> wlanVenueOp.getAction() == Action.ADD,
                          String.format(
                              "The value of `action` field in WlanVenue operation should be %s",
                              Action.ADD))
                      .extracting(Operation::getWlanVenue)
                      .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                        alsoSoftly.assertThat(wlanVenue.getWlanId())
                            .isEqualTo(payload.getNetworkId());
                        alsoSoftly.assertThat(wlanVenue.getVenueId())
                            .isEqualTo(payload.getVenueId());
                      }));
                  softly.assertThat(op)
                      .filteredOn(Operation::hasWlanApGroup)
                      .filteredOn(wlanApGroupOp -> wlanApGroupOp.getWlanApGroup().getVenueId()
                          .equals(venueId))
                      .describedAs("The count of WlanApGroup operations should be %d",
                          StrictRadioTypeEnum.values().length)
                      .hasSize(StrictRadioTypeEnum.values().length)
                      .allMatch(wlanApGroupOp -> wlanApGroupOp.getAction() == Action.ADD,
                          String.format(
                              "The value of `action` field in all WlanApGroup operations should be %s",
                              Action.ADD))
                      .extracting(Operation::getWlanApGroup)
                      .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                        if (BooleanUtils.isFalse(payload.getIsAllApGroups())) {
                          return;
                        }
                        // isAllApGroups != false
                        if (StringUtils.isEmpty(payload.getVlanPoolId())) {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                          if (payload.getAllApGroupsVlanId() != null) {
                            alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                            alsoSoftly.assertThat(wlanApGroup.getVlanId())
                                .extracting(Int32Value::getValue)
                                .isEqualTo(payload.getAllApGroupsVlanId().intValue());
                          } else {
                            alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                          }
                          return;
                        }
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                        alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                            .extracting(StringValue::getValue)
                            .isEqualTo(payload.getVlanPoolId());
                      }))
                      .allSatisfy(wlanApGroup -> assertSoftly(alsoSoftly -> {
                        if (BooleanUtils.isNotFalse(payload.getIsAllApGroups())
                            || CollectionUtils.isEmpty(payload.getApGroups())) {
                          return;
                        }
                        // isAllApGroups == false
                        final var networkApGroupReq = payload.getApGroups().get(0);
                        if (StringUtils.isEmpty(networkApGroupReq.getVlanPoolId())) {
                          alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isFalse();
                          if (networkApGroupReq.getVlanId() != null) {
                            alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isTrue();
                            alsoSoftly.assertThat(wlanApGroup.getVlanId())
                                .extracting(Int32Value::getValue)
                                .isEqualTo(networkApGroupReq.getVlanId().intValue());
                          } else {
                            alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                          }
                          return;
                        }
                        alsoSoftly.assertThat(wlanApGroup.hasVlanId()).isFalse();
                        alsoSoftly.assertThat(wlanApGroup.hasVlanPoolId()).isTrue();
                        alsoSoftly.assertThat(wlanApGroup.getVlanPoolId())
                            .extracting(StringValue::getValue)
                            .isEqualTo(networkApGroupReq.getVlanPoolId());
                      }));
                })));
      });
    }

    void assertVlanPoolTemplateVenue(String vlanPoolTemplateId, int expectedCount) {
      QueryRequest queryParams = new QueryRequest();
      queryParams.setPage(1);
      queryParams.setPageSize(25);
      assertThatNoException().isThrownBy(() -> {
        VlanPoolVenueDataQueryResponse result = vlanPoolTemplateServiceCtrl.getVlanPoolTemplateVenues(
            vlanPoolTemplateId, queryParams);
        assertEquals(expectedCount, result.getTotalCount());
      });
    }
  }

  private static com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler dummyNetworkVenueScheduler() {
    final var scheduler = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenueScheduler();
    scheduler.setType(SchedulerTypeEnum.CUSTOM);
    scheduler.setMon(randomTimeSlot());
    scheduler.setTue(randomTimeSlot());
    scheduler.setWed(randomTimeSlot());
    scheduler.setThu(randomTimeSlot());
    scheduler.setFri(randomTimeSlot());
    scheduler.setSat(randomTimeSlot());
    scheduler.setSun(randomTimeSlot());
    return scheduler;
  }
}
