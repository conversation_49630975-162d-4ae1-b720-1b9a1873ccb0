package com.ruckus.cloud.wifi.integration.venue;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueSyslog;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueSyslogServerProfile;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Objects;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

;

@Tag("SyslogServerProfileTest")
@WifiIntegrationTest
public class ConsumeVenueSyslogServerProfileSettingRequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired private RevisionService revisionService;

  @Autowired private KafkaTopicProvider kafkaTopicProvider;

  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_SYSLOG_SERVER_PROFILE_SETTINGS)
  class ConsumeUpdateVenueSyslogServerProfileSettingsRequestTest {

    private SyslogServerProfile syslogServerProfile;

    private SyslogServerProfile newBindSyslogServerProfile;

    private VenueSyslogServerProfile venueSyslogServerProfile;

    private Venue venue;

    @BeforeEach
    void givenSyslogServerProfileAndVenueInDb(final Tenant tenant, final Venue venue) {
      this.venue = venue;

      this.syslogServerProfile =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile()
              .generate();
      syslogServerProfile.setTenant(tenant);

      repositoryUtil.createOrUpdate(
          syslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      newBindSyslogServerProfile =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.syslogServerProfile()
              .setName(always("profile-2"))
              .generate();
      newBindSyslogServerProfile.setTenant(tenant);
      repositoryUtil.createOrUpdate(
          newBindSyslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      this.venue.setApPassword("1qaz@WSX");
      this.venue.setCountryCode("US");
      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      venue.setSyslog(venueSyslog);
      venue.setSyslogServerProfile(syslogServerProfile);
      repositoryUtil.createOrUpdate(
          this.venue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venue.getId());
    }

    @Payload
    private VenueSyslogServerProfile getVenueSyslogServerProfile() {
      venueSyslogServerProfile = new VenueSyslogServerProfile();
      venueSyslogServerProfile.setServiceProfileId(newBindSyslogServerProfile.getId());
      venueSyslogServerProfile.setEnabled(true);
      return venueSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueSyslogServerProfile payload) {
      // Then
      validateResult(CfgAction.UPDATE_VENUE_SYSLOG_SERVER_PROFILE_SETTINGS, venue.getId(), payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_TEMPLATE_SYSLOG_SERVER_PROFILE_SETTINGS)
  class ConsumeUpdateVenueTemplateSyslogServerProfileSettingsRequestTest {

    private SyslogServerProfile newBindSyslogServerProfile;

    private VenueSyslogServerProfile venueSyslogServerProfile;

    private Venue venue;

    @BeforeEach
    void givenSyslogServerProfileAndVenueInDb(final Tenant tenant, @Template final Venue venue, @Template final SyslogServerProfile syslogServerProfile) {
      this.venue = venue;

      this.newBindSyslogServerProfile = Generators.syslogServerProfile()
          .setName(always("profile-2"))
          .setIsTemplate(always(true))
          .setTenant(always(tenant))
          .generate();
      repositoryUtil.createOrUpdate(
          this.newBindSyslogServerProfile, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());

      this.venue.setApPassword("1qaz@WSX");
      this.venue.setCountryCode("US");
      VenueSyslog venueSyslog = new VenueSyslog();
      venueSyslog.setEnabled(true);
      venue.setSyslog(venueSyslog);
      venue.setSyslogServerProfile(syslogServerProfile);
      repositoryUtil.createOrUpdate(this.venue, txCtxExtension.getTenantId(), txCtxExtension.newRequestId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueTemplateId", venue.getId());
    }

    @Payload
    private VenueSyslogServerProfile getVenueSyslogServerProfile() {
      venueSyslogServerProfile = new VenueSyslogServerProfile();
      venueSyslogServerProfile.setServiceProfileId(this.newBindSyslogServerProfile.getId());
      venueSyslogServerProfile.setEnabled(true);
      return venueSyslogServerProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueSyslogServerProfile payload) {
      // Then
      validateTemplateResult(CfgAction.UPDATE_VENUE_TEMPLATE_SYSLOG_SERVER_PROFILE_SETTINGS, venue.getId(), payload);
    }
  }

  private void validateTemplateResult(CfgAction apiAction, String venueId, VenueSyslogServerProfile payload) {
    validateRepositoryData(venueId, payload, apiAction);
    validateCmnCfgCollectorMessages(apiAction, venueId, payload);
    validateTemplateActivityMessages(apiAction);
  }

  private void validateResult(
      CfgAction apiAction, String venueId, VenueSyslogServerProfile payload) {
    validateRepositoryData(venueId, payload, apiAction);
    validateDdccmCfgRequestMessages(apiAction, venueId, payload);
    validateCmnCfgCollectorMessages(apiAction, venueId, payload);
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(
      String venueId,
      com.ruckus.cloud.wifi.eda.viewmodel.VenueSyslogServerProfile payload,
      CfgAction apiAction) {
    if (venueId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final Venue venue = repositoryUtil.find(Venue.class, venueId);

    if (payload == null) {
      assertThat(venue).isNull();
      return;
    }

    assertThat(venue)
        .isNotNull()
        .matches(profile -> Objects.equals(profile.getId(), venueId))
        .matches(
            profile ->
                Objects.equals(
                    profile.getSyslogServerProfile().getId(), payload.getServiceProfileId()))
        .matches(profile -> Objects.equals(profile.getSyslog().getEnabled(), payload.getEnabled()));
  }

  private void validateDdccmCfgRequestMessages(
      CfgAction apiAction,
      String venueId,
      com.ruckus.cloud.wifi.eda.viewmodel.VenueSyslogServerProfile payload) {
    if (apiAction == null || venueId == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    SyslogServerProfile syslogServerProfile =
        repositoryUtil.find(SyslogServerProfile.class, payload.getServiceProfileId());

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(ddccmCfgRequestMessage.getPayload())
                    .extracting(WifiConfigRequest::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
                    .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
                    .allMatch(op -> venueId.equals(op.getId()))
                    .allMatch(op -> op.getAction() == Action.MODIFY)
                    .allSatisfy(
                        op ->
                            assertThat(op)
                                .extracting(
                                    com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                                .matches(
                                    commonInfo ->
                                        commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                    .allSatisfy(
                        op -> {
                          assertThat(payload).isNotNull();
                          assertThat(op)
                              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getVenue)
                              .matches(v -> venueId.equals(v.getId()))
                              .matches(
                                  v ->
                                      syslogServerProfile
                                          .getPrimary()
                                          .getServer()
                                          .equals(v.getSyslog().getAddress()));
                        }));
  }

  private void validateCmnCfgCollectorMessages(
      CfgAction apiAction,
      String venueId,
      com.ruckus.cloud.wifi.eda.viewmodel.VenueSyslogServerProfile payload) {
    if (apiAction == null || venueId == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(cmnCfgCollectorMessage.getPayload())
                    .matches(msg -> msg.getTenantId().equals(tenantId))
                    .matches(msg -> msg.getRequestId().equals(requestId))
                    .extracting(ViewmodelCollector::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
                    .filteredOn(
                        op ->
                            EsConstants.Index.SYSLOG_SERVER_PROFILE_INDEX_NAME.equals(
                                op.getIndex()))
                    .filteredOn(op -> payload.getServiceProfileId().equals(op.getId()))
                    .filteredOn(op -> op.getOpType() == OpType.MOD)
                    .hasSize(1)
                    .singleElement()
                    .satisfies(
                        op -> {
                          if (op.getOpType() != OpType.DEL) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(
                                    doc ->
                                        payload
                                            .getServiceProfileId()
                                            .equals(doc.get(EsConstants.Key.ID).getStringValue()))
                                .matches(
                                    doc ->
                                        tenantId.equals(
                                            doc.get(EsConstants.Key.TENANT_ID).getStringValue()));
                          }
                        }));
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
 messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(CfgStatus.ConfigurationStatus.Status.OK))
            .matches(msg -> msg.getStep().equals(getApiFlowName(apiAction)))
            .extracting(CfgStatus.ConfigurationStatus::getEventDate)
            .isNotNull());

    final var activityMessage = messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId);
    assertThat(activityMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityMessage.getPayload())
            .matches(msg -> ActivityConstant.IMPACTED_STEP_ID.equals(msg.getStepId()))
            .matches(msg -> msg.getDeviceIdsCount() == 0)
            .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList)
            .asList()
            .isEmpty());
  }

  private void validateTemplateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
 messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(CfgStatus.ConfigurationStatus.Status.OK))
            .matches(msg -> msg.getStep().equals(getApiFlowName(apiAction)))
            .extracting(CfgStatus.ConfigurationStatus::getEventDate)
            .isNotNull());
  }

  private String getApiFlowName(CfgAction cfgAction) {
    switch (cfgAction) {
      case UPDATE_VENUE_TEMPLATE_SYSLOG_SERVER_PROFILE_SETTINGS:
        return ApiFlowNames.UPDATE_VENUE_TEMPLATE_SYSLOG_SERVER_PROFILE_SETTINGS;
      case UPDATE_VENUE_SYSLOG_SERVER_PROFILE_SETTINGS:
        return ApiFlowNames.UPDATE_VENUE_SYSLOG_SERVER_PROFILE_SETTINGS;
      default:
        return null;
    }
  }
}
