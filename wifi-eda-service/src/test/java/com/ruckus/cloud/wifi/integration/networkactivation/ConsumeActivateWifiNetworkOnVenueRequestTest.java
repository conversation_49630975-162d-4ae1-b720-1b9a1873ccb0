package com.ruckus.cloud.wifi.integration.networkactivation;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullListValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.venueWifiNetwork;
import static com.ruckus.cloud.wifi.integration.WifiCfgChangeAssertions.assertNetworkVenueSoftly;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture.randomOpenNetwork;
import static com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture.randomNetworkVenue;
import static com.ruckus.cloud.wifi.test.fixture.TenantTestFixture.randomTenant;
import static com.ruckus.cloud.wifi.test.fixture.TunnelProfileTestFixture.randomTunnelProfile;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenue;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.RequestCommon;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.AaaType;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmVxLANTunnelProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Eap;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Plmn;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20RoamConsortium;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueWifiNetwork;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.EntityIdGenerationLockService.BatchEntityLockEnum;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.GuestNetwork;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("NetworkActivationTest")
@WifiIntegrationTest
class ConsumeActivateWifiNetworkOnVenueRequestTest {

  @Autowired
  protected MessageCaptors messageCaptors;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private ExtendedMessageUtil messageUtil;

  void validateResult(String tenantId, String requestId, String venueId, String networkId,
      String apiAction,
      List<String> impactList) {
    final var revision = revisionService.changes(requestId, tenantId,
        ConfigRequestHandler.apiActionToFlowName(apiAction));
    final var networkVenueId = revision.getNewEntities().stream()
        .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
        .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
    final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);

    assertThat(networkVenue)
        .isNotNull()
        .satisfies(nv -> assertSoftly(softly -> {
          softly.assertThat(nv.getId()).isEqualTo(networkVenueId);
          softly.assertThat(nv.getNetwork().getId()).isEqualTo(networkId);
          softly.assertThat(nv.getVenue().getId()).isEqualTo(venueId);
          softly.assertThat(nv.getVenue().getName()).isNotBlank();
          softly.assertThat(nv.getVenue().getTenant().getId()).isEqualTo(tenantId);
          softly.assertThat(nv.getAllApGroupsRadioTypes())
              .as("Network venue should have specific AP group radio types")
              .isNotNull()
              .containsAll(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz));
        }));

    if (NetworkTypeEnum.GUEST.equals(networkVenue.getNetwork().getType())) {
      assertThat(networkVenue.getVenuePortal())
          .isNotNull();
    }

    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(wifiCfgChangeMessage).isNotNull()
        .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> assertSoftly(softly -> {
          softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
          softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
          softly.assertThat(msg.getOperationList())
              .isNotEmpty()
              .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
              .filteredOn(op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
              .as("The ADD NetworkVenue operation count should be 1")
              .hasSize(1)
              .singleElement()
              .extracting(com.ruckus.cloud.wifi.proto.Operation::getNetworkVenue)
              .satisfies(assertNetworkVenueSoftly(networkId, venueId));
        }));

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(payload -> assertSoftly(softly -> {
          softly.assertThat(payload.getTenantId()).isEqualTo(tenantId);
          softly.assertThat(payload.getRequestId()).isEqualTo(requestId);
          softly.assertThat(payload.getOperationsList())
              .isNotEmpty()
              .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
              .singleElement()
              .satisfies(op -> assertSoftly(alsoSoftly -> {
                alsoSoftly.assertThat(op.getOpType()).isEqualTo(OpType.ADD);
                alsoSoftly.assertThat(op.getId()).isEqualTo(networkVenueId);
                alsoSoftly.assertThat(op.getDocMap()).isNotEmpty()
                    .containsEntry(Key.ID, ValueUtils.stringValue(networkVenueId))
                    .containsEntry(Key.TYPE, EsConstants.Value.VENUE_NETWORK_MAP)
                    .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId))
                    .containsEntry(Key.NETWORK_ID, ValueUtils.stringValue(networkId))
                    .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(venueId))
                    .containsEntry(Key.VENUE_NAME,
                        ValueUtils.stringValue(networkVenue.getVenue().getName()));
              }));
        }));

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> assertSoftly(softly -> {
          softly.assertThat(msg.getStatus()).isEqualTo(Status.OK);
          softly.assertThat(msg.getStep()).isEqualTo(ApiFlowNames.ACTIVATE_WIFI_NETWORK_ON_VENUE);
          softly.assertThat(msg.getEventDate()).isNotNull();
        }));

    final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(activityImpactedMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .matches(msg -> msg.getStepId().equals(ActivityConstant.IMPACTED_STEP_ID))
        .extracting(ActivityImpactdevice.ImpactDevice::getDeviceIdsList,
            InstanceOfAssertFactories.list(String.class))
        .hasSameElementsAs(impactList);

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .allSatisfy(op -> assertThat(op)
            .extracting(Operation::getCommonInfo)
            .describedAs("The CommonInfo should not be null")
            .isNotNull()
            .satisfies(assertDdccmCommonInfoSoftly(requestId, tenantId)))
        .filteredOn(Operation::hasWlanVenue)
        .describedAs("The count of WlanVenue operations should be 1")
        .hasSize(1)
        .singleElement()
        .satisfies(wlanVenueOp -> assertSoftly(softly -> {
          softly.assertThat(wlanVenueOp.getAction())
              .describedAs("The value of `action` field in WlanVenue operation should be [%s]",
                  Action.ADD)
              .isEqualTo(Action.ADD);
          softly.assertThat(wlanVenueOp.getId())
              .describedAs("The value of `id` field in WlanVenue operation should be [%s]",
                  networkVenueId)
              .isEqualTo(networkVenueId);
          softly.assertThat(wlanVenueOp.getWlanVenue())
              .describedAs("The WlanVenue should not be null")
              .isNotNull()
              .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                alsoSoftly.assertThat(wlanVenue.getWlanId()).isEqualTo(networkId);
                alsoSoftly.assertThat(wlanVenue.getVenueId()).isEqualTo(venueId);
              }));
        }));
  }

  private static Consumer<RequestCommon> assertDdccmCommonInfoSoftly(String requestId,
      String tenantId) {
    return commonInfo -> assertSoftly(softly -> {
      softly.assertThat(commonInfo.getRequestId())
          .describedAs("The value of `requestId` field in CommonInfo should be [%s]",
              requestId)
          .isEqualTo(requestId);
      softly.assertThat(commonInfo.getTenantId())
          .describedAs("The value of `tenantId` field in CommonInfo should be [%s]",
              tenantId)
          .isEqualTo(tenantId);
      softly.assertThat(commonInfo.getSender())
          .describedAs("The value of `sender` field in CommonInfo should be [%s]",
              ServiceType.WIFI_SERVICE)
          .isEqualTo(ServiceType.WIFI_SERVICE);
    });
  }

  @Nested
  class GivenOpenNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String apGroupId;
    private String apId;

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupPersistedInDb(@OpenNetwork Network network,
        Venue venue, @DefaultApGroup ApGroup apGroup, Ap ap) {
      networkId = network.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
      apId = ap.getId();
    }

    @Payload
    private VenueWifiNetwork payload() {
      return venueWifiNetwork().generate();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), venueId, networkId,
          CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE.key(), List.of(apId));
    }

    @Nested
    class WithMacRegistration {

      @BeforeEach
      void givenOpenNetworkWithMacRegistrationPersistedInDb(@OpenNetwork Network network,
          Venue venue,
          @DefaultApGroup ApGroup apGroup) {
        network.getWlan().setMacRegistrationListId(randomId());
        network.getWlan().setMacAddressAuthentication(true);
        repositoryUtil.createOrUpdate(network, network.getTenant().getId(), randomTxId());
      }

      @Payload
      private VenueWifiNetwork payload() {
        return venueWifiNetwork().generate();
      }

      @ApiAction.RequestParams
      private RequestParams requestParams() {
        return new RequestParams().addPathVariable("venueId", venueId)
            .addPathVariable("wifiNetworkId", networkId);
      }

      @Test
      @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE)
      void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
        final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(txCtx);
        assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(Operation.class::cast)
            .filteredOn(Operation::hasWlanVenue).first().matches(operation ->
                operation.getWlanVenue().getAuthenticationServerId()
                    .equals(txCtx.getTenant() + "-Auth-Radius-AAA") && operation.getWlanVenue()
                    .getAuthAaa().getThroughController() && operation.getWlanVenue().getAuthAaa()
                    .getType().equals(AaaType.RADIUS));
      }
    }
  }

  @FeatureFlag(enable = FlagNames.EDGE_DELEGATION_POC_TOGGLE)
  @Nested
  class GivenEdgeDelegationPoCToggleEnabled {

    @Nested
    class GivenVenueTemplateAndOpenNetworkTemplateAndSdLanProfileFromMspPersistedInDb {

      private final String mspTenantId = randomId();
      private final String venueTemplateId = randomId();
      private final String networkTemplateId = randomId();
      private final String tunnelProfileId = randomId();

      @BeforeEach
      void givenVenueTemplateAndOpenNetworkTemplateAndSdLanProfileFromMspPersistedInDb() {
        final Tenant mspTenant = repositoryUtil.createOrUpdate(
            randomTenant(tenant -> {
              tenant.setId(mspTenantId);
              tenant.setName("MSP Tenant");
            }), mspTenantId);

        final var venueTemplate = repositoryUtil.createOrUpdate(
            randomVenue(mspTenant, venue -> {
              venue.setId(venueTemplateId);
              venue.setIsTemplate(true);
              venue.setTenant(mspTenant);
            }), mspTenantId);

        final var networkTemplate = repositoryUtil.createOrUpdate(
            randomOpenNetwork(mspTenant, network -> {
              network.setId(networkTemplateId);
              network.setIsTemplate(true);
              network.setTenant(mspTenant);
            }), mspTenantId);

        repositoryUtil.createOrUpdate(
            randomNetworkVenue(networkTemplate, venueTemplate, networkVenue -> {
              networkVenue.setApWlanId(
                  randomNumber(BatchEntityLockEnum.NETWORK_VENUE_TEMPLATE_AP_WLAN_ID.getMin(),
                      BatchEntityLockEnum.NETWORK_VENUE_TEMPLATE_AP_WLAN_ID.getMax()));
              networkVenue.setTenant(mspTenant);
            }), mspTenantId);

        final var tunnelProfile = repositoryUtil.createOrUpdate(
            randomTunnelProfile(mspTenant, tp -> {
              tp.setId(tunnelProfileId);
              tp.setTenant(mspTenant);
            }), mspTenantId);

        final SdLanProfile sdLanProfile = repositoryUtil.createOrUpdate(
            Generators.sdLanProfile()
                .setSdLanProfileRegularSettings(nullListValue())
                .setTenant(always(mspTenant))
                .generate(), mspTenantId);

        final var sdLanRegularSetting = repositoryUtil.createOrUpdate(
            Generators.sdLanProfileRegularSetting()
                .setSdLanProfile(always(sdLanProfile))
                .setTunnelProfile(always(tunnelProfile))
                .setVenue(always(venueTemplate))
                .setTenant(always(mspTenant))
                .generate(), mspTenantId);

        repositoryUtil.createOrUpdate(Generators.sdLanProfileNetworkMapping()
            .setSdLanProfileRegularSetting(always(sdLanRegularSetting))
            .setNetwork(always(networkTemplate))
            .setTenant(always(mspTenant))
            .generate(), mspTenantId);
      }

      @Nested
      class GivenVenueTemplateInstanceAndNetworkTemplateInstancePersistedInDb {

        private final String venueInstanceId = randomId();
        private final String networkInstanceId = randomId();

        @BeforeEach
        void givenVenueTemplateInstanceAndNetworkTemplateInstancePersistedInDb(
            final Tenant ecTenant) {
          repositoryUtil.createOrUpdate(
              randomVenue(ecTenant, venue -> {
                venue.setId(venueInstanceId);
                venue.setTemplateId(venueTemplateId);
                venue.setTenant(ecTenant);
              }), ecTenant.getId());
          repositoryUtil.createOrUpdate(
              randomOpenNetwork(ecTenant, network -> {
                network.setId(networkInstanceId);
                network.setTemplateId(networkTemplateId);
                network.setTenant(ecTenant);
              }), ecTenant.getId());
        }

        @Payload
        private VenueWifiNetwork payload() {
          return venueWifiNetwork().generate();
        }

        @ApiAction.RequestParams
        private RequestParams requestParams() {
          return new RequestParams().addPathVariable("venueId", venueInstanceId)
              .addPathVariable("wifiNetworkId", networkInstanceId);
        }

        @Test
        @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE)
        void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
          final String tenantId = txCtx.getTenant();
          final String requestId = txCtx.getTxId();
          final String apiAction = CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE.key();

          final var revision = revisionService.changes(requestId, tenantId,
              ConfigRequestHandler.apiActionToFlowName(apiAction));
          final var networkVenueId = revision.getNewEntities().stream()
              .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
              .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId))
              .isNotNull()
              .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
              .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
              .extracting(KafkaProtoMessage::getPayload).isNotNull()
              .extracting(WifiConfigRequest::getOperationsList,
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty().hasSize(2)
              .allSatisfy(op -> assertThat(op)
                  .extracting(Operation::getCommonInfo)
                  .describedAs("The CommonInfo should not be null")
                  .isNotNull()
                  .satisfies(assertDdccmCommonInfoSoftly(requestId, tenantId)))
              .satisfiesExactlyInAnyOrder(
                  op -> assertSoftly(softly -> {
                    softly.assertThat(op.getConfigCase()).isEqualTo(ConfigCase.WLANVENUE);
                    softly.assertThat(op.hasWlanVenue()).isTrue();
                    softly.assertThat(op.getAction()).isEqualTo(Action.ADD);
                    softly.assertThat(op.getId()).isEqualTo(networkVenueId);
                    softly.assertThat(op.getWlanVenue()).isNotNull()
                        .satisfies(wlanVenue -> assertSoftly(alsoSoftly -> {
                          alsoSoftly.assertThat(wlanVenue.getWlanId()).isEqualTo(networkInstanceId);
                          alsoSoftly.assertThat(wlanVenue.getVenueId()).isEqualTo(venueInstanceId);
                        }));
                  }),
                  op -> assertSoftly(softly -> {
                    softly.assertThat(op.getConfigCase())
                        .isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
                    softly.assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
                    softly.assertThat(op.getAction()).isEqualTo(Action.ADD);
                    softly.assertThat(op.getId()).isEqualTo(tunnelProfileId);
                    softly.assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                        .extracting(CcmVxLANTunnelProfile::getId)
                        .isEqualTo(tunnelProfileId);
                  }));
        }
      }
    }
  }

  @Nested
  class GivenOweTransitionNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String apGroupId;
    private String apId;

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupPersistedInDb(Tenant tenant,
        Venue venue, @DefaultApGroup ApGroup apGroup, Ap ap) {
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      Network master = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .generate();
      Network slave = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setIsOweMaster(true);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setOwePairNetworkId(
          slave.getId());
      master = repositoryUtil.createOrUpdate(master, master.getTenant().getId(), randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setOwePairNetworkId(
          master.getId());
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setIsOweMaster(false);
      slave = repositoryUtil.createOrUpdate(slave, slave.getTenant().getId(), randomTxId());

      networkId = master.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
      apId = ap.getId();
    }

    @Payload
    private VenueWifiNetwork payload() {
      return venueWifiNetwork().generate();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE)
    @FeatureFlag(enable = { FlagNames.WIFI_AP_DEFAULT_6G_ENABLEMENT_TOGGLE, FlagNames.WIFI_OWE_TRANSITION_FOR_6G} )
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateOweTransitionResult(txCtx.getTenant(), txCtx.getTxId(),
          CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE.key(),
          List.of(apId), venueId, networkId);
    }

    void validateOweTransitionResult(String tenantId, String requestId, String apiAction,
        List<String> impactList, String venueId, String networkId) {
      final var revision = revisionService.changes(requestId, tenantId,
          ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueId = revision.getNewEntities().stream()
          .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
          .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
      assertThat(networkVenue.getOweTransWlanId()).isNotNull();
      assertThat(networkVenue.getAllApGroupsRadioTypes())
          .as("Network venue should have specific AP group radio types")
          .isNotNull()
          .containsAll(List.of(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz, RadioTypeEnum._6_GHz));

      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> assertSoftly(softly -> {
            softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
            softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
            softly.assertThat(msg.getOperationList())
                .isNotEmpty()
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .filteredOn(op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                .as("The ADD NetworkVenue operation count should be 1")
                .hasSize(2);
          }));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(payload -> assertSoftly(softly -> {
            softly.assertThat(payload.getTenantId()).isEqualTo(tenantId);
            softly.assertThat(payload.getRequestId()).isEqualTo(requestId);
            softly.assertThat(payload.getOperationsList())
                .isNotEmpty()
                .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                .hasSize(2);
          }));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> assertSoftly(softly -> {
            softly.assertThat(msg.getStatus()).isEqualTo(Status.OK);
            softly.assertThat(msg.getStep()).isEqualTo(ApiFlowNames.ACTIVATE_WIFI_NETWORK_ON_VENUE);
            softly.assertThat(msg.getEventDate()).isNotNull();
          }));

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .allSatisfy(op -> assertThat(op)
              .extracting(Operation::getCommonInfo)
              .describedAs("The CommonInfo should not be null")
              .isNotNull()
              .satisfies(assertDdccmCommonInfoSoftly(requestId, tenantId)))
          .satisfies(op -> assertThat(op)
                .filteredOn(Operation::hasWlanVenue)
                .describedAs("The count of WlanVenue operations should be 2")
                .hasSize(2));
    }

  }

  @Nested
  class GivenOweTransitionGuestNetworkPersistedInDb {

    private String networkId;
    private String venueId;

    @BeforeEach
    void givenGuestNetworkAndDefaultApGroupPersistedInDb(Tenant tenant,
        Venue venue, @DefaultApGroup ApGroup apGroup, Ap ap) {
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      var master = (com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork) com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class)
          .generate();
      var slave = (com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork) com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class)
          .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      master.setIsOweMaster(true);
      master.setOwePairNetworkId(slave.getId());
      master.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.WISPr)
              .setWisprPage(com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                  .wisprPage().setCustomExternalProvider(always(false)))
              .generate());
      master.getGuestPortal().setNetwork(master);
      master = repositoryUtil.createOrUpdate(master, master.getTenant().getId(), randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      slave.setOwePairNetworkId(master.getId());
      slave.setIsOweMaster(false);
      slave.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.WISPr)
              .setWisprPage(com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
                  .wisprPage().setCustomExternalProvider(always(false)))
              .generate());
      slave.getGuestPortal().setNetwork(slave);
      repositoryUtil.createOrUpdate(slave, slave.getTenant().getId(), randomTxId());

      networkId = master.getId();
      venueId = venue.getId();
    }

    @Payload
    private VenueWifiNetwork payload() {
      return venueWifiNetwork().generate();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE)
    @FeatureFlag(enable = FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateOweTransitionResult(txCtx.getTenant(), txCtx.getTxId(),
          CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE.key());
    }

    void validateOweTransitionResult(String tenantId, String requestId, String apiAction) {
      final var revision = revisionService.changes(requestId, tenantId,
          ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueId = revision.getNewEntities().stream()
          .filter(txEntity -> txEntity.getEntity() instanceof NetworkVenue)
          .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
      assertNotNull(networkVenue);
      assertThat(networkVenue.getOweTransWlanId()).isNotNull();
      final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(wifiCfgChangeMessage).isNotNull()
          .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.API_ACTION, apiAction))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> assertSoftly(softly -> {
            softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
            softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
            softly.assertThat(msg.getOperationList())
                .isNotEmpty()
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .filteredOn(op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.ADD)
                .as("The ADD NetworkVenue operation count should be 2")
                .hasSize(2);
          }));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(payload -> assertSoftly(softly -> {
            softly.assertThat(payload.getTenantId()).isEqualTo(tenantId);
            softly.assertThat(payload.getRequestId()).isEqualTo(requestId);
            softly.assertThat(payload.getOperationsList())
                .isNotEmpty()
                .filteredOn(op -> Index.NETWORK_VENUE_MAPPING.equals(op.getIndex()))
                .hasSize(2);
          }));

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityCfgChangeRespMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> assertSoftly(softly -> {
            softly.assertThat(msg.getStatus()).isEqualTo(Status.OK);
            softly.assertThat(msg.getStep()).isEqualTo(ApiFlowNames.ACTIVATE_WIFI_NETWORK_ON_VENUE);
            softly.assertThat(msg.getEventDate()).isNotNull();
          }));

      final var activityImpactedMessage = messageCaptors.getActivityImpactDeviceMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(activityImpactedMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenantId, requestId);
      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty()
          .allSatisfy(op -> assertThat(op)
              .extracting(Operation::getCommonInfo)
              .describedAs("The CommonInfo should not be null")
              .isNotNull()
              .satisfies(assertDdccmCommonInfoSoftly(requestId, tenantId)))
          .satisfies(op -> assertThat(op)
                .filteredOn(Operation::hasWlanVenue)
                .describedAs("The count of WlanVenue operations should be 2")
                .hasSize(2));
    }
  }

  @Nested
  class GivenGuestNetworkPersistedInDb {

    private String networkId;
    private String venueId;

    @BeforeEach
    void givenGuestNetworkAndDefaultApGroupPersistedInDb(
        @GuestNetwork com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork network, Venue venue,
        @DefaultApGroup ApGroup apGroup) {
      networkId = network.getId();
      venueId = venue.getId();
      sendActivatePortalServiceProfileOnNetwork(network.getTenant().getId(), networkId);
    }

    @Payload
    private VenueWifiNetwork payload() {
      return venueWifiNetwork().generate();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Test
    @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), venueId, networkId,
          CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE.key(), Collections.emptyList());
    }

    private void sendActivatePortalServiceProfileOnNetwork(String tenantId, String networkId) {
      WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .apiAction(CfgAction.ACTIVATE_PORTAL_SERVICE_PROFILE_ON_WIFI_NETWORK)
          .requestParams(new RequestParams().addPathVariable("wifiNetworkId", networkId)
              .addPathVariable("portalServiceProfileId", "portalServiceProfileId")).build();
      messageUtil.sendWifiCfgRequest(wifiCfgRequest);
    }
  }

  @Nested
  class GivenHotspot20NetworkPersistedInDb {

    private String wifiNetworkId;
    private String venueId;

    @BeforeEach
    void givenHotspot20NetworkAndDefaultApGroupPersistedInDb(
        Tenant tenant, Venue venue, @DefaultApGroup ApGroup apGroup) {
      final var network = network(Hotspot20Network.class).generate();

      final var hotspot20Operator = Generators.hotspot20Operator().generate();
      final var hotspot20FriendlyName = Generators.hotspot20FriendlyName().generate();
      hotspot20Operator.setFriendlyNames(List.of(hotspot20FriendlyName));
      hotspot20FriendlyName.setOperator(hotspot20Operator);

      final var hotspot20IdentityProvider = Generators.hotspot20IdentityProvider()
          .setRoamConsortiumOis(nullValue(Hotspot20RoamConsortium.class).toListGenerator(0))
          .setPlmns(nullValue(Hotspot20Plmn.class).toListGenerator(0)).generate();
      final var hotspot20NaiRealm = Generators.hotspot20NaiRealm()
          .setEaps(nullValue(Hotspot20Eap.class).toListGenerator(0)).generate();
      hotspot20IdentityProvider.setNaiRealms(List.of(hotspot20NaiRealm));
      hotspot20NaiRealm.setIdentityProvider(hotspot20IdentityProvider);

      final var connectionCapability = Generators.hotspot20ConnectionCapability().generate();
      final var networkHotspot20Settings = Generators.networkHotspot20Settings().generate();
      connectionCapability.setNetworkHotspot20Settings(networkHotspot20Settings);
      networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability));
      networkHotspot20Settings.setOperator(hotspot20Operator);

      final var hotspot20Network = (Hotspot20Network) network;
      hotspot20Network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA2Enterprise);
      hotspot20Network.getWlan().setNetwork(hotspot20Network);
      hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

      hotspot20IdentityProvider.setNetworkHotspot20SettingsList(List.of(networkHotspot20Settings));

      repositoryUtil.createOrUpdate(hotspot20Operator, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20FriendlyName, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20IdentityProvider, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20NaiRealm, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(connectionCapability, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(hotspot20Network, tenant.getId(), randomTxId());

      wifiNetworkId = hotspot20Network.getId();
      venueId = venue.getId();
    }

    @Payload
    private VenueWifiNetwork payload() {
      return venueWifiNetwork().generate();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("wifiNetworkId", wifiNetworkId);
    }

    @Test
    @ApiAction(CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE)
    @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
    void thenShouldHandleTheRequestSuccessfully(TxCtx txCtx) {
      validateResult(txCtx.getTenant(), txCtx.getTxId(), venueId, wifiNetworkId,
          CfgAction.ACTIVATE_WIFI_NETWORK_ON_VENUE.key(), Collections.emptyList());
    }
  }
}
