package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApVersionLabelEnum;
import com.ruckus.cloud.wifi.servicemodel.enums.ApVersionCategory;
import com.ruckus.cloud.wifi.servicemodel.projection.IdAndStringAggregationProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.param.SkipSave;
import java.util.Arrays;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import org.springframework.test.context.jdbc.Sql;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;
import static org.junit.jupiter.api.Assertions.assertTrue;

@WifiJpaDataTest
class ApVersionRepositoryTest {

  @Autowired
  private ApVersionRepository apVersionRepository;

  @Autowired
  private TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository;

  @Test
  void testFindLatestReleaseVersionByBranch(
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.511")
          ApVersion ver_4_9_1_103_511,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.513")
          ApVersion ver_4_9_1_103_513,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********3.888")
          ApVersion ver_4_9_1_1033_888,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "********.888")
          ApVersion ver_4_9_1_10_888,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.513")
          ApVersion ver_4_9_2_103_513) {

    apVersionRepository.saveAll(
        List.of(ver_4_9_1_103_511, ver_4_9_1_103_513, ver_4_9_1_1033_888, ver_4_9_1_10_888, ver_4_9_2_103_513));

    ApVersion res = apVersionRepository.findLatestReleaseVersionByBranch("*********").get();
    Assertions.assertEquals(ver_4_9_1_103_513.getId(), res.getId());

    res = apVersionRepository.findLatestReleaseVersionByBranch("*********").get();
    Assertions.assertEquals(ver_4_9_2_103_513.getId(), res.getId());

    assertTrue(apVersionRepository.findLatestReleaseVersionByBranch("*******").isEmpty());
  }

  @Test
  void testFindByLabelIn(
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "********.100") ApVersion apVersion1,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "********.200") ApVersion apVersion2,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "********.300") ApVersion apVersion3,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "********.400") ApVersion apVersion4,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "********.500") ApVersion apVersion5) {
    apVersion1.setLabels(List.of(ApVersionLabelEnum.ALPHA));
    apVersion2.setLabels(List.of(ApVersionLabelEnum.BETA));
    apVersion3.setLabels(List.of(ApVersionLabelEnum.ALPHA, ApVersionLabelEnum.BETA));
    apVersion4.setLabels(List.of(ApVersionLabelEnum.GA));

    apVersionRepository.saveAll(List.of(apVersion1, apVersion2, apVersion3, apVersion4, apVersion5));
    List<ApVersion> anyAlphaBeta = apVersionRepository.findByLabelIn(List.of(ApVersionLabelEnum.ALPHA.toString(), ApVersionLabelEnum.BETA.toString()));
    assertThat(anyAlphaBeta).hasSize(3);

    List<ApVersion> anyAlpha = apVersionRepository.findByLabelIn(List.of(ApVersionLabelEnum.ALPHA.toString()));
    assertThat(anyAlpha).hasSize(2);

    List<ApVersion> anyBeta = apVersionRepository.findByLabelIn(List.of(ApVersionLabelEnum.BETA.toString()));
    assertThat(anyBeta).hasSize(2);
  }


  @Test
  void testFindByIsGreenfieldTrue(
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.199") ApVersion apVersion1,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.299") ApVersion apVersion2) {
    apVersion1.setIsGreenfield(true);
    apVersion2.setIsGreenfield(false);
    apVersionRepository.saveAll(List.of(apVersion1, apVersion2));
    List<ApVersion> result = apVersionRepository.findByIsGreenfieldTrue();
    assertThat(result)
        .hasSize(1)
        .first()
        .extracting(ApVersion::getId)
        .isEqualTo(apVersion1.getId());
  }

  @Test
  void testFindFirstGreenfieldFirmwareVersionOrderByIdDesc(
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.399") ApVersion apVersion1,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.499") ApVersion apVersion2) {
    apVersion1.setIsGreenfield(true);
    apVersion2.setIsGreenfield(true);
    apVersionRepository.saveAll(List.of(apVersion1, apVersion2));
    Optional<ApVersion> result = apVersionRepository.findFirstGreenfieldFirmwareVersionOrderByIdDesc();
    assertThat(result.get().getId())
        .isEqualTo(apVersion2.getId());
  }

  @Test
  void testFindGreenfieldFirmwareVersionsOrderByIdDescOffset(
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.599") ApVersion apVersion1,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.699") ApVersion apVersion2,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.799") ApVersion apVersion3) {
    apVersion1.setIsGreenfield(true);
    apVersion2.setIsGreenfield(true);
    apVersion3.setIsGreenfield(true);
    apVersionRepository.saveAll(List.of(apVersion1, apVersion2, apVersion3));
    List<ApVersion> result = apVersionRepository.findGreenfieldFirmwareVersionsOrderByIdDescOffset(1);
    assertThat(result)
        .hasSize(2)
        .first()
        .extracting(ApVersion::getId)
        .isEqualTo(apVersion2.getId());
  }

  @Test
  void testFindGreenfieldFirmwareSupportedApModelsByVersionIn(
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.200") ApVersion apVersion1,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.300") ApVersion apVersion2,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.400") ApVersion apVersion3) {
    apVersion1.setSupportedApModels(List.of("R550", "R600", "R500"));
    apVersion1.setIsGreenfield(true);
    apVersion2.setSupportedApModels(List.of("R550", "R720", "R710", "R520"));
    apVersion2.setIsGreenfield(true);
    apVersion3.setSupportedApModels(List.of("R550", "R770"));
    apVersion3.setIsGreenfield(true);
    apVersionRepository.saveAll(List.of(apVersion1, apVersion2, apVersion3));
    List<IdAndStringAggregationProjection> result = apVersionRepository.findGreenfieldFirmwareSupportedApModelsByVersionIn(
        List.of(apVersion1.getId(), apVersion2.getId(), apVersion3.getId()));
    assertThat(Arrays.asList(result.get(0).getAggregationValue().split(",")))
        .hasSize(2)
        .containsExactlyInAnyOrder("R550", "R770");
    assertThat(Arrays.asList(result.get(1).getAggregationValue().split(",")))
        .hasSize(3)
        .containsExactlyInAnyOrder("R720", "R710", "R520");
    assertThat(Arrays.asList(result.get(2).getAggregationValue().split(",")))
        .hasSize(2)
        .containsExactlyInAnyOrder("R600", "R500");
  }

  @Test
  void testFindTenantLatestAvailableReleaseVersion(
      Tenant tenant,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.10")
          ApVersion ver_4_9_1_103_10,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.50")
          ApVersion ver_4_9_1_103_50,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.122")
          ApVersion ver_4_9_1_103_122,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.200")
          ApVersion ver_4_9_2_103_200,
      @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.300")
          ApVersion ver_4_9_3_103_300) {
    apVersionRepository.saveAll(
        List.of(ver_4_9_1_103_10, ver_4_9_1_103_50, ver_4_9_1_103_122, ver_4_9_2_103_200, ver_4_9_3_103_300));

    tenantAvailableApFirmwareRepository.saveAll(
        List.of(
            newTenantAvailableApFirmware(tenant, ver_4_9_1_103_10),
            newTenantAvailableApFirmware(tenant, ver_4_9_1_103_50),
            newTenantAvailableApFirmware(tenant, ver_4_9_2_103_200)
        ));

    assertThat(apVersionRepository.findTenantLatestAvailableReleaseVersion(tenant.getId(), "*********"))
        .isPresent()
        .get()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(ver_4_9_1_103_50.getId());

    assertThat(apVersionRepository.findTenantLatestAvailableReleaseVersion(tenant.getId(), "*********"))
        .isPresent()
        .get()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo(ver_4_9_2_103_200.getId());

    assertThat(apVersionRepository.findTenantLatestAvailableReleaseVersion(tenant.getId(), "*********"))
        .isEmpty();
    assertThat(apVersionRepository.findTenantLatestAvailableReleaseVersion("w23k5jkfdme3ikfi4iofkefk2dfekl", "*********"))
        .isEmpty();
  }

  @Test
  void testFindByCategoryVersionFilter(
    @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.500")
    ApVersion apVersion1,
    @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.600")
    ApVersion apVersion2,
    @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.200")
    ApVersion apVersion3,
    @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.300")
    ApVersion apVersion4,
    @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.1000")
    ApVersion apVersion5,
    @SkipSave @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.1700")
    ApVersion apVersion6) {
    apVersionRepository.saveAll(List.of(apVersion1, apVersion2, apVersion3, apVersion4, apVersion5, apVersion6));
    List<ApVersion> result = apVersionRepository.findByCategoryVersionFilter(
      ApVersionCategory.RECOMMENDED.toString(),
      "*********.552",
      "6.2.0.999999.0.0",
      "*********.244",
      "6.2.999999.0.0",
      "*********.1404"
    );
    assertThat(result)
        .hasSizeGreaterThanOrEqualTo(3)
        .extracting(AbstractBaseEntity::getId)
        .contains(apVersion2.getId(), apVersion4.getId(), apVersion6.getId());
  }

  @Test
  @Sql(statements = """
            INSERT INTO tenant (id) VALUES ('tenantId1');
      INSERT INTO ap_version (id) VALUES ('*********.124'), ('*********.154'), ('*********.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time)
        VALUES ('timeSlotId', 'WAITING_REMINDER', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version)
        VALUES ('tenantId1', 'venueId1', '*********.124'),
               ('tenantId1', 'venueId2', '*********.124');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue)
        VALUES ('tenantId1', 'scheduleId1', 'RUNNING', 'timeSlotId', '*********.124', 'venueId1'),
               ('tenantId1', 'scheduleId2', 'PENDING', 'timeSlotId', '*********.154', 'venueId1'),
               ('tenantId1', 'scheduleId3', 'PENDING', 'timeSlotId', '*********.154', 'venueId2');
      INSERT INTO upgrade_schedule_firmware_version(id, upgrade_schedule, ap_firmware_version, tenant, created_date, updated_date)
        VALUES ('usfvId1', 'scheduleId1', '*********.124', 'tenantId1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
               ('usfvId2', 'scheduleId1', '*********.124', 'tenantId1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
               ('usfvId3', 'scheduleId2', '*********.154', 'tenantId1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
               ('usfvId4', 'scheduleId3', '*********.124', 'tenantId1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
               ('usfvId5', 'scheduleId3', '*********.154', 'tenantId1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
      """)
  void testFindByUpgradeVenueIds() {
    List<ApVersion> upgradeApVersions = apVersionRepository.findByUpgradeVenueIds(
        List.of("venueId1", "venueId2"));
    assertThat(upgradeApVersions).hasSize(2).noneMatch(v -> v.getId().equals("*********.124"))
        .anyMatch(v -> v.getId().equals("*********.154"))
        .anyMatch(v -> v.getId().equals("*********.124"));
  }

  @Test
  @Sql(statements = """
      INSERT INTO ap_version (id, supported_ap_models) VALUES ('*********.124', '["R500", "R510", "R610"]'), 
        ('*********.125', null),
        ('*********.154', '["R550", "R560", "R650"]'), 
        ('*********.204', '["R550", "R650"]'),
        ('*********.154', null),
        ('7.0.0.105.154', '["R770"]');
      """)
  void testFindBySupportedApModelsIsNull() {
    assertThat(apVersionRepository.findBySupportedApModelsIsNull())
        .hasSize(2)
        .extracting(AbstractBaseEntity::getId)
        .containsExactlyInAnyOrder("*********.125", "*********.154");
  }

  @Test
  @Sql(statements ="""
      INSERT INTO ap_version (id, supported_ap_models) VALUES ('*********.124', '["R500", "R510", "R610"]'),
        ('*********.125', null),
        ('*********.154', '["R550", "R560", "R650"]'),
        ('*********.204', '["R550", "R650"]'),
        ('*********.154', null),
        ('7.0.0.105.154', '["R770"]');
        INSERT INTO ap_model_greenfield_firmware(id, firmware) VALUES
        ('R550', '*********.204'),
        ('R650', '*********.154'),
        ('R770', '7.0.0.105.154');
      """)
  void findApModelGreenfieldFirmwareVersions() {
    assertThat(apVersionRepository.findApModelGreenfieldFirmwareVersions())
        .hasSize(3)
        .extracting(ApVersion::getId, ApVersion::getSupportedApModels)
        .containsExactlyInAnyOrder(
            tuple("*********.154", List.of("R550", "R560", "R650")),
            tuple("*********.204", List.of("R550", "R650")),
            tuple("7.0.0.105.154", List.of("R770")));
  }

  private TenantAvailableApFirmware newTenantAvailableApFirmware(Tenant tenant, ApVersion apVersion) {
    TenantAvailableApFirmware taaf = new TenantAvailableApFirmware();
    taaf.setApVersion(apVersion);
    taaf.setTenant(tenant);
    return taaf;
  }
}
