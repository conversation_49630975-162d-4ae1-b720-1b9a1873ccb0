package com.ruckus.cloud.wifi.entitylistener;

import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApOperationalData;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApSubStateEnum;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.impl.FactoryResetServiceImpl;
import com.ruckus.cloud.wifi.service.impl.TroubleshootingServiceImpl;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.verification.VerificationMode;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class ApFactoryResetPublisherTest {

  private static final String AP_ID = CommonTestFixture.randomSerialNumber();
  private static final String TENANT_ID = CommonTestFixture.randomId();
  private static final String VENUE_ID = CommonTestFixture.randomId();
  private static final String TX_ID = CommonTestFixture.randomTxId();
  private static final String USER_NAME = CommonTestFixture.randomName();

  @MockBean
  private FactoryResetServiceImpl factoryResetService;

  @SpyBean
  private ApFactoryResetPublisher apFactoryResetPublisher;

  @SpyBean
  private TroubleshootingServiceImpl troubleshootingService;

  @BeforeEach
  public void setup() {
    TxCtxHolder.set(new TxCtxHolder.TxCtx(TENANT_ID, TX_ID, USER_NAME, "Delete AP"));
  }

  @Test
  public void testNotSendFactoryResetEvent_WhenApNotFound() throws Exception {
    verifySendFactoryResetEvent(mockAp(null), never());
  }

  @Test
  public void testNotSendFactoryResetEvent_WhenApNeverContactedCloud() throws Exception {
    verifySendFactoryResetEvent(mockAp(ApSubStateEnum.NeverContactedCloud), never());
  }

  @Test
  public void testSendFactoryResetEvent_successfully() throws Exception {
    verifySendFactoryResetEvent(mockAp(ApSubStateEnum.ApplyingConfiguration), times(1));
  }

  private void verifySendFactoryResetEvent(Ap ap, VerificationMode mode) throws Exception {
    apFactoryResetPublisher.flush(apFactoryResetPublisher
        .build(new TxEntity(ap, EntityAction.DELETE), emptyTxChanges()));

    verify(troubleshootingService, mode)
        .sendFactoryResetRequest(eq(TX_ID), eq(TENANT_ID), eq(VENUE_ID), eq(AP_ID));

    verify(factoryResetService, mode)
        .request(eq(TX_ID), eq(TENANT_ID), eq(VENUE_ID), eq(AP_ID));
  }

  private Ap mockAp(ApSubStateEnum state) {
    Ap ap = new Ap(AP_ID);
    ap.setTenant(new Tenant(TENANT_ID));
    Venue venue = new Venue(VENUE_ID);
    ApGroup apGroup = new ApGroup();
    apGroup.setVenue(venue);
    ap.setApGroup(apGroup);
    ap.setOperationalData(mockApOperationData(state));
    return ap;
  }

  private ApOperationalData mockApOperationData(ApSubStateEnum state) {
    if (state == null) {
      return null;
    }
    ApOperationalData data = new ApOperationalData();
    data.setSubState(state);
    return data;
  }
}
