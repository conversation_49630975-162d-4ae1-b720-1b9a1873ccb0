package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DeviceTypeEnum.Gaming;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.PlayStation;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.PlayStation2;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.PlayStation3;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.Xbox;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.OsVendorEnum.Xbox360;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.devicePolicy;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.devicePolicyRule;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.DevicePolicyRule;
import com.ruckus.acx.ddccm.protobuf.wifi.DeviceTypeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.OSAccessEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.OsVendorEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AccessEnum;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.proto.DevicePolicyRuleMigrationJob;
import com.ruckus.cloud.wifi.proto.DevicePolicyRuleMigrationJob.JobType;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("DevicePolicyTest")
@WifiIntegrationTest
@FeatureRole("AP-70")
@FeatureFlag(enable = FlagNames.ACX_UI_NEW_OS_VENDOR_IN_DEVICE_POLICY_TOGGLE)
class ConsumeDevicePolicyRuleMigrationJobTest {

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class GivenDevicePolicyWithSingleXboxRulePersistedInDb {

    private DevicePolicy devicePolicyWithSingleXboxRule;

    @BeforeEach
    void givenDevicePolicyWithSingleXboxRulePersistedInDb(final Tenant tenant) {
      devicePolicyWithSingleXboxRule = repositoryUtil.createOrUpdate(devicePolicy()
          .setTenant(always(tenant))
          .setDefaultAccess(always(AccessEnum.ALLOW))
          .generate(devicePolicy ->
              devicePolicy.setRules(devicePolicyRule()
                  .setAction(always(AccessEnum.BLOCK))
                  .setDeviceType(always(Gaming))
                  .setOsVendor(always(Xbox))
                  .setDevicePolicy(always(devicePolicy))
                  .generate(1))), tenant.getId(), randomTxId());
    }

    @Test
    void testConsumeDevicePolicyRuleMigrationJobWithRoutineJobType(final Tenant tenant) {

      final var routineJob = WifiAsyncJob.newBuilder()
          .setDevicePolicyRuleMigrationJob(
              DevicePolicyRuleMigrationJob.newBuilder().setJobType(JobType.ROUTINE)).build();
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, routineJob);

      final var wifiAsyncJobMessage = messageCaptors.getWifiAsyncJobMessageCaptor()
          .getValue(tenant.getId(), 2);

      assertThat(wifiAsyncJobMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(wifiAsyncJob -> {
            assertThat(wifiAsyncJob.hasDevicePolicyRuleMigrationJob()).isTrue();
            assertThat(wifiAsyncJob.getDevicePolicyRuleMigrationJob()).isNotNull()
                .satisfies(job -> {
                  assertThat(job.getJobType())
                      .isEqualTo(DevicePolicyRuleMigrationJob.JobType.MIGRATION);
                  assertThat(job.getDevicePolicyId())
                      .isEqualTo(devicePolicyWithSingleXboxRule.getId());
                });
          });

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId(), requestId)).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList).asList()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
          .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasDevicePolicy)
          .hasSize(1).singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(Action.MODIFY);
            assertThat(op.getId()).isEqualTo(devicePolicyWithSingleXboxRule.getId());
            assertThat(op.getCommonInfo())
                .satisfies(commonInfo -> {
                  assertThat(commonInfo.getTenantId()).isEqualTo(tenant.getId());
                  assertThat(commonInfo.getRequestId()).startsWith(requestId);
                  assertThat(commonInfo.getSender()).isEqualTo(ServiceType.WIFI_SERVICE);
                });
          })
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDevicePolicy)
          .satisfies(devicePolicy -> {
            assertThat(devicePolicy.getDefaultAccess()).isEqualTo(OSAccessEnum.OS_ALLOW);
            assertThat(devicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
            assertThat(devicePolicy.getDevicePolicyRulesList())
                .hasSize(2)
                .allSatisfy(devicePolicyRule -> {
                  assertThat(devicePolicyRule.getAction()).isEqualTo(OSAccessEnum.OS_BLOCK);
                  assertThat(devicePolicyRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                })
                .extracting(DevicePolicyRule::getOsVendor)
                .containsExactlyInAnyOrder(OsVendorEnum.XBOX, OsVendorEnum.XBOX_360);
          });
    }
  }

  @Nested
  class GivenDevicePolicyWithXboxAndXbox360RulesPersistedInDb {

    private DevicePolicy devicePolicyWithXboxAndXbox360Rules;

    @BeforeEach
    void givenDevicePolicyWithXboxAndXbox360RulesPersistedInDb(final Tenant tenant) {
      devicePolicyWithXboxAndXbox360Rules = repositoryUtil.createOrUpdate(devicePolicy()
          .setTenant(always(tenant))
          .setDefaultAccess(always(AccessEnum.BLOCK))
          .generate(devicePolicy ->
              devicePolicy.setRules(devicePolicyRule()
                  .setAction(always(AccessEnum.ALLOW))
                  .setDeviceType(always(Gaming))
                  .setVlan(options(40, 50))
                  .setOsVendor(options(Xbox360, Xbox))
                  .setDevicePolicy(always(devicePolicy))
                  .generate(2))), tenant.getId(), randomTxId());
    }

    @Test
    void testConsumeDevicePolicyRuleMigrationJobWithRoutineJobType(final Tenant tenant) {

      final var routineJob = WifiAsyncJob.newBuilder()
          .setDevicePolicyRuleMigrationJob(
              DevicePolicyRuleMigrationJob.newBuilder().setJobType(JobType.ROUTINE)).build();
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, routineJob);

      final var wifiAsyncJobMessage = messageCaptors.getWifiAsyncJobMessageCaptor()
          .getValue(tenant.getId(), 2);

      assertThat(wifiAsyncJobMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(wifiAsyncJob -> {
            assertThat(wifiAsyncJob.hasDevicePolicyRuleMigrationJob()).isTrue();
            assertThat(wifiAsyncJob.getDevicePolicyRuleMigrationJob()).isNotNull()
                .satisfies(job -> {
                  assertThat(job.getJobType())
                      .isEqualTo(DevicePolicyRuleMigrationJob.JobType.MIGRATION);
                  assertThat(job.getDevicePolicyId())
                      .isEqualTo(devicePolicyWithXboxAndXbox360Rules.getId());
                });
          });

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId(), requestId)).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList).asList()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
          .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasDevicePolicy)
          .hasSize(1).singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(Action.MODIFY);
            assertThat(op.getId()).isEqualTo(devicePolicyWithXboxAndXbox360Rules.getId());
            assertThat(op.getCommonInfo())
                .satisfies(commonInfo -> {
                  assertThat(commonInfo.getTenantId()).isEqualTo(tenant.getId());
                  assertThat(commonInfo.getRequestId()).startsWith(requestId);
                  assertThat(commonInfo.getSender()).isEqualTo(ServiceType.WIFI_SERVICE);
                });
          })
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDevicePolicy)
          .satisfies(devicePolicy -> {
            assertThat(devicePolicy.getDefaultAccess()).isEqualTo(OSAccessEnum.OS_BLOCK);
            assertThat(devicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
            assertThat(devicePolicy.getDevicePolicyRulesList())
                .hasSize(2)
                .allSatisfy(devicePolicyRule -> {
                  assertThat(devicePolicyRule.getAction()).isEqualTo(OSAccessEnum.OS_ALLOW);
                  assertThat(devicePolicyRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                  assertThat(devicePolicyRule.getVlan().getValue()).isEqualTo(50);
                })
                .extracting(DevicePolicyRule::getOsVendor)
                .containsExactlyInAnyOrder(OsVendorEnum.XBOX, OsVendorEnum.XBOX_360);
          });
    }
  }

  @Nested
  class GivenDevicePolicyWithSinglePlayStationRulePersistedInDb {

    private DevicePolicy devicePolicyWithSinglePlayStationRule;

    @BeforeEach
    void givenDevicePolicyWithSinglePlayStationRulePersistedInDb(final Tenant tenant) {
      devicePolicyWithSinglePlayStationRule = repositoryUtil.createOrUpdate(devicePolicy()
          .setTenant(always(tenant))
          .setDefaultAccess(always(AccessEnum.ALLOW))
          .generate(devicePolicy ->
              devicePolicy.setRules(devicePolicyRule()
                  .setAction(always(AccessEnum.BLOCK))
                  .setDeviceType(always(Gaming))
                  .setOsVendor(always(PlayStation))
                  .setDevicePolicy(always(devicePolicy))
                  .generate(1))), tenant.getId(), randomTxId());
    }

    @Test
    void testConsumeDevicePolicyRuleMigrationJobWithRoutineJobType(final Tenant tenant) {

      final var routineJob = WifiAsyncJob.newBuilder()
          .setDevicePolicyRuleMigrationJob(
              DevicePolicyRuleMigrationJob.newBuilder().setJobType(JobType.ROUTINE)).build();
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, routineJob);

      final var wifiAsyncJobMessage = messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenant.getId());

      assertThat(wifiAsyncJobMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(wifiAsyncJob -> {
            assertThat(wifiAsyncJob.hasDevicePolicyRuleMigrationJob()).isTrue();
            assertThat(wifiAsyncJob.getDevicePolicyRuleMigrationJob()).isNotNull()
                .extracting(DevicePolicyRuleMigrationJob::getJobType)
                .isEqualTo(DevicePolicyRuleMigrationJob.JobType.ROUTINE);
          });

      messageCaptors.assertThat(
          messageCaptors.getWifiAsyncJobMessageCaptor(),
          messageCaptors.getDdccmMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());
    }
  }

  @Nested
  class GivenDevicePolicyWithSinglePlayStation2RulePersistedInDb {

    private DevicePolicy devicePolicyWithSinglePlayStation2Rule;

    @BeforeEach
    void givenDevicePolicyWithSinglePlayStation2RulePersistedInDb(final Tenant tenant) {
      devicePolicyWithSinglePlayStation2Rule = repositoryUtil.createOrUpdate(devicePolicy()
          .setTenant(always(tenant))
          .setDefaultAccess(always(AccessEnum.ALLOW))
          .generate(devicePolicy ->
              devicePolicy.setRules(devicePolicyRule()
                  .setAction(always(AccessEnum.BLOCK))
                  .setDeviceType(always(Gaming))
                  .setOsVendor(always(PlayStation2))
                  .setDevicePolicy(always(devicePolicy))
                  .generate(1))), tenant.getId(), randomTxId());
    }

    @Test
    void testConsumeDevicePolicyRuleMigrationJobWithRoutineJobType(final Tenant tenant) {

      final var routineJob = WifiAsyncJob.newBuilder()
          .setDevicePolicyRuleMigrationJob(
              DevicePolicyRuleMigrationJob.newBuilder().setJobType(JobType.ROUTINE)).build();
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, routineJob);

      final var wifiAsyncJobMessage = messageCaptors.getWifiAsyncJobMessageCaptor()
          .getValue(tenant.getId(), 2);

      assertThat(wifiAsyncJobMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(wifiAsyncJob -> {
            assertThat(wifiAsyncJob.hasDevicePolicyRuleMigrationJob()).isTrue();
            assertThat(wifiAsyncJob.getDevicePolicyRuleMigrationJob()).isNotNull()
                .satisfies(job -> {
                  assertThat(job.getJobType())
                      .isEqualTo(DevicePolicyRuleMigrationJob.JobType.MIGRATION);
                  assertThat(job.getDevicePolicyId())
                      .isEqualTo(devicePolicyWithSinglePlayStation2Rule.getId());
                });
          });

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId(), requestId)).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList).asList()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
          .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasDevicePolicy)
          .hasSize(1).singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(Action.MODIFY);
            assertThat(op.getId()).isEqualTo(devicePolicyWithSinglePlayStation2Rule.getId());
            assertThat(op.getCommonInfo())
                .satisfies(commonInfo -> {
                  assertThat(commonInfo.getTenantId()).isEqualTo(tenant.getId());
                  assertThat(commonInfo.getRequestId()).startsWith(requestId);
                  assertThat(commonInfo.getSender()).isEqualTo(ServiceType.WIFI_SERVICE);
                });
          })
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDevicePolicy)
          .satisfies(devicePolicy -> {
            assertThat(devicePolicy.getDefaultAccess()).isEqualTo(OSAccessEnum.OS_ALLOW);
            assertThat(devicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
            assertThat(devicePolicy.getDevicePolicyRulesList())
                .hasSize(2)
                .allSatisfy(devicePolicyRule -> {
                  assertThat(devicePolicyRule.getAction()).isEqualTo(OSAccessEnum.OS_BLOCK);
                  assertThat(devicePolicyRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                })
                .extracting(DevicePolicyRule::getOsVendor)
                .containsExactlyInAnyOrder(OsVendorEnum.PLAYSTATION, OsVendorEnum.PS2);
          });
    }
  }

  @Nested
  class GivenDevicePolicyWithSinglePlayStation3RulePersistedInDb {

    private DevicePolicy devicePolicyWithSinglePlayStation3Rule;

    @BeforeEach
    void givenDevicePolicyWithSinglePlayStation3RulePersistedInDb(final Tenant tenant) {
      devicePolicyWithSinglePlayStation3Rule = repositoryUtil.createOrUpdate(devicePolicy()
          .setTenant(always(tenant))
          .setDefaultAccess(always(AccessEnum.ALLOW))
          .generate(devicePolicy ->
              devicePolicy.setRules(devicePolicyRule()
                  .setAction(always(AccessEnum.BLOCK))
                  .setDeviceType(always(Gaming))
                  .setOsVendor(always(PlayStation2))
                  .setDevicePolicy(always(devicePolicy))
                  .generate(1))), tenant.getId(), randomTxId());
    }

    @Test
    void testConsumeDevicePolicyRuleMigrationJobWithRoutineJobType(final Tenant tenant) {

      final var routineJob = WifiAsyncJob.newBuilder()
          .setDevicePolicyRuleMigrationJob(
              DevicePolicyRuleMigrationJob.newBuilder().setJobType(JobType.ROUTINE)).build();
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, routineJob);

      final var wifiAsyncJobMessage = messageCaptors.getWifiAsyncJobMessageCaptor()
          .getValue(tenant.getId(), 2);

      assertThat(wifiAsyncJobMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(wifiAsyncJob -> {
            assertThat(wifiAsyncJob.hasDevicePolicyRuleMigrationJob()).isTrue();
            assertThat(wifiAsyncJob.getDevicePolicyRuleMigrationJob()).isNotNull()
                .satisfies(job -> {
                  assertThat(job.getJobType())
                      .isEqualTo(DevicePolicyRuleMigrationJob.JobType.MIGRATION);
                  assertThat(job.getDevicePolicyId())
                      .isEqualTo(devicePolicyWithSinglePlayStation3Rule.getId());
                });
          });

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId(), requestId)).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList).asList()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
          .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasDevicePolicy)
          .hasSize(1).singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(Action.MODIFY);
            assertThat(op.getId()).isEqualTo(devicePolicyWithSinglePlayStation3Rule.getId());
            assertThat(op.getCommonInfo())
                .satisfies(commonInfo -> {
                  assertThat(commonInfo.getTenantId()).isEqualTo(tenant.getId());
                  assertThat(commonInfo.getRequestId()).startsWith(requestId);
                  assertThat(commonInfo.getSender()).isEqualTo(ServiceType.WIFI_SERVICE);
                });
          })
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDevicePolicy)
          .satisfies(devicePolicy -> {
            assertThat(devicePolicy.getDefaultAccess()).isEqualTo(OSAccessEnum.OS_ALLOW);
            assertThat(devicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
            assertThat(devicePolicy.getDevicePolicyRulesList())
                .hasSize(2)
                .allSatisfy(devicePolicyRule -> {
                  assertThat(devicePolicyRule.getAction()).isEqualTo(OSAccessEnum.OS_BLOCK);
                  assertThat(devicePolicyRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                })
                .extracting(DevicePolicyRule::getOsVendor)
                .containsExactlyInAnyOrder(OsVendorEnum.PLAYSTATION, OsVendorEnum.PS2);
          });
    }
  }

  @Nested
  class GivenDevicePolicyWithPlayStation2AndPlayStation3RulesPersistedInDb {

    private DevicePolicy devicePolicyWithPlayStation2AndPlayStation3Rules;

    @BeforeEach
    void givenDevicePolicyWithPlayStation2AndPlayStation3RulesPersistedInDb(final Tenant tenant) {
      devicePolicyWithPlayStation2AndPlayStation3Rules = repositoryUtil.createOrUpdate(devicePolicy()
          .setTenant(always(tenant))
          .setDefaultAccess(always(AccessEnum.BLOCK))
          .generate(devicePolicy ->
              devicePolicy.setRules(devicePolicyRule()
                  .setAction(always(AccessEnum.ALLOW))
                  .setDeviceType(always(Gaming))
                  .setVlan(options(10, 20))
                  .setOsVendor(options(PlayStation2, PlayStation3))
                  .setDevicePolicy(always(devicePolicy))
                  .generate(2))), tenant.getId(), randomTxId());
    }

    @Test
    void testConsumeDevicePolicyRuleMigrationJobWithRoutineJobType(final Tenant tenant) {

      final var routineJob = WifiAsyncJob.newBuilder()
          .setDevicePolicyRuleMigrationJob(
              DevicePolicyRuleMigrationJob.newBuilder().setJobType(JobType.ROUTINE)).build();
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, routineJob);

      final var wifiAsyncJobMessage = messageCaptors.getWifiAsyncJobMessageCaptor()
          .getValue(tenant.getId(), 2);

      assertThat(wifiAsyncJobMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(wifiAsyncJob -> {
            assertThat(wifiAsyncJob.hasDevicePolicyRuleMigrationJob()).isTrue();
            assertThat(wifiAsyncJob.getDevicePolicyRuleMigrationJob()).isNotNull()
                .satisfies(job -> {
                  assertThat(job.getJobType())
                      .isEqualTo(DevicePolicyRuleMigrationJob.JobType.MIGRATION);
                  assertThat(job.getDevicePolicyId())
                      .isEqualTo(devicePolicyWithPlayStation2AndPlayStation3Rules.getId());
                });
          });

      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId(), requestId)).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList).asList()
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
          .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasDevicePolicy)
          .hasSize(1).singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(Action.MODIFY);
            assertThat(op.getId()).isEqualTo(devicePolicyWithPlayStation2AndPlayStation3Rules.getId());
            assertThat(op.getCommonInfo())
                .satisfies(commonInfo -> {
                  assertThat(commonInfo.getTenantId()).isEqualTo(tenant.getId());
                  assertThat(commonInfo.getRequestId()).startsWith(requestId);
                  assertThat(commonInfo.getSender()).isEqualTo(ServiceType.WIFI_SERVICE);
                });
          })
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDevicePolicy)
          .satisfies(devicePolicy -> {
            assertThat(devicePolicy.getDefaultAccess()).isEqualTo(OSAccessEnum.OS_BLOCK);
            assertThat(devicePolicy.getDevicePolicyRulesCount()).isEqualTo(2);
            assertThat(devicePolicy.getDevicePolicyRulesList())
                .hasSize(2)
                .allSatisfy(devicePolicyRule -> {
                  assertThat(devicePolicyRule.getAction()).isEqualTo(OSAccessEnum.OS_ALLOW);
                  assertThat(devicePolicyRule.getDeviceType()).isEqualTo(DeviceTypeEnum.GAMING);
                  assertThat(devicePolicyRule.getVlan().getValue()).isEqualTo(20);
                })
                .extracting(DevicePolicyRule::getOsVendor)
                .containsExactlyInAnyOrder(OsVendorEnum.PLAYSTATION, OsVendorEnum.PS2);
          });
    }
  }
}
