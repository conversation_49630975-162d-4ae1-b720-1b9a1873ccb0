package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tenant (id) VALUES ('a782b402d95c46b4aabce87b77e68612');
    INSERT INTO network (id, name, type, tenant, is_template) VALUES (
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        'be3cb5cdef1d4fedbb7b778774157e41',
        'OPEN',
        '4c8279f79307415fa9e4c88a1819f0fc',
        false); 
    INSERT INTO network (id, name, type, tenant, is_template) VALUES (
        'efd0e13cc150444ca1956dd68c8999f2',
        '37081c84876547f28b2a11950ad46141',
        'DPSK',
        'a782b402d95c46b4aabce87b77e68612',
        false); 
    INSERT INTO network (id, name, type, tenant, dsae_network_pair_id, is_dsae_service_network, is_template) VALUES (
        '0fce2b2a2dee43d0af1b12d036fb618b',
        '37081c84876547f28b2a11950ad46142',
        'DPSK',
        'a782b402d95c46b4aabce87b77e68612',
        'b3c8da5b6b6940cd87db89b2948ca51b',
        true,
        false);  
    INSERT INTO network (id, name, type, tenant, dsae_network_pair_id, is_dsae_service_network, is_template) VALUES (
        '0fce2b2a2dee43d0af1b12d036fb618c',
        '37081c84876547f28b2a11950ad46143',
        'DPSK',
        'a782b402d95c46b4aabce87b77e68612',
        'b3c8da5b6b6940cd87db89b2948ca51b',
        false,
        false);      
    INSERT INTO network (id, name, type, tenant, dsae_network_pair_id, is_dsae_service_network, is_template) VALUES (
        '0fce2b2a2dee43d0af1b12d036fb618d',
        '37081c84876547f28b2a11950ad46144',
        'DPSK',
        'a782b402d95c46b4aabce87b77e68612',
        'b3c8da5b6b6940cd87db89b2948ca51b',
        false,
        true);    
    """)
public class DpskNetworkRepositoryTest {

  @Autowired
  private DpskNetworkRepository target;

  @Test
  void testFindTenantIdByType() {
    var result = target.countByTenantId("a782b402d95c46b4aabce87b77e68612");
    assertEquals(3, result);
  }

  @ApplyTemplateFilter
  @Test
  void countByTenantIdAndIsTemplate() {
    var result = target.countByTenantId("a782b402d95c46b4aabce87b77e68612");
    assertEquals(1, result);
  }

  @Test
  void testFindByDsaeNetworkPairIdAndTenantIdAndIsDsaeServiceNetwork() {
    var result = target.findByDsaeNetworkPairIdAndTenantIdAndIsDsaeServiceNetwork(
        "b3c8da5b6b6940cd87db89b2948ca51b", "a782b402d95c46b4aabce87b77e68612", true);
    assertEquals("0fce2b2a2dee43d0af1b12d036fb618b", result.getId());
  }

  @Test
  void testFindByDsaeNetworkPairIdInAndTenantIdAndIsDsaeServiceNetwork() {
    var result = target.findByDsaeNetworkPairIdInAndTenantIdAndIsDsaeServiceNetwork(
        List.of("b3c8da5b6b6940cd87db89b2948ca51b"), "a782b402d95c46b4aabce87b77e68612", false);
    assertEquals("0fce2b2a2dee43d0af1b12d036fb618c", result.get(0).getId());
  }
}
