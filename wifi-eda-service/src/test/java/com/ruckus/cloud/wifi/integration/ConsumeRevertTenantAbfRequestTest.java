package com.ruckus.cloud.wifi.integration;

import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNull;

@WifiIntegrationTest
public class ConsumeRevertTenantAbfRequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Test
  public void revertTenantAbf(Tenant adminTenant) {
    ApVersion defaultVersion = ApVersionTestFixture.recommendedApVersion("6.2.2.103.111", a -> {});
    repositoryUtil.createOrUpdate(defaultVersion, adminTenant.getId(), randomTxId());
    ApVersion targetVersion = ApVersionTestFixture.recommendedApVersion("6.2.0.103.111", a -> {});
    repositoryUtil.createOrUpdate(targetVersion, adminTenant.getId(), randomTxId());
    List<String> modelsWithSpecificAttrs = List.of("R550", "R560");

    String requestId = randomTxId();
    String userName = randomName();
    List<Tenant> tenantList =
        List.of(
            TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
            TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));
    tenantList.forEach(
        tenant -> repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId()));

    List<String> tenantIdList = tenantList.stream().map(tenant -> {
      tenant = repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId());
      createTenantFirmwareVersion(tenant, "eol-ap-2022-12", targetVersion);

      Venue venue = VenueTestFixture.randomVenue(tenant, venue1 -> venue1.setWifiFirmwareVersion(defaultVersion));
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
      createVenueFirmwareVersion(venue, "eol-ap-2022-12", targetVersion);
      createVenueApModelSpecificAttributes(venue, modelsWithSpecificAttrs);
      return tenant.getId();
    }).toList();

    var params = new RequestParams();
    params.addPathVariable("firmwareVersion", targetVersion.getId());

    // When
    messageUtil.sendWifiCfgRequest(
        adminTenant.getId(),
        requestId,
        CfgExtendedAction.REVERT_TENANT_ABF,
        userName,
        params,
        tenantIdList);

    // Then
    validateTenants(tenantList, targetVersion);
    tenantList.forEach(tenant -> {
      List<Venue> venues = repositoryUtil.findAll(Venue.class, tenant.getId());
      validateVenuesAndApModelSpecificAttributes(venues, targetVersion, modelsWithSpecificAttrs, List.of("R560"));
    });
  }

  @Nested
  class whenRevertVersionHasAbfCustomModelRules {

    @Test
    public void revertTenantAbfFor700104Tenant(Tenant adminTenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.104.100") ApVersion defaultVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.100") ApVersion targetVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.100") ApVersion versionInAbf1) {
      List<String> modelsWithSpecificAttrs = List.of("R770", "R550", "R500", "R350");
      String requestId = randomTxId();
      String userName = randomName();
      List<Tenant> tenantList =
          List.of(
              TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
              TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));

      List<String> tenantIdList = tenantList.stream().map(tenant -> {
        tenant = repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId());
        createTenantFirmwareVersion(tenant, "eol-ap-2022-12", versionInAbf1);
        createTenantFirmwareVersion(tenant, "ABF2-3R", targetVersion);

        Venue venue = VenueTestFixture.randomVenue(tenant, venue1 -> venue1.setWifiFirmwareVersion(defaultVersion));
        repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
        createVenueFirmwareVersion(venue, "eol-ap-2022-12", versionInAbf1);
        createVenueFirmwareVersion(venue, "ABF2-3R", targetVersion);
        createVenueApModelSpecificAttributes(venue, modelsWithSpecificAttrs);
        return tenant.getId();
      }).toList();

      var params = new RequestParams();
      params.addPathVariable("firmwareVersion", targetVersion.getId());

      // When
      messageUtil.sendWifiCfgRequest(
          adminTenant.getId(),
          requestId,
          CfgExtendedAction.REVERT_TENANT_ABF,
          userName,
          params,
          tenantIdList);

      // Then
      validateTenants(tenantList, targetVersion);
      tenantList.forEach(tenant -> {
        List<Venue> venues = repositoryUtil.findAll(Venue.class, tenant.getId());
        validateVenuesAndApModelSpecificAttributes(venues, targetVersion, modelsWithSpecificAttrs, List.of("R770"));
      });
    }

    @Test
    public void revertTenantAbfFor700105Tenant(Tenant adminTenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.100") ApVersion defaultVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.3.103.100") ApVersion targetVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.100") ApVersion versionInAbf1) {
      List<String> modelsWithSpecificAttrs = List.of("R770", "R550", "R500", "R350:R350E");

      String requestId = randomTxId();
      String userName = randomName();
      List<Tenant> tenantList =
          List.of(
              TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)),
              TenantTestFixture.randomTenant((t) -> t.setLatestReleaseVersion(defaultVersion)));

      List<String> tenantIdList = tenantList.stream().map(tenant -> {
        tenant = repositoryUtil.createOrUpdate(tenant, adminTenant.getId(), randomTxId());
        createTenantFirmwareVersion(tenant, "eol-ap-2022-12", versionInAbf1);
        createTenantFirmwareVersion(tenant, "ABF2-3R", targetVersion);

        Venue venue = VenueTestFixture.randomVenue(tenant, venue1 -> venue1.setWifiFirmwareVersion(defaultVersion));
        repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
        createVenueFirmwareVersion(venue, "eol-ap-2022-12", versionInAbf1);
        createVenueFirmwareVersion(venue, "ABF2-3R", targetVersion);
        createVenueApModelSpecificAttributes(venue, modelsWithSpecificAttrs);
        return tenant.getId();
      }).toList();

      var params = new RequestParams();
      params.addPathVariable("firmwareVersion", targetVersion.getId());

      // When
      messageUtil.sendWifiCfgRequest(
          adminTenant.getId(),
          requestId,
          CfgExtendedAction.REVERT_TENANT_ABF,
          userName,
          params,
          tenantIdList);

      // Then
      validateTenants(tenantList, targetVersion);
      tenantList.forEach(tenant -> {
        List<Venue> venues = repositoryUtil.findAll(Venue.class, tenant.getId());
        validateVenuesAndApModelSpecificAttributes(venues, targetVersion, modelsWithSpecificAttrs,
            List.of("R770", "R350:R350E"));
      });
    }
  }

  private void createTenantFirmwareVersion(Tenant tenant, String branchType, ApVersion version) {
    TenantFirmwareVersion tfv = new TenantFirmwareVersion();
    tfv.setId(randomId());
    tfv.setBranchType(branchType);
    tfv.setLatestFirmwareVersion(version);
    tfv.setTenant(tenant);
    repositoryUtil.createOrUpdate(tfv, tenant.getId(), randomTxId());
  }

  void validateTenants(List<Tenant> tenants, ApVersion targetVersion) {
    tenants.forEach(
        (tenant) -> {
          Tenant updatedTenant = repositoryUtil.find(Tenant.class, tenant.getId());
          assertThat(updatedTenant)
              .isNotNull()
              .matches(t -> Objects.equals(tenant.getId(), t.getId()))
              .matches(
                  t -> Objects.equals(targetVersion.getId(), t.getLatestReleaseVersion().getId()));
          assertNull(tenant.getLatestBetaVersion());
        });
  }

  void validateVenuesAndApModelSpecificAttributes(List<Venue> updatedVenues, ApVersion targetVersion,
      List<String> modelsWithSpecificAttrs, List<String> unsupportedModels) {
    List<String> modelsWithSpecificAttrsAfterRevert = new ArrayList<>(modelsWithSpecificAttrs);
    modelsWithSpecificAttrsAfterRevert.removeIf(unsupportedModels::contains);

    assertThat(updatedVenues)
        .hasSize(1)
        .allMatch(venue -> targetVersion.getId().equals(venue.getWifiFirmwareVersion().getId()))
        .flatExtracting(Venue::getModelSpecificAttributes)
        .extracting(VenueApModelSpecificAttributes::getModel)
        .hasSize(modelsWithSpecificAttrsAfterRevert.size())
        .doesNotContainAnyElementsOf(unsupportedModels)
        .containsAll(modelsWithSpecificAttrsAfterRevert);
  }

  private void createVenueFirmwareVersion(Venue venue, String branchType, ApVersion version) {
    VenueFirmwareVersion vfv = new VenueFirmwareVersion();
    vfv.setTenant(venue.getTenant());
    vfv.setVenue(venue);
    vfv.setBranchType(branchType);
    vfv.setCurrentFirmwareVersion(version);
    repositoryUtil.createOrUpdate(vfv, venue.getTenant().getId(), randomTxId());
  }

  private void createVenueApModelSpecificAttributes(Venue venue, List<String> models) {
    models.forEach(model -> {
      VenueApModelSpecificAttributes vamsa = new VenueApModelSpecificAttributes();
      vamsa.setVenue(venue);
      vamsa.setModel(model);
      repositoryUtil.createOrUpdate(vamsa, venue.getTenant().getId(), randomTxId());
    });
  }
}
