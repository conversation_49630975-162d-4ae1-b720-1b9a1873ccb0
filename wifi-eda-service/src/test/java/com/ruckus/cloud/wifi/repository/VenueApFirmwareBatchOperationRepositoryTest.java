package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.VenueApFirmwareBatchOperation;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApModelCountProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Slf4j
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenantId');
    INSERT INTO venue (id, tenant)
        VALUES ('venueId1', 'tenantId'),
               ('venueId2', 'tenantId'),
               ('venueId3', 'tenantId');
    INSERT INTO venue_ap_firmware_batch_operation(id, batch_id, tenant, venue, impacted_ap_count)
        VALUES ('vafboId1', 'batchId1', 'tenantId', 'venueId1', 3),
                ('vafboId2', 'batchId1', 'tenantId', 'venueId2', 5),
                ('vafboId3', 'batchId1', 'tenantId', 'venueId3', 7),
                ('vafboId4', 'batchId2', 'tenantId', 'venueId1', 1),
                ('vafboId5', 'batchId2', 'tenantId', 'venueId2', 1),
                ('vafboId6', 'batchId2', 'tenantId', 'venueId3', 1);
      """)
class VenueApFirmwareBatchOperationRepositoryTest {

  @Autowired
  private VenueApFirmwareBatchOperationRepository venueApFirmwareBatchOperationRepository;

  @Test
  void testDeleteByTenantIdAndBatchId() {
    int originalCount = (int) venueApFirmwareBatchOperationRepository.count();
    assertThat(originalCount).isEqualTo(6);

    int result = venueApFirmwareBatchOperationRepository.deleteByTenantIdAndBatchId("tenantId", "batchId1");
    assertThat(result).isEqualTo(3);

    List<VenueApFirmwareBatchOperation> afterResultSet = venueApFirmwareBatchOperationRepository.findAll();
    assertThat(afterResultSet).isNotNull().hasSize(3)
        .allMatch(p -> p.getBatchId().equals("batchId2"));
  }

  @Test
  void testDeleteByTenantIdAndVenueId() {
    int originalCount = (int) venueApFirmwareBatchOperationRepository.count();
    assertThat(originalCount).isEqualTo(6);

    int result = venueApFirmwareBatchOperationRepository.deleteByTenantIdAndVenueId("tenantId", "venueId1");
    assertThat(result).isEqualTo(2);

    List<VenueApFirmwareBatchOperation> afterResultSet = venueApFirmwareBatchOperationRepository.findAll();
    assertThat(afterResultSet).isNotNull().hasSize(4)
        .allMatch(p -> !p.getVenue().getId().equals("venueId1"));
  }

  @Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenantId1');
    INSERT INTO venue (id, tenant)
        VALUES ('venueId1', 'tenantId1'),
               ('venueId2', 'tenantId1'),
               ('venueId3', 'tenantId1');
    INSERT INTO ap_group (id, tenant, venue)
        VALUES ('apgId1', 'tenantId1', 'venueId1'),
               ('apgId2', 'tenantId1', 'venueId2'),
               ('apgId3', 'tenantId1', 'venueId3');
    INSERT INTO venue_ap_firmware_batch_operation (id, batch_id, tenant, venue, target_ap_models)
        VALUES ('vafboId1', 'batchId1', 'tenantId1', 'venueId1', '["R550", "R350"]'),
                ('vafboId2', 'batchId1', 'tenantId1', 'venueId2', '["R650", "R770"]'),
                ('vafboId3', 'batchId1', 'tenantId1', 'venueId3', '["R550"]'),
                ('vafboId4', 'batchId2', 'tenantId1', 'venueId1', '["R500", "R550"]'),
                ('vafboId5', 'batchId2', 'tenantId1', 'venueId2', '["R600"]'),
                ('vafboId6', 'batchId2', 'tenantId1', 'venueId3', '["R600", "R750"]');
    INSERT INTO ap (id, model, ap_group, tenant)
        VALUES ('apId1', 'R550', 'apgId1', 'tenantId1'),
               ('apId2', 'R350', 'apgId1', 'tenantId1'),
               ('apId3', 'R350', 'apgId1', 'tenantId1'),
               ('apId4', 'R770', 'apgId1', 'tenantId1'),
               ('apId5', 'R350', 'apgId1', 'tenantId1'),
               ('apId6', 'R650', 'apgId2', 'tenantId1'),
               ('apId7', 'R650', 'apgId2', 'tenantId1'),
               ('apId8', 'R550', 'apgId3', 'tenantId1'),
               ('apId9', 'R550', 'apgId3', 'tenantId1');
      """)
  @Test
  void testFindImpactedApCountByTenantIdAndBatchId() {
    List<VenueApModelCountProjection> result = venueApFirmwareBatchOperationRepository.findImpactedApCountByTenantIdAndBatchId(
        "tenantId1", "batchId1");

    assertThat(result).isNotEmpty().hasSize(4).satisfies(r -> {
      assertThat(r).filteredOn(p -> p.getVenueId().equals("venueId1"))
          .hasSize(2)
          .anyMatch(p -> p.getApModel().equals("R550") && p.getApCount() == 1)
          .anyMatch(p -> p.getApModel().equals("R350") && p.getApCount() == 3);

      assertThat(r).filteredOn(p -> p.getVenueId().equals("venueId2"))
          .hasSize(1)
          .anyMatch(p -> p.getApModel().equals("R650") && p.getApCount() == 2);
    });
  }
}
