package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortType.ApLanPortType_UNSET;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationAttributeTypeEnum.AP_MAC;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationAttributeTypeEnum.AP_MODEL;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationAttributeTypeEnum.AP_NAME;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationAttributeTypeEnum.ESSID;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationAttributeTypeEnum.INTERFACE;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationAttributeTypeEnum.INTERFACE_NO_PREFIX;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationAttributeTypeEnum.USER_DEFINED;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationAttributeTypeEnum.VLAN;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationDelimiterEnum.COLON;
import static com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationDelimiterEnum.SEMICOLON;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.DhcpOption82MacFormat;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpOption82SubOption1CustomizationAttributeTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.IsolatePacketsTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpOption82SubOption1Customization;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpOption82SubOption1CustomizationAttribute;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.ApLanPortRepository;
import com.ruckus.cloud.wifi.repository.LanPortAdoptionRepository;
import com.ruckus.cloud.wifi.repository.VenueLanPortRepository;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption151Enum;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class DdccmLanPortAdoptionOperationBuilderTest {

  @MockBean private VenueLanPortRepository venueLanPortRepository;
  @MockBean private ApLanPortRepository apLanPortRepository;
  @MockBean private LanPortAdoptionRepository lanPortAdoptionRepository;
  @SpyBean private DdccmEthernetPortProfileOperationBuilder ethernetPortProfileOperationBuilder;
  @SpyBean private DdccmPinLanPortProfileOperationBuilder pinLanPortProfileOperationBuilder;

  @SpyBean private DdccmLanPortAdoptionOperationBuilder unit;

  @RegisterExtension public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  private final Map<IsolatePacketsTypeEnum, Boolean> MULTICAST_ENABLED =
      Map.of(
          IsolatePacketsTypeEnum.MULTICAST, true,
          IsolatePacketsTypeEnum.UNICAST_MULTICAST, true,
          IsolatePacketsTypeEnum.UNICAST, false);

  private final Map<IsolatePacketsTypeEnum, Boolean> UNICAST_ENABLED =
      Map.of(
          IsolatePacketsTypeEnum.UNICAST, true,
          IsolatePacketsTypeEnum.UNICAST_MULTICAST, true,
          IsolatePacketsTypeEnum.MULTICAST, false);

  @Test
  void testAddLanPortAdoptionWithoutFeatureFlags() {
    var adoption = Generators.lanPortAdoption().generate();
    var operations = unit.build(new NewTxEntity<>(adoption), emptyTxChanges());

    assertThat(operations).isEmpty();
    verify(unit, never()).config(any(), any(), any(), any());
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Test
  void testAddLanPortAdoption() {
    var adoption = Generators.lanPortAdoption().generate();
    var operations = unit.build(new NewTxEntity<>(adoption), emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.ADD, adoption, operation.getApLanPortProfile()));
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Test
  void testAddLanPortAdoptionWithSoftGreProfile() {
    var adoption =
        Generators.lanPortAdoption()
            .setSoftGreActivation(Generators.softGreProfileLanPortActivation())
            .generate();
    var operations = unit.build(new NewTxEntity<>(adoption), emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.ADD, adoption, operation.getApLanPortProfile()));
  }

  @FeatureFlag(
      enable = {
        FlagNames.ACX_UI_ETHERNET_TOGGLE,
        FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
        FlagNames.WIFI_IPSEC_PSK_OVER_NETWORK
      })
  @Test
  void testAddLanPortAdoptionWithIpsecProfile() {
    var adoption =
        Generators.lanPortAdoption()
            .setSoftGreActivation(Generators.softGreProfileLanPortActivationWithIpsec())
            .generate();
    var operations = unit.build(new NewTxEntity<>(adoption), emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.ADD, adoption, operation.getApLanPortProfile()));
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Test
  void testAddLanPortAdoptionWithDhcpOption82Activation() {
    var adoption =
        Generators.lanPortAdoption()
            .setDhcpOption82Activation(
                Generators.dhcpOption82LanPortActivation().setSubOption151Text(randomString(6)))
            .generate();
    var operations = unit.build(new NewTxEntity<>(adoption), emptyTxChanges());

    var expectedDcpOption82Format =
        "1,string,ETH:$IFNAME$:$VLAN$:$SSID$:$MODEL$:$HOSTNAME$:$DEVMAC$:$LOCATION$;2,hex,$STAMAC$:$SSID$;150,hex,$IPVLAN$;151,string,$ADDTYPE$%s;"
            .formatted(adoption.getDhcpOption82Activation().getSubOption151Text());
    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation ->
                validate(
                    EntityAction.ADD,
                    adoption,
                    operation.getApLanPortProfile(),
                    expectedDcpOption82Format));
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Test
  void testModifyLanPortAdoption() {
    var adoption =
        Generators.lanPortAdoption()
            .setSoftGreActivation(Generators.softGreProfileLanPortActivation())
            .generate();
    var operations =
        unit.build(new ModifiedTxEntity<>(adoption, Set.of("softGreActivation")), emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.MODIFY, adoption, operation.getApLanPortProfile()));
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Test
  void testModifyLanPortAdoptionAndDhcpOption82On151Essid() {
    var adoption =
        Generators.lanPortAdoption()
            .setDhcpOption82Activation(
                Generators.dhcpOption82LanPortActivation()
                    .setSubOption151Format(always(DhcpOption82SubOption151Enum.SUBOPT151_ESSID)))
            .generate();

    var operations =
        unit.build(
            new ModifiedTxEntity<>(adoption, Set.of("dhcpOption82LanPortActivation")),
            emptyTxChanges());
    var expectedDcpOption82Format =
        "1,string,ETH:$IFNAME$:$VLAN$:$SSID$:$MODEL$:$HOSTNAME$:$DEVMAC$:$LOCATION$;2,hex,$STAMAC$:$SSID$;150,hex,$IPVLAN$;151,string,$SSID$;";
    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation ->
                validate(
                    EntityAction.MODIFY,
                    adoption,
                    operation.getApLanPortProfile(),
                    expectedDcpOption82Format));
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Test
  void testModifyLanPortAdoptionWithDhcpOption82ButDisableSub() {
    var adoption = Generators.lanPortAdoption().generate();
    var expectedDcpOption82Format = "";

    var operations =
        unit.build(
            new ModifiedTxEntity<>(adoption, Set.of("dhcpOption82LanPortActivation")),
            emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation ->
                validate(
                    EntityAction.MODIFY,
                    adoption,
                    operation.getApLanPortProfile(),
                    expectedDcpOption82Format));
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Test
  void testDeleteLanPortAdoption() {
    var adoption = Generators.lanPortAdoption().generate();
    var operations = unit.build(new DeletedTxEntity<>(adoption), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(
            operation ->
                operation.hasApLanPortProfile()
                    && adoption
                        .getEthernetPortProfileId()
                        .equals(operation.getApLanPortProfile().getId()));
  }

  @FeatureFlag(enable = {FlagNames.ACX_UI_ETHERNET_TOGGLE, FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE})
  @Test
  void testAddLanPortAdoptionWithClientIsolationActivationButDisableFeatureFlag() {
    var adoption =
        Generators.lanPortAdoption()
            .setClientIsolationActivation(Generators.clientIsolationLanPortActivation())
            .generate();
    var operations = unit.build(new NewTxEntity<>(adoption), emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> {
              assertTrue(operation.getApLanPortProfile().hasClientIsolation());
              assertFalse(
                  operation.getApLanPortProfile().getClientIsolation().getClientIsolationEnabled());
            });
  }

  @FeatureFlag(
      enable = {
        FlagNames.ACX_UI_ETHERNET_TOGGLE,
        FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
        FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE
      })
  @Test
  void testAddLanPortAdoptionWithNoClientIsolation() {
    var adoption = Generators.lanPortAdoption().generate();
    var operations = unit.build(new NewTxEntity<>(adoption), emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.ADD, adoption, operation.getApLanPortProfile()));
  }

  @FeatureFlag(
      enable = {
        FlagNames.ACX_UI_ETHERNET_TOGGLE,
        FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
        FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE
      })
  @Test
  void testAddLanPortAdoptionWithClientIsolation() {
    var adoption =
        Generators.lanPortAdoption()
            .setClientIsolationActivation(Generators.clientIsolationLanPortActivation())
            .generate();
    var operations = unit.build(new NewTxEntity<>(adoption), emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.ADD, adoption, operation.getApLanPortProfile()));
  }

  @FeatureFlag(
      enable = {
        FlagNames.ACX_UI_ETHERNET_TOGGLE,
        FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
        FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE
      })
  @Test
  void testAddLanPortAdoptionWithClientIsolationAndAllowList() {
    var adoption =
        Generators.lanPortAdoption()
            .setClientIsolationActivation(
                Generators.clientIsolationLanPortActivation()
                    .setClientIsolationAllowlist(Generators.clientIsolationAllowlist()))
            .generate();
    var operations = unit.build(new NewTxEntity<>(adoption), emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.ADD, adoption, operation.getApLanPortProfile()));
  }

  @FeatureFlag(
      enable = {
        FlagNames.ACX_UI_ETHERNET_TOGGLE,
        FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
        FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE
      })
  @Test
  void testModifyLanPortAdoptionWithClientIsolation() {
    var adoption =
        Generators.lanPortAdoption()
            .setClientIsolationActivation(
                Generators.clientIsolationLanPortActivation()
                    .setClientIsolationAllowlist(Generators.clientIsolationAllowlist()))
            .generate();
    var operations =
        unit.build(
            new ModifiedTxEntity<>(adoption, Set.of("clientIsolationActivation")),
            emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.MODIFY, adoption, operation.getApLanPortProfile()));
  }

  @FeatureFlag(
      enable = {
        FlagNames.ACX_UI_ETHERNET_TOGGLE,
        FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
        FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE
      })
  @Test
  void testModifyLanPortAdoptionWithClientIsolationUnicast() {
    var adoption =
        Generators.lanPortAdoption()
            .setClientIsolationActivation(
                Generators.clientIsolationLanPortActivation()
                    .setPacketsType(always(IsolatePacketsTypeEnum.UNICAST))
                    .setClientIsolationAllowlist(Generators.clientIsolationAllowlist()))
            .generate();
    var operations =
        unit.build(
            new ModifiedTxEntity<>(adoption, Set.of("clientIsolationActivation")),
            emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.MODIFY, adoption, operation.getApLanPortProfile()));
  }

  @FeatureFlag(
      enable = {
        FlagNames.ACX_UI_ETHERNET_TOGGLE,
        FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE,
        FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE
      })
  @Test
  void testModifyLanPortAdoptionWithClientIsolationMulticast() {
    var adoption =
        Generators.lanPortAdoption()
            .setClientIsolationActivation(
                Generators.clientIsolationLanPortActivation()
                    .setPacketsType(always(IsolatePacketsTypeEnum.MULTICAST))
                    .setClientIsolationAllowlist(Generators.clientIsolationAllowlist()))
            .generate();
    var operations =
        unit.build(
            new ModifiedTxEntity<>(adoption, Set.of("clientIsolationActivation")),
            emptyTxChanges());

    assertThat(operations)
        .hasSize(1)
        .allSatisfy(Operation::hasApLanPortProfile)
        .allSatisfy(
            operation -> validate(EntityAction.MODIFY, adoption, operation.getApLanPortProfile()));
  }

  @Test
  void testGetCustomizationAttribute() {
    var customization1 = new DhcpOption82SubOption1Customization();
    customization1.setDelimiter(SEMICOLON);
    customization1.setAttributes(
        List.of(createAttribute(INTERFACE), createAttribute(VLAN), createAttribute(AP_NAME)));
    var result1 = unit.getCustomizationAttribute(customization1);
    assertEquals("1,string,ETH:$IFNAME$\\\\;$VLAN$\\\\;$HOSTNAME$;", result1.toString());

    var customization2 = new DhcpOption82SubOption1Customization();
    customization2.setDelimiter(COLON);
    customization2.setAttributes(
        List.of(createAttribute(INTERFACE_NO_PREFIX), createAttribute(AP_MAC)));
    var result2 = unit.getCustomizationAttribute(customization2);
    assertEquals("1,string,$IFNAME$:$DEVMAC$;", result2.toString());

    var customization3 = new DhcpOption82SubOption1Customization();
    customization3.setDelimiter(SEMICOLON);
    customization3.setAttributes(
        List.of(
            createAttribute(AP_MODEL),
            createUserDefinedAttribute("CustomText"),
            createAttribute(ESSID)));
    var result3 = unit.getCustomizationAttribute(customization3);
    assertEquals("1,string,$MODEL$\\\\;CustomText\\\\;$SSID$;", result3.toString());

    var customization4 = new DhcpOption82SubOption1Customization();
    customization4.setDelimiter(COLON);
    customization4.setAttributes(
        List.of(
            createAttribute(INTERFACE),
            createAttribute(INTERFACE_NO_PREFIX),
            createAttribute(VLAN),
            createAttribute(ESSID),
            createAttribute(AP_MODEL),
            createAttribute(AP_NAME),
            createAttribute(AP_MAC),
            createUserDefinedAttribute("TestValue")));
    var result4 = unit.getCustomizationAttribute(customization4);
    assertEquals(
        "1,string,ETH:$IFNAME$:$IFNAME$:$VLAN$:$SSID$:$MODEL$:$HOSTNAME$:$DEVMAC$:TestValue;",
        result4.toString());
  }

  private DhcpOption82SubOption1CustomizationAttribute createUserDefinedAttribute(String text) {
    var attribute = new DhcpOption82SubOption1CustomizationAttribute();
    attribute.setType(USER_DEFINED);
    attribute.setText(text);
    return attribute;
  }

  private DhcpOption82SubOption1CustomizationAttribute createAttribute(
      DhcpOption82SubOption1CustomizationAttributeTypeEnum type) {
    var attribute = new DhcpOption82SubOption1CustomizationAttribute();
    attribute.setType(type);
    return attribute;
  }

  private void validate(EntityAction action, LanPortAdoption source, ApLanPortProfile target) {
    validate(action, source, target, null);
  }

  private void validate(
      EntityAction action,
      LanPortAdoption source,
      ApLanPortProfile target,
      String dhcpOption82Format) {
    if (action != EntityAction.DELETE) {
      assertNotEquals("", target.getTenantId());
      var untagId =
          source.getApLanPortProfile().getType() == ApLanPortTypeEnum.TRUNK
              ? 1
              : (int) source.getApLanPortProfile().getUntagId();
      assertEquals(untagId, target.getUntagId());
      assertEquals((int) source.getApLanPortProfile().getUntagId(), target.getUntagVlanId());
      assertEquals(source.getApLanPortProfile().getVlanMembers(), target.getVlanMembers());
      assertNotEquals(ApLanPortType_UNSET, target.getLanPortType());
    }
    assertEquals(source.getEthernetPortProfileId(), target.getId());
    assertEquals(source.getId(), target.getName());
    var softGreActivation = Optional.ofNullable(source.getSoftGreActivation());
    softGreActivation.ifPresentOrElse(
        activation -> {
          assertEquals(ApLanPortProfile.CcmAccessNetworkType.TUNNAL, target.getAccessNetworkType());
          assertEquals(
              activation.getSoftGreProfile().getId(), target.getTunnelProfile().getProfileId());
          if (activation.getIpsecProfile() != null) {
            assertEquals(
                activation.getIpsecProfile().getId(), target.getIpsecProfile().getProfileId());
          }
        },
        () -> {
          assertEquals(ApLanPortProfile.CcmAccessNetworkType.WAN, target.getAccessNetworkType());
          assertFalse(target.hasTunnelProfile());
        });
    var dhcpOption82Activation = Optional.ofNullable(source.getDhcpOption82Activation());
    dhcpOption82Activation.ifPresentOrElse(
        activation -> {
          var settings = dhcpOption82Activation.get();
          assertTrue(target.hasDhcpOption82Settings());
          assertEquals(1, target.getDhcpOption82Settings().getDhcpOption82());
          assertEquals(
              DhcpOption82MacFormat.valueOf(settings.getMacDelimiter().getValue()),
              target.getDhcpOption82Settings().getDhcpOption82MacFormat());
          assertEquals(
              dhcpOption82Format, target.getDhcpOption82Settings().getDhcpOption82Format());
        },
        () -> {
          assertTrue(target.hasDhcpOption82Settings());
          assertEquals(0, target.getDhcpOption82Settings().getDhcpOption82());
        });
    validateClientIsolation(target, source.getClientIsolationActivation());
  }

  private void validateClientIsolation(
      ApLanPortProfile target, ClientIsolationLanPortActivation clientIsolationLanPortActivation) {
    Optional.ofNullable(clientIsolationLanPortActivation)
        .ifPresentOrElse(
            activation -> {
              assertEquals(
                  activation.getAutoVrrp(),
                  target.getClientIsolation().getClientIsolationAutoVrrpEnabled());
              assertEquals(
                  MULTICAST_ENABLED.get(activation.getPacketsType()),
                  target.getClientIsolation().getClientIsolationMulticastEnabled());
              assertEquals(
                  UNICAST_ENABLED.get(activation.getPacketsType()),
                  target.getClientIsolation().getClientIsolationUnicastEnabled());
              Optional.ofNullable(activation.getClientIsolationAllowlist())
                  .ifPresentOrElse(
                      clientIsolationAllowlist ->
                          assertEquals(
                              clientIsolationAllowlist.getId(),
                              target.getClientIsolation().getClientIsolationWhitelistId()),
                      () ->
                          assertTrue(
                              StringUtils.isEmpty(
                                  target.getClientIsolation().getClientIsolationWhitelistId())));
              assertTrue(target.getUserSidePort().getUserSidePortEnabled());
              assertEquals(32, target.getUserSidePort().getUserSidePortMaxClient().getValue());
            },
            () -> {
              assertTrue(target.hasClientIsolation());
              assertFalse(target.getClientIsolation().getClientIsolationEnabled());
            });
  }
}
