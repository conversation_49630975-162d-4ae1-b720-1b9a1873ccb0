package com.ruckus.cloud.wifi.entitylistener;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.core.hibernate.entitylistener.AggregatedEntityListener;
import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.entitylistener.impl.EntityListenerManagerImpl;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.List;
import java.util.function.Predicate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
public class EntityListenerManagerTest {

  @Autowired
  private EntityListenerManager entityListenerManager;

  @Test
  public void testGetAll() {
    var result = entityListenerManager.getAll();

    assertThat(result).hasSize(2);
  }

  @Test
  public void testGet() {
    var result = entityListenerManager.get(EntityListenerEndpoint.DDCCM);
    assertThat(result).hasSize(1);
  }

  @Test
  public void testGet_returnEmptyList() {
    var result = entityListenerManager.get(EntityListenerEndpoint.DRS);
    assertThat(result).isEmpty();
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public EntityListenerManager entityListenerManager() {
      return new EntityListenerManagerImpl();
    }

    @Bean
    public DummyImpactedApListener dummyImpactedApListener() {
      return new DummyImpactedApListener();
    }

    @Bean
    public DummyDdccmListener dummyDdccmListener() {
      return new DummyDdccmListener();
    }

  }

  static private abstract class DummyListener implements AggregatedEntityListener<Void> {

    @Override
    public Predicate<BaseEntity> filter() {
      return null;
    }

    @Override
    public int flush(List<Void> voids) {
      return 0;
    }

    @Override
    public List<Void> build(TxEntity<?> txEntity, TxChanges changes) {
      return null;
    }
  }

  static private class DummyImpactedApListener extends DummyListener {

    @Override
    public String getEndpoint() {
      return EntityListenerEndpoint.IMPACTED_AP.name();
    }
  }

  static private class DummyDdccmListener extends DummyListener {

    @Override
    public String getEndpoint() {
      return EntityListenerEndpoint.DDCCM.name();
    }
  }
}
