package com.ruckus.cloud.wifi.repository;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import static org.assertj.core.api.Assertions.assertThat;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('fdec8ab5a6af4f8597ad15a10d84ba69');
    INSERT INTO network_hotspot20settings (id, tenant) VALUES
        ('99f7eefcb22f44c593bfe3dc91def1e1', 'fdec8ab5a6af4f8597ad15a10d84ba69');
    INSERT INTO hotspot20connection_capability (id, tenant, network_hotspot20settings) VALUES
        ('9e1454480e8f4b1bb333d235c8aef180', 'fdec8ab5a6af4f8597ad15a10d84ba69', '99f7eefcb22f44c593bfe3dc91def1e1');
    """)
public class Hotspot20ConnectionCapabilityRepositoryTest {

  @Autowired
  private Hotspot20ConnectionCapabilityRepository repository;

  @Test
  void existsByTenantIdAndNetworkHotspot20settingsId() {
    assertThat(repository.existsByTenantIdAndNetworkHotspot20SettingsId("fdec8ab5a6af4f8597ad15a10d84ba69", "99f7eefcb22f44c593bfe3dc91def1e1")).isTrue();
    assertThat(repository.existsByTenantIdAndNetworkHotspot20SettingsId("fdec8ab5a6af4f8597ad15a10d84ba69", "99f7eefcb22f44c593bfe3dc91def1e2")).isFalse();
    assertThat(repository.existsByTenantIdAndNetworkHotspot20SettingsId("fdec8ab5a6af4f8597ad15a10d84ba70", "99f7eefcb22f44c593bfe3dc91def1e1")).isFalse();
  }

  @Test
  void deleteByTenantIdAndNetworkHotspot20settingsId() {
    assertThat(repository.existsByTenantIdAndNetworkHotspot20SettingsId("fdec8ab5a6af4f8597ad15a10d84ba69", "99f7eefcb22f44c593bfe3dc91def1e1")).isTrue();
    repository.deleteByTenantIdAndNetworkHotspot20SettingsId("fdec8ab5a6af4f8597ad15a10d84ba69", "99f7eefcb22f44c593bfe3dc91def1e1");
    assertThat(repository.existsByTenantIdAndNetworkHotspot20SettingsId("fdec8ab5a6af4f8597ad15a10d84ba69", "99f7eefcb22f44c593bfe3dc91def1e1")).isFalse();
  }

}
