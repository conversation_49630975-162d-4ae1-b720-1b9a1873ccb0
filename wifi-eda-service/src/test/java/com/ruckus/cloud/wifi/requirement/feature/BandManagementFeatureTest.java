package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_SWITCHABLE_RF_TOGGLE;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioCustomization;
import com.ruckus.cloud.wifi.repository.VenueApModelSpecificAttributesRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class BandManagementFeatureTest {

  @SpyBean
  private BandManagementFeature unit;

  @MockBean
  private VenueApModelSpecificAttributesRepository modelSpecificAttributesRepository;

  @Test
  @FeatureFlag(disable = WIFI_SWITCHABLE_RF_TOGGLE)
  void givenSwitchRFFFDisable(Venue venue) {
    BDDAssertions.then(unit.test(venue)).isFalse();
  }

  @Test
  @FeatureFlag(enable = WIFI_SWITCHABLE_RF_TOGGLE)
  void givenSwitchableRfIncompatibleForVenue(Venue venue) {
    when(modelSpecificAttributesRepository.existsByTenantIdAndVenueIdAndBandModeNotNull(anyString(),
        anyString())).thenReturn(true);

    BDDAssertions.then(unit.test(venue)).isTrue();
  }

  @Test
  @FeatureFlag(enable = WIFI_SWITCHABLE_RF_TOGGLE)
  void givenSwitchableRfCompatibleForVenue(Venue venue) {
    when(modelSpecificAttributesRepository.existsByTenantIdAndVenueIdAndBandModeNotNull(anyString(),
        anyString())).thenReturn(false);

    BDDAssertions.then(unit.test(venue)).isFalse();
  }

  @Test
  @FeatureFlag(enable = WIFI_SWITCHABLE_RF_TOGGLE)
  void givenVenueTriBandEnabledForVenue(Venue venue) {
    VenueRadioCustomization radioCustomization = new VenueRadioCustomization();
    radioCustomization.setTripleBandEnabled(true);
    venue.setRadioCustomization(radioCustomization);
    when(modelSpecificAttributesRepository.existsByTenantIdAndVenueIdAndBandModeNotNull(anyString(),
        anyString())).thenReturn(false);

    BDDAssertions.then(unit.test(venue)).isTrue();
  }
}
