package com.ruckus.cloud.wifi.integration.network;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeShort;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkTestFixture.wisprPage;
import static com.ruckus.cloud.wifi.test.kafka.ConsumerRecordAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.api.rest.RadiusRestCtrl.RadiusMapper;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CalledStationIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdDelimiterEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.viewmodel.Radius;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.Operation;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.AccountingRadiusServiceTestFixture;
import com.ruckus.cloud.wifi.test.fixture.AuthRadiusServiceTestFixture;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenuePortalTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang.BooleanUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
public class ConsumeGuestNetworkRequestTest {

  private static final String EXTERNAL_PORTAL_URL = "https://guestPortal.com";
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;

  @Autowired
  FeatureFlagService featureFlagService;

  private com.ruckus.cloud.wifi.eda.viewmodel.Radius map(
      com.ruckus.cloud.wifi.eda.servicemodel.Radius serviceInput) {
    return RadiusMapper.INSTANCE.ServiceRadius2Radius(serviceInput);
  }

  void assertCommonGuestNetworkResult(GuestNetwork expected, Network actual) {
    assertThat(actual)
        .isNotNull()
        .matches(n -> n.getId().equals(expected.getId()), "id")
        .matches(n -> n.getName().equals(expected.getName()), "name")
        .matches(n -> n.getType().equals(NetworkTypeEnum.GUEST), "networkType");
  }

  void assertRadius(Radius expected, com.ruckus.cloud.wifi.eda.servicemodel.Radius actual) {
    assertEquals(expected.getType(), actual.getType());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(
        expected.getPrimary().getIp(), actual.getPrimary().getIp());
    assertEquals(
        expected.getSecondary().getIp(), actual.getSecondary().getIp());
  }

  private void assertCloudpathNetworkAndR1(GuestNetwork networkRequest,
      com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork networkResult) {
    assertCommonGuestNetworkResult(networkRequest, networkResult);
    assertRadius(networkRequest.getAuthRadius(), networkResult.getAuthRadius());
    assertRadius(networkRequest.getAccountingRadius(), networkResult.getAccountingRadius());
    assertThat(networkResult)
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
        .isNotNull()
        .matches(
            p -> p.getWalledGardens().equals(networkRequest.getGuestPortal().getWalledGardens()))
        .matches(p -> p.getGuestNetworkType().equals(GuestNetworkTypeEnum.Cloudpath),
            "GuestPortal.guestNetworkType")
        .matches(p -> p.getExternalPortalUrl()
                .equals(networkRequest.getGuestPortal().getExternalPortalUrl()),
            "GuestPortal.externalPortalUrl");
  }

  private void assertWisprNetworkOtherProvider(GuestNetwork networkRequest,
      com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork networkResult) {
    assertCommonGuestNetworkResult(networkRequest, networkResult);

    assertThat(networkResult)
        .matches(n -> Objects.isNull(networkResult.getAuthRadius()))
        .matches(n -> Objects.isNull(networkResult.getAccountingRadius()))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
        .isNotNull()
        .matches(g -> g.getGuestNetworkType().equals(GuestNetworkTypeEnum.WISPr),
            "GuestPortal.guestNetworkType")
        .matches(g -> g.getExternalPortalUrl().equals(EXTERNAL_PORTAL_URL),
            "GuestPortal.externalPortalUrl")
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal::getWisprPage)
        .isNotNull()
        .matches(w -> Objects.isNull(w.getExternalProviderName()))
        .matches(w -> Objects.isNull(w.getAaaName()))
        .matches(w -> w.getCustomExternalProvider()
            .equals(networkRequest.getGuestPortal().getWisprPage().getCustomExternalProvider()))
        .matches(w -> w.getAuthType().equals(AuthTypeEnum.RADIUS))
        .matches(w -> !w.getAuthRadius().getAuthRadiusServices().isEmpty())
        .matches(w -> !w.getAccountingRadius().getAccountingRadiusServices().isEmpty());

    assertRadius(networkRequest.getGuestPortal().getWisprPage().getAuthRadius(),
        networkResult.getGuestPortal().getWisprPage().getAuthRadius());
    assertRadius(networkRequest.getGuestPortal().getWisprPage().getAccountingRadius(),
        networkResult.getGuestPortal().getWisprPage().getAccountingRadius());

    assertRadius(networkRequest.getGuestPortal().getWisprPage().getAuthRadius(),
        networkResult.getGuestPortal().getWisprPage().getAuthRadius()
            .getAuthRadiusServices().get(0).getRadius());

    assertRadius(networkRequest.getGuestPortal().getWisprPage().getAccountingRadius(),
        networkResult.getGuestPortal().getWisprPage().getAccountingRadius()
            .getAccountingRadiusServices().get(0).getRadius());

    assertThat(networkResult.getGuestPortal().getWisprPage().getAuthRadius()
        .getAuthRadiusServices())
        .filteredOn(authRadiusService -> authRadiusService.getGuestPortal() != null
            && authRadiusService.getGuestPortal().getId()
            .equals(networkResult.getGuestPortal().getId()))
        .hasSize(1);
  }

  private void assertWisprNetworkPredefinedAAA(GuestNetwork networkRequest,
      com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork networkResult) {
    assertCommonGuestNetworkResult(networkRequest, networkResult);

    assertThat(networkResult)
        .matches(n -> Objects.isNull(networkResult.getAuthRadius()))
        .matches(n -> Objects.isNull(networkResult.getAccountingRadius()))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
        .isNotNull()
        .matches(g -> g.getGuestNetworkType().equals(GuestNetworkTypeEnum.WISPr),
            "GuestPortal.guestNetworkType")
        .matches(g -> g.getExternalPortalUrl().equals(EXTERNAL_PORTAL_URL),
            "GuestPortal.externalPortalUrl")
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal::getWisprPage)
        .isNotNull()
        .matches(w -> Objects.nonNull(w.getExternalProviderName()))
        .matches(w -> Objects.nonNull(w.getAaaName()))
        .matches(w -> w.getCustomExternalProvider()
            .equals(networkRequest.getGuestPortal().getWisprPage().getCustomExternalProvider()))
        .matches(w -> w.getAuthType().equals(AuthTypeEnum.RADIUS))
        .matches(w -> Objects.isNull(w.getAuthRadius()))
        .matches(w -> Objects.isNull(w.getAccountingRadius()));
  }

  private void assertUpdateWisprNetworkOtherProviderRadiusId(
      String radiusId,
      GuestNetwork networkRequest,
      com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork networkResult) {
    assertCommonGuestNetworkResult(networkRequest, networkResult);

    assertThat(networkResult)
        .matches(n -> Objects.isNull(networkResult.getAuthRadius()))
        .matches(n -> Objects.isNull(networkResult.getAccountingRadius()))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
        .isNotNull()
        .matches(g -> g.getGuestNetworkType().equals(GuestNetworkTypeEnum.WISPr),
            "GuestPortal.guestNetworkType")
        .matches(g -> g.getExternalPortalUrl().equals(EXTERNAL_PORTAL_URL),
            "GuestPortal.externalPortalUrl")
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal::getWisprPage)
        .isNotNull()
        .matches(w -> Objects.isNull(w.getExternalProviderName()))
        .matches(w -> Objects.isNull(w.getAaaName()))
        .matches(w -> w.getCustomExternalProvider()
            .equals(networkRequest.getGuestPortal().getWisprPage().getCustomExternalProvider()))
        .matches(w -> w.getAuthType().equals(AuthTypeEnum.RADIUS))
        .matches(w -> !w.getAuthRadius().getAuthRadiusServices().isEmpty())
        .matches(w -> w.getAuthRadius().getId().equals(radiusId));
  }

  private void assertWisprNetworkOtherProviderAlwaysAccept(GuestNetwork networkRequest,
      com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork networkResult) {
    assertCommonGuestNetworkResult(networkRequest, networkResult);

    assertThat(networkResult)
        .matches(n -> Objects.isNull(networkResult.getAuthRadius()))
        .matches(n -> Objects.isNull(networkResult.getAccountingRadius()))
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
        .isNotNull()
        .matches(g -> g.getGuestNetworkType().equals(GuestNetworkTypeEnum.WISPr),
            "GuestPortal.guestNetworkType")
        .matches(g -> g.getExternalPortalUrl().equals(EXTERNAL_PORTAL_URL),
            "GuestPortal.externalPortalUrl")
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestPortal::getWisprPage)
        .isNotNull()
        .matches(w -> Objects.isNull(w.getExternalProviderName()))
        .matches(w -> Objects.isNull(w.getAaaName()))
        .matches(w -> w.getCustomExternalProvider()
            .equals(networkRequest.getGuestPortal().getWisprPage().getCustomExternalProvider()))
        .matches(w -> w.getAuthType().equals(AuthTypeEnum.ALWAYS_ACCEPT))
        .matches(w -> Objects.isNull(w.getAuthRadius()))
        .matches(w -> !w.getAccountingRadius().getAccountingRadiusServices().isEmpty());

    assertRadius(networkRequest.getGuestPortal().getWisprPage().getAccountingRadius(),
        networkResult.getGuestPortal().getWisprPage().getAccountingRadius());

    assertRadius(networkRequest.getGuestPortal().getWisprPage().getAccountingRadius(),
        networkResult.getGuestPortal().getWisprPage().getAccountingRadius()
            .getAccountingRadiusServices().get(0).getRadius());

    assertThat(networkResult.getGuestPortal().getWisprPage().getAuthRadius()).isNull();
  }

  private void assertWisprNetworkCalledStationId(GuestNetwork networkRequest,
      com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork networkResult) {
    assertCommonGuestNetworkResult(networkRequest, networkResult);

    // confirm default value
    var src = networkRequest.getWlan().getAdvancedCustomization().getRadiusOptions();
    assertNotNull(src);
    assertNull(src.getCalledStationIdType());

    // confirm guestNetworkType is WISPr
    assertThat(networkResult)
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getGuestPortal)
        .isNotNull()
        .matches(g -> g.getGuestNetworkType().equals(GuestNetworkTypeEnum.WISPr),
            "GuestPortal.guestNetworkType");

    // confirm calledStationIdType is AP_MAC
    assertThat(networkResult)
        .extracting(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork::getWlan)
        .isNotNull()
        .extracting(Wlan::getAdvancedCustomization)
        .isNotNull()
        .extracting(WlanAdvancedCustomization::getRadiusOptions)
        .isNotNull()
        .matches(r -> !r.getSingleSessionIdAccounting(),
            "RadiusOptions.SingleSessionIdAccounting")
        .matches(r -> Objects.equals(r.getCalledStationIdType(), CalledStationIdTypeEnum.AP_MAC),
            "RadiusOptions.calledStationIdType")
        .matches(r -> Objects.equals(r.getNasIdType(), NasIdTypeEnum.BSSID),
            "RadiusOptions.nasIdType")
        .matches(r -> Objects.equals(r.getNasMaxRetry(), 2),
            "RadiusOptions.nasMaxRetry")
        .matches(r -> Objects.equals(r.getNasReconnectPrimaryMin(), 5),
            "RadiusOptions.nasReconnectPrimaryMin")
        .matches(r -> Objects.equals(r.getNasRequestTimeoutSec(), 3),
            "RadiusOptions.nasRequestTimeoutSec")
        .matches(r -> Objects.equals(r.getNasIdDelimiter(), NasIdDelimiterEnum.DASH),
            "RadiusOptions.nasIdDelimiter")
        .matches(r -> Objects.isNull(r.getUserDefinedNasId()),
            "RadiusOptions.userDefinedNasId");
  }

  private void validateWifiCfgChangeMessage(TxCtx txCtx, int radiusScopeUpdateCount,
      int authRadiusServiceCount, int acctRadiusServiceCount) {
    final var wifiCfgChangeMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(txCtx);
    assertThat(wifiCfgChangeMessageRecord)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .isNotNull()
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(txCtx.getTxId());
    WifiConfigChange wifiConfigChange = wifiCfgChangeMessageRecord.getPayload();

    assertThat(wifiConfigChange)
        .extracting(
            p -> p.getOperationList()).asList()
        .isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasRadius)
        .as(String.format("The Radius operation count should be [%s]", radiusScopeUpdateCount))
        .hasSize(radiusScopeUpdateCount);

    assertThat(wifiConfigChange)
        .extracting(
            p -> p.getOperationList()).asList()
        .isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAuthRadiusService)
        .as(String.format("The AuthRadiusService operation count should be [%s]",
            authRadiusServiceCount))
        .hasSize(authRadiusServiceCount);

    assertThat(wifiConfigChange)
        .extracting(
            p -> p.getOperationList()).asList()
        .isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasAccountingRadiusService)
        .as(String.format("The AccountingRadiusService operation count should be [%s]",
            acctRadiusServiceCount))
        .hasSize(acctRadiusServiceCount);
  }

  private void validateDdccmCfgRequestMessages(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                               List<String> networkIdList, GuestNetwork payload) {
    final boolean isAaaVlanOverrideAvailable = BooleanUtils.isTrue(payload.getWlan().getBypassCPUsingMacAddressAuthentication())
            && GuestNetworkTypeEnum.WISPr.equals(payload.getGuestPortal().getGuestNetworkType());

    if (apiAction == null
            || apiAction == CfgAction.ADD_NETWORK
            || networkIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtx.getTenant());
      return;
    }

    final var ddccmCfgRequestMessage = messageUtil.receive(kafkaTopicProvider.getDdccmCfgRequest());

    assertThat(ddccmCfgRequestMessage).isNotNull()
            .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, txCtx.getTxId()))
            .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, txCtx.getTenant()))
            .extracting(ConsumerRecord::value).isNotNull();

    assertThatNoException().isThrownBy(() ->
            assertThat(WifiConfigRequest.parseFrom(ddccmCfgRequestMessage.value()))
                    .extracting(WifiConfigRequest::getOperationsList).asList()
                    .isNotEmpty()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
                    .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue)
                    .describedAs("Should contains %s WlanVenue operations", networkIdList.size())
                    .hasSize(networkIdList.size())
//                    .allMatch(op -> StringUtils.isNotEmpty(op.getId()),
//                            "All the ids of the WlanVenue operations should not be empty")
//                    .allMatch(op -> op.getAction() == action(apiAction),
//                            String.format("All the actions of the WlanVenue operations should be [%s]", apiAction))
                    .allSatisfy(op -> {
                      assertThat(op)
                              .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
                              .isNotNull()
                              .extracting(WlanVenue::getAdvancedCustomization)
                              .isNotNull()
                              .matches(wlanAdvancedCustomization ->
                                isAaaVlanOverrideAvailable
                                        ? Objects.equals(wlanAdvancedCustomization.getEnableAaaVlanOverride().getValue(), payload.getWlan().getAdvancedCustomization().getEnableAaaVlanOverride())
                                        : !wlanAdvancedCustomization.hasEnableAaaVlanOverride()
                              );
                    }));
  }

  @Nested
  class whenConsumeAddCloudpathGuestNetworkRequest {

    private Radius authRadius;
    private Radius acctRadius;

    @Payload("CloudpathR1")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadCloudpathR1() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.Cloudpath,
          n -> {
            n.setAuthRadius(authRadius);
            n.setAccountingRadius(acctRadius);
          });
      ;
      return networkRequest;
    }

    @BeforeEach
    void givenRadiusPersistedInDb(final Tenant tenant) {
      var radius1 = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(radius1, tenant.getId(), randomTxId());
      authRadius = map(radius1);
      var radius2 = RadiusTestFixture.acctRadius();
      repositoryUtil.createOrUpdate(radius2, tenant.getId(), randomTxId());
      acctRadius = map(radius2);
      var authRadiusService = AuthRadiusServiceTestFixture.randomAuthRadiusService(tenant, radius1);
      repositoryUtil.createOrUpdate(authRadiusService, tenant.getId(), randomTxId());
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("CloudpathR1"))
    void thenSaveCloudpathNetworkAndR1(TxCtx txCtx,
        @Payload("CloudpathR1") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertCloudpathNetworkAndR1(networkRequest, networkResult);
    }
  }

  @Nested
  class whenConsumeUpdateCloudpathGuestNetworkRequest {

    private Radius authRadius;
    private Radius acctRadius;
    private String networkId;

    @Payload("CloudpathR1")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadCloudpathR1() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.Cloudpath,
          n -> {
            n.setAuthRadius(authRadius);
            n.setAccountingRadius(acctRadius);
            n.setName(randomName());
            n.getWlan().setSsid(randomName(10));
            n.setId(networkId);
          });
      ;
      return networkRequest;
    }

    @BeforeEach
    void givenNetworkAndRadiusPersistedInDb(final Tenant tenant) {
      var radius1 = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(radius1, tenant.getId(), randomTxId());
      authRadius = map(radius1);
      var radius2 = RadiusTestFixture.acctRadius();
      repositoryUtil.createOrUpdate(radius2, tenant.getId(), randomTxId());
      acctRadius = map(radius2);
      com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(
          tenant, g -> {
            g.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.Cloudpath);
            g.setAuthRadius(radius1);
            g.setAccountingRadius(radius2);
          });

      var network = repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      networkId = network.getId();
      var authRadiusService = AuthRadiusServiceTestFixture.randomAuthRadiusService(tenant, radius1);
      repositoryUtil.createOrUpdate(authRadiusService, tenant.getId(), randomTxId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("CloudpathR1"))
    void thenUpdateCloudpathNetworkAndR1(TxCtx txCtx,
        @Payload("CloudpathR1") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertCloudpathNetworkAndR1(networkRequest, networkResult);
    }
  }


  @Nested
  class whenConsumeAddWISPrGuestNetworkRequest {

    private Radius authRadius;
    private Radius acctRadius;

    @Payload("OtherProvider")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadOtherProvider() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> n.getGuestPortal()
              .setWisprPage(
                  GuestNetworkViewModelTestFixture.wisprPageOtherProviderNoExternalProviderInfo(
                      authRadius, acctRadius)));
      return networkRequest;
    }

    @Payload("PredefinedAAA")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadPredefinedAAA() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> n.getGuestPortal()
              .setWisprPage(GuestNetworkViewModelTestFixture.wisprPagePredefinedAAA()));
      return networkRequest;
    }

    @Payload("OtherProviderRadiusId")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadOtherProviderRadiusId() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> n.getGuestPortal().setWisprPage(
              GuestNetworkViewModelTestFixture.wisprPageOtherProviderRadiusId(authRadius.getId())));
      return networkRequest;
    }

    @Payload("OtherProviderAlwaysAccept")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadOtherProviderAlwaysAccept() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> n.getGuestPortal()
              .setWisprPage(GuestNetworkViewModelTestFixture.wisprPageOtherProviderAlwaysAccept(
                  wisprPage -> wisprPage.setAccountingRadius(acctRadius))));
      networkRequest.getWlan().setBypassCPUsingMacAddressAuthentication(Boolean.FALSE);
      return networkRequest;
    }

    @BeforeEach
    void givenRadiusPersistedInDb(final Tenant tenant) {
      var radius1 = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(radius1, tenant.getId(), randomTxId());
      authRadius = map(radius1);
      var radius2 = RadiusTestFixture.acctRadius();
      repositoryUtil.createOrUpdate(radius2, tenant.getId(), randomTxId());
      acctRadius = map(radius2);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("PredefinedAAA"))
    void thenSaveWisprNetworkPredefinedAAA(TxCtx txCtx,
        @Payload("PredefinedAAA") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertWisprNetworkPredefinedAAA(networkRequest, networkResult);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("OtherProviderRadiusId"))
    void thenSaveWisprNetworkOtherProviderRadiusId(TxCtx txCtx,
        @Payload("OtherProviderRadiusId") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());
      final var radiusResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.Radius.class, authRadius.getId());

      assertUpdateWisprNetworkOtherProviderRadiusId(radiusResult.getId(), networkRequest,
          networkResult);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("OtherProvider"))
    void thenSaveWisprNetworkOtherProviderAndR1Pver(TxCtx txCtx,
        @Payload("OtherProvider") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertWisprNetworkOtherProvider(networkRequest, networkResult);
      validateWifiCfgChangeMessage(txCtx, 2, 1, 1);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("OtherProviderAlwaysAccept"))
    void thenSaveWisprNetworkOtherProviderAlwaysAccept(TxCtx txCtx,
        @Payload("OtherProviderAlwaysAccept") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {

      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertWisprNetworkOtherProviderAlwaysAccept(networkRequest, networkResult);
      validateWifiCfgChangeMessage(txCtx, 1, 0, 1);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("OtherProvider"))
    void thenSaveWisprNetworkOtherProviderAndUpdate(TxCtx txCtx,
        @Payload("OtherProvider") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest,
        Venue venue) {

      //Check network with CustomExternalProvider
      final var networkCreateResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());
      String authRadiusServiceId = networkCreateResult.getGuestPortal().getWisprPage()
          .getAuthRadius().getAuthRadiusServices().get(0).getId();
      assertThat(authRadiusServiceId).isNotNull();

      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(networkCreateResult.getId());
      payload.setVenueId(venue.getId());
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      sendCreateNetworkActivation(txCtx.getTenant(), payload);

      //Update network with Other Provider
      final var networkUpdateRequest = networkRequest;

      sendUpdateNetwork(txCtx.getTenant(), networkUpdateRequest);
      final var networkUpdateResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkUpdateRequest.getId());
      assertWisprNetworkOtherProvider(networkUpdateRequest, networkUpdateResult);
    }

    private void sendUpdateNetwork(String tenantId, GuestNetwork networkRequest) {
      WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .apiAction(CfgAction.UPDATE_NETWORK)
          .requestParams(new RequestParams().addPathVariable("networkId", networkRequest.getId()))
          .payload(networkRequest).build();

      messageUtil.sendWifiCfgRequest(wifiCfgRequest);
    }

    private void sendCreateNetworkActivation(String tenantId, NetworkVenue networkVenue) {
      WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .apiAction(CfgAction.ADD_NETWORK_VENUE)
          .payload(networkVenue).build();

      messageUtil.sendWifiCfgRequest(wifiCfgRequest);
    }

    private void sendDeleteNetwork(String tenantId, String networkId) {
      WifiCfgRequest wifiCfgRequestDelete = WifiCfgRequest.builder()
          .tenantId(tenantId)
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .apiAction(CfgAction.DELETE_NETWORK)
          .requestParams(new RequestParams().addPathVariable("networkId", networkId))
          .build();

      messageUtil.sendWifiCfgRequest(wifiCfgRequestDelete);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("OtherProvider"))
    void thenSaveWisprNetworkCalledStationId(TxCtx txCtx,
        @Payload("OtherProvider") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertWisprNetworkCalledStationId(networkRequest, networkResult);
    }
  }

  @Nested
  class whenConsumeUpdateWISPrGuestNetworkRequest {

    private String authRadiusId;
    private String networkId;
    private Radius acctRadius;

    @Payload("PredefinedAAA")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadPredefinedAAA() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> {
            n.getGuestPortal()
                .setWisprPage(GuestNetworkViewModelTestFixture.wisprPagePredefinedAAA());
            n.setName(randomName());
            n.getWlan().setSsid(randomName(10));
            n.setId(networkId);
          });
      return networkRequest;
    }

    @Payload("OtherProviderRadiusId")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadOtherProviderRadiusId() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> {
            n.getGuestPortal().setWisprPage(
                GuestNetworkViewModelTestFixture.wisprPageOtherProviderRadiusId(authRadiusId));
            n.setName(randomName());
            n.getWlan().setSsid(randomName(10));
            n.setId(networkId);
          });
      return networkRequest;
    }

    @Payload("OtherProviderAlwaysAccept")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadOtherProviderAlwaysAccept() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> {
            n.getGuestPortal()
                .setWisprPage(GuestNetworkViewModelTestFixture.wisprPageOtherProviderAlwaysAccept(
                    wisprPage -> wisprPage.setAccountingRadius(acctRadius)));
            n.setName(randomName());
            n.getWlan().setSsid(randomName(10));
            n.setId(networkId);
          });
      networkRequest.getWlan().setBypassCPUsingMacAddressAuthentication(Boolean.FALSE);
      return networkRequest;
    }

    @Payload("wisprPredefinedAAAWithAaaVlanOverride")
    private GuestNetwork payloadWisprPredefinedAAAWithAaaVlanOverride() {
      final var networkRequest = payloadPredefinedAAA();
      networkRequest.getWlan().setBypassCPUsingMacAddressAuthentication(true);
      networkRequest.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);
      return networkRequest;
    }

    @Payload("wisprOtherProviderWithAaaVlanOverride")
    private GuestNetwork payloadWisprOtherProviderWithAaaVlanOverride() {
      final var networkRequest = payloadOtherProviderRadiusId();
      networkRequest.getWlan().setBypassCPUsingMacAddressAuthentication(true);
      networkRequest.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);
      return networkRequest;
    }

    @Payload("wisprOtherProviderAlwaysAcceptWithAaaVlanOverride")
    private GuestNetwork payloadWisprOtherProviderAlwaysAcceptWithAaaVlanOverride() {
      final var networkRequest = payloadOtherProviderAlwaysAccept();
      networkRequest.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(true);
      return networkRequest;
    }

    /**
     * <p>
     * Create WISPr Network & Activation Venue test:
     * <ol>
     *     <li>Create authRadiusService(with GuestPortal)</li>
     *     <li>Create acctRadiusService(with GuestPortal)</li>
     *     <li>Create WISPr Network Other Provider with authRadius & acctRadius</li>
     *     <li>Activation Venue</li>
     * </ol>
     * </p>
     */
    @BeforeEach
    void givenRadiusPersistedInDb(final Tenant tenant, final Venue venue) {
      var radius1 = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(radius1, tenant.getId(), randomTxId());
      var radius2 = RadiusTestFixture.acctRadius();
      repositoryUtil.createOrUpdate(radius2, tenant.getId(), randomTxId());
      var radius3 = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(radius3, tenant.getId(), randomTxId());

      var authRadiusService1 = AuthRadiusServiceTestFixture.randomAuthRadiusService(tenant,
          radius1);
      repositoryUtil.createOrUpdate(authRadiusService1, tenant.getId(), randomTxId());
      var acctRadiusService2 = AccountingRadiusServiceTestFixture.randomAccountingRadiusService(
          tenant, radius2);
      repositoryUtil.createOrUpdate(acctRadiusService2, tenant.getId(), randomTxId());
      var authRadiusService3 = AuthRadiusServiceTestFixture.randomAuthRadiusService(tenant,
          radius3);
      repositoryUtil.createOrUpdate(authRadiusService3, tenant.getId(), randomTxId());

      var guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(
          tenant, g -> {
            g.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
            g.getGuestPortal().setWisprPage(wisprPage());
            g.getGuestPortal().getWisprPage().setCustomExternalProvider(true);
            g.getGuestPortal().getWisprPage().setAuthRadius(radius1);
            g.getGuestPortal().getWisprPage().setAccountingRadius(radius2);
          });
      var network = repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(guestNetwork.getGuestPortal(), tenant.getId(), randomTxId());

      // Activation Venue
      final var networkVenue = networkVenue()
          .setNetwork(always(guestNetwork)).setVenue(always(venue)).generate();
      guestNetwork.setNetworkVenues(List.of(networkVenue));
      networkVenue.setNetwork(guestNetwork);
      final var venuePortal = VenuePortalTestFixture.randomVenuePortal(tenant, vportal -> {
        vportal.setNetworkVenue(networkVenue);
        vportal.setNetworkPortal(guestNetwork.getGuestPortal());
      });
      networkVenue.setVenuePortal(venuePortal);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      acctRadius = map(radius2);
      networkId = network.getId();
      authRadiusId = radius3.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("PredefinedAAA"))
    void thenUpdateWisprNetworkPredefinedAAA(TxCtx txCtx,
        @Payload("PredefinedAAA") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertWisprNetworkPredefinedAAA(networkRequest, networkResult);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("OtherProviderRadiusId"))
    void thenUpdateWisprNetworkOtherProviderRadiusId(TxCtx txCtx,
        @Payload("OtherProviderRadiusId") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertUpdateWisprNetworkOtherProviderRadiusId(authRadiusId, networkRequest, networkResult);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("OtherProviderAlwaysAccept"))
    void thenSaveWisprNetworkOtherProviderAlwaysAccept(TxCtx txCtx,
        @Payload("OtherProviderAlwaysAccept") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {

      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertWisprNetworkOtherProviderAlwaysAccept(networkRequest, networkResult);
      validateWifiCfgChangeMessage(txCtx, 1, 0, 1);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("wisprPredefinedAAAWithAaaVlanOverride"))
    void thenSaveWisprPredefinedAaaShouldHasNoAaaVlanOverrideWithoutFF(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                           @Payload("wisprPredefinedAAAWithAaaVlanOverride") GuestNetwork payload) {
      validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("wisprPredefinedAAAWithAaaVlanOverride"))
    void thenSaveWisprOtherProviderAaaEnableAaaVlanOverride(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                           @Payload("wisprPredefinedAAAWithAaaVlanOverride") GuestNetwork payload) {
      validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("wisprOtherProviderWithAaaVlanOverride"))
    void thenSaveWisprCustomShouldEnableAaaVlanOverride(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                         @Payload("wisprOtherProviderWithAaaVlanOverride") GuestNetwork payload) {
      validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("wisprOtherProviderAlwaysAcceptWithAaaVlanOverride"))
    void thenSaveWisprCustomAlwaysAcceptShouldFailAaaVlanOverride(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                        @Payload("wisprOtherProviderAlwaysAcceptWithAaaVlanOverride") GuestNetwork payload) {
      validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
    }
  }

  @Nested
  class whenConsumeUpdateSelfSignInGuestNetworkRequest {

    private String networkId;

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadDefaultSelfSignIn() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
              GuestNetworkTypeEnum.SelfSignIn,
              n -> {
                n.setName(randomName());
                n.getWlan().setSsid(randomName(10));
              });
      return networkRequest;
    }

    @BeforeEach
    void givenNetworkPersistedInDb(final Tenant tenant, final Venue venue) {

      var guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(
              tenant, g -> {
                g.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.SelfSignIn);
              });
      var network = repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(guestNetwork.getGuestPortal(), tenant.getId(), randomTxId());

      // Activation Venue
      final var networkVenue = networkVenue()
              .setNetwork(always(guestNetwork)).setVenue(always(venue)).generate();
      guestNetwork.setNetworkVenues(List.of(networkVenue));
      networkVenue.setNetwork(guestNetwork);
      final var venuePortal = VenuePortalTestFixture.randomVenuePortal(tenant, vportal -> {
        vportal.setNetworkVenue(networkVenue);
        vportal.setNetworkPortal(guestNetwork.getGuestPortal());
      });
      networkVenue.setVenuePortal(venuePortal);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());

      networkId = network.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload)
    void thenSaveSelfSignInThenNoAaaVlanOverride(TxCtxHolder.TxCtx txCtx, CfgAction apiAction,
                                                                  @Payload GuestNetwork payload) {
      validateDdccmCfgRequestMessages(txCtx, apiAction, List.of(networkId), payload);
    }

  }

  @Nested
  class whenConsumeUpdateWISPrAlwaysAcceptGuestNetworkRequest {

    private String networkId;
    private Radius authRadius;
    private Radius acctRadius;

    @Payload("OtherProvider")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadOtherProvider() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> {
            n.getGuestPortal()
                .setWisprPage(
                    GuestNetworkViewModelTestFixture.wisprPageOtherProviderNoExternalProviderInfo(
                        authRadius, acctRadius));
            n.setName(randomName());
            n.getWlan().setSsid(randomName(10));
            n.setId(networkId);
          });
      return networkRequest;
    }

    @Payload("PredefinedAAA")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadPredefinedAAA() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> {
            n.getGuestPortal()
                .setWisprPage(GuestNetworkViewModelTestFixture.wisprPagePredefinedAAA());
            n.setName(randomName());
            n.getWlan().setSsid(randomName(10));
            n.setId(networkId);
          });
      return networkRequest;
    }

    @Payload("OtherProviderAlwaysAccept")
    private com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork payloadOtherProviderAlwaysAccept() {
      final var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.WISPr,
          n -> {
            n.getGuestPortal()
                .setWisprPage(GuestNetworkViewModelTestFixture.wisprPageOtherProviderAlwaysAccept(
                    wisprPage -> wisprPage.setAccountingRadius(acctRadius)));
            n.setName(randomName());
            n.getWlan().setSsid(randomName(10));
            n.setId(networkId);
          });
      networkRequest.getWlan().setBypassCPUsingMacAddressAuthentication(Boolean.FALSE);
      return networkRequest;
    }

    /**
     * <p>
     * Create WISPr Always Accept Network & Activation Venue test:
     * <ol>
     *     <li>Create authRadiusService(No GuestPortal)</li>
     *     <li>Create acctRadiusService(No GuestPortal)</li>
     *     <li>Create WISPr Network with Accept Network</li>
     *     <li>Activation Venue</li>
     * </ol>
     * </p>
     */
    @BeforeEach
    void givenRadiusPersistedInDb(final Tenant tenant, final Venue venue) {
      var radius1 = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(radius1, tenant.getId(), randomTxId());
      var authRadiusService1 = AuthRadiusServiceTestFixture.randomAuthRadiusService(tenant,
          radius1);
      repositoryUtil.createOrUpdate(authRadiusService1, tenant.getId(), randomTxId());
      var radius2 = RadiusTestFixture.acctRadius();
      repositoryUtil.createOrUpdate(radius2, tenant.getId(), randomTxId());
      var acctRadiusService2 = AccountingRadiusServiceTestFixture.randomAccountingRadiusService(
          tenant, radius2);
      repositoryUtil.createOrUpdate(acctRadiusService2, tenant.getId(), randomTxId());

      var guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(
          tenant, g -> {
            g.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
            g.getGuestPortal().setWisprPage(GuestNetworkTestFixture.wisprPageAlwaysAccept());
          });
      var network = repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(guestNetwork.getGuestPortal(), tenant.getId(), randomTxId());

      // Activation Venue
      final var networkVenue = networkVenue()
          .setNetwork(always(guestNetwork)).setVenue(always(venue)).generate();
      guestNetwork.setNetworkVenues(List.of(networkVenue));
      networkVenue.setNetwork(guestNetwork);
      final var venuePortal = VenuePortalTestFixture.randomVenuePortal(tenant, vportal -> {
        vportal.setNetworkVenue(networkVenue);
        vportal.setNetworkPortal(guestNetwork.getGuestPortal());
      });
      networkVenue.setVenuePortal(venuePortal);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      authRadius = map(radius1);
      acctRadius = map(radius2);
      networkId = network.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("OtherProvider"))
    void thenSaveWisprNetworkOtherProviderAndR1Pver(TxCtx txCtx,
        @Payload("OtherProvider") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertWisprNetworkOtherProvider(networkRequest, networkResult);
      validateWifiCfgChangeMessage(txCtx, 0, 1, 1);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("PredefinedAAA"))
    void thenUpdateWisprNetworkPredefinedAAA(TxCtx txCtx,
        @Payload("PredefinedAAA") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertWisprNetworkPredefinedAAA(networkRequest, networkResult);
      validateWifiCfgChangeMessage(txCtx, 0, 0, 0);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("OtherProviderAlwaysAccept"))
    void thenSaveWisprNetworkOtherProviderAlwaysAccept(TxCtx txCtx,
        @Payload("OtherProviderAlwaysAccept") com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkRequest) {

      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkRequest.getId());

      assertWisprNetworkOtherProviderAlwaysAccept(networkRequest, networkResult);
      validateWifiCfgChangeMessage(txCtx, 0, 0, 1);
    }

    @Test
    @ApiAction(value = CfgAction.DELETE_NETWORK)
    void thenDeleteWisprNetworkOtherProviderAlwaysAccept(TxCtx txCtx) {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkId);
      assertNull(networkResult);
    }
  }

  @Nested
  @ApiAction(value = CfgAction.DELETE_NETWORK)
  class whenConsumeDeleteWISPrGuestNetworkRequest {

    private String networkId;

    @BeforeEach
    void givenWISPrNetworkPersistedInDb(final Tenant tenant) {
      var radius1 = RadiusTestFixture.authRadius();
      repositoryUtil.createOrUpdate(radius1, tenant.getId(), randomTxId());
      var radius2 = RadiusTestFixture.acctRadius();
      repositoryUtil.createOrUpdate(radius2, tenant.getId(), randomTxId());
      com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork guestNetwork = GuestNetworkTestFixture.randomGuestNetwork(
          tenant, g -> {
            g.getGuestPortal().setGuestNetworkType(GuestNetworkTypeEnum.WISPr);
            g.getGuestPortal().setWisprPage(wisprPage());
            g.getGuestPortal().getWisprPage().setAuthRadius(radius1);
            g.getGuestPortal().getWisprPage().setAccountingRadius(radius2);
          });

      repositoryUtil.createOrUpdate(guestNetwork, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(guestNetwork.getGuestPortal(), tenant.getId(), randomTxId());
      networkId = guestNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    void thenDeleteGuestNetwork() {
      final var networkResult = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, networkId);

      assertNull(networkResult);
    }
  }

}
