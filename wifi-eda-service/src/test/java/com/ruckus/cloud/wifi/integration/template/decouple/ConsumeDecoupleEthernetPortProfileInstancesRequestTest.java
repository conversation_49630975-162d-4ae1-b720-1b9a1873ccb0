package com.ruckus.cloud.wifi.integration.template.decouple;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.EthernetPortProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DECOUPLE_ETHERNET_PORT_PROFILE_INSTANCES;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@WifiIntegrationTest
public class ConsumeDecoupleEthernetPortProfileInstancesRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void decouple_ethernetPortProfileInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add EthernetPortProfile as a template
    final var ethernetPortProfileTemplate = repositoryUtil.createOrUpdate(
        EthernetPortProfileTestFixture.randomEthernetPortProfileTemplate(mspTenant), mspTenant.getId());

    // ec tenant add EthernetPortProfile instance from template
    changeTxCtxTenant(ecTenantId);
    var ethernetPortProfileInstance = EthernetPortProfileTestFixture.randomEthernetPortProfileTemplateInstance(
        ecTenant, ethernetPortProfileTemplate.getId());
    ethernetPortProfileInstance.setName("ethernet-port-profile-instance");
    ethernetPortProfileInstance.setIsEnforced(true);
    ethernetPortProfileInstance = repositoryUtil.createOrUpdate(ethernetPortProfileInstance, ecTenantId);

    // Verify initial state - instance should have templateId and isEnforced = true
    EthernetPortProfile instanceBeforeDecouple = repositoryUtil.find(EthernetPortProfile.class,
        ethernetPortProfileInstance.getId(), ecTenantId, false);
    assertAll("Verify ethernet port profile instance before decoupling",
        () -> assertEquals(ethernetPortProfileTemplate.getId(), instanceBeforeDecouple.getTemplateId()),
        () -> assertFalse(instanceBeforeDecouple.getIsTemplate()),
        () -> assertTrue(instanceBeforeDecouple.getIsEnforced()));

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_ETHERNET_PORT_PROFILE_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_ETHERNET_PORT_PROFILE_INSTANCES, ecTenantId);

    // Verify ethernet port profile instance was decoupled
    EthernetPortProfile instanceAfterDecouple = repositoryUtil.find(EthernetPortProfile.class,
        ethernetPortProfileInstance.getId(), ecTenantId, false);
    assertAll("Verify ethernet port profile instance after decoupling",
        () -> assertNull(instanceAfterDecouple.getTemplateId(), "templateId should be null after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsEnforced(), "isEnforced should be false after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsTemplate(), "isTemplate should remain false"));

    // Verify template is not affected
    changeTxCtxTenant(mspTenant.getId());
    EthernetPortProfile templateAfterDecouple = repositoryUtil.find(EthernetPortProfile.class,
        ethernetPortProfileTemplate.getId(), mspTenant.getId(), true);
    assertAll("Verify template is not affected",
        () -> assertTrue(templateAfterDecouple.getIsTemplate(), "Template should remain as template"),
        () -> assertEquals(ethernetPortProfileTemplate.getName(), templateAfterDecouple.getName(),
            "Template name should be unchanged"));

    assertDdccmCfgRequestNotSent(ecTenantId);
    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instanceAfterDecouple.getId());
  }

  @Test
  public void decouple_multipleEthernetPortProfileInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add multiple EthernetPortProfile templates
    final var template1 = repositoryUtil.createOrUpdate(
        EthernetPortProfileTestFixture.randomEthernetPortProfileTemplate(mspTenant), mspTenant.getId());
    final var template2 = repositoryUtil.createOrUpdate(
        EthernetPortProfileTestFixture.randomEthernetPortProfileTemplate(mspTenant), mspTenant.getId());

    // ec tenant add multiple EthernetPortProfile instances from templates
    changeTxCtxTenant(ecTenantId);
    var instance1 = EthernetPortProfileTestFixture.randomEthernetPortProfileTemplateInstance(
        ecTenant, template1.getId());
    instance1.setName("ethernet-port-profile-instance-1");
    instance1 = repositoryUtil.createOrUpdate(instance1, ecTenantId);

    var instance2 = EthernetPortProfileTestFixture.randomEthernetPortProfileTemplateInstance(
        ecTenant, template2.getId());
    instance2.setName("ethernet-port-profile-instance-2");
    instance2 = repositoryUtil.createOrUpdate(instance2, ecTenantId);

    // Execute decouple operation for all ethernet port profiles
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_ETHERNET_PORT_PROFILE_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_ETHERNET_PORT_PROFILE_INSTANCES, ecTenantId);

    // Verify all ethernet port profile instances were decoupled
    EthernetPortProfile instance1AfterDecouple = repositoryUtil.find(EthernetPortProfile.class,
        instance1.getId(), ecTenantId, false);
    EthernetPortProfile instance2AfterDecouple = repositoryUtil.find(EthernetPortProfile.class,
        instance2.getId(), ecTenantId, false);

    assertAll("Verify all ethernet port profile instances were decoupled",
        () -> assertNull(instance1AfterDecouple.getTemplateId(), "Instance 1 templateId should be null"),
        () -> assertFalse(instance1AfterDecouple.getIsEnforced(), "Instance 1 isEnforced should be false"),
        () -> assertNull(instance2AfterDecouple.getTemplateId(), "Instance 2 templateId should be null"),
        () -> assertFalse(instance2AfterDecouple.getIsEnforced(), "Instance 2 isEnforced should be false"));

    assertDdccmCfgRequestNotSent(ecTenantId);

    // Validate CmnCfgCollector message contains both decoupled ethernet port profile instances
    String requestId = TxCtxHolder.txId();
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(ecTenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(p -> p.getTenantId().equals(ecTenantId))
        .matches(p -> p.getRequestId().equals(requestId));
  }

  private void validateCmnCfgCollectorMessage(String tenantId, String requestId, String instanceId) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(p -> p.getTenantId().equals(tenantId))
        .matches(p -> p.getRequestId().equals(requestId));

    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .hasSize(1)
        .filteredOn(o -> o.getId().equals(instanceId)).first()
        .matches(o -> EsConstants.Index.ETHERNET_PORT_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .matches(o -> o.getOpType().equals(OpType.MOD))
        .matches(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(tenantId))
        .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())
        .matches(o -> !o.getDocMap().containsKey(EsConstants.Key.IS_ENFORCED));
//        .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue());
  }
} 