package com.ruckus.cloud.wifi.service.integration;

import com.ruckus.cloud.wifi.client.file.ImportFileClient;
import com.ruckus.cloud.wifi.client.file.ImportFileClientImpl;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.grpc.MockFileClient;
import java.io.File;
import java.io.IOException;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

import static org.junit.jupiter.api.Assertions.assertTrue;

@WifiUnitTest
public class ImportFileClientImplTest {

  @Autowired
  private ImportFileClient importFileClient;

  @Test
  @Disabled
  public void testDownloadCsvFile() throws IOException {
    String downloadUrl =
        "https://storage.googleapis.com/dev-alto-file-storage-1/tenant/f034ae2ee5f446aca3b6ec2a921ee620/f72a19e343004fffb9713773f62d4432-002.csv?GoogleAccessId=<EMAIL>&Expires=1709960053&Signature=J%2BKDs1Bjh1LzjodWam%2FTpgbvVUDnmbKphvrFDG0mqOdawgbM4NN9eVT%2BVZxjO7YEm0uBjMByqDhKbpiMHBdHKBqbzLcoSSqWxiBYY7eSWLKTncPfy92jIjl65hF0CXdgZeLnxluiqksAA%2FsTLnjqN7Bh6uk2ZS43y1kg7SmvbMMZ4pmFXSU0rq2n4UQEi7uN7%2Bx6Q%2Br7GcDgDFTMydf9M%2F6ERwgOF4z8eAqmYpbAhYMjVRGcB8H%2FJprzi5O8hzz0e4bZWrVhxL4%2BzxeKsKp6ER467nKOoaKdXSDvCsZC3aEuz8S9PN1Avi%2B%2FYtH2KXqPCPczDZQKC6Nfx1sShbcx8Q%3D%3D";
    String filePath = "/tmp/import_ap_test.csv";
    importFileClient.downloadFile(filePath, downloadUrl);

    File csvFile = new File(filePath);
    assertTrue(csvFile.delete());
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public ImportFileClient testImportFileClient() {
      return new ImportFileClientImpl(new MockFileClient());
    }
  }
}