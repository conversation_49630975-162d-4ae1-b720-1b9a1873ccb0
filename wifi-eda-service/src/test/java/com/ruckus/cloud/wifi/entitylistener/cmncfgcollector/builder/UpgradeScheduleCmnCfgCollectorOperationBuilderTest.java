package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.UpgradeScheduleCmnCfgCollectorOperationBuilder.KEY_PRE_LOAD_SCHEDULE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_CMN_CFG_OPERATION_BUILDER_BULK_PRE_LOAD_TOGGLE;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.tuple;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.internal.verification.VerificationModeFactory.times;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule.Fields;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.service.core.tx.DummyEntitiesOrderService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesImpl;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ScheduleTimeSlotTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleFirmwareVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleTestFixture;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
@FeatureFlag(enable = {
    AP_FW_MGMT_UPGRADE_BY_MODEL,
    WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE,
    WIFI_EDA_CMN_CFG_OPERATION_BUILDER_BULK_PRE_LOAD_TOGGLE
})
public class UpgradeScheduleCmnCfgCollectorOperationBuilderTest {
  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");
  @MockBean
  private UpgradeScheduleRepository upgradeScheduleRepository;
  @SpyBean
  private UpgradeScheduleCmnCfgCollectorOperationBuilder unit;

  @Test
  void testGetEntityClass() {
    assertThat(unit.entityClass()).isEqualTo(UpgradeSchedule.class);
  }

  @Test
  void testGetIndex() {
    assertThat(unit.index()).isEqualTo(EsConstants.Index.VENUE);
  }

  @Nested
  class testBuildConfig {

    @Test
    @FeatureFlag(disable = WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
    public void givenFFIsDisabled() {
      var schedule = new UpgradeSchedule();
      schedule.setVenue(new Venue(randomId()));
      List<Operations> operations = unit.build(new TxEntity<>(schedule, EntityAction.DELETE), emptyTxChanges());
      assertThat(unit.shouldSkipBuild(new TxEntity<>(schedule, EntityAction.DELETE), emptyTxChanges())).isTrue();
      assertThat(operations).isEmpty();
    }

    @Test
    public void deletePendingSchedule() {
      var venue = new Venue(randomId());
      var version700 = ApVersionTestFixture.recommendedApVersion("*********.500", c -> {});
      var version624 = ApVersionTestFixture.recommendedApVersion("*********.200", c -> {});
      var sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
      var schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts, c -> {});
      var usfvFor624 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version624, c -> {});
      var usfvFor700 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version700, c -> {});
      schedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor624, usfvFor700));
      doReturn(List.of()).when(upgradeScheduleRepository).findByTenantIdAndVenueId(any(), any());

      List<Operations> operations = unit.build(new TxEntity<>(schedule, EntityAction.DELETE), emptyTxChanges());

      assertThat(unit.action(EntityAction.DELETE)).isEqualTo(OpType.MOD);
      assertThat(operations)
          .isNotEmpty().singleElement()
          .extracting(Operations::getDocMap)
          .extracting(docMap -> docMap.get(Key.NEXT_AP_FIRMWARE_SCHEDULES))
          .extracting(value -> value.getListValue().getValuesList())
          .asList()
          .isEmpty();
    }

    @Test
    public void deleteNonPendingSchedule() {
      var venue = new Venue(randomId());
      var version700 = ApVersionTestFixture.recommendedApVersion("*********.500", c -> {});
      var version624 = ApVersionTestFixture.recommendedApVersion("*********.200", c -> {});
      var sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
      var schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts, c -> {
        c.setStatus(UpgradeScheduleStatus.FINISHED);
      });
      var usfvFor624 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version624, c -> {});
      var usfvFor700 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version700, c -> {});
      schedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor624, usfvFor700));
      doReturn(List.of()).when(upgradeScheduleRepository).findByTenantIdAndVenueId(any(), any());

      List<Operations> operations = unit.build(new TxEntity<>(schedule, EntityAction.DELETE), emptyTxChanges());

      assertThat(unit.action(EntityAction.DELETE)).isEqualTo(OpType.MOD);
      assertThat(operations)
          .isNotEmpty().singleElement()
          .extracting(Operations::getDocMap)
          .extracting(docMap -> docMap.get(Key.NEXT_AP_FIRMWARE_SCHEDULES))
          .extracting(value -> value.getListValue().getValuesList())
          .asList()
          .isEmpty();
      verify(upgradeScheduleRepository, times(1)).findFirstByVenueIdAndStatusIsPendingOrNullOrderById(any());
    }

    @Test
    public void deleteNonPendingScheduleAndExistPendingSchedule() {
      Operations.Builder builder = Operations.newBuilder();
      var venue = new Venue(randomId());
      var version700 = ApVersionTestFixture.recommendedApVersion("*********.500", c -> {});
      var version624 = ApVersionTestFixture.recommendedApVersion("*********.200", c -> {});
      var sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
      var runningSchedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts,
          c -> { c.setStatus(UpgradeScheduleStatus.RUNNING);});
      var pendingSchedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts,
          c -> {});
      var usfvFor624 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(runningSchedule, version624, c -> {});
      var usfvFor700 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(pendingSchedule, version700, c -> {});
      runningSchedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor700));
      pendingSchedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor624));
      doReturn(pendingSchedule).when(upgradeScheduleRepository)
          .findFirstByVenueIdAndStatusIsPendingOrNullOrderById(any());

      unit.config(builder, runningSchedule, EntityAction.DELETE);

      assertThat(unit.action(EntityAction.DELETE)).isEqualTo(OpType.MOD);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.NEXT_AP_FIRMWARE_SCHEDULES))
          .extracting(value -> value.getListValue().getValuesList())
          .satisfies(values -> {
            assertThat(values)
                .extracting(v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO))
                .extracting(v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO_VERSION).getStringValue(),
                    v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO_CATEGORY).getStringValue())
                .containsExactlyInAnyOrder(
                    tuple(version624.getId(), version624.getCategory().toString()));
          });
      verify(upgradeScheduleRepository, times(1)).findFirstByVenueIdAndStatusIsPendingOrNullOrderById(any());
    }

    @Test
    public void deleteScheduleAndVenue_skipBuild() {
      var venue = new Venue(randomId());
      var version700 = ApVersionTestFixture.recommendedApVersion("*********.500", c -> {});
      var version624 = ApVersionTestFixture.recommendedApVersion("*********.200", c -> {});
      var sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
      var schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts, c -> {});
      var usfvFor624 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version624, c -> {});
      var usfvFor700 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version700, c -> {});
      schedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor624, usfvFor700));
      doReturn(List.of(schedule)).when(upgradeScheduleRepository).findByTenantIdAndVenueId(any(), any());

      TxChanges txChanges = new TxChangesImpl(null);
      txChanges.add(venue, Set.of(), EntityAction.DELETE);
      List<Operations> operations = unit.build(new TxEntity<>(schedule, EntityAction.DELETE), txChanges);

      assertThat(unit.action(EntityAction.DELETE)).isEqualTo(OpType.MOD);
      assertThat(operations).isEmpty();
    }

    @Test
    public void deleteScheduleAndExistNewScheduleInTx_skipBuild() {
      var venue = new Venue(randomId());
      var version700 = ApVersionTestFixture.recommendedApVersion("*********.500", c -> {});
      var version624 = ApVersionTestFixture.recommendedApVersion("*********.200", c -> {});
      var sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
      var oldSchedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts, c -> {});
      var newSchedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts, c -> {});
      var usfvFor624 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(oldSchedule, version624, c -> {});
      var usfvFor700 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(newSchedule, version700, c -> {});
      oldSchedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor624));
      newSchedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor624));
      doReturn(List.of(newSchedule)).when(upgradeScheduleRepository).findByTenantIdAndVenueId(any(), any());

      TxChanges txChanges = new TxChangesImpl(DummyEntitiesOrderService.INSTANCE);
      txChanges.add(newSchedule, Set.of(), EntityAction.ADD);
      List<Operations> operations = unit.build(new TxEntity<>(oldSchedule, EntityAction.DELETE), txChanges);

      assertThat(unit.action(EntityAction.DELETE)).isEqualTo(OpType.MOD);
      assertThat(operations).isEmpty();
    }

    @Test
    public void updatePendingSchedule() {
      Operations.Builder builder = Operations.newBuilder();
      var venue = new Venue(randomId());
      var version700 = ApVersionTestFixture.recommendedApVersion("*********.500", c -> {});
      var version624 = ApVersionTestFixture.recommendedApVersion("*********.200", c -> {});
      var sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
      var schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts, c -> {});
      var usfvFor624 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version624, c -> {});
      var usfvFor700 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version700, c -> {});
      schedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor624, usfvFor700));
      doReturn(List.of(schedule)).when(upgradeScheduleRepository).findByTenantIdAndVenueId(any(), any());

      unit.config(builder, schedule, EntityAction.MODIFY);

      assertThat(unit.action(EntityAction.MODIFY)).isEqualTo(OpType.MOD);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.NEXT_AP_FIRMWARE_SCHEDULES))
          .extracting(value -> value.getListValue().getValuesList())
          .satisfies(values -> {
            assertThat(values)
                .extracting(v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO))
                .extracting(v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO_VERSION).getStringValue(),
                    v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO_CATEGORY).getStringValue())
                .containsExactlyInAnyOrder(
                    tuple(version624.getId(), version624.getCategory().toString()),
                    tuple(version700.getId(), version700.getCategory().toString()));
          });
    }

    @Test
    public void addPendingSchedule() {
      Operations.Builder builder = Operations.newBuilder();
      var venue = new Venue(randomId());
      var version700 = ApVersionTestFixture.recommendedApVersion("*********.500", c -> {});
      var version624 = ApVersionTestFixture.recommendedApVersion("*********.200", c -> {});
      var sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
      var schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts, c -> {});
      var usfvFor624 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version624, c -> {});
      var usfvFor700 = UpgradeScheduleFirmwareVersionTestFixture
          .randomUpgradeScheduleFirmwareVersion(schedule, version700, c -> {});
      schedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor624, usfvFor700));
      doReturn(List.of(schedule)).when(upgradeScheduleRepository).findByTenantIdAndVenueId(any(), any());

      unit.config(builder, schedule, EntityAction.ADD);

      assertThat(unit.action(EntityAction.ADD)).isEqualTo(OpType.MOD);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.NEXT_AP_FIRMWARE_SCHEDULES))
          .extracting(value -> value.getListValue().getValuesList())
          .satisfies(values -> {
            assertThat(values)
                .extracting(v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO))
                .extracting(v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO_VERSION).getStringValue(),
                    v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO_CATEGORY).getStringValue())
                .containsExactlyInAnyOrder(
                    tuple(version624.getId(), version624.getCategory().toString()),
                    tuple(version700.getId(), version700.getCategory().toString()));
          });
    }

    @Test
    public void addPendingScheduleWithEmptyUpgradeScheduleVersionList() {
      Operations.Builder builder = Operations.newBuilder();
      var venue = new Venue(randomId());
      var version700 = ApVersionTestFixture.recommendedApVersion("*********.500", c -> {});
      var sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
      var schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts, c -> {});
      doReturn(List.of(schedule)).when(upgradeScheduleRepository).findByTenantIdAndVenueId(any(), any());

      unit.config(builder, schedule, EntityAction.ADD);

      assertThat(unit.action(EntityAction.ADD)).isEqualTo(OpType.MOD);
      assertThat(builder.build().getDocMap())
          .extracting(docMap -> docMap.get(Key.NEXT_AP_FIRMWARE_SCHEDULES))
          .extracting(value -> value.getListValue().getValuesList())
          .asList()
          .isEmpty();
    }

    @Nested
    @FeatureFlag(enable = WIFI_EDA_CMN_CFG_OPERATION_BUILDER_BULK_PRE_LOAD_TOGGLE)
    class withPreloadData {
      @Test
      public void deleteNonPendingScheduleAndExistPendingSchedule() {
        Operations.Builder builder = Operations.newBuilder();
        var venue = new Venue(randomId());
        var version700 = ApVersionTestFixture.recommendedApVersion("*********.500", c -> {});
        var version624 = ApVersionTestFixture.recommendedApVersion("*********.200", c -> {});
        var sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
        var runningSchedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts,
            c -> { c.setStatus(UpgradeScheduleStatus.RUNNING);});
        var pendingSchedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, version700, sts,
            c -> {});
        var usfvFor624 = UpgradeScheduleFirmwareVersionTestFixture
            .randomUpgradeScheduleFirmwareVersion(runningSchedule, version624, c -> {});
        var usfvFor700 = UpgradeScheduleFirmwareVersionTestFixture
            .randomUpgradeScheduleFirmwareVersion(pendingSchedule, version700, c -> {});
        runningSchedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor700));
        pendingSchedule.setUpgradeScheduleFirmwareVersions(List.of(usfvFor624));
        Map<Object, Object> preloadData = new HashMap<>();
        preloadData.put(KEY_PRE_LOAD_SCHEDULE, pendingSchedule);

        unit.config(builder, runningSchedule, EntityAction.DELETE, preloadData);

        assertThat(unit.action(EntityAction.DELETE)).isEqualTo(OpType.MOD);
        assertThat(builder.build().getDocMap())
            .extracting(docMap -> docMap.get(Key.NEXT_AP_FIRMWARE_SCHEDULES))
            .extracting(value -> value.getListValue().getValuesList())
            .satisfies(values -> {
              assertThat(values)
                  .extracting(v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO))
                  .extracting(v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO_VERSION).getStringValue(),
                      v -> v.getStructValue().getFieldsMap().get(Key.VERSION_INFO_CATEGORY).getStringValue())
                  .containsExactlyInAnyOrder(
                      tuple(version624.getId(), version624.getCategory().toString()));
            });
        verify(upgradeScheduleRepository, never()).findFirstByVenueIdAndStatusIsPendingOrNullOrderById(any());
      }
    }
  }

  @Nested
  class testPreload {
    @Test
    public void givenEntityActionIsDelete_preloadVenuePendingSchedule() {
      var venue = new Venue(randomId());
      var venuePendingSchedule = new UpgradeSchedule();
      venuePendingSchedule.setVenue(venue);
      venuePendingSchedule.setStatus(UpgradeScheduleStatus.PENDING);
      doReturn(List.of(venuePendingSchedule)).when(upgradeScheduleRepository)
          .findFirstByTenantIdAndVenueIdInAndStatusIsPendingOrNull(eq(txCtxExtension.getTenantId()), any());
      var schedule = new UpgradeSchedule();
      schedule.setVenue(venue);
      schedule.setStatus(UpgradeScheduleStatus.FINISHED);
      var scheduleTxEntity = new TxEntity<>(schedule, EntityAction.DELETE);
      // When
      unit.preload(List.of(scheduleTxEntity), emptyTxChanges());
      // Then
      assertThat(scheduleTxEntity.getPreloadedData().get(KEY_PRE_LOAD_SCHEDULE)).isEqualTo(venuePendingSchedule);
      verify(upgradeScheduleRepository, times(1)).findFirstByTenantIdAndVenueIdInAndStatusIsPendingOrNull(any(), any());
    }

    @Test
    public void givenEntityActionIsNotDelete_skipPreload() {
      var venue = new Venue(randomId());
      var schedule = new UpgradeSchedule();
      schedule.setVenue(venue);
      schedule.setStatus(UpgradeScheduleStatus.RUNNING);
      var scheduleTxEntity = new TxEntity<>(schedule, EntityAction.MODIFY);
      // When
      unit.preload(List.of(scheduleTxEntity), emptyTxChanges());
      // Then
      assertThat(scheduleTxEntity.getPreloadedData().get(KEY_PRE_LOAD_SCHEDULE)).isNull();
      verify(upgradeScheduleRepository, never()).findFirstByTenantIdAndVenueIdInAndStatusIsPendingOrNull(any(), any());
    }

    @Test
    @FeatureFlag(disable = WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
    public void givenFirmwareManagementRbacFfIsOff_skipPreload() {
      var venue = new Venue(randomId());
      var schedule = new UpgradeSchedule();
      schedule.setVenue(venue);
      schedule.setStatus(UpgradeScheduleStatus.FINISHED);
      var scheduleTxEntity = new TxEntity<>(schedule, EntityAction.DELETE);
      // When
      unit.preload(List.of(scheduleTxEntity), emptyTxChanges());
      // Then
      assertThat(scheduleTxEntity.getPreloadedData().get(KEY_PRE_LOAD_SCHEDULE)).isNull();
      verify(upgradeScheduleRepository, never()).findFirstByTenantIdAndVenueIdInAndStatusIsPendingOrNull(any(), any());
    }
  }

  @Nested
  class testClearPreload {
    @Test
    public void givenEntityPreloadedDataIsNotEmpty() {
      var venue = new Venue(randomId());
      var venuePendingSchedule = new UpgradeSchedule();
      venuePendingSchedule.setVenue(venue);
      venuePendingSchedule.setStatus(UpgradeScheduleStatus.PENDING);

      var schedule = new UpgradeSchedule();
      schedule.setVenue(venue);
      schedule.setStatus(UpgradeScheduleStatus.FINISHED);
      var scheduleTxEntityA = new TxEntity<>(schedule, EntityAction.DELETE);
      scheduleTxEntityA.getPreloadedData().put(KEY_PRE_LOAD_SCHEDULE, venuePendingSchedule);
      scheduleTxEntityA.getPreloadedData().put("OTHER_KEY", "test");
      var scheduleTxEntityB = new TxEntity<>(schedule, EntityAction.DELETE);
      scheduleTxEntityB.getPreloadedData().put(KEY_PRE_LOAD_SCHEDULE, venuePendingSchedule);
      // When
      unit.clearPreload(List.of(scheduleTxEntityA, scheduleTxEntityB), emptyTxChanges());
      // Then
      assertThat(scheduleTxEntityA.getPreloadedData().get(KEY_PRE_LOAD_SCHEDULE)).isNull();
      assertThat(scheduleTxEntityA.getPreloadedData().get("OTHER_KEY")).isNotNull();
      assertThat(scheduleTxEntityB.getPreloadedData().get(KEY_PRE_LOAD_SCHEDULE)).isNull();
    }

    @Test
    public void givenEntityPreloadedDataIsEmpty() {
      var venue = new Venue(randomId());
      var venuePendingSchedule = new UpgradeSchedule();
      venuePendingSchedule.setVenue(venue);
      venuePendingSchedule.setStatus(UpgradeScheduleStatus.PENDING);

      var schedule = new UpgradeSchedule();
      schedule.setVenue(venue);
      schedule.setStatus(UpgradeScheduleStatus.FINISHED);
      var scheduleTxEntityWithoutPreloadA = new TxEntity<>(schedule, EntityAction.DELETE);
      var scheduleTxEntityWithoutPreloadB = new TxEntity<>(schedule, EntityAction.DELETE);
      // When
      unit.clearPreload(List.of(scheduleTxEntityWithoutPreloadA, scheduleTxEntityWithoutPreloadB), emptyTxChanges());
      // Then
      assertThat(scheduleTxEntityWithoutPreloadA.getPreloadedData()).isEmpty();
      assertThat(scheduleTxEntityWithoutPreloadB.getPreloadedData()).isEmpty();
    }

    @Test
    @FeatureFlag(disable = WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
    public void givenFirmwareManagementRbacFfIsOff_skipClear() {
      var venue = new Venue(randomId());
      var venuePendingSchedule = new UpgradeSchedule();
      venuePendingSchedule.setVenue(venue);
      venuePendingSchedule.setStatus(UpgradeScheduleStatus.PENDING);

      var schedule = new UpgradeSchedule();
      schedule.setVenue(venue);
      schedule.setStatus(UpgradeScheduleStatus.FINISHED);
      var scheduleTxEntityA = new TxEntity<>(schedule, EntityAction.DELETE);
      scheduleTxEntityA.getPreloadedData().put(KEY_PRE_LOAD_SCHEDULE, venuePendingSchedule);
      scheduleTxEntityA.getPreloadedData().put("OTHER_KEY", "test");
      // When
      unit.clearPreload(List.of(scheduleTxEntityA), emptyTxChanges());
      // Then
      assertThat(scheduleTxEntityA.getPreloadedData().get(KEY_PRE_LOAD_SCHEDULE)).isNotNull();
      assertThat(scheduleTxEntityA.getPreloadedData().get("OTHER_KEY")).isNotNull();
    }
  }

  @Nested
  class testHasModification {

    @Test
    public void givenEntityActionIsNotModify() {
      assertThat(unit.hasModification(EntityAction.ADD, Collections.emptySet(), false)).isTrue();
    }

    @Test
    public void givenAnyFields() {
      assertThat(unit.hasModification(EntityAction.MODIFY, Set.of(Fields.VERSION), false)).isTrue();
      assertThat(unit.hasModification(EntityAction.MODIFY, Set.of(Fields.STATUS), false)).isTrue();
      assertThat(unit.hasModification(EntityAction.MODIFY, Set.of(AbstractBaseEntity.Fields.UPDATEDDATE),
          false)).isFalse();
    }
  }
}
