package com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder;

import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.repository.SyslogServerProfileRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import static org.assertj.core.api.Assertions.assertThat;

@WifiUnitTest
public class SyslogServerProfileIndexCmnCfgCollectorOperationBuilderTest {

  @MockBean private VenueRepository venueRepository;

  @MockBean private SyslogServerProfileRepository syslogServerProfileRepository;

  @SpyBean
  private SyslogServerProfileCmnCfgCollectorOperationBuilder
      syslogServerProfileCmnCfgCollectorOperationBuilder;

  @Test
  public void testGetEntityClass() {
    assertThat(syslogServerProfileCmnCfgCollectorOperationBuilder.entityClass())
        .isEqualTo(SyslogServerProfile.class);
  }

  @Nested
  class testBuildConfig {}
}
