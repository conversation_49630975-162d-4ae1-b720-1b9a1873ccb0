package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.ruckus.acx.ddccm.protobuf.wifi.AcctRealmProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class DdccmAccoutingRadiusProfileOperationBuilderTest {

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");
  @SpyBean
  private DdccmAccountingRadiusProfileOperationBuilder builder;

  @Test
  void testAddAcctRealmProfileBuilder() {

    Radius accountingRadius = Generators.radiusProfile().generate();
    accountingRadius.setName(accountingRadius.getName() + randomString(5).generate());
    AccountingRadiusService accountingRadiusService = Generators.accountingRadiusService(accountingRadius).generate();
    AccountingRadiusProfile accountingRadiusProfile = Generators.accountingRadiusProfile(accountingRadiusService)
        .generate();
    accountingRadiusProfile.setTenant(Generators.tenant().generate());
    accountingRadiusProfile.setHotspot20SupportEnabled(true);

    List<Operation> operations = builder.build(new NewTxEntity<>(accountingRadiusProfile),
        emptyTxChanges());

    assertEquals(1, operations.size());
    AcctRealmProfile accountingRealmProfile = operations.get(0).getAcctRealmProfile();

    assertNotNull(accountingRealmProfile);
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());
    assertEquals(accountingRadiusProfile.getHotspot20SupportEnabled(),
        accountingRealmProfile.getH20SuppportEnabled());
  }
}