package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenantId1');
      INSERT INTO ap_version (id) VALUES ('7.0.0.103.124'), ('6.2.2.103.124'), ('6.2.0.103.124');
      INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time) 
        VALUES ('timeSlotId', 'WAITING_REMINDER', '2022-02-19 05:00:00.000', '2022-02-19 07:00:00.000');
      INSERT INTO venue (tenant, id, ap_version) VALUES ('tenantId1', 'venueId1', '7.0.0.103.124');
      INSERT INTO upgrade_schedule(tenant, id, status, time_slot, version, venue) 
        VALUES ('tenantId1', 'scheduleId1', 'PENDING', 'timeSlotId', '7.0.0.103.124', 'venueId1'),
               ('tenantId1', 'scheduleId2', 'RUNNING', 'timeSlotId', '7.0.0.103.124', 'venueId1');
      INSERT INTO upgrade_schedule_firmware_version(id, upgrade_schedule, ap_firmware_version, tenant, created_date, updated_date)
        VALUES ('usfvId1', 'scheduleId1', '6.2.2.103.124', 'tenantId1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
               ('usfvId2', 'scheduleId1', '6.2.0.103.124', 'tenantId1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
               ('usfvId3', 'scheduleId2', '7.0.0.103.124', 'tenantId1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
      """)
@WifiJpaDataTest
class UpgradeScheduleFirmwareVersionRepositoryTest {

  @Autowired
  private UpgradeScheduleFirmwareVersionRepository repository;

  @Test
  void findPendingScheduleVersionsByVenueIdTest() {
    List<UpgradeScheduleFirmwareVersion> upgradeScheduleFirmwareVersionList =
        repository.findScheduleVersionsByVenueIdAndStatus("venueId1",
            UpgradeScheduleStatus.PENDING);

    assertThat(upgradeScheduleFirmwareVersionList).isNotNull().hasSize(2)
        .anyMatch(v -> v.getApFirmwareVersion().getId().equals("6.2.2.103.124"))
        .anyMatch(v -> v.getApFirmwareVersion().getId().equals("6.2.0.103.124"));

    upgradeScheduleFirmwareVersionList =
        repository.findScheduleVersionsByVenueIdAndStatus("venueId1",
            UpgradeScheduleStatus.RUNNING);

    assertThat(upgradeScheduleFirmwareVersionList).isNotNull().hasSize(1)
        .singleElement().matches(v -> v.getApFirmwareVersion().getId().equals("7.0.0.103.124"));
  }

  @Test
  void findByTenantIdAndUpgradeScheduleIdTest() {
    assertThat(repository.findByTenantIdAndUpgradeScheduleId("tenantId1", "scheduleId1")).isNotEmpty()
        .hasSize(2)
        .anyMatch(v -> v.getApFirmwareVersion().getId().equals("6.2.2.103.124"))
        .anyMatch(v -> v.getApFirmwareVersion().getId().equals("6.2.0.103.124"));
    assertThat(repository.findByTenantIdAndUpgradeScheduleId("tenantId1", "scheduleId2")).isNotEmpty()
        .hasSize(1)
        .singleElement().matches(v -> v.getApFirmwareVersion().getId().equals("7.0.0.103.124"));
    assertThat(repository.findByTenantIdAndUpgradeScheduleId("tenantId1", "scheduleId3")).isEmpty();
    assertThat(repository.findByTenantIdAndUpgradeScheduleId("tenantId2", "scheduleId2")).isEmpty();
  }
}
