package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_DPSK3_NON_PROXY_MODE_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiNetworkRadiusServerProfileSettings;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MacAuthMacFormatEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.DPSKNetworkTestFixture;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Tag("RadiusServerProfileTest")
@WifiIntegrationTest
public class ConsumeWifiNetworkRadiusServerProfileSettingsRequestTest extends AbstractRequestTest{

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private ExtendedMessageUtil messageUtil;

  public static final String WIFI_NETWORK_ID = "wifiNetworkId";

  @Test
  void testUpdateWifiNetworkRadiusServerProfileSettings(
      final Tenant tenant, final Venue venue) {

    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();

    final var openNetwork = network(OpenNetwork.class).generate();
    openNetwork.getWlan().setNetwork(openNetwork);
    openNetwork.getWlan().setMacAddressAuthentication(true);
    openNetwork.getWlan().setMacAuthMacFormat(MacAuthMacFormatEnum.Lower);
    openNetwork.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(false);
    repositoryUtil.createOrUpdate(openNetwork, tenant.getId(), randomTxId());
    networkId = openNetwork.getId();

    WifiNetworkRadiusServerProfileSettings settings = new WifiNetworkRadiusServerProfileSettings();
    settings.setMacAuthMacFormat(MacAuthMacFormatEnum.UpperColon);
    settings.setEnableAuthProxy(true);

    RequestParams requestParams =
       new RequestParams()
          .addPathVariable(WIFI_NETWORK_ID, networkId);

    messageUtil.sendWifiCfgRequest(
        tenantId, requestId,
        CfgAction.UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS,
        userName,
        requestParams,
        settings);
    assertActivityStatusSuccess(UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS, tenantId);

    final OpenNetwork network =
        repositoryUtil.find(OpenNetwork.class, networkId);
    assertThat(network)
        .isNotNull()
        .matches(n -> Objects.equals(n.getId(), networkId))
        .matches(n -> Objects.equals(n.getEnableAuthProxy(), true))
        .matches(n -> Objects.equals(n.getEnableAccountingProxy(), false))
        .matches(n -> Objects.equals(n.getWlan().getMacAuthMacFormat(),
            MacAuthMacFormatEnum.UpperColon));

  }

  @Test
  void testUpdateWifiNetworkRadiusServerProfileSettingsOnAAANetwork(final Tenant tenant) {
    String networkId;
    String tenantId = tenant.getId();
    String userName = randomName();
    String requestId = randomTxId();

    final var aaaNetwork = network(AAANetwork.class).generate();
    aaaNetwork.getWlan().setNetwork(aaaNetwork);
    aaaNetwork.getWlan().setMacAddressAuthentication(true);
    aaaNetwork.getWlan().setMacAuthMacFormat(MacAuthMacFormatEnum.Lower);
    aaaNetwork.getWlan().getAdvancedCustomization().setEnableAaaVlanOverride(false);
    repositoryUtil.createOrUpdate(aaaNetwork, tenant.getId(), randomTxId());
    networkId = aaaNetwork.getId();

    WifiNetworkRadiusServerProfileSettings settings = new WifiNetworkRadiusServerProfileSettings();
    settings.setMacAuthMacFormat(MacAuthMacFormatEnum.UpperColon);

    RequestParams requestParams = new RequestParams().addPathVariable(WIFI_NETWORK_ID, networkId);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgAction.UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS,
        userName,
        requestParams,
        settings);
    assertActivityStatusSuccess(UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS, tenantId);

    final AAANetwork network = repositoryUtil.find(AAANetwork.class, networkId);
    assertThat(network)
        .isNotNull()
        .matches(n -> Objects.equals(n.getId(), networkId))
        .matches(n -> Objects.equals(n.getEnableAuthProxy(), false))
        .matches(n -> Objects.equals(n.getEnableAccountingProxy(), false))
        .matches(n -> Objects.equals(n.getWlan().getMacAddressAuthentication(), true))
        .matches(
            n ->
                Objects.equals(n.getWlan().getMacAuthMacFormat(), MacAuthMacFormatEnum.UpperColon));
  }

  @Test
  void testUpdateWifiNetworkRadiusServerProfileSettingsOnDpsk3NetworkWithoutFF(final Tenant tenant,
      AuthRadiusService authRadiusService) {
    var tenantId = tenant.getId();
    var userName = randomName();
    var requestId = randomTxId();
    String dsaeServiceNetworkId;

    final var dsaeServiceNetwork = repositoryUtil.createOrUpdate(
        DPSKNetworkTestFixture.randomDpskNetwork(tenant, authRadiusService,
            network -> {
              network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
              network.setIsDsaeServiceNetwork(true);
              network.setUseDpskService(false);
            }), tenant.getId(), randomTxId());
    dsaeServiceNetworkId = dsaeServiceNetwork.getId();
    dsaeServiceNetwork.setDsaeNetworkPairId(dsaeServiceNetworkId);
    repositoryUtil.createOrUpdate(dsaeServiceNetwork, tenant.getId(), randomTxId());

    var settings = new WifiNetworkRadiusServerProfileSettings();
    settings.setEnableAuthProxy(true);

    RequestParams requestParams = new RequestParams().addPathVariable(WIFI_NETWORK_ID,
        dsaeServiceNetworkId);

    assertThatThrownBy(
        () ->
            messageUtil.sendWifiCfgRequest(
                tenantId,
                requestId,
                CfgAction.UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS,
                userName,
                requestParams,
                settings))
        .isNotNull()
        .getRootCause()
        .isInstanceOf(InvalidPropertyValueException.class)
        .hasMessage(
            "Cannot update network RADIUS server profile on DPSK3 network ["
                + dsaeServiceNetworkId
                + "] as DPSK3 network is not supported.");
  }

  @Test
  @FeatureFlag(enable = WIFI_DPSK3_NON_PROXY_MODE_TOGGLE)
  void testUpdateWifiNetworkRadiusServerProfileSettingsOnDpsk3NetworkWithRadiusProfileAndFFOn(final Tenant tenant,
      AuthRadiusService authRadiusService) {
    var tenantId = tenant.getId();
    var userName = randomName();
    var requestId = randomTxId();
    String dsaeServiceNetworkId;

    final var dsaeServiceNetwork = repositoryUtil.createOrUpdate(
        DPSKNetworkTestFixture.randomDpskNetwork(tenant, authRadiusService,
            network -> {
              network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
              network.setIsDsaeServiceNetwork(true);
              network.setUseDpskService(false);
            }), tenant.getId(), randomTxId());
    dsaeServiceNetworkId = dsaeServiceNetwork.getId();
    dsaeServiceNetwork.setDsaeNetworkPairId(dsaeServiceNetworkId);
    repositoryUtil.createOrUpdate(dsaeServiceNetwork, tenant.getId(), randomTxId());

    var settings = new WifiNetworkRadiusServerProfileSettings();
    settings.setEnableAuthProxy(true);

    var requestParams = new RequestParams().addPathVariable(WIFI_NETWORK_ID,
        dsaeServiceNetworkId);

    assertThatThrownBy(
        () ->
            messageUtil.sendWifiCfgRequest(
                tenantId,
                requestId,
                CfgAction.UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS,
                userName,
                requestParams,
                settings))
        .isNotNull()
        .getRootCause()
        .isInstanceOf(InvalidPropertyValueException.class)
        .hasMessage(
            "Cannot update network RADIUS server profile on DPSK3 network ["
                + dsaeServiceNetworkId + "] when Auth proxy mode is enabled.");
  }

  @Test
  @FeatureFlag(enable = WIFI_DPSK3_NON_PROXY_MODE_TOGGLE)
  void testUpdateWifiNetworkRadiusServerProfileSettingsOnDpsk3NetworkWithoutRadiusProfileAndFFOn(final Tenant tenant,
      AuthRadiusService authRadiusService) {
    var tenantId = tenant.getId();
    var userName = randomName();
    var requestId = randomTxId();
    String dsaeServiceNetworkId;

    authRadiusService.setRadius(null);
    final var dsaeServiceNetwork = repositoryUtil.createOrUpdate(
        DPSKNetworkTestFixture.randomDpskNetwork(tenant, authRadiusService,
            network -> {
              network.getWlan().setWlanSecurity(WlanSecurityEnum.WPA23Mixed);
              network.setIsDsaeServiceNetwork(true);
              network.setUseDpskService(false);
            }), tenant.getId(), randomTxId());
    dsaeServiceNetworkId = dsaeServiceNetwork.getId();
    dsaeServiceNetwork.setDsaeNetworkPairId(dsaeServiceNetworkId);
    repositoryUtil.createOrUpdate(dsaeServiceNetwork, tenant.getId(), randomTxId());

    var settings = new WifiNetworkRadiusServerProfileSettings();
    settings.setEnableAuthProxy(true);

    var requestParams = new RequestParams().addPathVariable(WIFI_NETWORK_ID,
        dsaeServiceNetworkId);

    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestId,
        CfgAction.UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS,
        userName,
        requestParams,
        settings);
    assertActivityStatusSuccess(UPDATE_WIFI_NETWORK_RADIUS_SERVER_PROFILE_SETTINGS, tenantId);

    final var network = repositoryUtil.find(DpskNetwork.class, dsaeServiceNetworkId);
    assertThat(network)
        .isNotNull()
        .matches(n -> Objects.equals(n.getId(), dsaeServiceNetworkId))
        .matches(n -> Objects.equals(n.getEnableAuthProxy(), true))
        .matches(n -> Objects.equals(n.getEnableAccountingProxy(), false));
  }
}
