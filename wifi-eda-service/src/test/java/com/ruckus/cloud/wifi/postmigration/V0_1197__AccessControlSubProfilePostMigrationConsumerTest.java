package com.ruckus.cloud.wifi.postmigration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.repository.ApplicationPolicyRepository;
import com.ruckus.cloud.wifi.repository.DevicePolicyRepository;
import com.ruckus.cloud.wifi.repository.L2AclPolicyRepository;
import com.ruckus.cloud.wifi.repository.L3AclPolicyRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.fixture.ApplicationPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.DevicePolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.L2AclPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.L3AclPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.MessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
class V0_1197__AccessControlSubProfilePostMigrationConsumerTest {
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageUtil messageUtil;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;

  @Autowired private L2AclPolicyRepository l2AclPolicyRepository;
  @Autowired private L3AclPolicyRepository l3AclPolicyRepository;
  @Autowired private DevicePolicyRepository devicePolicyRepository;
  @Autowired private ApplicationPolicyRepository applicationPolicyRepository;

  @Autowired private V0_1197__AccessControlSubProfilePostMigrationConsumer postMigrationConsumer;

  @Nested
  class WhenRunPostMigration {
    private List<String> l2AclPolicyTenantIds;
    private List<String> l3AclPolicyTenantIds;
    private List<String> devicePolicyTenantIds;
    private List<String> applicationPolicyTenantIds;

    @BeforeEach
    void beforeEach() {
      final var tenants =
          Stream.generate(() -> TenantTestFixture.randomTenant(t -> {}))
              .limit(5)
              .peek(tenant -> repositoryUtil.createOrUpdate(tenant, tenant.getId(), randomTxId()))
              .collect(Collectors.toList());
      l2AclPolicyTenantIds =
          tenants.stream()
              .limit(3)
              .map(tenant -> L2AclPolicyTestFixture.randomL2AclPolicy(tenant, l -> {}))
              .peek(
                  policy ->
                      repositoryUtil.createOrUpdate(
                          policy, policy.getTenant().getId(), randomTxId()))
              .map(policy -> policy.getTenant().getId())
              .toList();
      Collections.shuffle(tenants);
      l3AclPolicyTenantIds =
          tenants.stream()
              .limit(3)
              .map(tenant -> L3AclPolicyTestFixture.randomL3AclPolicy(tenant, l -> {}))
              .peek(
                  policy ->
                      repositoryUtil.createOrUpdate(
                          policy, policy.getTenant().getId(), randomTxId()))
              .map(policy -> policy.getTenant().getId())
              .toList();
      Collections.shuffle(tenants);
      devicePolicyTenantIds =
          tenants.stream()
              .limit(3)
              .map(tenant -> DevicePolicyTestFixture.randomDevicePolicy(tenant, d -> {}))
              .peek(
                  policy ->
                      repositoryUtil.createOrUpdate(
                          policy, policy.getTenant().getId(), randomTxId()))
              .map(policy -> policy.getTenant().getId())
              .toList();
      Collections.shuffle(tenants);
      applicationPolicyTenantIds =
          tenants.stream()
              .limit(3)
              .map(tenant -> ApplicationPolicyTestFixture.randomApplicationPolicy(tenant, a -> {}))
              .peek(
                  policy ->
                      repositoryUtil.createOrUpdate(
                          policy, policy.getTenant().getId(), randomTxId()))
              .map(policy -> policy.getTenant().getId())
              .toList();
    }

    @Test
    void thenSendAsyncJobForEachTenant() {
      final var expectedTenantIds = new HashSet<String>();
      expectedTenantIds.addAll(l2AclPolicyRepository.findAllDistinctTenantIds());
      expectedTenantIds.addAll(l3AclPolicyRepository.findAllDistinctTenantIds());
      expectedTenantIds.addAll(devicePolicyRepository.findAllDistinctTenantIds());
      expectedTenantIds.addAll(applicationPolicyRepository.findAllDistinctTenantIds());

      postMigrationConsumer.run(null);

      final var receivedTenantIds = new HashSet<String>();
      final var receivedRequestIds = new HashSet<String>();

      for (int i = 0; i < expectedTenantIds.size(); i++) {
        final var message = receive();
        assertThat(message)
            .isNotNull()
            .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID) != null)
            .matches(m -> m.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID) != null)
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(WifiAsyncJob::getJobCase)
            .isEqualTo(WifiAsyncJob.JobCase.ACCESS_CONTROL_SUB_PROFILE_POST_MIGRATION_JOB);

        receivedTenantIds.add(
            new String(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_TENANT_ID).value()));

        receivedRequestIds.add(
            new String(message.getHeaders().lastHeader(WifiCommonHeader.WIFI_REQUEST_ID).value()));
      }

      assertThat(receivedTenantIds)
          .isNotEmpty()
          .hasSameElementsAs(expectedTenantIds)
          .containsAll(l2AclPolicyTenantIds)
          .containsAll(l3AclPolicyTenantIds)
          .containsAll(devicePolicyTenantIds)
          .containsAll(applicationPolicyTenantIds);

      assertThat(receivedRequestIds).isNotEmpty().hasSize(1);
    }

    private KafkaProtoMessage<WifiAsyncJob> receive() {
      return messageUtil.receive(
          kafkaTopicProvider.getWifiAsyncJob(),
          data -> {
            try {
              return WifiAsyncJob.parseFrom(data);
            } catch (InvalidProtocolBufferException e) {
              throw new RuntimeException(e);
            }
          });
    }
  }
}
