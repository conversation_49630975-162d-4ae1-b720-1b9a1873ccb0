package com.ruckus.cloud.wifi.integration;

import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.cloud.wifi.proto.DpskPassphraseGeneration;
import com.ruckus.cloud.wifi.proto.NetworkVenue;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import com.ruckus.cloud.wifi.proto.WlanAdvancedCustomization;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.SoftAssertions;

public class WifiCfgChangeAssertions {

  private WifiCfgChangeAssertions() {
    // For Sonar squid:S1118
    throw new IllegalStateException("Test assertions class");
  }

  public static Consumer<com.ruckus.cloud.wifi.proto.DpskNetwork> assertDpskNetworkSoftly(
      final com.ruckus.cloud.wifi.eda.viewmodel.DpskNetwork dpskNetworkReq) {
    return dpskNetworkOp -> assertSoftly(softly -> {
      if (dpskNetworkReq.getId() != null) {
        softly.assertThat(dpskNetworkOp.getId())
            .as("The `id` field in DpskNetwork message should not be null").isNotNull()
            .as("The `id` field in DpskNetwork message should not be empty")
            .matches(name -> StringUtils.isNotEmpty(name.getValue()), "should not be empty")
            .extracting(StringValue::getValue)
            .as("The `id` field in DpskNetwork message should be %s", dpskNetworkReq.getId())
            .isEqualTo(dpskNetworkReq.getId());
      }
      softly.assertThat(dpskNetworkOp.getName())
          .as("The `name` field in DpskNetwork message should not be null").isNotNull()
          .as("The `name` field in DpskNetwork message should not be empty")
          .matches(name -> StringUtils.isNotEmpty(name.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `name` field in DpskNetwork message should be %s", dpskNetworkReq.getName())
          .isEqualTo(dpskNetworkReq.getName());
      softly.assertThat(dpskNetworkOp.getOldName())
          .as("The `old_name` in DpskNetwork message should not be null").isNotNull()
          .as("The `old_name` in DpskNetwork message should not be empty")
          .matches(name -> StringUtils.isNotEmpty(name.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `old_name` field in DpskNetwork message should be %s", dpskNetworkReq.getName())
          .isEqualTo(dpskNetworkReq.getName());
      softly.assertThat(dpskNetworkOp.getDescription())
          .as("The `description` field in DpskNetwork message should not be null").isNotNull()
          .as("The `description` field in DpskNetwork message should not be empty")
          .matches(description -> StringUtils.isNotEmpty(description.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `description` field in DpskNetwork message should be %s",
              dpskNetworkReq.getDescription())
          .isEqualTo(dpskNetworkReq.getDescription());
      softly.assertThat(dpskNetworkOp.getOldDescription())
          .as("The `old_description` field in DpskNetwork message should not be null").isNotNull()
          .as("The `old_description` field in DpskNetwork message should not be empty")
          .matches(description -> StringUtils.isNotEmpty(description.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `old_description` field in DpskNetwork message should be %s",
              dpskNetworkReq.getDescription())
          .isEqualTo(dpskNetworkReq.getDescription());
      softly.assertThat(dpskNetworkOp.getWlanId())
          .as("The `wlan_id` field in DpskNetwork message should not be null").isNotNull()
          .as("The `wlan_id` field in DpskNetwork message should not be empty")
          .matches(wlanId -> StringUtils.isNotEmpty(wlanId.getValue()), "should not be empty");
      softly.assertThat(dpskNetworkOp.getOldWlanId())
          .as("The `old_wlan_id` field in DpskNetwork message should not be null").isNotNull()
          .as("The `old_wlan_id` field in DpskNetwork message should not be empty")
          .matches(wlanId -> StringUtils.isNotEmpty(wlanId.getValue()), "should not be empty");

      if (dpskNetworkReq.getDpskPassphraseGeneration() == null) {
        softly.assertThat(dpskNetworkOp.hasDpskPassphraseGeneration()).isFalse();
        softly.assertThat(dpskNetworkOp.hasOldDpskPassphraseGeneration()).isFalse();
        return;
      }
      assertDpskPassphraseGenerationSoftly(softly, dpskNetworkReq.getDpskPassphraseGeneration())
          .accept("dpsk_passphrase_generation", dpskNetworkOp.getDpskPassphraseGeneration());
      assertDpskPassphraseGenerationSoftly(softly, dpskNetworkReq.getDpskPassphraseGeneration())
          .accept("old_dpsk_passphrase_generation", dpskNetworkOp.getOldDpskPassphraseGeneration());
    });
  }

  private static BiConsumer<String, DpskPassphraseGeneration> assertDpskPassphraseGenerationSoftly(
      SoftAssertions softly, final com.ruckus.cloud.wifi.eda.viewmodel.DpskPassphraseGeneration dpskPassphraseGenerationReq) {
    return (dpskPassphraseGenerationFieldName, dpskPassphraseGenerationOp) -> {
      softly.assertThat(dpskPassphraseGenerationOp.getLength())
          .as("The `length` setting in `%s` field of DpskNetwork message should not be null",
              dpskPassphraseGenerationFieldName)
          .isNotNull()
          .extracting(Int32Value::getValue)
          .as("The `length` setting in `%s` field of DpskNetwork message should be %d",
              dpskPassphraseGenerationFieldName,
              dpskPassphraseGenerationReq.getLength())
          .isEqualTo(dpskPassphraseGenerationReq.getLength().intValue());
      softly.assertThat(dpskPassphraseGenerationOp.getFormat().name())
          .as("The `format` setting in `%s` field of DpskNetwork message should be %s",
              dpskPassphraseGenerationFieldName,
              "PassphraseFormatEnum_" + dpskPassphraseGenerationReq.getFormat().name())
          .isEqualTo("PassphraseFormatEnum_" + dpskPassphraseGenerationReq.getFormat().name());
      softly.assertThat(dpskPassphraseGenerationOp.getExpiration().name())
          .as("The `expiration` setting in `%s` field of DpskNetwork message should be %s",
              dpskPassphraseGenerationFieldName,
              "PassphraseExpirationEnum_" + dpskPassphraseGenerationReq.getExpiration().name())
          .isEqualTo("PassphraseExpirationEnum_" + dpskPassphraseGenerationReq.getExpiration().name());
      softly.assertThat(dpskPassphraseGenerationOp.getPolicyDefaultAccess().name())
          .as("The `policy_default_access` setting in `%s` field of DpskNetwork message should be %s",
              dpskPassphraseGenerationFieldName,
              "DpskPolicyDefaultAccessEnum_" + dpskPassphraseGenerationReq.getPolicyDefaultAccess().name())
          .isEqualTo("DpskPolicyDefaultAccessEnum_" + dpskPassphraseGenerationReq.getPolicyDefaultAccess().name());
    };
  }

  public static Consumer<com.ruckus.cloud.wifi.proto.Wlan> assertWlanSoftly(
      final com.ruckus.cloud.wifi.eda.viewmodel.DpskWlan dpskWlanReq) {
    return wlanOp -> assertSoftly(softly -> {
      softly.assertThat(wlanOp.getEnabled())
          .as("The `enabled` field in Wlan message should not be null")
          .isNotNull()
          .extracting(BoolValue::getValue)
          .as("The `enabled` field in Wlan message should be %s", dpskWlanReq.getEnabled())
          .isEqualTo(dpskWlanReq.getEnabled());
      softly.assertThat(wlanOp.getOldEnabled())
          .as("The `old_enabled` field in Wlan message should not be null")
          .isNotNull()
          .extracting(BoolValue::getValue)
          .as("The `old_enabled` field in Wlan message should be %s", dpskWlanReq.getEnabled())
          .isEqualTo(dpskWlanReq.getEnabled());
      softly.assertThat(wlanOp.getSsid())
          .as("The `ssid` field in Wlan message should not be null").isNotNull()
          .as("The `ssid` field in Wlan message should not be empty")
          .matches(ssid -> StringUtils.isNotEmpty(ssid.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `ssid` field in Wlan message should be %s", dpskWlanReq.getSsid())
          .isEqualTo(dpskWlanReq.getSsid());
      softly.assertThat(wlanOp.getOldSsid())
          .as("The `old_ssid` field in Wlan message should not be null").isNotNull()
          .as("The `old_ssid` field in Wlan message should not be empty")
          .matches(ssid -> StringUtils.isNotEmpty(ssid.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `old_ssid` field in Wlan message should be %s", dpskWlanReq.getSsid())
          .isEqualTo(dpskWlanReq.getSsid());
      softly.assertThat(wlanOp.getVlanId())
          .as("The `vlan_id` field in Wlan message should not be null")
          .isNotNull()
          .extracting(Int32Value::getValue)
          .as("The `vlan_id` field in Wlan message should be %s", dpskWlanReq.getVlanId())
          .isEqualTo(dpskWlanReq.getVlanId().intValue());
      softly.assertThat(wlanOp.getOldVlanId())
          .as("The `old_vlan_id` field in Wlan message should not be null")
          .isNotNull()
          .extracting(Int32Value::getValue)
          .as("The `old_vlan_id` field in Wlan message should be %s", dpskWlanReq.getVlanId())
          .isEqualTo(dpskWlanReq.getVlanId().intValue());
      softly.assertThat(wlanOp.getWlanSecurity().name())
          .as("The `wlan_security` field in Wlan message should be %s",
              "WlanSecurityEnum_" + dpskWlanReq.getWlanSecurity().name())
          .isEqualTo("WlanSecurityEnum_" + dpskWlanReq.getWlanSecurity().name());
      softly.assertThat(wlanOp.getOldWlanSecurity().name())
          .as("The `old_wlan_security` field in Wlan message should be %s",
              "WlanSecurityEnum_" + dpskWlanReq.getWlanSecurity().name())
          .isEqualTo("WlanSecurityEnum_" + dpskWlanReq.getWlanSecurity().name());
    });
  }

  public static Consumer<com.ruckus.cloud.wifi.proto.Wlan> assertWlanSoftly(
          final com.ruckus.cloud.wifi.eda.viewmodel.OpenWlan openWlanReq) {
    return wlanOp -> assertSoftly(softly -> {
      softly.assertThat(wlanOp.getAdvancedCustomization())
              .as("The `enabled` field in Wlan message should not be null")
              .isNotNull()
              .extracting(WlanAdvancedCustomization::getEnableAaaVlanOverride)
              .extracting(BoolValue::getValue)
              .as("The `enableAaaVlanOverride` field in Wlan message should be %s", openWlanReq.getAdvancedCustomization().getEnableAaaVlanOverride())
              .isEqualTo(openWlanReq.getAdvancedCustomization().getEnableAaaVlanOverride());
    });
  }

  public static Consumer<NetworkVenue> assertNetworkVenueSoftly(
      final String expectedNetworkId, final String expectedVenueId) {
    return networkVenueOp -> assertSoftly(softly -> {
      softly.assertThat(networkVenueOp.getNetworkId())
          .as("The `networkId` field in NetworkVenue message should not be null").isNotNull()
          .as("The `networkId` field in NetworkVenue message should not be empty")
          .matches(networkId -> StringUtils.isNotEmpty(networkId.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `networkId` field in NetworkVenue message should be %s", expectedNetworkId)
          .isEqualTo(expectedNetworkId);
      softly.assertThat(networkVenueOp.getOldNetworkId())
          .as("The `oldNetworkId` field in NetworkVenue message should not be null").isNotNull()
          .as("The `oldNetworkId` field in NetworkVenue message should not be empty")
          .matches(networkId -> StringUtils.isNotEmpty(networkId.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `oldNetworkId` field in NetworkVenue message should be %s", expectedNetworkId)
          .isEqualTo(expectedNetworkId);
      softly.assertThat(networkVenueOp.getVenueId())
          .as("The `venueId` field in NetworkVenue message should not be null").isNotNull()
          .as("The `venueId` field in NetworkVenue message should not be empty")
          .matches(venueId -> StringUtils.isNotEmpty(venueId.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `venueId` field in NetworkVenue message should be %s", expectedVenueId)
          .isEqualTo(expectedVenueId);
      softly.assertThat(networkVenueOp.getOldVenueId())
          .as("The `oldVenueId` field in NetworkVenue message should not be null").isNotNull()
          .as("The `oldVenueId` field in NetworkVenue message should not be empty")
          .matches(venueId -> StringUtils.isNotEmpty(venueId.getValue()), "should not be empty")
          .extracting(StringValue::getValue)
          .as("The `oldVenueId` field in NetworkVenue message should be %s", expectedVenueId)
          .isEqualTo(expectedVenueId);
    });
  }
}
