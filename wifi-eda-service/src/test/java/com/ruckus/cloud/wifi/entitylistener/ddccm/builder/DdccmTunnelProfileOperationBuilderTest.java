package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeInteger;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmVxLANTunnelProfile.TunnelType;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmVxLANTunnelProfile.VlanHandleType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation.ConfigCase;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelMtuTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.utils.FeatureRolesUtils;
import java.util.List;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@Tag("TunnelServiceProfileTest")
@WifiUnitTest
class DdccmTunnelProfileOperationBuilderTest {

  @Autowired
  private DdccmTunnelProfileOperationBuilder builder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  void testCreateTunnelProfileWithManalMode() {
    TunnelProfile tunnelProfile = createTunnelProfileWithManualMode();
    List<Operation> operations = builder.build(new NewTxEntity<>(tunnelProfile), emptyTxChanges());

    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals(tunnelProfile.getName(), operations.get(0).getCcmVxLANTunnelProfile().getName());
    assertFalse(operations.get(0).getCcmVxLANTunnelProfile().getForcedFragmentation());
    assertEquals((int) tunnelProfile.getMtuSize(),
        operations.get(0).getCcmVxLANTunnelProfile().getManualModePmtu());
    assertTrue(operations.get(0).getCcmVxLANTunnelProfile().getPmtuManualMode());
    assertEquals(tunnelProfile.getId(), operations.get(0).getCcmVxLANTunnelProfile().getId());
    assertEquals(tunnelProfile.getAgeTimeMinutes()*60,
        operations.get(0).getCcmVxLANTunnelProfile().getVxlanTunnelAgeTimeSecs());
    assertEquals(VlanHandleType.VXLAN_VNI_ONLY,
        operations.get(0).getCcmVxLANTunnelProfile().getVxlanVlanHandleType());
    assertEquals((int)tunnelProfile.getKeepAliveInterval(),
        operations.get(0).getCcmVxLANTunnelProfile().getKeepAliveInterval());
    assertEquals((int)tunnelProfile.getKeepAliveRetry(),
        operations.get(0).getCcmVxLANTunnelProfile().getKeepAliveRetries());
    assertEquals(0, operations.get(0).getCcmVxLANTunnelProfile().getPmtudRequestRetries());
    assertEquals(0, operations.get(0).getCcmVxLANTunnelProfile().getPmtudRequestInterval());
  }

  @Test
  void testCreateTunnelProfileWithAutoMode() {
    TunnelProfile tunnelProfile = createTunnelProfileWithAutoMode();
    List<Operation> operations = builder.build(new NewTxEntity<>(tunnelProfile), emptyTxChanges());

    assertEquals(1, operations.size());

    assertEquals(Action.ADD, operations.get(0).getAction());
    assertEquals(tunnelProfile.getName(), operations.get(0).getCcmVxLANTunnelProfile().getName());
    assertEquals(BooleanUtils.toBoolean(tunnelProfile.getForceFragmentation()),
        operations.get(0).getCcmVxLANTunnelProfile().getForcedFragmentation());
    assertFalse(operations.get(0).getCcmVxLANTunnelProfile().getPmtuManualMode());
    assertEquals(tunnelProfile.getId(), operations.get(0).getCcmVxLANTunnelProfile().getId());
    assertEquals(tunnelProfile.getAgeTimeMinutes()*60,
        operations.get(0).getCcmVxLANTunnelProfile().getVxlanTunnelAgeTimeSecs());
    assertEquals(VlanHandleType.VXLAN_VNI_ONLY,
        operations.get(0).getCcmVxLANTunnelProfile().getVxlanVlanHandleType());
    assertEquals(0, operations.get(0).getCcmVxLANTunnelProfile().getManualModePmtu());
    assertEquals((int)tunnelProfile.getMtuRequestRetry(),
        operations.get(0).getCcmVxLANTunnelProfile().getPmtudRequestRetries());
    assertEquals((int)tunnelProfile.getMtuRequestTimeout(),
        operations.get(0).getCcmVxLANTunnelProfile().getPmtudRequestInterval());
    assertEquals((int)tunnelProfile.getKeepAliveInterval(),
        operations.get(0).getCcmVxLANTunnelProfile().getKeepAliveInterval());
    assertEquals((int)tunnelProfile.getKeepAliveRetry(),
        operations.get(0).getCcmVxLANTunnelProfile().getKeepAliveRetries());
  }

  private static TunnelProfile createTunnelProfileWithManualMode() {
    TunnelProfile tunnelProfile = Generators.tunnelProfile().generate();
    tunnelProfile.setMtuType(TunnelMtuTypeEnum.MANUAL);
    tunnelProfile.setMtuSize(rangeInteger(576, 1450).generate());
    return tunnelProfile;
  }

  private static TunnelProfile createTunnelProfileWithAutoMode() {
    TunnelProfile tunnelProfile = Generators.tunnelProfile().generate();
    tunnelProfile.setMtuType(TunnelMtuTypeEnum.AUTO);
    return tunnelProfile;
  }

  @FeatureFlag(enable = FlagNames.EDGE_NAT_TRAVERSAL_PHASE1_TOGGLE)
  @Nested
  class GivenEdgeNatTraversalPhase1ToggleEnabled {

    @FeatureRole(FeatureRolesUtils.EDGE_NAT_TRAVERSAL)
    @Nested
    class WithEdgeNatTraversalFeatureRole {

      @Test
      void testCreateTunnelProfileWithNatTraversalEnabled() {
        assertThat(builder.build(new NewTxEntity<>(
            createTunnelProfileWithNatTraversalEnabled(true)), emptyTxChanges()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile -> {
                    assertThat(ccmVxLANTunnelProfile.getTunnelType()).isEqualTo(TunnelType.VXLAN_GPE);
                    assertThat(ccmVxLANTunnelProfile.getNatTSupport()).isTrue();
                  });
            });

        assertThat(builder.build(new NewTxEntity<>(
            createTunnelProfileWithNatTraversalEnabled(false)), emptyTxChanges()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile -> {
                    assertThat(ccmVxLANTunnelProfile.getTunnelType()).isEqualTo(TunnelType.VXLAN_GPE);
                    assertThat(ccmVxLANTunnelProfile.getNatTSupport()).isFalse();
                  });
            });
      }
    }

    @Nested
    class WithoutEdgeNatTraversalFeatureRole {

      @Test
      void testCreateTunnelProfileWithNatTraversalEnabled() {
        assertThat(builder.build(new NewTxEntity<>(
            createTunnelProfileWithNatTraversalEnabled(true)), emptyTxChanges()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile -> {
                    assertThat(ccmVxLANTunnelProfile.getTunnelType()).isEqualTo(TunnelType.VXLAN_GPE);
                    assertThat(ccmVxLANTunnelProfile.getNatTSupport()).isFalse();
                  });
            });

        assertThat(builder.build(new NewTxEntity<>(
            createTunnelProfileWithNatTraversalEnabled(false)), emptyTxChanges()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile -> {
                    assertThat(ccmVxLANTunnelProfile.getTunnelType()).isEqualTo(TunnelType.VXLAN_GPE);
                    assertThat(ccmVxLANTunnelProfile.getNatTSupport()).isFalse();
                  });
            });
      }
    }
  }

  @FeatureFlag(disable = FlagNames.EDGE_NAT_TRAVERSAL_PHASE1_TOGGLE)
  @Nested
  class GivenEdgeNatTraversalPhase1ToggleDisabled {

    @Test
    void testCreateTunnelProfileWithNatTraversalEnabled() {
      assertThat(builder.build(new NewTxEntity<>(
          createTunnelProfileWithNatTraversalEnabled(true)), emptyTxChanges()))
          .isNotEmpty().singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(Action.ADD);
            assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
            assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
            assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                .satisfies(ccmVxLANTunnelProfile -> {
                  assertThat(ccmVxLANTunnelProfile.getTunnelType())
                      .isEqualTo(TunnelType.TUNNEL_TYPE_UNSPECIFIED);
                  assertThat(ccmVxLANTunnelProfile.getNatTSupport()).isFalse();
                });
          });

      assertThat(builder.build(new NewTxEntity<>(
          createTunnelProfileWithNatTraversalEnabled(false)), emptyTxChanges()))
          .isNotEmpty().singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(Action.ADD);
            assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
            assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
            assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                .satisfies(ccmVxLANTunnelProfile -> {
                  assertThat(ccmVxLANTunnelProfile.getTunnelType())
                      .isEqualTo(TunnelType.TUNNEL_TYPE_UNSPECIFIED);
                  assertThat(ccmVxLANTunnelProfile.getNatTSupport()).isFalse();
                });
          });
    }
  }

  private static TunnelProfile createTunnelProfileWithNatTraversalEnabled(
      Boolean natTraversalEnabled) {
    return Generators.tunnelProfile()
        .setNatTraversalEnabled(always(natTraversalEnabled)).generate();
  }

  @FeatureFlag(enable = FlagNames.EDGE_L2GRE_TOGGLE)
  @Nested
  class GivenEdgeL2greToggleEnable {

    @FeatureRole(FeatureRolesUtils.EDGE_L2GRE)
    @Test
    void testCreateL2greTunnelProfileWithFeatureRole() {
      TunnelProfile tunnelProfile = createTunnelProfileWithManualMode();
      tunnelProfile.setTunnelType(TunnelTypeEnum.L2GRE);
      List<Operation> operations = builder.build(new NewTxEntity<>(tunnelProfile), emptyTxChanges());
      assertEquals(1, operations.size());

      assertEquals(Action.ADD, operations.get(0).getAction());
      assertEquals(tunnelProfile.getId(), operations.get(0).getCcmVxLANTunnelProfile().getId());
      assertEquals(tunnelProfile.getName(), operations.get(0).getCcmVxLANTunnelProfile().getName());
      assertEquals(tunnelProfile.getTunnelType().name(), operations.get(0).getCcmVxLANTunnelProfile().getTunnelType().name());

      assertFalse(operations.get(0).getCcmVxLANTunnelProfile().getForcedFragmentation());

      assertEquals((int) tunnelProfile.getMtuSize(),
          operations.get(0).getCcmVxLANTunnelProfile().getManualModePmtu());
      assertTrue(operations.get(0).getCcmVxLANTunnelProfile().getPmtuManualMode());

      assertEquals(VlanHandleType.VXLAN_VNI_ONLY,
          operations.get(0).getCcmVxLANTunnelProfile().getVxlanVlanHandleType());

      assertNotEquals((int)tunnelProfile.getKeepAliveInterval(),
          operations.get(0).getCcmVxLANTunnelProfile().getKeepAliveInterval());
      assertNotEquals((int)tunnelProfile.getKeepAliveRetry(),
          operations.get(0).getCcmVxLANTunnelProfile().getKeepAliveRetries());
      assertEquals(0,
          operations.get(0).getCcmVxLANTunnelProfile().getVxlanTunnelAgeTimeSecs());
    }

    @Test
    void testCreateL2greTunnelProfileWithoutFeatureRole() {
      TunnelProfile tunnelProfile = createTunnelProfileWithManualMode();
      tunnelProfile.setTunnelType(TunnelTypeEnum.L2GRE);
      List<Operation> operations = builder.build(new NewTxEntity<>(tunnelProfile), emptyTxChanges());
      assertEquals(1, operations.size());

      assertEquals(Action.ADD, operations.get(0).getAction());
      assertEquals(tunnelProfile.getId(), operations.get(0).getCcmVxLANTunnelProfile().getId());
      assertEquals(tunnelProfile.getName(), operations.get(0).getCcmVxLANTunnelProfile().getName());
      assertFalse(operations.get(0).getCcmVxLANTunnelProfile().getForcedFragmentation());
      assertEquals((int) tunnelProfile.getMtuSize(),
          operations.get(0).getCcmVxLANTunnelProfile().getManualModePmtu());
      assertTrue(operations.get(0).getCcmVxLANTunnelProfile().getPmtuManualMode());
      assertEquals(VlanHandleType.VXLAN_VNI_ONLY,
          operations.get(0).getCcmVxLANTunnelProfile().getVxlanVlanHandleType());
      assertEquals(0,
          operations.get(0).getCcmVxLANTunnelProfile().getVxlanTunnelAgeTimeSecs());

      assertEquals(tunnelProfile.getTunnelType().name(), operations.get(0).getCcmVxLANTunnelProfile().getTunnelType().name());
    }
  }

  @FeatureFlag(disable = FlagNames.EDGE_L2GRE_TOGGLE)
  @Nested
  class GivenEdgeL2greToggleDisable {

    @FeatureRole(FeatureRolesUtils.EDGE_L2GRE)
    @Test
    void testCreateL2greTunnelProfileWithFeatureRole() {
      TunnelProfile tunnelProfile = createTunnelProfileWithManualMode();
      tunnelProfile.setTunnelType(TunnelTypeEnum.L2GRE);
      List<Operation> operations = builder.build(new NewTxEntity<>(tunnelProfile), emptyTxChanges());
      assertEquals(1, operations.size());

      assertEquals(Action.ADD, operations.get(0).getAction());
      assertEquals(tunnelProfile.getId(), operations.get(0).getCcmVxLANTunnelProfile().getId());
      assertEquals(tunnelProfile.getName(), operations.get(0).getCcmVxLANTunnelProfile().getName());
      assertFalse(operations.get(0).getCcmVxLANTunnelProfile().getForcedFragmentation());
      assertEquals((int) tunnelProfile.getMtuSize(),
          operations.get(0).getCcmVxLANTunnelProfile().getManualModePmtu());
      assertTrue(operations.get(0).getCcmVxLANTunnelProfile().getPmtuManualMode());
      assertEquals(VlanHandleType.VXLAN_VNI_ONLY,
          operations.get(0).getCcmVxLANTunnelProfile().getVxlanVlanHandleType());
      assertEquals(tunnelProfile.getAgeTimeMinutes()*60,
          operations.get(0).getCcmVxLANTunnelProfile().getVxlanTunnelAgeTimeSecs());

      assertNotEquals(tunnelProfile.getTunnelType().name(), operations.get(0).getCcmVxLANTunnelProfile().getTunnelType().name());
    }

    @Test
    void testCreateL2greTunnelProfileWithoutFeatureRole() {
      TunnelProfile tunnelProfile = createTunnelProfileWithManualMode();
      tunnelProfile.setTunnelType(TunnelTypeEnum.L2GRE);
      List<Operation> operations = builder.build(new NewTxEntity<>(tunnelProfile), emptyTxChanges());
      assertEquals(1, operations.size());

      assertEquals(Action.ADD, operations.get(0).getAction());
      assertEquals(tunnelProfile.getId(), operations.get(0).getCcmVxLANTunnelProfile().getId());
      assertEquals(tunnelProfile.getName(), operations.get(0).getCcmVxLANTunnelProfile().getName());
      assertFalse(operations.get(0).getCcmVxLANTunnelProfile().getForcedFragmentation());
      assertEquals((int) tunnelProfile.getMtuSize(),
          operations.get(0).getCcmVxLANTunnelProfile().getManualModePmtu());
      assertTrue(operations.get(0).getCcmVxLANTunnelProfile().getPmtuManualMode());
      assertEquals(VlanHandleType.VXLAN_VNI_ONLY,
          operations.get(0).getCcmVxLANTunnelProfile().getVxlanVlanHandleType());
      assertEquals(tunnelProfile.getAgeTimeMinutes()*60,
          operations.get(0).getCcmVxLANTunnelProfile().getVxlanTunnelAgeTimeSecs());

      assertNotEquals(tunnelProfile.getTunnelType().name(), operations.get(0).getCcmVxLANTunnelProfile().getTunnelType().name());
    }
  }


  @FeatureFlag(enable = FlagNames.EDGE_IPSEC_VXLAN_TOGGLE)
  @Nested
  class GivenEdgeIpsecVxLanToggleEnabled {
    @FeatureRole(FeatureRolesUtils.EDGE_IPSEC_VXLAN)
    @Nested
    class WithEdgeIpsecVxLanFeatureRole {
      @Test
      void testCreateTunnelProfileWithIpsecProfile()  {
        TunnelProfile tunnelProfile = createTunnelProfileWithIpsecProfile();
        assertThat(builder.build(new NewTxEntity<>(
            tunnelProfile), emptyTxChanges()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile ->
                      assertThat(ccmVxLANTunnelProfile.getIpsecProfileId())
                          .isEqualTo(tunnelProfile.getIpsecProfile().getId()));
            });
      }

      @Test
      void testCreateTunnelProfileWithoutIpsecProfile()  {
        assertThat(builder.build(new NewTxEntity<>(
            createTunnelProfileWithAutoMode()), emptyTxChanges()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile ->
                      assertThat(ccmVxLANTunnelProfile.getIpsecProfileId()).isEmpty());
            });
      }
    }

    @Nested
    class WithoutEdgeIpsecVxLanFeatureRole {
      @Test
      void testCreateTunnelProfileWithIpsecProfile()  {
        assertThat(builder.build(new NewTxEntity<>(
            createTunnelProfileWithIpsecProfile()), emptyTxChanges()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile ->
                      assertThat(ccmVxLANTunnelProfile.getIpsecProfileId()).isEmpty());
            });
      }

      @Test
      void testCreateTunnelProfileWithoutIpsecProfile()  {
        assertThat(builder.build(new NewTxEntity<>(
            createTunnelProfileWithAutoMode()), emptyTxChanges()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getAction()).isEqualTo(Action.ADD);
              assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
              assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
              assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                  .satisfies(ccmVxLANTunnelProfile ->
                      assertThat(ccmVxLANTunnelProfile.getIpsecProfileId()).isEmpty());
            });
      }
    }
  }

  private static TunnelProfile createTunnelProfileWithIpsecProfile() {
    TunnelProfile tunnelProfile = Generators.tunnelProfile().generate();
    tunnelProfile.setIpsecProfile(Generators.ipsecProfile().generate());
    return tunnelProfile;
  }

  @FeatureFlag(disable = FlagNames.EDGE_IPSEC_VXLAN_TOGGLE)
  @Nested
  class GivenEdgeIpsecVxLanToggleDisabled {
    @Test
    void testCreateTunnelProfileWithIpsecProfile()  {
      assertThat(builder.build(new NewTxEntity<>(
          createTunnelProfileWithIpsecProfile()), emptyTxChanges()))
          .isNotEmpty().singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(Action.ADD);
            assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
            assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
            assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                .satisfies(ccmVxLANTunnelProfile ->
                    assertThat(ccmVxLANTunnelProfile.getIpsecProfileId()).isEmpty());
          });
    }

    @Test
    void testCreateTunnelProfileWithoutIpsecProfile()  {
      assertThat(builder.build(new NewTxEntity<>(
          createTunnelProfileWithAutoMode()), emptyTxChanges()))
          .isNotEmpty().singleElement()
          .satisfies(op -> {
            assertThat(op.getAction()).isEqualTo(Action.ADD);
            assertThat(op.getConfigCase()).isEqualTo(ConfigCase.CCMVXLANTUNNELPROFILE);
            assertThat(op.hasCcmVxLANTunnelProfile()).isTrue();
            assertThat(op.getCcmVxLANTunnelProfile()).isNotNull()
                .satisfies(ccmVxLANTunnelProfile ->
                    assertThat(ccmVxLANTunnelProfile.getIpsecProfileId()).isEmpty());
          });
    }
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    public DdccmTunnelProfileOperationBuilder ddccmTunnelProfileOperationBuilder() {
      var builder = spy(DdccmTunnelProfileOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }
  }
}
