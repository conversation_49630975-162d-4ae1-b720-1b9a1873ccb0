package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.ddccm.protobuf.wifi.DnsProxyRule;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.DnsProxy;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueDnsProxy;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class DdccmVenueDnsProxyOperationBuilderTest {

  @SpyBean
  private DdccmVenueDnsProxyOperationBuilder builder;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  public void testBuild() {
    DnsProxy dnsProxy = Generators.dnsProxy().generate();
    NetworkVenue networkVenue = Generators.networkVenue()
        .setVenue(Generators.venue())
        .generate();
    VenueDnsProxy venueDnsProxy = new VenueDnsProxy();
    venueDnsProxy.setId(randomId());
    venueDnsProxy.setDnsProxy(dnsProxy);
    venueDnsProxy.setNetworkVenue(networkVenue);

    List<Operation> operationList = builder.build(new NewTxEntity<>(venueDnsProxy), emptyTxChanges());
    assertThat(operationList).isNotEmpty()
        .hasSize(1)
        .extracting(Operation::getVenueDnsProxy)
        .satisfiesExactly(vDnsProxy -> {
          assertThat(vDnsProxy.getId()).isEqualTo(venueDnsProxy.getId());
          assertThat(vDnsProxy.getVenueId()).isEqualTo(venueDnsProxy.getNetworkVenue().getVenue().getId());
          assertThat(vDnsProxy.getName()).isEqualTo(venueDnsProxy.getId());
          assertThat(vDnsProxy.getDescription()).isEqualTo(venueDnsProxy.getNetworkVenue().getVenue().getId());
          assertThat(vDnsProxy.getDnsProxyRuleList())
              .hasSize(dnsProxy.getDnsProxyRules().size())
              .satisfies(ruleList -> {
                assertThat(ruleList)
                    .extracting(DnsProxyRule::getDomainName)
                    .containsExactlyInAnyOrder(
                        dnsProxy.getDnsProxyRules().stream().map(com.ruckus.cloud.wifi.eda.servicemodel.DnsProxyRule::getDomainName).toArray(String[]::new)
                    );

                assertThat(ruleList)
                    .flatExtracting(DnsProxyRule::getIpList)
                    .containsExactlyInAnyOrder(
                        dnsProxy.getDnsProxyRules().stream().flatMap(rule -> rule.getIpList().stream()).toArray(String[]::new)
                    );
              });

        });
  }

}
