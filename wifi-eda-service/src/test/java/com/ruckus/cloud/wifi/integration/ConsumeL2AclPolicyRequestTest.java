package com.ruckus.cloud.wifi.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.L2AccessControl;
import com.ruckus.acx.ddccm.protobuf.wifi.L2RestrictionEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.L2AclPolicyRestCtrl;
import com.ruckus.cloud.wifi.eda.service.L2AclPolicyServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AccessEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.L2AclPolicyTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;


@Slf4j
@WifiIntegrationTest
public class ConsumeL2AclPolicyRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private L2AclPolicyServiceCtrl l2AclPolicyServiceCtrl;

  @Test
  public void testCudL2AclPolicy(Tenant tenant) throws Exception {
    // Given
    String tenantId = txCtxExtension.getTenantId();
    createVenue(tenant, "venue-test");

    // When - add L2 policy
    L2AclPolicy l2AclPolicy = L2AclPolicyTestFixture.randomL2AclPolicy();
    executeNewUiEdaFlow(tenantId, l2AclPolicy, CfgAction.ADD_L2ACL_POLICY);
    // Then - add L2 policy
    // assert activity service
    assertActivityStatusSuccess(ApiFlowNames.ADD_L2ACL_POLICY, tenantId);
    // assert result
    List<L2AclPolicy> policies = getAllL2AclPolicies();
    assertEquals(1, policies.size(), "Should have one L2 Policy");
    assertEquals(l2AclPolicy.getName(), policies.get(0).getName(), "Policy name should equal test data");
    assertEquals(AccessEnum.ALLOW, policies.get(0).getAccess(), "Policy access should equal test data");

    // assert ddccm - expected invoke 1 l2 policy
    List<L2AccessControl> ddccmPolicies = receiveDdccmOperations(1, tenantId).stream()
            .filter(o -> o.getAction().equals(Action.ADD))
            .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.L2ACCESSCONTROL))
            .map(Operation::getL2AccessControl).collect(Collectors.toList());
    assertEquals(1, ddccmPolicies.size(), "Ddccm - Should have one L2 Policy");
    assertEquals(l2AclPolicy.getName(), ddccmPolicies.get(0).getName(), "Ddccm - Policy name should equal test data");
    assertEquals(L2RestrictionEnum.L2RestrictionEnum_ALLOW, ddccmPolicies.get(0).getEtherTypeRestriction(), "Ddccm - Policy access should equal test data");

    // assert cmnViewModelCollector - expected invoke 1 l2 policy
    assertCmnViewModelCollectorPolicies(OpType.ADD, policies.get(0).getId(), policies.get(0).getName(), tenant.getId(), 0);

    // When - update L2 policy
    l2AclPolicy.setName("policy2");
    l2AclPolicy.setAccess(AccessEnum.BLOCK);
    executeNewUiEdaFlow(tenantId, l2AclPolicy, CfgAction.UPDATE_L2ACL_POLICY, l2AclPolicy.getId());

    // Then - update L2 policy
    // assert activity service
    assertActivityStatusSuccess(ApiFlowNames.UPDATE_L2ACL_POLICY, tenantId);
    // assert result
    policies = getAllL2AclPolicies();
    assertEquals(1, policies.size(), "Should have one L2 Policy");
    assertEquals("policy2", policies.get(0).getName(), "Policy name should equal test data");
    assertEquals(AccessEnum.BLOCK, policies.get(0).getAccess(), "Policy access should equal test data");
    // assert ddccm - expected invoke 1 l2 policy
    ddccmPolicies = receiveDdccmOperations(1, tenantId).stream()
            .filter(o -> o.getAction().equals(Action.MODIFY))
            .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.L2ACCESSCONTROL))
            .map(Operation::getL2AccessControl).collect(Collectors.toList());
    assertEquals(1, ddccmPolicies.size(), "Ddccm - Should have one L2 Policy");
    assertEquals("policy2", ddccmPolicies.get(0).getName(), "Ddccm - Policy name should equal test data");
    assertEquals(L2RestrictionEnum.L2RestrictionEnum_BLOCK, ddccmPolicies.get(0).getRestriction(), "Ddccm - Policy access should equal test data");
    // assert cmnViewModelCollector - expected invoke 1 l2 policy
    assertCmnViewModelCollectorPolicies(OpType.MOD, policies.get(0).getId(), policies.get(0).getName(), tenantId, 0);

    // When - delete L2 policy
    executeNewUiEdaFlow(tenantId, null, CfgAction.DELETE_L2ACL_POLICY, l2AclPolicy.getId());
    // Then - update L2 policy
    // assert activity service
    assertActivityStatusSuccess(ApiFlowNames.DELETE_L2ACL_POLICY, tenantId);
    // assert result
    policies = getAllL2AclPolicies();
    assertEquals(0, policies.size(), "Should have no L2 Policy");
    // assert ddccm - expected invoke 1 l2 policy
    ddccmPolicies = receiveDdccmOperations(1, tenantId).stream()
            .filter(o -> o.getAction().equals(Action.DELETE))
            .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.L2ACCESSCONTROL))
            .map(Operation::getL2AccessControl).collect(Collectors.toList());
    assertEquals(1, ddccmPolicies.size(), "Ddccm - Should have one L2 Policy");
    assertEquals(l2AclPolicy.getId(), ddccmPolicies.get(0).getId(), "Ddccm - Policy id should equal test data");
    assertCmnViewModelCollectorPolicies(OpType.DEL, l2AclPolicy.getId(), null, tenantId, 0);
  }

  private void executeNewUiEdaFlow(String tenantId, L2AclPolicy l2AclPolicy, CfgAction cfgAction) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();

    sendWifiCfgRequest(tenantId, requestId, cfgAction, userName, mapViewModel(l2AclPolicy));
  }

  private void executeNewUiEdaFlow(String tenantId, L2AclPolicy policy, CfgAction action, String policyId) {
    String requestId = txCtxExtension.getRequestId();
    String userName = txCtxExtension.getUserName();
    RequestParams params = new RequestParams().addPathVariable("l2AclPolicyId", policyId);

    sendWifiCfgRequest(tenantId, requestId, action, userName, params, mapViewModel(policy));
  }

  @SneakyThrows
  public List<L2AclPolicy> getAllL2AclPolicies() {
    return l2AclPolicyServiceCtrl.getAllL2AclPolicies().stream().map(this::map).collect(Collectors.toList());
  }

  public L2AclPolicy map(L2AclPolicy l2AclPolicy) {
    return L2AclPolicyRestCtrl.L2AclPolicyMapper.INSTANCE.L2AclPolicy2ServiceL2AclPolicy(
            L2AclPolicyRestCtrl.L2AclPolicyMapper.INSTANCE.ServiceL2AclPolicy2L2AclPolicy(l2AclPolicy));
  }

  public com.ruckus.cloud.wifi.eda.viewmodel.L2AclPolicy mapViewModel(L2AclPolicy l2AclPolicy) {
    return L2AclPolicyRestCtrl.L2AclPolicyMapper.INSTANCE.ServiceL2AclPolicy2L2AclPolicy(l2AclPolicy);
  }

  private void assertCmnViewModelCollectorPolicies(OpType expectedOpType,
                                                   String expectedServiceProfileId, String serviceName, String tenantId, int scope) {
    List<Operations> viewModelOperations = receiveViewmodelCollectorOperations(1, tenantId).stream()
            .filter(o -> expectedServiceProfileId.equals(o.getId())).collect(Collectors.toList());
    Operations accessControlProfileIndexOps = viewModelOperations.stream()
      .filter(op -> op.getIndex().equals(EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME)).findFirst().get();
    assertEquals(expectedOpType, accessControlProfileIndexOps.getOpType());
    if (!accessControlProfileIndexOps.getOpType().equals(OpType.DEL)) {
      assertEquals(serviceName, accessControlProfileIndexOps.getDocMap().get(EsConstants.Key.NAME).getStringValue());
      assertEquals(scope, accessControlProfileIndexOps.getDocMap().get(EsConstants.Key.NETWORK_IDS).getListValue().getValuesCount());
    }
  }
}
