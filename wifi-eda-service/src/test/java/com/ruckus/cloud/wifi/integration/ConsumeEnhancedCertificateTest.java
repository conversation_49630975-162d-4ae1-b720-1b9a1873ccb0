package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.tenantca.protobuf.CertificateProto.Certificate;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.Objects;
import java.util.UUID;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeEnhancedCertificateTest {

  public static final String TEST_CERT_PEM = "test cert";
  public static final String TEST_CA_CHAIN_PEM = "test ca chain";

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  class whenConsumeEnhancedCertificate {

    @Test
    @FeatureFlag(enable = WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE)
    void thenSaveCertAndCaChain(Ap ap) {
      var requestId = randomTxId();
      messageUtil.sendEnhancedCertificate(
          ap.getTenant().getId(),
          ap.getId(),
          requestId,
          Certificate.newBuilder()
              .setId(ap.getId())
              .setCertPem(TEST_CERT_PEM)
              .setCacertPem(TEST_CA_CHAIN_PEM)
              .build());

      verifyDbContent(ap.getId());

      verifyDdccmMessaage(ap);
    }

    @Test
    @FeatureFlag(disable = WIFI_EDA_TLS_KEY_ENHANCED_MODE_CONFIG_TOGGLE)
    void thenSkipProcessCertAndCaChain(Ap ap) {
      messageUtil.sendEnhancedCertificate(
          ap.getTenant().getId(),
          ap.getId(),
          UUID.randomUUID().toString(),
          Certificate.newBuilder()
              .setId(ap.getId())
              .setCertPem(TEST_CERT_PEM)
              .setCacertPem(TEST_CA_CHAIN_PEM)
              .build());

      verifyDbColumnNotSave(ap.getId());

      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(ap.getTenant().getId());
    }

    private void verifyDdccmMessaage(Ap ap) {
      final var ddccmConfigRecord = messageCaptors.getDdccmMessageCaptor()
          .getValue(ap.getTenant().getId());
      assertThat(ddccmConfigRecord).isNotNull();
      assertThatNoException()
          .isThrownBy(
              () ->
                  assertThat(ddccmConfigRecord.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList)
                      .asList()
                      .isNotEmpty()
                      .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
                      .filteredOn(
                          com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApEnhancedCertInfo)
                      .filteredOn(op -> ap.getId().equals(op.getId()))
                      .hasSize(1)
                      .allMatch(op -> op.getAction() == Action.MODIFY)
                      .allSatisfy(
                          op ->
                              assertThat(op)
                                  .extracting(
                                      com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                                  .matches(commonInfo -> ap.getTenant().getId()
                                      .equals(commonInfo.getTenantId()))
                                  .matches(
                                      commonInfo ->
                                          commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                      .allSatisfy(
                          op -> {
                            assertThat(op)
                                .extracting(
                                    com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApEnhancedCertInfo)
                                .matches(certInfo -> ap.getId().contains(certInfo.getId()))
                                .matches(
                                    certInfo -> TEST_CERT_PEM.equals(certInfo.getCert())
                                        && TEST_CA_CHAIN_PEM.equals(certInfo.getCaChain()));
                          })
          );
    }

    private void verifyDbContent(String apId) {
      assertThat(repositoryUtil.find(Ap.class, apId)).isNotNull().matches(
          ap1 -> TEST_CERT_PEM.equals(ap1.getTlsEnhancedCertInfo().getCert())
                && TEST_CA_CHAIN_PEM.equals(ap1.getTlsEnhancedCertInfo().getCaChain()));
    }

    private void verifyDbColumnNotSave(String apId) {
      assertThat(repositoryUtil.find(Ap.class, apId)).isNotNull().matches(
          ap1 -> Objects.isNull(ap1.getTlsEnhancedCertInfo().getCert())
              && Objects.isNull(ap1.getTlsEnhancedCertInfo().getCaChain()));
    }
  }
}
