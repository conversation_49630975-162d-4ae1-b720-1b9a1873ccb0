package com.ruckus.cloud.wifi.requirement.feature;

import static org.apache.commons.lang3.RandomUtils.nextBoolean;

import com.ruckus.cloud.wifi.eda.servicemodel.FastRoamingOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class OverTheDsFeatureTest {

  @SpyBean
  private OverTheDsFeature unit;

  @Nested
  class WhenIsOverTheDsEnabled {

    @Test
    @FeatureFlag(disable = FlagNames.WIFI_OVER_THE_DS_FT_SUPPORT_TOGGLE)
    void givenFFDisabled() {
      final var network = new Network();
      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Nested
    @FeatureFlag(enable = FlagNames.WIFI_OVER_THE_DS_FT_SUPPORT_TOGGLE)
    class GivenFFEnabled {

      @Test
      void givenWlanIsNull() {
        final var network = new Network();
        network.setWlan(null);

        BDDAssertions.then(unit.test(network)).isFalse();
      }

      @Test
      void givenAdvancedCustomizationIsNull() {
        final var wlan = new Wlan();
        wlan.setAdvancedCustomization(null);
        final var network = new Network();
        network.setWlan(wlan);

        BDDAssertions.then(unit.test(network)).isFalse();
      }

      @Nested
      class GivenAdvancedCustomizationIsNotNull {

        @Test
        void givenEnableFastRoamingIsFalse() {
          final var customization = new WlanAdvancedCustomization();
          customization.setEnableFastRoaming(false);
          final var wlan = new Wlan();
          wlan.setAdvancedCustomization(customization);
          final var network = new Network();
          network.setWlan(wlan);

          BDDAssertions.then(unit.test(network)).isFalse();
        }

        @Test
        void thenReturnEnabled() {
          final var enabled = nextBoolean();
          final var customization = new WlanAdvancedCustomization();
          customization.setEnableFastRoaming(true);
          final var fastRoamingOptions = new FastRoamingOptions();
          fastRoamingOptions.setStatisticsOverDistributedSystemEnabled(enabled);
          customization.setFastRoamingOptions(fastRoamingOptions);
          final var wlan = new Wlan();
          wlan.setAdvancedCustomization(customization);
          final var network = new Network();
          network.setWlan(wlan);

          BDDAssertions.then(unit.test(network)).isEqualTo(enabled);
        }
      }
    }
  }
}
