package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatNoException;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.notification.dnb.gpb.Dnb.DNB;
import com.ruckus.cloud.notification.dnb.gpb.Dnb.TaskFactoryReset;
import com.ruckus.cloud.notification.service.gpb.DeviceNotification;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.client.viewmodel.ViewmodelClientGrpc;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApSubStateEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgent;
import com.ruckus.cloud.wifi.eda.servicemodel.ApSnmpAgentProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ScheduleTimeSlotTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
public class ConsumeDeleteApV1RequestTest extends AbstractRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private ViewmodelClientGrpc viewmodelClientGrpc;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  List<Ap> apList = new ArrayList<>();

  @BeforeEach
  void beforeEach(ApGroup apGroup) {
    repositoryUtil.createOrUpdate(apGroup);
    Ap ap = Generators.ap().generate();
    ap.setApGroup(apGroup);
    ap.setTenant(apGroup.getTenant());

    apList.add(repositoryUtil.createOrUpdate(ap));
  }

  @Test
  void deleteApWithFactoryReset() throws Exception {
    Ap ap = apList.get(0);
    final var tenantId = ap.getTenant().getId();
    final var userName = randomName();

    // Set the AP is operational
    mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.Operational);

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams()
        .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
        .addPathVariable("serialNumber", ap.getId());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_AP_V1, userName, requestParams, "");

    final var factoryResetMessage = messageCaptors.getDeviceNotificationMessageCaptor()
            .getValue(tenantId, delRequestId);
    assertThat(factoryResetMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(factoryResetMessage.getPayload())
            .extracting(DeviceNotification::getContent)
            .satisfies(assertDnb(ap.getId())));
    verifyOnlyApCmnMessage(tenantId, delRequestId, ap.getId());
  }

  @Test
  void deleteApWithoutFactoryReset() throws Exception {
    Ap ap = apList.get(0);
    final var tenantId = ap.getTenant().getId();
    final var userName = randomName();

    // Set the AP is operational
    mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.NeverContactedCloud);

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams()
        .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
        .addPathVariable("serialNumber", ap.getId());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_AP_V1, userName, requestParams, "");

    messageCaptors.getDeviceNotificationMessageCaptor().assertNotSentByTenant(tenantId);
    verifyOnlyApCmnMessage(tenantId, delRequestId, ap.getId());
  }

  @Test
  void deleteApWithApSnmpProfileBinding(ApSnmpAgentProfile apSnmpAgentProfile) throws Exception {
    Ap ap = apList.get(0);
    final var tenantId = ap.getTenant().getId();
    final var userName = randomName();
    var apSnmpAgent = new ApSnmpAgent();
    apSnmpAgent.setEnableApSnmp(true);
    apSnmpAgent.setApSnmpAgentProfile(apSnmpAgentProfile);
    ap.setApSnmpAgent(apSnmpAgent);
    repositoryUtil.createOrUpdate(ap);

    // Set the AP is operational
    mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.NeverContactedCloud);

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams()
        .addPathVariable("venueId", ap.getApGroup().getVenue().getId())
        .addPathVariable("serialNumber", ap.getId());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_AP_V1, userName, requestParams, "");

    messageCaptors.getDeviceNotificationMessageCaptor().assertNotSentByTenant(tenantId);
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, delRequestId);
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(delRequestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .hasSize(2)
        .satisfies(ops -> {
          Operations modOp = ops.get(0);
          assertThat(modOp.getOpType()).isEqualTo(OpType.MOD);
          assertThat(modOp.getId()).isEqualTo(apSnmpAgentProfile.getId());
          // ap delete
          Operations delOp = ops.get(1);
          assertThat(delOp.getOpType()).isEqualTo(OpType.DEL);
          assertThat(delOp.getId()).isEqualTo(ap.getId());
        });
  }

  ///TODO - Add more tests from ConsumeDeleteApRequestTest.java

  private void verifyOnlyApCmnMessage(String tenantId, String requestId, String apId) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSize(1)
        .allSatisfy(op -> {
          assertThat(op.getOpType()).isEqualTo(OpType.DEL);
          assertThat(op.getId()).isEqualTo(apId);
        });
  }

  private void mockOperationalAp(String serialNumber, String tenantId, ApSubStateEnum stateEnum) {
    List<ApDTO> apsList = new ArrayList<>();
    apsList.add(ApDTO.builder().serialNumber(serialNumber).subState(stateEnum)
        .build());
    doReturn(apsList).when(viewmodelClientGrpc).getApsBySerialNumber(anyString(),
        eq(tenantId), anySet());
  }

  private UpgradeSchedule createUpgradeSchedule(Venue venue, ApVersion apVersion) {
    ScheduleTimeSlot sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
    repositoryUtil.createOrUpdate(sts, venue.getTenant().getId(), randomTxId());
    UpgradeSchedule schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, apVersion, sts,
        c -> {});
    return repositoryUtil.createOrUpdate(schedule, venue.getTenant().getId(), randomTxId());
  }

  public static Consumer<ByteString> assertDnb(String serialNumber) {
    return byteString -> {
      try {
        assertThat(DNB.parseFrom(byteString))
            .extracting(DNB::getTaskFactoryReset)
            .extracting(TaskFactoryReset::getCallbackUrl)
            .matches(callback -> callback.contains(serialNumber));
      } catch (InvalidProtocolBufferException e) {
        throw new RuntimeException(e);
      }
    };
  }
}
