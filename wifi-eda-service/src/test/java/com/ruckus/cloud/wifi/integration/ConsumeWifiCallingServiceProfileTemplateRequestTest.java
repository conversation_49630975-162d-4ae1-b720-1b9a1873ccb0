package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.core.header.HttpHeaderName.RKS_IDM_USER_ID;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.defaultIdGenerator;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.rangeShort;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.google.common.collect.Lists;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiCallingPriorityEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanAdvancedCustomization;
import com.ruckus.acx.ddccm.protobuf.wifi.WlanVenue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.WifiCallingServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.QosPriorityEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfileV1_1;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.repository.DpskNetworkRepository;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.test.util.WifiCfgRequest;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Tag("WifiCallingServiceProfileTemplateTest")
@WifiIntegrationTest
public class ConsumeWifiCallingServiceProfileTemplateRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private DpskNetworkRepository dpskNetworkRepository;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE)
  class ConsumeAddWifiCallingServiceProfileTemplateRequestTest {

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile getPayload() {
      var wifiCallingServiceProfile = Generators.wifiCallingServiceProfile()
          .setId(nullValue(String.class)).generate();
      wifiCallingServiceProfile.setEPDGs(Generators.epdg().generate(1));

      return wifiCallingServiceProfile;
    }

    @Payload("withNetwork")
    private com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile getPayloadWithNetwork()
        throws ExecutionException, InterruptedException {
      var wifiCallingServiceProfile = Generators.wifiCallingServiceProfile()
          .setId(nullValue(String.class)).generate();
      wifiCallingServiceProfile.setEPDGs(Generators.epdg().generate(1));
      var networkRequest = addNetwork();
      wifiCallingServiceProfile.setNetworkIds(List.of(networkRequest.getId()));

      return wifiCallingServiceProfile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload) {
      validateResult(CfgAction.ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE, payload);
    }

    @Test
    @ApiAction(value = CfgAction.ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE, payload = @Payload("withNetwork"))
    void thenShouldHandleTheRequestWithNetworkSuccessfully(
        @Payload("withNetwork") com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload) {
      validateResult(CfgAction.ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1)
  class ConsumeAddWifiCallingServiceProfileTemplateV1_1RequestTest {

    @Payload
    private WifiCallingServiceProfileV1_1 getPayload() {
      WifiCallingServiceProfileV1_1 wifiCallingServiceProfileV1_1 = new WifiCallingServiceProfileV1_1();
      wifiCallingServiceProfileV1_1.setId(randomId());
      wifiCallingServiceProfileV1_1.setServiceName(randomName());
      wifiCallingServiceProfileV1_1.setDescription(randomName());
      wifiCallingServiceProfileV1_1.setQosPriority(QosPriorityEnum.WIFICALLING_PRI_BE);
      wifiCallingServiceProfileV1_1.setEPDGs(Generators.epdg().generate(1));
      return wifiCallingServiceProfileV1_1;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload WifiCallingServiceProfileV1_1 payload) {
      validateResult(CfgAction.ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE)
  class ConsumeUpdateWifiCallingServiceProfileTemplateRequestTest {

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile getPayload() {
      var wifiCallingServiceProfile = Generators.wifiCallingServiceProfile()
          .setId(nullValue(String.class)).generate();
      wifiCallingServiceProfile.setEPDGs(Generators.epdg().generate(1));

      return wifiCallingServiceProfile;
    }

    @Payload("withNetwork")
    private com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile getUpdatePayloadWithNetwork()
        throws ExecutionException, InterruptedException {
      var wifiCallingServiceProfile = Generators.wifiCallingServiceProfile()
          .setId(nullValue(String.class)).generate();
      wifiCallingServiceProfile.setEPDGs(Generators.epdg().generate(1));
      var networkRequest = addNetwork();
      wifiCallingServiceProfile.setNetworkIds(List.of(networkRequest.getId()));

      return wifiCallingServiceProfile;
    }

    private String wifiCallingServiceProfileTemplateId;

    @BeforeEach
    void givenOneWifiCallingServiceProfileTemplatePersistedInDb(@Template final WifiCallingServiceProfile wifiCallingServiceProfile) {
      wifiCallingServiceProfileTemplateId = wifiCallingServiceProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiCallingServiceProfileId",
        wifiCallingServiceProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload) {
      validateResult(CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE,
        wifiCallingServiceProfileTemplateId, payload);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE, payload = @Payload("withNetwork"))
    void thenShouldHandleTheRequestWithNetworkSuccessfully(
        @Payload("withNetwork") com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload) {
      validateResult(CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE,
        wifiCallingServiceProfileTemplateId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1)
  class ConsumeUpdateWifiCallingServiceProfileTemplateV1_1RequestTest {

    @Payload
    private WifiCallingServiceProfileV1_1 getPayload() {
      WifiCallingServiceProfileV1_1 wifiCallingServiceProfileV1_1 = new WifiCallingServiceProfileV1_1();
      wifiCallingServiceProfileV1_1.setId(randomId());
      wifiCallingServiceProfileV1_1.setServiceName(randomName());
      wifiCallingServiceProfileV1_1.setDescription(randomName());
      wifiCallingServiceProfileV1_1.setQosPriority(QosPriorityEnum.WIFICALLING_PRI_BE);
      wifiCallingServiceProfileV1_1.setEPDGs(Generators.epdg().generate(1));
      return wifiCallingServiceProfileV1_1;
    }

    private String wifiCallingServiceProfileTemplateId;

    @BeforeEach
    void givenOneWifiCallingServiceProfileTemplatePersistedInDb(@Template final WifiCallingServiceProfile wifiCallingServiceProfile) {
      wifiCallingServiceProfileTemplateId = wifiCallingServiceProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiCallingServiceProfileId",
        wifiCallingServiceProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload WifiCallingServiceProfileV1_1 payload) {
      validateResult(CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1,
        wifiCallingServiceProfileTemplateId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE)
  @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
  @FeatureRole("BETA-DPSK3")
  class ConsumeUpdateWifiCallingServiceProfileTemplateRequestTestWithDsaeNetwork {

    private String networkId;
    private String wifiCallingServiceProfileTemplateId;

    @BeforeEach
    void givenOneWifiCallingServiceProfileTemplatePersistedInDb(@Template Venue venue,
        @Template final WifiCallingServiceProfile wifiCallingServiceProfile) {
      wifiCallingServiceProfileTemplateId = wifiCallingServiceProfile.getId();

      // Add DSAE network
      var requestNetwork = Generators.dpskNetwork()
          .setName(serialName("AddDsaeNetwork"))
          .setDescription(randomString(64))
          .setDpskServiceProfileId(defaultIdGenerator())
          .setWlan(Generators.dpskWpa23MixedModeWlan())
          .generate();

      WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
          .tenantId(txCtxExtension.getTenantId())
          .requestId(randomTxId())
          .addHeader(RKS_IDM_USER_ID.getName(), randomName())
          .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
          .apiAction(CfgAction.ADD_NETWORK_TEMPLATE)
          .payload(requestNetwork).build();
      messageUtil.sendWifiCfgRequest(wifiCfgRequest);

      var dsaeNetwork = repositoryUtil.find(com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork.class,
          requestNetwork.getId(), txCtxExtension.getTenantId(), true);
      dsaeNetwork.getWlan().getAdvancedCustomization().setWifiCallingEnabled(true);
      dsaeNetwork.getWlan().getAdvancedCustomization()
          .setWifiCallingIds(List.of(wifiCallingServiceProfileTemplateId));

      //Bind network venue for DSAE network
      final var dsaeServiceNetworkActivationMapping = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
      dsaeServiceNetworkActivationMapping.setId(randomId()); // autoGenerated is true in wifi-api
      dsaeServiceNetworkActivationMapping.setNetworkId(dsaeNetwork.getId());
      dsaeServiceNetworkActivationMapping.setIsAllApGroups(true);
      dsaeServiceNetworkActivationMapping.setVenueId(venue.getId());
      dsaeServiceNetworkActivationMapping.setAllApGroupsVlanId(
          rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dsaeServiceNetworkActivationMapping.setAllApGroupsRadioTypes(
          List.of(RadioTypeEnum.values())); // All radio types
      sendCreateNetworkActivation(txCtxExtension.getTenantId(),
          List.of(dsaeServiceNetworkActivationMapping));

      networkId = dsaeNetwork.getId();
    }

    @Payload("withDsaeNetwork")
    private com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile getUpdatePayloadWithNetwork() {
      var wifiCallingServiceProfile = Generators.wifiCallingServiceProfile()
          .setId(nullValue(String.class)).generate();
      wifiCallingServiceProfile.setEPDGs(Generators.epdg().generate(1));
      wifiCallingServiceProfile.setNetworkIds(List.of(networkId));

      return wifiCallingServiceProfile;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiCallingServiceProfileId",
        wifiCallingServiceProfileTemplateId);
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE, payload = @Payload("withDsaeNetwork"))
    void thenShouldHandleTheRequestWithDsaeNetworkSuccessfully(
        @Payload("withDsaeNetwork") com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload) {
      validateDdccmCfgRequestMessages(CfgAction.UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE,
          List.of(wifiCallingServiceProfileTemplateId), payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE)
  @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
  @FeatureRole("BETA-DPSK3")
  class ConsumeActivateWifiCallingServiceProfileTemplateOnWifiNetworkTemplateRequestTestWithDsaeNetwork {
    private String wifiNetworkTemplateId;
    private String wifiCallingServiceProfileTemplateId;

    @BeforeEach
    void givenOneWifiCallingServiceProfileTemplatePersistedInDb(
        @Template Venue venue, @Template final WifiCallingServiceProfile wifiCallingServiceProfile) {
      wifiCallingServiceProfileTemplateId = wifiCallingServiceProfile.getId();

      // Add DSAE network
      var requestNetwork =
          Generators.dpskNetwork()
              .setName(serialName("AddDsaeNetwork"))
              .setDescription(randomString(64))
              .setDpskServiceProfileId(defaultIdGenerator())
              .setWlan(Generators.dpskWpa23MixedModeWlan())
              .generate();

      WifiCfgRequest wifiCfgRequest =
          WifiCfgRequest.builder()
              .tenantId(txCtxExtension.getTenantId())
              .requestId(randomTxId())
              .addHeader(RKS_IDM_USER_ID.getName(), randomName())
              .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
              .apiAction(CfgAction.ADD_NETWORK_TEMPLATE)
              .payload(requestNetwork)
              .build();
      messageUtil.sendWifiCfgRequest(wifiCfgRequest);

      var dsaeNetwork = repositoryUtil.find(com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork.class,
          requestNetwork.getId(), txCtxExtension.getTenantId(), true);
      // Bind network venue for DSAE network
      final var dsaeServiceNetworkActivationMapping =
          new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
      dsaeServiceNetworkActivationMapping.setId(randomId()); // autoGenerated is true in wifi-api
      dsaeServiceNetworkActivationMapping.setNetworkId(dsaeNetwork.getId());
      dsaeServiceNetworkActivationMapping.setIsAllApGroups(true);
      dsaeServiceNetworkActivationMapping.setVenueId(venue.getId());
      dsaeServiceNetworkActivationMapping.setAllApGroupsVlanId(
          rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dsaeServiceNetworkActivationMapping.setAllApGroupsRadioTypes(
          List.of(RadioTypeEnum.values())); // All radio types
      sendCreateNetworkActivation(
          txCtxExtension.getTenantId(), List.of(dsaeServiceNetworkActivationMapping));

      wifiNetworkTemplateId = dsaeNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId)
          .addPathVariable("wifiCallingServiceProfileId", wifiCallingServiceProfileTemplateId);
    }

    @Test
    @ApiAction(value = CfgAction.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE)
    void thenShouldHandleTheRequestWithDsaeNetworkSuccessfully() {
      validateDdccmCfgRequestMessagesForV2(
          CfgAction.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          List.of(wifiCallingServiceProfileTemplateId),
          null);
      validateCmnCfgCollectorMessagesForV2(
          CfgAction.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          List.of(wifiCallingServiceProfileTemplateId),
          null,
        wifiNetworkTemplateId);
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE)
  @FeatureFlag(enable = FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE)
  @FeatureRole("BETA-DPSK3")
  class ConsumeDeactivateWifiCallingServiceProfileTemplateOnWifiNetworkTemplateRequestTestWithDsaeNetwork {
    private String wifiNetworkTemplateId;
    private String wifiCallingServiceProfileTemplateId;

    @BeforeEach
    void givenOneWifiCallingServiceProfilePersistedInDb(
        @Template Venue venue, @Template final WifiCallingServiceProfile wifiCallingServiceProfile) {
      wifiCallingServiceProfileTemplateId = wifiCallingServiceProfile.getId();

      // Add DSAE network
      var requestNetwork =
          Generators.dpskNetwork()
              .setName(serialName("AddDsaeNetwork"))
              .setDescription(randomString(64))
              .setDpskServiceProfileId(defaultIdGenerator())
              .setWlan(Generators.dpskWpa23MixedModeWlan())
              .generate();

      WifiCfgRequest wifiCfgRequest =
          WifiCfgRequest.builder()
              .tenantId(txCtxExtension.getTenantId())
              .requestId(randomTxId())
              .addHeader(RKS_IDM_USER_ID.getName(), randomName())
              .addHeader(HttpHeaderName.RKS_FEATURE_ROLES.getName(), "BETA-DPSK3")
              .apiAction(CfgAction.ADD_NETWORK_TEMPLATE)
              .payload(requestNetwork)
              .build();
      messageUtil.sendWifiCfgRequest(wifiCfgRequest);

      var dsaeNetwork = repositoryUtil.find(com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork.class,
          requestNetwork.getId(), txCtxExtension.getTenantId(), true);
      dsaeNetwork.getWlan().getAdvancedCustomization().setWifiCallingEnabled(true);
      dsaeNetwork
          .getWlan()
          .getAdvancedCustomization()
          .setWifiCallingIds(List.of(wifiCallingServiceProfileTemplateId));

      log.warn("dsaeNetwork: {}, isTemplate: {}", dsaeNetwork, dsaeNetwork.getIsTemplate());

      // Bind network venue for DSAE network
      final var dsaeServiceNetworkActivationMapping =
          new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
      dsaeServiceNetworkActivationMapping.setId(randomId()); // autoGenerated is true in wifi-api
      dsaeServiceNetworkActivationMapping.setNetworkId(dsaeNetwork.getId());
      dsaeServiceNetworkActivationMapping.setIsAllApGroups(true);
      dsaeServiceNetworkActivationMapping.setVenueId(venue.getId());
      dsaeServiceNetworkActivationMapping.setAllApGroupsVlanId(
          rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      dsaeServiceNetworkActivationMapping.setAllApGroupsRadioTypes(
          List.of(RadioTypeEnum.values())); // All radio types
      sendCreateNetworkActivation(
          txCtxExtension.getTenantId(), List.of(dsaeServiceNetworkActivationMapping));

      wifiNetworkTemplateId = dsaeNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("wifiNetworkTemplateId", wifiNetworkTemplateId)
          .addPathVariable("wifiCallingServiceProfileId", wifiCallingServiceProfileTemplateId);
    }

    @Test
    @ApiAction(value = CfgAction.DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE)
    void thenShouldHandleTheRequestWithDsaeNetworkSuccessfully() {
      validateDdccmCfgRequestMessagesForV2(
          CfgAction.DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          List.of(wifiCallingServiceProfileTemplateId),
          null);
      validateCmnCfgCollectorMessagesForV2(
          CfgAction.DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          List.of(wifiCallingServiceProfileTemplateId),
          null,
        wifiNetworkTemplateId);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE)
  class ConsumeDeleteWifiCallingServiceProfileTemplateRequestTest {
    private String wifiCallingServiceProfileTemplateId;

    @BeforeEach
    void givenOneWifiCallingServiceProfileTemplatePersistedInDb(@Template final WifiCallingServiceProfile wifiCallingServiceProfile) {
      wifiCallingServiceProfileTemplateId = wifiCallingServiceProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiCallingServiceProfileId",
        wifiCallingServiceProfileTemplateId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateResult(
          CfgAction.DELETE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE,
        wifiCallingServiceProfileTemplateId,
          (com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile) null);
    }
  }

  @Nested
  @ApiAction(CfgAction.ADD_NETWORK_TEMPLATE)
  class ConsumeAddNetworkTemplateWithWifiCallingTemplateIdsRequestTest {

    private List<String> wifiCallingServiceProfileTemplateIds;

    private String venueTemplateId;

    @Payload
    private GuestNetwork getPayload() {
      var guestNetwork = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.ClickThrough,
          n -> {
            n.setPortalServiceProfileId(randomId());
            n.getWlan().getAdvancedCustomization().setWifiCallingEnabled(true);
            n.getWlan().getAdvancedCustomization()
                .setWifiCallingIds(this.wifiCallingServiceProfileTemplateIds);
          }
      );
      return guestNetwork;
    }

    @Payload("WifiCallingDisabled")
    private GuestNetwork getWifiCallingDisabledPayload() {
      var guestNetwork = GuestNetworkViewModelTestFixture.getGuestNetwork(
          GuestNetworkTypeEnum.ClickThrough,
          n -> {
            n.setPortalServiceProfileId(randomId());
            n.getWlan().getAdvancedCustomization().setWifiCallingEnabled(false);
            n.getWlan().getAdvancedCustomization()
                .setWifiCallingIds(this.wifiCallingServiceProfileTemplateIds);
          }
      );
      return guestNetwork;
    }

    @Payload("WithNetworkVenue")
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload()
            throws ExecutionException, InterruptedException {
      GuestNetwork network = addNetworkWithWifiCallingIds(wifiCallingServiceProfileTemplateIds, true);
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(network.getId());
      payload.setVenueId(venueTemplateId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      return payload;
    }

    @BeforeEach
    void givenOneWifiCallingServiceProfileTemplatePersistedInDb(
        @Template WifiCallingServiceProfile wifiCallingServiceProfile,
        @Template Venue venue) {
      this.wifiCallingServiceProfileTemplateIds = Lists.newArrayList(wifiCallingServiceProfile.getId());
      this.venueTemplateId = venue.getId();
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
            @Payload GuestNetwork payload) {
      var network = repositoryUtil
          .find(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, payload.getId());

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(txCtxExtension.getTenantId()))
              .matches(msg -> msg.getRequestId().equals(txCtxExtension.getRequestId()))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
              .hasSize(1)
              .allMatch(op -> wifiCallingServiceProfileTemplateIds.contains(op.getId()))
              .allMatch(op -> op.getOpType() == OpType.MOD)
              .allSatisfy(op -> {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> network.getId().equals(doc.get(Key.NETWORK_IDS)
                        .getListValue().getValues(0).getStringValue()));
                }));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_TEMPLATE, payload = @Payload("WithNetworkVenue"))
    void thenShouldHandleWifiCallingTemplateWithNetworkVenueTemplateTheRequestSuccessfully(
            @Payload com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {

      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

      assertThat(ddccmCfgRequestMessage).isNotNull()
              .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
              .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
              .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
              assertThat(ddccmCfgRequestMessage.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList).asList()
                      .isNotEmpty()
                      .extracting(Operation.class::cast)
                      .filteredOn(Operation::hasWlanVenue)
                      .hasSize(1)
                      .allMatch(op -> wifiCallingServiceProfileTemplateIds.get(0).equals(op.getWlanVenue().getAdvancedCustomization().getWifiCallingPolicyIds(0))));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_TEMPLATE, payload = @Payload("WifiCallingDisabled"))
    void thenShouldHandleWifiCallingTemplateDisabledTheRequestSuccessfully(
        @Payload("WifiCallingDisabled") GuestNetwork payload) {
      var network = repositoryUtil
          .find(com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork.class, payload.getId());

      assertThat(network.getWifiCallingServiceProfileNetworks())
          .hasSize(1)
          .allMatch(wcspn ->
              wifiCallingServiceProfileTemplateIds.contains(wcspn.getWifiCallingServiceProfile().getId()));

      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .matches(msg -> msg.getTenantId().equals(txCtxExtension.getTenantId()))
              .matches(msg -> msg.getRequestId().equals(txCtxExtension.getRequestId()))
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .extracting(Operations.class::cast)
              .filteredOn(op -> Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
              .hasSize(1)
              .allMatch(op -> wifiCallingServiceProfileTemplateIds.contains(op.getId()))
              .allMatch(op -> op.getOpType() == OpType.MOD)
              .allSatisfy(op -> {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> doc.get(Key.NETWORK_IDS)
                        .getListValue().getValuesCount() == 0);
              }));
    }
  }

  @Nested
  @ApiAction(CfgAction.ADD_NETWORK_VENUE_TEMPLATE)
  class ConsumeAddAndUpdateWithNewNetworkTemplateForWifiCallingServiceProfileTemplateTest {

    private List<String> wifiCallingServiceProfileTemplateIds;

    private String venueTemplateId;

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload()
            throws ExecutionException, InterruptedException {
      GuestNetwork network = addNetworkWithWifiCallingIds(wifiCallingServiceProfileTemplateIds, true);
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(network.getId());
      payload.setVenueId(venueTemplateId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      return payload;
    }

    @BeforeEach
    void givenOneWifiCallingServiceProfileTemplatePersistedInDb(
            @Template WifiCallingServiceProfile wifiCallingServiceProfile,
            @Template Venue venue) {
      this.wifiCallingServiceProfileTemplateIds = Lists.newArrayList(wifiCallingServiceProfile.getId());
      this.venueTemplateId = venue.getId();
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_TEMPLATE)
    void thenShouldHandleWifiCallingTemplateWithNetworkVenueTemplateRequestSuccessfully() {

      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

      assertThat(ddccmCfgRequestMessage).isNotNull()
              .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
              .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
              .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
              assertThat(ddccmCfgRequestMessage.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList).asList()
                      .isNotEmpty()
                      .extracting(Operation.class::cast)
                      .filteredOn(Operation::hasWlanVenue)
                      .hasSize(1)
                      .allMatch(op -> wifiCallingServiceProfileTemplateIds.get(0).equals(op.getWlanVenue().getAdvancedCustomization().getWifiCallingPolicyIds(0))));
    }
  }

  @Nested
  @ApiAction(value = CfgAction.DELETE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE)
  class ConsumeDeleteWifiCallingServiceProfileTemplateWithNetworkBindingTest {

    private String wifiCallingServiceProfileTemplateIdToDelete;
    private List<String> wifiCallingServiceProfileTemplateIds;

    private String venueTemplateId;

    private String networkVenueTemplateId;

    @Payload("NetworkVenueBinding")
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload()
            throws ExecutionException, InterruptedException {
      GuestNetwork network = addNetworkWithWifiCallingIds(wifiCallingServiceProfileTemplateIds, false);
      final var payload = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue();
      payload.setId(randomId()); // autoGenerated is true in wifi-api
      payload.setNetworkId(network.getId());
      payload.setVenueId(venueTemplateId);
      payload.setIsAllApGroups(true);
      payload.setAllApGroupsVlanId(rangeShort((short) 1, (short) 4094).setRandom(true).generate());
      payload.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radio types
      this.networkVenueTemplateId = payload.getId();
      return payload;
    }

    @BeforeEach
    void givenOneWifiCallingServiceProfileTemplatePersistedInDb(
            @Template WifiCallingServiceProfile wifiCallingServiceProfile,
            @Template Venue venue) {
      this.wifiCallingServiceProfileTemplateIdToDelete = wifiCallingServiceProfile.getId();
      this.wifiCallingServiceProfileTemplateIds = Lists.newArrayList(wifiCallingServiceProfile.getId());
      this.venueTemplateId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("wifiCallingServiceProfileId",
        wifiCallingServiceProfileTemplateIdToDelete);
    }

    @Test
    void thenShouldHandleDeleteWifiCallingRequestSuccessfully() {
      final WifiCallingServiceProfile wifiCallingServiceProfile = repositoryUtil
        .find(WifiCallingServiceProfile.class, wifiCallingServiceProfileTemplateIds.get(0));
      assertThat(wifiCallingServiceProfile).isNull();

      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

      assertThat(ddccmCfgRequestMessage).isNotNull()
              .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
              .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
              .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
              assertThat(ddccmCfgRequestMessage.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList).asList()
                      .isNotEmpty()
                      .extracting(Operation.class::cast)
                      .filteredOn(Operation::hasWlanVenue)
                      .hasSize(0));

      assertThatNoException().isThrownBy(() ->
              assertThat(ddccmCfgRequestMessage.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList).asList()
                      .isNotEmpty()
                      .extracting(Operation.class::cast)
                      .filteredOn(Operation::hasWifiCalling)
                      .filteredOn(op -> op.getAction().equals(Action.DELETE))
                      .hasSize(1));
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK_VENUE_TEMPLATE, payload = @Payload("NetworkVenueBinding"))
    void thenShouldHandleWifiCallingTemplateWithNetworkVenueTemplateRequestSuccessfully() {

      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

      assertThat(ddccmCfgRequestMessage).isNotNull()
              .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
              .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
              .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
              assertThat(ddccmCfgRequestMessage.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList).asList()
                      .isNotEmpty()
                      .extracting(Operation.class::cast)
                      .filteredOn(Operation::hasWlanVenue)
                      .hasSize(1)
                      .extracting(Operation::getWlanVenue)
                      .extracting(WlanVenue::getAdvancedCustomization)
                      .filteredOn(WlanAdvancedCustomization::getWifiCallingPolicyEnabled)
                      .isEmpty());
    }
  }

  private void validateNothingHappened(CfgAction apiAction) {
    validateRepositoryData(null, null, apiAction);

    validateCmnCfgCollectorMessages(null, null, null);
    validateActivityMessages(null, null);
    validateDdccmCfgRequestMessages(null, null, null);
  }

  private void validateResult(CfgAction apiAction,
      com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges = revisionService.changes(requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    final String wifiCallingServiceProfileId = txChanges.getNewEntities().stream()
        .filter(txEntity -> txEntity.getEntity() instanceof WifiCallingServiceProfile)
        .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
    validateResult(apiAction, wifiCallingServiceProfileId, payload);
  }

  private void validateResult(CfgAction apiAction, String wifiCallingServiceProfileId,
      com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload) {
    validateRepositoryData(wifiCallingServiceProfileId, payload, apiAction);
    validateCmnCfgCollectorMessages(apiAction, List.of(wifiCallingServiceProfileId), payload);
    validateActivityMessages(apiAction, List.of(wifiCallingServiceProfileId));
    validateDdccmCfgRequestMessages(apiAction, List.of(wifiCallingServiceProfileId), payload);
  }

  private void validateRepositoryData(String wifiCallingServiceProfileId,
      com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload, CfgAction apiAction) {
    if (wifiCallingServiceProfileId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final WifiCallingServiceProfile wifiCallingServiceProfile = repositoryUtil
        .find(WifiCallingServiceProfile.class, wifiCallingServiceProfileId);

    if (payload == null) {
      assertThat(wifiCallingServiceProfile).isNull();
      return;
    }

    assertThat(wifiCallingServiceProfile)
        .isNotNull()
        .matches(wcsp -> Objects.equals(wcsp.getId(), wifiCallingServiceProfileId))
        .matches(wcsp -> Objects.equals(wcsp.getIsTemplate(), Boolean.TRUE.booleanValue()))
        .matches(wcsp -> Objects.equals(wcsp.getServiceName(), payload.getServiceName()))
        .matches(wcsp -> Objects.equals(wcsp.getDescription(), payload.getDescription()))
        .matches(wcsp -> Objects.equals(wcsp.getQosPriority(), payload.getQosPriority()))
        .matches(wcsp -> Objects.equals(wcsp.getEPDGs().size(), payload.getEPDGs().size()))
        .matches(wcsp -> CollectionUtils.isEmpty(payload.getNetworkIds()) ||
            wcsp.getWifiCallingServiceProfileNetwork().stream()
                .map(wcspn -> wcspn.getNetwork().getId())
                .collect(Collectors.toList())
                .contains(payload.getNetworkIds().get(0))
            );
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction,
      List<String> wifiCallingServiceProfileIds,
      com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload) {
    if (apiAction == null || wifiCallingServiceProfileIds == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operations.class::cast)
            .filteredOn(op -> Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(wifiCallingServiceProfileIds.size())
            .allMatch(op -> wifiCallingServiceProfileIds.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction))
            .allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> wifiCallingServiceProfileIds.contains(doc.get(Key.ID).getStringValue()))
                    .matches(doc -> Boolean.TRUE.equals(doc.get(Key.IS_TEMPLATE).getBoolValue()))
                    .matches(doc -> Objects.equals(payload.getQosPriority().toString(),
                        doc.get(Key.QOS_PRIORITY).getStringValue()))
                    .matches(doc -> Objects.equals(doc.get(Key.EPDGS).getListValue().getValues(0)
                            .getStructValue().getFieldsMap().get(Key.EPDGS_DOMAIN).getStringValue(),
                        payload.getEPDGs().get(0).getDomain()))
                    .matches(doc -> CollectionUtils.isEmpty(payload.getNetworkIds())  ||
                            payload.getNetworkIds().contains(doc.get(Key.NETWORK_IDS)
                                .getListValue().getValues(0).getStringValue()));
              }
            }));
  }

  private void validateActivityMessages(CfgAction apiAction, List<String> entityIds) {
    if (apiAction == null) {
      messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);;
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    if (apiAction.equals(CfgAction.ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE)) {
      assertThat(activityCfgChangeRespMessage.getPayload().getEntityIdList())
          .containsAll(entityIds);
    }

    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(tenantId);  }

  private void validateDdccmCfgRequestMessages(CfgAction apiAction, List<String> wifiCallingServiceProfileIds,
      com.ruckus.cloud.wifi.eda.viewmodel.WifiCallingServiceProfile payload) {
    if (apiAction == null || wifiCallingServiceProfileIds == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(Operation.class::cast)
            .filteredOn(Operation::hasWifiCalling)
            .hasSize(wifiCallingServiceProfileIds.size())
            .allMatch(op -> wifiCallingServiceProfileIds.contains(op.getWifiCalling().getId()))
            .allMatch(op -> op.getAction() == action(apiAction))
            .allSatisfy(op -> assertThat(op)
                .extracting(Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op)
                    .extracting(Operation::getWifiCalling)
                    .matches(vp -> wifiCallingServiceProfileIds.contains(vp.getId()));
              } else {
                assertThat(payload).isNotNull();
                assertThat(op)
                    .extracting(Operation::getWifiCalling)
                    .matches(wcsp -> wifiCallingServiceProfileIds.contains(wcsp.getId()))
                    .matches(wcsp -> wcsp.getQosPriority().equals(WifiCallingPriorityEnum.BEST_EFFORT))
                    .matches(wcsp -> payload.getServiceName().equals(wcsp.getName()))
                    .matches(wcsp -> payload.getEPDGs().get(0).getDomain().equals(wcsp.getEPDGList().get(0).getDomain()));
              }
            }));
  }

  private void validateResult(CfgAction apiAction, WifiCallingServiceProfileV1_1 payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges =
        revisionService.changes(
            requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    final String wifiCallingServiceProfileId =
        txChanges.getNewEntities().stream()
            .filter(txEntity -> txEntity.getEntity() instanceof WifiCallingServiceProfile)
            .map(TxEntity::getId)
            .findAny()
            .orElseGet(Assertions::fail);
    validateResult(apiAction, wifiCallingServiceProfileId, payload);
  }

  private void validateResult(
      CfgAction apiAction,
      String wifiCallingServiceProfileId,
      WifiCallingServiceProfileV1_1 payload) {
    validateRepositoryDataForV2(wifiCallingServiceProfileId, payload, apiAction);
    validateCmnCfgCollectorMessagesForV2(
        apiAction, List.of(wifiCallingServiceProfileId), payload, null);
    validateActivityMessages(apiAction, List.of(wifiCallingServiceProfileId));
    validateDdccmCfgRequestMessagesForV2(apiAction, List.of(wifiCallingServiceProfileId), payload);
  }

  private void validateRepositoryDataForV2(
      String wifiCallingServiceProfileId,
      WifiCallingServiceProfileV1_1 payload,
      CfgAction apiAction) {
    if (wifiCallingServiceProfileId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final WifiCallingServiceProfile wifiCallingServiceProfile =
        repositoryUtil.find(WifiCallingServiceProfile.class, wifiCallingServiceProfileId);

    if (payload == null) {
      assertThat(wifiCallingServiceProfile).isNull();
      return;
    }

    assertThat(wifiCallingServiceProfile)
        .isNotNull()
        .matches(wcsp -> Objects.equals(wcsp.getId(), wifiCallingServiceProfileId))
        .matches(wcsp -> Objects.equals(wcsp.getIsTemplate(), Boolean.TRUE.booleanValue()))
        .matches(wcsp -> Objects.equals(wcsp.getServiceName(), payload.getServiceName()))
        .matches(wcsp -> Objects.equals(wcsp.getDescription(), payload.getDescription()))
        .matches(wcsp -> Objects.equals(wcsp.getQosPriority(), payload.getQosPriority()))
        .matches(wcsp -> Objects.equals(wcsp.getEPDGs().size(), payload.getEPDGs().size()));
  }

  private void validateCmnCfgCollectorMessagesForV2(
      CfgAction apiAction,
      List<String> wifiCallingServiceProfileIds,
      WifiCallingServiceProfileV1_1 payload,
      String wifiNetworkId) {
    if (apiAction == null || wifiCallingServiceProfileIds == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage =
        messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(cmnCfgCollectorMessage.getPayload())
                    .matches(msg -> msg.getTenantId().equals(tenantId))
                    .matches(msg -> msg.getRequestId().equals(requestId))
                    .extracting(ViewmodelCollector::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(Operations.class::cast)
                    .filteredOn(
                        op -> Index.WIFI_CALLING_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
                    .hasSize(wifiCallingServiceProfileIds.size())
                    .allMatch(op -> wifiCallingServiceProfileIds.contains(op.getId()))
                    .allMatch(op -> op.getOpType() == opType(apiAction))
                    .allSatisfy(
                        op -> {
                          if (op.getOpType() != OpType.DEL && payload != null) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(
                                    doc ->
                                        wifiCallingServiceProfileIds.contains(
                                            doc.get(Key.ID).getStringValue()))
                                .matches(
                                    doc ->
                                        Objects.equals(
                                            payload.getQosPriority().toString(),
                                            doc.get(Key.QOS_PRIORITY).getStringValue()))
                                .matches(
                                    doc ->
                                        Objects.equals(
                                            doc.get(Key.EPDGS)
                                                .getListValue()
                                                .getValues(0)
                                                .getStructValue()
                                                .getFieldsMap()
                                                .get(Key.EPDGS_DOMAIN)
                                                .getStringValue(),
                                            payload.getEPDGs().get(0).getDomain()));
                          } else if (payload == null) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(
                                    doc ->
                                        wifiCallingServiceProfileIds.contains(
                                            doc.get(Key.ID).getStringValue()))
                                .matches(
                                    doc ->
                                        wifiNetworkId.contains(
                                            doc.get(Key.NETWORK_IDS).getStringValue()));
                          }
                        }));
  }

  private void validateDdccmCfgRequestMessagesForV2(
      CfgAction apiAction,
      List<String> wifiCallingServiceProfileIds,
      WifiCallingServiceProfileV1_1 payload) {
    if (apiAction == null || wifiCallingServiceProfileIds == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(ddccmCfgRequestMessage.getPayload())
                    .extracting(WifiConfigRequest::getOperationsList)
                    .asList()
                    .isNotEmpty()
                    .extracting(Operation.class::cast)
                    .filteredOn(Operation::hasWifiCalling)
                    .hasSize(wifiCallingServiceProfileIds.size())
                    .allMatch(
                        op -> wifiCallingServiceProfileIds.contains(op.getWifiCalling().getId()))
                    .allMatch(op -> op.getAction() == action(apiAction))
                    .allSatisfy(
                        op ->
                            assertThat(op)
                                .extracting(
                                    Operation::getCommonInfo)
                                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                                .matches(
                                    commonInfo ->
                                        commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                    .allSatisfy(
                        op -> {
                          if (op.getAction() == Action.DELETE) {
                            assertThat(op)
                                .extracting(
                                    Operation::getWifiCalling)
                                .matches(vp -> wifiCallingServiceProfileIds.contains(vp.getId()));
                          } else if (payload == null) {
                            assertThat(op)
                                .extracting(
                                    Operation::getWifiCalling)
                                .matches(
                                    wcsp -> wifiCallingServiceProfileIds.contains(wcsp.getId()))
                                .matches(
                                    wcsp ->
                                        wcsp.getQosPriority()
                                            .equals(WifiCallingPriorityEnum.BEST_EFFORT));
                          } else {
                            assertThat(payload).isNotNull();
                            assertThat(op)
                                .extracting(
                                    Operation::getWifiCalling)
                                .matches(
                                    wcsp -> wifiCallingServiceProfileIds.contains(wcsp.getId()))
                                .matches(
                                    wcsp ->
                                        wcsp.getQosPriority()
                                            .equals(WifiCallingPriorityEnum.BEST_EFFORT))
                                .matches(wcsp -> payload.getServiceName().equals(wcsp.getName()));
                          }
                        }));
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE, ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1 -> ApiFlowNames
          .ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE;
      case UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE,
          UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1 -> ApiFlowNames
          .UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE;
      case DELETE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE -> ApiFlowNames.DELETE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE;
      case ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE -> ApiFlowNames
          .ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE;
      case DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE -> ApiFlowNames
          .DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE, ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1 -> OpType.ADD;
      case UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE,
          UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1,
          ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE -> OpType.MOD;
      case DELETE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE, ADD_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1 -> Action.ADD;
      case UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE,
          UPDATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_V1_1,
          ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
          DEACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE -> Action.MODIFY;
      case DELETE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private GuestNetwork addNetwork() throws ExecutionException, InterruptedException {
    var userName = randomName();
    var requestIdAdd = txCtxExtension.getRequestId();
    var tenantId = txCtxExtension.getTenantId();
    var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough,
        n -> {
          n.setPortalServiceProfileId(randomId());
          n.getWlan().getAdvancedCustomization().setWifiCallingEnabled(true);
        });

    // Add Network
    messageUtil.sendWifiCfgRequest(
        tenantId,
        requestIdAdd,
        CfgAction.ADD_NETWORK_TEMPLATE,
        userName,
        networkRequest);
    messageUtil.clearMessage();
    return networkRequest;
  }

  private GuestNetwork addNetworkWithWifiCallingIds(List<String> wifiCallingServiceProfileIds, boolean enabled) throws ExecutionException, InterruptedException {
    var userName = randomName();
    var requestIdAdd = txCtxExtension.getRequestId();
    var tenantId = txCtxExtension.getTenantId();
    var networkRequest = GuestNetworkViewModelTestFixture.getGuestNetwork(
            GuestNetworkTypeEnum.ClickThrough,
            n -> {
              n.setPortalServiceProfileId(randomId());
              n.getWlan().getAdvancedCustomization().setWifiCallingEnabled(enabled);
              n.getWlan().getAdvancedCustomization().setWifiCallingIds(wifiCallingServiceProfileIds);
            });

    // Add Network
    messageUtil.sendWifiCfgRequest(
            tenantId,
            requestIdAdd,
            CfgAction.ADD_NETWORK_TEMPLATE,
            userName,
            networkRequest);
    messageUtil.clearMessage();
    return networkRequest;
  }

  private void sendCreateNetworkActivation(String tenantId,
      List<com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping> networkVenues) {
    WifiCfgRequest wifiCfgRequest = WifiCfgRequest.builder()
        .tenantId(tenantId)
        .requestId(randomTxId())
        .addHeader(RKS_IDM_USER_ID.getName(), randomName())
        .apiAction(CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS)
        .payload(networkVenues).build();
    messageUtil.sendWifiCfgRequest(wifiCfgRequest);
  }

}
