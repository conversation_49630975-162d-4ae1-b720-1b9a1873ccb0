package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.InstanceOfAssertFactories.list;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.entitlement.Action;
import com.ruckus.cloud.entitlement.ProtoDeviceType;
import com.ruckus.cloud.entitlement.ProtoRequestSource;
import com.ruckus.cloud.entitlement.operation.eda.EdaOperation;
import com.ruckus.cloud.entitlement.operation.eda.EntOperationsEDARequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
public class ConsumeAddApsRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  class whenConsumeAddApsRequest {

    @Test
    void thenSaveAps(ApGroup apGroup) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var apRequests =
          Stream.generate(ApRequest::new)
              .peek(a -> a.setSerialNumber(randomSerialNumber()))
              .peek(a -> a.setApGroupId(apGroup.getId()))
              .peek(a -> a.setVenueId(apGroup.getVenue().getId()))
              .peek(a -> a.setName(randomName()))
              .limit(3)
              .collect(Collectors.toList());

      messageUtil.sendWifiCfgRequest(
          apGroup.getTenant().getId(),
          requestId,
          CfgAction.ADD_APS,
          userName,
          apRequests);

      for (final var apRequest : apRequests) {
        validateResult(apRequest);
      }
      validateAggregatedResult(apGroup.getTenant().getId(), requestId, apRequests, apGroup);
    }

    @Nested
    class givenRevisionExists {

      @Test
      void thenSendMessageAsWell(ApGroup apGroup) {
        final var userName = randomName();

        for (int i = 0; i < 3; i++) {
          final var requestId = randomTxId();
          final var apRequests =
              Stream.generate(ApRequest::new)
                  .peek(a -> a.setSerialNumber(randomSerialNumber()))
                  .peek(a -> a.setApGroupId(apGroup.getId()))
                  .peek(a -> a.setVenueId(apGroup.getVenue().getId()))
                  .peek(a -> a.setName(randomName()))
                  .limit(3)
                  .collect(Collectors.toList());
          messageUtil.sendWifiCfgRequest(
              apGroup.getTenant().getId(),
              requestId,
              CfgAction.ADD_APS,
              userName,
              apRequests);

          for (final var apRequest : apRequests) {
            validateResult(apRequest);
          }
          validateAggregatedResult(apGroup.getTenant().getId(), requestId, apRequests, apGroup);
        }
      }
    }

    void validateResult(ApRequest apRequest) {
      final var ap = repositoryUtil.find(Ap.class, apRequest.getSerialNumber());

      assertThat(ap)
          .isNotNull()
          .matches(a -> Objects.equals(a.getId(), apRequest.getSerialNumber()))
          .matches(a -> Objects.equals(a.getName(), apRequest.getName()))
          .extracting(Ap::getApGroup)
          .isNotNull()
          .matches(g -> Objects.equals(g.getId(), apRequest.getApGroupId()))
          .extracting(ApGroup::getVenue)
          .isNotNull()
          .matches(v -> Objects.equals(v.getId(), apRequest.getVenueId()));
    }

    void validateAggregatedResult(String tenantId, String requestId, List<ApRequest> apRequests,
        ApGroup targetApGroup) {
      final var apSerialNumbers = apRequests.stream().map(ApRequest::getSerialNumber).toList();
      assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
          .getValue(tenantId, requestId)).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> assertSoftly(softly -> {
            softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
            softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
          }))
          .extracting(ViewmodelCollector::getOperationsList, list(Operations.class))
          .isNotEmpty().hasSize(apRequests.size())
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
                .isNotEmpty().hasSize(apRequests.size())
                .allSatisfy(op -> assertThat(op.getOpType()).isEqualTo(OpType.ADD))
                .extracting(Operations::getId)
                .containsExactlyInAnyOrderElementsOf(apSerialNumbers);
          });

      assertThat(messageCaptors.getEntitlementDeviceOperationMessageCaptor()
          .getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_TENANT_ID, tenantId))
          .satisfies(assertHeader(WifiCommonHeader.WIFI_DEVICE_TYPE, ProtoDeviceType.WIFI.name()))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> assertSoftly(softly -> {
            softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
            softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
            softly.assertThat(msg.getRequestSource()).isEqualTo(ProtoRequestSource.WIFI_SERVICE);
          }))
          .extracting(EntOperationsEDARequest::getOperationList, list(EdaOperation.class))
          .isNotEmpty().hasSize(apRequests.size())
          .allSatisfy(op -> {
            assertThat(op.getAction()).isEqualTo(Action.CONSUME);
            assertThat(op.getDevice().getDeviceType()).isEqualTo(ProtoDeviceType.WIFI);
          })
          .extracting(op -> op.getDevice().getSerialNumber())
          .containsExactlyInAnyOrderElementsOf(apSerialNumbers);

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId)).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> assertSoftly(softly -> {
            softly.assertThat(msg.getStatus()).isEqualTo(Status.OK);
            softly.assertThat(msg.getStep()).isEqualTo(ApiFlowNames.ADD_APS);
            softly.assertThat(msg.getEventDate()).isNotNull();
          }));

      for (var apRequest : apRequests) {
        assertThat(messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
            .getValue(tenantId, requestId)).isNotNull()
            .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
            .extracting(KafkaProtoMessage::getPayload).isNotNull()
            .satisfies(msg -> assertSoftly(softly -> {
              softly.assertThat(msg.getId()).isIn(apSerialNumbers);
              softly.assertThat(msg.getSerial()).isIn(apSerialNumbers);
              softly.assertThat(msg.getSerial()).isEqualTo(msg.getId());
              softly.assertThat(msg.getGroupId()).isEqualTo(apRequest.getApGroupId());
              softly.assertThat(msg.getVenueId()).isEqualTo(apRequest.getVenueId());
              softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
            }));
      }

      messageCaptors.getActivityImpactDeviceMessageCaptor()
          .assertNotSentByTenant(tenantId);
    }
  }
}
