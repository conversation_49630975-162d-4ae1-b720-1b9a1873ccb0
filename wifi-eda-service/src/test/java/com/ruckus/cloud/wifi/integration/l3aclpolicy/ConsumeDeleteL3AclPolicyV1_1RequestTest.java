package com.ruckus.cloud.wifi.integration.l3aclpolicy;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.exception.ObjectInUseException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.stream.Stream;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeDeleteL3AclPolicyV1_1RequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeDeleteL3AclPolicyV1_1Message {
    @Test
    void givenPolicyNotExists(Tenant tenant) {
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          txCtxExtension.getRequestId(),
          CfgAction.DELETE_L3ACL_POLICY_V1_1,
          randomName(),
          new RequestParams().addPathVariable("l3AclPolicyId", randomId()),
          "");

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
          .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_L3ACL_POLICY));
    }

    @Nested
    class GivenPolicyExists {
      private String l3AclPolicyId;

      @BeforeEach
      void beforeEach(L3AclPolicy l3AclPolicy) {
        l3AclPolicyId = l3AclPolicy.getId();
      }

      private RequestParams requestParams() {
        return new RequestParams().addPathVariable("l3AclPolicyId", l3AclPolicyId);
      }

      @Test
      void givenPolicyAlreadyActivatedOnNetwork(
          Tenant tenant, L3AclPolicy l3AclPolicy, Network network) {
        network.getWlan().getAdvancedCustomization().setL3AclEnable(true);
        network.getWlan().getAdvancedCustomization().setL3AclPolicy(l3AclPolicy);
        repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

        assertThatThrownBy(
                () ->
                    messageUtil.sendWifiCfgRequest(
                        tenant.getId(),
                        txCtxExtension.getRequestId(),
                        CfgAction.DELETE_L3ACL_POLICY_V1_1,
                        randomName(),
                        requestParams(),
                        ""))
            .isNotNull()
            .getRootCause()
            .isInstanceOf(ObjectInUseException.class);

        messageCaptors.assertThat(
            messageCaptors.getDdccmMessageCaptor(),
            messageCaptors.getCmnCfgCollectorMessageCaptor()
        ).doesNotSendByTenant(tenant.getId());

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
            .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_L3ACL_POLICY));
      }

      @Nested
      class GivenPolicyNotActivatedOnNetwork {
        @Test
        void givenPolicyAlreadyActivatedOnSomeAccessControlProfiles(
            Tenant tenant, L3AclPolicy l3AclPolicy) {
          final var accessControlProfileIds =
              Stream.generate(AccessControlProfileTestFixture::randomAccessControlProfile)
                  .limit(3)
                  .peek(p -> p.setTenant(tenant))
                  .peek(p -> p.setL3AclPolicy(l3AclPolicy))
                  .peek(p -> p.setL3AclEnable(true))
                  .peek(p -> repositoryUtil.createOrUpdate(p, tenant.getId(), randomTxId()))
                  .map(AbstractBaseEntity::getId)
                  .toList();

          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.DELETE_L3ACL_POLICY_V1_1,
              randomName(),
              requestParams(),
              "");

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .hasSize(accessControlProfileIds.size() + 1)
              .satisfies(
                  operations ->
                      assertThat(operations)
                          .filteredOn(o -> o.getAction() == Action.MODIFY)
                          .hasSize(accessControlProfileIds.size())
                          .allMatch(
                              o ->
                                  !o.getCcmFirewallProfile()
                                      .getFirewallL3AccessControlPolicy()
                                      .hasL3AccessControlPolicyId())
                          .extracting(Operation::getId)
                          .containsExactlyInAnyOrderElementsOf(accessControlProfileIds))
              .filteredOn(o -> o.getAction() == Action.DELETE)
              .singleElement()
              .extracting(Operation::getId)
              .isEqualTo(l3AclPolicyId);

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .hasSize(accessControlProfileIds.size() + 1)
              .satisfies(
                  operations ->
                      assertThat(operations)
                          .filteredOn(o -> o.getOpType() == OpType.MOD)
                          .hasSize(3)
                          .allMatch(
                              o ->
                                  o.getDocOrDefault(
                                          EsConstants.Key.L3_ACL_POLICY_ID,
                                          Value.getDefaultInstance())
                                      .getStringValue()
                                      .isEmpty())
                          .extracting(Operations::getId)
                          .containsExactlyInAnyOrderElementsOf(accessControlProfileIds))
              .filteredOn(o -> o.getOpType() == OpType.DEL)
              .singleElement()
              .extracting(Operations::getId)
              .isEqualTo(l3AclPolicyId);

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_L3ACL_POLICY));
        }

        @Test
        void givenPolicyNotActivatedOnAccessControlProfile(Tenant tenant) {
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.DELETE_L3ACL_POLICY_V1_1,
              randomName(),
              requestParams(),
              "");

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .singleElement()
              .matches(o -> o.getAction() == Action.DELETE)
              .extracting(Operation::getId)
              .isEqualTo(l3AclPolicyId);

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .singleElement()
              .matches(o -> o.getOpType() == OpType.DEL)
              .extracting(Operations::getId)
              .isEqualTo(l3AclPolicyId);

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(a -> a.getStep().equals(ApiFlowNames.DELETE_L3ACL_POLICY));
        }
      }
    }
  }
}
