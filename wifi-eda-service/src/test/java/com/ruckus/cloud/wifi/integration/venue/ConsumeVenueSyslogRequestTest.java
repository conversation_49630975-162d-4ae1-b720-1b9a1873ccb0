package com.ruckus.cloud.wifi.integration.venue;

import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.venueSyslog;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.VenueTestFixture.randomVenue;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.SyslogFacilityEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.SyslogFlowLevelEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.SyslogPriorityEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.SyslogProtocolEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Struct;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ProtocolEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.VenueSyslogGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueSyslog;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@WifiIntegrationTest
class ConsumeVenueSyslogRequestTest {

  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_SYSLOG)
  class ConsumeUpdateVenueSyslogRequestTest {

    @Payload
    private final VenueSyslogGenerator generator = Generators.venueSyslog();

    private String venueId;

    @BeforeEach
    void givenOneVenuePersistedInDb(final Venue venue) {
      venueId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueSyslog payload) {
      validateResult(CfgAction.UPDATE_VENUE_SYSLOG, venueId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.RESET_VENUE_SYSLOG)
  class ConsumeResetVenueSyslogRequestTest {

    private String venueId;

    @BeforeEach
    void givenOneVenuePersistedInDbWithSyslogEnabled(final Tenant tenant) {
      final Venue venue = randomVenue(tenant);
      venue.setSyslog(venueSyslog().generate());
      venueId = repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId()).getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateResult(CfgAction.RESET_VENUE_SYSLOG, venueId, new VenueSyslog());
    }
  }

  private void validateResult(CfgAction apiAction, String venueId, VenueSyslog payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final TxChanges txChanges = revisionService.changes(requestId, tenantId,
        ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    assertThat(txChanges.getNewEntities()).isEmpty();
    assertThat(txChanges.getModifiedEntities())
        .filteredOn(txEntity -> txEntity.getEntity() instanceof Venue)
        .hasSize(1).singleElement()
        .extracting(TxEntity::getId).isEqualTo(venueId);
    assertThat(txChanges.getDeletedEntities()).isEmpty();

    validateRepositoryData(venueId, payload, apiAction);
    validateDdccmCfgRequestMessages(apiAction, venueId, payload);
    validateCmnCfgCollectorMessages(apiAction, venueId, payload);
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(String venueId, VenueSyslog payload,
      CfgAction apiAction) {
    if (venueId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final Venue venue = repositoryUtil.find(Venue.class, venueId);

    assertThat(venue)
        .isNotNull()
        .extracting(Venue::getSyslog)
        .satisfies(venueSyslog -> assertSoftly(softly -> {
          softly.assertThat(venueSyslog.getEnabled()).isEqualTo(payload.getEnabled());
          softly.assertThat(venueSyslog.getServer()).isEqualTo(payload.getServer());
          softly.assertThat(venueSyslog.getPort()).isEqualTo(payload.getPort());
          softly.assertThat(venueSyslog.getProtocol()).isEqualTo(payload.getProtocol());
          softly.assertThat(venueSyslog.getSecondaryServer()).isEqualTo(payload.getSecondaryServer());
          softly.assertThat(venueSyslog.getSecondaryPort()).isEqualTo(payload.getSecondaryPort());
          softly.assertThat(venueSyslog.getSecondaryProtocol()).isEqualTo(payload.getSecondaryProtocol());
          softly.assertThat(venueSyslog.getFacility()).isEqualTo(payload.getFacility());
          softly.assertThat(venueSyslog.getPriority()).isEqualTo(payload.getPriority());
          softly.assertThat(venueSyslog.getFlowLevel()).isEqualTo(payload.getFlowLevel());
        }));
  }

  private void validateDdccmCfgRequestMessages(CfgAction apiAction, String venueId,
      VenueSyslog payload) {
    if (apiAction == null || venueId == null) {
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .extracting(WifiConfigRequest::getOperationsList,
            InstanceOfAssertFactories.list(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class))
        .isNotEmpty()
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .hasSize(1).singleElement()
        .satisfies(op -> {
          assertThat(op.getId()).isEqualTo(venueId);
          assertThat(op.getAction()).isEqualTo(action(apiAction));
          assertThat(op.getCommonInfo())
              .satisfies(commonInfo -> {
                assertThat(commonInfo.getRequestId()).isEqualTo(requestId);
                assertThat(commonInfo.getTenantId()).isEqualTo(tenantId);
                assertThat(commonInfo.getSender()).isEqualTo(ServiceType.WIFI_SERVICE);
              });
          assertThat(op.getVenue())
              .isNotNull()
              .satisfies(venue -> {
                assertThat(venue.getId()).isEqualTo(venueId);
                if (BooleanUtils.isNotTrue(payload.getEnabled())) {
                  assertThat(venue.hasSyslog()).isFalse();
                  return;
                }
                assertThat(venue.hasSyslog()).isTrue();
                assertThat(venue.getSyslog())
                    .isNotNull()
                    .satisfies(syslog -> {
                      assertThat(syslog.getAddress()).isEqualTo(payload.getServer());
                      assertThat(syslog.getPort()).isEqualTo(Int32Value.of(payload.getPort()));
                      assertThat(syslog.getProtocol()).isEqualTo(protocol(payload.getProtocol()));
                      if (StringUtils.isNotEmpty(payload.getSecondaryServer())) {
                        assertThat(syslog.hasSecondaryAddress()).isTrue();
                        assertThat(syslog.getSecondaryAddress())
                            .isEqualTo(StringValue.of(payload.getSecondaryServer()));
                      } else {
                        assertThat(syslog.hasSecondaryAddress()).isFalse();
                      }
                      if (syslog.getSecondaryPort() != null) {
                        assertThat(syslog.hasSecondaryPort()).isTrue();
                        assertThat(syslog.getSecondaryPort())
                            .isEqualTo(Int32Value.of(payload.getSecondaryPort()));
                      } else {
                        assertThat(syslog.hasSecondaryPort()).isFalse();
                      }
                      if (syslog.getSecondaryProtocol() != null) {
                        assertThat(syslog.getSecondaryProtocol())
                            .isEqualTo(protocol(payload.getSecondaryProtocol()));
                      } else {
                        assertThat(syslog.getSecondaryProtocol())
                            .isEqualTo(SyslogProtocolEnum.SyslogProtocolEnum_UNSET);
                      }
                      assertThat(syslog.getFacility())
                          .isEqualTo(SyslogFacilityEnum.valueOf(payload.getFacility().name()));
                      assertThat(syslog.getPriority())
                          .isEqualTo(SyslogPriorityEnum.valueOf(
                              "SyslogPriorityEnum_" + payload.getPriority().name()));
                      assertThat(syslog.getFlowLevel())
                          .isEqualTo(SyslogFlowLevelEnum.valueOf(
                              "SyslogFlowLevelEnum_" + payload.getFlowLevel().name()));
                    });
              });
        });
  }

  private static SyslogProtocolEnum protocol(ProtocolEnum protocol) {
    return SyslogProtocolEnum.valueOf(
        (protocol == ProtocolEnum.TLS ? "RSYSPROTO_" : "IPPROTO_") + protocol.name());
  }

  private static Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case UPDATE_VENUE_SYSLOG,
          RESET_VENUE_SYSLOG -> Action.MODIFY;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, String venueId,
      VenueSyslog payload) {
    if (apiAction == null || venueId == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(requestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .hasSize(1).singleElement()
        .satisfies(op -> {
          assertThat(op.getIndex()).isEqualTo(EsConstants.Index.VENUE);
          assertThat(op.getOpType()).isEqualTo(OpType.MOD);
          assertThat(op.getId()).isEqualTo(venueId);
          assertThat(op.getDocMap())
              .extractingByKey("syslogServer").isNotNull()
              .extracting(Value::getStructValue)
              .extracting(Struct::getFieldsMap,
                  InstanceOfAssertFactories.map(String.class, Value.class))
              .extractingByKey("enabled").isNotNull()
              .extracting(Value::getBoolValue)
              .isEqualTo(payload.getEnabled());
        });
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors.assertThat(
          messageCaptors.getActivityCfgChangeMessageCaptor(),
          messageCaptors.getActivityImpactDeviceMessageCaptor()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> {
          assertThat(msg.getStatus()).isEqualTo(Status.OK);
          assertThat(msg.getStep()).isEqualTo(apiFlowName(apiAction));
        })
        .extracting(ConfigurationStatus::getEventDate).isNotNull();

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> {
          assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
          assertThat(msg.getDeviceIdsCount()).isZero();
        });
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case UPDATE_VENUE_SYSLOG -> ApiFlowNames.UPDATE_VENUE_SYSLOG;
      case RESET_VENUE_SYSLOG -> ApiFlowNames.RESET_VENUE_SYSLOG;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
