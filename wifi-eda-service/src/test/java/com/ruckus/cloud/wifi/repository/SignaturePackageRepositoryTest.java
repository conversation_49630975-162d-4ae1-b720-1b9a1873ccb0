package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO signature_package (id, version, default_version, next_version, releasable)
        VALUES ('v1', 'v1', true, 'v2', true);
    INSERT INTO signature_package (id, version, default_version, next_version, releasable)
        VALUES ('v2', 'v2', false, null, true);
    INSERT INTO signature_package_mapping (id, signature_package, version, to_version, generated)
        VALUES ('v1_v2', 'v1', 'v1', 'v2', false);
    INSERT INTO signature_package_mapping_item (id, signature_package_mapping, type,
            application_id, application_name, category_id, category_name,
            to_application_id, to_application_name, to_category_id, to_category_name)
        VALUES ('v1_v2_i1', 'v1_v2', 'APPLICATION_ADDED',
            null, null, null, null,
            3005, '163Mail', 31, 'Webmail');
    INSERT INTO signature_package_mapping_item (id, signature_package_mapping, type,
            application_id, application_name, category_id, category_name,
            to_application_id, to_application_name, to_category_id, to_category_name)
        VALUES ('v1_v2_i2', 'v1_v2', 'APPLICATION_REMOVED',
            1496, 'COMM', 3, 'Audio/Video',
            null, null, null, null);
    INSERT INTO signature_package_mapping_item (id, signature_package_mapping, type,
            application_id, application_name, category_id, category_name,
            to_application_id, to_application_name, to_category_id, to_category_name)
        VALUES ('v1_v2_i3', 'v1_v2', 'CATEGORY_UPDATED',
            3442, 'Realvnc', 13, 'Realvnc',
            3442, 'Realvnc', 27, 'Thin Client');
    INSERT INTO signature_package_mapping_item (id, signature_package_mapping, type,
            application_id, application_name, category_id, category_name,
            to_application_id, to_application_name, to_category_id, to_category_name)
        VALUES ('v1_v2_i4', 'v1_v2', 'APPLICATION_RENAMED',
            1138, 'Stafaband.info', 30, 'Web',
            1138, 'Stafaband.co', 30, 'Web');
    """)
public class SignaturePackageRepositoryTest {

  @Autowired
  private SignaturePackageRepository signaturePackageRepository;

  @Autowired
  private SignaturePackageMappingRepository signaturePackageMappingRepository;

  @Test
  void findSignaturePackageTest() {
    var v1 = signaturePackageRepository.findById("v1");
    assertTrue(v1.isPresent());
    assertFalse(v1.get().getSignaturePackageMappings().isEmpty());
    var v2 = signaturePackageRepository.findById(v1.get().getNextVersion());
    assertTrue(v2.isPresent());
    assertTrue(v2.get().getSignaturePackageMappings().isEmpty());
    var defaultSignaturePackage = signaturePackageRepository.findByDefaultVersionIsTrue();
    assertTrue(defaultSignaturePackage.getDefaultVersion());
    assertEquals("v1", defaultSignaturePackage.getVersion());
  }

  @Test
  void existsSignaturePackageTest() {
    var result = signaturePackageRepository.existsById("v1");
    assertTrue(result);
    result = signaturePackageRepository.existsById("v2");
    assertTrue(result);
    result = signaturePackageRepository.existsById("v3");
    assertFalse(result);
    result = signaturePackageRepository.existsByIdIn(List.of("v1", "v2"));
    assertTrue(result);
    result = signaturePackageRepository.existsByIdIn(List.of("v1", "v2", "v3"));
    assertTrue(result);
    result = signaturePackageRepository.existsByIdIn(List.of("v3", "v4"));
    assertFalse(result);
  }

  @Test
  void findSignaturePackageMappingByVersionAndToVersionTest() {
    var result = signaturePackageMappingRepository.findByVersionAndToVersion("v1", "v2");
    assertTrue(result.isPresent());
    assertEquals(4, result.get().getSignaturePackageMappingItems().size());
  }

  @Test
  void existsSignaturePackageMappingByVersionAndToVersionTest() {
    var result = signaturePackageMappingRepository.existsByVersionAndToVersion("v1", "v2");
    assertTrue(result);
    result = signaturePackageMappingRepository.existsByVersionAndToVersion("v1", "v3");
    assertFalse(result);
  }
}
