package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_FLOW;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_REQUEST_ID;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.HEADER_TEMPLATE_TENANT_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.acx.activity.protobuf.execution.common.ActivityCommon;
import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan;
import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RogueApPolicy;
import com.ruckus.cloud.entitlement.operation.eda.EntOperationsEDARequest;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.venue.proto.Address;
import com.ruckus.cloud.venue.proto.VenueEvent;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.ClientIsolationProfile;
import com.ruckus.cloud.wifi.eda.viewmodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueRogueAp;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.integration.AbstractServiceTest;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public abstract class AbstractRequestTest extends AbstractServiceTest {

  private static final Duration RECEIVE_TIMEOUT = Duration.ofSeconds(5L);
  private static final long ALL_RECEIVE_TIMEOUT_IN_SECONDS = 5;

  /**
   * If you don't want to do clearMessage() manually,
   * set this to true in your test.
   */
  protected boolean clearMessageBeforeEachEdaOperation = false;

  @Autowired
  protected ExtendedMessageUtil messageUtil;
  @Autowired
  protected KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  protected MessageCaptors messageCaptors;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = false;
  }

  public static void changeTxCtxTenant(String ecTenantId) {
    TxCtxHolder.TxCtx txCtx =
        new TxCtxHolder.TxCtx(ecTenantId, TxCtxHolder.txId(),
            TxCtxHolder.get().getUserName(), TxCtxHolder.get().getFlowName());
    TxCtxHolder.set(txCtx);
  }

  public void sendWifiCfgRequest(
      String tenantId,
      String requestId,
      CfgAction apiAction,
      String userName,
      Object payload) {
    clearMessageBeforeEachEdaOperation();
    try {
      requestId = changeTxId(requestId); //TODO workaround for jpa rollback
      messageUtil.sendWifiCfgRequest(tenantId, requestId, apiAction, userName, payload);
    } catch (Exception ignored) {}
  }

  private void clearMessageBeforeEachEdaOperation() {
    if (clearMessageBeforeEachEdaOperation) {
      clearMessage();
    }
  }

  public void sendWifiCfgRequest(
      String tenantId,
      String requestId,
      CfgAction apiAction,
      String userName,
      RequestParams requestParams,
      Object payload) {
    sendWifiCfgRequest(tenantId, requestId, apiAction, userName, requestParams, Maps.newHashMap(), payload, true);
  }

  /**
   * This is for *ByTemplate
   */
  public void sendByTemplateWifiCfgRequest(
      String tenantId,
      String requestId,
      CfgAction apiAction,
      String flowName,
      String userName,
      RequestParams requestParams,
      Object payload) {
    Map<String, String> rhs = new HashMap<>(Map.of(
        HEADER_TEMPLATE_FLOW, flowName,
        HEADER_TEMPLATE_TENANT_ID, tenantId,
        HEADER_TEMPLATE_REQUEST_ID, requestId
    ));
    sendWifiCfgRequest(tenantId, requestId, apiAction, userName, requestParams, rhs, payload, false);
  }

  public void sendWifiCfgRequest(
      String tenantId,
      String requestId,
      CfgAction apiAction,
      String userName,
      RequestParams requestParams,
      Map<String, String> requestHeaders,
      Object payload,
      boolean changeTxId) {
    clearMessageBeforeEachEdaOperation();
    try {
      if (changeTxId) {
        requestId = changeTxId(requestId); //TODO workaround for jpa rollback
      }
      messageUtil.sendWifiCfgRequest(tenantId, requestId, apiAction, userName, requestParams, requestHeaders, payload);
    } catch (Exception e) {
      log.error("sendWifiCfgRequest failed", e);
    }
  }

  public void sendVenueCfgChange(String tenantId, String requestId, VenueEvent payload) {
    clearMessageBeforeEachEdaOperation();
    try {
      requestId = changeTxId(requestId); //TODO workaround for jpa rollback
      messageUtil.sendVenueCfgChange(tenantId, requestId, payload);
    } catch (Exception ignored) {}
  }

  /**
   * Use receiveDdccmOperations(int messageCount, String tenantId) in case receive messages from other tenant
   */
  @SneakyThrows
  @Deprecated
  public List<Operation> receiveDdccmOperations(int messageCount) {
    Preconditions.checkArgument(messageCount > 0,
        "messageCount must > 0 or use assertDdccmCfgRequestNotSent()");
    return CompletableFuture.supplyAsync(
        () -> {
          List<Operation> operations = new ArrayList<>();
          for (int i=0;i<messageCount;i++) {
            var record = messageCaptors.getDdccmMessageCaptor().getValue();
            if (record == null) {
              // in order to not wait till timeout, please set an exact messageCount
              String msg = i == 0 ?
                  "Ddccm null record [%s]. Please check or assertDdccmCfgRequestNotSent()":
                  "Ddccm null record [%s]. Please check or decrease messageCount";
              throw new IllegalArgumentException(String.format(msg, i));
            }
            operations.addAll(record.getPayload().getOperationsList());
          }
          return operations;
        }
    ).get(ALL_RECEIVE_TIMEOUT_IN_SECONDS, TimeUnit.SECONDS);
  }

  @SneakyThrows
  public List<Operation> receiveDdccmOperations(int messageCount, String tenantId) {
    Preconditions.checkArgument(messageCount > 0,
        "messageCount must > 0 or use assertDdccmCfgRequestNotSent()");
    return CompletableFuture.supplyAsync(
        () -> {
          List<Operation> operations = new ArrayList<>();
          for (int i=0;i<messageCount;i++) {
            var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, RECEIVE_TIMEOUT);
            if (record == null) {
              // in order to not wait till timeout, please set an exact messageCount
              String msg = i == 0 ?
                  "Ddccm null record [%s]. Please check or assertDdccmCfgRequestNotSent()":
                  "Ddccm null record [%s]. Please check or decrease messageCount";
              throw new IllegalArgumentException(String.format(msg, i));
            }
            operations.addAll(record.getPayload().getOperationsList());
          }
          return operations;
        }
    ).get(ALL_RECEIVE_TIMEOUT_IN_SECONDS, TimeUnit.SECONDS);
  }

  /**
   * Use receiveViewmodelCollectorOperations(int messageCount, String tenantId) in case receive messages from other tenant
   */
  @SneakyThrows
  @Deprecated
  public List<Operations> receiveViewmodelCollectorOperations(int messageCount) {
    Preconditions.checkArgument(messageCount > 0,
        "messageCount must > 0 or use assertCmnCfgCollectorCfgRequestNotSent()");
    return CompletableFuture.supplyAsync(
        () -> {
          List<Operations> operations = new ArrayList<>();
          for (int i=0;i<messageCount;i++) {
            var record = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(RECEIVE_TIMEOUT);
            if (record == null) {
              // in order to not wait till timeout, please set an exact messageCount
              String msg = i == 0 ?
                  "CmnCfg null record [%s]. Please check or assertCmnCfgCollectorCfgRequestNotSent()":
                  "CmnCfg null record [%s]. Please check or decrease messageCount";
              throw new IllegalArgumentException(String.format(msg, i));
            }
            operations.addAll(record.getPayload().getOperationsList());
          }
          return operations;
        }
    ).get(ALL_RECEIVE_TIMEOUT_IN_SECONDS, TimeUnit.SECONDS);
  }

  @SneakyThrows
  public List<Operations> receiveViewmodelCollectorOperations(int messageCount, String tenantId) {
    Preconditions.checkArgument(messageCount > 0,
        "messageCount must > 0 or use assertCmnCfgCollectorCfgRequestNotSent()");
    return CompletableFuture.supplyAsync(
        () -> {
          List<Operations> operations = new ArrayList<>();
          for (int i=0;i<messageCount;i++) {
            var record = messageCaptors.getCmnCfgCollectorMessageCaptor()
                .getValue(tenantId, RECEIVE_TIMEOUT);
            if (record == null) {
              // in order to not wait till timeout, please set an exact messageCount
              String msg = i == 0 ?
                  "CmnCfg null record [%s]. Please check or assertCmnCfgCollectorCfgRequestNotSent()":
                  "CmnCfg null record [%s]. Please check or decrease messageCount";
              throw new IllegalArgumentException(String.format(msg, i));
            }
            operations.addAll(record.getPayload().getOperationsList());
          }
          return operations;
        }
    ).get(ALL_RECEIVE_TIMEOUT_IN_SECONDS, TimeUnit.SECONDS);
  }

  /**
   * Use receiveRoguePolicyOperations(int messageCount, String tenantId) in case receive messages from other tenant
   */
  @SneakyThrows
  @Deprecated
  public List<RogueApPolicy> receiveRoguePolicyOperations(int messageCount) {
    Preconditions.checkArgument(messageCount > 0,
        "messageCount must > 0 or use assertRoguePolicyNotSent()");
    return CompletableFuture.supplyAsync(
        () -> {
          List<RogueApPolicy> operations = new ArrayList<>();
          for (int i=0;i<messageCount;i++) {
            var record = messageCaptors.getRogueApPolicyMessageCaptor()
                .getValue(RECEIVE_TIMEOUT);
            if (record == null) {
              // in order to not wait till timeout, please set an exact messageCount
              String msg = i == 0 ?
                  "RoguePolicy null record [%s]. Please check or assertRoguePolicyNotSent()":
                  "RoguePolicy null record [%s]. Please check or decrease messageCount";
              throw new IllegalArgumentException(String.format(msg, i));
            }
            operations.add(record.getPayload());
          }
          return operations;
        }
    ).get(ALL_RECEIVE_TIMEOUT_IN_SECONDS, TimeUnit.SECONDS);
  }

  @SneakyThrows
  public List<RogueApPolicy> receiveRoguePolicyOperations(int messageCount, String tenantId) {
    Preconditions.checkArgument(messageCount > 0,
        "messageCount must > 0 or use assertRoguePolicyNotSent()");
    return CompletableFuture.supplyAsync(
        () -> {
          List<RogueApPolicy> operations = new ArrayList<>();
          for (int i=0;i<messageCount;i++) {
            var record = messageCaptors.getRogueApPolicyMessageCaptor()
                .getValue(tenantId, RECEIVE_TIMEOUT);
            if (record == null) {
              // in order to not wait till timeout, please set an exact messageCount
              String msg = i == 0 ?
                  "RoguePolicy null record [%s]. Please check or assertRoguePolicyNotSent()":
                  "RoguePolicy null record [%s]. Please check or decrease messageCount";
              throw new IllegalArgumentException(String.format(msg, i));
            }
            operations.add(record.getPayload());
          }
          return operations;
        }
    ).get(ALL_RECEIVE_TIMEOUT_IN_SECONDS, TimeUnit.SECONDS);
  }

 public ConfigurationStatus receiveActivityStatus() {
    return Optional.ofNullable(messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(RECEIVE_TIMEOUT)).map(KafkaProtoMessage::getPayload)
        .orElseThrow(() -> new RuntimeException("No ActivityStatus"));
  }

  public ConfigurationStatus receiveActivityStatus(String tenantId) {
    return Optional.ofNullable(messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, RECEIVE_TIMEOUT))
        .map(record -> record.getPayload())
        .orElseThrow(() -> new RuntimeException("No ActivityStatus"));
  }

  public ActivityExecutionPlan receiveActivityPlan(String tenantId) {
    return Optional.ofNullable(messageCaptors.getActivityPlanMessageCaptor()
            .getValue(tenantId, RECEIVE_TIMEOUT))
        .map(record -> record.getPayload())
        .orElseThrow(() -> new RuntimeException("No ActivityPlan"));
  }

  @SneakyThrows
  public List<EntOperationsEDARequest> receiveEntitlementEdaOperations(int messageCount, String tenantId) {
    Preconditions.checkArgument(messageCount > 0,
      "messageCount must > 0 or use assertEntOperationsEDARequestNotSent()");
    return CompletableFuture.supplyAsync(
      () -> {
        List<EntOperationsEDARequest> operations = new ArrayList<>();
        for (int i=0;i<messageCount;i++) {
          var record = messageCaptors.getEntitlementDeviceOperationMessageCaptor()
            .getValue(tenantId, RECEIVE_TIMEOUT);
          if (record == null) {
            // in order to not wait till timeout, please set an exact messageCount
            String msg = i == 0 ?
              "CmnCfg null record [%s]. Please check or assertEntOperationsEDARequestNotSent()":
              "CmnCfg null record [%s]. Please check or decrease messageCount";
            throw new IllegalArgumentException(String.format(msg, i));
          }
          operations.add(record.getPayload());
        }
        return operations;
      }
    ).get(ALL_RECEIVE_TIMEOUT_IN_SECONDS, TimeUnit.SECONDS);
  }

  @SneakyThrows
  public void clearMessage() {
    messageUtil.clearMessage();;
  }

  /**
   * Use assertActivityStatusSuccess(String step, String tenantId) in case receive messages from other tenant
   * @param step
   */
  @Deprecated
  public void assertActivityStatusSuccess(String step) {
    ConfigurationStatus status = receiveActivityStatus();
    assertAll("assert activity status success",
        () -> assertEquals(ConfigurationStatus.Status.OK, status.getStatus()),
        () -> assertEquals(step, status.getStep())
    );
  }

  public void assertActivityStatusSuccess(String step, String tenantId) {
    ConfigurationStatus status = receiveActivityStatus(tenantId);
    assertAll("assert activity status success",
        () -> assertEquals(ConfigurationStatus.Status.OK, status.getStatus()),
        () -> assertEquals(step, status.getStep())
    );
  }

  // If test produces steps are not in order, you can use this.
  public void assertActivityStatusSuccess(String tenantId, List<String> steps) {
    List<ConfigurationStatus> list = new ArrayList<>();
    Set<String> set = new HashSet<>();
    try {
      for (;;) {
        var s = receiveActivityStatus(tenantId);
        list.add(s);
        set.add(s.getStep());
      }
    } catch (Exception e) {
      // ignore
    }
    assertAll("assert all activities status success",
        () -> list.forEach(s -> assertEquals(ConfigurationStatus.Status.OK, s.getStatus(), "status %s not OK".formatted(s.getStep()))),
        () -> assertTrue(set.containsAll(steps), "steps %s not in %s".formatted(steps.toString(), set.toString()))
    );
  }

  public ActivityExecutionPlan assertActivityPlan(String flow, String tenantId) {
    ActivityExecutionPlan plan = receiveActivityPlan(tenantId);
    assertAll("assert activity plan",
        () -> assertEquals(flow, plan.getViewSteps(0).getName())
    );
    return plan;
  }

  public void assertActivityStatusSuccess(String step, String tenantId, List<String> entityIds) {
    ConfigurationStatus status = receiveActivityStatus(tenantId);
    assertAll("assert activity status success",
        () -> assertEquals(ConfigurationStatus.Status.OK, status.getStatus()),
        () -> assertEquals(step, status.getStep()),
        () -> assertThat(status.getEntityIdList().containsAll(entityIds))
    );
  }

  /**
   * Use assertActivityStatusFail(String step, Errors errorCode, String tenantId) in case receive messages from other tenant
   * @param step
   */
  @SneakyThrows
  @Deprecated
  public void assertActivityStatusFail(String step, Errors errorCode) {
    ConfigurationStatus status = receiveActivityStatus();
    assertAll("assert activity status fail",
        () -> assertEquals(Status.FAIL, status.getStatus()),
        () -> assertEquals(step, status.getStep())
    );
    JSONObject error = new JSONObject(status.getError());
    assertAll("assert activity status fail error",
        () -> assertEquals(errorCode.code(), error.get("code")),
        () -> assertEquals(errorCode.message(), error.get("message"))
    );
  }

  @SneakyThrows
  public void assertActivityStatusFail(String step, Errors errorCode, String tenantId) {
    ConfigurationStatus status = receiveActivityStatus(tenantId);
    assertAll("assert activity status fail",
        () -> assertEquals(Status.FAIL, status.getStatus()),
        () -> assertEquals(step, status.getStep())
    );
    JSONObject error = new JSONObject(status.getError());
    assertAll("assert activity status fail error",
        () -> assertEquals(errorCode.code(), error.get("code")),
        () -> assertEquals(errorCode.message(), error.get("message"))
    );
  }

  /**
   * Use assertActivityStatusFail(String step, String tenantId) in case receive messages from other tenant
   * @param step
   */
  @SneakyThrows
  @Deprecated
  public void assertActivityStatusFail(String step) {
    ConfigurationStatus status = receiveActivityStatus();
    assertAll("assert activity status fail",
        () -> assertEquals(Status.FAIL, status.getStatus()),
        () -> assertEquals(step, status.getStep())
    );
  }

  @SneakyThrows
  public ConfigurationStatus assertActivityStatusFail(String step, String tenantId) {
    ConfigurationStatus status = receiveActivityStatus(tenantId);
    assertAll("assert activity status fail",
        () -> assertEquals(Status.FAIL, status.getStatus()),
        () -> assertEquals(step, status.getStep())
    );
    return status;
  }

  protected void assertNoMessages(String tenantId, String topic) {
    messageCaptors.assertThat(topic)
        .doesNotSendByTenant(tenantId);
  }

  protected void assertNoMessages(String tenantId, String topic1, String topic2) {
    messageCaptors.assertThat(topic1, topic2)
        .doesNotSendByTenant(tenantId);
  }

  protected void assertNoMessages(String tenantId, String topic1, String topic2, String topic3) {
    messageCaptors.assertThat(topic1, topic2, topic3)
        .doesNotSendByTenant(tenantId);
  }

  public void assertActivityPlanNotSent(String tenantId) {
    assertNoMessages(tenantId, kafkaTopicProvider.getActivityPlan());
  }

  public void assertActivityStatusNotSent(String tenantId) {
    assertNoMessages(tenantId, kafkaTopicProvider.getActivityCfgChangeResp());
  }

  public void assertRoguePolicyNotSent(String tenantId) {
    assertNoMessages(tenantId, kafkaTopicProvider.getRoguePolicy());
  }

  public void assertCmnCfgCollectorCfgRequestNotSent(String tenantId) {
    assertNoMessages(tenantId, kafkaTopicProvider.getCmnCfgCollectorCfgRequest());
  }

  public void assertDdccmCfgRequestNotSent(String... tenantIds) {
    messageCaptors.assertThat(kafkaTopicProvider.getDdccmCfgRequest())
        .doesNotSendByTenants(tenantIds);
  }

  // eda method

  void updateEdaNetwork(String tenantId, String userName,
      String networkId, com.ruckus.cloud.wifi.eda.viewmodel.Network network) {
    RequestParams rps = new RequestParams().addPathVariable("networkId", networkId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_NETWORK, userName, rps, network);
  }

  protected void edaUpdateNetworkTemplate(String tenantId, String userName,
                        String networkId, com.ruckus.cloud.wifi.eda.viewmodel.Network network) {
    RequestParams rps = new RequestParams().addPathVariable("networkTemplateId", networkId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_NETWORK_TEMPLATE, userName, rps, network);
  }

  void deleteEdaNetwork(String tenantId, String userName, String networkId) {
    RequestParams rps = new RequestParams().addPathVariable("networkId", networkId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_NETWORK, userName, rps, "");
  }

  void edaAddNetworkVenue(String tenantId, String userName,
      String networkId, String venueId, String clientIsolationAllowlistId) {
    com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue nv = Generators.networkVenue().generate();
    nv.setNetworkId(networkId);
    nv.setVenueId(venueId);
    nv.setClientIsolationAllowlistId(clientIsolationAllowlistId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE, userName, nv);
  }

  protected void edaAddNetworkVenueTemplate(String tenantId, String userName,
                          String networkId, String venueId, String clientIsolationAllowlistId) {
    com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue nv = Generators.networkVenue().generate();
    nv.setNetworkId(networkId);
    nv.setVenueId(venueId);
    nv.setClientIsolationAllowlistId(clientIsolationAllowlistId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE_TEMPLATE, userName, nv);
  }

  void addEdaNetworkVenueMapping(String tenantId, String userName,
      String networkId, String venueId) {
    final var networkVenue = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
    networkVenue.setId(randomId()); // autoGenerated is true in wifi-api
    networkVenue.setNetworkId(networkId);
    networkVenue.setVenueId(venueId);
    networkVenue.setIsAllApGroups(true);
    networkVenue.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radi

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE_MAPPINGS, userName, List.of(networkVenue));
  }

  protected void edaAddNetworkVenueTemplateMapping(String tenantId, String userName,
      String networkId, String venueId) {
    final var networkVenue = new com.ruckus.cloud.wifi.eda.viewmodel.NetworkActivationMapping();
    networkVenue.setId(randomId()); // autoGenerated is true in wifi-api
    networkVenue.setNetworkId(networkId);
    networkVenue.setVenueId(venueId);
    networkVenue.setIsAllApGroups(true);
    networkVenue.setAllApGroupsRadioTypes(List.of(RadioTypeEnum.values())); // All radi

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_VENUE_TEMPLATE_MAPPINGS, userName, List.of(networkVenue));
  }

  void updateEdaNetworkVenue(String tenantId, String userName, String networkVenueId,
      com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue networkVenue, String clientIsolationAllowlistId) {
    networkVenue.setClientIsolationAllowlistId(clientIsolationAllowlistId);
    RequestParams rps = new RequestParams().addPathVariable("networkVenueId", networkVenueId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_NETWORK_VENUE, userName,
        rps, networkVenue);
  }

  void edaUpdateNetworkVenueTemplate(String tenantId, String userName, String networkVenueId,
                             com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue networkVenue, String clientIsolationAllowlistId) {
    networkVenue.setClientIsolationAllowlistId(clientIsolationAllowlistId);
    RequestParams rps = new RequestParams().addPathVariable("networkVenueId", networkVenueId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_NETWORK_VENUE_TEMPLATE, userName,
        rps, networkVenue);
  }

  void deleteEdaNetworkVenue(String tenantId, String userName, String networkVenueId) {
    RequestParams rps = new RequestParams().addPathVariable("networkVenueId", networkVenueId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DELETE_NETWORK_VENUE, userName,
        rps, null);
  }

  void edaDeleteNetworkVenueTemplate(String tenantId, String userName, String networkVenueId) {
    RequestParams rps = new RequestParams().addPathVariable("networkVenueId", networkVenueId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DELETE_NETWORK_VENUE_TEMPLATE, userName,
        rps, null);
  }

  void addEdaClientIsolationProfile(String tenantId, String userName,
      ClientIsolationProfile clientIsolationProfile) {
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_PROFILE, userName,
        clientIsolationProfile);
  }

  void activateEdaClientIsolationProfileWithNetwork(String tenantId, String userName,
      String venueId, String networkId, String clientIsolationProfileId) {
    RequestParams rps = new RequestParams().addPathVariable("venueId", venueId)
        .addPathVariable("wifiNetworkId", networkId)
        .addPathVariable("clientIsolationProfileId", clientIsolationProfileId);
    sendWifiCfgRequest(tenantId, randomTxId(),
        CfgAction.ACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, userName, rps, "");
  }

  void deactivateEdaClientIsolationProfileWithNetwork(String tenantId, String userName,
      String venueId, String networkId, String clientIsolationProfileId) {
    RequestParams rps = new RequestParams().addPathVariable("venueId", venueId)
        .addPathVariable("wifiNetworkId", networkId)
        .addPathVariable("clientIsolationProfileId", clientIsolationProfileId);
    sendWifiCfgRequest(tenantId, randomTxId(),
        CfgAction.DEACTIVATE_CLIENT_ISOLATION_PROFILE_ON_WIFI_NETWORK, userName, rps, "");
  }

  @Deprecated(forRemoval = true)
  protected void edaAddDhcpConfigServiceProfile(String tenantId, String userName,
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep) {
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE,
        userName, dhcpConfigServiceProfileDeep);
  }

  @Deprecated(forRemoval = true)
  protected void edaAddDhcpConfigServiceProfileTemplate(String tenantId, String userName,
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep) {
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE,
        userName, dhcpConfigServiceProfileDeep);
  }

  void edaAddDhcpConfigServiceProfileV1_1(String tenantId, String userName,
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE_V1_1,
        userName, dhcpConfigServiceProfile);
  }

  protected void edaAddDhcpConfigServiceProfileTemplateV1_1(String tenantId, String userName,
      com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_V1_1,
        userName, dhcpConfigServiceProfile);
  }

  @Deprecated(forRemoval = true)
  protected void edaUpdateDhcpConfigServiceProfile(String tenantId, String userName,
      String dhcpConfigServiceProfileId, com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep) {
    RequestParams rps = new RequestParams().addPathVariable("dhcpConfigServiceProfileId", dhcpConfigServiceProfileId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE, userName,
        rps, dhcpConfigServiceProfileDeep);
  }

  protected void edaUpdateDhcpConfigServiceProfileV1_1(String tenantId, String userName,
      String dhcpConfigServiceProfileId, com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfile dhcpConfigServiceProfile) {
    RequestParams rps = new RequestParams().addPathVariable("dhcpConfigServiceProfileId", dhcpConfigServiceProfileId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE_V1_1, userName,
        rps, dhcpConfigServiceProfile);
  }

  protected void edaUpdateVenueDhcpConfigServiceProfileSetting(String tenantId, String userName, String venueId,
      com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting) {
    RequestParams rps = new RequestParams().addPathVariable("venueId", venueId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, userName,
        rps, venueDhcpConfigServiceProfileSetting);
  }

  protected void edaUpdateVenueTemplateDhcpConfigServiceProfileSetting(String tenantId, String userName, String venueId,
      com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting) {
    RequestParams rps = new RequestParams().addPathVariable("venueTemplateId", venueId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_VENUE_TEMPLATE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, userName,
        rps, venueDhcpConfigServiceProfileSetting);
  }

  void edaActivateVenueDhcpConfigServiceProfileSettings(String tenantId, String userName,
      String venueId, String dhcpConfigServiceProfileId,
      VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings) {
    RequestParams rps = new RequestParams().addPathVariable("venueId", venueId)
        .addPathVariable("dhcpConfigServiceProfileId", dhcpConfigServiceProfileId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_AND_UPDATE_SETTINGS,
        userName,
        rps, venueDhcpConfigServiceProfileSettings);
  }

  protected void edaActivateVenueDhcpConfigServiceProfileTemplateSettings(String tenantId, String userName,
      String venueId, String dhcpConfigServiceProfileId,
      VenueDhcpConfigServiceProfileSettings venueDhcpConfigServiceProfileSettings) {
    RequestParams rps = new RequestParams().addPathVariable("venueTemplateId", venueId)
        .addPathVariable("dhcpConfigServiceProfileId", dhcpConfigServiceProfileId);
    sendWifiCfgRequest(tenantId, randomTxId(),
        CfgAction.ACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE_AND_UPDATE_SETTINGS,
        userName, rps, venueDhcpConfigServiceProfileSettings);
  }

  void edaDeactivateVenueDhcpConfigServiceProfileSettings(String tenantId, String userName,
      String venueId, String dhcpConfigServiceProfileId) {
    RequestParams rps = new RequestParams().addPathVariable("venueId", venueId)
        .addPathVariable("dhcpConfigServiceProfileId", dhcpConfigServiceProfileId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DEACTIVATE_DHCP_CONFIG_SERVICE_PROFILE, userName,
        rps, null);
  }

  void edaDeactivateVenueDhcpConfigServiceProfileTemplateSettings(String tenantId, String userName,
      String venueId, String dhcpConfigServiceProfileId) {
    RequestParams rps = new RequestParams().addPathVariable("venueTemplateId", venueId)
        .addPathVariable("dhcpConfigServiceProfileId", dhcpConfigServiceProfileId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DEACTIVATE_DHCP_CONFIG_SERVICE_PROFILE_TEMPLATE, userName,
        rps, null);
  }

  void edaDeactivateDhcpPool(String tenantId, String userName, String venueId, String dhcpPoolId) {
    RequestParams rps = new RequestParams().addPathVariable("venueId", venueId).addPathVariable("dhcpPoolId", dhcpPoolId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DEACTIVATE_VENUE_DHCP_POOL, userName,
        rps, null);
  }

  protected void edaDeactivateTemplateDhcpPool(String tenantId, String userName, String venueId, String dhcpPoolId) {
    RequestParams rps = new RequestParams().addPathVariable("venueTemplateId", venueId).addPathVariable("dhcpPoolId", dhcpPoolId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DEACTIVATE_VENUE_TEMPLATE_DHCP_POOL, userName,
        rps, null);
  }

  void edaActivateDhcpPool(String tenantId, String userName, String venueId, String dhcpPoolId) {
    RequestParams rps = new RequestParams().addPathVariable("venueId", venueId).addPathVariable("dhcpPoolId", dhcpPoolId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.ACTIVATE_VENUE_DHCP_POOL, userName,
        rps, null);
  }

  void edaActivateTemplateDhcpPool(String tenantId, String userName, String venueId, String dhcpPoolId) {
    RequestParams rps = new RequestParams().addPathVariable("venueTemplateId", venueId).addPathVariable("dhcpPoolId", dhcpPoolId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.ACTIVATE_VENUE_TEMPLATE_DHCP_POOL, userName,
        rps, null);
  }

  protected void edaAddRogueApPolicyProfileTemplate(String tenantId, String userName,
      RogueClassificationPolicy rogueClassificationPolicy) {
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_AP_POLICY_PROFILE_TEMPLATE,
        userName, rogueClassificationPolicy);
  }

  protected void edaAddRoguePolicyTemplate(String tenantId, String userName,
      RogueClassificationPolicy rogueClassificationPolicy) {
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY_TEMPLATE,
        userName, rogueClassificationPolicy);
  }

  protected void edaUpdateVenueRogueAp(String tenantId, String userName, String venueId, VenueRogueAp venueRogueAp) {
    RequestParams rps = new RequestParams().addPathVariable("venueId", venueId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_VENUE_ROGUE_AP, userName,
        rps, venueRogueAp);
  }

  protected void edaUpdateVenueTemplateRogueAp(String tenantId, String userName, String venueId, VenueRogueAp venueRogueAp) {
    RequestParams rps = new RequestParams().addPathVariable("venueTemplateId", venueId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_VENUE_TEMPLATE_ROGUE_AP, userName,
        rps, venueRogueAp);
  }

  protected void edaUpdateVenueTemplateRoguePolicySettings(String tenantId, String userName, String venueId, VenueRogueAp venueRogueAp) {
    RequestParams rps = new RequestParams().addPathVariable("venueTemplateId", venueId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_VENUE_TEMPLATE_ROGUE_POLICY_SETTINGS, userName,
        rps, venueRogueAp);
  }

  protected void edaActivateRoguePolicyOnVenueTemplate(String tenantId, String userName, String venueId, String roguePolicyId) {
    var requestParams =
        new RequestParams()
            .addPathVariable("venueTemplateId", venueId)
            .addPathVariable("roguePolicyTemplateId", roguePolicyId);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE_TEMPLATE,
        userName,
        requestParams,
        null);
  }

  protected void edaActivateRoguePolicyOnVenue(String tenantId, String userName, String venueId, String roguePolicyId) {
    var requestParams =
        new RequestParams()
            .addPathVariable("venueId", venueId)
            .addPathVariable("roguePolicyId", roguePolicyId);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);
  }

  protected void edaActivateWifiCallingServiceProfileOnWifiNetworkTemplate(String tenantId, String userName,
      String networkId, String wifiCallingServiceProfileId) {
    var requestParams =
        new RequestParams()
            .addPathVariable("wifiNetworkTemplateId", networkId)
            .addPathVariable("wifiCallingServiceProfileId", wifiCallingServiceProfileId);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_WIFI_CALLING_SERVICE_PROFILE_TEMPLATE_ON_WIFI_NETWORK_TEMPLATE,
        userName,
        requestParams,
        null);
  }

  protected ActivityExecutionPlan buildActivityExecutionPlan(
      String flowName, WifiActivityPlan.Action action, String entityType, String entityId) {
    return ActivityPlan.ActivityExecutionPlan.newBuilder()
        .setProductType("WIFI")
        .setEntityType(entityType)
        .setEntityId(entityId)
        .setSuccessTemplate("Success...")
        .setFailedTemplate("Failed...")
        .setDescriptionTemplate("Desc...")
        .addPlanSteps(ActivityPlan.PlanStep.newBuilder()
            .setService("wifi")
            .setResponseType(ActivityCommon.StepResponseType.REQUEST)
            .setName(flowName)
            .setViewStep(flowName)
            .build())
        .addViewSteps(ActivityPlan.ViewStep.newBuilder()
            .setDescription("Desc...")
            .setName(flowName)
            .build())
        .setAction(action.name())
        .build();
  }

  protected VenueEvent buildTemplateInstanceEvent(Venue venueTemplate, String instanceId,
      String ecTenantId) {
    return VenueEvent.newBuilder()
        .setTenantId(venueTemplate.getTenant().getId())
        .addOperation(com.ruckus.cloud.venue.proto.Operation.newBuilder().setAction(
                com.ruckus.cloud.venue.proto.Action.ADD)
            .setVenueTemplateInstance(
                com.ruckus.cloud.venue.proto.VenueTemplateInstance.newBuilder()
                    .setVenueId(instanceId)
                    .setVenueName(venueTemplate.getName())
                    .setTemplateId(venueTemplate.getId())
                    .setTemplateVersion(System.currentTimeMillis())
                    .setEcTenantId(ecTenantId)
                    .setAddress(Address.newBuilder()
                        .setCountryCode(venueTemplate.getCountryCode())
                        .setTimezone(venueTemplate.getTimezone())
                        .setAddressLine(venueTemplate.getAddressLine())
                        .setLongitude(
                            Double.parseDouble(venueTemplate.getDeviceGps().getLongitude()))
                        .setLatitude(Double.parseDouble(venueTemplate.getDeviceGps().getLatitude()))
                        .build()))).build();
  }
}
