package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accountingRadiusService;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcct;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.tenant;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.radiusAcctWithIpv6;
import static com.ruckus.cloud.wifi.utils.FeatureRolesUtils.PROXY_RADSEC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import com.google.protobuf.BoolValue;
import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RadiusSecondaryHost;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccountingRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.RadiusServer;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureRole;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
public class DdccmAccountingRadiusServiceOperationBuilderTest {

  @Autowired
  private DdccmAccountingRadiusServiceOperationBuilder builder;

  @Test
  void testCreateAccountingRadiusService() {
    Radius radius = radiusAcct().generate();
    AccountingRadiusService accountingRadiusServiceService = accountingRadiusService(radius).setTenant(tenant()).generate();
    Operation.Builder opBuilder = Operation.newBuilder().setAction(Action.ADD);
    builder.config(opBuilder, accountingRadiusServiceService, EntityAction.ADD, null);
    Operation result = opBuilder.build();

    assertThat(result)
        .hasFieldOrPropertyWithValue("action", Action.ADD)
        .hasFieldOrPropertyWithValue("id", accountingRadiusServiceService.getId());
    assertThat(result.getRadiusAccountingService())
        .hasFieldOrPropertyWithValue("id", accountingRadiusServiceService.getId())
        .hasFieldOrPropertyWithValue("name", accountingRadiusServiceService.getId());
    assertThat(result.getRadiusAccountingService().getPrimary()).isNotNull();
    assertThat(result.getRadiusAccountingService().getPrimary())
        .hasFieldOrPropertyWithValue("ip", accountingRadiusServiceService.getRadius().getPrimary().getIp())
        .hasFieldOrPropertyWithValue("port", accountingRadiusServiceService.getRadius().getPrimary().getPort())
        .hasFieldOrPropertyWithValue("sharedSecret", accountingRadiusServiceService.getRadius().getPrimary().getSharedSecret());

    assertThat(result.getRadiusAccountingService().getSecondary()).isNotNull();
    assertThat(result.getRadiusAccountingService().getSecondary())
        .hasFieldOrPropertyWithValue("ip", accountingRadiusServiceService.getRadius().getSecondary().getIp())
        .hasFieldOrPropertyWithValue("port", accountingRadiusServiceService.getRadius().getSecondary().getPort())
        .hasFieldOrPropertyWithValue("sharedSecret", accountingRadiusServiceService.getRadius().getSecondary().getSharedSecret());
  }

  @Test
  void testCreateIPv6AccountingRadiusService() {
    Radius radius = radiusAcctWithIpv6().generate();
    AccountingRadiusService accountingRadiusServiceService = accountingRadiusService(radius).setTenant(tenant()).generate();
    Operation.Builder opBuilder = Operation.newBuilder().setAction(Action.ADD);
    builder.config(opBuilder, accountingRadiusServiceService, EntityAction.ADD, null);
    Operation result = opBuilder.build();

    assertThat(result)
      .hasFieldOrPropertyWithValue("action", Action.ADD)
      .hasFieldOrPropertyWithValue("id", accountingRadiusServiceService.getId());
    assertThat(result.getRadiusAccountingService())
      .hasFieldOrPropertyWithValue("id", accountingRadiusServiceService.getId())
      .hasFieldOrPropertyWithValue("name", accountingRadiusServiceService.getId());
    assertThat(result.getRadiusAccountingService().getPrimary()).isNotNull();
    assertThat(result.getRadiusAccountingService().getPrimary())
      .hasFieldOrPropertyWithValue("ipv6", accountingRadiusServiceService.getRadius().getPrimary().getIp())
      .hasFieldOrPropertyWithValue("port", accountingRadiusServiceService.getRadius().getPrimary().getPort())
      .hasFieldOrPropertyWithValue("sharedSecret", accountingRadiusServiceService.getRadius().getPrimary().getSharedSecret());

    assertThat(result.getRadiusAccountingService().getSecondary()).isNotNull();
    assertThat(result.getRadiusAccountingService().getSecondary())
      .hasFieldOrPropertyWithValue("ipv6", accountingRadiusServiceService.getRadius().getSecondary().getIp())
      .hasFieldOrPropertyWithValue("port", accountingRadiusServiceService.getRadius().getSecondary().getPort())
      .hasFieldOrPropertyWithValue("sharedSecret", accountingRadiusServiceService.getRadius().getSecondary().getSharedSecret());
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_RADSEC_TOGGLE)
  @FeatureRole(PROXY_RADSEC)
  void testCreateTlsEnabledAccountingRadiusService() {
    Radius radius = radiusAcct()
        .setRadSecOptions(Generators.radSecOptions())
        .setSecondary(nullValue(RadiusServer.class))
        .generate();
    AccountingRadiusService accountingRadiusService = accountingRadiusService(radius).setTenant(tenant()).generate();
    Operation.Builder opBuilder = Operation.newBuilder().setAction(Action.ADD);
    builder.config(opBuilder, accountingRadiusService, EntityAction.ADD, null);
    Operation result = opBuilder.build();

    assertThat(result)
        .hasFieldOrPropertyWithValue("action", Action.ADD)
        .hasFieldOrPropertyWithValue("id", accountingRadiusService.getId());
    assertThat(result.getRadiusAccountingService())
        .hasFieldOrPropertyWithValue("id", accountingRadiusService.getId())
        .hasFieldOrPropertyWithValue("name", accountingRadiusService.getId())
        .hasFieldOrPropertyWithValue("tlsEnabled", BoolValue.of(accountingRadiusService.getRadius().getRadSecOptions().getTlsEnabled()))
        .hasFieldOrPropertyWithValue("cnSanIdentity", accountingRadiusService.getRadius().getRadSecOptions().getCnSanIdentity())
        .hasFieldOrPropertyWithValue("ocspUrl", accountingRadiusService.getRadius().getRadSecOptions().getOcspUrl())
        .hasFieldOrPropertyWithValue("certificateAuthorityId", StringValue.of(
            accountingRadiusService.getRadius().getRadSecOptions().getCertificateAuthorityId()))
        .hasFieldOrPropertyWithValue("clientCertificateId", StringValue.of(
            accountingRadiusService.getRadius().getRadSecOptions().getClientCertificateId()))
        .hasFieldOrPropertyWithValue("serverCertificateId", StringValue.of(
            accountingRadiusService.getRadius().getRadSecOptions().getServerCertificateId()))
        .hasFieldOrPropertyWithValue("secondary", RadiusSecondaryHost.getDefaultInstance());

    assertThat(result.getRadiusAccountingService().getPrimary()).isNotNull();
    assertThat(result.getRadiusAccountingService().getPrimary())
        .hasFieldOrPropertyWithValue("ip", accountingRadiusService.getRadius().getPrimary().getIp())
        .hasFieldOrPropertyWithValue("port", accountingRadiusService.getRadius().getPrimary().getPort());
  }

  @Test
  void testUpdateTlsEnabledAccountingRadiusServiceWithoutFF() {
    Radius radius = radiusAcct()
        .setRadSecOptions(Generators.radSecOptions())
        .setSecondary(nullValue(RadiusServer.class))
        .generate();
    AccountingRadiusService accountingRadiusService = accountingRadiusService(radius).setTenant(tenant()).generate();
    Operation.Builder opBuilder = Operation.newBuilder().setAction(Action.MODIFY);
    builder.config(opBuilder, accountingRadiusService, EntityAction.MODIFY, null);
    Operation result = opBuilder.build();

    assertThat(result)
        .hasFieldOrPropertyWithValue("action", Action.MODIFY)
        .hasFieldOrPropertyWithValue("id", accountingRadiusService.getId());
    assertThat(result.getRadiusAccountingService())
        .hasFieldOrPropertyWithValue("id", accountingRadiusService.getId())
        .hasFieldOrPropertyWithValue("name", accountingRadiusService.getId())
        .hasFieldOrPropertyWithValue("tlsEnabled", BoolValue.of(false))
        .hasFieldOrPropertyWithValue("cnSanIdentity", "")
        .hasFieldOrPropertyWithValue("ocspUrl", "")
        .hasFieldOrPropertyWithValue("certificateAuthorityId", StringValue.of(""))
        .hasFieldOrPropertyWithValue("clientCertificateId", StringValue.of(""))
        .hasFieldOrPropertyWithValue("serverCertificateId", StringValue.of(""))
        .hasFieldOrPropertyWithValue("secondary", RadiusSecondaryHost.getDefaultInstance());

    assertThat(result.getRadiusAccountingService().getPrimary()).isNotNull();
    assertThat(result.getRadiusAccountingService().getPrimary())
        .hasFieldOrPropertyWithValue("ip", accountingRadiusService.getRadius().getPrimary().getIp())
        .hasFieldOrPropertyWithValue("port", accountingRadiusService.getRadius().getPrimary().getPort())
        .hasFieldOrPropertyWithValue("sharedSecret", accountingRadiusService.getRadius().getPrimary().getSharedSecret());
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public DdccmAccountingRadiusServiceOperationBuilder ddccmAccountingRadiusServiceServiceOperationBuilder() {
      DdccmAccountingRadiusServiceOperationBuilder builder = Mockito.spy(DdccmAccountingRadiusServiceOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }
  }
}