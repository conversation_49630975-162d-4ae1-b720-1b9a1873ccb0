package com.ruckus.cloud.wifi.service.compatibility.impl;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willAnswer;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.compatibility.contract.model.Compatibility;
import com.ruckus.cloud.compatibility.contract.model.DeviceAggregation;
import com.ruckus.cloud.compatibility.contract.model.DeviceAggregationGroup;
import com.ruckus.cloud.compatibility.contract.model.DeviceIdentityWrapper;
import com.ruckus.cloud.compatibility.contract.requirement.RequirementChecker;
import com.ruckus.cloud.compatibility.contract.requirement.RequirementCheckerGroup;
import com.ruckus.cloud.wifi.client.viewmodel.ViewmodelClientGrpc;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.NetworkRepository;
import com.ruckus.cloud.wifi.repository.NetworkVenueRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.requirement.ExtendedRequirementCheckerProviderV1;
import com.ruckus.cloud.wifi.service.compatibility.ExtendedDeviceService;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApSerialNumberProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModel;
import com.ruckus.cloud.wifi.viewmodel.ApRequirementV1;
import java.util.List;
import java.util.stream.Stream;
import org.assertj.core.api.BDDAssertions;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

@SuppressWarnings("unchecked")
@WifiUnitTest
class ExtendedCompatibilityServiceImplV1Test {

  @SpyBean
  ExtendedCompatibilityServiceImplV1 unit;
  @MockBean
  private VenueRepository venueRepository;
  @MockBean
  private NetworkRepository networkRepository;
  @MockBean
  private NetworkVenueRepository networkVenueRepository;
  @MockBean
  private ApRepository apRepository;
  @MockBean
  private ExtendedDeviceService deviceService;
  @MockBean
  private ExtendedRequirementCheckerProviderV1 requirementCheckerProvider;
  @MockBean
  private ViewmodelClientGrpc viewmodelClient;

  @Test
  void whenGetRequirementCheckProvider() {
    BDDAssertions.then(unit.getRequirementCheckerProvider())
        .isNotNull()
        .isEqualTo(requirementCheckerProvider);
  }

  @Test
  void whenGetDeviceService() {
    BDDAssertions.then(unit.getDeviceService()).isNotNull().isEqualTo(deviceService);
  }

  @Nested
  class WhenCheckCompatibilitiesByNetworks {

    final List<String> networkIds =
        Stream.generate(CommonTestFixture::randomId).limit(3).toList();

    final List<DeviceAggregationGroup<ApFirmwareModel>> deviceAggregationGroups =
        networkIds.stream()
            .limit(8)
            .map(
                networkId ->
                    DeviceAggregationGroup.<ApFirmwareModel>builder()
                        .id(networkId)
                        .deviceAggregations(
                            (List<DeviceAggregation<ApFirmwareModel>>) mock(List.class))
                        .build())
            .toList();
    final List<RequirementCheckerGroup<ApRequirementV1, ApFirmwareModel>>
        networkCheckerGroups =
        networkIds.stream()
            .skip(1)
            .map(
                networkId ->
                    RequirementCheckerGroup.<ApRequirementV1, ApFirmwareModel>builder()
                        .id(networkId)
                        .requirementCheckers(
                            (List<RequirementChecker<ApRequirementV1, ApFirmwareModel>>)
                                mock(List.class))
                        .build())
            .toList();

    private Page<NetworkVenue> networkVenues;

    @BeforeEach
    void setup() {
      // Initialize networkVenues with mock data
      var network1 = new Network(networkIds.get(0));
      var network2 = new Network(networkIds.get(1));
      var network3 = new Network(networkIds.get(2));
      var venue1 = new Venue("venue1");
      var venue2 = new Venue("venue2");

      final var networkVenue1 = new NetworkVenue();
      networkVenue1.setId(randomId());
      networkVenue1.setNetwork(network1);
      networkVenue1.setVenue(venue1);

      final var networkVenue2 = new NetworkVenue();
      networkVenue2.setId(randomId());
      networkVenue2.setNetwork(network1);
      networkVenue2.setVenue(venue2);

      final var networkVenue3 = new NetworkVenue();
      networkVenue3.setId(randomId());
      networkVenue3.setNetwork(network2);
      networkVenue3.setVenue(venue1);

      final var networkVenue4 = new NetworkVenue();
      networkVenue4.setId(randomId());
      networkVenue4.setNetwork(network3);
      networkVenue4.setVenue(venue2);

      networkVenues = new PageImpl<>(
          List.of(networkVenue1, networkVenue2, networkVenue3, networkVenue4));

      // Mocking repository and service methods
      when(networkRepository.findByTenantId(nullable(Pageable.class), anyString()))
          .thenReturn(new PageImpl(Lists.newArrayList(network1, network2, network3)));
      when(deviceService.getDeviceAggregationGroupsByNetworks(anyList())).thenReturn(
          deviceAggregationGroups);
      when(requirementCheckerProvider.getRequirementCheckerGroupsByNetworks(anyList(), any(),
          anyList(), any()))
          .thenReturn(networkCheckerGroups);
      when(unit.getNetworkVenues(anyString(), anyList(), any(), any(Pageable.class)))
          .thenReturn(networkVenues);
    }

    @Test
    void thenCalculateCompatibilities() {
      willReturn(deviceAggregationGroups)
          .given(deviceService)
          .getDeviceAggregationGroupsByNetworks(anyList());
      willReturn(networkCheckerGroups)
          .given(requirementCheckerProvider)
          .getRequirementCheckerGroupsByNetworks(anyList(), any(), anyList(), any());
      willAnswer(
          invocation ->
              Compatibility.builder().id(invocation.getArgument(0, String.class)).build())
          .given(unit)
          .calculateCompatibility(anyString(), anyList(), anyList());
      BDDAssertions.then(
              unit.checkCompatibilitiesByNetworks(
                  anyList(),
                  anyList(),
                  anyList(),
                  anyList(),
                  any()))
          .isNotNull()
          .hasSize(networkIds.size())
          .extracting(Compatibility::getId)
          .containsExactlyElementsOf(networkIds);

      then(deviceService).should().getDeviceAggregationGroupsByNetworks(eq(networkIds));
      then(requirementCheckerProvider)
          .should()
          .getRequirementCheckerGroupsByNetworks(eq(networkIds), any(), anyList(), anyList());
      final var idCaptor = ArgumentCaptor.forClass(String.class);
      final var checkerCaptor = ArgumentCaptor.forClass(List.class);
      final var deviceAggregationCaptor = ArgumentCaptor.forClass(List.class);

      then(unit)
          .should(times(networkIds.size()))
          .calculateCompatibility(
              idCaptor.capture(), checkerCaptor.capture(), deviceAggregationCaptor.capture());
      final var ids = idCaptor.getAllValues();
      final var checkers = checkerCaptor.getAllValues();
      final var deviceAggregations = deviceAggregationCaptor.getAllValues();
      for (int i = 0; i < networkIds.size(); i++) {
        final var networkId = networkIds.get(i);
        final var checker =
            networkCheckerGroups.stream()
                .filter(c -> networkId.equals(c.getId()))
                .findFirst()
                .map(RequirementCheckerGroup::getRequirementCheckers)
                .orElse(List.of());
        final var deviceAggregation =
            deviceAggregationGroups.stream()
                .filter(d -> networkId.equals(d.getId()))
                .findFirst()
                .map(DeviceAggregationGroup::getDeviceAggregations)
                .orElse(List.of());
        assertThat(ids.get(i)).isEqualTo(networkId);
        assertThat(checkers.get(i)).isEqualTo(checker);
        assertThat(deviceAggregations.get(i)).isEqualTo(deviceAggregation);
      }
    }
  }

  @Nested
  class WhenCheckCompatibilitiesByVenues {

    final List<String> venueIds = Stream.generate(CommonTestFixture::randomId).limit(3).toList();
    final List<RequirementChecker<ApRequirementV1, ApFirmwareModel>> venueCheckers =
        Stream.<RequirementChecker<ApRequirementV1, ApFirmwareModel>>generate(
                () -> mock(RequirementChecker.class))
            .limit(5)
            .toList();
    final List<DeviceAggregationGroup<ApFirmwareModel>> deviceAggregationGroups =
        venueIds.stream()
            .limit(8)
            .map(
                venueId ->
                    DeviceAggregationGroup.<ApFirmwareModel>builder()
                        .id(venueId)
                        .deviceAggregations(
                            (List<DeviceAggregation<ApFirmwareModel>>) mock(List.class))
                        .build())
            .toList();
    final List<RequirementCheckerGroup<ApRequirementV1, ApFirmwareModel>>
        venueCheckerGroups =
        venueIds.stream()
            .skip(1)
            .map(
                venueId ->
                    RequirementCheckerGroup.<ApRequirementV1, ApFirmwareModel>builder()
                        .id(venueId)
                        .requirementCheckers(
                            (List<RequirementChecker<ApRequirementV1, ApFirmwareModel>>)
                                mock(List.class))
                        .build())
            .toList();

    private Page<NetworkVenue> networkVenues;

    @BeforeEach
    void setup() {
      // Initialize networkVenues with mock data
      var venue1 = new Venue(venueIds.get(0));
      var venue2 = new Venue(venueIds.get(1));
      var venue3 = new Venue(venueIds.get(2));
      var network1 = new Network("network1");
      var network2 = new Network("network2");

      final var networkVenue1 = new NetworkVenue();
      networkVenue1.setId(randomId());
      networkVenue1.setVenue(venue1);
      networkVenue1.setNetwork(network1);

      final var networkVenue2 = new NetworkVenue();
      networkVenue2.setId(randomId());
      networkVenue2.setVenue(venue1);
      networkVenue2.setNetwork(network2);

      final var networkVenue3 = new NetworkVenue();
      networkVenue3.setId(randomId());
      networkVenue3.setVenue(venue2);
      networkVenue3.setNetwork(network2);

      final var networkVenue4 = new NetworkVenue();
      networkVenue4.setId(randomId());
      networkVenue4.setVenue(venue3);
      networkVenue4.setNetwork(network2);

      networkVenues = new PageImpl<>(
          List.of(networkVenue1, networkVenue2, networkVenue3, networkVenue4));

      // Mocking repository and service methods
      when(venueRepository.findByTenantId(nullable(Pageable.class), anyString()))
          .thenReturn(new PageImpl(Lists.newArrayList(venue1, venue2, venue3)));
      when(deviceService.getDeviceAggregationGroups(anyList())).thenReturn(deviceAggregationGroups);
      when(requirementCheckerProvider.getRequirementCheckerGroupsByVenues(anyList(), any(),
          anyList(), any()))
          .thenReturn(venueCheckerGroups);
      when(unit.getNetworkVenues(anyString(), anyList(), any(), any(Pageable.class)))
          .thenReturn(networkVenues);
    }

    @Test
    void thenCalculateCompatibilities() {

      // Mocking behavior based on method logic
      willReturn(venueCheckers)
          .given(requirementCheckerProvider)
          .findRequirementCheckers(eq(venueIds), anyList(), anyList());
      willReturn(venueCheckerGroups)
          .given(requirementCheckerProvider)
          .getRequirementCheckerGroups(anyList());
      willReturn(deviceAggregationGroups)
          .given(deviceService)
          .getDeviceAggregationGroups(anyList());
      willAnswer(
          invocation ->
              Compatibility.builder().id(invocation.getArgument(0, String.class)).build())
          .given(unit)
          .calculateCompatibility(anyString(), anyList(), anyList());

      // Test case when `preCheck` is `false`
      BDDAssertions.then(unit.checkCompatibilitiesByVenues(
              anyList(),
              anyList(),
              anyList(),
              anyList(),
              anyBoolean(),
              any()))
          .isNotNull()
          .hasSize(venueIds.size())
          .extracting(Compatibility::getId)
          .containsExactlyElementsOf(venueIds);

      // Verify interactions
      then(deviceService).should().getDeviceAggregationGroups(eq(venueIds));
      then(requirementCheckerProvider).should()
          .getRequirementCheckerGroupsByVenues(eq(venueIds), any(), anyList(), anyList());

      final var idCaptor = ArgumentCaptor.forClass(String.class);
      final var checkerCaptor = ArgumentCaptor.forClass(List.class);
      final var deviceAggregationCaptor = ArgumentCaptor.forClass(List.class);

      then(unit)
          .should(times(venueIds.size()))
          .calculateCompatibility(
              idCaptor.capture(), checkerCaptor.capture(), deviceAggregationCaptor.capture());
      final var ids = idCaptor.getAllValues();
      final var checkers = checkerCaptor.getAllValues();
      final var deviceAggregations = deviceAggregationCaptor.getAllValues();
      for (int i = 0; i < venueIds.size(); i++) {
        final var venueId = venueIds.get(i);
        final var checker =
            venueCheckerGroups.stream()
                .filter(c -> venueId.equals(c.getId()))
                .findFirst()
                .map(RequirementCheckerGroup::getRequirementCheckers)
                .orElse(List.of());
        final var deviceAggregation =
            deviceAggregationGroups.stream()
                .filter(d -> venueId.equals(d.getId()))
                .findFirst()
                .map(DeviceAggregationGroup::getDeviceAggregations)
                .orElse(List.of());
        assertThat(ids.get(i)).isEqualTo(venueId);
        assertThat(checkers.get(i)).isEqualTo(checker);
        assertThat(deviceAggregations.get(i)).isEqualTo(deviceAggregation);
      }

    }
  }

  @Nested
  class WhenCheckCompatibilitiesByAps {

    final List<String> apIds = Stream.generate(CommonTestFixture::randomId).limit(3).toList();
    final List<String> venueIds = Stream.generate(CommonTestFixture::randomId).limit(3).toList();
    final List<RequirementChecker<ApRequirementV1, ApFirmwareModel>> checkers =
        Stream.<RequirementChecker<ApRequirementV1, ApFirmwareModel>>generate(
                () -> mock(RequirementChecker.class))
            .limit(5)
            .toList();
    final List<DeviceIdentityWrapper<ApFirmwareModel>> devices =
        apIds.stream()
            .map(
                id ->
                    DeviceIdentityWrapper.<ApFirmwareModel>builder()
                        .id(id)
                        .device(mock(ApFirmwareModel.class))
                        .build())
            .toList();
    private Page<NetworkVenue> networkVenues;

    @BeforeEach
    void setup() throws Exception {
      var ap1 = new Ap(apIds.get(0));
      var ap2 = new Ap(apIds.get(1));
      var ap3 = new Ap(apIds.get(2));
      // Initialize networkVenues with mock data
      var venue1 = new Venue(venueIds.get(0));
      var venue2 = new Venue(venueIds.get(1));
      var venue3 = new Venue(venueIds.get(2));
      var network1 = new Network("network1");
      var network2 = new Network("network2");

      final var networkVenue1 = new NetworkVenue();
      networkVenue1.setId(randomId());
      networkVenue1.setVenue(venue1);
      networkVenue1.setNetwork(network1);

      final var networkVenue2 = new NetworkVenue();
      networkVenue2.setId(randomId());
      networkVenue2.setVenue(venue1);
      networkVenue2.setNetwork(network2);

      final var networkVenue3 = new NetworkVenue();
      networkVenue3.setId(randomId());
      networkVenue3.setVenue(venue2);
      networkVenue3.setNetwork(network2);

      final var networkVenue4 = new NetworkVenue();
      networkVenue4.setId(randomId());
      networkVenue4.setVenue(venue3);
      networkVenue4.setNetwork(network2);

      final var venueApIds = List.of(
          new VenueApSerialNumberProjection(venue1.getId(), apIds.get(0)),
          new VenueApSerialNumberProjection(venue1.getId(), apIds.get(1)),
          new VenueApSerialNumberProjection(venue1.getId(), apIds.get(2))
      );
      networkVenues = new PageImpl<>(
          List.of(networkVenue1, networkVenue2, networkVenue3, networkVenue4));
      when(unit.getNetworkVenues(anyString(), anyList(), anyList(), any(Pageable.class)))
          .thenReturn(networkVenues);

      when(apRepository.findByTenantId(nullable(Pageable.class), anyString()))
          .thenReturn(new PageImpl(Lists.newArrayList(ap1, ap2, ap3)));
      when(apRepository.findVenueIdsByApSerialNumbers(anyList(), anyString())).thenReturn((venueApIds));
      when(
          requirementCheckerProvider.getRequirementCheckersForAps(anyString(), anyList(), anyList(),
              any()))
          .thenReturn(checkers);
      when(deviceService.getDevicesWithIdentity(anyList())).thenReturn(devices);
    }

    @Test
    void thenCalculateCompatibilities() throws Exception {
      BDDAssertions.then(unit.checkCompatibilitiesByAps(
              anyList(),
              anyList(),
              anyList(),
              anyList(),
              any()))
          .isNotNull()
          .hasSize(apIds.size())
          .extracting(Compatibility::getId)
          .containsExactlyElementsOf(apIds);

      // Verify interactions
      then(deviceService).should().getDevicesWithIdentity(eq(apIds));
      then(requirementCheckerProvider).should()
          .getRequirementCheckersForAps(eq(venueIds.get(0)), any(), anyList(), anyList());
    }
  }
}
