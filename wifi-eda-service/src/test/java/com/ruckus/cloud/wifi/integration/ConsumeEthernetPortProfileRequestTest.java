package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_AP_POE_OPERATING_MODE_SETTING_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertPrefixHeader;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.protobuf.BoolValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.PortNameEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueApModel;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelSpecific;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.servicemodel.SoftGreProfileLanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.SupplicantAuthenticationOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortAuthTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortSupplicantTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.PoeModeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.ApLanPortSettingsV1Generator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.EthernetPortProfileGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.VenueApModelLanPortSettingsV1Generator;
import com.ruckus.cloud.wifi.eda.viewmodel.ApLanPortSettingsV1;
import com.ruckus.cloud.wifi.eda.viewmodel.ApLanPortSpecificSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelLanPortSettingsV1;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelLanPortSpecificSettings;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.handler.cfgrequest.firmware.RevertTenantAbfRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.service.impl.LanPortAdoptionServiceImpl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApLanPortTestFixture;
import com.ruckus.cloud.wifi.test.fixture.EthernetPortProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.LanPortAdoptionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.RadiusTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueApModelSpecificAttributesTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueLanPortTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Tag("EthernetPortProfileTest")
@FeatureFlag(enable = {
    FlagNames.ACX_UI_ETHERNET_TOGGLE,
    FlagNames.ACX_ETHERNET_PORT_SUPPORT_PROXY_RADIUS_TOGGLE })
@WifiIntegrationTest
class ConsumeEthernetPortProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  @Autowired
  private LanPortAdoptionDataHelper dataHelper;

  @Autowired
  private LanPortAdoptionServiceImpl lanPortAdoptionService;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private RevertTenantAbfRequestHandler revertTenantAbfRequestHandler;

  @Nested
  @ApiAction(CfgAction.ADD_ETHERNET_PORT_PROFILE)
  class ConsumeAddRequestTest {

    @Payload
    private final EthernetPortProfileGenerator generator = Generators.ethernetPortProfile()
        .setAuthType(always(ApLanPortAuthTypeEnum.PORT_BASED_AUTHENTICATOR))
        .setEnableAuthProxy(always(true));

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.EthernetPortProfile payload) {
      validateResult(CfgAction.ADD_ETHERNET_PORT_PROFILE, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_ETHERNET_PORT_PROFILE)
  class ConsumeUpdateRequestTest {

    @Payload
    private final EthernetPortProfileGenerator generator = Generators.ethernetPortProfile()
        .setId(nullValue(String.class))
        .setName(serialName("UpdatedProfile"));

    private String profileId;

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile) {
      ethernetPortProfile.setName("OldProfile");
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());
      profileId = ethernetPortProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("ethernetPortProfileId", profileId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.EthernetPortProfile payload) {
      validateResult(CfgAction.UPDATE_ETHERNET_PORT_PROFILE, profileId, payload);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.UPDATE_ETHERNET_PORT_PROFILE)
  class ConsumeUpdateRequestTestWithoutLanPortAdoption {

    @Payload
    private final EthernetPortProfileGenerator generator = Generators.ethernetPortProfile()
        .setId(nullValue(String.class))
        .setName(serialName("UpdatedProfile"));

    private String profileId;
    private String adoptionId;

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile) {
      ethernetPortProfile.setName("OldProfile");
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());
      var adoption = new LanPortAdoption(randomId());
      adoption.setEthernetPortProfileId(777);
      adoption.setApLanPortProfile(ethernetPortProfile);
      adoption.setChecksum(lanPortAdoptionService.calculateChecksum(adoption));
      repositoryUtil.createOrUpdate(adoption, txCtxExtension.getTenantId(), randomTxId());
      profileId = ethernetPortProfile.getId();
      adoptionId = adoption.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("ethernetPortProfileId", profileId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.EthernetPortProfile payload) {
      validateResult(CfgAction.UPDATE_ETHERNET_PORT_PROFILE, profileId, payload, adoptionId);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_ETHERNET_PORT_PROFILE)
  class ConsumeDeleteRequestTest {

    private String profileId;

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile) {
      profileId = ethernetPortProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("ethernetPortProfileId", profileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateResult(CfgAction.DELETE_ETHERNET_PORT_PROFILE, profileId, null);
    }
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE)
  class ConsumeActivateRadiusToEthernetProfileRequestTest {

    private String ethernetPortProfileId;
    private String radiusServerProfileId;

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile, final Radius radius) {
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.MAC_BASED_AUTHENTICATOR);
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());
      ethernetPortProfileId = ethernetPortProfile.getId();
      radiusServerProfileId = radius.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("ethernetPortProfileId", ethernetPortProfileId)
          .addPathVariable("radiusServerProfileId", radiusServerProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateRadiusActivationResult(CfgAction.ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE,
          ethernetPortProfileId, radiusServerProfileId);
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE)
  class ConsumeDeactivateRadiusToEthernetProfileRequestTest {

    private String ethernetPortProfileId;
    private String radiusServerProfileId;

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile) {
      final Radius radius = RadiusTestFixture.authRadius();
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.MAC_BASED_AUTHENTICATOR);
      ethernetPortProfile.setAuthRadius(radius);
      radius.setAuthEthernetPortProfiles(List.of(ethernetPortProfile));
      repositoryUtil.createOrUpdate(radius, txCtxExtension.getTenantId(), randomTxId());
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());
      ethernetPortProfileId = ethernetPortProfile.getId();
      radiusServerProfileId = radius.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("ethernetPortProfileId", ethernetPortProfileId)
          .addPathVariable("radiusServerProfileId", radiusServerProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateRadiusActivationResult(CfgAction.DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE,
          ethernetPortProfileId, radiusServerProfileId);
    }
  }

  @Test
  void testActivateEthernetPortProfileToVenueApModelLanPort_apply_to_multi_port(
      Tenant tenant, Venue venue, EthernetPortProfile ethernetPortProfile) throws Exception {

    // save eth port profile
    ethernetPortProfile.setName("port1");
    ethernetPortProfile.setApLanPortId(1);
    ethernetPortProfile.setType(ApLanPortTypeEnum.TRUNK);
    ethernetPortProfile.setUntagId((short) 1);
    ethernetPortProfile.setVlanMembers("1-4094");
    ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
    ethernetPortProfile.setTenant(tenant);
    repositoryUtil.createOrUpdate(ethernetPortProfile, tenant.getId(), randomTxId());

    EthernetPortProfile ethernetPortProfile2 = new EthernetPortProfile();
    ethernetPortProfile2.setId(randomTxId());
    ethernetPortProfile2.setName("port2");
    ethernetPortProfile2.setApLanPortId(2);
    ethernetPortProfile2.setType(ApLanPortTypeEnum.ACCESS);
    ethernetPortProfile2.setUntagId((short) 2);
    ethernetPortProfile2.setVlanMembers("2");
    ethernetPortProfile2.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
    ethernetPortProfile2.setTenant(tenant);
    repositoryUtil.createOrUpdate(ethernetPortProfile2, tenant.getId(), randomTxId());

    CfgAction action = CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT;
    String ethernetPortProfileId = ethernetPortProfile.getId();
    String ethernetPortProfileId2 = ethernetPortProfile2.getId();
    String venueId = venue.getId();
    String apModel = "R500";

    sendActivateEthernetPortProfileToVenueApModelLanPortRequestParams(
        venueId, apModel, "1", ethernetPortProfileId, tenant);

    sendActivateEthernetPortProfileToVenueApModelLanPortRequestParams(
        venueId, apModel, "2", ethernetPortProfileId2, tenant);

    String apModel3 = "R610";
    sendActivateEthernetPortProfileToVenueApModelLanPortRequestParams(
        venueId, apModel3, "1", ethernetPortProfileId, tenant);


    assertAll("assert activity status success",
        () -> validateVenueRepositoryData(action,
            venueId, apModel, "1", ethernetPortProfileId),
        () -> validateVenueRepositoryData(action,
            venueId, apModel, "2", ethernetPortProfileId2),
        () -> validateVenueRepositoryData(action,
            venueId, apModel3, "1", ethernetPortProfileId)
    );
  }

  private void sendActivateEthernetPortProfileToVenueApModelLanPortRequestParams(
      String venueId, String apModel, String portId, String ethernetPortProfileId, Tenant tenant) {

    final var requestId = randomTxId();
    RequestParams req = new RequestParams().addPathVariable("venueId", venueId)
        .addPathVariable("apModel", apModel)
        .addPathVariable("portId", portId)
        .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);

    messageUtil.sendWifiCfgRequest(tenant.getId(), requestId, CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT,
        tenant.getName(), req, "");
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT)
  class ConsumeActivateEthernetPortProfileToVenueApModelLanPortRequestTest {

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "1";

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile, final Venue venue) {
      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT,
          venueId, apModel, portId, ethernetPortProfileId);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT)
  class ConsumeActivateEthernetPortProfileToVenueApModelLanPortRequestWithMigratedToLanPortAdoptionTest {

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "1";

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile, final Venue venue) {
      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(
          ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT,
          venueId,
          apModel,
          portId,
          ethernetPortProfileId,
          true);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT)
  class ConsumeActivateEthernetPortProfileToVenueApModelLanPortRequestWithMigratedToLanPortAdoptionAlreadyTest {

    private String profileId;
    private String venueId;
    private String apModel = "R320";
    private String portId = "1";

    @BeforeEach
    void prepareData(final Venue venue) {
      var profile1 = createEthernetPortProfile(venue, 100);
      var adoption = createLanPortAdoption(profile1, 888);
      var modelSpecific = createModelSpecific(venue, apModel);
      var port1 = createPort(venue, modelSpecific, profile1, adoption, portId);
      modelSpecific.setLanPorts(List.of(port1));
      venue.setModelSpecificAttributes(List.of(modelSpecific));

      var profile2 = createEthernetPortProfile(venue, 101);
      profileId = profile2.getId();
      venueId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", profileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT,
          venueId,
          apModel,
          portId,
          profileId,
          true);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT)
  class ConsumeActivateEthernetPortProfileToVenueApModelLanPortRequestWithMigratedToLanPortAdoptionAlreadyAndChangeToAnotherProfileTest {

    private String tenantId;
    private String profileId;
    private String venueId;
    private String apModel = "R320";
    private String portId = "1";
    private String softGreProfileId;
    private String clientIsolationProfileId;

    @BeforeEach
    void prepareData(final Venue venue) {
      tenantId = venue.getTenant().getId();
      venueId = venue.getId();
      var softGreProfile = dataHelper.createSoftGreProfile(venue.getTenant());
      var clientIsolationProfile = dataHelper.createClientIsolationAllowlist(venue.getTenant());
      softGreProfileId = softGreProfile.getId();
      clientIsolationProfileId = clientIsolationProfile.getId();
      var softGreActivation = new SoftGreProfileLanPortActivation();
      softGreActivation.setSoftGreProfile(softGreProfile);
      var clientIsolationActivation = new ClientIsolationLanPortActivation();
      clientIsolationActivation.setClientIsolationAllowlist(clientIsolationProfile);
      dataHelper.createVenueLanPortDataWithAdoption(
          venue, apModel, portId, 3, of(softGreActivation, clientIsolationActivation));
      var profile = dataHelper.createEthernetPortProfile(venue.getTenant(), 999);

      profileId = profile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", profileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT,
          venueId,
          apModel,
          portId,
          profileId,
          true);
      var adoptions = repositoryUtil.findAll(LanPortAdoption.class, tenantId);
      assertThat(adoptions).hasSize(1);
      var adoption = adoptions.get(0);
      assertThat(adoption.getSoftGreActivation())
          .isNotNull()
          .extracting(SoftGreProfileLanPortActivation::getSoftGreProfile)
          .matches(s -> s.getId().equals(softGreProfileId));
      assertThat(adoption.getClientIsolationActivation())
          .isNotNull()
          .extracting(ClientIsolationLanPortActivation::getClientIsolationAllowlist)
          .matches(c -> c.getId().equals(clientIsolationProfileId));
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT)
  class ConsumeActivateEthernetPortProfileToVenueApModelLanPortRequestWithMultipleVenues {

    private String tenantId;
    private String profileId;
    private String venueId1;
    private String venueId2;
    private String apModel = "R320";
    private String portId = "1";

    @BeforeEach
    void prepareData(Venue venue) {
      tenantId = venue.getTenant().getId();
      venueId1 = venue.getId();
      dataHelper.createVenueLanPortData(venue, apModel, portId, 3);
      var profile = dataHelper.createEthernetPortProfile(venue.getTenant(), 999);
      profileId = profile.getId();

      var venue2 = dataHelper.createVenue(venue.getTenant());
      venueId2 = venue2.getId();
      dataHelper.createVenueLanPortData(venue2, apModel, portId, 3);
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId1)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", profileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      final var requestId = txCtxExtension.getRequestId();
      final var ddccmMessagesVenue1 =
          messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

      assertThat(ddccmMessagesVenue1)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload)
          .isNotNull();
      assertThatNoException()
          .isThrownBy(
              () -> {
                var operations =
                    assertThat(ddccmMessagesVenue1.getPayload())
                        .extracting(WifiConfigRequest::getOperationsList)
                        .asList()
                        .isNotEmpty()
                        .extracting(Operation.class::cast);
                operations.filteredOn(Operation::hasApLanPortProfile).hasSize(1);
                operations
                    .filteredOn(Operation::hasVenue)
                    .extracting(Operation::getVenue)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getId)
                    .contains(venueId1);
              });

      final var asyncJobMessages = messageCaptors.getWifiAsyncJobMessageCaptor().getValue(tenantId);
      assertThat(asyncJobMessages)
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(WifiAsyncJob::hasLanPortAdoptionMigrationJob)
          .matches(job -> job.getLanPortAdoptionMigrationJob().getVenueId().equals(venueId2));

      final var ddccmMessagesVenue2 =
          messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

      assertThat(ddccmMessagesVenue2)
          .isNotNull()
          .satisfies(assertPrefixHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload)
          .isNotNull();
      assertThatNoException()
          .isThrownBy(
              () -> {
                var operations =
                    assertThat(ddccmMessagesVenue2.getPayload())
                        .extracting(WifiConfigRequest::getOperationsList)
                        .asList()
                        .isNotEmpty()
                        .extracting(Operation.class::cast);
                operations.filteredOn(Operation::hasApLanPortProfile).hasSize(1);
                operations
                    .filteredOn(Operation::hasVenue)
                    .extracting(Operation::getVenue)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getId)
                    .contains(venueId2);
              });
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT)
  class ConsumeDeactivateEthernetPortProfileToVenueApModelLanPortRequestTest {

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "1";

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile, final Venue venue) {
      venue.setApPassword("password");
      repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), randomTxId());

      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      // save venue
      VenueApModelSpecificAttributes attr = new VenueApModelSpecificAttributes();
      attr.setModel("R500");
      VenueLanPort venueLanPort = new VenueLanPort();
      venueLanPort.setApLanPortProfile(ethernetPortProfile);
      venueLanPort.setVenueApModelSpecificAttributes(attr);
      attr.setLanPorts(List.of(venueLanPort));
      venue.setModelSpecificAttributes(List.of(attr));
      repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT,
          venueId, apModel, portId, null);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT)
  class ConsumeDeactivateEthernetPortProfileToVenueApModelLanPortRequestWithMigratedToLanPortAdoptionTest {

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "1";

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile, final Venue venue) {
      venue.setApPassword("password");
      repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), randomTxId());

      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(
          ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      // save venue
      VenueApModelSpecificAttributes attr = new VenueApModelSpecificAttributes();
      attr.setModel("R500");
      VenueLanPort venueLanPort = new VenueLanPort();
      venueLanPort.setApLanPortProfile(ethernetPortProfile);
      venueLanPort.setVenueApModelSpecificAttributes(attr);
      attr.setLanPorts(List.of(venueLanPort));
      venue.setModelSpecificAttributes(List.of(attr));
      repositoryUtil.createOrUpdate(venue, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT,
          venueId,
          apModel,
          portId,
          null,
          true);
    }
  }

  @Test
  void testActivateEthernetPortProfileToApLanPort_apply_to_multi_port(
      Tenant tenant, Venue venue, EthernetPortProfile ethernetPortProfile,
      final ApGroup apGroup, final Ap ap) throws Exception {

    // save eth port profile
    ethernetPortProfile.setName("port1");
    ethernetPortProfile.setApLanPortId(1);
    ethernetPortProfile.setType(ApLanPortTypeEnum.TRUNK);
    ethernetPortProfile.setUntagId((short) 1);
    ethernetPortProfile.setVlanMembers("1-4094");
    ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
    ethernetPortProfile.setTenant(tenant);
    repositoryUtil.createOrUpdate(ethernetPortProfile, tenant.getId(), randomTxId());

    EthernetPortProfile ethernetPortProfile2 = new EthernetPortProfile();
    ethernetPortProfile2.setId(randomTxId());
    ethernetPortProfile2.setName("port2");
    ethernetPortProfile2.setApLanPortId(2);
    ethernetPortProfile2.setType(ApLanPortTypeEnum.ACCESS);
    ethernetPortProfile2.setUntagId((short) 2);
    ethernetPortProfile2.setVlanMembers("2");
    ethernetPortProfile2.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
    ethernetPortProfile2.setTenant(tenant);
    repositoryUtil.createOrUpdate(ethernetPortProfile2, tenant.getId(), randomTxId());

    // save ap
    apGroup.setVenue(venue);
    ap.setName(randomName());
    ap.setModel("R500");
    ap.setApGroup(apGroup);
    repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());

    CfgAction action = CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT;
    String ethernetPortProfileId = ethernetPortProfile.getId();
    String ethernetPortProfileId2 = ethernetPortProfile2.getId();
    String venueId = venue.getId();
    String serialNumber = ap.getId();

    sendActivateEthernetPortProfileToApLanPortRequestParams(
        venueId, serialNumber, "1", ethernetPortProfileId, tenant);

    sendActivateEthernetPortProfileToApLanPortRequestParams(
        venueId, serialNumber, "2", ethernetPortProfileId2, tenant);

    assertAll("assert activity status success",
        () -> validateApRepositoryData(action,
            serialNumber, "1", ethernetPortProfileId),
        () -> validateApRepositoryData(action,
            serialNumber, "2", ethernetPortProfileId2)
    );
  }

  private void sendActivateEthernetPortProfileToApLanPortRequestParams(
      String venueId, String serialNumber, String portId, String ethernetPortProfileId, Tenant tenant) {

    final var requestId = randomTxId();
    RequestParams req = new RequestParams().addPathVariable("venueId", venueId)
        .addPathVariable("serialNumber", serialNumber)
        .addPathVariable("portId", portId)
        .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);

    messageUtil.sendWifiCfgRequest(tenant.getId(), requestId, CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT,
        tenant.getName(), req, "");
  }

  @Nested
  @ApiAction(CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT)
  class ConsumeActivateEthernetPortProfileToApLanPortRequestTest {

    private String ethernetPortProfileId;
    private String venueId = randomId();;
    private String serialNumber;
    private String portId = "1";

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile, final ApGroup apGroup,
        final Venue venue, final Ap ap) {
      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      // save ap
      apGroup.setVenue(venue);
      ap.setName(randomName());
      ap.setModel("R500");
      ap.setApGroup(apGroup);
      repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = apGroup.getVenue().getId();
      serialNumber = ap.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToApLanPortActivationResult(
          CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT,
          venueId, serialNumber, portId, ethernetPortProfileId);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT)
  class ConsumeActivateEthernetPortProfileToApLanPortRequestWithMigratedToLanPortAdoptionTest {

    private String ethernetPortProfileId;
    private String venueId = randomId();
    private String serialNumber;
    private String portId = "1";

    @BeforeEach
    void givenOnePersistedInDb(
        final EthernetPortProfile ethernetPortProfile,
        final ApGroup apGroup,
        final Venue venue,
        final Ap ap) {
      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(
          ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      // save ap
      apGroup.setVenue(venue);
      ap.setName(randomName());
      ap.setModel("R500");
      ap.setApGroup(apGroup);
      repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = apGroup.getVenue().getId();
      serialNumber = ap.getId();

    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToApLanPortActivationResult(
          CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT,
          venueId,
          serialNumber,
          portId,
          ethernetPortProfileId,
          true);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT)
  class ConsumeActivateEthernetPortProfileToApLanPortRequestWithMigratedToLanPortAdoptionAlreadyTest {

    private String profileId;
    private String venueId = randomId();
    private String serialNumber;
    private String portId = "1";

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue,
        final ApGroup apGroup,
        final @ApModel("R320") Ap ap) {
      initAp(ap, apGroup);
      var profile1 = createEthernetPortProfile(venue, 100);
      var adoption = createLanPortAdoption(profile1, 888);
      var port1 = createPort(venue, ap.getModelSpecific(), profile1, adoption, portId);
      ap.getModelSpecific().setLanPorts(List.of(port1));

      var profile2 = createEthernetPortProfile(venue, 101);
      profileId = profile2.getId();
      venueId = apGroup.getVenue().getId();
      serialNumber = ap.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", profileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToApLanPortActivationResult(
          CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT,
          venueId,
          serialNumber,
          portId,
          profileId,
          true);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT)
  class ConsumeActivateEthernetPortProfileToApLanPortRequestWithMultipleAps {

    private String tenantId;
    private String venueId;
    private String serialNumber1;
    private String serialNumber2;
    private String portId = "1";
    private String profileId;

    @BeforeEach
    void prepareData(final Venue venue) {
      var ap1 = dataHelper.createAp(venue);
      ap1.setModel("R320");
      var ap2 = dataHelper.createAp(venue);
      ap2.setModel("R320");
      dataHelper.createApLanPortData(venue, ap1, "1", 777);
      dataHelper.createApLanPortData(venue, ap2, "1", 888);
      var profile = dataHelper.createEthernetPortProfile(venue.getTenant(), 999);

      tenantId = venue.getTenant().getId();
      venueId = venue.getId();
      serialNumber1 = ap1.getId();
      serialNumber2 = ap2.getId();
      profileId = profile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber1)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", profileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      final var requestId = txCtxExtension.getRequestId();
      final var ddccmMessages =
          messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

      assertThat(ddccmMessages)
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload)
          .isNotNull();

      assertThatNoException()
          .isThrownBy(
              () -> {
                var operations =
                    assertThat(ddccmMessages.getPayload())
                        .extracting(WifiConfigRequest::getOperationsList)
                        .asList()
                        .isNotEmpty()
                        .extracting(Operation.class::cast);
                operations.filteredOn(Operation::hasApLanPortProfile).hasSize(2);
                operations
                    .filteredOn(Operation::hasAp)
                    .extracting(Operation::getAp)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getId)
                    .contains(serialNumber1, serialNumber2);
              });
    }
  }

  @Nested
  @ApiAction(CfgAction.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT)
  class ConsumeDeactivateEthernetPortProfileToApLanPortRequestTest {

    private String ethernetPortProfileId;
    private String venueId = randomId();;
    private String serialNumber;
    private String portId = "1";

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile, final ApGroup apGroup,
        final Venue venue, final Ap ap) {

      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      apGroup.setVenue(venue);
      ap.setName(randomName());
      ap.setModel("R500");
      ap.setApGroup(apGroup);
      repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = apGroup.getVenue().getId();
      serialNumber = ap.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToApLanPortActivationResult(
          CfgAction.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT,
          venueId, serialNumber, portId, null);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT)
  class ConsumeDeactivateEthernetPortProfileToApLanPortRequestWithMigratedToLanPortAdoptionTest {

    private String ethernetPortProfileId;
    private String venueId = randomId();
    private String serialNumber;
    private String portId = "1";

    @BeforeEach
    void givenOnePersistedInDb(
        final EthernetPortProfile ethernetPortProfile,
        final ApGroup apGroup,
        final Venue venue,
        final Ap ap) {

      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(
          ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      apGroup.setVenue(venue);
      ap.setName(randomName());
      ap.setModel("R500");
      ap.setApGroup(apGroup);
      repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = apGroup.getVenue().getId();
      serialNumber = ap.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId)
          .addPathVariable("ethernetPortProfileId", ethernetPortProfileId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      validateEthernetPortProfileToApLanPortActivationResult(
          CfgAction.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT,
          venueId,
          serialNumber,
          portId,
          null,
          true);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeUpdateVenueApModelLanPortOverwriteSettingsRequestTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator = Generators.venueApModelLanPortSettingsV1()
        .setEnabled(always(false));

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "2";

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile, final ApGroup apGroup,
        final Venue venue, final Ap ap) throws CommonException {

      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS,
          venueId, apModel, portId, ethernetPortProfileId);
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeUpdateVenueApModelLanPortOverwriteSettingsWithMigratedToLanPortAdoptionTest {

    @Payload
    private final VenueApModelLanPortSettingsV1Generator generator =
        Generators.venueApModelLanPortSettingsV1().setEnabled(always(false));

    private String ethernetPortProfileId;
    private String venueId;
    private String apModel = "R500";
    private String portId = "2";

    @BeforeEach
    void givenOnePersistedInDb(
        final EthernetPortProfile ethernetPortProfile,
        final ApGroup apGroup,
        final Venue venue,
        final Ap ap)
        throws CommonException {

      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.ACCESS);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("2");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(
          ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = venue.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("apModel", apModel)
          .addPathVariable("portId", portId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload VenueApModelLanPortSettingsV1 payload) {
      validateEthernetPortProfileToVenueApModelLanPortActivationResult(
          CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS,
          venueId,
          apModel,
          portId,
          ethernetPortProfileId,
          true);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeUpdateApLanPortOverwriteSettingsRequestTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator = Generators.apLanPortSettingsV1()
        .setOverwriteUntagId(always((short) 3))
        .setOverwriteVlanMembers(always("1-4094"))
        .setEnabled(always(false));

    private String ethernetPortProfileId;
    private String venueId = randomId();;
    private String serialNumber;
    private String portId = "2";

    @BeforeEach
    void givenOnePersistedInDb(final EthernetPortProfile ethernetPortProfile, final ApGroup apGroup,
        final Venue venue, final Ap ap) {
      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.TRUNK);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("1-4094");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      // save ap
      apGroup.setVenue(venue);
      ap.setName(randomName());
      ap.setModel("R500");
      ap.setApGroup(apGroup);
      repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = apGroup.getVenue().getId();
      serialNumber = ap.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      validateEthernetPortProfileToApLanPortActivationResult(
          CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS,
          venueId, serialNumber, portId, ethernetPortProfileId, false, payload.getEnabled());
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully_withPortEnabled(@Payload ApLanPortSettingsV1 payload) {
      payload.setEnabled(true);
      messageUtil.sendWifiCfgRequest(txCtxExtension.getTenantId(), randomTxId(),
          CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS,
          randomName(), requestParams(), payload);

      validateEthernetPortProfileToApLanPortActivationResult(
          CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS,
          venueId, serialNumber, portId, ethernetPortProfileId, false, payload.getEnabled());
    }
  }

  @Nested
  @FeatureFlag(enable = FlagNames.WIFI_ETHERNET_SOFTGRE_TOGGLE)
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeUpdateApLanPortOverwriteSettingsRequestWithMigratedToLanPortAdoptionTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator =
        Generators.apLanPortSettingsV1()
            .setOverwriteUntagId(always((short) 3))
            .setOverwriteVlanMembers(always("1-4094"))
            .setEnabled(always(false));

    private String ethernetPortProfileId;
    private String venueId = randomId();
    private String serialNumber;
    private String portId = "2";

    @BeforeEach
    void givenOnePersistedInDb(
        final EthernetPortProfile ethernetPortProfile,
        final ApGroup apGroup,
        final Venue venue,
        final Ap ap) {
      // save eth port profile
      ethernetPortProfile.setName(randomName());
      ethernetPortProfile.setType(ApLanPortTypeEnum.TRUNK);
      ethernetPortProfile.setUntagId((short) 2);
      ethernetPortProfile.setVlanMembers("1-4094");
      ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
      repositoryUtil.createOrUpdate(
          ethernetPortProfile, txCtxExtension.getTenantId(), randomTxId());

      // save ap
      apGroup.setVenue(venue);
      ap.setName(randomName());
      ap.setModel("R500");
      ap.setApGroup(apGroup);
      repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());

      ethernetPortProfileId = ethernetPortProfile.getId();
      venueId = apGroup.getVenue().getId();
      serialNumber = ap.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Disabled("FIXME: failed by assertNotSent")
    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      validateEthernetPortProfileToApLanPortActivationResult(
          CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS,
          venueId,
          serialNumber,
          portId,
          ethernetPortProfileId,
          true,
          payload.getEnabled());
    }
  }

  @Test
  void testUpdateVenueApModelLanPortSpecificSettings(Tenant tenant, Venue venue) {
    VenueApModelLanPortSpecificSettings H550Payload = new VenueApModelLanPortSpecificSettings();
    H550Payload.setPoeMode(PoeModeEnum._802_3af);
    H550Payload.setPoeOut(true);
    sendUpdateVenueApModelLanPortSpecificSettingsRequestParams(venue.getId(), tenant, "H550", H550Payload);

    validateUpdateVenueApModelLanPortSpecificSettings(venue.getId(), "H550", PoeModeEnum._802_3af, true);
  }

  @Test
  void testUpdateVenueApModelLanPortSpecificSettingsNoPoeMode(Tenant tenant, Venue venue) {
    VenueApModelLanPortSpecificSettings T710Payload = new VenueApModelLanPortSpecificSettings();
    T710Payload.setPoeOut(true);
    sendUpdateVenueApModelLanPortSpecificSettingsRequestParams(venue.getId(), tenant, "T710", T710Payload);

    validateUpdateVenueApModelLanPortSpecificSettings(venue.getId(), "T710", null, true);
  }

  @Test
  void testUpdateVenueApModelLanPortSpecificSettingsWithLanPorts(Tenant tenant, Venue venue) {

    String venueId = venue.getId();
    String apModel = "H550";

    // save eth port profile
    EthernetPortProfile ethernetPortProfile = new EthernetPortProfile();
    ethernetPortProfile.setName("port1");
    ethernetPortProfile.setApLanPortId(1);
    ethernetPortProfile.setType(ApLanPortTypeEnum.TRUNK);
    ethernetPortProfile.setUntagId((short) 1);
    ethernetPortProfile.setVlanMembers("1-4094");
    ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
    ethernetPortProfile.setTenant(tenant);
    ethernetPortProfile = repositoryUtil.createOrUpdate(ethernetPortProfile, tenant.getId(), randomTxId());
    String ethernetPortProfileId = ethernetPortProfile.getId();

    sendActivateEthernetPortProfileToVenueApModelLanPortRequestParams(
        venueId, apModel, "1", ethernetPortProfileId, tenant);

    CfgAction action = CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT;
    assertAll("assert activity status success",
        () -> validateVenueRepositoryData(action,
            venueId, apModel, "1", ethernetPortProfileId));

    VenueApModelLanPortSpecificSettings H550Payload = new VenueApModelLanPortSpecificSettings();
    H550Payload.setPoeMode(PoeModeEnum._802_3bt_Class_5);
    H550Payload.setPoeOut(true);
    sendUpdateVenueApModelLanPortSpecificSettingsRequestParams(venue.getId(), tenant, "H550", H550Payload);

    validateUpdateVenueApModelLanPortSpecificSettings(venue.getId(), "H550", PoeModeEnum._802_3bt_Class_5, true);
    final Venue result = repositoryUtil.find(Venue.class, venueId);
    assertThat(result)
        .extracting(Venue::getModelSpecificAttributes)
        .extracting(attrs -> attrs.stream()
            .filter(attr -> attr.getModel().equals(apModel)).findAny().orElse(null))
        .matches(attr -> Objects.equals(attr.getPoeMode(), PoeModeEnum._802_3bt_Class_5))
        .matches(attr -> attr.getLanPorts().stream()
            .map(VenueLanPort::getApLanPortProfile)
            .map(EthernetPortProfile::getName)
            .filter(name -> name.equals("port1")).findAny().isPresent());
  }

  @Test
  void testUpdateVenueApModelLanPortTwoSupplicant(Tenant tenant, Venue venue) {

    String venueId = venue.getId();
    String apModel = "R550";

    // save eth port profile
    EthernetPortProfile ethernetPortProfile = new EthernetPortProfile();
    ethernetPortProfile.setName("port1");
    ethernetPortProfile.setApLanPortId(5);
    ethernetPortProfile.setType(ApLanPortTypeEnum.TRUNK);
    ethernetPortProfile.setUntagId((short) 1);
    ethernetPortProfile.setVlanMembers("1-4094");
    ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.SUPPLICANT);

    SupplicantAuthenticationOptions supplicantAuthenticationOptions = new SupplicantAuthenticationOptions();
    supplicantAuthenticationOptions.setType(ApLanPortSupplicantTypeEnum.MAC_AUTH);
    ethernetPortProfile.setSupplicantAuthenticationOptions(supplicantAuthenticationOptions);

    ethernetPortProfile.setTenant(tenant);
    ethernetPortProfile = repositoryUtil.createOrUpdate(ethernetPortProfile, tenant.getId(), randomTxId());
    String ethernetPortProfileId = ethernetPortProfile.getId();

    sendActivateEthernetPortProfileToVenueApModelLanPortRequestParams(
        venueId, apModel, "1", ethernetPortProfileId, tenant);

    // one supplicant should success
    CfgAction action = CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT;
    assertAll("assert activity status success",
        () -> validateVenueRepositoryData(action,
            venueId, apModel, "1", ethernetPortProfileId));

    // apply again should success
    sendActivateEthernetPortProfileToVenueApModelLanPortRequestParams(
        venueId, apModel, "1", ethernetPortProfileId, tenant);

    // two supplicant should fail
    RuntimeException ex = assertThrows(RuntimeException.class, () ->
        sendActivateEthernetPortProfileToVenueApModelLanPortRequestParams(
            venueId, apModel, "2", ethernetPortProfileId, tenant));
    assertTrue(StringUtils.contains(ex.getCause().getCause().getMessage(),
        "Only allow 1 Supplicant type Ethernet port profile"));
  }

  private void validateUpdateVenueApModelLanPortSpecificSettings(
      String venueId, String apModel, PoeModeEnum poeMode, boolean poeOut) {

    final Venue result = repositoryUtil.find(Venue.class, venueId);
    assertThat(result)
        .extracting(Venue::getModelSpecificAttributes)
        .extracting(attrs -> attrs.stream()
            .filter(attr -> attr.getModel().equals(apModel)).findAny().orElse(null))
        .matches(attr -> Objects.equals(attr.getPoeMode(), poeMode))
        .matches(attr -> Objects.equals(attr.getPoeOut(), poeOut));

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    // Verify message headers and payload
    assertThat(ddccmMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    // Get operations from payload
    List<Operation> operations = ddccmMessage.getPayload().getOperationsList();

    // Verify venue operation
    assertThat(operations)
        .filteredOn(Operation::hasVenue)
        .singleElement()
        .satisfies(op -> {
          assertThat(op.getId()).isEqualTo(venueId);
          assertThat(op.getAction()).isEqualTo(Action.MODIFY);

          // Verify VenueApModel in venue operation
          List<VenueApModel> apModels = op.getVenue().getVenueApModelsList();
          assertThat(apModels)
              .hasSize(1)
              .singleElement()
              .satisfies(model -> {
                assertThat(model.getModelName()).isEqualTo(apModel);
                Optional.ofNullable(poeMode).ifPresent(p ->
                    assertThat(model.getPoeModeSetting()).isEqualTo(
                        com.ruckus.acx.ddccm.protobuf.wifi.PoeModeEnum.valueOf(p.name())));
              });
        });
  }

  private void sendUpdateVenueApModelLanPortSpecificSettingsRequestParams(
      String venueId, Tenant tenant, String apModel, VenueApModelLanPortSpecificSettings payload) {

    final var requestId = txCtxExtension.getRequestId();
    RequestParams req = new RequestParams()
        .addPathVariable("venueId", venueId)
        .addPathVariable("apModel", apModel);

    messageUtil.sendWifiCfgRequest(tenant.getId(), requestId,
        CfgAction.UPDATE_VENUE_AP_MODEL_LAN_PORT_SPECIFIC_SETTINGS,
        tenant.getName(), req, payload);
  }

  @Test
  void testUpdateApLanPortSpecificSettings(Tenant tenant, Venue venue, Ap ap) {

    String venueId = venue.getId();
    String serialNumber = ap.getId();

    ap.setModel("H550");
    repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());

    ApLanPortSpecificSettings apLanPortSpecificSettings = new ApLanPortSpecificSettings();
    apLanPortSpecificSettings.setPoeMode(PoeModeEnum._802_3af);
    apLanPortSpecificSettings.setPoeOut(true);
    apLanPortSpecificSettings.setUseVenueSettings(false);
    sendUpdateApLanPortSpecificSettingsRequestParams(venueId, tenant, serialNumber, apLanPortSpecificSettings);

    validateUpdateApLanPortSpecificSettings(venueId, ap.getId(), PoeModeEnum._802_3af, true, false);

    ApLanPortSpecificSettings apLanPortSpecificSettings2 = new ApLanPortSpecificSettings();
    apLanPortSpecificSettings2.setPoeMode(PoeModeEnum._802_3bt_Class_5);
    apLanPortSpecificSettings2.setPoeOut(false);
    apLanPortSpecificSettings2.setUseVenueSettings(false);
    sendUpdateApLanPortSpecificSettingsRequestParams(venueId, tenant, serialNumber, apLanPortSpecificSettings2);

    validateUpdateApLanPortSpecificSettings(venue.getId(), ap.getId(), PoeModeEnum._802_3bt_Class_5, false, false);

    ApLanPortSpecificSettings apLanPortSpecificSettings3 = new ApLanPortSpecificSettings();
    apLanPortSpecificSettings3.setUseVenueSettings(true);
    sendUpdateApLanPortSpecificSettingsRequestParams(venueId, tenant, serialNumber, apLanPortSpecificSettings3);

    final Ap result = repositoryUtil.find(Ap.class, ap.getId());
    assertThat(result)
        .extracting(Ap::getModelSpecific)
        .extracting(ApModelSpecific::getLanPorts)
        // ExtendedApServiceCtrlImpl.java:getApLanPortSettings will return UseVenueSettings = true
        .matches(CollectionUtils::isEmpty);
  }

  @Test
  @FeatureFlag(enable = WIFI_AP_POE_OPERATING_MODE_SETTING_TOGGLE)
  void testUpdateApLanPortSpecificSettingsWithLanPorts(Tenant tenant, Venue venue, Ap ap) {

    String venueId = venue.getId();
    String serialNumber = ap.getId();

    ap.setModel("H550");
    repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());

    // save eth port profile
    EthernetPortProfile ethernetPortProfile = new EthernetPortProfile();
    ethernetPortProfile.setName("port1");
    ethernetPortProfile.setApLanPortId(1);
    ethernetPortProfile.setType(ApLanPortTypeEnum.TRUNK);
    ethernetPortProfile.setUntagId((short) 1);
    ethernetPortProfile.setVlanMembers("1-4094");
    ethernetPortProfile.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
    ethernetPortProfile.setTenant(tenant);
    ethernetPortProfile = repositoryUtil.createOrUpdate(ethernetPortProfile, tenant.getId(), randomTxId());
    String ethernetPortProfileId = ethernetPortProfile.getId();

    sendActivateEthernetPortProfileToApLanPortRequestParams(
        venueId, serialNumber, "1", ethernetPortProfileId, tenant);

    CfgAction action = CfgAction.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT;
    assertAll("assert activity status success",
        () -> validateApRepositoryData(action,
            serialNumber, "1", ethernetPortProfileId));

    ApLanPortSpecificSettings apLanPortSpecificSettings = new ApLanPortSpecificSettings();
    apLanPortSpecificSettings.setPoeMode(PoeModeEnum._802_3bt_Class_5);
    apLanPortSpecificSettings.setPoeOut(false);
    apLanPortSpecificSettings.setUseVenueSettings(false);
    sendUpdateApLanPortSpecificSettingsRequestParams(venueId, tenant, serialNumber, apLanPortSpecificSettings);

    final Ap result = repositoryUtil.find(Ap.class, ap.getId());
    assertThat(result)
        .extracting(Ap::getModelSpecific)
        .matches(attr -> Objects.equals(attr.getPoeMode(), PoeModeEnum._802_3bt_Class_5))
        .matches(attr -> attr.getLanPorts().stream()
            .map(ApLanPort::getApLanPortProfile)
            .map(EthernetPortProfile.class::cast)
            .map(EthernetPortProfile::getName)
            .filter(name -> name.equals("port1")).findAny().isPresent());
  }

  private void validateUpdateApLanPortSpecificSettings(
      String venueId, String serialNumber, PoeModeEnum poeMode, boolean poeOut, boolean useVenueSettings) {

    final Ap result = repositoryUtil.find(Ap.class, serialNumber);
    assertThat(result)
        .extracting(Ap::getModelSpecific)
        .matches(attr -> Objects.equals(attr.getPoeMode(), poeMode))
        .matches(attr -> Objects.equals(attr.getPoeOut(), poeOut))
        .matches(attr -> Objects.equals(attr.getUseVenueSettings(), useVenueSettings))
        .matches(attr -> !attr.getLanPorts().isEmpty());
  }

  private void sendUpdateApLanPortSpecificSettingsRequestParams(
      String venueId, Tenant tenant, String serialNumber, ApLanPortSpecificSettings payload) {

    final var requestId = randomTxId();
    RequestParams req = new RequestParams()
        .addPathVariable("venueId", venueId)
        .addPathVariable("serialNumber", serialNumber);

    messageUtil.sendWifiCfgRequest(tenant.getId(), requestId,
        CfgAction.UPDATE_AP_LAN_PORT_SPECIFIC_SETTINGS,
        tenant.getName(), req, payload);
  }

  private void validateEthernetPortProfileToApLanPortActivationResult(
      CfgAction apiAction,
      String venueId,
      String serialNumber,
      String portId,
      String ethernetPortProfileId,
      boolean migratedToLanPortAdoption,
      boolean portEnabled) {
    validateApRepositoryData(
        apiAction, serialNumber, portId, ethernetPortProfileId, migratedToLanPortAdoption, portEnabled);
    validateApDdccmCfgRequestMessages(
        apiAction, venueId, serialNumber, portId, ethernetPortProfileId, migratedToLanPortAdoption);
    validateActivityMessages(apiAction);
  }

  private void validateEthernetPortProfileToApLanPortActivationResult(
      CfgAction apiAction,
      String venueId,
      String serialNumber,
      String portId,
      String ethernetPortProfileId,
      boolean migratedToLanPortAdoption) {
    validateApRepositoryData(
        apiAction, serialNumber, portId, ethernetPortProfileId, migratedToLanPortAdoption, null);
    validateApDdccmCfgRequestMessages(
        apiAction, venueId, serialNumber, portId, ethernetPortProfileId, migratedToLanPortAdoption);
    validateActivityMessages(apiAction);
  }

  private void validateEthernetPortProfileToApLanPortActivationResult(
      CfgAction apiAction,
      String venueId,
      String serialNumber,
      String portId,
      String ethernetPortProfileId) {
    validateEthernetPortProfileToApLanPortActivationResult(
        apiAction, venueId, serialNumber, portId, ethernetPortProfileId, false);
  }

  private void validateEthernetPortProfileToVenueApModelLanPortActivationResult(
      CfgAction apiAction,
      String venueId,
      String apModel,
      String portId,
      String ethernetPortProfileId,
      boolean migratedToLanPortAdoption) {
    validateVenueRepositoryData(
        apiAction, venueId, apModel, portId, ethernetPortProfileId, migratedToLanPortAdoption);
    validateVenueDdccmCfgRequestMessages(
        apiAction, venueId, apModel, portId, ethernetPortProfileId, migratedToLanPortAdoption);
    validateActivityMessages(apiAction);
  }

  private void validateEthernetPortProfileToVenueApModelLanPortActivationResult(
      CfgAction apiAction,
      String venueId, String apModel, String portId, String ethernetPortProfileId) {
    validateEthernetPortProfileToVenueApModelLanPortActivationResult(
        apiAction, venueId, apModel, portId, ethernetPortProfileId, false
    );
  }

  private void validateResult(CfgAction apiAction,
      com.ruckus.cloud.wifi.eda.viewmodel.EthernetPortProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges = revisionService.changes(requestId, tenantId,
        ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    final String profileId = txChanges.getNewEntities().stream()
        .filter(txEntity -> txEntity.getEntity() instanceof EthernetPortProfile)
        .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
    validateResult(apiAction, profileId, payload);
  }

  private void validateResult(
      CfgAction apiAction,
      String profileId,
      com.ruckus.cloud.wifi.eda.viewmodel.EthernetPortProfile payload,
      String adoptionId) {
    validateRepositoryData(profileId, payload, apiAction);
    validateCmnCfgCollectorMessages(apiAction, profileId, null);
    validateDdccmCfgRequestMessages(
        apiAction, adoptionId != null ? List.of(adoptionId) : List.of(profileId), payload, null);
    validateActivityMessages(apiAction);
  }

  private void validateResult(
      CfgAction apiAction,
      String profileId,
      com.ruckus.cloud.wifi.eda.viewmodel.EthernetPortProfile payload) {
    validateResult(apiAction, profileId, payload, null);
  }

  private void validateRadiusActivationResult(CfgAction apiAction, String ethernetPortProfileId,
      String radiusServerProfileId) {
    validateRepositoryData(apiAction, ethernetPortProfileId, radiusServerProfileId);
    validateCmnCfgCollectorMessages(apiAction, ethernetPortProfileId, radiusServerProfileId);
    validateDdccmCfgRequestMessages(apiAction, List.of(ethernetPortProfileId), null, radiusServerProfileId);
    validateActivityMessages(apiAction);
  }

  private void validateRepositoryData(String profileId,
      com.ruckus.cloud.wifi.eda.viewmodel.EthernetPortProfile payload, CfgAction apiAction) {
    if (profileId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final EthernetPortProfile ethernetPortProfile = repositoryUtil.find(EthernetPortProfile.class,
        profileId);

    if (payload == null) {
      assertThat(ethernetPortProfile).isNull();
      return;
    }

    assertThat(ethernetPortProfile)
        .isNotNull()
        .matches(r -> Objects.equals(r.getId(), profileId))
        .matches(r -> Objects.equals(r.getTenant().getId(), txCtxExtension.getTenantId()))
        .matches(r -> Objects.equals(r.getName(), payload.getName()))
        .matches(r -> Objects.equals(r.getUntagId(), payload.getUntagId()))
        .matches(r -> Objects.equals(r.getEnableAuthProxy(), payload.getEnableAuthProxy()))
        .matches(r -> Objects.equals(r.getVlanMembers(), payload.getVlanMembers()));
  }

  private void validateRepositoryData(CfgAction apiAction, String entityId,
      String radiusProfileId) {
    assertThat(entityId).isNotNull();
    switch (apiAction) {
      case ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> {
        final EthernetPortProfile ethernetPortProfile = repositoryUtil.find(EthernetPortProfile.class,
            entityId);
        assertThat(ethernetPortProfile)
            .isNotNull()
            .matches(
                r -> Objects.equals(r.getAuthRadius().getId(),
                    radiusProfileId));
        final Radius radius = repositoryUtil.find(Radius.class,
            radiusProfileId);
        assertThat(radius)
            .isNotNull()
            .matches(
                r -> Objects.equals(r.getAuthEthernetPortProfiles().get(0).getId(),
                    entityId));
      }
      case DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> {
        final EthernetPortProfile ethernetPortProfile = repositoryUtil.find(EthernetPortProfile.class,
            entityId);
        assertThat(ethernetPortProfile)
            .isNotNull()
            .matches(
                r -> Objects.isNull(r.getAuthRadius()));
        final Radius radius = repositoryUtil.find(Radius.class,
            radiusProfileId);
        assertThat(radius)
            .isNotNull()
            .matches(
                r -> ObjectUtils.isEmpty(r.getAuthEthernetPortProfiles()));
      }
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    }
  }

  private void validateVenueRepositoryData(CfgAction apiAction,
      String venueId, String apModel, String portId, String ethernetPortProfileId) {
    validateVenueRepositoryData(apiAction, venueId, apModel, portId, ethernetPortProfileId, false);
  }

  private void validateVenueRepositoryData(
      CfgAction apiAction,
      String venueId,
      String apModel,
      String portId,
      String ethernetPortProfileId,
      boolean migratedToLanPortAdoption) {
    assertThat(venueId).isNotNull();

    switch (apiAction) {
      case ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT -> {
        final Venue venue = repositoryUtil.find(Venue.class, venueId);
        assertThat(venue)
            .extracting(Venue::getModelSpecificAttributes)
            .extracting(attrs -> attrs.stream()
                .filter(attr -> attr.getModel().equals(apModel)).findAny().orElse(null))
            .extracting(attr -> attr.getLanPorts().stream()
                .filter(lanPort -> lanPort.getPortId().equals(portId)).findAny().orElse(null))
            .isNotNull()
            .matches(lanPort -> Objects.equals(lanPort.getApLanPortProfile().getId(), ethernetPortProfileId))
            .matches(port ->
                    !migratedToLanPortAdoption
                        || Objects.equals(port.getLanPortAdoption().getApLanPortProfile().getId(), ethernetPortProfileId));
        final EthernetPortProfile ethernetPortProfile = repositoryUtil.find(EthernetPortProfile.class,
            ethernetPortProfileId);
        assertThat(ethernetPortProfile)
            .extracting(EthernetPortProfile::getVenueLanPorts)
            .extracting(lanPorts -> lanPorts.stream()
                .filter(lanPort -> lanPort.getVenueApModelSpecificAttributes().getModel().equals(apModel))
                .filter(lanPort -> lanPort.getPortId().equals(portId))
                .findAny()
                .orElse(null))
            .isNotNull()
            .matches(lanPort -> Objects.equals(lanPort.getApLanPortProfile().getId(), ethernetPortProfileId))
            .matches(port ->
                      !migratedToLanPortAdoption
                          || Objects.equals(port.getLanPortAdoption().getApLanPortProfile().getId(), ethernetPortProfileId));
      }
      case DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT -> {
        final Venue venue = repositoryUtil.find(Venue.class, venueId);
        assertThat(venue)
            .extracting(Venue::getModelSpecificAttributes)
            .extracting(attrs -> attrs.stream()
                .filter(attr -> attr.getModel().equals(apModel)).findAny().orElse(null))
            .extracting(attr -> attr.getLanPorts().stream()
                .filter(lanPort -> lanPort.getPortId().equals(portId)).findAny().orElse(null))
            .isNotNull()
            // reset to default profile
            .matches(lanPort -> Objects.equals(lanPort.getApLanPortProfile().getIsDefault(), true))
            .matches(lanPort -> !Objects.equals(lanPort.getApLanPortProfile().getId(), ethernetPortProfileId))
            .matches(port ->
                !migratedToLanPortAdoption
                    || !Objects.equals(port.getLanPortAdoption().getApLanPortProfile().getId(), ethernetPortProfileId));
      }
      case UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS -> {
        final Venue venue = repositoryUtil.find(Venue.class, venueId);
        assertThat(venue)
            .extracting(Venue::getModelSpecificAttributes)
            .extracting(attrs -> attrs.stream()
                .filter(attr -> attr.getModel().equals(apModel)).findAny().orElse(null))
            .extracting(attr -> attr.getLanPorts().stream()
                .filter(lanPort -> lanPort.getPortId().equals(portId)).findAny().orElse(null))
            .isNotNull()
            .matches(lanPort -> Objects.equals(lanPort.getEnabled(), false));
      }
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    }
  }

  private void validateApRepositoryData(CfgAction apiAction, String serialNumber,
      String portId, String ethernetPortProfileId) {
    validateApRepositoryData(apiAction, serialNumber, portId, ethernetPortProfileId, false, null);
  }

  private void validateApRepositoryData(
      CfgAction apiAction,
      String serialNumber,
      String portId,
      String ethernetPortProfileId,
      boolean migratedToLanPortAdoption,
      Boolean portEnabled) {
    assertThat(serialNumber).isNotNull();
    switch (apiAction) {
      case ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT -> {
        final Ap ap = repositoryUtil.find(Ap.class, serialNumber);
        assertThat(ap)
            .extracting(Ap::getModelSpecific)
            .extracting(attr -> attr.getLanPorts().stream()
                .filter(lanPort -> lanPort.getPortId().equals(portId)).findAny().orElse(null))
            .isNotNull()
            .matches(lanPort -> Objects.equals(lanPort.getApLanPortProfile().getId(), ethernetPortProfileId))
            .matches(port ->
                !migratedToLanPortAdoption
                    || Objects.equals(port.getLanPortAdoption().getApLanPortProfile().getId(), ethernetPortProfileId));
      }
      case DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT -> {
        final Ap ap = repositoryUtil.find(Ap.class, serialNumber);
        assertThat(ap)
            .extracting(Ap::getModelSpecific)
            .extracting(attr -> attr.getLanPorts().stream()
                .filter(lanPort -> lanPort.getPortId().equals(portId)).findAny().orElse(null))
            .isNotNull()
            // reset to default profile
            .matches(lanPort -> Objects.equals(((EthernetPortProfile) lanPort.getApLanPortProfile()).getIsDefault(), true))
            .matches(lanPort -> !Objects.equals(lanPort.getApLanPortProfile().getId(), ethernetPortProfileId))
            .matches(port ->
                !migratedToLanPortAdoption
                    || !Objects.equals(port.getLanPortAdoption().getApLanPortProfile().getId(), ethernetPortProfileId));
      }
      case UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS -> {
        final Ap ap = repositoryUtil.find(Ap.class, serialNumber);
        assertThat(ap)
            .extracting(Ap::getModelSpecific)
            .extracting(attr -> attr.getLanPorts().stream()
                .filter(lanPort -> lanPort.getPortId().equals(portId)).findAny().orElse(null))
            .isNotNull()
            .matches(lanPort -> Objects.equals(lanPort.getApLanPortOverwriteSettings().getUntagId(), (short)3))
            .matches(lanPort -> Objects.equals(lanPort.getApLanPortOverwriteSettings().getVlanMembers(), "1-4094"))
            .matches(lanPort -> Objects.equals(lanPort.getEnabled(), portEnabled));
      }
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    }
  }

  private void validateDdccmCfgRequestMessages(CfgAction apiAction, List<String> profileIdList,
      com.ruckus.cloud.wifi.eda.viewmodel.EthernetPortProfile payload, String radiusProfileId) {
    if (apiAction == null || profileIdList == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasApLanPortProfile)
            .hasSize(profileIdList.size())
            .allMatch(op -> profileIdList.contains(op.getId()))
            .allMatch(op -> op.getAction() == action(apiAction))
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
                    .matches(r -> profileIdList.contains(r.getName())); // special case for ap
              } else if (op.getAction() == Action.ADD) {
                assertThat(payload).isNotNull();
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
                    .matches(r -> profileIdList.contains(r.getName())) // special case for ap
                    .matches(r -> Objects.nonNull(r.getId()))
                    // ACX-57924: Don't apply the change of untag VLAN to the APs with old firmwares to avoid ACX-44877
                    .matches(r -> r.getUntagId() == 1)
                    .matches(r -> payload.getEnableAuthProxy().equals(r.getRoleAuthenticator().getAuthenticationServerProxyEnabled()))
                    .matches(r -> payload.getEnableAccountingProxy().equals(r.getRoleAuthenticator().getAccountingServerProxyEnabled()))
                    .matches(r -> payload.getVlanMembers().equals(r.getVlanMembers()));
              } else {
                switch (apiAction) {
                  case UPDATE_ETHERNET_PORT_PROFILE -> {
                    assertThat(payload).isNotNull();
                    assertThat(op)
                        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
                        .matches(r -> profileIdList.contains(r.getName())) // special case for ap
                        // ACX-57924: Don't apply the change of untag VLAN to the APs with old firmwares to avoid ACX-44877
                        .matches(r -> r.getUntagId() == 1)
                        .matches(r -> payload.getEnableAuthProxy().equals(r.getRoleAuthenticator().getAuthenticationServerProxyEnabled()))
                        .matches(r -> payload.getEnableAccountingProxy().equals(r.getRoleAuthenticator().getAccountingServerProxyEnabled()))
                        .matches(r -> payload.getVlanMembers().equals(r.getVlanMembers()));
                  }
                  case ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> {
                    assertThat(op)
                        .extracting(
                            com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
                        .matches(
                            r -> profileIdList.contains(r.getName()));
                    // TODO: verify radius fields after https://jira.ruckuswireless.com/browse/ACX-59337 was done
                  }
                  case DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> {
                    assertThat(op)
                        .extracting(
                            com.ruckus.acx.ddccm.protobuf.wifi.Operation::getApLanPortProfile)
                        .matches(
                            r -> r.hasRoleAuthenticator() == false);
                  }
                  default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
                }
              }
            }));
  }

  private void validateVenueDdccmCfgRequestMessages(
      CfgAction apiAction,
      String venueId,
      String apModel,
      String portId,
      String ethernetPortProfileId,
      boolean migratedToLanPortAdoption) {
    if (apiAction == null || venueId == null || ethernetPortProfileId == null) {
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }
    final var requestId = txCtxExtension.getRequestId();
    final var tenantId = txCtxExtension.getTenantId();
    final var ddccmCfgRequestMessage =
        messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    var profileMap = new HashMap<Integer, ApLanPortProfile>();
    if (migratedToLanPortAdoption) {
      var profiles =
          ddccmCfgRequestMessage.getPayload().getOperationsList().stream()
              .filter(Operation::hasApLanPortProfile)
              .filter(op -> op.getAction() == Action.ADD)
              .map(Operation::getApLanPortProfile)
              .toList();
      assertFalse(profiles.isEmpty());
      profiles.forEach(profile -> profileMap.put(profile.getId(), profile));
      var venues =
          ddccmCfgRequestMessage.getPayload().getOperationsList().stream()
              .filter(Operation::hasVenue)
              .map(Operation::getVenue)
              .toList();
      assertFalse(venues.isEmpty());
      venues.stream()
          .flatMap(v -> v.getVenueApModelsList().stream())
          .flatMap(model -> model.getLanPortList().stream())
          .forEach(
              lanPort -> {
                assertTrue(profileMap.containsKey(lanPort.getApLanPortProfileId()));
                assertEquals(
                    profileMap.get(lanPort.getApLanPortProfileId()).getName(),
                    lanPort.getEthProfileName());
              });
    }

    assertThat(ddccmCfgRequestMessage.getPayload())
        .extracting(WifiConfigRequest::getOperationsList)
        .asList()
        .isNotEmpty()
        .extracting(Operation.class::cast)
        .filteredOn(Operation::hasVenue)
        .allMatch(op -> venueId.equals(op.getId()))
        .allMatch(op -> op.getAction() == action(apiAction))
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .allSatisfy(
            op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              switch (apiAction) {
                case ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT -> {
                  assertThat(op)
                      .extracting(Operation::getVenue)
                      .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getVenueApModelsList)
                      .extracting(
                          apModels ->
                              apModels.stream()
                                  .filter(model -> model.getModelName().equals(apModel))
                                  .findAny()
                                  .orElse(null))
                      .extracting(
                          model ->
                              model.getLanPortList().stream()
                                  .filter(lanPort -> lanPort.getPortName().equals(portName(portId)))
                                  .findAny()
                                  .orElse(null))
                      .isNotNull()
                      .matches(
                          r ->
                              r.getEthProfileName()
                                  .equals(
                                      migratedToLanPortAdoption
                                          ? profileMap.get(r.getApLanPortProfileId()).getName()
                                          : ethernetPortProfileId));
                }
                case UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS -> {
                  assertThat(op)
                      .extracting(Operation::getVenue)
                      .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getVenueApModelsList)
                      .extracting(
                          apModels ->
                              apModels.stream()
                                  .filter(model -> model.getModelName().equals(apModel))
                                  .findAny()
                                  .orElse(null))
                      .extracting(
                          model ->
                              model.getLanPortList().stream()
                                  .filter(lanPort -> lanPort.getPortName().equals(portName(portId)))
                                  .findAny()
                                  .orElse(null))
                      .isNotNull()
                      .matches(r -> r.getEnabled().equals(BoolValue.of(false)));
                }
                default ->
                    throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
              }
            });
  }

  private void validateApDdccmCfgRequestMessages(CfgAction apiAction, String venueId,
      String serialNumber, String portId, String ethernetPortProfileId, boolean migratedToLanPortAdoption) {

    if (apiAction == null || serialNumber == null || ethernetPortProfileId == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    var profileMap = new HashMap<Integer, ApLanPortProfile>();
    if (migratedToLanPortAdoption) {
      var profiles =
          ddccmCfgRequestMessage.getPayload().getOperationsList().stream()
              .filter(Operation::hasApLanPortProfile)
              .filter(op -> op.getAction() == Action.ADD)
              .map(Operation::getApLanPortProfile)
              .toList();
      assertFalse(profiles.isEmpty());
      profiles.forEach(profile -> profileMap.put(profile.getId(), profile));
    }
    var operations =
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList)
            .asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast);

    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasVenue)
        .allMatch(op -> venueId.equals(op.getId()))
        .allMatch(op -> op.getAction() == action(apiAction));
    operations
        .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasAp)
        .isNotEmpty()
        .allSatisfy(op -> {
              assertThat(op.getAction()).isEqualTo(Action.MODIFY);
              switch (apiAction) {
                case ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT -> {
                  assertThat(op)
                      .extracting(
                          com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
                      .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
                      .extracting(model -> model.getLanPortList().stream()
                          .filter(lanPort -> lanPort.getPortName().equals(portName(portId))).findAny().orElse(null))
                      .isNotNull()
                      .matches(
                          r -> r.getEthProfileName().equals(ethernetPortProfileId));
                }
                case UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS -> {
                  assertThat(op)
                      .extracting(
                          com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
                      .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
                      .extracting(model -> model.getLanPortList().stream()
                          .filter(lanPort -> lanPort.getPortName().equals(portName(portId))).findAny().orElse(null))
                      .isNotNull()
                      .matches(
                          r -> r.getEnabled().equals(BoolValue.of(false)));
                }
                case ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT->{
                  if(migratedToLanPortAdoption){
                    assertThat(op)
                        .extracting(
                            com.ruckus.acx.ddccm.protobuf.wifi.Operation::getAp)
                        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
                        .isNotNull();
                  }

                }
                default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
              }
        });
  }

  private void validateApDdccmCfgRequestMessages(CfgAction apiAction, String venueId,
                                                 String serialNumber, String portId, String ethernetPortProfileId) {
    validateApDdccmCfgRequestMessages(apiAction, venueId, serialNumber, portId, ethernetPortProfileId, false);
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, String ethProfileId,
      String radiusProfileId) {
    if (apiAction == null || ethProfileId == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast).satisfies(ops -> {
              assertThat(ops).filteredOn(
                      op -> Index.ETHERNET_PORT_PROFILE_INDEX_NAME.equals(op.getIndex()))
                  .hasSize(1)
                  .allMatch(op -> ethProfileId.equals(op.getId()))
                  .allMatch(op -> op.getOpType() == opType(apiAction))
                  .allSatisfy(op -> {
                    if (op.getOpType() != OpType.DEL) {
                      assertThat(op.getDocMap())
                          .containsEntry(Key.ID, ValueUtils.stringValue(ethProfileId))
                          .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenantId));
                      switch (apiAction) {
                        case ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> {
                          assertThat(op)
                              .extracting(Operations::getDocMap)
                              .matches(
                                  doc -> !doc.get(Key.AUTH_RADIUS_ID).getStringValue()
                                      .isEmpty());
                        }
                        case DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> {
                          assertThat(op)
                              .extracting(Operations::getDocMap)
                              .matches(doc -> doc.get(Key.AUTH_RADIUS_ID).getStringValue()
                                  .isEmpty());
                        }
                      }
                    }
                  });
              if (radiusProfileId != null) {
                switch (apiAction) {
                  case ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> {
                    assertThat(ops).filteredOn(
                            op -> Index.RADIUS_SERVER_PROFILE_INDEX_NAME.equals(op.getIndex()))
                        .hasSize(1)
                        .allMatch(op -> radiusProfileId.equals(op.getId()))
                        .allMatch(op -> op.getOpType() == opType(apiAction))
                        .allSatisfy(op -> {
                          if (op.getOpType() != OpType.DEL) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(doc -> doc.get(Key.ETHERNET_PORT_PROFILE_IDS).hasListValue())
                                .matches(doc -> tenantId.equals(
                                    doc.get(EsConstants.Key.TENANT_ID).getStringValue()));
                          }
                        });
                  }
                  case DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> {
                    assertThat(ops).filteredOn(
                            op -> Index.RADIUS_SERVER_PROFILE_INDEX_NAME.equals(op.getIndex()))
                        .hasSize(1)
                        .allMatch(op -> radiusProfileId.equals(op.getId()))
                        .allMatch(op -> op.getOpType() == opType(apiAction))
                        .allSatisfy(op -> {
                          if (op.getOpType() != OpType.DEL) {
                            assertThat(op)
                                .extracting(Operations::getDocMap)
                                .matches(
                                    doc -> doc.get(Key.ETHERNET_PORT_PROFILE_IDS).getNullValue().equals(
                                        NullValue.NULL_VALUE))
                                .matches(doc -> tenantId.equals(
                                    doc.get(EsConstants.Key.TENANT_ID).getStringValue()));
                          }
                        });
                  }
                  default ->
                      throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
                }
              }
            }));
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ETHERNET_PORT_PROFILE -> OpType.ADD;
      case UPDATE_ETHERNET_PORT_PROFILE,
           ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE,
           DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE -> OpType.MOD;
      case DELETE_ETHERNET_PORT_PROFILE -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private void validateActivityMessages(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors.assertThat(
          kafkaTopicProvider.getActivityCfgChangeResp(),
          kafkaTopicProvider.getActivityImpacted()
      ).doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(activityCfgChangeRespMessage.getPayload())
            .matches(msg -> msg.getStatus().equals(Status.OK))
            .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
            .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(tenantId);
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ETHERNET_PORT_PROFILE -> Action.ADD;
      case UPDATE_ETHERNET_PORT_PROFILE,
           ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE,
           DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE,
           ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT,
           DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT,
           ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT,
           DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT,
           UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS,
           UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS -> Action.MODIFY;
      case DELETE_ETHERNET_PORT_PROFILE -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ETHERNET_PORT_PROFILE -> ApiFlowNames.ADD_ETHERNET_PORT_PROFILE;
      case UPDATE_ETHERNET_PORT_PROFILE -> ApiFlowNames.UPDATE_ETHERNET_PORT_PROFILE;
      case DELETE_ETHERNET_PORT_PROFILE -> ApiFlowNames.DELETE_ETHERNET_PORT_PROFILE;
      case ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE ->
          ApiFlowNames.ACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE;
      case DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE ->
          ApiFlowNames.DEACTIVATE_RADIUS_TO_ETHERNET_PORT_PROFILE;
      case ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT ->
          ApiFlowNames.ACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT;
      case DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT ->
          ApiFlowNames.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_VENUE_AP_MODEL_LAN_PORT;
      case ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT ->
          ApiFlowNames.ACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT;
      case DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT ->
          ApiFlowNames.DEACTIVATE_ETHERNET_PORT_PROFILE_TO_AP_LAN_PORT;
      case UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS ->
          ApiFlowNames.UPDATE_VENUE_AP_MODEL_LAN_PORT_OVERWRITE_SETTINGS;
      case UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS ->
          ApiFlowNames.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS;

      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private PortNameEnum portName(String portId) {
    return PortNameEnum.forNumber(Integer.valueOf(portId));
  }

  private EthernetPortProfile createEthernetPortProfile(Venue venue, int ethernetPortProfileId) {
    return repositoryUtil.createOrUpdate(
        EthernetPortProfileTestFixture.randomEthernetPortProfile(
            venue,
            e -> {
              e.setApLanPortId(ethernetPortProfileId);
              e.setName(randomName());
              e.setType(ApLanPortTypeEnum.TRUNK);
              e.setUntagId((short) 1);
              e.setVlanMembers("1-4094");
              e.setAuthType(ApLanPortAuthTypeEnum.DISABLED);
            }),
        txCtxExtension.getTenantId(),
        randomTxId());
  }

  private LanPortAdoption createLanPortAdoption(
      EthernetPortProfile profile, int ethernetPortProfileId) {
    return repositoryUtil.createOrUpdate(
        LanPortAdoptionTestFixture.randomLanPortAdoption(
            profile,
            adoption -> {
              adoption.setEthernetPortProfileId(ethernetPortProfileId);
              adoption.setChecksum(lanPortAdoptionService.calculateChecksum(adoption));
            }),
        txCtxExtension.getTenantId(),
        randomTxId());
  }

  private VenueApModelSpecificAttributes createModelSpecific(Venue venue, String apModel) {
    return repositoryUtil.createOrUpdate(
        VenueApModelSpecificAttributesTestFixture.randomVenueApModelSpecificAttributes(
            venue,
            v -> {
              v.setModel(apModel);
              v.setLanPorts(new ArrayList<>());
            }),
        txCtxExtension.getTenantId(),
        randomTxId());
  }

  private VenueLanPort createPort(
      Venue venue,
      VenueApModelSpecificAttributes modelSpecific,
      EthernetPortProfile profile,
      LanPortAdoption adoption,
      String portId) {
    return repositoryUtil.createOrUpdate(
        VenueLanPortTestFixture.randomVenueLanPort(
            venue,
            modelSpecific,
            port -> {
              port.setEnabled(true);
              port.setApLanPortProfile(profile);
              port.setLanPortAdoption(adoption);
              port.setPortId(portId);
              port.setVenueApModelSpecificAttributes(modelSpecific);
            }),
        txCtxExtension.getTenantId(),
        randomTxId());
  }

  private ApLanPort createPort(
      Venue venue,
      ApModelSpecific apSpecific,
      EthernetPortProfile profile,
      LanPortAdoption adoption,
      String portId) {
    return repositoryUtil.createOrUpdate(
        ApLanPortTestFixture.randomApLanPort(
            venue,
            apSpecific,
            port -> {
              port.setEnabled(true);
              port.setApLanPortProfile(profile);
              port.setLanPortAdoption(adoption);
              port.setPortId(portId);
              port.setModelSpecific(apSpecific);
            }),
        txCtxExtension.getTenantId(),
        randomTxId());
  }

  private void initAp(Ap ap, ApGroup apGroup) {
    var apSpecific = new ApModelSpecific();
    apSpecific.setLanPorts(new ArrayList<>());
    apSpecific =
        repositoryUtil.createOrUpdate(apSpecific, txCtxExtension.getTenantId(), randomTxId());

    ap.setModelSpecific(apSpecific);
    ap.setApGroup(apGroup);
    repositoryUtil.createOrUpdate(ap, txCtxExtension.getTenantId(), randomTxId());
  }
}
