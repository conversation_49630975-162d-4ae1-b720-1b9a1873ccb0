package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatNoException;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.notification.dnb.gpb.Dnb;
import com.ruckus.cloud.notification.dnb.gpb.Dnb.DNB;
import com.ruckus.cloud.notification.dnb.gpb.Dnb.TaskFactoryReset;
import com.ruckus.cloud.notification.service.gpb.DeviceNotification;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.client.viewmodel.ViewmodelClientGrpc;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApSubStateEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceRule;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.ApRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ScheduleTimeSlotTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleFirmwareVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.UpgradeScheduleTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
public class ConsumeDeleteApRequestTest extends AbstractRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private ViewmodelClientGrpc viewmodelClientGrpc;

  @Test
  void deleteApWithFactoryReset(Ap ap) throws Exception {
    final var tenantId = ap.getTenant().getId();
    final var userName = randomName();

    // Set the AP is operational
    mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.Operational);

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams().addPathVariable("serialNumber", ap.getId());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_AP, userName, requestParams, "");

    final var factoryResetMessage = messageCaptors.getDeviceNotificationMessageCaptor()
            .getValue(tenantId, delRequestId);
    assertThat(factoryResetMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(factoryResetMessage.getPayload())
            .extracting(DeviceNotification::getContent)
            .satisfies(assertDnb(ap.getId())));
  }

  @Test
  void deleteApWithoutFactoryReset(Ap ap) throws Exception {
    final var tenantId = ap.getTenant().getId();
    final var userName = randomName();

    // Set the AP is operational
    mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.NeverContactedCloud);

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams().addPathVariable("serialNumber", ap.getId());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_AP, userName, requestParams, "");

    messageCaptors.getDeviceNotificationMessageCaptor().assertNotSentByTenant(tenantId);
  }

  @Test
  void deleteApWithNetworkApGroup(Ap ap, ApGroup apGroup, NetworkVenue networkVenue)
      throws Exception {
    final var tenantId = ap.getTenant().getId();
    final var userName = randomName();

    NetworkApGroup networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
        apGroup);
    NetworkApGroupRadio networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
        networkApGroup);
    ap.setApGroup(apGroup);
    repositoryUtil.createOrUpdate(apGroup, apGroup.getTenant().getId(), randomTxId());
    repositoryUtil.createOrUpdate(networkApGroup, apGroup.getTenant().getId(), randomTxId());
    repositoryUtil.createOrUpdate(networkApGroupRadio, apGroup.getTenant().getId(), randomTxId());
    repositoryUtil.createOrUpdate(ap, apGroup.getTenant().getId(), randomTxId());

    // Set the AP is operational
    mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.NeverContactedCloud);

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams().addPathVariable("serialNumber", ap.getId());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_AP, userName,
        requestParams, "");

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, delRequestId);
    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    // validate AP Group cmnCfgCollectorMessage
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(delRequestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
        .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
        .isEmpty();

    // validate AP cmnCfgCollectorMessage, should send empty deviceGroupName when we delete AP Group in EDA
    assertThat(cmnCfgCollectorMessage.getPayload())
        .satisfies(msg -> {
          assertThat(msg.getTenantId()).isEqualTo(tenantId);
          assertThat(msg.getRequestId()).isEqualTo(delRequestId);
        })
        .extracting(ViewmodelCollector::getOperationsList,
            InstanceOfAssertFactories.list(Operations.class))
        .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
        .isNotEmpty().singleElement()
        .extracting(Operations::getOpType).isEqualTo(OpType.DEL);
  }

  @Test
  void deleteApWithMulticastDnsProxyServiceProfile(@DefaultApGroup ApGroup apGroup) throws Exception {
    // Add AP
    final var tenantId = apGroup.getTenant().getId();
    final var requestId = randomTxId();
    final var userName = randomName();
    final var serialNumber = randomSerialNumber();
    final var apRequest = new ApRequest();
    apRequest.setSerialNumber(serialNumber);
    apRequest.setVenueId(apGroup.getVenue().getId());
    apRequest.setName(randomName());

    messageUtil.sendWifiCfgRequest(tenantId, requestId, CfgExtendedAction.ADD_AP, userName, apRequest);
    final var ap = repositoryUtil.find(Ap.class, apRequest.getSerialNumber());

    // Add mDns Proxy service profile
    final MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = Generators.multicastDnsProxyServiceProfile().generate();
    multicastDnsProxyServiceProfile.getRules()
            .forEach(rule -> rule.setMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile));

    // Negative case: incorrect rule with mDNS name but without mDNS protocol
    MulticastDnsProxyServiceRule multicastDnsProxyServiceRule = new MulticastDnsProxyServiceRule();
    multicastDnsProxyServiceRule.setService(BridgeServiceEnum.AIRPLAY);
    multicastDnsProxyServiceRule.setMdnsName("rule33");
    multicastDnsProxyServiceRule.setEnabled(true);
    multicastDnsProxyServiceRule.setFromVlan(308);
    multicastDnsProxyServiceRule.setToVlan(309);
    multicastDnsProxyServiceRule.setMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile);
    multicastDnsProxyServiceProfile.getRules().add(multicastDnsProxyServiceRule);

    repositoryUtil.createOrUpdate(multicastDnsProxyServiceProfile, tenantId, randomTxId());
    var mDnsProxyServiceProfile = repositoryUtil.find(MulticastDnsProxyServiceProfile.class,
        multicastDnsProxyServiceProfile.getId());
    assertThat(mDnsProxyServiceProfile).isNotNull();

    // Add activation
    MulticastDnsProxyServiceProfileAp multicastDnsProxyServiceProfileAp = new MulticastDnsProxyServiceProfileAp();
    multicastDnsProxyServiceProfileAp.setId(randomId());
    multicastDnsProxyServiceProfileAp.setAp(ap);
    multicastDnsProxyServiceProfileAp.setMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile);
    repositoryUtil.createOrUpdate(multicastDnsProxyServiceProfileAp, tenantId, randomTxId());

    final var mDnsProxyServiceProfileAp =
        repositoryUtil.find(MulticastDnsProxyServiceProfileAp.class, multicastDnsProxyServiceProfileAp.getId());
    assertThat(mDnsProxyServiceProfileAp).isNotNull()
        .extracting(MulticastDnsProxyServiceProfileAp::getAp)
        .matches( mAp -> mAp.getId().equals(apRequest.getSerialNumber()));

    messageUtil.clearMessage();

    // Delete AP
    final var delRequestId = randomTxId();
    RequestParams requestParams = new RequestParams().addPathVariable("serialNumber", apRequest.getSerialNumber());
    messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_AP, userName, requestParams, apRequest);

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, delRequestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .as("The total operation count should be 2").hasSize(2)
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .satisfies(ops -> {
              assertThat(ops)
                  // Produced by MulticastDnsProxyServiceProfileCmnCfgCollectorOperationBuilder
                  .filteredOn(op -> EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
                  .filteredOn(op -> op.getOpType() == OpType.MOD)
                  .as("The MOD %s operation count should be 1",
                      EsConstants.Index.MULTICAST_DNS_PROXY_SERVICE_PROFILE_INDEX_NAME).hasSize(1)
                  .singleElement()
                  .extracting(Operations::getId)
                  .as("The operation id should be %s", multicastDnsProxyServiceProfile.getId())
                  .isEqualTo(multicastDnsProxyServiceProfile.getId());
            }));
  }

  @Nested
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  class whenApFwMgmtUpgradeByModelFfTurnOn {

    @Test
    void testDeleteAp_deleteApModelFirmwareMapping(Ap ap,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.511") ApVersion apVersion)
        throws Exception {
      final var tenantId = ap.getTenant().getId();
      final var userName = randomName();
      ap.setModel("R550");
      repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
      VenueCurrentFirmware vcf =
          VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(ap.getApGroup().getVenue(), apVersion,
              ap.getModel());
      repositoryUtil.createOrUpdate(vcf, tenantId, randomTxId());
      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId)).isNotEmpty();

      // Set the AP is operational
      mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.Operational);

      // Delete AP
      final var delRequestId = randomTxId();
      RequestParams requestParams = new RequestParams().addPathVariable("serialNumber", ap.getId());
      messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_AP, userName, requestParams, "");

      final var factoryResetMessage = messageCaptors.getDeviceNotificationMessageCaptor()
          .getValue(tenantId, delRequestId);
      assertThat(factoryResetMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(factoryResetMessage.getPayload())
              .extracting(DeviceNotification::getContent)
              .satisfies(assertDnb(ap.getId())));
      final var cmnCfgCollectorMsg = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, delRequestId);
      validateCmnCfgCollectorMsg(cmnCfgCollectorMsg, ap.getApGroup().getVenue().getId(), List.of(), null);
      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId)).isEmpty();
    }

    @Test
    void testDeleteAp_whenExistsSameModelApInVenue_skipDeleteApModelFirmwareMapping(Ap ap,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.511") ApVersion apVersion)
        throws Exception {
      final var tenantId = ap.getTenant().getId();
      final var venueId = ap.getApGroup().getVenue().getId();
      final var userName = randomName();
      final var apModel = "R550";
      Ap sameModelAp = createAp(createApGroup(ap.getApGroup().getVenue(), "group-1"), randomSerialNumber());
      ap.setModel(apModel);
      repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
      sameModelAp.setModel(apModel);
      repositoryUtil.createOrUpdate(sameModelAp, tenantId, randomTxId());

      VenueCurrentFirmware vcf =
          VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(ap.getApGroup().getVenue(), apVersion,
              ap.getModel());
      repositoryUtil.createOrUpdate(vcf, tenantId, randomTxId());
      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId)).isNotEmpty();

      // Set the AP is operational
      mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.Operational);

      // Delete AP
      final var delRequestId = randomTxId();
      RequestParams requestParams = new RequestParams().addPathVariable("serialNumber", ap.getId());
      messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_AP, userName, requestParams, "");

      final var factoryResetMessage = messageCaptors.getDeviceNotificationMessageCaptor()
          .getValue(tenantId, delRequestId);
      assertThat(factoryResetMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() -> assertThat(factoryResetMessage.getPayload())
          .extracting(DeviceNotification::getContent)
          .satisfies(assertDnb(ap.getId())));

      final var cmnCfgCollectorMsg = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, delRequestId);
      validateCmnCfgCollectorMsg(cmnCfgCollectorMsg, ap.getApGroup().getVenue().getId(), List.of(vcf), false);

      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId))
          .hasSize(1)
          .singleElement()
          .satisfies(res -> {
            assertThat(res.getVenue().getId()).isEqualTo(venueId);
            assertThat(res.getApModel()).isEqualTo(apModel);
          });
    }

    @Test
    void testDeleteAp_whenVenueHasUpgradeSchedule_updateSchedule(
        ApGroup apGroup,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.511") ApVersion version491,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.2.103.588") ApVersion version492,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.3.103.888") ApVersion version493)
        throws Exception {
      final var tenantId = apGroup.getTenant().getId();
      final var userName = randomName();
      Ap r500Ap = createAp(apGroup, randomSerialNumber(), "R500", null);
      Ap r550Ap = createAp(apGroup, randomSerialNumber(), "R550", null);
      Ap r560Ap = createAp(apGroup, randomSerialNumber(), "R560", null);
      Ap r770Ap1 = createAp(apGroup, randomSerialNumber(), "R770", null);
      Ap r770Ap2 = createAp(apGroup, randomSerialNumber(), "R770", null);
      List<Ap> apList = List.of(r500Ap, r550Ap, r560Ap, r770Ap1, r770Ap2);

      apList.forEach(ap -> {
        repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
        mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.Operational);
      });

      apList.stream().map(Ap::getModel).distinct().forEach(model -> {
        repositoryUtil.createOrUpdate(
            VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(apGroup.getVenue(), version491, model),
            tenantId, randomTxId());
      });
      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId)).hasSize(4);

      UpgradeSchedule schedule = createUpgradeSchedule(apGroup.getVenue(), version492);
      UpgradeScheduleFirmwareVersion scheduleVer492 =
          UpgradeScheduleFirmwareVersionTestFixture.randomUpgradeScheduleFirmwareVersion(
              schedule, version492, List.of(r500Ap.getModel()), c -> {});
      repositoryUtil.createOrUpdate(scheduleVer492, tenantId, randomTxId());
      UpgradeScheduleFirmwareVersion scheduleVer493 =
          UpgradeScheduleFirmwareVersionTestFixture.randomUpgradeScheduleFirmwareVersion(
              schedule, version493, List.of(r550Ap.getModel(), r560Ap.getModel(), r770Ap1.getModel()), c -> {});
      repositoryUtil.createOrUpdate(scheduleVer493, tenantId, randomTxId());

      // Delete AP
      final var delRequestId = randomTxId();
      List<String> deleteApIds = List.of(r500Ap.getId(), r550Ap.getId(), r770Ap1.getId());
      messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_APS, userName, deleteApIds);

      messageCaptors.getDeviceNotificationMessageCaptor().assertNotSentByTenant(tenantId);
      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId)).hasSize(2)
          .extracting(VenueCurrentFirmware::getApModel)
          .containsExactlyInAnyOrder("R560", "R770");
      final var cmnCfgCollectorMsg = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, delRequestId);
      validateCmnCfgCollectorMsg(cmnCfgCollectorMsg, apGroup.getVenue().getId(),
          repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId), false);
      assertThat(repositoryUtil.find(UpgradeScheduleFirmwareVersion.class, scheduleVer492.getId(), tenantId)).isNull();
      assertThat(repositoryUtil.find(UpgradeScheduleFirmwareVersion.class, scheduleVer493.getId(), tenantId))
          .isNotNull()
          .extracting(UpgradeScheduleFirmwareVersion::getTargetApModels)
          .asList()
          .containsExactlyInAnyOrder("R560", "R770");
    }

    @Test
    void testDeleteAp_whenVenueHasUpgradeScheduleAndRemoveAllScheduleModels_deleteSchedule(
        ApGroup apGroup,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "*********.511") ApVersion version491,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.2.103.588") ApVersion version492,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "4.9.3.103.888") ApVersion version493)
        throws Exception {
      final var tenantId = apGroup.getTenant().getId();
      final var userName = randomName();
      Ap r500Ap = createAp(apGroup, randomSerialNumber(), "R500", null);
      Ap r550Ap = createAp(apGroup, randomSerialNumber(), "R550", null);
      Ap r560Ap = createAp(apGroup, randomSerialNumber(), "R560", null);
      Ap r750Ap = createAp(apGroup, randomSerialNumber(), "R750", null);
      List<Ap> apList = List.of(r500Ap, r550Ap, r560Ap, r750Ap);

      apList.forEach(ap -> {
        repositoryUtil.createOrUpdate(ap, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(
            VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(apGroup.getVenue(), version491, ap.getModel()),
            tenantId, randomTxId());
        // Set the AP is operational
        mockOperationalAp(ap.getId(), tenantId, ApSubStateEnum.Operational);
      });
      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId)).hasSize(4);

      UpgradeSchedule schedule = createUpgradeSchedule(apGroup.getVenue(), version492);
      UpgradeScheduleFirmwareVersion scheduleVer492 =
          UpgradeScheduleFirmwareVersionTestFixture.randomUpgradeScheduleFirmwareVersion(
              schedule, version492, List.of(r500Ap.getModel()), c -> {});
      repositoryUtil.createOrUpdate(scheduleVer492, tenantId, randomTxId());
      UpgradeScheduleFirmwareVersion scheduleVer493 =
          UpgradeScheduleFirmwareVersionTestFixture.randomUpgradeScheduleFirmwareVersion(
              schedule, version493, List.of(r550Ap.getModel(), r560Ap.getModel()), c -> {});
      repositoryUtil.createOrUpdate(scheduleVer493, tenantId, randomTxId());

      // Delete AP
      final var delRequestId = randomTxId();
      List<String> deleteApIds = List.of(r500Ap.getId(), r550Ap.getId(), r560Ap.getId());
      messageUtil.sendWifiCfgRequest(tenantId, delRequestId, CfgAction.DELETE_APS, userName, deleteApIds);

      messageCaptors.getDeviceNotificationMessageCaptor().assertNotSentByTenant(tenantId);
      assertThat(repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId)).hasSize(1)
          .extracting(VenueCurrentFirmware::getApModel)
          .containsExactlyInAnyOrder("R750");
      final var cmnCfgCollectorMsg = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, delRequestId);
      validateCmnCfgCollectorMsg(cmnCfgCollectorMsg, apGroup.getVenue().getId(),
          repositoryUtil.findAll(VenueCurrentFirmware.class, tenantId), false);
      assertThat(repositoryUtil.find(UpgradeSchedule.class, schedule.getId(), tenantId)).isNull();
      assertThat(repositoryUtil.find(UpgradeScheduleFirmwareVersion.class, scheduleVer492.getId(), tenantId)).isNull();
      assertThat(repositoryUtil.find(UpgradeScheduleFirmwareVersion.class, scheduleVer493.getId(), tenantId)).isNull();
    }

    private void validateCmnCfgCollectorMsg(KafkaProtoMessage<ViewmodelCollector> cmnCfgCollectorMessage,
        String venueId, List<VenueCurrentFirmware> vcfList, Boolean isApUpToUpdate) {
      Tuple[] vcfTuples = vcfList.stream().map(vcf -> new Tuple(vcf.getApModel(), vcf.getFirmware().getId()))
          .toArray(Tuple[]::new);
      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .extracting(ViewmodelCollector::getOperationsList).asList()
          .isNotEmpty()
          .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
          .filteredOn(op -> Index.VENUE.equals(op.getIndex()) && op.getOpType() == OpType.MOD)
          .filteredOn(
              op -> venueId.equals(op.getId()) && op.getDocMap().containsKey(EsConstants.Key.CURRENT_AP_FIRMWARES))
          .isNotEmpty().singleElement()
          .extracting(Operations::getDocMap)
          .satisfies(docMap -> {
            assertThat(docMap.get(EsConstants.Key.CURRENT_AP_FIRMWARES).getListValue().getValuesList())
                .extracting(c -> c.getStructValue().getFieldsMap())
                .extracting(map -> map.get(Key.AP_MODEL).getStringValue(), map -> map.get(Key.FIRMWARE).getStringValue())
                .containsExactlyInAnyOrder(vcfTuples);
            if (isApUpToUpdate != null) {
              assertThat(docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE).getBoolValue()).isEqualTo(isApUpToUpdate);
            } else {
              assertThat(docMap.get(Key.IS_AP_FIRMWARE_UP_TO_DATE).getNullValue()).isEqualTo(NullValue.NULL_VALUE);
            }
            assertThat(docMap.get(Key.LAST_AP_FIRMWARE_UPDATE).getNullValue()).isEqualTo(NullValue.NULL_VALUE);
          });
    }
  }

  private void mockOperationalAp(String serialNumber, String tenantId, ApSubStateEnum stateEnum) {
    List<ApDTO> apsList = new ArrayList<>();
    apsList.add(ApDTO.builder().serialNumber(serialNumber).subState(stateEnum)
        .build());
    doReturn(apsList).when(viewmodelClientGrpc).getApsBySerialNumber(anyString(),
        eq(tenantId), anySet());
  }

  private UpgradeSchedule createUpgradeSchedule(Venue venue, ApVersion apVersion) {
    ScheduleTimeSlot sts = ScheduleTimeSlotTestFixture.randomScheduleTimeSlot(c -> {});
    repositoryUtil.createOrUpdate(sts, venue.getTenant().getId(), randomTxId());
    UpgradeSchedule schedule = UpgradeScheduleTestFixture.randomUpgradeSchedule(venue, apVersion, sts,
        c -> {});
    return repositoryUtil.createOrUpdate(schedule, venue.getTenant().getId(), randomTxId());
  }

  public static Consumer<ByteString> assertDnb(String serialNumber) {
    return byteString -> {
      try {
        assertThat(Dnb.DNB.parseFrom(byteString))
            .extracting(DNB::getTaskFactoryReset)
            .extracting(TaskFactoryReset::getCallbackUrl)
            .matches(callback -> callback.contains(serialNumber));
      } catch (InvalidProtocolBufferException e) {
        throw new RuntimeException(e);
      }
    };
  }
}
