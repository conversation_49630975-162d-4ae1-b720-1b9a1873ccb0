package com.ruckus.cloud.wifi.service.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.notification.VenueUpgradeVersionsMapping;
import com.ruckus.cloud.wifi.notification.route.TenantFirmwareNotificationRouter;
import com.ruckus.cloud.wifi.repository.VenueCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.service.impl.ApBranchFamilyServiceRouter;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.kafka.publisher.WifiSchedulePublisher;
import com.ruckus.cloud.wifi.notification.NotificationUpdateFirmwareHandler;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.repository.ScheduleTimeSlotRepository;
import com.ruckus.cloud.wifi.repository.TenantRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.repository.VenueFirmwareVersionRepository;
import com.ruckus.cloud.wifi.service.AggregateNotificationService;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.ScheduleTimeSlotService;
import com.ruckus.cloud.wifi.service.TenantAvailableApFirmwareService;
import com.ruckus.cloud.wifi.service.UpgradeScheduleService;
import com.ruckus.cloud.wifi.service.WifiScheduleService;
import com.ruckus.cloud.wifi.service.entity.UpdateFirmwareSchedule;
import com.ruckus.cloud.wifi.service.impl.ScheduleTimeSlotServiceImpl;
import com.ruckus.cloud.wifi.service.impl.UpgradeScheduleServiceImpl;
import com.ruckus.cloud.wifi.service.impl.WifiScheduleServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.DhcpServiceMockTestConfig;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.servicemodel.enums.TimePeriod;
import com.ruckus.cloud.wifi.servicemodel.enums.UpgradeScheduleStatus;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.utils.RksTransactionHelper;
import java.time.DayOfWeek;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockReset;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;

@WifiJpaDataTest
public class WifiScheduleServiceTest {

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  private static final List<DayOfWeek> DAY_OF_WEEKS =
      List.of(DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.SUNDAY);

  private static final List<String> TIME_PERIODS =
      List.of(TimePeriod.TIME_PERIOD_06_08.name(), TimePeriod.TIME_PERIOD_12_14.name());

  private static final String kairosPayloadParameterFormat = "\"parameters\":[\"%s";

  @SpyBean private WifiScheduleService wifiScheduleService;

  @SpyBean private ExtendedVenueServiceCtrl venueServiceCtrl;

  @SpyBean private UpgradeScheduleService upgradeScheduleService;

  @MockBean(reset = MockReset.BEFORE)
  private WifiSchedulePublisher wifiSchedulePublisher;

  @MockBean private TenantFirmwareNotificationRouter tenantFirmwareNotificationRouter;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired private UpgradeScheduleRepository upgradeScheduleRepository;

  @Test
  public void testSendMailTrigger_SecondVenueNoSchedule(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.3.1.1.1") ApVersion version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.3.1.1.2") ApVersion targetVersion) throws Exception {
    // Given
    tenant.setLatestReleaseVersion(version);
    repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.newRandomId());
    Venue venueA = createVenue(tenant, version, "America/Los_Angeles");
    Venue venueB = createVenue(tenant, version, "America/Los_Angeles");
    VenueCurrentFirmware vcfA = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venueA, version, "R550");
    VenueCurrentFirmware vcfB = VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venueB, version, "R550");
    repositoryUtil.createOrUpdate(vcfA, tenant.getId(), txCtxExtension.newRandomId());
    repositoryUtil.createOrUpdate(vcfB, tenant.getId(), txCtxExtension.newRandomId());
    doNothing().when(venueServiceCtrl).arrangeUpgradeSchedule(any(),any(),any(),any());
    UpgradeSchedule usA = new UpgradeSchedule("UpgradeScheduleId");
    doReturn(List.of(usA)).when(upgradeScheduleService).getSchedulesByVenueId(eq(venueA.getId()));
    doReturn(List.of()).when(upgradeScheduleService).getSchedulesByVenueId(eq(venueB.getId()));

    // When
    wifiScheduleService.createScheduleByApModel(buildUpdateFwSchedule(targetVersion, tenant));

    // Then
    verify(tenantFirmwareNotificationRouter, times(1))
        .sendCreateScheduleNotifications(any(Tenant.class), any(), any(), anyString());
  }

  @Test
  public void testCreateSchedule(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.1.1.1.1") ApVersion version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.1.1.1.2") ApVersion targetVersion) throws Exception {
    tenant.setLatestReleaseVersion(version);
    repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.newRandomId());
    Venue venueA = createVenue(tenant, version, "America/Los_Angeles");
    Venue venueB = createVenue(tenant, version, "America/Los_Angeles");

    wifiScheduleService.createSchedule(buildUpdateFwSchedule(targetVersion, tenant));

    String usId1 = upgradeScheduleRepository.findAllByVenueId(venueA.getId()).get(0).getId();
    String usId2 = upgradeScheduleRepository.findAllByVenueId(venueB.getId()).get(0).getId();

    ArgumentCaptor<String> parametersCaptor = ArgumentCaptor.forClass(String.class);

    verify(wifiSchedulePublisher, times(2)).publishKairosRegister(parametersCaptor.capture(), anyMap());
    assertThat(parametersCaptor.getAllValues()).isNotNull().hasSize(2)
        .anyMatch(s -> s.contains(String.format(kairosPayloadParameterFormat, usId1)))
        .anyMatch(s -> s.contains(String.format(kairosPayloadParameterFormat, usId2)));
    verify(tenantFirmwareNotificationRouter, times(1))
        .sendCreateScheduleNotifications(any(Tenant.class), any(), any(), anyString());
    verifyCreatedVenueSchedule(venueA, targetVersion, false);
    verifyCreatedVenueSchedule(venueB, targetVersion, false);
  }

  @Test
  public void testCreateSchedule_sendCreateScheduleNotificationsFailed(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.1.1.1.1") ApVersion version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.1.1.1.2") ApVersion targetVersion) throws Exception {
    tenant.setLatestReleaseVersion(version);
    repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.newRandomId());
    Venue venueA = createVenue(tenant, version, "America/Los_Angeles");
    doThrow(new RuntimeException("send notification failed"))
        .when(tenantFirmwareNotificationRouter)
        .sendCreateScheduleNotifications(any(Tenant.class), any(), any(), anyString());

    wifiScheduleService.createSchedule(buildUpdateFwSchedule(targetVersion, tenant));

    verify(wifiSchedulePublisher, times(1)).publishKairosRegister(anyString(), anyMap());
    verifyCreatedVenueSchedule(venueA, targetVersion, false);
  }

  @Test
  public void testScheduleSameVersionAsVenueActiveVersion(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.1") ApVersion version62,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.1") ApVersion version60)
      throws Exception {
    tenant.setLatestReleaseVersion(version62);
    repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.newRandomId());
    Venue venueA = createVenue(tenant, version62, "America/Los_Angeles");
    VenueFirmwareVersion vfv = new VenueFirmwareVersion(txCtxExtension.newRandomId());
    vfv.setTenant(tenant);
    vfv.setBranchType("abf1");
    vfv.setVenue(venueA);
    vfv.setCurrentFirmwareVersion(version60);
    repositoryUtil.createOrUpdate(vfv, tenant.getId(), txCtxExtension.newRandomId());

    wifiScheduleService.createSchedule(buildUpdateFwSchedule(version62, tenant));

    verify(wifiSchedulePublisher, never()).publishKairosRegister(anyString(), anyMap());
    verify(tenantFirmwareNotificationRouter, times(0))
        .sendCreateScheduleNotifications(any(Tenant.class), any(), any(), anyString());
  }

  @Test
  public void testScheduleSameVersionAsVenueLegacyVersion(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.1") ApVersion version62,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.1") ApVersion version60)
      throws Exception {
    tenant.setLatestReleaseVersion(version62);
    repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.newRandomId());
    Venue venueA = createVenue(tenant, version62, "America/Los_Angeles");
    VenueFirmwareVersion vfv = new VenueFirmwareVersion(txCtxExtension.newRandomId());
    vfv.setTenant(tenant);
    vfv.setBranchType("abf1");
    vfv.setVenue(venueA);
    vfv.setCurrentFirmwareVersion(version60);
    repositoryUtil.createOrUpdate(vfv, tenant.getId(), txCtxExtension.newRandomId());

    wifiScheduleService.createSchedule(buildUpdateFwSchedule(version60, tenant));

    verify(wifiSchedulePublisher, never()).publishKairosRegister(anyString(), anyMap());
    verify(tenantFirmwareNotificationRouter, times(0))
        .sendCreateScheduleNotifications(any(Tenant.class), any(), any(), anyString());
  }

  @Test
  public void testCreateScheduleNotIncludeRunningSchedule(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.1.1.1.1") ApVersion version,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "5.1.1.1.2") ApVersion targetVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.100") ApVersion targetVersion2) throws Exception {
    tenant.setLatestReleaseVersion(version);
    repositoryUtil.createOrUpdate(tenant, tenant.getId(), txCtxExtension.newRandomId());
    Venue venueA = createVenue(tenant, version, "America/Los_Angeles");
    Venue venueB = createVenue(tenant, version, "America/Los_Angeles");
    createRunningUpgradeSchedule(venueB, tenant, targetVersion2);

    wifiScheduleService.createSchedule(buildUpdateFwSchedule(targetVersion, tenant));

    ArgumentCaptor<VenueUpgradeVersionsMapping> versionMapping =
        ArgumentCaptor.forClass(VenueUpgradeVersionsMapping.class);

    verify(wifiSchedulePublisher, times(2)).publishKairosRegister(any(), anyMap());
    verify(tenantFirmwareNotificationRouter, times(1))
        .sendCreateScheduleNotifications(any(Tenant.class), any(), versionMapping.capture(), anyString());
    assertThat(versionMapping.getValue().getVersionsByVenueId(venueB.getId()))
        .containsExactly("5.1.1.1.2");
  }

  private void verifyCreatedVenueSchedule(
      Venue venue, ApVersion targetVersion, boolean isCreateFailed) {
    if (isCreateFailed) {
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId())).isNotNull().hasSize(0);
    } else {
      assertThat(upgradeScheduleRepository.findAllByVenueId(venue.getId()))
          .isNotNull().hasSize(1).singleElement()
          .matches(s -> s.getStatus().equals(UpgradeScheduleStatus.PENDING))
          .matches(s -> s.getVersion().getId().equals(targetVersion.getId()));
    }
  }

  private Venue createVenue(Tenant tenant, ApVersion version, String timezone){
    Venue venue = VenueTestFixture.randomVenue(tenant, v -> {
      v.setTenant(tenant);
      v.setWifiFirmwareVersion(version);
      v.setTimezone(timezone);
    });
    repositoryUtil.createOrUpdate(venue, tenant.getId(), txCtxExtension.newRandomId());
    return venue;
  }

  private UpdateFirmwareSchedule buildUpdateFwSchedule(ApVersion targetVersion, Tenant tenant){
    return UpdateFirmwareSchedule.builder()
        .version(targetVersion.getId())
        .tenantId(tenant.getId())
        .requestId(txCtxExtension.getRequestId())
        .days(DAY_OF_WEEKS)
        .times(TIME_PERIODS)
        .build();
  }

  private UpgradeSchedule createRunningUpgradeSchedule(Venue venue, Tenant tenant, ApVersion apVersion) {
    ScheduleTimeSlot timeSlot = new ScheduleTimeSlot(UUID.randomUUID().toString());
    repositoryUtil.createOrUpdate(timeSlot, tenant.getId(), txCtxExtension.newRandomId());
    UpgradeSchedule schedule = new UpgradeSchedule(UUID.randomUUID().toString());
    schedule.setVenue(venue);
    schedule.setVersion(apVersion);
    schedule.setStatus(UpgradeScheduleStatus.RUNNING);
    schedule.setTenant(tenant);
    schedule.setTimeSlot(timeSlot);
    repositoryUtil.createOrUpdate(schedule, tenant.getId(), txCtxExtension.newRandomId());
    UpgradeScheduleFirmwareVersion upgradeScheduleFirmwareVersion =
        new UpgradeScheduleFirmwareVersion(UUID.randomUUID().toString());
    upgradeScheduleFirmwareVersion.setApFirmwareVersion(apVersion);
    upgradeScheduleFirmwareVersion.setUpgradeSchedule(schedule);
    upgradeScheduleFirmwareVersion.setTenant(tenant);
    repositoryUtil.createOrUpdate(upgradeScheduleFirmwareVersion, tenant.getId(), txCtxExtension.newRandomId());
    return schedule;
  }

  @TestConfiguration
  @Import({
      ExtendedVenueServiceCtrlImplTestConfig.class,
      DhcpServiceMockTestConfig.class
  })
  static class TestConfig {
    @Bean
    public WifiScheduleService wifiScheduleService(
        ExtendedVenueServiceCtrl extendedVenueServiceCtrl,
        ScheduleTimeSlotService scheduleTimeSlotService,
        UpgradeScheduleService upgradeScheduleService,
        TenantAvailableApFirmwareService tenantAvailableApFirmwareService,
        TenantFirmwareNotificationRouter tenantFirmwareNotificationRouter,
        WifiSchedulePublisher wifiSchedulePublisher,
        TenantRepository tenantRepository,
        ScheduleTimeSlotRepository scheduleTimeSlotRepository,
        ApVersionRepository apVersionRepository,
        VenueFirmwareVersionRepository venueFirmwareVersionRepository,
        AggregateNotificationService aggregateNotificationService,
        VenueCurrentFirmwareRepository venueCurrentFirmwareRepository) {
      return new WifiScheduleServiceImpl(
          extendedVenueServiceCtrl,
          scheduleTimeSlotService,
          upgradeScheduleService,
          tenantAvailableApFirmwareService,
          tenantFirmwareNotificationRouter,
          wifiSchedulePublisher,
          tenantRepository,
          scheduleTimeSlotRepository,
          apVersionRepository,
          venueFirmwareVersionRepository,
          aggregateNotificationService,
          venueCurrentFirmwareRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public NotificationUpdateFirmwareHandler notificationUpdateFirmwareHandler() {
      return mock(NotificationUpdateFirmwareHandler.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public WifiSchedulePublisher wifiSchedulePublisher() {
      return mock(WifiSchedulePublisher.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public RksTransactionHelper transactionHandler() {
      return new RksTransactionHelper();
    }

    @Bean
    @ConditionalOnMissingBean
    public ScheduleTimeSlotService scheduleTimeSlotService(
        ScheduleTimeSlotRepository scheduleTimeSlotRepository,
        RksTransactionHelper rksTransactionHelper) {
      return new ScheduleTimeSlotServiceImpl(scheduleTimeSlotRepository, rksTransactionHelper);
    }

    @Bean
    @ConditionalOnMissingBean
    public ApBranchFamilyServiceRouter apBranchFamilyService() {
      return mock(ApBranchFamilyServiceRouter.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantAvailableApFirmwareService tenantAvailableApFirmwareService() {
      return mock(TenantAvailableApFirmwareService.class);
    }

    @Bean
    @Primary
    public UpgradeScheduleService upgradeScheduleService(
        UpgradeScheduleRepository upgradeScheduleRepository, ApBranchFamilyServiceRouter apBranchFamilyService,
        UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository,
        ApVersionRepository apVersionRepository,
        VenueCurrentFirmwareRepository venueCurrentFirmwareRepository) {
      return new UpgradeScheduleServiceImpl(upgradeScheduleRepository, apBranchFamilyService,
          upgradeScheduleFirmwareVersionRepository, apVersionRepository, venueCurrentFirmwareRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public AggregateNotificationService aggregateNotificationService() {
      return mock(AggregateNotificationService.class);
    }
  }
}
