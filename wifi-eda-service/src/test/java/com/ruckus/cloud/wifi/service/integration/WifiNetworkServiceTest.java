package com.ruckus.cloud.wifi.service.integration;

import com.ruckus.cloud.tenantservice.api.GetTenantResponse;
import com.ruckus.cloud.tenantservice.api.PrivacySetting;
import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.service.WifiNetworkServiceCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkHotspot20Settings;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Hotspot20Ipv4AddressTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ManagementFrameProtectionEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.Hotspot20ConnectionCapabilityRepository;
import com.ruckus.cloud.wifi.service.core.exception.InvalidPropertyValueException;
import com.ruckus.cloud.wifi.service.integration.config.WifiNetworkServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.service.template.TemplateManagementService;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.Hotspot20NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.Objects;

import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@Tag("WifiNetworkTest")
@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
class WifiNetworkServiceTest {

  @Autowired
  private WifiNetworkServiceCtrl wifiNetworkServiceCtrl;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private Hotspot20ConnectionCapabilityRepository hotspot20ConnectionCapabilityRepository;

  @MockBean
  private TenantClient tenantClient;

  @MockBean
  private TemplateManagementService templateManagementService;

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testAddHotspot20WifiNetwork(Tenant tenant) throws Exception {
    doReturn(GetTenantResponse
            .newBuilder()
            .addPrivacySettings(PrivacySetting.newBuilder().setName("ARC").setIsEnabled(true).build())
            .build()).when(tenantClient).getTenantData(any(), any());
    // Add hotspot 2.0 network
    var addPayload = Hotspot20NetworkTestFixture.randomHotspot20Network(tenant);
    var added = (Hotspot20Network) wifiNetworkServiceCtrl.addWifiNetwork(addPayload);
    var hotspot20SettingsId = added.getHotspot20Settings().getId();

    var addedDb = repositoryUtil.find(Network.class, addPayload.getId());
    assertThat(addedDb)
        .isNotNull()
        .extracting(n -> (Hotspot20Network) n)
        .matches(n -> Objects.equals(NetworkTypeEnum.HOTSPOT20, n.getType()))
        .extracting(Hotspot20Network::getHotspot20Settings)
        .isNotNull()
        .extracting(NetworkHotspot20Settings::getConnectionCapabilities).asList()
        .isNotEmpty();
  }

  @Test
  @FeatureFlag(enable = {FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE, FlagNames.WIFI_NETWORK_APPLICATION_CONTROL_TOGGLE_GLOBAL_DEFAULT})
  void testAddHotspot20WifiNetwork_failGetApplicationVisibilityEnabled(Tenant tenant) throws Exception {
    doReturn(GetTenantResponse
            .newBuilder()
            .addPrivacySettings(PrivacySetting.newBuilder().setName("APP_VISIBILITY").setIsEnabled(true).build())
            .build()).when(tenantClient).getTenantData(any(), any());
    // Add hotspot 2.0 network
    var addPayload = Hotspot20NetworkTestFixture.randomHotspot20Network(tenant);
    var added = (Hotspot20Network) wifiNetworkServiceCtrl.addWifiNetwork(addPayload);
    var hotspot20SettingsId = added.getHotspot20Settings().getId();

    var addedDb = repositoryUtil.find(Network.class, addPayload.getId());
    assertThat(addedDb)
            .isNotNull()
            .extracting(n -> (Hotspot20Network) n)
            .matches(n -> Objects.equals(NetworkTypeEnum.HOTSPOT20, n.getType()))
            .extracting(Hotspot20Network::getHotspot20Settings)
            .isNotNull()
            .extracting(NetworkHotspot20Settings::getConnectionCapabilities).asList()
            .isNotEmpty();
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testAddHotspot20WifiNetwork_withoutConnectionCapabilities(Tenant tenant) throws Exception {
    doReturn(GetTenantResponse
            .newBuilder()
            .addPrivacySettings(PrivacySetting.newBuilder().setName("ARC").setIsEnabled(true).build())
            .build()).when(tenantClient).getTenantData(any(), any());
    // Add hotspot 2.0 network
    var addPayload = Hotspot20NetworkTestFixture.randomHotspot20Network(tenant,
        n -> n.getHotspot20Settings().setConnectionCapabilities(List.of()));
    var added = (Hotspot20Network) wifiNetworkServiceCtrl.addWifiNetwork(addPayload);
    var hotspot20SettingsId = added.getHotspot20Settings().getId();

    var addedDb = repositoryUtil.find(Network.class, addPayload.getId());
    assertThat(addedDb)
        .isNotNull()
        .extracting(n -> (Hotspot20Network) n)
        .matches(n -> Objects.equals(NetworkTypeEnum.HOTSPOT20, n.getType()))
        .extracting(Hotspot20Network::getHotspot20Settings)
        .isNotNull()
        .extracting(NetworkHotspot20Settings::getConnectionCapabilities).asList()
        .isEmpty();
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testUpdateHotspot20WifiNetwork(Tenant tenant) throws Exception {
    // Given
    final var network = network(Hotspot20Network.class).generate();
    final var connectionCapability1 = Generators.hotspot20ConnectionCapability((short) 1, 0).generate();
    final var connectionCapability2 = Generators.hotspot20ConnectionCapability((short) 6, 20).generate();
    final var networkHotspot20Settings = Generators.networkHotspot20Settings().generate();
    connectionCapability1.setNetworkHotspot20Settings(networkHotspot20Settings);
    connectionCapability2.setNetworkHotspot20Settings(networkHotspot20Settings);
    networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability1, connectionCapability2));

    final var hotspot20Network = (Hotspot20Network) network;
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

    repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(connectionCapability1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(connectionCapability2, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(hotspot20Network, tenant.getId(), randomTxId());

    // Update hotspot 2.0 network
    var updatePayload = Hotspot20NetworkTestFixture.randomHotspot20Network(tenant, n -> n.setId(hotspot20Network.getId()));
    updatePayload.setAccountingInterimUpdates((short) 100);
    updatePayload.getHotspot20Settings().setIpv4AddressType(Hotspot20Ipv4AddressTypeEnum.PUBLIC);
    final var connectionCapability3 = Generators.hotspot20ConnectionCapability((short) 6, 22).generate();
    final var connectionCapability4 = Generators.hotspot20ConnectionCapability((short) 6, 80).generate();
    updatePayload.getHotspot20Settings().setConnectionCapabilities(List.of(
        connectionCapability1, connectionCapability3, connectionCapability4
    ));

    var updated = wifiNetworkServiceCtrl.updateWifiNetwork(updatePayload.getId(), updatePayload);

    var updatedDb = repositoryUtil.find(Network.class, updatePayload.getId());
    assertThat(updatedDb)
        .isNotNull()
        .extracting(n -> (Hotspot20Network) n)
        .matches(n -> Objects.equals(NetworkTypeEnum.HOTSPOT20, n.getType()))
        .matches(n -> Objects.equals((short) 100, n.getAccountingInterimUpdates()))
        .extracting(Hotspot20Network::getHotspot20Settings)
        .matches(hs -> Objects.equals(Hotspot20Ipv4AddressTypeEnum.PUBLIC, hs.getIpv4AddressType()))
        .isNotNull()
        .extracting(NetworkHotspot20Settings::getConnectionCapabilities).asList() // Cleanup
        .hasSize(3); // 1, 3, 4
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testUpdateHotspot20WifiNetwork_withoutConnectionCapabilities(Tenant tenant) throws Exception {
    // Given
    final var network = network(Hotspot20Network.class).generate();
    final var connectionCapability1 = Generators.hotspot20ConnectionCapability((short) 1, 0).generate();
    final var connectionCapability2 = Generators.hotspot20ConnectionCapability((short) 6, 20).generate();
    final var networkHotspot20Settings = Generators.networkHotspot20Settings().generate();
    connectionCapability1.setNetworkHotspot20Settings(networkHotspot20Settings);
    connectionCapability2.setNetworkHotspot20Settings(networkHotspot20Settings);
    networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability1, connectionCapability2));

    final var hotspot20Network = (Hotspot20Network) network;
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

    repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(connectionCapability1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(connectionCapability2, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(hotspot20Network, tenant.getId(), randomTxId());

    // Update hotspot 2.0 network
    var updatePayload = Hotspot20NetworkTestFixture.randomHotspot20Network(tenant, n -> n.setId(hotspot20Network.getId()));
    updatePayload.setAccountingInterimUpdates((short) 100);
    updatePayload.getHotspot20Settings().setIpv4AddressType(Hotspot20Ipv4AddressTypeEnum.PUBLIC);
    updatePayload.getHotspot20Settings().setConnectionCapabilities(List.of());

    var updated = wifiNetworkServiceCtrl.updateWifiNetwork(updatePayload.getId(), updatePayload);
    var updatedDb = repositoryUtil.find(Network.class, updatePayload.getId());
    assertThat(updatedDb)
        .isNotNull()
        .extracting(n -> (Hotspot20Network) n)
        .matches(n -> Objects.equals(NetworkTypeEnum.HOTSPOT20, n.getType()))
        .matches(n -> Objects.equals((short) 100, n.getAccountingInterimUpdates()))
        .extracting(Hotspot20Network::getHotspot20Settings)
        .matches(hs -> Objects.equals(Hotspot20Ipv4AddressTypeEnum.PUBLIC, hs.getIpv4AddressType()))
        .isNotNull()
        .extracting(NetworkHotspot20Settings::getConnectionCapabilities).asList()
        .isEmpty();

    // Verify no orphans on database
    assertFalse(hotspot20ConnectionCapabilityRepository.existsByTenantIdAndNetworkHotspot20SettingsId(tenant.getId(), networkHotspot20Settings.getId()));
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_HOTSPOT20_R1_TOGGLE)
  void testDeleteHotspot20WifiNetwork(Tenant tenant) throws Exception {
    // Given
    final var network = network(Hotspot20Network.class).generate();
    final var connectionCapability1 = Generators.hotspot20ConnectionCapability((short) 1, 0).generate();
    final var connectionCapability2 = Generators.hotspot20ConnectionCapability((short) 6, 20).generate();
    final var networkHotspot20Settings = Generators.networkHotspot20Settings().generate();
    connectionCapability1.setNetworkHotspot20Settings(networkHotspot20Settings);
    connectionCapability2.setNetworkHotspot20Settings(networkHotspot20Settings);
    networkHotspot20Settings.setConnectionCapabilities(List.of(connectionCapability1, connectionCapability2));

    final var hotspot20Network = (Hotspot20Network) network;
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    hotspot20Network.setHotspot20Settings(networkHotspot20Settings);

    repositoryUtil.createOrUpdate(networkHotspot20Settings, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(connectionCapability1, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(connectionCapability2, tenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(hotspot20Network, tenant.getId(), randomTxId());

    // Delete hotspot 2.0 network
    wifiNetworkServiceCtrl.deleteWifiNetwork(hotspot20Network.getId());
    var deletedDb = repositoryUtil.find(Network.class, hotspot20Network.getId());
    assertThat(deletedDb)
        .isNull();

    // Verify no orphans on database
    assertFalse(hotspot20ConnectionCapabilityRepository.existsByTenantIdAndNetworkHotspot20SettingsId(tenant.getId(), networkHotspot20Settings.getId()));
  }

  @Test
  @FeatureFlag(disable = FlagNames.WIFI_FR_ALLOW_NETWORK_TYPE_MODIFICATION_TOGGLE)
  void testUpdateOpenToPskWifiNetworkShouldThrowInvalidPropertyValueException(
      OpenNetwork openNetwork) {
    final var pskNetwork = NetworkTestFixture.randomPskNetwork(openNetwork.getTenant(),
        network -> network.setId(openNetwork.getId()));

    assertThatExceptionOfType(InvalidPropertyValueException.class).isThrownBy(
            () -> wifiNetworkServiceCtrl.updateWifiNetwork(openNetwork.getId(), pskNetwork))
        .withMessage("Network type cannot be changed. Network id: " + openNetwork.getId())
        .extracting(InvalidPropertyValueException::getErrorCode)
        .isEqualTo(Errors.WIFI_10006);
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_FR_ALLOW_NETWORK_TYPE_MODIFICATION_TOGGLE)
  void testUpdateOpenToPskWifiNetworkShouldNotThrowAnyException(OpenNetwork openNetwork) {
    final var pskNetwork = NetworkTestFixture.randomPskNetwork(openNetwork.getTenant(),
        network -> network.setId(openNetwork.getId()));

    assertThatNoException().isThrownBy(
        () -> wifiNetworkServiceCtrl.updateWifiNetwork(openNetwork.getId(), pskNetwork));

    assertThat(repositoryUtil.find(Network.class, pskNetwork.getId()))
        .isNotNull()
        .satisfies(network -> {
          assertThat(network.getName()).isEqualTo(pskNetwork.getName());
          assertThat(network.getType()).isEqualTo(NetworkTypeEnum.PSK);
          assertThat(network).isInstanceOf(PskNetwork.class);
        });
    ;
  }

  @Test
  void testMfpWhenAddAaaNetworkWithWpa2(Tenant tenant) {
    var aaaNetwork = NetworkTestFixture.randomAAANetwork(tenant,
        n -> n.getWlan().setManagementFrameProtection(ManagementFrameProtectionEnum.Optional));
    assertThatNoException().isThrownBy(
        () -> wifiNetworkServiceCtrl.addWifiNetwork(aaaNetwork));

    assertThat(repositoryUtil.find(Network.class, aaaNetwork.getId()))
        .isNotNull()
        .satisfies(network -> {
          assertThat(network.getName()).isEqualTo(aaaNetwork.getName());
          assertThat(network.getWlan().getManagementFrameProtection()).isEqualTo(
              ManagementFrameProtectionEnum.Optional);
          assertThat(network).isInstanceOf(AAANetwork.class);
        });
  }

  @Test
  void testMfpWhenAddOpenNetwork(Tenant tenant) {
    var openNetwork = NetworkTestFixture.randomOpenNetwork(tenant);
    assertThatNoException().isThrownBy(
        () -> wifiNetworkServiceCtrl.addWifiNetwork(openNetwork));

    assertThat(repositoryUtil.find(Network.class, openNetwork.getId()))
        .isNotNull()
        .satisfies(network -> {
          assertThat(network.getName()).isEqualTo(openNetwork.getName());
          assertThat(network.getWlan().getManagementFrameProtection()).isEqualTo(null);
          assertThat(network).isInstanceOf(OpenNetwork.class);
        });
  }

  @Test
  void testMfpWhenAddOpenOweNetwork(Tenant tenant) {
    var openNetwork = NetworkTestFixture.randomOpenNetwork(tenant);
    openNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.OWE);
    assertThatNoException().isThrownBy(
        () -> wifiNetworkServiceCtrl.addWifiNetwork(openNetwork));

    assertThat(repositoryUtil.find(Network.class, openNetwork.getId()))
        .isNotNull()
        .satisfies(network -> {
          assertThat(network.getName()).isEqualTo(openNetwork.getName());
          assertThat(network.getWlan().getManagementFrameProtection()).isEqualTo(ManagementFrameProtectionEnum.Required);
          assertThat(network).isInstanceOf(OpenNetwork.class);
        });
  }

  @Test
  void testMfpWhenUpdateAaaNetworkWithWpa3(AAANetwork aaaNetwork) {
    aaaNetwork.getWlan().setWlanSecurity(WlanSecurityEnum.WPA3);
    assertThatNoException().isThrownBy(
        () -> wifiNetworkServiceCtrl.updateWifiNetwork(aaaNetwork.getId(), aaaNetwork));

    assertThat(repositoryUtil.find(Network.class, aaaNetwork.getId()))
        .isNotNull()
        .satisfies(network -> {
          assertThat(network.getName()).isEqualTo(aaaNetwork.getName());
          assertThat(network.getWlan().getManagementFrameProtection()).isEqualTo(
              ManagementFrameProtectionEnum.Required);
          assertThat(network).isInstanceOf(AAANetwork.class);
        });
  }

  @TestConfiguration
  @Import({WifiNetworkServiceCtrlImplTestConfig.class})
  static class TestConfig {

  }
}
