package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.ruckus.acx.ddccm.protobuf.wifi.AuthRealmProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.AuthRadiusService;
import com.ruckus.cloud.wifi.eda.servicemodel.Radius;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class DdccmAuthRadiusProfileOperationBuilderTest {

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");
  @SpyBean
  private DdccmAuthRadiusProfileOperationBuilder builder;

  @Test
  void testAddAuthRealmProfileBuilder() {

    Radius authRadius = Generators.radiusProfile().generate();
    authRadius.setName(authRadius.getName() + randomString(5).generate());
    AuthRadiusService authRadiusService = Generators.authRadiusService(authRadius).generate();
    AuthRadiusProfile authRadiusProfile = Generators.authRadiusProfile(authRadiusService)
        .generate();
    authRadiusProfile.setTenant(Generators.tenant().generate());
    authRadiusProfile.setHotspot20SupportEnabled(true);

    List<Operation> operations = builder.build(new NewTxEntity<>(authRadiusProfile),
        emptyTxChanges());

    assertEquals(1, operations.size());
    AuthRealmProfile authRealmProfile = operations.get(0).getAuthRealmProfile();

    assertNotNull(authRealmProfile);
    assertEquals(EntityAction.ADD.toString(), operations.get(0).getAction().toString());
    assertEquals(authRadiusProfile.getHotspot20SupportEnabled(),
        authRealmProfile.getH20SuppportEnabled());

  }
}