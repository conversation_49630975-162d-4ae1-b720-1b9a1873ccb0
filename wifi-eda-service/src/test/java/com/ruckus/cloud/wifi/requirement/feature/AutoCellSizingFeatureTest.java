package com.ruckus.cloud.wifi.requirement.feature;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_R370_TOGGLE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.service.validator.ApCapabilityValidator;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class AutoCellSizingFeatureTest {

  @MockBean
  private ApCapabilityValidator apCapabilityValidator;

  @SpyBean
  private AutoCellSizingFeature unit;

  @Test
  @FeatureFlag(disable = WIFI_R370_TOGGLE)
  void givenR370FFDisable(Venue venue) {
    BDDAssertions.then(unit.test(venue)).isFalse();
  }

  @Test
  @FeatureFlag(enable = WIFI_R370_TOGGLE)
  void givenAutoCellSizingCompatibleForVenue(Venue venue) {
    when(apCapabilityValidator.isAutoCellSizingIncompatibleByVenue(
        any(Venue.class))).thenReturn(false);

    BDDAssertions.then(unit.test(venue)).isFalse();
  }

  @Test
  @FeatureFlag(enable = WIFI_R370_TOGGLE)
  void givenAutoCellSizingIncompatibleForVenue(Venue venue) {
    when(apCapabilityValidator.isAutoCellSizingIncompatibleByVenue(
        any(Venue.class))).thenReturn(true);

    BDDAssertions.then(unit.test(venue)).isTrue();
  }
}
