package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        'e465bac6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant) VALUES (
        'venue1', '4c8279f79307415fa9e4c88a1819f0fc');    
    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model) VALUES (
        '4ce4e037e89f433da02ced7993613955',
        'e465bac6afb747a4987d0d0945f77221',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'M510');
    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model, band_mode) VALUES (
        'id1', 'venue1',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'T670', 'TRIPLE');
    INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model) VALUES (
        'id2', 'venue1',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'R770');
    """)
public class VenueApModelSpecificAttributesRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String VENUE_ID = "e465bac6afb747a4987d0d0945f77221";
  private static final String MODEL = "M510";

  @Autowired
  private VenueApModelSpecificAttributesRepository target;

  @Test
  void findByVenueIdAndModelTest() {
    var result = target.findByVenueIdAndModelAndTenantId(VENUE_ID, MODEL, TENANT_ID);
    assertNotNull(result);
    result = target.findByVenueIdAndModelAndTenantId(VENUE_ID, "M520", TENANT_ID);
    assertNull(result);
  }

  @Test
  void findByModelInAndVenueIdAndTenantIdTest() {
    var result = target.findByModelInAndVenueIdAndTenantId(List.of(MODEL), VENUE_ID, TENANT_ID);
    assertNotNull(result);
    assertEquals(1, result.size());
    result = target.findByModelInAndVenueIdAndTenantId(List.of("M520"), VENUE_ID, TENANT_ID);
    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  void existsByTenantIdAndVenueIdAndBandModeNotNullTest() {
    var result = target.existsByTenantIdAndVenueIdAndBandModeNotNull(TENANT_ID, VENUE_ID);
    assertFalse(result);
    result = target.existsByTenantIdAndVenueIdAndBandModeNotNull(TENANT_ID, "venue1");
    assertTrue(result);
  }

  @Test
  void findByTenantIdAndVenueId() {
    List<VenueApModelSpecificAttributes> result = target.findByTenantIdAndVenueId(TENANT_ID, "venue1");
    assertEquals(2, result.size());
    assertTrue(List.of("id2", "id1").contains(result.get(1).getId()));

    List<VenueApModelSpecificAttributes> result2 = target.findByTenantIdAndVenueId(TENANT_ID, "falseVenue");
    assertEquals(0, result2.size());
  }

}
