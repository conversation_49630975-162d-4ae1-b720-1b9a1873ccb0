package com.ruckus.cloud.wifi.integration.lbsserverprofile;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.LbsServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static net.logstash.logback.composite.LogstashVersionJsonProvider.DEFAULT_VERSION;
import static org.assertj.core.api.Assertions.assertThat;

@WifiIntegrationTest
public class ConsumeActivateLbsServerProfileOnVenueRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class Setup {

    private Tenant tenant;

    private Venue venue;

    private LbsServerProfile lbsServerProfile;

    @BeforeEach
    void givenLbsServerProfile(final Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = DEFAULT_VERSION) ApVersion defaultVersion) {
      lbsServerProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.lbsServerProfile().generate();
      repositoryUtil.createOrUpdate(lbsServerProfile, tenant.getId(), randomTxId());

      venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

      this.tenant = tenant;
    }

    @Nested
    class WhenConsumeActivateLbsServerProfileOnVenueMessage {

      @Test
      void thenActivateAndSendingMessages() {
        sendWifiCfgRequest(lbsServerProfile);
        verifyRepository(lbsServerProfile);
        verifyDdccmMessage();
        verifyCmnCfgCollectorMessage(lbsServerProfile);
        verifyActivityCfgChangeMessage();
      }

      @Test
      void thenActivateAndSendingMessagesAgain() {
        venue.setLbsServerProfile(lbsServerProfile);
        repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

        final var lbsServerProfile2 = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.lbsServerProfile().generate();
        repositoryUtil.createOrUpdate(lbsServerProfile2, tenant.getId(), randomTxId());

        sendWifiCfgRequest(lbsServerProfile2);
        verifyRepository(lbsServerProfile2);
        verifyDdccmMessage();
        verifyCmnCfgCollectorMessage(lbsServerProfile, lbsServerProfile2); // old one and new one
        verifyActivityCfgChangeMessage();
      }

      private void sendWifiCfgRequest(LbsServerProfile lbsServerProfile) {
        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.ACTIVATE_LBS_SERVER_PROFILE_ON_VENUE,
            randomName(),
            new RequestParams()
                .addPathVariable("venueId", venue.getId())
                .addPathVariable("lbsServerProfileId", lbsServerProfile.getId()),
            "");
      }

      private void verifyRepository(LbsServerProfile lbsServerProfile) {
        assertThat(repositoryUtil.find(Venue.class, venue.getId(), tenant.getId()))
            .isNotNull()
            .extracting(Venue::getLbsServerProfile)
            .extracting(LbsServerProfile::getId)
            .isEqualTo(lbsServerProfile.getId());
      }

      private void verifyDdccmMessage() {
        assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(
                m -> m.getPayload().getOperationsList(),
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .hasSize(1)
            .first()
            .matches(o -> o.getAction() == Action.MODIFY)
            .matches(o -> o.getId().equals(venue.getId()));
      }

      private void verifyCmnCfgCollectorMessage(LbsServerProfile... lbsServerProfiles) {
        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(lbsServerProfiles.length)
            .allMatch(o -> o.getOpType() == OpType.MOD)
            .allSatisfy(o ->
                assertThat(lbsServerProfiles).anyMatch(p -> o.getId().equals(p.getId()))
            );
      }

      private void verifyActivityCfgChangeMessage() {
        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(a -> a.getStep().equals(ApiFlowNames.ACTIVATE_LBS_SERVER_PROFILE_ON_VENUE));
      }
    }
  }

}
