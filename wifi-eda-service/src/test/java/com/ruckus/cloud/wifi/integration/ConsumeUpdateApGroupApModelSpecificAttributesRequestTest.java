package com.ruckus.cloud.wifi.integration;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.wifi.AntennaTypeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.ApGroupApModel;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroupApModelAntennaTypeSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApGroupApModelExternalAntennaSettings;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueFirmwareVersionTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
@WifiIntegrationTest
@FeatureFlag(enable = {
    FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE,
    FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE,
    FlagNames.WIFI_AP_GROUP_MORE_PARAMETER_PHASE3_TOGGLE
})
public class ConsumeUpdateApGroupApModelSpecificAttributesRequestTest {
  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;
  @Autowired
  private ObjectMapper objectMapper;


  @Test
  @FeatureFlag(enable = FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE)
  void thenApGroupAntennaType(ApGroup apGroup) throws JsonProcessingException {
    upgradeVenueToAbfWifi7Version(apGroup.getVenue());
    final String tenantId = txCtxExtension.getTenantId();
    // default
    final VenueApGroupApModelAntennaTypeSettings request = objectMapper.readValue("""
          {
            "antennaTypeSettings": [{
              "model": "T670SN",
              "antennaType": "NARROW"
            }],
            "useVenueSettings": false
          }
          """, new TypeReference<>() {});
    final String requestId1 = randomTxId();
    sendRequest(
        CfgAction.UPDATE_VENUE_AP_GROUP_ANTENNA_TYPE,
        requestId1,
        apGroup.getVenue().getId(),
        apGroup.getId(),
        request);

    assertActivitySuccess(requestId1, ApiFlowNames.UPDATE_VENUE_AP_GROUP_ANTENNA_TYPE);

    final var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId1);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    log.debug("ddccmRequest={}", ddccmRequest);

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(req -> req.stream().anyMatch(Operation::hasApGroup))
        .isNotEmpty()
        .filteredOn(Operation::hasApGroup)
        .filteredOn(op -> !op.getApGroup().getApGroupApModelsList().isEmpty())
        .first()
        .extracting(Operation::getApGroup)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::getApGroupApModelsList)
        .isNotNull().asList().first()
        .extracting(ApGroupApModel.class::cast)
        .matches(m -> m.getAntennaType().equals(AntennaTypeEnum.Narrow))
        .matches(m -> m.getModelName().equals("T670SN"));

    // use venue settings
    final VenueApGroupApModelAntennaTypeSettings request2 = objectMapper.readValue("""
          {
            "antennaTypeSettings": [{
              "model": "T670SN",
              "antennaType": "NARROW"
            }],
            "useVenueSettings": true
          }
          """, new TypeReference<>() {});
    final String requestId2 = randomTxId();
    sendRequest(
        CfgAction.UPDATE_VENUE_AP_GROUP_ANTENNA_TYPE,
        requestId2,
        apGroup.getVenue().getId(),
        apGroup.getId(),
        request2);

    assertActivitySuccess(requestId2, ApiFlowNames.UPDATE_VENUE_AP_GROUP_ANTENNA_TYPE);

    final var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
    assertThat(record2).isNotNull();
    WifiConfigRequest ddccmRequest2 = record2.getPayload();
    log.debug("ddccmRequest2={}", ddccmRequest2);

    assertThat(ddccmRequest2.getOperationsList()).isNotNull()
        .matches(req -> req.stream().anyMatch(Operation::hasApGroup))
        .isNotEmpty()
        .filteredOn(Operation::hasApGroup)
        .filteredOn(op -> !op.getApGroup().getApGroupApModelsList().isEmpty())
        .first()
        .extracting(Operation::getApGroup)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::getApGroupApModelsList)
        .isNotNull().asList().first()
        .extracting(ApGroupApModel.class::cast)
        .matches(m -> m.getAntennaType().equals(AntennaTypeEnum.AntennaTypeEnum_UNSET)) // reset to default
        .matches(m -> m.getModelName().equals("T670SN"));
  }

  @Test
  @FeatureFlag(enable = FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE)
  void thenApGroupExternalAntennaSettings(ApGroup apGroup) throws JsonProcessingException {
    upgradeVenueToAbfWifi7Version(apGroup.getVenue());
    final String tenantId = txCtxExtension.getTenantId();
    // default
    final VenueApGroupApModelExternalAntennaSettings request = objectMapper.readValue("""
          {
            "externalAntennaSettings": [{
              "model": "E510",
              "enable24G": true,
              "gain24G": 10
            }],
            "useVenueSettings": false
          }
          """, new TypeReference<>() {});
    final String requestId1 = randomTxId();
    sendRequest(
        CfgAction.UPDATE_VENUE_AP_GROUP_AP_MODEL_EXTERNAL_ANTENNA_SETTINGS,
        requestId1,
        apGroup.getVenue().getId(),
        apGroup.getId(),
        request);

    assertActivitySuccess(requestId1, ApiFlowNames.UPDATE_VENUE_AP_GROUP_AP_MODEL_EXTERNAL_ANTENNA_SETTINGS);

    final var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId1);
    assertThat(record).isNotNull();
    WifiConfigRequest ddccmRequest = record.getPayload();
    log.debug("ddccmRequest={}", ddccmRequest);

    assertThat(ddccmRequest.getOperationsList()).isNotNull()
        .matches(req -> req.stream().anyMatch(Operation::hasApGroup))
        .isNotEmpty()
        .filteredOn(Operation::hasApGroup)
        .filteredOn(op -> !op.getApGroup().getApGroupApModelsList().isEmpty())
        .first()
        .extracting(Operation::getApGroup)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::getApGroupApModelsList)
        .isNotNull().asList().first()
        .extracting(ApGroupApModel.class::cast)
        .matches(m -> m.getExternalAntenna24().getDbi().equals(Int32Value.of(10)))
        .matches(m -> m.getExternalAntenna24().getEnabled().equals(BoolValue.of(true)));

    // Update empty request
    final VenueApGroupApModelExternalAntennaSettings request2 = objectMapper.readValue("""
          {
            "externalAntennaSettings": [{
              "model": "E510",
              "enable24G": true,
              "gain24G": 10
            }],
            "useVenueSettings": true
          }
          """, new TypeReference<>() {});
    final String requestId2 = randomTxId();
    sendRequest(
        CfgAction.UPDATE_VENUE_AP_GROUP_AP_MODEL_EXTERNAL_ANTENNA_SETTINGS,
        requestId2,
        apGroup.getVenue().getId(),
        apGroup.getId(),
        request2); // Empty request

    assertActivitySuccess(requestId2, ApiFlowNames.UPDATE_VENUE_AP_GROUP_AP_MODEL_EXTERNAL_ANTENNA_SETTINGS);

    final var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
    assertThat(record2).isNotNull();
    WifiConfigRequest ddccmRequest2 = record2.getPayload();
    assertThat(ddccmRequest2.getOperationsList()).isNotNull()
        .matches(req -> req.stream().anyMatch(Operation::hasApGroup))
        .isNotEmpty()
        .filteredOn(Operation::hasApGroup)
        .filteredOn(op -> !op.getApGroup().getApGroupApModelsList().isEmpty())
        .first()
        .extracting(Operation::getApGroup)
        .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApGroup::getApGroupApModelsList)
        .isNotNull().asList().first()
        .extracting(ApGroupApModel.class::cast)
        .matches(m -> !m.hasExternalAntenna24())
        .matches(m -> !m.hasExternalAntenna50());
  }

  void upgradeVenueToAbfWifi7Version(Venue venue) {
    ApVersion abf1Version = ApVersionTestFixture.recommendedApVersion("6.2.0.103.105", v -> {});
    ApVersion abf2Version = ApVersionTestFixture.recommendedApVersion("6.2.3.103.114", v -> {});
    ApVersion abf3Version = ApVersionTestFixture.recommendedApVersion("7.0.0.105.212", v -> {});
    repositoryUtil.createOrUpdate(abf1Version, venue.getTenant().getId(), randomTxId());
    repositoryUtil.createOrUpdate(abf2Version, venue.getTenant().getId(), randomTxId());
    repositoryUtil.createOrUpdate(abf3Version, venue.getTenant().getId(), randomTxId());
    VenueFirmwareVersion abf1Vfv = VenueFirmwareVersionTestFixture.randomVenueFirmwareVersion(venue, abf1Version,
        "eol-ap-2022-12");
    VenueFirmwareVersion abf2Vfv = VenueFirmwareVersionTestFixture.randomVenueFirmwareVersion(venue, abf2Version,
        "ABF2-3R");
    repositoryUtil.createOrUpdate(abf1Vfv, venue.getTenant().getId(), randomTxId());
    repositoryUtil.createOrUpdate(abf2Vfv, venue.getTenant().getId(), randomTxId());
    venue.setWifiFirmwareVersion(abf3Version);
    venue.setApPassword("1qaz@WSX");
    repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());
  }

  private void sendRequest(CfgAction cfgAction, String requestId, String venueId, String apGroupId, Object payload) {
    messageUtil.sendWifiCfgRequest(txCtxExtension.getTenantId(), requestId,
        cfgAction, randomName(),
        new RequestParams().addPathVariable("venueId", venueId).addPathVariable("apGroupId", apGroupId),
        payload);
  }

  private void assertActivitySuccess(String requestId, String stepId) {
    final String tenantId = txCtxExtension.getTenantId();
    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> {
          assertThat(msg.getStatus()).isEqualTo(CfgStatus.ConfigurationStatus.Status.OK);
          assertThat(msg.getStep()).isEqualTo(stepId);
        })
        .extracting(CfgStatus.ConfigurationStatus::getEventDate).isNotNull();
  }
}
