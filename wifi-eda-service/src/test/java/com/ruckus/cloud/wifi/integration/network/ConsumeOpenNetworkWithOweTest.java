package com.ruckus.cloud.wifi.integration.network;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.list;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.options;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.randomString;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.templateString;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.networkApGroup;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.core.model.BaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RadioTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SchedulerTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.OpenNetworkGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.OpenWlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.QosMapSetOptions;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Deprecated(forRemoval = true)
@Slf4j
@WifiIntegrationTest
public class ConsumeOpenNetworkWithOweTest extends AbstractRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private RevisionService revisionService;

  @Test
  void createOpenNetworkWithOweTransitionThenDelete(Tenant tenant) {
    // Given
    String ssid = "aa1234567890123456789012345678";
    var networkRequest = getOweTransNetwork(randomId(), ssid);

    // When
    var requestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        requestId,
        CfgAction.ADD_NETWORK,
        randomName(),
        new RequestParams(),
        networkRequest);

    // Then
    OpenNetwork master = repositoryUtil.find(OpenNetwork.class, networkRequest.getId());
    OpenNetwork slave = repositoryUtil.find(OpenNetwork.class, master.getOwePairNetworkId());

    String masterId = master.getId();
    String slaveId = slave.getId();

    assertThat(master)
        .isNotNull()
        .matches(n -> n.getId().equals(networkRequest.getId()))
        .matches(n -> n.getName().equals(networkRequest.getName()))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .matches(n -> n.getOwePairNetworkId().equals(slave.getId()))
        .matches(OpenNetwork::getIsOweMaster)
        .extracting(OpenNetwork::getWlan)
        .isNotNull()
        .matches(wlan -> wlan.getWlanSecurity().name()
            .equals(OpenWlanSecurityEnum.OWETransition.name()));

    assertThat(slave)
        .isNotNull()
        .matches(n -> n.getName().equals(ssid + "-owe-tr"))
        .matches(n -> n.getType().equals(NetworkTypeEnum.OPEN))
        .matches(n -> n.getOwePairNetworkId().equals(master.getId()))
        .matches(n -> !n.getIsOweMaster())
        .extracting(OpenNetwork::getWlan)
        .isNotNull()
        .matches(wlan -> wlan.getSsid().equals(ssid + "-o"))
        .matches(wlan -> wlan.getWlanSecurity().name()
            .equals(OpenWlanSecurityEnum.OWETransition.name()));

    var deleteRequestId = randomTxId();
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        deleteRequestId,
        CfgAction.DELETE_NETWORK,
        randomName(),
        new RequestParams().addPathVariable("networkId", master.getId()),
        StringUtils.EMPTY);

    OpenNetwork masterAfterDelete = repositoryUtil.find(OpenNetwork.class, masterId);
    OpenNetwork slaveAfterMasterDelete = repositoryUtil.find(OpenNetwork.class, slaveId);

    assertThat(masterAfterDelete).isNull();
    assertThat(slaveAfterMasterDelete).isNull();
  }

  com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork getOweTransNetwork(String networkId,
      String ssid) {
    com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork network = new com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork();
    network.setId(networkId);
    network.setName("masterOpenNetwork");
    network.setDescription(StringUtils.EMPTY);
    network.setWlan(new com.ruckus.cloud.wifi.eda.viewmodel.OpenWlan());
    network.getWlan().setEnabled(true);
    network.getWlan().setSsid(ssid);
    network.getWlan().setVlanId((short) 1);
    network.getWlan().setMacRegistrationListId(randomId());
    network.getWlan().setMacAddressAuthentication(true);
    network.getWlan()
        .setWlanSecurity(com.ruckus.cloud.wifi.eda.viewmodel.OpenWlanSecurityEnum.OWETransition);
    network.getWlan().setAdvancedCustomization(
        new com.ruckus.cloud.wifi.eda.viewmodel.OpenWlanAdvancedCustomization());
    network.getWlan().getAdvancedCustomization().setWifiCallingEnabled(false);
    network.getWlan().getAdvancedCustomization().setWifiCallingIds(new ArrayList<>());
    network.getWlan().getAdvancedCustomization().setDnsProxyEnabled(true);
    QosMapSetOptions qos = new QosMapSetOptions();
    qos.setRules(new ArrayList<>());
    network.getWlan().getAdvancedCustomization().setQosMapSetOptions(qos);
    return network;
  }

  @Nested
  class GivenOpenNetworkPersistedInDb {

    private String networkId;

    @BeforeEach
    void givenOpenNetworkAndDefaultApGroupAndNetworkVenuePersistedInDb(Tenant tenant,
        Venue venue, @DefaultApGroup ApGroup apGroup, Ap ap,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork Network openNetwork,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.networkvenue.ScheduledNetworkVenue NetworkVenue networkVenue) {
      networkVenue.setApWlanId(1);
      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId());
      networkId = openNetwork.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Payload("oweTransitionRequest")
    private OpenNetworkGenerator oweTransitionRequest() {
      return Generators.openNetwork()
          .setId(templateString(networkId))
          .setName(serialName("oweTransitionRequest"))
          .setDescription(randomString(64))
          .setWlan(Generators.oweTransitionWlan());
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("oweTransitionRequest"))
    void testNormalOpenToOweTransitionSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("oweTransitionRequest") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      payload.setId(networkId);
      validateOweTransitionResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.UPDATE_NETWORK.key(), true);
    }
  }


  @Nested
  class GivenOweTransitionNetworkPersistedInDb {

    private String networkId;
    private String venueId;
    private String networkVenueId;
    private String slaveNetworkId;
    private String apGroupId;
    private String networkApGroupId;

    @BeforeEach
    void givenOweNetworkVenuesPersistedInDb(Tenant tenant) {
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);
      Network master = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .generate();
      Network slave = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network(
              com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class)
          .generate();

      master.setTenant(tenant);
      master.setName(masterSsid);
      master.getWlan().setNetwork(master);
      master.getWlan().setSsid(masterSsid);
      master.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setIsOweMaster(true);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) master).setOwePairNetworkId(
          slave.getId());
      master = repositoryUtil.createOrUpdate(master, master.getTenant().getId(), randomTxId());

      slave.setTenant(tenant);
      slave.setName(masterSsid + "-owe-tr");
      slave.getWlan().setNetwork(slave);
      slave.getWlan().setSsid(masterSsid + "-owe-tr");
      slave.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setOwePairNetworkId(
          master.getId());
      ((com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork) slave).setIsOweMaster(false);
      slave = repositoryUtil.createOrUpdate(slave, slave.getTenant().getId(), randomTxId());

      var venue = repositoryUtil.createOrUpdate(VenueTestFixture.randomVenue(tenant),
          tenant.getId(),
          randomTxId());
      var apGroup = repositoryUtil.createOrUpdate(
          ApGroupTestFixture.randomApGroup(venue, (e) -> e.setIsDefault(true)), tenant.getId(),
          randomTxId());
      venue.setApGroups(List.of(apGroup));

      var masterNetworkVenue = NetworkVenueTestFixture.randomNetworkVenue(master, venue);
      var slaveNetworkVenue = NetworkVenueTestFixture.randomNetworkVenue(slave, venue);
      masterNetworkVenue.setApWlanId(1);
      masterNetworkVenue.setOweTransWlanId(2);
      slaveNetworkVenue.setApWlanId(2);
      slaveNetworkVenue.setOweTransWlanId(1);
      repositoryUtil.createOrUpdate(masterNetworkVenue, tenant.getId(), randomTxId());
      repositoryUtil.createOrUpdate(slaveNetworkVenue, tenant.getId(), randomTxId());

      var masterNetworkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(masterNetworkVenue, apGroup);
      masterNetworkApGroup = repositoryUtil.createOrUpdate(masterNetworkApGroup, tenant.getId(), randomTxId());

      var slaveNetworkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(slaveNetworkVenue, apGroup);
      slaveNetworkApGroup = repositoryUtil.createOrUpdate(slaveNetworkApGroup, tenant.getId(), randomTxId());

      var masterNetworkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(masterNetworkApGroup);
      masterNetworkApGroupRadio = repositoryUtil.createOrUpdate(masterNetworkApGroupRadio, tenant.getId(), randomTxId());

      var slaveNetworkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(slaveNetworkApGroup);
      slaveNetworkApGroupRadio = repositoryUtil.createOrUpdate(slaveNetworkApGroupRadio, tenant.getId(), randomTxId());

      networkVenueId = masterNetworkVenue.getId();
      slaveNetworkId = slaveNetworkVenue.getId();
      networkId = master.getId();
      venueId = venue.getId();
      apGroupId = apGroup.getId();
      networkApGroupId = masterNetworkApGroup.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @ApiAction.RequestParams("networkVenueParams")
    private RequestParams networkVenueRequestParams() {
      return new RequestParams().addPathVariable("networkVenueId", networkVenueId);
    }

    @Payload("oweTransitionRequest")
    private OpenNetworkGenerator oweTransitionRequest() {
      return Generators.openNetwork()
          .setId(templateString(networkId))
          .setName(serialName("oweTransitionRequest"))
          .setDescription(randomString(64))
          .setWlan(Generators.oweTransitionWlan());
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("oweTransitionRequest"))
    void testOweTransitionToOweTransitionSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("oweTransitionRequest") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      payload.setId(networkId);
      validateOweTransitionResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.UPDATE_NETWORK.key(), false);
    }

    @Payload("normalOpenRequest")
    private OpenNetworkGenerator normalOpenRequest() {
      return Generators.openNetwork()
          .setId(templateString(networkId))
          .setName(serialName("normalOpenRequest"))
          .setDescription(randomString(64));
    }


    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("normalOpenRequest"))
    void testOweTransitionToNormalOpenSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("normalOpenRequest") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      payload.setId(networkId);
      validateNormalOpenResult(txCtx.getTenant(), txCtx.getTxId(), payload,
          CfgAction.UPDATE_NETWORK.key());
    }

    @Payload("oweTransactionBackToOweRequest")
    private OpenNetworkGenerator oweTransactionBackToOweRequest() {
      return Generators.openNetwork()
          .setId(templateString(networkId))
          .setName(serialName("oweTransactionBackToOweRequest"))
          .setDescription(randomString(64))
          .setOwePairNetworkId(randomString(28))
          .setIsOweMaster(alwaysTrue())
          .setWlan(Generators.oweWlan());
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("oweTransactionBackToOweRequest"))
    void testOweTransitionToOweOpenSuccessfully(TxCtx txCtx, CfgAction apiAction,
        @Payload("oweTransactionBackToOweRequest") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      payload.setId(networkId);
      validateNormalOpenResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.UPDATE_NETWORK.key());
    }

    @Payload("updateNetworkVenueRequest")
    private com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue updateNetworkVenueRequest() {
      final var generator = Generators.networkVenue()
        .setId(always(networkVenueId))
        .setNetworkId(always(networkId))
        .setVenueId(always(venueId))
        .setScheduler(Generators.networkVenueScheduler(SchedulerTypeEnum.CUSTOM))
        .setIsAllApGroups(alwaysFalse())
        .setApGroups(list(networkApGroup()
          .setId(always(networkApGroupId))
          .setRadioTypes(list(options(RadioTypeEnum._2_4_GHz, RadioTypeEnum._5_GHz), 2))
          .setApGroupId(always(apGroupId)),1));
      return generator.generate();
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK_VENUE, payload = @Payload("updateNetworkVenueRequest"),
        requestParams = @ApiAction.RequestParams("networkVenueParams"))
    void testUpdateNetworkVenue(TxCtx txCtx, CfgAction apiAction,
        @Payload("updateNetworkVenueRequest") com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload) {
      validateNetworkVenueResult(txCtx.getTenant(), txCtx.getTxId(), payload, CfgAction.UPDATE_NETWORK_VENUE.key());
    }

    @Payload("deleteNetworkVenuesRequest")
    private List<String> payload() {
      return List.of(networkVenueId);
    }

    @Test
    @ApiAction(value = CfgAction.DELETE_NETWORK_VENUES, payload = @Payload("deleteNetworkVenuesRequest"),
        requestParams = @ApiAction.RequestParams("networkVenueParams"))
    void testDeleteNetworkVenues() {
      var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
      assertNull(networkVenue);
      var slaveNetwork = repositoryUtil.find(NetworkVenue.class, slaveNetworkId);
      assertNull(slaveNetwork);
    }
  }


  private void validateOweTransitionResult(String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload, String apiAction,
      boolean isNetworkVenueUpdated) {

    if (isNetworkVenueUpdated) {
      final var revision = revisionService.changes(requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(apiAction));
      final var networkVenueIds = revision.getAll().stream()
          .filter(entity -> entity instanceof NetworkVenue)
          .map(BaseEntity::getId).toList();
      assertTrue(networkVenueIds.size() > 0);

      for (var networkVenueId : networkVenueIds) {
        final var networkVenue = repositoryUtil.find(NetworkVenue.class, networkVenueId);
        assertNotEquals(networkVenue.getOweTransWlanId(), networkVenue.getApWlanId());
        assertNotEquals(0, networkVenue.getApWlanId());
        assertNotNull(networkVenue.getScheduler());
      }
    }

    OpenNetwork master = repositoryUtil.find(OpenNetwork.class, payload.getId());
    OpenNetwork slave = repositoryUtil.find(OpenNetwork.class, master.getOwePairNetworkId());
    assertTrue(master.getIsOweMaster());
    assertFalse(slave.getIsOweMaster());

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(cmnCfgCollectorMessage.getPayload())
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty());

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);
    assertThat(ddccmCfgRequestMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ddccmCfgRequestMessage.getPayload())
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .satisfies(op -> assertSoftly(softly -> {
              softly.assertThat(payload).isNotNull();
              softly.assertThat(op)
                  .filteredOn(Operation::hasWlanVenue)
                  .describedAs("The count of WlanVenue operations should be 2")
                  .hasSize(2);
            })));
  }

  private void validateNormalOpenResult(String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload, String apiAction) {
    OpenNetwork openNetwork = repositoryUtil.find(OpenNetwork.class, payload.getId());
    assertNull(openNetwork.getOwePairNetworkId());
    assertFalse(openNetwork.getIsOweMaster());
    assertTrue(openNetwork.getWlan().getWlanSecurity() != WlanSecurityEnum.OWETransition);
  }

  private void validateNetworkVenueResult(String tenantId, String requestId,
      com.ruckus.cloud.wifi.eda.viewmodel.NetworkVenue payload, String apiAction) {

    final var revision = revisionService.changes(requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(apiAction));
    final var networkVenueIds = revision.getAll().stream()
        .filter(entity -> entity instanceof NetworkVenue)
        .map(BaseEntity::getId).toList();
    assertTrue(networkVenueIds.size() > 0);

    for (var id : networkVenueIds) {
      final var networkVenue = repositoryUtil.find(NetworkVenue.class, id);
      assertNotEquals(networkVenue.getOweTransWlanId(), networkVenue.getApWlanId());
      assertNotEquals(0, networkVenue.getApWlanId());
      assertNotNull(networkVenue.getScheduler());
      assertEquals(4, networkVenue.getNetworkApGroups().get(0).getNetworkApGroupRadios().size());
    }

    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(wifiCfgChangeMessage).isNotNull()
        .satisfies(msg -> assertThat(msg.getKey()).isEqualTo(tenantId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.API_ACTION, CfgAction.UPDATE_NETWORK_VENUE.key()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(wifiCfgChangeMessage.getPayload())
            .satisfies(msg -> assertSoftly(softly -> {
              softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
              softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
            }))
            .extracting(WifiConfigChange::getOperationList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.wifi.proto.Operation.class::cast)
            .satisfies(ops -> assertThat(ops)
                .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasNetworkVenue)
                .filteredOn(
                    op -> op.getAction() == com.ruckus.cloud.wifi.proto.Operation.Action.MODIFY)
                .as("The MODIFY NetworkVenue operation count should be 2")
                .hasSize(2)));
  }
}
