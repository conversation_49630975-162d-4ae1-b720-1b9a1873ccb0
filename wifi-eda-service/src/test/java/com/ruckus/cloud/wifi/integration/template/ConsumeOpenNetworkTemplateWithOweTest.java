package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_WIFI_NETWORK_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.NETWORK;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.service.ExtendedNetworkTemplateServiceCtrl;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.NetworkTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeOpenNetworkTemplateWithOweTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedNetworkTemplateServiceCtrl networkTemplateServiceCtrl;

  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;
  
  @SneakyThrows
  @Test
  public void testAddOpenNetworkTemplate(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // add OpenNetwork
    OpenNetwork openNetwork = NetworkTestFixture.randomOpenOweNetwork(tenant, r -> {});

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, map(openNetwork));
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, tenantId);

    OpenNetwork openNetworkAdded = (OpenNetwork) networkTemplateServiceCtrl.getNetworkTemplate(openNetwork.getId());
    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops",
        () -> assertEquals(2, viewOps.size()),
        // "OWE Master network"
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> openNetworkAdded.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> openNetworkAdded.getOwePairNetworkId().equals(o.getDocMap().get(EsConstants.Key.OWE_PAIR_NETWORK_ID).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())),
        // "OWE Slave network"
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> openNetworkAdded.getOwePairNetworkId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> openNetworkAdded.getId().equals(o.getDocMap().get(EsConstants.Key.OWE_PAIR_NETWORK_ID).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue()))
    );
  }

  @Test
  public void testUpdateOpenNetworkTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // add OpenNetwork
    OpenNetwork openOweMasterNetwork = NetworkTestFixture.randomOpenOweNetwork(tenant, r -> {
      r.setIsTemplate(true);
      r.setIsOweMaster(true);
    });
    OpenNetwork openOweSlaveNetwork = NetworkTestFixture.randomOpenOweNetwork(tenant, r -> {
      r.setIsTemplate(true);
      r.setIsOweMaster(false);
    });
    openOweMasterNetwork.setOwePairNetworkId(openOweSlaveNetwork.getId());
    openOweSlaveNetwork.setOwePairNetworkId(openOweMasterNetwork.getId());
    repositoryUtil.createOrUpdate(openOweMasterNetwork, tenantId, randomTxId());
    repositoryUtil.createOrUpdate(openOweSlaveNetwork, tenantId, randomTxId());

    //////////
    // update OpenNetwork
    openOweMasterNetwork.setName(openOweMasterNetwork.getName() + "-updated");
    RequestParams rps = new RequestParams().addPathVariable("networkTemplateId", openOweMasterNetwork.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.UPDATE_NETWORK_TEMPLATE, userName, rps, map(openOweMasterNetwork));
    assertActivityStatusSuccess(UPDATE_NETWORK_TEMPLATE, tenantId);

    OpenNetwork openNetworkUpdated = (OpenNetwork) networkTemplateServiceCtrl.getNetworkTemplate(openOweMasterNetwork.getId());

    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops",
        () -> assertEquals(2, viewOps.size()),
        // "OWE Master network"
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> openNetworkUpdated.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.MOD)
            .filter(o -> openNetworkUpdated.getOwePairNetworkId().equals(o.getDocMap().get(EsConstants.Key.OWE_PAIR_NETWORK_ID).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())),
        // "OWE Slave network"
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> openNetworkUpdated.getOwePairNetworkId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.MOD)
            .filter(o -> openNetworkUpdated.getId().equals(o.getDocMap().get(EsConstants.Key.OWE_PAIR_NETWORK_ID).getStringValue()))
            .anyMatch(o -> o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue()))
    );
  }

  @Test
  public void testDeleteOpenNetworkTemplate(Tenant tenant) throws Exception {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    //////////
    // add OpenNetwork
    OpenNetwork openNetwork = NetworkTestFixture.randomOpenOweNetwork(tenant, r -> {});

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, map(openNetwork));
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, tenantId);
    var viewOpsAdd = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops", () -> assertEquals(2, viewOpsAdd.size()));

    OpenNetwork openOweMasterNetwork = (OpenNetwork) networkTemplateServiceCtrl.getNetworkTemplate(openNetwork.getId());

    //////////
    // delete OpenNetwork
    RequestParams rps = new RequestParams().addPathVariable("networkTemplateId", openOweMasterNetwork.getId());
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.DELETE_NETWORK_TEMPLATE, userName, rps, map(openOweMasterNetwork));
    assertActivityStatusSuccess(DELETE_NETWORK_TEMPLATE, tenantId);

    var viewOps = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel ops",
        () -> assertEquals(2, viewOps.size()),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> openOweMasterNetwork.getId().equals(o.getId()))
            .anyMatch(o -> o.getOpType() == OpType.DEL)),
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> openOweMasterNetwork.getOwePairNetworkId().equals(o.getId()))
            .anyMatch(o -> o.getOpType() == OpType.DEL))
    );
  }

  @Test
  public void addOpenNetworkWithOweByTemplate(Tenant mspTenant) throws Exception {
    var mspTenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenantId, randomTxId());

    //////////
    // add OpenNetwork
    OpenNetwork openNetwork = NetworkTestFixture.randomOpenOweNetwork(mspTenant, n -> { n.setIsTemplate(true); });

    sendWifiCfgRequest(mspTenantId, randomTxId(), CfgAction.ADD_NETWORK_TEMPLATE, userName, map(openNetwork));
    assertActivityStatusSuccess(ADD_NETWORK_TEMPLATE, mspTenantId);

    var viewOps = receiveViewmodelCollectorOperations(1, mspTenantId);
    log.warn("viewOps: {}", viewOps);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(viewOps, 2), // 2 networks, one is OWE transition slave network
        () -> assertTrue(viewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> openNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> openNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );

    OpenNetwork openNetworkAdded = repositoryUtil.find(OpenNetwork.class, openNetwork.getId(), mspTenantId);
    assertTrue(openNetworkAdded.getIsOweMaster());
    assertNotNull(openNetworkAdded.getOwePairNetworkId());

    // create ec network by msp network template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();

    String instanceId = randomId();

    ActivityExecutionPlan ecActivityPlan = buildActivityExecutionPlan(
        ADD_WIFI_NETWORK, WifiActivityPlan.Action.ADD,
        ExecutionPlanEntityTypes.NETWORK, instanceId);
    String ecActivityPlanBytes = executionPlanBuilder.toByteArrayBase64(ecActivityPlan);

    String mspRequestId = randomTxId();
    RequestParams rps = new RequestParams()
        .addPathVariable(TEMPLATE_ID, openNetworkAdded.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId);
    rps.addRequestParam(INSTANCE_ID, instanceId);
    rps.addRequestParam(ACTIVITY_PLAN_BYTES, ecActivityPlanBytes);
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId,
        CfgAction.ADD_WIFI_NETWORK_BY_TEMPLATE, ADD_WIFI_NETWORK_BY_TEMPLATE, userName,
        rps, instanceCreateRequest);

    assertActivityPlan(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK, ecTenantId);
    assertActivityStatusSuccess(ADD_WIFI_NETWORK_BY_TEMPLATE, mspTenantId);

    OpenNetwork ecOpenNetwork = repositoryUtil.find(OpenNetwork.class, instanceId, ecTenantId);
    assertEquals(instanceId, ecOpenNetwork.getId());
    assertEquals(openNetworkAdded.getId(), ecOpenNetwork.getTemplateId());
    assertEquals(openNetworkAdded.getUpdatedDate().getTime(), ecOpenNetwork.getTemplateVersion());
    assertTrue(ecOpenNetwork.getIsOweMaster());
    assertNotNull(ecOpenNetwork.getOwePairNetworkId());

    var ecViewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert viewmodel ops",
        () -> assertViewmodelOps(ecViewOps, 2),
        () -> assertTrue(ecViewOps.stream()
            .filter(o -> NETWORK.equals(o.getIndex()))
            .filter(o -> ecOpenNetwork.getId().equals(o.getId()))
            .filter(o -> o.getOpType() == OpType.ADD)
            .filter(o -> ecOpenNetwork.getName()
                .equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .findAny().isPresent())
    );
  }

  private void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }
}
