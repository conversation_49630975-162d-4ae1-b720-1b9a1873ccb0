package com.ruckus.cloud.wifi.integration.ap;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.InstanceOfAssertFactories.list;
import static org.assertj.core.api.SoftAssertions.assertSoftly;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.cloud.entitlement.Action;
import com.ruckus.cloud.entitlement.ProtoDeviceType;
import com.ruckus.cloud.entitlement.ProtoRequestSource;
import com.ruckus.cloud.entitlement.operation.eda.EdaOperation;
import com.ruckus.cloud.entitlement.operation.eda.EntOperationsEDARequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.core.error.SimpleError;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ScanMethodEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.ApCreationV1;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.service.core.api.rest.ErrorResponse;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.DefaultApGroup;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupRadioTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkApGroupTestFixture;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeAddApV1RequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  class whenConsumeAddApRequest {

    @Test
    void thenSaveAp(@DefaultApGroup ApGroup apGroup) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var serialNumber = randomSerialNumber();
      final var apRequest = new ApCreationV1();
      apRequest.setSerialNumber(serialNumber);
      apRequest.setName(randomName());

      final RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", apGroup.getVenue().getId());

      messageUtil.sendWifiCfgRequest(apGroup.getTenant().getId(), requestId, CfgAction.ADD_AP_V1,
          userName, requestParams, apRequest);

      validateResult(apGroup.getTenant().getId(), requestId, apGroup.getVenue().getId(), apRequest, apGroup);
    }

    @Test
    void thenSaveApWithNetworkApGroup(@DefaultApGroup ApGroup apGroup, NetworkVenue networkVenue) {
      final var requestId = randomTxId();
      final var userName = randomName();
      final var serialNumber = randomSerialNumber();

      NetworkApGroup networkApGroup = NetworkApGroupTestFixture.randomNetworkApGroup(networkVenue,
          apGroup);
      NetworkApGroupRadio networkApGroupRadio = NetworkApGroupRadioTestFixture.randomNetworkApGroupRadio(
          networkApGroup);
      repositoryUtil.createOrUpdate(apGroup, apGroup.getTenant().getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkApGroup, apGroup.getTenant().getId(), randomTxId());
      repositoryUtil.createOrUpdate(networkApGroupRadio, apGroup.getTenant().getId(), randomTxId());

      final var apRequest = new ApCreationV1();
      apRequest.setSerialNumber(serialNumber);
      apRequest.setName(randomName());

      final RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", apGroup.getVenue().getId());

      messageUtil.sendWifiCfgRequest(apGroup.getTenant().getId(), requestId, CfgAction.ADD_AP_V1,
          userName, requestParams, apRequest);

      validateResult(apGroup.getTenant().getId(), requestId, apGroup.getVenue().getId(), apRequest, apGroup);
    }

    @Nested
    class givenVenueIsNotExists {

      @Test
      void thenSaveAp(Tenant tenant) {
        final var requestId = randomTxId();
        final var venueId = randomId();
        final var userName = randomName();
        final var apRequest = new ApCreationV1();
        apRequest.setSerialNumber(randomSerialNumber());
        apRequest.setName(randomName());

        final RequestParams requestParams = new RequestParams();
        requestParams.addPathVariable("venueId", venueId);

        messageUtil.sendWifiCfgRequest(tenant.getId(), requestId, CfgAction.ADD_AP_V1, userName,
            requestParams, apRequest);

        validateResult(tenant.getId(), requestId, venueId, apRequest, null);
      }

      @Nested
      class givenApExistsAlready {

        @Test
        void thenApShouldNotBeCreated(Tenant tenant, Ap ap) {
          final var requestId = randomTxId();
          final var tenantId = tenant.getId();
          final var venueId = randomId();
          final var userName = randomName();
          final var apRequest = new ApCreationV1();
          apRequest.setSerialNumber(ap.getId());
          apRequest.setName(randomName());

          final RequestParams requestParams = new RequestParams();
          requestParams.addPathVariable("venueId", venueId);

          assertThrows(Exception.class, () -> messageUtil.sendWifiCfgRequest(
              tenant.getId(), requestId, CfgAction.ADD_AP_V1, userName, requestParams, apRequest));

          final var venue = repositoryUtil.find(Venue.class, venueId);
          assertThat(venue).isNull();

          final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
              .getValue(tenantId, requestId);

          assertThat(activityCfgChangeRespMessage).isNotNull()
              .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
              .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
              .extracting(KafkaProtoMessage::getPayload).isNotNull();

          assertThatNoException().isThrownBy(() ->
              assertThat(activityCfgChangeRespMessage.getPayload())
                  .satisfies(msg -> assertSoftly(softly -> {
                    softly.assertThat(msg.getStatus()).isEqualTo(Status.FAIL);
                    softly.assertThat(msg.getStep()).isEqualTo(CfgExtendedAction.Constants.ADD_AP);
                    softly.assertThat(msg.getEventDate()).isNotNull();
                    softly.assertThatCode(() -> assertThat(
                        new ObjectMapper().readValue(msg.getError(), SimpleError.class))
                        .satisfies(error -> {
                          assertThat(error.getCode()).isEqualTo(Errors.WIFI_10130.code());
                          assertThat(error.getMessage()).isEqualTo(Errors.WIFI_10130.message());
                        })).doesNotThrowAnyException();
                  })));
        }
      }
    }
  }

  @Nested
  class givenRevisionExists {

    @Test
    void thenSendMessageAsWell(@DefaultApGroup ApGroup apGroup)
        {
      final var userName = randomName();
      final var apRequest = new ApCreationV1();

      final RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", apGroup.getVenue().getId());

      for (int i = 0; i < 5; i++) {
        final var requestId = randomTxId();
        final var serialNumber = randomSerialNumber();
        apRequest.setSerialNumber(serialNumber);
        apRequest.setName(randomName());
        messageUtil.sendWifiCfgRequest(
            apGroup.getTenant().getId(),
            requestId,
            CfgAction.ADD_AP_V1,
            userName,
            requestParams,
            apRequest);

        validateResult(apGroup.getTenant().getId(), requestId, apGroup.getVenue().getId(), apRequest, apGroup);
      }
    }
  }

  @Nested
  class givenNullApName {

    @Test
    void thenFailedInDdccmBuilder_andShouldReceiveOnlyActivityFail(@DefaultApGroup ApGroup apGroup)
        {
      final var tenantId = apGroup.getTenant().getId();
      final var requestId = randomTxId();
      final var userName = randomName();
      final var serialNumber = randomSerialNumber();
      final var apRequest = new ApCreationV1();
      apRequest.setSerialNumber(serialNumber);
      // No given AP name

      final RequestParams requestParams = new RequestParams();
      requestParams.addPathVariable("venueId", apGroup.getVenue().getId());

      assertThrows(Exception.class, () ->
          messageUtil.sendWifiCfgRequest(
              apGroup.getTenant().getId(),
              requestId,
              CfgAction.ADD_AP_V1,
              userName,
              requestParams,
              apRequest));

      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(tenantId);

      final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
          .getValue(tenantId, requestId);

      assertThat(activityCfgChangeRespMessage).isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      assertThatNoException().isThrownBy(() ->
          assertThat(activityCfgChangeRespMessage.getPayload())
              .matches(msg -> msg.getStatus().equals(Status.FAIL))
              .matches(msg -> msg.getStep().equals(CfgExtendedAction.Constants.ADD_AP))
              .extracting(ConfigurationStatus::getEventDate).isNotNull());
    }
  }

  void validateResult(String tenantId, String requestId, String venueId, ApCreationV1 apRequest, ApGroup targetApGroup) {
    assertThat(repositoryUtil.find(Ap.class, apRequest.getSerialNumber()))
        .isNotNull()
        .satisfies(ap -> assertSoftly(softly -> {
          softly.assertThat(ap.getId()).isEqualTo(apRequest.getSerialNumber());
          softly.assertThat(ap.getName()).isEqualTo(apRequest.getName());
          softly.assertThat(ap.getRadioCustomization().getApRadioParams6G().getEnableAfc())
              .isFalse();
          softly.assertThat(ap.getRadioCustomization().getApRadioParams24G().getMethod())
              .isEqualTo(ScanMethodEnum.CHANNELFLY);
          softly.assertThat(ap.getRadioCustomization().getApRadioParams50G().getMethod())
              .isEqualTo(ScanMethodEnum.CHANNELFLY);
          softly.assertThat(
              ap.getRadioCustomization().getApRadioParamsDual5G().getRadioParamsLower5G()
                  .getMethod()).isEqualTo(ScanMethodEnum.CHANNELFLY);
          softly.assertThat(
              ap.getRadioCustomization().getApRadioParamsDual5G().getRadioParamsUpper5G()
                  .getMethod()).isEqualTo(ScanMethodEnum.CHANNELFLY);
          softly.assertThat(ap.getApGroup()).isNotNull()
              .satisfies(apGroup -> {
                if (targetApGroup == null) {
                  assertThat(apGroup.getId()).isNotEmpty();
                  assertThat(apGroup.getIsDefault()).isTrue();
                  assertThat(apGroup.getVenue()).isNotNull()
                      .extracting(Venue::getId).isEqualTo(venueId);
                } else {
                  assertThat(apGroup.getId()).isEqualTo(targetApGroup.getId());
                  assertThat(apGroup.getIsDefault()).isEqualTo(targetApGroup.getIsDefault());
                  assertThat(apGroup.getVenue()).isNotNull()
                      .extracting(Venue::getId).isEqualTo(targetApGroup.getVenue().getId());
                }
              });
        }));

    assertThat(messageCaptors.getDeviceRegistrationPolicyMessageCaptor()
        .getValue(tenantId, requestId)).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> assertSoftly(softly -> {
          softly.assertThat(msg.getId()).isEqualTo(apRequest.getSerialNumber());
          softly.assertThat(msg.getSerial()).isEqualTo(apRequest.getSerialNumber());
          softly.assertThat(msg.getVenueId()).isEqualTo(venueId);
          softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
          if (targetApGroup == null) {
            softly.assertThat(msg.getGroupId()).isNotEmpty();
          } else {
            softly.assertThat(msg.getGroupId()).isEqualTo(targetApGroup.getId());
          }
        }));

    assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId)).isNotNull()
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> assertSoftly(softly -> {
          softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
          softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
        }))
        .extracting(ViewmodelCollector::getOperationsList, list(Operations.class))
        .isNotEmpty()
        .satisfies(ops -> {
          assertThat(ops)
              .filteredOn(op -> Index.DEVICE.equals(op.getIndex()))
              .isNotEmpty().singleElement()
              .satisfies(op -> assertSoftly(softly -> {
                softly.assertThat(op.getOpType()).isEqualTo(OpType.ADD);
                softly.assertThat(op.getId()).isEqualTo(apRequest.getSerialNumber());
              }))
              .extracting(Operations::getDocMap)
              .satisfies(docMap -> assertSoftly(softly -> {
                softly.assertThat(docMap.get(Key.FLOORPLAN_ID)).isNull();
                if (targetApGroup == null) {
                  softly.assertThat(docMap.get(Key.VENUE_NAME)).isNotNull();
                } else {
                  softly.assertThat(docMap.get(Key.VENUE_NAME))
                      .isEqualTo(ValueUtils.stringValue(targetApGroup.getVenue().getName()));
                }
              }));
        });

    assertThat(messageCaptors.getEntitlementDeviceOperationMessageCaptor()
        .getValue(tenantId, requestId)).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.WIFI_REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_TENANT_ID, tenantId))
        .satisfies(assertHeader(WifiCommonHeader.WIFI_DEVICE_TYPE, ProtoDeviceType.WIFI.name()))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> assertSoftly(softly -> {
          softly.assertThat(msg.getTenantId()).isEqualTo(tenantId);
          softly.assertThat(msg.getRequestId()).isEqualTo(requestId);
          softly.assertThat(msg.getRequestSource()).isEqualTo(ProtoRequestSource.WIFI_SERVICE);
        }))
        .extracting(EntOperationsEDARequest::getOperationList, list(EdaOperation.class))
        .isNotEmpty().singleElement()
        .satisfies(op -> assertSoftly(softly -> {
          softly.assertThat(op.getAction()).isEqualTo(Action.CONSUME);
          softly.assertThat(op.getDevice().getDeviceType()).isEqualTo(ProtoDeviceType.WIFI);
          softly.assertThat(op.getDevice().getSerialNumber())
              .isEqualTo(apRequest.getSerialNumber());
        }));

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValue(tenantId, requestId)).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> assertSoftly(softly -> {
          softly.assertThat(msg.getStatus()).isEqualTo(Status.OK);
          softly.assertThat(msg.getStep()).isEqualTo(CfgExtendedAction.Constants.ADD_AP);
          softly.assertThat(msg.getEventDate()).isNotNull();
        }));

    messageCaptors.assertThat(
        kafkaTopicProvider.getImpactedDevices(),
        kafkaTopicProvider.getFranzServiceDrivenDeviceStatus()
    ).doesNotSendByTenant(tenantId);
  }
}
