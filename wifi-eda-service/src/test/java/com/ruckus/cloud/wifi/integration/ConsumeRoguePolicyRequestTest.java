package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_ROGUE_POLICY_ON_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ROGUE_POLICY;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DEACTIVATE_ROGUE_POLICY_ON_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_ROGUE_POLICY;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_ROGUE_POLICY;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.ROGUE_AP_POLICY_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.service.impl.RoguePolicyServiceCtrlImpl.DEFAULT_ROGUE_AP_POLICY_NAME;
import static com.ruckus.cloud.wifi.service.impl.RoguePolicyServiceCtrlImpl.NONPERSISTED_DEFAULT_POLICY_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture.extractVenues;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.RogueApPolicy;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.RogueClassificationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRogueAp;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.RogueNetworkProtectionStrategyEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.repository.RogueClassificationPolicyRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.RogueApPolicyTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeRoguePolicyRequestTest extends AbstractRequestTest {
  @Autowired private RogueClassificationPolicyRepository rogueClassificationPolicyRepository;

  @Autowired private VenueRepository venueRepository;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void addRoguePolicy(Tenant tenant) {
    var tenantId = tenant.getId();
    var userName = txCtxExtension.getUserName();
    var myPolicyId = randomId();

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    p.setId(myPolicyId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p));

    RogueClassificationPolicy policy = getRoguePolicy(myPolicyId);
    assertNotNull(policy);
    assertEquals(myPolicyId, policy.getId());
    assertEquals("Rogue Policy", policy.getName());
    assertEquals(2, policy.getRules().size());

    assertRoguePolicyNotSent(tenantId);

    // 1 RoguePolicy
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll(
        "assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 1),
        () ->
            assertViewmodelCollectorRogueApPolicy(
                viewmodelOperations, OpType.ADD, myPolicyId, "Rogue Policy", 0.0));

    assertActivityStatusSuccess(ADD_ROGUE_POLICY, tenantId);
  }

  @Test
  public void addDefaultRogueClassificationPolicy(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    // test a non-pure default rogue policy
    RogueClassificationPolicy p =
        RogueApPolicyTestFixture.rogueApPolicy(DEFAULT_ROGUE_AP_POLICY_NAME);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p));

    assertEquals(0, getRogueApPolicies().size());

    assertActivityStatusFail(ADD_ROGUE_POLICY, Errors.WIFI_10192, tenantId);
  }

  @Test
  public void updateRogueClassificationPolicy(Tenant tenant) {
    var tenantId = tenant.getId();
    var userName = txCtxExtension.getUserName();
    var policyId1 = randomId();

    Venue v1 = createVenue(tenant, "v1");
    String venueId1 = v1.getId();

    // set p1 to v1
    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    p.setId(policyId1);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p));

    var requestParams =
        new RequestParams()
            .addPathVariable("venueId", v1.getId())
            .addPathVariable("roguePolicyId", policyId1);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);
    var rogueOperations = receiveRoguePolicyOperations(1, tenantId);
    assertAll(
        "assert rogue publisher",
        () -> assertRoguePolicyPublisher(rogueOperations, 1),
        () -> assertRoguePolicyPublisher(rogueOperations, venueId1, policyId1));
    assertRoguePolicyNotSent(tenantId); // make sure no more

    List<RogueClassificationPolicy> policies =
        rogueClassificationPolicyRepository.findByTenantId(tenantId);
    assertEquals(2, policies.size());
    RogueClassificationPolicy rogueClassificationPolicy =
        policies.stream()
            .filter(o -> !DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
            .findAny()
            .orElseThrow();
    String roguePolicyId = rogueClassificationPolicy.getId();
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());
    assertEquals(venueId1, extractVenues(rogueClassificationPolicy).get(0).getId());

    // update policy p1
    p.setId(roguePolicyId);
    // remove a rule
    p.setName("Rogue Policy New");
    p.getRules().remove(0);

    RequestParams rps = new RequestParams().addPathVariable("roguePolicyId", roguePolicyId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_ROGUE_POLICY, userName, rps, map(p));

    List<RogueClassificationPolicy> policies2 =
        rogueClassificationPolicyRepository.findByTenantId(tenantId);
    assertEquals(2, policies2.size());
    RogueClassificationPolicy rogueClassificationPolicy2 =
        policies2.stream().filter(o -> roguePolicyId.equals(o.getId())).findAny().orElseThrow();
    assertEquals("Rogue Policy New", rogueClassificationPolicy2.getName());
    assertEquals(1, rogueClassificationPolicy2.getRules().size());
    assertEquals(venueId1, extractVenues(rogueClassificationPolicy2).get(0).getId());

    // 1 RoguePolicy, 1 VenueRogueAp
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll(
        "assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 2),
        () -> assertViewmodelCollectorRogueApPolicy(
            viewmodelOperations, OpType.MOD, roguePolicyId, "Rogue Policy New", 1.0),
        () -> assertViewmodelCollectorVenueRogueAp(
            viewmodelOperations, OpType.MOD, v1.getId(), roguePolicyId, "Rogue Policy New"));

    assertActivityStatusSuccess(UPDATE_ROGUE_POLICY, tenantId);
  }

  private void assertRoguePolicyPublisher(List<RogueApPolicy> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertRoguePolicyPublisher(
      List<RogueApPolicy> operations, String venueId, String roguePolicyId) {
    RogueApPolicy rogueApPolicy =
        operations.stream().filter(o -> venueId.equals(o.getVenueId())).findAny().orElseThrow();
    assertEquals(roguePolicyId, rogueApPolicy.getId());
  }

  @Test
  public void deleteRogueClassificationPolicy(Tenant tenant) {
    var tenantId = tenant.getId();
    var userName = txCtxExtension.getUserName();
    var policyId = randomId();

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    p.setId(policyId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p));

    List<RogueClassificationPolicy> policies =
        rogueClassificationPolicyRepository.findByTenantId(tenantId);
    assertEquals(1, policies.size());
    RogueClassificationPolicy rogueClassificationPolicy = policies.get(0);
    String roguePolicyId = rogueClassificationPolicy.getId();
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());

    // delete

    String requestId2 = randomTxId();
    RequestParams rps = new RequestParams().addPathVariable("roguePolicyId", roguePolicyId);
    sendWifiCfgRequest(tenantId, requestId2, CfgAction.DELETE_ROGUE_POLICY, userName, rps, "");

    List<RogueClassificationPolicy> policies2 =
        rogueClassificationPolicyRepository.findByTenantId(tenantId);
    assertEquals(0, policies2.size());

    assertRoguePolicyNotSent(tenantId);

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll(
        "assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 1),
        () -> assertViewmodelCollectorRogueApPolicy(
                viewmodelOperations, OpType.DEL, roguePolicyId, null, null));

    assertActivityStatusSuccess(DELETE_ROGUE_POLICY, tenantId);
  }

  @Test
  public void activateRoguePolicyOnVenue(Tenant tenant) {
    var tenantId = tenant.getId();
    var userName = txCtxExtension.getUserName();
    var myPolicyId = randomId();

    Venue v = createVenue(tenant, "v");
    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    p.setId(myPolicyId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p));

    // 1 RoguePolicy
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll(
        "assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 1),
        () ->
            assertViewmodelCollectorRogueApPolicy(
                viewmodelOperations, OpType.ADD, myPolicyId, "Rogue Policy", 0.0));
    assertActivityStatusSuccess(ADD_ROGUE_POLICY, tenantId);

    var requestParams =
        new RequestParams()
            .addPathVariable("venueId", v.getId())
            .addPathVariable("roguePolicyId", myPolicyId);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);

    RogueClassificationPolicy policy = getRoguePolicy(myPolicyId);
    assertNotNull(policy);
    assertEquals(myPolicyId, policy.getId());
    assertEquals("Rogue Policy", policy.getName());
    assertEquals(2, policy.getRules().size());

    List<RogueClassificationPolicy> policies =
        rogueClassificationPolicyRepository.findByTenantId(tenantId);
    RogueClassificationPolicy defaultRogueClassificationPolicy =
        policies.stream()
            .filter(o -> DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
            .findAny()
            .orElseThrow();
    assertNotNull(defaultRogueClassificationPolicy);
    assertNotEquals(NONPERSISTED_DEFAULT_POLICY_ID, defaultRogueClassificationPolicy.getId());

    VenueRogueAp venueRogueAp = getVenueRoguePolicySettings(v.getId());
    assertTrue(venueRogueAp.getEnabled());
    assertEquals((short) 0, venueRogueAp.getReportThreshold());
    assertFalse(venueRogueAp.getNetworkProtection());
    assertEquals(RogueNetworkProtectionStrategyEnum.CONSERVATIVE, venueRogueAp.getNetworkProtectionStrategy());

    var rogueOperations = receiveRoguePolicyOperations(1, tenantId);
    assertAll(
        "assert rogue publisher",
        () -> assertRoguePolicyPublisher(rogueOperations, 1),
        () -> assertRoguePolicyPublisher(rogueOperations, v.getId(), policy.getId()));
    assertRoguePolicyNotSent(tenantId); // make sure no more

    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll(
        "assert ddccm publisher",
        () -> assertDdccmPublisher(ddccmOperations, 1),
        () -> assertDdccmPublisherVenue(ddccmOperations, v.getId(), true, 0));

    // 2 RoguePolicy, 1 Venue
    var viewmodelOperations2 = receiveViewmodelCollectorOperations(1, tenantId);
    log.info("{}", viewmodelOperations2);
    assertAll(
        "assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations2, 3),
        () ->
            assertViewmodelCollectorRogueApPolicy(
                viewmodelOperations2,
                OpType.ADD,
                defaultRogueClassificationPolicy.getId(),
                DEFAULT_ROGUE_AP_POLICY_NAME,
                0.0),
        () ->
            assertViewmodelCollectorRogueApPolicy(
                viewmodelOperations2, OpType.MOD, myPolicyId, "Rogue Policy", 1.0),
        () ->
            assertViewmodelCollectorVenueRogueAp(
                viewmodelOperations2, OpType.MOD, v.getId(), policy.getId(), "Rogue Policy"));

    assertActivityStatusSuccess(ACTIVATE_ROGUE_POLICY_ON_VENUE, tenantId);
  }

  @Test
  public void deactivateRoguePolicyOnVenue(Tenant tenant) {
    var tenantId = tenant.getId();
    var userName = txCtxExtension.getUserName();
    var policyId = randomId();

    Venue v1 = createVenue(tenant, "v1");
    Venue v2 = createVenue(tenant, "v2");
    String venueId1 = v1.getId();
    String venueId2 = v2.getId();

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    p.setId(policyId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p));

    var requestParams =
        new RequestParams()
            .addPathVariable("venueId", v1.getId())
            .addPathVariable("roguePolicyId", policyId);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);
    requestParams =
        new RequestParams()
            .addPathVariable("venueId", v2.getId())
            .addPathVariable("roguePolicyId", policyId);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);

    List<RogueClassificationPolicy> policies =
        rogueClassificationPolicyRepository.findByTenantId(tenantId);
    assertEquals(2, policies.size());
    RogueClassificationPolicy rogueClassificationPolicy =
        policies.stream()
            .filter(o -> !DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
            .findAny()
            .orElseThrow();
    String roguePolicyId = rogueClassificationPolicy.getId();
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());
    assertEquals(2, extractVenues(rogueClassificationPolicy).size());

    // update

    requestParams =
        new RequestParams()
            .addPathVariable("venueId", v1.getId())
            .addPathVariable("roguePolicyId", policyId);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.DEACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);

    List<RogueClassificationPolicy> policies2 =
        rogueClassificationPolicyRepository.findByTenantId(tenantId);
    assertEquals(2, policies2.size());
    RogueClassificationPolicy rogueClassificationPolicy2 =
        policies2.stream().filter(o -> roguePolicyId.equals(o.getId())).findAny().orElseThrow();
    assertEquals("Rogue Policy", rogueClassificationPolicy2.getName());
    assertEquals(2, rogueClassificationPolicy2.getRules().size());
    assertEquals(1, extractVenues(rogueClassificationPolicy2).size());

    var rogueOperations = receiveRoguePolicyOperations(1, tenantId);
    assertAll(
        "assert rogue publisher",
        () -> assertRoguePolicyPublisher(rogueOperations, 1),
        () ->
            assertRoguePolicyPublisher(
                rogueOperations, venueId1, rogueClassificationPolicy2.getId()));
    assertRoguePolicyNotSent(tenantId); // make sure no more

    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll(
        "assert ddccm publisher",
        () -> assertDdccmPublisher(ddccmOperations, 1),
        () -> assertDdccmPublisherVenue(ddccmOperations, venueId1, false, 0));

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll(
        "assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 2),
        () ->
            assertViewmodelCollectorRogueApPolicy(
                viewmodelOperations, OpType.MOD, policyId, "Rogue Policy", 1.0),
        () ->
            assertViewmodelCollectorVenueRogueApDeactivate(
                viewmodelOperations, OpType.MOD, venueId1));

    assertActivityStatusSuccess(DEACTIVATE_ROGUE_POLICY_ON_VENUE, tenantId);
  }

  @Test
  public void updateVenueRoguePolicySettings(Tenant tenant) {
    clearMessageBeforeEachEdaOperation = false;

    var tenantId = tenant.getId();
    var userName = txCtxExtension.getUserName();
    var profileId = randomId();

    Venue v1 = createVenue(tenant, "v1");
    String venueId1 = v1.getId();

    RogueClassificationPolicy p = RogueApPolicyTestFixture.rogueApPolicy("Rogue Policy");
    p.setId(profileId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p));
    assertActivityStatusSuccess(ADD_ROGUE_POLICY, tenantId);
    var requestParams =
        new RequestParams()
            .addPathVariable("venueId", venueId1)
            .addPathVariable("roguePolicyId", profileId);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);

    List<RogueClassificationPolicy> policies =
        rogueClassificationPolicyRepository.findByTenantId(tenantId);
    assertEquals(2, policies.size());
    RogueClassificationPolicy rogueClassificationPolicy =
        policies.stream()
            .filter(o -> !DEFAULT_ROGUE_AP_POLICY_NAME.equals(o.getName()))
            .findAny()
            .orElseThrow();
    assertEquals("Rogue Policy", rogueClassificationPolicy.getName());
    assertEquals(2, rogueClassificationPolicy.getRules().size());
    assertEquals(1, extractVenues(rogueClassificationPolicy).size());

    VenueRogueAp venueRogueAp = new VenueRogueAp();
    venueRogueAp.setReportThreshold((short) 10);
    requestParams = new RequestParams().addPathVariable("venueId", venueId1);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.UPDATE_VENUE_ROGUE_POLICY_SETTINGS,
        userName,
        requestParams,
        venueRogueAp);

    var venueOptional = venueRepository.findByIdAndTenantId(venueId1, tenantId);
    if (venueOptional.isPresent()) {
      var venue = venueOptional.get();
      assertEquals((short) 10, venue.getRogueAp().getReportThreshold());
    }
  }

  @Test
  public void validateRogueClassificationPolicyBindMaxCounts(Tenant tenant) {
    var tenantId = tenant.getId();
    var userName = txCtxExtension.getUserName();

    /** default: rogue-ap-policy-profile: tenant-max-count: 3 rule-max-count: 2 bind-max-count: 2 */
    Venue v1 = createVenue(tenant, "v1");
    Venue v2 = createVenue(tenant, "v2");
    Venue v3 = createVenue(tenant, "v3");

    // test bind-max-count
    var p2Id = randomId();
    RogueClassificationPolicy p2 = RogueApPolicyTestFixture.rogueApPolicy("p2", 1);
    p2.setId(p2Id);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p2));
    var requestParams =
        new RequestParams()
            .addPathVariable("venueId", v1.getId())
            .addPathVariable("roguePolicyId", p2Id);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);
    requestParams =
        new RequestParams()
            .addPathVariable("venueId", v2.getId())
            .addPathVariable("roguePolicyId", p2Id);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);
    requestParams =
        new RequestParams()
            .addPathVariable("venueId", v3.getId())
            .addPathVariable("roguePolicyId", p2Id);
    sendWifiCfgRequest(
        tenantId,
        randomTxId(),
        CfgAction.ACTIVATE_ROGUE_POLICY_ON_VENUE,
        userName,
        requestParams,
        null);

    List<RogueClassificationPolicy> policies =
        rogueClassificationPolicyRepository.findByTenantId(tenantId);
    RogueClassificationPolicy rogueClassificationPolicy =
        policies.stream().filter(o -> p2Id.equals(o.getId())).findAny().orElseThrow();
    assertEquals(2, extractVenues(rogueClassificationPolicy).size());
    assertActivityStatusFail(ACTIVATE_ROGUE_POLICY_ON_VENUE, Errors.WIFI_10195, tenantId);
  }

  @Test
  public void validateRogueClassificationPolicyRuleMaxCounts(Tenant tenant) {
    var tenantId = tenant.getId();
    var userName = txCtxExtension.getUserName();

    /** default: rogue-ap-policy-profile: tenant-max-count: 3 rule-max-count: 2 bind-max-count: 2 */
    // test rule-max-count
    RogueClassificationPolicy p1 = RogueApPolicyTestFixture.rogueApPolicy("p1", 3);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p1));
    assertEquals(0, rogueClassificationPolicyRepository.findByTenantId(tenantId).size());

    assertNoMessages(tenantId,
        kafkaTopicProvider.getRoguePolicy(),
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    assertActivityStatusFail(ADD_ROGUE_POLICY, Errors.WIFI_10194, tenantId);
  }

  @Test
  public void validateRogueClassificationPolicyTenantMaxCounts(Tenant tenant) {
    var tenantId = tenant.getId();
    var userName = txCtxExtension.getUserName();

    /** default: rogue-ap-policy-profile: tenant-max-count: 3 rule-max-count: 2 bind-max-count: 2 */
    // tenant-max-count
    RogueClassificationPolicy p3 = RogueApPolicyTestFixture.rogueApPolicy("p3", 2);
    RogueClassificationPolicy p4 = RogueApPolicyTestFixture.rogueApPolicy("p4", 1);
    RogueClassificationPolicy p5 = RogueApPolicyTestFixture.rogueApPolicy("p5", 1);
    RogueClassificationPolicy p6 = RogueApPolicyTestFixture.rogueApPolicy("p6", 1);

    // test tenant-max-count

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p3));
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p4));
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p5));
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_ROGUE_POLICY, userName, map(p6));

    assertEquals(3, rogueClassificationPolicyRepository.findByTenantId(tenantId).size());

    // make sure no more
    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getRoguePolicy());

    assertActivityStatusFail(ADD_ROGUE_POLICY, Errors.WIFI_10193, tenantId);
  }

  private static void assertViewmodelCollector(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private static void assertViewmodelCollectorRogueApPolicy(
      List<Operations> operations,
      OpType opType,
      String roguePolicyId,
      String roguePolicyName,
      Double scope) {
    assertTrue(
        operations.stream()
            .filter(o -> ROGUE_AP_POLICY_PROFILE_INDEX_NAME.equals(o.getIndex()))
            .filter(o -> roguePolicyId.equals(o.getId()))
            .filter(o -> o.getOpType() == opType)
            .filter(o -> o.getOpType() == OpType.DEL || false == o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue())
            .filter(
                o ->
                    roguePolicyName == null
                        || roguePolicyName.equals(
                            o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
            .filter(
                o ->
                    scope == null
                        || o.getDocMap()
                                .get(EsConstants.Key.VENUE_IDS)
                                .getListValue()
                                .getValuesCount()
                            == scope)
            .findAny()
            .isPresent());
  }

  private static void assertViewmodelCollectorVenueRogueAp(
      List<Operations> operations, OpType opType, String venueId, String roguePolicyId, String roguePolicyName) {
    Operations venue =
        operations.stream().filter(o -> venueId.equals(o.getId())).findAny().orElseThrow();
    assertEquals(EsConstants.Index.VENUE, venue.getIndex());
    assertEquals(opType, venue.getOpType());
    assertEquals(
        roguePolicyId,
        venue
            .getDocMap()
            .get("rogueDetection")
            .getStructValue()
            .getFieldsOrThrow("policyId")
            .getStringValue());
    assertEquals(
        roguePolicyName,
        venue
            .getDocMap()
            .get("rogueDetection")
            .getStructValue()
            .getFieldsOrThrow("policyName")
            .getStringValue());
  }

  private static void assertViewmodelCollectorVenueRogueApDeactivate(
      List<Operations> operations, OpType opType, String venueId) {
    Operations venue =
        operations.stream().filter(o -> venueId.equals(o.getId())).findAny().orElseThrow();
    assertEquals(EsConstants.Index.VENUE, venue.getIndex());
    assertEquals(opType, venue.getOpType());
    assertEquals(
        StringUtils.EMPTY,
        venue
            .getDocMap()
            .get("rogueDetection")
            .getStructValue()
            .getFieldsOrThrow("policyId")
            .getStringValue());
  }

  private static void assertDdccmPublisher(List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private static void assertDdccmPublisherVenue(
      List<Operation> operations, String venueId, boolean rogueEnabled, int threshold) {
    assertTrue(
        operations.stream()
            .filter(Operation::hasVenue)
            .map(Operation::getVenue)
            .anyMatch(
                v ->
                    venueId.equals(v.getId())
                        && rogueEnabled == v.getRogueEnabled()
                        && threshold == v.getRogueApReportThreshold().getValue()));
  }
}
