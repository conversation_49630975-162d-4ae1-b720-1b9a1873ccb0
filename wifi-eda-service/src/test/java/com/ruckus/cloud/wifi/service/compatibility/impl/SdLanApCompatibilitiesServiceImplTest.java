package com.ruckus.cloud.wifi.service.compatibility.impl;

import static com.ruckus.cloud.wifi.requirement.ApFeatureRequirement.SD_LAN;
import static com.ruckus.cloud.wifi.requirement.ApFeatureRequirement.TUNNEL_PROFILE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.never;

import com.ruckus.cloud.compatibility.contract.model.DeviceAggregation;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileNetworkMapping;
import com.ruckus.cloud.wifi.eda.servicemodel.SdLanProfileRegularSetting;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.viewmodel.SdLanApCompatibilities;
import com.ruckus.cloud.wifi.repository.SdLanProfileRegularSettingRepository;
import com.ruckus.cloud.wifi.service.ScopeDataService;
import com.ruckus.cloud.wifi.service.compatibility.EdgeServiceApCompatibilitiesService.EdgeServiceVenuesNetworks;
import com.ruckus.cloud.wifi.service.core.rbac.Scope;
import com.ruckus.cloud.wifi.service.core.rbac.ScopeData;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.CommonTestFixture;
import com.ruckus.cloud.wifi.viewmodel.ApCompatibility;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModel;
import com.ruckus.cloud.wifi.viewmodel.ApRequirement;
import com.ruckus.cloud.wifi.viewmodel.IncompatibleWifiFeature;
import com.ruckus.cloud.wifi.viewmodel.VenueSdLanApCompatibility;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.redis.core.RedisTemplate;

@WifiUnitTest
public class SdLanApCompatibilitiesServiceImplTest {

  @RegisterExtension
  public final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @MockBean
  private SdLanProfileRegularSettingRepository sdLanProfileRegularSettingRepository;
  @MockBean(name = "rcgRedisTemplate")
  private RedisTemplate redisTemplate;
  @MockBean
  private ScopeDataService scopeDataService;
  @SpyBean
  private SdLanApCompatibilitiesServiceImpl unit;

  @Nested
  class WhenGetSdLanIdsVenueIdsMapByServiceIds {

    List<String> sdLanIds;
    List<String> venueIds;
    List<SdLanProfileRegularSetting> settings;
    HashMap<String, List<String>> expectedMap;

    @BeforeEach
    void init() {
      sdLanIds = Stream.generate(CommonTestFixture::randomId).limit(2).toList();
      venueIds = Stream.generate(CommonTestFixture::randomId).limit(2).toList();
      settings = List.of(
          newSdLanProfileRegularSetting(sdLanIds.get(0), venueIds.get(0)),
          newSdLanProfileRegularSetting(sdLanIds.get(1), venueIds.get(1)));
      expectedMap = toSdLanIdsVenueIdsMap(settings);
    }

    @Test
    void GivenEmptyServiceIdList() {
      willReturn(settings).given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndVenueIsNotNull(anyString());
      willReturn(settings)
          .given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(anyString(), anyList());

      ScopeData scopeData = new ScopeData();
      Scope scope = new Scope();
      scope.setVenues(venueIds);
      scopeData.setScopes(scope);
      willReturn(Optional.of(scopeData)).given(scopeDataService).getScopeData();

      BDDAssertions.then(
              unit.getSdLanIdsVenueIdsMapByServiceIds(txCtxExtension.getTenantId(), null))
          .isNotNull()
          .hasSize(sdLanIds.size())
          .allSatisfy((key, value) -> {
            List<String> expectedValues = expectedMap.get(key);
            assertThat(value).containsExactlyInAnyOrderElementsOf(expectedValues);
          });

      then(sdLanProfileRegularSettingRepository)
          .should()
          .findByTenantIdAndVenueIsNotNull(eq(txCtxExtension.getTenantId()));
    }

    @Test
    void GivenServiceIdList() {
      willReturn(settings)
          .given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(anyString(), anyList());

      ScopeData scopeData = new ScopeData();
      Scope scope = new Scope();
      scope.setVenues(venueIds);
      scopeData.setScopes(scope);
      willReturn(Optional.of(scopeData)).given(scopeDataService).getScopeData();

      BDDAssertions.then(
              unit.getSdLanIdsVenueIdsMapByServiceIds(txCtxExtension.getTenantId(), sdLanIds))
          .isNotNull()
          .hasSize(sdLanIds.size())
          .allSatisfy((key, value) -> {
            List<String> expectedValues = expectedMap.get(key);
            assertThat(value).containsExactlyInAnyOrderElementsOf(expectedValues);
          });

      then(sdLanProfileRegularSettingRepository)
          .should()
          .findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(eq(txCtxExtension.getTenantId()),
              argThat(ids -> CollectionUtils.isEqualCollection(ids, sdLanIds)));
    }

    @Test
    void GivenEmptySdLanProfileRegularSettingList() {
      willReturn(List.of())
          .given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndVenueIsNotNull(anyString());
      willReturn(List.of())
          .given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(anyString(), anyList());

      BDDAssertions.then(
              unit.getSdLanIdsVenueIdsMapByServiceIds(txCtxExtension.getTenantId(), List.of()))
          .isNotNull()
          .hasSize(0);

      then(sdLanProfileRegularSettingRepository)
          .should()
          .findByTenantIdAndVenueIsNotNull(eq(txCtxExtension.getTenantId()));
    }

    @Test
    void GivenServiceIdListWithScopeNotEmpty() {
      willReturn(settings)
          .given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(anyString(), anyList());
      ScopeData scopeData = new ScopeData();
      Scope scope = new Scope();
      scope.setVenues(List.of(venueIds.get(0)));
      scopeData.setScopes(scope);
      willReturn(Optional.of(scopeData)).given(scopeDataService).getScopeData();

      BDDAssertions.then(
              unit.getSdLanIdsVenueIdsMapByServiceIds(txCtxExtension.getTenantId(), sdLanIds))
          .isNotNull()
          .hasSize(1)
          .allSatisfy((key, value) -> {
            List<String> expectedValues = expectedMap.get(key);
            assertThat(value).containsExactlyInAnyOrderElementsOf(expectedValues);
          });

      then(sdLanProfileRegularSettingRepository)
          .should()
          .findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(eq(txCtxExtension.getTenantId()),
              argThat(ids -> CollectionUtils.isEqualCollection(ids, sdLanIds)));
    }

  }

  @Nested
  class WhenToSdLanApCompatibilities {

    @Test
    void GivenVenueSdLanCompatibilityList() {
      final var serviceId = CommonTestFixture.randomId();
      final var venueSdLanCompatibilityList = List.of(
          new VenueSdLanApCompatibility(CommonTestFixture.randomId(), List.of(), 1, 1));

      final var expected = new SdLanApCompatibilities();
      expected.setServiceId(serviceId);
      expected.setVenueSdLanApCompatibilities(venueSdLanCompatibilityList);

      BDDAssertions.then(unit.toSdLanApCompatibilities(serviceId, venueSdLanCompatibilityList))
          .isNotNull()
          .satisfies(result -> {
            assertThat(result.getServiceId()).isEqualTo(expected.getServiceId());
            assertThat(result.getVenueSdLanApCompatibilities())
                .containsExactlyInAnyOrderElementsOf(expected.getVenueSdLanApCompatibilities());
          });
    }

  }

  @Nested
  class WhenMergeApCompatibilities {

    @Test
    void GivenSdLanCompatibilityOnly() {
      int totalDeviceCount = RandomUtils.nextInt(3, 10);
      int sdLanIncompatibleCount = 1;
      int tunnelProfileIncompatibleCount = 0;
      ApCompatibility sdLanApCompatibility = generateFeatureCompatibility(
          SD_LAN.getFeatureName(), sdLanIncompatibleCount, totalDeviceCount);
      ApCompatibility tunnelProfileApCompatibility = generateFeatureCompatibility(
          TUNNEL_PROFILE.getFeatureName(), tunnelProfileIncompatibleCount, totalDeviceCount);

      BDDAssertions.then(unit.mergeApCompatibilities(tunnelProfileApCompatibility, sdLanApCompatibility))
          .isNotNull()
          .satisfies(result -> {
            assertEquals(result.getIncompatible(), sdLanIncompatibleCount);
            assertEquals(result.getTotal(), totalDeviceCount);
            assertEquals(result.getIncompatibleFeatures().stream()
                .filter(incompatibleFeature ->
                    incompatibleFeature.getFeatureName().equals(SD_LAN.getFeatureName()))
                .map(incompatibleFeature ->
                    incompatibleFeature.getIncompatibleDevices().get(0).getCount())
                .findFirst().get(), sdLanIncompatibleCount);
            assertEquals(result.getIncompatibleFeatures().stream()
                .filter(incompatibleFeature ->
                    incompatibleFeature.getFeatureName().equals(TUNNEL_PROFILE.getFeatureName()))
                .count(), 0);
          });
    }

    @Test
    void GivenTunnelProfileCompatibilityOnly() {
      int totalDeviceCount = RandomUtils.nextInt(3, 10);
      int sdLanIncompatibleCount = 0;
      int tunnelProfileIncompatibleCount = 2;
      ApCompatibility sdLanApCompatibility = generateFeatureCompatibility(
          SD_LAN.getFeatureName(), sdLanIncompatibleCount, totalDeviceCount);
      ApCompatibility tunnelProfileApCompatibility = generateFeatureCompatibility(
          TUNNEL_PROFILE.getFeatureName(), tunnelProfileIncompatibleCount, totalDeviceCount);

      BDDAssertions.then(unit.mergeApCompatibilities(tunnelProfileApCompatibility, sdLanApCompatibility))
          .isNotNull()
          .satisfies(result -> {
            assertEquals(result.getIncompatible(), tunnelProfileIncompatibleCount);
            assertEquals(result.getTotal(), totalDeviceCount);
            assertEquals(result.getIncompatibleFeatures().stream()
                .filter(incompatibleFeature ->
                    incompatibleFeature.getFeatureName().equals(TUNNEL_PROFILE.getFeatureName()))
                .map(incompatibleFeature ->
                    incompatibleFeature.getIncompatibleDevices().get(0).getCount())
                .findFirst().get(), tunnelProfileIncompatibleCount);
            assertEquals(result.getIncompatibleFeatures().stream()
                .filter(incompatibleFeature ->
                    incompatibleFeature.getFeatureName().equals(SD_LAN.getFeatureName()))
                .count(), 0);
          });
    }

    @Test
    void GivenBothSdLanAndTunnelProfileNotEmpty() {
      int totalDeviceCount = RandomUtils.nextInt(3, 10);
      int sdLanIncompatibleCount = 1;
      int tunnelProfileIncompatibleCount = 2;
      ApCompatibility sdLanApCompatibility = generateFeatureCompatibility(
          SD_LAN.getFeatureName(), sdLanIncompatibleCount, totalDeviceCount);
      ApCompatibility tunnelProfileApCompatibility = generateFeatureCompatibility(
          TUNNEL_PROFILE.getFeatureName(), tunnelProfileIncompatibleCount, totalDeviceCount);

      BDDAssertions.then(unit.mergeApCompatibilities(tunnelProfileApCompatibility, sdLanApCompatibility))
          .isNotNull()
          .satisfies(result -> {
            assertEquals(result.getIncompatible(), tunnelProfileIncompatibleCount);
            assertEquals(result.getTotal(), totalDeviceCount);
            assertEquals(result.getIncompatibleFeatures().stream()
                .filter(incompatibleFeature ->
                    incompatibleFeature.getFeatureName().equals(TUNNEL_PROFILE.getFeatureName()))
                .map(incompatibleFeature ->
                    incompatibleFeature.getIncompatibleDevices().get(0).getCount())
                .findFirst().get(), tunnelProfileIncompatibleCount);
            assertEquals(result.getIncompatibleFeatures().stream()
                .filter(incompatibleFeature ->
                    incompatibleFeature.getFeatureName().equals(SD_LAN.getFeatureName()))
                .map(incompatibleFeature ->
                    incompatibleFeature.getIncompatibleDevices().get(0).getCount())
                .findFirst().get(), sdLanIncompatibleCount);
            assertEquals(result.getIncompatibleFeatures().stream()
                .count(), 2);
          });
    }

    @Test
    void GivenBothSdLanAndTunnelProfileBothEmpty() {
      int totalDeviceCount = RandomUtils.nextInt(3, 10);
      int sdLanIncompatibleCount = 0;
      int tunnelProfileIncompatibleCount = 0;
      ApCompatibility sdLanApCompatibility = generateFeatureCompatibility(
          SD_LAN.getFeatureName(), sdLanIncompatibleCount, totalDeviceCount);
      ApCompatibility tunnelProfileApCompatibility = generateFeatureCompatibility(
          TUNNEL_PROFILE.getFeatureName(), tunnelProfileIncompatibleCount, totalDeviceCount);

      BDDAssertions.then(unit.mergeApCompatibilities(tunnelProfileApCompatibility, sdLanApCompatibility))
          .isNotNull()
          .satisfies(result -> {
            assertEquals(result.getIncompatible(), 0);
            assertEquals(result.getTotal(), totalDeviceCount);
            assertEquals(result.getIncompatibleFeatures().size(),0);
          });
    }
  }

  @Nested
  class WhenGetVenuesNetworksList {

    List<String> sdLanIds;
    List<String> venueIds;
    List<SdLanProfileRegularSetting> regularSettings;
    List<EdgeServiceVenuesNetworks> expectedList;
    ScopeData scopeData;

    @BeforeEach
    void init() {
      sdLanIds = Stream.generate(CommonTestFixture::randomId).limit(2).toList();
      venueIds = Stream.generate(CommonTestFixture::randomId).limit(2).toList();
      SdLanProfileRegularSetting regularSetting0 = newSdLanProfileRegularSetting(
          sdLanIds.get(0), venueIds.get(0));
      regularSetting0.setSdLanProfileNetworkMappings(List.of(
          newSdLanProfileNetworkMapping("networkId")));

      SdLanProfileRegularSetting regularSetting1 = newSdLanProfileRegularSetting(
          sdLanIds.get(1), venueIds.get(1));
      regularSetting1.setSdLanProfileNetworkMappings(List.of(
          newSdLanProfileNetworkMapping( "networkId")));
      regularSettings = List.of(regularSetting0, regularSetting1);

      expectedList = List.of(
          EdgeServiceVenuesNetworks.builder()
              .serviceId(sdLanIds.get(0))
              .venueIds(List.of(venueIds.get(0)))
              .networkIds(List.of("networkId"))
              .build(),
          EdgeServiceVenuesNetworks.builder()
              .serviceId(sdLanIds.get(1))
              .venueIds(List.of(venueIds.get(1)))
              .networkIds(List.of("networkId"))
              .build()
      );

      scopeData = new ScopeData();
      Scope scope = new Scope();
      scope.setVenues(venueIds);
      scopeData.setScopes(scope);
      willReturn(Optional.of(scopeData)).given(scopeDataService).getScopeData();
    }

    @Test
    void GivenEmptyServiceIdList() {
      willReturn(regularSettings).given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndVenueIsNotNull(anyString());
      willReturn(regularSettings)
          .given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(anyString(), anyList());

      BDDAssertions.then(
              unit.getVenuesNetworksList(txCtxExtension.getTenantId(), null))
          .isNotNull()
          .hasSize(sdLanIds.size())
          .allSatisfy(venuesNetworks -> {
            assertThat(venuesNetworks).isIn(expectedList);
          });

      then(sdLanProfileRegularSettingRepository)
          .should()
          .findByTenantIdAndVenueIsNotNull(eq(txCtxExtension.getTenantId()));
    }

    @Test
    void GivenEmptyServiceIdListWithLimitedScope() {
      willReturn(regularSettings).given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndVenueIsNotNull(anyString());
      willReturn(regularSettings)
          .given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(anyString(), anyList());
      scopeData.getScopes().setVenues(List.of(venueIds.get(0)));

      BDDAssertions.then(
              unit.getVenuesNetworksList(txCtxExtension.getTenantId(), null))
          .isNotNull()
          .hasSize(scopeData.getScopes().getVenues().size())
          .allSatisfy(venuesNetworks -> {
            assertThat(venuesNetworks).isIn(expectedList);
          });

      then(sdLanProfileRegularSettingRepository)
          .should()
          .findByTenantIdAndVenueIsNotNull(eq(txCtxExtension.getTenantId()));
    }

    @Test
    void GivenNotEmptyServiceIdList() {
      List<String> serviceIds = List.of(sdLanIds.get(0));
      willReturn(List.of(regularSettings.get(0)))
          .given(sdLanProfileRegularSettingRepository)
          .findByTenantIdAndSdLanProfileIdInAndVenueIsNotNull(anyString(), eq(serviceIds ));

      BDDAssertions.then(
              unit.getVenuesNetworksList(txCtxExtension.getTenantId(), List.of(regularSettings.get(0).getSdLanProfile().getId())))
          .isNotNull()
          .hasSize(serviceIds.size())
          .allSatisfy(venuesNetworks -> {
            assertThat(venuesNetworks).isIn(expectedList);
          });

      then(sdLanProfileRegularSettingRepository)
          .should(never())
          .findByTenantIdAndVenueIsNotNull(eq(txCtxExtension.getTenantId()));
    }
  }

  private SdLanProfileRegularSetting newSdLanProfileRegularSetting(String sdLanId, String venueId) {
    SdLanProfileRegularSetting sdLanProfileRegularSetting = new SdLanProfileRegularSetting();
    sdLanProfileRegularSetting.setSdLanProfile(new SdLanProfile(sdLanId));
    sdLanProfileRegularSetting.setVenue(new Venue(venueId));
    return sdLanProfileRegularSetting;
  }

  private SdLanProfileNetworkMapping newSdLanProfileNetworkMapping(String networkId) {
    SdLanProfileNetworkMapping sdLanProfileNetworkMapping = new SdLanProfileNetworkMapping();
    sdLanProfileNetworkMapping.setNetwork(new Network(networkId));
    return sdLanProfileNetworkMapping;
  }

  private HashMap<String, List<String>> toSdLanIdsVenueIdsMap(
      List<SdLanProfileRegularSetting> settings) {
    return settings.stream()
        .filter(setting -> setting.getSdLanProfile() != null)
        .collect(Collectors.groupingBy(setting -> setting.getSdLanProfile().getId(),
            HashMap::new,
            Collectors.mapping(setting -> setting.getVenue().getId(), Collectors.toList())));
  }

  private ApCompatibility generateFeatureCompatibility(
      String featureName, int deviceCount, int totalDeviceCount) {
    final List<IncompatibleWifiFeature> incompatibleFeatures = deviceCount == 0 ?
        List.of() : List.of(generateIncompatibleFeature( featureName, deviceCount));

    return new ApCompatibility(randomId(),
        incompatibleFeatures,
        totalDeviceCount,
        deviceCount);
  }

  private IncompatibleWifiFeature generateIncompatibleFeature(
      String featureName, int deviceCount) {
    return new IncompatibleWifiFeature(
        featureName,
        ApRequirement.builder().requiredFw(RandomStringUtils.randomAlphanumeric(10))
        .supportedModelFamilies(new String[]{RandomStringUtils.randomAlphanumeric(5)})
        .build(),
        List.of(generateDeviceAggregation(deviceCount)));
  }

  private DeviceAggregation<ApFirmwareModel> generateDeviceAggregation(int count) {
    return DeviceAggregation.<ApFirmwareModel>builder()
        .device(
            ApFirmwareModel.builder()
                .firmware(RandomStringUtils.randomAlphanumeric(10))
                .model(RandomStringUtils.randomAlphanumeric(5))
                .build())
        .count(count)
        .build();
  }

}
