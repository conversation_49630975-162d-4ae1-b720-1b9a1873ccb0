package com.ruckus.cloud.wifi.service.integration;


import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceRule;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BridgeServiceEnum;
import com.ruckus.cloud.wifi.mapper.MulticastDnsProxyServiceProfilePartialMerge;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.MulticastDnsProxyServiceProfileApRepository;
import com.ruckus.cloud.wifi.repository.MulticastDnsProxyServiceProfileRepository;
import com.ruckus.cloud.wifi.repository.MulticastDnsProxyServiceRuleRepository;
import com.ruckus.cloud.wifi.service.ApDeleteService;
import com.ruckus.cloud.wifi.service.ExtendedApServiceCtrl;
import com.ruckus.cloud.wifi.service.InitApGroupService;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.impl.MulticastDnsProxyServiceProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedApServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.MulticaseDnsProxyTestFixture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
@TestPropertySource(properties = {
        "default.multicast-dns-proxy-service-profile.tenant-max-count: 5",
        "default.multicast-dns-proxy-service-profile.rule-max-count: 5"})
public class MulticastDnsProxyProfileServiceTest extends AbstractServiceTest {

  private final int MAX_RULE_COUNT = 5;
  private final int MAX_TENANT_COUNT = 5;

  @Autowired
  private MulticastDnsProxyServiceProfileApRepository multicastDnsProxyServiceProfileApRepository;
  @Autowired
  private MulticastDnsProxyServiceRuleRepository multicastDnsProxyServiceRuleRepository;
  @Autowired
  private MulticastDnsProxyServiceProfileRepository multicastDnsProxyServiceProfileRepository;
  @MockBean
  private InitApGroupService initApGroupService;
  @MockBean
  private ApDeleteService apDeleteService;
  @Autowired
  private MulticastDnsProxyServiceProfileServiceCtrlImpl mDnsProxyServiceProfileServiceCtrl;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();


  @Test
  public void testAddMulticastDnsProxyServiceProfileExceedMaxRecord(Tenant tenant) throws Exception {
    createVenue(tenant, "venue-test");

    //When add MAX_TENANT_COUNT records
    for (int count = 0; count < MAX_TENANT_COUNT; count++) {
      String serviceName = "mDnsProxyProfile" + count;
      MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile(serviceName);
      mDnsProxyServiceProfileServiceCtrl.addMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile);
    }

    MulticastDnsProxyServiceProfile expectedMDnsProfile = MulticaseDnsProxyTestFixture.randomMulticastDnsProxyServiceProfile();
    CommonException exception = assertThrows(CommonException.class, () -> {
      mDnsProxyServiceProfileServiceCtrl.addMulticastDnsProxyServiceProfile(expectedMDnsProfile);
    });
    assertEquals(Errors.WIFI_10425, exception.getErrorCode());
  }

  @Test
  public void testAddMulticastDnsProxyProfileExceedMaxRecord(Tenant tenant) throws Exception {
    createVenue(tenant, "venue-test");

    //When add MAX_TENANT_COUNT records
    for (int count = 0; count < MAX_TENANT_COUNT; count++) {
      String serviceName = "mDnsProxyProfile" + count;
      MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile(serviceName);
      mDnsProxyServiceProfileServiceCtrl.addMulticastDnsProxyProfile(multicastDnsProxyServiceProfile);
    }

    MulticastDnsProxyServiceProfile expectedMDnsProfile = MulticaseDnsProxyTestFixture.randomMulticastDnsProxyServiceProfile();
    CommonException exception = assertThrows(CommonException.class, () -> {
      mDnsProxyServiceProfileServiceCtrl.addMulticastDnsProxyProfile(expectedMDnsProfile);
    });
    assertEquals(Errors.WIFI_10425, exception.getErrorCode());
  }

  @Test
  public void testUpdateMulticastDnsProxyServiceProfileRuleExceedMaxRecord(Tenant tenant) throws Exception {
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = addMulticastDnsProxyServiceProfile(
        MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1"));

    for (int count = 0; count < MAX_RULE_COUNT; count++) {
      MulticastDnsProxyServiceRule multicastDnsProxyServiceRule = new MulticastDnsProxyServiceRule();
      multicastDnsProxyServiceRule.setService(BridgeServiceEnum.AIRPLAY);
      multicastDnsProxyServiceRule.setEnabled(true);
      multicastDnsProxyServiceRule.setFromVlan(3+count);
      multicastDnsProxyServiceRule.setToVlan(4+count);
      multicastDnsProxyServiceProfile.getRules().add(multicastDnsProxyServiceRule);
    }

    CommonException exception = assertThrows(CommonException.class, () -> {
      mDnsProxyServiceProfileServiceCtrl.updateMulticastDnsProxyServiceProfile(
          multicastDnsProxyServiceProfile.getId(), multicastDnsProxyServiceProfile);
    });
    assertEquals(Errors.WIFI_10434, exception.getErrorCode());
  }

  @Test
  public void testUpdateMulticastDnsProxyProfileRuleExceedMaxRecord(Tenant tenant) throws Exception {
     MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = addMulticastDnsProxyProfile(
         MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1"));

    for (int count = 0; count < MAX_RULE_COUNT; count++) {
      MulticastDnsProxyServiceRule multicastDnsProxyServiceRule = new MulticastDnsProxyServiceRule();
      multicastDnsProxyServiceRule.setService(BridgeServiceEnum.AIRPLAY);
      multicastDnsProxyServiceRule.setEnabled(true);
      multicastDnsProxyServiceRule.setFromVlan(3+count);
      multicastDnsProxyServiceRule.setToVlan(4+count);
      multicastDnsProxyServiceProfile.getRules().add(multicastDnsProxyServiceRule);
    }

    CommonException exception = assertThrows(CommonException.class, () -> {
      mDnsProxyServiceProfileServiceCtrl.updateMulticastDnsProxyProfile(
          multicastDnsProxyServiceProfile.getId(), multicastDnsProxyServiceProfile);
    });
    assertEquals(Errors.WIFI_10434, exception.getErrorCode());
  }

  @Test
  public void testGetMulticastDnsProxyServiceProfileApsByVenueWithoutAp(Tenant tenant) throws Exception {
    Venue venue = createVenue(tenant, "venue-test");
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");

    mDnsProxyServiceProfileServiceCtrl.addMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile);

    assertDoesNotThrow(() -> {
      List<MulticastDnsProxyServiceProfileAp> list = mDnsProxyServiceProfileServiceCtrl.getMulticastDnsProxyServiceProfileApsByVenue(venue.getId());
    });
    assertEquals(0, mDnsProxyServiceProfileServiceCtrl.getMulticastDnsProxyServiceProfileApsByVenue(venue.getId()).size());
  }

  @Test
  public void testGetMulticastDnsProxyServiceProfileApsByVenueWithAp(Tenant tenant) throws Exception {
    Venue venue = createVenue(tenant, "venue-test");
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProxyProfile1");
    Ap ap = createAp(tenant, venue);
    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setAp(ap);
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(Collections.singletonList(mDnsAp));
    mDnsProxyServiceProfileServiceCtrl.addMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile);

    assertDoesNotThrow(() -> {
      List<MulticastDnsProxyServiceProfileAp> list = mDnsProxyServiceProfileServiceCtrl.getMulticastDnsProxyServiceProfileApsByVenue(venue.getId());
    });

    List<MulticastDnsProxyServiceProfileAp> profileAps = mDnsProxyServiceProfileServiceCtrl.getMulticastDnsProxyServiceProfileApsByVenue(venue.getId());
    assertEquals(1, profileAps.size());
    assertEquals(ap.getId(), profileAps.get(0).getAp().getId());
  }

  @TestConfiguration
  @Import(ExtendedApServiceCtrlImplTestConfig.class)
  static class TestConfig {

    private final int MAX_RULE_COUNT = 5;
    private final int MAX_TENANT_COUNT = 5;
    @Bean
    MulticastDnsProxyServiceProfileServiceCtrlImpl mDnsProxyServiceProfileServiceCtrl(
            MulticastDnsProxyServiceProfileRepository multicastDnsProxyServiceProfileRepository,
            MulticastDnsProxyServiceProfileApRepository multicastDnsProxyServiceProfileApRepository,
            MulticastDnsProxyServiceRuleRepository multicastDnsProxyServiceRuleRepository,
            ExtendedApServiceCtrl apServiceCtrl, ApRepository apRepository,
            MulticastDnsProxyServiceProfilePartialMerge merge) {
      return new MulticastDnsProxyServiceProfileServiceCtrlImpl(MAX_TENANT_COUNT, MAX_RULE_COUNT,
          multicastDnsProxyServiceProfileApRepository, multicastDnsProxyServiceRuleRepository,
          multicastDnsProxyServiceProfileRepository, apServiceCtrl, apRepository, merge);
    }

    @Bean
    @ConditionalOnMissingBean
    public MulticastDnsProxyServiceProfilePartialMerge multicastDnsProxyServiceProfilePartialMerge() {
      return mock(MulticastDnsProxyServiceProfilePartialMerge.class);
    }
  }
}
