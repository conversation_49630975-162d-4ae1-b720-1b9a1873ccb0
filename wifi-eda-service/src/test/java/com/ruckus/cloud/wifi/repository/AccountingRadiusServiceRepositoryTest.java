package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenantId01');
    
    INSERT INTO radius (id, tenant, is_template)
        VALUES ('radiusId01', 'tenantId01', 'false');
    
    INSERT INTO cloudpath_server(id, tenant)
        VALUES ('cloudpathId01', 'tenantId01');
        
    INSERT INTO accounting_radius_service(id, tenant, radius)
        VALUES ('radiusServiceId01', 'tenantId01', 'radiusId01');
    INSERT INTO accounting_radius_service(id, tenant, radius)
        VALUES ('radiusServiceId02', 'tenantId01', 'radiusId01');
    INSERT INTO accounting_radius_service(id, tenant, radius, guest_portal, cloudpath_server)
        VALUES ('radiusServiceId03', 'tenantId01', 'radiusId01', null, null);
    INSERT INTO accounting_radius_service(id, tenant, radius, guest_portal, cloudpath_server)
        VALUES ('radiusServiceId04', 'tenantId01', 'radiusId01', null, 'cloudpathId01');

    INSERT INTO accounting_radius_profile (id, tenant, accounting_radius_service, hotspot20support_enabled)
        VALUES ('radiusProfileId01', 'tenantId01', 'radiusServiceId01', 'true');
    INSERT INTO accounting_radius_profile (id, tenant, accounting_radius_service)
        VALUES ('radiusProfileId02', 'tenantId01', 'radiusServiceId03');        
    INSERT INTO accounting_radius_profile (id, tenant, accounting_radius_service)
        VALUES ('radiusProfileId04', 'tenantId01', 'radiusServiceId04');
    """)
class AccountingRadiusServiceRepositoryTest {

  private static final String TENANT_ID = "tenantId01";

  @Autowired
  AccountingRadiusServiceRepository repository;

  @Test
  void findByTenantIdAndAccountingRadiusServiceId() {

    int count = repository.countByRadiusId("radiusId01");
    assertEquals(4, count);

    var result1 = repository.findByTenantIdAndRadiusIdAndGuestPortalIdIsNullAndCloudpathServerIdIsNullAndHotspot20SupportEnabledIsTrue(TENANT_ID, "radiusId01");
    assertEquals("radiusServiceId01", result1.getId());

    var service3 = repository.findByTenantIdAndRadiusIdAndGuestPortalIdIsNullAndCloudpathServerIdIsNullAndAndHotspot20SupportEnabledIsNotTrue(TENANT_ID, "radiusId01");
    assertEquals("radiusServiceId03", service3.getId());

  }
}