package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioCustomization;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueRadioParams6G;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class AfcFeatureTest {

  @SpyBean
  private AfcFeature unit;

  @Nested
  class WhenIsAfcEnabled {
    @Test
    void givenAfcIsNull() {
      final var venue = new Venue();
      VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
      VenueRadioParams6G radioParams6G = new VenueRadioParams6G();
      radioParams6G.setEnableAfc(null);
      venueRadioCustomization.setRadioParams6G(radioParams6G);
      venue.setRadioCustomization(venueRadioCustomization);
      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Nested
    class GivenBssColoringIsNotNull {

      @Test
      void thenReturnAfcEnabled() {
        final var venue = new Venue();
        VenueRadioCustomization venueRadioCustomization = new VenueRadioCustomization();
        VenueRadioParams6G radioParams6G = new VenueRadioParams6G();
        radioParams6G.setEnableAfc(true);
        venueRadioCustomization.setRadioParams6G(radioParams6G);
        venue.setRadioCustomization(venueRadioCustomization);
        BDDAssertions.then(unit.test(venue)).isTrue();
      }
    }
  }
}
