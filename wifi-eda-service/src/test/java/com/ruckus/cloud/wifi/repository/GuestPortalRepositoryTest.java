package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tenant (id) VALUES ('a782b402d95c46b4aabce87b77e68612');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        'be3cb5cdef1d4fedbb7b778774157e41',
        'GUEST',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO guest_portal (id, tenant, network, guest_network_type) VALUES (
        'e65ed4dc6fa04a329cf22e76db0128e2',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        'GuestPass');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'efd0e13cc150444ca1956dd68c8999f2',
        '37081c84876547f28b2a11950ad46141',
        'GUEST',
        'a782b402d95c46b4aabce87b77e68612');
    INSERT INTO guest_portal (id, tenant, network, guest_network_type, aaa_name) VALUES (
        'efd0e13cc150444ca1956dd68c8999f2',
        'a782b402d95c46b4aabce87b77e68612',
        'efd0e13cc150444ca1956dd68c8999f2',
        'WISPr',
        'Cloud4WiX');
    """)
public class GuestPortalRepositoryTest {

  @Autowired
  private GuestPortalRepository target;

  @Test
  void testFindTenantIdByGuestNetworkType() {
    var result = target.findTenantIdByGuestNetworkType(GuestNetworkTypeEnum.GuestPass);
    assertEquals(1, result.size());
    assertTrue(result.contains("4c8279f79307415fa9e4c88a1819f0fc"));
  }

  @Test
  void testFindTenantIdByNotGuestNetworkType() {
    var result = target.findTenantIdByNotGuestNetworkType(GuestNetworkTypeEnum.WISPr);
    assertEquals(1, result.size());
    assertTrue(result.contains("4c8279f79307415fa9e4c88a1819f0fc"));
  }

  @Test
  void testFindAaaNameByGuestNetworkAndTenantId() {
    var result = target.findAaaNameByGuestNetworkAndTenantId(GuestNetworkTypeEnum.WISPr,
        "a782b402d95c46b4aabce87b77e68612");
    assertEquals(1, result.size());
    assertTrue(result.contains("Cloud4WiX"));
  }

  @Test
  void testCountByGuestNetworkTypeInAndTenantId() {
    var result = target.countByGuestNetworkTypeInAndTenantId(
        List.of(GuestNetworkTypeEnum.WISPr), "a782b402d95c46b4aabce87b77e68612");
    assertEquals(1, result);

    result = target.countByGuestNetworkTypeInAndTenantId(
        List.of(GuestNetworkTypeEnum.WISPr), "4c8279f79307415fa9e4c88a1819f0fc");
    assertEquals(0, result);

    result = target.countByGuestNetworkTypeInAndTenantId(
        List.of(GuestNetworkTypeEnum.GuestPass), "4c8279f79307415fa9e4c88a1819f0fc");
    assertEquals(1, result);
  }

}
