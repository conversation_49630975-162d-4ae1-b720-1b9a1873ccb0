package com.ruckus.cloud.wifi.requirement.feature;

import static org.apache.commons.lang3.RandomUtils.nextBoolean;

import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import org.assertj.core.api.BDDAssertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class TlsKeyEnhancedModeFeatureTest {

  @SpyBean
  private TlsKeyEnhancedModeFeature unit;


  @Nested
  class WhenIsTlsKeyEnhancedModeEnabled {

    @Test
    void givenTlsKeyEnhancedModeIsNull() {
      final var venue = new Venue();
      venue.setTlsKeyEnhancedModeEnabled(null);

      BDDAssertions.then(unit.test(venue)).isFalse();
    }

    @Nested
    class GivenTlsKeyEnhancedModeIsNotNull {

      @Test
      void thenReturnTlsKeyEnhancedModeEnabled() {
        final var enabled = nextBoolean();
        final var venue = new Venue();
        venue.setTlsKeyEnhancedModeEnabled(enabled);

        BDDAssertions.then(unit.test(venue)).isEqualTo(enabled);
      }
    }
  }

}
