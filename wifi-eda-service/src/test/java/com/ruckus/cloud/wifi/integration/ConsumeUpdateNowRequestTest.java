package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueApModel;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.request.CfgExtendedAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApFirmwareUpgradeRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.EthernetPortProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueLanPort;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckus.cloud.wifi.viewmodel.UpdateNow;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@FeatureFlag(enable = FlagNames.WIFI_EDA_FW_MANAGEMENT_RBAC_BUILD_OPERATION_TOGGLE)
public class ConsumeUpdateNowRequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;

  @Autowired private RepositoryUtil repositoryUtil;

  @Autowired private MessageCaptors messageCaptors;

  private final String DEFAULT_VERSION = "6.2.1.103.1";

  private final String TARGET_VERSION = "6.2.1.103.2";

  private final String REQUEST_ID = randomTxId();

  @Test
  public void updateNowTest(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = DEFAULT_VERSION)
          ApVersion defaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = TARGET_VERSION)
          ApVersion targetVersion) {
    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());

    List<UpdateNow> requestViews = new ArrayList<>();
    requestViews.add(this.newUpdateNow(List.of(venue.getId()), "active", targetVersion.getId()));

    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");

    // When
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        REQUEST_ID,
        CfgExtendedAction.UPDATE_NOW,
        randomName(),
        new RequestParams(),
        requestViews);

    // Then
    validateVenueCurrentVersion(venue, TARGET_VERSION);
    assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), REQUEST_ID));
    validateActivityCfgMessageResult(tenant);
    validateDdccmCfgMessageResult(tenant, TARGET_VERSION);
  }

  @Test
  public void updateNowTest_updateTwoAbfVersion(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.111") ApVersion nonActiveDefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.112") ApVersion nonActiveTargetVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = DEFAULT_VERSION) ApVersion defaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = TARGET_VERSION) ApVersion targetVersion) {
    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    VenueFirmwareVersion vfv = createVenueFirmwareVersion(venue, nonActiveDefaultVersion, "eol-ap-2022-12");

    List<UpdateNow> requestViews = new ArrayList<>();
    requestViews.add(this.newUpdateNow(List.of(venue.getId()), "active", targetVersion.getId()));
    requestViews.add(this.newUpdateNow(List.of(venue.getId()), "eol-ap-2022-12", nonActiveTargetVersion.getId()));

    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");
    ApFirmwareUpgradeRequest r500ApFwUpgradeReq = createAndJoinApToVenue(venue, "R500");
    ApFirmwareUpgradeRequest r770ApFwUpgradeReq = createAndJoinApToVenue(venue, "R770");

    // When
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        REQUEST_ID,
        CfgExtendedAction.UPDATE_NOW,
        randomName(),
        new RequestParams(),
        requestViews);

    // Then
    VenueFirmwareVersion updatedVfv = repositoryUtil.find(VenueFirmwareVersion.class, vfv.getId());
    assertEquals(nonActiveTargetVersion.getId(), updatedVfv.getCurrentFirmwareVersion().getId());
    validateVenueCurrentVersion(venue, TARGET_VERSION);
    assertTrue(
        isApFirmwareUpgradeRequestUpdated(r500ApFwUpgradeReq.getId(), REQUEST_ID, nonActiveTargetVersion.getId()));
    assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), REQUEST_ID, targetVersion.getId()));
    assertTrue(isApFirmwareUpgradeRequestUpdated(r770ApFwUpgradeReq.getId(), REQUEST_ID, targetVersion.getId()));
    validateActivityCfgMessageResult(tenant);
    validateDdccmCfgMessageResult(tenant, TARGET_VERSION);
  }

  @Test
  public void updateNowTest_onlyUpdateNonAbfVersion(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.111") ApVersion nonActiveDefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.112") ApVersion nonActiveTargetVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = DEFAULT_VERSION) ApVersion defaultVersion) {
    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    VenueFirmwareVersion vfvEol =
        createVenueFirmwareVersion(venue, nonActiveDefaultVersion, "eol-ap-2022-12");

    List<UpdateNow> requestViews = new ArrayList<>();
    requestViews.add(this.newUpdateNow(List.of(venue.getId()), "eol-ap-2022-12", nonActiveTargetVersion.getId()));

    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");
    ApFirmwareUpgradeRequest r500ApFwUpgradeReq = createAndJoinApToVenue(venue, "R500");
    ApFirmwareUpgradeRequest r750ApFwUpgradeReq = createAndJoinApToVenue(venue, "R750");

    // When
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        REQUEST_ID,
        CfgExtendedAction.UPDATE_NOW,
        randomName(),
        new RequestParams(),
        requestViews);

    // Then
    VenueFirmwareVersion updatedVfv = repositoryUtil.find(VenueFirmwareVersion.class, vfvEol.getId());
    assertEquals(nonActiveTargetVersion.getId(), updatedVfv.getCurrentFirmwareVersion().getId());
    validateVenueCurrentVersion(venue, DEFAULT_VERSION);
    assertTrue(
        isApFirmwareUpgradeRequestUpdated(r500ApFwUpgradeReq.getId(), REQUEST_ID, nonActiveTargetVersion.getId()));
    assertFalse(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), REQUEST_ID));
    assertFalse(isApFirmwareUpgradeRequestUpdated(r750ApFwUpgradeReq.getId(), REQUEST_ID));
    validateActivityCfgMessageResult(tenant);
    validateDdccmCfgMessageResult(tenant, null);
  }

  @Test
  public void updateNowTest_onlyUpdateVenueActiveAbfVersionAndTenantActiveAbfIsAbfWifi7(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.111") ApVersion nonActiveDefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = DEFAULT_VERSION) ApVersion defaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = TARGET_VERSION) ApVersion targetVersion) {
    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    VenueFirmwareVersion vfvEol =
        createVenueFirmwareVersion(venue, nonActiveDefaultVersion, "eol-ap-2022-12");
    // Tenant active abf is abf3-wifi7
    createVenueFirmwareVersion(venue, defaultVersion, "ABF2-3R");

    List<UpdateNow> requestViews = new ArrayList<>();
    requestViews.add(this.newUpdateNow(List.of(venue.getId()), "active", targetVersion.getId()));

    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");
    ApFirmwareUpgradeRequest r500ApFwUpgradeReq = createAndJoinApToVenue(venue, "R500");
    ApFirmwareUpgradeRequest r750ApFwUpgradeReq = createAndJoinApToVenue(venue, "R750");

    // When
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        REQUEST_ID,
        CfgExtendedAction.UPDATE_NOW,
        randomName(),
        new RequestParams(),
        requestViews);

    // Then
    assertEquals(nonActiveDefaultVersion.getId(),
        repositoryUtil.find(VenueFirmwareVersion.class, vfvEol.getId()).getCurrentFirmwareVersion().getId());
    validateVenueCurrentVersion(venue, TARGET_VERSION);
    assertFalse(isApFirmwareUpgradeRequestUpdated(r500ApFwUpgradeReq.getId(), REQUEST_ID));
    assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), REQUEST_ID, TARGET_VERSION));
    assertTrue(isApFirmwareUpgradeRequestUpdated(r750ApFwUpgradeReq.getId(), REQUEST_ID, TARGET_VERSION));
    validateActivityCfgMessageResult(tenant);
    validateDdccmCfgMessageResult(tenant, TARGET_VERSION);
  }

  @Nested
  class whenTargetVersionHasCustomModelRules {
    @Test
    public void updateNowTest_updateTwoAbfVersionWithNewActiveAbfVersion(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.111") ApVersion nonActiveDefaultVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.112") ApVersion nonActiveTargetVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.1") ApVersion defaultVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.222") ApVersion targetVersion) {
      // Given
      Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
      VenueFirmwareVersion vfvForEol =
          createVenueFirmwareVersion(venue, nonActiveDefaultVersion, "eol-ap-2022-12");
      VenueFirmwareVersion vfvFor3R =
          createVenueFirmwareVersion(venue, defaultVersion, "ABF2-3R");

      List<UpdateNow> requestViews = new ArrayList<>();
      requestViews.add(newUpdateNow(List.of(venue.getId()), "active", targetVersion.getId()));
      requestViews.add(
          newUpdateNow(List.of(venue.getId()), "eol-ap-2022-12", nonActiveTargetVersion.getId()));
      ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");
      ApFirmwareUpgradeRequest r500ApFwUpgradeReq = createAndJoinApToVenue(venue, "R500");
      ApFirmwareUpgradeRequest r770ApFwUpgradeReq = createAndJoinApToVenue(venue, "R770");
      ApFirmwareUpgradeRequest r350eApFwUpgradeReq = createAndJoinApToVenue(venue, "R350:R350E");

      // When
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          REQUEST_ID,
          CfgExtendedAction.UPDATE_NOW,
          randomName(),
          new RequestParams(),
          requestViews);

      // Then
      VenueFirmwareVersion updatedVfvForEol = repositoryUtil.find(VenueFirmwareVersion.class, vfvForEol.getId());
      assertEquals(nonActiveDefaultVersion.getId(), updatedVfvForEol.getCurrentFirmwareVersion().getId());
      VenueFirmwareVersion updatedVfvFor3R = repositoryUtil.find(VenueFirmwareVersion.class, vfvFor3R.getId());
      assertEquals(nonActiveTargetVersion.getId(), updatedVfvFor3R.getCurrentFirmwareVersion().getId());
      validateVenueCurrentVersion(venue, targetVersion.getId());
      assertFalse(
          isApFirmwareUpgradeRequestUpdated(r500ApFwUpgradeReq.getId(), REQUEST_ID, nonActiveTargetVersion.getId()));
      assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), REQUEST_ID, targetVersion.getId()));
      assertTrue(isApFirmwareUpgradeRequestUpdated(r770ApFwUpgradeReq.getId(), REQUEST_ID, targetVersion.getId()));
      assertTrue(isApFirmwareUpgradeRequestUpdated(r350eApFwUpgradeReq.getId(), REQUEST_ID, targetVersion.getId()));
      validateActivityCfgMessageResult(tenant);
      validateDdccmCfgMessageResult(tenant, targetVersion.getId());
    }

    @Tag("EthernetPortProfileTest")
    @Test
    public void updateNowTest_updateActiveAbfVersionAndExistUnsupportedApModelSpecificAttributesInVenue(
        Tenant tenant,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.111") ApVersion abf1DefaultVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.1.103.1") ApVersion abf2DefaultVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.105.222") ApVersion defaultVersion,
        @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.104.222") ApVersion targetVersion) {
      // Given
      Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
      repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
      createVenueFirmwareVersion(venue, abf1DefaultVersion, "eol-ap-2022-12");
      createVenueFirmwareVersion(venue, abf2DefaultVersion, "ABF2-3R");

      List<UpdateNow> requestViews = new ArrayList<>();
      requestViews.add(newUpdateNow(List.of(venue.getId()), "active", targetVersion.getId()));
      createVenueApModelSpecificAttributes(tenant, venue, List.of("R750", "H550", "R350:R350E"));

      ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");
      ApFirmwareUpgradeRequest r500ApFwUpgradeReq = createAndJoinApToVenue(venue, "R500");
      ApFirmwareUpgradeRequest r770ApFwUpgradeReq = createAndJoinApToVenue(venue, "R770");
      ApFirmwareUpgradeRequest r350eApFwUpgradeReq = createAndJoinApToVenue(venue, "R350:R350E");

      // When
      messageUtil.sendWifiCfgRequest(
          tenant.getId(),
          REQUEST_ID,
          CfgExtendedAction.UPDATE_NOW,
          randomName(),
          new RequestParams(),
          requestViews);

      // Then
      validateVenueCurrentVersion(venue, targetVersion.getId());
      assertFalse(
          isApFirmwareUpgradeRequestUpdated(r500ApFwUpgradeReq.getId(), REQUEST_ID, targetVersion.getId()));
      assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), REQUEST_ID, targetVersion.getId()));
      assertTrue(isApFirmwareUpgradeRequestUpdated(r770ApFwUpgradeReq.getId(), REQUEST_ID, targetVersion.getId()));
      assertTrue(isApFirmwareUpgradeRequestUpdated(r350eApFwUpgradeReq.getId(), REQUEST_ID, targetVersion.getId()));
      validateActivityCfgMessageResult(tenant);
      validateDdccmCfgMessageResult(tenant, targetVersion.getId(), List.of("R750", "H550"));
      List<Venue> venues = repositoryUtil.findAll(Venue.class, tenant.getId());
      validateVenuesAndApModelSpecificAttributes(venues, targetVersion, List.of("R750", "H550"),
          List.of("R350:R350E"));
    }
  }

  @Test
  void updateNowTest_onlyUpdateBetaVersion(
      Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "7.0.0.99.100") ApVersion betaVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = "6.2.0.103.111") ApVersion nonActiveDefaultVersion,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(value = DEFAULT_VERSION) ApVersion defaultVersion) {
    // Given
    Venue venue = VenueTestFixture.randomVenue(tenant, v -> v.setWifiFirmwareVersion(defaultVersion));
    repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId());
    VenueFirmwareVersion vfvEol =
        createVenueFirmwareVersion(venue, nonActiveDefaultVersion, "eol-ap-2022-12");

    List<UpdateNow> requestViews = new ArrayList<>();
    requestViews.add(this.newUpdateNow(List.of(venue.getId()), "beta", betaVersion.getId()));

    ApFirmwareUpgradeRequest r550ApFwUpgradeReq = createAndJoinApToVenue(venue, "R550");
    ApFirmwareUpgradeRequest r500ApFwUpgradeReq = createAndJoinApToVenue(venue, "R500");
    ApFirmwareUpgradeRequest r750ApFwUpgradeReq = createAndJoinApToVenue(venue, "R750");

    // When
    messageUtil.sendWifiCfgRequest(
        tenant.getId(),
        REQUEST_ID,
        CfgExtendedAction.UPDATE_NOW,
        randomName(),
        new RequestParams(),
        requestViews);

    // Then
    VenueFirmwareVersion updatedVfv = repositoryUtil.find(VenueFirmwareVersion.class, vfvEol.getId());
    assertEquals(nonActiveDefaultVersion.getId(), updatedVfv.getCurrentFirmwareVersion().getId());
    validateVenueCurrentVersion(venue, betaVersion.getId());
    assertFalse(
        isApFirmwareUpgradeRequestUpdated(r500ApFwUpgradeReq.getId(), REQUEST_ID));
    assertTrue(isApFirmwareUpgradeRequestUpdated(r550ApFwUpgradeReq.getId(), REQUEST_ID, betaVersion.getId()));
    assertTrue(isApFirmwareUpgradeRequestUpdated(r750ApFwUpgradeReq.getId(), REQUEST_ID, betaVersion.getId()));
    validateActivityCfgMessageResult(tenant);
    validateDdccmCfgMessageResult(tenant, betaVersion.getId());
  }

  private void validateVenueCurrentVersion(Venue venue, String version){
    Venue updatedVenue = repositoryUtil.find(Venue.class, venue.getId());
    assertThat(updatedVenue)
        .isNotNull()
        .matches(v -> v.getWifiFirmwareVersion().getId().equals(version));
  }

  void validateVenuesAndApModelSpecificAttributes(List<Venue> updatedVenues, ApVersion targetVersion,
      List<String> modelsWithSpecificAttrs, List<String> unsupportedModels) {
    List<String> modelsWithSpecificAttrsAfterUpdate = new ArrayList<>(modelsWithSpecificAttrs);
    modelsWithSpecificAttrsAfterUpdate.removeIf(unsupportedModels::contains);

    assertThat(updatedVenues)
        .hasSize(updatedVenues.size())
        .allMatch(venue -> targetVersion.getId().equals(venue.getWifiFirmwareVersion().getId()))
        .flatExtracting(Venue::getModelSpecificAttributes)
        .extracting(VenueApModelSpecificAttributes::getModel)
        .hasSize(modelsWithSpecificAttrsAfterUpdate.size())
        .doesNotContainAnyElementsOf(unsupportedModels)
        .containsAll(modelsWithSpecificAttrsAfterUpdate);
  }

  private boolean isApFirmwareUpgradeRequestUpdated(String apFirmwareUpgradeRequestId, String requestId) {
    return isApFirmwareUpgradeRequestUpdated(apFirmwareUpgradeRequestId, requestId, null);
  }

  private boolean isApFirmwareUpgradeRequestUpdated(
      String apFirmwareUpgradeRequestId, String requestId, String targetVersion) {
    ApFirmwareUpgradeRequest upgradeRequest =
        repositoryUtil.find(ApFirmwareUpgradeRequest.class, apFirmwareUpgradeRequestId);
    assertNotNull(upgradeRequest);

    return upgradeRequest.getRequestId().equals(requestId)
        && (Objects.isNull(targetVersion) || upgradeRequest.getTargetVersion().equals(targetVersion));
  }

  private void validateActivityCfgMessageResult(Tenant tenant) {
    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenant.getId(), REQUEST_ID);
    assertThat(activityCfgChangeRespMessage).isNotNull();
    assertThat(activityCfgChangeRespMessage.getPayload()).isNotNull();

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(REQUEST_ID);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .extracting(KafkaProtoMessage::getHeaders)
        .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
        .isNotNull()
        .extracting(Header::value)
        .extracting(String::new)
        .isEqualTo(tenant.getId());

    assertThat(activityCfgChangeRespMessage.getPayload())
        .matches(p -> p.getStatus().equals(Status.OK))
        .matches(p -> p.getStep().equals(CfgExtendedAction.Constants.UPDATE_NOW))
        .matches(p -> p.getEventDate() != null);
  }

  private void validateDdccmCfgMessageResult(Tenant tenant, String targetActiveVersion) {
    validateDdccmCfgMessageResult(tenant, targetActiveVersion, null);
  }

  private void validateDdccmCfgMessageResult(Tenant tenant, String targetActiveVersion, List<String> venueApModels) {
    boolean isOnlyUpdateNonActiveVersion = targetActiveVersion == null;
    if (isOnlyUpdateNonActiveVersion) {
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(tenant.getId());
    } else {
      final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
          .getValue(tenant.getId(), REQUEST_ID);

      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(REQUEST_ID);

      assertThat(ddccmCfgRequestMessage)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.TENANT_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(tenant.getId());

      assertThatNoException()
          .isThrownBy(
              () ->
                  assertThat(ddccmCfgRequestMessage.getPayload())
                      .extracting(WifiConfigRequest::getOperationsList)
                      .asList()
                      .isNotEmpty()
                      .extracting(Operation.class::cast)
                      .allMatch(op -> op.getAction() == Action.MODIFY)
                      .allSatisfy(
                          op ->
                              assertThat(op)
                                  .extracting(Operation::getCommonInfo)
                                  .matches(commonInfo -> REQUEST_ID.equals(commonInfo.getRequestId()))
                                  .matches(commonInfo -> tenant.getId().equals(commonInfo.getTenantId()))
                                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
                      .allSatisfy(
                          op -> {
                            assertThat(op)
                                .extracting(Operation::getVenue)
                                .matches(com.ruckus.acx.ddccm.protobuf.wifi.Venue::hasVersion)
                                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getVersion)
                                .matches(v -> targetActiveVersion.equals(v.getValue()));

                            if (!CollectionUtils.isEmpty(venueApModels)) {
                              assertThat(op)
                                  .extracting(Operation::getVenue)
                                  .extracting(venue -> venue.getVenueApModelsList().stream().map(
                                      VenueApModel::getModelName).toList())
                                  .matches(models -> models.size() == venueApModels.size() & models.containsAll(
                                      venueApModels));
                            }
                          })
          );
    }
  }

  private void createVenueApModelSpecificAttributes(Tenant tenant, Venue venue, List<String> models) {
    for (String model : models) {
      VenueApModelSpecificAttributes venueApModelSpecificAttributes = new VenueApModelSpecificAttributes();
      venueApModelSpecificAttributes.setVenue(venue);
      venueApModelSpecificAttributes.setModel(model);
      venueApModelSpecificAttributes.setTenant(tenant);
      venueApModelSpecificAttributes = repositoryUtil.createOrUpdate(venueApModelSpecificAttributes,
          tenant.getId(), randomTxId());

      EthernetPortProfile ethernetPortProfile = new EthernetPortProfile();
      ethernetPortProfile.setTenant(tenant);
      ethernetPortProfile.setApLanPortId(1);
      ethernetPortProfile = repositoryUtil.createOrUpdate(ethernetPortProfile, tenant.getId(),
          randomTxId());

      VenueLanPort venueLanPort = new VenueLanPort();
      venueLanPort.setVenueApModelSpecificAttributes(venueApModelSpecificAttributes);
      venueLanPort.setTenant(tenant);
      venueLanPort.setApLanPortProfile(ethernetPortProfile);
      venueLanPort.setPortId("1");
      repositoryUtil.createOrUpdate(venueLanPort, tenant.getId(), randomTxId());

      venueApModelSpecificAttributes.setLanPorts(List.of(venueLanPort));
      repositoryUtil.createOrUpdate(venueApModelSpecificAttributes, tenant.getId(), randomTxId());
    }
  }

  private VenueFirmwareVersion createVenueFirmwareVersion(Venue venue, ApVersion apVersion, String branchType) {
    return repositoryUtil.createOrUpdate(
            newVenueFirmwareVersion(venue, apVersion, branchType),
            venue.getTenant().getId(),
            randomTxId());
  }

  private ApFirmwareUpgradeRequest createAndJoinApToVenue(Venue venue, String apModel) {
    Ap ap = ApTestFixture.randomAp(venue);
    ap.setModel(apModel);

    return repositoryUtil.createOrUpdate(
        newApFirmwareUpgradeRequest(randomId(), venue.getTenant(), venue, ap),
        venue.getTenant().getId(),
        randomTxId());
  }

  private UpdateNow newUpdateNow(List<String> venueIds, String fwCategoryId, String fwVersion) {
    UpdateNow updateNow = new UpdateNow();
    updateNow.setVenueIds(venueIds);
    updateNow.setFirmwareCategoryId(fwCategoryId);
    updateNow.setFirmwareVersion(fwVersion);
    return updateNow;
  }

  private ApFirmwareUpgradeRequest newApFirmwareUpgradeRequest(
      String id, Tenant tenant, Venue venue, Ap ap) {
    ApFirmwareUpgradeRequest apFirmwareUpgradeRequest = new ApFirmwareUpgradeRequest();
    apFirmwareUpgradeRequest.setId(id);
    apFirmwareUpgradeRequest.setRequestId(randomId());
    apFirmwareUpgradeRequest.setTenant(tenant);
    apFirmwareUpgradeRequest.setVenueId(venue.getId());
    apFirmwareUpgradeRequest.setSerialNumber(ap.getId());
    apFirmwareUpgradeRequest.setModel(ap.getModel());
    apFirmwareUpgradeRequest.setSourceVersion(DEFAULT_VERSION);
    apFirmwareUpgradeRequest.setTargetVersion(DEFAULT_VERSION);
    return apFirmwareUpgradeRequest;
  }

  private VenueFirmwareVersion newVenueFirmwareVersion(Venue venue, ApVersion version, String branchType) {
    VenueFirmwareVersion vfv = new VenueFirmwareVersion();
    vfv.setId(venue.getId()+branchType);
    vfv.setVenue(venue);
    vfv.setTenant(venue.getTenant());
    vfv.setBranchType(branchType);
    vfv.setCurrentFirmwareVersion(version);
    return vfv;
  }
}
