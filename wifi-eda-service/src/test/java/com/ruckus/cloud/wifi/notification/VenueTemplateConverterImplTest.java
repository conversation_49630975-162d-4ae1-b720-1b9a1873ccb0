package com.ruckus.cloud.wifi.notification;

import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.notification.VenueApCountExecutorFactory.VenueAllApCountExecutor;
import com.ruckus.cloud.wifi.notification.VenueApCountExecutorFactory.VenueImpactApCountByMappingExecutor;
import com.ruckus.cloud.wifi.notification.VenueApCountExecutorFactory.VenueImpactApCountByModelsExecutor;
import com.ruckus.cloud.wifi.notification.dto.VenueTemplateDto;
import com.ruckus.cloud.wifi.notification.template.VenueTemplateStatusEnum;
import com.ruckus.cloud.wifi.notification.utils.UpgradeFirmwareNotificationUtil;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.ScheduleTimeSlotRepository;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueScheduleTimeSlotProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.HashMap;
import lombok.Builder;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.tuple;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WifiUnitTest
class VenueTemplateConverterImplTest {

  @SpyBean
  private VenueTemplateConverter venueTemplateConverter;

  @MockBean
  private ApRepository apRepository;

  @MockBean
  private ScheduleTimeSlotRepository scheduleTimeSlotRepository;

  @SpyBean
  private VenueTemplateDtoBuilderHelper venueTemplateDtoBuilderHelper;

  @MockBean
  private ExtendedVenueServiceCtrl venueServiceCtrl;

  @MockBean
  private VenueApCountExecutorFactory apCountExecutorFactory;

  @MockBean
  private VenueAllApCountExecutor venueAllApCountExecutor;

  @MockBean
  private VenueImpactApCountByModelsExecutor venueImpactApCountByModelsExecutor;

  @MockBean
  private VenueImpactApCountByMappingExecutor venueImpactApCountByMappingExecutor;

  @Test
  void testToTemplateWithoutFF() {
    // Given
    when(apRepository.findVenueApCountByVenueIds(any())).thenReturn(List.of());
    Tenant tenant = new Tenant("tenantId-1");
    Venue venue = getVenue(tenant, "venue-1", "Asia/Taipei");

    // When
    VenueTemplateDto result = venueTemplateConverter.toTemplate(venue);

    // Then
    assertThat(result).isNotNull().matches(dto -> dto.getNumberOfAps() == 0);
  }

  @Test
  void testToTemplateWithStatus() {
    // Given
    when(apRepository.findVenueApCountByVenueIds(any())).thenReturn(List.of());
    Tenant tenant = new Tenant("tenantId-1");
    Venue venue = getVenue(tenant, "venue-1", "Asia/Taipei");

    VenueTemplateDto result =
        venueTemplateConverter.toTemplate(venue, VenueTemplateStatusEnum.SUCCESS);

    assertThat(result)
        .isNotNull()
        .matches(dto -> dto.getStatus().equals(VenueTemplateStatusEnum.SUCCESS))
        .matches(dto -> dto.getAddress().equals(venue.getAddressLine()));

    result = venueTemplateConverter.toTemplate(venue, VenueTemplateStatusEnum.FAILED);
    assertThat(result)
        .isNotNull()
        .matches(dto -> dto.getStatus().equals(VenueTemplateStatusEnum.FAILED))
        .matches(dto -> dto.getAddress().equals(venue.getAddressLine()));
  }

  @Test
  void groupVenueTemplateByTimeSlot() {
    // Given
    Tenant tenant = new Tenant("tenantId-1");
    Venue venue = getVenue(tenant,"venue1", "Asia/Taipei");
    Venue venue2 = getVenue(tenant, "venue2","Asia/Taipei");
    Map<String, Integer> venueIdApCountMapping = mockVenueApCount(venue, 1, venue2, 3);
    when(apCountExecutorFactory.createAllApCountExecutor(any()))
        .thenReturn(venueAllApCountExecutor);
    when(venueAllApCountExecutor.execute())
        .thenReturn(venueIdApCountMapping);

    ScheduleTimeSlot sts = getScheduleTimeSlot("timeSlot1", new Date(), new Date());
    List<VenueScheduleTimeSlotProjection> venueScheduleTimeSlotProjections = new ArrayList<>(List.of(
        new MockVenueScheduleTimeSlotProjection(venue.getId(), sts.getId(), sts.getStartDateTime(), sts.getEndDateTime()),
        new MockVenueScheduleTimeSlotProjection(venue2.getId(), sts.getId(), sts.getStartDateTime(), sts.getEndDateTime())
    ));
    when(scheduleTimeSlotRepository.findVenueScheduleTimeSlotByVenueIdIn(any()))
        .thenReturn(venueScheduleTimeSlotProjections);

    Map<String, List<VenueTemplateDto>> result =
        venueTemplateConverter.groupVenueTemplateByTimeSlot(List.of(venue, venue2), "");

    assertThat(result).hasSize(1);
    assertThat(result.values())
        .flatExtracting(dto -> dto)
        .hasSize(2)
        .extracting(VenueTemplateDto::getName, VenueTemplateDto::getNumberOfAps)
        .containsExactlyInAnyOrder(
            tuple(venue.getName(), 1),
            tuple(venue2.getName(), 3));
  }

  @Test
  void groupVenueTemplateByTimeSlot_whenExistMultiTimeSlot() {
    // Given
    Tenant tenant = new Tenant("tenantId-1");
    Venue venue = getVenue(tenant,"venue1", "Asia/Taipei");
    Venue venue2 = getVenue(tenant, "venue2","PST");
    Venue venue3 = getVenue(tenant, "venue3","Asia/Taipei");
    Map<String, Integer> venueIdApCountMapping = mockVenueApCount(
        venue, 1, venue2, 3, venue3, 4);
    when(apCountExecutorFactory.createAllApCountExecutor(any()))
        .thenReturn(venueAllApCountExecutor);
    when(venueAllApCountExecutor.execute())
        .thenReturn(venueIdApCountMapping);

    Date yesterday = new Date(new Date().getTime() - 86400000);
    ScheduleTimeSlot timeSlot1 = getScheduleTimeSlot("timeSlot1", new Date(), new Date());
    ScheduleTimeSlot timeSlot2 = getScheduleTimeSlot("timeSlot2", yesterday, yesterday);
    List<VenueScheduleTimeSlotProjection> venueScheduleTimeSlotProjections = new ArrayList<>(List.of(
        new MockVenueScheduleTimeSlotProjection(venue.getId(), timeSlot1.getId(), timeSlot1.getStartDateTime(), timeSlot1.getEndDateTime()),
        new MockVenueScheduleTimeSlotProjection(venue2.getId(), timeSlot1.getId(), timeSlot1.getStartDateTime(), timeSlot1.getEndDateTime()),
        new MockVenueScheduleTimeSlotProjection(venue3.getId(), timeSlot2.getId(), timeSlot2.getStartDateTime(), timeSlot2.getEndDateTime())
    ));
    when(scheduleTimeSlotRepository.findVenueScheduleTimeSlotByVenueIdIn(any()))
        .thenReturn(venueScheduleTimeSlotProjections);

    Map<String, List<VenueTemplateDto>> result =
        venueTemplateConverter.groupVenueTemplateByTimeSlot(List.of(venue, venue2, venue3), "");

    assertThat(result).hasSize(2);
    assertThat(result.values())
        .flatExtracting(dto -> dto)
        .hasSize(3)
        .extracting(VenueTemplateDto::getName, VenueTemplateDto::getNumberOfAps)
        .containsExactlyInAnyOrder(
            tuple(venue.getName(), 1),
            tuple(venue2.getName(), 3),
            tuple(venue3.getName(), 4));
  }

  @Test
  void groupVenueTemplateByTimeSlotWithPreviousTimeSlotStrMap() {
    VenueTemplateDto dto = mockVenueTemplateDto("venue1", "Asia/Taipei");
    VenueTemplateDto dto2 = mockVenueTemplateDto("venue2", "Asia/Taipei");
    VenueTemplateDto dto3 = mockVenueTemplateDto("venue3", "America/New_York");

    Date yesterday = new Date(new Date().getTime() - 86400000);
    ScheduleTimeSlot timeSlot1 = getScheduleTimeSlot("timeSlot1", new Date(), new Date());
    ScheduleTimeSlot timeSlot2 = getScheduleTimeSlot("timeSlot2", yesterday, yesterday);
    List<VenueScheduleTimeSlotProjection> venueScheduleTimeSlotProjections = new ArrayList<>(
        List.of(
            new MockVenueScheduleTimeSlotProjection(dto.getId(), timeSlot1.getId(),
                timeSlot1.getStartDateTime(), timeSlot1.getEndDateTime()),
            new MockVenueScheduleTimeSlotProjection(dto2.getId(), timeSlot1.getId(),
                timeSlot1.getStartDateTime(), timeSlot1.getEndDateTime()),
            new MockVenueScheduleTimeSlotProjection(dto3.getId(), timeSlot2.getId(),
                timeSlot2.getStartDateTime(), timeSlot2.getEndDateTime())
        ));
    when(scheduleTimeSlotRepository.findVenueScheduleTimeSlotByVenueIdIn(any()))
        .thenReturn(venueScheduleTimeSlotProjections);

    Map<String, List<VenueTemplateDto>> result =
        venueTemplateConverter.groupVenueTemplateByTimeSlot(
            Map.of(dto.getId(), "time1", dto2.getId(), "time1", dto3.getId(), "time2"),
            List.of(dto, dto2, dto3));

    assertThat(result).hasSize(2).extractingByKey(
            UpgradeFirmwareNotificationUtil.processTemplateDate(timeSlot1.getStartDateTime(),
                timeSlot1.getEndDateTime()) + "@@time1")
        .matches(dtos -> dtos.contains(dto) && dtos.contains(dto2));
  }

  @Test
  void testToTemplateByApModel() {
    // Given
    when(apRepository.findVenueApCountByVenueIds(any())).thenReturn(List.of());
    Tenant tenant = new Tenant("tenantId-1");
    Venue venue = getVenue(tenant, "venue-1", "Asia/Taipei");

    Map<String, Integer> venueApCountMap = Map.of(venue.getId(), 2);

    when(apCountExecutorFactory.createImpactApCountByModelsExecutor(any()))
        .thenReturn(venueImpactApCountByModelsExecutor);
    when(venueImpactApCountByModelsExecutor.execute())
        .thenReturn(venueApCountMap);

    when(apCountExecutorFactory.createImpactApCountByMappingExecutor(any()))
        .thenReturn(venueImpactApCountByMappingExecutor);
    when(venueImpactApCountByMappingExecutor.execute())
        .thenReturn(venueApCountMap);

    List<VenueTemplateDto> results = venueTemplateConverter.toTemplateByApModels(List.of(venue));
    assertThat(results)
        .isNotNull().element(0)
        .matches(dto -> dto.getImpactedNumberOfAps() == 2)
        .matches(dto -> dto.getAddress().equals(venue.getAddressLine()));

    results = venueTemplateConverter.toTemplateByApModels(List.of(venue), venueApCountMap);
    assertThat(results)
        .isNotNull().element(0)
        .matches(dto -> dto.getImpactedNumberOfAps() == 2)
        .matches(dto -> dto.getAddress().equals(venue.getAddressLine()));

    results = venueTemplateConverter.toTemplateByApModels(List.of(venue),
        VenueTemplateStatusEnum.SUCCESS, Map.of());

    assertThat(results)
        .isNotNull().element(0)
        .matches(dto -> dto.getStatus().equals(VenueTemplateStatusEnum.SUCCESS))
        .matches(dto -> dto.getImpactedNumberOfAps() == 2)
        .matches(dto -> dto.getAddress().equals(venue.getAddressLine()));
  }

  private VenueTemplateDto mockVenueTemplateDto(String id, String timezone) {
    VenueTemplateDto venueTemplateDto = new VenueTemplateDto();
    venueTemplateDto.setId(id);
    venueTemplateDto.setTimeZone(timezone);
    return venueTemplateDto;
  }

  private Venue getVenue(Tenant tenant, String id, String timezone) {
    Venue venue = new Venue(id);
    venue.setName(id);
    venue.setTenant(tenant);
    venue.setTimezone(timezone);
    venue.setAddressLine("address-1");
    return venue;
  }

  private ScheduleTimeSlot getScheduleTimeSlot(String id, Date startDate, Date endDate) {
    ScheduleTimeSlot res = new ScheduleTimeSlot(id);
    res.setStartDateTime(startDate);
    res.setEndDateTime(endDate);
    return res;
  }

  private Map<String, Integer> mockVenueApCount(Venue venue1, int apCount1, Venue venue2, int apCount2) {
    Map<String, Integer> mapping = new HashMap<>();
    mapping.put(venue1.getId(), apCount1);
    mapping.put(venue2.getId(), apCount2);
    return mapping;
  }

  private Map<String, Integer> mockVenueApCount(Venue venue1, int apCount1, Venue venue2, int apCount2,
      Venue venue3, int apCount3) {
    Map<String, Integer> mapping = new HashMap<>();
    mapping.put(venue1.getId(), apCount1);
    mapping.put(venue2.getId(), apCount2);
    mapping.put(venue3.getId(), apCount3);
    return mapping;
  }

  @Builder
  static class MockVenueScheduleTimeSlotProjection implements VenueScheduleTimeSlotProjection {
    private String venueId;
    private String scheduleTimeSlotId;
    private Date startDateTime;
    private Date endDateTime;

    @Override
    public String getVenueId() {
      return venueId;
    }

    @Override
    public String getScheduleTimeSlotId() {
      return scheduleTimeSlotId;
    }

    @Override
    public Date getStartDateTime() {
      return startDateTime;
    }

    @Override
    public Date getEndDateTime() {
      return endDateTime;
    }
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    @ConditionalOnMissingBean
    public VenueTemplateConverter venueTemplateConverter(
        ScheduleTimeSlotRepository scheduleTimeSlotRepository,
        VenueTemplateDtoBuilderHelper venueTemplateDtoBuilderHelper,
        VenueApCountExecutorFactory apCountExecutorFactory) {
      return new VenueTemplateConverterImpl(
          scheduleTimeSlotRepository,
          venueTemplateDtoBuilderHelper,
          apCountExecutorFactory);
    }

    @Bean
    @ConditionalOnMissingBean
    public VenueTemplateDtoBuilderHelper venueTemplateDtoBuilderHelper() {
      return new VenueTemplateDtoBuilderHelper();
    }

    @Bean
    @ConditionalOnMissingBean
    public VenueTemplateDtoUtil venueTemplateDtoUtil(
        ExtendedVenueServiceCtrl venueServiceCtrl,
        VenueApCountExecutorFactory venueApCountExecutorFactory) {
      return new VenueTemplateDtoUtil(
          venueServiceCtrl,
          venueApCountExecutorFactory);
    }
  }
}
