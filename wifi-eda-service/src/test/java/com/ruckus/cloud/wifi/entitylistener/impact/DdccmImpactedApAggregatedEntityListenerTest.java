package com.ruckus.cloud.wifi.entitylistener.impact;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.activity.ActivityImpactDeviceService;
import com.ruckus.cloud.wifi.core.hibernate.entitylistener.AggregatedEntityListener;
import com.ruckus.cloud.wifi.core.model.EntityAction;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.entitylistener.EntityListenerEndpoint;
import com.ruckus.cloud.wifi.entitylistener.ddccm.DdccmAggregatedEntityListener;
import com.ruckus.cloud.wifi.kafka.publisher.FranzDeviceStatusPublisher;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
public class DdccmImpactedApAggregatedEntityListenerTest {

  @MockBean
  private FranzDeviceStatusPublisher franzDeviceStatusPublisher;

  @SpyBean
  private DdccmImpactedApAggregatedEntityListener ddccmImpactedApAggregatedEntityListener;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Test
  public void testShouldBuildWhenParentFlush_parentIsDdccmAndFlushed() {
    doReturn(List.of("123")).when(ddccmImpactedApAggregatedEntityListener)
        .bulkBuild(anyList(), anyList(), anyList(), any(TxChangesReader.class), any(), any());
    AggregatedEntityListener mockAggregatedEntityListener = mock(AggregatedEntityListener.class);
    doReturn(EntityListenerEndpoint.DDCCM.name()).when(mockAggregatedEntityListener).getEndpoint();

    List<String> result = ddccmImpactedApAggregatedEntityListener.buildFromParent(
        new DdccmAggregatedEntityListener(Collections.emptyList()), List.of(new Object()),
        List.of(new TxEntity<>(new Ap("123"), EntityAction.ADD)),
        Collections.emptyList(), Collections.emptyList(), emptyTxChanges(), null, null);

    assertFalse(result.isEmpty());
  }

  @Test
  public void testBuildFromWhenParentFlush_parentIsDdccmAndNotFlushed() {
    doReturn(List.of("123")).when(ddccmImpactedApAggregatedEntityListener)
        .bulkBuild(anyList(), anyList(), anyList(), any(TxChangesReader.class), any(), any());
    AggregatedEntityListener mockAggregatedEntityListener = mock(AggregatedEntityListener.class);
    doReturn(EntityListenerEndpoint.DDCCM.name()).when(mockAggregatedEntityListener).getEndpoint();

    List<String> result = ddccmImpactedApAggregatedEntityListener.buildFromParent(
        new DdccmAggregatedEntityListener(Collections.emptyList()), Collections.emptyList(),
        List.of(new TxEntity<>(new Ap("123"), EntityAction.ADD)),
        Collections.emptyList(), Collections.emptyList(), emptyTxChanges(), null, null);

    assertTrue(result.isEmpty());
  }

  @Test
  public void testShouldBuildWhenParentFlush_parentIsNotDdccm() {
    doReturn(List.of("123")).when(ddccmImpactedApAggregatedEntityListener)
        .bulkBuild(anyList(), anyList(), anyList(), any(TxChangesReader.class), any(), any());
    AggregatedEntityListener mockAggregatedEntityListener = mock(AggregatedEntityListener.class);
    doReturn(EntityListenerEndpoint.DRS.name()).when(mockAggregatedEntityListener).getEndpoint();

    List<String> result = ddccmImpactedApAggregatedEntityListener.buildFromParent(
        mockAggregatedEntityListener, Collections.emptyList(),
        List.of(new TxEntity<>(new Ap("123"), EntityAction.ADD)),
        Collections.emptyList(), Collections.emptyList(), emptyTxChanges(), null, null);

    assertFalse(result.isEmpty());
  }

  @Test
  public void testFlush_publishApplyingConfigStat() {
    List<String> serialNumbers = List.of(randomSerialNumber(), randomSerialNumber());

    ddccmImpactedApAggregatedEntityListener.flush(serialNumbers);

    verify(franzDeviceStatusPublisher, times(1)).publishApplyingConfig(
        argThat(aps -> aps.containsAll(serialNumbers)));
  }

  @Test
  public void testFlush_doNotPublishApplyingConfigStat_whenNoImpact() {
    ddccmImpactedApAggregatedEntityListener.flush(Collections.emptyList());

    verify(franzDeviceStatusPublisher, never()).publishApplyingConfig(anyList());
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public DdccmImpactedApAggregatedEntityListener ddccmImpactedApAggregatedEntityListener(
        FranzDeviceStatusPublisher franzDeviceStatusPublisher,
        FeatureFlagService featureFlagService) {
      return new DdccmImpactedApAggregatedEntityListener(franzDeviceStatusPublisher,
          featureFlagService);
    }

    @MockBean
    private ActivityImpactDeviceService activityImpactDeviceService;


  }
}
