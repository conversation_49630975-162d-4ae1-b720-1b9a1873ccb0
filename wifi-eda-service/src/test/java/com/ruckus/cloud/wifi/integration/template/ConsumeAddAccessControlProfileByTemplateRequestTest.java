package com.ruckus.cloud.wifi.integration.template;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ACCESS_CONTROL_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.ACTIVITY_PLAN_BYTES;
import static com.ruckus.cloud.wifi.cfg.activity.Constant.INSTANCE_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.handler.cfgrequest.template.operation.TemplateCreateInstanceOperation.newViewProperty;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TARGET_TENANT_ID;
import static com.ruckus.cloud.wifi.service.impl.InternalTemplateServiceCtrlImpl.TEMPLATE_ID;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.utils.TemplateUtils.STEP_ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_INSTANCE_POST;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.activity.protobuf.execution.plan.ActivityPlan.ActivityExecutionPlan;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.activity.plan.ExecutionPlanBuilder;
import com.ruckus.cloud.wifi.activity.plan.WifiActivityPlan;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanEntityTypes;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicyRule;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3Rule;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.viewmodel.TemplateInstanceCreateRequest;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.AccessControlProfileRepository;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.ApplicationPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.DevicePolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.L2AclPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.L3AclPolicyTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@WifiIntegrationTest
public class ConsumeAddAccessControlProfileByTemplateRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private AccessControlProfileRepository accessControlProfileRepository;
  @Autowired
  private ExecutionPlanBuilder executionPlanBuilder;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void add_accessControlProfile_without_policies(Tenant mspTenant, AccessControlProfile profile) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AccessControlProfile Template
    profile.setIsTemplate(true);
    profile.setApplicationPolicy(null);
    profile.setDevicePolicy(null);
    profile.setDevicePolicyEnable(false);
    profile.setApplicationPolicyEnable(false);
    repositoryUtil.createOrUpdate(profile, mspTenant.getId(), randomTxId());

    // check inserted into DB
    AccessControlProfile profileTemplateFromDB =
        repositoryUtil.find(AccessControlProfile.class, profile.getId(), mspTenantId, true);
    assertAccessControlProfile(profile, profileTemplateFromDB);

    // create ec access control set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_ACCESS_CONTROL_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.ACCESS_CONTROL_PROFILE, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, profileTemplateFromDB.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE,
        ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_ACCESS_CONTROL_PROFILE, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_INSTANCE_POST, mspTenantId);
    assertActivityStatusSuccess(ADD_ACCESS_CONTROL_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, mspTenantId);

    AccessControlProfile ecProfile = accessControlProfileRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecProfile.getId());
    assertEquals(profileTemplateFromDB.getId(), ecProfile.getTemplateId());
    assertEquals(profileTemplateFromDB.getUpdatedDate().getTime(), ecProfile.getTemplateVersion());

    var viewOps = receiveViewmodelCollectorOperations(1, ecTenantId);
    assertAll("assert 1 viewmodel ops: AccessControlProfile",
        () -> assertViewmodelOps(viewOps, 1),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME,
            ecProfile.getId(), ecProfile.getName())
    );
  }

  @Test

  public void add_accessControlProfile_with_policies(Tenant mspTenant, @Template AccessControlProfile profile) throws InterruptedException {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AccessControlProfile Template with Application and Device Policy Template
    addMspPolicyToProfile(mspTenant, profile);

    // check inserted into DB
    AccessControlProfile profileTemplateFromDB =
        repositoryUtil.find(AccessControlProfile.class, profile.getId(), mspTenantId, true);
    assertAccessControlProfile(profile, profileTemplateFromDB);

    // create ec access control set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_ACCESS_CONTROL_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.ACCESS_CONTROL_PROFILE, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, profileTemplateFromDB.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE,
        ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_ACCESS_CONTROL_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_ACCESS_CONTROL_PROFILE, ecTenantId);
    assertActivityStatusSuccess(ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, mspTenantId);
    assertActivityStatusSuccess(STEP_ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_INSTANCE_POST, ecTenantId);
    assertActivityStatusSuccess(STEP_ADD_ACCESS_CONTROL_PROFILE_TEMPLATE_INSTANCE_POST, mspTenantId);

    // check policy rule in ec profile not override MSP's rule
    AccessControlProfile mspProfile =
        repositoryUtil.find(AccessControlProfile.class, profile.getId(), mspTenantId, true);
    AccessControlProfile ecProfile = accessControlProfileRepository.getByIdAndTenantId(instanceId, ecTenantId);
    assertEquals(instanceId, ecProfile.getId());
    assertEquals(profileTemplateFromDB.getId(), ecProfile.getTemplateId());
    assertEquals(profileTemplateFromDB.getUpdatedDate().getTime(), ecProfile.getTemplateVersion());

    List<ApplicationPolicyRule> mspApplicationPolicyRules = mspProfile.getApplicationPolicy().getRules();
    List<DevicePolicyRule> mspDevicePolicyRules = mspProfile.getDevicePolicy().getRules();
    List<L3Rule> mspL3Rules = mspProfile.getL3AclPolicy().getL3Rules();
    List<ApplicationPolicyRule> ecApplicationPolicyRules = ecProfile.getApplicationPolicy().getRules();
    List<DevicePolicyRule> ecDevicePolicyRules = ecProfile.getDevicePolicy().getRules();
    List<L3Rule> ecL3Rules = ecProfile.getL3AclPolicy().getL3Rules();

    assertEquals(mspApplicationPolicyRules.size(), ecApplicationPolicyRules.size());
    assertEquals(mspDevicePolicyRules.size(), ecDevicePolicyRules.size());
    assertEquals(mspL3Rules.size(), ecL3Rules.size());

    // check policy rule id and tenant in ec profile is different from MSP
    assertNotEquals(mspApplicationPolicyRules.stream().map(ApplicationPolicyRule::getId).collect(Collectors.toSet()),
        ecApplicationPolicyRules.stream().map(ApplicationPolicyRule::getId).collect(Collectors.toSet()));
    assertNotEquals(mspDevicePolicyRules.stream().map(DevicePolicyRule::getId).collect(Collectors.toSet()),
        ecDevicePolicyRules.stream().map(DevicePolicyRule::getId).collect(Collectors.toSet()));
    assertNotEquals(mspL3Rules.stream().map(L3Rule::getId).collect(Collectors.toSet()),
        ecL3Rules.stream().map(L3Rule::getId).collect(Collectors.toSet()));

    var viewOps = receiveViewmodelCollectorOperations(9, ecTenantId);
    assertAll("assert 9 viewmodel ops: ApplicationPolicy, DevicePolicy, L2Policy, L3Policy, AccessControlProfile",
        () -> assertViewmodelOps(viewOps, 9),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME,
            ecProfile.getApplicationPolicy().getId(), ecProfile.getApplicationPolicy().getName()),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME,
            ecProfile.getDevicePolicy().getId(), ecProfile.getDevicePolicy().getName()),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME,
            ecProfile.getL2AclPolicy().getId(), ecProfile.getL2AclPolicy().getName()),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME,
            ecProfile.getL3AclPolicy().getId(), ecProfile.getL3AclPolicy().getName()),
        () -> assertViewmodelCollector(viewOps, OpType.ADD, ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME,
            ecProfile.getId(), ecProfile.getName()),
        // activate policy
        () -> assertAccessControlWithPolicyViewmodelCollector(viewOps, OpType.MOD, ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME,
            ecProfile.getId(), ecProfile.getApplicationPolicy()),
        () -> assertAccessControlWithPolicyViewmodelCollector(viewOps, OpType.MOD, ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME,
            ecProfile.getId(), ecProfile.getDevicePolicy()),
        () -> assertAccessControlWithPolicyViewmodelCollector(viewOps, OpType.MOD, ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME,
            ecProfile.getId(), ecProfile.getL2AclPolicy()),
        () -> assertAccessControlWithPolicyViewmodelCollector(viewOps, OpType.MOD, ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME,
            ecProfile.getId(), ecProfile.getL3AclPolicy())
    );
  }

  private void addMspPolicyToProfile(Tenant mspTenant, AccessControlProfile profile) {
    DevicePolicy devicePolicy = DevicePolicyTestFixture
        .randomDevicePolicy(mspTenant,(o) -> {o.setIsTemplate(true);});
    ApplicationPolicy applicationPolicy = ApplicationPolicyTestFixture
        .randomApplicationPolicy(mspTenant, (o) -> {o.setIsTemplate(true);});
    L2AclPolicy l2AclPolicy = L2AclPolicyTestFixture.randomL2AclPolicy(mspTenant, (o) -> {o.setIsTemplate(true);});
    L3AclPolicy l3AclPolicy = L3AclPolicyTestFixture.randomL3AclPolicy(mspTenant, (o) -> {o.setIsTemplate(true);});
    profile.setIsTemplate(true);
    profile.setApplicationPolicy(applicationPolicy);
    profile.setApplicationPolicyEnable(true);
    profile.setDevicePolicy(devicePolicy);
    profile.setDevicePolicyEnable(true);
    profile.setL2AclPolicy(l2AclPolicy);
    profile.setL2AclEnable(true);
    profile.setL3AclPolicy(l3AclPolicy);
    profile.setL3AclEnable(true);

    repositoryUtil.createOrUpdate(applicationPolicy, mspTenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(devicePolicy, mspTenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(l2AclPolicy, mspTenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(l3AclPolicy, mspTenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(profile, mspTenant.getId(), randomTxId());
  }

  @Test
  public void ec_add_access_control_fail_then_msp_activity_should_fail(Tenant mspTenant, AccessControlProfile profile) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AccessControlProfile Template
    profile.setIsTemplate(true);
    profile.setApplicationPolicy(null);
    profile.setDevicePolicy(null);
    profile.setDevicePolicyEnable(false);
    profile.setApplicationPolicyEnable(false);
    profile.setName("test-profile");
    repositoryUtil.createOrUpdate(profile, mspTenant.getId(), randomTxId());

    // check inserted into DB
    AccessControlProfile profileTemplateFromDB =
        repositoryUtil.find(AccessControlProfile.class, profile.getId(), mspTenantId, true);
    assertAccessControlProfile(profile, profileTemplateFromDB);

    // profile already in ec tenant before apply template
    AccessControlProfile existedEcProfile = AccessControlProfileTestFixture.randomAccessControlProfile();
    existedEcProfile.setName("test-profile");
    repositoryUtil.createOrUpdate(existedEcProfile, ecTenant.getId(), randomTxId());

    // create ec access control set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_ACCESS_CONTROL_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.ACCESS_CONTROL_PROFILE, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, profileTemplateFromDB.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE,
        ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_ACCESS_CONTROL_PROFILE, ecTenantId);
    assertActivityStatusFail(ADD_ACCESS_CONTROL_PROFILE, ecTenantId);
    assertActivityStatusFail(ApiFlowNames.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  @Disabled("Enable When policies change to post step version")
  @Test
  public void ec_add_sub_policy_fail_then_msp_activity_should_fail(Tenant mspTenant, AccessControlProfile profile) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AccessControlProfile Template with Application and Device Policy Template
    DevicePolicy devicePolicy = DevicePolicyTestFixture
        .randomDevicePolicy(mspTenant,(o) -> {
          o.setIsTemplate(true);
          o.setName("test-policy");
        });
    ApplicationPolicy applicationPolicy = ApplicationPolicyTestFixture
        .randomApplicationPolicy(mspTenant, (o) -> {o.setIsTemplate(true);});
    profile.setIsTemplate(true);
    profile.setApplicationPolicy(applicationPolicy);
    profile.setDevicePolicy(devicePolicy);

    repositoryUtil.createOrUpdate(applicationPolicy, mspTenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(devicePolicy, mspTenant.getId(), randomTxId());
    repositoryUtil.createOrUpdate(profile, mspTenant.getId(), randomTxId());

    // check inserted into DB
    AccessControlProfile profileTemplateFromDB =
        repositoryUtil.find(AccessControlProfile.class, profile.getId(), mspTenantId, true);
    assertAccessControlProfile(profile, profileTemplateFromDB);

    // policy already in ec tenant before apply template
    DevicePolicy existedEcPolicy = DevicePolicyTestFixture
        .randomDevicePolicy(mspTenant,(o) -> {o.setName("test-policy");});
    existedEcPolicy.setName("test-policy");
    repositoryUtil.createOrUpdate(existedEcPolicy, ecTenant.getId(), randomTxId());

    // create ec access control set by msp template
    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_ACCESS_CONTROL_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.ACCESS_CONTROL_PROFILE, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, profileTemplateFromDB.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE,
        ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, userName, requestParams, new TemplateInstanceCreateRequest());

    assertActivityPlan(ADD_ACCESS_CONTROL_PROFILE, ecTenantId);
    assertActivityStatusFail(ADD_ACCESS_CONTROL_PROFILE, ecTenantId);
    assertActivityStatusFail(ApiFlowNames.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  @Test
  public void ec_add_access_control_fail_msp_should_get_activity_fail_because_incorrect_overrides(Tenant mspTenant,
      AccessControlProfile profile) {
    var mspTenantId = mspTenant.getId();
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {});
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId(), randomTxId());

    // msp tenant add AccessControlProfile Template
    profile.setIsTemplate(true);
    profile.setApplicationPolicy(null);
    profile.setDevicePolicy(null);
    profile.setDevicePolicyEnable(false);
    profile.setApplicationPolicyEnable(false);
    profile.setName("test-profile");
    repositoryUtil.createOrUpdate(profile, mspTenant.getId(), randomTxId());

    // check inserted into DB
    AccessControlProfile profileTemplateFromDB =
        repositoryUtil.find(AccessControlProfile.class, profile.getId(), mspTenantId, true);
    assertAccessControlProfile(profile, profileTemplateFromDB);

    // create ec access control set by msp template
    TemplateInstanceCreateRequest instanceCreateRequest = new TemplateInstanceCreateRequest();
    instanceCreateRequest.setOverrides(List.of(newViewProperty("x", "x"))); // incorrect

    String instanceId = randomId();
    ActivityExecutionPlan activityExecutionPlan = buildActivityExecutionPlan(ADD_ACCESS_CONTROL_PROFILE,
        WifiActivityPlan.Action.ADD, ExecutionPlanEntityTypes.ACCESS_CONTROL_PROFILE, instanceId);
    String mspRequestId = randomTxId();

    RequestParams requestParams = new RequestParams()
        .addPathVariable(TEMPLATE_ID, profileTemplateFromDB.getId())
        .addPathVariable(TARGET_TENANT_ID, ecTenantId)
        .addRequestParam(INSTANCE_ID, instanceId)
        .addRequestParam(ACTIVITY_PLAN_BYTES, executionPlanBuilder.toByteArrayBase64(activityExecutionPlan));
    sendByTemplateWifiCfgRequest(mspTenantId, mspRequestId, CfgAction.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE,
        ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, userName, requestParams, instanceCreateRequest);

    assertActivityPlanNotSent(ecTenantId);
    assertActivityStatusFail(ApiFlowNames.ADD_ACCESS_CONTROL_PROFILE_BY_TEMPLATE, mspTenantId);
  }

  private static void assertViewmodelCollector(List<Operations> operations, OpType opType,
      String index, String id, String name) {
    assertTrue(operations.stream()
        .filter(o -> index.equals(o.getIndex()))
        .filter(o -> id.equals(o.getId()))
        .filter(o -> o.getOpType() == opType)
        .anyMatch(o -> name.equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue())));
  }

  private static void assertAccessControlWithPolicyViewmodelCollector(List<Operations> operations, OpType opType,
      String index, String id, Object object) {
    assertTrue(operations.stream()
        .filter(o -> index.equals(o.getIndex()))
        .filter(o -> id.equals(o.getId()))
        .filter(o -> o.getOpType() == opType)
        .anyMatch(o -> {
          if (object instanceof ApplicationPolicy applicationPolicy) {
            return applicationPolicy.getId().equals(o.getDocMap().get(EsConstants.Key.APPLICATION_POLICY_ID).getStringValue())
                && applicationPolicy.getName().equals(o.getDocMap().get(EsConstants.Key.APPLICATION_POLICY_NAME).getStringValue());
          } else if (object instanceof DevicePolicy devicePolicy) {
            return devicePolicy.getId().equals(o.getDocMap().get(EsConstants.Key.DEVICE_POLICY_ID).getStringValue())
                && devicePolicy.getName().equals(o.getDocMap().get(EsConstants.Key.DEVICE_POLICY_NAME).getStringValue());
          } else if (object instanceof L2AclPolicy l2AclPolicy) {
            return l2AclPolicy.getId().equals(o.getDocMap().get(EsConstants.Key.L2_ACL_POLICY_ID).getStringValue())
                && l2AclPolicy.getName().equals(o.getDocMap().get(EsConstants.Key.L2_ACL_POLICY_NAME).getStringValue());
          } else if (object instanceof L3AclPolicy l3AclPolicy) {
            return l3AclPolicy.getId().equals(o.getDocMap().get(EsConstants.Key.L3_ACL_POLICY_ID).getStringValue())
                && l3AclPolicy.getName().equals(o.getDocMap().get(EsConstants.Key.L3_ACL_POLICY_NAME).getStringValue());
          } else {
            return false;
          }
        }));
  }

  private void assertAccessControlProfile(AccessControlProfile expected, AccessControlProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getDescription(), actual.getDescription());

    if (expected.getApplicationPolicy() != null && expected.getApplicationPolicy().getId() != null) {
      assertEquals(expected.getApplicationPolicy().getId(), actual.getApplicationPolicy().getId());
    } else {
      assertNull(actual.getApplicationPolicy());
    }
    assertEquals(expected.getApplicationPolicyEnable(), actual.getApplicationPolicyEnable());

    if (expected.getDevicePolicy() != null && expected.getDevicePolicy().getId() != null) {
      assertEquals(expected.getDevicePolicy().getId(), actual.getDevicePolicy().getId());
    } else {
      assertNull(actual.getDevicePolicy());
    }
    assertEquals(expected.getDevicePolicyEnable(), actual.getDevicePolicyEnable());

    if (expected.getL2AclPolicy() != null && expected.getL2AclPolicy().getId() != null) {
      assertEquals(expected.getL2AclPolicy().getId(), actual.getL2AclPolicy().getId());
    } else {
      assertNull(actual.getL2AclPolicy());
    }
    assertEquals(expected.getL2AclEnable(), actual.getL2AclEnable());

    if (expected.getL3AclPolicy() != null && expected.getL3AclPolicy().getId() != null) {
      assertEquals(expected.getL3AclPolicy().getId(), actual.getL3AclPolicy().getId());
    } else {
      assertNull(actual.getL3AclPolicy());
    }
    assertEquals(expected.getL3AclEnable(), actual.getL3AclEnable());

    if (expected.getRateLimiting() != null) {
      assertEquals(expected.getRateLimiting().getDownlinkLimit(),  actual.getRateLimiting().getDownlinkLimit());
      assertEquals(expected.getRateLimiting().getUplinkLimit(), actual.getRateLimiting().getUplinkLimit());
      assertEquals(expected.getRateLimiting().getEnabled(), actual.getRateLimiting().getEnabled());
    } else {
      assertNull(actual.getRateLimiting());
    }
  }

  private void assertViewmodelOps(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }
}
