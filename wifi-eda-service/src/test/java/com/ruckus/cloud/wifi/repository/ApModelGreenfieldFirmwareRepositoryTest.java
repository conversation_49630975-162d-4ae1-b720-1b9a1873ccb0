package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO ap_version(id, supported_ap_models, category, created_date, updated_date) VALUES
    ('6.2.0.103.800', '["R500", "R510", "R610"]', 'RECOMMENDED', '2022-03-23 00:12:53.427', '2022-03-23 00:12:53.427'),
    ('6.2.0.103.1234', '[ "R500", "R510", "R610"]', 'RECOMMENDED', '2022-03-23 00:13:53.427', '2022-03-23 00:13:53.427'),
    ('6.2.2.103.434', '["R510","R610","R550"]', 'RECOMMENDED', '2022-03-23 00:14:53.427', '2022-03-23 00:14:53.427'),
    ('6.2.2.103.555', '["R510","R610","R550"]', 'RECOMMENDED', '2022-03-23 00:15:53.427', '2022-03-23 00:15:53.427'),
    ('6.2.4.103.983', null, 'RECOMMENDED', '2022-03-23 00:16:53.427', '2022-03-23 00:16:53.427'),
    ('7.0.0.103.300', '["R770"]', 'RECOMMENDED', '2022-03-23 00:17:53.427', '2022-03-23 00:17:53.427'),
    ('7.0.0.105.230', '["R550","T350:T350e","R770"]', 'RECOMMENDED', '2022-03-23 00:18:53.427', '2022-03-23 00:18:53.427'),
    ('6.2.0.103.123', '["R500","R600","R510","R610"]', 'RECOMMENDED', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
    ('6.2.2.103.556', '["R550","R650","R750","R770"]', 'RECOMMENDED', '2022-03-23 00:11:53.428', '2022-03-23 00:11:53.428'),
    ('7.0.0.999.100', '["R550","T350:T350e","R770"]', 'BETA', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427');
    INSERT INTO ap_model_greenfield_firmware(id, firmware) VALUES 
    ('R500', '6.2.0.103.800'),
    ('R550', '6.2.2.103.434'),
    ('R510', '6.2.2.103.434'),
    ('R770', '7.0.0.999.100');
    """)
public class ApModelGreenfieldFirmwareRepositoryTest {

  @Autowired
  private ApModelGreenfieldFirmwareRepository repository;

  @Test
  public void testGetMaximumFirmwareVersion() {
    assertThat(repository.getMaximumFirmwareVersion())
        .isNotEmpty()
        .get()
        .isEqualTo("7.0.0.999.100");
  }

  @Test
  public void testFindDistinctFirmwareVersions() {
    assertThat(repository.findDistinctFirmwareVersions())
        .hasSize(3)
        .containsExactlyInAnyOrder("6.2.0.103.800", "6.2.2.103.434", "7.0.0.999.100");
  }
}
