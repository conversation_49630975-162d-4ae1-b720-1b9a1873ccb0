package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.test.fixture.ApTestFixture.randomAp;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.MulticastDnsProxyServiceProfileAp;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.fixture.MulticaseDnsProxyTestFixture;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO venue (id, tenant) VALUES ('b1a2c59f92d243cba7ca5b4bfe83c289', '6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO ap_group (id, tenant, venue, is_default) VALUES
        ('753755ebcac84ce798ea5f3ee11b5731', '6700bc51acf84c4aa9510df2ca00b5f4', 'b1a2c59f92d243cba7ca5b4bfe83c289', TRUE),
        ('243280ce025f4def9ac45626271a077f', '6700bc51acf84c4aa9510df2ca00b5f4', 'b1a2c59f92d243cba7ca5b4bfe83c289', FALSE);
    INSERT INTO ap (id, tenant, ap_group, soft_deleted) VALUES
        ('983518622414', '6700bc51acf84c4aa9510df2ca00b5f4', '753755ebcac84ce798ea5f3ee11b5731', false),
        ('983518622415', '6700bc51acf84c4aa9510df2ca00b5f4', '753755ebcac84ce798ea5f3ee11b5731', false),
        ('454643635712', '6700bc51acf84c4aa9510df2ca00b5f4', '243280ce025f4def9ac45626271a077f', false);
    INSERT INTO multicast_dns_proxy_service_profile (id, tenant) VALUES ('34ffc50c45284c0a8a3ce5809d9c1c76', '6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO multicast_dns_proxy_service_profile_ap (id, tenant, ap, multicast_dns_proxy_service_profile) VALUES
        ('d888fd374a974e548af80a7ecbc74230', '6700bc51acf84c4aa9510df2ca00b5f4', '983518622414', '34ffc50c45284c0a8a3ce5809d9c1c76'),
        ('e619cccb6bd74075ba0b220611969aab', '6700bc51acf84c4aa9510df2ca00b5f4', '454643635712', '34ffc50c45284c0a8a3ce5809d9c1c76');
    """)
class MulticastDnsProxyServiceProfileRepositoryTest {

  private static final String TENANT_ID = "6700bc51acf84c4aa9510df2ca00b5f4";
  private static final String AP_GROUP_ID = "753755ebcac84ce798ea5f3ee11b5731";
  private static final String VENUE_ID = "b1a2c59f92d243cba7ca5b4bfe83c289";
  private static final String MULTICAST_DNS_PROXY_SERVICE_PROFILE_ID = "34ffc50c45284c0a8a3ce5809d9c1c76";

  @Autowired
  private MulticastDnsProxyServiceProfileRepository repository;
  @Autowired
  private MulticastDnsProxyServiceProfileApRepository profileAprepository;

  @Test
  void findByTenantIdAndVenueIdTest() {
    assertThat(repository.findByTenantIdAndVenueId(TENANT_ID, VENUE_ID))
        .isNotNull()
        .hasSize(1)
        .singleElement()
        .extracting(MulticastDnsProxyServiceProfile::getId)
        .isEqualTo(MULTICAST_DNS_PROXY_SERVICE_PROFILE_ID);
  }

  @Test
  void save() {
    MulticastDnsProxyServiceProfile multicastDnsProxyServiceProfile = MulticaseDnsProxyTestFixture.multicastDnsProxyServiceProfile("mDnsProfile");
    multicastDnsProxyServiceProfile.setId("34ffc50c45284c0a8a3ce5809d9c1c76");
    multicastDnsProxyServiceProfile.getRules().get(0).setTenant(new Tenant(TENANT_ID));
    Ap ap = randomAp();
    ap.setId("983518622415");
    ap.getApGroup().setId(AP_GROUP_ID);
    ap.getApGroup().getVenue().setId(VENUE_ID);
    ap.setTenant(new Tenant(TENANT_ID));

    MulticastDnsProxyServiceProfileAp mDnsAp = new MulticastDnsProxyServiceProfileAp();
    mDnsAp.setId(randomId());
    mDnsAp.setAp(ap);
    mDnsAp.setTenant(ap.getTenant());
    mDnsAp.setMulticastDnsProxyServiceProfile(multicastDnsProxyServiceProfile);
    multicastDnsProxyServiceProfile.setMulticastDnsProxyServiceProfileAps(List.of(new MulticastDnsProxyServiceProfileAp(mDnsAp.getId())));

    MulticastDnsProxyServiceProfile mDnsProfileFromRepo = repository.save(multicastDnsProxyServiceProfile);
    assertThat(mDnsProfileFromRepo)
            .isNotNull()
            .extracting(MulticastDnsProxyServiceProfile::getId)
            .isEqualTo(multicastDnsProxyServiceProfile.getId());
    assertThat(mDnsProfileFromRepo.getMulticastDnsProxyServiceProfileAps().get(0))
            .isNotNull()
            .extracting(MulticastDnsProxyServiceProfileAp::getId)
            .isEqualTo(mDnsAp.getId());
  }
}
