package com.ruckus.cloud.wifi.integration.l2aclpolicy;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.L2AclPolicyTestFixture.randomL2AclPolicy;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.api.rest.L2AclPolicyRestCtrl;
import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.stream.Stream;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeUpdateL2AclPolicyV1_1RequestTest {
  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeUpdateL2AclPolicyV1_1Message {
    @Test
    void givenPolicyNotExists(Tenant tenant) {
      final var l2AclPolicy =
          L2AclPolicyRestCtrl.L2AclPolicyMapper.INSTANCE.ServiceL2AclPolicy2L2AclPolicy(
              randomL2AclPolicy(tenant, p -> {}));

      assertThatThrownBy(
              () ->
                  messageUtil.sendWifiCfgRequest(
                      tenant.getId(),
                      txCtxExtension.getRequestId(),
                      CfgAction.UPDATE_L2ACL_POLICY_V1_1,
                      randomName(),
                      new RequestParams().addPathVariable("l2AclPolicyId", l2AclPolicy.getId()),
                      l2AclPolicy))
          .isNotNull()
          .getRootCause()
          .isInstanceOf(ObjectNotFoundException.class);

      messageCaptors.assertThat(
          messageCaptors.getDdccmMessageCaptor(),
          messageCaptors.getCmnCfgCollectorMessageCaptor()
      ).doesNotSendByTenant(tenant.getId());

      assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
          .isNotNull()
          .extracting(KafkaProtoMessage::getPayload)
          .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.FAIL)
          .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_L2ACL_POLICY));
    }

    @Nested
    class GivenPolicyExists {
      @Nested
      class GivenPolicyNameChanged {
        @Test
        void givenPolicyAlreadyActivatedOnSomeAccessControlProfiles(
            Tenant tenant, L2AclPolicy targetL2AclPolicy) {
          final var accessControlProfileIds =
              Stream.generate(AccessControlProfileTestFixture::randomAccessControlProfile)
                  .limit(3)
                  .peek(p -> p.setTenant(tenant))
                  .peek(p -> p.setL2AclPolicy(targetL2AclPolicy))
                  .peek(p -> p.setL2AclEnable(true))
                  .peek(p -> repositoryUtil.createOrUpdate(p, tenant.getId(), randomTxId()))
                  .map(AbstractBaseEntity::getId)
                  .toList();

          final var l2AclPolicy =
              L2AclPolicyRestCtrl.L2AclPolicyMapper.INSTANCE.ServiceL2AclPolicy2L2AclPolicy(
                  randomL2AclPolicy(tenant, p -> p.setId(targetL2AclPolicy.getId())));

          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.UPDATE_L2ACL_POLICY_V1_1,
              randomName(),
              new RequestParams().addPathVariable("l2AclPolicyId", l2AclPolicy.getId()),
              l2AclPolicy);

          assertThat(repositoryUtil.find(L2AclPolicy.class, l2AclPolicy.getId(), tenant.getId()))
              .isNotNull()
              .matches(p -> p.getName().equals(l2AclPolicy.getName()))
              .matches(p -> p.getAccess() == l2AclPolicy.getAccess())
              .matches(p -> p.getDescription().equals(l2AclPolicy.getDescription()))
              .extracting(
                  L2AclPolicy::getMacAddresses, InstanceOfAssertFactories.list(String.class))
              .hasSize(l2AclPolicy.getMacAddresses().size())
              .allMatch(
                  mac ->
                      l2AclPolicy.getMacAddresses().stream()
                          .anyMatch(s -> s.equalsIgnoreCase(mac)));

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .hasSize(1)
              .first()
              .matches(o -> o.getAction() == Action.MODIFY)
              .matches(o -> o.getId().equals(l2AclPolicy.getId()));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .hasSize(accessControlProfileIds.size() + 1)
              .allMatch(o -> o.getOpType() == OpType.MOD)
              .satisfies(
                  operations ->
                      assertThat(operations)
                          .filteredOn(
                              o ->
                                  o.getIndex()
                                      .equals(
                                          EsConstants.Index
                                              .ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME))
                          .hasSize(accessControlProfileIds.size())
                          .allMatch(
                              o ->
                                  o.getDocOrDefault(
                                          EsConstants.Key.L2_ACL_POLICY_ID,
                                          Value.getDefaultInstance())
                                      .getStringValue()
                                      .equals(l2AclPolicy.getId()))
                          .allMatch(
                              o ->
                                  o.getDocOrDefault(
                                          EsConstants.Key.L2_ACL_POLICY_NAME,
                                          Value.getDefaultInstance())
                                      .getStringValue()
                                      .equals(l2AclPolicy.getName()))
                          .extracting(Operations::getId)
                          .containsExactlyInAnyOrderElementsOf(accessControlProfileIds))
              .filteredOn(
                  o -> o.getIndex().equals(EsConstants.Index.ACCESS_CONTROL_SUB_PROFILE_INDEX_NAME))
              .singleElement()
              .matches(o -> o.getOpType() == OpType.MOD)
              .matches(o -> o.getId().equals(l2AclPolicy.getId()));

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_L2ACL_POLICY));
        }

        @Test
        void givenPolicyNotActivatedOnAccessControlProfile(
            Tenant tenant, L2AclPolicy targetL2AclPolicy) {
          final var l2AclPolicy =
              L2AclPolicyRestCtrl.L2AclPolicyMapper.INSTANCE.ServiceL2AclPolicy2L2AclPolicy(
                  randomL2AclPolicy(tenant, p -> p.setId(targetL2AclPolicy.getId())));

          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.UPDATE_L2ACL_POLICY_V1_1,
              randomName(),
              new RequestParams().addPathVariable("l2AclPolicyId", l2AclPolicy.getId()),
              l2AclPolicy);

          assertThat(repositoryUtil.find(L2AclPolicy.class, l2AclPolicy.getId(), tenant.getId()))
              .isNotNull()
              .matches(p -> p.getName().equals(l2AclPolicy.getName()))
              .matches(p -> p.getAccess() == l2AclPolicy.getAccess())
              .matches(p -> p.getDescription().equals(l2AclPolicy.getDescription()))
              .extracting(
                  L2AclPolicy::getMacAddresses, InstanceOfAssertFactories.list(String.class))
              .hasSize(l2AclPolicy.getMacAddresses().size())
              .allMatch(
                  mac ->
                      l2AclPolicy.getMacAddresses().stream()
                          .anyMatch(s -> s.equalsIgnoreCase(mac)));

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .hasSize(1)
              .first()
              .matches(o -> o.getAction() == Action.MODIFY)
              .matches(o -> o.getId().equals(l2AclPolicy.getId()));

          assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .hasSize(1)
              .first()
              .matches(o -> o.getOpType() == OpType.MOD)
              .matches(o -> o.getId().equals(l2AclPolicy.getId()));

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_L2ACL_POLICY));
        }
      }

      @Test
      void givenPolicyNameNotChanged(Tenant tenant, L2AclPolicy targetL2AclPolicy) {
        final var l2AclPolicy =
            L2AclPolicyRestCtrl.L2AclPolicyMapper.INSTANCE.ServiceL2AclPolicy2L2AclPolicy(
                randomL2AclPolicy(
                    tenant,
                    p -> {
                      p.setId(targetL2AclPolicy.getId());
                      p.setName(targetL2AclPolicy.getName());
                    }));

        messageUtil.sendWifiCfgRequest(
            tenant.getId(),
            txCtxExtension.getRequestId(),
            CfgAction.UPDATE_L2ACL_POLICY_V1_1,
            randomName(),
            new RequestParams().addPathVariable("l2AclPolicyId", l2AclPolicy.getId()),
            l2AclPolicy);

        assertThat(repositoryUtil.find(L2AclPolicy.class, l2AclPolicy.getId(), tenant.getId()))
            .isNotNull()
            .matches(p -> p.getName().equals(l2AclPolicy.getName()))
            .matches(p -> p.getAccess() == l2AclPolicy.getAccess())
            .matches(p -> p.getDescription().equals(l2AclPolicy.getDescription()))
            .extracting(L2AclPolicy::getMacAddresses, InstanceOfAssertFactories.list(String.class))
            .hasSize(l2AclPolicy.getMacAddresses().size())
            .allMatch(
                mac ->
                    l2AclPolicy.getMacAddresses().stream().anyMatch(s -> s.equalsIgnoreCase(mac)));

        assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(
                m -> m.getPayload().getOperationsList(),
                InstanceOfAssertFactories.list(Operation.class))
            .isNotEmpty()
            .hasSize(1)
            .first()
            .matches(o -> o.getAction() == Action.MODIFY)
            .matches(o -> o.getId().equals(l2AclPolicy.getId()));

        assertThat(messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .extracting(
                ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .hasSize(1)
            .first()
            .matches(o -> o.getOpType() == OpType.MOD)
            .matches(o -> o.getId().equals(l2AclPolicy.getId()));

        assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
            .isNotNull()
            .extracting(KafkaProtoMessage::getPayload)
            .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
            .matches(a -> a.getStep().equals(ApiFlowNames.UPDATE_L2ACL_POLICY));
      }
    }
  }
}
