package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO venue (id, tenant) VALUES ('b1a2c59f92d243cba7ca5b4bfe83c289', '6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO hotspot20operator (id, name, tenant) VALUES
        ('059796b87b054c359a9828a354aab591', 'hotspot20operator-1', '6700bc51acf84c4aa9510df2ca00b5f4'),
        ('059796b87b054c359a9828a354aab592', 'hotspot20operator-2', '6700bc51acf84c4aa9510df2ca00b5f4');
    INSERT INTO hotspot20friendly_name (id, tenant, name, language, operator) VALUES
        ('ec0a51f6bde14074b06dcdb2d6a18380', '6700bc51acf84c4aa9510df2ca00b5f4', 'friendlyName-1', 'ENG', '059796b87b054c359a9828a354aab591');
    """)
class Hotspot20OperatorRepositoryTest {

  private static final String TENANT_ID = "6700bc51acf84c4aa9510df2ca00b5f4";

  @Autowired
  private Hotspot20OperatorRepository repository;

  @Test
  void existsByTenantIdAndNameAndIdNot() {
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "hotspot20operator-new"
      , "059796b87b054c359a9828a354aab590")).isFalse();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "hotspot20operator-1", "")).isTrue();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "hotspot20operator-1"
      , "059796b87b054c359a9828a354aab591")).isFalse();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "hotspot20operator-2", "")).isTrue();
    assertThat(repository.existsByTenantIdAndNameAndIdNot(TENANT_ID, "hotspot20operator-2",
      "059796b87b054c359a9828a354aab592")).isFalse();
  }
}
