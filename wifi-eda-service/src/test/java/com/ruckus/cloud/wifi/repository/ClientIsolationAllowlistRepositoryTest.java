package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@Tag("ClientIsolationAllowListTest")
@WifiJpaDataTest
@Sql(
    statements =
        """
            -- base data
            INSERT INTO tenant (id) VALUES ('5c8279f79307415fa9e4c88a1819f0fd');
            INSERT INTO venue (id, tenant) VALUES ('88c2e1ccebd745be839198cddf686a88', '5c8279f79307415fa9e4c88a1819f0fd');
            INSERT INTO client_isolation_allowlist (id, name, tenant, venue)
                VALUES ('cia284d992694d5c9d7a2fcf2289a0bd', 'Client Isolation Profile 1', '5c8279f79307415fa9e4c88a1819f0fd', '88c2e1ccebd745be839198cddf686a88'),
                       ('cia284d992694d5c9d7a2fcf2289a0be', 'Client Isolation Profile 2', '5c8279f79307415fa9e4c88a1819f0fd', '88c2e1ccebd745be839198cddf686a88'),
                       ('cia284d992694d5c9d7a2fcf2289a0bf', 'Client Isolation Profile 3', '5c8279f79307415fa9e4c88a1819f0fd', '88c2e1ccebd745be839198cddf686a88');
            -- lan-port-adoption and client-isolation activation
            INSERT INTO ap_lan_port_profile (id, tenant, category)
                VALUES ('04b9de2f3f174f95a6bd432d9df79113', '5c8279f79307415fa9e4c88a1819f0fd', 'ETHERNET');
            INSERT INTO lan_port_adoption (id, tenant, ap_lan_port_profile, checksum)
                VALUES ('f11920c90d7444a596b9a499008007b7', '5c8279f79307415fa9e4c88a1819f0fd', '04b9de2f3f174f95a6bd432d9df79113', 'TEST-CHECKSUM-1'),
                       ('03e17c01cfd446cf93efd1cf6dfe22d2', '5c8279f79307415fa9e4c88a1819f0fd', '04b9de2f3f174f95a6bd432d9df79113', 'TEST-CHECKSUM-2'),
                       ('2fd0160d42fa43b085f88db893532c9g', '5c8279f79307415fa9e4c88a1819f0fd', '04b9de2f3f174f95a6bd432d9df79113', 'TEST-CHECKSUM-3');
            INSERT INTO client_isolation_lan_port_activation (id, tenant, lan_port_adoption, client_isolation_allowlist)
                VALUES ('404fb76fd8f04e2c9bbccfa35e8f3efe', '5c8279f79307415fa9e4c88a1819f0fd', 'f11920c90d7444a596b9a499008007b7', 'cia284d992694d5c9d7a2fcf2289a0bd'),
                       ('b5f1e64ea06b4170831a09f7530eaac4', '5c8279f79307415fa9e4c88a1819f0fd', '03e17c01cfd446cf93efd1cf6dfe22d2', 'cia284d992694d5c9d7a2fcf2289a0be'),
                       ('cbeb8916e41e469c9bfaec93d0ddf7f3', '5c8279f79307415fa9e4c88a1819f0fd', '2fd0160d42fa43b085f88db893532c9g', 'cia284d992694d5c9d7a2fcf2289a0bf');
            -- venue-lan-port
            INSERT INTO venue_ap_model_specific_attributes (id, tenant, venue, model)
                VALUES ('9f4ba5e6690d43a4a4fd8e69d3e6556e', '5c8279f79307415fa9e4c88a1819f0fd', '88c2e1ccebd745be839198cddf686a88', 'R550'),
                       ('06c6017ebbce4d93947a3f85959d450d', '5c8279f79307415fa9e4c88a1819f0fd', '88c2e1ccebd745be839198cddf686a88', 'R770');
            INSERT INTO venue_lan_port (id, tenant, ap_lan_port_profile, venue_ap_model_specific_attributes, lan_port_adoption)
                VALUES ('31f9e514cc904b298b2082e682bc1b82', '5c8279f79307415fa9e4c88a1819f0fd', '04b9de2f3f174f95a6bd432d9df79113', '9f4ba5e6690d43a4a4fd8e69d3e6556e', 'f11920c90d7444a596b9a499008007b7'),
                       ('246012ca780f43e083e9357715ed1582', '5c8279f79307415fa9e4c88a1819f0fd', '04b9de2f3f174f95a6bd432d9df79113', '9f4ba5e6690d43a4a4fd8e69d3e6556e', '03e17c01cfd446cf93efd1cf6dfe22d2'),
                       ('a0bbed8ed73f42118f8775f25ad8e458', '5c8279f79307415fa9e4c88a1819f0fd', '04b9de2f3f174f95a6bd432d9df79113', '06c6017ebbce4d93947a3f85959d450d', '2fd0160d42fa43b085f88db893532c9g');
            -- ap-lan-port
            INSERT INTO ap_group (id, tenant, venue)
                VALUES ('9f88e013eee440c0b9eec3813ef90766', '5c8279f79307415fa9e4c88a1819f0fd', '88c2e1ccebd745be839198cddf686a88');
            INSERT INTO ap_model_specific (id, tenant)
                VALUES ('a373aa0c325f4eec9df989e3473a44c3', '5c8279f79307415fa9e4c88a1819f0fd'),
                       ('47f0229657a7449b90e918e54ccb7c83', '5c8279f79307415fa9e4c88a1819f0fd');
            INSERT INTO ap (id, tenant, ap_group, model_specific, soft_deleted)
                VALUES ('963736948005', '5c8279f79307415fa9e4c88a1819f0fd', '9f88e013eee440c0b9eec3813ef90766', 'a373aa0c325f4eec9df989e3473a44c3', false),
                       ('178550000012', '5c8279f79307415fa9e4c88a1819f0fd', '9f88e013eee440c0b9eec3813ef90766', '47f0229657a7449b90e918e54ccb7c83', false);
            INSERT INTO ap_lan_port (id, tenant, model_specific, ap_lan_port_profile, lan_port_adoption)
                VALUES ('190b16625118404cbf920c136f2e5ab5', '5c8279f79307415fa9e4c88a1819f0fd', 'a373aa0c325f4eec9df989e3473a44c3', '04b9de2f3f174f95a6bd432d9df79113', 'f11920c90d7444a596b9a499008007b7'),
                       ('a793bcc9e589491ba48065c2977b8960', '5c8279f79307415fa9e4c88a1819f0fd', 'a373aa0c325f4eec9df989e3473a44c3', '04b9de2f3f174f95a6bd432d9df79113', '03e17c01cfd446cf93efd1cf6dfe22d2'),
                       ('04a34f1f15e44ad1b2663aa99c0184c7', '5c8279f79307415fa9e4c88a1819f0fd', '47f0229657a7449b90e918e54ccb7c83', '04b9de2f3f174f95a6bd432d9df79113', '2fd0160d42fa43b085f88db893532c9g');
            """)
class ClientIsolationAllowlistRepositoryTest {

  private static final String TENANT_ID = "5c8279f79307415fa9e4c88a1819f0fd";
  private static final String VENUE_ID = "88c2e1ccebd745be839198cddf686a88";
  private static final String AP_MODEL = "R550";

  @Autowired
  private ClientIsolationAllowlistRepository clientIsolationAllowlistRepository;

  @Test
  void findAllVenueLanPortActivatedClientIsolationProfilesByVenueIdsOrApSerials() {
    var result = clientIsolationAllowlistRepository.findAllLanPortsActivatedClientIsolationProfilesByVenueIdsOrApSerials(
        TENANT_ID, List.of(VENUE_ID), List.of("963736948005"));
    assertEquals(3, result.size());
  }

  @Test
  void findClientIsolationProfilesActivatedOnVenueApModelLanPortsTest() {
    var result =
        clientIsolationAllowlistRepository
            .findClientIsolationProfilesActivatedOnVenueApModelLanPorts(
                TENANT_ID, List.of(VENUE_ID), AP_MODEL);
    assertEquals(2, result.size());
    assertEquals(
        Set.of("cia284d992694d5c9d7a2fcf2289a0bd", "cia284d992694d5c9d7a2fcf2289a0be"),
        result.stream().map(ClientIsolationAllowlist::getId).collect(Collectors.toSet()));
  }
}
