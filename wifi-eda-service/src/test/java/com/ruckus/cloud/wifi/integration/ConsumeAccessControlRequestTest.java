package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysTrue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.accessControlProfile;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.APPLICATION_POLICY_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.APPLICATION_POLICY_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.DEVICE_POLICY_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.DEVICE_POLICY_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.L2_ACL_POLICY_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.L2_ACL_POLICY_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.L3_ACL_POLICY_ID;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key.L3_ACL_POLICY_NAME;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value.TYPE_APPLICATION_POLICY;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value.TYPE_DEVICE_POLICY;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.CcmFirewallProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.NullValue;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.IdAndEnabledGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.network.OpenNetwork;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

;

@Tag("AccessControlProfileTest")
@WifiIntegrationTest
public class ConsumeAccessControlRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  protected MessageCaptors messageCaptors;
  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_ACCESS_CONTROL_PROFILE)
  class ConsumeAddAccessControlProfileRequestTest {

    String devicePolicyId;
    String applicationPolicyId;

    @BeforeEach
    private void setup(final ApplicationPolicy applicationPolicy, final DevicePolicy devicePolicy) {
      devicePolicyId = devicePolicy.getId();
      applicationPolicyId = applicationPolicy.getId();
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.AccessControlProfile payload() {
      final com.ruckus.cloud.wifi.eda.viewmodel.AccessControlProfile profile = Generators.accessControlProfile()
          .generate();
      profile.setApplicationPolicy(
          new IdAndEnabledGenerator().setId(always(applicationPolicyId)).setEnabled(alwaysTrue())
              .generate());
      profile.setDevicePolicy(
          new IdAndEnabledGenerator().setId(always(devicePolicyId)).setEnabled(alwaysTrue())
              .generate());
      return profile;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.AccessControlProfile payload) {
      final AccessControlProfile profile = repositoryUtil.find(AccessControlProfile.class,
          payload.getId());
      assertNotNull(profile);
      validateRepositoryData(payload, profile);
      validateDdccmCfgRequestMessages(CfgAction.ADD_ACCESS_CONTROL_PROFILE,
          List.of(payload.getId()), profile);
      validateCmnCfgCollectorMessages(CfgAction.ADD_ACCESS_CONTROL_PROFILE,
          List.of(payload.getId()), profile, 0);
      validateActivityMessages(CfgAction.ADD_ACCESS_CONTROL_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_ACCESS_CONTROL_PROFILE)
  class ConsumeUpdateAccessControlProfileRequestTest {

    String devicePolicyId;
    String accessControlProfileId;

    @BeforeEach
    private void setup(final DevicePolicy devicePolicy,
        final AccessControlProfile accessControlProfile) {
      devicePolicyId = devicePolicy.getId();
      accessControlProfileId = accessControlProfile.getId();
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.AccessControlProfile payload() {
      final com.ruckus.cloud.wifi.eda.viewmodel.AccessControlProfile profile = Generators.accessControlProfile()
          .generate();
      // update existing application policy to null
      profile.setApplicationPolicy(
          new IdAndEnabledGenerator().setEnabled(alwaysFalse()).setId(always(null)).generate());
      // update device policy to another one
      profile.setDevicePolicy(
          new IdAndEnabledGenerator().setId(always(devicePolicyId)).setEnabled(alwaysTrue())
              .generate());
      return profile;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("accessControlProfileId", accessControlProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.AccessControlProfile payload) {
      final AccessControlProfile profile = repositoryUtil.find(AccessControlProfile.class,
          accessControlProfileId);
      assertNotNull(profile);
      payload.setId(accessControlProfileId);
      validateRepositoryData(payload, profile);
      validateDdccmCfgRequestMessages(CfgAction.UPDATE_ACCESS_CONTROL_PROFILE,
          List.of(payload.getId()), profile);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_ACCESS_CONTROL_PROFILE,
          List.of(payload.getId()), profile, 0);
      validateActivityMessages(CfgAction.UPDATE_ACCESS_CONTROL_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_ACCESS_CONTROL_PROFILE)
  class ConsumeDeleteAccessControlProfileRequestTest {

    private String accessControlProfileId;

    @BeforeEach
    void givenOneRowPersistedInDb(final AccessControlProfile accessControlProfile) {
      accessControlProfileId = accessControlProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("accessControlProfileId", accessControlProfileId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      assertNull(repositoryUtil.find(AccessControlProfile.class, accessControlProfileId));
      validateDdccmCfgRequestMessages(CfgAction.DELETE_ACCESS_CONTROL_PROFILE,
          List.of(accessControlProfileId), null);
      validateCmnCfgCollectorMessages(CfgAction.DELETE_ACCESS_CONTROL_PROFILE,
          List.of(accessControlProfileId), null, 0);
      validateActivityMessages(CfgAction.DELETE_ACCESS_CONTROL_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_BULK_ACCESS_CONTROL_PROFILES)
  class ConsumeDeleteAccessControlProfilesRequestTest {

    @Payload
    private List<String> idList;

    @BeforeEach
    void givenTenRowsPersistedInDb(final Tenant tenant) {
      final var accessControlProfiles = accessControlProfile().generate(10);
      accessControlProfiles.forEach(
          policy -> repositoryUtil.createOrUpdate(policy, tenant.getId(), randomTxId()));
      idList = accessControlProfiles.stream().map(AccessControlProfile::getId)
          .collect(Collectors.toList());
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload List<String> payload) {
      assertThat(idList).extracting(id -> repositoryUtil.find(AccessControlProfile.class, id))
          .isNotEmpty().allMatch(Objects::isNull);
      validateDdccmCfgRequestMessages(CfgAction.DELETE_BULK_ACCESS_CONTROL_PROFILES, idList, null);
      validateCmnCfgCollectorMessages(CfgAction.DELETE_BULK_ACCESS_CONTROL_PROFILES, idList, null,
          0);
      validateActivityMessages(CfgAction.DELETE_BULK_ACCESS_CONTROL_PROFILES);
    }
  }

  @Nested
  @ApiAction(CfgAction.ADD_NETWORK)
  class ConsumeAddNetworkWithProfileRequestTest {

    private AccessControlProfile accessControlProfile;
    private ApplicationPolicy applicationPolicy;
    private DevicePolicy devicePolicy;

    @BeforeEach
    private void setup(AccessControlProfile accessControlProfile) {
      this.accessControlProfile = accessControlProfile;
      this.applicationPolicy = accessControlProfile.getApplicationPolicy();
      this.devicePolicy = accessControlProfile.getDevicePolicy();
    }

    @Payload("WithAccessControlProfile")
    private com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork accessControlProfile() {
      final var networkRequest = Generators.pskNetwork().generate();
      networkRequest.getWlan().getAdvancedCustomization().setAccessControlEnable(true);
      networkRequest.getWlan().getAdvancedCustomization()
          .setAccessControlProfileId(accessControlProfile.getId());
      return networkRequest;
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("WithAccessControlProfile"))
    void thenShouldHandleTheRequestSuccessfully_WithAccessControl(
        @Payload("WithAccessControlProfile") com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork payload) {
      final PskNetwork network = repositoryUtil.find(PskNetwork.class, payload.getId());
      assertThat(network).isNotNull().matches(n -> StringUtils.equals(n.getId(), payload.getId()))
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlProfile() != null)
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlProfile().getId()
              .equals(accessControlProfile.getId()))
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlEnable());
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      validateCmnCfgCollectorMessages(CfgAction.ADD_NETWORK, List.of(accessControlProfile.getId()),
          accessControlProfile, 1);
      validateActivityMessages(CfgAction.ADD_NETWORK);
    }

    @Payload("WithRespectivePolicy")
    private com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork policies() {
      final var networkRequest = Generators.pskNetwork().generate();
      networkRequest.getWlan().getAdvancedCustomization()
          .setApplicationPolicyId(applicationPolicy.getId());
      networkRequest.getWlan().getAdvancedCustomization().setApplicationPolicyEnable(true);
      networkRequest.getWlan().getAdvancedCustomization().setDevicePolicyId(devicePolicy.getId());
      networkRequest.getWlan().getAdvancedCustomization().setRespectiveAccessControl(true);
      return networkRequest;
    }

    @Test
    @ApiAction(value = CfgAction.ADD_NETWORK, payload = @Payload("WithRespectivePolicy"))
    void thenShouldHandleTheRequestSuccessfully_WithRespectivePolicy(
        @Payload("WithRespectivePolicy") com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork payload) {
      final PskNetwork network = repositoryUtil.find(PskNetwork.class, payload.getId());
      assertThat(network).isNotNull().matches(n -> Objects.equals(n.getId(), payload.getId()))
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlProfile() == null)
          .matches(n -> !n.getWlan().getAdvancedCustomization().getAccessControlEnable())
          .matches(n -> n.getWlan().getAdvancedCustomization().getApplicationPolicy() != null)
          .matches(n -> n.getWlan().getAdvancedCustomization().getApplicationPolicyEnable())
          .matches(n -> n.getWlan().getAdvancedCustomization().getDevicePolicy() != null);
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      validateRespectivePolicyCmnCfgCollectorMessages(applicationPolicy, devicePolicy, 1);
      validateActivityMessages(CfgAction.ADD_NETWORK);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_NETWORK)
  class ConsumeUpdateNetworkWithProfileRequestTest {

    AccessControlProfile accessControlProfile;
    ApplicationPolicy applicationPolicy;
    DevicePolicy devicePolicy;
    Network network;

    @BeforeEach
    public void beforeEach(final @OpenNetwork Network network, Venue venue, NetworkVenue networkVenue,
        final AccessControlProfile accessControlProfile) {
      this.accessControlProfile = accessControlProfile;
      this.applicationPolicy = accessControlProfile.getApplicationPolicy();
      this.devicePolicy = accessControlProfile.getDevicePolicy();
      this.network = network;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", network.getId());
    }

    @Payload("WithAccessControlProfile")
    private com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork accessControlProfile() {
      final var networkRequest = Generators.openNetwork().generate();
      networkRequest.setId(network.getId());
      networkRequest.getWlan().getAdvancedCustomization().setRespectiveAccessControl(false);
      networkRequest.getWlan().getAdvancedCustomization().setAccessControlEnable(true);
      networkRequest.getWlan().getAdvancedCustomization()
          .setAccessControlProfileId(accessControlProfile.getId());
      return networkRequest;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("WithAccessControlProfile"))
    void thenShouldHandleTheRequestSuccessfully_WithAccessControl(TxCtx txCtx, CfgAction apiAction,
        @Payload("WithAccessControlProfile") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      final com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork network = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, payload.getId());
      assertThat(network).isNotNull().matches(n -> Objects.equals(n.getId(), payload.getId()))
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlProfile() != null)
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlProfile().getId()
              .equals(accessControlProfile.getId()))
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlEnable());
      validateWlanVenueDdccmCfgRequestMessages(accessControlProfile.getId(), null, null);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_NETWORK,
          List.of(accessControlProfile.getId()), accessControlProfile, 1);
      validateActivityMessages(CfgAction.UPDATE_NETWORK);
    }

    @Payload("WithRespectivePolicy")
    private com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork policies() {
      final var networkRequest = Generators.openNetwork().generate();
      networkRequest.setId(network.getId());
      networkRequest.getWlan().getAdvancedCustomization()
          .setApplicationPolicyId(applicationPolicy.getId());
      networkRequest.getWlan().getAdvancedCustomization().setApplicationPolicyEnable(true);
      networkRequest.getWlan().getAdvancedCustomization().setDevicePolicyId(devicePolicy.getId());
      networkRequest.getWlan().getAdvancedCustomization().setRespectiveAccessControl(true);
      return networkRequest;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK, payload = @Payload("WithRespectivePolicy"))
    void thenShouldHandleTheRequestSuccessfully_WithRespectivePolicy(
        @Payload("WithRespectivePolicy") com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      final com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork network = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, payload.getId());
      assertThat(network).isNotNull().matches(n -> Objects.equals(n.getId(), payload.getId()))
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlProfile() == null)
          .matches(n -> !n.getWlan().getAdvancedCustomization().getAccessControlEnable())
          .matches(n -> n.getWlan().getAdvancedCustomization().getApplicationPolicy() != null)
          .matches(n -> n.getWlan().getAdvancedCustomization().getApplicationPolicyEnable())
          .matches(n -> n.getWlan().getAdvancedCustomization().getDevicePolicy() != null);
      validateWlanVenueDdccmCfgRequestMessages(null, applicationPolicy.getId(),
          devicePolicy.getId());
      validateRespectivePolicyCmnCfgCollectorMessages(applicationPolicy, devicePolicy, 1);
      validateActivityMessages(CfgAction.UPDATE_NETWORK);
    }
  }

  @Nested
  class ConsumeUpdateAndDeleteNetworkWithoutAccessControlRequestTest {

    AccessControlProfile accessControlProfile;
    Network network;

    @BeforeEach
    public void beforeEach(final @OpenNetwork Network network,
        final AccessControlProfile accessControlProfile) {
      network.getWlan().getAdvancedCustomization().setAccessControlEnable(true);
      network.getWlan().getAdvancedCustomization().setAccessControlProfile(accessControlProfile);
      this.network = repositoryUtil.createOrUpdate(network, network.getTenant().getId(),
          randomTxId());
      this.accessControlProfile = accessControlProfile;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", network.getId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork accessControlProfile() {
      final var networkRequest = Generators.openNetwork().generate();
      networkRequest.setId(network.getId());
      networkRequest.getWlan().getAdvancedCustomization().setAccessControlEnable(false);
      networkRequest.getWlan().getAdvancedCustomization().setAccessControlProfileId(null);
      return networkRequest;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK)
    void thenShouldHandleTheRequestSuccessfully_WithAccessControl(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      final com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork network = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, payload.getId());
      assertThat(network).isNotNull().matches(n -> Objects.equals(n.getId(), payload.getId()))
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlProfile() == null)
          .matches(n -> !n.getWlan().getAdvancedCustomization().getAccessControlEnable());
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_NETWORK,
          List.of(accessControlProfile.getId()), accessControlProfile, 0);
      validateActivityMessages(CfgAction.UPDATE_NETWORK);
    }

    @Test
    @ApiAction(CfgAction.DELETE_NETWORK)
    void thenShouldHandleTheRequestSuccessfully_WithAccessControl() {
      assertNull(repositoryUtil.find(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class,
          network.getId()));
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      validateCmnCfgCollectorMessages(CfgAction.DELETE_NETWORK,
          List.of(accessControlProfile.getId()), accessControlProfile, 0);
      validateActivityMessages(CfgAction.DELETE_NETWORK);
    }
  }

  @Nested
  class ConsumeUpdateAndDeleteNetworkWithoutRespectivePolicyRequestTest {

    DevicePolicy devicePolicy;
    ApplicationPolicy applicationPolicy;
    Network network;

    @BeforeEach
    public void beforeEach(final @OpenNetwork Network network,
        final AccessControlProfile accessControlProfile) {
      this.devicePolicy = accessControlProfile.getDevicePolicy();
      this.applicationPolicy = accessControlProfile.getApplicationPolicy();
      network.getWlan().getAdvancedCustomization().setApplicationPolicy(applicationPolicy);
      network.getWlan().getAdvancedCustomization().setApplicationPolicyEnable(true);
      network.getWlan().getAdvancedCustomization().setDevicePolicy(devicePolicy);
      network.getWlan().getAdvancedCustomization().setRespectiveAccessControl(true);
      this.network = repositoryUtil.createOrUpdate(network, network.getTenant().getId(),
          randomTxId());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", network.getId());
    }

    @Payload
    private com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork policies() {
      final var networkRequest = Generators.openNetwork().generate();
      networkRequest.setId(network.getId());
      networkRequest.getWlan().getAdvancedCustomization().setApplicationPolicyId(null);
      networkRequest.getWlan().getAdvancedCustomization().setApplicationPolicyEnable(false);
      networkRequest.getWlan().getAdvancedCustomization().setDevicePolicyId(null);
      networkRequest.getWlan().getAdvancedCustomization().setRespectiveAccessControl(true);
      return networkRequest;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_NETWORK)
    void thenShouldHandleTheRequestSuccessfully_WithRespectivePolicy(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.OpenNetwork payload) {
      final com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork network = repositoryUtil.find(
          com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class, payload.getId());
      assertThat(network).isNotNull().matches(n -> Objects.equals(n.getId(), payload.getId()))
          .matches(n -> n.getWlan().getAdvancedCustomization().getAccessControlProfile() == null)
          .matches(n -> n.getWlan().getAdvancedCustomization().getApplicationPolicy() == null)
          .matches(n -> n.getWlan().getAdvancedCustomization().getDevicePolicy() == null);
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      validateRespectivePolicyCmnCfgCollectorMessages(applicationPolicy, devicePolicy, 0);
      validateActivityMessages(CfgAction.UPDATE_NETWORK);
    }

    @Test
    @ApiAction(value = CfgAction.DELETE_NETWORK)
    void thenShouldHandleTheRequestSuccessfully_WithRespectivePolicy() {
      assertNull(repositoryUtil.find(com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork.class,
          network.getId()));
      messageCaptors.getDdccmMessageCaptor().assertNotSentByTenant(txCtxExtension.getTenantId());
      validateRespectivePolicyCmnCfgCollectorMessages(applicationPolicy, devicePolicy, 0);
      validateActivityMessages(CfgAction.DELETE_NETWORK);
    }
  }

  private void validateDdccmCfgRequestMessages(CfgAction apiAction, List<String> idList,
      AccessControlProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasCcmFirewallProfile)
            .hasSize(idList.size()).allMatch(op -> idList.contains(op.getId()))
            .allMatch(op -> op.getAction() == action(apiAction)).allSatisfy(
                op -> assertThat(op).extracting(
                        com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op).extracting(
                        com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCcmFirewallProfile)
                    .matches(config -> idList.contains(config.getId()));
              } else {
                assertThat(payload).isNotNull();
                assertThat(op).extracting(
                        com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCcmFirewallProfile)
                    .matches(config -> idList.contains(config.getId()))
                    .matches(config -> payload.getName().equals(config.getName()))
                    .matches(config -> assertPolicies(payload, config)).matches(
                        config -> ((long) payload.getRateLimiting().getDownlinkLimit() * 1000000)
                            == config.getDownlinkRateLimitingBps()).matches(
                        config -> ((long) payload.getRateLimiting().getUplinkLimit() * 1000000)
                            == config.getUplinkRateLimitingBps());
              }
            }));
  }

  private void validateWlanVenueDdccmCfgRequestMessages(String accessControlProfileId,
      String applicationPolicyId, String devicePolicyId) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasWlanVenue).hasSize(1)
            .first().matches(op -> op.getAction() == Action.MODIFY)
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getWlanVenue)
            .matches(wlanVenue -> {
              if (accessControlProfileId != null) {
                return wlanVenue.getFirewallProfileId().getValue().equals(accessControlProfileId);
              }
              if (applicationPolicyId != null && devicePolicyId != null) {
                return wlanVenue.getAppPolicyId().getValue().equals(applicationPolicyId)
                    && wlanVenue.getDevicePolicyId().getValue().equals(devicePolicyId);
              }
              return false;
            }));
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, List<String> idList,
      AccessControlProfile payload, int expectedNetworkCount) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(idList.size()).allMatch(op -> idList.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction)).allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op).extracting(Operations::getDocMap)
                    .matches(doc -> idList.contains(doc.get(EsConstants.Key.ID).getStringValue()))
                    .matches(
                        doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                    .matches(doc -> payload.getName().equals(doc.get(Key.NAME).getStringValue()))
                    .matches(doc -> assertCfgPolicies(payload, doc)).matches(
                        doc -> expectedNetworkCount == doc.get(Key.NETWORK_IDS).getListValue()
                            .getValuesCount());
              }
            }));
  }

  private void validateRespectivePolicyCmnCfgCollectorMessages(
      com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy applicationPolicy,
      com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy devicePolicy, int expectedNetworkCount) {

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast).satisfies(ops -> {
              assertThat(ops).filteredOn(
                      op -> TYPE_APPLICATION_POLICY.equals(op.getDocMap().get(Key.TYPE))).hasSize(1)
                  .first().extracting(Operations::getDocMap).matches(
                      doc -> applicationPolicy.getId()
                          .equals(doc.get(EsConstants.Key.ID).getStringValue())).matches(
                      doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                  .matches(
                      doc -> applicationPolicy.getName().equals(doc.get(Key.NAME).getStringValue()))
                  .matches(doc -> applicationPolicy.getRules().size() == (doc.get(Key.RULES)
                      .getNumberValue())).matches(
                      doc -> expectedNetworkCount == doc.get(Key.NETWORK_IDS).getListValue()
                          .getValuesCount());
              assertThat(ops).filteredOn(
                      op -> TYPE_DEVICE_POLICY.equals(op.getDocMap().get(Key.TYPE))).hasSize(1).first()
                  .extracting(Operations::getDocMap).matches(
                      doc -> devicePolicy.getId().equals(doc.get(EsConstants.Key.ID).getStringValue()))
                  .matches(
                      doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                  .matches(doc -> devicePolicy.getName().equals(doc.get(Key.NAME).getStringValue()))
                  .matches(doc -> devicePolicy.getRules().size() == (doc.get(Key.RULES)
                      .getNumberValue())).matches(
                      doc -> expectedNetworkCount == doc.get(Key.NETWORK_IDS).getListValue()
                          .getValuesCount());
            })

    );
  }

  private void validateActivityMessages(CfgAction apiAction) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> assertThat(
        activityCfgChangeRespMessage.getPayload()).matches(
            msg -> msg.getStatus().equals(Status.OK))
        .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
        .extracting(ConfigurationStatus::getEventDate).isNotNull());
  }


  private boolean assertPolicies(AccessControlProfile payload, CcmFirewallProfile config) {
    assertFalse(config.hasFirewallL2AccessControlPolicy());
    assertFalse(config.hasFirewallL3AccessControlPolicy());
    if (payload.getApplicationPolicy() != null) {
      assertEquals(payload.getApplicationPolicy().getId(),
          config.getFirewallQmApplicationPolicy().getQmApplicationPolicyId().getValue());
    } else {
      assertFalse(config.hasFirewallQmApplicationPolicy());
    }
    if (payload.getDevicePolicy() != null) {
      assertEquals(payload.getDevicePolicy().getId(),
          config.getFirewallDevicePolicy().getDevicePolicyId().getValue());
    } else {
      assertFalse(config.hasFirewallDevicePolicy());
    }

    return true;
  }

  private boolean assertCfgPolicies(AccessControlProfile payload, Map<String, Value> doc) {
    assertEquals(NullValue.NULL_VALUE, doc.get(L2_ACL_POLICY_NAME).getNullValue());
    assertEquals(NullValue.NULL_VALUE, doc.get(L2_ACL_POLICY_ID).getNullValue());
    assertEquals(NullValue.NULL_VALUE, doc.get(L3_ACL_POLICY_NAME).getNullValue());
    assertEquals(NullValue.NULL_VALUE, doc.get(L3_ACL_POLICY_ID).getNullValue());
    if (payload.getDevicePolicy() != null) {
      assertEquals(payload.getDevicePolicy().getName(),
          doc.get(DEVICE_POLICY_NAME).getStringValue());
      assertEquals(payload.getDevicePolicy().getId(), doc.get(DEVICE_POLICY_ID).getStringValue());
    } else {
      assertEquals(NullValue.NULL_VALUE, doc.get(DEVICE_POLICY_NAME).getNullValue());
      assertEquals(NullValue.NULL_VALUE, doc.get(DEVICE_POLICY_ID).getNullValue());
    }

    if (payload.getApplicationPolicy() != null) {
      assertEquals(payload.getApplicationPolicy().getName(),
          doc.get(APPLICATION_POLICY_NAME).getStringValue());
      assertEquals(payload.getApplicationPolicy().getId(),
          doc.get(APPLICATION_POLICY_ID).getStringValue());
    } else {
      assertEquals(NullValue.NULL_VALUE, doc.get(APPLICATION_POLICY_NAME).getNullValue());
      assertEquals(NullValue.NULL_VALUE, doc.get(APPLICATION_POLICY_ID).getNullValue());
    }

    return true;
  }

  private void validateNothingHappened(CfgAction apiAction) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
    assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
    messageCaptors.assertThat(
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest(),
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(tenantId);
  }

  private void validateRepositoryData(
      com.ruckus.cloud.wifi.eda.viewmodel.AccessControlProfile expected,
      AccessControlProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getName(), actual.getName());
    assertEquals(expected.getDescription(), actual.getDescription());
    if (expected.getApplicationPolicy() != null && expected.getApplicationPolicy().getId() != null) {
      assertEquals(expected.getApplicationPolicy().getId(), actual.getApplicationPolicy().getId());
    } else {
      assertNull(actual.getApplicationPolicy());
    }
    assertEquals(expected.getApplicationPolicy().getEnabled(), actual.getApplicationPolicyEnable());
    if (expected.getDevicePolicy() != null && expected.getDevicePolicy().getId() != null) {
      assertEquals(expected.getDevicePolicy().getId(), actual.getDevicePolicy().getId());
    } else {
      assertNull(actual.getDevicePolicy());
    }
    assertEquals(expected.getDevicePolicy().getEnabled(), actual.getDevicePolicyEnable());
    assertNull(actual.getL2AclPolicy());
    assertFalse(actual.getL2AclEnable());
    assertNull(actual.getL3AclPolicy());
    assertFalse(actual.getL3AclEnable());
    if (expected.getRateLimiting() != null) {
      assertEquals(expected.getRateLimiting().getDownlinkLimit(),
          actual.getRateLimiting().getDownlinkLimit());
      assertEquals(expected.getRateLimiting().getUplinkLimit(),
          actual.getRateLimiting().getUplinkLimit());
      assertEquals(expected.getRateLimiting().getEnabled(), actual.getRateLimiting().getEnabled());
    } else {
      assertNull(actual.getRateLimiting());
    }
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ACCESS_CONTROL_PROFILE -> Action.ADD;
      case UPDATE_ACCESS_CONTROL_PROFILE -> Action.MODIFY;
      case DELETE_ACCESS_CONTROL_PROFILE, DELETE_BULK_ACCESS_CONTROL_PROFILES -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ACCESS_CONTROL_PROFILE -> OpType.ADD;
      case UPDATE_ACCESS_CONTROL_PROFILE, ADD_NETWORK, UPDATE_NETWORK, DELETE_NETWORK -> OpType.MOD;
      case DELETE_ACCESS_CONTROL_PROFILE, DELETE_BULK_ACCESS_CONTROL_PROFILES -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_ACCESS_CONTROL_PROFILE -> ApiFlowNames.ADD_ACCESS_CONTROL_PROFILE;
      case UPDATE_ACCESS_CONTROL_PROFILE -> ApiFlowNames.UPDATE_ACCESS_CONTROL_PROFILE;
      case DELETE_ACCESS_CONTROL_PROFILE -> ApiFlowNames.DELETE_ACCESS_CONTROL_PROFILE;
      case DELETE_BULK_ACCESS_CONTROL_PROFILES -> ApiFlowNames.DELETE_BULK_ACCESS_CONTROL_PROFILES;
      case ADD_NETWORK -> ApiFlowNames.ADD_NETWORK;
      case UPDATE_NETWORK -> ApiFlowNames.UPDATE_NETWORK;
      case DELETE_NETWORK -> ApiFlowNames.DELETE_NETWORK;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
