package com.ruckus.cloud.wifi.integration.asyncjob;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.alwaysFalse;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.ap;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.apGroup;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkApGroup;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.networkVenue;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wlan;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Value;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.builder.utils.ValueUtils;
import com.ruckus.cloud.wifi.proto.ApGroupPostMigrationJob;
import com.ruckus.cloud.wifi.proto.WifiAsyncJob;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("ApGroupTest")
@WifiIntegrationTest
class ConsumeApGroupPostMigrationJobTest {

  @Autowired
  private RepositoryUtil repositoryUtil;

  @Autowired
  private ExtendedMessageUtil messageUtil;

  @Autowired
  protected MessageCaptors messageCaptors;

  @Nested
  class GivenApGroupPersistedInDb {

    private ApGroup apGroup;

    @BeforeEach
    void givenApGroupPersistedInDb(final Tenant tenant, final Venue venue) {
      apGroup = repositoryUtil.createOrUpdate(apGroup()
          .setTenant(always(tenant))
          .setVenue(always(venue))
          .setIsDefault(alwaysFalse())
          .generate(), tenant.getId(), randomTxId());
    }

    @Test
    void whenConsumeApGroupPostMigrationJob(final Tenant tenant) {
      final var requestId = randomTxId();

      messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
          .setApGroupPostMigrationJob(ApGroupPostMigrationJob.newBuilder()).build());

      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenant.getId());
    }

    @Nested
    class GivenApGroupWith10ApsPersistedInDb {

      @BeforeEach
      void given10ApsPersistedInDb(final Tenant tenant) {
        apGroup.setAps(ap().setTenant(always(tenant)).setApGroup(always(apGroup))
            .generate(10, ap -> repositoryUtil.createOrUpdate(ap, tenant.getId(), randomTxId())));
      }

      @Test
      void whenConsumeApGroupPostMigrationJob(final Tenant tenant) {
        final var requestId = randomTxId();

        messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
            .setApGroupPostMigrationJob(ApGroupPostMigrationJob.newBuilder()).build());

        final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenant.getId());
        assertThat(cmnCfgCollectorMessage).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull();

        assertThat(cmnCfgCollectorMessage.getPayload())
            .satisfies(msg -> {
              assertThat(msg.getTenantId()).isEqualTo(tenant.getId());
              assertThat(msg.getRequestId()).startsWith(requestId);
            })
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
            .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getId()).isEqualTo(apGroup.getId());
              assertThat(op.getOpType()).isEqualTo(OpType.MOD);
              assertThat(op.getDocMap())
                  .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                  .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenant.getId()))
                  .containsEntry(Key.ID, ValueUtils.stringValue(apGroup.getId()))
                  .containsEntry(Key.NAME, ValueUtils.stringValue(apGroup.getName()))
                  .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(apGroup.getVenue().getId()))
                  .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                  .containsEntry(Key.WIFI_NETWORK_IDS, ValueUtils.nullValue())
                  .satisfies(docMap -> {
                    if (StringUtils.isNotEmpty(apGroup.getDescription())) {
                      assertThat(docMap)
                          .containsEntry(Key.DESCRIPTION,
                              ValueUtils.stringValue(apGroup.getDescription()));
                    }
                  });
            });
      }
    }

    @Nested
    class GivenApGroupWith10NetworksPersistedInDb {

      private List<NetworkApGroup> networkApGroups;

      @BeforeEach
      void given10NetworksPersistedInDb(final Tenant tenant, final Venue venue) {
        networkApGroups = networkApGroup()
            .setApGroup(always(apGroup))
            .setNetworkVenue(networkVenue()
                .setVenue(always(venue)).setIsAllApGroups(alwaysFalse())
                .setNetwork(network(OpenNetwork.class)
                    .setTenant(always(tenant))
                    .setWlan(wlan().setTenant(always(tenant)))))
            .generate(10, networkApGroup -> {
              networkApGroup.getNetworkVenue().getNetwork().getWlan()
                  .setNetwork(networkApGroup.getNetworkVenue().getNetwork());
              networkApGroup.getNetworkVenue().setNetwork(
                  repositoryUtil.createOrUpdate(networkApGroup.getNetworkVenue().getNetwork(), tenant.getId(), randomTxId()));
              networkApGroup.setNetworkVenue(
                  repositoryUtil.createOrUpdate(networkApGroup.getNetworkVenue(), tenant.getId(), randomTxId()));
            }).stream().map(networkApGroup -> repositoryUtil.createOrUpdate(networkApGroup, tenant.getId(), randomTxId())).toList();
      }

      @Test
      void whenConsumeApGroupPostMigrationJob(final Tenant tenant) {
        final var requestId = randomTxId();

        messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
            .setApGroupPostMigrationJob(ApGroupPostMigrationJob.newBuilder()).build());

        final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
            .getValue(tenant.getId());
        assertThat(cmnCfgCollectorMessage).isNotNull()
            .extracting(KafkaProtoMessage::getPayload).isNotNull();

        assertThat(cmnCfgCollectorMessage.getPayload())
            .satisfies(msg -> {
              assertThat(msg.getTenantId()).isEqualTo(tenant.getId());
              assertThat(msg.getRequestId()).startsWith(requestId);
            })
            .extracting(ViewmodelCollector::getOperationsList,
                InstanceOfAssertFactories.list(Operations.class))
            .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
            .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
            .isNotEmpty().singleElement()
            .satisfies(op -> {
              assertThat(op.getId()).isEqualTo(apGroup.getId());
              assertThat(op.getOpType()).isEqualTo(OpType.MOD);
              assertThat(op.getDocMap())
                  .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                  .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenant.getId()))
                  .containsEntry(Key.ID, ValueUtils.stringValue(apGroup.getId()))
                  .containsEntry(Key.NAME, ValueUtils.stringValue(apGroup.getName()))
                  .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(apGroup.getVenue().getId()))
                  .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                  .satisfies(docMap -> {
                    if (StringUtils.isNotEmpty(apGroup.getDescription())) {
                      assertThat(docMap)
                          .containsEntry(Key.DESCRIPTION,
                              ValueUtils.stringValue(apGroup.getDescription()));
                    }
                    assertThat(docMap.get(Key.WIFI_NETWORK_IDS))
                        .isNotNull()
                        .extracting(value -> value.getListValue().getValuesList(),
                            InstanceOfAssertFactories.list(
                                com.ruckus.cloud.events.gpb.Value.class))
                        .isNotEmpty().hasSize(networkApGroups.size())
                        .extracting(com.ruckus.cloud.events.gpb.Value::getStringValue)
                        .containsExactlyInAnyOrderElementsOf(networkApGroups.stream()
                            .map(NetworkApGroup::getNetworkVenue).map(NetworkVenue::getNetwork)
                            .map(Network::getId).toList());
                  });
            });
      }

      @Nested
      class GivenApGroupWith10NetworksAnd10ApsPersistedInDb {

        @BeforeEach
        void given10ApsPersistedInDb(final Tenant tenant) {
          apGroup.setAps(ap().setTenant(always(tenant)).setApGroup(always(apGroup))
              .generate(10, ap -> repositoryUtil.createOrUpdate(ap, tenant.getId(), randomTxId())));
        }

        @Test
        void whenConsumeApGroupPostMigrationJob(final Tenant tenant) {
          final var requestId = randomTxId();

          messageUtil.sendAsyncJob(tenant.getId(), requestId, WifiAsyncJob.newBuilder()
              .setApGroupPostMigrationJob(ApGroupPostMigrationJob.newBuilder()).build());

          final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor()
              .getValue(tenant.getId());
          assertThat(cmnCfgCollectorMessage).isNotNull()
              .extracting(KafkaProtoMessage::getPayload).isNotNull();

          assertThat(cmnCfgCollectorMessage.getPayload())
              .satisfies(msg -> {
                assertThat(msg.getTenantId()).isEqualTo(tenant.getId());
                assertThat(msg.getRequestId()).startsWith(requestId);
              })
              .extracting(ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .isNotEmpty().hasSizeGreaterThanOrEqualTo(1)
              .filteredOn(op -> Index.DEVICE_GROUP.equals(op.getIndex()))
              .isNotEmpty().singleElement()
              .satisfies(op -> {
                assertThat(op.getId()).isEqualTo(apGroup.getId());
                assertThat(op.getOpType()).isEqualTo(OpType.MOD);
                assertThat(op.getDocMap())
                    .containsEntry(Key.TYPE, Value.DEVICE_GROUP)
                    .containsEntry(Key.TENANT_ID, ValueUtils.stringValue(tenant.getId()))
                    .containsEntry(Key.ID, ValueUtils.stringValue(apGroup.getId()))
                    .containsEntry(Key.NAME, ValueUtils.stringValue(apGroup.getName()))
                    .containsEntry(Key.VENUE_ID, ValueUtils.stringValue(apGroup.getVenue().getId()))
                    .containsEntry(Key.APGROUPS_IS_DEFAULT, ValueUtils.boolValue(false))
                    .satisfies(docMap -> {
                      if (StringUtils.isNotEmpty(apGroup.getDescription())) {
                        assertThat(docMap)
                            .containsEntry(Key.DESCRIPTION,
                                ValueUtils.stringValue(apGroup.getDescription()));
                      }
                      assertThat(docMap.get(Key.WIFI_NETWORK_IDS))
                          .isNotNull()
                          .extracting(value -> value.getListValue().getValuesList(),
                              InstanceOfAssertFactories.list(
                                  com.ruckus.cloud.events.gpb.Value.class))
                          .isNotEmpty().hasSize(networkApGroups.size())
                          .extracting(com.ruckus.cloud.events.gpb.Value::getStringValue)
                          .containsExactlyInAnyOrderElementsOf(networkApGroups.stream()
                              .map(NetworkApGroup::getNetworkVenue).map(NetworkVenue::getNetwork)
                              .map(Network::getId).toList());
                    });
              });
        }
      }
    }
  }
}
