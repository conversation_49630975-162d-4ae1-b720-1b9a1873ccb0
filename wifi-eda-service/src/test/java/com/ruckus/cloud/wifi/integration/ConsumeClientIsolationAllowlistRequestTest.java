package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_CLIENT_ISOLATION_ALLOWLIST;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_CLIENT_ISOLATION_ALLOWLIST;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_CLIENT_ISOLATION_ALLOWLISTS;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_CLIENT_ISOLATION_ALLOWLIST;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.openNetwork;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME;
import static com.ruckus.cloud.wifi.test.fixture.ClientIsolationTestFixture.clientIsolationAllowlist;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlistVenueQueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlistVenueQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.OpenNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueClientIsolationAllowlistQueryRequest;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueClientIsolationAllowlistQueryResponse;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.SortOrderEnum;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

@Slf4j
@WifiIntegrationTest
public class ConsumeClientIsolationAllowlistRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void addClientIsolationAllowlist(Tenant tenant) {
    clearMessageBeforeEachEdaOperation = false;

    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();
    var myAllowlistId = randomId();

    Venue v1 = createVenue(tenant, "v1");
    Venue v2 = createVenue(tenant, "v2");

    ClientIsolationAllowlist allowlist = clientIsolationAllowlist("name");
    allowlist.setId(myAllowlistId);

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_ALLOWLIST, tenantId);

    List<ClientIsolationAllowlist> allowlists = getClientIsolationAllowlists();
    assertEquals(1, allowlists.size());
    String clientIsolationAllowlistId = allowlists.get(0).getId();
    assertEquals(myAllowlistId, clientIsolationAllowlistId);

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisher(ddccmOperations, 1),
          () -> assertDdccmPublisherAllowlist(ddccmOperations,
              Action.ADD, clientIsolationAllowlistId, "name")
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollector(viewmodelOperations, 1),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.ADD, clientIsolationAllowlistId, "name", 0.0)
      );
    }

    // apply allowlist to venues
    clearMessage();

    log.info("===== create n1, n2");
    OpenNetwork n1 = addOpenNetwork(map(openNetwork("n1").generate()));
    PskNetwork n2 = addPskNetwork(map(pskNetwork("n2").generate()));

    log.info("===== add nv1");
    edaAddNetworkVenue(tenantId, userName, n1.getId(), v1.getId(), clientIsolationAllowlistId);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);
    log.info("===== add nv2");
    edaAddNetworkVenue(tenantId, userName, n2.getId(), v2.getId(), clientIsolationAllowlistId);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    n1 = (OpenNetwork) getNetwork(n1.getId());
    n2 = (PskNetwork) getNetwork(n2.getId());

    assertEquals(clientIsolationAllowlistId, n1.getNetworkVenues().get(0).getClientIsolationAllowlist().getId());
    assertEquals(clientIsolationAllowlistId, n2.getNetworkVenues().get(0).getClientIsolationAllowlist().getId());

    {
      var ddccmOperations = receiveDdccmOperations(2, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisherNotEmpty(ddccmOperations),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(4, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollectorNotEmpty(viewmodelOperations),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 1.0),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 2.0)
      );
    }

    // test tenant-max-count 2
    clearMessage();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(clientIsolationAllowlist("al2")));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_ALLOWLIST, tenantId);

    clearMessage();

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(clientIsolationAllowlist("al3")));
    assertActivityStatusFail(ADD_CLIENT_ISOLATION_ALLOWLIST, Errors.WIFI_10241, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    // test bind-max-count 3
    clearMessage();

    log.info("===== create n3, n4");
    OpenNetwork n3 = addOpenNetwork(map(openNetwork("n3").generate()));
    PskNetwork n4 = addPskNetwork(map(pskNetwork("n4").generate()));

    log.info("===== add nv3");
    edaAddNetworkVenue(tenantId, userName, n3.getId(), v1.getId(), clientIsolationAllowlistId);

    clearMessage();

    log.info("===== add nv4");
    edaAddNetworkVenue(tenantId, userName, n4.getId(), v2.getId(), clientIsolationAllowlistId);
    assertActivityStatusFail(ADD_NETWORK_VENUE, Errors.WIFI_10455, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    // unset it from venues
    clearMessage();

    log.info("===== unset nvs allowlist");
    {
      NetworkVenue nv1 = getNetworkVenueByNetworkAndVenue(n1.getId(), v1.getId());
      NetworkVenue nv2 = getNetworkVenueByNetworkAndVenue(n2.getId(), v2.getId());
      NetworkVenue nv3 = getNetworkVenueByNetworkAndVenue(n3.getId(), v1.getId());
      List.of(nv1, nv2, nv3)
          .stream()
          .map(this::map)
          .forEach(nv -> updateEdaNetworkVenue(tenantId, userName, nv.getId(), nv, null));
    }

    {
      var ddccmOperations = receiveDdccmOperations(2, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisherNotEmpty(ddccmOperations),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(3, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollectorNotEmpty(viewmodelOperations),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 2.0),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 1.0),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 0.0)
      );
    }

    // update back by network-venue
    clearMessage();

    log.info("===== set nv1 allowlist, nv2 allowlist for the next 2 tests");
    {
      NetworkVenue nv = getNetworkVenueByNetworkAndVenue(n1.getId(), v1.getId());
      updateEdaNetworkVenue(tenantId, userName, nv.getId(), map(nv), clientIsolationAllowlistId);
    }
    {
      NetworkVenue nv = getNetworkVenueByNetworkAndVenue(n2.getId(), v2.getId());
      updateEdaNetworkVenue(tenantId, userName, nv.getId(), map(nv), clientIsolationAllowlistId);
    }

    {
      var ddccmOperations = receiveDdccmOperations(2, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisherNotEmpty(ddccmOperations),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(2, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollectorNotEmpty(viewmodelOperations),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 1.0),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 2.0)
      );
    }

    // network disable client-isolation
    clearMessage();

    log.info("===== disable n1 client-isolation");
    n1 = (OpenNetwork) getNetwork(n1.getId());
    n1.getWlan().getAdvancedCustomization().setClientIsolation(false);
    n1.getWlan().getAdvancedCustomization().setClientIsolationOptions(null);
    updateEdaNetwork(tenantId, userName, n1.getId(), map(n1));
    assertActivityStatusSuccess(UPDATE_NETWORK, tenantId);

    n1 = (OpenNetwork) getNetwork(n1.getId());
    assertFalse(n1.getWlan().getAdvancedCustomization().getClientIsolation());
    assertNull(n1.getNetworkVenues().get(0).getClientIsolationAllowlist());

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisherNotEmpty(ddccmOperations),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollectorNotEmpty(viewmodelOperations),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 1.0)
      );
    }

    // if network didn't enable client-isolation, can't set isolation profile by network-venue
    clearMessage();

    log.info("===== update nv1 again");
    NetworkVenue nv1 = getNetworkVenueByNetworkAndVenue(n1.getId(), v1.getId());
    updateEdaNetworkVenue(tenantId, userName, nv1.getId(), map(nv1), clientIsolationAllowlistId);
    assertActivityStatusFail(UPDATE_NETWORK_VENUE, Errors.WIFI_10457, tenantId);

    n1 = (OpenNetwork) getNetwork(n1.getId());
    assertFalse(n1.getWlan().getAdvancedCustomization().getClientIsolation());
    assertNull(n1.getNetworkVenues().get(0).getClientIsolationAllowlist());

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());
  }

  @Test
  public void networkOrNetworkVenueForClientIsolationAllowlist(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");

    ClientIsolationAllowlist allowlist = clientIsolationAllowlist("name");
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_ALLOWLIST, tenantId);

    List<ClientIsolationAllowlist> allowlists = getClientIsolationAllowlists();
    assertEquals(1, allowlists.size());
    String clientIsolationAllowlistId = allowlists.get(0).getId();

    clearMessage();

    // add network + add network-venue with clientIsolationAllowlist

    OpenNetwork n = addOpenNetwork(map(openNetwork("n").generate()));
    edaAddNetworkVenue(tenantId, userName, n.getId(), v.getId(), clientIsolationAllowlistId);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisherNotEmpty(ddccmOperations),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollectorNotEmpty(viewmodelOperations),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 1.0)
      );
    }

    clearMessage();

    // delete network-venue

    NetworkVenue networkVenue = getNetworkVenueByNetwork(n.getId()).get(0);
    deleteEdaNetworkVenue(tenantId, userName, networkVenue.getId());
    assertActivityStatusSuccess(DELETE_NETWORK_VENUE, tenantId);

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisherNotEmpty(ddccmOperations),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollectorNotEmpty(viewmodelOperations),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 0.0)
      );
    }

    clearMessage();

    // add network-venue

    edaAddNetworkVenue(tenantId, userName, n.getId(), v.getId(), clientIsolationAllowlistId);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisherNotEmpty(ddccmOperations),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollectorNotEmpty(viewmodelOperations),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 1.0)
      );
    }

    // delete network

    deleteEdaNetwork(tenantId, userName, n.getId());

    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccmPublisherNotEmpty(ddccmOperations),
          () -> assertDdccmPublisherNoAllowlist(ddccmOperations)
      );

      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertViewmodelCollectorNotEmpty(viewmodelOperations),
          () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
              OpType.MOD, clientIsolationAllowlistId, "name", 0.0)
      );
    }
  }

  @Test
  public void updateClientIsolationAllowlist(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    ClientIsolationAllowlist allowlist = clientIsolationAllowlist("name");

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_ALLOWLIST, tenantId);

    List<ClientIsolationAllowlist> allowlists = getClientIsolationAllowlists();
    assertEquals(1, allowlists.size());
    String clientIsolationAllowlistId = allowlists.get(0).getId();

    // start to update

    allowlist.setName("newname");

    RequestParams rps = new RequestParams()
        .addPathVariable("clientIsolationAllowlistId", clientIsolationAllowlistId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.UPDATE_CLIENT_ISOLATION_ALLOWLIST, userName,
        rps, map(allowlist));
    assertActivityStatusSuccess(UPDATE_CLIENT_ISOLATION_ALLOWLIST, tenantId);

    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccmPublisher(ddccmOperations, 1),
        () -> assertDdccmPublisherAllowlist(ddccmOperations,
            Action.MODIFY, clientIsolationAllowlistId, "newname")
    );

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 1),
        () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
            OpType.MOD, clientIsolationAllowlistId, "newname", 0.0)
    );
  }

  @Test
  public void deleteClientIsolationAllowlist(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");

    ClientIsolationAllowlist allowlist = clientIsolationAllowlist("name");

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_ALLOWLIST, tenantId);

    List<ClientIsolationAllowlist> allowlists = getClientIsolationAllowlists();
    assertEquals(1, allowlists.size());
    String clientIsolationAllowlistId = allowlists.get(0).getId();

    // bind allowlist to network

    OpenNetwork n = addOpenNetwork(map(openNetwork("n").generate()));
    edaAddNetworkVenue(tenantId, userName, n.getId(), v.getId(), clientIsolationAllowlistId);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    // start to delete

    // not allow to delete allowlist when it is being used

    RequestParams rps = new RequestParams()
        .addPathVariable("clientIsolationAllowlistId", clientIsolationAllowlistId);
    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DELETE_CLIENT_ISOLATION_ALLOWLIST, userName,
        rps, "");
    assertActivityStatusFail(DELETE_CLIENT_ISOLATION_ALLOWLIST, Errors.WIFI_10454, tenantId);

    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    // remove binding

    NetworkVenue nv = getNetworkVenueByNetworkAndVenue(n.getId(), v.getId());
    updateEdaNetworkVenue(tenantId, userName, nv.getId(), map(nv), null);
    assertActivityStatusSuccess(UPDATE_NETWORK_VENUE, tenantId);

    // delete again

    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DELETE_CLIENT_ISOLATION_ALLOWLIST, userName,
        rps, "");
    assertActivityStatusSuccess(DELETE_CLIENT_ISOLATION_ALLOWLIST, tenantId);

    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccmPublisher(ddccmOperations, 1),
        () -> assertDdccmPublisherAllowlist(ddccmOperations,
            Action.DELETE, clientIsolationAllowlistId, null)
    );

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 1),
        () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
            OpType.DEL, clientIsolationAllowlistId, null, null)
    );
  }

  @Test
  public void deleteClientIsolationAllowlists(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    ClientIsolationAllowlist a1 = clientIsolationAllowlist("a1");
    ClientIsolationAllowlist a2 = clientIsolationAllowlist("a2");

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(a1));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_ALLOWLIST, tenantId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(a2));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_ALLOWLIST, tenantId);

    List<ClientIsolationAllowlist> allowlists = getClientIsolationAllowlists();
    assertEquals(2, allowlists.size());
    List<String> clientIsolationAllowlistIds = allowlists.stream()
        .map(ClientIsolationAllowlist::getId)
        .collect(Collectors.toList());

    // start to delete

    sendWifiCfgRequest(
        tenantId, randomTxId(), CfgAction.DELETE_CLIENT_ISOLATION_ALLOWLISTS, userName, clientIsolationAllowlistIds);
    assertActivityStatusSuccess(DELETE_CLIENT_ISOLATION_ALLOWLISTS, tenantId);

    assertEquals(0, getClientIsolationAllowlists().size());

    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccmPublisher(ddccmOperations, 2),
        () -> assertDdccmPublisherAllowlist(ddccmOperations,
            Action.DELETE, clientIsolationAllowlistIds.get(0), null),
        () -> assertDdccmPublisherAllowlist(ddccmOperations,
            Action.DELETE, clientIsolationAllowlistIds.get(1), null)
    );

    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertViewmodelCollector(viewmodelOperations, 2),
        () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
            OpType.DEL, clientIsolationAllowlistIds.get(0), null, null),
        () -> assertViewmodelCollectorAllowlist(viewmodelOperations,
            OpType.DEL, clientIsolationAllowlistIds.get(1), null, null)
    );
  }

  @Test
  public void clientIsolationAllowlistUsages(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v1 = createVenue(tenant, "v1");
    Venue v2 = createVenue(tenant, "v2");
    ClientIsolationAllowlist a1 = clientIsolationAllowlist("a1");
    ClientIsolationAllowlist a2 = clientIsolationAllowlist("a2");

    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(a1));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_ALLOWLIST, tenantId);
    sendWifiCfgRequest(tenantId, randomTxId(), CfgAction.ADD_CLIENT_ISOLATION_ALLOWLIST,
        userName, map(a2));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_ALLOWLIST, tenantId);

    List<ClientIsolationAllowlist> allowlists = getClientIsolationAllowlists();
    assertEquals(2, allowlists.size());
    String clientIsolationAllowlistId0 = allowlists.get(0).getId();
    String clientIsolationAllowlistId1 = allowlists.get(1).getId();

    {
      ClientIsolationAllowlistVenueQueryResponse r1 = getVenueUsage(clientIsolationAllowlistId0,
          new ClientIsolationAllowlistVenueQueryRequest());
      assertEquals(0, r1.getTotalCount().intValue());
      VenueClientIsolationAllowlistQueryResponse r2 = getClientIsolationAllowlistUsageByVenue(v1.getId(),
          new VenueClientIsolationAllowlistQueryRequest());
      assertEquals(0, r2.getTotalCount().intValue());
    }

    // apply them to venues

    // allowlist0 - v1,v2
    // allowlist1 - v2

    OpenNetwork v1_open = addOpenNetwork(map(openNetwork("v1_open").generate()));
    OpenNetwork v2_open = addOpenNetwork(map(openNetwork("v2_open").generate()));
    PskNetwork v1_psk = addPskNetwork(map(pskNetwork("v1_psk").generate()));
    PskNetwork v2_psk = addPskNetwork(map(pskNetwork("v2_psk").generate()));
    edaAddNetworkVenue(tenantId, userName, v1_open.getId(), v1.getId(), clientIsolationAllowlistId0);
    edaAddNetworkVenue(tenantId, userName, v2_open.getId(), v2.getId(), clientIsolationAllowlistId0);
    edaAddNetworkVenue(tenantId, userName, v1_psk.getId(), v1.getId(), clientIsolationAllowlistId0);
    edaAddNetworkVenue(tenantId, userName, v2_psk.getId(), v2.getId(), clientIsolationAllowlistId1);

    assertThrows(CommonException.class, () -> {
      ClientIsolationAllowlistVenueQueryRequest request_xxx = new ClientIsolationAllowlistVenueQueryRequest();
      request_xxx.setSortField("xxx"); // not allowed
      getVenueUsage(clientIsolationAllowlistId0, request_xxx);
    });

    {
      ClientIsolationAllowlistVenueQueryRequest request_default = new ClientIsolationAllowlistVenueQueryRequest();
      ClientIsolationAllowlistVenueQueryResponse response_default =
          getVenueUsage(clientIsolationAllowlistId0, request_default);
      assertEquals(2, response_default.getTotalCount().longValue());
      assertEquals("v1", response_default.getData().get(0).getVenueName());
      assertEquals(2, response_default.getData().get(0).getNetworkCount().longValue());
      assertEquals(1, response_default.getData().get(1).getNetworkCount().longValue());

      ClientIsolationAllowlistVenueQueryRequest request_name_desc = new ClientIsolationAllowlistVenueQueryRequest();
      request_name_desc.setSortField("venueName");
      request_name_desc.setSortOrder(SortOrderEnum.DESC);
      ClientIsolationAllowlistVenueQueryResponse response_name_desc =
          getVenueUsage(clientIsolationAllowlistId0, request_name_desc);
      assertEquals(2, response_name_desc.getTotalCount().longValue());
      assertEquals("v2", response_name_desc.getData().get(0).getVenueName());

      ClientIsolationAllowlistVenueQueryRequest request_v1 = new ClientIsolationAllowlistVenueQueryRequest();
      request_v1.setSearchString("v1");
      ClientIsolationAllowlistVenueQueryResponse response_v1 =
          getVenueUsage(clientIsolationAllowlistId0, request_v1);
      assertEquals(1, response_v1.getTotalCount().longValue());
      assertEquals("v1", response_v1.getData().get(0).getVenueName());
    }

    assertThrows(CommonException.class, () -> {
      VenueClientIsolationAllowlistQueryRequest request_xxx = new VenueClientIsolationAllowlistQueryRequest();
      request_xxx.setSortField("xxx"); // not allowed
      getClientIsolationAllowlistUsageByVenue(v2.getId(), request_xxx);
    });

    {
      VenueClientIsolationAllowlistQueryRequest request_default = new VenueClientIsolationAllowlistQueryRequest();
      VenueClientIsolationAllowlistQueryResponse response_default =
          getClientIsolationAllowlistUsageByVenue(v2.getId(), request_default);
      assertEquals(2, response_default.getTotalCount().longValue());
      assertEquals("a1", response_default.getData().get(0).getName());

      VenueClientIsolationAllowlistQueryRequest request_name_desc = new VenueClientIsolationAllowlistQueryRequest();
      request_name_desc.setSortField("name");
      request_name_desc.setSortOrder(SortOrderEnum.DESC);
      VenueClientIsolationAllowlistQueryResponse response_name_desc =
          getClientIsolationAllowlistUsageByVenue(v2.getId(), request_name_desc);
      assertEquals(2, response_name_desc.getTotalCount().longValue());
      assertEquals("a2", response_name_desc.getData().get(0).getName());
    }
  }

  private static void assertDdccmPublisherNotEmpty(
      List<Operation> operations) {
    assertTrue(!operations.isEmpty());
  }

  private static void assertDdccmPublisher(
      List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private static void assertDdccmPublisherAllowlist(
      List<Operation> operations, Action action, String allowlistId, String allowlistName) {
    assertTrue(operations.stream()
        .filter(o -> o.getAction() == action)
        .filter(Operation::hasVenueClientIsolationWhitelist)
        .map(Operation::getVenueClientIsolationWhitelist)
        .filter(v -> allowlistName == null || allowlistName.equals(v.getName()))
        .anyMatch(v -> allowlistId.equals(v.getId())));
  }

  private static void assertDdccmPublisherNoAllowlist(
      List<Operation> operations) {
    assertTrue(operations.stream()
            .noneMatch(Operation::hasVenueClientIsolationWhitelist));
  }

  private static void assertViewmodelCollectorNotEmpty(
      List<Operations> operations) {
    assertTrue(!operations.isEmpty(), "viewmodel collector is empty");
  }

  private static void assertViewmodelCollector(
      List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size(),
        "viewmodel collector size not equal, expected=" + opsSize + " actual=" + operations.size());
  }

  private static void assertViewmodelCollectorAllowlist(
      List<Operations> operations, OpType opType, String allowlistId, String allowlistName, Double scope) {
    assertTrue(operations.stream()
        .filter(o -> CLIENT_ISOLATION_ALLOWLIST_INDEX_NAME.equals(o.getIndex()))
        .filter(o -> allowlistId.equals(o.getId()))
        .filter(o -> o.getOpType() == opType)
        .filter(o -> allowlistName == null || allowlistName.equals(o.getDocMap().get(EsConstants.Key.NAME).getStringValue()))
        .filter(o -> scope == null || o.getDocMap().get(EsConstants.Key.VENUE_IDS).getListValue().getValuesCount() == scope)
        .findAny().isPresent(), "viewmodel collector allow list opType=" + opType + ", allowlistId=" + allowlistId + ",allowlistName="+ allowlistName + ", scope="+ scope + ", operations=" + operations);
  }
}
