package com.ruckus.cloud.wifi.notification;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.service.ApVersionService;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApCountProjection;
import com.ruckus.cloud.wifi.servicemodel.projection.VenueApModelCountProjection;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.collections.ListUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;

@WifiUnitTest
class VenueApCountExecutorFactoryTest {

  @MockBean
  private UpgradeScheduleRepository upgradeScheduleRepository;

  @MockBean
  private ApRepository apRepository;

  @MockBean
  private ApVersionService apVersionService;

  @SpyBean
  private VenueApCountExecutorFactory venueApCountExecutorFactory;

  @Test
  public void testVenueAllApCountExecutor() {
    List<Venue> venues = mockVenues(5);
    List<VenueApCountProjection> projections = mockApCountProjections(venues, 3);
    when(apRepository.findVenueApCountByVenueIds(any()))
        .thenReturn(projections);

    Map<String, Integer> venueIdApCountMap = venueApCountExecutorFactory.createAllApCountExecutor(venues)
        .execute();

    Assertions.assertThat(venueIdApCountMap)
        .hasSize(5)
        .allSatisfy((k,v) ->
            Assertions.assertThat(v)
                .isEqualTo(3));
  }

  @Test
  public void testVenueImpactApCountExecutor() {
    String tenantId = "tenantId";
    List<Venue> venues = mockVenues(5);
    List<VenueApCountProjection> projections = mockApCountProjections(venues, 3);
    VenueUpgradeVersionsMapping versionMapping =
        VenueUpgradeVersionsMapping.createByVenueIdAndVersion(venues, List.of(new ApVersion()));
    Map<String, List<String>> venueApModelMapping = mockVenueApModelMap(venues, "R550", "T600");
    when(apVersionService.getVenueImpactedModelsMapWhenUpgradeToVersions(eq(tenantId), any()))
        .thenReturn(venueApModelMapping);
    when(apRepository.findVenueApCountByVenueIdsAndApModels(any(), any()))
        .thenReturn(projections);

    Map<String, Integer> venueApCountMapping = venueApCountExecutorFactory.createImpactApCountExecutor(
        tenantId, venues, versionMapping).execute();

    verify(apRepository).findVenueApCountByVenueIdsAndApModels(any(), any());
    Assertions.assertThat(venueApCountMapping)
        .hasSize(5)
        .allSatisfy((k,v) ->
            Assertions.assertThat(v)
                .isEqualTo(3)
        );
  }

  @Test
  public void testVenueImpactApCountExecutorCanSeparateModelGroupToQuery() {
    String tenantId = "tenantId";
    List<Venue> venues1 = mockVenues(3);
    List<Venue> venues2 = mockVenues(3);
    List<Venue> venueTotal = ListUtils.union(venues1, venues2);
    List<VenueApCountProjection> projections = mockApCountProjections(venueTotal, 5);
    Map<String, List<String>> venueApModelMappingTotal = mockVenueApModelMap(venues1, "R550", "T600");
    Map<String, List<String>> venueApModelMapping2 = mockVenueApModelMap(venues2, "T600");
    venueApModelMappingTotal.putAll(venueApModelMapping2);
    VenueUpgradeVersionsMapping versionMapping =
        VenueUpgradeVersionsMapping.createByVenueIdAndVersion(venues1, List.of(new ApVersion()));
    when(apVersionService.getVenueImpactedModelsMapWhenUpgradeToVersions(eq(tenantId), any()))
        .thenReturn(venueApModelMappingTotal);
    when(apRepository.findVenueApCountByVenueIdsAndApModels(any(), any()))
        .thenReturn(projections);

    venueApCountExecutorFactory.createImpactApCountExecutor(
        tenantId, venues1, versionMapping)
        .execute();

    verify(apRepository, times(2)).findVenueApCountByVenueIdsAndApModels(any(), any());
  }

  @Test
  void testVenueImpactApCountByModelsExecutor() {
    List<Venue> venues = mockVenues(5);
    List<VenueApModelCountProjection> projections = mockUpgradeImpactedApCountProjections(
        venues, 3);
    when(upgradeScheduleRepository.findImpactedApCountByVenueIdsAndUpgradeScheduleStatus(any(),
        any())).thenReturn(projections);

    Map<String, Integer> venueApCountMapping = venueApCountExecutorFactory.createImpactApCountByModelsExecutor(
        venues).execute();

    verify(upgradeScheduleRepository).findImpactedApCountByVenueIdsAndUpgradeScheduleStatus(any(),
        any());
    Assertions.assertThat(venueApCountMapping)
        .hasSize(5)
        .allSatisfy((k, v) ->
            Assertions.assertThat(v)
                .isEqualTo(3)
        );
  }

  @Test
  void testVenueImpactApCountByModelsMapExecutor() {
    List<Venue> venues = mockVenues(5);
    Map<String, Integer> venueApCountMap = mockUpgradeImpactedApCountProjections(
        venues, 3).stream().collect(
        Collectors.toMap(VenueApModelCountProjection::getVenueId,
            p -> p.getApCount().intValue()));

    Map<String, Integer> venueApCountMapping = venueApCountExecutorFactory.createImpactApCountByMappingExecutor(
        venueApCountMap).execute();

    Assertions.assertThat(venueApCountMapping)
        .hasSize(5)
        .allSatisfy((k, v) ->
            Assertions.assertThat(v)
                .isEqualTo(3)
        );
  }

  private List<Venue> mockVenues(int number) {
    List<Venue> venues = new LinkedList<>();
    for(int i=0; i<number; i++) {
      Venue venue = new Venue();
      venue.setId(UUID.randomUUID().toString());
      venues.add(venue);
    }
    return venues;
  }

  private List<VenueApCountProjection> mockApCountProjections(List<Venue> venues, long apCount) {
    return venues.stream()
        .map(v -> new VenueApCountProjection(v.getId(), apCount))
        .toList();
  }

  private Map<String, List<String>> mockVenueApModelMap(List<Venue> venues, String... models) {
    List<String> apModels = Arrays.asList(models);
    return venues.stream()
        .collect(Collectors.toMap(Venue::getId, v -> apModels));
  }

  private List<VenueApModelCountProjection> mockUpgradeImpactedApCountProjections(List<Venue> venues, int apCount) {
    return venues.stream().map(
        v -> (VenueApModelCountProjection) new VenueApModelCountProjectionImpl(
            v.getId(), "R560", (long) apCount)).toList();
  }

  static class VenueApModelCountProjectionImpl implements
      VenueApModelCountProjection {

    private final String venueId;
    private final String apModel;
    private final Long apCount;

    public VenueApModelCountProjectionImpl(String venueId, String apModel,
        Long apCount) {
      this.venueId = venueId;
      this.apModel = apModel;
      this.apCount = apCount;
    }

    @Override
    public String getVenueId() {
      return this.venueId;
    }

    @Override
    public String getApModel() {
      return this.apModel;
    }

    @Override
    public Long getApCount() {
      return this.apCount;
    }
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    public VenueApCountExecutorFactory venueApCountExecutorFactory(UpgradeScheduleRepository upgradeScheduleRepository,
        ApRepository apRepository,
        ApVersionService apVersionService) {
      return new VenueApCountExecutorFactory(upgradeScheduleRepository, apRepository, apVersionService);
    }
  }
}