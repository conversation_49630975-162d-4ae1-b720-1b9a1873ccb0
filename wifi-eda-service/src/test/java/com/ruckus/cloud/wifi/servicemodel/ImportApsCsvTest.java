package com.ruckus.cloud.wifi.servicemodel;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.ruckus.cloud.wifi.service.core.util.ImportConfig;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import java.io.File;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.Test;

@WifiUnitTest
public class ImportApsCsvTest {

  @Test
  public void testGetImportErros() {
    ImportApsCsv apsCsv = generateImportApsCsv("aps_import_invalid_venue.csv", randomTxId());
    String message = String.format(ImportConfig.TOTAL_REMAINING_DEVICES_MESSAGE_FORMAT, 1);
    apsCsv.setLicenseErrorMessage(message);
    apsCsv.addImportException(ImportConfig.LICENSES_NUMBER_ERROR, message);

    assertNotNull(apsCsv);
    assertThatNoException().isThrownBy(apsCsv::getImportErrors);
  }

  private ImportApsCsv generateImportApsCsv(String fileName, String requestId) {
    File csvFile = new File("./src/test/resources/csv/" + fileName);
    String fileId = "fileId";
    String downloadUrl = "http://import_aps.download.url";
    AtomicReference<ImportApsCsv> importApsCsv = new AtomicReference<>();
    assertThatNoException().isThrownBy(() ->
        importApsCsv.set(new ImportApsCsv(
            "TENANT_ID",
            "VENUE_NAME",
            requestId,
            fileId,
            fileName,
            downloadUrl,
            csvFile)));

    return importApsCsv.get();
  }
}
