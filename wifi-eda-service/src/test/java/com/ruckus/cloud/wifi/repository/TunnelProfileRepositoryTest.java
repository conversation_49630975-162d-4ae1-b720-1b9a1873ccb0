package com.ruckus.cloud.wifi.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelNetworkSegmentTypeEnum;
import com.ruckus.cloud.wifi.servicemodel.projection.TunnelProfileTenantIdProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

@Tag("TunnelServiceProfileTest")
@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tunnel_profile (id, name, tenant, mtu_type, mtu_size, force_fragmentation, type, is_default) VALUES (
        'afc284d992694d5c9d7a2fcf2289a0bd',
        'tunnel profile 1',
        '4c8279f79307415fa9e4c88a1819f0fc',
        'MANUAL',
        1450,
        false,
        'VLAN_VXLAN',
        true);
    """)
class TunnelProfileRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String TUNNEL_PROFILE_ID = "afc284d992694d5c9d7a2fcf2289a0bd";
  private static final String TUNNEL_PROFILE_NAME = "tunnel profile 1";

  @Autowired
  private TunnelProfileRepository target;

  @Test
  void findByIsDefaultAndTenantIdTest() {
    assertThat(target.findByIsDefaultAndTenantId(true, TENANT_ID)).hasSize(1);
    assertThat(target.findByIsDefaultAndTenantId(false, TENANT_ID)).isEmpty();
  }


  @Test
  void findByIdInAndTenantIdTest() {
    assertThat(target.findByIdInAndTenantId(List.of(TUNNEL_PROFILE_ID), TENANT_ID)).hasSize(1);
    assertThat(target.findByIdInAndTenantId(List.of(), TENANT_ID)).isEmpty();
    assertThat(target.findByIdInAndTenantId(List.of("non_exist_id"), TENANT_ID)).isEmpty();
  }

  @Test
  void findByIdAndTypeAndIsDefaultAndTenantIdTest() {
    assertThat(
        target.findByIdAndTypeAndIsDefaultAndTenantId(TUNNEL_PROFILE_ID, TunnelNetworkSegmentTypeEnum.VLAN_VXLAN,
            true, TENANT_ID)).isPresent();
    assertThat(
        target.findByIdAndTypeAndIsDefaultAndTenantId(TUNNEL_PROFILE_ID, TunnelNetworkSegmentTypeEnum.VLAN_VXLAN,
            false, TENANT_ID)).isEmpty();
  }

  @Test
  void existsByTenantIdAndNameAndIdNotTest() {
    assertThat(
        target.existsByTenantIdAndNameAndIdNot(TENANT_ID, TUNNEL_PROFILE_NAME, "12345")).isTrue();
    assertThat(target.existsByTenantIdAndNameAndIdNot(TENANT_ID, TUNNEL_PROFILE_NAME,
        TUNNEL_PROFILE_ID)).isFalse();
    assertThat(
        target.existsByTenantIdAndNameAndIdNot(TENANT_ID, "new name", TUNNEL_PROFILE_ID)).isFalse();
  }


  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant-1');
      INSERT INTO tenant (id) VALUES ('tenant-2');
      INSERT INTO ipsec_profile (id, tenant) VALUES ('ipsec-2', 'tenant-2');
      INSERT INTO tunnel_profile (id, name, tenant, is_default) VALUES ('tp-1', 'TP1', 'tenant-1', false);
      INSERT INTO tunnel_profile (id, name, tenant, is_default, ipsec_profile) VALUES ('tp-2', 'TP2', 'tenant-2', false, 'ipsec-2');
      """)
  @Test
  void findByTenantIdAndIpsecProfileIdTest() {
    assertThat(target.findByTenantIdAndIpsecProfileId("tenant-1", "ipsec-1")).isEmpty();
    assertThat(target.findByTenantIdAndIpsecProfileId("tenant-2", "ipsec-2")).hasSize(1);
  }

  @Sql(statements = """
      INSERT INTO tenant (id) VALUES ('tenant-1');
      INSERT INTO tunnel_profile (id, name, tenant, is_default) VALUES ('tp-1', 'TP1', 'tenant-1', false);
      """)
  @Test
  void findAllUnusedDefaultTunnelProfileIds_shouldNotReturnNonDefaultProfile() {
    assertThat(target.findAllUnusedDefaultTunnelProfileProjections()).isEmpty();
  }

  @Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenant-1');
    INSERT INTO tenant (id) VALUES ('tenant-2');
    INSERT INTO tenant (id) VALUES ('tenant-3');
    INSERT INTO tunnel_profile (id, name, tenant, is_default) VALUES ('tp-1', 'TP1', 'tenant-1', true);
    INSERT INTO tunnel_profile (id, name, tenant, is_default) VALUES ('tp-2', 'TP2', 'tenant-2', true);
    """)
  @Test
  void findAllUnusedDefaultTunnelProfileIds_shouldReturnUnusedDefaultProfile() {
    assertThat(target.findAllUnusedDefaultTunnelProfileProjections())
        .map(TunnelProfileTenantIdProjection::tunnelProfileId)
        .containsExactly("tp-1", "tp-2");

  }

  @Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenant-1');
    INSERT INTO tunnel_profile (id, name, tenant, is_default) VALUES ('tp-1', 'TP1', 'tenant-1', true);
    INSERT INTO sd_lan_profile (id, tenant) VALUES ('sdp-1', 'tenant-1');
    INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, tunnel_profile) 
      VALUES ('rs-1', 'tenant-1', 'sdp-1', 'tp-1');
    """)
  @Test
  void findAllUnusedDefaultTunnelProfileIds_shouldNotReturnDefaultProfileUsedBySdLan() {
    assertThat(target.findAllUnusedDefaultTunnelProfileProjections()).isEmpty();
  }

  @Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenant-1');
    INSERT INTO tunnel_profile (id, name, tenant, is_default) VALUES ('tp-1', 'TP1', 'tenant-1', false);
    INSERT INTO tunnel_profile (id, name, tenant, is_default) VALUES ('tp-2', 'TP2', 'tenant-1', true);
    INSERT INTO sd_lan_profile (id, tenant) VALUES ('sdp-1', 'tenant-1');
    INSERT INTO sd_lan_profile_regular_setting (id, tenant, sd_lan_profile, tunnel_profile, guest_traffic_tunnel_profile) 
      VALUES ('rs-1', 'tenant-1', 'sdp-1', 'tp-1', 'tp-2');
    """)
  @Test
  void findAllUnusedDefaultTunnelProfileIds_shouldNotReturnDefaultProfileUsedBySdLanDmz() {
    assertThat(target.findAllUnusedDefaultTunnelProfileProjections()).isEmpty();
  }

  @Sql(statements = """
    INSERT INTO tenant (id) VALUES ('tenant-1');
    INSERT INTO tunnel_profile (id, name, tenant, is_default) VALUES ('tp-1', 'TP1', 'tenant-1', true);
    INSERT INTO pin_profile (id, tenant) VALUES ('pin-1', 'tenant-1');
    INSERT INTO pin_profile_regular_setting (id, tenant, pin_profile, tunnel_profile) 
      VALUES ('rs-1', 'tenant-1', 'pin-1', 'tp-1');
    """)
  @Test
  void findAllUnusedDefaultTunnelProfileIds_shouldNotReturnDefaultProfileUsedByPin() {
    assertThat(target.findAllUnusedDefaultTunnelProfileProjections()).isEmpty();
  }

  @Test
  void existsByIdAndTenantIdAndIsTemplateFalse_shouldReturnTrue() {
    assertThat(target.existsByIdAndTenantIdAndIsTemplate(TUNNEL_PROFILE_ID, TENANT_ID, false))
        .isTrue();
  }
}

