package com.ruckus.cloud.wifi.integration.accesscontrolprofile;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.DevicePolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L2AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.NetworkVenueTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.time.Duration;
import java.util.List;
import java.util.stream.Stream;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
public class ConsumeActivateAccessControlProfileOnWifiNetworkRequestTest {

  @Autowired private ExtendedMessageUtil messageUtil;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  class WhenConsumeActivateAccessControlProfileOnWifiNetworkMessage {

    private List<NetworkVenue> networkVenues;
    private String accessControlProfileId;
    private String networkId;

    @BeforeEach
    void beforeEach(Tenant tenant, AccessControlProfile accessControlProfile, Network network) {
      accessControlProfileId = accessControlProfile.getId();
      networkId = network.getId();
      networkVenues =
          Stream.generate(() -> VenueTestFixture.randomVenue(tenant))
              .limit(3)
              .peek(venue -> repositoryUtil.createOrUpdate(venue, tenant.getId(), randomTxId()))
              .map(venue -> NetworkVenueTestFixture.randomNetworkVenue(network, venue))
              .peek(
                  networkVenue ->
                      repositoryUtil.createOrUpdate(networkVenue, tenant.getId(), randomTxId()))
              .toList();
    }

    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("accessControlProfileId", accessControlProfileId)
          .addPathVariable("wifiNetworkId", networkId);
    }

    @Nested
    class GivenNetworkExists {
      @Nested
      class GivenHasRespectivePolicies {
        @BeforeEach
        void beforeEach(
            Tenant tenant,
            Network network,
            AccessControlProfile accessControlProfile,
            L2AclPolicy l2AclPolicy,
            L3AclPolicy l3AclPolicy,
            DevicePolicy devicePolicy,
            ApplicationPolicy applicationPolicy) {
          final var advancedCustomization = network.getWlan().getAdvancedCustomization();
          advancedCustomization.setAccessControlProfile(null);
          advancedCustomization.setAccessControlEnable(false);
          advancedCustomization.setRespectiveAccessControl(true);
          advancedCustomization.setL2AclEnable(true);
          advancedCustomization.setL2AclPolicy(l2AclPolicy);
          advancedCustomization.setL3AclEnable(true);
          advancedCustomization.setL3AclPolicy(l3AclPolicy);
          advancedCustomization.setDevicePolicy(devicePolicy);
          advancedCustomization.setApplicationPolicyEnable(true);
          advancedCustomization.setApplicationPolicy(applicationPolicy);
          repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
        }

        @Test
        void thenShouldClearRespectivePolicies(
            Tenant tenant,
            L2AclPolicy l2AclPolicy,
            L3AclPolicy l3AclPolicy,
            DevicePolicy devicePolicy,
            ApplicationPolicy applicationPolicy) {
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.ACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK,
              randomName(),
              requestParams(),
              "");

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .hasSize(networkVenues.size())
              .allSatisfy(
                  operation ->
                      assertThat(operation)
                          .matches(o -> o.getAction() == Action.MODIFY)
                          .extracting(Operation::getWlanVenue)
                          .matches(wlanVenue -> !wlanVenue.hasL2ACLId())
                          .matches(wlanVenue -> !wlanVenue.hasL3ACLId())
                          .matches(wlanVenue -> !wlanVenue.hasDevicePolicyId())
                          .matches(wlanVenue -> !wlanVenue.hasAppPolicyId())
                          .satisfies(
                              wlanVenue -> assertThat(wlanVenue.getWlanId()).isEqualTo(networkId))
                          .satisfies(
                              wlanVenue ->
                                  assertThat(wlanVenue.getFirewallProfileId().getValue())
                                      .isEqualTo(accessControlProfileId)))
              .extracting(Operation::getId)
              .allMatch(id -> networkVenues.stream().anyMatch(nv -> id.equals(nv.getId())));

          assertThat(
                  messageCaptors
                      .getCmnCfgCollectorMessageCaptor()
                      .getValue(tenant.getId(), Duration.ofSeconds(1)))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .hasSize(5)
              .anySatisfy(
                  operation ->
                      assertThat(operation)
                          .matches(o -> o.getOpType() == OpType.MOD)
                          .matches(o -> o.getId().equals(accessControlProfileId)))
              .anySatisfy(
                  operation ->
                      assertThat(operation)
                          .matches(o -> o.getOpType() == OpType.MOD)
                          .matches(o -> o.getId().equals(l2AclPolicy.getId())))
              .anySatisfy(
                  operation ->
                      assertThat(operation)
                          .matches(o -> o.getOpType() == OpType.MOD)
                          .matches(o -> o.getId().equals(l3AclPolicy.getId())))
              .anySatisfy(
                  operation ->
                      assertThat(operation)
                          .matches(o -> o.getOpType() == OpType.MOD)
                          .matches(o -> o.getId().equals(devicePolicy.getId())))
              .anySatisfy(
                  operation ->
                      assertThat(operation)
                          .matches(o -> o.getOpType() == OpType.MOD)
                          .matches(o -> o.getId().equals(applicationPolicy.getId())));

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(
                  a ->
                      a.getStep()
                          .equals(ApiFlowNames.ACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK));
        }
      }

      @Nested
      class GivenHasNoRespectivePolicy {
        @BeforeEach
        void beforeEach(Tenant tenant, Network network, AccessControlProfile accessControlProfile) {
          network.getWlan().getAdvancedCustomization().setAccessControlProfile(null);
          network.getWlan().getAdvancedCustomization().setAccessControlEnable(false);
          network.getWlan().getAdvancedCustomization().setRespectiveAccessControl(false);
          repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());
        }

        @Test
        void givenAccessControlProfileActivatedOnNetwork(Tenant tenant) {
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.ACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK,
              randomName(),
              requestParams(),
              "");

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .hasSize(networkVenues.size())
              .allSatisfy(
                  operation ->
                      assertThat(operation)
                          .matches(o -> o.getAction() == Action.MODIFY)
                          .extracting(Operation::getWlanVenue)
                          .matches(wlanVenue -> !wlanVenue.hasL2ACLId())
                          .matches(wlanVenue -> !wlanVenue.hasL3ACLId())
                          .matches(wlanVenue -> !wlanVenue.hasDevicePolicyId())
                          .matches(wlanVenue -> !wlanVenue.hasAppPolicyId())
                          .satisfies(
                              wlanVenue -> assertThat(wlanVenue.getWlanId()).isEqualTo(networkId))
                          .satisfies(
                              wlanVenue ->
                                  assertThat(wlanVenue.getFirewallProfileId().getValue())
                                      .isEqualTo(accessControlProfileId)))
              .extracting(Operation::getId)
              .allMatch(id -> networkVenues.stream().anyMatch(nv -> id.equals(nv.getId())));

          assertThat(
                  messageCaptors
                      .getCmnCfgCollectorMessageCaptor()
                      .getValue(tenant.getId(), Duration.ofSeconds(1)))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .hasSize(1)
              .first()
              .matches(o -> o.getOpType() == OpType.MOD)
              .matches(o -> o.getId().equals(accessControlProfileId));

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(
                  a ->
                      a.getStep()
                          .equals(ApiFlowNames.ACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK));
        }
      }

      @Nested
      class GivenHasAccessControlProfile {
        String accessControlProfileId2;

        @BeforeEach
        void beforeEach(Tenant tenant, Network network) {
          AccessControlProfile accessControlProfile =
              AccessControlProfileTestFixture.randomAccessControlProfile();
          accessControlProfile =
              repositoryUtil.createOrUpdate(accessControlProfile, tenant.getId());
          accessControlProfileId2 = accessControlProfile.getId();
          network
              .getWlan()
              .getAdvancedCustomization()
              .setAccessControlProfile(accessControlProfile);
          network.getWlan().getAdvancedCustomization().setAccessControlEnable(true);
          network.getWlan().getAdvancedCustomization().setRespectiveAccessControl(false);
          repositoryUtil.createOrUpdate(network, tenant.getId());
        }

        @Test
        void givenAccessControlProfileActivatedOnNetwork(Tenant tenant) {
          messageUtil.sendWifiCfgRequest(
              tenant.getId(),
              txCtxExtension.getRequestId(),
              CfgAction.ACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK,
              randomName(),
              requestParams(),
              "");

          assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(
                  m -> m.getPayload().getOperationsList(),
                  InstanceOfAssertFactories.list(Operation.class))
              .isNotEmpty()
              .hasSize(networkVenues.size())
              .allSatisfy(
                  operation ->
                      assertThat(operation)
                          .matches(o -> o.getAction() == Action.MODIFY)
                          .extracting(Operation::getWlanVenue)
                          .matches(wlanVenue -> !wlanVenue.hasL2ACLId())
                          .matches(wlanVenue -> !wlanVenue.hasL3ACLId())
                          .matches(wlanVenue -> !wlanVenue.hasDevicePolicyId())
                          .matches(wlanVenue -> !wlanVenue.hasAppPolicyId())
                          .satisfies(
                              wlanVenue -> assertThat(wlanVenue.getWlanId()).isEqualTo(networkId))
                          .satisfies(
                              wlanVenue ->
                                  assertThat(wlanVenue.getFirewallProfileId().getValue())
                                      .isEqualTo(accessControlProfileId)))
              .extracting(Operation::getId)
              .allMatch(id -> networkVenues.stream().anyMatch(nv -> id.equals(nv.getId())));

          assertThat(
                  messageCaptors
                      .getCmnCfgCollectorMessageCaptor()
                      .getValue(tenant.getId(), Duration.ofSeconds(1)))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .extracting(
                  ViewmodelCollector::getOperationsList,
                  InstanceOfAssertFactories.list(Operations.class))
              .hasSize(2)
              .allMatch(o -> o.getOpType() == OpType.MOD)
              .extracting(Operations::getId)
              .containsExactlyInAnyOrder(accessControlProfileId, accessControlProfileId2);

          assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenant.getId()))
              .isNotNull()
              .extracting(KafkaProtoMessage::getPayload)
              .matches(a -> a.getStatus() == CfgStatus.ConfigurationStatus.Status.OK)
              .matches(
                  a ->
                      a.getStep()
                          .equals(ApiFlowNames.ACTIVATE_ACCESS_CONTROL_PROFILE_ON_WIFI_NETWORK));
        }
      }
    }
  }
}
