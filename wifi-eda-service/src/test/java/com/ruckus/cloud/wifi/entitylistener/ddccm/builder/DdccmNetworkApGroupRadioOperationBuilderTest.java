package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_EDA_WPA3_DSAE_TOGGLE;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.google.protobuf.StringValue;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.DpskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroupRadio;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VlanPool;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.service.core.tx.TxChangesReader;
import com.ruckus.cloud.wifi.service.core.tx.entity.DeletedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.ModifiedTxEntity;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@Tag("ApGroupTest")
@WifiUnitTest
public class DdccmNetworkApGroupRadioOperationBuilderTest {

  @Autowired
  private DdccmNetworkApGroupRadioOperationBuilder builder;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyMspTenantId");

  @Test
  public void testAddNetworkApGroupRadio() {
    var networkApGroupRadio = networkApGroupRadio(false);
    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new NewTxEntity(networkApGroupRadio))).when(txChanges).getNewEntities();
    var operations = builder.build(new NewTxEntity<>(networkApGroupRadio), emptyTxChanges());
    assertEquals(1, operations.size());
    assertNetworkApGroupOperation(Action.ADD, networkApGroupRadio,
        operations.stream().filter(op -> networkApGroupRadio.getId().equals(op.getId())).findFirst()
            .get(), false);
  }

  @Test
  @FeatureFlag(enable = WIFI_EDA_WPA3_DSAE_TOGGLE)
  public void testAddDsaeNetworkApGroupRadio() {
    var networkApGroupRadio = networkApGroupRadio(true);
    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new NewTxEntity(networkApGroupRadio))).when(txChanges).getNewEntities();
    var operations = builder.build(new NewTxEntity<>(networkApGroupRadio), emptyTxChanges());
    assertEquals(1, operations.size());
    assertNetworkApGroupOperation(Action.ADD, networkApGroupRadio,
        operations.stream().filter(op -> networkApGroupRadio.getId().equals(op.getId())).findFirst()
            .get(), true);
  }

  @Test
  public void testModifyNetworkApGroupRadio() {
    var networkApGroupRadio = networkApGroupRadio(true);
    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new ModifiedTxEntity(networkApGroupRadio, Collections.emptySet())))
        .when(txChanges).getModifiedEntities();
    var operations = builder
        .build(new ModifiedTxEntity<>(networkApGroupRadio, Collections.emptySet()), emptyTxChanges());
    assertEquals(1, operations.size());
    assertNetworkApGroupOperation(Action.MODIFY, networkApGroupRadio,
        operations.stream().filter(op -> networkApGroupRadio.getId().equals(op.getId())).findFirst()
            .get(), false);
  }

  @Test
  @FeatureFlag(enable = WIFI_EDA_WPA3_DSAE_TOGGLE)
  public void testModifyDsaeNetworkApGroupRadio() {
    var networkApGroupRadio = networkApGroupRadio(true);
    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new ModifiedTxEntity(networkApGroupRadio, Collections.emptySet())))
        .when(txChanges).getModifiedEntities();
    var operations = builder
        .build(new ModifiedTxEntity<>(networkApGroupRadio, Collections.emptySet()), emptyTxChanges());
    assertEquals(1, operations.size());
    assertNetworkApGroupOperation(Action.MODIFY, networkApGroupRadio,
        operations.stream().filter(op -> networkApGroupRadio.getId().equals(op.getId())).findFirst()
            .get(), true);
  }

  @Test
  public void testDeleteNetworkApGroupRadio() {
    var networkApGroupRadio = networkApGroupRadio(false);
    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new DeletedTxEntity(networkApGroupRadio))).when(txChanges).getDeletedEntities();
    var operations = builder.build(new DeletedTxEntity<>(networkApGroupRadio), emptyTxChanges());
    assertEquals(1, operations.size());
    assertNetworkApGroupOperation(Action.DELETE, networkApGroupRadio,
        operations.stream().filter(op -> networkApGroupRadio.getId().equals(op.getId())).findFirst()
            .get(), false);
  }

  @Test
  @FeatureFlag(enable = WIFI_EDA_WPA3_DSAE_TOGGLE)
  public void testDeleteDsaeNetworkApGroupRadio() {
    var networkApGroupRadio = networkApGroupRadio(true);
    var txChanges = spy(TxChangesReader.class);
    doReturn(List.of(new DeletedTxEntity(networkApGroupRadio))).when(txChanges).getDeletedEntities();
    var operations = builder.build(new DeletedTxEntity<>(networkApGroupRadio), emptyTxChanges());
    assertEquals(1, operations.size());
    assertNetworkApGroupOperation(Action.DELETE, networkApGroupRadio,
        operations.stream().filter(op -> networkApGroupRadio.getId().equals(op.getId())).findFirst()
            .get(), true);
  }

  private NetworkApGroupRadio networkApGroupRadio(boolean isDsaeNetwork) {
    // Given
    final var tenant = new Tenant(genUUID());
    final var venue = new Venue(genUUID());
    venue.setTenant(tenant);
    var network = new DpskNetwork();
    network.setName(String.format("DPSK %s", randomName(4)));
    network.setTenant(tenant);

    final var networkVenue = new NetworkVenue();
    networkVenue.setId(randomId());
    networkVenue.setNetwork(network);
    networkVenue.setVenue(venue);
    networkVenue.setTenant(tenant);
    networkVenue.setApWlanId(123);

    final var apGroup = new ApGroup();
    apGroup.setId(randomId());
    apGroup.setVenue(venue);
    apGroup.setTenant(tenant);

    final var networkApGroup = new NetworkApGroup();
    networkApGroup.setId(randomId());
    networkApGroup.setApGroup(apGroup);
    networkApGroup.setNetworkVenue(networkVenue);
    networkApGroup.setTenant(tenant);

    var networkApGroupRadio = new NetworkApGroupRadio(genUUID());
    networkApGroupRadio.setNetworkApGroup(networkApGroup);
    if (isDsaeNetwork) {
      ((DpskNetwork) (networkApGroupRadio.getNetworkApGroup().getNetworkVenue()
          .getNetwork())).setDsaeNetworkPairId("123");
      ((DpskNetwork) (networkApGroupRadio.getNetworkApGroup().getNetworkVenue()
          .getNetwork())).setIsDsaeServiceNetwork(true);
    }
    networkApGroupRadio.setRadio(StrictRadioTypeEnum._5_GHz);
    networkApGroupRadio.setVlanPool(new VlanPool());
    networkApGroupRadio.setVlanId((short) 1);

    return networkApGroupRadio;
  }

  private void assertNetworkApGroupOperation(Action action, NetworkApGroupRadio networkApGroupRadio,
      Operation operation, boolean isDsaeNetwork) {
    var wlanApGroup = operation.getWlanApGroup();

    assertEquals(action, operation.getAction());
    assertNotNull(wlanApGroup);
    assertEquals(networkApGroupRadio.getNetworkApGroup().getApGroup().getId(), wlanApGroup.getApGroupId());
    assertEquals(StringValue.of(
                Integer.toString(networkApGroupRadio.getNetworkApGroup().getNetworkVenue().getApWlanId()))
            .getValue(), wlanApGroup.getApWlanId().getValue());

    if (Action.DELETE != action) {
      if (isDsaeNetwork) {
        assertEquals(true, wlanApGroup.getIsDsaeWlan());
      } else {
        assertEquals(false, wlanApGroup.getIsDsaeWlan());
      }
    }
  }

  private static String genUUID() {
    return UUID.randomUUID().toString();
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    public DdccmNetworkApGroupRadioOperationBuilder ddccmNetworkApGroupOperationBuilder() {
      var builder = spy(DdccmNetworkApGroupRadioOperationBuilder.class);
      doReturn(true).when(builder).hasChanged(any(), any());
      return builder;
    }
  }
}
