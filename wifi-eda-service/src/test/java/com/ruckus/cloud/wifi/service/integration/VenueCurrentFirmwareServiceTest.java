package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.core.header.HttpHeaderContext;
import com.ruckus.cloud.wifi.core.header.HttpHeaderName;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApModelMinimumFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TenantAvailableApFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApVersionLabelEnum;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.repository.ApModelMinimumFirmwareRepository;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.repository.TenantAvailableApFirmwareRepository;
import com.ruckus.cloud.wifi.repository.TenantCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.repository.VenueCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.VenueCurrentFirmwareService;
import com.ruckus.cloud.wifi.service.impl.VenueCurrentFirmwareServiceImpl;
import com.ruckus.cloud.wifi.servicemodel.TenantEarlyAccessInfo;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueCurrentFirmwareTestFixture;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

@WifiJpaDataTest
public class VenueCurrentFirmwareServiceTest extends AbstractServiceTest {

  @Autowired
  private TenantCurrentFirmwareRepository tenantCurrentFirmwareRepository;

  @Autowired
  private VenueCurrentFirmwareRepository venueCurrentFirmwareRepository;

  @Autowired
  private TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository;

  @Autowired
  private ApVersionRepository apVersionRepository;

  @Autowired
  private ApModelMinimumFirmwareRepository apModelMinimumFirmwareRepository;

  @Autowired
  private VenueCurrentFirmwareService venueCurrentFirmwareService;

  @Autowired
  private RepositoryUtil repositoryUtil;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @MockBean
  private TenantClient tenantClient;

  @BeforeEach
  void setup(Tenant tenant) {
    Map<String, List<String>> versionStrAndSupportedModels = new HashMap<>();
    versionStrAndSupportedModels.put("7.0.0.0.1", List.of("R500", "R550", "R650", "R750", "R770"));
    versionStrAndSupportedModels.put("7.0.0.0.2", List.of("R550", "R650", "R888"));
    versionStrAndSupportedModels.put("7.0.0.0.3", List.of("R888"));
    versionStrAndSupportedModels.put("7.0.0.0.4", List.of("R550"));
    versionStrAndSupportedModels.put("7.0.0.0.5", null);
    versionStrAndSupportedModels.put("7.0.0.0.6", List.of());
    versionStrAndSupportedModels.put("7.0.0.0.7", List.of("R700"));

    versionStrAndSupportedModels.forEach((version, models) -> {
      ApVersion apVersion = newApVersion(version, models);
      apVersionRepository.save(apVersion);
      tenantAvailableApFirmwareRepository.save(newTenantAvailableApFirmware(tenant, apVersion));
    });
  }

  @Test
  void testFindApVersionByVenueOrTenantDefaultForModel(Venue venue) {
    ApVersion version70002 = apVersionRepository.getReferenceById("7.0.0.0.2");
    venueCurrentFirmwareRepository
        .save(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, version70002, "R888"));

    ApVersion version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R888");
    assertEquals("7.0.0.0.2", version.getId());
    version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R550");
    assertEquals("7.0.0.0.4", version.getId());
    version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R650");
    assertEquals("7.0.0.0.2", version.getId());
    version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R700");
    assertEquals("7.0.0.0.7", version.getId());
  }

  @Test
  @FeatureFlag(enable = FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE)
  void testFindApVersionByVenueOrTenantDefaultForModel_whenGreenfieldByApModelToggleTurnOn(
      Venue venue) {
    ApVersion ver70002 = apVersionRepository.getReferenceById("7.0.0.0.2");
    ApVersion ver70003 = apVersionRepository.getReferenceById("7.0.0.0.3");
    ApVersion ver70004 = apVersionRepository.getReferenceById("7.0.0.0.4");
    ApVersion ver70007 = apVersionRepository.getReferenceById("7.0.0.0.7");

    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70002,
            "R550"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70003,
            "R650"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70004,
            "R700"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70007,
            "R888")
    ));
    venueCurrentFirmwareRepository
        .save(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, ver70002, "R888"));

    ApVersion version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R888");
    assertEquals(ver70002.getId(), version.getId());
    version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R550");
    assertEquals(ver70002.getId(), version.getId());
    version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R650");
    assertEquals(ver70003.getId(), version.getId());
    version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R700");
    assertEquals(ver70004.getId(), version.getId());
  }

  @Test
  @FeatureFlag(enable = { FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE, FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE })
  void testFindApVersionByVenueOrTenantDefaultForModel_whenEarlyAccessFfTurnOn(
      Venue venue) {
    ApVersion ver70002 = apVersionRepository.getReferenceById("7.0.0.0.2");
    ApVersion ver70003 = apVersionRepository.getReferenceById("7.0.0.0.3");
    ApVersion ver70004 = apVersionRepository.getReferenceById("7.0.0.0.4");
    ApVersion ver70007 = apVersionRepository.getReferenceById("7.0.0.0.7");

    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70002, "R550"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70003, "R650"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70004, "R700"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70007, "R888")
    ));
    venueCurrentFirmwareRepository.save(
        VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, ver70002, "R888"));

    ver70007.setLabels(List.of(ApVersionLabelEnum.BETA));
    ver70007.setSupportedApModels(List.of("R550", "R888", "R650"));
    apVersionRepository.save(ver70007);
    doReturn(new TenantEarlyAccessInfo(true, false)).when(tenantClient)
        .getEarlyAccessInfo(eq(venue.getTenant().getId()), any());

    // Then
    ApVersion version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R888");
    assertEquals(ver70002.getId(), version.getId());
    version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R550");
    assertEquals(ver70007.getId(), version.getId());
    version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R650");
    assertEquals(ver70007.getId(), version.getId());
    version = venueCurrentFirmwareService
        .findApVersionByVenueOrTenantDefaultForModel(venue.getTenant().getId(), venue.getId(), "R700");
    assertEquals(ver70004.getId(), version.getId());
  }

  @Test
  void testCreateIfNotExists(Venue venue) {

    venueCurrentFirmwareService.createIfNotExists(venue.getTenant().getId(), venue.getId(), "R550");

    Optional<VenueCurrentFirmware> res = venueCurrentFirmwareRepository.findByTenantIdAndVenueIdAndApModel(
        venue.getTenant().getId(), venue.getId(), "R550");
    assertThat(res)
        .isPresent()
        .get()
        .satisfies(vcf -> {
          assertEquals("7.0.0.0.4", vcf.getFirmware().getId());
          assertEquals("R550", vcf.getApModel());
          assertEquals(venue.getId(), vcf.getVenue().getId());
        });
  }

  @Test
  @FeatureFlag(enable = FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE)
  void testCreateIfNotExists_whenGreenfieldFfTurnOn(Venue venue) {
    ApVersion ver70002 = apVersionRepository.getReferenceById("7.0.0.0.2");
    ApVersion ver70003 = apVersionRepository.getReferenceById("7.0.0.0.3");

    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70002,
            "R550"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70003,
            "R650")
    ));

    venueCurrentFirmwareService.createIfNotExists(venue.getTenant().getId(), venue.getId(), "R550");

    Optional<VenueCurrentFirmware> res = venueCurrentFirmwareRepository.findByTenantIdAndVenueIdAndApModel(
        venue.getTenant().getId(), venue.getId(), "R550");
    assertThat(res)
        .isPresent()
        .get()
        .satisfies(vcf -> {
          assertEquals("7.0.0.0.2", vcf.getFirmware().getId());
          assertEquals("R550", vcf.getApModel());
          assertEquals(venue.getId(), vcf.getVenue().getId());
        });
  }

  @Test
  @FeatureFlag(enable = { FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE,
      FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE })
  void testCreateIfNotExists_whenEarlyAccessFfTurnOn(Venue venue) {
    ApVersion ver70002 = apVersionRepository.getReferenceById("7.0.0.0.2");
    ApVersion ver70003 = apVersionRepository.getReferenceById("7.0.0.0.3");
    ApVersion beta70004 = apVersionRepository.getReferenceById("7.0.0.0.4");
    beta70004.setLabels(List.of(ApVersionLabelEnum.BETA));
    apVersionRepository.save(beta70004);

    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70002, "R550"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70003, "R650")));
    // Then
    venueCurrentFirmwareService.createIfNotExists(venue.getTenant().getId(), venue.getId(), "R550");

    Optional<VenueCurrentFirmware> res = venueCurrentFirmwareRepository.findByTenantIdAndVenueIdAndApModel(
        venue.getTenant().getId(), venue.getId(), "R550");
    assertThat(res)
        .isPresent()
        .get()
        .satisfies(vcf -> {
          assertEquals("7.0.0.0.2", vcf.getFirmware().getId());
          assertEquals("R550", vcf.getApModel());
          assertEquals(venue.getId(), vcf.getVenue().getId());
        });
  }

  @Test
  @FeatureFlag(enable = { FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE,
      FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE })
  void testCreateIfNotExists_whenEarlyAccessFfTurnOnAndIsBetaTenant(Venue venue) {
    ApVersion ver70002 = apVersionRepository.getReferenceById("7.0.0.0.2");
    ApVersion ver70003 = apVersionRepository.getReferenceById("7.0.0.0.3");
    ApVersion beta70004 = apVersionRepository.getReferenceById("7.0.0.0.4");
    beta70004.setLabels(List.of(ApVersionLabelEnum.BETA));
    beta70004.setSupportedApModels(List.of("R550"));
    apVersionRepository.save(beta70004);
    doReturn(new TenantEarlyAccessInfo(true, false)).when(tenantClient)
        .getEarlyAccessInfo(eq(venue.getTenant().getId()), any());

    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70002, "R550"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70003, "R650")));
    // Then
    venueCurrentFirmwareService.createIfNotExists(venue.getTenant().getId(), venue.getId(), "R550");

    Optional<VenueCurrentFirmware> res = venueCurrentFirmwareRepository.findByTenantIdAndVenueIdAndApModel(
        venue.getTenant().getId(), venue.getId(), "R550");
    assertThat(res)
        .isPresent()
        .get()
        .satisfies(vcf -> {
          assertEquals("7.0.0.0.4", vcf.getFirmware().getId());
          assertEquals("R550", vcf.getApModel());
          assertEquals(venue.getId(), vcf.getVenue().getId());
        });
  }

  @Test
  @FeatureFlag(enable = { FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE,
      FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE })
  void testCreateIfNotExists_whenEarlyAccessFfTurnOnAndIsBetaAndAlphaTenant(Venue venue) {
    ApVersion ver70002 = apVersionRepository.getReferenceById("7.0.0.0.2");
    ApVersion ver70003 = apVersionRepository.getReferenceById("7.0.0.0.3");
    ApVersion beta70004 = apVersionRepository.getReferenceById("7.0.0.0.4");
    ApVersion alpha70005 = apVersionRepository.getReferenceById("7.0.0.0.5");
    beta70004.setLabels(List.of(ApVersionLabelEnum.GA, ApVersionLabelEnum.BETA));
    beta70004.setSupportedApModels(List.of("R550"));
    alpha70005.setLabels(List.of(ApVersionLabelEnum.ALPHA));
    alpha70005.setSupportedApModels(List.of("R550"));
    apVersionRepository.saveAll(List.of(beta70004, alpha70005));
    doReturn(new TenantEarlyAccessInfo(true, true)).when(tenantClient)
        .getEarlyAccessInfo(eq(venue.getTenant().getId()), any());

    tenantCurrentFirmwareRepository.saveAll(List.of(
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70002, "R550"),
        TenantCurrentFirmwareTestFixture.randomTenantCurrentFirmware(venue.getTenant(), ver70003, "R650")));
    // Then
    venueCurrentFirmwareService.createIfNotExists(venue.getTenant().getId(), venue.getId(), "R550");

    Optional<VenueCurrentFirmware> res = venueCurrentFirmwareRepository.findByTenantIdAndVenueIdAndApModel(
        venue.getTenant().getId(), venue.getId(), "R550");
    assertThat(res)
        .isPresent()
        .get()
        .satisfies(vcf -> {
          assertEquals("7.0.0.0.5", vcf.getFirmware().getId());
          assertEquals("R550", vcf.getApModel());
          assertEquals(venue.getId(), vcf.getVenue().getId());
        });
  }

  @Test
  void testDeleteIfModelsNotExistsInVenue(Venue venue) {
    String tenantId = venue.getTenant().getId();
    String venueId = venue.getId();
    List<String> models = List.of("R550", "R750", "R650");
    models.forEach(model -> {
      venueCurrentFirmwareService.createIfNotExists(tenantId, venueId, model);
      assertThat(
          venueCurrentFirmwareRepository.findByTenantIdAndVenueIdAndApModel(tenantId, venueId, model)).isPresent();
    });

    venueCurrentFirmwareService.deleteIfModelsNotExistsInVenue(tenantId, venueId, models);

    models.forEach(model -> assertThat(
        venueCurrentFirmwareRepository.findByTenantIdAndVenueIdAndApModel(tenantId, venueId, model)).isNotPresent());
  }

  @Test
  void testDeleteIfModelsNotExistsInVenue_whenVenueHasTargetModelsAp(Venue venue) {
    String tenantId = venue.getTenant().getId();
    String venueId = venue.getId();
    List<String> models = List.of("R550", "R750", "R650", "R500", "R770");
    this.addAps(venue, List.of("R550", "R550", "R500", "R650"));
    models.forEach(model -> {
      venueCurrentFirmwareService.createIfNotExists(tenantId, venueId, model);
      assertThat(
          venueCurrentFirmwareRepository.findByTenantIdAndVenueIdAndApModel(tenantId, venueId, model)).isPresent();
    });

    venueCurrentFirmwareService.deleteIfModelsNotExistsInVenue(tenantId, venueId, List.of("R550", "R650", "R750"));
    assertThat(venueCurrentFirmwareRepository.findByTenantId(tenantId))
        .extracting(VenueCurrentFirmware::getApModel)
        .containsAll(List.of("R550", "R650"))
        .containsAll(List.of("R500", "R770"))
        .doesNotContain("R750");
  }

  @Test
  void deleteByTenantIdAndVenueIdAndApModelIn(Venue venue) {
    String tenantId = venue.getTenant().getId();
    String venueId = venue.getId();
    List<String> models = List.of("R550", "R750", "R650", "R500", "R770");
    models.forEach(model -> {
      venueCurrentFirmwareService.createIfNotExists(tenantId, venueId, model);
      assertThat(
          venueCurrentFirmwareRepository.findByTenantIdAndVenueIdAndApModel(tenantId, venueId, model)).isPresent();
    });

    venueCurrentFirmwareService.deleteByTenantIdAndVenueIdAndApModelIn(tenantId, venueId,
        List.of("R550", "R650", "R750"));
    assertThat(venueCurrentFirmwareRepository.findByTenantId(tenantId))
        .extracting(VenueCurrentFirmware::getApModel)
        .containsAll(List.of("R500", "R770"))
        .doesNotContain("R550", "R650", "R750");
  }

  @Test
  void testIsLowerThanApModelMinimumFirmware() {
    ApVersion version70004 = apVersionRepository.getReferenceById("7.0.0.0.4");
    ApVersion version70005 = apVersionRepository.getReferenceById("7.0.0.0.5");
    ApVersion version70006 = apVersionRepository.getReferenceById("7.0.0.0.6");
    createApModelMinimumFirmware("R770", version70005);
    createApModelMinimumFirmware("R500", version70005);
    createApModelMinimumFirmware("R550", version70005);

    assertTrue(venueCurrentFirmwareService.isLowerThanApModelMinimumFirmware(version70004, "R770"));
    assertFalse(venueCurrentFirmwareService.isLowerThanApModelMinimumFirmware(version70005, "R500"));
    assertFalse(venueCurrentFirmwareService.isLowerThanApModelMinimumFirmware(version70006, "R550"));
    assertFalse(venueCurrentFirmwareService.isLowerThanApModelMinimumFirmware(version70005, "R750"));
  }

  @Test
  void testHasFw6ApAndOtherFwAp(Venue venue) {
    ApVersion apVersion = newApVersion("6.2.4.0.225", List.of("R600"));
    apVersionRepository.save(apVersion);
    venueCurrentFirmwareRepository.save(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion, "R600"));
    ApVersion apVersion2 = newApVersion("7.0.0.200.5000", List.of("R550"));
    apVersionRepository.save(apVersion2);
    venueCurrentFirmwareRepository.save(VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(venue, apVersion2, "R550"));
    boolean result = venueCurrentFirmwareService.hasFw6ApAndOtherFwAp(venue.getTenant().getId(), venue.getId());
    assertTrue(result);
  }

  @Test
  void testHasAnyApHasFw6ApAndOtherFwAp(Venue venue) {
    var result = venueCurrentFirmwareService.hasAnyApHasFw6ApAndOtherFwAp(venue.getTenant().getId(), venue.getId());
    assertFalse(result.getLeft());
    assertFalse(result.getRight());
  }

  @Test
  void testGetTheBiggestFwVersion(Venue venue) {
    String tenantId = venue.getTenant().getId();
    String venueId = venue.getId();
    ApVersion biggestVersionInVenue = apVersionRepository.getReferenceById("7.0.0.0.1");
    venueCurrentFirmwareRepository.save(
        VenueCurrentFirmwareTestFixture.randomVenueCurrentFirmware(
            venue, biggestVersionInVenue, "R550"));

    ApVersion result = venueCurrentFirmwareService.getTheBiggestFwVersion(tenantId, venueId);

    assertEquals(biggestVersionInVenue.getId(), result.getId());
  }

  @Test
  void testGetTheBiggestFwVersion_BiggestVersionInTenant(Venue venue) {
    String tenantId = venue.getTenant().getId();
    String venueId = venue.getId();
    ApVersion biggestVersionInTenant = apVersionRepository.getReferenceById("7.0.0.0.7");

    ApVersion result = venueCurrentFirmwareService.getTheBiggestFwVersion(tenantId, venueId);

    assertEquals(biggestVersionInTenant.getId(), result.getId());
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE)
  void testGetTheBiggestFwVersion_BiggestVersionInTenant_EarlyAccessEnabled_Normal(Venue venue) {
    String tenantId = venue.getTenant().getId();
    String venueId = venue.getId();
    ApVersion biggestVersionInTenant = apVersionRepository.getReferenceById("7.0.0.0.7");

    ApVersion result = venueCurrentFirmwareService.getTheBiggestFwVersion(tenantId, venueId);

    assertEquals(biggestVersionInTenant.getId(), result.getId());
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE)
  void testGetTheBiggestFwVersion_BiggestVersionInTenant_EarlyAccessEnabled_IsAlpha(Venue venue) {
    String tenantId = venue.getTenant().getId();
    String venueId = venue.getId();
    ApVersion alpha = new ApVersion("7.0.0.0.8");
    alpha.setLabels(List.of(ApVersionLabelEnum.ALPHA));
    apVersionRepository.save(alpha);
    HttpHeaderContext.setHeader(HttpHeaderName.RKS_IS_ALPHA_FLAG, "true");

    ApVersion result = venueCurrentFirmwareService.getTheBiggestFwVersion(tenantId, venueId);

    assertEquals(alpha.getId(), result.getId());
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE)
  void testGetTheBiggestFwVersion_BiggestVersionInTenant_EarlyAccessEnabled_IsBeta(Venue venue) {
    String tenantId = venue.getTenant().getId();
    String venueId = venue.getId();
    ApVersion beta = new ApVersion("7.0.0.0.8");
    beta.setLabels(List.of(ApVersionLabelEnum.BETA));
    apVersionRepository.save(beta);
    HttpHeaderContext.setHeader(HttpHeaderName.RKS_IS_BETA_FLAG, "true");

    ApVersion result = venueCurrentFirmwareService.getTheBiggestFwVersion(tenantId, venueId);

    assertEquals(beta.getId(), result.getId());
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE)
  void testGetMaximumAndMinimumFirmwareVersion_EarlyAccess(Venue venue) {
    String tenantId = venue.getTenant().getId();
    ApVersion beta = new ApVersion("7.0.0.0.10");
    beta.setLabels(List.of(ApVersionLabelEnum.BETA));
    apVersionRepository.save(beta);
    HttpHeaderContext.setHeader(HttpHeaderName.RKS_IS_BETA_FLAG, "true");

    Pair<String, String> pair = venueCurrentFirmwareService.getMaximumAndMinimumFirmwareVersion(tenantId, venue);

    assertEquals(beta.getId(), pair.getLeft());
    assertEquals(beta.getId(), pair.getRight());
  }

  private ApVersion newApVersion(String version, List<String> apModels) {
    return ApVersionTestFixture.recommendedApVersion(version, v -> v.setSupportedApModels(apModels));
  }

  private void createApModelMinimumFirmware(String model, ApVersion apVersion) {
    ApModelMinimumFirmware apModelMinimumFirmware = new ApModelMinimumFirmware(model);
    apModelMinimumFirmware.setMinimumFirmware(apVersion);
    apModelMinimumFirmwareRepository.save(apModelMinimumFirmware);
  }

  private TenantAvailableApFirmware newTenantAvailableApFirmware(Tenant tenant, ApVersion apVersion) {
    TenantAvailableApFirmware taaf = new TenantAvailableApFirmware();
    taaf.setTenant(tenant);
    taaf.setApVersion(apVersion);
    return taaf;
  }

  private void addAps(Venue venue, List<String> apModels) {
    ApGroup apGroup = createApGroup(venue, "group1");
    apModels.forEach(model -> createAp(apGroup, randomSerialNumber(), model, null));
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    @Primary
    public VenueCurrentFirmwareService venueCurrentFirmwareService(
        TenantCurrentFirmwareRepository tenantCurrentFirmwareRepository,
        VenueCurrentFirmwareRepository venueCurrentFirmwareRepository,
        TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository,
        ApRepository apRepository,
        ApModelMinimumFirmwareRepository apModelMinimumFirmwareRepository,
        VenueRepository venueRepository,
        ApVersionRepository apVersionRepository,
        FeatureFlagService featureFlagService,
        TenantClient tenantClient) {
      return new VenueCurrentFirmwareServiceImpl(
          tenantCurrentFirmwareRepository,
          venueCurrentFirmwareRepository,
          tenantAvailableApFirmwareRepository,
          apRepository,
          apModelMinimumFirmwareRepository,
          venueRepository,
          apVersionRepository,
          featureFlagService,
          tenantClient);
    }
  }
}