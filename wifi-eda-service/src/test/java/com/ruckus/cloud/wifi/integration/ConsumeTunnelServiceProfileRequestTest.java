package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ExecutionPlanStep.SYNC_DATA_TO_VIEWMODEL_STEP;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.nullValue;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.serialName;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.tunnelProfile;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.ConsumerRecordAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.google.protobuf.StringValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.TunnelProfileNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.TunnelMtuTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.TunnelServiceProfileGenerator;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.handler.cfgrequest.ConfigRequestHandler;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.proto.Operation;
import com.ruckus.cloud.wifi.proto.WifiConfigChange;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder.TxCtx;
import com.ruckus.cloud.wifi.service.core.tx.entity.TxEntity;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.assertj.core.api.AssertionsForClassTypes;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("TunnelServiceProfileTest")
@WifiIntegrationTest
public class ConsumeTunnelServiceProfileRequestTest {

  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private RevisionService revisionService;

  @Autowired
  private KafkaTopicProvider kafkaTopicProvider;
  @Autowired
  private MessageCaptors messageCaptors;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.ADD_TUNNEL_SERVICE_PROFILE)
  class ConsumeAddTunnelServiceProfileRequestTest {

    @Payload
    private final TunnelServiceProfileGenerator generator = Generators.tunnelServiceProfile();

    @Test
    void thenShouldHandleTheRequestIfFeatureFlagBeingEnabled(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.TunnelServiceProfile payload) {
      validateResult(CfgAction.ADD_TUNNEL_SERVICE_PROFILE, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_TUNNEL_SERVICE_PROFILE)
  class ConsumeUpdateTunnelServiceProfileRequestTest {

    @Payload
    private final TunnelServiceProfileGenerator generator = Generators.tunnelServiceProfile()
        .setId(nullValue(String.class))
        .setName(serialName("UpdateTunnelProfile"));

    private String tunnelServiceProfileId;

    @BeforeEach
    void givenOneTunnelProfilePersistedInDb(final TunnelProfile tunnelProfile) {
      tunnelServiceProfileId = tunnelProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("tunnelServiceProfileId", tunnelServiceProfileId);
    }

    @Test
    void thenShouldHandleTheRequestIfFeatureFlagBeingEnabled(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.TunnelServiceProfile payload) {
      validateResult(CfgAction.UPDATE_TUNNEL_SERVICE_PROFILE, tunnelServiceProfileId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_TUNNEL_SERVICE_PROFILE)
  class ConsumeUpdateTunnelServiceProfileRequestTestWithSamePayload {

    private static final boolean forceFragmentation = true;
    private static final short ageTimeMinutes = 5000;
    private static final short keepAliveInterval = 1000;
    private static final short keepAliveRetry = 2000;
    private static final short mtuRequestTimeout = 3000;
    private static final short mtuRequestRetry = 56;

    @Payload
    private final TunnelServiceProfileGenerator generator = Generators.tunnelServiceProfile()
        .setId(nullValue(String.class))
        .setName(serialName("UpdateTunnelProfile"))
        .setForceFragmentation(always(forceFragmentation))
        .setAgeTimeMinutes(always(ageTimeMinutes))
        .setKeepAliveInterval(always(keepAliveInterval))
        .setKeepAliveRetry(always(keepAliveRetry))
        .setMtuRequestTimeout(always(mtuRequestTimeout))
        .setMtuRequestRetry(always(mtuRequestRetry));

    private String tunnelServiceProfileId;

    @BeforeEach
    void givenOneTunnelProfilePersistedInDb(final Tenant tenant) {
      tunnelServiceProfileId = UUID.randomUUID().toString();
      final TunnelProfile dbTunnelProfile = com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.tunnelProfile()
          .setTenant(always(tenant))
          .setId(always(tunnelServiceProfileId))
          .setName(serialName("UpdateTunnelProfile"))
          .setForceFragmentation(always(forceFragmentation))
          .setAgeTimeMinutes(always(ageTimeMinutes))
          .setKeepAliveInterval(always(keepAliveInterval))
          .setKeepAliveRetry(always(keepAliveRetry))
          .setMtuRequestTimeout(always(mtuRequestTimeout))
          .setMtuRequestRetry(always(mtuRequestRetry))
          .generate();
      repositoryUtil.createOrUpdate(dbTunnelProfile);
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("tunnelServiceProfileId", tunnelServiceProfileId);
    }

    @Test
    void thenShouldHandleTheRequestIfFeatureFlagBeingEnabled(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.TunnelServiceProfile payload) throws Exception {
      validateRepositoryData(tunnelServiceProfileId, payload, CfgAction.UPDATE_TUNNEL_SERVICE_PROFILE);
      validateActivityMessages(List.of(apiFlowName(CfgAction.UPDATE_TUNNEL_SERVICE_PROFILE), SYNC_DATA_TO_VIEWMODEL_STEP));

      messageCaptors.getDdccmMessageCaptor().assertNotSent(txCtxExtension.getTenantId(), txCtxExtension.getRequestId());
      messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSent(txCtxExtension.getTenantId(), txCtxExtension.getRequestId());

      validateWifiCfgChangeMessages(CfgAction.UPDATE_TUNNEL_SERVICE_PROFILE, tunnelServiceProfileId);
    }
  }

  @Nested
  @ApiAction(CfgAction.PATCH_TUNNEL_SERVICE_PROFILE)
  class ConsumePatchTunnelServiceProfileRequestTest {

    @Payload
    private final TunnelServiceProfileGenerator generator = Generators.tunnelServiceProfile()
        .setId(nullValue(String.class))
        .setName(serialName("PatchTunnelProfile"));

    private String tunnelServiceProfileId;

    @BeforeEach
    void givenOneTunnelProfilePersistedInDb(final TunnelProfile tunnelProfile) {
      tunnelServiceProfileId = tunnelProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("tunnelServiceProfileId", tunnelServiceProfileId);
    }

    @Test
    void thenShouldHandleTheRequestIfFeatureFlagBeingEnabled(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.TunnelServiceProfile payload) {
      validateResult(CfgAction.PATCH_TUNNEL_SERVICE_PROFILE, tunnelServiceProfileId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_TUNNEL_SERVICE_PROFILE)
  class ConsumeDeleteTunnelServiceProfileRequestTest {
    private String tunnelServiceProfileId;

    @BeforeEach
    void givenOneTunnelProfilePersistedInDb(final TunnelProfile tunnelProfile) {
      tunnelServiceProfileId = tunnelProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("tunnelServiceProfileId", tunnelServiceProfileId);
    }

    @Test
    void thenShouldHandleTheRequestIfFeatureFlagBeingEnabled(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.TunnelServiceProfile payload) {
      validateResult(CfgAction.DELETE_TUNNEL_SERVICE_PROFILE, tunnelServiceProfileId, null);
    }
  }

  @Nested
  @ApiAction(value = CfgAction.DELETE_NETWORK)
  class whenConsumeDeleteNetworkWithTunnelProfileRequest {
    private String networkId;
    private String tunnelProfileId;

    @BeforeEach
    void givenOneTunnelProfileAndOneTunnelProfileNetworkAndOneNetworkPersistedInDb(
        final Tenant tenant, Venue venue, Network network) {
      // Add WiFi Calling Service Profile
      TunnelProfile tunnelProfile = tunnelProfile().generate();
      tunnelProfile.setTenant(tenant);
      repositoryUtil.createOrUpdate(tunnelProfile, tenant.getId(), randomTxId());

      TunnelProfileNetwork tunnelProfileNetwork = new TunnelProfileNetwork();
      tunnelProfileNetwork.setTenant(tenant);
      tunnelProfileNetwork.setCentralizedForwardingServiceId(UUID.randomUUID().toString());
      tunnelProfileNetwork.setCentralizedForwardingServiceVenue(venue);
      tunnelProfileNetwork.setNetwork(network);
      tunnelProfileNetwork.setTunnelProfile(tunnelProfile);
      repositoryUtil.createOrUpdate(tunnelProfileNetwork, tenant.getId(), randomTxId());

      tunnelProfile.setTunnelProfileNetworks(List.of(tunnelProfileNetwork));
      repositoryUtil.createOrUpdate(tunnelProfile, tenant.getId(), randomTxId());

      network.setTunnelProfileNetworks(List.of(tunnelProfileNetwork));
      repositoryUtil.createOrUpdate(network, tenant.getId(), randomTxId());

      networkId = network.getId();
      tunnelProfileId = tunnelProfile.getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("networkId", networkId);
    }

    @Test
    void thenDeleteNetworkShouldInvokeTunnelProfileCmnCfgCollectorChange(TxCtx txCtx)
        throws IOException, ExecutionException, InterruptedException {

      final var wifiCfgChangeDeleteMessageRecord = messageCaptors.getWifiCfgChangeMessageCaptor()
          .getValue(txCtx);
      assertThat(wifiCfgChangeDeleteMessageRecord)
          .isNotNull()
          .extracting(KafkaProtoMessage::getHeaders)
          .isNotNull()
          .extracting(headers -> headers.lastHeader(WifiCommonHeader.WIFI_REQUEST_ID))
          .isNotNull()
          .extracting(Header::value)
          .extracting(String::new)
          .isEqualTo(txCtx.getTxId());

      final Network deleteNetwork = repositoryUtil.find(
          Network.class, networkId);

      assertThat(deleteNetwork).isNull();
      final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(txCtx);

      assertThat(cmnCfgCollectorMessage).isNotNull()
          .extracting(KafkaProtoMessage::getPayload).isNotNull();

      AssertionsForClassTypes.assertThatNoException().isThrownBy(() ->
          assertThat(cmnCfgCollectorMessage.getPayload())
              .extracting(ViewmodelCollector::getOperationsList).asList()
              .isNotEmpty()
              .as("The total operation count should be 2").hasSize(2)
              .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
              .satisfies(ops -> {
                assertThat(ops)
                    .filteredOn(op -> EsConstants.Index.TUNNEL_PROFILE_INDEX_NAME.equals(
                        op.getIndex()))
                    .filteredOn(op -> op.getOpType() == OpType.MOD)
                    .as("The MOD %s operation count should be 1",
                        EsConstants.Index.TUNNEL_PROFILE_INDEX_NAME).hasSize(1)
                    .singleElement()
                    .extracting(Operations::getId)
                    .as("The operation id should be %s", tunnelProfileId)
                    .isEqualTo(tunnelProfileId);
              }));

    }
  }

  private void validateResult(CfgAction apiAction,
      com.ruckus.cloud.wifi.eda.viewmodel.TunnelServiceProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();
    final TxChanges txChanges = revisionService.changes(requestId, tenantId, ConfigRequestHandler.apiActionToFlowName(apiAction.key()));
    final String tunnelServiceProfileId = txChanges.getNewEntities().stream()
        .filter(txEntity -> txEntity.getEntity() instanceof TunnelProfile)
        .map(TxEntity::getId).findAny().orElseGet(Assertions::fail);
    validateResult(apiAction, tunnelServiceProfileId, payload);
  }

  private void validateResult(CfgAction apiAction, String tunnelServiceProfileId,
      com.ruckus.cloud.wifi.eda.viewmodel.TunnelServiceProfile payload) {
    validateRepositoryData(tunnelServiceProfileId, payload, apiAction);
    validateDdccmCfgRequestMessages(apiAction, List.of(tunnelServiceProfileId), payload);
    validateCmnCfgCollectorMessages(apiAction, List.of(tunnelServiceProfileId));
    validateActivityMessages(List.of(apiFlowName(apiAction)));
  }

  private void validateRepositoryData(String tunnelServiceProfileId,
      com.ruckus.cloud.wifi.eda.viewmodel.TunnelServiceProfile payload, CfgAction apiAction) {
    if (tunnelServiceProfileId == null) {
      final String requestId = txCtxExtension.getRequestId();
      final String tenantId = txCtxExtension.getTenantId();
      final TxChanges txChanges = revisionService.changes(requestId, tenantId, apiAction.key());
      assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
      return;
    }

    final TunnelProfile tunnelProfile = repositoryUtil.find(TunnelProfile.class, tunnelServiceProfileId);

    if (payload == null) {
      assertThat(tunnelProfile).isNull();
      return;
    }

    assertThat(tunnelProfile)
        .isNotNull()
        .matches(tp -> Objects.equals(tp.getId(), tunnelServiceProfileId))
        .matches(tp -> Objects.equals(tp.getName(), payload.getName()))
        .matches(tp -> Objects.equals(tp.getMtuType(), payload.getMtuType()))
        .matches(tp -> Objects.equals(tp.getAgeTimeMinutes(), payload.getAgeTimeMinutes()))
        .matches(tp -> Objects.equals(tp.getForceFragmentation(), payload.getForceFragmentation()));
  }

  private void validateDdccmCfgRequestMessages(CfgAction apiAction, List<String> tunnelServiceProfileIds,
      com.ruckus.cloud.wifi.eda.viewmodel.TunnelServiceProfile payload) {
    if (apiAction == null || tunnelServiceProfileIds == null) {
      messageCaptors.getDdccmMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageUtil.receive(kafkaTopicProvider.getDdccmCfgRequest());

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(ConsumerRecord::value).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(WifiConfigRequest.parseFrom(ddccmCfgRequestMessage.value()))
            .extracting(WifiConfigRequest::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasCcmVxLANTunnelProfile)
            .hasSize(tunnelServiceProfileIds.size())
            .allMatch(op -> tunnelServiceProfileIds.contains(op.getId()))
            .allMatch(op -> op.getAction() == action(apiAction))
            .allSatisfy(op -> assertThat(op)
                .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
            .allSatisfy(op -> {
              if (op.getAction() == Action.DELETE) {
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCcmVxLANTunnelProfile)
                    .matches(vp -> tunnelServiceProfileIds.contains(vp.getId()));
              } else {
                assertThat(payload).isNotNull();
                assertThat(op)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCcmVxLANTunnelProfile)
                    .matches(tp -> tunnelServiceProfileIds.contains(tp.getId()))
                    .matches(tp -> (TunnelMtuTypeEnum.MANUAL == payload.getMtuType())
                        == tp.getPmtuManualMode())
                    .matches(tp -> payload.getName().equals(tp.getName()))
                    .matches(tp -> payload.getForceFragmentation() == tp.getForcedFragmentation())
                    .matches(tp -> payload.getAgeTimeMinutes() * 60 == tp.getVxlanTunnelAgeTimeSecs());
              }
            }));
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction, List<String> tunnelServiceProfileIds) {
    if (apiAction == null || tunnelServiceProfileIds == null) {
      messageCaptors.getCmnCfgCollectorMessageCaptor()
          .assertNotSentByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageUtil.receive(
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());

    assertThat(cmnCfgCollectorMessage).isNotNull()
        .extracting(ConsumerRecord::value).isNotNull();

    assertThatNoException().isThrownBy(() ->
        assertThat(ViewmodelCollector.parseFrom(cmnCfgCollectorMessage.value()))
            .matches(msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList()
            .isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> Index.TUNNEL_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(tunnelServiceProfileIds.size())
            .allMatch(op -> tunnelServiceProfileIds.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction))
            .allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op)
                    .extracting(Operations::getDocMap)
                    .matches(doc -> tunnelServiceProfileIds.contains(doc.get(EsConstants.Key.ID).getStringValue()))
                    .matches(doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()));
              }
            }));
  }

  private void validateActivityMessages(List<String> steps) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor()
        .getValues(steps.size(), List.of(tenantId), requestId))
        .hasSize(steps.size())
        .allSatisfy(KafkaMessageAssertions.assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .allSatisfy(KafkaMessageAssertions.assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .map(KafkaProtoMessage::getPayload)
        .allMatch(status -> status.getStatus().equals(Status.OK))
        .extracting(ConfigurationStatus::getStep)
        .containsExactlyInAnyOrderElementsOf(steps);

    final var activityImpactedMessage = messageUtil.receive(kafkaTopicProvider.getActivityImpacted());
    assertThat(activityImpactedMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(ConsumerRecord::value).isNotNull();
  }

  private void validateWifiCfgChangeMessages(CfgAction apiAction, String tunnelProfileId) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var wifiCfgChangeMessage = messageCaptors.getWifiCfgChangeMessageCaptor()
        .getValue(tenantId, requestId);

    assertThat(wifiCfgChangeMessage.getPayload())
        .isNotNull()
        .extracting(WifiConfigChange::getOperationList, InstanceOfAssertFactories.list(Operation.class))
        .isNotEmpty()
        .satisfies(ops -> assertThat(ops)
            .filteredOn(com.ruckus.cloud.wifi.proto.Operation::hasTunnelProfile)
            .filteredOn(op -> op.getAction() == wifiCfgAction(apiAction))
            .hasSize(1)
            .singleElement()
            .extracting(Operation::getTunnelProfile)
            .extracting(com.ruckus.cloud.wifi.proto.TunnelProfile::getId)
            .extracting(StringValue::getValue)
            .isEqualTo(tunnelProfileId));
  }

  private Action action(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_TUNNEL_SERVICE_PROFILE -> Action.ADD;
      case UPDATE_TUNNEL_SERVICE_PROFILE, PATCH_TUNNEL_SERVICE_PROFILE -> Action.MODIFY;
      case DELETE_TUNNEL_SERVICE_PROFILE -> Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_TUNNEL_SERVICE_PROFILE -> OpType.ADD;
      case UPDATE_TUNNEL_SERVICE_PROFILE, PATCH_TUNNEL_SERVICE_PROFILE -> OpType.MOD;
      case DELETE_TUNNEL_SERVICE_PROFILE -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private Operation.Action wifiCfgAction(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_TUNNEL_SERVICE_PROFILE -> Operation.Action.ADD;
      case UPDATE_TUNNEL_SERVICE_PROFILE, PATCH_TUNNEL_SERVICE_PROFILE -> Operation.Action.MODIFY;
      case DELETE_TUNNEL_SERVICE_PROFILE -> Operation.Action.DELETE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_TUNNEL_SERVICE_PROFILE -> ApiFlowNames.ADD_TUNNEL_SERVICE_PROFILE;
      case UPDATE_TUNNEL_SERVICE_PROFILE -> ApiFlowNames.UPDATE_TUNNEL_SERVICE_PROFILE;
      case PATCH_TUNNEL_SERVICE_PROFILE -> ApiFlowNames.PATCH_TUNNEL_SERVICE_PROFILE;
      case DELETE_TUNNEL_SERVICE_PROFILE -> ApiFlowNames.DELETE_TUNNEL_SERVICE_PROFILE;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
