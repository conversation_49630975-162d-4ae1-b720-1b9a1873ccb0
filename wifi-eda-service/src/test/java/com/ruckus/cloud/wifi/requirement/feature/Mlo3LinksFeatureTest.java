package com.ruckus.cloud.wifi.requirement.feature;

import com.ruckus.cloud.wifi.eda.servicemodel.MultiLinkOperationOptions;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Wlan;
import com.ruckus.cloud.wifi.eda.servicemodel.WlanAdvancedCustomization;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import org.assertj.core.api.BDDAssertions;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
public class Mlo3LinksFeatureTest {

  @SpyBean
  private Mlo3LinksFeature unit;

  @Nested
  class WhenIsMlo3LinksEnabled {

    @Test
    void givenWlanIsNull() {
      final var network = new Network();
      network.setWlan(null);

      BDDAssertions.then(unit.test(network)).isFalse();
    }

    @Nested
    class GivenWlanIsNotNull {

      @Test
      void givenAdvancedCustomizationIsNull() {
        final var wlan = new Wlan();
        wlan.setAdvancedCustomization(null);
        final var network = new Network();
        network.setWlan(wlan);

        BDDAssertions.then(unit.test(network)).isFalse();
      }

      @Nested
      class GivenAdvancedCustomizationIsNotNull {

        @NotNull
        private Network getMultiLinkOperationNetwork(boolean multiLinkOperationEnabled,
            boolean enable6G) {
          final var customization = new WlanAdvancedCustomization();
          customization.setMultiLinkOperationEnabled(multiLinkOperationEnabled);
          MultiLinkOperationOptions multiLinkOperationOptions = getMultiLinkOperationOptions(
              enable6G);
          customization.setMultiLinkOperationOptions(multiLinkOperationOptions);
          final var wlan = new Wlan();
          wlan.setAdvancedCustomization(customization);
          final var network = new Network();
          network.setWlan(wlan);
          return network;
        }

        @NotNull
        private MultiLinkOperationOptions getMultiLinkOperationOptions(boolean enable6G) {
          MultiLinkOperationOptions multiLinkOperationOptions = new MultiLinkOperationOptions();
          multiLinkOperationOptions.setEnable24G(true);
          multiLinkOperationOptions.setEnable50G(true);
          multiLinkOperationOptions.setEnable6G(enable6G);
          return multiLinkOperationOptions;
        }

        @Nested
        class Given3LinksEnabled {

          @Test
          void thenReturnEnabled() {
            var network = getMultiLinkOperationNetwork(true, true);

            BDDAssertions.then(unit.test(network)).isEqualTo(false);
          }

          @Test
          void thenReturnDisabled() {
            var network = getMultiLinkOperationNetwork(false, true);

            BDDAssertions.then(unit.test(network)).isEqualTo(false);
          }
        }

        @Nested
        @FeatureFlag(enable = FlagNames.WIFI_EDA_WIFI7_MLO_3LINK_TOGGLE)
        class Given3LinksAndFeatureFlagEnabled {

          @Test
          void thenReturnEnabled() {
            var network = getMultiLinkOperationNetwork(true, true);

            BDDAssertions.then(unit.test(network)).isEqualTo(true);
          }

          @Test
          void thenReturnDisabled() {
            var network = getMultiLinkOperationNetwork(false, true);

            BDDAssertions.then(unit.test(network)).isEqualTo(false);
          }
        }

        @Nested
        @FeatureFlag(enable = FlagNames.WIFI_EDA_WIFI7_MLO_3LINK_TOGGLE)
        class Given2LinksEnabled {

          @Test
          void thenReturnDisabled() {
            var network = getMultiLinkOperationNetwork(true, false);

            BDDAssertions.then(unit.test(network)).isEqualTo(false);
          }
        }
      }
    }
  }
}
