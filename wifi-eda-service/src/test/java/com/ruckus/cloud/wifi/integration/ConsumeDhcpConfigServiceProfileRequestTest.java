package com.ruckus.cloud.wifi.integration;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ACTIVATE_VENUE_DHCP_POOL;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_CLIENT_ISOLATION_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_DHCP_CONFIG_SERVICE_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.ADD_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DEACTIVATE_VENUE_DHCP_POOL;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_DHCP_CONFIG_SERVICE_PROFILE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DELETE_DHCP_CONFIG_SERVICE_PROFILES;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_NETWORK_VENUE;
import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.dhcpConfigServiceProfileDeep;
import static com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators.pskNetwork;
import static com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index.DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME;
import static com.ruckus.cloud.wifi.test.fixture.ClientIsolationTestFixture.clientIsolationAllowlist;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture.venueDhcpConfigServiceProfileSetting;
import static com.ruckus.cloud.wifi.test.fixture.GuestNetworkViewModelTestFixture.getGuestNetwork;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.doReturn;

import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueDhcpServiceSetting;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.events.gpb.Value;
import com.ruckus.cloud.events.gpb.ViewmodelCollector;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.client.viewmodel.ViewmodelClientGrpc;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApStateEnum;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ApSubStateEnum;
import com.ruckus.cloud.wifi.client.viewmodel.dto.ConnectedClientDTO;
import com.ruckus.cloud.wifi.eda.api.rest.DhcpConfigServiceProfileRestCtrl.DhcpConfigServiceProfileMapper;
import com.ruckus.cloud.wifi.eda.error.Errors;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ApplicationPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.ClientIsolationAllowlist;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpConfigServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpServiceProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApDhcpRoleEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.DhcpModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.StrictRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.DhcpConfigServiceProfileDeepGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigClientLeaseTime;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpPool;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpPoolUsage;
import com.ruckus.cloud.wifi.eda.viewmodel.DhcpServiceAp;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDhcpConfigServiceProfileSetting;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Key;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.revision.service.RevisionService;
import com.ruckus.cloud.wifi.service.core.tx.TxChanges;
import com.ruckus.cloud.wifi.service.impl.ExtendedDhcpServiceProfileVenueServiceCtrlImpl.DefaultGuestNetworkConfig;
import com.ruckus.cloud.wifi.service.pubsub.DhcpPoolStatsService;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.fixture.DhcpConfigServiceProfileTestFixture;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import com.ruckuswireless.scg.protobuf.DnbApDhcpPools;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("DhcpConfigServiceProfileTest")
@WifiIntegrationTest
class ConsumeDhcpConfigServiceProfileRequestTest extends AbstractRequestTest {

  private final String DEFAULT_VERSION = "6.2.0.103.1";

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private RevisionService revisionService;
  @Autowired
  private ViewmodelClientGrpc viewmodelClientGrpc;
  @Autowired
  protected DhcpPoolStatsService dhcpPoolStatsService;
  @Autowired
  private DefaultGuestNetworkConfig defaultGuestNetworkConfig;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Nested
  @ApiAction(CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE)
  class ConsumeAddDhcpConfigServiceProfileRequestTest {

    @Payload
    private final DhcpConfigServiceProfileDeepGenerator generator = dhcpConfigServiceProfileDeep();

    @Payload("defaultProfile")
    DhcpConfigServiceProfileDeep defaultProfile() {
      DhcpConfigServiceProfileDeep payload = dhcpConfigServiceProfileDeep().generate();
      payload.setServiceName("DHCP-Guest");
      return payload;
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, payload.getId());
      assertNotNull(dhcpConfigServiceProfile);

      validateRepositoryData(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
          dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
              .collect(Collectors.toList())), dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.ADD_DHCP_CONFIG_SERVICE_PROFILE,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ADD_DHCP_CONFIG_SERVICE_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE)
  class ConsumeUpdateDhcpConfigServiceProfileRequestTest {

    private String id;
    private DhcpConfigServiceProfile savedDhcpConfigServiceProfile;

    @BeforeEach
    void givenOneRowPersistedInDb(DhcpConfigServiceProfile dhcpConfigServiceProfile) {
      id = dhcpConfigServiceProfile.getId();
      this.savedDhcpConfigServiceProfile = dhcpConfigServiceProfile;
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("dhcpConfigServiceProfileId", id);
    }

    @Payload
    private final DhcpConfigServiceProfileDeepGenerator generator = Generators.dhcpConfigServiceProfileDeep();

    @Test
    void thenShouldHandleTheRequestSuccessfully(
        @Payload com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, id);
      assertNotNull(dhcpConfigServiceProfile);
      payload.setId(id);

      validateRepositoryData(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.ADD,
          dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
              .collect(Collectors.toList()), Action.DELETE,
          savedDhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
              .collect(Collectors.toList())), dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ApiFlowNames.UPDATE_DHCP_CONFIG_SERVICE_PROFILE);
    }

    @Payload("onlyModifyDhcpPools")
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep() {
      DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep = DhcpConfigServiceProfileMapper.INSTANCE.ServiceDhcpConfigServiceProfile2DhcpConfigServiceProfileDeep(
          savedDhcpConfigServiceProfile);
      dhcpConfigServiceProfileDeep.getDhcpPools().get(0).setName("name1");
      return dhcpConfigServiceProfileDeep;
    }

    @Test
    @ApiAction(value = CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE, payload = @Payload("onlyModifyDhcpPools"))
    void thenShouldHandleTheRequestSuccessfully_onlyModifyDhcpPools(
        @Payload("onlyModifyDhcpPools") com.ruckus.cloud.wifi.eda.viewmodel.DhcpConfigServiceProfileDeep payload) {
      final DhcpConfigServiceProfile dhcpConfigServiceProfile = repositoryUtil.find(
          DhcpConfigServiceProfile.class, id);
      assertNotNull(dhcpConfigServiceProfile);
      payload.setId(id);

      validateRepositoryData(payload, dhcpConfigServiceProfile);
      validateDdccmCfgRequestMessages(Map.of(Action.MODIFY,
          dhcpConfigServiceProfile.getDhcpPools().stream()
              .filter(dhcpPool -> dhcpPool.getName().equals("name1")).map(DhcpServiceProfile::getId)
              .collect(Collectors.toList())), dhcpConfigServiceProfile);
      validateCmnCfgCollectorMessages(CfgAction.UPDATE_DHCP_CONFIG_SERVICE_PROFILE,
          List.of(payload.getId()), dhcpConfigServiceProfile);
      validateActivityMessages(ApiFlowNames.UPDATE_DHCP_CONFIG_SERVICE_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_DHCP_CONFIG_SERVICE_PROFILE)
  class ConsumeDeleteDhcpConfigServiceProfileRequestTest {

    private String id;
    private List<String> dhcpPoolIds;

    @BeforeEach
    void givenOneApplicationPolicyPersistedInDb(
        final DhcpConfigServiceProfile dhcpConfigServiceProfile) {
      id = dhcpConfigServiceProfile.getId();
      dhcpPoolIds = dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
          .collect(Collectors.toList());
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams().addPathVariable("dhcpConfigServiceProfileId", id);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully() {
      assertNull(repositoryUtil.find(DhcpConfigServiceProfile.class, id));
      validateDdccmCfgRequestMessages(Map.of(Action.DELETE, dhcpPoolIds), null);
      validateCmnCfgCollectorMessages(CfgAction.DELETE_DHCP_CONFIG_SERVICE_PROFILE, List.of(id),
          null);
      validateActivityMessages(DELETE_DHCP_CONFIG_SERVICE_PROFILE);
    }
  }

  @Nested
  @ApiAction(CfgAction.DELETE_DHCP_CONFIG_SERVICE_PROFILES)
  class ConsumeDeleteDhcpConfigServiceProfilesRequestTest {

    @Payload
    private final List<String> dhcpConfigServiceProfileIds = new ArrayList<>();
    private final List<String> dhcpPoolIds = new ArrayList<>();

    @BeforeEach
    void givenTenApplicationPoliciesPersistedInDb(final Tenant tenant) {
      for (int i = 0; i < 9; i++) {
        DhcpConfigServiceProfile dhcpConfigServiceProfile = DhcpConfigServiceProfileTestFixture.randomDhcpConfigServiceProfile();
        dhcpConfigServiceProfile = repositoryUtil.createOrUpdate(dhcpConfigServiceProfile,
            tenant.getId(), randomTxId());
        dhcpConfigServiceProfileIds.add(dhcpConfigServiceProfile.getId());
        dhcpPoolIds.addAll(
            dhcpConfigServiceProfile.getDhcpPools().stream().map(DhcpServiceProfile::getId)
                .collect(Collectors.toList()));
      }
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload List<String> payload) {
      assertThat(dhcpConfigServiceProfileIds).extracting(
              applicationPolicyId -> repositoryUtil.find(ApplicationPolicy.class, applicationPolicyId))
          .isNotEmpty().allMatch(Objects::isNull);
      validateDdccmCfgRequestMessages(Map.of(Action.DELETE, dhcpPoolIds), null);
      validateCmnCfgCollectorMessages(CfgAction.DELETE_DHCP_CONFIG_SERVICE_PROFILES,
          dhcpConfigServiceProfileIds, null);
      validateActivityMessages(DELETE_DHCP_CONFIG_SERVICE_PROFILES);
    }
  }

  private void validateCmnCfgCollectorMessages(CfgAction apiAction,
      List<String> dhcpConfigServiceProfileIds, DhcpConfigServiceProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);

    assertThat(cmnCfgCollectorMessage).isNotNull().extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(cmnCfgCollectorMessage.getPayload()).matches(
                msg -> msg.getTenantId().equals(tenantId))
            .matches(msg -> msg.getRequestId().equals(requestId))
            .extracting(ViewmodelCollector::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.cloud.events.gpb.Operations.class::cast)
            .filteredOn(op -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(op.getIndex()))
            .hasSize(dhcpConfigServiceProfileIds.size())
            .allMatch(op -> dhcpConfigServiceProfileIds.contains(op.getId()))
            .allMatch(op -> op.getOpType() == opType(apiAction)).allSatisfy(op -> {
              if (op.getOpType() != OpType.DEL) {
                assertThat(op).extracting(Operations::getDocMap).matches(
                        doc -> dhcpConfigServiceProfileIds.contains(
                            doc.get(EsConstants.Key.ID).getStringValue())).matches(
                        doc -> tenantId.equals(doc.get(EsConstants.Key.TENANT_ID).getStringValue()))
                    .matches(
                        doc -> payload.getServiceName().equals(doc.get(Key.NAME).getStringValue()))
                    .matches(doc -> payload.getDhcpPools().size() == (doc.get(Key.DHCP_POOLS)
                        .getListValue().getValuesCount()))
                    .matches(doc -> 0 == doc.get(Key.VENUE_IDS).getListValue().getValuesCount())
                    .matches(doc -> false == doc.get(Key.IS_TEMPLATE).getBoolValue());
              }
            }));
  }

  private void validateDdccmCfgRequestMessages(Map<Action, List<String>> actionIdMap,
      DhcpConfigServiceProfile payload) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var ddccmCfgRequestMessage = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);

    assertThat(ddccmCfgRequestMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(
        () -> assertThat(ddccmCfgRequestMessage.getPayload()).extracting(
                WifiConfigRequest::getOperationsList).asList().isNotEmpty()
            .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Operation.class::cast)
            .filteredOn(com.ruckus.acx.ddccm.protobuf.wifi.Operation::hasDhcpServiceProfile)
            .hasSize(actionIdMap.values().stream().mapToInt(List::size).sum()).satisfies(ops -> {
              assertThat(ops).allSatisfy(op -> assertThat(op).extracting(
                      com.ruckus.acx.ddccm.protobuf.wifi.Operation::getCommonInfo)
                  .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                  .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                  .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE));
              if (actionIdMap.containsKey(Action.DELETE)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.DELETE)
                    .hasSize(actionIdMap.get(Action.DELETE).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.DELETE)
                                .contains(dhcpServiceProfile.getId())));
              }
              if (actionIdMap.containsKey(Action.ADD)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.ADD)
                    .hasSize(actionIdMap.get(Action.ADD).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.ADD)
                                .contains(dhcpServiceProfile.getId())).matches(
                                dhcpServiceProfile -> assertDdccmDetail(dhcpServiceProfile, payload)));
              }
              if (actionIdMap.containsKey(Action.MODIFY)) {
                assertThat(ops).filteredOn(op -> op.getAction() == Action.MODIFY)
                    .hasSize(actionIdMap.get(Action.MODIFY).size()).allSatisfy(
                        op -> assertThat(op).extracting(
                                com.ruckus.acx.ddccm.protobuf.wifi.Operation::getDhcpServiceProfile)
                            .matches(dhcpServiceProfile -> actionIdMap.get(Action.MODIFY)
                                .contains(dhcpServiceProfile.getId())).matches(
                                dhcpServiceProfile -> actionIdMap.get(Action.MODIFY)
                                    .contains(dhcpServiceProfile.getId())).matches(
                                dhcpServiceProfile -> assertDdccmDetail(dhcpServiceProfile, payload)));
              }
            }));
  }

  private boolean assertDdccmDetail(
      com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile dhcpServiceProfile,
      DhcpConfigServiceProfile payload) {
    DhcpServiceProfile dhcpPool = payload.getDhcpPools().stream()
        .filter(p -> p.getId().equals(dhcpServiceProfile.getId())).findFirst().get();
    assertEquals(dhcpPool.getName(), dhcpServiceProfile.getName());
    assertEquals(Integer.valueOf(dhcpPool.getVlanId()), dhcpServiceProfile.getVlanId());
    assertEquals(dhcpPool.getSubnetAddress(), dhcpServiceProfile.getSubnetAddress());
    assertEquals(dhcpPool.getSubnetMask(), dhcpServiceProfile.getSubnetMask());
    assertEquals(dhcpPool.getStartIpAddress(), dhcpServiceProfile.getStartIpAddress());
    assertEquals(dhcpPool.getEndIpAddress(), dhcpServiceProfile.getEndIpAddress());
    return true;
  }

  private void validateRepositoryData(DhcpConfigServiceProfileDeep expected,
      DhcpConfigServiceProfile actual) {
    assertEquals(expected.getId(), actual.getId());
    assertEquals(expected.getServiceName(), actual.getServiceName());
    assertEquals(expected.getDhcpMode(), actual.getDhcpMode());
    assertEquals(expected.getDhcpPools().size(), actual.getDhcpPools().size());

    for (DhcpPool expectedPool : expected.getDhcpPools()) {
      DhcpServiceProfile actualPool = actual.getDhcpPools().stream()
          .filter(pool -> pool.getName().equals(expectedPool.getName())).findFirst().orElse(null);
      assertNotNull(actualPool);
      assertEquals(expectedPool.getName(), actualPool.getName());
      assertEquals(expectedPool.getDescription(), actualPool.getDescription());
      assertEquals(expectedPool.getVlanId(), actualPool.getVlanId());
      assertEquals(expectedPool.getSubnetAddress(), actualPool.getSubnetAddress());
      assertEquals(expectedPool.getSubnetMask(), actualPool.getSubnetMask());
      assertEquals(expectedPool.getPrimaryDnsIp(), actualPool.getPrimaryDnsIp());
      assertEquals(expectedPool.getSecondaryDnsIp(), actualPool.getSecondaryDnsIp());
      assertEquals(expectedPool.getLeaseTimeHours(), actualPool.getLeaseTimeHours());
      assertEquals(expectedPool.getLeaseTimeMinutes(), actualPool.getLeaseTimeMinutes());
      assertEquals(expectedPool.getStartIpAddress(), actualPool.getStartIpAddress());
      assertEquals(expectedPool.getEndIpAddress(), actualPool.getEndIpAddress());
    }
  }

  private void validateNothingHappened(CfgAction action) {
    final TxChanges txChanges = revisionService.changes(txCtxExtension.getRequestId(),
        txCtxExtension.getTenantId(), action.key());
    assertThat(txChanges).isNotNull().matches(TxChanges::isEmpty);
    messageCaptors.assertThat(
        kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest(),
        kafkaTopicProvider.getActivityCfgChangeResp(),
        kafkaTopicProvider.getActivityImpacted()
    ).doesNotSendByTenant(txCtxExtension.getTenantId());
  }

  private void validateActivityMessages(String apiFlowNames) {
    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage = messageCaptors.getActivityCfgChangeMessageCaptor()
            .getValue(tenantId, requestId);
    assertThat(activityCfgChangeRespMessage).isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();

    assertThatNoException().isThrownBy(() -> assertThat(
        activityCfgChangeRespMessage.getPayload()).matches(
            msg -> msg.getStatus().equals(Status.OK)).matches(msg -> msg.getStep().equals(apiFlowNames))
        .extracting(ConfigurationStatus::getEventDate).isNotNull());

    messageCaptors.getActivityImpactDeviceMessageCaptor()
        .assertNotSentByTenant(tenantId);
  }

  private OpType opType(CfgAction apiAction) {
    return switch (apiAction) {
      case ADD_DHCP_CONFIG_SERVICE_PROFILE -> OpType.ADD;
      case UPDATE_DHCP_CONFIG_SERVICE_PROFILE -> OpType.MOD;
      case DELETE_DHCP_CONFIG_SERVICE_PROFILE, DELETE_DHCP_CONFIG_SERVICE_PROFILES -> OpType.DEL;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettings(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // When - enable venue dhcp setting
    // enable profile with 3 dhcpPools & 4 dhcpAps
    var poolsAmount = 3;
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(poolsAmount, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, dhcpConfigServiceProfileDeep);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpConfigServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // assert result - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueDhcpConfigServiceProfileSetting(venueId);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, dhcpAps,
        true, poolsAmount);

    // assert result - getDhcpConfigServiceProfile
    {
      DhcpConfigServiceProfileDeep configServiceProfile = getDhcpConfigServiceProfileDeep(venueDhcpServiceProfileSetting.getServiceProfileId());
      assertEquals(1, configServiceProfile.getUsage().size());
      assertEquals(venueId, configServiceProfile.getUsage().get(0).getVenueId());
    }
    // assert result - getDhcpConfigServiceProfiles
    final DhcpConfigServiceProfileDeep finalConfigServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    assertDhcpConfigServiceProfile(finalConfigServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"),
        List.of(venueId));

    // assert result - getVenueDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalConfigServiceProfile.getDhcpPools(),
        finalConfigServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), finalConfigServiceProfile.getDhcpPools(),
            result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    // expected invoke 1 dhcpConfigServiceProfile
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId, true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalConfigServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndBindToAnotherEachApProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venue dhcp setting
    // 3 dhcpPools & 4 dhcpAps in payload
    String dhcpServiceProfileId;
    {
      edaAddDhcpConfigServiceProfile(tenantId, userName, DhcpConfigServiceProfileTestFixture
          .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
      assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
      DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
      dhcpServiceProfileId = dhcpServiceProfile.getId();
      VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
          venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
      edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
      assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);
    }

    // When - venue bind To Another Profile
    String anotherDhcpServiceProfileId;
    {
      edaAddDhcpConfigServiceProfile(tenantId, userName, DhcpConfigServiceProfileTestFixture
          .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnEachAPs));
      assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
      DhcpConfigServiceProfileDeep anotherDhcpServiceProfile = getDhcpConfigServiceProfileDeeps().stream()
          .filter(e -> !e.getId().equals(dhcpServiceProfileId)).findFirst().orElse(null);
      anotherDhcpServiceProfileId = anotherDhcpServiceProfile.getId();
      VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
          venueDhcpConfigServiceProfileSetting(true, dhcpAps, anotherDhcpServiceProfile.getId());
      venueDhcpServiceProfileSetting.setServiceProfileId(anotherDhcpServiceProfile.getId());
      venueDhcpServiceProfileSetting.setDhcpServiceAps(null);
      edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
      assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

      // assert result - getVenueDhcpConfigServiceProfileSetting
      VenueDhcpConfigServiceProfileSetting result = getVenueDhcpConfigServiceProfileSetting(venueId);
      assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, null,
          false, 0);
    }

    // assert result - getDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep anotherDhcpServiceProfile = getDhcpConfigServiceProfileDeep(anotherDhcpServiceProfileId);
    assertEquals(1, anotherDhcpServiceProfile.getUsage().size());
    assertEquals(venueId, anotherDhcpServiceProfile.getUsage().get(0).getVenueId());
    assertNull(getDhcpConfigServiceProfileDeep(dhcpServiceProfileId).getUsage());

    // assert result - getDhcpConfigServiceProfiles
    List<DhcpConfigServiceProfileDeep> deeps = getDhcpConfigServiceProfileDeeps();
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile = deeps.stream()
        .filter(e -> e.getId().equals(dhcpServiceProfileId)).findFirst().orElse(null);
    DhcpConfigServiceProfileDeep finalAnotherDhcpServiceProfile = deeps.stream()
        .filter(e -> e.getId().equals(anotherDhcpServiceProfileId)).findFirst().orElse(null);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"), null);
    assertDhcpConfigServiceProfile(finalAnotherDhcpServiceProfile, "DhcpConfigServiceProfile#2",
        DhcpModeEnum.EnableOnEachAPs, List.of("DhcpPool#1", "DhcpPool#2"), List.of(venueId));

    // assert result - getVenueDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalAnotherDhcpServiceProfile.getDhcpPools(),
        finalAnotherDhcpServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), finalAnotherDhcpServiceProfile.getDhcpPools(), null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 3),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId, true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalAnotherDhcpServiceProfile),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalDhcpServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndBindToAnotherHierarchicalProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);
    List<DhcpServiceAp> anotherDhcpAps = dhcpService2ApList(apGroup);

    // enable venue dhcp setting
    // 3 dhcpPools & 4 dhcpAps in payload
    edaAddDhcpConfigServiceProfile(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - venue bind To Another Profile
    DhcpConfigServiceProfileDeep hierarchical = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnHierarchicalAPs);
    hierarchical.getDhcpPools().get(0).setVlanId((short) 1);
    edaAddDhcpConfigServiceProfile(tenantId, userName, hierarchical);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep anotherDhcpServiceProfile = getDhcpConfigServiceProfileDeeps().stream()
        .filter(e -> !e.getId().equals(dhcpServiceProfile.getId())).findFirst().orElse(null);
    venueDhcpServiceProfileSetting.setServiceProfileId(anotherDhcpServiceProfile.getId());
    venueDhcpServiceProfileSetting.setDhcpServiceAps(anotherDhcpAps);
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // assert result - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueDhcpConfigServiceProfileSetting(venueId);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, anotherDhcpAps,
        false, 0);

    // assert result - getDhcpConfigServiceProfile
    anotherDhcpServiceProfile = getDhcpConfigServiceProfileDeep(anotherDhcpServiceProfile.getId());
    var anotherDhcpServiceProfileId = anotherDhcpServiceProfile.getId();
    assertEquals(1, anotherDhcpServiceProfile.getUsage().size());
    assertEquals(venueId, anotherDhcpServiceProfile.getUsage().get(0).getVenueId());

    // assert result - getDhcpConfigServiceProfiles
    List<DhcpConfigServiceProfileDeep> deeps = getDhcpConfigServiceProfileDeeps();
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile = deeps.stream()
        .filter(e -> e.getId().equals(dhcpServiceProfile.getId())).findFirst().orElse(null);
    DhcpConfigServiceProfileDeep finalAnotherDhcpServiceProfile = deeps.stream()
        .filter(e -> e.getId().equals(anotherDhcpServiceProfileId)).findFirst().orElse(null);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"), null);
    assertNull(getDhcpConfigServiceProfile(dhcpServiceProfile.getId()).getDhcpPoolVenueUsage());
    assertDhcpConfigServiceProfile(finalAnotherDhcpServiceProfile, "DhcpConfigServiceProfile#2",
        DhcpModeEnum.EnableOnHierarchicalAPs, List.of("DhcpPool#1", "DhcpPool#2"), List.of(venueId));

    // assert result - getVenueDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalAnotherDhcpServiceProfile.getDhcpPools(),
        finalAnotherDhcpServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnHierarchicalAPs.toString(), finalAnotherDhcpServiceProfile.getDhcpPools(), result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 3),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId, true),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalAnotherDhcpServiceProfile),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalDhcpServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndDisableDhcpSettings(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfileSettings
    // 3 dhcpPools & 4 dhcpAps in payload
    edaAddDhcpConfigServiceProfile(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - disable venue dhcp setting
    venueDhcpServiceProfileSetting.setEnabled(false);
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // assert result - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueDhcpConfigServiceProfileSetting(venueId);
    venueDhcpServiceProfileSetting.setServiceProfileId(null);
    venueDhcpServiceProfileSetting.setDhcpServiceAps(null);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, null,
        false, 0);

    // assert result - getDhcpConfigServiceProfile
    dhcpServiceProfile = getDhcpConfigServiceProfileDeep(dhcpServiceProfile.getId());
    assertNull(dhcpServiceProfile.getUsage());

    // assert result - getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"),
        null);

    // assert result - getVenueDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, null, null);

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, false,
            null, null, null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, venueId, false),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalDhcpServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_venueEnableDhcpSettingsAndUpdateSameSettings(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfileSettings
    // 3 dhcpPools & 4 dhcpAps in payload
    edaAddDhcpConfigServiceProfile(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - update the same venue dhcp setting
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // assert result - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueDhcpConfigServiceProfileSetting(venueId);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, dhcpAps,
        true, 3);

    // assert result - getDhcpConfigServiceProfile
    dhcpServiceProfile = getDhcpConfigServiceProfileDeep(venueDhcpServiceProfileSetting.getServiceProfileId());
    assertEquals(1, dhcpServiceProfile.getUsage().size());
    assertEquals(venueId, dhcpServiceProfile.getUsage().get(0).getVenueId());

    // assert result - getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    assertDhcpConfigServiceProfile(finalDhcpServiceProfile, "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2", "DhcpPool#3"),
        List.of(venueId));

    // assert result - getVenueDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalDhcpServiceProfile.getDhcpPools(),
        finalDhcpServiceProfile.getDhcpPools());

    // assert ddccm - venue
    // expected invoke 1 venue (1 Venue operation and 1 VenueDhcpServiceSetting operation)
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    DhcpConfigServiceProfileDeep finalDhcpServiceProfile1 = dhcpServiceProfile;
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), finalDhcpServiceProfile1.getDhcpPools(), result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 1),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.MOD, finalDhcpServiceProfile)
    );
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_dhcpApOverMaxAmount_fail(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);
    dhcpAps.addAll(dhcpService4ApList(apGroup));
    dhcpAps.addAll(dhcpService4ApList(apGroup));

    // When - EnableOnEachAPs over max amount fail
    // 3 dhcpPools & 12 dhcpAps in payload
    edaAddDhcpConfigServiceProfile(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnEachAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep dhcpServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpServiceProfileSetting);

    // Then - EnableOnEachAPs over max amount fail
    assertActivityStatusFail(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10155, tenantId);

    // When - EnableOnMultipleAPs over max amount fail
    edaAddDhcpConfigServiceProfile(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    List<DhcpConfigServiceProfileDeep> dhcpConfigServiceProfiles = getDhcpConfigServiceProfileDeeps();
    DhcpConfigServiceProfileDeep dhcpServiceProfile2 = dhcpConfigServiceProfiles.stream()
        .filter(e -> !e.getId().equals(dhcpServiceProfile.getId())).findFirst().orElse(null);
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpAps, dhcpServiceProfile2.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpConfigServiceProfileSetting);

    // Then - EnableOnMultipleAPs over max amount fail
    assertActivityStatusFail(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10155, tenantId);
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_invalidDhcpServiceApRole_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    String ap2SN = randomSerialNumber();
    createAp(apGroup, ap2SN, "R720", randomMacAddress());
    List<DhcpServiceAp> dhcpServiceApList = new LinkedList<>();
    dhcpServiceApList.add(createDhcpServiceAp(ap2SN, ApDhcpRoleEnum.BackupServer));

    // When - EnableOnMultipleAPs invalidDhcpServiceApRole
    edaAddDhcpConfigServiceProfile(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnMultipleAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    List<DhcpConfigServiceProfileDeep> dhcpConfigServiceProfiles = getDhcpConfigServiceProfileDeeps();
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, dhcpServiceApList, dhcpConfigServiceProfiles.get(0).getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpConfigServiceProfileSetting);

    // Then - EnableOnMultipleAPs over max amount fail
    assertActivityStatusFail(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10135, tenantId);
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_disableVenueWithCellularAp_fail(Tenant tenant, @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion("*******.111") ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");
    createAp(apGroup, randomSerialNumber(), "M510", randomMacAddress());
    var venueId = v.getId();

    // EnableOnEachAPs
    edaAddDhcpConfigServiceProfile(tenantId, userName, DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(2, 2, DhcpModeEnum.EnableOnEachAPs));
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    List<DhcpConfigServiceProfileDeep> dhcpConfigServiceProfiles = getDhcpConfigServiceProfileDeeps();
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, null, dhcpConfigServiceProfiles.get(0).getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpConfigServiceProfileSetting);

    // When - disable venueDhcpConfigServiceProfileSetting with cellular ap
    venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(false, null, dhcpConfigServiceProfiles.get(0).getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId, venueDhcpConfigServiceProfileSetting);

    // Then - fail
    assertActivityStatusFail(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10139, tenantId);
  }

  @Test
  void testUpdateVenueDhcpConfigServiceProfileSettings_EnableDhcpSettingsWithNetworkVlanNotInclude_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService2ApList(apGroup);

    // add network bind venue
    com.ruckus.cloud.wifi.eda.viewmodel.PskNetwork networkDeepReq = pskNetwork("psk").generate();
    networkDeepReq.getWlan().setVlanId((short) 5);
    PskNetwork psk = addPskNetwork(map(networkDeepReq));
    edaAddNetworkVenue(tenantId, userName, psk.getId(), v.getId(), null);

    // When - enable venue dhcp setting
    // add dhcpConfigServiceProfile - EnableOnHierarchicalAPs
    DhcpConfigServiceProfileDeep hierarchical = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnHierarchicalAPs);
    hierarchical.getDhcpPools().get(0).setVlanId((short) 1);
    edaAddDhcpConfigServiceProfile(tenantId, userName, hierarchical);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    List<DhcpConfigServiceProfileDeep> dhcpConfigServiceProfiles = getDhcpConfigServiceProfileDeeps();
    DhcpConfigServiceProfileDeep customDhcpConfigServiceProfile = dhcpConfigServiceProfiles.get(0);

    // enable venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, customDhcpConfigServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpConfigServiceProfileSetting);

    // Then
    // assert activity
    assertActivityStatusFail(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, Errors.WIFI_10169, tenantId);
  }

  @Test
  void testDeactivateVenueDhcpPool_ActivateVenueDhcpPool(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, getDhcpConfigServiceProfileDeeps().get(0).getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - deactivate 1 dhcpPool
    final DhcpConfigServiceProfileDeep finalConfigServiceProfile =
        getDhcpConfigServiceProfileDeep(venueDhcpServiceProfileSetting.getServiceProfileId());
    DhcpPool deactivatePool = finalConfigServiceProfile.getDhcpPools().get(2);
    edaDeactivateDhcpPool(tenantId, userName, venueId, deactivatePool.getId());

    // Then - deactivate 1 dhcpPool
    // assert activity
    assertActivityStatusSuccess(ApiFlowNames.DEACTIVATE_VENUE_DHCP_POOL, tenantId);

    // assert result - getVenueDhcpPoolUsage
    List<DhcpPoolUsage> poolUsages = getVenueDhcpPoolUsage(venueId);
    List<DhcpPool> activeDhcpPools = finalConfigServiceProfile.getDhcpPools().stream()
        .filter(dhcpPool -> dhcpPool != deactivatePool).collect(Collectors.toList());
    assertDhcpPoolUsage(poolUsages, finalConfigServiceProfile.getDhcpPools(), activeDhcpPools);

    // assert result - getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting result = getVenueDhcpConfigServiceProfileSetting(
        venueId);
    assertVenueDhcpConfigServiceProfileSetting(result, venueDhcpServiceProfileSetting, dhcpAps,
        true, 2);

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), activeDhcpPools, result.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    assertCmnCfgCollectorCfgRequestNotSent(tenantId);

    // When - activate 1 dhcpPool
    edaActivateDhcpPool(tenantId, userName, venueId, deactivatePool.getId());

    // Then - activate 1 dhcpPool
    // assert activity
    assertActivityStatusSuccess(ACTIVATE_VENUE_DHCP_POOL, tenantId);
    // assert result - getVenueDhcpPoolUsage
    poolUsages = getVenueDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(poolUsages, finalConfigServiceProfile.getDhcpPools(),
        finalConfigServiceProfile.getDhcpPools());
    VenueDhcpConfigServiceProfileSetting result2 = getVenueDhcpConfigServiceProfileSetting(
        venueId);
    assertVenueDhcpConfigServiceProfileSetting(result2, venueDhcpServiceProfileSetting, dhcpAps,
        true, 3);

    // assert ddccm
    var activateDdccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(activateDdccmOperations, 2),
        () -> assertDdccmVenueAndVenueDhcpSetting(activateDdccmOperations, true, true,
            DhcpModeEnum.EnableOnMultipleAPs.toString(), finalConfigServiceProfile.getDhcpPools(), result2.getDhcpServiceAps())
    );

    // assert cmn-cfg-collector
    assertCmnCfgCollectorCfgRequestNotSent(tenantId);
  }

  @Test
  void testDeactivateVenueDhcpPool_deactivateLastDhcpPool_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // enable venueDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep savedConfigServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, savedConfigServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - deactivate last dhcpPool
    DhcpPool deactivatePool = configServiceProfile.getDhcpPools().get(0);
    edaDeactivateDhcpPool(tenantId, userName, venueId, deactivatePool.getId());

    // Then - deactivate last dhcpPool should disable venue dhcp setting
    // assert activity
    assertActivityStatusFail(DEACTIVATE_VENUE_DHCP_POOL, Errors.WIFI_10420, tenantId);

    // assert ddccm, cmn-cfg-collector
    assertNoMessages(tenantId, kafkaTopicProvider.getDdccmCfgRequest(),
        kafkaTopicProvider.getCmnCfgCollectorCfgRequest());
  }

  @Test
  void testActivateVenueDhcpPool_deactivateVenuePool_withVenueDhcpConfigSettingsDisabled_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();

    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);

    // When - activate 1 dhcpPool
    String dhcpPoolId1 = configServiceProfile.getDhcpPools().get(0).getId();
    edaActivateDhcpPool(tenantId, userName, venueId, dhcpPoolId1);

    // Then - activate 1 dhcpPool
    // assert activity
    assertActivityStatusFail(ACTIVATE_VENUE_DHCP_POOL, Errors.WIFI_10419, tenantId);

    // When - deactivate 1 dhcpPool
    edaDeactivateDhcpPool(tenantId, userName, venueId, dhcpPoolId1);

    // Then - deactivate 1 dhcpPool
    // assert activity
    assertActivityStatusFail(DEACTIVATE_VENUE_DHCP_POOL, Errors.WIFI_10419, tenantId);
  }

  @Test
  void testActivateVenueDhcpPool_activatePoolIdNotExistInDhcpConfigServiceProfile_fail(Tenant tenant) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // update venueDhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(1, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep savedConfigServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, savedConfigServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - activate 1 dhcpPool
    edaActivateDhcpPool(tenantId, userName, venueId, "otherDhcpPoolId");

    // Then - activate 1 dhcpPool
    // assert activity
    assertActivityStatusFail(ACTIVATE_VENUE_DHCP_POOL, Errors.WIFI_10420, tenantId);
  }

  @Test
  void testGetVenueDhcpPoolUsageWithIpCount(Tenant tenant) throws Exception {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService2ApList(apGroup);

    // enable venueDhcpConfigServiceProfileSetting
    // 4 dhcpPools & 2 dhcpAps in payload
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(4, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep savedConfigServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, savedConfigServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // get dhcpConfigServiceProfile
    configServiceProfile = getDhcpConfigServiceProfileDeep(savedConfigServiceProfile.getId());

    assertThat(configServiceProfile.getDhcpPools().size()).isEqualTo(4);
    var sortedPool = new ArrayList<>(configServiceProfile.getDhcpPools());
    sortedPool.sort(Comparator.comparing(DhcpPool::getName));

    assertThat(sortedPool).extracting(DhcpPool::getName)
            .containsSequence("DhcpPool#1", "DhcpPool#2", "DhcpPool#3", "DhcpPool#4");

    edaDeactivateDhcpPool(tenantId, userName, venueId, sortedPool.get(2).getId());
    edaDeactivateDhcpPool(tenantId, userName, venueId, sortedPool.get(3).getId());

    //mock ViewModelAgent
    ApDTO apOperationalData = apOperationalData(dhcpAps.get(0).getSerialNumber(),
        ApStateEnum.Operational, ApSubStateEnum.Operational);
    doReturn(Arrays.asList(apOperationalData)).when(viewmodelClientGrpc)
        .getApsBySerialNumber(any(), any(), anySet());
    DnbApDhcpPools.DhcpPoolData mockDhcpPoolData = createMockApDhcpPoolData();
    doReturn(mockDhcpPoolData).when(this.dhcpPoolStatsService)
        .request(any(), any(), any(), any(), any());

    // When
    List<DhcpPoolUsage> poolUsages = getVenueDhcpPoolUsage(venueId);
    var sortedUsagePool = new ArrayList<>(poolUsages);
    sortedUsagePool.sort(Comparator.comparing(DhcpPoolUsage::getName));

    assertThat(sortedUsagePool).extracting(DhcpPoolUsage::getName)
        .containsSequence("DhcpPool#1", "DhcpPool#2", "DhcpPool#3", "DhcpPool#4");

    // Then
    assertDhcpPoolUsage(sortedUsagePool, sortedPool, sortedPool.subList(0, 2));
    assertEquals((short) 100, (short)sortedUsagePool.get(0).getTotalIpCount());
    assertEquals((short) 2, (short)sortedUsagePool.get(0).getUsedIpCount());
    assertEquals((short) 10, (short)sortedUsagePool.get(1).getTotalIpCount());
    assertEquals((short) 1, (short)sortedUsagePool.get(1).getUsedIpCount());
  }

  private ApDTO apOperationalData(String serialNumber, ApStateEnum state,
                                        ApSubStateEnum subState) {
    String mockModel = "R730";
    com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO.Radio radio =
        new com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO.Radio();
    radio.setRadio5Ghz(new com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO.RadioParms(
        6, "max", 2, 1, StrictRadioTypeEnum._5_GHz));
    radio.setRadio24Ghz(new com.ruckus.cloud.wifi.client.viewmodel.dto.ApDTO.RadioParms(
        6, "max", 4, 0, StrictRadioTypeEnum._2_4_GHz));
    radio.setRadios(Arrays.asList(radio.getRadio5Ghz(), radio.getRadio24Ghz()));
    return ApDTO.builder().serialNumber(serialNumber)
        .model(mockModel)
        .clientCount(4)
        .externalIp("*******")
        .firmware("*******")
        .indoorModel(true)
        .ip("*******")
        .lastContacted("2020-01-23T10:49:19.562+0000")
        .lastUpdated("2020-01-23T10:49:19.562+0000")
        .mac("12:CF:23:44:55:55")
        .state(state)
        .subState(subState)
        .uptimeSeconds(1234125l)
        .radio(radio)
//        .poePortStatus("Up 1000Mbps")
        .build();

  }

  private List<ConnectedClientDTO> getMockDhcpClients() {
    //mock ViewModelAgent
    List<ConnectedClientDTO> mockClients = new ArrayList<>();
    ConnectedClientDTO mockClient1 = new ConnectedClientDTO();
    mockClient1.setHostname("hostname");
    mockClient1.setClientMac("E4:A7:A0:D4:15:84");
    mockClients.add(mockClient1);
    ConnectedClientDTO mockClient2 = new ConnectedClientDTO();
    mockClient2.setHostname("hostname 2");
    mockClient2.setClientMac("f4:a7:a0:d4:15:99");
    mockClients.add(mockClient2);

    return mockClients;
  }

  private DnbApDhcpPools.DhcpPoolData createMockApDhcpPoolData() {
    DnbApDhcpPools.DhcpPoolData.Builder dhcpPoolDataBuilder = DnbApDhcpPools.DhcpPoolData.newBuilder();
    dhcpPoolDataBuilder.setApMac("24:79:2A:28:48:D0");

    // poolInfoList
    DnbApDhcpPools.DhcpPerPoolStats.Builder dhcpPerPoolStatsBuilder1 = DnbApDhcpPools.DhcpPerPoolStats.newBuilder();
    dhcpPerPoolStatsBuilder1.setVlan(1001);
    dhcpPerPoolStatsBuilder1.setTotalIpCount(100);
    dhcpPerPoolStatsBuilder1.setUsedIpCount(2);

    // list 1 for poolInfoList 1
    DnbApDhcpPools.DhcpClientInfo.Builder dhcpClientInfoBuilder1 = DnbApDhcpPools.DhcpClientInfo.newBuilder();
    dhcpClientInfoBuilder1.setClientMac("e4:a7:a0:d4:15:84");
    dhcpClientInfoBuilder1.setClientIp("*************");
    dhcpClientInfoBuilder1.setLeaseExpiryTime(1590822371);

    // list 2 for poolInfoList 1
    DnbApDhcpPools.DhcpClientInfo.Builder dhcpClientInfoBuilder2 = DnbApDhcpPools.DhcpClientInfo.newBuilder();
    dhcpClientInfoBuilder2.setClientMac("f4:a7:a0:d4:15:99");
    dhcpClientInfoBuilder2.setClientIp("*************");
    dhcpClientInfoBuilder2.setLeaseExpiryTime(1590825971);

    dhcpPerPoolStatsBuilder1.addDhcpClients(dhcpClientInfoBuilder1);
    dhcpPerPoolStatsBuilder1.addDhcpClients(dhcpClientInfoBuilder2);

    dhcpPoolDataBuilder.addDhcpPoolInfo(dhcpPerPoolStatsBuilder1);

    // poolInfoList 2
    DnbApDhcpPools.DhcpPerPoolStats.Builder dhcpPerPoolStatsBuilder2 = DnbApDhcpPools.DhcpPerPoolStats.newBuilder();
    dhcpPerPoolStatsBuilder2.setVlan(1002);
    dhcpPerPoolStatsBuilder2.setTotalIpCount(10);
    dhcpPerPoolStatsBuilder2.setUsedIpCount(1);

    // list 3 for poolInfoList 2
    DnbApDhcpPools.DhcpClientInfo.Builder dhcpClientInfoBuilder3 = DnbApDhcpPools.DhcpClientInfo.newBuilder();
    dhcpClientInfoBuilder3.setClientMac("g1:a7:a0:d4:15:00");
    dhcpClientInfoBuilder3.setClientIp("**************");
    dhcpClientInfoBuilder3.setLeaseExpiryTime(1590821171);

    dhcpPerPoolStatsBuilder2.addDhcpClients(dhcpClientInfoBuilder3);

    dhcpPoolDataBuilder.addDhcpPoolInfo(dhcpPerPoolStatsBuilder2);

    return dhcpPoolDataBuilder.build();
  }

  @Test
  void testGetDhcpLeaseByVenue(Tenant tenant) throws Exception {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService2ApList(apGroup);

    // enable venueDhcpConfigServiceProfileSetting
    // 2 dhcpPools & 2 dhcpAps in payload
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(2, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep savedConfigServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, savedConfigServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // get dhcpConfigServiceProfile
    configServiceProfile = getDhcpConfigServiceProfileDeep(savedConfigServiceProfile.getId());

    //mock ViewModelAgent
    List<ConnectedClientDTO> mockClients = getMockDhcpClients();
    doReturn(getMockDhcpClients()).when(viewmodelClientGrpc)
        .getConnectedClientsByVenueId(any(), any(), any());
    DnbApDhcpPools.DhcpPoolData mockDhcpPoolData = createMockApDhcpPoolData();
    doReturn(mockDhcpPoolData).when(this.dhcpPoolStatsService)
        .request(any(), any(), any(), any(), any());

    // When
    List<DhcpConfigClientLeaseTime> dhcpClientLeaseTimeList = getDhcpPoolLeases(venueId);

    // Then
    assertApDhcpClientLeaseTimes(dhcpClientLeaseTimeList, configServiceProfile, mockClients);
    assertDhcpConfigServiceProfile(getDhcpConfigServiceProfileDeeps().get(0), "DhcpConfigServiceProfile#1",
        DhcpModeEnum.EnableOnMultipleAPs, List.of("DhcpPool#1", "DhcpPool#2"),
        List.of(venueId));
    configServiceProfile = getDhcpConfigServiceProfileDeep(savedConfigServiceProfile.getId());
    assertEquals(1, configServiceProfile.getUsage().size());
    assertEquals(venueId, configServiceProfile.getUsage().get(0).getVenueId());
    assertEquals((short) 3, (short)configServiceProfile.getUsage().get(0).getUsedIpCount());
  }

  private void assertApDhcpClientLeaseTimes(
      List<DhcpConfigClientLeaseTime> actualDhcpClientLeaseTimes,
      DhcpConfigServiceProfileDeep configServiceProfile, List<ConnectedClientDTO> expectedClients) {
    assertNotNull(actualDhcpClientLeaseTimes);

    Set<String> expectedDhcpProfileIds = configServiceProfile.getDhcpPools().stream()
        .map(DhcpPool::getId).collect(Collectors.toSet());
    Set<String> expectedDhcpProfileNames = configServiceProfile.getDhcpPools().stream()
        .map(DhcpPool::getName).collect(Collectors.toSet());

    // actualApDhcpClientLeaseTimes will be sorted by leaseExpiryTime
    DhcpConfigClientLeaseTime dhcpClientLeaseTime1 = actualDhcpClientLeaseTimes.get(1);

    assertEquals(dhcpClientLeaseTime1.getHostname(), expectedClients.get(0).getHostname());
    assertEquals(dhcpClientLeaseTime1.getIpAddress(), "*************");
    assertTrue(expectedDhcpProfileIds.contains(dhcpClientLeaseTime1.getDhcpPoolId()));
    assertTrue(expectedDhcpProfileNames.contains(dhcpClientLeaseTime1.getDhcpPoolName()));
    assertEquals(dhcpClientLeaseTime1.getMacAddress(), "e4:a7:a0:d4:15:84");
    assertEquals(dhcpClientLeaseTime1.getStatus(), "Online");
    assertEquals(dhcpClientLeaseTime1.getLeaseExpiration(),
        Instant.ofEpochSecond(1590822371).toString());

    DhcpConfigClientLeaseTime dhcpClientLeaseTime2 = actualDhcpClientLeaseTimes.get(2);

    assertEquals(dhcpClientLeaseTime2.getHostname(), expectedClients.get(1).getHostname());
    assertEquals(dhcpClientLeaseTime2.getIpAddress(), "*************");
    assertTrue(expectedDhcpProfileIds.contains(dhcpClientLeaseTime2.getDhcpPoolId()));
    assertTrue(expectedDhcpProfileNames.contains(dhcpClientLeaseTime2.getDhcpPoolName()));
    assertEquals(dhcpClientLeaseTime2.getMacAddress(), "f4:a7:a0:d4:15:99");
    assertEquals(dhcpClientLeaseTime2.getStatus(), "Online");
    assertEquals(dhcpClientLeaseTime2.getLeaseExpiration(),
        Instant.ofEpochSecond(1590825971).toString());

    DhcpConfigClientLeaseTime dhcpClientLeaseTime3 = actualDhcpClientLeaseTimes.get(0);

    assertEquals(dhcpClientLeaseTime3.getHostname(), null);
    assertEquals(dhcpClientLeaseTime3.getIpAddress(), "**************");
    assertTrue(expectedDhcpProfileIds.contains(dhcpClientLeaseTime3.getDhcpPoolId()));
    assertTrue(expectedDhcpProfileNames.contains(dhcpClientLeaseTime3.getDhcpPoolName()));
    assertEquals(dhcpClientLeaseTime3.getMacAddress(), "g1:a7:a0:d4:15:00");
    assertEquals(dhcpClientLeaseTime3.getStatus(), "Offline");
    assertEquals(dhcpClientLeaseTime3.getLeaseExpiration(),
        Instant.ofEpochSecond(1590821171).toString());
  }

  @Test
  void testAddGuestNetworkWithDhcpEnableTwice_venueWithoutDhcpConfigServiceProfile_Successfully(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    var venueId = v.getId();

    // When - addGuestNetwork1
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setName("Guest Deep 1");
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n1 = addGuestNetwork(map(networkDeepReq));
    edaAddNetworkVenue(tenantId, userName, n1.getId(), venueId, null);

    // Then - addGuestNetwork1
    // assert result - network
    assertNotNull(n1);
    assertTrue(n1.getEnableDhcp());
    assertNotNull(n1.getPortalServiceProfileId());

    // assert result - getDhcpConfigServiceProfiles
    final DhcpConfigServiceProfileDeep defaultDhcpServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultDhcpServiceProfile);
    assertEquals(venueId, defaultDhcpServiceProfile.getVenueIds().get(0));

    // assert result - venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        getVenueDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedVenueProfileSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedVenueProfileSetting.setEnabled(true); // venue dhcp should be enabled
    expectedVenueProfileSetting.setServiceProfileId(defaultDhcpServiceProfile.getId());
    assertVenueDhcpConfigServiceProfileSetting(venueDhcpConfigServiceProfileSetting,
        expectedVenueProfileSetting, null, false, 0);

    // assert ddccm
    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertAll("assert ddccm publisher",
          () -> assertDdccm(ddccmOperations, 5),
          () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD, defaultDhcpServiceProfile.getDhcpPools()),
          () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
              DhcpModeEnum.EnableOnEachAPs.toString(), defaultDhcpServiceProfile.getDhcpPools(), null)
      );
    }

    // assert cmn-cfg-collector
    {
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertCmnViewModelCollector(viewmodelOperations, 4),
          () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations, OpType.ADD, defaultDhcpServiceProfile)
      );
    }

    // When - addGuestNetwork2
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq2 = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq2.setName("Guest Deep 2");
    networkDeepReq2.setEnableDhcp(true);
    networkDeepReq2.setPortalServiceProfileId(networkDeepReq.getPortalServiceProfileId());
    GuestNetwork n2 = addGuestNetwork(map(networkDeepReq2)); // This can't reuse networkDeepReq somehow in wifi-eda
    addEdaNetworkVenueMapping(tenantId, userName, n2.getId(), venueId);

    // Then - addGuestNetwork2
    // assert result - network
    assertTrue(n2.getEnableDhcp());
    assertNotNull(n2.getPortalServiceProfileId());

    // assert result - getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep defaultDhcpServiceProfile2 = getDhcpConfigServiceProfileDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultDhcpServiceProfile2);
    assertEquals(venueId, defaultDhcpServiceProfile2.getVenueIds().get(0));

    // assert result - venueDhcpConfigServiceProfileSetting
    venueDhcpConfigServiceProfileSetting = getVenueDhcpConfigServiceProfileSetting(venueId);
    assertVenueDhcpConfigServiceProfileSetting(venueDhcpConfigServiceProfileSetting,
        expectedVenueProfileSetting, null, false, 0);

    // assert ddccm
    {
      var ddccmOperations = receiveDdccmOperations(1, tenantId);
      assertDdccmNoDhcpServiceProfile(ddccmOperations);
      assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations);
    }

    // assert cmn-cfg-collector
    {
      var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
      assertAll("assert viewmodel collector",
          () -> assertCmnViewModelCollector(viewmodelOperations, 2),
          () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
      );
    }
  }

  private void assertDefaultDhcpConfigServiceProfile(DhcpConfigServiceProfileDeep result) {
    assertEquals(defaultGuestNetworkConfig.getName(), result.getServiceName(),
        "Default DhcpConfigServiceProfile name should equal DHCP-Guest");
    assertEquals(DhcpModeEnum.EnableOnEachAPs, result.getDhcpMode(),
        "Default DhcpConfigServiceProfile mode should equal EnableOnEachAPs");
    assertEquals(1, result.getDhcpPools().size(),
        "Default DhcpConfigServiceProfile should have 1 dhcpPool");

    DhcpPool defaultDhcpPool = result.getDhcpPools().get(0);
    assertEquals(defaultGuestNetworkConfig.getName(), defaultDhcpPool.getName(),
        "Default DhcpPool name should equal DHCP-Guest");
    assertEquals((short) 3000, (short) defaultDhcpPool.getVlanId(),
        "Default DhcpPool vlanId should equal 3000");
  }

  @Test
  void testAddGuestNetworkWithDhcpEnable_venueWithDhcpConfigProfile_Successfully(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    ApGroup apGroup = createApGroup(v, "apg");

    var venueId = v.getId();
    List<DhcpServiceAp> dhcpAps = dhcpService4ApList(apGroup);

    // add dhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnMultipleAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep customConfigServiceProfileDeep = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, dhcpAps, customConfigServiceProfileDeep.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - addGuestNetwork
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setName("Guest Deep 1");
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork addedNetwork = addGuestNetwork(map(networkDeepReq));
    edaAddNetworkVenue(tenantId, userName, addedNetwork.getId(), venueId, null);

    // Then - addGuestNetwork result
    assertTrue(addedNetwork.getEnableDhcp());
    assertNotNull(addedNetwork.getPortalServiceProfileId());

    // assert venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting = getVenueDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedSetting.setEnabled(true);
    expectedSetting.setServiceProfileId(customConfigServiceProfileDeep.getId());
    assertVenueDhcpConfigServiceProfileSetting(venueDhcpConfigServiceProfileSetting,
        expectedSetting, dhcpAps, true, 0);

    // assert getVenueDhcpPoolUsage
    List<DhcpPoolUsage> dhcpPoolUsages = getVenueDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(dhcpPoolUsages, customConfigServiceProfileDeep.getDhcpPools(),
        customConfigServiceProfileDeep.getDhcpPools());

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 7),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 4),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  void testUpdateGuestNetworkWithDhcp_fromDhcpEnable_toDhcpDisable(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetwork with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetwork(map(networkDeepReq));
    edaAddNetworkVenue(tenantId, userName, n.getId(), venueId, null);

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());
    // When - updateGuestNetwork to dhcp disable
    n.setEnableDhcp(false);
    updateEdaNetwork(tenantId, userName, n.getId(), map(n));

    // Then
    GuestNetwork updatedNetwork = (GuestNetwork) getNetwork(n.getId());
    assertFalse(updatedNetwork.getEnableDhcp());
    assertNotNull(updatedNetwork.getPortalServiceProfileId());

    // assert ddccm - VenueDhcpServiceSetting
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  void testUpdateGuestNetworkWithDhcp_fromDhcpDisable_toDhcpEnable(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetwork with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(false);
    GuestNetwork n = addGuestNetwork(map(networkDeepReq));
    edaAddNetworkVenue(tenantId, userName, n.getId(), venueId, null);

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());
    // When - updateGuestNetwork to dhcp disable
    n.setEnableDhcp(true);
    updateEdaNetwork(tenantId, userName, n.getId(), map(n));

    // Then
    GuestNetwork updatedNetwork = (GuestNetwork) getNetwork(n.getId());
    assertTrue(updatedNetwork.getEnableDhcp());
    assertNotNull(updatedNetwork.getPortalServiceProfileId());

    // assert getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep defaultProfile = getDhcpConfigServiceProfileDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultProfile);
    assertEquals(venueId, defaultProfile.getVenueIds().get(0));

    // assert getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting actualSetting = getVenueDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedSetting.setEnabled(true);
    expectedSetting.setServiceProfileId(defaultProfile.getId());
    assertVenueDhcpConfigServiceProfileSetting(actualSetting, expectedSetting, null, false, 0);

    // assert ddccm - VenueDhcpServiceSetting
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 5),
        () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD, defaultProfile.getDhcpPools()),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), defaultProfile.getDhcpPools(), null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 4),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations,
            OpType.ADD, defaultProfile)
    );
  }

  @Test
  void testAddGuestNetworkVenueWithNetworkDhcpEnable_venueWithoutDhcpConfigProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetwork with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetwork(map(networkDeepReq));

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());
    // When - add NetworkVenue
    edaAddNetworkVenue(tenantId, userName, n.getId(), venueId, null);

    // Then - add NetworkVenue
    // assert getDhcpConfigServiceProfiles
    DhcpConfigServiceProfileDeep defaultProfile = getDhcpConfigServiceProfileDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultProfile);
    assertEquals(venueId, defaultProfile.getVenueIds().get(0));

    // assert getVenueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting actualSetting = getVenueDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedSetting.setEnabled(true);
    expectedSetting.setServiceProfileId(defaultProfile.getId());
    assertVenueDhcpConfigServiceProfileSetting(actualSetting, expectedSetting, null, false, 0);

    // assert ddccm - VenueDhcpServiceSetting
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 5),
        () -> assertDdccmDhcpServiceProfile(ddccmOperations, Action.ADD, defaultProfile.getDhcpPools()),
        () -> assertDdccmVenueAndVenueDhcpSetting(ddccmOperations, true, true,
            DhcpModeEnum.EnableOnEachAPs.toString(), defaultProfile.getDhcpPools(), null)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 4),
        () -> assertCmnViewModelCollectorDhcpConfigServiceProfile(viewmodelOperations,
            OpType.ADD, defaultProfile)
    );
  }

  @Test
  void testAddGuestNetworkVenueWithNetworkDhcpEnable_venueWithDhcpConfigProfile(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetwork with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetwork(map(networkDeepReq));

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());

    // add dhcpConfigServiceProfile
    DhcpConfigServiceProfileDeep configServiceProfile = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnEachAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, configServiceProfile);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);

    // enable venue dhcp
    DhcpConfigServiceProfileDeep customConfigServiceProfileDeep = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpServiceProfileSetting = venueDhcpConfigServiceProfileSetting(
        true, null, customConfigServiceProfileDeep.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, venueId,
        venueDhcpServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    // When - add NetworkVenue
    edaAddNetworkVenue(tenantId, userName, n.getId(), venueId, null);

    // Then - add NetworkVenue
    // assert venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        getVenueDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedSetting.setEnabled(true);
    expectedSetting.setServiceProfileId(customConfigServiceProfileDeep.getId());
    assertVenueDhcpConfigServiceProfileSetting(venueDhcpConfigServiceProfileSetting,
        expectedSetting, null, false, 0);

    // assert getVenueDhcpPoolUsage
    List<DhcpPoolUsage> dhcpPoolUsages = getVenueDhcpPoolUsage(venueId);
    assertDhcpPoolUsage(dhcpPoolUsages, customConfigServiceProfileDeep.getDhcpPools(), customConfigServiceProfileDeep.getDhcpPools());

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  public void testDeleteGuestNetworkVenueWithNetworkDhcpEnable_Successfully(Tenant tenant,
      @com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApVersion(DEFAULT_VERSION) ApVersion apVersion) {
    // Given
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v", apVersion);
    var venueId = v.getId();

    // addGuestNetwork with dhcp enable
    com.ruckus.cloud.wifi.eda.viewmodel.GuestNetwork networkDeepReq = getGuestNetwork(
        GuestNetworkTypeEnum.ClickThrough, n -> {});
    networkDeepReq.setEnableDhcp(true);
    GuestNetwork n = addGuestNetwork(map(networkDeepReq));

    // assert portalServiceProfileId
    assertNotNull(n.getPortalServiceProfileId());

    // add networkVenue
    edaAddNetworkVenue(tenantId, userName, n.getId(), venueId, null);
    NetworkVenue nv = getNetworkVenueByNetworkAndVenue(n.getId(), venueId);
    String networkVenueId = nv.getId();

    // When - delete networkVenue
    deleteEdaNetworkVenue(tenantId, userName, networkVenueId);

    // Then - delete networkVenue
    // assert result
    DhcpConfigServiceProfileDeep defaultProfile = getDhcpConfigServiceProfileDeeps().get(0);
    assertDefaultDhcpConfigServiceProfile(defaultProfile);
    assertEquals(venueId, defaultProfile.getVenueIds().get(0));

    // assert venueDhcpConfigServiceProfileSetting
    VenueDhcpConfigServiceProfileSetting actualVenueSetting =
        getVenueDhcpConfigServiceProfileSetting(venueId);
    VenueDhcpConfigServiceProfileSetting expectedVenueSetting = new VenueDhcpConfigServiceProfileSetting();
    expectedVenueSetting.setEnabled(true);
    expectedVenueSetting.setServiceProfileId(defaultProfile.getId());
    assertVenueDhcpConfigServiceProfileSetting(actualVenueSetting, expectedVenueSetting, null,
        false, 0);

    // assert ddccm
    var ddccmOperations = receiveDdccmOperations(1, tenantId);
    assertAll("assert ddccm publisher",
        () -> assertDdccm(ddccmOperations, 2),
        () -> assertDdccmNoDhcpServiceProfile(ddccmOperations),
        () -> assertDdccmNoVenueAndVenueDhcpSetting(ddccmOperations)
    );

    // assert cmn-cfg-collector
    var viewmodelOperations = receiveViewmodelCollectorOperations(1, tenantId);
    assertAll("assert viewmodel collector",
        () -> assertCmnViewModelCollector(viewmodelOperations, 2),
        () -> assertCmnViewModelCollectorNoDhcpConfigServiceProfile(viewmodelOperations)
    );
  }

  @Test
  void testUpdateNetworkVenueClientIsolationAllowlist_whenDhcpServiceEnabled_ShouldBeDisallowed(Tenant tenant) {
    var tenantId = txCtxExtension.getTenantId();
    var userName = txCtxExtension.getUserName();

    Venue v = createVenue(tenant, "v");

    // AddClientIsolationAllowlist
    ClientIsolationAllowlist allowlist = clientIsolationAllowlist("allowlist");
    addEdaClientIsolationProfile(tenantId, userName, mapToClientIsolationProfile(allowlist));
    assertActivityStatusSuccess(ADD_CLIENT_ISOLATION_PROFILE, tenantId);

    List<ClientIsolationAllowlist> allowlists = getClientIsolationAllowlists();
    assertEquals(1, allowlists.size());
    String clientIsolationAllowlistId = allowlists.get(0).getId();

    // add network
    PskNetwork pskNetwork = addPskNetwork(map(pskNetwork("pskNetwork").generate()));

    // add dhcp
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfileDeep = DhcpConfigServiceProfileTestFixture
        .dhcpConfigServiceProfileDeep(3, 1, DhcpModeEnum.EnableOnEachAPs);
    edaAddDhcpConfigServiceProfile(tenantId, userName, dhcpConfigServiceProfileDeep);
    assertActivityStatusSuccess(ADD_DHCP_CONFIG_SERVICE_PROFILE, tenantId);
    DhcpConfigServiceProfileDeep dhcpConfigServiceProfile = getDhcpConfigServiceProfileDeeps().get(0);
    VenueDhcpConfigServiceProfileSetting venueDhcpConfigServiceProfileSetting =
        venueDhcpConfigServiceProfileSetting(true, null, dhcpConfigServiceProfile.getId());
    edaUpdateVenueDhcpConfigServiceProfileSetting(tenantId, userName, v.getId(), venueDhcpConfigServiceProfileSetting);
    assertActivityStatusSuccess(UPDATE_VENUE_DHCP_CONFIG_SERVICE_PROFILE_SETTING, tenantId);

    edaAddNetworkVenue(tenantId, userName, pskNetwork.getId(), v.getId(), clientIsolationAllowlistId);
    assertActivityStatusFail(ADD_NETWORK_VENUE, Errors.WIFI_10243, tenantId);

    edaAddNetworkVenue(tenantId, userName, pskNetwork.getId(), v.getId(), null);
    assertActivityStatusSuccess(ADD_NETWORK_VENUE, tenantId);

    NetworkVenue nv = getNetworkVenueByNetworkAndVenue(pskNetwork.getId(), v.getId());
    updateEdaNetworkVenue(tenantId, userName, nv.getId(), map(nv), clientIsolationAllowlistId);
    assertActivityStatusFail(UPDATE_NETWORK_VENUE, Errors.WIFI_10243, tenantId);
  }

  @SneakyThrows
  private List<DhcpServiceAp> dhcpService4ApList(ApGroup apGroup) {
    String ap1SN = randomSerialNumber();
    String ap2SN = randomSerialNumber();
    String ap3SN = randomSerialNumber();
    String ap4SN = randomSerialNumber();

    createAp(apGroup, ap1SN, "R510", randomMacAddress());
    createAp(apGroup, ap2SN, "R720", randomMacAddress());
    createAp(apGroup, ap3SN, "R750", randomMacAddress());
    createAp(apGroup, ap4SN, "R320", randomMacAddress());

    List<DhcpServiceAp> dhcpServiceApList = new LinkedList<>();
    dhcpServiceApList.add(createDhcpServiceAp(ap1SN, ApDhcpRoleEnum.PrimaryServer));
    dhcpServiceApList.add(createDhcpServiceAp(ap2SN, ApDhcpRoleEnum.BackupServer));
    dhcpServiceApList.add(createDhcpServiceAp(ap3SN, ApDhcpRoleEnum.NatGateway));
    dhcpServiceApList.add(createDhcpServiceAp(ap4SN, ApDhcpRoleEnum.NatGateway));

    return dhcpServiceApList;
  }

  @SneakyThrows
  private List<DhcpServiceAp> dhcpService2ApList(ApGroup apGroup) {
    String ap1SN = randomSerialNumber();
    String ap2SN = randomSerialNumber();

    createAp(apGroup, ap1SN, "R510", randomMacAddress());
    createAp(apGroup, ap2SN, "R720", randomMacAddress());

    List<DhcpServiceAp> dhcpServiceApList = new LinkedList<>();
    dhcpServiceApList.add(createDhcpServiceAp(ap1SN, ApDhcpRoleEnum.PrimaryServer));
    dhcpServiceApList.add(createDhcpServiceAp(ap2SN, ApDhcpRoleEnum.BackupServer));

    return dhcpServiceApList;
  }

  private DhcpServiceAp createDhcpServiceAp(String serialNumber, ApDhcpRoleEnum role) {
    DhcpServiceAp dhcpServiceAp = new DhcpServiceAp();
    dhcpServiceAp.setSerialNumber(serialNumber);
    dhcpServiceAp.setRole(role);
    return dhcpServiceAp;
  }

  private void assertVenueDhcpConfigServiceProfileSetting(
      VenueDhcpConfigServiceProfileSetting result,
      VenueDhcpConfigServiceProfileSetting expectedVenueDhcpServiceProfileSetting,
      List<DhcpServiceAp> expectedDhcpAps, boolean shouldAssertIp, int ipAmount) {
    assertEquals(expectedVenueDhcpServiceProfileSetting.getServiceProfileId(), result.getServiceProfileId(),
        "ServiceProfileId should equal test data");
    assertEquals(expectedVenueDhcpServiceProfileSetting.getEnabled(), result.getEnabled(),
        "VenueDhcpServiceProfileSetting should be enabled / disabled");

    if (expectedDhcpAps != null) {
      List<String> expectedSerialNumbers = expectedDhcpAps.stream()
          .map(DhcpServiceAp::getSerialNumber).collect(Collectors.toList());
      List<String> actualSerialNumbers = result.getDhcpServiceAps().stream()
          .map(DhcpServiceAp::getSerialNumber).collect(Collectors.toList());
      DhcpServiceAp primaryDhcpSeviceAp = result.getDhcpServiceAps().stream()
          .filter(ap -> ApDhcpRoleEnum.PrimaryServer.equals(ap.getRole())).findFirst().get();
      DhcpServiceAp backupDhcpSeviceAp = result.getDhcpServiceAps().stream()
          .filter(ap -> ApDhcpRoleEnum.BackupServer.equals(ap.getRole())).findFirst().get();
      assertEquals(expectedDhcpAps.size(), result.getDhcpServiceAps().size(),
          "Should have DhcpServiceAps");
      assertTrue(actualSerialNumbers.containsAll(expectedSerialNumbers),
          "DhcpServiceAps serial numbers should equal test data");

      if (shouldAssertIp && ipAmount > 0) {
        List<String> primaryIps = Arrays.asList("***********", "***********", "***********",
            "***********");
        List<String> secondaryIps = Arrays.asList("***********", "***********", "***********",
            "***********");
        assertTrue(primaryDhcpSeviceAp.getDhcpIps().containsAll(primaryIps.subList(0, ipAmount)),
            "Primary DhcpServiceAp IP should equal test data");
        assertTrue(backupDhcpSeviceAp.getDhcpIps().containsAll(secondaryIps.subList(0, ipAmount)),
            "Secondary DhcpServiceAp IP should equal test data");
      }
    } else {
      assertEquals(0, result.getDhcpServiceAps().size(), "Should have no DhcpServiceAps");
    }
  }

  private void assertDhcpConfigServiceProfile(DhcpConfigServiceProfileDeep result,
                                              String expectedServiceName, DhcpModeEnum expectedDhcpMode, List<String> expectedDhcpPoolNames,
                                              List<String> expectedVenueIds) {
    assertEquals(expectedServiceName, result.getServiceName());
    assertEquals(expectedDhcpMode, result.getDhcpMode());
    assertEquals(expectedDhcpPoolNames.size(), result.getDhcpPools().size());
    List<String> actualDhcpPoolNames = result.getDhcpPools().stream().map(DhcpPool::getName)
        .collect(Collectors.toList());
    assertTrue(actualDhcpPoolNames.containsAll(expectedDhcpPoolNames));
    if (expectedVenueIds != null) {
      assertEquals(expectedVenueIds.size(), result.getVenueIds().size());
      assertTrue(result.getVenueIds().containsAll(expectedVenueIds));
      assertNull(result.getUsage());
    } else {
      assertEquals(0, result.getVenueIds().size());
    }
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getVlanId).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getSubnetAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getSubnetMask).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getStartIpAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getEndIpAddress).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getLeaseTimeHours).count());
    assertNotSame(0, (int) result.getDhcpPools().stream().map(DhcpPool::getLeaseTimeMinutes).count());
  }

  private void assertDhcpPoolUsage(List<DhcpPoolUsage> poolUsages,
                                   List<DhcpPool> expectedAllDhcpPools, List<DhcpPool> expectedActiveDhcpPools) {
    if (expectedAllDhcpPools == null) {
      assertEquals(0, poolUsages.size());
      return;
    }
    List<String> dhcpPoolIds = expectedAllDhcpPools.stream().map(DhcpPool::getId)
        .collect(Collectors.toList());
    List<Short> vlanIds = expectedAllDhcpPools.stream().map(DhcpPool::getVlanId)
        .collect(Collectors.toList());
    List<String> startIpAddress = expectedAllDhcpPools.stream().map(DhcpPool::getStartIpAddress)
        .collect(Collectors.toList());
    List<String> subnetAddress = expectedAllDhcpPools.stream().map(DhcpPool::getSubnetAddress)
        .collect(Collectors.toList());
    assertEquals(expectedAllDhcpPools.size(), poolUsages.size(), "Should have dhcpPool");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getId).collect(Collectors.toList())
        .containsAll(dhcpPoolIds), "dhcpPoolIds should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getVlanId).collect(Collectors.toList())
        .containsAll(vlanIds), "vlanId should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getStartIpAddress).collect(Collectors.toList())
        .containsAll(startIpAddress), "startIpAddress should equal test data");
    assertTrue(poolUsages.stream().map(DhcpPoolUsage::getSubnetAddress).collect(Collectors.toList())
        .containsAll(subnetAddress), "subnetAddress should equal test data");

    List<String> activeUsageIds = poolUsages.stream().filter(DhcpPoolUsage::getActive)
        .map(DhcpPoolUsage::getId).collect(Collectors.toList());
    if (expectedActiveDhcpPools != null) {
      assertEquals(expectedActiveDhcpPools.size(),
          activeUsageIds.size(), "Should have active dhcpPool");
      assertTrue(expectedActiveDhcpPools.stream().map(DhcpPool::getId).collect(Collectors.toList())
          .containsAll(activeUsageIds));
    } else {
      assertEquals(0, activeUsageIds.size(), "Should have no active dhcpPool");
    }
  }

  private void assertDdccmVenueAndVenueDhcpSetting(List<Operation> operations,
      boolean shouldAssertVenue, boolean expectedEnabled, String dhcpModeEnum,
      List<DhcpPool> expectedDhcpPools, List<DhcpServiceAp> expectedDhcpServiceAps) {

    // assert ddccm - venue
    List<com.ruckus.acx.ddccm.protobuf.wifi.Venue> ddccmVenue = operations.stream()
        .filter(o -> o.getAction().equals(Action.MODIFY))
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUE)).map(Operation::getVenue)
        .collect(Collectors.toList());
    if (shouldAssertVenue) {
      assertEquals(1, ddccmVenue.size(), "Should have one Venue operation send to ddccm");
    } else {
      assertEquals(0, ddccmVenue.size(), "Should have no Venue operation send to ddccm");
    }

    // assert ddccm - VenueDhcpServiceSetting
    List<VenueDhcpServiceSetting> ddccmVenueDhcpServiceSettings = operations.stream()
        .filter(o -> o.getAction().equals(Action.ADD)) // VENUEDHCPSERVICESETTING action is always add
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUEDHCPSERVICESETTING))
        .map(Operation::getVenueDhcpServiceSetting).collect(Collectors.toList());

    assertEquals(1, ddccmVenueDhcpServiceSettings.size(),
        "Should have one VenueDhcpServiceSetting operation send to ddccm");
    VenueDhcpServiceSetting ddccmVenueDhcpServiceSetting = ddccmVenueDhcpServiceSettings.get(0);
    assertEquals(expectedEnabled, ddccmVenueDhcpServiceSetting.getEnabled().getValue(),
        "Ddccm VenueDhcpServiceSetting enabled should be " + expectedEnabled);

    if (expectedEnabled) {
      assertEquals(dhcpModeEnum, ddccmVenueDhcpServiceSetting.getMode(),
          "Ddccm VenueDhcpServiceSetting mode error");
      assertEquals(
          expectedDhcpPools.size(),
          ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().size(),
          "Ddccm VenueDhcpServiceSetting should contain dhcpServiceProfileIds(dhcpPoolIds)");
      assertTrue(
          ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().containsAll(
              expectedDhcpPools.stream().map(DhcpPool::getId).collect(Collectors.toList())),
          "Ddccm VenueDhcpServiceSetting dhcpServiceProfileIds(dhcpPoolIds) should equal test data");

      if (expectedDhcpServiceAps != null) {
        assertEquals(expectedDhcpServiceAps.size(),
            ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
            "Ddccm Should have DhcpServiceAps");
        List<String> expectedIps = expectedDhcpServiceAps.stream()
            .flatMap(ap -> ap.getDhcpIps().stream()).collect(Collectors.toList());
        List<String> actualIps = ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().stream()
            .flatMap(ap -> ap.getNatGatewayIpList().stream()).collect(Collectors.toList());
        assertEquals(expectedIps.size(), actualIps.size());
        assertTrue(actualIps.containsAll(expectedIps),
            "Ddccm VenueDhcpServiceSetting ip should equal test data");
      } else {
        assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
            "Ddccm Should have no DhcpServiceAps");
      }
    } else {
      assertEquals("", ddccmVenueDhcpServiceSetting.getMode());
      assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceProfileIdsList().size(),
          "Ddccm VenueDhcpServiceSetting should have no dhcpServiceProfileIds(dhcpPoolIds)");
      assertEquals(0, ddccmVenueDhcpServiceSetting.getDhcpServiceApsList().size(),
          "Ddccm Should have no DhcpServiceAps");
    }
  }

  private void assertCmnViewModelCollectorDhcpConfigServiceProfile(
      List<Operations> operations, String venueId, boolean enabled) {
    assertTrue(operations.stream()
        .filter(o -> venueId.equals(o.getId()))
        .allMatch(o -> o.getDocMap().get("dhcp").getStructValue().getFieldsMap()
            .get("enabled").getBoolValue() == enabled));
  }

  private void assertCmnViewModelCollectorDhcpConfigServiceProfile(
      List<Operations> operations, OpType expectedOpType, DhcpConfigServiceProfileDeep expectedServiceProfile) {
    List<Operations> viewModelOperations = operations.stream()
        .filter(o -> expectedServiceProfile.getId().equals(o.getId())).collect(Collectors.toList());
    Operations operation = viewModelOperations.stream()
        .filter(o -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .findFirst().get();
    assertEquals(expectedOpType, operation.getOpType());

    if (!operation.getOpType().equals(OpType.DEL)) {
      assertEquals(expectedServiceProfile.getServiceName(),
          operation.getDocMap().get(Key.NAME).getStringValue());
      assertEquals(expectedServiceProfile.getDhcpPools().size(),
          operation.getDocMap().get(Key.DHCP_POOLS)
              .getListValue().getValuesCount());
      assertEquals(false,
          operation.getDocMap().get(Key.IS_TEMPLATE)
              .getBoolValue());

      List<String> venueIds = operation.getDocMap()
          .get(Key.VENUE_IDS).getListValue().getValuesList().stream()
          .map(Value::getStringValue).collect(Collectors.toList());
      if (expectedServiceProfile.getVenueIds() != null) {
        List<String> expectedVenueIds = expectedServiceProfile.getVenueIds();
        assertEquals(expectedVenueIds.size(), venueIds.size());
        assertTrue(expectedVenueIds.containsAll(venueIds));
      } else {
        assertEquals(0, venueIds.size());
      }
    }
  }

  private void assertCmnViewModelCollectorNoDhcpConfigServiceProfile(List<Operations> operations) {
    assertTrue(operations.stream().noneMatch(o -> DHCP_CONFIG_SERVICE_PROFILE_INDEX_NAME.equals(o.getIndex())));
  }

  private void assertDdccmDhcpServiceProfile(
      List<Operation> operations, Action expectedAction, List<DhcpPool> expectedDhcpPools) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile> ddccmDhcpServiceProfiles = operations
        .stream()
        .filter(o -> o.getAction().equals(expectedAction))
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.DHCPSERVICEPROFILE))
        .map(Operation::getDhcpServiceProfile).collect(Collectors.toList());

    // assert dhcpPoolId
    List<String> expectedDhcpPoolIds = expectedDhcpPools.stream().map(DhcpPool::getId)
        .collect(Collectors.toList());
    List<String> actualDhcpPoolIds = ddccmDhcpServiceProfiles.stream()
        .map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getId).collect(Collectors.toList());
    assertEquals(expectedDhcpPoolIds.size(), actualDhcpPoolIds.size());
    assertTrue(actualDhcpPoolIds.containsAll(expectedDhcpPoolIds));

    // assert dhcpPoolName
    List<String> expectedDhcpPoolNames = expectedDhcpPools.stream().map(DhcpPool::getName)
        .collect(Collectors.toList());
    List<String> actualDhcpPoolNames = ddccmDhcpServiceProfiles.stream()
        .map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getName).collect(Collectors.toList());
    assertEquals(expectedDhcpPoolNames.size(), actualDhcpPoolNames.size());
    assertTrue(ddccmDhcpServiceProfiles.stream().map(com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile::getName)
        .collect(Collectors.toList()).containsAll(actualDhcpPoolNames));
  }

  private void assertDdccm(List<Operation> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertCmnViewModelCollector(List<Operations> operations, int opsSize) {
    assertEquals(opsSize, operations.size());
  }

  private void assertDdccmNoDhcpServiceProfile(List<Operation> operations) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.DhcpServiceProfile> ddccmDhcpServiceProfiles = operations
        .stream()
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.DHCPSERVICEPROFILE))
        .map(Operation::getDhcpServiceProfile).collect(Collectors.toList());
    assertEquals(0, ddccmDhcpServiceProfiles.size(),
        "Should have no DhcpServiceProfile send to ddccm");
  }

  private void assertDdccmNoVenueAndVenueDhcpSetting(List<Operation> operations) {
    List<com.ruckus.acx.ddccm.protobuf.wifi.Venue> ddccmVenue = operations
        .stream()
        .filter(o -> o.getConfigCase().equals(Operation.ConfigCase.VENUE)).map(Operation::getVenue)
        .collect(Collectors.toList());
    assertEquals(0, ddccmVenue.size(),
        "Should have no Venue operation send to ddccm");
  }
}
