package com.ruckus.cloud.wifi.service.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.ruckus.cloud.wifi.eda.servicemodel.NetworkApGroup;
import com.ruckus.cloud.wifi.proto.grpc.IdQueryRequest;
import com.ruckus.cloud.wifi.service.ExtendedNetworkVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.NetworkVenueGrpcService;
import com.ruckus.cloud.wifi.service.impl.NetworkVenueGrpcServiceImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;

@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
public class NetworkVenueGrpcServiceTest {

  @Autowired
  NetworkVenueGrpcService unit;

  @MockBean
  ExtendedNetworkVenueServiceCtrl extendedNetworkVenueServiceCtrl;

  @Test
  void testGetNetworkApGroupsByApGroupIds(NetworkApGroup networkApGroup) {
    final var result = unit.getNetworkApGroupsByApGroupIds(
        IdQueryRequest.newBuilder().addIds(networkApGroup.getApGroup().getId()).build());

    assertThat(result.getNetworkApGroupsList())
        .hasSize(1)
        .singleElement()
        .satisfies(e -> {
          assertThat(e.getApGroupId()).isEqualTo(networkApGroup.getApGroup().getId());
          assertThat(e.getNetworkId()).isEqualTo(networkApGroup.getNetworkVenue().getNetwork().getId());
          assertThat(e.getVenueId()).isEqualTo(networkApGroup.getNetworkVenue().getVenue().getId());
          assertThat(e.getNetworkName()).isEqualTo(networkApGroup.getNetworkVenue().getNetwork().getName());
          assertThat(e.getIsSlaveNetwork()).isFalse();
        });
  }

  @Test
  void testGetNetworkApGroupsByVenueIds(NetworkApGroup networkApGroup) {
    final var result = unit.getNetworkApGroupsByVenueIds(
        IdQueryRequest.newBuilder().addIds(networkApGroup.getNetworkVenue().getVenue().getId()).build());

    assertThat(result.getNetworkApGroupsList())
        .hasSize(1)
        .singleElement()
        .satisfies(e -> {
          assertThat(e.getApGroupId()).isEqualTo(networkApGroup.getApGroup().getId());
          assertThat(e.getNetworkId()).isEqualTo(networkApGroup.getNetworkVenue().getNetwork().getId());
          assertThat(e.getVenueId()).isEqualTo(networkApGroup.getNetworkVenue().getVenue().getId());
          assertThat(e.getNetworkName()).isEqualTo(networkApGroup.getNetworkVenue().getNetwork().getName());
          assertThat(e.getIsSlaveNetwork()).isFalse();
        });
  }

  @TestConfiguration
  static class TestConfig {

    @Bean
    NetworkVenueGrpcService networkVenueGrpcService() {
      return new NetworkVenueGrpcServiceImpl();
    }
  }
}
