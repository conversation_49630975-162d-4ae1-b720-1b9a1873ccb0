package com.ruckus.cloud.wifi.integration.venue;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomMacAddress;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomName;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomSerialNumber;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.BoolValue;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.wifi.AntennaTypeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort8021XType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortType;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.PoeModeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.PortNameEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueApLanPort;
import com.ruckus.acx.ddccm.protobuf.wifi.VenueApModel;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.activity.ActivityConstant;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Mesh;
import com.ruckus.cloud.wifi.eda.servicemodel.SyslogServerProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.TripleBand;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApLanPortTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.BandModeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CalledStationIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.CellularNetworkSelectionEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.Channel6GEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.LteBandRegionEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.MeshRadioTypeEnumV1_1;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdDelimiterEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.NasIdTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WanConnectionEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.MdnsFencingGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.VenueApMulticastDnsFencingSettingsGenerator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.VenueSyslogServerProfileGenerator;
import com.ruckus.cloud.wifi.eda.viewmodel.ApModelExternalAntenna;
import com.ruckus.cloud.wifi.eda.viewmodel.ApModelLanPort;
import com.ruckus.cloud.wifi.eda.viewmodel.ApModelLed;
import com.ruckus.cloud.wifi.eda.viewmodel.BssColoring;
import com.ruckus.cloud.wifi.eda.viewmodel.DenialOfServiceProtection;
import com.ruckus.cloud.wifi.eda.viewmodel.LteBandLockChannel;
import com.ruckus.cloud.wifi.eda.viewmodel.MeshRadioTypeEnum;
import com.ruckus.cloud.wifi.eda.viewmodel.RadioParams6G;
import com.ruckus.cloud.wifi.eda.viewmodel.RadioParamsDual5G;
import com.ruckus.cloud.wifi.eda.viewmodel.SimSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApBssColoringSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApCellularSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApClientAdmissionControlSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApDirectedMulticastSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApDosProtectionSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApLoadBalancingSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApMeshSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApMeshSettingsV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelAntennaTypeSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelBandModeSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelCellular;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelExternalAntennaSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelLanPortSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApModelLedSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApRadio6GHzSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApRadio6GHzSettingsV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApRadioDual5GHzSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApRadioSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApRadioSettingsV1_1;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueApRadiusOptionSettings;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueClientAdmissionControl;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueDirectedMulticast;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueLanPort;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueLoadBalancing;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueRadioCustomization;
import com.ruckus.cloud.wifi.eda.viewmodel.VenueRadiusOptions;
import com.ruckus.cloud.wifi.entitylistener.ddccm.DdccmConstants;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.Template;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.VenueDecorator;
import com.ruckus.cloud.wifi.test.fixture.ApVersionTestFixture;
import com.ruckus.cloud.wifi.test.fixture.VenueFirmwareVersionTestFixture;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.ExtendedMessageUtil;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@WifiIntegrationTest
@Slf4j
class ConsumeVenueLevelConfigTest {

  @RegisterExtension
  private TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();
  @Autowired
  private ExtendedMessageUtil messageUtil;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  private RepositoryUtil repositoryUtil;
  @Autowired
  private MessageCaptors messageCaptors;

  @Nested
  class WhenConsumeUpdateVenueLanPortsRequest {

    @Test
    void thenUpdateVenueLanPorts(Venue venue) throws JsonProcessingException {
      final String tenantId = txCtxExtension.getTenantId();
      // default
      final var json = """
          [{
            "lanPorts": [
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "1",
                "enabled": true
              },
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "2",
                "enabled": true
              }
            ],
            "model": "R610",
            "poeOut": null,
            "poeMode": "Auto"
          }]
          """;
      final List<ApModelLanPort> requests = objectMapper.readValue(json, new TypeReference<>() {});
      // disable second port
      requests.get(0).getLanPorts().get(1).setEnabled(false);
      final String requestId1 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_LAN_PORTS, requestId1, venue.getId(), requests);
      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId1))
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId1))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(2)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(Operation::hasVenue)
                .hasSize(1).singleElement()
                .satisfies(assertVenueOperation(tenantId, venue, requests.get(0)));
            assertThat(ops)
                .filteredOn(Operation::hasApLanPortProfile)
                .hasSize(1).singleElement()
                .satisfies(apLanPortProfileOp -> {
                  assertThat(apLanPortProfileOp.getAction()).isEqualTo(Action.ADD);
                  assertThat(apLanPortProfileOp.getApLanPortProfile())
                      .satisfies(assertApLanPortProfile(tenantId, venue.getId(),
                          requests.get(0).getLanPorts().get(0)));
                });
          });

      // re-enable second port
      requests.get(0).getLanPorts().get(1).setEnabled(true);
      final String requestId2 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_LAN_PORTS, requestId2, venue.getId(), requests);
      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2))
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId2))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(2)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(Operation::hasVenue)
                .hasSize(1).singleElement()
                .satisfies(assertVenueOperation(tenantId, venue, requests.get(0)));
            assertThat(ops)
                .filteredOn(Operation::hasApLanPortProfile)
                .hasSize(1).singleElement()
                .satisfies(apLanPortProfileOp -> {
                  assertThat(apLanPortProfileOp.getAction()).isEqualTo(Action.MODIFY);
                  assertThat(apLanPortProfileOp.getApLanPortProfile())
                      .satisfies(assertApLanPortProfile(tenantId, venue.getId(),
                          requests.get(0).getLanPorts().get(1)));
                });
          });

      // change second port type
      requests.get(0).getLanPorts().get(1).setType(ApLanPortTypeEnum.ACCESS);
      requests.get(0).getLanPorts().get(1).setVlanMembers("1");
      final String requestId3 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_LAN_PORTS, requestId3, venue.getId(), requests);
      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId3))
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId3))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(3)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(Operation::hasVenue)
                .hasSize(1).singleElement()
                .satisfies(assertVenueOperation(tenantId, venue, requests.get(0)));
            assertThat(ops)
                .filteredOn(Operation::hasApLanPortProfile)
                .hasSize(2)
                .satisfies(apLanPortProfileOps -> {
                  assertThat(apLanPortProfileOps)
                      .filteredOn(op -> op.getAction() == Action.ADD)
                      .hasSize(1).singleElement()
                      .extracting(Operation::getApLanPortProfile)
                      .satisfies(assertApLanPortProfile(tenantId, venue.getId(),
                          requests.get(0).getLanPorts().get(1)));
                  assertThat(apLanPortProfileOps)
                      .filteredOn(op -> op.getAction() == Action.MODIFY)
                      .hasSize(1).singleElement()
                      .extracting(Operation::getApLanPortProfile)
                      .satisfies(assertApLanPortProfile(tenantId, venue.getId(),
                          requests.get(0).getLanPorts().get(0)));
                });
          });

      // change first port type and revert second port to default
      requests.get(0).getLanPorts().get(0).setType(ApLanPortTypeEnum.ACCESS);
      requests.get(0).getLanPorts().get(0).setVlanMembers("1");
      requests.get(0).getLanPorts().get(1).setType(ApLanPortTypeEnum.TRUNK);
      requests.get(0).getLanPorts().get(1).setVlanMembers("1-4094");
      final String requestId4 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_LAN_PORTS, requestId4, venue.getId(), requests);
      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId4))
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId4))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(3)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(Operation::hasVenue)
                .hasSize(1).singleElement()
                .satisfies(assertVenueOperation(tenantId, venue, requests.get(0)));
            final var venueApLanPorts = ops.stream()
                .filter(Operation::hasVenue)
                .flatMap(op -> op.getVenue().getVenueApModelsList().stream())
                .map(VenueApModel::getLanPortList).findAny().orElseThrow();
            final var apLanPortProfileId1 = venueApLanPorts.stream()
                .filter(venueApLanPort -> venueApLanPort.getPortName() == PortNameEnum.LAN1)
                .map(VenueApLanPort::getEthProfileName).findAny().orElseThrow();
            final var apLanPortProfileId2 = venueApLanPorts.stream()
                .filter(venueApLanPort -> venueApLanPort.getPortName() == PortNameEnum.LAN2)
                .map(VenueApLanPort::getEthProfileName).findAny().orElseThrow();
            assertThat(ops)
                .filteredOn(Operation::hasApLanPortProfile)
                .hasSize(2)
                .allSatisfy(op -> assertThat(op.getAction()).isEqualTo(Action.MODIFY))
                .satisfies(apLanPortProfileOps -> {
                  assertThat(apLanPortProfileOps)
                      .filteredOn(op -> apLanPortProfileId1.equals(op.getId()))
                      .hasSize(1).singleElement()
                      .extracting(Operation::getApLanPortProfile)
                      .satisfies(assertApLanPortProfile(tenantId, venue.getId(),
                          requests.get(0).getLanPorts().get(0)));
                  assertThat(apLanPortProfileOps)
                      .filteredOn(op -> apLanPortProfileId2.equals(op.getId()))
                      .hasSize(1).singleElement()
                      .extracting(Operation::getApLanPortProfile)
                      .satisfies(assertApLanPortProfile(tenantId, venue.getId(),
                          requests.get(0).getLanPorts().get(1)));
                });
          });

      // restore to default
      final List<ApModelLanPort> restoreRequests = objectMapper.readValue(json, new TypeReference<>() {});
      final String requestId5 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_LAN_PORTS, requestId5, venue.getId(), restoreRequests);
      assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId5))
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId5))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .extracting(WifiConfigRequest::getOperationsList,
              InstanceOfAssertFactories.list(Operation.class))
          .isNotEmpty().hasSize(2)
          .satisfies(ops -> {
            assertThat(ops)
                .filteredOn(Operation::hasVenue)
                .hasSize(1).singleElement()
                .satisfies(assertVenueOperation(tenantId, venue, restoreRequests.get(0)));
            assertThat(ops)
                .filteredOn(Operation::hasApLanPortProfile)
                .hasSize(1).singleElement()
                .satisfies(apLanPortProfileOp -> {
                  assertThat(apLanPortProfileOp.getAction()).isEqualTo(Action.MODIFY);
                  assertThat(apLanPortProfileOp.getApLanPortProfile())
                      .satisfies(assertApLanPortProfile(tenantId, venue.getId(),
                          restoreRequests.get(0).getLanPorts().get(0)));
                });
          });
    }

    static Consumer<Operation> assertVenueOperation(String tenantId, Venue venue,
        ApModelLanPort expectedApModelLanPort) {
      return venueOp -> {
        assertThat(venueOp.getAction()).isEqualTo(Action.MODIFY);
        assertThat(venueOp.getId()).isEqualTo(venue.getId());
        assertThat(venueOp.getVenue())
            .satisfies(venueProto -> {
              assertThat(venueProto.getTenantId()).isEqualTo(tenantId);
              assertThat(venueProto.getId()).isEqualTo(venue.getId());
              assertThat(venueProto.getName()).isEqualTo(venue.getName());
              assertThat(venueProto.getVenueApModelsCount()).isEqualTo(1);
              assertThat(venueProto.getVenueApModelsList())
                  .hasSize(1).singleElement()
                  .satisfies(assertVenueApModel(venue.getId(), expectedApModelLanPort));
            });
      };
    }

    static Consumer<VenueApModel> assertVenueApModel(String venueId, ApModelLanPort expected) {
      return venueApModel -> {
        assertThat(venueApModel.getVenueId()).isEqualTo(venueId);
        assertThat(venueApModel.getModelName()).isEqualTo(expected.getModel());
        assertThat(venueApModel.getPoeModeSetting())
            .isEqualTo(PoeModeEnum.valueOf(expected.getPoeMode().name()));
        assertThat(venueApModel.getLanPortCount()).isEqualTo(expected.getLanPorts().size());
        assertThat(venueApModel.getLanPortList())
            .hasSize(expected.getLanPorts().size())
            .satisfies(venueApLanPorts -> expected.getLanPorts()
                .forEach(expectedLanPort -> assertThat(venueApLanPorts)
                    .filteredOn(lanPort -> lanPort.getPortName()
                        == PortNameEnum.forNumber(
                        Integer.parseInt(expectedLanPort.getPortId())))
                    .hasSize(1).singleElement()
                    .satisfies(venueApLanPort ->
                        assertThat(venueApLanPort.getEnabled()).isEqualTo(
                            BoolValue.of(expectedLanPort.getEnabled())))));
      };
    }

    static Consumer<ApLanPortProfile> assertApLanPortProfile(String tenantId, String venueId,
        VenueLanPort expected) {
      return apLanPortProfile -> {
        assertThat(apLanPortProfile.getTenantId()).isEqualTo(tenantId);
        assertThat(apLanPortProfile.getUntagId()).isEqualTo(expected.getUntagId().intValue());
        assertThat(apLanPortProfile.getVlanMembers()).isEqualTo(expected.getVlanMembers());
        assertThat(apLanPortProfile.getLanPortType()).isEqualTo(
            ApLanPortType.valueOf("%sPORT".formatted(expected.getType())));
        assertThat(apLanPortProfile.getDynamicVlanEnabled()).isEqualTo(BoolValue.of(false));
        assertThat(apLanPortProfile.getPort8021X().getType()).isEqualTo(ApLanPort8021XType.DISABLE);
      };
    }

    @Test
    void thenUpdateVenueLanPortsImpactedAps(@ApModel("T750SE") Ap ap, Venue venue)
        throws JsonProcessingException {

      final String requestId = randomTxId();
      final String tenantId = txCtxExtension.getTenantId();

      final var apGroups = Generators.apGroup()
          .setVenue(always(venue)).generate(2);

      apGroups.forEach(apGroup -> {
        Ap apIn = new Ap(randomSerialNumber());
        apIn.setApGroup(apGroup);
        apIn.setName(randomName());
        apIn.setMac(randomMacAddress());
        repositoryUtil.createOrUpdate(apGroup, tenantId, randomTxId());
        repositoryUtil.createOrUpdate(apIn, tenantId, randomTxId());
      });

      // default
      final List<ApModelLanPort> requests = objectMapper.readValue("""
          [{
            "lanPorts": [
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "1",
                "enabled": true
              },
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "2",
                "enabled": true
              }
            ],
            "model": "R610",
            "poeOut": null,
            "poeMode": "Auto"
          }]
          """, new TypeReference<>() {});
      // disable second port
      requests.get(0).getLanPorts().get(1).setEnabled(false);

      sendRequest(CfgAction.UPDATE_VENUE_LAN_PORTS, requestId, venue.getId(), requests);

      assertActivitySuccess(requestId, ApiFlowNames.UPDATE_VENUE_LAN_PORTS);

      assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
          .isNotNull()
          .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
          .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
          .extracting(KafkaProtoMessage::getPayload).isNotNull()
          .satisfies(msg -> {
            assertThat(msg.getStepId()).isEqualTo(ActivityConstant.IMPACTED_STEP_ID);
            assertThat(msg.getDeviceIdsCount()).isEqualTo(3);
          });
    }
  }

  @Nested
  class WhenConsumeUpdateVenueLedOnRequest {

    @Test
    void thenUpdateVenueLedOn(Venue venue) throws JsonProcessingException {
      // default
      final List<ApModelLed> requests = objectMapper.readValue("""
          [{
            "ledEnabled": true,
            "model": "R610"
          }]
          """, new TypeReference<>() {});

      // Disable R610 LED
      requests.get(0).setLedEnabled(Boolean.FALSE);
      final String requestId1 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_LED_ON, requestId1, venue.getId(), requests);

      assertActivitySuccess(requestId1, ApiFlowNames.UPDATE_VENUE_LED_ON);

      // Re-enable R610 LED
      requests.get(0).setLedEnabled(Boolean.TRUE);
      final String requestId2 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_LED_ON, requestId2, venue.getId(), requests);

      assertActivitySuccess(requestId2, ApiFlowNames.UPDATE_VENUE_LED_ON);
    }

    @Test
    void thenUpdateVenueLedOnWhenVenueApModelSpecificAttributesExisted(Venue venue)
        throws JsonProcessingException {
      // default
      final List<ApModelLanPort> lanPorts = objectMapper.readValue("""
          [{
            "lanPorts": [
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "1",
                "enabled": true
              },
              {
                "type": "TRUNK",
                "untagId": 1,
                "vlanMembers": "1-4094",
                "portId": "2",
                "enabled": true
              }
            ],
            "model": "R610",
            "poeOut": null,
            "poeMode": "Auto"
          }]
          """, new TypeReference<>() {});
      // disable second port
      lanPorts.get(0).getLanPorts().get(1).setEnabled(false);
      final String requestId1 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_LAN_PORTS, requestId1, venue.getId(), lanPorts);

      assertActivitySuccess(requestId1, ApiFlowNames.UPDATE_VENUE_LAN_PORTS);

      // default
      final List<ApModelLed> requests = objectMapper.readValue("""
          [{
            "ledEnabled": true,
            "model": "R610"
          }]
          """, new TypeReference<>() {});
      // Disable R610 LED
      requests.get(0).setLedEnabled(Boolean.FALSE);
      final String requestId2 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_LED_ON, requestId2, venue.getId(), requests);

      assertActivitySuccess(requestId2, ApiFlowNames.UPDATE_VENUE_LED_ON);
    }
  }

  @Nested
  class WhenConsumeUpdateVenueApModelCellularRequest {

    @Test
    void thenVenueApModelCellular(Venue venue) throws JsonProcessingException {
      // default
      final var request1 = objectMapper.readValue("""
          {
            "model": "M510",
            "primarySim": {
              "lteBands": [],
              "enabled": true,
              "apn": "defaultapn",
              "roaming": true,
              "networkSelection": "AUTO"
            },
            "secondarySim": {
              "lteBands": [],
              "enabled": true,
              "apn": "defaultapn",
              "roaming": true,
              "networkSelection": "AUTO"
            },
            "wanConnection": "ETH_WITH_CELLULAR_FAILOVER",
            "primaryWanRecoveryTimer": 60
          }
          """, VenueApModelCellular.class);
      final String requestId1 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_AP_MODEL_CELLULAR, requestId1, venue.getId(), request1);

      assertActivitySuccess(requestId1, ApiFlowNames.UPDATE_VENUE_AP_MODEL_CELLULAR);

      // Set sim1 3GBand: "B2", sim2 4GBand: "B4"
      final var request2 = objectMapper.readValue("""
          {
            "model": "M510",
            "primarySim": {
              "lteBands": [
                {
                  "band3G": [
                    "B2"
                  ],
                  "band4G": null,
                  "region": "USA_CANADA"
                }
              ],
              "enabled": true,
              "apn": "defaultapn",
              "roaming": true,
              "networkSelection": "AUTO"
            },
            "secondarySim": {
              "lteBands": [
                {
                  "band3G": null,
                  "band4G": [
                    "B4"
                  ],
                  "region": "USA_CANADA"
                }
              ],
              "enabled": true,
              "apn": "defaultapn",
              "roaming": true,
              "networkSelection": "AUTO"
            },
            "wanConnection": "ETH_WITH_CELLULAR_FAILOVER",
            "primaryWanRecoveryTimer": 60
          }
          """, VenueApModelCellular.class);
      final String requestId2 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_AP_MODEL_CELLULAR, requestId2, venue.getId(), request2);

      assertActivitySuccess(requestId2, ApiFlowNames.UPDATE_VENUE_AP_MODEL_CELLULAR);

      // Set sim1 4GBand: "B4", sim2 3GBand: "B2"
      final var request3 = objectMapper.readValue("""
          {
            "model": "M510",
            "primarySim": {
              "lteBands": [
                {
                  "band3G": null,
                  "band4G": [
                    "B4"
                  ],
                  "region": "USA_CANADA"
                }
              ],
              "enabled": true,
              "apn": "defaultapn",
              "roaming": true,
              "networkSelection": "AUTO"
            },
            "secondarySim": {
              "lteBands": [
                {
                  "band3G": [
                    "B2"
                  ],
                  "band4G": null,
                  "region": "USA_CANADA"
                }
              ],
              "enabled": true,
              "apn": "defaultapn",
              "roaming": true,
              "networkSelection": "AUTO"
            },
            "wanConnection": "ETH_WITH_CELLULAR_FAILOVER",
            "primaryWanRecoveryTimer": 60
          }
          """, VenueApModelCellular.class);
      final String requestId3 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_AP_MODEL_CELLULAR, requestId3, venue.getId(), request3);

      assertActivitySuccess(requestId3, ApiFlowNames.UPDATE_VENUE_AP_MODEL_CELLULAR);
    }
  }

  @Nested
  class WhenConsumeUpdateVenueExternalAntennaRequest {

    @Test
    void thenVenueExternalAntennaVenue(Venue venue) throws JsonProcessingException {
      // default
      final List<ApModelExternalAntenna> request = objectMapper.readValue("""
          [
            {
              "enable24G": false,
              "enable50G": false,
              "model": "E510"
            },
            {
              "enable50G": false,
              "model": "T300E"
            },
            {
              "enable24G": false,
              "enable50G": false,
              "model": "T350SE"
            },
            {
              "enable24G": false,
              "enable50G": false,
              "model": "T750SE"
            }
          ]
          """, new TypeReference<>() {});

      final String requestId1 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_EXTERNAL_ANTENNA, requestId1, venue.getId(), request);

      assertActivitySuccess(requestId1, ApiFlowNames.UPDATE_VENUE_EXTERNAL_ANTENNA);

      // E510 Enable 2.4
      request.stream()
          .filter(ea -> "E510".equals(ea.getModel()))
          .findAny().ifPresent(ea -> {
            ea.setEnable24G(true);
            ea.setGain24G(3);
          });
      final String requestId2 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_EXTERNAL_ANTENNA, requestId2, venue.getId(), request);

      assertActivitySuccess(requestId2, ApiFlowNames.UPDATE_VENUE_EXTERNAL_ANTENNA);

      // E510 Disable 2.4, enable 5
      request.stream()
          .filter(ea -> "E510".equals(ea.getModel()))
          .findAny().ifPresent(ea -> {
            ea.setEnable24G(null);
            ea.setGain24G(null);
            ea.setEnable50G(true);
            ea.setGain50G(3);
          });
      final String requestId3 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_EXTERNAL_ANTENNA, requestId3, venue.getId(), request);

      assertActivitySuccess(requestId3, ApiFlowNames.UPDATE_VENUE_EXTERNAL_ANTENNA);

      // revert
      request.stream()
          .filter(ea -> "E510".equals(ea.getModel()))
          .findAny().ifPresent(ea -> {
            ea.setEnable24G(null);
            ea.setGain24G(null);
            ea.setEnable50G(null);
            ea.setGain50G(null);
          });
      final String requestId4 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_EXTERNAL_ANTENNA, requestId4, venue.getId(), request);

      assertActivitySuccess(requestId4, ApiFlowNames.UPDATE_VENUE_EXTERNAL_ANTENNA);
    }
  }

  @Nested
  class WhenConsumeUpdateVenueX670Request {
    @Test
    @FeatureFlag(enable = FlagNames.WIFI_SWITCHABLE_RF_TOGGLE)
    void thenVenueBandMode(Venue venue) throws JsonProcessingException {
      upgradeVenueToAbfWifi7Version(venue);
      final String tenantId = txCtxExtension.getTenantId();
      // default
      final List<VenueApModelBandModeSettings> request = objectMapper.readValue("""
          [{
            "model": "R670",
            "bandMode": "DUAL"
          }]
          """, new TypeReference<>() {
      });
      final String requestId1 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_BAND_MODE, requestId1, venue.getId(), request);

      assertActivitySuccess(requestId1, ApiFlowNames.UPDATE_VENUE_BAND_MODE);

      final var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId1);
      assertThat(record).isNotNull();
      WifiConfigRequest ddccmRequest = record.getPayload();
      log.debug("ddccmRequest={}", ddccmRequest);

      assertThat(ddccmRequest.getOperationsList()).isNotNull()
          .matches(req -> req.stream().anyMatch(Operation::hasVenue))
          .hasSize(1).first()
          .extracting(Operation::getVenue)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getVenueApModelsList)
          .isNotNull().asList().first()
          .extracting(VenueApModel.class::cast)
          .matches(m -> m.getBandCombinationMode().getValue().equals(DdccmConstants.BAND_MODE_DUAL))
          .matches(m -> m.getModelName().equals("R670"));

      // Update empty request
      final String requestId2 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_BAND_MODE, requestId2, venue.getId(), List.of()); // Empty request

      assertActivitySuccess(requestId2, ApiFlowNames.UPDATE_VENUE_BAND_MODE);

      final var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
      assertThat(record2).isNotNull();
      WifiConfigRequest ddccmRequest2 = record2.getPayload();
      log.debug("ddccmRequest2={}", ddccmRequest2);
      assertThat(ddccmRequest2.getOperationsList()).isNotNull()
          .matches(req -> req.stream().anyMatch(Operation::hasVenue))
          .hasSize(1).first()
          .extracting(Operation::getVenue)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getVenueApModelsList)
          .isNotNull().asList().first()
          .extracting(VenueApModel.class::cast)
          .matches(m -> m.getModelName().equals("R670"))
          .extracting(VenueApModel::getBandCombinationMode)
          .matches(b -> StringUtils.isEmpty(b.getValue()));   // getBandCombinationMode is empty
    }

    @Test
    @FeatureFlag(enable = FlagNames.WIFI_ANTENNA_TYPE_SELECTION_TOGGLE)
    void thenVenueAntennaType(Venue venue) throws JsonProcessingException {
      upgradeVenueToAbfWifi7Version(venue);
      final String tenantId = txCtxExtension.getTenantId();
      // default
      final List<VenueApModelAntennaTypeSettings> request = objectMapper.readValue("""
          [{
            "model": "T670SN",
            "antennaType": "NARROW"
          }]
          """, new TypeReference<>() {});
      final String requestId1 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_ANTENNA_TYPE, requestId1, venue.getId(), request);

      assertActivitySuccess(requestId1, ApiFlowNames.UPDATE_VENUE_ANTENNA_TYPE);

      final var record = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId1);
      assertThat(record).isNotNull();
      WifiConfigRequest ddccmRequest = record.getPayload();
      log.debug("ddccmRequest={}", ddccmRequest);

      assertThat(ddccmRequest.getOperationsList()).isNotNull()
          .matches(req -> req.stream().anyMatch(Operation::hasVenue))
          .hasSize(1).first()
          .extracting(Operation::getVenue)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getVenueApModelsList)
          .isNotNull().asList().first()
          .extracting(VenueApModel.class::cast)
          .matches(m -> m.getAntennaType().equals(AntennaTypeEnum.Narrow))
          .matches(m -> m.getModelName().equals("T670SN"));

      // Update empty request
      final String requestId2 = randomTxId();
      sendRequest(CfgAction.UPDATE_VENUE_ANTENNA_TYPE, requestId2, venue.getId(), List.of()); // Empty request

      assertActivitySuccess(requestId2, ApiFlowNames.UPDATE_VENUE_ANTENNA_TYPE);

      final var record2 = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId2);
      assertThat(record2).isNotNull();
      WifiConfigRequest ddccmRequest2 = record2.getPayload();
      log.debug("ddccmRequest2={}", ddccmRequest2);

      assertThat(ddccmRequest2.getOperationsList()).isNotNull()
          .matches(req -> req.stream().anyMatch(Operation::hasVenue))
          .hasSize(1).first()
          .extracting(Operation::getVenue)
          .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Venue::getVenueApModelsList)
          .isNotNull().asList().first()
          .extracting(VenueApModel.class::cast)
          .matches(m -> m.getModelName().equals("T670SN"))
          .matches(m -> m.getAntennaType().equals(AntennaTypeEnum.AntennaTypeEnum_UNSET));

    }

    void upgradeVenueToAbfWifi7Version(Venue venue) {
      ApVersion abf1Version = ApVersionTestFixture.recommendedApVersion("6.2.0.103.105", v -> {});
      ApVersion abf2Version = ApVersionTestFixture.recommendedApVersion("6.2.3.103.114", v -> {});
      ApVersion abf3Version = ApVersionTestFixture.recommendedApVersion("7.0.0.105.212", v -> {});
      repositoryUtil.createOrUpdate(abf1Version, venue.getTenant().getId(), randomTxId());
      repositoryUtil.createOrUpdate(abf2Version, venue.getTenant().getId(), randomTxId());
      repositoryUtil.createOrUpdate(abf3Version, venue.getTenant().getId(), randomTxId());
      VenueFirmwareVersion abf1Vfv = VenueFirmwareVersionTestFixture.randomVenueFirmwareVersion(venue, abf1Version,
          "eol-ap-2022-12");
      VenueFirmwareVersion abf2Vfv = VenueFirmwareVersionTestFixture.randomVenueFirmwareVersion(venue, abf2Version,
          "ABF2-3R");
      repositoryUtil.createOrUpdate(abf1Vfv, venue.getTenant().getId(), randomTxId());
      repositoryUtil.createOrUpdate(abf2Vfv, venue.getTenant().getId(), randomTxId());
      venue.setWifiFirmwareVersion(abf3Version);
      venue.setApPassword("1qaz@WSX");
      repositoryUtil.createOrUpdate(venue, venue.getTenant().getId(), randomTxId());
    }
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateBssColoring(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new BssColoring();
    request.setBssColoringEnabled(false);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_BSS_COLORING_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_BSS_COLORING_SETTINGS);
  }

  @Test
  void testUpdateVenueTemplateApBssColoringSettings(@Template Venue venue) {
    var request = new VenueApBssColoringSettings();
    request.setBssColoringEnabled(false);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_BSS_COLORING_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_BSS_COLORING_SETTINGS);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateRadioCustomization(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new VenueRadioCustomization();
    var radioParamsDual5G = new RadioParamsDual5G();
    radioParamsDual5G.setEnabled(false);
    request.setRadioParamsDual5G(radioParamsDual5G);
    var radioParams6G = new RadioParams6G();
    radioParams6G.setAllowedChannels(List.of(Channel6GEnum._1, Channel6GEnum._5, Channel6GEnum._9));
    request.setRadioParams6G(radioParams6G);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_RADIO_CUSTOMIZATION, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_RADIO_CUSTOMIZATION);
  }

  @Test
  void testUpdateVenueTemplateApRadioSettings(@Template Venue venue) {
    var request = new VenueApRadioSettings();
    var radioParamsDual5G = new VenueApRadioDual5GHzSettings();
    radioParamsDual5G.setEnabled(false);
    request.setRadioParamsDual5G(radioParamsDual5G);
    var radioParams6G = new VenueApRadio6GHzSettings();
    radioParams6G.setAllowedChannels(List.of(Channel6GEnum._1, Channel6GEnum._5, Channel6GEnum._9));
    request.setRadioParams6G(radioParams6G);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_RADIO_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_RADIO_CUSTOMIZATION);
  }

  @Test
  void testUpdateVenueTemplateApRadioSettingsV1_1(@Template Venue venue) {
    var request = new VenueApRadioSettingsV1_1();
    var radioParamsDual5G = new VenueApRadioDual5GHzSettings();
    radioParamsDual5G.setEnabled(false);
    request.setRadioParamsDual5G(radioParamsDual5G);
    var radioParams6G = new VenueApRadio6GHzSettingsV1_1();
    radioParams6G.setAllowedChannels(List.of(Channel6GEnum._1, Channel6GEnum._5, Channel6GEnum._9));
    request.setRadioParams6G(radioParams6G);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_RADIO_SETTINGS_V1_1, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_RADIO_CUSTOMIZATION);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testResetVenueTemplateRadioCustomization(
      @VenueDecorator(isTemplate = true) Venue venue) {

    sendRequest(CfgAction.RESET_VENUE_TEMPLATE_RADIO_CUSTOMIZATION, venue.getId(), "");

    assertVenueTemplateApi(ApiFlowNames.RESET_VENUE_TEMPLATE_RADIO_CUSTOMIZATION);
  }

  @Deprecated(forRemoval = true)
  @Test
  void tesUpdateVenueTemplateLoadBalancing(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new VenueLoadBalancing();
    request.setEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_LOAD_BALANCING, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_LOAD_BALANCING);
  }

  @Test
  void testUpdateVenueTemplateApLoadBalancingSettings(@Template Venue venue) {
    var request = new VenueApLoadBalancingSettings();
    request.setEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_LOAD_BALANCING_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_LOAD_BALANCING);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateClientAdmissionControlSettings(
      @VenueDecorator(isTemplate = true,
          bandBalancingEnabled = false) Venue venue) {
    var request = new VenueClientAdmissionControl();
    request.setEnable24G(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_CLIENT_ADMISSION_CONTROL_SETTINGS,
        venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_CLIENT_ADMISSION_CONTROL_SETTINGS);
  }

  @Test
  void testUpdateVenueTemplateApClientAdmissionControlSettings(
      @VenueDecorator(isTemplate = true, bandBalancingEnabled = false) Venue venue) {
    var request = new VenueApClientAdmissionControlSettings();
    request.setEnable24G(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_CLIENT_ADMISSION_CONTROL_SETTINGS,
        venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_CLIENT_ADMISSION_CONTROL_SETTINGS);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateExternalAntenna(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new ArrayList<ApModelExternalAntenna>();
    var externalAntenna = new ApModelExternalAntenna();
    externalAntenna.setEnable24G(true);
    externalAntenna.setModel("E510");
    request.add(externalAntenna);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_EXTERNAL_ANTENNA, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_EXTERNAL_ANTENNA);
  }

  @Test
  void testUpdateVenueTemplateApModelExternalAntennaSettings(@Template Venue venue) {
    var request = new ArrayList<VenueApModelExternalAntennaSettings>();
    var externalAntenna = new VenueApModelExternalAntennaSettings();
    externalAntenna.setEnable24G(true);
    externalAntenna.setModel("E510");
    request.add(externalAntenna);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_MODEL_EXTERNAL_ANTENNA_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_EXTERNAL_ANTENNA);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateTripleBandRadioSettings(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new TripleBand();
    request.setEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_TRIPLE_BAND_RADIO_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_TRIPLE_BAND_RADIO_SETTINGS);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateTApModelCellular(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new VenueApModelCellular();
    var primarySimSettings = new SimSettings();
    var secondarySimSettings = new SimSettings();
    var lteBandLockChannel = new LteBandLockChannel();
    lteBandLockChannel.setBand3G(List.of());
    lteBandLockChannel.setBand4G(List.of());
    lteBandLockChannel.setRegion(LteBandRegionEnum.DOMAIN_1);
    primarySimSettings.setLteBands(List.of(lteBandLockChannel));
    primarySimSettings.setNetworkSelection(CellularNetworkSelectionEnum.AUTO);
    secondarySimSettings.setLteBands(List.of(lteBandLockChannel));
    secondarySimSettings.setNetworkSelection(CellularNetworkSelectionEnum.AUTO);
    request.setModel("M510");
    request.setPrimaryWanRecoveryTimer(30);
    request.setPrimarySim(primarySimSettings);
    request.setSecondarySim(secondarySimSettings);
    request.setWanConnection(WanConnectionEnum.CELLULAR);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_MODEL_CELLULAR, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_AP_MODEL_CELLULAR);
  }

  @Test
  void testUpdateVenueTemplateApCellularSettings(@Template Venue venue) {
    var request = new VenueApCellularSettings();
    var primarySimSettings = new SimSettings();
    var secondarySimSettings = new SimSettings();
    var lteBandLockChannel = new LteBandLockChannel();
    lteBandLockChannel.setBand3G(List.of());
    lteBandLockChannel.setBand4G(List.of());
    lteBandLockChannel.setRegion(LteBandRegionEnum.DOMAIN_1);
    primarySimSettings.setLteBands(List.of(lteBandLockChannel));
    primarySimSettings.setNetworkSelection(CellularNetworkSelectionEnum.AUTO);
    secondarySimSettings.setLteBands(List.of(lteBandLockChannel));
    secondarySimSettings.setNetworkSelection(CellularNetworkSelectionEnum.AUTO);
    request.setModel("M510");
    request.setPrimaryWanRecoveryTimer(30);
    request.setPrimarySim(primarySimSettings);
    request.setSecondarySim(secondarySimSettings);
    request.setWanConnection(WanConnectionEnum.CELLULAR);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_CELLULAR_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_AP_MODEL_CELLULAR);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateLanPorts(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new ArrayList<ApModelLanPort>();
    var apModelLanPort = new ApModelLanPort();
    var venueLanPort1 = new VenueLanPort();
    venueLanPort1.setEnabled(true);
    venueLanPort1.setPortId("1");
    venueLanPort1.setType(ApLanPortTypeEnum.TRUNK);
    venueLanPort1.setVlanMembers("1-4094");
    venueLanPort1.setUntagId((short) 1);
    var venueLanPort2 = new VenueLanPort();
    venueLanPort2.setEnabled(true);
    venueLanPort2.setPortId("2");
    venueLanPort2.setType(ApLanPortTypeEnum.ACCESS);
    venueLanPort2.setVlanMembers("2");
    venueLanPort2.setUntagId((short) 2);
    apModelLanPort.setModel("R710");
    apModelLanPort.setLanPorts(List.of(venueLanPort1, venueLanPort2));
    request.add(apModelLanPort);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_LAN_PORTS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_LAN_PORTS);
  }

  @Test
  void testUpdateVenueTemplateApModelLanPortSettings(@Template Venue venue) {
    var request = new ArrayList<VenueApModelLanPortSettings>();
    var venueApModelLanPortSettings = new VenueApModelLanPortSettings();
    var venueLanPort1 = new VenueLanPort();
    venueLanPort1.setEnabled(true);
    venueLanPort1.setPortId("1");
    venueLanPort1.setType(ApLanPortTypeEnum.TRUNK);
    venueLanPort1.setVlanMembers("1-4094");
    venueLanPort1.setUntagId((short) 1);
    var venueLanPort2 = new VenueLanPort();
    venueLanPort2.setEnabled(true);
    venueLanPort2.setPortId("2");
    venueLanPort2.setType(ApLanPortTypeEnum.ACCESS);
    venueLanPort2.setVlanMembers("2");
    venueLanPort2.setUntagId((short) 2);
    venueApModelLanPortSettings.setModel("R710");
    venueApModelLanPortSettings.setLanPorts(List.of(venueLanPort1, venueLanPort2));
    request.add(venueApModelLanPortSettings);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_MODEL_LAN_PORT_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_LAN_PORTS);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateMeshOptions(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new Mesh();
    request.setRadioType(MeshRadioTypeEnumV1_1._5_GHz);
    request.setEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_MESH_OPTIONS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_MESH_OPTIONS);
  }

  @Test
  void testUpdateVenueTemplateApMeshSettings(@Template Venue venue) {
    var request = new VenueApMeshSettings();
    request.setRadioType(MeshRadioTypeEnum._5_GHz);
    request.setEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_MESH_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_MESH_OPTIONS);
  }

  @Test
  void testUpdateVenueTemplateApMeshSettingsV1_1(@Template Venue venue) {
    var request = new VenueApMeshSettingsV1_1();
    request.setRadioType(MeshRadioTypeEnumV1_1._5_GHz);
    request.setEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_MESH_SETTINGS_V1_1, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_MESH_OPTIONS);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateDirectedMulticast(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new VenueDirectedMulticast();
    request.setNetworkEnabled(false);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_DIRECTED_MULTICAST, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_DIRECTED_MULTICAST);
  }

  @Test
  void testUpdateVenueTemplateApDirectedMulticastSettings(@Template Venue venue) {
    var request = new VenueApDirectedMulticastSettings();
    request.setNetworkEnabled(false);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_DIRECTED_MULTICAST_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_DIRECTED_MULTICAST);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateRadiusOptions(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new VenueRadiusOptions();
    request.setNasIdType(NasIdTypeEnum.BSSID);
    request.setNasIdDelimiter(NasIdDelimiterEnum.DASH);
    request.setCalledStationIdType(CalledStationIdTypeEnum.BSSID);
    request.setOverrideEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_RADIUS_OPTIONS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_RADIUS_OPTIONS);
  }

  @Test
  void testUpdateVenueTemplateApRadiusOptions(@Template Venue venue) {
    var request = new VenueApRadiusOptionSettings();
    request.setNasIdType(NasIdTypeEnum.BSSID);
    request.setNasIdDelimiter(NasIdDelimiterEnum.DASH);
    request.setCalledStationIdType(CalledStationIdTypeEnum.BSSID);
    request.setOverrideEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_RADIUS_OPTIONS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_RADIUS_OPTIONS);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateDenialOfServiceProtection(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new DenialOfServiceProtection();
    request.setEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_DENIAL_OF_SERVICE_PROTECTION,
        venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_DENIAL_OF_SERVICE_PROTECTION);
  }

  @Test
  void testUpdateVenueTemplateApDosProtectionSettings(@Template Venue venue) {
    var request = new VenueApDosProtectionSettings();
    request.setEnabled(true);

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_DOS_PROTECTION_SETTINGS,
        venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_DENIAL_OF_SERVICE_PROTECTION);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateLedOn(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new ArrayList<ApModelLed>();
    var apModelLed = new ApModelLed();
    request.add(apModelLed);
    apModelLed.setLedEnabled(true);
    apModelLed.setModel("R710");

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_LED_ON, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_LED_ON);
  }

  @Test
  void testUpdateVenueTemplateApModelLedSettings(@Template Venue venue) {
    var request = new ArrayList<VenueApModelLedSettings>();
    var apModelLed = new VenueApModelLedSettings();
    request.add(apModelLed);
    apModelLed.setLedEnabled(true);
    apModelLed.setModel("R710");

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_MODEL_LED_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_LED_ON);
  }

  @Deprecated(forRemoval = true)
  @Test
  void testUpdateVenueTemplateBonjourFencing(
      @VenueDecorator(isTemplate = true) Venue venue) {
    var request = new MdnsFencingGenerator().generate();

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_BONJOUR_FENCING, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_BONJOUR_FENCING);
  }

  @Test
  void testUpdateVenueTemplateApMulticastDnsFencingSettings(@Template Venue venue) {
    var request = new VenueApMulticastDnsFencingSettingsGenerator().generate();

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_AP_MULTICAST_DNS_FENCING_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_BONJOUR_FENCING);
  }

  @Test
  void testUpdateVenueTemplateSyslogServerProfile(
      @VenueDecorator(isTemplate = true) Venue venue, @Template final SyslogServerProfile syslogServerProfile) {
    var request = new VenueSyslogServerProfileGenerator()
        .setServiceProfileId(always(syslogServerProfile.getId()))
        .setEnabled(always(true)).generate();

    messageUtil.sendWifiCfgRequest(txCtxExtension.getTenantId(), txCtxExtension.getRequestId(),
        CfgAction.UPDATE_VENUE_TEMPLATE_SYSLOG_SERVER_PROFILE_SETTINGS, randomName(),
        new RequestParams().addPathVariable("venueTemplateId", venue.getId()), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_SYSLOG_SERVER_PROFILE_SETTINGS);
  }

  @Test
  void testUpdateVenueTemplateBandModeSettings(@Template Venue venue) {
    var request = new ArrayList<VenueApModelBandModeSettings>();
    var apBandMode = new VenueApModelBandModeSettings();
    request.add(apBandMode);
    apBandMode.setBandMode(BandModeEnum.DUAL);
    apBandMode.setModel("R710");

    sendRequest(CfgAction.UPDATE_VENUE_TEMPLATE_BAND_MODE_SETTINGS, venue.getId(), request);

    assertVenueTemplateApi(ApiFlowNames.UPDATE_VENUE_TEMPLATE_BAND_MODE_SETTINGS);
  }


  private void sendRequest(CfgAction cfgAction, String venueId, Object payload) {
    sendRequest(cfgAction, txCtxExtension.getRequestId(), venueId, payload);
  }

  private void sendRequest(CfgAction cfgAction, String requestId, String venueId, Object payload) {
    messageUtil.sendWifiCfgRequest(txCtxExtension.getTenantId(), requestId,
        cfgAction, randomName(),
        new RequestParams().addPathVariable("venueId", venueId), payload);
  }

  private void assertVenueTemplateApi(String stepId) {
    final String tenantId = txCtxExtension.getTenantId();
    final String requestId = txCtxExtension.getRequestId();

    assertActivitySuccess(requestId, stepId);
    messageCaptors.getActivityImpactDeviceMessageCaptor().assertNotSentByTenant(tenantId);

    assertThat(messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull();
  }

  private void assertActivitySuccess(String requestId, String stepId) {
    final String tenantId = txCtxExtension.getTenantId();
    assertThat(messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload).isNotNull()
        .satisfies(msg -> {
          assertThat(msg.getStatus()).isEqualTo(Status.OK);
          assertThat(msg.getStep()).isEqualTo(stepId);
        })
        .extracting(ConfigurationStatus::getEventDate).isNotNull();
  }
}
