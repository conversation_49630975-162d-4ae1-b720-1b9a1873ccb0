package com.ruckus.cloud.wifi.entitylistener.ddccm.builder;

import static com.ruckus.acx.ddccm.protobuf.wifi.L3ProtocolEnum.L3ProtocolEnum_CUSTOM;
import static com.ruckus.cloud.wifi.utils.TxChangesTestUtils.emptyTxChanges;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ruckus.acx.ddccm.protobuf.wifi.IpAclRules;
import com.ruckus.acx.ddccm.protobuf.wifi.IpModeEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.L3AccessControl;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.UTPAccessEnum;
import com.ruckus.acx.ddccm.protobuf.wifi.UTPDefaultActionEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.L3AclPolicy;
import com.ruckus.cloud.wifi.eda.servicemodel.L3IpPort;
import com.ruckus.cloud.wifi.eda.servicemodel.L3Rule;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.AccessEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.tx.entity.NewTxEntity;
import com.ruckus.cloud.wifi.test.WifiUnitTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;

@WifiUnitTest
class DdccmL3AclPolicyOperationBuilderTest {

  @SpyBean
  private DdccmL3AclPolicyOperationBuilder unit;

  @RegisterExtension
  public TxCtxExtension txCtxExtension = TxCtxExtension.withTenant("MyTenantId");

  @Test
  public void testAddL3AclPolicy() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasL3AccessControl)
        .allSatisfy(operation -> {
          validate(policy, operation.getL3AccessControl());
        });
  }

  @Test
  public void testAddL3AclPolicyWithAnyIp() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    L3Rule rule = policy.getL3Rules().get(0);
    L3IpPort source = new L3IpPort();
    source.setEnableIpSubnet(false);
    source.setPort("67");

    L3IpPort dest = new L3IpPort();
    dest.setEnableIpSubnet(false);
    source.setPort("67");

    rule.setSource(source);
    rule.setDestination(dest);

    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
      .hasSize(1)
      .allMatch(Operation::hasL3AccessControl)
      .allSatisfy(operation -> {
        L3AccessControl config = operation.getL3AccessControl();
        validate(policy, config);
        IpAclRules firstRule = config.getIpAclRules(0);
        assertEquals("", firstRule.getSourceIp().getValue());
        assertEquals("", firstRule.getDestinationIp().getValue());
        assertEquals(IpModeEnum.IpModeEnum_UNSET, firstRule.getIpMode());
      });
  }

  @Test
  public void testAddL3AclPolicyWithIPv4Source() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    L3Rule rule = policy.getL3Rules().get(0);
    L3IpPort source = new L3IpPort();
    source.setIp("***********");
    rule.setSource(source);
    
    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasL3AccessControl)
        .allSatisfy(operation -> {
          L3AccessControl config = operation.getL3AccessControl();
          validate(policy, config);
          IpAclRules firstRule = config.getIpAclRules(0);
          assertEquals("***********", firstRule.getSourceIp().getValue());
          assertEquals("***************", firstRule.getSourceIpMask().getValue());
          assertEquals(IpModeEnum.IPV4, firstRule.getIpMode());
        });
  }

  @Test
  public void testAddL3AclPolicyWithIPv6Source() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    L3Rule rule = policy.getL3Rules().get(0);
    L3IpPort source = new L3IpPort();
    source.setIp("2001:db8::1");
    rule.setSource(source);
    
    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasL3AccessControl)
        .allSatisfy(operation -> {
          L3AccessControl config = operation.getL3AccessControl();
          validate(policy, config);
          IpAclRules firstRule = config.getIpAclRules(0);
          assertEquals("2001:db8::1", firstRule.getSourceIp().getValue());
          assertEquals("/64", firstRule.getSourceIpMask().getValue());
          assertEquals(IpModeEnum.IPV6, firstRule.getIpMode());
        });
  }

  @Test
  public void testAddL3AclPolicyWithIPv4Destination() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    L3Rule rule = policy.getL3Rules().get(0);
    L3IpPort destination = new L3IpPort();
    destination.setIp("********");
    rule.setDestination(destination);
    
    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasL3AccessControl)
        .allSatisfy(operation -> {
          L3AccessControl config = operation.getL3AccessControl();
          validate(policy, config);
          IpAclRules firstRule = config.getIpAclRules(0);
          assertEquals("********", firstRule.getDestinationIp().getValue());
          assertEquals("***************", firstRule.getDestinationIpMask().getValue());
          assertEquals(IpModeEnum.IPV4, firstRule.getIpMode());
        });
  }

  @Test
  public void testAddL3AclPolicyWithIPv6Destination() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    L3Rule rule = policy.getL3Rules().get(0);
    L3IpPort destination = new L3IpPort();
    destination.setIp("2001:db8::abcd");
    rule.setDestination(destination);
    
    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasL3AccessControl)
        .allSatisfy(operation -> {
          L3AccessControl config = operation.getL3AccessControl();
          validate(policy, config);
          IpAclRules firstRule = config.getIpAclRules(0);
          assertEquals("2001:db8::abcd", firstRule.getDestinationIp().getValue());
          assertEquals("/64", firstRule.getDestinationIpMask().getValue());
          assertEquals(IpModeEnum.IPV6, firstRule.getIpMode());
        });
  }

  @Test
  public void testAddL3AclPolicyWithCustomProtocol() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    L3Rule rule = policy.getL3Rules().get(0);
    rule.setProtocol(com.ruckus.cloud.wifi.eda.servicemodel.enums.L3ProtocolEnum.L3ProtocolEnum_CUSTOM);
    rule.setCustomProtocol(123);
    
    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasL3AccessControl)
        .allSatisfy(operation -> {
          L3AccessControl config = operation.getL3AccessControl();
          validate(policy, config);
          IpAclRules firstRule = config.getIpAclRules(0);
          assertEquals(L3ProtocolEnum_CUSTOM, firstRule.getProtocol());
          assertEquals(123, firstRule.getCustomProtocol().getValue());
        });
  }

  @Test
  public void testAddL3AclPolicyWithPortRange() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    L3Rule rule = policy.getL3Rules().get(0);
    L3IpPort source = new L3IpPort();
    source.setPort("80-90");
    rule.setSource(source);
    L3IpPort destination = new L3IpPort();
    destination.setPort("443");
    rule.setDestination(destination);
    
    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasL3AccessControl)
        .allSatisfy(operation -> {
          L3AccessControl config = operation.getL3AccessControl();
          validate(policy, config);
          IpAclRules firstRule = config.getIpAclRules(0);
          assertEquals(80, firstRule.getSourceMinPort().getValue());
          assertEquals(90, firstRule.getSourceMaxPort().getValue());
          assertEquals(443, firstRule.getDestinationMinPort().getValue());
        });
  }

  @Test
  public void testAddL3AclPolicyWithBlockAction() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    policy.setDefaultAccess(AccessEnum.BLOCK);
    L3Rule rule = policy.getL3Rules().get(0);
    rule.setAccess(AccessEnum.BLOCK);
    
    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasL3AccessControl)
        .allSatisfy(operation -> {
          L3AccessControl config = operation.getL3AccessControl();
          validate(policy, config);
          assertEquals(UTPDefaultActionEnum.DefaultActionEnum_BLOCK, config.getDefaultAction());
          assertEquals(UTPAccessEnum.UTPAccessEnum_BLOCK, config.getIpAclRules(0).getAccess());
        });
  }

  @Test
  public void testAddL3AclPolicyWithDescription() {
    L3AclPolicy policy = Generators.l3AclPolicy().generate();
    L3Rule rule = policy.getL3Rules().get(0);
    rule.setDescription("Test rule description");
    
    List<Operation> operations = unit.build(new NewTxEntity<>(policy), emptyTxChanges());
    assertThat(operations)
        .hasSize(1)
        .allMatch(Operation::hasL3AccessControl)
        .allSatisfy(operation -> {
          L3AccessControl config = operation.getL3AccessControl();
          validate(policy, config);
          assertEquals("Test rule description", config.getIpAclRules(0).getDescription());
        });
  }

  private void validate(L3AclPolicy src, L3AccessControl config) {
    assertEquals(src.getId(), config.getId());
    assertEquals(src.getName(), config.getName());
    
    if (src.getDefaultAccess() == AccessEnum.ALLOW) {
      assertEquals(UTPDefaultActionEnum.DefaultActionEnum_ALLOW, config.getDefaultAction());
    } else {
      assertEquals(UTPDefaultActionEnum.DefaultActionEnum_BLOCK, config.getDefaultAction());
    }
    
    assertEquals(src.getL3Rules().size(), config.getIpAclRulesCount());
    
    for (int i = 0; i < src.getL3Rules().size(); i++) {
      L3Rule srcRule = src.getL3Rules().get(i);
      IpAclRules configRule = config.getIpAclRules(i);
      
      assertEquals(srcRule.getPriority(), configRule.getPriority());
      
      if (srcRule.getAccess() == AccessEnum.ALLOW) {
        assertEquals(UTPAccessEnum.UTPAccessEnum_ALLOW, configRule.getAccess());
      } else {
        assertEquals(UTPAccessEnum.UTPAccessEnum_BLOCK, configRule.getAccess());
      }
      
      if (srcRule.getDescription() != null) {
        assertEquals(srcRule.getDescription(), configRule.getDescription());
      }
      
      if (srcRule.getCustomProtocol() != null) {
        assertEquals(srcRule.getCustomProtocol(), configRule.getCustomProtocol().getValue());
      }
      
      // Validate source configuration
      if (srcRule.getSource() != null) {
        if (srcRule.getSource().getIp() != null) {
          assertEquals(srcRule.getSource().getIp(), configRule.getSourceIp().getValue());
        }
        if (srcRule.getSource().getIpMask() != null) {
          assertEquals(srcRule.getSource().getIpMask(), configRule.getSourceIpMask().getValue());
        }
        if (srcRule.getSource().getEnableIpSubnet() != null) {
          assertEquals(srcRule.getSource().getEnableIpSubnet(), configRule.getEnableSourceIpSubnet().getValue());
        }
        if (srcRule.getSource().getPort() != null) {
          String[] portRange = srcRule.getSource().getPort().split("-");
          assertEquals(Integer.valueOf(portRange[0]), configRule.getSourceMinPort().getValue());
          if (portRange.length == 2) {
            assertEquals(Integer.valueOf(portRange[1]), configRule.getSourceMaxPort().getValue());
          }
        }
      }
      
      // Validate destination configuration
      if (srcRule.getDestination() != null) {
        if (srcRule.getDestination().getIp() != null) {
          assertEquals(srcRule.getDestination().getIp(), configRule.getDestinationIp().getValue());
        }
        if (srcRule.getDestination().getIpMask() != null) {
          assertEquals(srcRule.getDestination().getIpMask(), configRule.getDestinationIpMask().getValue());
        }
        if (srcRule.getDestination().getEnableIpSubnet() != null) {
          assertEquals(srcRule.getDestination().getEnableIpSubnet(), configRule.getEnableDestinationIpSubnet().getValue());
        }
        if (srcRule.getDestination().getPort() != null) {
          String[] portRange = srcRule.getDestination().getPort().split("-");
          assertEquals(Integer.valueOf(portRange[0]), configRule.getDestinationMinPort().getValue());
          if (portRange.length == 2) {
            assertEquals(Integer.valueOf(portRange[1]), configRule.getDestinationMaxPort().getValue());
          }
        }
      }
    }
  }
}
