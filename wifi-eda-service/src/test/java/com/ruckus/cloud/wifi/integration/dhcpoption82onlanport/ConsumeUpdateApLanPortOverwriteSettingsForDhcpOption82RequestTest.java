package com.ruckus.cloud.wifi.integration.dhcpoption82onlanport;

import static com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper.ActivationConfig.of;
import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.test.kafka.KafkaMessageAssertions.assertHeader;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

import com.ruckus.acx.activity.protobuf.execution.impactdevice.ActivityImpactdevice.ImpactDevice;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus;
import com.ruckus.acx.activity.protobuf.execution.plan.CfgStatus.ConfigurationStatus.Status;
import com.ruckus.acx.ddccm.protobuf.common.Action;
import com.ruckus.acx.ddccm.protobuf.common.ServiceType;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile;
import com.ruckus.acx.ddccm.protobuf.wifi.ApLanPortProfile.CcmDhcpOption82Settings;
import com.ruckus.acx.ddccm.protobuf.wifi.DhcpOption82MacFormat;
import com.ruckus.acx.ddccm.protobuf.wifi.Operation;
import com.ruckus.acx.ddccm.protobuf.wifi.WifiConfigRequest;
import com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.datahelper.LanPortAdoptionDataHelper;
import com.ruckus.cloud.wifi.eda.mapper.CustomMapper;
import com.ruckus.cloud.wifi.eda.servicemodel.Ap;
import com.ruckus.cloud.wifi.eda.servicemodel.ApGroup;
import com.ruckus.cloud.wifi.eda.servicemodel.ApLanPort;
import com.ruckus.cloud.wifi.eda.servicemodel.DhcpOption82LanPortActivation;
import com.ruckus.cloud.wifi.eda.servicemodel.LanPortAdoption;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.ApLanPortSettingsV1Generator;
import com.ruckus.cloud.wifi.eda.test.valgen.viewmodel.Generators;
import com.ruckus.cloud.wifi.eda.viewmodel.ApLanPortSettingsV1;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.WifiCommonHeader;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption150Enum;
import com.ruckus.cloud.wifi.servicemodel.enums.DhcpOption82SubOption151Enum;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction;
import com.ruckus.cloud.wifi.test.extension.annotation.ApiAction.Payload;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.ApModel;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.kafka.KafkaTopicProvider;
import com.ruckus.cloud.wifi.test.kafka.captor.MessageCaptors;
import com.ruckus.cloud.wifi.test.util.KafkaProtoMessage;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("EthernetPortProfileTest")
@FeatureFlag(
    enable = {
      FlagNames.ACX_UI_ETHERNET_TOGGLE,
      FlagNames.WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE,
      FlagNames.WIFI_ETHERNET_DHCP_OPTION_82_TOGGLE,
    })
@WifiIntegrationTest
class ConsumeUpdateApLanPortOverwriteSettingsForDhcpOption82RequestTest {

  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private KafkaTopicProvider kafkaTopicProvider;
  @Autowired private MessageCaptors messageCaptors;
  @Autowired private LanPortAdoptionDataHelper dataHelper;

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Nested
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeEnableDhcpOption82OnApLanPortRequestTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator =
        Generators.apLanPortSettingsV1()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(Generators.dhcpOption82Settings())
            .setOverwriteUntagId(always((short) 1))
            .setOverwriteVlanMembers(always("1-4094"))
            .setEnabled(always(true));

    private String venueId;
    private String serialNumber;
    private String portId = "1";
    private String apLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue, final @ApModel("R320") Ap ap, final ApGroup apGroup) {
      var apLanPortData = dataHelper.createApLanPortDataWithAdoption(venue, ap, portId, 3);
      serialNumber = ap.getId();
      venueId = venue.getId();
      apLanPortId = apLanPortData.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      verifyResult(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS, apLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeEnableDhcpOption82OnApLanPortAndExistsDhcpOption82RequestTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator =
        Generators.apLanPortSettingsV1()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(Generators.dhcpOption82Settings())
            .setOverwriteUntagId(always((short) 1))
            .setOverwriteVlanMembers(always("1-4094"))
            .setEnabled(always(true));

    private String venueId;
    private String serialNumber;
    private String portId = "1";
    private String apLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue, final @ApModel("R320") Ap ap, final ApGroup apGroup) {
      DhcpOption82LanPortActivation dhcpOption82LanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .dhcpOption82LanPortActivation()
              .generate();
      var apLanPortData =
          dataHelper.createApLanPortDataWithAdoption(
              venue, ap, portId, 3, of(dhcpOption82LanPortActivation));
      serialNumber = ap.getId();
      venueId = venue.getId();
      apLanPortId = apLanPortData.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      verifyResult(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS, apLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeDisableDhcpOption82OnApLanPortRequestTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator =
        Generators.apLanPortSettingsV1()
            .setDhcpOption82Enabled(always(false))
            .setOverwriteUntagId(always((short) 1))
            .setOverwriteVlanMembers(always("1-4094"))
            .setEnabled(always(true));

    private String venueId;
    private String serialNumber;
    private String portId = "1";
    private String apLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue, final @ApModel("R320") Ap ap, final ApGroup apGroup) {
      DhcpOption82LanPortActivation dhcpOption82LanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .dhcpOption82LanPortActivation()
              .generate();
      var apLanPortData =
          dataHelper.createApLanPortDataWithAdoption(
              venue, ap, portId, 3, of(dhcpOption82LanPortActivation));
      serialNumber = ap.getId();
      venueId = venue.getId();
      apLanPortId = apLanPortData.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      verifyResult(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS, apLanPortId, payload);
    }
  }

  @Nested
  @ApiAction(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS)
  class ConsumeDisablePortOnApLanPortRequestTest {

    @Payload
    private final ApLanPortSettingsV1Generator generator =
        Generators.apLanPortSettingsV1()
            .setDhcpOption82Enabled(always(true))
            .setDhcpOption82Settings(Generators.dhcpOption82Settings())
            .setOverwriteUntagId(always((short) 1))
            .setOverwriteVlanMembers(always("1-4094"))
            .setEnabled(always(false));

    private String venueId;
    private String serialNumber;
    private String portId = "1";
    private String apLanPortId;

    @BeforeEach
    void givenOnePersistedInDb(
        final Venue venue, final @ApModel("R550") Ap ap, final ApGroup apGroup) {
      DhcpOption82LanPortActivation dhcpOption82LanPortActivation =
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators
              .dhcpOption82LanPortActivation()
              .generate();
      var apLanPortData =
          dataHelper.createApLanPortDataWithAdoption(
              venue, ap, portId, 3, of(dhcpOption82LanPortActivation));
      var apLanPortData2 =
          dataHelper.createApLanPortDataWithAdoption(
              venue, ap, "2", 4, of(dhcpOption82LanPortActivation));
      serialNumber = ap.getId();
      venueId = venue.getId();
      apLanPortId = apLanPortData.port().getId();
    }

    @ApiAction.RequestParams
    private RequestParams requestParams() {
      return new RequestParams()
          .addPathVariable("venueId", venueId)
          .addPathVariable("serialNumber", serialNumber)
          .addPathVariable("portId", portId);
    }

    @Test
    void thenShouldHandleTheRequestSuccessfully(@Payload ApLanPortSettingsV1 payload) {
      var expectedSettings = new ApLanPortSettingsV1();
      expectedSettings.setClientIsolationEnabled(false);
      verifyResult(CfgAction.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS, apLanPortId, expectedSettings);
    }
  }

  private void verifyResult(
      CfgAction apiAction, String apLanPortId, ApLanPortSettingsV1 expectedSettings) {
    verifyDb(apLanPortId, expectedSettings);
    verifyDdccm(apLanPortId, expectedSettings);
    verifyCmnConfig();
    verifyActivity(apiAction);
  }

  private void verifyDb(String apLanPortId, ApLanPortSettingsV1 expectedApLanPortSettingsV1) {
    assertThat(apLanPortId).isNotNull();
    final var port = repositoryUtil.find(ApLanPort.class, apLanPortId);

    if (expectedApLanPortSettingsV1.getDhcpOption82Enabled()) {
      var dhcpOption82Activation =
          assertThat(port)
              .extracting(ApLanPort::getLanPortAdoption)
              .extracting(LanPortAdoption::getDhcpOption82Activation)
              .isNotNull();
      var expectedSettings = expectedApLanPortSettingsV1.getDhcpOption82Settings();
      dhcpOption82Activation.matches(
          settings ->
              settings.getSubOption1Enabled().equals(expectedSettings.getSubOption1Enabled())
                  && settings.getSubOption2Enabled().equals(expectedSettings.getSubOption2Enabled())
                  && settings
                      .getSubOption150Enabled()
                      .equals(expectedSettings.getSubOption150Enabled())
                  && settings
                      .getSubOption151Enabled()
                      .equals(expectedSettings.getSubOption151Enabled())
                  && settings.getMacDelimiter()
                      == CustomMapper.INSTANCE.mapMacDelimiter(expectedSettings.getMacDelimiter())
                  && settings.getSubOption1Format()
                      == CustomMapper.INSTANCE.mapSubOption1Format(
                          expectedSettings.getSubOption1Format())
                  && settings
                      .getSubOption1Customization()
                      .equals(
                          CustomMapper.INSTANCE.dhcpOption82SubOption1CustomizationToJson(
                              expectedSettings.getSubOption1Customization()))
                  && settings.getSubOption2Format()
                      == CustomMapper.INSTANCE.mapSubOption2Format(
                          expectedSettings.getSubOption2Format())
                  && settings.getSubOption151Format()
                      == CustomMapper.INSTANCE.mapSubOption151Format(
                          expectedSettings.getSubOption151Format())
                  && settings.getSubOption151Text().equals(expectedSettings.getSubOption151Text()));

    } else {
      assertThat(port)
          .extracting(ApLanPort::getLanPortAdoption)
          .extracting(LanPortAdoption::getDhcpOption82Activation)
          .isNull();
    }
  }

  private void verifyDdccm(String apLanPortId, ApLanPortSettingsV1 expectedSettings) {
    final var requestId = txCtxExtension.getRequestId();
    final var tenantId = txCtxExtension.getTenantId();

    final var ddccmMessages = messageCaptors.getDdccmMessageCaptor().getValue(tenantId, requestId);
    assertThat(ddccmMessages)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    final var apLanPort = repositoryUtil.find(ApLanPort.class, apLanPortId);
    var ethernetPortProfileId = apLanPort.getLanPortAdoption().getEthernetPortProfileId();
    var operations =
        assertThat(ddccmMessages.getPayload())
            .extracting(WifiConfigRequest::getOperationsList)
            .asList()
            .isNotEmpty()
            .extracting(Operation.class::cast);
    operations
        .filteredOn(Operation::hasAp)
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(Operation::getAp)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.Ap::getModel)
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApModel::getLanPortList)
                    .asList()
                    .extracting(com.ruckus.acx.ddccm.protobuf.wifi.ApLanPort.class::cast)
                    .anyMatch(port -> ethernetPortProfileId.equals(port.getApLanPortProfileId())));
    operations
        .filteredOn(Operation::hasApLanPortProfile)
        .filteredOn(operation -> operation.getAction() == Action.ADD)
        .isNotEmpty()
        .allSatisfy(
            op ->
                assertThat(op)
                    .extracting(Operation::getCommonInfo)
                    .matches(commonInfo -> requestId.equals(commonInfo.getRequestId()))
                    .matches(commonInfo -> tenantId.equals(commonInfo.getTenantId()))
                    .matches(commonInfo -> commonInfo.getSender() == ServiceType.WIFI_SERVICE))
        .allSatisfy(op -> verifyAddOperation(op, apLanPort, expectedSettings));
    operations
        .filteredOn(operation -> operation.getAction() == Action.DELETE)
        .isNotEmpty()
        .allSatisfy(this::verifyDeleteOperation);
  }

  private void verifyAddOperation(
      Operation operation, ApLanPort apLanPort, ApLanPortSettingsV1 expectedSettings) {
    var profile = assertThat(operation).extracting(Operation::getApLanPortProfile);
    profile
        .matches(
            apLanPortProfile ->
                apLanPort.getLanPortAdoption().getId().equals(apLanPortProfile.getName()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getLanPortAdoption()
                    .getEthernetPortProfileId()
                    .equals(apLanPortProfile.getId()))
        .matches(
            apLanPortProfile ->
                (apLanPort.getApLanPortProfile().getType().name() + "PORT")
                    .equals(apLanPortProfile.getLanPortType().name()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagVlanId()))
        .matches(
            apLanPortProfile ->
                apLanPort
                    .getApLanPortProfile()
                    .getUntagId()
                    .equals((short) apLanPortProfile.getUntagId()));
    if (expectedSettings.getDhcpOption82Enabled()) {
      var dhcp = apLanPort.getLanPortAdoption().getDhcpOption82Activation();
      String dhcpOption82OptionFormat =
          "1,string,ETH:$IFNAME$:$IFNAME$:$VLAN$:$SSID$:$MODEL$:$HOSTNAME$:$DEVMAC$:123456789012345678901234;"
              + dhcp.getSubOption2Format().getValue()
              + DhcpOption82SubOption150Enum.SUBOPT150_VLAN_ID.getValue()
              + String.format(
                  DhcpOption82SubOption151Enum.SUBOPT151_AREA_NAME.getValue(), "151Input");

      profile
          .extracting(ApLanPortProfile::getDhcpOption82Settings)
          .isNotNull()
          .matches(ccmDhcpOption82Settings -> ccmDhcpOption82Settings.getDhcpOption82() == 1)
          .matches(
              ccmDhcpOption82Settings ->
                  dhcpOption82OptionFormat.equals(ccmDhcpOption82Settings.getDhcpOption82Format()))
          .matches(
              ccmDhcpOption82Settings ->
                  ccmDhcpOption82Settings
                      .getDhcpOption82MacFormat()
                      .equals(DhcpOption82MacFormat.MAC_NODELIMITER));

    } else {
      profile
          .matches(ApLanPortProfile::hasDhcpOption82Settings)
          .extracting(ApLanPortProfile::getDhcpOption82Settings)
          .matches(
              ccmDhcpOption82Settings ->
                  CcmDhcpOption82Settings.getDefaultInstance().equals(ccmDhcpOption82Settings));
    }
  }

  private void verifyDeleteOperation(Operation operation) {
    assertThat(operation).extracting(Operation::getApLanPortProfile).isNotNull();
  }

  private void verifyActivity(CfgAction apiAction) {
    if (apiAction == null) {
      messageCaptors
          .assertThat(
              kafkaTopicProvider.getActivityCfgChangeResp(),
              kafkaTopicProvider.getActivityImpacted())
          .doesNotSendByTenant(txCtxExtension.getTenantId());
      return;
    }

    final String requestId = txCtxExtension.getRequestId();
    final String tenantId = txCtxExtension.getTenantId();

    final var activityCfgChangeRespMessage =
        messageCaptors.getActivityCfgChangeMessageCaptor().getValue(tenantId, requestId);

    assertThat(activityCfgChangeRespMessage)
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull();

    assertThatNoException()
        .isThrownBy(
            () ->
                assertThat(activityCfgChangeRespMessage.getPayload())
                    .matches(msg -> msg.getStatus().equals(Status.OK))
                    .matches(msg -> msg.getStep().equals(apiFlowName(apiAction)))
                    .extracting(ConfigurationStatus::getEventDate)
                    .isNotNull());

    assertThat(messageCaptors.getActivityImpactDeviceMessageCaptor().getValue(tenantId, requestId))
        .isNotNull()
        .satisfies(assertHeader(WifiCommonHeader.REQUEST_ID, requestId))
        .satisfies(assertHeader(WifiCommonHeader.TENANT_ID, tenantId))
        .extracting(KafkaProtoMessage::getPayload)
        .isNotNull()
        .extracting(ImpactDevice::getDeviceIdsCount)
        .isEqualTo(1);
  }

  private void verifyCmnConfig() {
    final String tenantId = txCtxExtension.getTenantId();
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }

  private String apiFlowName(CfgAction apiAction) {
    return switch (apiAction) {
      case UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS ->
          ApiFlowNames.UPDATE_AP_LAN_PORT_OVERWRITE_SETTINGS;
      default -> throw new UnsupportedOperationException("Unsupported CfgAction: " + apiAction);
    };
  }
}
