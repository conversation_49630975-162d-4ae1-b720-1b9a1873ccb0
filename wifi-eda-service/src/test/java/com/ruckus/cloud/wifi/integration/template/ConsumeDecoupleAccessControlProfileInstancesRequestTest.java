package com.ruckus.cloud.wifi.integration.template;

import com.google.common.collect.Maps;
import com.ruckus.cloud.events.gpb.OpType;
import com.ruckus.cloud.events.gpb.Operations;
import com.ruckus.cloud.wifi.cfg.request.CfgAction;
import com.ruckus.cloud.wifi.eda.servicemodel.AccessControlProfile;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants;
import com.ruckus.cloud.wifi.entitylistener.cmncfgcollector.EsConstants.Index;
import com.ruckus.cloud.wifi.integration.AbstractRequestTest;
import com.ruckus.cloud.wifi.repository.AccessControlProfileRepository;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.test.WifiIntegrationTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.fixture.AccessControlProfileTestFixture;
import com.ruckus.cloud.wifi.test.fixture.TenantTestFixture;
import com.ruckus.cloud.wifi.test.util.RequestParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static com.ruckus.cloud.wifi.cfg.activity.ApiFlowNames.DECOUPLE_ACCESS_CONTROL_PROFILE_INSTANCES;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@WifiIntegrationTest
public class ConsumeDecoupleAccessControlProfileInstancesRequestTest extends AbstractRequestTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @Autowired
  private AccessControlProfileRepository accessControlProfileRepository;

  @BeforeEach
  void init() {
    clearMessageBeforeEachEdaOperation = true;
  }

  @Test
  public void decouple_accessControlProfileInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add AccessControlProfile as a template
    final var accessControlProfileTemplate = repositoryUtil.createOrUpdate(
        AccessControlProfileTestFixture.randomAccessControlProfile(mspTenant, null, null, p -> {
          p.setName("acp-template");
          p.setIsTemplate(true);
          p.setApplicationPolicyEnable(false);
          p.setDevicePolicyEnable(false);
        }), mspTenant.getId());

    // ec tenant add AccessControlProfile instance from template
    changeTxCtxTenant(ecTenantId);
    var accessControlProfileInstance = AccessControlProfileTestFixture.randomAccessControlProfile(ecTenant, null, null,
        p -> {
          p.setName("acp-instance");
          p.setTemplateId(accessControlProfileTemplate.getId());
          p.setIsTemplate(false);
          p.setIsEnforced(true);
          p.setApplicationPolicyEnable(false);
          p.setDevicePolicyEnable(false);
        });
    accessControlProfileInstance = repositoryUtil.createOrUpdate(accessControlProfileInstance, ecTenantId);

    // Verify initial state - instance should have templateId and isEnforced = true
    AccessControlProfile instanceBeforeDecouple = repositoryUtil.find(AccessControlProfile.class,
        accessControlProfileInstance.getId(), ecTenantId, false);
    assertAll("Verify AccessControlProfile instance before decoupling",
        () -> assertEquals(accessControlProfileTemplate.getId(), instanceBeforeDecouple.getTemplateId()),
        () -> assertFalse(instanceBeforeDecouple.getIsTemplate()),
        () -> assertTrue(instanceBeforeDecouple.getIsEnforced()));

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_ACCESS_CONTROL_PROFILE_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_ACCESS_CONTROL_PROFILE_INSTANCES, ecTenantId);

    // Verify AccessControlProfile instance was decoupled
    AccessControlProfile instanceAfterDecouple = repositoryUtil.find(AccessControlProfile.class,
        accessControlProfileInstance.getId(), ecTenantId, false);
    assertAll("Verify AccessControlProfile instance after decoupling",
        () -> assertNull(instanceAfterDecouple.getTemplateId(), "templateId should be null after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsEnforced(), "isEnforced should be false after decoupling"),
        () -> assertFalse(instanceAfterDecouple.getIsTemplate(), "isTemplate should remain false"));

    // Verify template is not affected
    changeTxCtxTenant(mspTenant.getId());
    AccessControlProfile templateAfterDecouple = repositoryUtil.find(AccessControlProfile.class,
        accessControlProfileTemplate.getId(), mspTenant.getId(), true);
    assertAll("Verify template is not affected",
        () -> assertTrue(templateAfterDecouple.getIsTemplate(), "Template should remain as template"),
        () -> assertEquals(accessControlProfileTemplate.getName(), templateAfterDecouple.getName(),
            "Template name should be unchanged"));

    assertDdccmCfgRequestNotSent(ecTenantId);
    validateCmnCfgCollectorMessage(ecTenantId, TxCtxHolder.txId(), instanceAfterDecouple.getId());
  }

  @Test
  public void decouple_multipleAccessControlProfileInstances(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // msp tenant add multiple AccessControlProfile templates
    final var template1 = repositoryUtil.createOrUpdate(
        AccessControlProfileTestFixture.randomAccessControlProfile(mspTenant, null, null, p -> {
          p.setName("acp-template-1");
          p.setIsTemplate(true);
          p.setApplicationPolicyEnable(false);
          p.setDevicePolicyEnable(false);
        }), mspTenant.getId());

    final var template2 = repositoryUtil.createOrUpdate(
        AccessControlProfileTestFixture.randomAccessControlProfile(mspTenant, null, null, p -> {
          p.setName("acp-template-2");
          p.setIsTemplate(true);
          p.setApplicationPolicyEnable(false);
          p.setDevicePolicyEnable(false);
        }), mspTenant.getId());

    // ec tenant add multiple AccessControlProfile instances from templates
    changeTxCtxTenant(ecTenantId);
    var instance1 = AccessControlProfileTestFixture.randomAccessControlProfile(ecTenant, null, null, p -> {
      p.setName("acp-instance-1");
      p.setTemplateId(template1.getId());
      p.setIsTemplate(false);
      p.setIsEnforced(true);
      p.setApplicationPolicyEnable(false);
      p.setDevicePolicyEnable(false);
    });
    instance1 = repositoryUtil.createOrUpdate(instance1, ecTenantId);

    var instance2 = AccessControlProfileTestFixture.randomAccessControlProfile(ecTenant, null, null, p -> {
      p.setName("acp-instance-2");
      p.setTemplateId(template2.getId());
      p.setIsTemplate(false);
      p.setIsEnforced(true);
      p.setApplicationPolicyEnable(false);
      p.setDevicePolicyEnable(false);
    });
    instance2 = repositoryUtil.createOrUpdate(instance2, ecTenantId);

    var instance3 = AccessControlProfileTestFixture.randomAccessControlProfile(ecTenant, null, null, p -> {
      p.setName("acp-instance-3");
      p.setTemplateId(template1.getId());
      p.setIsTemplate(false);
      p.setIsEnforced(true);
      p.setApplicationPolicyEnable(false);
      p.setDevicePolicyEnable(false);
    });
    instance3 = repositoryUtil.createOrUpdate(instance3, ecTenantId);

    // Execute decouple operation for all AccessControlProfiles
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_ACCESS_CONTROL_PROFILE_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_ACCESS_CONTROL_PROFILE_INSTANCES, ecTenantId);

    // Verify all AccessControlProfile instances were decoupled
    AccessControlProfile instance1AfterDecouple = repositoryUtil.find(AccessControlProfile.class,
        instance1.getId(), ecTenantId, false);
    AccessControlProfile instance2AfterDecouple = repositoryUtil.find(AccessControlProfile.class,
        instance2.getId(), ecTenantId, false);
    AccessControlProfile instance3AfterDecouple = repositoryUtil.find(AccessControlProfile.class,
        instance3.getId(), ecTenantId, false);

    assertAll("Verify all AccessControlProfile instances were decoupled",
        () -> assertNull(instance1AfterDecouple.getTemplateId(), "Instance 1 templateId should be null"),
        () -> assertFalse(instance1AfterDecouple.getIsEnforced(), "Instance 1 isEnforced should be false"),
        () -> assertNull(instance2AfterDecouple.getTemplateId(), "Instance 2 templateId should be null"),
        () -> assertFalse(instance2AfterDecouple.getIsEnforced(), "Instance 2 isEnforced should be false"),
        () -> assertNull(instance3AfterDecouple.getTemplateId(), "Instance 3 templateId should be null"),
        () -> assertFalse(instance3AfterDecouple.getIsEnforced(), "Instance 3 isEnforced should be false"));

    assertDdccmCfgRequestNotSent(ecTenantId);

    // Validate CmnCfgCollector message contains all three decoupled
    // AccessControlProfile instances
    String requestId = TxCtxHolder.txId();
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(ecTenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(p -> p.getTenantId().equals(ecTenantId))
        .matches(p -> p.getRequestId().equals(requestId));

    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .hasSize(3)
        .extracting(Operations::getId)
        .containsExactlyInAnyOrder(instance1.getId(), instance2.getId(), instance3.getId());

    // Verify each operation in the message
    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .allMatch(o -> Index.ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .allMatch(o -> o.getOpType().equals(OpType.MOD))
        .allMatch(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(ecTenantId))
        .allMatch(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue());
//        .allMatch(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue());  // Uncomment if isEnforced ready

    // Verify templates are not affected
    changeTxCtxTenant(mspTenant.getId());
    AccessControlProfile template1After = repositoryUtil.find(AccessControlProfile.class,
        template1.getId(), mspTenant.getId(), true);
    AccessControlProfile template2After = repositoryUtil.find(AccessControlProfile.class,
        template2.getId(), mspTenant.getId(), true);

    assertAll("Verify templates are not affected",
        () -> assertTrue(template1After.getIsTemplate(), "Template 1 should remain as template"),
        () -> assertTrue(template2After.getIsTemplate(), "Template 2 should remain as template"));
  }

  @Test
  public void decouple_noAccessControlProfileInstances_shouldSucceed(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant with no AccessControlProfile instances
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // Execute decouple operation on tenant with no AccessControlProfiles
    changeTxCtxTenant(ecTenantId);
    String requestId = randomTxId();
    sendWifiCfgRequest(ecTenantId, requestId, CfgAction.DECOUPLE_ACCESS_CONTROL_PROFILE_INSTANCES, userName,
        new RequestParams(), Maps.newHashMap(), "", false);

    assertActivityStatusSuccess(DECOUPLE_ACCESS_CONTROL_PROFILE_INSTANCES, ecTenantId);

    // Should not receive any viewmodel operations since no AccessControlProfiles
    // were affected
    assertViewmodelOpsNotSent(ecTenantId);
    assertDdccmCfgRequestNotSent(ecTenantId);
  }

  @Test
  public void decouple_accessControlProfileInstancesWithoutTemplateId_shouldSucceed(Tenant mspTenant) {
    var userName = txCtxExtension.getUserName();

    // create ec tenant
    Tenant ecTenant = TenantTestFixture.randomTenant((t) -> {
    });
    String ecTenantId = ecTenant.getId();
    repositoryUtil.createOrUpdate(ecTenant, mspTenant.getId());

    // ec tenant add AccessControlProfile instance without templateId (standalone
    // instance)
    changeTxCtxTenant(ecTenantId);
    var standaloneInstance = AccessControlProfileTestFixture.randomAccessControlProfile(ecTenant, null, null, p -> {
      p.setName("acp-standalone");
      p.setTemplateId(null); // No template association
      p.setIsTemplate(false);
      p.setIsEnforced(false);
      p.setApplicationPolicyEnable(false);
      p.setDevicePolicyEnable(false);
    });
    standaloneInstance = repositoryUtil.createOrUpdate(standaloneInstance, ecTenantId);

    // Execute decouple operation
    changeTxCtxTenant(ecTenantId);
    sendWifiCfgRequest(ecTenantId, randomTxId(), CfgAction.DECOUPLE_ACCESS_CONTROL_PROFILE_INSTANCES, userName, "");

    assertActivityStatusSuccess(DECOUPLE_ACCESS_CONTROL_PROFILE_INSTANCES, ecTenantId);

    // Verify standalone instance is not affected (no templateId to decouple)
    AccessControlProfile standaloneAfterDecouple = repositoryUtil.find(AccessControlProfile.class,
        standaloneInstance.getId(), ecTenantId, false);
    assertAll("Verify standalone instance is not affected",
        () -> assertNull(standaloneAfterDecouple.getTemplateId(), "templateId should remain null"),
        () -> assertFalse(standaloneAfterDecouple.getIsEnforced(), "isEnforced should remain false"),
        () -> assertFalse(standaloneAfterDecouple.getIsTemplate(), "isTemplate should remain false"));

    assertDdccmCfgRequestNotSent(ecTenantId);
    // No CmnCfgCollector message should be sent since no instances were actually
    // decoupled
    assertViewmodelOpsNotSent(ecTenantId);
  }

  private void assertViewmodelOpsNotSent(String tenantId) {
    messageCaptors.getCmnCfgCollectorMessageCaptor().assertNotSentByTenant(tenantId);
  }

  private void validateCmnCfgCollectorMessage(String tenantId, String requestId, String instanceId) {
    final var cmnCfgCollectorMessage = messageCaptors.getCmnCfgCollectorMessageCaptor().getValue(tenantId, requestId);
    assertThat(cmnCfgCollectorMessage.getPayload()).isNotNull();

    assertThat(cmnCfgCollectorMessage.getPayload())
        .matches(p -> p.getTenantId().equals(tenantId))
        .matches(p -> p.getRequestId().equals(requestId));

    assertThat(cmnCfgCollectorMessage.getPayload().getOperationsList())
        .hasSize(1)
        .filteredOn(o -> o.getId().equals(instanceId)).first()
        .matches(o -> EsConstants.Index.ACCESS_CONTROL_POLICY_PROFILE_INDEX_NAME.equals(o.getIndex()))
        .matches(o -> o.getOpType().equals(OpType.MOD))
        .matches(o -> o.getDocMap().get(EsConstants.Key.TENANT_ID).getStringValue().equals(tenantId))
        .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_TEMPLATE).getBoolValue());
//        .matches(o -> !o.getDocMap().get(EsConstants.Key.IS_ENFORCED).getBoolValue()) // Uncomment if isEnforced ready
  }
}
