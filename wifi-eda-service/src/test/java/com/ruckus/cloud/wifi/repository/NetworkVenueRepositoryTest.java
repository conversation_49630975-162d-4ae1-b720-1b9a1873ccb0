package com.ruckus.cloud.wifi.repository;

import static com.ruckus.cloud.wifi.repository.NetworkVenueRepository.formattedNetworkIdAtVenueId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatRuntimeException;
import static org.assertj.core.groups.Tuple.tuple;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.eda.servicemodel.AbstractBaseEntity;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.NetworkVenue;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.servicemodel.projection.NetworkVenueTenantIdProjection;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.ApplyTemplateFilter;
import com.ruckus.cloud.wifi.utils.TxCtxUtils;
import java.util.List;
import org.assertj.core.api.Condition;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO tenant (id) VALUES ('1373fcf2d15911eea5060242ac120002'); -- MSP For Template
    INSERT INTO tenant (id) VALUES ('e359afa2d15911eea5060242ac120002');-- EC1 For Instance
    INSERT INTO tenant (id) VALUES ('64372ed4084a11ef92620242ac120002');-- EC2 For Instance
    -- For with ClientIsolationAllowlist
    INSERT INTO venue (id, tenant) VALUES (
        '78c2e1ccebd745be839198cddf686a86',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant, is_template) VALUES (
        '2160b65cd15911eea5060242ac120002',
        '1373fcf2d15911eea5060242ac120002', true);
    INSERT INTO venue (id, tenant, is_template) VALUES (
        'f5b185cd81db4be89a6686fb339dc40b',
        '1373fcf2d15911eea5060242ac120002', true);
    INSERT INTO venue (id, tenant, template_id) VALUES (
        'de6d4a62d15911eea5060242ac120002',
        'e359afa2d15911eea5060242ac120002',
        '2160b65cd15911eea5060242ac120002');
    INSERT INTO venue (id, tenant, template_id) VALUES (
        '3cff2cd5e78047e2a74a3216ae0983d0',
        '64372ed4084a11ef92620242ac120002',
        '2160b65cd15911eea5060242ac120002');
    INSERT INTO network (id, name, type, tenant) VALUES (
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        'Open Network',
        'OPEN',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant, is_template) VALUES (
        '420d50d6d15911eea5060242ac120002',
        'Open Network Template in MSP',
        'OPEN',
        '1373fcf2d15911eea5060242ac120002', true);
    INSERT INTO network (id, name, type, tenant, template_id) VALUES (
        '07e5f042d15a11eea5060242ac120002',
        'Open Network Instance in EC1',
        'OPEN',
        'e359afa2d15911eea5060242ac120002',
        '420d50d6d15911eea5060242ac120002');
    INSERT INTO network (id, name, type, tenant, template_id) VALUES (
        '03934a27f5d549cc97087c46a277946f',
        'Open Network Instance in EC2',
        'OPEN',
        '64372ed4084a11ef92620242ac120002',
        '420d50d6d15911eea5060242ac120002');
    INSERT INTO client_isolation_allowlist (id, venue, tenant) VALUES (
        '38810fd6faa84f18b7c5c8b9656bdfb1',
        '78c2e1ccebd745be839198cddf686a86',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network_venue (id, client_isolation_allowlist, network, venue, tenant) VALUES (
        'd20a29ee20eb4ce5b4eb507cb35fa972',
        '38810fd6faa84f18b7c5c8b9656bdfb1',
        'c9845a491cbc43d596ffcf3b5fca8c4f',
        '78c2e1ccebd745be839198cddf686a86',
        '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network_venue (id, network, venue, tenant, is_template) VALUES (
        '5a97e10cd15911eea5060242ac120002',
        '420d50d6d15911eea5060242ac120002',
        '2160b65cd15911eea5060242ac120002',
        '1373fcf2d15911eea5060242ac120002', true);
    INSERT INTO network_venue (id, network, venue, tenant, is_template) VALUES (
        'ce3421a31b064474b7ec6bcc5093fcd1',
        '420d50d6d15911eea5060242ac120002',
        'f5b185cd81db4be89a6686fb339dc40b',
        '1373fcf2d15911eea5060242ac120002', true);
    INSERT INTO network_venue (id, network, venue, tenant, template_id) VALUES (
        '3173cb28d15a11eea5060242ac120002',
        '07e5f042d15a11eea5060242ac120002',
        'de6d4a62d15911eea5060242ac120002',
        'e359afa2d15911eea5060242ac120002',
        '5a97e10cd15911eea5060242ac120002');
    INSERT INTO network_venue (id, network, venue, tenant, template_id) VALUES (
        '6e95c73c077e463eb66aecec877f03e2',
        '03934a27f5d549cc97087c46a277946f',
        '3cff2cd5e78047e2a74a3216ae0983d0',
        '64372ed4084a11ef92620242ac120002',
        '5a97e10cd15911eea5060242ac120002');
    -- For without ClientIsolationAllowlist
    INSERT INTO venue (id, tenant) VALUES
        ('03756bba1328459e9e289f98d4319d54', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES
        ('77cef26623cc4330ba4d0acef5cffcf4', 'Network-1', 'OPEN', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('da0f296b61d94b899c782e969f432be4', 'Network-2', 'OPEN', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('efd0e13cc150444ca1956dd68c8999f2', 'Network-3', 'OPEN', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network_venue (id, network, venue, tenant) VALUES
        ('2251caaaef5e4cf4a9e6165c2ff26662', '77cef26623cc4330ba4d0acef5cffcf4',
          '03756bba1328459e9e289f98d4319d54', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('41ec5326dc8d4ba39951d178b6760454', '77cef26623cc4330ba4d0acef5cffcf4',
          'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('d5b35a93d8e54a3a843c98d3e1dddde9', 'da0f296b61d94b899c782e969f432be4',
          'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('eb5db59075374089a5fa4415a474983e', 'efd0e13cc150444ca1956dd68c8999f2',
          'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    -- For Hotspot 20 
    INSERT INTO venue (id, tenant) VALUES
        ('06f00c816aed4fc09acdd3ddf59ad6b6', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network (id, name, type, tenant) VALUES
        ('e5123f27d8c549d98b4d121e400f9cff', 'Hotspot 20 Network', 'HOTSPOT20', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('7ec535276d4643bb8108e326c7f1c45a', 'AAA Network', 'AAA', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO network_venue (id, network, venue, tenant) VALUES
        ('67f31a3948d848d08e1c8dbb0a8bbc6f', 'e5123f27d8c549d98b4d121e400f9cff',
          '06f00c816aed4fc09acdd3ddf59ad6b6', '4c8279f79307415fa9e4c88a1819f0fc'),
        ('b4fec1b0b3ec455499834cc3d4652eb3', '7ec535276d4643bb8108e326c7f1c45a',
          '06f00c816aed4fc09acdd3ddf59ad6b6', '4c8279f79307415fa9e4c88a1819f0fc');
    ---
    INSERT INTO venue (id, tenant) VALUES
        ('74c86704084a11ef92620242ac120002', '64372ed4084a11ef92620242ac120002'),
        ('a5350316084a11ef92620242ac120002', '64372ed4084a11ef92620242ac120002'),
        ('2c99f028084b11ef92620242ac120002', '64372ed4084a11ef92620242ac120002');
    INSERT INTO network (id, name, type, tenant) VALUES 
        ('d947fd84084a11ef92620242ac120002',
         'Open Network in EC2',
         'OPEN',
         '64372ed4084a11ef92620242ac120002');
    INSERT INTO network_venue (id, network, venue, tenant, is_all_ap_groups) VALUES
        ('0a6c801a084b11ef92620242ac120002', 'd947fd84084a11ef92620242ac120002',
          '74c86704084a11ef92620242ac120002', '64372ed4084a11ef92620242ac120002', true),
        ('1e5a0d5e084b11ef92620242ac120002', 'd947fd84084a11ef92620242ac120002',
          'a5350316084a11ef92620242ac120002', '64372ed4084a11ef92620242ac120002', false),
        ('e2784c14084b11ef92620242ac120002', 'd947fd84084a11ef92620242ac120002',
          '2c99f028084b11ef92620242ac120002', '64372ed4084a11ef92620242ac120002', null);
    """)
class NetworkVenueRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";
  private static final String MSP_TENANT_ID = "1373fcf2d15911eea5060242ac120002";
  private static final String EC_TENANT_ID_1 = "e359afa2d15911eea5060242ac120002";
  private static final String EC_TENANT_ID_2 = "64372ed4084a11ef92620242ac120002";
  private static final String VENUE_ID_1 = "03756bba1328459e9e289f98d4319d54";
  private static final String VENUE_ID_2 = "e465bac6afb747a4987d0d0945f77221";
  private static final String VENUE_TEMPLATE_ID_1 = "2160b65cd15911eea5060242ac120002";
  private static final String VENUE_TEMPLATE_ID_2 = "f5b185cd81db4be89a6686fb339dc40b";
  private static final String NETWORK_ID_1 = "77cef26623cc4330ba4d0acef5cffcf4";
  private static final String NETWORK_ID_2 = "da0f296b61d94b899c782e969f432be4";
  private static final String NETWORK_ID_3 = "efd0e13cc150444ca1956dd68c8999f2";
  private static final String NETWORK_ID_4 = "c9845a491cbc43d596ffcf3b5fca8c4f";
  private static final String NETWORK_TEMPLATE_ID = "420d50d6d15911eea5060242ac120002";
  private static final String VENUE_ID_WITH_CLIENT_ISOLATION_ALLOWLIST = "78c2e1ccebd745be839198cddf686a86";
  private static final String VENUE_ID_WITHOUT_CLIENT_ISOLATION_ALLOWLIST = "e465bac6afb747a4987d0d0945f77221";

  @Autowired
  private NetworkVenueRepository target;

  @Test
  void existsByVenueIdAndClientIsolationAllowlistNotNullTest() {
    var result = target.existsByVenueIdAndTenantIdAndClientIsolationAllowlistNotNull(
        VENUE_ID_WITH_CLIENT_ISOLATION_ALLOWLIST, TENANT_ID);
    assertTrue(result);

    result = target.existsByVenueIdAndTenantIdAndClientIsolationAllowlistNotNull(
        VENUE_ID_WITHOUT_CLIENT_ISOLATION_ALLOWLIST, TENANT_ID);
    assertFalse(result);
  }

  @Test
  void findByTenantIdAndNetworkIdAtVenueIdInTest() {
    assertThat(target.findByTenantIdAndNetworkIdAtVenueIdIn(TENANT_ID,
        List.of(
            formattedNetworkIdAtVenueId(NETWORK_ID_1, VENUE_ID_2),
            formattedNetworkIdAtVenueId(NETWORK_ID_2, VENUE_ID_1), // NOT EXIST
            formattedNetworkIdAtVenueId(NETWORK_ID_3, VENUE_ID_2))))
        .isNotNull().isNotEmpty()
        .hasSize(2)
        .extracting(nv -> nv.getNetwork().getId(), nv -> nv.getVenue().getId())
        .containsExactlyInAnyOrder(
            tuple(NETWORK_ID_1, VENUE_ID_2),
            tuple(NETWORK_ID_3, VENUE_ID_2));
  }

  @Test
  void findByTenantIdAndNetworkIdAtVenueIdInOrNetworkIdInOrVenueIdInTest() {
    var pageable = PageRequest.ofSize(10);
    var result = target.findByTenantIdAndNetworkIdAtVenueIdInOrNetworkIdInOrVenueIdIn(TENANT_ID,
        List.of("c9845a491cbc43d596ffcf3b5fca8c4f@78c2e1ccebd745be839198cddf686a86",
            "efd0e13cc150444ca1956dd68c8999f2@e465bac6afb747a4987d0d0945f77221"),
        List.of("77cef26623cc4330ba4d0acef5cffcf4"),
        List.of("03756bba1328459e9e289f98d4319d54"), pageable);
    assertThat(result)
        .isNotNull().isNotEmpty()
        .hasSize(4)
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "c9845a491cbc43d596ffcf3b5fca8c4f".equals(nv.getNetwork().getId())
                && "78c2e1ccebd745be839198cddf686a86".equals(nv.getVenue().getId()),
            "networkId should be c9845a491cbc43d596ffcf3b5fca8c4f && venueId should be 78c2e1ccebd745be839198cddf686a86"))
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "efd0e13cc150444ca1956dd68c8999f2".equals(nv.getNetwork().getId())
                && "e465bac6afb747a4987d0d0945f77221".equals(nv.getVenue().getId()),
            "networkId should be efd0e13cc150444ca1956dd68c8999f2 && venueId should be e465bac6afb747a4987d0d0945f77221"))
        .areExactly(2, new Condition<>((NetworkVenue nv) ->
            "77cef26623cc4330ba4d0acef5cffcf4".equals(nv.getNetwork().getId()),
            "networkId should be c9845a491cbc43d596ffcf3b5fca8c4f"))
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "03756bba1328459e9e289f98d4319d54".equals(nv.getVenue().getId()),
            "venueId should be 03756bba1328459e9e289f98d4319d54"));

    result = target.findByTenantIdAndNetworkIdAtVenueIdInOrNetworkIdInOrVenueIdIn(TENANT_ID,
        List.of("c9845a491cbc43d596ffcf3b5fca8c4f@78c2e1ccebd745be839198cddf686a86",
            "efd0e13cc150444ca1956dd68c8999f2@e465bac6afb747a4987d0d0945f77221"),
        List.of("77cef26623cc4330ba4d0acef5cffcf4"), null, pageable);
    assertThat(result)
        .isNotNull().isNotEmpty()
        .hasSize(4)
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "c9845a491cbc43d596ffcf3b5fca8c4f".equals(nv.getNetwork().getId())
                && "78c2e1ccebd745be839198cddf686a86".equals(nv.getVenue().getId()),
            "networkId should be c9845a491cbc43d596ffcf3b5fca8c4f && venueId should be 78c2e1ccebd745be839198cddf686a86"))
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "efd0e13cc150444ca1956dd68c8999f2".equals(nv.getNetwork().getId())
                && "e465bac6afb747a4987d0d0945f77221".equals(nv.getVenue().getId()),
            "networkId should be efd0e13cc150444ca1956dd68c8999f2 && venueId should be e465bac6afb747a4987d0d0945f77221"))
        .areExactly(2, new Condition<>((NetworkVenue nv) ->
            "77cef26623cc4330ba4d0acef5cffcf4".equals(nv.getNetwork().getId()),
            "networkId should be c9845a491cbc43d596ffcf3b5fca8c4f"));

    result = target.findByTenantIdAndNetworkIdAtVenueIdInOrNetworkIdInOrVenueIdIn(TENANT_ID,
        List.of("c9845a491cbc43d596ffcf3b5fca8c4f@78c2e1ccebd745be839198cddf686a86",
            "efd0e13cc150444ca1956dd68c8999f2@e465bac6afb747a4987d0d0945f77221"),
        null, List.of("03756bba1328459e9e289f98d4319d54"), pageable);
    assertThat(result)
        .isNotNull().isNotEmpty()
        .hasSize(3)
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "c9845a491cbc43d596ffcf3b5fca8c4f".equals(nv.getNetwork().getId())
                && "78c2e1ccebd745be839198cddf686a86".equals(nv.getVenue().getId()),
            "networkId should be c9845a491cbc43d596ffcf3b5fca8c4f && venueId should be 78c2e1ccebd745be839198cddf686a86"))
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "efd0e13cc150444ca1956dd68c8999f2".equals(nv.getNetwork().getId())
                && "e465bac6afb747a4987d0d0945f77221".equals(nv.getVenue().getId()),
            "networkId should be efd0e13cc150444ca1956dd68c8999f2 && venueId should be e465bac6afb747a4987d0d0945f77221"))
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "03756bba1328459e9e289f98d4319d54".equals(nv.getVenue().getId()),
            "venueId should be 03756bba1328459e9e289f98d4319d54"));

    result = target.findByTenantIdAndNetworkIdAtVenueIdInOrNetworkIdInOrVenueIdIn(TENANT_ID,
        List.of("c9845a491cbc43d596ffcf3b5fca8c4f@78c2e1ccebd745be839198cddf686a86",
            "efd0e13cc150444ca1956dd68c8999f2@e465bac6afb747a4987d0d0945f77221"),
        null, null, pageable);
    assertThat(result)
        .isNotNull().isNotEmpty()
        .hasSize(2)
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "c9845a491cbc43d596ffcf3b5fca8c4f".equals(nv.getNetwork().getId())
                && "78c2e1ccebd745be839198cddf686a86".equals(nv.getVenue().getId()),
            "networkId should be c9845a491cbc43d596ffcf3b5fca8c4f && venueId should be 78c2e1ccebd745be839198cddf686a86"))
        .areExactly(1, new Condition<>((NetworkVenue nv) ->
            "efd0e13cc150444ca1956dd68c8999f2".equals(nv.getNetwork().getId())
                && "e465bac6afb747a4987d0d0945f77221".equals(nv.getVenue().getId()),
            "networkId should be efd0e13cc150444ca1956dd68c8999f2 && venueId should be e465bac6afb747a4987d0d0945f77221"));

    pageable = PageRequest.of(0, 1, Direction.ASC, "network.id");
    result = target.findByTenantIdAndNetworkIdAtVenueIdInOrNetworkIdInOrVenueIdIn(TENANT_ID,
        List.of("c9845a491cbc43d596ffcf3b5fca8c4f@78c2e1ccebd745be839198cddf686a86",
            "efd0e13cc150444ca1956dd68c8999f2@e465bac6afb747a4987d0d0945f77221"),
        null, null, pageable);
    assertThat(result)
        .isNotNull().isNotEmpty()
        .hasSize(1)
        .singleElement()
        .satisfies(nv -> {
          assertThat(nv.getNetwork().getId())
              .isEqualTo("c9845a491cbc43d596ffcf3b5fca8c4f");
          assertThat(nv.getVenue().getId())
              .isEqualTo("78c2e1ccebd745be839198cddf686a86");
        });

    pageable = pageable.next();
    result = target.findByTenantIdAndNetworkIdAtVenueIdInOrNetworkIdInOrVenueIdIn(TENANT_ID,
        List.of("c9845a491cbc43d596ffcf3b5fca8c4f@78c2e1ccebd745be839198cddf686a86",
            "efd0e13cc150444ca1956dd68c8999f2@e465bac6afb747a4987d0d0945f77221"),
        null, null, pageable);
    assertThat(result)
        .isNotNull().isNotEmpty()
        .hasSize(1)
        .singleElement()
        .satisfies(nv -> {
          assertThat(nv.getNetwork().getId())
              .isEqualTo("efd0e13cc150444ca1956dd68c8999f2");
          assertThat(nv.getVenue().getId())
              .isEqualTo("e465bac6afb747a4987d0d0945f77221");
        });

    pageable = pageable.next();
    result = target.findByTenantIdAndNetworkIdAtVenueIdInOrNetworkIdInOrVenueIdIn(TENANT_ID,
        List.of("c9845a491cbc43d596ffcf3b5fca8c4f@78c2e1ccebd745be839198cddf686a86",
            "efd0e13cc150444ca1956dd68c8999f2@e465bac6afb747a4987d0d0945f77221"),
        null, null, pageable);
    assertThat(result)
        .isNotNull().isEmpty();
  }

  @Test
  void findNetworkVenueTenantIdProjectionsByNetworkTemplateIdAtVenueTemplateIdInTest() {
    assertThatRuntimeException().isThrownBy(
            () -> target.findNetworkVenueTenantIdProjectionsByNetworkTemplateIdAtVenueTemplateIdIn(
                List.of(formattedNetworkIdAtVenueId(NETWORK_TEMPLATE_ID, VENUE_TEMPLATE_ID_1))))
        .withRootCauseExactlyInstanceOf(IllegalAccessException.class);

    TxCtxUtils.allowCrossingTenantQuery(NetworkVenue.class);

    assertThat(target.findNetworkVenueTenantIdProjectionsByNetworkTemplateIdAtVenueTemplateIdIn(
        List.of(formattedNetworkIdAtVenueId(NETWORK_TEMPLATE_ID, VENUE_TEMPLATE_ID_1))))
        .isNotNull().isNotEmpty()
        .hasSize(2)
        .extracting(NetworkVenueTenantIdProjection::tenantId,
            NetworkVenueTenantIdProjection::networkVenueId,
            NetworkVenueTenantIdProjection::networkId,
            NetworkVenueTenantIdProjection::venueId)
        .containsExactlyInAnyOrder(
            tuple(EC_TENANT_ID_1, "3173cb28d15a11eea5060242ac120002",
                "07e5f042d15a11eea5060242ac120002", "de6d4a62d15911eea5060242ac120002"),
            tuple(EC_TENANT_ID_2, "6e95c73c077e463eb66aecec877f03e2",
                "03934a27f5d549cc97087c46a277946f", "3cff2cd5e78047e2a74a3216ae0983d0"));
  }

  @Test
  void findAllDistinctTenantIdsTest() {
    var result = target.findAllDistinctTenantIds();
    assertFalse(result.isEmpty());
    assertTrue(result.stream().anyMatch("4c8279f79307415fa9e4c88a1819f0fc"::equals));
  }

  @Test
  void findByTenantIdAndVenueIdAndHotspot20NetworkTest() {
    var result = target.findByTenantIdAndVenueIdAndHotspot20Network(TENANT_ID, "06f00c816aed4fc09acdd3ddf59ad6b6");
    assertEquals(1, result.size());

    var result2 = target.findByTenantIdAndVenueIdAndHotspot20Network(TENANT_ID, "4c8279f79307415fa9e4c88a1819f0fc");
    assertTrue(result2.isEmpty());
  }

  @Test
  void testFindByTemplateIdAndTenantId() {
    var result = target.findByTemplateIdAndTenantId("5a97e10cd15911eea5060242ac120002",
        "e359afa2d15911eea5060242ac120002");
    assertThat(result).isNotEmpty();
  }

  @ApplyTemplateFilter
  @Test
  void testFindByVenueIdAndTenantIdAndIsTemplate() {
    var result = target.findByVenueIdAndTenantIdAndIsTemplate("2160b65cd15911eea5060242ac120002",
        "1373fcf2d15911eea5060242ac120002");
    assertThat(result)
        .hasSize(1)
        .singleElement()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo("5a97e10cd15911eea5060242ac120002");
  }

  @ApplyTemplateFilter
  @Test
  void testFindByNetworkIdAndVenueIdAndTenantIdAndIsTemplate() {
    var result = target.findByNetworkIdAndVenueIdAndTenantIdAndIsTemplate("420d50d6d15911eea5060242ac120002",
        "2160b65cd15911eea5060242ac120002", "1373fcf2d15911eea5060242ac120002");
    assertThat(result)
        .isNotEmpty()
        .get()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo("5a97e10cd15911eea5060242ac120002");
  }

  @ApplyTemplateFilter
  @Test
  void testFindByNetworkIdAndVenueIdAndIsTemplate() {
    assertThatRuntimeException().isThrownBy(
            () -> target.findByNetworkIdAndVenueIdAndIsTemplate(NETWORK_TEMPLATE_ID,
                VENUE_TEMPLATE_ID_1))
        .withRootCauseExactlyInstanceOf(IllegalAccessException.class);

    TxCtxUtils.allowCrossingTenantQuery(NetworkVenue.class);

    assertThat(
        target.findByNetworkIdAndVenueIdAndIsTemplate(NETWORK_TEMPLATE_ID, VENUE_TEMPLATE_ID_1))
        .isPresent()
        .map(AbstractBaseEntity::getId)
        .hasValue("5a97e10cd15911eea5060242ac120002");
  }

  @ApplyTemplateFilter
  @Test
  void testFindByNetworkIdAndVenueIdInAndIsTemplate() {
    assertThatRuntimeException().isThrownBy(
            () -> target.findByNetworkIdAndVenueIdInAndIsTemplate(NETWORK_TEMPLATE_ID,
                List.of(VENUE_TEMPLATE_ID_1, VENUE_TEMPLATE_ID_2)))
        .withRootCauseExactlyInstanceOf(IllegalAccessException.class);

    TxCtxUtils.allowCrossingTenantQuery(NetworkVenue.class);

    assertThat(target.findByNetworkIdAndVenueIdInAndIsTemplate(NETWORK_TEMPLATE_ID,
        List.of(VENUE_TEMPLATE_ID_1, VENUE_TEMPLATE_ID_2)))
        .isNotEmpty()
        .extracting(AbstractBaseEntity::getId)
        .containsExactlyInAnyOrder("5a97e10cd15911eea5060242ac120002", "ce3421a31b064474b7ec6bcc5093fcd1");
  }

  @ApplyTemplateFilter
  @Test
  void testFindByIdAndTenantIdAndIsTemplate() {
    var result = target.findByIdAndTenantIdAndIsTemplate("5a97e10cd15911eea5060242ac120002",
        "1373fcf2d15911eea5060242ac120002");
    assertThat(result)
        .isNotEmpty()
        .get()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo("5a97e10cd15911eea5060242ac120002");
  }

  @Test
  void testFindByVenueIdAndTenantIdAndIsAllApGroups() {
    assertThat(target.findByVenueIdAndTenantIdAndIsAllApGroups("74c86704084a11ef92620242ac120002",
        "64372ed4084a11ef92620242ac120002"))
        .hasSize(1)
        .singleElement()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo("0a6c801a084b11ef92620242ac120002");

    assertThat(target.findByVenueIdAndTenantIdAndIsAllApGroups("a5350316084a11ef92620242ac120002",
        "64372ed4084a11ef92620242ac120002"))
        .isEmpty();

    assertThat(target.findByVenueIdAndTenantIdAndIsAllApGroups("2c99f028084b11ef92620242ac120002",
        "64372ed4084a11ef92620242ac120002"))
        .hasSize(1)
        .singleElement()
        .extracting(AbstractBaseEntity::getId)
        .isEqualTo("e2784c14084b11ef92620242ac120002");
  }

  @ApplyTemplateFilter
  @Nested
  class InTemplateFlow {

    @Test
    void testFindByTenantIdAndNetworkId() {
      assertThat(target.findByTenantIdAndNetworkId("1373fcf2d15911eea5060242ac120002",
          "420d50d6d15911eea5060242ac120002", Pageable.unpaged()))
          .hasSize(2)
          .extracting(AbstractBaseEntity::getId)
          .containsExactlyInAnyOrder("5a97e10cd15911eea5060242ac120002", "ce3421a31b064474b7ec6bcc5093fcd1");
    }

    @Test
    void testFindByTenantIdAndVenueId() {
      assertThat(target.findByTenantIdAndVenueId("1373fcf2d15911eea5060242ac120002",
          "2160b65cd15911eea5060242ac120002", Pageable.unpaged()))
          .hasSize(1);
    }

    @Test
    void testFindByTenantId() {
      assertThat(target.findByTenantId(Pageable.unpaged(), "1373fcf2d15911eea5060242ac120002"))
          .isNotEmpty();
    }
  }

  @Nested
  class OutOfTemplateFlow {

    @Test
    void testFindByTenantIdAndNetworkId() {
      assertThat(target.findByTenantIdAndNetworkId("1373fcf2d15911eea5060242ac120002",
          "420d50d6d15911eea5060242ac120002", Pageable.unpaged()))
          .hasSize(0);
    }

    @Test
    void testFindByTenantIdAndVenueId() {
      assertThat(target.findByTenantIdAndVenueId("1373fcf2d15911eea5060242ac120002",
          "2160b65cd15911eea5060242ac120002", Pageable.unpaged()))
          .hasSize(0);
    }

    @Test
    void testFindByTenantId() {
      assertThat(target.findByTenantId(Pageable.unpaged(), "1373fcf2d15911eea5060242ac120002"))
          .isEmpty();
    }
  }

  @Test
  void testFormattedNetworkIdAtVenueId() {
    final var expectedNetworkId = randomId();
    final var expectedVenueId = randomId();

    assertThat(formattedNetworkIdAtVenueId(expectedNetworkId, expectedVenueId))
        .isEqualTo("%s@%s".formatted(expectedNetworkId, expectedVenueId));

    assertThat(formattedNetworkIdAtVenueId(new Network(expectedNetworkId), new Venue(expectedVenueId)))
        .isEqualTo("%s@%s".formatted(expectedNetworkId, expectedVenueId));
  }

  @Test
  void testFindByTenantIdAndNetworkIdIn() {
    assertThat(target.findByTenantIdAndNetworkIdIn("e359afa2d15911eea5060242ac120002",
        List.of("07e5f042d15a11eea5060242ac120002")))
        .hasSize(1);
  }

  @Test
  void testFindByTenantIdAndVenueIdIn() {
    assertThat(target.findByTenantIdAndVenueIdIn("e359afa2d15911eea5060242ac120002",
        List.of("de6d4a62d15911eea5060242ac120002")))
        .hasSize(1);
  }

  @Sql(statements ="""
       INSERT INTO tenant (id) VALUES ('0b6e7b0ac19a41fba2e88cc827a911b7');
       INSERT INTO venue (id, tenant) VALUES
        ('5091ff395ae2486eafa87da4d1b25148', '0b6e7b0ac19a41fba2e88cc827a911b7'), -- Venue 1
        ('cde0f269c94a5208bbbcba1048fa121b', '0b6e7b0ac19a41fba2e88cc827a911b7'), -- Venue 2
        ('b87bb8b4ffbf48fd8bf324cdb71c7a04', '0b6e7b0ac19a41fba2e88cc827a911b7'); -- Venue 3
       INSERT INTO network (id, name, type, tenant) VALUES 
        ('c7d2631daaad4d9fbd677dbe40a771ef', 'c7d2631daaad4d9fbd677dbe40a771ef', -- Network 1
         'OPEN', '0b6e7b0ac19a41fba2e88cc827a911b7'),
        ('4d3c6ef27bc84509bb8c417cc5403bd2', '4d3c6ef27bc84509bb8c417cc5403bd2', -- Network 2
         'OPEN', '0b6e7b0ac19a41fba2e88cc827a911b7'),
        ('5a89ee913c24403fb9f2dae4d414dbd9', '5a89ee913c24403fb9f2dae4d414dbd9', -- Network 3
         'OPEN', '0b6e7b0ac19a41fba2e88cc827a911b7');
       INSERT INTO network_venue (id, venue, network, tenant) VALUES
        ('eebf148484cb4a419c31df8cadd84c5a', '5091ff395ae2486eafa87da4d1b25148',  -- NV1 = Venue1, Network1
         'c7d2631daaad4d9fbd677dbe40a771ef', '0b6e7b0ac19a41fba2e88cc827a911b7'),
        ('82bad82b17424bc5aee38f920723dd24', '5091ff395ae2486eafa87da4d1b25148',  -- NV2 = Venue1, Network2
         '4d3c6ef27bc84509bb8c417cc5403bd2', '0b6e7b0ac19a41fba2e88cc827a911b7'),
        ('7c7b72abb9e6428584a666e6ed515233', 'cde0f269c94a5208bbbcba1048fa121b',  -- NV3 = Venue2, Network2
         '4d3c6ef27bc84509bb8c417cc5403bd2', '0b6e7b0ac19a41fba2e88cc827a911b7'),
        ('b3aa355021a74ca7b72ab65909069e7d', 'cde0f269c94a5208bbbcba1048fa121b',  -- NV4 = Venue2, Network3
         '5a89ee913c24403fb9f2dae4d414dbd9', '0b6e7b0ac19a41fba2e88cc827a911b7'),
        ('66df403ca7c5492aa0c58d7f7e79d6b6', 'b87bb8b4ffbf48fd8bf324cdb71c7a04',  -- NV5 = Venue3, Network3
         '5a89ee913c24403fb9f2dae4d414dbd9', '0b6e7b0ac19a41fba2e88cc827a911b7');
      """)
  @Test
  void testFindByTenantAndVenueIdInAndNetworkIdIn() {
    final var tenantId = "0b6e7b0ac19a41fba2e88cc827a911b7";
    final var v1 = "5091ff395ae2486eafa87da4d1b25148";
    final var v2 = "cde0f269c94a5208bbbcba1048fa121b";
    final var v3 = "b87bb8b4ffbf48fd8bf324cdb71c7a04";
    final var n1 = "c7d2631daaad4d9fbd677dbe40a771ef";
    final var n2 = "4d3c6ef27bc84509bb8c417cc5403bd2";
    final var n3 = "5a89ee913c24403fb9f2dae4d414dbd9";
    final var nv1 = "eebf148484cb4a419c31df8cadd84c5a";
    final var nv2 = "82bad82b17424bc5aee38f920723dd24";
    final var nv3 = "7c7b72abb9e6428584a666e6ed515233";
    final var nv4 = "b3aa355021a74ca7b72ab65909069e7d";
    final var nv5 = "66df403ca7c5492aa0c58d7f7e79d6b6";


    assertThat(target.findByTenantIdAndVenueIdInAndNetworkIdIn(tenantId,
        List.of(v1), List.of(n1, n2)))
        .hasSize(2)
        .extracting(AbstractBaseEntity::getId)
        .containsOnly(nv1, nv2);

    assertThat(target.findByTenantIdAndVenueIdInAndNetworkIdIn(tenantId,
        List.of(v1, v3), List.of(n1, n3)))
        .hasSize(2)
        .extracting(AbstractBaseEntity::getId)
        .containsOnly(nv1, nv5);

    assertThat(target.findByTenantIdAndVenueIdInAndNetworkIdIn(tenantId,
        List.of(v1, v2, v3), List.of(n2, n3)))
        .hasSize(4)
        .extracting(AbstractBaseEntity::getId)
        .containsOnly(nv2, nv3, nv4, nv5);

    assertThat(target.findByTenantIdAndVenueIdInAndNetworkIdIn(tenantId,
        List.of(v1, v2, v3), List.of(n1, n2, n3)))
        .hasSize(5)
        .extracting(AbstractBaseEntity::getId)
        .containsOnly(nv1, nv2, nv3, nv4, nv5);

    assertThat(target.findByTenantIdAndVenueIdInAndNetworkIdIn(tenantId,
        List.of(n2, n3), List.of(v1)))
        .isEmpty();
  }
}
