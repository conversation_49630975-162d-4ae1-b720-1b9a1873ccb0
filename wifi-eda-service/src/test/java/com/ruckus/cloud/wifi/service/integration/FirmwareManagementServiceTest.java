package com.ruckus.cloud.wifi.service.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ruckus.cloud.wifi.capabilities.CloudSupportApModels;
import com.ruckus.cloud.wifi.client.tenant.TenantClient;
import com.ruckus.cloud.wifi.client.venue.VenueClient;
import com.ruckus.cloud.wifi.context.ContextHelper;
import com.ruckus.cloud.wifi.core.service.FeatureFlagService;
import com.ruckus.cloud.wifi.eda.api.rest.viewmodel.EnvironmentApFirmwareInfoView;
import com.ruckus.cloud.wifi.eda.servicemodel.ApVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.ScheduleTimeSlot;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeSchedule;
import com.ruckus.cloud.wifi.eda.servicemodel.UpgradeScheduleFirmwareVersion;
import com.ruckus.cloud.wifi.eda.servicemodel.Venue;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueApModelSpecificAttributes;
import com.ruckus.cloud.wifi.eda.servicemodel.VenueCurrentFirmware;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.ApVersionLabelEnum;
import com.ruckus.cloud.wifi.featureflag.FlagNames;
import com.ruckus.cloud.wifi.kafka.publisher.WifiSchedulePublisher;
import com.ruckus.cloud.wifi.mapper.VenueMerge;
import com.ruckus.cloud.wifi.mapper.VenueMergeImpl;
import com.ruckus.cloud.wifi.notification.kafka.TenantFirmwareNotificationSender;
import com.ruckus.cloud.wifi.repository.ApModelGreenfieldFirmwareRepository;
import com.ruckus.cloud.wifi.repository.ApModelMinimumFirmwareRepository;
import com.ruckus.cloud.wifi.repository.ApRepository;
import com.ruckus.cloud.wifi.repository.ApVersionRepository;
import com.ruckus.cloud.wifi.repository.ScheduleTimeSlotRepository;
import com.ruckus.cloud.wifi.repository.TenantAvailableApFirmwareRepository;
import com.ruckus.cloud.wifi.repository.TenantCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.repository.TenantRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleFirmwareVersionRepository;
import com.ruckus.cloud.wifi.repository.UpgradeScheduleRepository;
import com.ruckus.cloud.wifi.repository.VenueApModelSpecificAttributesRepository;
import com.ruckus.cloud.wifi.repository.VenueCurrentFirmwareRepository;
import com.ruckus.cloud.wifi.repository.VenueRepository;
import com.ruckus.cloud.wifi.service.ApUpgradeService;
import com.ruckus.cloud.wifi.service.ExtendedVenueServiceCtrl;
import com.ruckus.cloud.wifi.service.FirmwareManagementService;
import com.ruckus.cloud.wifi.service.ScopeDataService;
import com.ruckus.cloud.wifi.service.TenantAvailableApFirmwareService;
import com.ruckus.cloud.wifi.service.TenantCurrentFirmwareService;
import com.ruckus.cloud.wifi.service.TenantService;
import com.ruckus.cloud.wifi.service.core.tx.TxCtxHolder;
import com.ruckus.cloud.wifi.service.impl.FirmwareManagementServiceImpl;
import com.ruckus.cloud.wifi.service.impl.TenantAvailableApFirmwareServiceImpl;
import com.ruckus.cloud.wifi.service.impl.TenantCurrentFirmwareServiceImpl;
import com.ruckus.cloud.wifi.service.impl.TenantServiceImpl;
import com.ruckus.cloud.wifi.service.integration.config.ExtendedVenueServiceCtrlImplTestConfig;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareModelQuery;
import com.ruckus.cloud.wifi.viewmodel.ApFirmwareSupportModels;
import com.ruckus.cloud.wifi.viewmodel.ApModelFirmware;
import com.ruckus.cloud.wifi.viewmodel.ApModelFirmwareSchedules;
import com.ruckus.cloud.wifi.viewmodel.VenueApFirmwareUpgradeSchedule;
import com.ruckus.cloud.wifi.viewmodel.VenueSchedule;
import com.ruckus.cloud.wifi.viewmodel.VenueVersion;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections.ListUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.testcontainers.shaded.com.fasterxml.jackson.core.JsonProcessingException;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

@WifiJpaDataTest
@ExtendWith(TxCtxExtension.class)
@TestPropertySource(properties = {
  "default.fw-upgrade.filter.eleven-ac-wave1-version: *********.552",
  "default.fw-upgrade.filter.eleven-ac-wave1-max-version: 6.2.0.999999.0.0",
  "default.fw-upgrade.filter.eleven-ac-wave2-version: 6.2.3.103.244",
  "default.fw-upgrade.filter.eleven-ac-wave2-max-version: 6.2.999999.0.0",
  "default.fw-upgrade.filter.active-version: 7.0.0.104.1404"
})
@Sql(statements = """
    INSERT INTO tenant(id) VALUES ('9520ee78bb1222ec84220242ac120002');
    INSERT INTO ap_version(id, is_greenfield, supported_ap_models, category, labels) VALUES
        ('*********.123', true, '["R500","R600","R510","R610"]', 'RECOMMENDED', 'ga,beta,alpha'),
        ('*********.800', false, '["C110", "R500", "R510", "R610"]', 'RECOMMENDED', 'ga,alpha'),
        ('*********.434', true, '["C110", "R510","R610","R550"]', 'RECOMMENDED', 'ga,beta'),
        ('*********.556', false, '["R550","R650","R750","R770"]', 'RECOMMENDED', 'beta'),
        ('*********.983', false, null, 'RECOMMENDED', 'alpha'),
        ('*********.300', false, '["R770"]', 'RECOMMENDED', 'ga'),
        ('*********.230', true, '["R550","R350:R350E","R770"]', 'RECOMMENDED', null);
    INSERT INTO venue (id, name, tenant, timezone) VALUES
        ('1dec899ad64e47649981797a26ab5668', 'Taipei-101', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei'),
        ('2dec899ad64e47649981797a26ab5668', 'CityHall', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei'),
        ('3dec899ad64e47649981797a26ab5668', 'CityCafe', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei'),
        ('4dec899ad64e47649981797a26ab5668', 'Hotel', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei');
    INSERT INTO ap_group (id, venue, tenant) VALUES
        ('818552afabf544878057e510b9bb88b5', '2dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002');
    INSERT INTO ap (id, name, ap_group, tenant, model, mac, soft_deleted) VALUES
        ('900000008883', 'My AP 650', '818552afabf544878057e510b9bb88b5', '9520ee78bb1222ec84220242ac120002', 'R650', 'AA:BB:CC:DD:EE:HH', false),
        ('900000008884', 'My AP 770', '818552afabf544878057e510b9bb88b5', '9520ee78bb1222ec84220242ac120002', 'R770', 'AA:BB:CC:DD:EE:GG', false),
        ('900000008885', 'My AP 550', '818552afabf544878057e510b9bb88b5', '9520ee78bb1222ec84220242ac120002', 'R550', 'AA:BB:CC:DD:EE:FF', false);
    INSERT INTO venue_current_firmware (id, ap_model, firmware, created_date, updated_date, venue, tenant) VALUES
        ('d09c1264ae704ba1969152aa44cc083a', 'R650', '*********.556', '2024-03-25 06:29:53.886', '2024-03-25 06:29:53.886', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('a09c1264ae704ba1969152aa44cc083a', 'R550', '*********.434', '2024-03-26 06:29:53.886', '2024-03-26 06:29:53.886', '2dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('r904kdf4ki5kfieldwsk3ij445j4imdm', 'R610', '*********.556', '2024-03-27 06:29:54.886', '2024-03-27 06:29:53.886', '2dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('2dmeiroejdjicm5j23j2mm2i3jemdi2j', 'R600', '*********.123', '2024-03-28 06:29:55.886', '2024-03-28 06:29:53.886', '2dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('3k4kdek3k4jkj1dlkfkrodk2kk3kk22k', 'R770', '*********.230', '2024-03-29 06:29:56.886', '2024-03-29 06:29:53.886', '3dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002');
    INSERT INTO tenant_available_ap_firmware(id, ap_version, tenant, created_date, updated_date)
    VALUES ('7720c78b733e41e19275a6abb24a0f3f7*********.800', '*********.800', '9520ee78bb1222ec84220242ac120002', '2022-03-24 00:11:53.427', '2022-03-24 00:11:53.427'),
        ('7720c78b733e41e19275a6abb24a0f3f8*********.123', '*********.123', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:53.427', '2022-03-23 00:11:53.427'),
        ('7720c78b733e41e19275a6abb24a0f3f9*********.434', '*********.434', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:54.427', '2022-03-23 00:11:54.427'),
        ('7720c78b733e41e19275a6abb24a0f3d1*********.556', '*********.556', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:55.427', '2022-03-23 00:11:55.427'),
        ('7720c78b733e41e19275a6abb24a0f3d2*********.983', '*********.983', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:56.427', '2022-03-23 00:11:56.427'),
        ('7720c78b733e41e19275a6abb24a0f3d3*********.300', '*********.300', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:11:57.427', '2022-03-23 00:11:57.427'),
        ('7720c78b733e41e19275a6abb24a0f3d3*********.230', '*********.230', '9520ee78bb1222ec84220242ac120002', '2022-03-23 00:10:57.427', '2022-03-23 00:10:57.427');
    INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time) VALUES
        ('dbd1e276ea86406996a9d91c5aca491e', 'WAITING_REMINDER', '2024-04-19 05:00:00.000', '2024-04-19 07:00:00.000');
    INSERT INTO upgrade_schedule(id, status, time_slot, version, venue, tenant) VALUES
        ('b75fa65db4de4e2e87a0d01129da131d', 'PENDING', 'dbd1e276ea86406996a9d91c5aca491e', '*********.556', '2dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002');
    INSERT INTO upgrade_schedule_firmware_version(id, tenant, ap_firmware_version, upgrade_schedule) VALUES 
        ('ff5fa65db4de4e2aa7a0d01129da13dd', '9520ee78bb1222ec84220242ac120002', '*********.556', 'b75fa65db4de4e2e87a0d01129da131d');    
    INSERT INTO ap_model_greenfield_firmware(id, firmware) VALUES 
        ('R650', '*********.556'),
        ('R550', '*********.434'),
        ('R610', '*********.556'),
        ('R600', '*********.123'),
        ('R770', '*********.230');
    INSERT INTO tenant_current_firmware(id, ap_model, tenant, firmware) VALUES 
        ('9520ee78bb1222ec84220242ac120002_R770','R770','9520ee78bb1222ec84220242ac120002','*********.230'),
        ('9520ee78bb1222ec84220242ac120002_R550','R550','9520ee78bb1222ec84220242ac120002','*********.230'),
        ('9520ee78bb1222ec84220242ac120002_R650','R650','9520ee78bb1222ec84220242ac120002','*********.556'),
        ('9520ee78bb1222ec84220242ac120002_R750','R750','9520ee78bb1222ec84220242ac120002','*********.556');
    """)
public class FirmwareManagementServiceTest extends AbstractServiceTest {

  @Autowired
  private FirmwareManagementService unit;

  @Autowired
  private ApVersionRepository apVersionRepository;

  @Autowired
  private UpgradeScheduleRepository upgradeScheduleRepository;

  @Autowired
  private ScheduleTimeSlotRepository scheduleTimeSlotRepository;

  @Autowired
  private VenueCurrentFirmwareRepository venueCurrentFirmwareRepository;

  @Autowired
  private VenueApModelSpecificAttributesRepository venueApModelSpecificAttributesRepository;

  @MockBean
  private TenantFirmwareNotificationSender tenantFirmwareNotificationSender;

  @MockBean
  private ApUpgradeService apUpgradeService;

  @MockBean
  private ScopeDataService scopeDataService;

  ObjectMapper objectMapper = new ObjectMapper();

  String TENANT_ID = "9520ee78bb1222ec84220242ac120002";
  @Autowired
  private TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository;

  @MockBean
  private TenantClient tenantClient;

  @BeforeEach
  void setUp() {
    doAnswer(invocation -> invocation.getArgument(0)).when(scopeDataService).filterByScopeData(any(), any());
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testGetVenueList() {
    var venues = unit.getVenueList(TENANT_ID, null).stream()
        .sorted(Comparator.comparing(VenueApFirmwareUpgradeSchedule::getId))
        .collect(Collectors.toList());

    assertThat(venues).hasSize(4);
    // 1dec899ad64e47649981797a26ab5668 contains R650, there are new firmwares in tenant, but not support R650
    assertThat(venues.get(0)).satisfies(v -> {
      assertThat(v.getId()).isEqualTo("1dec899ad64e47649981797a26ab5668");
      assertThat(v.isApFirmwareUpToDate()).isTrue();
      assertThat(v.getCurrentApFirmwares()).hasSize(1);
      assertThat(v.getCurrentApFirmwares().stream().map(ApModelFirmware::getApModel)).containsExactlyInAnyOrder("R650");
      assertThat(v.getLastApFirmwareUpdate()).isEqualTo(
          ZonedDateTime.of(
              LocalDateTime.parse("2024-03-25 06:29:53.886",
                  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
              ), ZoneId.systemDefault()));
      assertThat(v.getNextApFirmwareSchedules()).hasSize(0);
    });

    // 2dec899ad64e47649981797a26ab5668 contains R550, R600 and R610, there are new firmwares in tenant
    assertThat(venues.get(1)).satisfies(v -> {
      assertThat(v.getId()).isEqualTo("2dec899ad64e47649981797a26ab5668");
      assertThat(v.isApFirmwareUpToDate()).isFalse();
      assertThat(v.getCurrentApFirmwares()).hasSize(3);
      assertThat(v.getCurrentApFirmwares().stream().map(ApModelFirmware::getApModel)).containsExactlyInAnyOrder("R610", "R550", "R600");
      assertThat(v.getLastApFirmwareUpdate()).isEqualTo(
          ZonedDateTime.of(
              LocalDateTime.parse("2024-03-28 06:29:53.886",
              DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
          ), ZoneId.systemDefault()));
      assertThat(v.getNextApFirmwareSchedules()).hasSize(1);
      assertThat(v.getNextApFirmwareSchedules().stream().map(VenueSchedule::getVersionInfo).map(VenueVersion::getVersion))
          .containsExactlyInAnyOrder("*********.556");
    });

    // 3dec899ad64e47649981797a26ab5668 contains only 1 R770 with latest *********.300
    assertThat(venues.get(2)).satisfies(v -> {
      assertThat(v.getId()).isEqualTo("3dec899ad64e47649981797a26ab5668");
      assertThat(v.isApFirmwareUpToDate()).isTrue();
      assertThat(v.getCurrentApFirmwares()).hasSize(1);
      assertThat(v.getCurrentApFirmwares().stream().map(ApModelFirmware::getApModel)).containsExactlyInAnyOrder("R770");
      assertThat(v.getLastApFirmwareUpdate()).isEqualTo(
          ZonedDateTime.of(
              LocalDateTime.parse("2024-03-29 06:29:53.886",
              DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
          ), ZoneId.systemDefault()));
    });

    // Empty Venue always return isApFirmwareUpToDate = true
    assertThat(venues.get(3)).satisfies(v -> {
      assertThat(v.getId()).isEqualTo("4dec899ad64e47649981797a26ab5668");
      assertThat(v.isApFirmwareUpToDate()).isTrue();
    });
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testGetVenueListWithQueryName() {
    var venues = unit.getVenueList(TENANT_ID, ApFirmwareModelQuery.builder().search("Hotel").build());

    assertThat(venues).hasSize(1);
    assertThat(venues.get(0)).satisfies(v -> {
      assertThat(v.getId()).isEqualTo("4dec899ad64e47649981797a26ab5668");
      assertThat(v.isApFirmwareUpToDate()).isTrue();
    });
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testGetVenueListWithQueryApFirmware() {
    var venues = unit.getVenueList(TENANT_ID, ApFirmwareModelQuery.builder().firmwareVersion("*********.230").build());

    // 3dec899ad64e47649981797a26ab5668 contains only 1 R770 with latest *********.300
    assertThat(venues).hasSize(1);
    assertThat(venues.get(0)).satisfies(v -> {
      assertThat(v.getId()).isEqualTo("3dec899ad64e47649981797a26ab5668");
      assertThat(v.isApFirmwareUpToDate()).isTrue();
      assertThat(v.getCurrentApFirmwares()).hasSize(1);
      assertThat(v.getCurrentApFirmwares().stream().map(ApModelFirmware::getApModel)).containsExactlyInAnyOrder("R770");
      assertThat(v.getLastApFirmwareUpdate()).isEqualTo(
          ZonedDateTime.of(
              LocalDateTime.parse("2024-03-29 06:29:53.886",
                  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
              ), ZoneId.systemDefault()));
    });
  }

  @Test
  @FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL, FlagNames.AP_FW_MGMT_EARLY_ACCESS_TOGGLE })
  void testGetApModelFirmwares() {
    var result = unit.getApModelFirmwares(TENANT_ID, false);
    assertEquals(7, result.size());
    assertOrdered(result, "*********.230", "*********.300", "*********.983", "*********.556", "*********.434", "*********.800", "*********.123");
    assertSupportedApModels(result,
        List.of("R550", "R770"),
        List.of("R770"),
        Collections.emptyList(),
        List.of("R550", "R650", "R770"),
        List.of("R550"),
        Collections.emptyList(),
        Collections.emptyList());
  }

  @Deprecated
  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testGetCurrentFirmwareVersionList() {
    assertThat(unit.getCurrentFirmwareVersionList(TENANT_ID))
      .hasSize(4);
  }

  @Test
  void testGetScheduledFirmwares() throws JsonProcessingException {
    List<ApFirmwareSupportModels> result = unit.getScheduledFirmwares(TENANT_ID);
    assertThat(result)
        .hasSize(1)
        .extracting(ApFirmwareSupportModels::getId)
        .isEqualTo(List.of("*********.556"));
    String strResult = objectMapper.writeValueAsString(result);
    assertFalse(strResult.toLowerCase().contains("null"));
  }

  @Test
  void testGetDistinctFirmwares() throws JsonProcessingException {
    List<ApFirmwareSupportModels> result = unit.getDistinctFirmwares(TENANT_ID);
    assertThat(unit.getDistinctFirmwares(TENANT_ID))
        .hasSize(4);
    String strResult = objectMapper.writeValueAsString(result);
    assertFalse(strResult.toLowerCase().contains("null"));
  }

  @Test
  void testGetGreenFieldFirmwares() {
    List<ApFirmwareSupportModels> result = unit.getGreenfieldFirmwares();
    assertThat(result).hasSize(4)
        .extracting(ApFirmwareSupportModels::getId, ApFirmwareSupportModels::getSupportedApModels)
        .containsExactlyInAnyOrder(
            tuple("*********.123", List.of("R500","R600","R510","R610")),
            tuple("*********.434", List.of("R510", "R610", "R550")),
            tuple("*********.556", List.of("R550","R650","R750","R770")),
            tuple("*********.230", List.of("R550", "R350:R350E", "R770")));
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testGetApModelLatestAvailableFirmwaresByTenantId() {
    assertThat(unit.getApModelLatestAvailableFirmwaresByTenantId(TENANT_ID))
        .hasSize(9)
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R500", "*********.800"),
            tuple("R510", "*********.434"),
            tuple("R600", "*********.123"),
            tuple("R610", "*********.434"),
            tuple("R650", "*********.556"),
            tuple("R750", "*********.556"),
            tuple("R550", "*********.230"),
            tuple("R350:R350E", "*********.230"),
            tuple("R770", "*********.230"));
    assertThat(unit.getApModelLatestAvailableFirmwaresByTenantId("tenantId")).isEmpty();
  }

  @Test
  @FeatureFlag(enable = {FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE})
  void testGetApModelLatestAvailableFirmwaresByTenantIdTcf() {
    assertThat(unit.getApModelLatestAvailableFirmwaresByTenantId(TENANT_ID))
        .hasSize(4)
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R770", "*********.230"),
            tuple("R550", "*********.230"),
            tuple("R650", "*********.556"),
            tuple("R750", "*********.556"));
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testGetEnvironmentApFirmwares() {
    List<EnvironmentApFirmwareInfoView> result = unit.getEnvironmentApFirmwares(true);
    assertThat(result)
        .hasSizeGreaterThanOrEqualTo(3)
        .extracting(EnvironmentApFirmwareInfoView::getFirmware)
        .contains("*********.800", "*********.983", "*********.230");

    assertThat(result).filteredOn(envFw -> envFw.getFirmware().equals("*********.800")).singleElement()
        .extracting(EnvironmentApFirmwareInfoView::getLabels)
        .asList().containsExactlyInAnyOrderElementsOf(List.of(ApVersionLabelEnum.ALPHA, ApVersionLabelEnum.GA));

    List<EnvironmentApFirmwareInfoView> resultAll = unit.getEnvironmentApFirmwares(false);
    assertThat(resultAll)
        .extracting(EnvironmentApFirmwareInfoView::getFirmware)
        .hasSizeGreaterThanOrEqualTo(7)
        .contains("*********.123",
            "*********.123",
            "*********.800",
            "*********.434",
            "*********.556",
            "*********.983",
            "*********.300",
            "*********.230");

    assertThat(resultAll).filteredOn(envFw -> envFw.getFirmware().equals("*********.556")).singleElement()
        .extracting(EnvironmentApFirmwareInfoView::getLabels)
        .asList().containsExactlyInAnyOrderElementsOf(List.of(ApVersionLabelEnum.BETA));
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testGetEnvironmentApModels() {
    List<String> result = unit.getEnvironmentApModels();
    assertThat(result)
        .hasSize(48)
        .contains("R850", "R350:R350E", "R770", "T670");
  }

  @Test
  void testGetAndUpsertGreenfieldApFirmwares() {
    List<ApModelFirmware> result = unit.getGreenfieldApFirmwares();
    assertThat(result)
        .hasSize(5)
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R650", "*********.556"),
            tuple("R550", "*********.434"),
            tuple("R610", "*********.556"),
            tuple("R600", "*********.123"),
            tuple("R770", "*********.230"));

    ApModelFirmware apModelFirmware = ApModelFirmware.builder().apModel("T670").firmware("*********.230").build();
    ApModelFirmware apModelFirmware2 = ApModelFirmware.builder().apModel("R550").firmware("*********.230").build();
    unit.upsertGreenfieldApFirmware(List.of(apModelFirmware, apModelFirmware2));
    result = unit.getGreenfieldApFirmwares();
    assertThat(result)
        .hasSize(6)
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R650", "*********.556"),
            tuple("R550", "*********.230"),
            tuple("R610", "*********.556"),
            tuple("R600", "*********.123"),
            tuple("R770", "*********.230"),
            tuple("T670", "*********.230"));
  }

  @Test
  void testGetGreenfieldImpactedApModels() {
    List<String> result = unit.getGreenfieldImpactedApModels("*********.230");
    assertThat(result)
        .hasSize(2)
        .containsExactlyInAnyOrder("R550","R350:R350E");
  }

  @Test
  void testGetGreenfieldImpactedApModelsEmpty() {
    List<String> result = unit.getGreenfieldImpactedApModels("1.2.3.4.5");
    assertThat(result)
        .hasSize(0);
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testGetCurrentFirmwaresByTenantIdAndVenueId() {
    String venueId = "2dec899ad64e47649981797a26ab5668";
    assertThat(unit.getCurrentFirmwaresByTenantIdAndVenueId(TENANT_ID, venueId, true))
        .hasSize(9)
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R550", "*********.434"),
            tuple("R610", "*********.556"),
            tuple("R600", "*********.123"),
            tuple("R500", "*********.800"),
            tuple("R510", "*********.434"),
            tuple("R650", "*********.556"),
            tuple("R750", "*********.556"),
            tuple("R350:R350E", "*********.230"),
            tuple("R770", "*********.230"));
    assertThat(unit.getCurrentFirmwaresByTenantIdAndVenueId(TENANT_ID, venueId, false))
        .hasSize(3)
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R550", "*********.434"),
            tuple("R610", "*********.556"),
            tuple("R600", "*********.123"));
  }

  @Test
  @FeatureFlag(enable = { FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL,
      FlagNames.GREENFIELD_BY_AP_MODEL_TOGGLE})
  void testGetCurrentFirmwaresByTenantIdAndVenueIdTcf() {
    String venueId = "2dec899ad64e47649981797a26ab5668";
    assertThat(unit.getCurrentFirmwaresByTenantIdAndVenueId(TENANT_ID, venueId, true))
        .hasSize(6)
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R550", "*********.434"),
            tuple("R610", "*********.556"),
            tuple("R600", "*********.123"),
            tuple("R650", "*********.556"),
            tuple("R770", "*********.230"),
            tuple("R750", "*********.556"));
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  void testGetCurrentFirmwaresByVenueIdAndModel() {
    String venueId = "2dec899ad64e47649981797a26ab5668";
    assertThat(unit.getCurrentFirmwaresByVenueIdAndModel(venueId, "R550"))
        .isNotNull()
        .satisfies(vcf -> {
          assertEquals("R550", vcf.getApModel());
          assertEquals("*********.434", vcf.getFirmware());
        });
    assertThat(unit.getCurrentFirmwaresByVenueIdAndModel(venueId, "R600"))
        .isNotNull()
        .satisfies(vcf -> {
          assertEquals("R600", vcf.getApModel());
          assertEquals("*********.123", vcf.getFirmware());
        });
    assertThat(unit.getCurrentFirmwaresByVenueIdAndModel(venueId, "R610"))
        .isNotNull()
        .satisfies(vcf -> {
          assertEquals("R610", vcf.getApModel());
          assertEquals("*********.556", vcf.getFirmware());
        });
    assertThat(unit.getCurrentFirmwaresByVenueIdAndModel(venueId, "R650")).isNull();
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  @Sql(statements = """
      INSERT INTO ap_version(id, category) VALUES
        ('*********.123', 'RECOMMENDED'), ('*********.124', 'RECOMMENDED'), ('*********.321', 'RECOMMENDED');
      INSERT INTO ap_model_minimum_firmware(id, minimum_firmware) VALUES 
        ('R550', '*********.123'), ('R560', '*********.124'), ('R750', '*********.321'), ('R770', '*********.321');
      """)
  void testGetAllApModelMinimumFirmwares() {
    assertThat(unit.getAllApModelMinimumFirmwares())
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R550", "*********.123"),
            tuple("R560", "*********.124"),
            tuple("R750", "*********.321"),
            tuple("R770", "*********.321"));
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  @Sql(statements = """
      INSERT INTO ap_version(id, category) VALUES
        ('*********.123', 'RECOMMENDED'), ('*********.124', 'RECOMMENDED'), ('*********.321', 'RECOMMENDED');
      INSERT INTO ap_model_minimum_firmware(id, minimum_firmware) VALUES 
        ('R550', '*********.123'), ('R560', '*********.124'), ('R750', '*********.321'), ('R770', '*********.321');
      """)
  void testUpsertApModelMinimumFirmware() {
    ApModelFirmware existedApModelMinimumFirmware = ApModelFirmware.builder().apModel("R770").firmware("*********.124").build();

    unit.upsertApModelMinimumFirmware(existedApModelMinimumFirmware);

    assertThat(unit.getAllApModelMinimumFirmwares())
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R550", "*********.123"),
            tuple("R560", "*********.124"),
            tuple("R750", "*********.321"),
            tuple("R770", "*********.124"));

    ApModelFirmware newApModelMinimumFirmware = ApModelFirmware.builder().apModel("R888").firmware("*********.321").build();

    unit.upsertApModelMinimumFirmware(newApModelMinimumFirmware);

    assertThat(unit.getAllApModelMinimumFirmwares())
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R550", "*********.123"),
            tuple("R560", "*********.124"),
            tuple("R750", "*********.321"),
            tuple("R770", "*********.124"),
            tuple("R888", "*********.321"));
  }

  @Test
  @FeatureFlag(enable = FlagNames.AP_FW_MGMT_UPGRADE_BY_MODEL)
  @Sql(statements = """
      INSERT INTO ap_version(id, category) VALUES
        ('*********.123', 'RECOMMENDED'), ('*********.124', 'RECOMMENDED'), ('*********.321', 'RECOMMENDED');
      INSERT INTO ap_model_minimum_firmware(id, minimum_firmware) VALUES 
        ('R550', '*********.123'), ('R560', '*********.124'), ('R750', '*********.321'), ('R770', '*********.321');
      """)
  void testDeleteApModelMinimumFirmware() {
    unit.deleteApModelMinimumFirmware("R770");
    unit.deleteApModelMinimumFirmware("R750");

    assertThat(unit.getAllApModelMinimumFirmwares())
        .extracting(ApModelFirmware::getApModel, ApModelFirmware::getFirmware)
        .containsExactlyInAnyOrder(
            tuple("R550", "*********.123"),
            tuple("R560", "*********.124"));
  }

  @Test
  @Sql(statements = """
  TRUNCATE TABLE upgrade_schedule CASCADE;
  INSERT INTO ap_version(id, is_greenfield, supported_ap_models, category, created_date, updated_date) VALUES
        ('*********.556', false, '["R550","R650","R750","R770"]', 'RECOMMENDED', '2022-03-23 00:11:53.428', '2022-03-23 00:11:53.428'),
        ('*********.230', true, '["R550","R350:R350E","R770"]', 'RECOMMENDED', '2022-03-23 00:18:53.427', '2022-03-23 00:18:53.427');
  INSERT INTO schedule_time_slot(id, status, start_date_time, end_date_time) VALUES
    ('dbd1e276ea86406996a9d91c5aca491e', 'WAITING_REMINDER', '2024-04-19 05:00:00.000', '2024-04-19 07:00:00.000');
  """)
  void testCreateUpgradeScheduleWithApModel(Tenant tenant, Venue venue) {
    String apModel = "R550";
    List<ApModelFirmware> targetFirmwares = List.of(ApModelFirmware.builder().firmware("*********.230").apModel(apModel).build());
    ApVersion targetVersion = new ApVersion("*********.230");
    ApVersion orgVersion = new ApVersion("*********.556");
    ScheduleTimeSlot scheduleTimeSlot = scheduleTimeSlotRepository.getReferenceById("dbd1e276ea86406996a9d91c5aca491e");
    Map<String, ApVersion> supportedFirmwareMap = Map.of(
        targetVersion.getId(), targetVersion,
        orgVersion.getId(), orgVersion
    );

    unit.createUpgradeScheduleWithApModel(tenant.getId(), venue.getId(), targetFirmwares, scheduleTimeSlot, supportedFirmwareMap);
    UpgradeSchedule existSchedule = upgradeScheduleRepository.findFirstByVenueIdAndStatusIsPendingOrNullOrderById(venue.getId());
    assertThat(existSchedule).isNotNull();
    UpgradeScheduleFirmwareVersion result = existSchedule.getUpgradeScheduleFirmwareVersions().get(0);
    assertThat(result.getApFirmwareVersion()).isEqualTo(targetVersion);
    assertThat(result.getTargetApModels()).contains(apModel);

  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant(id) VALUES ('9520ee78bb1222ec84220242ac120002');
      INSERT INTO ap_version(id, category, supported_ap_models) VALUES
        ('*********.123', 'RECOMMENDED', '["R500","R550","R650"]'), 
        ('*********.124', 'RECOMMENDED', '["R550","R650"]'), 
        ('*********.321', 'RECOMMENDED', '["R550","R650","R770"]');
      INSERT INTO venue (id, name, tenant, timezone) VALUES
        ('1dec899ad64e47649981797a26ab5668', 'Taipei-101', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei'),
        ('4dec899ad64e47649981797a26ab5668', 'Hotel', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei'),
        ('e3e5ww9ad64e4764998ef4na3rfe2svf', 'Taipei-101', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei');
      INSERT INTO ap_group (id, venue, tenant) VALUES
        ('818552afabf544878057e510b9bb88b5', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('5erferafabf544878057e510b3er3efe', '4dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('eerffr2fabf544878057e510brlpf34l', 'e3e5ww9ad64e4764998ef4na3rfe2svf', '9520ee78bb1222ec84220242ac120002');
      INSERT INTO ap (id, name, ap_group, tenant, model, mac, soft_deleted) VALUES
        ('900000008883', 'My AP 770', '818552afabf544878057e510b9bb88b5', '9520ee78bb1222ec84220242ac120002', 'R770', 'AA:BB:CC:DD:EE:HH', false),
        ('900000008884', 'My AP 550', '818552afabf544878057e510b9bb88b5', '9520ee78bb1222ec84220242ac120002', 'R550', 'AA:BB:CC:DD:EE:GG', false),
        ('900000008885', 'My AP 550', '5erferafabf544878057e510b3er3efe', '9520ee78bb1222ec84220242ac120002', 'R550', 'AA:BB:CC:DD:EE:FF', false),
        ('900000008886', 'My AP 550', 'eerffr2fabf544878057e510brlpf34l', '9520ee78bb1222ec84220242ac120002', null, null, false),
        ('900000008887', 'My AP 550', 'eerffr2fabf544878057e510brlpf34l', '9520ee78bb1222ec84220242ac120002', null, 'AA:BB:CC:DD:EE:II', false);
      INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant) VALUES
        ('d09c1264ae704ba1969152aa44cc083a', 'R770', '*********.321', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('er4rfe64ae704ba1969152aa44cc083a', 'R550', '*********.321', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('rgt66774ae704ba1969152aa44cc083a', 'R550', '*********.321', '4dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('2dmeiroejdjicm5j23j2mm2i3jemdi2j', 'R770', '*********.321', '4dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('45f5tre4jdjicm5j23j2mm24o4ko4kr4', 'R650', '*********.321', '4dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002');      
      """)
  void testPresetVersionMappingForUnjoinedApModels() throws Exception {
    ContextHelper.execute(() -> {
      try {
        unit.presetVersionMappingForUnjoinedApModels(TENANT_ID, "1dec899ad64e47649981797a26ab5668",
            "*********.124");
        unit.presetVersionMappingForUnjoinedApModels(TENANT_ID, "4dec899ad64e47649981797a26ab5668",
            "*********.123");
        unit.presetVersionMappingForUnjoinedApModels(TENANT_ID, "e3e5ww9ad64e4764998ef4na3rfe2svf",
            "*********.124");
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }, TENANT_ID, TxCtxHolder.txId(), TxCtxHolder.get().getFlowName());

    assertThat(venueCurrentFirmwareRepository.findByTenantIdAndVenueId(TENANT_ID,
        "1dec899ad64e47649981797a26ab5668"))
        .isNotEmpty()
        .extracting(VenueCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R550", "*********.321"),
            tuple("R770", "*********.321"),
            tuple("R650", "*********.124"));

    assertThat(venueCurrentFirmwareRepository.findByTenantIdAndVenueId(TENANT_ID,
        "4dec899ad64e47649981797a26ab5668"))
        .isNotEmpty()
        .extracting(VenueCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R500", "*********.123"),
            tuple("R550", "*********.321"),
            tuple("R770", "*********.321"),
            tuple("R650", "*********.123"));

    assertThat(venueCurrentFirmwareRepository.findByTenantIdAndVenueId(TENANT_ID,
        "e3e5ww9ad64e4764998ef4na3rfe2svf"))
        .isNotEmpty()
        .extracting(VenueCurrentFirmware::getApModel, vcf -> vcf.getFirmware().getId())
        .containsExactlyInAnyOrder(
            tuple("R550", "*********.124"),
            tuple("R650", "*********.124"));
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant(id) VALUES ('9520ee78bb1222ec84220242ac120002');
      INSERT INTO ap_version(id, supported_ap_models) VALUES
        ('*********.123', '["R500","R550","R650"]'), 
        ('*********.124', '["R550","R650"]'), 
        ('*********.188', '["R550","R650"]'),
        ('*********.200', '["R550","R650","R770"]'),
        ('*********.321', '["R550","R650","R770"]');
      INSERT INTO venue (id, name, tenant, timezone) VALUES
        ('1dec899ad64e47649981797a26ab5668', 'Taipei-101', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei'),
        ('4dec899ad64e47649981797a26ab5668', 'Hotel', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei');
      INSERT INTO ap_group (id, venue, tenant) VALUES
        ('818552afabf544878057e510b9bb88b5', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('5erferafabf544878057e510b3er3efe', '4dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002');
      INSERT INTO ap (id, name, ap_group, tenant, model, mac, soft_deleted) VALUES
        ('900000008883', 'My AP 770', '818552afabf544878057e510b9bb88b5', '9520ee78bb1222ec84220242ac120002', 'R770', 'AA:BB:CC:DD:EE:HH', false),
        ('900000008884', 'My AP 550', '818552afabf544878057e510b9bb88b5', '9520ee78bb1222ec84220242ac120002', 'R550', 'AA:BB:CC:DD:EE:GG', false),
        ('900000008885', 'My AP 550', '5erferafabf544878057e510b3er3efe', '9520ee78bb1222ec84220242ac120002', 'R550', 'AA:BB:CC:DD:EE:FF', false);
      INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant) VALUES
        ('d09c1264ae704ba1969152aa44cc083a', 'R770', '*********.321', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('er4rfe64ae704ba1969152aa44cc083a', 'R550', '*********.321', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('rgt66774ae704ba1969152aa44cc083a', 'R550', '*********.321', '4dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002');
      INSERT INTO tenant_available_ap_firmware(id, ap_version, tenant) VALUES
        ('7720c78b733e41e19275a6abb24a0f3f7*********.800', '*********.123', '9520ee78bb1222ec84220242ac120002'),
        ('7720c78b733e41e19275a6abb24a0f3f8*********.123', '*********.124', '9520ee78bb1222ec84220242ac120002'),
        ('7720c78b733e41e19275a6abb24a0f3f9*********.188', '*********.188', '9520ee78bb1222ec84220242ac120002'),
        ('7720c78b733e41e19275a6abb24a0f3d3*********.321', '*********.321', '9520ee78bb1222ec84220242ac120002');
      """)
  void testReplaceTenantFirmwareVersions() throws Exception {
    ContextHelper.execute(() -> {
      try {
        unit.replaceTenantFirmwareVersions(TENANT_ID, List.of("*********.200", "*********.188", "*********.123"));
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }, TENANT_ID, TxCtxHolder.txId(), TxCtxHolder.get().getFlowName());

    assertThat(tenantAvailableApFirmwareRepository.findByTenant(new Tenant("9520ee78bb1222ec84220242ac120002")))
        .extracting(taaf -> taaf.getApVersion().getId())
        .hasSize(3)
        .containsExactlyInAnyOrder("*********.200", "*********.188", "*********.123");

    ArgumentCaptor<ApModelFirmwareSchedules> captor = ArgumentCaptor.forClass(ApModelFirmwareSchedules.class);
    verify(apUpgradeService, times(1)).updateNowByApModel(any(), eq("1dec899ad64e47649981797a26ab5668"),
        captor.capture());
    assertThat(captor.getValue())
        .extracting(ApModelFirmwareSchedules::getTargetFirmwares)
        .asList()
        .hasSize(2)
        .extracting(e -> ((ApModelFirmware) e).getApModel(), e -> ((ApModelFirmware) e).getFirmware())
        .containsExactlyInAnyOrder(tuple("R770", "*********.200"), tuple("R550", "*********.200"));

    verify(apUpgradeService, times(1)).updateNowByApModel(any(), eq("4dec899ad64e47649981797a26ab5668"),
        captor.capture());
    assertThat(captor.getValue())
        .extracting(ApModelFirmwareSchedules::getTargetFirmwares)
        .asList()
        .hasSize(1)
        .extracting(e -> ((ApModelFirmware) e).getApModel(), e -> ((ApModelFirmware) e).getFirmware())
        .containsExactlyInAnyOrder(tuple("R550", "*********.200"));
  }

  @Test
  @Sql(statements = """
      INSERT INTO tenant(id) VALUES ('9520ee78bb1222ec84220242ac120002');
      INSERT INTO ap_version(id, supported_ap_models) VALUES
        ('*********.123', '["R500","R550","R650"]'), 
        ('*********.124', '["R550","R650"]'), 
        ('*********.188', '["R550","R650"]'),
        ('*********.200', '["R550","R650","R770"]'),
        ('*********.321', '["R550","R650","R770","R888"]');
      INSERT INTO venue (id, name, tenant, timezone) VALUES
        ('1dec899ad64e47649981797a26ab5668', 'Taipei-101', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei'),
        ('4dec899ad64e47649981797a26ab5668', 'Hotel', '9520ee78bb1222ec84220242ac120002', 'Asia/Taipei');
      INSERT INTO ap_group (id, venue, tenant) VALUES
        ('818552afabf544878057e510b9bb88b5', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('5erferafabf544878057e510b3er3efe', '4dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002');
      INSERT INTO ap (id, name, ap_group, tenant, model, mac, soft_deleted) VALUES
        ('900000008883', 'My AP 770', '818552afabf544878057e510b9bb88b5', '9520ee78bb1222ec84220242ac120002', 'R770', 'AA:BB:CC:DD:EE:HH', false),
        ('900000008884', 'My AP 550', '818552afabf544878057e510b9bb88b5', '9520ee78bb1222ec84220242ac120002', 'R550', 'AA:BB:CC:DD:EE:GG', false),
        ('900000008885', 'My AP 550', '5erferafabf544878057e510b3er3efe', '9520ee78bb1222ec84220242ac120002', 'R550', 'AA:BB:CC:DD:EE:FF', false);
      INSERT INTO venue_current_firmware (id, ap_model, firmware, venue, tenant) VALUES
        ('d09c1264ae704ba1969152aa44cc083a', 'R770', '*********.321', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('er4rfe64ae704ba1969152aa44cc083a', 'R550', '*********.321', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002'),
        ('rgt66774ae704ba1969152aa44cc083a', 'R550', '*********.321', '4dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002');
      INSERT INTO tenant_available_ap_firmware(id, ap_version, tenant) VALUES
        ('7720c78b733e41e19275a6abb24a0f3f7*********.800', '*********.123', '9520ee78bb1222ec84220242ac120002'),
        ('7720c78b733e41e19275a6abb24a0f3f8*********.123', '*********.124', '9520ee78bb1222ec84220242ac120002'),
        ('7720c78b733e41e19275a6abb24a0f3d3*********.321', '*********.321', '9520ee78bb1222ec84220242ac120002');
      INSERT INTO venue_ap_model_specific_attributes (id, venue, tenant, model) VALUES 
        ('e5r2fe2edw384f45831ef560cd34ewde', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002', 'R888'),
        ('8r8fke66533ferwfrege5eff234453dw', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002', 'R777'),
        ('fe563eaztu65425re85ur4er4509rkfm', '1dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002', 'R550'),
        ('e2k0pm4650wocd99oejxr9lhg5jjjkde', '4dec899ad64e47649981797a26ab5668', '9520ee78bb1222ec84220242ac120002', 'R666');
      """)
  void testReplaceTenantFirmwareVersions_whenUnsupportedVenueModelSpecificAttrsExist_deleteAttrs() throws Exception {
    ContextHelper.execute(() -> {
      try {
        unit.replaceTenantFirmwareVersions(TENANT_ID, List.of("*********.200", "*********.188", "*********.123"));
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }, TENANT_ID, TxCtxHolder.txId(), TxCtxHolder.get().getFlowName());

    assertThat(tenantAvailableApFirmwareRepository.findByTenant(new Tenant("9520ee78bb1222ec84220242ac120002")))
        .extracting(taaf -> taaf.getApVersion().getId())
        .hasSize(3)
        .containsExactlyInAnyOrder("*********.200", "*********.188", "*********.123");

    ArgumentCaptor<ApModelFirmwareSchedules> captor = ArgumentCaptor.forClass(ApModelFirmwareSchedules.class);
    verify(apUpgradeService, times(1)).updateNowByApModel(any(), eq("1dec899ad64e47649981797a26ab5668"),
        captor.capture());
    assertThat(captor.getValue())
        .extracting(ApModelFirmwareSchedules::getTargetFirmwares)
        .asList()
        .hasSize(2)
        .extracting(e -> ((ApModelFirmware) e).getApModel(), e -> ((ApModelFirmware) e).getFirmware())
        .containsExactlyInAnyOrder(tuple("R770", "*********.200"), tuple("R550", "*********.200"));

    verify(apUpgradeService, times(1)).updateNowByApModel(any(), eq("4dec899ad64e47649981797a26ab5668"),
        captor.capture());
    assertThat(captor.getValue())
        .extracting(ApModelFirmwareSchedules::getTargetFirmwares)
        .asList()
        .hasSize(1)
        .extracting(e -> ((ApModelFirmware) e).getApModel(), e -> ((ApModelFirmware) e).getFirmware())
        .containsExactlyInAnyOrder(tuple("R550", "*********.200"));

    assertThat(venueApModelSpecificAttributesRepository.findByTenantId("9520ee78bb1222ec84220242ac120002"))
        .isNotEmpty()
        .extracting(attr -> attr.getVenue().getId(), VenueApModelSpecificAttributes::getModel)
        .containsExactlyInAnyOrder(tuple("1dec899ad64e47649981797a26ab5668", "R550"));
  }

  @Test
  @Sql(statements = """
  INSERT INTO ap_version(id, labels, category) VALUES
    ('*********.230', 'ga', 'RECOMMENDED'),
    ('*********.231', 'beta,alpha' , 'RECOMMENDED'),
    ('*********.234', 'ga,legacyBeta', 'RECOMMENDED'),
    ('*********.239', null, 'RECOMMENDED'),
    ('*********.240', '', 'RECOMMENDED');
  """)
  void testUpdateApVersionLabels() {
    unit.updateApVersionLabels("*********.230", List.of(ApVersionLabelEnum.ALPHA));

    assertThat(apVersionRepository.findById("*********.230")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.ALPHA);
    assertThat(apVersionRepository.findById("*********.231")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.LEGACY_ALPHA, ApVersionLabelEnum.BETA);
    assertThat(apVersionRepository.findById("*********.234")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.GA, ApVersionLabelEnum.LEGACY_BETA);
    assertThat(apVersionRepository.findById("*********.239")).isPresent().get()
        .extracting(ApVersion::getLabels).isNull();
    assertThat(apVersionRepository.findById("*********.240")).isPresent().get()
        .extracting(ApVersion::getLabels).isNull();
  }

  @Test
  @Sql(statements = """
  INSERT INTO ap_version(id, labels, category) VALUES
    ('*********.230', 'ga', 'RECOMMENDED'),
    ('*********.231', 'beta,alpha' , 'RECOMMENDED'),
    ('*********.233', 'legacyAlpha', 'RECOMMENDED'),
    ('*********.234', 'ga,legacyBeta', 'RECOMMENDED');
  """)
  void testUpdateApVersionLabels_setToBetaAndAlpha() {
    unit.updateApVersionLabels("*********.230", List.of(ApVersionLabelEnum.ALPHA, ApVersionLabelEnum.BETA));

    assertThat(apVersionRepository.findById("*********.230")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.ALPHA, ApVersionLabelEnum.BETA);
    assertThat(apVersionRepository.findById("*********.231")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.LEGACY_ALPHA, ApVersionLabelEnum.LEGACY_BETA);
    assertThat(apVersionRepository.findById("*********.233")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.LEGACY_ALPHA);
    assertThat(apVersionRepository.findById("*********.234")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.GA, ApVersionLabelEnum.LEGACY_BETA);
  }

  @Test
  @Sql(statements = """
  INSERT INTO ap_version(id, labels, category) VALUES
    ('*********.230', 'ga', 'RECOMMENDED'),
    ('*********.231', 'beta,alpha' , 'RECOMMENDED'),
    ('*********.233', 'legacyAlpha', 'RECOMMENDED'),
    ('*********.234', 'ga,legacyBeta', 'RECOMMENDED');
  """)
  void testUpdateApVersionLabels_setToNull() {
    unit.updateApVersionLabels("*********.230", null);

    assertThat(apVersionRepository.findById("*********.230")).isPresent().get()
        .extracting(ApVersion::getLabels)
        .satisfies(labels -> assertTrue(labels == null || labels.isEmpty()));
    assertThat(apVersionRepository.findById("*********.231")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.ALPHA, ApVersionLabelEnum.BETA);
    assertThat(apVersionRepository.findById("*********.233")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.LEGACY_ALPHA);
    assertThat(apVersionRepository.findById("*********.234")).isPresent().get()
        .extracting(ApVersion::getLabels).asList()
        .containsExactlyInAnyOrder(ApVersionLabelEnum.GA, ApVersionLabelEnum.LEGACY_BETA);
  }

  @SafeVarargs
  private void assertSupportedApModels(List<ApFirmwareSupportModels> result, List<String>... expected) {
    List<List<String>> actual = result.stream().map(ApFirmwareSupportModels::getSupportedApModels).collect(Collectors.toList());
    assertThat(actual).containsExactly(expected);
  }

  private void assertOrdered(List<ApFirmwareSupportModels> result, String... expected) {
    List<String> actual = result.stream().map(ApFirmwareSupportModels::getId).collect(Collectors.toList());
    assertThat(actual).containsExactly(expected);
  }

  @TestConfiguration
  @Import({CloudSupportApModels.class, ExtendedVenueServiceCtrlImplTestConfig.class})
  static class TestConfig {
    @Bean
    @ConditionalOnMissingBean
    public VenueClient mockVenueClient() {
      return mock(VenueClient.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public VenueMerge venueMerge() {
      return new VenueMergeImpl();
    }

    @Bean
    @ConditionalOnMissingBean
    public ApUpgradeService apUpgradeService() {
      return mock(ApUpgradeService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public FirmwareManagementService FirmwareManagementService(
        TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository,
        VenueRepository venueRepository,
        VenueCurrentFirmwareRepository venueCurrentFirmwareRepository,
        UpgradeScheduleRepository upgradeScheduleRepository,
        ApRepository apRepository,
        ApVersionRepository apVersionRepository,
        ApModelMinimumFirmwareRepository apModelMinimumFirmwareRepository,
        VenueClient venueClient,
        FeatureFlagService featureFlagService,
        CloudSupportApModels cloudSupportApModels,
        UpgradeScheduleFirmwareVersionRepository upgradeScheduleFirmwareVersionRepository,
        ApModelGreenfieldFirmwareRepository apModelGreenfieldFirmwareRepository,
        TenantService tenantService,
        TenantAvailableApFirmwareService tenantAvailableApFirmwareService,
        TenantCurrentFirmwareService tenantCurrentFirmwareService,
        WifiSchedulePublisher wifiSchedulePublisher,
        TenantFirmwareNotificationSender tenantFirmwareNotificationSender,
        ExtendedVenueServiceCtrl extendedVenueServiceCtrl,
        ApUpgradeService apUpgradeService,
        ScopeDataService scopeDataService) {
      return new FirmwareManagementServiceImpl(tenantAvailableApFirmwareRepository, venueRepository,
          venueCurrentFirmwareRepository, upgradeScheduleRepository, apRepository, apVersionRepository,
          apModelMinimumFirmwareRepository, venueClient, featureFlagService, cloudSupportApModels,
          upgradeScheduleFirmwareVersionRepository, apModelGreenfieldFirmwareRepository,
          tenantService, tenantAvailableApFirmwareService, tenantCurrentFirmwareService, wifiSchedulePublisher,
          tenantFirmwareNotificationSender, extendedVenueServiceCtrl, apUpgradeService, scopeDataService);
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantService tenantService(TenantRepository tenantRepository) {
      return new TenantServiceImpl(tenantRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantAvailableApFirmwareService tenantAvailableApFirmwareService(
        TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository) {
      return new TenantAvailableApFirmwareServiceImpl(tenantAvailableApFirmwareRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantCurrentFirmwareService tenantCurrentFirmwareService(
        TenantCurrentFirmwareRepository tenantCurrentFirmwareRepository,
        ApModelGreenfieldFirmwareRepository apModelGreenfieldFirmwareRepository,
        TenantAvailableApFirmwareRepository tenantAvailableApFirmwareRepository,
        ApVersionRepository apVersionRepository) {
      return new TenantCurrentFirmwareServiceImpl(tenantCurrentFirmwareRepository,
          apModelGreenfieldFirmwareRepository, tenantAvailableApFirmwareRepository, apVersionRepository);
    }
  }
}
