package com.ruckus.cloud.wifi.service.integration;

import static com.ruckus.cloud.wifi.eda.test.valgen.Generators.always;
import static com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.network;
import static com.ruckus.cloud.wifi.featureflag.FlagNames.WIFI_CAPTIVE_PORTAL_OWE_TRANSITION;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomId;
import static com.ruckus.cloud.wifi.test.fixture.CommonTestFixture.randomTxId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.ruckus.cloud.wifi.client.identity.IdentityClient;
import com.ruckus.cloud.wifi.client.identity.IdentityDto;
import com.ruckus.cloud.wifi.eda.servicemodel.AAANetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.GuestNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Hotspot20Network;
import com.ruckus.cloud.wifi.eda.servicemodel.Network;
import com.ruckus.cloud.wifi.eda.servicemodel.PskNetwork;
import com.ruckus.cloud.wifi.eda.servicemodel.Tenant;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.GuestNetworkTypeEnum;
import com.ruckus.cloud.wifi.eda.servicemodel.enums.WlanSecurityEnum;
import com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators;
import com.ruckus.cloud.wifi.service.core.exception.CommonException;
import com.ruckus.cloud.wifi.service.core.exception.ObjectNotFoundException;
import com.ruckus.cloud.wifi.service.impl.IdentityProfileServiceCtrlImpl;
import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import com.ruckus.cloud.wifi.test.extension.TxCtxExtension;
import com.ruckus.cloud.wifi.test.extension.annotation.decorator.FeatureFlag;
import com.ruckus.cloud.wifi.test.util.RepositoryUtil;
import com.ruckus.cloud.wifi.test.util.TestContextHelper;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@WifiJpaDataTest
class IdentityProfileServiceTest {

  @RegisterExtension
  private final TxCtxExtension txCtxExtension = TxCtxExtension.withRandomTenantAndRequestId();

  @SpyBean private IdentityProfileServiceCtrlImpl identityProfileServiceCtrl;
  @Autowired private RepositoryUtil repositoryUtil;
  @Autowired private TestContextHelper testContextHelper;
  @MockBean private IdentityClient identityClient;

  private AAANetwork aaaNetwork;
  private PskNetwork pskNetwork;
  private Hotspot20Network hotspot20Network;
  private String identityGroupId;
  private String identityId;

  @BeforeEach
  void setUp(Tenant tenant) {
    identityGroupId = "identityGroup01";
    identityId = "identity01";
    var radiusId = "radiusTestId";
    var radiusAuth = Generators.radiusAuth().generate();
    radiusAuth.setId(radiusId);
    repositoryUtil.createOrUpdate(radiusAuth);

    aaaNetwork = network(AAANetwork.class).generate();
    aaaNetwork.setAuthRadius(radiusAuth);
    aaaNetwork.setName("aaaNetwork");
    aaaNetwork.setTenant(tenant);
    aaaNetwork.getWlan().setNetwork(aaaNetwork);
    repositoryUtil.createOrUpdate(aaaNetwork);

    pskNetwork = network(PskNetwork.class).generate();
    pskNetwork.setName("pskNetwork");
    pskNetwork.setTenant(tenant);
    pskNetwork.getWlan().setNetwork(pskNetwork);
    repositoryUtil.createOrUpdate(pskNetwork);

    hotspot20Network = network(Hotspot20Network.class).generate();
    hotspot20Network.setName("hotsport20Network");
    hotspot20Network.setTenant(tenant);
    hotspot20Network.getWlan().setNetwork(hotspot20Network);
    repositoryUtil.createOrUpdate(hotspot20Network);
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateIdentityGroupOnWifiNetwork(Tenant tenant) throws Exception {
    // Given
    aaaNetwork.setIdentityGroupId(identityGroupId);
    repositoryUtil.createOrUpdate(aaaNetwork);

    // When
    var mockIdentity = new IdentityDto();
    mockIdentity.setId(identityGroupId);
    when(identityClient.getIdentityGroup(anyString(), anyString(), anyBoolean()))
        .thenReturn(Optional.of(mockIdentity));
    identityProfileServiceCtrl.activateIdentityGroupOnWifiNetwork(
        aaaNetwork.getId(), identityGroupId);

    // Then
    testContextHelper.executeInNewTransaction(
        () -> {
          var savedNetwork = (AAANetwork) repositoryUtil.find(Network.class, aaaNetwork.getId());
          assertThat(savedNetwork)
              .isNotNull()
              .extracting(AAANetwork::getIdentityGroupId, AAANetwork::getIdentityId)
              .containsExactlyInAnyOrder(identityGroupId, null);
        },
        tenant.getId(),
        randomTxId());
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateIdentityGroupOnWifiNetworkThrowException(Tenant tenant) throws Exception {
    // Given
    aaaNetwork.setIdentityGroupId(identityGroupId);
    repositoryUtil.createOrUpdate(aaaNetwork);

    // When
    when(identityClient.getIdentityGroup(anyString(), anyString(), anyBoolean()))
        .thenReturn(Optional.empty());

    // Then
    assertThrows(
        ObjectNotFoundException.class,
        () ->
            identityProfileServiceCtrl.activateIdentityGroupOnWifiNetwork(
                aaaNetwork.getId(), identityGroupId));
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateIdentityGroupOnWifiAAANetworkWithCertificateTemplateThrowException(Tenant tenant)
      throws Exception {
    // Given
    aaaNetwork.setIdentityGroupId(identityGroupId);
    aaaNetwork.setUseCertificateTemplate(true);
    aaaNetwork.getWlan().setCertificateTemplateId("certificateTemplateId");
    repositoryUtil.createOrUpdate(aaaNetwork);

    // When
    var mockIdentity = new IdentityDto();
    mockIdentity.setId(identityGroupId);
    when(identityClient.getIdentityGroup(anyString(), anyString(), anyBoolean()))
        .thenReturn(Optional.of(mockIdentity));

    // Then
    assertThrows(
        IllegalArgumentException.class,
        () ->
            identityProfileServiceCtrl.activateIdentityGroupOnWifiNetwork(
                aaaNetwork.getId(), identityGroupId));
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateIdentityOnWifiNetwork(Tenant tenant) throws Exception {
    // Given
    pskNetwork.setIdentityGroupId(identityGroupId);
    pskNetwork.setIdentityId(identityId);
    repositoryUtil.createOrUpdate(pskNetwork);

    // When
    var mockIdentity = new IdentityDto();
    mockIdentity.setId(identityId);
    when(identityClient.getIdentity(anyString(), anyString(), anyString()))
        .thenReturn(Optional.of(mockIdentity));
    identityProfileServiceCtrl.activateIdentityOnWifiNetwork(
        pskNetwork.getId(), identityGroupId, identityId);

    // Then
    testContextHelper.executeInNewTransaction(
        () -> {
          var savedNetwork = (PskNetwork) repositoryUtil.find(Network.class, pskNetwork.getId());
          assertThat(savedNetwork)
              .isNotNull()
              .extracting(PskNetwork::getIdentityGroupId, PskNetwork::getIdentityId)
              .containsExactlyInAnyOrder(identityGroupId, identityId);
        },
        tenant.getId(),
        randomTxId());
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateIdentityOnWifiNetworkError() throws Exception {
    // Given
    aaaNetwork.setIdentityGroupId(identityGroupId);
    aaaNetwork.setIdentityId(identityId);
    repositoryUtil.createOrUpdate(aaaNetwork);

    // When
    when(identityClient.getIdentity(anyString(), anyString(), anyString()))
        .thenReturn(Optional.empty());

    // Then
    assertThrows(
        ObjectNotFoundException.class,
        () ->
            identityProfileServiceCtrl.activateIdentityOnWifiNetwork(
                aaaNetwork.getId(), identityGroupId, identityId));
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateIdentityOnWifiPskNetworkWithMacRegistrationListError() throws Exception {
    // Given
    pskNetwork.setIdentityGroupId(identityGroupId);
    pskNetwork.setIdentityId(identityId);
    pskNetwork.getWlan().setMacAddressAuthentication(true);
    pskNetwork.getWlan().setMacRegistrationListId("macRegistrationListId");
    repositoryUtil.createOrUpdate(pskNetwork);

    // When
    var mockIdentity = new IdentityDto();
    mockIdentity.setId(identityId);
    when(identityClient.getIdentity(anyString(), anyString(), anyString()))
        .thenReturn(Optional.of(mockIdentity));

    // Then
    assertThrows(
        IllegalArgumentException.class,
        () ->
            identityProfileServiceCtrl.activateIdentityOnWifiNetwork(
                pskNetwork.getId(), identityGroupId, identityId));
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateIdentityOnWifiAAANetworkThrowException() throws Exception {
    // Given
    aaaNetwork.setIdentityGroupId(identityGroupId);
    aaaNetwork.setIdentityId(identityId);
    repositoryUtil.createOrUpdate(aaaNetwork);

    // When
    var mockIdentity = new IdentityDto();
    mockIdentity.setId(identityId);
    when(identityClient.getIdentity(anyString(), anyString(), anyString()))
        .thenReturn(Optional.of(mockIdentity));

    // Then
    assertThrows(
        IllegalArgumentException.class,
        () ->
            identityProfileServiceCtrl.activateIdentityOnWifiNetwork(
                aaaNetwork.getId(), identityGroupId, identityId));
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testActivateIdentityOnWifiHotSpot20NetworkThrowException() throws Exception {
    // Given
    hotspot20Network.setIdentityGroupId(identityGroupId);
    hotspot20Network.setIdentityId(identityId);
    hotspot20Network.getWlan().setCertificateTemplateId("certificateTemplateId");
    repositoryUtil.createOrUpdate(hotspot20Network);

    // When
    var mockIdentity = new IdentityDto();
    mockIdentity.setId(identityId);
    when(identityClient.getIdentity(anyString(), anyString(), anyString()))
        .thenReturn(Optional.of(mockIdentity));

    // Then
    assertThrows(
        IllegalArgumentException.class,
        () ->
            identityProfileServiceCtrl.activateIdentityOnWifiNetwork(
                hotspot20Network.getId(), identityGroupId, identityId));
  }

  @Nested
  @FeatureFlag(enable = WIFI_CAPTIVE_PORTAL_OWE_TRANSITION)
  class TestActivateOnWifiGuestOweTrNetwork {

    GuestNetwork primaryGuestNetworkFromDb = Generators.network(GuestNetwork.class).generate();
    GuestNetwork secondaryGuestNetworkFromDb = Generators.network(GuestNetwork.class).generate();

    @BeforeEach
    void setup(Tenant tenant) {
      identityGroupId = "identityGroupId01";
      identityId = "identity01";
      String workflowProfileId = "workflowProfile01";
      String masterSsid = "oweMasterNameSsid-" + randomId().substring(1, 6);

      primaryGuestNetworkFromDb.setTenant(tenant);
      primaryGuestNetworkFromDb.setName(masterSsid);
      primaryGuestNetworkFromDb.getWlan().setNetwork(primaryGuestNetworkFromDb);
      primaryGuestNetworkFromDb.getWlan().setSsid(masterSsid);
      primaryGuestNetworkFromDb.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      primaryGuestNetworkFromDb.setIsOweMaster(true);
      primaryGuestNetworkFromDb.setOwePairNetworkId(secondaryGuestNetworkFromDb.getId());
      primaryGuestNetworkFromDb.setWorkflowProfileId(workflowProfileId);
      primaryGuestNetworkFromDb.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Workflow)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      primaryGuestNetworkFromDb.getGuestPortal().setNetwork(primaryGuestNetworkFromDb);
      primaryGuestNetworkFromDb =
          repositoryUtil.createOrUpdate(
              primaryGuestNetworkFromDb,
              primaryGuestNetworkFromDb.getTenant().getId(),
              randomTxId());

      secondaryGuestNetworkFromDb.setTenant(tenant);
      secondaryGuestNetworkFromDb.setName(masterSsid + "-owe-tr");
      secondaryGuestNetworkFromDb.getWlan().setNetwork(secondaryGuestNetworkFromDb);
      secondaryGuestNetworkFromDb.getWlan().setSsid(masterSsid + "-owe-tr");
      secondaryGuestNetworkFromDb.getWlan().setWlanSecurity(WlanSecurityEnum.OWETransition);
      secondaryGuestNetworkFromDb.setOwePairNetworkId(primaryGuestNetworkFromDb.getId());
      secondaryGuestNetworkFromDb.setWorkflowProfileId(workflowProfileId);
      secondaryGuestNetworkFromDb.setIsOweMaster(false);
      secondaryGuestNetworkFromDb.setGuestPortal(
          com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.guestPortal(
                  GuestNetworkTypeEnum.Workflow)
              .setWisprPage(
                  com.ruckus.cloud.wifi.eda.test.valgen.servicemodel.Generators.wisprPage()
                      .setCustomExternalProvider(always(false)))
              .generate());
      secondaryGuestNetworkFromDb.getGuestPortal().setNetwork(secondaryGuestNetworkFromDb);
      repositoryUtil.createOrUpdate(
          secondaryGuestNetworkFromDb,
          secondaryGuestNetworkFromDb.getTenant().getId(),
          randomTxId());
    }

    @Test
    void activateIdentityGroupSuccess() throws CommonException {
      var mockIdentity = new IdentityDto();
      mockIdentity.setId(identityGroupId);
      when(identityClient.getIdentityGroup(anyString(), anyString(), anyBoolean()))
          .thenReturn(Optional.of(mockIdentity));

      assertThatNoException()
          .isThrownBy(
              () ->
                  identityProfileServiceCtrl.activateIdentityGroupOnWifiNetwork(
                      primaryGuestNetworkFromDb.getId(), identityGroupId));
      var savedNetwork1 =
          (GuestNetwork) repositoryUtil.find(Network.class, primaryGuestNetworkFromDb.getId());
      assertThat(savedNetwork1)
          .isNotNull()
          .extracting(GuestNetwork::getIdentityGroupId, GuestNetwork::getIdentityId)
          .containsExactlyInAnyOrder(identityGroupId, null);
      var savedNetwork2 =
          (GuestNetwork) repositoryUtil.find(Network.class, secondaryGuestNetworkFromDb.getId());
      assertThat(savedNetwork2)
          .isNotNull()
          .extracting(GuestNetwork::getIdentityGroupId, GuestNetwork::getIdentityId)
          .containsExactlyInAnyOrder(identityGroupId, null);
    }

    @Test
    void activateIdentitySuccess() throws CommonException {
      var mockIdentityGroup = new IdentityDto();
      mockIdentityGroup.setId(identityGroupId);
      when(identityClient.getIdentityGroup(anyString(), anyString(), anyBoolean()))
          .thenReturn(Optional.of(mockIdentityGroup));

      var mockIdentity = new IdentityDto();
      mockIdentity.setId(identityId);
      when(identityClient.getIdentity(anyString(), anyString(), anyString()))
          .thenReturn(Optional.of(mockIdentity));

      assertThatNoException()
          .isThrownBy(
              () ->
                  identityProfileServiceCtrl.activateIdentityOnWifiNetwork(
                      primaryGuestNetworkFromDb.getId(), identityGroupId, identityId));
      var savedNetwork1 =
          (GuestNetwork) repositoryUtil.find(Network.class, primaryGuestNetworkFromDb.getId());
      assertThat(savedNetwork1)
          .isNotNull()
          .extracting(GuestNetwork::getIdentityGroupId, GuestNetwork::getIdentityId)
          .containsExactlyInAnyOrder(identityGroupId, identityId);
      var savedNetwork2 =
          (GuestNetwork) repositoryUtil.find(Network.class, secondaryGuestNetworkFromDb.getId());
      assertThat(savedNetwork2)
          .isNotNull()
          .extracting(GuestNetwork::getIdentityGroupId, GuestNetwork::getIdentityId)
          .containsExactlyInAnyOrder(identityGroupId, identityId);
    }
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testDeactivateIdentityGroupOnWifiNetwork(Tenant tenant) throws Exception {
    // Given
    aaaNetwork.setIdentityGroupId(identityGroupId);
    repositoryUtil.createOrUpdate(aaaNetwork);

    // When
    var mockIdentity = new IdentityDto();
    mockIdentity.setId(identityGroupId);
    when(identityClient.getIdentityGroup(anyString(), anyString(), anyBoolean()))
            .thenReturn(Optional.of(mockIdentity));
    identityProfileServiceCtrl.deactivateIdentityGroupOnWifiNetwork(aaaNetwork.getId(),
            identityGroupId);

    // Then
    testContextHelper.executeInNewTransaction(() -> {
      var savedNetwork = (AAANetwork) repositoryUtil.find(Network.class, aaaNetwork.getId());
      assertThat(savedNetwork)
              .isNotNull()
              .extracting(AAANetwork::getIdentityGroupId, AAANetwork::getIdentityId)
              .containsExactlyInAnyOrder(null, null);
    }, tenant.getId(), randomTxId());
  }

  @Test
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  void testDeactivateIdentityOnWifiNetworkError() throws Exception {
    // Given
    aaaNetwork.setIdentityGroupId(identityGroupId);
    aaaNetwork.setIdentityId(identityId);
    repositoryUtil.createOrUpdate(aaaNetwork);

    // When
    when(identityClient.getIdentity(anyString(), anyString(), anyString()))
            .thenReturn(Optional.empty());

    // Then
    assertThrows(
            ObjectNotFoundException.class,
            () ->  identityProfileServiceCtrl.deactivateIdentityGroupOnWifiNetwork(aaaNetwork.getId(),
                    identityGroupId));
  }
}
