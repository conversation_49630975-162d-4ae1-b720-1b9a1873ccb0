package com.ruckus.cloud.wifi.repository;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ruckus.cloud.wifi.test.WifiJpaDataTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@WifiJpaDataTest
@Sql(statements = """
    INSERT INTO tenant (id) VALUES ('4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO venue (id, tenant)
        VALUES ('e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap_group (id,
            venue, tenant)
        VALUES ('818552afabf544878057e510b9bb88b5',
            'e465bac6afb747a4987d0d0945f77221', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap (id, ap_group,
            floorplan_id, tenant)
        VALUES ('900000005015', '818552afabf544878057e510b9bb88b5',
            '5e180d7fd19d4dc1a6a768aa2255a23d', '4c8279f79307415fa9e4c88a1819f0fc');
    INSERT INTO ap_picture (id,
            image_id, image_name,
            tenant, ap)
        VALUES ('4837aebe7fa3475a9b84e4a4115055f2',
            '0fbfa1e15ce74e54b9f58d231d2d49dd-001.jpg', 'placeholder.jpg',
            '4c8279f79307415fa9e4c88a1819f0fc', '900000005015');
    """)
public class ApPictureRepositoryTest {

  private static final String TENANT_ID = "4c8279f79307415fa9e4c88a1819f0fc";

  private static final String AP_ID = "900000005015";

  @Autowired
  private ApPictureRepository apPictureRepository;

  @Test
  void findByApIdAndTenantIdTest() {
    var result = apPictureRepository.findByApIdAndTenantId(AP_ID, TENANT_ID);
    assertTrue(result.isPresent());
  }
}
