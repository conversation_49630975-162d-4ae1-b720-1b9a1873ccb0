<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <artifactId>wifi-eda-service</artifactId>
  <name>wifi-eda-service</name>
  <version>${revision}</version>
  <packaging>jar</packaging>

  <parent>
    <groupId>com.ruckus.cloud.wifi</groupId>
    <artifactId>wifi-eda-parent</artifactId>
    <version>1.7.0-SNAPSHOT</version>
    <relativePath>../wifi-eda-parent</relativePath>
  </parent>

  <modelVersion>4.0.0</modelVersion>

  <properties>
    <testcontainers.version>1.18.0</testcontainers.version>
    <ruckus-testcontainers.version>0.0.3</ruckus-testcontainers.version>
    <dummy-junit4.version>0.0.1</dummy-junit4.version>
    <jib-maven-plugin.version>3.4.4</jib-maven-plugin.version>
    <sonar.coverage.exclusions>**/dto/*,**/featureflag/FlagNames.java</sonar.coverage.exclusions>
    <argLine></argLine>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers-bom</artifactId>
        <version>${testcontainers.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>com.ruckus.cloud</groupId>
        <artifactId>testcontainers</artifactId>
        <version>${ruckus-testcontainers.version}</version>
      </dependency>

      <dependency>
        <groupId>com.ruckus.cloud</groupId>
        <artifactId>dummy-junit4</artifactId>
        <version>${dummy-junit4.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>

    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud</groupId>
      <artifactId>vault-dynamic-loader-jdbc</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud</groupId>
      <artifactId>configmap-refresh</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-notification</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-lib-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-revision</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-kafka</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-activity</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>javax.servlet-api</artifactId>
          <groupId>javax.servlet</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-impact</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-metric</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-pubsub</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-api-acx-codegen</artifactId>
      <classifier>wifi-cfg-request</classifier>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-r1-spec</artifactId>
      <classifier>wifi-eda-service</classifier>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-service-rks-db-tool</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-api-acx-codegen</artifactId>
      <classifier>wifi-eda-service</classifier>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-proto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-proto-mapper</artifactId>
      <classifier>wifi-eda-mapper</classifier>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-proto-view-mapper</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-grpc</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-redis</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.file</groupId>
      <artifactId>file-proto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.tenant</groupId>
      <artifactId>tenant-proto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>venue-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>nuketenant-protobuf-message</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>mspservice-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>device-lifecycle-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>device-firmware-operator-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>websocket-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx.ddccm</groupId>
      <artifactId>ddccm-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>viewmodel-collector-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.entitlement</groupId>
      <artifactId>entitlement-proto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.notification</groupId>
      <artifactId>notification-proto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>device-notification-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>activity-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>franz-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud</groupId>
      <artifactId>feature-flag-client-spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.drs</groupId>
      <artifactId>drs-broker-proto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.viewmodel</groupId>
      <artifactId>viewmodel-proto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.mspservice</groupId>
      <artifactId>mspservice-proto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>aggregate-notifiction-protobuf-message</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx.edge.service</groupId>
      <artifactId>edge-service-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx.tenant-ca</groupId>
      <artifactId>tenant-ca-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.certificatetemplate</groupId>
      <artifactId>certificate-template-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx.wifi.service</groupId>
      <artifactId>wifi-service-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.guest</groupId>
      <artifactId>guest-proto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>external-auth-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>entity-association-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud</groupId>
      <artifactId>compatibility-contract</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-csv</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-all</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>com.vaadin.external.google</groupId>
          <artifactId>android-json</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.asarkar.grpc</groupId>
      <artifactId>grpc-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>postgresql</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>kafka</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud</groupId>
      <artifactId>testcontainers</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud</groupId>
      <artifactId>dummy-junit4</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.wifi</groupId>
      <artifactId>wifi-eda-test-utils</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk18on</artifactId>
    </dependency>

    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk18on</artifactId>
      <version>${bcprov.version}</version>
    </dependency>

    <dependency>
      <groupId>net.jradius</groupId>
      <artifactId>jradius-core</artifactId>
    </dependency>

    <dependency>
      <groupId>net.jradius</groupId>
      <artifactId>jradius-dictionary</artifactId>
    </dependency>

    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
    </dependency>

    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
    </dependency>

    <dependency>
      <groupId>jakarta.xml.bind</groupId>
      <artifactId>jakarta.xml.bind-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx</groupId>
      <artifactId>events-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ruckus.cloud.common.service</groupId>
      <artifactId>service-post-migration</artifactId>
    </dependency>

    <dependency>
      <groupId>org.awaitility</groupId>
      <artifactId>awaitility</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.google.maps</groupId>
      <artifactId>google-maps-services</artifactId>
      <version>${google-maps-services.version}</version>
    </dependency>

    <dependency>
      <groupId>com.ruckus.acx.hubbiot</groupId>
      <artifactId>hubbiot-protobuf</artifactId>
    </dependency>

    <dependency>
      <groupId>org.json</groupId>
      <artifactId>json</artifactId>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <annotationProcessorPaths>
            <annotationProcessorPath>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${mapstruct.version}</version>
            </annotationProcessorPath>
            <annotationProcessorPath>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </annotationProcessorPath>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <excludedGroups>${testExcludedGroups}</excludedGroups>
          <properties>
            <configurationParameters>
              junit.platform.reporting.open.xml.enabled = true
              junit.platform.reporting.output.dir = target/surefire-reports
            </configurationParameters>
          </properties>
          <argLine>@{argLine} -Xmx4096m -Dspring.test.context.cache.maxSize=4</argLine>
          <reuseForks>true</reuseForks>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.basepom.maven</groupId>
        <artifactId>duplicate-finder-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <printEqualFiles>true</printEqualFiles>
          <failBuildInCaseOfDifferentContentConflict>true</failBuildInCaseOfDifferentContentConflict>
          <failBuildInCaseOfEqualContentConflict>true</failBuildInCaseOfEqualContentConflict>
          <failBuildInCaseOfConflict>true</failBuildInCaseOfConflict>
          <checkCompileClasspath>true</checkCompileClasspath>
          <checkRuntimeClasspath>true</checkRuntimeClasspath>
          <checkTestClasspath>false</checkTestClasspath>
          <ignoredDependencies>
            <dependency>
              <groupId>com.ruckus.acx</groupId>
              <artifactId>events-protobuf</artifactId>
            </dependency>
            <dependency>
              <groupId>com.ruckus.acx</groupId>
              <artifactId>viewmodel-collector-protobuf</artifactId>
            </dependency>
          </ignoredDependencies>
          <ignoredClassPatterns>
            <ignoredClassPattern>javax.*$</ignoredClassPattern>
          </ignoredClassPatterns>
          <ignoredResourcePatterns>
            <ignoredResourcePattern>.*\.txt$</ignoredResourcePattern>
            <ignoredResourcePattern>.*common\.proto$</ignoredResourcePattern>
          </ignoredResourcePatterns>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>ci</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.google.cloud.tools</groupId>
            <artifactId>jib-maven-plugin</artifactId>
            <version>${jib-maven-plugin.version}</version>
            <configuration>
              <from>
                <image>${base.image.jre17-3.21.rks}</image>
              </from>
              <to>
                <image>${gar.parent.url}/wifi-eda:SNAPSHOT</image>
              </to>
              <container>
                <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
              </container>
            </configuration>
            <executions>
              <execution>
                <id>default-package</id>
                <phase>package</phase>
                <goals>
                  <goal>dockerBuild</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>io.github.zlika</groupId>
            <artifactId>reproducible-build-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>strip-jar</id>
                <phase>package</phase>
                <goals>
                  <goal>strip-jar</goal>
                </goals>
              </execution>
              <execution>
                <id>strip-archive</id>
                <phase>pre-integration-test</phase>
                <goals>
                  <goal>strip-jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
