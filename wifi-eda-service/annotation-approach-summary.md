# Annotation + Consumer搬運方案

## 核心想法
使用現有的 `@AllowTemplateOperationOnly(isRecTemplate = true)` annotation，在 CfgRequestConsumer 中讀取並設定正確的 templateContext。

## 實現步驟

### 1. REC Handler 使用 Annotation
```java
@Component
@Slf4j
@AllowTemplateOperationOnly(isRecTemplate = true)  // 標記為REC template
public class AddWifiNetworkRecTemplateHandler extends AddWifiNetworkTemplateHandler {
    // 不需要任何額外程式碼，只需要annotation
}
```

### 2. Consumer 中途搬運
```java
if (isTemplateHandler(handler.getClass())) {
    log.info("Processing a template handler, flag template API flow");
    flagTemplateApiFlowInContext();
    
    // 讀取annotation並設定對應的templateContext
    AllowTemplateOperationOnly annotation = AnnotationUtils.findAnnotation(handler.getClass(), AllowTemplateOperationOnly.class);
    if (annotation != null && annotation.isRecTemplate()) {
        // REC template handler
        TemplateUtils.setTemplateContext(TemplateUtils.TemplateContext.REC.name());
        log.info("Set REC template context for handler: {}", handler.getClass().getSimpleName());
    } else {
        // MSP template handler (包括沒有annotation的舊handlers)
        TemplateUtils.setTemplateContext(TemplateUtils.TemplateContext.MSP.name());
        log.info("Set MSP template context for handler: {}", handler.getClass().getSimpleName());
    }
}
```

## 優勢

1. **最小侵入**: 只需要在REC handlers上加annotation
2. **零影響MSP**: 所有現有MSP handlers完全不需要修改
3. **簡潔明確**: annotation清晰標明handler類型
4. **易於擴展**: 未來CRUD操作只需要複製annotation

## 未來CRUD擴展

```java
// Update REC Template
@AllowTemplateOperationOnly(isRecTemplate = true)
public class UpdateWifiNetworkRecTemplateHandler extends UpdateWifiNetworkTemplateHandler {
}

// Delete REC Template  
@AllowTemplateOperationOnly(isRecTemplate = true)
public class DeleteWifiNetworkRecTemplateHandler extends DeleteWifiNetworkTemplateHandler {
}

// Get REC Template
@AllowTemplateOperationOnly(isRecTemplate = true) 
public class GetWifiNetworkRecTemplateHandler extends GetWifiNetworkTemplateHandler {
}
```

## 預期結果

- REC handlers: templateContext = "REC"
- MSP handlers (包括舊的): templateContext = "MSP"  
- Consumer日誌會清楚顯示設定的context類型

這個方案比interface方法更簡潔，同時完全保護現有MSP代碼。