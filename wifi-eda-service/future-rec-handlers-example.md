# Future REC Template Handlers 範例

## 完整的 CRUD 支援

```java
// 1. Update REC Template Handler
@Component
@Slf4j
public class UpdateWifiNetworkRecTemplateHandler extends UpdateWifiNetworkTemplateHandler 
    implements TemplateContextProvider {

    public UpdateWifiNetworkRecTemplateHandler(UpdateWifiNetworkHandler delegate) {
        super(delegate);
    }

    @Override
    public String apiAction() {
        return CfgAction.UPDATE_WIFI_NETWORK_REC_TEMPLATE.key();
    }

    @Override
    public String stepId() {
        return ApiFlowNames.UPDATE_WIFI_NETWORK_REC_TEMPLATE;
    }

    @Override
    public TemplateUtils.TemplateContext getTemplateContext() {
        return TemplateUtils.TemplateContext.REC;
    }
}

// 2. Delete REC Template Handler
@Component 
@Slf4j
public class DeleteWifiNetworkRecTemplateHandler extends DeleteWifiNetworkTemplateHand<PERSON>
    implements Template<PERSON>ontextProvider {

    public DeleteWifiNetworkRecTemplateHandler(DeleteWifiNetworkHandler delegate) {
        super(delegate);
    }

    @Override
    public String apiAction() {
        return CfgAction.DELETE_WIFI_NETWORK_REC_TEMPLATE.key();
    }

    @Override
    public String stepId() {
        return ApiFlowNames.DELETE_WIFI_NETWORK_REC_TEMPLATE;
    }

    @Override
    public TemplateUtils.TemplateContext getTemplateContext() {
        return TemplateUtils.TemplateContext.REC;
    }
}

// 3. Get REC Template Handler (if needed)
@Component
@Slf4j  
public class GetWifiNetworkRecTemplateHandler extends GetWifiNetworkTemplateHandler
    implements TemplateContextProvider {

    public GetWifiNetworkRecTemplateHandler(GetWifiNetworkHandler delegate) {
        super(delegate);
    }

    @Override
    public String apiAction() {
        return CfgAction.GET_WIFI_NETWORK_REC_TEMPLATE.key();
    }

    @Override
    public String stepId() {
        return ApiFlowNames.GET_WIFI_NETWORK_REC_TEMPLATE;
    }

    @Override
    public TemplateUtils.TemplateContext getTemplateContext() {
        return TemplateUtils.TemplateContext.REC;
    }
}
```

## 優勢

1. **零影響舊代碼**: 現有的MSP template handlers完全不需要修改
2. **清晰的模式**: 每個REC handler只需要實現TemplateContextProvider
3. **自動處理**: CfgRequestConsumer自動識別並設定正確的template context
4. **可擴展性**: 輕鬆支援新的template types和操作
5. **易於測試**: 可以獨立測試每個handler的template context

## 注意事項

- 所有現有的MSP template handlers會自動獲得MSP context，無需任何修改
- 只有新的REC handlers需要實現TemplateContextProvider介面
- Consumer會自動根據介面實現選擇正確的template context