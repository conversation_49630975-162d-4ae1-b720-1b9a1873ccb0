# AllowTemplateOperationOnly with REC Template Support

## 概述

`AllowTemplateOperationOnly` 註解現在支援一個新的參數 `isRecTemplate`，允許在模板操作期間同時啟用 REC (Record) 模板流程。

## 功能說明

### 新增參數

```java
@AllowTemplateOperationOnly(isRecTemplate = true)
```

- `isRecTemplate`: 布林值參數，預設為 `false`
- 當設為 `true` 時，會在模板操作期間同時啟用 `TemplateUtils.flagRecTemplateApiFlowInContext()`

### 使用方式

#### 1. 類別層級註解

```java
@Service
@AllowTemplateOperationOnly(isRecTemplate = true)
public class WifiNetworkRecTemplateServiceCtrlImpl implements WifiNetworkRecTemplateServiceCtrl {
    // 所有方法都會啟用 REC 模板流程
}
```

#### 2. 方法層級註解

```java
@Service
public class ExampleService {
    
    @AllowTemplateOperationOnly(isRecTemplate = true)
    public void methodWithRecTemplateFlow() {
        // 啟用 REC 模板流程
    }
    
    @AllowTemplateOperationOnly(isRecTemplate = false)
    public void methodWithoutRecTemplateFlow() {
        // 不啟用 REC 模板流程
    }
}
```

#### 3. 混合使用

```java
@Service
@AllowTemplateOperationOnly(isRecTemplate = true)  // 類別層級預設啟用
public class MixedTemplateService {
    
    public void methodWithClassLevelRecTemplate() {
        // 使用類別層級的 REC 模板設定
    }
    
    @AllowTemplateOperationOnly(isRecTemplate = false)  // 方法層級覆蓋
    public void methodWithDisabledRecTemplate() {
        // 明確停用 REC 模板流程
    }
}
```

## 技術實作

### AOP 切面邏輯

1. **檢查模板過濾器狀態**: 如果模板過濾器已停用，則跳過處理
2. **獲取註解參數**: 從方法或類別層級獲取 `isRecTemplate` 參數
3. **啟用模板流程**: 
   - 啟用一般模板流程 (`TemplateUtils.flagTemplateApiFlowInContext(true)`)
   - 如果 `isRecTemplate = true`，同時啟用 REC 模板流程
4. **執行方法**: 執行被註解的方法
5. **恢復狀態**: 在 finally 區塊中恢復原始狀態

### 狀態管理

- 使用 `TemplateUtils.flagRecTemplateApiFlowInContext()` 管理 REC 模板狀態
- 支援嵌套調用，正確恢復原始狀態
- 提供詳細的日誌記錄用於調試

## 測試範例

```java
@Test
void testMethodWithRecTemplateFlow() {
    // 測試啟用 REC 模板流程的方法
    exampleService.methodWithRecTemplateFlow();
    
    // 驗證 REC 模板流程被正確啟用和恢復
    verify(TemplateUtils.flagRecTemplateApiFlowInContext(true), times(1));
    verify(TemplateUtils.flagRecTemplateApiFlowInContext(false), times(1));
}
```

## 注意事項

1. **向後兼容**: 預設值為 `false`，確保現有程式碼不受影響
2. **性能考量**: REC 模板流程會增加額外的狀態管理開銷
3. **調試支援**: 提供詳細的日誌記錄，便於問題排查
4. **嵌套支援**: 正確處理嵌套的模板操作

## 遷移指南

### 從舊版本遷移

如果之前沒有使用 REC 模板功能，無需任何變更。

### 啟用 REC 模板流程

```java
// 舊方式（如果存在）
@AllowTemplateOperationOnly
public void oldMethod() {
    // 只有一般模板流程
}

// 新方式
@AllowTemplateOperationOnly(isRecTemplate = true)
public void newMethod() {
    // 同時啟用一般模板流程和 REC 模板流程
}
``` 